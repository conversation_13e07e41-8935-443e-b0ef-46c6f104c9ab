import React, { Fragment, forwardRef, useEffect, useMemo, useState } from 'react';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import badges_icon_leader_board from 'Assets/user_activity_board/badges_icon_leader_board.svg';
import FlowerBadges from './FlowerBadges';
import Radio from '@mui/material/Radio';
import FormControlLabel from '@mui/material/FormControlLabel';
import { Map as MapImmutable, List } from 'immutable';
import { badgeStyles, badgeStylesObject, initialBadgeList } from '../../utils';
import PropTypes from 'prop-types';
function Badges({ leaderBoardSetting, render }, badgeRef) {
  const [state, setState] = useState(MapImmutable());
  const [sourceDirectory, setSourceDirectory] = useState(MapImmutable());

  const constructedData = useMemo(() => {
    const badgeName = leaderBoardSetting.get('badgeName', List()).reduce((acc, cur) => {
      acc = acc.set(cur.get('name', ''), cur);
      return acc;
    }, MapImmutable());

    const badgeList = initialBadgeList.map((item) => {
      if (!badgeName.get(item.get('name'), MapImmutable())?.isEmpty()) {
        item = badgeName.get(item.get('name'), MapImmutable());
      }
      return item;
    });
    return badgeList;
  }, [leaderBoardSetting]);

  useEffect(() => {
    //this effect for when ever the api response change we should set the api response to above state
    // and one more thing we have a refresh button . if when button clicked we are calling the api
    // and trigger rerender help of render state
    if (leaderBoardSetting.isEmpty()) {
      // this if contains the dynamic imports of 15 images to reduce the initial load time in simple terms load images on demand
      let directory = new Map();
      const fetchImage = async (badge, index, style) => {
        try {
          const response = await import(
            `Assets/user_activity_board/badges/${style}/${style}_${badge
              .get('name', '')
              .toLowerCase()}.svg`
          );
          directory.set(style + index, response.default);
        } catch (err) {
          console.log('err', err); //eslint-disable-line
        }
      };

      const fetchData = async () => {
        for (const style of badgeStyles) {
          for (const [index, badge] of constructedData.entries()) {
            await fetchImage(badge, index, style); // Wait for fetchImage to complete
          }
        }
        setSourceDirectory(directory);
      };

      fetchData();
    } else {
      //*************************________________________
      setState(
        MapImmutable({
          badgeStyle: leaderBoardSetting.get('badgeStyle', 'flower_badge'),
          badgeName: constructedData,
        })
      );
    }
  }, [render]); //eslint-disable-line

  badgeRef.current = state;

  function onFlowerLabelChange(e) {
    e.persist();
    if (state.get('badgeStyle', 'flower_badge') !== e.target.value) {
      setState((prev) => {
        const doRevertIfExist =
          leaderBoardSetting.get('badgeStyle', 'flower_badge') === e.target.value;
        prev = prev
          .set('badgeStyle', e.target.value)
          .set('badgeName', doRevertIfExist ? constructedData : initialBadgeList);
        return prev;
      });
    }
  }
  return (
    <div className="mb-5">
      <Accordion disableGutters sx={{ background: 'white !important' }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1a-content"
          id="panel1a-header"
          className="px-3 py-2"
        >
          <div className="d-flex">
            <img src={badges_icon_leader_board} alt="icon_late_config" />
            <div className="ml-3">
              <div className="f-18 bold late_config_color">Badges</div>
              <span className="f-15 text-light-grey pt-1">Select the percentage ranges</span>
            </div>
          </div>
        </AccordionSummary>
        <AccordionDetails>
          <div className="f-12">Select the Badge style</div>
          {badgeStyles.map((item, index) => (
            <Fragment key={index}>
              <FormControlLabel
                checked={state.get('badgeStyle', 'flower_badge') === item}
                value={item}
                control={<Radio />}
                label={badgeStylesObject[item]}
                onChange={onFlowerLabelChange}
              />
              <FlowerBadges
                sourceDirectory={sourceDirectory}
                state={
                  state.get('badgeStyle', 'flower_badge') === item
                    ? state
                    : MapImmutable({
                        badgeStyle: item,
                        badgeName: initialBadgeList,
                      })
                }
                badgeKey={item}
                inputDisable={state.get('badgeStyle', 'flower_badge') !== item}
                setState={setState}
              />
            </Fragment>
          ))}
        </AccordionDetails>
      </Accordion>
    </div>
  );
}

export default forwardRef(Badges);

Badges.propTypes = {
  leaderBoardSetting: PropTypes.instanceOf(MapImmutable),
  render: PropTypes.bool,
};
