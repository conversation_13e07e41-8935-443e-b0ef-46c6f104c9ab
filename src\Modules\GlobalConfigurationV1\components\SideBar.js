import React from 'react';
import { List, ListItem, ListItemText } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useTheme } from '@mui/material/styles';
import { useHistory, useLocation } from 'react-router-dom';

function SideBar(props) {
  const theme = useTheme();
  const history = useHistory();
  const location = useLocation();
  const useStylesFunction = makeStyles(() => ({
    root: {
      width: '100%',
      height: '50px',
      backgroundColor: theme.palette.background.paper,
      paddingTop: '0px',
      paddingBottom: '0px',
      borderBottom: '1px solid #F3F4F6',
      '&$selected': {
        backgroundColor: '#EFF9FB',
        '&:hover': {
          backgroundColor: '#EFF9FB',
        },
      },
      '&:hover': {
        backgroundColor: '#EFF9FB',
      },
    },
    nested: {
      paddingLeft: theme.spacing(4),
    },
    selected: {},
  }));
  const classes = useStylesFunction();

  return (
    <List component="nav" aria-labelledby="nested-list-subheader" className={classes.root}>
      <ListItem
        selected={true}
        classes={{ root: classes.root, selected: classes.selected }}
        button
        onClick={() => history.push('/globalConfiguration-v1/institution')}
        // onClick={() => history.push(`/${type}/${id}/${name}/global-configuration/u-sity`)}
      >
        <ListItemText
          primary={
            location.pathname.includes('/globalConfiguration-v1/tagMasters')
              ? 'Survey'
              : 'Institution'
          }
        />
      </ListItem>
      {/* <ListItem
        // selected={usity === 0}
        classes={{ root: classes.root, selected: classes.selected }}
        button
        // onClick={() => history.push(`/${type}/${id}/${name}/global-configuration/u-sity`)}
      >
        <ListItemText primary={'Program'} />
      </ListItem> */}
    </List>
  );
}

export default SideBar;
