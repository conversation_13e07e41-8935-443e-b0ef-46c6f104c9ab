import React, { useEffect, useMemo, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { fromJS, Map, List } from 'immutable';
import {
  Menu,
  ListItemButton,
  List as ListView,
  ListItemText,
  ListSubheader,
  Checkbox,
  InputAdornment,
  // Collapse,
  // ListItem,
  // ListItemIcon,
  // Divider,
} from '@mui/material';
import { capitalize, jsUcfirstAll } from 'utils';
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
// import ExpandLessIcon from '@mui/icons-material/ExpandLess';
// import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import MaterialInput from 'Widgets/FormElements/material/Input';
import moment from 'moment';
import { getNotStartedUpcoming } from '../utils';
import SearchIcon from '@mui/icons-material/Search';
import { getCourseNameWithCode } from './tableModule';

function TableFilter({
  show,
  open,
  type,
  handleCloseFilter,
  data,
  checkedFilter,
  setCheckedFilter,
  tableType,
}) {
  const [search, setSearch] = useState('');
  const inputRef = useRef(null);
  const mapValue = tableType === '' ? 'allData' : 'curricularData';

  useEffect(() => {
    search && setSearch('');
    open && inputRef.current?.focus();
  }, [open]);

  const handleCheckbox = (checked, item, type) => {
    const filteredValue = checkedFilter.getIn([mapValue, type], List()).toJS();
    let newValue = [];
    if (checked) {
      newValue = [...filteredValue, item];
    } else {
      newValue = [...filteredValue.filter((value) => value !== item)];
    }
    const updateValue = checkedFilter.setIn([mapValue, type], fromJS(newValue));
    setCheckedFilter(updateValue);
  };

  function programFilter(item) {
    return checkedFilter.getIn([mapValue, 'programFilter'], List()).size > 0
      ? checkedFilter
          .getIn([mapValue, 'programFilter'], List())
          .includes(item.get('program_name', ''))
      : item;
  }

  function courseFilter(item) {
    return checkedFilter.getIn([mapValue, 'courseFilter'], List()).size > 0
      ? checkedFilter
          .getIn([mapValue, 'courseFilter'], List())
          .includes(getCourseNameWithCode(item))
      : item;
  }

  function deliveryFilter(item) {
    const dFilter = checkedFilter.getIn([mapValue, 'deliveryFilter'], List());
    return dFilter.size > 0
      ? dFilter.includes(item.getIn(['session', 'delivery_symbol'], '')) ||
          dFilter.includes(item.get('type', ''))
      : item;
  }

  function staffFilter(item) {
    const hasStaff = checkedFilter.getIn([mapValue, 'staffFilter'], List());
    const staffItems = item
      .get('staffs', List())
      .map((item) =>
        jsUcfirstAll(
          `${item.getIn(['staff_name', 'first'], '')} ${item.getIn(
            ['staff_name', 'middle'],
            ''
          )} ${item.getIn(['staff_name', 'last'], '')}`
        )
      );
    return hasStaff.size > 0 ? hasStaff.some((i) => staffItems.includes(i)) : item;
  }

  function modeFilter(item) {
    return checkedFilter.getIn([mapValue, 'modeFilter'], List()).size > 0
      ? checkedFilter.getIn([mapValue, 'modeFilter'], List()).includes(item.get('mode', ''))
      : item;
  }

  function statusFilter(item) {
    return checkedFilter.getIn([mapValue, 'statusFilter'], List()).size > 0
      ? checkedFilter
          .getIn([mapValue, 'statusFilter'], List())
          .includes(
            item.get('status', '') !== 'pending'
              ? item.get('status', '')
              : getNotStartedUpcoming(item)
          )
      : item;
  }

  function remarkFilter(item) {
    const hasStatus = checkedFilter.getIn([mapValue, 'remarkFilter'], List());
    const sessionStatus = item.get('sessionStatus', List());
    return hasStatus.size > 0 ? hasStatus.some((i) => sessionStatus.includes(i)) : item;
  }

  function handoutsFilter(item) {
    const hasMode = checkedFilter.getIn([mapValue, 'handoutsFilter'], List());
    return hasMode.size > 0 ? hasMode.includes(item.get('colorStatus', '')) : item;
  }

  function termFilters(item) {
    const hasTerm = checkedFilter.getIn([mapValue, 'termFilter', 'term'], '');
    const hasYears = checkedFilter.getIn([mapValue, 'termFilter', 'years'], List());
    const hasLevels = checkedFilter.getIn([mapValue, 'termFilter', 'levels'], List());
    if (hasLevels.size > 0) {
      return (
        item.get('term', '') === hasTerm &&
        hasYears.includes(item.get('year_no', '')) &&
        hasLevels.includes(item.get('level_no', ''))
      );
    } else if (hasYears.size > 0) {
      return item.get('term', '') === hasTerm && hasYears.includes(item.get('year_no', ''));
    } else if (hasTerm !== '') {
      return hasTerm !== '' ? item.get('term', '') === hasTerm : item;
    }
    return item;
  }

  function getDataList(name, type) {
    if (type === 'programFilter') {
      const uniqueItems = data
        .filter(courseFilter)
        .filter(deliveryFilter)
        .filter(staffFilter)
        .filter(modeFilter)
        .filter(statusFilter)
        .filter(remarkFilter)
        .filter(handoutsFilter)
        .filter(termFilters)
        .map((item) => item.get(name, ''))
        .filter((item) => item !== '');
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'courseFilter') {
      const uniqueItems = data
        .filter(programFilter)
        .filter(deliveryFilter)
        .filter(staffFilter)
        .filter(modeFilter)
        .filter(statusFilter)
        .filter(remarkFilter)
        .filter(handoutsFilter)
        .filter(termFilters)
        .map(getCourseNameWithCode)
        .filter((item) => item !== '');
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'deliveryFilter') {
      let uniqueItems = data
        .filter(programFilter)
        .filter(courseFilter)
        .filter(staffFilter)
        .filter(modeFilter)
        .filter(statusFilter)
        .filter(remarkFilter)
        .filter(handoutsFilter)
        .filter(termFilters);

      const hasSupportSession = uniqueItems.some(
        (item) => item.get('type', '') === 'support_session'
      );
      const hasEvent = uniqueItems.some((item) => item.get('type', '') === 'event');

      uniqueItems = uniqueItems
        .filter((item) => item.get('type', '') === 'regular')
        .map((item) => `${item.getIn(['session', 'delivery_symbol'], item.get('title', ''))}`);
      if (hasSupportSession) {
        uniqueItems = [...uniqueItems, 'support_session'];
      }
      if (hasEvent) {
        uniqueItems = [...uniqueItems, 'event'];
      }
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'staffFilter') {
      const uniqueItems = data
        .filter(programFilter)
        .filter(courseFilter)
        .filter(deliveryFilter)
        .filter(modeFilter)
        .filter(statusFilter)
        .filter(remarkFilter)
        .filter(handoutsFilter)
        .filter(termFilters)
        .reduce(
          (acc, item) =>
            acc.concat(
              item
                .get('staffs', List())
                .map((item) =>
                  jsUcfirstAll(
                    `${item.getIn(['staff_name', 'first'], '')} ${item.getIn(
                      ['staff_name', 'middle'],
                      ''
                    )} ${item.getIn(['staff_name', 'last'], '')}`
                  )
                )
            ),
          List()
        );
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'modeFilter') {
      const filteredUniqueItems = data
        .filter(programFilter)
        .filter(courseFilter)
        .filter(deliveryFilter)
        .filter(staffFilter)
        .filter(statusFilter)
        .filter(remarkFilter)
        .filter(handoutsFilter)
        .filter(termFilters);
      const uniqueItems = filteredUniqueItems.map((item) => item.get('mode', ''));
      const hasOfflineSession = filteredUniqueItems.some(
        (item) => item.get('classModeType', '') === 'offline'
      );
      const hasWebSession = filteredUniqueItems.some(
        (item) => item.get('scheduleStartFrom', '') === 'web'
      );
      if (hasOfflineSession) {
        return fromJS([...new Set(uniqueItems.push('offline'))]);
      }
      if (hasWebSession) {
        return fromJS([...new Set(uniqueItems.push('web'))]);
      }
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'statusFilter') {
      const uniqueItems = data
        .filter(programFilter)
        .filter(courseFilter)
        .filter(deliveryFilter)
        .filter(staffFilter)
        .filter(modeFilter)
        .filter(remarkFilter)
        .filter(handoutsFilter)
        .filter(termFilters)
        .map((item) => {
          if (item.get('isActive', false) === false) {
            return item.set('status', 'cancelled');
          } else if (item.get('status', '') === 'pending') {
            const scheduleStartDate = Date.parse(item.get('scheduleStartDateAndTime', new Date()));
            const scheduleEndDate = Date.parse(item.get('scheduleEndDateAndTime', new Date()));
            const utcDate = Date.parse(moment(new Date()).utc().format());
            return scheduleStartDate < utcDate && utcDate < scheduleEndDate
              ? item.set('status', 'not-started')
              : item.set('status', 'upcoming');
          } else {
            return item;
          }
        })
        .map((item) => item.get('status', ''));
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'remarkFilter') {
      const uniqueItems = data
        .filter(programFilter)
        .filter(courseFilter)
        .filter(deliveryFilter)
        .filter(staffFilter)
        .filter(modeFilter)
        .filter(termFilters)
        .filter(statusFilter)
        .filter(handoutsFilter)
        .reduce(
          (acc, item) => acc.concat(item.get('sessionStatus', List()).map((item) => item)),
          List()
        )
        .filter((item) => item !== '');
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'handoutsFilter') {
      const uniqueItems = data
        .filter(programFilter)
        .filter(courseFilter)
        .filter(deliveryFilter)
        .filter(staffFilter)
        .filter(modeFilter)
        .filter(termFilters)
        .filter(statusFilter)
        .filter(remarkFilter)
        .map((item) => item.get(name, ''))
        .filter((item) => item !== '' && item !== undefined);
      return fromJS([...new Set(uniqueItems)]);
    } else if (type === 'termFilter') {
      return termFilter();
    } else {
      const uniqueItems = data.map((item) => item.get(name, '')).filter((item) => item !== '');
      return fromJS([...new Set(uniqueItems)]);
    }
  }

  function termFilter() {
    const uniqueItems = data
      .filter(programFilter)
      .filter(courseFilter)
      .filter(deliveryFilter)
      .filter(staffFilter)
      .filter(modeFilter)
      .filter(statusFilter)
      .filter(remarkFilter)
      .filter(handoutsFilter);

    return uniqueItems;
  }

  const dataList =
    type === 'programFilter'
      ? getDataList('program_name', type)
      : type === 'courseFilter'
      ? getDataList('course_name', type)
      : type === 'modeFilter'
      ? getDataList('mode', type)
      : type === 'statusFilter'
      ? getDataList('status', type)
      : type === 'remarkFilter'
      ? getDataList('sessionStatus', type)
      : type === 'deliveryFilter'
      ? getDataList('delivery_symbol', type)
      : type === 'staffFilter'
      ? getDataList('staffs', type)
      : type === 'termFilter'
      ? getDataList('', type)
      : type === 'handoutsFilter'
      ? getDataList('colorStatus', type)
      : List();

  const filteredData = useMemo(() => {
    return search
      ? dataList.filter((item) => {
          const textToSearch = item === 'ongoing' ? 'InProgress' : item;
          return textToSearch.toLowerCase().includes(search.toLowerCase());
        })
      : dataList;
  }, [dataList, search]);

  // const [innerFilter, setInnerFilter] = useState(false);
  // const [innerFilterYear, setInnerFilterYear] = useState(false);

  // const handleFilterInner = () => {
  //   setInnerFilter(!innerFilter);
  // };

  // const handleFilterYear = () => {
  //   setInnerFilterYear(!innerFilterYear);
  // };

  const courseTermData = (data, type) => {
    return data
      .groupBy((s) => s.get(type))
      .keySeq()
      .toList()
      .filter((item) => item !== undefined)
      .sort((a, b) =>
        type === `year_no`
          ? a.replace('year', '') - b.replace('year', '')
          : type === `level_no`
          ? a.replace('Level ', '') - b.replace('Level ', '')
          : b - a
      )
      .map((item) => {
        return Map({
          name:
            type === `year_no`
              ? `Year ` + item.replace('year', '')
              : type === `term`
              ? capitalize(item)
              : type === `level_no` && capitalize(item),
          value: item,
        });
      });
  };

  const handleSelectChange = (value, name, type) => {
    let updatedValue = checkedFilter;
    if (name === 'term') {
      updatedValue = updatedValue
        .setIn([mapValue, type, name], value)
        .setIn([mapValue, type, 'years'], fromJS([]))
        .setIn([mapValue, type, 'levels'], fromJS([]));
    } else if (name === 'years') {
      updatedValue = updatedValue
        .setIn([mapValue, type, 'years'], fromJS(value))
        .setIn([mapValue, type, 'levels'], fromJS([]));
    } else if (name === 'levels') {
      updatedValue = updatedValue.setIn([mapValue, type, 'levels'], fromJS(value));
    }
    setCheckedFilter(updatedValue);
  };

  const filteredTerm = () => {
    return dataList.filter((s) =>
      checkedFilter.getIn([mapValue, 'termFilter', 'term'], '') !== ''
        ? s.get('term') === checkedFilter.getIn([mapValue, 'termFilter', 'term'], '')
        : s
    );
  };

  const filteredYear = () => {
    return dataList.filter((s) =>
      checkedFilter.getIn([mapValue, 'termFilter', 'years'], List()).size > 0
        ? checkedFilter.getIn([mapValue, 'termFilter', 'years'], List()).includes(s.get('year_no'))
        : s
    );
  };

  return (
    <React.Fragment>
      <Menu
        id={type}
        anchorEl={show}
        keepMounted
        open={open}
        onClose={handleCloseFilter}
        PaperProps={{
          style: {
            width: '30ch',
          },
        }}
      >
        <ListView
          component="div"
          className="align-items-center"
          disablePadding
          subheader={
            <ListSubheader className="f-16">
              <div>
                Select{' '}
                {type === 'programFilter'
                  ? 'Program'
                  : type === 'courseFilter'
                  ? 'Course'
                  : type === 'modeFilter'
                  ? 'Mode'
                  : type === 'statusFilter'
                  ? 'Status'
                  : type === 'remarkFilter'
                  ? 'Remark'
                  : type === 'deliveryFilter'
                  ? 'Delivery'
                  : type === 'staffFilter'
                  ? 'Staff'
                  : type === 'termFilter'
                  ? 'Term / Year / Level'
                  : type === 'handoutsFilter'
                  ? 'Handout'
                  : ''}
              </div>
              {type !== 'termFilter' && (
                <MaterialInput
                  elementType={'materialInput'}
                  value={search}
                  size={'small'}
                  changed={(e) => setSearch(e.target.value)}
                  placeholder={'Search...'}
                  startAdornment={
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  }
                  stopPropagation
                  inputRef={inputRef}
                />
              )}
            </ListSubheader>
          }
        >
          {type === 'termFilter' ? (
            <React.Fragment>
              <div style={{ padding: '0px 18px' }}>
                <MaterialInput
                  elementType={'materialSelectNew'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{
                    options: [
                      { name: 'Select Term', value: '' },
                      ...courseTermData(dataList, 'term').toJS(),
                    ],
                  }}
                  changed={(e) => handleSelectChange(e.target.value, 'term', type)}
                  value={checkedFilter.getIn([mapValue, 'termFilter', 'term'], '')}
                  label={'Term'}
                  labelclass={'mb-0 f-14'}
                  displayEmpty={true}
                />
                {/* {checkedFilter.getIn([mapValue, 'termFilter', 'term'], '') !== '' && ( */}
                <MaterialInput
                  elementType={'materialSelectMultiple'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  isAllSelected={false}
                  labelclass={'mb-1 f-14'}
                  label={'Year'}
                  elementConfig={{
                    options:
                      checkedFilter.getIn([mapValue, 'termFilter', 'term'], '') !== ''
                        ? [...courseTermData(filteredTerm(), 'year_no').toJS()]
                        : [],
                  }}
                  value={checkedFilter.getIn([mapValue, 'termFilter', 'years'], List()).toJS()}
                  changed={(e) => handleSelectChange(e.target.value, 'years', type)}
                  multiple={true}
                  displayEmpty={true}
                  displayName={'Select Year'}
                />
                {/* )} */}
                {/* {checkedFilter.getIn([mapValue, 'termFilter', 'term'], '') !== '' &&
                  checkedFilter.getIn([mapValue, 'termFilter', 'years'], List()).size > 0 && ( */}
                <MaterialInput
                  elementType={'materialSelectMultiple'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  isAllSelected={false}
                  labelclass={'mb-1 f-14'}
                  label={'Level'}
                  elementConfig={{
                    options:
                      checkedFilter.getIn([mapValue, 'termFilter', 'term'], '') !== '' &&
                      checkedFilter.getIn([mapValue, 'termFilter', 'years'], List()).size > 0
                        ? [...courseTermData(filteredYear(), 'level_no').toJS()]
                        : [],
                  }}
                  value={checkedFilter.getIn([mapValue, 'termFilter', 'levels'], List()).toJS()}
                  changed={(e) => handleSelectChange(e.target.value, 'levels', type)}
                  multiple={true}
                  displayEmpty={true}
                  displayName={'Select Level'}
                />
                {/* )} */}
              </div>
            </React.Fragment>
          ) : (
            <React.Fragment>
              {filteredData.map((item, j) => (
                <ListItemButton
                  dense
                  key={j}
                  onClick={(e) =>
                    handleCheckbox(
                      !checkedFilter.getIn([mapValue, type], List()).includes(item),
                      item,
                      type
                    )
                  }
                >
                  <React.Fragment>
                    <Checkbox
                      edge="start"
                      checked={checkedFilter.getIn([mapValue, type], List()).includes(item)}
                      tabIndex={-1}
                      disableRipple
                      onClick={(e) => handleCheckbox(e.target.checked, item, type)}
                    />
                    <ListItemText
                      className="text-capitalize"
                      primary={
                        item === 'ongoing' ? 'InProgress' : jsUcfirstAll(item?.replace('_', ' '))
                      }
                    />
                  </React.Fragment>
                </ListItemButton>
              ))}
            </React.Fragment>
          )}
        </ListView>
      </Menu>
    </React.Fragment>
  );
}

TableFilter.propTypes = {
  show: PropTypes.object,
  open: PropTypes.bool,
  handleCloseFilter: PropTypes.func,
  type: PropTypes.string,
  data: PropTypes.instanceOf(List),
  handleFilter: PropTypes.func,
  checkedFilter: PropTypes.instanceOf(Map),
  setCheckedFilter: PropTypes.func,
  tableType: PropTypes.string,
};

export default TableFilter;
