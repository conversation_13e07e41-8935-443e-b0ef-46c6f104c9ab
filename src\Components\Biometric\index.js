import React, { useEffect } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { Trans } from 'react-i18next';

import { getSiteUrl } from 'utils';

export default function NewWebCam({
  handleFaceClose,
  userId,
  _user_id,
  successCallBack,
  errorCallBack,
}) {
  useEffect(() => {
    window.addEventListener('message', handlePostCameraAuthentication);
    return () => {
      window.removeEventListener('message', handlePostCameraAuthentication);
    };
  }, []);

  const handlePostCameraAuthentication = (e) => {
    const { type } = e.data;
    if (type === 'success') {
      successCallBack();
    } else if (type === 'error') {
      errorCallBack();
    }
  };

  function removeTrailingSlash(url) {
    if (url.endsWith('/')) {
      return url.slice(0, -1);
    }
    return url;
  }

  const siteURL = getSiteUrl();
  const iframeURL = `${removeTrailingSlash(
    siteURL.DC_URL
  )}/face/index1.html?userId=${userId}&_user_id=${_user_id}&url=${removeTrailingSlash(
    process.env.REACT_APP_API_URL
  )}/user/userBiometricRegister`;

  return (
    <React.Fragment>
      <Modal
        show={true}
        dialogClassName="model-1200"
        centered
        backdroptransition={{
          in: true,
          appear: true,
          timeout: 300,
        }}
      >
        <div className="container">
          <div className="d-flex justify-content-between">
            <div className="pt-3">
              <p className="f-20 mb-2 ">
                <Trans i18nKey={'biometric.captureFace'}></Trans> (Follow instructions to capture
                the required face positions.)
              </p>
            </div>
            <div className="pt-3">
              <b className="pr-2">
                <Button
                  variant="outline-primary"
                  className="border-radious-8"
                  onClick={() => {
                    handleFaceClose();
                  }}
                >
                  {' '}
                  <Trans i18nKey={'cancel_upper'}></Trans>
                </Button>{' '}
              </b>
            </div>
          </div>
        </div>
        <Modal.Body>
          <div style={{ width: '100%', position: 'relative', height: '75vh' }}>
            <iframe
              src={iframeURL}
              title="Face capture"
              style={{ width: '100%', border: 'none', height: '100%' }}
              allowFullScreen
              allow="camera;microphone"
            ></iframe>
          </div>
        </Modal.Body>
      </Modal>
    </React.Fragment>
  );
}
