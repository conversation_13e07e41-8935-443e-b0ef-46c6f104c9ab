import React from 'react';
import { Modal } from 'react-bootstrap';
import { fromJS, Map } from 'immutable';
import PropTypes from 'prop-types';
import ArchiveImg from 'Assets/archive.png';
import MButton from 'Widgets/FormElements/material/Button';

const DeleteModal = (props) => {
  const {
    show,
    cancel,
    typeId,
    type,
    subType,
    assessment,
    callback,
    componentName,
    fileName,
    mode,
    handleDelete,
    ids,
  } = props;
  const assessmentId = assessment?.get('_id', '');
  const stage = ids?.get('showMarks', false);
  const toggleCallback = () => {
    callback();
    cancel();
  };
  const handleSubmit = () => {
    if (mode === 'plan') {
      const requestBody = fromJS({
        _id: assessmentId,
        typeId: ids.get('typeId', ''),
        subTypeId: ids.get('subTypeId', ''),
        assessmentTypeId: ids.get('assessmentTypeId', ''),
        assessmentId: ids.get('assessmentId', ''),
      });
      handleDelete({ requestBody, mode: 'delete', callback: () => toggleCallback(), stage });
    } else if (mode === 'student') {
      handleDelete();
    } else {
      const requestBody = fromJS({
        _id: typeId,
        type,
        subType,
        assessmentId,
      });
      handleDelete(requestBody, () => toggleCallback());
    }
  };
  return (
    <Modal show={show} onHide={cancel} centered>
      <Modal.Body>
        <div className="d-flex mb-3">
          <img className="mr-2" alt={'Archive'} src={ArchiveImg} />
          <p className="mb-0 f-22 bold"> {`Delete  ${componentName}`}</p>
        </div>

        <div className="p-2">
          <p className="mb-0 break-word">
            {`Are you sure you want to delete the selected '${fileName}'?`}
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <MButton variant="outlined" color="primary" className={'mr-2'} clicked={cancel}>
          No
        </MButton>
        <MButton variant="contained" color="red" clicked={handleSubmit}>
          Yes
        </MButton>
      </Modal.Footer>
    </Modal>
  );
};
DeleteModal.propTypes = {
  show: PropTypes.bool,
  cancel: PropTypes.func,
  type: PropTypes.string,
  typeId: PropTypes.string,
  subType: PropTypes.string,
  getAssessmentTypesList: PropTypes.func,
  assessment: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  removeAssessmentType: PropTypes.func,
  assessmentObj: PropTypes.object,
  mode: PropTypes.string,
  handleDelete: PropTypes.func,
  ids: PropTypes.instanceOf(Map),
  callback: PropTypes.func,
  componentName: PropTypes.string,
  fileName: PropTypes.string,
};
export default DeleteModal;
