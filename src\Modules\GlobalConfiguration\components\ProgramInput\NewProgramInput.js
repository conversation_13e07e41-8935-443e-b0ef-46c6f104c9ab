import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory } from 'react-router-dom';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectProgramInput, selectBasicDetails } from '_reduxapi/global_configuration/selectors';
import { getInstitutionHeader } from 'v2/utils';

import { LabelConfiguration, ProgramType, CurriculumNaming, CreditHours } from './AllRows';
import { getLang } from 'utils';
export const programInputContext = React.createContext({});
function ProgramInput({
  getProgramInput,
  programInput,
  updateLabel,
  updateProgramType,
  updateProgramInputMode,
  setData,
  basicDetails,
}) {
  const history = useHistory();
  const [show, setShow] = useState(false);
  const [selectedProgramType, setSelectedProgramType] = useState(Map());
  const [type, setType] = useState('');
  const [radioValue, setRadioValue] = useState(
    Map({
      curriculumNaming: 1,
      creditHours: '',
      departments: '',
    })
  );
  const institutionHeader = getInstitutionHeader(history);
  useEffect(() => {
    getProgramInput({ header: institutionHeader });
  }, []); // eslint-disable-line

  useEffect(() => {
    if (
      programInput.get('curriculumNaming', List()).size > 0 ||
      programInput.get('creditHours', List()).size > 0
    ) {
      const selectedCurriculum = programInput
        ?.get('curriculumNaming', List())
        .find((item) => item.get('isDefault', false));
      const selectedCreditHours = programInput
        ?.get('creditHours', List())
        .find((item) => item.get('isDefault', false));
      const updatedValue = radioValue.update((key) => {
        return key
          .set('curriculumNaming', selectedCurriculum?.get('mode', 3))
          .set('creditHours', selectedCreditHours?.get('mode', ''));
      });
      setRadioValue(updatedValue);
    }
  }, [programInput]); // eslint-disable-line

  const handleReset = (e) => {
    e.stopPropagation();
    updateLabel({ id: programInput.get('_id'), type: 'reset', header: institutionHeader });
  };

  const handleOpen = (name, type, item) => {
    let showModal = {
      ...show,
      [name]: !show[name],
    };
    setShow(showModal);
    setType(type);
    setSelectedProgramType(item);
  };

  const handleRadioButton = (key, value, id) => {
    let updatedValue = radioValue.set(key, value);
    setRadioValue(updatedValue);
    if (key !== 'departments') {
      updateProgramInputMode({
        id,
        requestData: {
          settingId: programInput.get('_id'),
        },
        type: key,
        header: institutionHeader,
      });
    }
  };

  function getDefaultLanguage() {
    const Lang = getLang();
    return (
      programInput &&
      programInput
        ?.get('labelConfiguration', List())
        ?.find((label) => label.get('language', 'en') === Lang)
    );
  }

  const getLanguage = getDefaultLanguage();
  const values = {
    show,
    handleOpen,
    programInput,
    updateLabel,
    basicDetails,
    institutionHeader,
    getLanguage,
    handleReset,
    selectedProgramType,
    setData,
    type,
    updateProgramType,
    handleRadioButton,
    radioValue,
    setRadioValue,
    disabled: false,
  };
  return (
    <programInputContext.Provider value={values}>
      <div className="pt-3 pl-3">
        <LabelConfiguration />
        <ProgramType />
        <CurriculumNaming />
        <CreditHours />
      </div>
    </programInputContext.Provider>
  );
}
ProgramInput.propTypes = {
  getProgramInput: PropTypes.func,
  programInput: PropTypes.instanceOf(Map),
  basicDetails: PropTypes.instanceOf(Map),
  updateLabel: PropTypes.func,
  updateProgramType: PropTypes.func,
  updateProgramInputMode: PropTypes.func,
  setData: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    programInput: selectProgramInput(state),
    basicDetails: selectBasicDetails(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ProgramInput);
