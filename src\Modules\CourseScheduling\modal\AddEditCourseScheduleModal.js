import React from 'react';
import PropTypes from 'prop-types';
import { List, Map, Set } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { ThemeProvider } from '@mui/styles';
import { Form } from 'react-bootstrap';
import {
  MUI_THEME,
  MUI_CHECKBOX_THEME,
  capitalize,
  jsUcfirstAll,
  intersectMany,
  getModes,
  isIndGroup,
  getManualStaffOption,
  isManualAttendanceEnabled,
  studentGroupRename,
  // isModuleEnabled,
} from '../../../utils';
import { getFormattedGroupName, useMuiMultiSelectStyles } from '../components/utils';
import { Dialog, DialogActions } from '@mui/material';
import { useSelector } from 'react-redux';
import MaterialInput from 'Widgets/FormElements/material/Input';

const menuProps = {
  getcontentanchorel: null,
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'center',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'center',
  },
  variant: 'menu',
};

function AddEditCourseScheduleModal({
  show,
  onHide,
  onSave,
  onChange,
  mode,
  data,
  course,
  activeSessionFlow,
  checkScheduleStatus,
  checkStudentGroupStatus,
  checkSessionGroupStatus,
  isRotation,
  programId,
}) {
  const courseGroups = getCourseGroups();
  const deliveryGroups = getDeliveryGroups();
  const subjects = getSubjects();
  const staffs = getStaffs();
  const infrastructures = getInfrastructures();
  const topics = getTopics();

  function handleChange(name, value) {
    switch (name) {
      case 'student_groups': {
        const selected = activeSessionFlow
          .get('student_group', List())
          .filter((group) => value.includes(group.get('_id')))
          .map((group) => group.set('session_group', List()));
        onChange(name, selected);
        break;
      }
      case 'session_groups': {
        onChange('student_groups', value);
        break;
      }
      case 'subjects': {
        onChange('staffs', List());
        const selected = activeSessionFlow
          .get('subjects', List())
          .filter((subject) => value.includes(subject.get('_subject_id')));
        onChange(name, selected);
        break;
      }
      case 'staffs': {
        const selected = staffs
          .filter((staff) => value.includes(staff.get('_id')))
          .map((staff) => staff.delete('name').delete('value'));
        onChange(name, selected);
        break;
      }
      case '_topic_id': {
        const topic = topics.find((t) => t.value === value);
        onChange(
          'topic',
          Map(topic ? { topic: topic.title, _topic_id: value } : { topic: '', _topic_id: '' })
        );
        break;
      }
      case 'manualStaffs': {
        onChange(name, value);
        break;
      }
      case 'mobileNumberOrEmail': {
        onChange(name, value);
        break;
      }
      default:
        onChange(name, value);
        break;
    }
  }

  function handleDeliveryGroupChange(option) {
    const isStudentGroup = option.get('isStudentGroup');
    const groups = data.getIn(['schedule', 'student_groups'], List());
    const selectedStudentGroupIndex = groups.findIndex(
      (g) => g.get('_id') === option.get(isStudentGroup ? 'value' : 'studentGroupId')
    );
    if (selectedStudentGroupIndex === -1) return;
    const selectedStudentGroup = groups.get(selectedStudentGroupIndex, Map());
    if (selectedStudentGroup.isEmpty()) return;
    if (isStudentGroup) {
      const cbState = getCourseDeliveryGroupCBState(
        option.get('value'),
        option.get('totalSessionGroups')
      );
      if (cbState === 'checked') {
        handleChange(
          'session_groups',
          groups.set(selectedStudentGroupIndex, selectedStudentGroup.set('session_group', List()))
        );
      } else {
        const actualStudentGroup = activeSessionFlow
          .get('student_group', List())
          .find((g) => g.get('_id') === option.get('value'));
        if (!actualStudentGroup) return;
        handleChange('session_groups', groups.set(selectedStudentGroupIndex, actualStudentGroup));
      }
    } else {
      const actualStudentGroup = activeSessionFlow
        .get('student_group', List())
        .find((g) => g.get('_id') === option.get('studentGroupId'));
      if (!actualStudentGroup) return;
      const sessionGroup = selectedStudentGroup
        .get('session_group', List())
        .find((g) => g.get('_id') === option.get('value'));
      if (!sessionGroup) {
        const actualSessionGroup = actualStudentGroup
          .get('session_group', List())
          .find((g) => g.get('_id') === option.get('value'));
        handleChange(
          'session_groups',
          groups.set(
            selectedStudentGroupIndex,
            selectedStudentGroup.set(
              'session_group',
              selectedStudentGroup.get('session_group', List()).push(actualSessionGroup)
            )
          )
        );
      } else {
        handleChange(
          'session_groups',
          groups.set(
            selectedStudentGroupIndex,
            selectedStudentGroup.set(
              'session_group',
              selectedStudentGroup
                .get('session_group', List())
                .filter((sg) => sg.get('_id') !== option.get('value'))
            )
          )
        );
      }
    }
  }

  function getCourseDeliveryGroupCBState(studentGroupId, actualCount) {
    const selectedStudentGroup = data
      .getIn(['schedule', 'student_groups'], List())
      .find((g) => g.get('_id') === studentGroupId);
    if (!selectedStudentGroup) return 'unchecked';
    const selectedCount = selectedStudentGroup.get('session_group', List()).size;
    if (selectedCount === 0) return 'unchecked';
    if (actualCount === selectedCount) return 'checked';
    return 'indeterminate';
  }

  function getCourseGroups() {
    return activeSessionFlow.get('student_group', List()).map((studentGroup) =>
      Map({
        name: getFormattedGroupName(
          studentGroupRename(studentGroup.get('group_name', ''), programId),
          isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
        ),
        value: studentGroup.get('_id'),
        ...(isRotation && { groupNo: studentGroup.get('group_no') }),
      })
    );
  }

  function getDeliveryGroups() {
    const selectedStudentGroupIds = getSelectedCourseGroups();
    if (!selectedStudentGroupIds.length) return List();
    const selectedStudentGroups = activeSessionFlow
      .get('student_group', List())
      .filter((g) => selectedStudentGroupIds.includes(g.get('_id')));
    return selectedStudentGroups.reduce((acc, g) => {
      return acc.concat(
        List([
          Map({
            name: getFormattedGroupName(
              studentGroupRename(g.get('group_name', ''), programId),
              isIndGroup(g.get('group_name', '')) ? 1 : 2
            ),
            value: g.get('_id'),
            isStudentGroup: true,
            totalSessionGroups: g.get('session_group', List()).size,
          }),
        ]).concat(
          g.get('session_group', List()).map((sessionGroup) =>
            Map({
              name: getFormattedGroupName(sessionGroup.get('group_name', ''), 3),
              value: sessionGroup.get('_id'),
              isStudentGroup: false,
              studentGroupId: g.get('_id'),
            })
          )
        )
      );
    }, List());
  }

  function getSubjects() {
    return activeSessionFlow.get('subjects', List()).map((subject) =>
      Map({
        name: subject.get('subject_name'),
        value: subject.get('_subject_id'),
      })
    );
  }

  function getTopics() {
    const topics = activeSessionFlow.get('topics', List()).map((topic) => {
      return {
        title: topic.get('title', ''),
        name: capitalize(topic.get('title', '')),
        value: topic.get('_id', ''),
      };
    });
    return topics.toJS();
  }

  function getStaffs() {
    const selectedSubjects = data.getIn(['schedule', 'subjects'], List());

    if (selectedSubjects.isEmpty()) return List();
    const subjects = selectedSubjects.map((item) => item.get('_subject_id')).toJS();
    const noOfSubjects = activeSessionFlow
      .get('subjects', List())
      .filter((item) => subjects.includes(item.get('_subject_id')));

    return noOfSubjects && noOfSubjects.size === 1
      ? noOfSubjects
          .reduce((acc, subject) => {
            return acc.concat(
              subject.get('staffs', List()).map((staff) =>
                staff.merge(
                  Map({
                    name: `${staff.getIn(['name', 'first'], '')} ${staff.getIn(
                      ['name', 'middle'],
                      ''
                    )} ${staff.getIn(['name', 'last'], '')}`,
                    value: staff.get('_id'),
                    staff_name: staff.get('name'),
                    _staff_id: staff.get('_id'),
                  })
                )
              )
            );
          }, List())
          .reduce((acc, staff) => acc.set(staff.get('value'), staff), Map())
          .valueSeq()
          .toList()
      : getIntersectSubjects(noOfSubjects);
  }

  function getIntersectSubjects(noOfSubjects) {
    let arrayStaffId = [];
    noOfSubjects
      .map((item) => item.get('staffs', List()))
      .reduce((_, el) => {
        const value = el.map((item) => item.get('_id'));
        arrayStaffId.push(value.toJS());
        return el;
      }, List());
    const intersectStaffArray = intersectMany(arrayStaffId);
    return noOfSubjects
      .reduce((acc, subject) => {
        return acc.concat(
          subject
            .get('staffs', List())
            .filter((item) => intersectStaffArray.includes(item.get('_id')))
            .map((staff) =>
              staff.merge(
                Map({
                  name: `${staff.getIn(['name', 'first'], '')} ${staff.getIn(
                    ['name', 'middle'],
                    ''
                  )} ${staff.getIn(['name', 'last'], '')}`,
                  value: staff.get('_id'),
                  staff_name: staff.get('name'),
                  _staff_id: staff.get('_id'),
                })
              )
            )
        );
      }, List())
      .reduce((acc, staff) => acc.set(staff.get('value'), staff), Map())
      .valueSeq()
      .toList();
  }

  function getInfrastructures() {
    const mode = data.getIn(['schedule', 'mode'], '');
    if (!mode) return List();
    const genders = Set(
      data
        .getIn(['schedule', 'student_groups'], List())
        .map((studentGroup) => studentGroup.get('gender'))
        .filter((g) => g)
    ).toList();
    if (genders.isEmpty()) return List();
    const includeAll = genders.size !== 1;
    const gender = genders.get(0, '').toLowerCase();
    if (mode === 'remote') {
      return course
        .get('remote_scheduling', List())
        .filter((room) => {
          if (
            // room.get('yearName') === course.get('year_no') &&
            room.get('term', '').toLowerCase() === course.get('term', '').toLowerCase() &&
            room.get('levelName') === course.get('level_no')
          ) {
            return (
              room.get('gender', '').toLowerCase() === gender || room.get('gender', '') === 'both'
            );
          }
          return false;
        })
        .map((room) =>
          room.merge(
            Map({
              name: room.get('meetingTitle'),
              value: room.get('_id'),
              secondaryTitle1: `${capitalize(room.get('gender', ''))}${
                room.get('meetingUsername', '') !== null
                  ? `, ${room.get('meetingUsername', '')}`
                  : ''
              }`,
              secondaryTitle2: `${
                room.get('remotePlatform', '') === 'teams'
                  ? 'Microsoft Teams'
                  : `${room.get('meetingId', '')}, Zoom`
              }`,
            })
          )
        )
        .sort((r1, r2) => {
          const v1 = r1.get('meetingTitle', '');
          const v2 = r2.get('meetingTitle', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    } else if (mode === 'onsite') {
      const subjects = data.getIn(['schedule', 'subjects'], List());
      if (subjects.isEmpty()) return List();
      let infrastructures = subjects.reduce(
        (acc, subject) => acc.concat(subject.get('subject_infra', List())),
        List()
      );
      if (!includeAll) {
        infrastructures = infrastructures.filter((infra) => {
          const gendersInTimeGroups = Set(
            infra
              .get('timing', List())
              .map((t) => {
                const g = t.getIn(['_time_id', 'gender']);
                if (!g) return '';
                return g.toLowerCase();
              })
              .filter((g) => g)
          ).toList();
          if (gendersInTimeGroups.isEmpty()) return false;
          return gendersInTimeGroups.includes('both') ? true : gendersInTimeGroups.includes(gender);
        });
      }
      return infrastructures
        .map((infra) =>
          infra.merge(
            Map({
              value: infra.get('_id'),
              secondaryTitle1: `${infra.get('floor_no', '')}, ${
                infra.get('zone', List()).join(', ') || 'NA'
              }, ${infra.get('room_no', '')}`,
              secondaryTitle2: `${infra.get('building_name', '')}`,
              outsideCampus: infra.get('outsideCampus', false),
            })
          )
        )
        .reduce((acc, infra) => acc.set(infra.get('_id'), infra), Map())
        .valueSeq()
        .toList()
        .sort((i1, i2) => {
          const v1 = i1.get('name', '');
          const v2 = i2.get('name', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    }
    return List();
  }

  function getSelectedCourseGroups() {
    return data
      .getIn(['schedule', 'student_groups'], List())
      .map((studentGroup) => studentGroup.get('_id'))
      .toJS();
  }

  function getSelectedDeliveryGroups() {
    return data
      .getIn(['schedule', 'student_groups'], List())
      .reduce(
        (acc, studentGroup) =>
          acc.concat(studentGroup.get('session_group', List()).map((g) => g.get('_id'))),
        List()
      )
      .toJS();
  }

  function getSelectedSubjects() {
    return data
      .getIn(['schedule', 'subjects'], List())
      .map((subject) => subject.get('_subject_id'))
      .toJS();
  }

  // function getSelectedTopic() {
  //   return [];
  //   return data
  //     .getIn(['schedule', 'topic'], List())
  //     .map((subject) => subject.get('_id'))
  //     .toJS();
  // }

  function getSelectedStaffs() {
    return data
      .getIn(['schedule', 'staffs'], List())
      .map((subject) => subject.get('_id'))
      .toJS();
  }

  function getSelectedInfrastructure() {
    return data.getIn(['schedule', '_infra_id'], '');
  }

  function isSameRotationGroup(groupNo) {
    if (!isRotation) return false;
    const rotationCount = data.getIn(['schedule', 'rotation_count']);
    if (rotationCount === null) return false;
    return rotationCount !== groupNo;
  }

  const selectedCourseGroups = getSelectedCourseGroups();
  const selectedDeliveryGroups = getSelectedDeliveryGroups();
  const selectedSubjects = getSelectedSubjects();
  const selectedStaffs = getSelectedStaffs();
  //const selectedTopic = getSelectedTopic();

  function getTableData() {
    const sessionFlow = data.getIn(['schedule', 'subjects', 0, 'session_flow']);

    return (
      <div className="go-wrapper " style={{ maxHeight: '500px' }}>
        <table className="table">
          {/* <Table striped bordered hover responsive> */}
          <thead>
            <tr className="border_top bg-lightgreen">
              <th className="border">
                <div className="aw-150 text-center"></div>
              </th>
              {sessionFlow
                .getIn([0, 'student_group'], List())
                .filter((item) => selectedCourseGroups.includes(item.get('_id')))
                .map((studentGroup) => {
                  const sessionGroupSize = studentGroup
                    .get('session_group', List())
                    .filter((item) => selectedDeliveryGroups.includes(item.get('_id'))).size;
                  return (
                    <th colSpan={sessionGroupSize} key={studentGroup.get('_id')} className="border">
                      <div
                        className={
                          sessionGroupSize === 2 ? 'aw-125 text-center' : 'aw-50 text-center'
                        }
                      >
                        {getFormattedGroupName(
                          studentGroupRename(studentGroup.get('group_name', ''), programId),
                          isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                        )}
                      </div>
                    </th>
                  );
                })}
            </tr>
          </thead>

          <tbody className="go-wrapper-height" style={{ maxHeight: '370px' }}>
            <tr className="tr-change">
              <td className="td-border">
                <div className="d-flex aw-150">
                  <div className="f-15 lecture_text bold pt-4"> Session </div>
                </div>
              </td>
              {sessionFlow
                .getIn([0, 'student_group'], List())
                .filter((item) => selectedCourseGroups.includes(item.get('_id')))
                .map((studentGroup) => {
                  return (
                    <React.Fragment key={studentGroup.get('_id')}>
                      {studentGroup
                        .get('session_group', List())
                        .filter((item) => selectedDeliveryGroups.includes(item.get('_id')))
                        .map((session) => {
                          return (
                            <td className="td-border" key={session.get('_id')}>
                              <div className="aw-50 text-center pl-1">
                                <Form.Check
                                  type="checkbox"
                                  label=""
                                  onClick={(e) =>
                                    checkStudentGroupStatus(
                                      e,
                                      session.get('_id'),
                                      studentGroup.get('_id')
                                    )
                                  }
                                />
                              </div>
                              <div className="pt-2 text-center f-15">
                                {' '}
                                {getFormattedGroupName(session.get('group_name', ''), 3)}
                              </div>
                            </td>
                          );
                        })}
                    </React.Fragment>
                  );
                })}
            </tr>

            {data.getIn(['schedule', 'subjects'], List()).map((subject) => {
              return subject.get('session_flow', List()).map((session) => {
                return (
                  <tr className="tr-change" key={session.get('_id')}>
                    <td className="td-border">
                      <div className="aw-150 d-flex">
                        <div>
                          <Form.Check
                            type="checkbox"
                            label=""
                            onClick={(e) =>
                              checkSessionGroupStatus(e, subject.get('_id'), session.get('_id'))
                            }
                          />
                        </div>

                        <div className="f-15 lecture_text">
                          {' '}
                          {`${session.get('delivery_symbol')}${session.get(
                            'delivery_no'
                          )} ${session.get('delivery_topic')}`}
                        </div>
                      </div>
                    </td>

                    {session
                      .get('student_group', List())
                      .filter((item) => selectedCourseGroups.includes(item.get('_id')))
                      .map((student_group) => {
                        return (
                          <React.Fragment key={student_group.get('_id')}>
                            {student_group
                              .get('session_group', List())
                              .filter((item) => selectedDeliveryGroups.includes(item.get('_id')))
                              .map((ses) => {
                                return (
                                  <td className="td-border" key={ses.get('_id')}>
                                    <div className="aw-50 text-center pl-1">
                                      <Form.Check
                                        type="checkbox"
                                        label=""
                                        disabled={
                                          ses.get('checked_status', false)
                                            ? false
                                            : ses.get('schedule_status', false)
                                        }
                                        checked={
                                          ses.get('schedule_status', false)
                                            ? true
                                            : ses.get('checked_status', false)
                                        }
                                        onChange={(e) =>
                                          checkScheduleStatus(
                                            e,
                                            subject.get('_id'),
                                            session.get('_id'),
                                            student_group.get('_id'),
                                            ses.get('_id')
                                          )
                                        }
                                      />
                                    </div>
                                  </td>
                                );
                              })}
                          </React.Fragment>
                        );
                      })}
                  </tr>
                );
              });
            })}

            {/* {sessionFlow.map((session) => {
              return (
                <tr className="tr-change" key={session.get('_id')}>
                  <td className="td-border">
                    sd
                    <div className="aw-150 d-flex">
                      <div>
                        <Form.Check
                          type="checkbox"
                          label=""
                          onClick={(e) => checkSessionGroupStatus(e, session.get('_id'))}
                        />
                      </div>

                      <div className="f-15 lecture_text">
                        {' '}
                        {`${session.get('delivery_symbol')}${session.get(
                          'delivery_no'
                        )} ${session.get('delivery_topic')}`}
                      </div>
                    </div>
                  </td>

                  {session
                    .get('student_group', List())
                    .filter((item) => selectedCourseGroups.includes(item.get('_id')))
                    .map((student_group) => {
                      return (
                        <React.Fragment key={student_group.get('_id')}>
                          {student_group
                            .get('session_group', List())
                            .filter((item) => selectedDeliveryGroups.includes(item.get('_id')))
                            .map((ses) => {
                              return (
                                <td className="td-border" key={ses.get('_id')}>
                                  <div className="aw-50 text-center pl-1">
                                    <Form.Check
                                      type="checkbox"
                                      label=""
                                      disabled={
                                        ses.get('checked_status', false)
                                          ? false
                                          : ses.get('schedule_status', false)
                                      }
                                      checked={
                                        ses.get('schedule_status', false)
                                          ? true
                                          : ses.get('checked_status', false)
                                      }
                                      onChange={(e) =>
                                        checkScheduleStatus(
                                          e,
                                          session.get('_id'),
                                          student_group.get('_id'),
                                          ses.get('_id')
                                        )
                                      }
                                    />
                                  </div>
                                </td>
                              );
                            })}
                        </React.Fragment>
                      );
                    })}
                </tr>
              );
            })} */}
          </tbody>
        </table>
      </div>
    );
  }

  const manualStaffOptions = useSelector((state) =>
    state.courseScheduling.getIn(['staffScheduleOptionList', 0], Map())
  );
  const classes = useMuiMultiSelectStyles();
  // const programType = [
  //   {
  //     name: 'Mobile Number *',
  //     value: 'Mobile Number',
  //   },
  //   {
  //     name: 'Email *',
  //     value: 'Email',
  //   },
  // ];
  // const withoutOutlineSelect = {
  //   '& .MuiOutlinedInput-input': {
  //     padding: '0px',
  //   },
  //   '&.MuiOutlinedInput-root': {
  //     width: '10em',
  //     '& fieldset': {
  //       border: 'none',
  //       color: 'red',
  //     },
  //     '&:hover fieldset': {
  //       border: 'none',
  //     },
  //     '&.Mui-focused fieldset': {
  //       border: 'none',
  //     },
  //   },
  // };
  return (
    <Dialog fullWidth={true} maxWidth={'md'} open={show} onClose={onHide}>
      <div className="p-3">
        <div className="model-main">
          <div className="row">
            <div
              className={`col-md-${
                data.getIn(['schedule', 'subjects'], List()).size > 0 ? '6' : '12'
              }`}
            >
              <p className="f-16 mb-3 bold">
                {jsUcfirstAll(data.getIn(['deliveryData', 'delivery_name'], ''))} Settings
              </p>

              {topics.length > 0 && (
                <div className="row pb-2">
                  <div className="col-md-5">
                    <p className="f-16 pt-2">Topics *</p>
                  </div>
                  <div className="col-md-7">
                    <FormControl fullWidth variant="outlined" size="small">
                      <Select
                        native
                        value={data.getIn(['schedule', '_topic_id'], '')}
                        onChange={(e) => handleChange('_topic_id', e.target.value)}
                      >
                        <option value=""></option>
                        {topics.map((option) => (
                          <option key={option.name} value={option.value}>
                            {option.name}
                          </option>
                        ))}
                      </Select>
                    </FormControl>
                    {/*
                    <FormControl fullWidth variant="outlined" size="small">
                      <Select
                        value={selectedTopic}
                        onChange={(e) => handleChange('topic', e.target.value)}
                        MenuProps={{
                          ...menuProps,
                          PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                        }}
                        renderValue={(selected) =>
                          topics
                            .reduce((acc, group) => {
                              if (!selected.includes(group.get('value'))) return acc;
                              return acc.push(group.get('name'));
                            }, List())
                            .join(', ')
                        }
                      >
                        {topics.map((topic) => (
                          <MenuItem className="white-space-normal" key={topic} value={topic}>
                            <ListItemIcon>
                              <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                                <Checkbox
                                  color="primary"
                                  checked={selectedSubjects.indexOf(topic) > -1}
                                  size="small"
                                />
                              </ThemeProvider>
                            </ListItemIcon>
                            <ListItemText primary={topic} />
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>*/}
                  </div>
                </div>
              )}

              <div className="row pb-2">
                <div className="col-md-5">
                  <p className="f-16 pt-2">Course Group *</p>
                </div>
                <div className="col-md-7">
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      keepMounted
                      multiple
                      value={selectedCourseGroups}
                      onChange={(e) => handleChange('student_groups', e.target.value)}
                      MenuProps={{
                        ...menuProps,
                        PaperProps: { style: { maxHeight: 200 } },
                      }}
                      renderValue={(selected) =>
                        courseGroups
                          .reduce((acc, group) => {
                            if (!selected.includes(group.get('value'))) return acc;
                            return acc.push(group.get('name'));
                          }, List())
                          .join(', ')
                      }
                    >
                      {courseGroups.map((option) => (
                        <MenuItem
                          className="white-space-normal"
                          key={option.get('value')}
                          value={option.get('value')}
                          disabled={isSameRotationGroup(option.get('groupNo'))}
                        >
                          <ListItemIcon>
                            <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                              <Checkbox
                                color="primary"
                                checked={selectedCourseGroups.indexOf(option.get('value')) > -1}
                                size="small"
                              />
                            </ThemeProvider>
                          </ListItemIcon>
                          <ListItemText primary={option.get('name')} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>
              <div className="row pb-2">
                <div className="col-md-5">
                  <p className="f-16 pt-2">Delivery Group *</p>
                </div>
                <div className="col-md-7">
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      keepMounted
                      multiple
                      value={selectedDeliveryGroups}
                      onChange={() => {}}
                      MenuProps={{
                        ...menuProps,
                        PaperProps: { style: { maxHeight: 200 } },
                      }}
                      renderValue={(selected) =>
                        deliveryGroups
                          .reduce((acc, group) => {
                            if (
                              !selected.includes(group.get('value')) ||
                              group.get('isStudentGroup')
                            )
                              return acc;
                            return acc.push(group.get('name'));
                          }, List())
                          .join(', ')
                      }
                      displayEmpty
                    >
                      {deliveryGroups.map((option) => {
                        const isStudentGroup = option.get('isStudentGroup');
                        const checked = isStudentGroup
                          ? getCourseDeliveryGroupCBState(
                              option.get('value'),
                              option.get('totalSessionGroups')
                            ) === 'checked'
                          : selectedDeliveryGroups.includes(option.get('value'));
                        return (
                          <MenuItem
                            key={option.get('value')}
                            value={option.get('value')}
                            disableGutters
                            className={`${classes[isStudentGroup ? 'single' : 'nested']} ${
                              isStudentGroup && checked ? classes.selectedAll : ''
                            }`}
                            onClick={() => handleDeliveryGroupChange(option)}
                            // disabled={studentGroupScheduleStatus.getIn(
                            //   [
                            //     option.get(isStudentGroup ? 'value' : 'studentGroupId'),
                            //     isStudentGroup
                            //       ? 'deliveryGroupParentDisabled'
                            //       : option.get('value'),
                            //   ],
                            //   false
                            // )}
                          >
                            <ListItemIcon className={classes.listItemIcon}>
                              <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                                <Checkbox
                                  size="small"
                                  color="primary"
                                  checked={checked}
                                  indeterminate={
                                    isStudentGroup
                                      ? getCourseDeliveryGroupCBState(
                                          option.get('value'),
                                          option.get('totalSessionGroups')
                                        ) === 'indeterminate'
                                      : false
                                  }
                                />
                              </ThemeProvider>
                            </ListItemIcon>
                            <ListItemText
                              primary={option.get('name')}
                              classes={{
                                primary: isStudentGroup ? classes.singleMenuItemLabel : '',
                              }}
                            />
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </div>
              </div>

              <div className="row pb-2">
                <div className="col-md-5">
                  <p className="f-16 pt-2">Subject *</p>
                </div>
                <div className="col-md-7">
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      keepMounted
                      multiple
                      value={selectedSubjects}
                      onChange={(e) => handleChange('subjects', e.target.value)}
                      MenuProps={{
                        ...menuProps,
                        PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                      }}
                      renderValue={(selected) =>
                        subjects
                          .reduce((acc, group) => {
                            if (!selected.includes(group.get('value'))) return acc;
                            return acc.push(group.get('name'));
                          }, List())
                          .join(', ')
                      }
                    >
                      {subjects.map((option) => (
                        <MenuItem
                          className="white-space-normal"
                          key={option.get('value')}
                          value={option.get('value')}
                        >
                          <ListItemIcon>
                            <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                              <Checkbox
                                color="primary"
                                checked={selectedSubjects.indexOf(option.get('value')) > -1}
                                size="small"
                              />
                            </ThemeProvider>
                          </ListItemIcon>
                          <ListItemText primary={option.get('name')} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>

              <div className="row pb-2">
                <div className="col-md-5">
                  <p className="f-16 pt-2">Mode *</p>
                </div>
                <div className="col-md-7">
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      native
                      value={data.getIn(['schedule', 'mode'], '')}
                      onChange={(e) => handleChange('mode', e.target.value)}
                    >
                      <option value=""></option>
                      {getModes(programId).map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.name}
                        </option>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>

              <div className="row pb-2">
                <div className="col-md-5">
                  <p className="f-16 pt-2">
                    Infrastructure {data.getIn(['schedule', 'mode'], '') === 'remote' && '*'}
                  </p>
                </div>
                <div className="col-md-7">
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      value={infrastructures.isEmpty() ? '' : getSelectedInfrastructure()}
                      onChange={(e) => {}}
                      MenuProps={{
                        ...menuProps,
                        PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                      }}
                      displayEmpty
                      renderValue={(value) => {
                        const infra =
                          infrastructures.find((infra) => infra.get('value') === value) || Map();
                        return infra.get('name', '');
                      }}
                    >
                      {!infrastructures.isEmpty() && (
                        <MenuItem value="" onClick={() => handleChange('infra', Map())}>
                          <ListItemText primary="Unselect" />
                        </MenuItem>
                      )}
                      {infrastructures.map((option) => (
                        <MenuItem
                          key={option.get('value')}
                          value={option.get('value')}
                          className="white-space-normal"
                          onClick={() => handleChange('infra', option)}
                        >
                          <ListItemText
                            primary={option.get('name')}
                            secondary={
                              <span className="d-block">
                                <span className="d-block">{option.get('secondaryTitle1', '')}</span>
                                <span className="d-block">{option.get('secondaryTitle2', '')}</span>
                              </span>
                            }
                          />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>

              <div className="row pb-2">
                <div className="col-md-5">
                  <p className="f-16 pt-2">Staff *</p>
                </div>
                <div className="col-md-7">
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      keepMounted
                      multiple
                      value={selectedStaffs}
                      onChange={(e) => handleChange('staffs', e.target.value)}
                      MenuProps={{
                        ...menuProps,
                        PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                      }}
                      renderValue={(selected) =>
                        staffs
                          .reduce((acc, group) => {
                            if (!selected.includes(group.get('value'))) return acc;
                            return acc.push(group.get('name'));
                          }, List())
                          .join(', ')
                      }
                    >
                      {staffs
                        .sort((a, b) => (a.get('name', '') > b.get('name', '') ? 1 : -1))
                        .map((option) => (
                          <MenuItem
                            className="white-space-normal"
                            key={option.get('value')}
                            value={option.get('value')}
                          >
                            <ListItemIcon>
                              <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                                <Checkbox
                                  color="primary"
                                  checked={selectedStaffs.indexOf(option.get('value')) > -1}
                                  size="small"
                                />
                              </ThemeProvider>
                            </ListItemIcon>
                            <ListItemText primary={option.get('name')} />
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </div>
              </div>
              {isManualAttendanceEnabled() &&
                data.getIn(['schedule', 'mode'], '') !== 'remote' &&
                manualStaffOptions.get('manualAttendance', false) &&
                manualStaffOptions.get('assignedStaffIds', List()).size > 0 && (
                  <div className="row align-items-center pt-2 pb-2">
                    <div className="col-md-5">
                      <div className="f-16">Attendance taking Staff</div>
                    </div>
                    <div className="col-md-7">
                      <MaterialInput
                        elementType={'materialSelectMultiple'}
                        disabled={mode === 'view'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{
                          options: getManualStaffOption(
                            manualStaffOptions.get('assignedStaffIds', List())
                          ).toJS(),
                        }}
                        label={''}
                        labelclass={'mb-0 f-14'}
                        changed={(e) => handleChange('manualStaffs', e.target.value)}
                        isAllSelected={false}
                        value={data.getIn(['schedule', 'manualStaffs'], List()).toJS()}
                        multiple={true}
                      />
                    </div>
                  </div>
                )}
              {/* {isModuleEnabled('OUTSIDE_CAMPUS') && (
                <Fragment>
                  {data.getIn(['schedule', 'outsideCampus'], false) ? (
                    <div className="row align-items-center pt-2 pb-2">
                      <div className="col-md-5">
                        <div className="f-16">
                          <MaterialInput
                            elementType={'materialSelect'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                            sx={withoutOutlineSelect}
                            elementConfig={{ options: programType }}
                            changed={(e) => handleChange('selectNumOrEmail', e.target.value)}
                            value={data.getIn(['schedule', 'selectNumOrEmail'], '')}
                          />
                        </div>
                      </div>
                      <div className="col-md-7">
                        <MaterialInput
                          elementType={'materialInput'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          changed={(e) => handleChange('mobileNumberOrEmail', e.target.value)}
                          value={data.getIn(['schedule', 'mobileNumberOrEmail'], '')}
                        />
                      </div>
                    </div>
                  ) : (
                    ''
                  )}
                </Fragment>
              )} */}
            </div>
            {data.getIn(['schedule', 'subjects'], List()).size > 0 && (
              <div className="col-md-6">{getTableData()}</div>
            )}
          </div>
        </div>
        <DialogActions className="mt-3">
          <div className="d-flex justify-content-end">
            <ThemeProvider theme={MUI_THEME}>
              <div className="mr-2">
                <Button variant="outlined" color="primary" onClick={onHide}>
                  CANCEL
                </Button>
              </div>
              <div>
                <Button variant="contained" color="primary" onClick={() => onSave(mode)}>
                  SAVE
                </Button>
              </div>
            </ThemeProvider>
          </div>
        </DialogActions>
      </div>
    </Dialog>
  );
}

AddEditCourseScheduleModal.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  onChange: PropTypes.func,
  onSave: PropTypes.func,
  checkScheduleStatus: PropTypes.func,
  checkStudentGroupStatus: PropTypes.func,
  checkSessionGroupStatus: PropTypes.func,
  mode: PropTypes.string,
  data: PropTypes.instanceOf(Map),
  course: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  activeSessionFlow: PropTypes.instanceOf(Map),
  getScheduleAvailability: PropTypes.func,
  studentGroupScheduleStatus: PropTypes.instanceOf(Map),
  isRotation: PropTypes.bool,
  programId: PropTypes.string,
};

export default AddEditCourseScheduleModal;
