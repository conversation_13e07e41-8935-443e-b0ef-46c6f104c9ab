import React, { useState, useEffect } from 'react';
import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  FormControlLabel,
  Paper,
  Typography,
  Stack,
  Avatar,
  Divider,
  Button,
} from '@mui/material';
import Switch from '@mui/material/Switch';
import Checkbox from '@mui/material/Checkbox';
import { List, fromJS, Map } from 'immutable';
import moment from 'moment';
import PropTypes from 'prop-types';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useHistory } from 'react-router-dom';

import { useStylesFunction } from '../css/designUtils';
import { validation, isSameTimeForEveryDayValidation } from '../utils';
import SessionTiming from './SessionTiming';
import MButton from 'Widgets/FormElements/material/Button';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { useDispatch } from 'react-redux';
import {
  updateSchedulePermission,
  updateFacialAndSelfRegisterDocumentToggle,
} from '_reduxapi/global_configuration/v1/actions';

const initialPermissionsState = Map();
function SessionDetails({ setData, postGlobalSessionSetting, globalSessionSetting }) {
  const classes = useStylesFunction();
  const history = useHistory();
  const dispatch = useDispatch();
  const sessionData = fromJS([
    {
      sessionName: '',
      sessionStart: new Date().setHours(0, 0, 0, 0),
      sessionEnd: new Date().setHours(0, 0, 0, 0),
    },
    {
      sessionName: '',
      sessionStart: new Date().setHours(0, 0, 0, 0),
      sessionEnd: new Date().setHours(0, 0, 0, 0),
    },
  ]);
  const workingDays = [
    'sunday',
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
  ];
  const workingDaysData = workingDays.map((day) => ({
    day,
    isActive: false,
    sessions: sessionData,
  }));

  const [expanded, setExpanded] = useState(true);
  const [expandedIndex, setExpandedIndex] = useState(true);
  const [sessionDetails, setSessionDetails] = useState(
    fromJS({
      isSameTimeForEveryDay: true,
      scheduleForNonWorkingDays: false,
      workingDays: workingDaysData,
      sessions: sessionData,
    })
  );
  const [permissions, setPermissions] = useState(initialPermissionsState);

  const noOFSelectedDays = sessionDetails.get('workingDays', List()).filter((item) => {
    return item.get('isActive', false) === true;
  });
  useEffect(() => {
    if (globalSessionSetting.size) {
      setSessionDetails(
        globalSessionSetting.set('sessions', globalSessionSetting.get('sessions', List()))
      );
    }
  }, [globalSessionSetting]);

  const handleAccordionChange = (panel) => (event, isExpanded) => {
    event.stopPropagation();
    if (panel === 'panel1') setExpanded(isExpanded ? panel : false);
    else {
      setExpandedIndex(isExpanded ? panel : false);
      isExpanded === false && isSameTimeForEveryDayValidation(sessionDetails, setSessionDetails);
      // validation({ sessionDetails, setSessionDetails, setData });
    }
  };

  const timeValidation = (index, value, type, dayIndex) => {
    if (sessionDetails.get('isSameTimeForEveryDay', false)) {
      if (
        type === 'sessionEnd' &&
        value < sessionDetails.getIn(['sessions', index, 'sessionStart'], '')
      ) {
        setData(Map({ message: 'End time must greater than start time' }));
        return false;
      }
    } else {
      if (
        type === 'sessionEnd' &&
        value <
          sessionDetails.getIn(['workingDays', dayIndex, 'sessions', index, 'sessionStart'], '')
      ) {
        setData(Map({ message: 'End time must greater than start time' }));
        return false;
      }
    }
    return true;
  };

  const handleChange = (e, index, type, dayIndex) => {
    const value = type === 'sessionName' ? e.target.value : new Date(e);
    if (type !== 'sessionName') {
      if (!timeValidation(index, value, type, dayIndex)) return;
    }

    let newValue = sessionDetails;
    switch (type) {
      case 'sessionStart': {
        if (sessionDetails.get('isSameTimeForEveryDay', false)) {
          newValue = newValue
            .setIn(['sessions', index, type], value)
            .setIn(['sessions', index, 'sessionEnd'], moment(value).add(3, 'hour'));
        } else {
          newValue = newValue
            .setIn(['workingDays', dayIndex, 'sessions', index, type], value)
            .setIn(['workingDays', dayIndex, 'sessions', index, 'error'], false)
            .setIn(
              ['workingDays', dayIndex, 'sessions', index, 'sessionEnd'],
              moment(value).add(3, 'hour')
            );
        }
        break;
      }
      default:
        if (sessionDetails.get('isSameTimeForEveryDay', false)) {
          newValue = newValue.setIn(['sessions', index, type], value);
        } else {
          newValue = newValue
            .setIn(['workingDays', dayIndex, 'sessions', index, type], value)
            .setIn(['workingDays', dayIndex, 'sessions', index, 'error'], false);
        }
        break;
    }
    setSessionDetails(newValue);

    // if (sessionDetails.get('isSameTimeForEveryDay', false)) {
    //   setSessionDetails(sessionDetails.setIn(['sessions', index, type], value));
    // } else {
    //   setSessionDetails(
    //     sessionDetails
    //       .setIn(['workingDays', dayIndex, 'sessions', index, type], value)
    //       .setIn(['workingDays', dayIndex, 'sessions', index, 'error'], false)
    //   );
    // }
  };

  const handleClickDay = (index, isActive) => {
    setSessionDetails(
      sessionDetails
        .setIn(['workingDays', index, 'isActive'], !isActive)
        .setIn(['workingDays', index, 'error'], false)
    );
  };
  // addSession
  // const addSession = (index) => {
  //   const sessionData = Map({
  //     sessionName: '',
  //     sessionStart: new Date(),
  //     sessionEnd: new Date(),
  //   });
  //   if (sessionDetails.get('isSameTimeForEveryDay', false))
  //     setSessionDetails(
  //       sessionDetails.set('sessions', sessionDetails.get('sessions', List()).push(sessionData))
  //     );
  //   else
  //     setSessionDetails(
  //       sessionDetails.setIn(
  //         ['workingDays', index, 'sessions'],
  //         sessionDetails.getIn(['workingDays', index, 'sessions'], List()).push(sessionData)
  //       )
  //     );
  // };

  // const removeSession = (index, dayIndex) => {
  //   if (sessionDetails.get('isSameTimeForEveryDay', false))
  //     setSessionDetails(sessionDetails.deleteIn(['sessions', index]));
  //   else setSessionDetails(sessionDetails.deleteIn(['workingDays', dayIndex, 'sessions', index]));
  // };

  const getTotalHours = (session) => {
    const totalMinutes = session.reduce((acc, item) => {
      const start = moment(item.get('sessionStart', ''));
      const end = moment(item.get('sessionEnd', ''));
      return end.diff(start, 'minutes') + acc;
    }, 0);
    const duration = moment.duration(totalMinutes, 'minutes');
    const hours = duration.hours();
    const mins = duration.minutes();
    return `${hours} hr ${mins} mins`;
  };

  const handleChecked = (e) => {
    setSessionDetails(
      sessionDetails
        .set('isSameTimeForEveryDay', e)
        .set('sessions', sessionData)
        .set('scheduleForNonWorkingDays', false)
        .set(
          'workingDays',
          sessionDetails
            .get('workingDays', List())
            .map((item) => item.set('isActive', false).set('sessions', sessionData))
        )
    );
  };

  const onSubmit = () => {
    if (!validation({ sessionDetails, setSessionDetails, setData })) return;
    postGlobalSessionSetting(sessionDetails);
  };

  const SwitchApiStaffFacial = (e) => {
    const callBack = () => {
      setData(
        Map({ globalSessionSetting: globalSessionSetting.set('staffFacial', newStaffFacial) })
      );
    };
    const newStaffFacial = e.target.checked;
    dispatch(
      updateFacialAndSelfRegisterDocumentToggle({
        isFacial: newStaffFacial,
        type: 'staff',
        callBack,
      })
    );
  };

  // student facial toggle button
  const SwitchApiStudentFacial = (e) => {
    const callBack = () => {
      setData(
        Map({ globalSessionSetting: globalSessionSetting.set('studentFacial', newStudentFacial) })
      );
    };
    const newStudentFacial = e.target.checked;
    dispatch(
      updateFacialAndSelfRegisterDocumentToggle({
        isFacial: newStudentFacial,
        type: 'student',
        callBack,
      })
    );
  };

  // student self registration document toggle button
  const SwitchApiStudentSelfRegistration = (e) => {
    const callBack = () => {
      setData(
        Map({
          globalSessionSetting: globalSessionSetting.set(
            'selfRegistrationDocument',
            studentSelfRegistration
          ),
        })
      );
    };
    const studentSelfRegistration = e.target.checked;
    dispatch(
      updateFacialAndSelfRegisterDocumentToggle({
        isSelfRegistration: studentSelfRegistration,
        callBack,
      })
    );
  };

  const handleSchedulePermission = (panel) => (event, isExpanded) => {
    event.stopPropagation();
    setExpanded(isExpanded ? panel : false);
  };

  const handleCreateSwitch = () => {
    setPermissions((prevState) => prevState.set('create', !prevState.get('create')));
  };

  const handleEditSwitch = () => {
    setPermissions((prevState) => prevState.set('edit', !prevState.get('edit')));
  };

  const handleSave = () => {
    const callBack = (shedulePermissionBool) => {
      setData(
        Map({
          globalSessionSetting: globalSessionSetting.set(
            'schedulePermission',
            shedulePermissionBool
          ),
        })
      );
    };
    dispatch(updateSchedulePermission(permissions, callBack));
  };
  useEffect(() => {
    setPermissions(
      Map({
        create: globalSessionSetting.getIn(['schedulePermission', 'create'], false),
        edit: globalSessionSetting.getIn(['schedulePermission', 'edit'], false),
      })
    );
  }, [globalSessionSetting]);

  const handleCancel = () => {
    setPermissions(
      Map({
        create: globalSessionSetting.getIn(['schedulePermission', 'create'], false),
        edit: globalSessionSetting.getIn(['schedulePermission', 'edit'], false),
      })
    );
    setExpanded(false);
  };
  const AvatarClass = { bgcolor: '#7440a8', width: '54px', height: '51px' };
  const AvatarNewClass = { bgcolor: '#1460a1', width: '54px', height: '51px' };
  return (
    <React.Fragment>
      <div className="d-flex align-items-center pt-3 pb-1">
        <HomeIcon fontSize="small" className="digi-gray-neutral" />
        <span
          className="ml-2 mr-2 remove_hover"
          onClick={() => history.push('/globalConfiguration-v1/institution')}
        >
          {' '}
          All Modules
        </span>
        <KeyboardArrowRightIcon fontSize="" />
        <span className="ml-2 text-skyblue"> Basic Details</span>
      </div>
      <Divider />
      <div className="p-3">
        <Accordion
          expanded={expanded === 'panel1'}
          onChange={handleAccordionChange('panel1')}
          className={classes.accordionBackgroundNone}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <div className="d-flex">
              <div className="workDay">
                <p className="mb-0 text-green f-15">SW</p>
              </div>
              <div className="ml-3">
                <p className="mb-1 bold f-19">Specify The Working Days</p>
                <p className="mb-0 f-15 digi-gray">
                  <div>No Working Days : {noOFSelectedDays.size}</div>
                </p>
              </div>
            </div>
          </AccordionSummary>
          <AccordionDetails>
            <div className="d-flex justify-content-end border-top pt-3 pb-3">
              {CheckPermission(
                'tabs',
                'Global Configuration',
                'Institution',
                '',
                'Basic Details',
                'Edit'
              ) && (
                <MButton variant="contained" color="primary" clicked={onSubmit}>
                  Save
                </MButton>
              )}
            </div>
            <div className="w-100">
              <div className="d-flex justify-content-between align-items-center">
                <>
                  <p className="mb-1 bold f-19">Same Time For Every Day</p>
                  <p className="mb-0 f-15 digi-gray-neutral">
                    For this you can set the particular day Timing & Session
                  </p>
                </>
                <FormControlLabel
                  control={
                    <Switch
                      checked={sessionDetails.get('isSameTimeForEveryDay', false)}
                      onChange={(e) => handleChecked(e.target.checked)}
                    />
                  }
                  label="Yes"
                />
              </div>
              {sessionDetails.get('isSameTimeForEveryDay', false) ? (
                <>
                  <div className="d-flex align-items-center pt-4">
                    <p className="mr-4 mb-0"> Days :</p>

                    {sessionDetails.get('workingDays', List()).map((item, index) => (
                      <div
                        className={item.get('isActive', false) ? 'circle day-bg-color' : 'circle'}
                        key={index}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleClickDay(index, item.get('isActive', false));
                        }}
                      >
                        <p className="mb-0 f-15 text-capitalize">{item.get('day', '').charAt(0)}</p>
                      </div>
                    ))}
                  </div>
                  <div className="d-flex justify-content-between align-items-center pt-4">
                    <p className="mb-2 f-15 digi-gray"> Session Timing</p>
                    <p className="mb-2 f-15 digi-gray">
                      {' '}
                      Total : {getTotalHours(sessionDetails.get('sessions', List()))}
                    </p>
                  </div>
                  <div className="timeBox">
                    <SessionTiming
                      sessionList={sessionDetails.get('sessions', List())}
                      handleChange={handleChange}
                      // removeSession={removeSession}
                    />

                    {/* add session */}
                    {/* <p
                      className="p-2 mb-0 f-15 text-skyblue remove_hover"
                      onClick={() => addSession()}
                    >
                      + Add Session
                    </p> */}
                  </div>
                </>
              ) : (
                <div className="mt-4">
                  {sessionDetails.get('workingDays', List()).map((item, dayIndex) => {
                    return (
                      <Accordion
                        expanded={expandedIndex === dayIndex}
                        onChange={handleAccordionChange(dayIndex)}
                        className={
                          item.get('error', false) && item.get('isActive', true)
                            ? classes.BackgroundRed
                            : classes.BackgroundNone
                        }
                        key={dayIndex}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon className="m-0 p-0" />}
                          className={classes.marginReduce}
                        >
                          <div className="d-flex align-items-center w-100">
                            <Checkbox
                              className="p-0"
                              checked={item.get('isActive', false)}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleClickDay(dayIndex, item.get('isActive', false));
                              }}
                            />
                            <p className="mb-0 ml-2 w-10 bold text-capitalize">{item.get('day')}</p>
                            {item.get('sessions', List()).map((time, timeIndex) => (
                              <p className="mb-0 mr-3" key={timeIndex}>
                                {' '}
                                • {moment(time.get('sessionStart', '')).format('hh:mm a')} -{' '}
                                {moment(time.get('sessionEnd', '')).format('hh:mm a')}
                              </p>
                            ))}

                            <p className="mb-0 mr-3">
                              • Total : {getTotalHours(item.get('sessions', List()))}
                            </p>
                            {item.get('error', false) && item.get('isActive', true) ? (
                              <div className="d-flex ml-auto mr-1">
                                <ErrorOutlineIcon color="warning"></ErrorOutlineIcon>
                              </div>
                            ) : (
                              ''
                            )}
                          </div>
                        </AccordionSummary>
                        <AccordionDetails>
                          <div className="w-100">
                            <div className="timeBox">
                              <SessionTiming
                                sessionList={item.get('sessions', List())}
                                handleChange={handleChange}
                                // removeSession={removeSession}
                                dayIndex={dayIndex}
                              />
                              {/* add session */}
                              {/* <p
                              className="p-2 mb-0 f-15 text-skyblue remove_hover"
                              onClick={() => addSession(dayIndex)}
                            >
                              {' '}
                              + Add Session
                            </p> */}
                            </div>
                          </div>
                        </AccordionDetails>
                      </Accordion>
                    );
                  })}
                </div>
              )}

              {/* <FormControlLabel
                value="end"
                onClick={(e) =>
                  setSessionDetails(
                    sessionDetails.set('scheduleForNonWorkingDays', e.target.checked)
                  )
                }
                control={
                  <Checkbox checked={sessionDetails.get('scheduleForNonWorkingDays', false)} />
                }
                label="Allow Schedule on Non - Working Days / Hours"
                labelPlacement="end"
              /> */}
            </div>
          </AccordionDetails>
        </Accordion>
        <Paper className="mt-3">
          <div className="p-3 d-flex align-items-center">
            <Avatar sx={AvatarClass} variant="rounded">
              SF
            </Avatar>
            <div className="ml-3">
              <div className="f-19 bold">Staff Facial</div>
              <div className="f-15">Staff Face Verification Can Be Enable Or Disable </div>
            </div>
            <div className="ml-auto">
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography>Off</Typography>
                <Switch
                  checked={globalSessionSetting.get('staffFacial', false)}
                  onClick={SwitchApiStaffFacial}
                />
                <Typography>On</Typography>
              </Stack>
            </div>
          </div>
        </Paper>
        <Paper className="mt-3">
          <div className="p-3 d-flex align-items-center">
            <Avatar sx={AvatarClass} variant="rounded">
              STF
            </Avatar>
            <div className="ml-3">
              <div className="f-19 bold">Student Facial</div>
              <div className="f-15">Student Face Verification Can Be Enable Or Disable </div>
            </div>
            <div className="ml-auto">
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography>Off</Typography>
                <Switch
                  checked={globalSessionSetting.get('studentFacial', false)}
                  onClick={SwitchApiStudentFacial}
                />
                <Typography>On</Typography>
              </Stack>
            </div>
          </div>
        </Paper>
        <Paper className="mt-3">
          <div className="p-3 d-flex align-items-center">
            <Avatar sx={AvatarClass} variant="rounded">
              SRD
            </Avatar>
            <div className="ml-3">
              <div className="f-19 bold">Student Self Registration Document</div>
              <div className="f-15">
                Student Self Registration Document Can Be Enable Or Disable
              </div>
            </div>
            <div className="ml-auto">
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography>Off</Typography>
                <Switch
                  checked={globalSessionSetting.get('selfRegistrationDocument', false)}
                  onClick={SwitchApiStudentSelfRegistration}
                />
                <Typography>On</Typography>
              </Stack>
            </div>
          </div>
        </Paper>
        <div className="mt-3">
          <Accordion
            elevation={2}
            expanded={expanded === 'panel2'}
            onChange={handleSchedulePermission('panel2')}
            className={classes.accordionBackgroundNone}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <div className="d-flex">
                <Avatar sx={AvatarNewClass} variant="rounded">
                  SP
                </Avatar>
                <div className="ml-3">
                  <div className="f-19 bold">Schedule Permission</div>
                  <div className="f-15">Allow Schedule Permission for Faculty</div>
                </div>
              </div>
            </AccordionSummary>
            <AccordionDetails>
              <Divider />
              <div className="ml-5 mr-3">
                <div className="d-flex w-100 align-items-center">
                  <Avatar sx={{ bgcolor: '#ff7691' }}>CP</Avatar>
                  <div className="p-3 d-flex align-items-center w-100">
                    <div className="ml-3">
                      <div className="f-19 bold">Create Permission</div>
                    </div>
                    <div className="ml-auto">
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography>Off</Typography>
                        <Switch
                          checked={permissions.get('create', false)}
                          onClick={handleCreateSwitch}
                        />
                        <Typography>On</Typography>
                      </Stack>
                    </div>
                  </div>
                </div>
                <div className="d-flex w-100 align-items-center">
                  <Avatar sx={{ bgcolor: '#006ef9' }}>EP</Avatar>
                  <div className="p-3 d-flex align-items-center w-100">
                    <div className="ml-3">
                      <div className="f-19 bold">Edit Permission</div>
                    </div>
                    <div className="ml-auto">
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography>Off</Typography>
                        <Switch
                          checked={permissions.get('edit', false)}
                          onClick={handleEditSwitch}
                        />
                        <Typography>On</Typography>
                      </Stack>
                    </div>
                  </div>
                </div>
              </div>
              <div className="d-flex">
                <Stack spacing={2} direction="row" className="ml-auto">
                  <Button variant="outlined" onClick={handleCancel}>
                    Cancel
                  </Button>
                  <Button variant="contained" onClick={handleSave}>
                    Save
                  </Button>
                </Stack>
              </div>
            </AccordionDetails>
          </Accordion>
        </div>
      </div>
    </React.Fragment>
  );
}

SessionDetails.propTypes = {
  setData: PropTypes.func,
  postGlobalSessionSetting: PropTypes.func,
  globalSessionSetting: PropTypes.func,
};

export default SessionDetails;
