import React, { Fragment, useReducer, useEffect, useState } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { connect } from 'react-redux';
import { NotificationManager } from 'react-notifications';
import PropTypes from 'prop-types';

import {
  Null,
  PrimaryButton,
  FlexWrapper,
  Padding,
  EventWrapper,
  ModalWrapper,
  ModalBackgroundWrapper,
} from '../../Styled';
import swal from 'sweetalert2';
import {
  changeTitle,
  courseSave,
  toggleModal,
  editCourseSave,
  saveCourseEvents,
  updateCourseEvents,
  getCourses,
  getBatchCourses,
  rotationalSave,
  editRotationalSave,
  getData,
  getRotationalCourses,
  listCourseEvents,
  resetMessage,
  changeTerm,
} from '../../../../_reduxapi/actions/calender';
import { nonRotational } from './InitialState';
import { rootNonRotationalReducer } from './CourseReducer';
import ChooseLevel from './ChooseLevel';
import BackgroundSelect from './BackgroundSelect';
import CourseTitle from './CourseTitle';
import CourseDuration from './CourseDuration';
import EventRows from '../../UtilityComponents/EventRows';
// import CourseGroup from './CourseGroup';
import AddEvent from '../../Modal/Events/AddEvent';
import CourseAddMode from './CourseAddMode';
import Loader from '../../../../Widgets/Loader/Loader';
// import CheckIds from '../../UtilityComponents/CheckIds';
import moment from 'moment';
import { getLang, jsUcfirstAll, timeFormat } from '../../../../utils';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
const lang = getLang();

const CourseInput = (props) => {
  const {
    changeTitle,
    courseSave,
    _calendar_id,
    courses,
    isLoading,
    toggleModal,
    editCourseSave,
    saveCourseEvents,
    updateCourseEvents,
    getData,
    apiCalled,
    getRotationalCourses,
    listCourseEvents,
    course_events,
    course_events_copy,
    error,
    success,
    resetMessage,
    activeInstitutionCalendar,
    changeTerm,
  } = props;

  const non_rotation = useReducer(rootNonRotationalReducer, nonRotational);
  const [nonRotation, setNonRotation] = non_rotation;
  const history = useHistory();
  const match = useRouteMatch();
  const active = match.params.year || 'year1';

  const [loaded, setLoaded] = useState(false);
  const [count, setCount] = useState(0);
  const localLoad = useState(false);
  const [loaderState, setLoaderState] = localLoad;

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let programId = params.get('programid');
  let urlYear = params.get('year');
  let urlName = params.get('pname');
  let urlNumber = params.get('number');
  let editCourse = params.get('edit');
  let urlTerm = params.get('term');

  useEffect(() => {
    changeTerm(urlTerm);
  }, [urlTerm, changeTerm]);

  let _id = params.get('_id');
  let titleHeader = t('role_management.role_actions.Add Course');
  let saveButton = t('add');
  if (editCourse !== null) {
    titleHeader = t('program_calendar.edit_course');
    saveButton = t('save');
  }

  useEffect(() => {
    if (
      nonRotation.title !== '' &&
      nonRotation.type.start_date !== '' &&
      nonRotation.type.end_date !== '' &&
      nonRotation.type._course_id !== '' &&
      nonRotation.triggerLists &&
      editCourse === null
    ) {
      let level_no = nonRotation.title;
      listCourseEvents(
        _calendar_id,
        level_no,
        nonRotation.type._course_id,
        nonRotation.type.start_date,
        nonRotation.type.end_date,
        'add'
      );
      setNonRotation({ type: 'STOP_LISTING', payload: false });
    } else if (
      nonRotation.title !== '' &&
      nonRotation.type.start_date !== '' &&
      nonRotation.type.end_date !== '' &&
      nonRotation.type._course_id !== '' &&
      nonRotation.triggerLists &&
      editCourse !== null
    ) {
      let level_no = nonRotation.title;
      listCourseEvents(
        _calendar_id,
        level_no,
        nonRotation.type._course_id,
        nonRotation.type.start_date,
        nonRotation.type.end_date,
        'edit'
      );
      setNonRotation({ type: 'STOP_LISTING', payload: false });
    }
  }, [nonRotation, setNonRotation, editCourse, _calendar_id, listCourseEvents]);

  useEffect(() => {
    setNonRotation({
      type: 'EVENT_ATTACH',
      payload: course_events,
    });
  }, [course_events, setNonRotation]);

  useEffect(() => {
    if (editCourse !== null) {
      setNonRotation({
        type: 'EVENT_COPY_TO_DELETE',
        payload: course_events_copy,
      });
    }
  }, [course_events_copy, editCourse, setNonRotation]);

  useEffect(
    () => {
      if (error !== null) {
        NotificationManager.error(error);
        setTimeout(() => {
          resetMessage();
        }, 2000);
      } else if (success != null) {
        //NotificationManager.success(success);
        setNonRotation({
          type: 'OFF_MODAL',
        });
        // setTimeout(() => {
        //   resetMessage();
        // }, 2000);
        if (
          success === jsUcfirstAll('Program calendar course added successfully') ||
          success === jsUcfirstAll('Program calendar course updated successfully')
        ) {
          history.push(
            `/program-calendar/${match.params.id}/${match.params.year}?year=${urlYear}&programid=${programId}&pname=${urlName}&term=${urlTerm}`
          );
        } else if (
          success === 'Program Calendar Course Event Added Successfully' ||
          success === 'Program Calendar Course Event Updated Successfully'
        ) {
          NotificationManager.success(success);
          setTimeout(() => {
            resetMessage();
          }, 2000);
        }
      }
    },
    /* eslint-disable */
    [
      error,
      success,
      resetMessage,
      history,
      match.params.id,
      match.params.year,
      programId,
      setNonRotation,
      urlName,
      urlYear,
    ]
    /* eslint-enable */
  );

  useEffect(() => {
    if (editCourse !== null && count === 0) {
      let activeArray = [];
      if (
        props[active] !== undefined &&
        props[active]['level'] &&
        props[active]['level'].length > 0
      ) {
        activeArray = props[active]['level'].filter((list) => {
          return list._id === _id;
        });
      }
      if (activeArray && activeArray.length > 0) {
        let actArray = activeArray[0];
        setNonRotation({
          type: 'INITIAL_LOAD_EDIT_COURSE',
          payload: actArray,
          course_id: editCourse,
          levels: props[active]['level'],
          number: urlNumber,
        });
        setCount(1);
      }
    }
  }, [editCourse, props, _id, count, setCount, urlNumber, setNonRotation, active]);

  useEffect(() => {
    if (apiCalled && !loaded && activeInstitutionCalendar && !activeInstitutionCalendar.isEmpty()) {
      console.log('landing5'); //eslint-disable-line
      getData(
        null,
        NotificationManager,
        setLoaded,
        match.params.id,
        activeInstitutionCalendar.get('_id')
      );
    }
  }, [apiCalled, loaded, getData, match.params.id, setLoaded, activeInstitutionCalendar]);

  useEffect(() => {
    changeTitle(titleHeader);
  });

  useEffect(() => {
    setNonRotation({
      type: 'ADD_COURSES',
      payload: courses,
    });
  }, [setNonRotation, courses]);

  const eventDeleteConfirm = (i, event_id) => {
    swal
      .fire({
        title: t('sure_delete'),
        text: t('once_deleted_cant_recover'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: t('yes_delete'),
        cancelButtonText: t('no_keep'),
        dangerMode: true,
      })
      .then((res) => {
        if (res.isConfirmed) {
          setNonRotation({
            type: 'DELETE_EVENT',
            payload: i,
            event_id: event_id,
          });

          NotificationManager.success('Program Calendar Course Event Deleted Successfully');
          setTimeout(() => {
            resetMessage();
          }, 2000);
        }
      });
  };

  const dataEventAlign = (content, rotation, rotationCount) => {
    let error = false;
    let final = { event_name: {} };

    let data = content.modal_content;

    let levelTitle = content.title;
    let courseId = content.type._course_id;

    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (courseId === '') {
      NotificationManager.error('Course id is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.start_date) > Date.parse(data.end_date)) {
      error = true;
      NotificationManager.error('End date should be greater than start date');
    }

    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    if (rotation === 'yes') {
      final.rotation_count = rotationCount;
    }
    final.event_calendar = 'course';
    final.year = active;
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final.end_date = endDate;
    final._calendar_id = _calendar_id;
    final.batch = urlTerm;
    final._course_id = courseId;
    if (!error) {
      saveCourseEvents(
        final,
        _calendar_id,
        levelTitle,
        courseId,
        content.type.start_date,
        content.type.end_date
      );
    }
  };

  const updateEvent = (content, rotation, rotationCount) => {
    let error = false;
    let final = { event_name: {} };

    let data = content.modal_content;
    let levelTitle = content.title;
    let courseId = content.type._course_id;
    const start = data.start_date + ' ' + data.start_time + ':00:000';
    const end = data.end_date + ' ' + data.end_time + ':00:000';

    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (courseId === '') {
      NotificationManager.error('Course id is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.end_date) < Date.parse(data.start_date)) {
      NotificationManager.error('Start date should not greater than End date');
      error = true;
    } else if (Date.parse(start) > Date.parse(end) || Date.parse(start) === Date.parse(end)) {
      NotificationManager.error('End time should be greater than Start time');
      error = true;
    }

    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    //final.year = active.slice(4);
    final.event_calendar = 'course';
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final._event_id = data._event_id;
    final.end_date = endDate;
    final._calendar_id = _calendar_id;
    final.batch = urlTerm;
    final.level_no = levelTitle;
    final._course_id = courseId;
    if (rotation === 'yes') {
      final.rotation_count = rotationCount;
    }

    if (!error) {
      updateCourseEvents(
        final,
        rotation,
        _calendar_id,
        levelTitle,
        courseId,
        content.type.start_date,
        content.type.end_date
      );
    }
  };

  const dataAlign = (state, rotation, rotationCount, dispatchFn) => {
    let data = {};
    let levelTitle = state.title;
    let courseId = state.type._course_id;
    let error = false;
    if (state.type.start_date === '' || state.type.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (courseId === '') {
      NotificationManager.error('Course id is required');
      error = true;
    }

    let events = [];
    if (state.events && state.events.length > 0) {
      events = state.events;
    }

    data._calendar_id = _calendar_id;
    data.level_no = levelTitle;
    data.batch = urlTerm;
    data._course_id = courseId;
    data.color_code = state.type.background_color;
    data._event_id = events.filter((item) => item._event_id).map((item) => item._event_id);
    if (rotation === 'yes') {
      data.rotation_count = state.type.rotation_count;
      data.by = state.custom_dates === 'dates' ? 'date' : 'week';
      data._batch_course_id = [];
      if (state.custom_dates === 'dates') {
        data.start_date = moment(state.type.start_date).format('YYYY-MM-DD');
        data.end_date = moment(state.type.end_date).format('YYYY-MM-DD');
      } else {
        data.start_week = state.academic_week_start;
        data.end_week = state.academic_week_end;
      }
    } else {
      data.start_date = moment(state.type.start_date).format('YYYY-MM-DD');
      data.end_date = moment(state.type.end_date).format('YYYY-MM-DD');
    }
    if (!error) {
      dispatchFn(data, rotation);
      changeTitle('');
    }
  };

  const temp = (rotation, rotationCount) => (
    <ModalWrapper>
      <ModalBackgroundWrapper>
        <h3 className="text-left">
          {nonRotation.modal_mode === 'add'
            ? t('role_management.role_actions.Add Event')
            : t('events.edit_event')}
        </h3>
        <p className="text-left">
          {' '}
          <Trans i18nKey={'select_date_to_sync'}></Trans>{' '}
        </p>
        <AddEvent
          data={nonRotation.modal_content}
          method={setNonRotation}
          min_len={nonRotation.type.start_date}
          max_len={nonRotation.type.end_date}
          levelStartDate={nonRotation.type.start_date}
          levelEndDate={nonRotation.type.end_date}
        />
        <FlexWrapper>
          <Null />
          <PrimaryButton className="light" onClick={() => setNonRotation({ type: 'OFF_MODAL' })}>
            <Trans i18nKey={'cancel'}></Trans>
          </PrimaryButton>
          <PrimaryButton
            className="bordernone"
            onClick={() => {
              if (nonRotation.modal_mode === 'add') {
                dataEventAlign(nonRotation, rotation, rotationCount);
              } else {
                updateEvent(nonRotation, rotation, rotationCount);
              }
            }}
          >
            <Trans i18nKey={'save'}></Trans>
          </PrimaryButton>
        </FlexWrapper>
      </ModalBackgroundWrapper>
    </ModalWrapper>
  );

  let startDate = '';
  let endDate = '';
  let rotation = 'no';
  let rotationCount = 0;
  let level_no = '';
  let filteredEvent = [];
  let currentLevelData = {};
  let levels = [];
  if (
    nonRotation.title !== '' &&
    props[active] !== undefined &&
    props[active]['level'] !== undefined &&
    props[active]['level'].length > 0
  ) {
    levels = props[active]['level'];
    currentLevelData = props[active]['level']
      .filter((list) => list.level_no.toLowerCase() === nonRotation.title.toLowerCase())
      .filter((item) => item.term === urlTerm)
      .reduce((_, el) => {
        return el;
      }, {});

    level_no = currentLevelData.level_no;

    startDate =
      currentLevelData.start_date !== undefined && currentLevelData.start_date !== ''
        ? moment(currentLevelData.start_date).format('YYYY-MM-DD')
        : '';
    endDate =
      currentLevelData.end_date !== undefined && currentLevelData.end_date !== ''
        ? moment(currentLevelData.end_date).format('YYYY-MM-DD')
        : '';
    rotation = currentLevelData.rotation;
    rotationCount = currentLevelData.rotation_count;
    filteredEvent = currentLevelData.events;
  }

  return (
    <div className="main">
      <Loader isLoading={isLoading} />
      <Loader isLoading={loaderState} />
      {/* {apiCalled && loaded && <CheckIds load={loaded} />} */}
      <Fragment>{nonRotation.modal && temp()}</Fragment>
      <Fragment>
        <FlexWrapper>
          <Padding
            className="back"
            onClick={() => {
              changeTitle('');
              history.push(
                `/program-calendar/${match.params.id}/${match.params.year}?year=${urlYear}&programid=${programId}&pname=${urlName}&term=${urlTerm}`
              );
              toggleModal();
              localStorage.removeItem('courseInputData');
            }}
          >
            <i className={`fas ${lang !== 'ar' ? 'fa-arrow-left' : 'fa-arrow-right'}`}></i>
          </Padding>
          <Padding
            className={`${lang !== 'ar' ? '' : 'ar-padding'}`}
            style={{ paddingLeft: '0px', fontSize: '20px' }}
          >
            {titleHeader}
          </Padding>
          <Null />
          {nonRotation.type.start_date && nonRotation.type.end_date && (
            <PrimaryButton
              className={'bordernone'}
              disabled={''}
              onClick={() => {
                if (nonRotation.update_method === 'add') {
                  dataAlign(nonRotation, rotation, rotationCount, courseSave);
                } else {
                  dataAlign(nonRotation, rotation, rotationCount, editCourseSave);
                }
              }}
            >
              {saveButton}
            </PrimaryButton>
          )}
        </FlexWrapper>
        <FlexWrapper mg={lang !== 'ar' ? '0 50px 0px 65px' : '0 65px 0px 50px'}>
          <ChooseLevel
            data={non_rotation}
            load={localLoad}
            programId={programId}
            urlTerm={urlTerm}
          />
          <Null />
          {nonRotation.title && <BackgroundSelect data={non_rotation} />}
        </FlexWrapper>
        {rotation === 'yes' && (
          <CourseAddMode
            data={non_rotation}
            getRotationalCourses={getRotationalCourses}
            NotificationManager={NotificationManager}
            setLoaderState={setLoaderState}
            id={_calendar_id}
            work_level_number={level_no}
            currentLevelData={currentLevelData}
            urlTerm={urlTerm}
          />
        )}
        {nonRotation.title !== '' &&
          (rotation === 'yes' ? (
            nonRotation.type.rotation_count ? (
              <CourseTitle data={non_rotation} programId={programId} />
            ) : null
          ) : (
            <CourseTitle data={non_rotation} programId={programId} />
          ))}
        {/* {nonRotation.title &&
          nonRotation.course_flow === 'rotation' &&
          nonRotation.type.rotation_count !== 0 &&
          nonRotation['batch_courses_id'].length !== 0 &&
          nonRotation['course_add_mode'] !== 'manual' && <CourseGroup data={non_rotation} />} */}
        {nonRotation.type._course_id && nonRotation.type.model && (
          <CourseDuration
            data={non_rotation}
            startDate={startDate}
            endDate={endDate}
            rotation={rotation}
            levels={levels}
            programId={programId}
            urlTerm={urlTerm}
          />
        )}
        {nonRotation.type.start_date && nonRotation.type.end_date && (
          <Fragment>
            {' '}
            <FlexWrapper mg="20px 30px 10px 30px">
              <p>
                {' '}
                <Trans i18nKey={'list_of_events_that_occur'}></Trans>.
              </p>
              <Null />
              <PrimaryButton
                className="light"
                onClick={() => {
                  setNonRotation({ type: 'SHOW_MODAL', payload: 'add' });
                }}
              >
                {' '}
                <i className="fas fa-plus" /> <Trans i18nKey={'program_calendar.events'}></Trans>
              </PrimaryButton>
            </FlexWrapper>
            <EventWrapper of_scroll="auto" className="go-wrapper-width">
              <EventRows show="title" />
              <div className="go-wrapper-height">
                {nonRotation.events &&
                  nonRotation.events
                    .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
                    .map((item, i) => {
                      let editEnable = true;
                      let filterValue = 0;
                      if (filteredEvent && filteredEvent.length > 0) {
                        filterValue = filteredEvent
                          .filter((list) => {
                            return list._event_id === item._event_id;
                          })
                          .reduce((_, el) => {
                            return el._id;
                          }, 0);
                      }

                      if (filterValue !== 0) {
                        editEnable = false;
                      }

                      return (
                        <EventRows
                          show="content"
                          i={i}
                          key={i}
                          content={item}
                          editHide={!editEnable}
                          edit={() => {
                            setNonRotation({
                              type: 'EDIT_EVENT',
                              payload: i,
                              event: item,
                            });
                          }}
                          del={() => eventDeleteConfirm(i, item._event_id)}
                        />
                      );
                    })}
              </div>
            </EventWrapper>{' '}
          </Fragment>
        )}
      </Fragment>
    </div>
  );
};

CourseInput.propTypes = {
  changeTitle: PropTypes.func,
  courseSave: PropTypes.func,
  _calendar_id: PropTypes.string,
  courses: PropTypes.object,
  isLoading: PropTypes.bool,
  toggleModal: PropTypes.func,
  editCourseSave: PropTypes.func,
  saveCourseEvents: PropTypes.func,
  updateCourseEvents: PropTypes.func,
  getData: PropTypes.func,
  apiCalled: PropTypes.bool,
  getRotationalCourses: PropTypes.func,
  listCourseEvents: PropTypes.func,
  course_events: PropTypes.object,
  course_events_copy: PropTypes.object,
  error: PropTypes.object,
  success: PropTypes.object,
  resetMessage: PropTypes.func,
  activeInstitutionCalendar: PropTypes.object,
  changeTerm: PropTypes.func,
};

const mapStateToProps = function (state) {
  // ({ calender }) => ({
  const { calender } = state;
  return {
    apiCalled: calender.commonApiCalled,
    edit: calender.course_editing,
    content: calender.edit_content,
    isLoading: calender.isLoading,
    courses: calender.add_courses,
    batch_courses: calender.rotational_batch_courses_id,
    _calendar_id: calender.program_calender_id,
    year1: calender.year1,
    year2: calender.year2,
    year3: calender.year3,
    year4: calender.year4,
    year5: calender.year5,
    year6: calender.year6,
    course_events: calender.course_events,
    course_events_copy: calender.course_events_copy,
    error: calender.error,
    success: calender.success,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, {
  changeTitle,
  courseSave,
  toggleModal,
  editCourseSave,
  saveCourseEvents,
  updateCourseEvents,
  rotationalSave,
  getCourses,
  getBatchCourses,
  editRotationalSave,
  getData,
  getRotationalCourses,
  listCourseEvents,
  resetMessage,
  changeTerm,
})(CourseInput);
