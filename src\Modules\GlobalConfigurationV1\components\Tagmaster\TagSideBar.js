import React from 'react';
import { useHistory } from 'react-router-dom';

import { List, ListItem, ListItemText } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useTheme } from '@mui/material/styles';

function TagSideBar(props) {
  const theme = useTheme();
  const history = useHistory();
  const useStylesFunction = makeStyles(() => ({
    root: {
      width: '100%',
      height: '50px',
      backgroundColor: theme.palette.background.paper,
      paddingTop: '0px',
      paddingBottom: '0px',
      borderBottom: '1px solid #F3F4F6',
      '&$selected': {
        backgroundColor: '#EFF9FB',
        '&:hover': {
          backgroundColor: '#EFF9FB',
        },
      },
      '&:hover': {
        backgroundColor: '#EFF9FB',
      },
    },
    nested: {
      paddingLeft: theme.spacing(4),
    },
    selected: {},
  }));
  const classes = useStylesFunction();

  return (
    <List component="nav" aria-labelledby="nested-list-subheader" className={classes.root}>
      <ListItem
        selected={true}
        classes={{ root: classes.root, selected: classes.selected }}
        button
        onClick={() => history.push('/globalConfiguration-v1/tagMasters')}
      >
        <ListItemText primary={'Survey'} />
      </ListItem>
    </List>
  );
}

export default TagSideBar;
