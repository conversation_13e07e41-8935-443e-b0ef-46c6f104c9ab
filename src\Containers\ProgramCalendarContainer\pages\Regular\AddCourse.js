import React, { Fragment, useEffect } from 'react';
import { connect, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { NotificationManager } from 'react-notifications';
import CourseInput from '../../AddCourse/Regular/CourseInput';
import { commonApiCall } from '../../../../_reduxapi/actions/calender';
import Loader from '../../../../Widgets/Loader/Loader';

const AddCourse = ({ apiCalled, token, commonApiCall }) => {
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let p_id = params.get('programid');
  let p_name = params.get('pname');
  const authDataArray = useSelector((state) => state?.auth);
  const activeInstitutionCalendar = authDataArray?.activeInstitutionCalendar;
  useEffect(() => {
    if (p_id !== null && p_name !== null && activeInstitutionCalendar.get('_id', '') !== '') {
      commonApiCall(
        token,
        NotificationManager,
        p_id,
        p_name,
        activeInstitutionCalendar.get('_id', '')
      );
    }
  }, [commonApiCall, p_id, p_name, token, activeInstitutionCalendar]);

  return <Fragment>{apiCalled ? <CourseInput /> : <Loader isLoading={!apiCalled} />}</Fragment>;
};

AddCourse.propTypes = {
  apiCalled: PropTypes.bool,
  token: PropTypes.string,
  commonApiCall: PropTypes.func,
};

const mapStateToProps = ({ calender, auth }) => ({
  apiCalled: calender.commonApiCalled,
  token: auth.token !== null ? auth.token.replace(/"/g, '') : null,
});

export default connect(mapStateToProps, {
  commonApiCall,
})(AddCourse);
