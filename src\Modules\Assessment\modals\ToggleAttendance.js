import React from 'react';
import { Modal } from 'react-bootstrap';
import { fromJS, List, Map } from 'immutable';
import PropTypes from 'prop-types';
import ArchiveImg from 'Assets/archive.png';
import MButton from 'Widgets/FormElements/material/Button';

const ToggleAttendanceModal = (props) => {
  const {
    show,
    cancel,
    studentName,
    index,
    data,
    setData,
    toastFunc,
    attendance,
    list,
    setList,
  } = props;
  const qnsArray = (item) => {
    let tempArr = [];
    for (let i = 0; i < data.get('noQuestions', 1); i++) {
      const quest = 'Q' + (i + 1);

      tempArr.push({ mark: item, questionName: quest });
    }
    return fromJS(tempArr);
  };
  const handleAttendance = (e) => {
    if (attendance) {
      setData(
        data
          .setIn(['studentDetails', index, 'studentMarks'], qnsArray(null))
          .setIn(['studentDetails', index, 'attendance'], false)
      );
      setList(
        list
          .setIn(['studentData', index, 'studentMarks'], qnsArray(null))
          .setIn(['studentData', index, 'attendance'], false)
      );
    } else {
      setData(data.setIn(['studentDetails', index, 'attendance'], true));
      setList(list.setIn(['studentData', index, 'attendance'], true));
    }
  };
  const handleSubmit = () => {
    handleAttendance();
    cancel();
    toastFunc(Map({ message: 'Updated Successfully' }));
  };
  return (
    <Modal show={show} onHide={cancel} centered>
      <Modal.Body>
        <div className="d-flex mb-3">
          <img className="mr-2" alt={'Archive'} src={ArchiveImg} />
          <p className="mb-0 f-22 bold"> {`Student Attendance`}</p>
        </div>

        <div className="p-2">
          <p className="mb-0 break-word">
            {`Are you sure you want to mark '${studentName}' as ${
              attendance ? 'Absent' : 'Present'
            }?`}
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <MButton variant="outlined" color="primary" className={'mr-2'} clicked={cancel}>
          No
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleSubmit}>
          Yes
        </MButton>
      </Modal.Footer>
    </Modal>
  );
};
ToggleAttendanceModal.propTypes = {
  show: PropTypes.bool,
  cancel: PropTypes.func,
  attendance: PropTypes.string,
  setData: PropTypes.func,
  toastFunc: PropTypes.func,
  studentName: PropTypes.string,
  index: PropTypes.number,
  data: PropTypes.instanceOf(Map),
  qnsArray: PropTypes.instanceOf(List),
  list: PropTypes.instanceOf(Map),
  setList: PropTypes.func,
};
export default ToggleAttendanceModal;
