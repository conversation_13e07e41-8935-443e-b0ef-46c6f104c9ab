import React from 'react';
import { List, Map } from 'immutable';
import { Link } from 'react-router-dom';
import PropTypes, { oneOfType } from 'prop-types';
import { Button, Accordion, Card, Badge } from 'react-bootstrap';
import CustomAccordionToggle from './CustomAccordionToggle';
import '../../../Assets/css/grouping.css';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { getVersionName, indVerRename, isGenderMerge, levelRename } from 'utils';

function Course({
  activeTerm,
  courseGroup,
  selectedGroups,
  foundationGroup,
  onCourseClick,
  onCourseGroupChange,
  onCourseGroupClick,
  isFoundation,
  activeYear,
  activeProgramIndex,
  activeCalendarIndex,
  setCommonProps,
  activeProgram,
}) {
  return (
    <>
      {activeTerm.get('data', List())?.map((term) => {
        return (
          <div key={term.get('group_name')} className="pt-2">
            <b className="f-21 mb-2 mr-2">
              {levelRename(term.get('level'), activeProgram.get('_id'))}
            </b>

            {(isFoundation || term.get('rotation', 'no') === 'yes') &&
              CheckPermission(
                'tabs',
                'Student Grouping',
                'Dashboard',
                '',
                'Dashboard Master Group',
                'View'
              ) && (
                <b className="f-21 mb-2 pl-3">
                  <Button
                    variant="outline-secondary"
                    className="border-radious-8"
                    onClick={() => foundationGroup(term)}
                    disabled={term.get('courses', List()).isEmpty() ? true : false}
                  >
                    VIEW {isFoundation ? 'FOUNDATION' : 'ROTATION'} GROUPS
                  </Button>
                </b>
              )}

            {term.get('courses', List()).isEmpty() && (
              <div className="placeholder-message course-placeholder-message">
                <h1>
                  <Trans i18nKey={'student_grouping.no_course'} />
                </h1>
                <div>
                  <Trans i18nKey={'student_grouping.unpublished_program'} />
                </div>
              </div>
            )}
            <Accordion className="pt-4">
              {term.get('courses').map((course) => {
                let isElectiveCourse = course?.get('course_type') === 'elective';
                if (term.get('rotation', 'no') === 'yes') {
                  const courseList = term.get('courses');
                  const checkElective = courseList.filter(
                    (item) => item.get('course_type') === 'elective'
                  );
                  isElectiveCourse = checkElective.size > 0;
                }

                let URL = `grouping?pid=${activeProgramIndex}&cid=${activeCalendarIndex}&year=${activeYear}&term=${term.get(
                  'term',
                  'regular'
                )}&level=${term.get(
                  'level',
                  2
                )}&isFoundation=${isFoundation}&isRotation=${term.includes(
                  'yes'
                )}&isElective=${isElectiveCourse}&course_no=${course.get(
                  'course_no'
                )}${getVersionName(course)}&course_id=${course.get('_course_id')}`;

                if (course?.get('course_type') === 'elective') {
                  URL = URL + `&isElect=true`;
                }
                const isGenderMixed = course.get('gender_mixed', false);
                const divClass = isGenderMixed ? 'col-md-3' : 'col-lg-5';
                const genderMerge = isGenderMerge();
                const genderGroups = genderMerge
                  ? ['both']
                  : isGenderMixed
                  ? ['male', 'female', 'both']
                  : ['male', 'female'];
                return (
                  <Card key={course.get('_course_id') + '-' + term.get('level', '')}>
                    <CustomAccordionToggle
                      as={Card.Header}
                      eventKey={course.get('_course_id') + '-' + term.get('level', '')}
                      callback={onCourseClick}
                    >
                      <div className="badge_customize">
                        <p className="f-12 mb-0">{course.get('course_no')}</p>
                        <Link
                          onClick={() => setCommonProps(term)}
                          className="f-14 text-blue"
                          to={URL}
                        >
                          {course.get('course_name')}
                          {getVersionName(course)}
                        </Link>
                      </div>
                    </CustomAccordionToggle>
                    <Accordion.Collapse
                      eventKey={course.get('_course_id') + '-' + term.get('level', '')}
                    >
                      <Card.Body className="bg-white">
                        <div className="border-bottom">
                          <div className="row">
                            <div className="col-md-2">
                              <p className="f-14 mb-2">
                                <Trans i18nKey={'student_grouping.delivery'} />
                              </p>
                            </div>
                            {genderMerge ? (
                              <div className="col-md-10">
                                <p className="f-14 mb-2">Both Groups</p>
                              </div>
                            ) : (
                              <>
                                <div className={divClass}>
                                  <p className="f-14 mb-2">
                                    <Trans i18nKey={'student_grouping.male_groups'} />
                                  </p>
                                </div>
                                <div className={divClass}>
                                  <p className="f-14 mb-2">
                                    <Trans i18nKey={'student_grouping.female_groups'} />
                                  </p>
                                </div>
                                {isGenderMixed && (
                                  <div className="col-md-4">
                                    <p className="f-14 mb-2">Both Groups</p>
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                        <div className="row">
                          {/* <div className="col-md-2">
                            <ul className="demo">
                              <div style={{ height: 25 }}></div>
                              {course.get('session_types').map((sessionType) => (
                                <li key={sessionType.get('_id')}>{sessionType.get('symbol')}</li>
                              ))}
                            </ul>
                          </div> */}
                          <SessionTypes
                            // isRotation={term.get('rotation', '') === 'yes'}
                            // isFoundation={term.get('group_mode', '') === 'foundation'}
                            course={course}
                            courseGroup={courseGroup}
                            activeProgram={activeProgram}
                            level={term.get('level', '')}
                          />
                          {genderGroups.map((gender, index) => (
                            <CourseGroup
                              key={`CourseGroup-${gender}-${index}`}
                              isRotation={term.get('rotation', '') === 'yes'}
                              isFoundation={term.get('group_mode', '') === 'foundation'}
                              course={course}
                              gender={gender}
                              courseGroup={courseGroup}
                              selectedGroups={selectedGroups}
                              onCourseGroupChange={onCourseGroupChange}
                              onCourseGroupClick={onCourseGroupClick}
                              level={term.get('level', '')}
                            />
                          ))}
                        </div>
                      </Card.Body>
                    </Accordion.Collapse>
                  </Card>
                );
              })}
            </Accordion>
          </div>
        );
      })}
    </>
  );
}

function SessionTypes({ course, courseGroup, activeProgram, level }) {
  const courseJoin = course.get('_course_id') + '-' + level;
  const maleData =
    courseGroup.has(courseJoin) &&
    courseGroup.get(courseJoin).getIn(['groups', 'male']) &&
    courseGroup.get(courseJoin).getIn(['groups', 'male', '0', 'delivery_groups']);

  const femaleData =
    courseGroup.has(courseJoin) &&
    courseGroup.get(courseJoin).getIn(['groups', 'female']) &&
    courseGroup.get(courseJoin).getIn(['groups', 'female', '0', 'delivery_groups']);

  let sessionTypeData = List();
  if (maleData !== undefined) {
    sessionTypeData = maleData;
  } else if (femaleData !== undefined) {
    sessionTypeData = femaleData;
  }
  return (
    <div className="col-md-2">
      <ul className="demo">
        <div style={{ height: 25 }}></div>
        {sessionTypeData &&
          sessionTypeData.size > 0 &&
          sessionTypeData
            .sort((a, b) => (a.get('session_type') > b.get('session_type') ? -1 : 1))
            .map((sessionType) => {
              return (
                <li key={sessionType.get('session_type')}>
                  {indVerRename(sessionType.get('session_type'), activeProgram.get('_id', ''))}
                </li>
              );
            })}
        {sessionTypeData &&
          sessionTypeData.size === 0 &&
          course
            .get('session_types')
            .sort((a, b) => (a.get('symbol') > b.get('symbol') ? -1 : 1))
            .map((sessionType) => (
              <li key={sessionType.get('_id')}>{sessionType.get('symbol')}</li>
            ))}
      </ul>
    </div>
  );
}

function CourseGroup({
  isFoundation,
  isRotation,
  course,
  courseGroup,
  gender,
  selectedGroups,
  onCourseGroupChange,
  onCourseGroupClick,
  level,
}) {
  const courseJoin = course.get('_course_id') + '-' + level;
  const isGenderMixed = course.get('gender_mixed', false);
  const divClass =
    gender === 'both' && !isGenderMixed ? `col-md-10` : isGenderMixed ? 'col-md-3' : 'col-lg-5';
  return (
    <div className={divClass}>
      <ul id="menu-grouping">
        {(isFoundation || isRotation) &&
          courseGroup.has(courseJoin) &&
          courseGroup.get(courseJoin).getIn(['groups', gender]) &&
          courseGroup
            .get(courseJoin)
            .getIn(['groups', gender])
            .map((group) => (
              <span
                key={`${course.get('_course_id')}-${gender}-T${group.get('group_name')}`}
                onClick={() =>
                  onCourseGroupChange(course.get('_course_id'), gender, group.get('group_name') - 1)
                }
                className={`tabaligment-blue-small ${
                  selectedGroups.get(course.get('_course_id'), Map({ [gender]: 0 })).get(gender) ===
                  group.get('group_name') - 1
                    ? 'tabactive-blue-small'
                    : ''
                }`}
              >
                Group {group.get('group_name')}
              </span>
            ))}
      </ul>
      {courseGroup.has(courseJoin) &&
        courseGroup.get(courseJoin).getIn(['groups', gender], List()).isEmpty() && (
          <div className="placeholder-message">
            <h1>
              <Trans i18nKey={'student_grouping.no_student_groups'} />
            </h1>
            <div>
              <Trans i18nKey={'student_grouping.no_student_groups_reason'} />
            </div>
          </div>
        )}
      <ul className="demo">
        {courseGroup.has(courseJoin) &&
          courseGroup.get(courseJoin).getIn(['groups', gender]) &&
          courseGroup
            .get(courseJoin)
            .getIn(['groups', gender])
            .slice(
              selectedGroups.get(course.get('_course_id'), Map({ [gender]: 0 })).get(gender),
              selectedGroups.get(course.get('_course_id'), Map({ [gender]: 0 })).get(gender) + 1
            )
            .map((group) =>
              group
                .get('delivery_groups')
                .sort((a, b) => (a.get('session_type') > b.get('session_type') ? -1 : 1))
                .map((deliveryGroups, i) => (
                  <li key={`${course.get('_course_id')}-${gender}-G${i}`}>
                    {deliveryGroups.get('groups').map((deliveryGroup) => (
                      <>
                        <Badge
                          className="remove_hover"
                          variant="light"
                          onClick={() =>
                            onCourseGroupClick(
                              Map({
                                courseId: course.get('_course_id'),
                                gender,
                                deliverySymbol: deliveryGroups.get('session_type'),
                                deliveryGroupNumber: deliveryGroup.slice(1),
                                isFoundation,
                                isRotation,
                                level,
                              })
                            )
                          }
                        >
                          {deliveryGroup}
                        </Badge>{' '}
                      </>
                    ))}
                  </li>
                ))
            )}
      </ul>
    </div>
  );
}

Course.propTypes = {
  activeTerm: PropTypes.instanceOf(Map),
  courseGroup: PropTypes.instanceOf(Map),
  selectedGroups: PropTypes.instanceOf(Map),
  activeYear: oneOfType([PropTypes.string, PropTypes.number]),
  activeProgram: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  foundationGroup: PropTypes.func,
  onCourseClick: PropTypes.func,
  onCourseGroupChange: PropTypes.func,
  onCourseGroupClick: PropTypes.func,
  isFoundation: PropTypes.bool,
  activeProgramIndex: oneOfType([PropTypes.string, PropTypes.number]),
  activeCalendarIndex: oneOfType([PropTypes.string, PropTypes.number]),
  setCommonProps: PropTypes.func,
};

CourseGroup.propTypes = {
  isRotation: PropTypes.bool,
  isFoundation: PropTypes.bool,
  course: PropTypes.instanceOf(Map),
  courseGroup: PropTypes.instanceOf(Map),
  gender: PropTypes.string,
  level: PropTypes.string,
  selectedGroups: PropTypes.instanceOf(Map),
  onCourseGroupChange: PropTypes.func,
  onCourseGroupClick: PropTypes.func,
};

SessionTypes.propTypes = {
  course: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  courseGroup: PropTypes.instanceOf(Map),
  activeProgram: PropTypes.instanceOf(Map),
  level: PropTypes.string,
};

export default Course;
