import React, { useState } from 'react';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CreateNewCalendar from './Models/CreateNewCalendar';
import moment from 'moment';
import { useCalenderQaPc } from 'Modules/QAPC/components/QualityAssuranceProcess/FormList/utils';

export default function AcademicYear() {
  const [expanded, setExpanded] = useState(false);
  const [open, setOpen] = useState(false);
  const handleChange = (type) => {
    setExpanded((prev) => (prev === type ? '' : type));
  };
  const [calenderList] = useCalenderQaPc();
  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <section className="QAPCchild-container  w-30 pb-4"></section>
      <section className="QAPCchild-container">
        <div className="d-flex bgLGrey mx-4 mt-3 py-2" onClick={() => handleChange('running')}>
          {expanded === 'running' ? (
            <ExpandLessIcon className="ml-2 mr-3 f-27" />
          ) : (
            <ExpandMoreIcon className="ml-2 mr-3 f-27" />
          )}
          <div style={{ fontWeight: '500', color: '#374151' }}>Running Academic Year</div>
        </div>
        {expanded === 'running' ? (
          <div className="pt-3 mx-4">
            {calenderList.map((calender, cIndex) => {
              return (
                <div className="bg-white border-bottom px-2 py-3" key={cIndex}>
                  <p className="m-0 f-16 fw-500" style={{ color: '#6B7280' }}>
                    Academic Year {calender.get('calendar_name', '')}
                  </p>
                  <p className="m-0 f-14 fw-400" style={{ color: '#6B7280' }}>
                    Start: {moment(calender.get('start_date', '')).format('Do MMMM')} End:{' '}
                    {moment(calender.get('end_date', '')).format('Do MMMM')}
                  </p>
                </div>
              );
            })}
          </div>
        ) : (
          <></>
        )}

        <CreateNewCalendar open={open} handleClose={handleClose} />
      </section>
    </>
  );
}
