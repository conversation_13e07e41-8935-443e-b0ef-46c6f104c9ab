import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { fromJS } from 'immutable';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { NotificationManager } from 'react-notifications';
import { Button } from 'react-bootstrap';
import { Trans, withTranslation } from 'react-i18next';
import DS_logo from 'Assets/ds_logo.svg';
import SnackBars from 'Modules/Utils/Snackbars';

import config from '_utils/config';
import * as Constants from '../../../constants';
import Loader from 'Widgets/Loader/Loader';
import Input from 'Widgets/FormElements/Input/Input';
import * as actions from '_reduxapi/actions/index';
import Footer from 'Shared/Footer';
import NewPassword from './NewPassword';
import i18n from '../../../i18n';
import { dString } from 'utils';
const emailVal = Constants.EMAIL_VALIDATION;
const Number = Constants.NUMBER_VALIDATION;
const { apiInstance } = config;

const toasterMessage = (msg) => {
  NotificationManager.success(msg);
  // store.addNotification({
  //   content: (
  //     <div className="success_notification">
  //       {msg}
  //       <img alt={msg} src={require('Assets/elipsis.svg')} className="notification-item-img" />{' '}
  //     </div>
  //   ),
  //   container: 'top-right',
  //   animationIn: ['animated', 'fadeIn'],
  //   animationOut: ['animated', 'zoomOut'],
  //   dismiss: {
  //     duration: 2000,
  //   },
  // });
};
class ForgotPassword extends Component {
  constructor() {
    super();
    this.state = {
      emailVerifyTap: true,
      otpVerificationTap: false,
      passwordTap: false,
      emailError: '',
      pswError: '',
      email: '',
      psw: '',
      otptype: 'mobile',
      signInMessage: '',
      otp: '',
      checkBoxMobile: true,
      checkBoxEmail: false,
      signInShow: true,
      minutes: 3,
      seconds: 0,
      sidebarShow: false,
      id: '',
      isLoading: false,
      delayMsg: true,
      show: false,
      password: '',
      confirmpassword: '',
      confirmPswError: '',
      disableotp: true,
    };
    this.forgetEmailRef = React.createRef();
  }

  componentDidMount() {
    this.triggerRef();
  }

  triggerRef = () => {
    if (this.forgetEmailRef.current !== null) {
      this.forgetEmailRef.current.focus();
    }
  };

  componentWillUnmount() {
    clearInterval(this.myInterval);
  }

  timer = () => {
    this.myInterval = setInterval(() => {
      const { seconds, minutes } = this.state;
      if (seconds > 0) {
        this.setState(({ seconds }) => ({
          seconds: seconds - 1,
        }));
      }
      if (seconds === 0) {
        if (minutes === 0) {
          this.setState({ disableotp: false });

          clearInterval(this.myInterval);
        } else {
          this.setState(({ minutes }) => ({
            minutes: minutes - 1,
            seconds: 59,
          }));
        }
      }
    }, 1000);
  };

  signUp = () => {
    this.props.history.push('/signup');
  };
  goBack = () => this.props.history.goBack();

  onChange = (e, name) => {
    e.preventDefault();
    if (name === 'email') {
      this.setState({
        email: e.target.value,
        emailError: '',
      });
    }
    if (name === 'psw') {
      this.setState({
        password: e.target.value,
        pswError: '',
      });
    }
    if (name === 'newPsw') {
      this.setState({
        newPsw: e.target.value,
        newPswError: '',
      });
    }
    if (name === 'confirmPsw') {
      this.setState({
        confirmpassword: e.target.value,
        confirmPswError: '',
      });
    }
    if (name === 'mobile') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mobile: e.target.value,
        mobileError: '',
      });
    }
    if (name === 'otp') {
      if (isNaN(e.target.value)) return;
      this.setState({
        otp: e.target.value,
        otpError: '',
      });
    }
  };

  validation = () => {
    let emailError = '';
    let pswError = '';

    if (!this.state.email) {
      emailError = i18n.t('user_management.email_required');
    } else if (!emailVal.test(this.state.email)) {
      emailError = i18n.t('leaveManagement.errorMsg.validMail');
    }

    // if (!this.state.psw) {
    //   pswError = "Password is Required";
    // } else if (this.state.psw.length <= 7) {
    //   pswError = "Minimum 8 character is required ";
    // }

    if (emailError || pswError) {
      this.setState({
        emailError,
        pswError,
      });
      return false;
    }
    return true;
  };

  passwordvalidation = () => {
    let confirmPswError = '';

    if (!this.state.password) {
      confirmPswError = i18n.t('Auth_words.password_is_required');
    } else if (this.state.password.length <= 7) {
      confirmPswError = i18n.t('Auth_words.min_eignt');
    }
    if (confirmPswError) {
      this.setState({
        confirmPswError,
      });
      return false;
    }
    return true;
  };

  otpValidation = () => {
    let otpError = '';

    if (!this.state.otp) {
      otpError = i18n.t('otp_required');
    } else if (this.state.otp.length <= 3) {
      otpError = i18n.t('Auth_words.min_four');
    } else if (!Number.test(this.state.otp)) {
      otpError = i18n.t('Auth_words.number_only');
    }

    if (otpError) {
      this.setState({
        otpError,
      });
      return false;
    }
    return true;
  };
  handleSubmit = (e) => {
    if (this.passwordvalidation()) {
      const confirmdata = {
        id: this.state.id,
        new_password: this.state.password,
      };
      if (this.state.password !== this.state.confirmpassword) {
        this.setState({
          confirmPswError: i18n.t('Auth_words.password_didnot_match'),
        });
        return false; // The form won't submit
      }
      apiInstance.post(`/user/forget_set_password`, confirmdata).then((res) => {
        if (res.data.status_code === 200) {
          toasterMessage(i18n.t('Auth_words.password_changed_successfully'));
          this.setState({ isLoading: false, delayMsg: true });
          this.props.history.push('/login');
        }
      });
    }
  };
  handleSignIn = (e) => {
    e.preventDefault();
    if (this.validation()) {
      const callBack = () => {
        this.goBack();
        //toasterMessage('Resent Link Send Successfully');
      };
      this.props.onForget({ mailId: this.state.email, callBack });
    }
  };

  handleResendOtp = (e) => {
    e.preventDefault();
    this.setState({ disableotp: true });
    const signIn = {
      email: this.state.email,
      otp_mode: this.state.otptype,
    };
    this.setState({ isLoading: true, minutes: 3, seconds: 0, otp: '', otpError: '' });
    apiInstance
      .post(`/user/forget_send_otp`, signIn)
      .then((res) => {
        if (res.data.status_code === 200) {
          toasterMessage(i18n.t('user_management.OTP_has_Been_Send'));
          this.setState(
            {
              isLoading: false,
              signInMessage: '',
              emailVerifyTap: false,
              otpVerificationTap: true,
            },
            () => {
              this.timer();
            }
          );
        } else {
          this.setState({
            signInMessage: res.data.message,
            isLoading: false,
          });
        }
      })
      .catch((error) => {
        this.setState({
          signInMessage: error.response.data.message,
          isLoading: false,
        });
      });
  };

  handleOtpSubmit = (e) => {
    e.preventDefault();
    if (this.otpValidation()) {
      const mobileNumber = {
        id: this.state.id,
        otp: this.state.otp,
      };
      this.setState({ isLoading: true, delayMsg: false });
      apiInstance.post('/user/otp_verify', mobileNumber).then((res) => {
        if (res.data.status_code === 200) {
          this.setState({ isLoading: false, delayMsg: true, passwordTap: true, show: true });
        } else {
          toasterMessage(i18n.t('user_management.invalid_otp'));
          this.setState({ isLoading: false, delayMsg: true });
        }
      });
    }
  };

  handlecheckMobile = (e) => {
    this.setState(
      {
        checkBoxMobile: true,
        checkBoxEmail: false,
        otptype: 'mobile',
      },
      () => this.triggerRef()
    );
  };

  handlecheckEmail = (e) => {
    this.setState(
      {
        checkBoxEmail: true,
        checkBoxMobile: false,
        otptype: 'email',
      },
      () => this.triggerRef()
    );
  };

  /* componentDidUpdate(prevProps, prevState) {
    if (prevProps.success !== this.props.success && this.props.success != null) {
      store.addNotification({
        content: <div className="success_notification">Login Successfully </div>,
        container: 'top-right',
        animationIn: ['animated', 'fadeIn'],
        animationOut: ['animated', 'zoomOut'],
        dismiss: {
          duration: 2000,
        },
      });

      this.setState({
        isLoading: false,
        signInMessage: '',
        emailVerifyTap: false,
        createPswTap: false,
        createMobileTap: true,
        resendOtp: true,
        signInShow: false,
        sidebarShow: true,
      });

      setTimeout(() => {
        this.setState({ delayMsg: true });
      }, 3000);
    } else if (prevProps.error !== this.props.error && this.props.error != null) {
      store.addNotification({
        content: <div className="success_notification">{this.props.error}</div>,
        container: 'top-right',
        animationIn: ['animated', 'fadeIn'],
        animationOut: ['animated', 'zoomOut'],
        dismiss: {
          duration: 2000,
        },
      });
      this.setState({
        isLoading: false,
      });
    }
  } */

  render() {
    const { signInShow } = this.state;
    const { isLoading, message, history, setData } = this.props;
    let search = window.location.search;
    let decodedData, email, password, expiry, _id, user_type, expiryTime, currentTime;
    if (search) {
      decodedData = dString(search.split('?')[1]);
      const individualData = decodedData.split('&');
      if (individualData[0] && individualData[0].includes('email'))
        email = individualData[0].split('=')[1];
      if (individualData[1] && individualData[1].includes('password'))
        password = individualData[1].split('=')[1];
      if (individualData[2] && individualData[2].includes('expiry')) {
        expiry = individualData[2].split('=')[1];
        expiryTime = new Date(expiry);
        currentTime = new Date();
      }
      if (individualData[3] && individualData[3].includes('id'))
        _id = individualData[3].split('=')[1];
      if (individualData[4] && individualData[4].includes('userType'))
        user_type = individualData[4].split('=')[1];
    }

    if (email && password && expiry) {
      if (currentTime > expiryTime) {
        history.push('/login');
        setData('Forget Password Link Expired');
        return <></>;
      }
      return (
        <NewPassword
          staffVerification={fromJS({ _id, user_type })}
          isChangePassword={true}
          changeDetails={{ email, password, expiry }}
        />
      );
    }
    return (
      <div>
        <Loader isLoading={isLoading} />
        {message !== '' && <SnackBars show={true} message={message} />}
        <div>
          <div className="login-bg">
            {signInShow === true && (
              <div className="container mt-4">
                <div className="">
                  <div className="row justify-content-center">
                    <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                      <h3 className="text-center">
                        {' '}
                        <img src={DS_logo} alt="Digi-scheduler" />
                      </h3>
                    </div>
                  </div>

                  <div className="row justify-content-center pt-3">
                    <div className="col-xl-4 col-lg-5 col-md-7 col-7">
                      <div className="outter-login">
                        <div className="d-flex mt-3 mb-3">
                          <i
                            className="fa fa-arrow-left pt-1 remove_hover"
                            aria-hidden="true"
                            onClick={this.goBack}
                          ></i>
                          <div className="d-flex w-100 justify-content-center bold">
                            <Trans i18nKey={'forgot'}></Trans>
                          </div>
                        </div>
                        <div className="pt-3 pb-3">
                          <Trans i18nKey={'Auth_words.forgot_message'}></Trans>
                        </div>
                        <div className="pt-2 ">
                          <Input
                            elementType={'input'}
                            elementConfig={{
                              type: 'email',
                              placeholder: i18n.t('enter_email'),
                              ref: this.forgetEmailRef,
                            }}
                            value={this.state.email}
                            label={i18n.t('email')}
                            feedback={this.state.emailError}
                            changed={(e) => this.onChange(e, 'email')}
                            className={'form-control'}
                          />
                        </div>
                        <div className="pt-3">
                          <Button
                            disabled={isLoading}
                            variant="primary"
                            size="lg"
                            block
                            onClick={this.handleSignIn}
                            className="f-14"
                          >
                            <Trans i18nKey={'Auth_words.send_reset_link'}></Trans>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="row justify-content-center pt-4 pb-2">
                    <Footer />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
}
ForgotPassword.propTypes = {
  error: PropTypes.string,
  message: PropTypes.string,
  success: PropTypes.bool,
  isLoading: PropTypes.bool,
  history: PropTypes.object,
  onForget: PropTypes.func,
  setData: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    error: state.auth.error,
    success: state.auth.success,
    message: state.userManagementV2.get('message', ''),
    isLoading: state.auth.isLoading,
    isAuthenticated: state.auth.token !== null,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    onAuth: (data) => dispatch(actions.auth(data)),
    onForget: (data) => dispatch(actions.forgetPasswordV2(data)),
    setData: (msg) => dispatch(actions.setData({ message: msg })),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(withTranslation()(ForgotPassword)));
