import React, { Suspense } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Trans } from 'react-i18next';

import { getFormattedCreditHours, getCourseDuration } from '../utils';
import {
  getTranslatedDuration,
  getVersionName,
  isModuleEnabled,
  levelRename,
} from '../../../../utils';
import { t } from 'i18next';

const SyncModal = React.lazy(() => import('../../modal/SyncModal'));

function CourseInfo({
  course,
  setData,
  activeScheduleView,
  studentGroupStatus,
  isAllowedToSchedule,
  currentCalendar,
  programId,
  callback,
}) {
  return (
    <div className="d-flex justify-content-between">
      <div style={{ flexBasis: '75%' }}>
        <h5 className="mb-1 pt-2 text-left">
          {course.get('isShared') && <span className="shared-course-chip mr-2">S</span>}
          <span>
            {`${course.get('courses_number', '')} - ${course.get('courses_name', '')}`}
            {getVersionName(course)}
          </span>
        </h5>
        <div className="row ml-0">
          <div className="pr-2">
            <span className="f-14">
              {`${levelRename(course.get('level_no', ''), programId)} ${course.get('term', '')}`}
            </span>
          </div>
          <div className="pr-2 border-left">
            <span className=" ml-2 f-14">
              {getFormattedCreditHours(course).replace(
                'Credit Hours',
                t('curriculum_keys.credit_hours')
              )}
            </span>
          </div>
          <div className="border-left pr-2">
            <span className=" ml-2 f-14">{getTranslatedDuration(getCourseDuration(course))}</span>
          </div>
        </div>
      </div>
      <div className="mr-3">
        {currentCalendar && isModuleEnabled('SIS_SYNC') && (
          <Suspense fallback="">
            <SyncModal course={course} programId={programId} callBackFn={callback} />
          </Suspense>
        )}
        <div className="row course_setting_tab cursor-pointer">
          <div
            className={`${currentCalendar ? 'border-right-blue' : ''} setting_active p-2`}
            onClick={() => {
              setData(Map({ activeScheduleView: 'list' }));
            }}
          >
            <i className="fa fa-list-alt" aria-hidden="true"></i>
            {activeScheduleView === 'list' && (
              <span className="pl-1 text-uppercase">
                <Trans i18nKey={'list'}></Trans>
              </span>
            )}
          </div>
          {currentCalendar && (
            <div
              className={`p-2 ${
                studentGroupStatus && isAllowedToSchedule
                  ? 'cursor-pointer'
                  : 'cursor-not-allowed opacity-05'
              }`}
              onClick={() => {
                if (studentGroupStatus && isAllowedToSchedule) {
                  setData(Map({ activeScheduleView: 'calendar' }));
                }
              }}
            >
              <i className="fa fa-calendar" aria-hidden="true"></i>
              {activeScheduleView === 'calendar' && (
                <span className="pl-1 text-uppercase">
                  <Trans i18nKey={'calendar'}></Trans>
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

CourseInfo.propTypes = {
  course: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  activeScheduleView: PropTypes.oneOf(['list', 'calendar']),
  studentGroupStatus: PropTypes.bool,
  isAllowedToSchedule: PropTypes.bool,
  currentCalendar: PropTypes.bool,
  programId: PropTypes.string,
  callback: PropTypes.func,
};

export default CourseInfo;
