import React from 'react';
import PropTypes from 'prop-types';

import Typography from '@mui/material/Typography';
import { useStylesFunction } from 'Modules/ProgramInput/v2/piUtil';
import { getShortString } from 'Modules/Shared/v2/Configurations';

function DepartmentName({ departmentName, departmentCount, translatedName }) {
  const classes = useStylesFunction();
  return (
    <Typography className={`${classes.departHeadingFirst} text-left break-word pr-2`}>
      <span>{getShortString(departmentName, 25)}</span>
      <div className="mt-2">
        <Typography className={classes.headingUnder}>
          {' '}
          {departmentCount} <span>{getShortString(translatedName, 40)}</span>
        </Typography>
      </div>
    </Typography>
  );
}

DepartmentName.propTypes = {
  departmentName: PropTypes.string,
  translatedName: PropTypes.string,
  departmentCount: PropTypes.number,
};
export default DepartmentName;
