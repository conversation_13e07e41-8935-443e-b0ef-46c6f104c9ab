import React from 'react';
import PropTypes from 'prop-types';
import { t } from 'i18next';

import sessionTypes from 'Assets/sessionTypes.svg';
import sessionTypesActive from 'Assets/sessionTypesActive.svg';
import courseMasterList from 'Assets/courseMasterList.svg';
import courseMasterListActive from 'Assets/courseMasterListActive.svg';
import Overview from 'Assets/overviewProgram.svg';
import overviewActive from 'Assets/overviewActive.svg';
import IndividualNav from './IndividualNav';
import { getLabelName } from 'Modules/Shared/v2/Configurations';

function OverViewTab({ ovActive }) {
  return (
    <IndividualNav
      sessionActive={ovActive}
      active={overviewActive}
      notActive={Overview}
      title={t('add_colleges.overview')}
      redirection={''}
    />
  );
}

OverViewTab.propTypes = {
  ovActive: PropTypes.string,
};

function SessionTypesTab({ sessionActive }) {
  return (
    <IndividualNav
      sessionActive={sessionActive}
      active={sessionTypesActive}
      notActive={sessionTypes}
      title={t('session_types')}
      redirection={'session-types'}
    />
  );
}

SessionTypesTab.propTypes = {
  sessionActive: PropTypes.string,
};

function CourseMasterTab({ ceActive }) {
  return (
    <IndividualNav
      sessionActive={ceActive}
      active={courseMasterListActive}
      notActive={courseMasterList}
      title={
        <span>
          {getLabelName({ label: 'course', length: 6 })} {t('independentCourse.List')}
        </span>
      }
      redirection={'course-master-list'}
    />
  );
}

CourseMasterTab.propTypes = {
  ceActive: PropTypes.string,
};
export { OverViewTab, SessionTypesTab, CourseMasterTab };
