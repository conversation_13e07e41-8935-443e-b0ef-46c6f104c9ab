import React from 'react';
import { useSelector } from 'react-redux';
import SnackBars from 'Modules/Utils/Snackbars';
import Loader from 'Widgets/Loader/Loader';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
import { Trans } from 'react-i18next';
import { selectIsLoading, selectMessage } from '_reduxapi/studentGroupV2/selectors';
import StudentGroup from './Dashboard';

export default function StudentGroupIndex() {
  const message = useSelector(selectMessage);
  const isLoading = useSelector(selectIsLoading);

  return (
    <div>
      {message && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>
        <Trans i18nKey="student_group" />
      </Breadcrumb>
      <StudentGroup />
    </div>
  );
}
