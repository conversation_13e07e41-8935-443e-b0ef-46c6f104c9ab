import React, { useContext, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { Trans } from 'react-i18next';
import CalenderDetails from './CalenderDetails';
import Courses from './CourseView';
import * as actions from '../../../_reduxapi/program_calendar/action';
import { withRouter } from 'react-router';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

import { PCContext } from '../index';
import { jsUcfirstAll, removeURLParams } from 'utils';
import Loader from 'Widgets/Loader/Loader';

const HomeComponent = (props) => {
  const indexData = useContext(PCContext);
  const {
    programCalendarDashboard,
    isLoading,
    // hasAdminAccess,
    programName,
    activeInstitutionCalendar,
  } = indexData;
  const { history } = props;
  const [loading, setLoading] = useState(false);
  const [count, setCount] = useState(0);

  const urlSwitchRegular = (term, year) => {
    const pathSearch =
      removeURLParams(history?.location, ['term', 'year']) + `&term=${term}&year=${year}`;
    const changedPath = history?.location.pathname;
    history.push(changedPath + pathSearch);
  };

  const printTermTask = (term, year, index) => {
    setTimeout(function () {
      urlSwitchRegular(term, year);
      setTimeout(() => {
        document.getElementById('PrintCalendar').style.padding = '40px 20px 10px 0px';
        document.getElementById('PrintCalendar').style.minWidth = '167em';
        var strightView = document.getElementsByClassName('strightView');
        for (var sView = 0; sView < strightView.length; sView++) {
          strightView[sView].style.fontSize = '1.5em';
        }
        var pdfFont = document.getElementsByClassName('pdfFont');
        for (var font = 0; font < pdfFont.length; font++) {
          pdfFont[font].style.fontSize = '1.3em';
        }
        var printTitle = document.getElementsByClassName('printTitle');
        for (var pTitle = 0; pTitle < printTitle.length; pTitle++) {
          printTitle[pTitle].style.display = 'block';
        }
        var printViewHide = document.getElementsByClassName('printViewHide');
        for (var pVHide = 0; pVHide < printViewHide.length; pVHide++) {
          printViewHide[pVHide].style.display = 'none';
        }
        html2canvas(document.getElementById('PrintCalendar')).then((canvas) => {
          canvas.id = 'contentImage' + index;
          canvas.setAttribute('class', 'contentImage');
          document.body.appendChild(canvas);
          document.getElementById('PrintCalendar').style.padding = '0px 0px 0px 0px';
          document.getElementById('PrintCalendar').style.minWidth = '';
          for (var pTitle = 0; pTitle < printTitle.length; pTitle++) {
            printTitle[pTitle].style.display = 'none';
          }
          for (var pVHide = 0; pVHide < printViewHide.length; pVHide++) {
            printViewHide[pVHide].style.display = 'block';
          }
          for (var sView = 0; sView < strightView.length; sView++) {
            strightView[sView].style.fontSize = '';
          }
          for (var font = 0; font < pdfFont.length; font++) {
            pdfFont[font].style.fontSize = '';
          }
        });
        setCount(index);
      }, 500);
    }, 2000 * index);
  };

  const terms = programCalendarDashboard.get('term', List());
  const years = programCalendarDashboard.get('year_level', List());

  const getTermYears = () => {
    const array = [];
    terms.map((term) => {
      years.map((year) => {
        array.push({
          term_name: term.get('term_name'),
          year_level: year.get('year'),
        });
        return year;
      });
      return term;
    });
    return array;
  };

  const printRegularPDF = () => {
    setLoading(true);
    const termYear = getTermYears();
    if (termYear.length > 0) {
      document.getElementById('printViewHide').style.display = 'block';
      termYear.forEach((year, index) => {
        printTermTask(year.term_name, year.year_level, index + 1);
      });
    }
  };

  useEffect(() => {
    const termYear = getTermYears();
    if (termYear.length > 0) {
      const checkLength = termYear.length;
      if (parseInt(count) === parseInt(checkLength)) {
        setTimeout(() => {
          var contentImage = document.body.querySelectorAll('canvas');
          var imgData = contentImage[0].toDataURL('image/jpeg', 1.0);
          var doc = new jsPDF('p', 'mm', 'a3');
          doc.addImage(imgData, 'jpeg', 10, 10, 280, 360);
          for (var i = 1; i < contentImage.length; i++) {
            var imgData1 = contentImage[i].toDataURL('image/jpeg', 1.0);
            //var imgHeight1 = contentImage[i].height * imgWidth / contentImage[i].width;
            doc.addPage();
            //doc.addImage(imgData1, 'jpeg', 0, 0, imgWidth, imgHeight);
            doc.addImage(imgData1, 'jpeg', 10, 10, 280, 360);
          }
          let pageName =
            programName + ` Academic Year (${activeInstitutionCalendar.get('calendar_name', '')})`;
          doc.save(jsUcfirstAll(pageName) + '.pdf');
          setLoading(false);
          document.getElementById('printViewHide').style.display = 'none';
          urlSwitchRegular(termYear[0].term_name, termYear[0].year_level);
          removeElements(document.querySelectorAll('canvas'));

          setCount(0);
        }, 1500);
      }
    }
  }, [count]); // eslint-disable-line

  const removeElements = (elms) => elms.forEach((el) => el.remove());

  return (
    <React.Fragment>
      <Loader isLoading={loading} />
      <div id="printViewHide"></div>
      {!programCalendarDashboard.isEmpty() ? (
        <>
          <CalenderDetails clickedPDF={printRegularPDF} />
          <div id="PrintCalendar">
            <Courses />
          </div>
        </>
      ) : (
        <>
          <div className="pt-5">
            <div className="col-md-12">
              <div className="notpublished-screen">
                <div className="notpublished">
                  <h2>
                    {' '}
                    {isLoading ? (
                      'Loading...'
                    ) : (
                      <Trans i18nKey={'program_calendar.still_not_been_published'}></Trans>
                    )}
                  </h2>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </React.Fragment>
  );
};

HomeComponent.propTypes = {
  programCalendarDashboard: PropTypes.instanceOf(Map),
  isLoading: PropTypes.bool,
  history: PropTypes.object,
  indexData: PropTypes.object,
  programId: PropTypes.string,
  hasAdminAccess: PropTypes.bool,
  programName: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

export default connect(null, actions)(withRouter(HomeComponent));
