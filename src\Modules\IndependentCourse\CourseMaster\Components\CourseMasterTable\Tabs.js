import React, { useContext, useState, useEffect } from 'react';
import { Tabs, Tab } from '@mui/material';
import i18n from 'i18next';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { independentCourseContext, courseMasterContext } from '../../../context';
import { useStylesFunction } from 'Modules/ProgramInput/v2/piUtil';
import AddCourseModal from '../../Modal/AddCourseModal';

export const ViewCourseTabs = ({ tabValue, setTabValue, tabLength }) => {
  const [active, draft] = tabLength;
  const handleChange = (event, newValue) => {
    setTabValue(newValue);
  };
  return (
    <div className="d-flex border-bottom">
      <Tabs
        value={tabValue}
        indicatorColor="primary"
        textColor="primary"
        onChange={handleChange}
        aria-label="disabled tabs example"
      >
        <Tab
          label={`${i18n.t('constant.added')} (${active})`}
          value={'activeTab'}
          style={{ textTransform: 'Capitalize' }}
        />
        <Tab
          label={`${i18n.t('constant.drafts')} (${draft})`}
          value={'archivedTab'}
          style={{ textTransform: 'Capitalize' }}
        />
      </Tabs>
    </div>
  );
};

ViewCourseTabs.propTypes = {
  tabValue: PropTypes.string,
  setTabValue: PropTypes.func,
  tabLength: PropTypes.array,
};

export const ParentComponent = ({ children }) => (
  <div className="main pb-5">
    <div className="container-fluid">
      <div className="row pb-4">
        <div className="col-md-12 pl-1">{children}</div>
      </div>
    </div>
  </div>
);

ParentComponent.propTypes = {
  children: PropTypes.array,
};

export const Header = ({ onSave, searchData, closeRef }) => {
  const classes = useStylesFunction();
  const { searchValue, setSearchValue } = searchData;
  const { institutionId, getToolTipData, independentOverview } =
    useContext(independentCourseContext);
  const { getIndependentSubjectsList } = useContext(courseMasterContext);
  const [showModal, setShowModal] = useState(false);
  useEffect(() => {
    closeRef.current = onClickClose;
  });
  const onClickClose = () => setShowModal(false);

  if (showModal) return <AddCourseModal show={showModal} onClose={onClickClose} onSave={onSave} />;
  const onClickAdd = () => {
    getIndependentSubjectsList(institutionId);
    setShowModal(true);
  };
  return (
    <div className="row align-items-center pt-3">
      <div className="col-md-6 col-6 col-xl-3 col-lg-3 pr-2 padding-3">
        <MaterialInput
          value={searchValue}
          elementType={'materialSearch'}
          changed={(e) => setSearchValue(e.target.value)}
          placeholder={'Search'}
        />
      </div>
      <div className="col-md-6 col-6 col-xl-9 col-lg-9  d-flex justify-content-around align-items-center">
        <div className="pt_23 pt_23 text-right w-100">
          <div>
            <MButton
              className={classes.courseButton}
              clicked={onClickAdd}
              disabled={
                independentOverview.get('sessionTypesCount', 0) === 0 ||
                independentOverview.get('deliveryTypesCount', 0) === 0
              }
            >
              <Trans i18nKey={'add_new_small'}></Trans>{' '}
              <span className="ml-1">{getToolTipData.course}</span>
            </MButton>
          </div>
        </div>
      </div>
    </div>
  );
};

Header.propTypes = {
  onSave: PropTypes.object,
  searchData: PropTypes.object,
  closeRef: PropTypes.object,
};
