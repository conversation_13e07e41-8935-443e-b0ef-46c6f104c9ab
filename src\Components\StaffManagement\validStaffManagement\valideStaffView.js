import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import axios from '../../../axios';
import Loader from '../../../Widgets/Loader/Loader';
import Input from '../../../Widgets/FormElements/Input/Input';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import { Accordion, Card, Button, Modal } from 'react-bootstrap';
import ProfileText from '../../../Widgets/ProfileText';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import { FlexWrapper, NavButton } from '../../../_components/Styled/styled';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import {
  addDefaultLastName,
  getLang,
  isModuleEnabled,
  lastNameRequired,
  maxDateSet,
} from '../../../utils';
import Tooltips from '../../../_components/UI/Tooltip/Tooltip';
import Select from 'react-select';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { clickArrow } from '../../utils';
import { t } from 'i18next';
import withCountryCodeHooks from 'Hoc/withCountryCodeHooks';
import { connect } from 'react-redux';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import LocalStorageService from 'LocalStorageService';

const gender = [
  ['Male', 'male'],
  ['Female', 'female'],
];

// let pageNum;

class validStaffView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isOpen: false,
      isOpen2: false,
      isOpen3: false,
      isOpen4: false,
      isOpen5: false,
      first: '',
      last: '',
      middle: '',
      family: '',
      empId: '',
      _nationality_id: [],
      _nationality_name: '',
      buildingNo: '',
      nationalId: '',
      city: '',
      distric: '',
      zipCode: '',
      unit: '',
      passport_no: '',
      username: '',
      email: '',
      mobile: '',
      office_extension: '',
      office_room_no: '',
      _nationality_id_doc: '',
      _address_doc: '',
      _appointment_order_doc: '',
      _employee_id_doc: '',

      edit: false,
      selectGender: gender[0][1][2],
      firstError: '',
      middleError: '',
      lastError: '',
      empIdError: '',
      nationalIdError: '',
      selectGenderError: '',
      program_no: '',
      enrollment_year: '',
      dob: '',
      mobileView: false,
      minutes: 3,
      seconds: 0,
      resendOtp: false,
      reason: '',
      otp: '',
      confirmMobile: false,
      uploadedDoc: [],
      updatedFile: '',
      uploadedImageError: '',
      countries: [],
      tabs: 0,
      employment_check: false,
      academic_check: false,
      active: false,
      userStatus: '',
      selectedIndex: '',
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let id = params.get('id');
    let tab = params.get('tab');
    this.setState({ id: id, tabs: tab });
    this.fetchApi(id);
  }

  componentWillUnmount() {
    clearInterval(this.myInterval);
  }

  timer = () => {
    this.myInterval = setInterval(() => {
      const { seconds, minutes } = this.state;
      if (seconds > 0) {
        this.setState(({ seconds }) => ({
          seconds: seconds - 1,
        }));
      }
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(this.myInterval);
        } else {
          this.setState(({ minutes }) => ({
            minutes: minutes - 1,
            seconds: 59,
          }));
        }
      }
    }, 1000);
  };

  callCountry = () => {
    const countries = LocalStorageService.getCustomToken('countries', true);
    if (!countries) {
      axios
        .get(`country?limit=300&pageNo=1`)
        .then((res) => {
          const duplicateAr = res.data.data.filter(
            (v, i, a) => a.findIndex((t) => t.name === v.name) === i
          );
          const countries = duplicateAr.map((data) => {
            return {
              label: data.name,
              value: data.name,
              id: data._id,
            };
          });
          this.setState({
            countries: countries,
            isLoading: false,
          });
          LocalStorageService.setCustomToken('countries', JSON.stringify(countries));
        })
        .catch((error) => {
          this.setState({
            countries: [],
            isLoading: false,
          });
        });
    } else {
      this.setState({
        countries,
        isLoading: false,
      });
    }
  };

  fetchApi = (id) => {
    this.setState({
      isLoading: true,
    });
    const { replaceCountryCode } = this.props;
    this.callCountry();
    axios
      .get(`/user/staff/${id}`)
      .then((res) => {
        const data = res.data.data;
        let country = [];
        if (data.address._nationality_id !== null) {
          country = [
            {
              label: data.address._nationality_id,
              value: data.address._nationality_id,
            },
          ];
        }
        let stateData = {
          first: data.name.first,
          last: data.name.last,
          middle: data.name.middle,
          family: data.name.family,
          empId: data.employee_id,
          nationalId: data.address.nationality_id,
          _nationality_id: country,
          _nationality_name: data.address._nationality_id,
          buildingNo: data.address.building,
          city: data.address.city,
          distric: data.address.district,
          zipCode: data.address.zip_code,
          unit: data.address.unit,
          passport_no: data.address.passport_no,
          program_no: data.program_no,
          enrollment_year: data.enrollment_year,
          email: data.email,
          mobile: data.mobile !== '' ? replaceCountryCode(data.mobile) : '',
          selectGender: data.gender,
          dob: data.dob,
          office_extension: data.office.office_extension,
          office_room_no: data.office.office_room_no,
          _nationality_id_doc: data.address._nationality_id_doc,
          _address_doc: data.address._address_doc,
          _degree_doc:
            data.qualifications.degree.length > 0 ? data.qualifications.degree[0]._degree_doc : '',
          _appointment_order_doc: data.enrollment._appointment_order_doc,
          _employee_id_doc: data.id_doc._employee_id_doc,
          correction_first_name: data.correction_first_name === true ? 'Correction Required' : '',
          correction_middle_name: data.correction_middle_name === true ? 'Correction Required' : '',
          correction_last_name: data.correction_last_name === true ? 'Correction Required' : '',
          correction_gender: data.correction_gender === true ? 'Correction Required' : '',
          correction_employee_id: data.correction_employee_id === true ? 'Correction Required' : '',
          correction_nationality_id:
            data.correction_nationality_id === true ? 'Correction Required' : '',
          isLoading: false,
          data: data,
          uploadedDoc: [
            {
              name: undefined,
            },
            {
              image:
                data.address._nationality_id_doc !== '' ? data.address._nationality_id_doc : '',
              name: <Trans i18nKey={'user_management.national_resident_id'} />,
              isOpen: false,
              imageOpen: false,
              imgName:
                data.address._nationality_id_doc !== ''
                  ? data.address._nationality_id_doc.split('-').pop()
                  : '',
            },
            {
              image:
                data.id_doc._employee_id_doc !== undefined && data.id_doc._employee_id_doc !== ''
                  ? data.id_doc._employee_id_doc
                  : '',
              name: <Trans i18nKey={'employee_id'} />,
              isOpen: false,
              imageOpen: false,
              imgName:
                data.id_doc._employee_id_doc !== undefined && data.id_doc._employee_id_doc !== ''
                  ? data.id_doc._employee_id_doc.split('-').pop()
                  : '',
            },
            {
              image:
                data.address._address_doc !== undefined && data.address._address_doc !== ''
                  ? data.address._address_doc
                  : '',
              name: (
                <Trans i18nKey={'user_management.saudi_postal_address'} values={{ Saudi: '' }} />
              ),
              isOpen: false,
              imageOpen: false,
              imgName:
                data.address._address_doc !== undefined && data.address._address_doc !== ''
                  ? data.address._address_doc.split('-').pop()
                  : '',
            },
            {
              image:
                data.enrollment._appointment_order_doc !== undefined &&
                data.enrollment._appointment_order_doc !== ''
                  ? data.enrollment._appointment_order_doc
                  : '',
              name: <Trans i18nKey={'global_configuration.appointment_order'} />,
              isOpen: false,
              imageOpen: false,
              imgName:
                data.enrollment._appointment_order_doc !== undefined &&
                data.enrollment._appointment_order_doc !== ''
                  ? data.enrollment._appointment_order_doc.split('-').pop()
                  : '',
            },
            {
              image:
                data.qualifications.degree && data.qualifications.degree.length > 0
                  ? data.qualifications.degree[0]._degree_doc
                  : '',
              name: <Trans i18nKey={'degree_name'} />,
              isOpen: false,
              imageOpen: false,
              imgName:
                data.qualifications.degree && data.qualifications.degree.length > 0
                  ? data.qualifications.degree[0]._degree_doc.split('-').pop()
                  : '',
            },
          ],
          userStatus: data.status,
        };
        this.setState(stateData);
        this.checkEmploymentApi(id);
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  checkEmploymentApi = (id) => {
    this.setState({ isLoading: true });
    axios
      .get(`user/staff_profile_details/${id}`)
      .then((res) => {
        let employment_check = false;
        let academic_check = false;
        if (res.data.status_code === 200) {
          employment_check = res.data.data.employment_check;
          academic_check = res.data.data.academic_check;
        }
        this.setState({
          isLoading: false,
          employment_check: employment_check,
          academic_check: academic_check,
        });
      })
      .catch((error) => {
        this.setState({ isLoading: false });
      });
  };

  handleEdit = () => {
    this.setState({
      edit: true,
    });
  };

  handleChangeText = (e, name) => {
    if (name === 'first') {
      this.setState({
        first: e.target.value,
        firstError: '',
      });
    }

    if (name === 'middle') {
      this.setState({
        middle: e.target.value,
        middleError: '',
      });
    }
    if (name === 'last') {
      this.setState({
        last: e.target.value,
        lastError: '',
      });
    }

    if (name === 'empId') {
      this.setState({
        empId: e.target.value,
        empIdError: '',
      });
    }
    if (name === 'nationalId') {
      this.setState({
        nationalId: e.target.value,
        nationalIdError: '',
      });
    }
    if (name === 'family') {
      this.setState({
        family: e.target.value,
        familyError: '',
      });
    }

    if (name === 'academic') {
      this.setState({
        academic: e.target.value,
        academicError: '',
      });
    }

    if (name === 'passport') {
      this.setState({
        passport_no: e.target.value,
        passportError: '',
      });
    }
    if (name === 'program_no') {
      this.setState({
        program_no: e.target.value,
        program_noError: '',
      });
    }

    if (name === 'dob') {
      this.setState({
        dob: e,
        dobError: '',
      });
    }

    if (name === 'buildingNo') {
      this.setState({
        buildingNo: e.target.value,
        buildingNoError: '',
      });
    }
    if (name === 'street') {
      this.setState({
        street: e.target.value,
        streetError: '',
      });
    }
    if (name === 'city') {
      this.setState({
        city: e.target.value,
        cityError: '',
      });
    }
    if (name === 'distric') {
      this.setState({
        distric: e.target.value,
        districError: '',
      });
    }

    if (name === 'zipCode') {
      if (isNaN(e.target.value)) return;
      this.setState({
        zipCode: e.target.value,
        zipCodeError: '',
      });
    }
    if (name === 'unit') {
      if (isNaN(e.target.value)) return;
      this.setState({
        unit: e.target.value,
        unitError: '',
      });
    }
    if (name === 'mobiles') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mobile: e.target.value,
        mobileError: '',
      });
    }

    if (name === 'reason') {
      this.setState({
        reason: e.target.value,
        reasonError: '',
      });
    }
    if (name === 'otp') {
      if (isNaN(e.target.value)) return;
      this.setState({
        otp: e.target.value,
        otpError: '',
      });
    }

    if (name === 'extension') {
      this.setState({
        office_extension: e.target.value,
        extensionError: '',
      });
    }
    if (name === 'room') {
      this.setState({
        office_room_no: e.target.value,
        roomError: '',
      });
    }
  };

  validation = () => {
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    const dotAlpha = /^[a-zA-Z0-9/. -]*$/;
    const AlphaNum = /^[a-zA-Z0-9]+$/;
    const Number = /^[0-9]+$/;
    let firstError = '';
    let middleError = '';
    let lastError = '';
    let nationalIdError = '';
    let selectGenderError = '';
    let empIdError = '';
    let dobError = '';
    let passportError = '';
    let mobileError = '';
    let extensionError = '';
    let roomError = '';
    let buildingNoError = '';
    let cityError = '';
    let districError = '';
    let zipCodeError = '';
    let unitError = '';
    const { countryCodeLength, mobileLengthMatch } = this.props;
    if (this.state.selectGender === 'l') {
      selectGenderError = 'Choose Gender';
    }
    if (!this.state.selectGender) {
      selectGenderError = 'Choose Gender';
    }
    const userDOBValidation = isModuleEnabled('USER_DOB');
    if (userDOBValidation) {
      if (this.state.dob === '' || this.state.dob === null) {
        dobError = 'DOB Field is Required';
      }
    }

    if (!this.state.mobile) {
      mobileError = 'Mobile no is Required';
    } else if (!space.test(this.state.mobile)) {
      mobileError = 'Space not allowed beginning & end';
    } else if (!mobileLengthMatch(this.state.mobile)) {
      mobileError = `Should be ${countryCodeLength} character is required`;
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Number Only Allow';
    }

    if (!this.state.empId) {
      empIdError = 'Employee Id is Required';
    } else if (!space.test(this.state.empId)) {
      empIdError = 'Space not allowed beginning & end';
    } else if (!AlphaNum.test(this.state.empId)) {
      empIdError = 'Special character are not allowed';
    } else if (this.state.empId.length <= 0) {
      empIdError = 'Minimum 1 character is required ';
    }

    if (!this.state.first) {
      firstError = 'First Name is Required';
    } else if (!space.test(this.state.first)) {
      firstError = 'Space not allowed beginning & end';
    }
    // else if (!spaceAlpha.test(this.state.first)) {
    //   firstError = 'Text Only allowed';
    // }
    else if (this.state.first.length <= 2) {
      firstError = 'Minimum 3 character is required ';
    }

    //   if (this.state.middle) {
    //     if (!space.test(this.state.middle)) {
    //     middleError = "Space not allowed beginning & end";
    //   } else if (!spaceAlpha.test(this.state.middle)) {
    //     middleError = "Text Only allowed";
    //   } else if (this.state.middle.length <= 2) {
    //     middleError = "Minimum 3 character is required ";
    //   }
    // }

    if (!lastNameRequired()) {
      if (!this.state.last) {
        lastError = t('user_management.last_name_required');
      } else if (!space.test(this.state.last)) {
        lastError = t('user_management.space_not_allowed');
      }
      // else if (!numbers.test(this.state.lName)) {
      //   lNameError = "Numbers not allowed";
      // }
      // else if (this.state.lName.length <= 2) {
      //   lastError = t('user_management.minimum_3_char_required');
      // }
    }

    if (isModuleEnabled('NATIONALITY_ID')) {
      if (!this.state.nationalId) {
        nationalIdError = 'National/Residence ID is Required';
      } else if (!space.test(this.state.nationalId)) {
        nationalIdError = 'Space not allowed beginning & end';
      } else if (!AlphaNum.test(this.state.nationalId)) {
        nationalIdError = 'Special character are not allowed';
      } else if (this.state.nationalId.length <= 4) {
        nationalIdError = 'Minimum 5 character is required ';
      }
    }
    if (this.state.passport_no) {
      if (!space.test(this.state.passport_no)) {
        passportError = 'Space not allowed beginning & end';
      } else if (!AlphaNum.test(this.state.passport_no)) {
        passportError = 'Special Charcter not Alowed';
      } else if (this.state.passport_no.length <= 2) {
        passportError = 'Minimum 3 character is required ';
      }
    }

    if (this.state.office_room_no) {
      if (!space.test(this.state.office_room_no)) {
        roomError = 'Space not allowed beginning & end';
      } else if (!dotAlpha.test(this.state.office_room_no)) {
        roomError = 'Special character not allowed';
      } else if (this.state.office_room_no.length <= 1) {
        roomError = 'Minimum 2 character is required ';
      }
    }

    if (this.state.office_extension) {
      if (!space.test(this.state.office_extension)) {
        extensionError = 'Space not allowed beginning & end';
      } else if (!Number.test(this.state.office_extension)) {
        extensionError = 'Numeric Only allowed';
      } else if (this.state.office_extension.length <= 1) {
        extensionError = 'Minimum 2 character is required ';
      }
    }

    if (!this.state.buildingNo) {
      buildingNoError = 'Building No, Street Name Field is Required';
    } else if (!space.test(this.state.buildingNo)) {
      buildingNoError = 'Space not allowed beginning & end';
    } else if (this.state.buildingNo.length <= 2) {
      buildingNoError = 'Minimum 3 character is required ';
    }

    if (!this.state.city) {
      cityError = 'City Name Field is Required';
    } else if (!space.test(this.state.city)) {
      cityError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.city)) {
      cityError = 'Text Only allowed';
    } else if (this.state.city.length <= 2) {
      cityError = 'Minimum 3 character is required ';
    }

    if (!this.state.distric) {
      districError = 'District Name Field is Required';
    } else if (!space.test(this.state.distric)) {
      districError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.distric)) {
      districError = 'Text Only allowed';
    } else if (this.state.distric.length <= 2) {
      districError = 'Minimum 3 character is required ';
    }

    if (!this.state.zipCode) {
      zipCodeError = 'Zip Code Field is Required';
    } else if (!space.test(this.state.zipCode)) {
      zipCodeError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.zipCode)) {
      zipCodeError = 'Numeric Only allowed';
    } else if (this.state.zipCode.length <= 2) {
      zipCodeError = 'Minimum 3 character is required ';
    } else if (parseInt(this.state.zipCode) > 1000000) {
      zipCodeError = 'Pls Enter 1000000 Lesser than value ';
    } else if (parseInt(this.state.zipCode) === 0) {
      zipCodeError = 'Pls Enter 0 Greater than value ';
    }

    if (!this.state.unit) {
      unitError = 'Floor Number Field is Required';
    } else if (!space.test(this.state.unit)) {
      unitError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.unit)) {
      unitError = 'Numeric Only allowed';
    } else if (parseInt(this.state.unit) > 50) {
      unitError = 'Pls Enter 50 Lesser than value ';
    } else if (parseInt(this.state.unit) === 0) {
      unitError = 'Pls Enter 0 Greater than value ';
    }

    if (
      firstError ||
      middleError ||
      lastError ||
      nationalIdError ||
      selectGenderError ||
      empIdError ||
      passportError ||
      mobileError ||
      extensionError ||
      roomError ||
      buildingNoError ||
      cityError ||
      districError ||
      zipCodeError ||
      unitError ||
      dobError
    ) {
      this.setState({
        firstError,
        middleError,
        lastError,
        nationalIdError,
        selectGenderError,
        empIdError,
        passportError,
        mobileError,
        roomError,
        extensionError,
        buildingNoError,
        cityError,
        districError,
        zipCodeError,
        unitError,
        dobError,
      });
      return false;
    }
    return true;
  };

  handleSingleSubmit = (e) => {
    e.preventDefault();

    let selectedCountry;

    if (this.state._nationality_id !== null) {
      let countries = this.state.countries.filter((data) => {
        return data.label === this.state._nationality_id.label;
      });

      let defaultCountry = this.state.countries.find(
        (o) => o.label === this.state._nationality_name
      );

      if (countries[0] !== undefined) {
        selectedCountry = countries[0].id;
      } else if (defaultCountry !== undefined) {
        selectedCountry = defaultCountry.id;
      } else {
        selectedCountry = '';
      }
    } else {
      selectedCountry = '';
    }

    this.setState({
      isValidate: false,
    });
    if (this.validation()) {
      let data = {
        id: this.state.id,
        first_name: this.state.first,
        last_name: addDefaultLastName(this.state.last),
        middle_name: this.state.middle,
        gender: this.state.selectGender,
        _nationality_id: selectedCountry,
        ...(this.state.nationalId !== '' && { nationality_id: this.state.nationalId }),
        mobile: this.state.mobile,
        building: this.state.buildingNo,
        city: this.state.city,
        district: this.state.distric,
        zip_code: this.state.zipCode,
        unit: this.state.unit,
        passport_no: this.state.passport_no,
        employee_id: this.state.empId,
        office_room_no: this.state.office_room_no,
        office_extension: this.state.office_extension,
        family_name: this.state.family,
      };

      if (this.state.dob !== '' && this.state.dob !== null) {
        data.dob = moment(this.state.dob).format('YYYY-MM-DD');
      } else {
        data.dob = '';
      }

      // if (this.state.family !== "") {
      //   data.family_name = this.state.family;
      // } else {
      //   data.family_name = "";
      // }

      this.setState({
        isLoading: true,
      });
      axios
        .put(`/user/user_edit`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              edit: false,
            },
            () => {
              this.fetchApi(this.state.id);
            }
          );

          NotificationManager.success(t('user_management.Profile_Edited_Successfully'));
        })
        .catch((error) => {
          let errorMessage = 'Validation Failed';
          if (error.response.data.data !== undefined) {
            errorMessage = error.response.data.data;
            if (typeof errorMessage === 'object') {
              const field = errorMessage.field?.user_id;
              errorMessage = errorMessage.message;
              if (field !== undefined) {
                errorMessage = errorMessage + ' - ' + field;
              }
            }
          }
          NotificationManager.error(`${errorMessage}`);

          this.setState({
            isLoading: false,
          });
        });
    }
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
        selectGenderError: '',
      });
    }
  };

  mobileVerify = () => {
    this.setState({
      mobileView: true,
    });
  };

  mobileVerifyClose = () => {
    this.setState({
      mobileView: false,
      resendOtp: false,
      minutes: 3,
      seconds: 0,
      reason: '',
      otp: '',
    });
  };

  mobileValidation = () => {
    const Number = /^[0-9]+$/;
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    let mobileError = '';
    let reasonError = '';
    const { countryCodeLength, mobileLengthMatch } = this.props;
    if (!this.state.mobile) {
      mobileError = 'Mobile No is Required';
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Mobile No Numbers Only Allow';
    } else if (!mobileLengthMatch(this.state.mobile)) {
      mobileError = `Mobile No ${countryCodeLength} character is required`;
    }

    if (this.state.reason === '') {
      reasonError = 'Reason Field is Required';
    }
    if (!space.test(this.state.reason)) {
      reasonError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.reason)) {
      reasonError = 'Text Only allowed';
    } else if (this.state.reason.length <= 2) {
      reasonError = 'Minimum 3 character is required ';
    }

    if (mobileError || reasonError) {
      this.setState({
        mobileError,
        reasonError,
      });
      return false;
    }
    return true;
  };

  otpValidation = () => {
    let otpError = '';
    const Number = /^[0-9]+$/;

    if (!this.state.otp) {
      otpError = 'OTP is Required';
    } else if (this.state.otp.length <= 3) {
      otpError = 'Minimum 4 character is required ';
    } else if (!Number.test(this.state.otp)) {
      otpError = 'Numbers Only Allow ';
    }

    if (otpError) {
      this.setState({
        otpError,
      });
      return false;
    }
    return true;
  };

  sendOTP = (e) => {
    e.preventDefault();
    const { showWithCountryCode } = this.props;
    if (this.mobileValidation()) {
      const data = {
        id: this.state.id,
        mobile: showWithCountryCode(this.state.mobile),
      };

      this.setState({
        isLoading: true,
        minutes: 3,
        seconds: 0,
      });
      axios
        .post(`/user/send_mobile_otp`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              resendOtp: true,
            },
            () => {
              this.timer();
            }
          );

          NotificationManager.success(t('user_management.OTP_has_Been_Send'));
        })
        .catch((error) => {
          NotificationManager.error(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  handleMobileSubmit = (e) => {
    e.preventDefault();
    if (this.otpValidation()) {
      const { showWithCountryCode } = this.props;
      const data = {
        id: this.state.id,
        mobile: showWithCountryCode(this.state.mobile),
        reason: this.state.reason,
        otp: this.state.otp,
        admin_id: this.state.id,
      };

      this.setState({
        isLoading: true,
      });
      axios
        .post(`/user/change_mobile`, data)
        .then((res) => {
          this.setState({
            isLoading: false,
            resendOtp: false,
            confirmMobile: true,
            mobileView: false,
            otp: '',
            reason: '',
            minutes: 3,
            seconds: 0,
          });

          NotificationManager.success(t('user_management.Phone_Number_Updated_Successfully'));
        })
        .catch((error) => {
          // let errorMessage='';
          NotificationManager.error(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  handleClickOpen = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = true;
    this.setState({
      uploadedDoc,
    });
  };

  handleClickClose = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = false;
    this.setState({
      uploadedDoc,
    });
  };

  imageUpdateOpen = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].imageOpen = true;
    this.setState({
      uploadedDoc,
      uploadedImageError: '',
    });
  };
  imageUpdateClose = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].imageOpen = false;
    this.setState({
      uploadedDoc,
      updatedFile: '',
      uploadedImageError: '',
    });
  };

  handleImageChange(e) {
    e.preventDefault();

    let reader = new FileReader();

    let file = e.target.files[0];

    if (file === null || file === undefined) {
      return;
    }

    const size = Math.ceil(parseFloat(file.size / 1024));
    if (size > 6000) {
      this.setState({
        uploadedImageError: 'image size should not be greater than 6 mb',
      });
      return;
    }

    const mimeType = file.type;
    if (mimeType.substr(0, 5) !== 'image') {
      this.setState({
        uploadedImageError: 'Image files only allowed',
      });
      return;
    }
    if (file !== null && file !== undefined) {
      // NotificationManager.success("Image added");
      reader.onloadend = () => {
        this.setState({
          updatedFile: file,
          uploadedImageError: ' ',
        });
      };
      reader.readAsDataURL(file);
    }
  }

  handleImageUpdate = (e, index) => {
    e.preventDefault();

    let data = new FormData();
    data.append('id', this.state.id);

    if (index === 1) {
      data.append('_nationality_id_doc', this.state.updatedFile);
    }
    if (index === 2) {
      data.append('_employee_id_doc', this.state.updatedFile);
    }
    if (index === 3) {
      data.append('_address_doc', this.state.updatedFile);
    }
    if (index === 4) {
      data.append('_appointment_order_doc', this.state.updatedFile);
    }
    if (index === 5) {
      data.append('_degree_doc', this.state.updatedFile);
    }

    this.setState({
      isLoading: true,
    });

    // return;
    axios
      .post(`user/staff_doc_edit`, data)
      .then((res) => {
        const uploadedDoc = this.state.uploadedDoc;
        uploadedDoc[index].imageOpen = false;
        this.setState(
          {
            isLoading: false,
            updatedFile: '',
            uploadedDoc,
          },
          () => {
            this.fetchApi(this.state.id);
          }
        );

        NotificationManager.success(t('user_management.Image_updated_successfully'));
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  handleActive = () => {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let id = params.get('id');

    this.setState({
      active: false,
      isLoading: true,
    });

    const data = {
      id: id,
      status: this.state.active,
    };

    axios
      .post(`user/active_inactive`, data)
      .then((res) => {
        this.setState({
          isLoading: false,
        });
        this.props.history.push({
          pathname: '/staff/management',
          state: {
            completeView: false,
            pendingView: true,
            inactiveView: false,
          },
        });

        NotificationManager.success(t('user_management.Staff_In_Active_Successfully'));
      })
      .catch((error) => {
        // let errorMessage='';
        NotificationManager.error(`${error.response.data.message}`);
        this.setState({
          isLoading: false,
        });
      });
  };

  handleSelect = (e, name) => {
    e.preventDefault();
    if (name === 'country') {
      this.setState({
        _nationality_id: e.target.value,
      });
    }
  };

  handleGoBack = () => {
    if (this.state.userStatus === 'completed') {
      this.props.history.push({
        pathname: '/staff/management',
      });
    } else {
      this.props.history.push({
        pathname: '/staff/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
          selectedTab: this.state.tabs !== '' ? parseInt(this.state.tabs) : 0,
        },
      });
    }
  };

  handleSelectedNationality = (_nationality_id) => {
    this.setState({
      _nationality_id,
    });
  };

  render() {
    const { minutes, seconds, tabs, selectedIndex } = this.state;
    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';

    const {
      showWithCountryCode,
      mobileLengthMatch,
      countryCodeLength,
      isMobileVerifyMandatory,
      loggedInUserData,
    } = this.props;
    const verifyMobile = isMobileVerifyMandatory();
    const userSensitiveData = isModuleEnabled('USER_SENSITIVE');
    const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
    const documentSecNeed = isModuleEnabled('DOCUMENT_SEC_NEED');
    const userDOBValidation = isModuleEnabled('USER_DOB');
    return (
      <React.Fragment>
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'management_staff_profile'}></Trans>
        </div>
        <NotificationContainer />
        <Loader isLoading={this.state.isLoading} />
        <FlexWrapper className="nav_bg">
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Profile View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Profile View'
            )) && (
            <Link exact to={`/staff/management/profile${this.props.location.search}`}>
              <NavButton
                className={this.props.location.pathname === '/staff/management/profile' && 'active'}
                color="white"
              >
                <Trans i18nKey={'user_management.profile'}></Trans>
              </NavButton>
            </Link>
          )}
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Academic View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Academic View'
            )) && (
            <Link exact to={`/staff/management/academic${this.props.location.search}`}>
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/academic' && 'active'
                }
                color="white"
              >
                <Trans i18nKey={'user_management.academic'}></Trans>
              </NavButton>
            </Link>
          )}
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Employment View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Employment View'
            )) && (
            <Link
              exact
              to={
                this.state.academic_check
                  ? `/staff/management/employment${this.props.location.search}`
                  : `/staff/management/profile${this.props.location.search}`
              }
            >
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/employment' && 'active'
                }
                color="white"
                disabled={!this.state.academic_check}
              >
                <Trans i18nKey={'user_management.employment'}></Trans>
              </NavButton>
            </Link>
          )}
          {loggedInUserData.get('staffFacial', false) &&
            (CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Biometric View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Biometric View'
              )) && (
              <Link
                exact
                to={
                  this.state.employment_check
                    ? `/staff/management/biometric${this.props.location.search}`
                    : `/staff/management/profile${this.props.location.search}`
                }
              >
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/biometric' && 'active'
                  }
                  color="white"
                  disabled={!this.state.employment_check}
                >
                  <Trans i18nKey={'user_management.biometric'}></Trans>
                </NavButton>
              </Link>
            )}
        </FlexWrapper>

        <div className="main pt-2">
          <div className="container">
            {this.state.userStatus === 'completed' && (
              <div className="float-left border border-radious-30">
                {(CheckPermission(
                  'tabs',
                  'User Management',
                  'Staff Management',
                  '',
                  'Registered',
                  'Profile Edit'
                ) ||
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Staff Management',
                    '',
                    'Registration Pending',
                    '',
                    'Invalid',
                    'Profile Edit'
                  ) ||
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Staff Management',
                    '',
                    'Registration Pending',
                    '',
                    'Valid',
                    'Profile Edit'
                  )) && (
                  <div className="pt-2 ">
                    <div className="float-left pr-3 pl-3">
                      <p
                        style={{ marginBottom: '11px' }}
                        className={`${getLang() === 'ar' ? 'digi-mr-16' : ''}`}
                      >
                        <Trans i18nKey={'status_inactive'}></Trans>
                      </p>
                    </div>
                    <div className="float-left pr-3">
                      <label className="switch mb-0">
                        <input
                          checked={this.state.active}
                          type="checkbox"
                          onClick={this.handleActive}
                        />
                        <span className="slider_check round"></span>
                      </label>
                    </div>
                  </div>
                )}
              </div>
            )}
            {(CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Profile Edit'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Profile Edit'
              )) && (
              <>
                {this.state.edit === true ? (
                  <div className="float-right">
                    <Button className="m-2" onClick={this.handleSingleSubmit}>
                      <Trans i18nKey={'save'}></Trans>
                    </Button>
                  </div>
                ) : (
                  <React.Fragment>
                    <div className="float-right">
                      <Button className="m-2" onClick={this.handleEdit}>
                        <Trans i18nKey={'edit'}></Trans>
                      </Button>
                    </div>
                  </React.Fragment>
                )}
              </>
            )}
            <div className="float-right">
              <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                <Trans i18nKey={'back'}></Trans>
              </Button>
            </div>

            <div className="clearfix"></div>

            <div className="white p-4 mb-5">
              <div className="row">
                <div className={documentSecNeed ? 'col-md-5' : 'col-md-6'}>
                  {this.state.edit === true ? (
                    <React.Fragment>
                      {/* edit field start */}
                      <div className="mt-0">
                        <div className="row">
                          <h4 className="bold">
                            {' '}
                            <Trans i18nKey={'edit_personal_details'}></Trans>{' '}
                          </h4>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.first}
                              floatingLabel={<Trans i18nKey={'first_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'first')}
                              feedback={this.state.firstError}
                              correction={this.state.correction_first_name}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.middle}
                              floatingLabel={<Trans i18nKey={'middle_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'middle')}
                              feedback={this.state.middleError}
                              correction={this.state.correction_middle_name}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.last}
                              floatingLabel={!lastNameRequired() ? 'Last Name' : t('last_name')}
                              changed={(e) => this.handleChangeText(e, 'last')}
                              feedback={this.state.lastError}
                              correction={this.state.correction_last_name}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.family}
                              floatingLabel={<Trans i18nKey={'family_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'family')}
                              feedback={this.state.familyError}
                            />
                          </div>

                          <div className="col-md-12">
                            <label
                              style={{
                                fontSize: '14px',
                                color: '#5264AE',
                                paddingLeft: '6px',
                              }}
                            >
                              <Trans i18nKey={'gender'}></Trans>
                            </label>
                            <Input
                              elementType={'radio'}
                              elementConfig={gender}
                              className={'form-radio1'}
                              selected={this.state.selectGender}
                              labelclass="radio-label2"
                              onChange={(e) => this.onRadioGroupChange(e, 'selectGender')}
                              feedback={this.state.selectGenderError}
                              correction={this.state.correction_gender}
                            />
                          </div>
                          {userDOBValidation && (
                            <div className="col-md-12 customeDatePickWrapper">
                              <p className="floatinglabelcustom">
                                <Trans i18nKey={'dob'}></Trans>{' '}
                              </p>
                              <i className="fa fa-clock-o calenderCustom" aria-hidden="true"></i>
                              <DatePicker
                                placeholderText="Date Of Birth"
                                selected={
                                  this.state.dob !== '' && this.state.dob !== null
                                    ? new Date(this.state.dob)
                                    : ''
                                }
                                onChange={(e) => this.handleChangeText(e, 'dob')}
                                value={
                                  this.state.dob !== '' && this.state.dob !== null
                                    ? moment(this.state.dob).format('D MMM YYYY')
                                    : ''
                                }
                                dateFormat="d MMM yyyy"
                                className={'form-control customeDatepick'}
                                showMonthDropdown
                                showYearDropdown
                                maxDate={maxDateSet()}
                                yearDropdownItemNumber={25}
                              />
                              <div className="InvalidFeedback">{this.state.dobError}</div>

                              {/* <Input
                              elementType={"floatinginput"}
                              elementConfig={{
                                type: "date",
                              }}
                              maxLength={25}
                              value={String(this.state.dob).slice(0, 10)}
                              floatingLabel={"Date Of Birth"}
                              changed={(e) => this.handleChangeText(e, "dob")}
                              feedback={this.state.dobError}
                            /> */}
                            </div>
                          )}
                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.empId}
                              floatingLabel={<Trans i18nKey={'employee_id'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'empId')}
                              feedback={this.state.empIdError}
                              correction={this.state.correction_employee_id}
                            />
                          </div>

                          <div className="col-md-12">
                            <p className="floatinglabelcustom">
                              <Trans i18nKey={'nationality_optional'}></Trans>{' '}
                            </p>
                            <Select
                              value={this.state._nationality_id}
                              options={this.state.countries}
                              onChange={this.handleSelectedNationality}
                              isClearable={true}
                            />

                            {/* <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.countries,
                              }}
                              value={this.state._nationality_id}
                              className={'customize_dropdown'}
                              floatingLabel={'Nationalty'}
                              changed={(e) => this.handleSelect(e, 'country')}
                              // feedback={this.state.countryError}
                            /> */}
                          </div>
                          {userSensitiveData && (
                            <>
                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={25}
                                  value={this.state.nationalId}
                                  floatingLabel={
                                    <Trans
                                      i18nKey={'national/residence'}
                                      values={{
                                        optional: !hasNationalIdValidation ? '(Optional)' : '',
                                      }}
                                    ></Trans>
                                  }
                                  changed={(e) => this.handleChangeText(e, 'nationalId')}
                                  feedback={this.state.nationalIdError}
                                  correction={this.state.correction_nationality_id}
                                />
                              </div>
                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={25}
                                  value={this.state.passport_no}
                                  floatingLabel={<Trans i18nKey={'passport_number'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'passport')}
                                  feedback={this.state.passportError}
                                />
                              </div>

                              <h4 className="bold pt-3">
                                <Trans i18nKey={'address_details'}></Trans>
                              </h4>

                              {/* <h3 className="f-16 pt-4 ">Address Details</h3> */}

                              <div className="col-md-12 pt-1">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  value={this.state.buildingNo}
                                  floatingLabel={<Trans i18nKey={'building_no'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'buildingNo')}
                                  feedback={this.state.buildingNoError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={25}
                                  value={this.state.distric}
                                  floatingLabel={<Trans i18nKey={'district_name'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'distric')}
                                  feedback={this.state.districError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={25}
                                  value={this.state.city}
                                  floatingLabel={<Trans i18nKey={'city_name'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'city')}
                                  feedback={this.state.cityError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={10}
                                  value={this.state.zipCode}
                                  floatingLabel={<Trans i18nKey={'zip_code'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'zipCode')}
                                  feedback={this.state.zipCodeError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={2}
                                  value={this.state.unit}
                                  floatingLabel={<Trans i18nKey={'floor_no'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'unit')}
                                  feedback={this.state.unitError}
                                />
                              </div>
                            </>
                          )}
                          <h4 className="bold pt-3">
                            <Trans i18nKey={'contact_details'}></Trans>{' '}
                          </h4>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.office_extension}
                              floatingLabel={<Trans i18nKey={'office_extension'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'extension')}
                              feedback={this.state.extensionError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.office_room_no}
                              floatingLabel={<Trans i18nKey={'office_room'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'room')}
                              feedback={this.state.roomError}
                            />
                          </div>

                          <div className={`col-md-${verifyMobile ? '6' : '12'}`}>
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={countryCodeLength}
                              value={this.state.mobile}
                              floatingLabel={<Trans i18nKey={'mobile_number'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'mobiles')}
                              // disabled={
                              //   this.state.confirmMobile === true
                              //     ? "disabled"
                              //     : ""
                              // }
                              feedback={this.state.mobileError}
                            />
                          </div>

                          {verifyMobile && (
                            <div className="col-md-6">
                              <p
                                className="text-blue remove_hover mb-0 pt-4"
                                onClick={
                                  mobileLengthMatch(this.state.mobile)
                                    ? this.mobileVerify
                                    : () => {}
                                }
                              >
                                {' '}
                                <Trans i18nKey={'change_verify'}></Trans>{' '}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                      {/* edit field end */}
                    </React.Fragment>
                  ) : (
                    <React.Fragment>
                      {/* view field start */}
                      <div className="mt-0">
                        <h4 className="bold">
                          {' '}
                          <Trans i18nKey={'personal'}></Trans>
                        </h4>

                        <ProfileText
                          title={<Trans i18nKey={'first_name'}></Trans>}
                          value={this.state.first}
                          error={this.state.correction_first_name}
                          className="border-bottom pb-1"
                        />
                        <ProfileText
                          title={<Trans i18nKey={'middle_name'}></Trans>}
                          value={this.state.middle ? this.state.middle : 'N/A'}
                          error={this.state.correction_middle_name}
                        />
                        <ProfileText
                          title={!lastNameRequired() ? 'Last Name' : t('last_name')}
                          value={this.state.last ? this.state.last : 'N/A'}
                          error={this.state.correction_last_name}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'family_name'}></Trans>}
                          value={this.state.family ? this.state.family : 'N/A'}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'gender'}></Trans>}
                          value={this.state.selectGender}
                          error={this.state.correction_gender}
                        />
                        {userDOBValidation && (
                          <ProfileText
                            title={<Trans i18nKey={'dob'}></Trans>}
                            value={
                              this.state.dob &&
                              this.state.dob !== undefined &&
                              moment(this.state.dob).format('D MMM YYYY')
                            }
                          />
                        )}
                        <ProfileText
                          title={<Trans i18nKey={'employee_id'}></Trans>}
                          value={this.state.empId}
                          error={this.state.correction_employee_id}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'emailId'}></Trans>}
                          value={this.state.email}
                        />
                        {userSensitiveData && (
                          <ProfileText
                            title={
                              <Trans
                                i18nKey={'national/residence'}
                                values={{ optional: !hasNationalIdValidation ? '(Optional)' : '' }}
                              ></Trans>
                            }
                            value={this.state.nationalId ? this.state.nationalId : 'N/A'}
                            error={this.state.correction_nationality_id}
                          />
                        )}
                        <ProfileText
                          title={<Trans i18nKey={'nationality_optional'}></Trans>}
                          value={
                            this.state._nationality_name ? this.state._nationality_name : 'N/A'
                          }
                        />
                        {userSensitiveData && (
                          <ProfileText
                            title={<Trans i18nKey={'passport_number'}></Trans>}
                            value={this.state.passport_no ? this.state.passport_no : 'N/A'}
                          />
                        )}
                      </div>
                      {userSensitiveData && (
                        <div className="mt-5">
                          <h4 className="bold">
                            {' '}
                            <Trans i18nKey={'address'}></Trans>
                          </h4>
                          <ProfileText
                            title={<Trans i18nKey={'building_no'}></Trans>}
                            value={this.state.buildingNo}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'city_name'}></Trans>}
                            value={this.state.city}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'district_name'}></Trans>}
                            value={this.state.distric}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'zip_code'}></Trans>}
                            value={this.state.zipCode}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'floor_no'}></Trans>}
                            value={this.state.unit}
                          />
                        </div>
                      )}
                      <div className="mt-5">
                        <h4 className="bold">
                          <Trans i18nKey={'contact_details'}></Trans>
                        </h4>

                        <ProfileText
                          title={<Trans i18nKey={'office_extension'}></Trans>}
                          value={this.state.office_extension ? this.state.office_extension : 'N/A'}
                          // error="ssss"
                        />
                        <ProfileText
                          title={<Trans i18nKey={'office_room'}></Trans>}
                          value={this.state.office_room_no ? this.state.office_room_no : 'N/A'}
                          // error="ssss"
                        />
                        <ProfileText
                          title={<Trans i18nKey={'mobile_number'}></Trans>}
                          value={showWithCountryCode(this.state.mobile)}
                          // error="ssss"
                        />
                      </div>
                      {/* view field end */}
                    </React.Fragment>
                  )}
                </div>
                {documentSecNeed && (
                  <div className="col-md-7">
                    <div className="mt-0">
                      <h4 className="bold d-flex">
                        <Trans i18nKey={'uploaded_documents'}></Trans>
                      </h4>
                      <Accordion defaultActiveKey="">
                        {this.state.uploadedDoc.map((data, index) => (
                          <React.Fragment key={index}>
                            {data.name !== undefined && (
                              <Card>
                                <Accordion.Toggle
                                  as={Card.Header}
                                  eventKey={index}
                                  onClick={() => clickArrow.call(this, index)}
                                  className="card-header-icon"
                                >
                                  <div className="float-left">{data.name}</div>
                                  <div className="float-right">
                                    <i
                                      className={`fa fa-chevron-${
                                        selectedIndex !== index ? 'down' : 'up'
                                      } f-14`}
                                    ></i>
                                  </div>
                                </Accordion.Toggle>
                                <Accordion.Collapse eventKey={index}>
                                  <Card.Body className="bg-white">
                                    <div className="float-right">
                                      {(CheckPermission(
                                        'tabs',
                                        'User Management',
                                        'Staff Management',
                                        '',
                                        permissionName,
                                        'Profile Edit'
                                      ) ||
                                        CheckPermission(
                                          'subTabs',
                                          'User Management',
                                          'Staff Management',
                                          '',
                                          'Registration Pending',
                                          '',
                                          permissionName,
                                          'Profile Edit'
                                        )) && (
                                        <Tooltips title={<Trans i18nKey={'edit'}></Trans>}>
                                          <i
                                            className="fa fa-pencil pr-2 remove_hover"
                                            onClick={() => this.imageUpdateOpen(index)}
                                          ></i>
                                        </Tooltips>
                                      )}
                                      <Tooltips title={<Trans i18nKey={'preview'}></Trans>}>
                                        <i
                                          className="fa fa-picture-o remove_hover"
                                          onClick={() => this.handleClickOpen(index)}
                                        ></i>
                                      </Tooltips>
                                    </div>

                                    {data.image !== '' && data.image !== undefined ? (
                                      <img className="w-100" src={data.image} alt="#" />
                                    ) : (
                                      <div className="float-left">
                                        <Trans i18nKey={'no_image'}></Trans>
                                      </div>
                                    )}
                                    {data.isOpen === true && (
                                      <Lightbox
                                        clickOutsideToClose={false}
                                        mainSrc={data.image}
                                        onCloseRequest={() => this.handleClickClose(index)}
                                      />
                                    )}
                                  </Card.Body>
                                </Accordion.Collapse>
                              </Card>
                            )}
                            <Modal
                              show={data.imageOpen}
                              centered
                              size="md"
                              onHide={() => this.imageUpdateClose(index)}
                            >
                              <Modal.Body>
                                <h3 className="bold pt-2">
                                  <Trans i18nKey={'edit_uploaded_file'}></Trans>
                                </h3>
                                <div className="text-wrap-file">
                                  <Trans i18nKey={'current_file'}></Trans> :{' '}
                                  {data.image?.split('/')[5]?.split('-')[1]?.split('?')[0]}
                                </div>
                                <div className="text-wrap-file">
                                  <Trans i18nKey={'changed_file'}></Trans> :{' '}
                                  <span>{this.state.updatedFile.name}</span>
                                </div>
                                <div className="InvalidFeedback">
                                  {this.state.uploadedImageError}
                                </div>
                                <div className="float-right">
                                  <Input
                                    elementType={'imageUpload'}
                                    elementConfig={{
                                      type: 'file',
                                    }}
                                    className="float-right"
                                    imagename={t('browse')}
                                    accept="image/png, image/jpeg"
                                    changed={(e) => this.handleImageChange(e)}
                                  />
                                </div>
                              </Modal.Body>
                              <Modal.Footer>
                                <Button
                                  variant="outline-primary"
                                  onClick={() => this.imageUpdateClose(index)}
                                >
                                  <Trans i18nKey={'close'}></Trans>
                                </Button>

                                <Button
                                  variant="outline-primary"
                                  onClick={(e) => this.handleImageUpdate(e, index)}
                                  className={
                                    this.state.updatedFile === '' ||
                                    this.state.uploadedImageError === ''
                                      ? 'disabled-icon'
                                      : ''
                                  }
                                  disabled={
                                    this.state.updatedFile === '' ||
                                    this.state.uploadedImageError === ''
                                      ? true
                                      : false
                                  }
                                >
                                  <Trans i18nKey={'upload'}></Trans>{' '}
                                </Button>
                              </Modal.Footer>
                            </Modal>
                          </React.Fragment>
                        ))}
                      </Accordion>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* mobile view entry funtion start   */}

        <Modal show={this.state.mobileView} centered size="md" onHide={this.mobileVerify}>
          <Modal.Body>
            <div className=" pt-2">
              <Trans i18nKey={'enter_change'}></Trans>
            </div>

            <div className="">
              <Input
                elementType={'floatinginput'}
                elementConfig={{
                  type: 'text',
                }}
                maxLength={40}
                value={this.state.reason}
                floatingLabel={<Trans i18nKey={'type_reason'}></Trans>}
                changed={(e) => this.handleChangeText(e, 'reason')}
                feedback={this.state.reasonError}
              />
            </div>

            <div className="pt-2">
              <b>
                <Trans i18nKey={'mobile_verify'}></Trans>{' '}
              </b>
            </div>
            <div className="pt-2">
              <b>
                <Trans i18nKey={'txt_mobile'}></Trans>
              </b>
              <br />
              <p className="text-gray pt-3 mb-0">
                {' '}
                <Trans i18nKey={'sent_mobile'}></Trans> {showWithCountryCode(this.state.mobile)}
              </p>
            </div>

            <div className="pt-2">
              <p className="red mb-0">{this.state.mobileError} </p>
            </div>

            <div className=" row pt-2">
              <div className="col-md-6">
                {minutes === 0 && seconds === 0 ? (
                  <Button variant="outline-primary" onClick={this.sendOTP} className="f-14">
                    <Trans i18nKey={'resend_otps'}></Trans>{' '}
                  </Button>
                ) : (
                  <React.Fragment>
                    {this.state.resendOtp === true ? (
                      <Button variant="outline-primary" className="f-14" disabled>
                        <Trans i18nKey={'resend_otps'}></Trans>{' '}
                      </Button>
                    ) : (
                      <Button variant="outline-primary" onClick={this.sendOTP} className="f-14">
                        <Trans i18nKey={'sendotps'}></Trans>{' '}
                      </Button>
                    )}
                  </React.Fragment>
                )}
              </div>

              <div className="col-md-6">
                {this.state.resendOtp === true && (
                  <div className="col-md-12">
                    {minutes === 0 && seconds === 0 ? (
                      ''
                    ) : (
                      <h1 className="f-16 float-right pt-2">
                        <Trans i18nKey={'resend_otp'}></Trans> : {minutes}:
                        {seconds < 10 ? `0${seconds}` : seconds}
                      </h1>
                    )}
                  </div>
                )}
              </div>

              {this.state.resendOtp === true && (
                <div className="col-md-12 pt-2">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={4}
                    value={this.state.otp}
                    floatingLabel={<Trans i18nKey={'enterotp'}></Trans>}
                    changed={(e) => this.handleChangeText(e, 'otp')}
                    feedback={this.state.otpError}
                  />
                </div>
              )}
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline-primary" onClick={(e) => this.mobileVerifyClose(e)}>
              <Trans i18nKey={'close'}></Trans>
            </Button>

            {this.state.resendOtp === true ? (
              <Button
                variant="primary"
                onClick={(e) => this.handleMobileSubmit(e)}
                className="f-14"
              >
                <Trans i18nKey={'submit'}></Trans>
              </Button>
            ) : (
              <Button variant="primary" className="f-14" disabled>
                <Trans i18nKey={'submit'}></Trans>
              </Button>
            )}
          </Modal.Footer>
        </Modal>

        {/* mobile view entry funtion start  */}
      </React.Fragment>
    );
  }
}
validStaffView.propTypes = {
  replaceCountryCode: PropTypes.func,
  showWithCountryCode: PropTypes.func,
  mobileLengthMatch: PropTypes.func,
  countryCodeLength: PropTypes.number,
  isMobileVerifyMandatory: PropTypes.func,
  history: PropTypes.object,
  location: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(withCountryCodeHooks(validStaffView)));
