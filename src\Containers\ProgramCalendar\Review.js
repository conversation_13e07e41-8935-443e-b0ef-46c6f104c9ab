import React, { Component } from 'react';
import { connect } from 'react-redux';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Loader from '../../Widgets/Loader/Loader';
import { <PERSON><PERSON>, Modal, Table, Badge } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import './review.css';
import '../ProgramCalendarContainer/program-table.css';
import axios from '../../axios';
import PropTypes from 'prop-types';
import moment from 'moment';
import ViceDeanResponse from './ViceDeanResponse';
import { PROGRAM_CALENDAR_ID } from '../../constants';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import DatePicker from 'react-datepicker';
import {
  timeFormat,
  formatFullName,
  getEnvCollegeName,
  isMobileVerifyMandatory,
} from '../../utils';
import Search from '../../Widgets/Search/Search';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { Map } from 'immutable';

let academicYear;
let programName;
let selectedArray = [];

const messageItems = [
  { name: 'Email', value: false },
  { name: 'DigiScheduler', value: false },
  { name: 'DigiClass', value: false },
];
class Review extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      searchView: false,
      requestView: false,
      staffData: [],
      reviewerData: [],
      reviewEndDate: '',
      reviewEndTime: '',
      alert: false,
      publishShow: false,
      createrName: '',
      reviewlist: [],
      deanDetail: '',
      notifyVia: messageItems,
      completeAllReview: false,
      completeAllReviewSubmit: false,
      sendReview: false,
      sendapproval: false,
      expireTime: 0,
      id: null,
      review: false,
      skip: false,
      searchText: null,
      creatertimestamp: null,
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let programCalendarId = params.get('id');
    academicYear = params.get('title');
    programName = params.get('name');
    this.setState({
      id: programCalendarId,
      reviewEndDate: moment().format('D MMM YYYY'),
      reviewEndTime: new Date(),
    });
    this.fetchApi(programCalendarId);
    setTimeout(() => {
      this.extendDate();
    }, 1500);
    const notifyVia = isMobileVerifyMandatory()
      ? [{ name: 'SMS', value: false }, ...messageItems]
      : [...messageItems];
    const resetValues = notifyVia.map((item) => {
      return { ...item, value: false };
    });
    this.setState({ notifyVia: resetValues });
  }

  handleSearch = (e) => {
    this.setState(
      {
        searchText: e.target.value,
      },
      () => {
        //this.fetchApi(this.state.id);
        this.getSearchData();
      }
    );
  };

  async fetchApi(programCalendarId) {
    this.setState({
      isLoading: true,
    });

    this.getReviewerLists(programCalendarId);
    this.getAddedReviewLists(programCalendarId);

    this.setState({
      isLoading: false,
    });
  }

  getAddedReviewLists = (programCalendarId) => {
    axios
      .get(`program_calendar_review/list_reviewer/${programCalendarId}`)
      .then((res) => {
        const reviewerData = res.data.data.review.map((data) => {
          return {
            name: formatFullName(data.reviewer_name),
            reviewerId: data._reviewer_id,
            isChecked: false,
          };
        });
        this.setState({
          reviewerData: reviewerData,
          isLoading: false,
        });
      })
      .catch((ex) => {});
  };
  getReviewerLists = (programCalendarId) => {
    axios
      .get(`program_calendar_review/list_review/creator/${programCalendarId}`)
      .then((res) => {
        const reviewlist = res.data.data.review.map((data) => {
          return {
            reviewerName: formatFullName(data.reviewer_name),
            reviews: data.reviews,
            _reviewer_id: data._reviewer_id,
            status: data.status,
            reviewedTime: data.reviewer_timestamp,
            reviewerTimestamp: data.reviewer_timestamp,
            reply: [
              { name: 'agree', value: 'agree' },
              { name: 'disagree', value: 'disagree' },
            ],
            replyRadio: -1,
            reviewerComment: data.reviewer_comment,
            createrComment: data.creater_comment,
            createrFeedback: data.creater_feedback,
            createrReply: '',
            creatertimestamp: data.creater_timestamp,
          };
        });
        let createrName = res.data.data.creater.name;
        let skipped = res.data.data.dean.review === false;
        this.setState({
          createrName: formatFullName(createrName),
          reviewlist: reviewlist,
          deanDetail: res.data.data.dean,
          isLoading: false,
          skip: skipped,
        });
      })
      .catch((ex) => {});
  };

  getSearchData = () => {
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });

    let endpoint = `user/get_all/staff/completed?limit=100&pageNo=1`;
    if (searchText) {
      endpoint = `user/user_search/staff/completed/${searchText}?limit=100&pageNo=1`;
    }

    axios
      .get(endpoint)
      .then((res) => {
        //if (res.data.data !== undefined && res.data.message !== 'Error Unable to find') {
        const staffData =
          res.data.data &&
          res.data.data.length > 0 &&
          res.data.data.map((data) => {
            return {
              employeeId: data.employee_id,
              email: data.email,
              role: data.role,
              name: formatFullName(data.name),
              id: data._id,
              isChecked:
                selectedArray && selectedArray.length > 0 && selectedArray.includes(data._id)
                  ? true
                  : false,
            };
          });

        const loggedInRole =
          this.props.token !== undefined && this.props.token !== ''
            ? this.props.token.replace(/^"(.*)"$/, '$1')
            : '';
        let selectStaffList = [];
        if (this.state.reviewerData && this.state.reviewerData.length > 0) {
          selectStaffList = this.state.reviewerData.map((list) => list.reviewerId);
        }
        let staffFilter =
          staffData &&
          staffData.length > 0 &&
          staffData.filter((data) => {
            return data.id !== loggedInRole && !selectStaffList.includes(data.id);
          });

        this.setState({
          staffData: staffFilter,
          isLoading: false,
        });
        // }
      })
      .catch((ex) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  handleCheckFieldElement = (event, id) => {
    // let data = this.state.staffData.filter(
    //   (item) =>
    //     item.role !== 'Dean' ||
    //     !CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish')
    // );
    // let id = this.state.staffData[index].id;
    // if (event.target.checked) {
    //   selectedArray.push(id);
    // } else {
    //   selectedArray =
    //     selectedArray && selectedArray.length > 0 && selectedArray.filter((e) => e !== id);
    // }
    // data[index].isChecked = event.target.checked;
    // this.setState({ staffData: data });
    let data = [
      ...this.state.staffData.filter(
        (item) =>
          item.role !== 'Dean' ||
          !CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish')
      ),
    ];
    if (event.target.checked) {
      selectedArray.push(id);
    } else {
      selectedArray =
        selectedArray && selectedArray.length > 0 && selectedArray.filter((e) => e !== id);
    }
    const updatedData = data.map((item) => {
      if (item.id === id) {
        return { ...item, isChecked: event.target.checked };
      } else {
        return item;
      }
    });
    this.setState({ staffData: updatedData });
  };

  handleChange = (e, name) => {
    // if (name === "reviewEndTime") {
    //   this.setState({
    //     reviewEndTime: e.target.value,
    //     reviewEndTimeError: "",
    //   });
    // }
  };

  expiryCheck = () => {
    let reviewDate = moment(this.state.reviewEndDate).format('YYYY-MM-DD');
    let reviewTime = timeFormat(moment(this.state.reviewEndTime).format('H:mm:ss'));

    let st = moment(reviewDate + 'T' + reviewTime).toDate();
    var startTime = moment(st);
    var endTime = moment();
    // calculate total duration
    var duration = moment.duration(startTime.diff(endTime));
    var days1 = parseInt(duration.asDays());
    // duration in hours
    var hours1 = parseInt(duration.asHours());
    // duration in minutes
    var minutes1 = parseInt(duration.asMinutes()) % 60;
    let expireTime = 0;
    if (minutes1 > 0) {
      expireTime = `${days1} D ${hours1} H ${minutes1} M`;
    }
    this.setState({ expireTime: expireTime });
  };

  reviewValidation = () => {
    let reviewEndDateError = '';
    let reviewEndTimeError = '';

    if (this.state.reviewEndDate === '') {
      reviewEndDateError = 'Choose End Date';
    }
    if (this.state.reviewEndTime === '') {
      reviewEndTimeError = 'Choose End Time';
    }
    if (reviewEndDateError || reviewEndTimeError) {
      this.setState({
        reviewEndTimeError,
        reviewEndDateError,
      });
      return false;
    }
    return true;
  };

  handleAllCheckBox = (e, name) => {
    let staffData = this.state.staffData;
    if (name === 'staff') {
      staffData.map((staffData) => (staffData.isChecked = e.target.checked));
      this.setState({ staffData: staffData });
    }

    let data = this.state.reviewerData;

    if (name === 'email') {
      data.map((data) => (data.isCheckedEmail = e.target.checked));
      this.setState({ reviewerData: data });
    }
    if (name === 'sms') {
      data.map((data) => (data.isCheckedSms = e.target.checked));
      this.setState({ reviewerData: data });
    }
    if (name === 'scheduler') {
      data.map((data) => (data.isCheckedScheduler = e.target.checked));
      this.setState({ reviewerData: data });
    }

    if (name === 'class') {
      data.map((data) => (data.isCheckedClass = e.target.checked));
      this.setState({ reviewerData: data });
    }
  };

  handleCheck = (event, name) => {
    if (name === 'skip') {
      this.setState({
        skip: event.target.checked,
      });
    }
  };

  handleCheckBox = (event, index, name) => {
    let data = this.state.reviewerData;

    if (name === 'email') {
      data[index].isCheckedEmail = event.target.checked;
      this.setState({ reviewerData: data });
    }
    if (name === 'sms') {
      data[index].isCheckedSms = event.target.checked;
      this.setState({ reviewerData: data });
    }

    if (name === 'scheduler') {
      data[index].isCheckedScheduler = event.target.checked;
      this.setState({ reviewerData: data });
    }

    if (name === 'class') {
      data[index].isCheckedClass = event.target.checked;
      this.setState({ reviewerData: data });
    }
  };

  searchModel = (status) => {
    if (status === true) {
      this.getSearchData();
    }
    this.setState(
      {
        searchView: status,
        searchText: null,
      },
      () => {
        //this.fetchApi(this.state.id);
        selectedArray = [];
      }
    );
  };

  selectStaff = (programCalendarId) => {
    // let chooseStaff = this.state.staffData.filter((staffData) => {
    //   return staffData.isChecked === true;
    // });

    // let selectStaff = chooseStaff.map((id) => {
    //   return id.id;
    // });
    let selectStaff = selectedArray;
    if (selectStaff && selectStaff.length > 0) {
      this.setState({
        isLoading: true,
      });

      const data = {
        _calendar_id: programCalendarId,
        _reviewer_id: selectStaff,
      };

      axios
        .post(`program_calendar_review/add_reviewer`, data)
        .then((res) => {
          if (res.status === 200) {
            NotificationManager.success(`Staff Added Successfully`);
            this.setState(
              {
                searchView: false,
                isLoading: false,
                searchText: null,
              },
              () => {
                this.getReviewerLists(this.state.id);
                this.getAddedReviewLists(this.state.id);
                selectedArray = [];
              }
            );
          } else {
            NotificationManager.error(`${res.data.message}`);
            this.setState({
              isLoading: false,
            });
          }
        })
        .catch((ex) => {});
    } else {
      NotificationManager.error(`Choose atleast one staff`);
    }
  };

  removeReviewer = (data, programCalendarId) => {
    this.setState({
      isLoading: true,
    });

    axios
      .delete(`program_calendar_review/remove_reviewer/${programCalendarId}/${data.reviewerId}`)
      .then((res) => {
        this.setState(
          {
            completeAllReview: false,
          },
          () => {
            this.getReviewerLists(this.state.id);
            this.getAddedReviewLists(this.state.id);
            setTimeout(() => {
              this.setState({ isLoading: false });
            }, 1000);
            selectedArray = [];
          }
        );
        NotificationManager.success(`Staff Delete Successfully`);
      })
      .catch((error) => {
        NotificationManager.error(`${error.response.data.message}`);
        this.setState({
          isLoading: false,
        });
      });
  };

  handleReviewRequest = () => {
    this.setState({
      requestView: !this.state.requestView,
    });
    setTimeout(() => {
      this.setState({ alert: false });
    }, 3000);
  };

  reviewEndDate = (date) => {
    this.setState({
      reviewEndDate: date,
    });
    setTimeout(() => {
      this.expiryCheck();
    }, 1000);
  };

  reviewEndTimes = (date) => {
    this.setState({
      reviewEndTime: date,
    });
    setTimeout(() => {
      this.expiryCheck();
    }, 1000);
  };

  sendReview = (e) => {
    e.preventDefault();
    if (this.reviewValidation()) {
      let emailChecked = this.state.reviewerData.filter((data) => {
        return (
          data.isCheckedEmail === true ||
          data.isCheckedSms === true ||
          data.isCheckedScheduler === true ||
          data.isCheckedClass === true
        );
      });

      if (emailChecked && emailChecked.length > 0) {
        let data = emailChecked.map((id) => {
          return {
            _reviewer_id: id.reviewerId,
            notify_via: [
              id.isCheckedEmail === true ? 'email' : '',
              id.isCheckedSms === true ? 'sms' : '',
              id.isCheckedScheduler === true ? 'digischeduler' : '',
              id.isCheckedClass === true ? 'digiclass' : '',
            ],
          };
        });

        if (data.length === this.state.reviewerData.length) {
          let reviewDate = moment(this.state.reviewEndDate).format('YYYY-MM-DD');
          let reviewTime = timeFormat(moment(this.state.reviewEndTime).format('H:mm:ss'));
          let st = moment(reviewDate + 'T' + reviewTime).toDate();

          const fullData = {
            _calendar_id: this.state.id,
            data,
            expire_date: reviewDate,
            expire_time: st.getTime(),
          };

          this.setState({
            isLoading: true,
          });

          axios
            .post(`program_calendar_review/send_reviewer_notification`, fullData)
            .then((res) => {
              NotificationManager.success(`Reviewer Request Sent Successfully`, '', 2500);
              this.setState(
                {
                  // data: data,
                  requestView: false,
                  isLoading: false,
                  alert: true,
                  reviewerData: [],
                  sendReview: true,
                  review: true,
                  // selectStaffList: [],
                },
                (e) => {
                  this.getReviewerLists(this.state.id);
                  this.getAddedReviewLists(this.state.id);
                  selectedArray = [];
                }
              );
            })
            .catch((error) => {
              NotificationManager.error(`${error.response.data.message}`);

              this.setState({
                isLoading: false,
              });
            });
        } else {
          NotificationManager.error(`Choose the type option for all senders`);
        }
      } else {
        NotificationManager.error(`Choose the type option`);
      }
    }
    setTimeout(() => {
      this.setState({ alert: false });
    }, 3000);
  };

  handleAlert = () => {
    this.setState({
      alert: !this.state.alert,
    });
  };

  handleAllCheckNotify = (event, replyindex) => {
    const notifyVia = this.state.notifyVia;
    notifyVia[replyindex].value = event.target.checked;
    this.setState({ notifyVia: notifyVia });
  };

  publishShow = () => {
    this.rollBackNotify();
    this.setState({
      publishShow: !this.state.publishShow,
    });
  };

  rollBackNotify = () => {
    const { notifyVia } = this.state;
    const updatedValue = notifyVia.map((item) => {
      return { ...item, value: false };
    });
    this.setState({
      notifyVia: updatedValue,
    });
  };

  publishHide = () => {
    this.setState({
      publishShow: false,
    });
  };

  publishSubmit = () => {
    let publishnotify = [];
    this.state.notifyVia
      .filter((item) => item.value === true)
      .map((item) => {
        publishnotify.push(item.name.toLowerCase());
        return item;
      });
    this.setState({ isLoading: true });
    const data = {
      _calendar_id: this.state.id,
      to: 'dean',
      message: `<p>Dear User,<br><br>
      The following Program Calendar has been sent for your review, please review and share your comments.
      <br><br>
      ${programName} | Academic year ${academicYear}
      <br><br>
      Best Regards<br>
      ${getEnvCollegeName()}</p>`,
      notify_via: publishnotify,
    };

    // return;

    axios
      .post(`program_calendar_review/send_notification`, data)
      .then((res) => {
        NotificationManager.success(`Program Calendar Sent Successfully`);
        this.setState(
          {
            isLoading: false,
            publishShow: false,
          },
          () => {
            this.getReviewerLists(this.state.id);
            this.getAddedReviewLists(this.state.id);
            selectedArray = [];
          }
        );
      })
      .catch((ex) => {
        this.setState({ isLoading: false });
      });
  };

  completeAllReviewShow = () => {
    this.rollBackNotify();
    this.setState({
      completeAllReview: !this.state.completeAllReview,
    });
  };

  completeAllReviewHide = () => {
    this.setState({
      completeAllReview: false,
    });
  };

  completeAllReviewSubmit = () => {
    const { loggedInUserData } = this.props;
    let notifydata = [];
    this.setState({ isLoading: true });
    this.state.notifyVia
      .filter((item) => item.value === true)
      .map((item) => {
        notifydata.push(item.name.toLowerCase());
        return item;
      });
    const data = {
      _calendar_id: this.state.id,
      to: 'reviewer',
      message: `<p>Dear User,<br><br>
      ${formatFullName(
        loggedInUserData.get('name', Map()).toJS()
      )} has Completed the Program Calendar Review,<br> Thank you for your valuable comments.
      <br><br>
      Best Regards<br>
      ${getEnvCollegeName()}</p>`,
      notify_via: notifydata,
    };

    axios
      .post(`program_calendar_review/send_notification`, data)
      .then((res) => {
        NotificationManager.success(`All Review completed Successfully`);
        this.setState(
          {
            isLoading: false,
            completeAllReview: false,
            completeAllReviewSubmit: true,
            sendapproval: true,
          },
          () => {
            this.getReviewerLists(this.state.id);
            this.getAddedReviewLists(this.state.id);
            selectedArray = [];
          }
        );
      })
      .catch((ex) => {
        this.setState({ isLoading: false });
      });
  };

  extendDate = () => {
    let oldDate = this.state.reviewEndDate;
    var new_date = moment(oldDate, 'D MMM YYYY').add(1, 'days');
    this.setState({ reviewEndDate: moment(new_date).format('D MMM YYYY') });
    setTimeout(() => {
      this.expiryCheck();
    }, 1000);
  };

  disableCompleteCheck = () => {
    const { notifyVia } = this.state;
    const array = notifyVia.filter((item) => item.value === true).length === 0;
    return array;
  };

  render() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let programCalendarId = params.get('id');
    programCalendarId = programCalendarId !== '' ? programCalendarId : PROGRAM_CALENDAR_ID;
    let text = 'Program Calendar';
    const items = [{ to: '#', label: 'Program Calendar > Review' }];
    return (
      <div>
        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link
                className="breadcrumb-icon"
                style={{ color: '#fff', marginTop: '-68px' }}
                key={index}
                to={to}
              >
                {label}
              </Link>
            ))}
        </Breadcrumb>
        <div className="main pt-3 pb-5 bg-white">
          <Loader isLoading={this.state.isLoading} />
          {/* container start  */}
          <div className="container-fluid">
            {/* row start  */}
            <div className="row mt-1 pb-3">
              <div className="col-md-5 ">
                <div className="float-left">
                  <Link onClick={() => window.history.back()}>
                    <i className="fa fa-arrow-left" aria-hidden="true"></i>
                  </Link>
                  <span className="pl-2 f-18 font-weight-bold"> Review Program Calendar</span>
                  <h3 className=" pl-4 f-14 pt-2">
                    Academic year {academicYear} | {programName} {text}
                  </h3>
                </div>
              </div>

              <div className="col-md-7 pb-2">
                <div className="float-right">
                  <div className="col-md-12 ">
                    <p className="float-left ml-3  mr-2 mt-4">
                      <span className="mr-2">
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => this.handleCheck(event, 'skip')}
                          value={this.state.skip}
                          disabled={
                            this.state.deanDetail.review !== undefined
                              ? !this.state.deanDetail.review
                              : false
                          }

                          // disabled={
                          //   this.state.reviewerData.length === 0 ? true : false
                          // }
                        />
                      </span>
                      Skip Review
                    </p>

                    <p className="mt-3 float-left mr-2">
                      <Button
                        variant={
                          this.state.reviewerData.length === 0 ||
                          this.state.skip === true ||
                          this.state.sendapproval === true
                            ? 'secondary'
                            : 'primary'
                        }
                        disabled={
                          this.state.reviewerData.length === 0 ||
                          this.state.skip === true ||
                          this.state.completeAllReview === true ||
                          this.state.sendapproval === true
                            ? true
                            : false
                        }
                        onClick={this.completeAllReviewShow}
                        block
                      >
                        COMPLETE ALL REVIEW
                      </Button>
                    </p>

                    <p className="mt-3 float-right">
                      {this.state.completeAllReviewSubmit !== true ? (
                        <Button
                          variant={this.state.skip ? 'primary' : 'secondary'}
                          disabled={!this.state.skip}
                          onClick={this.publishShow}
                          block
                        >
                          SEND FOR APPROVAL
                        </Button>
                      ) : (
                        <Button variant="primary" onClick={this.publishShow} block>
                          SEND FOR APPROVAL
                        </Button>
                      )}
                    </p>
                  </div>
                </div>
              </div>

              <div className="col-md-6">
                <h4 className="f-16 pt-4"> Send all years to review </h4>
              </div>

              <div className="col-md-6">
                <div className="float-right">
                  <div className="col-md-12 ">
                    {this.state.reviewerData.length === 0 ||
                    this.state.skip === true ||
                    this.state.sendapproval === true ||
                    this.state.completeAllReview === true ? (
                      <p className="ml-5 mt-2 float-right">
                        <Button variant="light" disabled block>
                          SEND REVIEW REQUEST
                        </Button>
                      </p>
                    ) : (
                      <p className="ml-5 mt-2 float-right">
                        <Button variant="primary" onClick={this.handleReviewRequest}>
                          SEND REVIEW REQUEST
                        </Button>
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="col-md-12">
                <div className="flex-container">
                  <div>
                    <p className="to"> To</p>
                  </div>
                  <div className="search_event">
                    <div className="inline_search">
                      <i
                        className="fa fa-search pr-4 remove_hover"
                        aria-hidden="true"
                        onClick={() => this.searchModel(true)}
                      ></i>
                      {this.state.reviewerData.map((data, index) => (
                        <span className="pr-3" key={index}>
                          <Badge variant="light" className="f-16 search_box_badge">
                            {data.name}
                            <i
                              className="fa fa-times pl-2 remove_hover"
                              aria-hidden="true"
                              onClick={() => this.removeReviewer(data, programCalendarId)}
                            ></i>
                          </Badge>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* row end  */}
          </div>
          {/* container end  */}
          {/* search staff view funtion start   */}
          <Modal
            show={this.state.searchView}
            centered
            dialogClassName="modal-900"
            onHide={() => this.searchModel(false)}
          >
            <Modal.Body>
              <div className="row w-100">
                <div className="col-md-12 pt-1 pb-3">Search Staff &amp; Send</div>
              </div>
              <div className="row pb-2">
                <div className="col-md-12 pl-4">
                  <Search
                    value={this.state.searchText}
                    className="w-100"
                    onChange={(e) => this.handleSearch(e)}
                  />
                </div>
              </div>
              <div className="col-md-12 pt-3">
                <div className="go-wrapper">
                  <table className="table">
                    <thead>
                      <tr>
                        <th className="border_color_blue">
                          <div className="aw-50">
                            {/* <input
                              type="checkbox"
                              className="calendarFormRadio text-checked-gray"
                              onClick={(e) =>
                                this.handleAllCheckBox(e, "staff")
                              }
                              value="checkedall"
                            /> */}
                          </div>
                        </th>
                        <th className="border_color_blue">
                          {' '}
                          <div className="aw-150">Employee Id </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">Name </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">Email </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-100">Role </div>
                        </th>
                      </tr>
                    </thead>

                    <tbody className="go-wrapper-height">
                      {this.state.staffData.length > 0 &&
                        this.state.staffData
                          .filter(
                            (item) =>
                              item.role !== 'Dean' ||
                              !CheckPermission(
                                'pages',
                                'Calendar',
                                'Institution Calendar',
                                'Publish'
                              )
                          )
                          .map((data, index) => {
                            return (
                              <tr
                                key={index}
                                className="tr-change"
                                style={{
                                  background: data.isChecked === true ? '#D1F4FF' : '',
                                }}
                              >
                                <td>
                                  <div className="aw-50">
                                    <input
                                      type="checkbox"
                                      className="calendarFormRadio text-checked-gray"
                                      onClick={(event) =>
                                        this.handleCheckFieldElement(event, data.id)
                                      }
                                      checked={data.isChecked}
                                    />
                                  </div>
                                </td>
                                <td className="text-blue">
                                  {' '}
                                  <div className="aw-150">{data.employeeId}</div>
                                </td>
                                <td className="text-lightgray">
                                  <div className="aw-200">{data.name}</div>
                                </td>
                                <td>
                                  <div className="aw-200">{data.email}</div>
                                </td>
                                <td className="text-lightgray">
                                  <div className="aw-100">{data.role}</div>
                                </td>
                              </tr>
                            );
                          })}
                    </tbody>
                  </table>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer>
              <span
                className="remove_hover btn btn-outline-primary"
                onClick={() => this.searchModel(false)}
              >
                CLOSE
              </span>
              <span
                className="remove_hover btn btn-primary"
                onClick={() => this.selectStaff(programCalendarId)}
              >
                SELECT
              </span>
            </Modal.Footer>
          </Modal>
          {/* search staff view funtion end   */}
          {/* review request view funtion start   */}
          <Modal
            show={this.state.requestView}
            centered
            size="lg"
            onHide={this.handleReviewRequestClose}
          >
            <Modal.Body>
              <div className="row w-100 pb-3">
                <div className="col-md-12 pt-1">Senders list</div>
                <div className="col-md-12 pt-2 text-lightgray">
                  Select the type of notification for each sender{' '}
                </div>
              </div>

              <Table>
                <thead className="th-white">
                  <tr>
                    <th className="border_color_blue">
                      <b>S.No </b>{' '}
                    </th>
                    <th className="border_color_blue">Name</th>

                    <th className="border_color_blue">
                      {' '}
                      <input
                        type="checkbox"
                        className="calendarFormRadio text-checked-gray"
                        onClick={(e) => this.handleAllCheckBox(e, 'email')}
                        value="checkedall"
                      />{' '}
                      Email
                    </th>
                    {isMobileVerifyMandatory() && (
                      <th className="border_color_blue">
                        {' '}
                        <input
                          type="checkbox"
                          className="calendarFormRadio text-checked-gray"
                          onClick={(e) => this.handleAllCheckBox(e, 'sms')}
                          value="checkedall"
                        />{' '}
                        SMS
                      </th>
                    )}

                    <th className="border_color_blue">
                      {' '}
                      <input
                        type="checkbox"
                        className="calendarFormRadio text-checked-gray"
                        onClick={(e) => this.handleAllCheckBox(e, 'scheduler')}
                        value="checkedall"
                      />{' '}
                      DigiScheduler
                    </th>
                    <th className="border_color_blue">
                      {' '}
                      <input
                        type="checkbox"
                        className="calendarFormRadio text-checked-gray"
                        onClick={(e) => this.handleAllCheckBox(e, 'class')}
                        value="checkedall"
                      />{' '}
                      DigiClass
                    </th>
                  </tr>
                </thead>

                {this.state.reviewerData.length === 0 ? (
                  <p className="text-center"> There is Reviewer </p>
                ) : (
                  <tbody>
                    {this.state.reviewerData.map((data, index) => (
                      <tr
                        key={index}
                        className="tr-change"
                        style={{
                          background: data.isCheckedEmail === true ? '#D1F4FF' : '',
                        }}
                      >
                        <td>{index + 1}</td>
                        <td>{data.name}</td>
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio text-checked-gray"
                            onClick={(e) => this.handleCheckBox(e, index, 'email')}
                            value="checkedall"
                            checked={data.isCheckedEmail}
                          />
                        </td>
                        {isMobileVerifyMandatory() && (
                          <td>
                            <input
                              type="checkbox"
                              className="calendarFormRadio text-checked-gray"
                              onClick={(e) => this.handleCheckBox(e, index, 'sms')}
                              value="checkedall"
                              checked={data.isCheckedSms}
                            />
                          </td>
                        )}
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio text-checked-gray"
                            onClick={(event) => this.handleCheckBox(event, index, 'scheduler')}
                            value="checkedall"
                            checked={data.isCheckedScheduler}
                          />
                        </td>
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio text-checked-gray"
                            onClick={(event) => this.handleCheckBox(event, index, 'class')}
                            value="checkedall"
                            checked={data.isCheckedClass}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                )}
              </Table>

              <div className="row pt-4">
                <div className="col-md-4">
                  <p className="pt-5"> Add deadline to submit review </p>
                </div>

                <div className="col-md-3">
                  {/* <Input
                    elementType={"floatinginput"}
                    elementConfig={{
                      type: "date",
                      min: moment().format("YYYY-MM-DD"),
                    }}
                    maxLength={25}
                    value={this.state.reviewEndDate}
                    floatingLabel={"End Date"}
                    changed={(e) => this.handleChange(e, "reviewEndDate")}
                    feedback={this.state.reviewEndDateError}
                  /> */}

                  <p className="mb-2">End Date</p>
                  <i className="fa fa-calendar-check-o calender1" aria-hidden="true"></i>

                  <DatePicker
                    selected={
                      this.state.reviewEndDate !== '' ? new Date(this.state.reviewEndDate) : ''
                    }
                    onChange={this.reviewEndDate}
                    value={this.state.reviewEndDate !== '' ? this.state.reviewEndDate : ''}
                    minDate={new Date()}
                    dateFormat="d MMM yyyy"
                    className={'form-control icon-form-calender'}
                    showMonthDropdown
                    showYearDropdown
                    feedback={this.state.reviewEndDateError}
                  />
                  <div className="InvalidFeedback">{this.state.reviewEndDateError}</div>
                </div>

                <div className="col-md-3">
                  <p className="mb-2"> End Time</p>
                  <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                  <DatePicker
                    selected={this.state.reviewEndTime}
                    onChange={this.reviewEndTimes}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    dateFormat="h:mm aa"
                    className="form-control icon-form"
                    feedback={this.state.reviewEndTimeError}
                  />

                  {/* <Input
                    elementType={"floatinginput"}
                    elementConfig={{
                      type: "time",
                    }}
                    maxLength={25}
                    value={this.state.reviewEndTime}
                    floatingLabel={"End Time"}
                    changed={(e) => this.handleChange(e, "reviewEndTime")}
                    dd={"from-control"}
                    feedback={this.state.reviewEndTimeError}
                  /> */}
                </div>
              </div>

              <div className="row pt-4">
                <div className="col-md-4">
                  <p className="pt-4 ">
                    {' '}
                    <b className="text-lightgray">Time left: {this.state.expireTime} </b>
                  </p>
                </div>

                <div className="col-md-4 pt-3">
                  <Button variant="light button-shadow bg-white" onClick={this.extendDate}>
                    EXTEND 24 H
                  </Button>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <span
                className="remove_hover btn btn-outline-primary"
                onClick={this.handleReviewRequest}
              >
                CLOSE
              </span>
              <span className="remove_hover btn btn-primary" onClick={this.sendReview}>
                SEND
              </span>
            </Modal.Footer>
          </Modal>
          {/* review requestview funtion end */} {/*Alert funtion funtion start   */}
          <Modal show={this.state.alert} centered size="sm" onHide={this.handleAlert}>
            <Modal.Body>
              <div className="row">
                <div className="col-md-12 pb-4 pt-3">
                  <img
                    alt="Invites Sent"
                    className="mx-auto d-block"
                    src={require('../../Assets/alert.png')}
                  />
                </div>
                <div className="col-md-12 pb-2 f-21">
                  <p className="text-center"> Invites Sent</p>
                </div>
              </div>
            </Modal.Body>
          </Modal>
          {/* Alert funtion end */}
          <Modal
            show={this.state.publishShow}
            centered
            onHide={this.publishHide}
            dialogClassName="modal-30w"
            aria-labelledby="example-custom-modal-styling-title"
          >
            <Modal.Body>
              <div className="row justify-content-center ">
                <div className="col-md-12">
                  <p className="f-16">Submit Program Calendar</p>
                  <small>
                    Are you sure you want to submit the program calendar to the Dean for review
                  </small>

                  <p className="pt-4">Notify via</p>
                  {this.state.notifyVia.map((data, notifyIndex) => (
                    <p className="pr-4 float-left" key={notifyIndex}>
                      <input
                        type="checkbox"
                        className="calendarFormRadio"
                        onClick={(e) => this.handleAllCheckNotify(e, notifyIndex)}
                        value="checkedall"
                      />{' '}
                      {data.name}
                    </p>
                  ))}

                  <div>
                    {' '}
                    <p className="f-16 text-red">{this.state.completeError}</p>{' '}
                  </div>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <button className="remove_hover btn btn-outline-primary" onClick={this.publishHide}>
                CLOSE
              </button>
              <button
                className="remove_hover btn btn-primary"
                disabled={this.disableCompleteCheck()}
                onClick={this.publishSubmit}
              >
                SUBMIT
              </button>
            </Modal.Footer>
          </Modal>
          <Modal
            show={this.state.completeAllReview}
            centered
            onHide={this.completeAllReviewHide}
            dialogClassName="modal-30w"
            aria-labelledby="example-custom-modal-styling-title"
          >
            <Modal.Body>
              <div className="row justify-content-center ">
                <div className="col-md-12">
                  <p className="f-16">Complete Review</p>
                  <small>
                    Your responses, if any, will be notified to all the reviewers. Are you sure you
                    want to complete the review of program calendar ?
                  </small>

                  <p className="pt-4">Notify via</p>
                  {this.state.notifyVia.map((data, notifyIndex) => (
                    <p className="pr-4 float-left" key={notifyIndex}>
                      <input
                        type="checkbox"
                        className="calendarFormRadio"
                        onClick={(e) => this.handleAllCheckNotify(e, notifyIndex)}
                        value="checkedall"
                      />{' '}
                      {data.name}
                    </p>
                  ))}

                  <div>
                    {' '}
                    <p className="f-16 text-red">{this.state.completeError}</p>{' '}
                  </div>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <button
                className="remove_hover btn btn-outline-primary"
                onClick={this.completeAllReviewHide}
              >
                CLOSE
              </button>
              <button
                className="remove_hover btn btn-primary"
                disabled={this.disableCompleteCheck()}
                onClick={this.completeAllReviewSubmit}
              >
                SUBMIT
              </button>
            </Modal.Footer>
          </Modal>
          {this.state.id !== null && (
            <ViceDeanResponse
              createrName={this.state.createrName}
              reviewlist={this.state.reviewlist}
              deanDetail={this.state.deanDetail}
              id={this.state.id}
              refresh={() => {
                this.fetchApi(this.state.id);
              }}
              backURL={() => window.history.back()}
            />
          )}
        </div>
      </div>
    );
  }
}

Review.propTypes = {
  token: PropTypes.string,
};

const mapStateToProps = (state) => {
  return {
    isAuthenticated: state.auth.token !== null,
    token: state.auth.token,
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(Review));
