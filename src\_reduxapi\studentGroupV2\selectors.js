import { Map } from 'immutable';

const studentGroupState = (state) => state.studentGroupV2;

const selectIsLoading = (state) => studentGroupState(state).get('isLoading');
const selectMessage = (state) => studentGroupState(state).get('message');
const selectUserPrograms = (state) => studentGroupState(state).get('userPrograms');
const selectProgramYearLevel = (state) => studentGroupState(state).get('programYearLevel');
const selectDeliveryTypes = (state) => studentGroupState(state).get('deliveryTypes');
const selectGroupSettings = (state) => studentGroupState(state).get('groupSettings');
const selectGeneratedGroupName = (state) => studentGroupState(state).get('generatedGroupName');
const selectStudentDetails = (state) => {
  const singleStudentDetails = studentGroupState(state).get('singleStudentDetails');
  return {
    studentDetails: singleStudentDetails.get('data', Map()),
    error: singleStudentDetails.get('error', ''),
  };
};

const selectTotalCompletedSession = (state) =>
  studentGroupState(state).get('totalCompletedSession');
const selectInvalidEntries = (state) => studentGroupState(state).get('invalidEntries');

export {
  selectIsLoading,
  selectMessage,
  selectUserPrograms,
  selectProgramYearLevel,
  selectDeliveryTypes,
  selectGroupSettings,
  selectGeneratedGroupName,
  selectStudentDetails,
  selectTotalCompletedSession,
  selectInvalidEntries,
};
