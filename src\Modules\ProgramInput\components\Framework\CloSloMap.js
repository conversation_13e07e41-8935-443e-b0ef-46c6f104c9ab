import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
//import { Form } from 'react-bootstrap';
import { List, Map } from 'immutable';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { TYPES } from './utils';
//import Tooltip from '../../../../_components/UI/Tooltip/Tooltip';
import Loader from '../../../../Widgets/Loader/Loader';
import * as actions from '../../../../_reduxapi/program_input/action';
import { selectCourse, selectSessionFlow } from '../../../../_reduxapi/program_input/selectors';
import { capitalize, getRandomId } from '../../../InfrastructureManagement/utils';
import { getURLParams, indVerRename } from '../../../../utils';
import { Trans } from 'react-i18next';
import { getLang } from '../../../../utils';
import { t } from 'i18next';
import CloSloMapTable from './CloSloMapTable';

const lang = getLang();
class CloSloMap extends Component {
  constructor(props) {
    super(props);
    this.state = {
      programName: getURLParams('_n', true),
      curriculumId: getURLParams('_curriculum_id', true),
      curriculumName: getURLParams('_curriculum_name', true),
      courseId: getURLParams('_course_id', true),
      courseName: getURLParams('_course_name', true),
      frameworkName: getURLParams('_framework_name', true),
      mappingType: 'alignment',
      loading: false,
      firstLoad: true,
    };
    this.updateData = this.updateData.bind(this);
    this.getMappedValue = this.getMappedValue.bind(this);
    this.handleChange = this.handleChange.bind(this);
  }

  componentDidMount() {
    const { courseId } = this.state;
    this.props.getCourse(courseId);
    this.props.getsessionFlow(courseId);
  }

  handleBack() {
    this.props.history.goBack();
  }

  getCLOAndSLO(type) {
    const { course, sessionFlow } = this.props;
    const domains =
      type === TYPES.CLO
        ? course.getIn(['framework', 'domains'], List())
        : sessionFlow.get('session_flow_data', List());
    return domains.reduce((acc, data) => {
      return acc.concat(
        data.get(type, List()).map((d) => {
          if (type === TYPES.SLO) {
            return d.merge(
              Map({
                _domain_id: data.get('_id'),
                delivery_symbol: data.get('delivery_symbol'),
                delivery_no: data.get('delivery_no'),
              })
            );
          }
          return d.set('_domain_id', data.get('_id'));
        })
      );
    }, List());
  }

  getCLODomainIndexes() {
    const { course } = this.props;
    return course.getIn(['framework', 'domains'], List()).reduce((acc, data, index) => {
      const count = data.get(TYPES.CLO, List()).size;
      return acc.push(index === 0 ? count : acc.get(index - 1) + count);
    }, List());
  }

  getMappedSLOs() {
    const mappedSlos = this.props.course
      .getIn(['framework', 'domains'], List())
      .reduce((acc, data) => {
        return acc.concat(
          data.get(TYPES.CLO, List()).reduce((cloAcc, clo) => {
            const cloId = clo.get('_id');
            return cloAcc.concat(
              clo.get(`${TYPES.SLO}s`, List()).reduce((sloAcc, slo) => {
                return sloAcc.push(slo.set('clo_id', cloId));
              }, List())
            );
          }, List())
        );
      }, List());
    return mappedSlos.reduce(
      (acc, slo) => acc.set(`${slo.get('clo_id')},${slo.get('slo_id')}`, slo),
      Map()
    );
  }

  getMappedValue(cloId, sloId) {
    const mappedValue = this.getMappedSLOs().getIn([`${cloId},${sloId}`, 'mapped_value'], '');
    return ['TRUE', 'true'].includes(mappedValue);
  }

  updateData(data) {
    const { course } = this.props;
    const domainsPath = ['framework', 'domains'];
    const domainIndex = course
      .getIn(domainsPath, List())
      .findIndex((domain) => domain.get('_id') === data.domain_id);
    if (domainIndex === -1) return;
    const cloPath = [...domainsPath, domainIndex, 'clo'];
    const cloIndex = course
      .getIn(cloPath, List())
      .findIndex((clo) => clo.get('_id') === data.clo_id);
    if (cloIndex === -1) return;
    const slosPath = [...cloPath, cloIndex, 'slos'];
    const slos = course.getIn(slosPath, List());
    if (!data.slo_id) {
      const updatedCourse = course.setIn(
        slosPath,
        slos.push(Map(data.slo).set('_id', getRandomId()))
      );
      this.props.setData(
        Map({
          course: updatedCourse,
          message: `${
            ['', 'FALSE'].includes(data.slo.mapped_value)
              ? t('program_input.response_strings.unmapped_successfully')
              : t('program_input.response_strings.mapped_successfully')
          }`,
        })
      );
      return;
    }
    const sloIndex = slos.findIndex((slo) => slo.get('_id') === data.slo_id);
    if (sloIndex === -1) return;
    const updatedCourse = course.setIn(
      [...slosPath, sloIndex, 'mapped_value'],
      data.slo.mapped_value
    );
    this.props.setData(
      Map({
        course: updatedCourse,
        message: `${
          ['', 'FALSE'].includes(data.slo.mapped_value)
            ? t('program_input.response_strings.unmapped_successfully')
            : t('program_input.response_strings.mapped_successfully')
        } `,
      })
    );
  }

  handleChange(checked, clo, slo) {
    this.setState({ loading: true });
    const { UpdateMapCLOPLO, course } = this.props;
    const endpoint = `digi_mapping/update_matrix_slo_clo`;
    const mappedSloData = this.getMappedSLOs().get(`${clo.get('_id')},${slo.get('_id')}`, Map());

    const requestBody = {
      course_id: course.get('_id'),
      domain_id: clo.get('_domain_id'),
      clo_id: clo.get('_id'),
      ...(!mappedSloData.isEmpty() && { slo_id: mappedSloData.get('_id') }),
      slo: {
        slo_id: slo.get('_id'),
        delivery_type_id: slo.get('_domain_id'),
        delivery_symbol: slo.get('delivery_symbol'),
        delivery_no: slo.get('delivery_no'),
        no: slo.get('no'),
        name: slo.get('name'),
        mapped_value: checked ? 'TRUE' : 'FALSE',
      },
    };
    this.setState({ loading: false });
    UpdateMapCLOPLO(endpoint, requestBody, () =>
      this.props.setData(
        Map({
          message: `${!checked ? 'Unmapped' : 'Mapped'} Successfully`,
        })
      )
    ); //this.updateData
  }

  render() {
    const {
      programName,
      frameworkName,
      curriculumName,
      courseName,
      mappingType,
      loading,
      firstLoad,
    } = this.state;

    const { course } = this.props;
    const CLO = this.getCLOAndSLO(TYPES.CLO);
    const SLO = this.getCLOAndSLO(TYPES.SLO);
    const cloDomainIndexes = this.getCLODomainIndexes();
    const programId = course.get('_program_id', '');
    const sloLabel = indVerRename('SLO', programId);

    let clickable = false;
    if (
      CheckPermission('pages', 'Program Input', 'Programs', 'Add Program') ||
      CheckPermission('pages', 'Program Input', 'Programs', 'Add Pre-requisite')
    ) {
      clickable = true;
    }

    return (
      <React.Fragment>
        <Loader isLoading={loading} />
        <div className="main bg-gray pb-5">
          <div className="bg-white pt-3 pb-3">
            <div className="container-fluid">
              <p className="font-weight-bold mb-0 text-left f-17">
                <i
                  className="fa fa-arrow-left pr-3 cursor-pointer"
                  aria-hidden="true"
                  onClick={() => this.handleBack()}
                ></i>
                <Trans i18nKey={'map'}></Trans>{' '}
                <Trans
                  i18nKey={'clo_slo'}
                  values={{
                    CLO: indVerRename('CLO', programId),
                    SLO: indVerRename('SLO', programId),
                  }}
                ></Trans>
              </p>
            </div>
          </div>

          <div className="bg-gray">
            <div className="container-fluid">
              <div className="row pb-4 justify-content-center">
                <div className="col-md-10">
                  <div className="p-4">
                    <div className="d-flex justify-content-between">
                      <h5 className="pb-2 pt-2">
                        {programName} / {frameworkName} / {curriculumName} / {courseName} /{' '}
                        {sloLabel} / {capitalize(mappingType)} <Trans i18nKey={'map'}></Trans>
                      </h5>
                    </div>

                    <div className="pb-4">
                      <div className="bg-white border-radious-8 p-3">
                        <p className={`f-16 mb-2 ${lang === 'ar' ? 'text-left' : ''}`}>
                          <Trans i18nKey={'selected_course'}></Trans> :{' '}
                          <b>
                            {courseName} {sloLabel}
                          </b>
                        </p>

                        <div className="bg-gray rounded p-3">
                          <div className={`program_tabheader ${lang === 'ar' ? 'text-left' : ''}`}>
                            <ul className="program_menu">
                              <span className={`mb-0 mt-2 cursor-pointer program_tab active`}>
                                {courseName}
                              </span>
                            </ul>
                          </div>

                          <div className="program-table border-bottom">
                            {CLO.isEmpty() || SLO.isEmpty() ? (
                              <div className="p-4 text-align-center">No records to display</div>
                            ) : (
                              <CloSloMapTable
                                CLO={CLO}
                                SLO={SLO}
                                clickable={clickable}
                                firstLoad={firstLoad}
                                cloDomainIndexes={cloDomainIndexes}
                                getMappedValue={this.getMappedValue}
                                handleChange={this.handleChange}
                                callBack={() => this.setState({ firstLoad: false })}
                              />

                              // <table className="table">
                              //   <thead className="border_bootom">
                              //     <tr>
                              //       <th className="bg-lightdark">
                              //         <div className="aw-75">CLO ##</div>
                              //       </th>
                              //       {CLO.map((clo) => (
                              //         <th key={clo.get('_id')}>
                              //           <Tooltip title={clo.get('name')}>
                              //             <div className="aw-50 ">{clo.get('no', '')}</div>
                              //           </Tooltip>
                              //         </th>
                              //       ))}
                              //     </tr>
                              //   </thead>
                              //   <tbody className="program-table-height learning_outcome_table_height">
                              //     {SLO.map((slo, i) => (
                              //       <tr key={slo.get('_id')} className="tr-change">
                              //         <td className="text-skyblue bg-lightdark border_all">
                              //           <Tooltip title={slo.get('name')}>
                              //             <div className="aw-75 f-14 pt-2 bold">
                              //               {`${slo.get('delivery_symbol', '')}${slo.get(
                              //                 'delivery_no',
                              //                 ''
                              //               )} ${slo.get('no')}`}
                              //             </div>
                              //           </Tooltip>
                              //         </td>
                              //         {CLO.map((clo, j) => (
                              //           <td
                              //             key={`${clo.get('_id')}-${i}-${j}`}
                              //             className={`border_all vertical-align-middle ${
                              //               cloDomainIndexes.includes(j + 1)
                              //                 ? 'border_right_dark'
                              //                 : ''
                              //             }`}
                              //           >
                              //             <div className="aw-50">
                              //               <Form.Check
                              //                 type="checkbox"
                              //                 className="ml-2 bold"
                              //                 checked={this.getMappedValue(
                              //                   clo.get('_id'),
                              //                   slo.get('_id')
                              //                 )}
                              //                 onChange={(e) => {
                              //                   if (
                              //                     CheckPermission(
                              //                       'pages',
                              //                       'Program Input',
                              //                       'Programs',
                              //                       'Add Program'
                              //                     ) ||
                              //                     CheckPermission(
                              //                       'pages',
                              //                       'Program Input',
                              //                       'Programs',
                              //                       'Add Pre-requisite'
                              //                     )
                              //                   ) {
                              //                     this.handleChange(e, clo, slo);
                              //                   }
                              //                 }}
                              //               />
                              //             </div>
                              //           </td>
                              //         ))}
                              //       </tr>
                              //     ))}
                              //   </tbody>
                              // </table>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

CloSloMap.propTypes = {
  history: PropTypes.object,
  course: PropTypes.instanceOf(Map),
  sessionFlow: PropTypes.instanceOf(Map),
  getCourse: PropTypes.func,
  getsessionFlow: PropTypes.func,
  UpdateMapCLOPLO: PropTypes.func,
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    course: selectCourse(state),
    sessionFlow: selectSessionFlow(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(CloSloMap);
