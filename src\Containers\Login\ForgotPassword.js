import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { NotificationManager } from 'react-notifications';
import { Button } from 'react-bootstrap';
// import { Trans } from 'react-i18next';

import * as Constants from '../../constants';
import Loader from '../../Widgets/Loader/Loader';
import Input from '../../Widgets/FormElements/Input/Input';
import Footer from 'Shared/Footer';
import Digiclass_white from 'Assets/Digiclass_white.svg';
import axios from '../../axios';
import useUnifyServiceHook from 'Hooks/useUnifyServiceHook';

const emailVal = Constants.EMAIL_VALIDATION;
const Number = Constants.NUMBER_VALIDATION;

class ForgotPassword extends Component {
  constructor() {
    super();
    this.state = {
      emailVerifyTap: true,
      otpVerificationTap: false,
      passwordTap: false,
      emailError: '',
      pswError: '',
      email: '',
      psw: '',
      otptype: 'email',
      signInMessage: '',
      otp: '',
      checkBoxMobile: true,
      checkBoxEmail: false,
      signInShow: true,
      minutes: 3,
      seconds: 0,
      sidebarShow: false,
      id: '',
      isLoading: false,
      delayMsg: true,
      show: false,
      password: '',
      confirmpassword: '',
      confirmPswError: '',
      disableotp: true,
    };
    this.forgetEmailRef = React.createRef();
  }

  componentDidMount() {
    this.triggerRef();
  }

  triggerRef = () => {
    if (this.forgetEmailRef.current !== null) {
      this.forgetEmailRef.current.focus();
    }
  };

  componentWillUnmount() {
    clearInterval(this.myInterval);
  }

  timer = () => {
    this.myInterval = setInterval(() => {
      const { seconds, minutes } = this.state;
      if (seconds > 0) {
        this.setState(({ seconds }) => ({
          seconds: seconds - 1,
        }));
      }
      if (seconds === 0) {
        if (minutes === 0) {
          this.setState({ disableotp: false });

          clearInterval(this.myInterval);
        } else {
          this.setState(({ minutes }) => ({
            minutes: minutes - 1,
            seconds: 59,
          }));
        }
      }
    }, 1000);
  };

  signUp = () => {
    this.props.history.push('/signup');
  };

  onChange = (e, name) => {
    e.preventDefault();
    if (name === 'email') {
      this.setState({
        email: e.target.value,
        emailError: '',
      });
    }
    if (name === 'psw') {
      this.setState({
        password: e.target.value,
        pswError: '',
      });
    }
    if (name === 'newPsw') {
      this.setState({
        newPsw: e.target.value,
        newPswError: '',
      });
    }
    if (name === 'confirmPsw') {
      this.setState({
        confirmpassword: e.target.value,
        confirmPswError: '',
      });
    }
    if (name === 'mobile') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mobile: e.target.value,
        mobileError: '',
      });
    }
    if (name === 'otp') {
      if (isNaN(e.target.value)) return;
      this.setState({
        otp: e.target.value,
        otpError: '',
      });
    }
  };

  validation = () => {
    let emailError = '';
    let pswError = '';

    if (!this.state.email) {
      emailError = 'Email is Required';
    } else if (!emailVal.test(this.state.email)) {
      emailError = 'Please enter Valid Email Address';
    }

    // if (!this.state.psw) {
    //   pswError = "Password is Required";
    // } else if (this.state.psw.length <= 7) {
    //   pswError = "Minimum 8 character is required ";
    // }

    if (emailError || pswError) {
      this.setState({
        emailError,
        pswError,
      });
      return false;
    }
    return true;
  };

  passwordvalidation = () => {
    let confirmPswError = '';

    if (!this.state.password) {
      confirmPswError = 'Password is Required';
    } else if (this.state.password.length <= 7) {
      confirmPswError = 'Minimum 8 character is required ';
    }
    if (confirmPswError) {
      this.setState({
        confirmPswError,
      });
      return false;
    }
    return true;
  };

  otpValidation = () => {
    let otpError = '';

    if (!this.state.otp) {
      otpError = 'OTP is Required';
    } else if (this.state.otp.length <= 3) {
      otpError = 'Minimum 4 character is required ';
    } else if (!Number.test(this.state.otp)) {
      otpError = 'Numbers Only Allow ';
    }

    if (otpError) {
      this.setState({
        otpError,
      });
      return false;
    }
    return true;
  };
  handleSubmit = (e) => {
    e.preventDefault();
    if (this.passwordvalidation()) {
      const confirmdata = {
        id: this.state.id,
        new_password: this.state.password,
      };
      if (this.state.password !== this.state.confirmpassword) {
        this.setState({ confirmPswError: 'password didnot match' });
        return false; // The form won't submit
      }
      axios.post(`/user/forget_set_password`, confirmdata).then((res) => {
        if (res.data.status_code === 200) {
          NotificationManager.success('Password Changed Successfully');
          // store.addNotification({
          //   content: (
          //     <div className="success_notification">
          //       IPassword Changed Successfully
          //       <img
          //         src={require('../../Assets/elipsis.svg')}
          //         className="notification-item-img"
          //         alt="Password Changed Successfully"
          //       />{' '}
          //     </div>
          //   ),
          //   container: 'top-right',
          //   animationIn: ['animated', 'fadeIn'],
          //   animationOut: ['animated', 'zoomOut'],
          //   dismiss: {
          //     duration: 2000,
          //   },
          // });
          this.setState({ isLoading: false, delayMsg: true });

          this.props.history.push('/login');
        }
      });
    }
  };
  hangleSignIn = (e) => {
    e.preventDefault();
    if (this.validation()) {
      const signIn = {
        email: this.state.email,
        otp_mode: this.state.otptype,
      };
      this.setState({ isLoading: true, delayMsg: false });

      axios
        .post(`/user/forget_send_otp`, signIn)
        .then((res) => {
          if (res.data.status_code === 200) {
            this.setState({ id: res.data.data._id });
            NotificationManager.success('Enter Your OTP');
            // store.addNotification({
            //   content: (
            //     <div className="success_notification">
            //       Enter Your OTP
            //       <img
            //         src={require('../../Assets/elipsis.svg')}
            //         className="notification-item-img"
            //         alt="Enter Your OTP"
            //       />{' '}
            //     </div>
            //   ),
            //   container: 'top-right',
            //   animationIn: ['animated', 'fadeIn'],
            //   animationOut: ['animated', 'zoomOut'],
            //   dismiss: {
            //     duration: 2000,
            //   },
            // });
            this.setState(
              {
                isLoading: false,
                signInMessage: '',
                emailVerifyTap: false,
                otpVerificationTap: true,
              },
              () => {
                this.timer();
              }
            );
          } else {
            this.setState({
              signInMessage: res.data.message,
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          this.setState({
            signInMessage: error.response.data.message,
            isLoading: false,
          });
        });
    }
  };

  handleResendOtp = (e) => {
    e.preventDefault();
    this.setState({ disableotp: true });
    const signIn = {
      email: this.state.email,
      otp_mode: this.state.otptype,
    };
    this.setState({ isLoading: true, minutes: 3, seconds: 0, otp: '', otpError: '' });
    axios
      .post(`/user/forget_send_otp`, signIn)
      .then((res) => {
        if (res.data.status_code === 200) {
          NotificationManager.success('OTP Has Been Send');
          // store.addNotification({
          //   content: (
          //     <div className="success_notification">
          //       OTP Has Been Send
          //       <img
          //         src={require('../../Assets/elipsis.svg')}
          //         className="notification-item-img"
          //         alt="OTP Has Been Send"
          //       />{' '}
          //     </div>
          //   ),
          //   container: 'top-right',
          //   animationIn: ['animated', 'fadeIn'],
          //   animationOut: ['animated', 'zoomOut'],
          //   dismiss: {
          //     duration: 2000,
          //   },
          // });
          this.setState(
            {
              isLoading: false,
              signInMessage: '',
              emailVerifyTap: false,
              otpVerificationTap: true,
            },
            () => {
              this.timer();
            }
          );
        } else {
          this.setState({
            signInMessage: res.data.message,
            isLoading: false,
          });
        }
      })
      .catch((error) => {
        this.setState({
          signInMessage: error.response.data.message,
          isLoading: false,
        });
      });
  };

  handleOtpSubmit = (e) => {
    e.preventDefault();
    if (this.otpValidation()) {
      const mobileNumber = {
        id: this.state.id,
        otp: this.state.otp,
      };
      this.setState({ isLoading: true, delayMsg: false });
      axios
        .post('/user/otp_verify', mobileNumber)
        .then((res) => {
          if (res.data.status_code === 200) {
            this.setState({ isLoading: false, delayMsg: true, passwordTap: true, show: true });
          } else {
            NotificationManager.error('Invalid OTP');
            // store.addNotification({
            //   content: (
            //     <div className="success_notification">
            //       Invalid OTP
            //       <img
            //         src={require('../../Assets/elipsis.svg')}
            //         className="notification-item-img"
            //         alt="Invalid OTP"
            //       />{' '}
            //     </div>
            //   ),
            //   container: 'top-right',
            //   animationIn: ['animated', 'fadeIn'],
            //   animationOut: ['animated', 'zoomOut'],
            //   dismiss: {
            //     duration: 2000,
            //   },
            // });
            this.setState({ isLoading: false, delayMsg: true });
          }
        })
        .catch((error) => {
          if (error.response?.data?.message) NotificationManager.error(error.response.data.message);
          this.setState({ isLoading: false, delayMsg: true });
        });
    }
  };

  handlecheckMobile = (e) => {
    this.setState(
      {
        checkBoxMobile: true,
        checkBoxEmail: false,
        otptype: 'mobile',
      },
      () => this.triggerRef()
    );
  };

  handlecheckEmail = (e) => {
    this.setState(
      {
        checkBoxEmail: true,
        checkBoxMobile: false,
        otptype: 'email',
      },
      () => this.triggerRef()
    );
  };

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.success !== this.props.success && this.props.success != null) {
      NotificationManager.error('Login Successfully');
      // store.addNotification({
      //   content: <div className="success_notification">Login Suceesfull </div>,
      //   container: 'top-right',
      //   animationIn: ['animated', 'fadeIn'],
      //   animationOut: ['animated', 'zoomOut'],
      //   dismiss: {
      //     duration: 2000,
      //   },
      // });

      this.setState({
        isLoading: false,
        signInMessage: '',
        emailVerifyTap: false,
        createPswTap: false,
        createMobileTap: true,
        resendOtp: true,
        signInShow: false,
        sidebarShow: true,
      });

      setTimeout(() => {
        this.setState({ delayMsg: true });
      }, 3000);
    } else if (prevProps.error !== this.props.error && this.props.error != null) {
      NotificationManager.error(this.props.error);
      // store.addNotification({
      //   content: <div className="success_notification">{this.props.error}</div>,
      //   container: 'top-right',
      //   animationIn: ['animated', 'fadeIn'],
      //   animationOut: ['animated', 'zoomOut'],
      //   dismiss: {
      //     duration: 2000,
      //   },
      // });
      this.setState({
        isLoading: false,
      });
    }
  }

  render() {
    const { minutes, seconds, isLoading, signInShow } = this.state;
    const { unifyData } = this.props;
    return (
      <div>
        <Loader isLoading={isLoading} />
        <div>
          <div className="login-bg">
            {signInShow === true && (
              <div className="container">
                <div className="row justify-content-center pt-5">
                  <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                    <h3 className="text-center">
                      {' '}
                      <img src={Digiclass_white} alt="DigiClass" width={'70%'} />
                    </h3>
                  </div>
                </div>

                <div className="row justify-content-center">
                  <div className="col-xl-4 col-lg-5 col-md-7 col-7">
                    <div className="outter-login">
                      {!this.state.passwordTap && (
                        <div className="row">
                          <div className="col-xl-7 col-lg-7 col-md-7 col-7">
                            <p className="f-20 pt-3 text-skyblue"> Forgot Password</p>
                          </div>
                          <div className="col-xl-5 col-lg-5 col-md-5 col-5">
                            {' '}
                            {unifyData.get('collegeLogo', '') !== '' && (
                              <img
                                src={unifyData.get('collegeLogo', '')}
                                alt="Digi-scheduler"
                                className="univ-logo"
                              />
                            )}
                          </div>
                        </div>
                      )}
                      {/* 1 Forgot Verification check start */}
                      {this.state.emailVerifyTap === true && (
                        <React.Fragment>
                          {this.state.signInMessage === '' ? (
                            ''
                          ) : (
                            <div className="bg-gray">
                              <p className="text-red text-center p-1">
                                <i className="fa fa-exclamation-circle" aria-hidden="true">
                                  {' '}
                                </i>{' '}
                                {this.state.signInMessage}
                              </p>
                            </div>
                          )}

                          {/* <div className="pt-2 pb-2 text-center">
                            <label className="pb-3 f-24">
                              {' '}
                              <Trans i18nKey={'forgot_msg'}></Trans>
                            </label>
                            <div className="otpcheck">
                                <div className="row">
                                  <div
                                    className={`col-md-6 ${
                                      this.state.checkBoxMobile === true && `checkboxActive`
                                    }`}
                                    onClick={this.handlecheckMobile}
                                  >
                                    <p className="m-0 text-center clickCheckBox"> Mobile</p>
                                  </div>
                                  <div
                                    className={`col-md-6 ${
                                      this.state.checkBoxEmail === true && `checkboxActive`
                                    }`}
                                    onClick={this.handlecheckEmail}
                                  >
                                    <p className="m-0 text-center clickCheckBox">Email </p>
                                  </div>
                                </div>
                              </div>
                          </div> */}
                          <form>
                            {this.state.checkBoxEmail === true && (
                              <div className="pt-2 pb-2">
                                <Input
                                  elementType={'input'}
                                  elementConfig={{
                                    type: 'email',
                                    placeholder: 'Enter Email address',
                                    ref: this.forgetEmailRef,
                                  }}
                                  value={this.state.email}
                                  label={'Email'}
                                  feedback={this.state.emailError}
                                  changed={(e) => this.onChange(e, 'email')}
                                  className={'form-control'}
                                />
                              </div>
                            )}

                            {this.state.checkBoxMobile === true && (
                              <div className="pt-2 pb-2">
                                <Input
                                  elementType={'input'}
                                  elementConfig={{
                                    type: 'email',
                                    placeholder: 'Enter Email address',
                                    ref: this.forgetEmailRef,
                                  }}
                                  value={this.state.email}
                                  label={'Email'}
                                  feedback={this.state.emailError}
                                  changed={(e) => this.onChange(e, 'email')}
                                  className={'form-control'}
                                />
                              </div>
                            )}

                            <div className="pt-3">
                              <Button
                                type="submit"
                                disabled={this.state.isLoading}
                                variant="primary"
                                size="lg"
                                block
                                onClick={this.hangleSignIn}
                                className="f-14"
                              >
                                SEND
                              </Button>
                            </div>
                          </form>
                        </React.Fragment>
                      )}
                      {/* 1 forgot check end */}

                      {/* 2 forgot Otp verification start */}

                      {this.state.otpVerificationTap === true && (
                        <React.Fragment>
                          {this.state.signInMessage === '' ? (
                            ''
                          ) : (
                            <div className="bg-gray">
                              <p className="text-red text-center p-1">
                                <i className="fa fa-exclamation-circle" aria-hidden="true">
                                  {' '}
                                </i>{' '}
                                {this.state.signInMessage}
                              </p>
                            </div>
                          )}
                          {!this.state.show && (
                            <React.Fragment>
                              <label> OTP Send To Your</label>

                              {this.state.otptype === 'mobile' ? (
                                <p className="pt-2">Mobile No: 98*********</p>
                              ) : (
                                <p className="pt-2">Email Id :{this.state.email}</p>
                              )}
                              <form>
                                <div className="pt-2">
                                  <Input
                                    elementType={'input'}
                                    elementConfig={{
                                      type: 'text',
                                      placeholder: 'Enter 4 Digit OTP',
                                    }}
                                    value={this.state.otp}
                                    label={'Enter OTP'}
                                    maxLength={4}
                                    feedback={this.state.otpError}
                                    changed={(e) => this.onChange(e, 'otp')}
                                    className={'form-control'}
                                  />
                                </div>

                                <div className="row pt-4 pb-4">
                                  <div className="col-md-12  text-center">
                                    {minutes === 0 && seconds === 0 ? (
                                      ''
                                    ) : (
                                      <h1 className="f-14 pb-2">
                                        Resend OTP In: {minutes}:
                                        {seconds < 10 ? `0${seconds}` : seconds}
                                      </h1>
                                    )}
                                  </div>

                                  <div className="col-md-12 text-center">
                                    <Button
                                      variant="outline-primary"
                                      size="md"
                                      onClick={this.handleResendOtp}
                                      className="f-14"
                                      disabled={this.state.disableotp}
                                    >
                                      Resend OTP{' '}
                                    </Button>
                                  </div>
                                </div>

                                <div className="pt-3">
                                  <Button
                                    type="submit"
                                    disabled={this.state.isLoading}
                                    variant="primary"
                                    size="lg"
                                    block
                                    onClick={this.handleOtpSubmit}
                                    className="f-14"
                                  >
                                    VERIFY
                                  </Button>
                                </div>
                              </form>
                            </React.Fragment>
                          )}
                        </React.Fragment>
                      )}

                      {/* 2 forgot Otp verification end */}

                      {/* 3 password start */}

                      {this.state.passwordTap === true && (
                        <React.Fragment>
                          <div className="row">
                            <div className="col-xl-8 col-lg-8 col-md-7 col-7">
                              <p className="f-20 pt-3 text-skyblue"> Reset Password </p>
                            </div>
                            <div className="col-xl-4 col-lg-3 col-md-5 col-5">
                              <h3 className="text-center">
                                {' '}
                                {unifyData.get('collegeLogo', '') !== '' && (
                                  <img
                                    src={unifyData.get('collegeLogo', '')}
                                    alt="Digi-scheduler"
                                    className="univ-logo"
                                  />
                                )}
                              </h3>{' '}
                            </div>
                          </div>

                          {this.state.signInMessage === '' ? (
                            ''
                          ) : (
                            <div className="bg-gray">
                              <p className="text-red text-center p-1">
                                <i className="fa fa-exclamation-circle" aria-hidden="true">
                                  {' '}
                                </i>{' '}
                                {this.state.signInMessage}
                              </p>
                            </div>
                          )}
                          <form>
                            <div className="pt-2">
                              <Input
                                elementType={'input'}
                                elementConfig={{
                                  type: 'password',
                                  placeholder: 'Enter New Password',
                                }}
                                value={this.state.password}
                                label={'Enter New Password'}
                                maxLength={30}
                                feedback={this.state.pswError}
                                changed={(e) => this.onChange(e, 'psw')}
                                className={'form-control'}
                              />
                            </div>

                            <div className="pt-2 ">
                              <Input
                                elementType={'input'}
                                elementConfig={{
                                  type: 'password',
                                  placeholder: 'Re-Enter Password',
                                }}
                                value={this.state.confirmpassword}
                                label={'Re-Enter Password'}
                                maxLength={30}
                                feedback={this.state.confirmPswError}
                                changed={(e) => this.onChange(e, 'confirmPsw')}
                                className={'form-control'}
                              />
                            </div>

                            <div className="pt-5">
                              <Button
                                type="submit"
                                disabled={this.state.isLoading}
                                variant="primary"
                                size="lg"
                                block
                                onClick={(e) => {
                                  this.handleSubmit(e);
                                }}
                                className="f-14"
                              >
                                DONE
                              </Button>
                            </div>
                          </form>
                        </React.Fragment>
                      )}
                      {/* 3 password end */}
                    </div>
                  </div>
                </div>
                <div className="row justify-content-center pt-4 pb-2">
                  <Footer />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
}

ForgotPassword.propTypes = {
  history: PropTypes.object,
  success: PropTypes.any,
  error: PropTypes.any,
};

const mapStateToProps = (state) => {
  return {
    error: state.auth.error,
    success: state.auth.success,
    isAuthenticated: state.auth.token !== null,
  };
};

const ConnectedForgotPassword = connect(mapStateToProps)(withRouter(ForgotPassword));

const ForgotPasswordWrapper = (props) => {
  const { unifyData, loading } = useUnifyServiceHook();

  return (
    <>
      <Loader isLoading={loading} />
      <ConnectedForgotPassword {...props} unifyData={unifyData} />
    </>
  );
};

export default ForgotPasswordWrapper;
