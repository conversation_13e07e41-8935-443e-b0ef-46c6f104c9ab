import React from 'react';
import PropTypes from 'prop-types';
//import { Trans } from 'react-i18next';
import Header from './OnBoarding/Header';
import BodyPage from './OnBoarding/BodyPage';
import Footer from './OnBoarding/Footer';

import '../../css/institution.css';

function OnboardingIndex() {
  return (
    <div className="login-bg">
      <div className="container">
        <Header />
        <BodyPage />
        <Footer />
      </div>
    </div>
  );
}

OnboardingIndex.propTypes = {
  children: PropTypes.array,
};
export default OnboardingIndex;
