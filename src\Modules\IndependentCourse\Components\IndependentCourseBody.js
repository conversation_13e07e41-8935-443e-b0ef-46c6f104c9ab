import React, { Suspense } from 'react';
import { Route, Switch } from 'react-router';
import { useParams } from 'react-router-dom';
import { dString } from 'utils';
const OverViewIndex = React.lazy(() => import('../OverView/OverViewIndex'));
const SessionTypes = React.lazy(() =>
  import('Modules/ProgramInput/v2/ConfigurationIndex/SessionTypes/SessionTypesIndex')
);
const CourseMaster = React.lazy(() => import('../CourseMaster/CourseMasterRoutes'));

function IndependentCourseBody() {
  const { id } = useParams();
  const institutionID = dString(id);
  return (
    <Suspense fallback="">
      <Switch>
        <Route path={'/:type/:id/:name/independent-course'} exact component={OverViewIndex}></Route>

        <Route
          path={'/:type/:id/:name/independent-course/course-master-list'}
          component={CourseMaster}
        ></Route>

        <Route path={'/:type/:id/:name/independent-course/session-types'} exact>
          <SessionTypes
            requiredData={{
              requiredID: institutionID,
              ProgramLabel: 'Independent Course',
              type: 'independent',
            }}
            requiredID={institutionID}
          />
        </Route>
      </Switch>
    </Suspense>
  );
}

/* IndependentCourseBody.propTypes = {
  location: PropTypes.instanceOf(List),
}; */

export default IndependentCourseBody;
