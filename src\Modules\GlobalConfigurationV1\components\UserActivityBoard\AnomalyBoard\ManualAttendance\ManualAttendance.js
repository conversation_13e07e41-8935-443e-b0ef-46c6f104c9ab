import React, { forwardRef } from 'react';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import manual_attendance from 'Assets/user_activity_board/manual_attendance.svg';
import OverAllAttendanceRate from './Criteria/OverAllAttendanceRate';
import PropTypes from 'prop-types';
import ConductedAttendanceSessions from './Criteria/ConductedAttendanceSessions';
import { Map } from 'immutable';

const ManualAttendance = forwardRef(({ anomalyBoard }, criteriaRef) => {
  return (
    <Accordion disableGutters sx={{ background: 'white !important' }}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1a-content"
        id="panel1a-header"
        className="px-3 py-2"
      >
        <div className="d-flex">
          <img src={manual_attendance} alt="icon_late_config" />
          <div className="ml-3">
            <div className="f-18 bold late_config_color">Manual Attendance</div>
            <span className="f-15 text-light-grey pt-1">Create the default Criteria’s.</span>
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails className="grid_criteria_leader">
        <OverAllAttendanceRate anomalyBoard={anomalyBoard} ref={criteriaRef} />
        <ConductedAttendanceSessions anomalyBoard={anomalyBoard} ref={criteriaRef} />
      </AccordionDetails>
    </Accordion>
  );
});

ManualAttendance.propTypes = {
  anomalyBoard: PropTypes.instanceOf(Map),
};

export default ManualAttendance;
