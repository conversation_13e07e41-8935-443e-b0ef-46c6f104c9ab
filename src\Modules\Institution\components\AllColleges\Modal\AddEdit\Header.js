import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { getLang } from 'utils';
import MButton from '../../../../../../Widgets/FormElements/material/Button';

function Header({ setShow, title, onClickSaved, handleBack }) {
  return (
    <>
      <div className={`${getLang() === 'ar' ? 'pl-2.5' : ''}`} style={{ width: '100%' }}>
        <div className="d-flex justify-content-between pt-1">
          <div className="pt-2 bold f-18">
            <Trans i18nKey={`add_colleges.${title}`}></Trans>
          </div>
          <div className="d-flex">
            <div className={`${getLang() === 'ar' ? 'col-md-6 pr-1 digi-ml-10' : 'col-md-6 pr-1'}`}>
              <MButton
                className="mr-3"
                color="inherit"
                variant="outlined"
                fullWidth
                onClick={() => setShow(false)}
                clicked={handleBack}
              >
                <Trans i18nKey={'cancel'}></Trans>
              </MButton>
            </div>
            <div className="col-md-6 pr-1">
              <MButton
                className="ss"
                color="primary"
                fullWidth={true}
                clicked={() => onClickSaved()}
                variant={'contained'}
              >
                <Trans i18nKey={'add_colleges.save'}></Trans>
              </MButton>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

Header.propTypes = {
  setShow: PropTypes.func,
  onClickSaved: PropTypes.func,
  title: PropTypes.string,
  handleBack: PropTypes.func,
};
export default Header;
