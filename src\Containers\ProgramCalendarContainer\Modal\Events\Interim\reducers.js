import { setMinutes, setHours } from 'date-fns';
export const initial_event_state = {
  event_mode: 'manual',
  type: {
    title: '',
    event_type: 'exam',
    start_date: '',
    start_time: setHours(setMinutes(new Date(), 0), 8),
    end_date: '',
    end_time: setHours(setMinutes(new Date(), 0), 17),
  },
  source: [],
  copied: [],
};

const toggle_select_all = (state, source_arr, check) => {
  if (check) {
    return {
      ...state,
      source: source_arr.map((item) => ({ ...item, isChecked: true })),
      copied: source_arr.map((item) => ({ ...item, isChecked: true })),
    };
  } else {
    return {
      ...state,
      source: source_arr.map((item) => ({ ...item, isChecked: false })),
      copied: [],
    };
  }
};

const toggle_item = (state, source_arr, check, id) => {
  if (check) {
    let updated = source_arr.map((item) =>
      item._id === id ? { ...item, isChecked: true } : { ...item }
    );
    return {
      ...state,
      source: [...updated],
      copied: updated.filter((item) => item.isChecked),
    };
  } else {
    let updated = source_arr.map((item) =>
      item._id === id ? { ...item, isChecked: false } : { ...item }
    );
    return {
      ...state,
      source: [...updated],
      copied: updated.filter((item) => item.isChecked),
    };
  }
};

export const events_reducer = (state, action) => {
  const { type, payload, name, id } = action;

  switch (type) {
    case 'INDIVIDUAL_TOGGLE':
      return toggle_item(state, [...state.source], payload, id);
    case 'CLEAR':
      return {
        ...state,
        event_mode: payload,
        type: {
          title: '',
          event_type: 'exam',
          start_date: '',
          start_time: '',
          end_date: '',
          end_time: '',
        },
        //source: [],
        //copied: [],
      };
    case 'SOURCE_FOR_COPY':
      return {
        ...state,
        source: payload.map((item) => ({ ...item, isChecked: false })),
      };
    case 'EVENT_MODE_CHANGE':
      return {
        ...state,
        [name]: payload,
        type: {
          title: '',
          event_type: 'exam',
          start_date: '',
          start_time: '',
          end_date: '',
          end_time: '',
        },
        copied: [],
        source: state.source.map((item) => ({ ...item, isChecked: false })),
      };
    case 'ON_TYPE':
      return {
        ...state,
        type: {
          ...state.type,
          [name]: payload,
        },
      };
    case 'TOGGLE_SELECT_ALL':
      return toggle_select_all(state, [...state.source], payload);

    default:
      return {
        ...state,
      };
  }
};
