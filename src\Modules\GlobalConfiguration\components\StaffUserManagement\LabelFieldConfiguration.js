import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/global_configuration/actions';
import {
  selectLabelConfigurationDetails,
  selectBasicDetails,
} from '_reduxapi/global_configuration/selectors';
import { AccordionProgramInput } from '../ReusableComponent';
import { fromJS, List, Map } from 'immutable';
import { t } from 'i18next';
import { IconButton, Menu, MenuItem } from '@mui/material';
import { Trans } from 'react-i18next';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import LabelDetail from './LabelDetail';
import { Checkbox, FormControlLabel } from '@mui/material';
import LabelRenameModal from '../../modal/LabelRenameModal';
import { makeStyles } from '@mui/styles';
import { getLang } from 'utils';

const LabelFieldConfiguration = ({
  settingId,
  setAccordionHeader,
  accordionOpen,
  setAccordionOpen,
  count,
  labelConfigurationDetails,
  getLabelConfigurationDetails,
  updateLabelStatus,
  updateVaccinationStatus,
  updateVaccineStatus,
  resetLabelConfiguration,
  updatePhoneLabelStatus,
  updateLabelField,
  setData,
  basicDetails,
  institutionHeader,
  type,
}) => {
  const useStylesFunction = makeStyles(() => ({
    root: { marginRight: 0, marginBottom: 0 },
    checkboxLabel: { fontWeight: 500 },
    checkbox: { padding: '0 9px', color: '#147AFC' },
  }));

  const classes = useStylesFunction();
  const [detailsAccordionOpen, setDetailsAccordionOpen] = useState({ 1: true });
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState({});

  useEffect(() => {
    settingId &&
      accordionOpen &&
      count !== 0 &&
      !labelConfigurationDetails.size &&
      getLabelConfigurationDetails({ settingId, headers: institutionHeader, type });
  }, [settingId, accordionOpen, count]); // eslint-disable-line

  const setDetailsAccordion = (i) => setDetailsAccordionOpen({ [i]: !detailsAccordionOpen[i] });

  const setDetailsAccordionHeader = (
    collapsed,
    heading,
    subHeading = '',
    vaccinationCheck = false
  ) => {
    return (
      <div className="f-16 bold digi-brown">
        <div>
          {vaccinationCheck ? (
            <FormControlLabel
              control={
                <Checkbox
                  classes={{ root: classes.checkbox }}
                  color="primary"
                  checked={labelConfigurationDetails.getIn(
                    ['VaccineDetailsCheckBox', 'isActive'],
                    false
                  )}
                  onChange={(e) => handleVaccinationStatus(e)}
                />
              }
              label={t(heading)}
              onClick={(e) => e.stopPropagation()}
              classes={{ root: classes.root, label: classes.checkboxLabel }}
            />
          ) : (
            <b>{t(heading)}</b>
          )}
        </div>
        {collapsed && subHeading && (
          <p className="mt-1 mb-0 f-13">
            <span>{subHeading}</span>
          </p>
        )}
      </div>
    );
  };

  const defaultLanguageLabelDetails = () => {
    const Lang = getLang();
    const labelDetails = labelConfigurationDetails
      .get('labelDetails', List())
      .find((label) => label.get('language', 'en') === Lang);
    return labelDetails || Map();
  };

  const getProfileSubDetails = () => {
    const contactDetails = type === 'staff' ? 'contactDetails' : 'otherContactDetails';
    return fromJS([
      {
        title: t('address_details'),
        labelType: 'addressDetails',
        data: defaultLanguageLabelDetails().get('addressDetails', List()).toJS(),
      },
      {
        title: t(type === 'staff' ? 'contact_details' : 'other_contact_details'),
        labelType: contactDetails,
        data: defaultLanguageLabelDetails().get(contactDetails, List()).toJS(),
      },
    ]);
  };

  const handleVaccinationStatus = (e) => {
    const value = e.target.checked;
    updateVaccinationStatus({
      formData: { isActive: value },
      settingId,
      headers: institutionHeader,
      type,
    });
  };

  const openRenameModal = (labelType, labelData) => {
    const selected = labelConfigurationDetails.get('labelDetails', List()).map((lang) => {
      const selLabel = lang
        .get(labelType, List())
        .find((label) => label.get('name', '') === labelData.get('name'));
      return Map({
        language: lang.get('language', 'en'),
        label: selLabel.get('translatedInput', '')
          ? selLabel.get('translatedInput')
          : selLabel.get('name', ''),
      });
    });
    setSelectedLabel({
      labelType,
      labelId: labelData.get('_id'),
      labelName: labelData.get('name'),
      labels: selected,
    });
    setShowRenameModal(true);
  };

  const labelStatusUpdateProps = {
    settingId,
    updateLabelStatus,
    updateVaccineStatus,
    updatePhoneLabelStatus,
    openRenameModal,
    institutionHeader,
    type,
    getLabelConfigurationDetails,
  };

  const MenuOptions = () => {
    const [anchorEl, setAnchorEl] = useState(null);
    const menuOpen = Boolean(anchorEl);
    const handleMenuClose = () => setAnchorEl(null);

    return (
      <div onClick={(e) => e.stopPropagation()}>
        <IconButton
          aria-label="more"
          aria-controls="long-menu"
          aria-haspopup="true"
          onClick={(e) => setAnchorEl(e.currentTarget)}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={menuOpen}
          onClose={handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: 48 * 4.5,
              width: '20ch',
            },
          }}
        >
          <MenuItem
            onClick={() => resetLabelConfiguration({ settingId, headers: institutionHeader, type })}
          >
            <Trans i18nKey={'global_configuration.reset_all'} />
          </MenuItem>
        </Menu>
      </div>
    );
  };

  const DetailsComponent = () => {
    return (
      <div className="container">
        <div className="text-right bold">
          <Trans i18nKey={'global_configuration.set_mandatory'} />
        </div>

        <LabelDetail
          detailsAccordionOpen={detailsAccordionOpen[1]}
          setDetailsAccordion={() => setDetailsAccordion(1)}
          summaryChildren={setDetailsAccordionHeader(
            detailsAccordionOpen[1],
            'global_configuration.basic_details',
            t('global_configuration.basic_details_info')
          )}
          details={defaultLanguageLabelDetails().get('basicDetails', List())}
          labelType={'basicDetails'}
          labelStatusUpdateProps={labelStatusUpdateProps}
        />

        <LabelDetail
          detailsAccordionOpen={detailsAccordionOpen[2]}
          setDetailsAccordion={() => setDetailsAccordion(2)}
          summaryChildren={setDetailsAccordionHeader(
            detailsAccordionOpen[2],
            'global_configuration.profile_details'
          )}
          details={defaultLanguageLabelDetails().get('profileDetails', List())}
          subDetails={getProfileSubDetails()}
          labelType={'profileDetails'}
          labelStatusUpdateProps={labelStatusUpdateProps}
        />

        <LabelDetail
          detailsAccordionOpen={detailsAccordionOpen[3]}
          setDetailsAccordion={() => setDetailsAccordion(3)}
          summaryChildren={setDetailsAccordionHeader(
            detailsAccordionOpen[3],
            'global_configuration.vaccination_details',
            '',
            true
          )}
          details={labelConfigurationDetails.get('vaccineDetails', List())}
          labelType={'vaccineDetails'}
          labelStatusUpdateProps={labelStatusUpdateProps}
          noBorder={true}
        />
      </div>
    );
  };

  return (
    <>
      <AccordionProgramInput
        expanded={accordionOpen || false}
        onClick={setAccordionOpen}
        summaryChildren={setAccordionHeader(
          'global_configuration.label_field_configuration',
          `${count} ${t('global_configuration.labels')}`,
          !accordionOpen,
          <MenuOptions />
        )}
        detailChildren={<DetailsComponent />}
      />
      <hr />

      {showRenameModal && (
        <LabelRenameModal
          show={showRenameModal}
          onClose={() => setShowRenameModal(false)}
          selectedLabel={selectedLabel}
          settingId={settingId}
          updateLabelField={updateLabelField}
          setData={setData}
          selectedLanguages={basicDetails.get('language', List())}
          institutionHeader={institutionHeader}
          type={type}
        />
      )}
    </>
  );
};

LabelFieldConfiguration.propTypes = {
  settingId: PropTypes.string,
  type: PropTypes.string,
  setAccordionHeader: PropTypes.func,
  accordionOpen: PropTypes.bool,
  setAccordionOpen: PropTypes.func,
  count: PropTypes.number,
  labelConfigurationDetails: PropTypes.instanceOf(Map),
  getLabelConfigurationDetails: PropTypes.func,
  updateLabelStatus: PropTypes.func,
  updateVaccineStatus: PropTypes.func,
  updateVaccinationStatus: PropTypes.func,
  resetLabelConfiguration: PropTypes.func,
  updatePhoneLabelStatus: PropTypes.func,
  updateLabelField: PropTypes.func,
  setData: PropTypes.func,
  basicDetails: PropTypes.instanceOf(Map),
  institutionHeader: PropTypes.object,
};

const mapStateToProps = function (state) {
  return {
    labelConfigurationDetails: selectLabelConfigurationDetails(state),
    basicDetails: selectBasicDetails(state),
  };
};

export default connect(mapStateToProps, actions)(LabelFieldConfiguration);
