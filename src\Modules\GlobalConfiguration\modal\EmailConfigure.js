import React, { useEffect, useState } from 'react';
import { Modal } from 'react-bootstrap';
import { Editor } from 'react-draft-wysiwyg';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans, useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { EditorState, convertToRaw, convertFromHTML, ContentState } from 'draft-js';
import draftToHtml from 'draftjs-to-html';
import { EDITOR_TOOLBAR } from '../../Institution/components/UniversityDetails/udUtil';
import { useDispatch } from 'react-redux';
import { updateMailConfiguration } from '_reduxapi/global_configuration/actions';
import { Map } from 'immutable';
import { nl2br } from 'utils';

function EmailConfigure({
  handleClose,
  open,
  setting,
  settingId,
  institutionHeader,
  source,
  type,
}) {
  const { t } = useTranslation();
  const [editorState, onEditorStateChange] = useState(EditorState.createEmpty());
  const [edited, setEdited] = useState(false);
  const dispatch = useDispatch();
  useEffect(() => {
    if (setting.get('labelBody', '')) {
      const blocksFromHTML = convertFromHTML(setting.get('labelBody', ''));
      const content = ContentState.createFromBlockArray(
        blocksFromHTML.contentBlocks,
        blocksFromHTML.entityMap
      );
      const editorStateData = EditorState.createWithContent(content);
      onEditorStateChange(editorStateData);
    }
  }, [setting]); //eslint-disable-line

  const handleSave = () => {
    const rawContentState = convertToRaw(editorState?.getCurrentContent());
    const markup = draftToHtml(rawContentState);
    const payload = {
      settingId: settingId,
      labelBody: nl2br(markup),
      labelSalutation: 'dear',
      labelSignature: 'Program Admin',
    };
    dispatch(
      updateMailConfiguration({
        settingId: setting.get('_id', ''),
        payload: payload,
        callback: handleClose(),
        headers: institutionHeader,
        type,
      })
    );
  };
  const handleChange = () => {
    setEdited(true);
  };
  return (
    <Modal show={open} centered onHide={handleClose} dialogClassName={`model-900 `}>
      <Modal.Header className="border-none pb-0" closeButton>
        <Modal.Title className="f-20"> {setting.get('labelName', '')}</Modal.Title>
      </Modal.Header>

      <Modal.Body className="pt-0 model-border rounded mt-3 mx-3 mb-0">
        <div className="pt-4 ">
          <div className="digi-editor-border rounded min-height_200px">
            <Editor
              editorState={editorState}
              wrapperClassName="wrapperClassName"
              editorClassName="editorClassName"
              onEditorStateChange={onEditorStateChange}
              toolbar={EDITOR_TOOLBAR}
              placeholder={t(`add_colleges.Add_Description`)}
              onChange={handleChange}
              value={``}
            ></Editor>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton
          disabled={!edited || !editorState?.getCurrentContent().getPlainText()}
          clicked={() => {
            !source ? handleSave() : handleClose();
          }}
        >
          <Trans i18nKey={'add_colleges.save'}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

EmailConfigure.propTypes = {
  open: PropTypes.bool,
  EditStaffFlag: PropTypes.bool,
  handleClose: PropTypes.func,
  portfolioDescription: PropTypes.instanceOf(EditorState),
  t: PropTypes.func,
  setting: PropTypes.instanceOf(Map),
  settingId: PropTypes.string,
  institutionHeader: PropTypes.object,
  source: PropTypes.string,
  type: PropTypes.string,
};

export default EmailConfigure;
