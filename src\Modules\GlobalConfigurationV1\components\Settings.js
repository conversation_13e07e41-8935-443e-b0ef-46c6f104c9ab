import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import WarningIcon from '@mui/icons-material/Warning';
import { Button, Modal } from 'react-bootstrap';
import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Paper } from '@mui/material';
import { LoadingButton } from '@mui/lab';

import { CheckPermission } from 'Modules/Shared/Permissions';
import { saveScheduleSettings } from '_reduxapi/global_configuration/v1/actions';
import { selectIsLoading } from '_reduxapi/global_configuration/v1/selectors';

const Settings = () => {
  const history = useHistory();
  const dispatch = useDispatch();
  const isLoading = useSelector(selectIsLoading);
  const [openDeleteModalOpen, setOpenDeleteModal] = useState(false);
  const isEditEnabled = CheckPermission(
    'tabs',
    'Global Configuration',
    'Institution',
    '',
    'Schedule Setting',
    'Edit'
  );

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleSave = () => {
    const payload = {
      scheduleDate: moment().format('YYYY-MM-DD'),
      dbUpdate: true,
    };
    dispatch(saveScheduleSettings({ payload, callBack: () => setOpenDeleteModal(false) }));
  };
  return (
    <div>
      <div className="d-flex align-items-center border-bottom pt-3 pb-3 mb-2">
        <HomeIcon fontSize="small" className="digi-gray-neutral" />
        <span
          className="ml-2 mr-2 remove_hover"
          onClick={() => history.push('/globalConfiguration-v1/institution')}
        >
          All Modules
        </span>
        <KeyboardArrowRightIcon fontSize="" />
        <span className="ml-2 text-skyblue">Settings</span>
      </div>
      <Paper className="p-2 mt-3" sx={{ minHeight: '100vh' }}>
        <div className="d-flex align-items-center p-4">
          <div className="d-flex flex-column">
            <div className="pt-1 pl-2 bold f-17">Change session mode from On-site to Remote</div>
            <div className="pl-2 f-14 digi-gray-neutral">
              Only Today&#39;s onsite schedule will be migrated to Remote schedule.
            </div>
          </div>
          <div className="ml-auto mr-2">
            <LoadingButton
              loading={isLoading}
              disabled={!isEditEnabled}
              onClick={() => setOpenDeleteModal(true)}
              variant="contained"
            >
              Switch to remote
            </LoadingButton>
          </div>
        </div>
      </Paper>
      {openDeleteModalOpen && (
        <DeleteModal
          show={openDeleteModalOpen}
          handleClickClose={() => setOpenDeleteModal(false)}
          handleClick={handleSave}
        />
      )}
    </div>
  );
};

export default Settings;

const DeleteModal = ({ show, handleClickClose, handleClick }) => {
  return (
    <Modal show={show} centered>
      <Modal.Body>
        <div className="row">
          <div className="col-md-12">
            <div className="d-flex align-items-center mb-2">
              <WarningIcon sx={{ fontSize: 18, color: 'red' }} />
              <div className="f-18 bold ml-2">Alert</div>
            </div>
            <p className="f-15 mb-1">
              Are you sure want to change all the session from onsite to remote?
            </p>
            <p className="f-15 mb-1 bold">Action cannot be Undone</p>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer className="">
        <div>
          <b className="pr-2">
            <Button
              variant="outline-primary"
              className="outline-primary"
              onClick={handleClickClose}
            >
              No
            </Button>
          </b>
          <b className="pr-2">
            <Button variant="primary" className="primary" onClick={() => handleClick()}>
              Yes
            </Button>
          </b>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

DeleteModal.propTypes = {
  show: PropTypes.bool,
  handleClickClose: PropTypes.func,
  id: PropTypes.string,
  callBackFn: PropTypes.func,
  history: PropTypes.object,
  courseId: PropTypes.string,
  activityModalType: PropTypes.string,
  activityName: PropTypes.string,
  activityTypeBase: PropTypes.string,
};
