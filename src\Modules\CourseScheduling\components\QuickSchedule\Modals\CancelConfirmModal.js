import React from 'react';
import PropTypes from 'prop-types';
import { Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import { buttonSx } from '../utils';

const titleSx = {
  padding: '12px 16px',
};

const CancelConfirmModal = ({ open, handleClose, handleConfirm }) => {
  return (
    <Dialog open={open} maxWidth="xs" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        Confirmation
      </DialogTitle>
      <DialogContent className="p-3">
        <Typography mt={0.5} mb={0.5} color="#374151">
          Are you sure want to cancel scheduling?
        </Typography>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
          Back
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleConfirm} sx={buttonSx}>
          Yes, Cancel
        </MButton>
      </DialogActions>
    </Dialog>
  );
};
CancelConfirmModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  handleConfirm: PropTypes.func,
};

export default CancelConfirmModal;
