import React from 'react';
import { Checkbox, FormControlLabel } from '@mui/material';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PropTypes from 'prop-types';
import { getShortString } from 'Modules/Shared/v2/Configurations';
// import { List } from 'immutable';
import { getCount } from '../utils';

const AccordionTreeView = ({
  handleChangeProgram,
  institutionIndex,
  item,
  classes,
  title,
  type,
  termIndex,
  curriculumIndex,
  yearLevelIndex,
  rotationIndex,
  rotationCourseIndex,
  regularCourseIndex,
  regularSizeIndex,
}) => {
  const checkType = ['regularCourse', 'rotationCourse'].includes(type);
  const { count, totalCount } = getCount(item, type);
  return (
    <div className="d-flex align-items-center">
      <div className={`d-flex align-items-center color-pointer ${classes}`}>
        <FormControlLabel
          control={<Checkbox color="primary" checked={item.get('status', false)} />}
          className="m-0 p-0"
          onChange={(event) =>
            handleChangeProgram({
              event,
              type,
              institutionIndex,
              termIndex,
              curriculumIndex,
              yearLevelIndex,
              rotationIndex,
              rotationCourseIndex,
              regularSizeIndex,
              regularCourseIndex,
              name: 'status',
            })
          }
        />
        <div
          className="digi-pointer d-flex align-self-center mt-1"
          onClick={() =>
            !checkType
              ? handleChangeProgram({
                  type,
                  institutionIndex,
                  termIndex,
                  curriculumIndex,
                  yearLevelIndex,
                  rotationIndex,
                  rotationCourseIndex,
                  regularSizeIndex,
                  regularCourseIndex,
                })
              : {}
          }
        >
          {!checkType && (
            <div className="mx-1 cursor">
              {item.get('isOpen', false) ? (
                <ExpandLessIcon fontSize="small" color="primary" />
              ) : (
                <ExpandMoreIcon fontSize="small" color="primary" />
              )}
            </div>
          )}
          <div className="f-14 bold">
            <div className="d-sm-block d-md-none d-sm-none d-lg-block d-xl-block d-block">
              {getShortString(title, 34)}
            </div>
            <div className="d-sm-none d-md-block d-sm-none d-lg-none d-xl-none d-block">
              {getShortString(title, 15)}
            </div>
          </div>
        </div>
      </div>
      {!checkType && (
        <div className="f-12 ml-auto mr-3">
          {count} of {totalCount} selected
        </div>
      )}
    </div>
  );
};

AccordionTreeView.propTypes = {
  handleChangeProgram: PropTypes.func,
  institutionIndex: PropTypes.number,
  item: PropTypes.object,
  classes: PropTypes.string,
  title: PropTypes.string,
  type: PropTypes.string,
  curriculumIndex: PropTypes.number,
  termIndex: PropTypes.number,
  yearLevelIndex: PropTypes.number,
  rotationIndex: PropTypes.number,
  rotationCourseIndex: PropTypes.number,
  regularCourseIndex: PropTypes.number,
  regularSizeIndex: PropTypes.number,
};

export default AccordionTreeView;
