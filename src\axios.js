import Config from './config.js';
import axios from 'axios';
import { createBrowserHistory } from 'history';

import LocalStorageService from './LocalStorageService';
import { getLang } from 'utils';
import {
  checkEncryptedRoute,
  encryptSessionKey,
  encryptPayload,
  decryptPayload,
} from './encryption.js';

const URL = Config.API_URL;
const forgotPasswordTokenUrls = ['/user/otp_verify', '/user/forget_set_password'];
const instance = axios.create({
  baseURL: URL,
});
const history = createBrowserHistory();

// instance.defaults.headers.common['Access-Control-Allow-Origin'] = '*';
instance.defaults.headers.common['Cache-Control'] = 'no-cache';
instance.defaults.headers.common['Pragma'] = 'no-cache';
instance.defaults.headers.common['Expires'] = '0';

instance.interceptors.request.use(
  async (config) => {
    const sessionKey = await encryptSessionKey(); // wait for promise to resolve
    config.headers['X-SESSION-KEY'] = sessionKey;

    let token = LocalStorageService.getAccessToken();
    let userId = LocalStorageService.getUserId();
    const forgotPasswordToken = LocalStorageService.getCustomCookie('forgot_password_access_token');

    if (forgotPasswordTokenUrls.includes(config.url) && forgotPasswordToken) {
      token = forgotPasswordToken;
      userId = LocalStorageService.getCustomToken('userId');
    }

    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    const institutionId = LocalStorageService.getCustomCookie('_institution_id');
    // if (institutionId && config.headers._parent_id === undefined) {
    if (institutionId !== '' && institutionId !== null) {
      config.headers['_institution_id'] = institutionId;
    }
    config.headers['role_id'] = LocalStorageService.getCustomToken('role_id');
    config.headers['user_id'] = userId;
    config.headers['_user_id'] = userId;
    config.headers['Accept-Language'] = getLang();

    // Encrypt request payload if route matches
    if (config.data && checkEncryptedRoute(config)) config.data = await encryptPayload(config.data);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
var check = false;
let alertShown = false;
instance.interceptors.response.use(
  async (response) => {
    const originalRequest = response.config;
    // Decrypt response if route matches
    if (response?.data?.data && checkEncryptedRoute(originalRequest, 'RESPONSE'))
      response.data = await decryptPayload(response?.data?.data);

    if (
      response.status === 200 &&
      response.data.data !== null &&
      [
        '/user/otp_verify',
        '/users/authLogin',
        '/digiclass/user/authLogin',
        '/user/signup',
        '/digiclass/user/authSSOLogin',
      ].includes(originalRequest.url)
    ) {
      const institutionId = response?.data?.data?.services?.REACT_APP_INSTITUTION_ID;
      if (
        (originalRequest.url.includes('/digiclass/user/authLogin') ||
          originalRequest.url.includes('/digiclass/user/authSSOLogin')) &&
        institutionId !== undefined
      ) {
        LocalStorageService.setCustomCookie('_institution_id', institutionId);
        LocalStorageService.setCustomCookie('landed', 'false');
        LocalStorageService.setCustomCookie('site-origin', window.location.origin);
        let updatedData = response.data.data;
        delete updatedData?.services?.ANOMALY_RESTRICT;
        delete updatedData?.services?.ANOMALY_RESTRICT_MESSAGE;
        const { tokens, ...otherData } = updatedData; //eslint-disable-line
        LocalStorageService.setCustomCookie('authData', otherData, true);
        instance.defaults.headers._institution_id = institutionId;
      }
      const tokenObject = {
        access_token: response.data.data.tokens.access.token,
        refresh_token: response.data.data.tokens.refresh.token,
        user_id: response?.data?.data?._id,
      };
      LocalStorageService.setToken(tokenObject);
      instance.defaults.headers.common['Authorization'] =
        'Bearer ' + LocalStorageService.getAccessToken();
      instance.defaults.headers.common['Accept-Language'] = getLang();
      instance.defaults.headers.user_id = LocalStorageService.getUserId();
      instance.defaults.headers._user_id = LocalStorageService.getUserId();
      return response;
    } else if (
      response.status === 200 &&
      response.data.data !== null &&
      [`/user/authLoggedIn/${LocalStorageService.getUserId()}`].includes(originalRequest.url)
    ) {
      const roles = response?.data?.data?.roles;
      const role_id = getRoleId(roles);
      LocalStorageService.setCustomToken('role_id', role_id);
      instance.defaults.headers.role_id = LocalStorageService.getCustomToken('role_id');
      return response;
    } else if (
      response.status === 200 &&
      response.data.data !== null &&
      ['/user/signup'].includes(originalRequest.url)
    ) {
      const institutionId = response?.data?.data?.services?.REACT_APP_INSTITUTION_ID;
      //const userId = response?.data?.data?._id;
      if (institutionId !== undefined) {
        LocalStorageService.setCustomCookie('_institution_id', institutionId);
        instance.defaults.headers._institution_id = institutionId;
        instance.defaults.headers.user_id = LocalStorageService.getUserId();
        instance.defaults.headers._user_id = LocalStorageService.getUserId();
      }
    } else if (originalRequest.url === '/user/forget_send_otp') {
      const accessToken = response.data.data.token.access.token;
      LocalStorageService.setCustomCookie('forgot_password_access_token', accessToken);
      LocalStorageService.setCustomToken('userId', response.data.data._id);
    }
    return response;
  },
  async function (error) {
    const originalRequest = error.config;

    if (
      error.response.status !== 401 &&
      error?.response?.data?.data &&
      checkEncryptedRoute(originalRequest, 'RESPONSE')
    )
      error.response.data = await decryptPayload(error.response.data.data);

    if (originalRequest.url === '/user/forget_send_otp' && error.response.status === 401)
      return Promise.reject(error);

    if (error.response.status !== undefined && error.response.status === 401 && check) {
      //originalRequest.url === '/user/refresh_token'
      alert('Your session has timed out. Please login again.');
      LocalStorageService.clearToken();
      history.push('/login');
      window.location.reload(true);
      return Promise.reject(error);
    }

    if (error.response.status !== undefined && error.response.status === 429) {
      alert('The system has received too many requests. Please try again after a few minutes.');
      LocalStorageService.clearToken();
      history.push('/login');
      window.location.reload(true);
      return Promise.reject(error);
    }

    if (error.response.status !== undefined && error.response.status === 401 && !check) {
      originalRequest._retry = true;
      const refreshToken = LocalStorageService.getRefreshToken();
      const userId = error?.config?.headers?.user_id;
      const roleId = error?.config?.headers?.role_id;
      const _institution_id = error?.config?.headers?._institution_id;
      return axios
        .post(
          URL + '/user/refresh_token',
          {},
          {
            headers: {
              Authorization: 'Bearer ' + refreshToken,
              _institution_id: _institution_id,
              user_id: userId,
              _user_id: userId,
              role_id: roleId,
            },
          }
        )
        .then((res) => {
          if (res.status === 200) {
            const tokenObject = {
              access_token: res.data.data.tokens.access.token,
              refresh_token: res.data.data.tokens.refresh.token,
              user_id: res?.data?.data?._id,
            };
            LocalStorageService.setToken(tokenObject);
            originalRequest.headers['Authorization'] = 'Bearer ' + tokenObject.access_token;
            originalRequest.headers['Accept-Language'] = getLang();
            return axios(originalRequest);
          }
        })
        .catch((error) => {
          check = true;
          if (!alertShown) {
            alert('Your session has timed out. Please login again.');
            LocalStorageService.clearToken();
            history.push('/login');
            window.location.reload(true);
            alertShown = true;
          }
        });
    }
    return Promise.reject(error);
  }
);

function getRoleId(roles) {
  if (roles !== undefined && roles.length > 0) {
    const selectedRole = roles.find((item) => item.isDefault === true);
    if (selectedRole !== undefined) {
      return selectedRole?._role_id?._id;
    } else if (roles.length === 1) {
      return roles[0]._id;
    }
  }
  return '';
}

export default instance;
