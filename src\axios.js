import Config from './config.js';
import axios from 'axios';
import { createBrowserHistory } from 'history';

import LocalStorageService from './LocalStorageService';
import { getLang } from 'utils';
import { decryptData, encryptData, checkEncryptedRoute } from './encryption.js';

const URL = Config.API_URL;
const forgotPasswordTokenUrls = ['/user/otp_verify', '/user/forget_set_password'];
const instance = axios.create({
  baseURL: URL,
});
const history = createBrowserHistory();

instance.defaults.headers.common['Access-Control-Allow-Origin'] = '*';
instance.defaults.headers.common['Cache-Control'] = 'no-cache';
instance.defaults.headers.common['Pragma'] = 'no-cache';
instance.defaults.headers.common['Expires'] = '0';

instance.interceptors.request.use(
  (config) => {
    let token = LocalStorageService.getAccessToken();
    let userId = LocalStorageService.getUserId();
    const forgotPasswordToken = LocalStorageService.getCustomCookie('forgot_password_access_token');

    if (forgotPasswordTokenUrls.includes(config.url) && forgotPasswordToken) {
      token = forgotPasswordToken;
      userId = LocalStorageService.getCustomToken('userId');
    }

    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    const institutionId = LocalStorageService.getCustomCookie('_institution_id');
    // if (institutionId && config.headers._parent_id === undefined) {
    if (institutionId !== '' && institutionId !== null) {
      config.headers['_institution_id'] = institutionId;
    }
    config.headers['role_id'] = LocalStorageService.getCustomToken('role_id');
    config.headers['user_id'] = userId;
    config.headers['_user_id'] = userId;
    config.headers['Accept-Language'] = getLang();

    // Encrypt request payload if route matches
    if (config.data && checkEncryptedRoute(config))
      config.data = encryptData({ content: config.data, handledBy: 'CLIENT' });

    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);
var check = false;
let alertShown = false;
instance.interceptors.response.use(
  (response) => {
    const originalRequest = response.config;

    // Decrypt response if route matches
    if (response?.data?.data && checkEncryptedRoute(originalRequest, 'RESPONSE'))
      response.data = decryptData({ content: response?.data?.data, handledBy: 'CLIENT' });

    if (
      response.status === 200 &&
      response.data.data !== null &&
      [
        '/user/otp_verify',
        '/users/authLogin',
        '/digiclass/user/authLogin',
        '/user/signup',
        '/digiclass/user/authSSOLogin',
      ].includes(originalRequest.url)
    ) {
      const institutionId = response?.data?.data?.services?.REACT_APP_INSTITUTION_ID;
      if (
        originalRequest.url.includes(`/digiclass/user/authLogin`, '/digiclass/user/authSSOLogin') &&
        institutionId !== undefined
      ) {
        LocalStorageService.setCustomCookie('_institution_id', institutionId);
        LocalStorageService.setCustomCookie('landed', 'false');
        LocalStorageService.setCustomCookie('site-origin', window.location.origin);

        let updatedData = response.data.data;
        delete updatedData?.services?.ANOMALY_RESTRICT;
        delete updatedData?.services?.ANOMALY_RESTRICT_MESSAGE;
        delete updatedData?.services?.ACTIVITY_MANAGEMENT;
        delete updatedData?.services?.ANNOUNCEMENT;
        delete updatedData?.services?.ASSIGNMENT_MOBILE;
        delete updatedData?.services?.ASSIGNMENT_MODULE;
        delete updatedData?.services?.ATTENDANCE_OR_CONDITION;
        delete updatedData?.services?.BLE_RANGE;
        delete updatedData?.services?.CHAT_ENABLED;
        delete updatedData?.services?.COURSE_RESTRICTION;
        delete updatedData?.services?.DC_MISSED_TO_COMPLETE;
        delete updatedData?.services?.DC_STAFF_OFFLINE;
        delete updatedData?.services?.DEFAULT_ATTENDANCE_CONDITION;
        delete updatedData?.services?.DIGI_CHAT;
        delete updatedData?.services?.DISCUSSION_FORUM;
        delete updatedData?.services?.DOCUMENT_MANAGEMENT;
        delete updatedData?.services?.FACE_AUTH_TYPE;
        delete updatedData?.services?.FACE_OFF;
        delete updatedData?.services?.FACE_VERIFY_MODE;
        delete updatedData?.services?.HEBA_ENGAGER;
        delete updatedData?.services?.LEADER_BOARD;
        delete updatedData?.services?.LOCAL_FACE_TEST;
        delete updatedData?.services?.MULTI_CALENDAR;
        delete updatedData?.services?.MULTI_DOCUMENT;
        delete updatedData?.services?.MULTI_SCHEDULE_START;
        delete updatedData?.services?.NATIVE_FACIAL;
        delete updatedData?.services?.ONSITE_WEB_START;
        delete updatedData?.services?.SAAM_MOBILE_APPLY;
        delete updatedData?.services?.SCHEDULED_V2;
        delete updatedData?.services?.STAFF_STUDENT_MANUAL_ATTENDANCE;
        delete updatedData?.services?.STUDENT_FACE_REGISTER;
        delete updatedData?.services?.SURVEY_OPEN_ENDED;
        delete updatedData?.services?.TIME_STAMP;

        const { tokens, ...otherData } = updatedData; //eslint-disable-line

        LocalStorageService.setCustomCookie('authData', otherData, true);
        instance.defaults.headers._institution_id = institutionId;
      }
      const tokenObject = {
        access_token: response.data.data.tokens.access.token,
        refresh_token: response.data.data.tokens.refresh.token,
        user_id: response?.data?.data?._id,
      };
      LocalStorageService.setToken(tokenObject);
      instance.defaults.headers.common['Authorization'] =
        'Bearer ' + LocalStorageService.getAccessToken();
      instance.defaults.headers.common['Accept-Language'] = getLang();
      instance.defaults.headers.user_id = LocalStorageService.getUserId();
      instance.defaults.headers._user_id = LocalStorageService.getUserId();
      return response;
    } else if (
      response.status === 200 &&
      response.data.data !== null &&
      [`/user/authLoggedIn/${LocalStorageService.getUserId()}`].includes(originalRequest.url)
    ) {
      const roles = response?.data?.data?.roles;
      const role_id = getRoleId(roles);
      LocalStorageService.setCustomToken('role_id', role_id);
      instance.defaults.headers.role_id = LocalStorageService.getCustomToken('role_id');
      return response;
    } else if (
      response.status === 200 &&
      response.data.data !== null &&
      ['/user/signup'].includes(originalRequest.url)
    ) {
      const institutionId = response?.data?.data?.services?.REACT_APP_INSTITUTION_ID;
      //const userId = response?.data?.data?._id;
      if (institutionId !== undefined) {
        LocalStorageService.setCustomCookie('_institution_id', institutionId);
        instance.defaults.headers._institution_id = institutionId;
        instance.defaults.headers.user_id = LocalStorageService.getUserId();
        instance.defaults.headers._user_id = LocalStorageService.getUserId();
      }
    } else if (originalRequest.url === '/user/forget_send_otp') {
      const accessToken = response.data.data.token.access.token;
      LocalStorageService.setCustomCookie('forgot_password_access_token', accessToken);
      LocalStorageService.setCustomToken('userId', response.data.data._id);
    }
    return response;
  },
  function (error) {
    const originalRequest = error.config;

    if (error?.response?.data?.data && checkEncryptedRoute(originalRequest, 'RESPONSE'))
      error.response.data = decryptData({
        content: error.response?.data?.data,
        handledBy: 'CLIENT',
      });

    if (originalRequest.url === '/user/forget_send_otp' && error.response.status === 401)
      return Promise.reject(error);

    if (error.response.status !== undefined && error.response.status === 401 && check) {
      //originalRequest.url === '/user/refresh_token'
      alert('Your session has timed out. Please login again.');
      LocalStorageService.clearToken();
      history.push('/login');
      window.location.reload(true);
      return Promise.reject(error);
    }

    if (error.response.status !== undefined && error.response.status === 401 && !check) {
      originalRequest._retry = true;
      const refreshToken = LocalStorageService.getRefreshToken();
      return axios
        .post(
          URL + '/user/refresh_token',
          {},
          {
            headers: {
              Authorization: 'Bearer ' + refreshToken,
            },
          }
        )
        .then((res) => {
          if (res.status === 200) {
            const tokenObject = {
              access_token: res.data.data.tokens.access.token,
              refresh_token: res.data.data.tokens.refresh.token,
              user_id: res?.data?.data?._id,
            };
            LocalStorageService.setToken(tokenObject);
            originalRequest.headers['Authorization'] = 'Bearer ' + tokenObject.access_token;
            originalRequest.headers['Accept-Language'] = getLang();
            return axios(originalRequest);
          }
        })
        .catch((error) => {
          check = true;
          if (!alertShown) {
            alert('Your session has timed out. Please login again.');
            LocalStorageService.clearToken();
            history.push('/login');
            window.location.reload(true);
            alertShown = true;
          }
        });
    }
    return Promise.reject(error);
  }
);

function getRoleId(roles) {
  if (roles !== undefined && roles.length > 0) {
    const selectedRole = roles.find((item) => item.isDefault === true);
    if (selectedRole !== undefined) {
      return selectedRole?._role_id?._id;
    } else if (roles.length === 1) {
      return roles[0]._id;
    }
  }
  return '';
}

export default instance;
