import React from 'react';
import { <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import { List } from 'immutable';
import DatePicker from 'react-datepicker';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import PropTypes from 'prop-types';

import Input from '../../../Widgets/FormElements/Input/Input';
import { GENDER_LIST, DAYS_LIST } from '../components/Shared/Constants';
import { allowedTimeInterval, formatDayName, jsUcfirst, setDate } from '../../../utils';

export default function AddEditExtraCurricularAndTiming({
  show,
  onHide,
  type,
  data,
  onChange,
  onSave,
}) {
  return (
    <Modal show={show} onHide={onHide} dialogClassName="modal-720" centered>
      <Modal.Body>
        <div className="container-fluid pt-2 pb-1 ">
          <div className="border-bottom">
            <p className="mb-2 font-weight-bold">
              {' '}
              {type === 'extra_curricular'
                ? t('course_schedule.extra_curricular')
                : t('course_schedule.break')}{' '}
            </p>
          </div>
          <div className="row mb-2 pt-3">
            <div className="col-md-6 mb-3 ">
              <Input
                elementType={'input'}
                elementConfig={{
                  type: 'text',
                }}
                value={data.get('title', '')}
                placeholder={''}
                changed={(e) => onChange('title', jsUcfirst(e.target.value))}
                className={'form-control'}
                label={t('title')}
                labelclass={'mb-1 text-uppercase'}
              />
            </div>

            <div className="col-md-6 mb-3 ">
              <Input
                elementType={'select'}
                elementConfig={{
                  options: GENDER_LIST,
                }}
                value={data.get('gender', '')}
                changed={(e) => onChange('gender', e.target.value)}
                className={''}
                label={t('gender')}
                labelclass={'mb-1 text-uppercase'}
              />
            </div>

            <div className="col-md-12">
              <p className="mb-1 mt-2 text-uppercase">
                <Trans i18nKey={'course_schedule.mode'}></Trans>
              </p>

              <Form.Check
                custom
                inline
                type="radio"
                label={t('course_schedule.daily')}
                name="formHorizontalRadios"
                id="formHorizontalRadios4"
                className="mt-1 ml-2 mr-5"
                checked={data.get('mode', '') === 'daily'}
                onChange={(e) => onChange('mode', 'daily')}
              />

              <Form.Check
                custom
                inline
                type="radio"
                label={t('course_schedule.weekly')}
                name="formHorizontalRadios"
                id="formHorizontalRadios3"
                className="mt-1 ml-2"
                checked={data.get('mode', '') === 'weekly'}
                onChange={(e) => onChange('mode', 'weekly')}
              />
              {data.get('mode', '') !== '' && (
                <div className="col-md-12 pl-0 pt-3 pb-3">
                  <div className="row">
                    {DAYS_LIST.map((days) => {
                      return (
                        <div
                          className="col remove_hover"
                          key={days}
                          style={{ flexGrow: 0, paddingRight: '0px' }}
                        >
                          <div
                            className="bg-check active mb-2"
                            onClick={(e) => onChange('days', days)}
                          >
                            <Form.Check
                              type="checkbox"
                              label={formatDayName(days)}
                              checked={data.get('days', List()).includes(days)}
                              className="mr-2 ml-2 remove_hover"
                              onChange={(e) => onChange('days', days)}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            <div className="col-md-8 mb-3">
              <p className="mb-1 mt-2 text-uppercase">
                <Trans i18nKey={'course_schedule.set_time'}></Trans>
              </p>
              <div className="d-flex justify-content-between">
                <div className="w-75">
                  <DatePicker
                    selected={
                      data.get('startTime', '') !== '' ? data.get('startTime', '') : setDate(8)
                    }
                    onChange={(date) => onChange('startTime', date)}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={allowedTimeInterval()}
                    timeCaption="Time"
                    dateFormat="h:mm aa"
                    className="form-control"
                  />
                </div>
                <div className="w-25">
                  <p className="mb-0 mt-2 text-center"></p>
                </div>
                <div className="w-75">
                  <DatePicker
                    selected={
                      data.get('endTime', '') !== '' ? data.get('endTime', '') : setDate(17)
                    }
                    onChange={(date) => onChange('endTime', date)}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={allowedTimeInterval()}
                    timeCaption="Time"
                    dateFormat="h:mm aa"
                    className="form-control"
                  />
                </div>
              </div>
            </div>
            {type === 'extra_curricular' && (
              <div className="col-md-12 pt-3 ">
                <div className="row">
                  <div className="col-md-1 pr-0">
                    <Form.Check
                      type="checkbox"
                      label=""
                      className="pl-4"
                      checked={data.get('allowCourseCoordinatesToEdit', false)}
                      onChange={(e) =>
                        onChange(
                          'allowCourseCoordinatesToEdit',
                          !data.get('allowCourseCoordinatesToEdit', false)
                        )
                      }
                    />
                  </div>

                  <div className="col-md-11 pl-0">
                    <p className="bold mb-0">
                      <Trans i18nKey={'course_schedule.allow_course_coordinators_to_edit'}></Trans>
                    </p>
                    <span className="f-14">
                      {' '}
                      <Trans
                        i18nKey={'course_schedule.allow_course_coordinators_to_schedule'}
                      ></Trans>
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <div className="pr-2">
          <Button variant="outline-primary" onClick={onHide}>
            <Trans i18nKey={'cancel'}></Trans>
          </Button>
        </div>
        <div className="pr-2">
          <Button variant="primary" onClick={onSave}>
            <Trans i18nKey={'save'}></Trans>
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
}

AddEditExtraCurricularAndTiming.propTypes = {
  onHide: PropTypes.func,
  type: PropTypes.string,
  data: PropTypes.object,
  onChange: PropTypes.func,
  onSave: PropTypes.func,
  show: PropTypes.bool,
};
