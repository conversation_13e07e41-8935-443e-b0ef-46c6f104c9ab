import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getCategoryForms, setData } from '_reduxapi/global_configuration/v1/actions';
import { List, Map, fromJS } from 'immutable';
import {
  getProgramDetails,
  getProgramListForQAPC,
  getConfigureTemplate,
} from '_reduxapi/global_configuration/v1/actions';
import {
  selectProgramDetails,
  selectProgramList,
  selectProgramCount,
  selectConfigureTemplate,
  selectCategoryForm,
} from '_reduxapi/global_configuration/v1/selectors';
// import { useSearchParams } from './QapcConfiguration';

export const useManipulateDataForTags = (existData, handleClose, setTagData, editIndex) => {
  const [tags, setTags] = useState(
    fromJS([
      Map({
        name: existData.get('name', ''),
        isDefault: existData.get('isDefault', false),
        level: existData.get('level', ''),
        isActive: true,
        isEdited: true,
        ...(existData.get('_id', '') && {
          _id: existData.get('_id', ''),
        }),
      }),
    ])
  );

  const dispatch = useDispatch();

  const setMessage = (message) => {
    dispatch(setData({ message }));
  };

  const handleValidate = () => {
    for (const [index, tag] of tags.entries()) {
      if (tag.get('name').trim() === '') {
        return setMessage('tag name required at tag' + (index + 1));
      }
      if (tag.get('level') === '') {
        return setMessage('select the level at tag' + (index + 1));
      }
    }
    if (editIndex !== undefined) {
      setTagData((prev) => prev.set(editIndex, tags.get(0, Map())));
      return handleClose();
    }
    setTagData((prev) => prev.merge(tags));
    handleClose();
  };
  const handleDelete = (index) => {
    setTags((prev) => prev.filter((_, i) => i !== index));
  };
  return [tags, setTags, handleValidate, handleDelete];
};

export const useDispatchAndSelectorFunctionsQlc = () => {
  const dispatch = useDispatch();
  const fetchProgramList = (data) => {
    dispatch(getProgramListForQAPC(data));
  };
  const fetchProgramDetails = (data, cb) => {
    dispatch(getProgramDetails(data));
  };
  const programList = useSelector(selectProgramList);
  const programDetails = useSelector(selectProgramDetails);
  const programCount = useSelector(selectProgramCount);
  const configureTemplate = useSelector(selectConfigureTemplate);
  // const [searchParams] = useSearchParams();
  // const currentCategoryIndex = Number(searchParams.get('currentCategoryIndex') ?? 0);
  const setMessage = (message) => {
    dispatch(setData({ message }));
  };
  return {
    dispatch,
    fetchProgramList,
    fetchProgramDetails,
    programList,
    programDetails,
    programCount,
    configureTemplate,
    // currentCategoryIndex,
    setMessage,
  };
};

export const useConfigureTemplate = () => {
  const configureTemplate = useSelector(selectConfigureTemplate);
  const dispatch = useDispatch();
  useEffect(() => {
    if (configureTemplate.size) return;
    dispatch(getConfigureTemplate());
  }, []);
  return [configureTemplate];
};

export const useCategoryForms = (categoryId) => {
  const dispatch = useDispatch();
  const [state, setState] = useState(Map());
  const categoryForms = useSelector(selectCategoryForm);
  useEffect(() => {
    if (categoryId)
      if (!state.has(categoryId))
        dispatch(
          getCategoryForms({
            params: {
              categoryId,
            },
          })
        );
  }, [categoryId]);
  useEffect(() => {
    if (categoryId) setState((prev) => prev.set(categoryId, constructRequest(categoryForms)));
  }, [categoryForms]);
  return [state, dispatch];
};

function constructRequest(allForms) {
  let allOptions = Map();
  let formOptions = List();
  allForms.forEach((form) => {
    // forms.forEach(form=>{
    const formId = form.get('_id', '');
    formOptions = formOptions.push(
      Map({
        name: form.get('name', ''),
        _id: formId,
      })
    );
    form.get('formOccurrence', List()).forEach((allProgram, programIndex) => {
      allProgram.get('curriculum', List()).forEach((curriculum, curriculumIndex) => {
        curriculum.get('years', List()).forEach((years, yearIndex) => {
          years.get('courses', List()).forEach((course, courseIndex) => {
            allOptions = allOptions.updateIn([formId, 'courseOptions'], List(), (options) =>
              options.push(
                course
                  .set('program_name', allProgram.get('program_name', ''))
                  .set('_program_id', allProgram.get('_program_id', ''))
                  .set('programIndex', programIndex)
                  .set('curriculumIndex', curriculumIndex)
                  .set('curriculum_name', curriculum.get('curriculum_name', ''))
                  .set('yearIndex', yearIndex)
                  .set('courseIndex', courseIndex)
              )
            );
            const firstAttempt = course.getIn(['occurrences', 'attemptType', 0, 'typeName'], '');
            if (firstAttempt === '' || firstAttempt === 'none') {
              allOptions = allOptions.setIn(
                [formId, course.get('_id', ''), 'attempt'],
                firstAttempt
              );
            }
            course.getIn(['occurrences', 'attemptType'], List()).forEach((attemptType) => {
              const attemptTypeId = attemptType.get('_id', '');
              allOptions = allOptions.updateIn(
                [formId, course.get('_id', ''), 'attemptTypeOptions'],
                List(),
                (data) =>
                  data.push(
                    Map({
                      name: attemptType.get('typeName', ''),
                      value: attemptTypeId,
                    })
                  )
              );
              const groupType = attemptType.get('group', '');

              attemptType.get('groupType', List()).forEach((group) => {
                allOptions = allOptions.updateIn(
                  [formId, course.get('_id', ''), attemptTypeId, 'groupOptions'],
                  List(),
                  (data) => data.push(group)
                );
              });
              const academicYear = attemptType.get('academicYear', '');
              allOptions = allOptions.setIn(
                [formId, course.get('_id', ''), attemptTypeId, 'academicYearOptions'],
                academicYear
              );
              allOptions = allOptions.setIn(
                [formId, course.get('_id', ''), attemptTypeId, 'groupType'],
                groupType
              );
            });
          });
        });
      });
    });
    // })
  });
  return allOptions.set('formOptions', formOptions);
}

export const numbers = [
  {
    name: 1,
    value: 1,
  },
  {
    name: 2,
    value: 2,
  },
  {
    name: 3,
    value: 3,
  },
  {
    name: 4,
    value: 4,
  },
  {
    name: 5,
    value: 5,
  },
  {
    name: 6,
    value: 6,
  },
  {
    name: 7,
    value: 7,
  },
  {
    name: 8,
    value: 8,
  },
  {
    name: 9,
    value: 9,
  },
  {
    name: 10,
    value: 10,
  },
];
export const monthNames = [
  { name: 'January', value: 'January' },
  { name: 'February', value: 'February' },
  { name: 'March', value: 'March' },
  { name: 'April', value: 'April' },
  { name: 'May', value: 'May' },
  { name: 'June', value: 'June' },
  { name: 'July', value: 'July' },
  { name: 'August', value: 'August' },
  { name: 'September', value: 'September' },
  { name: 'October', value: 'October' },
  { name: 'November', value: 'November' },
  { name: 'December', value: 'December' },
];

export const numberMonth = {
  1: 'January',
  2: 'February',
  3: 'March',
  4: 'April',
  5: 'May',
  6: 'June',
  7: 'July',
  8: 'August',
  9: 'September',
  10: 'October',
  11: 'November',
  12: 'December',
};

export const monthName = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const validateForm = (
  modalState,
  setData,
  dispatch,
  level,
  constructedAttemptTypes,
  actions
) => {
  let errorMessage = '';

  const validateField = (key, errorMessage) =>
    modalState.get('attemptTypes', List()).some((s) => s.get(key, '') === 'none') && errorMessage;

  const validateList = (list, errorMessage) => list.size === 0 && errorMessage;

  const validateAttemptTypes = (field, errorMessage) =>
    constructedAttemptTypes.some((attempt) => !attempt.get(field, '')) && errorMessage;

  const institutionLevelValidation = () =>
    validateAttemptTypes('startMonth', 'Select Start Month') ||
    validateAttemptTypes('endMonth', 'Select End Month');

  const otherLevelValidation = () =>
    (level === 'course' &&
      actions.studentGroups &&
      validateList(modalState.get('studentGroups', List()), 'Select Student Groups')) ||
    (actions.academicTerms && validateField('termName', 'Select Term')) ||
    (actions.attemptType && validateField('typeName', 'Select Attempt Type')) ||
    validateAttemptTypes('minimum', 'Select Minimum Times') ||
    validateAttemptTypes('startMonth', 'Select Start Month') ||
    validateAttemptTypes('endMonth', 'Select End Month');

  errorMessage = level === 'institution' ? institutionLevelValidation() : otherLevelValidation();

  if (errorMessage) {
    dispatch(setData(Map({ message: errorMessage })));
    return false;
  }

  return true;
};
