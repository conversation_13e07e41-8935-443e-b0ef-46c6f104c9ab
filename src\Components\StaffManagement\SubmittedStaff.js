/* eslint-disable react/jsx-key */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Table } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

import Loader from '../../Widgets/Loader/Loader';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import Search from '../../Widgets/Search/Search';
import Pagination from '../StaffManagement/Pagination';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import * as actions from '../../_reduxapi/user_management/action';
import Export from '../../Containers/StudentRegistrationContainer/Export';

class SubmittedStaff extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      searchText: '',
      limit: 10,
      totalPages: 1,
      pageLength: 1,
    };
    this.timeout = 0;
  }

  componentDidMount() {
    this.fetchApi({});
  }

  successTrigger = (res) => {
    const data = res.data.data.map((data) => {
      return {
        id: data._id,
        username: data.username,
        employee_id: data.employee_id,
        email: data.email,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        gender: data.gender,
        nationality_id: data.address.nationality_id,
        isChecked: false,
      };
    });
    this.setState({
      data: data,
      isLoading: false,
      totalPages: res.data.totalPages,
    });
  };

  fetchApi = ({ limit = 10, pageLength = 1 }) => {
    const { getUserManagementList } = this.props;
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });
    getUserManagementList(
      {
        searchText: searchText,
        type: 'staff',
        status: 'submitted',
        limit: limit,
        pageLength: pageLength,
        slug: 'get_all',
      },
      this.successTrigger,
      () => {
        this.setState({
          data: [],
          isLoading: false,
          totalPages: 1,
          pageLength: 1,
        });
      }
    );
  };

  handleClickView = (data) => {
    this.props.history.push({
      pathname: '/submitted/staff/profile',
      search: '?id=' + data.id,
      state: this.props.selectedTab,
    });
  };

  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      const { limit } = this.state;
      setTimeout(() => {
        this.fetchApi({ limit });
        this.setState({ pageLength: 1 });
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState({ searchText: e.target.value }, () => this.doSearch(e));
  };

  pagination = (e) => {
    this.setState(
      {
        limit: e.target.value,
        pageLength: 1,
      },
      () => {
        setTimeout(() => {
          this.fetchApi({});
        }, 500);
      }
    );
  };

  pageCount = (value) => {
    this.setState(
      {
        pageLength: value,
      },
      () => {
        this.fetchApi({ pageLength: value, limit: this.state.limit });
      }
    );
  };

  render() {
    const header = [
      <Trans i18nKey={'employee_id'}></Trans>,
      <Trans i18nKey={'email'}></Trans>,
      <Trans i18nKey={'first_name'}></Trans>,
      'Middle Name',
      'Last Name',
      <Trans i18nKey={'gender'}></Trans>,
      <Trans i18nKey={'national_id'}></Trans>,
    ];

    return (
      <div className="main bg-gray pt-2 pb-5">
        <Loader isLoading={this.state.isLoading} />
        <div className="container-fluid">
          <div className="float-right p-2 d-flex">
            <Export userType="staff" status="submitted" />
            {CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              'Submitted',
              'Search'
            ) && <Search value={this.state.searchText} onChange={(e) => this.handleSearch(e)} />}
          </div>
          <div className="">
            <div className="clearfix"> </div>
            <React.Fragment>
              <div className="p-3">
                <div className="dash-table">
                  <Table responsive hover>
                    <thead className="th-change">
                      <tr>
                        {header.map((header) => (
                          <th>{header}</th>
                        ))}
                      </tr>
                    </thead>
                    {this.state.data.length === 0 ? (
                      <TableEmptyMessage />
                    ) : (
                      <tbody>
                        {this.state.data.map((data, index) => (
                          <tr className="tr-change" key={index}>
                            <td
                              className={
                                CheckPermission(
                                  'subTabs',
                                  'User Management',
                                  'Staff Management',
                                  '',
                                  'Registration Pending',
                                  '',
                                  'Submitted',
                                  'Profile View'
                                )
                                  ? 'profile_view'
                                  : ''
                              }
                              onClick={
                                CheckPermission(
                                  'subTabs',
                                  'User Management',
                                  'Staff Management',
                                  '',
                                  'Registration Pending',
                                  '',
                                  'Submitted',
                                  'Profile View'
                                )
                                  ? (e) => this.handleClickView(data)
                                  : () => {}
                              }
                            >
                              {data.employee_id}
                            </td>
                            <td>{data.email}</td>
                            <td>{data.first}</td>
                            <td>{data.middle}</td>
                            <td>{data.last}</td>
                            <td>{data.gender}</td>
                            <td>{data.nationality_id}</td>
                          </tr>
                        ))}
                      </tbody>
                    )}
                  </Table>
                </div>
                <Pagination
                  totalPages={this.state.totalPages}
                  switchPagination={this.pagination}
                  switchPageCount={this.pageCount}
                  pageLength={this.state.pageLength}
                />
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  }
}

SubmittedStaff.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
  selectedTab: PropTypes.number,
};

export default connect(null, actions)(withRouter(SubmittedStaff));
