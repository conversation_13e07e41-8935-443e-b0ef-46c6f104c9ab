import React, { Component } from 'react';
import DatePicker from 'react-datepicker';
import { Button, Modal, Badge, Table } from 'react-bootstrap';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import moment from 'moment';
import { t } from 'i18next';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Map, List } from 'immutable';

import axios from '../../../axios';
import AddNew from '../../../Widgets/Button/AddNew';
import Loader from '../../../Widgets/Loader/Loader';
import '../ReviewEvent/review.css';
import MainTitle from '../../../Widgets/MainTitle';
import TableEmptyMessage from '../../../Widgets/CustomMessage/TableEmptyMessage';
import Input from '../../../Widgets/FormElements/Input/Input';
import Breadcrumb from '../../../Widgets/Breadcrumb/Breadcrumb';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import {
  formatFullName,
  getEnvCollegeName,
  getURLParams,
  isMobileVerifyMandatory,
  showArabicMailShow,
} from '../../../utils';
import {
  selectActiveInstitutionCalendar,
  selectUserId,
  selectUserInfo,
} from '../../../_reduxapi/Common/Selectors';
import CalenderIndx from '../index';
import * as actions from '../../../_reduxapi/institution/actions';

import { getLang } from 'utils';
import withCalendarHooks from 'Hoc/withCalendarHooks';
const lang = getLang();
const agree = [
  ['Agree', 'agree'],
  ['Disagree', 'disagree'],
];

class ReviewAccept extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      staffData: [],
      selectStaffList: [],
      review: [],
      eventStatus: [],
      selectAgree: agree[0][1][2],
      chooseAgree: '',
      eventName: '',
      eventDetail: '',
      arab1: '',
      arab2: '',
      selectType: '',
      startDate: '',
      endDate: '',
      minutes: '',
      hours: '',
      days: '',
      feed: '',
      eventType: [
        { name: '', value: '' },
        { name: t('events.event_types.holiday'), value: 'holiday' },
        { name: t('events.event_types.exam'), value: 'exam' },
        { name: t('events.event_types.training'), value: 'training' },
        { name: t('events.event_types.orientation'), value: 'orientation' },
        { name: t('events.event_types.general'), value: 'general' },
      ],
      venue: [
        { name: '', value: '' },
        { name: 'Madurai', value: '5ea93af58d49c51140fcf63e' },
        { name: 'Chennai', value: '5ea93af58d49c51140fcf64e' },
      ],
      selectVenue: '',
      eventId: '',
      skip: false,
      eventView: false,
      eventEdit: false,
      searchView: false,
      requestView: false,
      isLoading: false,
      reviewEndDate: '',
      reviewEndTime: new Date(),
      evId: '',
      evType: '',
      evName: '',
      evArab1: '',
      evDetail: '',
      evArab2: '',
      evStartDate: '',
      evEndDate: '',
      evStartTime: '',
      evEndTime: '',
      completeEmail: '',
      completeSms: '',
      completeScheduler: '',
      completeClass: '',
      completeError: '',
      dean_comment: '',
      dean_feedback: '',
      expireTime: 0,
      calendarId: getURLParams('id', true),
      calendarName: getURLParams('name', true),
      isActive: getURLParams('s', true) === 'true',
    };
  }

  componentDidMount() {
    this.fetchApi();
    // this.interval = setInterval(() => {
    //   const { activeInstitutionCalendar } = this.props;
    //   if (!activeInstitutionCalendar.isEmpty()) {
    //     this.fetchApi();
    //     clearInterval(this.interval);
    //   }
    // }, 500);
  }

  // componentDidUpdate(prevProps) {
  //   const { activeInstitutionCalendar } = this.props;
  //   if (activeInstitutionCalendar !== prevProps.activeInstitutionCalendar) {
  //     this.fetchApi();
  //   }
  // }

  // componentWillUnmount() {
  //   clearInterval(this.interval);
  // }

  fetchApi = () => {
    const { calendarId } = this.state;
    const { loggedInUserData } = this.props;
    const id = calendarId;
    this.setState({
      isLoading: true,
    });

    let url = `institution_calendar_event/list_calendar/${id}?limit=500&pageNo=1`;
    const subRole = loggedInUserData.get('sub_role', List());
    if (
      CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Add Event') ||
      (subRole && subRole.size > 0 && subRole.indexOf('Institution_Calendar_Reviewer') !== -1)
    ) {
      url = `institution_calendar_event/list_event/${id}`;
    }
    axios
      .get(url)
      .then((res) => {
        let data = [];
        if (res.data.data && res.data.data.length > 0) {
          data = res.data.data.map((r) => {
            return {
              _id: r._id,
              first_language: r.event_name.first_language,
              event_date: r.event_date,
              reviewEvent: r.review.map((rs) => {
                return {
                  reviewerId: rs._reviewer_ids,
                  reviews: rs.status,
                };
              }),
            };
          });
        }

        let view = {
          _id: data[0]._id,
        };
        this.eventViewShow('', view);

        this.setState({
          data: data,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.props.setData({ message: error.response.data.message });
        this.setState({
          isLoading: false,
        });
      });
  };

  handleCheck = (event, name) => {
    if (name === 'day') {
      this.setState({
        oneDay: event.target.checked,
        endDate: this.state.startDate,
      });
    }
    if (name === 'value') {
      this.setState({
        valueCheck: event.target.checked,
      });
    }
    if (name === 'skip') {
      this.setState({
        skip: event.target.checked,
      });
    }
  };

  handleChange = (e, name) => {
    if (name === 'eventName') {
      this.setState({
        eventName: e.target.value,
        eventNameError: '',
      });
    }

    if (name === 'eventDetail') {
      this.setState({
        eventDetail: e.target.value,
        eventDetailError: '',
      });
    }
    if (name === 'arab1') {
      this.setState({
        arab1: e.target.value,
        arab1Error: '',
      });
    }
    if (name === 'arab2') {
      this.setState({
        arab2: e.target.value,
        arab2Error: '',
      });
    }
    if (name === 'startDate') {
      this.setState({
        startDate: e.target.value,
        startDateError: '',
      });
    }
    if (name === 'endDate') {
      this.setState({
        endDate: e.target.value,
        endDateError: '',
      });
    }

    if (name === 'startTime') {
      this.setState({
        startTime: e.target.value,
        startTimeError: '',
      });
    }
    if (name === 'reviewEndDate') {
      this.setState({
        reviewEndDate: e.target.value,
        reviewEndDateError: '',
      });
    }

    if (name === 'feed') {
      this.setState({
        feed: e.target.value,
      });
    }
  };

  handleSelect = (e, name) => {
    if (name === 'eventType') {
      this.setState({
        selectType: e.target.value,
        selectTypeError: '',
      });
    }
    if (name === 'venue') {
      this.setState({
        selectVenue: e.target.value,
        selectVenueError: '',
      });
    }
  };

  eventViewShow = (e, view) => {
    this.setState({
      eventView: true,
      isLoading: true,
      eventId: view._id,
    });
    const { userId } = this.props;
    axios
      .get(`institution_calendar_event/list_id_reviewer/${view._id}`)
      .then((res) => {
        const eventSingleView = res.data.data;
        const review = eventSingleView.review.map((re) => {
          return {
            reviewerId: re._reviewer_ids,
            first: re.reviewer_name.name.first,
            middle: re.reviewer_name.name.middle,
            last: re.reviewer_name.name.last,
            dean_comment: re.dean_comment,
            dean_feedback: re.dean_feedback,
            expireTime: userId === re._reviewer_ids ? re.expire.expire_time : '',
            comment: userId === re._reviewer_ids ? re.reviewer_comment : '',
            status: re.status,
            reviewBox: userId === re._reviewer_ids ? re.reviews : '',
            reviews: re.reviews,
          };
        });

        // login reviewer agree,comment get  start

        const comment = review.filter((data) => {
          return data.comment !== '';
        });

        let feed = '';
        if (comment.length !== 0) {
          feed = comment[0].comment;
        } else {
          feed = '';
        }

        const reviewBox = review.filter((data) => {
          return data.reviewBox !== '';
        });

        let reviewCheckBox = '';
        if (reviewBox.length !== 0) {
          reviewCheckBox = reviewBox[0].reviewBox === true ? 'agree' : 'disagree';
        } else {
          reviewCheckBox = '';
        }

        // login reviewer agree, comment get  end

        // expire time count start
        const reviewFilter = review.filter((data) => {
          return data.expireTime !== '';
        });

        let minutes = 0;
        let hours = 0;
        let days = 0;
        let expireTime = 0;
        if (reviewFilter && reviewFilter.length) {
          var startTime = moment(reviewFilter[0].expireTime);
          var endTime = moment();
          var duration = moment.duration(startTime.diff(endTime));
          var days1 = parseInt(duration.asDays());
          var hours1 = parseInt(duration.asHours());
          var minutes1 = parseInt(duration.asMinutes()) % 60;

          if (minutes1 > 0) {
            expireTime = `${days1} D ${hours1} H ${minutes1} M`;
          }
        }

        this.setState({
          expireTime: expireTime,
          review: review,
          minutes: minutes,
          hours: hours,
          days: days,
          feed: feed,
          selectAgree: reviewCheckBox,
          evId: eventSingleView._id,
          evType: eventSingleView.event_type,
          evName: eventSingleView.event_name.first_language,
          evArab1: eventSingleView.event_name.second_language,
          evDetail: eventSingleView.event_description.first_language,
          evArab2: eventSingleView.event_description.second_language,
          evStartDate: eventSingleView.event_date,
          evEndDate: eventSingleView.end_date,
          evStartTime: eventSingleView.start_time,
          evEndTime: eventSingleView.end_time,
          isLoading: false,
        });
      })

      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'agree') {
      this.setState({
        selectAgree: e.target.value,
        selectAgreeError: '',
      });
      if (e.target.value === 'agree') {
        this.setState({
          chooseAgree: true,
        });
      } else if (e.target.value === 'disagree') {
        this.setState({
          chooseAgree: false,
        });
      }
    }
  };

  validation = (e, name) => {
    let selectAgreeError = '';

    if (this.state.selectAgree === 'r') {
      selectAgreeError = 'Choose Agree Or Disagree';
    }

    if (selectAgreeError) {
      this.setState({
        selectAgreeError,
      });

      return false;
    }
    return true;
  };

  handleSubmit = (e) => {
    e.preventDefault();

    if (this.validation()) {
      const data = {
        reviewer_type: 'reviewer',
        _event_id: this.state.eventId,
        _reviewer_id: this.props.userId,
        review: this.state.chooseAgree,
        review_comment: this.state.feed,
      };

      if (data.review === '') {
        this.props.setData({ message: t('required_reply_type') });
        return;
      }

      this.setState({
        isLoading: true,
      });

      axios
        .post(`institution_calendar_event/add_reviewer_review`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              review: [],
            },
            () => {
              let view = {
                _id: this.state.evId,
              };
              this.eventViewShow(e, view);
            }
          );

          this.props.setData({ message: t('updated_review') });
        })
        .catch((error) => {
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  handleAllCheckBox = (event, name) => {
    if (name === 'completeemail') {
      let email = event.target.checked;
      if (email === true) {
        this.setState({
          completeEmail: 'email',
          completeError: '',
        });
      } else {
        this.setState({
          completeEmail: '',
        });
      }
    }

    if (name === 'completesms') {
      let sms = event.target.checked;
      if (sms === true) {
        this.setState({
          completeSms: 'sms',
          completeError: '',
        });
      } else {
        this.setState({
          completeSms: '',
        });
      }
    }

    if (name === 'completescheduler') {
      let Scheduler = event.target.checked;
      if (Scheduler === true) {
        this.setState({
          completeScheduler: 'digiclass',
          completeError: '',
        });
      } else {
        this.setState({
          completeScheduler: '',
        });
      }
    }

    if (name === 'completeclass') {
      let digiclass = event.target.checked;
      if (digiclass === true) {
        this.setState({
          completeClass: 'digiclass',
          completeError: '',
        });
      } else {
        this.setState({
          completeClass: '',
        });
      }
    }
  };

  completeReview = (e) => {
    this.setState({
      completeView: true,
    });
  };

  completeReviewClose = (e) => {
    this.setState({
      completeView: false,
    });
  };

  completeReviewSubmit = (e) => {
    const { calendarId } = this.state;
    const { loggedInUserData } = this.props;
    if (
      this.state.completeEmail !== '' ||
      this.state.completeSms !== '' ||
      this.state.completeScheduler !== '' ||
      this.state.completeClass !== ''
    ) {
      const data = {
        to: 'dean',
        _calendar_id: calendarId,
        message: `<p>Dear User,<br><br>
        ${formatFullName(
          loggedInUserData.get('name', Map()).toJS()
        )} has Completed the Institution Calendar Review,
        <br><br>
        Best Regards<br>
        ${getEnvCollegeName()}</p>`,
        notify_via: [
          this.state.completeEmail,
          this.state.completeSms,
          this.state.completeScheduler,
          this.state.completeClass,
        ],
      };
      this.setState({
        isLoading: true,
      });

      axios
        .post(`institution_calendar_event/send_notification`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              completeView: false,
              completeEmail: '',
              completeSms: '',
              completeScheduler: '',
              completeClass: '',
            },
            () => {
              this.fetchApi();
            }
          );

          this.props.setData({ message: t('updated_all_review') });
          this.props.history.push('/InstitutionCalendar');
        })
        .catch((error) => {
          // let errorMessage='';
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      this.setState({
        completeError: 'Please Choose the Notification',
      });
    }
  };

  render() {
    let dd = moment(this.state.startTimeView).format('hh:mm a');
    let ss = moment(this.state.endTimeView).format('hh:mm a');
    const items = [
      { to: '#', label: t('side_nav.menus.institution_calendar') },
      { label: t('role_management.tabs.Review') },
    ];
    const { userId } = this.props;
    const { calendarName, isActive } = this.state;
    const currentCalendar = isActive; //isCurrentCalendar(calendarId);

    const iconForm = lang === 'ar' ? 'icon-form-arabic' : 'icon-form';
    const iconFormCalender = lang === 'ar' ? 'icon-form-calender-arabic' : 'icon-form-calender';

    const showArabic = showArabicMailShow();
    return (
      <React.Fragment>
        <CalenderIndx />
        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={index} to={to}>
                {label}
              </Link>
            ))}
        </Breadcrumb>
        <Loader isLoading={this.state.isLoading} />
        <div className="main " style={{ backgroundColor: '#fff' }}>
          <div className="container">
            <div className="row mt-2 ">
              <div className="col-lg-12">
                <AddNew create={t('backToCalendar')} to="/InstitutionCalendar" />
                <div className="clearfix"> </div>
              </div>

              <div className="col-md-6">
                <MainTitle
                  title={t('reviewEvent')}
                  subTitle={`${t('academic_year')} ${calendarName} | ${t(
                    'side_nav.menus.institution_calendar'
                  )} | ${t('mainCalendar')}`}
                  fontsize="f-14 pt-2"
                />
                <div className="clearfix"> </div>
              </div>

              <div className="col-md-6">
                <div className="float-right">
                  <div className="col-md-12 ">
                    {currentCalendar && (
                      <p className="mt-3 float-right">
                        {this.state.data === '' ? (
                          <Button variant="primary" block disabled>
                            {t('events.submit')}
                          </Button>
                        ) : (
                          <Button variant="primary" block onClick={this.completeReview}>
                            {t('events.submit')}
                          </Button>
                        )}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <hr />

            <div className="row">
              <div className="col-md-3">
                {this.state.data.map((view, i) => (
                  <div
                    key={i}
                    className="p-2 flex-container review_sideNav"
                    onClick={(e) => this.eventViewShow(e, view)}
                  >
                    <div className="flex-10">
                      <p className="f-16 mb-1">{moment(view.event_date).format('MMM Do YYYY')} </p>
                      <p className="f-14 mb-1 font-weight-bold"> {view.first_language} </p>
                    </div>

                    <div className="flex-1">
                      {view.reviewEvent.map((rs, i) => (
                        <React.Fragment key={i}>
                          {userId === rs.reviewerId && (
                            <p>
                              {' '}
                              {rs.reviews === 'Done' && (
                                <i
                                  className="fa fa-check-circle tick pt-10 f-24 text-green"
                                  aria-hidden="true"
                                ></i>
                              )}
                            </p>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="col-md-9">
                {/* event view start          */}

                {this.state.eventView === true && (
                  <div className="pb-5">
                    <div className="p-3 review_mainNav">
                      <div className="row">
                        <div className="col-md-9">
                          <h4 className="f-20 mb-1 text-left">
                            {' '}
                            {this.state.evName}{' '}
                            <Badge variant="secondary">
                              {t(`events.event_types.${this.state.evType}`)}
                            </Badge>{' '}
                          </h4>
                        </div>

                        <div className="col-md-3"></div>

                        <div className="col-md-12 pt-2 text-left">
                          <p className="f-16 mb-2"> {this.state.evDetail}</p>
                          <p className="f-16 mb-2">
                            {' '}
                            <b>{t('Date')}:</b>{' '}
                            {this.state.evStartDate !== ''
                              ? `${t(
                                  `calender.${moment(this.state.eventDate).format('MMM')}`
                                )} ${moment(this.state.eventDate).format('Do')}`
                              : t('loading')}{' '}
                            -{' '}
                            {this.state.evEndDate !== ''
                              ? `${t(
                                  `calender.${moment(this.state.evEndDate).format('MMM')}`
                                )} ${moment(this.state.evEndDate).format('Do')}`
                              : t('loading')}{' '}
                          </p>
                          <p className="f-16 mb-2">
                            {' '}
                            <b>{t('global_configuration.time')}:</b>{' '}
                            {this.state.evStartTime !== ''
                              ? moment(this.state.evStartTime).format('hh:mm A')
                              : t('loading')}{' '}
                            -{' '}
                            {this.state.evEndTime !== ''
                              ? moment(this.state.evEndTime).format('hh:mm A')
                              : t('loading')}{' '}
                          </p>
                          {/* <p className="f-16 mb-2">
                            {" "}
                            <b>Venue:</b> Conference Hall No. 4
                          </p> */}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {this.state.review.map((re, index) => {
                  let isEnabled = true;
                  if (re.status !== undefined && re.status !== 'Done') {
                    isEnabled = false;
                  }
                  return (
                    <React.Fragment key={index}>
                      {userId === re.reviewerId && (
                        <div className="col-md-10 pl-0 ">
                          <p> Your Review</p>

                          <div className="dash-inner">
                            <div className="row">
                              <div className="col-md-6">
                                <p>Dr.{formatFullName(re)}</p>
                              </div>
                              {this.state.expireTime !== 0 && (
                                <>
                                  <div className="col-md-6 ">
                                    <p className="text-right">
                                      Time left : {this.state.expireTime}
                                    </p>
                                  </div>

                                  <div className="col-md-6">
                                    {re.dean_comment !== '' && (
                                      <p>Dean Reply : {re.dean_comment}</p>
                                    )}
                                  </div>
                                  <div className="col-md-6 float-right">
                                    <p className="text-right">
                                      {re.dean_feedback === false && (
                                        <Button variant="outline-danger" size="sm">
                                          No
                                        </Button>
                                      )}

                                      {re.dean_feedback === true && (
                                        <Button variant="outline-success" size="sm">
                                          Yes
                                        </Button>
                                      )}
                                    </p>
                                  </div>

                                  <div className="col-md-12">
                                    <p> Reply</p>
                                    {!isEnabled ? (
                                      <Input
                                        elementType={'radio'}
                                        elementConfig={agree}
                                        className={'form-radio1'}
                                        selected={this.state.selectAgree}
                                        labelclass="radio-label2"
                                        onChange={(e) => this.onRadioGroupChange(e, 'agree')}
                                        feedback={this.state.selectAgreeError}
                                      />
                                    ) : re.reviews === true ? (
                                      <span style={{ color: 'green', fontSize: '15px' }}>
                                        Agree
                                      </span>
                                    ) : (
                                      <span style={{ color: 'red', fontSize: '15px' }}>
                                        Disagree
                                      </span>
                                    )}

                                    <div className="pt-3" style={{ wordBreak: 'break-word' }}>
                                      {!isEnabled ? (
                                        <textarea
                                          value={this.state.feed}
                                          onChange={(e) => this.handleChange(e, 'feed')}
                                          className={'form-control'}
                                          placeholder={'Add a comment'}
                                          disabled={isEnabled}
                                        />
                                      ) : (
                                        this.state.feed
                                      )}
                                    </div>
                                  </div>

                                  <div className="col-md-12 pt-3 ">
                                    {!isEnabled && (
                                      <div className="float-right">
                                        <Button
                                          variant="outline-primary"
                                          onClick={this.handleSubmit}
                                        >
                                          Send{' '}
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                </>
                              )}
                              {this.state.expireTime === 0 && (
                                <div className="col-md-6">
                                  <div
                                    className="float-right"
                                    style={{ color: 'red', paddingTop: '4px', fontSize: '15px' }}
                                  >
                                    Time Left
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </React.Fragment>
                  );
                })}

                <div className="col-md-10 pl-0 pt-5 ">
                  {this.state.review.length !== 0 && <p> Other’s Responses</p>}

                  {this.state.review.map((re, i) => (
                    <React.Fragment key={i}>
                      {userId !== re.reviewerId && (
                        <div className="pt-2">
                          <div className="dash-inner">
                            <div className="row">
                              <div className="col-md-8">
                                <p className=" mb-0">Dr. {formatFullName(re)}</p>
                              </div>

                              <div className="col-md-4 ">
                                <div className="float-right">
                                  {re.reviews === '' && <p className="text-right mb-0">Pending</p>}
                                  {re.reviews === true && (
                                    <Button variant="outline-success" size="sm">
                                      Yes
                                    </Button>
                                  )}
                                  {re.reviews === false && (
                                    <Button variant="outline-danger" size="sm">
                                      No
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </React.Fragment>
                  ))}
                </div>

                {/* event view end          */}
              </div>
            </div>
          </div>
        </div>
        {/* review request view funtion start   */}
        <Modal
          show={this.state.completeView}
          centered
          onHide={this.completeReviewClose}
          dialogClassName="modal-30w"
          aria-labelledby="example-custom-modal-styling-title"
        >
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1">{t('confirmReview')}</div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <div className="row justify-content-center pt-4">
              <div className="col-md-12">
                <p>{t('wantToCompleteReview')}? </p>

                <p className="pt-2">{t('notifyUserVia')}</p>
                <p className={`${lang === 'ar' ? 'pl-4' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completeemail')}
                    value="checkedall"
                  />{' '}
                  {t('email')}
                </p>

                {isMobileVerifyMandatory() && (
                  <p className={`${lang === 'ar' ? '' : 'pr-4'} float-left`}>
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'completesms')}
                      value="checkedall"
                    />{' '}
                    {t('sms')}
                  </p>
                )}

                <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completescheduler')}
                    value="checkedall"
                  />{' '}
                  DigiScheduler
                </p>

                <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completeclass')}
                    value="checkedall"
                  />{' '}
                  DigiClass
                </p>

                <div>
                  {' '}
                  <p className="f-16 text-red">{this.state.completeError}</p>{' '}
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <span
              className="remove_hover btn btn-outline-primary"
              onClick={this.completeReviewClose}
            >
              {t('events.close')}
            </span>
            {(this.state.completeClass !== '' ||
              this.state.completeEmail !== '' ||
              this.state.completeScheduler !== '' ||
              this.state.completeSms !== '') && (
              <span className="remove_hover btn btn-primary" onClick={this.completeReviewSubmit}>
                {t('events.submit')}
              </span>
            )}
          </Modal.Footer>
        </Modal>
        {/* complete review  send funtion end */}
        {/* review request view funtion start   */}
        <Modal
          show={this.state.requestView}
          centered
          size="lg"
          onHide={this.handleReviewRequestClose}
        >
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1">Senders list</div>
              <div className="col-md-12 pt-1">select the type of notification for each sender </div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <Table responsive>
              <thead className="th-change">
                <tr>
                  <th>S.No</th>
                  <th>Name</th>
                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'email')}
                      value="checkedall"
                    />{' '}
                    Email
                  </th>

                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'sms')}
                      value="checkedall"
                    />{' '}
                    SMS
                  </th>

                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'scheduler')}
                      value="checkedall"
                    />{' '}
                    DigiScheduler
                  </th>
                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'class')}
                      value="checkedall"
                    />{' '}
                    DigiClass
                  </th>
                </tr>
              </thead>
              {this.state.selectStaffList.length === 0 ? (
                <TableEmptyMessage />
              ) : (
                <tbody>
                  {this.state.selectStaffList.map((data, index) => (
                    <tr
                      key={index}
                      className="tr-change"
                      style={{
                        background: data.isCheckedEmail === true ? '#D1F4FF' : '',
                      }}
                    >
                      <td>{index + 1}</td>
                      <td>{formatFullName(data)}</td>

                      <td>
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => this.handleCheckBox(event, index, 'email')}
                          value="checkedall"
                          checked={data.isCheckedEmail}
                        />
                      </td>
                      {isMobileVerifyMandatory() && (
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={(event) => this.handleCheckBox(event, index, 'sms')}
                            value="checkedall"
                            checked={data.isCheckedSms}
                          />
                        </td>
                      )}

                      <td>
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => this.handleCheckBox(event, index, 'scheduler')}
                          value="checkedall"
                          checked={data.isCheckedScheduler}
                        />
                      </td>

                      <td>
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => this.handleCheckBox(event, index, 'class')}
                          value="checkedall"
                          checked={data.isCheckedClass}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </Table>

            <div className="row pt-4">
              <div className="col-md-4">
                <p className="pt-5 "> Add deadline to submit review </p>
              </div>

              <div className="col-md-4">
                <Input
                  elementType={'input'}
                  elementConfig={{
                    type: 'date',
                  }}
                  maxLength={25}
                  value={this.state.reviewEndDate}
                  changed={(e) => this.handleChange(e, 'reviewEndDate')}
                  feedback={this.state.reviewEndDateError}
                  className={`form-control ${iconFormCalender}`}
                  label={'End Date'}
                />
              </div>

              <div className="col-md-4">
                <p className="mb-2"> End Time</p>
                <i className="fa fa-clock-o calender" aria-hidden="true"></i>

                <DatePicker
                  selected={this.state.reviewEndTime}
                  onChange={this.reviewEndTimes}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  dateFormat="h:mm aa"
                  className={`form-control ${iconForm}`}
                  feedback={this.state.reviewEndDateError}
                />
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <span
              className="remove_hover btn btn-outline-primary"
              onClick={this.handleReviewRequestClose}
            >
              Close
            </span>
            <span className="remove_hover btn btn-primary" onClick={this.sendReview}>
              Submit
            </span>
          </Modal.Footer>
        </Modal>
        {/* review requestview funtion end */} {/* search staff view funtion start   */}
        <Modal show={this.state.searchView} centered size="lg" onHide={this.searchModelClose}>
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1">Search Staff</div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <Table responsive>
              <thead className="th-change">
                <tr>
                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={this.handleAllChecked}
                      value="checkedall"
                    />
                  </th>
                  <th>EmployeeID</th>
                  <th>Name</th>
                  <th>Program</th>
                  <th>Program Admin Role</th>
                  <th>Institute Admin Role</th>
                </tr>
              </thead>
              {this.state.staffData.length === 0 ? (
                <TableEmptyMessage />
              ) : (
                <tbody>
                  {this.state.staffData.map((data, index) => (
                    <tr
                      key={index}
                      className="tr-change"
                      style={{
                        background: data.isChecked === true ? '#D1F4FF' : '',
                      }}
                    >
                      {/* <td>{String(data.isChecked)}</td> */}
                      {data.isChecked !== true ? (
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={(event) => this.handleCheckChieldElement(event, index)}
                            value="checkedall"
                          />
                        </td>
                      ) : (
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={(event) => this.handleCheckChieldElement(event, index)}
                            value="checkedall"
                            checked
                          />
                        </td>
                      )}
                      <td>{data.employee_id}</td>
                      <td>{formatFullName(data)}</td>
                      <td>{data.employee_id}</td>
                      <td>{data.employee_id}</td>
                      <td>{data.employee_id}</td>
                    </tr>
                  ))}
                </tbody>
              )}
            </Table>
          </Modal.Body>
          <Modal.Footer>
            <span className="remove_hover btn btn-outline-primary" onClick={this.searchModelClose}>
              Close
            </span>
            <span className="remove_hover btn btn-primary" onClick={this.selectStaff}>
              Submit
            </span>
          </Modal.Footer>
        </Modal>
        {/* search staff view funtion end */} {/* event edit funtion start   */}
        <Modal show={this.state.eventEdit} centered size="lg" onHide={this.eventEditClose}>
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1">Edit Event</div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <form>
              <div className="managetext p-3">
                <div className="row ">
                  <div className="col-lg-6 col-md-6 col-xl-6 pb-2 ">
                    <Input
                      elementType={'floatingselect'}
                      elementConfig={{
                        options: this.state.eventType,
                      }}
                      value={this.state.selectType}
                      floatingLabel={'Event Type'}
                      changed={(e) => this.handleSelect(e, 'eventType')}
                      feedback={this.state.selectTypeError}
                    />
                  </div>
                </div>

                <div className="row ">
                  <div
                    className={`${
                      !showArabic ? 'col-lg-6 col-md-6 col-xl-6' : 'col-lg-12 col-md-12 col-xl-12'
                    } pb-2 pt-3`}
                  >
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      // className={'ddd'}
                      // inputclassName={'ddd1'}
                      maxLength={25}
                      value={this.state.eventName}
                      floatingLabel={'Event Name'}
                      changed={(e) => this.handleChange(e, 'eventName')}
                      feedback={this.state.eventNameError}
                    />
                  </div>
                  {!showArabic && (
                    <div className="col-lg-6 col-md-6 col-xl-6 pb-2 pt-3 ">
                      <Input
                        elementType={'floatinginput'}
                        elementConfig={{
                          type: 'text',
                        }}
                        maxLength={35}
                        value={this.state.arab1}
                        dd={'rtl-text'}
                        floatingLabel={'يريد العالم أن'}
                        changed={(e) => this.handleChange(e, 'arab1')}
                        feedback={this.state.arab1Error}
                      />
                    </div>
                  )}

                  <div
                    className={`${
                      !showArabic ? 'col-lg-6 col-md-6 col-xl-6' : 'col-lg-12 col-md-12 col-xl-12'
                    } pb-2 pt-3`}
                  >
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={200}
                      value={this.state.eventDetail}
                      floatingLabel={'Event Detail'}
                      changed={(e) => this.handleChange(e, 'eventDetail')}
                      feedback={this.state.eventDetailError}
                    />
                  </div>
                  {!showArabic && (
                    <div className="col-lg-6 col-md-6 col-xl-6 pb-2 pt-3 ">
                      <Input
                        elementType={'floatinginput'}
                        elementConfig={{
                          type: 'text',
                        }}
                        maxLength={25}
                        value={this.state.arab2}
                        dd={'rtl-text'}
                        floatingLabel={'تسجّل الآن'}
                        changed={(e) => this.handleChange(e, 'arab2')}
                        feedback={this.state.arab2Error}
                      />
                    </div>
                  )}

                  <div className="col-lg-7 col-md-7 col-xl-7 pb-2">
                    <div className="row">
                      <div className="col-6 col-md-6">
                        <div className="">
                          <Input
                            elementType={'input'}
                            elementConfig={{
                              type: 'date',
                            }}
                            maxLength={25}
                            value={this.state.startDate}
                            changed={(e) => this.handleChange(e, 'startDate')}
                            feedback={this.state.startDateError}
                            label={'Start Date'}
                            className={`form-control ${iconFormCalender}`}
                          />
                        </div>

                        <div className="pt-3">
                          <Input
                            elementType={'input'}
                            elementConfig={{
                              type: 'date',
                            }}
                            maxLength={25}
                            value={this.state.endDate}
                            changed={(e) => this.handleChange(e, 'endDate')}
                            feedback={this.state.endDateError}
                            dd={'icon-form'}
                            className={`form-control ${iconFormCalender}`}
                          />
                        </div>
                      </div>

                      <div className="col-6 col-md-6">
                        <div className="">
                          <p className="mb-2"> Start Time</p>
                          <i className="fa fa-clock-o calender" aria-hidden="true"></i>

                          <DatePicker
                            selected={this.state.startTime}
                            onChange={this.setStartTime}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            dateFormat="h:mm aa"
                            className={`form-control ${iconForm}`}
                            value={dd}
                          />
                        </div>

                        <div className="pt-3">
                          <p className="mb-2 pt-2"> End Time</p>
                          <i className="fa fa-clock-o calender" aria-hidden="true"></i>

                          <DatePicker
                            selected={this.state.endTime}
                            onChange={this.setEndTime}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            timeCaption="End Time"
                            dateFormat="h:mm aa"
                            className={`form-control ${iconForm}`}
                            value={ss}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="col-lg-5 col-md-5 col-xl-5 pb-2">
                    <div className="row">
                      <p className="ml-4 mt-5">
                        <span className="mr-2">
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={(event) => this.handleCheck(event, 'day')}
                            value={this.state.oneDay}
                          />
                        </span>
                        One Day Event
                      </p>
                    </div>
                  </div>

                  <div className="col-xl-12 pb-2 ">
                    <div className="row">
                      <div className="col-xl-5 pb-2 mt-3 ">
                        <p className="mt-3">
                          <span className="mr-2">
                            <input
                              type="checkbox"
                              className="calendarFormRadio"
                              onClick={(event) => this.handleCheck(event, 'value')}
                              value={this.state.valueCheck}
                            />
                          </span>
                          Reserve a venue for the event
                        </p>
                      </div>
                      {this.state.valueCheck === true && (
                        <div className="col-xl-5 pb-2 ">
                          <Input
                            elementType={'floatingselect'}
                            elementConfig={{
                              options: this.state.venue,
                            }}
                            value={this.state.selectVenue}
                            floatingLabel={'Venue'}
                            changed={(e) => this.handleSelect(e, 'venue')}
                            feedback={this.state.selectVenueError}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </Modal.Body>
          <Modal.Footer>
            <span className="remove_hover btn btn-outline-primary" onClick={this.eventEditClose}>
              Close
            </span>
            <span className="remove_hover btn btn-primary" onClick={this.handleEditEventSubmit}>
              Submit
            </span>
          </Modal.Footer>
        </Modal>
        {/* event edit funtion end */}
      </React.Fragment>
    );
  }
}

ReviewAccept.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  loggedInUserData: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  userId: PropTypes.string,
  setData: PropTypes.func,
  isCurrentCalendar: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    userId: selectUserId(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps, actions)(withRouter(withCalendarHooks(ReviewAccept)));
