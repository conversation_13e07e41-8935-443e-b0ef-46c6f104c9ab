import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { Button } from 'react-bootstrap';

import Steps from './Steps';
import AssignCourseForm from './AssignCourseForm';
import AssignSessionFlowForm from './AssignSessionFlowForm';
import { getSessionFlowWeekValidation, getAssignCourseValidation } from './utils';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  selectActiveStep,
  selectCourse,
  selectSessionFlow,
  selectProgramCurriculumList,
  selectEditedCourse,
  selectAssignedCourse,
  selectEditedSessionFlow,
  selectEditedAdditionalSessionFlow,
} from '../../../../_reduxapi/program_input/selectors';
import { capitalize } from '../../../InfrastructureManagement/utils';
import ArrowLeft from '../../../../Assets/arrow.svg';
import { getLang, getURLParams, levelRename } from '../../../../utils';
import { isWeekCheckboxChecked } from './utils';
import '../../css/program.css';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import LocalStorageService from 'LocalStorageService';

const currentLang = getLang();
export class AssignCourse extends Component {
  constructor(props) {
    super(props);
    const {
      match: {
        params: { curriculumId, courseId, assignedId },
      },
    } = props;
    this.state = {
      curriculumId,
      courseId,
      assignedId,
      STEPS: [t('constant.course_details'), t('constant.session_flow')],
      programId: getURLParams('_id', true),
      programName: getURLParams('_n', true),
      version: getURLParams('ver', true),
      year: getURLParams('yr', true),
      yearId: getURLParams('_yr_id', true),
      level: getURLParams('lvl', true),
      levelId: getURLParams('_lvl_id', true),
    };
    this.handleNextStep = this.handleNextStep.bind(this);
    this.handleBackClick = this.handleBackClick.bind(this);
  }

  componentDidMount() {
    const { courseId, assignedId, level } = this.state;
    this.props.getProgramCurriculumList();
    this.props.getCourse(courseId, false);
    if (assignedId !== 'new') {
      this.props.getAssignedCourse(courseId, assignedId);
    }
    this.props.getsessionFlow(courseId, level);
    this.props.setData(
      Map({
        activeStep: LocalStorageService.getCustomToken('activeStep') === '1' ? 1 : 0,
        course: Map(),
        assignedCourse: Map(),
        sessionFlow: Map(),
        editedCourse: Map({
          course_assigned_details: Map({
            course_shared_with: List(),
          }),
        }),
      })
    );
  }

  handleBackClick() {
    this.props.history.goBack();
  }

  handleNextStep() {
    const { activeStep } = this.props;
    switch (activeStep) {
      case 0:
        this.saveAssignedCourse();
        break;
      case 1:
        this.saveSessionFlowDetails();
        break;
      default:
        this.props.setData(Map({ message: 'Unknown action' }));
        break;
    }
  }

  saveAssignedCourse() {
    const {
      courseId,
      assignedId,
      programId,
      programName,
      curriculumId,
      version,
      year,
      yearId,
      level,
      levelId,
    } = this.state;
    const { editedCourse, assignedCourse, resetMessage } = this.props;
    if (!editedCourse.getIn(['course_assigned_details', 'course_duration'])) {
      resetMessage(t('messages.start_week'));
      return;
    }
    const requestBody = {
      _program_id: programId,
      program_name: programName,
      _curriculum_id: curriculumId,
      curriculum_name: version,
      _year_id: yearId,
      year: year,
      _level_id: levelId,
      level_no: level,
      course_duration: editedCourse.getIn(['course_assigned_details', 'course_duration']).toJS(),
      course_shared_with: editedCourse
        .getIn(['course_assigned_details', 'course_shared_with'])
        .map((c) => c.delete('_id'))
        .toJS(),
      isConfigured: true,
      isManualAssign: getURLParams('isManualAssign', true) !== '' ? false : true,
    };
    const validationMessage = getAssignCourseValidation(requestBody);
    if (validationMessage.length) {
      resetMessage(validationMessage);
      return;
    }
    this.props.assignCourse({
      operation: assignedCourse.isEmpty() ? 'create' : 'update',
      _id: assignedId,
      courseId,
      levelName: level,
      requestBody: assignedCourse.isEmpty()
        ? {
            course_assigned_details: requestBody,
          }
        : requestBody,
    });
  }

  saveSessionFlowDetails() {
    const {
      sessionFlow,
      editedSessionFlow,
      resetMessage,
      editedAdditionalSessionFlow,
    } = this.props;
    if (sessionFlow.get('session_flow_data', List()).isEmpty()) {
      resetMessage(t('program_input.error_strings.no_session_flow'));
      return;
    }

    const transformSessionData = (data, editedData) =>
      data.map((session, index) => {
        const week = editedData.getIn([index, 'week']);
        return Map({
          s_no: index + 1,
          _session_id: session.get('_session_id'),
          delivery_type: session.get('delivery_type'),
          _delivery_id: session.get('_delivery_id'),
          delivery_symbol: session.get('delivery_symbol'),
          delivery_no: session.get('delivery_no'),
          delivery_topic: session.get('delivery_topic').trim(),
          subjects: session.get('subjects').map((subject) => subject.delete('_id')),
          duration: session.get('duration'),
          week,
          ...(session.get('_id', '').length > 15 && { _id: session.get('_id') }),
        });
      });

    const requestBody = Map({
      session_flow_data: transformSessionData(
        sessionFlow.get('session_flow_data', List()),
        editedSessionFlow
      ),
      additional_session_flow_data: transformSessionData(
        sessionFlow.get('additional_session_flow_data', List()),
        editedAdditionalSessionFlow
      ),
      // session_flow_data: sessionFlow.get('session_flow_data', List()).map((s, i) =>
      //   Map({
      //     s_no: i + 1,
      //     _session_id: s.get('_session_id'),
      //     delivery_type: s.get('delivery_type'),
      //     _delivery_id: s.get('_delivery_id'),
      //     delivery_symbol: s.get('delivery_symbol'),
      //     delivery_no: s.get('delivery_no'),
      //     delivery_topic: s.get('delivery_topic').trim(),
      //     subjects: s.get('subjects').map((s) => s.delete('_id')),
      //     duration: s.get('duration'),
      //     week: editedSessionFlow.getIn([i, 'week']),
      //     ...(s.get('_id', '').length > 15 && { _id: s.get('_id') }),
      //   })
      // ),
      session_flow_deleted_ids: List(),
      _course_id: sessionFlow.get('_course_id'),
      _program_id: sessionFlow.get('_program_id'),
    }).toJS();
    const validationMessage = getSessionFlowWeekValidation(editedSessionFlow);
    if (validationMessage.length) {
      resetMessage(validationMessage);
      return;
    }
    this.props.saveSessionFlow({
      operation: 'update',
      sessionFlowId: sessionFlow.get('_id', ''),
      courseId: sessionFlow.get('_course_id'),
      url: `/program-input/configuration/curriculum-details${this.props.location.search}`,
      history: this.props.history,
      requestBody,
      isActive: true,
      assign: true,
    });
  }

  disabledSubmit = () => {
    const { editedSessionFlow, activeStep } = this.props;
    const checkboxState = isWeekCheckboxChecked(editedSessionFlow);
    if (activeStep === 1 && checkboxState) {
      let filled = [];
      editedSessionFlow &&
        editedSessionFlow.size > 0 &&
        editedSessionFlow.map((item) => {
          if (item.getIn(['week', 'week_no'], '') === '') {
            filled.push('yes');
          }
          return item;
        });
      return filled.length > 0;
    }
    return false;
  };

  handleChange = (e, name, index) => {
    const value = e.target.value;

    const { level } = this.state;
    const { setData, editedSessionFlow, editedAdditionalSessionFlow } = this.props;

    switch (name) {
      case 'week':
        setData(
          Map({
            editedSessionFlow: editedSessionFlow.setIn(
              [index, 'week'],
              Map({
                level_no: level,
                week_no: value,
              })
            ),
          })
        );
        break;
      case 'additionalWeek':
        setData(
          Map({
            editedAdditionalSessionFlow: editedAdditionalSessionFlow.setIn(
              [index, 'week'],
              Map({
                level_no: level,
                week_no: value,
              })
            ),
          })
        );
        break;
      default:
        break;
    }
  };

  handleWeekCheckbox = (checked, name) => {
    const { level } = this.state;
    const { editedCourse, setData, editedSessionFlow, editedAdditionalSessionFlow } = this.props;
    const start = editedCourse.getIn(
      ['course_assigned_details', 'course_duration', 'start_week'],
      0
    );

    switch (name) {
      case 'week':
        setData(
          Map({
            editedSessionFlow: editedSessionFlow.map((s, i) =>
              s
                .setIn(['week', 'week_no'], checked && i === 0 ? `${start}` : '')
                .setIn(['week', 'level_no'], level)
            ),
          })
        );
        break;
      case 'additionalWeek':
        setData(
          Map({
            editedAdditionalSessionFlow: editedAdditionalSessionFlow.map((s, i) =>
              s
                .setIn(['week', 'week_no'], checked && i === 0 ? `${start}` : '')
                .setIn(['week', 'level_no'], level)
            ),
          })
        );
        break;
      default:
        break;
    }
  };

  render() {
    const { version, year, level, curriculumId } = this.state;
    const {
      activeStep,
      course,
      programCurriculumList,
      editedCourse,
      sessionFlow,
      editedSessionFlow,
      editedAdditionalSessionFlow,
    } = this.props;
    const programId = getURLParams('_id', true);
    return (
      <React.Fragment>
        <div className="d-flex bg-white p-3">
          <div className="f-20 digi-font-500 digi-black col-md-4" style={{ padding: '0px 8px' }}>
            <img
              src={ArrowLeft}
              alt="back"
              className="mr-3 cursor-pointer"
              onClick={this.handleBackClick}
            />
            <span>
              <Trans i18nKey={'assign_course'}></Trans>
            </span>
          </div>
          <div className="col-md-8">
            <Steps steps={this.state.STEPS} activeStep={activeStep} />
          </div>
        </div>
        <div className="main bg-gray pb-5">
          <div className="bg-gray">
            <div className="container-fluid">
              <div className="row pb-4">
                <div className="col-md-12">
                  <div className="p-5">
                    <div className="d-flex justify-content-between">
                      <h5 className="pb-2 pt-2">
                        <Trans i18nKey={'curriculum'}></Trans>
                        <i className="fa fa-angle-right pr-2 pl-2" aria-hidden="true"></i>
                        {capitalize(version)}
                        <i className="fa fa-angle-right pr-2 pl-2" aria-hidden="true"></i>
                        {year.replace('year', t('constant.year'))}
                        <i className="fa fa-angle-right pr-2 pl-2" aria-hidden="true"></i>
                        {levelRename(level, programId)}
                      </h5>
                    </div>

                    {activeStep === 0 && (
                      <AssignCourseForm
                        programId={programId}
                        curriculumId={curriculumId}
                        yearId={getURLParams('_yr_id', true)}
                        levelId={getURLParams('_lvl_id', true)}
                        course={course}
                        programCurriculumList={programCurriculumList}
                        editedCourse={editedCourse}
                        setData={this.props.setData}
                        resetMessage={this.props.resetMessage}
                      />
                    )}
                    {activeStep === 1 && (
                      <AssignSessionFlowForm
                        course={course}
                        editedCourse={editedCourse}
                        sessionFlow={sessionFlow}
                        editedSessionFlow={editedSessionFlow}
                        editedAdditionalSessionFlow={editedAdditionalSessionFlow}
                        setData={this.props.setData}
                        levelName={level}
                        handleWeekCheckbox={this.handleWeekCheckbox}
                        handleChange={this.handleChange}
                      />
                    )}

                    <div className="row pt-3">
                      <div className={`col-md-6 ${currentLang === 'ar' ? 'text-left' : ''}`}>
                        <b className="pr-3">
                          <Button variant="outline-primary" onClick={this.handleBackClick}>
                            <Trans i18nKey={'cancel'}></Trans>
                          </Button>
                        </b>
                      </div>
                      <div className="col-md-6">
                        <div className="float-right">
                          <b className="pr-2">
                            <Button
                              variant="primary"
                              onClick={this.handleNextStep}
                              disabled={this.disabledSubmit()}
                            >
                              {activeStep === 0 ? t('configuration.next') : t('complete')}
                            </Button>
                          </b>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

AssignCourse.propTypes = {
  history: PropTypes.object,
  match: PropTypes.object,
  location: PropTypes.object,
  course: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  activeStep: PropTypes.number,
  saveCourse: PropTypes.func,
  sessionFlow: PropTypes.instanceOf(Map),
  editedSessionFlow: PropTypes.instanceOf(List),
  editedCourse: PropTypes.instanceOf(Map),
  assignedCourse: PropTypes.instanceOf(Map),
  saveSessionFlow: PropTypes.func,
  getCourse: PropTypes.func,
  getAssignedCourse: PropTypes.func,
  getsessionFlow: PropTypes.func,
  programCurriculumList: PropTypes.instanceOf(List),
  getProgramCurriculumList: PropTypes.func,
  assignCourse: PropTypes.func,
  resetMessage: PropTypes.func,
  editedAdditionalSessionFlow: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    activeStep: selectActiveStep(state),
    course: selectCourse(state),
    editedCourse: selectEditedCourse(state),
    sessionFlow: selectSessionFlow(state),
    programCurriculumList: selectProgramCurriculumList(state),
    assignedCourse: selectAssignedCourse(state),
    editedSessionFlow: selectEditedSessionFlow(state),
    editedAdditionalSessionFlow: selectEditedAdditionalSessionFlow(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(AssignCourse);
