import React from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';

import { getFormattedGroupName } from '../utils';
import CheckCircleGreenIcon from '../../../../Assets/alert2.png';
import { getURLParams, isIndGroup, studentGroupRename } from '../../../../utils';
function ListViewStudentGroups({ studentGroups, isRotation, studentGroupStatus }) {
  const programId = getURLParams('programId', true);

  function getGroupedStudentGroups(studentGroups) {
    return studentGroups.groupBy((sGroup) => sGroup.get('group_no')).toMap();
  }

  return (
    <div className="row bg-gray border-radious-8">
      {!isRotation ? (
        <>
          <div className="col-md-4">
            <div className="mr-1 mt-1">Student Groups - </div>
          </div>
          <div className="col-md-8">
            <div className="d-flex flex-wrap">
              {studentGroupStatus
                ? studentGroups.map((sGroup) => {
                    return (
                      <div key={sGroup.get('_id')} className="mr-2 mb-1 mt-1">
                        <div className="icon_schedule">
                          {getFormattedGroupName(
                            studentGroupRename(sGroup.get('group_name', ''), programId),
                            isIndGroup(sGroup.get('group_name', '')) ? 1 : 2
                          )}
                          {sGroup.get('schedule_status') ? (
                            <img src={CheckCircleGreenIcon} alt="check" className="pl-1" />
                          ) : (
                            <i
                              className="fa fa-exclamation-circle f-14 text-gray pl-1"
                              aria-hidden="true"
                            ></i>
                          )}
                        </div>
                      </div>
                    );
                  })
                : 'Student Group Not Created'}
            </div>
          </div>
        </>
      ) : studentGroupStatus ? (
        getGroupedStudentGroups(studentGroups)
          .entrySeq()
          .map(([groupNo, studentGroup]) => (
            <React.Fragment key={`R${groupNo}`}>
              <div className="col-md-2">
                <div className="mr-1 mt-1">{`R${groupNo} - `}</div>
              </div>
              <div className="col-md-10">
                <div className="d-flex flex-wrap">
                  {studentGroup.map((sGroup) => {
                    return (
                      <div key={sGroup.get('_id')} className="mr-2 mb-1 mt-1">
                        <div className="icon_schedule">
                          {getFormattedGroupName(
                            studentGroupRename(sGroup.get('group_name', ''), programId),
                            isIndGroup(sGroup.get('group_name', '')) ? 1 : 2
                          )}
                          {sGroup.get('schedule_status') ? (
                            <img src={CheckCircleGreenIcon} alt="check" className="pl-1" />
                          ) : (
                            <i
                              className="fa fa-exclamation-circle f-14 text-gray pl-1"
                              aria-hidden="true"
                            ></i>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </React.Fragment>
          ))
      ) : (
        'Student Group Not Created'
      )}
    </div>
  );
}

ListViewStudentGroups.propTypes = {
  studentGroups: PropTypes.instanceOf(List),
  isRotation: PropTypes.bool,
  studentGroupStatus: PropTypes.bool,
};

export default ListViewStudentGroups;
