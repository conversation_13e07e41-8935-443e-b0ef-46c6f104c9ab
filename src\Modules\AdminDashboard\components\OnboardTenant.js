import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Snackbars from '../../../Modules/Utils/Snackbars';
import * as actions from '../../../_reduxapi/admin_dashboard/actions';

const OnboardTenant = () => {
  const { error } = useSelector((state) => state.adminDashboard);
  const dispatch = useDispatch();
  const [tenantDetails, setTenantDetails] = useState({
    name: '',
    subdomain: '',
  });

  const handleTenantNameChange = (e) =>
    setTenantDetails({ ...tenantDetails, name: e.target.value });
  const handleTenantSubdomainChange = (e) =>
    setTenantDetails({ ...tenantDetails, subdomain: e.target.value });

  const handleOnboardTenant = (e) => {
    e.preventDefault();
    dispatch(actions.onBoardTenant(tenantDetails));
  };

  return (
    <div className="mt-5 mb-5">
      <h3 className="mb-2">Onboard New Tenant</h3>
      <form className="col-lg-6" onSubmit={handleOnboardTenant} autoComplete="off">
        <div className="form-group">
          <label htmlFor="tenantName">Tenant Name</label>
          <input
            type="text"
            className="form-control w-3"
            id="tenantName"
            aria-describedby="text"
            placeholder="Enter tenant name"
            onChange={handleTenantNameChange}
          />
        </div>
        <div className="form-group">
          <label htmlFor="subdomain">Subdomain</label>
          <input
            type="text"
            className="form-control"
            id="subdomain"
            placeholder="Enter tenant subdomain"
            onChange={handleTenantSubdomainChange}
          />
        </div>
        <button
          type="submit"
          className="btn btn-primary mt-2"
          disabled={!tenantDetails.name.length || !tenantDetails.subdomain.length}
        >
          Submit
        </button>
      </form>
      {error !== '' ? <Snackbars message={error} /> : null}
    </div>
  );
};

export default OnboardTenant;
