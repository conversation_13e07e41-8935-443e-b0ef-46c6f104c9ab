import { useSelector } from 'react-redux';
import { fromJS } from 'immutable';
import { envSignUpService } from 'utils';

function useCountryCode() {
  const authDataArray = useSelector((state) => state?.auth);
  const authData = fromJS(authDataArray);
  const countryCode = authData.getIn(
    ['loggedInUserData', 'services', 'REACT_APP_COUNTRY_CODE'],
    91
  );

  const countryCodeCookie =
    envSignUpService('REACT_APP_COUNTRY_CODE_LENGTH', false) !== ''
      ? envSignUpService('REACT_APP_COUNTRY_CODE_LENGTH', false)
      : 9;

  const countryCodeLength = parseInt(
    authData.getIn(
      ['loggedInUserData', 'services', 'REACT_APP_COUNTRY_CODE_LENGTH'],
      countryCodeCookie
    )
  );

  const replaceCountryCode = (mobileNumber) => {
    const number = String(mobileNumber);
    if (number === '' || number === null) return '';
    const numberLength = parseInt(number.length);
    if (numberLength === countryCodeLength) return mobileNumber;
    if (numberLength > countryCodeLength) {
      return number?.substring(countryCode.length, countryCodeLength + countryCode.length);
    }
    return mobileNumber;
  };

  const showWithCountryCode = (mobileNumber) => {
    return countryCode + replaceCountryCode(mobileNumber);
  };

  const mobileLengthMatch = (mobileNumber) => {
    return replaceCountryCode(String(mobileNumber)).length === parseInt(countryCodeLength);
  };

  const isMobileVerifyMandatory = () => {
    const verifyMobile = authData.getIn(['loggedInUserData', 'services', 'MOBILE'], false);
    return verifyMobile === 'true';
  };

  return {
    replaceCountryCode,
    showWithCountryCode,
    mobileLengthMatch,
    countryCodeLength,
    isMobileVerifyMandatory,
    countryCode,
  };
}

export default useCountryCode;
