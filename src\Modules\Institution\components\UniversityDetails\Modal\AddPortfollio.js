import React, { useState, useEffect } from 'react';
import { Modal } from 'react-bootstrap';
import { Editor } from 'react-draft-wysiwyg';
import { Trans, useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { EditorState } from 'draft-js';
import MaterialInput from 'Widgets/FormElements/material/Input';
import UploadMedia from './EditComponents/UploadMedia';
import MButton from 'Widgets/FormElements/material/Button';
import { EDITOR_TOOLBAR } from '../udUtil';

function AddPortfolio({
  show,
  handleClose,
  portfolioDescription,
  portfolioName,
  portfolioIndex,
  savePortFolio,
  previousImg,
  setData,
  imgName,
  cancelShow,
}) {
  const { t } = useTranslation();
  useEffect(() => {
    setName(portfolioName);
    onEditorStateChange(portfolioDescription);
  }, [portfolioName, portfolioDescription]);
  const [name, setName] = useState('');
  const [editorState, onEditorStateChange] = useState('');
  const [media, setMediaFiles] = useState(previousImg);
  const disableSave = () => {
    if (!editorState?.getCurrentContent && !name) return true;
    return !editorState?.getCurrentContent().getPlainText() || !name;
  };

  function editorStateLength() {
    if (!editorState?.getCurrentContent) return 0;
    return editorState?.getCurrentContent().getPlainText().length;
  }

  return (
    <Modal
      show={show}
      dialogClassName={`model-900 ${cancelShow ? 'display-none' : 'display-block'}`}
    >
      <Modal.Header className="border-none pb-0">
        <Modal.Title className="f-20">Add Portfolio</Modal.Title>
      </Modal.Header>

      <Modal.Body className="pt-0 model-border rounded mt-3 mx-3 mb-0">
        <UploadMedia media={media} setMediaFiles={setMediaFiles} setData={setData} name={imgName} />
        <div className="col-md-12 p-0">
          <div>
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              value={name}
              name="courseName"
              changed={(e) => setName(e.target.value)}
              placeholder={t('add_colleges.title')}
              fullWidth
              label={<Trans i18nKey={'add_colleges.title'}></Trans>}
              labelclass={'f-14 pt-1'}
            />
          </div>
        </div>
        <div className="pt-4 ">
          <p className="mb-0 f-14 pb-2">Description</p>

          <div className="digi-editor-border rounded min-height_200px">
            <Editor
              editorState={editorState}
              wrapperClassName="wrapperClassName"
              editorClassName="editorClassName"
              onEditorStateChange={onEditorStateChange}
              toolbar={EDITOR_TOOLBAR}
              placeholder={t(`add_colleges.Add_Description`)}
            />
          </div>
          <div className="text-right f-14">{editorStateLength()}/5000</div>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton
          className="mr-3"
          color="inherit"
          variant="outlined"
          clicked={() => {
            if (
              portfolioDescription?.getCurrentContent().getPlainText() !==
                editorState?.getCurrentContent().getPlainText() ||
              portfolioName !== name ||
              previousImg !== media
            ) {
              handleClose('portfolio', 'cancel');
            } else {
              handleClose();
            }
          }}
        >
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>

        <MButton
          clicked={() => {
            savePortFolio(
              name,
              // editorState.getCurrentContent().getPlainText(),
              editorState?.getCurrentContent(),
              media,
              portfolioIndex
            );
            //handleClose('portfolio', 'addPortfolio', portfolioIndex, { name, editorState });
          }}
          disabled={disableSave()}
        >
          <Trans i18nKey={'add_colleges.save'}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

AddPortfolio.propTypes = {
  show: PropTypes.bool,
  portfolioIndex: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  portfolioName: PropTypes.string,
  previousImg: PropTypes.string,
  imgName: PropTypes.string,
  setData: PropTypes.func,
  handleClose: PropTypes.func,
  savePortFolio: PropTypes.func,
  portfolioDescription: PropTypes.instanceOf(EditorState),
  cancelShow: PropTypes.bool,
  t: PropTypes.func,
};

export default AddPortfolio;
