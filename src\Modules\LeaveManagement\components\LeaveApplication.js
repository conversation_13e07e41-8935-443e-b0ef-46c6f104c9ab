import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { <PERSON><PERSON>, Badge } from 'react-bootstrap';
import moment from 'moment';
import { fromJS, List, Map } from 'immutable';
import { formatDistanceStrict, isValid } from 'date-fns';
import { t } from 'i18next';
import StaffSchedule from './StaffSchedule';
import ConfirmModal from '../modal/ConfirmModal';
import ApproveConfirmModal from '../modal/ApproveConfirmModal';
import Input from '../../../Widgets/FormElements/Input/Input';

import {
  selectUserId,
  selectUserInfo,
  selectSelectedRole,
  selectUserType,
  selectActiveInstitutionCalendar,
} from '../../../_reduxapi/Common/Selectors';
import {
  selectLeaveCategories,
  selectPermissionList,
  selectLeave,
  selectInstitutionCalendar,
  selectScheduleStatus,
  selectReviewerList,
  selectLeaveList,
  selectLeaveOverview,
  selectPermissionOverview,
  selectroleName,
} from '../../../_reduxapi/leave_management/selectors';
import * as actions from '../../../_reduxapi/leave_management/actions';
import { getScheduleListWithSubstituteStaff, getScheduleIdArray } from '../utils';
import { capitalize, getURLParams, getLang } from '../../../utils';
const lang = getLang();
class LeaveApplication extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      reviewView: true,
      reviewView1: true,
      substituteStaff: Map(),
      commentList: null,
      shouldFetchSessions: false,
      isSubstituteStaffPopulated: false,
      show: false,
      status: null,
      paid: true,
      unpaid: false,
      showApprove: false,
      role: null,
      paymentStatus: 'paid',
      isFetched: false,
      substituteStaffList: [],
    };
  }

  componentDidMount() {
    this.props.setBreadCrumbName(
      ` ${t('side_nav.menus.Faculty_Academic_Accountability_Management')}`
    );
    this.intervalReviewer = setInterval(() => {
      const { leavedata } = this.props;
      if (leavedata?._user_id === undefined) {
        this.setReviewerDetails();
      } else {
        const { userId, activeInstitutionCalendar, reviewer, selectedRole } = this.props;
        const institution_id = activeInstitutionCalendar.get('_id');
        const type = getURLParams('type', true);
        reviewer(userId, type, institution_id, selectedRole.getIn(['_role_id', '_id'], ''));
        clearInterval(this.intervalReviewer);
      }
    }, 500);

    this.interval = setInterval(() => {
      const { activeInstitutionCalendar, leavedata } = this.props;
      if (
        activeInstitutionCalendar.get('_id', '') !== '' &&
        leavedata?._user_id !== undefined &&
        leavedata?._user_id !== ''
      ) {
        clearInterval(this.interval);
        this.loadInitialData();
      }
    }, 500);
  }

  static getDerivedStateFromProps(props, state) {
    const { leavedata, scheduleStatus, activeInstitutionCalendar } = props;
    const leave = leavedata ? fromJS(leavedata) : Map();
    if (!state.shouldFetchSessions && !leave.isEmpty()) {
      const from = new Date(leave.get('from'));
      const to = new Date(leave.get('to'));
      if (!isValid(from) || !isValid(to)) return null;
      props.getScheduleStatus({
        institutionCalendarId: activeInstitutionCalendar.get('_id'),
        userId: leave.get('_user_id'),
        startDate: leave.get('from'),
        endDate: leave.get('to'),
      });
      return { shouldFetchSessions: true };
    }
    if (!state.isSubstituteStaffPopulated && scheduleStatus && !scheduleStatus.isEmpty()) {
      const substituteStaff = scheduleStatus.get('data', List()).reduce((acc, s) => {
        const scheduleId = s.get('_id');
        const assignedSubstituteStaff = s.get('assigned_substitute_staff', Map());
        if (assignedSubstituteStaff.isEmpty()) return acc;
        return acc.set(
          scheduleId,
          Map({
            schedule_id: scheduleId,
            substitute_staff: Map({
              name: assignedSubstituteStaff.get('substitute_staff_name'),
              _staff_id: assignedSubstituteStaff.get('_substitute_staff_id'),
            }),
          })
        );
      }, Map());
      return { substituteStaff, isSubstituteStaffPopulated: true };
    }
    return null;
  }

  componentWillUnmount() {
    clearInterval(this.interval);
    clearInterval(this.intervalReviewer);
  }

  loadInitialData = () => {
    const { leavedata, activeInstitutionCalendar } = this.props;
    const institution_id = activeInstitutionCalendar.get('_id');

    this.setState({ pending: true });

    this.props.getPermissionOverview({
      userId: leavedata?._user_id,
      userType: 'staff',
      institutionCalendarId: institution_id,
    });

    this.props.getLeaveOverview({
      userId: leavedata?._user_id,
      userType: 'staff',
      type: leavedata?.activeTab,
      institutionCalendarId: institution_id,
    });
  };

  setReviewerDetails = () => {
    const {
      userId,
      activeInstitutionCalendar,
      reviewer,
      reviewerList,
      setLeaveData,
      selectedRole,
    } = this.props;
    const institution_id = activeInstitutionCalendar.get('_id');
    const type = getURLParams('type', true);
    const leaveId = getURLParams('id', false);
    const flow = getURLParams('flow', true);
    if (reviewerList.size === 0) {
      reviewer(userId, type, institution_id, selectedRole.getIn(['_role_id', '_id'], ''));
    } else {
      const currentData = reviewerList
        .getIn([0, 'pending'])
        .filter((item) => item.get('_id') === leaveId);

      const datalist = currentData.get(0, Map()).toJS();
      const list = {
        ...datalist,
        activeTab: type,
        leave_flow: flow,
      };
      setLeaveData(list);
    }
  };

  handleClick = () => {
    this.setState({
      reviewView: !this.state.reviewView,
    });
  };
  handleClick1 = () => {
    this.setState({
      reviewView1: !this.state.reviewView1,
    });
  };
  handleClickback = () => {
    this.props.history.goBack();
  };
  isLeaveTypePermission() {
    return this.props.leavedata?.activeTab === 'permission';
  }

  hasSchedule() {
    const { scheduleStatus } = this.props;
    const scheduleList = scheduleStatus.get('data', List());
    return !scheduleList.isEmpty();
  }

  handleChange = (incomingValue, type) => {
    if (type === 'paid') {
      this.setState({
        paid: incomingValue,
        paymentStatus: 'paid',
        unpaid: false,
      });
    }

    if (type === 'unpaid') {
      this.setState({
        paid: false,
        paymentStatus: 'unpaid',
        unpaid: incomingValue,
      });
    }
  };
  commentChange = (e) => {
    this.setState({
      commentList: e.target.value,
    });
  };

  isSubstituteStaffAssignedToSessions = () => {
    const { scheduleStatus } = this.props;
    const { substituteStaff } = this.state;

    let scheduleList = scheduleStatus.get('data', List());
    if (scheduleList.isEmpty()) return true;

    scheduleList = scheduleList.map((s) => {
      const scheduleId = s.get('_id');
      const isSubstituteStaffRequired =
        s.get('staffs', List()).filter((staff) => staff.get('status') === 'pending').size <= 1;
      const isSubstituteStaffSelected = isSubstituteStaffRequired
        ? !substituteStaff.get(scheduleId, Map()).isEmpty()
        : true;

      return s.merge(Map({ isSubstituteStaffRequired, isSubstituteStaffSelected }));
    });
    const scheduleWithoutSubstituteStaff = scheduleList
      .filter((s) => !s.get('isSubstituteStaffSelected'))
      .toList();
    if (scheduleWithoutSubstituteStaff.size) {
      this.props.resetMessage(t('leaveManagement.staff_required_sessions'));
    }

    return scheduleWithoutSubstituteStaff.size === 0;
  };

  handleSingleSubmit = (status, flow) => {
    if (flow === 'rejected' || !status || this.isSubstituteStaffAssignedToSessions()) {
      const { scheduleStatus } = this.props;
      this.setState((state) => ({
        show: status,
        status: flow,
        role: this.props.roleName,
        substituteStaffList: status
          ? !state.substituteStaff.isEmpty()
            ? getScheduleListWithSubstituteStaff(
                state.substituteStaff,
                scheduleStatus.get('data', List())
              )
                .valueSeq()
                .toList()
                .toJS()
            : getScheduleIdArray(scheduleStatus)
          : [],
      }));
    }
  };

  handleApproveSubmit = (status, flow) => {
    if (this.isSubstituteStaffAssignedToSessions()) {
      const { scheduleStatus } = this.props;
      this.setState((state) => ({
        showApprove: status,
        status: flow,
        role: this.props.roleName,
        substituteStaffList: status
          ? !state.substituteStaff.isEmpty()
            ? getScheduleListWithSubstituteStaff(
                state.substituteStaff,
                scheduleStatus.get('data', List())
              )
                .valueSeq()
                .toList()
                .toJS()
            : getScheduleIdArray(scheduleStatus)
          : [],
      }));
    }
  };

  handleSubstituteStaffChange(staffId, schedule) {
    const scheduleId = schedule.get('_id');
    if (!staffId) {
      this.setState((state) => {
        return { substituteStaff: state.substituteStaff.delete(scheduleId) };
      });
      return;
    }
    const staffs = schedule.get('substitute_staff', List());
    const selectedStaff = staffs.find((s) => s.get('_staff_id') === staffId);
    if (selectedStaff) {
      this.setState((state) => {
        return {
          substituteStaff: state.substituteStaff.set(
            scheduleId,
            Map({
              schedule_id: scheduleId,
              substitute_staff: selectedStaff,
            })
          ),
        };
      });
    }
  }

  render() {
    const {
      leavedata,
      leaveOverview,
      scheduleStatus,
      permissionOverview,
      selectedRole,
    } = this.props;

    const permissionOverviewlist = permissionOverview?.toJS();
    const fromdata = moment(leavedata.from).format('DD MMM	YYYY').split('\t');
    const todata = moment(leavedata.to).format('DD MMM	YYYY').split('\t');
    fromdata[0] = fromdata[0].split(' ');
    todata[0] = todata[0].split(' ');
    const formatedFromDate = `${fromdata[0][0]} ${t(`calender.${fromdata[0][1]}`)} ${fromdata[1]}`;
    const formatedToDate = `${todata[0][0]} ${t(`calender.${todata[0][1]}`)} ${todata[1]}`;

    const dateTimeFormet = (value) => {
      const format = moment(value).format('DD MMM	YYYY hh:mm A').split(' ');
      format[1] = format[1].split('\t');
      format[3] = t(`date.${format[3]}`);
      const formatedData = `${format[0]} ${t(`calender.${format[1][0]}`)} ${'  '}${
        format[1][1]
      }  ${'    '}   ${format[2]} ${format[3]}`;

      return formatedData;
    };
    return (
      <div className="main pt-3 pb-5 ">
        {/* <Loader isLoading={this.state.isLoading} /> */}
        <div className="container-fluid ">
          <div className="row w-100">
            {/* start - reviewer review buttons*/}
            <div className="col-md-6 pt-3 pb-2 text-initial">
              <b className="pl-2 f-18" onClick={this.handleClickback}>
                {' '}
                <i
                  className={`fa fa-arrow-left remove_hover ${
                    lang === 'ar' ? 'pr-3 mr-3' : 'pr-3'
                  } text-primary f-16`}
                  aria-hidden="true"
                >
                  {' '}
                </i>
                {leavedata?.activeTab === 'permission'
                  ? t('leaveManagement.approve_type_applications', {
                      type: t('leaveManagement.tabs.Permission'),
                    })
                  : leavedata?.activeTab === 'leave'
                  ? t('leaveManagement.approve_type_applications', {
                      type: t('leaveManagement.tabs.Leave'),
                    })
                  : t('leaveManagement.approve_type_applications', {
                      type: t('leaveManagement.tabs.On-Duty'),
                    })}
              </b>
            </div>

            <div className="col-md-6">
              <div className="float-right">
                {leavedata?.leave_flow === 'pending' && (
                  <Button
                    variant={
                      this.state.commentList === null || this.state.commentList.trim() === ''
                        ? 'secondary'
                        : 'outline-primary'
                    }
                    className="m-2"
                    disabled={
                      this.state.commentList === null || this.state.commentList.trim() === ''
                        ? true
                        : false
                    }
                    onClick={(e) => this.handleSingleSubmit(true, 'rejected')}
                  >
                    {t('leaveManagement.reject')}
                  </Button>
                )}

                {this.props.roleName !== 'approver' && leavedata?.leave_flow === 'pending' && (
                  <Button onClick={(e) => this.handleSingleSubmit(true, 'approve')}>
                    {t('leaveManagement.COMPLETE')}
                  </Button>
                )}

                {this.props.roleName === 'approver' && leavedata?.leave_flow === 'pending' && (
                  <Button onClick={(e) => this.handleApproveSubmit(true, 'approve')}>
                    {t('leaveManagement.COMPLETE')}
                  </Button>
                )}
              </div>

              {this.state.show && (
                <ConfirmModal
                  title={`${t('confirm')} ${
                    leavedata?.activeTab === 'permission'
                      ? t('leaveManagement.tabs.Permission')
                      : leavedata?.activeTab === 'leave'
                      ? t('leaveManagement.tabs.Leave')
                      : t('leaveManagement.tabs.On-Duty')
                  }`}
                  content={`${
                    leavedata?.activeTab === 'permission'
                      ? t('leaveManagement.tabs.Permission')
                      : leavedata?.activeTab === 'leave'
                      ? t('leaveManagement.tabs.Leave')
                      : t('leaveManagement.tabs.Leave')
                  } ${t('leaveManagement.application_is')} ${
                    this.state.status === 'approve'
                      ? t('leaveManagement.Reviewed')
                      : t('leaveManagement.Rejected')
                  } ${t('leaveManagement.confirm_proceed')}`}
                  closed={this.handleSingleSubmit.bind(this)}
                  comment={this.state.commentList}
                  status={this.state.status}
                  leaveflow={leavedata?.leave_flow}
                  search={this.props.match.params.id}
                  roleId={selectedRole.getIn(['_role_id', '_id'])}
                  scheduleList={this.state.substituteStaffList}
                  // changeToreview={leavedata?.changeToreview}
                />
              )}

              {this.state.showApprove && (
                <ApproveConfirmModal
                  title={
                    this.props.leavedata?.activeTab === 'leave'
                      ? t('leaveManagement.approve_type_applications', {
                          type: t('leaveManagement.tabs.Leave'),
                        })
                      : this.props.leavedata?.activeTab === 'on_duty'
                      ? t('leaveManagement.approve_type_applications', {
                          type: t('leaveManagement.tabs.On-Duty'),
                        })
                      : t('leaveManagement.approve_type_applications', {
                          type: t('leaveManagement.tabs.Permission'),
                        })
                  }
                  closed={this.handleApproveSubmit.bind(this)}
                  comment={this.state.commentList}
                  status={this.state.status}
                  leaveflow={leavedata?.leave_flow}
                  leavedata={this.props.leavedata}
                  userId={this.props.userId}
                  paymentStatus={this.state.paymentStatus}
                  roleId={selectedRole.getIn(['_role_id', '_id'])}
                  scheduleList={this.state.substituteStaffList}
                />
              )}
            </div>
          </div>
          {/* end - dean review buttons*/}

          <div className="p-2">
            <div className="reviw-bg-grey">
              <div className="d-flex justify-content-between">
                <div className="f-18 bold ">
                  {`${t('leaveManagement.Applicant')} ${
                    leavedata?.activeTab === 'on_duty'
                      ? t('leaveManagement.tabs.on_duty').toLowerCase()
                      : t(`leaveManagement.tabs.${leavedata?.activeTab}`).toLowerCase()
                  }
                     ${t('reports_analytics.summary').toLowerCase()}`}
                </div>
                <div className="f-16 bold">
                  {this.props.roleName === 'approver' && leavedata?.leave_flow === 'pending' && (
                    <>
                      <span className="pr-4"> {t('leaveManagement.Salary')} </span>
                      <span>
                        <span className={'radio-label2'}>
                          <input
                            type="radio"
                            checked={this.state.paid}
                            onChange={(e) => this.handleChange(e.target.checked, 'paid')}
                            className={`form-control form-radio1`}
                          />
                          <span className={'radio-label2'}>{t('leaveManagement.Paid')}</span>
                        </span>
                        <span className={'radio-label2'}>
                          <input
                            type="radio"
                            checked={this.state.unpaid}
                            onChange={(e) => this.handleChange(e.target.checked, 'unpaid')}
                            className={`form-control form-radio1`}
                          />
                          <span className={'radio-label2'}>{t('leaveManagement.Unpaid')}</span>
                        </span>
                      </span>
                    </>
                  )}
                </div>
              </div>
              <div className="row pb-3 text-initial">
                <div className="col-md-12">
                  <div className="f-16 pb-2">
                    <span className="profile_view">{leavedata?.staff?.user_id}</span>{' '}
                    {leavedata?.staff?.name.first}
                  </div>
                  {leavedata?.activeTab !== 'permission' &&
                    leavedata?.activeTab !== 'report_absence' && (
                      <>
                        {' '}
                        <div className="f-16 pb-2">
                          {leavedata?.activeTab === 'on_duty'
                            ? t('leaveManagement.onDutyCategory')
                            : t('leaveManagement.leaveCategory')}
                          :{leavedata?.leave_category?.name}
                        </div>
                        <div className="f-16 pb-2">
                          {leavedata?.activeTab === 'on_duty'
                            ? t('leaveManagement.onDutyType')
                            : t('leaveManagement.leave Type')}
                          :{leavedata?.leave_type?.name}
                        </div>
                      </>
                    )}

                  <div className="f-16 pb-2">
                    {t('leaveManagement.Reason')}:{leavedata?.reason}
                  </div>
                  <div className="f-16 pb-2">
                    {t('leaveManagement.Date')}: {formatedFromDate}
                    &nbsp;{t('leaveManagement.to')} &nbsp;
                    {formatedToDate}
                  </div>
                  {leavedata?.activeTab === 'permission' && (
                    <div className="f-16 pb-2">
                      {t('reports_analytics.duration')}:
                      {formatDistanceStrict(new Date(leavedata.to), new Date(leavedata.from))}{' '}
                    </div>
                  )}
                  {this.props.roleName !== 'approver' && (
                    <div className="f-16 pb-2">
                      {t('leaveManagement.Payment_Status')}:{capitalize(leavedata?.payment_status)}{' '}
                    </div>
                  )}

                  <div className="f-16 pb-2">
                    {t('leaveManagement.attachment')}
                    {t('leaveManagement.s')}:
                    {leavedata?._leave_reason_doc === 'null' ? (
                      <span className="profile_view"> {t('leaveManagement.No_Attachment')}</span>
                    ) : (
                      <Link>
                        <span
                          className="profile_view"
                          onClick={() => {
                            window.open(`${leavedata?._leave_reason_doc_url}`);
                          }}
                        >
                          {leavedata?._leave_reason_doc?.split('/').slice(-1)}
                        </span>
                      </Link>
                    )}
                  </div>
                  <div className="p-2">
                    {leavedata?.activeTab === 'permission' && (
                      <div className="f-20 pb-1">
                        {t('leaveManagement.Permission_overview')}
                        <span className="f-16">
                          {' '}
                          {t('leaveManagement.Academic_year')}{' '}
                          {this.props.activeInstitutionCalendar.get('calendar_name')}
                        </span>
                      </div>
                    )}
                    {leavedata?.activeTab === 'permission' && (
                      <b className="pr-2">
                        <Badge className="bg-lightgreen border-radious-8 f-15 p-2">
                          {/* {permissionOverviewlist?.[0]?.permission_taken} used out of{' '} */}
                          {permissionOverviewlist?.[0]?.permission_hours} {t('configuration.hours')}{' '}
                          ({permissionOverviewlist?.[0]?.permission_frequency}{' '}
                          {t('global_configuration.time')}) /{' '}
                          {permissionOverviewlist?.[0]?.permission_frequency_by}
                        </Badge>
                      </b>
                    )}
                    {leavedata?.activeTab !== 'permission' &&
                      leaveOverview &&
                      leaveOverview.map((overview) => (
                        <b key={overview.get('_id')} className="pr-2">
                          <Badge className="bg-lightgreen border-radious-8 f-15 p-2">
                            {`${overview.get('leave_type', '')} 
                            ${overview.get('no_of_days', '')} ${t(
                              'global_configuration.day_s'
                            ).toLowerCase()}/${overview.get('entitlement', '')}`}
                          </Badge>
                        </b>
                      ))}
                  </div>
                </div>
              </div>
              <div className="row">
                <div className="col-md-6 col-lg-5 col-xl-5">
                  {leavedata?.level_approvers?.map((item) => {
                    return (
                      <>
                        <div
                          className={`bg-lightdark p-2 border-radious-5 ${
                            getLang() === 'ar' ? 'text-left' : ''
                          }`}
                        >
                          <div className="f-16 pb-1">
                            <b> {t('configure_levels.level')}</b> {item.level_no}{' '}
                          </div>
                          <div className="f-14 pb-1"> {dateTimeFormet(item.comment_time)}</div>
                          <div className="f-16 pb-1">
                            <b> {item?.comment}</b>{' '}
                          </div>
                        </div>
                      </>
                    );
                  })}
                </div>
              </div>
              {leavedata?.leave_flow === 'pending' && (
                <div className="row">
                  <div className="col-md-4">
                    <Input
                      elementType="floatinginput"
                      elementConfig={{ type: 'text' }}
                      changed={(e) => {
                        this.commentChange(e);
                      }}
                      maxLength={200}
                      floatingLabel={t('leaveManagement.Add_comments')}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
          {this.hasSchedule() && (
            <div className="p-2">
              <StaffSchedule
                selectedSubstituteStaff={this.state.substituteStaff}
                scheduleStatus={scheduleStatus}
                handleSubstituteStaffChange={this.handleSubstituteStaffChange.bind(this)}
                isSubstituteStaffReadOnly={leavedata?.leave_flow !== 'pending'}
                isScheduled={true}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
}
LeaveApplication.propTypes = {
  getScheduleStatus: PropTypes.func,
  setBreadCrumbName: PropTypes.func,
  reviewer: PropTypes.func,
  getPermissionOverview: PropTypes.func,
  setLeaveData: PropTypes.func,
  resetMessage: PropTypes.func,
  userType: PropTypes.string,
  userId: PropTypes.string,
  roleName: PropTypes.string,
  leavedata: PropTypes.object,
  history: PropTypes.object,
  match: PropTypes.object,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  selectedRole: PropTypes.instanceOf(Map),
  scheduleStatus: PropTypes.instanceOf(Map),
  getLeaveOverview: PropTypes.instanceOf(Map),
  reviewerList: PropTypes.instanceOf(List),
  leaveOverview: PropTypes.instanceOf(List),
  permissionOverview: PropTypes.instanceOf(List),
};

const mapStateToProps = (state) => {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    userType: selectUserType(state),
    reviewerList: selectReviewerList(state),
    leavedata: selectLeaveList(state),
    userId: selectUserId(state),
    leaveOverview: selectLeaveOverview(state),
    permissions: selectPermissionList(state),
    leave: selectLeave(state),
    institutionCalendar: selectInstitutionCalendar(state),
    scheduleStatus: selectScheduleStatus(state),
    userInfo: selectUserInfo(state),
    leaveCategories: selectLeaveCategories(state),
    permissionOverview: selectPermissionOverview(state),
    roleName: selectroleName(state),
    selectedRole: selectSelectedRole(state),
  };
};
export default compose(withRouter, connect(mapStateToProps, actions))(LeaveApplication);
