import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { Map } from 'immutable';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';
import MButton from 'Widgets/FormElements/material/Button';
import * as Constants from '../../../constants';
import * as actions from '_reduxapi/user_management/v2/actions';
import AuthIndex from '../SignUpIndex';
import DS_logo from 'Assets/ds_logo.svg';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { selectStaffVerification } from '_reduxapi/user_management/v2/selectors';
import NewPassword from './NewPassword';
import i18n from '../../../i18n';
import Footer from 'Shared/Footer';

const emailVal = Constants.EMAIL_VALIDATION;
class SignUp extends Component {
  constructor() {
    super();
    this.state = {
      email: '',
      password: '',
    };
    this.emailRef = React.createRef();
    this.passwordRef = React.createRef();
  }

  componentDidMount() {
    this.props.setData({ staffVerification: Map() });
    if (this.emailRef.current !== null) {
      this.emailRef.current.focus();
    }
  }

  onChange = (e, name) => {
    e.preventDefault();
    this.setState({
      [name]: e.target.value,
    });
  };

  validation = () => {
    const { setData } = this.props;
    const { email, password } = this.state;

    if (!email) {
      setData({ message: i18n.t('user_management.email_required') });
      return false;
    }
    if (!emailVal.test(email)) {
      setData({ message: i18n.t('leaveManagement.errorMsg.validMail') });
      return false;
    }
    if (!password) {
      setData({ message: i18n.t('Auth_words.password_is_required') });
      return false;
    }
    if (password.length <= 7) {
      setData({ message: i18n.t('Auth_words.min_eignt') });
      return false;
    }

    return true;
  };

  handleSignIn = (e) => {
    e.preventDefault();
    const { postSignUp, history } = this.props;
    const { email, password } = this.state;

    if (this.validation()) {
      const authData = {
        email: email,
        password: password,
      };
      postSignUp({ formData: { authData }, history });
    }
  };

  render() {
    const { t, staffVerification } = this.props;
    const { email, password } = this.state;
    return (
      <div>
        <AuthIndex />
        <div>
          <div className="login-bg">
            <div className="grandParentContainer">
              <div className="parentContainer">
                <div className="row justify-content-center">
                  <div className="col-12 col-xl-12 col-lg-12 col-md-12">
                    <h3 className="text-center">
                      <img src={DS_logo} alt="Digi-scheduler" />
                    </h3>
                  </div>
                </div>

                <div className="row justify-content-center pt-3">
                  <div className="col-12 col-xl-12 col-lg-12 col-md-12">
                    <div className="outter-login">
                      <div>
                        <p className="f-20 pt-3 text-center text-gray">
                          <Trans i18nKey={'Sign_Up'}></Trans>
                        </p>
                      </div>

                      <React.Fragment>
                        <form>
                          <div className="pt-2">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'text'}
                              placeholder={t('enter_email')}
                              variant={'outlined'}
                              size={'small'}
                              label={i18n.t('email')}
                              changed={(e) => this.onChange(e, 'email')}
                              value={email}
                              elementConfig={{
                                ref: this.emailRef,
                                autoFocus: true,
                              }}
                            />
                          </div>
                          <div className="pt-2 position-relative">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'text'}
                              placeholder={t('enter_password')}
                              variant={'outlined'}
                              size={'small'}
                              label={i18n.t('Temporary_Password')}
                              changed={(e) => this.onChange(e, 'password')}
                              value={password}
                              elementConfig={{
                                ref: this.passwordRef,
                              }}
                            />
                          </div>
                          <div className="pt-3">
                            <MButton type="submit" clicked={this.handleSignIn} fullWidth>
                              <Trans i18nKey={'Sign_Up'} />
                            </MButton>
                          </div>
                        </form>
                      </React.Fragment>
                    </div>
                  </div>
                </div>
                <div className="row justify-content-center pt-4 pb-2">
                  <Footer />
                </div>
              </div>
            </div>
          </div>
        </div>
        {!staffVerification.getIn(['verification', 'password'], true) && (
          <NewPassword isChangePassword={false} staffVerification={staffVerification} />
        )}
      </div>
    );
  }
}

SignUp.propTypes = {
  t: PropTypes.func,
  history: PropTypes.object,
  setData: PropTypes.func,
  postSignUp: PropTypes.func,
  staffVerification: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  return {
    staffVerification: selectStaffVerification(state),
  };
};

export default connect(mapStateToProps, actions)(withRouter(withTranslation()(SignUp)));
