<!DOCTYPE html>
<html>
  <head>
    <title>Map with Autocomplete and Range Selector</title>

    <style>
      #map {
        height: 400px;
        width: 100%;
      }
      .controls {
        left: 50%;
        z-index: 1;
      }
      .Form-controls {
        display: flex;
        height: 40px;
      }
      #searchInput {
        flex-grow: 1;
        box-sizing: border-box;
        border-radius: 3px;
        font-size: 14px;
        outline: none;
        z-index: 1;
        border-radius: 5px;
      }
      #rangeInput {
        width: 100%;
      }
      #outside-campus-map {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
    </style>
  </head>
  <body>
    <div id="outside-campus-map">
      <div class="Form-controls">
        <input id="searchInput" type="text" placeholder="Enter a location" />
      </div>
      <div id="map"></div>
      <input id="rangeInput" type="range" min="10" max="100" value="10" />
    </div>
    <script>
      var map, autocomplete, circle;
      var latitude, longitude, radius; // Declare these variables in the global scope
      var queryParams = new URLSearchParams(window.location.search);
      var radiusQuery = queryParams.get('radius');
      var latitudeQuery = queryParams.get('latitude');
      var longitudeQuery = queryParams.get('longitude');

      function getLocation() {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition((position) => {
            const pos = {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            };
            map.setCenter(pos);
            circle.setCenter(pos);
          });
        }
      }

      function initMap() {
        map = new google.maps.Map(document.getElementById('map'), {
          center: {
            lat: parseFloat(latitudeQuery) || -34.397,
            lng: parseFloat(longitudeQuery) || 150.644,
          },
          zoom: 17,
        });

        autocomplete = new google.maps.places.Autocomplete(document.getElementById('searchInput'));
        autocomplete.bindTo('bounds', map);

        circle = new google.maps.Circle({
          map: map,
          radius: parseInt(radiusQuery, 10) || 10,
          fillColor: '#AA0000',
          fillOpacity: 0.35,
          strokeColor: '#AA0000',
          strokeOpacity: 0.8,
          strokeWeight: 2,
          clickable: false,
        });

        autocomplete.addListener('place_changed', function () {
          var place = autocomplete.getPlace();
          if (!place.geometry) {
            window.alert("No details available for input: '" + place.name + "'");
            return;
          }

          latitude = place.geometry.location.lat();
          longitude = place.geometry.location.lng();
          if (place.geometry.viewport) {
            map.fitBounds(place.geometry.viewport);
          } else {
            map.setCenter(place.geometry.location);
            map.setZoom(17);
          }
          circle.setCenter(place.geometry.location);
          updateCircleRadius();
          let msg = { latitude: latitude, longitude: longitude, radius: radius };
          window.parent.postMessage(msg, '*');
        });

        document.getElementById('rangeInput').addEventListener('input', updateCircleRadius);

        document.getElementById('rangeInput').value = parseInt(radiusQuery, 10) || 10;

        if (!latitudeQuery || !longitudeQuery) {
          getLocation();
        } else {
          circle.setCenter(
            new google.maps.LatLng(parseFloat(latitudeQuery), parseFloat(longitudeQuery))
          );
        }
      }

      function updateCircleRadius() {
        radius = parseInt(document.getElementById('rangeInput').value, 10);
        circle.setRadius(radius);
      }

      function initializeMap() {
        initMap();
      }
    </script>
    <script
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDbI6tNTzXV7hVYzN0FqHU8H77TNC6ECus&libraries=places&callback=initializeMap"
      async
      defer
    ></script>
  </body>
</html>
