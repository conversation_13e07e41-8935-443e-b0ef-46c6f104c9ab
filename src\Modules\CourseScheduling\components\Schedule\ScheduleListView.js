import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Dropdown, Table, Form } from 'react-bootstrap';
import { List, Map, fromJS } from 'immutable';
import { format, isValid } from 'date-fns';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import Tooltip from '@mui/material/Tooltip';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import ListViewStudentGroups from './ListViewStudentGroups';
import { getFormattedGroupName } from '../utils';
import {
  capitalize,
  indVerRename,
  isIndGroup,
  studentGroupRename,
  studentGroupViewList,
  isModuleEnabled,
} from '../../../../utils';
import CheckCircleGreenIcon from '../../../../Assets/alert2.png';
import MergeIcon from '../../../../Assets/merge.svg';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
function ScheduleListView({
  activeViewType,
  course,
  isAllowedToSchedule,
  handleAddEditSchedule,
  studentGroupStatus,
  fetchCourseSchedule,
  paginationMetaData,
  isLoading,
  handleDeleteSchedule,
  handleCancelSchedule,
  showMergeCheckbox,
  mergeScheduleList,
  handleMergeScheduleCheckboxChange,
  handleEditMergeSchedule,
  handleAddEditSupportAndEvents,
  handleCancelDeleteSupportAndEventsSchedule,
  handleStudentGroupModalOpen,
  addEventsAndSupportSessionRef,
  supportSessionTypes,
  isRotation,
  studentGroupId,
  currentCalendar,
  programId,
  showMissedToComplete,
  showRevoke,
  status,
  handleMissedCheckboxChange,
}) {
  const [expanded, setExpanded] = useState(Map());

  const [infiniteRef] = useInfiniteScroll({
    loading: isLoading,
    hasNextPage: checkIfHasNextPage(),
    onLoadMore: () => {
      const { currentPage, totalPages } = getPaginationMetaData();
      if (currentPage === null) return;
      if (currentPage === totalPages) return;
      fetchCourseSchedule({
        page: paginationMetaData.get('currentPage') + 1,
        type:
          activeViewType === 'support'
            ? 'support_session'
            : activeViewType === 'events'
            ? 'event'
            : activeViewType,
      });
    },
  });

  function checkIfHasNextPage() {
    const { currentPage, totalPages } = getPaginationMetaData();
    if (currentPage === null) return false;
    return currentPage !== totalPages;
  }

  function getPaginationMetaData() {
    return {
      currentPage: paginationMetaData.get('currentPage'),
      totalPages: paginationMetaData.get('totalPages'),
    };
  }

  function getScheduleDate(schedule) {
    const scheduleDate = new Date(schedule.get('schedule_date'));
    if (!isValid(scheduleDate)) return '';
    return format(scheduleDate, 'd LLL E');
  }

  function prefixZero(num) {
    if (typeof num !== 'number') return num;
    return num < 10 ? `0${num}` : num;
  }

  function getScheduleTime(schedule) {
    const start = schedule.get('start', Map());
    const end = schedule.get('end', Map());
    if (start.isEmpty() || end.isEmpty()) return '';
    return `${start.get('hour')}:${prefixZero(start.get('minute', 0))} ${start.get(
      'format'
    )} - ${end.get('hour')}:${prefixZero(end.get('minute', 0))} ${end.get('format')}`;
  }

  function getSubType(schedule) {
    if (schedule.get('type') === 'support_session') {
      const subType = supportSessionTypes.find(
        (supportSessionType) => supportSessionType.value === schedule.get('sub_type')
      );
      return subType ? subType.name : capitalize(schedule.get('sub_type', ''));
    }
    return capitalize(schedule.get('sub_type', ''));
  }

  function shouldDisableMergeCheckbox(groupNo) {
    if (!isRotation) return false;
    const rotationGroupNo = mergeScheduleList.getIn([0, 'rotation_count'], null);
    if (rotationGroupNo === null) return false;
    return rotationGroupNo !== groupNo;
  }

  function getStudentGroupingDetails(sessionFlow) {
    const groupedDataImmutable = sessionFlow
      .groupBy((session) => session.get('group_name'))
      .map((group) => {
        return group.reduce(
          (acc, session) => {
            return acc
              .update('total_students', (total) => total + session.get('total_students', ''))
              .update(
                'selected_students',
                (selected) => selected + session.get('selected_students', '')
              )
              .update('session_group', (symbols) =>
                symbols.push(
                  getFormattedGroupName(
                    studentGroupRename(
                      session.getIn(['session_group', 0, 'group_name'], ''),
                      programId
                    ),
                    3
                  )
                )
              )
              .update('delivery_symbol', (delivery_symbol) => session.get('delivery_symbol', ''));
          },

          fromJS({
            total_students: 0,
            selected_students: 0,
            session_group: [],
            delivery_symbol: '',
          })
        );
      })
      .map((group) => group.update('session_group', (symbols) => symbols.join(', ')));
    return groupedDataImmutable;
  }

  return (
    <div className="min_h p-1">
      {activeViewType === 'regular' && (
        <Table responsive hover>
          <thead className="bg-white thead_border">
            <tr>
              <th>
                <Trans i18nKey={'s_no'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'Delivery_Type'}></Trans>
              </th>

              <th colSpan="2">
                <div className="row">
                  <div className="col-md-4">
                    <Trans i18nKey={'student_group'}></Trans>
                  </div>
                  <div className="col-md-8">
                    <Trans i18nKey={'configuration.subject'}></Trans>
                  </div>
                </div>
              </th>
              <th>
                <Trans i18nKey={'configure_levels.mode'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'user_management.staff'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'infra'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'Date'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'dashboard_view.time'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'schedule'}></Trans>
              </th>
            </tr>
          </thead>
          <tbody>
            {course.get('session_flow', List()).isEmpty() ? (
              <tr>
                <td colSpan="10">
                  <div className="mt-3 mb-3 text-center">
                    <Trans i18nKey={'no_session_flow_found'}></Trans>
                  </div>
                </td>
              </tr>
            ) : (
              course.get('session_flow', List()).map((sessionFlow, index) => {
                const isScheduled = Boolean(
                  sessionFlow.getIn(['schedule', 0, 'schedule_date'], '')
                );
                return (
                  <React.Fragment key={sessionFlow.get('_id')}>
                    <tr>
                      <td>
                        <div className="pt-1 min-width-50">
                          <span alt={isScheduled ? 'scheduled' : 'unscheduled'}>
                            {sessionFlow.get('student_group', List()).size > 0 &&
                            sessionFlow
                              .get('student_group', List())
                              .every((sGroup) => sGroup.get('schedule_status')) ? (
                              <img src={CheckCircleGreenIcon} alt="check" className="pl-1" />
                            ) : (
                              <i
                                className="fa fa-exclamation-circle f-14 text-gray pl-1"
                                aria-hidden="true"
                              ></i>
                            )}
                          </span>
                          <b className="pl-2">{index + 1}</b>
                        </div>
                      </td>
                      <td>
                        <div className="pt-1 max-width-300">
                          {`${sessionFlow.get('delivery_symbol', '')}${sessionFlow.get(
                            'delivery_no',
                            ''
                          )} ${sessionFlow.get('delivery_topic', '')}`}
                        </div>
                      </td>
                      <td colSpan="2">
                        <div>
                          <ListViewStudentGroups
                            studentGroupStatus={studentGroupStatus}
                            isRotation={isRotation}
                            studentGroups={sessionFlow.get('student_group', List())}
                          />
                        </div>
                      </td>
                      <td colSpan="5">
                        <div className="pt-1"></div>
                      </td>
                      <td>
                        {studentGroupStatus && (
                          <div className="d-flex justify-content-between align-items-center">
                            {currentCalendar &&
                              isAllowedToSchedule &&
                              CheckPermission(
                                'subTabs',
                                'Schedule Management',
                                'Course Scheduling',
                                '',
                                'Schedule',
                                '',
                                'Course Schedule',
                                'Add'
                              ) && (
                                <i
                                  className="far fa-calendar-alt text-skyblue cursor-pointer mr-3 ml-3 f-16 calendar-icon"
                                  aria-hidden="true"
                                  onClick={() => handleAddEditSchedule(sessionFlow, 'create')}
                                ></i>
                              )}
                            <i
                              className={`fa fa-chevron-${
                                expanded.get(sessionFlow.get('_id')) ? 'up' : 'down'
                              } ${!currentCalendar ? 'pl-4' : ''} text-skyblue cursor-pointer`}
                              aria-hidden="true"
                              onClick={() =>
                                setExpanded(
                                  expanded.set(
                                    sessionFlow.get('_id'),
                                    !expanded.get(sessionFlow.get('_id'), false)
                                  )
                                )
                              }
                            ></i>
                          </div>
                        )}
                      </td>
                    </tr>
                    {expanded.get(sessionFlow.get('_id')) &&
                      sessionFlow.get('schedule', List()).isEmpty() && (
                        <tr>
                          <td colSpan="10">
                            <div className="mt-3 mb-3 text-center">
                              <Trans i18nKey={'no_schedule_found'}></Trans>
                            </div>
                          </td>
                        </tr>
                      )}
                    {expanded.get(sessionFlow.get('_id')) &&
                      sessionFlow
                        .get('schedule', List())
                        .filter((st) =>
                          status === 'missed'
                            ? st.get('status', '') === 'missed'
                            : status === 'missed to completed'
                            ? st.get('isMissedToComplete', false)
                            : status === 'completed'
                            ? st.get('status', '') === 'completed'
                            : status === 'canceled'
                            ? !st.get('isActive', false)
                            : true
                        )
                        .filter((sg) => {
                          if (studentGroupId !== '') {
                            const studentGroupArray = sg
                              .get('student_groups', List())
                              .map((item) => item.get('group_id', ''))
                              .toJS();
                            return studentGroupArray.includes(studentGroupId);
                          } else {
                            return sg;
                          }
                        })
                        .map((schedule) => {
                          const isScheduleActive = schedule.get('isActive');
                          const isMerged = schedule.get('merge_status');
                          const mergeSchedules = schedule.get('mergedSchedules', List());
                          const checkboxStatus = showMissedToComplete
                            ? showMissedToComplete && schedule.get('status', '') === 'missed'
                            : showRevoke && schedule.get('isMissedToComplete', false);
                          return (
                            <tr
                              key={schedule.get('_id')}
                              className={`bg-gray table-row-item ${
                                isScheduleActive ? '' : 'inactive-schedule'
                              }`}
                            >
                              <td>
                                <div className="d-flex mt-2">
                                  {isMerged ? (
                                    <Tooltip
                                      title={mergeSchedules
                                        .map(
                                          (mergedSchedule) =>
                                            mergedSchedule.get('delivery_symbol', '') +
                                            mergedSchedule.get('delivery_no', '')
                                        )
                                        .join(', ')}
                                      enterDelay={500}
                                      arrow
                                    >
                                      <img src={MergeIcon} alt="merged" />
                                    </Tooltip>
                                  ) : schedule.get('isActive') && showMergeCheckbox ? (
                                    <Form.Check
                                      type="checkbox"
                                      checked={Boolean(
                                        mergeScheduleList.find(
                                          (s) => s.get('_id') === schedule.get('_id')
                                        )
                                      )}
                                      onChange={(event) =>
                                        handleMergeScheduleCheckboxChange(
                                          event.target.checked,
                                          schedule
                                        )
                                      }
                                      disabled={shouldDisableMergeCheckbox(
                                        schedule.get('rotation_count')
                                      )}
                                    />
                                  ) : null}
                                  {schedule.get('isActive', false) && checkboxStatus && (
                                    <Form.Check
                                      className="ml-2"
                                      type="checkbox"
                                      checked={Boolean(
                                        mergeScheduleList.find(
                                          (s) => s.get('_id') === schedule.get('_id')
                                        )
                                      )}
                                      onChange={(event) =>
                                        handleMissedCheckboxChange(event.target.checked, schedule)
                                      }
                                      disabled={shouldDisableMergeCheckbox(
                                        schedule.get('rotation_count')
                                      )}
                                    />
                                  )}
                                </div>
                              </td>
                              <td>
                                <div className="pt-1">{schedule.get('topic', '')}</div>
                                {schedule.get('status', '') === 'missed' &&
                                  schedule.get('isActive', false) && (
                                    <div className="pt-1 text-red">
                                      {capitalize(schedule.get('status', ''))}
                                    </div>
                                  )}
                                {schedule.get('status', '').toLowerCase().includes('completed') &&
                                  schedule.get('isActive', false) && (
                                    <div className="pt-1 bold text-darkGreen">
                                      {capitalize(
                                        schedule.get('status', '').replace('missed to', '')?.trim()
                                      )}
                                    </div>
                                  )}
                              </td>
                              <td colSpan="2">
                                <div>
                                  <div className="row p-1">
                                    <div className="col-md-4">
                                      <p className="f-13 mb-0 pl-1">{`${schedule
                                        .get('student_groups', List())
                                        .map((studentGroup) => {
                                          const studentGroupName = getFormattedGroupName(
                                            studentGroupRename(
                                              studentGroup.get('group_name', ''),
                                              programId
                                            ),
                                            isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                                          );
                                          const sessionGroupNames = studentGroup
                                            .get('session_group', List())
                                            .map((sessionGroup) =>
                                              getFormattedGroupName(
                                                sessionGroup.get('group_name', ''),
                                                3
                                              )
                                            )
                                            .join(', ');
                                          return `${studentGroupName} - ${sessionGroupNames}`;
                                        })
                                        .join(', ')}`}</p>
                                    </div>
                                    <div className="col-md-8">
                                      <div className="d-flex flex-wrap">
                                        <p className="f-13 mb-0">
                                          {schedule
                                            .get('subjects', List())
                                            .map((subject) => subject.get('subject_name'))
                                            .join(', ')}
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td>
                                <div className="pt-1">
                                  {capitalize(indVerRename(schedule.get('mode', ''), programId))}
                                </div>
                              </td>
                              <td>
                                <div className="pt-1">
                                  <div>
                                    {schedule
                                      .get('staffs', List())
                                      .map(
                                        (staff) =>
                                          `${staff.getIn(
                                            ['staff_name', 'first'],
                                            ''
                                          )} ${staff.getIn(
                                            ['staff_name', 'middle'],
                                            ''
                                          )} ${staff.getIn(['staff_name', 'last'], '')}`
                                      )
                                      .join(', ')}
                                  </div>
                                  {isModuleEnabled('OUTSIDE_CAMPUS_V2') && (
                                    <>
                                      <div>
                                        {schedule.get('externalStaff', List()).size > 0 &&
                                          ` (${schedule
                                            .get('externalStaff', List())
                                            .map((externalStaff) => externalStaff.get('name', ''))
                                            .join(', ')})External Staff `}
                                      </div>
                                      <div>
                                        {schedule.get('classLeader', List()).size > 0 && (
                                          <>
                                            (
                                            {schedule
                                              .get('classLeader', List())
                                              .map(
                                                (classLeader) =>
                                                  `${classLeader.getIn(
                                                    ['name', 'first'],
                                                    ''
                                                  )} ${classLeader.getIn(['name', 'middle'], '')}`
                                              )
                                              .join(', ')}
                                            ){`Class Leader `}
                                          </>
                                        )}
                                      </div>
                                    </>
                                  )}
                                </div>
                              </td>
                              <td>
                                <div className="pt-1">{schedule.get('infra_name', '')}</div>
                              </td>
                              <td>
                                <div className="pt-1">{getScheduleDate(schedule)}</div>
                              </td>
                              <td>
                                <div className="pt-1">{getScheduleTime(schedule)}</div>
                              </td>
                              <td>
                                <div className="pt-1 d-flex justify-content-between table-action-button-container">
                                  {currentCalendar &&
                                    isAllowedToSchedule &&
                                    CheckPermission(
                                      'subTabs',
                                      'Schedule Management',
                                      'Course Scheduling',
                                      '',
                                      'Schedule',
                                      '',
                                      'Course Schedule',
                                      'Edit'
                                    ) && (
                                      <Tooltip
                                        title={
                                          isScheduleActive ? (
                                            'Edit Schedule'
                                          ) : (
                                            <span>
                                              Editing a cancelled schedule is not allowed. Instead,
                                              click{' '}
                                              <i
                                                className="fa fa-ellipsis-v pl-1 pr-1"
                                                aria-hidden="true"
                                              ></i>{' '}
                                              and Re-Assign.
                                            </span>
                                          )
                                        }
                                        enterDelay={500}
                                        arrow
                                      >
                                        <i
                                          className={`fa fa-pencil mt-1 ml-3 cursor-${
                                            isScheduleActive ? 'pointer' : 'not-allowed'
                                          }`}
                                          aria-hidden="true"
                                          onClick={() => {
                                            if (isScheduleActive) {
                                              if (isMerged) {
                                                handleEditMergeSchedule(schedule, sessionFlow);
                                              } else {
                                                handleAddEditSchedule(
                                                  sessionFlow,
                                                  'update',
                                                  schedule
                                                );
                                              }
                                            }
                                          }}
                                        ></i>
                                      </Tooltip>
                                    )}
                                  {currentCalendar &&
                                    isAllowedToSchedule &&
                                    (CheckPermission(
                                      'subTabs',
                                      'Schedule Management',
                                      'Course Scheduling',
                                      '',
                                      'Schedule',
                                      '',
                                      'Course Schedule',
                                      'Cancel/Re-Assign'
                                    ) ||
                                      CheckPermission(
                                        'subTabs',
                                        'Schedule Management',
                                        'Course Scheduling',
                                        '',
                                        'Schedule',
                                        '',
                                        'Course Schedule',
                                        'Delete'
                                      )) && (
                                      <small className="f-18 mr-1">
                                        <Dropdown>
                                          <Dropdown.Toggle
                                            variant=""
                                            id="dropdown-table"
                                            className="table-dropdown"
                                            size="sm"
                                          >
                                            <div className="f-16">
                                              <i
                                                className="fa fa-ellipsis-v"
                                                aria-hidden="true"
                                              ></i>
                                            </div>
                                          </Dropdown.Toggle>
                                          <Dropdown.Menu renderOnMount={false}>
                                            {isAllowedToSchedule &&
                                              CheckPermission(
                                                'subTabs',
                                                'Schedule Management',
                                                'Course Scheduling',
                                                '',
                                                'Schedule',
                                                '',
                                                'Course Schedule',
                                                'Cancel/Re-Assign'
                                              ) && (
                                                <Dropdown.Item
                                                  disabled={isMerged}
                                                  onClick={() => {
                                                    if (isScheduleActive) {
                                                      handleCancelSchedule(
                                                        { sessionFlow, schedule },
                                                        false
                                                      );
                                                    } else {
                                                      handleAddEditSchedule(
                                                        sessionFlow,
                                                        'update',
                                                        schedule,
                                                        undefined,
                                                        true,
                                                        true
                                                      );
                                                    }
                                                  }}
                                                >
                                                  {isScheduleActive ? 'Cancel' : 'Re-Assign'}
                                                </Dropdown.Item>
                                              )}
                                            {isAllowedToSchedule &&
                                              CheckPermission(
                                                'subTabs',
                                                'Schedule Management',
                                                'Course Scheduling',
                                                '',
                                                'Schedule',
                                                '',
                                                'Course Schedule',
                                                'Delete'
                                              ) && (
                                                <Dropdown.Item
                                                  disabled={isMerged}
                                                  onClick={() =>
                                                    handleDeleteSchedule(
                                                      { sessionFlow, schedule },
                                                      false
                                                    )
                                                  }
                                                >
                                                  <Trans i18nKey={'delete'}></Trans>
                                                </Dropdown.Item>
                                              )}
                                          </Dropdown.Menu>
                                        </Dropdown>
                                      </small>
                                    )}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                    {checkIfHasNextPage() && <tr ref={infiniteRef} />}
                  </React.Fragment>
                );
              })
            )}
          </tbody>
        </Table>
      )}
      {['events', 'support'].includes(activeViewType) && (
        <div className="events-support-session-table-wrapper">
          <Table responsive hover>
            <thead className="bg-white thead_border">
              <tr>
                <th>
                  <Trans i18nKey={'s_no'}></Trans>
                </th>
                <th>
                  {activeViewType === 'events' ? t('event') : t('session')}
                  <Trans i18nKey={'title'}></Trans>
                </th>
                <th className="w-300px">
                  <Trans i18nKey={'student_group'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'type'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'configuration.subject'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'configure_levels.mode'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'user_management.staff'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'infra'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'Date'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'dashboard_view.time'}></Trans>
                </th>
                <th>
                  <Trans i18nKey={'schedule'}></Trans>
                </th>
              </tr>
            </thead>
            <tbody>
              {course.get('session_flow', List()).isEmpty() ? (
                <tr>
                  <td colSpan="11">
                    <div className="mt-3 mb-3 text-center">
                      <i className="fa fa-exclamation-circle text-gray mr-1" aria-hidden="true"></i>
                      {`${activeViewType === 'events' ? 'Events' : 'Support session'} not created`}
                      {currentCalendar && isAllowedToSchedule && (
                        <div className="mt-3">
                          <span
                            className="cursor-pointer"
                            style={{ color: '#3E95EF' }}
                            onClick={() => {
                              if (addEventsAndSupportSessionRef) {
                                addEventsAndSupportSessionRef.current.click();
                              }
                            }}
                          >
                            Add
                          </span>{' '}
                          {activeViewType === 'events' ? 'Events' : 'Support session'}
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ) : (
                course.get('session_flow', List()).map((sessionFlow, index) => {
                  const isScheduleActive = sessionFlow.get('isActive');
                  const isScheduled = Boolean(sessionFlow.get('schedule_date'));
                  return (
                    <React.Fragment key={sessionFlow.get('_id')}>
                      <tr className={isScheduleActive ? '' : 'inactive-schedule'}>
                        <td>
                          <div className="pt-1 min-width-50">
                            <span>
                              {isScheduled ? (
                                <img src={CheckCircleGreenIcon} alt="check" className="pl-1" />
                              ) : (
                                <i
                                  className="fa fa-exclamation-circle f-14 text-gray pl-1"
                                  aria-hidden="true"
                                ></i>
                              )}
                            </span>
                            <b className="pl-2">{index + 1}</b>
                          </div>
                        </td>
                        <td>
                          <div className="pt-1 max-width-300 word-wrap-break-word">
                            {sessionFlow.get('title', '') || 'N/A'}
                            <div>
                              {sessionFlow.get('status', '') === 'missed' &&
                                sessionFlow.get('isActive', false) && (
                                  <div className="pt-1 text-red">
                                    {capitalize(sessionFlow.get('status', ''))}
                                  </div>
                                )}
                              {sessionFlow.get('status', '').toLowerCase().includes('completed') &&
                                sessionFlow.get('isActive', false) && (
                                  <div className="pt-1 bold text-darkGreen">
                                    {capitalize(
                                      sessionFlow.get('status', '').replace('missed to', '')?.trim()
                                    )}
                                  </div>
                                )}
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex bg-gray border-radious-8">
                            <div className="mr-1 mt-1 flex-basis-35">
                              <Trans i18nKey={'student_groups'}></Trans>
                            </div>
                            <div className="mr-1 mt-1 d-flex flex-wrap flex-basis-65">
                              {sessionFlow.get('type', '') !== 'regular'
                                ? studentGroupViewList(
                                    sessionFlow.get('student_groups', List()),
                                    programId
                                  )
                                    .entrySeq()
                                    .map(([groupName, sGroup]) => {
                                      return (
                                        <div key={groupName} className="mr-2 mb-1">
                                          <div className="icon_schedule">
                                            {getFormattedGroupName(
                                              studentGroupRename(groupName, programId),
                                              2
                                            )}
                                            {sGroup.get('delivery_symbol', '') !== '' &&
                                              `- 
                                      ${sGroup.get('session_group')}`}
                                            - {sGroup.get('selected_students')} /
                                            {sGroup.get('total_students')}
                                          </div>
                                        </div>
                                      );
                                    })
                                : getStudentGroupingDetails(
                                    sessionFlow.get('student_groups', List())
                                  )}
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="pt-1">{getSubType(sessionFlow)}</div>
                        </td>
                        <td>
                          <div className="pt-1 max-width-300">
                            {sessionFlow
                              .get('subjects', List())
                              .map((subject) => subject.get('subject_name'))
                              .join(', ')}
                          </div>
                        </td>
                        <td>
                          <div className="pt-1">
                            {capitalize(indVerRename(sessionFlow.get('mode', ''), programId))}
                          </div>
                        </td>
                        <td>
                          <div className="pt-1">
                            {sessionFlow
                              .get('staffs', List())
                              .map(
                                (staff) =>
                                  `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
                                    ['staff_name', 'middle'],
                                    ''
                                  )} ${staff.getIn(['staff_name', 'last'], '')}`
                              )
                              .join(', ')}
                          </div>
                        </td>
                        <td>
                          <div className="pt-1">{sessionFlow.get('infra_name', '')}</div>
                        </td>
                        <td>
                          <div className="pt-1">{getScheduleDate(sessionFlow)}</div>
                        </td>
                        <td>
                          <div className="pt-1">{getScheduleTime(sessionFlow)}</div>
                        </td>
                        <td>
                          <div className="pt-1 d-flex justify-content-between">
                            {currentCalendar &&
                              isAllowedToSchedule &&
                              CheckPermission(
                                'subTabs',
                                'Schedule Management',
                                'Course Scheduling',
                                '',
                                'Schedule',
                                '',
                                'Course Schedule',
                                isScheduled ? 'Edit' : 'Add'
                              ) && (
                                <i
                                  className={`ml-3 cursor-${
                                    isScheduleActive ? 'pointer' : 'not-allowed'
                                  } ${
                                    isScheduled
                                      ? 'fa fa-pencil mt-1 '
                                      : 'far fa-calendar-alt text-skyblue calendar-icon'
                                  }`}
                                  aria-hidden="true"
                                  onClick={() => {
                                    if (isScheduleActive) {
                                      handleAddEditSupportAndEvents({
                                        schedule: sessionFlow,
                                        mode: isScheduled ? 'update' : 'create',
                                      });
                                    }
                                  }}
                                ></i>
                              )}
                            {currentCalendar && isAllowedToSchedule && (
                              <small className="f-18 mr-1">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    variant=""
                                    id="dropdown-table"
                                    className="table-dropdown"
                                    size="sm"
                                  >
                                    <div className="f-16">
                                      <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                                    </div>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu renderOnMount={false}>
                                    {isAllowedToSchedule &&
                                      (isScheduled ||
                                        CheckPermission(
                                          'subTabs',
                                          'Schedule Management',
                                          'Course Scheduling',
                                          '',
                                          'Schedule',
                                          '',
                                          'Course Schedule',
                                          'Edit'
                                        )) && (
                                        <Dropdown.Item
                                          onClick={() => {
                                            const type = sessionFlow.get('type');
                                            handleStudentGroupModalOpen(
                                              type === 'support_session'
                                                ? 'support'
                                                : sessionFlow.get('sub_type'),
                                              {
                                                readOnly: false, // readOnly: isScheduled,
                                                session: sessionFlow,
                                                mode: 'update',
                                              }
                                            );
                                          }}
                                        >
                                          {isScheduled ? t('edit') : t('edit')}
                                        </Dropdown.Item>
                                      )}
                                    {isAllowedToSchedule &&
                                      CheckPermission(
                                        'subTabs',
                                        'Schedule Management',
                                        'Course Scheduling',
                                        '',
                                        'Schedule',
                                        '',
                                        'Course Schedule',
                                        'Cancel/Re-Assign'
                                      ) && (
                                        <Dropdown.Item
                                          onClick={() => {
                                            if (isScheduleActive) {
                                              handleCancelDeleteSupportAndEventsSchedule(
                                                { schedule: sessionFlow, mode: 'cancel' },
                                                false
                                              );
                                            } else {
                                              handleAddEditSupportAndEvents({
                                                schedule: sessionFlow,
                                                mode: 'update',
                                                isReassign: true,
                                              });
                                            }
                                          }}
                                          disabled={!isScheduled}
                                        >
                                          {isScheduleActive ? t('cancel') : t('re-assign')}
                                        </Dropdown.Item>
                                      )}
                                    {isAllowedToSchedule &&
                                      CheckPermission(
                                        'subTabs',
                                        'Schedule Management',
                                        'Course Scheduling',
                                        '',
                                        'Schedule',
                                        '',
                                        'Course Schedule',
                                        'Delete'
                                      ) && (
                                        <Dropdown.Item
                                          onClick={() =>
                                            handleCancelDeleteSupportAndEventsSchedule(
                                              { schedule: sessionFlow, mode: 'delete' },
                                              false
                                            )
                                          }
                                        >
                                          <Trans i18nKey={'delete'}></Trans>
                                        </Dropdown.Item>
                                      )}
                                  </Dropdown.Menu>
                                </Dropdown>
                              </small>
                            )}
                          </div>
                        </td>
                      </tr>
                      {checkIfHasNextPage() && <tr ref={infiniteRef} />}
                    </React.Fragment>
                  );
                })
              )}
            </tbody>
          </Table>
        </div>
      )}
    </div>
  );
}

ScheduleListView.propTypes = {
  activeViewType: PropTypes.string,
  course: PropTypes.instanceOf(Map),
  isAllowedToSchedule: PropTypes.bool,
  handleAddEditSchedule: PropTypes.func,
  studentGroupStatus: PropTypes.bool,
  fetchCourseSchedule: PropTypes.func,
  paginationMetaData: PropTypes.instanceOf(Map),
  isLoading: PropTypes.bool,
  handleDeleteSchedule: PropTypes.func,
  handleCancelSchedule: PropTypes.func,
  handleMergeScheduleCheckboxChange: PropTypes.func,
  showMergeCheckbox: PropTypes.bool,
  mergeScheduleList: PropTypes.instanceOf(List),
  handleEditMergeSchedule: PropTypes.func,
  handleAddEditSupportAndEvents: PropTypes.func,
  handleCancelDeleteSupportAndEventsSchedule: PropTypes.func,
  handleStudentGroupModalOpen: PropTypes.func,
  addEventsAndSupportSessionRef: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.shape({ current: PropTypes.instanceOf(Element) }),
  ]),
  supportSessionTypes: PropTypes.array,
  isRotation: PropTypes.bool,
  studentGroupId: PropTypes.string,
  currentCalendar: PropTypes.bool,
  programId: PropTypes.string,
  showMissedToComplete: PropTypes.bool,
  showRevoke: PropTypes.bool,
  status: PropTypes.string,
  handleMissedCheckboxChange: PropTypes.func,
};

export default ScheduleListView;
