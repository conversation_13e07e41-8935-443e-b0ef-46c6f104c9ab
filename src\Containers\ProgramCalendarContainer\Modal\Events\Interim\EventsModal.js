import React, { Fragment, useReducer, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { connect } from 'react-redux';

import {
  toggleInterimModal,
  saveInterimEvents,
  saveSynInterimEvents,
} from '../../../../../_reduxapi/actions/interimCalendar';

import { FlexWrapper, PrimaryButton, Null, BlockWrapper } from '../../../Styled';
import { initial_event_state, events_reducer } from './reducers';
import AddEvent from '../AddEvent';
import SyncEvent from '../SyncEvent';
import { NotificationManager } from 'react-notifications';
import moment from 'moment';
import { indVerRename, timeFormat } from '../../../../../utils';
import { selectActiveInstitutionCalendar } from '../../../../../_reduxapi/Common/Selectors';

import { Trans } from 'react-i18next';
import LocalStorageService from 'LocalStorageService';
const dataAlign = (
  data,
  active,
  dispatchFn,
  type,
  regularBatch,
  interimBatch,
  currentProgramCalendarId,
  academicStartDate,
  academicEndDate,
  sem_data,
  activeInstitutionCalendar
) => {
  let events = [...sem_data[1]['events']];
  let error = false;
  let final = { event_name: {} };

  let batch = 'interim';
  if (regularBatch === true && interimBatch === true) {
    batch = 'both';
    events = [...sem_data[0]['events'], ...sem_data[1]['events']];
  } else if (regularBatch === true) {
    batch = 'regular';
    events = [...sem_data[0]['events']];
  }
  if (type === 'manual') {
    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    const check_start = st;
    const check_end = et;

    events.forEach((item) => {
      if (Date.parse(item.start_time) <= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_start)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) <= Date.parse(check_end) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.start_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.end_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      }
    });

    if (regularBatch === false && interimBatch === false) {
      NotificationManager.error('Term is required');
      error = true;
    } else if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.start_date) > Date.parse(data.end_date)) {
      error = true;
      NotificationManager.error('End date should be greater than start date');
    }
    // else if (Date.parse(academicStartDate) > Date.parse(data.start_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Level start date should be greater than or equal to Academic start date"
    //   );
    // } else if (Date.parse(academicEndDate) < Date.parse(data.start_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Level start date should be lesser than or equal to Academic end date"
    //   );
    // } else if (Date.parse(academicEndDate) < Date.parse(data.end_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Level end date should be lesser than or equal to Academic end date"
    //   );
    // }

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    final.event_calendar = 'program';
    final.year = active;
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final.end_date = endDate;
    final._calendar_id = currentProgramCalendarId;
    final.batch = batch;
  } else {
    let syncData = {};
    if (regularBatch === false && interimBatch === false) {
      NotificationManager.error('Term is required');
      error = true;
    } else if (data.length > 0) {
      syncData._calendar_id = currentProgramCalendarId;
      syncData.year = active;
      syncData.batch = batch;
      let eventIdGrouping = data.map((event) => {
        return event._id;
      });
      syncData._event_id = eventIdGrouping;
      final = syncData;
    } else {
      NotificationManager.error('Choose atleast one events');
      error = true;
    }
  }
  if (!error) {
    dispatchFn(final, activeInstitutionCalendar.get('_id'));
  }
};

const EventsInterimModal = (props) => {
  const {
    events,
    active,
    toggleInterimModal,
    saveInterimEvents,
    saveSynInterimEvents,
    currentProgramCalendarId,
    academicStartDate,
    academicEndDate,
    active_semester,
    activeInstitutionCalendar,
  } = props;
  const [event_method, setEvent_method] = useReducer(events_reducer, initial_event_state);

  const [regularBatch, setRegularBatch] = useState(true);
  const [interimBatch, setInterimBatch] = useState(true);

  useEffect(() => {
    setEvent_method({ type: 'SOURCE_FOR_COPY', payload: events });
  }, [events]);
  const levelStartDate = props?.[active]?.['semesters']?.[active_semester]?.[0]?.start_date;
  const levelEndDate =
    props[active]['semesters'][active_semester][1].end_date !== undefined &&
    props[active]['semesters'][active_semester][1].end_date !== ''
      ? props[active]['semesters'][active_semester][1].end_date
      : props[active]['semesters'][active_semester][0].end_date;
  const programId = LocalStorageService.getCustomToken('programId');
  return (
    <Fragment>
      <BlockWrapper>
        <h3 className="text-left">
          <Trans i18nKey={'role_management.role_actions.Add Event'}></Trans>
        </h3>
        <p className="text-left">
          <Trans i18nKey={'select_date_to_sync'}></Trans>
        </p>

        <FlexWrapper mg="20px 0">
          <input
            type="checkbox"
            name="regularBatch"
            value={regularBatch}
            checked={regularBatch === true}
            onChange={(e) => setRegularBatch(!regularBatch)}
            id="manual"
          />
          <label htmlFor="manual">
            {' '}
            <Trans
              i18nKey={'program_calendar.regular_term'}
              values={{ Term: indVerRename('Term', programId) }}
            ></Trans>
          </label>
          &nbsp;&nbsp;&nbsp;
          <input
            type="checkbox"
            name="interimBatch"
            value={interimBatch}
            checked={interimBatch === true}
            onChange={(e) => setInterimBatch(!interimBatch)}
            id="manual"
          />
          <label htmlFor="manual">
            {' '}
            <Trans
              i18nKey={'program_calendar.interim_term'}
              values={{ Term: indVerRename('Term', programId) }}
            ></Trans>
          </label>
        </FlexWrapper>
      </BlockWrapper>
      <FlexWrapper mg="20px 0">
        <input
          type="radio"
          name="event_mode"
          value="manual"
          id="manual"
          checked={event_method.event_mode === 'manual'}
          onChange={(e) =>
            setEvent_method({
              type: 'EVENT_MODE_CHANGE',
              payload: e.target.value,
              name: e.target.name,
            })
          }
        />
        <label htmlFor="manual">
          {' '}
          <Trans i18nKey={'add_event_manually'}></Trans>
        </label>
      </FlexWrapper>
      <Fragment>
        {event_method.event_mode === 'manual' && (
          <BlockWrapper>
            <AddEvent
              data={event_method.type}
              method={setEvent_method}
              min_len={academicStartDate}
              max_len={academicEndDate}
              levelStartDate={levelStartDate}
              levelEndDate={levelEndDate}
            />
          </BlockWrapper>
        )}
      </Fragment>
      <FlexWrapper mg="20px 0">
        <input
          type="radio"
          name="event_mode"
          id="sync"
          value="sync"
          checked={event_method.event_mode === 'sync'}
          onChange={(e) =>
            setEvent_method({
              type: 'EVENT_MODE_CHANGE',
              payload: e.target.value,
              name: e.target.name,
            })
          }
        />
        <label htmlFor="sync">
          <Trans i18nKey={'add_events_from_institution_calendar'}></Trans>
        </label>
      </FlexWrapper>
      <Fragment>
        {event_method.event_mode === 'sync' &&
        event_method.source &&
        event_method.source.length !== 0 ? (
          <BlockWrapper>
            <SyncEvent data={event_method.source} method={setEvent_method} />
          </BlockWrapper>
        ) : event_method.event_mode === 'sync' ? (
          'No Data Found...'
        ) : (
          ''
        )}
      </Fragment>
      <FlexWrapper className="ji_end">
        <Null />
        <PrimaryButton className="light" onClick={() => toggleInterimModal()}>
          cancel
        </PrimaryButton>
        <PrimaryButton
          className="bordernone"
          onClick={() => {
            if (event_method.event_mode === 'manual') {
              dataAlign(
                event_method.type,
                active,
                saveInterimEvents,
                'manual',
                regularBatch,
                interimBatch,
                currentProgramCalendarId,
                academicStartDate,
                academicEndDate,
                props[active]['semesters'][active_semester],
                activeInstitutionCalendar
              );
            } else {
              dataAlign(
                event_method.copied,
                active,
                saveSynInterimEvents,
                'sync',
                regularBatch,
                interimBatch,
                currentProgramCalendarId,
                academicStartDate,
                academicEndDate,
                props[active]['semesters'][active_semester],
                activeInstitutionCalendar
              );
            }
          }}
        >
          save
        </PrimaryButton>
      </FlexWrapper>
    </Fragment>
  );
};

EventsInterimModal.propTypes = {
  events: PropTypes.array,
  active: PropTypes.object,
  toggleInterimModal: PropTypes.func,
  saveSynInterimEvents: PropTypes.func,
  saveInterimEvents: PropTypes.string,
  currentProgramCalendarId: PropTypes.string,
  academicStartDate: PropTypes.string,
  academicEndDate: PropTypes.string,
  active_semester: PropTypes.array,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  // = ({ interimCalendar }) => ({

  const { interimCalendar } = state;
  return {
    events: interimCalendar.academic_year_events,
    active: interimCalendar.active_year,
    id: interimCalendar.institution_Calender_Id,
    currentProgramCalendarId: interimCalendar.currentProgramCalendarId,
    academicStartDate: interimCalendar.academic_start_date,
    academicEndDate: interimCalendar.academic_end_date,
    active_semester: interimCalendar.active_semesters,
    year1: interimCalendar.year1,
    year2: interimCalendar.year2,
    year3: interimCalendar.year3,
    year4: interimCalendar.year4,
    year5: interimCalendar.year5,
    year6: interimCalendar.year6,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, {
  toggleInterimModal,
  saveInterimEvents,
  saveSynInterimEvents,
})(EventsInterimModal);
