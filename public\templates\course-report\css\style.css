body{
  padding: 32px;
  background-color: rgb(241 241 241);
}

  #section-c table {
    border-collapse: collapse;
    width: 100%;
    font-family: Arial, sans-serif;
  }
  
  #section-c table th, td {
    border: 1px solid #ddd;
    text-align: center;
    padding: 8px;
  }
  
  #section-c table tr:nth-child(even) {
    background-color: #f2f2f2;
  }
  
  #section-c table th {
    background-color: rgb(148 152 203);
    color: white;
  }
  
 .left-header {
    background-color:rgb(76 61 142) !important;
    color: white;
  }




  #section-d  .container {
    background-color: #fff;
    margin: 20px auto;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 80%;
    max-width: 800px;
  }
  
  #section-d  .container  h2 {
    color: #333;
  }
  
  #section-d  .container p {
    color: #555;
    line-height: 1.6;
  }
  
  #section-d  .highlight {
    color: #007bff;
  }

  
  #section-f {
    /* background-color: #f7f7f7; */
    font-family: Arial, sans-serif;
}

#section-f  h2, h3 {
    margin-bottom: 10px;
}
#section-f  h2 {
    color: #00b3b3;
}

#section-f .table-header-color{
  color: #525252
  ;
}
#section-f  h3 {
    color: #4b0082;
}
#section-f .section-f-content {
    margin-bottom: 40px;
}
#section-f .box {
    border: 1px solid #ffffff;
    padding: 20px;
    margin-top: 10px;
}
#section-f  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}
#section-f  th, td {
    border: 1px solid #ffffff;
    padding: 10px;
    text-align: left;
    
}
#section-f  th {
    text-align: center !important;

}
/* #section-f th {
    background-color: #f7f7f7;
} */
#section-f .note {
    font-size: 12px;
    color: #808080;
    margin-top: 10px;
}


  
  .purple {
    background-color: #9077C0;
  }
  
  .light-purple {
    background-color: #C2B2D1;
  }
  
  .teal {
    background-color: #45B39D;
  }
  
  .light-teal {
    background-color: #90D4C6;
  }

.border-purple-custom-100{
  border:1px solid rgb(147 139 185);
}

.text-blue-custom-100{
  color: rgb(87 125 189);
}

.sections-header{
  color:rgb(88 74 149)
}

.sub-header-bg{
background-color: rgb(148 152 203) !important;
}

.text-teal-500{
  color: #52b5c2 !important;
}

.text-teal-bg-500{
  background-color: #52b5c3 !important;
}

/* #section-e .main-header{
  background-color: ;
} */

.header-image{
  width: -webkit-fill-available;
}

.template-details-border{
  border-color: #948bb9;
  border-width: 1px;
}

.user-input-italic{
  border: none;
  margin-left: 16px;
  min-width:350px;
}