import { makeStyles } from '@mui/styles';

export const useStylesFunction = makeStyles(() => ({
  BackgroundNone: {
    boxShadow: 'none',
    border: '1px solid #dee2e6',
    background: '#ffff !important',
    '& .MuiAccordionSummary-content.Mui-expanded': {
      margin: '2px 0 !important',
    },
  },
  accordionBackgroundNone: {
    boxShadow: 'none',
    background: '#ffff !important',
    borderRadius: 'unset !important',
    borderBottom: 'none !important',
    '& .MuiAccordionSummary-content.Mui-expanded': {
      margin: '10px 0 !important',
    },
  },
  marginReduce: {
    margin: '0px 0 !important',
  },
  BackgroundRed: {
    boxShadow: 'none',
    border: '1px solid red',
    background: '#ffff !important',
    '& .MuiAccordionSummary-content.Mui-expanded': {
      margin: '2px 0 !important',
    },
  },
}));
