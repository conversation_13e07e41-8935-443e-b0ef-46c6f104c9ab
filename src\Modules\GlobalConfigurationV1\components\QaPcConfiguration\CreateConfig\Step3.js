import React, { useEffect, useState } from 'react';
import { Checkbox, Divider, Drawer, MenuItem, Paper, Radio, Select, Tooltip } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';

import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MaterialInput from 'Widgets/FormElements/material/Input';
import CircleIcon from '@mui/icons-material/Circle';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Map, List, fromJS } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import { selectRoleUserListQ360 } from '_reduxapi/global_configuration/v1/selectors';
import { roleUserListQ360, setData } from '_reduxapi/global_configuration/v1/actions';
import AddIcon from '@mui/icons-material/Add';
import LocalStorageService from 'LocalStorageService';
const paddingSmall = {
  '& .MuiInputBase-input.MuiOutlinedInput-input': { padding: '2.5px 5px', textAlign: 'center' },
};
const programTypeNumber = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((time) => ({
  value: time,
  name: `${time < 10 ? '0' : ''}${time}`,
}));

export default function Step3({ state, setState: setApproverLevel }) {
  const dispatch = useDispatch();
  const RoleUserList = useSelector(selectRoleUserListQ360);
  const [drawer, setDrawer] = useState(Map());
  const toggleDrawer = (level) => () => {
    setDrawer(level);
  };
  const [userRole, setRoleData] = useState(Map());
  useEffect(() => {
    const isRoleNotEmpty =
      RoleUserList.get('roleList', Map()).size && RoleUserList.get('roleMap', Map());
    if (!isRoleNotEmpty) dispatch(roleUserListQ360());
  }, []);
  useEffect(() => {
    let updatedRoleList = Map();
    let updatedRoleMap = Map();

    RoleUserList.get('roleData', List()).forEach((element) => {
      const UserId = element.get('_id', '');
      const name = element.get('name', '');
      updatedRoleList = updatedRoleList.set(UserId, Map({ name }));
    });
    RoleUserList.get('roleUserList', List()).forEach((element) => {
      element.get('roles', List()).forEach((data) => {
        const roleId = data.getIn(['_role_id', '_id'], '');
        const userId = element.getIn(['_user_id', '_id'], '');
        updatedRoleList = updatedRoleList.updateIn([roleId, 'users'], List(), (usersList) =>
          usersList.push(`${roleId}+${userId}`)
        );
      });
      const userId = element.getIn(['_user_id', '_id'], '');
      const name = element.getIn(['_user_id', 'name'], '');
      const academicId = element.getIn(['_user_id', 'user_id'], '');
      updatedRoleMap = updatedRoleMap.set(userId, Map({ name, academicId, userId }));
    });

    setRoleData((prevState) =>
      prevState.merge({
        roleList: updatedRoleList,
        roleMap: updatedRoleMap,
      })
    );
  }, [RoleUserList]);
  const addPaper = (levelIndex) => {
    setApproverLevel((prev) =>
      prev.update('approvalLevel', Map(), (approvalLevel) =>
        approvalLevel.splice(
          levelIndex + 1,
          0,
          fromJS({
            level: levelIndex + 2,
            name: `Level ${levelIndex + 2} Hierarchy`,
          })
        )
      )
    );
  };
  const onSave = (data, index) => {
    setApproverLevel((prev) =>
      prev.updateIn(['approvalLevel', index], Map(), (approver) => approver.merge(data))
    );
    setDrawer(Map());
  };
  const handleDeleteApproval = (value) => {
    setApproverLevel((prev) => prev.deleteIn(['approvalLevel', value + 1]));
  };
  return (
    <>
      <div className="d-flex justify-content-center ">
        <div className="min-width-400 position-relative">
          {state.get('approvalLevel', List()).map((level, levelIndex) => {
            return (
              <>
                <div key={levelIndex} className="mt-1">
                  <section
                    className="border rounded w-100 text-center cursor-pointer"
                    onClick={toggleDrawer(level.set('index', levelIndex))}
                  >
                    {level.get('level', 0) === 1 && (
                      <div className="py-1 bg-primary text-white">Approval Hierarchy</div>
                    )}
                    <section className="d-flex f-16 fw-500 p-3 align-items-center bg-white">
                      <div className="flex-grow-1 text-left">{`${level.get('name', '')}`}</div>
                      {/* <ArrowForwardIosOutlinedIcon sx={{ fontSize: '10px' }} /> */}
                    </section>
                    <section className="d-flex align-items-center p-3 gap-8 bg-white">
                      {level.get('selectedRoleUser', List()).size !== 0 && (
                        <>
                          <div className="f-12 fw-400">Approval Role:</div>
                          <span className="f-12 fw-400">
                            {level.get('selectedRoleUser', List()).size}
                          </span>
                          <Tooltip
                            title={
                              <div className="d-flex align-items-center">
                                {level
                                  .get('selectedRoleUser', List())
                                  .map((data, index) => {
                                    return data
                                      .get('userIds', List())
                                      .map(
                                        (user) =>
                                          `${user.getIn(['name', 'first'], '')} ${user.getIn(
                                            ['name', 'last'],
                                            ''
                                          )}`
                                      )
                                      .join(',');
                                  })
                                  .join(',')}
                              </div>
                            }
                          >
                            <InfoOutlinedIcon sx={{ fontSize: '10px' }} />
                          </Tooltip>

                          <Divider orientation="vertical" flexItem />
                        </>
                      )}
                      {level.get('turnAroundTime', '') !== '' && (
                        <>
                          <div className="f-12 fw-400">
                            TAT: {level.get('turnAroundTime', '')} Days
                          </div>
                          <Divider orientation="vertical" flexItem />
                        </>
                      )}
                      {level.get('category', '') !== '' && (
                        <>
                          <div className="f-12 fw-400">{level.get('category', '')}</div>
                        </>
                      )}
                    </section>
                  </section>
                  <div className="d-flex align-items-center justify-content-between w-25 add-button position-relative flex-column div-arrow m-auto">
                    <CircleIcon
                      sx={{ fontSize: 3, color: '#D1D5DB' }}
                      className="position-relative z-1 cursor-pointer"
                    />
                    <CircleIcon
                      sx={{ fontSize: 11, color: '#D1D5DB' }}
                      className="position-Ob-tp z-1 cursor-pointer"
                    />
                    <Paper
                      className="d-flex position-relative z-1 cursor-pointer"
                      sx={{ borderRadius: '50px', backgroundColor: '#F59E0B' }}
                      onClick={() => addPaper(levelIndex)}
                    >
                      <AddIcon sx={{ color: '#FFFFFF' }} className="py-1" />
                    </Paper>
                    <ArrowDropDownIcon
                      sx={{ fontSize: 3, color: '#D1D5DB' }}
                      className="position-relative z-1 cursor-pointer"
                    />
                    <ArrowDropDownIcon
                      sx={{ fontSize: 30, color: '#D1D5DB' }}
                      className="position-Ob-bm z-1 cursor-pointer"
                    />
                  </div>
                  {/* <section className="w-100 text-center arrowIcon"></section> */}
                </div>
                {state.get('approvalLevel', List()).size - 1 !== levelIndex && (
                  <div
                    className="level-approval"
                    onClick={() => handleDeleteApproval(levelIndex)}
                  />
                )}
              </>
            );
          })}
        </div>
      </div>
      {drawer.size ? (
        <DrawerComponent
          toggleDrawer={toggleDrawer}
          drawer={drawer}
          userRole={userRole}
          onSave={onSave}
        />
      ) : (
        <></>
      )}
    </>
  );
}

const drawerPaperW40 = {
  '& .MuiDrawer-paper': { width: '55em' },
};

const DrawerComponent = ({ toggleDrawer, drawer, userRole, onSave }) => {
  const [state, setState] = useState(drawer);
  const sections = LocalStorageService.getCustomToken('sections', true);
  useEffect(() => {
    if (drawer.size) {
      setState((prev) => {
        let checkedIds = List();
        if (drawer.get('selectedRoleUser', List()).size) {
          drawer.get('selectedRoleUser', List()).forEach((role) => {
            if (role.get('userIds', List()) && role.get('userIds', List()))
              role.get('userIds', List()).forEach((user) => {
                checkedIds = checkedIds.push(
                  `${role.get('role_Id', '')}+${user.get('userId', '')}`
                );
              });
          });
        }
        return prev.set('checkedIds', checkedIds);
      });
    }
  }, [drawer]);
  const [expanded, setExpanded] = useState(-1);
  const onClickAccordion = (index) => () => {
    const open = index === expanded ? -1 : index;
    setExpanded(open);
  };
  const handleApproverLevelNameChange = (event) => {
    const newValue = event.target.value;
    setState((prevState) => prevState.set('name', newValue));
  };
  const handleSearchInputChange = (event) => {
    const newValue = event.target.value;
    setState((prevState) => prevState.set('searchInput', newValue));
  };
  const handleChangeRoleListChecked = (event, value) => {
    const UsersData = value.get('users');
    const isChecked = event.target.checked;
    // const userRoleData = userRole.get('roleMap', List());
    if (isChecked) {
      if (UsersData && UsersData.size !== 0) {
        setState((prevState) =>
          prevState.update('checkedIds', List(), (ides) => {
            return ides.concat(UsersData);
          })
        );
      } else {
        setData('No Data');
      }
    } else {
      setState((prevState) => prevState.set('checkedIds', List()));
    }
  };
  const handleChangeRoleChecked = (event, userId) => {
    const isChecked = event.target.checked;
    if (isChecked) {
      if (userId !== '') {
        setState((prevState) => prevState.update('checkedIds', List(), (ids) => ids.push(userId)));
      }
    } else {
      setState((prevState) =>
        prevState.update('checkedIds', List(), (ids) => ids.filter((id) => id !== userId))
      );
    }
  };
  const handleClearAll = () => {
    setState((prevState) => prevState.set('checkedIds', List()));
  };
  const handleCheckboxChange = (key) => () => {
    setState((prevState) => prevState.set(key, !prevState.get(key)));
  };
  const handleMinimumUsersChange = (event) => {
    setState((prevState) => prevState.set('minimumUsers', event.target.value));
  };
  const handleTATChange = (event) => {
    const selectedTAT = parseInt(event.target.value, 10);
    setState((prevState) => prevState.set('turnAroundTime', selectedTAT));
  };
  const handleRadioChange = (value) => () => {
    setState((prevState) => prevState.set('category', value).set('categorySelected', List()));
  };
  const handleCheckboxChangeCategory = (value, page) => () => {
    if (page === state.get('category', ''))
      setState((prevState) => {
        const categorySelected = prevState.get('categorySelected', List());
        if (categorySelected.includes(value)) {
          return prevState.set(
            'categorySelected',
            categorySelected.filter((item) => item !== value)
          );
        } else {
          return prevState.set('categorySelected', categorySelected.push(value));
        }
      });
  };
  const handleCategorySelectedChange = (event) => {
    const { value } = event.target;
    setState((prevState) => prevState.set('specificSections', List(value)));
  };
  const onClickSave = () => {
    const requestBody = fromJS({
      name: state.get('name', ''),
      requireAll: state.get('requireAll', true),
      requireMinimum: state.get('requireMinimum', false),
      turnAroundTime: state.get('turnAroundTime', 1),
      escalateRequest: state.get('escalateRequest', false),
      allowToSkipping: state.get('allowToSkipping', false),
      allowToOverwrite: state.get('allowToOverwrite', false),
      category: state.get('category', ''),
      minimum_user: state.get('minimum_user', 0),
      categorySelected: state.get('categorySelected', List()).toArray(),
      specificSections: state.get('specificSections', List()).toArray(),
      selectedRoleUser: state
        .get('checkedIds', List())
        .groupBy((data) => {
          const [role_Id] = data.split('+');
          return role_Id;
        })
        .map((userIds, role_Id) => {
          const role_name = userRole.getIn(['roleList', role_Id, 'name'], '');
          return Map({
            role_Id,
            role_name,
            userIds: userIds.map((key) => {
              const [, userId] = key.split('+');
              return userRole.getIn(['roleMap', userId], Map());
            }),
          });
        })
        .toList(),
    });
    onSave(requestBody, state.get('index', 0));
  };

  const getSelectedAndTotal = () => {
    const size = state.get('checkedIds', List())?.size || 0;
    const totalList = userRole.get('roleMap', Map()).size;
    const totalLists = String(totalList).padStart(2, '0');
    const CheckedRoleId = String(size).padStart(2, '0');
    return `${CheckedRoleId}/${totalLists}`;
  };

  return (
    <Drawer sx={drawerPaperW40} anchor="right" open={true} onClose={toggleDrawer(Map())}>
      <div className="px-4 py-3">
        <div className="q360-popup-Header py-2">Tell us about your Form</div>
        <div className="popup-q360-overflow-step3  px-3 py-3">
          <MaterialInput
            elementType={'materialInput'}
            type={'text'}
            variant={'outlined'}
            size={'small'}
            label={<div className="f-12 q360-color-gray">Approver Level Name</div>}
            placeholder={'Enter Approver Level Name'}
            value={state.get('name', '')}
            changed={handleApproverLevelNameChange}
          />
          <div className="f-12 q360-color-gray py-2">Approval Category</div>
          <Paper elevation={0} variant="outlined" sx={{ height: '265px' }}>
            <div className="d-flex" style={{ flexWrap: 'wrap' }}>
              <div className="flex-grow-1 border-right w-50">
                <div className="d-flex align-items-center gap-5">
                  <div className="p-1 flex-grow-1">
                    <MaterialInput
                      elementType={'materialSearch'}
                      type={'text'}
                      size={'small'}
                      placeholder={'Search a Role, Name, ID...'}
                      labelclass={'searchLeft'}
                      addClass="f-12"
                      value={state.get('searchInput', '')}
                      changed={handleSearchInputChange}
                    />
                  </div>
                  <div className="d-flex ml-auto gap-10 pr-2 align-items-center">
                    <div className="f-14 text-mGrey">{getSelectedAndTotal()}</div>
                    <div
                      className="text-primary underlined-text f-12 cursor-pointer"
                      onClick={handleClearAll}
                    >
                      Clear All
                    </div>
                  </div>
                </div>
                <div className="overflowRoleList">
                  {state.get('checkedIds', List()).map((checkedIdsData, index) => {
                    const [, roleId] =
                      checkedIdsData !== undefined ? checkedIdsData.split('+') : [];
                    const obj = userRole.getIn(['roleMap', roleId], Map());
                    const firstName = obj.getIn(['name', 'first'], '');
                    const lastName = obj.getIn(['name', 'last'], '');
                    const fullName = `${firstName} ${lastName}`;
                    const academicId = obj.get('academicId', '');
                    if (
                      fullName.toLowerCase().includes(state.get('searchInput', '').toLowerCase()) ||
                      academicId.toLowerCase().includes(state.get('searchInput', '').toLowerCase())
                    ) {
                      return (
                        <div key={index}>
                          <div className="d-flex align-items-center">
                            <Checkbox
                              size="small"
                              checked={state.get('checkedIds', List()).includes(checkedIdsData)}
                              onChange={(event) => handleChangeRoleChecked(event, checkedIdsData)}
                            />

                            <div className="f-14">
                              {academicId} - {fullName}
                            </div>
                          </div>
                          <Divider />
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
              <div className="flex-grow-1 w-50 ">
                <div>
                  <div className="d-flex justify-content-center p-2 color-black f-14 fw-400">
                    Select a Roles & Users
                  </div>
                  <div className="px-2 pt-3 overflowRoleList">
                    {userRole
                      .get('roleList', Map())
                      .filter((role) => {
                        return state.get('checkedIds', List()).size === 0
                          ? role
                              .get('name', '')
                              .toLowerCase()
                              .includes(state.get('searchInput', '').toLowerCase())
                          : role;
                      })
                      .entrySeq()
                      .slice(0, 50)
                      .map(([key, value], roleListIndex) => {
                        return (
                          <React.Fragment key={key}>
                            <Accordion
                              elevation={0}
                              variant="elevation"
                              expanded={expanded === roleListIndex}
                              onChange={onClickAccordion(roleListIndex)}
                              slotProps={{ transition: { unmountOnExit: true } }}
                            >
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls={`panel${key}-content`}
                                id={`panel${key}-header`}
                                className={expanded === roleListIndex ? 'color-selected' : ''}
                              >
                                <div className="d-flex align-items-center">
                                  <Checkbox
                                    size="small"
                                    checked={
                                      value.get('users', List()).size > 0 &&
                                      value
                                        .get('users')
                                        .every((ides) =>
                                          state.get('checkedIds', List()).includes(ides)
                                        )
                                    }
                                    onChange={(event) => handleChangeRoleListChecked(event, value)}
                                  />
                                  <div className="f-16 fw-400">{value.get('name', '')}</div>
                                </div>
                              </AccordionSummary>
                              {expanded === roleListIndex && (
                                <AccordionDetails>
                                  {value.get('users', List()).map((usersData, index) => {
                                    const usersSplitId = usersData.split('+');
                                    // const usersId = usersSplitId[0];
                                    const roleId = usersSplitId[1];
                                    const obj = userRole.getIn(['roleMap', roleId], Map());
                                    const firstName = obj.getIn(['name', 'first'], '');
                                    const lastName = obj.getIn(['name', 'last'], '');
                                    const academicId = obj.get('academicId', '');
                                    return (
                                      <div key={index}>
                                        <div className="d-flex align-items-center">
                                          <div>
                                            <SubdirectoryArrowRightIcon
                                              color="primary"
                                              className="cursor-pointer"
                                              sx={{ fontSize: 10 }}
                                            />
                                          </div>
                                          <div className="d-flex align-items-center">
                                            <Checkbox
                                              size="small"
                                              checked={state
                                                .get('checkedIds', List())
                                                .includes(usersData)}
                                              onChange={(event) =>
                                                handleChangeRoleChecked(event, usersData)
                                              }
                                            />

                                            <div className="f-14">
                                              {academicId} - {`${firstName} ${lastName}`}
                                            </div>
                                          </div>
                                        </div>
                                        <Divider />
                                      </div>
                                    );
                                  })}
                                </AccordionDetails>
                              )}
                            </Accordion>
                            <Divider />
                          </React.Fragment>
                        );
                      })}
                  </div>
                </div>
              </div>
            </div>
          </Paper>
          <div>
            <div className="f-12 q360-color-gray py-2">Approval Configuration</div>
            <div className="d-flex align-items-center gap-10">
              <div className="d-flex align-items-center gap-5">
                <Checkbox
                  size="small"
                  className="p-0"
                  checked={state.get('requireAll', true)}
                  onChange={handleCheckboxChange('requireAll')}
                />

                <div className="f-16 fw-400 pl-2">Approval Required From All User</div>
              </div>
              <div className="d-flex align-items-center">
                <Checkbox
                  size="small"
                  className="p-0"
                  checked={state.get('requireMinimum', false)}
                  onChange={handleCheckboxChange('requireMinimum')}
                />

                <div className="f-16 fw-400 pl-2">Approval Required Minimum</div>
              </div>
              <div className="d-flex align-items-center">
                <div className="col-5 pr-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'number'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'0'}
                    disabled={!state.get('requireMinimum', false)}
                    sx={paddingSmall}
                    value={state.get('minimumUsers', 0)}
                    changed={handleMinimumUsersChange}
                  />
                </div>
                <div className="f-16 fw-400">Users</div>
              </div>
            </div>
          </div>
          <div>
            <div className="f-12 q360-color-gray py-2">TAT (Turn Around Time)</div>
            <div>
              <MaterialInput
                elementType={'materialSelect'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{ options: programTypeNumber }}
                value={state.get('turnAroundTime', '')}
                changed={handleTATChange}
              />
            </div>
            <div className="py-3">
              <div className="d-flex align-items-center gap-5 ">
                <Checkbox
                  size="small"
                  className="p-0"
                  checked={state.get('escalateRequest', false)}
                  onChange={handleCheckboxChange('escalateRequest')}
                />

                <div className="f-16 fw-400 pl-2">Escalate to next level, if no response</div>
              </div>
              <div className="d-flex align-items-center gap-5 py-3">
                <Checkbox
                  size="small"
                  className="p-0"
                  checked={state.get('allowToSkipping', false)}
                  onChange={handleCheckboxChange('allowToSkipping')}
                />

                <div className="f-16 fw-400 pl-2">
                  Allow skipping the all previous levels of Approval
                </div>
              </div>
              <div className="d-flex align-items-center gap-5">
                <Checkbox
                  size="small"
                  className="p-0"
                  checked={state.get('allowToOverwrite', false)}
                  onChange={handleCheckboxChange('allowToOverwrite')}
                />
                <div className="f-16 fw-400 pl-2">
                  Allow to overwrite all previous level rejected applications
                </div>
              </div>
            </div>
          </div>
          <Divider />
          <div>
            <div className="f-14 text-primary py-3">Advance Settings</div>
            <div className="f-12">Category</div>
            <div>
              <div className="d-flex align-items-center gap-5">
                <Radio
                  value="Entire"
                  name="category-radio"
                  checked={state.get('category', '') === 'Entire'}
                  onChange={handleRadioChange('Entire')}
                  inputProps={{ 'aria-label': 'Entire' }}
                  size="small"
                />
                <div className="f-16 fw-400 pl-1">Entire Forum</div>
              </div>
              <div className="d-flex align-items-center pl-4 ml-3">
                <div className="d-flex align-items-center">
                  <Checkbox
                    size="small"
                    className="p-1"
                    checked={
                      state.get('category', '') === 'Entire' &&
                      state.get('categorySelected', List()).includes('Edit')
                    }
                    onChange={handleCheckboxChangeCategory('Edit', 'Entire')}
                  />
                  <div className="f-16 fw-400 pl-1">Edit</div>
                </div>
                <div className="d-flex align-items-center">
                  <Checkbox
                    size="small"
                    className="p-1"
                    checked={
                      state.get('category') === 'Entire' &&
                      state.get('categorySelected').includes('Comment')
                    }
                    onChange={handleCheckboxChangeCategory('Comment', 'Entire')}
                  />
                  <div className="f-16 fw-400 pl-1">Comment</div>
                </div>
                <div className="d-flex align-items-center">
                  <Checkbox
                    size="small"
                    className="p-1"
                    checked={
                      state.get('category') === 'Entire' &&
                      state.get('categorySelected').includes('Publish')
                    }
                    onChange={handleCheckboxChangeCategory('Publish', 'Entire')}
                  />
                  <div className="f-16 fw-400 pl-1">Publish</div>
                </div>
              </div>
            </div>
            <div>
              <div className="d-flex align-items-center gap-5">
                <Radio
                  value="Specific"
                  name="category-radio"
                  checked={state.get('category', '') === 'Specific'}
                  onChange={handleRadioChange('Specific')}
                  inputProps={{ 'aria-label': 'Specific' }}
                  size="small"
                />
                <div className="f-16 fw-400 pl-1">Specific Sections</div>
              </div>
              <div className="ml-3 pl-4">
                <Select
                  multiple
                  value={state.get('specificSections', List()).toJS()}
                  onChange={handleCategorySelectedChange}
                  renderValue={(selected) => selected.join(', ')}
                  size={'small'}
                  fullWidth
                >
                  {sections
                    .map((data) => {
                      return { name: data, value: data };
                    })
                    .map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.name}
                      </MenuItem>
                    ))}
                </Select>
              </div>
              <div className="d-flex align-items-center pl-4 ml-3">
                <div className="d-flex align-items-center">
                  <Checkbox
                    size="small"
                    className="p-1"
                    checked={
                      state.get('category', '') === 'Specific' &&
                      state.get('categorySelected', List()).includes('Edit')
                    }
                    onChange={handleCheckboxChangeCategory('Edit', 'Specific')}
                  />
                  <div className="f-16 fw-400 pl-1">Edit</div>
                </div>
                <div className="d-flex align-items-center ">
                  <Checkbox
                    size="small"
                    className="p-1"
                    checked={
                      state.get('category', '') === 'Specific' &&
                      state.get('categorySelected', List()).includes('Comment')
                    }
                    onChange={handleCheckboxChangeCategory('Comment', 'Specific')}
                  />
                  <div className="f-16 fw-400 pl-1">Comment</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="d-flex align-items-center pt-3">
          <div className="d-flex align-items-center ml-auto gap-20">
            <div>
              <MButton
                variant="outlined"
                className="px-4"
                size={'small'}
                clicked={toggleDrawer(Map())}
                color={'gray'}
              >
                Cancel
              </MButton>
            </div>
            <div>
              <MButton
                variant="contained"
                className="px-4"
                color="primary"
                size={'small'}
                clicked={onClickSave}
              >
                Save
              </MButton>
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};
