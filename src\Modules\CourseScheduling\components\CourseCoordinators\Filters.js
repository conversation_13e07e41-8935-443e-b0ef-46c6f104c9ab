import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import Checkbox from '@mui/material/Checkbox';
import ListItemText from '@mui/material/ListItemText';
import { ThemeProvider } from '@mui/styles';
import { muiMenuProps, useMuiMultiSelectStyles } from '../utils';
import SortIcon from '../../../../Assets/sort.png';
import {
  capitalize,
  indVerRename,
  levelRename,
  MUI_CHECKBOX_THEME,
  stringToUC,
} from '../../../../utils';
import { t } from 'i18next';

function Filters({
  terms,
  courses,
  filter,
  sortDirection,
  handleChange,
  handleSortDirectionChange,
  programId,
}) {
  const classes = useMuiMultiSelectStyles();
  function getYearLevels() {
    const grouped = courses.groupBy((course) => course.get('year'));
    return grouped
      .entrySeq()
      .reduce(
        (acc, [key, value]) =>
          acc.push(
            Map({
              year: key,
              levels: value
                .reduce(
                  (levelAcc, v) =>
                    levelAcc.set(
                      v.get('level_no'),
                      Map({
                        name: levelRename(v.get('level_no'), programId),
                        value: v.get('level_no'),
                      })
                    ),
                  Map()
                )
                .valueSeq(),
            })
          ),
        List()
      )
      .reduce((acc, year) => {
        const yearName = year.get('year');
        const totalLevels = year.get('levels', List()).size;
        const levels = year.get('levels', List()).map((level) =>
          level.merge(
            Map({
              isLevel: true,
              key: `${yearName}-${level.get('name')}`,
              year: yearName,
              totalLevels,
            })
          )
        );
        const options = List([
          Map({
            isLevel: false,
            levels: levels.map((l) => l.get('value')),
            totalLevels: levels.size,
            key: yearName,
            name: `Year ${yearName.split('year').join('')}`,
            value: yearName,
          }),
        ]).concat(levels);
        return acc.concat(options);
      }, List());
  }

  function getDepartmentsAndSubjects(key) {
    const currentDepartmentId = filter.get('department');
    return courses
      .reduce((acc, course) => {
        const id = course.getIn(['administration', `_${key}_id`], '');
        if (key === 'subject' && currentDepartmentId) {
          if (currentDepartmentId !== course.getIn(['administration', '_department_id'], '')) {
            return acc;
          }
        }
        return acc.set(
          id,
          Map({
            name: course.getIn(['administration', `${key}_name`], ''),
            value: id,
          })
        );
      }, Map())
      .valueSeq()
      .sort((c1, c2) => {
        const v1 = c1.get('name', '');
        const v2 = c2.get('name', '');
        return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
      });
  }

  function handleYearLevelChange(option) {
    const yearLevel = filter.get('yearLevel');
    if (!option.get('isLevel')) {
      const year = option.get('value');
      const levels = option.get('levels');
      let newLevels = List();
      if (yearLevel.getIn([year, 'levels'], List()).size !== levels.size) {
        newLevels = levels;
      }
      handleChange(
        yearLevel.mergeIn([year], Map({ levels: newLevels, total: levels.size })),
        'yearLevel'
      );
    } else {
      const [year, level] = [option.get('year'), option.get('value')];
      const existingLevels = yearLevel.getIn([year, 'levels'], List()).toList();
      let newLevels;
      if (existingLevels.includes(level)) {
        newLevels = existingLevels.filter((l) => l !== level);
      } else {
        newLevels = existingLevels.push(level);
      }
      handleChange(
        yearLevel.mergeIn([year], Map({ levels: newLevels, total: option.get('totalLevels') })),
        'yearLevel'
      );
    }
  }

  function getSelectedYearLevels() {
    const yearLevel = filter.get('yearLevel');
    return yearLevel
      .entrySeq()
      .reduce((acc, [key, value]) => {
        const levels = value.get('levels');
        const totalLevels = value.get('total');
        if (levels.size === totalLevels) {
          return acc.concat(List([key]).concat(levels));
        }
        return acc.concat(levels);
      }, List())
      .toJS();
  }

  function getIndeterminateState(option) {
    if (option.get('isLevel')) return false;
    const year = option.get('value');
    const levels = filter.getIn(['yearLevel', year, 'levels'], List());
    const totalLevels = filter.getIn(['yearLevel', year, 'total'], List());
    return levels.size > 0 && levels.size < totalLevels;
  }

  function renderYearLevelSelect(selected) {
    const yearLevel = filter.get('yearLevel', Map());
    let displayText = '';
    yearLevel.entrySeq().forEach(([key, value]) => {
      const levels = value.get('levels', List());
      if (levels.isEmpty()) return;
      if (displayText.length) {
        displayText = 'Mixed';
        return;
      }
      displayText = `Y${key.split('year').join('')} - ${levels
        .map((l) => `${l.slice(0, 1)}${isNaN(l.slice(-1)) ? '' : l.slice(-1)}`)
        .join(', ')}`;
    });
    return displayText || 'All';
  }

  const selectedYearLevels = getSelectedYearLevels();

  return (
    <div className="p-3">
      <div className="row">
        <div className="col-md-3">
          <div className="f-14">{stringToUC(indVerRename('TERM', programId))}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filter.get('term', '')}
              onChange={(e) => handleChange(e.target.value, 'term')}
            >
              <option value="">{t('all')}</option>
              {terms.map((term) => (
                <option key={term} value={term}>
                  {capitalize(term)}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>

        <div className="col-md-9">
          <div className="row align-items-end">
            <div className="col-md-1 pl-0 pr-0">
              <div
                className="course_sort_icon bg-gray cursor-pointer"
                onClick={handleSortDirectionChange}
              >
                <img
                  src={SortIcon}
                  alt="sort"
                  className={`img-fluid ${sortDirection === 'asc' ? 'rotate-180' : ''}`}
                />
              </div>
            </div>

            <div className="col-md-2 pl-2 pr-1">
              <div className="f-14 text-uppercase">{t('status')}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filter.get('status', '')}
                  onChange={(e) => handleChange(e.target.value, 'status')}
                >
                  <option value="">{t('all')}</option>
                  {['assigned', 'pending'].map((status) => (
                    <option key={status} value={status}>
                      {capitalize(status)}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>

            <div className="col-md-2 pl-2 pr-1">
              <div className="f-14 text-uppercase">
                {t('year')} / {stringToUC(indVerRename('LEVEL', programId))}
              </div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  multiple
                  value={selectedYearLevels}
                  onChange={() => {}}
                  MenuProps={{
                    ...muiMenuProps,
                    PaperProps: { style: { maxHeight: 280 } },
                  }}
                  renderValue={renderYearLevelSelect}
                  displayEmpty
                >
                  {getYearLevels().map((option) => {
                    return (
                      <MenuItem
                        key={option.get('key')}
                        value={option.get('value')}
                        disableGutters
                        className={classes[option.get('isLevel') ? 'nested' : 'single']}
                        onClick={() => handleYearLevelChange(option)}
                      >
                        <ListItemIcon className={classes.listItemIcon}>
                          <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                            <Checkbox
                              size="small"
                              color="primary"
                              checked={selectedYearLevels.includes(option.get('value'))}
                              indeterminate={getIndeterminateState(option)}
                            />
                          </ThemeProvider>
                        </ListItemIcon>
                        <ListItemText
                          primary={option.get('name')}
                          classes={{
                            primary: !option.get('isLevel') ? classes.singleMenuItemLabel : '',
                          }}
                        />
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </div>

            <div className="col-md-2 pl-2 pr-1">
              <div className="f-14 text-uppercase">{t('department')}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filter.get('department', '')}
                  onChange={(e) => handleChange(e.target.value, 'department')}
                >
                  <option value="">{t('all')}</option>
                  {getDepartmentsAndSubjects('department').map((option) => (
                    <option key={option.get('value')} value={option.get('value')}>
                      {option.get('name')}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>

            <div className="col-md-2 pl-2 pr-1">
              <div className="f-14 text-uppercase">{t('subject')}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filter.get('subject', '')}
                  onChange={(e) => handleChange(e.target.value, 'subject')}
                >
                  <option value="">{t('all')}</option>
                  {getDepartmentsAndSubjects('subject').map((option) => (
                    <option key={option.get('value')} value={option.get('value')}>
                      {option.get('name')}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

Filters.propTypes = {
  terms: PropTypes.instanceOf(List),
  courses: PropTypes.instanceOf(List),
  filter: PropTypes.instanceOf(Map),
  sortDirection: PropTypes.oneOf(['asc', 'desc']),
  handleChange: PropTypes.func,
  handleSortDirectionChange: PropTypes.func,
  programId: PropTypes.string,
};

export default Filters;
