import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';

import YearLevelFilters from './YearLevelFilters';
import CourseTimeTable from './CourseTimeTable';
import ViewCourseScheduleStatus from '../../modal/ViewCourseScheduleStatus';
import { t } from 'i18next';
import { getEnvLabelChanged, indVerRename, levelRename, stringToUC } from '../../../../utils';

function LevelTimeTable(props) {
  const {
    courseList,
    programId,
    institutionCalendarId,
    getTimeTableData,
    timeTableData,
    setData,
    academicYear,
    programName,
    getIndividualCourseList,
    individualCourseDetails,
  } = props;
  const [filter, setFilter] = useState(
    Map({
      term: '',
      year: '',
      level: '',
      course: '',
    })
  );
  const [showCourseStatusModal, setShowCourseStatusModal] = useState(false);

  useEffect(() => {
    if (!filter.get('term')) {
      handleFilterChange(courseList.get('term', List()).get(0, ''), 'term');
    }
    // eslint-disable-next-line
  }, [courseList]);

  function handleFilterChange(value, name) {
    switch (name) {
      case 'term':
        setFilter(filter.merge(Map({ term: value, year: '', level: '', course: '' })));
        break;
      case 'year':
        setFilter(filter.merge(Map({ year: value, level: '', course: '' })));
        break;
      case 'level':
        setFilter(filter.merge(Map({ level: value, course: '' })));
        break;
      default:
        setFilter(filter.set(name, value));
        if (name === 'course' && value !== '') {
          getIndividualCourseList(value, filter.get('term'), filter.get('level'));
        }
        break;
    }
  }

  function getCourses() {
    const term = filter.get('term', '');
    const year = filter.get('year', '');
    const levelNo = filter.get('level', '');
    if (!term || !year || !levelNo) return List();
    const levelData = courseList
      .get('courses', List())
      .find(
        (level) =>
          level.get('term') === term &&
          level.get('year') === year &&
          level.get('level_no') === levelNo
      );
    if (!levelData) return List();
    return levelData.get('courses', List());
  }

  function getScheduledAndTotalText() {
    const courses = getCourses();
    return `${courses.reduce(
      (acc, course) => (course.get('schedule_status') ? acc + 1 : acc),
      0
    )}/${courses.size} - ${t('courses_scheduled')}`;
  }

  function getCourse() {
    const courses = getCourses();
    return courses.find((c) => c.get('_course_id') === filter.get('course')) || Map();
  }

  function getYearLevel() {
    const dates = courseList.get('level_term_dates', List());
    const yearLevel = dates.find(
      (d) =>
        d.get('term') === filter.get('term') &&
        d.get('year') === filter.get('year') &&
        d.get('level_no') === filter.get('level')
    );
    return yearLevel || Map();
  }

  function handleCourseStatusModalClose() {
    setShowCourseStatusModal(false);
  }

  return (
    <div className="m-3 bg-white">
      <div className="p-3 m-1">
        <YearLevelFilters
          data={courseList}
          filter={filter}
          handleChange={handleFilterChange}
          sortDirection="asc"
          handleSortDirectionChange={() => {}}
          resetLabelName="Select"
          programId={programId}
        />
        {(!filter.get('year') || !filter.get('level')) && (
          <div className="mt-1 f-12">
            <i className="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
            {getEnvLabelChanged()
              ? `Please select YEAR and ${stringToUC(
                  indVerRename('LEVEL', programId)
                )} to view timetable`
              : t('select_level_year')}
          </div>
        )}
      </div>
      <div>
        {filter.get('year') && filter.get('level') && (
          <>
            <div className="p-3">
              <span className="mr-3">
                {`${filter
                  .get('year', '')
                  .split('year')
                  .join('Year ')
                  .replace('Year', t('year'))} / ${levelRename(filter.get('level'), programId)}`}
              </span>
              <span
                className="courses-scheduled-chip cursor-pointer"
                onClick={() => setShowCourseStatusModal(true)}
              >
                {getScheduledAndTotalText()}
              </span>
            </div>
            <div>
              <CourseTimeTable
                timeTableFor={filter.get('course') ? 'course' : 'level'}
                course={getCourse()}
                courseDetails={individualCourseDetails}
                yearLevel={getYearLevel()}
                programId={programId}
                courseId={filter.get('course')}
                term={filter.get('term')}
                level={filter.get('level')}
                year={filter.get('year')}
                institutionCalendarId={institutionCalendarId}
                getTimeTableData={getTimeTableData}
                timeTableData={timeTableData}
                setData={setData}
                academicYear={academicYear}
                programName={programName}
                getIndividualCourseList={getIndividualCourseList}
              />
            </div>
          </>
        )}
      </div>
      <ViewCourseScheduleStatus
        show={showCourseStatusModal}
        handleClose={handleCourseStatusModalClose}
        courses={getCourses()}
      />
    </div>
  );
}

LevelTimeTable.propTypes = {
  courseList: PropTypes.instanceOf(Map),
  getIndividualCourseList: PropTypes.instanceOf(List),
  programId: PropTypes.string,
  academicYear: PropTypes.string,
  institutionCalendarId: PropTypes.string,
  programName: PropTypes.string,
  getTimeTableData: PropTypes.func,
  setData: PropTypes.func,
  timeTableData: PropTypes.instanceOf(Map),
  individualCourseDetails: PropTypes.instanceOf(Map),
};

export default LevelTimeTable;
