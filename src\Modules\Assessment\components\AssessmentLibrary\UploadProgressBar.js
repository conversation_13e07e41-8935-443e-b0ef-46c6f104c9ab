import React, { useRef } from 'react';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import AttachFile from 'Assets/attach_file.svg';
import { ProgressBar } from 'react-bootstrap';
import Button from 'Widgets/FormElements/material/Button';

const OnUploadProgressBar = ({
  showProgress,
  setShowProgress,
  setImportPercent,
  importPercent = 70,
  fileNameOn,
  setFileNameOn,
  handleChangeFile,
  fileSizeOn,
  setFileSizeOn,
}) => {
  const callValidation = useRef(false);
  const style = {
    marginLeft: '380px',
    position: 'absolute',
  };

  let file = document.getElementById('upload-file-progress');
  let load = 0;
  let process = '';
  let fileName = '';
  let fileSize = '';

  if (file) {
    file.oninput = () => {
      setShowProgress(true);
      fileSize = file.files[0].size;
      fileName = file.files[0].name;
      if (fileSize <= 1000000) {
        fileSize = (fileSize / 1000).toFixed(2) + 'kb';
      }
      if (fileSize === 1000000 || fileSize <= 1000000000) {
        fileSize = (fileSize / 100).toFixed(2) + 'mb';
      }
      if (fileSize === 1000000000 || fileSize <= 10000000000) {
        fileSize = (fileSize / 100).toFixed(2) + 'gb';
      }
      setFileNameOn(fileName);
      setFileSizeOn(fileSize);
      getFileInfo(fileName);
    };
    file.onclick = function () {
      this.value = null;
    };
  }
  let upload = () => {
    if (load >= 100) {
      setTimeout(() => {
        setShowProgress(false);
        setImportPercent(0);
        load = 0;
        clearInterval(process);
        callValidation.current && handleChangeFile(file);
      }, [500]);
    } else {
      load = load + 4;
      setImportPercent(load);
    }
  };
  function getFileInfo(fileName) {
    if (fileName) {
      callValidation.current = true;
      load = 0;
      setImportPercent(0);
      process = setInterval(upload, 100);
    }
  }
  const cancelUpload = () => {
    setShowProgress(false);
    setImportPercent(0);
    clearInterval(process);
    setFileNameOn('');
    load = 0;
    callValidation.current = false;
  };
  return (
    <Modal
      show={showProgress}
      centered
      onHide={() => setShowProgress(false)}
      dialogClassName="modal-500"
    >
      <Modal.Body>
        <div className="d-flex mb-3 justify-content-between">
          <p className="mb-0 f-22 bold">Uploading...</p>
          <div style={style}>
            <span className="extension-format-chip">{fileSizeOn}</span>
          </div>
        </div>
        <div className="p-2">
          <div className="progressBarWidth mb-1">
            <span className="uploadLabelSpan">
              <span className="material-icons">
                <img src={AttachFile} alt="" className="w-15p" />
              </span>
              {getShortString(fileNameOn, 30)}
            </span>
            <ProgressBar
              now={importPercent}
              animated
              // width="100px"
              variant="success"
              active
              label={`${importPercent}%`}
            />
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer className="border-none">
        <Button variant="outlined" color="inherit" clicked={cancelUpload}>
          <Trans i18nKey={'cancel'}></Trans>
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

OnUploadProgressBar.propTypes = {
  showProgress: PropTypes.bool,
  setShowProgress: PropTypes.func,
  setImportPercent: PropTypes.func,
  importPercent: PropTypes.number,
  fileNameOn: PropTypes.string,
  setFileNameOn: PropTypes.func,
  fileSizeOn: PropTypes.string,
  setFileSizeOn: PropTypes.func,
  handleChangeFile: PropTypes.func,
};
export default OnUploadProgressBar;
