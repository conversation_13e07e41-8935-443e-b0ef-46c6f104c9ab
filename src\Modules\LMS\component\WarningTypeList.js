import React, { useState, useEffect, Suspense } from 'react';
import PropTypes from 'prop-types';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Input from 'Widgets/FormElements/Input/Input';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';

import { Checkbox, Divider } from '@mui/material';
import { Map, List } from 'immutable';
import {
  iscomprehensiveMode,
  useFilteredOptions,
  useIsWarningValue,
  warningTypeName,
} from '../utils';
import { useStylesFunction } from '../designUtils';

// redux
import * as actions from '_reduxapi/leave_management/actions';
import { connect } from 'react-redux';
import { capitalize } from 'utils';

const DeleteModal = React.lazy(() => import('./Modal/DeleteModal'));

const MenuToolbar = ({
  warningConfig,
  handleClickOpen,
  index,
  item,
  setToBeDeletedId,
  setShow,
}) => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (e) => {
    e.stopPropagation();
    setAnchorEl(e.currentTarget);
  };

  const handleDeleteTypes = (e, id) => {
    setToBeDeletedId(id);
    e.stopPropagation();
    handleClose(e);
    setShow(true);
  };

  const handleClose = (e) => {
    e.stopPropagation();
    setAnchorEl(null);
  };
  return (
    <div className="ml-2 mt-1 pt-1">
      <MoreVertIcon
        color="primary"
        id="basic-button"
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      />
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
        className="pr-5 pl-5"
      >
        <MenuItem
          className=" pr-4 pl-4 text-primary"
          onClick={(e) => {
            e.stopPropagation();
            handleClose(e);
            handleClickOpen('update', index);
          }}
        >
          Edit
        </MenuItem>
        {index !== 0 && warningConfig.size !== index + 1 && <Divider />}
        {index !== 0 && warningConfig.size !== index + 1 && (
          <MenuItem
            className=" pr-4 pl-4 text-danger"
            onClick={(e) => handleDeleteTypes(e, item.get('_id'))}
          >
            Delete
          </MenuItem>
        )}
      </Menu>
    </div>
  );
};

const WarningTypeList = ({
  lmsSettings,
  handleClickOpen,
  isEditWarning,
  handleChangeActive,
  deleteWarningConfig,
  sessionWise,
}) => {
  const { accordion, accordionSummaryBorder, checkedDisabled } = useStylesFunction();
  const [isActive, setIsActive] = useState(List());
  const [show, setShow] = useState(false);
  const [toBeDeletedId, setToBeDeletedId] = useState('');
  const warningMode = lmsSettings.get('warningMode');
  const iscomprehensive = iscomprehensiveMode(warningMode);
  const isWarningValue = useIsWarningValue(iscomprehensive);
  const warningConfig = isWarningValue
    ? lmsSettings.get('comprehensiveWarningConfig', List())
    : lmsSettings.get('warningConfig', List());

  useEffect(() => {
    setIsActive(warningConfig.map((data) => data.get('isActive', false)));
  }, [lmsSettings]); //eslint-disable-line

  const handleDelete = () => {
    deleteWarningConfig(
      {
        settingId: lmsSettings.get('_id', ''),
        warningConfigId: toBeDeletedId,
      },
      setShow(false),
      sessionWise
    );
  };

  const warningName = (i) => {
    const typeName = warningTypeName.find((_, index) => index === i);
    const lastIndex = warningConfig.size;
    return lastIndex !== i + 1 ? typeName : 'Denial';
  };

  const toBeDeletedName = () => {
    const index = warningConfig.findIndex((s) => s.get('_id') === toBeDeletedId);
    return warningName(index);
  };

  const handleActiveInactive = (e, btnName, btnValue, index, item) => {
    e.stopPropagation();
    if (!isEditWarning) return;
    if (isActive.get(index, '') !== btnValue) {
      btnName === 'activeBtn'
        ? setIsActive(isActive.set(index, true))
        : setIsActive(isActive.set(index, false));
      handleChangeActive(!item.get('isActive', false), item.get('_id', ''));
    }
  };

  const filteredOptions = useFilteredOptions(iscomprehensive);

  return (
    <React.Fragment>
      {warningConfig.size > 0 &&
        warningConfig.map((item, index) => (
          <Accordion className={`${accordion} mb-3`} key={index}>
            <AccordionSummary
              expandIcon={
                <div>
                  <ExpandMoreIcon className="mt-2" />
                </div>
              }
              aria-controls="panel1bh-content"
              id="panel1bh-header"
              className={`panelSummary ${accordionSummaryBorder}`}
            >
              <Typography sx={{ flexShrink: 0 }} className="mt-3 bold text-dark">
                {warningName(index)}
              </Typography>
              <div className="flex-grow-1 pr-3 pl-3">
                <Divider className="mt-4 pt-1 digi-divider-mdl" />
              </div>

              <div className="d-flex mt-2 mr-2">
                <Input
                  elementType={'activeInactiveButton'}
                  onClick={(e, btnName, btnValue) =>
                    handleActiveInactive(e, btnName, btnValue, index, item)
                  }
                  active={isActive.get(index, false)}
                />
                {isEditWarning && (
                  <MenuToolbar
                    warningConfig={warningConfig}
                    handleClickOpen={handleClickOpen}
                    index={index}
                    item={item}
                    setShow={setShow}
                    toBeDeletedId={toBeDeletedId}
                    setToBeDeletedId={setToBeDeletedId}
                  />
                )}
              </div>
            </AccordionSummary>
            <AccordionDetails>
              <div className="mt-4">
                {warningConfig.size === item.get('typeName', '') && (
                  <div className="d-flex mb-2">
                    <div className="digi-light-gray mt-1 mr-2">Denial Condition:</div>
                    <div className="radio-adjustment">
                      <Input
                        disabled
                        elementType={'radio'}
                        elementConfig={[
                          ['Cumulative', 'Cumulative'],
                          ['Individual', 'Individual'],
                        ]}
                        className={'form-radio1'}
                        labelclass="radio-label2"
                        selected={item.get('denialCondition', 'Cumulative')}
                      />
                    </div>
                  </div>
                )}

                <div className="d-flex align-items-top mb-2">
                  <div className="mr-5 digi-max-width-200 ">
                    <div className="digi-light-gray mb-1">Label:</div>
                    <div className="digi-word-wrap">{item.get('labelName', '')}</div>
                  </div>
                  <div className="mr-5 digi-max-width-200">
                    <div className="digi-light-gray mb-1">Warning message (Optional):</div>
                    <div className="digi-word-wrap">{item.get('message', '')}</div>
                  </div>

                  <div className="mr-5">
                    <div className="digi-light-gray mb-1">
                      {`${isWarningValue ? 'No of Sessions' : 'Set Percentage'} `}:
                    </div>
                    <div className="d-flex align-items-center">
                      <div className="w-50 mr-1">
                        {iscomprehensive === 'percentage' ? (
                          <>{item.get('percentage', '')}%</>
                        ) : (
                          <>{item.get(iscomprehensive, '')}</>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mr-5">
                    <div className="digi-light-gray mb-1">Warning Color:</div>
                    <div
                      className="color-swatch digi-pointer-events"
                      style={{ backgroundColor: item.get('colorCode', '') }}
                    />
                  </div>
                </div>

                {warningConfig.size === item.get('typeName', '') && (
                  <>
                    <div className="digi-light-gray">Denial Management:</div>
                    <div className="d-flex mb-2">
                      <div className="digi-light-gray mt-1 mr-2">Access Type:</div>
                      <div className="radio-adjustment">
                        <Input
                          disabled
                          elementType={'radio'}
                          elementConfig={[
                            ['Role', 'role'],
                            ['User', 'user'],
                          ]}
                          className={'form-radio1'}
                          labelclass="radio-label2"
                          selected={item.getIn(['denialManagement', 'accessType'], 'role')}
                        />
                      </div>
                      <Stack direction="row" spacing={1} className="d-inline">
                        {item.getIn(['denialManagement', 'accessType'], 'role') === 'role'
                          ? item
                              .get('denialManagement', List())
                              .get('roleIds', List())
                              .map((item, roleIndex) => {
                                return (
                                  <Chip
                                    key={roleIndex}
                                    label={item.get('name', '')}
                                    variant="outlined"
                                    className="digi-role-bg-chip mb-2"
                                  />
                                );
                              })
                          : item
                              .get('denialManagement', List())
                              .get('userIds', List())
                              .map((item, userIndex) => {
                                return (
                                  <Chip
                                    key={userIndex}
                                    label={
                                      item.getIn(['name', 'first'], '') +
                                      ' ' +
                                      item.getIn(['name', 'last'], '')
                                    }
                                    variant="outlined"
                                    className="digi-role-bg-chip mb-2"
                                  />
                                );
                              })}
                      </Stack>
                    </div>

                    {item.get('denialCondition', '') === 'Individual' && (
                      <>
                        <div className="row mb-3">
                          {item.get('categoryWisePercentage', List()).map((cat, catIndex) => (
                            <div className="col-md-3" key={catIndex}>
                              <div className="digi-light-gray mb-1 text-capitalize">
                                {cat.get('categoryName', '')}:
                              </div>
                              <div className="d-flex align-items-center">
                                <div className="mr-1 text-capitalize">
                                  {cat.get('percentage', '')}%
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="w-50 mb-3">
                          <div className="digi-light-gray mb-1">
                            Unapplied leave will be considered as:
                          </div>
                          {capitalize(item.get('unappliedLeaveConsideredAs'))}
                        </div>
                      </>
                    )}
                  </>
                )}
                {isWarningValue ? (
                  <></>
                ) : (
                  <div className="d-flex align-items-center mb-2">
                    <div>
                      <div className="d-flex align-items-center mr-2">
                        <Checkbox
                          disabled
                          checked={item.get('notificationToStaff', false)}
                          className={`pl-0 ${checkedDisabled}`}
                        />
                        <div>Send Alert Notifications To Course Staffs</div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="mb-2 w-100">
                  <div className="d-flex align-items-center">
                    <div className="d-flex align-items-center mr-2">
                      <Checkbox
                        disabled
                        checked={item.get('isAdditionStaffNotify', false)}
                        className={`pl-0 ${checkedDisabled}`}
                      />
                      <div>Which Additional Staff Gets Alert Notification About the Warning:</div>
                    </div>
                  </div>

                  <Stack direction="row" spacing={1} className="d-inline">
                    {item.get('notificationRoleIds', List()).map((item, roleIndex) => {
                      return (
                        <Chip
                          key={roleIndex}
                          label={item.get('name', '')}
                          variant="outlined"
                          className="digi-role-bg-chip mb-2"
                        />
                      );
                    })}
                  </Stack>
                </div>

                {/* <div className="row">
                  <div className="col-md-12 align-items-center">
                    {item.get('notificationRoleIds', List()).map((item, roleIndex) => {
                      return (
                        <div
                          key={roleIndex}
                          className="col-md-4 rounded digi-role-bg-chip px-2 py-1 mr-1"
                        >
                          {item.get('name', '')}
                        </div>
                      );
                    })}
                  </div>
                </div> */}

                <div className="d-flex align-items-center mb-2">
                  <div>
                    <div className="d-flex align-items-center mr-2">
                      <Checkbox
                        disabled
                        checked={item.getIn(['notificationToStudent', 'isActive'], false)}
                        className={`pl-0 ${checkedDisabled}`}
                      />
                      <div>Send Alert Notifications To Students</div>
                    </div>
                  </div>
                </div>
                {item.getIn(['notificationToStudent', 'isActive'], false) && (
                  <div className="d-flex mb-2">
                    <div className="digi-light-gray mt-1 mr-2">Type:</div>
                    <div className="radio-adjustment">
                      <Input
                        disabled
                        elementType={'radio'}
                        elementConfig={filteredOptions}
                        className={'form-radio1'}
                        labelclass="radio-label2"
                        selected={item.getIn(['notificationToStudent', 'setType'], 'manual')}
                      />
                    </div>
                  </div>
                )}

                {item.getIn(['notificationToStudent', 'setTpe'], 'manual') === 'manual' && (
                  <Stack direction="row" spacing={1} className="d-inline">
                    {item
                      .getIn(['notificationToStudent', 'sendNotificationAuthority'])
                      .map((item, roleIndex) => {
                        return (
                          <Chip
                            key={roleIndex}
                            label={item.get('name', '')}
                            variant="outlined"
                            className="digi-role-bg-chip mb-2"
                          />
                        );
                      })}
                  </Stack>
                )}

                <div className="d-flex align-items-center">
                  <div>
                    <div className="d-flex align-items-center mr-2">
                      <Checkbox
                        disabled
                        checked={item.get('acknowledgeToStudent', false)}
                        className={`pl-0 ${checkedDisabled}`}
                      />
                      <div>Acknowledge Warning To Students In Warning Alerts</div>
                    </div>
                  </div>
                </div>

                <div className="d-flex align-items-center">
                  <div>
                    <div className="d-flex align-items-center mr-2">
                      <Checkbox
                        disabled
                        checked={item.get('markItMandatory', false)}
                        className={`pl-0 ${checkedDisabled}`}
                      />
                      <div>Mark it Mandatory</div>
                    </div>
                  </div>
                </div>
                {warningConfig.size === item.get('typeName', '') && (
                  <div className="d-flex align-items-center">
                    <div>
                      <div className="d-flex align-items-center mr-2">
                        <Checkbox
                          disabled
                          checked={item.get('restrictCourseAccess', false)}
                          className={`pl-0 ${checkedDisabled}`}
                        />
                        <div>Restrict Course Access</div>
                      </div>
                    </div>
                  </div>
                )}
                {warningConfig.size === item.get('typeName', '') && (
                  <>
                    <div className="d-flex align-items-center">
                      <div>
                        <div className="d-flex align-items-center mr-2">
                          <Checkbox
                            disabled
                            checked={item.get('adminRestrictedAttendance', false)}
                            className={`pl-0 ${checkedDisabled}`}
                          />
                          <div>Admin Can Give Attendance in Restricted Schedules</div>
                        </div>
                      </div>
                    </div>
                    <div className="d-flex align-items-center">
                      <div>
                        <div className="d-flex align-items-center mr-2">
                          <Checkbox
                            disabled
                            checked={item.get('scheduleStaffRestrictedAttendance', false)}
                            className={`pl-0 ${checkedDisabled}`}
                          />
                          <div>Allow Schedule Staff To Give Attendance</div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </AccordionDetails>
          </Accordion>
        ))}

      {show && (
        <Suspense fallback="">
          <DeleteModal
            show={show}
            setShow={setShow}
            handleDelete={handleDelete}
            name={toBeDeletedName()}
            toBeDeletedName={toBeDeletedName()}
          />
        </Suspense>
      )}
    </React.Fragment>
  );
};
WarningTypeList.propTypes = {
  lmsSettings: PropTypes.instanceOf(Map),
  handleClickOpen: PropTypes.func,
  handleChangeActive: PropTypes.func,
  deleteWarningConfig: PropTypes.func,
  warningConfig: PropTypes.object,
  index: PropTypes.number,
  isEditWarning: PropTypes.bool,
  item: PropTypes.object,
  toBeDeletedId: PropTypes.string,
  setToBeDeletedId: PropTypes.func,
  setShow: PropTypes.func,
  sessionWise: PropTypes.string,
};

MenuToolbar.propTypes = {
  warningConfig: PropTypes.object,
  handleClickOpen: PropTypes.func,
  index: PropTypes.number,
  item: PropTypes.object,
  toBeDeletedId: PropTypes.string,
  setToBeDeletedId: PropTypes.func,
  setShow: PropTypes.func,
};

export default connect(null, actions)(WarningTypeList);
