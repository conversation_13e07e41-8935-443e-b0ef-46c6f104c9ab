import React from 'react';
import { Route, Switch } from 'react-router';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import SnackBars from 'Modules/Utils/Snackbars';
import './css/style.css';
import { Map } from 'immutable';
// redux
import * as actions from '_reduxapi/global_configuration/v1/actions';
import { selectIsLoading, selectMessage } from '_reduxapi/global_configuration/v1/selectors';
import './components/UserActivityBoard/user_activity_board.css';
// utils
import Loader from 'Widgets/Loader/Loader';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
import SideBar from './components/SideBar';
import GlobalConfiguration from './components/GlobalConfiguration';
import BasicDetail from './components/BasicDetail';
import { CheckPermission } from 'Modules/Shared/Permissions';
import TardisEnabled from './components/TardisEnabled';
import { isModuleEnabled } from 'utils';
import LateConfiguration from './components/AttendanceConfiguration/LateConfiguration';
import SessionStatusAdjustment from './components/SessionStatusAdjustment';
import LeaderBoard from './components/UserActivityBoard/LeaderBoard/LeaderBoard';
import AnomalyBoard from './components/UserActivityBoard/AnomalyBoard/AnomalyBoard';
import Settings from './components/Settings';
// import { Grid } from '@mui/material';
import TagMaster from './components/Tagmaster/TagMaster';
import QAPCConfiguration, {
  useCurrentPage,
} from 'Modules/GlobalConfigurationV2/components/Q360Configuration';

const getHideSideBar = (query) => {
  if (!query) return false;
  return query.includes('configure_template');
};
const GlobalConfigurationV1 = ({ message, isLoading }) => {
  const query = useCurrentPage();
  const hideSideBar = getHideSideBar(query);
  return (
    <div>
      {message && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>{'Global Configuration'}</Breadcrumb>
      {/* <DashboardIndex /> */}
      <div className="pb-5 profile-details">
        <div className="container-fluid">
          <div className="row height_100vh">
            {!hideSideBar && (
              <div className=" col-xxl-2 col-xl-2 col-lg-2 col-md-2 col-sm-4 col-3 p-0  bg-white border-right">
                <SideBar />
              </div>
            )}
            <div
              className={`${
                !hideSideBar
                  ? ' col-xxl-10 margin_bottom_60px col-xl-10  col-lg-10  col-md-10 col-sm-8 col-9 p-0'
                  : 'w-100'
              }`}
            >
              <Switch>
                {CheckPermission('pages', 'Global Configuration', 'Institution', 'View') && (
                  <Route
                    path="/globalConfiguration-v1/institution"
                    exact
                    component={GlobalConfiguration}
                  />
                )}
                {CheckPermission(
                  'tabs',
                  'Global Configuration',
                  'Institution',
                  '',
                  'Basic Details',
                  'View'
                ) && (
                  <Route exact path="/globalConfiguration-v1/basicDetail" component={BasicDetail} />
                )}
                {isModuleEnabled('TARDIS_MODULE') && (
                  <Route
                    exact
                    path="/globalConfiguration-v1/disciplinary_remarks"
                    component={TardisEnabled}
                  />
                )}
                {isModuleEnabled('LATE_ABSENT_CONFIGURATION') && (
                  <Route
                    exact
                    path="/globalConfiguration-v1/attendance_configuration"
                    component={LateConfiguration}
                  />
                )}
                {!isModuleEnabled('ATTENDANCE_EXCLUDE') && (
                  <Route
                    exact
                    path="/globalConfiguration-v1/session_status_adjustment"
                    component={SessionStatusAdjustment}
                  />
                )}
                {/* {!isModuleEnabled('ATTENDANCE_EXCLUDE') && ( */}
                <Route exact path="/globalConfiguration-v1/leader_board" component={LeaderBoard} />
                <Route
                  exact
                  path="/globalConfiguration-v1/anomaly_board"
                  component={AnomalyBoard}
                />
                {/* )} */}
                {CheckPermission(
                  'tabs',
                  'Global Configuration',
                  'Institution',
                  '',
                  'Schedule Setting',
                  'View'
                ) && <Route exact path="/globalConfiguration-v1/settings" component={Settings} />}

                <Route path="/globalConfiguration-v1/tagMasters" exact component={TagMaster} />
                {isModuleEnabled('Q360') && (
                  <Route
                    exact
                    path="/globalConfiguration-v1/qa_pc_configuration"
                    component={QAPCConfiguration}
                  />
                )}
              </Switch>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

GlobalConfigurationV1.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
  loggedInUserData: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
  };
};

export default connect(mapStateToProps, actions)(withRouter(GlobalConfigurationV1));
