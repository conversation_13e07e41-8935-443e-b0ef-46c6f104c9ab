import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map as IMap, fromJS } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import Button from 'Widgets/FormElements/material/Button';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import {
  RadioGroup,
  FormControlLabel,
  Radio,
  MenuItem,
  Checkbox,
  FormControl,
  TextField,
} from '@mui/material';
import { label_levels_qlc, value_label_qlc } from './CreateTags';
import { useDispatch, useSelector } from 'react-redux';
import {
  getAttemptType,
  getSingleConfig,
  updateCategoryConfigure,
} from '_reduxapi/global_configuration/v1/actions';
import { selectAttemptType } from '_reduxapi/global_configuration/v1/selectors';
import ListItemText from '@mui/material/ListItemText';
import Select from '@mui/material/Select';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
//----------------------------------Utils Start-----------------------------------------------------
const outlinedNone = {
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      border: 'none !important',
    },
  },
  '& input': {
    color: '#374151 !important',
  },
  '& .Mui-disabled': {
    '-webkit-text-fill-color': 'inherit !important',
  },
};

const outlinedNoneImportant = {
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      border: 'none !important',
    },
  },
  '&.MuiTextField-root': {
    border: 'none !important',
  },
};

const categoryFor = fromJS([
  {
    name: 'Annual',
    value: 'annual',
  },
  {
    name: 'Periodic',
    value: 'periodic',
  },
]);

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

//----------------------------------Utils End-------------------------------------------------------

const CourseSpecificationConfigure = ({ open, handleClose, category, isEdit = false }) => {
  const [state, setState] = useState(category);
  const [inputDisabled, setInputDisabled] = useState(isEdit);
  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setState(
      (prev) => prev.set('attemptType', typeof value === 'string' ? value.split(',') : value)
      // On autofill we get a stringified value.
    );
  };
  const attemptType = useSelector(selectAttemptType);

  const constructedAttemptType = attemptType.reduce((acc, cur) => {
    return acc.set(
      cur.get('_id'),
      IMap({
        name: cur.get('name', ''),
        attemptId: cur.get('_id', ''),
      })
    );
  }, new IMap());
  const dispatch = useDispatch();
  function onDescribeChange(e) {
    e.persist();
    setState((prev) => prev.set('describe', e.target.value));
  }
  const onCategoryForChange = (e) => {
    setState((prev) => prev.set('categoryFor', e.target.value));
  };
  const onMandatoryChange = (key) => (e) => {
    setState((prev) => prev.set(key, e.target.checked));
  };
  const callBack = () => {
    dispatch(
      getSingleConfig(`/qapcCategory/singleCategoryConfig?categoryId=${category.get('_id', '')}`)
    );
    handleClose();
  };
  const handleChangePeriodic = (event) => {
    const {
      target: { value },
    } = event;
    setState((pre) => pre.set('periodicInterval', Number(value)));
  };
  function onSave() {
    let updateState = state.set('categoryId', state.get('_id', '')).set('isConfigure', true);
    // updateState = updateState.update('attemptType', List(), (attemptType) => {
    //   return fromJS(attemptType).map((att) => constructedAttemptType.get(att));
    // });
    dispatch(updateCategoryConfigure(updateState.toJS(), callBack));
  }
  function onLevelRadioChange(e) {
    setState((prev) => prev.set('level', e.target.value));
  }
  function makeEdit() {
    if (inputDisabled) {
      setInputDisabled(false);
    }
  }
  useEffect(() => {
    if (!attemptType.size) {
      dispatch(getAttemptType('/qapcCategory/getAttemptType'));
    }
  }, []);
  const checkDisable = {
    color: inputDisabled === true ? '#6B7280 !important' : '',
    '&.Mui-checked': {
      color: inputDisabled === true ? '#6B7280 !important' : '',
    },
  };
  return (
    <DialogModal show={open} onClose={handleClose} maxWidth={'sm'} fullWidth={true}>
      <div className="px-4 py-3">
        <div className="d-flex align-items-center pb-2">
          <div className="q360-popup-Header">{state.get('categoryName', '')} Configure</div>
          <div className="ml-auto">
            <EnableOrDisable valid={isEdit && inputDisabled}>
              <ModeEditIcon className="ml-auto cursor-pointer" onClick={makeEdit} />
            </EnableOrDisable>
          </div>
        </div>
        <div className="popup-q360-overflow">
          <div>
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              label={<div className="f-12 fw-400 text-mGrey">{state.get('categoryName', '')}</div>}
              value={state.get('categoryName', '')}
              disabled={true}
              sx={outlinedNone}
            />
          </div>
          <div className="ml-1">
            <FormControl disabled={state.get('isDefault', false) || inputDisabled}>
              <div className="f-12 fw-400 text-mGrey mt-2 ">Level</div>
              <RadioGroup row value={state.get('level', '')} onChange={onLevelRadioChange}>
                {value_label_qlc.map((option, i) => (
                  <FormControlLabel
                    key={option}
                    value={option}
                    control={<Radio size="small" sx={checkDisable} />}
                    label={<div className="text-dark">{label_levels_qlc[i]}</div>}
                    // classes={{ label: classes.label }}
                  />
                ))}
              </RadioGroup>
            </FormControl>
          </div>
          <div>
            <MaterialInput
              elementType={'materialTextArea'}
              type={'text'}
              disabled={inputDisabled}
              variant={'outlined'}
              changed={onDescribeChange}
              value={state.get('describe', '')}
              label={<div className="f-12 fw-400 text-mGrey">Describe (Optional)</div>}
              placeholder={'Describe here'}
              maxRows={'4'}
              minRows={'4'}
              bgWhite={inputDisabled === true ? false : true}
              sx={inputDisabled === true ? outlinedNoneImportant : ''}
            />
          </div>
          {/* <div>
          <div className="f-16 fw-400 text-dGrey mb-2 ">Attempt Type (Optional)</div>
          <MaterialInput
            elementType={'materialSelect'}
            type={'text'}
            variant={'outlined'}
            elementConfig={{ options: programType }}
          />
        </div> */}
          <div className="w-100 ">
            <div className="m-0 f-12 fw-400 text-mGrey mt-2 pb-2">Attempt Type (Optional)</div>
            <Select
              value={fromJS(state.get('attemptType', List())).toJS()}
              onChange={handleChange}
              disabled={inputDisabled}
              multiple
              renderValue={(selected) =>
                selected.map((item) => constructedAttemptType.get(item)?.get('name', '')).join(', ')
              }
              MenuProps={MenuProps}
              className="w-100"
              sx={{
                '& .MuiOutlinedInput-input': {
                  padding: '10.5px 14px',
                },
              }}
            >
              {attemptType.map((name) => (
                <MenuItem key={name.get('_id', '')} value={name.get('_id', '')}>
                  <Checkbox
                    checked={state.get('attemptType', List()).includes(name.get('_id', ''))}
                  />
                  <ListItemText primary={name.get('name', '')} />
                </MenuItem>
              ))}
            </Select>
          </div>

          <div className="ml-1 d-flex flex-column">
            <FormControl disabled={inputDisabled}>
              <div className="f-12 fw-400 text-mGrey mt-2">Category For</div>
              <RadioGroup row value={state.get('categoryFor', '')} onChange={onCategoryForChange}>
                {categoryFor.map((option) => (
                  <FormControlLabel
                    value={option.get('value', '')}
                    key={option.get('value', '')}
                    control={<Radio size="small" sx={checkDisable} />}
                    label={<div className="text-dark">{option.get('name', '')}</div>}
                  />
                ))}
              </RadioGroup>
            </FormControl>
            {state.get('categoryFor', '') === 'periodic' && (
              <>
                <div className="f-12 fw-400 text-mGrey">Periodic Interval</div>
                <TextField
                  placeholder="Enter Periodic Number"
                  size="small"
                  value={state.get('periodicInterval', 0)}
                  onChange={handleChangePeriodic}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&.Mui-focused': {
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid grey',
                        },
                      },
                    },
                    width: '250px',
                  }}
                />
              </>
            )}
          </div>
          <div className={`d-flex align-items-center`}>
            <div>
              <Checkbox
                disabled={inputDisabled}
                onClick={onMandatoryChange('incorporateMandatory')}
                size="small"
                checked={state.get('incorporateMandatory', false)}
                className="pl-0"
                sx={checkDisable}
              />
            </div>
            <div>Check the box to confirm the Incorporate details as mandatory.</div>
          </div>
          <div className={`d-flex align-items-center`}>
            <div>
              <Checkbox
                onClick={onMandatoryChange('displayMandatory')}
                disabled={inputDisabled}
                size="small"
                checked={state.get('displayMandatory', false)}
                className="pl-0"
                sx={checkDisable}
              />
            </div>
            <div>Check the box to confirm the display capture as mandatory.</div>
          </div>
        </div>
        <div className="d-flex align-items-center pt-3">
          <div className="d-flex align-items-center ml-auto gap-20">
            <div>
              <Button
                clicked={handleClose}
                variant="outlined"
                className="px-4"
                size={'small'}
                color={'gray'}
              >
                Cancel
              </Button>
            </div>
            <div>
              <Button
                clicked={onSave}
                variant="contained"
                className="px-4"
                color="primary"
                size={'small'}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DialogModal>
  );
};

CourseSpecificationConfigure.propTypes = {
  open: PropTypes.bool,
  isEdit: PropTypes.bool,
  handleClose: PropTypes.func,
  category: PropTypes.instanceOf(IMap),
};

export default CourseSpecificationConfigure;
