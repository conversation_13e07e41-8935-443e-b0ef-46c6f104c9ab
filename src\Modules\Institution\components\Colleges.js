import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import { Button, Table } from 'react-bootstrap';
import Dialog from '@mui/material/Dialog';
import { Trans } from 'react-i18next';
import InstitutionView from './InstitutionView';
import InstitutionForm from './InstitutionForm';
import AlertConfirmModal from '../../ProgramInput/modal/AlertConfirmModal';
import * as actions from '../../../_reduxapi/institution/actions';
import {
  selectColleges,
  selectInstitution,
  selectCountryList,
  selectShowCollegeModal,
  selectArchivedColleges,
} from '../../../_reduxapi/institution/selectors';
import { selectUserId } from '../../../_reduxapi/Common/Selectors';

import '../css/institution.css';
import '../../ProgramInput/css/program.css';
import '../../../Assets/css/grouping.css';
import { fileSizeTypeCheck } from '../../../utils';

const TABS = ['Colleges'];
const collegeModalInititalData = {
  operation: '',
  type: 'college',
  name: '',
  college: Map(),
};

class Colleges extends Component {
  constructor() {
    super();
    this.state = {
      activeTab: 0,
      collegeModalData: collegeModalInititalData,
      logo: null,
      modalData: {
        show: false,
      },
      collegeViewModalData: {
        show: false,
        institution: Map(),
      },
    };
    this.onTabChange = this.onTabChange.bind(this);
    this.handleAddEditCollege = this.handleAddEditCollege.bind(this);
    this.handleAddEditModalClose = this.handleAddEditModalClose.bind(this);
    this.handleChange = this.handleChange.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.handleArchiveCollege = this.handleArchiveCollege.bind(this);
  }

  componentDidMount() {
    this.props.getInstitution(this.props.userId, true);
    this.props.getCountryList();
  }

  onTabChange(tabIndex) {
    this.setState({
      activeTab: tabIndex,
    });
  }

  handleAddEditCollege(operation, type, college = Map()) {
    this.props.setData(Map({ showCollegeModal: true }));
    const { institution } = this.props;
    this.setState({
      collegeModalData: {
        operation,
        type,
        name: `${operation === 'create' ? 'Add New' : 'Edit'} College`,
        college: Map({
          ...(operation === 'create' && {
            university_name: institution.get('institute_name', ''),
            university_id: institution.get('_id', ''),
          }),
          ...(operation === 'update' && {
            logo: college.get('logo', ''),
            _id: college.get('_id', ''),
          }),
          college_name: college.get('college_name', ''),
          address_details: {
            address: college.getIn(['address_details', 'address'], ''),
            country: college.getIn(['address_details', 'country'], ''),
            state: college.getIn(['address_details', 'state'], ''),
            city: college.getIn(['address_details', 'city'], ''),
            district: college.getIn(['address_details', 'district'], ''),
            zipcode: college.getIn(['address_details', 'zipcode'], ''),
          },
        }),
      },
    });
  }

  isExistingCollege(data) {
    if (!data) return false;
    const { formData } = data;
    const { colleges } = this.props;
    return colleges
      .filter((c) => c.get('_id') !== formData.get('_id'))
      .map((c) => c.get('college_name'))
      .includes(formData.get('college_name'));
  }

  handleSubmit(data) {
    if (data) {
      if (data.operation === 'delete' && data.confirmed === false) {
        this.setModalData({
          show: true,
          title: 'Delete College',
          body: <div>Are you sure you want to delete this college?</div>,
          variant: 'confirm',
          data: { data, operation: 'delete', type: 'college' },
          confirmButtonLabel: 'DELETE',
          cancelButtonLabel: 'CANCEL',
        });
        return;
      }
      if (['create', 'update'].includes(data.operation) && this.isExistingCollege(data)) {
        this.props.setData(Map({ message: 'The college name already exists' }));
        return;
      }
    }
    const { institution } = this.props;
    this.props.addCollege({ institutionId: institution.get('_id'), ...data });
  }

  handleChange(event, name) {
    if (name === 'logo') {
      let files = event;
      if (!files) files = [];
      if (!fileSizeTypeCheck(this.props, files[0])) {
        this.setState({
          logo: null,
        });
        return;
      } else {
        this.setState({
          logo: files.length ? files[0] : null,
        });
        return;
      }
    }

    const value = event.target.value;

    if (['no_of_college', 'zipcode'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    const setOperation = ['address', 'country', 'state', 'city', 'district', 'zipcode'].includes(
      name
    )
      ? 'setIn'
      : 'set';

    this.setState((state) => {
      return {
        collegeModalData: {
          ...state.collegeModalData,
          college: state.collegeModalData.college[setOperation](
            setOperation === 'set' ? name : ['address_details', name],
            value
          ).mergeDeep(
            Map({
              address_details: Map({
                ...(name === 'country' && { state: '', city: '', district: '' }),
                ...(name === 'state' && { city: '', district: '' }),
                ...(name === 'city' && { district: '' }),
              }),
            })
          ),
        },
      };
    });
  }

  handleAddEditModalClose() {
    this.props.setData(Map({ showCollegeModal: false }));
    this.setState({ logo: null, collegeModalData: collegeModalInititalData });
  }

  handleCollegeViewModal(show, college) {
    this.setState({
      collegeViewModalData: {
        show,
        institution: college,
      },
    });
  }

  handleArchiveCollege(data, isConfirmed) {
    const { collegeName, ...requestData } = data;
    if (!isConfirmed) {
      const operation = requestData.isActive ? 'Reset' : 'Archive';
      this.setModalData({
        show: true,
        title: `${operation} College`,
        body: (
          <div>
            <div>
              Are you sure you want to {operation.toLowerCase()} the selected {`'${collegeName}'`}?
            </div>
            {!requestData.isActive && (
              <div className="mt-2">
                {' '}
                <Trans i18nKey={'You_can_restore_anytime_later'}></Trans>{' '}
              </div>
            )}
          </div>
        ),
        variant: 'confirm',
        data: { data, operation: 'archive', type: 'college' },
        confirmButtonLabel: operation.toUpperCase(),
        cancelButtonLabel: <Trans i18nKey={'cancel'}></Trans>,
      });
      return;
    }
    this.props.archiveCollege({ institutionId: this.props.institution.get('_id'), ...requestData });
  }

  setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(titleIcon && { titleIcon }),
        ...(body && { body }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ data, operation, type }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => {
        switch (operation) {
          case 'delete': {
            const { confirmed, ...rest } = data; //eslint-disable-line
            this.handleSubmit(rest);
            return;
          }
          case 'archive': {
            this.handleArchiveCollege(data, true);
            return;
          }
          default:
            return;
        }
      }
    );
  }

  getActiveTabComponent() {
    switch (this.state.activeTab) {
      case 0:
        return this.getCollegesComponent();
      default:
        return 'Unknown Tab';
    }
  }

  getCollegeCard(c, isArchived) {
    const address = c.get('address_details', Map());
    return (
      <div key={c.get('_id')}>
        <div className="row digi-set-table m-0 ">
          <div className="col-md-8 col-lg-8 col-xl-8">
            <div className="digi-font-500 f-15 color-gray">
              <h5 className="mb-2">{c.get('college_name', '')}</h5>
              <div>{`${address.get('address', '')}`}</div>
              <div>{`${address.get('city', '')}, ${address.get('state', '')} ${address.get(
                'zipcode',
                ''
              )}`}</div>
              <div>{`${address.get('country', '')}`}</div>
            </div>
          </div>
          <div className="col-md-4 col-lg-4 col-xl-4  ">
            <div className="d-flex float-right mt-2 myDIV">
              <span className="pr-3 ss">
                <Button
                  variant="outline-primary"
                  style={{ width: '100px' }}
                  onClick={() => {
                    if (isArchived) {
                      this.handleArchiveCollege(
                        {
                          isActive: true,
                          _id: c.get('_id'),
                          collegeName: c.get('college_name', ''),
                        },
                        false
                      );
                    } else {
                      this.handleCollegeViewModal(
                        true,
                        c.merge(
                          Map({
                            institute_type: 'individual',
                            institute_name: c.get('college_name', ''),
                          })
                        )
                      );
                    }
                  }}
                >
                  {isArchived ? 'RESET' : 'VIEW'}
                </Button>
              </span>

              <div className="collegeView">
                {!isArchived ? (
                  <>
                    <span className="mt-2 pr-3">
                      <i
                        className="fa fa-pencil remove_hover cursor-pointer"
                        aria-hidden="true"
                        onClick={() => this.handleAddEditCollege('update', 'college', c)}
                      ></i>
                    </span>
                    <span className="mt-2">
                      <i
                        className="fa fa-archive remove_hover cursor-pointer"
                        aria-hidden="true"
                        onClick={() =>
                          this.handleArchiveCollege(
                            {
                              isActive: false,
                              _id: c.get('_id'),
                              collegeName: c.get('college_name', ''),
                            },
                            false
                          )
                        }
                      ></i>
                    </span>
                  </>
                ) : (
                  <span className="mt-2">
                    <i
                      className="fa fa-trash remove_hover cursor-pointer"
                      aria-hidden="true"
                      onClick={() =>
                        this.handleSubmit({
                          operation: 'delete',
                          _id: c.get('_id'),
                          confirmed: false,
                        })
                      }
                    ></i>
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  getCollegesComponent() {
    const { colleges, archivedColleges } = this.props;
    return (
      <>
        {colleges.isEmpty() ? (
          this.getEmptyView('college', this.handleAddEditCollege)
        ) : (
          <div className="colleges_card bg-lightwhite">
            {colleges.map((c) => this.getCollegeCard(c, false))}
          </div>
        )}
        {!archivedColleges.isEmpty() && (
          <div className="mb-3">
            <h5 className="pb-2 pt-4">Archives</h5>
            <div className="colleges_card bg-lightwhite">
              {archivedColleges.map((c) => this.getCollegeCard(c, true))}
            </div>
          </div>
        )}
      </>
    );
  }

  getEmptyView(type, handler) {
    return (
      <div className="program_card bg-lightwhite">
        <div className="p-1 min_h">
          <Table responsive hover>
            <tbody></tbody>
          </Table>
        </div>
        <div className="text-uppercase text-center">
          <Button variant="primary" onClick={() => handler('create', type, Map())}>
            ADD NEW COLLEGE
          </Button>
        </div>
      </div>
    );
  }

  render() {
    const { activeTab, collegeModalData, logo, modalData, collegeViewModalData } = this.state;
    const { countryList, colleges, showCollegeModal } = this.props;
    return (
      <>
        <div className="customize_tab">
          <ul id="menu">
            {TABS.map((tab, i) => (
              <span
                key={`${tab}-${i}`}
                onClick={this.completeTab}
                className={`tabaligment cursor-pointer ${activeTab === i ? 'tabactive' : ''}`}
              >
                {tab}
              </span>
            ))}
          </ul>
        </div>
        <div className="main pt-3 pb-5 bg-gray">
          <div className="container">
            <div className="p-3">
              <div className="d-flex justify-content-between">
                <h5 className="pb-2 pt-2">{TABS[activeTab]}</h5>
                <div className="pb-2">
                  {activeTab === 0 && !colleges.isEmpty() && (
                    <Button
                      variant="primary"
                      className="border-radious-8 f-14"
                      onClick={() => this.handleAddEditCollege('create', 'college', Map())}
                    >
                      ADD NEW COLLEGE
                    </Button>
                  )}
                </div>
              </div>
              {this.getActiveTabComponent()}
            </div>
          </div>
        </div>
        <Dialog maxWidth="xs" open={showCollegeModal} onClose={this.handleAddEditModalClose}>
          {showCollegeModal && (
            <InstitutionForm
              countryList={countryList}
              institutionType={{ type: collegeModalData.type, name: collegeModalData.name }}
              institution={collegeModalData.college}
              logo={logo}
              handleChange={this.handleChange}
              handleBack={this.handleAddEditModalClose}
              handleSubmit={this.handleSubmit}
              operation={collegeModalData.operation}
            />
          )}
        </Dialog>
        <Dialog
          maxWidth="xs"
          open={collegeViewModalData.show}
          onClose={() => this.handleCollegeViewModal(false, Map())}
        >
          {collegeViewModalData.show && (
            <>
              <InstitutionView
                institution={collegeViewModalData.institution}
                showBoxShadow={false}
              />
              <div className="d-flex justify-content-flex-end mb-3 mr-3 ml-3">
                <div>
                  <Button
                    onClick={() => this.handleCollegeViewModal(false, Map())}
                    variant="primary"
                  >
                    CLOSE
                  </Button>
                </div>
              </div>
            </>
          )}
        </Dialog>
        <AlertConfirmModal
          show={modalData.show}
          title={modalData.title || ''}
          titleIcon={modalData.titleIcon}
          body={modalData.body || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />
      </>
    );
  }
}

Colleges.propTypes = {
  userId: PropTypes.string,
  getInstitution: PropTypes.func,
  setData: PropTypes.func,
  getColleges: PropTypes.func,
  addCollege: PropTypes.func,
  getCountryList: PropTypes.func,
  archiveCollege: PropTypes.func,
  institution: PropTypes.instanceOf(Map),
  countryList: PropTypes.instanceOf(List),
  colleges: PropTypes.instanceOf(List),
  archivedColleges: PropTypes.instanceOf(List),
  showCollegeModal: PropTypes.bool,
};

const mapStateToProps = (state) => {
  return {
    userId: selectUserId(state),
    institution: selectInstitution(state),
    colleges: selectColleges(state),
    countryList: selectCountryList(state),
    showCollegeModal: selectShowCollegeModal(state),
    archivedColleges: selectArchivedColleges(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Colleges);
