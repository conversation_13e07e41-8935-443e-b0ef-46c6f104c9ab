import React, { Component } from 'react';
import PropTypes from 'prop-types';

class Tab extends Component {
  static propTypes = {
    activeTab: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    onClick: PropTypes.func.isRequired,
  };

  onClick = () => {
    const { label, onClick } = this.props;
    onClick(label);
  };

  render() {
    const {
      onClick,
      props: { activeTab, label },
    } = this;

    let className = 'tab-list-item-white';

    if (activeTab === label) {
      className += ' tab-list-active-white';
    }

    return (
      <li className={className} onClick={onClick}>
        <span className="tab-bg">{label}</span>
      </li>
    );
  }
}

export default Tab;
