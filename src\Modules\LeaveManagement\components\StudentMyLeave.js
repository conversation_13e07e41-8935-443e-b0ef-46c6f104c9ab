import React, { Component, Suspense } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import moment from 'moment';
import { Button, Table } from 'react-bootstrap';
import PropTypes, { oneOfType } from 'prop-types';
import { t } from 'i18next';
import { Map, List } from 'immutable';

import { capitalize } from '../../InfrastructureManagement/utils';
import AlertModal from '../../InfrastructureManagement/modal/AlertModal';
import Pagination from '../../StudentGrouping/components/Pagination';
import * as actions from '../../../_reduxapi/leave_management/actions';
import {
  selectInstitutionCalendar,
  selectLeaveCategories,
  selectPermissionList,
  selectLeaveOverview,
  selectStudentLeaveList,
} from '../../../_reduxapi/leave_management/selectors';
import {
  selectSelectedRole,
  selectActiveInstitutionCalendar,
} from '../../../_reduxapi/Common/Selectors';
import '../../../Assets/css/leave_management.css';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getTimestamp, eString, getURLParams } from '../../../utils';

import { getLang } from 'utils';
const lang = getLang();
const StudentProfileModal = React.lazy(() => import('../modal/StudentProfileModal'));

var totalPages;
class StudentMyLeave extends Component {
  constructor() {
    super();
    this.state = {
      activeTab: 1,
      modalData: { show: false },
      show: false,
      status: null,
      pageCount: 10,
      currentPage: 1,
      searchTerm: '',
      studentId: '',
      deleteId: null,
      sortBy: { type: '', orderBy: true },
      profileModal: { status: false, studentId: '' },
      programId: getURLParams('id', true),
    };
  }

  columnSorting = (a, b, name, type = '') => {
    const { sortBy } = this.state;
    if (type === '') {
      return sortBy.orderBy
        ? `${a.getIn(name)}` > `${b.getIn(name)}`
          ? 1
          : -1
        : `${b.getIn(name)}` > `${a.getIn(name)}`
        ? 1
        : -1;
    } else if (type === 'TIME') {
      return sortBy.orderBy
        ? getTimestamp(a.getIn(name)) - getTimestamp(b.getIn(name))
        : getTimestamp(b.getIn(name)) - getTimestamp(a.getIn(name));
    }
  };

  handleSaveClick = (id, operation) => {
    if (operation === 'delete') {
      const institution_calendar = this.props.activeInstitutionCalendar.get('_id');
      const { programId } = this.state;
      this.props.deletestudent(id, () => {
        this.props.studentLeaveList(institution_calendar, programId);
      });
    }
  };

  handleDeleteClick(data) {
    const id = data.get('_id');
    this.setState({ deleteId: id });
    this.setModalData({
      show: true,
      title: 'Confirm delete',
      description: 'Are you sure you want to delete',
      variant: 'confirm',
      cancelButtonLabel: 'CANCEL',
      data: id,
    });
    return;
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
      },
      studentId: data,
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm(id) {
    const { deleteId } = this.state;
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.handleSaveClick(deleteId, 'delete')
    );
  }

  componentDidMount() {
    const { activeInstitutionCalendar, studentLeaveList, setBreadCrumbName } = this.props;
    setBreadCrumbName(t('side_nav.menus.student_register'));
    if (activeInstitutionCalendar.get('_id', '') !== '') {
      const { programId } = this.state;
      studentLeaveList(activeInstitutionCalendar.get('_id'), programId);
    }
  }

  componentDidUpdate(prevProps) {
    const { activeInstitutionCalendar, studentLeaveList } = this.props;
    if (
      prevProps.activeInstitutionCalendar !== activeInstitutionCalendar &&
      activeInstitutionCalendar.get('_id', '') !== ''
    ) {
      const { programId } = this.state;
      studentLeaveList(activeInstitutionCalendar.get('_id'), programId);
    }
  }

  onNextClick = () => {
    if (this.state.currentPage + 1 >= totalPages) {
      this.setState({
        currentPage: totalPages,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage + 1,
      });
    }
  };

  handleChangeSearch = (e) => {
    this.setState({
      searchTerm: e.target.value,
    });
  };

  onFullLastClick = () => {
    this.setState({
      currentPage: 1,
    });
  };

  onFullForwardClick = () => {
    this.setState({
      currentPage: totalPages,
    });
  };

  onBackClick = () => {
    if (this.state.currentPage - 1 === 0) {
      this.setState({
        currentPage: 1,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage - 1,
      });
    }
  };

  pagination = (value) => {
    this.setState({
      pageCount: value,
      currentPage: 1,
    });
  };

  handleGoBack = () => {
    this.props.history.push(
      `/leave-management/student-register?id=${eString(this.state.programId)}`
    );
  };

  navigateToApplyLeave = () => {
    this.props.history.push(
      `/leave-management/student-register/new?id=${eString(this.state.programId)}`
    );
  };

  editLeave(data) {
    const id = data.get('_id');
    const type = data.get('type', 'leave');
    this.props.history.push(
      `/leave-management/student-register/${id}?type=${eString(type)}&id=${eString(
        this.state.programId
      )}`
    );
  }

  getProgramName = () => {
    const { selectedRole } = this.props;
    const { programId } = this.state;
    const program = selectedRole.get('program', List());
    if (program.size > 0) {
      const programName = program.find((item) => item.get('_program_id') === programId);
      return programName.get('program_name', '') !== ''
        ? `(${programName.get('program_name', '')})`
        : '';
    }
    return '';
  };

  render() {
    const { modalData, pageCount, currentPage, searchTerm, sortBy, profileModal } = this.state;
    const { StudentLeaveList } = this.props;
    const SearchText = searchTerm.toLowerCase();
    totalPages =
      StudentLeaveList?.size % pageCount === 0
        ? StudentLeaveList?.size / pageCount
        : Math.floor(StudentLeaveList?.size / pageCount) + 1;
    return (
      <React.Fragment>
        <div className="main pb-5 bg-gray ">
          <div className="bg-white pt-3 pb-3">
            <div className="container-fluid">
              <p className="font-weight-bold mb-0 f-17 text-left">
                {' '}
                <i
                  className={`fa fa-arrow-${lang === 'ar' ? 'right' : 'left'} pr-3`}
                  aria-hidden="true"
                  onClick={this.handleGoBack}
                ></i>
                {t('side_nav.menus.student_register')}{' '}
              </p>
            </div>
          </div>
          <div className="container ">
            {/* end - dean review buttons*/}
            <div className="p-2">
              <div className="row pt-4">
                <div className="col-md-12">
                  <div className="d-flex justify-content-between">
                    <p className="f-18 mb-0 pt-3 bold">
                      {' '}
                      {t('leaveManagement.studentLeaveEntries')} {this.getProgramName()}
                    </p>
                    {CheckPermission(
                      'tabs',
                      'Leave Management',
                      'Student Register',
                      '',
                      'Student Leave Entry',
                      'Add'
                    ) && (
                      <div className="">
                        <Button
                          variant="primary"
                          className="m-2"
                          onClick={this.navigateToApplyLeave}
                        >
                          {t('leaveManagement.addNewEntry')}
                        </Button>
                      </div>
                    )}
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <p className="f-14 bold"> {t('leaveManagement.listAllStudents')} </p>
                    <div className="sb-example-1 w-50">
                      {CheckPermission(
                        'tabs',
                        'Leave Management',
                        'Student Register',
                        '',
                        'Student Leave Entry',
                        'Search'
                      ) && (
                        <div className="search">
                          <input
                            type="text"
                            className="searchTerm"
                            placeholder={t('leaveManagement.searchByStudents')}
                            value={searchTerm}
                            maxLength={100}
                            onChange={(e) => this.handleChangeSearch(e)}
                          />
                          <button type="submit" className="searchButton">
                            <i className="fa fa-search"></i>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="border rounded bg-white">
                    <div className="go-wrapper">
                      <Table className="table">
                        <thead className="th-change">
                          <tr>
                            <th>
                              <div className="aw-50">{t('s_no')}</div>
                            </th>
                            <th>
                              {' '}
                              <div className="aw-150">
                                <i
                                  className={`fa fa-sort-amount-${
                                    sortBy.type === 'ACADEMIC_ID' && !sortBy.orderBy ? 'down' : 'up'
                                  } remove_hover pr-2`}
                                  onClick={() =>
                                    this.setState({
                                      sortBy: {
                                        type: 'ACADEMIC_ID',
                                        orderBy: !this.state.sortBy.orderBy,
                                      },
                                    })
                                  }
                                  aria-hidden="true"
                                ></i>
                                {t('academic_id')}
                              </div>
                            </th>
                            <th>
                              <div className="aw-150">
                                <i
                                  className={`fa fa-sort-amount-${
                                    sortBy.type === 'STUDENT_NAME' && !sortBy.orderBy
                                      ? 'down'
                                      : 'up'
                                  } remove_hover pr-2`}
                                  onClick={() =>
                                    this.setState({
                                      sortBy: {
                                        type: 'STUDENT_NAME',
                                        orderBy: !this.state.sortBy.orderBy,
                                      },
                                    })
                                  }
                                  aria-hidden="true"
                                ></i>
                                {t('events.student')}
                              </div>
                            </th>
                            <th>
                              {' '}
                              <div className="aw-150">
                                <i
                                  className={`fa fa-sort-amount-${
                                    sortBy.type === 'APPLICATION_DATE' && !sortBy.orderBy
                                      ? 'down'
                                      : 'up'
                                  } remove_hover pr-2`}
                                  onClick={() =>
                                    this.setState({
                                      sortBy: {
                                        type: 'APPLICATION_DATE',
                                        orderBy: !this.state.sortBy.orderBy,
                                      },
                                    })
                                  }
                                  aria-hidden="true"
                                ></i>
                                {t('leaveManagement.applicationDate')}
                              </div>
                            </th>
                            <th>
                              {' '}
                              <div className="aw-150">
                                <i
                                  className={`fa fa-sort-amount-${
                                    sortBy.type === 'LEAVE_TYPE' && !sortBy.orderBy ? 'down' : 'up'
                                  } remove_hover pr-2`}
                                  onClick={() =>
                                    this.setState({
                                      sortBy: {
                                        type: 'LEAVE_TYPE',
                                        orderBy: !this.state.sortBy.orderBy,
                                      },
                                    })
                                  }
                                  aria-hidden="true"
                                ></i>
                                {t('leaveManagement.leaveType')}
                              </div>
                            </th>
                            <th>
                              <div className="aw-150">{t('dashboard_view.time')}</div>
                            </th>
                            <th>
                              <div className="aw-200">{t('leaveManagement.dates_')}</div>
                            </th>
                            <th>
                              <div className="aw-150">{t('leaveManagement.Total_No_of_days')}</div>
                            </th>
                            <th>
                              <div className="aw-150">{t('leaveManagement.approveOn')}</div>
                            </th>
                            <th>
                              <div className="aw-100">{t('leaveManagement.approveOn')}</div>
                            </th>
                          </tr>
                        </thead>
                        <tbody className="go-wrapper-height">
                          {StudentLeaveList?.filter(
                            (data, i) =>
                              i >= pageCount * (currentPage - 1) && i < pageCount * currentPage
                          )
                            .filter(
                              (data, i) =>
                                data
                                  .getIn(['_user_id', 'user_id'], '')
                                  .toLowerCase()
                                  .includes(SearchText) ||
                                data
                                  .getIn(['_user_id', 'name', 'first'], '')
                                  .toLowerCase()
                                  .includes(SearchText) ||
                                data
                                  .getIn(['_user_id', 'name', 'middle'], '')
                                  .toLowerCase()
                                  .includes(SearchText) ||
                                data
                                  .getIn(['_user_id', 'name', 'last'], '')
                                  .toLowerCase()
                                  .includes(SearchText) ||
                                `${data.getIn(['_user_id', 'name', 'first'], '')}${data.getIn(
                                  ['_user_id', 'name', 'middle'],
                                  ''
                                )}${data.getIn(['_user_id', 'name', 'last'], '')}`
                                  .toLowerCase()
                                  .includes(SearchText)
                            )
                            // eslint-disable-next-line
                            .sort((a, b) => {
                              if (sortBy.type === 'ACADEMIC_ID')
                                return this.columnSorting(a, b, ['_user_id', 'user_id'], '');
                              else if (sortBy.type === 'STUDENT_NAME')
                                return this.columnSorting(a, b, ['_user_id', 'name', 'first'], '');
                              else if (sortBy.type === 'LEAVE_TYPE')
                                return this.columnSorting(a, b, ['type'], '');
                              else if (sortBy.type === 'APPLICATION_DATE')
                                return this.columnSorting(a, b, ['application_date'], 'TIME');
                            })
                            ?.map((data, i) => {
                              return (
                                <tr className="tr-change" key={i}>
                                  <td>
                                    <div className="aw-50">{i + 1}</div>
                                  </td>
                                  <td>
                                    <div
                                      onClick={() => {
                                        if (
                                          CheckPermission(
                                            'pages',
                                            'Leave Management',
                                            'Student Register',
                                            'Profile View'
                                          )
                                        ) {
                                          this.setState({
                                            profileModal: {
                                              status: true,
                                              studentId: data.getIn(['_user_id', '_id'], ''),
                                            },
                                          });
                                        }
                                      }}
                                      className={`aw-150 ${
                                        CheckPermission(
                                          'pages',
                                          'Leave Management',
                                          'Student Register',
                                          'Profile View'
                                        )
                                          ? 'text-skyblue'
                                          : ''
                                      } bold remove_hover pl-4`}
                                    >
                                      {data.getIn(['_user_id', 'user_id'])}
                                    </div>
                                  </td>
                                  <td>
                                    <div className="aw-150 text-skyblue pl-4">
                                      {data.getIn(['_user_id', 'name', 'first'], '') +
                                        data.getIn(['_user_id', 'name', 'middle'], '') +
                                        data.getIn(['_user_id', 'name', 'last'], '')}
                                    </div>
                                  </td>
                                  <td>
                                    {' '}
                                    <div className="aw-150 pl-4">
                                      {moment(data.get('application_date')).format('DD MMM	YYYY')}
                                    </div>
                                  </td>
                                  <td>
                                    <div className="aw-150 pl-4">
                                      {' '}
                                      {capitalize(data.get('type', ''))}
                                    </div>
                                  </td>
                                  <td>
                                    <div className="aw-150">
                                      {' '}
                                      {moment(data.get('from')).format(' hh:mm A')}
                                      {''} -{moment(data.get('to')).format(' hh:mm A')}
                                    </div>
                                  </td>
                                  <td>
                                    {data.get('type', '') === 'permission' && (
                                      <div className="aw-200">
                                        {moment(data.get('from')).format('DD MMM	YYYY')}
                                        {''}
                                      </div>
                                    )}
                                    {data.get('type', '') !== 'permission' && (
                                      <div className="aw-200">
                                        {moment(data.get('from')).format('DD MMM	YYYY')}
                                        {''} to {moment(data.get('to')).format('DD MMM	YYYY')}{' '}
                                      </div>
                                    )}
                                  </td>
                                  <td>
                                    <div className="aw-150">{data.get('days', 0)}</div>
                                  </td>
                                  <td>
                                    <div className="aw-150">
                                      {moment(data.getIn(['approved_by', 'date'])).format(
                                        'DD MMM	YYYY'
                                      )}
                                    </div>
                                  </td>
                                  <td>
                                    <div className="aw-100 d-flex justify-content-space w-30 ">
                                      {CheckPermission(
                                        'tabs',
                                        'Leave Management',
                                        'Student Register',
                                        '',
                                        'Student Leave Entry',
                                        'Edit'
                                      ) && (
                                        <b className="float-left pr-2 f-16">
                                          <i
                                            className="fa fa-pencil fa-lg remove_hover mr-2 f-14"
                                            onClick={this.editLeave.bind(this, data)}
                                          />
                                        </b>
                                      )}

                                      {CheckPermission(
                                        'tabs',
                                        'Leave Management',
                                        'Student Register',
                                        '',
                                        'Student Leave Entry',
                                        'Delete'
                                      ) && (
                                        <b className="float-left pl-2 f-16">
                                          <i
                                            className="fa fa-trash fa-lg remove_hover f-14"
                                            onClick={this.handleDeleteClick.bind(this, data)}
                                          />
                                        </b>
                                      )}
                                    </div>
                                  </td>{' '}
                                </tr>
                              );
                            })}
                        </tbody>
                      </Table>
                    </div>
                  </div>
                  {StudentLeaveList?.size !== 0 && (
                    <Pagination
                      pagination={this.pagination}
                      onNextClick={this.onNextClick}
                      pagevalue={pageCount}
                      onBackClick={this.onBackClick}
                      onFullLastClick={this.onFullLastClick}
                      onFullForwardClick={this.onFullForwardClick}
                      data={totalPages}
                      currentPage={currentPage}
                    />
                  )}
                  <AlertModal
                    show={modalData.show}
                    title={modalData.title || ''}
                    description={modalData.description || ''}
                    variant={modalData.variant || 'confirm'}
                    confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
                    cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
                    onClose={this.onModalClose.bind(this)}
                    onConfirm={this.onConfirm.bind(this)}
                  />
                  {profileModal.status && (
                    <Suspense fallback="">
                      <StudentProfileModal
                        profileModal={profileModal}
                        setProfileModal={() =>
                          this.setState({
                            profileModal: {
                              status: false,
                              studentId: '',
                            },
                          })
                        }
                      />
                    </Suspense>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    institutionCalendar: selectInstitutionCalendar(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    leaveCategories: selectLeaveCategories(state),
    permissions: selectPermissionList(state),
    leaveOverview: selectLeaveOverview(state),
    StudentLeaveList: selectStudentLeaveList(state),
    selectedRole: selectSelectedRole(state),
  };
};

StudentMyLeave.propTypes = {
  selectedRole: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  studentLeaveList: PropTypes.func,
  deletestudent: PropTypes.func,
  StudentLeaveList: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  history: PropTypes.object,
  setBreadCrumbName: PropTypes.func,
};

export default compose(withRouter, connect(mapStateToProps, actions))(StudentMyLeave);
