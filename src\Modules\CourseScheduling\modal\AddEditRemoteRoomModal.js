import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Button, Modal, Form } from 'react-bootstrap';

import Input from '../../../Widgets/FormElements/Input/Input';
import { capitalize } from '../../../utils';
import { t } from 'i18next';
import { Trans } from 'react-i18next';

const GENDER = {
  MALE: 'male',
  FEMALE: 'female',
  BOTH: 'both',
};
const GENDER_OPTIONS = [{ name: '', value: '' }].concat(
  Object.values(GENDER).map((gender) => {
    return {
      name: capitalize(gender),
      value: gender,
    };
  })
);

function AddEditRemoteRoomModal({ show, onHide, onSave, onChange, mode, data }) {
  return (
    <Modal show={show} onHide={onHide} dialogClassName="modal-600" centered>
      <Modal.Body>
        <div className="container-fluid pt-2 pb-1">
          <div className="border-bottom">
            <p className="mb-2 font-weight-bold">{`${
              mode === t('create') ? t('add') : t('edit')
            } ${t('infra_management.remote.room')}`}</p>
          </div>
          <div className="row pt-3">
            <div className="col-md-8">
              <Input
                elementType="input"
                elementConfig={{ type: 'text' }}
                value={data.get('meetingTitle', '')}
                changed={(e) => onChange('meetingTitle', e.target.value)}
                className="form-control"
                label={
                  t('infra_management.remote.remote_rooms_list_table_headers.meetingTitle') + '*'
                }
                labelclass="mb-1 f-14"
              />
            </div>
            <div className="col-md-4">
              <Input
                elementType="select"
                elementConfig={{ options: GENDER_OPTIONS }}
                value={data.get('gender', '')}
                changed={(e) => onChange('gender', e.target.value)}
                label={t('infra_management.remote.remote_rooms_list_table_headers.gender') + '*'}
                labelclass="mb-1 f-14"
              />
            </div>
          </div>
          <div className="row pt-2">
            <div className="col-md-12">
              <p className="mb-1 mt-2"> App</p>

              <Form.Check
                custom
                inline
                type="radio"
                label="Zoom"
                name="formHorizontalRadios"
                id="formHorizontalRadios4"
                className="mt-1 ml-2 mr-5"
                checked={data.get('remotePlatform', '') === 'zoom'}
                onChange={(e) => onChange('remotePlatform', 'zoom')}
              />

              <Form.Check
                custom
                inline
                type="radio"
                label="Microsoft Teams"
                name="formHorizontalRadios"
                id="formHorizontalRadios3"
                className="mt-1 ml-2"
                checked={data.get('remotePlatform', '') === 'teams'}
                onChange={(e) => onChange('remotePlatform', 'teams')}
              />
            </div>
          </div>
          {data.get('remotePlatform', '') === 'zoom' && (
            <>
              <div className="row pt-2">
                <div className="col-md-8">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('meetingUrl', '')}
                    changed={(e) => onChange('meetingUrl', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.meetingUrl') + '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
                <div className="col-md-4 ">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('meetingId', '')}
                    changed={(e) => onChange('meetingId', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.meetingId') + '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
              </div>
              <div className="row pt-2">
                <div className="col-md-8">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('meetingUsername', '')}
                    changed={(e) => onChange('meetingUsername', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.meetingUsername') +
                      '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
                <div className="col-md-4 ">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('passCode', '')}
                    changed={(e) => onChange('passCode', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.passCode') + '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
              </div>
              <div className="row pt-2">
                <div className="col-md-8">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('associatedEmail', '')}
                    changed={(e) => onChange('associatedEmail', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.associatedEmail') +
                      '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
                <div className="col-md-4 ">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('password', '')}
                    changed={(e) => onChange('password', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.password') + '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
              </div>
              <div className="row pt-2">
                <div className="col-md-8">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('apiKey', '')}
                    changed={(e) => onChange('apiKey', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.apiKey') + '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
                <div className="col-md-4 ">
                  <Input
                    elementType="input"
                    elementConfig={{ type: 'text' }}
                    value={data.get('apiSecretKey', '')}
                    changed={(e) => onChange('apiSecretKey', e.target.value)}
                    className="form-control"
                    label={
                      t('infra_management.remote.remote_rooms_list_table_headers.apiSecretKey') +
                      '*'
                    }
                    labelclass="mb-1 f-14"
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="pr-2">
          <Button variant="outline-primary" onClick={onHide}>
            <Trans i18nKey={'cancel'}></Trans>
          </Button>
        </div>
        <div className="pr-2">
          <Button variant="primary" onClick={onSave}>
            <Trans i18nKey={'save'}></Trans>
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
}

AddEditRemoteRoomModal.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  onSave: PropTypes.func,
  onChange: PropTypes.func,
  mode: PropTypes.string,
  data: PropTypes.instanceOf(Map),
};

export default AddEditRemoteRoomModal;
