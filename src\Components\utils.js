export function clickArrow(index) {
  const { selectedIndex } = this.state;
  this.setState({
    selectedIndex: selectedIndex !== index ? index : '',
  });
}

export let ACADEMIC_DESIGNATION = [
  {
    name: 'Assistant Professor',
    value: 'Assistant Professor',
  },
  {
    name: 'Associate Professor',
    value: 'Associate Professor',
  },
  {
    name: 'Lab Assistant',
    value: 'Lab Assistant',
  },
  {
    name: 'Lab Associate',
    value: 'Lab Associate',
  },
  {
    name: 'Lab In-charge',
    value: 'Lab In-charge',
  },
  {
    name: 'Laboratory Technician',
    value: 'Laboratory Technician',
  },
  {
    name: 'Language Teacher',
    value: 'Language Teacher',
  },
  {
    name: 'Lecturer',
    value: 'Lecturer',
  },
  {
    name: 'Network & Security Engineer',
    value: 'Network & Security Engineer',
  },
  {
    name: 'Others',
    value: 'Others',
  },
  {
    name: 'Professor',
    value: 'Professor',
  },
  {
    name: 'Teaching Assistant',
    value: 'Teaching Assistant',
  },
  {
    name: 'Tutor',
    value: 'Tutor',
  },
];

export let STAFF_TYPE = [
  { name: 'Academic Allocation', value: 'academic', isChecked: false },
  {
    name: 'Administrative Staff',
    value: 'administration',
    isChecked: false,
  },
];
