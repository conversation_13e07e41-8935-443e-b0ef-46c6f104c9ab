import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import Button from '@mui/material/Button';
import { ThemeProvider } from '@mui/styles';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import Filters from './Filters';
import CourseTable from './CourseTable';
import AlertConfirmModal from '../../modal/AlertConfirmModal';
import { selectCourseCoordinators } from '../../../../_reduxapi/course_scheduling/selectors';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import * as actions from '../../../../_reduxapi/course_scheduling/action';
import { MUI_THEME, getURLParams } from '../../../../utils';
import '../../css/courseSchedule.css';
import { t } from 'i18next';
import withCalendarHooks from 'Hoc/withCalendarHooks';

class CourseCoordinators extends Component {
  constructor(props) {
    super(props);
    this.state = {
      programId: getURLParams('programId', true),
      programName: getURLParams('programName', true),
      filter: Map({
        term: '',
        status: '',
        yearLevel: Map(),
        department: '',
        subject: '',
      }),
      sortDirection: 'asc',
      modalData: {
        show: false,
      },
    };
    this.fetchCourseCoordinators = this.fetchCourseCoordinators.bind(this);
    this.handleBackClick = this.handleBackClick.bind(this);
    this.handleFilterChange = this.handleFilterChange.bind(this);
    this.handleSortDirectionChange = this.handleSortDirectionChange.bind(this);
  }

  componentDidMount() {
    this.fetchCourseCoordinators();
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.activeInstitutionCalendar.get('_id') !==
      prevProps.activeInstitutionCalendar.get('_id')
    ) {
      this.fetchCourseCoordinators();
    }
  }

  handleBackClick() {
    this.props.history.goBack();
  }

  fetchCourseCoordinators() {
    this.props.setData(Map({ courseCoordinators: Map() }));
    const activeInstitutionCalendarId = this.props.activeInstitutionCalendar.get('_id');
    const programId = this.state.programId;
    if (activeInstitutionCalendarId && programId) {
      this.props.getCourseCoordinators(activeInstitutionCalendarId, programId);
    }
  }

  getCourses() {
    const { courseCoordinators } = this.props;
    const { filter, sortDirection } = this.state;
    const courses = courseCoordinators.get('courses', List());
    const filteredCourses = courses
      .filter((course) => {
        const term = filter.get('term');
        const status = filter.get('status');
        const yearLevel = filter.get('yearLevel');
        const department = filter.get('department');
        const subject = filter.get('subject');
        if (
          !term &&
          !status &&
          yearLevel.every((v) => v.get('levels', List()).size === 0) &&
          !department &&
          !subject
        ) {
          return true;
        }
        let incTerm = true;
        let incStatus = true;
        let incYearLevel = true;
        let incDepartment = true;
        let incSubject = true;
        if (term) {
          incTerm = course.get('term') === term;
        }
        if (status) {
          const isAssigned = Boolean(course.getIn(['coordinators', '_user_id']));
          incStatus = status === 'assigned' ? isAssigned : !isAssigned;
        }
        if (yearLevel.some((v) => v.get('levels', List()).size)) {
          const levels = yearLevel.entrySeq().reduce((acc, [key, value]) => {
            return acc.concat(value.get('levels', List()).map((l) => Map({ year: key, level: l })));
          }, List());
          incYearLevel =
            levels.findIndex(
              (l) =>
                l.get('year') === course.get('year') && l.get('level') === course.get('level_no')
            ) !== -1;
        }
        if (department) {
          incDepartment = course.getIn(['administration', '_department_id']) === department;
        }
        if (subject) {
          incSubject = course.getIn(['administration', '_subject_id']) === subject;
        }
        return incTerm && incStatus && incYearLevel && incDepartment && incSubject;
      })
      .toList();
    return filteredCourses.sort((c1, c2) => {
      const v1 = `${c1.get('year', '')} - ${c1.get('level_no', '')}`;
      const v2 = `${c2.get('year', '')} - ${c2.get('level_no', '')}`;
      const v3 = `${c1.get('courses_number', '')} - ${c1.get('courses_name', '')}`;
      const v4 = `${c2.get('courses_number', '')} - ${c2.get('courses_name', '')}`;
      if (sortDirection === 'asc') {
        return (
          new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2) ||
          new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v3, v4)
        );
      }
      return (
        new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v2, v1) ||
        new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v4, v3)
      );
    });
  }

  getTerms() {
    const { courseCoordinators } = this.props;
    return courseCoordinators.get('term', List());
  }

  handleFilterChange(value, name) {
    this.setState((state) => {
      return {
        filter: state.filter.merge(
          Map({ [name]: value, ...(name === 'department' && { subject: '' }) })
        ),
      };
    });
  }

  handleSortDirectionChange() {
    const { sortDirection } = this.state;
    this.setState({ sortDirection: sortDirection === 'asc' ? 'desc' : 'asc' });
  }

  publish(isConfirmed) {
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: t('course_schedule.confirm_publish'),
        body: <div>{t('course_schedule.confirm_publish_desc')}</div>,
        variant: 'confirm',
        data: { data: {}, operation: 'publish' },
        confirmButtonLabel: t('publish'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    this.props.publishCourseCoordinator({
      _institution_calendar_id: this.props.activeInstitutionCalendar.get('_id'),
      _program_id: getURLParams('programId', true),
    });
  }

  setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(titleIcon && { titleIcon }),
        ...(body && { body }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ data, operation }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => {
        switch (operation) {
          case 'publish': {
            this.publish(true);
            return;
          }
          default:
            return;
        }
      }
    );
  }

  disabledPublish = () => {
    const courses = this.getCourses();
    const filterAssignedCoordinator = courses.filter(
      (item) =>
        item.getIn(['coordinators', '_user_id'], '') !== '' &&
        item.getIn(['coordinators', '_user_id'], '') !== undefined
    );
    return courses.size > 0 && filterAssignedCoordinator.size > 0;
  };

  render() {
    const { programName, filter, modalData, programId } = this.state;
    const { activeInstitutionCalendar, isCurrentCalendar } = this.props;
    const currentCalendar = isCurrentCalendar();
    return (
      <div className="main pb-5 bg-gray">
        <div className="bg-white pt-3 pb-3">
          <div className="container-fluid">
            <div className="d-flex justify-content-between align-items-center">
              <div className="font-weight-bold mb-0 f-17">
                <i
                  className="fa fa-arrow-left mr-3 cursor-pointer"
                  aria-hidden="true"
                  onClick={this.handleBackClick}
                ></i>
                {programName || ''}
              </div>
              {currentCalendar &&
                CheckPermission(
                  'tabs',
                  'Schedule Management',
                  'Assign Course Coordinator',
                  '',
                  'Course List',
                  'Publish'
                ) && (
                  <div>
                    <ThemeProvider theme={MUI_THEME}>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<i className="fa fa-globe" aria-hidden="true"></i>}
                        className="text-uppercase"
                        onClick={() => this.publish(false)}
                        disabled={!this.disabledPublish()}
                      >
                        {t('publish')}
                      </Button>
                    </ThemeProvider>
                  </div>
                )}
            </div>
          </div>
        </div>

        <div className="p-4 bg-gray">
          <div className="container">
            <div className="bg-white border-radious-8">
              <Filters
                filter={filter}
                terms={this.getTerms()}
                courses={this.props.courseCoordinators.get('courses', List())}
                handleChange={this.handleFilterChange}
                sortDirection={this.state.sortDirection}
                handleSortDirectionChange={this.handleSortDirectionChange}
                programId={programId}
              />
              <CourseTable
                courses={this.getCourses()}
                institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
                setMessage={this.props.resetMessage}
                assignCoordinator={this.props.assignCourseCoordinator}
                currentCalendar={currentCalendar}
                programId={programId}
              />
            </div>
          </div>
        </div>
        <AlertConfirmModal
          show={modalData.show}
          title={modalData.title || ''}
          titleIcon={modalData.titleIcon}
          body={modalData.body || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />
      </div>
    );
  }
}

CourseCoordinators.propTypes = {
  history: PropTypes.object,
  setData: PropTypes.func,
  resetMessage: PropTypes.func,
  getInstitutionCalendar: PropTypes.func,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getCourseCoordinators: PropTypes.func,
  courseCoordinators: PropTypes.instanceOf(Map),
  assignCourseCoordinator: PropTypes.func,
  publishCourseCoordinator: PropTypes.func,
  isCurrentCalendar: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    courseCoordinators: selectCourseCoordinators(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions)
)(withCalendarHooks(CourseCoordinators));
