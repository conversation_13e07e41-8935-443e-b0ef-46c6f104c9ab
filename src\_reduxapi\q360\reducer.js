import * as actions from './actions';
import { fromJS, Map, List } from 'immutable';
// import { formAddendum, settingTag } from './mock';
// import { formAddendum, singleFormListInstance } from './mock';

const initialState = fromJS({
  isLoading: false,
  isProgramDetailsLoading: false,
  message: '',
  getStepTwoDetails: {},
  signedUrl: Map(),
  categoryForms: {},
  configureTemplate: [],
  updateCategory: [],
  qaPcSetting: {},
  formOccurrences: {},
  //assigned course
  qapcPrograms: {},
  programCount: {},
  qapcProgramDetails: {},
  institutionList: [],
  formConfigureList: {},
  singleFormList: {},
  formAddendum: {},
  formSettingData: {},
  academicYears: [],
  formAssuranceList: {},
  singleForm: {},
  settingTags: {},
  //INCORPORATE_DATA{
  formApiCallStatus: false,
  inCorporateSectionData: [],
  inCorporateFilters: {
    categoryForm: [],
    termAndAttemptType: [],
    programList: [],
  },
  //INCORPORATE_DATA}
  rpActions: {},
  qapcRoles: [],
  apiCallDoneForIncorporate: false,
  referenceDocumentDetails: Map(),
  searchProgramDetails: Map(),
  referenceFromAttachments: Map(),
  incorporateFromWithData: Map(),
  todoMissedData: Map(),
  categoryList: List(),
  evidenceDocument: Map(), //schema looks like
  //evidenceDocument schema: {
  //   formInitiatorId:Map({
  //     formEvidenceAttachment:List(), //implies attachment for Accordion
  //     sectionAttachments:List() //implies attachment for section wise
  //   })
  // }
  approvalCategoryList: [],
  fromApplicationList: {},
  approverPending: {},
  levelRoleUserList: {},
  dashboardSettings: {},
  dashboardGraphData: {},
  incorporateTooltipData: {},
  isDashboardLoading: false,
});
export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.UPLOAD_ATTACHMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPLOAD_ATTACHMENT_SUCCESS: {
      return state.set('isLoading', false).set('uploadFileUrl', fromJS(action.data));
    }
    case actions.UPLOAD_ATTACHMENT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_APPROVAL_CATEGORY_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_APPROVAL_CATEGORY_LIST_SUCCESS: {
      return state.set('isLoading', false).set('approvalCategoryList', fromJS(action.data));
    }
    case actions.GET_APPROVAL_CATEGORY_LIST_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_APPROVER_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_APPROVER_LIST_SUCCESS: {
      let constructData = Map();
      fromJS(action.data)
        .entrySeq()
        .forEach(([key, pending]) => {
          pending.forEach((item) => {
            const formInitiatorId = item.get('formInitiatorId', '');
            constructData = constructData.setIn([key, formInitiatorId], item);
          });
        });
      return state
        .set('isLoading', false)
        .setIn(['approverPending', action.categoryId], constructData);
    }
    case actions.GET_APPROVER_LIST_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_FROM_APPLICATION_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FROM_APPLICATION_LIST_SUCCESS: {
      let constructData = Map();
      fromJS(action.data?.filterMatchingFormData).forEach((value) => {
        const _id = value.get('_id', '');
        constructData = constructData.set(`${action.categoryId}+${_id}`, value);
      });
      return state.set('isLoading', false).set('fromApplicationList', constructData);
    }
    case actions.GET_FROM_APPLICATION_LIST_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_STEP_TWO_DETAILS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_STEP_TWO_DETAILS_SUCCESS: {
      let updatedState = state;
      if (action.setDataIntoReducer) {
        updatedState = updatedState
          .setIn(['formAddendum', 'formName'], action.data.get('formName', ''))
          .setIn(['formAddendum', 'formType'], action.data.get('formType', ''))
          .setIn(['formAddendum', 'attachments'], action.data.get('attachments', ''))
          .setIn(
            ['formAddendum', 'sectionAttachments'],
            action.data.get('sectionAttachments', List())
          );
      }
      return updatedState.set('isLoading', false).set('getStepTwoDetails', action.data);
    }
    case actions.GET_STEP_TWO_DETAILS_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_SIGNED_URL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SIGNED_URL_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_SIGNED_URL_FAILURE: {
      // const { response } = action.error;
      return state.set('isLoading', false).set('error', action.error);
      // .set('message', response.data.message);
    }
    case actions.GENERATE_URL_REQUEST: {
      return state.set('isLoading', true).set('signedUrl', null);
    }
    case actions.GENERATE_URL_SUCCESS: {
      return state.set('isLoading', false).set('signedUrl', action.data.signedUrl);
    }
    case actions.GENERATE_URL_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_CATEGORY_FORM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_CATEGORY_FORM_SUCCESS: {
      const { data, params } = action.data;
      const param = new URLSearchParams(params);
      const pageNo = param.get('pageNo');
      const limit = param.get('limit');
      const searchKey = param.get('searchKey') || '';
      const categoryId = param.get('categoryId');
      const concatPagination = `${pageNo}+${limit}`;
      const categoryForms = fromJS(data.data.formSettingData || data.data.formData);
      const total = data.data.totalCount;
      return state.set('isLoading', false).update('categoryForms', Map(), (forms) => {
        return forms
          .setIn([categoryId, 'forms', searchKey, concatPagination], categoryForms)
          .setIn([categoryId, searchKey, 'totalCount'], total);
      });
    }
    case actions.GET_CATEGORY_FORM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_VARIETY_OF_FORMS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_VARIETY_OF_FORMS_SUCCESS: {
      const { data, params } = action.data;
      const param = new URLSearchParams(params);
      const pageNo = param.get('pageNo');
      const limit = param.get('limit');
      const searchKey = param.get('searchKey') || '';
      const formType = param.get('formType');
      const concatPagination = `${pageNo}+${limit}`;
      const categoryForms = fromJS(data.data.categoryFormData);
      const total = data.data.totalCount;
      return state.set('isLoading', false).update('categoryForms', Map(), (forms) => {
        return forms
          .setIn([formType, 'forms', searchKey, concatPagination], categoryForms)
          .setIn([formType, searchKey, 'totalCount'], total);
      });
    }
    case actions.GET_VARIETY_OF_FORMS_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.UPDATE_CATEGORY_FORM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_CATEGORY_FORM_SUCCESS: {
      return state.set('isLoading', false).set('updateCategory', fromJS(action.data));
    }
    case actions.UPDATE_CATEGORY_FORM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.QA_PC_SAVE_SETTING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.QA_PC_SAVE_SETTING_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.QA_PC_SAVE_SETTING_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_INSTITUTION_CALENDAR_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_INSTITUTION_CALENDAR_LIST_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_INSTITUTION_CALENDAR_LIST_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.GET_QA_PC_SETTING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_QA_PC_SETTING_SUCCESS: {
      return state.set('isLoading', false).set('qaPcSetting', fromJS(action.data));
    }
    case actions.GET_QA_PC_SETTING_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.POST_INSTITUTION_CALENDAR_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_INSTITUTION_CALENDAR_LIST_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', 'The calendar has been successfully updated.');
    }
    case actions.POST_INSTITUTION_CALENDAR_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.UPDATE_CATEGORY_CONFIGURE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_CATEGORY_CONFIGURE_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.UPDATE_CATEGORY_CONFIGURE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occured');
    }
    case actions.GET_ATTEMPT_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_ATTEMPT_TYPE_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_ATTEMPT_TYPE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occured');
    }
    case actions.GET_SINGLE_CONFIG_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SINGLE_CONFIG_SUCCESS: {
      const data = fromJS(action.data);
      const categoryPosition = action.index;
      return state.set('isLoading', false).setIn(['configureTemplate', categoryPosition], data);
    }
    case actions.GET_SINGLE_CONFIG_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occured');
    }
    case actions.UPDATE_DUPLICATE_FORM_REQUEST_V2: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_DUPLICATE_FORM_SUCCESS_V2: {
      return state.set('isLoading', false).set('message', 'Updated SuccessFully');
    }
    case actions.UPDATE_DUPLICATE_FORM_FAILURE_V2: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response.data.message);
    }
    case actions.CREATE_DUPLICATE_FORM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.CREATE_DUPLICATE_FORM_SUCCESS: {
      const { data, params } = action.data;
      const param = new URLSearchParams(params);
      const pageNo = param.get('pageNo');
      const limit = param.get('limit');
      const searchKey = param.get('searchKey') || '';
      const categoryId = param.get('categoryId');
      const concatPagination = `${pageNo}+${limit}`;
      return state
        .set('isLoading', false)
        .set('message', (action.method === 'create' ? 'Created' : 'Updated') + ' SuccessFully')
        .updateIn(
          ['categoryForms', categoryId, 'forms', searchKey, concatPagination],
          List(),
          (forms) => forms.unshift(fromJS(data))
        )
        .updateIn(['categoryForms', categoryId, searchKey, 'totalCount'], 0, (count) => count++);
    }
    case actions.CREATE_DUPLICATE_FORM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : 'Error Occured');
    }
    case actions.GET_CONFIGURE_TEMPLATE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_CONFIGURE_TEMPLATE_SUCCESS: {
      return state.set('isLoading', false).set('configureTemplate', fromJS(action.data));
    }
    case actions.GET_CONFIGURE_TEMPLATE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occured');
    }
    case actions.GET_SINGLE_FORM_OCCURRENCE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SINGLE_FORM_OCCURRENCE_SUCCESS: {
      return state
        .set('isLoading', false)
        .setIn(['formOccurrences', action.formId], fromJS(action.data));
    }
    case actions.GET_SINGLE_FORM_OCCURRENCE_FAILURE: {
      return state.set('isLoading', true);
    }
    case actions.QA_PC_PROGRAM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.QA_PC_PROGRAM_LIST_SUCCESS: {
      return state
        .set('isLoading', false)
        .update('qapcPrograms', Map(), (allProgram) => {
          const updatedProgram = allProgram.setIn(
            [action.query, action.currentPaginationCount],
            fromJS(action.data.programData ?? List())
          );
          return updatedProgram;
        })
        .update('programCount', Map(), (programCount) => {
          return programCount.setIn(
            [action.query, action.currentPaginationCount],
            action.data.totalCount ?? 0
          );
        });
    }
    case actions.QA_PC_PROGRAM_LIST_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : 'an_error_occured_try_again');
    }
    case actions.QA_PC_PROGRAM_DETAILS_REQUEST_V1: {
      return state.set('isProgramDetailsLoading', true).set('isLoading', false);
    }
    case actions.QA_PC_PROGRAM_DETAILS_SUCCESS_V1: {
      return state
        .set('isProgramDetailsLoading', false)
        .update('qapcProgramDetails', (programDetails) => programDetails.merge(action.data));
    }
    case actions.QA_PC_PROGRAM_DETAILS_FAILURE_V1: {
      const { response } = action.error;
      return state
        .set('isProgramDetailsLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : 'an_error_occured_try_again');
    }
    case actions.QA_PC_CURRICULUM_DETAILS_REQUEST_V1: {
      return state.set('isProgramDetailsLoading', true);
    }
    case actions.QA_PC_CURRICULUM_DETAILS_SUCCESS_V1: {
      return state
        .set('isProgramDetailsLoading', false)
        .update('qapcProgramDetails', (programDetails) => programDetails.merge(action.data));
    }
    case actions.QA_PC_CURRICULUM_DETAILS_FAILURE_V1: {
      const { response } = action.error;
      return state
        .set('isProgramDetailsLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : 'an_error_occured_try_again');
    }
    case actions.GET_INSTITUTION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_INSTITUTION_SUCCESS: {
      let institutionMap = Map();
      for (const eachInstitution of fromJS(action.data)) {
        institutionMap = institutionMap.set(
          eachInstitution.get('_id', ''),
          Map({
            institutionName: eachInstitution.get('name', ''),
            assignedInstitutionId: eachInstitution.get('_id', ''),
          })
        );
      }
      return state.set('isLoading', false).set('institutionList', institutionMap);
    }
    case actions.GET_INSTITUTION_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.PUT_RESET_FORM_COURSE_SETTING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.PUT_RESET_FORM_COURSE_SETTING_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Reset Category Setting');
    }
    case actions.PUT_RESET_FORM_COURSE_SETTING_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.UPDATE_CATEGORY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_CATEGORY_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Category Create SuccessFully');
    }
    case actions.UPDATE_CATEGORY_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_CONCLUDING_PHASE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_CONCLUDING_PHASE_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_CONCLUDING_PHASE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_APPROVAL_HIERARCHY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_APPROVAL_HIERARCHY_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_APPROVAL_HIERARCHY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.MULTIPLE_FILE_UPLOAD_URL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.MULTIPLE_FILE_UPLOAD_URL_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.MULTIPLE_FILE_UPLOAD_URL_FAILURE: {
      return state.set('isLoading', false);
    }

    case actions.GET_TERM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_TERM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('termList', fromJS(action.data));
    }
    case actions.GET_TERM_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.POST_CONFIGURE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_CONFIGURE_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Saved Successfully');
    }
    case actions.POST_CONFIGURE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_FORM_CONFIGURATION_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FORM_CONFIGURATION_LIST_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_FORM_CONFIGURATION_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.UPDATE_CONFIGURE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_CONFIGURE_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Saved Successfully');
    }
    case actions.UPDATE_CONFIGURE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.UPDATE_FORM_CONFIGURE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_FORM_CONFIGURE_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Saved Successfully');
    }
    case actions.UPDATE_FORM_CONFIGURE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_SINGLE_FORM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SINGLE_FORM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('singleFormList', fromJS(action.data));
    }
    case actions.GET_SINGLE_FORM_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_FORM_ADDENDUM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FORM_ADDENDUM_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('formApiCallStatus', true)
        .set('formAddendum', fromJS(action.data));
    }
    case actions.GET_FORM_ADDENDUM_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_FORM_CONFIGURE_LIST_SUCCESS: {
      return state.setIn(['formConfigureList', action.id], fromJS(action.data));
    }
    case actions.GET_FORM_SETTING_LIST_REQUEST: {
      return state.set('isLoading', true).set('formSettingData', fromJS({}));
    }
    case actions.GET_FORM_SETTING_LIST_SUCCESS: {
      return state.set('isLoading', false).set('formSettingData', fromJS(action.data));
    }
    case actions.GET_FORM_SETTING_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_ACADEMIC_YEARS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_ACADEMIC_YEARS_SUCCESS: {
      return state.set('isLoading', false).set('academicYears', fromJS(action.data));
    }
    case actions.GET_ACADEMIC_YEARS_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_FORM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_FORM_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Saved Successfully');
    }
    case actions.UPDATE_FORM_FAILURE: {
      return state.set('isLoading', false);
    }

    case actions.GET_FORM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FORM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('formAssuranceList', fromJS(action.data));
    }
    case actions.GET_FORM_LIST_FAILURE: {
      return state.set('isLoading', false).set('formAssuranceList', fromJS({}));
    }

    case actions.UPDATE_FORM_STATUS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_FORM_STATUS_SUCCESS: {
      const type = {
        delete: 'Deleted',
        archive: 'Archived',
        unArchive: 'Revoked',
        draft: 'Drafted',
        submitted: 'Submitted',
      };
      const msg = type[action.message];
      return state.set('isLoading', false).set('message', `${msg} Successfully`);
    }
    case actions.UPDATE_FORM_STATUS_FAILURE: {
      return state.set('isLoading', false);
    }

    case actions.GET_SINGLE_FORM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SINGLE_FORM_SUCCESS: {
      return state.set('isLoading', false).set('singleForm', fromJS(action.data));
    }
    case actions.GET_SINGLE_FORM_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_SETTING_TAGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SETTING_TAGS_SUCCESS: {
      return state.set('isLoading', false).set('settingTags', fromJS(action.data));
    }
    case actions.GET_SETTING_TAGS_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_INCORPORATE_FILTER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_INCORPORATE_FILTER_SUCCESS: {
      return state
        .set('isLoading', false)
        .setIn(['inCorporateFilters', action.storeKey], fromJS(action.data));
    }
    case actions.GET_INCORPORATE_FILTER_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_INCORPORATE_SECTION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_INCORPORATE_SECTION_SUCCESS: {
      return state
        .set('isLoading', false)
        .set(
          'inCorporateSectionData',
          fromJS(action.data).map((section) =>
            section.set('sectionId', section.get('sectionName', ''))
          )
        )
        .set('apiCallDoneForIncorporate', true);
    }
    case actions.GET_INCORPORATE_SECTION_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.SET_INCORPORATE_SECTION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SET_INCORPORATE_SECTION_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.SET_INCORPORATE_SECTION_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_RP_ACTION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_RP_ACTION_SUCCESS: {
      return state.set('isLoading', false).set('rpActions', fromJS(action.data));
    }
    case actions.GET_RP_ACTION_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_QAPC_ROLES_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_QAPC_ROLES_SUCCESS: {
      return state.set('isLoading', false).set('qapcRoles', fromJS(action.data.qapcRoleIds));
    }
    case actions.GET_QAPC_ROLES_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_REFERENCE_FORM_ATTACHMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_REFERENCE_FORM_ATTACHMENT_SUCCESS: {
      return state
        .set('isLoading', false)
        .setIn(['referenceFromAttachments', action.id], fromJS(action.data ?? []));
    }
    case actions.GET_REFERENCE_FORM_ATTACHMENT_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_REF_ACADEMIC_YEAR_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_REF_ACADEMIC_YEAR_SUCCESS: {
      return state.set('referenceDocumentDetails', fromJS(action.data)).set('isLoading', false);
    }
    case actions.GET_REF_ACADEMIC_YEAR_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_INCORPORATE_FROM_WITH_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_INCORPORATE_FROM_WITH_SUCCESS: {
      return state.set('incorporateFromWithData', fromJS(action.data)).set('isLoading', false);
    }
    case actions.GET_INCORPORATE_FROM_WITH_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.SEARCH_REFERENCE_DOCUMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SEARCH_REFERENCE_DOCUMENT_SUCCESS: {
      return state.set('isLoading', false).set('searchProgramDetails', fromJS(action.data));
    }
    case actions.SEARCH_REFERENCE_DOCUMENT_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_TODO_MISSED_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_TODO_MISSED_SUCCESS: {
      return state.set('isLoading', false).set('todoMissedData', fromJS(action.data));
    }
    case actions.GET_TODO_MISSED_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_CATEGORY_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_CATEGORY_LIST_SUCCESS: {
      return state.set('isLoading', false).set('categoryList', fromJS(action.data));
    }
    case actions.GET_CATEGORY_LIST_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_EVIDENCE_DOCUMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_EVIDENCE_DOCUMENT_SUCCESS: {
      return state
        .set('isLoading', false)
        .setIn(['evidenceDocument', action.formInitiatorId], fromJS(action.data));
    }
    case actions.GET_EVIDENCE_DOCUMENT_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_LEVEL_ROLE_USER_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_LEVEL_ROLE_USER_LIST_SUCCESS: {
      return state
        .set('isLoading', false)
        .setIn(['levelRoleUserList', action.formInitiatorId], fromJS(action.data));
    }
    case actions.GET_LEVEL_ROLE_USER_LIST_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_INCORPORATE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_INCORPORATE_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_INCORPORATE_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.QA_PC_DASHBOARD_CREATE_CATEGORY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.QA_PC_DASHBOARD_CREATE_CATEGORY_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Saved Successfully');
    }
    case actions.QA_PC_DASHBOARD_CREATE_CATEGORY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.QAPC_GET_DASHBOARD_SETTINGS_REQUEST: {
      return state.set('isLoading', true).set('dashboardSettings', fromJS({}));
    }
    case actions.QAPC_GET_DASHBOARD_SETTINGS_SUCCESS: {
      const { data } = action;
      return state.set('isLoading', false).set('dashboardSettings', fromJS(data));
    }
    case actions.QAPC_GET_DASHBOARD_SETTINGS_FAILURE: {
      // const { response } = action.error;
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.QA_PC_DASHBOARD_UPDATE_CATEGORY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.QA_PC_DASHBOARD_UPDATE_CATEGORY_SUCCESS: {
      return state.set('isLoading', false).set('message', action.message);
    }
    case actions.QA_PC_DASHBOARD_UPDATE_CATEGORY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.QAPC_GET_DASHBOARD_REQUEST: {
      return state
        .set('isDashboardLoading', true)
        .set('dashboardGraphData', fromJS({}))
        .set('formAssuranceList', fromJS({}));
    }
    case actions.QAPC_GET_DASHBOARD_SUCCESS: {
      const { data } = action;
      return state.set('isDashboardLoading', false).set('dashboardGraphData', fromJS(data));
    }
    case actions.QAPC_GET_DASHBOARD_FAILURE: {
      // const { response } = action.error;
      return state.set('isDashboardLoading', false).set('error', action.error);
    }
    case actions.POST_INCORPORATE_SECTION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_INCORPORATE_SECTION_SUCCESS: {
      const { data, payload } = action.data;
      const { levelName, formId, title } = payload;
      const key = `${title}-${levelName}-${formId}`;
      return state.set('isLoading', false).setIn(['incorporateTooltipData', key], fromJS(data));
    }
    case actions.POST_INCORPORATE_SECTION_FAILURE: {
      // const { response } = action.error;
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_FORM_INITIATOR_USER_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FORM_INITIATOR_USER_LIST_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_FORM_INITIATOR_USER_LIST_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.PUT_SEND_EMAIL_TO_FORM_INITIATOR_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.PUT_SEND_EMAIL_TO_FORM_INITIATOR_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.PUT_SEND_EMAIL_TO_FORM_INITIATOR_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.ADD_COMMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_COMMENT_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.ADD_COMMENT_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_SEARCH_DOCUMENT_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SEARCH_DOCUMENT_LIST_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_SEARCH_DOCUMENT_LIST_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.PUT_CREATE_DUPLICATE_FORM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.PUT_CREATE_DUPLICATE_FORM_SUCCESS: {
      return state.set('isLoading', false).set('message', action?.data);
    }
    case actions.PUT_CREATE_DUPLICATE_FORM_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.PUT_UNPUBLISHED_FORM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.PUT_UNPUBLISHED_FORM_SUCCESS: {
      return state.set('isLoading', false).set('message', action?.data);
    }
    case actions.PUT_UNPUBLISHED_FORM_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_DISCUSSION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_DISCUSSION_SUCCESS: {
      return state.set('isLoading', false).set('discussionLists', fromJS(action?.data));
    }
    case actions.GET_DISCUSSION_FAILURE: {
      return state.set('isLoading', false);
    }

    case actions.CREATE_DISCUSSION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.CREATE_DISCUSSION_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.CREATE_DISCUSSION_FAILURE: {
      return state.set('isLoading', false);
    }

    case actions.GET_DISCUSSION_COUNT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_DISCUSSION_COUNT_SUCCESS: {
      return state.set('isLoading', false).set('discussionUnreadDetails', fromJS(action?.data));
    }
    case actions.GET_DISCUSSION_COUNT_FAILURE: {
      return state.set('isLoading', false);
    }

    default:
      return state;
  }
}
