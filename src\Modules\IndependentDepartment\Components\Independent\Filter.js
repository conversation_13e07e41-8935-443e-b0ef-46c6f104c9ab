import React from 'react';
import PropTypes from 'prop-types';
import {
  Menu,
  MenuItem,
  Typography,
  FormControlLabel,
  Radio,
  FormControl,
  RadioGroup,
} from '@mui/material';
import ListItemText from '@mui/material/ListItemText';
import Checkbox from '@mui/material/Checkbox';
import { ThemeProvider } from '@mui/styles';
import { CHECKBOX_THEME } from 'Modules/ProgramInput/components/Course/utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';

function Filter({
  anchorEl,
  options,
  selectedValues,
  allFunc,
  header,
  selectedFilter,
  sharedRadio,
  showShared,
  departmentTabValue,
}) {
  const { onSelect, handleClose, onRadioChange } = allFunc;
  const ITEM_HEIGHT = 48;
  const open = Boolean(anchorEl);
  return (
    <Menu
      id="long-menu"
      anchorEl={anchorEl}
      keepMounted
      open={open}
      onClose={handleClose}
      PaperProps={{
        style: {
          maxHeight: ITEM_HEIGHT * 4.5,
          width: showShared ? '40ch' : '20ch',
        },
      }}
    >
      <Typography component="div" className="dropdown-title">
        {header}
      </Typography>
      {showShared && departmentTabValue === 'academic' && (
        <FormControl component="fieldset" className="ml-3">
          <RadioGroup
            row
            aria-label="position"
            name="position"
            value={sharedRadio}
            onChange={(e) => onRadioChange(e.target.value)}
          >
            <FormControlLabel
              value="sharedWith"
              control={<Radio color="primary" />}
              label={'Shared With'}
            />
            <FormControlLabel
              value="sharedFrom"
              control={<Radio color="primary" />}
              label={'Shared From'}
            />
          </RadioGroup>
        </FormControl>
      )}
      {showShared && departmentTabValue === 'academic' && <hr className="mb-0 mt-0" />}
      {options.map((option) => (
        <MenuItem
          className="white-space-normal"
          key={option.value}
          value={option.value}
          onClick={() => onSelect(option.value, selectedFilter)}
        >
          <ThemeProvider theme={CHECKBOX_THEME}>
            <Checkbox checked={Boolean(selectedValues.includes(option.value))} color="primary" />
          </ThemeProvider>
          <ListItemText primary={getShortString(option.name, 15)} />
        </MenuItem>
      ))}
    </Menu>
  );
}

Filter.propTypes = {
  anchorEl: PropTypes.object,
  options: PropTypes.array,
  selectedValues: PropTypes.array,
  header: PropTypes.string,
  allFunc: PropTypes.object,
  selectedFilter: PropTypes.string,
  sharedRadio: PropTypes.string,
  showShared: PropTypes.bool,
  departmentTabValue: PropTypes.string,
};
export default Filter;
