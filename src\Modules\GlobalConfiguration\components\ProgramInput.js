import React, { useState, useEffect, Suspense } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory, useParams } from 'react-router-dom';
import { Accordion, Card, Dropdown } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import Checkbox from '@mui/material/Checkbox';
import { selectProgramSettings } from '_reduxapi/program_input/v2/selectors';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectProgramInput, selectBasicDetails } from '_reduxapi/global_configuration/selectors';
import Module1 from 'Assets/img/globalConfiguration/module1.svg';
import Module2 from 'Assets/img/globalConfiguration/module2.svg';
import Standard from 'Assets/img/globalConfiguration/standardCreditSystem.svg';
import Dynamic from 'Assets/img/globalConfiguration/dynamicCreditSystem.svg';
import { getInstitutionHeader } from 'v2/utils';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';
import { dString } from 'utils';
import i18n from '../../../i18n';
import { getLabelName } from 'Modules/Shared/v2/Configurations';

const RenameModal = React.lazy(() => import('../modal/RenameModal'));
const AddEditProgramTypeModal = React.lazy(() => import('../modal/AddEditProgramTypeModal'));
const DeleteModal = React.lazy(() => import('../modal/DeleteModal'));

function ProgramInput({
  getProgramInput,
  programInput,
  updateLabel,
  updateProgramType,
  updateProgramInputMode,
  updateProgramDuration,
  setData,
  programSettings,
  basicDetails,
  updateProgramBasicDetails,
}) {
  const history = useHistory();
  const params = useParams();
  const getProgramID = dString(params.programID ? params.programID : params.id);
  const [arrow, setArrow] = useState('');
  const [show, setShow] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState('');
  const [selectedProgramType, setSelectedProgramType] = useState(Map());
  const [type, setType] = useState('');
  const [radioValue, setRadioValue] = useState(
    Map({
      curriculumNaming: 1,
      creditHours: '',
      departments: '',
      programDurationFormat: '',
      checkBox: false,
    })
  );
  const pgmInput = params.programID
    ? programSettings.getIn(['programSettings', 'settings', 'programInput'], Map())
    : programInput;
  const institutionHeader = getInstitutionHeader(history);

  useEffect(() => {
    !params.programID && getProgramInput({ header: institutionHeader });
  }, []); // eslint-disable-line

  useEffect(() => {
    if (
      pgmInput.get('curriculumNaming', List()).size > 0 ||
      pgmInput.get('creditHours', List()).size > 0
    ) {
      const selectedCurriculum = pgmInput
        ?.get('curriculumNaming', List())
        .find((item) => item.get('isDefault', false));
      const selectedCreditHours = pgmInput
        ?.get('creditHours', List())
        .find((item) => item.get('isDefault', false));
      const selectedProgramDurationFormat = pgmInput
        ?.get('programDurationFormat', List())
        .find((item) => item.get('isDefault', false));

      const checkBoxStatus = pgmInput.getIn(['programDurationFormat', 1, 'withoutLevel'], false);
      const updatedValue = radioValue.update((key) => {
        return key
          .set('curriculumNaming', selectedCurriculum?.get('mode', 3))
          .set('creditHours', selectedCreditHours?.get('mode', ''))
          .set('programDurationFormat', selectedProgramDurationFormat?.get('defaultInput', ''))
          .set('checkBox', checkBoxStatus);
      });
      setRadioValue(updatedValue);
    }
  }, [pgmInput]); // eslint-disable-line

  const handleReset = (e) => {
    e.stopPropagation();
    setArrow({ 1: false });
    updateLabel({ id: programInput.get('_id'), type: 'reset', header: institutionHeader });
  };

  const displayRow = (key, heading, type) => {
    return (
      <>
        <div className="row">
          <div className="col-md-12">
            <Accordion.Toggle
              as={Card.Header}
              eventKey={key}
              className="icon_remove_leave pl-0"
              onClick={() => {
                setArrow({ [key]: !arrow[key] });
              }}
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex">
                  <span>
                    <i
                      className={`fa fa-chevron-${arrow[key] ? 'up' : 'down'} f-14 icon-blue`}
                      aria-hidden="true"
                    ></i>
                  </span>

                  <div className="pl-3">
                    <div className="f-16 digi-brown remove_hover">
                      <b>
                        <Trans i18nKey={heading}></Trans>
                      </b>
                      {!arrow[key] && (
                        <div className="text-grey f-15">
                          <span>
                            {type === 'label_configuration' ? (
                              <>
                                {
                                  programInput.getIn(['labelConfiguration', 0, 'labels'], List())
                                    .size
                                }{' '}
                                <Trans i18nKey={'global_configuration.labels'}></Trans>
                              </>
                            ) : type === 'program_type' ? (
                              <>
                                {programInput.get('programType', List()).size}{' '}
                                <Trans i18nKey={'global_configuration.program_type'}></Trans>
                              </>
                            ) : type === 'curriculum_naming' ? (
                              <>
                                {[1, 2].includes(radioValue.get('curriculumNaming', ''))
                                  ? `${t('global_configuration.mode')} ${radioValue.get(
                                      'curriculumNaming',
                                      ''
                                    )}`
                                  : t('global_configuration.manual_naming')}
                              </>
                            ) : type === 'credit_hours' ? (
                              <>
                                {radioValue.get('creditHours', '') === 'standard'
                                  ? t('global_configuration.standard')
                                  : t('global_configuration.dynamic')}
                              </>
                            ) : type === 'program_Duration_Format' ? (
                              <>
                                {radioValue.get('programDurationFormat', '') === 'year'
                                  ? t('global_configuration.year_format')
                                  : t('global_configuration.Phase_format', {
                                      Phase: getLabelName({ label: 'phase' })
                                        ? getLabelName({
                                            label: 'phase',
                                            isToolTip: false,
                                            length: 15,
                                          })
                                        : 'Phase',
                                    })}
                              </>
                            ) : (
                              <>
                                {[1, 2].includes(radioValue.get('departments', ''))
                                  ? `Mode ${radioValue.get('departments', '')}`
                                  : 'Both'}
                              </>
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {key === '1' && arrow['1'] && (
                  <div className="text-center">
                    <small className="f-18" onClick={(e) => e.stopPropagation()}>
                      <Dropdown>
                        <Dropdown.Toggle
                          variant=""
                          id="dropdown-table"
                          className="table-dropdown"
                          size="sm"
                        >
                          <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                          <Dropdown.Item href="#" onClick={(e) => handleReset(e)}>
                            <Trans i18nKey={'global_configuration.reset'}></Trans>
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      </Dropdown>{' '}
                    </small>
                  </div>
                )}
                {key === '2' && arrow['2'] && programInput.get('programType', List()).size > 0 && (
                  <div
                    className="float-right mb-2 text-blue ml-auto icon-blue"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpen('programType');
                    }}
                  >
                    + <Trans i18nKey={'global_configuration.add_new_type'}></Trans>
                  </div>
                )}
              </div>
            </Accordion.Toggle>
          </div>
        </div>
      </>
    );
  };

  const labelConfig = (label, selectedLabel, key) => {
    return (
      <>
        <div className="mb-2 d-flex justify-content-between">
          <div className="bold mb-2">{label}</div>
          <div
            className="float-right icon-blue"
            onClick={() => {
              handleOpen('labelConfig');
              setSelectedLabel(selectedLabel);
            }}
          >
            <Trans i18nKey={'global_configuration.rename'}></Trans>
          </div>
        </div>
        {[1, 3, 6, 8].includes(key) && <hr />}
      </>
    );
  };

  const handleOpen = (name, type, item) => {
    let showModal = {
      ...show,
      [name]: !show[name],
    };
    setShow(showModal);
    setType(type);
    setSelectedProgramType(item);
  };

  const handleCheckbox = (id) => {
    const status = radioValue.get('checkBox', false);
    let updatedValue = radioValue.set('checkBox', !status);
    setRadioValue(updatedValue);
    const requestData = { programId: getProgramID, withoutLevel: !status };
    updateProgramDuration({
      id,
      requestData,
      header: institutionHeader,
    });
  };

  const handleRadioButton = (key, value, id) => {
    let updatedValue = radioValue.set(key, value);
    if (key === 'programDurationFormat') {
      updatedValue = updatedValue.set('checkBox', false);
      const requestData = { programId: getProgramID, withoutLevel: false };
      updateProgramDuration({
        id,
        requestData,
        headers: institutionHeader,
      });
    } else if (key !== 'departments') {
      !params.programID
        ? updateProgramInputMode({
            id,
            requestData: {
              settingId: programInput.get('_id'),
            },
            type: key,
            header: institutionHeader,
          })
        : updateProgramBasicDetails({
            id,
            programId: getProgramID,
            type: key,
            urlInputMode: `/program-input/update-${
              key === 'curriculumNaming' ? 'curriculum' : 'credithours'
            }/${id}`,
            header: institutionHeader,
          });
    }
    setRadioValue(updatedValue);
  };

  function getDefaultLanguage() {
    return (
      programInput &&
      programInput
        ?.get('labelConfiguration', List())
        ?.find((label) => label.get('language', 'en') === 'en')
    );
  }

  const getLanguage = getDefaultLanguage();

  return (
    <React.Fragment>
      <div>
        <div>
          <div className="pt-3 pl-3">
            <Accordion defaultActiveKey="" className="mt-3">
              {/* Label configuration */}
              {!params.programID &&
                displayRow('1', 'global_configuration.label_configuration', 'label_configuration')}
              {!params.programID && (
                <Accordion.Collapse eventKey="1" className="bg-white">
                  <Card.Body className="innerbodyLeave border-top-remove">
                    <div className="container">
                      <div className="row">
                        <div className="float-right mb-2 text-blue ml-auto"></div>
                      </div>
                      {getLanguage &&
                        getLanguage.size > 0 &&
                        getLanguage
                          .get('labels', List())
                          .map((data, key) => (
                            <>
                              {labelConfig(
                                !data.get('translatedInput', '')
                                  ? data.get('name', '')
                                  : data.get('translatedInput', ''),
                                data.get('name', ''),
                                key
                              )}
                            </>
                          ))}
                    </div>
                  </Card.Body>
                </Accordion.Collapse>
              )}
              {show['labelConfig'] && (
                <Suspense fallback="">
                  <RenameModal
                    show={show}
                    handleClose={() => handleOpen('labelConfig')}
                    labelName={selectedLabel}
                    labelConfiguration={programInput}
                    updateLabel={updateLabel}
                    selectedLanguage={basicDetails.get('language', [])}
                    institutionHeader={institutionHeader}
                  />
                </Suspense>
              )}
              {!params.programID && <hr />}

              {/* Program Type */}
              {!params.programID &&
                displayRow('2', 'global_configuration.program_type', 'program_type')}
              {!params.programID && (
                <Accordion.Collapse eventKey="2" className="bg-white">
                  <Card.Body className=" innerbodyLeave border-top-remove">
                    <div className="container">
                      {programInput.get('programType', List()).size > 0 ? (
                        <>
                          {programInput.get('programType', List()) &&
                            programInput.get('programType', List()).map((item, key) => (
                              <div key={key} className="mb-2 d-flex justify-content-between">
                                <div>
                                  <div className="bold mb-2">
                                    {item.get('name', '')} - {item.get('code', '')}
                                  </div>
                                  <div className="mb-2"></div>
                                </div>
                                <div className="float-right digi-pl-8 digi-pr-8">
                                  <img
                                    src={EditIcon}
                                    alt="edit"
                                    className="digi-pr-12 remove_hover"
                                    onClick={() => handleOpen('programType', 'update', item)}
                                  />

                                  <img
                                    src={DeleteIcon}
                                    alt="Delete"
                                    className="digi-pr-12 remove_hover"
                                    onClick={() =>
                                      handleOpen('programTypeDelete', 'programTypeDelete', item)
                                    }
                                  />
                                </div>
                              </div>
                            ))}
                        </>
                      ) : (
                        <div className="row digi-tbl-pad pt-3 pl-0 digi-text-justify">
                          <div className="col-md-12 btn-description-border text-center p-5">
                            <button
                              className="remove_hover btn btn-description mr-3"
                              onClick={() => handleOpen('programType')}
                            >
                              <Trans i18nKey={'global_configuration.create_type'}></Trans>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </Card.Body>
                </Accordion.Collapse>
              )}
              {show['programType'] && (
                <Suspense fallback="">
                  <AddEditProgramTypeModal
                    show={show}
                    handleClose={() => handleOpen('programType')}
                    selectedProgramType={selectedProgramType}
                    type={type}
                    settingId={programInput.get('_id')}
                    updateProgramType={updateProgramType}
                    setData={setData}
                    institutionHeader={institutionHeader}
                  />
                </Suspense>
              )}
              {show['programTypeDelete'] && (
                <Suspense fallback="">
                  <DeleteModal
                    open={show}
                    handleClose={() => handleOpen('programTypeDelete')}
                    type={type}
                    item={selectedProgramType}
                    settingId={programInput.get('_id')}
                    updateProgramType={updateProgramType}
                    institutionHeader={institutionHeader}
                  />
                </Suspense>
              )}
              {!params.programID && <hr />}

              {/* Curriculum Naming */}
              {displayRow('3', 'global_configuration.curriculum_naming', 'curriculum_naming')}
              <Accordion.Collapse eventKey="3" className="bg-white">
                <Card.Body className="innerbodyLeave border-top-remove">
                  <div className="container">
                    {' '}
                    <div className="float-left">
                      <FormControl component="fieldset">
                        <div className="mb-3">
                          {pgmInput.get('curriculumNaming', List()).size > 0 &&
                            pgmInput.get('curriculumNaming', List()).map((item, key) => (
                              <div className="mb-3" key={key}>
                                <FormControlLabel
                                  className="float-left"
                                  value={item.get('mode', 3)}
                                  label={
                                    [1, 2].includes(item.get('mode', ''))
                                      ? `${t('global_configuration.mode')} ${item.get('mode', '')}`
                                      : t('global_configuration.manual_naming')
                                  }
                                  control={
                                    <Radio
                                      color="primary"
                                      disabled={params.programID ? true : false}
                                      className="float-left"
                                      checked={
                                        radioValue.get('curriculumNaming', 3) ===
                                        item.get('mode', 3)
                                      }
                                    />
                                  }
                                  onChange={() =>
                                    handleRadioButton(
                                      'curriculumNaming',
                                      item.get('mode', 3),
                                      item.get('_id')
                                    )
                                  }
                                />
                                <div className="clearfix"> </div>
                                {[1, 2].includes(item.get('mode', '')) && (
                                  <div className="row ml-2">
                                    <img
                                      src={item.get('mode', '') === 1 ? Module1 : Module2}
                                      alt="mode1"
                                    />
                                  </div>
                                )}
                              </div>
                            ))}
                        </div>
                      </FormControl>
                    </div>{' '}
                    <div className="clearfix"> </div>
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              <hr />

              {/* Credit Hours */}
              {displayRow('4', 'global_configuration.credit_hours', 'credit_hours')}
              <Accordion.Collapse eventKey="4" className="bg-white">
                <Card.Body className="innerbodyLeave border-top-remove">
                  <div className="container">
                    <div className="float-left">
                      <FormControl component="fieldset">
                        <div className="mb-3">
                          {pgmInput.get('creditHours', List()).size > 0 &&
                            pgmInput.get('creditHours', List()).map((item, key) => (
                              <div className="mb-3" key={key}>
                                <FormControlLabel
                                  className="float-left"
                                  value={item.get('mode', '')}
                                  label={
                                    item.get('mode', '') === 'standard'
                                      ? t('global_configuration.standard')
                                      : t('global_configuration.dynamic')
                                  }
                                  control={
                                    <Radio
                                      color="primary"
                                      checked={
                                        radioValue.get('creditHours', '') === item.get('mode', '')
                                      }
                                      disabled={
                                        pgmInput.get('isCreditHourEditable', true) ? false : true
                                      }
                                    />
                                  }
                                  onChange={() =>
                                    handleRadioButton(
                                      'creditHours',
                                      item.get('mode', ''),
                                      item.get('_id')
                                    )
                                  }
                                />
                                <div className="clearfix"> </div>
                                <div className="row ml-2">
                                  <img
                                    src={item.get('mode', '') === 'standard' ? Standard : Dynamic}
                                    alt="mode1"
                                  />
                                </div>
                              </div>
                            ))}
                        </div>
                      </FormControl>
                    </div>
                    <div className="clearfix"> </div>
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              <hr />
              {displayRow(
                '5',
                i18n.t('global_configuration.program_Duration_Format', {
                  program: getLabelName({ label: 'program' })
                    ? getLabelName({ label: 'program', isToolTip: true, length: 15 })
                    : 'Programs',
                }),
                'program_Duration_Format'
              )}

              <Accordion.Collapse eventKey="5" className="bg-white">
                <Card.Body className="innerbodyLeave border-top-remove">
                  <div className="container">
                    <div className="float-left">
                      <FormControl component="fieldset">
                        <div className="d-flex">
                          {pgmInput.get('programDurationFormat', List()).size > 0 &&
                            pgmInput.get('programDurationFormat', List()).map((item, key) => (
                              <div key={key}>
                                <FormControlLabel
                                  className="float-left"
                                  value={item.get('defaultInput', '')}
                                  label={
                                    item.get('defaultInput', '') === 'year'
                                      ? t('global_configuration.year_format')
                                      : t('global_configuration.Phase_format', {
                                          Phase: getLabelName({ label: 'phase' })
                                            ? getLabelName({
                                                label: 'phase',
                                                isToolTip: false,
                                                length: 15,
                                              })
                                            : 'Phase',
                                        })
                                  }
                                  control={
                                    <Radio
                                      color="primary"
                                      checked={
                                        radioValue.get('programDurationFormat', '') ===
                                        item.get('defaultInput', '')
                                      }
                                    />
                                  }
                                  onChange={() =>
                                    handleRadioButton(
                                      'programDurationFormat',
                                      item.get('defaultInput', ''),
                                      item.get('_id')
                                    )
                                  }
                                />
                                <div className="clearfix"> </div>
                              </div>
                            ))}
                        </div>
                      </FormControl>
                      <br />
                      {radioValue.get('programDurationFormat', '') === 'phase' && (
                        <FormControl className="d-flex flex-column">
                          <FormControlLabel
                            className="float-left font_size_bold"
                            // value={item.get('format', '')}
                            label={t('global_configuration.Without_Level')}
                            control={
                              <Checkbox
                                color="primary"
                                checked={radioValue.get('checkBox', false)}
                              />
                            }
                            onChange={() =>
                              handleCheckbox(
                                pgmInput.getIn(['programDurationFormat', 1, '_id'], List())
                              )
                            }
                          />
                          <span className="font_size_margin">
                            <Trans i18nKey={'global_configuration.Without_Level_enabled'}></Trans>
                          </span>
                        </FormControl>
                      )}
                    </div>
                    <div className="clearfix"> </div>
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              <hr />
            </Accordion>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}
ProgramInput.propTypes = {
  getProgramInput: PropTypes.func,
  programInput: PropTypes.instanceOf(Map),
  basicDetails: PropTypes.instanceOf(Map),
  updateLabel: PropTypes.func,
  updateProgramType: PropTypes.func,
  updateProgramInputMode: PropTypes.func,
  updateProgramDuration: PropTypes.func,
  updateProgramBasicDetails: PropTypes.func,
  setData: PropTypes.func,
  programSettings: PropTypes.instanceOf(Map),
};
const mapStateToProps = (state) => {
  return {
    programInput: selectProgramInput(state),
    basicDetails: selectBasicDetails(state),
    programSettings: selectProgramSettings(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ProgramInput);
