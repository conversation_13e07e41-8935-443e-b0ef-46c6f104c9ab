/* eslint-disable array-callback-return */
import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import FullCalendar from '@fullcalendar/react';
import arLocale from '@fullcalendar/core/locales/ar';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Modal, Button } from 'react-bootstrap';
import moment from 'moment';
import DatePicker, { registerLocale } from 'react-datepicker';
import {
  startOfDay,
  set,
  format,
  isValid,
  differenceInMinutes,
  addMinutes,
  subMilliseconds,
} from 'date-fns';
import ar from 'date-fns/locale/ar';

import {
  extractedRequiredDate,
  convertTime12to24,
  transformDateToCustomObject,
  getHour,
  getFormattedGroupName,
} from '../utils';
import {
  formatTwoString,
  getLang,
  getTranslatedShortDays,
  isIndGroup,
  getEnvLabelChanged,
  allowedTimeInterval,
  studentGroupRename,
  getURLParams,
} from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import Tooltips from '../../../../_components/UI/Tooltip/Tooltip';
import SessionDeliveryTypeList from './SessionDeliveryTypeList';
import RotationTabs from './RotationTabs';
import { t } from 'i18next';
function ManageScheduleCalendarView(props) {
  const { individualSessionDetails, course, setData, handleSaveDayTime, currentCalendar } = props;
  const [activeSession, setActiveSession] = useState(Map());
  const [activeDelivery, setActiveDelivery] = useState(Map());
  const [activeSetting, setActiveSetting] = useState(Map({ id: '', title: '', occurrenceId: '' }));
  const [eventsList, setEventsList] = useState([]);
  const calendarComponentRef = useRef(null);
  const [excludedTime, setExcludedTime] = useState(List());
  const [modalData, setModalData] = useState(
    Map({
      modal: false,
      day: '',
      startTime: '',
      endTime: '',
      mode: 'create',
    })
  );
  const [rotation, setRotation] = useState(0);
  const lang = getLang();

  registerLocale('ar', ar);
  const programId = getURLParams('programId', true);

  function checkIsOverLapping() {
    const checkStartTime = excludedTime.getIn([0, 'start'], '');
    const checkEndTime = excludedTime.getIn([0, 'end'], '');
    const startTime = getStartEndDateCheck(modalData.get('startTime', Map()));
    const endTime = getStartEndDateCheck(modalData.get('endTime', Map()));
    return checkDateAndTime(checkStartTime, checkEndTime, startTime, endTime);
  }

  function saveData() {
    if (!checkIsOverLapping()) {
      setData(Map({ message: t('start_end_time_should_not_overlap') }));
      return;
    }

    const requestBody = {
      start: modalData.get('startTime').toJS(),
      end: modalData.get('endTime').toJS(),
      day: modalData.get('day').toLowerCase(),
    };
    handleSaveDayTime(
      requestBody,
      activeSetting.get('id'),
      activeSetting.get('occurrenceId'),
      modalData.get('mode'),
      () => triggerModal(false)
    );
  }
  function deleteData() {
    const requestBody = {
      start: {},
      end: {},
      day: '',
    };
    handleSaveDayTime(
      requestBody,
      activeSetting.get('id'),
      activeSetting.get('occurrenceId'),
      'delete',
      () => triggerModal(false)
    );
  }

  function getStartEndDate(type) {
    const date = modalData.get(type, Map());
    if (date.isEmpty()) return null;
    return set(startOfDay(new Date()), { hours: getHour(date), minutes: date.get('minute') });
  }

  function triggerModal(status = false, day = '', startTime = '', endTime = '', mode = 'create') {
    setModalData(
      Map({
        modal: status,
        day: day,
        startTime: startTime,
        endTime: endTime,
        mode: mode,
      })
    );
    if (!status) {
      //setActiveSetting(Map({ id: '', title: '', occurrenceId: '' }));
    }
  }

  function allowScheduledTiming(selectInfo, endFormatTime) {
    return eventsList.filter(
      (item) =>
        item.groupId === 'EXTRA_CURRICULAR_AND_BREAK' &&
        item.allowEdit === false &&
        !checkDateAndTime(
          Date.parse(item.startStr),
          Date.parse(item.endStr),
          Date.parse(selectInfo.start),
          Date.parse(endFormatTime)
        )
    ).length;
  }

  function handleDateSelect(selectInfo) {
    if (!currentCalendar) {
      return;
    }
    const duration = activeSession.get('session_duration', 0);
    const endFormatTime = new Date(moment(selectInfo.start).add(duration, 'minutes').format());
    const allowScheduledTimings = allowScheduledTiming(selectInfo, endFormatTime);
    if (allowScheduledTimings > 0) {
      setData(Map({ message: t('start_end_time_should_not_overlap') }));
      return;
    }

    if (activeSetting.get('id', '') !== '') {
      const startTime = transformDateToCustomObject(selectInfo.start);
      const endTime =
        duration !== 0
          ? transformDateToCustomObject(endFormatTime)
          : transformDateToCustomObject(selectInfo.end);
      const day = moment(selectInfo.start).format('dddd');
      triggerModal(true, day, startTime, endTime, 'create');
      setExcludedTime(getExcludedTimes(selectInfo.start));
    } else {
      setData(Map({ message: t('please_choose_session') }));
      return;
    }
  }

  function checkDateAndTime(checkStartTime, checkEndTime, startTime, endTime) {
    if (
      (startTime < checkStartTime || checkEndTime > startTime) &&
      (endTime > checkStartTime || checkEndTime < endTime)
    ) {
      return false;
    }
    return true;
  }

  function countOfSameTimeEvents(eventInfo) {
    return eventsList.filter((item) => {
      return (
        !checkDateAndTime(
          Date.parse(item.startStr),
          Date.parse(item.endStr),
          Date.parse(eventInfo.event.start),
          Date.parse(eventInfo.event.end)
        ) &&
        item.groupId === 'SETTING_SCHEDULE' &&
        item.scheduleDate === eventInfo.event.extendedProps.scheduleDate
      );
    }).length;
  }

  function renderEventContent(eventInfo) {
    const countOfSameTime = countOfSameTimeEvents(eventInfo);

    return (
      <div className={'text-center calendarFont'}>
        {countOfSameTime > 2 ? (
          <Tooltips title={eventInfo.event.title + '.' + eventInfo.timeText}>
            <b>{eventInfo.event.extendedProps.shortTitle}</b>
          </Tooltips>
        ) : (
          <>
            {eventInfo.event.title !== '' && (
              <Tooltips
                title={
                  studentGroupRename(eventInfo.event.title, programId) + '.' + eventInfo.timeText
                }
              >
                <b>{studentGroupRename(eventInfo.event.title, programId)}</b>
                <br />
              </Tooltips>
            )}
            <i>{eventInfo.timeText}</i>
          </>
        )}
      </div>
    );
  }

  function getDateFromDay(fromDate, day) {
    const dateArray = {
      sunday: fromDate,
      monday: moment(fromDate).add(1, 'd').format('YYYY-MM-DD'),
      tuesday: moment(fromDate).add(2, 'd').format('YYYY-MM-DD'),
      wednesday: moment(fromDate).add(3, 'd').format('YYYY-MM-DD'),
      thursday: moment(fromDate).add(4, 'd').format('YYYY-MM-DD'),
      friday: moment(fromDate).add(5, 'd').format('YYYY-MM-DD'),
      saturday: moment(fromDate).add(6, 'd').format('YYYY-MM-DD'),
    };
    return dateArray[day];
  }

  useEffect(() => {
    loadExtraCurricularAndBreakEvents(course, individualSessionDetails);
  }, [course, individualSessionDetails, rotation]); // eslint-disable-line

  function loadRotation(count) {
    setRotation(count);
  }

  function loadExtraCurricularAndBreakEvents(course, individualSessionDetails) {
    let calendarApi = calendarComponentRef.current.getApi();
    calendarApi.next();
    let currentRange = calendarApi.currentDataManager.state.dateProfile.currentRange;
    const fromDate = moment(currentRange.start).format('YYYY-MM-DD');
    const toDate = moment(currentRange.end).format('YYYY-MM-DD');
    let totalDateArray = [];
    let colorGroup = {};

    course
      .get('extra_curricular_break_timing', List())
      .toJS()
      .map((item) => {
        let checkDate = extractedRequiredDate(fromDate, toDate, item.days);
        if (checkDate && checkDate.length > 0) {
          checkDate.forEach((key) => {
            totalDateArray.push({
              title: item.title,
              shortTitle: item.title,
              groupId: 'EXTRA_CURRICULAR_AND_BREAK',
              start:
                key +
                'T' +
                convertTime12to24(
                  formatTwoString(item.startTime.hour) +
                    ':' +
                    formatTwoString(item.startTime.minute) +
                    ' ' +
                    item.startTime.format
                ),
              end:
                key +
                'T' +
                convertTime12to24(
                  formatTwoString(item.endTime.hour) +
                    ':' +
                    formatTwoString(item.endTime.minute) +
                    ' ' +
                    item.endTime.format
                ),
              startStr: new Date(
                key +
                  'T' +
                  convertTime12to24(
                    formatTwoString(item.startTime.hour) +
                      ':' +
                      formatTwoString(item.startTime.minute) +
                      ' ' +
                      item.startTime.format
                  )
              ),
              endStr: new Date(
                key +
                  'T' +
                  convertTime12to24(
                    formatTwoString(item.endTime.hour) +
                      ':' +
                      formatTwoString(item.endTime.minute) +
                      ' ' +
                      item.endTime.format
                  )
              ),
              type: 'extra_curricular',
              allowEdit: item.allowCourseCoordinatesToEdit,
              titleText:
                item.startTime.hour +
                ':' +
                formatTwoString(item.startTime.minute) +
                ' - ' +
                item.endTime.hour +
                ':' +
                formatTwoString(item.endTime.minute),
            });
          });
        }
        return item;
      });

    individualSessionDetails.map((session) => {
      session.get('delivery', List()).map((delivery) => {
        delivery
          .get('settings', List())
          .filter((item) => (rotation !== 0 ? rotation === item.get('rotation_count', '') : item))
          .map((setting) => {
            const activeSettingId = setting.get('_id', '');
            if (setting.get('occurrence', List()).size > 0) {
              let arrayList = [];
              setting
                .get('session', List())
                .map((sessionGroup) => {
                  return sessionGroup
                    .get('student_groups', List())
                    .map((studentGroup) => {
                      const studentGroupName = getFormattedGroupName(
                        studentGroup.get('group_name', ''),
                        isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                      );
                      const sessionGroupNames = studentGroup
                        .get('session_group', List())
                        .map((sessionGroup) =>
                          getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                        )
                        .join(', ');
                      const value = `${studentGroupName} - ${sessionGroupNames}`;
                      arrayList.push(value);
                      return value;
                    })
                    .toJS();
                })
                .toJS();

              setting.get('occurrence', List()).map((occur) => {
                const findDate = getDateFromDay(fromDate, occur.get('day', 'sunday'));
                const arrayName = arrayList
                  .filter((value, index, self) => self.indexOf(value) === index)
                  .join(', ');
                if (!colorGroup[activeSettingId])
                  colorGroup[activeSettingId] = 'hsl(' + Math.random() * 360 + ', 100%, 75%)';
                totalDateArray.push({
                  title: arrayName + ' ' + delivery.get('delivery_name', ''),
                  shortTitle: arrayName,
                  groupId: 'SETTING_SCHEDULE',
                  activeSession: session,
                  activeDelivery: delivery,
                  activeSetting: Map({
                    id: activeSettingId,
                    title: arrayName,
                    occurrenceId: occur.get('_id'),
                  }),
                  start:
                    findDate +
                    'T' +
                    convertTime12to24(
                      formatTwoString(occur.getIn(['start', 'hour'])) +
                        ':' +
                        formatTwoString(occur.getIn(['start', 'minute'])) +
                        ' ' +
                        occur.getIn(['start', 'format'])
                    ),
                  end:
                    findDate +
                    'T' +
                    convertTime12to24(
                      formatTwoString(occur.getIn(['end', 'hour'])) +
                        ':' +
                        formatTwoString(occur.getIn(['end', 'minute'])) +
                        ' ' +
                        occur.getIn(['end', 'format'])
                    ),
                  startStr: new Date(
                    findDate +
                      'T' +
                      convertTime12to24(
                        formatTwoString(occur.getIn(['start', 'hour'])) +
                          ':' +
                          formatTwoString(occur.getIn(['start', 'minute'])) +
                          ' ' +
                          occur.getIn(['start', 'format'])
                      )
                  ),
                  endStr: new Date(
                    findDate +
                      'T' +
                      convertTime12to24(
                        formatTwoString(occur.getIn(['end', 'hour'])) +
                          ':' +
                          formatTwoString(occur.getIn(['end', 'minute'])) +
                          ' ' +
                          occur.getIn(['end', 'format'])
                      )
                  ),
                  type: 'setting_schedule',
                  allowEdit: false,
                  backgroundColor: colorGroup[activeSettingId],
                  titleText:
                    occur.getIn(['start', 'hour']) +
                    ':' +
                    formatTwoString(occur.getIn(['start', 'minute'])) +
                    ' - ' +
                    occur.getIn(['end', 'hour']) +
                    ':' +
                    formatTwoString(occur.getIn(['end', 'minute'])),
                });
              });
            }
          });
      });
    });
    setEventsList(totalDateArray);
  }

  function handleChange(name, value) {
    if (name === 'startTime') {
      const duration = activeSession.get('session_duration', 60);
      const updatedModalData = modalData.merge(
        Map({
          startTime: transformDateToCustomObject(value),
          endTime: transformDateToCustomObject(
            new Date(moment(value).add(duration, 'minutes').format())
          ),
        })
      );
      setModalData(updatedModalData);
    } else if (name === 'endTime') {
      const updatedModalData = modalData.merge(
        Map({
          endTime: transformDateToCustomObject(value),
        })
      );
      setModalData(updatedModalData);
    }
  }

  function getCourseSessionGroup(type) {
    if (activeSetting.get('id', '') !== '' && activeSetting.get('title', '') !== '') {
      const firstSplit = activeSetting.get('title', '').split(' - ').join(', ').split(', ');
      if (firstSplit && firstSplit.length > 0) {
        const courseGroup = firstSplit.filter((item) => {
          const subS = item.substr(0, getEnvLabelChanged() ? 2 : 3);
          return [
            'M-G',
            'F-G',
            'MG-',
            'FG-',
            'M-R',
            'F-R',
            'MRG',
            'FRG',
            'MF-',
            'MFG',
            'SG-',
            'SG',
            'FG',
            'MG',
            'MF',
            'MR',
            'FR',
            'M-',
            'F-',
          ].includes(subS);
        });
        if (type === 'course') {
          return courseGroup.join(', ');
        } else {
          return firstSplit.filter((item) => !courseGroup.includes(item)).join(', ');
        }
      }
    }
    return '';
  }

  function handleEventClick(clickInfo) {
    if (!currentCalendar) {
      return;
    }
    const startTime = transformDateToCustomObject(clickInfo.event?.start);
    const endTime =
      clickInfo.event?.end !== null ? transformDateToCustomObject(clickInfo.event?.end) : startTime;
    const day = moment(clickInfo.event?.start).format('dddd');
    if (clickInfo.event?.extendedProps?.type === 'extra_curricular') {
      if (clickInfo.event?.extendedProps?.allowEdit) {
        if (activeSetting.get('id', '') !== '') {
          triggerModal(true, day, startTime, endTime, 'create');
        } else {
          alert('Please choose session / delivery group and setting');
          setData(Map({ message: 'Please choose session / delivery group and setting' }));
          return;
        }
      }
      return false;
    }
    setActiveSession(clickInfo.event?.extendedProps?.activeSession);
    setActiveDelivery(clickInfo.event?.extendedProps?.activeDelivery);
    setActiveSetting(clickInfo.event?.extendedProps?.activeSetting);
    triggerModal(true, day, startTime, endTime, 'update');
  }

  function getStartEndDateCheck(date) {
    return set(startOfDay(new Date()), { hours: getHour(date), minutes: date.get('minute') });
  }

  function getExcludedTimes(date) {
    const extraCurricularAndBreaks = course.get('extra_curricular_break_timing', List());
    if (extraCurricularAndBreaks.isEmpty()) return List();
    const scheduleDate = new Date(date);
    if (!isValid(scheduleDate)) return List();
    const day = format(scheduleDate, 'EEEE').toLowerCase();
    const filteredData = extraCurricularAndBreaks.filter(
      (e) =>
        e.get('gender') === 'both' &&
        e.get('days', List()).includes(day) &&
        (e.get('type') === 'break' || !e.get('allowCourseCoordinatesToEdit', false))
    );
    return filteredData.reduce((acc, e) => {
      const startDate = getStartEndDateCheck(e.get('startTime', Map()));
      const endDate = getStartEndDateCheck(e.get('endTime', Map()));
      if (!isValid(startDate) || !isValid(endDate)) return acc;
      const durationInMinutes = differenceInMinutes(endDate, startDate);
      let each15MinutesOfInterval = List([addMinutes(startDate, 1)]);
      const timeIntervals = allowedTimeInterval();
      for (let i = timeIntervals; i < durationInMinutes; i = i + timeIntervals) {
        each15MinutesOfInterval = each15MinutesOfInterval.push(addMinutes(startDate, i));
      }
      const end = subMilliseconds(addMinutes(startDate, durationInMinutes), 1);
      each15MinutesOfInterval = each15MinutesOfInterval.push(end);
      return acc.push(
        Map({
          start: startDate,
          end,
          excludedTimes: each15MinutesOfInterval,
        })
      );
    }, List());
  }
  return (
    <div className="col-lg-12">
      <div className="row border-top">
        <div className="col-md-3 border-right pr-0">
          <p className="mb-2 pl-3 pt-2 bold">{t('student_grouping.session_delivery_type')}</p>
          <SessionDeliveryTypeList
            individualSessionDetails={individualSessionDetails}
            activeSession={activeSession}
            setActiveSession={setActiveSession}
            activeDelivery={activeDelivery}
            setActiveDelivery={setActiveDelivery}
            activeSetting={activeSetting}
            setActiveSetting={setActiveSetting}
            rotation={rotation}
          />
        </div>
        <div className="col-md-9">
          <div className="calendar-container">
            <div className="calendar-header">
              <h1>
                Schedule{' '}
                <span className="device-show">
                  (Note: For iPad / Tablet users please click and hold the time slot for one second
                  to open schedule popup){' '}
                </span>
              </h1>
            </div>
            {course.get('rotation', 'no') === 'yes' && (
              <RotationTabs course={course} callBack={loadRotation} />
            )}
            <div className="settings-calendar">
              <FullCalendar
                locale={getLang() === 'ar' ? arLocale : null}
                ref={calendarComponentRef}
                plugins={[timeGridPlugin, interactionPlugin]}
                headerToolbar={{
                  left: '',
                  center: '',
                  right: '',
                }}
                dayHeaderContent={(dt) => {
                  return getTranslatedShortDays(moment(dt.date).format('ddd').toLowerCase());
                }}
                initialView="timeGridWeek"
                dayHeaderFormat={{ day: 'numeric', weekday: 'short' }}
                scrollTime={'08:00:00'}
                eventOverlap={true}
                editable={false}
                selectable={true}
                selectMirror={true}
                dayMaxEvents={true}
                weekends={true}
                events={eventsList}
                eventTextColor={'black'}
                contentHeight={'auto'}
                select={
                  CheckPermission(
                    'subTabs',
                    'Schedule Management',
                    'Course Scheduling',
                    '',
                    'Schedule',
                    '',
                    'Manage Course',
                    'Session Default Settings Add'
                  )
                    ? handleDateSelect
                    : ''
                }
                eventContent={renderEventContent} // custom render function
                eventClassNames={function (arg) {
                  if (arg.event.extendedProps.type) {
                    return ['blockEvents'];
                  } else {
                    return ['normal'];
                  }
                }}
                slotEventOverlap={false}
                allDaySlot={false}
                eventClick={
                  CheckPermission(
                    'subTabs',
                    'Schedule Management',
                    'Course Scheduling',
                    '',
                    'Schedule',
                    '',
                    'Manage Course',
                    'Session Default Settings Edit'
                  )
                    ? handleEventClick
                    : ''
                }
                longPressDelay={100}
              />
            </div>
          </div>
        </div>
      </div>

      {modalData.get('modal', false) && (
        <Modal show={true} dialogClassName="model-500" centered>
          <Modal.Body>
            <div className="">
              <div className="border-bottom mb-3">
                <p className="mb-2">
                  {' '}
                  <b>{activeDelivery.get('delivery_name', '')}</b>
                </p>
              </div>
              <div className="row align-items-center pt-2 pb-2">
                <div className="col-md-5">
                  <div className="f-16">{t('day')} *</div>
                </div>
                <div className="col-md-7">
                  <input type="text" className="form-control" value={modalData.get('day', '')} />
                </div>
              </div>
              <div className="row align-items-center pt-2 pb-2">
                <div className="col-md-5">
                  <div className="f-16">{t('dashboard_view.time')} *</div>
                </div>
                <div className="col-md-7">
                  <div className="row">
                    <div className="col-md-6 pr-1">
                      <div className="schedule-date-picker-container">
                        <DatePicker
                          onChange={(date) => handleChange('startTime', date)}
                          {...(lang === 'ar' && { locale: 'ar' })}
                          selected={getStartEndDate('startTime')}
                          className="schedule-date-picker-input"
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={allowedTimeInterval()}
                          timeCaption={t('reports_analytics.start_time')}
                          dateFormat="h:mm aa"
                          timeFormat="hh:mm aa"
                          excludeTimes={excludedTime
                            .reduce(
                              (acc, excluded) => acc.concat(excluded.get('excludedTimes', List())),
                              List()
                            )
                            .toJS()}
                        />
                      </div>
                    </div>

                    <div className="col-md-6 pl-1">
                      <div className="schedule-date-picker-container">
                        <DatePicker
                          {...(lang === 'ar' && { locale: 'ar' })}
                          onChange={(date) => handleChange('endTime', date)}
                          selected={getStartEndDate('endTime')}
                          className="schedule-date-picker-input"
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={allowedTimeInterval()}
                          timeCaption={t('reports_analytics.end_time')}
                          dateFormat="h:mm aa"
                          timeFormat="hh:mm aa"
                          excludeTimes={excludedTime
                            .reduce(
                              (acc, excluded) => acc.concat(excluded.get('excludedTimes', List())),
                              List()
                            )
                            .toJS()}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="row align-items-center pt-2 pb-2">
                <div className="col-md-5">
                  <div className="f-16">{t('course_group')} *</div>
                </div>
                <div className="col-md-7">
                  <input
                    type="text"
                    className="form-control"
                    value={studentGroupRename(getCourseSessionGroup('course'), programId)}
                  />
                </div>
              </div>
              <div className="row align-items-center pt-2 pb-2">
                <div className="col-md-5">
                  <div className="f-16">{t('delivery_group')} *</div>
                </div>
                <div className="col-md-7">
                  <input
                    type="text"
                    className="form-control"
                    value={getCourseSessionGroup('session')}
                  />
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <div className="container">
              <div className="row">
                <div className="col-md-12" style={{ paddingRight: '0', paddingLeft: '0' }}>
                  {modalData.get('mode', 'create') === 'update' &&
                    CheckPermission(
                      'subTabs',
                      'Schedule Management',
                      'Course Scheduling',
                      '',
                      'Schedule',
                      '',
                      'Manage Course',
                      'Session Default Settings Delete'
                    ) && (
                      <div className="mr-2" style={{ position: 'absolute' }}>
                        <i
                          className="fa fa-trash remove_hover"
                          style={{ fontSize: '24px' }}
                          onClick={() => deleteData()}
                        ></i>
                      </div>
                    )}

                  <div className="float-right">
                    <b className="pr-3">
                      <Button variant="outline-secondary" onClick={() => triggerModal(false)}>
                        {t('cancel')}
                      </Button>
                    </b>

                    <b>
                      <Button variant="primary" onClick={() => saveData()}>
                        {t('save')}
                      </Button>
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
}

ManageScheduleCalendarView.propTypes = {
  individualSessionDetails: PropTypes.instanceOf(List),
  course: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  handleSaveDayTime: PropTypes.func,
  currentCalendar: PropTypes.bool,
};

export default ManageScheduleCalendarView;
