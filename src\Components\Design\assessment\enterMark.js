import React, { useState } from 'react';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import dotIcon from '../../../Assets/dotIcon.svg';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
function EnterMark(props) {
  const programType = [
    {
      name: 'CO - Course Learning Outcome',
      value: 'CO - Course Learning Outcome',
    },
    {
      name: 'CO - Course Learning Outcome',
      value: 'CO - Course Learning Outcome',
    },
    {
      name: 'CO - Course Learning Outcome',
      value: 'CO - Course Learning Outcome',
    },
  ];

  const quentionNumber = [
    {
      name: 'C01',
      value: 'C01',
    },
    {
      name: 'C02',
      value: 'C02',
    },
    {
      name: 'C03',
      value: 'C03',
    },
  ];

  const gender = [
    {
      name: 'Male',
      value: 'Male',
    },
    {
      name: 'Female',
      value: 'Female',
    },
  ];

  const [show, setShow] = useState(false);
  const handleShow = () => {
    setShow(true);
  };
  const handleModalClose = () => {
    setShow(false);
  };

  const [AddShow, setAddShow] = useState(false);
  const handleAddShow = () => {
    setAddShow(true);
  };
  const handleAddModalClose = () => {
    setAddShow(false);
  };

  return (
    <div className="main pb-5">
      <div className="bg-white p-2 border-bottom-2px">
        <div className="container-fluid">
          <div className="row ">
            <div className="col-md-4 pt-1">
              <p className="font-weight-bold mb-0 f-17">
                {' '}
                <i className="fa fa-arrow-left pr-3 remove_hover" aria-hidden="true"></i>Enter Mark
              </p>
            </div>
            <div className="col-md-8 d-flex justify-content-end">
              <MButton variant="outlined" color="primary" className={'mr-2'}>
                Cancel
              </MButton>
              <MButton variant="contained" color="primary" clicked={handleAddShow}>
                Save
              </MButton>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray p-2">
        <div className="container-fluid">
          <div className="row ">
            <div className="col-md-5 pt-1">
              <p className="bold mb-0 f-20">Alumini Survey - 1</p>
              <p className="bold mb-0 f-15">Total Marks: 100</p>
            </div>
            <div className="col-md-7">
              <div className="d-flex justify-content-end align-items-end">
                <div className="w-20 pr-2">
                  <MaterialInput
                    elementType={'materialSelect'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    elementConfig={{ options: programType }}
                    label={'Select Student Level'}
                    labelclass={'mb-0 f-14'}
                  />
                </div>
                <div className="w-20 pr-2 pl-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'number'}
                    variant={'outlined'}
                    size={'small'}
                    elementConfig={{ options: programType }}
                    label={'No of Question'}
                    labelclass={'mb-0 f-14'}
                  />
                </div>

                <div className="w-28 pt-1">
                  <MButton
                    variant="contained"
                    clicked={handleShow}
                    color={'skyBlueButton'}
                    className="bold mb-2 mt-3"
                  >
                    {' '}
                    Add Student Manually
                  </MButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-gray pl-3 pr-3">
        <div className="assessment_table">
          <table align="left">
            <thead>
              <tr>
                <th scope="col" className="borderNone">
                  <div className="cw_350 custom_space">
                    <p className="thHeader text-left">Questions</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q1</p>
                  </div>
                </th>

                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q2</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q3</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q4</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q5</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q6</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q7</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q8</p>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div className="cw_100 custom_space">
                    <p className="thHeader">Q9</p>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th scope="col" className="borderNone">
                  <div className="row custom_space">
                    <div className="col-md-12">
                      <MaterialInput
                        elementType={'materialSelect'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{ options: programType }}
                      />
                    </div>
                  </div>
                </th>

                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialSelect'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: quentionNumber }}
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="borderNone">
                  <div className="row custom_space">
                    <div className="col-md-12">
                      <p className="thHeader text-left">Total Mark</p>
                    </div>
                  </div>
                </th>

                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="borderNone">
                  <div className="row custom_space">
                    <div className="col-md-9">
                      <p className="thHeader text-left">Attainment Benchmark</p>
                    </div>
                    <div className="cw_80">
                      <TextField
                        fullWidth
                        variant="outlined"
                        size="small"
                        InputProps={{
                          endAdornment: <InputAdornment position="end">%</InputAdornment>,
                        }}
                      />
                    </div>
                  </div>
                </th>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
                <td className="borderNone bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="bg-white">
                  <div className="d-flex custom_space align-items-center">
                    <p className="mb-0 mr-4">1</p>
                    <div className="mr-3 w-75">
                      <p className="mb-0">Mohamed Abdhulhkathar</p>
                      <span className="mr-1 f-14"> 123456789 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Male </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> L9 </span>
                      <img src={dotIcon} alt="Deactivated" />
                    </div>
                    <div className="d-flex justify-content-end ml-3 mt-3 pt-2 remove_hover">
                      <p className="mb-0 f-14 mr-3 text-green">P</p>
                      <ArrowDropDownIcon fontSize="small" className="remove_hover" />
                    </div>
                  </div>
                </th>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="bg-white">
                  <div className="d-flex custom_space align-items-center">
                    <p className="mb-0 mr-4">2</p>
                    <div className="mr-3 w-75">
                      <p className="mb-0">Mohamed Abdhulhkathar</p>
                      <span className="mr-1 f-14"> 123456789 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Male </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> L9 </span>
                      <img src={dotIcon} alt="Deactivated" />
                    </div>
                    <div className="d-flex justify-content-end ml-3 mt-3 pt-2 remove_hover">
                      <p className="mb-0 f-14 mr-3 text-green">P</p>
                      <ArrowDropDownIcon fontSize="small" className="remove_hover" />
                    </div>
                  </div>
                </th>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
              </tr>
              <tr>
                <th scope="col" className="bg-white">
                  <div className="d-flex custom_space align-items-center">
                    <p className="mb-0 mr-4">3</p>
                    <div className="mr-3 w-75">
                      <p className="mb-0">Mohamed Abdhulhkathar</p>
                      <span className="mr-1 f-14"> 123456789 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Male </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> L9 </span>
                      <img src={dotIcon} alt="Deactivated" />
                    </div>
                    <div className="d-flex justify-content-end ml-3 mt-3 pt-2 remove_hover">
                      <p className="mb-0 f-14 mr-3 text-green">P</p>
                      <ArrowDropDownIcon fontSize="small" className="remove_hover" />
                    </div>
                  </div>
                </th>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
              </tr>
              <tr>
                <th scope="col" className="bg-white">
                  <div className="d-flex custom_space align-items-center">
                    <p className="mb-0 mr-4">4</p>
                    <div className="mr-3 w-75">
                      <p className="mb-0">Mohamed Abdhulhkathar</p>
                      <span className="mr-1 f-14"> 123456789 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Male </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> L9 </span>
                      <img src={dotIcon} alt="Deactivated" />
                    </div>
                    <div className="d-flex justify-content-end ml-3 mt-3 pt-2 remove_hover">
                      <p className="mb-0 f-14 mr-3 text-green">P</p>
                      <ArrowDropDownIcon fontSize="small" className="remove_hover" />
                    </div>
                  </div>
                </th>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="bg-white">
                  <div className="d-flex custom_space align-items-center">
                    <p className="mb-0 mr-4">5</p>
                    <div className="mr-3 w-75">
                      <p className="mb-0">Mohamed Abdhulhkathar</p>
                      <span className="mr-1 f-14"> 123456789 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Male </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> L9 </span>
                      <img src={dotIcon} alt="Deactivated" />
                    </div>
                    <div className="d-flex justify-content-end ml-3 mt-3 pt-2 remove_hover">
                      <p className="mb-0 f-14 mr-3 text-red">A</p>
                      <ArrowDropDownIcon fontSize="small" className="remove_hover" />
                    </div>
                  </div>
                </th>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="bg-white">
                  <div className="d-flex custom_space align-items-center">
                    <p className="mb-0 mr-4">6</p>
                    <div className="mr-3 w-75">
                      <p className="mb-0">Mohamed Abdhulhkathar</p>
                      <span className="mr-1 f-14"> 123456789 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Male </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> L9 </span>
                      <img src={dotIcon} alt="Deactivated" />
                    </div>
                    <div className="d-flex justify-content-end ml-3 mt-3 pt-2 remove_hover">
                      <p className="mb-0 f-14 mr-3 text-red">A</p>
                      <ArrowDropDownIcon fontSize="small" className="remove_hover" />
                    </div>
                  </div>
                </th>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="bg-gray">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="bg-white">
                  <div className="d-flex custom_space align-items-center">
                    <p className="mb-0 mr-4">7</p>
                    <div className="mr-3 w-75">
                      <p className="mb-0">Mohamed Abdhulhkathar</p>
                      <span className="mr-1 f-14"> 123456789 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Male </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> L9 </span>
                      <img src={dotIcon} alt="Deactivated" />
                    </div>
                    <div className="d-flex justify-content-end ml-3 mt-3 pt-2 remove_hover">
                      <p className="mb-0 f-14 mr-3 text-green">P</p>
                      <ArrowDropDownIcon fontSize="small" className="remove_hover" />
                    </div>
                  </div>
                </th>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="10"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
                <td className="">
                  <div className="cw_100 custom_space">
                    <MaterialInput
                      elementType={'materialInputTransparent'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      value="90"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {show && (
        <MaterialDialog show={show} onClose={handleModalClose} maxWidth={'xs'} fullWidth={true}>
          <div className="w-100 p-4">
            <p className="mb-3 pb-2 border-bottom bold f-19"> Edit Student</p>
            <div className="mt-2 mb-2">
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                label={'Student Name'}
                labelclass={'mb-0 f-14'}
              />
            </div>
            <div className="mt-2 mb-2">
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                label={'Student ID'}
                labelclass={'mb-0 f-14'}
              />
            </div>

            <div className="mt-2 mb-2">
              <p className="bold mb-1 f-15">Gender</p>
              <div className="d-flex">
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Male"
                  labelPlacement="end"
                />
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Female"
                  labelPlacement="end"
                />
              </div>
            </div>
            <div className="d-flex justify-content-end border-top pt-3">
              <MButton
                variant="outlined"
                color="primary"
                className={'mr-2'}
                clicked={handleModalClose}
              >
                Cancel
              </MButton>
              <MButton variant="contained" color="primary">
                Save
              </MButton>
            </div>
          </div>
        </MaterialDialog>
      )}

      {AddShow && (
        <MaterialDialog
          show={AddShow}
          onClose={handleAddModalClose}
          maxWidth={'md'}
          fullWidth={true}
        >
          <div className="w-100 p-4">
            <p className="mb-3 pb-2 border-bottom bold f-19"> Add Student </p>
            <div className="mt-2 mb-2">
              <div className="program_table">
                <table align="left">
                  <thead>
                    <tr>
                      <th>
                        <div className=""> S no.</div>
                      </th>

                      <th>
                        <div className=""> Student Name</div>
                      </th>

                      <th>
                        <div className=""> Student ID</div>
                      </th>
                      <th>
                        <div className=""> Gender</div>
                      </th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr className="tr-change">
                      <td className="">
                        <div className="mt-2">01</div>
                      </td>
                      <td className="">
                        <div className="mt-2">Student Name 1</div>
                      </td>
                      <td className="">
                        <div className="mt-2">9874563210</div>
                      </td>
                      <td className="">
                        <div className="mt-2">Male</div>
                      </td>
                    </tr>
                    <tr className="tr-change">
                      <td className="">
                        <div className="mt-2">02</div>
                      </td>
                      <td className="">
                        <div className="mt-2">Student Name 2</div>
                      </td>
                      <td className="">
                        <div className="mt-2">9874533889</div>
                      </td>
                      <td className="">
                        <div className="mt-2">Female</div>
                      </td>
                    </tr>
                    <tr className="tr-change">
                      <td className="w-15">
                        <div className="mt-2">
                          {' '}
                          <MaterialInput
                            elementType={'materialInput'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                          />
                        </div>
                      </td>
                      <td className="">
                        <div className="mt-2">
                          {' '}
                          <MaterialInput
                            elementType={'materialInput'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                          />
                        </div>
                      </td>
                      <td className="">
                        <div className="mt-2">
                          {' '}
                          <MaterialInput
                            elementType={'materialInput'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                          />
                        </div>
                      </td>
                      <td className="w-15">
                        <div className="mt-2">
                          {' '}
                          <MaterialInput
                            elementType={'materialSelect'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                            elementConfig={{ options: gender }}
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div className="mt-3 mb-2">
              <p className="mb-0 pb-2 bold  text-right text-skyblue">+ Add New </p>
            </div>

            <div className="d-flex justify-content-end border-top pt-3">
              <MButton
                variant="outlined"
                color="primary"
                className={'mr-2'}
                clicked={handleModalClose}
              >
                Cancel
              </MButton>
              <MButton variant="contained" color="primary">
                Save
              </MButton>
            </div>
          </div>
        </MaterialDialog>
      )}
    </div>
  );
}

export default EnterMark;
