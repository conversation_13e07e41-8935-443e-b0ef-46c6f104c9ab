import moment from 'moment';
const saveEdit = (state, payload, year, index, from) => {
  let data = {
    ...payload,
    event_name: {
      first_language: payload.title,
    },
    event_date: payload.start_date,
    start_time: payload.start_date + 'T' + payload.start_time + '00:000',
    end_time: payload.end_date + 'T' + payload.end_time + '00:000',
  };
  delete data.title;
  delete data.start_date;
  let final_array = [];
  if (index < state[year][from].length - 1 && index > 0) {
    final_array = [
      ...state[year][from].slice(0, index),
      data,
      ...state[year][from].slice(index + 1),
    ];
  } else if (index === state[year][from].length - 1) {
    final_array = [...state[year][from].slice(0, index), data];
  } else {
    final_array = [data, ...state[year][from].slice(index + 1)];
  }

  return {
    ...state,
    modal: false,
    edit: {
      _id: '',
      description: '',
      event_type: '',
      title: '',
      start_date: '',
      start_time: '',
      end_date: '',
      end_time: '',
    },
    edit_config: {
      year: '',
      index: '',
      from: '',
    },
    [year]: {
      ...state[year],
      [from]: [...final_array],
    },
  };
};

const syncExam = (state, year, from, start, end) => {
  let source = state[year]['events'];
  let year_events = source.filter((item) =>
    item.event_type === 'exam'
      ? Date.parse(item.start_date) >= Date.parse(start)
        ? Date.parse(item.end_date) <= Date.parse(end)
          ? false
          : true
        : true
      : true
  );
  let level_events = source.filter((item) =>
    item.event_type === 'exam'
      ? Date.parse(item.start_date) >= Date.parse(start)
        ? Date.parse(item.end_date) <= Date.parse(end)
          ? true
          : false
        : false
      : false
  );

  return {
    ...state,
    [year]: {
      ...state[year],
      events: [...year_events],
      [from]: [...state[year][from], ...level_events],
    },
  };
};

const rootReducer = (state, action) => {
  const {
    type,
    payload,
    year,
    name,
    id,
    index,
    from,
    start,
    end,
    min,
    max,
    cal_id,
    events,
    level_num,
    _calender_id,
    term,
  } = action;

  switch (type) {
    case 'CLEAR_TWO_DATE':
      return {
        ...state,
        [year]: {
          ...state[year],
          [name]: '',
        },
      };
    case 'DELETE_EVENT':
      return {
        ...state,
        [year]: {
          ...state[year],
          [from]: state[year][from].filter((item, i) => i !== index),
        },
      };
    case 'PRESS_SYNC':
      return syncExam(state, year, from, start, end);
    case 'EDIT_COMPLETE':
      return saveEdit(state, payload, year, index, from);
    case 'SHOW_MODAL':
      return {
        ...state,
        modal: true,
        edit: {
          activeSemester: '',
          year: year,
          event_type: payload.event_type,
          title: payload.event_name,
          start_date: moment(payload.event_date).format('D MMM YYYY'),
          start_time: payload.start_time,
          end_date: moment(payload.end_date).format('D MMM YYYY'),
          end_time: payload.end_time,
          _id: id,
          payload: payload,
          level_num,
          cal_id,
        },
        edit_config: {
          year: year,
          index: index,
          from: from,
          min,
          max,
          events,
        },
      };
    case 'CLEAR_MODAL':
      return {
        ...state,
        modal: false,
      };
    case 'ON_TYPE':
      return {
        ...state,
        edit: {
          ...state.edit,
          [name]: payload,
        },
      };
    case 'OFF':
      return {
        ...state,
        modal: false,
      };
    case 'ON_INPUT': {
      let copyOldState = { ...state };
      let copyYear = copyOldState[year];
      let copyLevel = copyYear['level'];
      const findLevelIndex = copyLevel.findIndex(
        (item) => item.level_no === level_num && item.term === term
      );
      let copyLevelIndex = copyLevel[findLevelIndex];
      copyLevelIndex[name] = payload;
      if (name === 'start_date') {
        copyLevelIndex['end_date'] = '';
      }
      // copyLevel[index] = copyLevelIndex;
      // copyYear['level'] = copyLevel;
      // copyOldState[year] = copyYear;
      return copyOldState;
    }
    case 'INITIAL_LOAD':
      return {
        ...state,
        year1: {
          ...state.year1,
          ...payload.year1,
        },
        year2: {
          ...state.year2,
          ...payload.year2,
        },
        year3: {
          ...state.year3,
          ...payload.year3,
        },
        year4: {
          ...state.year4,
          ...payload.year4,
        },
        year5: {
          ...state.year5,
          ...payload.year5,
        },
        year6: {
          ...state.year6,
          ...payload.year6,
        },
        _calender_id,
      };
    default:
      return {
        ...state,
      };
  }
};

export default rootReducer;
