import React from 'react';
import PropTypes from 'prop-types';
import i18n from '../../../../i18n';
import { FlexWrapper, BlockWrapper, Label, Select } from '../../Styled';

const CourseAddMode = ({
  data,
  getRotationalCourses,
  NotificationManager,
  setLoaderState,
  id,
  work_level_number,
  currentLevelData,
  urlTerm,
}) => {
  const [nonRotation, setNonRotation] = data;

  return (
    <BlockWrapper mg="0 50px 0px 65px">
      {nonRotation.course_add_mode === 'manual' && (
        <FlexWrapper mg="15px 0px">
          <Label mg="0px 10px 0px 0px">{i18n.t('select_a_rotation')}</Label>
          <Select
            value={nonRotation.type.rotation_count}
            disabled={nonRotation.disable_items}
            onChange={(e) => {
              setNonRotation({
                type: 'ON_CHANGE',
                name: 'rotation_count',
                payload: Number(e.target.value),
              });
              setLoaderState(true);
              getRotationalCourses(
                id,
                urlTerm,
                work_level_number,
                Number(e.target.value),
                NotificationManager,
                setLoaderState
              );
            }}
          >
            <option value={0}>{i18n.t('select_rotation')}</option>

            {currentLevelData &&
              currentLevelData['rotation_course'] &&
              currentLevelData['rotation_course'].length > 0 &&
              currentLevelData['rotation_course'].map((item, i) => (
                <option key={i} value={i + 1}>{`Rotation ${i + 1}`}</option>
              ))}
          </Select>
        </FlexWrapper>
      )}
    </BlockWrapper>
  );
};

CourseAddMode.propTypes = {
  data: PropTypes.object,
  currentLevelData: PropTypes.object,
  getRotationalCourses: PropTypes.func,
  setLoaderState: PropTypes.func,
  NotificationManager: PropTypes.any,
  id: PropTypes.string,
  work_level_number: PropTypes.string,
};

export default CourseAddMode;
