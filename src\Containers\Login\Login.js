import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Redirect, with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import PropTypes from 'prop-types';
import { NotificationManager } from 'react-notifications';
import { Button, Form } from 'react-bootstrap';
import { Trans, withTranslation } from 'react-i18next';

import config from '../../_utils/config';
import DS_logo from '../../Assets/ds_logo.svg';
import * as Constants from '../../constants';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import Input from '../../Widgets/FormElements/Input/Input';
import * as actions from '../../_reduxapi/actions/index';
import { getActiveVersion, getLang, isDemoVer, isIndVer } from '../../utils';
import i18n from '../../i18n';
import LocalStorageService from 'LocalStorageService';

const emailVal = Constants.EMAIL_VALIDATION;
const Number = Constants.NUMBER_VALIDATION;
const { apiInstance } = config;
class Login extends Component {
  constructor() {
    super();
    this.state = {
      emailVerifyTap: true,
      otpVerificationTap: false,
      emailError: '',
      pswError: '',
      email: '',
      psw: '',
      otpType: 'mobile',
      signInMessage: '',
      otp: '',
      checkBoxMobile: true,
      checkBoxEmail: false,
      signInShow: true,
      minutes: 3,
      seconds: 0,
      sidebarShow: false,
      id: '',
      isLoading: false,
      delayMsg: true,
      passwordType: 'password',
    };
    this.otpRef = React.createRef();
    this.emailRef = React.createRef();
    this.passwordRef = React.createRef();
  }

  componentDidMount() {
    if (this.emailRef.current !== null) {
      this.emailRef.current.focus();
    }
  }

  showPassword = () => {
    if (this.passwordRef.current !== null) {
      const type = this.passwordRef.current.type;
      const updatedValue = type === 'password' ? 'text' : 'password';
      this.passwordRef.current.type = updatedValue;
      this.setState({ passwordType: updatedValue });
    }
  };

  componentWillUnmount() {
    clearInterval(this.myInterval);
  }

  timer = () => {
    this.myInterval = setInterval(() => {
      const { seconds, minutes } = this.state;
      if (seconds > 0) {
        this.setState(({ seconds }) => ({
          seconds: seconds - 1,
        }));
      }
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(this.myInterval);
        } else {
          this.setState(({ minutes }) => ({
            minutes: minutes - 1,
            seconds: 59,
          }));
        }
      }
    }, 1000);
  };

  signUp = () => {
    this.props.history.push('/signup');
  };

  onChange = (e, name) => {
    e.preventDefault();
    if (name === 'email') {
      this.setState({
        email: e.target.value,
        emailError: '',
      });
    }
    if (name === 'psw') {
      this.setState({
        psw: e.target.value,
        pswError: '',
      });
    }
    if (name === 'newPsw') {
      this.setState({
        newPsw: e.target.value,
        newPswError: '',
      });
    }
    if (name === 'confirmPsw') {
      this.setState({
        confirmPsw: e.target.value,
        confirmPswError: '',
      });
    }
    if (name === 'mobile') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mobile: e.target.value,
        mobileError: '',
      });
    }
    if (name === 'otp') {
      if (isNaN(e.target.value)) return;
      this.setState({
        otp: e.target.value,
        otpError: '',
      });
    }
  };

  validation = () => {
    let emailError = '';
    let pswError = '';

    if (!this.state.email) {
      emailError = i18n.t('user_management.email_required');
    } else if (!emailVal.test(this.state.email)) {
      emailError = i18n.t('leaveManagement.errorMsg.validMail');
    }

    if (!this.state.psw) {
      pswError = i18n.t('Auth_words.password_is_required');
    } else if (this.state.psw.length <= 7) {
      pswError = i18n.t('Auth_words.min_eignt');
    }

    if (emailError || pswError) {
      this.setState({
        emailError,
        pswError,
      });
      return false;
    }
    return true;
  };

  otpValidation = () => {
    let otpError = '';

    if (!this.state.otp) {
      otpError = i18n.t('resend_otp');
    } else if (this.state.otp.length <= 3) {
      otpError = i18n.t('Auth_words.min_four');
    } else if (!Number.test(this.state.otp)) {
      otpError = i18n.t('Auth_words.number_only');
    }

    if (otpError) {
      this.setState({
        otpError,
      });
      return false;
    }
    return true;
  };

  resetSteps = () => {
    this.setState({
      isLoading: false,
      emailVerifyTap: true,
      otpVerificationTap: false,
      otp: '',
      signInMessage: 'Role is not assigned, Please contact admin',
    });

    setTimeout(() => this.setState({ signInMessage: '' }), 5000);
  };

  handleSignIn = (e) => {
    e.preventDefault();
    if (this.validation()) {
      const { isAdminLogin } = this.props;
      const signIn = {
        email: this.state.email,
        password: this.state.psw,
        ...(!isAdminLogin && { otp_mode: this.state.otpType }),
      };
      this.setState({ isLoading: true });
      const url = isAdminLogin ? '/user/university_user_login' : '/user/login_otp';
      axios
        .post(url, signIn)
        .then((res) => {
          if (res.data.status_code === 200) {
            if (!isAdminLogin) {
              NotificationManager.error('Resend OTP In');
              // store.addNotification({
              //   content: (
              //     <div className="success_notification">
              //       <Trans i18nKey={'resend_otp'}></Trans>
              //       <img
              //         src={require('../../Assets/elipsis.svg')}
              //         className="notification-item-img"
              //         alt="Enter Your OTP"
              //       />{' '}
              //     </div>
              //   ),
              //   container: 'top-right',
              //   animationIn: ['animated', 'fadeIn'],
              //   animationOut: ['animated', 'zoomOut'],
              //   dismiss: {
              //     duration: 200,
              //   },
              // });
              this.setState(
                {
                  id: res.data.data._id,
                  isLoading: false,
                  signInMessage: '',
                  emailVerifyTap: false,
                  otpVerificationTap: true,
                },
                () => {
                  this.timer();
                }
              );
            } else {
              this.props.onAuth(res.data.data, this.resetSteps);
            }
            if (this.otpRef.current !== null) {
              this.otpRef.current.focus();
            }
          } else {
            this.setState({
              signInMessage: res.data.message,
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          this.setState({
            signInMessage:
              (error.response && error.response.data.message) || 'Some internal error occurred',
            isLoading: false,
          });
        });
    }
  };

  handleResendOtp = (e) => {
    e.preventDefault();
    const signIn = {
      email: this.state.email,
      password: this.state.psw,
      otp_mode: this.state.otpType,
    };
    this.setState({ isLoading: true, minutes: 3, seconds: 0, otp: '', otpError: '' });
    apiInstance
      .post(`/user/login_otp`, signIn)
      .then((res) => {
        if (res.data.status_code === 200) {
          NotificationManager.success('OTP has Been Send');
          // store.addNotification({
          //   content: (
          //     <div className="success_notification">
          //       <Trans i18nKey={'OTP_has_Been_Send'}></Trans>
          //       <img
          //         src={require('../../Assets/elipsis.svg')}
          //         className="notification-item-img"
          //         alt={i18n.t('OTP_has_Been_Send')}
          //       />{' '}
          //     </div>
          //   ),
          //   container: 'top-right',
          //   animationIn: ['animated', 'fadeIn'],
          //   animationOut: ['animated', 'zoomOut'],
          //   dismiss: {
          //     duration: 5000,
          //   },
          // });
          this.setState(
            {
              isLoading: false,
              signInMessage: '',
              emailVerifyTap: false,
              otpVerificationTap: true,
            },
            () => {
              this.timer();
            }
          );
        } else {
          this.setState({
            signInMessage: res.data.message,
            isLoading: false,
          });
        }
      })
      .catch((error) => {
        this.setState({
          signInMessage: error.response.data.message,
          isLoading: false,
        });
      });
  };

  handleOtpSubmit = (e) => {
    e.preventDefault();
    if (this.otpValidation()) {
      const mobileNumber = {
        id: this.state.id,
        otp: this.state.otp,
      };
      this.setState({ isLoading: true, delayMsg: false });
      this.props.onAuth(mobileNumber, this.resetSteps);
    }
  };

  handleCheckMobile = (e) => {
    this.setState({
      checkBoxMobile: true,
      checkBoxEmail: false,
      otpType: 'mobile',
    });
  };

  handleCheckEmail = (e) => {
    this.setState({
      checkBoxEmail: true,
      checkBoxMobile: false,
      otpType: 'email',
    });
  };

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.success !== this.props.success && this.props.success != null) {
      NotificationManager.success('Login Successfully');
      // store.addNotification({
      //   content: (
      //     <div className="success_notification">
      //       <Trans i18nKey={'login_successfully'}></Trans>{' '}
      //     </div>
      //   ),
      //   container: 'top-right',
      //   animationIn: ['animated', 'fadeIn'],
      //   animationOut: ['animated', 'zoomOut'],
      //   dismiss: {
      //     duration: 5000,
      //   },
      // });

      this.setState({
        isLoading: false,
        signInMessage: '',
        emailVerifyTap: false,
        createPswTap: false,
        createMobileTap: true,
        resendOtp: true,
        signInShow: false,
        sidebarShow: true,
      });

      setTimeout(() => {
        this.setState({ delayMsg: true });
      }, 3000);
    } else if (prevProps.error !== this.props.error && this.props.error != null) {
      NotificationManager.error(this.props.error);
      // store.addNotification({
      //   content: <div className="success_notification">{this.props.error}</div>,
      //   container: 'top-right',
      //   animationIn: ['animated', 'fadeIn'],
      //   animationOut: ['animated', 'zoomOut'],
      //   dismiss: {
      //     duration: 5000,
      //   },
      // });
      this.setState({
        isLoading: false,
      });
    }
  }

  renderForgotPassword(isAdminLogin = false) {
    return (
      <Link exact="true" to="/forgot-password">
        <p
          className={`pt-${isAdminLogin ? '4' : '2'} mb-0 ${
            isAdminLogin ? 'text-center' : 'text-right'
          }`}
        >
          <Trans i18nKey={'forgot'}></Trans>
        </p>
      </Link>
    );
  }

  render() {
    const { minutes, seconds, passwordType } = this.state;
    const { isAdminLogin, isAdmin, isInstitutionOnBoarded } = this.props;
    let redirectPath = LocalStorageService.getCustomToken('redirectPath');
    let redirectUrl = null;
    if (this.props.isAuthenticated && this.state.delayMsg) {
      if (redirectPath !== '' && redirectPath !== null) {
        redirectUrl = <Redirect to={redirectPath} />;
      } else if (isAdmin && !isInstitutionOnBoarded) {
        redirectUrl = <Redirect to="/institution/onboarding" />;
      } else {
        redirectUrl = !isAdmin ? (
          <Redirect to="/overview" />
        ) : getActiveVersion() === '/v2' ? (
          <Redirect to="/university-Details" />
        ) : (
          <Redirect to="/overview" />
        );
      }
    }

    return (
      <div>
        <Loader isLoading={this.state.isLoading} />
        <div>
          {redirectUrl}
          <div className="login-bg">
            {this.state.signInShow === true && (
              <div className="container">
                <div className="row justify-content-center pt-3">
                  <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                    <h3 className="text-center">
                      {' '}
                      <img src={DS_logo} alt="Digischeduler" />
                    </h3>
                  </div>
                </div>

                <div className="row justify-content-center pt-3">
                  <div className="col-xl-4 col-lg-5 col-md-7 col-7">
                    <div className="outter-login">
                      {!isAdminLogin ? (
                        <div className="row">
                          <div className="col-xl-8 col-lg-8 col-md-7 col-7">
                            <p className="f-20 pt-3 text-skyblue">
                              {' '}
                              <Trans i18nKey={'signIn'}></Trans>{' '}
                            </p>
                          </div>
                          <div className="col-xl-4 col-lg-3 col-md-5 col-5">
                            <h3 className="text-center">
                              {' '}
                              {isDemoVer() || isIndVer() ? (
                                <img
                                  src={require('../../Assets/logo-D.jpg')}
                                  style={{ width: '83px' }}
                                  alt="Digi-scheduler"
                                />
                              ) : (
                                <img src={require('../../Assets/logo.png')} alt="Digi-scheduler" />
                              )}
                            </h3>{' '}
                          </div>
                        </div>
                      ) : (
                        <div>
                          <p className="f-20 pt-3 text-center text-gray text-uppercase">
                            <Trans i18nKey={'add_colleges.login'}></Trans>
                          </p>
                        </div>
                      )}

                      {/* 1 login Verification check start */}

                      {this.state.emailVerifyTap === true && (
                        <React.Fragment>
                          {this.state.signInMessage === '' ? (
                            ''
                          ) : (
                            <div className="bg-gray">
                              <p className="text-red text-center p-1">
                                <i className="fa fa-exclamation-circle" aria-hidden="true">
                                  {' '}
                                </i>{' '}
                                {this.state.signInMessage}
                              </p>
                            </div>
                          )}
                          <Form>
                            <div className="pt-2">
                              <Input
                                elementType={'input'}
                                elementConfig={{
                                  type: 'text',
                                  placeholder: i18n.t('enter_email'),
                                  ref: this.emailRef,
                                }}
                                value={this.state.email}
                                label={i18n.t('email')}
                                maxLength={50}
                                feedback={this.state.emailError}
                                changed={(e) => this.onChange(e, 'email')}
                                className={'form-control'}
                              />
                            </div>
                            <div className="pt-2 position-relative">
                              <Input
                                elementType={'input'}
                                elementConfig={{
                                  type: 'password',
                                  placeholder: i18n.t('enter_password'),
                                  ref: this.passwordRef,
                                }}
                                value={this.state.psw}
                                label={i18n.t('password')}
                                maxLength={30}
                                feedback={this.state.pswError}
                                changed={(e) => this.onChange(e, 'psw')}
                                className={'form-control'}
                              />
                              <span
                                className={`remove_hover ${
                                  getLang() === 'ar' ? 'p-viewer-ar' : 'p-viewer'
                                }`}
                              >
                                <i
                                  className={`fa fa-eye${passwordType === 'text' ? '-slash' : ''}`}
                                  aria-hidden="true"
                                  onClick={this.showPassword}
                                >
                                  {' '}
                                </i>
                              </span>
                              {!isAdminLogin && this.renderForgotPassword()}
                            </div>
                            {!isAdminLogin && (
                              <div className="pt-2">
                                <label>
                                  {' '}
                                  <Trans i18nKey={'sendotp'}></Trans>{' '}
                                </label>

                                <div className="otpcheck">
                                  <div className="row">
                                    <div
                                      className={`col-md-6 ${
                                        this.state.checkBoxMobile === true && `checkboxActive`
                                      }`}
                                      onClick={this.handleCheckMobile}
                                    >
                                      <p className="m-0 text-center clickCheckBox">
                                        <Trans i18nKey={'mobile'}></Trans>{' '}
                                      </p>
                                    </div>
                                    <div
                                      className={`col-md-6 ${
                                        this.state.checkBoxEmail === true && `checkboxActive`
                                      }`}
                                      onClick={this.handleCheckEmail}
                                    >
                                      <p className="m-0 text-center clickCheckBox">
                                        <Trans i18nKey={'email'}></Trans>{' '}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                            <div className="pt-3">
                              <Button
                                disabled={this.state.isLoading}
                                variant="primary"
                                size="lg"
                                block
                                onClick={this.handleSignIn}
                                className="f-14"
                                type="submit"
                              >
                                <Trans i18nKey={'signIn'}></Trans>
                              </Button>
                            </div>
                          </Form>
                          {isAdminLogin && this.renderForgotPassword(true)}
                        </React.Fragment>
                      )}
                      {/* 1 login check end */}

                      {/* 2 login Otp verification start */}

                      {this.state.otpVerificationTap === true && (
                        <React.Fragment>
                          {this.state.signInMessage === '' ? (
                            ''
                          ) : (
                            <div className="bg-gray">
                              <p className="text-red text-center p-1">
                                <i className="fa fa-exclamation-circle" aria-hidden="true">
                                  {' '}
                                </i>{' '}
                                {this.state.signInMessage}
                              </p>
                            </div>
                          )}

                          <React.Fragment>
                            <label>
                              {' '}
                              <Trans i18nKey={'otpsendto'}></Trans>
                            </label>

                            {this.state.otpType === 'mobile' ? (
                              <div className="bg-gray">
                                <p className=" p-2">
                                  {' '}
                                  <Trans i18nKey={'mobileCheck'}></Trans>{' '}
                                </p>
                              </div>
                            ) : (
                              <div className="bg-gray">
                                <p className="p-2">
                                  {' '}
                                  <Trans i18nKey={'emailId'}></Trans> : {this.state.email}
                                </p>
                              </div>
                            )}
                          </React.Fragment>
                          <Form>
                            <div className="pt-2">
                              <Input
                                elementType={'input'}
                                elementConfig={{
                                  type: 'text',
                                  placeholder: i18n.t('enter_otp'),
                                  ref: this.otpRef,
                                }}
                                value={this.state.otp}
                                label={i18n.t('enterotp')}
                                maxLength={4}
                                feedback={this.state.otpError}
                                changed={(e) => this.onChange(e, 'otp')}
                                className={'form-control'}
                              />
                            </div>

                            <div className="row pt-5">
                              <div className="col-md-12">
                                {minutes === 0 && seconds === 0 ? (
                                  ''
                                ) : (
                                  <h1 className="f-16">
                                    <Trans i18nKey={'resend_otp'}></Trans> : {minutes}:
                                    {seconds < 10 ? `0${seconds}` : seconds}
                                  </h1>
                                )}
                              </div>

                              {minutes === 0 && seconds === 0 ? (
                                <div className="col-md-6">
                                  <Button
                                    variant="primary"
                                    size="lg"
                                    block
                                    onClick={this.handleResendOtp}
                                    className="f-14"
                                  >
                                    <Trans i18nKey={'resend_otps'}></Trans>{' '}
                                  </Button>
                                </div>
                              ) : (
                                <div className="col-md-6">
                                  <React.Fragment>
                                    <Button
                                      variant="primary"
                                      size="lg"
                                      block
                                      className="f-14"
                                      disabled
                                    >
                                      <Trans i18nKey={'resend_otps'}></Trans>{' '}
                                    </Button>
                                  </React.Fragment>
                                </div>
                              )}

                              <div className="col-md-6">
                                <Button
                                  variant="primary"
                                  size="lg"
                                  block
                                  onClick={this.handleOtpSubmit}
                                  className="f-14"
                                  type="submit"
                                >
                                  <Trans i18nKey={'done'}></Trans>{' '}
                                </Button>
                              </div>
                            </div>
                          </Form>
                        </React.Fragment>
                      )}

                      {/* 2 login Otp verification end */}
                    </div>
                  </div>
                </div>
                <div className="row justify-content-center pt-4 pb-2">
                  <p className="text-center text-white ">
                    &copy; {new Date().getFullYear()},{' '}
                    <a href="https://digi-val.com/" target="blank" className="text-white">
                      <Trans i18nKey={'login_footer'}></Trans>
                    </a>
                    .<Trans i18nKey={'alright_reserved'}></Trans>.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
}

Login.propTypes = {
  isAdminLogin: PropTypes.bool,
  isAdmin: PropTypes.bool,
  isInstitutionOnBoarded: PropTypes.bool,
  t: PropTypes.func,
  isAuthenticated: PropTypes.bool,
  history: PropTypes.object,
  success: PropTypes.string,
  error: PropTypes.string,
  onAuth: PropTypes.func,
};

Login.defaultProps = {
  isAdminLogin: false,
};

const mapStateToProps = (state) => {
  return {
    error: state.auth.error,
    success: state.auth.success,
    isAuthenticated: state.auth.token !== null,
    isAdmin: state.auth.isAdmin,
    isInstitutionOnBoarded: state.auth.isInstitutionOnboarded,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    onAuth: (data, callBack) => dispatch(actions.auth(data, callBack)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(withTranslation()(Login)));
