import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import axios from '../../../axios';
import Loader from '../../../Widgets/Loader/Loader';
import Input from '../../../Widgets/FormElements/Input/Input';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import Tooltips from '../../../_components/UI/Tooltip/Tooltip';
import {
  getLang,
  maxDateSet,
  ucFirst,
  isModuleEnabled,
  capitalize,
  lastNameRequired,
  addDefaultLastName,
} from '../../../utils';
import { Accordion, Card, Button, Modal } from 'react-bootstrap';
import ProfileText from '../../../Widgets/ProfileText';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import Select from 'react-select';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import VaccinationDetails from '../../../Modules/UserManagement/VaccinationDetails';
import { Trans } from 'react-i18next';
import { clickArrow } from '../../utils';
import { t } from 'i18next';
import { isIndVer } from 'utils';
import withCountryCodeHooks from 'Hoc/withCountryCodeHooks';
import ContactDetails from './ContactDetails';
import LocalStorageService from 'LocalStorageService';
// import { envSignUpService } from '../../utils';

const gender = [
  ['Male', 'male'],
  ['Female', 'female'],
];
const contactList = [
  ['Parent', 'parent'],
  ['Guardian', 'guardian'],
  ['Spouse', 'spouse'],
];
let id;
// const contactDetail = {
//   email: '',
//   mobile: '',
//   name: '',
// };

const contactDetailError = { emailError: '', nameError: '', mobileError: '' };
// let pageNum;
class validStudentView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      first: '',
      last: '',
      middle: '',
      family: '',
      empId: '',
      _nationality_id: [],
      _nationality_name: '',
      buildingNo: '',
      nationalId: '',
      city: '',
      distric: '',
      zipCode: '',
      unit: '',
      passport_no: '',
      username: '',
      email: '',
      mobile: '',
      office_extension: '',
      _nationality_id_doc: '',
      _address_doc: '',
      _college_id_doc: '',
      _school_certificate_doc: '',
      _entrance_exam_certificate_doc: '',
      _admission_order_doc: '',
      edit: false,
      selectGender: gender[0][1][2],
      firstError: '',
      middleError: '',
      lastError: '',
      empIdError: '',
      nationalIdError: '',
      selectGenderError: '',
      program_no: '',
      enrollment_year: '',
      academic: '',
      dob: '',
      mobileView: false,
      minutes: 3,
      seconds: 0,
      resendOtp: false,
      reason: '',
      otp: '',
      confirmMobile: false,
      uploadedDoc: [],
      imageUpdateShow: false,
      updatedFile: '',
      programData: [],
      countries: [],
      uploadedImageError: '',
      contact: [],
      relation_type: '',
      selectedBatch: '',
      active: false,
      userStatus: '',
      tabs: 0,
      selectedIndex: '',
      selectContact: '',
      contactsError: [{ ...contactDetailError }, { ...contactDetailError }],
    };
  }
  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');
    let tab = params.get('tabs') || params.get('tab');
    this.setState({ tabs: tab });
    this.fetchApi(id, () => {
      const userSensitive = isModuleEnabled('USER_SENSITIVE');
      if (!userSensitive) {
        this.setState({
          buildingNo: 'Not Applicable',
          city: 'Not Applicable',
          distric: 'Not Applicable',
          zipCode: '111',
          unit: '1',
          chooseNational: true,
          selectNational: 'correct',
        });
      }
    });
  }

  componentWillUnmount() {
    clearInterval(this.myInterval);
  }

  timer = () => {
    this.myInterval = setInterval(() => {
      const { seconds, minutes } = this.state;
      if (seconds > 0) {
        this.setState(({ seconds }) => ({
          seconds: seconds - 1,
        }));
      }
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(this.myInterval);
        } else {
          this.setState(({ minutes }) => ({
            minutes: minutes - 1,
            seconds: 59,
          }));
        }
      }
    }, 1000);
  };

  getBatch = () => {
    const { programData, program_no } = this.state;
    if (programData && programData.length > 0 && program_no !== 'select') {
      const terms = programData
        .filter((item) => item.value === program_no)
        .reduce((_, el) => el.term, [])
        .map((item) => {
          return { name: item.term_name, value: item.term_name };
        });
      terms.unshift({
        name: `Select ${isIndVer() ? 'Intake' : 'Batch'}`,
        value: 'select',
      });

      return terms;
    }
    return [];
  };

  callCountry = () => {
    const countries = LocalStorageService.getCustomToken('countries', true);
    if (!countries) {
      axios
        .get(`country?limit=300&pageNo=1`)
        .then((res) => {
          const duplicateAr = res.data.data.filter(
            (v, i, a) => a.findIndex((t) => t.name === v.name) === i
          );
          const countries = duplicateAr.map((data) => {
            return {
              label: data.name,
              value: data.name,
              id: data._id,
            };
          });
          this.setState({
            countries: countries,
            isLoading: false,
          });
          LocalStorageService.setCustomToken('countries', JSON.stringify(countries));
        })
        .catch((error) => {
          this.setState({
            countries: [],
            isLoading: false,
          });
        });
    } else {
      this.setState({
        countries,
        isLoading: false,
      });
    }
  };

  callProgram = () => {
    axios
      .get(`/digi_program?limit=200&pageNo=1`)
      .then((res) => {
        const data =
          res.data.data &&
          res.data.data.length > 0 &&
          res.data.data
            .filter((item) => item.isActive === true && item.isDeleted === false)
            .map((data) => {
              return {
                name: data.name,
                value: data.code,
                term: data.term,
              };
            });
        this.setState({
          programData: data,
          // selectedProgram: data[0].value,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          programData: [],
          isLoading: false,
        });
      });
  };
  fetchApi = (id, callBack) => {
    this.setState({
      isLoading: true,
    });
    const { replaceCountryCode } = this.props;
    this.callCountry();
    axios
      .get(`/user/student/${id}`)
      .then((res) => {
        const data = res.data.data;
        let country = [];
        if (data.address._nationality_id !== null) {
          country = [
            {
              label: data.address._nationality_id,
              value: data.address._nationality_id,
            },
          ];
        }
        let contact = [];
        if (data.contact.length !== 0) {
          if (
            data.contact[0].relation_type === 'guardian' ||
            data.contact[0].relation_type === 'spouse'
          ) {
            contact = data.contact;
          } else {
            var addedRelationType = [];
            contact =
              data.contact &&
              data.contact.length > 0 &&
              data.contact.map((cont) => {
                if (cont.relation_type !== undefined) {
                  addedRelationType.push(cont.relation_type);
                }
                return {
                  email: cont.email !== undefined ? cont.email : '',
                  mobile: cont.mobile !== undefined ? cont.mobile : '',
                  name: cont.name !== undefined ? cont.name : '',
                  relation_type:
                    cont.relation_type !== undefined
                      ? cont.relation_type
                      : addedRelationType.includes('father')
                      ? 'mother'
                      : 'father',
                  _id: cont._id !== undefined ? cont._id : '',
                };
              });
          }
          contact = contact.map((item) => {
            return { ...item, mobile: item.mobile !== '' ? replaceCountryCode(item.mobile) : '' };
          });
        } else {
          contact = data.contact;
        }

        this.setState(
          {
            data: data,
            first: data.name.first,
            last: data.name.last,
            middle: data.name.middle,
            family: data.name.family,
            academic: data.academic,
            nationalId: data.address.nationality_id,
            _nationality_id: country,
            _nationality_name: data.address._nationality_id,
            buildingNo: data.address.building,
            city: data.address.city,
            distric: data.address.district,
            zipCode: data.address.zip_code,
            unit: data.address.unit,
            passport_no: data.address.passport_no,
            program_no: data.program_no,
            enrollment_year: data.enrollment_year,
            email: data.email,
            selectContact:
              data.contact.length === 0
                ? ''
                : ['father', 'mother'].includes(data.contact[0]?.relation_type)
                ? 'parent'
                : data.contact[0].relation_type,
            mobile: data.mobile !== '' ? replaceCountryCode(data.mobile) : '',
            selectGender: data.gender,
            dob: data.dob,
            contact: data.contact.length > 0 ? contact : [],
            uploadedDoc: [
              {
                name: undefined,
              },
              {
                image:
                  data.student_docs._school_certificate_doc !== undefined &&
                  data.student_docs._school_certificate_doc !== ''
                    ? data.student_docs._school_certificate_doc
                    : '',
                name: t('user_management.school_certificate'),
                isOpen: false,
                imageOpen: false,
                imgName:
                  data.student_docs._school_certificate_doc !== undefined &&
                  data.student_docs._school_certificate_doc !== ''
                    ? data.student_docs._school_certificate_doc.split('-').pop()
                    : '',
              },
              {
                image:
                  data.student_docs._entrance_exam_certificate_doc !== undefined &&
                  data.student_docs._entrance_exam_certificate_doc !== ''
                    ? data.student_docs._entrance_exam_certificate_doc
                    : '',
                name: t('user_management.entrance_exam_certificate'),
                isOpen: false,
                imageOpen: false,
                imgName:
                  data.student_docs._entrance_exam_certificate_doc !== undefined &&
                  data.student_docs._entrance_exam_certificate_doc !== ''
                    ? data.student_docs._entrance_exam_certificate_doc.split('-').pop()
                    : '',
              },
              {
                image:
                  data.enrollment._admission_order_doc !== undefined &&
                  data.enrollment._admission_order_doc !== ''
                    ? data.enrollment._admission_order_doc
                    : '',
                name: t('user_management.admission_documents'),
                isOpen: false,
                imageOpen: false,
                imgName:
                  data.enrollment._admission_order_doc !== undefined &&
                  data.enrollment._admission_order_doc !== ''
                    ? data.enrollment._admission_order_doc.split('-').pop()
                    : '',
              },
              {
                image:
                  data.id_doc._college_id_doc !== undefined && data.id_doc._college_id_doc !== ''
                    ? data.id_doc._college_id_doc
                    : '',
                name: t('user_management.college_id'),
                isOpen: false,
                imageOpen: false,
                imgName:
                  data.id_doc._college_id_doc !== undefined && data.id_doc._college_id_doc !== ''
                    ? data.id_doc._college_id_doc.split('-').pop()
                    : '',
              },
              {
                image:
                  data.address._nationality_id_doc !== undefined &&
                  data.address._nationality_id_doc !== ''
                    ? data.address._nationality_id_doc
                    : '',
                name: t('user_management.national_resident_id'),
                isOpen: false,
                imageOpen: false,
                imgName:
                  data.address._nationality_id_doc !== undefined &&
                  data.address._nationality_id_doc !== ''
                    ? data.address._nationality_id_doc.split('-').pop()
                    : '',
              },
              {
                image:
                  data.address._address_doc !== undefined && data.address._address_doc !== ''
                    ? data.address._address_doc
                    : '',
                name: t('user_management.national_address'),
                isOpen: false,
                imageOpen: false,
                imgName:
                  data.address._address_doc !== undefined && data.address._address_doc !== ''
                    ? data.address._address_doc.split('-').pop()
                    : '',
              },
            ],
            correction_first_name: data.correction_first_name === true ? 'Correction Required' : '',
            correction_middle_name:
              data.correction_middle_name === true ? 'Correction Required' : '',
            correction_last_name: data.correction_last_name === true ? 'Correction Required' : '',
            correction_gender: data.correction_gender === true ? 'Correction Required' : '',
            correction_academic_no:
              data.correction_academic_no === true ? 'Correction Required' : '',
            correction_nationality_id:
              data.correction_nationality_id === true ? 'Correction Required' : '',
            correction_program_no: data.correction_program_no === true ? 'Correction Required' : '',
            isLoading: false,
            selectedBatch: data.batch,
            userStatus: data.status,
          },
          () => {
            callBack && callBack();
          }
        );
        this.callProgram();
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };
  handleEdit = () => {
    this.setState({
      edit: true,
    });
  };

  handleChangeText = (e, name) => {
    if (name === 'first') {
      this.setState({
        first: e.target.value,
        firstError: '',
      });
    }

    if (name === 'middle') {
      this.setState({
        middle: e.target.value,
        middleError: '',
      });
    }
    if (name === 'last') {
      this.setState({
        last: e.target.value,
        lastError: '',
      });
    }

    if (name === 'empId') {
      this.setState({
        empId: e.target.value,
        empIdError: '',
      });
    }
    if (name === 'nationalId') {
      this.setState({
        nationalId: e.target.value,
        nationalIdError: '',
      });
    }
    if (name === 'family') {
      this.setState({
        family: e.target.value,
        familyError: '',
      });
    }

    if (name === 'academic') {
      this.setState({
        academic: e.target.value,
        academicError: '',
      });
    }

    if (name === 'passport') {
      this.setState({
        passport_no: e.target.value,
        passportError: '',
      });
    }
    if (name === 'program_no') {
      this.setState({
        program_no: e.target.value,
        program_noError: '',
      });
    }

    if (name === 'dob') {
      this.setState({
        dob: e,
        dobError: '',
      });
    }

    if (name === 'buildingNo') {
      this.setState({
        buildingNo: e.target.value,
        buildingNoError: '',
      });
    }
    if (name === 'street') {
      this.setState({
        street: e.target.value,
        streetError: '',
      });
    }
    if (name === 'city') {
      this.setState({
        city: e.target.value,
        cityError: '',
      });
    }
    if (name === 'distric') {
      this.setState({
        distric: e.target.value,
        districError: '',
      });
    }

    if (name === 'zipCode') {
      if (isNaN(e.target.value)) return;
      this.setState({
        zipCode: e.target.value,
        zipCodeError: '',
      });
    }

    if (name === 'unit') {
      if (isNaN(e.target.value)) return;
      this.setState({
        unit: e.target.value,
        unitError: '',
      });
    }

    if (name === 'mobiles') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mobile: e.target.value,
        mobileError: '',
      });
    }

    if (name === 'reason') {
      this.setState({
        reason: e.target.value,
        reasonError: '',
      });
    }
    if (name === 'otp') {
      if (isNaN(e.target.value)) return;
      this.setState({
        otp: e.target.value,
        otpError: '',
      });
    }

    if (name === 'faName') {
      this.setState({
        faName: e.target.value,
        faNameError: '',
      });
    }

    if (name === 'maName') {
      this.setState({
        maName: e.target.value,
        maNameError: '',
      });
    }

    if (name === 'fEmail') {
      this.setState({
        fEmail: e.target.value,
        fEmailError: '',
      });
    }

    if (name === 'mEmail') {
      this.setState({
        mEmail: e.target.value,
        mEmailError: '',
      });
    }

    if (name === 'fPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        fPhone: e.target.value,
        fPhoneError: '',
      });
    }

    if (name === 'mPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mPhone: e.target.value,
        mPhoneError: '',
      });
    }

    if (name === 'gName') {
      this.setState({
        gName: e.target.value,
        gNameError: '',
      });
    }
    if (name === 'gPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        gPhone: e.target.value,
        gPhoneError: '',
      });
    }
    if (name === 'gEmail') {
      this.setState({
        gEmail: e.target.value,
        gEmailError: '',
      });
    }
    if (name === 'studendRelation') {
      this.setState({
        studendRelation: e.target.value,
        studendRelationError: '',
      });
    }

    if (name === 'sName') {
      this.setState({
        sName: e.target.value,
        sNameError: '',
      });
    }
    if (name === 'sPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        sPhone: e.target.value,
        sPhoneError: '',
      });
    }
    if (name === 'sEmail') {
      this.setState({
        sEmail: e.target.value,
        sEmailError: '',
      });
    }
  };

  validation = () => {
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    const AlphaNum = /^[a-zA-Z0-9]+$/;
    const Number = /^[0-9]+$/;
    const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    let firstError = '';
    let middleError = '';
    let lastError = '';
    let nationalIdError = '';
    let selectGenderError = '';
    let academicError = '';
    let dobError = '';
    let passportError = '';
    let program_noError = '';
    let mobileError = '';
    let buildingNoError = '';
    let cityError = '';
    let districError = '';
    let zipCodeError = '';
    let unitError = '';
    let selectedBatchError = '';
    let contactsError = [{ ...contactDetailError }, { ...contactDetailError }];
    const { mobileLengthMatch, countryCodeLength } = this.props;

    if (this.state.selectGender === 'l') {
      selectGenderError = 'Choose Gender';
    }
    if (!this.state.selectGender) {
      selectGenderError = 'Choose Gender';
    }

    if (this.state.selectContact !== '') {
      const contactName = this.state.selectContact;
      const fatherFieldIsEmpty =
        this.state.contact[0].name === '' ||
        this.state.contact[0].email === '' ||
        this.state.contact[0].mobile === '';

      const motherFieldIsNotEmpty =
        this.state.contact[1]?.name !== '' ||
        this.state.contact[1]?.email !== '' ||
        this.state.contact[1]?.mobile !== '';
      const fatherFieldIsNotEmpty =
        this.state.contact[0].name !== '' ||
        this.state.contact[0].email !== '' ||
        this.state.contact[0].mobile !== '';
      const validateFather =
        motherFieldIsNotEmpty && fatherFieldIsNotEmpty
          ? true
          : motherFieldIsNotEmpty
          ? false
          : fatherFieldIsEmpty
          ? true
          : fatherFieldIsNotEmpty;

      const contactIndex = 0;

      if (this.state.selectContact === 'parent' ? validateFather : true) {
        if (
          this.state.contact[contactIndex].email !== '' &&
          this.state.contact[contactIndex].email !== undefined &&
          !emailRegex.test(this.state.contact[contactIndex].email)
        ) {
          contactsError[contactIndex].emailError = 'Enter The Valid Email ID';
        }
        if (!this.state.contact[contactIndex].mobile) {
          contactsError[contactIndex].mobileError = `${
            contactName !== 'parent' ? capitalize(contactName) : 'Father'
          } Contact Number Is Required`;
        } else if (!mobileLengthMatch(this.state.contact[contactIndex].mobile)) {
          contactsError[
            contactIndex
          ].mobileError = `Enter The Valid ${countryCodeLength} Digit Number`;
        }

        if (!this.state.contact[contactIndex].name) {
          contactsError[contactIndex].nameError = `${
            contactName !== 'parent' ? capitalize(contactName) : 'Father'
          } Name Is Required`;
        }
      }
      if (this.state.selectContact === 'parent') {
        if (motherFieldIsNotEmpty) {
          if (
            this.state.contact[1].email !== '' &&
            this.state.contact[1].email !== undefined &&
            !emailRegex.test(this.state.contact[1].email)
          ) {
            contactsError[1].emailError = 'Enter The Valid Email ID';
          }
          if (!this.state.contact[1].mobile) {
            contactsError[1].mobileError = `Mother Contact Number Is Required`;
          } else if (!mobileLengthMatch(this.state.contact[1].mobile)) {
            contactsError[1].mobileError = `Enter The Valid ${countryCodeLength} Digit Number`;
          }

          if (!this.state.contact[1].name) {
            contactsError[1].nameError = `Mother Name Is Required`;
          }
        }
      }
    }

    const userDOBValidation = isModuleEnabled('USER_DOB');
    if (userDOBValidation) {
      if (this.state.dob === '' || this.state.dob === null) {
        dobError = 'DOB Field is Required';
      }
    }

    if (!this.state.mobile) {
      mobileError = 'Mobile no is Required';
    } else if (!space.test(this.state.mobile)) {
      mobileError = 'Space not allowed beginning & end';
    } else if (!mobileLengthMatch(this.state.mobile)) {
      mobileError = `Should be ${countryCodeLength} character is required`;
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Number Only Allow';
    }

    if (!this.state.academic) {
      academicError = 'Academic ID is Required';
    }
    if (!space.test(this.state.academic)) {
      academicError = 'Space not allowed beginning & end';
    } else if (!AlphaNum.test(this.state.academic)) {
      academicError = 'Special character are not allowed';
    } else if (this.state.academic.length <= 0) {
      academicError = 'Minimum 1 character is required ';
    }

    if (!this.state.program_no) {
      program_noError = 'Program Number is Required';
    }
    // if (!space.test(this.state.program_no)) {
    //   program_noError = "Space not allowed beginning & end";
    // } else if (!AlphaNum.test(this.state.program_no)) {
    //   program_noError = "Text Only allowed";
    // } else if (this.state.program_no.length <= 2) {
    //   program_noError = "Minimum 3 character is required ";
    // }

    if (!this.state.first) {
      firstError = 'First Name is Required';
    }
    if (!space.test(this.state.first)) {
      firstError = 'Space not allowed beginning & end';
    }
    //  else if (!spaceAlpha.test(this.state.first)) {
    //   firstError = 'Text Only allowed';
    // }
    else if (this.state.first.length <= 2) {
      firstError = 'Minimum 3 character is required ';
    }

    //   if (this.state.middle) {
    //     if (!space.test(this.state.middle)) {
    //     middleError = "Space not allowed beginning & end";
    //   } else if (!spaceAlpha.test(this.state.middle)) {
    //     middleError = "Text Only allowed";
    //   } else if (this.state.middle.length <= 2) {
    //     middleError = "Minimum 3 character is required ";
    //   }
    // }

    if (!lastNameRequired()) {
      if (!this.state.last) {
        lastError = t('user_management.last_name_required');
      } else if (!space.test(this.state.last)) {
        lastError = t('user_management.space_not_allowed');
      }
      // else if (!numbers.test(this.state.lName)) {
      //   lastError = "Numbers not allowed";
      // }
      // else if (this.state.lName.length <= 2) {
      //   lastError = t('user_management.minimum_3_char_required');
      // }
    }

    if (isModuleEnabled('NATIONALITY_ID')) {
      if (!this.state.nationalId) {
        nationalIdError = 'National/Residence ID is Required';
      } else if (!space.test(this.state.nationalId)) {
        nationalIdError = 'Space not allowed beginning & end';
      } else if (!AlphaNum.test(this.state.nationalId)) {
        nationalIdError = 'Special character are not allowed';
      } else if (this.state.nationalId.length <= 4) {
        nationalIdError = 'Minimum 5 character is required ';
      }
    }
    if (this.state.passport_no) {
      if (!space.test(this.state.passport_no)) {
        passportError = 'Space not allowed beginning & end';
      } else if (!AlphaNum.test(this.state.passport_no)) {
        passportError = 'Special Charcter not Alowed';
      } else if (this.state.passport_no.length <= 2) {
        passportError = 'Minimum 3 character is required ';
      }
    }

    if (!this.state.buildingNo) {
      buildingNoError = 'Building No, Street Name Field is Required';
    } else if (!space.test(this.state.buildingNo)) {
      buildingNoError = 'Space not allowed beginning & end';
    } else if (this.state.buildingNo.length <= 2) {
      buildingNoError = 'Minimum 3 character is required ';
    }

    if (!this.state.city) {
      cityError = 'City Name Field is Required';
    } else if (!space.test(this.state.city)) {
      cityError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.city)) {
      cityError = 'Text Only allowed';
    } else if (this.state.city.length <= 2) {
      cityError = 'Minimum 3 character is required ';
    }

    if (!this.state.distric) {
      districError = 'District Name Field is Required';
    } else if (!space.test(this.state.distric)) {
      districError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.distric)) {
      districError = 'Text Only allowed';
    } else if (this.state.distric.length <= 2) {
      districError = 'Minimum 3 character is required ';
    }

    if (!this.state.zipCode) {
      zipCodeError = 'Zip Code Field is Required';
    } else if (!space.test(this.state.zipCode)) {
      zipCodeError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.zipCode)) {
      zipCodeError = 'Numeric Only allowed';
    } else if (this.state.zipCode.length <= 2) {
      zipCodeError = 'Minimum 3 character is required ';
    } else if (parseInt(this.state.zipCode) > 1000000) {
      zipCodeError = 'Pls Enter 1000000 Lesser than value ';
    } else if (parseInt(this.state.zipCode) === 0) {
      zipCodeError = 'Pls Enter 0 Greater than value ';
    }

    if (!this.state.unit) {
      unitError = 'Floor Number Field is Required';
    } else if (!space.test(this.state.unit)) {
      unitError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.unit)) {
      unitError = 'Numeric Only allowed';
    } else if (parseInt(this.state.unit) > 50) {
      unitError = 'Pls Enter 50 Lesser than value ';
    } else if (parseInt(this.state.unit) === 0) {
      unitError = 'Pls Enter 0 Greater than value ';
    }

    if (this.state.selectedBatch === 'select') {
      selectedBatchError = `${isIndVer() ? 'Intake' : 'Batch'} is Required`;
    }

    if (
      firstError ||
      middleError ||
      lastError ||
      nationalIdError ||
      selectGenderError ||
      academicError ||
      passportError ||
      program_noError ||
      mobileError ||
      buildingNoError ||
      cityError ||
      districError ||
      zipCodeError ||
      unitError ||
      dobError ||
      selectedBatchError ||
      contactsError[0].emailError ||
      contactsError[0].mobileError ||
      contactsError[0].nameError ||
      contactsError[1].emailError ||
      contactsError[1].mobileError ||
      contactsError[1].nameError
    ) {
      this.setState({
        firstError,
        middleError,
        lastError,
        nationalIdError,
        selectGenderError,
        academicError,
        passportError,
        program_noError,
        mobileError,
        buildingNoError,
        cityError,
        districError,
        zipCodeError,
        unitError,
        dobError,
        selectedBatchError,
        contactsError,
      });
      return false;
    }
    return true;
  };

  handleSingleSubmit = (e) => {
    e.preventDefault();

    let selectedCountry;
    const { replaceCountryCode } = this.props;
    if (this.state._nationality_id !== null) {
      let countries = this.state.countries.filter((data) => {
        return data.label === this.state._nationality_id.label;
      });

      let defaultCountry = this.state.countries.find(
        (o) => o.label === this.state._nationality_name
      );

      if (countries[0] !== undefined) {
        selectedCountry = countries[0].id;
      } else if (defaultCountry !== undefined) {
        selectedCountry = defaultCountry.id;
      } else {
        selectedCountry = '';
      }
    } else {
      selectedCountry = '';
    }

    let contact = this.state.contact;
    let contacts;

    if (contact[0] !== undefined) {
      if (contact[0].relation_type === 'guardian') {
        contacts = {
          type: 'guardian',
          relation:
            contact[0].relation !== '' && contact[0].relation !== undefined
              ? contact[0].relation.trim()
              : '',
          guardian_name:
            contact[0].name !== '' && contact[0].name !== undefined ? contact[0].name.trim() : '',
          guardian_mobile:
            contact[0].mobile !== '' && contact[0].mobile !== undefined
              ? replaceCountryCode(contact[0].mobile)
              : '',
          guardian_email:
            contact[0].email !== '' && contact[0].email !== undefined
              ? contact[0].email.trim()
              : '',
        };
      } else if (contact[0].relation_type === 'spouse') {
        contacts = {
          type: 'spouse',
          spouse_name:
            contact[0].name !== '' && contact[0].name !== undefined ? contact[0].name.trim() : '',
          spouse_mobile:
            contact[0].mobile !== '' && contact[0].mobile !== undefined
              ? replaceCountryCode(contact[0].mobile)
              : '',
          spouse_email:
            contact[0].email !== '' && contact[0].email !== undefined
              ? contact[0].email.trim()
              : '',
        };
      } else {
        contacts = {
          type: 'parent',
          father_name:
            contact[0]?.name !== undefined && contact[0]?.name !== ''
              ? contact[0]?.name.trim()
              : '',
          father_mobile:
            contact[0].mobile !== undefined && contact[0].mobile !== ''
              ? replaceCountryCode(contact[0].mobile)
              : '',
          father_email:
            contact[0]?.email !== undefined && contact[0]?.email !== ''
              ? contact[0]?.email.trim()
              : '',
          mother_name:
            contact[1]?.name !== undefined && contact[1]?.name !== ''
              ? contact[1]?.name.trim()
              : '',
          mother_mobile:
            contact[1].mobile !== undefined && contact[1].mobile !== ''
              ? replaceCountryCode(contact[1].mobile)
              : '',
          mother_email:
            contact[1]?.email !== undefined && contact[1]?.email !== ''
              ? contact[1]?.email.trim()
              : '',
        };
      }
    }

    // return;

    this.setState({
      isValidate: false,
    });
    if (this.validation()) {
      const data = {
        id: id,
        academic_no: this.state.academic,
        first_name: this.state.first,
        last_name: addDefaultLastName(this.state.last),
        middle_name: this.state.middle,
        gender: this.state.selectGender,
        nationality_id: this.state.nationalId,
        _nationality_id: selectedCountry,
        mobile: this.state.mobile,
        building: this.state.buildingNo,
        city: this.state.city,
        district: this.state.distric,
        zip_code: this.state.zipCode,
        unit: this.state.unit,
        passport_no: this.state.passport_no,
        family_name: this.state.family,
        program_no: this.state.program_no,
        contact: contacts,
        batch: this.state.selectedBatch,
      };

      if (this.state.dob !== '' && this.state.dob !== null) {
        data.dob = moment(this.state.dob).format('YYYY-MM-DD');
      } else {
        data.dob = '';
      }

      this.setState({
        isLoading: true,
      });
      axios
        .put(`/user/user_edit`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              edit: false,
            },
            () => {
              this.fetchApi(id);
            }
          );
          NotificationManager.success(t('user_management.Profile_Edited_Successfully'));
        })
        .catch((error) => {
          let errorMessage = 'Validation Failed';
          if (error.response.data.data !== undefined) {
            errorMessage = error.response.data.data;
            if (typeof errorMessage === 'object') {
              const field = errorMessage.field?.user_id;
              errorMessage = errorMessage.message;
              if (field !== undefined) {
                errorMessage = errorMessage + ' - ' + field;
              }
            }
          }
          NotificationManager.error(`${errorMessage}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  onRadioGroupChange = (e, name) => {
    const inputValue = e.target.value;
    if (name === 'selectGender') {
      this.setState({
        selectGender: inputValue,
        selectGenderError: '',
      });
    }
    if (name === 'contact') {
      let contacts = [];

      const contactDetail = {
        email: '',
        mobile: '',
        name: '',
      };
      if (inputValue === 'parent') {
        contacts = [
          {
            ...contactDetail,
            relation_type: 'father',
          },
          {
            ...contactDetail,
            relation_type: 'mother',
          },
        ];
      } else {
        contacts = [
          {
            ...contactDetail,
            relation_type: inputValue,
          },
        ];
      }

      this.setState({
        selectContact: inputValue,
        contactError: '',
        contact: contacts,
        contactsError: [{ ...contactDetailError }, { ...contactDetailError }],
      });
    }
  };

  mobileVerify = () => {
    this.setState({
      mobileView: true,
    });
  };

  mobileVerifyClose = () => {
    this.setState({
      mobileView: false,
      resendOtp: false,
      minutes: 3,
      seconds: 0,
      reason: '',
      otp: '',
    });
  };

  mobileValidation = () => {
    const Number = /^[0-9]+$/;
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    let mobileError = '';
    let reasonError = '';
    const { countryCodeLength, mobileLengthMatch } = this.props;
    if (!this.state.mobile) {
      mobileError = 'Mobile No is Required';
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Mobile No Numbers Only Allow';
    } else if (!mobileLengthMatch(this.state.mobile)) {
      mobileError = `Mobile No ${countryCodeLength} character is required`;
    }

    if (this.state.reason === '') {
      reasonError = 'Reason Field is Required';
    }
    if (!space.test(this.state.reason)) {
      reasonError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.reason)) {
      reasonError = 'Text Only allowed';
    } else if (this.state.reason.length <= 2) {
      reasonError = 'Minimum 3 character is required ';
    }

    if (mobileError || reasonError) {
      this.setState({
        mobileError,
        reasonError,
      });
      return false;
    }
    return true;
  };

  otpValidation = () => {
    let otpError = '';
    const Number = /^[0-9]+$/;

    if (!this.state.otp) {
      otpError = 'OTP is Required';
    } else if (this.state.otp.length <= 3) {
      otpError = 'Minimum 4 character is required ';
    } else if (!Number.test(this.state.otp)) {
      otpError = 'Numbers Only Allow ';
    }

    if (otpError) {
      this.setState({
        otpError,
      });
      return false;
    }
    return true;
  };

  sendOTP = (e) => {
    e.preventDefault();
    const { showWithCountryCode } = this.props;
    if (this.mobileValidation()) {
      const data = {
        id: id,
        mobile: showWithCountryCode(this.state.mobile),
      };
      this.setState({
        isLoading: true,
        minutes: 3,
        seconds: 0,
      });
      axios
        .post(`/user/send_mobile_otp`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              resendOtp: true,
            },
            () => {
              this.timer();
            }
          );

          NotificationManager.success(t(`user_management.OTP_has_Been_Send`));
        })
        .catch((error) => {
          // let errorMessage='';
          NotificationManager.error(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  handleMobileSubmit = (e) => {
    e.preventDefault();
    // id = this.props.location.search.split("=")[1];
    if (this.otpValidation()) {
      const { showWithCountryCode } = this.props;
      const data = {
        id: id,
        mobile: showWithCountryCode(this.state.mobile),
        reason: this.state.reason,
        otp: this.state.otp,
        admin_id: id,
      };

      this.setState({
        isLoading: true,
      });
      axios
        .post(`/user/change_mobile`, data)
        .then((res) => {
          this.setState({
            isLoading: false,
            resendOtp: false,
            confirmMobile: true,
            mobileView: false,
            otp: '',
            reason: '',
            minutes: 3,
            seconds: 0,
          });

          NotificationManager.success(t(`user_management.Phone_Number_Updated_Successfully`));
        })
        .catch((error) => {
          // let errorMessage='';
          NotificationManager.error(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  handleClickOpen = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = true;
    this.setState({
      uploadedDoc,
    });
  };

  handleClickClose = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = false;
    this.setState({
      uploadedDoc,
    });
  };

  imageUpdateOpen = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].imageOpen = true;
    this.setState({
      uploadedDoc,
      uploadedImageError: '',
    });
  };
  imageUpdateClose = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].imageOpen = false;
    this.setState({
      uploadedDoc,
      updatedFile: '',
      uploadedImageError: '',
    });
  };

  handleImageChange(e) {
    e.preventDefault();

    let reader = new FileReader();

    let file = e.target.files[0];

    if (file === null || file === undefined) {
      return;
    }

    const size = Math.ceil(parseFloat(file.size / 1024));
    if (size > 6000) {
      this.setState({
        uploadedImageError: 'Image size should not be greater than 6 mb',
      });
      return;
    }

    const mimeType = file.type;
    if (mimeType.substr(0, 5) !== 'image') {
      this.setState({
        uploadedImageError: 'Image files only allowed',
      });
      return;
    }
    if (file !== null && file !== undefined) {
      // NotificationManager.success("Image added");
      reader.onloadend = () => {
        this.setState({
          updatedFile: file,
          uploadedImageError: ' ',
        });
      };
      reader.readAsDataURL(file);
    }
  }
  handleImageUpdate = (e, index) => {
    e.preventDefault();

    let data = new FormData();
    data.append('id', id);
    if (index === 1) {
      data.append('_school_certificate_doc', this.state.updatedFile);
    }
    if (index === 2) {
      data.append('_entrance_exam_certificate_doc', this.state.updatedFile);
    }
    if (index === 3) {
      data.append('_admission_order_doc', this.state.updatedFile);
    }
    if (index === 4) {
      data.append('_college_id_doc', this.state.updatedFile);
    }
    if (index === 5) {
      data.append('_nationality_id_doc', this.state.updatedFile);
    }
    if (index === 6) {
      data.append('_address_doc', this.state.updatedFile);
    }

    this.setState({
      isLoading: true,
    });

    // return;
    axios
      .post(`user/staff_doc_edit`, data)
      .then((res) => {
        const uploadedDoc = this.state.uploadedDoc;
        uploadedDoc[index].imageOpen = false;
        this.setState(
          {
            isLoading: false,
            updatedFile: '',
            uploadedDoc,
          },
          () => {
            this.fetchApi(id);
          }
        );

        NotificationManager.success(t(`user_management.Image_updated_successfully`));
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  handleActive = () => {
    this.setState({
      active: false,
      isLoading: true,
    });

    const data = {
      id: id,
      status: this.state.active,
    };

    axios
      .post(`/user/active_inactive`, data)
      .then((res) => {
        this.setState({
          isLoading: false,
        });
        this.props.history.push({
          pathname: '/student/management',
          state: {
            completeView: false,
            pendingView: true,
            inactiveView: false,
          },
        });

        NotificationManager.success(t('user_management.Student_In_Active_Successfully'));
      })
      .catch((error) => {
        NotificationManager.error(`${error.response.data.message}`);
        this.setState({
          isLoading: false,
        });
      });
  };

  handleSelect = (e, name) => {
    e.preventDefault();
    if (name === 'program') {
      this.setState({
        program_no: e.target.value,
        program_noError: '',
      });
    }

    if (name === 'country') {
      this.setState({
        _nationality_id: e.target.value,
        countryError: '',
      });
    }

    if (name === 'batch') {
      this.setState({
        selectedBatch: e.target.value,
        selectedBatchError: '',
      });
    }
  };

  handleChange = (e, name, index) => {
    let contact = this.state.contact;
    let contactsError = this.state.contactsError;

    if (name === 'name') {
      contact[index].name = e.target.value;
      contactsError[index].nameError = '';
    }

    if (name === 'mobile') {
      contact[index].mobile = e.target.value.replace(/[^0-9]/g, '');
      contactsError[index].mobileError = '';
    }

    if (name === 'email') {
      contact[index].email = e.target.value;
      contactsError[index].emailError = '';
    }
    if (name === 'studendRelation') {
      contact[index].relation = e.target.value;
    }
    this.setState({
      contact,
    });
  };

  handleSelectedNationality = (_nationality_id) => {
    this.setState({
      _nationality_id,
    });
  };

  handleGoBack = () => {
    if (this.props.userStatus === 'completed') {
      this.props.history.push({
        pathname: '/student/management',
      });
    } else {
      this.props.handleGoBack();
    }
  };

  render() {
    const { minutes, seconds, tabs, selectedIndex } = this.state;
    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';

    let selectedProgramName;
    for (let i = 0; i < this.state.programData.length; i++) {
      if (this.state.programData[i].value === this.state.program_no) {
        selectedProgramName = this.state.programData[i].name;
      }
    }

    const {
      showWithCountryCode,
      mobileLengthMatch,
      countryCodeLength,
      isMobileVerifyMandatory,
    } = this.props;
    const verifyMobile = isMobileVerifyMandatory();
    const userSensitiveData = isModuleEnabled('USER_SENSITIVE');
    const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
    const documentSecNeed = isModuleEnabled('DOCUMENT_SEC_NEED');
    const userDOBValidation = isModuleEnabled('USER_DOB');
    return (
      <React.Fragment>
        <NotificationContainer />

        <Loader isLoading={this.state.isLoading} />

        <div className="main pt-4">
          <div className="container">
            {this.props.userStatus === 'completed' && (
              <div className="float-left border border-radious-30">
                {(CheckPermission(
                  'tabs',
                  'User Management',
                  'Student Management',
                  '',
                  permissionName,
                  'Profile Edit'
                ) ||
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    permissionName,
                    'Profile Edit'
                  )) && (
                  <div className="pt-2 ">
                    <div className="float-left pr-3 pl-3">
                      <p
                        style={{ marginBottom: '11px' }}
                        className={`${getLang() === 'ar' ? 'digi-mr-16' : ''}`}
                      >
                        <Trans i18nKey={'status_inactive'}></Trans>{' '}
                      </p>
                    </div>
                    <div className="float-left pr-3">
                      <label className="switch mb-0">
                        <input
                          checked={this.state.active}
                          type="checkbox"
                          onClick={this.handleActive}
                        />
                        <span className="slider_check round"></span>
                      </label>
                    </div>
                  </div>
                )}
              </div>
            )}
            {this.state.edit === true ? (
              <div className="float-right">
                <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                  <Trans i18nKey={'back'}></Trans>{' '}
                </Button>
                {(CheckPermission(
                  'tabs',
                  'User Management',
                  'Student Management',
                  '',
                  permissionName,
                  'Profile Edit'
                ) ||
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    permissionName,
                    'Profile Edit'
                  )) && (
                  <Button className="m-2" onClick={this.handleSingleSubmit}>
                    <Trans i18nKey={'save'}></Trans>{' '}
                  </Button>
                )}
              </div>
            ) : (
              <React.Fragment>
                <div className="float-right">
                  <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                    <Trans i18nKey={'back'}></Trans>{' '}
                  </Button>
                  {(CheckPermission(
                    'tabs',
                    'User Management',
                    'Student Management',
                    '',
                    permissionName,
                    'Profile Edit'
                  ) ||
                    CheckPermission(
                      'subTabs',
                      'User Management',
                      'Student Management',
                      '',
                      'Registration Pending',
                      '',
                      permissionName,
                      'Profile Edit'
                    )) && (
                    <Button className="m-2" onClick={this.handleEdit}>
                      <Trans i18nKey={'edit'}></Trans>{' '}
                    </Button>
                  )}
                </div>
              </React.Fragment>
            )}

            <div className="clearfix"></div>

            <div className="white p-4 mb-5">
              <div className="row">
                <div className="col-md-5">
                  {this.state.edit === true ? (
                    <React.Fragment>
                      {/* edit field start */}
                      <div className="mt-0">
                        <div className="row">
                          <h4 className="bold">
                            <Trans i18nKey={'edit_personal_details'}></Trans>
                          </h4>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.first}
                              floatingLabel={<Trans i18nKey={'first_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'first')}
                              feedback={this.state.firstError}
                              correction={this.state.correction_first_name}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.middle}
                              floatingLabel={<Trans i18nKey={'middle_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'middle')}
                              feedback={this.state.middleError}
                              correction={this.state.correction_middle_name}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              esting
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.last}
                              floatingLabel={!lastNameRequired() ? 'Last Name' : t('last_name')}
                              changed={(e) => this.handleChangeText(e, 'last')}
                              feedback={this.state.lastError}
                              correction={this.state.correction_last_name}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.family}
                              floatingLabel={<Trans i18nKey={'family_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'family')}
                              feedback={this.state.familyError}
                            />
                          </div>

                          <div className="col-md-12">
                            <label>
                              <Trans i18nKey={'gender'}></Trans>
                            </label>
                            <Input
                              elementType={'radio'}
                              elementConfig={gender}
                              className={'form-radio1'}
                              selected={this.state.selectGender}
                              labelclass="radio-label2"
                              onChange={(e) => this.onRadioGroupChange(e, 'selectGender')}
                              feedback={this.state.selectGenderError}
                              correction={this.state.correction_gender}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.academic}
                              floatingLabel={<Trans i18nKey={'academic_id'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'academic')}
                              feedback={this.state.academicError}
                              correction={this.state.correction_academic_no}
                            />
                          </div>
                          {userDOBValidation && (
                            <div className="col-md-12 customeDatePickWrapper">
                              <p className="floatinglabelcustom"> Date Of Birth *</p>
                              <i className="fa fa-clock-o calenderCustom" aria-hidden="true"></i>
                              <DatePicker
                                placeholderText="Date Of Birth"
                                selected={
                                  this.state.dob !== '' && this.state.dob !== null
                                    ? new Date(this.state.dob)
                                    : ''
                                }
                                onChange={(e) => this.handleChangeText(e, 'dob')}
                                value={
                                  this.state.dob !== '' && this.state.dob !== null
                                    ? moment(this.state.dob).format('D MMM YYYY')
                                    : ''
                                }
                                dateFormat="d MMM yyyy"
                                className={'form-control customeDatepick'}
                                showMonthDropdown
                                showYearDropdown
                                maxDate={maxDateSet()}
                                yearDropdownItemNumber={25}
                              />
                              <div className="InvalidFeedback">{this.state.dobError}</div>
                              {/* <Input
                                elementType={"floatinginput"}
                                elementConfig={{
                                  type: "date",
                                }}
                                maxLength={25}
                                value={String(this.state.dob).slice(0, 10)}
                                floatingLabel={"Date of birth"}
                                changed={(e) => this.handleChangeText(e, "dob")}
                                feedback={this.state.dobError}
                              /> */}
                            </div>
                          )}

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'email',
                              }}
                              value={this.state.email}
                              floatingLabel={<Trans i18nKey={'emailId'}></Trans>}
                              disabled={true}
                            />
                          </div>

                          <div className="col-md-12">
                            <p className="floatinglabelcustom">
                              {' '}
                              <Trans i18nKey={'nationality_optional'}></Trans>
                            </p>
                            <Select
                              value={this.state._nationality_id}
                              options={this.state.countries}
                              onChange={this.handleSelectedNationality}
                              isClearable={true}
                            />
                            {/* <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.countries,
                              }}
                              value={this.state._nationality_id}
                              className={'customize_dropdown'}
                              floatingLabel={'Nationalty'}
                              changed={(e) => this.handleSelect(e, 'country')}
                              feedback={this.state.countryError}
                            /> */}
                          </div>
                          {userSensitiveData && (
                            <div className="col-md-12">
                              <Input
                                elementType={'floatinginput'}
                                elementConfig={{
                                  type: 'text',
                                }}
                                maxLength={25}
                                value={this.state.nationalId}
                                floatingLabel={
                                  <Trans
                                    i18nKey={'national/residence'}
                                    values={{
                                      optional: !hasNationalIdValidation ? '(Optional)' : '',
                                    }}
                                  ></Trans>
                                }
                                changed={(e) => this.handleChangeText(e, 'nationalId')}
                                feedback={this.state.nationalIdError}
                              />
                            </div>
                          )}

                          <div className="col-md-12">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.programData,
                              }}
                              value={this.state.program_no}
                              className={'customize_dropdown'}
                              changed={(e) => this.handleSelect(e, 'program')}
                              floatingLabel={<Trans i18nKey={'program_name'}></Trans>}
                              feedback={this.state.program_noError}
                              correction={this.state.correction_program_no}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.getBatch(),
                              }}
                              value={this.state.selectedBatch}
                              label={isIndVer() ? 'Intake' : 'Batch'}
                              className={'customize_dropdown'}
                              changed={(e) => this.handleSelect(e, 'batch')}
                              feedback={this.state.selectedBatchError}
                            />
                          </div>
                          {userSensitiveData && (
                            <div className="col-md-12">
                              <Input
                                elementType={'floatinginput'}
                                elementConfig={{
                                  type: 'text',
                                }}
                                maxLength={25}
                                value={this.state.passport_no}
                                floatingLabel={<Trans i18nKey={'passport_number'}></Trans>}
                                changed={(e) => this.handleChangeText(e, 'passport')}
                                feedback={this.state.passportError}
                              />
                            </div>
                          )}
                          {userSensitiveData && (
                            <>
                              <h4 className="bold pt-3">
                                <Trans i18nKey={'address_details'}></Trans>
                              </h4>

                              {/* <h3 className="f-16 pt-4 ">Address Details</h3> */}

                              <div className="col-md-12 pt-1">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  value={this.state.buildingNo}
                                  floatingLabel={<Trans i18nKey={'building_no'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'buildingNo')}
                                  feedback={this.state.buildingNoError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={25}
                                  value={this.state.distric}
                                  floatingLabel={<Trans i18nKey={'district_name'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'distric')}
                                  feedback={this.state.districError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={25}
                                  value={this.state.city}
                                  floatingLabel={<Trans i18nKey={'city_name'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'city')}
                                  feedback={this.state.cityError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={10}
                                  value={this.state.zipCode}
                                  floatingLabel={<Trans i18nKey={'zip_code'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'zipCode')}
                                  feedback={this.state.zipCodeError}
                                />
                              </div>

                              <div className="col-md-12">
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={2}
                                  value={this.state.unit}
                                  floatingLabel={<Trans i18nKey={'floor_no'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'unit')}
                                  feedback={this.state.unitError}
                                />
                              </div>
                            </>
                          )}
                          <h4 className="bold pt-3">
                            <Trans i18nKey={'contact_details'}></Trans>
                          </h4>
                          <div className="col-md-12">
                            <div className="row">
                              <div className={`col-md-${verifyMobile ? '8' : '12'}`}>
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={countryCodeLength}
                                  value={this.state.mobile}
                                  floatingLabel={<Trans i18nKey={'mobile_number'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'mobiles')}
                                  // disabled={
                                  //   this.state.confirmMobile === true
                                  //     ? "disabled"
                                  //     : ""
                                  // }
                                  feedback={this.state.mobileError}
                                />
                              </div>
                              {verifyMobile && (
                                <div className="col-md-4">
                                  <p
                                    className="text-blue remove_hover mb-0 pt-4"
                                    onClick={
                                      mobileLengthMatch(this.state.mobile)
                                        ? this.mobileVerify
                                        : () => {}
                                    }
                                  >
                                    {' '}
                                    <Trans i18nKey={'change_verify'}></Trans>{' '}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="col-md-10 mr-1">
                            <Input
                              elementType={'radio'}
                              elementConfig={contactList}
                              className={'form-radio1 mr-1'}
                              selected={this.state.selectContact}
                              labelclass="radio mr-2"
                              onChange={(e) => this.onRadioGroupChange(e, 'contact')}
                              feedback={this.state.contactError}
                            />
                          </div>
                          <div className="col-md-12">
                            <ContactDetails
                              contactsError={this.state.contactsError}
                              contact={this.state.contact}
                              countryCodeLength={countryCodeLength}
                              handleChange={this.handleChange}
                            />
                          </div>
                          {userSensitiveData && this.state.data?._id !== undefined && (
                            <VaccinationDetails
                              edit={this.state.edit}
                              userType="student"
                              permissionName={permissionName}
                              userId={this.state.data._id}
                              type="content"
                            />
                          )}
                        </div>
                      </div>
                      {/* edit field end */}
                    </React.Fragment>
                  ) : (
                    <React.Fragment>
                      {/* view field start */}
                      <div className="mt-0">
                        <h4 className="bold">
                          <Trans i18nKey={'personal'}></Trans>
                        </h4>

                        <ProfileText
                          title={<Trans i18nKey={'first_name'}></Trans>}
                          value={this.state.first}
                          error={this.state.correction_first_name}
                          className="border-bottom pb-1"
                        />
                        <ProfileText
                          title={<Trans i18nKey={'middle_name'}></Trans>}
                          value={this.state.middle ? this.state.middle : 'N/A'}
                          error={this.state.correction_middle_name}
                        />
                        <ProfileText
                          title={!lastNameRequired() ? 'Last Name' : t('last_name')}
                          value={this.state.last ? this.state.last : 'N/A'}
                          error={this.state.correction_last_name}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'family_name'}></Trans>}
                          value={this.state.family ? this.state.family : 'N/A'}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'gender'}></Trans>}
                          value={this.state.selectGender}
                          error={this.state.correction_gender}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'academic_id'}></Trans>}
                          value={this.state.academic}
                          error={this.state.correction_academic_no}
                        />
                        {userDOBValidation && (
                          <ProfileText
                            title={<Trans i18nKey={'dob'}></Trans>}
                            value={
                              this.state.dob &&
                              this.state.dob !== undefined &&
                              moment(this.state.dob).format('D MMM YYYY')
                            }
                          />
                        )}
                        <ProfileText
                          title={<Trans i18nKey={'emailId'}></Trans>}
                          value={this.state.email}
                        />
                        {userSensitiveData && (
                          <ProfileText
                            title={
                              <Trans
                                i18nKey={'national/residence'}
                                values={{ optional: !hasNationalIdValidation ? '(Optional)' : '' }}
                              ></Trans>
                            }
                            value={this.state.nationalId ? this.state.nationalId : 'N/A'}
                            error={this.state.correction_nationality_id}
                          />
                        )}
                        <ProfileText
                          title={<Trans i18nKey={'nationality_optional'}></Trans>}
                          value={
                            this.state._nationality_name ? this.state._nationality_name : 'N/A'
                          }
                        />
                        <ProfileText
                          title={<Trans i18nKey={'program_name'}></Trans>}
                          value={selectedProgramName}
                          error={this.state.correction_program_no}
                        />

                        <ProfileText
                          title={isIndVer() ? 'Intake' : 'Batch'}
                          value={
                            this.state.selectedBatch !== '' ? ucFirst(this.state.selectedBatch) : ''
                          }
                          error={this.state.selectedBatchError}
                        />
                        {userSensitiveData && (
                          <ProfileText
                            title={<Trans i18nKey={'passport_number'}></Trans>}
                            value={this.state.passport_no ? this.state.passport_no : 'N/A'}
                          />
                        )}
                      </div>
                      {userSensitiveData && (
                        <div className="mt-5">
                          <h4 className="bold">
                            <Trans i18nKey={'address'}></Trans>
                          </h4>
                          <ProfileText
                            title={<Trans i18nKey={'building_no'}></Trans>}
                            value={this.state.buildingNo}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'city_name'}></Trans>}
                            value={this.state.city}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'district_name'}></Trans>}
                            value={this.state.distric}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'zip_code'}></Trans>}
                            value={this.state.zipCode}
                          />
                          <ProfileText
                            title={<Trans i18nKey={'floor_no'}></Trans>}
                            value={this.state.unit}
                          />
                        </div>
                      )}

                      <div className="mt-5">
                        <h4 className="bold">
                          <Trans i18nKey={'contact_details'}></Trans>
                        </h4>

                        <ProfileText
                          title={<Trans i18nKey={'mobile_number'}></Trans>}
                          value={showWithCountryCode(this.state.mobile)}
                        />
                        <div className="row">
                          {this.state.contact &&
                            this.state.contact.map((data) => (
                              <>
                                {data.name && (
                                  <div className="col-md-6">
                                    {data.name && data.name !== undefined && (
                                      <ProfileText
                                        title={`${
                                          data.relation_type === 'guardian'
                                            ? 'Guardian'
                                            : data.relation_type === 'spouse'
                                            ? 'Spouse'
                                            : data.relation_type === undefined
                                            ? ''
                                            : ucFirst(data.relation_type)
                                        } Name`}
                                        value={data.name}
                                      />
                                    )}
                                    {data.mobile && data.mobile !== undefined && (
                                      <ProfileText
                                        title={<Trans i18nKey={'mobile_number'}></Trans>}
                                        value={showWithCountryCode(data.mobile)}
                                      />
                                    )}
                                    {data.email && data.email !== undefined && (
                                      <ProfileText
                                        title={<Trans i18nKey={'email'}></Trans>}
                                        value={data.email}
                                      />
                                    )}
                                  </div>
                                )}
                              </>
                            ))}
                        </div>
                      </div>
                      {userSensitiveData && this.state.data?._id !== undefined && (
                        <VaccinationDetails
                          edit={this.state.edit}
                          userType="student"
                          permissionName={permissionName}
                          userId={this.state.data._id}
                          type="content"
                        />
                      )}
                      {/* view field end */}
                    </React.Fragment>
                  )}
                </div>
                {documentSecNeed && (
                  <div className="col-md-7">
                    <div className="mt-0">
                      <h4 className="bold d-flex">
                        <Trans i18nKey={'uploaded_documents'}></Trans>{' '}
                      </h4>{' '}
                      <Accordion defaultActiveKey="">
                        {this.state.uploadedDoc.map((data, index) => (
                          <React.Fragment key={index}>
                            {data.name !== undefined && (
                              <Card>
                                <Accordion.Toggle
                                  as={Card.Header}
                                  eventKey={index}
                                  onClick={() => clickArrow.call(this, index)}
                                  className="card-header-icon"
                                >
                                  <div className="float-left">{data.name}</div>
                                  <div className="float-right">
                                    <i
                                      className={`fa fa-chevron-${
                                        selectedIndex !== index ? 'down' : 'up'
                                      } f-14`}
                                    ></i>
                                  </div>
                                </Accordion.Toggle>
                                <Accordion.Collapse eventKey={index}>
                                  <Card.Body className="bg-white">
                                    <div className="float-right">
                                      {(CheckPermission(
                                        'tabs',
                                        'User Management',
                                        'Student Management',
                                        '',
                                        permissionName,
                                        'Profile Edit'
                                      ) ||
                                        CheckPermission(
                                          'subTabs',
                                          'User Management',
                                          'Student Management',
                                          '',
                                          'Registration Pending',
                                          '',
                                          permissionName,
                                          'Profile Edit'
                                        )) && (
                                        <Tooltips title={<Trans i18nKey={'edit'}></Trans>}>
                                          <i
                                            className="fa fa-pencil pr-2 remove_hover"
                                            onClick={() => this.imageUpdateOpen(index)}
                                          ></i>
                                        </Tooltips>
                                      )}
                                      <Tooltips title={<Trans i18nKey={'preview'}></Trans>}>
                                        <i
                                          className="fa fa-picture-o remove_hover"
                                          onClick={() => this.handleClickOpen(index)}
                                        ></i>
                                      </Tooltips>
                                    </div>
                                    {data.image !== '' && data.image !== undefined ? (
                                      <img className="w-100" src={data.image} alt="#" />
                                    ) : (
                                      <div className="float-left">
                                        {' '}
                                        <Trans i18nKey={'no_image'}></Trans>
                                      </div>
                                    )}
                                    {data.isOpen === true && (
                                      <Lightbox
                                        clickOutsideToClose={false}
                                        mainSrc={data.image}
                                        onCloseRequest={() => this.handleClickClose(index)}
                                      />
                                    )}
                                  </Card.Body>
                                </Accordion.Collapse>
                              </Card>
                            )}
                            <Modal
                              show={data.imageOpen}
                              centered
                              size="md"
                              onHide={() => this.imageUpdateClose(index)}
                            >
                              <Modal.Body>
                                <h3 className="bold pt-2">
                                  <Trans i18nKey={'edit_uploaded_file'}></Trans>
                                </h3>
                                <div className="text-wrap-file">
                                  <Trans i18nKey={'current_file'}></Trans> :{' '}
                                  {data.image?.split('/')[5]?.split('-')[1]?.split('?')[0]}
                                </div>
                                <div className="text-wrap-file">
                                  <Trans i18nKey={'changed_file'}></Trans>:{' '}
                                  <span>{this.state.updatedFile.name}</span>
                                </div>
                                <div className="InvalidFeedback">
                                  {this.state.uploadedImageError}
                                </div>

                                <div className="float-right">
                                  <Input
                                    elementType={'imageUpload'}
                                    elementConfig={{
                                      type: 'file',
                                    }}
                                    className="float-right"
                                    imagename={t('browse')}
                                    accept="image/png, image/jpeg"
                                    changed={(e) => this.handleImageChange(e)}
                                  />
                                </div>
                              </Modal.Body>
                              <Modal.Footer>
                                <Button
                                  variant="outline-primary"
                                  onClick={() => this.imageUpdateClose(index)}
                                >
                                  <Trans i18nKey={'close'}></Trans>
                                </Button>
                                <Button
                                  variant="outline-primary"
                                  onClick={(e) => this.handleImageUpdate(e, index)}
                                  className={
                                    this.state.updatedFile === '' ||
                                    this.state.uploadedImageError === ''
                                      ? 'disabled-icon'
                                      : ''
                                  }
                                  disabled={
                                    this.state.updatedFile === '' ||
                                    this.state.uploadedImageError === ''
                                      ? true
                                      : false
                                  }
                                >
                                  <Trans i18nKey={'upload'}></Trans>{' '}
                                </Button>
                              </Modal.Footer>
                            </Modal>
                          </React.Fragment>
                        ))}
                      </Accordion>
                      {userSensitiveData && this.state.data?._id !== undefined && (
                        <VaccinationDetails
                          edit={this.state.edit}
                          userType="student"
                          permissionName={permissionName}
                          userId={this.state.data._id}
                          type="image"
                        />
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* mobile view entry funtion start   */}

        <Modal show={this.state.mobileView} centered size="md" onHide={this.mobileVerify}>
          <Modal.Body>
            <div className=" pt-2">Enter a reason to make this change</div>

            <div className="">
              <Input
                elementType={'floatinginput'}
                elementConfig={{
                  type: 'text',
                }}
                maxLength={40}
                value={this.state.reason}
                floatingLabel={<Trans i18nKey={'type_reason'}></Trans>}
                changed={(e) => this.handleChangeText(e, 'reason')}
                feedback={this.state.reasonError}
              />
            </div>

            <div className="pt-2">
              <b>
                {' '}
                <Trans i18nKey={'mobile_verify'}></Trans>{' '}
              </b>
            </div>
            <div className="pt-2">
              <b>
                <Trans i18nKey={'txt_mobile'}></Trans>
              </b>
              <br />
              <p className="text-gray pt-3 mb-0">
                {' '}
                <Trans i18nKey={'sent_mobile'}></Trans> {showWithCountryCode(this.state.mobile)}
              </p>
            </div>

            <div className="pt-2">
              <p className="red mb-0">{this.state.mobileError} </p>
            </div>

            <div className=" row pt-2">
              <div className="col-md-6">
                {minutes === 0 && seconds === 0 ? (
                  <Button variant="outline-primary" onClick={this.sendOTP} className="f-14">
                    <Trans i18nKey={'resend_otps'}></Trans>{' '}
                  </Button>
                ) : (
                  <React.Fragment>
                    {this.state.resendOtp === true ? (
                      <Button variant="outline-primary" className="f-14" disabled>
                        <Trans i18nKey={'resend_otps'}></Trans>{' '}
                      </Button>
                    ) : (
                      <Button variant="outline-primary" onClick={this.sendOTP} className="f-14">
                        <Trans i18nKey={'sendotps'}></Trans>{' '}
                      </Button>
                    )}
                  </React.Fragment>
                )}
              </div>

              <div className="col-md-6">
                {this.state.resendOtp === true && (
                  <div className="col-md-12">
                    {minutes === 0 && seconds === 0 ? (
                      ''
                    ) : (
                      <h1 className="f-16 float-right pt-2">
                        <Trans i18nKey={'resend_otp'}></Trans> : {minutes}:
                        {seconds < 10 ? `0${seconds}` : seconds}
                      </h1>
                    )}
                  </div>
                )}
              </div>

              {this.state.resendOtp === true && (
                <div className="col-md-12 pt-2">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={4}
                    value={this.state.otp}
                    floatingLabel={<Trans i18nKey={'enterotp'}></Trans>}
                    changed={(e) => this.handleChangeText(e, 'otp')}
                    feedback={this.state.otpError}
                  />
                </div>
              )}
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline-primary" onClick={(e) => this.mobileVerifyClose(e)}>
              <Trans i18nKey={'close'}></Trans>
            </Button>

            {this.state.resendOtp === true ? (
              <Button
                variant="primary"
                onClick={(e) => this.handleMobileSubmit(e)}
                className="f-14"
              >
                <Trans i18nKey={'submit'}></Trans>{' '}
              </Button>
            ) : (
              <Button variant="primary" className="f-14" disabled>
                <Trans i18nKey={'submit'}></Trans>{' '}
              </Button>
            )}
          </Modal.Footer>
        </Modal>

        {/* mobile view entry funtion start  */}
      </React.Fragment>
    );
  }
}
validStudentView.propTypes = {
  userStatus: PropTypes.func,
  history: PropTypes.func,
  handleGoBack: PropTypes.func,
  replaceCountryCode: PropTypes.func,
  showWithCountryCode: PropTypes.func,
  mobileLengthMatch: PropTypes.func,
  countryCodeLength: PropTypes.number,
  isMobileVerifyMandatory: PropTypes.func,
};
export default withRouter(withCountryCodeHooks(validStudentView));
