import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import swal from 'sweetalert2';
import {
  toggleModal,
  courseEdit,
  changeTitle,
  deleteCourse,
  deleterotationalCourse,
} from '../../../../_reduxapi/actions/calender';
import {
  PrimaryButton,
  FlexWrapper,
  Null,
  EventWrapper,
  Margin,
  DisplayTag,
  Heading,
} from '../../Styled';
import EventRows from '../../UtilityComponents/EventRows';
import jsPDF from 'jspdf';
import moment from 'moment';
import { jsUcfirst, addPDFPageNo, getEnvCollegeName } from '../../../../utils';
import { t } from 'i18next';

const DeleteModal = (props) => {
  const {
    toggleModal,
    id,
    active,
    content,
    changeTitle,
    deleterotationalCourse,
    deleteCourse,
    courseEdit,
    academic_year_name,
    nav_bar_title,
  } = props;
  let modal_term = content.modal_term !== undefined ? content.modal_term : 'regular';
  const history = useHistory();
  // const match = useRouteMatch();
  // const active = match.params.year || "year2";

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let programId = params.get('programid');
  let urlYear = params.get('year');
  let urlName = params.get('pname');

  const {
    modal_level: level,
    modal_course_id: course_id,
    modal_level_name: title,
    modal_rotational_number: number,
    modal_course_type: type,
  } = content;

  let course_details;
  //let rotation_no;

  if (number === '') {
    const course_index = props[active][level].findIndex((item) => item._course_id === course_id);
    course_details = props[active][level][course_index];
  } else {
    const course_index = props[active][level][number][type].findIndex(
      (item) => item._course_id === course_id
    );
    course_details = props[active][level][number][type][course_index];
  }

  let isRotation =
    props[active]['level'][0] !== undefined ? props[active]['level'][0].rotation : 'no';

  let modal_iframe_show =
    content.modal_iframe_show !== undefined ? content.modal_iframe_show : false;
  let modal_currentPGAccess_show =
    content.modal_currentPGAccess !== undefined ? content.modal_currentPGAccess : false;

  let rotationWeekNo = 'Rotation 1';
  if (isRotation === 'yes') {
    rotationWeekNo = `Rotation ${number !== '' ? number + 1 : 1}`;
  }

  const dataAlign = (dispatchFn, cancel) => {
    let data = {};

    data._calendar_id = props[active]['id'];
    data.level_no = String(title);
    data.batch = modal_term;
    data._course_id = course_details['_course_id'];

    if (number !== '') {
      data.rotation_no = number + 1;
      //rotation_no = data.rotation_no;
    }

    swal
      .fire({
        title: t('sure_delete'),
        text: t('once_deleted_cant_recover'),
        icon: 'warning',
        buttons: true,
        dangerMode: true,
      })
      .then((res) => {
        if (res.isConfirmed) {
          return dispatchFn(data);
        } else {
          return cancel();
        }
      });
  };

  const printCourseEventListPDF = () => {
    var doc = new jsPDF();
    let eventsList = [];
    if (course_details['courses_events'] && course_details['courses_events'].length > 0) {
      eventsList = course_details['courses_events'].map((list, index) => {
        let eventTitle =
          typeof list.event_name === 'object' ? list.event_name.first_language : list.event_name;
        let event_date = '';
        if (list.event_date !== '') {
          event_date = moment(list.event_date).format('DD MMM YYYY');
        }
        let end_date = '';
        if (list.end_date !== '') {
          end_date = moment(list.end_date).format('DD MMM YYYY');
        }
        let start_time = '';
        if (list.start_time !== '') {
          start_time = moment(Date.parse(list.start_time)).format('hh:mm  A');
        }
        let end_time = '';
        if (list.end_time !== '') {
          end_time = moment(Date.parse(list.end_time)).format('hh:mm  A');
        }
        return [index + 1, eventTitle, list.event_type, event_date, start_time, end_date, end_time];
      });
    }

    let yearTitle = '';
    if (
      academic_year_name !== undefined &&
      academic_year_name !== '' &&
      academic_year_name !== null
    ) {
      // let splitYear = academic_year_name.split('-');
      // let year1Title = splitYear[0];
      // let year2Title = splitYear[1];
      yearTitle = academic_year_name; //year1Title + ' - ' + year2Title.substr(2, 4);
    }

    let navTitle = '';
    if (nav_bar_title !== '') {
      navTitle = nav_bar_title.split('>');
      navTitle = navTitle[0].trim();
    }

    let programTitle = navTitle !== '' ? jsUcfirst(navTitle) : 'Program Calendar';
    let courseTitle = title !== '' ? jsUcfirst(title) : '';
    let st_date =
      course_details.start_date !== ''
        ? moment(course_details.start_date).format('DD-MMM YYYY')
        : '';
    let en_date =
      course_details.end_date !== '' ? moment(course_details.end_date).format('DD-MMM YYYY') : '';

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text('Academic year ' + yearTitle, 14, 20);
    doc.text(getEnvCollegeName().toLowerCase(), 200, 20, null, null, 'right');
    doc.setFont('helvetica', 'bold');
    doc.text(programTitle, 14, 30);
    doc.setFont('helvetica', 'normal');
    doc.text(`Year : ${active.replace('year', '')}  Level : ${String(title)}`, 14, 37);
    doc.text(`Term : Regular`, 14, 44);
    doc.line(14, 50, 200, 50);
    let lastPos = 50;
    if (isRotation === 'yes') {
      doc.text(rotationWeekNo, 14, lastPos + 6);
      doc.line(14, lastPos + 9, 200, lastPos + 9);
      lastPos = 59;
    }
    doc.text(
      `${course_details.courses_number} - ${courseTitle} ( Date range : ${st_date} to ${en_date} )`,
      14,
      lastPos + 10
    );

    jsPDF.autoTableSetDefaults({
      headStyles: { fillColor: '#e0e0e0', textColor: 0 },
      bodyStyles: { fillColor: '#ffffff', textColor: 0 },
    });
    doc.autoTable({
      startY: lastPos + 15,
      head: [
        ['S.no', 'Event Title', 'Event Type', 'Start Date', 'Start Time', 'End Date', 'End Time'],
      ],
      body: eventsList,
      tableLineColor: [189, 195, 199],
      tableLineWidth: 0.5,
      theme: 'grid',
    });
    addPDFPageNo(doc);
    doc.save(`${course_details.courses_number}-Course-event-list.pdf`);
  };

  return (
    <Fragment>
      <FlexWrapper>
        <Heading fs="20px" fw="500">
          Course details
        </Heading>
        <Null />
        {!modal_iframe_show && (
          <PrimaryButton
            className={
              course_details['courses_events'] && course_details['courses_events'].length > 0
                ? 'light'
                : 'light disable'
            }
            disabled={
              !(course_details['courses_events'] && course_details['courses_events'].length > 0)
            }
            onClick={printCourseEventListPDF}
          >
            Export
          </PrimaryButton>
        )}
        {modal_currentPGAccess_show && !modal_iframe_show && (
          <PrimaryButton
            mg="0px"
            className="light"
            onClick={() => {
              courseEdit();
              history.push(
                `/course/${id}/${active}?year=${urlYear}&programid=${programId}&pname=${urlName}&name=${course_details.courses_name}`
              );
              // history.push(
              //   `/course-v1/${id}/${active}?year=${urlYear}&programid=${programId}&pname=${urlName}&name=${course_details.courses_name}&edit=${course_id}&_id=${_id}`
              // );
              changeTitle('Edit Course');
              // LocalStorageService.setCustomToken("activeYear", active);
              localStorage.removeItem('courseInputData');
            }}
          >
            edit
          </PrimaryButton>
        )}
      </FlexWrapper>
      <Heading fw="500" mg="10px 0" fs="18px">
        {`${course_details.courses_name}`}{' '}
        {course_details.courses_number !== undefined
          ? '(' + course_details.courses_number + ')'
          : ''}
      </Heading>
      <FlexWrapper>
        <DisplayTag>{title}</DisplayTag>
        <DisplayTag>{course_details.model}</DisplayTag>
      </FlexWrapper>
      <FlexWrapper mg="5px 0px">
        <Heading fw="500">Start date:</Heading>
        {`${new Date(course_details.start_date).toDateString()}`}
      </FlexWrapper>
      <FlexWrapper mg="5px 0px">
        <Heading fw="500">End date:</Heading>
        {`${new Date(course_details.end_date).toDateString()}`}
      </FlexWrapper>
      <p>Course events</p>
      <EventWrapper mg="20px 0px">
        <EventRows show="title" />
        {course_details['courses_events']
          .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
          //.filter((item) => course_details["_event_id"].includes(item._id))
          .map((item, i) => (
            <EventRows
              key={item._id}
              show="content"
              content={item}
              i={i}
              icon="off"
              del={() => {}}
              edit={() => {}}
            />
          ))}
      </EventWrapper>
      <FlexWrapper>
        {modal_currentPGAccess_show && !modal_iframe_show && (
          <FlexWrapper
            onClick={() => {
              if (number !== '') {
                dataAlign(deleterotationalCourse, toggleModal);
              } else {
                dataAlign(deleteCourse, toggleModal);
              }

              toggleModal();
            }}
          >
            <i className="fas fa-trash"></i>
            <Margin mg="0px 10px" style={{ cursor: 'pointer' }}>
              Delete course
            </Margin>
          </FlexWrapper>
        )}
        <Null />
        <PrimaryButton className="bordernone" mg="0px" onClick={() => toggleModal()}>
          cancel
        </PrimaryButton>
      </FlexWrapper>
    </Fragment>
  );
};

DeleteModal.propTypes = {
  toggleModal: PropTypes.func,
  changeTitle: PropTypes.func,
  deleterotationalCourse: PropTypes.func,
  deleteCourse: PropTypes.func,
  courseEdit: PropTypes.func,
  id: PropTypes.string,
  active: PropTypes.string,
  academic_year_name: PropTypes.string,
  nav_bar_title: PropTypes.string,
  content: PropTypes.object,
};

const mapStateToProps = ({ calender, auth, interimCalendar }) => ({
  active: calender.active_year,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
  id: calender.institution_Calender_Id,
  content: calender.edit_content,
  academic_year_name: calender.academic_year_name,
  nav_bar_title:
    calender.nav_bar_title !== ''
      ? calender.nav_bar_title
      : '' || interimCalendar.nav_bar_title !== ''
      ? interimCalendar.nav_bar_title
      : '',
  userRole: auth.loggedInUserData.role,
});

export default connect(mapStateToProps, {
  toggleModal,
  changeTitle,
  deleteCourse,
  deleterotationalCourse,
  courseEdit,
})(DeleteModal);
