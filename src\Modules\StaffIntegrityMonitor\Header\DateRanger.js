import React, { forwardRef, useState } from 'react';
import { Button, FormControl, Radio, FormControlLabel, RadioGroup } from '@mui/material';
import { Divider, Typography } from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import TextField from '@mui/material/TextField';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Map, fromJS } from 'immutable';
import PropTypes from 'prop-types';
import moment from 'moment';
import { MenuReuse } from '../utils';
import { useDispatch } from 'react-redux';
import { setData } from '_reduxapi/global_configuration/v1/actions';

const dateSelectedTypeLMS = Object.freeze({
  till_date: 'Till Date',
  current_month: 'Current Month',
  last_month: 'Last Month',
  specific_date: 'Specific Date',
});

const DateRanger = forwardRef(({ yearWithProgram, setYearWithProgram }, refreshStatusRef) => {
  const [open, setOpen] = useState(
    Map({
      anchorEl: null,
      date: yearWithProgram.getIn(['primaryData', 'date'], Map()),
    })
  );
  const dispatch = useDispatch();
  const handleInput = (type, item) => {
    switch (type) {
      case 'till_date': {
        const updatedData = Map({
          from: moment(
            yearWithProgram.getIn(['primaryData', 'academicYear', 'start_date'], '')
          ).format('YYYY-MM-DD'),
          to: moment().format('YYYY-MM-DD'),
          displayType: 'till_date',
        });
        setYearWithProgram((prev) => prev.setIn(['primaryData', 'date'], updatedData));
        setOpen(() =>
          Map({
            date: updatedData,
          })
        );

        break;
      }
      case 'current_month': {
        const updatedData = Map({
          from: moment().month(moment().month()).startOf('month').format('YYYY-MM-DD'),
          to: moment().month(moment().month()).endOf('month').format('YYYY-MM-DD'),
          displayType: 'current_month',
        });
        setYearWithProgram((prev) => prev.setIn(['primaryData', 'date'], updatedData));

        setOpen((prev) =>
          Map({
            date: updatedData,
          })
        );

        break;
      }
      case 'last_month': {
        const previousMonth = moment().subtract(1, 'months').month();
        const updatedData = Map({
          from: moment().month(previousMonth).startOf('month').format('YYYY-MM-DD'),
          to: moment().month(previousMonth).endOf('month').format('YYYY-MM-DD'),
          displayType: 'last_month',
        });
        setYearWithProgram((prev) => prev.setIn(['primaryData', 'date'], updatedData));

        setOpen(() =>
          Map({
            date: updatedData,
          })
        );
        break;
      }
      case 'apply_date': {
        const startDate = moment(open.getIn(['date', 'from'], ''), 'YYYY-MM-DD');
        const endDate = moment(open.getIn(['date', 'to'], ''), 'YYYY-MM-DD');
        if (
          open.getIn(['date', 'from'], '') !== open.getIn(['date', 'to'], '') &&
          !endDate.isAfter(startDate, 'day')
        ) {
          dispatch(setData(Map({ message: 'EndDate should be greater than startDate' })));
          break;
        }
        setOpen((prev) => prev.set('anchorEl', null).set('isOpen', false));
        setYearWithProgram((prev) => prev.setIn(['primaryData', 'date'], open.get('date', Map())));
        refreshStatusRef.current = 'fresh';
        break;
      }

      case 'specific_date': {
        const updatedData = Map({
          from: moment().format('YYYY-MM-DD'),
          to: moment().format('YYYY-MM-DD'),
          displayType: 'specific_date',
        });
        setOpen((prev) => prev.set('date', updatedData));
        break;
      }
      case 'cancel_clicked': {
        setOpen((prev) =>
          prev
            .set('anchorEl', null)
            .set('isOpen', false)
            .set('date', yearWithProgram.getIn(['primaryData', 'date'], Map()))
        );
        break;
      }
      default:
        return;
    }
  };
  const handleRadioChange = (event) => {
    const value = event.target.value;
    if (open.getIn(['date', 'displayType'], '') === value) return;
    handleInput(value);
  };
  return (
    <FormControl sx={{ minWidth: 180, mr: 1 }}>
      <Typography
        component="div"
        className="muiOutline remove_hover bg-white"
        aria-label="more"
        aria-controls="long-menu"
        aria-haspopup="true"
        onClick={(e) => {
          const data = fromJS({
            ...open.toJS(),
            type: 'date',
            isOpen: true,
            anchorEl: e.currentTarget,
          });
          setOpen(data);
        }}
      >
        <div className="d-flex justify-content-between pt-2 pb-2">
          <p className="mb-0 pl-3">
            {dateSelectedTypeLMS[open.getIn(['date', 'displayType'], '')]}{' '}
          </p>
          <ArrowDropDownIcon
            className="text-gray mr-2"
            sx={{
              transform: `rotate(${open.get('type', '') === 'date' ? '180deg' : '0deg'})`,
            }}
          />
        </div>
      </Typography>
      {open.get('type', '') === 'date' && (
        <MenuReuse
          open={open}
          setOpen={() => handleInput('cancel_clicked')}
          width="auto"
          isManualClose={open.getIn(['date', 'displayType'], '') !== 'specific_date' ? true : false}
        >
          <div className="p-2">
            <FormControl component="fieldset" className="w-100">
              <RadioGroup
                aria-label="options"
                name="options"
                value={open.getIn(['date', 'displayType'], '')}
                onChange={handleRadioChange}
              >
                <FormControlLabel
                  className="ml-1 mb-0"
                  value="till_date"
                  control={
                    <Radio
                      sx={{
                        '&.MuiRadio-root': {
                          color: '#147AFC',
                        },
                      }}
                      size="small"
                    />
                  }
                  label={<div className="f-15">Till Date</div>}
                />
                <Divider className="my-0" light />
                <FormControlLabel
                  value="current_month"
                  className="ml-1 mb-0"
                  control={
                    <Radio
                      sx={{
                        '&.MuiRadio-root': {
                          color: '#147AFC',
                        },
                      }}
                      size="small"
                    />
                  }
                  label={<div className="f-15">Current Month</div>}
                />
                <Divider className="my-0" light />
                <FormControlLabel
                  value="last_month"
                  className="ml-1 mb-0"
                  control={
                    <Radio
                      sx={{
                        '&.MuiRadio-root': {
                          color: '#147AFC',
                        },
                      }}
                      size="small"
                    />
                  }
                  label={<div className="f-15">Last Month</div>}
                />
                <Divider className="my-0" light />
                <FormControlLabel
                  value="specific_date"
                  className="ml-1 mb-0"
                  control={
                    <Radio
                      sx={{
                        '&.MuiRadio-root': {
                          color: '#147AFC',
                        },
                      }}
                      size="small"
                    />
                  }
                  label={
                    <div className="f-15 ">
                      {open.getIn(['date', 'displayType'], '') === 'specific_date'
                        ? 'Select '
                        : 'Specific '}
                      Date
                    </div>
                  }
                />
                <Divider className="my-0" light />
              </RadioGroup>
            </FormControl>
            {open.getIn(['date', 'displayType'], '') === 'specific_date' && (
              <div className="m-3">
                <div className="d-flex mt-3">
                  <div className="d-flex align-items-center mr-3">
                    <div className="mr-2 f-15">
                      Start <br /> Date
                    </div>
                    <div>
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DatePicker
                          value={new Date(open.getIn(['date', 'from'], ''))}
                          onChange={(newValue) => {
                            setOpen(
                              open.setIn(
                                ['date', 'from'],
                                moment(new Date(newValue)).format('YYYY-MM-DD')
                              )
                            );
                          }}
                          renderInput={(params) => <TextField {...params} />}
                          InputProps={{
                            endAdornment: (
                              <ArrowDropDownIcon
                                sx={{
                                  transform: `rotate(${
                                    open.getIn(['date', 'displayType'], '') === 'specific_date'
                                      ? '180deg'
                                      : '0deg'
                                  })`,
                                }}
                              />
                            ),
                          }}
                        />
                      </LocalizationProvider>
                    </div>
                  </div>
                  <div className="d-flex align-items-center">
                    <div className="mr-2 f-15">
                      End <br /> Date
                    </div>
                    <div>
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DatePicker
                          value={new Date(open.getIn(['date', 'to'], ''))}
                          onChange={(newValue) => {
                            setOpen(
                              open.setIn(
                                ['date', 'to'],
                                moment(new Date(newValue)).format('YYYY-MM-DD')
                              )
                            );
                          }}
                          renderInput={(params) => <TextField {...params} />}
                          InputProps={{
                            endAdornment: (
                              <ArrowDropDownIcon
                                sx={{
                                  transform: `rotate(${
                                    open.getIn(['date', 'displayType'], '') === 'specific_date'
                                      ? '180deg'
                                      : '0deg'
                                  })`,
                                }}
                              />
                            ),
                          }}
                        />
                      </LocalizationProvider>
                    </div>
                  </div>
                </div>
                <p className="mt-2 f-15">
                  The leave management report data will be gathered during the Date period.
                </p>
                <div className="d-flex justify-content-end mt-3">
                  <Button
                    size="small"
                    variant="outlined"
                    sx={{
                      '&:focus': {
                        outline: 'none',
                      },
                    }}
                    className="mr-3 border text-dark border-secondary px-3"
                    onClick={() => handleInput('cancel_clicked')}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => handleInput('apply_date')}
                  >
                    apply
                  </Button>
                </div>
              </div>
            )}
          </div>
        </MenuReuse>
      )}
    </FormControl>
  );
});
DateRanger.propTypes = {
  resetMessage: PropTypes.func,
  setYearWithProgram: PropTypes.func,
  yearWithProgram: PropTypes.instanceOf(Map),
};
export default DateRanger;
