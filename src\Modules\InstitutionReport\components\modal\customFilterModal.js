import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import MButton from 'Widgets/FormElements/material/Button';
import { fromJS, List, Map } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { get60MinTime } from '../../utils';
function CustomFilterModal({
  show,
  handleFilterModalClose,
  sessionStatus,
  setStatus,
  lateStarted,
  earlyEnded,
  statusWithPermission,
}) {
  const [statusCheck, setStatusCheck] = useState(fromJS(statusWithPermission));
  const [timing, setTiming] = useState(Map({ 'Late Started': 10, 'Early Ended': 10 }));

  useEffect(() => {
    if (sessionStatus.size > 0) {
      const updatedState = statusCheck.map((item) =>
        sessionStatus.includes(item.get('name')) ? item.set('checked', true) : item
      );
      setStatusCheck(updatedState);
    }
    setTiming(
      timing
        .set('Late Started', lateStarted !== '' ? lateStarted : 10)
        .set('Early Ended', earlyEnded !== '' ? earlyEnded : 10)
    );
  }, [sessionStatus, lateStarted, earlyEnded]); // eslint-disable-line

  const handleCheck = (e, name) => {
    const findIndex = statusCheck.findIndex((item) => item.get('name', '') === name);
    const updatedData = statusCheck.setIn([findIndex, 'checked'], e.target.checked);
    setStatusCheck(updatedData);
  };

  const handleAllCheck = (e) => {
    const updatedData = statusCheck.map((item) => item.set('checked', e.target.checked));
    setStatusCheck(updatedData);
  };

  const handleSubmit = () => {
    const filteredValue = statusCheck
      .filter((item) => item.get('checked', false))
      .map((item) => item.get('name', ''));
    let lateStart = filteredValue.includes('late') ? timing.get('Late Started', '') : '';
    let earlyEnd = filteredValue.includes('early') ? timing.get('Early Ended', '') : '';
    setStatus(filteredValue, lateStart, earlyEnd);
    handleFilterModalClose();
  };

  const disabledStatus = () => {
    const statusArray = statusCheck.filter((item) => {
      return (
        !['Merged', 'Late Started', 'Early Ended'].includes(item.get('label')) &&
        item.get('checked', false) === true
      );
    });
    return statusArray.size === 0;
  };

  return (
    <MaterialDialog show={show} onClose={handleFilterModalClose} maxWidth={'sm'} fullWidth={true}>
      <div className="w-100 p-4">
        <p className="mb-3 bold f-19"> Customizing Filter</p>
        <div className="d-flex justify-content-between align-items-center  filtersSelect">
          <p className="mb-0 bold text-skyblue">All Programs</p>
          <FormControlLabel
            checked={
              statusCheck.filter((item) => item.get('checked', false)).size === statusCheck.size
            }
            label="Select All"
            className="m-0"
            control={<Checkbox />}
            onChange={(event) => handleAllCheck(event)}
          />
        </div>

        <p className="mb-2 bold"> Session Status</p>
        {statusCheck
          .filter((item) => {
            return !['Merged', 'Late Started', 'Early Ended'].includes(item.get('label'));
          })
          .map((item, index) => {
            return (
              <div key={index}>
                <FormControlLabel
                  checked={item.get('checked', false)}
                  control={<Checkbox />}
                  label={item.get('label', '')}
                  labelPlacement="end"
                  onChange={(event) => handleCheck(event, item.get('name', ''))}
                />
              </div>
            );
          })}

        {statusCheck
          .filter((item) => {
            return ['Merged', 'Late Started', 'Early Ended'].includes(item.get('label'));
          })
          .map((item, index) => {
            return (
              <React.Fragment key={index}>
                {index === 0 && <p className="mb-3 mt-2 bold"> Remarks</p>}
                <div
                  className={`${
                    ['Late Started', 'Early Ended'].includes(item.get('label'))
                      ? 'd-flex justify-content-between align-items-center mb-3'
                      : ''
                  }`}
                >
                  <FormControlLabel
                    checked={item.get('checked', false)}
                    control={<Checkbox />}
                    label={item.get('label', '')}
                    labelPlacement="end"
                    onChange={(event) => handleCheck(event, item.get('name', ''))}
                  />
                  {['Late Started', 'Early Ended'].includes(item.get('label')) ? (
                    <div>
                      <p className="mb-0 f-14 digi-gray"> Grace Time</p>
                      <MaterialInput
                        disabled={!item.get('checked')}
                        elementType={'materialSelectNew'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{
                          options: get60MinTime(),
                        }}
                        changed={(e) => setTiming(timing.set(item.get('label'), e.target.value))}
                        value={timing.get(item.get('label'), 10)}
                        label={''}
                        labelclass={'mb-0 f-14'}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                </div>
              </React.Fragment>
            );
          })}

        <div className="d-flex justify-content-end mt-4">
          <MButton
            variant="outlined"
            color="darkGray"
            clicked={handleFilterModalClose}
            className="mr-3"
          >
            Cancel
          </MButton>
          <MButton variant="contained" clicked={handleSubmit} disabled={disabledStatus()}>
            Apply
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
}

CustomFilterModal.propTypes = {
  show: PropTypes.bool,
  handleFilterModalClose: PropTypes.func,
  setStatus: PropTypes.func,
  sessionStatus: PropTypes.instanceOf(List),
  lateStarted: PropTypes.string,
  earlyEnded: PropTypes.string,
  statusWithPermission: PropTypes.array,
};

export default React.memo(CustomFilterModal);
