import React from 'react';
import { fromJS } from 'immutable';
import { useHistory, useParams, useLocation } from 'react-router-dom';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { t } from 'i18next';
import { isModuleEnabled } from 'utils';

function CourseSideNav() {
  const history = useHistory();
  const location = useLocation();
  const params = useParams();

  function getMenuItems() {
    const menuItems = [
      { path: '/overview', title: 'Overview' },
      { path: '/session-status', title: 'Session Status' },
      // { path: '/activity', title: 'Activity' },
      { path: '/attendance-log', title: 'Attendance Log' },

      { path: '/students', title: 'Student Details' },
      { path: '/staffs', title: 'Staff Details' },
    ];
    if (isModuleEnabled('ACTIVITY_ENABLED')) {
      menuItems.splice(5, 0, { path: '/activity', title: 'Activity' });
    }
    const menuItemsArray = [];
    const translatedTitle = (tab) => {
      switch (tab) {
        case 'attendance-log':
          return t('reports_analytics.attendance_log');
        case 'session-status':
          return t('reports_analytics.session_status');
        case 'students':
          return t('reports_analytics.student_details');
        case 'staffs':
          return t('reports_analytics.staff_details');
        default:
          return t(`reports_analytics.${tab}`);
      }
    };
    menuItems.forEach((val) => {
      const { title, path } = val;
      if (CheckPermission('tabs', 'Reports and Analytics', 'Course Details', '', title, 'View')) {
        const name = translatedTitle(path.substring(1));
        menuItemsArray.push({ title: name, path });
      }
    });
    // menuItemsArray.push({ title: t('reports_analytics.settings'), path: '/settings' });
    return fromJS(menuItemsArray);
  }

  const menuItems = getMenuItems();
  return (
    <>
      {menuItems
        .filter((item) => {
          if (isModuleEnabled('ACTIVITY_ENABLED') === true) {
            return item;
          } else {
            return item.title !== 'Activity';
          }
        })
        .map((item) => {
          const path = item.get('path');
          const isActive = location.pathname.includes(path);
          return (
            <div
              key={path}
              className={`inner_drop_program ${isActive ? 'inner_drop_active' : ''}`}
              onClick={() => {
                history.replace(
                  `/reports/programs/${params.programId}/courses/${params.courseId}${path}${location.search}`
                );
              }}
            >
              <p className="mb-0 font-weight-bold">{item.get('title')} </p>
            </div>
          );
        })}
    </>
  );
}

export default CourseSideNav;
