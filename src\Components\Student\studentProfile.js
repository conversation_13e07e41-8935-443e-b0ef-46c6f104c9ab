import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import Input from '../../Widgets/FormElements/Input/Input';
import Button from '../../Widgets/FormElements/Button/Button';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import {
  maxDateSet,
  getURLParams,
  envSignUpService,
  parentDetailsMandatory,
  hasUserSensitiveData,
} from '../../utils';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import Select from 'react-select';
import VaccinationDetails from '../../Modules/UserManagement/VaccinationDetails';
import withCountryCodeHooks from 'Hoc/withCountryCodeHooks';
import { withRouter } from 'react-router-dom';

const fnameinput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const mnameinput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const lnameinput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const genderinput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];

const acainput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const enrollinput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];

const nationalinput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const programinput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];

const contact = [
  ['Parent', 'parent'],
  ['Guardian', 'guardian'],
  ['Spouse', 'spouse'],
];

class studentProfile extends Component {
  state = {
    selectFname: fnameinput[0][1][2],
    chooseFname: '',
    selectMname: mnameinput[0][1][2],
    chooseMname: '',
    selectLname: lnameinput[0][1][2],
    chooseLname: '',
    selectgender: genderinput[0][1][2],
    choosegender: '',
    selectAcadamic: acainput[0][1][2],
    chooseAcadamic: '',

    selectEnrollment: enrollinput[0][1][2],
    chooseEnrollment: '',

    selectNational: nationalinput[0][1][2],
    chooseNational: '',

    selectProgram: programinput[0][1][2],
    chooseProgram: '',

    selectContact: '',
    chooseContact: '',
    fName: '',
    mName: '',
    lName: '',
    DOB: '',
    selectedCountry: null,
    residendId: '',
    passpord: '',
    empId: '',
    buildingNo: '',
    city: '',
    distric: '',
    zipCode: '',
    phone: '',
    unit: '',
    GenderError: '',
    DOBError: '',
    countryError: '',
    buildingNoError: '',
    cityError: '',
    districError: '',
    zipCodeError: '',
    phoneError: '',
    unitError: '',

    selectedOption: null,
    isloading: false,
    university: [],

    pdf: '',
    faName: '',
    maName: '',
    fEmail: '',
    mEmail: '',
    fPhone: '',
    mPhone: '',

    gName: '',
    gPhone: '',
    gEmail: '',
    studendRelation: '',

    sName: '',
    sPhone: '',
    sEmail: '',

    fNameError: '',
    maNameError: '',
    fEmailError: '',
    mEmailError: '',
    fPhoneError: '',
    mPhoneError: '',

    gNameError: '',
    gPhoneError: '',
    gEmailError: '',

    sNameError: '',
    sPhoneError: '',
    sEmailError: '',
    academic: '',
    programNumber: '',
    enrollment: '',
    programName: '',
    programData: [],
    id: getURLParams('id'),
  };

  componentDidMount = () => {
    const parentValidation = envSignUpService('PARENT_APP', true);
    this.setState(
      {
        id: getURLParams('id'),
        chooseContact: parentValidation ? 'parent' : '',
        selectContact: parentValidation ? 'parent' : '',
      },
      () => this.fetchApi(this.state.id)
    );
    const userSensitive = hasUserSensitiveData();
    if (userSensitive) {
      this.setState({
        buildingNo: 'Not Applicable',
        city: 'Not Applicable',
        distric: 'Not Applicable',
        zipCode: '111',
        unit: '1',
        chooseNational: true,
        selectNational: 'correct',
      });
    }
  };

  fetchApi = (id) => {
    this.setState({ isLoading: true });
    axios
      .get(`country?limit=300&pageNo=1`)
      .then((res) => {
        const duplicateAr = res.data.data.filter(
          (v, i, a) => a.findIndex((t) => t.name === v.name) === i
        );
        const university = duplicateAr.map((data) => {
          return {
            label: data.name,
            value: data._id,
          };
        });
        // university.unshift({
        //   name: "",
        //   value: "",
        // });
        this.setState({
          university: university,
          univer: university[0].value,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });

    // staff api start //
    this.setState({ isLoading: true });

    axios
      .get(`user/student/${id}`)
      .then((res) => {
        const staff = res.data.data;

        this.setState({
          fName: staff.name.first,
          lName: staff.name.last !== undefined && staff.name.last !== null ? staff.name.last : '',
          mName:
            staff.name.middle !== undefined && staff.name.middle !== null ? staff.name.middle : '',
          gender: staff.gender,
          academic: staff.academic,
          residendId: staff.address.nationality_id,
          phone: staff.mobile,
          programNumber: staff.program_no,
          programName: staff.program_name !== undefined ? staff.program_name.split(' ')[0] : '',
          enrollment: staff.enrollment_year,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });

    // staff api end //

    //let field = { field: ['name', 'no'] };
    const institutionId = envSignUpService('REACT_APP_INSTITUTION_ID', false);
    const headers = {
      _institution_id: institutionId,
    };
    axios
      .get(
        '/digi_program?limit=200',
        institutionId
          ? {
              headers: headers,
            }
          : {}
      ) //program/program_list
      .then((res) => {
        if (res.data.data && res.data.data.length > 0) {
          const data = res.data.data.map((data) => {
            return {
              name: data.name,
              value: data.code,
            };
          });
          this.setState({
            programData: data,
            isLoading: false,
          });
        }
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  validation = (e, name) => {
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    const Number = /^[0-9]+$/;

    let genderError = '';
    let DOBError = '';
    let countryError = '';
    let buildingNoError = '';
    let cityError = '';
    let districError = '';
    let zipCodeError = '';
    let unitError = '';
    // let passpordError = "";
    let contactError = '';

    let selectFnameError = '';
    let selectMnameError = '';
    let selectLnameError = '';
    let acadamicError = '';
    let enrollmentError = '';
    let selectNationalError = '';
    let programError = '';

    let nameError = '';
    let emailError = '';
    let phoneError = '';

    let nameMError = '';
    let emailMError = '';
    let phoneMError = '';
    const { mobileLengthMatch, countryCodeLength } = this.props;

    const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    // if (this.state.selectContact === 'parent') {
    //   if (this.state.fEmail !== '' || this.state.fPhone !== '' || this.state.fName !== '') {
    //     if (
    //       this.state.fEmail !== '' &&
    //       this.state.fEmail !== undefined &&
    //       !emailRegex.test(this.state.fEmail)
    //     ) {
    //       emailError = 'Check the Email ID';
    //     }
    //     if (!this.state.fPhone) {
    //       phoneError = 'Contact Number Is Required';
    //     }
    //     if (!this.state.fName) {
    //       nameError = 'Name Is Required';
    //     }
    //   } else if (this.state.fEmail !== '' || this.state.fPhone !== '' || this.state.fName !== '') {
    //     if (
    //       this.state.mEmail !== '' &&
    //       this.state.mEmail !== undefined &&
    //       !emailRegex.test(this.state.mEmail)
    //     ) {
    //       emailMError = 'Check the Email ID';
    //     }
    //     if (!this.state.mPhone) {
    //       phoneMError = 'Contact Number Is Required';
    //     }
    //     if (!this.state.maName) {
    //       nameMError = 'Name Is Required';
    //     }
    //   }
    // }

    const parentValidation = envSignUpService('PARENT_APP', true);
    if (parentValidation) {
      if (this.state.selectContact === 'parent') {
        const fatherFieldIsEmpty =
          this.state.fEmail === '' || this.state.fPhone === '' || this.state.faName === '';
        const motherFieldIsNotEmpty =
          this.state.mEmail !== '' || this.state.mPhone !== '' || this.state.maName !== '';
        const fatherFieldIsNotEmpty =
          this.state.fEmail !== '' || this.state.fPhone !== '' || this.state.faName !== '';

        const validateFather =
          motherFieldIsNotEmpty && fatherFieldIsNotEmpty
            ? true
            : motherFieldIsNotEmpty
            ? false
            : fatherFieldIsEmpty
            ? true
            : this.state.fEmail !== '' || this.state.fPhone !== '' || this.state.faName !== '';

        if (validateFather) {
          if (
            this.state.fEmail !== '' &&
            this.state.fEmail !== undefined &&
            !emailRegex.test(this.state.fEmail)
          ) {
            emailError = 'Enter The Valid Email ID';
          }
          if (!this.state.fPhone) {
            phoneError = 'Father Contact Number Is Required';
          } else if (!mobileLengthMatch(this.state.fPhone)) {
            phoneError = `Enter The Valid ${countryCodeLength} Digit Number`;
          }
          if (!this.state.faName) {
            nameError = 'Father Name Is Required';
          }
        }
        if (this.state.mEmail !== '' || this.state.mPhone !== '' || this.state.maName !== '') {
          if (
            this.state.mEmail !== '' &&
            this.state.mEmail !== undefined &&
            !emailRegex.test(this.state.mEmail)
          ) {
            emailMError = 'Enter The Valid Email ID';
          }
          if (!this.state.mPhone) {
            phoneMError = 'Mother Contact Number Is Required';
          } else if (!mobileLengthMatch(this.state.mPhone)) {
            phoneMError = `Enter The Valid ${countryCodeLength} Digit Number`;
          }
          if (!this.state.maName) {
            nameMError = 'Mother Name Is Required';
          }
        }
      }

      if (this.state.selectContact === 'spouse') {
        if (
          this.state.sEmail !== '' &&
          this.state.sEmail !== undefined &&
          !emailRegex.test(this.state.sEmail)
        ) {
          emailError = `Enter The Valid Email ID`;
        }
        if (!this.state.sPhone) {
          phoneError = 'Spouse Contact Number Is Required';
        } else if (!mobileLengthMatch(this.state.sPhone)) {
          phoneError = `Enter The Valid ${countryCodeLength} Digit Number`;
        }
        if (!this.state.sName) {
          nameError = 'Spouse Name Is Required';
        }
      }

      if (this.state.selectContact === 'guardian') {
        if (
          this.state.gEmail !== '' &&
          this.state.gEmail !== undefined &&
          !emailRegex.test(this.state.gEmail)
        ) {
          emailError = 'Enter The Valid Email ID';
        }
        if (!this.state.gPhone) {
          phoneError = 'Guardian Contact Number Is Required';
        } else if (!mobileLengthMatch(this.state.gPhone)) {
          phoneError = `Enter The Valid ${countryCodeLength} Digit Number`;
        }
        if (!this.state.gName) {
          nameError = 'Guardian Name Is Required';
        }
      }
    }

    if (this.state.selectFname === 'r') {
      selectFnameError = 'Choose First Name Correct Or Wrong';
    }

    if (this.state.mName) {
      if (this.state.selectMname === 'r') {
        selectMnameError = 'Choose Middle Name Correct Or Wrong';
      }
    }
    if (this.state.lName) {
      if (this.state.selectLname === 'r') {
        selectLnameError = 'Choose Last Name Correct Or Wrong';
      }
    }
    if (this.state.selectgender === 'r') {
      genderError = 'Choose Gender Correct Or Wrong';
    }
    if (this.state.selectAcadamic === 'r') {
      acadamicError = 'Choose Academic Correct Or Wrong';
    }
    // if (this.state.selectEnrollment === "r") {
    //   enrollmentError = "Choose Enrollment Correct Or Wrong";
    // }

    if (this.state.selectProgram === 'r') {
      programError = 'Choose Program Correct Or Wrong';
    }

    if (this.state.selectNational === 'r') {
      selectNationalError = 'Choose National ID Correct Or Wrong';
    }
    const userDOBValidation = envSignUpService('USER_DOB', true);
    if (userDOBValidation) {
      if (this.state.DOB === '' || this.state.DOB === null) {
        DOBError = 'DOB Field is Required';
      }
    }
    // if (this.state.selectedCountry === '') {
    //   countryError = 'Nationality Field is Required';
    // }

    if (!this.state.buildingNo) {
      buildingNoError = 'Building No, Street Name Field is Required';
    } else if (!space.test(this.state.buildingNo)) {
      buildingNoError = 'Space not allowed beginning & end';
    } else if (this.state.buildingNo.length <= 2) {
      buildingNoError = 'Minimum 3 character is required ';
    }

    if (!this.state.city) {
      cityError = 'City Name Field is Required';
    } else if (!space.test(this.state.city)) {
      cityError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.city)) {
      cityError = 'Text Only allowed';
    } else if (this.state.city.length <= 2) {
      cityError = 'Minimum 3 character is required ';
    }

    if (!this.state.distric) {
      districError = 'District Name Field is Required';
    } else if (!space.test(this.state.distric)) {
      districError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.distric)) {
      districError = 'Text Only allowed';
    } else if (this.state.distric.length <= 2) {
      districError = 'Minimum 3 character is required ';
    }

    if (!this.state.zipCode) {
      zipCodeError = 'Zip Code Field is Required';
    } else if (!space.test(this.state.zipCode)) {
      zipCodeError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.zipCode)) {
      zipCodeError = 'Numeric Only allowed';
    } else if (this.state.zipCode.length <= 2) {
      zipCodeError = 'Minimum 3 character is required ';
    } else if (parseInt(this.state.zipCode) > 1000000) {
      zipCodeError = 'Pls Enter 1000000 Lesser than value ';
    } else if (parseInt(this.state.zipCode) === 0) {
      zipCodeError = 'Pls Enter 0 Greater than value ';
    }

    if (!this.state.unit) {
      unitError = 'Floor Number Field is Required';
    } else if (!space.test(this.state.unit)) {
      unitError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.unit)) {
      unitError = 'Numeric Only allowed';
    } else if (parseInt(this.state.unit) > 50) {
      unitError = 'Pls Enter 50 Lesser than value ';
    } else if (parseInt(this.state.unit) === 0) {
      unitError = 'Pls Enter 0 Greater than value ';
    }

    const selectedContact = this.state.selectContact;
    const hasParents = parentDetailsMandatory();
    if (hasParents) {
      if (selectedContact === 'r') {
        contactError = 'Contact Details is Required.';
      } else if (selectedContact === 'parent') {
        const { faName, fPhone, maName, mPhone } = this.state;
        const fName = faName.trim();
        const mName = maName.trim();
        const fNumber = fPhone.trim();
        const mNumber = mPhone.trim();
        if (fName === '' && mName === '') {
          contactError = 'Father / Mother Name and Mobile Number is Required.';
        } else if (fName !== '') {
          if (fNumber === '') {
            contactError = 'Father Mobile Number is Required.';
          } else if (fNumber !== '' && fNumber.length < 10) {
            contactError = 'Father Mobile Number Should be 10 Digits';
          }
        } else if (mName !== '') {
          if (mNumber === '') {
            contactError = 'Mother Mobile Number is Required.';
          } else if (mNumber !== '' && mNumber.length < 10) {
            contactError = 'Mother Mobile Number Should be 10 Digits';
          }
        }
      } else if (selectedContact === 'guardien') {
        const { gName, gPhone } = this.state;
        const gnName = gName.trim();
        const gnPhone = gPhone.trim();
        if (gnName === '') {
          contactError = 'Guardian Name is Required.';
        } else if (gnPhone === '') {
          contactError = 'Guardian Mobile Number is Required.';
        } else if (gnPhone !== '' && gnPhone.length < 10) {
          contactError = 'Guardian Mobile Number Should be 10 Digits';
        }
      } else if (selectedContact === 'spouse') {
        const { sName, sPhone } = this.state;
        const snName = sName.trim();
        const snPhone = sPhone.trim();
        if (snName === '') {
          contactError = 'Spouse Name is Required.';
        } else if (snPhone === '') {
          contactError = 'Spouse Mobile Number is Required.';
        } else if (snPhone !== '' && snPhone.length < 10) {
          contactError = 'Spouse Mobile Number Should be 10 Digits';
        }
      }
    }

    if (
      genderError ||
      DOBError ||
      countryError ||
      buildingNoError ||
      cityError ||
      districError ||
      zipCodeError ||
      unitError ||
      selectFnameError ||
      selectMnameError ||
      selectLnameError ||
      acadamicError ||
      selectNationalError ||
      enrollmentError ||
      programError ||
      nameError ||
      emailError ||
      phoneError ||
      nameMError ||
      emailMError ||
      phoneMError
    ) {
      this.setState({
        genderError,
        DOBError,
        countryError,
        buildingNoError,
        cityError,
        districError,
        zipCodeError,
        unitError,
        selectFnameError,
        selectMnameError,
        selectLnameError,
        acadamicError,
        selectNationalError,
        enrollmentError,
        programError,
        nameError,
        emailError,
        phoneError,
        nameMError,
        emailMError,
        phoneMError,
      });
      return false;
    }
    if (hasParents && contactError) {
      this.setState({ contactError });
      return false;
    }
    return true;
  };

  handleChange = (e, name) => {
    e.preventDefault();

    if (name === 'fName') {
      this.setState({
        fName: e.target.value,
        fNameError: '',
      });
    }
    if (name === 'mName') {
      this.setState({
        mName: e.target.value,
        maNameError: '',
      });
    }
    if (name === 'lName') {
      this.setState({
        lName: e.target.value,
        lNameError: '',
      });
    }

    if (name === 'familyName') {
      this.setState({
        familyName: e.target.value,
        familyNameError: '',
      });
    }
    if (name === 'residendId') {
      if (isNaN(e.target.value)) return;
      this.setState({
        residendId: e.target.value,
        residendIdError: '',
      });
    }
    if (name === 'passpord') {
      // if (isNaN(e.target.value)) return;
      this.setState({
        passpord: e.target.value,
        passpordError: '',
      });
    }
    if (name === 'empId') {
      this.setState({
        empId: e.target.value,
        empIdError: '',
      });
      axios
        .get(`staff/employee_id/${e.target.value}`)
        .then((res) => {
          const em = res.data.status;
          if (em === true) {
            this.setState({
              empIdError: 'This Employee Id is Already Exit',
            });
          } else {
            this.setState({
              empIdError: '',
            });
          }
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    }
    if (name === 'collegeId') {
      this.setState({
        collegeId: e.target.value,
        collegeIdError: '',
      });
    }
    if (name === 'buildingNo') {
      this.setState({
        buildingNo: e.target.value,
        buildingNoError: '',
      });
    }
    if (name === 'street') {
      this.setState({
        street: e.target.value,
        streetError: '',
      });
    }
    if (name === 'city') {
      this.setState({
        city: e.target.value,
        cityError: '',
      });
    }
    if (name === 'distric') {
      this.setState({
        distric: e.target.value,
        districError: '',
      });
    }

    if (name === 'zipCode') {
      if (isNaN(e.target.value)) return;
      this.setState({
        zipCode: e.target.value,
        zipCodeError: '',
      });
    }

    if (name === 'phone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        phone: e.target.value,
        phoneError: '',
      });
    }
    if (name === 'Additionalphone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        Additionalphone: e.target.value,
        AdditionalphoneError: '',
      });
    }
    if (name === 'unit') {
      if (isNaN(e.target.value)) return;
      this.setState({
        unit: e.target.value,
        unitError: '',
      });
    }
    if (name === 'degree') {
      this.setState({
        degree: e.target.value,
        degreeError: '',
      });
    }

    if (name === 'faName') {
      this.setState({
        faName: e.target.value,
        fNameError: '',
      });
    }

    if (name === 'maName') {
      this.setState({
        maName: e.target.value,
        maNameError: '',
      });
    }

    if (name === 'fEmail') {
      this.setState({
        fEmail: e.target.value,
        fEmailError: '',
      });
    }

    if (name === 'mEmail') {
      this.setState({
        mEmail: e.target.value,
        mEmailError: '',
      });
    }

    if (name === 'fPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        fPhone: e.target.value,
        fPhoneError: '',
      });
    }

    if (name === 'mPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mPhone: e.target.value,
        mPhoneError: '',
      });
    }

    if (name === 'gName') {
      this.setState({
        gName: e.target.value,
        gNameError: '',
      });
    }
    if (name === 'gPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        gPhone: e.target.value,
        gPhoneError: '',
      });
    }
    if (name === 'gEmail') {
      this.setState({
        gEmail: e.target.value,
        gEmailError: '',
      });
    }
    if (name === 'studendRelation') {
      this.setState({
        studendRelation: e.target.value,
        studendRelationError: '',
      });
    }

    if (name === 'sName') {
      this.setState({
        sName: e.target.value,
        sNameError: '',
      });
    }
    if (name === 'sPhone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        sPhone: e.target.value,
        sPhoneError: '',
      });
    }
    if (name === 'sEmail') {
      this.setState({
        sEmail: e.target.value,
        sEmailError: '',
      });
    }
  };

  handleSelectDOB = (date) => {
    this.setState({
      DOB: date,
      DOBError: '',
    });
  };

  handleSelect = (e, name) => {
    e.preventDefault();

    if (name === 'country') {
      this.setState({
        selectedCountry: e.target.value,
        countryError: '',
      });
    }
  };

  handleSubmit = (e) => {
    const { id } = this.state;
    //id = this.props.location.search.split('=')[1];
    e.preventDefault();
    const documentSecNeed = envSignUpService('DOCUMENT_SEC_NEED', true);
    if (this.validation()) {
      let contact;
      if (this.state.chooseContact === 'parent') {
        contact = {
          type: 'parent',
          father_name: this.state.faName !== '' ? this.state.faName.trim() : '',
          father_mobile: this.state.fPhone !== '' ? this.state.fPhone : '',
          father_email: this.state.fEmail !== '' ? this.state.fEmail.trim() : '',
          mother_name: this.state.maName !== '' ? this.state.maName.trim() : '',
          mother_mobile: this.state.mPhone !== '' ? this.state.mPhone : '',
          mother_email: this.state.mEmail !== '' ? this.state.mEmail.trim() : '',
        };
      } else if (this.state.chooseContact === 'guardian') {
        contact = {
          type: 'guardian',
          // relation: this.state.studendRelation !== '' ? this.state.studendRelation.trim() : '',
          guardian_name: this.state.gName !== '' ? this.state.gName.trim() : '',
          guardian_mobile: this.state.gPhone !== '' ? this.state.gPhone : '',
          guardian_email: this.state.gEmail !== '' ? this.state.gEmail.trim() : '',
        };
      } else if (this.state.chooseContact === 'spouse') {
        contact = {
          type: 'spouse',
          spouse_name: this.state.sName !== '' ? this.state.sName.trim() : '',
          spouse_mobile: this.state.sPhone !== '' ? this.state.sPhone : '',
          spouse_email: this.state.sEmail !== '' ? this.state.sEmail.trim() : '',
        };
      }

      if (contact === undefined) {
        contact = {};
      }

      const data = {
        id: id,
        first_name: this.state.chooseFname,
        middle_name: this.state.chooseMname !== '' ? this.state.chooseMname : true,
        last_name: this.state.chooseLname !== '' ? this.state.chooseLname : true,
        gender: this.state.choosegender,
        academic_no: this.state.chooseAcadamic,
        nationality_id: this.state.chooseNational,
        program_no: this.state.chooseProgram,
        //enrollment_year: this.state.chooseEnrollment,
        dob: this.state.DOB ? moment(this.state.DOB).format('YYYY-MM-DD') : '',
        building: this.state.buildingNo,
        city: this.state.city,
        district: this.state.distric,
        zip_code: this.state.zipCode,
        unit: this.state.unit,
        passport_no: this.state.passpord,
        contact: contact,
      };
      if (this.state.selectedCountry !== null) {
        data._nationality_id =
          this.state.selectedCountry?.value !== undefined ? this.state.selectedCountry?.value : '';
      }
      this.setState({ isLoading: true, isValidate: true });
      axios
        .post(`user/profile_update`, data)
        .then((res) => {
          NotificationManager.success(
            t(`user_management.Student_Personal_Details_Added_Successfully`)
          );
          this.setState({ isLoading: false });
          if (documentSecNeed) {
            this.props.history.push({
              pathname: '/student/upload',
              search: '?id=' + id,
            });
          } else {
            this.props.history.push('/login');
          }
        })
        .catch((error) => {
          NotificationManager.error(`${error?.response?.data?.data}`);
          this.setState({ isLoading: false });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'fnameinput') {
      this.setState({
        selectFname: e.target.value,
        selectFnameError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseFname: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseFname: false,
        });
      }
    }

    if (name === 'mnameinput') {
      this.setState({
        selectMname: e.target.value,
        selectMnameError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseMname: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseMname: false,
        });
      }
    }

    if (name === 'lnameinput') {
      this.setState({
        selectLname: e.target.value,
        selectLnameError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseLname: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseLname: false,
        });
      }
    }

    if (name === 'genderinput') {
      this.setState({
        selectgender: e.target.value,
        genderError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          choosegender: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          choosegender: false,
        });
      }
    }

    if (name === 'empinput') {
      this.setState({
        selectEmp: e.target.value,
        selectEmpError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseEmp: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseEmp: false,
        });
      }
    }

    if (name === 'nationalinput') {
      this.setState({
        selectNational: e.target.value,
        selectNationalError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseNational: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseNational: false,
        });
      }
    }

    if (name === 'contact') {
      this.setState({
        selectContact: e.target.value,
        contactError: '',
        nameError: '',
        emailError: '',
        phoneError: '',
        nameMError: '',
        emailMError: '',
        phoneMError: '',

        faName: '',
        maName: '',
        fEmail: '',
        mEmail: '',
        fPhone: '',
        mPhone: '',

        gName: '',
        gPhone: '',
        gEmail: '',
        studendRelation: '',

        sName: '',
        sPhone: '',
        sEmail: '',
      });
      if (e.target.value === 'parent') {
        this.setState({
          chooseContact: 'parent',
        });
      } else if (e.target.value === 'guardian') {
        this.setState({
          chooseContact: 'guardian',
        });
      } else if (e.target.value === 'spouse') {
        this.setState({
          chooseContact: 'spouse',
        });
      }
    }

    if (name === 'Acadamic') {
      this.setState({
        selectAcadamic: e.target.value,
        acadamicError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseAcadamic: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseAcadamic: false,
        });
      }
    }

    if (name === 'Enrollment') {
      this.setState({
        selectEnrollment: e.target.value,
        enrollmentError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseEnrollment: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseEnrollment: false,
        });
      }
    }

    if (name === 'program') {
      this.setState({
        selectProgram: e.target.value,
        programError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseProgram: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseProgram: false,
        });
      }
    }
  };

  handleSelectedNationality = (selectedCountry) => {
    this.setState({
      selectedCountry,
      countryError: '',
    });
  };

  render() {
    let selectedProgramName = '';
    if (
      this.state.programData &&
      this.state.programData.length > 0 &&
      this.state.programNumber !== ''
    ) {
      selectedProgramName = this.state.programData
        .filter((item) => item.value === this.state.programNumber)
        .reduce((_, el) => el.name, '');
    }
    const countryCodeLength = envSignUpService('REACT_APP_COUNTRY_CODE_LENGTH', false);
    const userSensitive = hasUserSensitiveData();
    const documentSecNeed = envSignUpService('DOCUMENT_SEC_NEED', true);
    const userDOBValidation = envSignUpService('USER_DOB', true);
    return (
      <div>
        <div className="staffprofile pl-3 pr-3 pt-3">
          <h2 className="f-20 text-white"> Profile Information </h2>
          <div className="row pt-4 pb-1 ">
            <div className="col-xl-1 col-lg-2 col-md-2 col-3 text-white  ">
              <div className="border-bottom-white-2px">
                <h3 className="f-16 text-center ">Personal</h3>
              </div>
            </div>
            {documentSecNeed && (
              <div className="col-xl-1 col-lg-1 col-md-1 col-3 text-white  ">
                <div className="">
                  <h3 className="f-16 text-center disabled-icon">Upload</h3>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="main pt-2">
          <Loader isLoading={this.state.isLoading} />
          <NotificationContainer />

          <div className="container mt-4">
            <div className="float-right pb-3">
              <Button btnType="Success" clicked={this.handleSubmit}>
                {' '}
                Submit
              </Button>
            </div>
            <div className="clearfix"></div>

            <div className=" outter-profile ">
              <div className="row">
                <div className="col-md-5">
                  <h3 className="f-16 "> Complete your profile information</h3>
                  {userDOBValidation && (
                    <div className="col-md-10 pt-3 customeDatePickWrapper">
                      <p className="floatinglabelcustom"> Date Of Birth *</p>
                      <i
                        className="fa fa-clock-o calenderCustom"
                        style={{ top: '3.5em' }}
                        aria-hidden="true"
                      ></i>
                      <DatePicker
                        placeholderText="Date Of Birth"
                        selected={
                          this.state.DOB !== '' && this.state.DOB !== null
                            ? new Date(this.state.DOB)
                            : ''
                        }
                        onChange={(d) => this.handleSelectDOB(d, 'DOB')}
                        value={
                          this.state.DOB !== '' && this.state.DOB !== null
                            ? moment(this.state.DOB).format('D MMM YYYY')
                            : ''
                        }
                        dateFormat="d MMM yyyy"
                        className={'form-control customeDatepick'}
                        showMonthDropdown
                        showYearDropdown
                        maxDate={maxDateSet()}
                        yearDropdownItemNumber={15}
                      />
                      <div className="InvalidFeedback">{this.state.DOBError}</div>

                      {/* <Input
                      elementType={"floatinginput"}
                      elementConfig={{
                        type: "date",
                      }}
                      maxLength={25}

                      value={this.state.DOB}
                      floatingLabel={"Date of birth"}
                      changed={(e) => this.handleSelect(e, "DOB")}
                      feedback={this.state.DOBError}
                    /> */}
                    </div>
                  )}
                  <div className="col-md-10 ">
                    <p className="floatinglabelcustom"> Nationality (Optional)</p>
                    <Select
                      options={this.state.university}
                      onChange={this.handleSelectedNationality}
                      isClearable={true}
                    />
                    <div className="InvalidFeedback">{this.state.countryError}</div>
                    {/* <Input
                      elementType={"floatingselect"}
                      elementConfig={{
                        options: this.state.university,
                      }}
                      value={this.state.selectedCountry}
                      floatingLabel={"Nationality"}
                      changed={(e) => this.handleSelect(e, "country")}
                      feedback={this.state.countryError}
                    /> */}
                  </div>
                  {!userSensitive && (
                    <>
                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.passpord}
                          floatingLabel={'Passport No'}
                          changed={(e) => this.handleChange(e, 'passpord')}
                          feedback={this.state.passpordError}
                        />
                      </div>

                      <h3 className="f-16 pt-4 ">Address Details</h3>

                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          value={this.state.buildingNo}
                          floatingLabel={'Building No, Street Name *'}
                          changed={(e) => this.handleChange(e, 'buildingNo')}
                          feedback={this.state.buildingNoError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.distric}
                          floatingLabel={'District Name *'}
                          changed={(e) => this.handleChange(e, 'distric')}
                          feedback={this.state.districError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.city}
                          floatingLabel={'City Name *'}
                          changed={(e) => this.handleChange(e, 'city')}
                          feedback={this.state.cityError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={10}
                          value={this.state.zipCode}
                          floatingLabel={'Zip Code *'}
                          changed={(e) => this.handleChange(e, 'zipCode')}
                          feedback={this.state.zipCodeError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={2}
                          value={this.state.unit}
                          floatingLabel={'Floor No *'}
                          changed={(e) => this.handleChange(e, 'unit')}
                          feedback={this.state.unitError}
                        />
                      </div>
                    </>
                  )}
                  <h3 className="f-16 pt-4 ">Contact details</h3>

                  <div className="col-md-10 pt-3">
                    <Input
                      elementType={'radio'}
                      elementConfig={contact}
                      className={'form-radio1'}
                      selected={this.state.selectContact}
                      labelclass="radio-label2"
                      onChange={(e) => this.onRadioGroupChange(e, 'contact')}
                      // feedback={this.state.phoneError}
                    />
                  </div>
                  {this.state.chooseContact === 'parent' && (
                    <div className="row pl-3">
                      <div className="col-md-6 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={30}
                          value={this.state.faName}
                          floatingLabel={'Father Name'}
                          changed={(e) => this.handleChange(e, 'faName')}
                          feedback={this.state.nameError}
                        />
                      </div>

                      <div className="col-md-6 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={30}
                          value={this.state.maName}
                          floatingLabel={'Mother Name'}
                          changed={(e) => this.handleChange(e, 'maName')}
                          feedback={this.state.nameMError}
                        />
                      </div>

                      <div className="col-md-6 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={countryCodeLength}
                          value={this.state.fPhone}
                          floatingLabel={'Father Mobile Number'}
                          changed={(e) => this.handleChange(e, 'fPhone')}
                          feedback={this.state.phoneError}
                        />
                      </div>

                      <div className="col-md-6 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={countryCodeLength}
                          value={this.state.mPhone}
                          floatingLabel={'Mother Mobile Number'}
                          changed={(e) => this.handleChange(e, 'mPhone')}
                          feedback={this.state.phoneMError}
                        />
                      </div>

                      <div className="col-md-6 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={100}
                          value={this.state.fEmail}
                          floatingLabel={'Father Email'}
                          changed={(e) => this.handleChange(e, 'fEmail')}
                          feedback={this.state.emailError}
                        />
                      </div>

                      <div className="col-md-6 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={100}
                          value={this.state.mEmail}
                          floatingLabel={'Mother Email'}
                          changed={(e) => this.handleChange(e, 'mEmail')}
                          feedback={this.state.emailMError}
                        />
                      </div>
                    </div>
                  )}

                  {this.state.chooseContact === 'guardian' && (
                    <div className="row pl-3">
                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={30}
                          value={this.state.gName}
                          floatingLabel={'Guardian Name'}
                          changed={(e) => this.handleChange(e, 'gName')}
                          feedback={this.state.nameError}
                        />
                      </div>

                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={countryCodeLength}
                          value={this.state.gPhone}
                          floatingLabel={'Guardian Mobile Number'}
                          changed={(e) => this.handleChange(e, 'gPhone')}
                          feedback={this.state.phoneError}
                        />
                      </div>

                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={100}
                          value={this.state.gEmail}
                          floatingLabel={'Guardian Email'}
                          changed={(e) => this.handleChange(e, 'gEmail')}
                          feedback={this.state.emailError}
                        />
                      </div>

                      {/* <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.studendRelation}
                          floatingLabel={'Relationship to Student'}
                          changed={(e) => this.handleChange(e, 'studendRelation')}
                          feedback={this.state.studendRelationError}
                        />
                      </div> */}
                    </div>
                  )}

                  {this.state.chooseContact === 'spouse' && (
                    <div className="row pl-3">
                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={30}
                          value={this.state.sName}
                          floatingLabel={'Spouse Name'}
                          changed={(e) => this.handleChange(e, 'sName')}
                          feedback={this.state.nameError}
                        />
                      </div>

                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={countryCodeLength}
                          value={this.state.sPhone}
                          floatingLabel={'Spouse Mobile Number'}
                          changed={(e) => this.handleChange(e, 'sPhone')}
                          feedback={this.state.phoneError}
                        />
                      </div>

                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={100}
                          value={this.state.sEmail}
                          floatingLabel={'Spouse Email'}
                          changed={(e) => this.handleChange(e, 'sEmail')}
                          feedback={this.state.emailError}
                        />
                      </div>
                    </div>
                  )}
                  {!userSensitive && this.state.id !== '' && (
                    <VaccinationDetails
                      className={'f-16'}
                      edit={true}
                      userType="student"
                      permissionName={'NO NEED'}
                      userId={this.state.id}
                      type="content"
                    />
                  )}
                </div>

                <div className="col-md-2"></div>

                <div className="col-md-5">
                  <div className="col-md-12">
                    <div className="row">
                      <div className="col-md-6 ">
                        <h3 className="f-16 ">Personal Details</h3>
                      </div>
                      <div className="col-md-6 ">Tap “correct” or “wrong”</div>
                    </div>
                  </div>

                  <div className="col-md-12 pt-3">
                    <div className="row border-bottom">
                      <div className="col-md-6">
                        <label className="toplabel">First Name *</label>
                        <p className="">{this.state.fName}</p>
                      </div>

                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={'radio'}
                          elementConfig={fnameinput}
                          className={'form-radio1'}
                          selected={this.state.selectFname}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'fnameinput')}
                          feedback={this.state.selectFnameError}
                        />
                      </div>
                    </div>
                    {this.state.mName && (
                      <div className="row border-bottom pt-2">
                        <div className="col-md-6">
                          <label className="toplabel">Middel Name *</label>
                          <p className="">{this.state.mName}</p>
                        </div>
                        <div className="col-md-6 pt-3">
                          <Input
                            elementType={'radio'}
                            elementConfig={mnameinput}
                            className={'form-radio1'}
                            selected={this.state.selectMname}
                            labelclass="radio-label2"
                            onChange={(e) => this.onRadioGroupChange(e, 'mnameinput')}
                            feedback={this.state.selectMnameError}
                          />
                        </div>
                      </div>
                    )}
                    {this.state.lName && (
                      <div className="row border-bottom pt-2">
                        <div className="col-md-6">
                          <label className="toplabel">Last Name *</label>
                          <p className="">{this.state.lName}</p>
                        </div>
                        <div className="col-md-6 pt-3">
                          <Input
                            elementType={'radio'}
                            elementConfig={lnameinput}
                            className={'form-radio1'}
                            selected={this.state.selectLname}
                            labelclass="radio-label2"
                            onChange={(e) => this.onRadioGroupChange(e, 'lnameinput')}
                            feedback={this.state.selectLnameError}
                          />
                        </div>
                      </div>
                    )}

                    <div className="row border-bottom pt-2">
                      <div className="col-md-6">
                        <label className="toplabel">Gender *</label>
                        <p className="">{this.state.gender}</p>
                      </div>
                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={'radio'}
                          elementConfig={genderinput}
                          className={'form-radio1'}
                          selected={this.state.selectgender}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'genderinput')}
                          feedback={this.state.genderError}
                        />
                      </div>
                    </div>
                  </div>

                  <h3 className="f-16 pt-3 ">Academic Details</h3>

                  <div className="col-md-12 pt-3">
                    <div className="row border-bottom">
                      <div className="col-md-6">
                        <label className="toplabel">Acadamic No *</label>
                        <p className="">{this.state.academic}</p>
                      </div>
                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={'radio'}
                          elementConfig={acainput}
                          className={'form-radio1'}
                          selected={this.state.selectAcadamic}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'Acadamic')}
                          feedback={this.state.acadamicError}
                        />
                      </div>
                    </div>

                    {/* <div className="row border-bottom pt-2">
                      <div className="col-md-6">
          
                        <label className="toplabel">Enrollment Year</label>
                        <p className="">{moment(this.state.enrollment).format("MMM Do YYYY")}</p>
                      </div>
                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={"radio"}
                          elementConfig={enrollinput}
                          className={"form-radio1"}
                          selected={this.state.selectEnrollment}
                          labelclass="radio-label2"
                          onChange={(e) =>
                            this.onRadioGroupChange(e, "Enrollment")
                          }
                          feedback={this.state.enrollmentError}
                        />
                      </div>
                    </div> */}
                    {!userSensitive && (
                      <div className="row border-bottom pt-2">
                        <div className="col-md-6">
                          <label className="toplabel">Resident ID / National ID *</label>
                          <p className="">{this.state.residendId}</p>
                        </div>
                        <div className="col-md-6 pt-3">
                          <Input
                            elementType={'radio'}
                            elementConfig={nationalinput}
                            className={'form-radio1'}
                            selected={this.state.selectNational}
                            labelclass="radio-label2"
                            onChange={(e) => this.onRadioGroupChange(e, 'nationalinput')}
                            feedback={this.state.selectNationalError}
                          />
                        </div>
                      </div>
                    )}
                    <div className="row border-bottom pt-2">
                      <div className="col-md-6">
                        <label className="toplabel">Program Name *</label>
                        <p className="">{selectedProgramName}</p>
                      </div>
                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={'radio'}
                          elementConfig={programinput}
                          className={'form-radio1'}
                          selected={this.state.selectProgram}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'program')}
                          feedback={this.state.programError}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

// export default studentProfile;
export default withRouter(withCountryCodeHooks(studentProfile));

studentProfile.propTypes = {
  history: PropTypes.func,
  mobileLengthMatch: PropTypes.func,
  countryCodeLength: PropTypes.func,
};
