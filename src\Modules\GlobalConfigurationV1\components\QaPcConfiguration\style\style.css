/* @import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  height: 100vh;
  width: 100vw;
  font-family: "Poppins", sans-serif;
  color: var(--gunmetal) !important;
} */

:root {
  --pale-blue-grey: #cfd8dc;
  --slate-gray: #6b7280;
  --gunmetal: #374151;
  --light-gray: #f3f4f6;
  --whitish-gray: #f9fafb;
  --sunshade-orange: #F59E0B;
  --light-steel-blue: #d1d5db;
  --vivid-red: #ef4444;
  --whisper-blue: #f7fcfd;
  --silver-chalice: #9ca3af;
  --Platinum: #e5e7eb;
  --light-blue: #e1f5fa;
  --slate-gray: #4B5563;
}
/* -----------------------------------Style Start-------------------------------------------------*/
.bold {
  font-weight: bold !important;
}
.fw-light {
  font-weight: 550 !important;
}
.cursor-pointer {
  cursor: pointer !important;
}
/* Set default scrollbar styles */
.course-qlc-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.course-qlc-scrollbar::-webkit-scrollbar-thumb {
  background: #c8c8c8;
  border-radius: 15px;
}

.course-qlc-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 15px;
}

/* Show scrollbar on hover */
.course-qlc-scrollbar:not(:hover)::-webkit-scrollbar {
  display: none;
}

.responsiveFontSizeSmall {
  font-size: clamp(0.75em, 2vw, 0.9em) !important;
}

.responsiveFontSizeMedium {
  font-size: clamp(0.875em, 2vw, 1.0625em) !important;
}
.responsiveFontSizeLarge {
  font-size: clamp(1.1em, 2.12vw, 1.3em) !important;
}
.btn-border-dotted {
  border: 1px dashed var(--light-steel-blue) !important;
}

/* -----------------------------------Style End---------------------------------------------------*/
/* -----------------------------------txt start---------------------------------------------------*/
.txt-pale-blue-grey {
  color: var(--pale-blue-grey) !important;
}
.txt-slate-gray {
  color: var(--slate-gray) !important;
}
.txt-gunmetal {
  color: var(--gunmetal) !important;
}
.txt-light-gray {
  color: var(--light-gray) !important;
}
.txt-sunshade-orange {
  color: var(--sunshade-orange) !important;
}
.txt-light-steel-blue {
  color: var(--light-steel-blue) !important;
}
.txt-vivid-red {
  color: var(--vivid-red) !important;
}
.txt-silver-chalice {
  color: var(--silver-chalice) !important;
}
.txt-slate-gray {
  color: var(--slate-gray) !important;
}
/* -----------------------------------gap End-----------------------------------------------------*/
/* Font Size Classes in em Units */

.action_only_disabled{
  pointer-events: none;
}

.border_qa_1{
  border-bottom: 1px solid grey;
}

.deepak_developer{
  font-size: 12px;
    color: gray;
    text-align: center;
}
.color-blue{
  background-color: #147AFC !important;
}
.level-approval::after{
  content:"✖";
  right: 0;
  position: absolute;
  border-radius: 50%;
  padding: 0 4px;
  cursor: pointer;
  transform: translate(30%,-30%);
  background-color:white;
  border: 1px solid #D3D7DD;
  width: 20px;
  height: 20px;
  font-size: 12px;
}
.popup-q360-overflow::-webkit-scrollbar {
  width: 5px;
  height: 10px;
}
.popup-q360-overflow::-webkit-scrollbar-thumb {
  background: #c8c8c8;
  border-radius: 15px;
}
.popup-q360-overflow::-webkit-scrollbar-track {
  background:transparent;
  border-radius: 15px;
}

.file-gallery-1 {
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 200px;
}