import { useState, useRef, useCallback } from 'react';

const useCaptcha = (failedAttemptsThreshold = 3) => {
  const [failedAttempts, setFailedAttempts] = useState(0);
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [captchaVerified, setCaptchaVerified] = useState(false);
  const captchaRef = useRef(null);

  const resetCaptcha = () => {
    if (captchaRef.current) {
      try {
        captchaRef.current.reset();
        setCaptchaVerified(false);
      } catch (e) {
        console.error('Error resetting CAPTCHA', e);
      }
    }
  };

  const incrementFailedAttempt = () => {
    setFailedAttempts((prev) => {
      const updated = prev + 1;
      if (updated >= failedAttemptsThreshold) {
        setShowCaptcha(true);
        setTimeout(resetCaptcha, 0); // Reset CAPTCHA widget on show
      }
      return updated;
    });
    setCaptchaVerified(false); // Reset verification state on failure
  };

  const onCaptchaChange = useCallback((token) => {
    setCaptchaVerified(!!token);
  }, []);

  return {
    failedAttempts,
    showCaptcha,
    captchaVerified,
    captchaRef,
    onCaptchaChange,
    incrementFailedAttempt,
  };
};

export default useCaptcha;
