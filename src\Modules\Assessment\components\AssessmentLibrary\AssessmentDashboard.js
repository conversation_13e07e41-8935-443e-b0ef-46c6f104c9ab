import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { List, fromJS, Map } from 'immutable';
import { useHistory } from 'react-router-dom';
import {
  formatTwoString,
  getURLParams,
  groupArray,
  removeURLParams,
  ucFirst,
  eString,
} from '../../../../utils';
import { Years } from '../../../../constants';
import { Alert } from '@mui/material';
import CustomReactProgressBar from 'Shared/CustomReactProgressBar';
import { CheckPermission } from 'Modules/Shared/Permissions';

function Dashboard({ assessmentTerm }) {
  const history = useHistory();
  const { location } = history;
  const { pathname } = location;
  const formatYearLevel = useCallback((yearLevel) => {
    const yearLevelArray = groupArray(yearLevel.toJS(), 'year');
    const pushArray = [];
    Object.keys(yearLevelArray).forEach(function (key) {
      pushArray.push({ year: key, level: yearLevelArray[key] });
    });
    return fromJS(pushArray);
  }, []);

  const term = getURLParams('term', true);
  const year = getURLParams('year', true);
  const level = getURLParams('level', true);

  const programAssessment = assessmentTerm.get('programAssessment', Map());
  const yearLevelList = formatYearLevel(assessmentTerm.get('yearLevelDetails', List()));

  function goToConfigure({ type = '' }) {
    const updatedTerm =
      term !== '' ? term : yearLevelList.getIn([0, 'level', 0, 'term'], 'regular');
    history.push(
      pathname +
        `/configure${removeURLParams(location, ['term', 'year', 'level', 'type'])}${
          updatedTerm !== '' ? `&term=${eString(updatedTerm)}` : ``
        }${type !== '' ? `&type=${eString(type)}` : ``}`
    );
  }

  function goToCourses({ type = '', year = '', level = '' }) {
    const updatedTerm =
      term !== '' ? term : yearLevelList.getIn([0, 'level', 0, 'term'], 'regular');
    history.push(
      pathname +
        `/configure/courses${removeURLParams(location, ['term', 'year', 'level', 'type'])}${
          updatedTerm !== '' ? `&term=${eString(updatedTerm)}` : ``
        }${type !== '' ? `&type=${eString(type)}` : ``}${
          year !== '' ? `&year=${eString(year)}` : ``
        }${level !== '' ? `&level=${eString(level)}` : ``}`
    );
  }
  return (
    <div className="p-3">
      {assessmentTerm.isEmpty() ? (
        <Alert severity="error">No Assessment Plan Found</Alert>
      ) : year === '' || year === 'assess' ? (
        <>
          <p className="f-20 mb-3 bold"> Program Level</p>
          <div className="levelBox mb-3">
            <div className="row align-items-center">
              <div className="col-md-6">
                <div className="row align-items-center">
                  <div className="col-md-6 mb-0">
                    <p className="ml-2 mb-0"> Program Assessment </p>
                  </div>
                  <div className="col-md-6 mb-0">
                    <p className="ml-2 mb-0"> Program Level </p>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="row align-items-center">
                  {programAssessment.get('assessmentMode', List()).map((assessment, index) => {
                    return (
                      <div className="col-md-5" key={index}>
                        <p className="mb-2">
                          {' '}
                          {ucFirst(assessment.get('typeName', ''))} -{' '}
                          {formatTwoString(assessment.get('assessmentAssignedCount', '00'))}/
                          {formatTwoString(assessment.get('assessmentCount', '00'))} Assessment{' '}
                        </p>
                        <div className="">
                          <CustomReactProgressBar
                            variant="primary"
                            total={assessment.get('assessmentCount', 0)}
                            now={assessment.get('assessmentAssignedCount', 0)}
                            className="border-radious-8 height-13"
                          />
                        </div>
                      </div>
                    );
                  })}
                  {CheckPermission(
                    'tabs',
                    'Assessment Management',
                    'Assessment Library',
                    '',
                    'Program / Course Assessments',
                    'View'
                  ) && (
                    <div className="col-md-2" onClick={() => goToConfigure({ type: 'program' })}>
                      <p className="mb-0 remove_hover f-15 bold"> VIEW </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </>
      ) : year === '' || year !== 'assess' ? (
        <>
          <p className="f-20 mb-3 bold"> Years &amp; Levels</p>
          <div className="border-bottom mb-2">
            <div className="row align-items-center">
              <div className="col-md-6">
                <div className="row align-items-center">
                  <div className="col-md-6 mb-0">
                    <p className="mb-0"> Year &amp; Levels </p>
                  </div>
                  <div className="col-md-6 mb-0">
                    <p className="mb-0"> Courses </p>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="row align-items-center">
                  <div className="col-md-5">
                    <p className="mb-0">Direct Assessment </p>
                  </div>
                  <div className="col-md-5">
                    <p className="mb-0">Indirect Assessment </p>
                  </div>
                  <div className="col-md-2"></div>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        ''
      )}
      {yearLevelList.size > 0 &&
        yearLevelList
          .filter((item) => (year === '' ? item : item.get('year') === year))
          .map((yearLevel, yIndex) => {
            return (
              <React.Fragment key={yIndex}>
                <p className="f-17 mb-3 mt-3 bold">
                  {Years[yearLevel.get('year').replace('year', '')].name}
                </p>
                {yearLevel
                  .get('level', List())
                  .filter((item) =>
                    year === '' ? item : level === '' ? item : item.get('level', '') === level
                  )
                  .map((level, lIndex) => {
                    return (
                      <div className="levelBox mb-2" key={lIndex}>
                        <div className="row align-items-center">
                          <div className="col-md-6">
                            <div className="row align-items-center">
                              <div className="col-md-6 mb-0">
                                <p className="ml-2 mb-0">{level.get('level', '')} </p>
                              </div>
                              <div className="col-md-6 mb-0">
                                <p className="ml-2 mb-0">
                                  {' '}
                                  {formatTwoString(level.get('courseIds', List()).size)} - Courses{' '}
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="col-md-6">
                            <div className="row align-items-center">
                              {level.get('assessmentMode', List()).map((assessment, index) => {
                                return (
                                  <div className="col-md-5" key={index}>
                                    <p className="mb-2">
                                      {' '}
                                      {ucFirst(assessment.get('typeName', ''))} -{' '}
                                      {formatTwoString(
                                        assessment.get('assessmentAssignedCount', '00')
                                      )}
                                      /{formatTwoString(assessment.get('assessmentCount', '00'))}{' '}
                                      Assessment{' '}
                                    </p>
                                    <div className="">
                                      <CustomReactProgressBar
                                        variant="primary"
                                        total={assessment.get('assessmentCount', 0)}
                                        now={assessment.get('assessmentAssignedCount', 0)}
                                        className="border-radious-8 height-13"
                                      />
                                    </div>
                                  </div>
                                );
                              })}
                              {CheckPermission(
                                'tabs',
                                'Assessment Management',
                                'Assessment Library',
                                '',
                                'Program / Course Assessments',
                                'View'
                              ) && (
                                <div
                                  className="col-md-2"
                                  onClick={() =>
                                    goToCourses({
                                      type: 'course',
                                      year: yearLevel.get('year'),
                                      level: level.get('level', ''),
                                    })
                                  }
                                >
                                  <p className="mb-0 remove_hover f-15 bold"> VIEW </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </React.Fragment>
            );
          })}
    </div>
  );
}

Dashboard.propTypes = {
  assessmentTerm: PropTypes.instanceOf(Map),
};
export default Dashboard;
