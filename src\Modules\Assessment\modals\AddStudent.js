import React, { useEffect, useState } from 'react';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { fromJS, List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { gender } from '../utils';
import { Delete } from '@mui/icons-material';

export default function AddStudent(props) {
  const { show, cancel, data, setData, toastFunc, list, setList, defaultCount } = props;
  const qnsArray = () => {
    let tempArr = [];
    for (let i = 0; i < data.get('noQuestions', 1); i++) {
      const quest = 'Q' + (i + 1);

      tempArr.push({ mark: null, questionName: quest });
    }
    return fromJS(tempArr);
  };
  const [studentData, setStudentData] = useState(
    fromJS([
      {
        name: '',
        studentId: '',
        gender: 'male',
        isDefault: false,
        attendance: true,
        studentMarks: qnsArray(),
      },
    ])
  );

  const handleAddStudentCount = () => {
    studentData.getIn([studentData.size - 1, 'name'], '') !== '' &&
    studentData.getIn([studentData.size - 1, 'studentId'], '') !== '' &&
    studentData.getIn([studentData.size - 1, 'gender'], '') !== ''
      ? setStudentData(
          studentData.set(
            studentData.size,
            fromJS({
              name: '',
              studentId: '',
              gender: 'male',
              isDefault: false,
              attendance: true,
              studentMarks: qnsArray(),
            })
          )
        )
      : toastFunc(
          Map({
            message: `Please fill all fields`,
          })
        );
  };

  const handleChange = (field, value, index) => {
    if (field === 'studentId') {
      setStudentData(studentData.setIn([index, field], value));
    } else {
      setStudentData(studentData.setIn([index, field], value));
    }
  };
  const handleRemove = (e, index, item) => {
    setStudentData(studentData.filter((_, fIndex) => fIndex !== index));
  };

  const callBack = (studentDetailsArray) => {
    setList(list.set('studentData', studentDetailsArray.slice(0, defaultCount)));
  };

  const handleSave = () => {
    let studentDetailsArray = data.get('studentDetails', List());
    studentDetailsArray = studentData.concat(studentDetailsArray);
    toastFunc(Map({ message: 'Saved Successfully' }));
    setData(data.set('studentDetails', studentDetailsArray));
    callBack(studentDetailsArray);
    cancel();
  };

  function findDuplicates() {
    const duplicates = studentData.toJS().reduce((acc, obj, index, arr) => {
      const duplicateIndex = arr.findIndex(
        (item) => item.studentId === obj.studentId && item !== obj
      );
      if (duplicateIndex !== -1 && !acc.includes(obj)) {
        acc.push(obj);
      }
      return acc;
    }, []);
    return duplicates.length > 0;
  }
  const valid = findDuplicates();
  useEffect(() => {
    if (valid) {
      toastFunc(Map({ message: 'Student already exist' }));
    }
  }, [valid]); //eslint-disable-line

  return (
    <MaterialDialog show={show} onClose={cancel} maxWidth={'md'} fullWidth={true}>
      <div className="w-100 p-4">
        <p className="mb-3 pb-2 border-bottom bold f-19"> Add Student </p>
        <div className="mt-2 mb-2">
          <div className="program_table">
            <table align="left">
              <thead>
                <tr>
                  <th>
                    <div className=""> S no.</div>
                  </th>

                  <th>
                    <div className=""> Student Name</div>
                  </th>

                  <th>
                    <div className=""> Student ID</div>
                  </th>
                  <th>
                    <div className=""> Gender</div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {studentData.map((item, index) =>
                  index + 1 === studentData.size ? (
                    <tr key={index} className="tr-change">
                      <td className="w-15">
                        <div className="mt-2">{index + 1}</div>{' '}
                      </td>
                      <td className="">
                        <div className="mt-2">
                          {' '}
                          <MaterialInput
                            elementType={'materialInput'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                            placeholder={'Name'}
                            value={item.get('name', '')}
                            changed={(e) => handleChange('name', e.target.value, index)}
                          />
                        </div>
                      </td>
                      <td className="">
                        <div className="mt-2">
                          {' '}
                          <MaterialInput
                            elementType={'materialInput'}
                            type={'text'}
                            variant={'outlined'}
                            placeholder={'ID'}
                            size={'small'}
                            value={item.get('studentId', '')}
                            changed={(e) => handleChange('studentId', e.target.value, index)}
                          />
                        </div>
                      </td>
                      <td className="w-15">
                        <div className="mt-2">
                          {' '}
                          <MaterialInput
                            elementType={'materialSelect'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                            value={item.get('gender', '')}
                            changed={(e) => handleChange('gender', e.target.value, index)}
                            elementConfig={{ options: gender }}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="mt-2 remove_hover" onClick={(e) => handleRemove(e, index)}>
                          <Delete />
                        </div>
                      </td>
                    </tr>
                  ) : (
                    <tr key={index} className="tr-change">
                      <td className="">
                        <div className="mt-2">{index + 1}</div>
                      </td>
                      <td className="">
                        <div className="mt-2">{item.get('name', '')}</div>
                      </td>
                      <td className="">
                        <div className="mt-2">{item.get('studentId', '')}</div>
                      </td>
                      <td className="">
                        <div className="mt-2">{item.get('gender', '')}</div>
                      </td>
                      <td>
                        <div
                          className="mt-2 remove_hover"
                          onClick={(e) => handleRemove(e, index, item)}
                        >
                          <Delete />
                        </div>
                      </td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          </div>
        </div>
        <div className="mt-3 mb-2">
          <p
            className={`mb-0 pb-2 bold  text-right ${
              !valid ? 'remove_hover text-skyblue' : 'text-gray'
            }`}
            onClick={!valid && handleAddStudentCount}
          >
            + Add New{' '}
          </p>
        </div>

        <div className="d-flex justify-content-end border-top pt-3">
          <MButton variant="outlined" color="primary" className={'mr-2'} clicked={cancel}>
            Cancel
          </MButton>
          <MButton
            variant="contained"
            color="primary"
            clicked={handleSave}
            disabled={
              valid ||
              !studentData.size ||
              studentData.getIn([studentData.size - 1, 'name'], '') === '' ||
              studentData.getIn([studentData.size - 1, 'studentId'], '') === '' ||
              studentData.getIn([studentData.size - 1, 'gender'], '') === ''
            }
          >
            Save
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
}
AddStudent.propTypes = {
  show: PropTypes.bool,
  cancel: PropTypes.func,
  data: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  toastFunc: PropTypes.func,
  list: PropTypes.instanceOf(Map),
  setList: PropTypes.func,
  defaultCount: PropTypes.number,
};
