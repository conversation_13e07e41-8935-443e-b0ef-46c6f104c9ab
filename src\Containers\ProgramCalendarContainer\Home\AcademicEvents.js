import React, { Fragment } from "react";
import { connect } from "react-redux";
import { useRouteMatch } from "react-router-dom";
import styled from "styled-components";
import { row_start, row_end, mergeEvents } from "../../../_utils/function";
import { coursePopUp } from "../../../_reduxapi/actions/calender";
import useDataFromStore from "../UtilityComponents/useDataFromStore";

const AcadEvents = styled.div`
  background-color: ${(props) =>
    props.bg
      ? props.bg
      : props.type === "holiday"
      ? "rgba(0, 0, 0, 0.54)"
      : "rgba(52, 50, 50, 0.54)"};
  grid-row: ${(props) =>
    props.check
      ? `${props.st_date + 1} / ${props.end_date + 1}`
      : `${props.st_date} / ${props.end_date}`};
  display: flex;
  text-align: center;
  align-items: center;
  z-index: 2;
  grid-column: 1 / -1;
  color: white;
`;

const Text = styled.div`
  flex: 1;
  font-size: 24px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const AcademicEvents = (props) => {
  const { rotate, elective, elective_data, coursePopUp, choose } = props;
  const match = useRouteMatch();
  const active = match.params.year || "year2";

  const { events, events1, start, start1 } = useDataFromStore();

  const common_start = choose === 0 ? start : start1;
  const common_events = choose === 0 ? events : events1;

  return (
    <Fragment>
      {props[active] && elective
        ? elective_data &&
          elective_data.map((item, i) => (
            <AcadEvents
              key={i}
              check={rotate}
              type={"holiday"}
              st_date={row_start(common_start, item.start_date)}
              end_date={row_end(common_start, item.end_date)}
              bg={item.color_code}
              onClick={() => {
                coursePopUp(
                  "edit_course",
                  "level_one_rotation_courses",
                  i,
                  props[active]["level_one_title"],
                  "level_one_columns",
                  "level_one_start_date",
                  "level_one_end_date",
                  "level_one_course_events",
                  "elective_course",
                  0
                );
              }}
            >
              {" "}
              <Text>{item.courses_name}</Text>
            </AcadEvents>
          ))
        : common_events &&
          (() => {
            const { output_events } = mergeEvents(common_events);

            return output_events.map((item, i) => (
              <AcadEvents
                key={i}
                check={rotate}
                type={item.event_type}
                st_date={row_start(common_start, item.event_date)}
                end_date={row_end(common_start, item.end_date)}
                bg=""
              >
                {" "}
                <Text>{item.event_name}</Text>
              </AcadEvents>
            ));
          })()}
    </Fragment>
  );
};

AcadEvents.defaultProps = {
  elective: false,
  elective_data: [],
};

const mapStateToProps = ({ calender }) => ({
  // start: calender.academic_year_start,
  // active: calender.active_year,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
});

export default connect(mapStateToProps, { coursePopUp })(AcademicEvents);
