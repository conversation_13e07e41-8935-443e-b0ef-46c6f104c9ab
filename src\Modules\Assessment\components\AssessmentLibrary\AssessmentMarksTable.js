import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';
import InputAdornment from '@mui/material/InputAdornment';
import MaterialInput from 'Widgets/FormElements/material/Input';
import dotIcon from '../../../../Assets/dotIcon.svg';
import { isEmptyObject, ucFirst, getURLParams, indVerRename } from '../../../../utils';
import EditStudent from 'Modules/Assessment/modals/EditStudent';
import DeleteModal from 'Modules/Assessment/modals/DeleteModal';
import ToggleAttendanceModal from 'Modules/Assessment/modals/ToggleAttendance';
import Tooltips from 'Widgets/FormElements/material/Tooltip';

function AssessmentMarksTable({
  data,
  type,
  setData,
  toastFunc,
  maxMarks,
  studentIds,
  mode,
  globalOutcomeList,
  list,
  setList,
  defaultCount,
}) {
  const programId = getURLParams('pid', true);
  const outComeType =
    type === 'program'
      ? [
          {
            name: `${indVerRename('PLO', programId)} - Program Learning Outcome`,
            value: 'PLO',
          },
        ]
      : [
          {
            name: `${indVerRename('CLO', programId)} - Course Learning Outcome`,
            value: 'CLO',
          },
          {
            name: `${indVerRename('SLO', programId)} - Course Learning Outcome`,
            value: 'SLO',
          },
        ];
  const [editModal, setEditModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [attendanceModal, setAttendanceModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(fromJS({}));

  const handleEditModalClose = () => {
    setEditModal(false);
  };
  const handleDeleteModalClose = () => {
    setDeleteModal(false);
  };
  const handleAttendanceModalClose = () => {
    setAttendanceModal(false);
  };

  const handleEditOpen = (e, index, editStudent) => {
    setSelectedStudent(fromJS({ student: editStudent, index }));
    setEditModal(true);
  };
  const handleAttendanceOpen = (e, index, editStudent) => {
    setSelectedStudent(fromJS({ student: editStudent, index }));
    setAttendanceModal(true);
  };
  const handleDeleteOpen = (e, index, deleteStudent) => {
    setSelectedStudent(fromJS({ student: deleteStudent, index }));
    setDeleteModal(true);
  };
  const handleDelete = () => {
    let updatedArr = data
      .get('studentDetails', List())
      .filter(
        (item) => item.get('studentId', '') !== selectedStudent.getIn(['student', 'studentId'], '')
      );
    let updatedList = list
      .get('studentData', List())
      .filter(
        (item) => item.get('studentId', '') !== selectedStudent.getIn(['student', 'studentId'], '')
      );
    setData(data.set('studentDetails', updatedArr));
    setList(list.set('studentData', updatedList));
    toastFunc(Map({ message: 'Updated Successfully' }));
    handleDeleteModalClose();
  };
  const studentList = data.get('studentDetails', List());
  function handleStudentMarkChange(e, student, index) {
    const value = e.target.value;

    if (value <= data.getIn(['questionMarks', index, 'totalMark'], '') || value === '-') {
      let updatedData = data;
      let listData = list;
      const studentIndex = data
        .get('studentDetails', List())
        .findIndex((item) => item.get('studentId', '') === student.get('studentId'));
      if (studentIndex > -1) {
        const HyphenIncludes =
          value === '--' ? '' : value.includes('-') ? value.replace('-', '') : null;
        const settingValue = value === '' ? value : value.includes('-') ? HyphenIncludes : value;
        updatedData = updatedData.setIn(
          ['studentDetails', studentIndex, 'studentMarks', index, 'mark'],
          settingValue
        );
        if (settingValue >= 0 || settingValue === '' || settingValue === '-') {
          setData(updatedData);
          setList(
            listData.setIn(
              ['studentData', studentIndex, 'studentMarks', index, 'mark'],
              settingValue
            )
          );
        }
      }
    } else {
      toastFunc(
        Map({
          message:
            data.getIn(['questionMarks', index, 'totalMark'], '') === '' ||
            data.getIn(['questionMarks', index, 'totalMark'], '') === null
              ? 'Please enter maximum marks for the question'
              : isNaN(value)
              ? 'Please enter numbers only'
              : `Maximum marks is ${data.getIn(['questionMarks', index, 'totalMark'], '-')}`,
        })
      );
    }
  }
  function handleChange(e, name, index = 0) {
    const value = e.target.value;
    let updatedData = data;
    switch (name) {
      case 'questionOutcome': {
        updatedData = updatedData.set(name, value);
        setData(updatedData);
        break;
      }
      case 'outComeIds': {
        updatedData = updatedData.setIn(['questionMarks', index, 'outComeIds'], fromJS([value]));
        setData(updatedData);
        break;
      }
      case 'totalMark': {
        let actualMarks = data
          .get('questionMarks', List())
          .filter((qn, i) => index !== i)
          .map((item, index) => item.get('totalMark', null))
          .reduce((acc, el) => Number(acc) + Number(el), parseInt(value));
        if (actualMarks > maxMarks) {
          toastFunc(Map({ message: `Maximum total marks is ${maxMarks}` }));
          return;
        }
        const HyphenIncludes =
          value === '--' ? '' : value.includes('-') ? parseInt(value.replace('-', '')) : null;
        const settingValue =
          value === '' ? value : value.includes('-') ? HyphenIncludes : parseInt(value);
        updatedData = data
          .setIn(['questionMarks', index, name], settingValue)
          .setIn(
            ['questionMarks', index, 'attainmentBenchMark'],
            value === ''
              ? value
              : value === '-'
              ? null
              : (data.get('benchMark', 100) / 100) * parseInt(settingValue)
          );
        (settingValue >= 0 || settingValue === '-' || settingValue === '') && setData(updatedData);

        break;
      }
      case 'attainmentBenchMark': {
        if (value > data.getIn(['questionMarks', index, 'totalMark'], '')) {
          toastFunc(
            Map({
              message:
                data.getIn(['questionMarks', index, 'totalMark'], '') === null ||
                data.getIn(['questionMarks', index, 'totalMark'], '') === ''
                  ? 'Please enter maximum marks for the question'
                  : `Maximum attainment bench marks is ${data.getIn(
                      ['questionMarks', index, 'totalMark'],
                      ''
                    )}`,
            })
          );
        } else {
          const HyphenIncludes =
            value === '--' ? '' : value.includes('-') ? parseInt(value.replace('-', '')) : null;
          const settingValue =
            value === '' ? value : value.includes('-') ? HyphenIncludes : parseInt(value);
          updatedData = updatedData.setIn(['questionMarks', index, name], settingValue);
          (settingValue >= 0 || settingValue === '-' || settingValue === '') &&
            setData(updatedData);
        }
        break;
      }
      case 'benchMark': {
        if (value <= 100) {
          const HyphenIncludes =
            value === '--' ? '' : value.includes('-') ? parseInt(value.replace('-', '')) : null;
          updatedData = data.set(name, value === '' ? value : parseInt(value)).set(
            'questionMarks',
            data
              .get('questionMarks', List())
              .map((item) =>
                item.set(
                  'attainmentBenchMark',
                  value === ''
                    ? value
                    : value.includes('-')
                    ? HyphenIncludes
                    : (item.get('totalMark', 0) * parseInt(value)) / 100
                )
              )
          );
          setData(updatedData);
        }
        break;
      }
      default:
        updatedData = updatedData.set(name, value);
        setData(updatedData);
        break;
    }
  }

  let dataTable = React.createRef();
  const onScroll = () => {
    let tableEl = dataTable.current;
    if (tableEl.scrollHeight - tableEl.scrollTop <= tableEl.clientHeight + 1) {
      if (list.get('itemsDisplayed', 0) <= studentList.size) {
        let data = list
          .set('itemsDisplayed', list.get('itemsDisplayed', 0) + defaultCount)
          .set('studentData', studentList.slice(0, list.get('itemsDisplayed', 0) + defaultCount));
        setList(data);
      }
    }
  };

  const questionMarks = data.get('questionMarks', List());
  const benchMark = data.get('benchMark', '-');

  const getStudentListsData = () => {
    return list.get('studentData', List()).map((student, index) => {
      return (
        <tr key={index}>
          <th scope="col" className="bg-white">
            <div className="d-flex custom_space align-items-center">
              <div className="w-9">
                <p className="mb-0 mr-2 text-center">{index + 1}</p>
              </div>
              <div className="mr-3 w-75">
                <p className="mb-0">{student.get('name', '')}</p>
                <span className="mr-1 f-14"> {student.get('studentId', '')} </span>
                <img src={dotIcon} alt="Deactivated" />
                <span className="mr-1 ml-1 f-14"> {ucFirst(student.get('gender', ''))} </span>
                <img src={dotIcon} alt="Deactivated" />
                <span className="mr-1 ml-1 f-14"> L9 </span>
                <img src={dotIcon} alt="Deactivated" />
              </div>
              <div className="d-flex align-items-center justify-content-end ml-3 mt-3 pt-2 remove_hover">
                <p
                  className={`mb-0 f-14 mr-3 ${
                    student.get('attendance', false) ? 'text-green' : 'text-red'
                  }`}
                  onClick={(e) =>
                    mode === 'view' ? () => {} : handleAttendanceOpen(e, index, student)
                  }
                >
                  {student.get('attendance', false) ? 'P' : 'A'}
                </p>
                {!student.get('isDefault', false) && (
                  <i
                    className="fa fa-pencil text-gray pr-3 f-16 remove_hover"
                    onClick={(e) => handleEditOpen(e, index, student)}
                  ></i>
                )}
                {!student.get('isDefault', false) && (
                  <i
                    className="fa fa-trash text-gray f-16 remove_hover"
                    onClick={(e) => handleDeleteOpen(e, index, student)}
                  ></i>
                )}
              </div>
            </div>
          </th>
          {getStudentMarkHeader(student)}
        </tr>
      );
    });
  }; //eslint-disable-line

  function getStudentMarkHeader(student) {
    return student.get('studentMarks', List()).map((item, i) => {
      return (
        <td className={`${!student.get('attendance', false) ? 'bg-gray' : ''}`} key={i}>
          <div className={`cw_100 custom_space`}>
            <input
              type="text"
              disabled={mode === 'view' || !student.get('attendance', false)}
              value={item.get('mark', '') === null ? '-' : item.get('mark', '')}
              onChange={(e) => handleStudentMarkChange(e, student, i)}
              className="normalInput"
            />
          </div>
        </td>
      );
    });
  }

  const getBenchMarkHeader = useMemo(() => {
    return questionMarks.map((question, index) => {
      return (
        <td className="borderNone bg-gray" key={index}>
          <div className="cw_100 custom_space">
            <input
              type="text"
              disabled={mode === 'view'}
              value={
                question.get('attainmentBenchMark', '') === null ||
                question.get('attainmentBenchMark', '') === ''
                  ? '-'
                  : parseFloat(question.get('attainmentBenchMark', '').toFixed(2))
              }
              onChange={(e) => handleChange(e, 'attainmentBenchMark', index)}
              className="normalInput"
            />
          </div>
        </td>
      );
    });
  }, [questionMarks, benchMark, mode]); //eslint-disable-line

  const getQuestionHeader = useMemo(() => {
    return questionMarks.map((question, index) => {
      return (
        <th scope="col" className="borderNone" key={index}>
          <div className="cw_100 custom_space">
            <p className="thHeader">{question.get('questionName', '')}</p>
          </div>
        </th>
      );
    });
  }, [questionMarks]); //eslint-disable-line

  const outcomeList = globalOutcomeList;
  const getOutComeHeader = useMemo(() => {
    return questionMarks.map((question, index) => {
      const outcomeName = outcomeList.find(
        (item) => item?.value === question.getIn(['outComeIds', 0], '')
      );
      return (
        <td className="borderNone bg-gray" key={index}>
          <Tooltips title={outcomeName?.name}>
            <div className="cw_100 custom_space">
              <MaterialInput
                elementType={'materialSelectMultiple'}
                disabled={mode === 'view'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{ options: isEmptyObject(outcomeList) ? [] : outcomeList }}
                label={''}
                labelclass={'mb-0 f-14'}
                changed={(e) => handleChange(e, 'outComeIds', index)}
                isAllSelected={false}
                value={question.get('outComeIds', List()).toJS()}
                multiple={false}
                iconType={'radio'}
              />
            </div>
          </Tooltips>
        </td>
      );
    });
  }, [questionMarks, benchMark, outcomeList, studentList, mode]); //eslint-disable-line

  const getTotalMarkHeader = useMemo(() => {
    return questionMarks.map((question, index) => {
      return (
        <td className="borderNone bg-gray" key={index}>
          <div className="cw_100 custom_space">
            <input
              type="text"
              disabled={mode === 'view'}
              value={question.get('totalMark', '') === null ? '-' : question.get('totalMark', '')}
              onChange={(e) => handleChange(e, 'totalMark', index)}
              className="normalInput"
            />
          </div>
        </td>
      );
    });
  }, [questionMarks, benchMark, studentList, mode]); //eslint-disable-line
  return (
    <div className="bg-gray pl-3 pr-3">
      <div className="assessment_table" ref={dataTable} onScroll={onScroll}>
        <table align="left" id="data-table">
          <thead>
            <tr>
              <th scope="col" className="borderNone">
                <div className="cw_350 custom_space">
                  <p className="thHeader text-left">Questions</p>
                </div>
              </th>
              {getQuestionHeader}
            </tr>
          </thead>
          <tbody>
            <tr>
              <th scope="col" className="borderNone">
                <div className="row custom_space assessment_outcome_select">
                  <div className="col-md-12">
                    <MaterialInput
                      elementType={'materialSelectNew'}
                      disabled={mode === 'view'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{ options: outComeType }}
                      changed={(e) => handleChange(e, 'questionOutcome')}
                      value={data.get('questionOutcome')}
                    />
                  </div>
                </div>
              </th>
              {getOutComeHeader}
            </tr>

            <tr>
              <th scope="col" className="borderNone">
                <div className="row custom_space">
                  <div className="col-md-12">
                    <p className="thHeader text-left">Total Mark</p>
                  </div>
                </div>
              </th>
              {getTotalMarkHeader}
            </tr>

            <tr>
              <th scope="col" className="borderNone">
                <div className="row custom_space align-items-center">
                  <div className="col-md-9">
                    <p className="thHeader text-left">Attainment Benchmark</p>
                  </div>
                  <div className="cw_80">
                    <MaterialInput
                      fullWidth={true}
                      elementType={'materialInput'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      label={''}
                      disabled={mode === 'view'}
                      inputAdornment={<InputAdornment position="end">%</InputAdornment>}
                      changed={(e) => handleChange(e, 'benchMark')}
                      value={data.get('benchMark')}
                    />
                  </div>
                </div>
              </th>
              {getBenchMarkHeader}
            </tr>
            {getStudentListsData()}
          </tbody>
        </table>
      </div>
      {editModal && (
        <EditStudent
          show={editModal}
          cancel={handleEditModalClose}
          data={data}
          setData={setData}
          toastFunc={toastFunc}
          editData={selectedStudent}
          studentIds={studentIds}
          index={selectedStudent?.get('index', 1)}
          list={list}
          setList={setList}
        />
      )}
      {deleteModal && (
        <DeleteModal
          show={deleteModal}
          cancel={handleDeleteModalClose}
          data={data}
          setData={setData}
          deleteData={selectedStudent}
          componentName={'Student'}
          fileName={selectedStudent?.getIn(['student', 'name'], '')}
          index={selectedStudent?.get('index', 1)}
          handleDelete={handleDelete}
          mode={'student'}
        />
      )}
      {attendanceModal && (
        <ToggleAttendanceModal
          show={attendanceModal}
          cancel={handleAttendanceModalClose}
          data={data}
          setData={setData}
          toastFunc={toastFunc}
          studentName={selectedStudent?.getIn(['student', 'name'], '')}
          index={selectedStudent?.get('index', 1)}
          attendance={selectedStudent?.getIn(['student', 'attendance'], '')}
          list={list}
          setList={setList}
        />
      )}
    </div>
  );
}

AssessmentMarksTable.propTypes = {
  type: PropTypes.string,
  globalOutcomeList: PropTypes.array,
  data: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  toastFunc: PropTypes.func,
  maxMarks: PropTypes.string,
  studentIds: PropTypes.instanceOf(List),
  mode: PropTypes.string,
  list: PropTypes.instanceOf(Map),
  setList: PropTypes.func,
  defaultCount: PropTypes.number,
};

export default AssessmentMarksTable;
