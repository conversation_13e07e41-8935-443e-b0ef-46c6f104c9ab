import React, {
  forwardRef,
  lazy,
  Suspense,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import PropTypes from 'prop-types';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  // ButtonGroup,
  Checkbox,
  Divider,
  IconButton,
  List as MenuList,
  ListItemText,
  ListSubheader,
  Menu,
  MenuItem,
  Popover,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
} from '@mui/material';
import { fromJS, List, Map } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import SearchIcon from '@mui/icons-material/Search';
import MButton from 'Widgets/FormElements/material/Button';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import AddIcon from '@mui/icons-material/Add';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import EditIcon from '@mui/icons-material/Edit';
import ErrorRoundedIcon from '@mui/icons-material/ErrorRounded';
import DoneIcon from '@mui/icons-material/Done';
import DriveFileMoveRoundedIcon from '@mui/icons-material/DriveFileMoveRounded';
import DriveFileMoveOutlinedIcon from '@mui/icons-material/DriveFileMoveOutlined';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import FilterListIcon from '@mui/icons-material/FilterList';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import MuiPagination from './Pagination';
import HowToRegOutlinedIcon from '@mui/icons-material/HowToRegOutlined';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectGroupSettings,
  selectProgramYearLevel,
  selectUserPrograms,
} from '_reduxapi/studentGroupV2/selectors';
import {
  addGroupSettings,
  deleteStudents,
  editGroupSettings,
  getGroupSettings,
  getProgramYearLevel,
  getUserPrograms,
  getYearWiseStudents,
  resetMessage,
  setData,
} from '_reduxapi/studentGroupV2/action';
import {
  formatToTwoDigitString,
  getVersionName,
  isGenderMerge,
  jsUcfirstAll,
  levelRename,
} from 'utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import moment from 'moment';
import SearchInput from './SearchInput';
import EditOffIcon from '@mui/icons-material/EditOff';
import FileDownloadOffOutlinedIcon from '@mui/icons-material/FileDownloadOffOutlined';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
// import SettingsIcon from '@mui/icons-material/Settings';

// const GenderSegregationModal = lazy(() => import('./Modals/GenderSegregationModal'));
const DeliveryGroupsModal = lazy(() => import('./Modals/DeliveryGroupsModal'));
const ImportStudentsModal = lazy(() => import('./Modals/ImportStudentsModal'));
const GroupingModal = lazy(() => import('./Modals/GroupingModal'));
const AddStudentModal = lazy(() => import('./Modals/AddStudentModal'));
const DeleteConfirmModal = lazy(() => import('./Modals/DeleteConfirmModal'));
const PublishConfirmModal = lazy(() => import('./Modals/PublishConfirmModal'));
const MoveGroupedModal = lazy(() => import('./Modals/MoveGroupedModal'));
const ViewImportedStudents = lazy(() => import('./Modals/ViewImportedStudents'));

const UNGROUPED = 'unGrouped';
const GROUPED = 'grouped';

// const menuProps = {
//   PaperProps: {
//     sx: {
//       maxHeight: 250,
//     },
//   },
//   anchorOrigin: {
//     vertical: 'bottom',
//     horizontal: 'left',
//   },
//   transformOrigin: {
//     vertical: 'top',
//     horizontal: 'left',
//   },
// };
const boxSx = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  color: '#6B7280',
};
const buttonSx = {
  padding: '4px 16px',
  color: '#0064C8',
  '& .MuiButton-startIcon': {
    marginRight: '4px',
  },
};
const iconSx = {
  fontSize: '0.9rem',
};
const circleIconSx = {
  fontSize: '1rem',
  marginRight: '4px',
};
const primaryColorSx = {
  color: '#0064C8',
};
const accordionSx = {
  border: '1px solid #E5E7EB',
  borderRadius: '12px !important',
  backgroundColor: '#F9FAFB',
  // paddingRight: '16px',
  '&:before': {
    display: 'none',
  },
  '&:not(:last-of-type)': {
    marginBottom: '12px',
  },
  '&:hover': {
    borderColor: '#ccc',
    backgroundColor: '#fff',
  },
  '&.Mui-expanded': {
    borderColor: '#0064C8',
    backgroundColor: '#fff',
  },
};
const accordionSummarySx = {
  flexDirection: 'row-reverse',
  padding: 0,
  '&.Mui-focusVisible': {
    backgroundColor: 'transparent',
  },
  '& .MuiAccordionSummary-expandIconWrapper': {
    position: 'absolute',
    top: '15px',
    left: '12px',
    padding: '4px',
    border: '1px solid #D1D5DB',
    borderRadius: '50%',
    backgroundColor: '#F9FAFB',
  },
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(90deg)',
    color: '#0064C8',
    border: '1px solid #53AEF9',
    backgroundColor: '#F7FCFD',
  },
  '& .MuiAccordionSummary-content': {
    margin: 0,
    flexDirection: 'column',
  },
  '&:focus-visible > .MuiAccordionSummary-expandIconWrapper': {
    boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.5)',
  },
};
const accordionFirstRowSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '12px 0 12px 44px',
  minHeight: '56px',
};
const accordionDetailsSx = {
  padding: 0,
};
const deliveryGroupsSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '14px',
  color: '#15803D',
  width: '156px',
};
const groupInfoSx = {
  display: 'flex',
  alignItems: 'center',
  marginRight: '16px',
  padding: '3px 8px',
  fontSize: '12px',
  borderRadius: '8px',
};
const errorInfoSx = {
  color: '#DC2626',
  backgroundColor: '#FEE2E2',
};
const successInfoSx = {
  color: '#15803D',
  backgroundColor: '#DCFCE7',
};
const buttonGroupSx = {
  padding: '4px',
  border: '1px solid #E5E7EB',
  borderRadius: '8px',
  backgroundColor: '#F3F4F6',
};
const groupButtonSx = {
  padding: '4px 12px',
  border: 0,
  textTransform: 'none',
  borderRadius: '8px !important',
  color: '#4B5563',
  fontWeight: 'normal',
  '&.Mui-selected': {
    color: '#0064C8',
    backgroundColor: '#fff',
    fontWeight: 500,
    boxShadow: '0 1px 3px 0 #11182733',
    '&:hover': {
      backgroundColor: '#fff',
    },
  },
};
const genderButtonSx = {
  padding: '8px 12px',
  border: '1px solid #D1D5DB !important',
  textTransform: 'none',
  borderRadius: '12px !important',
  color: '#4B5563',
  fontWeight: 'normal',
  '&.Mui-selected': {
    color: '#0064C8',
    backgroundColor: '#F7FCFD',
    fontWeight: 500,
    borderColor: '#0064C8 !important',
    '&:hover': {
      backgroundColor: '#F7FCFD',
    },
  },
  '&:not(:last-of-type)': {
    marginRight: '8px',
  },
};
const studentsAccordionSx = {
  '&:before': {
    display: 'none',
  },
};
const studentsAccordionSummarySx = {
  padding: '12px 0',
  '&.Mui-focusVisible': {
    backgroundColor: 'transparent',
  },
  '& .MuiAccordionSummary-expandIconWrapper': {
    position: 'initial',
    padding: '4px',
    border: '1px solid #D1D5DB',
    borderRadius: '50%',
    backgroundColor: '#F9FAFB',
  },
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(90deg)',
    color: '#0064C8',
    border: '1px solid #53AEF9',
    backgroundColor: '#F7FCFD',
  },
  '& .MuiAccordionSummary-content': {
    margin: 0,
    flexDirection: 'column',
  },
  '&:focus-visible > .MuiAccordionSummary-expandIconWrapper': {
    boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.5)',
  },
};
const studentsAccordionDetailsSx = {
  padding: 0,
  cursor: 'default',
};
const checkboxSx = {
  padding: 0,
  marginRight: '8px',
  color: '#6B7280',
};
const deleteIconSx = {
  padding: 0,
  color: '#EF4444',
};
const unGroupIconSx = {
  padding: 0,
  marginRight: '16px',
  color: '#6B7280',
};
const levelAccordionSx = {
  padding: 0,
  border: 0,
};
const levelAccordionHeaderSx = {
  border: '1px solid #E5E7EB',
  borderRadius: '12px',
  minHeight: '58px',
};
const levelAccordionHeaderActiveSx = {
  borderColor: '#0064C8',
};
// const courseAccordionHeaderSx = {
//   paddingLeft: '16px',
//   border: '1px solid #E5E7EB',
//   borderRadius: '12px',
//   backgroundColor: '#F9FAFB',
//   minHeight: '58px',
// };
const accordionFooterSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'end',
  gap: '16px',
  padding: '16px',
  backgroundColor: 'white',
  borderRadius: '0 0 12px 12px',
  textAlign: 'right',
  cursor: 'default',
};
const deleteRowSx = {
  color: '#EB5757',
  backgroundColor: '#FEE2E2',
  padding: '8px 16px',
  borderRadius: '8px',
  cursor: 'default',
};
const levelSx = {
  position: 'relative',
  margin: '16px 0',
  paddingLeft: '44px',
};
const subdirectoryIconSx = {
  position: 'absolute',
  top: '19px',
  left: '12px',
  color: '#9CA3AF',
};
const courseSx = {
  position: 'relative',
  margin: '16px 0',
  paddingLeft: '44px',
};
const studentTableSx = {
  color: '#4B5563',
  border: '1px solid #D1D5DB',
  borderRadius: '8px',
};
const searchInputSx = {
  '& .MuiInputBase-root': {
    fontSize: 14,
    paddingRight: '8px',
  },
};
const footerButtonSx = {
  minWidth: 120,
};
const studentsCountSx = {
  marginLeft: '4px',
  padding: 0,
  color: '#0064C8',
  minWidth: 'auto',
  textDecoration: 'underline',
  lineHeight: 1.5,
};
const deleteButtonSx = {
  padding: '2px',
  color: 'inherit',
  backgroundColor: 'transparent',
  textTransform: 'uppercase !important',
  lineHeight: 1.5,
  '&:hover': {
    backgroundColor: 'transparent',
  },
  '& .MuiButton-startIcon': {
    marginRight: '4px',
  },
};
const programSelectBtnSx = {
  paddingRight: 0,
  textTransform: 'none !important',
  color: 'rgba(0, 0, 0, 0.87)',
  borderColor: 'rgba(0, 0, 0, 0.23)',
  '&:hover': {
    borderColor: 'rgba(0, 0, 0, 0.87)',
    backgroundColor: 'transparent',
  },
  '& .MuiSvgIcon-root': {
    color: 'rgba(0, 0, 0, 0.54)',
  },
};

const getSettingsCounts = (selectedData) => {
  const type = selectedData.get('type', '');
  let maleCount, femaleCount, noOfGroups, ungroupedCount, groupedCount;
  let noOfCourses = 0;
  switch (type) {
    case 'level': {
      maleCount = selectedData.getIn(['selectedLevel', 'levelMaleCount'], 0);
      femaleCount = selectedData.getIn(['selectedLevel', 'levelFemaleCount'], 0);
      noOfGroups = selectedData.getIn(['selectedLevel', 'levelNumberOfGroup'], 0);
      ungroupedCount = selectedData.getIn(['selectedLevel', 'levelUngroupedCount'], 0);
      groupedCount = selectedData.getIn(['selectedLevel', 'levelGroupedCount'], 0);
      noOfCourses = selectedData.getIn(['selectedLevel', 'noOfCourses'], 0);
      break;
    }
    case 'course': {
      maleCount = selectedData.getIn(['selectedCourse', 'courseMaleCount'], 0);
      femaleCount = selectedData.getIn(['selectedCourse', 'courseFemaleCount'], 0);
      noOfGroups = selectedData.getIn(['selectedCourse', 'courseNumberOfGroup'], 0);
      ungroupedCount = selectedData.getIn(['selectedCourse', 'courseUngroupedCount'], 0);
      groupedCount = selectedData.getIn(['selectedCourse', 'courseGroupedCount'], 0);
      break;
    }
    default: {
      maleCount = selectedData.get('yearMaleCount', 0);
      femaleCount = selectedData.get('yearFemaleCount', 0);
      noOfGroups = selectedData.get('yearNumberOfGroup', 0);
      ungroupedCount = selectedData.get('yearUngroupedCount', 0);
      groupedCount = selectedData.get('yearGroupedCount', 0);
      noOfCourses = selectedData.get('noOfCourses', 0);
    }
  }

  return {
    maleCount,
    femaleCount,
    noOfGroups,
    hasSettings: !!(maleCount || femaleCount || noOfGroups),
    ungroupedCount,
    groupedCount,
    noOfCourses,
  };
};

const checkIsImported = (selectedData) => {
  const type = selectedData.get('type', '');
  switch (type) {
    case 'level':
      return selectedData.getIn(['selectedLevel', 'isImportedLevel'], false);
    case 'course':
      return selectedData.getIn(['selectedCourse', 'isImportedCourse'], false);
    default:
      return selectedData.get('isImportedYear', false);
  }
};

const constructCacheKey = (data) => {
  const selectedType = data.get('type', '');
  const year = data.get('year', '');
  const level = data.getIn(['selectedLevel', 'level_no'], '');
  const courseId = data.getIn(['selectedCourse', '_course_id'], '');
  const cacheKey =
    selectedType === 'year'
      ? [year, 'settings']
      : selectedType === 'level'
      ? [year, 'levels', level]
      : [year, 'courses', `${level}+${courseId}`];
  return cacheKey;
};

export const getConstructedStudents = (data, settingId) => {
  return data.update('studentList', List(), (students) =>
    students.map((student) => {
      const isDisabled = student.get('studentGroupingSettingId') !== settingId;
      return student.set('isDisabled', isDisabled).update('groups', List(), (groups) =>
        groups.reduce((newObj, group) => {
          return newObj.set(group.get('deliverySymbol', ''), group.get('groupNo', ''));
        }, Map())
      );
    })
  );
};

export const getGenderWiseStudents = (studentData, genderType) => {
  if (genderType === 'both') return studentData;
  return studentData.update('studentList', List(), (students) =>
    students.filter((s) => s.get('gender', '').toLowerCase() === genderType)
  );
};

const checkCourseHasSettings = (level) => {
  if (!level.get('course', List()).size) return false;
  return level.get('course', List()).every((course) => course.get('courseNumberOfGroup', 0) > 0);
};

export const constructLevelData = (level, yearSettings) => {
  const yearNo = yearSettings.getIn(['settings', 'year'], '');
  const yearCourseIds = yearSettings.getIn(['settings', 'courseIds'], List());

  const levelNo = level.get('level_no', '');
  const isImportDisabled = level.get('isImportDisabled', false);
  const isSettingsDisabled = level.get('isSettingsDisabled', false);
  const levelCourseIds = yearSettings.getIn(['levels', levelNo, 'courseIds'], List());
  let hasLevelHierarchySettings = true;

  const courses = level.get('course', List()).map((course) => {
    const courseId = course.get('_course_id');
    const settingsFromYear = yearCourseIds.includes(courseId);
    const settingsFromLevel = levelCourseIds.includes(courseId);
    const hasHierarchySettings = settingsFromYear || settingsFromLevel;
    const settingsFrom = Map({
      ...(hasHierarchySettings && {
        type: settingsFromYear ? 'year' : 'level',
        year: yearNo,
        level: levelNo,
      }),
    });
    if (!hasHierarchySettings || settingsFromLevel) hasLevelHierarchySettings = false;

    return course
      .set('hasHierarchySettings', hasHierarchySettings)
      .set('settingsFrom', settingsFrom)
      .set('isImportDisabled', hasHierarchySettings)
      .set('isSettingsDisabled', hasHierarchySettings);
  });

  if (!courses.size) hasLevelHierarchySettings = false;

  const settingsFrom = Map({
    ...(hasLevelHierarchySettings && {
      type: 'year',
      year: yearNo,
    }),
  });

  return level
    .set('course', courses)
    .set('hasHierarchySettings', hasLevelHierarchySettings)
    .set('settingsFrom', settingsFrom)
    .set('isImportDisabled', isImportDisabled || hasLevelHierarchySettings)
    .set('isSettingsDisabled', isSettingsDisabled || hasLevelHierarchySettings);
};

const getSettingsFromInfo = (settingsFrom, programId) => {
  return settingsFrom.get('type') === 'year'
    ? `Settings added for ${settingsFrom.get('year', '').replace('year', 'Year ')}`
    : `Settings added for ${levelRename(settingsFrom.get('level', ''), programId)}`;
};

const checkHierarchySettings = (selectedData) => {
  const type = selectedData.get('type', '');
  switch (type) {
    case 'level':
      return {
        hasHierarchySettings: selectedData.getIn(['selectedLevel', 'hasHierarchySettings'], false),
        settingsFrom: selectedData.getIn(['selectedLevel', 'settingsFrom'], Map()),
        hasCourseSettings: selectedData.getIn(['selectedLevel', 'hasCourseSettings'], false),
      };
    default:
      return {
        hasHierarchySettings: selectedData.getIn(['selectedCourse', 'hasHierarchySettings'], false),
        settingsFrom: selectedData.getIn(['selectedCourse', 'settingsFrom'], Map()),
        hasLevelSettings: selectedData.getIn(['selectedCourse', 'hasLevelSettings'], false),
      };
  }
};

const getDisableInfo = (selectedData) => {
  const { noOfCourses } = getSettingsCounts(selectedData);
  const {
    hasHierarchySettings,
    settingsFrom,
    hasCourseSettings,
    hasLevelSettings,
  } = checkHierarchySettings(selectedData);
  const programId = selectedData.get('programId', '');
  const type = selectedData.get('type', '');

  return type !== 'course' && !noOfCourses
    ? 'No Courses'
    : hasHierarchySettings
    ? getSettingsFromInfo(settingsFrom, programId)
    : hasCourseSettings
    ? 'Settings added for courses'
    : hasLevelSettings
    ? 'Settings added for levels'
    : 'Settings added for level or courses';
};

export const getUserFullName = (user, nameKey = 'name') => {
  const firstName = user.getIn([nameKey, 'first'], '');
  const middleName = user.getIn([nameKey, 'middle'], '');
  const lastName = user.getIn([nameKey, 'last'], '');
  const fullName = [firstName, middleName, lastName].filter((item) => item).join(' ');
  return jsUcfirstAll(fullName);
};

export const useCallProgramYearLevel = ({ filters }) => {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const institutionCalendarId = activeInstitutionCalendar.get('_id', '');
  const programId = filters.get('programId');
  const term = filters.get('term');
  const params = { module: 'studentGroup', institutionCalendarId, programId, term };

  const fetchProgramYearLevel = () => dispatch(getProgramYearLevel(params));

  return [fetchProgramYearLevel];
};

export const useCallGroupSettings = ({ selectedData }) => {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const groupSettings = useSelector(selectGroupSettings);
  const cacheKey = constructCacheKey(selectedData);
  const institutionCalendarId = activeInstitutionCalendar.get('_id', '');
  const programId = selectedData.get('programId', '');
  const term = selectedData.get('term', '');
  const year = selectedData.get('year', '');
  const yearSettings = groupSettings.get(year, Map());
  const settingsCache = groupSettings.getIn(cacheKey, Map());

  const callGroupSettings = (callback = () => {}) => {
    const params = { institutionCalendarId, programId, term, year };
    dispatch(getGroupSettings({ params, callback }));
  };

  const callImportedStudents = ({ params, callback = () => {} }) => {
    const studentGroupSettingId = settingsCache.get('_id', '');
    const userSelectedType = selectedData.get('type', '');
    const level = selectedData.getIn(['selectedLevel', 'level_no']);
    const courseId = selectedData.getIn(['selectedCourse', '_course_id']);
    const payload = {
      institutionCalendarId,
      programId,
      term,
      studentGroupSettingId,
      userSelectedType,
      year,
      ...(level && { level }),
      ...(courseId && { courseIds: [courseId] }),
      ...params,
    };
    dispatch(getYearWiseStudents({ params: payload, callback }));
  };

  const clearSettings = () => {
    dispatch(setData(Map({ groupSettings: groupSettings.delete(year) })));
  };

  return {
    yearSettings,
    groupSettings: settingsCache,
    callGroupSettings,
    callImportedStudents,
    clearSettings,
  };
};

const useStudentsAccordionRef = () => {
  const studentsAccordionRef = useRef([]);

  const setGroupType = (groupType, index) => {
    studentsAccordionRef.current[index]?.setFilters((prev) => prev.set('group', groupType));
  };

  const getImportedStudents = (index) => studentsAccordionRef.current[index]?.getImportedStudents();

  return [studentsAccordionRef, setGroupType, getImportedStudents];
};

const ProgramSelect = ({ options, value, onChange }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [search, setSearch] = useState('');
  const [paperWidth, setPaperWidth] = useState(null);
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  useEffect(() => {
    if (!open) setSearch('');
    else setPaperWidth(anchorEl.offsetWidth);
  }, [open]);

  const filteredOptions = useMemo(() => {
    if (!search) return options;

    const searchValue = search.toLowerCase();
    return options.filter((option) => option.get('name', '').toLowerCase().includes(searchValue));
  }, [options, search]);

  const renderValue = useMemo(() => {
    if (!value) return '- Select -';

    const selectedOption = options.find((option) => option.get('value') === value);
    return getShortString(selectedOption?.get('name'), 35, false);
  }, [options, value]);

  const handleClick = (e) => setAnchorEl(e.currentTarget);

  const handleClose = () => setAnchorEl(null);

  const handleChange = (val) => {
    if (val !== value) onChange(val);
    handleClose();
  };

  return (
    <>
      <MButton variant="outlined" clicked={handleClick} sx={programSelectBtnSx} fullWidth>
        <div className="d-flex align-items-center justify-content-between w-100">
          <span className="f-16 fw-400">{renderValue}</span>
          {open ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
        </div>
      </MButton>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        slotProps={{ paper: { sx: { maxHeight: 350, width: paperWidth } } }}
      >
        <ListSubheader sx={{ padding: '16px 8px 0' }}>
          <SearchInput placeholder="Search program" value={search} changed={setSearch} />
        </ListSubheader>
        <MenuList sx={{ pt: 0 }}>
          {filteredOptions.map((option, index) => {
            const optionValue = option.get('value', '');
            return (
              <MenuItem
                key={index}
                tabIndex={0}
                className="py-2"
                onClick={() => handleChange(optionValue)}
                selected={optionValue === value}
              >
                <ListItemText primary={getShortString(option.get('name', ''), 35)} />
              </MenuItem>
            );
          })}
        </MenuList>
      </Popover>
    </>
  );
};
ProgramSelect.propTypes = {
  options: PropTypes.instanceOf(List),
  value: PropTypes.string,
  onChange: PropTypes.func,
};

const SelectWithSearch = ({ placeholder, options, value, onChange }) => {
  const selectedOption = options.find((option) => option.value === value) || null;

  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => option.name}
      value={selectedOption}
      onChange={(_, newValue) => onChange(newValue?.value || '')}
      renderInput={(params) => (
        <TextField {...params} variant="outlined" size="small" placeholder={placeholder} />
      )}
      isOptionEqualToValue={(option, value) => option.value === value?.value}
    />
  );
};
SelectWithSearch.propTypes = {
  placeholder: PropTypes.string,
  options: PropTypes.array,
  value: PropTypes.string,
  onChange: PropTypes.func,
};

const ProgramWiseList = ({ filters, programYearLevel }) => {
  // const [showGenderModal, setShowGenderModal] = useState(false);
  const [search, setSearch] = useState('');
  const [expanded, setExpanded] = useState(-1);
  const [studentsAccordionRef, setGroupType, getImportedStudents] = useStudentsAccordionRef();

  const yearList = useMemo(() => {
    return programYearLevel.map((item) => {
      let yearCourses = 0;
      let isYearSettingDisabled = true;
      let hasLevelSettings = true;

      const levels = item.get('level', List()).map((lvl) => {
        const noOfCourses = lvl.get('course', List()).size;
        const hasCourseSettings = checkCourseHasSettings(lvl);
        yearCourses += noOfCourses;
        if (!lvl.get('levelNumberOfGroup')) {
          hasLevelSettings = false;
          if (!hasCourseSettings) isYearSettingDisabled = false;
        }

        return lvl
          .set('noOfCourses', noOfCourses)
          .set('hasCourseSettings', hasCourseSettings)
          .set('isImportDisabled', !noOfCourses || hasCourseSettings)
          .set('isSettingsDisabled', !noOfCourses || hasCourseSettings);
      });

      return item
        .set('level', levels)
        .set('noOfCourses', yearCourses)
        .set('hasLevelSettings', hasLevelSettings)
        .set('isImportDisabled', !yearCourses || isYearSettingDisabled)
        .set('isSettingsDisabled', !yearCourses || isYearSettingDisabled);
    });
  }, [programYearLevel]);

  const handleAccordion = (index) => {
    setExpanded(index === expanded ? -1 : index);
  };

  const handleClickInfo = (index) => (groupType) => {
    setExpanded(index);
    setGroupType(groupType, index);
  };

  return (
    <div>
      <div className="row justify-content-between align-items-center my-3">
        <div className="col-sm-6 col-md-8">
          <p className="bold text-uppercase f-15">
            List of year in selected program ({formatToTwoDigitString(programYearLevel.size)})
          </p>
        </div>
        {false && (
          <div className="col-sm-6 col-md-4">
            <div className="d-flex align-items-center my-2">
              <div className="flex-grow-1">
                <MaterialInput
                  elementType="materialInput"
                  type="text"
                  variant="outlined"
                  size="small"
                  placeholder="Search Student Name/ID"
                  inputAdornment={<SearchIcon fontSize="small" color="action" />}
                  value={search}
                  changed={(e) => setSearch(e.target.value)}
                />
              </div>
              {/* <MButton
              className="mb-1 ml-2"
              startIcon={<SettingsIcon />}
              clicked={() => setShowGenderModal(true)}
            >
              Gender Segregation
            </MButton> */}
              <div className="d-flex align-items-center">
                <div className="d-flex">
                  <Divider orientation="vertical" className="ml-3" flexItem />
                  <MButton variant="text" sx={buttonSx} clicked={() => {}} disabled>
                    Export All
                  </MButton>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div>
        {yearList.map((year, index) => {
          const selectedData = filters.merge(year).set('type', 'year');
          return (
            <MuiAccordion
              key={index}
              expanded={expanded === index}
              handleChange={() => handleAccordion(index)}
              summary={
                <div className="pr-3">
                  <AccordionHeader
                    title={year.get('year', '').replace('year', 'Year ')}
                    noOfCourses={year.get('noOfCourses', 0)}
                    selectedData={selectedData}
                    handleClickInfo={handleClickInfo(index)}
                    refreshStudents={() => getImportedStudents(index)}
                    isImportDisabled={year.get('isImportDisabled', false)}
                    isSettingsDisabled={year.get('isSettingsDisabled', false)}
                    openAccordion={() => setExpanded(index)}
                  />

                  {year.get('isImportedYear', false) && (
                    <div className="pl-3 bg-white" onClick={(e) => e.stopPropagation()}>
                      <StudentsAccordion
                        ref={(el) => (studentsAccordionRef.current[index] = el)}
                        selectedData={selectedData}
                        isExpanded={expanded === index}
                      />
                    </div>
                  )}
                </div>
              }
              details={
                <div className="pr-3">
                  <LevelAccordion year={selectedData} isExpanded={expanded === index} />
                </div>
              }
            />
          );
        })}
      </div>

      {/* {showGenderModal && (
        <Suspense fallback="">
          <GenderSegregationModal
            open
            handleClose={() => setShowGenderModal(false)}
            handleSave={() => {}}
          />
        </Suspense>
      )} */}
    </div>
  );
};
ProgramWiseList.propTypes = {
  filters: PropTypes.instanceOf(Map),
  programYearLevel: PropTypes.instanceOf(List),
};

const MuiAccordion = ({ expanded, handleChange, summary, details, sx = {} }) => {
  return (
    <Accordion
      expanded={expanded}
      onChange={handleChange}
      elevation={0}
      sx={{ ...accordionSx, ...sx }}
      disableGutters
    >
      <AccordionSummary
        expandIcon={<ArrowForwardIosSharpIcon sx={iconSx} />}
        sx={accordionSummarySx}
      >
        {summary}
      </AccordionSummary>
      <AccordionDetails sx={accordionDetailsSx}>{details}</AccordionDetails>
    </Accordion>
  );
};
MuiAccordion.propTypes = {
  expanded: PropTypes.bool,
  handleChange: PropTypes.func,
  summary: PropTypes.node,
  details: PropTypes.node,
  sx: PropTypes.object,
};

const AccordionHeader = ({
  title,
  noOfCourses,
  selectedData,
  handleClickInfo,
  refreshStudents,
  isImportDisabled,
  isSettingsDisabled,
  openAccordion,
  sx = {},
}) => {
  const dispatch = useDispatch();
  const [fetchProgramYearLevel] = useCallProgramYearLevel({ filters: selectedData });
  const { callGroupSettings } = useCallGroupSettings({ selectedData });
  const isImported = checkIsImported(selectedData);
  const { ungroupedCount, groupedCount } = getSettingsCounts(selectedData);

  const handleSaveSettings = (isImport = false) => (requestBody, closeModal) => {
    const groupSettingId = requestBody.get('groupSettingId');
    const settingFunc = groupSettingId ? editGroupSettings : addGroupSettings;
    dispatch(
      settingFunc({
        requestBody,
        callback: () => {
          fetchProgramYearLevel();
          callGroupSettings();
          if (isImport) refreshStudents();
          else openAccordion();
          closeModal();
        },
      })
    );
  };

  return (
    <Box sx={{ ...accordionFirstRowSx, ...sx }}>
      <div className="f-16 bold">{title}</div>
      <div className="d-flex align-items-center" onClick={(e) => e.stopPropagation()}>
        {(isImported || ungroupedCount > 0) && (
          <GroupInfo
            groupedCount={groupedCount}
            ungroupedCount={ungroupedCount}
            handleClick={handleClickInfo}
          />
        )}
        {selectedData.get('type') !== 'course' && (
          <>
            <p className="f-14 mx-3">
              {noOfCourses
                ? `${noOfCourses} ${noOfCourses === 1 ? 'Course' : 'Courses'}`
                : 'No Courses'}
            </p>
            <Divider orientation="vertical" flexItem />
          </>
        )}
        <ImportStudents
          selectedData={selectedData}
          handleSave={handleSaveSettings(true)}
          isDisabled={isImportDisabled}
          refreshStudents={refreshStudents}
        />
        <Divider orientation="vertical" flexItem />
        <DeliveryGroups
          selectedData={selectedData}
          handleSave={handleSaveSettings()}
          isDisabled={isSettingsDisabled}
        />
      </div>
    </Box>
  );
};
AccordionHeader.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  noOfCourses: PropTypes.number,
  selectedData: PropTypes.instanceOf(Map),
  handleClickInfo: PropTypes.func,
  refreshStudents: PropTypes.func,
  isImportDisabled: PropTypes.bool,
  isSettingsDisabled: PropTypes.bool,
  openAccordion: PropTypes.func,
  sx: PropTypes.object,
};

const LevelAccordion = ({ year, isExpanded }) => {
  const [expanded, setExpanded] = useState(-1);
  const [studentsAccordionRef, setGroupType, getImportedStudents] = useStudentsAccordionRef();
  const { yearSettings, callGroupSettings } = useCallGroupSettings({ selectedData: year });
  const programId = year.get('programId', '');

  // Reset expanded state when year accordion closes
  useEffect(() => {
    if (!isExpanded) setExpanded(-1);
    else if (yearSettings.isEmpty()) callGroupSettings();
  }, [isExpanded]);

  const levelList = useMemo(() => {
    return year.get('level', List()).map((level) => constructLevelData(level, yearSettings));
  }, [year, yearSettings]);

  const handleAccordion = (index) => {
    const newExpanded = index === expanded ? -1 : index;
    setExpanded(newExpanded);
  };

  const handleClickInfo = (index) => (groupType) => {
    setExpanded(index);
    setGroupType(groupType, index);
  };

  return (
    <>
      {levelList.map((level, index) => {
        const selectedData = year.set('selectedLevel', level).set('type', 'level');
        return (
          <Box key={index} sx={levelSx}>
            <SubdirectoryArrowRightIcon fontSize="small" sx={subdirectoryIconSx} />
            <MuiAccordion
              expanded={expanded === index}
              handleChange={() => handleAccordion(index)}
              sx={levelAccordionSx}
              summary={
                <>
                  <AccordionHeader
                    title={levelRename(level.get('level_no', ''), programId)}
                    noOfCourses={level.get('noOfCourses', 0)}
                    selectedData={selectedData}
                    handleClickInfo={handleClickInfo(index)}
                    refreshStudents={() => getImportedStudents(index)}
                    isImportDisabled={level.get('isImportDisabled', false)}
                    isSettingsDisabled={level.get('isSettingsDisabled', false)}
                    openAccordion={() => setExpanded(index)}
                    sx={{
                      ...levelAccordionHeaderSx,
                      ...(expanded === index && levelAccordionHeaderActiveSx),
                    }}
                  />

                  {level.get('isImportedLevel', false) && (
                    <div onClick={(e) => e.stopPropagation()}>
                      <StudentsAccordion
                        ref={(el) => (studentsAccordionRef.current[index] = el)}
                        selectedData={selectedData}
                        isExpanded={expanded === index}
                      />
                    </div>
                  )}
                </>
              }
              details={<CourseAccordion level={selectedData} isExpanded={expanded === index} />}
            />
          </Box>
        );
      })}
      <Footer selectedData={year} backgroundColor="#F7FCFD" />
    </>
  );
};
LevelAccordion.propTypes = {
  year: PropTypes.instanceOf(Map),
  isExpanded: PropTypes.bool,
};

const CourseAccordion = ({ level, isExpanded }) => {
  const [expanded, setExpanded] = useState(-1);
  const [studentsAccordionRef, setGroupType, getImportedStudents] = useStudentsAccordionRef();

  // Reset expanded state when level accordion closes
  useEffect(() => {
    if (!isExpanded) setExpanded(-1);
  }, [isExpanded]);

  const handleAccordion = (index) => {
    const newExpanded = index === expanded ? -1 : index;
    setExpanded(newExpanded);
  };

  const handleClickInfo = (index) => (groupType) => {
    setExpanded(index);
    setGroupType(groupType, index);
  };

  return (
    <>
      {level.getIn(['selectedLevel', 'course'], List()).map((course, index) => {
        const selectedData = level.set('selectedCourse', course).set('type', 'course');
        return (
          <Box key={index} sx={courseSx}>
            <SubdirectoryArrowRightIcon fontSize="small" sx={subdirectoryIconSx} />
            <MuiAccordion
              expanded={expanded === index}
              handleChange={() => handleAccordion(index)}
              sx={levelAccordionSx}
              summary={
                <>
                  <AccordionHeader
                    title={getShortString(
                      `${course.get('courses_number', '')} - ${course.get(
                        'courses_name',
                        ''
                      )}${getVersionName(course)}`,
                      40
                    )}
                    noOfCourses={course.get('noOfCourses', 0)}
                    selectedData={selectedData}
                    handleClickInfo={handleClickInfo(index)}
                    refreshStudents={() => getImportedStudents(index)}
                    isImportDisabled={course.get('isImportDisabled', false)}
                    isSettingsDisabled={course.get('isSettingsDisabled', false)}
                    openAccordion={() => setExpanded(index)}
                    sx={{
                      ...levelAccordionHeaderSx,
                      ...(expanded === index && levelAccordionHeaderActiveSx),
                    }}
                  />

                  {course.get('isImportedCourse', false) && (
                    <div onClick={(e) => e.stopPropagation()}>
                      <StudentsAccordion
                        ref={(el) => (studentsAccordionRef.current[index] = el)}
                        selectedData={selectedData}
                        isExpanded={expanded === index}
                      />
                    </div>
                  )}

                  {expanded === index && <Footer selectedData={selectedData} />}
                </>
              }
            />
          </Box>
        );
      })}
      <Footer selectedData={level} />
    </>
  );
};
CourseAccordion.propTypes = {
  level: PropTypes.instanceOf(Map),
  isExpanded: PropTypes.bool,
};

const DeliveryGroups = ({ selectedData, handleSave, isDisabled }) => {
  const [showDeliveryGroups, setShowDeliveryGroups] = useState(false);
  const { noOfGroups, hasSettings } = getSettingsCounts(selectedData);

  const getTooltipMessage = () => {
    return isDisabled ? getDisableInfo(selectedData) : 'Set student groups for each delivery type';
  };

  return (
    <>
      {noOfGroups ? (
        <Box sx={deliveryGroupsSx}>
          <CheckCircleIcon sx={circleIconSx} /> {formatToTwoDigitString(noOfGroups)}{' '}
          {noOfGroups === 1 ? 'group' : 'groups'}
          <IconButton sx={{ p: 0, ml: '4px' }} onClick={() => setShowDeliveryGroups(true)}>
            <EditIcon fontSize="small" sx={primaryColorSx} />
          </IconButton>
        </Box>
      ) : (
        <Tooltip title={getTooltipMessage()} placement="top" arrow>
          <div>
            <MButton
              variant="text"
              sx={buttonSx}
              startIcon={isDisabled ? <EditOffIcon /> : <AddIcon />}
              clicked={() => setShowDeliveryGroups(true)}
              disabled={isDisabled}
            >
              Delivery Groups
            </MButton>
          </div>
        </Tooltip>
      )}

      {showDeliveryGroups && (
        <Suspense fallback="">
          <DeliveryGroupsModal
            open
            data={selectedData}
            handleClose={() => setShowDeliveryGroups(false)}
            handleSave={handleSave}
            hasSettings={hasSettings}
          />
        </Suspense>
      )}
    </>
  );
};
DeliveryGroups.propTypes = {
  selectedData: PropTypes.instanceOf(Map),
  handleSave: PropTypes.func,
  isDisabled: PropTypes.bool,
};

const ImportStudents = ({ selectedData, handleSave, isDisabled, refreshStudents }) => {
  const dispatch = useDispatch();
  const [showImport, setShowImport] = useState(false);
  const [showImportedStudents, setShowImportedStudents] = useState('');
  const { maleCount, femaleCount, hasSettings } = getSettingsCounts(selectedData);

  const getTooltipMessage = () => {
    return isDisabled ? getDisableInfo(selectedData) : 'Import Students';
  };

  const handleClickCount = (gender) => {
    const count = gender === 'male' ? maleCount : femaleCount;
    if (count) return setShowImportedStudents(gender);

    dispatch(resetMessage(`No ${gender} students imported yet`));
  };

  return (
    <>
      {maleCount || femaleCount ? (
        <Box
          width={200}
          className="d-flex align-items-center justify-content-center f-14 text-gray"
        >
          <span>Male: </span>
          <MButton variant="text" sx={studentsCountSx} onClick={() => handleClickCount('male')}>
            {maleCount}
          </MButton>
          <span className="mx-1">•</span>
          <span>Female: </span>
          <MButton variant="text" sx={studentsCountSx} onClick={() => handleClickCount('female')}>
            {femaleCount}
          </MButton>
          <IconButton sx={{ p: 0, ml: '8px' }} onClick={() => setShowImport(true)}>
            <FileDownloadOutlinedIcon fontSize="small" sx={primaryColorSx} />
          </IconButton>
        </Box>
      ) : (
        <Box width={200} className="text-center">
          <Tooltip title={getTooltipMessage()} placement="top" arrow>
            <span>
              <MButton
                variant="text"
                sx={buttonSx}
                startIcon={
                  isDisabled ? <FileDownloadOffOutlinedIcon /> : <FileDownloadOutlinedIcon />
                }
                clicked={() => setShowImport(true)}
                disabled={isDisabled}
              >
                Import
              </MButton>
            </span>
          </Tooltip>
        </Box>
      )}

      {showImport && (
        <Suspense fallback="">
          <ImportStudentsModal
            open
            data={selectedData}
            handleClose={() => setShowImport(false)}
            handleSave={handleSave}
            hasSettings={hasSettings}
          />
        </Suspense>
      )}

      {showImportedStudents && (
        <Suspense fallback="">
          <ViewImportedStudents
            open
            genderType={showImportedStudents}
            data={selectedData}
            handleClose={() => setShowImportedStudents('')}
            refreshStudents={refreshStudents}
          />
        </Suspense>
      )}
    </>
  );
};
ImportStudents.propTypes = {
  selectedData: PropTypes.instanceOf(Map),
  handleSave: PropTypes.func,
  isDisabled: PropTypes.bool,
  refreshStudents: PropTypes.func,
};

const GroupInfo = ({ groupedCount, ungroupedCount, handleClick }) => {
  return (
    <>
      {!ungroupedCount ? (
        <Tooltip
          title=""
          // title={`${groupedCount} students grouped!`}
          placement="top"
          arrow
        >
          <Box
            sx={{ ...groupInfoSx, ...successInfoSx }}
            // onClick={() => handleClick(GROUPED)}
          >
            <DoneIcon sx={circleIconSx} />
            All grouped
          </Box>
        </Tooltip>
      ) : (
        <Tooltip title={`${ungroupedCount} students not yet grouped`} placement="top" arrow>
          <Box
            sx={{ ...groupInfoSx, ...errorInfoSx }}
            // onClick={() => handleClick(UNGROUPED)}
          >
            <ErrorRoundedIcon sx={circleIconSx} />
            {ungroupedCount} Ungrouped
          </Box>
        </Tooltip>
      )}
      <Divider orientation="vertical" flexItem />
    </>
  );
};
GroupInfo.propTypes = {
  groupedCount: PropTypes.number,
  ungroupedCount: PropTypes.number,
  handleClick: PropTypes.func,
};

const StudentsAccordion = forwardRef(({ selectedData, isExpanded }, ref) => {
  const dispatch = useDispatch();
  const { groupSettings, callGroupSettings, callImportedStudents } = useCallGroupSettings({
    selectedData,
  });
  const [fetchProgramYearLevel] = useCallProgramYearLevel({ filters: selectedData });
  const [expanded, setExpanded] = useState(true);
  const [filters, setFilters] = useState(Map());
  const [students, setStudents] = useState(Map());
  const [limit, setLimit] = useState(10);
  const [pageNo, setPageNo] = useState(1);
  const [showImportedInfo, setShowImportedInfo] = useState(false);
  const [search, setSearch] = useState(Map());
  const [showGroupingModal, setShowGroupingModal] = useState(Map());
  const [showAddStudent, setShowAddStudent] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(Map());
  const [showMoveModal, setShowMoveModal] = useState(Map());
  const [sortBy, setSortBy] = useState(Map());
  const { noOfGroups } = getSettingsCounts(selectedData);
  const isGrouped = filters.get('group') === GROUPED;
  const genderType = filters.get('gender', '');
  const ungroupedCount = students.get('unGroupedCount', 0);
  const groupedCount = students.get('groupedCount', 0);
  const maleCount = students.get('maleCount', 0);
  const femaleCount = students.get('femaleCount', 0);
  const mixedCount = students.get('mixedCount', 0);
  const genderMerge = isGenderMerge();
  const groupSettingId = groupSettings.get('_id', '');

  const resetFilters = () => {
    setFilters(Map({ group: UNGROUPED, gender: genderMerge ? 'both' : 'male', isChanged: true }));
  };

  useEffect(() => {
    resetFilters();
  }, [genderMerge]);

  useEffect(() => {
    if (!isExpanded) {
      setExpanded(true);
      resetFilters();
      setLimit(10);
      setShowImportedInfo(false);
      setSortBy(Map());
      return;
    }

    // if (groupSettings.isEmpty()) callGroupSettings();
  }, [isExpanded]);

  const getImportedStudents = (params = {}) => {
    if (!groupSettingId) return;

    const groupType = filters.get('group', '');
    const data = {
      groupType,
      ...(genderType !== 'both' && { genderType }),
      ...params,
    };
    const validParams = Object.fromEntries(Object.entries(data).filter(([_, v]) => v !== ''));
    setSearch(Map());
    setPageNo(1);
    setStudents(Map());
    callImportedStudents({
      params: validParams,
      callback: (res) => {
        const genderWiseStudents = getGenderWiseStudents(res, genderType);
        const constructedData = getConstructedStudents(genderWiseStudents, groupSettingId);
        setStudents(constructedData);
      },
    });
  };

  useEffect(() => {
    if (!filters.get('isChanged', false) || !isExpanded) return;
    getImportedStudents();
  }, [groupSettingId, filters, isExpanded]);

  // useEffect(() => {
  //   if (!search.get('isEdited', false)) return;

  //   const searchKey = search.get('value', '');
  //   const timeout = setTimeout(() => {
  //     setPageNo(1);
  //     getImportedStudents({ pageNo: 1, searchKey });
  //   }, 1000);

  //   return () => clearTimeout(timeout);
  // }, [search]);

  const filteredStudents = useMemo(() => {
    const studentList = students.get('studentList', List());
    const searchKey = search.get('value', '').toLowerCase();
    if (!searchKey) return studentList;

    return studentList.filter((s) => {
      const academicId = s.get('academicId', '').toLowerCase();
      const name = getUserFullName(s).toLowerCase();
      return academicId.includes(searchKey) || name.includes(searchKey);
    });
  }, [students, search]);

  const sortedStudents = useMemo(() => {
    return filteredStudents.sort((a, b) => {
      const sortByColumn = sortBy.get('column');
      if (sortByColumn) {
        if (sortByColumn === 'academicId') {
          const aValue = a.get(sortByColumn);
          const bValue = b.get(sortByColumn);
          if (sortBy.get('asc')) return aValue > bValue ? 1 : -1;
          else return bValue > aValue ? 1 : -1;
        }
      }

      // Sort checked first
      const checkedDiff = b.get('checked', false) - a.get('checked', false);
      if (checkedDiff !== 0) return checkedDiff;

      // Sort enabled first
      return a.get('isDisabled', false) - b.get('isDisabled', false);
    });
  }, [filteredStudents, sortBy]);

  const deliveryGroups = useMemo(() => {
    const deliveryGroups = groupSettings.get('deliveryGroups', List());
    if (!deliveryGroups.size) return Map();

    return deliveryGroups.reduce((acc, group) => {
      const gender = group.get('gender', '');
      const selectedTypes = group
        .get('deliveryTypes', List())
        .flatMap((type) => type.get('selectedType', List()));

      return acc.update(gender, List(), (prev) => prev.concat(selectedTypes));
    }, Map());
  }, [groupSettings]);

  const selectedCount = useMemo(() => {
    const studentList = students.get('studentList', List());
    return studentList.filter((student) => student.get('checked')).size;
  }, [students]);

  const handleAccordion = () => {
    setExpanded(!expanded);
  };

  const handleChange = (key) => (e) => {
    const value = e.target.value;
    setFilters((prev) => prev.set(key, value));
    if (!expanded) setExpanded(true);
  };

  const handleSearch = (value) => {
    setSearch(Map({ isEdited: true, value }));
    setPageNo(1);
  };

  const handlePageChange = (_, pageNo) => {
    setPageNo(pageNo);
  };

  const handleLimitChange = (e) => {
    const limit = parseInt(e.target.value, 10);
    setLimit(limit);
    setPageNo(1);
  };

  const handleCheckboxChange = (id) => (e) => {
    const isChecked = e.target.checked;
    const studentIds = id === 'all' ? sortedStudents.map((s) => s.get('userId', '')) : List([id]);

    setStudents((prev) =>
      prev.update('studentList', List(), (studentList) =>
        studentList.map((student) => {
          const userId = student.get('userId');
          const isDisabled = student.get('isDisabled');
          if (isDisabled || !studentIds.includes(userId)) return student;

          return student.set('checked', isChecked);
        })
      )
    );
  };

  const handleClickGroup = () => {
    if (!selectedCount) return dispatch(resetMessage('Select some students to group!'));
    setShowGroupingModal(Map({ show: true, groupingData: getGroupingData() }));
  };

  const handleClickMove = (isMultiple) => (student) => {
    if (isMultiple && !selectedCount)
      return dispatch(resetMessage('Select some students to move!'));

    const groupingData = isMultiple
      ? getGroupingData()
      : { genderType, selectedStudents: List([student]), isAllStudent: false };
    setShowMoveModal(Map({ show: true, groupingData }));
  };

  const getGroupingData = () => {
    const studentList = students.get('studentList', List());
    const selectedStudents = studentList.filter((s) => s.get('checked'));
    const isAllStudent = selectedStudents.size === studentList.size;
    return { genderType, selectedStudents, isAllStudent };
  };

  const handleClickDelete = (isMultiple) => (student) => {
    const selectedStudents = isMultiple
      ? students.get('studentList', List()).filter((s) => s.get('checked'))
      : List([student]);
    const isAnyGrouped = selectedStudents.some((s) => s.get('groups', Map()).some((item) => item));
    const studentIds = selectedStudents.map((s) => s.get('userId'));
    setShowDeleteModal(Map({ show: true, studentIds, isAnyGrouped }));
  };

  const handleConfirmDelete = (data) => {
    const studentIds = showDeleteModal.get('studentIds', List());
    const requestBody = { groupSettingId, studentIds, ...data };
    dispatch(
      deleteStudents({
        requestBody,
        callback: () => {
          fetchProgramYearLevel();
          callGroupSettings();
          getImportedStudents();
          setShowDeleteModal(Map());
        },
      })
    );
  };

  const handleSort = (column) => () => {
    setSortBy((prev) => prev.set('column', column).set('asc', !prev.get('asc')));
    setPageNo(1);
  };

  useImperativeHandle(ref, () => ({ setFilters, getImportedStudents }));

  if (!isExpanded) return null;

  return (
    <>
      {selectedCount > 0 && (
        <div className="bg-white pt-2">
          <DeleteRow count={selectedCount} handleDelete={handleClickDelete(true)} />
        </div>
      )}
      <Accordion
        expanded={expanded}
        onChange={handleAccordion}
        elevation={0}
        sx={studentsAccordionSx}
        disableGutters
        onClick={(e) => e.stopPropagation()}
      >
        <AccordionSummary
          expandIcon={<ArrowForwardIosSharpIcon sx={iconSx} />}
          sx={studentsAccordionSummarySx}
        >
          <div className="d-flex align-items-center justify-content-between">
            <Box display="flex" gap={2} onClick={(e) => e.stopPropagation()}>
              <ToggleButtonGroup
                value={expanded ? filters.get('group', '') : ''}
                exclusive
                onChange={handleChange('group')}
                aria-label="grouping"
                sx={buttonGroupSx}
              >
                <ToggleButton value={UNGROUPED} sx={groupButtonSx}>
                  Ungrouped ({ungroupedCount})
                </ToggleButton>
                <ToggleButton value={GROUPED} sx={groupButtonSx}>
                  Grouped ({groupedCount})
                </ToggleButton>
              </ToggleButtonGroup>

              <ToggleButtonGroup
                value={expanded ? filters.get('gender', '') : ''}
                exclusive
                onChange={handleChange('gender')}
                aria-label="gender"
              >
                {genderMerge ? (
                  <ToggleButton value="both" sx={genderButtonSx}>
                    Mixed ({mixedCount})
                  </ToggleButton>
                ) : (
                  [
                    <ToggleButton key="male" value="male" sx={genderButtonSx}>
                      Male ({maleCount})
                    </ToggleButton>,
                    <ToggleButton key="female" value="female" sx={genderButtonSx}>
                      Female ({femaleCount})
                    </ToggleButton>,
                  ]
                )}
              </ToggleButtonGroup>
            </Box>

            {expanded && (
              <Box
                gap={0.75}
                className="d-flex align-items-center ml-auto mr-1"
                onClick={(e) => e.stopPropagation()}
              >
                <MButton
                  variant="text"
                  sx={buttonSx}
                  startIcon={<AddIcon />}
                  clicked={() => setShowAddStudent(true)}
                  disabled={!groupSettingId}
                >
                  Add student
                </MButton>
                <Box sx={{ paddingTop: '5px' }}>
                  <SearchInput
                    placeholder="Search student"
                    value={search.get('value', '')}
                    changed={handleSearch}
                    sx={searchInputSx}
                  />
                </Box>
                {isGrouped ? (
                  <MButton startIcon={<DriveFileMoveRoundedIcon />} clicked={handleClickMove(true)}>
                    Move To
                  </MButton>
                ) : (
                  <MButton
                    startIcon={<DriveFileMoveRoundedIcon />}
                    clicked={handleClickGroup}
                    disabled={!noOfGroups}
                  >
                    {selectedCount > 0 && <span className="mr-1">({selectedCount})</span>}Group To
                  </MButton>
                )}
                <MoreIcon
                  showImportedInfo={showImportedInfo}
                  setShowImportedInfo={setShowImportedInfo}
                />
              </Box>
            )}
            <p className="f-14 mr-2">{expanded ? 'Hide' : 'View students'}</p>
          </div>
        </AccordionSummary>
        <AccordionDetails sx={studentsAccordionDetailsSx}>
          <StudentsTable
            deliveryGroups={deliveryGroups.get(genderType, List())}
            students={sortedStudents}
            showImportedInfo={showImportedInfo}
            pagination={{ pageNo, limit, handlePageChange, handleLimitChange }}
            isGrouped={isGrouped}
            isAllGrouped={false}
            handleCheckboxChange={handleCheckboxChange}
            handleClickDelete={handleClickDelete(false)}
            handleClickMove={handleClickMove(false)}
            handleSort={handleSort}
          />
        </AccordionDetails>
      </Accordion>

      {showGroupingModal.get('show') && (
        <Suspense fallback="">
          <GroupingModal
            open
            data={selectedData}
            groupingData={showGroupingModal.get('groupingData', {})}
            handleClose={() => setShowGroupingModal(Map())}
            refreshStudents={getImportedStudents}
          />
        </Suspense>
      )}

      {showAddStudent && (
        <Suspense fallback="">
          <AddStudentModal
            open
            data={selectedData}
            handleClose={() => setShowAddStudent(false)}
            refreshStudents={getImportedStudents}
          />
        </Suspense>
      )}

      {showDeleteModal.get('show') && (
        <Suspense fallback="">
          <DeleteConfirmModal
            open
            data={selectedData}
            isGrouped={showDeleteModal.get('isAnyGrouped', false)}
            studentIds={showDeleteModal.get('studentIds', List())}
            handleClose={() => setShowDeleteModal(Map())}
            handleDelete={handleConfirmDelete}
          />
        </Suspense>
      )}

      {showMoveModal.get('show') && (
        <Suspense fallback="">
          <MoveGroupedModal
            open
            data={selectedData}
            groupingData={showMoveModal.get('groupingData', {})}
            handleClose={() => setShowMoveModal(Map())}
            refreshStudents={getImportedStudents}
          />
        </Suspense>
      )}
    </>
  );
});
StudentsAccordion.propTypes = {
  selectedData: PropTypes.instanceOf(Map),
  isExpanded: PropTypes.bool,
};

const MoreIcon = ({ showImportedInfo, setShowImportedInfo }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const handleClick = (e) => setAnchorEl(e.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const handleMenuClick = (menu) => {
    if (menu === 'importedInfo') setShowImportedInfo(!showImportedInfo);
    handleClose();
  };

  return (
    <>
      <IconButton
        aria-label="more"
        aria-controls="long-menu"
        aria-haspopup="true"
        onClick={handleClick}
        size="small"
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{ style: { width: '19ch' } }}
      >
        {/* <MenuItem onClick={() => {}}>Import</MenuItem> */}
        {/* <MenuItem onClick={() => {}}>Export</MenuItem> */}
        <MenuItem onClick={() => handleMenuClick('importedInfo')}>
          {showImportedInfo ? 'Hide Imported By' : 'Show Imported By'}
        </MenuItem>
      </Menu>
    </>
  );
};
MoreIcon.propTypes = {
  showImportedInfo: PropTypes.bool,
  setShowImportedInfo: PropTypes.func,
};

const StudentsTable = ({
  deliveryGroups,
  students,
  showImportedInfo,
  pagination,
  isGrouped,
  isAllGrouped,
  handleCheckboxChange,
  handleClickDelete,
  handleClickMove,
  handleSort,
}) => {
  const { pageNo, limit, handlePageChange, handleLimitChange } = pagination;
  const totalStudents = students.size;
  const startIndex = (pageNo - 1) * limit;
  const paginatedStudents = students.slice(startIndex, startIndex + limit);

  const { isAllChecked, isIndeterminate } = useMemo(() => {
    const selectedCount = students.filter((student) => student.get('checked')).size;
    const isAllChecked = selectedCount === students.size;
    return {
      isAllChecked,
      isIndeterminate: selectedCount > 0 && !isAllChecked,
    };
  }, [students]);

  const colSpanCount = useMemo(() => {
    const noOfColumns = 4 + deliveryGroups.size;
    return showImportedInfo ? noOfColumns + 2 : noOfColumns;
  }, [deliveryGroups, showImportedInfo]);

  return (
    <Box sx={studentTableSx}>
      <div className="table-responsive">
        <table className="table student-grouping-table">
          <thead>
            <tr>
              <th>
                <div className="d-flex align-items-center">
                  {paginatedStudents.size > 0 && (
                    <Checkbox
                      sx={checkboxSx}
                      checked={isAllChecked}
                      indeterminate={isIndeterminate}
                      onChange={handleCheckboxChange('all')}
                    />
                  )}
                  Academic ID{' '}
                  <IconButton sx={{ p: 0, ml: '8px' }} onClick={handleSort('academicId')}>
                    <FilterListIcon sx={primaryColorSx} />
                  </IconButton>
                </div>
              </th>
              <th>Student Name</th>
              <th>CGPA</th>
              {deliveryGroups.map((group, index) => (
                <th key={index}>{group.get('deliverySymbol', '')}</th>
              ))}
              {showImportedInfo && (
                <>
                  <th>
                    <div className="d-flex align-items-center">
                      Imported By
                      {/* <IconButton sx={{ p: 0, ml: '8px' }}>
                        <FilterListIcon sx={primaryColorSx} />
                      </IconButton> */}
                    </div>
                  </th>
                  <th>
                    <div className="d-flex align-items-center">
                      Imported at
                      {/* <IconButton sx={{ p: 0, ml: '8px' }}>
                        <FilterListIcon sx={primaryColorSx} />
                      </IconButton> */}
                    </div>
                  </th>
                </>
              )}
              <th className="text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedStudents.size ? (
              paginatedStudents.map((student, index) => (
                <tr key={index} {...(student.get('checked') && { className: 'selected' })}>
                  <td>
                    <div className="d-flex align-items-center">
                      <Checkbox
                        sx={checkboxSx}
                        checked={student.get('checked', false)}
                        onChange={handleCheckboxChange(student.get('userId'))}
                        disabled={student.get('isDisabled', false)}
                      />
                      {student.get('academicId', '')}
                    </div>
                  </td>
                  <td>{getUserFullName(student)}</td>
                  <td>{student.get('mark', '')}</td>
                  {deliveryGroups.map((group, index) => (
                    <td key={index}>
                      {student.getIn(['groups', group.get('deliverySymbol', '')], '') || '-'}
                    </td>
                  ))}
                  {showImportedInfo && (
                    <>
                      <td>{getUserFullName(student, 'importedBy')}</td>
                      <td>
                        {moment(student.get('importedAt', new Date())).format('h:mma, DD MMM YYYY')}
                      </td>
                    </>
                  )}
                  <td>
                    <div className="d-flex align-items-center justify-content-center">
                      {isGrouped && (
                        <IconButton
                          sx={unGroupIconSx}
                          disabled={student.get('isDisabled', false)}
                          onClick={() => handleClickMove(student)}
                        >
                          <DriveFileMoveOutlinedIcon />
                        </IconButton>
                      )}
                      <IconButton
                        sx={deleteIconSx}
                        disabled={student.get('isDisabled', false)}
                        onClick={() => handleClickDelete(student)}
                      >
                        <DeleteOutlineIcon />
                      </IconButton>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={colSpanCount}>
                  {isAllGrouped ? (
                    <Box
                      className="d-flex align-items-center justify-content-center"
                      gap={0.5}
                      sx={{ color: '#15803D' }}
                    >
                      <HowToRegOutlinedIcon fontSize="small" />
                      All students grouped
                    </Box>
                  ) : (
                    <p className="text-center">No data found</p>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalStudents > 10 && (
        <div className="my-2 px-3">
          <MuiPagination
            totalItems={totalStudents}
            limit={limit}
            page={pageNo}
            handleLimitChange={handleLimitChange}
            handlePageChange={handlePageChange}
          />
        </div>
      )}
    </Box>
  );
};
StudentsTable.propTypes = {
  deliveryGroups: PropTypes.instanceOf(List),
  students: PropTypes.instanceOf(List),
  showImportedInfo: PropTypes.bool,
  pagination: PropTypes.object,
  isGrouped: PropTypes.bool,
  isAllGrouped: PropTypes.bool,
  handleCheckboxChange: PropTypes.func,
  handleClickDelete: PropTypes.func,
  handleClickMove: PropTypes.func,
  handleSort: PropTypes.func,
};

const Footer = ({ selectedData, backgroundColor }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [showPublishModal, setShowPublishModal] = useState(Map());
  const { noOfGroups } = getSettingsCounts(selectedData);
  const isImported = checkIsImported(selectedData);
  const selectedType = selectedData.get('type', '');
  // const show = isImported || noOfGroups;
  const show = noOfGroups > 0;

  // const handleClick = (e) => setAnchorEl(e.currentTarget);
  const handleClose = (e) => setAnchorEl(null);

  const handleClickMenu = () => {
    const type = isImported ? 'groupedStudents' : 'deliveryGroups';
    setShowPublishModal(Map({ show: true, type }));
    // handleClose();
  };

  if (!show) return null;
  return (
    <Box
      sx={{
        ...accordionFooterSx,
        ...(backgroundColor && { backgroundColor }),
        ...(selectedType !== 'year' && { paddingRight: 0 }),
        ...(selectedType === 'level' && { paddingTop: 0 }),
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {/* {isImported && (
        <MButton variant="outlined" sx={footerButtonSx}>
          Export
        </MButton>
      )} */}
      {noOfGroups > 0 && (
        <MButton sx={footerButtonSx} clicked={handleClickMenu}>
          Publish
        </MButton>
      )}
      {/* <ButtonGroup onClick={handleClick}>
        <MButton>Publish</MButton>
        <MButton sx={{ marginLeft: '1px !important', padding: 0 }}>
          <ExpandMoreIcon />
        </MButton>
      </ButtonGroup> */}
      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <MenuItem onClick={() => handleClickMenu('deliveryGroups')}>Delivery groups</MenuItem>
        <MenuItem onClick={() => handleClickMenu('groupedStudents')}>Grouped students</MenuItem>
        {/* <MenuItem onClick={() => {}}>History</MenuItem> */}
      </Menu>

      {showPublishModal.get('show') && (
        <Suspense fallback="">
          <PublishConfirmModal
            open
            data={selectedData}
            type={showPublishModal.get('type', '')}
            isPublished={false}
            handleClose={() => setShowPublishModal(Map())}
          />
        </Suspense>
      )}
    </Box>
  );
};
Footer.propTypes = {
  selectedData: PropTypes.instanceOf(Map),
  backgroundColor: PropTypes.string,
};

export const DeleteRow = ({ count, handleDelete }) => {
  return (
    <Box className="d-flex align-items-center justify-content-between f-14" sx={deleteRowSx}>
      <p>
        Selected: <span className="bold">{formatToTwoDigitString(count)}</span>
      </p>
      <MButton
        variant="text"
        sx={deleteButtonSx}
        startIcon={<DeleteOutlineIcon />}
        clicked={handleDelete}
      >
        Delete
      </MButton>
    </Box>
  );
};

function StudentGrouping() {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const userPrograms = useSelector(selectUserPrograms);
  const programYearLevel = useSelector(selectProgramYearLevel);
  const [filters, setFilters] = useState(Map());
  const [fetchProgramYearLevel] = useCallProgramYearLevel({ filters });
  const calendarId = activeInstitutionCalendar.get('_id', '');
  const programId = filters.get('programId', '');
  const term = filters.get('term', '');

  useEffect(() => {
    setFilters(Map());
    dispatch(setData(fromJS({ userPrograms: [], programYearLevel: [], groupSettings: {} })));
    if (calendarId)
      dispatch(getUserPrograms({ module: 'studentGroup', institutionCalendarId: calendarId }));
  }, [calendarId]);

  useEffect(() => {
    if (!(calendarId && programId && term)) return;
    fetchProgramYearLevel();
  }, [programId, term]);

  const { programOptions, termOptions } = useMemo(() => {
    const constructedOptions = userPrograms.reduce((newObj, item) => {
      const programId = item.get('_id', '');
      const terms = item
        .get('term', List())
        .map((term) => Map({ name: term.get('term_name', ''), value: term.get('term_name', '') }));

      return newObj
        .update('programs', List(), (prev) =>
          prev.push(Map({ name: item.get('name', ''), value: programId }))
        )
        .setIn(['terms', programId], terms);
    }, Map());

    return {
      programOptions: constructedOptions.get('programs', List()),
      termOptions: constructedOptions.get('terms', Map()),
    };
  }, [userPrograms]);

  useEffect(() => {
    const defaultProgram = programOptions.getIn([0, 'value'], '');
    const defaultTerm = termOptions.getIn([defaultProgram, 0, 'value'], '');
    setFilters(Map({ programId: defaultProgram, term: defaultTerm }));
  }, [programOptions, termOptions]);

  const handleChange = (key) => (e) => {
    const value = key === 'programId' ? e : e.target.value;
    dispatch(setData(fromJS({ programYearLevel: [], groupSettings: {} })));

    if (key === 'programId')
      return setFilters(Map({ [key]: value, term: termOptions.getIn([value, 0, 'value'], '') }));
    setFilters((prev) => prev.set(key, value));
  };

  return (
    <div className="main bg-gray pb-5">
      <div className="container-fluid pt-3 pb-4">
        <div className="bg-white p-3 border-radious-8 mb-3">
          <p className="bold mb-2 text-uppercase f-15">Filter By</p>
          <div className="row">
            <div className="col-md-4">
              <div className="form-group">
                <label className="mb-1 f-14">Program</label>
                <ProgramSelect
                  options={programOptions}
                  value={programId}
                  onChange={handleChange('programId')}
                />
              </div>
              {/* <MaterialInput
                elementType="MuiSelect"
                size="small"
                labelclass="mb-1 f-14"
                label="Program"
                placeholder="- Select -"
                elementConfig={programOptions}
                value={programId}
                changed={handleChange('programId')}
                MenuProps={menuProps}
                displayEmpty
              /> */}
            </div>
            <div className="col-md-2">
              <MaterialInput
                elementType="MuiSelect"
                size="small"
                labelclass="mb-1 f-14"
                label="Term"
                placeholder="- Select -"
                elementConfig={termOptions.get(programId, List())}
                value={term}
                changed={handleChange('term')}
                displayEmpty
              />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 pb-4 border-radious-8 position-relative min-height-300">
          {!programYearLevel.size ? (
            <Box sx={boxSx}>- No filters applied yet to show -</Box>
          ) : (
            <ProgramWiseList filters={filters} programYearLevel={programYearLevel} />
          )}
        </div>
      </div>
    </div>
  );
}

export default StudentGrouping;
