import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON>lex<PERSON>rapper, BlockWrapper, Label, Select } from '../Styled';

const CourseAddMode = ({
  data,
  getRotationalCourses,
  NotificationManager,
  setLoaderState,
  id,
  work_level_number,
}) => {
  const [nonRotation, setNonRotation] = data;
  return (
    <BlockWrapper mg="0 50px 0px 65px">
      {/* <FlexWrapper>
        <Label>Select rotation type</Label>
      </FlexWrapper>
      <FlexWrapper>
        <Input
          type="radio"
          name="rotation_type"
          id="manual_mode"
          checked={nonRotation.course_add_mode === "manual"}
          onChange={() => setNonRotation({ type: "ADD_COURSE_MANUAL" })}
        />
        <Label id="manual_mode" htmlFor="manual_mode" mg="0px">
          Manual
        </Label>
        <Input
          type="radio"
          name="rotation_type"
          id="auto_mode"
          disabled
          checked={nonRotation.course_add_mode === "auto"}
          onChange={() => setNonRotation({ type: "ADD_COURSE_AUTO" })}
        />
        <Label id="auto_mode" htmlFor="auto_mode" mg="0px">
          Automatic
        </Label>
      </FlexWrapper> */}
      {nonRotation.course_add_mode === 'manual' && (
        <FlexWrapper mg="15px 0px">
          <Label mg="0px 10px 0px 0px">Select a rotation</Label>
          <Select
            value={nonRotation.type.rotation_count}
            disabled={nonRotation.disable_title}
            onChange={(e) => {
              setNonRotation({
                type: 'ON_CHANGE',
                name: 'rotation_count',
                payload: Number(e.target.value),
              });
              setLoaderState(true);
              getRotationalCourses(
                id,
                'regular',
                work_level_number,
                Number(e.target.value),
                NotificationManager,
                setLoaderState
              );
            }}
          >
            <option value={0}>select rotation</option>

            {nonRotation['edit']['level'][
              nonRotation['work_course'] === 'level_two_courses' ? 1 : 0
            ]['rotation_course'].map((item, i) => (
              <option key={i} value={i + 1}>{`Rotation ${i + 1}`}</option>
            ))}
          </Select>
        </FlexWrapper>
      )}
    </BlockWrapper>
  );
};

CourseAddMode.propTypes = {
  data: PropTypes.object,
  id: PropTypes.string,
  work_level_number: PropTypes.string,
  getRotationalCourses: PropTypes.func,
  NotificationManager: PropTypes.func,
  setLoaderState: PropTypes.func,
};

export default CourseAddMode;
