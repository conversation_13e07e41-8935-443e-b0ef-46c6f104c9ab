import { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import { useDispatch } from 'react-redux';
import { fromJS, Map } from 'immutable';
import LocalStorageService from 'LocalStorageService';
import { encryptSessionKey, decryptPayload } from '../encryption';

export const UNIFY_SERVICE_API_URL = process.env.REACT_APP_UNIFY_SERVICE_API_URL;

function useUnifyServiceHook() {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [unifyData, setUnifyData] = useState(Map());
  const apiCalledRef = useRef(false);

  useEffect(() => {
    const getUnifyServiceData = async () => {
      if (apiCalledRef.current) return;
      apiCalledRef.current = true;

      const encryptedSessionKey = await encryptSessionKey();
      setLoading((prev) => !prev);
      axios
        .get(`${UNIFY_SERVICE_API_URL}`, {
          params: {
            code: process.env.REACT_APP_CLIENT_NAME,
          },
          headers: {
            'X-SESSION-KEY': encryptedSessionKey,
          },
        })
        .then(async (res) => {
          if (res?.data?.data) {
            const response = await decryptPayload(res.data.data);
            const collegeData = response.collegeList?.[0];
            if (collegeData) {
              const splitData = {
                baseUrl: collegeData.baseUrl,
                hebaUrl: collegeData.baseUrl,
                services: collegeData.services,
                collegeLogo: collegeData.collegeLogo,
                sso: collegeData.sso,
                ssoProvider: collegeData.ssoProvider,
                fireBase: collegeData.fireBase,
                reCaptcha: collegeData.reCaptcha,
                fileUploadUrl: collegeData.storageUrl,
                storageBucket: collegeData.storageBucket,
              };
              LocalStorageService.setCustomCookie('unifyData', splitData, true);
              setUnifyData(fromJS(splitData));
            }
          }
          setLoading((prev) => !prev);
        })
        .catch((error) => {
          setLoading((prev) => !prev);
        });
    };

    const timeoutId = setTimeout(() => {
      const response = LocalStorageService.getCustomCookie('unifyData', true);
      if (!response) getUnifyServiceData();
      else setUnifyData(fromJS(response));
    }, 1000);

    return () => {
      clearTimeout(timeoutId);
      apiCalledRef.current = false;
    };
  }, [dispatch]);

  const hebaUrl = unifyData.get('hebaUrl', '');
  return { loading, unifyData, hebaUrl };
}

export default useUnifyServiceHook;
