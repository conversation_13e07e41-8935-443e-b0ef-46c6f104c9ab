import LocalStorageService from 'LocalStorageService';
import { isDocumentMandatory, envSignUpService } from 'utils';

export const data = {
  selectedOption: null,
  validated: false,
  isloading: false,
  isValidate: true,
  degree: '',
  appointment: '',
  degreeImage: '',
  addressImage: '',
  collegeImage: '',
  residentImage: '',
  pdf: '',
  address1: '',
  academic: '',
  residendId: '',
};

export function validation() {
  let degreeImageError = '';
  const documentMandatory = envSignUpService('DOCUMENT', true);
  const hasToken = LocalStorageService.getAccessToken();
  const checkDocument = hasToken ? isDocumentMandatory() : documentMandatory;
  // if (!this.state.degreeImage) {
  //   degreeImageError = 'Degree Field is Required';
  // }

  // if (this.state.residentImage === '') {
  //   residentImageError = 'School Certificate Field Is required';
  // }

  // if (this.state.collegeImage === '') {
  //   collegeImageError = 'Entrance Exam Field Is required';
  // }

  if (this.state.degreeImage === '' && checkDocument) {
    degreeImageError = 'National ID image is required';
  }
  // if (this.state.addressImage === '') {
  //   addressImageError = 'Admission field Is required';
  // }

  // if (this.state.appointment === '') {
  //   appointmentError = 'College Image Field Is required';
  // }
  // if (this.state.address1 === '') {
  //   address1Error = 'Address Image Field Is required';
  // }

  if (
    // degreeError ||
    // residentImageError ||
    // collegeImageError ||
    degreeImageError
    // appointmentError
    // addressImageError
    // address1Error
  ) {
    this.setState({
      // degreeError,
      // residentImageError,
      // collegeImageError,
      degreeImageError,
      // appointmentError,
      // addressImageError,
      // address1Error,
    });

    return false;
  }
  return true;
}
