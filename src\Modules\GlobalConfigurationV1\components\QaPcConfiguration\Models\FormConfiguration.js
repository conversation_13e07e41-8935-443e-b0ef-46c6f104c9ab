import React, { Fragment, useEffect, useState } from 'react';
import { List, Map as IMap } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Checkbox,
  Divider,
  Menu,
} from '@mui/material';
import Button from 'Widgets/FormElements/material/Button';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import Chip from '@mui/material/Chip';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import TabContext from '@mui/lab/TabContext';
import { TabList, TabPanel } from '@mui/lab';
import Tab from '@mui/material/Tab';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import { useDispatchAndSelectorFunctionsQlc } from '../utils';
import { jsUcfirstAll } from 'utils';
import TablePagination from '@mui/material/TablePagination';
import { createDuplicateForm, updateDuplicateForm } from '_reduxapi/q360/actions';
import { DebouncedSearch } from 'Widgets/FormElements/material/DebouncedSearch';

const ITEM_HEIGHT = 38;
const tabBorderNone = {
  borderBottom: 'none !important',
  '& .MuiTab-root': {
    padding: '0px',
    margin: '0px 40px 0px 0px',
  },
};

const tabBorderNoneYear = {
  borderBottom: 'none !important',
  '& .MuiTab-root': {
    padding: '0px',
    margin: '0px 30px 0px 0px',
    minWidth: '0px',
  },
};

const textTransform = {
  textTransform: 'none',
};

const tabPadding = {
  '&.MuiTabPanel-root': {
    padding: '0px',
  },
};

const formatYear = (year) => {
  const number = year.split('r')[1];
  return 'Year ' + number;
};

export default function FormConfiguration({ open, handleClose, formName, existingData = IMap() }) {
  const [anchorEl1, setAnchorEl1] = React.useState(null);
  const open1 = Boolean(anchorEl1);
  const [programId, setProgramId] = useState('');
  const [pgmDetailsState, setPgmDetailsState] = useState(
    existingData.get('pgmDetailsState', IMap())
  );
  const [pgmDetailsParentState, setPgmDetailsParentState] = useState(
    existingData.get('pgmDetailsState', IMap())
  );
  const [pagination, setPagination] = useState(
    IMap({
      pageNo: 1,
      limit: 10,
    })
  );
  const [formData, setFormData] = useState(
    IMap({
      formName: existingData.get('formName', ''),
      describe: existingData.get('describe', ''),
    })
  );
  const {
    fetchProgramList,
    fetchProgramDetails,
    programList,
    programDetails,
    dispatch,
    programCount,
    configureTemplate,
    currentCategoryIndex,
    setMessage,
  } = useDispatchAndSelectorFunctionsQlc();

  const handleClick1 = (event) => {
    setAnchorEl1(event.currentTarget);
  };
  const handleClose1 = () => {
    setAnchorEl1(null);
  };

  const handleAccordion = (_id) => () => {
    setProgramId((prev) => (prev === _id ? '' : _id));
  };

  const handleChange = (e, pageNo) => {
    setPagination((prev) => prev.set('pageNo', pageNo));
  };
  const handleChangeRowsPerPage = (event) => {
    setPagination((prev) => prev.set('limit', parseInt(event.target.value, 10)).set('pageNo', 0));
  };

  const currentPaginationCount = pagination.get('pageNo', 1) * pagination.get('count', 10);
  function manipulatingProgramData(query) {
    let proceedApiCal = false;
    if (query !== undefined) {
      setPagination((prev) => prev.set('pageNo', 1));
      proceedApiCal = true;
    }
    const checkCountKeyIsExceeded = programList.get(currentPaginationCount, null);
    if (!checkCountKeyIsExceeded) {
      proceedApiCal = true;
    }
    if (!proceedApiCal) return;
    fetchProgramList({
      params: {
        ...pagination.toJS(),
        ...(query !== undefined && {
          searchKey: query,
          pageNo: 1,
        }),
      },
      currentPaginationCount: currentPaginationCount,
    });
  }

  useEffect(() => {
    manipulatingProgramData();
  }, [pagination]);

  function syncingEditStateAndReducer(reducerProgram) {
    setPgmDetailsState((prev) =>
      prev.update(programId, IMap(), (programData) => {
        reducerProgram = reducerProgram.set(
          'selectedProgramCount',
          programData.get('selectedProgramCount', 0)
        );
        programData
          .get('curriculum', IMap())
          .entrySeq()
          .forEach(([cKey, curriculum]) => {
            return curriculum
              .get('years', IMap())
              .entrySeq()
              .forEach(([yearNo, year]) => {
                reducerProgram = reducerProgram.setIn(
                  ['curriculum', cKey, 'years', yearNo, 'selectedCourseCount'],
                  year.get('selectedCourseCount', 0)
                );
                return year
                  .get('courseIds', IMap())
                  .entrySeq()
                  .forEach(([courseType, courseList]) => {
                    const courseString = courseList.toString();
                    return (reducerProgram = reducerProgram.updateIn(
                      ['curriculum', cKey, 'years', yearNo, 'courseIds', courseType],
                      List(),
                      (reducerCourseList) => {
                        const updateCourseList = reducerCourseList.map((course) => {
                          if (courseString.includes(course.get('courseId', ''))) {
                            const findStateCourse = courseList.find(
                              (cs) => cs.get('courseId', '') === course.get('courseId', '')
                            );
                            return findStateCourse ?? IMap();
                          }
                          return course;
                        });
                        return updateCourseList;
                      }
                    ));
                  });
              });
          });
        return reducerProgram;
      })
    );
  }

  function programDetailsCallBack(data, pgmId = programId) {  	
    if (pgmDetailsState.has(pgmId)) {
      syncingEditStateAndReducer(data.get(pgmId, IMap()));
    } else {
      setPgmDetailsState((prev) => prev.merge(data));
    }
  }

  const fetchApi = (pgmId = programId, checkedProcessCallBack, checked) => {
    fetchProgramDetails({
      params: { programId: [pgmId] },
      cb: (data) => {
        programDetailsCallBack(data, pgmId);
        checkedProcessCallBack && checkedProcessCallBack(pgmId, checked);
      },
    });
  };

  const handleProgramDelete = (programId) => (e) => {
    setPgmDetailsParentState((prev) => prev.delete(programId));
    setPgmDetailsState((prev) => prev.delete(programId));
  };

  function checkedProcessCallBack(programId, checked) {
    setPgmDetailsState((prev) => {
      if (!checked) return prev.delete(programId);
      return prev
        .updateIn([programId, 'curriculum'], IMap(), (curriculum) => {
          return curriculum.map((cur) =>
            cur.update('years', IMap(), (years) => {
              return years.map((year) =>
                year
                  .update('courseIds', IMap(), (courseIds) => {
                    return courseIds.map((courseArray) => {
                      const updatedCourseArray = courseArray.map((item) =>
                        item.set('isChecked', checked)
                      );
                      return updatedCourseArray;
                    });
                  })
                  .set('selectedCourseCount', checked ? year.get('constructedCourseCount', 0) : 0)
              );
            })
          );
        })
        .updateIn([programId, 'selectedProgramCount'], () => {
          if (!checked) {
            return 0;
          }
          return prev.getIn([programId, 'constructedProgramCount'], 0);
        });
    });
  }
  const handleIndividualProgramChecked = (pgmId) => async (e) => {
    e.stopPropagation();
    const checked = e.target.checked;
    if (!pgmDetailsState.has(pgmId)) {
      return fetchApi(pgmId, checkedProcessCallBack, checked);
    }
    checkedProcessCallBack(pgmId, checked);
  };
  function clearAll() {
    manipulatingProgramData('');
    setPgmDetailsState(pgmDetailsParentState);
    setAnchorEl1(null);
  }

  function onSave() {
    setPgmDetailsParentState(pgmDetailsState);
    setAnchorEl1(null);
  }

  function handleConstruction() {
    let payloadProgram = List();
    for (const [programId, program] of pgmDetailsState.entrySeq()) {
      let curriculum = List();
      for (const [curId, cur] of program.get('curriculum', IMap()).entrySeq()) {
        let years = List();
        for (const [yearNo, year] of cur.get('years', IMap()).entrySeq()) {
          let mergedCourseList = List();
          for (const [, courseList] of year.get('courseIds', IMap()).entrySeq()) {
            const filteredCourseList = courseList.filter((item) => item.get('isChecked', false));
            mergedCourseList = mergedCourseList.merge(filteredCourseList);
          }
          years = years.push(
            IMap({
              year: yearNo,
              courses: mergedCourseList,
            })
          );
        }
        curriculum = curriculum.push(
          IMap({
            _curriculum_id: curId,
            curriculum_name: cur.get('curriculum_name', ''),
            years: years,
          })
        );
      }

      payloadProgram = payloadProgram.push(
        IMap({
          _program_id: programId,
          program_name: program.get('program_name', ''),
          curriculum: curriculum,
        })
      );
    }

    return payloadProgram.toJS();
  }

  function handleCreate() {
    if (!formData.get('formName', '').trim()) {
      return setMessage('Enter the Form Name');
    }
    if (pgmDetailsParentState.isEmpty()) {
      return setMessage('Select the course from program');
    }
    const payload = {
      [existingData.size ? '_id' : 'categoryId']: configureTemplate.getIn(
        [currentCategoryIndex, '_id'],
        ''
      ),
      name: formData.get('formName', ''),
      describe: formData.get('describe', ''),
      formOccurrence: handleConstruction(),
    };
    if (existingData.size) return dispatch(updateDuplicateForm(payload, handleClose));

    dispatch(createDuplicateForm(payload, 'create', handleClose));
  }

  const handleInput = (key) => (e) => {
    setFormData((prev) => prev.set(key, e.target.value));
  };

  useEffect(() => {
    if (programId) {
      if (!programDetails.has(programId)) {
        fetchApi();
      } else {
        syncingEditStateAndReducer(programDetails.get(programId, IMap()));
      }
    }
  }, [programId]);

  return (
    <DialogModal
      show={open}
      onClose={handleClose}
      maxWidth={'sm'}
      fullWidth={true}
      className={`${open1 ? 'invisible' : 'visible'}`}
    >
      <div className="py-3 ">
        <div className="d-flex align-items-center pb-2 px-4">
          <div>
            <div className="q360-popup-Header">
              {existingData.size ? 'Edit Form' : `Duplicate ${jsUcfirstAll(formName)} Form`}
            </div>
          </div>
        </div>
        <div className="popup-q360-overflow px-4">
          <div>
            <MaterialInput
              changed={handleInput('formName')}
              elementType={'materialInput'}
              type={'text'}
              value={formData.get('formName', '')}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey">Form Name</div>}
              placeholder={'Enter Name'}
            />
          </div>
          <div>
            <MaterialInput
              changed={handleInput('describe')}
              value={formData.get('describe', '')}
              elementType={'materialTextArea'}
              type={'text'}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey pt-2">Describe (Optional)</div>}
              placeholder={'Describe here'}
              maxRows={'4'}
              minRows={'4'}
              bgWhite={true}
            />
          </div>
          <div>
            <div className="f-12 fw-400 text-dGrey mb-2 pt-2">Assign Courses</div>
            <div className="border border-secondary rounded pl-3 q360-select-pd ">
              <div className="d-flex align-items-center cursor-pointer" onClick={handleClick1}>
                <div className="text-muted">Select Courses</div>
                <div className="ml-auto cursor">
                  <ArrowDropDownIcon className="pt-1" />
                </div>
              </div>
              <Menu
                anchorEl={anchorEl1}
                keepMounted
                open={open1}
                onClose={handleClose1}
                PaperProps={{
                  style: {
                    maxHeight: ITEM_HEIGHT * 40.5,
                    width: '65ch',
                  },
                }}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'center',
                }}
                transformOrigin={{
                  vertical: 'bottom',
                  horizontal: 'center',
                }}
                BackdropProps={{ style: { backgroundColor: 'rgba(0, 0, 0, 0.5)' } }}
              >
                <div className="py-2 px-3">
                  {anchorEl1 && (
                    <DebouncedSearch
                      placeholder={'Search a Program Name'}
                      doSomething={manipulatingProgramData}
                    />
                  )}
                  <div>
                    <div className="d-flex align-items-center mb-2">
                      <div className="f-12 fw-400 text-lGrey pl-2">Select Courses</div>
                      {/* <div className="ml-auto">
                        <div className="d-flex align-items-center">
                          <div>
                            <Checkbox onClick={handleAllProgramCheckBox} size="small" checked={pgmDetailsState.get('constructedProgramCount',0)===pgmDetailsState.get('selectedProgramCount',0)} />
                          </div>
                          <div className="f-12 fw-500 text-dGrey">All Program</div>
                        </div>
                      </div> */}
                    </div>
                    <div className="q360-overflow-y">
                      {programList.get(currentPaginationCount, List()).map((program) => (
                        <Accordion
                          expanded={program.get('_id', '') === programId}
                          key={program.get('_id', '')}
                          className="bg-white"
                          onChange={handleAccordion(program.get('_id', ''))}
                          sx={{ borderRadius: '8px' }}
                          elevation={0}
                          disableGutters
                        >
                          <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            className="px-0 my-0"
                            sx={{
                              '&.MuiAccordionSummary-root': {
                                minHeight: '42px',
                                backgroundColor:
                                  program.get('_id', '') === programId ? '#f3f4f6' : '',
                              },
                              '.MuiAccordionSummary-content': {
                                margin: '0px',
                              },
                            }}
                          >
                            <div className="d-flex align-items-center w-100">
                              <div className="d-flex align-items-center">
                                <div>
                                  <Checkbox
                                    size="small"
                                    onClick={handleIndividualProgramChecked(program.get('_id', ''))}
                                    checked={
                                      pgmDetailsState.has(program.get('_id', '')) &&
                                      pgmDetailsState.getIn(
                                        [program.get('_id', ''), 'selectedProgramCount'],
                                        0
                                      ) ===
                                        pgmDetailsState.getIn(
                                          [program.get('_id', ''), 'constructedProgramCount'],
                                          0
                                        )
                                    }
                                  />
                                </div>
                                <div className="fw-400 f-14 text-dGrey text-capitalize">
                                  {program.get('name', '')}
                                </div>
                              </div>
                              <div className="ml-auto f-12 color-lt-gray">
                                {' '}
                                {pgmDetailsState.getIn(
                                  [program.get('_id', ''), 'selectedProgramCount'],
                                  0
                                )}{' '}
                                selected{' '}
                              </div>
                            </div>
                          </AccordionSummary>
                          <AccordionDetails>
                            {program.get('_id', '') === programId && (
                              <FormCurriculum
                                pgmDetailsState={pgmDetailsState}
                                setPgmDetailsState={setPgmDetailsState}
                                programId={programId}
                              />
                            )}
                          </AccordionDetails>
                        </Accordion>
                      ))}
                    </div>
                  </div>

                  <div className="d-flex align-items-center pt-3 justify-content-between">
                    <TablePagination
                      component={'div'}
                      className="mt-0 mb-0"
                      sx={{
                        '& .MuiTablePagination-selectLabel': {
                          fontSize: '12px',
                        },
                        '& .MuiInputBase-root-MuiTablePagination-select': {
                          fontSize: '12px',
                        },
                        '& .MuiTablePagination-displayedRows': {
                          fontSize: '12px',
                        },
                        '& .MuiIconButton-root': {
                          fontSize: '10px',
                        },
                        '& .MuiTablePagination-toolbar': {
                          paddingLeft: '0px !important',
                        },
                      }}
                      count={programCount}
                      page={pagination.get('pageNo', 1)}
                      onPageChange={handleChange}
                      rowsPerPage={pagination.get('limit', 10)}
                      onRowsPerPageChange={handleChangeRowsPerPage}
                    />
                    <div className="d-flex gap-15">
                      <Button
                        clicked={clearAll}
                        variant="outlined"
                        className=" px-4"
                        size={'small'}
                        color={'gray'}
                      >
                        Clear all
                      </Button>
                      <Button
                        clicked={onSave}
                        variant="contained"
                        className="px-4"
                        color="primary"
                        size={'small'}
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                </div>
              </Menu>
            </div>
            <div className="d-flex align-items-center text-overflow-wrap">
              {pgmDetailsParentState.entrySeq().map(([programId, program]) => {
                const courseCount = program.get('selectedProgramCount', 0);
                if (!courseCount) return null;
                return (
                  <Chip
                    key={programId}
                    label={
                      <div className="py-2">
                        <div className="f-12 fw-400 text-dGrey text-capitalize">
                          {program.get('program_name', '')}
                        </div>
                        <div className="f-10 text-mGrey fw-400">
                          {`${courseCount} ${(courseCount === 0) & 1 ? 'Course' : 'Courses'}`}
                        </div>
                      </div>
                    }
                    onDelete={handleProgramDelete(programId)}
                    deleteIcon={<CloseIcon className="f-18" />}
                    sx={{
                      '&.MuiButtonBase-root.MuiChip-root': {
                        borderRadius: '6px',
                        color: '#000000',
                        height: '45px',
                      },
                    }}
                    className="select-color mt-2 mr-3"
                  />
                );
              })}
            </div>
          </div>
        </div>
        <div className="d-flex align-items-center pt-3 px-4">
          <div className="d-flex align-items-center ml-auto gap-20">
            <div>
              <Button
                clicked={handleClose}
                variant="outlined"
                className="px-4"
                size={'small'}
                color={'gray'}
              >
                Cancel
              </Button>
            </div>
            <div>
              <Button
                clicked={handleCreate}
                variant="contained"
                className="px-4"
                color="primary"
                size={'small'}
              >
                {existingData.size ? 'Save' : 'Create'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </DialogModal>
  );
}

function FormCurriculum({ programId, pgmDetailsState, setPgmDetailsState }) {
  const curriculum = pgmDetailsState.getIn([programId, 'curriculum'], IMap()).entrySeq();

  const handleChangeCurriculum = (_, newValue) => {
    setCurriculumValue(newValue);
  };
  const [curriculumValue, setCurriculumValue] = React.useState(0);

  if (!curriculum.size) {
    return <div className="deepak_developer">No Data...</div>;
  }
  return (
    <TabContext value={curriculumValue}>
      <TabList sx={tabBorderNone} onChange={handleChangeCurriculum}>
        {curriculum.map(([curId, cur], curIndex) => (
          <Tab
            key={curId}
            label={cur.get('curriculum_name', '')}
            value={curIndex}
            sx={textTransform}
          />
        ))}
      </TabList>
      {curriculum.map(([curId, cur], curIndex) => {
        return (
          <TabPanel sx={tabPadding} value={curIndex} key={curId}>
            <FormYear
              cur={cur}
              programId={programId}
              curId={curId}
              pgmDetailsState={pgmDetailsState}
              setPgmDetailsState={setPgmDetailsState}
            />
          </TabPanel>
        );
      })}
    </TabContext>
  );
}

function FormYear({ cur, curId, programId, pgmDetailsState, setPgmDetailsState }) {
  const [yearValue, setYearValue] = React.useState(0);
  const handleChangeYear = (_, newValue) => {
    setYearValue(newValue);
  };
  const years = cur.get('years', IMap()).entrySeq();

  if (!years.size) {
    return <div className="deepak_developer">No Data...</div>;
  }

  return (
    <TabContext value={yearValue}>
      <TabList sx={tabBorderNoneYear} onChange={handleChangeYear}>
        {years.map(([yearNo], yIndex) => (
          <Tab label={formatYear(yearNo)} value={yIndex} sx={textTransform} key={yearNo} />
        ))}
      </TabList>

      {years.map(([yearNo, year], yIndex) => (
        <TabPanel sx={tabPadding} value={yIndex} key={yearNo}>
          <FormCourse
            curId={curId}
            programId={programId}
            year={year}
            yearNo={yearNo}
            pgmDetailsState={pgmDetailsState}
            setPgmDetailsState={setPgmDetailsState}
          />
        </TabPanel>
      ))}
    </TabContext>
  );
}

function FormCourse({ year, yearNo, programId, curId, pgmDetailsState, setPgmDetailsState }) {
  const courseIds = year.get('courseIds', IMap()).entrySeq();
  const gettingYearKey = [programId, 'curriculum', curId, 'years', yearNo];

  if (!courseIds.size) {
    return <div className="deepak_developer">No Data...</div>;
  }
  function handleAllCourse(e) {
    const checked = e.target.checked;
    setPgmDetailsState((prev) =>
      prev
        .setIn(
          [...gettingYearKey, 'selectedCourseCount'],
          checked ? prev.getIn([...gettingYearKey, 'constructedCourseCount'], 0) : 0
        )
        .updateIn([...gettingYearKey, 'courseIds'], IMap(), (courseIds) =>
          courseIds.map((cType) => cType.map((item) => item.set('isChecked', checked)))
        )
        .update(programId, (program) => {
          let increment = 0;
          program
            .get('curriculum', IMap())
            .map((cur) =>
              cur
                .get('years', IMap())
                .map((year) => (increment += year.get('selectedCourseCount', 0)))
            );
          return program.set('selectedProgramCount', increment);
        })
    );
  }
  const handleIndividualCourse = (courseType, index) => (e) => {
    const checked = e.target.checked;
    setPgmDetailsState((prev) =>
      prev
        .updateIn([...gettingYearKey, 'selectedCourseCount'], 0, (selectedCourseCount) => {
          if (checked) {
            return selectedCourseCount + 1;
          }
          return selectedCourseCount - 1;
        })
        .updateIn([...gettingYearKey, 'courseIds', courseType, index], IMap(), (course) =>
          course.set('isChecked', checked)
        )
        .update(programId, (program) => {
          let increment = 0;
          program
            .get('curriculum', IMap())
            .map((cur) =>
              cur
                .get('years', IMap())
                .map((year) => (increment += year.get('selectedCourseCount', 0)))
            );
          return program.set('selectedProgramCount', increment);
        })
    );
  };
  return (
    <div>
      <div className="d-flex align-items-center">
        <div className="f-12 fw-400 text-lGrey">Select Course</div>
        <div className="ml-auto">
          <div className="d-flex align-items-center">
            <Checkbox
              size="small"
              checked={
                pgmDetailsState.getIn([...gettingYearKey, 'selectedCourseCount'], 0) ===
                pgmDetailsState.getIn([...gettingYearKey, 'constructedCourseCount'], 0)
              }
              onClick={handleAllCourse}
            />
            <div className="f-12 fw-400 text-dGrey">All Course</div>
          </div>
        </div>
      </div>
      {courseIds.map(([courseType, courseList]) => {
        return (
          <Fragment key={yearNo + courseType}>
            <div className="f-12 fw-400 text-lGrey pt-2">{jsUcfirstAll(courseType)} Courses</div>
            {courseList.map((course, index) => (
              <Fragment key={course.get('courseId', index) + yearNo + courseType}>
                <div className="d-flex align-items-center ">
                  <div className="mx-3">
                    <SubdirectoryArrowRightIcon
                      color="primary"
                      className="cursor-pointer"
                      sx={{ fontSize: 10 }}
                    />
                  </div>
                  <div className="d-flex align-items-center">
                    <div>
                      <Checkbox
                        onClick={handleIndividualCourse(courseType, index)}
                        size="small"
                        checked={course.get('isChecked', false)}
                      />
                    </div>
                    <div className="d-flex align-items-center">
                      {jsUcfirstAll(courseType) === 'Shared From' && (
                        <ShareOutlinedIcon color="primary" sx={{ fontSize: 15 }} className="mr-2" />
                      )}
                      <div className="f-14">
                        {course.get('course_code', '')} -{' '}
                        {jsUcfirstAll(course.get('course_name', ''))}
                      </div>
                    </div>
                  </div>
                </div>
                <Divider />
              </Fragment>
            ))}
          </Fragment>
        );
      })}
    </div>
  );
}
