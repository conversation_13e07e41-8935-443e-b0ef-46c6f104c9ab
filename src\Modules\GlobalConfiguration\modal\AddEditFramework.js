import React from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';

function AddEditFramework({ open, handleClose }) {
  const handleChange = (name, event) => {};
  return (
    <div>
      <Modal show={Boolean(open)} onHide={handleClose}>
        <Modal.Header className="border-none pb-0">
          <Modal.Title className="f-20">Create New Framework</Modal.Title>
        </Modal.Header>

        <Modal.Body className="pt-4">
          <div className="mb-2">
            <FormControl variant="outlined" className="wd-100">
              <FormLabel component="legend">Framework Name</FormLabel>
              <OutlinedInput
                id="outlined-adornment-weight"
                className="outline-text-input"
                onChange={handleChange('weight')}
                labelWidth={0}
              />
            </FormControl>
          </div>
          <div className="mb-2">
            <FormControl variant="outlined" className="wd-100">
              <FormLabel component="legend">Sort Code</FormLabel>
              <OutlinedInput
                id="outlined-adornment-weight"
                className="outline-text-input"
                onChange={handleChange('weight')}
                labelWidth={0}
              />
            </FormControl>
          </div>
        </Modal.Body>

        <Modal.Footer className="border-none">
          <MButton color="inherit" variant="outlined">
            <Trans i18nKey={'cancel'}></Trans>
          </MButton>

          <MButton color="primary" variant="contained">
            <Trans i18nKey={'save'}></Trans>
          </MButton>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

AddEditFramework.propTypes = {
  open: PropTypes.object,
  handleClose: PropTypes.func,
};

export default AddEditFramework;
