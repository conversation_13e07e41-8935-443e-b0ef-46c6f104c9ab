import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import { useHistory, withRouter } from 'react-router-dom';
import { NavButton } from '../Styled';
import { selectProgramCalendarDashboard } from '_reduxapi/program_calendar/selectors';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { jsUcfirst, removeURLParams } from 'utils';

const NavRowTermsButtons = ({ programCalendarDashboard, color, theme }) => {
  const history = useHistory();
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlTerm = params.get('term');
  const triggerFunction = (term) => {
    const location = history.location;
    const changedPath = location.pathname;
    const pathSearch = removeURLParams(location, ['term']) + `&term=${term}`;
    history.push(changedPath + pathSearch);
  };

  const terms = programCalendarDashboard.get('term', List());

  return (
    <Fragment>
      {terms &&
        terms.size > 0 &&
        terms.map((term, index) => {
          const termName = term.get('term_name', '');
          return (
            <NavButton
              key={index}
              className={`${urlTerm === termName ? theme : ''}`}
              onClick={() => {
                triggerFunction(termName);
              }}
              color={color}
            >
              {jsUcfirst(termName)}
            </NavButton>
          );
        })}
    </Fragment>
  );
};

NavRowTermsButtons.propTypes = {
  programCalendarDashboard: PropTypes.instanceOf(Map),
  color: PropTypes.string,
  theme: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    programCalendarDashboard: selectProgramCalendarDashboard(state),
  };
};

export default connect(mapStateToProps)(withRouter(NavRowTermsButtons));
