import React from 'react';
import PropTypes from 'prop-types';
import { ThemeProvider } from '@mui/styles';
import Button from '@mui/material/Button';
import { Dialog, DialogActions } from '@mui/material';
import { MUI_THEME } from '../../../utils';

function AlertConfirmModal(props) {
  const {
    show,
    variant,
    title,
    titleIcon,
    body,
    confirmButtonLabel,
    cancelButtonLabel,
    onClose,
    onConfirm,
    data,
  } = props;
  return (
    <Dialog fullWidth={true} maxWidth={'sm'} open={show} onClose={onClose}>
      {/* <Modal show={show} centered onHide={onClose}> */}
      <div className="p-4">
        <div className="d-flex pb-3">
          {titleIcon && <div className="pr-2">{titleIcon}</div>}
          <div className="f-20 mb-0 digi-black">{title}</div>
        </div>
        <div className="pb-2 border-radious-8">{body}</div>
        <DialogActions className={'mt-2 p-0'}>
          <ThemeProvider theme={MUI_THEME}>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => {
                onClose();
                if (data && data.operation === 'finish') {
                  data.callback && data.callback();
                }
              }}
            >
              {cancelButtonLabel}
            </Button>
            {variant === 'confirm' && (
              <Button variant="contained" color="primary" onClick={() => onConfirm(data)}>
                {confirmButtonLabel}
              </Button>
            )}
          </ThemeProvider>
        </DialogActions>
      </div>
    </Dialog>
  );
}

AlertConfirmModal.defaultProps = {
  variant: 'confirm',
  title: '',
  body: '',
  confirmButtonLabel: 'YES',
  cancelButtonLabel: 'NO',
};

AlertConfirmModal.propTypes = {
  show: PropTypes.bool,
  variant: PropTypes.oneOf(['alert', 'confirm']),
  title: PropTypes.string,
  titleIcon: PropTypes.object,
  body: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  confirmButtonLabel: PropTypes.string,
  cancelButtonLabel: PropTypes.string,
  onClose: PropTypes.func,
  onConfirm: PropTypes.func,
  data: PropTypes.any,
};

export default AlertConfirmModal;
