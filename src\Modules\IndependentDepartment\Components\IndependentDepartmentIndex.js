import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory } from 'react-router-dom';
import { getInstitutionHeader } from 'v2/utils';
import {
  selectProgramDepartment,
  selectDepartmentDashboard,
  selectIndependentDepartmentList,
} from '_reduxapi/program_input/v2/selectors';
import * as actions from '_reduxapi/program_input/v2/actions';
import * as globalConfigActions from '_reduxapi/global_configuration/actions';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import IndependentDepartment from './Independent/IndependentDepartment';
import { getFullLabelName } from 'Modules/Shared/v2/Configurations';

function IndependentDepartmentIndex({
  departmentList,
  programInputList,
  getDepartmentProgram,
  addEditDeleteDepartment,
  addEditDeleteSubject,
  setData,
  getDepartmentDashboard,
  departmentDashboardDetails,
  getIndependentDepartment,
  getProgramInput,
  updateIndependentDepartment,
}) {
  const history = useHistory();
  const institutionHeader = getInstitutionHeader(history);
  const institutionId = institutionHeader?._institution_id;
  const [departmentTabValue, setDepartmentTabValue] = useState('academic');

  const departmentRef = useRef();

  useEffect(() => {
    getProgramInput({ header: institutionHeader });
  }, []); // eslint-disable-line

  useEffect(() => {
    getDepartmentDashboard(institutionId);
  }, []); // eslint-disable-line

  useEffect(() => {
    getIndependentDepartment(institutionId, {
      pageNo: 1,
      limit: 50,
      type: departmentTabValue,
      showShared: true,
    });
  }, [departmentTabValue]); // eslint-disable-line

  const departmentsOperation = (
    departmentName,
    closeModal,
    operation,
    departmentID,
    departmentType,
    selectedProgramId,
    programName,
    oldProgramId,
    isDepartmentAssignedOrShared
  ) => {
    const callBackFunc = () => {
      closeModal && closeModal(false);
      departmentRef.current();
      getDepartmentDashboard(institutionId);
    };

    let postData = {};
    if (operation !== 'delete') postData = { departmentName };
    if (operation === 'share') postData = departmentName;
    if (operation === 'add') {
      postData = {
        ...postData,
        _institution_id: institutionId,
        type: departmentType,
      };
      if (departmentType === 'academic') {
        postData._program_id = selectedProgramId;
        postData.programName = programName;
      }
    }
    if (operation === 'edit') {
      postData = {
        ...postData,
      };
      if (departmentType === 'academic') {
        if (!isDepartmentAssignedOrShared) {
          postData.programName = programName;
          postData.type = departmentType;
        }
        postData._program_id = selectedProgramId;
        postData.isDepartmentAssignedOrShared = isDepartmentAssignedOrShared;
      } else if (departmentType === 'admin') {
        if (!isDepartmentAssignedOrShared) {
          postData.type = departmentType;
        }
        postData.isDepartmentAssignedOrShared = isDepartmentAssignedOrShared;
      }
    }
    if (operation !== 'edit') {
      addEditDeleteDepartment(operation, postData, callBackFunc, departmentID);
    } else {
      updateIndependentDepartment(operation, postData, callBackFunc, departmentID);
    }
  };

  const subjectsOperation = (operation, data, departmentID, closeModal) => {
    const callBackFunc = () => {
      closeModal && closeModal(false);
      departmentRef.current();
      getDepartmentDashboard(institutionId);
    };

    //operation, formData, callBack, departmentID
    addEditDeleteSubject(operation, data, callBackFunc, departmentID);
  };

  const getProgramList = () => {
    getDepartmentProgram(institutionId);
  };

  const translateInput = {
    departmentName: getFullLabelName({ label: 'departments' }),
    subjectName: getFullLabelName({ label: 'subject' }),
    term: getFullLabelName({ label: 'term' }),
    program: getFullLabelName({ label: 'program' }),
    programName: getFullLabelName({ label: 'program' }),
    preRequisiteName: getFullLabelName({ label: 'Pre-Requisite Course' }),
  };

  const value = {
    departmentList,
    programInputList,
    departmentsOperation,
    subjectsOperation,
    setData,
    getProgramList,
    translateInput,
    isIndependent: true,
    departmentDetails: departmentDashboardDetails,
    setDepartmentTabValue,
    departmentTabValue,
    getIndependentDepartment,
    institutionId,
    departmentRef,
  };

  return (
    <parentContext.departmentContext.Provider value={value}>
      <IndependentDepartment />
    </parentContext.departmentContext.Provider>
  );
}

IndependentDepartmentIndex.propTypes = {
  departmentList: PropTypes.instanceOf(Map),
  programInputList: PropTypes.instanceOf(Map),
  addEditDeleteDepartment: PropTypes.func,
  getDepartmentProgram: PropTypes.func,
  setData: PropTypes.func,
  addEditDeleteSubject: PropTypes.func,
  departmentDashboardDetails: PropTypes.instanceOf(Map),
  getDepartmentDashboard: PropTypes.func,
  getIndependentDepartment: PropTypes.func,
  getProgramInput: PropTypes.func,
  updateIndependentDepartment: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    departmentList: selectIndependentDepartmentList(state),
    programInputList: selectProgramDepartment(state),
    departmentDashboardDetails: selectDepartmentDashboard(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, { ...actions, ...globalConfigActions })
)(IndependentDepartmentIndex);
