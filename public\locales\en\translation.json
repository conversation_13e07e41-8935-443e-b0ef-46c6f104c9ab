{"error": "Error", "an_error_occured_getting_staff_designation": "An error occured getting staff designation", "allow_editing": "Allow Editing", "dont_allow": "Don't Allow Editing", "dashboard": "dashboard", "my_courses": "My Courses", "my_leave": "My Leaves", "calendar": "Calendar", "email_id_config": "Email Id Configuration", "configured": "Configured", "break_existed": "This break has be <br/> created by University", "add_break": "Add Break", "profile": "Profile", "event_existed": "This event has be <br/> created by University", "national_college": "National College", "for_medical_studies": "for Medical Studies", "upcoming_events": "Upcoming Events", "sending_mail": "Sending Test Mail...", "mail_send": "Test Mail sent successfully", "test_again": "Test again", "view_all": "View All", "no_data_events": "No Upcoming Events Found", "no_level": "No Level Found ...", "no_record_found": " No records found ...", "no_course_found": " No Course found ...", "session_rating": "Session Ratings", "my_course": "MY COURSES", "recent_chat": "Recent Chat", "recent_doc": "Recent documents", "recent_activity": "RECENT ACTIVITIES", "activity": "ACTIVITY", "course_name": "COURSE NAME", "course_name_small": "Course Name", "status": "Status", "action": "Actions", "start_quiz": "START QUIZ", "edit_qus": "EDIT QUESTION", "profile_view": "Profile View", "logout": "Logout", "primary_program": "PRIMARY PROGRAM", "department": "DEPARTMENT", "department_small": "Department", "staff_type": "STAFF TYPE", "enroll_program": "ENROLLED PROGRAM", "enroll_term": "ENROLLED TERM", "enroll_date": "ENROLLED DATE", "email": "Email", "submit": "SUBMITTED", "result": "VIEW RESULT", "notifications": "Notifications", "no_notifiction": "No Notifications Found...", "digival": "Developed and Maintained by DigiVal Solutions", "digival2": "Powered by DigiVal Solutions", "all": "All", "start": "To start ", "complete": "Completed", "progress": "In Progress", "course_search": "Search Course Code/Course Name", "feedbacks": "feedbacks", "sessions": "Sessions", "all_session": "ALL SESSIONS", "all_document": "ALL DOCUMENTS", "weeks": "Weeks", "all_activity": "ALL ACTIVITIES", "chats": "CHATS", "the_subjects": "Subjects", "s_no": "S.No", "code": "Code", "program_names": "Program Names", "program_type": "Program Type", "college_program_type": "College Program Type", "program_level": "Program Level", "degree_name": "Degree Name", "department_and_subjects": "Department & Subjects", "session_types": "Session Types", "active_curriculum": "Active Curriculum", "archived": "Archived", "active_programs": "Active Programs", "all_programs": "All Programs", "item_per_page": "Items per page", "page": "Page", "of": "of", "degree": "Degree", "import_program": "Import Program", "import_department": "Import Department", "import_session_type": "Import SessionType", "import_delivery_type": "Import DeliveryType", "import_curriculum": "Import Curriculum", "import_course": "Import Course", "import_session": "Import Session", "import_slo": "Import {{<PERSON><PERSON>}}", "program": "Program", "pre_requisite_courses": "Pre-Requisite Courses", "pre_requisite_course": "Pre-Requisite Course", "choose_pre_requisite_courses": "Choose a Pre-Requisite Course", "no_course_pre_requisite": "No course assigned in pre-requisite", "add_new": "ADD NEW", "add_new_small": "Add New", "import": "IMPORT", "search_code_type": "Search Code / Name", "undergraduate": "Undergraduate", "postgraduate": "Postgraduate", "pre_requisite": "Pre Requisite", "all_frameworks": "All Frameworks", "impact_mapping": "Impact Mapping", "content_mapping": "Content Mapping", "standard_range_settings": "Standard Range Settings", "add_new_framework": "Add New Framework", "theme_domain": "Theme / Domains", "add_domain_theme": "ADD DOMAIN / THEME", "cancel": "Cancel", "ok": "OK", "ac": "AC :", "uc": "UC :", "cc": "CC :", "content_map": "Content Map", "selected_course": "Selected course", "no_records_to_display": "No records to display", "plo_hash": "{{name}} ##", "done": "Done", "confirm": "Confirm", "delete": "Delete", "remove": "REMOVE", "Remove": "Remove", "are_you_sure_you_want_to_delete_the_selected": "Are you sure you want to delete the selected", "archiveConfirmation": "Are you sure you want to archive the selected ‘Preperatory Year’ ? You can restore anytime later", "archive_selected": "Are you sure you want to archive the selected", "restore_anytime": "You can restore anytime later", "question_mark": "?", "framework_delete_desc": "This action will permanently remove the selected framework and its configurations from the application!", "requisite_delete_desc": "This action will permanently remove the selected Pre-Requisite Course and its configurations from the application!", "departmentDelete": "This action will permanently remove the selected <departToolTip/> and its <subjectToolTip/> from the application!", "subjectDelete": "This action will permanently remove the selected {{subjectName}} from the {{departmentName}} !", "requisite_desc": "Are you sure you want to delete the selected ‘Preperatory Year’ ?", "restoreProgram": "Are you sure you want to restore the selected program ?", "confirmRestore": "Are you sure you want to restore the selected ", "requisite_cannot_desc": "As this program is currently running, you can not Archive this program", "changed_not_saved": "The changes will not be saved if you choose to cancel", "remove_selected": "This action will permanently remove the selected <typeToolTip/> and its configurations from the application!", "config_application": "and its configurations from the application!", "confirm_cancel": "Are you sure you want to cancel?", "framework": "Framework", "Outcome_Naming": "Outcome Naming", "Framework1": "Framework 1", "Mode1": "Mode 1", "Mode2": "Mode 2", "activate_profile": "Activate Profile", "domain": "Domain", "impact": "Impact", "impact_s": "impact", "content_s": "content", "curriculum": "Curriculum", "add": "Add", "update": "Update", "name": "Name", "sort_code": "Short Code", "domain_name": "Domain Name", "save_domain": "Save Domain", "delete_domain": "Delete Domain", "edit_domain": "Edit Domain", "delete_framework": "Delete Framework", "edit_framework": "Edit Framework", "required_reply_type": "Reply type is required", "updated_review": "Review Updated Successfully", "updated_all_review": "All Review Update Successfully", "framework_header": {"label": "Frameworks", "title": "Frameworks for learning outcomes ", "desc": "Create all the frameworks which is going to be used across all the program curriculum", "no_domain_found_under_this_framework": "No domains found under this framework"}, "confirm_delete": "Confirm Delete", "domain_details": {"domain_confirm_desc": "Are you sure you want to delete the selected domain"}, "delete_upper": "DELETE", "cancel_upper": "CANCEL", "confirm_upper": "CONFIRM", "subjects": "Subjects", "dept": "Dept", "view": "VIEW", "configure": "CONFIGURE", "restore": "RESTORE", "customize": "Customize", "standard_range_setting": {"auto_save_desc": "Values will be auto saved", "label": "Standard range settings", "desc": "Under each framework, for each program curriculum, define the maximum & minimum number of {{CLO}}s required to be mapped with {{PLO}}s under each domain/theme.", "expand": "EXPAND", "expanded_desc": "Standard range of the framework defined within the framework may be applied to all the Program’s curriculum that follows it."}, "year": "Year", "higher_limit": "Higher limit", "lower_limit": "Lower limit", "tabs": {"frameworks": "Frameworks", "programs": "Programs"}, "mapping": {"types_label": "Mapping Types", "types": {"content": "Content", "impact": "Impact"}, "add_level": "Add Level", "description": "Description", "name_of_level": "Name of Level", "tips": "Tips"}, "Ibn Sina National Medical College": "Ibn Sina National Medical College", "you_are": "YOU ARE", "academic_year": "Academic Year", "rows_import": "rows to import", "select_appropriate": "Select appropriate options", "select_a_file": "Select a file to import", "accepted_formats": "Accepted file formats: xlsx", "download_template": "DOWNLOAD TEMPLATE", "browse": "BROWSE", "program_name": "Program Name", "division_name": "Division Name", "add_save": "Add & Save", "type": "Type", "program_code": "Program Code", "not_imported": "to be not imported...", "data_check": "Data check", "list_error_entity": "List of entities that cannot be imported.", "export_entry": "EXPORT INVALID ENTRIES", "back": "BACK", "back_s": "back", "continue": "CONTINUE", "error_message": "Error Message", "department_name": "Department Name", "session_name": "Session Name", "session_symbol": "Session Symbol", "delivery_name": "Delivery Name", "Course_Code": "Course Code", "Delivery_Type": "Delivery Type", "delivery_number": "Delivery Number", "delivery_topic": "Delivery Topic", "Curriculum_Name": "Curriculum Name", "updated_successfully": "Updated Successfully", "Hour_As": "Hour As", "edit": "Edit", "all_fields_mandatory": "All Fields are Mandatory", "no_of_terms": "No.of {{name}}s", "academic_per_year": " Academic Schedule Per {{Year}}", "academicPerYear": "Academic Schedule Per <year/>", "term_no": "{{name}} No", "term_name": "{{name}} Name", "save": "Save", "You_can": "You can", "anytime_later": "anytime later.", "are_you_sure_you_want_to": "Are you sure you want to", "no_data_found": "No Data Found", "map_type": {"validation": {"level_name": "Level name already exists", "name_length": "Level name should contain minimum 3 characters long", "short_code": "Short code already exists", "description_length": "Description should contain minimum 3 characters long", "description_max_length": "Description should contain maximum", "description_char_long": "characters long"}, "operations": {"add": "ADD", "update": "Update"}, "mapping_values": {"impact": "High, Medium and Low", "content": "Introductry, Proficient and Advanced"}, "tips": {"usually": "Usually", "between_conent": "type of mapping will be"}, "form": {"labels": {"name": "Name of Level", "sort": "Short Code to be used while Aligning in the table", "description": "Description"}}, "maximum": "Maximum", "characters": "characters", "remaining": "Remaining", "title_desc": "Type of level and their description"}, "forgot": "Forgot Password?", "signIn": "Sign In", "sendotp": "Send OTP To", "password": "Password", "sendotps": "Send OTP", "mobile": "Mobile", "otpsendto": "OTP Send To Your", "mobileCheck": "  Mobile Number: 9*********", "emailId": "Email ID", "enter_email": "Enter Email Id", "enter_password": "Enter Password", "forgot_msg": "Choose a method to receive OTP", "enterotp": "Enter OTP", "resend_otp": "Resend OTP In", "resend_otps": "Resend OTP", "enter_otp": "Enter 4 Digit OTP", "copyright": "Copyright", "developed": "Developed", "UAE": "Maintained by Digival IT Solutions - UAE (in technology partnership with Digival Solutions Pvt Ltd, India).", "login_footer": "Digival IT Solutions -  UAE  (in technology partnership with Digival Solutions Pvt Ltd, India).", "alright_reserved": " All rights reserved", "login_footer_v2": "Digival IT Solutions", "otp_required": "OTP is Required", "terms": "{{name}}s", "term": "{{name}}", "UG": "UG", "PG": "PG", "Diploma": "Diploma", "used_in": "Used in", "side_nav": {"menus": {"global_configuration": "Global Configuration", "dashboard": "Dashboard", "calendar": "Calendar", "program_calendar": "Program Calendar", "user_management": "User Management", "student_grouping": "Student Grouping", "infra_management": "Infra Management", "roles_permissions": "Roles & Permissions", "leave_management": "Leave Management", "program_inputs": "Program Inputs", "schedule_management": "Schedule Management", "reports_analytics": "Reports & Analytics", "institution_calendar": "Institution Calendar", "reviewer": "Reviewer", "staff_management": "Staff Management", "student_management": "Student management", "onsite": "Onsite", "remote": "Remote", "leave_settings": "Leave Settings", "my_leaves": "My leaves", "student_register": "Student Register", "report_absence": "Report Absence", "approver": "Approver", "course_scheduling": "Course Scheduling", "extra_curricular": "Extra curricular & Break", "assign_course_coordinator": "Assign Course Coordinator", "assessment-management": "Assessment Management", "assessment_type": "Assessment Type", "assessment_library": "Assessment Library", "attainment_calculator": "Attainment Calculator", "attainment_settings": "Attainment Settings", "attainment_reports": "Attainment Reports", "curricular_monitoring": "Curricular Monitoring", "lms_reports": "Student Reports", "Academic_Accountability_Management": "Academic Accountability Management", "Faculty_Academic_Accountability_Management": "Faculty Academic Accountability Management", "faculty_leaves": "Faculty Leaves", "faculty_report_absence": "Faculty Report Absence", "User_Module_Permission": "User Module Permission", "Tag_Master": "Tag Master", "user_global_search": "User Global Search"}}, "pre-requisite": "Pre Requisite", "curriculam": "Curriculam", "curricula": "<PERSON><PERSON><PERSON><PERSON>", "IBN": "Ibn <PERSON>a National College for Medical Studies", "college_name": "<PERSON>", "collegename": "<PERSON>", "configuration": {"title": "Program Configuration", "department": "Department", "add_new": "Add New", "edit": "Edit", "add_new_dept": "Add New Department", "add_new_session": "Add New Session Type", "edit_session": "Edit Session Type", "all_dept": "All Departments", "session_type": "Session Type", "next": "Next", "save_and_continue": "Save & Finish", "share_with": "Shared with:", "subject_share_with": "Shared with:", "not_shared": "Not shared with any program", "add_subject": "ADD SUBJECT", "no_subject": "No subjects", "delivery_type": "Delivery Types", "contact_hour": "Contact Hours / Credit Hour", "contact_hours": "Contact Hours", "duration_contact": "Duration / Contact", "duration_contact_hour": "Duration / Contact Hour", "duration": "Duration", "no_delivery_type": "No delivery types", "add_delivery_type": "Add Delivery Type", "minutes": "Minutes", "in_minutes": "Minutes", "hours": "Hours", "periods": "Periods", "Configure Departments": "Configure Departments", "Configure Session Types": "Configure Session Types", "dept_name": "Department Name", "add_new_subject": "ADD NEW SUBJECT", "subject": "Subject", "the_subject": "Subject", "edit_subject": "Edit Subject", "edit_dept": "Edit Department", "subject_name": "Subject Name"}, "validations": {"terms": "The number of {{term}}s must be greater than 0", "program_name": "The program name must contain at least three characters", "name": "Program Name should have at least three characters", "code": "Program Code should have at least three characters"}, "delete_department": {"title": "Delete Department", "body": "This action will permanently remove the selected department and its subjects from the application!", "confirm": "Are you sure you want to delete the selected "}, "delete_subject": {"title": "Delete Subject", "body": "This action will permanently remove the selected subject from the department!", "confirm": "Are you sure you want to delete the selected "}, "delete_type": {"title": "Delete Delivery Type", "body": "This action will permanently delete the selected delivery type from the application!", "confirm": "Are you sure you want to delete "}, "delivery_type": {"confirm_delete": "Are you sure you want to delete delivery type"}, "session_type_alert": {"confirm_delete": "Are you sure you want to delete session type"}, "session_type_delete": "This action will permanently remove the selected session type and related delivery types!", "course_delete": "This action will permanently remove the selected <courseToolTip/> and related details!", "theme_delete": "This action will permanently remove the selected <themeToolTip/> and related details!", "sub_theme_delete": "This action will permanently remove the selected <themeToolTip/> and related details!", "delivery_type_delete": "This action will permanatly delete the selected delivery type from the application!", "session_type_delete_desc": "Are you sure you want to delete {{type}} type '{{name}}' ?", "cDetails": {"shared_department": "Shared Departments", "departments": "Departments", "no_departments": "No department for this program", "dept_header": "Departments / Departments Shared with the Program", "department": "Department", "credit": "Credit hour", "duration_contact": "Duration / Contact hour", "settings": "Settings", "master_graph": "Master Graph"}, "master_graph": {"matrix": "Mapping matrix for", "curr": "Curriculum", "a": "a", "domain": "DOMAIN/THEME", "year_level": "YEAR/{{LEVEL}}", "delivery_type": "DELIVERY TYPE", "selected": "SELECTED", "compare": "COMPARE WITH DEFINED", "edit_range": "Edit ranges:", "select": "Select", "range": "Range defined for", "framework_range": "Framework standard range", "theme": "THEME", "year": "YEAR", "no_of_years": "NUMBER OF YEARS IN THE SELECTED PROGRAM", "no_of_slo": "NO. OF {{CLO}}", "mapped_with_plo": "MAPPED WITH {{PLO}}", "no_of_clo": "Number of {{<PERSON>L<PERSON>}} aligned with {{PLO}} for selected", "program_caps": "PROGRAM", "level_outcome": "{{LEVEL}} OUTCOME", "total_in": "TOTAL IN", "customer_settings": "Customize Standard Range specific to:", "tip": "Tip", "is_under": "is under", "with_plo": "with {{PLO}}, it is selected by default as compulsory alignment.", "to_clo": "To map {{CLO}}'s of the", "year_program": "Year Program, select Curriculum under different frameworks as per your institution follows."}, "curriculum_keys": {"pls_select_course": "Please select a course", "group_course": "Group course", "assign_course": "Assign course", "no_level_found": "No level found", "search_code_name": "Search Course Code / Course Name", "year_error": "End year should be equal and greater than start year.", "week_error": "End week should be equal and greater than start week.", "level_required": "Levels per year is required.", "credit_hours": "Credit Hours", "hours_as": "Hours As", "set_value_as": "Set Value as", "total": "Total", "to_sm": "to", "framework": "Framework", "optional": "Optional", "years": "years", "levels_per_year": "{{name}}s per Year", "level_name": "{{name}} Name", "start_week": "Start Week", "end_week": "End Week", "duration": "Duration", "weeks": "weeks", "week": "Week", "save": "SAVE", "save_continue": "SAVE & CONTINUE", "enter_credit_hours": "Enter Credit Hours", "max_credit_hours": "Maximum Credit Hours", "min_credit_hours": "Minimum Credit Hours", "total_credit_hours": "Total Credit Hours", "choose_framework": "Choose Framework", "program_duration": "Program Duration", "starts_at": "Starts At", "ends_at": "Ends At", "total_no_of_levels": "Total No .of.{{name}}s"}, "assign": "ASSIGN", "assign_small": "Assign", "courses": "Courses", "durations": "Durations", "duration": "Duration", "archive_found": "No Archive Curriculum...", "add_new_curriculam": " Add New Curriculum", "create_new_curriculum": "Create New Curriculum", "start_week": "Start Week", "end_week": "End Week", "assign_course": " Assign Course", "course_master_list": "Course Master List", "model_pupup": {"collage_name_required": "College name is required", "street_name_required": "Street name is required", "city_name_required": "City name is required", "state_name_required": "State name is required", "save_upper": "SAVE", "add_new_college": "Add New College"}, "delete_messages": {"program": "This action will permanently remove the selected program and its configurations from the application!", "curriculum": "This action will permanently remove the selected curriculum and its configurations from the application!", "pre_requisite": "This action will permanently remove the selected pre-requisite course and its configurations from the application!", "vaccine_category": "This action will permanently remove the selected category and related details!", "vaccine_details": "This action will permanently remove the selected vaccination details!", "employment_schedule": "This action will permanently remove the selected schedule and related details!", "auxiliary_allocation": "This action will permanently remove the selected auxiliary allocation and related details!"}, "labels": {"session_name": "Session Name", "session_symbol": "Session Symbol", "credit_hour": "Contact Hours per Credit Hour", "period": "Period for a week / Credit", "session_duration": "Session Duration (optional)", "period_duration": "Period Duration", "delevery_name": "Delivery Name", "delivery_symbol": "Delivery Symbol", "delivery_duration": "Delivery Duration", "periods": "Period / Week"}, "placeholder": {"session_name": "Enter session name", "session_symbol": "Enter session symbol", "credit_hour": "Enter contact hours per credit hour", "session_duration": "Enter session duration", "period": "Enter period for a week / Credit", "period_duration": "Enter Period Duration", "delivery_name": "Enter delivery name", "delivery_symbol": "Enter delivery symbol", "delivery_duration": "Enter delivery duration"}, "assigned_in": "Assigned In", "add_new_course": "ADD NEW COURSE", "course_status": "Course Status", "course_type": "Course Type", "no_of_weeks": "No.of Weeks", "occurrence": "Occurrence", "levels_assigned": "{{Level}}s Assigned", "basic_details": "Basic Details", "course_recurring_at": "Course Recurring at", "course_occurring_at": "Course Occurring at", "choose_levels": "Choose Levels", "duration_in_weeks": "Duration in Weeks", "delivering_subjects": "Delivering Subjects", "choose_subject": "Choose Subject", "administrating_subject/dept/program": "Administrating Subject/Dept/Program", "allow_scheduling": "Allow to edit credit hours while scheduling?", "save_as_draft": "SAVE AS DRAFT", "applicable_delivery_types": "Applicable Delivery Types", "duration_sharing": "Duration and Sharing Details", "duration_&_sharing_with": "Duration & Sharing with", "duration_in_min": "Duration (min)", "delivery": "Delivery", "number": "Number", "symbol": "Symbol", "delivery_symbol_new": "Delivery Symbol", "delivery_number_new": "Delivery Number", "contact_hours_summary": "Contact Hours Summary", "add_new_sessions": "Add New Session", "session_flow": "Session flow", "manage_curriculum": "Manage {{PLO}}s, {{CLO}}s and {{SLO}}s for each Program’s curriculum", "program/year/level": "Program/Year/Level/Course", "no_plo": "NO. {{PLO}}", "clo_done": "{{<PERSON>L<PERSON>}} DONE", "map_types": "Map Type", "map_plo_clo_slo": "Map {{PLO}}-{{CLO}}-{{SLO}}", "s_for": "s for", "delete_framework_content": "Once the framework is changed, the previous data under the specific framework will be automatically deleted", "sure_delete": "Are you sure?", "yes": "Yes", "no": "No", "is": "is", "are": "are", "YES": "YES", "NO": "NO", "yes_cancel": "Yes, <PERSON>cel", "map": "MAP", "plo_clo": "{{PLO}}-{{CLO}}", "clo_slo": "{{<PERSON>L<PERSON>}}-{{SLO}}", "selected_mapping": "Select Programs, framework", "mapping_type": "mapping type", "clo_plo_mapping": "{{CLO}} - {{PLO}} Mapping", "step_2": " PROCEED TO STEP 2", "SLO": "{{SLO}}", "new": "New", "choose_framework": "Choose Framework", "starts_at": "Starts at", "ends_at": "Ends at", "to": "to", "enter_credit": "Enter Credit Hours", "min_credit": "Min Credit Hours", "max_credit": "Max Credit Hours", "total_credit": "Total Credit Hours", "configure_incomplete": "Configuration Incomplete!", "select_program": "Select Programs / Departments", "Sharing": "Sharing", "non": "-NA-", "session_type": "Session Type", "should_achieve": "Should achieve targeted credit hours", "department_success_msg": "The departments and session types for the {{name}} program configured successfully.Continue to add curriculum and courses to complete the program configuration.", "configuration_successful": "Configuration Successful", "not_share": " Not shared with any program", "subject_shared": "Subject Shared with", "constant": {"alert": "<PERSON><PERSON>", "hrs": "Hrs", "total": "Total", "splitup": "Splitup", "fixedValues": "FixedValues", "range_of_value": "Range of Values", "st_year": "1st Year", "nd_year": "2nd Year", "rd_year": "3rd Year", "th_year": "4th Year", "5th_year": "5th Year", "6th_year": "6th Year", "7th_year": "7th Year", "level": "{{name}}", "start_week": "Start Week", "end_week": "End Week", "year": "Year", "added": "Added", "drafts": "Drafts", "standard": "Standard", "selective": "Selective", "elective": "Elective", "required": "Required", "assigned": "Assigned", "not_assigned": "Not Assigned", "search_code_name": "Search Code / Name", "search_program": "Search Program", "credit_hours": "Credit Hours", "course_details": "Course Details", "session_flow": "Session Flow", "contact_hours": "Contact Hours", "duration_hours": "Duration / Contact", "duration": "Duration", "na": "NA"}, "frameworks": {"program": "Program/Year/{{Level}}/Course", "alignment": "Alignment", "optional": "Optional", "required": "Required", "impact": "Impact", "map": "Map", "K": "K", "S": "S", "V": "V", "Yr": "Yr", "validations": {"code": "Domain code is required", "name": "Domain name is required", "code_exists": "Domain code already exists", "name_exists": "Domain name already exists", "f_name": "Framework name already exists", "f_name_exists": "Framework short code already exists"}}, "contact_hour_per_credit_hour": "Contact hour per Credit hour", "duration_per_contact_hour": "Duration per Contact hour", "messages": {"create_program": "Program Created Successfully.", "create_pre_requisite": "Pre-Requisite Course Created Successfully.", "update_program": "Program Edited Successfully.", "update_pre_requisite": "Pre-Requisite Course Edited Successfully.", "delete_program": "Program Deleted Successfully.", "delete_pre_requisite": "Pre-Requisite Course Deleted Successfully.", "create_curriculum": "Curriculum Created Successfully.", "update_curriculum": "Curriculum Edited Successfully.", "curriculum_restored": "Curriculum Restored Successfully.", "curriculum_archived": "Curriculum Archived Successfully.", "curriculum_deleted": "Curriculum Deleted Successfully.", "content_select": "Content map type Selected Successfully.", "map_deselect": "Map Type Deselected Successfully.", "map_select": "Map Type Selected Successfully.", "restored": "Restored", "archived": "Archived", "successfully": "Successfully", "impact_mapping": "Impact Mapping", "content_mapping": "Content Mapping", "added": "Added", "edited": "Edited", "week_error": "Week is not provided in the S.No(s).", "start_week": "Please choose a start week", "contact_exceeded": "Contact hours exceeded for", "contact_achieved": "Contact hours are not achieved for", "empty_field": "Some of the Fields are Empty or Invalid in Session Flow", "add_one_session": "Please add at least one session flow", "session_zero_error": "Session Type credit hours should be greater than or equal to zero", "session_type_zero_error": "Session Type contact hours should be greater than zero", "duration_zero_error": "Session Type Duration / Contact should not be empty", "duration_type_zero_error": "Session Type Duration / Contact should be greater than or equal to zero", "delivery_type_zero": "Delivery Type duration should be greater than zero", "two_character_error": "name should have atleast two characters", "is_req": "is required", "content_added": "Content Mapping Added successfully", "content_updated": "Content Mapping Edited successfully", "content_deleted": "Content Mapping Deleted successfully", "impact_added": "Impact Mapping Added successfully", "impact_updated": "Impact Mapping Edited successfully", "impact_deleted": "Impact Mapping Deleted successfully", "map_type_seleced": "Map type selected successfully", "map_type_deselected": "Map type deselected successfully", "department_shared_successful": "Department Shared Successfully.", "department_un_shared_successful": "Department Removed Successfully."}, "modal": {"messages": {"template_mismatch": "Template <PERSON><PERSON><PERSON>", "template_content": "Imported xlsx columns are not matched. Check the sample template", "invalid_format": "Error: Invalid file format", "upload_error": "You have uploaded an invalid file type. Accepted file formats is .xlsx", "error_header": "This action will permanently remove the selected session type and related delivery types!"}}, "error_program": "An error occurred while fetching the program.", "click_hear": "Click here", "retry_or": "to retry or", "go_to_program": "to go to Programs page.", "invalid_step": "Invalid Step", "employee_id": "Employee ID", "academic_no": "Academic No", "academic_id": "Academic Id", "first_name": "First Name", "middle_name": "Middle Name (Optional)", "national_id": "National ID", "last_name": "Last Name (Optional)", "family_name": "Family Name (Optional)", "gender": "Gender", "registered": "Registered", "registration_pending": "Registration Pending", "inactive": "Inactive", "bulk_import": "Bulk Import", "single_entry": "Single Entry", "multiple_entry": "Multiple Entry", "download_import_template": "Download - Import Template", "management_staff_profile": "Staff Management / Staff Profile", "status_inactive": "Status Inactive", "edit_personal_details": "Edit Personal Details", "family": "Family", "national/residence": "National/Residence ID {{optional}}", "dob": "Date Of Birth", "nationality_optional": "Nationality (Optional)", "nationality": "Nationality", "nationality_id": "Nationality Id", "passport_number": "Passport Number (Optional)", "address_details": "Address Details", "building_no": "Building No, Street Name ", "district_name": "District Name", "city_name": "City Name", "zip_code": "Zip Code", "type_zipcode": "Type Your Zip Code", "floor_no": "Floor No", "contact_details": "Contact Details", "other_contact_details": "Other Contact Details", "office_extension": "Office Extension (Optional)", "office_room": "Office Room (Optional)", "mobile_number": "Mobile Number", "change_verify": "Change Verify", "personal": "Personal", "no_image": "No image", "enter_change": "Enter a reason to make this change", "type_reason": "Type Reason", "txt_mobile": " You seem to editing the mobile number. This action requires verification of the new mobile number by sending an password.", "mobile_verify": "Mobile number verification", "sent_mobile": "Sent to the mobile Number", "send_mail": " SEND MAIL", "staff_selected": "Staff Selected on this page", "used_id": "User ID", "user_management": {"request_sent": "Request Sent", "validation_pending": "Validation pending", "tabs": {"All": "All", "Imported": "Imported", "Invited": "Invited", "Submitted": "Submitted", "Mismatch": "Mismatch", "Invalid": "Invalid", "Valid": "<PERSON><PERSON>", "Expired": "Expired"}, "student_management_profile": "Student Management / Student Profile", "staff_biometric": "Staff Management / Staff Biometric", "staff_employment": "Staff Management / Staff Employment", "program_number": "Program Number", "building_street": "Building Street", "college_id": "College Id", "school_certificate": "School Certificate", "entrance_exam": "Entrance Exam", "enrollment_document": "Enrollment Document", "profile_information": " Profile Information", "file_document": "Fill the below information and attach the corresponding documents", "accepted_formats": "Accepted formats [ JPG, PNG ], Upload file max size [ 200KB ]", "entrance_exam_certificate": "Entrance Exam Certificate", "admission_documents": "Admission Documents", "college/residence": " College/Residence ID numbers", "national_address": "National Address", "batch": "{{<PERSON><PERSON>}}", "enter_reason": "Enter a reason to make this change", "verify_text": " You seem to editing the mobile number. This action requires verification of the new mobiile number by sending an password.", "profile": "Profile", "academic": "Academic", "employment": "Employment", "biometric": "Biometric", "academic_designation": "Academic Designation", "primary": "Primary", "select_program": "Select Program", "select_department": "Select Department", "select_division": "Select Division", "select_subject": "Select Subject", "add_subject_name": "Add Subject Name", "auxiliary": "Auxiliary", "add_more_subject": "Add More Subject", "add_auxiliary": "Add Auxiliary", "department_name": "Department Name", "capture_fingerprint": "Capture Fingerprint", "search_name_email_id": "Search by name, email, employee id...", "search_name_email_academic": "Search by name, email, academic no...", "staff": "Staff", "student": "Student", "completed": "Completed", "inactive": "Inactive", "all": "All", "imported": "Imported", "invited": "Invited", "submitted": "Submitted", "mismatch": "Mismatch", "invalid": "Invalid", "valid": "<PERSON><PERSON>", "expired": "Expired", "national_resident_id": "National ID / Resident ID", "saudi_postal_address": "{{Saudi}}Postal address", "program_field_required": "Program Field Is required", "department_field_required": "Department Field Is required", "subject_field_required": "Subject Field Is required", "Administrative Staff": "Administrative Staff", "Academic Allocation": "Academic Allocation", "Assistant Professor": "Assistant Professor", "Associate Professor": "Associate Professor", "Lab Assistant": "Lab Assistant", "Lab Associate": "Lab Associate", "Lab In-charge": "Lab In-charge", "Laboratory Technician": "Laboratory Technician", "Language Teacher": "Language Teacher", "Lecturer": "Lecturer", "Network & Security Engineer": "Network & Security Engineer", "Others": "Others", "Professor": "Professor", "Teaching Assistant": "Teaching Assistant", "Tutor": "Tutor", "On-site": "{{onsite}}", "Online": "Online", "sun": "SUN", "mon": "MON", "tue": "TUE", "wed": "WED", "thu": "THU", "fri": "FRI", "sat": "SAT", "Select": "Select", "Full Time": "Full Time", "Part Time": "Part Time", "assign_roles": "Assign Roles", "reports_to": "Reports to", "institute_role": "دور المعهد", "admin_role": "Admin Role", "default_role": "Default Role", "apply_department": "Apply Department", "Search": "Search", "role": "Role", "no_data_found": "No data found. Please select programs.", "not_allow_dynamic_role": "Not allow to edit dynamic roles...", "deleted_confirm": "Yes, delete it!", "no_delete_keep_it": "No, keep it", "delete_warning": "Once deleted, can't recover", "import_staff_list": "Import Staff List", "import_file": "Import File", "sample_import_staff": "sample-import-staff-lists.csv", "de_active": "De-Active", "add_new_staff": "Add New Staff", "fill_below_information": "Fill the Below Profile Information", "delete_staff": "Delete Staff", "confirm_delete_staff": "Confirm To Delete Staff", "importing_data": "Importing Data", "edit_staff": "Edit Staff", "edit_below_profile": "Edit the Below Profile Information", "choose_gender": "<PERSON>ose <PERSON>", "first_name_required": "First Name is Required", "space_not_allowed": "Space not allowed beginning & end", "minimum_3_char_required": "Minimum 3 character is required", "minimum_5_char_required": "Minimum 5 character is required", "minimum_1_char_required": "Minimum 1 character is required", "last_name_required": "Last Name is Required", "email_required": "Email is Required", "pls_enter_valid_mail": "Pls enter Valid Email Address", "special_character_not_allowed": "Special character are not allowed", "employeeId_required": "Employee ID is Required", "National_Residence_id_required": "National/Residence ID is Required", "single_entry_success": "Single Entry Successfully", "edited_success": "Edited Successfully", "exported_successfully": "Exported Successfully", "Student_In_Active_Successfully": "Student In-Active Successfully", "Profile_Edited_Successfully": "Profile Edited Successfully", "Student_Activated_SuccessFully": "Student Activated SuccessFully", "Staff_Activated_SuccessFully": "Staff Activated SuccessFully", "UG_certificate": "UG Certificate", "COVID_19_vaccination_details": "COVID-19 Vaccination Details", "Import_Student_List": "Import Student List", "sample_import_student": "sample-import-student-lists.csv", "add_new_student": "Add New Student", "Academic_no_Required": "Academic no is Required", "Batch_is_Required": "{{<PERSON><PERSON>}} is Required", "Program_is_Required": "Program is Required", "Personal_Details": "Personal Details", "Family_Name_Optional": "Family Name (Optional)", "confirm_delete_student": "Confirm To Delete Student", "delete_student": "Delete Student", "student_added_successfully": "Student added successfully", "student_updated_successfully": "Student Updated successfully", "edit_student": "Edit Student", "Staff_Deleted_Successfully": "Staff Deleted Successfully", "Student_Deleted_Successfully": "Student Deleted Successfully", "Staff_Detail_Added_Successfully": "Staff Personal Detail Added Successfully", "image_added": "Image added", "Staff_Document_Added_successfully": "Staff Document Added successfully", "Error_CSV_file": "Error in CSV file", "OTP_has_Been_Send": "OTP has Been Send", "Phone_Number_Updated_Successfully": "Phone Number Updated Successfully", "Staff_In_Active_Successfully": "Staff In-Active Successfully", "Choose_atLeast_one_checkbox": "Choose atLeast one checkbox", "Mail_Sent_Successfully": "Mail Sent Successfully", "success": "Success", "Subject_already_added": "Subject already added", "Academic_Details_Entered_Successfully": "Academic Details Entered Successfully", "Face_Image_Is_Confirm": "Face Image Is Confirm", "Thumb_finger_is_Confirm": "Thumb finger is Confirm", "Index_finger_is_Confirm": "Index finger is Confirm", "Employment_Added_Successfully": "Employment Details Added Successfully", "Image_updated_successfully": "Image updated successfully", "Student_Personal_Details_Added_Successfully": "Student Personal Details Added Successfully", "staff_Document_Added_successfully": "Student Document Added successfully", "biometrics": "Biometrics"}, "calender": {"sun": "S", "mon": "M", "tus": "T", "tue": "T", "wed": "W", "thu": "T", "fri": "F", "sat": "S", "Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "May": "May", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sep": "Sep", "Oct": "Oct", "Nov": "Nov", "Dec": "Dec"}, "staff_management": "Staff Management", "student_management": "Student management", "imonths": {"muharram": "<PERSON><PERSON><PERSON>", "safar": "<PERSON><PERSON>", "rabi_ul_awwal": "<PERSON><PERSON><PERSON><PERSON>", "rabi_ul_akhir": "<PERSON><PERSON><PERSON><PERSON>", "jumadal_ula": "Ju<PERSON><PERSON>", "jumadal_akhira": "<PERSON><PERSON><PERSON>", "rajab": "<PERSON><PERSON>", "sha_ban": "<PERSON><PERSON><PERSON><PERSON>", "ramadan": "<PERSON><PERSON>", "shawwal": "<PERSON><PERSON>", "dhul_qa_ada": "<PERSON><PERSON>", "dhul_hijja": "<PERSON><PERSON>"}, "iweeddays": {"ahad": "<PERSON><PERSON>", "ithnin": "<PERSON><PERSON><PERSON>", "thulatha": "<PERSON><PERSON><PERSON><PERSON>", "arbaa": "Arbaa", "khams": "K<PERSON><PERSON>", "jumuah": "<PERSON><PERSON><PERSON>", "jabt": "<PERSON>bt"}, "uploaded_documents": "Uploaded Documents", "upload": "Upload", "address": "Address", "admin": "Admin", "na": "N/A", "preview": "Preview", "edit_uploaded_file": "Edit uploaded file", "current_file": "Current file", "changed_file": "Changed file", "events": {"event_name": "Event Name", "event_details": "Event Detail", "create_event": "Create Event", "event_type": "Event Type", "staff": "Staff", "student": "Student", "event_for": "Event For", "gender_specific": "Gender Specific", "one_day_event": "One Day Event", "close": "Close", "submit": "Submit", "cancel": "Cancel", "start_date": "Start Date", "end_date": "End Date", "start_time": "Start Time", "end_time": "End Time", "onde_day_event": "One Day Event", "event_detail": "Event Name", "edit_event": "Edit Event", "event_types": {"holiday": "Holiday", "exam": "Exam", "training": "Training", "orientation": "Orientation", "general": "General"}, "V": "q", "venue": {"madurai": "Madurai", "chennai": "Chennai"}, "Yr": "year"}, "biometric": {"point1": "1. Place dry finger on the sensor", "point2": "2. <PERSON><PERSON><PERSON> keep the finger against the sensor ", "choose_option": "Choose Fingerprint Option", "capture": "Capture", "matcher": "Matcher", "thumb_finger": "Thumb Finger Capture", "index_finger": "Index Finger Capture", "bio_verify": "Biometric Verification", "bio_success": "Biometric Entry Successful", "bio_capture_txt": "Make sure you have good ambient light. Try to fit your face in circle", "retake": "RETAKE", "captureFace": "Capture Face", "biometric_registration": "Biometric Registration", "registration_can_done": "Registration can be done again to renew biometric data", "register_again": "Register again", "please_look_the_camera": "Please look into the camera and hold still", "good_ambient_light": "Make sure you have good ambient light", "face_in_circle": "Try to fit your face in circle", "captured_successfully !": "Your face has been captured successfully !"}, "employment": {"staff_employment": "Staff Employment Type", "schedule_type": "Schedule Type", "add_schedule": "Add Schedule", "staff_mode": "Staff Mode", "select_day": "Select Day(s)", "select_duration": "Select Time Duration", "employment_schedule": "Employment schedule", "define_date": "Define start and end date", "define_timings": "Define Timings", "add_term": "Add another term", "edit_schedule": "Edit Schedule", "by_day": "By Day", "by_date": "By Date", "remote_mode": "Remote Mode", "onsite_mode": "Onsite Mode", "days_and_time": "Days and Time", "mode": "Mode", "timing": "Timing", "common": "Common", "independant": "Independant", "remote": "Remote", "onsite": "Onsite", "remote_mode_and_onsite_mode": "Remote Mode and Onsite Mode"}, "sharing_subjects": "Sharing all the subjects", "date": {"am": "AM", "pm": "PM", "AM": "AM", "PM": "PM"}, "program_input": {"Choose_the_program": "Choose the {{program}}", "departmentName_type": "{{departmentName}} Type", "configuration": {"add": {"deliveryType": "Add New Delivery Type", "sessionType": "Add New Session Type"}, "edit": {"deliveryType": "Edit Delivery Type", "sessionType": "Edit Session Type"}}, "delete_title": {"Program": "Delete Program", "Pre-Requisite": "Delete Pre-Requisite Course", "Curriculum": "Delete Curriculum", "Framework": "Delete Framework", "impact": "Delete Impact", "content": "Delete Content", "college": "Delete College"}, "archive_program": "Archive Program", "restore_program": "Restore Program", "add_new_program": "Add New Program", "edit_program": "Edit Program", "framework_name": "Framework Name", "framework_sort_code": "Framework Short Code", "domain_name": "Domain Name", "domain_sort_code": "Domain Short Code", "sample_data_headers": {"Program_Name": "Program_Name", "Session_Name": "Session_Name", "Program_Code": "Program_Code", "Department_Name": "Department_Name", "Type": "Type", "Program_Type": "Program_Type", "Program_Level": "Program_Level", "Degree_Name": "Degree_Name", "No_of_Terms": "No_of_Terms", "Contact_Hours_per_Credit_Hour": "Contact_Hours_per_Credit_Hour", "Duration_Per_Contact_Hour": "Duration_Per_Contact_Hour", "Session_Duration_Minutes": "Session_Duration_Minutes", "Session_Symbol": "Session_Symbol", "Delivery_Name": "Delivery_Name", "Delivery_Duration_Minutes": "Delivery_Duration_Minutes", "Delivery_Symbol": "Delivery_Symbol", "Curriculum_Name": "Curriculum_Name", "Hour_As": "Hour_As", "Set_Value_As": "Set_Value_As", "Program_Duration_Start_At": "Program_Duration_Start_At", "Program_Duration_End_At": "Program_Duration_End_At", "Year": "Year", "Level": "Level", "Course_Type": "Course_Type", "Course_Recurring_at": "Course_Recurring_at", "Course_Code": "Course_Code", "Course_Name": "Course_Name", "Version_Name": "Version_Name", "Start_Week": "Start_Week", "Duration_in_weeks": "Duration_in_weeks", "Delivering_Subjects": "Delivering_Subjects", "Administrating_Subject_Dept_Prog": "Administrating_Subject_Dept_Prog", "Theory_Credit_Hours": "Theory_Credit_Hours", "Theory_Contact_hours_per_credit_hour": "Theory_Contact_hours_per_credit_hour", "Theory_duration_per_contact_Hour_in_minutes": "Theory_duration_per_contact_Hour_in_minutes", "Theory_duration_in_minutes": "Theory_duration_in_minutes", "Practical_Credit_Hours": "Practical_Credit_Hours", "Practical_Contact_hours_per_credit_hour": "Practical_Contact_hours_per_credit_hour", "Practical_duration_per_contact_Hour_in_minutes": "Practical_duration_per_contact_Hour_in_minutes", "Practical_duration_in_minutes": "Practical_duration_in_minutes", "Clinical_Credit_Hours": "Clinical_Credit_Hours", "Clinical_Contact_hours_per_credit_hour": "Clinical_Contact_hours_per_credit_hour", "Clinical_duration_per_contact_Hour_in_minutes": "Clinical_duration_per_contact_Hour_in_minutes", "Clinical_duration_in_minutes": "Clinical_duration_in_minutes", "Allow_to_edit_credit_hours_while_scheduling": "Allow_to_edit_credit_hours_while_scheduling", "Should_Achieve_target_credit_hours": "Should_Achieve_target_credit_hours", "Delivery_Type": "Delivery_Type", "Delivery_Number": "Delivery_Number", "Delivery_Topic": "Delivery_Topic", "Subject": "Subject", "Duration_Min": "Duration_Min", "Delivery_Weeks": "Delivery_Weeks", "slo_description": "slo_description"}, "map_plo_clo": "Map {{PLO}}-{{CLO}}", "shared_from": "Shared from", "course_types": {"Standard": "Standard", "Selective": "Selective", "Elective": "Elective", "Required": "Required"}, "choose_program": "Choose Program", "choose_curriculum": "Choose Curriculum", "choose_year": "Choose Year", "choose_level": "Choose {{Level}}", "participating_subjects": "Participating Subjects", "minutes": "Min", "administrating_department": "Administrating Department", "course_duration": "Course Duration", "choose_start_week": "choose start week", "share_this_course": "Share this Course", "ungroup": "UNGROUP", "all_subjects": "All <subject/>s", "all_subjects_f": "All Subjects", "credit_hours": "<PERSON>r.<PERSON>", "contact_hours": "Co.H", "duration": "Dur", "modals": {"delete_program": {"confirm": "Are you sure you want to delete the selected {{name}} ?"}, "restore": {"body_1": "Are you sure you want to restore the selected {{name}} ?", "body_2": "You can archive anytime later"}, "archive": {"body_1": "Are you sure you want to archive the selected {{name}} ?", "body_2": "You can restore anytime later"}, "delete_course_modal": {"title": "Delete Course", "body": "Are you sure you want to delete the selected course "}, "delete_impact_modal": {"title": "Delete Impact", "body": "Are you sure you want to delete the selected"}, "remove_assigned_course": {"title": "Remove Assigned Course", "body": "Are you sure you want to remove?"}, "delete_session_type": {"title": "Delete Session Type"}, "delete_delivery_type": {"title": "Delete Delivery Type"}}, "types": {"Session": "Session", "Delivery": "Delivery"}, "archive": "Archive", "edit": "edit", "delete": "Delete", "restore": "Rest<PERSON>", "okay": "okay", "s_archive": "archive", "s_restore": "restore", "new_program": "Create new program to share within the college", "shareToCollege": "to share within the college", "term_no": "Term Number", "term_name": "Term Name", "regular": "Regular", "interim": "Interim", "sessiontype": "Session Types & Delivery Types", "sessiontypeContent": "Create session type and delivery type, which is applicable for the <program/>", "department_config": "Configure <departmentName/> and <subjectName/>", "departmentContent": "Create necessary <departmentName/> and <subjectName/> for the <program/>", "add_session": "Add Session Types", "add_subject": "Add Subject", "the_selected": "the selected", "program_type": {"Program": "Program", "Pre-Requisite": "Pre-Requisite", "Curriculum": "Curriculum", "Framework": "Framework", "impact": "Impact", "content": "Content"}, "course_key_map": {"course_type": "Course Type", "course_recurring": "Course Recurring", "course_code": "Course Code", "course_name": "Course Name", "duration": "Duration", "participating": "Delivering Subjects", "administration": "Administrating Subject", "credit_hours": "Credit Hours", "allow_editing": "Allow to edit credit hours while scheduling", "achieve_target": "Achieve targeted credit hours", "course_occurring": "Course occurring"}, "response_strings": {"session_type_created_successfully": "Session Type Created Successfully", "session_type_edited_successfully": "Session Type Edited Successfully", "session_type_deleted_successfully": "Session Type Deleted Successfully", "delivery_type_created_successfully": "Delivery Type Created Successfully", "delivery_type_edited_successfully": "Delivery Type Edited Successfully", "delivery_type_deleted_successfully": "Delivery Type Deleted Successfully", "college_added_successfully": "College Added Successfully", "college_deleted_successfully": "College Deleted Successfully", "department_edited_successfully": "Department Edited Successfully", "department_created_successfully": "Department Created Successfully", "department_deleted_successfully": "Department Deleted Successfully", "subject_created_successfully": "Subject Created Successfully", "subject_edited_successfully": "Subject Edited Successfully", "subject_deleted_successfully": "Subject Deleted Successfully", "subject_shared_successfully": "Subject Shared Successfully", "subject_un_shared_successfully": "Subject Removed Successfully", "framework_added_successfully": "Framework Added Successfully", "framework_edited_successfully": "Framework Edited Successfully", "framework_deleted_successfully": "Framework Deleted Successfully", "framework_selected_successfully": "Framework Selected Successfully", "framework_deselected_successfully": "Framework De-selected Successfully", "domain_added_successfully": "Domain Added Successfully", "domain_edited_successfully": "Domain Edited Successfully", "domain_deleted_successfully": "Domain Deleted Successfully", "course_edited_successfully": "Course Edited Successfully", "course_deleted_successfully": "Course Deleted Successfully", "course_drafted_successfully": "Course Drafted Successfully", "course_assigned_successfully": "Course assigned Successfully", "assignedCourse_edited_successfully": "Assigned course Edited Successfully", "assignedCourse_removed_successfully": "Assigned course removed Successfully", "courses_edited_successfully": "Courses Edited Successfully", "courses_deleted_successfully": "Courses Deleted Successfully", "courses_drafted_successfully": "Courses Drafted Successfully", "course_created_successfully": "Course created Successfully", "program_imported_successfully": "Program Imported Successfully", "course_imported_successfully": "Course Imported Successfully", "session_imported_successfully": "Session Imported Successfully", "session_type_imported_successfully": "Session Type Imported Successfully", "delivery_type_imported_successfully": "Delivery Type Imported Successfully", "department_imported_successfully": "Department Imported Successfully", "curriculum_imported_successfully": "Curriculum Imported Successfully", "slo_imported_successfully": "{{SLO}} Imported Successfully", "criteria_for_scoring_descriptions": "Criteria for Scoring Descriptions", "questions": "Questions", "settings_saved_successfully": "Setting<PERSON> saved successfully", "plo_added_successfully": "{{PLO}} Added Successfully", "plo_edited_successfully": "{{PLO}} Edited Successfully", "plo_deleted_successfully": "{{PLO}} Deleted Successfully", "clo_added_successfully": "{{<PERSON>L<PERSON>}} Added Successfully", "clo_edited_successfully": "{{<PERSON>L<PERSON>}} Edited Successfully", "clo_deleted_successfully": "{{<PERSON>LO}} Deleted Successfully", "slo_added_successfully": "{{SLO}} Added Successfully", "slo_edited_successfully": "{{SLO}} Edited Successfully", "slo_deleted_successfully": "{{SLO}} Deleted Successfully", "mapped_successfully": "Mapped Successfully", "unmapped_successfully": "Unmapped Successfully", "sessionFlow": {"edite": "Session Flow Updated Successfully", "delete": "Session Flow Deleted Successfully", "drafte": "Session Flow Drafted Successfully", "adde": "Session Flow Added Successfully"}}, "error_strings": {"error_fetching_mapping_data": "An error occurred while fetching the mapping data", "error_fetching_programs": "An error occurred while fetching the programs", "error_fetching_course_details": "An error occurred while fetching course details", "error_fetching_session_flow_details": "An error occurred while fetching session flow details", "the_subjects": "The Subject(s) ", "are_duplicated": " are Duplicated", "already_exists": " already exists", "the_department": "The Department ", "course_duration_not_valid": "Course duration is not valid", "lower_limit_shouldbe_lesser": "Lower limit should be lesser than higher limit", "higher_limit_cannot_be_empty": "Higher limit cannot be empty", "lower_limit_cannot_be_empty": "Lower limit cannot be empty", "name_shouldnot_be_empty": "Name should not be empty", "sessionflow_details_not_found": "Session flow details not found", "curriculum_details_not_found": "Curriculum details not found", "course_details_not_found": "Course details not found", "no_courses_found": "No courses found", "end_week_should_not_be_greater_than": "End week should not be greater than", "no_session_flow": "No session flow. Please provide session flow in the course master list.", "some_of_the_fields_empty": "Some of the fields in share [S.No(s). (", "is_empty": ")] is empty", "no_session_topics_found": "no session topics found", "no_domains_found": "no domains found", "the_session_name": "The Session name", "the_delivery_name": "The Delivery name", "session_type_should_be_greater_than_zero": "At least one session type should have credit hours greater than zero", "no_delivery_type_selected": "There are no delivery types selected", "select_delivery_type": "Please select at least one delivery type for each session type", "no_data": "There is no data to save as draft", "choose_delivery_type": "Please choose a delivery type in the S.No(s).", "to_save_draft": "in order to save as draft", "session_symbol_only_alphabets": "Session Symbol should contain only alphabets", "delivery_symbol_only_alphabets": "Delivery Symbol should contain only alphabets", "delivery_symbol_present": "Delivery Symbol already exists", "session_duration/period_only_numbers": "Session Duration/ Period should contain only positive numbers", "contact_hours/period_only_numbers": "Contact hours/ Period should contain only positive numbers", "durationMax": "Duration in Weeks should not be greater than", "start_week_should_not_be_greater_than": "Start week should not be greater than", "durationNotValid": "Duration in Weeks is not valid"}, "labels": {"enter_topic": "Enter a topic", "enter_duration": "Enter duration"}, "active": "Active", "noDeliveryTypes": "No Delivery Types", "settings_error_message": "Please add settings and proceed"}, "role_management": {"roles": "Roles", "roles_list": {"Super Admin": "Super Admin", "Department chairman": "Department Chairman", "Program Admin": "Program Admin"}, "modules": "<PERSON><PERSON><PERSON>", "subModule": "Sub-module / page", "actions": "Actions", "pages": "Pages", "modals": {"Add": "Add Role", "Edit": "Edit Role", "role_name": "Role Name", "confirm_delete": "Confirm Delete", "activate": "Confirm Activate", "deactivate": "Confirm Deactivate"}, "role_events": {"activate": "Activate", "deactivate": "Deactivate"}, "strings": {"delete_role": "Are you sure you want to delete this role from entry?", "activate": "Are you sure you want to activate the permission for the assigned role?", "deactivate": "Are you sure you want to deactivate the permission for the assigned role?"}, "modules_list": {"Dashboard": "Dashboard", "Calendar": "Calendar", "Program Calendar": "Program Calendar", "Search_College": "Search College", "User Management": "User Management", "Student Grouping": "Student Grouping", "Infrastructure Management": "Infrastructure Management", "Roles and Permissions": "Roles and Permissions", "Leave Management": "Leave Management", "Program Input": "Program Input", "Schedule Management": "Schedule Management", "Reports and Analytics": "Reports and Analytics", "Institution Calendar": "Institution Calendar", "reviewer": "reviewer", "Staff Management": "Staff Management", "Student Management": "Student Management", "Onsite": "Onsite", "Remote": "Remote", "Leave Settings": "Leave Settings", "my_leaves": "", "student_register": " ", "report_absence": " ", "approver": "approver", "Course Scheduling": "Course Scheduling", "extra_curricular": " Extra Curricular", "assign_course_coordinator": "  ", "Staff Leave": "Staff Leave", "Student Leave": "Student Leave", "Student Register": "Student Register", "Report Absence": "Report Absence", "Course Details": "Course Details", "Programs": "Programs", "Colleges": "Colleges", "Extra Curricular and Break": "Extra Curricular and Break", "Assign Course Coordinator": " Assign Course Coordinator ", "Approve Leave": "Approve Leave", "Settings": "Settings", "Level List": "Level List", "Staff": "Staff", "Student": "Student", "Academic Student Details": "Academic Student Details", "Department Staff Details": "Department Staff Details", "Registration Pending": "Registration Pending", "Schedule": "Schedule", "classType": "{{classType}}", "Assessment Management": "Assessment Management", "Attainment Calculator": "Attainment Calculator", "Assessment Library": "Assessment Library", "Assessment Type": "Assessment Type", "Attainment Setting": "Attainment Setting", "Attainment Report": "Attainment Report", "Curriculum Monitoring": "Curriculum Monitoring", "Student Report": "Student Report", "Leave Authority": "Leave Authority", "Global Configuration": "Global Configuration", "Institution": "Institution", "Announcement Management": "Announcement Management", "Manage Announcement": "Manage Announcement", "Secondary Attendance": "Secondary Attendance", "Attendance": "Attendance", "Session Missed To Completed Status": "Session Missed To Completed Status", "Manage Course Advance Settings": "Manage Course Advance Settings", "Missed to Schedule": "Missed to Schedule", "User Management Policy": "User Management Policy", "Announcement": "Announcement", "All Announcement Log": "All Announcement Log", "Manual Attendance": "Manual Attendance", "Face Anamoly Purification": "Face Anamoly Purification", "Anomaly Purifying": "Anomaly Purifying", "Academic_Accountability_Management": "Academic Accountability Management", "Global Search": "Global Search", "Survey": "Survey", "All Survey": "All Survey", "Survey Management": "Survey Management", "Survey Template": "Survey Template", "Q360": "Q360", "Configuration": "Configuration", "Permission": "Permission", "Academic Year": "Academic Year", "Configure Template": "Configure Template", "Tag Report": "Tag Report", "User Module Permission": "User Module Permission"}, "tabs": {"View All": "View All", "Event details": "Event details", "Review": "Review", "Permission": "Permission", "Leave": "Leave", "On Duty": "On Duty", "Criteria Manipulation": "Criteria Manipulation", "Student Leave Entry": "Student Leave Entry", "Attendance Sheet": "Attendance Sheet", "Academic Overview": "Academic Overview", "Department Overview": "Department Overview", "Overview": "Overview", "Attendance Log": "Attendance Log", "Session Status": "Session Status", "Staff Details": "Staff Details", "Student Details": "Student Details", "Activity": "Activity", "Course": "Course", "Registered": "Registered", "Inactive": "Inactive", "Dashboard": "Dashboard", "Master": "Master", "Course Group": "Course Group", "Course Group Settings": "Course Group Settings", "Active Programs": "Active Programs", "Archived": "Archived", "Extra Curricular": "Extra Curricular", "Break": "Break", "Course List": "Course List", "TimeTable": "TimeTable", "Settings": "Settings", "Level List": "Level List", "Staff": "Staff", "Student": "Student", "Department Staff Details": "Department Staff Details", "Registration Pending": "Registration Pending", "Dashboard Master Group": "Dashboard Master Group", "Master Group": "Master Group", "Master Group Settings": "Master Group Settings", "Academic Student Details": "Academic Student Details", "Schedule": "Schedule", "Assessment Plan": "Assessment Plan", "Program / Course Assessments": "Program / Course Assessments", "Set Assessment Type": "Set Assessment Type", "Regulations": "Regulations", "Customize": "Customize", "Individual Student Reports": "Individual Student Reports", "Student Leave Management Reports": "Student Leave Management Reports", "Student LMS": "Student LMS", "Basic Details": "Basic Details", "Schedule Setting": "Schedule Setting", "Announcement User Permissions": "Announcement User Permissions", "Create Announcements": "Create Announcements", "Re-Register Request": "Re-Register Request", "Survey Bank": "Survey Bank", "Reports": "Reports"}, "sub_tabs": {"Permission": "Permission", "Time Groups": "Time Groups", "Buildings and Hospitals": "Buildings and Hospitals", "Room Details": "Room Details", "HR Contact": "HR Contact", "Staff Details": "Staff Details", "Student Details": "Student Details", "Leave Classification": "Leave Classification", "On Duty Classification": "On Duty Classification", "Leave Approval Process": "Leave Approval Process", "Report Absence": "Report Absence", "Warning and Absence Calculation": "Warning and Absence Calculation", "Course Schedule": "Course Schedule", "Course TimeTable": "Course TimeTable", "Manage Course": "Manage Course", "All": "All", "Imported": "Imported", "Invited": "Invited", "Submitted": "Submitted", "Mismatch": "Mismatch", "Invalid": "Invalid", "Valid": "<PERSON><PERSON>", "Expired": "Expired", "Evaluation Plan": "Evaluation Plan", "Attainment Level": "Attainment Level", "Assessment View": "Assessment View", "Student Report": "Student Report", "Criteria Management": "Criteria Management", "Student View": "Student View", "Warning Report": "Warning Report", "Leave Management": "Leave Management", "On Duty": "On Duty", "Leave": "Leave", "Survey bank settings": "Survey bank settings"}, "role_actions": {"Search": "Search", "Edit": "Edit", "Delete": "Delete", "Complete Review": "Complete Review", "Send Review Request": "Send Review Request", "View": "View", "Add": "Add", "Profile View": "Profile View", "Profile Edit": "Profile Edit", "Suspend": "Suspend", "ReApply": "ReApply", "Update": "Update", "Reset": "Reset", "Create": "Create", "Add Event": "Add Event", "Publish": "Publish", "Export": "Export", "View All": "View All", "Review": "Review", "Calendar List View": "Calendar List View", "Calendar Edit": "Calendar Edit", "Calendar Delete": "Calendar Delete", "Calendar Active and Inactive": "Calendar Active and Inactive", "Add Category": "Add Category", "Edit Category": "Edit Category", "Delete Category": "Delete Category", "Add Leave Type": "Add Leave Type", "Edit Leave Type": "Edit Leave Type", "Delete Leave Type": "Delete Leave Type", "Deactivate Leave Type": "Deactivate Leave Type", "Activate & Deactivate": "Activate & Deactivate", "Module Edit": "Module Edit", "PLO Bar Graph": "PLO Bar Graph", "PO Bar Graph": "PO Bar Graph", "Credit Hours": "Credit Hours", "Students Bar Graph": "Students Bar Graph", "Staffs Bar Graph": "Staffs Bar Graph", "Calendar Settings": "Calendar Settings", "Review Calendar": "Review Calendar", "Event List": "Event List", "Academic View": "Academic View", "Academic Edit": "Academic Edit", "Employment View": "Employment View", "Employment Edit": "Employment Edit", "Biometric View": "Biometric View", "Biometric Edit": "Biometric Edit", "Assign Role": "Assign Role", "Update Status": "Update Status", "Add Single": "Add Single", "Add Bulk": "Add Bulk", "Send Mail": "Send Mail", "Update Valid/Invalid": "Update Valid/Invalid", "Update Invalid": "Update Invalid", "Import": "Import", "Course View": "Course View", "Course Export": "Course Export", "Extra Allowed": "Extra Allowed", "Ungrouped": "Ungrouped", "Clone": "<PERSON><PERSON>", "Add Program": "Add Program", "Add Course": "Add Course", "Add Pre-requisite": "Add Pre-Requisite Course", "Config View": "Config View", "Archive": "Archive", "Restore": "Rest<PERSON>", "List View": "List View", "Enable/Disable": "Enable/Disable", "Cancel/Re-Assign": "Cancel/Re-Assign", "Merge": "<PERSON><PERSON>", "Session Default Settings Add": "Session Default Settings Add", "Session Default Settings Edit": "Session Default Settings Edit", "Session Default Settings Delete": "Session Default Set<PERSON>s Delete", "Session Default Settings View": "Session Default Settings View", "Manage Topic View": "Manage Topic View", "Manage Topic Add": "Manage Topic Add", "Manage Topic Edit": "Manage Topic Edit", "Manage Topic Delete": "Manage Topic Delete", "Advance Settings View": "Advance Settings View", "Advance Settings Edit": "Advance Settings Edit", "Group": "Group", "Settings": "Settings", "Filter": "Filter", "View Action": "View Action", "Assessment Hierarchy Settings": "Assessment Hierarchy Settings", "Set Assessment Type": "Set Assessment Type", "Add Student Manually": "Add Student Manually", "CLO View": "CLO View", "PLO View": "PLO View", "Class Attainment": "Class Attainment", "Student Attainment": "Student Attainment", "Manage Attainment": "Manage Attainment", "Add Type": "Add Type", "Assessment View": "Assessment View", "Assessment Enter Mark": "Assessment Enter Mark", "Import ( Mark / Student Template )": "Import ( Mark / Student Template )", "Add / Update": "Add / Update", "Plo Bar Graph": "Plo Bar Graph", "Course Attainment": "Course Attainment", "Date Select": "Date Select", "Time Select": "Time Select", "Academic Year": "Academic Year", "Filter Program": "Filter Program", "Filter Course": "Filter Course", "Curricular Delivery Export": "Curricular Delivery Export", "Curricular Delivery View": "Curricular Delivery View", "Curricular Delivery Filter": "Curricular Delivery Filter", "All Deliveries Export": "All Deliveries Export", "All Deliveries View": "All Deliveries View", "All Deliveries Filter": "All Deliveries Filter", "Completed": "Completed", "Not Started": "Not Started", "InProgress": "InProgress", "Upcoming": "Upcoming", "Missed": "Missed", "Cancelled": "Cancelled", "Merged": "<PERSON>rged", "Late Started": "Late Started", "Early Ended": "Early Ended", "Student View": "Student View", "Export All": "Export All", "Program Export": "Program Export", "Program View": "Program View", "On Duty Approval Levels and Roles Edit": "On Duty Approval Levels and Roles Edit", "On Duty Approval Levels and Roles View": "On Duty Approval Levels and Roles View", "On Duty Configuration Edit": "On Duty Configuration Edit", "On Duty Configuration View": "On Duty Configuration View", "General Configuration Edit": "General Configuration Edit", "General Configuration View": "General Configuration View", "Leave Policy Documents Edit": "Leave Policy Documents Edit", "Leave Policy Documents View": "Leave Policy Documents View", "Warnings & Notifications Edit": "Warnings & Notifications Edit", "Warnings & Notifications View": "Warnings & Notifications View", "Leave Classifications Edit": "Leave Classifications Edit", "Leave Classifications View": "Leave Classifications View", "General View": "General View", "General Edit": "General Edit", "Permission Approval Levels and Roles Edit": "Permission Approval Levels and Roles Edit", "Permission Approval Levels and Roles View": "Permission Approval Levels and Roles View", "Permission Configuration Edit": "Permission Configuration Edit", "Permission Configuration View": "Permission Configuration View", "Edit Advance Setting Missed To Complete": "Edit Advance Setting Missed To Complete", "Manage": "Manage", "Upload Document": "Upload Document", "Save as Draft": "Save as Draft", "Revoke": "Revoke", "Approve/Reject": "Approve/Reject", "Create Survey": "Create Survey", "Create Survey Template": "Create Survey Template", "Active / Inactive": "Active / Inactive", "Run Survey Template": "Run Survey Template", "view": "view"}, "response_messages": {"create_role": "Role Created Successfully", "delete_role": "Role Deleted Successfully", "assign_role": "Roles Assigned Successfully", "activate_role": "Role Activated Successfully", "deactivate_role": "Role Deactivated Successfully", "update_role": "Role Updated Successfully", "choose_actions_for_the_selected_role": "Choose actions for the selected role."}}, "infra_management": {"table_headers": {"_building_id": "Building", "floor": "Floor", "zone": "Zone", "room_no": "Room No", "name": "Name", "usage": "Usage", "timing": "Tim<PERSON>", "program": "Program", "department": "Department", "subject": "Subject", "delivery_type": "Del. type", "capacity": "Capacity", "reserved": "Reserved"}, "no_data": "", "no_records": "No Records Found", "buttons": {"add": "ADD", "settings": "SETTINGS"}, "search_placeholder": "Search by Room No/Name", "list": "List of Infrastructure", "s_no": "S.No", "gender": {"Male": "Male", "Female": "Female", "Male & Female": "Male & Female"}, "add_location_modal": {"building_type": {"Building_Type": "Building Type", "College": "College", "Hospital": "Hospital"}, "zones": "Zones", "floors": "Floors", "new_location": "New location", "select_from_existing_locations": "Select from existing locations"}, "modals": {"confirm_delete": "Confirm Delete", "floor": {"description": "Are you sure you want to delete this floor entry?"}, "zone": {"description": "Are you sure you want to delete this zone entry?"}, "time_slot": {"description": "Are you sure you want to delete this time entry?"}, "building": {"description": "Are you sure you want to delete this building & hospital entry?"}, "infrastructure": {"description": "Are you sure you want to delete this infrastructure?"}}, "infra_settings": {"infrastructure_settings": "Infrastructure Settings", "time_groups": {"time_groups_slots": "Time groups(slots)", "start_time": "Start time", "end_time": "End Time", "gender": "Gender"}, "buildings_hospitals": {"buildings_and_hospitals": "Buildings and Hospitals", "location": "Location", "building/hospital": "Building/hospital", "number_of_floors": "no. of floors", "number_of_zones": "no. of zones"}, "zone_name": "Zone name", "floor_name": "Floor name", "category": "Category", "level": "Level"}, "add_infra": {"select_building": "Select a building", "na": "NA", "select_usage": " Select Usage", "Enter floor name": "Enter floor name", "Enter zone name": "Enter zone name"}, "alerts": {"zone_assigned": {"title": "Alert: Zone assigned", "description": "There are one or more infrastructures assigned with this zone. Unassign this zone from the infrastructures in order to edit or delete."}, "invalid_zone": {"title": "Alert: invalid zone name", "description": "Zone name cannot be empty. Please provide a valid zone name."}, "zone_exists": {"title": "Alert: zone exists for this type", "description": "If you want to edit under the zones, unassign all the zones delete them first in order to delete the basic infrastruture from the system."}, "time_slot_assigned": {"title": "Alert: Time slot assigned", "description": "There are one or more infrastructures assigned with this time slot. Change the time slot or delete all the infrastructures in order to edit or delete the time slot from the system."}, "time_slot_exists": {"title": "Alert: Time slot exists", "description": "Time slot already exists. Please provide a different time slot"}, "invalid_time_slot": {"title": "Alert: Invalid time slot", "description": "Either start or end time slot is invalid. Please choose a valid start or end time slot.", "start_date_after_end_date": "The start time is after the end time slot. Please choose a valid start and end time slot.", "same_start_end_date": "The start and end time slot is same. Please choose a valid start and end time slot."}, "floor_assigned": {"title": "Alert: Floor assigned", "description": "There are one or more infrastructures assigned with this floor. Unassign this floor from the infrastructures in order to edit or delete."}, "invalid_floor_name": {"title": "Alert: invalid floor name", "description": "Floor name cannot be empty. Please provide a valid floor name."}, "floor_exists": {"title": "Alert: floor exists for this type", "description": "If you want to edit under the particular category under floor, unassign all the category under this type and delete them first in order to delete the basic infrastruture from the system"}, "building_assigned": {"title": "Error: Building / Hospital assigned", "description": "There are one or more infrastructures assigned with this building / hospital. Delete all the infrastructures in order to delete this building / hospital from the system."}, "validation_error": {"title": "Alert: Validation error"}}, "bread_crumb": {"Infrastructure Management - Onsite": "Infrastructure Management - Onsite", "Settings": "Settings", "Infrastructure Management - Remote": "Infrastructure Management - Remote", "Course Scheduling": "Course Scheduling", "Extra curricular & Break": "Extra curricular & Break", "Assign Course Coordinator": "Assign Course Coordinator"}, "response_strings": {"error_occured": "An error occurred while fetching buildings and hospitals.", "time_group_created": "Time group created successfully", "time_group_updated": "Time group updated successfully", "time_group_deleted": "Time group Deleted successfully", "infra_created": "infrastructure created successfully", "infra_updated": "Infrastructure updated successfully", "infra_deleted": "Infrastructure Deleted successfully"}, "validation_strings": {"room_no": "Room no should have minimum 3 characters.", "capacity": "Capacity should be between 1 to 150.", "reserved": "Reserved should be between 0 to 10.", "name_length": "Name should be greater than 3 characters."}, "remote": {"room": "Room", "term": "{{TERM}}", "number_of_rooms": "No of Rooms", "remote_room_link": "Remote Room Link", "add_room": "ADD ROOM", "is_required": "is required", "modal": {"delete": {"title": "Delete Room", "description": "Are you sure you want to delete the selected room"}}, "remote_rooms_list_table_headers": {"meetingTitle": "Meeting Title", "meetingUrl": "Meeting URL", "meetingId": "Meeting ID", "meetingUsername": "Meeting Username", "gender": "Gender", "remotePlatform": "Remote Platform", "associatedEmail": "Associated Email", "password": "Password", "passCode": "Passcode", "apiKey": "API KEY", "apiSecretKey": "API SECRET KEY", "app": "App"}, "error_msgs": {"no_room_created": "No Room Created", "invalid_meeting_url": "Invalid meeting url. Please provide a valid url.", "invalid_associated_email": "Invalid associated email. Please provide a valid email.", "meeting_title_already_exists": "Meeting title already exists", "an_error_occured_try_again": "An error occurred. Please try again.", "no_changes_made": "You have not made any changes"}, "response_strings": {"room_created": "Room created successfully", "room_updated": "Room updated successfully", "room_deleted": "Room deleted successfully"}}}, "settings": {"configuration": "Configuration", "basic_settings": "Basic Settings For Survey & Activity", "configure_basic_settings": "Configure Basic Settings For Survey And Activity", "configure": "configure", "manage": "manage", "continue_editing": "continue editing", "self_evaluation_survey": "Self Evaluation Survey", "session_experience_survey": "Session Experience Survey", "activity": "Activity", "rating_scale": "Rating Scale", "benchmark_value": "Benchmark Value", "advance_setting": "Advance Setting", "completed": "completed", "pending": "pending", "disabled": "disabled", "rating_scale_small": "rating scale", "benchmark_name": "benchmark name", "benchmark_short_name": "benchmark short name", "colors": "colors", "advance_settings": "Advance Settings", "written_feedback": "Get written Feedback From student", "allow_students": "Allow Students to rate At any time", "configuration_modal": {"survey_types": "Survey Types", "set_rating_scale": "Set Rating Scale", "set_benchmark_value": "Set Benchmark Value", "no_of_questions": "NO OF QUESTIONS", "user_permission": "User Permission", "allow_course_admin_desc": "Allow course admin to disable the survey types", "enable_disable_survey_types": "Enable / Disable the Survey Types", "rating_scale_for_survey": "Rating Scale for Survey", "configure_rating_scale_for_survey": "Configure Rating Scale For Survey", "session_experience_survey_desc": "Evaluation of students experience of the session, conducted after every session", "separate_rating_scale_values": "Separate rating scale values for each questions", "configure_benchmark": "Configure bench Mark For Survey & Activities", "value_format": "Value Format", "benchmark_values": "Benchmark Values for Survey & Activities", "benchmark_values_activity": "Benchmark Values for Activities"}, "alert": {"main_title": "Changing Any settings will", "reset_title": "Reset the Configuration", "existing_title": "affect the existing data’s"}, "advance_setting_modal": {"configure_advance_setting": "Configure Advance Setting For"}, "benchmark_setting": {"set_value": "Set Value", "set_color": "Set Color", "max": "Max", "add_another": "Add Another", "enter_name": "Enter Name", "enter_number": "Enter number", "enter_value": "Enter value", "enter_short_name": "Enter Short Name", "color_error_msg": "Color Should Be Unique For Each Bench Mark"}, "cancel": "CANCEL", "submit": "SUBMIT", "proceed": "Proceed", "save_as_draft": "Save as draft", "next": "NEXT", "back": "back", "direct_evaluation": "Direct Evaluation Of {{SLO}}’s By Students, Conducted After Every Session", "rating_scale_modal": {"scale_point_survey_value": "Scale Point survey value", "criteria_for_scoring": "Criteria for Scoring", "set_color": "Set Color", "below_questions": "Below Questions will be asked after each session", "config_rating_scale": "Config Rating Scale", "session_experience_survey_question": "Session Experience Survey Question", "QUESTION": "QUESTION", "enter_description": "Enter description", "color_error_msg": "Color Should Be Unique For Each Scale Points"}, "rating_title": "rating title", "criteria_for_scoring": "criteria for scoring", "response_msg": {"benchmark_settings": "Benchmark Settings", "saved": "Saved", "drafted": "Drafted", "successfully": "Successfully", "survey_types": "Survey Types", "set_rating_scale": "Set Rating Scale", "set_benchmark_value": "Set Benchmark Value", "self_evaluation_survey_advance_settings": "Self Evaluation Survey Advance Settings", "session_experience_survey_advance_settings": "Session Experience Survey Advance Settings", "rating_scale": "Rating Scale", "session_eExperience_survey": "Session Experience Survey", "self_evaluation_survey": "Self Evaluation Survey"}, "constants": {"common": "Common", "independent": "Independent", "points": "Points", "percentage": "Percentage"}, "set_rating_scale": "Set rating Scale", "set_value_for_all_survey_type": "Set Value for all Survey type", "create_question_for_session_experience_survey": "Create Question For Session Experience Survey"}, "program_calendar": {"date_range": "Date Range", "courses": "COURSES", "still_not_been_published": "still not been published", "calendar_settings": "CALENDAR SETTINGS", "academic_week": "Acad. week", "calendar_week": "Cal. week", "events_list": "EVENTS LIST", "set_calendar": "Set Calendar", "set_curriculum": "SET CURRICULUM", "set_new_curriculum": "Set a new curriculum", "curriculum_already_set": "Curriculum Already Set", "no_calendar": "No Calendar", "click": "Click", "define_dates_and_curriculum": "and define dates and curriculum", "program_calendar_settings": "Program Calendar Settings", "academic_year": "Academic Year", "set_curriculum_versions": "Set Curriculum Versions", "interim_term": "Interim {{Term}}", "regular_term": "Regular {{Term}}", "level_wise_view": "{{Level}}-wise view", "year_wise_view": "Year-wise view", "select": "Select", "export": "EXPORT", "export_as_pdf": "Export As PDF", "set_level_in_calendar_settings": "Set Level In Calendar Settings", "create_institution_calendar": "Create Institution Calendar", "primary_calendar": "Primary Calendar", "create": "CREATE", "events": "Events", "click_to_create_event": "Click + icon to create an event ", "edit_course": "Edit Course", "course": "Course", "no_weeks": "No events in this week duration. please add new events", "select_a_level": "Select a {{level}} for the course", "complete_level": "Complete {{Level}}", "start_date_read_only": "Start date(read only)", "end_date_read_only": "End date(read only)", "select_new_dates": "Select new dates", "course_duration": "Course Duration", "by_academic_weeks": "By Academic weeks", "from_week_number": "From week no.", "week": "Week", "by_dates": "By Dates", "model_type": "Model type", "select_course": "Select Course", "no_courses_in": "No courses in", "box_color_in_course_map": "Box color in course map", "select_start_week": "Select start week", "select_end_week": "Select end week", "to": "To"}, "dashboard_view": {"no_of_year": "No. of Years", "active_curriculum": "Active Curriculum", "configure_settings": "Configure <PERSON><PERSON>s", "configure": "Configure", "monitoring": "Monitoring", "course_name": "COURSE NAME / ID", "year_level": " Year / {{Level}}", "leave_mgmt": "Leave Mgmt", "depart_subjcet": "Department / Subject", "year": "Year", "term": "{{Term}}", "curriculum": "Curriculum", "final_warning": "Final Warning", "denial": "Denial", "session_complete": "No of Session Completed", "total_course": "Total Courses", "total_level": "Total {{Level}}", "total_subject": "Total Subject", "total_year": "Total Year", "total_department": "Total Department", "total_staff": "Total Staff", "academic_year": "Academic Year / {{Level}}", "my_session": "My Session", "search_program_name_code": "Search Program Name, Code", "subject": "Subject", "delivery_type": "Delivery Type", "time": "Time", "student_groups": "Student Groups", "scheduling_in_progress": "Scheduling In-Progress", "course_completed": "Course Completed", "course_not_started": "Course Not Started", "shared_from": "Shared From", "admin_course": "Admin Course", "participatory_course": "Participatory Course", "level": "Level", "department": "Department", "staff": "Staff", "program": "Program", "show_more": "Show More", "permission": "Permission", "am": "am", "pm": "pm", "st": "st", "nd": "nd", "rd": "rd", "th": "th"}, "loading": "Loading...", "apply": "APPLY", "no_data": "No data found...", "fetching_data": "Fetching data. Please wait.....", "select_a_rotation": "Select a rotation", "select_rotation": "Select rotation", "group_this_course_with_other": "Group this course with other existing courses in this level", "select_one_or_more_courses": "Select one or more courses", "regular": "Regular", "interim": "Interim", "event_title": "Event Title", "event_type": "Event Type", "add_events_from_institution_calendar": "Add events from institute calender", "total_no_of_days": "Total number of days", "total_no_of_day": "Total number of day", "select_date_to_sync": "select a date in the institute calender to sync.", "add_event_manually": "Add event manually", "list_of_events_that_occur": "List of events that occur during the course duration", "amp": "&", "course_assigned_already": "Course Assigned Already!", "no_options": "No options", "import_program_download": {"Program_Name": "اسم البرنامج"}, "add_colleges": {"pod_principles_of_diseases": " POD 203 - Principles of Diseases", "standard_Curriculum_requirement": "Standard • Curriculum Requirement", "Introduction_to_the_topic": "Introduction to the topic", "about_university": "About University", "about_college": "About College", "vision": "Vision", "mission": "Mission", "add_portfolio": "Add Portfolio", "city": "City", "choose_city": "Choose city", "state": "State", "Choose_State": "Choose State", "country": "Country", "Choose_Country": "Choose Country", "zip_code": "Zipcode", "choose_institution": "Choose your type of Institution", "setup": "We can help you to setup", "choose_organisation": "Choose this if you want to manage multiple Institutions running under your organisation", "group_institutions": "University / Group of institutions", "individual_institution": "Individual Institution", "choose_this_institution": "Choose this if you want to manage a Individual Institution", "login": "login", "add_university": "Add New University / Group of Institution", "save": "save", "add_photo": "Add Photo", "upload_logo": "Upload your Logo", "file_format": "The file should be less than 1 MB and format can be .jpeg, .PNG", "university_name": "University Name", "university_code": "University Code", "accreditation": "Accreditation", "Accreditation_Details": "Accreditation Details", "Accreditation_Agency_name": "Accreditation Agency name", "Accreditation_Type": "Accreditation Type", "Accreditation_Number": "Accreditation Number", "Accreditation_Value": "Accreditation Value", "Validity_Start_On": "Validity Start On", "Validity_Ends_On": "Validity Ends On", "Others": "Others", "Small_Discription": "Small Discription...", "Add_Accreditation_Type": "{{type}} Accreditation Type", "Select_type": "Select type", "Delete_Accreditation_Details": "{{type}} Accreditation Details", "Accreditation_Permanatly_Remove": "This action will permanatly remove the selected {{name}}", "Accreditation_Permanatly_Remove2": "Are you sure you want to delete session type ‘{{name}}’ ?", "No_colleges_under_university": "No of colleges under university", "accreditationAgencyName_required": "Accreditation Agency Name is required", "accreditationAgencyName_max": "Accreditation Agency Name Max length 100", "accreditationType_required": "Accreditation Type is required", "accreditationNumber_required": "Accreditation Number is required", "accreditationValue_required": "Accreditation Value is required", "validityStart_required": "Validity Start is required", "validityEnd_required": "Validity End is required", "others_required": "Others is required", "collage_under_university": "No Collage Under University", "address_details": "Address Details", "address_line1": "Address Line 1", "district": "District", "complete_address": "Type Your Complete Address", "state_region": "State / Region", "type_district": "Type Your District", "no": "No", "yes_cancel": "Yes, <PERSON>cel", "title": "Name of the Title", "add_description": "Add Description", "change_photo": "CHANGE PHOTO", "no_of_colleges": "No. of Colleges:", "No_Colleges_Added_Yet": "No Colleges Added Yet", "all_colleges": "All Colleges", "add_new_college": "Add New College", "edit_college_details": "Edit College Details", "college_name": "College Name", "college_code": "College Code", "assign_admin": "Assign <PERSON><PERSON>", "restore_college": "Restore College", "delete_college": "Delete College", "are_you_sure_want_restore": "Are you sure you want to restore the selected college ?", "are_you_sure_want_delete": "Are you sure you want to delete the selected college ?", "archive_college": "Archive College", "archive_selected_college": "Are you sure you want to archive the selected college ? You can restore anytime later", "medicine_program": "Medicine Program", "overview": "Overview", "learning_outcomes": "Learning Outcomes", "configure_s": "Configure", "pod_203": "POD 203", "goals": "Goals", "objectives": "Objectives", "cannot_archive": "Cannot Archive", "currently_active": "As this college is currently running a active institution calender, you can not Archive this college", "hours_as": "Hours as", "fixed_value": "Fixed Value", "range_of_values": "Range of Values", "theory": "Theory", "practical": "Practical", "clinical": "Clinical", "all_curriculum": "All Curriculum", "version": "Version", "nqf": "National Quality Framework", "credit_hours_summary": "Credit Hours Summary", "all_course": "All <course/>", "standard_courses": "Standard courses", "selective_courses": "Selective courses", "configuration_incomplete": "Configuration Incomplete !", "assign_course_master": "Assign <courseLabel/> From Master list", "total_levels": "Total No. of Levels", "certificate_optional": "Certificate ( Optional )", "add_certificate": "Add Certificate", "assign_course_levels": "Assign course to <levelLabel/>", "create_course_master_list": "Create course in master list to assign course to <levelLabel/>", "curriculum_program": "Create New Curriculum for the <program/>", "master_list": "Go to master list", "add_courses_assign": "Add Courses and Assign to Levels", "level_from_master_list": "You Can Assign a Course to any Level from Master List", "curriculum_configured": "Before adding course to master list, Curriculum need to be configured", "diploma": "Diploma", "delete_certificate": "Delete Certificate", "certificate_name": "Certificate Name", "curriculum_details": "Curriculum Details", "departments_subjects": "Departments & Subjects", "levels_certification": "<levelLabel /> & Certification", "configure_your_program": "Configure your <programName/> <departmentName/> and <subjectName/> and assign to staffs", "configure_session": "Configure the Session types and Delivery types applicable for the <programName/>", "configure_curriculum": "Configure the curriculum details", "configure_course": "Configure the <course/> details", "complete_configuration": "Follow the below steps to complete configuration", "UniversityName_Required": "University Name is required", "UniversityName_MinLength": "University Name length must be at least 3 character", "UniversityName_AlphaNum": "University Name must be alpha numeric", "InstitutionName_Required": "Institution Name is required", "InstitutionName_MinLength": "Institution Name length must be at least 3 character", "InstitutionName_AlphaNum": "Institution Name must be alpha numeric", "CollegeName_Required": "College Name is required", "CollegeName_MinLength": "College Name length must be at least 3 character", "CollegeName_AlphaNum": "College Name must be alpha numeric", "UniversityCode_Required": "University is required", "UniversityCode_MinLength": "University code length must be at least 3 character", "InstitutionCode_Required": "Institution code is required", "InstitutionCode_MinLength": "Institution code length must be at least 3 character", "CollegeCode_Required": "College code is required", "CollegeCode_MinLength": "College code length must be at least 3 character", "Accreditation_Required": "Accreditation is required", "Accreditation_MinLength": "Accreditation length must be at least 3 character", "NoOfCollege_Required": "No of College is required", "NoOfCollege_Number": "No of College must be number", "Address_Required": "Address is required", "Country_Required": "Country is required", "District_Required": "District is required", "State_Required": "State is required", "City_Required": "City is required", "ZipCode_Required": "ZipCode is required", "ZipCode_MinLength": "ZipCode length must be atleast 4 characters", "Logo_Required": "Logo is required", "Add_Description": "Add Description", "about_media_format": "JPG, JPEG, PNG, MP3, MP4 format only allowed", "about_media_size": "Upload File less than 250mb", "Upload_Media": "Upload Media", "Accepted_formats": "Accepted formats", "aboutUniversity": "Delete Description", "portfolio": "Delete Portfolio", "desc": {"aboutUniversity": "Are you sure you want to delete the selected description ?", "portfolio": "Are you sure you want to delete the selected portfolio ?", "cancel_confirmation": "Are you sure you want to cancel?", "cancel_alert": "The changes will not be saved if you choose to cancel", "addGoals": "Are you sure you want to delete the selected description ?", "addObjectives": "Are you sure you want to delete the selected description ?"}, "addGoals": "Delete Goals", "addObjectives": "Delete Objectives", "UnBundling": "UnBundling"}, "global_configuration": {"year": {"year1": "1 st Year", "year2": "2 st Year", "year3": "3 st Year", "year4": "4 st Year", "year5": "5 st Year", "year6": "6 st Year", "year7": "7 st Year"}, "phase": {"phase1": "Phase 1", "phase2": "Phase 2", "phase3": "Phase 3", "phase4": "Phase 4", "phase5": "Phase 5", "phase6": "Phase 6", "phase7": "Phase 7"}, "Phase_name": "Phase Name", "Phases": "Phases", "level_per_year": "{{level}} per {{year}}", "userName": "User Name", "enter_userName": "Enter Your User Name", "userName_info": "User Name Can Be Duplicate As From Email", "enter_email": "Enter Your From Email", "enter_to_email": "Enter To Email", "to_email": "To Email", "enter_password": "Enter Your Password", "enter_smtp_client": "Enter your SMTP Client", "From_Email": "From Email", "enter_port_number": "Enter Your Port Number", "Mandatory_Documents": "Mandatory Documents", "Allow_multiple_documents": "Allow multiple documents", "Document_Size": "Document Size", "basic_details": "Basic Details", "staff_user_management": "Staff User Management", "student_user_management": "Student User Management", "program_input": "Program Input", "learning_outcome_management": "Learning Outcome Management", "independentCourse": "Independent Course Input", "time_zone": "Time Zone", "select_languages": "Select Languages", "choose_languages": "Choose Applicable languages for the University", "working_days": "Working Days", "breaks": "Breaks", "events": "Events", "gender_segregation": "Gender Segregation", "set_time_zone": "Set Time Zone", "working_hours": "Working Hours", "set_independent": "Set Independent working hours", "email_configuration": "Email id Configuration", "email_configuration_pending": "Email id configuration is Pending", "display_name": "Display Name", "smtp_client": "SMTP Client", "re_enter_password": "Re - Enter Password", "port_number": "Port Number", "send_test_email": "Send Test Email", "full_time": "Full Time", "allow_mixed_vaccine": "Allow mixed vaccine", "add_new_vaccine_category": "Add New Vaccine Category", "vaccination_configuration": "Vaccination Configuration", "vaccine_number": "Vaccine Number", "vaccination_name": "Vaccination Name", "vaccination_type": "Vaccination Type", "antigen_name": "Antigen Name", "company_name": "Company Name", "brand_name": "Brand Name", "total_no_of_dosage": "Total No of Dosage", "doses": "Do<PERSON>", "boosters": "Boosters", "deadline": "Deadline", "add_booster_dosage": "Add <PERSON><PERSON>er <PERSON>", "total_dosage": "Total Dosage", "booster_dose": "<PERSON><PERSON><PERSON>", "add_new_vaccination": "Add New Vaccination", "add_new_document": "Add New Document", "add_new_category": "Add New Category", "select_time": "Select Time", "id_proof": "ID Proof", "educational_details": "Educational Details", "address_details": "Address Details", "employment_details": "Employment details", "medical_details": "Medical Details", "notification_mail": "Notification Mail", "mandatory_document": "Mandatory Document", "mandatory_document_notification_mail": "Mandatory Document Notification Mail", "recurring": "Recurring", "recur_every": "Recur every", "day_s": "Day(s)", "total_no_of_booster": "Total No of Booster", "daily": "Daily", "weekly": "Weekly", "custom": "Custom", "Profile_moved_invalid": "Profile moved to invalid", "vaccination_qr_code": "Vaccination - QR code", "vaccination_certificate": "Vaccination certificate", "health_passport": "Health Passport", "appointment_order": "Appointment Order", "employee_id": "Employee ID", "postal_address": "Postal Address", "degree_certificate": "Degree Certificate", "passport": "Passport", "resident_national_id": "Resident / National id", "set_mandatory": "Set <PERSON>tory", "choose_documents": " Choose Documents", "max_size_for_doc": "Max size for Doc", "independant": "Independant", "department_subject": "Department & Subject", "days": {"days": "Days", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "from": "From", "to": "To", "time": "Time", "add_break": "Add Breaks", "break_name": "Break Name", "occurence_days": "Occurence Days", "days_short": {"sunday": "Sun", "monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat"}, "add_new_break": "Add New Break", "edit_break": "Edit Break", "delete_break": "Delete Break", "add_event": "Add Event", "event_name": "Event Name", "leave": "Leave", "edit_event": "Edit Event", "delete_event": "Delete Event", "group_separately": "Group Male and Female Students separately", "no_gender_segregation": "No Gender Segregation", "staff_type": "Staff Type", "create_new": "Create New", "edit_staff_type": "Edit Staff type", "department_and_subject": "Department and Subject", "label_configuration": "Label Configuration", "curriculum_naming": "Curriculum Naming", "credit_hours": "Credit Hours", "program_Duration_Format": "{{program}} Duration Format", "credit_system": "Credit System", "rename": "<PERSON><PERSON>", "reset": "Reset", "label_name": "Label Name", "add_media": "Add Media", "program_type": "Program Type", "edit_program_type": "Edit Program Type", "add_program_type": "Add Program Type", "languages": " Languages", "workingDays": " Working Days", "groupMaleAndFemale": "Group Male and Female Students separately", "defaultLanguage": "Default Language", "add_new_event": "Add New Event", "break_name_alpha_numeric": "Break Name must be alpha numeric", "brkEventExisted": "Break already existed at the same time", "event_name_alpha_numeric": "Event Name must be alpha numeric", "labels": "Labels", "add_new_type": "Add New Type", "create_type": "Create Type", "short_code": "Short Code", "program_type_length": "Program Type length must be at least 2 character", "program_type_letter": "Program Type allow alphabets only", "short_code_length": "Short Code length must be at least 2 character", "short_code_letter": "Short Code allow alphabets only", "standard": "Standard Credit System", "dynamic": "Dynamic Credit System", "year_format": "Year Format", "Phase_format": "{{Phase}} Format", "Without_Level": "Without Level", "Without_Level_enabled": "If this is enabled Level will not be Created", "contact": "Contact Hour System", "mode": "Mode", "manual_naming": "Manual Naming", "label": "Label", "label_field_configuration": "Label / Field Configuration", "reset_all": "Reset All", "basic_details_info": "Basic Details will be needed to import a staff", "profile_details": "Profile Details", "vaccination_details": "Vaccination Details", "default_country_code": "Default country code", "allow_to_change_country_code": "Allow to change country code", "online_verification_desc": "Biometric verification should be done through online as mandatory process", "offline_verification_desc": "Biometric verification can be done through only offline", "both_verification_desc": "Biometric verification can be done through online or offline", "privacy_settings": "Privacy Settings", "blur_candidate_photos": "Blur Candidate Photos other than verification", "candidate_photo_blurred": "Candidate Photo blurred", "candidate_photo_unblurred": "Candidate Photo unblurred", "action_response_messages": {"saved_successfully": "Saved Successfully", "updated_successfully": "Updated Successfully", "deleted_successfully": "Deleted Successfully", "an_error_occured_try_again": "An error occurred. Please try again.", "reset_successfully": "Reset Successfully"}, "delete_confirm_modal": {"this_action_will_delete_break": "This action will permanently remove the selected break from the basic details", "this_action_will_delete_email": "This action will permanently remove the selected email from the basic details", "this_action_will_delete_event": "This action will permanently remove the selected event from the basic details", "this_action_will_delete_program_type": "This action will permanently remove the selected Program type from the Program Input"}, "configuration_info": {"headerBasicDetail": "Basic Detail", "descriptionBasicDetail": "If you disable the institution calendar users can set the start and end date for all program on current date started.", "headerDisciplinaryRemarks": "Disciplinary Remarks Configuration", "descriptionDisciplinaryRemarks": "Define and manage various types of misbehavior efficiently", "headerAttendanceConfig": "Attendance Configuration System", "descriptionAttendanceConfig": "Involves customizable settings to manage attendance tracking, allowing institutions to tailor attendance parameters and streamline record-keeping processes efficiently.", "headerSessionStatusManager": "Institutional Session Status Manager: Empowering <PERSON><PERSON> for Seamless Student", "descriptionSessionStatusManager": " Empower admin control over student status management. Configure global settings for automatic session status adjustments.", "headerLeaderBoard": "Leader Board", "descriptionLeaderBoard": "The Leader Board modules serve to recognize outstanding staff members and monitor suspicious events in curricular delivery.", "headerAnomalyBoard": "Anomaly Board", "descriptionAnomalyBoard": "The overall manual attendance given to the students by the staff for all the course.", "headerSettings": "Settings", "descriptionSettings": "Configure global settings for the system.", "headerSurvey": "Tag Master", "descriptionSurvey": "Your go-to solution for seamless course organization, simplifying tagging and streamlining course management for admins."}}, "course_master": {"add_content": "Add <course/> and Assign to <level/>", "sub_content": "You Can Assign a <course/> to any <level/> from Master List", "add_new_course": "add new <course/>", "basic_details": "Basic Details", "administrating": "Administrating <subject/> / <department/> / <program/>", "administratingLabel": "Administrating {{subject}} / {{department}} / {{program}}", "level_config": "Levels Configuration", "course_info": "<course/> Info", "courseEditor": "<course/> Editor", "color": "Color", "sub_theme": "Sub theme", "level_status": "<level/> Status", "save_draft": "Save as draft", "courseRecurring": "<course/> <name/> <level/>", "durationWeek": "Duration in weeks", "sessionDelivery": "Session and Delivery type Configuration", "selectCourse": "Select <course/> Occurence & Duration", "delivery_duration": "Delivery Duration", "interactiveLecture": "Interactive Lecture", "lecture": "Lecture", "selected_delivery_type": "Selected Delivery Types", "label_name": "اسم الطابع", "advanced_setting": "Advance Settings ", "optional": "( Optional )", "prerequisite": "Prerequisite {{course}}", "corequisite": "Corequisite {{course}}", "prerequisiteComponent": "Prerequisite <course/>", "corequisiteComponent": "Corequisite <course/>", "portfolio": "Portfolio", "add_media": "Add Media", "program_type": "Program Type", "edit_program_type": "Edit Program Type", "add_program_type": "Add Program Type", "languages": " Languages", "workingDays": " Working Days", "groupMaleAndFemale": "Group Male and Female Students separately", "defaultLanguage": "Default Language", "add_new_event": "Add New Event", "action_response_messages": {"timeZone_updated_successfully": "Time Zone Updated Successfully", "languages_updated_successfully": "Languages Updated Successfully", "workingDays_updated_successfully": "Working Days Updated Successfully", "session_updated_successfully": "Session Updated Successfully", "break_updated_successfully": "Break Updated Successfully", "event_updated_successfully": "Event Updated Successfully", "genderSegregation_updated_successfully": "Gender Segregation Updated Successfully", "event_added_successfully": "Event Added Successfully", "break_added_successfully": "Break Added Successfully", "an_error_occured_try_again": "An error occurred. Please try again."}, "course_editor": "Course Editor (Optional)", "themeUsed": "{{theme}} Already Used", "subThemeUsed": "{{subTheme}} Already Used"}, "configure_levels": {"year": "Year", "v1": "V1.0", "dentistry": "Dentistry", "Lorem_ipsum": " Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.  Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore   eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.", "anatomy_dep_prog": "Anatomy / Dep / Prog", "selectives": "No. of <selective/> to be enrolled for this <level/>", "fund_2022": "FUND 222 - Fundamentals of Human Body", "fund_2022_only": "FUND 222", "fund_of_human": "Fundamentals of Human b...", "selective_curriculam": "Selective • Curriculum Requirement • Credit Hours: 3 (1 + 1 + 1 )", "coures_info": "Coures Info", "coures_editor": "Coures Editor", "levelstatus": "Levels Status", "add_new_course_s": "add new course", "course_type": "Course Type", "levels_configuration": "Levels Configuration", "assigned_in": "Assigned In", "info": "Info", "assigned_to": "Assigned to", "level": "Level", "add_program_type": "Add Program Type", "basic_detail": "Basic Details", "advance_setting": "Advance Settings ", "session_flow": "Session flow", "delivery_no": "Delivery Number", "mode": "Mode", "theme": "Theme", "week": "Week", "knowledge": "Knowledge", "color": "Color", "sub_theme": "Sub theme", "course_duration": "Course Duration", "share_this_course": "Share this course Optional", "participating_subjects": "Participating <subject/>", "anatomy_physiology": "Anatomy, Physiology", "course_editor": "Course Editor", "dr_Mohamed": "<PERSON>", "sessionType_deliveryTypes": "Session Type / Delivery Types", "trch_mixed": "T Cr.H - 3 • Co.H/Cr.H - 3 • Dur - Mixed", "il60_dur": "IL Dur - 60 Min", "level123": "Level 1, Level 2, Level 3", "allow_edit": " Allow to edit credit hours while scheduling?", "allow_credit_hours": " Allow Editing Should achieve targetted credit hours", "item_two": "Item Two", "item_three": "Item Three", "item_four": "Item Four", "linked_sessions": "Linked Sessions", "l1": "L1", "study_plan": "Study Plan", "clear_filter": "Clear Filter", "themes_added": "Themes added", "link_sessions": "<PERSON>", "session_linked": "Session Linked", "assigned": "Assigned", "pending": "Pending", "coures_name": "Coures Name", "manage_versions": "Manage Versions", "alert": "<PERSON><PERSON>", "change_version": "Changes will be saved as Version", "save_version": "Do you want to make Version", "keep_version": "Keep Version", "active_version": "Active Version 2.0", "selective_config": "<selective/> <course/> Configuration", "enrolled": "No.of <selective/> <course/> to be enrolled for this <level/>"}, "Link_session": {"add_theme": "Add Theme", "link_session": "Link Session", "show_unlinked_session": "Show unlinked session", "session_topic": "Session topic", "appropriate_theme_for_topic": "Appropriate Theme for Topic", "linked_sessions": "Linked Sessions", "session_linked": "Session Linked", "edit_linked_sessions": "Edit Linked Sessions", "delete_link_session": "This action will permanatly remove the selected link session and its configurations from the application!"}, "saved_successfully": "Saved successfully", "deleted_successfully": "Deleted successfully", "an_error_occured_try_again": "An error occurred. Please try again.", "Mins": "<PERSON>s", "curriculum_delete": "This action will permanatly remove the selected Curriculum and its configurations from the application!", "curriculum_delete_desc": "Are you sure you want to delete the selected '{{name}}' ?", "un_bundle_course": "Un bundling of <course/> in a <programType/> as per the need of the University or College", "delete_certificate_desc": "This action will permanently delete the selected certificate from the application!", "delete_certificate": "Delete Certificate", "delete_certificate_sub_desc": "Are you sure you want to delete certificate", "curriculum_validation": {"curriculum_name": "Curriculum Name is required", "start_year": "{{program}} Start {{year}} is required", "end_year": "{{program}} End {{year}} is required", "credit_hours": "Credit Hours is required", "certificate_name": "Certificate Name is required", "certificate_minlength": "Certificate Name length must be at least 2 character", "curriculum_minlength": "Curriculum Name length must be at least 2 character", "year_error": "End {{year}} should be equal and greater than start {{year}}.", "uniqueLevelName": "{{level}} Name must be unique", "levelRequired": "{{level}} is required", "levelNameRequired": "{{level}} Name is required"}, "course_validation": {"course_type": "{{course}} Type is required", "course_code": "{{course}} Code is required", "course_code_chars": "{{course}} Code characters should be between 3 to 50", "course_name": "{{course}} Name is required", "course_name_chars": "{{course}} Name characters should be between 3 to 50", "admin_subject": "Administration Subject is required", "participation_subject": "ParticipationSubject is required", "course_occurring": "{{course}} {{name}} is required", "duration_weeks": "Duration in Weeks is required", "duration_weeks_min": "Duration in Weeks should be between 1 to 52", "credit_hours": "Credit Hours should be required and should be number", "select_deliveryType": "Please select at least one delivery type for each session type", "select_sessionType": "At least one session type should selected", "sessionTypeUsed": "Session type already used", "subjectUsed": "{{subject<PERSON><PERSON><PERSON>}} already used", "deliveryTypeUsed": "Delivery type already used", "deliveringRequired": "Delivering {{<PERSON><PERSON><PERSON><PERSON>}} is required", "AdministratingRequired": "Administrating {{<PERSON><PERSON><PERSON><PERSON>}} is required"}, "certificate_name": "Enter Certificate Name", "view_s": "View", "back_cc": "Back", "certificate": "Certificate", "enter_curriculum_name": "Enter Curriculum Name", "enter_level": "Enter {{level}}", "departSubjectSharedFrom": "<departmentName/> / <subjectName/> Shared from the Program", "sharedDepartSubject": "Select a <programLabel/> / <departmentLabel/>", "program_duration": "Duration", "per": "per", "total_no_levels": "Total No. of", "mixed": "Mixed", "before_creating_new_curriculum": "Before creating new curriculum", "need_to_be_configured": "need to be configured", "enter_credit_hour": "Enter Credit Hour", "enter_max_credit_hour": "Enter Max Credit Hour", "enter_min_credit_hour": "Enter Min Credit Hour", "select_framework": "Select Framework", "noSubject": "No <name/>", "dynamicName": "<name/> Name", "no_match_found": "No Match Found", "credit_hour_max": "Max value must be greater than min value", "department_subject_session_delivery": "<department/> and <subject/>, Session and Delivery Types", "duplicate": "Duplicate", "advance_settings_desc": "<course/> which needs to be completed before this <course/> needs to be tagged, Will be used in Reports and Auto Scheduling", "add_PreRequisite_course": "Add Pre-requisite course", "add_CoRequisite_course": "Add Co-requisite course", "portfolio_desc": "Add any Goals, Attributes, Study Plan... etc", "course_delivering_subjects": "Delivering <subject/>", "before_creating_new_course": "Before creating new <course/>", "label_config": {"course_type": "{{course}} Type", "level_config": "<level/> Configuration", "course_code": "{{course}} Code", "course_name": "{{course}} Name", "course_editor": "{{course}} Editor (Optional)", "course_master_list": "{{course}} Master List", "course_details": "{{course}} Details", "course__type": "<course/> Type", "course__code": "<course/> Code", "course__name": "<course/> Name", "course__editor": "<course/> Editor (Optional)", "course__master_list": "<course/> Master List", "course__details": "<course/> Details", "editor": "Editor"}, "there_no_data": "There is No data", "description": "Description", "userManagement": {"configuration_designation": "Designation Configuration", "designation": "Designation", "designations": "Designations", "category": "Category", "category_name": "Category Name", "add_designation": "Add Designation", "edit_designation": "Edit Designation", "add_new_designation": "+ Add New Designation", "disable_assistant": "Disable {{name}}", "delete_assistant": "Delete {{name}}", "disable_content": "As {{name}} Designation is assigned to multiple staffs, User Should change the designation to {{type}} the current designation", "search": "Search ID/Name", "emp_name": "Employee Name", "emp_id": "Employee Id", "email": "Email <PERSON>d", "mail_content": "Mail Content Configuration", "Edit_request": "Edit - Request For Verification", "request_verification": "Request for verification", "mail_setting": "Mail Settings Configuration", "request_mail": "Request Mail expiration", "set_reminder": "Set Reminder Mail", "recurring": "Recurring", "bio": "Biometric Configuration", "configured": "Configured"}, "termsBetween": "No Of {{TermNameLabel}} Must be between 1 to 99", "termsNotNumber": "No Of {{TermNameLabel}} Should be Number", "drafted_successfully": "Drafted successfully", "already_selected": "{{label}} already selected", "start_week_is_required": "Start week is required", "curriculum_is_required": "Curriculum is required", "is_required": "{{label}} is required", "share_this": "Share this", "week_one": "week", "week_other": "weeks", "curriculum_requirement": "Curriculum Requirement", "department_restructured": {"add_new_department": "+ Add New <departmentName/>"}, "delete_selective": {"title": "Selective", "desc": "This action will permanently remove the selected {{typeCourse}} from the application!"}, "validation_selective": {"credit_hours": "Please fill all the fields", "max_credit_hours": "Max credit hours not match", "credit_hours_length": "Credit Hours length must be less than 3"}, "document": "Document", "activity_log": "Activity Log", "departments_subjects": {"total_departments_subjects": "Total <departmentName/> and <subjectName/>", "department_type": "<departmentName/> Type", "admin": "Admin"}, "delete_doc_category": {"desc": "This action will permanently remove the selected {{category}} from the application!"}, "independentCourse": {"step1": "Step 1", "step2": "Step 2", "independentCourseLabel": "Independent Course", "List": "List", "addNew": "Add Independent <course/>", "directlyRun": "A <course/> run directly by the <department/>", "course": "<course/>", "configure_session": "Configure the Session types and Delivery types applicable for the Independent <course/>", "noSession": "No Sessions Created", "noCourse": "No <course/> Created", "courseDuration": "<course/> Duration", "sessionAndDeliveryType": "Session and Delivery Types", "shareThisCourse": "Share this <course/> "}, "Others": "Others", "mail": "Mail", "documents": "Documents", "overview_alert": {"title": "Redirecting To <department/> and <subject/>", "desc": "This action will take you to the <department/> and <subject/> module"}, "no_courses_in_this_level": "No courses in this level", "add_courses_by_clicking": "Add courses by clicking the <addIcon/> Course icon above", "no_of_rotations": "No of rotations", "student_grouping": {"no_course": "No courses found here!", "unpublished_program": "This may be due to unpublished program calendar.", "delivery": "Delivery", "male_groups": "Male Groups", "female_groups": "Female Groups", "export": "EXPORT", "import": "IMPORT", "close": "CLOSE", "cancel": "CANCEL", "save": "SAVE", "back": "BACK", "search_academic": "Search by academic number", "courseGroupSettings": " COURSE GROUPS SETTINGS", "publish": "PUBLISH", "add": "ADD", "group": "GROUP", "confirm": "CONFIRM", "no_student_groups": "No student groups found here", "no_student_groups_reason": "This may be due to no students imported or no grouping performed on the existing students in the course.", "extras_allowed": "Extras allowed in each group", "student_grouping_profile": "Student grouping profile", "ungrouped": "Ungrouped", "rotation_group_settings": "Rotation Group Settings", "program_calendar": "Program Calendar", "gender_options": "Gender options per student rotation group", "rotation": "Rotation", "gender": "Gender", "group_capacity": "Group Capcity", "group_name": "Group Name", "provide_students": "  Please provide equal or near equal number of students in each group", "publish_notify": " The following institute personnel will be notified on publishing the student groups", "program_vice_dean": " Program Vice Dean", "course_coordinators": "Course Coordinators", "department_chairman": " Department Chairman", "students": "Students", "groups": "Groups", "noOfGroups": "No. of Groups", "students_per_group": "Students per Group", "session_delivery_type": "Session/Delivery Type", "ungrouped_students": "ungrouped students", "term": "Term", "level": "Level", "program": "Program", "regular": "Regular", "interim": "Interim", "course_groups": "Course Groups", "select_recent": " Select recent imports", "select_all_page": "Select all in this page", "select_all": "Select all", "deselect_all": "Deselect all", "foundation_group_settings": "Foundation Group Settings", "noOfStudentsGroup": " No. of students in each group", "total_group_capacity": "Total groups capacity - No. of students", "noOfMaleGroups": "No. of male groups", "noOfFemaleGroups": "No. of female groups", "male": "Male", "female": "Female", "preview": "Preview", "add_student": "Add Student", "student_details": "Student details", "name": "Name", "enrolled_program": "Enrolled Program", "no_data": "No data found", "student_exists": "The student already exists", "no_student": "No Students Selected", "clone_student": "Clone student groups", "clone_student_description": "Cloning student groups replicates identical groups and students of those groups in one or more selected courses. Cloning is applied for courses within the foundation year or rotation student groups. Cloning is applicable when student groups are created or edited.", "select_applicable_courses": "Select applicable courses to clone student grouping", "not_cloned": " All existing student groups and settings in the selected courses will be overwritten. Grouping for students who are added manually will not be cloned.", "course_group_details": "Course Group Details", "student_name": "Student Name", "serial_no": "S.NO", "academic_no": "Academic No", "import_students": "Import students", "select_appropriate": "Select appropriate options", "select_term": "Select a term", "select_curriculum": "Select a curriculum", "select_course": "Select a course", "next": "NEXT", "confirm_delete": "Confirm Delete", "confirm_delete_msg": "Are you sure you want to delete the student?", "yes": "YES", "no": "NO", "course_group_settings": "Course Group Settings", "select": "Select", "format": "Format", "no_student_found": "No Students found in this group", "automatic": "Automatic", "manual": "Manual", "existing_grouping": " All the existing delivery types will be auto-grouped based on", "current_groups": "Current groups availability", "select_student_group": "Select a student group ", "add_selected_students": "Add selected students to", "select_delivery": " Select existing delivery types for grouping", "students_selected": "No. of Students selected", "IUnderstand": "I Understand", "editing_info": "  Editing the student’s foundation group will ungrouped him/her in all the courses groups, if grouped. Make sure to regroup the student(s) in all course groups.", "alert": "ALERT", "course": "Course", "group_allocation": "Group Allocation", "edit": "Edit", "student_id": "Student ID", "ungrouped_info": "Only then, the students shall be shown in the Ungrouped list under each Course.", "students_grouped": "There are students to be grouped in", "ungrouped_list": "Student Groups - Ungrouped list.", "select_file": " Select a file to import ", "accepted_format": "Accepted file formats", "browse": "BROWSE", "download_template": "DOWNLOAD TEMPLATE", "continue": "CONTINUE", "total_students": " Total number of students", "entities_error": " Entities with error message will not be imported.", "duplicates_import": "Only one of the duplicates will be imported", "importing_data": " Importing data", "imported": "Imported", "academic_year": "Academic Year", "error_message": "Error Message", "import_timestamp": "Import Timestamp", "import_by": "Import By", "academic_id": "Academic ID", "marks_gpa": "Marks/GPA", "marks": "Marks", "cgpa": "CGPA", "no_students_found": "No Students Found", "year": "Year"}, "eventDescription": "Event Description", "reviewEvent": "Review Event", "gregorian": "<PERSON><PERSON>", "maleOnly": "Male Only", "femaleOnly": "feMale Only", "chooseGender": "<PERSON>ose <PERSON>", "chooseEventType": "Choose Event Type", "chooseVenueType": "<PERSON><PERSON> Venue Type", "chooseStartDate": "Choose start Date", "chooseEndDate": "Choose end Date", "endDateGreat": "End date should be greater than start date", "endTimeGreat": "End time should be greater than start time", "eventNameRequired": "Event Name is Required", "minimumThreeChar": "Minimum 3 character is required", "event_deleted_successfully": "Event Deleted Successfully", "event_updated_successfully": "Event Updated Successfully", "event_created_successfully": "Event Created Successfully", "calender_created_successfully": "<PERSON><PERSON> Created Successfully", "deanPublished": "Dean Published the Institution Calendar for the Academic year", "publishedSuccessfully": "Institution Calendar Published Successfully", "publishCalender": "Are you sure you want Publish this Calendar", "notifyUser": "Notify all users", "students": "Students", "sms": "SMS", "notifyVia": "Notify via", "reviewAccept": "Review Accept", "sureDelete": "Are you sure to delete", "Date": "Date", "publishInstitution": "Publish Institution Calendar", "eventDateBetween": "Event date should be between {{startDate}} and {{endDate}}", "viewEvents": "View Events", "backToCalendar": "Back to Calendar", "chooseSendNotify": "Please Choose Send Notify To", "chooseNotification": "Please Choose the Notification", "completeAllReview": "Complete All Review", "skipReview": "Skip Review", "sendEventsToReview": "Send all events to review", "noEventData": "There is no Event Data", "responses": "Responses", "reply": "Reply", "agree": "Agree", "disagree": "Disagree", "send": "Send", "confirmReview": "Confirm Review Completion", "wantToCompleteReview": "Are You sure you want to Complete the review for events in this calender", "notifyUserVia": "Notify all reviewers via", "selectNotificationType": "select the type of notification for each sender", "sendersList": "Senders list", "addDeadLine": "Add deadline to submit review", "searchStaff": "Search Staff", "mainCalendar": "Main calendar", "role": "Role", "allReviewCompleted": "All Review Completed Successfully", "reviewCompleted": "Review Completed Successfully", "chooseAgreeOrDisagree": "Choose Agree Or Disagree", "staffDeleted": "Staff Deleted Successfully", "staffAdded": "Staff Added Successfully", "reviewRequestSuccess": "Review Request Sent Successfully", "deanCompletedReview": "<PERSON> has Completed the Institution Calendar Review.", "chooseOneStaff": "Choose atleast one staff", "mandatoryRequired": "Mandatory fields should not be empty", "chooseEndTime": "Choose End Time", "searchByTypeExist": "Search by name, email, academic no...", "searchByTypeNotExist": "Search by name, email, employee id...", "once_deleted_cant_recover": "Once event deleted, can't recover", "yes_delete": "Yes, delete it!", "no_keep": "No, keep it", "level_dates_saved": "{{Level}} dates saved successfully", "setting_level_dates_unsuccessfull": "Setting {{level}} dates went wrong. Please try again", "start_end_dates_assigned": "Start date and End date assigned successfully to particular level", "event_delete_went_wrong": "Event delete went wrong. please try again", "program_events_added_successfully": "Program Calendar Events Added Successfully", "program_events_updated_successfully": "Program Calendar Events Updated Successfully", "semester": "<PERSON><PERSON><PERSON>", "leaveManagement": {"errorMsg": {"validMail": "Please enter Valid Email Address", "maxAllowedPerMonth": "Max.allowed per month is {{days}}"}, "tabs": {"Permission": "Permission", "Leave": "Leave", "On Duty": "On Duty", "leave": "leave", "permission": "Permission", "on_duty": "On-duty", "On-Duty": "On-Duty", "Reports": "Reports"}, "approved": "Approved", "Applied_on": "Applied on", "Permission_Time": "Permission Time", "Permission_date": "Permission date", "Availed_Status": "Availed {{type}} Status", "confirm_calcel_applied": "Are you sure you want to cancel your applied", "edit_further": "You can edit it to a future date instead.", "List_of_all": "List of all", "applied_and_taken": "applied and taken", "s": "s", "Choose_date": "Choose a date", "Start Date": "Start Date", "End Date": "End Date", "Reason": "Reason", "Select_category": "Select a category", "Select_type": "Select a type", "Application": "Application", "days": "days", "day": "day", "Choose_time": "Choose a time", "hour": "hour", "schedule_select_time": " You have a scheduled activity during the selected time. If substitute staff is not available then the leave application may get rejected.", "attachment": "Attachment", "Exceeds_2MB_limit": "Exceeds 2 MB limit", "accepted_formet": "Accepted formats [ PDF, JPG, PNG ]. Max file size 2 MB.", "Total_No_of_days": "Total No of days", "Date": "Date", "Start Time": "Start Time", "End Time": "End Time", "Application_Summary_Rejected": "Application Summary -Rejected", "to": "to", "personal_notify_leave": "The below personnel will be notified about the leave:", "Leave_applicant": "Leave applicant", "Concerned_course_coordinator": "Concerned course coordinator", "Concerned_department_chairman": "Concerned department chairman", "Concerned_vice_dean": "Concerned vice dean", "Approvers": "Approvers", "Substitute_staff": "Substitute staff", "HR_manager": "HR manager", "re_apply": "RE-APPLY", "Confirm_submit": "Confirm submit", "dates_": "Dates", "application_summary": "application summary", "leave_application_pending": "List of all pending leave applications ", "Time_Stamp": "Time Stamp", "searchBy_id_name": "Search by employee id,name,..", "Administrative": "Administrative", "Report_staff_absence": "Report a staff's absence", "report": "Report", "Select_the_staff": "Select the staff", "Noticed": "Noticed", "Un_noticed": "Un-noticed", "optional": "(optional)", "staff_report_absence_breadcrumb": "Faculty Academic Accountability Management > Report Absence", "Suspend_Absence_Report": "Suspend Absence Report", "Mandatory_information": "Mandatory information", "Suspend_reason": "Suspend reason", "Academic_Information": "Academic Information", "Contact_Information": "Contact Information", "Employment_Type": "Employment Type", "staff_required_sessions": "Please select the substitute staff for all the required sessions", "approve_type_applications": "Approve {{type}} Application", "reject": "REJECT", "COMPLETE": "COMPLETE", "application_is": "application is", "confirm_proceed": "click CONFIRM to proceed", "Reviewed": "Reviewed", "Rejected": "Rejected", "Applicant": "Applicant", "Salary": "Salary", "Unpaid": "Unpaid", "Payment_Status": "Payment Status", "No_Attachment": "No Attachment", "Permission_overview": "Permission overview", "Academic_year": "Academic year", "Add_comments": "Add comments", "successMsg": {"permissionUpdated": "Permission Updated Successfully.", "update": "Updated", "delete": "Deleted", "create": "Created", "applied": "Applied", "Added": "Added", "Updated": "Updated", "activated": "Activated", "deactivated": "Deactivated", "Leave": "Leave", "On-Duty": "On-Duty", "categorySuccess": "{{type}} Category {{operation}} Successfully.", "typeSuccess": "{{type}} Type {{operation}} Successfully.", "onlyTypeSuccess": "Type {{leaveUpdate}} Successfully.", "leaveApprovalMessage": "Leave / Permission / On-Duty Settings at Approval Level {{name}} {{type}} Successfully", "reportAbsenceSettingMsg": "Report Absence Setting Updated Successfully.", "hrMailStatus": "HR Email ID Updated Successfully", "studentWarningSettings": "Students Leave Warning & Absence Settings {{operation}} Successfully", "Cancelled_Successfully": "Cancelled Successfully"}, "HR_MailId": "HR email id", "setFrequency": "Set frequency for permission", "applicantAssign": "Let the applicant assign a substitute", "setTimeAllowForPermission": "Set time allowed for permission (hours)", "attachmentMandatory": "Attachment mandatory", "allowApplySessionApplied": "Allow applicant to apply while his/her session is scheduled", "perYear": "per year", "perMonth": "per month", "notApplicable": "not applicable", "editOD": "Edit OD Category", "confirmDeleteCategory": "Are you sure you want to delete the category?", "confirmDeleteType": "Are you sure you want to {{data}} the type?", "yesSure": "YES, I'M SURE", "allTypeDelete": "All types under this category will also be deleted.", "bothGender": "Gender - Male, Female", "genderMale": "Gender - Male", "genderFemale": "Gender - Female", "entitlementPerCourse": "Entitlement - {{no_of_days}}% /Course", "entitlementPerEntitlement": "Entitlement - {{no_of_days}} days/{{entitlement}}", "paymentStatus": "Payment status - {{payment}}", "considerHolidayBetweenWorkingDays": "Consider holidays/weekends as leave when it falls between two working days", "requiredReason": "Requires reason from the applicant", "requiredProof": "Requires proof to be attached", "addType": "Add Type", "maxAllowed": "Max. allowed per month", "percentPerCourse": "% per course", "courseSessions": "Course Sessions", "numberOfDays": "Number of days", "entitlement": "Entitlement", "entitlementBasedOn": "Entitlement based on", "descriptionOptional": "Description (optional)", "typeName": "Type Name ", "addOD": "Add OD type", "editODTpe": "Edit OD type", "createOD": "Create OD Category", "addLeave": "Add leave type", "createLeave": "Create Leave Category", "editLeaveType": "Edit leave type", "confirm": "Confirm {{data}}", "editLeave": "Edit Leave Category", "gender_specific": "Gender specific", "category_name": "Category Name", "Paid": "Paid", "Activate": "Activate", "Deactivate": "Deactivate", "onDutyClassifications": "On Duty Classifications", "addApprovers": "Add Approvers", "leaveApprovalProcess": "Leave Approval Process", "approverLevelName": "Approver level name", "approverAssociation": "Approver association", "InstituteRole": "Institute role", "academicStaff": "Academic Staff", "administrativeStaff": "Administrative Staff", "applicantsDept": "Applicants Dept/Prog", "others": "Others", "searchBy": "Search by staff type,level,role..", "approvalLevel": "Approval Level", "approvalLevelName": "Approval Level name", "approvalAssociated": "Approval associated to", "approvalAssociation": "Approval association", "staffList": "Staff List", "searchByMail": "Search by email id, employee id...", "empID": "Employee Id", "staffName": "Staff name", "sureDelete": "Are you sure you want to delete", "attachmentMandatoryReport": "Attachment mandatory to report absence", "addWarning": "Add Warning", "warning": "Warning", "warningMessage": "Warning Message", "absence": "Absence %", "absencePercent": "Absence percentage: No. of absent sessions / Total number of sessions x100", "warningAndAbsenceCalc": "Warning and absence Calculation", "level": "LEVEL", "gender": "GENDER", "studentLevelEntry": "STUDENT LEAVE ENTRY", "noDataFound": "No data found here!", "selectFilters": "Please select filters.", "programCourseList": "Program Course List", "denialAbsencePercent": "Denial - Absence Percentage", "criteriaManipulated": "CRITERIA MANIPULATED", "allStudents": "All Students", "criteriaManipulation": "Criteria Manipulation", "academic": "ACADEMIC #", "name": "NAME", "attendedConducted": "ATTENDED / CONDUCTED", "totalSession": "TOTAL SESSION", "attendance": "ATTENDANCE %", "absent": "ABSENT %", "noOfLeave": "NO OF LEAVE", "warningAbsent": "WARNING ABSENT %", "studentLateAbsent": "STUDENT LATE ABSENT", "status": "STATUS", "enRolledStudents": "Enrolled students", "CriteriaManipulation": "CRITERIA MANIPULATED %", "addNewEntry": "Add new entry", "listAllStudents": "List of all student leaves entered", "searchByStudents": "Search by student name or academic id", "applicationDate": "Application date", "leaveType": "LeaveType", "approveOn": "Approved on", "studentLeaveEntries": "Student Leave Entries", "selectStudent": "Select the Student", "selectLeave": "Select Leave Type", "leaveCategory": "Leave Category", "leave Type": "Leave Type", "onDutyCategory": "OnDuty Category", "onDutyType": "On-Duty Type", "typeHere": "Type here", "exceedsLimit": "Exceeds 2 MB limit.", "approvedBy": "Approved by", "commentOptional": "Comment (optional)", "enterStudentAbsence": "Enter student absence", "searchStudent": "Search Student", "searchByAcademic": "Search by email id, academic id...", "StudentList": "StudentList", "Absentee": "Absentee", "Dean": "<PERSON>", "StudentRegistered": "Student {{data}} Registered", "on-DutyCategory": "On-Duty Category", "personWillNotify": "The below persons will be notified about the absence:", "confirmReportAbsence": "Confirm Report Absence", "reasonForManipulation": "Reason For Criteria Manipulation", "editCriteria": "Edit the criteria for Denial absence %", "noOfStudents": "No of students", "updateStudentList": "UPDATE STUDENT LIST WITH NEW CRITERIA", "contactInformation": "Contact Information", "totalAttendedSessions": "Total attended sessions", "outOf": "out of", "sessions": "sessions", "presentTime": "Present time per session", "recordEnteredBy": "Record entered by", "attendanceRecord": "Attendance record timestamp", "authenticationMode": "Authentication mode", "primaryStaff": "Primary Staff", "attendanceStatus": "Attendance Status", "modeOfInstruction": "Mode of Instruction", "sessionStartTime": "Session start time", "sessionWiseAttendance": "Sessionwise attendance", "noSessionsFound": "No Sessions Found", "notAvailable": "Not Available", "Present": "Present", "Absent": "Absent", "Auto": "Auto", "studentAttendanceSheet": "Student attendance sheet", "term": "TERM", "Cancelled": "Cancelled", "time": "TIME", "listAllPending": "List of all pending {{tab}} applications", "staffLeaveManagement": "Staff Leave Management"}, "select": "Select", "shared_from": "Shared From", "shared_from_col": "Shared From :", "from": "From", "with": "With", "reports_analytics": {"no_of_program_learning_outcomes": "No. of Program Learning Outcomes", "no_of_credit_hours": "No. of Credit Hours", "no_of_students": "No. of Students", "no_of_staffs": "No. of Staffs", "no_of_plo": "No. of PLO", "search_program_name_code": "Search Program Name, Code", "no_programs_found": "No programs found", "no_of_years": "No. Of Years", "male": "Male", "female": "Female", "part_time": "Part Time", "full_time": "Full Time", "theory": "Theory", "practical": "Practical", "clinical": "Clinical", "overview": "Overview", "student_details": "Student Details", "staff_details": "Staff Details", "no_of_students_registered_in_courses": "No. of Students Registered in Courses", "no_of_courses": "No. of Courses", "no_courses_found": "No courses found", "course_id_name": "Course ID / Name", "rotation": "Rotation", "final_warning": "Final Warning", "denial": "Denial", "credit": "Credit", "contact_hr": "Contact hr", "attendance_percentage": "Attendance Percentage", "no_of_session_completed": "No. of Session Completed", "session_incomplete": "Session Incomplete!", "not_started": "Not Started", "on_going": "On Going", "session_completed": "Session Completed", "all_levels": "ALL LEVELS", "student_name": "Student Name", "academic_no": "Academic No.", "denial_courses": "Denial Courses", "search_name_id": "Search Name, ID", "levels": "{{Level}}s", "no_records_found": "No records found", "staff_count_includes_department_staff": "Staff count is calculated by including shared department staff’s", "no_of_staffs_in_departments": "No. of Staffs In Departments", "staff_count_excludes_department_staff": "Staff count is calculated by excluding shared department staff’s", "all_courses": "All Courses", "search_department_name": "Search Department Name", "no_departments_found": "No departments found", "no_subjects_found": "No subjects found", "staff_name": "Staff Name", "staff_id": "Staff ID", "administration": "Administration", "academic": "Academic", "academic_and_administration": "Academic and Administration", "staff_type": "Staff Type", "primary_program": "Primary Program", "primary_department": "Primary Department", "primary_subject": "Primary Subject", "employment_type": "Employment Type", "assigned_course": "Assigned Course", "workloads": "Workloads", "current_courses": "Current Courses", "ongoing": "Ongoing", "attendance_status": "Attendance Status", "academic_details": "Academic Details", "employment_details": "Employment Details", "academic_information": "Academic Information", "academic_id": "Academic ID", "enrolled_program": "Enrolled Program", "enrolled_term": "Enrolled Term", "enrolled_date": "Enrolled Date", "delivered_session": "Delivered Session Learning Outcome", "planned_session": "Planned Session Status", "session": "Session", "credit_hours": "Credit Hours", "contact_hours": "Contact Hours", "scheduled_session": "Scheduled Session Status", "support_session": "Support Session Status", "staff_session": "Staff’s Scheduled Session Status", "student_group": "Student Group", "participating_subjects": "Participating Subjects", "admin_subject": "Administrating Subject", "administrating_department": "Administrating Department", "from": "/ from", "shared_with": "Shared with", "course_coordinator": "Course Coordinator", "select_domain_theme": " Select Domain/Theme", "all_delivery_types": "ALL DELIVERY TYPES IN THE SELECTED GROUP", "gender": "Gender", "noOfSessions": "No. of Sessions", "noOfSessionsAttended": "No. of Sessions Attended", "noOfWarning": "No. of Warning", "warning": "Warning", "sort_by": "Sort By", "export": "EXPORT", "noOfAbsent": "No. of Leave / Absent", "attendance": "Attendance", "all": "All", "export_attendance_report": "Export Attendance Report", "choose_type": "Choose Type", "staff_attendance_log": "Staff Attendance Log", "every_session_log": "Log of every sessions", "student_attendance_log": "Student Attendance Log", "which_student_group": "Which Student Group", "start_date": "Start Date", "end_date": "End Date", "cancel": "Cancel", "export_pdf": "Export PDF", "report_from": "Report From", "staff_attendance": "Staff Attendance", "session_details": "Session Details", "topic_name": "Topic Name", "date_time": "Date & Time", "student_groups": "Student Groups", "duration": "Duration", "subject": "Subject", "mode": "Mode", "staff": "Staff", "infra": "Infra", "status": "Status", "no_schedule": "No Schdeule Found", "summary": "SUMMARY", "name": "Name", "delivery_type": "Delivery Type", "start_time": "Start Time", "end_time": "End Time", "completed": "Completed", "pending": "Pending", "merged": "<PERSON>rged", "canceled": "Canceled", "missed": "Missed", "onsite": "Onsite", "remote": "Remote", "Interactive lecture": "Interactive lecture", "Laboratory practical session": "Laborotory practical session", "English": "English", "course_student_groups": "Course & Student Groups", "avg": "Avg", "serial_no": "S.NO", "mapped_course_learning_outcome": "Mapped Course Learning Outcome", "mapped_program_learning_outcome": "Mapped Program Learning Outcome", "program_learning_outcome": "Program Learning Outcome", "mapped": "Mapped", "show": "Show", "hide": "<PERSON>de", "attendance_log": "Attendance Log", "activity": "Activity", "passport_no": "Passport No.", "date_of_joining": "Date of Joining", "no_of_departments": "No. of Departments", "shared": "Shared", "deptartments": "Deptartments", "no_of_subjects": "No. of Subjects", "session_status": "Session Status", "settings": "Settings", "term": "Term", "academic_year_level": "Academic Year / Level", "level": "Level", "total_no_of_staffs": "Total No. of Staffs", "total_no_of_students": "Total No. of Students", "total_no_of_sessions": "Total No. of Sessions", "total_no_of_credit_hours": "Total No. of Credit Hours", "total_no_of_contact_hours": "Total No. of Contact Hours", "total_no_of_courses": "Total No. of Courses", "total_no_of_support_sessions": "Total No of Support Sessions", "total_no_of_subjects": "Total No. of Subjects", "no_of_sessions": "No. of Sessions", "target_benchmark": "Target Benchmark", "class_achievement": "Class Achievement", "selected_item": "Selected Item", "all_completed_sessions": "All Completed Sessions", "s_no": "S.No.", "summary_sm": "Summary", "session_canceled": "Session Canceled", "view_details": "View Details", "enabled": "Enabled", "disabled": "Disabled", "no_of_students_answered": "No Of Students Answered", "questions": "Questions", "by": "By", "quiz": "Quiz", "answered_correct": "Answered Correct", "percentage_note": "Note: Percentage values computed are based on Total no of Students", "individual_responses": "Individual Responses", "answered": "Answered", "min_left": "<PERSON>", "responses_received": "responses received", "session_missed": "Session Missed", "student_attendance_report": "Student Attendance Report", "staff_attendance_report": "Staff Attendance Report", "attended": "Attended", "browser_does_not_support": "Your browser does not support the audio element.", "correct_answer": "Correct Answer", "right_answer_feedback": "Right Answer Feedback", "wrong_answer_feedback": "Wrong Answer Feedback", "general_feedback": "General <PERSON>", "total_students": "Total Students", "counselling": "Counselling", "academic advisor": "academic advisor", "feedback": "<PERSON><PERSON><PERSON>", "training": "Training", "executed": "Executed", "planned": "Planned", "scheduled session": "Scheduled Session", "no_data": "No data available", "session_s": "Session(s)", "show_learning_outcome": "Show Learning Outcome", "show_course_learning_outcome": "Show Course Learning Outcome", "show_program_learning_outcome": "Show Program Learning Outcome", "select_curriculum": "Select Curriculum", "framework": "Framework", "theme": "THEME", "selected": "Selected", "Directed self learning": "Directed self learning", "Anatomy": "Anatomy"}, "student_group_not_created": "Student Group not Created", "regular_session": "Regular Session", "session": "Session", "no_schedule_found": "No Schedule Found", "TYPE_colon": "TYPE:", "MODE_colon": "MODE:", "INFRA_colon": "INFRA:", "STUDENT_GROUPS_colon": "STUDENT GROUPS:", "DURATION_colon": "Duration:", "SUBJECT_colon": "SUBJECT:", "TOPIC_colon": "TOPIC:", "STAFF_colon": "STAFF:", "courses_scheduled": "Courses Scheduled", "select_level_year": "Please select YEAR and LEVEL to view timetable", "student_group": "Student Group", "infra": "Infra", "schedule": "Schedule", "no_session_flow_found": "No session flow found", "student_groups": "Student Groups", "title": "Title", "event": "Event", "re-assign": "Re-Assign", "merge_sessions": "<PERSON><PERSON>", "merge": "<PERSON><PERSON>", "list": "List", "session_title": "Session Title", "event_title_type": "Event Title / Type", "infrastructure": "Infrastructure", "note": "NOTE", "course_group": "Course Group", "delivery_group": "Delivery Group", "delivery_session": "Delivery Session", "topic": "Topic", "Regular Session": "Regular Session", "Support Session": "Support Session", "Events": "Events", "Pending": "Pending", "Scheduled": "Scheduled", "no_students_found": "No students found", "acad_no": "Acad. No.", "search_student": "Search Student", "general": "general", "exam": "exam", "training": "training", "day": "Day", "disable": "Disable", "enable": "Enable", "publish": "Publish", "export": "Export", "days": {"sun": "SUN", "mon": "MON", "tue": "TUE", "wed": "WED", "thu": "THU", "fri": "FRI", "sat": "SAT"}, "close": "Close", "subject": "Subject", "course": "Course", "assign_staff": "Assign Staff", "course_schedule": {"status": "Status", "extra_curricular": "Extra Curricular", "break": "Break", "start_time": "Start Time", "end_time": "End Time", "items_not_created": "Items not created", "to_create_click_add_button": "To create click add button", "allow_course_coordinators_to_edit": "Allow Course Coordinators To Edit", "allow_course_coordinators_to_schedule": "This will allow course coordinators to schedule session at this time", "mode": "Mode", "set_time": "Set Time", "daily": "Daily", "weekly": "Weekly", "choose_delivery_types": "Choose Delivery Types", "auto_assign_session_default_settings": "Auto assign session default settings", "delivery_types": "Delivery Types", "scheduled_session": "Scheduled Session", "no_session_found": "No session found", "break_session_flow": "Break session flow", "break_session_flow_desc": "Breaking session flow will allow user to schedule session in any order", "session_default_settings": "Auto assign session default settings", "session_default_settings_desc": "Session default settings will be automatically assigned to the sessions", "delete_extra_curricular_title": "Delete Extra Curricular & Break", "delete_extra_curricular_desc": "Are you sure you want to delete the selected data", "extra_curricular_break": "Extra Curricular & Break", "confirm_publish": "Confirm Publish", "confirm_publish_desc": "Are you sure you want to publish?", "what_to_export": "What to Export?", "export_type": "Export Type", "cancel_schedule": "Cancel Schedule", "cancel_schedule_desc": "Are you sure you want to cancel the selected schedule?", "delete_schedule": "Delete Schedule", "delete_schedule_desc": "Are you sure you want to delete the selected schedule?", "fieldNames": {"Title": "Title is required", "Gender": "Gender is required", "Mode": "Mode is required", "Days": "Days is required", "Start Time": "Start Time is required", "End Time": "End Time is required"}, "gender_list": {"male": "Male", "female": "Female", "both": "Both"}, "type_list": {"break": "Break", "extra_curricular": "Extra Curricular"}, "status_list": {"enable": "Enable", "disable": "Disable"}, "validation": {"select_topic": "Please select a topic", "select_type": "Please select a type", "choose_date": "Please choose a date", "choose_valid_date": "Invalid date. Please choose a valid date.", "valid_start_time": "Please choose a valid start time", "valid_end_time": "Please choose a valid end time", "same_time": "Start and end time cannot be same", "greater_start_time": "End time should be greater than start time", "select_course_group": "Please select one or more course groups", "select_subjects": "Please select one or more subjects", "select_staffs": "Please select one or more staffs", "select_mode": "Please select a mode", "select_infrastructure": "Please select a infrastructure", "select_rotation": "Please select rotation", "end_time_should_be_greater_than_start_time": "End time should be greater than start time.", "data_already_exists": "Data already exists with same data", "title_is_required": "Title is required", "at_least_one_student_is_required": "At least one student is required", "please_select_atleast_one_student_group": "Please select atleast one student group"}}, "view_credit_hours": "VIEW CREDIT HOURS", "credit_hours_details": "Credit Hours Details", "co_hr_per_cr_hr": "Co.Hr.Per Cr.Hr", "session_per_cr_hr": "Session Per Cr.Hr", "per_session_duration": "Per Session Duration", "total_co_hr": "Total Co.Hr", "total_sessions": "Total Sessions", "no_session_delivery_found": "No session/delivery found", "hr": "HR", "min": "min", "session_default_settings": "Session Default Set<PERSON>s", "manage_topic": "Manage Topic", "break_session_flow": "Break Session Flow", "breaking_session_flow": "Breaking session flow will allow user to schedule session in any order", "auto_assign_session_default_settings": "Auto assign session default settings", "session_default_settings_will_be_assigned": "Session default settings will be automatically assigned to the sessions", "create_topic_set": "CREATE TOPIC SET", "delivery_type_set": "DELIVERY TYPE SET", "topic_title": "TOPIC TITLE", "no_topics_found": "No topics found", "group_setting": "GROUP SETTING", "enter_topics": "Enter Topics", "topic_set": "Topic Set", "choose_delivery_type": "Choose Delivery Type", "add_topic": "Add Topic", "enter_topic": "Enter Topic", "select_delivery_type": "Select Delivery Type", "start_end_time_should_not_overlap": "Start and End time should not overlap Extracurricular/Break time", "please_choose_session": "Please choose session / delivery group and setting", "staffManagement": {"registered": "Registered", "pending": "Registration Pending", "inactive": "Inactive", "add_new_staff": "Add New Staff", "roles": "Roles", "emp_name": "Employee Name", "emp_id": "Employee Id", "email": "Email <PERSON>d", "national": "National Id", "gender": "Gender", "status": "Status", "fullyVaccinated": "Fully Vaccinated", "partiallyVaccinated": "Partially Vaccinated", "vaccinationIncomplete": "Vaccination Incomplete", "make_inactive": "Make Inactive", "send_mail": "Send Mail", "assign_role_pending": "Assign role pending", "auxiliary_allocation": "Auxiliary Allocation"}, "phone_number": "Phone Number", "male": "Male", "female": "Female", "add_new_entry": "Add New Entry", "export_error_entries": "Export error entries", "error_entries_found": "{{errorCount}} error entries found out of {{entriesCount}} entries", "error_entries_cannot_be_imported": "Error entries cannot be imported", "duplicate_found": "Duplicate found", "invalid_entry": "Invalid entry", "already_exist_in_db": "Already Exist in Data Base", "continue_sm": "Continue", "no_user_available": "No User Available", "configure_email_to_add_staff": "Mail ID configuration in Global setting must be done before Add/Import staffs", "request_mail_sent_successfully_one": "Request Mail Sent to {{type}} Successfully", "request_mail_sent_successfully_other": "Request Mail Sent to {{count}} {{type}}s Successfully", "are_you_sure_you_want_to_reset": "Are you sure you want to reset", "retake_biometric_registration": "Re-Take Biometric Registration", "delete_the_existing_biometric_data": "This action will delete the existing biometric data, Are you sure you want {{name}} to retake biometric registration?", "yes_retake": "Yes, Re-Take", "Sign_Up": "Sign Up", "Temporary_Password": "Temporary Password", "attainment_tree": "Attainment Tree", "delete_attainment_desc": "This action will permanently remove the selected attainment and related details!", "node": "Node", "delete_node_desc": "This action will permanently remove the selected node and related details!", "University": "University", "Program": "Program", "Show_shared_and": "Show shared {{departmentName}} and {{subjectName}}", "Auth_words": {"login_successfully": "Login Successfully", "Don_t_have_an_account": "Don’t have an account?", "forgot_message": "Please enter your registered email address. We’ll send link to reset your password", "send_reset_link": "SEND RESET LINK", "password_is_required": "Password is Required", "min_eignt": "Minimum 8 character is required", "min_four": "Minimum 4 character is required", "number_only": "Numbers Only Allow", "password_didnot_match": "password didnot match", "password_changed_successfully": "Password Changed Successfully", "invalid_otp": "Invalid OTP"}, "Survey_TagMaster": {"tag_master": "Tag Master", "tags": "Tag's", "groups": "Groups", "family": "Family", "add_more": "Add More", "manage_tags": "Manage Tags", "search_name_code": "name , Code...", "search": "Search", "save": "Save", "cancel": "Cancel", "short_code": "Short Code", "tag_name": "Tag Name", "description": "Description", "tagName": "Tag", "survey": "Survey"}, "User_Module_Permission": {"user_module_permission": "User Module Permission"}, "Disciplinary_Remarks": {"remarkLabel": "Remarks Label", "description": "It will not appear on students if you disable.", "optionWithComment": "With Comment", "optionWithoutComment": "Without Comment", "switchOn": "ON", "switchOff": "OFF"}, "user_global_search": {"disciplinary_remarks": {"addViewLog": "Add / View disciplinary Log", "addDisciplinaryRemarks": "Add Disciplinary Remarks", "addRemarks": "Add Remarks", "searchPlaceholder": "Search for Staff's name / Remarks", "selectRemarksPlaceholder": "Select Remarks", "selectRemarksRequired": "Select remark field is required", "addCommentPlaceholder": "Add a Comment", "addFileButton": "ADD FILE", "moreDetails": "More Details", "sessionDetails": "Session Details", "sentOn": "<PERSON><PERSON>", "viewLabel": "view", "updated": "Updated", "edit": "Edit", "delete": "Delete", "reject": "Reject", "approve": "Approve", "no_Remarks": "No Remarks", "update": "Update", "tableHeadings": {"disciplinaryRemarksColumn": "Disciplinary Remarks", "reportedByColumn": "Reported By", "commentsColumn": "Comments", "statusAttachmentColumn": "Status / Attachment"}}, "uploadFile": {"uploadFileTitle": "Upload File (Max 5)", "fileTypesLabel": "PNG, JPG, MP4.... Some files exceed the 50MB limit", "dragDropLabel": "Drag or Drop the files", "unsupportedFileType": "Unsupported file type", "unsupportedFileSize": "One or more files exceed 50MB", "maxFileCountExceeded": "Maximum file count of 5 exceeded", "totalFileSizeExceeded": "Total file size exceeds 50MB"}, "attachmentView": {"attachmentViewTitle": "Attachment File"}, "action_response_messages": {"saved_successfully": "Saved Successfully", "reset_successfully": "Reset Successfully", "updated_successfully": "Updated Successfully", "deleted_successfully": "Deleted Successfully", "email_sent_successfully": "<PERSON>ail sent successfully", "an_error_occured_try_again": "An error occurred. Please try again."}, "cancelButton": "Cancel", "closeButton": "Close", "uploadButton": "Upload", "sendMailButton": "Send Email", "confirmEmailSending": "Confirm Email Sending", "confirmEmailQuestion": "Are you sure you want to send this email?"}, "q360SettingDashboard": {"categoryConfigure": {"categoryName": "Category Name", "configure": "Configure", "level": "Level", "periodicInterval": "Periodic Interval", "describeOptional": "Decribe (Optional)", "describeHere": "Decribe here", "categoryFor": "Category For", "enterPeriodicNumber": "Enter Periodic Number", "selectCheckBox": "Select the checkboxes for the actions needed:", "academicTermTooltip": "academicTermTooltip", "studentGroupTooltip": "studentGroupTooltip", "attemptTypeTooltip": "attemptTypeTooltip", "academicCalendarTooltip": "academicCalendarTooltip", "occurrenceTooltip": "occurrenceTooltip", "incorporateTooltip": "incorporateTooltip", "displayCaptureTooltip": "displayCaptureTooltip"}}, "q360FormCreation": {"completeForm": "Import the entire form as a single unit. In this mode,Q360 will treat the form as a whole and will not recognize or process individual sections within it. This means Q360 cannot perform data incorporation oranalysis between sections. However, it can analyze and incorporate data across different complete forms.", "sectionForm": "Import each section of the form separately. In this mode, Q360 can recognize and process individual sections, enabling it to incorporate and analyze data between sections from various forms and categories. This allows for more detailed and precise data analysis."}}