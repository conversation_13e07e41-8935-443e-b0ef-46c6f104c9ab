import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import MButton from 'Widgets/FormElements/material/Button';
import DS_logo from 'Assets/ds_logo.svg';
import SnackBars from 'Modules/Utils/Snackbars';
import * as actions from '_reduxapi/user_management/v2/actions';
import { selectMessage } from '_reduxapi/user_management/v2/selectors';
import MaterialInput from 'Widgets/FormElements/material/Input';

class NewPassword extends Component {
  constructor() {
    super();
    this.state = {
      newPassword: '',
      confirmPassword: '',
    };
    this.emailRef = React.createRef();
    this.passwordRef = React.createRef();
  }

  componentDidMount() {
    if (this.emailRef.current !== null) {
      this.emailRef.current.focus();
    }
  }

  onChange = (e, name) => {
    e.preventDefault();
    this.setState({
      [name]: e.target.value,
    });
  };

  validation = () => {
    const { setData } = this.props;
    const { newPassword, confirmPassword } = this.state;

    if (!newPassword) {
      setData({ message: 'New Password is Required' });
      return false;
    }
    if (newPassword.length <= 7 || confirmPassword.length <= 7) {
      setData({ message: 'Minimum 8 character is required' });
      return false;
    }
    if (!confirmPassword) {
      setData({ message: 'Confirm Password is Required' });
      return false;
    }
    if (newPassword !== confirmPassword) {
      setData({ message: 'Password Not Match' });
      return false;
    }

    return true;
  };

  handleSignIn = (e) => {
    e.preventDefault();
    const { updateNewPassword, staffVerification, history, isChangePassword } = this.props;
    const { newPassword } = this.state;

    const id = staffVerification.get('_id', '');
    const instId = staffVerification.get('_institution_id', '');
    const type = staffVerification.get('user_type', '');

    if (this.validation()) {
      const authData = {
        user_type: type,
        setMode: 'password',
        newPassword: newPassword,
        forgotPassword: isChangePassword,
      };
      updateNewPassword({
        formData: { authData },
        id: id,
        history,
        instId: instId,
        isChangePassword,
      });
    }
  };

  render() {
    const { newPassword, confirmPassword } = this.state;
    const { isChangePassword, changeDetails, message } = this.props;
    return (
      <>
        {message !== '' && <SnackBars show={true} message={message} />}
        <div>
          <div>
            <div className="login-bg">
              <div className="container">
                <div className="row justify-content-center pt-3">
                  <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                    <h3 className="text-center">
                      {' '}
                      <img src={DS_logo} alt="Digischeduler" />
                    </h3>
                  </div>
                </div>

                <div className="row justify-content-center pt-3">
                  <div className="col-xl-4 col-lg-5 col-md-7 col-7">
                    <div className="outter-login">
                      <div>
                        <p className="f-20 pt-3 text-center text-gray">
                          {isChangePassword ? 'Set New Password' : 'Sign Up'}{' '}
                        </p>
                      </div>
                      {isChangePassword && (
                        <div className="mt-4 mb-4 f-14">
                          Set new password for <strong>{`${changeDetails.email}`}</strong>
                        </div>
                      )}
                      <React.Fragment>
                        <form>
                          <div className="pt-2">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'text'}
                              placeholder={'New Password'}
                              variant={'outlined'}
                              size={'small'}
                              label={'New Password'}
                              changed={(e) => this.onChange(e, 'newPassword')}
                              value={newPassword}
                            />
                          </div>
                          <div className="pt-2 position-relative">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'text'}
                              placeholder={'Confirm Password'}
                              variant={'outlined'}
                              size={'small'}
                              label={'Confirm Password'}
                              changed={(e) => this.onChange(e, 'confirmPassword')}
                              value={confirmPassword}
                            />
                          </div>
                          <div className="pt-3">
                            <MButton type="submit" clicked={this.handleSignIn} fullWidth>
                              {isChangePassword ? 'Save and continue' : 'Sign Up'}
                            </MButton>
                          </div>
                        </form>
                      </React.Fragment>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}

NewPassword.propTypes = {
  t: PropTypes.func,
  history: PropTypes.object,
  setData: PropTypes.func,
  updateNewPassword: PropTypes.func,
  staffVerification: PropTypes.object,
  isChangePassword: PropTypes.bool,
  message: PropTypes.string,
  changeDetails: PropTypes.object,
};

const mapStateToProps = (state) => {
  return { message: selectMessage(state) };
};

export default connect(mapStateToProps, actions)(withRouter(withTranslation()(NewPassword)));
