import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import DeleteIcon from 'Assets/delete_icon_dark.svg';

const EditableRow = ({ data, type, handleRowValue, handleDeleteRow }) => {
  const [selectedRow, setSelectedRow] = useState(-1);
  return (
    <>
      {data.map((item, index) => {
        const editable = selectedRow === index;
        const lastRow = index === data.size - 1;
        return (
          <div
            key={index}
            className={`row no-gutters cursor-pointer ${!lastRow ? 'border-bottom' : ''}`}
            onClick={() => setSelectedRow(index)}
          >
            <div className={`col-6 f-14 ${!editable ? 'digi-pt-10 digi-pb-10' : 'pt-1'}`}>
              {editable ? (
                <MaterialInput
                  elementType={'materialInput'}
                  type={'text'}
                  variant={'outlined'}
                  value={item.get('labelName')}
                  changed={(e) => handleRowValue(e, 'labelName', type, index)}
                  maxLength={100}
                  size={'small'}
                />
              ) : (
                item.get('labelName')
              )}
            </div>
            <div className={`col-4 f-14 pl-3 ${!editable ? 'digi-pt-10 digi-pb-10' : 'pt-1'}`}>
              {editable ? (
                <MaterialInput
                  elementType={'materialInput'}
                  type={'number'}
                  variant={'outlined'}
                  value={String(item.get('days'))}
                  changed={(e) => handleRowValue(e, 'days', type, index)}
                  startAdornment={<span className="digi-light-gray mr-3">Day</span>}
                  size={'small'}
                />
              ) : (
                <>
                  <span className="digi-light-gray mr-3">Day</span>
                  {item.get('days')}
                </>
              )}
            </div>
            <div className={`col-2 f-14 align-self-center text-right`}>
              <img
                src={DeleteIcon}
                alt="Delete"
                className="remove_hover p-2"
                onClick={() => handleDeleteRow(type, index)}
              />
            </div>
          </div>
        );
      })}
    </>
  );
};

EditableRow.propTypes = {
  data: PropTypes.instanceOf(List),
  type: PropTypes.string,
  handleRowValue: PropTypes.func,
  handleDeleteRow: PropTypes.func,
};

export default EditableRow;
