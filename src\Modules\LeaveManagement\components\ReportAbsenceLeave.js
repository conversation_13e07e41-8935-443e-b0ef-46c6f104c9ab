import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import * as actions from '../../../_reduxapi/leave_management/actions';
import moment from 'moment';
import EmployeeModal from '../modal/EmployeeModal';
import ExportModal from '../modal/Exportmodal';
import {
  selectAllProgramLists,
  selectActiveInstitutionCalendar,
} from '../../../_reduxapi/Common/Selectors';
import {
  // selectActiveInstitutionCalendar,
  selectReviewerList,
} from '../../../_reduxapi/leave_management/selectors';
import { Dropdown, Button, Table } from 'react-bootstrap';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import Pagination from '../../StudentGrouping/components/Pagination';
import { getLang, getTimestamp } from '../../../utils';
import { t } from 'i18next';

var totalPages;
class ReportAbsenceLeave extends Component {
  constructor(props) {
    super(props);
    this.state = {
      completeView: true,
      inactiveView: false,
      ondutyView: true,
      reviewed: false,
      rejected: false,
      pending: false,
      review: false,
      leaveList: [],
      activeTab: 1,
      show: false,
      showExport: false,
      employee_id: null,
      searchText: '',
      pageCount: 10,
      currentPage: 1,
      sortBy: { type: '', orderBy: true },
      isInitialApiCalled: true,
    };
  }

  columnSorting = (a, b, name, type = '') => {
    const { sortBy } = this.state;
    if (type === '') {
      return sortBy.orderBy
        ? `${a.getIn(name)}` > `${b.getIn(name)}`
          ? 1
          : -1
        : `${b.getIn(name)}` > `${a.getIn(name)}`
        ? 1
        : -1;
    } else if (type === 'TIME') {
      return sortBy.orderBy
        ? getTimestamp(a.getIn(name)) - getTimestamp(b.getIn(name))
        : getTimestamp(b.getIn(name)) - getTimestamp(a.getIn(name));
    }
  };

  inactiveTab = () => {
    this.setState({
      completeView: false,
      inactiveView: true,
      ondutyView: false,
    });
  };
  ondutyTab = () => {
    this.setState({
      completeView: false,
      inactiveView: false,
      ondutyView: true,
    });
  };
  completeTab = () => {
    this.setState({
      completeView: true,
      inactiveView: false,
      ondutyView: false,
    });
  };

  exportShow = (status) => {
    this.setState({
      showExport: status,
    });
  };
  navigate = () => {
    this.props.history.push(`/leave-management/reportleave/${1}`);
  };

  componentDidMount() {
    this.interval = setInterval(() => {
      const { activeinstitutionCalendar } = this.props;
      if (activeinstitutionCalendar.get('_id', '') !== '' && this.state.isInitialApiCalled) {
        this.setState({ isInitialApiCalled: false, pending: true });
        const institution_id = activeinstitutionCalendar.get('_id');
        this.props.reviewer('report_absence', 'staff', institution_id);
        clearInterval(this.interval);
      }
    }, 500);
    this.props.setBreadCrumbName(t('leaveManagement.staff_report_absence_breadcrumb'));
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  componentDidUpdate(prevProps) {
    const { activeinstitutionCalendar, reviewer } = this.props;
    const institution_id = activeinstitutionCalendar.get('_id');
    if (
      prevProps.activeinstitutionCalendar.get('_id', '') !==
        activeinstitutionCalendar.get('_id', '') &&
      this.state.isInitialApiCalled === false &&
      activeinstitutionCalendar.get('_id', '') !== ''
    ) {
      reviewer('report_absence', 'staff', institution_id);
    }
  }

  handleChange = (e) => {
    this.setState({ searchText: e.target.value });
  };

  editleave = (id, institution_id) => {
    this.props.history.push(`/leave-management/reportleave/${id}?_id=${institution_id}`);
  };
  suspendleave = (id, institution_id) => {
    this.props.history.push(`/leave-management/suspendleave/${id}?_id=${institution_id}`);
  };
  rowClick = (data) => {
    const datalist = data?.toJS();
    const list = {
      ...datalist,
      flow: 'row',
    };
    this.props.setLeaveData(list);

    this.props.history.push(`/leave-management/suspendleave/${data.get('_id')}`);
  };

  employeeDetails = (status, data) => {
    this.setState({ show: status, employee_id: data });
  };

  onFullLastClick = () => {
    this.setState({
      currentPage: 1,
    });
  };

  onFullForwardClick = () => {
    this.setState({
      currentPage: totalPages,
    });
  };

  onBackClick = () => {
    if (this.state.currentPage - 1 === 0) {
      this.setState({
        currentPage: 1,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage - 1,
      });
    }
  };

  pagination = (value) => {
    this.setState({
      pageCount: value,
      currentPage: 1,
    });
  };

  onNextClick = () => {
    if (this.state.currentPage + 1 >= totalPages) {
      this.setState({
        currentPage: totalPages,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage + 1,
      });
    }
  };

  render() {
    const { reviewerList, activeinstitutionCalendar } = this.props;
    const { pending, reviewed, searchText, show, showExport, pageCount, sortBy } = this.state;
    const list = pending ? 'pending' : reviewed ? 'approved' : 'rejected';
    const institution_id = activeinstitutionCalendar.get('_id', '');

    totalPages =
      reviewerList && reviewerList?.size % pageCount === 0
        ? reviewerList?.size / pageCount
        : Math.floor(reviewerList?.size / pageCount) + 1;

    return (
      <>
        <div className="main pt-3 pb-5 bg-white">
          <div className="container">
            <div className="row pb-3">
              <div className="col-md-12">
                <div className="row">
                  <div className={`col-md-6 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                    <b className="mb-2 f-16"> {t('side_nav.menus.report_absence')}</b>
                    <br />
                    <span className="f-15 ">{t('leaveManagement.leave_application_pending')} </span>
                  </div>

                  <div className="col-md-5">
                    <div className="float-right">
                      {CheckPermission('pages', 'Leave Management', 'Report Absence', 'Add') && (
                        <Button variant="primary" className="f-14" onClick={this.navigate}>
                          {t('side_nav.menus.report_absence')}
                        </Button>
                      )}{' '}
                    </div>
                  </div>

                  <div className="col-md-1">
                    <div className="float-right">
                      {CheckPermission('pages', 'Leave Management', 'Report Absence', 'Export') && (
                        <Button variant="primary" className="f-14" onClick={this.exportShow}>
                          {t('program_calendar.export')}
                        </Button>
                      )}{' '}
                    </div>
                  </div>
                  <div className="col-md-6"></div>
                  <div className="col-md-6 mt-2">
                    <div className="sb-example-1">
                      {CheckPermission('pages', 'Leave Management', 'Report Absence', 'Search') && (
                        <div className="search">
                          <input
                            type="text"
                            className="searchTerm"
                            placeholder={t('leaveManagement.searchBy_id_name')}
                            onChange={(e) => this.handleChange(e)}
                          />
                          <button type="submit" className="searchButton">
                            <i className="fa fa-search"></i>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <div className="row pt-4">
              <div className="col-md-12">
             */}
            <div className="row pb-3">
              <div className="col-md-12">
                <div className="dash-table">
                  <Table responsive>
                    <thead className="group_table_top">
                      <tr>
                        {/* <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className="fa fa-align-center remove_hover pr-2"
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold">Employee ID</span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className="fa fa-align-center remove_hover pr-2"
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold">Employee Name</span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className="fa fa-align-center remove_hover pr-2"
                                aria-hidden="true"
                              >
                                <span className="pl-1 ">Program </span>
                              </i>
                            </li>
                          </div>
                        </th> */}
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className={`fa fa-sort-amount-${
                                  sortBy.type === 'EMPLOYEE_ID' && !sortBy.orderBy ? 'down' : 'up'
                                } remove_hover pr-2`}
                                onClick={() =>
                                  this.setState({
                                    sortBy: {
                                      type: 'EMPLOYEE_ID',
                                      orderBy: !this.state.sortBy.orderBy,
                                    },
                                  })
                                }
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('employee_id')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className={`fa fa-sort-amount-${
                                  sortBy.type === 'EMPLOYEE_NAME' && !sortBy.orderBy ? 'down' : 'up'
                                } remove_hover pr-2`}
                                onClick={() =>
                                  this.setState({
                                    sortBy: {
                                      type: 'EMPLOYEE_NAME',
                                      orderBy: !this.state.sortBy.orderBy,
                                    },
                                  })
                                }
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('userManagement.emp_name')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i className={`fa`} aria-hidden="true">
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('student_grouping.program')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i className={`fa`} aria-hidden="true">
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('type')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i className={`fa`} aria-hidden="true">
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('global_configuration.from')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i className={`fa`} aria-hidden="true">
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('global_configuration.to')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i className={`fa`} aria-hidden="true">
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('leaveManagement.Total_No_of_days')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className={`fa fa-sort-amount-${
                                  sortBy.type === 'TIME_STAMP' && !sortBy.orderBy ? 'down' : 'up'
                                } remove_hover pr-2`}
                                onClick={() =>
                                  this.setState({
                                    sortBy: {
                                      type: 'TIME_STAMP',
                                      orderBy: !this.state.sortBy.orderBy,
                                    },
                                  })
                                }
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('leaveManagement.Time_Stamp')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i className={`fa`} aria-hidden="true">
                                <span className="pl-1 font-weight-bold font-family-roboto ">
                                  {t('status')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue"></th>
                      </tr>
                    </thead>
                    <tbody>
                      {reviewerList &&
                        reviewerList.size > 0 &&
                        reviewerList
                          // eslint-disable-next-line
                          ?.sort((a, b) => {
                            if (sortBy.type === 'EMPLOYEE_ID')
                              return this.columnSorting(a, b, ['user_data', 'user_id'], '');
                            else if (sortBy.type === 'EMPLOYEE_NAME')
                              return this.columnSorting(a, b, ['user_data', 'name', 'first'], '');
                            else if (sortBy.type === 'TIME_STAMP')
                              return this.columnSorting(a, b, ['updatedAt'], 'TIME');
                          })
                          .filter(
                            (_, i) =>
                              i >= pageCount * (this.state.currentPage - 1) &&
                              i < pageCount * this.state.currentPage
                          )
                          .filter((item) =>
                            searchText !== ''
                              ? item
                                  .getIn(['user_data', 'user_id'], '')
                                  .toLowerCase()
                                  .search(searchText.toLowerCase()) > -1 ||
                                item
                                  .getIn(['user_data', 'name', 'first'], '')
                                  .toLowerCase()
                                  .search(searchText.toLowerCase()) > -1 ||
                                item
                                  .getIn(['user_data', 'name', 'middle'], '')
                                  .toLowerCase()
                                  .search(searchText.toLowerCase()) > -1 ||
                                item
                                  .getIn(['user_data', 'name', 'last'], '')
                                  .toLowerCase()
                                  .search(searchText.toLowerCase()) > -1 ||
                                item.getIn(['user_data', 'name', 'first'], '') +
                                  item.getIn(['user_data', 'name', 'middle'], '') +
                                  item
                                    .getIn(['user_data', 'name', 'last'], '')
                                    .toLowerCase()
                                    .search(searchText.toLowerCase()) >
                                  -1
                              : item
                          )
                          .map((data, i) => {
                            return (
                              <tr
                                key={i}
                                className="tr-bottom-border"
                                // onClick={(e) => {
                                //   this.rowClick(data);
                                // }}
                              >
                                <td
                                  className={
                                    CheckPermission(
                                      'pages',
                                      'Leave Management',
                                      'Report Absence',
                                      'Profile View'
                                    )
                                      ? 'text-blue remove_hover'
                                      : ''
                                  }
                                  onClick={
                                    CheckPermission(
                                      'pages',
                                      'Leave Management',
                                      'Report Absence',
                                      'Profile View'
                                    )
                                      ? (e) => {
                                          e.preventDefault();
                                          e.stopPropagation();
                                          this.employeeDetails(true, data.get('_user_id', ''));
                                        }
                                      : () => {}
                                  }
                                >
                                  <p className="mb-0">
                                    {data.getIn(['user_data', 'user_id'], '')}{' '}
                                  </p>
                                </td>
                                <td className="text-blue">
                                  <p className="mb-0">
                                    {data.getIn(['user_data', 'name', 'first'], '') +
                                      ' ' +
                                      data.getIn(['user_data', 'name', 'middle'], '') +
                                      ' ' +
                                      data.getIn(['user_data', 'name', 'last'], '')}{' '}
                                  </p>
                                </td>

                                <td className="text-blue">
                                  {data.getIn(['user_data', 'programs'], '')}{' '}
                                </td>

                                <td>
                                  <p className="mb-0">
                                    {' '}
                                    {data.get('is_noticed', false) === true
                                      ? 'Noticed'
                                      : 'Un-noticed'}
                                  </p>
                                </td>
                                <td>
                                  <p className="mb-0">
                                    {' '}
                                    {moment(data.get('from')).format('DD MMM	YYYY')}
                                  </p>
                                </td>
                                <td>
                                  <p className="mb-0">
                                    {' '}
                                    {moment(data.get('to')).format('DD MMM	YYYY')}
                                  </p>
                                </td>
                                <td>
                                  <p className="mb-0"> {data.get('days', 0)}</p>
                                </td>
                                <td>
                                  <p className=" mb-0">
                                    {' '}
                                    {moment(data.get('updatedAt')).format('DD MMM	YYYY hh:mm A')}
                                  </p>
                                </td>
                                <td>
                                  <p className=" mb-0">
                                    {' '}
                                    {data.get('isActive', false) === false
                                      ? 'Suspended'
                                      : 'Applied'}
                                  </p>
                                </td>
                                <td>
                                  <div className="aw-50">
                                    {(CheckPermission(
                                      'pages',
                                      'Leave Management',
                                      'Report Absence',
                                      'Edit'
                                    ) ||
                                      CheckPermission(
                                        'pages',
                                        'Leave Management',
                                        'Report Absence',
                                        'Suspend'
                                      )) &&
                                      data.get('isActive', false) === true && (
                                        <div className="f-18">
                                          <Dropdown>
                                            <Dropdown.Toggle
                                              variant=""
                                              id="dropdown-table"
                                              className="table-dropdown"
                                              size="sm"
                                            >
                                              <div>
                                                <i
                                                  className="fa fa-ellipsis-v"
                                                  aria-hidden="true"
                                                ></i>
                                              </div>
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu className="dropdown-flex">
                                              {CheckPermission(
                                                'pages',
                                                'Leave Management',
                                                'Report Absence',
                                                'Edit'
                                              ) &&
                                                data.get('isActive', false) !== false && (
                                                  <Dropdown.Item
                                                    onClick={() =>
                                                      this.editleave(
                                                        data.get('_id'),
                                                        institution_id
                                                      )
                                                    }
                                                    // disabled={this.disableEditOrCancel(leave, 'edit')}
                                                  >
                                                    {t('role_management.role_actions.View')}
                                                  </Dropdown.Item>
                                                )}
                                              {CheckPermission(
                                                'pages',
                                                'Leave Management',
                                                'Report Absence',
                                                'Suspend'
                                              ) &&
                                                data.get('isActive', false) !== false && (
                                                  <Dropdown.Item
                                                    onClick={() =>
                                                      this.suspendleave(
                                                        data.get('_id'),
                                                        institution_id
                                                      )
                                                    }
                                                    // disabled={this.disableEditOrCancel(leave, 'cancel')}
                                                  >
                                                    {t('role_management.role_actions.Suspend')}
                                                  </Dropdown.Item>
                                                )}
                                            </Dropdown.Menu>
                                          </Dropdown>
                                        </div>
                                      )}
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                    </tbody>
                  </Table>
                </div>
              </div>
            </div>

            {reviewerList && reviewerList.size > 0 && (
              <Pagination
                pagination={this.pagination}
                onNextClick={this.onNextClick}
                pagevalue={this.state.pageCount}
                onBackClick={this.onBackClick}
                onFullLastClick={this.onFullLastClick}
                onFullForwardClick={this.onFullForwardClick}
                data={totalPages}
                currentPage={this.state.currentPage}
              />
            )}
          </div>

          {show && (
            <EmployeeModal
              closed={this.employeeDetails.bind(this)}
              dataId={this.state.employee_id}
            />
          )}
          {showExport && (
            <ExportModal
              closed={this.exportShow.bind(this)}
              data={reviewerList}
              status={list}
              leavetype={'report_absence'}
            />
          )}
        </div>
      </>
    );
  }
}

ReportAbsenceLeave.propTypes = {
  history: PropTypes.object,
  reviewer: PropTypes.func,
  setBreadCrumbName: PropTypes.func,
  setLeaveData: PropTypes.func,
  activeinstitutionCalendar: PropTypes.instanceOf(Map),
  reviewerList: PropTypes.instanceOf(List),
};

const mapStateToProps = (state) => {
  return {
    activeinstitutionCalendar: selectActiveInstitutionCalendar(state),
    reviewerList: selectReviewerList(state),
    allprogramList: selectAllProgramLists(state),
  };
};
export default compose(withRouter, connect(mapStateToProps, actions))(ReportAbsenceLeave);
