import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { addExternalStaff, setData } from '_reduxapi/course_scheduling/action';
import { List, Map, fromJS } from 'immutable';
import { Checkbox, Divider, InputAdornment, OutlinedInput } from '@mui/material';
import FormControl from '@mui/material/FormControl';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import DoneIcon from '@mui/icons-material/Done';
import CloseIcon from '@mui/icons-material/Close';
import MaterialInput from 'Widgets/FormElements/material/Input';
import useCountry from 'Hooks/useCountryCodeHook';
import { selectExternalStaff } from '_reduxapi/course_scheduling/selectors';

function ExternalStaff({
  programId,
  courseId,
  institutionCalendarId,
  checkExternalDatas,
  setModalData,
  setDataMsg,
}) {
  const [isAddButtonDisabled, setAddButtonDisabled] = useState(false);
  const [items, setItems] = useState(Map());
  const [searchQuery, setSearchQuery] = useState('');

  const dispatch = useDispatch();

  const handleAddItem = () => {
    if (items.size === 0) {
      setItems(Map({ name: '', email: '', mobileNo: '', showInput: false, createdAt: new Date() }));
      setAddButtonDisabled(true);
    }
  };

  const handleInputChange = ({ e, key, index, mode }) => {
    const value = e.target.value;
    const updateData = (key, value) => {
      if (mode === 'update') {
        setModalData((pre) =>
          pre.updateIn(['schedule', 'checkExternalList'], (data) => data.setIn([index, key], value))
        );
      } else {
        setItems(items.set(key, value));
      }
    };

    if (key === 'name') {
      const regex = /^[a-zA-Z\s]*$/;
      if (regex.test(value)) {
        updateData(key, value);
      }
    } else if (key === 'email' || key === 'mobileNo') {
      updateData(key, value);
    }
  };

  const handleChangeChecked = ({ e, key, index }) => {
    setModalData((pre) =>
      pre.setIn(
        ['schedule', 'checkExternalList'],
        checkExternalDatas.setIn([index, key], e.target.checked)
      )
    );
  };
  const { countryCodeLength, countryCode } = useCountry();
  const handleSave = ({ index, mode, resultsId }) => {
    if (
      mode === 'update'
        ? checkExternalDatas.getIn([index, 'name'], '').trim() === ''
        : items.get('name').trim() === '' || mode === 'update'
        ? checkExternalDatas.getIn([index, 'email'], '').trim() === ''
        : items.get('email').trim() === '' || mode === 'update'
        ? checkExternalDatas.getIn([index, 'mobileNo'], '').trim() === ''
        : items.get('mobileNo', '').trim() === ''
    ) {
      setDataMsg({ message: 'Input cannot be empty' });
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (
      !emailRegex.test(
        mode === 'update' ? checkExternalDatas.getIn([index, 'email'], '') : items.get('email', '')
      )
    ) {
      setDataMsg({ message: 'Invalid email format' });
      return;
    }

    const expectedNumberSize =
      mode === 'update'
        ? checkExternalDatas.getIn([index, 'mobileNo'], '')
        : items.get('mobileNo', '');

    if (countryCodeLength !== expectedNumberSize.length) {
      setDataMsg({ message: 'Invalid number for country code' });
      return;
    }

    const requestBody = {
      ...(mode === 'create' && {
        _program_id: programId,
        _course_id: courseId,
        _institution_calendar_id: institutionCalendarId,
      }),
      name: mode === 'update' ? checkExternalDatas.getIn([index, 'name'], '') : items.get('name'),
      mobileNo:
        mode === 'update'
          ? checkExternalDatas.getIn([index, 'mobileNo'], '')
          : items.get('mobileNo', ''),
      email:
        mode === 'update' ? checkExternalDatas.getIn([index, 'email'], '') : items.get('email'),
      ...(mode === 'update' && { _id: resultsId }),
    };

    const callBack = (resultId) => {
      setItems(Map());
      setModalData((prev) => {
        if (mode === 'create') {
          const currentDateString = new Date().toISOString();
          const immutableRequestBody = fromJS(requestBody);
          const updatedRequestBody = immutableRequestBody
            .set('createdAt', currentDateString)
            .set('_id', resultId);
          return prev.updateIn(['schedule', 'checkExternalList'], (checkExternalList) =>
            checkExternalList.push(updatedRequestBody)
          );
        }
        return prev.setIn(
          ['schedule', 'checkExternalList'],
          checkExternalDatas.setIn([index, 'showInput'], false)
        );
      });

      setAddButtonDisabled(false);
    };

    dispatch(addExternalStaff({ mode, requestBody, callBack }));
  };

  const externalStaff = useSelector(selectExternalStaff);
  const handleCloseEdit = ({ mode, resultsId, index }) => {
    const invalid = ['', null, undefined];
    if (mode === 'delete') {
      dispatch(setData(Map({ externalStaff: externalStaff.slice(index + 1) })));
    }
    const callBack = () => {
      setModalData((prev) => {
        if (!invalid.includes(index) && mode === 'delete') {
          return prev.updateIn(['schedule', 'checkExternalList'], (checkExternalList) => {
            return checkExternalList.delete(index);
          });
        }
        return prev;
      });
    };

    dispatch(addExternalStaff({ mode, resultsId, callBack }));
  };

  const handleClose = (index) => {
    setItems(Map());
    setAddButtonDisabled(false);
    setModalData((pre) =>
      pre.setIn(
        ['schedule', 'checkExternalList'],
        checkExternalDatas.setIn([index, 'showInput'], false)
      )
    );
    // setSelectedCountry(null);
  };

  const handleEdit = (index) => {
    setModalData((pre) =>
      pre.setIn(
        ['schedule', 'checkExternalList'],
        checkExternalDatas.setIn([index, 'showInput'], true)
      )
    );
  };

  const newUser = List(checkExternalDatas);
  let newUserData = List();
  let existingUser = List();
  const currentDateString = new Date().toISOString().slice(0, 10);

  newUser.forEach((userData, index) => {
    if (searchQuery === '' || userData.get('name', '').toLowerCase().includes(searchQuery)) {
      if (userData.size !== 0) {
        const createdAt = userData.get('createdAt', '');
        const createdAtDate = new Date(createdAt);
        if (!isNaN(createdAtDate.getTime())) {
          const createdAtDateString = createdAtDate.toISOString().slice(0, 10);
          if (currentDateString === createdAtDateString) {
            newUserData = newUserData.push(userData.set('_index_id', index));
          } else {
            existingUser = existingUser.push(userData.set('_index_id', index));
          }
        }
      }
    }
  });

  const handleSearchData = (event) => {
    const query = event.target.value.toLowerCase();
    setSearchQuery(query);
  };

  return (
    <>
      <MaterialInput
        elementType={'materialSearch'}
        placeholder={'Search'}
        id={'#rowReverse'}
        labelclass={'searchRight'}
        className="remove_hover mb-1"
        changed={handleSearchData}
        value={searchQuery}
      />
      <div>
        <div className="border-Dash mb-1">
          <div
            onClick={handleAddItem}
            className={`f-13 d-flex p-2 align-items-center justify-content-center ds-pointer text-primary ${
              isAddButtonDisabled ? 'disabled' : ''
            }`}
          >
            ADD NEW USER
          </div>
        </div>
        <div>
          {newUserData?.size > 0 && <div className="f-14 mb-1">New User</div>}
          {isAddButtonDisabled ? (
            <>
              <div className="d-flex align-items-center justify-content-center ">
                <FormControl sx={{ width: 300 }} className="mr-3">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={`Full Name`}
                    value={items.get('name', '')}
                    changed={(e) => handleInputChange({ e, key: 'name', mode: 'create' })}
                  />
                </FormControl>
                <FormControl sx={{ width: 400 }} className="mr-3">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={`Email`}
                    value={items.get('email', '')}
                    changed={(e) => handleInputChange({ e, key: 'email', mode: 'create' })}
                  />
                </FormControl>
                <FormControl sx={{ width: '36em' }} className="mr-3 mb-2 ">
                  <OutlinedInput
                    variant="outlined"
                    size="small"
                    type="number"
                    placeholder="Mobile No"
                    value={items.get('mobileNo')}
                    // sx={outlineInputStyleMobileNo}
                    startAdornment={
                      <InputAdornment position="start">+{countryCode}</InputAdornment>
                    }
                    onChange={(e) => handleInputChange({ e, key: 'mobileNo', mode: 'create' })}
                  />
                </FormControl>
                <DoneIcon
                  color="primary"
                  className="mb-2 mr-2 ds-pointer"
                  onClick={() => handleSave({ index: '-1', mode: 'create' })}
                />
                <CloseIcon
                  className="mb-2 ds-pointer"
                  onClick={() => handleClose()}
                  sx={{ color: '#f21f26' }}
                />
              </div>
            </>
          ) : (
            ''
          )}
          {newUserData.map((result) => {
            const resultsId = result.get('_id', '');
            const index = result.get('_index_id', '');

            return (
              <>
                {!result.get('showInput', false) ? (
                  <>
                    <div className="d-flex align-items-center px-3 py-2 mt-3" key={index}>
                      <div className="pr-2">
                        <Checkbox
                          checked={result.get('isChecked', '')}
                          onChange={(e) =>
                            handleChangeChecked({
                              e,
                              index,
                              key: 'isChecked',
                            })
                          }
                          className="p-1"
                        />
                      </div>
                      <div className="pt-1">
                        <div className="f-14">{result.get('name', '')}</div>
                        <div className="d-flex">
                          <div className="f-14">Email: {result.get('email', '')}</div>
                          <Divider
                            sx={{ '&.MuiDivider-root': { borderColor: '#4B5563' } }}
                            orientation="vertical"
                            flexItem
                            className="mx-2 my-1"
                          />
                          <div className="f-14">Mobile No: {result.get('mobileNo', '')}</div>
                        </div>
                      </div>
                      <div className="ml-auto">
                        <div className="d-flex align-items-center">
                          <EditIcon
                            fontSize="small"
                            className="mr-3 ds-pointer"
                            color="disabled"
                            onClick={() => handleEdit(index)}
                            sx={{ color: 'disabled', '&:hover': { color: 'blue' } }}
                          />
                          <DeleteIcon
                            fontSize="small"
                            color="action"
                            className="ds-pointer"
                            sx={{ color: 'action', '&:hover': { color: 'red' } }}
                            onClick={() =>
                              handleCloseEdit({
                                mode: 'delete',
                                resultsId,
                                index,
                              })
                            }
                          />
                        </div>
                      </div>
                    </div>
                    <Divider />
                  </>
                ) : (
                  <div
                    className="d-flex align-items-center justify-content-center px-3 py-2 mt-3"
                    key={index}
                  >
                    <FormControl sx={{ width: 300 }} className="mr-3">
                      <MaterialInput
                        elementType={'materialInput'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        placeholder={`Full Name`}
                        value={result.get('name', '')}
                        changed={(e) =>
                          handleInputChange({
                            e,
                            index,
                            key: 'name',
                            mode: 'update',
                          })
                        }
                      />
                    </FormControl>
                    <FormControl sx={{ width: 400 }} className="mr-3">
                      <MaterialInput
                        elementType={'materialInput'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        placeholder={`Email`}
                        value={result.get('email', '')}
                        changed={(e) =>
                          handleInputChange({
                            e,
                            index,
                            key: 'email',
                            mode: 'update',
                          })
                        }
                      />
                    </FormControl>
                    <FormControl sx={{ width: '36em' }} className="mr-3 mb-2 ">
                      <OutlinedInput
                        variant="outlined"
                        size="small"
                        type="number"
                        placeholder="Mobile No"
                        value={result.get('mobileNo', '')}
                        // sx={outlineInputStyleMobileNo}
                        startAdornment={
                          <InputAdornment position="start">{countryCode}</InputAdornment>
                        }
                        onChange={(e) =>
                          handleInputChange({
                            e,
                            index,
                            key: 'mobileNo',
                            mode: 'update',
                          })
                        }
                      />
                    </FormControl>
                    <DoneIcon
                      color="disabled"
                      className="mb-2 mr-2 ds-pointer"
                      onClick={() => handleSave({ index, mode: 'update', resultsId })}
                      sx={{ color: 'disabled', '&:hover': { color: 'blue' } }}
                    />
                    <CloseIcon
                      color="action"
                      className="mb-2 ds-pointer"
                      onClick={() => handleClose(index)}
                      sx={{ color: 'action', '&:hover': { color: 'red' } }}
                    />
                  </div>
                )}
              </>
            );
          })}
          {existingUser?.size > 0 && <div className="f-14 mt-3 mb-2">Existing User</div>}
          {existingUser.map((result) => {
            const resultsId = result.get('_id', '');
            const index = result.get('_index_id', '');
            return (
              <>
                {!result.get('showInput', false) ? (
                  <>
                    <div className="d-flex align-items-center px-3 py-2 mt-3" key={index}>
                      <div className="pr-2">
                        <Checkbox
                          checked={result.get('isChecked', '')}
                          onChange={(e) =>
                            handleChangeChecked({
                              e,
                              index,
                              key: 'isChecked',
                            })
                          }
                          className="p-1"
                        />
                      </div>
                      <div className="pt-1">
                        <div className="f-14">{result.get('name', '')}</div>
                        <div className="d-flex">
                          <div className="f-14">Email: {result.get('email', '')}</div>
                          <Divider
                            sx={{ '&.MuiDivider-root': { borderColor: '#4B5563' } }}
                            orientation="vertical"
                            flexItem
                            className="mx-2 my-1"
                          />
                          <div className="f-14">Mobile No: {result.get('mobileNo', '')}</div>
                        </div>
                      </div>
                      <div className="ml-auto">
                        <div className="d-flex align-items-center">
                          <EditIcon
                            fontSize="small"
                            className="mr-3 ds-pointer"
                            color="disabled"
                            onClick={() => handleEdit(index)}
                            sx={{ color: 'disabled', '&:hover': { color: 'blue' } }}
                          />
                          <DeleteIcon
                            fontSize="small"
                            color="action"
                            className="ds-pointer"
                            sx={{ color: 'action', '&:hover': { color: 'red' } }}
                            onClick={() => handleCloseEdit({ mode: 'delete', resultsId, index })}
                          />
                        </div>
                      </div>
                    </div>
                    <Divider />
                  </>
                ) : (
                  <>
                    <div
                      className="d-flex align-items-center justify-content-center px-3 py-2 mt-3"
                      key={index}
                    >
                      <FormControl sx={{ width: 300 }} className="mr-3">
                        <MaterialInput
                          elementType={'materialInput'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          placeholder={`Full Name`}
                          value={result.get('name', '')}
                          changed={(e) =>
                            handleInputChange({
                              e,
                              index,
                              key: 'name',
                              mode: 'update',
                            })
                          }
                        />
                      </FormControl>
                      <FormControl sx={{ width: 400 }} className="mr-3">
                        <MaterialInput
                          elementType={'materialInput'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          placeholder={`Email`}
                          value={result.get('email', '')}
                          changed={(e) =>
                            handleInputChange({
                              e,
                              index,
                              key: 'email',
                              mode: 'update',
                            })
                          }
                        />
                      </FormControl>
                      {/* <FormControl sx={{ width: 400 }} className="mr-3">
                        <MaterialInput
                          elementType={'materialInput'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          placeholder={`Mobile No`}
                          value={result.get('mobileNo', '')}
                          changed={(e) =>
                            handleInputChange({
                              e,
                              index,
                              key: 'mobileNo',
                              mode: 'update',
                            })
                          }
                        />
                      </FormControl> */}
                      <FormControl sx={{ width: '36em' }} className="mr-3 mb-2 ">
                        <OutlinedInput
                          variant="outlined"
                          size="small"
                          type="number"
                          placeholder="Mobile No"
                          value={result.get('mobileNo', '')}
                          startAdornment={
                            <InputAdornment position="start">+{countryCode}</InputAdornment>
                          }
                          onChange={(e) =>
                            handleInputChange({
                              e,
                              index,
                              key: 'mobileNo',
                              mode: 'update',
                            })
                          }
                        />
                      </FormControl>
                      <DoneIcon
                        color="disabled"
                        className="mb-2 mr-2 ds-pointer"
                        onClick={() => handleSave({ index, mode: 'update', resultsId })}
                        sx={{ color: 'disabled', '&:hover': { color: 'blue' } }}
                      />
                      <CloseIcon
                        color="action"
                        className="mb-2 ds-pointer"
                        onClick={() => handleClose(index)}
                        sx={{ color: 'action', '&:hover': { color: 'red' } }}
                      />
                    </div>
                    <Divider />
                  </>
                )}
              </>
            );
          })}
        </div>
      </div>

      {/* {isModuleEnabled('OUTSIDE_CAMPUS') && (
    <Fragment>
      {data.getIn(['schedule', 'outsideCampus'], false) ? (
        <div className="pt-2 pb-2">
          <div className="">
            <div className="f-16">
              <MaterialInput
                elementType={'materialSelect'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                sx={withoutOutlineSelect}
                elementConfig={{ options: programType }}
                changed={(e) => handleChanges('selectNumOrEmail', e.target.value)}
                value={data.getIn(['schedule', 'selectNumOrEmail'], '')}
              />
            </div>
          </div>
          <div className="">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              changed={(e) =>
                handleChanges('mobileNumberOrEmail', e.target.value)
              }
              value={data.getIn(['schedule', 'mobileNumberOrEmail'], '')}
            />
          </div>
        </div>
      ) : (
        ''
      )}
    </Fragment>
  )} */}
    </>
  );
}

ExternalStaff.propTypes = {
  programId: PropTypes.string,
  courseId: PropTypes.string,
  institutionCalendarId: PropTypes.string,
  checkExternalDatas: PropTypes.instanceOf(List),
  setModalData: PropTypes.func,
  scheduleId: PropTypes.string,
  checkUpdateExternal: PropTypes.instanceOf(List),
  data: PropTypes.instanceOf(Map),
  checkDataUpdateExternal: PropTypes.instanceOf(List),
  setDataMsg: PropTypes.func,
};
export default ExternalStaff;
