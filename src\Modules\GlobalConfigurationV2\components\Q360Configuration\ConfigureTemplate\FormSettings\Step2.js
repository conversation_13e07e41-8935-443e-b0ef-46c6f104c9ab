import React, { useEffect, useState, useRef, forwardRef } from 'react';
import { useSelector } from 'react-redux';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { List, Map, fromJS } from 'immutable';
import image from 'Assets/q360_dashboard/doc_view/image.svg';
import pdf from 'Assets/q360_dashboard/doc_view/pdf.svg';
import video from 'Assets/q360_dashboard/doc_view/video.svg';
// import left_move from 'Assets/q360_dashboard/doc_view/left_move.svg';
// import right_move from 'Assets/q360_dashboard/doc_view/right_move.svg';
import KeyboardArrowLeftRoundedIcon from '@mui/icons-material/KeyboardArrowLeftRounded';
import ChevronRightRoundedIcon from '@mui/icons-material/ChevronRightRounded';
import CloseIcon from '@mui/icons-material/Close';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import DownloadIcon from '@mui/icons-material/Download';
import {
  // uploadAttachment,
  // getUpload,
  updateCategoryForm,
  multipleFileUpload,
  getStepTwoDetails,
  getSignedUrl,
} from '_reduxapi/q360/actions';
// import DialogModal from 'Widgets/FormElements/material/DialogModal';
import { Divider, Drawer, Paper, TextField } from '@mui/material';
import InsertPhotoIcon from '@mui/icons-material/InsertPhoto';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import {
  // selectedSignedUrl,
  // selectedGetUpload,
  selectStepTwoDetails,
} from '_reduxapi/q360/selectors';
import { useSearchParams } from '../..';
import VideoCameraBackIcon from '@mui/icons-material/VideoCameraBack';
import {
  // useAutoSave,
  // useAutoSaveWithCheckChanges,
  useCallApiHook,
  useConfigureTemplate,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { setData } from '_reduxapi/qapc/actions';
import Buttons from 'Widgets/FormElements/material/Button';
import CkEditor5 from 'Widgets/CkEditor/CkEditor';
import { alphabetArray } from 'Modules/GlobalConfigurationV2/utils/jsUtils';
import { Delete } from '@mui/icons-material';
import { PUBLIC_PATH } from 'constants';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import LocalStorageService from 'LocalStorageService';
//----------------------------------UI Utils Start--------------------------------------------------
const fileUploadSX = {
  fill: '#147AFC',
};
const paperSx = {
  width: 30,
  height: 30,
  borderRadius: '50%',
  padding: '3px',
  right: -15,
  bottom: -27,
  zIndex: 2,
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const fileTypes = 'PDF, JPG, Video, IMG... up to 250MB';
export function getSections(iframeRef) {
  if (iframeRef.current) {
    const document = iframeRef.current.contentWindow.document;
    return Array.from(document.querySelectorAll('.table-of-contents td'))
      .filter((data) => data.className === 'p-4 pl-12')
      .map((data) => data.textContent);
  }
  return [];
}
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------
//----------------------------------custom hooks end------------------------------------------------
//----------------------------------componentStart--------------------------------------------------
const UploadFileCard = ({ children, fileTypes, onClick }) => {
  return (
    <label
      htmlFor="file-upload"
      className="d-inline-flex flex-row-reverse flex-wrap borderDashed cursor-pointer rounded"
      onClick={onClick}
    >
      {children && children}
      <div className="d-flex flex-column justify-content-center align-items-center px-4 py-2 bg_newTableGray flex-grow-1 rounded-right">
        <FileUploadIcon sx={fileUploadSX} />
        <div className="text-blue f-14">Upload</div>
      </div>
      <div className="flex-grow-1 px-4 py-3 bg-white border-right rounded-left">
        <div className="f-14">Upload file</div>
        <div className="f-10 text-cGrey font-weight-normal">{fileTypes}</div>
      </div>
    </label>
  );
};
const FileUploadInput = ({ onChange }) => {
  return <input multiple type="file" id="file-upload" className="d-none" onChange={onChange} />;
};
const UploadFileListComponent = ({ filesData, formName, setFilesAttachment, isEditable }) => {
  const [openModalIndex, setOpenModalIndex] = useState(null);
  const handleDelete = (e, index) => {
    e.stopPropagation();
    const updatedFiles = filesData.filter((_, i) => i !== index);
    setFilesAttachment(updatedFiles);
  };
  const miniumViewFiles = filesData.slice(0, 4);
  return (
    <>
      <div className="d-flex align-items-baseline ">
        <div className="d-flex align-items-center gap-10 ProgressBar">
          {miniumViewFiles.map((eachFile, index) => (
            <Paper
              key={index}
              className="d-flex align-items-center px-3 py-2 cursor-pointer"
              onClick={() => setOpenModalIndex(index)}
            >
              {eachFile.get('url', '').includes('.pdf') ? (
                <PictureAsPdfIcon fontSize="small" className="mr-2" />
              ) : eachFile.get('url', '').includes('.mp4') ? (
                <VideoCameraBackIcon fontSize="small" className="mr-2" />
              ) : (
                <InsertPhotoIcon fontSize="small" className="mr-2" />
              )}
              <div className={'f-14 text-mGrey'}>
                {getShortString(eachFile.get('name', ''), 20)}
              </div>
              <EnableOrDisable valid={!isEditable}>
                <CloseIcon
                  fontSize="small"
                  className="ml-3 text-lGrey"
                  onClick={(e) => handleDelete(e, index)}
                />
              </EnableOrDisable>
            </Paper>
          ))}
          {filesData.size > 4 && (
            <div className="doc_view_attach ml-3">
              <>
                <AttachFileIcon sx={{ fontSize: '18px', color: 'black' }} />
                {filesData.size}
              </>
              <DrawerRight
                filesData={filesData}
                formName={formName}
                setFilesAttachment={setFilesAttachment}
              >
                <span className="text-blue remove_hover">View</span>
              </DrawerRight>
            </div>
          )}
        </div>
      </div>
      {/* <UploadModal
        file={filesData.get(openModalIndex, Map())}
        open={openModalIndex !== null}
        handleClose={() => setOpenModalIndex(null)}
      /> */}
      <FileViewAtDrawer
        file={filesData.get(openModalIndex, Map())}
        open={openModalIndex !== null}
        // formName={formName}
        index={openModalIndex}
        filesData={filesData}
        setOpenModalIndex={setOpenModalIndex}
        handleClose={() => setOpenModalIndex(null)}
      />
    </>
  );
};
// export const UploadModal = ({ file, open, handleClose }) => { // this is future development, may requirment change by the QA
//   const [getUrl] = useCallApiHook(getSignedUrl);
//   const isPdf = file.get('url', '').includes('.pdf');
//   const [signedUrl, setSignedUrl] = useState('');
//   const isVideo = file.get('url', '').includes('.mp4');
//   const url = file.get('url', '');
//   useEffect(() => {
//     url && getUrl(url, (url) => setSignedUrl(url));
//   }, [url]);
//   return (
//     <DialogModal maxWidth={'xl'} fullWidth={true} show={open} onClose={handleClose}>
//       <>
//         <div className="m-3">
//           <div>
//             {isPdf ? (
//               <iframe src={signedUrl} title="pdf" className="d-flex w-100 ht-40vh"></iframe>
//             ) : isVideo ? (
//               <embed type="video/webm" width="100%" height="350" src={signedUrl}></embed>
//             ) : (
//               <div className="img_overflow_scroll_sub">
//                 <img src={signedUrl} alt="" width="100%" height="40vh" className="p-0 img-fluid" />
//               </div>
//             )}
//           </div>
//           <div className="d-flex w-100">
//             <div className="ml-auto">
//               <Button
//                 onClick={() => {
//                   handleClose(null);
//                   setSignedUrl('');
//                 }}
//                 className="mx-2"
//               >
//                 Close
//               </Button>
//             </div>
//           </div>
//         </div>
//       </>
//     </DialogModal>
//   );
// };
//----------------------------------componentEnd----------------------------------------------------

// const Step2 = () => {
//   const categoryFormId = useCurrentPage('categoryFormId');
//   const [filesAttachment, setFilesAttachment] = useState(List());
//   const getUploadData = useSelector(selectedGetUpload);
//   const currentRef = useRef({ boolean: { isEdited: false } });
//   const [UploadApi] = useCallApiHook(getUpload);
//   const [UploadAttachmentApi] = useCallApiHook(uploadAttachment);
//   const [setDataApi] = useCallApiHook(setData);

//   const callBack = (newFormsWithUrl) => {
//     setFilesAttachment((prev) => prev.merge(newFormsWithUrl));
//   };
//   const handleInputChange = (e) => {
//     const files = e.target.files;
//     const filesValidate = e.target.files[0];
//     const type = filesValidate.type.split('/')[1];
//     const filesValues = e.target.files[0].name;
//     const isDuplicates = filesAttachment.some((val) => val.get('name', '').includes(filesValues));
//     if (!['pdf', 'jpeg', 'jpg', 'png', 'mp4'].includes(type)) {
//       return setDataApi({ message: 'Upload only pdf, jpeg, jpg, png, video' });
//     }
//     if (filesValidate.size > 250 * 1024 * 1024) {
//       return setDataApi({ message: 'File size exceeds the limit of 250MB' });
//     }
//     if (isDuplicates) {
//       return setDataApi({ message: 'Attachment already exists' });
//     }
//     if (filesAttachment.size > 4) {
//       return setDataApi({ message: 'You can only upload a maximum of five attachments.' });
//     }
//     const convertFormData = new FormData();
//     convertFormData.append('file', files);
//     multipleFileUpload(convertFormData, callBack);
//     e.target.value = null;
//     currentRef.current.boolean = { isEdited: true };
//   };
//   useAutoSave(updateCategoryForm, currentRef);
//   const iframeRef = useRef();
//   const sendMessageToIframe = () => {
//     if (iframeRef.current) {
//       iframeRef.current.contentWindow.postMessage({ values: [], from: 'fromDC' }, '*');
//       LocalStorageService.setCustomToken('sections', JSON.stringify(getSections(iframeRef)));
//     }
//   };
//   const handleIframeLoad = () => {
//     sendMessageToIframe();
//   };
//   useEffect(() => UploadApi(categoryFormId, callBack), []);
//   useEffect(() => {
//     currentRef.current.data = {
//       attachments: filesAttachment.map((data) => ({
//         url: data.get('url', ''),
//         name: data.get('name', ''),
//       })),
//       categoryFormId,
//     };
//   }, [filesAttachment]);
//   return (
//     <div className="w-100 h_100vh">
//       <div className="mb-1 f-14">Attached Reference Files</div>
//       <UploadFileCard fileTypes={fileTypes}>
//         <FileUploadInput onChange={handleInputChange} />
//       </UploadFileCard>
//       {!!filesAttachment.size && (
//         <UploadFileListComponent
//           filesData={filesAttachment}
//           setFilesAttachment={setFilesAttachment}
//         />
//       )}
//       <iframe
//         ref={iframeRef}
//         src={'/course-spec.html'}
//         title="Face Anomaly Report"
//         width="100%"
//         height="100%"
//         style={{ border: 'none', overflow: 'auto' }}
//         onLoad={handleIframeLoad}
//         sandbox="allow-same-origin"
//       ></iframe>
//     </div>
//   );
// };

const inititalData = Map();
const SectionWithEditor = forwardRef(
  ({ index, reduxSectionDetails, handleSectionDelete, formType, sectionName, isPublish }, ref) => {
    const [state, setState] = useState(
      Map({
        sectionName: '',
        description: '',
      })
    );
    ref.current = ref.current.set(index, state);
    function editorHandleChange(editor) {
      const editorData = editor.getData();
      setState((prev) => prev.set('description', editorData));
    }
    useEffect(() => setState(reduxSectionDetails.get(index, Map())), [reduxSectionDetails]);
    return (
      <Paper elevation={1} className="p-2 mt-3">
        <div className="d-flex">
          <p className="mt-1 mb-2 ">{sectionName}</p>
          <EnableOrDisable valid={!isPublish}>
            {index !== 0 ? (
              <CloseIcon onClick={handleSectionDelete(index)} className="ml-auto" />
            ) : (
              <></>
            )}
          </EnableOrDisable>
        </div>
        <TextField
          fullWidth
          className="mb-3"
          disabled={isPublish}
          value={state.get('sectionName', '')}
          onChange={(event) => setState((prev) => prev.set('sectionName', event.target.value))}
          sx={{
            '& .MuiInputBase-input': {
              // '&.Mui-focused fieldset': {
              //   borderColor: '#147AFC',
              // },
              padding: '4px 14px',
            },
          }}
        />
        <CkEditor5
          isShowPremiumFeatures
          data={state.get('description', '')}
          onChange={editorHandleChange}
          isReadOnly={isPublish}
        />
      </Paper>
    );
  }
);
const SectionWithEditorComplete = forwardRef(
  ({ index, reduxSectionDetails, handleSectionDelete, formType, sectionName, isPublish }, ref) => {
    const [state, setState] = useState(
      Map({
        sectionName: 'section',
        description: '',
      })
    );
    ref.current = ref.current.set(index, state);
    function editorHandleChange(editor) {
      const editorData = editor.getData();
      setState((prev) => prev.set('description', editorData));
    }

    useEffect(() => setState(reduxSectionDetails.get(index, Map())), [reduxSectionDetails]);
    return (
      <Paper elevation={1} className="mt-3" sx={{ minHeight: '500px' }}>
        <div className="f-24 fw-400 text-dGrey border-bottom px-2">
          <p className="p-3">{sectionName}</p>
        </div>
        <div className={'p-2 mt-3'}>
          <CkEditor5
            isShowPremiumFeatures
            data={state.get('description', '')}
            onChange={editorHandleChange}
            isReadOnly={isPublish}
          />
        </div>
      </Paper>
    );
  }
);

const Step2New = forwardRef((props, deriveChildData) => {
  const sectionCurrentDataRef = useRef(List());
  const [noOfSection, setNoOfSection] = useState(fromJS([inititalData]));
  const [filesAttachment, setFilesAttachment] = useState(List());
  const stepTwoDetails = useSelector(selectStepTwoDetails);
  const reduxSectionDetails = stepTwoDetails.get('sectionAttachments', List());
  const formType = stepTwoDetails.get('formType', '');
  const formName = stepTwoDetails.get('formName', '');
  const [getStepDetails] = useCallApiHook(getStepTwoDetails);
  const [fileUpload] = useCallApiHook(multipleFileUpload);
  const [updateForm] = useCallApiHook(updateCategoryForm);
  const [setDataApi] = useCallApiHook(setData);
  const [currentCategory] = useConfigureTemplate();
  const categoryFormType = currentCategory.get('categoryFormType', 'form');
  const [searchParams] = useSearchParams();
  const categoryFormId = searchParams.get('categoryFormId');
  const categoryId = currentCategory.get('_id', '');
  const isPublish = searchParams.get('published');
  const level = searchParams.get('level');
  const isNotInstitution = level !== 'institution';

  const callBack = (response) => {
    setFilesAttachment((prev) => prev.merge(response.get('attachments', List())));
    const sectionAttachments = response.get('sectionAttachments', List());
    sectionCurrentDataRef.current = sectionAttachments;
    if (sectionAttachments.size > 1) {
      setNoOfSection(sectionAttachments);
    }
    initialApiCallDone.current = true;
  };

  function validateStepTwo() {
    if (categoryFormType === 'form' && formType === 'section' && sectionCurrentDataRef.current) {
      const sectionNames = sectionCurrentDataRef.current.map((section) =>
        section.get('sectionName', '')
      );
      if (sectionNames.includes('')) {
        setDataApi({ message: 'Section Name not allowed as Empty' });
        return false;
      }
      const filteringDuplicates = [...new Set(sectionNames.toJS())];
      if (filteringDuplicates.length !== sectionNames.size) {
        setDataApi({ message: ' Duplicate Section Name not allowed' });
        return false;
      }
      return true; //means all validation are passed
    }
    return true; //means all validation are passed
  }

  const callBackUpload = (files) => {
    setFilesAttachment((prev) => prev.merge(files));
  };
  const initialApiCallDone = useRef(false);
  const handleConstruction = (updateStep) => {
    if (!initialApiCallDone.current) {
      updateStep();
      return;
    }
    // const isSectionOrEditorDataIsEmpty = sectionCurrentDataRef.current.some(
    //   (section) => section.get('sectionName', '') === '' || section.get('editordata', '') === ''
    // );
    // if (isSectionOrEditorDataIsEmpty) {
    //   return setDataApi({ message: 'Section Name or editor data is not allowed as empty' });
    // }
    const payload = {
      attachments: filesAttachmentRef.current.toJS(),
      sectionAttachments: sectionCurrentDataRef.current.toJS(),
      categoryFormId,
      categoryId,
      // formType,
      step: 2,
    };
    updateForm(payload, updateStep);
  };

  deriveChildData.current = {
    validateStepTwo,
    handleConstruction,
  };

  const filesAttachmentRef = useRef(List());
  filesAttachmentRef.current = filesAttachment;
  // useAutoSaveWithCheckChanges(
  //   { attachments: filesAttachment, sectionAttachments: sectionCurrentDataRef.current },
  //   {
  //     attachments: stepTwoDetails.get('attachments', List()),
  //     sectionAttachments: reduxSectionDetails,
  //   },
  //   handleConstruction
  // );
  // useEffect(() => {
  //   //for autoSave
  //   return () => {
  //     // if (
  //     //   JSON.stringify(
  //     //     fromJS(
  //     //       Map({
  //     //         attachments: filesAttachmentRef.current,
  //     //         sectionAttachments: sectionCurrentDataRef.current,
  //     //       })
  //     //     ).toJS()
  //     //   ) !==
  //     //   JSON.stringify(
  //     //     fromJS({
  //     //       attachments: stepTwoDetails.get('attachments', List()),
  //     //       sectionAttachments: reduxSectionDetails,
  //     //     }).toJS()
  //     //   )
  //     // ) {
  //     // }
  //     handleConstruction();
  //   };
  // }, []);
  const handleInputChange = (e) => {
    const files = e.target.files;
    if (files.length > 5) {
      return setDataApi({ message: 'You can only upload 5 Document per time' });
    }
    let isUnSupportedFileExistInDocs = false;
    let filesSize = 0;

    // this loop for consturct filesAttachment using fileName
    let filesAttachmentWithName = Map();
    for (let i = 0; i < filesAttachment.size; i++) {
      const currentIteratedFile = filesAttachment.get(i, Map());
      filesAttachmentWithName = filesAttachmentWithName.set(currentIteratedFile.get('name'));
    }
    let isDuplicateFileIsExist = false;
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      //this condition for check the upload file are exist old files through the file name
      if (filesAttachmentWithName.has(file.name)) {
        isDuplicateFileIsExist = true;
        break;
      }
      //this condition for check the file type in valid
      const fileType = file.name.split('.').pop();
      filesSize += file.size;
      if (
        !['pdf', 'jpeg', 'jpg', 'png', 'mp4'].includes(fileType) &&
        !isUnSupportedFileExistInDocs
      ) {
        isUnSupportedFileExistInDocs = true;
        break;
      }
    }
    // const isDuplicates = filesAttachment.some((val) => val.get('name', '').includes(filesValues));
    if (isDuplicateFileIsExist) {
      return setDataApi({ message: 'Duplicate Files not allowed with in same name' });
    }
    if (isUnSupportedFileExistInDocs) {
      return setDataApi({ message: 'Upload only pdf, jpeg, jpg, png' });
    }
    if (filesSize > 250 * 1024 * 1024) {
      return setDataApi({ message: 'File size exceeds the limit of 250MB' });
    }
    // if (isDuplicates) {
    //   return setDataApi({ message: 'Attachment already exists' });
    // }

    const convertFormData = new FormData();
    for (let i = 0; i < files.length; i++) {
      convertFormData.append('file', files[i]);
    }
    fileUpload(convertFormData, callBackUpload);
    e.target.value = null;
  };
  function handleSectionAdd() {
    setNoOfSection((prev) => prev.push(prev.length + 1));
  }

  const handleSectionDelete = (index) => () => {
    setNoOfSection((prev) => prev.filter((item, i) => i !== index));
    sectionCurrentDataRef.current = sectionCurrentDataRef.current.filter((item, i) => i !== index);
  };
  function callGetApi() {
    getStepDetails(categoryFormId, callBack);
  }
  useEffect(() => callGetApi(), []);

  //iframe code

  const iframeRef = useRef();
  const sendMessageToIframe = () => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow.postMessage({ values: [], from: 'fromDC' }, '*');
      LocalStorageService.setCustomToken('sections', JSON.stringify(getSections(iframeRef)));
    }
  };
  const handleIframeLoad = () => {
    sendMessageToIframe();
  };
  const templateUrlObject = {
    course: '/templates/course-report/course_report.html',
    program: '/program-report/program_report.html',
  };
  const subUrl = templateUrlObject[level];
  return (
    <>
      <div className="d-flex pb-3">
        <EnableOrDisable valid={filesAttachment.size > 0 || !isPublish}>
          <div>
            <div className="mb-1 f-14">Attached Guide Resources</div>
            <EnableOrDisable valid={!isPublish}>
              <UploadFileCard fileTypes={fileTypes}>
                <FileUploadInput onChange={handleInputChange} />
              </UploadFileCard>
            </EnableOrDisable>
            {!!filesAttachment.size && (
              <UploadFileListComponent
                filesData={filesAttachment}
                formName={formName}
                setFilesAttachment={setFilesAttachment}
                isEditable={isPublish}
              />
            )}
          </div>
        </EnableOrDisable>
        <EnableOrDisable valid={!isPublish}>
          {categoryFormType === 'form' && formType === 'section' && (
            <Buttons clicked={handleSectionAdd} className="ml-auto height_40px">
              Add Sections{' '}
            </Buttons>
          )}
        </EnableOrDisable>
      </div>
      {categoryFormType === 'template' && isNotInstitution && (
        <iframe
          id="formIframe"
          ref={iframeRef}
          src={PUBLIC_PATH + subUrl}
          title="Face Anomaly Report"
          width="100%"
          style={{ border: 'none', overflow: 'auto', height: '100vh' }}
          onLoad={handleIframeLoad}
        ></iframe>
      )}
      {categoryFormType === 'form' &&
        formType === 'section' &&
        noOfSection.map((sectionNumber, index) => (
          <SectionWithEditor
            key={index}
            sectionName={`Section ${alphabetArray[index]}`}
            formType={formType}
            handleSectionDelete={handleSectionDelete}
            reduxSectionDetails={reduxSectionDetails}
            ref={sectionCurrentDataRef}
            index={index}
            isPublish={isPublish}
          />
        ))}
      {categoryFormType === 'form' &&
        formType === 'complete' &&
        noOfSection.map((sectionNumber, index) => (
          <SectionWithEditorComplete
            key={index}
            sectionName={formName}
            formType={formType}
            handleSectionDelete={handleSectionDelete}
            reduxSectionDetails={reduxSectionDetails}
            ref={sectionCurrentDataRef}
            index={index}
            isPublish={isPublish}
          />
        ))}
    </>
  );
});

export default Step2New;

export function DrawerRight({
  children,
  setFilesAttachment,
  handleDeleteAsProps,
  showDelete = true,
  filesData = List(),
  drawerName = 'Guide Resources',
  edit = true,
  intimateToParentWhenDrawerOpen,
}) {
  const [open, setOpen] = useState(false);
  const [openModalIndex, setOpenModalIndex] = useState(null);

  const handleOpenOrClose = () => {
    intimateToParentWhenDrawerOpen && intimateToParentWhenDrawerOpen(open);
    setOpen((prev) => !prev);
  };
  const handleDelete = (index) => (e) => {
    e.stopPropagation();
    if (filesData.size === 1) handleOpenOrClose();
    setFilesAttachment && setFilesAttachment((prev) => prev.filter((_, i) => i !== index));
    handleDeleteAsProps && handleDeleteAsProps(index);
  };
  return (
    <>
      <div onClick={handleOpenOrClose}>{children}</div>
      <Drawer
        anchor="right"
        open={open}
        className="rightDrawer"
        sx={{ '& .MuiDrawer-paper': { overflow: 'visible', width: '78vw' } }}
      >
        <div className="close-btn" onClick={handleOpenOrClose}>
          <CloseIcon />
          <div className="close-btn-bottom"></div>
        </div>
        <div className="close-btn-bottom"></div>
        <div className="my-2 mx-s3">
          <div className="f-24 pl-3">{drawerName}</div>
          {/* <div className="f-10 text-grey">NCAA Course</div> */}
        </div>
        <Divider />
        <div className="mt-1 my-2 mx-3 gap_doc_view d-flex flex-wrap">
          {filesData.map((file, i) => {
            const fileExtension = file.get('name', '').split('.').pop();
            return (
              <div
                key={i}
                className="doc_view_div1 remove_hover"
                onClick={() => setOpenModalIndex(i)}
              >
                <div className="d-flex doc_view_div2">
                  {fileExtension === 'mp4' && <img src={video} alt="img" />}
                  {fileExtension === 'pdf' && <img src={pdf} alt="img" />}
                  {fileExtension !== 'pdf' && fileExtension !== 'mp4' && (
                    <img alt="img" src={image} />
                  )}
                </div>
                <div className="p-2 d-flex align-items-center border_top_grey_view">
                  <div className="doc_view_div3">{getShortString(file.get('name', ''), 26)}</div>
                  {edit && showDelete && (
                    <div className="ml-auto remove_hover" onClick={handleDelete(i)}>
                      <Delete sx={{ color: 'red', fontSize: '14px' }} />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </Drawer>
      <FileViewAtDrawer
        file={filesData.get(openModalIndex, Map())}
        open={openModalIndex !== null}
        drawerName={drawerName}
        // formName={formName}
        index={openModalIndex}
        filesData={filesData}
        setOpenModalIndex={setOpenModalIndex}
        handleClose={() => setOpenModalIndex(null)}
      />
    </>
  );
}

export function FileViewAtDrawer({
  open,
  // formName,
  handleClose,
  setOpenModalIndex,
  index,
  filesData,
  file,
  drawerName = 'Guide Resources',
}) {
  const [getUrl] = useCallApiHook(getSignedUrl);
  const isPdf = file.get('url', '').includes('.pdf');
  const [signedUrl, setSignedUrl] = useState('');
  const isVideo = file.get('url', '').includes('.mp4');
  const url = file.get('url', '');
  const showMoveArrows = filesData.size > 1;
  const showLeftMove = showMoveArrows && index !== 0;
  const showRightMove = showMoveArrows && filesData.size !== index + 1;
  const handleLeftClick = () => {
    setOpenModalIndex((prev) => prev - 1);
  };

  const fileDownloads = (signedUrl) => {
    const filename = signedUrl.split('/').pop().split('?')[0] || 'DisplayFile';
    fetch(signedUrl).then((response) => {
      response.blob().then((blob) => {
        let url = window.URL.createObjectURL(blob);
        let downloadFile = document.createElement('a');
        downloadFile.href = url;
        downloadFile.download = filename;
        downloadFile.click();
      });
    });
  };
  const handleRightClick = () => {
    setOpenModalIndex((prev) => prev + 1);
  };
  useEffect(() => {
    url && getUrl(url, (url) => setSignedUrl(url));
  }, [url]);
  return (
    <Drawer
      anchor="right"
      open={open}
      className="rightDrawer"
      sx={{ '& .MuiDrawer-paper': { overflow: 'visible', width: '88vw' } }}
    >
      <div className="close-btn" onClick={handleClose}>
        <CloseIcon />
      </div>
      <div className="close-btn-bottom"></div>

      <div className="my-3 mx-3 d-flex">
        <div className="f-24">{drawerName}</div>
        {/* <div className="f-10 text-grey">{formName}</div> */}
        <div className="ml-auto" onClick={() => fileDownloads(signedUrl)}>
          <DownloadIcon />
        </div>
      </div>
      <Divider />
      <div className="slider-grid">
        <div className="slider-left">
          <Paper
            className="cursor-pointer"
            sx={{
              ...paperSx,
              pointerEvents: showLeftMove ? 'cursor' : 'none',
            }}
            onClick={handleLeftClick}
          >
            <KeyboardArrowLeftRoundedIcon sx={{ opacity: showLeftMove ? 1 : 0.3 }} />
          </Paper>
        </div>
        <div className="custom-scrollbar g-config-scrollbar items-scroll rounded">
          {isPdf && <iframe src={signedUrl} title="pdf" className="d-flex w-100 ht_80vh"></iframe>}
          {isVideo && <embed type="video/webm" src={signedUrl} width="100%" height="350"></embed>}
          {!isPdf && !isVideo && (
            <img src={signedUrl} alt="" width="100%" height="80vh" className="p-0 img-fluid" />
          )}
        </div>
        <div className="slider-right">
          <Paper
            className="cursor-pointer"
            sx={{
              ...paperSx,
              pointerEvents: showRightMove ? 'cursor' : 'none',
            }}
            onClick={handleRightClick}
          >
            <ChevronRightRoundedIcon sx={{ opacity: showRightMove ? 1 : 0.3 }} />
          </Paper>
        </div>
      </div>
    </Drawer>
  );
}
