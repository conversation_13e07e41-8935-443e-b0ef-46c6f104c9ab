import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes, { oneOfType } from 'prop-types';
import { Map, List } from 'immutable';
import moment from 'moment';
import 'react-circular-progressbar/dist/styles.css';

import FirstCard from './FirstCard';
import MonitoringCard from './MonitoringCard';
import ProgramCard from './ProgramCard';
import * as actions from '../../../../_reduxapi/dashboard/action';
import * as actions1 from '../../../../_reduxapi/leave_management/actions';
import * as actions2 from '../../../../_reduxapi/reports_and_analytics/action';
import {
  selectActiveInstitutionCalendar,
  selectUserId,
  selectSelectedRole,
} from '../../../../_reduxapi/Common/Selectors';
import {
  selectLeaveRequestsLists,
  selectConfiguredProgramLists,
  selectDepartmentSubjectLists,
  selectMySessionLists,
  selectLoading,
  selectMonitoringCourses,
  selectMonitoringCourseDetail,
} from '../../../../_reduxapi/dashboard/selectors';
import {
  selectDashboardProgramList,
  selectDashboardLoader,
} from '../../../../_reduxapi/reports_and_analytics/selectors';
import { eString } from '../../../../utils';
class Dashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTabs: Map({
        toDoList: false,
        leaveManagementList: false,
        sessionList: true,
      }),
    };
  }

  componentDidMount() {
    //const { programsLists, leaveRequestsLists } = this.props;
    //if (programsLists.size === 0) {
    this.programLists();
    //}
    //if (leaveRequestsLists.get('pending', List()).size === 0) {
    this.leaveLists();
    //}

    //this.getConfigureSettings();
    this.getMySessionLists();
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.activeInstitutionCalendar.get('_id') !== undefined &&
      this.props.activeInstitutionCalendar.get('_id') !==
        prevProps.activeInstitutionCalendar.get('_id')
    ) {
      this.leaveLists();
      this.programLists();
      //this.getConfigureSettings();
      this.getMySessionLists();
    }
  }

  getMySessionLists = () => {
    const { getMySessionLists, userId, activeInstitutionCalendar } = this.props;
    if (activeInstitutionCalendar.get('_id', '') !== '') {
      const currentDate = moment().format('YYYY-MM-DD');
      getMySessionLists(
        activeInstitutionCalendar.get('_id', ''),
        userId,
        currentDate + 'T00:00:00.000Z'
      );
    }
  };

  leaveLists = () => {
    const { getLeaveRequestsLists, userId, activeInstitutionCalendar, selectedRole } = this.props;
    if (
      activeInstitutionCalendar.get('_id', '') !== '' &&
      selectedRole.getIn(['_role_id', '_id'], '') !== ''
    ) {
      getLeaveRequestsLists(
        userId,
        activeInstitutionCalendar.get('_id', ''),
        selectedRole.getIn(['_role_id', '_id'], '')
      );
    }
  };

  programLists = () => {
    const { getDashboardDataType, userId, activeInstitutionCalendar, selectedRole } = this.props;
    if (
      activeInstitutionCalendar.get('_id', '') !== '' &&
      selectedRole.getIn(['_role_id', '_id'], '') !== ''
    ) {
      getDashboardDataType({
        institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
        userId,
        roleId: selectedRole.getIn(['_role_id', '_id'], 'program_list'),
        dataType: 'program_list',
      });
    }
  };

  getConfigureSettings = () => {
    const { getConfigureSettings, userId, activeInstitutionCalendar, selectedRole } = this.props;
    if (
      activeInstitutionCalendar.get('_id', '') !== '' &&
      selectedRole.getIn(['_role_id', '_id'], '') !== ''
    ) {
      getConfigureSettings(
        userId,
        activeInstitutionCalendar.get('_id', ''),
        selectedRole.getIn(['_role_id', '_id'], '')
      );
    }
  };

  tabUpdateState = (list) => {
    this.setState({ activeTabs: list });
  };

  saveConfigureSettings = (data, callBack) => {
    const {
      updateConfigureSettings,
      userId,
      activeInstitutionCalendar,
      selectedRole,
      getMonitoringCourseList,
    } = this.props;
    updateConfigureSettings(
      userId,
      activeInstitutionCalendar.get('_id', ''),
      selectedRole.getIn(['_role_id', '_id'], ''),
      data,
      () => {
        callBack();
        getMonitoringCourseList(
          userId,
          activeInstitutionCalendar.get('_id', ''),
          selectedRole.getIn(['_role_id', '_id'], '')
        );
      }
    );
  };

  redirectDetailPage = (id, name) => {
    const { history } = this.props;
    const query = `?programId=${eString(id)}&programName=${eString(name)}`;
    history.push(`dashboard/details${query}`);
  };

  goToReports = (course, level, linkActiveUrl) => {
    const { history, setData } = this.props;
    setData(Map({ courseOverview: Map() }));
    history.push(
      `/reports/programs/${eString(course.get('_program_id'))}/courses/${eString(
        course.get('_course_id')
      )}/${linkActiveUrl}?level=${eString(level.get('level_no'))}&term=${eString(
        level.get('term')
      )}&rotation=${eString(course.get('rotation'))}&rotationCount=${eString(
        course.get('rotation_count', 0)
      )}`
    );
  };

  render() {
    const { activeTabs } = this.state;
    const {
      leaveRequestsLists,
      dashboardProgramList,
      configuredProgramLists,
      setData,
      departmentSubjectLists,
      setLeaveData,
      mySessionLists,
      selectedRole,
      loading,
      dashboardLoading,
      getMonitoringCourseList,
      userId,
      activeInstitutionCalendar,
      monitoringCourses,
      getMonitoringCourseDetail,
      monitoringCourseDetail,
    } = this.props;

    return (
      <div className="main bg-gray pb-5">
        <div className="pt-3">
          <div className="container">
            <div className="row pb-4">
              <div className="col">
                <FirstCard
                  clicked={this.tabUpdateState}
                  activeTabs={activeTabs}
                  change={this.handleSelect}
                  leaveRequestsLists={leaveRequestsLists}
                  setLeaveData={setLeaveData}
                  mySessionLists={mySessionLists}
                  programsLists={dashboardProgramList.get('programs', List())}
                  loading={loading}
                />
              </div>

              <div className="col">
                <MonitoringCard
                  goToReports={this.goToReports}
                  roleId={selectedRole.getIn(['_role_id', '_id'], '')}
                  loading={loading}
                  getMonitoringCourseList={getMonitoringCourseList}
                  institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
                  userId={userId}
                  monitoringCourses={monitoringCourses}
                  getMonitoringCourseDetail={getMonitoringCourseDetail}
                  monitoringCourseDetail={monitoringCourseDetail}
                />
              </div>

              <div className="col">
                <ProgramCard
                  configuredProgramLists={configuredProgramLists}
                  programsLists={dashboardProgramList.get('programs', List())}
                  setData={setData}
                  departmentSubjectLists={departmentSubjectLists}
                  updateConfigureSettings={this.saveConfigureSettings}
                  redirectDetailPage={this.redirectDetailPage}
                  loading={dashboardLoading}
                  settingsLoading={loading}
                  getConfigureSettings={this.getConfigureSettings}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

Dashboard.propTypes = {
  userId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getDashboardDataType: PropTypes.func,
  setData: PropTypes.func,
  configuredProgramLists: PropTypes.instanceOf(List),
  departmentSubjectLists: PropTypes.instanceOf(List),
  updateConfigureSettings: PropTypes.func,
  saveConfigureSettings: PropTypes.func,
  selectedRole: PropTypes.instanceOf(Map),
  goToReports: PropTypes.func,
  getMonitoringCourseList: PropTypes.func,
  monitoringCourses: oneOfType([PropTypes.instanceOf(Map), PropTypes.instanceOf(List)]),
  getMonitoringCourseDetail: PropTypes.func,
  monitoringCourseDetail: PropTypes.instanceOf(Map),
  dashboardProgramList: PropTypes.instanceOf(Map),
  getConfigureSettings: PropTypes.func,
  getMySessionLists: PropTypes.func,
  leaveRequestsLists: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  setLeaveData: PropTypes.func,
  mySessionLists: PropTypes.instanceOf(List),
  loading: PropTypes.instanceOf(Map),
  dashboardLoading: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  getLeaveRequestsLists: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    userId: selectUserId(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    leaveRequestsLists: selectLeaveRequestsLists(state),
    dashboardProgramList: selectDashboardProgramList(state),
    configuredProgramLists: selectConfiguredProgramLists(state),
    departmentSubjectLists: selectDepartmentSubjectLists(state),
    selectedRole: selectSelectedRole(state),
    mySessionLists: selectMySessionLists(state),
    loading: selectLoading(state),
    dashboardLoading: selectDashboardLoader(state),
    monitoringCourses: selectMonitoringCourses(state),
    monitoringCourseDetail: selectMonitoringCourseDetail(state),
  };
};

export default withRouter(
  connect(mapStateToProps, { ...actions, ...actions1, ...actions2 })(Dashboard)
);
