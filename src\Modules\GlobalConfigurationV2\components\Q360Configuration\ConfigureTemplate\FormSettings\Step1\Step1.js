import React, { useEffect, useState } from 'react';
import { List, Map as IMap, fromJS, Set } from 'immutable';
import { useSelector, useDispatch } from 'react-redux';
import { Checkbox, Divider } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CloseIcon from '@mui/icons-material/Close';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import { makeStyles } from '@mui/styles';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import InfoIcon from '@mui/icons-material/Info';

import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { courseTypeHeading } from 'Modules/QAPC/components/QualityAssuranceProcess/FormList/utils';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import {
  useCallApiHook,
  useSearchParams,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import {
  getSingleFormOccurrence,
  postConfigure,
  getFormConfigurationList,
  updateConfigure,
  getQaPcSetting,
  setData,
  getTermList,
} from '_reduxapi/q360/actions';
import { selectFormOccurrence, selectQaPcSetting, selectTermList } from '_reduxapi/q360/selectors';
import ConfigureModal2 from './ConfigureModal2';
import { monthName, validateForm } from './utils';
import ConfiguredDetails from './ConfiguredDetails';
import { jsUcfirstAll } from 'utils';

export const switchSX = (isChecked = false) => ({
  '& .MuiSwitch-thumb': {
    background: '#147afc',
    width: '15px',
    height: '15px',
  },
  '& .MuiSwitch-track': {
    height: '69%',
    width: '95%',
    background: `${!isChecked ? '#D1D5DB' : '#147afc'} !important`,
  },
});

export const switchSXDisable = {
  '& .MuiSwitch-thumb': {
    background: '#9CA3AF',
    width: '15px',
    height: '15px',
  },
  '& .MuiSwitch-track': {
    height: '69%',
    width: '95%',
    background: '#D1D5DB !important',
  },
};

export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '14px',
    color: '#4B5563',
    fontWeight: 400,
  },
}));

const tabBorderNone = {
  borderBottom: 'none !important',
  '& .MuiTab-root': {
    padding: '0px',
    margin: '0px 40px 0px 0px',
    minWidth: '0px',
  },
};

const textTransform = {
  textTransform: 'none',
  color: '#6B7280',
  fontSize: '14px',
  fontWeight: '400',
};

const tabPadding = {
  '&.MuiTabPanel-root': {
    padding: '0px',
  },
};

const singleGroup = IMap({
  name: 'all',
  minimum: 1,
  startMonth: 'January',
  endMonth: 'December',
});

const getCourseListSize = (courseList) => {
  const shared_with_others = courseList
    .get('shared_with_others', IMap())
    .filter((isEnable) => isEnable.get('isEnable', false)).size;
  const shared_from = courseList
    .get('shared_from', IMap())
    .filter((isEnable) => isEnable.get('isEnable', false)).size;
  const standard = courseList
    .get('standard', IMap())
    .filter((isEnable) => isEnable.get('isEnable', false)).size;
  const selective = courseList
    .get('selective', IMap())
    .filter((isEnable) => isEnable.get('isEnable', false)).size;
  return shared_with_others + shared_from + standard + selective;
};

const getCurriculumListSize = (curriculumList) => {
  const standard = curriculumList
    .get('standard', IMap())
    .filter((isEnable) => isEnable.get('isEnable', false)).size;
  return standard;
};

const getInstitutionListSize = (institutionList) => {
  const standard = institutionList
    .get('standard', IMap())
    .filter((isEnable) => isEnable.get('isEnable', false)).size;
  return standard;
};

const monthNameKey = (key) => {
  const index = monthName.findIndex((i) => i === key);
  return index + 1;
};

export default function Step1({ state, setState, updateStepOneConfiguration }) {
  const classes = useStyles();
  const [checkedData, setCheckedData] = useState(IMap());
  const [expanded, setExpanded] = useState(0);
  const [modal, setModal] = useState(false);
  const [institutionDetails, setInstitutionDetails] = useState(IMap());
  const [searchCourse, setSearchCourse] = useState(IMap());
  const [selectedConfig, setSelectedConfig] = useState();
  const formOccurrences = useSelector(selectFormOccurrence);
  const termList = useSelector(selectTermList);
  const [searchParams] = useSearchParams();
  const formId = searchParams.get('categoryFormId');
  const categoryId = searchParams.get('categoryId');
  const level = searchParams.get('level');
  const isPublished = searchParams.get('published');

  const singleFormOccurrence = formOccurrences.get(formId, List());

  const dispatch = useDispatch();

  const handleCourseSearch = (event, index) => {
    event.stopPropagation();
    const value = event.target.value;
    setSearchCourse((prev) => prev.set(index, value));
  };

  const [getApi] = useCallApiHook(getSingleFormOccurrence);
  const [getQaPcSettingApi] = useCallApiHook(getQaPcSetting);

  const qaPcSetting = useSelector(selectQaPcSetting);

  useEffect(() => {
    getApi(formId);
  }, [formId]);

  useEffect(() => {
    // if (qaPcSetting.get('tagData', List()).size) return;
    getQaPcSettingApi();
  }, []);

  useEffect(() => {
    const detail3 = [];
    singleFormOccurrence.toJS().forEach((detail) => {
      let program = detail3.find((p) => p._program_id === detail.programId);
      if (!program) {
        program = {
          _program_id: detail.programId,
          program_name: detail.programName,
          curriculum: [],
        };
        detail3.push(program);
      }
      let curriculum = program.curriculum.find(
        (course) => course._curriculum_id === detail.curriculumId
      );
      if (!curriculum) {
        curriculum = {
          _curriculum_id: detail.curriculumId,
          curriculum_name: detail.curriculumName,
          years: [],
        };
        program.curriculum.push(curriculum);
      }
      let year = curriculum.years.find((y) => y.year === detail.year);
      if (!year) {
        year = {
          year: detail.year,
          courses: [],
        };
        curriculum.years.push(year);
      }
      year.courses.push({
        _id: detail._id,
        _program_id: detail.programId,
        program_name: detail.programName,
        _curriculum_id: detail.curriculumId,
        curriculum_name: detail.curriculumName,
        year: detail.year,
        courseId: detail.courseId,
        course_name: detail.courseName,
        course_code: detail.courseCode,
        shared_with_others: detail.sharedWithOthers,
        shared_from_others: detail.sharedFormOthers,
        course_type: detail.courseType,
        institution_type: detail.institutionType,
        isConfigure: detail.isConfigure,
        isEnable: detail.isEnable,
        tags: detail.tags,
      });
    });

    // setState(state.set('formOccurrence', fromJS(detail3)));
  }, [singleFormOccurrence]);

  const [getFormConfigurationListApi] = useCallApiHook(getFormConfigurationList);

  const [getTermListApi] = useCallApiHook(getTermList);

  useEffect(() => {
    if (
      level !== 'institution' &&
      state.get('formOccurrence', List()).size !== 0 &&
      termList.size === 0
    )
      getTermListApi(
        expanded === 0 ? state.getIn(['formOccurrence', 0, '_program_id'], '') : expanded
      );
  }, [expanded, state]);

  const onClickAccordion = (id) => () => {
    const open = id === expanded ? -1 : id;
    setExpanded(open);
  };

  const handleChangeAttemptData = (data) => (e) => {
    e.stopPropagation();
    let { key, value, isEdited, studentGroupKey } = data;
    if (value === undefined) value = e.target.value;
    setState((prevState) => {
      let result = IMap();
      result = prevState.setIn(key, value).setIn(isEdited, true);
      if (key.includes('group')) {
        key[key.length - 1] = 'groupType';
        if (value === 'all') {
          result = result.setIn(key, List([singleGroup]));
        } else {
          const studentGroups = result.getIn([...studentGroupKey, 'studentGroups'], List());
          result = result.setIn(
            key,
            studentGroups.map((groupName) => singleGroup.set('name', groupName))
          );
        }
      }
      return result;
    });
  };

  const handleCheckTag = (
    key,
    tagIndex,
    isChecked,
    tagName,
    tagId,
    tagData,
    isEdited,
    hasSubTags
  ) => {
    setState((prev) => {
      let prevState = prev.setIn(isEdited, true);
      return prevState.updateIn(key, List(), (prevState) => {
        const existingTagIndex = prevState.findIndex((tag) => tag.get('_id') === tagId);
        const subTags = isChecked ? tagData.getIn([tagIndex, 'subTag'], List()) : List();
        if (existingTagIndex !== -1) {
          return hasSubTags
            ? prevState.setIn([existingTagIndex, 'subTag'], subTags)
            : prevState.filter((tag) => tag.get('_id', '') !== tagId);
        } else {
          if (hasSubTags) {
            return prevState.push(
              IMap({
                _id: tagId,
                name: tagName,
                subTag: subTags,
              })
            );
          } else {
            return prevState.push(
              IMap({
                _id: tagId,
                name: tagName,
              })
            );
          }
        }
      });
    });
  };

  const handleCheckSubTag = (key, tagIndex, subTagIndex, isChecked, tagId, tagDatas, isEdited) => {
    setState((prev) => {
      let prevState = prev.setIn(isEdited, true);
      return prevState.updateIn(key, List(), (prevTags) => {
        if (typeof prevState.getIn(['tags', tagIndex], '') === 'undefined') {
          prevState = prevState.setIn(['tags', tagIndex], IMap());
        }
        const existingTagIndex = prevTags.findIndex((tag) => tag.get('_id') === tagId);
        const subTag = tagDatas.getIn([tagIndex, 'subTag', subTagIndex]);
        if (existingTagIndex !== -1) {
          const currentSubTags = prevTags.getIn([existingTagIndex, 'subTag'], List());

          if (isChecked) {
            if (!currentSubTags.includes(subTag)) {
              return prevTags.updateIn([existingTagIndex, 'subTag'], (subTags) =>
                subTags.push(subTag)
              );
            }
          } else {
            return prevTags.updateIn([existingTagIndex, 'subTag'], (subTags) =>
              subTags.filter((st) => st !== subTag)
            );
          }
        } else if (isChecked) {
          return prevTags.push(
            IMap({
              _id: tagId,
              subTag: List([subTag]),
            })
          );
        }

        return prevTags;
      });
    });
  };

  const updateGroupsCount = (data) => (e) => {
    let { key, isEdited, attemptTypes } = data;
    e.stopPropagation();
    const length = e.target.value;
    const result = Array.from({ length }, (_, index) => `Gr-${String(index + 1).padStart(2, '0')}`);
    setState((prevState) =>
      prevState
        .setIn(key, fromJS(result))
        .setIn(isEdited, true)
        .update(attemptTypes, List(), (data) => {
          let newData = data;
          newData.forEach((_, index) => {
            newData = newData.setIn([index, 'group'], 'all');
          });
          return newData;
        })
    );
  };

  const handleDelete = (data) => () => {
    let { key, index } = data;
    setState((prevState) => prevState.setIn(key, prevState.getIn(key, List()).delete(index)));
  };

  const generateOutput = ({
    selectedOccurrences,
    typeKey,
    academicTermKey,
    attemptTypeKey,
    academicTerm,
    attemptType,
    actions,
  }) => {
    const selectedTerms =
      typeKey === 'academicTerm' ? selectedOccurrences : state.getIn(academicTermKey);
    const selectedTypes =
      typeKey === 'attemptType' ? selectedOccurrences : state.getIn(attemptTypeKey);
    const selectedAcademicTerm = state.getIn(academicTerm);
    const selectedAttemptType = state.getIn(attemptType);
    let output = List();

    const initialAttemptType = fromJS({
      typeName: 'All',
      termName: 'All',
      executionsPer: true,
      academicYear: 'every',
      group: level === 'course' && actions.studentGroups ? 'all' : 'none',
      groupType: [singleGroup],
    });

    const checkTerm = selectedAcademicTerm === 'all' && level !== 'institution' ? 'All' : 'none';
    const checkAttemptType =
      selectedAttemptType === 'all' && level !== 'institution' ? 'All' : 'none';

    if (selectedTerms.isEmpty() && selectedTypes.isEmpty()) {
      output = output.push(
        fromJS(initialAttemptType.set('termName', checkTerm).set('typeName', checkAttemptType))
      );
    }

    if (selectedTerms.isEmpty() && !selectedTypes.isEmpty()) {
      selectedTypes.forEach((type) => {
        output = output.push(
          fromJS(initialAttemptType.set('termName', checkTerm).set('typeName', type))
        );
      });
      return output;
    }

    if (selectedTypes.isEmpty() && !selectedTerms.isEmpty()) {
      selectedTerms.forEach((term) => {
        output = output.push(
          fromJS(initialAttemptType.set('termName', term).set('typeName', checkAttemptType))
        );
      });
      return output;
    }

    selectedTerms.forEach((term) => {
      selectedTypes.forEach((type) => {
        output = output.push(
          fromJS(initialAttemptType.set('termName', term).set('typeName', type))
        );
      });
    });

    return output;
  };

  const handleChangeTermAndAttempt = ({ key, value, typeKey, isEdited, actions }) => {
    const keys = {
      academicTermKey: key.concat(['selectedAcademicTerm']),
      attemptTypeKey: key.concat(['selectedAttemptType']),
      academicTerm: key.concat(['academicTerm']),
      attemptType: key.concat(['attemptType']),
      attemptTypesKey: key.concat(['attemptTypes']),
    };
    const selectedKey = typeKey === 'academicTerm' ? keys.academicTermKey : keys.attemptTypeKey;

    const selectedOccurrences = value === 'all' ? List(['All']) : List();

    setState((prevState) =>
      prevState
        .setIn(key.concat([typeKey]), value)
        .setIn(selectedKey, selectedOccurrences)
        .setIn(isEdited, true)
        .setIn(
          keys.attemptTypesKey,
          generateOutput({
            selectedOccurrences,
            typeKey,
            ...keys,
            actions,
          })
        )
    );
  };

  const handleCheckTermAndAttempt = ({ e, value, isEdited, key, typeKey, actions }) => {
    const isChecked = e.target.checked;
    const keys = {
      academicTermKey: key.concat(['selectedAcademicTerm']),
      attemptTypeKey: key.concat(['selectedAttemptType']),
      academicTerm: key.concat(['academicTerm']),
      attemptType: key.concat(['attemptType']),
      attemptTypesKey: key.concat(['attemptTypes']),
    };
    const selectedKey = typeKey === 'academicTerm' ? keys.academicTermKey : keys.attemptTypeKey;

    setState((prevState) => {
      const selectedOccurrences = isChecked
        ? prevState.getIn(selectedKey, List()).push(value)
        : prevState.getIn(selectedKey, List()).filter((id) => id !== value);

      return prevState
        .setIn(selectedKey, selectedOccurrences)
        .setIn(isEdited, true)
        .setIn(
          keys.attemptTypesKey,
          generateOutput({
            selectedOccurrences,
            typeKey,
            ...keys,
            actions,
          })
        );
    });
  };

  const [courseExpanded, setCourseExpanded] = useState(0);
  const onClickAccordionCourse = (id, fId, commonAttemptTypeKey) => (e) => {
    e.stopPropagation();
    const open = id === courseExpanded ? -1 : id;
    setCourseExpanded(open);
    let setInKeyArray = [...commonAttemptTypeKey];

    const callBack = (data) => {
      function transformAttemptTypes(attemptTypes1) {
        let grouped = IMap();
        attemptTypes1.forEach((item) => {
          const key = `${item.attemptTypeName}-${item.term}`;
          const groupType = {
            name: item.groupName,
            minimum: item.minimum,
            startMonth: monthName[item.startMonth - 1],
            endMonth: monthName[item.endMonth - 1],
            _id: item._id,
          };

          if (!grouped.has(key)) {
            grouped = grouped.set(
              key,
              IMap({
                typeName: item.attemptTypeName,
                termName: item.term,
                executionsPer: item.executionsPer,
                academicYear: item.academicYear,
                group: item.group,
                groupType: List(),
              })
            );
          }

          grouped = grouped.updateIn([key, 'groupType'], (list) => list.push(fromJS(groupType)));
        });

        return grouped.valueSeq().toJS();
      }

      const attemptTypes = transformAttemptTypes(data.get('formGroupData', List()).toJS());

      const updatedAttemptTypes = fromJS(attemptTypes);

      const constructData = () => {
        return {
          attemptTypes: fromJS(updatedAttemptTypes),
          studentGroups: data.getIn(['formCourseSettingData', 'numberOfGroups'], List()),
          tags: data.getIn(['formCourseSettingData', 'tags'], List()),
          actions: data.getIn(['formCourseSettingData', 'actions'], IMap()),
          academicTerm:
            updatedAttemptTypes.getIn([0, 'termName'], 'all').toLowerCase() === 'all'
              ? 'all'
              : 'every',
          attemptType:
            updatedAttemptTypes.getIn([0, 'typeName'], 'all').toLowerCase() === 'all'
              ? 'all'
              : 'every',
          selectedAcademicTerm: Set(
            updatedAttemptTypes.map((term) => term.get('termName', ''))
          ).toList(),
          selectedAttemptType: Set(
            updatedAttemptTypes.map((term) => term.get('typeName', ''))
          ).toList(),
        };
      };

      setState((prev) =>
        prev
          .setIn([...setInKeyArray, 'occurrences'], fromJS(constructData()))
          .setIn([...setInKeyArray, 'prevOccurrences'], fromJS(constructData()))
      );
    };

    if (statusTab !== 'Pending') getFormConfigurationListApi(fId, callBack);
  };
  const [curriculumTab, setCurriculumTab] = useState(0);
  const onClickCurriculumTab = (_, newValue) => {
    setCurriculumTab(newValue);
  };
  const [yearTab, setYearTab] = useState(0);
  const onClickYearTab = (_, newValue) => {
    setYearTab(newValue);
  };
  const [statusTab, setStatusTab] = useState('Pending');
  const onClickStatusTab = (_, newValue) => {
    setStatusTab(newValue);
  };
  const handleCheckedCourseTypeWise = (paramKey, value, programIndex, curriculumTab, yearTab) => (
    e
  ) => {
    // const key = `${programIndex}/${curriculumTab}/${yearTab}/${paramKey}`;

    const key =
      level === 'course'
        ? `${programIndex}/${curriculumTab}/${yearTab}/${paramKey}`
        : `${programIndex}/${paramKey}`;
    const checked = e.target.checked;
    if (checked) {
      const constructedKeys = value.map((detail) => {
        if (level === 'course') {
          return `${detail.get('courseId', '')}/${detail.get('course_name', '')} ${detail.get(
            'course_code'
          )}/${detail.get('_id')}`;
        }
        if (level === 'program') {
          return `${detail.get('_curriculum_id', '')}/${detail.get(
            'curriculum_name',
            ''
          )}/${detail.get('_id')}`;
        }
      });

      return setCheckedData((prev) => prev.set(key, constructedKeys));
    }
    setCheckedData((prev) => prev.delete(key));
  };

  const [postApi] = useCallApiHook(postConfigure);
  const [updateApi] = useCallApiHook(updateConfigure);

  const handleCheckedIndividual = (paramKey, courseKey, programIndex, curriculumTab, yearTab) => (
    e
  ) => {
    e.stopPropagation();
    const key =
      level === 'course'
        ? `${programIndex}/${curriculumTab}/${yearTab}/${paramKey}`
        : `${programIndex}/${paramKey}`;
    const checked = e.target.checked;
    const updatedCourse = checkedData.get(key, List()).filter((course) => course !== courseKey);
    setCheckedData((prev) => {
      if (checked) {
        return prev.set(key, prev.get(key, List()).push(courseKey));
      }
      if (updatedCourse.size) {
        return prev.set(key, updatedCourse);
      }
      return prev.delete(key);
    });
  };
  const handleToggle = ({ constructKey, value, courseIndex = -1, mergedData }) => (e) => {
    e.stopPropagation();
    const checked = e.target.checked;
    const [programIndex, curriculumTab, yearTab, key] = constructKey.split('/');

    const statusKey = statusTab === 'Pending' ? 0 : 1;

    let setInKeyArray =
      level === 'course'
        ? [
            'formOccurrence',
            programIndex,
            'curriculum',
            curriculumTab,
            'years',
            yearTab,
            'courses',
            statusKey,
            key,
          ]
        : level === 'program'
        ? [
            'formOccurrence',
            constructKey.split('/')[0],
            'curriculum',
            statusKey,
            constructKey.split('/')[1],
          ]
        : ['formOccurrence', statusKey, constructKey.split('/')[1]];

    if (courseIndex > -1) {
      return setState((prev) =>
        prev.updateIn(setInKeyArray.concat(courseIndex), IMap(), (course) =>
          course.set('isEnable', checked)
        )
      );
    }
    setState((prev) => {
      let updatedState = prev;

      if (level === 'course') {
        const filters = mergedData.map((item) => {
          const [courseId, courseName, id] = item.split('/');
          return IMap({ courseId, courseName, id });
        });

        const updatedCourses = value.map((course) => {
          const match = filters.some(
            (filter) =>
              filter.get('courseId') === course.get('courseId') &&
              filter.get('courseName') ===
                `${course.get('course_name')} ${course.get('course_code')}` &&
              filter.get('id') === course.get('_id')
          );

          return course.set('isEnable', course.get('isEnable', false) ? !match : false);
        });

        updatedState = updatedState.setIn(setInKeyArray, updatedCourses);
      } else {
        updatedState = updatedState.setIn(
          setInKeyArray,
          value.map((course) => course.set('isEnable', checked))
        );
      }

      return updatedState;
    });
    setCheckedData(IMap());
  };
  const openOrCloseModal = (constructKey) => {
    if (!modal && level !== 'institution') {
      setSelectedConfig(constructKey.split('/')[0]);
    }
    setModal((prev) => !prev);
  };

  const getInstitutionDetails = (details) => {
    setInstitutionDetails((prev) =>
      prev
        .set('institutionName', details.get('institutionName', ''))
        .set('_id', details.get('_id', ''))
    );
  };

  const handleConfigure = (course) => (e) => {
    e.stopPropagation();
    openOrCloseModal();
    getInstitutionDetails(course);
  };

  const constructedRequestData = ({
    modalState,
    constructedAttemptTypes,
    method,
    ids,
    isEnable,
  }) => {
    const key = method === 'update' ? 'categoryFormCourseId' : 'categoryFormCourseIds';
    const requestData = {
      categoryFormId: formId,
      categoryId: categoryId,
      tags: modalState.get('tags', List()),
      numberOfGroups: modalState.get('studentGroups', List()),
      selectedType: constructedAttemptTypes.map((attempt) =>
        attempt
          .set('startMonth', monthNameKey(attempt.get('startMonth', '')))
          .set('endMonth', monthNameKey(attempt.get('endMonth', '')))
          .set('attemptTypeName', attempt.get('typeName', ''))
          .set('term', attempt.get('termName', ''))
          .set(
            'groupName',
            level === 'course' && attempt.get('group', '') !== 'none'
              ? attempt.get('name', '')
              : 'none'
          )
          .delete('typeName')
          .delete('termName')
          .delete('name')
      ),
      [key]: ids,
      ...(method === 'update' && { isEnable }),
    };
    return requestData;
  };
  const postCallBack = () => {
    getApi(formId);
    setCheckedData(IMap());
    openOrCloseModal();
    updateStepOneConfiguration && updateStepOneConfiguration();
  };
  const constructingForConfiguration = (modalState = IMap(), actions) => () => {
    const constructedAttemptTypes = modalState
      .get('attemptTypes', List())
      .reduce((acc, attempt) => {
        const groupTypes = attempt
          .get('groupType')
          .map((group) => attempt.delete('groupType').merge(group));
        return acc.concat(groupTypes);
      }, List());

    let ids = List();

    if (level !== 'institution') {
      checkedData
        .entrySeq()
        .map(([_, courses]) =>
          courses.map((courseKey) => {
            const _id = courseKey.split('/')[2];
            ids = ids.push(_id);
            return ids;
          })
        )
        .toList();
    } else {
      ids = ids.push(institutionDetails.get('_id'));
    }

    const requestData = constructedRequestData({
      modalState,
      constructedAttemptTypes,
      method: 'create',
      ids,
    });

    if (validateForm(modalState, setData, dispatch, level, constructedAttemptTypes, actions)) {
      postApi(requestData, postCallBack);
    }
  };

  const editConfiguration = ({
    modalState = IMap(),
    id,
    isEdited,
    prevModalState = IMap(),
    isEnable,
    // actions,
  }) => () => {
    const getGroupTypes = (attempt) =>
      attempt.get('groupType', List()).map((group) => attempt.delete('groupType').merge(group));

    const constructAttemptTypes = (state) =>
      state
        .get('attemptTypes', List())
        .reduce((acc, attempt) => acc.concat(getGroupTypes(attempt)), List());

    const removeIdFromItems = (items) => items.map((item) => item.delete('_id'));

    // const markDeletedItems = (constructedAttemptTypes, constructedPrevAttemptTypes) =>
    //   constructedPrevAttemptTypes.map((item) => {
    //     const group = item.get('group', '');
    //     const name = item.get('name', '');
    //     const typeName = item.get('typeName', '');
    //     const termName = item.get('termName', '');

    //     const isDeleted = !constructedAttemptTypes.some((currentItem) => {
    //       const termMatches =
    //         !actions.get('academicTerms', false) || currentItem.get('termName', '') === termName;
    //       const typeMatches =
    //         !actions.get('attemptType', false) || currentItem.get('typeName', '') === typeName;

    //       if (level === 'course') {
    //         const nameMatches =
    //           !actions.get('studentGroups', false) || currentItem.get('name', '') === name;
    //         const groupMatches =
    //           !actions.get('studentGroups', false) || currentItem.get('group', '') === group;
    //         return nameMatches && groupMatches && termMatches && typeMatches;
    //       }

    //       if (level === 'program') {
    //         return termMatches && typeMatches;
    //       }

    //       return false;
    //     });

    //     return item.set('isDeleted', isDeleted);
    //   });

    const markDeletedItems = (constructedPrevAttemptTypes) =>
      constructedPrevAttemptTypes.map((item) => {
        return item.set('isDeleted', true);
      });

    const constructedAttemptTypes = removeIdFromItems(constructAttemptTypes(modalState));
    const constructedPrevAttemptTypes = constructAttemptTypes(prevModalState);

    const finalOutput =
      level === 'institution'
        ? constructedAttemptTypes
        : constructedAttemptTypes.concat(markDeletedItems(constructedPrevAttemptTypes));

    const callback = () => {
      setState((prev) => {
        let prevState = prev.setIn(isEdited, false);
        return prevState;
      });
    };

    const requestData = constructedRequestData({
      modalState,
      constructedAttemptTypes: finalOutput,
      method: 'update',
      ids: id,
      isEnable,
    });

    updateApi(requestData, callback);
  };

  const handleCloseEdit = (isEdited) => (e) => {
    e.stopPropagation();
    setState((prev) => {
      let prevState = prev.setIn(isEdited, false);
      return prevState;
    });
  };

  const getProgramName = () => {
    if (!checkedData.size) return '';
    const key = checkedData.keySeq().first(); // keySeq() returns an Iterable of keys
    const [programIndex] = key.split('/');
    return state.getIn(['formOccurrence', programIndex, 'program_name'], '');
  };

  const preventPropagation = (e) => e.stopPropagation();

  const getProgramId = () => {
    if (!checkedData.size) return '';
    const key = checkedData.keySeq().first();
    const [programIndex] = key.split('/');
    return state.getIn(['formOccurrence', programIndex, '_program_id'], '');
  };

  let institutionLevel = state.get('formOccurrence', List());

  const handleChange = {
    handleChangeAttemptData,
    handleCheckTag,
    handleCheckSubTag,
    updateGroupsCount,
    handleDelete,
    handleChangeTermAndAttempt,
    handleCheckTermAndAttempt,
  };

  return (
    <>
      {level === 'course' &&
        state.get('formOccurrence', List()).map((program, programIndex) => {
          const yearsList = program.getIn(['curriculum', curriculumTab, 'years'], List());
          const courseList = yearsList.getIn([yearTab, 'courses'], List());
          const checkBoxKeyData = [programIndex, curriculumTab, yearTab];
          const accordionExpanded1 = program.get('_program_id', '') === expanded;
          const actualExpanded = accordionExpanded1 || (expanded === 0 && programIndex === 0);

          return (
            <>
              <Accordion
                key={programIndex}
                className="bg-white"
                sx={{ borderRadius: '8px' }}
                expanded={actualExpanded}
                onChange={onClickAccordion(program.get('_program_id', ''))}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel3-content"
                  id="panel3-header"
                >
                  <div className="d-flex align-items-center w-100 mt-2">
                    <div className="fw-400 f-20 text-mGrey">{program.get('program_name', '')}</div>
                    <div
                      className="ml-auto flex-bases-350 mr-4"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MaterialInput
                        elementType={'materialSearch'}
                        type={'text'}
                        size={'small'}
                        changed={(e) => handleCourseSearch(e, programIndex)}
                        value={searchCourse.get(programIndex, '')}
                        placeholder={'Search Course Name & ID...'}
                        labelclass={'searchLeft'}
                        addClass="f-12"
                      />
                    </div>
                  </div>
                </AccordionSummary>
                <Divider className="mx-3" />
                {actualExpanded && (
                  <AccordionDetails>
                    <TabContext value={curriculumTab}>
                      <TabList sx={tabBorderNone} onChange={onClickCurriculumTab}>
                        {program
                          .get('curriculum', List())
                          .map((curriculumData, curriculumIndex) => {
                            return (
                              <Tab
                                label={curriculumData.get('curriculum_name', '')}
                                key={curriculumIndex}
                                value={curriculumIndex}
                                sx={textTransform}
                              />
                            );
                          })}
                      </TabList>
                      <TabPanel sx={tabPadding} value={curriculumTab}>
                        <TabContext value={yearTab}>
                          <TabList sx={tabBorderNone} onChange={onClickYearTab}>
                            {yearsList.map((yearData, yearIndex) => {
                              return (
                                <Tab
                                  label={jsUcfirstAll(yearData.get('year', ''))}
                                  key={yearIndex}
                                  value={yearIndex}
                                  sx={textTransform}
                                />
                              );
                            })}
                          </TabList>
                          <TabPanel sx={tabPadding} value={yearTab}>
                            <TabContext value={statusTab}>
                              <TabList sx={tabBorderNone} onChange={onClickStatusTab}>
                                <Tab
                                  label={`Pending (${getCourseListSize(
                                    courseList.get(0, List())
                                  )})`}
                                  value="Pending"
                                  sx={textTransform}
                                />
                                <Tab
                                  label={`Configured (${getCourseListSize(
                                    courseList.get(1, List())
                                  )})`}
                                  value="Configured"
                                  sx={textTransform}
                                />
                              </TabList>
                              <TabPanel sx={tabPadding} value={statusTab}>
                                {courseList
                                  .get(statusTab === 'Pending' ? 0 : 1, IMap())
                                  .isEmpty() ? (
                                  <div className="f-14 text-grey my-2">No Course Found...</div>
                                ) : (
                                  courseList
                                    .get(statusTab === 'Pending' ? 0 : 1, IMap())
                                    .entrySeq()
                                    .map(([key, value], index) => {
                                      if (value.size === 0) return null;
                                      const constructKey = `${programIndex}/${curriculumTab}/${yearTab}/${key}`;
                                      const isToggleChecked = value.some((item) =>
                                        item.get('isEnable', false)
                                      );
                                      const isSharedFromCourse = key === 'shared_from';

                                      const checkConfigured =
                                        checkedData.filter(
                                          (_, key) => Number(key.split('/')[0]) === programIndex
                                        ).size !== 0;

                                      const filterCourse = value.filter((course) =>
                                        course
                                          .get('course_name', '')
                                          .toLowerCase()
                                          .includes(
                                            searchCourse.get(programIndex, '').toLowerCase()
                                          )
                                      );

                                      const mergedData = checkedData.reduce(
                                        (acc, value) => acc.concat(value),
                                        List()
                                      );

                                      return (
                                        <div key={key} className="my-3">
                                          {filterCourse.size !== 0 && (
                                            <div className="d-flex align-items-center my-3">
                                              <div className="d-flex align-items-center flex-grow-1">
                                                <EnableOrDisable valid={statusTab === 'Pending'}>
                                                  {!isSharedFromCourse && (
                                                    <Checkbox
                                                      checked={
                                                        checkedData.get(constructKey, List())
                                                          .size ===
                                                          value.filter((detail) =>
                                                            detail.get('isEnable', false)
                                                          ).size && checkedData.size !== 0
                                                      }
                                                      onChange={handleCheckedCourseTypeWise(
                                                        key,
                                                        value.filter((detail) =>
                                                          detail.get('isEnable', false)
                                                        ),
                                                        ...checkBoxKeyData
                                                      )}
                                                      size="small"
                                                      className="p-0 pr-2"
                                                    />
                                                  )}
                                                </EnableOrDisable>
                                                <div className="f-14 fw-400 text-dGrey ">
                                                  {courseTypeHeading[key]}
                                                </div>
                                              </div>
                                              {checkConfigured &&
                                                checkedData.filter((_, key) => key === constructKey)
                                                  .size !== 0 &&
                                                checkedData.size !== 0 &&
                                                statusTab === 'Pending' &&
                                                !isPublished && (
                                                  <>
                                                    {(checkedData.get(constructKey, List()).size ||
                                                      isToggleChecked) && (
                                                      <MButton
                                                        variant="contained"
                                                        color="primary"
                                                        size={'small'}
                                                        clicked={() =>
                                                          openOrCloseModal(constructKey)
                                                        }
                                                      >
                                                        Configure
                                                      </MButton>
                                                    )}
                                                    <EnableOrDisable
                                                      valid={
                                                        true
                                                        // checkedData.get(constructKey, List()).size
                                                      }
                                                    >
                                                      <FormControlLabel
                                                        className="m-0 ml-2"
                                                        control={
                                                          <Switch
                                                            checked={isToggleChecked}
                                                            onChange={handleToggle({
                                                              constructKey,
                                                              value,
                                                              mergedData,
                                                            })}
                                                            size="small"
                                                            sx={switchSX(isToggleChecked)}
                                                          />
                                                        }
                                                        classes={{
                                                          label: classes.label,
                                                        }}
                                                        label={isToggleChecked ? 'ON' : 'OFF'}
                                                      />
                                                    </EnableOrDisable>
                                                  </>
                                                )}
                                            </div>
                                          )}

                                          {filterCourse.map((course, index) => {
                                            const courseKey = `${course.get(
                                              'courseId',
                                              ''
                                            )}/${course.get('course_name', '')} ${course.get(
                                              'course_code'
                                            )}/${course.get('_id')}`;
                                            const commonAttemptTypeKey = [
                                              'formOccurrence',
                                              programIndex,
                                              'curriculum',
                                              curriculumTab,
                                              'years',
                                              yearTab,
                                              'courses',
                                              1,
                                              key,
                                              index,
                                            ];
                                            const generatingUniqueKeys = constructKey + courseKey;
                                            const isCheckSharedForm = isSharedFromCourse
                                              ? () => {}
                                              : handleToggle;

                                            const sharedWithCourse = course
                                              .getIn(
                                                ['sharedCourses', 'course_assigned_details'],
                                                List()
                                              )
                                              .map((program) => program.get('program_name'))
                                              .join(',');
                                            return (
                                              <div
                                                key={generatingUniqueKeys}
                                                onClick={
                                                  !course.get('isEdited', false)
                                                    ? onClickAccordionCourse(
                                                        generatingUniqueKeys,
                                                        course.get('_id'),
                                                        commonAttemptTypeKey
                                                      )
                                                    : () => {}
                                                }
                                              >
                                                <Accordion
                                                  className="bg-white"
                                                  expanded={generatingUniqueKeys === courseExpanded}
                                                  elevation={0}
                                                  variant="outlined"
                                                >
                                                  <AccordionSummary
                                                    expandIcon={
                                                      !course.get('isEdited', false) ? (
                                                        <ExpandMoreIcon />
                                                      ) : (
                                                        <CloseIcon
                                                          className="ml-2"
                                                          onClick={handleCloseEdit(
                                                            commonAttemptTypeKey.concat([
                                                              'isEdited',
                                                            ])
                                                          )}
                                                        />
                                                      )
                                                    }
                                                    sx={{
                                                      '& .MuiAccordionSummary-content': {
                                                        margin: 0,
                                                      },
                                                    }}
                                                  >
                                                    <div className="d-flex w-100 align-items-center">
                                                      <div
                                                        className="flex-grow-1 d-flex align-items-center"
                                                        onClick={preventPropagation}
                                                      >
                                                        <EnableOrDisable
                                                          valid={statusTab === 'Pending'}
                                                        >
                                                          {!isSharedFromCourse && (
                                                            <Checkbox
                                                              size="small"
                                                              className="p-0 pr-2"
                                                              checked={checkedData
                                                                .get(constructKey, List())
                                                                .includes(courseKey)}
                                                              onChange={handleCheckedIndividual(
                                                                key,
                                                                courseKey,
                                                                ...checkBoxKeyData
                                                              )}
                                                              disabled={
                                                                !course.get('isEnable', false)
                                                              }
                                                            />
                                                          )}
                                                        </EnableOrDisable>
                                                        <div className="d-flex align-items-center">
                                                          {isSharedFromCourse && (
                                                            <ShareOutlinedIcon
                                                              color="primary"
                                                              sx={{
                                                                fontSize: 15,
                                                              }}
                                                              className="mr-2"
                                                            />
                                                          )}

                                                          <div>
                                                            {isSharedFromCourse && (
                                                              <div className="f-10 fw-500 text-capitalize text-grey">
                                                                {course.get('course_type', '')}{' '}
                                                                Courses
                                                              </div>
                                                            )}
                                                            <div className="f-14 fw-500 text-dGrey">
                                                              {course.get('course_name', '')}
                                                            </div>
                                                          </div>
                                                          {isSharedFromCourse && (
                                                            <Tooltips title={sharedWithCourse}>
                                                              <InfoIcon
                                                                sx={{
                                                                  fontSize: 15,
                                                                }}
                                                                className="ml-2"
                                                              />
                                                            </Tooltips>
                                                          )}
                                                        </div>
                                                      </div>

                                                      {!course.get('isEdited', false) ? (
                                                        <EnableOrDisable
                                                          valid={
                                                            !checkedData
                                                              .get(constructKey, List())
                                                              .includes(courseKey)
                                                          }
                                                        >
                                                          <div
                                                            className="pr-3"
                                                            onClick={isCheckSharedForm({
                                                              constructKey,
                                                              value,
                                                              courseIndex: index,
                                                            })}
                                                          >
                                                            <FormControlLabel
                                                              // checked={tag.get('isDefault', false)}
                                                              className="m-0"
                                                              control={
                                                                <Switch
                                                                  size="small"
                                                                  checked={course.get(
                                                                    'isEnable',
                                                                    false
                                                                  )}
                                                                  sx={
                                                                    isSharedFromCourse
                                                                      ? switchSXDisable
                                                                      : switchSX(
                                                                          course.get(
                                                                            'isEnable',
                                                                            false
                                                                          )
                                                                        )
                                                                  }
                                                                />
                                                              }
                                                              classes={{
                                                                label: classes.label,
                                                              }}
                                                              disabled={isSharedFromCourse}
                                                              label={
                                                                course.get('isEnable', false)
                                                                  ? 'ON'
                                                                  : 'OFF'
                                                              }
                                                            />
                                                          </div>
                                                        </EnableOrDisable>
                                                      ) : (
                                                        !isPublished && (
                                                          <SaveButton
                                                            modalState={course.get(
                                                              'occurrences',
                                                              IMap()
                                                            )}
                                                            id={course.get('_id', '')}
                                                            isEdited={commonAttemptTypeKey.concat([
                                                              'isEdited',
                                                            ])}
                                                            prevModalState={course.get(
                                                              'prevOccurrences',
                                                              IMap()
                                                            )}
                                                            isEnable={course.get('isEnable', false)}
                                                            actions={course.getIn(
                                                              ['occurrences', 'actions'],
                                                              List()
                                                            )}
                                                            editConfiguration={editConfiguration}
                                                          />
                                                        )
                                                      )}
                                                    </div>
                                                  </AccordionSummary>
                                                  <Divider className="mx-3" />

                                                  {generatingUniqueKeys === courseExpanded && (
                                                    <ConfiguredDetails
                                                      course={course}
                                                      key={key}
                                                      commonAttemptTypeKey={commonAttemptTypeKey}
                                                      handleChange={handleChange}
                                                      index={index}
                                                      level={level}
                                                      statusTab={statusTab}
                                                    />
                                                  )}
                                                </Accordion>
                                              </div>
                                            );
                                          })}
                                        </div>
                                      );
                                    })
                                )}
                              </TabPanel>
                            </TabContext>
                          </TabPanel>
                        </TabContext>
                      </TabPanel>
                    </TabContext>
                  </AccordionDetails>
                )}
              </Accordion>
            </>
          );
        })}

      {level === 'program' &&
        state.get('formOccurrence', List()).map((program, programIndex) => {
          const accordionExpanded1 = program.get('_program_id', '') === expanded;
          const actualExpanded = accordionExpanded1 || (expanded === 0 && programIndex === 0);
          const checkBoxKeyData = [programIndex];
          const curriculumList = program.get('curriculum', List());

          return (
            <>
              <Accordion
                key={programIndex}
                className="bg-white"
                sx={{ borderRadius: '8px' }}
                expanded={actualExpanded}
                onChange={onClickAccordion(program.get('_program_id', ''))}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel3-content"
                  id="panel3-header"
                >
                  <div className="d-flex align-items-center w-100 mt-2">
                    <div className="fw-400 f-20 text-mGrey">{program.get('program_name', '')}</div>
                  </div>
                </AccordionSummary>
                <Divider className="mx-3" />

                {actualExpanded && (
                  <AccordionDetails>
                    <TabContext value={statusTab}>
                      <TabList sx={tabBorderNone} onChange={onClickStatusTab}>
                        <Tab
                          label={`Pending (${getCurriculumListSize(
                            curriculumList.get(0, List())
                          )})`}
                          value="Pending"
                          sx={textTransform}
                        />
                        <Tab
                          label={`Configured (${getCurriculumListSize(
                            curriculumList.get(1, List())
                          )})`}
                          value="Configured"
                          sx={textTransform}
                        />
                      </TabList>
                      <TabPanel sx={tabPadding} value={statusTab}>
                        {curriculumList.get(statusTab === 'Pending' ? 0 : 1, IMap()).isEmpty() ? (
                          <div className="f-14 text-grey my-2">No Curriculum Found...</div>
                        ) : (
                          curriculumList
                            .get(statusTab === 'Pending' ? 0 : 1, IMap())
                            .entrySeq()
                            .map(([key, value], index) => {
                              if (value.size === 0) return null;
                              const constructKey = `${programIndex}/${key}`;
                              const isToggleChecked = value.every((item) =>
                                item.get('isEnable', false)
                              );
                              const checkConfigured =
                                checkedData.filter(
                                  (_, key) => Number(key.split('/')[0]) === programIndex
                                ).size !== 0;
                              return (
                                <div key={key} className="my-3">
                                  <div className="d-flex align-items-center my-3">
                                    <div className="d-flex align-items-center flex-grow-1">
                                      {/* <EnableOrDisable valid={statusTab === 'Pending'}>
                                        <Checkbox
                                          checked={
                                            checkedData.get(constructKey, List()).size ===
                                            value.size
                                          }
                                          onChange={handleCheckedCourseTypeWise(
                                            key,
                                            value,
                                            ...checkBoxKeyData
                                          )}
                                          size="small"
                                          className="p-0 pr-2"
                                        />
                                      </EnableOrDisable>
                                      <div className="f-14 fw-400 text-dGrey ">All Curriculum</div> */}
                                    </div>
                                    <EnableOrDisable
                                      valid={
                                        index === 0 &&
                                        checkConfigured &&
                                        checkedData.size !== 0 &&
                                        statusTab === 'Pending' &&
                                        !isPublished
                                      }
                                    >
                                      <MButton
                                        variant="contained"
                                        color="primary"
                                        size={'small'}
                                        clicked={() => openOrCloseModal(constructKey)}
                                      >
                                        Configure
                                      </MButton>
                                      <FormControlLabel
                                        className="m-0 ml-2"
                                        control={
                                          <Switch
                                            checked={true}
                                            onChange={handleToggle({ constructKey, value })}
                                            size="small"
                                            sx={switchSX(isToggleChecked)}
                                          />
                                        }
                                        classes={{ label: classes.label }}
                                        label={isToggleChecked ? 'ON' : 'OFF'}
                                      />
                                    </EnableOrDisable>
                                  </div>

                                  {value.map((course, index) => {
                                    const courseKey = `${course.get(
                                      '_curriculum_id',
                                      ''
                                    )}/${course.get('curriculum_name', '')}/${course.get('_id')}`;
                                    const commonAttemptTypeKey = [
                                      'formOccurrence',
                                      programIndex,
                                      'curriculum',
                                      1,
                                      key,
                                      index,
                                    ];

                                    const generatingUniqueKeys = constructKey + courseKey;
                                    return (
                                      <div
                                        key={generatingUniqueKeys}
                                        onClick={onClickAccordionCourse(
                                          generatingUniqueKeys,
                                          course.get('_id'),
                                          commonAttemptTypeKey
                                        )}
                                      >
                                        <Accordion
                                          className="bg-white"
                                          expanded={generatingUniqueKeys === courseExpanded}
                                          elevation={0}
                                          variant="outlined"
                                        >
                                          <AccordionSummary
                                            expandIcon={
                                              !course.get('isEdited', false) ? (
                                                <ExpandMoreIcon />
                                              ) : (
                                                <CloseIcon
                                                  className="ml-2"
                                                  onClick={handleCloseEdit(
                                                    commonAttemptTypeKey.concat(['isEdited'])
                                                  )}
                                                />
                                              )
                                            }
                                            sx={{
                                              '& .MuiAccordionSummary-content': {
                                                margin: 0,
                                              },
                                            }}
                                          >
                                            <div className="d-flex w-100 align-items-center">
                                              <div
                                                className="flex-grow-1 d-flex align-items-center"
                                                onClick={preventPropagation}
                                              >
                                                <EnableOrDisable valid={statusTab === 'Pending'}>
                                                  <Checkbox
                                                    size="small"
                                                    className="p-0 pr-2"
                                                    checked={checkedData
                                                      .get(constructKey, List())
                                                      .includes(courseKey)}
                                                    onChange={handleCheckedIndividual(
                                                      key,
                                                      courseKey,
                                                      ...checkBoxKeyData
                                                    )}
                                                    disabled={!course.get('isEnable', false)}
                                                  />
                                                </EnableOrDisable>
                                                <div className="d-flex align-items-center">
                                                  <div className="f-14 fw-500 text-dGrey ">
                                                    {course.get('curriculum_name', '')}
                                                  </div>
                                                </div>
                                              </div>

                                              {!course.get('isEdited', false) ? (
                                                <EnableOrDisable
                                                  valid={
                                                    !checkedData
                                                      .get(constructKey, List())
                                                      .includes(courseKey)
                                                  }
                                                >
                                                  <div
                                                    className="pr-3"
                                                    onClick={handleToggle({
                                                      constructKey,
                                                      value,
                                                      courseIndex: index,
                                                    })}
                                                  >
                                                    <FormControlLabel
                                                      // checked={tag.get('isDefault', false)}
                                                      className="m-0"
                                                      control={
                                                        <Switch
                                                          size="small"
                                                          checked={course.get('isEnable', false)}
                                                          sx={switchSX(isToggleChecked)}
                                                        />
                                                      }
                                                      classes={{
                                                        label: classes.label,
                                                      }}
                                                      // disabled={statusTab !== 'Pending'}
                                                      label={
                                                        course.get('isEnable', false) ? 'ON' : 'OFF'
                                                      }
                                                    />
                                                  </div>
                                                </EnableOrDisable>
                                              ) : (
                                                !isPublished && (
                                                  <SaveButton
                                                    modalState={course.get('occurrences', IMap())}
                                                    id={course.get('_id', '')}
                                                    isEdited={commonAttemptTypeKey.concat([
                                                      'isEdited',
                                                    ])}
                                                    prevModalState={course.get(
                                                      'prevOccurrences',
                                                      IMap()
                                                    )}
                                                    isEnable={course.get('isEnable', false)}
                                                    actions={course.getIn(
                                                      ['occurrences', 'actions'],
                                                      List()
                                                    )}
                                                    editConfiguration={editConfiguration}
                                                  />
                                                )
                                              )}
                                              {/* <div
                                                className="pr-3"
                                                onClick={handleToggle(constructKey, value, index)}
                                              >
                                                <FormControlLabel
                                                  // checked={tag.get('isDefault', false)}
                                                  className="m-0"
                                                  control={
                                                    <Switch
                                                      size="small"
                                                      checked={course.get('isEnable', false)}
                                                      sx={switchSX}
                                                    />
                                                  }
                                                  classes={{ label: classes.label }}
                                                  label={course.get('isEnable', false) ? 'ON' : 'OFF'}
                                                />
                                              </div> */}
                                            </div>
                                          </AccordionSummary>
                                          <Divider className="mx-3" />
                                          {generatingUniqueKeys === courseExpanded && (
                                            <ConfiguredDetails
                                              course={course}
                                              key={key}
                                              commonAttemptTypeKey={commonAttemptTypeKey}
                                              handleChange={handleChange}
                                              index={index}
                                              level={level}
                                              statusTab={statusTab}
                                              qaPcSetting={qaPcSetting}
                                            />
                                          )}
                                        </Accordion>
                                      </div>
                                    );
                                  })}
                                </div>
                              );
                            })
                        )}
                      </TabPanel>
                    </TabContext>
                  </AccordionDetails>
                )}
              </Accordion>
            </>
          );
        })}

      {level === 'institution' && (
        <>
          <TabContext value={statusTab}>
            <TabList sx={tabBorderNone} onChange={onClickStatusTab}>
              <Tab
                label={`Pending (${getInstitutionListSize(institutionLevel.get(0, List()))})`}
                value="Pending"
                sx={textTransform}
              />
              <Tab
                label={`Configured (${getInstitutionListSize(institutionLevel.get(1, List()))})`}
                value="Configured"
                sx={textTransform}
              />
            </TabList>
            <TabPanel sx={tabPadding} value={statusTab}>
              {institutionLevel.get(statusTab === 'Pending' ? 0 : 1, IMap()).isEmpty() ? (
                <div className="f-14 text-grey my-2">No Institution Found...</div>
              ) : (
                institutionLevel
                  .get(statusTab === 'Pending' ? 0 : 1, IMap())
                  .entrySeq()
                  .map(([key, value], index) => {
                    if (value.size === 0) return null;
                    const constructKey = `${index}/${key}`;

                    return (
                      <div key={key} className="my-3">
                        {value.map((course, index) => {
                          const courseKey = `${course.get(
                            'assignedInstitutionId',
                            ''
                          )}/${course.get('institutionName', '')}/${course.get('_id')}`;
                          const commonAttemptTypeKey = ['formOccurrence', 1, key, index];

                          const generatingUniqueKeys = constructKey + courseKey;

                          return (
                            <div
                              key={generatingUniqueKeys}
                              onClick={onClickAccordionCourse(
                                generatingUniqueKeys,
                                course.get('_id'),
                                commonAttemptTypeKey
                              )}
                            >
                              <Accordion
                                className="bg-white p-2"
                                expanded={generatingUniqueKeys === courseExpanded}
                                elevation={0}
                                variant="outlined"
                              >
                                <AccordionSummary
                                  expandIcon={
                                    !course.get('isEdited', false) ? (
                                      <ExpandMoreIcon />
                                    ) : (
                                      <CloseIcon
                                        className="ml-2"
                                        onClick={handleCloseEdit(
                                          commonAttemptTypeKey.concat(['isEdited'])
                                        )}
                                      />
                                    )
                                  }
                                  sx={{
                                    '& .MuiAccordionSummary-content': {
                                      margin: 0,
                                    },
                                  }}
                                >
                                  <div className="d-flex w-100 align-items-center">
                                    <div
                                      className="flex-grow-1 d-flex align-items-center"
                                      onClick={preventPropagation}
                                    >
                                      <div className="d-flex align-items-center">
                                        <div className="f-14 fw-500 text-dGrey ">
                                          {course.get('institutionName', '')}
                                        </div>
                                      </div>
                                    </div>
                                    {/* <div
                                      className="pr-3"
                                      onClick={handleToggle(constructKey, value, index)}
                                    >
                                      <FormControlLabel
                                        // checked={tag.get('isDefault', false)}
                                        className="m-0 mr-2"
                                        control={
                                          <Switch
                                            size="small"
                                            checked={course.get('isEnable', false)}
                                            sx={switchSX}
                                          />
                                        }
                                        classes={{ label: classes.label }}
                                        label={course.get('isEnable', false) ? 'ON' : 'OFF'}
                                      />
                                    </div> */}

                                    {!course.get('isEdited', false) ? (
                                      <div
                                        className="pr-3"
                                        onClick={handleToggle({
                                          constructKey,
                                          value,
                                          courseIndex: index,
                                        })}
                                      >
                                        <FormControlLabel
                                          // checked={tag.get('isDefault', false)}
                                          className="m-0"
                                          control={
                                            <Switch
                                              size="small"
                                              checked={course.get('isEnable', false)}
                                              sx={switchSX(course.get('isEnable', false))}
                                            />
                                          }
                                          classes={{
                                            label: classes.label,
                                          }}
                                          // disabled={statusTab !== 'Pending'}
                                          label={course.get('isEnable', false) ? 'ON' : 'OFF'}
                                        />
                                      </div>
                                    ) : (
                                      !isPublished && (
                                        <SaveButton
                                          modalState={course.get('occurrences', IMap())}
                                          id={course.get('_id', '')}
                                          isEdited={commonAttemptTypeKey.concat(['isEdited'])}
                                          prevModalState={course.get('prevOccurrences', IMap())}
                                          isEnable={course.get('isEnable', false)}
                                          actions={course.getIn(['occurrences', 'actions'], List())}
                                          editConfiguration={editConfiguration}
                                        />
                                      )
                                    )}

                                    {index === 0 &&
                                      statusTab === 'Pending' &&
                                      course.get('isEnable', false) &&
                                      !isPublished && (
                                        <MButton
                                          variant="contained"
                                          color="primary"
                                          size={'small'}
                                          clicked={handleConfigure(course)}
                                        >
                                          Configure
                                        </MButton>
                                      )}
                                  </div>
                                </AccordionSummary>
                                <Divider className="mx-3" />
                                {generatingUniqueKeys === courseExpanded && (
                                  <ConfiguredDetails
                                    course={course}
                                    key={key}
                                    commonAttemptTypeKey={commonAttemptTypeKey}
                                    handleChange={handleChange}
                                    index={index}
                                    level={level}
                                    statusTab={statusTab}
                                  />
                                )}
                              </Accordion>
                            </div>
                          );
                        })}
                      </div>
                    );
                  })
              )}
            </TabPanel>
          </TabContext>
        </>
      )}

      {modal && (
        <ConfigureModal2
          modal={modal}
          openOrCloseModal={openOrCloseModal}
          constructingForConfiguration={constructingForConfiguration}
          checkedData={checkedData.filter((_, key) => key.split('/')[0] === selectedConfig)}
          setDelete={setCheckedData}
          programName={
            level !== 'institution'
              ? getProgramName()
              : institutionDetails.get('institutionName', '')
          }
          programId={level !== 'institution' ? getProgramId() : ''}
        />
      )}
    </>
  );
}

const SaveButton = ({
  modalState,
  id,
  isEdited,
  prevModalState,
  isEnable,
  actions,
  editConfiguration,
}) => {
  return (
    <MButton
      variant="contained"
      className="px-4"
      color="primary"
      size="small"
      onClick={editConfiguration({
        modalState,
        id,
        isEdited,
        prevModalState,
        isEnable,
        actions,
      })}
    >
      Save
    </MButton>
  );
};
