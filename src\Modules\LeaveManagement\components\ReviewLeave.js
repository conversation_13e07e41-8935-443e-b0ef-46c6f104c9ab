import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { List, Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { <PERSON><PERSON>, Badge, Table } from 'react-bootstrap';

import * as actions from '../../../_reduxapi/leave_management/actions';
import EmployeeModal from '../modal/EmployeeModal';
import ExportModal from '../modal/Exportmodal';
import Pagination from '../../StudentGrouping/components/Pagination';
import {
  selectUserId,
  selectAllProgramLists,
  selectSelectedRole,
} from '../../../_reduxapi/Common/Selectors';
import { selectReviewerList } from '../../../_reduxapi/leave_management/selectors';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import { getTimestamp, jsUcfirst, eString } from '../../../utils';
import { t } from 'i18next';
import LocalStorageService from 'LocalStorageService';

const LEAVE_TYPE = {
  0: 'permission',
  1: 'leave',
  2: 'on_duty',
};
var totalPages;
class ReviewLeave extends Component {
  constructor(props) {
    super(props);
    this.state = {
      completeView: true,
      inactiveView: false,
      ondutyView: true,
      reviewed: false,
      rejected: false,
      pending: false,
      review: false,
      leaveList: [],
      activeTab: 1,
      show: false,
      pageCount: 10,
      currentPage: 1,
      showexport: false,
      employee_id: null,
      flow: null,
      searchTerm: '',
      isFetched: false,
      sortBy: { type: '', orderBy: true },
    };
  }

  inactiveTab = () => {
    this.setState({
      completeView: false,
      inactiveView: true,
      ondutyView: false,
    });
  };
  ondutyTab = () => {
    this.setState({
      completeView: false,
      inactiveView: false,
      ondutyView: true,
    });
  };
  completeTab = () => {
    this.setState({
      completeView: true,
      inactiveView: false,
      ondutyView: false,
    });
  };
  onFullLastClick = () => {
    this.setState({
      currentPage: 1,
    });
  };

  onFullForwardClick = () => {
    this.setState({
      currentPage: totalPages,
    });
  };

  onBackClick = () => {
    if (this.state.currentPage - 1 === 0) {
      this.setState({
        currentPage: 1,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage - 1,
      });
    }
  };

  pagination = (value) => {
    this.setState({
      pageCount: value,
      currentPage: 1,
    });
  };

  exportShow = (status) => {
    this.setState({
      showexport: status,
    });
  };
  ChangeToreview = (status) => {
    if (status === 'approve') {
      this.setState({ reviewed: true });
    }
    if (status === 'rejected') {
      this.setState({ rejected: true });
    }
  };
  componentDidMount() {
    this.props.setBreadCrumbName(t('side_nav.menus.Faculty_Academic_Accountability_Management'));
    this.interval = setInterval(() => {
      const { activeInstitutionCalendar } = this.props;
      if (activeInstitutionCalendar.get('_id', '') !== '') {
        clearInterval(this.interval);
        this.loadInitialData();
      }
    }, 500);
  }
  componentDidUpdate(prevProps) {
    if (
      prevProps?.activeInstitutionCalendar.get('_id', '') !== '' &&
      prevProps?.activeInstitutionCalendar.get('_id', '') !==
        this.props?.activeInstitutionCalendar.get('_id', '')
    ) {
      this.loadInitialData();
    }
  }

  loadInitialData = () => {
    const { userId, activeInstitutionCalendar, selectedRole } = this.props;
    this.setState({ pending: true });
    const institution_id = activeInstitutionCalendar.get('_id');
    let activeid = +LocalStorageService.getCustomToken('activeLeaveid');
    this.setState(
      {
        activeTab: this.state.activeTab === activeid ? this.state.activeTab : activeid,
        flow: 'pending',
      },
      () => {
        this.props.reviewer(
          userId,
          LEAVE_TYPE[this.state.activeTab],
          institution_id,
          selectedRole.getIn(['_role_id', '_id'], '')
        );
      }
    );
  };

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  handleReview = (name) => {
    const { activeInstitutionCalendar, selectedRole } = this.props;
    if (name === 'reviewed') {
      this.setState({ reviewed: true, flow: 'reviewed', rejected: false, pending: false });
    }
    if (name === 'rejected') {
      this.setState({ rejected: true, flow: 'rejected', reviewed: false, pending: false });
    }
    if (name === 'pending') {
      this.setState({ rejected: false, flow: 'pending', reviewed: false, pending: true });
    }

    const institution_id = activeInstitutionCalendar.get('_id');
    this.props.reviewer(
      this.props.userId,
      LEAVE_TYPE[this.state.activeTab],
      institution_id,
      selectedRole.getIn(['_role_id', '_id'], '')
    );
  };

  onNextClick = () => {
    if (this.state.currentPage + 1 >= totalPages) {
      this.setState({
        currentPage: totalPages,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage + 1,
      });
    }
  };

  handleChange = (e) => {
    this.setState({
      searchTerm: e.target.value,
    });
  };
  static getDerivedStateFromProps(props, state) {
    const { activeInstitutionCalendar } = props;
    const institution_calendar = activeInstitutionCalendar.get('_id');
    if (!state.isFetched && institution_calendar) {
      return {
        isFetched: true,
      };
    }
    return null;
  }
  onTabChange(index) {
    const { activeInstitutionCalendar, selectedRole, reviewer, userId } = this.props;
    this.setState(
      {
        activeTab: index,
      },
      () => {
        const institution_id = activeInstitutionCalendar.get('_id');
        const { activeTab } = this.state;
        reviewer(
          userId,
          LEAVE_TYPE[activeTab],
          institution_id,
          selectedRole.getIn(['_role_id', '_id'], '')
        );
        LocalStorageService.setCustomToken('activeLeaveid', activeTab);
      }
    );
  }
  edit = (data, active, tab) => {
    const datalist = data.toJS();
    const list = {
      ...datalist,
      activeTab: active,
      leave_flow: this.state.flow,
    };
    this.props.setLeaveData(list);
    this.props.history.push(
      `/leave-management/${list.leave_flow}/${tab}?id=${list._id}&type=${eString(
        active
      )}&flow=${eString(this.state.flow)}`
    );
  };
  employeedetails = (status, data) => {
    this.setState({ show: status, employee_id: data });
  };
  programdetails = (data) => {
    return (
      <div className="aw-150">
        {' '}
        <b className="ml-4">
          {' '}
          {this.props?.allProgramList
            .filter((item) => item?.id === data)
            .map((item) => {
              return item?.name;
            })}
        </b>
      </div>
    );
  };

  columnSorting = (a, b, name, type = '') => {
    const { sortBy } = this.state;
    if (type === '') {
      return sortBy.orderBy
        ? `${a.getIn(name)}` > `${b.getIn(name)}`
          ? 1
          : -1
        : `${b.getIn(name)}` > `${a.getIn(name)}`
        ? 1
        : -1;
    } else if (type === 'TIME') {
      return sortBy.orderBy
        ? getTimestamp(a.getIn(name)) - getTimestamp(b.getIn(name))
        : getTimestamp(b.getIn(name)) - getTimestamp(a.getIn(name));
    }
  };
  TABS = [
    t('leaveManagement.tabs.Permission'),
    t('leaveManagement.tabs.Leave'),
    t('leaveManagement.tabs.On Duty'),
  ];

  render() {
    const { reviewerList } = this.props;
    const {
      pending,
      reviewed,
      rejected,
      activeTab,
      pageCount,
      currentPage,
      searchTerm,
      flow,
      sortBy,
    } = this.state;
    const list = pending ? 'pending' : reviewed ? 'reviewed' : 'rejected';
    const SearchText = searchTerm.toLowerCase();
    const currentListSize = reviewerList?.getIn([0, flow], List()).size;
    totalPages =
      currentListSize % pageCount === 0
        ? currentListSize / pageCount
        : Math.floor(currentListSize / pageCount) + 1;
    const tab = t(`leaveManagement.tabs.${LEAVE_TYPE[activeTab]}`);
    return (
      <>
        <div className="customize_tab">
          <ul id="menu">
            {this.TABS.map((tab, i) => (
              <span
                key={`${tab}-${i}`}
                onClick={this.onTabChange.bind(this, i)}
                className={`tabaligment${i === activeTab ? ' tabactive' : ''}`}
              >
                {tab}
              </span>
            ))}
          </ul>
        </div>

        <div className="main pt-3 pb-5 bg-white">
          <div className="container">
            <div className="row pb-3">
              <div className="col-md-5">
                <span className="pr-3" onClick={() => this.handleReview('pending')}>
                  <Badge
                    variant={
                      pending === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    {t('Pending')}
                  </Badge>
                </span>
                <span className="pr-3" onClick={() => this.handleReview('reviewed')}>
                  <Badge
                    variant={
                      reviewed === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    {t('leaveManagement.Reviewed')}
                  </Badge>
                </span>
                <span className="pr-3" onClick={() => this.handleReview('rejected')}>
                  <Badge
                    variant={
                      rejected === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    {t('leaveManagement.Rejected')}/{t('leaveManagement.Cancelled')}
                  </Badge>
                </span>
              </div>

              <div className="col-md-7">
                <div className="row">
                  <div className="col-md-10">
                    <div className="sb-example-1">
                      <div className="search">
                        <input
                          type="text"
                          className="searchTerm"
                          placeholder={t('leaveManagement.searchBy_id_name')}
                          onChange={(e) => this.handleChange(e)}
                        />
                        <button type="submit" className="searchButton">
                          <i className="fa fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-2">
                    <div className="float-right">
                      <Button variant="primary" className="f-14" onClick={this.exportShow}>
                        {t('role_management.role_actions.Export')}{' '}
                      </Button>{' '}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <div className="row pt-4">
              <div className="col-md-12">
             */}
            <div className="row pb-3">
              <div className="col-md-12 text-left">
                <b className="f-15 ">{t('leaveManagement.listAllPending', { tab })}</b>
                <div className="dash-table">
                  <Table responsive>
                    <thead className="group_table_top">
                      <tr>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className={`fa fa-sort-amount-${
                                  sortBy.type === 'EMPLOYEE_ID' && !sortBy.orderBy ? 'down' : 'up'
                                } remove_hover pr-2`}
                                onClick={() =>
                                  this.setState({
                                    sortBy: {
                                      type: 'EMPLOYEE_ID',
                                      orderBy: !this.state.sortBy.orderBy,
                                    },
                                  })
                                }
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold font-family-roboto">
                                  {t('employee_id')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className={`fa fa-sort-amount-${
                                  sortBy.type === 'EMPLOYEE_NAME' && !sortBy.orderBy ? 'down' : 'up'
                                } remove_hover pr-2`}
                                onClick={() =>
                                  this.setState({
                                    sortBy: {
                                      type: 'EMPLOYEE_NAME',
                                      orderBy: !this.state.sortBy.orderBy,
                                    },
                                  })
                                }
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold font-family-roboto">
                                  {t('userManagement.emp_name')}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className={`fa fa-sort-amount-${
                                  sortBy.type === 'PROGRAM' && !sortBy.orderBy ? 'down' : 'up'
                                } remove_hover pr-2`}
                                onClick={() =>
                                  this.setState({
                                    sortBy: {
                                      type: 'PROGRAM',
                                      orderBy: !this.state.sortBy.orderBy,
                                    },
                                  })
                                }
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-family-roboto font-weight-bold">
                                  {t('program')}{' '}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>

                        <>
                          <th className="border_color_blue">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className={`fa fa-sort-amount-${
                                    sortBy.type === 'FROM' && !sortBy.orderBy ? 'down' : 'up'
                                  } remove_hover pr-2`}
                                  onClick={() =>
                                    this.setState({
                                      sortBy: {
                                        type: 'FROM',
                                        orderBy: !this.state.sortBy.orderBy,
                                      },
                                    })
                                  }
                                  aria-hidden="true"
                                >
                                  <span className="pl-1 font-family-roboto font-weight-bold ">
                                    {t('from')}{' '}
                                  </span>
                                </i>
                              </li>
                            </div>
                          </th>
                          <th className="border_color_blue">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className={`fa fa-sort-amount-${
                                    sortBy.type === 'TO' && !sortBy.orderBy ? 'down' : 'up'
                                  } remove_hover pr-2`}
                                  onClick={() =>
                                    this.setState({
                                      sortBy: {
                                        type: 'TO',
                                        orderBy: !this.state.sortBy.orderBy,
                                      },
                                    })
                                  }
                                  aria-hidden="true"
                                >
                                  <span className="pl-1 font-family-roboto font-weight-bold">
                                    {LEAVE_TYPE[this.state.activeTab] !== 'permission'
                                      ? t('program_calendar.to')
                                      : t('leaveManagement.time')}
                                  </span>
                                </i>
                              </li>
                            </div>
                          </th>
                          {LEAVE_TYPE[this.state.activeTab] !== 'permission' && (
                            <th className="border_color_blue">
                              <div id="icon_space">
                                <li id="icon_space_li">
                                  {' '}
                                  <i
                                    className={`fa fa-sort-amount-${
                                      sortBy.type === 'DAYS' && !sortBy.orderBy ? 'down' : 'up'
                                    } remove_hover pr-2`}
                                    onClick={() =>
                                      this.setState({
                                        sortBy: {
                                          type: 'DAYS',
                                          orderBy: !this.state.sortBy.orderBy,
                                        },
                                      })
                                    }
                                    aria-hidden="true"
                                  >
                                    <span className="pl-1 font-family-roboto font-weight-bold">
                                      {t('leaveManagement.Total_No_of_days')}
                                    </span>
                                  </i>
                                </li>
                              </div>
                            </th>
                          )}
                        </>

                        {this.state.reviewed && (
                          <th className="border_color_blue">
                            <b className="">{t('status')}</b>
                          </th>
                        )}
                        {LEAVE_TYPE[this.state.activeTab] !== 'permission' && (
                          <th className="border_color_blue">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className={`fa fa-sort-amount-${
                                    sortBy.type === 'LEAVE_TYPE' && !sortBy.orderBy ? 'down' : 'up'
                                  } remove_hover pr-2`}
                                  onClick={() =>
                                    this.setState({
                                      sortBy: {
                                        type: 'LEAVE_TYPE',
                                        orderBy: !this.state.sortBy.orderBy,
                                      },
                                    })
                                  }
                                  aria-hidden="true"
                                >
                                  <span className="pl-1 font-family-roboto font-weight-bold">
                                    {t('leaveManagement.leave Type')}{' '}
                                  </span>
                                </i>
                              </li>
                            </div>
                          </th>
                        )}

                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className={`fa fa-sort-amount-${
                                  sortBy.type === 'TIME_STAMP' && !sortBy.orderBy ? 'down' : 'up'
                                } remove_hover pr-2`}
                                onClick={() =>
                                  this.setState({
                                    sortBy: {
                                      type: 'TIME_STAMP',
                                      orderBy: !this.state.sortBy.orderBy,
                                    },
                                  })
                                }
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-family-roboto font-weight-bold">
                                  {t('leaveManagement.Time_Stamp')}{' '}
                                </span>
                              </i>
                            </li>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {reviewerList
                        ?.getIn([0, `${list}`], List())
                        // eslint-disable-next-line
                        ?.sort((a, b) => {
                          if (sortBy.type === 'EMPLOYEE_ID')
                            return this.columnSorting(a, b, ['staff', 'user_id'], '');
                          else if (sortBy.type === 'EMPLOYEE_NAME')
                            return this.columnSorting(a, b, ['staff', 'name', 'first'], '');
                          else if (sortBy.type === 'PROGRAM')
                            return this.columnSorting(a, b, ['staff', 'program'], '');
                          else if (sortBy.type === 'LEAVE_TYPE')
                            return this.columnSorting(a, b, ['leave_type', 'name'], '');
                          else if (sortBy.type === 'DAYS')
                            return this.columnSorting(a, b, ['days'], '');
                          else if (sortBy.type === 'TIME_STAMP')
                            return this.columnSorting(a, b, ['status_time'], 'TIME');
                          else if (sortBy.type === 'FROM')
                            return this.columnSorting(a, b, ['from'], 'TIME');
                          else if (sortBy.type === 'TO')
                            return this.columnSorting(a, b, ['to'], 'TIME');
                        })
                        ?.filter(
                          (data, i) =>
                            i >= pageCount * (currentPage - 1) && i < pageCount * currentPage
                        )
                        .filter(
                          (data, i) =>
                            data
                              .getIn(['staff', 'name', 'first'], '')
                              .toLowerCase()
                              .includes(SearchText) ||
                            data
                              .getIn(['staff', 'name', 'middle'], '')
                              .toLowerCase()
                              .includes(SearchText) ||
                            data
                              .getIn(['staff', 'name', 'last'], '')
                              .toLowerCase()
                              .includes(SearchText) ||
                            data
                              .getIn(['staff', 'user_id'], '')
                              .toLowerCase()
                              .includes(SearchText) ||
                            `${data.getIn(['staff', 'name', 'first'], '')}${data.getIn(
                              ['staff', 'name', 'middle'],
                              ''
                            )}${data.getIn(['staff', 'name', 'last'], '')}`
                              .toLowerCase()
                              .includes(SearchText)
                        )
                        ?.map((data, i) => {
                          return (
                            <tr
                              key={i}
                              className="tr-bottom-border"
                              onClick={() => {
                                this.edit(data, LEAVE_TYPE[activeTab], activeTab);
                              }}
                            >
                              <td
                                className="text-blue remove_hover"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  this.employeedetails(true, data.get('_user_id'));
                                }}
                              >
                                <p className="pl-20 mb-0">
                                  {data.getIn(['staff', 'user_id'], '')}{' '}
                                </p>
                              </td>
                              <td className="text-blue">
                                <p className="pl-20 mb-0">
                                  {data.getIn(['staff', 'name', 'first'], '') +
                                    data.getIn(['staff', 'name', 'middle'], '') +
                                    data.getIn(['staff', 'name', 'last'], '')}{' '}
                                </p>
                              </td>

                              <td className="text-blue">{data.getIn(['staff', 'program'], '')} </td>
                              <td>
                                <p className="mb-0">
                                  {' '}
                                  {moment(data.get('from')).format('DD MMM	YYYY')}
                                </p>
                              </td>
                              {LEAVE_TYPE[this.state.activeTab] === 'permission' && (
                                <td>
                                  <p className="mb-0">
                                    {moment(data.get('from')).format('hh:mm A')} -
                                    {moment(data.get('to')).format('hh:mm A')}
                                  </p>
                                </td>
                              )}
                              {LEAVE_TYPE[this.state.activeTab] !== 'permission' && (
                                <>
                                  {' '}
                                  <td>
                                    <p className="mb-0">
                                      {' '}
                                      {moment(data.get('to')).format('DD MMM	YYYY')}
                                    </p>
                                  </td>
                                  <td>
                                    <p className=" mb-0">{data.get('days', 0)}</p>
                                  </td>
                                </>
                              )}

                              {this.state.reviewed && (
                                <td>
                                  <p className=" mb-0">
                                    {jsUcfirst(data.get('status')) === 'Approve'
                                      ? 'Approved'
                                      : jsUcfirst(data.get('status'))}
                                  </p>
                                </td>
                              )}
                              {LEAVE_TYPE[this.state.activeTab] !== 'permission' && (
                                <td>
                                  <p className=" mb-0">{data.getIn(['leave_type', 'name'])}</p>
                                </td>
                              )}

                              <td>
                                <p className=" mb-0">
                                  {' '}
                                  {moment(data.get('status_time')).format('hh:mm A DD MMM	YYYY')}
                                </p>
                              </td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </Table>
                  {reviewerList?.getIn([0, flow], List()).size > 0 && (
                    <Pagination
                      pagination={this.pagination}
                      onNextClick={this.onNextClick}
                      pagevalue={pageCount}
                      onBackClick={this.onBackClick}
                      onFullLastClick={this.onFullLastClick}
                      onFullForwardClick={this.onFullForwardClick}
                      data={totalPages}
                      currentPage={currentPage}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
          {this.state.show && (
            <EmployeeModal
              closed={this.employeedetails.bind(this)}
              dataId={this.state.employee_id}
            />
          )}
          {this.state.showexport && (
            <ExportModal
              closed={this.exportShow.bind(this)}
              data={reviewerList?.getIn([0, `${list}`])}
              status={list}
              leavetype={LEAVE_TYPE[this.state.activeTab]}
              SearchText={SearchText}
            />
          )}
        </div>
      </>
    );
  }
}

ReviewLeave.propTypes = {
  setBreadCrumbName: PropTypes.func,
  reviewer: PropTypes.func,
  setLeaveData: PropTypes.func,
  userId: PropTypes.string,
  history: PropTypes.object,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  selectedRole: PropTypes.instanceOf(Map),
  allProgramList: PropTypes.instanceOf(List),
  reviewerList: PropTypes.instanceOf(List),
};

const mapStateToProps = (state) => {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    reviewerList: selectReviewerList(state),
    userId: selectUserId(state),
    allProgramList: selectAllProgramLists(state),
    selectedRole: selectSelectedRole(state),
  };
};
export default compose(withRouter, connect(mapStateToProps, actions))(ReviewLeave);
