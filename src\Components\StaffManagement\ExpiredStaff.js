/* eslint-disable react/jsx-key */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Table, Modal, Button } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import Search from '../../Widgets/Search/Search';
import Pagination from '../StaffManagement/Pagination';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import * as actions from '../../_reduxapi/user_management/action';
import Export from '../../Containers/StudentRegistrationContainer/Export';
import MailContentModal from 'Containers/Modal/MailModal';
class ExpiredStaff extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      registrationConfirmationShow: false,
      mailId: '',
      message: '',
      confirmDelete: false,
      searchText: '',
      limit: 10,
      totalPages: 1,
      pageLength: 1,
    };
    this.timeout = 0;
  }

  componentDidMount() {
    this.fetchApi({});
  }

  successTrigger = (res) => {
    const data = res.data.data.map((data) => {
      return {
        id: data._id,
        username: data.username,
        employee_id: data.employee_id,
        email: data.email,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        gender: data.gender,
        nationality_id: data.address.nationality_id,
        isChecked: false,
      };
    });
    this.setState({
      data: data,
      isLoading: false,
      totalPages: res.data.totalPages,
    });
  };

  fetchApi = ({ limit = 10, pageLength = 1 }) => {
    const { getUserManagementList } = this.props;
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });
    getUserManagementList(
      {
        searchText: searchText,
        type: 'staff',
        status: 'expired',
        limit: limit,
        pageLength: pageLength,
        slug: 'get_all',
      },
      this.successTrigger,
      () => {
        this.setState({
          data: [],
          isLoading: false,
          totalPages: 1,
          pageLength: 1,
        });
      }
    );
  };

  handleClickRegistrationConfirmation = () => {
    this.setState({
      registrationConfirmationShow: true,
    });
  };

  handleClickRegistrationConfirmationClose = () => {
    this.setState({
      registrationConfirmationShow: false,
    });
  };

  handleAllChecked = (event) => {
    let data = this.state.data;
    data.map((data) => (data.isChecked = event.target.checked));
    this.setState({ data: data });
  };

  handleCheckFieldElement = (event, index) => {
    let data = this.state.data;
    data[index].isChecked = event.target.checked;
    this.setState({ data: data });
  };

  handleClickMailPush = () => {
    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });

    if (mailData.length > 0) {
      let id = mailData.map((data) => {
        return data.id;
      });

      this.setState({
        registrationConfirmationShow: true,
        mailId: id,
      });
    } else {
      NotificationManager.error(t('user_management.Choose_atLeast_one_checkbox'), '', 2000);
    }
  };

  handleState = (data1) => {
    this.setState(data1);
  };
  pagination = (e) => {
    this.setState(
      {
        limit: e.target.value,
        pageLength: 1,
      },
      () => {
        setTimeout(() => {
          this.fetchApi({});
        }, 500);
      }
    );
  };

  pageCount = (value) => {
    this.setState(
      {
        pageLength: value,
      },
      () => {
        this.fetchApi({ pageLength: value, limit: this.state.limit });
      }
    );
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
        selectGenderError: '',
      });
    }
  };

  handleEdit = (data) => {
    this.props.history.push({
      pathname: '/staff/profile/edit',
      search: '?id=' + data.id,
      state: this.props.selectedTab,
    });
  };

  handleConfirmDeleteShow = (e, data) => {
    this.setState({
      confirmDelete: true,
      deleteId: data.id,
    });
  };

  handleConfirmDeleteClose = (e) => {
    this.setState({
      confirmDelete: false,
    });
  };

  handleDelete = (e) => {
    this.setState({
      isLoading: true,
    });
    axios
      .delete(`user/${this.state.deleteId}`)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
            confirmDelete: false,
          },
          () => {
            this.fetchApi({});
          }
        );
        this.props.fetchApi();
        NotificationManager.success(t('deleted_successfully'));
      })
      .catch((error) => {
        NotificationManager.danger(`${error.response.data.message}`);
        this.setState({ isLoading: false });
      });
  };

  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      const { limit } = this.state;
      setTimeout(() => {
        this.fetchApi({ limit });
        this.setState({ pageLength: 1 });
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState({ searchText: e.target.value }, () => this.doSearch(e));
  };

  render() {
    const header = [
      <Trans i18nKey={'employee_id'}></Trans>,
      <Trans i18nKey={'email'}></Trans>,
      <Trans i18nKey={'first_name'}></Trans>,
      'Middle Name',
      'Last Name',
      <Trans i18nKey={'gender'}></Trans>,
      <Trans i18nKey={'national_id'}></Trans>,
      <Trans i18nKey={'action'}></Trans>,
    ];
    const totalSelect = this.state.mailId.length;

    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });

    return (
      <div className="main bg-gray pt-2 pb-5">
        <Loader isLoading={this.state.isLoading} />
        <div className="container-fluid">
          <div className="">
            <div className="float-left pt-2 pl-3 ">
              {CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Expired',
                'Send Mail'
              ) && (
                <span className="mr-0">
                  <Button
                    disabled={mailData.length > 0 ? false : true}
                    className={mailData.length > 0 ? '' : 'disabled-icon'}
                    onClick={(e) => this.handleClickMailPush(e)}
                  >
                    <Trans i18nKey={'send_mail'}></Trans>{' '}
                    <i className="fa fa-envelope" aria-hidden="true"></i>
                  </Button>
                </span>
              )}
              {/* mail funtion start   */}

              {this.state.registrationConfirmationShow && (
                <MailContentModal
                  show={this.state.registrationConfirmationShow}
                  hide={this.handleClickRegistrationConfirmationClose}
                  type={'Staff'}
                  state={this.state}
                  setState={this.handleState}
                  totalSelect={totalSelect}
                  fetchApi={this.fetchApi}
                  linkText={'CLICK HERE'}
                />
              )}

              {/* mail funtion end */}

              {/* Delete model start  */}

              <Modal show={this.state.confirmDelete} onHide={this.handleConfirmDeleteClose}>
                <Modal.Header closeButton>
                  <Modal.Title id="example-modal-sizes-title-sm">Delete Staff </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                  <div className="row">
                    <div className="col-md-7 pt-2">
                      <p className="pt-2">
                        {' '}
                        <Trans i18nKey={'user_management.confirm_delete_staff'} />{' '}
                      </p>
                    </div>
                  </div>
                </Modal.Body>
                <Modal.Footer>
                  <Button btnType="Success" onClick={this.handleConfirmDeleteClose}>
                    {' '}
                    <Trans i18nKey={'events.cancel'} />
                  </Button>{' '}
                  <Button btnType="Success" onClick={(e) => this.handleDelete(e)}>
                    {' '}
                    <Trans i18nKey={'confirm'} />
                  </Button>{' '}
                </Modal.Footer>
              </Modal>

              {/* Delete model end  */}
            </div>

            <div className="float-right p-2 d-flex">
              <Export userType="staff" status="expired" />
              {CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Expired',
                'Search'
              ) && <Search value={this.state.searchText} onChange={(e) => this.handleSearch(e)} />}
            </div>
            <div className="clearfix"> </div>

            <React.Fragment>
              <div className="p-3">
                <div className="dash-table">
                  <Table responsive hover>
                    <thead className="th-change">
                      <tr>
                        <th>
                          {' '}
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={this.handleAllChecked}
                            value="checkedall"
                          />
                        </th>
                        {header.map((header, i) => (
                          <th key={i}>{header}</th>
                        ))}
                      </tr>
                    </thead>
                    {this.state.data.length === 0 ? (
                      <TableEmptyMessage />
                    ) : (
                      <tbody>
                        {this.state.data.map((data, index) => (
                          <tr
                            className="tr-change"
                            style={{
                              background: data.isChecked === true ? '#D1F4FF' : '',
                            }}
                          >
                            {data.isChecked !== true ? (
                              <td>
                                <input
                                  type="checkbox"
                                  className="calendarFormRadio"
                                  onClick={(event) => this.handleCheckFieldElement(event, index)}
                                  value="checkedall"
                                />
                              </td>
                            ) : (
                              <td>
                                <input
                                  type="checkbox"
                                  className="calendarFormRadio"
                                  onClick={(event) => this.handleCheckFieldElement(event, index)}
                                  value="checkedall"
                                  checked
                                />
                              </td>
                            )}
                            {/* <td>{data.username}</td> */}
                            <td>{data.employee_id}</td>
                            <td>{data.email}</td>
                            <td>{data.first}</td>
                            <td>{data.middle}</td>
                            <td>{data.last}</td>
                            <td>{data.gender}</td>
                            <td>{data.nationality_id}</td>
                            <td>
                              {CheckPermission(
                                'subTabs',
                                'User Management',
                                'Staff Management',
                                '',
                                'Registration Pending',
                                '',
                                'Expired',
                                'Edit'
                              ) && (
                                <i
                                  className="fa fa-pencil text-darkgray pr-3 f-16 remove_hover"
                                  onClick={() => this.handleEdit(data)}
                                ></i>
                              )}
                              {CheckPermission(
                                'subTabs',
                                'User Management',
                                'Staff Management',
                                '',
                                'Registration Pending',
                                '',
                                'Expired',
                                'Delete'
                              ) && (
                                <i
                                  className="fa fa-trash text-darkgray f-16 remove_hover"
                                  onClick={(e) => this.handleConfirmDeleteShow(e, data)}
                                ></i>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    )}
                  </Table>
                </div>

                <Pagination
                  totalPages={this.state.totalPages}
                  switchPagination={this.pagination}
                  switchPageCount={this.pageCount}
                  pageLength={this.state.pageLength}
                />
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  }
}

ExpiredStaff.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
  selectedTab: PropTypes.number,
  fetchApi: PropTypes.func,
};

export default connect(null, actions)(withRouter(ExpiredStaff));
