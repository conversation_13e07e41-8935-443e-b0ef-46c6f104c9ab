import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';
import moment from 'moment';

import TimeTable from './TimeTable';
import TimeTableWeekPagination from './TimeTablePagination';
import { Button } from '@mui/material';
import { getRotationDates, getWeeksArray } from '../utils';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

import { getFormattedCreditHours, getFormattedCourseDuration } from '../utils';
import {
  capitalize,
  getCollegeLogo,
  getLang,
  getURLParams,
  indVerRename,
  levelRename,
} from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import '../../css/timetable.css';
import { t } from 'i18next';

const lang = getLang();
function CourseTimeTable(props) {
  const {
    timeTableFor,
    programId,
    courseId,
    term,
    level,
    year,
    course,
    yearLevel,
    institutionCalendarId,
    getTimeTableData,
    timeTableData,
    setData,
    academicYear,
    programName,
    courseDetails,
    getIndividualCourseList,
  } = props;
  const isRotation = course.get('rotation', '') === 'yes';
  const [weeks, setWeeks] = useState(List());
  const [activeWeek, setActiveWeek] = useState(Map());
  const [logo, setLogo] = useState('');

  useEffect(() => {
    const logoPath = getCollegeLogo();
    if (logoPath) {
      setLogo(logoPath);
    }
  }, []);

  useEffect(prepareWeeks, [timeTableFor === 'course' ? course : yearLevel]); //eslint-disable-line

  useEffect(() => {
    getIndividualCourseList(course.get('_course_id'), course.get('term'), course.get('level_no'));
  }, [institutionCalendarId, getIndividualCourseList]); // eslint-disable-line

  useEffect(() => {
    if (activeWeek.get('date')) {
      const startDate = activeWeek.get('date') + 'T00:00:00.000Z';
      const endDate = activeWeek.get('endDate') + 'T00:00:00.000Z';
      getTimeTableData({
        type: timeTableFor,
        programId,
        institutionCalendarId,
        ...(timeTableFor === 'course' && { courseId }),
        term,
        level,
        startDate,
        endDate,
      });
    }
    // eslint-disable-next-line
  }, [activeWeek]);

  function prepareWeeks() {
    const { start, end } = getStartEndDate();
    const weeks = fromJS(getWeeksArray(start, end));
    setActiveWeek(weeks.get(0, Map()));
    setWeeks(weeks);
  }

  function getStartEndDate() {
    let data = timeTableFor === 'course' ? course : yearLevel;
    if (!data) {
      data = Map();
    }
    let startDate = data.get('start_date', undefined);
    let endDate = data.get('end_date', undefined);
    if (isRotation) {
      const rotationDates = getRotationDates(course)
        .reduce(
          (rAcc, r) => rAcc.concat(List([moment(r.get('start_date')), moment(r.get('end_date'))])),
          List()
        )
        .toJS();
      startDate = moment.min(rotationDates);
      endDate = moment.max(rotationDates);
    }
    return {
      start: moment(startDate).format('YYYY-MM-DD'),
      end: moment(endDate).format('YYYY-MM-DD'),
    };
  }

  function getActiveWeekIndex() {
    return weeks.findIndex((week) => week.get('weekNumber') === activeWeek.get('weekNumber'));
  }

  function handlePreviousWeekClick() {
    const activeWeekIndex = getActiveWeekIndex();
    if (activeWeekIndex === -1) return;
    if (activeWeekIndex === 0) return;
    setActiveWeek(weeks.get(activeWeekIndex - 1));
  }

  function handleNextWeekClick() {
    const activeWeekIndex = getActiveWeekIndex();
    if (activeWeekIndex === -1) return;
    if (activeWeekIndex === weeks.size - 1) return;
    setActiveWeek(weeks.get(activeWeekIndex + 1));
  }

  function handleWeekClick(week) {
    setActiveWeek(week);
  }

  function exportCourseLevelPdf(activeWeek) {
    setData(Map({ loading: Map({ exportLoading: true }) }));
    document.getElementById('timetable-container').style.overflow = 'unset';
    const levelCourseTable = document.getElementById('level-course-timetable');
    var HTML_Width = levelCourseTable.offsetWidth;
    var HTML_Height = levelCourseTable.offsetHeight;
    var top_left_margin = 15;
    var PDF_Width = HTML_Width + top_left_margin * 2;
    var PDF_Height = PDF_Width * 1.5 + top_left_margin * 2; //1587
    var canvas_image_width = HTML_Width;
    var canvas_image_height = HTML_Height;
    var totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;
    html2canvas(document.getElementById('level-course-timetable'), { allowTaint: true }).then(
      function (canvas) {
        canvas.getContext('2d');
        var imgData = canvas.toDataURL('image/jpeg', 1.0);
        var doc = new jsPDF('p', 'pt', [PDF_Width, PDF_Height]);

        const academicYearName = `Academic Year - ${academicYear.split('-').join(' to ')}`;
        if (timeTableFor === 'course') {
          const courseName = courseDetails.get('courses_name', '');
          const courseCode = courseDetails.get('courses_number', '');
          const courseType = capitalize(courseDetails.get('course_type', ''));
          const courseDuration = getFormattedCourseDuration(courseDetails);
          const creditHours = getFormattedCreditHours(courseDetails);
          const year = courseDetails.get('year_no', '').split('year').join('Year ');
          const level = courseDetails.get('level_no', '');
          const term = `${capitalize(courseDetails.get('term', ''))} ${indVerRename(
            'Term',
            programId
          )}`;
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(12);
          doc.addImage(logo, 'JPEG', 10, 10, 130, 130);
          doc.text(`${courseCode.toUpperCase()} - ${courseName.toUpperCase()}`, 150, 25);
          doc.text(`PROGRAM : ${programName.toUpperCase()}`, 150, 50);
          doc.text(`${academicYearName.toUpperCase()}`, 150, 75);
          doc.text(
            `${year.toUpperCase()} | ${levelRename(
              level,
              programId
            ).toUpperCase()} | ${term.toUpperCase()} | WEEK ${activeWeek.get('weekNumber', 1)}`,
            150,
            100
          );
          doc.text(
            `${indVerRename(courseType, programId)}  |  ${creditHours}  |  ${courseDuration}`,
            150,
            125
          );
          doc.line(10, 140, 1980, 140);
          //doc.addImage(imgData, 'jpeg', 10, 80, imgWidth, imgHeight);
          doc.addImage(
            imgData,
            'JPG',
            top_left_margin,
            155,
            canvas_image_width,
            canvas_image_height
          );
          for (var i = 1; i <= totalPDFPages; i++) {
            doc.addPage();
            doc.addImage(
              imgData,
              'JPG',
              top_left_margin,
              -(PDF_Height * i) + (top_left_margin + 24) * 4,
              canvas_image_width,
              canvas_image_height
            );
          }
          doc.save(`${courseCode}-${courseName}.pdf`);
        } else {
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(12);
          doc.addImage(logo, 'JPEG', 10, 10, 130, 130);
          doc.text(`${programName.toUpperCase()} PROGRAM TIME TABLE`, 150, 50);
          doc.text(`${academicYearName.toUpperCase()}`, 150, 75);
          doc.text(
            `${year.split('year').join('YEAR ')} | ${levelRename(
              level,
              programId
            ).toUpperCase()} | ${term.toUpperCase()} | WEEK ${activeWeek.get('weekNumber', 1)}`,
            150,
            100
          );
          doc.line(10, 140, 1980, 140);
          doc.addImage(
            imgData,
            'JPG',
            top_left_margin,
            155,
            canvas_image_width,
            canvas_image_height
          );
          for (var j = 1; j <= totalPDFPages; j++) {
            doc.addPage();
            doc.addImage(
              imgData,
              'JPG',
              top_left_margin,
              -(PDF_Height * j) + (top_left_margin + 24) * 4,
              canvas_image_width,
              canvas_image_height
            );
          }
          //doc.addImage(imgData, 'jpeg', 10, 70, imgWidth, imgHeight);
          doc.save(`${programName}-${year}-${levelRename(level, programId)}-${term}.pdf`);
        }
        setData(Map({ loading: Map({ exportLoading: false }) }));
        document.getElementById('timetable-container').style.overflow = 'scroll';
        removeElements(document.querySelectorAll('canvas'));
      }
    );

    // html2canvas(document.getElementById('level-course-timetable'),{allowTaint:true})
    //   .then((canvas) => {
    //     canvas.setAttribute('class', 'contentImage');
    //     document.body.appendChild(canvas);
    //   })
    //   .then((canvas) => {
    //     setData(Map({ loading: Map({ exportLoading: false }) }));
    //     var contentImage = document.body.querySelectorAll('canvas');
    //     var imgData = contentImage[0].toDataURL('image/jpeg', 1.0);
    //     var doc = new jsPDF('p', 'mm', 'a3');
    //     var imgWidth = 210;
    //     var imgHeight = (contentImage[0].height * imgWidth) / contentImage[0].width;
    //     const academicYearName = `Academic Year - ${academicYear.split('-').join(' to ')}`;
    //     if (timeTableFor === 'course') {
    //       const courseName = courseDetails.get('courses_name', '');
    //       const courseCode = courseDetails.get('courses_number', '');
    //       const courseType = capitalize(courseDetails.get('course_type', ''));
    //       const courseDuration = getFormattedCourseDuration(courseDetails);
    //       const creditHours = getFormattedCreditHours(courseDetails);
    //       const year = courseDetails.get('year_no', '').split('year').join('Year ');
    //       const level = courseDetails.get('level_no', '');
    //       const term = `${capitalize(courseDetails.get('term', ''))} Term`;
    //       doc.setFont('helvetica', 'bold');
    //       doc.setFontSize(12);
    //       doc.addImage(
    //         Logo,
    //         'JPEG',
    //         10,
    //         10,
    //         50,
    //         50
    //       );
    //       doc.text(`${courseCode.toUpperCase()} - ${courseName.toUpperCase()}`, 60, 20);
    //       doc.text(`PROGRAM : ${programName.toUpperCase()}`, 60, 30);
    //       doc.text(`${academicYearName.toUpperCase()}`, 60, 40);
    //       doc.text(
    //         `${year.toUpperCase()} | ${level.toUpperCase()} | ${term.toUpperCase()} | WEEK ${activeWeek.get(
    //           'weekNumber',
    //           1
    //         )}`,
    //         60,
    //         50
    //       );
    //       doc.text(`${courseType}  |  ${creditHours}  |  ${courseDuration}`, 60, 60);
    //       doc.line(10, 70, 225, 70);
    //       doc.addImage(imgData, 'jpeg', 10, 80, imgWidth, imgHeight);
    //       doc.save(`${courseCode}-${courseName}.pdf`);
    //     } else {
    //       doc.setFont('helvetica', 'bold');
    //       doc.setFontSize(12);
    //       doc.addImage(Logo, 'JPEG', 10, 10, 50, 50);
    //       doc.text(`${programName.toUpperCase()} PROGRAM TIME TABLE`, 60, 20);
    //       doc.text(`${academicYearName.toUpperCase()}`, 60, 30);
    //       doc.text(
    //         `${year
    //           .split('year')
    //           .join(
    //             'YEAR '
    //           )} | ${level.toUpperCase()} | ${term.toUpperCase()} | WEEK ${activeWeek.get(
    //           'weekNumber',
    //           1
    //         )}`,
    //         60,
    //         40
    //       );
    //       doc.line(10, 60, 225, 60);
    //       doc.addImage(imgData, 'jpeg', 10, 70, imgWidth, imgHeight);
    //       doc.save(`${programName}-${year}-${level}-${term}.pdf`);
    //     }
    //     document.getElementById('timetable-container').style.overflow = 'scroll';
    //     removeElements(document.querySelectorAll('canvas'));
    //   });
  }
  const removeElements = (elms) => elms.forEach((el) => el.remove());

  const checkPerm =
    getURLParams('courseId', true) === ''
      ? CheckPermission(
          'tabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'TimeTable',
          'Export'
        )
      : CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Course TimeTable',
          'Export'
        );
  return (
    <>
      <React.Fragment>
        <div className="mr-2">
          {checkPerm && (
            <Button
              style={{
                position: 'absolute',
                ...(lang !== 'ar' ? { right: '15px' } : { left: '15px' }),
                top: getURLParams('courseId', true) === '' ? '90px' : '-45px',
                color: '#3E95EF',
                border: '1px solid rgba(62, 149, 239, 0.5)',
              }}
              variant="outlined"
              color="primary"
              onClick={() => exportCourseLevelPdf(activeWeek)}
              {...(lang !== 'ar'
                ? {
                    startIcon: <i className="fas fa-download" aria-hidden="true"></i>,
                  }
                : { endIcon: <i className="fas fa-download pl-2" aria-hidden="true"></i> })}
            >
              {t('program_calendar.export')}
            </Button>
          )}
        </div>
        <TimeTableWeekPagination
          weeks={weeks}
          activeWeek={activeWeek}
          handleWeekClick={handleWeekClick}
          handlePreviousWeekClick={handlePreviousWeekClick}
          handleNextWeekClick={handleNextWeekClick}
        />
        <div className="mt-3 timetable-container" id="timetable-container">
          <TimeTable
            timeTableData={timeTableData}
            week={activeWeek}
            timeTableFor={timeTableFor}
            exportClicked={exportCourseLevelPdf}
          />
        </div>
      </React.Fragment>
    </>
  );
}

CourseTimeTable.propTypes = {
  timeTableFor: PropTypes.string,
  programId: PropTypes.string,
  courseId: PropTypes.string,
  term: PropTypes.string,
  level: PropTypes.string,
  year: PropTypes.string,
  academicYear: PropTypes.string,
  programName: PropTypes.string,
  course: PropTypes.instanceOf(Map),
  yearLevel: PropTypes.instanceOf(Map),
  institutionCalendarId: PropTypes.string,
  getTimeTableData: PropTypes.func,
  timeTableData: PropTypes.instanceOf(Map),
  courseDetails: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  getIndividualCourseList: PropTypes.func,
};

export default CourseTimeTable;
