import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Map } from 'immutable';

import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import { selectLoading, selectMessage } from '../../_reduxapi/assessment/selector';
import { selectActiveInstitutionCalendar } from '../../_reduxapi/Common/Selectors';
import AssessmentTypes from './components/AssessmentTypes/AssessmentTypes';
import AssessmentProgramsList from './components/AssessmentLibrary/AssessmentProgramsList';
import AssessmentProgramsDetail from './components/AssessmentLibrary/AssessmentProgramsDetail';
import { getURLParams } from '../../utils';
import RegulationsList from './components/Attainment/AttainmentSettings';
import RegulationView from './components/Attainment/RegulationView';
import AttainmentReports from './components/Attainment/Reports';
import AttainmentCourses from './components/Attainment/AttainmentCourses';

class Assessment extends Component {
  render() {
    const { message, loading, activeInstitutionCalendar, history } = this.props;
    const { location } = history;
    const items = [
      {
        to: '#',
        label:
          location?.pathname.includes('/assessment-management/assessment_details') ||
          location?.pathname.includes('/assessment-management/assessment_library')
            ? 'Assessment Management / Assessment Library'
            : location?.pathname.includes('/attainment-calculator/attainment-settings')
            ? 'Attainment Calculator / Attainment Settings'
            : location?.pathname.includes('/attainment-calculator/attainment-reports')
            ? 'Attainment Calculator / Attainment Reports'
            : 'Assessment Management',
      },
    ];
    const programId = getURLParams('pid', true);
    const programName = getURLParams('pname', true);
    return (
      <React.Fragment>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={loading} />
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <Switch>
          <Route exact path="/assessment-management/assessment-types" component={AssessmentTypes} />
          <Route
            exact
            path="/assessment-management/assessment_library"
            component={AssessmentProgramsList}
          />
          <Route
            path="/assessment-management/assessment_details"
            render={() => {
              return (
                <AssessmentProgramsDetail
                  programId={programId}
                  programName={programName}
                  activeInstitutionCalendar={activeInstitutionCalendar}
                />
              );
            }}
          />
          <Route
            exact
            path="/attainment-calculator/attainment-settings"
            render={() => {
              return <AssessmentProgramsList view="regulations" />;
            }}
          />
          <Route
            exact
            path="/attainment-calculator/attainment-settings/regulations"
            component={RegulationsList}
          />
          <Route
            exact
            path="/attainment-calculator/attainment-settings/regulations/:regulationId"
            component={RegulationView}
          />
          <Route
            exact
            path="/attainment-calculator/attainment-reports"
            component={AttainmentReports}
          />
          <Route
            exact
            path="/attainment-calculator/attainment-courses/:regulationId"
            component={AttainmentCourses}
          />
        </Switch>
      </React.Fragment>
    );
  }
}

Assessment.propTypes = {
  message: PropTypes.string,
  loading: PropTypes.bool,
  match: PropTypes.object,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  location: PropTypes.object,
};

const mapStateToProps = function (state) {
  return {
    loading: selectLoading(state),
    message: selectMessage(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(Assessment);
