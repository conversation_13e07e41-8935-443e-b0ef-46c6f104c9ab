.color-light-gray {
  color: rgba(0, 0, 0, 0.54);
}

.level-row {
  border: 1px solid rgba(0, 0, 0, 0.38);
  border-radius: 6px;
}

.level-row:hover {
  border: 1px solid #3e95ef;
  background: #edf6ff;
}

.year-level-course-group:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.54);
}

.course-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.course-row {
  border: 1px solid transparent;
  border-radius: 6px;
}

.course-row:hover {
  border: 1px solid #3e95ef;
  background: #edf6ff;
}

.border-bottom-partial-gray {
  border-bottom: 1px solid rgba(0, 0, 0, 0.54);
}

.w-120 {
  width: 120px;
}

.rotate-180 {
  transform: rotate(180deg);
}

.calendar-icon {
  border: 1px solid #d1f4ff;
  box-shadow: 0px 0px 2px rgb(0 0 0 / 25%);
  border-radius: 4px;
  padding: 5px;
}

.schedule-date-picker-container .react-datepicker-wrapper {
  display: block;
}

.schedule-date-picker-input {
  border-radius: 4px;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.23);
  padding: 10.5px 14px;
  line-height: 19.0016px;
  height: 40px;
  color: rgba(0, 0, 0, 0.87);
}

.schedule-date-picker-input:hover {
  border: 1px solid rgb(0, 0, 0);
}

.blockEvents {
  background: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.06);
}

.dropdown-item.activeItem {
  color: black;
  text-decoration: none;
  background-color: #edf6ff !important;
}

.color-red {
  color: rgb(255, 0, 0);
}

.calendarFont {
  font-size: 94%;
  word-break: break-word;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.opacity-05 {
  opacity: 0.5;
}

.max-width-300 {
  max-width: 300px;
}

.min-width-50 {
  min-width: 50px;
}

.schedule-calendar .fc .fc-timegrid-slot,
.schedule-calendar .fc .fc-timegrid-slot-lane {
  height: 5em;
}
.schedule-calendar td.fc-timegrid-col.fc-day {
  width: 25em;
}
.schedule-calendar th.fc-col-header-cell.fc-day {
  width: 25em;
}

.fc-v-event .fc-event-main {
  height: unset;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
}

.calendar-container .schedule-calendar .fc-view {
  overflow-x: scroll;
}

.calendar-container .schedule-calendar .fc-view > table {
  width: auto;
}

.fc-event {
  font-size: 12px;
}

/* .fc-day-fri,
.fc-day-sat {
  display: none;
} */

.calendarFont b {
  color: black;
  cursor: pointer;
}

.schedule-calendar td[data-time*='00:00:00'],
.schedule-calendar td[data-time*='00:30:00'],
.schedule-calendar td[data-time*='01:00:00'],
.schedule-calendar td[data-time*='01:30:00'],
.schedule-calendar td[data-time*='02:00:00'],
.schedule-calendar td[data-time*='02:30:00'],
.schedule-calendar td[data-time*='03:00:00'],
.schedule-calendar td[data-time*='03:30:00'],
.schedule-calendar td[data-time*='04:00:00'],
.schedule-calendar td[data-time*='04:30:00'],
.schedule-calendar td[data-time*='05:00:00'],
.schedule-calendar td[data-time*='05:30:00'],
.schedule-calendar td[data-time*='06:00:00'],
.schedule-calendar td[data-time*='06:30:00'],
.schedule-calendar td[data-time*='07:00:00'],
.schedule-calendar td[data-time*='07:30:00'],
.schedule-calendar td[data-time*='19:00:00'],
.schedule-calendar td[data-time*='19:30:00'],
.schedule-calendar td[data-time*='20:00:00'],
.schedule-calendar td[data-time*='20:30:00'],
.schedule-calendar td[data-time*='21:00:00'],
.schedule-calendar td[data-time*='21:30:00'],
.schedule-calendar td[data-time*='22:00:00'],
.schedule-calendar td[data-time*='22:30:00'],
.schedule-calendar td[data-time*='23:00:00'],
.schedule-calendar td[data-time*='23:30:00'],
.settings-calendar td[data-time*='00:00:00'],
.settings-calendar td[data-time*='00:30:00'],
.settings-calendar td[data-time*='01:00:00'],
.settings-calendar td[data-time*='01:30:00'],
.settings-calendar td[data-time*='02:00:00'],
.settings-calendar td[data-time*='02:30:00'],
.settings-calendar td[data-time*='03:00:00'],
.settings-calendar td[data-time*='03:30:00'],
.settings-calendar td[data-time*='04:00:00'],
.settings-calendar td[data-time*='04:30:00'],
.settings-calendar td[data-time*='05:00:00'],
.settings-calendar td[data-time*='05:30:00'],
.settings-calendar td[data-time*='06:00:00'],
.settings-calendar td[data-time*='06:30:00'],
.settings-calendar td[data-time*='07:00:00'],
.settings-calendar td[data-time*='07:30:00'],
.settings-calendar td[data-time*='19:00:00'],
.settings-calendar td[data-time*='19:30:00'],
.settings-calendar td[data-time*='20:00:00'],
.settings-calendar td[data-time*='20:30:00'],
.settings-calendar td[data-time*='21:00:00'],
.settings-calendar td[data-time*='21:30:00'],
.settings-calendar td[data-time*='22:00:00'],
.settings-calendar td[data-time*='22:30:00'],
.settings-calendar td[data-time*='23:00:00'],
.settings-calendar td[data-time*='23:30:00'] {
  display: none;
}

.max_hs {
  max-height: 500px;
  overflow: auto;
  background-color: #fafafa;
}

.groupDiv {
  border-top: 1px solid lightgray;
  padding: 5px 0px;
}

.groupDiv span {
  border-right: 1px solid lightgray;
  padding: 7px 4px 12px;
}

.groupDiv span:last-child,
.groupDiv div:last-child {
  border: none;
}

.groupDiv div {
  display: inline-block;
  text-align: center;
  margin: 0 auto;
  border-right: 1px solid lightgray;
}

.courses-scheduled-chip {
  background-color: #edf6ff;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 20px;
  padding: 4px 8px;
  color: rgba(0, 0, 0, 0.38);
  font-size: 14px;
}

.width-400 {
  width: 400px;
}

.popover-content-group {
  margin-bottom: 0.875rem;
}

.popover-content-group > span:first-child {
  color: rgba(0, 0, 0, 0.38);
  font-size: 13px;
}

.popover-content-group > span:last-child {
  color: rgba(0, 0, 0, 0.87);
}

.settings-calendar .fc .fc-timegrid-slot,
.settings-calendar .fc .fc-timegrid-slot-lane {
  height: 4em;
}

.inactive-schedule {
  color: #1a191991;
}

.model_hover:hover {
  background: #9bc3ed45;
}
.model_active {
  background: #9bc3ed45;
}

.flex-wrap-wrap {
  flex-wrap: wrap;
}
.word_break {
  word-break: break-word;
}

.vertical-align-top {
  vertical-align: top !important;
}

.student-search-input-root {
  padding-right: 7px !important;
}

.student-search-input {
  padding-top: 5.25px !important;
  padding-bottom: 5.25px !important;
  padding-left: 7px !important;
  font-size: 0.875rem !important;
}

.student-search-input-end-adornment {
  font-size: 0.875rem !important;
}

.student-group-chip {
  color: #3e95ef;
  border: 1px solid #3e95ef;
  border-radius: 25px;
  cursor: pointer;
  padding-bottom: 0.25rem;
  padding-top: 0.25rem;
  padding-right: 1rem;
  padding-left: 1rem;
  font-size: 12px;
  margin: 0.25rem;
}

.student-group-chip-active {
  color: #000000;
  background-color: #edf6ff;
}

.student-group-chip-disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.w-300px {
  width: 300px;
}

.flex-basis-35 {
  flex-basis: 35%;
}
.flex-basis-65 {
  flex-basis: 65%;
}
.flex-basis-57 {
  flex-basis: 57%;
}

.events-support-session-table-wrapper .table-responsive {
  overflow-x: unset;
}


.top-sticky-drawer{
  position: sticky;
  top: 0px;
  background: white;
  z-index: 99;
}
.PlanningSchedule_H_W{
  height: 135px;
  width: 367px;
  position: absolute;
  top: -45px;
}

.Top_sticky_drawer{
  position:sticky;
  top: 0px;
  background-color: white;
  z-index: 99;
}

.Bottom_sticky_drawer{
  position:sticky;
  bottom: 0px;
}

.Drawer-body{
  overflow: auto;
  height: 100vh;
}

.Drawer-body::-webkit-scrollbar {
  width: 7px;
  height:4px
}

.Drawer-body::-webkit-scrollbar-thumb {
  background-color: #adabab;
  border-radius: 4px;
}

.staffListClass{
  overflow: auto;
 height: calc(100vh - 59vh);
 max-height: 25vh;
}

.staffListClass::-webkit-scrollbar {
  height: 4px;
  width: 4px;
  background: #F3F4F6;

}
.staffListClass::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 10px;
}

.colorLiteGreen{
color: #16A34A;
}

.ds-pointer{
  cursor: pointer;
}

.img-position{
  background-color: rgb(238, 255, 255);
  overflow: auto;
  height: 83px;
  position: relative;
}
.ht-4{
  height: 4.9em;
  overflow: auto;
}
.ht-4::-webkit-scrollbar {
  height: 8px;
  width: 8px;
  background: #F3F4F6;

}
.ht-4::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 10px;
}

.top-sticky-Search{
  overflow: auto;
  height: 90px;
  position: relative;
}
.border-Dash{
  border: 1px solid #147AFC;
  border-style: dashed;
 border-radius: 8px;
}

.student-group-chip-disable-new {
  opacity: 0.5;
  border: 1px solid #3e95ef;
  color: #3e95ef;
}

.position-icon{
  position: absolute;
  right: 3px;
  top: 7px;
}