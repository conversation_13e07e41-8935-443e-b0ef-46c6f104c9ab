import React, { useRef } from 'react';
import { Box, IconButton } from '@mui/material';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import CloseIcon from '@mui/icons-material/Close';
import { resetMessage } from '_reduxapi/studentGroupV2/action';
import { useDispatch } from 'react-redux';
import ExcelJS from 'exceljs';
import PropTypes from 'prop-types';

const boxSx = {
  display: 'flex',
  alignItems: 'stretch',
  fontSize: '14px',
  border: '1px dashed #D1D5DB',
  borderRadius: '8px',
  color: '#6B7280',
  backgroundColor: '#F9FAFB',
  cursor: 'pointer',
  '&:focus-visible': {
    border: '1px solid rgba(0, 0, 0, 0.87)',
  },
};
const uploadButtonSx = {
  padding: '10px 20px',
  borderLeft: 1,
  borderColor: '#D1D5DB',
  backgroundColor: '#fff',
  color: '#147AFC',
  textAlign: 'center',
  borderRadius: '0 8px 8px 0',
};

const FileUpload = ({ filename, templateHeaders, setImportedData }) => {
  const dispatch = useDispatch();
  const inputRef = useRef(null);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
    e.target.value = '';
  };

  const validateTemplate = (headers) => {
    return templateHeaders.some((key, index) => key !== headers[index]);
  };

  const handleFiles = async (files) => {
    resetImportedData();
    const file = files[0];
    if (!file.name.endsWith('.xlsx')) return dispatch(resetMessage('Only .xlsx files are allowed'));

    const arrayBuffer = await file.arrayBuffer();
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);
    const worksheet = workbook.getWorksheet(1);

    const headerRow = worksheet.getRow(1);
    const headers = headerRow.values.slice(1);
    const isTemplateMismatch = validateTemplate(headers);
    if (isTemplateMismatch)
      return dispatch(resetMessage('Format not matched. Check the sample template.'));

    let data = [];
    worksheet.eachRow((row, rowNumber) => {
      const rowValues = row.values;
      rowValues.shift(); // Remove first empty element
      if (rowNumber === 1) return;
      data.push(rowValues);
    });

    setImportedData({ filename: file.name, data });
  };

  const handleClick = () => {
    inputRef.current.click();
  };

  const resetImportedData = () => {
    setImportedData({ filename: '', data: [] });
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') handleClick();
  };

  return (
    <>
      <Box
        tabIndex={0}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        sx={boxSx}
      >
        <p className="p-3 flex-grow-1 align-self-center">
          Drag and drop file here <em>(xlsx)</em>
        </p>
        <Box sx={uploadButtonSx}>
          <FileUploadIcon />
          <p>Upload</p>
        </Box>
        <input
          ref={inputRef}
          type="file"
          accept=".xlsx"
          className="d-none"
          onChange={handleChange}
        />
      </Box>
      {filename && (
        <div className="d-flex align-items-center mt-2">
          <span className="f-14 mr-2">{filename}</span>
          <IconButton sx={{ p: '2px' }} className="gray-neutral" onClick={resetImportedData}>
            <CloseIcon />
          </IconButton>
        </div>
      )}
    </>
  );
};

FileUpload.propTypes = {
  filename: PropTypes.string,
  templateHeaders: PropTypes.array,
  setImportedData: PropTypes.func,
};

export default FileUpload;
