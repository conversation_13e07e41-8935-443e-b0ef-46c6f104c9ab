import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { independentCourseContext } from '../../context';

function IndividualNav({
  sessionActive,
  active,
  notActive,
  title,
  redirection,
  callBack,
  isSelected,
  arrow,
}) {
  const history = useHistory();
  const { getIndependentOverview, institutionId } = useContext(independentCourseContext);
  const { location } = history;
  const match = useRouteMatch();
  function checkActive(link) {
    if (link === 'overView')
      return !checkActive('course-master-list') && !checkActive('session-types')
        ? 'program_active_v2'
        : '';
    const url = location.pathname;
    if (url.includes(link)) return 'program_active_v2';
    return '';
  }
  return (
    <div
      className={`d-flex justify-content-between subject_hover_pi ${checkActive(sessionActive)}`}
      onClick={() => {
        getIndependentOverview(institutionId);
        history.push(`${match.url}${redirection ? '/' + redirection : ''}`);
      }}
    >
      <div className="d-flex w-100">
        <img
          alt="No Img"
          src={checkActive(sessionActive) !== '' ? active : notActive}
          width="12%"
          className="pr-2"
        />{' '}
        <p className="mb-0 text-left">{title}</p>
      </div>
      {arrow && (
        <p className="mb-0">
          <i className={isSelected ? 'fa fa-caret-up' : 'fa fa-caret-down'} aria-hidden="true"></i>
        </p>
      )}
    </div>
  );
}

IndividualNav.propTypes = {
  sessionActive: PropTypes.string,
  title: PropTypes.any,
  redirection: PropTypes.string,
  active: PropTypes.any,
  notActive: PropTypes.any,
  callBack: PropTypes.func,
  isSelected: PropTypes.any,
  arrow: PropTypes.bool,
};
export default IndividualNav;
