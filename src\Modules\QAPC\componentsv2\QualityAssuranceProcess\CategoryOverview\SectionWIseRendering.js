import { Accordion, AccordionDetails, AccordionSummary, Divider } from '@mui/material';
import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { List } from 'immutable';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import CkEditor5 from 'Widgets/CkEditor/CkEditor';
import { fromJS, Map } from 'immutable';
import { useCallApiHook } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import {
  getFormAddendum,
  getStepTwoDetails,
  multipleFileUpload,
  setData,
  getSignedUrl,
} from '_reduxapi/q360/actions';
import { useSelector } from 'react-redux';
import { selectFormAddendum } from '_reduxapi/q360/selectors';
import DeferredInput from './DeferredInput';
import { AlphabetsArray } from 'utils';
import { DrawerRight } from 'Modules/GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step2';
import { PUBLIC_PATH } from 'constants';
import Button from '@mui/material/Button';
import FullscreenIcon from '@mui/icons-material/Fullscreen';

const stickyPosition = { position: 'sticky', top: 0, bottom: 10, zIndex: 1, background: 'white' };
// import ReferenceDocument from './ReferenceDocument';
const SectionWIseRendering = forwardRef(function (
  {
    formInitiatorId,
    isUserPerformedActionWithDocuments,
    categoryFormId,
    isTemplateBased,
    isSectionWise,
    edit,
    documents,
    setDocuments,
    level,
    pdfFunction,
  },
  fetchChildComponentData
) {
  const formAddendum = useSelector(selectFormAddendum);

  const { handleDocType, fileInputRef, setPdfDocuments, pdfDocuments } = pdfFunction;
  // const formTemplateAsString = formAddendum.get('formTemplate', {});
  // const formTemplateAsJson = JSON.parse(formTemplateAsString);
  const handleIframeLoad = () => {
    sendMessageToIframe();
  };
  const iframeRef = useRef(null);
  const iframePdfRef = useRef(null);

  const sendMessageToIframe = () => {
    // Check if iframeRef is defined and not null
    const iframe = iframeRef.current;
    if (iframe) {
      // Access the contentWindow of the iframe and use postMessage to send a message
      const iframeWindow = iframe.contentWindow;
      iframe.style.height = iframeWindow.document.body.scrollHeight + 10 + 'px';
      iframeRef.current.contentWindow.postMessage(
        { values: formAddendum.get('formTemplate', Map()).toJS(), from: 'fromDC' },
        '*'
      );
    }
  };
  const getFormTemplateData = () => {
    let iframe = iframeRef.current;
    // Call the function defined in the iframe to retrieve data
    let data = iframe.contentWindow.dataRetrieve();
    setDocuments((prev) => prev.set('formTemplate', data));
    // console.log({ data, count: JSON.stringify(data) });
    return data;
  };

  const [setDataApi] = useCallApiHook(setData);
  const [fileUpload] = useCallApiHook(multipleFileUpload);
  const [getFormDetails] = useCallApiHook(getFormAddendum);
  const [getStepDetails] = useCallApiHook(getStepTwoDetails);
  const [getUrl] = useCallApiHook(getSignedUrl);

  const callBackUpload = (docs, key, sectionIndex = -1) => {
    if (sectionIndex !== -1) {
      return setDocuments((prev) =>
        prev.updateIn([key, sectionIndex, 'evidenceAttachment'], List(), (attachments) => {
          return attachments.merge(docs);
        })
      );
    }
    setDocuments((prev) =>
      prev.update(key, List(), (attachments) => {
        return attachments.merge(docs);
      })
    );
  };

  useImperativeHandle(
    fetchChildComponentData,
    () => {
      return {
        getFormTemplateData,
      };
    },
    [documents]
  );

  const callBackUploadPdf = (docs) => {
    const doc = docs.get(0, Map());
    const url = doc.get('url', '');
    url &&
      getUrl(url, (url) => {
        setPdfDocuments(doc.set('signedUrl', url).set('documentType', 'pdf'));
      });
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const convertFormData = new FormData();
      convertFormData.append('file', file);
      fileUpload(convertFormData, (data) => callBackUploadPdf(data), 'pdf');
      // Perform further actions with the file
    }
    event.target.value = null;
  };
  const handleInputChange = (type, filesAttachment, sectionId) => (e) => {
    const files = e.target.files;
    if (files.length > 5) {
      return setDataApi({ message: 'You can only upload 5 Document per time' });
    }
    let isUnSupportedFileExistInDocs = false;
    let filesSize = 0;

    // this loop for construct filesAttachment using fileName
    let filesAttachmentWithName = Map();
    for (let i = 0; i < filesAttachment.size; i++) {
      const currentIteratedFile = filesAttachment.get(i, Map());
      filesAttachmentWithName = filesAttachmentWithName.set(currentIteratedFile.get('name'));
    }
    let isDuplicateFileIsExist = false;
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      //this condition for check the upload file are exist old files through the file name
      if (filesAttachmentWithName.has(file.name)) {
        isDuplicateFileIsExist = true;
        break;
      }
      //this condition for check the file type in valid
      const fileType = file.name.split('.').pop();
      filesSize += file.size;
      if (
        !['pdf', 'jpeg', 'jpg', 'png', 'mp4'].includes(fileType) &&
        !isUnSupportedFileExistInDocs
      ) {
        isUnSupportedFileExistInDocs = true;
        break;
      }
    }
    // const isDuplicates = filesAttachment.some((val) => val.get('name', '').includes(filesValues));
    if (isDuplicateFileIsExist) {
      return setDataApi({ message: 'Duplicate Files not allowed with in same name' });
    }
    if (isUnSupportedFileExistInDocs) {
      return setDataApi({ message: 'Upload only pdf, jpeg, jpg, png , video' });
    }
    if (filesSize > 250 * 1024 * 1024) {
      return setDataApi({ message: 'File size exceeds the limit of 250MB' });
    }
    // if (isDuplicates) {
    //   return setDataApi({ message: 'Attachment already exists' });
    // }
    const convertFormData = new FormData();
    for (let i = 0; i < files.length; i++) {
      convertFormData.append('file', files[i]);
    }
    fileUpload(convertFormData, (data) => callBackUpload(data, type, sectionId));
    e.target.value = null;
  };
  const onSectionNameChange = (sectionIndex) => (text) => {
    setDocuments((prev) => prev.setIn(['sectionAttachments', sectionIndex, 'sectionName'], text));
  };

  const handleDeleteSectionAttachment = (sectionIndex) => (docIndex) => {
    setDocuments((prev) =>
      prev.updateIn(
        ['sectionAttachments', sectionIndex, 'evidenceAttachment'],
        List(),
        (evidence) => {
          return evidence.filter((_, i) => i !== docIndex);
        }
      )
    );
  };
  const handleEditor = (sectionIndex) => (editor) => {
    const editorData = editor.getData();
    setDocuments((prev) =>
      prev.setIn(['sectionAttachments', sectionIndex, 'description'], editorData)
    );
  };

  const callBackGetStepDetails = (res) => {
    setDocuments((prev) =>
      prev
        .setIn(['categoryFormId', 'attachments'], res.get('attachments', List()))
        .set('sectionAttachments', res.get('sectionAttachments', List()))
    );
  };

  useEffect(() => {
    if (formAddendum.size) callBackUploadPdf(formAddendum.get('pdfAttachment', List()));
  }, [formAddendum]);

  useEffect(() => {
    //THIS EFFECT FOR CALLING API BASED ON CONDITION
    // [IF USER DOESN'T MAKE ANY ACTION TO THE CURRENT PAGE WE SHOULD CALL THE SETTING API TO FETCH THE DOCUMENT AND SECTION DETAILS
    //ELSE WE SHOULD CALL THE CURRENT CONFIGURE PAGE getApi TO FETCH USER PREVIOUS RESPONSE]
    if (!isUserPerformedActionWithDocuments) {
      getStepDetails(categoryFormId, callBackGetStepDetails);
    } else {
      getFormDetails({
        formInitiatorId,
        cb: (response) => {
          setDocuments(fromJS(response));
        },
      });
    }
  }, []);

  const toggleFullScreen = () => {
    const iframe = iframePdfRef.current;
    if (iframe) {
      if (!document.fullscreenElement) {
        iframe.requestFullscreen().catch((err) => {
          // console.error(`Failed to enable fullscreen mode: ${err.message}`);
        });
      } else {
        document.exitFullscreen();
      }
    }
  };

  const templateUrlObject = {
    course: '/templates/course-report/course_report.html',
    program: '/program-report/program_report.html',
  };
  const subUrl = templateUrlObject[level];
  const isNotInstitution = level !== 'institution';
  const documentType = pdfDocuments.get('documentType', '');
  return (
    <>
      <div className="mb-3 grid_section_with_resource_item">
        <div className="">
          {/* form creation */}
          <div className="flex-grow-1">
            <Accordion
              sx={{
                background: 'white !important',
                '& .MuiAccordionDetails-root': {
                  padding: '0 !important',
                },
                '& .MuiAccordionSummary-content': {
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  margin: '12px 0px 6px 0px',
                },
                '& .css-eqpfi5-MuiAccordionSummary-content.Mui-expanded': {
                  margin: '10px 0px 5px 0px!important',
                },
              }}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={stickyPosition}>
                <div className="grey_shade_1 f-24 ml-2 py-3">
                  <div className="d-flex">
                    <div> Form Creation</div>
                    <div className="d-flex align-items-center justify-content-center ml-2">
                      <Button
                        id="basic-button"
                        sx={{ textTransform: 'none !important' }}
                        onClick={handleDocType('pdf')}
                      >
                        Import form as PDF
                      </Button>
                      <input
                        type="file"
                        accept=".pdf"
                        ref={fileInputRef}
                        style={{ display: 'none' }}
                        onChange={handleFileUpload}
                      />
                    </div>
                  </div>
                </div>
                {edit && (
                  <div>
                    <label htmlFor="file_upload_category">
                      <div className="d-flex mr-3 drag_drop_dashed cursor-pointer">
                        <div className="p-2 pl-3 pr-5 ">
                          <div className="f-16 grey_shade_2">Upload Evidence</div>
                          <div className="f-12 grey_shade_3">
                            PNG, JPG, PDF, Docs... up to 250MB
                          </div>
                        </div>
                        <div className="grey_background_1 px-3 text-center py-2 drag_drop_dashed_left">
                          <FileUploadIcon className="text-primary" />
                          <div className="text-primary f-12">Upload</div>
                        </div>
                      </div>
                    </label>
                    <input
                      multiple
                      onChange={handleInputChange(
                        'formEvidenceAttachment',
                        documents.get('formEvidenceAttachment', List())
                      )}
                      id="file_upload_category"
                      type="file"
                      className="d-none"
                    />
                  </div>
                )}
              </AccordionSummary>
              <AccordionDetails>
                <>
                  <Divider />
                  <div className="p-2">
                    {documentType === 'pdf' ? (
                      <div>
                        <div className="d-flex w-100">
                          <div className="d-flex ml-auto">
                            <div
                              className="mb-2 text-blue cursor-pointer"
                              onClick={() => setPdfDocuments(Map())}
                            >
                              Delete Pdf{' '}
                            </div>
                            <div
                              onClick={toggleFullScreen}
                              className="mb-2 ml-3 text-blue cursor-pointer"
                            >
                              <FullscreenIcon />
                            </div>
                          </div>
                        </div>
                        <iframe
                          src={pdfDocuments.get('signedUrl', '')}
                          title="pdf"
                          className="d-flex w-100 ht_80vh p-2"
                          ref={iframePdfRef}
                        ></iframe>
                      </div>
                    ) : (
                      <>
                        {/* section card */}
                        {isTemplateBased && isNotInstitution && (
                          <iframe
                            ref={iframeRef}
                            className="m-3"
                            src={PUBLIC_PATH + subUrl}
                            title="Face Anomaly Report"
                            width="100%"
                            style={{ border: 'none', overflow: 'auto' }}
                            onLoad={handleIframeLoad}
                            // sandbox=" allow-scripts"
                            id="myIframe"
                          />
                        )}
                        {!isTemplateBased &&
                          documents.get('sectionAttachments', List()).size !== 0 &&
                          documents
                            .get('sectionAttachments', List())
                            .map((section, sectionIndex) => {
                              // const sectionWiseDocument = section.get('evidenceAttachment', List());
                              const sectionWiseDocument = section.get('evidenceAttachment', List());
                              return (
                                <div className="p-3 drag_drop_solid mb-3" key={sectionIndex}>
                                  {isSectionWise && (
                                    <div className="d-flex mb-3 align-items-center">
                                      <div className="flex-grow-1 mr-3">
                                        <p className="f-12 grey_shade_1 mb-2">
                                          Section {AlphabetsArray[sectionIndex]}
                                        </p>
                                        <DeferredInput
                                          sx={{
                                            '& .MuiInputBase-root': {
                                              height: '37px',
                                            },
                                          }}
                                          initialValue={section.get('sectionName', '')}
                                          onDeferChange={onSectionNameChange(sectionIndex)}
                                          disabled={true}
                                        />
                                      </div>
                                      {edit && (
                                        <>
                                          <label
                                            htmlFor={`individual_section_upload_form_initiator_${sectionIndex}`}
                                          >
                                            <div className="text-center mr-3">
                                              <FileUploadIcon className="text-primary" />
                                              <div className="text-primary f-10">Upload</div>
                                              <div className="text-primary f-10">Evidence</div>
                                            </div>
                                          </label>
                                          <input
                                            className="d-none"
                                            multiple
                                            onChange={handleInputChange(
                                              'sectionAttachments',
                                              sectionWiseDocument,
                                              sectionIndex
                                            )}
                                            id={`individual_section_upload_form_initiator_${sectionIndex}`}
                                            type="file"
                                          />
                                        </>
                                      )}
                                      {sectionWiseDocument.size > 0 && (
                                        <>
                                          <Divider
                                            className="mr-2"
                                            orientation="vertical"
                                            variant="middle"
                                            flexItem
                                          />
                                          <AttachFileIcon sx={{ fontSize: '14px' }} />
                                          <span className="f-14 mr-2">
                                            {sectionWiseDocument.size}
                                          </span>
                                          &nbsp;
                                          <DrawerRight
                                            drawerName={'Section Documents'}
                                            filesData={sectionWiseDocument}
                                            handleDeleteAsProps={handleDeleteSectionAttachment(
                                              sectionIndex
                                            )}
                                            edit={edit}
                                          >
                                            <span className="text-primary text-underline f-12">
                                              View
                                            </span>
                                          </DrawerRight>
                                        </>
                                      )}
                                    </div>
                                  )}
                                  <div className="p-3 g-config-scrollbar custom-scroll-ck-editor ">
                                    <CkEditor5
                                      data={section.get('description', '')}
                                      onChange={handleEditor(sectionIndex)}
                                      isShowPremiumFeatures
                                      isReadOnly={!edit}
                                    />
                                  </div>
                                </div>
                              );
                            })}
                      </>
                    )}
                  </div>
                </>
              </AccordionDetails>
            </Accordion>
          </div>
        </div>
      </div>
    </>
  );
});

export default SectionWIseRendering;
