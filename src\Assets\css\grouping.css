#dropdown-split-basic {
  padding-right: 46px;
  background-color: white;
  border: 1px solid #fff;
}

#dropdown-split-basic1 {
  background-color: white;
  border: 1px solid #fff;
}

th.no-borders {
  border: 2px solid #fff;
}

.border-dark-import {
  border: 5px solid #bfbdbd;
}

.border-left-import {
  border-left: 5px solid #bfbdbd;
}

.model-main {
  min-height: 350px;
}

.badge_grouping {
  padding: 10px 15px 10px 15px;
  color: #007bff;
  background-color: #e9ebec;
  border-radius: 16px;
  cursor: pointer;
}

.badge_college {
  padding: 10px 15px 10px 15px;
  color: #000000;
  background-color: #e9ebec;
  border-radius: 16px;
  cursor: pointer;
}

.badge_grouping_active {
  border: 2px solid #007bff;
}

.group_table_top {
  border: 2px solid #ece8e8d4;
}

.group_count {
  border: 2px solid #ced1d4;
  padding: 3px 15px 3px 15px;
  border-radius: 20px;
  color: #acaeb1;
  font-size: 13px;
}

.radio_width {
  width: 200px;
}

.input_bottom {
  padding: 4px 10px 4px 10px;
  border-bottom: 2px solid #cacaca;
}

.tabaligment {
  cursor: pointer;
}

.academic-year-icon {
  cursor: pointer;
  color: rgba(0, 0, 0, 0.54);
}

.tabaligment-blue {
  color: #005cbf !important;
  text-align: center;
  padding: 13px 22px;
  text-decoration: none;
  font-size: 17px;
}

.tabactive-blue {
  border-bottom: 3px solid #005cbf;
}

.tabaligment-blue:hover {
  color: #005cbf;
  text-decoration: none;
  border-bottom: 3px solid #005cbf;
}

.tabaligment-blue-small {
  color: #005cbf !important;
  text-align: center;
  padding: 6px 10px;
  text-decoration: none;
  font-size: 11px;
}

.tabactive-blue-small {
  border-bottom: 3px solid #005cbf;
}

.tabaligment-blue-small:hover {
  color: #005cbf;
  text-decoration: none;
  border-bottom: 3px solid #005cbf;
}

ul#menu-grouping {
  margin-left: -40px;
  height: 25px;
}

ul#menu-grouping-overflow {
  height: 56px;
  white-space: nowrap;
  overflow-x: auto;
  width: 100%;
}

/* width */
#menu-grouping-overflow::-webkit-scrollbar {
  width: 8px;
  height: 6px;
}

/* Track */
#menu-grouping-overflow::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px grey;
  border-radius: 10px;
}

/* Handle */
#menu-grouping-overflow::-webkit-scrollbar-thumb {
  background: #c4c4c4;
  border-radius: 10px;
}

/* Handle on hover */
#menu-grouping-overflow::-webkit-scrollbar-thumb:hover {
  background: #949191;
}

.table-shadow {
  padding: 1px;
  box-shadow: inset 3px 3px 12px -7px rgb(0 0 0 / 41%);
  border: 1px solid #f7f3f3cc;
  border-radius: 8px;
  /* min-height: 150px !important; */
}

.rotation-height {
  height: 175px;
  overflow-y: auto;
}

/* width */
.rotation-height::-webkit-scrollbar {
  width: 8px;
}

/* Track */
.rotation-height::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px grey;
  border-radius: 10px;
}

/* Handle */
.rotation-height::-webkit-scrollbar-thumb {
  background: #c4c4c4;
  border-radius: 10px;
}

/* Handle on hover */
.rotation-height::-webkit-scrollbar-thumb:hover {
  background: #949191;
}

.table-height {
  max-height: 350px;
  overflow-y: auto;
}

#dropdown-split-basic {
  padding-right: 46px;
  background-color: #dadadc;
  border: 1px solid #dadadc;
  padding-left: 1px;
}

#dropdown-split-basic1 {
  background-color: #dadadc;
  border: 1px solid #dadadc;
}

th.no-borders {
  border: 2px solid #dadadc;
  padding: 2px !important;
}

.border-dark-import {
  border: 5px solid #dadadc;
}

.border-left-import {
  border-left: 5px solid #dadadc;
  background: white;
  border-radius: 15px;
  padding: 8px !important;
}

/* width */
.table-height::-webkit-scrollbar {
  width: 8px;
}

/* Track */
.table-height::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px #8080801f;
  border-radius: 10px;
}

/* Handle */
.table-height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.table-height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.switch-field {
  display: flex;
  overflow: hidden;
}

.switch-field input {
  position: absolute !important;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  width: 1px;
  border: 0;
  overflow: hidden;
}

.switch-field label {
  /* background-color: #e4e4e4; */
  color: rgba(0, 0, 0, 0.6);
  font-size: 13px;
  line-height: 1;
  text-align: center;
  padding: 6px 21px;
  margin-right: -1px;
  /* border: 1px solid rgba(0, 0, 0, 0.2);
	box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px rgba(255, 255, 255, 0.1); */
  transition: all 0.1s ease-in-out;
}

.switch-field label:hover {
  cursor: pointer;
}

.switch-field input:checked + label {
  background-color: #4ea1eb;
  box-shadow: none;
  color: white;
}

.switch-field label:first-of-type {
  border-radius: 4px 0 0 4px;
  margin-bottom: 0px;
  border-radius: 20px;
}

.switch-field label:last-of-type {
  border-radius: 0 4px 4px 0;
  margin-bottom: 0px;
  border-radius: 20px;
}

.radioBorder {
  border: 1px solid #4ea1eb;
  padding: 2px;
  border-radius: 19px;
}

.radioTop {
  margin-top: -24px;
}

.tab-list {
  border-bottom: 1px solid #ccc;
  padding-left: 0;
  background-color: #ffffff;
  cursor: pointer;
  margin-bottom: 0px;
}

.tab-list-item {
  display: inline-block;
  list-style: none;
  margin-bottom: -1px;
  padding-top: 15px;
  padding-bottom: 15px;
  border-right: 1px solid #f9f6f6;
}

.tab-list-active {
  background-color: #f9f9f9;
  border: solid #f9f9f9;
  border-width: 1px 35px 0 35px;
  box-shadow: 0px -3px 3px 0px rgb(0 0 0 / 33%);
}

.tab-bg {
  padding: 15px;
}

.bg-light-gray {
  background-color: #f9f9f9;
}

tr.tr-change:hover {
  background: linear-gradient(0deg, rgba(92, 175, 242, 0.2), rgba(92, 175, 242, 0.2)), #ffffff;
}

.td-change:hover::after {
  background: url(../../Assets/trash.png) no-repeat top right;
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  float: right;
  margin: 0 6px 0 0;
  cursor: pointer;
}

.placeholder-message {
  color: rgba(0, 0, 0, 0.38);
}

.placeholder-message h1 {
  font-size: 24px;
  font-weight: 400;
}

.placeholder-message div {
  font-size: 14px;
}

.course-placeholder-message {
  text-align: center;
  margin-top: 3rem;
}

.search-list {
  width: 100%;
  position: relative;
  display: flex;
}

.searchTerm-list {
  min-width: 440px;
  width: 100%;
  padding: 4px 34px 4px 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-sizing: border-box;
  border-radius: 8px;
  height: 38px;
}

.searchButton-list {
  color: #bcc0c0;
  position: relative;
  top: 10px;
  right: 26px;
}

::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(0, 0, 0, 0.38);
}

::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(0, 0, 0, 0.38);
}

:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(0, 0, 0, 0.38);
}

:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(0, 0, 0, 0.38);
}

.jumbotron-view {
  padding: 12px;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}

.max-wd-70 {
  max-width: 70%;
}

@media (min-width: 576px) {
  .modal-sm-90 {
    max-width: 380px;
  }
}

.light-pink {
  background: rgba(235, 87, 87, 0.1);
}

.fc-grey {
  color: rgba(0, 0, 0, 0.6);
}

.outline-primary {
  background: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
}

.primary {
  background: #3e95ef;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
}

.searchIcon {
  right: 10%;
  position: relative;
  color: #bcc0c0;
}

.searchTerm-Infra {
  width: 93%;
  padding: 4px 34px 4px 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-sizing: border-box;
  border-radius: 8px;
  height: 38px;
}

.searchTerm-Infra-rtl {
  width: 93%;
  padding: 4px 4px 4px 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-sizing: border-box;
  border-radius: 8px;
  height: 38px;
}

.staff-model-table-row td div {
  max-width: 200px;
  overflow-wrap: anywhere;
}

.staff-model-table tbody {
  display: block;
  height: 35vh;
  overflow: auto;
}
.staff-model-table thead,
.staff-model-table tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed; /* even columns width , fix width of table too*/
}

.staff_model
  .MuiDialog-container
  .MuiPaper-root.MuiPaper-root.MuiDialog-paper.MuiDialog-paperScrollPaper.MuiDialog-paperWidthLg.MuiPaper-elevation24.MuiPaper-rounded {
  max-width: 70% !important;
  min-height: calc(100% - 100px) !important;
  padding: 20px 40px 0px 40px !important;
}

.staff-model-tablehead {
  background: #f9fafb !important;
}

.staff-model-table tbody::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
.staff-model-table tbody::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 5px;
}

/* Handle */
.staff-model-table tbody::-webkit-scrollbar-thumb {
  background: rgba(196, 196, 196, 0.5);
}

/* Handle on hover */
.staff-model-table tbody::-webkit-scrollbar-thumb:hover {
  background: rgba(196, 196, 196, 0.5);
}

.dropdown_checkbox_label{
  display: flex;
  width: -webkit-fill-available;
}

.dropdown_checkbox_label .MuiButtonBase-root.MuiRadio-root{
  padding: 0px .25rem;
}
