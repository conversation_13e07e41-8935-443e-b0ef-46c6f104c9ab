import React from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import SettingsIcon from '@mui/icons-material/Settings';
import { List, Map } from 'immutable';
import { eString } from 'utils';

import { CheckPermission } from 'Modules/Shared/Permissions';
import { useHistory } from 'react-router-dom';
import LocalStorageService from 'LocalStorageService';

function ReportButtons({ selectValues, optionsList, courseProgramReport, generateReport }) {
  const programId = selectValues.getIn(['program', 'value'], '');
  const attainmentId = selectValues.getIn(['attainment', 'value'], '');
  const institutionCalendarId = selectValues.getIn(['year', 'value'], '');
  const term = selectValues.getIn(['term', 'value'], '');
  const outCome = selectValues.getIn(['outcome', 'value'], '');
  const courseId = selectValues.getIn(['course', 'value'], '');
  const attainmentType = selectValues.getIn(['attainmentType', 'value'], '');
  const programName = selectValues.getIn(['program', 'label'], '');
  const levelNo = selectValues.getIn(['level', 'value'], '');
  const history = useHistory();

  const handleGenerateReport = () => {
    if (programId && attainmentId && institutionCalendarId && term && outCome) {
      generateReport({ programId, attainmentId, outCome, institutionCalendarId, term });
    }
  };

  const handleManageAttainment = (name) => {
    LocalStorageService.setCustomToken('selectValues', JSON.stringify(selectValues));
    LocalStorageService.setCustomToken('optionsList', JSON.stringify(optionsList));
    history.push(
      `/attainment-calculator/${
        name === 'courseList' ? 'attainment-courses' : 'attainment-settings/regulations'
      }/${eString(attainmentId)}?pid=${eString(programId)}&pname=${eString(
        programName
      )}&type=${eString('course')}&outCome=${eString(outCome)}&term=${eString(
        term
      )}&courseId=${eString(courseId)}&year=${eString(institutionCalendarId)}&levelNo=${eString(
        levelNo
      )}&icId=${eString(institutionCalendarId)}`
    );
  };

  return (
    <div className="d-flex border-bottom pb-3">
      <div className="mr-auto">
        {attainmentType && outCome === 'plo' && (
          <MButton variant="contained" color={'white'} className="bold mb-2 mt-3">
            Showing Reports From -{' '}
            {attainmentType === 'Class Attainment'
              ? courseProgramReport.get('curriculumCourses', List())
              : courseProgramReport.get('curriculumCourses', List()).size}{' '}
            Courses{' '}
            <span className="text-blue ml-2" onClick={() => handleManageAttainment('courseList')}>
              View
            </span>
          </MButton>
        )}
      </div>
      <div className="ml-auto d-flex">
        {attainmentType === 'Class Attainment' && outCome === 'plo' && (
          <div className="pr-2">
            <MButton
              variant="contained"
              color={'white'}
              className="bold mb-2 mt-3"
              startIcon={<SettingsIcon />}
              clicked={() => handleGenerateReport()}
            >
              Generate Report
            </MButton>
          </div>
        )}
        {CheckPermission(
          'pages',
          'Attainment Calculator',
          'Attainment Report',
          'Manage Attainment'
        ) && (
          <div className="pr-2">
            <MButton
              variant="contained"
              color={'white'}
              className="bold mb-2 mt-3"
              startIcon={<SettingsIcon />}
              clicked={() => handleManageAttainment()}
              disabled={!attainmentType || outCome === 'plo'}
            >
              Manage Attainment
            </MButton>
          </div>
        )}
        {/* {CheckPermission('pages', 'Attainment Calculator', 'Attainment Report', 'Export') && (
          <div>
            <MButton variant="contained" color={'blue'} className="bold mb-2 mt-3">
              EXPORT
            </MButton>
          </div>
        )} */}
      </div>
    </div>
  );
}

ReportButtons.propTypes = {
  courseProgramReport: PropTypes.instanceOf(Map),
  selectValues: PropTypes.instanceOf(Map),
  optionsList: PropTypes.instanceOf(Map),
  generateReport: PropTypes.func,
};

export default ReportButtons;
