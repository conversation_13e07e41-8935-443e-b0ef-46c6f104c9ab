import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import readXlsxFile from 'read-excel-file';
import { Modal, But<PERSON>, Table } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import { List } from 'immutable';

import ErrorModal from '../../../StudentGrouping/Modal/ErrorModal';
import * as actions from '../../../../_reduxapi/program_input/action';
import { capitalize } from '../../../InfrastructureManagement/utils';
import {
  selectInvalidDataSessionList,
  selectvalidDataSessionList,
} from '../../../../_reduxapi/program_input/selectors';
import { getLang, getURLParams } from '../../../../utils';
import { t } from 'i18next';
import { exportToExcel } from 'Modules/ProgramInput/utils';

const lng = getLang();
class ImportFromSessionFile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      exportShow: true,
      importShow: false,
      importStudent: false,
      importStudentList: false,
      directValid: true,
      chooseNational: '',
      csvRecords: [],
      group1: true,
      group2: false,
      group3: false,
      importAlert: false,
      importTable: false,
      filename: null,
      dataCheckShow: true,
      invalidList: [],
      importFormatError: false,
      importBtnEnable: false,
      programId: getURLParams('_id', true),
      curriculumId: getURLParams('_curriculum_id', true),
    };
  }

  handleChange = (event) => {
    if (
      event.target.files[0].type !==
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      this.setState({ importFormatError: true });
    } else {
      this.setState({ importFormatError: false, tempMismatch: false });
      this.getRecords(event.target.files[0]);
    }
  };

  sampleData = () => {
    const sampleData = [
      {
        [t('program_input.sample_data_headers.Program_Name')]: '',
        [t('program_input.sample_data_headers.Curriculum_Name')]: '',
        [t('program_input.sample_data_headers.Course_Code')]: '',
        [t('program_input.sample_data_headers.Version_Name')]: '',
        [t('program_input.sample_data_headers.Year')]: '',
        [t('program_input.sample_data_headers.Level')]: '',
        [t('program_input.sample_data_headers.Delivery_Type')]: '',
        [t('program_input.sample_data_headers.Delivery_Symbol')]: '',
        [t('program_input.sample_data_headers.Delivery_Number')]: '',
        [t('program_input.sample_data_headers.Delivery_Topic')]: '',
        [t('program_input.sample_data_headers.Subject')]: '',
        [t('program_input.sample_data_headers.Duration_Min')]: '',
        [t('program_input.sample_data_headers.Delivery_Weeks')]: '',
        [t('program_input.sample_data_headers.slo_description')]: '',
      },
    ];

    exportToExcel(sampleData, 'sampleSession');
  };

  handleChangeData = () => {
    this.setState({
      filename: null,
      csvRecords: [],
      importBtnEnable: false,
      tempMismatch: false,
    });
  };
  async getRecords(csvFileObject) {
    const map = {
      Program_Name: 'Program_Name',
      Curriculum_Name: 'Curriculum_Name',
      Course_Code: 'Course_Code',
      Version_Name: 'Version_Name',
      Year: 'Year',
      Level: 'Level',
      Delivery_Type: 'Delivery_Type',
      Delivery_Symbol: 'Delivery_Symbol',
      Delivery_Number: 'Delivery_Number',
      Delivery_Topic: 'Delivery_Topic',
      Subject: 'Subject',
      Duration_Min: 'Duration_Min',
      Delivery_Weeks: 'Delivery_Weeks',
      slo_description: 'slo_description',
    };
    var filename = csvFileObject.name;

    this.setState({ filename });
    let SessionFlow = [
      'Delivery_Type',
      'Delivery_Symbol',
      'Delivery_Number',
      'Delivery_Topic',
      'Subject',
      'Duration_Min',
      'Delivery_Weeks',
      'slo_description',
    ];

    await readXlsxFile(csvFileObject, { map }).then(({ rows }) => {
      if (rows[0] === undefined) {
        this.setState({ tempMismatch: true });
        return true;
      }
      let tempMismatch = Object.keys(rows[0]).filter((element) => SessionFlow.includes(element));
      if (tempMismatch.length === 0) {
        this.setState({ tempMismatch: true });
        return true;
      }

      this.setState({ csvRecords: rows, importBtnEnable: true });
    });
  }

  afterImport = () => {
    const { ImportSessionCsvCheck } = this.props;
    const { csvRecords } = this.state;
    this.setState({
      dataCheckShow: false,
    });
    let data = csvRecords.map((row, index) => {
      return {
        s_no: index + 1,
        Program_Name:
          String(capitalize(row.Program_Name)) === 'undefined'
            ? ''
            : String(capitalize(row.Program_Name)).trim(),
        Curriculum_Name:
          String(capitalize(row.Curriculum_Name)) === 'undefined'
            ? ''
            : String(capitalize(row.Curriculum_Name)).trim(),
        Course_Code:
          String(capitalize(row.Course_Code)) === 'undefined'
            ? ''
            : String(capitalize(row.Course_Code)).trim(),
        Version_Name:
          String(capitalize(row.Version_Name)) === 'undefined'
            ? ''
            : String(capitalize(row.Version_Name)).trim(),
        Year: String(row.Year) === 'undefined' ? '' : String(row.Year).trim(),
        Level:
          String(capitalize(row.Level)) === 'undefined' ? '' : String(capitalize(row.Level)).trim(),
        Delivery_Type:
          String(capitalize(row.Delivery_Type)) === 'undefined'
            ? ''
            : String(capitalize(row.Delivery_Type)).trim(),
        Delivery_Symbol:
          String(capitalize(row.Delivery_Symbol)) === 'undefined'
            ? ''
            : String(capitalize(row.Delivery_Symbol)).trim(),
        Delivery_Number: row.Delivery_Number === undefined ? '' : row.Delivery_Number,
        Delivery_Topic:
          String(capitalize(row.Delivery_Topic)) === 'undefined'
            ? ''
            : String(capitalize(row.Delivery_Topic)).trim(),
        Subject:
          String(capitalize(row.Subject)) === 'undefined'
            ? ''
            : String(capitalize(row.Subject)).trim(),
        Duration_Min: row.Duration_Min === undefined ? '' : row.Duration_Min,
        Delivery_Weeks:
          String(row.Delivery_Weeks) === 'undefined' ? '' : String(row.Delivery_Weeks).trim(),
        slo_description:
          String(capitalize(row.slo_description)) === 'undefined'
            ? ''
            : String(capitalize(row.slo_description)).trim(),
      };
    });
    let body = {
      session_flow: data,
    };

    ImportSessionCsvCheck(
      `digi_course/data_check_import_session_flow`,
      body,
      () => {
        this.inValidDataCheck();
      },
      () => {
        this.validDataCheck();
      }
    );
  };

  validDataCheck = () => {
    this.props.closed(false);
  };
  fileName = () => {
    this.setState({
      filename: null,
      tempMismatch: false,
    });
  };

  importContinue = () => {
    const { ValidSessionDataList, ImportSessionCsv } = this.props;

    if (ValidSessionDataList === undefined) {
      this.props.closed(false);
    }

    if (ValidSessionDataList !== undefined) {
      let listData = this.props.validSessionDataList && this.props.validSessionDataList.toJS();

      let data = listData.map((row) => {
        return {
          Program_Name:
            String(capitalize(row.data.Program_Name)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Program_Name)).trim(),
          Curriculum_Name:
            String(capitalize(row.data.Curriculum_Name)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Curriculum_Name)).trim(),
          Course_Code:
            String(capitalize(row.data.Course_Code)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Course_Code)).trim(),
          Version_Name:
            String(capitalize(row.data.Version_Name)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Version_Name)).trim(),
          Year: String(row.data.Year) === 'undefined' ? '' : String(row.data.Year).trim(),
          Level:
            String(capitalize(row.data.Level)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Level)).trim(),
          Delivery_Type:
            String(capitalize(row.data.Delivery_Type)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Delivery_Type)).trim(),
          Delivery_Symbol:
            String(capitalize(row.data.Delivery_Symbol)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Delivery_Symbol)).trim(),
          Delivery_Number: row.data.Delivery_Number === undefined ? '' : row.data.Delivery_Number,
          Delivery_Topic:
            String(capitalize(row.data.Delivery_Topic)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Delivery_Topic)).trim(),
          Subject:
            String(capitalize(row.data.Subject)) === 'undefined'
              ? ''
              : String(capitalize(row.data.Subject)).trim(),
          Duration_Min: row.data.Duration_Min === undefined ? '' : row.data.Duration_Min,
          Delivery_Weeks:
            String(row.data.Delivery_Weeks) === 'undefined'
              ? ''
              : String(row.data.Delivery_Weeks).trim(),
          slo_description:
            String(capitalize(row.data.slo_description)) === 'undefined'
              ? ''
              : String(capitalize(row.data.slo_description)).trim(),
        };
      });
      let body = {
        session_flow: data,
      };

      ImportSessionCsv(`digi_course/import_session_flow`, body, () => {
        this.props.closed(false);
      });
    }
  };
  importListBack = () => {
    this.setState({
      dataCheckShow: true,
      directValid: true,
    });
  };
  exportList = () => {
    let listData = this.props.InvalidSessionDataList && this.props.InvalidSessionDataList.toJS();

    let data = listData.map((row) => {
      return {
        Program_Name: row.data.Program_Name,
        Curriculum_Name: row.data.Curriculum_Name,
        Course_Code: row.data.Course_Code,
        Version_Name: row.data.Version_Name,
        Year: row.data.Year,
        Level: row.data.Level,
        Delivery_Type: row.data.Delivery_Type,
        Delivery_Symbol: row.data.Delivery_Symbol,
        Delivery_Number: row.data.Delivery_Number,
        Delivery_Topic: row.data.Delivery_Topic,
        Subject: row.data.Subject,
        Duration_Min: row.data.Duration_Min,
        Delivery_Weeks: row.data.Delivery_Weeks,
        slo_description: row.data.slo_description,
      };
    });

    exportToExcel(data, 'Session-invalid-list');
  };
  cancelBtn = () => {
    this.setState(
      {
        dataCheckShow: false,
      },
      () => {
        this.props.closed(false);
      }
    );
  };

  inValidDataCheck = () => {
    this.setState({ directValid: false });
  };
  render() {
    const {
      filename,
      csvRecords,
      directValid,
      dataCheckShow,
      importFormatError,
      tempMismatch,
      importBtnEnable,
    } = this.state;
    const { InvalidSessionDataList, ValidSessionDataList } = this.props;

    return (
      <div className="main pt-3 pb-5 bg-white">
        <Modal show={dataCheckShow} dialogClassName="model-800">
          <div className="container">
            <p className="f-20 mb-2 pt-3">
              {' '}
              <Trans i18nKey={'import_session'}></Trans>
            </p>
          </div>

          <Modal.Body>
            <div className="model-main bg-gray pl-3 border-radious-8">
              <p className="f-14 mb-2 pt-2 text-gray">
                {' '}
                <Trans i18nKey={'select_appropriate'}></Trans>
              </p>

              <div className="row pb-5">
                <div className="col-md-5">
                  <b className="f-16 mb-4 pt-3">
                    <Trans i18nKey={'select_a_file'}></Trans>
                    <br></br>
                    <small>
                      {' '}
                      <Trans i18nKey={'accepted_formats'}></Trans>
                    </small>
                  </b>
                  {filename === null ? (
                    ''
                  ) : (
                    <div className="bg-lightdark border-radious-8 w-75 ">
                      <b className=" pl-2 pr-2 f-14" onClick={this.handleChangeData}>
                        {filename.length > 15 ? filename.substring(0, 4) + '...' : filename}{' '}
                        <i
                          className={`fa fa-times remove_hover import_remove_icon ${
                            lng === 'ar' ? 'float-right' : ''
                          }`}
                          aria-hidden="true"
                        ></i>
                      </b>
                    </div>
                  )}
                </div>

                <div className="col-md-7">
                  <b className="pl-5">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary  import-padding border-radious-8"
                    >
                      <Trans i18nKey={'browse'}></Trans>{' '}
                      <input
                        id="fileUpload"
                        type="file"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        value=""
                        onChange={this.handleChange}
                      />
                    </label>
                  </b>

                  <Link>
                    <b className="pl-5 text-blue" onClick={() => this.sampleData()}>
                      {' '}
                      <i className="fa fa-download pr-2" aria-hidden="true"></i>
                      <Trans i18nKey={'download_template'}></Trans>
                    </b>
                  </Link>
                </div>
              </div>

              <div className="border-radious-8 bg-lightgray">
                <div className="row d-flex justify-content-center">
                  <div className="col-md-11 pt-5 ">
                    <div className="p-2 bg-lightdark border-radious-8">
                      <Table className="">
                        <thead>
                          <tr className="no-border">
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'Curriculum_Name'}></Trans>{' '}
                              </Button>
                            </th>

                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'Course_Code'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'year'}></Trans>
                              </Button>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {csvRecords &&
                            csvRecords
                              .filter((val, i) => i < 3)
                              .map((item, i) => {
                                return (
                                  <tr className="border-dark-import" key={i}>
                                    <td className="border-left-import">{item.Program_Name}</td>
                                    <td className="border-left-import">{item.Curriculum_Name}</td>

                                    <td className="border-left-import">{item.Course_Code}</td>
                                    <td className="border-left-import">{item.Year}</td>
                                  </tr>
                                );
                              })}
                        </tbody>
                      </Table>
                    </div>
                  </div>

                  <div className="col-md-11 pt-2 pb-2">
                    <b className="float-right f-14">
                      {csvRecords.length} <Trans i18nKey={'rows_import'}></Trans>{' '}
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <div className={`${lng === 'ar' ? 'pl-4' : 'pr-4'}`}>
              <p className="text-blue mb-0 remove_hover" onClick={this.cancelBtn}>
                {' '}
                <Trans i18nKey={'cancel'}></Trans>{' '}
              </p>
            </div>

            <div className="pr-2">
              <Button
                variant={importBtnEnable && csvRecords.length > 0 ? 'primary' : 'secondary'}
                disabled={importBtnEnable && csvRecords.length > 0 ? false : true}
                onClick={this.afterImport}
              >
                <Trans i18nKey={'import'}></Trans>
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {!directValid && (
          <Modal show={true} size="lg">
            <div className="container">
              <p className="f-20 mb-1 pt-3">
                {' '}
                <Trans i18nKey={'import_session'}></Trans>
              </p>

              <p className="f-14 mb-2 ">
                {InvalidSessionDataList?.size} <Trans i18nKey={'of'}></Trans> {csvRecords.length}{' '}
                <Trans i18nKey={'not_imported'}></Trans>{' '}
              </p>
            </div>

            <Modal.Body>
              <div className="pb-1">
                <p className="f-16 mb-2">
                  {' '}
                  <Trans i18nKey={'data_check'}></Trans>
                </p>
                <div className="row">
                  <div className="col-md-6">
                    <p className="f-14 mb-2">
                      {' '}
                      <Trans i18nKey={'list_error_entity'}></Trans>
                    </p>
                  </div>
                  <p className="f-14 mb-2 float-right text-blue" onClick={() => this.exportList()}>
                    <Trans i18nKey={'export_entry'}></Trans>
                  </p>
                </div>

                <div className="go-wrapper">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>
                          <div className="aw-75">
                            <Trans i18nKey={'s_no'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-100">
                            <Trans i18nKey={'program_name'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-200">
                            <Trans
                              i18nKey={'program_input.sample_data_headers.Curriculum_Name'}
                            ></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-300">
                            <Trans i18nKey={'error_message'}></Trans>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="go-wrapper-height tr-change">
                      {InvalidSessionDataList &&
                        InvalidSessionDataList.map((item, i) => {
                          return (
                            <tr key={i}>
                              <td>
                                <div className="aw-75">{i + 1}</div>
                              </td>
                              <td>
                                <div className="aw-100">{item.getIn(['data', 'Program_Name'])}</div>
                              </td>
                              <td>
                                <div className="aw-200">
                                  {item.getIn(['data', 'Curriculum_Name'])}
                                </div>
                              </td>

                              <td>
                                <div className="aw-300">
                                  {item.getIn(['message']).map((message, i) => (
                                    <li key={i}>{message}</li>
                                  ))}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <div className="pr-4">
                <p className="text-blue mb-0 remove_hover" onClick={this.importListBack}>
                  {' '}
                  <Trans i18nKey={'back'}></Trans>{' '}
                </p>
              </div>

              <div className="pr-2">
                <Button
                  variant={
                    ValidSessionDataList && ValidSessionDataList.size > 0 ? 'primary' : 'Secondary'
                  }
                  onClick={this.importContinue}
                  disabled={ValidSessionDataList && ValidSessionDataList.size === 0}
                >
                  <Trans i18nKey={'continue'}></Trans>
                </Button>
              </div>
            </Modal.Footer>
          </Modal>
        )}
        {importFormatError && (
          <div>
            <ErrorModal
              showDetail={importFormatError}
              title={t('modal.messages.invalid_format')}
              content={t('modal.messages.upload_error')}
              filename={this.fileName}
            />
          </div>
        )}

        {tempMismatch && (
          <div>
            <ErrorModal
              showDetail={tempMismatch}
              title={t('modal.messages.template_mismatch')}
              content={t('modal.messages.template_content')}
              filename={this.fileName}
            />
          </div>
        )}
      </div>
    );
  }
}

ImportFromSessionFile.propTypes = {
  ImportSessionCsvCheck: PropTypes.func,
  closed: PropTypes.func,
  validSessionDataList: PropTypes.instanceOf(List),
  ImportSessionCsv: PropTypes.func,
  InvalidSessionDataList: PropTypes.instanceOf(List),
  ValidSessionDataList: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    InvalidSessionDataList: selectInvalidDataSessionList(state),
    ValidSessionDataList: selectvalidDataSessionList(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ImportFromSessionFile);
