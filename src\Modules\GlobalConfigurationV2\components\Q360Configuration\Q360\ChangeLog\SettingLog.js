import React from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { Drawer, Table, TableBody, TableRow, TableCell, TableHead, Box } from '@mui/material';
import { muiPaperSx } from 'Modules/QAPC/componentsv2/QualityAssuranceApproverFlow/approvalUtils';

export const SettingLogs = ({ open, handleClose }) => {
  return (
    <Drawer anchor="right" open={open} className="rightDrawer" sx={muiPaperSx}>
      <div className="close-btn" onClick={handleClose}>
        <CloseIcon />
        <div className="close-btn-bottom"></div>
      </div>
      <div className="border-bottom f-18 py-2 px-2 ">
        <div className="f-18 fw-500">Change Log For Settings</div>
        <div className="f-12 text-gray">
          Fundamentals of Human Body Curriculum / Program Name 01 / All Groups
        </div>
      </div>
      <Box
        className="py-4 g-config-scrollbar more-details-accordion-scroll px-3 "
        sx={{ background: '#f9fafb' }}
      >
        <Table>
          <TableHead className="fixed-header">
            <TableRow className="bg-white">
              <TableCell>
                <div className="f-14 text-black fw-500">Approval Level</div>
              </TableCell>

              <TableCell sx={{ width: '300px' }}>
                <div className="f-14 text-black fw-500">Action</div>
              </TableCell>

              <TableCell>
                <div className="f-14 text-black fw-500">Time</div>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow className="bg-transparent" sx={{ '& td': { borderBottom: 'none' } }}>
              <TableCell>
                <div className="f-14 text-black fw-500">Today - 31st Jan 2023</div>
              </TableCell>
            </TableRow>
            {new Array(20).fill(1).map((item) => (
              <TableRow className="bg-white" key={item}>
                <TableCell>Level 1</TableCell>
                <TableCell>
                  <div className="p-2 rounded bg-grey">
                    <div>
                      Reason will be display here Reason will e...
                      <span className="f-12 text-primary text_underline">Show More</span>
                    </div>
                    <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                  </div>
                </TableCell>
                <TableCell>12:05 PM</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Box>
    </Drawer>
  );
};
