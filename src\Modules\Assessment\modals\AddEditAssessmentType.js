import React, { useEffect, useState } from 'react';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { assessmentValidation } from '../utils';
import { ucFirst } from 'utils';

const AddEditAssessmentTypeModal = (props) => {
  const {
    show,
    handleModalClose,
    selectedSubType,
    selectedType,
    typeId,
    handleSubmit,
    getAssessmentTypesList,
    assessment,
    mode,
    assessmentTypes,
    setData,
  } = props;
  const [name, setName] = useState(assessment?.get('name', ''));
  const [edited, setEdited] = useState(false);
  const [namesArray, setNamesArray] = useState(List());

  const assmtId = assessment?.get('_id', '');
  useEffect(() => {
    const tempNamesArray = assessmentTypes
      .get('types', List())
      .filter((item, index) => item.get('typeName', '') === selectedType)
      .reduce((_, el) => el)
      .get('subTypes', List())
      .filter((subItem, subIndex) => subItem.get('typeName', '') === selectedSubType)
      .reduce((_, subEl) => subEl)
      .get('assessmentTypes', List())
      .map((xy) => xy.get('name', '').toLowerCase());
    tempNamesArray.size > 0 && setNamesArray(tempNamesArray);
  }, []); //eslint-disable-line

  const handleChange = (e) => {
    setEdited(true);
    if (assessment?.get('name', '') !== '' && assessment?.get('name', '') === e.target.value) {
      setEdited(false);
    }

    setName(e.target.value);
  };
  const toggleCallback = () => {
    getAssessmentTypesList();
    handleModalClose();
  };
  const handleSave = () => {
    const requestBody = fromJS({
      _id: typeId,
      type: selectedType,
      subType: selectedSubType,
      assessmentName: name.trim(),
      ...(mode === 'edit' && { assessmentId: assmtId }),
    });
    const newNamesArray = namesArray.filter((item) => item !== assessment?.get('name', ''));
    assessmentValidation(
      name.trim(),
      assessment?.get('name', '') !== '' ? newNamesArray : namesArray,
      setData,
      'Assessment Type'
    ) && handleSubmit(requestBody, () => toggleCallback());
  };
  return (
    <MaterialDialog show={show} onClose={handleModalClose} maxWidth={'xs'} fullWidth={true}>
      <div className="w-100 p-4">
        <p className="mb-3 pb-2 border-bottom bold f-19">
          {mode === 'edit'
            ? `Edit ${assessment?.get('name', '')}`
            : `${ucFirst(selectedSubType)} - Create New Assessment`}
        </p>
        <div className="mt-2 mb-2 pb-2">
          <MaterialInput
            elementType={'materialInput'}
            type={'text'}
            variant={'outlined'}
            size={'small'}
            label={'Assessment Name'}
            value={name}
            placeholder={'Name'}
            changed={(e) => handleChange(e)}
            maxLength={100}
          />
        </div>

        <div className="d-flex justify-content-end border-top pt-3">
          <MButton variant="outlined" color="primary" className={'mr-2'} clicked={handleModalClose}>
            Cancel
          </MButton>
          <MButton
            variant="contained"
            color="primary"
            clicked={handleSave}
            disabled={!edited || !name}
          >
            Save
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
};
AddEditAssessmentTypeModal.propTypes = {
  show: PropTypes.bool,
  handleModalClose: PropTypes.func,
  selectedSubType: PropTypes.string,
  selectedType: PropTypes.string,
  typeId: PropTypes.string,
  handleSubmit: PropTypes.func,
  getAssessmentTypesList: PropTypes.func,
  assessment: PropTypes.instanceOf(Map),
  mode: PropTypes.string,
  assessmentTypes: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  handleAddModalClose: PropTypes.func,
};
export default AddEditAssessmentTypeModal;
