import React, { useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { getLang } from 'utils';
import { useHistory } from 'react-router-dom';
import { connect } from 'react-redux';
import { Map } from 'immutable';

import { selectIndependentOverview, selectIsLoading } from '_reduxapi/program_input/v2/selectors';
import * as actions1 from '_reduxapi/program_input/v2/actions';
import * as actions2 from '_reduxapi/global_configuration/actions';

import { getInstitutionHeader } from 'v2/utils';
import IndependentCourseNav from './Components/IndependentCourseNav/IndependentCourseSideNavIndex';
import IndependentCourseBody from './Components/IndependentCourseBody';
import { independentCourseContext } from './context';
import { getTransLatedLabelName } from './ICutil';

function IndependentCourseIndex({
  getIndependentOverview,
  independentOverview,
  settingsData,
  isLoading,
  getIndependentCourse,
}) {
  const history = useHistory();
  const institutionHeader = getInstitutionHeader(history, isLoading);
  const institutionId = institutionHeader?._institution_id;
  useEffect(() => {
    getIndependentOverview(institutionId);
    getIndependentCourse({ header: institutionHeader, uId: institutionId });
  }, []); // eslint-disable-line

  const getToolTipData = useMemo(() => getTransLatedLabelName(), [settingsData]); // eslint-disable-line
  function checkActive(link) {
    const url = history.location.pathname;
    if (url.includes(link)) return false;
    return true;
  }
  const isCourseConfigActive =
    checkActive('/course-master-list/configuration') &&
    checkActive('/course-master-list/view-course');
  const value = {
    independentOverview,
    institutionHeader,
    institutionId,
    getIndependentOverview,
    getToolTipData,
  };
  return (
    <independentCourseContext.Provider value={value}>
      <div className="main pb-5">
        <div>
          <div className="d-flex pb-4">
            {isCourseConfigActive && (
              <div className={`${getLang() === 'ar' ? 'sideBar_program' : 'pr-0'}`}>
                <div className="pi_sidebar">
                  <IndependentCourseNav />
                </div>
              </div>
            )}
            <div className={`border-left w-100`}>
              <div className="pb-0">
                <IndependentCourseBody />
              </div>
            </div>
          </div>
        </div>
      </div>
    </independentCourseContext.Provider>
  );
}
const mapStateToProps = (state) => {
  return {
    independentOverview: selectIndependentOverview(state),
    isLoading: selectIsLoading(state),
  };
};
IndependentCourseIndex.propTypes = {
  getIndependentOverview: PropTypes.func,
  getIndependentCourse: PropTypes.func,
  independentOverview: PropTypes.instanceOf(Map),
  isLoading: PropTypes.bool,
  settingsData: PropTypes.instanceOf(Map),
};
export default connect(mapStateToProps, { ...actions1, ...actions2 })(IndependentCourseIndex);
