
  export function handleImageChange(name,e) {
    // e.preventDefault();

    if (name === "appointment") {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }
      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== "image") {
        return;
      }
      if (file !== null && file !== undefined) {
        reader.onloadend = () => {
          this.setState({
            appointment: file,
            appointmentError: "",
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === "degreeImage") {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }
      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== "image") {
        return;
      }
      if (file !== null && file !== undefined) {
        reader.onloadend = () => {
          this.setState({
            degreeImage: file,
            degreeImageError: "",
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === "addressImage") {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }
      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== "image") {
        return;
      }
      if (file !== null && file !== undefined) {
        reader.onloadend = () => {
          this.setState({
            addressImage: file,
            addressImageError: "",
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === "collegeImage") {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }
      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== "image") {
        return;
      }
      if (file !== null && file !== undefined) {
        reader.onloadend = () => {
          this.setState({
            collegeImage: file,
            collegeImageError: "",
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === "residentImage") {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }
      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== "image") {
        return;
      }
      if (file !== null && file !== undefined) {
        reader.onloadend = () => {
          this.setState({
            residentImage: file,
            residentImageError: "",
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === "address1") {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }
      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== "image") {
        return;
      }
      if (file !== null && file !== undefined) {
        reader.onloadend = () => {
          this.setState({
            address1: file,
            address1Error: "",
          });
        };
        reader.readAsDataURL(file);
      }
    }
  }

  export function handleChange(e, name) {
    e.preventDefault();

    if (name === "degree") {
      this.setState({
        degree: e.target.value,
        degreeError: "",
      });
    }
  };