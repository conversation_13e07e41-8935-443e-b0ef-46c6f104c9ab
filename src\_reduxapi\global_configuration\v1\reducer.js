import { t } from 'i18next';
import { fromJS } from 'immutable';
import * as actions from './actions';

const initialState = fromJS({
  isLoading: false,
  message: '',
  breadcrumbs: {},
  globalSessionSetting: {},
  tardis: [],
  late_config: {},
  programList: [],
  sessionStatusDetails: {},
  performanceTableFilter: {},
  performanceTableBody: {},
  leaderBoardSetting: {},
  tagMasterSettings: {},
  ///------------------QA_PC_SETTING_DATA
  uploadFileUrl: {},
  calenderList: [],
  configureTemplate: [],
  attemptType: [],
  qapcPrograms: {},
  qapcProgramDetails: {},
  programCount: 0,
  categoryForms: [],
  totalCategoryForms: 0,
  referenceForm: [],
});

export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_GLOBAL_SESSION_SETTING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_GLOBAL_SESSION_SETTING_SUCCESS: {
      return state.set('isLoading', false).set('globalSessionSetting', fromJS(action.data));
    }
    case actions.GET_GLOBAL_SESSION_SETTING_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }

    case actions.POST_GLOBAL_SESSION_SETTING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_GLOBAL_SESSION_SETTING_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Saved Successfully');
    }
    case actions.POST_GLOBAL_SESSION_SETTING_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }

    case actions.ADD_TARDIS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_TARDIS_SUCCESS: {
      const { data } = action;
      return state
        .set('isLoading', false)
        .set('message', `${data}` === 'CREATED_SUCCESSFULLY' ? 'Created Successfully' : `${data}`);
    }
    case actions.ADD_TARDIS_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          `${response.data.message}` === 'DUPLICATE_NAME_FOUND'
            ? 'Duplicate name found'
            : `${response.data.message}`
        );
    }

    case actions.GET_TARDIS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_TARDIS_SUCCESS: {
      return state.set('isLoading', false).set('tardis', fromJS(action.data));
    }
    case actions.GET_TARDIS_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }

    case actions.DELETE_TARDIS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_TARDIS_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Deleted Successfully');
    }
    case actions.DELETE_TARDIS_FAILURE: {
      const { response } = action.error;
      return state.set('isLoading', false).set('message', response.message);
    }
    ////////////////// ***** LATE CONFIG ******* ///////////
    case actions.GET_LATE_CONFIG_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_LATE_CONFIG_SUCCESS: {
      return state.set('isLoading', false).set('late_config', fromJS(action.data));
    }
    case actions.GET_LATE_CONFIG_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.SET_LATE_CONFIG_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SET_LATE_CONFIG_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated SuccessFully');
    }
    case actions.SET_LATE_CONFIG_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    ////////////////// ***** SESSION STATUS DETAILS******* ///////////
    case actions.GET_SESSION_STATUS_DETAILS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SESSION_STATUS_DETAILS_SUCCESS: {
      return state.set('isLoading', false).set('sessionStatusDetails', fromJS(action.data));
    }
    case actions.GET_SESSION_STATUS_DETAILS_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.SET_SESSION_STATUS_DETAILS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SET_SESSION_STATUS_DETAILS_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated SuccessFully');
    }
    case actions.SET_SESSION_STATUS_DETAILS_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    // ------------------------------------LeaderBoard setting
    case actions.ADD_PARAMETER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_PARAMETER_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', (action.method === 'post' ? 'Created' : 'Updated') + ' Successfully');
    }
    case actions.ADD_PARAMETER_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.DELETE_PARAMETER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_PARAMETER_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Deleted Successfully');
    }
    case actions.DELETE_PARAMETER_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.GET_PARAMETER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PARAMETER_SUCCESS: {
      return state.set('isLoading', false).set('leaderBoardSetting', fromJS(action.data));
    }
    case actions.GET_PARAMETER_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.SAVE_LEADER_BOARD_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_LEADER_BOARD_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.SAVE_LEADER_BOARD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.GET_PROGRAM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('programList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_PERFORMANCE_TABLE_FILTER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PERFORMANCE_TABLE_FILTER_SUCCESS: {
      return state.set('isLoading', false).set('performanceTableFilter', fromJS(action.data));
    }
    case actions.GET_PERFORMANCE_TABLE_FILTER_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_PERFORMANCE_TABLE_BODY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PERFORMANCE_TABLE_BODY_SUCCESS: {
      return state.set('isLoading', false).set('performanceTableBody', fromJS(action.data));
    }
    case actions.GET_PERFORMANCE_TABLE_BODY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.SAVE_SCHEDULE_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_SCHEDULE_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.SAVE_SCHEDULE_SETTINGS_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }

    ////////////////// ***** SURVEY ******* ///////////

    case actions.POST_TAG_MASTER_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_TAG_MASTER_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.POST_TAG_MASTER_SETTINGS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.GET_TAG_MASTER_SETTINGS_REQUEST: {
      return state.set('isLoading', true).set('tagMasterSettings', fromJS({}));
    }
    case actions.GET_TAG_MASTER_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('tagMasterSettings', fromJS(action.data));
    }
    case actions.GET_TAG_MASTER_SETTINGS_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.DELETE_TAG_MASTER_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_TAG_MASTER_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Deleted Successfully');
    }
    case actions.DELETE_TAG_MASTER_SETTINGS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.UPDATE_REMARKS_VISIBILITY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_REMARKS_VISIBILITY_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('global_configuration.action_response_messages.updated_successfully'));
    }
    case actions.UPDATE_REMARKS_VISIBILITY_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.GET_REMARKS_VISIBILITY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_REMARKS_VISIBILITY_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_REMARKS_VISIBILITY_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }

    case actions.PUT_UPDATE_SCHEDULE_PERMISSION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.PUT_UPDATE_SCHEDULE_PERMISSION_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.PUT_UPDATE_SCHEDULE_PERMISSION_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated Successfully');
    }
    case actions.UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;

      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.UPDATE_HANDOUT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_HANDOUT_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.UPDATE_HANDOUT_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_HANDOUT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_HANDOUT_SUCCESS: {
      return state.set('isLoading', false).set('courseHandout', fromJS(action.data));
    }
    case actions.GET_HANDOUT_FAILURE: {
      return state.set('isLoading', false);
    }

    default:
      return state;
  }
}

function getErrorMessage(action) {
  const { response: { data: { message = '' } = {} } = {} } = action.error;
  return message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
}
