.icon_remove_leave.card-header::before {
  content: '';
}

.icon_remove_leave.card-header {
  background-color: white;
  border-top-left-radius: 11px;
  border-top-right-radius: 11px;
  border-bottom: 1px solid rgb(0 0 0 / 0%);
}

.innerbodyLeave.card-body {
  padding: 0px 0px 0px 5px !important;
}

.sb-example-2 .search {
  width: 100%;
  position: relative;
  display: flex;
}

.sb-example-2 .searchTerm {
  width: 100%;
  color: #9e9e9e;
  border-right: none;
  border-bottom: 1px solid;
  border-top: none;
  border-left: none;
  background: #ffffff00;
}

.sb-example-2 .searchButton {
  width: 30px;
  height: 37px;
  background: #ffffff00;
  text-align: center;
  color: #9e9e9e;
  cursor: pointer;
  font-size: 14px;
  border-right: none;
  border-bottom: 1px solid;
  border-top: none;
  border-left: none;
}

.tab-list-white {
  border-bottom: 1px solid #ccc;
  padding-left: 0;
  background-color: #ffffff;
  cursor: pointer;
  margin-bottom: 0px;
}

.tab-list-item-white.tab-list-active-white span {
  background-color: #ffffff;
  border: solid #ffffff;
  border-width: 1px 10px 2px 10px;
  box-shadow: 3px -2px 5px 1px rgb(0 0 0 / 33%);
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.tab-list-item-white span {
  margin-bottom: 0px;
  padding-top: 15px;
  padding-bottom: 15px;
  background: rgba(0, 0, 0, 0.08); /*#f9f9f991;*/
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.tab-list-item-white {
  display: inline-block;
  list-style: none;
  border-right: 5px solid #ffffff;
  padding-right: 5px;
  padding-bottom: 14px;
}

.border-light {
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
  box-shadow: 2px 2px 6px 0px rgb(0 0 0 / 17%);
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}

/* input#siva_input {
  max-width: 38%;
  display: none;
} */

/* width */
.leaveManage::-webkit-scrollbar {
  height: 10px;
  width: 10px;
}

/* Handle */
.leaveManage::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.leaveManage::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.leaveManage-height::-webkit-scrollbar {
  width: 7px;
}

/* Handle */
.leaveManage-height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.leaveManage-height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.leaveManage-width::-webkit-scrollbar {
  height: 7px;
}

/* Handle */
.leaveManage-width::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.leaveManage-width::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.leaveManage-height {
  overflow-y: auto;
  max-height: 250px;
}

.leaveManage {
  overflow-x: auto;
  width: 100%;
}

.leaveManage table {
  width: 100%;
}

.leaveManage table tbody {
  display: block;
  overflow-x: hidden;
}

.leaveManage table thead {
  display: table;
}

.leaveManage table tfoot {
  display: table;
}

.leaveManage table thead tr,
.leaveManage table tbody tr,
.leaveManage table tfoot tr {
  display: table-row;
}

/* .go-wrapper table th,
.go-wrapper table td { 
  white-space: nowrap; 
} */

.leaveManage .aw-50 {
  min-height: 1px;
  width: 50px;
  word-wrap: break-word;
}

.leaveManage .aw-100 {
  min-height: 1px;
  width: 100px;
  word-wrap: break-word;
}
.leaveManage .aw-118 {
  min-height: 1px;
  width: 118px;
  word-wrap: break-word;
}

.leaveManage .aw-150 {
  min-height: 1px;
  width: 150px;
  word-wrap: break-word;
}

.leaveManage .aw-200 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}

.leaveManage .aw-300 {
  min-height: 1px;
  width: 300px;
  word-wrap: break-word;
}

.leaveManage .aw-400 {
  min-height: 1px;
  width: 400px;
  word-wrap: break-word;
}

.reviw-bg-grey {
  background: rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  padding: 20px;
}

.reviw-batch {
  background: #d1f4ff;
  border-radius: 8px;
  padding: 10px;
  font-size: 16px;
}

.dropdown-flex.dropdown-menu.show {
  display: flex;
  z-index: 10000;
}

.light-grey {
  background: rgba(0, 0, 0, 0.06);
}

.table_background {
  background-color: #f9f9f9f2;
  padding: 15px 10px 15px 26px;
  border-radius: 6px;
}

.box_lightgreen {
  background: #d1f4ff;
  border-radius: 8px;
  padding: 15px;
}

.box_milghtgray {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  padding: 15px;
}

.browse-button {
  width: 150px;
}

.padding-left-5 {
  padding-left: 5px;
}

.padding-right-5 {
  padding-right: 5px;
}

.no-wrap {
  white-space: nowrap;
}
.w-10 {
  width: 10%;
}
.w-15 {
  width: 15%;
}

.leave_caleder {
  position: absolute;
  top: 1.9em;
  right: 17px;
}
.remove_icon {
  position: absolute;
  top: 2.2em;
  right: 18px;
  cursor: pointer;
}
.w-100px {
  width: 100px;
}
.w-45px {
  width: 45px;
}
.w-40 {
  width: 40%;
}

.tab-list-white li {
  margin-top: 15px;
}
