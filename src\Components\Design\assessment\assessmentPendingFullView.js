import React, { useState } from 'react';
import SettingsIcon from '@mui/icons-material/Settings';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useStylesFunction } from './designUtils';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Menu,
  MenuItem,
  IconButton,
  ListItem,
  List,
  ListItemText,
  Collapse,
  ListItemIcon,
  FormControlLabel,
  Checkbox,
  Divider,
  Typography,
} from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import PeopleIcon from '@mui/icons-material/People';
function AssessmentType(props) {
  const classes = useStylesFunction();
  const [expanded, setExpanded] = useState(true);
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  const [expandeds, setExpandeds] = useState(true);
  const handleChanges = (panel2) => (event, isExpandeds) => {
    setExpandeds(isExpandeds ? panel2 : false);
  };
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const options = ['Delete', 'Edit'];
  const ITEM_HEIGHT = 48;

  const [show, setShow] = useState(false);
  const handleShow = () => {
    setShow(true);
  };
  const handleModalClose = () => {
    setShow(false);
  };
  const [AddShow, setAddShow] = useState(false);
  const handleAddShow = () => {
    setAddShow(true);
  };
  const handleAddModalClose = () => {
    setAddShow(false);
  };

  const programType = [
    {
      name: 'all',
      value: 'all',
    },
    {
      name: 'Course Level',
      value: 'Course Level',
    },
  ];

  const [open1, setOpen] = React.useState(true);

  const handleClicks = () => {
    setOpen(!open1);
  };

  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="bg-white p-3 border-bottom-2px">
        <div className="container-fluid">
          <p className="font-weight-bold mb-0 text-left f-17">
            <i className="fa fa-arrow-left pr-3 remove_hover" aria-hidden="true"></i>Fundamentals of
            Human b...
          </p>
        </div>
      </div>

      <div className="container pl-0">
        <div className="w-100">
          <div className="p-3">
            <p className="mb-2 bold f-19"> Assessment Types </p>
            <div className="pt-3">
              <Accordion
                expanded={expanded === 'panel1'}
                onChange={handleChange('panel1')}
                className={classes.accordionBackgroundNone}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon fontSize={`large`} edge={'start'} />}
                  aria-controls="panel1bh-content"
                  id="panel1bh-header"
                >
                  <p className="mb-0 bold f-19">Direct</p>
                </AccordionSummary>
                <AccordionDetails>
                  <div className="w-100">
                    <>
                      <div className="d-flex justify-content-between align-items-center">
                        <p className="mb-2 bold ">Internal </p>
                        <div className="d-flex">
                          <SettingsIcon onClick={handleShow} className="remove_hover" />
                        </div>
                      </div>

                      <div>
                        <Accordion
                          expanded={expandeds === 'panel2'}
                          onChange={handleChanges('panel2')}
                          className={classes.accordionBackgroundNone}
                        >
                          <AccordionSummary
                            expandIcon={<ExpandMoreIcon fontSize={`small`} className="m-0 p-0" />}
                            aria-controls="panel1bh-content"
                            id="panel1bh-header"
                            className={classes.accordionArrowPosition}
                          >
                            <div className="d-flex justify-content-between align-items-center ml-2 mb-3 mt-3 w-100">
                              <p className="mb-0 f-15">Quiz</p>
                              <div
                                className="d-flex text-skyblue bold remove_hover"
                                onClick={handleAddShow}
                              >
                                <AddIcon />
                                <p className="mb-0 f-15 padding-top-2px">Add New</p>
                              </div>
                            </div>
                          </AccordionSummary>
                          <AccordionDetails>
                            <div className="w-100">
                              <>
                                <div className="bg-white pl-3 pr-3 pt-2 pb-2 mb-2 border-radious-4">
                                  <div className="d-flex justify-content-between align-items-center">
                                    <div className="d-flex align-items-center">
                                      <div className="digi-Shared-bg p-3 border-radious-8">
                                        <p className="mb-0 f-15 pl-1 pr-1"> Q</p>
                                      </div>
                                      <p className="mb-0 pl-3"> Quiz</p>
                                    </div>

                                    <div className="d-flex align-items-center">
                                      <div className="border-left text-center pl-4 pr-4">
                                        <p className="mb-0  f-14 text-lightgray"> Mark</p>
                                        <p className="mb-0  bold"> 100</p>
                                      </div>

                                      <div className="border-left text-center pb-2 pl-4 pr-4 pt-2">
                                        <p className="mb-0  bold text-skyblue"> Enter Mark</p>
                                      </div>

                                      <div className="">
                                        <IconButton
                                          aria-label="more"
                                          aria-controls="long-menu"
                                          aria-haspopup="true"
                                          onClick={handleClick}
                                          size="small"
                                        >
                                          <MoreVertIcon />
                                        </IconButton>
                                        <Menu
                                          id="long-menu"
                                          anchorEl={anchorEl}
                                          keepMounted
                                          open={open}
                                          onClose={handleClose}
                                          PaperProps={{
                                            style: {
                                              maxHeight: ITEM_HEIGHT * 4.5,
                                              width: '20ch',
                                            },
                                          }}
                                        >
                                          {options.map((option) => (
                                            <MenuItem
                                              key={option}
                                              selected={option === 'Pyxis'}
                                              onClick={handleClose}
                                            >
                                              {option}
                                            </MenuItem>
                                          ))}
                                        </Menu>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </>

                              <>
                                <div className="bg-white pl-3 pr-3 pt-2 pb-2 mb-2 border-radious-4">
                                  <div className="d-flex justify-content-between align-items-center">
                                    <div className="d-flex align-items-center">
                                      <div className="digi-Shared-bg p-3 border-radious-8">
                                        <p className="mb-0 f-15 pl-1 pr-1"> QZ</p>
                                      </div>
                                      <p className="mb-0 pl-3"> Quiz - 2</p>
                                    </div>

                                    <div className="d-flex align-items-center">
                                      <div className="border-left text-center pl-4 pt-2 pr-4 pb-2 d-flex align-items-center w-100">
                                        <div className="mb-0 mr-2 mt-1">
                                          <PeopleIcon />
                                        </div>
                                        <p className="mb-0  bold"> 90</p>
                                      </div>

                                      <div className="border-left text-center pl-4 pt-2 pb-2 pr-4 d-flex align-items-center w-100">
                                        <div className="mb-0 mr-2 questionBorder bold">Q’s</div>
                                        <p className="mb-0  bold"> 10</p>
                                      </div>

                                      <div className="border-left text-center pl-4 pt-2 pb-2 pr-4 d-flex align-items-center w-100">
                                        <div className="mb-0 mr-2 questionBorder bold">Lo</div>
                                        <p className="mb-0  bold"> 04</p>
                                      </div>

                                      <div className="border-left text-center pl-4 pr-4">
                                        <p className="mb-0  f-14 text-lightgray"> Mark</p>
                                        <p className="mb-0  bold"> 100</p>
                                      </div>

                                      <div className="border-left text-center pb-2 pl-4 pr-4">
                                        <p className="mb-0  pt-2 bold text-skyblue"> View</p>
                                      </div>

                                      <div className="">
                                        <IconButton
                                          aria-label="more"
                                          aria-controls="long-menu"
                                          aria-haspopup="true"
                                          onClick={handleClick}
                                          size="small"
                                        >
                                          <MoreVertIcon />
                                        </IconButton>
                                        <Menu
                                          id="long-menu"
                                          anchorEl={anchorEl}
                                          keepMounted
                                          open={open}
                                          onClose={handleClose}
                                          PaperProps={{
                                            style: {
                                              maxHeight: ITEM_HEIGHT * 4.5,
                                              width: '20ch',
                                            },
                                          }}
                                        >
                                          {options.map((option) => (
                                            <MenuItem
                                              key={option}
                                              selected={option === 'Pyxis'}
                                              onClick={handleClose}
                                            >
                                              {option}
                                            </MenuItem>
                                          ))}
                                        </Menu>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </>
                            </div>
                          </AccordionDetails>
                        </Accordion>
                      </div>
                    </>
                  </div>
                </AccordionDetails>
              </Accordion>
            </div>
          </div>
        </div>
      </div>

      {show && (
        <MaterialDialog show={show} onClose={handleModalClose} maxWidth={'xs'} fullWidth={true}>
          <div className="w-100 p-4">
            <p className="mb-3 pb-2  bold f-19"> Set Assessment Type For Internal</p>
            <div className="mt-2 mb-2">
              <div>
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Internal"
                  labelPlacement="Quiz"
                />
              </div>
              <div>
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="IT"
                  labelPlacement="end"
                />
              </div>
              <div>
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Mid Teram"
                  labelPlacement="end"
                />
              </div>
              <div>
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Assingment"
                  labelPlacement="end"
                />
              </div>
              <div>
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="University Exam"
                  labelPlacement="end"
                />
              </div>
              <div>
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Survey"
                  labelPlacement="end"
                />
              </div>
              <div>
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="  Course Evaluation Survey"
                  labelPlacement="end"
                />
              </div>
            </div>

            <div className="d-flex justify-content-end border-top pt-3">
              <MButton
                variant="outlined"
                color="primary"
                className={'mr-2'}
                clicked={handleModalClose}
              >
                Cancel
              </MButton>
              <MButton variant="contained" color="primary">
                Save
              </MButton>
            </div>
          </div>
        </MaterialDialog>
      )}

      {AddShow && (
        <MaterialDialog
          show={AddShow}
          onClose={handleAddModalClose}
          maxWidth={'xs'}
          fullWidth={true}
        >
          <div className="w-100 p-4">
            <p className="mb-3 pb-2 border-bottom bold f-19"> Add Assessment For Mid Term</p>
            <div className="mt-2 mb-2 ">
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                label={'Assessment Name'}
              />
            </div>

            <div className="mt-2 mb-2">
              <MaterialInput
                elementType={'materialSelect'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{ options: programType }}
                label={'Assessment Occuring'}
              />
            </div>

            <div className="mt-2 mb-2">
              <label className="form-label ">Select Courses</label>
              <Typography
                component="div"
                className="muiOutline remove_hover"
                aria-label="more"
                aria-controls="long-menu"
                aria-haspopup="true"
                onClick={handleClick}
              >
                <div className="d-flex justify-content-between pt-2 pb-2">
                  <p className="mb-0 pl-3">10 Courses </p>
                  <ArrowDropDownIcon className="text-gray" />
                </div>
              </Typography>
              <Menu
                id="long-menu"
                anchorEl={anchorEl}
                keepMounted
                open={open}
                onClose={handleClose}
                PaperProps={{
                  style: {
                    maxHeight: ITEM_HEIGHT * 10.5,
                    width: '42.2ch',
                  },
                }}
              >
                <div className="w-100 p-3">
                  <MaterialInput
                    elementType={'materialSearch'}
                    type={'text'}
                    size={'small'}
                    placeholder={'Search'}
                  />
                </div>
                <>
                  <ListItem button>
                    <ListItemText primary="Year 1" className="text-darkgray" />
                  </ListItem>
                  <Divider />
                  <List component="div" className="align-items-center" disablePadding>
                    <ListItem button>
                      <ListItemIcon className="minWidth-32">
                        <Checkbox
                          edge="start"
                          // checked={}
                          tabIndex={-1}
                          disableRipple
                          color="primary"
                        />
                      </ListItemIcon>
                      <ListItemText primary="Level 1" />
                      <ListItemText className="text-right" primary="2 course Selected" />
                      <div onClick={handleClicks}>{open1 ? <ExpandLess /> : <ExpandMore />}</div>
                    </ListItem>
                  </List>
                  <Divider />

                  <Collapse in={open1} timeout="auto" unmountOnExit>
                    <ListItem button>
                      <ListItemIcon className="minWidth-32">
                        <Checkbox
                          edge="start"
                          // checked={}
                          tabIndex={-1}
                          disableRipple
                          color="primary"
                        />
                      </ListItemIcon>
                      <ListItemText primary="MPFH 105  -  UX Design - 5 Cr. H " />
                    </ListItem>
                    <Divider />
                    <ListItem button>
                      <ListItemIcon className="minWidth-32">
                        <Checkbox
                          edge="start"
                          // checked={}
                          tabIndex={-1}
                          disableRipple
                          color="primary"
                        />
                      </ListItemIcon>
                      <ListItemText primary="MPFH 105  -  Fundamental of Human - 5 Cr. H" />
                    </ListItem>
                    <Divider />
                  </Collapse>
                </>
                <>
                  <ListItem button>
                    <ListItemText primary="Year 2" />
                  </ListItem>
                  <Divider />
                  <List component="div" className="align-items-center" disablePadding>
                    <ListItem button>
                      <ListItemIcon className="minWidth-32">
                        <Checkbox
                          edge="start"
                          // checked={}
                          tabIndex={-1}
                          disableRipple
                          color="primary"
                        />
                      </ListItemIcon>
                      <ListItemText primary="Level 3" />
                      {/* <ListItemText className="text-right" primary="2 course Selected" /> */}
                      <div onClick={handleClicks}>{open1 ? <ExpandLess /> : <ExpandMore />}</div>
                    </ListItem>
                  </List>
                  <Divider />

                  <Collapse in={open1} timeout="auto" unmountOnExit>
                    <ListItem button>
                      <ListItemIcon className="minWidth-32">
                        <Checkbox
                          edge="start"
                          // checked={}
                          tabIndex={-1}
                          disableRipple
                          color="primary"
                        />
                      </ListItemIcon>
                      <ListItemText primary="Level 3" />
                    </ListItem>
                    <Divider />
                    <ListItem button>
                      <ListItemIcon className="minWidth-32">
                        <Checkbox
                          edge="start"
                          // checked={}
                          tabIndex={-1}
                          disableRipple
                          color="primary"
                        />
                      </ListItemIcon>
                      <ListItemText primary="Level 4" />
                    </ListItem>
                    <Divider />
                  </Collapse>
                </>
              </Menu>
            </div>
            <div className="mt-2 mb-2 pb-2">
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                label={'Set Total Marks'}
              />
            </div>

            <div className="d-flex justify-content-end border-top pt-3">
              <MButton
                variant="outlined"
                color="primary"
                className={'mr-2'}
                clicked={handleAddModalClose}
              >
                Cancel
              </MButton>
              <MButton variant="contained" color="primary">
                Save
              </MButton>
            </div>
          </div>
        </MaterialDialog>
      )}
    </div>
  );
}

export default AssessmentType;
