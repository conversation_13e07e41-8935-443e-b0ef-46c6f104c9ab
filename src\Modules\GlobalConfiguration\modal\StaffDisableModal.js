import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import MaterialInput from 'Widgets/FormElements/material/Input';
import DropdownCheckBox from 'Widgets/FormElements/material/DropdownCheckBox';
import i18n from 'i18next';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectStaffDesignations } from '_reduxapi/global_configuration/selectors';
// import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { List, Map } from 'immutable';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import CancelModal from 'Containers/Modal/Cancel';

function StaffDisableModal(props) {
  const {
    disable,
    handleClose,
    designationCheck,
    setDisableOpen,
    settingId,
    designations,
    updateDesignation,
    getDesignations,
    type,
    institutionHeader,
    staffDesignations,
    allUsersUpdateDesignation,
    getStaffParticularDesignation,
  } = props;
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [edited, setEdited] = useState(false);
  const [userDoc, setUserDoc] = useState([]);
  const [userDocSafe, setUserDocSafe] = useState([]);
  const id = disable['id'] ?? '';
  const index = disable['index'] ?? 0;
  const open = disable['show'] ?? false;
  const [editAll, setEditAll] = useState(designations.getIn([index, '_id'], ''));
  const operation = disable['operation'] ?? 'disable';

  const getDesignationList = () => {
    if (designations.size > 0) {
      return designations
        .filter((value) => value.get('isActive', '') === true)
        .map((item) => {
          return {
            name: item.get('designationName', ''),
            value: item.get('_id', ''),
          };
        })
        .toJS();
    }
    return [];
  };
  const designationList = getDesignationList();

  useEffect(() => {
    setUserDoc(staffDesignations.get('userDoc', List()).toJS());
    setUserDocSafe(staffDesignations.get('userDoc', List()).toJS());
  }, [staffDesignations]);
  let designationId = designations.getIn([index, '_id'], '');
  useEffect(() => {
    designationId !== '' &&
      getStaffParticularDesignation({
        type: designationId,
        headers: institutionHeader,
        errorCallBack,
        operation: operation,
      });
  }, []); // eslint-disable-line

  const errorCallBack = (error) => {
    setUserDoc([]);
    setUserDocSafe([]);
    if (error.response.data.message === 'No data found') {
      operation === 'disable'
        ? designationCheck({
            id,
            settingId,
            index,
            check: false,
            callBack,
            headers: institutionHeader,
            type,
          })
        : onDelete();
    }
  };

  const callBack = () => {
    setDisableOpen({ ...disable, show: false });
  };
  const onDelete = () => {
    const deleteCallback = () => {
      setDisableOpen(false);
      getDesignations({ headers: institutionHeader, settingId, type });
    };
    updateDesignation({
      operation: 'delete',
      id,
      settingId,
      callBack: deleteCallback,
      headers: institutionHeader,
      type,
    });
  };

  const handleSubmit = () => {
    const requestBody = {
      type: 'all',
      previousDesignation: designations.getIn([index, '_id'], ''),
      designation: '',
    };

    if (
      !userDoc.every((v, index, array) => {
        if (v.toDesignation === array[0].toDesignation) {
          return true;
        }
        return false;
      })
    ) {
      requestBody.type = 'selective';
      delete requestBody.previousDesignation;
      delete requestBody.designation;
      requestBody.updateDesignation = [];
      requestBody.updateDesignation = userDoc.map((v) => {
        return { id: v._id, designation: v.toDesignation };
      });
    } else {
      requestBody.designation = userDoc[0].toDesignation;
    }
    allUsersUpdateDesignation({ settingId, requestBody, headers: institutionHeader, callBacks });
  };
  const callBacks = () => {
    if (operation === 'disable') {
      designationCheck({
        id,
        settingId,
        index,
        check: false,
        callBack,
        headers: institutionHeader,
        type,
      });
    } else {
      onDelete();
    }
  };

  const handleChange = (e, data) => {
    if (data === 'all') {
      setEditAll(e.target.value);
      let newUserDoc = userDoc.map((v) => ({ ...v, toDesignation: e.target.value }));
      setUserDoc(newUserDoc);
      setEdited(true);
    } else {
      var newUserDoc = userDoc.map((v) => {
        if (v._id === data) {
          return { ...v, toDesignation: e.target.value };
        }
        return v;
      });

      setUserDoc(newUserDoc);
      newUserDoc.every((v) => {
        return (
          v.toDesignation !== designations.getIn([index, '_id'], '') &&
          v['toDesignation'] !== undefined
        );
      })
        ? setEdited(true)
        : setEdited(false);
      newUserDoc = userDocSafe.map((v) => {
        if (v._id === data) {
          return { ...v, toDesignation: e.target.value };
        }
        return v;
      });
      setUserDocSafe(newUserDoc);
    }
  };

  const handleCancel = () => {
    edited ? setShowCancelModal(true) : handleClose();
  };

  const onSearch = (e) => {
    setUserDoc(
      userDocSafe.filter((v) => {
        return (
          v.name.first.value.toLowerCase().includes(e.target.value) ||
          (v.user_id?.value !== undefined && v.user_id?.value !== null
            ? v.user_id?.value.toLowerCase().includes(e.target.value)
            : false)
        );
      })
    );
  };
  // {staffDesignations.get('currentPage', '')}
  var sortList = [
    { name: 'Recently Added', value: 0 },
    { name: 'A-Z', value: 1 },
    { name: 'Z-A', value: 2 },
  ];

  const sortCallBack = (sortType, type) => {
    setUserDoc(
      userDoc
        .slice()
        .sort((a, b) =>
          sortType === 'name'
            ? type === 1
              ? b.name.first?.value.toLowerCase() > a.name.first?.value.toLowerCase()
                ? -1
                : 1
              : type === 2 && b.name.first?.value.toLowerCase() > a.name.first?.value.toLowerCase()
              ? 1
              : -1
            : sortType === 'id'
            ? type === 1
              ? b.user_id?.value.toLowerCase() > a.user_id?.value.toLowerCase()
                ? -1
                : 1
              : type === 2 && b.user_id?.value.toLowerCase() > a.user_id?.value.toLowerCase()
              ? 1
              : -1
            : null
        )
    );
  };
  return (
    <>
      {staffDesignations.get('userDoc', List()).size > 0 && (
        <>
          <DialogModal show={open} onClose={handleCancel} maxWidth={'md'}>
            <div className="p-3">
              <div className="mb-1">
                <p className="f-22 mb-1">
                  {' '}
                  {i18n.t(
                    operation === 'disable'
                      ? 'userManagement.disable_assistant'
                      : 'userManagement.delete_assistant',
                    {
                      name: designations.getIn([index, 'designationName'], ''),
                    }
                  )}
                </p>

                <p className="f-15 mb-2 remove_hover">
                  {' '}
                  {i18n.t('userManagement.disable_content', {
                    name: designations.getIn([index, 'designationName'], ''),
                    type: operation === 'disable' ? 'disable' : 'delete',
                  })}
                </p>
                <div className="row">
                  <div className="col-md-5 col-xl-4 col-5 col-lg-4">
                    <MaterialInput
                      elementType={'materialSearch'}
                      placeholder={i18n.t('userManagement.search')}
                      // labelclass={'mb-1 f-15'}
                      changed={(e) => onSearch(e)}
                      labelclass={'searchLeft'}
                    />
                  </div>

                  <div className="col-md-12">
                    <div className="program_table pt-2 mb-2">
                      <table align="left" className="staff-model-table">
                        <thead>
                          <tr>
                            <th className="bg_newTableGray">
                              <div className="d-flex">
                                <p className="digi-mb-0">
                                  <DropdownCheckBox
                                    title={<Trans i18nKey={'userManagement.emp_name'}></Trans>}
                                    options={sortList}
                                    name={'name'}
                                    className="dropdowncheckbox"
                                    sortCallBack={sortCallBack}
                                  />
                                </p>
                              </div>
                            </th>

                            <th className="bg_newTableGray">
                              <div className="d-flex">
                                <p className="digi-mb-0">
                                  <DropdownCheckBox
                                    title={<Trans i18nKey={'userManagement.emp_id'}></Trans>}
                                    options={sortList}
                                    name={'id'}
                                    className="dropdowncheckbox"
                                    sortCallBack={sortCallBack}
                                  />
                                </p>
                              </div>
                            </th>
                            <th className="bg_newTableGray">
                              <div className="staff_model_bold">
                                <Trans i18nKey={'userManagement.email'}></Trans>
                              </div>
                            </th>
                            <th className="w-25 bg_newTableGray">
                              <div className="staff_model_bold">
                                <Trans i18nKey={'userManagement.designation'}></Trans>
                              </div>
                            </th>
                          </tr>
                          <tr className="bg-gray">
                            <td colSpan={3}>
                              <div className="bold">
                                Total {designations.getIn([index, 'designationName'], '')}
                                {': '}
                                {userDoc.length}
                                {/* {designations.getIn([index, 'designationName'], '')} : 12 */}
                              </div>
                            </td>

                            <td className="pl-0 pr-4">
                              <div>
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: designationList }}
                                  changed={(e) => handleChange(e, 'all')}
                                  value={
                                    editAll !== designations.getIn([index, '_id'], '')
                                      ? editAll
                                      : designations.getIn([index, '_id'], '')
                                  }
                                  id="paddingStaff"
                                />
                              </div>
                            </td>
                          </tr>
                        </thead>
                        <tbody>
                          {userDoc.map((value, i) => {
                            return (
                              <tr key={i} className="tr-change staff-model-table-row">
                                <td>
                                  <div>{value?.name.first?.value}</div>
                                  {/* <div>{value?.name.middle.value}</div>
                            <div>{value?.name.last.value}</div> */}
                                </td>
                                <td>
                                  <div>{value?.user_id?.value}</div>
                                </td>
                                <td>
                                  <div>{value?.email}</div>
                                </td>
                                <td>
                                  <div>
                                    <MaterialInput
                                      elementType={'materialSelect'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                      elementConfig={{ options: designationList }}
                                      changed={(e) => handleChange(e, value._id)}
                                      value={
                                        value['toDesignation'] !== undefined
                                          ? value.toDesignation
                                          : designations.getIn([index, '_id'], '')
                                      }
                                      id="paddingStaff"
                                    />
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div className="my-2 text-right">
                <MButton className="mr-3" color="gray" variant="outlined" clicked={handleCancel}>
                  <Trans i18nKey={'cancel'}></Trans>
                </MButton>

                <MButton
                  color="primary"
                  variant="contained"
                  clicked={handleSubmit}
                  disabled={!edited}
                >
                  <Trans i18nKey={'continue'}></Trans>
                </MButton>
              </div>{' '}
            </div>
          </DialogModal>
          <CancelModal
            showCancel={showCancelModal}
            setCancel={setShowCancelModal}
            setShow={handleClose}
          />
        </>
      )}
    </>
  );
}

StaffDisableModal.propTypes = {
  handleClose: PropTypes.func,
  disable: PropTypes.instanceOf(Map),
  designations: PropTypes.instanceOf(List),
  designationCheck: PropTypes.func,
  settingId: PropTypes.string,
  type: PropTypes.string,
  setDisableOpen: PropTypes.func,
  institutionHeader: PropTypes.object,
  updateDesignation: PropTypes.func,
  allUsersUpdateDesignation: PropTypes.func,
  getDesignations: PropTypes.func,
  staffDesignations: PropTypes.instanceOf(Map),
  getStaffParticularDesignation: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    staffDesignations: selectStaffDesignations(state),
  };
};

export default connect(mapStateToProps, actions)(StaffDisableModal);
