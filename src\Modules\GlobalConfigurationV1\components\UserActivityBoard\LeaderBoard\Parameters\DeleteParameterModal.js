import React, { Fragment, useState } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';
import alert_delete_icon from 'Assets/user_activity_board/alert_delete_icon.svg';
import Button from 'Widgets/FormElements/material/Button';
import { useDispatch } from 'react-redux';
import { deleteParameter } from '_reduxapi/global_configuration/v1/actions';
import DeleteIcon from '@mui/icons-material/Delete';

function DeleteParameterModal({ name, _id }) {
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();

  const onHide = () => {
    setOpen(false);
  };

  function onConfirm() {
    dispatch(deleteParameter(_id, onHide));
  }
  return (
    <Fragment>
      <DeleteIcon
        size="small"
        className="text-red mx-3"
        onClick={(e) => {
          e.stopPropagation();
          setOpen(true);
        }}
      />
      <Modal
        show={open}
        centered
        onHide={onHide}
        onClick={(e) => e.stopPropagation()}
        dialogClassName="modal-500"
      >
        <Modal.Body>
          <div className="d-flex mb-3">
            <img className="mr-2" alt={'Archive'} src={alert_delete_icon} />
            <p className="mb-0 f-22 bold">Validation error!</p>
          </div>

          <div className="p-2">
            <p className="mb-2 break-word">
              {`The staff performance criterion below will not be taken into consideration if you disable the "${name}."`}
            </p>
            <p className="mb-0 break-word">Are you sure you want to disable the parameters?</p>
          </div>
        </Modal.Body>

        <Modal.Footer className="border-none pt-0">
          <Button variant="outlined" color="inherit" clicked={onHide}>
            <Trans i18nKey={'cancel'}></Trans>
          </Button>

          <Button variant="contained" color="red" clicked={onConfirm}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment>
  );
}

DeleteParameterModal.propTypes = {
  onHide: PropTypes.func,
  _id: PropTypes.string,
  name: PropTypes.string,
};
export default DeleteParameterModal;
