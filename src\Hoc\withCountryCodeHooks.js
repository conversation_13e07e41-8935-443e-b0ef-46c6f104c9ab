import useCountryCode from 'Hooks/useCountryCodeHook';
import React from 'react';

function withCountryCodeHooks(Component) {
  const InjectedCurrentCalendar = function (props) {
    const {
      replaceCountryCode,
      showWithCountryCode,
      mobileLengthMatch,
      countryCodeLength,
      isMobileVerifyMandatory,
    } = useCountryCode();
    return (
      <Component
        {...props}
        replaceCountryCode={replaceCountryCode}
        showWithCountryCode={showWithCountryCode}
        mobileLengthMatch={mobileLengthMatch}
        countryCodeLength={countryCodeLength}
        isMobileVerifyMandatory={isMobileVerifyMandatory}
      />
    );
  };
  return InjectedCurrentCalendar;
}

export default withCountryCodeHooks;
