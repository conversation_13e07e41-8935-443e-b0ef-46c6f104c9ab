import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';

import YearLevelCourse from './YearLevelCourse';
import Course from './Course';
import LevelTimeTable from './LevelTimeTable';
import * as actions from '../../../../_reduxapi/course_scheduling/action';
import { getLang, getURLParams, removeURLParams } from '../../../../utils';
import {
  selectCourseList,
  selectActiveTab,
  selectTimeTableData,
  selectIndividualCourseDetails,
} from '../../../../_reduxapi/course_scheduling/selectors';
import {
  selectUserId,
  selectActiveInstitutionCalendar,
  selectSelectedRole,
} from '../../../../_reduxapi/Common/Selectors';
import CourseTabs from './CourseTabs';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { t } from 'i18next';
import QuickSchedule from '../QuickSchedule';
// import { CustomBreadcrumbs } from '../QuickSchedule/utils';
// import { Typography } from '@mui/material';

const lang = getLang();

export class Schedule extends Component {
  constructor() {
    super();
    this.state = {
      programId: getURLParams('programId', true),
      programName: getURLParams('programName', true),
    };
    this.handleBackClick = this.handleBackClick.bind(this);
  }

  componentDidMount() {
    this.fetchCourseList();
    this.checkPermission();
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.activeInstitutionCalendar.get('_id') !==
      prevProps.activeInstitutionCalendar.get('_id')
    ) {
      this.fetchCourseList();
      this.checkPermission();
    }
  }

  getIndividualCourse = (courseId, term, level) => {
    const { programId } = this.state;
    const { activeInstitutionCalendar, getIndividualCourse } = this.props;
    if (
      !activeInstitutionCalendar.isEmpty() &&
      courseId !== undefined &&
      term !== undefined &&
      level !== undefined
    ) {
      getIndividualCourse(programId, activeInstitutionCalendar.get('_id'), courseId, term, level);
    }
  };

  checkPermission = () => {
    const { setData } = this.props;
    if (getURLParams('courseId', true) === '') {
      if (
        CheckPermission(
          'tabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          'Course View'
        )
      ) {
        setData(Map({ activeTab: 'Schedule', activeScheduleView: 'list' }));
      } else if (
        CheckPermission('tabs', 'Schedule Management', 'Course Scheduling', '', 'TimeTable', 'View')
      ) {
        setData(Map({ activeTab: 'TimeTable' }));
      } else setData(Map({ activeTab: 'QuickSchedule' }));
    }
  };

  fetchCourseList() {
    this.props.setData(Map({ courseList: Map() }));
    const { programId } = this.state;
    const { userId, activeInstitutionCalendar, selectedRole } = this.props;
    const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id');
    const roleId = selectedRole.getIn(['_role_id', '_id']);
    if (userId && programId && activeInstitutionCalendarId && roleId)
      this.props.getCourseList(userId, activeInstitutionCalendarId, programId, roleId);
  }

  getActiveCourseId() {
    return getURLParams('courseId', true);
  }

  handleBackClick() {
    if (!this.getActiveCourseId()) {
      this.props.history.replace('/course-scheduling/schedule');
    } else {
      const { setData } = this.props;
      setData(Map({ activeTab: 'Schedule' }));
      this.props.history.replace(
        `/course-scheduling/schedule/list/${removeURLParams(this.props.location, [
          'courseId',
          'courseName',
          'studentGroupStatus',
        ])}`
      );
    }
  }

  getCourseNameAndVersion() {
    return `${getURLParams('courseName', true)}${getURLParams('versionName', true)}`;
  }

  getBreadcrumbs() {
    const { programName } = this.state;
    const { setData, history } = this.props;
    const activeCourseId = this.getActiveCourseId();

    const routes = [
      {
        name: 'Course Scheduling',
        onClick: () => history.replace('/course-scheduling/schedule'),
      },
      {
        name: `${programName} ${t('program')}`,
        onClick: () => {
          setData(Map({ activeTab: 'Schedule' }));
          history.replace(
            `/course-scheduling/schedule/list/${removeURLParams(this.props.location, [
              'courseId',
              'courseName',
              'studentGroupStatus',
            ])}`
          );
        },
      },
    ];

    return activeCourseId ? [...routes, { name: this.getCourseNameAndVersion() }] : routes;
  }

  renderComponent() {
    const {
      activeTab,
      courseList,
      setData,
      activeInstitutionCalendar,
      timeTableData,
      individualCourseDetails,
    } = this.props;
    const activeCourseId = this.getActiveCourseId();

    if (activeTab === 'QuickSchedule') return <QuickSchedule fromCourseId={activeCourseId} />;
    if (activeCourseId) {
      return (
        <Course
          getIndividualCourseList={this.getIndividualCourse}
          fetchCourseList={() => this.fetchCourseList()}
          breadcrumbList={this.getBreadcrumbs()}
        />
      );
    }
    if (activeTab === 'Schedule') {
      return <YearLevelCourse courseList={courseList} setData={setData} />;
    }

    return (
      <LevelTimeTable
        programId={this.state.programId}
        courseList={courseList}
        institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
        getTimeTableData={this.props.getTimeTableData}
        timeTableData={timeTableData}
        setData={setData}
        academicYear={activeInstitutionCalendar.get('calendar_name', '')}
        programName={getURLParams('programName', true)}
        getIndividualCourseList={this.getIndividualCourse}
        individualCourseDetails={individualCourseDetails}
      />
    );
  }

  render() {
    const { programName } = this.state;
    const { setData, activeTab } = this.props;
    const activeCourseId = this.getActiveCourseId();

    return (
      <div className="main pb-5 bg-gray">
        {(activeTab === 'QuickSchedule' || !activeCourseId) && (
          <div className="bg-white pt-3 pb-3">
            <div className="container-fluid">
              <div className="d-flex align-items-center">
                <p className="bold mb-0 f-17">
                  <i
                    className={` cursor-pointer  mr-3 fa ${
                      lang !== 'ar' ? 'fa-arrow-left' : 'fa-arrow-right'
                    }`}
                    aria-hidden="true"
                    onClick={this.handleBackClick}
                  ></i>
                  {activeCourseId
                    ? this.getCourseNameAndVersion()
                    : `${programName} ${t('program')}`}
                </p>
                {/* <div>
                  <CustomBreadcrumbs breadcrumbList={this.getBreadcrumbs()} />
                  <Typography fontSize={20} fontWeight={500} color="#374151">
                    Course Scheduling
                  </Typography>
                </div> */}
                <div style={{ flexGrow: 1, display: 'flex', justifyContent: 'center' }}>
                  <div>
                    <CourseTabs
                      setData={setData}
                      activeTab={activeTab}
                      showManageCourse={!!activeCourseId}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {this.renderComponent()}
      </div>
    );
  }
}

Schedule.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  setData: PropTypes.func,
  userId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getCourseList: PropTypes.func,
  courseList: PropTypes.instanceOf(Map),
  activeTab: PropTypes.string,
  timeTableData: PropTypes.instanceOf(Map),
  getTimeTableData: PropTypes.func,
  individualCourseDetails: PropTypes.instanceOf(Map),
  selectedRole: PropTypes.instanceOf(Map),
  getIndividualCourse: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    userId: selectUserId(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    courseList: selectCourseList(state),
    activeTab: selectActiveTab(state),
    timeTableData: selectTimeTableData(state),
    individualCourseDetails: selectIndividualCourseDetails(state),
    selectedRole: selectSelectedRole(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Schedule);
