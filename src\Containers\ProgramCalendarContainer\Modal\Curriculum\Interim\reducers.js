export const initialState_interim_curriculum = {
  curriculumYears: [],
  curriculumLevels: [],
  term1: {
    show: false,
    selectedTerms: '',
    yearLevel: [],
  },
  term2: {
    show: false,
    selectedTerms: '',
    yearLevel: [],
  },
};

const initialCallModify = (state, years, levels) => {
  return {
    ...state,
    curriculumYears: years,
    curriculumLevels: levels,
  };
};

const setTerms = (state, payload, terms) => {
  return {
    ...state,
    [terms]: {
      show: payload !== '',
      selectedTerms: payload,
      yearLevel:
        payload === terms + '_level_wise_view'
          ? state.curriculumLevels
          : payload === terms + '_year_wise_view'
          ? state.curriculumYears
          : [],
    },
  };
};

const setTermsValue = (state, terms, payload, name) => {
  const yearLevel = [...state[terms].yearLevel];
  const yearLevelIndex = yearLevel[name];
  yearLevelIndex['value'] = payload;
  yearLevel[name] = yearLevelIndex;
  return {
    ...state,
    [terms]: {
      ...state[terms],
      yearLevel: yearLevel,
    },
  };
};

export const curriculum_interim_reducer = (state, action) => {
  const { type, payload, name, terms, curriculum_years, curriculum_levels } = action;
  switch (type) {
    case 'INITIAL_LOAD_MODIFY':
      return initialCallModify(state, curriculum_years, curriculum_levels);
    case 'CURRICULUM_CHANGE_MODIFY':
      return setTerms(state, payload, terms);
    case 'VALUE_CHANGE_MODIFY':
      return setTermsValue(state, terms, payload, name);
    default:
      return {
        ...state,
      };
  }
};
