import React from 'react';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import MButton from 'Widgets/FormElements/material/Button';
import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import PropTypes from 'prop-types';
export default function Header({ boardName, validate, wantToSetApiResponse }) {
  const history = useHistory();
  return (
    <div className="d-lg-flex d-xl-flex d-md-flex d-sm-block  mb-3 pb-3 border-bottom align-items-center">
      <div className="d-flex align-items-center">
        <HomeIcon fontSize="small" className="digi-gray-neutral" />
        <span
          className="ml-2 mr-2 remove_hover"
          onClick={() => history.push('/globalConfiguration-v1/institution')}
        >
          All Modules
        </span>
        <KeyboardArrowRightIcon fontSize="" />
        <span className="ml-2 text-skyblue">{boardName} </span>
      </div>
      <div className="d-flex ml-auto mt-3 mt-lg-0 mt-xl-0 mt-xxl-0">
        <MButton
          variant="outlined"
          color="gray"
          className="mr-2   px-3 bold text-dark"
          sx={{ textTransform: 'none' }}
          size={'small'}
          clicked={wantToSetApiResponse}
        >
          Cancel
        </MButton>

        <MButton
          color="blue"
          className="ml-3"
          clicked={validate}
          // disabled={isLoading || !isChangesHappened}
        >
          Save
        </MButton>
      </div>
    </div>
  );
}
Header.propTypes = {
  boardName: PropTypes.string,
  validate: PropTypes.func,
  wantToSetApiResponse: PropTypes.func,
};
