import React, { useState } from 'react';
import { Fragment } from 'react';
import { Input, Label, Paragraph, FlexWrapper, Select } from '../Styled';
import MultiSelect from 'react-multi-select-component';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';

export default function CourseGroup({ data }) {
  const [nonRotation, setNonRotation] = data;

  let options = [];

  nonRotation['batch_courses_id'].map((item, i) =>
    options.push({ label: item.courses_name, value: item._id })
  );

  const handleSelect = (data) => {
    setNonRotation({
      type: 'SET_BATCH_COURSES_ID',
      payload: data.map((item) => item.value),
    });
  };

  return (
    <Fragment>
      <FlexWrapper mg="20px 80px">
        <Input
          type="checkbox"
          name="group_course"
          id="course_group"
          checked={nonRotation.group_course}
          onChange={(e) => {
            setNonRotation({
              type: 'ON_TOGGLE',
              name: e.target.name,
            });
          }}
        />
        <Label mg="0px" htmlFor="course_group">
          {' '}
          <Trans i18nKey={'group_this_course_with_other'}></Trans>
        </Label>
      </FlexWrapper>
      {nonRotation.group_course && (
        <FlexWrapper mg="20px 80px">
          <Paragraph>
            <Trans i18nKey={'select_one_or_more_courses'}></Trans>
          </Paragraph>
          <MultiSelect
            options={options}
            value={options.filter((item) =>
              nonRotation['type']['_batch_course_id'].includes(item.value)
            )}
            onChange={handleSelect}
            hasSelectAll={false}
            labelledBy={'Select'}
          />
          {/* {nonRotation["copy_dates"]
            .filter((item, i) => i !== 0)
            .map((item, i) => {
              return (
                <Fragment>
                  <Input type="checkbox" id={`batch_course${i}`} />
                  <Label htmlFor={`batch_course${i}`}>
                    {item.courses_name}
                  </Label>
                </Fragment>
              );
            })} */}
          {/* <Select multiple>
            {nonRotation["copy_dates"].map((item, i) => {
              if (i === 0) {
                return <option value="">select batch course</option>;
              } else {
                return (
                  <option value={item._course_id}>{item.courses_name}</option>
                );
              }
            })}
          </Select> */}
        </FlexWrapper>
      )}
    </Fragment>
  );
}
CourseGroup.propTypes = {
  data: PropTypes.object,
};
