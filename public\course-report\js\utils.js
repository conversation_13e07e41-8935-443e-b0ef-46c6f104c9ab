function formatReadableDateRange(startDate, endDate) {
    const formattedStartDate = moment(startDate).format('MMMM D, YYYY');
    const formattedEndDate = moment(endDate).format('MMMM D, YYYY');
    return `  ${formattedStartDate} -  ${formattedEndDate}`;
}


  
function filterByKey(data, key, value) {
    return data.filter(function(item) {
      return item[key] === value;
    });
  }
  
  function filterByKeys(data, filters) {
    return data.filter(function(item) {
      // Check if all key-value pairs in filters match the item
      return Object.keys(filters).every(function(key) {
        return item[key] === filters[key];
      });
    });
  }


  
function printPage(){
  $(".export-course-report-button").click(function(){
    console.log("reports clicked")
    window.print()
  })
  
}

printPage()

  const getStartAndEndYear = ({ startDate, endDate }) => ({ academicYearStart: new Date(startDate).getFullYear(), academicYearEnd: new Date(endDate).getFullYear() });

//   var filters = {
//     'term': 'regular',
//     'year': 'year2'
//   };
  
//   var filteredArray = filterByKeys(data, filters);
  

//pending item 
/*
    colspan design in grade distributoin 
*/