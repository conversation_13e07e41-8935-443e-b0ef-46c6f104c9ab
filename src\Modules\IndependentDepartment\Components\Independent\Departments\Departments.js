import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';

import AccordionSummary from '@mui/material/AccordionSummary';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useStylesFunction } from 'Modules/ProgramInput/v2/piUtil';
// import DepartmentName from './DepartmentName';
import DepartmentBody from './DepartmentBody';
import DepartmentAction from 'Modules/ProgramInput/v2/ConfigurationIndex/Departments/Components/DepartmentSubjects/Departments/DepartmentActions/DepartmentActions';
import Typography from '@mui/material/Typography';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import { getShortString } from 'Modules/Shared/v2/Configurations';

function Departments({ data, expanded, translateInput }) {
  const classes = useStylesFunction();
  const department = useContext(parentContext.departmentContext);
  const { departmentTabValue } = department;
  const programName = data.get('programName', '')
    ? data.get('programName', '')
    : data.getIn(['sharedTo', 'programName'], '');

  const disableDepartment =
    departmentTabValue === 'admin'
      ? data.get('sharedFromProgramId', '')
        ? true
        : false
      : data.get('sharedFromProgramId', '') || data.get('type', '') === 'admin'
      ? true
      : false;
  return (
    <AccordionSummary
      expandIcon={<ExpandMoreIcon className="pt-0" />}
      aria-controls="panel1bh-content"
      id="panel1bh-header"
    >
      <Typography className={`${classes.heading} bold text-dark break-word  pr-1`}>
        <span className="spant-1 ">{getShortString(data.get('departmentName', ''), 25)} </span>
      </Typography>

      {departmentTabValue === 'academic' && (
        <Typography className={`${classes.subjectHeadingAcademic} bold text-dark break-word`}>
          {getShortString(programName, 15)}
        </Typography>
      )}
      <Typography
        className={
          departmentTabValue === 'academic'
            ? classes.subjectHeadingAcademic
            : classes.subjectHeadingAdmin
        }
      ></Typography>

      <DepartmentBody
        sharedWith={data.get('sharedWith', List())}
        departmentId={data.get('_id', '')}
        sharedFromDepartment={disableDepartment}
        sharedFromProgramName={data.get('sharedFromProgramName', 'Admin')}
      />
      <DepartmentAction
        expanded={true}
        departmentName={data.get('departmentName', '')}
        departmentId={data.get('_id', '')}
        sharedWith={data.get('sharedWith', List())}
        programId={data.get('_program_id', '')}
        departmentType={data.get('type', '')}
        isDepartmentAssignedOrShared={
          data.get('isDepartmentAssigned', false) || data.get('isDepartmentOrSubjectShared', false)
        }
        sharedFromDepartment={disableDepartment}
      />
    </AccordionSummary>
  );
}

Departments.propTypes = {
  data: PropTypes.instanceOf(Map),
  expanded: PropTypes.bool,
  translateInput: PropTypes.object,
};
export default Departments;
