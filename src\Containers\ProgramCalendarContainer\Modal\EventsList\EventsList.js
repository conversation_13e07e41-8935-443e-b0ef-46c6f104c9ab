import React from 'react';
import { connect } from 'react-redux';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import jsPDF from 'jspdf';
import moment from 'moment';
import { jsUcfirst, addPDFPageNo, levelRename, getEnvCollegeName } from '../../../../utils';

const eventLists = (props) => {
  const {
    show,
    clicked,
    data,
    currentSemester,
    nav_bar_title,
    academic_year_name,
    active,
    iframeShow,
    interim_active,
    interim_academic_year_name,
    isInterim,
    programId,
    activeTerm,
  } = props;

  const printEventsListPDF = () => {
    var doc = new jsPDF();

    let navTitle = '';
    if (nav_bar_title !== '') {
      navTitle = nav_bar_title.split('>');
      navTitle = navTitle[0].trim();
    }
    let programTitle = navTitle !== '' ? jsUcfirst(navTitle) : 'Program Calendar';

    let yearTitle = '';
    if (
      !isInterim &&
      academic_year_name !== undefined &&
      academic_year_name !== '' &&
      academic_year_name !== null
    ) {
      //let splitYear = academic_year_name.split('-');
      // let year1Title = splitYear[0];
      // let year2Title = splitYear[1];
      yearTitle = academic_year_name; //year1Title + ' - ' + year2Title.substr(2, 4);
    } else if (
      isInterim &&
      interim_academic_year_name !== undefined &&
      interim_academic_year_name !== '' &&
      interim_academic_year_name !== null
    ) {
      // let splitYear = interim_academic_year_name.split('-');
      // let year1Title = splitYear[0];
      // let year2Title = splitYear[1];
      yearTitle = interim_academic_year_name; // year1Title + ' - ' + year2Title.substr(2, 4);
    }

    let activeYearTitle = isInterim
      ? interim_active.replace('year', '')
      : active.replace('year', '');

    jsPDF.autoTableSetDefaults({
      headStyles: { fillColor: '#e0e0e0', textColor: 0 },
      bodyStyles: { fillColor: '#ffffff', textColor: 0 },
    });

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);

    var finalY = doc.lastAutoTable.finalY || 10;
    doc.text('Academic year ' + yearTitle, 14, finalY + 10);
    doc.text(getEnvCollegeName().toLowerCase(), 200, finalY + 10, null, null, 'right');
    doc.setFont('helvetica', 'bold');
    doc.text(programTitle, 14, finalY + 20);

    doc.setFont('helvetica', 'normal');
    doc.text(`Year : ${activeYearTitle}`, 14, finalY + 27);
    if (currentSemester !== '') {
      doc.text(`Semester : ${currentSemester.replace('semester', '')}`, 14, finalY + 34);
    }

    doc.line(14, finalY + 38, 200, finalY + 38);

    if (data && data.length > 0) {
      data
        .filter((item) =>
          activeTerm !== '' ? activeTerm === item.term : item.term === data[0].term
        )
        .map((dt) => {
          doc.setFont('helvetica', 'bold');
          const lvNo = levelRename(dt.level_no, programId);
          if (doc.lastAutoTable.finalY === undefined) {
            doc.text(`${lvNo} - ${dt.term}`, 14, finalY + 48);
          } else {
            finalY = doc.lastAutoTable.finalY;
            doc.text(`${lvNo} - ${dt.term}`, 14, finalY + 10);
          }

          doc.setFont('helvetica', 'normal');
          let eventsList = [];
          if (dt.events && dt.events.length > 0) {
            eventsList = dt.events
              .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
              .map((list, index) => {
                let eventTitle =
                  typeof list.event_name === 'object'
                    ? list.event_name.first_language
                    : list.event_name;
                let event_date = '';
                if (list.event_date !== '') {
                  event_date = moment(list.event_date).format('DD MMM YYYY');
                }
                let end_date = '';
                if (list.end_date !== '') {
                  end_date = moment(list.end_date).format('DD MMM YYYY');
                }
                let start_time = '';
                if (list.start_time !== '') {
                  start_time = moment(Date.parse(list.start_time)).format('hh:mm  A');
                }
                let end_time = '';
                if (list.end_time !== '') {
                  end_time = moment(Date.parse(list.end_time)).format('hh:mm  A');
                }
                return [
                  index + 1,
                  eventTitle,
                  list.event_type,
                  event_date,
                  start_time,
                  end_date,
                  end_time,
                ];
              });
          }
          if (doc.lastAutoTable.finalY === undefined) {
            doc.autoTable({
              startY: finalY + 58,
              head: [
                [
                  'S.no',
                  'Event Title',
                  'Event Type',
                  'Start Date',
                  'Start Time',
                  'End Date',
                  'End Time',
                ],
              ],
              body: eventsList.length > 0 ? eventsList : [['No Data Found...']],
              tableLineColor: [189, 195, 199],
              tableLineWidth: 0.5,
              theme: 'grid',
            });
          } else {
            doc.autoTable({
              startY: finalY + 18,
              head: [
                [
                  'S.no',
                  'Event Title',
                  'Event Type',
                  'Start Date',
                  'Start Time',
                  'End Date',
                  'End Time',
                ],
              ],
              body: eventsList.length > 0 ? eventsList : [['No Data Found...']],
              tableLineColor: [189, 195, 199],
              tableLineWidth: 0.5,
              theme: 'grid',
            });
          }
          return dt;
        });
    }

    addPDFPageNo(doc);
    doc.save(`${programTitle}-events-list.pdf`);
  };

  return (
    <Modal show={show} onHide={clicked} size="lg">
      <div className="container-fluid">
        <p className="f-18 mb-2 pt-3 font-weight-bold"> Event list </p>
      </div>
      <Modal.Body>
        <div className="model-main">
          <div className="row">
            {data &&
              data.length > 0 &&
              data
                .filter((item) =>
                  activeTerm !== '' ? activeTerm === item.term : item.term === data[0].term
                )
                .map((list, index) => {
                  return (
                    <React.Fragment key={index}>
                      <div className={index !== 0 ? 'col-md-12 pt-3' : 'col-md-12'}>
                        {currentSemester !== '' && index === 0 && (
                          <div className="f-16 pt-2 font-weight-bold">
                            Semester {currentSemester.replace('semester', '')}{' '}
                          </div>
                        )}
                        {index === 0 && (
                          <div className="f-16 pt-2 font-weight-bold">
                            Year {list.year.replace('year', '')}
                          </div>
                        )}
                        <div className="f-16 pt-2 font-weight-bold">
                          {levelRename(list.level_no, programId)} - {list.term}{' '}
                        </div>
                      </div>

                      <div className="col-md-12 pt-3">
                        <div className="go-wrapper">
                          <table className="table">
                            <thead>
                              <tr>
                                <th>
                                  <div className="aw-50 text-dark">S.NO</div>
                                </th>
                                <th>
                                  <div className="aw-200 text-dark">Event Title</div>
                                </th>
                                <th>
                                  <div className="aw-200 text-dark">Start date &amp; time</div>
                                </th>
                                <th>
                                  <div className="aw-200 text-dark">End date &amp; time</div>
                                </th>
                              </tr>
                            </thead>
                            <tbody className="go-wrapper-height">
                              {list.events &&
                                list.events.length > 0 &&
                                list.events
                                  .sort(
                                    (a, b) => Date.parse(a.start_time) - Date.parse(b.start_time)
                                  )
                                  .map((event, eIndex) => {
                                    let eventTitle =
                                      typeof event.event_name === 'object'
                                        ? event.event_name.first_language
                                        : event.event_name;
                                    let event_date = '';
                                    if (event.event_date !== '') {
                                      event_date = moment(event.event_date).format('DD MMM YYYY');
                                    }
                                    let end_date = '';
                                    if (event.end_date !== '') {
                                      end_date = moment(event.end_date).format('DD MMM YYYY');
                                    }
                                    let start_time = '';
                                    if (event.start_time !== '') {
                                      start_time = moment(Date.parse(event.start_time)).format(
                                        'hh:mm  A'
                                      );
                                    }
                                    let end_time = '';
                                    if (event.end_time !== '') {
                                      end_time = moment(Date.parse(event.end_time)).format(
                                        'hh:mm  A'
                                      );
                                    }
                                    return (
                                      <tr key={eIndex}>
                                        <td>
                                          <div className="aw-50"> {eIndex + 1}</div>
                                        </td>
                                        <td>
                                          <div className="aw-200"> {eventTitle}</div>
                                        </td>
                                        <td>
                                          <div className="aw-200">
                                            {' '}
                                            {start_time}, {event_date}
                                          </div>
                                        </td>
                                        <td>
                                          <div className="aw-200">
                                            {' '}
                                            {end_time}, {end_date}
                                          </div>
                                        </td>
                                      </tr>
                                    );
                                  })}
                              {list.events && list.events.length === 0 && (
                                <tr>
                                  <td colSpan="7">
                                    <div className="aw-400 text-right"> No Data Found...</div>
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </React.Fragment>
                  );
                })}
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="container">
          <div className="row">
            <div className="col-md-8">
              <p className="mb-0 text-lightgray mt-2"></p>
            </div>
            <div className="col-md-4">
              <div className="float-right">
                <b className="pr-3">
                  <Button variant="outline-secondary" onClick={clicked}>
                    CLOSE
                  </Button>
                </b>
                {!iframeShow && (
                  <b className="pr-2">
                    <Button variant="primary" onClick={printEventsListPDF}>
                      EXPORT
                    </Button>
                  </b>
                )}
              </div>
            </div>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

const mapStateToProps = ({ interimCalendar, calender }) => ({
  active: calender.active_year,
  interim_active: interimCalendar.active_year,
  active_semesters: interimCalendar.active_semesters,
  academic_year_name: calender.academic_year_name,
  interim_academic_year_name: interimCalendar.academic_year_name,
  nav_bar_title:
    calender.nav_bar_title !== ''
      ? calender.nav_bar_title
      : '' || interimCalendar.nav_bar_title !== ''
      ? interimCalendar.nav_bar_title
      : '',
  activeTerm: calender.active_term,
});

export default connect(mapStateToProps, null)(eventLists);
