import React, { Fragment, createContext } from 'react';
import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { List, Map, fromJS } from 'immutable';
import { useHistory, useLocation } from 'react-router-dom';
// import MainConfig from './MainConfig';
// import AcademicYear from './AcademicYear';
// import ConfigureTemplate from './ConfigureTemplate/ConfigureTemplate';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
// import TemplateSettings from './ConfigureTemplate/TemplateSettings/TemplateSettings';
// import './style/style.css';

import CreateConfig from './CreateConfig';
import { useSelector } from 'react-redux';
import { selectCategoryForm } from '_reduxapi/global_configuration/v1/selectors';

export const QueryContext = createContext();

const showBack = ['configure_template', 'settings'];
let actionsOfQuery = fromJS({
  // qa_pc_configuration: {
  //   label: 'QA Process Configuration',
  //   key: 'qa_pc_configuration',
  //   component: MainConfig,
  // },
  // configure_template: {
  //   label: 'Configure Template',
  //   key: 'qa_pc_configuration+configure_template',
  //   backKey: 'qa_pc_configuration',
  //   component: ConfigureTemplate,
  // },
  // academic_year: {
  //   label: 'Academic Year',
  //   key: 'qa_pc_configuration+academic_year',
  //   component: AcademicYear,
  // },
  // settings: {
  //   label: 'Settings',
  //   key: 'qa_pc_configuration+configure_template+settings',
  //   backKey: 'qa_pc_configuration+configure_template',
  //   component: TemplateSettings,
  // },
});

const Dummy = () => <></>;

export const useSearchParams = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  return [searchParams];
};
export const useCurrentPage = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('query');
  return query;
};

const updateKeyForFormConfig = ({ currentPage }) => {
  showBack.push(currentPage);
  actionsOfQuery = actionsOfQuery.set(
    currentPage,
    fromJS({
      label: currentPage.split(',').join(' '),
      key: `qa_pc_configuration+configure_template+${currentPage}`,
      backKey: 'qa_pc_configuration+configure_template',
      component: CreateConfig,
    })
  );
};
export default function QAPCConfiguration() {
  const query = useCurrentPage();
  const initialQuery = query && query.length !== 0 ? query.split(' ') : [];
  const pagesLength = initialQuery.length - 1;
  const currentPage = initialQuery[pagesLength];
  const prevPage = initialQuery[pagesLength - 1];
  const formDetailPage = prevPage === 'configure_template' && !actionsOfQuery.has(currentPage);
  if (formDetailPage) updateKeyForFormConfig({ currentPage });
  const history = useHistory();
  const goToInstitution = () => {
    history.push('/globalConfiguration-v1/institution');
  };
  const updateRoute = (page) => () => {
    history.push(`/globalConfiguration-v1/qa_pc_configuration?query=${page}`);
  };
  const categoryForms = useSelector(selectCategoryForm);
  const [searchParams] = useSearchParams();
  const categoryFormIndex = searchParams.get('categoryFormIndex') ?? 0;
  const Component = actionsOfQuery.getIn([currentPage, 'component'], Dummy);
  return (
    <div className="position-relative">
      <section className="py-3 border-bottom">
        <div className="d-flex align-items-center f-12 px-3">
          <HomeIcon fontSize="small" className="digi-gray-neutral" />
          <span
            className="remove_hover f-12 ml-1"
            style={{ color: '#9CA3AF' }}
            onClick={goToInstitution}
          >
            All Modules
          </span>
          <KeyboardArrowRightIcon
            className={`f-16 ${initialQuery.length === 1 ? 'text-dGrey' : 'text-grey'}`}
          />
          {initialQuery.map((item, index) => {
            const data = actionsOfQuery.get(item, Map());
            const isCurrent = currentPage === item;
            return (
              <Fragment key={index}>
                <span
                  className={`remove_hover ${isCurrent ? 'text-primary f-12' : 'text-grey'}`}
                  onClick={updateRoute(data.get('key', ''))}
                >
                  {data.get('label', '')}
                </span>
                {pagesLength !== index && (
                  <KeyboardArrowRightIcon
                    className={`f-16 ${isCurrent ? 'text-grey' : 'text-dGrey'}`}
                  />
                )}
              </Fragment>
            );
          })}
        </div>
        {showBack.includes(currentPage) && (
          <>
            <div
              className="f-24 d-inline-flex align-items-center cursor-pointer px-3 gap-3"
              onClick={updateRoute(actionsOfQuery.getIn([currentPage, 'backKey'], ''))}
            >
              <ArrowBackIcon />
              <div className="pl-2">
                <p className="m-0">{actionsOfQuery.getIn([currentPage, 'label'], '')}</p>
                {formDetailPage && (
                  <div className="f-14 fw-400 text-mGrey">
                    Assigned Program :
                    {categoryForms
                      .getIn([categoryFormIndex, 'formOccurrence'], List())
                      .map((occ) => occ.get('program_name', ''))
                      .join(',')}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </section>
      <Component />
    </div>
  );
}
