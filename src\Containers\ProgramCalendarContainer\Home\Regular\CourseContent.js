import React, { Fragment } from 'react';
import { Trans } from 'react-i18next';
import styled from 'styled-components';

import { PrimaryButton, TextContainer, Text } from '../../Styled';
import LevelCourses from './LevelCourses';
// import AcademicEvents from "./AcademicEvents";
import PropTypes, { oneOfType } from 'prop-types';

const ContentBlock = styled.div`
  display: grid;
  text-align: center;
  grid-template-rows: repeat(${(props) => props.len || 0}, 50px);
`;

const CourseContent = (props) => {
  const {
    active,
    length,
    number,
    rotational,
    start_date,
    end_date,
    courses,
    events,
    coursePopUp,
    level_no,
    term,
    iframeShow,
    currentPGAccess,
    levels,
  } = props;

  return (
    <Fragment>
      {start_date !== '' && end_date !== '' ? (
        <ContentBlock len={length}>
          <LevelCourses
            number={number}
            rotational={rotational}
            start_date={start_date}
            end_date={end_date}
            courses={courses}
            events={events}
            coursePopUp={coursePopUp}
            level_no={level_no}
            term={term}
            active={active}
            iframeShow={iframeShow}
            currentPGAccess={currentPGAccess}
            levels={levels}
            len={length}
          />
          {/* <AcademicEvents start_date={start_date} end_date={end_date} events={events} /> */}
        </ContentBlock>
      ) : (
        <TextContainer>
          <Text>
            <Trans i18nKey={'program_calendar.no_calendar'}></Trans>
          </Text>{' '}
          <Text>
            <Trans i18nKey={'program_calendar.click'}></Trans>
            <PrimaryButton className="dim ml-2 mr-2">
              <Trans i18nKey={'program_calendar.calendar_settings'}></Trans>
            </PrimaryButton>
            <Trans i18nKey={'program_calendar.define_dates_and_curriculum'}></Trans>
          </Text>
        </TextContainer>
      )}
    </Fragment>
  );
};

CourseContent.propTypes = {
  active: PropTypes.string,
  length: PropTypes.number,
  number: PropTypes.number,
  rotational: PropTypes.bool,
  start_date: PropTypes.string,
  end_date: PropTypes.string,
  courses: oneOfType([PropTypes.object, PropTypes.array]),
  events: oneOfType([PropTypes.object, PropTypes.array]),
  coursePopUp: PropTypes.func,
  level_no: PropTypes.string,
  term: PropTypes.string,
  iframeShow: PropTypes.bool,
  currentPGAccess: PropTypes.bool,
  levels: oneOfType([PropTypes.object, PropTypes.array]),
};

CourseContent.defaultProps = {
  number: -1,
  rotational: false,
};

export default CourseContent;
