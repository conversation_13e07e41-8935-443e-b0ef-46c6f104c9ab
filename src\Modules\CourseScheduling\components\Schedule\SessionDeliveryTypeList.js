import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import success from '../../../../Assets/alert2.png';
import { getFormattedGroupName } from '../utils';
import { getURLParams, indVerRename, isIndGroup, studentGroupRename } from 'utils';

function SessionDeliveryTypeList({
  individualSessionDetails,
  activeSession,
  setActiveSession,
  activeDelivery,
  setActiveDelivery,
  activeSetting,
  setActiveSetting,
  rotation,
}) {
  const programId = getURLParams('programId', true);
  return (
    <div className="max_hs p-1 roles_height">
      {individualSessionDetails.isEmpty() ? (
        <div className="mt-3 mb-3 text-center">No session flow found</div>
      ) : (
        <>
          {individualSessionDetails.map((session, sIndex) => {
            return (
              <React.Fragment key={sIndex}>
                <div
                  className={`course_sidebar remove_hover ${
                    activeSession.get('session_id') === session.get('session_id', '')
                      ? 'course_active'
                      : ''
                  }`}
                  onClick={() => {
                    setActiveSession(session);
                    setActiveDelivery(Map());
                    setActiveSetting(Map({ id: '', title: '' }));
                  }}
                >
                  <b>
                    {' '}
                    <span className="pl-2 f-18">{`${indVerRename(
                      session.get('session_name', ''),
                      programId
                    )}`}</span>
                  </b>
                </div>
                {activeSession.get('session_id') === session.get('session_id', '') && (
                  <>
                    {session.get('delivery', List()).map((delivery, dIndex) => (
                      <>
                        <div
                          className={`course_sidebar_inner remove_hover ${
                            activeSession.get('session_id') === session.get('session_id', '')
                              ? 'course_active'
                              : ''
                          }`}
                          key={dIndex}
                          onClick={() => {
                            setActiveDelivery(delivery);
                            setActiveSetting(Map({ id: '', title: '' }));
                          }}
                        >
                          <div className="pl-4 ">
                            <i
                              className="fa fa-level-up fa-rotate-90 f-12 mr-2"
                              aria-hidden="true"
                            ></i>
                            {`${delivery.get('delivery_name', '')}`}
                          </div>
                        </div>
                        {activeDelivery.get('delivery_id') === delivery.get('delivery_id', '') && (
                          <>
                            {delivery
                              .get('settings', List())
                              .filter((item) =>
                                rotation !== 0 ? rotation === item.get('rotation_count', '') : item
                              )
                              .isEmpty() ? (
                              <div className="mt-3 mb-3 text-center">No setting found</div>
                            ) : (
                              ''
                            )}
                            {delivery
                              .get('settings', List())
                              .filter((item) =>
                                rotation !== 0 ? rotation === item.get('rotation_count', '') : item
                              )
                              .map((setting, sIndex) => {
                                let arrayList = [];
                                setting
                                  .get('session', List())
                                  .map((sessionGroup) => {
                                    return sessionGroup
                                      .get('student_groups', List())
                                      .map((studentGroup) => {
                                        const studentGroupName = getFormattedGroupName(
                                          studentGroupRename(
                                            studentGroup.get('group_name', ''),
                                            programId
                                          ),
                                          isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                                        );
                                        const sessionGroupNames = studentGroup
                                          .get('session_group', List())
                                          .map((sessionGroup) =>
                                            getFormattedGroupName(
                                              sessionGroup.get('group_name', ''),
                                              3
                                            )
                                          )
                                          .join(', ');
                                        const value = `${studentGroupName} - ${sessionGroupNames}`;
                                        arrayList.push(value);
                                        return value;
                                      })
                                      .toJS();
                                  })
                                  .toJS();
                                const title = arrayList
                                  .filter((value, index, self) => self.indexOf(value) === index)
                                  .join(', ');
                                return (
                                  <div
                                    className={`course_sidebar_inner remove_hover ${
                                      activeSetting.get('id') === setting.get('_id')
                                        ? 'course_active'
                                        : ''
                                    }`}
                                    key={sIndex}
                                    onClick={() => {
                                      setActiveSetting(
                                        Map({ id: setting.get('_id'), title: title })
                                      );
                                    }}
                                  >
                                    <div className="pl-4 ">
                                      {setting.get('occurrence', List()).size > 0 ? (
                                        <img src={success} alt="check" className="mr-1" />
                                      ) : (
                                        <i
                                          className="fa fa-exclamation-circle f-12 mr-1"
                                          aria-hidden="true"
                                        ></i>
                                      )}
                                      {title}
                                    </div>
                                  </div>
                                );
                              })}
                          </>
                        )}
                      </>
                    ))}
                  </>
                )}
              </React.Fragment>
            );
          })}
        </>
      )}
    </div>
  );
}

SessionDeliveryTypeList.propTypes = {
  individualSessionDetails: PropTypes.instanceOf(List),
  activeSession: PropTypes.instanceOf(Map),
  activeDelivery: PropTypes.instanceOf(Map),
  activeSetting: PropTypes.instanceOf(Map),
  rotation: PropTypes.number,
  setActiveSession: PropTypes.func,
  setActiveDelivery: PropTypes.func,
  setActiveSetting: PropTypes.func,
};

export default SessionDeliveryTypeList;
