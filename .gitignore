# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
ckeditor5/node_modules/
/.pnp
.pnp.js
/.gitlab

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
report*.json

.env.development
.env.demo
.env.production
.env.stating
.env.uat
.gitlab-ci.yml

Dockerfile
/src/constants.js