import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';

import '../../../css/institution.css';
import University_logo from '../../../../../Assets/university.svg';
import individual_institution from '../../../../../Assets/individual_institution.svg';
import MButton from 'Widgets/FormElements/material/Button';

export const INSTITUTION_TYPES = [
  {
    title: 'University / Group of Institutions',
    subtitle:
      'Choose this if you want to manage multiple institutions running under your organization',
    type: 'group',
    logo: University_logo,
  },
  {
    title: 'Individual Institution',
    subtitle: 'Choose this if you want to manage an individual institution',
    type: 'individual',
    logo: individual_institution,
  },
];

function InstitutionType({ handleNext }) {
  const [selectedOption, setSelectedOption] = useState({});
  const disableNext = !selectedOption.type;
  return (
    <div className="row justify-content-center pt-3">
      <div className=" col-md-8 col-xl-5 col-lg-6 col-12 ">
        <div className="outter-login">
          <div className="row pt-2">
            <p className="mb-1 f-20 pt-2 bold">Choose your type of institution</p>
          </div>
          <div className="row mt-3 mb-3 f-16">We can help you to setup</div>
          {INSTITUTION_TYPES.map((institutionType, i) => (
            <div
              key={`${institutionType.title}-${i}`}
              className="pb-2 pt-2 cursor-pointer"
              onClick={() =>
                setSelectedOption({ type: institutionType.type, name: institutionType.title })
              }
            >
              <div
                className={`row rounded p-3 ${
                  institutionType.type === selectedOption.type
                    ? 'university-view-active'
                    : 'university-view'
                }`}
              >
                <div className="col-md-3 pr-0 pt-1">
                  <img src={institutionType.logo} width="60px" height="60px" alt="universityLogo" />
                </div>
                <div className="col-md-9 pl-0">
                  <p className=" mb-1 f-17 bold">{institutionType.title}</p>
                  <small className="text-justify f-14">{institutionType.subtitle}</small>
                </div>
              </div>
            </div>
          ))}
          <div className="row pt-2 pb-2">
            <div className="col-md-6 pl-0">
              <Link to="/logout">
                <MButton
                  className="text-uppercase mr-3 onboard-button-wd-140 logout-link-clr"
                  color="inherit"
                  variant="outlined"
                >
                  LOG OUT
                </MButton>
              </Link>
            </div>
            <div className="col-md-6 pr-0">
              <div className="float-right">
                <MButton
                  className="onboard-button-wd-140"
                  disabled={disableNext}
                  clicked={() => handleNext(selectedOption)}
                >
                  NEXT
                </MButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

InstitutionType.propTypes = {
  handleNext: PropTypes.func,
};

export default InstitutionType;
