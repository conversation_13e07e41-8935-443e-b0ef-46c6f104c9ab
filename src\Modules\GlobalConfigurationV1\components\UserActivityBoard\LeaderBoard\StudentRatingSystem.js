import React, { useState } from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Switch from '@mui/material/Switch';
import student_rating_system from 'Assets/user_activity_board/student_rating_system.svg';

export default function StudentRatingSystem() {
  const [toggle, setToggle] = useState(false);
  return (
    <Accordion disableGutters sx={{ background: 'white !important', my: 3 }}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1a-content"
        id="panel1a-header"
        className="px-3 py-2"
      >
        <div className="d-flex">
          <img src={student_rating_system} alt="student_rating_system" />
          <div className="ml-3">
            <div className="f-18 bold late_config_color">Student Rating System</div>
            <span className="f-15 text-light-grey pt-1">
              Set the student feedback review settings
            </span>
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <div className="thresHold_config ">
          <div className="d-flex align-items-center">
            <div className="pt-1 pl-2 bold f-17">
              Enable Student Star Rating Feedback on Staff Session Completion{' '}
            </div>
            <div className="ml-auto mr-2">
              <Switch checked={toggle} onChange={(e) => setToggle(e.target.checked)} />
              <span>{toggle ? 'ON' : 'OFF'} </span>
            </div>
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
}
