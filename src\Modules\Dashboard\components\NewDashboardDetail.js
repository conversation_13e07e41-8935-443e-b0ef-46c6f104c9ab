import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import moment from 'moment';
import Input from '../../../Widgets/FormElements/Input/Input';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import {
  getURLParams,
  getFormattedCourseDuration,
  formatFullName,
  eString,
  getTranslatedDuration,
  indVerRename,
  stringToUC,
  levelRename,
  getEnvLabelChanged,
  jsUcfirstAll,
  getVersionName,
} from '../../../utils';
import {
  selectActiveInstitutionCalendar,
  selectUserId,
  selectSelectedRole,
} from '../../../_reduxapi/Common/Selectors';
import {
  selectDepartmentSubjectLists,
  selectAcademicYearLists,
  selectStaffCount,
  selectLoading,
  selectMonitoringCourseDetail,
} from '../../../_reduxapi/dashboard/selectors';
import * as actions from '../../../_reduxapi/dashboard/action';
import * as actions1 from '../../../_reduxapi/reports_and_analytics/action';
import { preparePercentageNew } from './Utils';
import Tooltips from '../../../_components/UI/Tooltip/Tooltip';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getActiveLink } from '../../ReportsAndAnalytics/utils';
import { Trans } from 'react-i18next';
import { getLang } from '../../../utils';
import { t } from 'i18next';

const lang = getLang();
var staffList;
class DashboardDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: 0,
      preparedProgramList: List(),
      preparedDepartmentList: List(),
      activeProgram: Map(),
      selectedYear: '',
      selectedLevel: '',
      selectedTerm: '',
      selectedDepartment: '',
      selectedStaff: '',
      selectedSubject: '',
      selectedProgram: '',
      programID: getURLParams('programId', true),
      showCourses: [],
      loader: '',
    };
  }

  componentDidMount() {
    this.getAcademicYear();
    this.interval = setInterval(() => {
      const { academicYearList } = this.props;
      if (!academicYearList.isEmpty()) {
        clearInterval(this.interval);
        this.loadInitialData();
      }
    }, 500);
  }

  loadInitialData = (sub) => {
    const programId = getURLParams('programId', true);
    if (sub) {
      const { departmentSubjectLists } = this.props;
      const dept = departmentSubjectLists.filter((item) => item.get('program_id') === programId);
      this.setState({
        preparedDepartmentList: dept,
      });
    } else {
      const { academicYearList } = this.props;

      const levels = academicYearList.get('yearLevelCourses', List());
      const grouped = levels
        .groupBy((level) => level.get('year'))
        .entrySeq()
        .reduce(
          (acc, [key, value]) =>
            acc.push(
              Map({
                year: key,
                levels: value,
                term: value.getIn([0, 'term'], 'regular'),
                termsOptions: value
                  .map((item) => item.get('term'))
                  .filter((value, index, self) => self.indexOf(value) === index),
              })
            ),
          List()
        );
      this.setState({
        preparedProgramList: grouped,
        activeProgram: academicYearList,
        selectedTerm: grouped.getIn([0, 'termsOptions', 0], 'regular'),
      });
    }
  };

  componentWillUnmount() {
    clearInterval(this.interval);
    clearInterval(this.interval1);
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.activeInstitutionCalendar.get('_id') !==
      prevProps.activeInstitutionCalendar.get('_id')
    ) {
      this.setState({ activeTab: 0, preparedDepartmentList: List() });
      this.getAcademicYear();
      this.interval1 = setInterval(() => {
        const { academicYearList } = this.props;
        if (academicYearList && academicYearList.size > 0) {
          clearInterval(this.interval1);
          this.loadInitialData();
        }
      }, 500);
    }
  }

  getAcademicYear = () => {
    const { getAcademicYear, userId, activeInstitutionCalendar, selectedRole } = this.props;
    const programId = getURLParams('programId', true);
    this.setState({ programID: programId });
    if (activeInstitutionCalendar.get('_id', '') !== '') {
      getAcademicYear(
        userId,
        activeInstitutionCalendar.get('_id', ''),
        selectedRole.getIn(['_role_id', '_id'], ''),
        'yearLevel',
        programId
      );
    }
  };
  getDepartmentSubject = () => {
    this.setState({ activeTab: 1 });
    const { getDepartmentSubject, userId, activeInstitutionCalendar, selectedRole } = this.props;
    const programId = getURLParams('programId', true);
    if (
      activeInstitutionCalendar.get('_id', '') !== '' &&
      !this.state.preparedDepartmentList.size
    ) {
      getDepartmentSubject(
        userId,
        activeInstitutionCalendar.get('_id', ''),
        selectedRole.getIn(['_role_id', '_id'], ''),
        'departmentSubject',
        programId
      );
      this.interval2 = setInterval(() => {
        const { departmentSubjectLists } = this.props;
        if (departmentSubjectLists && departmentSubjectLists.size > 0) {
          clearInterval(this.interval2);
          const sub = true;
          this.loadInitialData(sub);
        }
      }, 500);
    }
  };

  handleSelect = (e, name) => {
    e.preventDefault();
    this.setState({
      [name]: e.target.value,
    });
    if (name === 'selectedYear') {
      this.setState({ selectedLevel: '' });
    } else if (name === 'selectedDepartment') {
      this.setState({ selectedSubject: '', selectedProgram: '', selectedStaff: '' });
    } else if (name === 'selectedSubject') {
      this.setState({ selectedProgram: '', selectedStaff: '' });
    }
  };

  sideView = () => {
    const { activeTab, programID } = this.state;
    return (
      <div className="col-md-3 program_sidebar">
        <div
          className={`d-flex justify-content-between dashboard_hover ${
            activeTab === 0 && 'dashboard_active'
          }`}
          onClick={() => this.setState({ activeTab: 0 })}
        >
          <p className="mb-0 font-weight-bold">
            <Trans
              i18nKey={'dashboard_view.academic_year'}
              values={{ Level: indVerRename('Level', programID) }}
            ></Trans>
          </p>
        </div>
        <div
          className={`d-flex justify-content-between dashboard_hover ${
            activeTab === 1 && 'dashboard_active'
          }`}
          onClick={() => this.getDepartmentSubject()}
        >
          <p className="mb-0 font-weight-bold">
            <Trans i18nKey={'dashboard_view.depart_subjcet'}></Trans>
          </p>
        </div>
      </div>
    );
  };

  headerCounts = () => {
    const { activeProgram, activeTab, preparedDepartmentList } = this.state;
    const { totalStaff } = this.props;
    const countDept = preparedDepartmentList.filter((dept) => dept.get('shared', false) !== true)
      .size;
    let countSub = 0;
    preparedDepartmentList
      .filter((dept) => dept.get('shared', false) !== true)
      .toJS()
      .forEach((dept) => {
        countSub += dept.subject.filter((sub) => sub.shared !== true).length;
      });

    return (
      <div className="row mb-3">
        <div className={`col-md-${activeTab === 0 ? '3' : '4'}`}>
          <div className="dashboard_list">
            <p className="mb-0 f-16 text-lightblack">
              {activeTab === 0 ? (
                <Trans i18nKey={'dashboard_view.active_curriculum'}></Trans>
              ) : (
                <Trans i18nKey={'dashboard_view.total_staff'}></Trans>
              )}
            </p>
            <p className="mb-0 bold f-35 ">
              {activeTab === 0
                ? activeProgram.get('curriculumCount', 0)
                : totalStaff
                ? totalStaff
                : 0}
            </p>
          </div>
        </div>

        <div className={`col-md-${activeTab === 0 ? '3' : '4'}`}>
          <div className="dashboard_list">
            <p className="mb-0 f-16 text-lightblack">
              {activeTab === 0 ? (
                <Trans i18nKey={'dashboard_view.total_year'}></Trans>
              ) : (
                <Trans i18nKey={'dashboard_view.total_department'}></Trans>
              )}
            </p>
            <p className="mb-0 bold f-35 ">
              {' '}
              {activeTab === 0 ? activeProgram.get('yearCount', 0) : countDept ? countDept : 0}
            </p>
          </div>
        </div>

        <div className={`col-md-${activeTab === 0 ? '3' : '4'}`}>
          <div className="dashboard_list">
            <p className="mb-0 f-16 text-lightblack">
              {activeTab === 0 ? (
                <Trans
                  i18nKey={'dashboard_view.total_level'}
                  values={{ Level: indVerRename('Level', this.state.programID) }}
                ></Trans>
              ) : (
                <Trans i18nKey={'dashboard_view.total_subject'}></Trans>
              )}
            </p>
            <p className="mb-0 bold f-35 ">
              {activeTab === 0 ? activeProgram.get('levelCount', 0) : countSub ? countSub : 0}
            </p>
          </div>
        </div>
        {activeTab === 0 && (
          <div className="col-md-3">
            <div className="dashboard_list">
              <p className="mb-0 f-16 text-lightblack">
                <Trans i18nKey={'dashboard_view.total_course'}></Trans>{' '}
              </p>
              <p className="mb-0 bold f-35 ">
                {/* {this.getCourseCount()} */}
                {activeProgram.get('courseCount', 0)}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  };

  getCourseCount = () => {
    const { activeTab, preparedProgramList, preparedDepartmentList, selectedTerm } = this.state;
    let value = 0;
    if (activeTab === 0) {
      if (preparedProgramList && preparedProgramList.size > 0) {
        preparedProgramList.map((year) => {
          return year
            .get('levels', List())
            .filter((item) => item.get('term', 'regular') === selectedTerm)
            .map((level) => {
              value += parseInt(level.get('course', List()).size);
              return level;
            });
        });
      }
    } else if (activeTab === 1) {
      if (preparedDepartmentList && preparedDepartmentList.size > 0) {
        preparedDepartmentList.map((department) => {
          return department.get('subject', List()).map((subject) => {
            value +=
              parseInt(
                subject.get('courses', List()).filter((item) => item.get('AdminCourse') === true)
                  .size
              ) +
              parseInt(
                subject
                  .get('courses', List())
                  .filter((item) => item.get('participatingCourse') === true).size
              );
            return subject;
          });
        });
      }
    }
    return value;
  };

  circularProgressBar = (data) => {
    const course = Map({
      completed_session: data.get('completed_session', 0),
      no_session: data.get('no_session', 0),
    });
    const value = preparePercentageNew(course);
    const color =
      course.get('completed_session', 0) === course.get('no_session', 0)
        ? '#3BC10C'
        : course.get('completed_session', 0) > 0 &&
          course.get('completed_session', 0) !== course.get('no_session', 0)
        ? '#4f95ef'
        : '';
    return (
      <div className="row mt-3">
        {course.get('no_session', 0) === 0 ? (
          <div className="col-md-12 scheduleProgress text-center">
            <Trans i18nKey={'dashboard_view.scheduling_in_progress'} />
          </div>
        ) : (
          <>
            <div className="col-md-4">
              <CircularProgressbar
                value={value}
                text={`${value}%`}
                styles={buildStyles({
                  pathColor: color,
                  textColor: color,
                })}
              />
            </div>

            <div className="col-md-8 align-self-center mt-4 mt-md-0">
              <p className="mb-0 f-14 text-lightblack">
                {course.get('completed_session', 0) === course.get('no_session', 0) &&
                course.get('no_session', 0) !== 0 ? (
                  <span style={{ color: '#3BC10C' }}>
                    <Trans i18nKey={'dashboard_view.course_completed'} />
                  </span>
                ) : course.get('completed_session', 0) > 0 &&
                  course.get('completed_session', 0) !== course.get('no_session', 0) ? (
                  <span>
                    <Trans i18nKey={'dashboard_view.session_complete'} />
                  </span>
                ) : (
                  <span style={{ color: '#ec5e85' }}>
                    <Trans i18nKey={'dashboard_view.course_not_started'} />
                  </span>
                )}
              </p>
              <p className="mb-0 f-15 bold">
                {course.get('completed_session', 0)} / {course.get('no_session', 0)}
              </p>
            </div>
          </>
        )}{' '}
      </div>
    );
  };

  getProgramCourseVersion = (course) => {
    const { activeTab } = this.state;
    return (
      activeTab === 1 && (
        <p className="mb-0 f-15 bold" style={{ width: '90%' }}>
          {course.get('course_name', '')} / {course.get('curriculum', '')} / Y
          {course.get('year', '').replace('year', '')} /{' '}
          {levelRename(course.get('level', ''), this.state.programID)} / {course.get('term', '')}
        </p>
      )
    );
  };

  printCourseShowMore = (course) => {
    const { monitoringCourseDetail, userId, activeInstitutionCalendar, selectedRole } = this.props;
    const connector =
      userId +
      activeInstitutionCalendar.get('_id') +
      selectedRole.getIn(['_role_id', '_id'], '') +
      course.get('_program_id') +
      course.get('_course_id') +
      course.get('term') +
      course.get('level') +
      course.get('rotation_count', 0);
    const data = monitoringCourseDetail.get(connector, Map());
    return (
      <div className="open-box-animations">
        {this.circularProgressBar(data)}
        {data.get('no_session', 0) !== 0 && (
          <div className="row m-2">
            <div className="mt-2">
              <span className="user_icon mr-3" style={{ background: '#FFFDED' }}>
                {' '}
                <i className="fa fa-user text-orange mr-3" aria-hidden="true"></i>
                <Trans i18nKey={'dashboard_view.final_warning'} /> -{' '}
                {data.get('final_warning', '0')}
              </span>
            </div>
            <div className="mt-2">
              <span className="user_icon  mr-3" style={{ background: '#FFEDED' }}>
                {' '}
                <i className="fa fa-user text-red mr-3" aria-hidden="true"></i>
                {jsUcfirstAll(data.get('denialLabel', 'Denial'))} - {data.get('denial', '0')}
              </span>
            </div>
          </div>
        )}
      </div>
    );
  };

  toggleCourses = (course) => {
    const { showCourses } = this.state;
    const {
      getMonitoringCourseDetail,
      userId,
      activeInstitutionCalendar,
      selectedRole,
    } = this.props;
    const courseId = course.get('_course_id');
    const connector =
      course.get('term', '') +
      course.get('level', '') +
      course.get('rotation', '') +
      course.get('rotation_count', 0);
    let updatedCourse = [...showCourses, courseId + connector];
    this.setState({ loader: courseId + connector });
    getMonitoringCourseDetail(
      userId,
      activeInstitutionCalendar.get('_id'),
      selectedRole.getIn(['_role_id', '_id'], ''),
      course.get('_program_id'),
      courseId,
      course.get('term'),
      course.get('level'),
      course.get('rotation_count', 0),
      () => {
        this.setState({ loader: '', showCourses: updatedCourse });
      }
    );
  };

  getCourseHtml = (course) => {
    const { showCourses, loader } = this.state;
    const linkActiveUrl = getActiveLink(CheckPermission);
    const linkActive = linkActiveUrl !== '' && course.get('isAdmin', false);
    //CheckPermission('pages', 'Reports and Analytics', 'Course Details', 'Course View');
    const connector =
      course.get('_course_id') +
      course.get('term', '') +
      course.get('level', '') +
      course.get('rotation', '') +
      course.get('rotation_count', 0);
    return (
      <div className={`position-relative dash_box ${linkActive ? 'remove_hover' : ''} `}>
        <div
          className={`${showCourses.includes(connector) ? 'border_bottom_dash' : ''}`}
          onClick={() => {
            if (linkActive) {
              this.goToReports(course, linkActiveUrl);
            }
          }}
        >
          {this.getProgramCourseVersion(course)}
          <p className="mb-1 f-15 bold" style={{ width: '90%' }}>
            {' '}
            {course.get('courses_number', '')} - {course.get('courses_name', '')}
            {getVersionName(course)}
            {course.get('rotation', 'no') === 'yes' &&
              ` - R${course.get('rotation_count', '')}`}{' '}
            {course.get('course_shared', false) && (
              <Tooltips
                title={`${t('dashboard_view.shared_from')} ${course.get(
                  'course_shared_program',
                  ''
                )}`}
                className="pl-2"
              >
                <span className="badge badge-light version_bg" style={{ padding: '5px 10px' }}>
                  S
                </span>
              </Tooltips>
            )}{' '}
          </p>
          <p className="mb-2 f-14 text-lightblack">
            {' '}
            {getEnvLabelChanged()
              ? indVerRename(course.get('model', ''), this.state.programID)
              : t(
                  course.get('model', '') === 'standard'
                    ? 'constant.standard'
                    : 'constant.selective'
                )}{' '}
            | {getTranslatedDuration(getFormattedCourseDuration(course))}
          </p>
        </div>
        {showCourses.includes(connector) ? (
          <React.Fragment
            onClick={() => {
              if (linkActive) {
                this.goToReports(course, linkActiveUrl);
              }
            }}
          >
            {this.printCourseShowMore(course)}
          </React.Fragment>
        ) : (
          <div
            className={`c-middle${lang === 'ar' ? '-left' : ''} text-primary`}
            onClick={() => this.toggleCourses(course)}
          >
            <Tooltips title={t('dashboard_view.show_more')}>
              <i className={`fa fa-${loader === connector ? 'spinner' : 'chevron-down'}`}></i>
            </Tooltips>
          </div>
        )}
        {/* {this.circularProgressBar(percentageValue, course)}
        {course.get('no_session', 0) !== 0 && (
          <div className="row m-2">
            <div className="mt-2">
              <span className="user_icon mr-3" style={{ background: '#FFFDED' }}>
                {' '}
                <i className="fa fa-user text-orange pr-3" aria-hidden="true"></i>
                Final Warning - {course.get('final_warning', '0')}
              </span>
            </div>
            <div className="mt-2">
              <span className="user_icon  mr-3" style={{ background: '#FFEDED' }}>
                {' '}
                <i className="fa fa-user text-red pr-3" aria-hidden="true"></i>
                Denial - {course.get('denial', '0')}
              </span>
            </div>
          </div>
        )} */}
      </div>
    );
  };

  showHideYearLevels = (index, id, status) => {
    const { preparedProgramList } = this.state;

    const levelIndex = preparedProgramList
      .getIn([index, 'levels'])
      .findIndex((level) => level.get('_id') === id);

    let updateValue = preparedProgramList.setIn([index, 'levels', levelIndex, 'show'], status);
    this.setState({ preparedProgramList: updateValue });
  };

  showHideDepartmentSubject = (deptId, subjectId, status) => {
    const { preparedDepartmentList } = this.state;
    const departmentIndex = preparedDepartmentList.findIndex((dept) => dept.get('_id') === deptId);
    const subjectIndex = preparedDepartmentList
      .getIn([departmentIndex, 'subject'])
      .findIndex((sub) => sub.get('_id') === subjectId);

    const updateValue = preparedDepartmentList.setIn(
      [departmentIndex, 'subject', subjectIndex, 'show'],
      status
    );
    this.setState({ preparedDepartmentList: updateValue });
  };

  checkDepartmentHasCourse = (courses, status) => {
    const { selectedProgram, selectedStaff } = this.state;
    return (
      courses
        .filter((item) => item.get(status) === true)
        .filter((item) => (selectedStaff !== '' ? item.get('staffs', List()).size > 0 : item))
        .filter((item) => {
          if (selectedStaff !== '') {
            const staffs = item
              .get('staffs', List())
              .map((stf) => stf.get('_staff_id'))
              .toJS();
            return staffs.includes(selectedStaff);
          } else {
            return item;
          }
        })
        .filter((item) =>
          selectedProgram !== ''
            ? item.getIn(['administration', 'program_name'], '') === selectedProgram
            : item
        ).size > 0
    );
  };

  goToReports = (course, linkActiveUrl) => {
    const { history, setData } = this.props;
    setData(Map({ courseOverview: Map() }));
    history.push(
      `/reports/programs/${eString(course.get('_program_id'))}/courses/${eString(
        course.get('_course_id')
      )}/${linkActiveUrl}?level=${eString(course.get('level'))}&term=${eString(
        course.get('term')
      )}&rotation=${eString(course.get('rotation'))}&rotationCount=${eString(
        course.get('rotation_count', 0)
      )}`
    );
  };

  yearLevelFilter = () => {
    const { preparedProgramList, selectedYear, selectedLevel, selectedTerm } = this.state;
    const { academicYearList, loading } = this.props;
    if (academicYearList.isEmpty() && !loading.get('GET_ACADEMIC_YEAR_LEVEL', false)) {
      return (
        <div className="text-center">
          <Trans i18nKey={'no_data'} />
        </div>
      );
    }
    if (preparedProgramList && preparedProgramList.size > 0) {
      let years = preparedProgramList.map((year) => {
        return {
          name: `${t('dashboard_view.year')} ${year.get('year').replace('year', '')}`,
          value: year.get('year'),
        };
      });
      years = [{ name: t('all'), value: '' }, ...years];
      let levels = [{ name: t('all'), value: '' }];
      if (selectedYear !== '') {
        const yearIndex = preparedProgramList.findIndex(
          (year) => year.get('year') === selectedYear
        );
        levels = preparedProgramList.getIn([yearIndex, 'levels'], List()).map((level) => {
          return {
            name: getEnvLabelChanged()
              ? levelRename(level.get('level_no'), this.state.programID)
              : `${t('dashboard_view.level')} ${level.get('level_no').replace('Level', '')}`,
            value: level.get('level_no'),
          };
        });
        levels = [...new Map(levels.map((item) => [item['value'], item])).values()];
        levels = [{ name: t('all'), value: '' }, ...levels];
      }
      const programId = getURLParams('programId', true);
      return (
        <div className="row">
          <div className="col-md-12">
            <div className="row">
              <div className="col-md-4 px-2">
                <Input
                  elementType={'select'}
                  elementConfig={{
                    options: preparedProgramList.getIn([0, 'termsOptions'], List()).map((item) => {
                      return { name: t(item), value: item };
                    }),
                  }}
                  value={selectedTerm}
                  className={'selectArrow'}
                  label={
                    getEnvLabelChanged()
                      ? stringToUC(indVerRename('Term', this.state.programID))
                      : t('dashboard_view.term', {
                          Term: indVerRename('Term', programId),
                        })
                  }
                  labelclass={'f-13 text-uppercase'}
                  changed={(e) => this.handleSelect(e, 'selectedTerm')}
                />
              </div>
              <div className="col-md-4 px-2">
                <Input
                  elementType={'select'}
                  elementConfig={{
                    options: years,
                  }}
                  value={selectedYear}
                  className={'selectArrow'}
                  label={t('dashboard_view.year')}
                  labelclass={'f-13 text-uppercase'}
                  changed={(e) => this.handleSelect(e, 'selectedYear')}
                />
              </div>

              <div className="col-md-4 px-2">
                <Input
                  elementType={'select'}
                  elementConfig={{
                    options: levels,
                  }}
                  value={selectedLevel}
                  className={'selectArrow'}
                  label={
                    getEnvLabelChanged()
                      ? stringToUC(indVerRename('Level', this.state.programID))
                      : t('dashboard_view.level')
                  }
                  labelclass={'f-13 text-uppercase'}
                  changed={(e) => this.handleSelect(e, 'selectedLevel')}
                />
              </div>
            </div>
          </div>
        </div>
      );
    }
  };

  departmentSubjectFilter = () => {
    const {
      preparedDepartmentList,
      selectedDepartment,
      selectedStaff,
      selectedSubject,
      selectedProgram,
    } = this.state;
    const { departmentSubjectLists, loading } = this.props;
    if (departmentSubjectLists.size === 0 && !loading.get('GET_ACADEMIC_YEAR_LEVEL', false))
      return (
        <div className="text-center">
          <Trans i18nKey={'no_data'} />
        </div>
      );
    if (preparedDepartmentList && preparedDepartmentList.size > 0) {
      let dList = preparedDepartmentList.map((item) => {
        return { name: item.get('department_name'), value: item.get('_id') };
      });

      dList = [{ name: t('all'), value: '' }, ...dList];
      let sList = [{ name: t('all'), value: '' }];
      if (selectedDepartment !== '') {
        sList = preparedDepartmentList
          .filter((item) => item.get('_id') === selectedDepartment)
          .reduce((_, el) => el.get('subject', List()), List())
          .map((item) => {
            return { name: item.get('subject_name'), value: item.get('_id') };
          });
        sList = [{ name: t('all'), value: '' }, ...sList];
      }

      staffList = [{ name: t('all'), value: '' }];
      preparedDepartmentList &&
        preparedDepartmentList
          .filter((item) =>
            selectedDepartment !== '' ? item.get('_id') === selectedDepartment : item
          )
          .map((dept) => {
            return dept
              .get('subject', List())
              .filter((item) =>
                selectedSubject !== '' ? item.get('_id') === selectedSubject : item
              )
              .map((subject) => {
                return subject.get('courses', List()).map((course) => {
                  return (
                    course.get('staffs', List()).size > 0 &&
                    course.get('staffs', List()).map((staff) => {
                      return staffList.push({
                        name: formatFullName(staff.get('staff_name', {}).toJS()),
                        value: staff.get('_staff_id', ''),
                      });
                    })
                  );
                });
              });
          });

      staffList = staffList.filter(
        (ele, ind) => ind === staffList.findIndex((elem) => elem.value === ele.value)
      );

      return (
        <div className="row">
          <div className="col-md-12">
            <div className="row">
              <div className="col-md-3 px-2">
                <Input
                  elementType={'select'}
                  elementConfig={{
                    options: dList,
                  }}
                  value={selectedDepartment}
                  className={'selectArrow'}
                  label={t('dashboard_view.department')}
                  labelclass={'f-13 text-uppercase'}
                  changed={(e) => this.handleSelect(e, 'selectedDepartment')}
                />
              </div>
              <div className="col-md-3 px-2">
                <Input
                  elementType={'select'}
                  elementConfig={{
                    options: sList,
                  }}
                  value={selectedSubject}
                  className={'selectArrow'}
                  label={t('dashboard_view.subject')}
                  labelclass={'f-13 text-uppercase'}
                  changed={(e) => this.handleSelect(e, 'selectedSubject')}
                />
              </div>
              <div className="col-md-3 px-2">
                <Input
                  elementType={'select'}
                  elementConfig={{
                    options: staffList,
                  }}
                  value={selectedStaff}
                  className={'selectArrow'}
                  label={t('dashboard_view.staff')}
                  labelclass={'f-13 text-uppercase'}
                  changed={(e) => this.handleSelect(e, 'selectedStaff')}
                />
              </div>
              <div className="col-md-3 px-2">
                <Input
                  elementType={'select'}
                  elementConfig={{
                    options: this.groupProgramName(),
                  }}
                  value={selectedProgram}
                  className={'selectArrow'}
                  label={t('dashboard_view.program')}
                  labelclass={'f-13 text-uppercase'}
                  changed={(e) => this.handleSelect(e, 'selectedProgram')}
                />
              </div>
            </div>
          </div>
        </div>
      );
    }
  };

  groupProgramName = () => {
    const { activeTab, preparedDepartmentList, selectedDepartment, selectedSubject } = this.state;
    let value = [];
    if (activeTab === 1) {
      if (preparedDepartmentList && preparedDepartmentList.size > 0) {
        preparedDepartmentList
          .filter((item) =>
            selectedDepartment !== '' ? item.get('_id') === selectedDepartment : item
          )
          .map((department) => {
            return department
              .get('subject', List())
              .filter((item) =>
                selectedSubject !== '' ? item.get('_id') === selectedSubject : item
              )
              .map((subject) => {
                return subject.get('courses', List()).map((course) => {
                  value.push({
                    name: course.getIn(['administration', 'program_name']),
                    value: course.getIn(['administration', 'program_name']),
                  });
                  return course;
                });
              });
          });
      }
      value = [
        { name: t('all'), value: '' },
        ...value.filter((ele, ind) => ind === value.findIndex((elem) => elem.name === ele.name)),
      ];
    }
    return value;
  };

  filteredCourses = (courses, status) => {
    const { selectedProgram, selectedStaff } = this.state;
    return (
      courses &&
      courses.size > 0 &&
      courses
        .filter((item) => item.get(status) === true)
        .filter((item) =>
          selectedProgram !== ''
            ? item.getIn(['administration', 'program_name'], '') === selectedProgram
            : item
        )
        .filter((item) => (selectedStaff !== '' ? item.get('staffs', List()).size > 0 : item))
        .filter((item) => {
          if (selectedStaff !== '') {
            const staffs = item
              .get('staffs', List())
              .map((stf) => stf.get('_staff_id'))
              .toJS();
            return staffs.includes(selectedStaff);
          } else {
            return item;
          }
        })
        .map((course, courseIndex) => {
          const percentageValue = preparePercentageNew(course);
          return (
            <div className="col-md-4 mb-2" key={courseIndex}>
              {this.getCourseHtml(course, percentageValue)}
            </div>
          );
        })
    );
  };

  checkCourseData = (levels) => {
    if (levels.size > 0) {
      const countOfLevelCourse = levels.filter((item) => item.get('course', List()).size > 0);
      if (countOfLevelCourse.size === levels.size) {
        return true;
      }
    }
    return false;
  };

  yearLevelView = () => {
    const { preparedProgramList, selectedYear, selectedLevel, selectedTerm } = this.state;
    const { academicYearList } = this.props;
    return (
      <>
        {!academicYearList.isEmpty() &&
          preparedProgramList &&
          preparedProgramList.size > 0 &&
          preparedProgramList
            .filter((item) => (selectedYear !== '' ? item.get('year') === selectedYear : item))
            .map((year, yIndex) => {
              return year
                .get('levels', List())
                .filter((item) => item.get('term', 'regular') === selectedTerm)
                .filter((item) =>
                  selectedLevel !== '' ? item.get('level_no') === selectedLevel : item
                )
                .map((level, levelIndex) => {
                  const hasData = this.checkCourseData(year.get('levels', List()));

                  return (
                    <React.Fragment key={levelIndex}>
                      {levelIndex === 0 && hasData && (
                        <div className="border-top p-2" key={levelIndex}>
                          <div className="d-flex justify-content-between">
                            <p className="mb-0 bold f-18">
                              {t('dashboard_view.year')} {year.get('year', '').replace('year', '')}
                            </p>
                          </div>
                        </div>
                      )}
                      {level.get('course', List()).size > 0 && (
                        <div className="p-4 border-top">
                          <div className="d-flex  justify-content-between">
                            <p className="mb-1 f-18 bold">
                              {getEnvLabelChanged() ? (
                                <>{levelRename(level.get('level_no', ''), this.state.programID)} </>
                              ) : (
                                <>
                                  {t('dashboard_view.level')}{' '}
                                  {level.get('level_no', '').replace('Level', '')}
                                </>
                              )}
                            </p>
                            <i
                              onClick={() =>
                                this.showHideYearLevels(
                                  yIndex,
                                  level.get('_id'),
                                  !level.get('show', true)
                                )
                              }
                              className={`fa fa-chevron-${
                                level.get('show', true) === true ? 'down' : 'up'
                              } text-skyblue remove_hover`}
                              aria-hidden="true"
                            ></i>
                          </div>
                          {level.get('show', true) === true && (
                            <>
                              <div className="d-flex text-left">
                                <p className="mb-2 f-16 text-lightblack termOption">
                                  {' '}
                                  <b
                                    className="text-capitalize"
                                    style={{ display: 'block', lineHeight: '25px' }}
                                  >
                                    {getTranslatedDuration(`(${moment(
                                      level.get('start_date')
                                    ).format('MMM D')} ${t('to')} 
                                    ${moment(level.get('end_date')).format('MMM D')})`)}
                                  </b>
                                  <i>
                                    {level.get('term', '')}
                                    , <Trans i18nKey={'dashboard_view.curriculum'} /> -{' '}
                                    {level.get('curriculum', '')}{' '}
                                  </i>
                                  {/* <Menu
                                  direction={'left'}
                                  overflow={'auto'}
                                  position={'initial'}
                                  menuButton={<i className="fa fa-caret-down ml-2"></i>}
                                >
                                  {year.get('termsOptions', List()).map((term) => {
                                    return (
                                      <MenuItem
                                        key={term}
                                        // onClick={() => callBack(item)}
                                        styles={
                                          term === level.get('term', '')
                                            ? {
                                                backgroundColor: '#ebebeb',
                                              }
                                            : {}
                                        }
                                      >
                                        {jsUcfirst(term)}, Curriculum -{' '}
                                        {level.get('curriculum', '')}{' '}
                                      </MenuItem>
                                    );
                                  })}
                                </Menu>
                             */}
                                </p>
                              </div>
                              <div className="row" style={{ clear: 'both' }}>
                                {level.get('course', List()).map((course, courseIndex) => {
                                  const percentageValue = preparePercentageNew(course);
                                  return (
                                    <div className="col-md-4 mb-2" key={courseIndex}>
                                      {this.getCourseHtml(course, percentageValue)}
                                    </div>
                                  );
                                })}
                              </div>
                            </>
                          )}
                        </div>
                      )}
                    </React.Fragment>
                  );
                });
            })}
      </>
    );
  };

  departmentSubjectView = () => {
    const { preparedDepartmentList, selectedDepartment, selectedSubject } = this.state;
    const { departmentSubjectLists } = this.props;
    if (
      departmentSubjectLists.size > 0 &&
      preparedDepartmentList &&
      preparedDepartmentList.size > 0
    ) {
      return preparedDepartmentList
        .filter((item) =>
          selectedDepartment !== '' ? item.get('_id') === selectedDepartment : item
        )
        .map((department) => {
          let sharedDept = '';
          if (department.get('shared', false)) {
            // sharedDept = department.get('shared_with', List()).map((share, index) => {
            //   return (
            //     <p className="ml-3 shardDiv" key={index}>
            //       <i className="fa fa-share-alt mr-3 f-22 text-blue" aria-hidden="true"></i>Shared from{' '}
            //       {share.get('program_name')}
            //     </p>
            //   );
            // });
            sharedDept = (
              <p className={`shardDiv ${lang === 'ar' ? 'digi-mr-16' : 'digi-ml-16'}`}>
                <i className="fa fa-share-alt mr-3 f-22 text-blue" aria-hidden="true"></i>
                <Trans i18nKey={'dashboard_view.shared_from'} />{' '}
                {department.get('shared_from_program_name')}
              </p>
            );
          }

          return (
            department
              .get('subject', List())
              .filter((item) =>
                selectedSubject !== '' ? item.get('_id') === selectedSubject : item
              )
              // .filter((item) => (selectedStaff !== '' ? item.get('_id') === staffSubjectId : item))
              .map((subject, subjectIndex) => {
                const AdminCourses = this.checkDepartmentHasCourse(
                  subject.get('courses'),
                  'AdminCourse'
                );
                const ParticipatoryCourses = this.checkDepartmentHasCourse(
                  subject.get('courses'),
                  'participatingCourse'
                );

                let sharedSubject = '';
                if (subject.get('shared', false)) {
                  // sharedSubject = subject.get('shared_with', List()).map((share, index) => {
                  //   return (
                  //     <p className="ml-3 shardDiv" style={{ display: 'inline' }} key={index}>
                  //       <i className="fa fa-share-alt mr-3 f-22 text-blue" aria-hidden="true"></i>Shared
                  //       from {share.get('program_name')}
                  //     </p>
                  //   );
                  // });

                  sharedSubject = (
                    <p
                      className={`shardDiv ${lang === 'ar' ? 'digi-mr-16' : 'digi-ml-16'}`}
                      style={{ display: 'inline' }}
                    >
                      <i className="fa fa-share-alt mr-3 f-22 text-blue" aria-hidden="true"></i>
                      <Trans i18nKey={'dashboard_view.shared_from'} />{' '}
                      {subject.get('subject_shared_from_program_name')}
                    </p>
                  );
                }

                return (
                  <React.Fragment key={subjectIndex}>
                    {(AdminCourses || ParticipatoryCourses) && (
                      <div className="border-top p-4" key={subjectIndex}>
                        <div className="d-flex">
                          <p className="mb-0 bold f-18"> {department.get('department_name', '')}</p>
                          {department.get('shared', false) && sharedDept}
                        </div>
                        <>
                          <div className="d-flex justify-content-between">
                            <p className="mb-1 f-18 bold">
                              {' '}
                              {subject.get('subject_name', '')}{' '}
                              {subject.get('shared', false) && sharedSubject}
                            </p>
                            <i
                              onClick={() =>
                                this.showHideDepartmentSubject(
                                  department.get('_id'),
                                  subject.get('_id'),
                                  !subject.get('show', true)
                                )
                              }
                              className={`fa fa-chevron-${
                                subject.get('show', true) === true ? 'down' : 'up'
                              } text-skyblue remove_hover`}
                              aria-hidden="true"
                            ></i>
                          </div>
                          {subject.get('show', true) === true && (
                            <>
                              {AdminCourses && (
                                <>
                                  <div>
                                    <p className="mb-2 f-16 text-lightblack text-left">
                                      <Trans i18nKey={'dashboard_view.admin_course'} />
                                    </p>
                                  </div>
                                  <div className="row" style={{ clear: 'both' }}>
                                    {this.filteredCourses(subject.get('courses'), 'AdminCourse')}
                                  </div>
                                </>
                              )}
                              {ParticipatoryCourses && (
                                <>
                                  <div>
                                    <p className="mb-2 f-16 text-lightblack text-left">
                                      <Trans i18nKey={'dashboard_view.participatory_course'} />
                                    </p>
                                  </div>
                                  <div className="row" style={{ clear: 'both' }}>
                                    {this.filteredCourses(
                                      subject.get('courses'),
                                      'participatingCourse'
                                    )}
                                  </div>
                                </>
                              )}
                            </>
                          )}
                        </>
                      </div>
                    )}
                  </React.Fragment>
                );
              })
          );
        });
    }
    return <></>;
  };

  render() {
    const programName = getURLParams('programName', true);
    const { activeTab } = this.state;
    const { academicYearList, departmentSubjectLists } = this.props;

    return (
      <div className="main bg-gray pb-5">
        <div className="bg-white pt-3">
          <div className="container-fluid">
            <div className="d-flex justify-content-start">
              <div className="mb-3">
                <p className="font-weight-bold mb-0 f-17">
                  {' '}
                  <i
                    className={`fa fa-arrow-${
                      lang === 'ar' ? 'right pr-3 mr-3' : 'left pr-3'
                    } remove_hover`}
                    aria-hidden="true"
                    onClick={() => this.props.history.push('/dashboard')}
                  ></i>
                  {programName}{' '}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray">
          <div className="container-fluid">
            <div className="row pb-4">
              {this.sideView()}
              <div className="col-md-9">
                <div className="p-4">
                  <div className="pt-4 pb-4">
                    {!academicYearList.isEmpty() || departmentSubjectLists.size > 0
                      ? this.headerCounts()
                      : null}
                    <div className="bg-white border-radious-8 pb-5">
                      <div className="p-4">
                        {activeTab === 0 ? this.yearLevelFilter() : this.departmentSubjectFilter()}
                      </div>
                      <div>
                        {activeTab === 0 ? this.yearLevelView() : this.departmentSubjectView()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

DashboardDetail.propTypes = {
  userId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  //getProgramsLists: PropTypes.func,
  getDashboardDataType: PropTypes.func,
  getDepartmentSubject: PropTypes.func,
  getAcademicYear: PropTypes.func,
  //configuredProgramLists: PropTypes.instanceOf(List),
  departmentSubjectLists: PropTypes.instanceOf(List),
  totalStaff: PropTypes.number,
  updateConfigureSettings: PropTypes.func,
  saveConfigureSettings: PropTypes.func,
  selectedRole: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  loading: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  academicYearList: PropTypes.instanceOf(Map),
  getMonitoringCourseDetail: PropTypes.func,
  monitoringCourseDetail: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  return {
    userId: selectUserId(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    //programsLists: selectDashboardProgramList(state),
    //configuredProgramLists: selectConfiguredProgramLists(state),
    departmentSubjectLists: selectDepartmentSubjectLists(state),
    totalStaff: selectStaffCount(state),
    academicYearList: selectAcademicYearLists(state),
    selectedRole: selectSelectedRole(state),
    loading: selectLoading(state),
    monitoringCourseDetail: selectMonitoringCourseDetail(state),
  };
};

export default withRouter(connect(mapStateToProps, { ...actions, ...actions1 })(DashboardDetail));
