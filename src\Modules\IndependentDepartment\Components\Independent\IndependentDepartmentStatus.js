import React, { useContext } from 'react';
import ReactECharts from 'echarts-for-react';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { List } from 'immutable';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import { getShortString, GetShortString } from 'Modules/Shared/v2/Configurations';

const flexStyle = {
  display: 'flex',
};
const axisStyle = {
  fontSize: '18px',
  fontFamily: 'roboto',
};

function IndependentDepartmentStatus() {
  const department = useContext(parentContext.departmentContext);
  const { departmentDetails, translateInput } = department;
  function getDepartmentsData(key) {
    return departmentDetails
      .get('programDistribution', List())
      .map((item) => item.get(key, 'Admin'))
      .toJS();
  }
  const options = {
    legend: {
      data: [translateInput.departmentName, translateInput.subjectName],
      icon: 'circle',
    },
    dataZoom: [
      {
        type: 'inside',
      },
      {
        type: 'slider',
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'category',
      style: flexStyle,
      data: getDepartmentsData('programName'),
      nameGap: 30,
      nameLocation: 'center',
      nameTextStyle: axisStyle,
    },
    yAxis: {
      type: 'value',
      nameGap: 30,
      nameTextStyle: axisStyle,
      boundaryGap: [0, '100%'],
      minInterval: 1,
    },
    series: [
      {
        name: translateInput.departmentName,
        data: getDepartmentsData('departments'),
        type: 'bar',
        barMaxWidth: 30,
        itemStyle: { color: '#16A34A' },
        lineStyle: {
          color: '#16A34A',
          barMaxWidth: 30,
        },
      },
      {
        name: translateInput.subjectName,
        data: getDepartmentsData('subjects'),
        type: 'bar',
        barMaxWidth: 30,
        itemStyle: { color: '#86EFAC' },
        lineStyle: {
          color: '#86EFAC',
          barMaxWidth: 30,
        },
      },
    ],
  };

  function DisplayNameAndCount(name, count) {
    return (
      <div className="digi-border-gray rounded digi-mt-12 digi-mb-12">
        <h4 className="float-left f-22 digi-pl-12  mb-2 mt-2 font-weight-normal digi-gray-neutral">
          {name}
        </h4>
        <h3 className="float-right mb-2 mt-2 f-20 digi-pr-12">{count}</h3>
        <div className="clearfix"></div>
      </div>
    );
  }

  return (
    <div className="row align-items-center mt-3 mb-2">
      <div className="col-md-6 col-xl-3 col-lg-3 col-6 col-sm-6 mt-3">
        <div className="digi-border-gray rounded pt-4 pb-4 pl-3 pr-3">
          <p className="f-18 font-weight-500 mb-1 mt-1 pb-2 pt-1 text-overflow-260">
            <Trans
              components={{
                departmentName: <GetShortString el={translateInput.departmentName} length={10} />,
                subjectName: <GetShortString el={translateInput.subjectName} length={10} />,
              }}
            >
              departments_subjects.total_departments_subjects
            </Trans>
          </p>
          {DisplayNameAndCount(
            getShortString(translateInput.departmentName, 15),
            departmentDetails.get('departmentCount', 0)
          )}
          {DisplayNameAndCount(
            getShortString(translateInput.subjectName, 15),
            departmentDetails.get('subjectCount', 0)
          )}
        </div>
      </div>

      <div className="col-md-6 col-xl-3 col-lg-3 col-6 col-sm-6 mt-3">
        <div className="digi-border-gray rounded pt-4 pb-4 pl-3 pr-3">
          <p className="f-18 font-weight-500 mb-1 mt-1 pb-2 pt-1 text-overflow-260">
            {' '}
            <Trans
              components={{
                departmentName: <GetShortString el={translateInput.departmentName} length={15} />,
              }}
            >
              departments_subjects.department_type
            </Trans>
          </p>
          {DisplayNameAndCount(
            t('user_management.academic'),
            departmentDetails.get('academicCount', 0)
          )}
          {DisplayNameAndCount(
            t('departments_subjects.admin'),
            departmentDetails.get('adminCount', 0)
          )}
        </div>
      </div>

      <div className="col-md-12 col-xl-6 col-lg-6 col-12 col-sm-12 mt-3">
        <div className="digi-border-gray rounded p-1">
          {departmentDetails.get('programDistribution', List()).size > 0 && (
            <ReactECharts
              style={{ width: '100%', height: '200px' }}
              option={options}
              className="echarts-height-200"
            />
          )}
        </div>
      </div>
    </div>
  );
}
export default IndependentDepartmentStatus;
