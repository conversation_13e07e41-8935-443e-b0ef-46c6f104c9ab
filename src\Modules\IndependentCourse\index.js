import React, { useEffect } from 'react';
import { Route } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { withRouter, <PERSON> } from 'react-router-dom';
import { Map } from 'immutable';

import Breadcrumb from 'Widgets/Breadcrumb/v2/Breadcrumb';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';
import IndependentCourse from './IndependentCourseIndex';
import {
  selectIsLoading,
  selectMessage,
  selectBreadCrumb,
} from '_reduxapi/program_input/v2/selectors';
import { selectProgramInput } from '_reduxapi/global_configuration/selectors';
import * as actions from '_reduxapi/program_input/v2/actions';
import * as globalConfigActions from '_reduxapi/global_configuration/actions';
import i18n from '../../i18n';
import { getInstitutionHeader } from 'v2/utils';

function ProgramInput({
  history,
  message,
  setBreadCrumb,
  breadcrumbs,
  isLoading,
  getProgramInput,
  programInput,
}) {
  useEffect(() => {
    setBreadCrumb([{ to: '#', label: i18n.t('independentCourse.independentCourseLabel') }]);
  }, [setBreadCrumb]);

  const institutionHeader = getInstitutionHeader(history);
  useEffect(() => {
    getProgramInput({ header: institutionHeader });
  }, []); // eslint-disable-line

  const items = [];
  if (breadcrumbs && breadcrumbs.size > 0) {
    breadcrumbs.map((bread) => {
      items.push({ to: bread.get('to'), label: bread.get('label') });
      return bread;
    });
  }

  return (
    <div>
      {message !== '' && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>
        {items.map(({ to, label }) => (
          <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
            {label}
          </Link>
        ))}
      </Breadcrumb>
      <Route path={`/:type/:id/:name/independent-course`}>
        <IndependentCourse settingsData={programInput} />
      </Route>
    </div>
  );
}

ProgramInput.propTypes = {
  isLoading: PropTypes.bool,
  setBreadCrumb: PropTypes.func,
  getProgramInput: PropTypes.func,
  history: PropTypes.object,
  breadcrumbs: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  message: PropTypes.string,
  programInput: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    breadcrumbs: selectBreadCrumb(state),
    programInput: selectProgramInput(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, { ...actions, ...globalConfigActions })
)(ProgramInput);
