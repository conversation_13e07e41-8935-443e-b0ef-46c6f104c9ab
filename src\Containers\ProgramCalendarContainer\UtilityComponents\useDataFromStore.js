import { useSelector } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';

const useDataFromStore = () => {
  const match = useRouteMatch();
  const active = match.params.year || 'year2';
  const sem = match.params.sem || 'semester1';

  const is_interim = useSelector(({ calender }) => calender.interim);
  const year = useSelector(({ calender }) => ({
    [active]: calender[active],
  }));

  let start,
    start1,
    end,
    end1,
    level_one,
    level_two,
    course1,
    course2,
    apply1,
    apply2,
    apply3,
    apply4,
    events,
    events1,
    semester,
    is_rotation,
    is_rotation1,
    electiveCourse,
    electiveCourse1,
    rotational_course,
    rotational_course1,
    new_start,
    new_end,
    new_courses;

  if (is_interim) {
    let check_run, check_run1;

    start = year[active]?.['interim_data']?.[sem]?.[0]?.['start_date'];
    end = year[active]?.['interim_data']?.[sem]?.[0]?.['end_date'];
    is_rotation = year[active]?.['interim_data']?.[sem]?.[0]?.['rotation'];

    start1 = year[active]?.['interim_data']?.[sem]?.[1]?.['start_date'];
    end1 = year[active]?.['interim_data']?.[sem]?.[1]?.['end_date'];
    is_rotation1 = year[active]?.['interim_data']?.[sem]?.[1]?.['rotation'];

    events = year[active]?.['interim_data']?.[sem]?.[0]?.['events'].filter(
      (item) => item.event_type !== 'exam'
    );
    events1 = year[active]?.['interim_data']?.[sem]?.[1]?.['events'].filter(
      (item) => item.event_type !== 'exam'
    );

    rotational_course = year[active]?.['interim_data']?.[sem]?.[0]?.['rotation_course'];
    rotational_course1 = year[active]?.['interim_data']?.[sem]?.[1]?.['rotation_course'];

    course1 = year[active]?.['interim_data']?.[sem]?.[0]?.['course'];
    course2 = year[active]?.['interim_data']?.[sem]?.[1]?.['course'];

    level_one = year[active]?.['interim_data']?.[sem]?.[0]?.['level_no'];
    level_two = year[active]?.['interim_data']?.[sem]?.[1]?.['level_no'];

    check_run =
      (rotational_course && rotational_course?.length !== 0) || (course1 && course1?.length !== 0);
    check_run1 =
      (rotational_course1 && rotational_course1?.length !== 0) ||
      (course2 && course2?.length !== 0);

    apply1 = check_run ? 'design' : '';
    apply2 = check_run ? '' : 'none';
    apply3 = check_run1 ? 'design' : '';
    apply4 = check_run1 ? '' : 'none';

    electiveCourse =
      !rotational_course ||
      rotational_course[0]?.['course'].filter((item) => item.model === 'elective');

    electiveCourse1 =
      !rotational_course1 ||
      rotational_course1[0]?.['course'].filter((item) => item.model === 'elective');

    semester = [1, 2];
  } else {
    let check_run;

    const modified_year = year[active]?.['raw_data']?.['level']?.sort(
      (a, b) => Number(a.level_no) - Number(b.level_no)
    );

    start = modified_year?.[0]?.['start_date'];
    end = modified_year?.[1]?.['end_date'] || modified_year?.[0]?.['end_date'];
    // based on new requirement
    new_start = modified_year?.[0]?.['start_date'];
    new_end = modified_year?.[1]?.['end_date'] || modified_year?.[0]?.['end_date'];
    // based on new requirement end

    is_rotation = modified_year?.[0]?.['rotation'];

    let level_one_events = modified_year?.[0]?.['events'].filter((item) => {
      if (is_rotation === 'no') {
        if (item.event_type !== 'exam') {
          return true;
        }
        return false;
      }
      return true;
    });

    let level_two_events = modified_year?.[1]?.['events'].filter((item) => {
      if (is_rotation === 'no') {
        if (item.event_type !== 'exam') {
          return true;
        }
        return false;
      }
      return true;
    });

    if (level_one_events) {
      if (level_two_events) {
        events = [...level_one_events, ...level_two_events];
      } else {
        events = [...level_one_events];
      }
    }

    rotational_course = modified_year?.[0]?.['rotation_course'];
    course1 = modified_year?.[0]?.['course'] || [];
    course2 = modified_year?.[1]?.['course'] || [];

    // based on new requirement start
    new_courses = [];
    if (course1.length) {
      const indexed_course = course1.map((item, i) => ({
        ...item,
        course_index: 0,
        actual_index: i,
      }));
      new_courses = [...indexed_course];
    }
    if (course2.length) {
      const indexed_course = course2.map((item, i) => ({
        ...item,
        course_index: 1,
        actual_index: i,
      }));
      new_courses = [...new_courses, ...indexed_course];
    }
    // based on new requirement end

    level_one = modified_year?.[0]?.['level_no'];
    level_two = modified_year?.[1]?.['level_no'];

    check_run =
      (rotational_course &&
        rotational_course?.[0]?.['course'] &&
        rotational_course?.[0]?.['course']?.length !== 0) ||
      (course1 && course1?.length !== 0) ||
      (course2 && course2?.length !== 0);

    apply1 = check_run ? 'design' : '';
    apply2 = check_run ? '' : 'none';

    semester = [1];

    electiveCourse =
      !rotational_course ||
      rotational_course[0]?.['course'].filter((item) => item.model === 'elective');
  }

  return {
    active,
    year,
    is_interim,
    start,
    start1,
    end,
    end1,
    events,
    events1,
    level_one,
    level_two,
    rotational_course,
    rotational_course1,
    course1,
    course2,
    apply1,
    apply2,
    apply3,
    apply4,
    electiveCourse,
    electiveCourse1,
    is_rotation,
    is_rotation1,
    semester,
    new_start,
    new_end,
    new_courses,
  };
};

export default useDataFromStore;
