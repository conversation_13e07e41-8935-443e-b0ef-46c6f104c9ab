import React, { Component, useRef, Fragment } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { Multiselect } from 'multiselect-react-dropdown';
import { format } from 'date-fns';

import { capitalize, getRandomId, getInfrastructureValidation, Android12Switch } from '../utils';
import AlertModal from '../modal/AlertModal';
// import Pagination from '../../StudentGrouping/components/Pagenation';
import Input from '../../../Widgets/FormElements/Input/Input';
import Tooltip from '../../../_components/UI/Tooltip/Tooltip';
import '../../../Assets/css/grouping.css';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getEnvInstitutionId, isModuleEnabled } from 'utils';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

const multiSelectStyle = {
  searchBox: {
    border: 'none',
    borderBottom: '1px solid rgb(117 117 117)',
    borderRadius: '0px',
  },
};
const singleSelectStyle = {
  searchBox: multiSelectStyle.searchBox,
  inputField: {
    width: '0px',
  },
};

const usage = [
  'Academic',
  // 'Exam',
  // 'Administrative'
];

function formatTimeGroup(t, type) {
  const startTime = format(new Date(t.get('start_time')), 'hh:mm a');
  const endTime = format(new Date(t.get('end_time')), 'hh:mm a');
  const gender = t.get('gender') === 'both' ? 'Male & Female' : capitalize(t.get('gender'));
  const formatted = `${startTime} - ${endTime} (${gender})`;
  return type === 'string' ? formatted : t.set('time', formatted);
}

class InfrastructureListTable extends Component {
  constructor() {
    super();
    this.state = {
      room: '',
      page: 1,
      pageSize: 10,
      modalData: {
        show: false,
      },
      checkOnsiteOrRemote: false,
    };
  }

  componentDidMount() {
    this.fetchInfrastructureList();
  }

  fetchInfrastructureList() {
    const { page, pageSize } = this.state;
    this.props.getInfrastructureList(page, pageSize);
  }

  getInfrastructures() {
    const { infrastructures } = this.props;
    return infrastructures.get('data', List());
  }

  getBuildings() {
    const { buildingsAndHospitals } = this.props;
    return buildingsAndHospitals
      .map((b) =>
        Map({
          _id: b.get('_id'),
          name: `${b.get('name', '')}, ${b.get('location', '')}`,
          floors: b.get('floors'),
          zones: b.get('zones'),
        })
      )
      .toJS();
  }

  getBuildingName(buildingId) {
    const { buildingsAndHospitals } = this.props;
    const selectedBuilding = buildingsAndHospitals.find((b) => b.get('_id') === buildingId);
    return selectedBuilding
      ? `${selectedBuilding.get('name', '')}, ${selectedBuilding.get('location', '')}`
      : '';
  }

  getTimings() {
    const { timings } = this.props;
    return timings
      .filter((t) => t.get('type') === 'onsite')
      .map(formatTimeGroup)
      .toJS();
  }

  addInfrastructure() {
    const _id = getRandomId();
    const { addedInfrastructures } = this.props;
    this.props.setData(
      Map({
        addedInfrastructures: addedInfrastructures.set(
          _id,
          Map({
            _id,
            _building_id: '',
            building_name: '',
            floor_no: '',
            zone: List(),
            room_no: '',
            name: '',
            usage: '',
            delivery_type: List(),
            timing: List(),
            program: List(),
            department: List(),
            subject: List(),
            capacity: '',
            reserved: '',
            radius: '',
            latitude: '',
            longitude: '',
            outsideCampus: true,
            selectedBuilding: Map(),
            selectedPrograms: List(),
          })
        ),
      })
    );
  }

  getAddedInfrastructures() {
    const { addedInfrastructures } = this.props;
    return addedInfrastructures.entrySeq();
  }

  handleChange(event, name, row, operation) {
    const value = name === 'outsideCampus' ? event.target.checked : event.target.value;
    const id = row.get('_id');
    if (['capacity', 'reserved'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    const { addedInfrastructures, editedInfrastructures } = this.props;
    this.props.setData(
      Map({
        ...(operation === 'create' && {
          addedInfrastructures: addedInfrastructures.setIn([id, name], value),
        }),
        ...(operation === 'update' && {
          editedInfrastructures: editedInfrastructures.setIn([id, name], value),
        }),
      })
    );
  }

  handleSelect(selectedList, selectedItem, row, name, operation, ref) {
    var resF = [];
    var arr = [];
    var dtArr = [];
    var sArr = [];
    const radius = this.toFindLocation('radius', selectedItem);
    const latitude = this.toFindLocation('latitude', selectedItem);
    const longitude = this.toFindLocation('longitude', selectedItem);
    const { addedInfrastructures, editedInfrastructures } = this.props;
    let finalOutcome = (selArray, search, mainArr) => {
      resF = [];
      selArray.map((rKey) => {
        let temp = false;
        mainArr.map((k) => {
          if (k === rKey[search]) {
            temp = true;
          }
          return '';
        });
        if (temp) resF.push(rKey);
        return '';
      });
      return resF;
    };
    const id = row.get('_id');
    if (name === 'building') {
      if (selectedItem === '') selectedItem = { _id: '', name: '', floors: [], zones: [] };
      else selectedItem = this.getBuildings().find((b) => b._id === selectedItem);
      ref.zoneRef.current.resetSelectedValues();
    } else if (name === 'program') {
      /* ref.departmentRef.current.resetSelectedValues();
      ref.subjectRef.current.resetSelectedValues();
      ref.deliveryTypeRef.current.resetSelectedValues(); */
      if (selectedItem.name === 'Select All') {
        if (selectedList.length === ref.programRef.current.props.options.length) {
          selectedList = [];
        } else {
          selectedList = ref.programRef.current.props.options.filter(
            (key) => key.name !== 'Select All'
          );
        }
      }
      selectedList.map((sKey) => {
        sKey.department_data.map((dKey) => {
          arr.push(dKey.department_name);
          dKey.subject.map((suKey) => sArr.push(suKey.subject_name));
          return '';
        });
        sKey.delivery_types.map((dtKey) => dtArr.push(dtKey.delivery_symbol));
        return '';
      });

      arr = finalOutcome(ref.departmentRef.current.props.selectedValues, 'department_name', arr);

      sArr = finalOutcome(ref.subjectRef.current.props.selectedValues, 'subject_name', sArr);

      dtArr = finalOutcome(
        ref.deliveryTypeRef.current.props.selectedValues,
        'delivery_symbol',
        dtArr
      );

      //ref.departmentRef.current.props.selectedValues = resF;
    } else if (name === 'department') {
      if (selectedItem.department_name === 'Select All') {
        //ref.subjectRef.current.resetSelectedValues();
        if (selectedList.length === ref.departmentRef.current.props.options.length) {
          selectedList = [];
        } else {
          selectedList = ref.departmentRef.current.props.options.filter(
            (key) => key.department_name !== 'Select All'
          );
        }
      }
      selectedList.map((sKey) => sKey.subject.map((suKey) => sArr.push(suKey.subject_name)));
      sArr = finalOutcome(ref.subjectRef.current.props.selectedValues, 'subject_name', sArr);
    } else if (name === 'subject') {
      if (selectedItem.subject_name === 'Select All') {
        if (selectedList.length === ref.current.props.options.length) {
          selectedList = [];
        } else {
          selectedList = ref.current.props.options.filter(
            (key) => key.subject_name !== 'Select All'
          );
        }
      }
    } else if (name === 'delivery_type') {
      if (selectedItem.delivery_symbol === 'Select All') {
        if (selectedList.length === ref.current.props.options.length) {
          selectedList = [];
        } else {
          selectedList = ref.current.props.options.filter(
            (key) => key.delivery_symbol !== 'Select All'
          );
        }
      }
    }

    const data = Map({
      ...(name === 'building' && {
        building_name: selectedItem.name,
        _building_id: selectedItem._id,
        selectedBuilding: fromJS(selectedItem),
        floor_no: '',
        zone: List(),
        radius: radius,
        latitude: latitude,
        longitude: longitude,
        ...(radius !== '' && latitude !== '' && longitude !== ''
          ? { outsideCampus: true }
          : { outsideCampus: false }),
      }),

      ...(name === 'floor' && { floor_no: selectedItem }),
      ...(name === 'zone' && { zone: fromJS(selectedList) }),
      ...(name === 'usage' && { usage: selectedItem }),
      ...(name === 'delivery_type' && { delivery_type: fromJS(selectedList) }),
      ...(name === 'timing' && { timing: fromJS(selectedList) }),
      ...(name === 'program' && {
        program: fromJS(selectedList),
        selectedPrograms: fromJS(selectedList),
        department: fromJS(arr),
        subject: fromJS(sArr),
        delivery_type: fromJS(dtArr),
      }),
      ...(name === 'department' && { department: fromJS(selectedList), subject: fromJS(sArr) }),
      ...(name === 'subject' && { subject: fromJS(selectedList) }),
    });
    this.props.setData(
      Map({
        ...(operation === 'create' && {
          addedInfrastructures: addedInfrastructures.mergeIn([id], data),
        }),
        ...(operation === 'update' && {
          editedInfrastructures: editedInfrastructures.mergeIn([id], data),
        }),
      })
    );
  }

  handleInfraCancelClick(row, operation) {
    const id = row.get('_id');
    const { addedInfrastructures, editedInfrastructures } = this.props;
    this.props.setData(
      Map({
        ...(operation === 'create' && { addedInfrastructures: addedInfrastructures.delete(id) }),
        ...(operation === 'update' && { editedInfrastructures: editedInfrastructures.delete(id) }),
      })
    );
  }

  handleInfraEditClick(row) {
    const { editedInfrastructures } = this.props;
    this.props.setData(
      Map({
        editedInfrastructures: editedInfrastructures.set(
          row.get('_id'),
          this.transformInfraToEdit(row)
        ),
      })
    );
  }

  transformInfraToEdit(data) {
    const { programDeptSubjects } = this.props;
    const selectedBuilding = fromJS(this.getBuildings()).find(
      (b) => b.get('_id') === data.get('_building_id')
    );
    const timing = data.get('timing', List());
    const selectedPrograms = programDeptSubjects.filter((p) =>
      data
        .get('program', List())
        .find((currentPrograms) => currentPrograms.get('_program_id') === p.get('_id'))
    );

    const delivery_type = selectedPrograms
      .map((p) => p.get('delivery_types'))
      .reduce((acc, d) => acc.concat(d), List())
      .filter((d) =>
        data
          .get('delivery_type', List())
          .find((del) => del.get('_delivery_type_id') === d.get('_delivery_type_id'))
      );

    const department = selectedPrograms
      .map((p) => p.get('department_data'))
      .reduce((acc, d) => acc.concat(d), List())
      .filter((d) =>
        data.get('department', List()).find((dept) => dept.get('_department_id') === d.get('_id'))
      );

    const subject = department
      .reduce((acc, s) => acc.concat(s.get('subject')), List())
      .filter((s) =>
        data.get('subject', List()).find((sub) => sub.get('_subject_id') === s.get('_id'))
      );

    const transformedData = data.merge({
      selectedBuilding: selectedBuilding || Map(),
      timing,
      program: selectedPrograms,
      selectedPrograms,
      department,
      subject,
      delivery_type,
    });
    return transformedData;
  }

  handleSaveClick(id, operation, isConfirmed = false) {
    const { page, pageSize } = this.state;
    const { addedInfrastructures, editedInfrastructures } = this.props;
    const data =
      operation === 'create'
        ? addedInfrastructures.get(id, Map())
        : editedInfrastructures.get(id, Map());
    const requestBody = {
      _id: id,
      ...(operation !== 'delete' && {
        _building_id: data.get('_building_id'),
        building_name: data.get('building_name'),
        floor_no: data.get('floor_no'),
        zone: data.get('zone', List()).toJS(),
        room_no: data.get('room_no'),
        name: data.get('name'),
        usage: data.get('usage'),
        delivery_type: data
          .get('delivery_type', List())
          .map((del) =>
            Map({
              _delivery_type_id: del.get('_delivery_type_id'),
              delivery_symbol: del.get('delivery_symbol'),
            })
          )
          .toJS(),
        timing: data
          .get('timing', List())
          .map((time) =>
            Map({
              _time_id: time.has('_time_id') ? time.get('_time_id') : time.get('_id'),
            })
          )
          .toJS(),
        program: data
          .get('program', List())
          .map((program) =>
            Map({
              _program_id: program.has('_program_id')
                ? program.get('_program_id')
                : program.get('_id'),
              name: program.get('name'),
            })
          )
          .toJS(),
        department: data
          .get('department', List())
          .map((dept) =>
            Map({ _department_id: dept.get('_id'), name: dept.get('department_name') })
          )
          .toJS(),
        subject: data
          .get('subject', List())
          .map((subject) =>
            Map({ _subject_id: subject.get('_id'), name: subject.get('subject_name') })
          )
          .toJS(),
        capacity: +data.get('capacity', ''),
        reserved: +data.get('reserved', ''),
        radius: data.get('radius', ''),
        latitude: data.get('latitude', ''),
        longitude: data.get('longitude', ''),
        outsideCampus: data.get('outsideCampus', false),
        _institution_id: getEnvInstitutionId(),
      }),
      operation,
      page,
      pageSize,
    };
    if (operation === 'delete') {
      if (!isConfirmed) {
        this.setModalData({
          show: true,
          title: t('infra_management.modals.confirm_delete'),
          description: t('infra_management.modals.infrastructure.description'),
          variant: 'confirm',
          cancelButtonLabel: t('cancel'),
          confirmButtonLabel: t('yes'),
          data: { id, operation },
        });
        return;
      }
    } else {
      const { invalidFields, invalidRange } = getInfrastructureValidation(requestBody);
      let message = '';
      if (invalidFields.length) {
        message =
          `${invalidFields.join(', ')} ${invalidFields.length === 1 ? t('is') : t('are')}` +
          ' ' +
          t('frameworks.required');
      }
      if (invalidRange.length) {
        message = `${message} ${invalidRange.join(' ')}`;
      }
      if (invalidFields.length || invalidRange.length) {
        this.setModalData({
          show: true,
          title: t('infra_management.alerts.validation_error.title'),
          description: message,
          variant: 'alert',
          cancelButtonLabel: t('ok'),
        });
        return;
      }
    }
    document.getElementById('infrastructure-list-container').scrollLeft = 0;
    this.props.updateInfrastructure(requestBody);
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ id, operation }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.handleSaveClick(id, operation, true)
    );
  }

  getName(row, key1, key2) {
    if (key1 === 'timing') {
      return row
        .get(key1, List())
        .map((t) => formatTimeGroup(t, 'string'))
        .join(', ');
    }
    return row
      .get(key1, List())
      .map((t) => t.get(key2))
      .join(', ');
  }

  onPageSizeChange(pageSize) {
    this.setState({ page: 1, pageSize }, this.fetchInfrastructureList);
  }

  handleNextPageClick() {
    const { infrastructures } = this.props;
    const page = infrastructures.get('totalPages');
    if (this.state.page === page || page === 0) return;
    this.setState((state) => {
      return { page: state.page + 1 };
    }, this.fetchInfrastructureList);
  }

  handlePrevPageClick() {
    if (this.state.page === 1) return;
    this.setState((state) => {
      return { page: state.page - 1 };
    }, this.fetchInfrastructureList);
  }

  handleFirstPageClick() {
    if (this.state.page === 1) return;
    this.setState({ page: 1 }, this.fetchInfrastructureList);
  }

  handleLastPageClick() {
    const { infrastructures } = this.props;
    const page = infrastructures.get('totalPages');
    if (page === this.state.page || page === 0) return;
    this.setState({ page }, this.fetchInfrastructureList);
  }

  toFindLocation(requiredLocation, buildingId) {
    const { buildingsAndHospitals } = this.props;
    const selectedBuilding = buildingsAndHospitals.find((b) => b.get('_id') === buildingId);
    if (selectedBuilding !== undefined) {
      return selectedBuilding.get(requiredLocation, '');
    }
    return '';
  }

  render() {
    const { modalData } = this.state;
    const {
      //infrastructures,
      programDeptSubjects,
      addedInfrastructures,
      editedInfrastructures,
    } = this.props;

    return (
      <div>
        <div className="table-border">
          <div id="infrastructure-list-container" className="infra_table mb-2">
            <table className="table">
              <thead>
                <tr>
                  <th className="border_color_blue">
                    <div className="aw-200">
                      <i className="fa fa-align-left" aria-hidden="true"></i>
                      <b className="pl-2">
                        <Trans i18nKey={'infra_management.table_headers._building_id'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-100">
                      <b>
                        <Trans i18nKey={'infra_management.table_headers.floor'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-100">
                      <b>
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.zone'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-100">
                      <i className="fa fa-align-center" aria-hidden="true"></i>
                      <b className="pl-2">
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.room_no'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <b>
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.name'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <i className="fa fa-align-center" aria-hidden="true"></i>
                      <b className="pl-2">
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.usage'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-200">
                      <b>
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.timing'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <i className="fa fa-align-center" aria-hidden="true"></i>
                      <b className="pl-2">
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.program'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <i className="fa fa-align-center" aria-hidden="true"></i>
                      <b className="pl-2">
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.department'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <b>
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.subject'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <i className="fa fa-align-center" aria-hidden="true"></i>
                      <b className="pl-2">
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.delivery_type'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <b>
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.capacity'}></Trans>
                      </b>
                    </div>
                  </th>
                  <th className="border_color_blue">
                    <div className="aw-150">
                      <b>
                        {' '}
                        <Trans i18nKey={'infra_management.table_headers.reserved'}></Trans>#
                      </b>
                    </div>
                  </th>
                  {isModuleEnabled('OUTSIDE_CAMPUS') && (
                    <Fragment>
                      <th className="border_color_blue">
                        <div className="aw-150">
                          <b>Radius</b>
                        </div>
                      </th>
                      <th className="border_color_blue">
                        <div className="aw-150">
                          <b>Latitude</b>
                        </div>
                      </th>
                      <th className="border_color_blue">
                        <div className="aw-150">
                          <b>Longitude</b>
                        </div>
                      </th>
                      <th className="border_color_blue">
                        <div className="aw-150">
                          <div className="d-flex aline-items-center">
                            <div className="d-flex align-items-end"></div>
                            <div>
                              <Stack direction="row" spacing={1} alignItems="center">
                                <Typography>OutSideCampus</Typography>
                              </Stack>
                            </div>
                          </div>
                        </div>
                      </th>
                    </Fragment>
                  )}
                  <th className="border_color_blue">
                    <div className="aw-100"></div>
                  </th>
                </tr>
              </thead>
              <tbody className="infra_table_height">
                {this.getInfrastructures().isEmpty() && this.getAddedInfrastructures().isEmpty() && (
                  <tr>
                    <td colSpan="14">
                      <Trans i18nKey={'infra_management.no_data'}></Trans>
                    </td>
                  </tr>
                )}
                {this.getAddedInfrastructures().map((row) => (
                  <EditableInfrastructure
                    key={row[0]}
                    row={row[1]}
                    buildings={this.getBuildings()}
                    programDeptSubjects={programDeptSubjects.toJS()}
                    timings={this.getTimings()}
                    handleSelect={this.handleSelect.bind(this)}
                    handleChange={this.handleChange.bind(this)}
                    handleCancelClick={this.handleInfraCancelClick.bind(this)}
                    handleSaveClick={this.handleSaveClick.bind(this)}
                    data={addedInfrastructures}
                    operation="create"
                  />
                ))}
                {this.getInfrastructures().map((row) => {
                  const isOutsideCampus = row.get('outsideCampus', false);
                  return !editedInfrastructures.has(row.get('_id')) ? (
                    <tr key={row.get('_id')} className="tr-change">
                      <td>
                        <div className="aw-200">
                          <p className="pl-4 float-left">
                            {this.getBuildingName(row.get('_building_id'))}
                          </p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-100">
                          <p className="float-left">{row.get('floor_no', '') || 'NA'}</p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-100">
                          <p className="float-left">
                            {row.get('zone', List()).toJS().join(', ') || 'NA'}
                          </p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-100">
                          <p className="pl-4 float-left">{row.get('room_no', '')}</p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="float-left">{row.get('name', '')}</p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="pl-4 float-left">{row.get('usage', '')}</p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-200">
                          <p className="float-left">{this.getName(row, 'timing', 'time')}</p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="pl-4 float-left">{this.getName(row, 'program', 'name')}</p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="pl-4 float-left">
                            {this.getName(row, 'department', 'name') || 'NA'}
                          </p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="float-left">
                            {this.getName(row, 'subject', 'name') || 'NA'}
                          </p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="pl-4 float-left">
                            {this.getName(row, 'delivery_type', 'delivery_symbol') || 'NA'}
                          </p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="float-left">{row.get('capacity', '')}</p>
                        </div>
                      </td>
                      <td>
                        <div className="aw-150">
                          <p className="float-left">{row.get('reserved', '') || '0'}</p>
                        </div>
                      </td>
                      {isModuleEnabled('OUTSIDE_CAMPUS') && (
                        <Fragment>
                          <td>
                            <div className="aw-150">
                              {isOutsideCampus && (
                                <p className="float-left">{row.get('radius', '') || '0'}</p>
                              )}
                            </div>
                          </td>
                          <td>
                            <div className="aw-150">
                              {isOutsideCampus && (
                                <p className="float-left">{row.get('latitude', '') || '0'}</p>
                              )}
                            </div>
                          </td>
                          <td>
                            <div className="aw-150">
                              {isOutsideCampus && (
                                <p className="float-left">{row.get('longitude', '') || '0'}</p>
                              )}
                            </div>
                          </td>
                          <td>
                            <div className="aw-100">
                              <Android12Switch checked={isOutsideCampus} disabled />
                            </div>
                          </td>
                        </Fragment>
                      )}
                      <td>
                        <div className="aw-100 pt-2">
                          {CheckPermission(
                            'pages',
                            'Infrastructure Management',
                            'Onsite',
                            'Edit'
                          ) && (
                            <b className="float-left pr-2 f-16">
                              <Tooltip title={t('edit')}>
                                <i
                                  className="fa fa-pencil"
                                  aria-hidden="true"
                                  onClick={this.handleInfraEditClick.bind(this, row)}
                                ></i>
                              </Tooltip>
                            </b>
                          )}
                          {CheckPermission(
                            'pages',
                            'Infrastructure Management',
                            'Onsite',
                            'Delete'
                          ) && (
                            <b className="float-left pl-2 f-16">
                              <Tooltip title={t('delete')}>
                                <i
                                  className="fa fa-trash"
                                  aria-hidden="true"
                                  onClick={this.handleSaveClick.bind(
                                    this,
                                    row.get('_id'),
                                    'delete',
                                    false
                                  )}
                                ></i>
                              </Tooltip>
                            </b>
                          )}
                        </div>
                      </td>
                    </tr>
                  ) : (
                    <EditableInfrastructure
                      key={row.get('_id')}
                      row={row}
                      buildings={this.getBuildings()}
                      programDeptSubjects={programDeptSubjects.toJS()}
                      timings={this.getTimings()}
                      handleSelect={this.handleSelect.bind(this)}
                      handleChange={this.handleChange.bind(this)}
                      handleCancelClick={this.handleInfraCancelClick.bind(this)}
                      handleSaveClick={this.handleSaveClick.bind(this)}
                      data={editedInfrastructures}
                      operation="update"
                    />
                  );
                })}
              </tbody>
            </table>
          </div>
          <AlertModal
            show={modalData.show}
            title={modalData.title || ''}
            description={modalData.description || ''}
            variant={modalData.variant || 'confirm'}
            confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
            cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
            onClose={this.onModalClose.bind(this)}
            onConfirm={this.onConfirm.bind(this)}
            data={modalData.data}
          />
        </div>
        {/* <Pagination
          data={infrastructures.get('totalPages', '')}
          currentPage={infrastructures.get('currentPage', '')}
          pagination={this.onPageSizeChange.bind(this)}
          onNextClick={this.handleNextPageClick.bind(this)}
          onBackClick={this.handlePrevPageClick.bind(this)}
          onFullForwardClick={this.handleLastPageClick.bind(this)}
          onFullLastClick={this.handleFirstPageClick.bind(this)}
        /> */}
      </div>
    );
  }
}

function EditableInfrastructure(props) {
  const {
    buildings,
    timings,
    programDeptSubjects,
    row,
    data,
    handleChange,
    handleSelect,
    operation,
    handleCancelClick,
    handleSaveClick,
  } = props;
  const zoneRef = useRef(null);
  const departmentRef = useRef(null);
  const subjectRef = useRef(null);
  const programRef = useRef(null);
  const deliveryTypeRef = useRef(null);
  const id = row.get('_id');

  function getDepartments() {
    let departRes = programDeptSubjects
      .filter((p) =>
        data
          .getIn([id, 'selectedPrograms'], List())
          .find((selectedProgram) => selectedProgram.get('_id') === p._id)
      )
      .map((p) => p.department_data)
      .reduce((acc, d) => acc.concat(d), []);
    if (departRes.length)
      departRes.unshift({
        department_name: 'Select All',
        _id: '1',
        subject: [],
      });
    return departRes;
  }

  function getSubjects() {
    let subRes = getDepartments()
      .filter((d) =>
        data.getIn([id, 'department'], List()).find((dept) => dept.get('_id') === d._id)
      )
      .reduce((acc, s) => acc.concat(s.subject), []);
    if (subRes.length) subRes.unshift({ subject_name: 'Select All', _id: '1' });

    return subRes;
  }

  function getDeliveryTypes() {
    let delRes = programDeptSubjects
      .filter((p) =>
        data
          .getIn([id, 'selectedPrograms'], List())
          .find((selectedProgram) => selectedProgram.get('_id') === p._id)
      )
      .map((p) => p.delivery_types)
      .reduce((acc, d) => acc.concat(d), []);
    if (delRes.length)
      delRes.unshift({
        delivery_symbol: 'Select All',
        _delivery_type_id: '1',
      });
    return delRes;
  }
  const outsideCampusValue = data.getIn([id, 'outsideCampus'], '');

  return (
    <tr className="tr-change">
      <td>
        <div className="aw-200">
          <div className="mt--18">
            <Input
              elementType="floatingselect"
              elementConfig={{
                options: [
                  { name: t('infra_management.add_infra.select_building'), value: '' },
                  ...buildings.map((b) => {
                    return { name: b.name, value: b._id };
                  }),
                ],
              }}
              value={data.getIn([id, 'selectedBuilding', '_id'], '')}
              changed={(e) =>
                handleSelect([], e.target.value, row, 'building', operation, { zoneRef })
              }
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-100">
          <div className="mt--18">
            <Input
              elementType="floatingselect"
              elementConfig={{
                options: [
                  { name: t('infra_management.add_infra.na'), value: '' },
                  ...data
                    .getIn([id, 'selectedBuilding', 'floors'], List())
                    .map((f) => Map({ name: f.get('floor_name'), value: f.get('floor_name') }))
                    .toJS(),
                ],
              }}
              value={data.getIn([id, 'floor_no'])}
              changed={(e) => handleSelect([], e.target.value, row, 'floor', operation)}
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-100">
          <div>
            <Multiselect
              ref={zoneRef}
              id={`zone-${id}`}
              options={data
                .getIn([id, 'selectedBuilding', 'zones'], List())
                .map((z) => z.get('zone'))
                .toJS()}
              selectedValues={data
                .getIn([id, 'selectedBuilding', 'zones'], List())
                .filter((z) => data.getIn([id, 'zone'], List()).includes(z.get('zone')))
                .map((z) => z.get('zone'))
                .toJS()}
              style={singleSelectStyle}
              showCheckbox={true}
              isObject={false}
              avoidHighlightFirstOption
              closeOnSelect={false}
              emptyRecordMsg="NA"
              onSelect={(selectedList, selectedItem) =>
                handleSelect(selectedList, selectedItem, row, 'zone', operation)
              }
              onRemove={(selectedList, removedItem) =>
                handleSelect(selectedList, removedItem, row, 'zone', operation)
              }
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-100 mt--15">
          <div className="mt--15">
            <Input
              name="room_no"
              elementType="floatinginput"
              elementConfig={{
                type: 'text',
              }}
              value={data.getIn([id, 'room_no'], '')}
              placeholder={t('type')}
              changed={(e) => handleChange(e, 'room_no', row, operation)}
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150 mt--15">
          <div className="mt--15">
            <Input
              name="name"
              elementType="floatinginput"
              elementConfig={{
                type: 'text',
              }}
              value={data.getIn([id, 'name'], '')}
              placeholder={t('type')}
              changed={(e) => handleChange(e, 'name', row, operation)}
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150">
          <div className="mt--18">
            <Input
              elementType="floatingselect"
              elementConfig={{
                options: [
                  { name: t('infra_management.add_infra.select_usage'), value: '' },
                  ...usage.map((u) => {
                    return { name: u, value: u };
                  }),
                ],
              }}
              value={data.getIn([id, 'usage'])}
              changed={(e) => handleSelect([], e.target.value, row, 'usage', operation)}
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-200">
          <div>
            <Multiselect
              id={`timing-${id}`}
              options={timings}
              selectedValues={data.getIn([id, 'timing'], List()).map(formatTimeGroup).toJS()}
              displayValue="time"
              showCheckbox
              style={multiSelectStyle}
              placeholder={t('master_graph.select')}
              avoidHighlightFirstOption
              closeOnSelect={false}
              onSelect={(selectedList, selectedItem) =>
                handleSelect(selectedList, selectedItem, row, 'timing', operation)
              }
              onRemove={(selectedList, removedItem) =>
                handleSelect(selectedList, removedItem, row, 'timing', operation)
              }
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150">
          <div>
            <Multiselect
              id={`programs-${id}`}
              ref={programRef}
              options={[
                {
                  name: 'Select All',
                  _program_id: '1',
                  department_data: [],
                  delivery_types: [],
                },
                ...programDeptSubjects,
              ]}
              selectedValues={data.getIn([id, 'program'], List()).toJS()}
              displayValue="name"
              showCheckbox
              avoidHighlightFirstOption
              closeOnSelect={false}
              style={multiSelectStyle}
              placeholder={t('master_graph.select')}
              onSelect={(selectedList, selectedItem) =>
                handleSelect(selectedList, selectedItem, row, 'program', operation, {
                  programRef,
                  departmentRef,
                  subjectRef,
                  deliveryTypeRef,
                })
              }
              onRemove={(selectedList, removedItem) =>
                handleSelect(selectedList, removedItem, row, 'program', operation, {
                  programRef,
                  departmentRef,
                  subjectRef,
                  deliveryTypeRef,
                })
              }
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150">
          <div>
            <Multiselect
              ref={departmentRef}
              id={`departments-${id}`}
              options={getDepartments()}
              selectedValues={data.getIn([id, 'department'], List()).toJS()}
              displayValue="department_name"
              showCheckbox
              avoidHighlightFirstOption
              closeOnSelect={false}
              style={multiSelectStyle}
              placeholder={t('master_graph.select')}
              onSelect={(selectedList, selectedItem) =>
                handleSelect(selectedList, selectedItem, row, 'department', operation, {
                  departmentRef,
                  subjectRef,
                })
              }
              onRemove={(selectedList, removedItem) =>
                handleSelect(selectedList, removedItem, row, 'department', operation, {
                  departmentRef,
                  subjectRef,
                })
              }
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150">
          <div>
            <Multiselect
              ref={subjectRef}
              id={`subjects-${id}`}
              options={getSubjects()}
              selectedValues={data.getIn([id, 'subject'], List()).toJS()}
              displayValue="subject_name"
              showCheckbox
              avoidHighlightFirstOption
              closeOnSelect={false}
              style={multiSelectStyle}
              placeholder={t('master_graph.select')}
              onSelect={(selectedList, selectedItem) =>
                handleSelect(selectedList, selectedItem, row, 'subject', operation, subjectRef)
              }
              onRemove={(selectedList, removedItem) =>
                handleSelect(selectedList, removedItem, row, 'subject', operation, subjectRef)
              }
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150">
          <div>
            <Multiselect
              ref={deliveryTypeRef}
              id={`deliveryTypes-${id}`}
              options={getDeliveryTypes()}
              selectedValues={data.getIn([id, 'delivery_type'], List()).toJS()}
              displayValue="delivery_symbol"
              showCheckbox
              style={multiSelectStyle}
              avoidHighlightFirstOption
              closeOnSelect={false}
              placeholder={t('master_graph.select')}
              onSelect={(selectedList, selectedItem) =>
                handleSelect(
                  selectedList,
                  selectedItem,
                  row,
                  'delivery_type',
                  operation,
                  deliveryTypeRef
                )
              }
              onRemove={(selectedList, removedItem) =>
                handleSelect(
                  selectedList,
                  removedItem,
                  row,
                  'delivery_type',
                  operation,
                  deliveryTypeRef
                )
              }
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150">
          <div className=" mt--15">
            <Input
              name="capacity"
              elementType="floatinginput"
              elementConfig={{
                type: 'text',
              }}
              value={data.getIn([id, 'capacity'], '')}
              placeholder={t('type')}
              changed={(e) => handleChange(e, 'capacity', row, operation)}
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-150">
          <div className=" mt--15">
            <Input
              name="reserved"
              elementType="floatinginput"
              elementConfig={{
                type: 'text',
              }}
              value={data.getIn([id, 'reserved'], '')}
              placeholder={t('type')}
              changed={(e) => handleChange(e, 'reserved', row, operation)}
            />
          </div>
        </div>
      </td>
      {isModuleEnabled('OUTSIDE_CAMPUS') && (
        <Fragment>
          <td>
            <div className="aw-150">
              {outsideCampusValue && (
                <div className="mt-3">
                  <p>{data.getIn([id, 'radius'], '')}</p>
                </div>
              )}
            </div>
          </td>
          <td>
            <div className="aw-150">
              {outsideCampusValue && (
                <div className=" mt-3">
                  <p>{data.getIn([id, 'latitude'], '')}</p>
                </div>
              )}
            </div>
          </td>
          <td>
            <div className="aw-150">
              {outsideCampusValue && (
                <div className=" mt-3">
                  <p>{data.getIn([id, 'longitude'], '')}</p>
                </div>
              )}
            </div>
          </td>
          <td>
            <div className="aw-100 pt-2">
              <Android12Switch
                disabled={
                  data.getIn([id, 'latitude'], '') === '' &&
                  data.getIn([id, 'longitude'], '') === '' &&
                  data.getIn([id, 'radius'], '') === ''
                }
                onChange={(e) => handleChange(e, 'outsideCampus', row, operation)}
                checked={
                  data.getIn([id, 'outsideCampus'], '') &&
                  data.getIn([id, 'selectedBuilding', '_id'], '') !== '' &&
                  data.getIn([id, 'latitude'], '') !== '' &&
                  data.getIn([id, 'longitude'], '') !== '' &&
                  data.getIn([id, 'radius'], '') !== ''
                }
              />
            </div>
          </td>
        </Fragment>
      )}

      <td>
        <div className="aw-100 pt-3">
          <b className="float-left pr-2 f-16 remove-hover">
            <Tooltip title={t('cancel')}>
              <i
                className="fa fa-times-circle"
                aria-hidden="true"
                onClick={() => handleCancelClick(row, operation)}
              ></i>
            </Tooltip>
          </b>
          <b className="float-left pl-2 f-16 remove-hover">
            <Tooltip title={t('save')}>
              <i
                className="fa fa-floppy-o"
                aria-hidden="true"
                onClick={() => handleSaveClick(id, operation, false)}
              ></i>
            </Tooltip>
          </b>
        </div>
      </td>
    </tr>
  );
}

InfrastructureListTable.propTypes = {
  getInfrastructureList: PropTypes.func,
  updateInfrastructure: PropTypes.func,
  infrastructures: PropTypes.instanceOf(Map),
  programDeptSubjects: PropTypes.instanceOf(List),
  timings: PropTypes.instanceOf(List),
  buildingsAndHospitals: PropTypes.instanceOf(List),
  addedInfrastructures: PropTypes.instanceOf(Map),
  editedInfrastructures: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
};

EditableInfrastructure.propTypes = {
  buildings: PropTypes.array,
  timings: PropTypes.array,
  programDeptSubjects: PropTypes.array,
  row: PropTypes.instanceOf(Map),
  data: PropTypes.instanceOf(Map),
  operation: PropTypes.string,
  handleChange: PropTypes.func,
  handleSelect: PropTypes.func,
  handleCancelClick: PropTypes.func,
  handleSaveClick: PropTypes.func,
};

export default InfrastructureListTable;
