const path = require('path');
const fs = require('fs');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const TerserPlugin = require('terser-webpack-plugin');
const dotenv = require('dotenv');
const ESLintPlugin = require('eslint-webpack-plugin');
// Function to get directories in a given path
function getDirectories(srcpath) {
  return fs
    .readdirSync(srcpath)
    .filter((file) => fs.statSync(path.join(srcpath, file)).isDirectory());
}

// Function to generate aliases based on directories in 'src'
function generateAliases() {
  const srcPath = path.resolve(__dirname, 'src');
  const aliases = {};

  // Get all directories in 'src' except 'node_modules'
  const directories = getDirectories(srcPath).filter((dir) => dir !== 'node_modules');

  // Generate aliases for each directory
  directories.forEach((dir) => {
    aliases[dir] = path.resolve(srcPath, dir);
  });
  aliases['utils'] = path.resolve(srcPath, 'utils');
  aliases['LocalStorageService'] = path.resolve(srcPath, 'LocalStorageService');
  aliases['constants'] = path.resolve(srcPath, 'constants');
  aliases['authConfig'] = path.resolve(srcPath, 'authConfig');
  aliases['config'] = path.resolve(srcPath, 'config');

  return aliases;
}

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  // console.log('env', env, argv, isProduction);
  const envFile = env && env.envFile ? env.envFile : '.env';
  const envPath = path.resolve(__dirname, envFile);
  if (fs.existsSync(envPath)) {
    dotenv.config({ path: envPath });
  } else {
    console.warn(`.env file not found: ${envPath}`);
  }
  const publicPath = `${process.env.REACT_APP_BASE_PATH || ''}/` || '';
  console.log({
    publicPath,
    path: `${publicPath}index.html`,
  });
  // const envVars = require('dotenv').config({ path: envPath }).parsed || {};
  return {
    mode: isProduction ? 'production' : 'development',
    entry: './src/index.js',
    devServer: {
      static: './build',
      port: 3000,
      historyApiFallback: {
        index: `${publicPath}index.html`, // Serve the index.html file for all routes
      },
      allowedHosts: 'all',
      // open: "chrome",
    },
    output: {
      path: path.resolve(__dirname, 'build'),
      filename: '[name].[contenthash].js',
      publicPath: publicPath,
      clean: true, // Clean the output directory before each build
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                presets: [['@babel/preset-env'], ['@babel/preset-react', { runtime: 'automatic' }]],
              },
            },
          ],
        },
        {
          test: /\.(png|svg|jpe?g|gif)$/i,
          type: 'asset/resource',
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf)$/,
          use: {
            loader: 'file-loader',
            options: {
              name: 'fonts/[name].[ext]',
            },
          },
        },
        {
          test: /\.json$/,
          loader: 'json-loader',
          type: 'javascript/auto', // For webpack 4 and above
        },
      ],
    },
    resolve: {
      extensions: ['.js', '.jsx', '.json'],
      alias: generateAliases(), // Generate aliases dynamically
      fallback: {
        fs: false,
      },
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: './public/index.html',
        favicon: './public/favicon.png',
        publicPath: publicPath,
      }),
      new HtmlWebpackPlugin({
        template: './public/course-report/index.html',
        filename: 'course-report.html',
      }),
      new HtmlWebpackPlugin({
        template: './public/course-spec/course_spec.html',
        filename: 'course-spec.html',
      }),

      new CopyWebpackPlugin({
        patterns: [
          {
            from: path.join(__dirname, '/public'),
            to: path.join(__dirname, '/build'),
            globOptions: {
              // ignore: ['**/*.html'], // Ignore HTML files if needed
              ignore: ['**/index.html'],
            },
          },
        ],
      }),
      new webpack.ProvidePlugin({
        React: 'react',
      }),
      new webpack.ProgressPlugin(),
      new Dotenv({
        path: envPath, // Path to your env file
      }),
      new webpack.HotModuleReplacementPlugin(),
      new ESLintPlugin({
        extensions: ['js', 'jsx'], // Lint these file types
        overrideConfigFile: './.eslintrc.json', // Path to ESLint config
        emitWarning: true, // Show warnings instead of errors
      }),
    ],
    node: {
      __dirname: true,
      __filename: true,
      global: true,
    },
    devtool: isProduction ? false : 'eval-source-map',
    optimization: {
      minimize: isProduction,
      minimizer: [new TerserPlugin()],
      splitChunks: {
        chunks: 'all',
      },
      runtimeChunk: 'single', // Separate the runtime code into a single chunk
    },
    cache: {
      type: 'filesystem', // Use filesystem caching to improve build speed
    },
    performance: {
      hints: isProduction ? 'warning' : false, // Show performance hints only in production mode
    },
  };
};
