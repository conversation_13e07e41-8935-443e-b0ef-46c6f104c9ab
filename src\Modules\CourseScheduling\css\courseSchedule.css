.course_sort_icon {
  margin: 8px 7px 7px 6px;
  font-size: 18px !important;
  text-align: center;
  border: 1px solid #ced4da;
  border-radius: 4px;
  color: #4b9dea;
}

.border-top-1px {
  border-top: 1px solid rgba(0, 0, 0, 0.54);
}

.course_inner_list {
  border-bottom: 1px solid rgb(198 193 193 / 54%);
}

.course_hover:hover {
  border: 1px solid #a1cbf5;
  border-radius: 6px;
  background: #edf6ff;
}

.course_tab {
  list-style-type: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.course_tab_li {
  float: left;
}

.course_tab_li_ar {
  float: right;
  padding: 2px 25px 0px 25px;
}

.course_tab_li {
  padding: 2px 25px 0px 25px;
}

.course_tab_li span {
  display: block;
  color: #3e95ef;
  text-align: center;
  padding: 2px 3px 15px 3px;
  text-decoration: none;
  font-weight: 500;
}

.course_tab_li_ar span {
  display: block;
  color: #3e95ef;
  text-align: center;
  padding: 2px 15px 3px 3px;
  text-decoration: none;
  font-weight: 500;
}

.course_list:hover {
  border-bottom: 2px solid #3e95f0;
}

.active_course {
  border-bottom: 2px solid #3e95f0;
  color: #3d5170 !important;
}

.td_remove_border {
  border-top: none !important;
}

.expend {
  border: 1px solid #3e95f0;
  padding: 7px 10px 7px 10px;
  border-radius: 15px;
}

.course_setting_tab {
  border: 1px solid #4ea0eb;
  border-radius: 5px;
  font-size: 15px;
  color: #3e95ef;
  font-weight: 500;
  background-color: white;
}

.border-right-blue {
  border-right: 1px solid #4ea0eb;
}

.disabled_course {
  color: #95d3fa !important;
  cursor: not-allowed;
}

.setting_active {
  background-color: #edf6ff69;
}

.thead_border {
  border-top: 2px solid #dee2e6;
}

.exclamation_icon {
  border-radius: 15px;
  padding: 4px 8px 4px 8px;
  background: rgb(204 206 210);
  color: white;
  font-size: 11px;
}

.calendar {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(8, minmax(120px, 1fr));
  grid-template-rows: 50px;
  grid-auto-rows: 40px;
  overflow: auto;
  border: 1px solid #f4f4f6;
}

.calendar-container {
  margin: auto;
  overflow: hidden;
  border-radius: 10px;
  background: #fff;
  max-width: 1200px;
}

.calendar-header {
  padding: 20px 10px 20px 0px;
  /* border-bottom: 1px solid rgba(166, 168, 179, 0.12); */
}

.calendar-header h1 {
  margin-bottom: 3px;
  font-size: 18px;
}

.calendar-header p {
  margin: 5px 0 0 0;
  font-size: 13px;
  font-weight: 600;
  color: rgba(81, 86, 93, 0.4);
}

.calendar-header button {
  background: 0;
  border: 0;
  padding: 0;
  color: rgba(81, 86, 93, 0.7);
  cursor: pointer;
  outline: 0;
}

.day {
  border-bottom: 1px solid rgb(166 168 179 / 27%);
  border-right: 1px solid rgb(166 168 179 / 27%);
  text-align: center;
  padding: 1px 2px;
  letter-spacing: 1px;
  font-size: 12px;
  box-sizing: border-box;
  color: #98a0a6;
  position: relative;
  pointer-events: none;
  z-index: 1;
}

.day:nth-of-type(8n + 8) {
  border-right: 0;
}

.day:nth-of-type(n + 1):nth-of-type(-n + 8) {
  grid-row: 2;
}

.day:nth-of-type(n + 9):nth-of-type(-n + 16) {
  grid-row: 3;
}

.day:nth-of-type(n + 17):nth-of-type(-n + 24) {
  grid-row: 4;
}

.day:nth-of-type(n + 25):nth-of-type(-n + 32) {
  grid-row: 5;
}

.day:nth-of-type(n + 33):nth-of-type(-n + 40) {
  grid-row: 6;
}

.day:nth-of-type(n + 41):nth-of-type(-n + 48) {
  grid-row: 7;
}

.day:nth-of-type(8n + 1) {
  grid-column: 1/1;
  padding-top: 10px;
}

.day:nth-of-type(8n + 2) {
  grid-column: 2/2;
}

.day:nth-of-type(8n + 3) {
  grid-column: 3/3;
}

.day:nth-of-type(8n + 4) {
  grid-column: 4/4;
}

.day:nth-of-type(8n + 5) {
  grid-column: 5/5;
}

.day:nth-of-type(8n + 6) {
  grid-column: 6/6;
}

.day:nth-of-type(8n + 7) {
  grid-column: 7/7;
}

.day:nth-of-type(8n + 8) {
  grid-column: 8/8;
}

.day-name {
  font-size: 12px;
  text-transform: uppercase;
  color: #99a1a7;
  text-align: center;
  border-bottom: 1px solid rgba(166, 168, 179, 0.12);
  line-height: 50px;
  font-weight: 500;
  border-right: 1px solid rgb(166 168 179 / 27%);
}

.day--disabled {
  color: rgba(152, 160, 166, 0.6);
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f9f9fa' fill-opacity='1' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/svg%3E");
  cursor: not-allowed;
}

.task {
  border-left-width: 3px;
  padding: 8px 12px;
  margin: 10px;
  border-left-style: solid;
  font-size: 14px;
  position: relative;
}

.task--warning {
  border-left-color: #fdb44d;
  grid-column: 4 / span 3;
  grid-row: 3;
  background: #fef0db;
  align-self: center;
  color: #fc9b10;
  margin-top: -5px;
}

.task--danger {
  border-left-color: #fa607e;
  grid-column: 2 / span 3;
  grid-row: 3;
  margin-top: 15px;
  background: rgba(253, 197, 208, 0.7);
  align-self: end;
  color: #f8254e;
}

.task--info {
  border-left-color: #4786ff;
  grid-column: 6 / span 2;
  grid-row: 5;
  margin-top: 15px;
  background: rgba(218, 231, 255, 0.7);
  align-self: end;
  color: #0a5eff;
}

.task--primary {
  background: #4786ff;
  border: 0;
  border-radius: 4px;
  grid-column: 3 / span 3;
  grid-row: 4;
  align-self: end;
  color: #fff;
  box-shadow: 0 10px 14px rgba(71, 134, 255, 0.4);
}

.task__detail {
  position: absolute;
  left: 0;
  top: calc(100% + 10px);
  background: #fff;
  border: 1px solid rgba(166, 168, 179, 0.2);
  color: #000;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  z-index: 2;
}

.task__detail:after,
.task__detail:before {
  bottom: 100%;
  left: 30%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.task__detail:before {
  border-bottom-color: rgba(166, 168, 179, 0.2);
  border-width: 8px;
  margin-left: -8px;
}

.task__detail:after {
  border-bottom-color: #fff;
  border-width: 6px;
  margin-left: -6px;
}

.task__detail h2 {
  font-size: 15px;
  margin: 0;
  color: #51565d;
}

.task__detail p {
  margin-top: 4px;
  font-size: 12px;
  margin-bottom: 0;
  font-weight: 500;
  color: rgba(81, 86, 93, 0.7);
}

.two_row_align {
  /* background: #EDF6FF; */
  height: 79px;
  border: 1px solid #55a8f0;
  border-radius: 5px;
  font-weight: 500;
}

.brack_gray {
  /* border: 1px solid #55a8f0; */
  height: 79px;
  font-weight: 500;
  background: #dfdfe3;
  border: none;
  padding-top: 20px;
}

.border_none {
  border: none;
}

.arrow_padding {
  padding: 1px 6px 1px 6px;
  border-radius: 3px;
  border: 1px solid #d1f4ff;
  font-size: 14px;
  box-shadow: 1px 2px 3px 0px rgb(0 0 0 / 22%);
}

.course_sidebar {
  padding: 10px 10px 10px 10px;
  border: 1px solid #f7f5f59e;
}

.course_sidebar:hover {
  background: #edf6ff;
}

.course_sidebar_inner {
  background: #f7f5f59e;
  padding: 10px 10px 10px 10px;
  border: 1px solid #f7f5f59e;
}

.course_active {
  background: #edf6ff;
}

.lecture_text {
  width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.border_top {
  border-top: 1px solid #36597c29 !important;
}

.bg-check.active {
  background: rgba(54, 127, 193, 0.05);
  border: 1px solid #3e95ef;
  border-radius: 8px;
}

.bg-check {
  border: 1px solid #3e95ef;
  border-radius: 8px;
}

.remote_schedule {
  border-radius: 6px;
  background: #fff;
  border: 1px solid #d7dadc;
  padding: 10px 20px 10px 20px;
}

.remote_schedule:hover {
  border: 1px solid #a1cbf5;
  border-radius: 6px;
  background: #edf6ff;
}

.emptyRender {
  font-size: 16px;
  letter-spacing: 0.25px;
  color: rgba(0, 0, 0, 0.38);
}

.disable-grey-tr {
  background: #fafafa;
}
.disable-grey-tr td {
  color: rgba(0, 0, 0, 0.54);
}

select.selectArrow {
  background: url(../../../Assets/dropdown.png) no-repeat right #ddd;
  -webkit-appearance: none;
  background-position: 96%;
  background-color: #fff;
  padding: 0 30px 0 10px !important;
}

.icon_schedule {
  border: 1px solid #3e95ef;
  border-radius: 7px;
  padding: 2px 5px 2px 5px;
  background: white;
}

.icon_schedule2 {
  border: 1px solid #ced4da;
  border-radius: 10px;
  padding: 2px 5px 2px 5px;
  background: white;
}
