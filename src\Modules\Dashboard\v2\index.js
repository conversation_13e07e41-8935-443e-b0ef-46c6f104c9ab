import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Map } from 'immutable';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Breadcrumb from 'Widgets/Breadcrumb/v2/Breadcrumb';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';
import { selectLoading, selectMessage } from '_reduxapi/dashboard/selectors';
import '../css/dashboard.css';

class Dashboard extends Component {
  isLoading() {
    const { history } = this.props;
    const { location } = history;
    return (
      location.pathname !== '/dashboard' && this.props.loading.valueSeq().some((value) => value)
    );
  }

  render() {
    const { message } = this.props;
    const labelName = 'Dashboard';
    const items = [{ to: '#', label: labelName }];
    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={this.isLoading()} />
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <div className="main">
          <div className="container">
            <div className="row"></div>
          </div>
        </div>
      </div>
    );
  }
}

Dashboard.propTypes = {
  message: PropTypes.string,
  loading: PropTypes.instanceOf(Map),
  history: PropTypes.object,
};

const mapStateToProps = function (state) {
  return {
    loading: selectLoading(state),
    message: selectMessage(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(Dashboard);
