import React from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import { t } from 'i18next';
import { getLang, isMobileVerifyMandatory } from '../../../utils';

function PublishInstitutionCalendar({
  show,
  currentCalendar,
  publishState,
  onClose,
  handleAllCheckBox,
  publishSubmit,
}) {
  const lang = getLang();
  const {
    publishError,
    completeError,
    completeClass,
    completeEmail,
    completeScheduler,
    completeSms,
    publishStaff,
    publishStudent,
  } = publishState;
  return (
    <Modal
      show={show}
      centered
      onHide={onClose}
      dialogClassName="modal-30w"
      aria-labelledby="example-custom-modal-styling-title"
    >
      <Modal.Header closeButton>
        <div className="row w-100">
          <div className="col-md-12 pt-1 f-18 font-weight-bold">{t('publishInstitution')}</div>
        </div>
      </Modal.Header>
      <Modal.Body>
        <div className="row justify-content-center pt-1">
          <div className="col-md-12">
            <p>{t('publishCalender')} </p>
            <p className="pt-2 pr-2 ">
              {t('notifyUser')}
              <span className="pl-3">
                <input
                  type="checkbox"
                  className="calendarFormRadio"
                  onClick={(e) => handleAllCheckBox(e, 'staff')}
                  value="checkedall"
                />{' '}
                {t('events.staff')}
              </span>
              <span className="pl-5">
                <input
                  type="checkbox"
                  className="calendarFormRadio"
                  onClick={(e) => handleAllCheckBox(e, 'student')}
                  value="checkedall"
                />{' '}
                {t('students')}
              </span>
            </p>
            <div className="f-16 text-red text-center"> {publishError}</div>
            <div className="pt-2 pb-2"> {t('notifyVia')}</div>
            <p className={`${lang === 'ar' ? 'pl-4' : 'pr-4'} float-left`}>
              <input
                type="checkbox"
                className="calendarFormRadio"
                onClick={(e) => handleAllCheckBox(e, 'completeemail')}
                value="checkedall"
              />{' '}
              {t('email')}
            </p>
            {isMobileVerifyMandatory() && (
              <p className={`${lang === 'ar' ? '' : 'pr-4'} float-left`}>
                <input
                  type="checkbox"
                  className="calendarFormRadio"
                  onClick={(e) => handleAllCheckBox(e, 'completesms')}
                  value="checkedall"
                />{' '}
                {t('sms')}
              </p>
            )}
            <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
              <input
                type="checkbox"
                className="calendarFormRadio"
                onClick={(e) => handleAllCheckBox(e, 'completescheduler')}
                value="checkedall"
              />{' '}
              DigiScheduler
            </p>
            <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
              <input
                type="checkbox"
                className="calendarFormRadio"
                onClick={(e) => handleAllCheckBox(e, 'completeclass')}
                value="checkedall"
              />{' '}
              DigiClass
            </p>
            <div>
              {' '}
              <p className="f-16 text-red">{completeError}</p>{' '}
            </div>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <span className="remove_hover btn btn-outline-primary" onClick={onClose}>
          {t('events.close')}
        </span>
        {currentCalendar &&
          (completeClass !== '' ||
            completeEmail !== '' ||
            completeScheduler !== '' ||
            completeSms !== '') &&
          (publishStaff !== '' || publishStudent !== '') && (
            <span className="remove_hover btn btn-primary" onClick={publishSubmit}>
              {t('role_management.role_actions.Publish')}
            </span>
          )}
      </Modal.Footer>
    </Modal>
  );
}

PublishInstitutionCalendar.propTypes = {
  show: PropTypes.bool,
  currentCalendar: PropTypes.bool,
  publishState: PropTypes.object,
  onClose: PropTypes.func,
  handleAllCheckBox: PropTypes.func,
  publishSubmit: PropTypes.func,
};

export default PublishInstitutionCalendar;
