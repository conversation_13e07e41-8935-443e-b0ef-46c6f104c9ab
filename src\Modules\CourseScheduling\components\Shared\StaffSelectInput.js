import React from 'react';
import { Map, List } from 'immutable';
import PropTypes from 'prop-types';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import ClearIcon from '@mui/icons-material/Clear';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { formattedFullName } from 'Modules/ReportsAndAnalytics/utils';

const StaffSelectInput = ({ setModalState, modalState, staffOptionList }) => {
  const handleChange = (event) => {
    const selectedValues = event.target.value;
    const selectedStaffList = List(selectedValues);
    setModalState((prev) => prev.set('selectedStaff', selectedStaffList));
  };

  const handleDelete = (index) => {
    setModalState(
      modalState.set('selectedStaff', modalState.get('selectedStaff', List()).delete(index))
    );
  };

  return (
    <div>
      <MaterialInput
        elementType={'materialSelectMultiple'}
        type={'text'}
        variant={'outlined'}
        size={'small'}
        elementConfig={{ options: staffOptionList.toJS() }}
        label={''}
        labelclass={'mb-0 f-14'}
        changed={(e) => handleChange(e)}
        isAllSelected={false}
        value={modalState.get('selectedStaff', List()).toJS()}
        multiple={true}
        placeholder={'Select Staff'}
      />
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, marginTop: 2 }}>
        {modalState.get('selectedStaff', List()).map((value, index) => {
          let errorStaffObject;
          const selectedStaffName = staffOptionList.find((staff) => {
            return staff.get('value') === value;
          });

          if (!selectedStaffName) {
            errorStaffObject = modalState.get('staffs', List()).find((staff) => {
              return value === staff.get('staffId', '');
            });
          }
          return (
            <>
              <Tooltips
                title={selectedStaffName === undefined ? 'This Staffs role has been removed' : ''}
              >
                <Chip
                  key={selectedStaffName}
                  color={selectedStaffName === undefined ? 'error' : 'default'}
                  variant={'outlined'}
                  label={
                    selectedStaffName === undefined
                      ? formattedFullName(errorStaffObject.get('staffName', Map()).toJS())
                      : selectedStaffName?.get('name', '')
                  }
                  onDelete={(event) => {
                    event.stopPropagation();
                    handleDelete(index);
                  }}
                  deleteIcon={<ClearIcon />}
                />
              </Tooltips>
            </>
          );
        })}
      </Box>
    </div>
  );
};
StaffSelectInput.propTypes = {
  setModalState: PropTypes.func,
  staffOptionList: PropTypes.array,
  modalState: PropTypes.instanceOf(Map),
};
export default StaffSelectInput;
