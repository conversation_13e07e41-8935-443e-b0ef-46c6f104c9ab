import React, { Fragment, useEffect } from 'react';
import { connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import swal from 'sweetalert2';
import PropTypes from 'prop-types';

import { toggleModal, courseEdit, deleteCourse } from '../../../../../_reduxapi/actions/calender';
import { CheckPermission } from '../../../../../Modules/Shared/Permissions';
import {
  PrimaryButton,
  FlexWrapper,
  Null,
  EventWrapper,
  Margin,
  DisplayTag,
  Heading,
} from '../../../Styled';
import EventRows from '../../../UtilityComponents/EventRows';
import jsPDF from 'jspdf';
import moment from 'moment';
//import { t } from 'i18next';
import { jsUcfirst, addPDFPageNo, levelRename, getEnvCollegeName } from '../../../../../utils';
import useCalendar from 'Hooks/useCalendarHook';

const CourseModal = (props) => {
  const {
    toggleModal,
    id,
    content,
    deleteCourse,
    courseEdit,
    groupYear,
    programId,
    currentProgramCalendarId,
    academic_year_name,
    nav_bar_title,
    active,
  } = props;
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlYear = params.get('year');
  let urlName = params.get('pname');

  let modal_iframe_show =
    content.modal_iframe_show !== undefined ? content.modal_iframe_show : false;
  let modal_currentPGAccess_show =
    content.modal_currentPGAccess !== undefined ? content.modal_currentPGAccess : false;

  let modal_active_year =
    content.modal_active_year !== undefined ? content.modal_active_year : 'year2';
  let modal_term = content.modal_term !== undefined ? content.modal_term : 'regular';
  let modal_level = content.modal_level !== undefined ? content.modal_level : 2;
  let modal_id = content.modal_id !== undefined ? content.modal_id : 0;
  let modal_number = content.modal_number !== undefined ? parseInt(content.modal_number) + 1 : 0;
  let course_details = groupYear[modal_active_year]['level'];

  let currentDetails = [];
  if (course_details && course_details.length > 0) {
    currentDetails = course_details.filter((list) => {
      return jsUcfirst(list.term) === jsUcfirst(modal_term) && list.level_no === modal_level;
    });
  }
  let startDate = '';
  let endDate = '';
  let level_no = '';
  let title = '';
  let events = [];
  let _course_id = '';
  var rotation_no = 'no';
  //let rotation_count = 0;
  let _id = '';
  let course_no = '';
  if (currentDetails && currentDetails.length > 0) {
    level_no = currentDetails[0].level_no;
    _id = currentDetails[0]._id;
    rotation_no = currentDetails[0].rotation;
    //rotation_count = currentDetails[0].rotation_count;
    let courseDetails = currentDetails[0].course;
    let rotationCourseDetails = currentDetails[0].rotation_course;
    let currentCourseDetails = [];
    if (courseDetails && courseDetails.length > 0) {
      currentCourseDetails = courseDetails.filter((el) => {
        return el._id === modal_id;
      });
    }
    if (rotationCourseDetails && rotationCourseDetails.length > 0) {
      let rotationDetails = rotationCourseDetails
        .filter((item) => parseInt(item.rotation_count) === parseInt(modal_number))
        .reduce((_, el) => el.course, []);
      currentCourseDetails =
        rotationDetails &&
        rotationDetails.length &&
        rotationDetails.filter((el) => {
          return el._id === modal_id;
        });
    }
    if (currentCourseDetails && currentCourseDetails.length > 0) {
      _course_id = currentCourseDetails[0]._course_id;
      startDate = currentCourseDetails[0].start_date;
      endDate = currentCourseDetails[0].end_date;
      title = currentCourseDetails[0].courses_name;
      events = currentCourseDetails[0].courses_events;

      course_no = currentCourseDetails[0].courses_number;
    }
  }
  const history = useHistory();
  const dataAlign = (dispatchFn, cancel, rotation_no, modal_number) => {
    let data = {};

    data._calendar_id = currentProgramCalendarId;
    data.level_no = level_no;
    data.batch = modal_term;
    data._course_id = _course_id;
    if (rotation_no === 'yes') {
      data.rotation_no = modal_number;
    }
    swal
      .fire({
        title: 'Confirm Delete',
        text: 'Are you sure want to delete this course?',
        icon: 'warning',
        buttons: true,
        dangerMode: true,
      })
      .then((res) => {
        if (res.isConfirmed) {
          return dispatchFn(data, rotation_no);
        } else {
          return cancel();
        }
      });
  };

  const printCourseEventListPDF = () => {
    var doc = new jsPDF();

    let eventsList = [];
    if (events && events.length > 0) {
      eventsList = events
        .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
        .map((list, index) => {
          let eventTitle =
            typeof list.event_name === 'object' ? list.event_name.first_language : list.event_name;
          let event_date = '';
          if (list.event_date !== '') {
            event_date = moment(list.event_date).format('DD MMM YYYY');
          }
          let end_date = '';
          if (list.end_date !== '') {
            end_date = moment(list.end_date).format('DD MMM YYYY');
          }
          let start_time = '';
          if (list.start_time !== '') {
            start_time = moment(Date.parse(list.start_time)).format('hh:mm  A');
          }
          let end_time = '';
          if (list.end_time !== '') {
            end_time = moment(Date.parse(list.end_time)).format('hh:mm  A');
          }
          return [
            index + 1,
            eventTitle,
            list.event_type,
            event_date,
            start_time,
            end_date,
            end_time,
          ];
        });
    }

    let yearTitle = '';
    if (
      academic_year_name !== undefined &&
      academic_year_name !== '' &&
      academic_year_name !== null
    ) {
      // let splitYear = academic_year_name.split('-');
      // let year1Title = splitYear[0];
      // let year2Title = splitYear[1];
      yearTitle = academic_year_name; //year1Title + ' - ' + year2Title.substr(2, 4);
    }

    let navTitle = '';
    if (nav_bar_title !== '') {
      navTitle = nav_bar_title.split('>');
      navTitle = navTitle[0].trim();
    }
    let rotationWeekNo = `Rotation ${modal_number}`;

    let programTitle = navTitle !== '' ? jsUcfirst(navTitle) : 'Program Calendar';
    let term = modal_term !== '' ? jsUcfirst(modal_term) : 'Regular';
    let courseTitle = title !== '' ? jsUcfirst(title) : '';
    let st_date = startDate !== '' ? moment(startDate).format('DD-MMM YYYY') : '';
    let en_date = endDate !== '' ? moment(endDate).format('DD-MMM YYYY') : '';

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text('Academic year ' + yearTitle, 14, 20);
    doc.text(getEnvCollegeName().toLowerCase(), 200, 20, null, null, 'right');
    doc.setFont('helvetica', 'bold');
    doc.text(programTitle, 14, 30);
    doc.setFont('helvetica', 'normal');
    doc.text(
      `Year : ${active.replace('year', '')}  ${levelRename('Level', programId)} : ${levelRename(
        level_no,
        programId
      )}`,
      14,
      37
    );
    doc.text(`Term : ${term}`, 14, 44);
    doc.line(14, 50, 200, 50);

    let lastPos = 50;
    if (rotation_no === 'yes') {
      doc.text(rotationWeekNo, 14, lastPos + 6);
      doc.line(14, lastPos + 9, 200, lastPos + 9);
      lastPos = 59;
    }

    doc.text(
      `${course_no} - ${courseTitle} ( Date range : ${st_date} to ${en_date} )`,
      14,
      lastPos + 10
    );

    jsPDF.autoTableSetDefaults({
      headStyles: { fillColor: '#e0e0e0', textColor: 0 },
      bodyStyles: { fillColor: '#ffffff', textColor: 0 },
    });
    doc.autoTable({
      startY: lastPos + 15,
      head: [
        ['S.no', 'Event Title', 'Event Type', 'Start Date', 'Start Time', 'End Date', 'End Time'],
      ],
      body: eventsList,
      tableLineColor: [189, 195, 199],
      tableLineWidth: 0.5,
      theme: 'grid',
    });
    addPDFPageNo(doc);
    doc.save(`${course_no}-Course-event-list.pdf`);
  };

  useEffect(() => {
    if (_course_id === undefined || _course_id === '') {
      toggleModal();
    }
  }, [_course_id, toggleModal]);

  const { isCurrentCalendar } = useCalendar();
  const currentCalendar = isCurrentCalendar();
  return (
    <Fragment>
      <FlexWrapper>
        <Heading fs="20px" fw="500">
          Course Details
        </Heading>
        <Null />

        {!modal_iframe_show && (
          <PrimaryButton
            className={events && events.length > 0 ? 'light' : 'light disable'}
            disabled={
              !(
                events &&
                events.length > 0 &&
                CheckPermission('tabs', 'Program Calendar', 'Dashboard', '', 'Course', 'Export')
              )
            }
            onClick={printCourseEventListPDF}
          >
            Export
          </PrimaryButton>
        )}

        {currentCalendar &&
          CheckPermission('tabs', 'Program Calendar', 'Dashboard', '', 'Course', 'Edit') &&
          modal_currentPGAccess_show &&
          !modal_iframe_show && (
            <PrimaryButton
              mg="0px"
              className="light"
              onClick={() => {
                courseEdit();
                history.push(
                  `/course-v1/${id}/${active}?year=${urlYear}&programid=${programId}&pname=${urlName}&name=${title}&edit=${_course_id}&_id=${_id}&number=${modal_number}&term=${modal_term}`
                );
                // changeTitle('Edit Course');
              }}
            >
              edit
            </PrimaryButton>
          )}
      </FlexWrapper>
      <Heading fw="500" mg="10px 0" fs="22px">
        {`${title}`} {course_no !== undefined ? '(' + course_no + ')' : ''}
      </Heading>
      <FlexWrapper>
        <DisplayTag style={{ marginLeft: '0' }}>{levelRename(level_no, programId)}</DisplayTag>
        <DisplayTag>Course</DisplayTag>
      </FlexWrapper>
      <FlexWrapper mg="5px 0px">
        <Heading fw="500">Start date:</Heading>
        {`${new Date(startDate).toDateString()}`}
      </FlexWrapper>
      <FlexWrapper mg="5px 0px">
        <Heading fw="500">End date:</Heading>
        {`${new Date(endDate).toDateString()}`}
      </FlexWrapper>
      <p>Course Events</p>
      <EventWrapper mg="20px 0px">
        <EventRows show="title" />
        {events &&
          events.length > 0 &&
          events
            .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
            .map((item, i) => (
              <EventRows
                key={item._id}
                show="content"
                content={item}
                i={i}
                icon="off"
                del={() => {}}
                edit={() => {}}
              />
            ))}
      </EventWrapper>
      <FlexWrapper>
        {currentCalendar &&
          CheckPermission('tabs', 'Program Calendar', 'Dashboard', '', 'Course', 'Delete') &&
          modal_currentPGAccess_show &&
          !modal_iframe_show && (
            <FlexWrapper
              onClick={() => {
                dataAlign(deleteCourse, toggleModal, rotation_no, modal_number);
                toggleModal();
              }}
            >
              <i className="fas fa-trash"></i>
              <Margin mg="0px 10px">Delete course</Margin>
            </FlexWrapper>
          )}
        <Null />
        <PrimaryButton className="bordernone" mg="0px" onClick={() => toggleModal()}>
          cancel
        </PrimaryButton>
      </FlexWrapper>
    </Fragment>
  );
};

CourseModal.propTypes = {
  toggleModal: PropTypes.func,
  id: PropTypes.string,
  content: PropTypes.object,
  deleteCourse: PropTypes.func,
  courseEdit: PropTypes.func,
  groupYear: PropTypes.object,
  programId: PropTypes.string,
  currentProgramCalendarId: PropTypes.string,
  academic_year_name: PropTypes.string,
  nav_bar_title: PropTypes.string,
  active: PropTypes.string,
};

const mapStateToProps = ({ auth, calender, interimCalendar }) => ({
  active: calender.active_year,
  groupYear: {
    year1: calender.year1,
    year2: calender.year2,
    year3: calender.year3,
    year4: calender.year4,
    year5: calender.year5,
    year6: calender.year6,
  },
  id: calender.institution_Calender_Id,
  content: calender.edit_content,
  programId: calender.programId,
  institution_Calender_Id: calender.institution_Calender_Id,
  currentProgramCalendarId: calender.program_calender_id,
  academic_year_name: calender.academic_year_name,
  nav_bar_title:
    calender.nav_bar_title !== ''
      ? calender.nav_bar_title
      : '' || interimCalendar.nav_bar_title !== ''
      ? interimCalendar.nav_bar_title
      : '',
  userRole: auth.loggedInUserData.role,
});

export default connect(mapStateToProps, {
  deleteCourse,
  toggleModal,
  courseEdit,
})(CourseModal);
