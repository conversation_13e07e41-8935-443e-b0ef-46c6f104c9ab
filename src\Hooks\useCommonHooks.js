import React from 'react';
import LocalStorageService from 'LocalStorageService';

export const useCollegeLogo = () => {
  const response = LocalStorageService.getCustomCookie('unifyData', true);
  const collegeLogo = response?.collegeLogo;
  return collegeLogo;
};

export const CollegeLogo = () => {
  const collegeLogo = useCollegeLogo();
  return <img src={collegeLogo} alt="Digi-scheduler" className="univ-logo" />;
};
