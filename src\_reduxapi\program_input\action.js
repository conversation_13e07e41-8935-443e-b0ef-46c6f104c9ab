import { fromJS, List, Map } from 'immutable';
import { createAction } from '../util';
import axios from '../../axios';
import { capitalize, eString, jsUcfirst } from '../../utils';
import i18n from '../../i18n';
import { programListWithType } from '_reduxapi/actions';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_ADD_STUDENT_ACTIVE = 'SET_ADD_STUDENT_ACTIVE';
const setAddStudentActive = createAction(SET_ADD_STUDENT_ACTIVE, 'status');
export function setAddStudent(status) {
  return function (dispatch) {
    dispatch(setAddStudentActive(status));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const POST_COLLEGE_REQUEST = 'POST_COLLEGE_REQUEST';
export const POST_COLLEGE_SUCCESS = 'POST_COLLEGE_SUCCESS';
export const POST_COLLEGE_FAILURE = 'POST_COLLEGE_FAILURE';

const postCollegeRequest = createAction(POST_COLLEGE_REQUEST, 'isLoading');
const postCollegeSuccess = createAction(POST_COLLEGE_SUCCESS, 'data');
const postCollegeFailure = createAction(POST_COLLEGE_FAILURE, 'error');

export const postCollege = (data, cb) => {
  return (dispatch) => {
    dispatch(postCollegeRequest(true));
    return axios
      .post(`digi_institute`, data)
      .then((res) => {
        dispatch(postCollegeSuccess(res.data.data));
        cb();
        // dispatch(setAddStudent(false));
      })
      .catch((errors) => dispatch(postCollegeFailure(errors)));
  };
};

export const UPDATE_COLLEGE_REQUEST = 'UPDATE_COLLEGE_REQUEST';
export const UPDATE_COLLEGE_SUCCESS = 'UPDATE_COLLEGE_SUCCESS';
export const UPDATE_COLLEGE_FAILURE = 'UPDATE_COLLEGE_FAILURE';

const updateCollegeRequest = createAction(UPDATE_COLLEGE_REQUEST, 'isLoading');
const updateCollegeSuccess = createAction(UPDATE_COLLEGE_SUCCESS, 'data');
const updateCollegeFailure = createAction(UPDATE_COLLEGE_FAILURE, 'error');

export const updateCollege = (data, cb, id) => {
  return (dispatch) => {
    dispatch(updateCollegeRequest(true));
    return axios
      .put(`digi_institute/${id}`, data)
      .then((res) => {
        dispatch(updateCollegeSuccess(res.data.data));
        cb();
      })
      .catch((errors) => dispatch(updateCollegeFailure(errors)));
  };
};

export const GET_COLLEGE_LIST_REQUEST = 'GET_COLLEGE_LIST_REQUEST';
export const GET_COLLEGE_LIST_SUCCESS = 'GET_COLLEGE_LIST_SUCCESS';
export const GET_COLLEGE_LIST_FAILURE = 'GET_COLLEGE_LIST_FAILURE';

const getCollegeListRequest = createAction(GET_COLLEGE_LIST_REQUEST, 'isLoading');
const getCollegeListSuccess = createAction(GET_COLLEGE_LIST_SUCCESS, 'data');
const getCollegeListFailure = createAction(GET_COLLEGE_LIST_FAILURE, 'error');

export function getCollegeList() {
  return function (dispatch) {
    dispatch(getCollegeListRequest(true));
    axios
      .get('digi_institute?limit=20&pageNo=1')
      .then((res) => dispatch(getCollegeListSuccess(res.data.data)))
      .catch((error) => dispatch(getCollegeListFailure(error)));
  };
}

export const DELETE_REQUEST = 'DELETE_REQUEST';
export const DELETE_SUCCESS = 'DELETE_SUCCESS';
export const DELETE_FAILURE = 'DELETE_FAILURE';

const deleteRequest = createAction(DELETE_REQUEST, 'isLoading');
const deleteSuccess = createAction(DELETE_SUCCESS, 'data', 'message');
const deleteFailure = createAction(DELETE_FAILURE, 'error');

export const deleteData = (endPoint, id, message, cb) => {
  return function (dispatch) {
    dispatch(deleteRequest(true));
    axios
      .delete(`${endPoint}/${id}`)
      .then((res) => {
        if (['impact', 'content'].includes(message)) {
          message = `${capitalize(message)} Mapping`;
        }
        dispatch(deleteSuccess(res.data.data, message));
        cb();
      })
      .catch((error) => dispatch(deleteFailure(error)));
  };
};

export const POST_PROGRAM_REQUEST = 'POST_PROGRAM_REQUEST';
export const POST_PROGRAM_SUCCESS = 'POST_PROGRAM_SUCCESS';
export const POST_PROGRAM_FAILURE = 'POST_PROGRAM_FAILURE';

const postProgramRequest = createAction(POST_PROGRAM_REQUEST, 'isLoading');
const postProgramSuccess = createAction(POST_PROGRAM_SUCCESS, 'data');
const postProgramFailure = createAction(POST_PROGRAM_FAILURE, 'error');

export const postProgram = (data, cb, type, history) => {
  return (dispatch) => {
    dispatch(postProgramRequest(true));
    return axios
      .post(`digi_program`, data)
      .then((res) => {
        let msg =
          data.program_type === 'pre-requisite'
            ? i18n.t('messages.create_pre_requisite')
            : i18n.t('messages.create_program');
        dispatch(postProgramSuccess(msg));
        cb();
        dispatch(programListWithType(true));
        if (type === 'continue') {
          history.push(`/program-input/program/${eString(res.data.data.responses._id)}/configure`);
        }
      })
      .catch((errors) => dispatch(postProgramFailure(errors)));
  };
};

export const UPDATE_PROGRAM_REQUEST = 'UPDATE_PROGRAM_REQUEST';
export const UPDATE_PROGRAM_SUCCESS = 'UPDATE_PROGRAM_SUCCESS';
export const UPDATE_PROGRAM_FAILURE = 'UPDATE_PROGRAM_FAILURE';

const updateProgramRequest = createAction(UPDATE_PROGRAM_REQUEST, 'isLoading');
const updateProgramSuccess = createAction(UPDATE_PROGRAM_SUCCESS, 'data');
const updateProgramFailure = createAction(UPDATE_PROGRAM_FAILURE, 'error');

export const updateProgram = (data, cb, id, componentName = null) => {
  return (dispatch) => {
    dispatch(updateProgramRequest(true));
    return axios
      .put(`digi_program/${id}`, data)
      .then((res) => {
        let msg =
          data.program_type === 'pre-requisite'
            ? i18n.t('messages.update_pre_requisite')
            : i18n.t('messages.update_program');
        if (data.isActive !== undefined && componentName !== null) {
          msg = `${componentName} ${
            data.isActive ? i18n.t('messages.restored') : i18n.t('messages.archived')
          } ${i18n.t('messages.successfully')}`;
        }
        dispatch(updateProgramSuccess(msg));
        cb();
        dispatch(programListWithType(true));
      })
      .catch((errors) => dispatch(updateProgramFailure(errors)));
  };
};

export const GET_PROGRAM_LIST_REQUEST = 'GET_PROGRAM_LIST_REQUEST';
export const GET_PROGRAM_LIST_SUCCESS = 'GET_PROGRAM_LIST_SUCCESS';
export const GET_PROGRAM_LIST_FAILURE = 'GET_PROGRAM_LIST_FAILURE';

const getProgramListRequest = createAction(GET_PROGRAM_LIST_REQUEST, 'isLoading');
const getProgramListSuccess = createAction(GET_PROGRAM_LIST_SUCCESS, 'data');
const getProgramListFailure = createAction(GET_PROGRAM_LIST_FAILURE, 'error');

export function getProgramList() {
  return function (dispatch) {
    dispatch(getProgramListRequest(true));
    axios
      .get('digi_program')
      .then((res) => dispatch(getProgramListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramListFailure(error)));
  };
}

export const GET_PROGRAM_DEPT_LIST_REQUEST = 'GET_PROGRAM_DEPT_LIST_REQUEST';
export const GET_PROGRAM_DEPT_LIST_SUCCESS = 'GET_PROGRAM_DEPT_LIST_SUCCESS';
export const GET_PROGRAM_DEPT_LIST_FAILURE = 'GET_PROGRAM_DEPT_LIST_FAILURE';

const getProgramDeptListRequest = createAction(GET_PROGRAM_DEPT_LIST_REQUEST);
const getProgramDeptListSuccess = createAction(GET_PROGRAM_DEPT_LIST_SUCCESS, 'data');
const getProgramDeptListFailure = createAction(GET_PROGRAM_DEPT_LIST_FAILURE, 'error');

export function getProgramDeptList() {
  return function (dispatch) {
    dispatch(getProgramDeptListRequest());
    axios
      .get('digi_program/program_department_list')
      .then((res) => dispatch(getProgramDeptListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramDeptListFailure(error)));
  };
}

export const GET_PROGRAM_BY_ID_REQUEST = 'GET_PROGRAM_BY_ID_REQUEST';
export const GET_PROGRAM_BY_ID_SUCCESS = 'GET_PROGRAM_BY_ID_SUCCESS';
export const GET_PROGRAM_BY_ID_FAILURE = 'GET_PROGRAM_BY_ID_FAILURE';

const getProgramByIdRequest = createAction(GET_PROGRAM_BY_ID_REQUEST);
const getProgramByIdSuccess = createAction(GET_PROGRAM_BY_ID_SUCCESS, 'data');
const getProgramByIdFailure = createAction(GET_PROGRAM_BY_ID_FAILURE, 'error');

export function getProgramById(id, fetchAdditionalData = false) {
  return function (dispatch) {
    dispatch(getProgramByIdRequest());
    axios
      .get(`digi_program/${id}`)
      .then((res) => {
        const data = res.data.data;
        dispatch(getProgramByIdSuccess(data));
        if (fetchAdditionalData) {
          dispatch(getDepartmentList(data._id));
          dispatch(getSessionTypes(data._id));
        }
      })
      .catch((error) => dispatch(getProgramByIdFailure(error)));
  };
}

export const GET_DEPARTMENT_LIST_REQUEST = 'GET_DEPARTMENT_LIST_REQUEST';
export const GET_DEPARTMENT_LIST_SUCCESS = 'GET_DEPARTMENT_LIST_SUCCESS';
export const GET_DEPARTMENT_LIST_FAILURE = 'GET_DEPARTMENT_LIST_FAILURE';

const getDepartmentListRequest = createAction(GET_DEPARTMENT_LIST_REQUEST);
const getDepartmentListSuccess = createAction(GET_DEPARTMENT_LIST_SUCCESS, 'data');
const getDepartmentListFailure = createAction(GET_DEPARTMENT_LIST_FAILURE, 'error');

export function getDepartmentList(programId) {
  return function (dispatch) {
    dispatch(getDepartmentListRequest());
    axios
      .get(`digi_department_subject/withOutShare/${programId}`)
      .then((res) => dispatch(getDepartmentListSuccess(res.data.data)))
      .catch((error) => dispatch(getDepartmentListFailure(error)));
  };
}

export const ADD_DEPARTMENT_REQUEST = 'ADD_DEPARTMENT_REQUEST';
export const ADD_DEPARTMENT_SUCCESS = 'ADD_DEPARTMENT_SUCCESS';
export const ADD_DEPARTMENT_FAILURE = 'ADD_DEPARTMENT_FAILURE';

const addDepartmentRequest = createAction(ADD_DEPARTMENT_REQUEST);
const addDepartmentSuccess = createAction(ADD_DEPARTMENT_SUCCESS, 'data');
const addDepartmentFailure = createAction(ADD_DEPARTMENT_FAILURE, 'error');

export function addDepartment(
  { operation, _id, program_id, program_name, department_name },
  callBack = () => {}
) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(addDepartmentRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      const requestBody = {
        ...{ program_id, program_name }, //operation === 'create' &&
        department_name,
      };
      axios[method[operation]](
        `digi_department_subject${operation !== 'create' ? '/' + _id : ''}`,
        ['create', 'update'].includes(operation) ? requestBody : {}
      )
        .then((res) => {
          dispatch(addDepartmentSuccess({ data: res.data.data, operation }));
          dispatch(getProgramDeptList());
          dispatch(getDepartmentList(program_id));
          dispatch(getProgramInputList(program_id));
          callBack();
        })
        .catch((error) => dispatch(addDepartmentFailure(error)));
    }
  };
}

export const ADD_SUBJECT_REQUEST = 'ADD_SUBJECT_REQUEST';
export const ADD_SUBJECT_SUCCESS = 'ADD_SUBJECT_SUCCESS';
export const ADD_SUBJECT_FAILURE = 'ADD_SUBJECT_FAILURE';

const addSubjectRequest = createAction(ADD_SUBJECT_REQUEST);
const addSubjectSuccess = createAction(ADD_SUBJECT_SUCCESS, 'data');
const addSubjectFailure = createAction(ADD_SUBJECT_FAILURE, 'error');

export function addSubject({ operation, _id, program_id, department_id, subject, subject_name }) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(addSubjectRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      const requestBody = {
        ...(operation === 'create' && { department_id, program_id, subject }),
        ...(operation === 'update' && { program_id, subject_name }),
      };
      axios[method[operation]](
        `digi_department_subject/subject${
          operation !== 'create' ? '/' + department_id + '/' + _id : ''
        }`,
        ['create', 'update'].includes(operation) ? requestBody : {}
      )
        .then((res) => {
          dispatch(addSubjectSuccess({ data: res.data.data, operation }));
          dispatch(getDepartmentList(program_id));
          dispatch(getProgramInputList(program_id));
        })
        .catch((error) => dispatch(addSubjectFailure(error)));
    }
  };
}

export const SHARE_DEPARTMENT_REQUEST = 'SHARE_DEPARTMENT_REQUEST';
export const SHARE_DEPARTMENT_SUCCESS = 'SHARE_DEPARTMENT_SUCCESS';
export const SHARE_DEPARTMENT_FAILURE = 'SHARE_DEPARTMENT_FAILURE';

const shareDepartmentRequest = createAction(SHARE_DEPARTMENT_REQUEST);
const shareDepartmentSuccess = createAction(SHARE_DEPARTMENT_SUCCESS, 'data', 'msg');
const shareDepartmentFailure = createAction(SHARE_DEPARTMENT_FAILURE, 'error');

export function shareDepartment({ programId, departmentId, msg, ...requestBody }) {
  return function (dispatch) {
    dispatch(shareDepartmentRequest());
    axios
      .put(`digi_department_subject/share_department/${departmentId}`, requestBody)
      .then((res) => {
        // let msg = i18n.t('messages.department_shared_successful');
        dispatch(shareDepartmentSuccess(res.data.data, msg));
        //dispatch(shareDepartmentSuccess(msg));
        dispatch(getDepartmentList(programId));
        dispatch(getProgramInputList(programId));
      })

      .catch((error) => dispatch(shareDepartmentFailure(error)));
  };
}

export const SHARE_SUBJECT_REQUEST = 'SHARE_SUBJECT_REQUEST';
export const SHARE_SUBJECT_SUCCESS = 'SHARE_SUBJECT_SUCCESS';
export const SHARE_SUBJECT_FAILURE = 'SHARE_SUBJECT_FAILURE';

const shareSubjectRequest = createAction(SHARE_SUBJECT_REQUEST);
const shareSubjectSuccess = createAction(SHARE_SUBJECT_SUCCESS, 'data', 'message');
const shareSubjectFailure = createAction(SHARE_SUBJECT_FAILURE, 'error');

export function shareSubject({ programId, departmentId, subjectId, msg, ...requestBody }) {
  return function (dispatch) {
    dispatch(shareSubjectRequest());
    axios
      .put(`digi_department_subject/share_subject/${departmentId}/${subjectId}`, requestBody)
      .then((res) => {
        dispatch(shareSubjectSuccess(res.data.data, msg));
        dispatch(getDepartmentList(programId));
        dispatch(getProgramInputList(programId));
      })
      .catch((error) => dispatch(shareSubjectFailure(error)));
  };
}

export const GET_SESSION_TYPE_LIST_REQUEST = 'GET_SESSION_TYPE_LIST_REQUEST';
export const GET_SESSION_TYPE_LIST_SUCCESS = 'GET_SESSION_TYPE_LIST_SUCCESS';
export const GET_SESSION_TYPE_LIST_FAILURE = 'GET_SESSION_TYPE_LIST_FAILURE';

const getSessionTypeListRequest = createAction(GET_SESSION_TYPE_LIST_REQUEST);
const getSessionTypeListSuccess = createAction(GET_SESSION_TYPE_LIST_SUCCESS, 'data');
const getSessionTypeListFailure = createAction(GET_SESSION_TYPE_LIST_FAILURE, 'error');

export function getSessionTypes(programId, createCourse = false) {
  return function (dispatch) {
    dispatch(getSessionTypeListRequest());
    axios
      .get(`digi_session_delivery_types/${programId}`)
      .then((res) => {
        dispatch(getSessionTypeListSuccess(res.data.data));
        if (createCourse) {
          dispatch(setSessionTypes(res.data.data));
        }
      })
      .catch((error) => dispatch(getSessionTypeListFailure(error)));
  };
}

export const SET_SESSION_TYPES_IN_COURSE = 'SET_SESSION_TYPES_IN_COURSE';

const setSessionTypesInCourse = createAction(SET_SESSION_TYPES_IN_COURSE, 'data');

function setSessionTypes(sessionTypes) {
  return function (dispatch) {
    const data = [];
    sessionTypes.forEach((s) => {
      const sessionType = {
        type_name: s.session_name,
        type_symbol: s.session_symbol,
        credit_hours: '',
        contact_hours: `${s.contact_hour_per_credit_hour}`,
        duration_per_contact_hour: s.duration_per_contact_hour,
        duration_split: false,
        duration: `${s.session_duration}`,
        _session_id: s._id,
        delivery_type: s.delivery_types.map((d) => {
          return {
            delivery_type: d.delivery_name,
            delivery_symbol: d.delivery_symbol,
            duration: `${d.delivery_duration}`,
            isActive: false,
            _delivery_id: d._id,
          };
        }),
      };
      data.push(sessionType);
    });
    dispatch(setSessionTypesInCourse(data));
  };
}

export const ADD_SESSION_DELIVERY_TYPE_REQUEST = 'ADD_SESSION_DELIVERY_TYPE_REQUEST';
export const ADD_SESSION_DELIVERY_TYPE_SUCCESS = 'ADD_SESSION_DELIVERY_TYPE_SUCCESS';
export const ADD_SESSION_DELIVERY_TYPE_FAILURE = 'ADD_SESSION_TYPE_FAILURE';

const addSessionDeliveryTypeRequest = createAction(ADD_SESSION_DELIVERY_TYPE_REQUEST);
const addSessionDeliveryTypeSuccess = createAction(ADD_SESSION_DELIVERY_TYPE_SUCCESS, 'data');
const addSessionDeliveryTypeFailure = createAction(ADD_SESSION_DELIVERY_TYPE_FAILURE, 'error');

export function addSessionDeliveryType(
  { operation, type, _id, sessionTypeId, program_id, ...rest },
  callBack = () => {}
) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(addSessionDeliveryTypeRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      const isSessionType = type === 'sessionType';
      const requestBody = {
        ...rest,
        ...{ program_id },
      };
      let url = '';
      if (isSessionType) {
        url = `digi_session_delivery_types${operation !== 'create' ? '/' + _id : ''}`;
      } else {
        let path = 'add_delivery_type';
        if (['update', 'delete'].includes(operation)) {
          path = `${operation}_delivery_type`;
        }
        url = `digi_session_delivery_types/${path}/${sessionTypeId}${
          operation !== 'create' ? '/' + _id : ''
        }`;
      }
      axios[method[operation]](url, ['create', 'update'].includes(operation) ? requestBody : {})
        .then((res) => {
          dispatch(
            addSessionDeliveryTypeSuccess({
              data: res.data.data,
              type: isSessionType ? 'Session' : 'Delivery',
              operation,
            })
          );
          dispatch(getSessionTypes(program_id));
          dispatch(getProgramInputList(program_id));
          callBack();
        })
        .catch((error) => dispatch(addSessionDeliveryTypeFailure(error)));
    }
  };
}

export const SAVE_CURRICULUM_REQUEST = 'SAVE_CURRICULUM_REQUEST';
export const SAVE_CURRICULUM_SUCCESS = 'SAVE_CURRICULUM_SUCCESS';
export const SAVE_CURRICULUM_FAILURE = 'SAVE_CURRICULUM_FAILURE';

const saveCurriculumRequest = createAction(SAVE_CURRICULUM_REQUEST, 'isLoading');
const saveCurriculumSuccess = createAction(SAVE_CURRICULUM_SUCCESS, 'data');
const saveCurriculumFailure = createAction(SAVE_CURRICULUM_FAILURE, 'error');

export const saveCurriculum = (data, type, curriculumId, redirect, history, urlLink, callBack) => {
  return (dispatch) => {
    dispatch(saveCurriculumRequest(true));
    let url = type === 'add' ? `/digi_curriculum` : `/digi_curriculum/${curriculumId}`;
    let message =
      type === 'add' ? i18n.t('messages.create_curriculum') : i18n.t('messages.update_curriculum');
    let method = type === 'add' ? 'post' : 'put';
    return axios[method](url, data)
      .then((res) => {
        dispatch(saveCurriculumSuccess(message));
        dispatch(getCurriculumList(data._program_id));
        callBack();
        if (redirect === 'continue' && type === 'add') {
          history.push(
            `${urlLink}&_curriculum_id=${eString(
              res.data.data.responses._id
            )}&_curriculum_name=${eString(res.data.data.responses.curriculum_name)}`
          );
        }
      })
      .catch((errors) => dispatch(saveCurriculumFailure(errors)));
  };
};

export const GET_CURRICULUM_REQUEST = 'GET_CURRICULUM_REQUEST';
export const GET_CURRICULUM_SUCCESS = 'GET_CURRICULUM_SUCCESS';
export const GET_CURRICULUM_FAILURE = 'GET_CURRICULUM_FAILURE';

const getCurriculumRequest = createAction(GET_CURRICULUM_REQUEST);
const getCurriculumSuccess = createAction(GET_CURRICULUM_SUCCESS, 'data');
const getCurriculumFailure = createAction(GET_CURRICULUM_FAILURE, 'error');

export function getCurriculum(id, createCourse = false) {
  return function (dispatch) {
    dispatch(getCurriculumRequest());
    axios
      .get(`digi_curriculum/${id}`)
      .then((res) => {
        const data = Array.isArray(res.data.data) && res.data.data.length ? res.data.data[0] : {};
        dispatch(getCurriculumSuccess(data));
        if (data._program_id) {
          dispatch(getSessionTypes(data._program_id, createCourse));
        }
      })
      .catch((error) => dispatch(getCurriculumFailure(error)));
  };
}

export const GET_CURRICULUM_LIST_REQUEST = 'GET_CURRICULUM_LIST_REQUEST';
export const GET_CURRICULUM_LIST_SUCCESS = 'GET_CURRICULUM_LIST_SUCCESS';
export const GET_CURRICULUM_LIST_FAILURE = 'GET_CURRICULUM_LIST_FAILURE';

const getCurriculumListRequest = createAction(GET_CURRICULUM_LIST_REQUEST, 'isLoading');
const getCurriculumListSuccess = createAction(GET_CURRICULUM_LIST_SUCCESS, 'data');
const getCurriculumListFailure = createAction(GET_CURRICULUM_LIST_FAILURE, 'error');

export function getCurriculumList(programId) {
  return function (dispatch) {
    dispatch(getCurriculumListRequest(true));
    axios
      .get(`/digi_curriculum/get_curriculum_by_program_id/${programId}`)
      .then((res) => {
        dispatch(getCurriculumListSuccess(res.data.data));
        dispatch(getProgramInputList(programId));
      })
      .catch((error) => dispatch(getCurriculumListFailure(error)));
  };
}

export const ARCHIVE_CURRICULUM_REQUEST = 'ARCHIVE_CURRICULUM_REQUEST';
export const ARCHIVE_CURRICULUM_SUCCESS = 'ARCHIVE_CURRICULUM_SUCCESS';
export const ARCHIVE_CURRICULUM_FAILURE = 'ARCHIVE_CURRICULUM_FAILURE';

const archiveCurriculumRequest = createAction(ARCHIVE_CURRICULUM_REQUEST, 'isLoading');
const archiveCurriculumSuccess = createAction(ARCHIVE_CURRICULUM_SUCCESS, 'data');
const archiveCurriculumFailure = createAction(ARCHIVE_CURRICULUM_FAILURE, 'error');

export function archiveCurriculum(data, callBack = () => {}, id) {
  return function (dispatch) {
    dispatch(archiveCurriculumRequest(true));
    axios
      .put(`/digi_curriculum/archive_curriculum/${id}`, data)
      .then((res) => {
        dispatch(
          archiveCurriculumSuccess(
            data.isActive
              ? i18n.t('messages.curriculum_restored')
              : i18n.t('messages.curriculum_archived')
          )
        );
        callBack();
      })
      .catch((error) => dispatch(archiveCurriculumFailure(error)));
  };
}

export const DELETE_CURRICULUM_REQUEST = 'DELETE_CURRICULUM_REQUEST';
export const DELETE_CURRICULUM_SUCCESS = 'DELETE_CURRICULUM_SUCCESS';
export const DELETE_CURRICULUM_FAILURE = 'DELETE_CURRICULUM_FAILURE';

const deleteCurriculumRequest = createAction(DELETE_CURRICULUM_REQUEST, 'isLoading');
const deleteCurriculumSuccess = createAction(DELETE_CURRICULUM_SUCCESS, 'data');
const deleteCurriculumFailure = createAction(DELETE_CURRICULUM_FAILURE, 'error');

export function deleteCurriculum(id, programId) {
  return function (dispatch) {
    dispatch(deleteCurriculumRequest(true));
    axios
      .delete(`/digi_curriculum/${id}`)
      .then((res) => {
        dispatch(deleteCurriculumSuccess(i18n.t('messages.curriculum_deleted')));
        dispatch(getCurriculumList(programId));
      })
      .catch((error) => dispatch(deleteCurriculumFailure(error)));
  };
}

export const GET_PREREQUISITE_LIST_REQUEST = 'GET_PREREQUISITE_LIST_REQUEST';
export const GET_PREREQUISITE_LIST_SUCCESS = 'GET_PREREQUISITE_LIST_SUCCESS';
export const GET_PREREQUISITE_LIST_FAILURE = 'GET_PREREQUISITE_LIST_FAILURE';

const getPrerequisiteListRequest = createAction(GET_PREREQUISITE_LIST_REQUEST, 'isLoading');
const getPrerequisiteListSuccess = createAction(GET_PREREQUISITE_LIST_SUCCESS, 'data');
const getPrerequisiteListFailure = createAction(GET_PREREQUISITE_LIST_FAILURE, 'error');

export function getPrerequisiteList() {
  return function (dispatch) {
    dispatch(getPrerequisiteListRequest(true));
    axios
      .get(`/digi_curriculum/get_pre_requisite`)
      .then((res) => dispatch(getPrerequisiteListSuccess(res.data.data)))
      .catch((error) => dispatch(getPrerequisiteListFailure(error)));
  };
}

export const GET_FRAMEWORK_LIST_REQUEST = 'GET_FRAMEWORK_LIST_REQUEST';
export const GET_FRAMEWORK_LIST_SUCCESS = 'GET_FRAMEWORK_LIST_SUCCESS';
export const GET_FRAMEWORK_LIST_FAILURE = 'GET_FRAMEWORK_LIST_FAILURE';

const getFrameworkListRequest = createAction(GET_FRAMEWORK_LIST_REQUEST, 'isLoading', 'id');
const getFrameworkListSuccess = createAction(GET_FRAMEWORK_LIST_SUCCESS, 'data', 'id');
const getFrameworkListFailure = createAction(GET_FRAMEWORK_LIST_FAILURE, 'error');

export function getFrameworkLists(id = null) {
  return function (dispatch) {
    dispatch(getFrameworkListRequest(true, id));
    axios
      .get(`/digi_framework`)
      .then((res) => dispatch(getFrameworkListSuccess(res.data.data, id)))
      .catch((error) => dispatch(getFrameworkListFailure(error)));
  };
}

export const GET_IMPACT_LIST_REQUEST = 'GET_IMPACT_LIST_REQUEST';
export const GET_IMPACT_LIST_SUCCESS = 'GET_IMPACT_LIST_SUCCESS';
export const GET_IMPACT_LIST_FAILURE = 'GET_IMPACT_LIST_FAILURE';

const getImpactListRequest = createAction(GET_IMPACT_LIST_REQUEST, 'showLoading');
const getImpactListSuccess = createAction(GET_IMPACT_LIST_SUCCESS, 'data');
const getImpactListFailure = createAction(GET_IMPACT_LIST_FAILURE, 'error');

export function getMapTypeLists(type, showLoading = true) {
  return function (dispatch) {
    dispatch(getImpactListRequest(showLoading));
    let URL = type === 'impact' ? `digi_impact_mapping_type` : `digi_content_mapping_type`;
    axios
      .get(URL)
      .then((res) => {
        dispatch(getImpactListSuccess({ data: res.data.data, showLoading }));
        dispatch(
          setData(
            Map({
              ...(type === 'impact' && { impactMapList: fromJS(res.data.data) }),
              ...(type === 'content' && { contentMapList: fromJS(res.data.data) }),
            })
          )
        );
      })
      .catch((error) => dispatch(getImpactListFailure({ error, showLoading })));
  };
}

export const POST_FRAMEWORK_REQUEST = 'POST_FRAMEWORK_REQUEST';
export const POST_FRAMEWORK_SUCCESS = 'POST_FRAMEWORK_SUCCESS';
export const POST_FRAMEWORK_FAILURE = 'POST_FRAMEWORK_FAILURE';

const postFrameworkRequest = createAction(POST_FRAMEWORK_REQUEST, 'isLoading');
const postFrameworkSuccess = createAction(POST_FRAMEWORK_SUCCESS, 'data');
const postFrameworkFailure = createAction(POST_FRAMEWORK_FAILURE, 'error');

export const saveFramework = (data, id, callBack) => {
  return (dispatch) => {
    dispatch(postFrameworkRequest(true));
    let URL = !id ? `digi_framework` : `digi_framework/${id}`;
    let method = !id ? 'post' : 'put';
    return axios[method](URL, data)
      .then((res) => {
        dispatch(postFrameworkSuccess(id));
        dispatch(getFrameworkLists(id));
        callBack();
      })
      .catch((error) => dispatch(postFrameworkFailure(error)));
  };
};

export const POST_IMPACT_REQUEST = 'POST_IMPACT_REQUEST';
export const POST_IMPACT_SUCCESS = 'POST_IMPACT_SUCCESS';
export const POST_IMPACT_FAILURE = 'POST_IMPACT_FAILURE';

const postImpactRequest = createAction(POST_IMPACT_REQUEST, 'isLoading');
const postImpactSuccess = createAction(POST_IMPACT_SUCCESS, 'data');
const postImpactFailure = createAction(POST_IMPACT_FAILURE, 'error');

export const submitImpact = (data, id, type) => {
  return (dispatch) => {
    dispatch(postImpactRequest(true));
    let URL =
      id === null
        ? `${type === 'impact' ? 'digi_impact_mapping_type' : 'digi_content_mapping_type'}`
        : `${type === 'impact' ? 'digi_impact_mapping_type' : 'digi_content_mapping_type'}/${id}`;
    let method = id === null ? 'post' : 'put';
    return axios[method](URL, data)
      .then((res) => {
        let message = '';
        if (type === 'impact' && !id) message += i18n.t('messages.impact_added');
        if (type === 'impact' && id) message += i18n.t('messages.impact_updated');
        if (type !== 'impact' && !id) message += i18n.t('messages.content_added');
        if (type !== 'impact' && id) message += i18n.t('messages.content_updated');
        dispatch(postImpactSuccess(message));
        dispatch(getMapTypeLists(type));
      })
      .catch((error) => dispatch(postImpactFailure(error)));
  };
};

export const POST_DOMAIN_REQUEST = 'POST_DOMAIN_REQUEST';
export const POST_DOMAIN_SUCCESS = 'POST_DOMAIN_SUCCESS';
export const POST_DOMAIN_FAILURE = 'POST_DOMAIN_FAILURE';

const postDomainRequest = createAction(POST_DOMAIN_REQUEST, 'isLoading');
const postDomainSuccess = createAction(POST_DOMAIN_SUCCESS, 'data');
const postDomainFailure = createAction(POST_DOMAIN_FAILURE, 'error');

export function postDomain(data, id, type, callBack) {
  return function (dispatch) {
    dispatch(postDomainRequest(true));
    axios
      .put(`digi_framework/${id}`, data)
      .then((res) => {
        dispatch(postDomainSuccess(type));
        dispatch(getFrameworkLists(id));
        callBack();
      })
      .catch((errors) => dispatch(postDomainFailure(errors)));
  };
}

export const SAVE_COURSE_REQUEST = 'SAVE_COURSE_REQUEST';
export const SAVE_COURSE_SUCCESS = 'SAVE_COURSE_SUCCESS';
export const SAVE_COURSE_FAILURE = 'SAVE_COURSE_FAILURE';

const saveCourseRequest = createAction(SAVE_COURSE_REQUEST);
const saveCourseSuccess = createAction(SAVE_COURSE_SUCCESS, 'data');
const saveCourseFailure = createAction(SAVE_COURSE_FAILURE, 'error');

export function saveCourse({
  operation,
  _id,
  requestBody,
  url,
  history,
  isActive,
  programId,
  curriculumId,
  isVersion,
  versionType,
  callback,
}) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(saveCourseRequest());
      const method = {
        create: 'post',
        ...(!isVersion
          ? { update: 'put' }
          : {
              update: versionType === 'create' ? 'post' : 'put',
            }),
        delete: 'delete',
      };

      // const operationType = !isVersion ?
      axios[method[operation]](
        `${
          operation === 'delete' || (operation === 'create' && !isVersion)
            ? '/digi_course'
            : `/courseVersioning/${
                versionType === 'create' && isVersion
                  ? 'createCourseVersioning'
                  : 'editCourseDetails'
              }`
        }${isVersion || ['delete', 'update'].includes(operation) ? `/${_id}` : ''}`,
        ['create', 'update'].includes(operation) ? requestBody : {}
      )
        .then((res) => {
          const data = res.data.data;
          dispatch(saveCourseSuccess({ data: data, operation, isActive }));
          // if (versionType === 'create') {
          //   dispatch(
          //     setData(
          //       Map({
          //         editedSessionFlow: [],
          //         editedAdditionalSessionFlow: [],
          //       })
          //     )
          //   );
          // }

          if (operation === 'create') {
            dispatch(
              setData(
                Map({
                  course: data && data.responses ? fromJS(data.responses) : Map(),
                  ...(requestBody.isActive && { activeStep: 1 }),
                })
              )
            );
            if (!requestBody.isActive) {
              history.push(url);
            }
          } else if (operation === 'update') {
            dispatch(getCourse(versionType === 'create' ? data._id : _id));
            if (versionType !== 'create') {
              dispatch(getsessionFlow(versionType === 'create' ? data._id : _id));
            }
            if (requestBody.isActive) {
              dispatch(setData(Map({ activeStep: 1 })));
              versionType === 'create' && callback(data._id);
            } else {
              history.push(url);
            }
          } else if (operation === 'delete') {
            dispatch(getCourseListByCurriculumId(programId, curriculumId));
            //dispatch(getCourseList());
          }
        })
        .catch((error) => dispatch(saveCourseFailure(error)));
    }
  };
}

export const GET_COURSE_LIST_REQUEST = 'GET_COURSE_LIST_REQUEST';
export const GET_COURSE_LIST_SUCCESS = 'GET_COURSE_LIST_SUCCESS';
export const GET_COURSE_LIST_FAILURE = 'GET_COURSE_LIST_FAILURE';

const getCourseListRequest = createAction(GET_COURSE_LIST_REQUEST);
const getCourseListSuccess = createAction(GET_COURSE_LIST_SUCCESS, 'data');
const getCourseListFailure = createAction(GET_COURSE_LIST_FAILURE, 'error');

export function getCourseList() {
  return function (dispatch) {
    dispatch(getCourseListRequest());
    axios
      .get('/digi_course')
      .then((res) => dispatch(getCourseListSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseListFailure(error)));
  };
}

export const GET_COURSE_REQUEST = 'GET_COURSE_REQUEST';
export const GET_COURSE_SUCCESS = 'GET_COURSE_SUCCESS';
export const GET_COURSE_FAILURE = 'GET_COURSE_FAILURE';

const getCourseRequest = createAction(GET_COURSE_REQUEST);
const getCourseSuccess = createAction(GET_COURSE_SUCCESS, 'data');
const getCourseFailure = createAction(GET_COURSE_FAILURE, 'error');

export function getCourse(id, prePopulate = true) {
  return function (dispatch) {
    dispatch(getCourseRequest());
    axios
      .get(`/digi_course/${id}`)
      .then((res) => {
        const data = res.data.data && !Array.isArray(res.data.data) ? res.data.data : {};
        if (data.framework && Array.isArray(data.framework.domains)) {
          data.framework.domains = data.framework.domains.map((d) => {
            const copiedDomain = { ...d };
            copiedDomain.clo = copiedDomain.clo.filter((c) => !c.isDeleted);
            return copiedDomain;
          });
        }
        dispatch(getCourseSuccess(data));
        if (Object.keys(data).length === 0 || !prePopulate) {
          return;
        }
        let editedCourse = Map({
          course_type: data.course_type,
          course_recurring: List(data?.course_recurring.map((c) => c._level_id)),
          course_occurring: List(data?.course_occurring.map((c) => c._level_id)),
          course_code: data.course_code,
          course_name: data.course_name,
          duration: data.duration === null ? '' : `${data.duration || ''}`,
          participating: List(data.participating.map((p) => p._subject_id)),
          administration: Map(data.administration || {}),
          credit_hours: fromJS(
            data.credit_hours.map((c) => {
              return {
                ...c,
                credit_hours: c.credit_hours === null ? '' : `${c.credit_hours}`,
                contact_hours: c.contact_hours === null ? '' : `${c.contact_hours}`,
              };
            })
          ),
          allow_editing: data.allow_editing === null ? '' : data.allow_editing,
          achieve_target: data.achieve_target === null ? '' : data.achieve_target,
        });
        const administration = editedCourse.get('administration', Map());
        if (!administration.isEmpty()) {
          const value = `${administration.get('subject_name')} | ${administration.get(
            'department_name'
          )} | ${administration.get('program_name')}`;
          editedCourse = editedCourse.set(
            'administration',
            administration.set('name', value).set('value', value)
          );
        }
        dispatch(
          setData(
            Map({
              editedCourse,
            })
          )
        );
      })
      .catch((error) => dispatch(getCourseFailure(error)));
  };
}

export const GET_SESSION_FLOW_REQUEST = 'GET_SESSION_FLOW_REQUEST';
export const GET_SESSION_FLOW_SUCCESS = 'GET_SESSION_FLOW_SUCCESS';
export const GET_SESSION_FLOW_FAILURE = 'GET_SESSION_FLOW_FAILURE';

const getsessionFlowRequest = createAction(GET_SESSION_FLOW_REQUEST, 'data');
const getsessionFlowSuccess = createAction(GET_SESSION_FLOW_SUCCESS, 'data');
const getsessionFlowFailure = createAction(GET_SESSION_FLOW_FAILURE, 'error');

export function getsessionFlow(courseId, levelName, clearDataOnFetch = true) {
  return function (dispatch) {
    dispatch(getsessionFlowRequest({ clearDataOnFetch }));
    axios
      .get(`/digi_course/list_id_session_flow/${courseId}`)
      .then((res) => {
        const data = Array.isArray(res.data.data) ? {} : res.data.data;
        dispatch(getsessionFlowSuccess(data));
        if (Object.keys(data).length === 0) {
          return;
        }
        const editedSessionFlow = fromJS(
          data.session_flow_data.map((s) => {
            return {
              s_no: s.s_no,
              _session_id: s._session_id,
              delivery_type: s.delivery_type,
              _delivery_id: s._delivery_id,
              delivery_symbol: s.delivery_symbol,
              delivery_no: s.delivery_no,
              delivery_topic: s.delivery_topic,
              sessionData: s?.sessionData,
              sessionDocumentDetails: s?.sessionDocumentDetails,
              subjects: s.subjects.map((s) => s._subject_id),
              duration: `${s.duration || ''}`,
              _id: s._id,
              week: !levelName
                ? s.week
                  ? s.week
                  : []
                : Array.isArray(s.week) && s.week.length
                ? s.week.find((w) => w.level_no === levelName) || {}
                : {},
              slo: s.slo || [],
              ...(s._module_id !== '' && { _module_id: s._module_id }),
            };
          })
        );

        const editedAdditionalSessionFlow = fromJS(
          data?.additional_session_flow_data !== undefined ||
            data?.additional_session_flow_data !== null
            ? data?.additional_session_flow_data.map((s) => {
                return {
                  s_no: s.s_no,
                  _session_id: s._session_id,
                  delivery_type: s.delivery_type,
                  _delivery_id: s._delivery_id,
                  delivery_symbol: s.delivery_symbol,
                  delivery_no: s.delivery_no,
                  delivery_topic: s.delivery_topic,
                  subjects: s.subjects.map((s) => s._subject_id),
                  duration: `${s.duration || ''}`,
                  _id: s._id,
                  week: !levelName
                    ? s.week
                      ? s.week
                      : []
                    : Array.isArray(s.week) && s.week.length
                    ? s.week.find((w) => w.level_no === levelName) || {}
                    : {},
                  slo: s.slo || [],
                  ...(s._module_id !== '' && { _module_id: s._module_id }),
                };
              })
            : []
        );

        dispatch(
          setData(
            Map({
              editedSessionFlow,
              editedAdditionalSessionFlow,
              max_hours: data.max_hours,
            })
          )
        );
      })
      .catch((error) => dispatch(getsessionFlowFailure(error)));
  };
}

export const SAVE_SESSION_FLOW_REQUEST = 'SAVE_SESSION_FLOW_REQUEST';
export const SAVE_SESSION_FLOW_SUCCESS = 'SAVE_SESSION_FLOW_SUCCESS';
export const SAVE_SESSION_FLOW_FAILURE = 'SAVE_SESSION_FLOW_FAILURE';

const saveSessionFlowRequest = createAction(SAVE_SESSION_FLOW_REQUEST);
const saveSessionFlowSuccess = createAction(SAVE_SESSION_FLOW_SUCCESS, 'data');
const saveSessionFlowFailure = createAction(SAVE_SESSION_FLOW_FAILURE, 'error');

export function saveSessionFlow({
  operation,
  courseId,
  sessionFlowId,
  requestBody,
  url,
  history,
  isActive,
  assign,
  isVersion,
  versionType,
}) {
  return function (dispatch) {
    if (['create', 'update'].includes(operation)) {
      dispatch(saveSessionFlowRequest());
      const method = {
        create: 'post',
        ...(!isVersion
          ? { update: 'put' }
          : {
              update: versionType === 'create' ? 'post' : 'put',
            }),
      };

      // `${
      //   operation === 'delete' || (operation === 'create' && !isVersion)
      //     ? '/digi_course'
      //     : `/courseVersioning/${
      //         versionType === 'create' && isVersion
      //           ? 'createCourseVersioning'
      //           : 'editCourseDetails'
      //       }`

      // const checkVersion = isVersion
      //   ? operation === 'create' && versionType === 'create'
      //   : operation === 'create';

      const checkVersion = !isVersion
        ? `/digi_course/session_flow_${operation === 'create' ? 'add' : 'update'}${
            operation !== 'create' ? '/' + sessionFlowId : ''
          }`
        : `/digi_course/session_flow_${versionType === 'create' ? 'add' : 'update'}${
            versionType !== 'create' ? '/' + sessionFlowId : ''
          }`;

      axios[method[operation]](
        checkVersion,
        ['create', 'update'].includes(operation) ? requestBody : {}
      )
        .then((res) => {
          const data = res.data.data;
          const message = res.data.message;
          dispatch(
            saveSessionFlowSuccess({ data: data, operation, isActive, assign, message: message })
          );
          dispatch(getsessionFlow(courseId));
          if ((url && history && data.edit_status === 1) || data === 'Added successfully') {
            history.push(url);
          }
        })
        .catch((error) => dispatch(saveSessionFlowFailure(error)));
    }
  };
}

export const GET_PROGRAM_CURRICULUM_LIST_REQUEST = 'GET_PROGRAM_CURRICULUM_LIST_REQUEST';
export const GET_PROGRAM_CURRICULUM_LIST_SUCCESS = 'GET_PROGRAM_CURRICULUM_LIST_SUCCESS';
export const GET_PROGRAM_CURRICULUM_LIST_FAILURE = 'GET_PROGRAM_CURRICULUM_LIST_FAILURE';

const getProgramCurriculumListRequest = createAction(GET_PROGRAM_CURRICULUM_LIST_REQUEST);
const getProgramCurriculumListSuccess = createAction(GET_PROGRAM_CURRICULUM_LIST_SUCCESS, 'data');
const getProgramCurriculumListFailure = createAction(GET_PROGRAM_CURRICULUM_LIST_FAILURE, 'error');

export function getProgramCurriculumList() {
  return function (dispatch) {
    dispatch(getProgramCurriculumListRequest());
    axios
      .get('/digi_course/program_curriculum_year_level_list')
      .then((res) => dispatch(getProgramCurriculumListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramCurriculumListFailure(error)));
  };
}

export const GET_ASSIGNED_COURSE_REQUEST = 'GET_ASSIGNED_COURSE_REQUEST';
export const GET_ASSIGNED_COURSE_SUCCESS = 'GET_ASSIGNED_COURSE_SUCCESS';
export const GET_ASSIGNED_COURSE_FAILURE = 'GET_ASSIGNED_COURSE_FAILURE';

const getAssignedCourseRequest = createAction(GET_ASSIGNED_COURSE_REQUEST);
const getAssignedCourseSuccess = createAction(GET_ASSIGNED_COURSE_SUCCESS, 'data');
const getAssignedCourseFailure = createAction(GET_ASSIGNED_COURSE_FAILURE, 'error');

export function getAssignedCourse(courseId, assignedId) {
  return function (dispatch) {
    dispatch(getAssignedCourseRequest());
    axios
      .get(`/digi_course/get_course_assigned_details/${assignedId}/${courseId}`)
      .then((res) => {
        const data = Array.isArray(res.data.data) ? {} : res.data.data;
        dispatch(getAssignedCourseSuccess(data));
        if (Object.keys(data).length === 0) {
          return;
        }
        dispatch(
          setData(
            Map({
              editedCourse: Map({
                course_assigned_details: fromJS(data.course_assigned_details || {}),
              }),
            })
          )
        );
      })
      .catch((error) => dispatch(getAssignedCourseFailure(error)));
  };
}

export const ASSIGN_COURSE_REQUEST = 'ASSIGN_COURSE_REQUEST';
export const ASSIGN_COURSE_SUCCESS = 'ASSIGN_COURSE_SUCCESS';
export const ASSIGN_COURSE_FAILURE = 'ASSIGN_COURSE_FAILURE';

const assignCourseRequest = createAction(ASSIGN_COURSE_REQUEST);
const assignCourseSuccess = createAction(ASSIGN_COURSE_SUCCESS, 'data');
const assignCourseFailure = createAction(ASSIGN_COURSE_FAILURE, 'error');

export function assignCourse({ operation, _id, courseId, levelName, requestBody, url, history }) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(assignCourseRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      const paramOp = operation === 'update' ? '_edit' : '_delete';
      axios[method[operation]](
        `/digi_course/assign_course${operation === 'create' ? '' : paramOp}/${courseId}${
          operation !== 'create' ? '/' + _id : ''
        }`,
        ['create', 'update'].includes(operation) ? requestBody : {}
      )
        .then((res) => {
          const data = res.data.data;
          dispatch(assignCourseSuccess({ data: data, operation }));
          if (operation === 'create') {
            dispatch(
              setData(
                Map({
                  assignedCourse: data && data.responses ? fromJS(data.responses) : Map(),
                })
              )
            );
          } else if (operation === 'update') {
            dispatch(getCourse(courseId, false));
            dispatch(getAssignedCourse(courseId, _id));
            dispatch(getsessionFlow(courseId, levelName));
          } else if (operation === 'delete') {
            if (url && history) {
              history.replace(url);
            }
          }
        })
        .catch((error) => dispatch(assignCourseFailure(error)));
    }
  };
}

export const GET_ASSIGNED_COURSE_LIST_REQUEST = 'GET_ASSIGNED_COURSE_LIST_REQUEST';
export const GET_ASSIGNED_COURSE_LIST_SUCCESS = 'GET_ASSIGNED_COURSE_LIST_SUCCESS';
export const GET_ASSIGNED_COURSE_LIST_FAILURE = 'GET_ASSIGNED_COURSE_LIST_FAILURE';

const getAssignedCourseListRequest = createAction(GET_ASSIGNED_COURSE_LIST_REQUEST);
const getAssignedCourseListSuccess = createAction(GET_ASSIGNED_COURSE_LIST_SUCCESS, 'data');
const getAssignedCourseListFailure = createAction(GET_ASSIGNED_COURSE_LIST_FAILURE, 'error');

export function getAssignedCourseList(programId, curriculumId, yearId, levelId) {
  return function (dispatch) {
    dispatch(getAssignedCourseListRequest());
    axios
      .get(`/digi_course/course_assigned_list/${programId}/${curriculumId}/${yearId}/${levelId}`)
      .then((res) => dispatch(getAssignedCourseListSuccess(res.data.data)))
      .catch((error) => dispatch(getAssignedCourseListFailure(error)));
  };
}

export const GET_COURSE_RECURRING_LEVEL_LIST_REQUEST = 'GET_COURSE_RECURRING_LEVEL_LIST_REQUEST';
export const GET_COURSE_RECURRING_LEVEL_LIST_SUCCESS = 'GET_COURSE_RECURRING_LEVEL_LIST_SUCCESS';
export const GET_COURSE_RECURRING_LEVEL_LIST_FAILURE = 'GET_COURSE_RECURRING_LEVEL_LIST_FAILURE';

const getCourseRecurringLevelListRequest = createAction(GET_COURSE_RECURRING_LEVEL_LIST_REQUEST);
const getCourseRecurringLevelListSuccess = createAction(
  GET_COURSE_RECURRING_LEVEL_LIST_SUCCESS,
  'data'
);
const getCourseRecurringLevelListFailure = createAction(
  GET_COURSE_RECURRING_LEVEL_LIST_FAILURE,
  'error'
);

export function getCourseRecurringLevelList(programId, curriculumId, levelId) {
  return function (dispatch) {
    dispatch(getCourseRecurringLevelListRequest());
    axios
      .get(`/digi_course/course_list_recurring_level/${programId}/${curriculumId}/${levelId}`)
      .then((res) => dispatch(getCourseRecurringLevelListSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseRecurringLevelListFailure(error)));
  };
}

export const SAVE_COURSE_GROUP_REQUEST = 'SAVE_COURSE_GROUP_REQUEST';
export const SAVE_COURSE_GROUP_SUCCESS = 'SAVE_COURSE_GROUP_SUCCESS';
export const SAVE_COURSE_GROUP_FAILURE = 'SAVE_COURSE_GROUP_FAILURE';

const saveCourseGroupRequest = createAction(SAVE_COURSE_GROUP_REQUEST);
const saveCourseGroupSuccess = createAction(SAVE_COURSE_GROUP_SUCCESS, 'data');
const saveCourseGroupFailure = createAction(SAVE_COURSE_GROUP_FAILURE, 'error');

export function saveCourseGroup({
  operation,
  requestBody,
  programId,
  curriculumId,
  yearId,
  levelId,
}) {
  return function (dispatch) {
    if (['group', 'ungroup'].includes(operation)) {
      dispatch(saveCourseGroupRequest());
      const method = {
        group: 'post',
        ungroup: 'put',
      };
      axios[method[operation]](
        `/digi_course_group${operation === 'ungroup' ? '/ungroup' : ''}`,
        requestBody
      )
        .then((res) => {
          const data = res.data.data;
          dispatch(saveCourseGroupSuccess({ data: data, operation }));
          dispatch(getAssignedCourseList(programId, curriculumId, yearId, levelId));
          if (operation === 'group') {
            dispatch(
              setData(
                Map({
                  selectedCoursesToGroup: Map(),
                })
              )
            );
          }
        })
        .catch((error) => dispatch(saveCourseGroupFailure(error)));
    }
  };
}

export const GET_PROGRAM_INPUT_LIST_REQUEST = 'GET_PROGRAM_INPUT_LIST_REQUEST';
export const GET_PROGRAM_INPUT_LIST_SUCCESS = 'GET_PROGRAM_INPUT_LIST_SUCCESS';
export const GET_PROGRAM_INPUT_LIST_FAILURE = 'GET_PROGRAM_INPUT_LIST_FAILURE';

const getProgramInputListRequest = createAction(GET_PROGRAM_INPUT_LIST_REQUEST, 'isLoading');
const getProgramInputListSuccess = createAction(GET_PROGRAM_INPUT_LIST_SUCCESS, 'data');
const getProgramInputListFailure = createAction(GET_PROGRAM_INPUT_LIST_FAILURE, 'error');

export function getProgramInputList(id) {
  return function (dispatch) {
    dispatch(getProgramInputListRequest(true));
    axios
      .get(`/digi_program/sidebar/${id}`)
      .then((res) => dispatch(getProgramInputListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramInputListFailure(error)));
  };
}

export const SAVE_PLO_CLO_REQUEST = 'SAVE_PLO_CLO_REQUEST';
export const SAVE_PLO_CLO_SUCCESS = 'SAVE_PLO_CLO_SUCCESS';
export const SAVE_PLO_CLO_FAILURE = 'SAVE_PLO_CLO_FAILURE';

const savePloCloRequest = createAction(SAVE_PLO_CLO_REQUEST);
const savePloCloSuccess = createAction(SAVE_PLO_CLO_SUCCESS, 'data');
const savePloCloFailure = createAction(SAVE_PLO_CLO_FAILURE, 'error');

export function savePloClo({ type, operation, _id, clearEntry, ...requestBody }, label) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(savePloCloRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      let url = `/digi_mapping/${operation === 'create' ? 'add' : operation}_${type}`;
      if (operation === 'delete') {
        const { curriculum_id, course_id, domain_id } = requestBody;
        url = `${url}/${_id}/${type === 'plo' ? curriculum_id : course_id}/${domain_id}`;
      } else if (operation === 'update') {
        url = `${url}/${_id}`;
      }
      axios[method[operation]](url, ['create', 'update'].includes(operation) ? requestBody : {})
        .then((res) => {
          const data = res.data.data;
          dispatch(
            savePloCloSuccess({
              data: data,
              type,
              label,
              operation:
                operation === 'create' ? 'add' : operation === 'update' ? 'edit' : operation,
            })
          );
          if (operation !== 'delete') {
            clearEntry(_id, requestBody.domain_id, operation);
          }
          if (type === 'plo') {
            dispatch(getCurriculum(requestBody.curriculum_id));
          }
          if (type === 'clo') {
            dispatch(getCourse(requestBody.course_id, false));
          }
        })
        .catch((error) => dispatch(savePloCloFailure(error)));
    }
  };
}

export const SAVE_SLO_REQUEST = 'SAVE_SLO_REQUEST';
export const SAVE_SLO_SUCCESS = 'SAVE_SLO_SUCCESS';
export const SAVE_SLO_FAILURE = 'SAVE_SLO_FAILURE';

const saveSloRequest = createAction(SAVE_SLO_REQUEST);
const saveSloSuccess = createAction(SAVE_SLO_SUCCESS, 'data');
const saveSloFailure = createAction(SAVE_SLO_FAILURE, 'error');

export function saveSlo({ type, operation, slo_id, clearEntry, ...requestBody }, label) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(saveSloRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      if (operation !== 'create') {
        requestBody.slo_id = slo_id;
      }
      axios[method[operation]](
        '/digi_mapping/slo',
        operation !== 'delete' ? requestBody : { data: requestBody }
      )
        .then((res) => {
          dispatch(
            saveSloSuccess({
              label,
              data: res.data.data,
              operation:
                operation === 'create' ? 'add' : operation === 'update' ? 'edit' : operation,
            })
          );
          if (operation !== 'delete') {
            clearEntry(slo_id, requestBody.session_flow_data_id, operation);
          }
          dispatch(getsessionFlow(requestBody.course_id, '', false));
        })
        .catch((error) => dispatch(saveSloFailure(error)));
    }
  };
}

export const GET_FRAMEWORK_DASHBOARD_LIST_REQUEST = 'GET_FRAMEWORK_DASHBOARD_LIST_REQUEST';
export const GET_FRAMEWORK_DASHBOARD_LIST_SUCCESS = 'GET_FRAMEWORK_DASHBOARD_LIST_SUCCESS';
export const GET_FRAMEWORK_DASHBOARD_LIST_FAILURE = 'GET_FRAMEWORK_DASHBOARD_LIST_FAILURE';

const getFrameworkDashboardListRequest = createAction(GET_FRAMEWORK_DASHBOARD_LIST_REQUEST);
const getFrameworkDashboardListSuccess = createAction(GET_FRAMEWORK_DASHBOARD_LIST_SUCCESS, 'data');
const getFrameworkDashboardFailure = createAction(GET_FRAMEWORK_DASHBOARD_LIST_FAILURE, 'error');

export function getFrameworkDashboardList() {
  return function (dispatch) {
    dispatch(getFrameworkDashboardListRequest());
    axios
      .get('/digi_mapping')
      .then((res) => dispatch(getFrameworkDashboardListSuccess(res.data.data)))
      .catch((error) => dispatch(getFrameworkDashboardFailure(error)));
  };
}

export const GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_REQUEST =
  'GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_REQUEST';
export const GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_SUCCESS =
  'GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_SUCCESS';
export const GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_FAILURE =
  'GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_FAILURE';

const getFrameworkDashboardDataByProgramIdRequest = createAction(
  GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_REQUEST
);
const getFrameworkDashboardDataByProgramIdSuccess = createAction(
  GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_SUCCESS,
  'data'
);
const getFrameworkDashboardDataByProgramIdFailure = createAction(
  GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_FAILURE,
  'error'
);

export function getFrameworkDashboardDataByProgramId(programId, curriculumId) {
  return function (dispatch) {
    dispatch(getFrameworkDashboardDataByProgramIdRequest());
    axios
      .get(`/digi_mapping/program/${programId}/${curriculumId}`)
      .then((res) =>
        dispatch(
          getFrameworkDashboardDataByProgramIdSuccess(
            Array.isArray(res.data.data) ? {} : res.data.data
          )
        )
      )
      .catch((error) => dispatch(getFrameworkDashboardDataByProgramIdFailure(error)));
  };
}

export const ADD_REMOVE_FRAMEWORK_REQUEST = 'ADD_REMOVE_FRAMEWORK_REQUEST';
export const ADD_REMOVE_FRAMEWORK_SUCCESS = 'ADD_REMOVE_FRAMEWORK_SUCCESS';
export const ADD_REMOVE_FRAMEWORK_FAILURE = 'ADD_REMOVE_FRAMEWORK_FAILURE';

const addRemoveFrameworkRequest = createAction(ADD_REMOVE_FRAMEWORK_REQUEST);
const addRemoveFrameworkSuccess = createAction(ADD_REMOVE_FRAMEWORK_SUCCESS, 'data');
const addRemoveFrameworkFailure = createAction(ADD_REMOVE_FRAMEWORK_FAILURE, 'error');

export function addRemoveFramework({ operation, programId, ...requestBody }, curriculumId) {
  return function (dispatch) {
    dispatch(addRemoveFrameworkRequest());
    axios
      .post(`/digi_mapping/${operation}_framework`, requestBody)
      .then((res) => {
        dispatch(addRemoveFrameworkSuccess({ data: res.data.data, operation }));
        dispatch(getFrameworkDashboardDataByProgramId(programId, curriculumId));
      })
      .catch((error) => dispatch(addRemoveFrameworkFailure(error)));
  };
}

export const GET_MAPPING_TREE_REQUEST = 'GET_MAPPING_TREE_REQUEST';
export const GET_MAPPING_TREE_SUCCESS = 'GET_MAPPING_TREE_SUCCESS';
export const GET_MAPPING_TREE_FAILURE = 'GET_MAPPING_TREE_FAILURE';

const getMappingTreeRequest = createAction(GET_MAPPING_TREE_REQUEST);
const getMappingTreeSuccess = createAction(GET_MAPPING_TREE_SUCCESS, 'data');
const getMappingTreeFailure = createAction(GET_MAPPING_TREE_FAILURE, 'error');

export function getMappingTree(data, cb) {
  return function (dispatch) {
    dispatch(getMappingTreeRequest());
    axios
      .post(`digi_mapping/get_mapping_tree`, data)
      .then((res) => {
        dispatch(getMappingTreeSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(getMappingTreeFailure(error)));
  };
}

function filterDeletedPlosClos(mappingMatrix) {
  return mappingMatrix
    .map((m) => {
      const plos = m.getIn(['matrix', 'plos'], List()).map((plo) => {
        return plo.set(
          'plo',
          plo.get('plo').filter((p) => !p.get('isDeleted'))
        );
      });
      const clos = m.getIn(['matrix', 'clos'], List()).map((clo) => {
        return clo.set(
          'clo',
          clo.get('clo').filter((c) => !c.get('isDeleted'))
        );
      });
      return m.setIn(['matrix', 'plos'], plos).setIn(['matrix', 'clos'], clos);
    })
    .toJS();
}

export const GET_MAP_CLO_PLO_REQUEST = 'GET_MAP_CLO_PLO_REQUEST';
export const GET_MAP_CLO_PLO_SUCCESS = 'GET_MAP_CLO_PLO_SUCCESS';
export const GET_MAP_CLO_PLO_FAILURE = 'GET_MAP_CLO_PLO_FAILURE';

const MapCloPloRequest = createAction(GET_MAP_CLO_PLO_REQUEST);
const MapCloPloSuccess = createAction(GET_MAP_CLO_PLO_SUCCESS, 'data');
const MapCloPloFailure = createAction(GET_MAP_CLO_PLO_FAILURE, 'error');

export function getMapCLOPLO(data, meta, cb) {
  return function (dispatch) {
    dispatch(MapCloPloRequest());
    axios
      .post(`digi_mapping/get_mapping_matrix`, data)
      .then((res) => {
        const mappingMatrix = filterDeletedPlosClos(fromJS(res.data.data));
        dispatch(MapCloPloSuccess(mappingMatrix));
        if (meta) {
          cb &&
            cb(
              Array.isArray(mappingMatrix) && mappingMatrix.length
                ? fromJS(mappingMatrix[meta.activeTab])
                : Map(),
              meta.activeTab
            );
        } else {
          cb && cb();
        }
      })
      .catch((error) => dispatch(MapCloPloFailure(error)));
  };
}

export const UPDATE_MAP_TYPE_REQUEST = 'UPDATE_MAP_TYPE_REQUEST';
export const UPDATE_MAP_TYPE_SUCCESS = 'UPDATE_MAP_TYPE_SUCCESS';
export const UPDATE_MAP_TYPE_FAILURE = 'UPDATE_MAP_TYPE_FAILURE';

const updateMapTypeRequest = createAction(UPDATE_MAP_TYPE_REQUEST);
const updateMapTypeSuccess = createAction(UPDATE_MAP_TYPE_SUCCESS, 'data');
const updateMapTypeFailure = createAction(UPDATE_MAP_TYPE_FAILURE, 'error');

export function updateMapType({
  mapTypeFor,
  isContent = false,
  curriculumId,
  courseId,
  assignedCourseId,
  programId,
  ...requestBody
}) {
  return function (dispatch) {
    dispatch(updateMapTypeRequest());
    axios
      .put(
        `/digi_mapping/update${isContent ? '_content' : ''}_mapping_type${
          mapTypeFor === 'course' ? '_' + mapTypeFor : ''
        }/${
          mapTypeFor === 'curriculum'
            ? curriculumId
            : courseId + '/' + assignedCourseId + '/' + curriculumId
        }`,
        requestBody
      )
      .then((res) => {
        let message = '';
        if (isContent) {
          message = i18n.t('messages.content_select');
        } else {
          const mappingType = requestBody.mapping_type;
          if (mappingType) message += i18n.t('messages.map_type_seleced');
          if (!mappingType) message += i18n.t('messages.map_type_deselected');
        }
        dispatch(updateMapTypeSuccess({ data: res.data.data, message }));
        dispatch(getFrameworkDashboardDataByProgramId(programId, curriculumId));
      })
      .catch((error) => dispatch(updateMapTypeFailure(error)));
  };
}

export const GET_MAPPING_MATRIX_REQUEST = 'GET_MAPPING_MATRIX_REQUEST';
export const GET_MAPPING_MATRIX_SUCCESS = 'GET_MAPPING_MATRIX_SUCCESS';
export const GET_MAPPING_MATRIX_FAILURE = 'GET_MAPPING_MATRIX_FAILURE';

const getMappingMatrixRequest = createAction(GET_MAPPING_MATRIX_REQUEST);
const getMappingMatrixSuccess = createAction(GET_MAPPING_MATRIX_SUCCESS, 'data');
const getMappingMatrixFailure = createAction(GET_MAPPING_MATRIX_FAILURE, 'error');

export function getMappingMatrix({ activeTab = 0, setActiveTabData, courseId }) {
  return function (dispatch) {
    dispatch(getMappingMatrixRequest());
    axios
      .get(`/digi_mapping/mapping_matrix/${courseId}`)
      .then((res) => {
        const data = res.data.data;
        const filteredTabs = data.filter((d) => {
          const plos = d.matrix.plos;
          if (!plos.length) return false;
          return plos.reduce((acc, plo) => (acc += plo.plo.length), 0);
        });
        const mappingMatrix = filterDeletedPlosClos(fromJS(filteredTabs));
        dispatch(getMappingMatrixSuccess(mappingMatrix));
        setActiveTabData &&
          setActiveTabData(
            activeTab,
            Array.isArray(mappingMatrix) && mappingMatrix.length
              ? fromJS(mappingMatrix[activeTab])
              : Map()
          );
      })
      .catch((error) => dispatch(getMappingMatrixFailure(error)));
  };
}

export const SAVE_CONTENT_MAP_REQUEST = 'SAVE_CONTENT_MAP_REQUEST';
export const SAVE_CONTENT_MAP_SUCCESS = 'SAVE_CONTENT_MAP_SUCCESS';
export const SAVE_CONTENT_MAP_FAILURE = 'SAVE_CONTENT_MAP_FAILURE';

const saveContentMapRequest = createAction(SAVE_CONTENT_MAP_REQUEST);
const saveContentMapSuccess = createAction(SAVE_CONTENT_MAP_SUCCESS, 'data');
const saveContentMapFailure = createAction(SAVE_CONTENT_MAP_FAILURE, 'error');

export function saveContentMap({ activeTab, callback, ...requestBody }) {
  return function (dispatch) {
    dispatch(saveContentMapRequest());
    axios
      .post(`/digi_mapping/update_matrix_clo_plo`, requestBody)
      .then((res) => {
        dispatch(saveContentMapSuccess(res.data.data));
        callback && callback(requestBody);
      })
      .catch((error) => dispatch(saveContentMapFailure(error)));
  };
}

export const POST_UPDATE_PLO_CLO_REQUEST = 'POST_UPDATE_PLO_CLO_REQUEST';
export const POST_UPDATE_PLO_CLO_SUCCESS = 'POST_UPDATE_PLO_CLO_SUCCESS';
export const POST_UPDATE_PLO_CLO_FAILURE = 'POST_UPDATE_PLO_CLO_FAILURE';

const UpdateCloPloRequest = createAction(POST_UPDATE_PLO_CLO_REQUEST);
const UpdateCloPloSuccess = createAction(POST_UPDATE_PLO_CLO_SUCCESS, 'data');
const UpdateCloPloFailure = createAction(POST_UPDATE_PLO_CLO_FAILURE, 'error');

export function UpdateMapCLOPLO(endpoint, data, callback) {
  return function (dispatch) {
    dispatch(UpdateCloPloRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(UpdateCloPloSuccess(res.data.data));
        callback && callback(data);
      })
      .catch((error) => dispatch(UpdateCloPloFailure(error)));
  };
}

export const GET_COURSE_LIST_BY_CURRICULUM_REQUEST = 'GET_COURSE_LIST_BY_CURRICULUM_REQUEST';
export const GET_COURSE_LIST_BY_CURRICULUM_SUCCESS = 'GET_COURSE_LIST_BY_CURRICULUM_SUCCESS';
export const GET_COURSE_LIST_BY_CURRICULUM_FAILURE = 'GET_COURSE_LIST_BY_CURRICULUM_FAILURE';

const getCourseListByCurriculumIdRequest = createAction(GET_COURSE_LIST_BY_CURRICULUM_REQUEST);
const getCourseListByCurriculumIdSuccess = createAction(
  GET_COURSE_LIST_BY_CURRICULUM_SUCCESS,
  'data'
);
const getCourseListByCurriculumIdFailure = createAction(
  GET_COURSE_LIST_BY_CURRICULUM_FAILURE,
  'error'
);

export function getCourseListByCurriculumId(programId, curriculumId) {
  return function (dispatch) {
    dispatch(getCourseListByCurriculumIdRequest());
    axios
      .get(
        `/courseVersioning/programCourseList?program_id=${programId}&curriculum_id=${curriculumId}`
      )
      .then((res) => dispatch(getCourseListByCurriculumIdSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseListByCurriculumIdFailure(error)));
  };
}

export const GET_DELIVERING_SUBJECT_REQUEST = 'GET_DELIVERING_SUBJECT_REQUEST';
export const GET_DELIVERING_SUBJECT_SUCCESS = 'GET_DELIVERING_SUBJECT_SUCCESS';
export const GET_DELIVERING_SUBJECT_FAILURE = 'GET_DELIVERING_SUBJECT_FAILURE';

const getDeliveringSubjectRequest = createAction(GET_DELIVERING_SUBJECT_REQUEST);
const getDeliveringSubjectSuccess = createAction(GET_DELIVERING_SUBJECT_SUCCESS, 'data');
const getDeliveringSubjectFailure = createAction(GET_DELIVERING_SUBJECT_FAILURE, 'error');

export function getDeliveringSubject(id) {
  return function (dispatch) {
    dispatch(getDeliveringSubjectRequest());
    axios
      .get(`digi_department_subject/course_department/${id}`)
      .then((res) => dispatch(getDeliveringSubjectSuccess(res.data.data)))
      .catch((error) => dispatch(getDeliveringSubjectFailure(error)));
  };
}

export const GET_FRAMEWORK_STANDARD_SETTINGS_REQUEST = 'GET_FRAMEWORK_STANDARD_SETTINGS_REQUEST';
export const GET_FRAMEWORK_STANDARD_SETTINGS_SUCCESS = 'GET_FRAMEWORK_STANDARD_SETTINGS_SUCCESS';
export const GET_FRAMEWORK_STANDARD_SETTINGS_FAILURE = 'GET_FRAMEWORK_STANDARD_SETTINGS_FAILURE';

const getFrameworkStandardSettingsRequest = createAction(GET_FRAMEWORK_STANDARD_SETTINGS_REQUEST);
const getFrameworkStandardSettingsSuccess = createAction(
  GET_FRAMEWORK_STANDARD_SETTINGS_SUCCESS,
  'data'
);
const getFrameworkStandardSettingsFailure = createAction(
  GET_FRAMEWORK_STANDARD_SETTINGS_FAILURE,
  'error'
);

export function getFrameworkStandardSettings() {
  return function (dispatch) {
    dispatch(getFrameworkStandardSettingsRequest());
    axios
      .get('/digi_framework/filter_curriculum_year_program_standard_range_settings')
      .then((res) => dispatch(getFrameworkStandardSettingsSuccess(res.data.data)))
      .catch((error) => dispatch(getFrameworkStandardSettingsFailure(error)));
  };
}

export const SAVE_FRAMEWORK_STANDARD_SETTINGS_REQUEST = 'SAVE_FRAMEWORK_STANDARD_SETTINGS_REQUEST';
export const SAVE_FRAMEWORK_STANDARD_SETTINGS_SUCCESS = 'SAVE_FRAMEWORK_STANDARD_SETTINGS_SUCCESS';
export const SAVE_FRAMEWORK_STANDARD_SETTINGS_FAILURE = 'SAVE_FRAMEWORK_STANDARD_SETTINGS_FAILURE';

const saveFrameworkStandardSettingsRequest = createAction(SAVE_FRAMEWORK_STANDARD_SETTINGS_REQUEST);
const saveFrameworkStandardSettingsSuccess = createAction(
  SAVE_FRAMEWORK_STANDARD_SETTINGS_SUCCESS,
  'data'
);
const saveFrameworkStandardSettingsFailure = createAction(
  SAVE_FRAMEWORK_STANDARD_SETTINGS_FAILURE,
  'error'
);

export function saveFrameworkStandardSettings({ type, programId, ...requestBody }) {
  return function (dispatch) {
    dispatch(saveFrameworkStandardSettingsRequest());
    axios
      .post(`/digi_${type}/upsert_standard_range_settings`, requestBody)
      .then((res) => {
        dispatch(saveFrameworkStandardSettingsSuccess(res.data.data));
        if (!requestBody.setting_id) {
          dispatch(
            type === 'framework'
              ? getFrameworkStandardSettings()
              : getFrameworkSettingsByProgramId(programId)
          );
        }
      })
      .catch((error) => dispatch(saveFrameworkStandardSettingsFailure(error)));
  };
}

export const GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_REQUEST =
  'GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_REQUEST';
export const GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_SUCCESS =
  'GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_SUCCESS';
export const GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_FAILURE =
  'GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_FAILURE';

const getFrameworkSettingsByProgramIdRequest = createAction(
  GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_REQUEST
);
const getFrameworkSettingsByProgramIdSuccess = createAction(
  GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_SUCCESS,
  'data'
);
const getFrameworkSettingsByProgramIdFailure = createAction(
  GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_FAILURE,
  'error'
);

export function getFrameworkSettingsByProgramId(programId) {
  return function (dispatch) {
    dispatch(getFrameworkSettingsByProgramIdRequest());
    axios
      .get(`/digi_framework/curriculum_standard_range_settings/${programId}`)
      .then((res) => dispatch(getFrameworkSettingsByProgramIdSuccess(res.data.data)))
      .catch((error) => dispatch(getFrameworkSettingsByProgramIdFailure(error)));
  };
}

export const GET_FRAMEWORK_MAPPING_GRAPH_REQUEST = 'GET_FRAMEWORK_MAPPING_GRAPH_REQUEST';
export const GET_FRAMEWORK_MAPPING_GRAPH_SUCCESS = 'GET_FRAMEWORK_MAPPING_GRAPH_SUCCESS';
export const GET_FRAMEWORK_MAPPING_GRAPH_FAILURE = 'GET_FRAMEWORK_MAPPING_GRAPH_FAILURE';

const getFrameworkMappingGraphRequest = createAction(GET_FRAMEWORK_MAPPING_GRAPH_REQUEST);
const getFrameworkMappingGraphSuccess = createAction(GET_FRAMEWORK_MAPPING_GRAPH_SUCCESS, 'data');
const getFrameworkMappingGraphFailure = createAction(GET_FRAMEWORK_MAPPING_GRAPH_FAILURE, 'error');

export function getFrameworkMappingGraph(
  programId,
  {
    activeCurriculum,
    activeCurriculumFramework,
    activeViewType,
    setActiveDomainYear,
    masterGraphData,
    activeDomain,
    activeYear,
    getSortedDomainYear,
  }
) {
  return function (dispatch) {
    dispatch(getFrameworkMappingGraphRequest());
    axios
      .get(`/digi_mapping/get_curriculum/${programId}`)
      .then((res) => {
        const data = res.data.data;
        dispatch(getFrameworkMappingGraphSuccess(data));
        let updatedActiveCurriculum = Map();
        let updatedActiveCurriculumFramework = Map();
        if (activeCurriculum.get('_id')) {
          const curriculum = data.curriculums.find((c) => c._id === activeCurriculum.get('_id'));
          if (curriculum) {
            updatedActiveCurriculum = fromJS(curriculum);
          }
          if (
            updatedActiveCurriculum.get('hasFramework') === false &&
            activeCurriculumFramework.get('_id')
          ) {
            const curriculumFramework = updatedActiveCurriculum
              .get('frameworks', List())
              .find((c) => c.get('_id') === activeCurriculumFramework.get('_id'));
            if (curriculumFramework) {
              updatedActiveCurriculumFramework = curriculumFramework;
              dispatch(setData(Map({ curriculumsWithFramework: List() })));
              dispatch(
                getCurriculumWithFramework({
                  program_id: programId,
                  curriculum_id: updatedActiveCurriculum.get('_id'),
                  curriculum: curriculumFramework.toJS(),
                  frameworkCurriculumId: curriculumFramework.get('_id'),
                  viewType: activeViewType,
                  setActiveDomainYear,
                  prePopulate: true,
                  activeDomain,
                  activeYear,
                  getSortedDomainYear,
                })
              );
            }
          }
          dispatch(
            setData(
              Map({
                masterGraphData: masterGraphData.merge(
                  Map({
                    activeCurriculum: updatedActiveCurriculum,
                    activeCurriculumFramework: updatedActiveCurriculumFramework,
                  })
                ),
              })
            )
          );
        }
      })
      .catch((error) => dispatch(getFrameworkMappingGraphFailure(error)));
  };
}

export const GET_CURRICULUM_WITHOUT_FRAMEWORK_REQUEST = 'GET_CURRICULUM_WITHOUT_FRAMEWORK_REQUEST';
export const GET_CURRICULUM_WITHOUT_FRAMEWORK_SUCCESS = 'GET_CURRICULUM_WITHOUT_FRAMEWORK_SUCCESS';
export const GET_CURRICULUM_WITHOUT_FRAMEWORK_FAILURE = 'GET_CURRICULUM_WITHOUT_FRAMEWORK_FAILURE';

const getCurriculumWithoutFrameworkRequest = createAction(GET_CURRICULUM_WITHOUT_FRAMEWORK_REQUEST);
const getCurriculumWithoutFrameworkSuccess = createAction(
  GET_CURRICULUM_WITHOUT_FRAMEWORK_SUCCESS,
  'data'
);
const getCurriculumWithoutFrameworkFailure = createAction(
  GET_CURRICULUM_WITHOUT_FRAMEWORK_FAILURE,
  'error'
);

export function getCurriculumWithFramework({
  frameworkCurriculumId,
  setActiveDomainYear,
  viewType,
  prePopulate = false,
  activeDomain = Map(),
  activeYear = Map(),
  getSortedDomainYear,
  ...requestBody
}) {
  return function (dispatch) {
    dispatch(getCurriculumWithoutFrameworkRequest());
    axios
      .post('/digi_mapping/get_curriculum_without_framework', requestBody)
      .then((res) => {
        res.data.data._framework_curriculum_id = frameworkCurriculumId;
        dispatch(getCurriculumWithoutFrameworkSuccess(res.data.data));
        const curriculum = fromJS(res.data.data);
        let activeDomainYearIndex = 0;
        if (prePopulate) {
          const data = getSortedDomainYear(viewType, curriculum);
          const index = data.findIndex(
            (d) =>
              d.get('_id') ===
              (viewType === 'domain' ? activeDomain.get('_id') : activeYear.get('_id'))
          );
          activeDomainYearIndex = index === -1 ? 0 : index;
        }
        setActiveDomainYear(viewType, curriculum, activeDomainYearIndex);
      })
      .catch((error) => dispatch(getCurriculumWithoutFrameworkFailure(error)));
  };
}

export const GET_CLO_SLO_MAPPING_GRAPH_REQUEST = 'GET_CLO_SLO_MAPPING_GRAPH_REQUEST';
export const GET_CLO_SLO_MAPPING_GRAPH_SUCCESS = 'GET_CLO_SLO_MAPPING_GRAPH_SUCCESS';
export const GET_CLO_SLO_MAPPING_GRAPH_FAILURE = 'GET_CLO_SLO_MAPPING_GRAPH_FAILURE';

const getCloSloMappingGraphRequest = createAction(GET_CLO_SLO_MAPPING_GRAPH_REQUEST);
const getCloSloMappingGraphSuccess = createAction(GET_CLO_SLO_MAPPING_GRAPH_SUCCESS, 'data');
const getCloSloMappingGraphFailure = createAction(GET_CLO_SLO_MAPPING_GRAPH_FAILURE, 'error');

export function getCloSloMappingGraph(courseId) {
  return function (dispatch) {
    dispatch(getCloSloMappingGraphRequest());
    axios
      .get(`/digi_mapping/get_slo_clo_graph_data/${courseId}`)
      .then((res) => dispatch(getCloSloMappingGraphSuccess(res.data.data)))
      .catch((error) => dispatch(getCloSloMappingGraphFailure(error)));
  };
}

export const IMPORT_PROGRAM_REQUEST = 'IMPORT_PROGRAM_REQUEST';
export const IMPORT_PROGRAM_SUCCESS = 'IMPORT_PROGRAM_SUCCESS';
export const IMPORT_PROGRAM_FAILURE = 'IMPORT_PROGRAM_FAILURE';

const ImportProgramRequest = createAction(IMPORT_PROGRAM_REQUEST);
const ImportProgramSuccess = createAction(IMPORT_PROGRAM_SUCCESS, 'data');
const ImportProgramFailure = createAction(IMPORT_PROGRAM_FAILURE, 'error');

export function ImportProgramCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportProgramRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportProgramSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(ImportCsv(`digi_program/import_program`, data, cb2));
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportProgramFailure(error));
        cb2();
      });
  };
}
export const IMPORT_RECORD_REQUEST = 'IMPORT_RECORD_REQUEST';
export const IMPORT_RECORD_SUCCESS = 'IMPORT_RECORD_SUCCESS';
export const IMPORT_RECORD_FAILURE = 'IMPORT_RECORD_FAILURE';

const ImportRecordRequest = createAction(IMPORT_RECORD_REQUEST);
const ImportRecordSuccess = createAction(IMPORT_RECORD_SUCCESS, 'data');
const ImportRecordFailure = createAction(IMPORT_RECORD_FAILURE, 'error');

export function ImportCsv(endPoint, data, cb) {
  return function (dispatch) {
    dispatch(ImportRecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportRecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportRecordFailure(error));
        cb();
      });
  };
}

export const IMPORT_COURSE_REQUEST = 'IMPORT_COURSE_REQUEST';
export const IMPORT_COURSE_SUCCESS = 'IMPORT_COURSE_SUCCESS';
export const IMPORT_COURSE_FAILURE = 'IMPORT_COURSE_SUCCESS';

const ImportCourseRequest = createAction(IMPORT_COURSE_REQUEST);
const ImportCourseSuccess = createAction(IMPORT_COURSE_SUCCESS, 'data');
const ImportCourseFailure = createAction(IMPORT_COURSE_FAILURE, 'error');

export function ImportCourseCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportCourseRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportCourseSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(
            ImportCourseCsv(
              `digi_course/import_course_with_assign`,
              data,

              cb2
            )
          );
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportCourseFailure(error));
        cb2();
      });
  };
}

export const IMPORT_COURSE_RECORD_REQUEST = 'IMPORT_COURSE_RECORD_REQUEST';
export const IMPORT_COURSE_RECORD_SUCCESS = 'IMPORT_COURSE_RECORD_SUCCESS';
export const IMPORT_COURSE_RECORD_FAILURE = 'IMPORT_COURSE_RECORD_FAILURE';

const ImportCourseRecordRequest = createAction(IMPORT_COURSE_RECORD_REQUEST);
const ImportCourseRecordSuccess = createAction(IMPORT_COURSE_RECORD_SUCCESS, 'data');
const ImportCourseRecordFailure = createAction(IMPORT_COURSE_RECORD_FAILURE, 'error');

export function ImportCourseCsv(endPoint, data, cb) {
  return function (dispatch) {
    dispatch(ImportCourseRecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportCourseRecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportCourseRecordFailure(error));
        cb();
      });
  };
}

export const IMPORT_SESSION_REQUEST = 'IMPORT_SESSION_REQUEST';
export const IMPORT_SESSION_SUCCESS = 'IMPORT_SESSION_SUCCESS';
export const IMPORT_SESSION_FAILURE = 'IMPORT_SESSION_FAILURE';

const ImportSessionRequest = createAction(IMPORT_SESSION_REQUEST);
const ImportSessionSuccess = createAction(IMPORT_SESSION_SUCCESS, 'data');
const ImportSessionFailure = createAction(IMPORT_SESSION_FAILURE, 'error');

export function ImportSessionCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportSessionRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportSessionSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(ImportSessionCsv(`digi_course/import_session_flow`, data, cb2));
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportSessionFailure(error));
        cb2();
      });
  };
}
export const IMPORT_SESSION_RECORD_REQUEST = 'IMPORT_SESSION_RECORD_REQUEST';
export const IMPORT_SESSION_RECORD_SUCCESS = 'IMPORT_SESSION_RECORD_SUCCESS';
export const IMPORT_SESSION_RECORD_FAILURE = 'IMPORT_SESSION_RECORD_FAILURE';

const ImportSessionRecordRequest = createAction(IMPORT_SESSION_RECORD_REQUEST);
const ImportSessionRecordSuccess = createAction(IMPORT_SESSION_RECORD_SUCCESS, 'data');
const ImportSessionRecordFailure = createAction(IMPORT_SESSION_RECORD_FAILURE, 'error');

export function ImportSessionCsv(endPoint, data, cb) {
  return function (dispatch) {
    dispatch(ImportSessionRecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportSessionRecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportSessionRecordFailure(error));
        cb();
      });
  };
}
export const IMPORT_DEPT_REQUEST = 'IMPORT_DEPT_REQUEST';
export const IMPORT_DEPT_SUCCESS = 'IMPORT_DEPT_SUCCESS';
export const IMPORT_DEPT_FAILURE = 'IMPORT_DEPT_FAILURE';

const ImportDeptRequest = createAction(IMPORT_DEPT_REQUEST);
const ImportDeptSuccess = createAction(IMPORT_DEPT_SUCCESS, 'data');
const ImportDeptFailure = createAction(IMPORT_DEPT_FAILURE, 'error');

export function ImportDeptCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportDeptRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportDeptSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(ImportDeptCsv(`digi_department_subject/import_department_subject`, data, cb2));
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportDeptFailure(error));
        cb2();
      });
  };
}
export const IMPORT_DEPT_RECORD_REQUEST = 'IMPORT_DEPT_RECORD_REQUEST';
export const IMPORT_DEPT_RECORD_SUCCESS = 'IMPORT_DEPT_RECORD_SUCCESS';
export const IMPORT_DEPT_RECORD_FAILURE = 'IMPORT_DEPT_RECORD_FAILURE';

const ImportDeptRecordRequest = createAction(IMPORT_DEPT_RECORD_REQUEST);
const ImportDeptRecordSuccess = createAction(IMPORT_DEPT_RECORD_SUCCESS, 'data');
const ImportDeptRecordFailure = createAction(IMPORT_DEPT_RECORD_FAILURE, 'error');

export function ImportDeptCsv(endPoint, data, cb) {
  return function (dispatch) {
    dispatch(ImportDeptRecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportDeptRecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportDeptRecordFailure(error));
        cb();
      });
  };
}
export const IMPORT_SESSION_TYPE_REQUEST = 'IMPORT_SESSION_TYPE_REQUEST';
export const IMPORT_SESSION_TYPE_SUCCESS = 'IMPORT_SESSION_TYPE_SUCCESS';
export const IMPORT_SESSION_TYPE_FAILURE = 'IMPORT_SESSION_TYPE_FAILURE';

const ImportSessionTypeRequest = createAction(IMPORT_SESSION_TYPE_REQUEST);
const ImportSessionTypeSuccess = createAction(IMPORT_SESSION_TYPE_SUCCESS, 'data');
const ImportSessionTypeFailure = createAction(IMPORT_SESSION_TYPE_FAILURE, 'error');

export function ImportSessionTypeCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportSessionTypeRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportSessionTypeSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(
            ImportSessionTypeCsv(`digi_session_delivery_types/import_session_type`, data, cb2)
          );
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportSessionTypeFailure(error));
        cb2();
      });
  };
}
export const IMPORT_SESSION_TYPE_RECORD_REQUEST = 'IMPORT_SESSION_TYPE_RECORD_REQUEST';
export const IMPORT_SESSION_TYPE_RECORD_SUCCESS = 'IMPORT_SESSION_TYPE_RECORD_SUCCESS';
export const IMPORT_SESSION_TYPE_RECORD_FAILURE = 'IMPORT_SESSION_TYPE_RECORD_FAILURE';

const ImportSessionTypeRecordRequest = createAction(IMPORT_SESSION_TYPE_RECORD_REQUEST);
const ImportSessionTypeRecordSuccess = createAction(IMPORT_SESSION_TYPE_RECORD_SUCCESS, 'data');
const ImportSessionTypeRecordFailure = createAction(IMPORT_SESSION_TYPE_RECORD_FAILURE, 'error');

export function ImportSessionTypeCsv(endPoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportSessionTypeRecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportSessionTypeRecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportSessionTypeRecordFailure(error));
        cb2();
      });
  };
}
export const IMPORT_DELIVERY_TYPE_REQUEST = 'IMPORT_DELIVERY_TYPE_REQUEST';
export const IMPORT_DELIVERY_TYPE_SUCCESS = 'IMPORT_DELIVERY_TYPE_SUCCESS';
export const IMPORT_DELIVERY_TYPE_FAILURE = 'IMPORT_DELIVERY_TYPE_FAILURE';

const ImportDeliveryTypeRequest = createAction(IMPORT_DELIVERY_TYPE_REQUEST);
const ImportDeliveryTypeSuccess = createAction(IMPORT_DELIVERY_TYPE_SUCCESS, 'data');
const ImportDeliveryTypeFailure = createAction(IMPORT_DELIVERY_TYPE_FAILURE, 'error');

export function ImportDeliveryCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportDeliveryTypeRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportDeliveryTypeSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(
            ImportDeliveryCsv(`digi_session_delivery_types/import_delivery_type`, data, cb2)
          );
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportDeliveryTypeFailure(error));
        cb2();
      });
  };
}
export const IMPORT_DELIVERY_TYPE_RECORD_REQUEST = 'IMPORT_DELIVERY_TYPE_RECORD_REQUEST';
export const IMPORT_DELIVERY_TYPE_RECORD_SUCCESS = 'IMPORT_DELIVERY_TYPE_RECORD_SUCCESS';
export const IMPORT_DELIVERY_TYPE_RECORD_FAILURE = 'IMPORT_DELIVERY_TYPE_RECORD_FAILURE';

const ImportDeliveryTypeRecordRequest = createAction(IMPORT_DELIVERY_TYPE_RECORD_REQUEST);
const ImportDeliveryTypeRecordSuccess = createAction(IMPORT_DELIVERY_TYPE_RECORD_SUCCESS, 'data');
const ImportDeliveryTypeRecordFailure = createAction(IMPORT_DELIVERY_TYPE_RECORD_FAILURE, 'error');

export function ImportDeliveryCsv(endPoint, data, cb) {
  return function (dispatch) {
    dispatch(ImportDeliveryTypeRecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportDeliveryTypeRecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportDeliveryTypeRecordFailure(error));
        cb();
      });
  };
}
export const IMPORT_CURRICULUM_REQUEST = 'IMPORT_CURRICULUM_REQUEST';
export const IMPORT_CURRICULUM_SUCCESS = 'IMPORT_CURRICULUM_SUCCESS';
export const IMPORT_CURRICULUM_FAILURE = 'IMPORT_CURRICULUM_FAILURE';

const ImportCurricullumRequest = createAction(IMPORT_CURRICULUM_REQUEST);
const ImportCurricullumSuccess = createAction(IMPORT_CURRICULUM_SUCCESS, 'data');
const ImportCurricullumFailure = createAction(IMPORT_CURRICULUM_FAILURE, 'error');

export function ImportCurricullumCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportCurricullumRequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportCurricullumSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(ImportCurricullumCsv(`digi_curriculum/import_curriculum`, data, cb2));
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportCurricullumFailure(error));
        cb2();
      });
  };
}
export const IMPORT_CURRICULUM_RECORD_REQUEST = 'IMPORT_CURRICULUM_RECORD_REQUEST';
export const IMPORT_CURRICULUM_RECORD_SUCCESS = 'IMPORT_CURRICULUM_RECORD_SUCCESS';
export const IMPORT_CURRICULUM_RECORD_FAILURE = 'IMPORT_CURRICULUM_RECORD_FAILURE';

const ImportCurricullumRecordRequest = createAction(IMPORT_CURRICULUM_RECORD_REQUEST);
const ImportCurricullumRecordSuccess = createAction(IMPORT_CURRICULUM_RECORD_SUCCESS, 'data');
const ImportCurricullumRecordFailure = createAction(IMPORT_CURRICULUM_RECORD_FAILURE, 'error');

export function ImportCurricullumCsv(endPoint, data, cb) {
  return function (dispatch) {
    dispatch(ImportCurricullumRecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportCurricullumRecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportCurricullumRecordFailure(error));
        cb();
      });
  };
}

export const IMPORT_SLO_REQUEST = 'IMPORT_SLO_REQUEST';
export const IMPORT_SLO_SUCCESS = 'IMPORT_SLO_SUCCESS';
export const IMPORT_SLO_FAILURE = 'IMPORT_SLO_FAILURE';

const ImportSLORequest = createAction(IMPORT_SLO_REQUEST);
const ImportSLOSuccess = createAction(IMPORT_SLO_SUCCESS, 'data');
const ImportSLOFailure = createAction(IMPORT_SLO_FAILURE, 'error');

export function ImportSLOCsvCheck(endpoint, data, cb, cb2) {
  return function (dispatch) {
    dispatch(ImportSLORequest());
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(ImportSLOSuccess(res.data.data));
        if (res.data.data.invalid_data.length === 0) {
          dispatch(ImportSLOCsv(`digi_course/import_slo`, data, cb2));
        } else {
          cb();
        }
      })
      .catch((error) => {
        dispatch(ImportSLOFailure(error));
        cb2();
      });
  };
}

export const IMPORT_SLO_RECORD_REQUEST = 'IMPORT_SLO_RECORD_REQUEST';
export const IMPORT_SLO_RECORD_SUCCESS = 'IMPORT_SLO_RECORD_SUCCESS';
export const IMPORT_SLO_RECORD_FAILURE = 'IMPORT_SLO_RECORD_FAILURE';

const ImportSLORecordRequest = createAction(IMPORT_SLO_RECORD_REQUEST);
const ImportSLORecordSuccess = createAction(IMPORT_SLO_RECORD_SUCCESS, 'data');
const ImportSLORecordFailure = createAction(IMPORT_SLO_RECORD_FAILURE, 'error');

export function ImportSLOCsv(endPoint, data, cb) {
  return function (dispatch) {
    dispatch(ImportSLORecordRequest());
    axios
      .post(endPoint, data)
      .then((res) => {
        dispatch(ImportSLORecordSuccess(res.data.data));
        dispatch(getProgramList());
        cb();
      })
      .catch((error) => {
        dispatch(ImportSLORecordFailure(error));
        cb();
      });
  };
}

export const GET_CONFIGURE_SETTING_REQUEST = 'GET_CONFIGURE_SETTING_REQUEST';
export const GET_CONFIGURE_SETTING_SUCCESS = 'GET_CONFIGURE_SETTING_SUCCESS';
export const GET_CONFIGURE_SETTING_FAILURE = 'GET_CONFIGURE_SETTING_FAILURE';

const getConfigureSettingsRequest = createAction(GET_CONFIGURE_SETTING_REQUEST);
const getConfigureSettingsSuccess = createAction(GET_CONFIGURE_SETTING_SUCCESS, 'data');
const getConfigureSettingsFailure = createAction(GET_CONFIGURE_SETTING_FAILURE, 'error');

export function getConfigureSettings(programId, calendarId) {
  return function (dispatch) {
    dispatch(getConfigureSettingsRequest());
    axios
      .get(`/program-report-setting?_program_id=${programId}&_institution_id=${calendarId}`)
      .then((res) => dispatch(getConfigureSettingsSuccess(res.data.data)))
      .catch((error) => dispatch(getConfigureSettingsFailure(error)));
  };
}

export const SAVE_CONFIGURE_SETTINGS_REQUEST = 'SAVE_CONFIGURE_SETTINGS_REQUEST';
export const SAVE_CONFIGURE_SETTINGS_SUCCESS = 'SAVE_CONFIGURE_SETTINGS_SUCCESS';
export const SAVE_CONFIGURE_SETTINGS_FAILURE = 'SAVE_CONFIGURE_SETTINGS_FAILURE';

const saveConfigureSettingsRequest = createAction(SAVE_CONFIGURE_SETTINGS_REQUEST, 'isLoading');
const saveConfigureSettingsSuccess = createAction(SAVE_CONFIGURE_SETTINGS_SUCCESS, 'data');
const saveConfigureSettingsFailure = createAction(SAVE_CONFIGURE_SETTINGS_FAILURE, 'error');

export function saveConfigureSettings(preparedData, programId, calendarId, callBack) {
  return function (dispatch) {
    dispatch(saveConfigureSettingsRequest(true));
    axios
      .put(
        `/program-report-setting?_program_id=${programId}&_institution_id=${calendarId}`,
        preparedData
      )
      .then((res) => {
        const data = res.data.data;
        let message = prepareMessage(preparedData);
        dispatch(saveConfigureSettingsSuccess({ data, message: message }));
        callBack && callBack();
      })
      .catch((errors) => dispatch(saveConfigureSettingsFailure(errors)));
  };
}

function prepareMessage(data) {
  const { step, status, selfEvaluationSurvey, sessionExperienceSurvey, scalePointStatus } = data;
  return `${
    step === 1
      ? i18n.t('settings.response_msg.survey_types')
      : step === 2
      ? i18n.t('settings.response_msg.set_rating_scale')
      : step === 3
      ? i18n.t('settings.response_msg.set_benchmark_value')
      : selfEvaluationSurvey?.advance
      ? i18n.t('settings.response_msg.self_evaluation_survey_advance_settings')
      : sessionExperienceSurvey?.advance
      ? i18n.t('settings.response_msg.session_experience_survey_advance_settings')
      : scalePointStatus === 'saved'
      ? i18n.t('settings.response_msg.rating_scale')
      : sessionExperienceSurvey?.scalePointStatus === 'saved'
      ? i18n.t('settings.response_msg.session_experience_survey')
      : selfEvaluationSurvey?.scalePointStatus === 'saved'
      ? i18n.t('settings.response_msg.self_evaluation_survey')
      : ''
  } ${
    [
      status,
      scalePointStatus,
      sessionExperienceSurvey?.scalePointStatus,
      selfEvaluationSurvey?.scalePointStatus,
    ].includes('saved')
      ? i18n.t('settings.response_msg.saved')
      : i18n.t('settings.response_msg.drafted')
  } ${i18n.t('settings.response_msg.successfully')}.`;
}

export const POST_BENCHMARK_REQUEST = 'POST_BENCHMARK_REQUEST';
export const POST_BENCHMARK_SUCCESS = 'POST_BENCHMARK_SUCCESS';
export const POST_BENCHMARK_FAILURE = 'POST_BENCHMARK_FAILURE';

const postBenchmarkRequest = createAction(POST_BENCHMARK_REQUEST, 'isLoading');
const postBenchmarkSuccess = createAction(POST_BENCHMARK_SUCCESS, 'data');
const postBenchmarkFailure = createAction(POST_BENCHMARK_FAILURE, 'error');

export function postBenchmark(data, programId, institutionCalendarId, callBack, type) {
  return function (dispatch) {
    dispatch(postBenchmarkRequest(true));
    axios
      .put(
        `/program-report-setting?_program_id=${programId}&_institution_id=${institutionCalendarId}`,
        data
      )
      .then((res) => {
        dispatch(postBenchmarkSuccess({ data: res.data.data, message: type }));
        callBack && callBack();
      })
      .catch((errors) => dispatch(postBenchmarkFailure(errors)));
  };
}

export const POST_COURSE_SETTING_REQUEST = 'POST_COURSE_SETTING_REQUEST';
export const POST_COURSE_SETTING_SUCCESS = 'POST_COURSE_SETTING_SUCCESS';
export const POST_COURSE_SETTING_FAILURE = 'POST_COURSE_SETTING_FAILURE';

const postCourseSettingRequest = createAction(POST_COURSE_SETTING_REQUEST, 'isLoading');
const postCourseSettingSuccess = createAction(POST_COURSE_SETTING_SUCCESS, 'data');
const postCourseSettingFailure = createAction(POST_COURSE_SETTING_FAILURE, 'error');

export function postCourseSetting(data, programId, institutionCalendarId, callBack) {
  return function (dispatch) {
    dispatch(postCourseSettingRequest(true));
    axios
      .put(
        `/program-report-setting/course?_program_id=${programId}&_institution_id=${institutionCalendarId}`,
        data
      )
      .then((res) => {
        dispatch(postCourseSettingSuccess({ data: res.data.data }));
        callBack && callBack();
      })
      .catch((errors) => dispatch(postCourseSettingFailure(errors)));
  };
}

export const GET_SESSION_ORDER_MODULE_REQUEST = 'GET_SESSION_ORDER_MODULE_REQUEST';
export const GET_SESSION_ORDER_MODULE_SUCCESS = 'GET_SESSION_ORDER_MODULE_SUCCESS';
export const GET_SESSION_ORDER_MODULE_FAILURE = 'GET_SESSION_ORDER_MODULE_FAILURE';

const getSessionOrderModulesRequest = createAction(GET_SESSION_ORDER_MODULE_REQUEST, 'isLoading');
const getSessionOrderModulesSuccess = createAction(GET_SESSION_ORDER_MODULE_SUCCESS, 'data');
const getSessionOrderModulesFailure = createAction(GET_SESSION_ORDER_MODULE_FAILURE, 'error');

export function getSessionOrderModules({ programId, courseId }) {
  return function (dispatch) {
    dispatch(getSessionOrderModulesRequest(true));
    axios
      .get(`/digi_course/sessionOrderModules/${programId}/${courseId}`)
      .then((res) => dispatch(getSessionOrderModulesSuccess(res.data.data)))
      .catch((error) => dispatch(getSessionOrderModulesFailure(error)));
  };
}

export const SAVE_SESSION_ORDER_MODULE_REQUEST = 'SAVE_SESSION_ORDER_MODULE_REQUEST';
export const SAVE_SESSION_ORDER_MODULE_SUCCESS = 'SAVE_SESSION_ORDER_MODULE_SUCCESS';
export const SAVE_SESSION_ORDER_MODULE_FAILURE = 'SAVE_SESSION_ORDER_MODULE_FAILURE';

const saveSessionOrderModulesRequest = createAction(SAVE_SESSION_ORDER_MODULE_REQUEST);
const saveSessionOrderModulesSuccess = createAction(SAVE_SESSION_ORDER_MODULE_SUCCESS, 'data');
const saveSessionOrderModulesFailure = createAction(SAVE_SESSION_ORDER_MODULE_FAILURE, 'error');

export function saveSessionOrderModules({ requestBody, type, id, callBack }) {
  return function (dispatch) {
    dispatch(saveSessionOrderModulesRequest());

    const method = {
      create: 'post',
      update: 'put',
      delete: 'delete',
    };

    axios[method[type]](
      `/digi_course/sessionOrderModules${type !== 'create' ? '/' + id : ''}`,
      ['create', 'update'].includes(type) ? requestBody : {}
    )
      .then((res) => {
        const message = `${jsUcfirst(type)}d Successfully`;
        dispatch(saveSessionOrderModulesSuccess(message));
        dispatch(
          getSessionOrderModules({
            programId: requestBody._program_id,
            courseId: requestBody._course_id,
          })
        );
        callBack && callBack();
      })
      .catch((error) => dispatch(saveSessionOrderModulesFailure(error)));
  };
}

export const CHECK_DUPLICATE_VERSION_REQUEST = 'CHECK_DUPLICATE_VERSION_REQUEST';
export const CHECK_DUPLICATE_VERSION_SUCCESS = 'CHECK_DUPLICATE_VERSION_SUCCESS';
export const CHECK_DUPLICATE_VERSION_FAILURE = 'CHECK_DUPLICATE_VERSION_FAILURE';

const checkDuplicateVersionRequest = createAction(CHECK_DUPLICATE_VERSION_REQUEST);
const checkDuplicateVersionSuccess = createAction(CHECK_DUPLICATE_VERSION_SUCCESS, 'data');
const checkDuplicateVersionFailure = createAction(CHECK_DUPLICATE_VERSION_FAILURE, 'error');

export function checkDuplicateVersion({ params, callBack }) {
  return function (dispatch) {
    dispatch(checkDuplicateVersionRequest());

    axios
      .get(`courseVersioning/checkDuplicateVersionName`, { params })
      .then((res) => {
        const isDuplicateVersionName = res.data.data.isDuplicateVersionName;
        const message = isDuplicateVersionName ? res.data.message : '';
        dispatch(checkDuplicateVersionSuccess(message));
        if (!isDuplicateVersionName) {
          callBack && callBack();
        }
      })
      .catch((error) => dispatch(checkDuplicateVersionFailure(error)));
  };
}

export const VIEW_COURSE_DETAIL_REQUEST = 'VIEW_COURSE_DETAIL_REQUEST';
export const VIEW_COURSE_DETAIL_SUCCESS = 'VIEW_COURSE_DETAIL_SUCCESS';
export const VIEW_COURSE_DETAIL_FAILURE = 'VIEW_COURSE_DETAIL_FAILURE';

const viewCourseDetailRequest = createAction(VIEW_COURSE_DETAIL_REQUEST);
const viewCourseDetailSuccess = createAction(VIEW_COURSE_DETAIL_SUCCESS, 'data');
const viewCourseDetailFailure = createAction(VIEW_COURSE_DETAIL_FAILURE, 'error');

export function viewCourseDetail({ courseId }) {
  return function (dispatch) {
    dispatch(viewCourseDetailRequest());

    axios
      .get(`courseVersioning/viewCourseDetails/${courseId}`)
      .then((res) => {
        dispatch(viewCourseDetailSuccess(res.data.data));
      })
      .catch((error) => dispatch(viewCourseDetailFailure(error)));
  };
}

export const DELETE_VERSION_COURSE_REQUEST = 'DELETE_VERSION_COURSE_REQUEST';
export const DELETE_VERSION_COURSE_SUCCESS = 'DELETE_VERSION_COURSE_SUCCESS';
export const DELETE_VERSION_COURSE_FAILURE = 'DELETE_VERSION_COURSE_FAILURE';

const deleteVersionCourseRequest = createAction(DELETE_VERSION_COURSE_REQUEST, 'isLoading');
const deleteVersionCourseSuccess = createAction(DELETE_VERSION_COURSE_SUCCESS, 'data', 'message');
const deleteVersionCourseFailure = createAction(DELETE_VERSION_COURSE_FAILURE, 'error');

export const deleteVersionCourse = ({ params, programId, curriculumId }) => {
  return function (dispatch) {
    dispatch(deleteVersionCourseRequest(true));
    axios
      .delete(`courseVersioning/deleteCourse`, { params })
      .then((res) => {
        dispatch(deleteVersionCourseSuccess());
        dispatch(getCourseListByCurriculumId(programId, curriculumId));
      })
      .catch((error) => dispatch(deleteVersionCourseFailure(error)));
  };
};

export const UPLOAD_SESSION_DOC_REQUEST = 'UPLOAD_SESSION_DOC_REQUEST';
export const UPLOAD_SESSION_DOC_SUCCESS = 'UPLOAD_SESSION_DOC_SUCCESS';
export const UPLOAD_SESSION_DOC_FAILURE = 'UPLOAD_SESSION_DOC_FAILURE';

const uploadSessionDocRequest = createAction(UPLOAD_SESSION_DOC_REQUEST);
const uploadSessionDocSuccess = createAction(UPLOAD_SESSION_DOC_SUCCESS);
const uploadSessionDocFailure = createAction(UPLOAD_SESSION_DOC_FAILURE, 'error');

export const uploadSessionDoc = (file, options, successCallBack, failureCallBack) => {
  return (dispatch) => {
    dispatch(uploadSessionDocRequest());
    axios
      .post(`/digi_course/uploadSessionDocument`, file, options)
      .then((res) => {
        dispatch(uploadSessionDocSuccess());
        successCallBack && successCallBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(uploadSessionDocFailure(error));
        failureCallBack && failureCallBack();
      });
  };
};

export const GENERATE_SIGNED_URL_REQUEST = 'GENERATE_SIGNED_URL_REQUEST';
export const GENERATE_SIGNED_URL_SUCCESS = 'GENERATE_SIGNED_URL_SUCCESS';
export const GENERATE_SIGNED_URL_FAILURE = 'GENERATE_SIGNED_URL_FAILURE';

const generateSignedUrlRequest = createAction(GENERATE_SIGNED_URL_REQUEST);
const generateSignedUrlSuccess = createAction(GENERATE_SIGNED_URL_SUCCESS, 'data');
const generateSignedUrlFailure = createAction(GENERATE_SIGNED_URL_FAILURE, 'error');

export function generateSignedUrl(url, cb) {
  return function (dispatch) {
    dispatch(generateSignedUrlRequest());

    axios
      .get(`digi_course/generateSignedUrl?url=${url}`)
      .then((res) => {
        dispatch(generateSignedUrlSuccess(res.data.data));
        cb && cb(res.data.data);
      })
      .catch((error) => dispatch(generateSignedUrlFailure(error)));
  };
}
