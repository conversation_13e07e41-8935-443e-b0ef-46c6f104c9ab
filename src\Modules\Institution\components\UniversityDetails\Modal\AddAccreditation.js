import React, { useState } from 'react';
import { Modal } from 'react-bootstrap';
import { Trans, useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';

function AddAccreditation({
  show,
  handleAccreditationClose,
  setAccreditationName,
  textName,
  optionsAccreditationType,
}) {
  const { t } = useTranslation();
  const [name, setName] = useState('');

  const disableSave = () => {
    if (name === '' || optionsAccreditationType?.some((item) => item.label === name)) return true;
    else return false;
  };

  return (
    <Modal show={show} dialogClassName={`model-600 d-flex`} centered>
      {/* cancelShow ? display-none  */}
      <Modal.Header className="border-none pb-0">
        <Modal.Title className="f-20">
          <Trans
            i18nKey={'add_colleges.Add_Accreditation_Type'}
            values={{ type: Object.keys(textName).length === 0 ? 'Add' : 'Edit' }}
          ></Trans>
        </Modal.Title>
      </Modal.Header>

      <Modal.Body className="p-0 rounded mt-3 mx-3 mb-0">
        <div className="col-md-12 p-0">
          <div>
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              value={name === '' ? textName.value : name}
              name="courseName"
              changed={(e) => setName(e.target.value)}
              placeholder={t('leaveManagement.typeName')}
              fullWidth
              label={<Trans i18nKey={'add_colleges.Accreditation_Type'}></Trans>}
              labelclass={'f-14 pt-1'}
            />
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton
          className="mr-3"
          color="inherit"
          variant="outlined"
          clicked={() => {
            handleAccreditationClose();
            setName('');
          }}
        >
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>

        <MButton
          clicked={() => {
            setAccreditationName(name, textName);
            setName('');
          }}
          disabled={disableSave()}
        >
          <Trans i18nKey={'add_colleges.save'}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

AddAccreditation.propTypes = {
  show: PropTypes.bool,
  portfolioName: PropTypes.string,
  previousImg: PropTypes.string,
  textName: PropTypes.object,
  setAccreditationName: PropTypes.func,
  handleAccreditationClose: PropTypes.func,
  optionsAccreditationType: PropTypes.object,
};
export default AddAccreditation;
