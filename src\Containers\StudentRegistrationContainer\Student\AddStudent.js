import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Input from '../../../Widgets/FormElements/Input/Input';
import Loader from '../../../Widgets/Loader/Loader';
import { Button } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import { data, gender, validation } from './data';
import axios from '../../../axios';
import Breadcrumb from '../../../Widgets/Breadcrumb/Breadcrumb';
import {
  getLang,
  jsUcfirstAll,
  isIndVer,
  isModuleEnabled,
  lastNameRequired,
  addDefaultLastName,
} from '../../../utils';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
class AddStudent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ...data,
      selectGender: gender[0][1],
      selectedBatch: 'select',
      programData: [],
      selectedProgram: '',
      isLoading: false,
      programList: [],
      email: '',
    };
  }

  componentDidMount() {
    this.fetchApi();
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });
    let field = { field: ['name', 'no'] };

    axios
      .get(`/digi_program?limit=200&pageNo=1`, field)
      .then((res) => {
        const data =
          res.data.data &&
          res.data.data.length > 0 &&
          res.data.data
            .filter((item) => item.isDeleted === false && item.isActive === true)
            .map((data) => {
              return {
                name: data.name !== '' ? jsUcfirstAll(data.name) : '',
                value: data.code,
              };
            });
        data.unshift({
          name: 'Select Program',
          value: 'select',
        });
        this.setState({
          programData: data,
          selectedProgram: data[0].value,
          isLoading: false,
          programList: res.data.data && res.data.data.length > 0 ? res.data.data : [],
        });
      })
      .catch((ex) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  handleChangeText = (e, name) => {
    if (name === 'fName') {
      this.setState({
        fName: e.target.value,
        fNameError: '',
      });
    }
    if (name === 'mName') {
      this.setState({
        mName: e.target.value,
        mNameError: '',
      });
    }
    if (name === 'lName') {
      this.setState({
        lName: e.target.value,
        lNameError: '',
      });
    }
    if (name === 'userName') {
      this.setState({
        userName: e.target.value,
        userNameError: '',
      });
    }
    if (name === 'academicNo') {
      this.setState({
        academicNo: e.target.value,
        academicNoError: '',
      });
    }
    if (name === 'nationalId') {
      this.setState({
        nationalId: e.target.value,
        nationalIdError: '',
      });
    }
    if (name === 'email') {
      this.setState({
        email: e.target.value,
        emailError: '',
      });
    }
  };

  handleSelect = (e, name) => {
    e.preventDefault();
    if (name === 'batch') {
      this.setState({
        selectedBatch: e.target.value,
        selectedBatchError: '',
      });
    }
    if (name === 'program') {
      this.setState({
        selectedProgram: e.target.value,
        selectedProgramError: '',
        selectedBatch: 'select',
      });
    }
  };

  async handleSingleSubmit(e) {
    e.preventDefault();
    if (validation.call(this)) {
      const studentProfile = {
        first_name: this.state.fName,
        last_name: addDefaultLastName(this.state.lName),
        middle_name: this.state.mName,
        family_name: this.state.userName,
        gender: this.state.selectGender,
        academic_no: this.state.academicNo,
        email: this.state.email,
        program_no: this.state.selectedProgram,
        batch: this.state.selectedBatch,
        nationality_id: this.state.nationalId,
        user_type: 'student',
      };
      // const data = {
      //   user_type: "student",
      //   data: [studentProfile],
      // };
      this.setState({
        isLoading: true,
      });

      axios
        .post(`user/single_insert`, studentProfile)
        .then((res) => {
          if (res.data.status_code === 201) {
            NotificationManager.success(t('user_management.student_added_successfully'));
            this.setState({
              isLoading: false,
            });
            this.handleGoBack();
          } else {
            NotificationManager.error(res.data.data);
            this.setState({
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          NotificationManager.error(`${error.response.data.data}`);
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  }

  onRadioGroupChange = (e, name) => {
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
        selectGenderError: '',
      });
    }
  };

  handleGoBack = () => {
    this.props.history.push({
      pathname: '/student/management',
      state: {
        completeView: false,
        pendingView: true,
        inactiveView: false,
      },
    });
  };

  getBatch = () => {
    const { programList, selectedProgram } = this.state;
    if (programList && programList.length > 0 && selectedProgram !== 'select') {
      const terms = programList
        .filter((item) => item.code === selectedProgram)
        .reduce((_, el) => el.term, [])
        .map((item) => {
          return { name: item.term_name, value: item.term_name };
        });

      terms.unshift({
        name: `Select ${isIndVer() ? 'Intake' : 'Batch'}`,
        value: 'select',
      });

      return terms;
    }
    return [];
  };

  render() {
    gender[0][0] = t('infra_management.gender.Male');
    gender[1][0] = t('infra_management.gender.Female');
    const items = [
      { to: '/student/management', label: t('side_nav.menus.student_management') },
      { to: '/student/profile/add', label: t('user_management.add_new_student') },
    ];
    const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
    const userSensitiveData = isModuleEnabled('USER_SENSITIVE');
    return (
      <React.Fragment>
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <div className="main pt-5 pb-5">
          <Loader isLoading={this.state.isLoading} />

          <div className="container">
            <div className="float-left pt-1 pb-3">
              {/* <Link to="/student/management">← Back to Student List</Link> */}
            </div>
            <div className="float-left white p-2">
              <div className="row w-100">
                <div className="col-md-6 pt-1 d-flex">
                  {' '}
                  <Trans i18nKey={'user_management.Personal_Details'} />
                </div>
                <div className="col-md-6">
                  <div className="float-right">
                    <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                      <Trans i18nKey={'back_cc'} />
                    </Button>

                    <Button tabindex="11" onClick={(e) => this.handleSingleSubmit(e)}>
                      <Trans i18nKey={'events.submit'} />{' '}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="row">
                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.fName}
                    floatingLabel={t('first_name')}
                    changed={(e) => this.handleChangeText(e, 'fName')}
                    feedback={this.state.fNameError}
                    tabindex={'1'}
                  />
                </div>
                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.mName}
                    floatingLabel={t('middle_name')}
                    changed={(e) => this.handleChangeText(e, 'mName')}
                    feedback={this.state.mNameError}
                    tabindex={'2'}
                  />
                </div>

                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.lName}
                    floatingLabel={!lastNameRequired() ? 'Last Name' : t('last_name')}
                    changed={(e) => this.handleChangeText(e, 'lName')}
                    feedback={this.state.lNameError}
                    tabindex={'3'}
                  />
                </div>

                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.userName}
                    floatingLabel={t('user_management.Family_Name_Optional')}
                    changed={(e) => this.handleChangeText(e, 'userName')}
                    tabindex={'4'}
                  />
                </div>

                <div className={`col-md-12 pt-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label>
                    <Trans i18nKey={'gender'} />
                  </label>
                  <Input
                    elementType={'radio'}
                    elementConfig={gender}
                    className={'form-radio1'}
                    selected={this.state.selectGender}
                    labelclass="radio-label2"
                    onChange={(e) => this.onRadioGroupChange(e, 'selectGender')}
                    feedback={this.state.selectGenderError}
                    tabindex={'5'}
                  />
                </div>
                <div className="col-md-12">
                  <div className="row">
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatinginput'}
                        elementConfig={{
                          type: 'text',
                        }}
                        maxLength={40}
                        value={this.state.email}
                        floatingLabel={t('emailId')}
                        changed={(e) => this.handleChangeText(e, 'email')}
                        feedback={this.state.emailError}
                        tabindex={'6'}
                      />
                    </div>
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatinginput'}
                        elementConfig={{
                          type: 'text',
                        }}
                        maxLength={25}
                        value={this.state.academicNo}
                        floatingLabel={t('academic_no')}
                        changed={(e) => this.handleChangeText(e, 'academicNo')}
                        feedback={this.state.academicNoError}
                        tabindex={'7'}
                      />
                    </div>
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatingselect'}
                        elementConfig={{
                          options: this.state.programData,
                        }}
                        value={this.state.selectedProgram}
                        floatingLabel={t('program_name')}
                        className={'customize_dropdown'}
                        changed={(e) => this.handleSelect(e, 'program')}
                        feedback={this.state.selectedProgramError}
                        tabindex={'9'}
                      />
                    </div>
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatingselect'}
                        elementConfig={{
                          options: this.getBatch(),
                        }}
                        value={this.state.selectedBatch}
                        className={'customize_dropdown'}
                        floatingLabel={t('user_management.batch', {
                          Batch: isIndVer() ? 'Intake' : 'Batch',
                        })}
                        changed={(e) => this.handleSelect(e, 'batch')}
                        feedback={this.state.selectedBatchError}
                        tabindex={'8'}
                      />
                    </div>
                  </div>
                </div>
                {userSensitiveData && (
                  <div className="col-md-12">
                    <div className="row">
                      <div className="col-md-3 pt-2">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.nationalId}
                          floatingLabel={t('national/residence', {
                            optional: !hasNationalIdValidation ? '(Optional)' : '',
                          })}
                          changed={(e) => this.handleChangeText(e, 'nationalId')}
                          feedback={this.state.nationalIdError}
                          tabindex={'10'}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

AddStudent.propTypes = {
  history: PropTypes.object,
};

export default withRouter(AddStudent);
