import React from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import InfoImage from 'Assets/info.svg';
import Tooltips from 'Widgets/FormElements/material/Tooltip';

export default function StudentLateAbsentBody({ lateConfig }) {
  return (
    <Tooltips
      title={
        <div>
          {/* <div className="white_capital Gray_Neutral_110 p-2 pl-3">Late Config</div> */}
          <table className="p-2">
            <thead>
              <th className="text_no_wrap">Label Name</th>
              <th className="text_no_wrap px-2">No of Absent * No of Late</th>
              <th className="text_no_wrap ">Student Absent</th>
            </thead>
            <tbody className="Gray_Neutral_100 ">
              {lateConfig.map((item, i) => (
                <tr className="white_border_bottom last_child_border_remove" key={i}>
                  <td className="table_hash_gray text-capitalize ">{item.get('labelName', '')}</td>
                  <td className="table_hash_gray text-center">
                    {item.get('noOfAbsent', '') + ' * ' + item.get('noOfLate')}
                  </td>
                  <td className="table_hash_gray">{item.get('studentAbsent', '')}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      }
    >
      <img className="pl-2 remove_hover" src={InfoImage} alt="info" />
    </Tooltips>
  );
}
StudentLateAbsentBody.propTypes = {
  lateConfig: PropTypes.instanceOf(List),
};
