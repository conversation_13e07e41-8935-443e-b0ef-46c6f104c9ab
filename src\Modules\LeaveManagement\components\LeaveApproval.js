import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { t } from 'i18next';
import { Map, List } from 'immutable';
import { Accordion, Card, Table, Button } from 'react-bootstrap';
import * as actions from '../../../_reduxapi/leave_management/actions';

import {
  selectLeaveCategories,
  selectRolesList,
  selectStaffList,
  selectApproverList,
  selectEditApproverList,
  selectEdited,
} from '../../../_reduxapi/leave_management/selectors';
import Input from '../../../Widgets/FormElements/Input/Input';
import StaffModal from '../modal/StaffModal';
import AlertModal from '../../InfrastructureManagement/modal/AlertModal';
import { jsUcfirst, getLang } from '../../../utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
const lang = getLang();

class LeaveApproval extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      arrow: false,
      arrow6: false,
      searchText: '',
      categoryModal: {
        show: false,
      },
      modalData: {
        show: false,
      },
      searchShow: false,
      studentId: '',
      year: [
        { name: '', value: '' },
        { name: '1', value: '1' },
        { name: '2', value: '2' },
      ],
      role: [],
      Approveassociation: '',
      selectedrole: null,
      selectrolename: null,
      approvename: '',
      name: '',
      selectedId: 0,
      staffType: null,
      selectedValues: [],
      options: [
        { name: '', value: '' },
        // { name: 'All Staff', value: 'both' },
        { name: t('leaveManagement.academicStaff'), value: 'academic' },
        { name: t('leaveManagement.administrativeStaff'), value: 'admin' },
      ],
      searchView1: false,
      searchText1: '',
      selectedId1: 0,
      name1: '',
      id: '',
      searchTerm: '',
      staffeditType: null,
    };
  }

  componentDidMount() {
    const { getRolesList, getAllStaff, getApproverList } = this.props;
    getApproverList();
    getRolesList();
    getAllStaff('completed');
  }

  static getDerivedStateFromProps(props, state) {
    const institutionrole = props.roleList
      ?.map((item) => {
        return {
          name: item.get('name'),
          value: item.get('_id'),
        };
      })
      .toJS();

    institutionrole.unshift({
      name: '',
      value: '',
    });
    return {
      role: institutionrole,
      selectedrole: state.selectedrole ? state.selectedrole : institutionrole[0]?.value,
      selectrolename: state.selectrolename ? state.selectrolename : institutionrole[0]?.name,
    };
  }
  Approveassociation = [
    {
      name: '',
      value: '',
    },
    {
      name: t('leaveManagement.applicantsDept'),
      value: 'Applicants Dept/Prog',
    },
    {
      name: t('leaveManagement.others'),
      value: 'Others',
    },
  ];
  changeHandler1 = (value, id, name, staffid) => {
    if (value) {
      this.setState({
        selectedId1: id,
        name1: name,
        selectStaffId: staffid,
      });
    }
  };

  changeHandler = (value, id, name, staffid) => {
    if (value) {
      this.setState({
        selectedId: id,
        name: name,
        selectStaffId: staffid,
      });
    }
  };

  handleRemoveAttachment() {
    this.setState({
      selectedAttachment: null,
    });
  }

  removeicon = () => {
    this.setState({
      name: '',
      removalIcon: false,
      selectedId: 0,
      searchText: '',
    });
  };

  modalOpen = () => {
    this.setState({
      searchView: !this.state.searchView,
      removalIcon: true,
      searchText: '',
      selectedId: 0,
    });
    this.state.searchText === '' && this.props.getAllStaff('completed');
  };

  modalClose = () => {
    this.setState({
      searchView: !this.state.searchView,
      name: '',
      removalIcon: false,
      selectedId: 0,
      searchText: '',
    });
  };

  removeicon1 = () => {
    this.setState({
      name1: '',
      removalIcon1: false,
      selectedId1: 0,
      searchText1: '',
    });
  };

  modalOpen1 = (id) => {
    this.setState({
      searchView1: !this.state.searchView1,
      removalIcon1: true,
      searchText1: '',
      id: id,
    });
    this.state.searchText1 === '' && this.props.getAllStaff('completed');
  };

  modalClose1 = () => {
    this.setState({
      searchView1: !this.state.searchView1,
      name1: '',
      removalIcon1: false,
      selectedId1: 0,
      searchText1: '',
    });
  };

  handleCancelClick(row, operation) {
    const id = row.get('_id');
    const { editApproverList } = this.props;
    this.props.setData(
      Map({
        ...(operation === 'update' && { editedApproverList: editApproverList.delete(id) }),
      })
    );
  }

  handleSelect = (e, name, operation, id, role) => {
    const { editApproverList } = this.props;
    const value = e.target.value;
    if (role) {
      var role_name = role.filter((data) => data.value === value);
      this.setState({
        selectrolename: role_name[0].name,
      });
    }

    this.props.setData(
      Map({
        ...(operation === 'update' && {
          editedApproverList: editApproverList.setIn([id, name], value),
        }),
        ...(operation === 'update' &&
          ['role'].includes(name) && {
            editedApproverList: editApproverList
              .setIn([id, name, '_roles_id'], value)
              .setIn([id, name, 'role_name'], role_name[0].name),
          }),
      })
    );

    if (operation !== 'update') {
      const rolename = this.state.role.filter((data) => data.value === e.target.value);
      if (name === 'role') {
        this.setState({
          selectedrole: e.target.value,
          selectrolename: rolename[0].name,
        });
      }
      if (name === 'approveassociation') {
        this.props.setData(
          Map({
            ...(operation === 'update' && {
              editedApproverList: editApproverList.setIn([id, name], value),
            }),
          })
        );
        if (e.target.value !== 'Others') {
          this.setState({
            name: '',
          });
        }
        this.setState({
          Approveassociation: e.target.value,
        });
      }
      if (name === 'approvename') {
        this.setState({
          approvename: e.target.value,
        });
      }
      if (name === 'staff_type') {
        this.props.setData(
          Map({
            ...(operation === 'update' && {
              editedApproverList: editApproverList.setIn([id, name], value),
            }),
          })
        );

        this.setState({
          staffType: e.target.value,
        });
      }
    }
  };

  handleSearch = (e) => {
    this.setState(
      {
        searchText: e.target.value,
      },
      () => {
        this.props.getAllStaff('completed', this.state.searchText);
      }
    );
  };

  handleSearch1 = (e) => {
    this.setState(
      {
        searchText1: e.target.value,
      },
      () => {
        this.props.getAllStaff('completed', this.state.searchText1);
      }
    );
  };

  iconPostion6 = () => {
    this.setState({
      arrow6: !this.state.arrow6,
    });
  };

  onSelect(selectedList, selectedItem) {
    const selectedvalue = [];
    for (let i = 0; i < selectedList.length; i++) {
      selectedvalue.push(selectedList[i].value);
    }
    this.setState({ selectedValues: selectedvalue });
  }

  handleEditClick(row) {
    const { editApproverList } = this.props;
    this.props.setData(
      Map({
        editedApproverList: editApproverList.set(row.get('_id'), row),
      })
    );
    this.setState({ selectStaffId: row.get('_staff_id', ''), name1: row.get('staff_name', '') });
  }

  add = () => {
    const item = [];
    item.push(this.state.staffType);
    const requestbody = {
      staff_type: item,
      staff_name: this.state.name,
      approver_level_name: this.state.approvename,
      approver_association: this.state.Approveassociation,
      _roles_id: this.state.selectedrole,
      role_name: this.state.selectrolename,
      _staff_id: this.state.selectStaffId,
    };

    this.props.addapprover(requestbody);

    this.setState({
      staffType: '',
      name: '',
      approvename: '',
      Approveassociation: '',
      selectedrole: '',
      selectrolename: '',
      role: [],
      role_name: '',
      selectStaffId: '',
    });
  };

  handleSaveClick = (id, operation, confirm, datas) => {
    const selectedvalue = [];
    for (let i = 0; i < this.state.selectedValues.length; i++) {
      selectedvalue.push(this.state.selectedValues[i].name);
    }
    if (operation === 'delete') {
      this.props.deleteapprover(id);
    }
    const { editApproverList } = this.props;
    const data = editApproverList.get(id);
    if (operation !== 'delete') {
      const item = [];
      item.push(data.get('staff_type', ''));
      const requestBody = {
        _id: id,
        ...(operation !== 'delete' && {
          staff_type: data.get('staff_type'),
          approver_level_name: data.get('approver_level_name'),
          approver_association: data.get('approver_association'),
          staff_name: data.get('approver_association') === 'Others' ? this.state.name1 : '',
          ...(data.get('approver_association') === 'Others' && {
            _staff_id: this.state.selectStaffId,
          }),
          ...(data.get('approver_association') !== 'Others' && {
            _roles_id: data.getIn(['role', '_roles_id'], ''),
            role_name:
              data.get('approver_association') !== 'Others'
                ? data.getIn(['role', 'role_name'], '')
                : '',
          }),
        }),
      };
      if (data.get('approver_association') === 'Others' && !this.state.name1) {
        let message = `Staff Name is Required `;
        this.setModalData({
          show: true,
          title: 'Alert: Validation error',
          description: message,
          variant: 'alert',
          cancelButtonLabel: 'OK',
        });
        return;
      }

      this.props.addapprover(requestBody, id, () => {
        this.props.setData(
          Map({
            ...(operation === 'update' && { editedApproverList: editApproverList.delete(id) }),
          })
        );
        this.setState({
          selectStaffId: '',
          selectedId: '',
          name: '',
          selectedId1: '',
          name1: '',
        });
      });
    }
  };

  handledeleteClick(data) {
    this.setModalData({
      show: true,
      title: t('confirm_delete'),
      description: t('leaveManagement.sureDelete'),
      variant: 'confirm',
      cancelButtonLabel: t('cancel_upper'),
      data: data,
    });
    return;
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
      },
      studentId: data,
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm(id) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.handleSaveClick(id, 'delete')
    );
  }

  handleChangeSearch = (e, name) => {
    this.setState({
      searchTerm: e.target.value,
    });
    if (name === 'searchTermOne') {
      this.setState({
        searchTermOne: e.target.value,
      });
    }
  };

  render() {
    const {
      modalData,
      studentId,
      approvename,
      selectedrole,
      options,
      name,
      searchTerm,
      staffType,
    } = this.state;
    const item = [];
    item.push({ name: '', value: '' });
    const { editApproverList, type } = this.props;
    const StaffModalData = {
      searchView: this.state.searchView,
      searchText: this.state.searchText,
      changeHandler: this.changeHandler.bind(this),
      handleSearch: this.handleSearch.bind(this),
      staffList: this.props.staffList,
      modalOpen: this.modalOpen,
      modalClose: this.modalClose,
      selectedId: this.state.selectedId,
    };
    const StaffModalList = {
      searchView: this.state.searchView1,
      searchText: this.state.searchText1,
      changeHandler: this.changeHandler1.bind(this),
      handleSearch: this.handleSearch1.bind(this),
      staffList: this.props.staffList,
      modalOpen: this.modalOpen1,
      modalClose: this.modalClose1,
      selectedId: this.state.selectedId1,
    };
    return (
      <>
        <Accordion defaultActiveKey="">
          <Card className="rounded">
            <Accordion.Toggle
              as={Card.Header}
              eventKey="7"
              className="icon_remove_leave"
              onClick={this.iconPostion6}
            >
              <div className="d-flex justify-content-between">
                <p className="mb-0">{t('leaveManagement.leaveApprovalProcess')}</p>

                <p className="mb-0">
                  {this.state.arrow6 === true ? (
                    <i className="fa fa-chevron-down f-14" aria-hidden="true"></i>
                  ) : (
                    <i
                      className={`fa fa-chevron-${lang === 'ar' ? 'down' : 'up'} fa-rotate-90 f-14`}
                      aria-hidden="true"
                    ></i>
                  )}
                </p>
              </div>
            </Accordion.Toggle>

            <Accordion.Collapse eventKey="7" className="bg-white ">
              <Card.Body className=" innerbodyLeave border-top-remove">
                <div className="container-fluid ">
                  <div className="row">
                    <div className="col-md-12 col-xl-5 text-left">
                      <div className="mb-3">
                        <p className="f-14 ">{t('leaveManagement.addApprovers')}</p>
                      </div>
                    </div>
                  </div>
                  {CheckPermission(
                    'subTabs',
                    'Leave Management',
                    'Leave Settings',
                    '',
                    jsUcfirst(type),
                    '',
                    'Leave Approval Process',
                    'Add'
                  ) && (
                    <div className="d-flex justify-content-between mt--15 text-left">
                      <div className="p-2 w-100">
                        <Input
                          elementType={'floatingselect'}
                          elementConfig={{
                            options: options,
                          }}
                          value={this.state.staffType}
                          floatingLabel={t('global_configuration.staff_type')}
                          changed={(e) => this.handleSelect(e, 'staff_type')}
                        />
                      </div>

                      <div className="p-2 w-100">
                        <div className={`${lang === 'ar' ? '' : 'pt-1'}`}>
                          <Input
                            elementType={'floatinginput'}
                            elementConfig={{
                              type: 'text',
                            }}
                            maxLength={20}
                            value={approvename}
                            floatingLabel={t('leaveManagement.approverLevelName')}
                            changed={(e) => this.handleSelect(e, 'approvename')}
                          />
                        </div>
                      </div>

                      <div className="p-2 w-100">
                        <Input
                          elementType={'floatingselect'}
                          elementConfig={{
                            options:
                              staffType === 'admin'
                                ? this.Approveassociation.filter(
                                    (item) => item.value !== 'Applicants Dept/Prog'
                                  )
                                : this.Approveassociation,
                          }}
                          value={this.state.Approveassociation}
                          floatingLabel={t('leaveManagement.approverAssociation')}
                          changed={(e) => this.handleSelect(e, 'approveassociation')}
                        />
                      </div>

                      <div className="p-2 w-100">
                        {this.state.Approveassociation !== 'Others' && (
                          <Input
                            elementType={'floatingselect'}
                            elementConfig={{
                              options: this.state.role,
                            }}
                            value={this.state.selectedrole}
                            floatingLabel={t('leaveManagement.InstituteRole')}
                            changed={(e) => this.handleSelect(e, 'role')}
                          />
                        )}
                      </div>

                      <div className="p-2 w-100">
                        <div className="pt-3">
                          <div className={`sb-example-2 ${lang === 'ar' ? '' : 'mt-1'}`}>
                            <div className="search">
                              <input
                                type="text"
                                className="searchTerm"
                                placeholder={t('role_management.role_actions.Search')}
                                value={this.state.name}
                                maxLength={10}
                                readOnly
                              />
                              <button type="submit" className="searchButton">
                                {this.state.Approveassociation === 'Others' && (
                                  <i
                                    className="fa fa-search"
                                    onClick={(e) => this.modalOpen('add')}
                                  ></i>
                                )}
                              </button>
                              {this.state.searchView && (
                                <StaffModal StaffModalData={StaffModalData} />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="p-1">
                        <div className="pt-4">
                          <Button
                            variant="outline-primary "
                            block
                            disabled={
                              !approvename ||
                              (this.state.Approveassociation === 'Others' && !name) ||
                              (this.state.Approveassociation !== 'Others' && !selectedrole)
                            }
                            onClick={() => this.add()}
                          >
                            {t('add')}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="row">
                    <div className="col-md-6 offset-md-6">
                      <div className="pt-1 pb-2">
                        <div className="sb-example-1">
                          <div className="search">
                            <input
                              type="text"
                              className={`searchTerm`}
                              placeholder={t('leaveManagement.searchBy')}
                              value={searchTerm}
                              maxLength={10}
                              onChange={(e) => this.handleChangeSearch(e)}
                            />
                            <button type="submit" className={`searchButton`}>
                              <i className="fa fa-search"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="row pt-2">
                    <div className="col-md-12 ">
                      <div className="border rounded">
                        <Table responsive hover>
                          <thead className="th-change">
                            <tr>
                              <th>{t('global_configuration.staff_type')}</th>
                              <th>{t('leaveManagement.approvalLevel')}</th>
                              <th>{t('leaveManagement.approvalLevelName')}</th>
                              <th>{t('leaveManagement.approvalAssociated')}</th>
                              <th>{t('leaveManagement.InstituteRole')}</th>
                              <th>{t('leaveManagement.staffName')}</th>
                              <th></th>
                            </tr>
                          </thead>
                          <tbody>
                            {this.props.approverList
                              ?.filter(
                                (data) =>
                                  (data.get('approver_association', '') &&
                                    data
                                      .get('approver_association', '')
                                      .toLowerCase()
                                      .includes(searchTerm)) ||
                                  (data.getIn(['role', 'role_name'], '') &&
                                    data
                                      .getIn(['role', 'role_name'], '')
                                      .toLowerCase()
                                      .includes(searchTerm)) ||
                                  (data.get('approver_level_name', '') &&
                                    data
                                      .get('approver_level_name', '')
                                      .toLowerCase()
                                      .includes(searchTerm))
                              )

                              .map((data, index) => {
                                return !editApproverList.has(data.get('_id')) ? (
                                  <tr className="tr-change" key={index}>
                                    <td>
                                      <div className="">
                                        {data.getIn(['staff_type', 0], '') === 'admin'
                                          ? 'Adminstrative Staff'
                                          : data.getIn(['staff_type', 0], '') === 'academic'
                                          ? 'Academic Staff'
                                          : 'All Staff'}
                                      </div>
                                    </td>

                                    <td>
                                      {' '}
                                      <div className="">Level {data.get('level_no')}</div>
                                    </td>
                                    <td>
                                      <div className="">{data.get('approver_level_name')}</div>
                                    </td>
                                    <td>
                                      <div className="">{data.get('approver_association')}</div>
                                    </td>
                                    <td>
                                      <div className="">{data.getIn(['role', 'role_name'])}</div>
                                    </td>
                                    <td>
                                      <div className="">{data.get('staff_name')}</div>
                                    </td>
                                    <td>
                                      <div className="">
                                        {CheckPermission(
                                          'subTabs',
                                          'Leave Management',
                                          'Leave Settings',
                                          '',
                                          jsUcfirst(type),
                                          '',
                                          'Leave Approval Process',
                                          'Edit'
                                        ) && (
                                          <i
                                            className="fa fa-pencil fa-lg remove_hover mr-2 f-14"
                                            aria-hidden="true"
                                            onClick={this.handleEditClick.bind(this, data)}
                                          ></i>
                                        )}
                                        {CheckPermission(
                                          'subTabs',
                                          'Leave Management',
                                          'Leave Settings',
                                          '',
                                          jsUcfirst(type),
                                          '',
                                          'Leave Approval Process',
                                          'Delete'
                                        ) && (
                                          <i
                                            className="fa fa-trash fa-lg remove_hover f-14"
                                            aria-hidden="true"
                                            onClick={this.handledeleteClick.bind(this, data)}
                                          ></i>
                                        )}
                                      </div>
                                    </td>
                                  </tr>
                                ) : (
                                  <EditableApproverList
                                    key={data.get('_id')}
                                    row={data}
                                    role={this.state.role}
                                    data={editApproverList}
                                    option={this.state.options}
                                    name={this.state.name1}
                                    searchView={this.state.searchView1}
                                    StaffModalData={StaffModalList}
                                    modalOpen={this.modalOpen1}
                                    handleChangeSearch={this.handleChangeSearch}
                                    handleChange={this.handleSelect.bind(this)}
                                    handleCancelClick={this.handleCancelClick.bind(this)}
                                    handleSaveClick={this.handleSaveClick.bind(this)}
                                    operation="update"
                                    Approveassociation={this.Approveassociation}
                                  />
                                );
                              })}
                          </tbody>
                        </Table>
                      </div>
                    </div>
                  </div>
                  <AlertModal
                    show={modalData.show}
                    title={modalData.title || ''}
                    description={modalData.description || ''}
                    variant={modalData.variant || 'confirm'}
                    confirmButtonLabel={modalData.confirmButtonLabel || t('YES')}
                    cancelButtonLabel={modalData.cancelButtonLabel || t('NO')}
                    onClose={this.onModalClose.bind(this)}
                    onConfirm={this.onConfirm.bind(this)}
                    data={studentId}
                  />
                </div>
              </Card.Body>
            </Accordion.Collapse>
          </Card>
        </Accordion>
      </>
    );
  }
}
LeaveApproval.propTypes = {
  editApproverList: PropTypes.object,
  type: PropTypes.string,
  roleList: PropTypes.instanceOf(List),
  staffList: PropTypes.instanceOf(List),
  approverList: PropTypes.instanceOf(List),
  addapprover: PropTypes.func,
  getRolesList: PropTypes.func,
  getAllStaff: PropTypes.func,
  getApproverList: PropTypes.func,
  setData: PropTypes.func,
  deleteapprover: PropTypes.func,
};
function EditableApproverList(props) {
  const {
    option,
    handleChange,
    data,
    row,
    role,
    operation,
    handleCancelClick,
    handleSaveClick,
    name,
    modalOpen,
    searchView,
    StaffModalData,
    Approveassociation,
  } = props;
  const id = row.get('_id');
  return (
    <tr className="tr-change">
      <td>
        <Input
          elementType={'floatingselect'}
          elementConfig={{
            options: option,
          }}
          value={data.getIn([id, 'staff_type', 0])}
          floatingLabel={t('global_configuration.staff_type')}
          changed={(e) => handleChange(e, 'staff_type', operation, id, option)}
        />
      </td>

      <td>
        <div className="">{data.getIn([id, 'level_no'])}</div>
      </td>
      <td>
        <div className="">
          <Input
            elementType={'floatinginput'}
            elementConfig={{
              type: 'text',
            }}
            maxLength={20}
            value={data.getIn([id, 'approver_level_name'])}
            floatingLabel={t('leaveManagement.approvalLevelName')}
            changed={(e) => handleChange(e, 'approver_level_name', operation, id)}
          />
        </div>
      </td>
      <td>
        <div className="">
          <Input
            elementType={'floatingselect'}
            elementConfig={{
              options:
                data.getIn([id, 'staff_type', 0], '') === 'admin'
                  ? Approveassociation.filter((item) => item.value !== 'Applicants Dept/Prog')
                  : Approveassociation,
            }}
            maxLength={20}
            value={data.getIn([id, 'approver_association'])}
            floatingLabel={t('leaveManagement.approvalAssociation')}
            changed={(e) => handleChange(e, 'approver_association', operation, id)}
          />
        </div>
      </td>

      <td>
        <div className="">
          {data.getIn([id, 'approver_association']) !== 'Others' && (
            <Input
              elementType={'floatingselect'}
              elementConfig={{
                options: role,
              }}
              maxLength={20}
              value={data.getIn([id, 'role', '_roles_id'])}
              floatingLabel={t('leaveManagement.InstituteRole')}
              changed={(e) => handleChange(e, 'role', operation, id, role)}
            />
          )}
        </div>
      </td>

      <td>
        {data.getIn([id, 'approver_association']) === 'Others' && (
          <div className="pt-2">
            <div className="sb-example-2 mt-2">
              <div className="search">
                <input
                  type="text"
                  className="searchTerm"
                  placeholder={t('role_management.role_actions.Search')}
                  value={name}
                  maxLength={10}
                  // onChange={(e) => handleChangeSearch(e)}
                />
                <button type="submit" className="searchButton">
                  {data.getIn([id, 'approver_association']) === 'Others' && (
                    <i className="fa fa-search" onClick={(e) => modalOpen(id)}></i>
                  )}
                </button>
                {searchView && <StaffModal StaffModalData={StaffModalData} />}
              </div>
            </div>
          </div>
        )}
      </td>
      <td>
        <div className="aw-50">
          <div className="d-flex justify-content-between pt-4">
            <b className="float-left pr-2 f-16">
              <i
                className="fa fa-times-circle"
                aria-hidden="true"
                onClick={() => handleCancelClick(row, operation)}
              />
            </b>

            <b className="float-left pl-2 f-16">
              <i
                className="fa fa-floppy-o"
                aria-hidden="true"
                onClick={() => handleSaveClick(id, operation, false, data)}
              />
            </b>
          </div>
        </div>
      </td>
    </tr>
  );
}

const mapStateToProps = function (state) {
  return {
    leaveCategories: selectLeaveCategories(state),
    roleList: selectRolesList(state),
    staffList: selectStaffList(state),
    approverList: selectApproverList(state),
    editApproverList: selectEditApproverList(state),
    edited: selectEdited(state),
  };
};
EditableApproverList.propTypes = {
  StaffModalData: PropTypes.object,
  editApproverList: PropTypes.object,
  modalOpen: PropTypes.func,
  handleChangeSearch: PropTypes.func,
  handleChange: PropTypes.func,
  handleCancelClick: PropTypes.func,
  handleSaveClick: PropTypes.func,
  searchView: PropTypes.bool,
  role: PropTypes.array,
  Approveassociation: PropTypes.array,
  option: PropTypes.array,
  operation: PropTypes.string,
  name: PropTypes.string,
  data: PropTypes.instanceOf(List),
  row: PropTypes.instanceOf(List),
};
export default compose(withRouter, connect(mapStateToProps, actions))(LeaveApproval);
