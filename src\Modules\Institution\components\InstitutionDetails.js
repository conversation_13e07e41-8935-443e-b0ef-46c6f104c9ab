import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import Dialog from '@mui/material/Dialog';

import InstitutionView from './InstitutionView';
import InstitutionForm from './InstitutionForm';
import * as actions from '../../../_reduxapi/institution/actions';
import {
  selectInstitution,
  selectCountryList,
  selectShowCollegeModal,
} from '../../../_reduxapi/institution/selectors';
import { selectUserId } from '../../../_reduxapi/Common/Selectors';
import '../css/institution.css';
import '../../ProgramInput/css/program.css';
import '../../../Assets/css/grouping.css';
import { fileSizeTypeCheck } from '../../../utils';

class InstitutionDetails extends Component {
  constructor() {
    super();
    this.state = {
      institution: Map(),
      logo: null,
    };
    this.handleChange = this.handleChange.bind(this);
    this.handleModalClose = this.handleModalClose.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.handleEdit = this.handleEdit.bind(this);
  }

  componentDidMount() {
    this.props.getInstitution(this.props.userId);
    this.props.getCountryList();
  }

  handleEdit() {
    const { institution } = this.props;
    const addressDetails = institution.get('address_details', Map());
    this.setState({
      institution: Map({
        _id: institution.get('_id'),
        institute_type: institution.get('institute_type', ''),
        institute_name: institution.get('institute_name', ''),
        ...(institution.get('institute_type') === 'group' && {
          noOfColleges: `${institution.get('noOfColleges', '')}`,
        }),
        logo: institution.get('logo', ''),
        address_details: {
          address: addressDetails.get('address', ''),
          country: addressDetails.get('country', ''),
          state: addressDetails.get('state', ''),
          city: addressDetails.get('city', ''),
          district: addressDetails.get('district', ''),
          zipcode: addressDetails.get('zipcode', ''),
        },
      }),
      logo: null,
    });
    this.props.setData(Map({ showCollegeModal: true }));
  }

  handleChange(event, name) {
    if (name === 'logo') {
      let files = event;
      if (!files) files = [];
      if (!fileSizeTypeCheck(this.props, files[0])) {
        this.setState({
          logo: null,
        });
        return;
      } else {
        this.setState({
          logo: files.length ? files[0] : null,
        });
        return;
      }
    }

    const value = event.target.value;

    if (['noOfColleges', 'zipcode'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    const setOperation = ['address', 'country', 'state', 'city', 'district', 'zipcode'].includes(
      name
    )
      ? 'setIn'
      : 'set';

    this.setState((state) => {
      return {
        institution: state.institution[setOperation](
          setOperation === 'set' ? name : ['address_details', name],
          value
        ).mergeDeep(
          Map({
            address_details: Map({
              ...(name === 'country' && { state: '', city: '', district: '' }),
              ...(name === 'state' && { city: '', district: '' }),
              ...(name === 'city' && { district: '' }),
            }),
          })
        ),
      };
    });
  }

  handleSubmit(data) {
    this.props.addInstitution({ ...data });
  }

  handleModalClose() {
    this.props.setData(Map({ showCollegeModal: false }));
    this.setState({ logo: null, institution: Map() });
  }

  render() {
    const { logo } = this.state;
    const { showCollegeModal, countryList, institution } = this.props;

    if (institution.isEmpty()) {
      return <div className="mt-5 text-align-center">Institution is empty</div>;
    }

    return (
      <>
        <div className="main pt-3 pb-5 bg-gray">
          <div className="container pb-5">
            <div className="row justify-content-center">
              <div className="col-xl-5 col-lg-5 col-md-7 col-12">
                <div className="d-flex justify-content-between pb-2">
                  <div className="pl-2 f-19 bold">
                    {`${
                      institution.get('institute_type') === 'group' ? 'University' : 'College'
                    } Details`}
                  </div>
                  <div className="pl-2 f-19 bold">
                    <i
                      className="fa fa-pencil text-darkgray f-16 remove_hover"
                      onClick={this.handleEdit}
                    ></i>
                  </div>
                </div>
                <InstitutionView institution={institution} showBoxShadow />
              </div>
            </div>
          </div>
        </div>
        <Dialog maxWidth="xs" open={showCollegeModal} onClose={this.handleModalClose}>
          {showCollegeModal && (
            <InstitutionForm
              countryList={countryList}
              institutionType={{
                type: institution.get('institute_type', ''),
                name: 'Edit Institution',
              }}
              institution={this.state.institution}
              logo={logo}
              handleChange={this.handleChange}
              handleBack={this.handleModalClose}
              handleSubmit={this.handleSubmit}
              operation="update"
            />
          )}
        </Dialog>
      </>
    );
  }
}

InstitutionDetails.propTypes = {
  userId: PropTypes.string,
  getInstitution: PropTypes.func,
  setData: PropTypes.func,
  getCountryList: PropTypes.func,
  addInstitution: PropTypes.func,
  institution: PropTypes.instanceOf(Map),
  countryList: PropTypes.instanceOf(List),
  showCollegeModal: PropTypes.bool,
};

const mapStateToProps = (state) => {
  return {
    userId: selectUserId(state),
    institution: selectInstitution(state),
    countryList: selectCountryList(state),
    showCollegeModal: selectShowCollegeModal(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(InstitutionDetails);
