import React from 'react';
import { Mo<PERSON>, Button } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import { getEnvCollegeName, getMailContent, showArabicMailShow } from 'utils';
import { NotificationManager } from 'react-notifications';
import { t } from 'i18next';
import axios from '../../axios';
import CKEditor5 from 'Widgets/CkEditor/CkEditor';

const MailContentModal = (props) => {
  const { show, hide, type, state, setState, totalSelect, linkText, fetchApi } = props;
  const handleSendMail = (e) => {
    let message = state.message;
    message += '<br/><hr/><br/>';
    if (showArabicMailShow()) {
      message += "<p dir='rtl' lang='ar'>الملاحظة:</p>";
      message +=
        "<p dir='rtl' lang='ar'>اضغط على الرابط الموجود في أول قسم هذا الإيميل للتسجيل</p>";
      message +=
        "<p dir='rtl' lang='ar'>لا بد من أن يتم التسجيل خلال التاريخ المحدد المذكور أعلاه</p>";
      message +=
        "<p dir='rtl' lang='ar'>تفضل بتجهيز بطاقة الهوية الوطنية/بطاقة الإقامة تم مسحها ضوئيا بصيغة .جي.بي.جي أو.بي.إن.جي قبل بدء عملية التسجيل.ويجب ألا يتجاوز حجم الملف ٦ ميغابايت لكي يتم تحميله علي النظام على أن الحجم الموصى به أقل من ١ ميغابايت.</p>";
      message +=
        "<p dir='rtl' lang='ar'><b>يجب على الطلبة أن يكون معهم أوراق القبول (على سبيل المثال برنامج الطب) عند زيارة مكتب التقاط بصمة الوجه لإكمال عملية التسجيل</b></p>";
      message +=
        "<p dir='rtl' lang='ar'>لمزيد من المعلومات رجاء الرجوع <a href=video_link>للفيديو</a></p>";
      if (type === 'Student') {
        message +=
          '<br><hr/><br>Your health and safety are our concern, so please visit the link below after completing the registration process to book an appointment to take a facial recognition by visiting the Digital Transformation Unit<br>Please be sure to come on time to avoid crowds and to serve you better.<br><a target="_blank" href="https://calendly.com/digivalsolutions/digival-registration">https://calendly.com/digivalsolutions/digival-registration</a>';
        message += '<br/><hr/><br/>';
        message +=
          "<p dir='rtl' lang='ar'>صحتكم وسلامتكم محل اهتمامنا لذا يرجى زيارة الرابط أدناه بعد إتمام عملية التسجيل لحجز موعد لأخذ بصمة</p>";
        message +=
          "<p dir='rtl' lang='ar'>التعرف على الوجه وذلك بزيارتكم لمقر وحدة التحول الرقمي</p>";
        message +=
          "<p dir='rtl' lang='ar'>يرجى الالتزام بالحضور في الموعد المحدد لتجنب الازدحام وخدمتكم بشكل أفضل</p>";
        message +=
          "<p dir='rtl' lang='ar'><a target='_blank' href='https://calendly.com/digivalsolutions/digival-registration'>https://calendly.com/digivalsolutions/digival-registration</a></p>";
      }
    }
    message += '<br><br>Best Regards<br>' + getEnvCollegeName();

    const data = {
      type: 'signup',
      message: message,
      id: state.mailId,
    };

    setState({
      isLoading: true,
    });
    axios
      .post(`user/user_mail_push`, data)
      .then((res) => {
        setState(
          {
            isLoading: false,
            registrationConfirmationShow: false,
          },
          () => {
            fetchApi({});
          }
        );

        NotificationManager.success(t('user_management.Mail_Sent_Successfully'));
      })
      .catch((error) => {
        NotificationManager.warning(`${error.response.data.message}`);
        setState({
          isLoading: false,
        });
      });
  };
  return (
    <Modal show={show} centered size="lg" onHide={hide}>
      <Modal.Header closeButton>
        <div className="row w-100">
          <div className="col-md-6 pt-1">{totalSelect + ' ' + type} Selected on this page</div>
          <div className="col-md-6">
            <div className="float-right">
              <Button onClick={(e) => handleSendMail(e)}>
                <Trans i18nKey={'send_mail'}></Trans>{' '}
                <i className="fa fa-envelope" aria-hidden="true"></i>
              </Button>
            </div>
          </div>
        </div>
      </Modal.Header>
      <Modal.Body>
        <CKEditor5
          data={`${getMailContent()}`}
          onInit={(editor) => {
            const data = editor.getData();
            const data1 = data
              .replace('NAME', 'v_name')
              .replace('CLICK HERE', '<a href=v_link>CLICK HERE</a>')
              .replace('AUTOGENERATEDTEMPORARYPASSWORD', 'v_password')
              .replace('DATE', 'v_date_expired')
              .replace('SENDER', 'v_admin_sign')
              .replace('Link', '<a href=video_link>Link</a>')
              .replace('SEE THE VIDEO', '<a href=video_link>SEE THE VIDEO</a>')
              .replace('COLLEGE', getEnvCollegeName());
            setState({ message: data1 });
          }}
          onChange={(editor) => {
            const data = editor.getData();
            const data1 = data
              .replace('NAME', 'v_name')
              .replace('CLICK HERE', `<a href=v_link>${linkText}</a>`)
              .replace('AUTOGENERATEDTEMPORARYPASSWORD', 'v_password')
              .replace('DATE', 'v_date_expired')
              .replace('SENDER', 'v_admin_sign')
              .replace('Link', '<a href=video_link>Link</a>')
              .replace('SEE THE VIDEO', '<a href=video_link>SEE THE VIDEO</a>')
              .replace('COLLEGE', getEnvCollegeName());
            setState({ message: data1 });
          }}
        />
      </Modal.Body>
    </Modal>
  );
};
MailContentModal.propTypes = {
  show: PropTypes.bool,
  hide: PropTypes.func,
  type: PropTypes.string,
  handleSendMail: PropTypes.func,
  totalSelect: PropTypes.number,
  setState: PropTypes.func,
  linkText: PropTypes.string,
  state: PropTypes.object,
  fetchApi: PropTypes.func,
};
export default MailContentModal;
