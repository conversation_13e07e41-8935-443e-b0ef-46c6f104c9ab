import React, { Fragment, useReducer, useEffect, useState } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { connect } from 'react-redux';
import { NotificationManager } from 'react-notifications';
import PropTypes from 'prop-types';

import {
  Null,
  PrimaryButton,
  FlexWrapper,
  Padding,
  EventWrapper,
  ModalWrapper,
  ModalBackgroundWrapper,
} from '../Styled';
import {
  changeTitle,
  courseSave,
  toggleModal,
  editCourseSave,
  saveCourseEvents,
  updateCourseEvents,
  getCourses,
  getBatchCourses,
  rotationalSave,
  editRotationalSave,
  getData,
  getRotationalCourses,
} from '../../../_reduxapi/actions/calender';
import { nonRotational } from './InitialState';
import { rootNonRotationalReducer } from './CourseReducer';
import EventRows from '../UtilityComponents/EventRows';
import BackgroundSelect from './BackgroundSelect';
import CourseTitle from './CourseTitle';
import CourseDuration from './CourseDuration';
import ChooseLevel from './ChooseLevel';
import AddEvent from '../Modal/Events/AddEvent';
import Loader from '../../../Widgets/Loader/Loader';
import CourseGroup from './CourseGroup';
import config from '../../../_utils/config';
import CourseAddMode from './CourseAddMode';
//import CheckIds from '../UtilityComponents/CheckIds';
import moment from 'moment';
import { getLang, timeFormat } from '../../../utils';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
import LocalStorageService from 'LocalStorageService';

const { apiInstance } = config;
const lang = getLang();

const CourseInput = (props) => {
  const {
    id_array,
    changeTitle,
    courseSave,
    _calendar_id,
    edit,
    courses,
    batch_courses,
    isLoading,
    content,
    toggleModal,
    editCourseSave,
    saveCourseEvents,
    rotationalSave,
    updateCourseEvents,
    getCourses,
    getBatchCourses,
    editRotationalSave,
    getData,
    apiCalled,
    getRotationalCourses,
    activeInstitutionCalendar,
  } = props;

  const non_rotation = useReducer(rootNonRotationalReducer, nonRotational);
  const [nonRotation, setNonRotation] = non_rotation;
  const history = useHistory();
  const match = useRouteMatch();
  const active = match.params.year || 'year2';

  const [loaded, setLoaded] = useState(false);
  const [count, setCount] = useState(0);
  const [count1, setCount1] = useState(0);
  const [count2, setCount2] = useState(0);
  const [count3, setCount3] = useState(0);
  const [redirect, setRedirect] = useState(false);
  const localLoad = useState(false);
  const [loaderState, setLoaderState] = localLoad;
  const [editCourseAdded, setEditCourseAdded] = useState(false);

  const [editCount, setEditCount] = useState(0);

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let programId = params.get('programid');
  let urlYear = params.get('year');
  let urlName = params.get('pname');
  // eslint-disable-next-line
  const editPullEvents = async () => {
    const res = await apiInstance.get(
      `program_calendar_event/list_event_date_filter/${props[active]['id']}/${nonRotation.level_no}/${nonRotation.type._course_id}/${nonRotation.type.start_date}/${nonRotation.type.end_date}`
    );
    if (res.data && res.data.status_code === 200 && res.data.data !== 'No Events found') {
      setNonRotation({
        type: 'EVENT_COPY_TO_DELETE',
        payload: res.data.data,
      });
      setLoaderState(false);
    } else {
      setNonRotation({
        type: 'EVENT_COPY_TO_DELETE',
        payload: [],
      });
      setLoaderState(false);
    }
  };

  useEffect(() => {
    if (nonRotation.update_method === 'edit' && editCount === 0) {
      editPullEvents();
      setEditCount(1);
    }
  }, [nonRotation, editCount, editPullEvents]);

  const pullEvents = async () => {
    const res = await apiInstance.get(
      `program_calendar_event/list_event_date_filter/${props[active]['id']}/${nonRotation.level_no}/${nonRotation.type._course_id}/${nonRotation.type.start_date}/${nonRotation.type.end_date}`
    );
    if (res.data && res.data.status_code === 200 && res.data.data !== 'No Events found') {
      setNonRotation({
        type: 'GET_COURSE_EVENTS',
        payload: res.data.data,
      });
      setNonRotation({
        type: 'CHECK_PREVIOUS_STATE',
      });
      setLoaderState(false);
    } else {
      setNonRotation({
        type: 'GET_COURSE_EVENTS',
        payload: [],
      });
      setNonRotation({
        type: 'CHECK_PREVIOUS_STATE',
      });
      setLoaderState(false);
    }
  };

  useEffect(() => {
    if (!nonRotation.set_course_events && nonRotation.course_events.length !== 0) {
      let check_ids = nonRotation.deleted_events.map((item) => item._event_id);
      let copy_events;

      if (check_ids.length === 0) {
        copy_events = nonRotation.course_events;
      } else {
        copy_events = nonRotation.course_events.filter(
          (item) => !check_ids.includes(item._event_id)
        );
      }
      setNonRotation({
        type: 'COPY_COURSE_EVENTS',
        payload: copy_events,
      });
    }
  });

  useEffect(() => {
    if (
      apiCalled &&
      !loaded &&
      id_array.length !== 0 &&
      activeInstitutionCalendar &&
      !activeInstitutionCalendar.isEmpty()
    ) {
      console.log('landing4'); //eslint-disable-line
      getData(
        id_array,
        NotificationManager,
        setLoaded,
        match.params.id,
        activeInstitutionCalendar.get('_id')
      );
    }
  }, [id_array, apiCalled, loaded, getData, match.params.id, setLoaded, activeInstitutionCalendar]);

  useEffect(() => {
    if (_calendar_id && count === 1 && count1 === 0) {
      //pullEvents();
      setCount1(1);
    }
  }, [_calendar_id, active, count, count1]);

  useEffect(() => {
    let title = 'Add Course';
    if (edit) {
      title = 'Edit Course';
    }
    changeTitle(title);
  });

  useEffect(() => {
    if (apiCalled && loaded) {
      if (nonRotation.update_method) {
        setNonRotation({
          type: 'ADD_COURSES',
          payload: courses,
        });
        if (batch_courses.length) {
          setNonRotation({
            type: 'ADD_BATCH_COURSES_ID',
            payload: batch_courses,
          });
        }
      }
    }

    if (LocalStorageService.getCustomToken('courseInputData', true) !== null && count === 0) {
      setNonRotation({
        type: 'FROM_LOCAL_STORAGE',
        previous: LocalStorageService.getCustomToken('courseInputData', true),
        payload: props[active],
      });
      setCount(1);
    } else {
      if (props[active]) {
        if (edit) {
          if (apiCalled && loaded && count2 === 0) {
            getCourses(
              props[active]['id'],
              'regular',
              content['modal_level_name'].slice(6),
              NotificationManager
            );
            setCount2(1);
          }
          if (content.modal_rotational_number !== '' && count2 === 1 && count3 === 0) {
            getBatchCourses(
              'yes',
              props[active]['id'],
              'regular',
              content['modal_level_name'].slice(6),
              NotificationManager
            );
            setCount3(1);
          }
          if (count2 === 1 && !editCourseAdded) {
            setNonRotation({
              type: 'INITIAL_LOAD_EDIT_COURSE',
              payload: props[active],
              data: content,
              year: active,
            });
            setEditCourseAdded(true);
          }
        } else {
          if (apiCalled && loaded) {
            setNonRotation({
              type: 'INITIAL_LOAD_ADD_COURSE',
              payload: props[active],
              year: active,
            });
          }
        }
      }
    }
  }, [
    active,
    apiCalled,
    batch_courses,
    content,
    count,
    count2,
    count3,
    courses,
    edit,
    getBatchCourses,
    getCourses,
    loaded,
    props,
    setNonRotation,
    editCourseAdded,
    nonRotation.update_method,
  ]);

  useEffect(() => {
    if (
      nonRotation.type.start_date !== '' &&
      nonRotation.type.end_date !== '' &&
      nonRotation.check.pre_start !== nonRotation.type.start_date
        ? true
        : nonRotation.check.pre_end !== nonRotation.type.end_date
    ) {
      pullEvents(); // initial Load
    }
  });

  useEffect(() => {
    if (redirect) {
      history.push(
        `/program-calendar/${match.params.id}/${match.params.year}?year=${urlYear}&programid=${programId}&pname=${urlName}`
      );
    }
  }, [
    redirect,
    history,
    match.params.id,
    urlName,
    urlYear,
    match.params.year,
    programId,
    match.params.sem,
  ]);

  const dataAlign = (state, cb, push) => {
    let error = false;
    let data = {};
    data._calendar_id = state['edit']['_id'];
    data.level_no = state['level_no'];
    data.batch = 'regular';
    data._course_id = state['type']['_course_id'];
    //these needs to deleted from api from

    //up to ^

    if (state['custom_dates'] === 'academic_week') {
      if (state['academic_week_start'] === 1) {
        data.start_date = state['edit'][state['work_start_date']];
      } else {
        data.start_date = state['type']['start_date'];
      }

      if (state['academic_week_end'] === state['academic_weeks'].length) {
        data.end_date = state['edit'][state['work_end_date']];
      } else {
        data.end_date = state['type']['end_date'];
      }
    } else {
      // if (
      //   Date.parse(state["type"]["start_date"]) <
      //   Date.parse(state["edit"][state["work_start_date"]])
      // ) {
      //   error = true;
      //   NotificationManager.error(
      //     "Course start date should greater than or equal to level start date"
      //   );
      // } else {
      //   data.start_date = state["type"]["start_date"];
      // }

      // if (
      //   Date.parse(state["type"]["end_date"]) >
      //   Date.parse(state["edit"][state["work_end_date"]])
      // ) {
      //   error = true;
      //   NotificationManager.error(
      //     "Course end date should lesser than or equal to level end date"
      //   );
      // } else {
      //   data.end_date = state["type"]["end_date"];
      // }
      data.start_date = state['type']['start_date'];
      data.end_date = state['type']['end_date'];
    }

    data.color_code = state['type']['background_color'];
    data._event_id = state['type']['events'].map((item) => item._event_id);
    // .filter((item) => item._id)

    if (!error) {
      setLoaderState(true);
      cb({ ...data }, push, NotificationManager, setLoaderState);
    }
  };

  const dataEventAlign = (data, edit, check, events) => {
    let error = false;
    let final = { event_name: {} };

    const start = data.start_date + ' ' + data.start_time + ':00:000';
    const end = data.end_date + ' ' + data.end_time + ':00:000';
    // const check_start = data.start_date + "T" + data.start_time + ":00.000Z";
    // const check_end = data.end_date + "T" + data.end_time + ":00.000Z";

    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    const check_start = st;
    const check_end = et;

    events.forEach((item) => {
      if (Date.parse(item.start_time) <= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_start)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name.first_language}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) <= Date.parse(check_end) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name.first_language}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.start_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name.first_language}. Please use different timings `
          );
        }
      } else if (Date.parse(item.end_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name.first_language}. Please use different timings `
          );
        }
      }
    });
    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.end_date) < Date.parse(data.start_date)) {
      NotificationManager.error('Start date should not greater than End date');
      error = true;
    } else if (Date.parse(start) > Date.parse(end) || Date.parse(start) === Date.parse(end)) {
      NotificationManager.error('End time should be greater than Start time');
      error = true;
    } else if (Date.parse(start) < Date.parse(edit.start_date)) {
      error = true;
      NotificationManager.error('Event start date should not be lesser than level start date');
    }
    // else if (Date.parse(start) > Date.parse(edit.end_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Event start date should not be greater than level end date"
    //   );
    // }
    // else if (Date.parse(end) > Date.parse(edit.end_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Event end date should not be greater than level end date"
    //   );
    // }

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    final.event_calendar = 'course';
    final.year = active;
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();

    // if (data.start_time) {
    //   final.start_time = start;
    // } else {
    //   final.start_time = data.start_date + " 00:00:00:000";
    // }
    // if (data.end_time) {
    //   final.end_time = end;
    // } else {
    //   final.end_time = data.end_date + " 00:00:00:000";
    // }

    final.end_date = endDate;
    final._calendar_id = _calendar_id;
    final._course_id = edit._course_id;
    final.batch = 'regular';

    if (check === 'rotation') {
      final.rotation_count = 1;
    }

    if (!error) {
      setLoaderState(true);
      saveCourseEvents(final, NotificationManager, pullEvents, setNonRotation);
    }
  };

  const updateEvent = (data, id, check, level) => {
    let error = false;
    let final = { event_name: {} };

    const start = data.start_date + ' ' + data.start_time + ':00:000';
    const end = data.end_date + ' ' + data.end_time + ':00:000';

    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.end_date) < Date.parse(data.start_date)) {
      NotificationManager.error('Start date should not greater than End date');
      error = true;
    } else if (Date.parse(start) > Date.parse(end) || Date.parse(start) === Date.parse(end)) {
      NotificationManager.error('End time should be greater than Start time');
      error = true;
    }

    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    //final.year = active.slice(4);
    final.event_calendar = data.event_calendar;
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();

    // if (data.start_time) {
    //   final.start_time = data.start_date + " " + data.start_time + ":00:000";
    // } else {
    //   final.start_time = data.start_date + " 00:00:00:000";
    // }
    // if (data.end_time) {
    //   final.end_time = data.end_date + " " + data.end_time + ":00:000";
    // } else {
    //   final.end_time = data.end_date + " 00:00:00:000";
    // }

    final.end_date = endDate;
    final._calendar_id = _calendar_id;
    final._course_id = id;
    final._event_id = data._event_id;
    final.batch = 'regular';
    final.level_no = level;

    if (!error) {
      setLoaderState(true);
      updateCourseEvents(final, NotificationManager, pullEvents, setNonRotation);
    }
  };

  const rotationalDataAlign = (data, cb, push) => {
    let error = false;
    let final = {};

    final._calendar_id = data['edit']['_id'];
    final.level_no = data['level_no'];
    final.batch = 'regular';
    final._course_id = data['type']['_course_id'];
    final.rotation_count = data['type']['rotation_count'];

    if (data['custom_dates'] === 'academic_week') {
      final.by = 'week';
      final.start_week = data['type']['week_start'];
      final.end_week = data['type']['week_end'];
    } else {
      final.by = 'date';
      // if (new Date(data["type"]["start_date"]).getDay() !== 0) {
      //   error = true;
      //   NotificationManager.error("Start date is not sunday");
      // }
      // if (
      //   Date.parse(data["type"]["start_date"]) <
      //   Date.parse(data["edit"][data["work_start_date"]])
      // ) {
      //   error = true;
      //   NotificationManager.error(
      //     "Course start date should greater than or equal to level start date"
      //   );
      // } else {
      //   final.start_date = moment(data["type"]["start_date"]).format("YYYY-MM-DD");
      // }
      final.start_date = moment(data['type']['start_date']).format('YYYY-MM-DD');
      // if (new Date(data["type"]["end_date"]).getDay() !== 4) {
      //   error = true;
      //   NotificationManager.error("End date is not Thursday");
      // }
      // if (
      //   Date.parse(data["type"]["end_date"]) >
      //   Date.parse(data["edit"][data["work_end_date"]])
      // ) {
      //   error = true;
      //   NotificationManager.error(
      //     "Course end date should lesser than or equal to level end date"
      //   );
      // } else {
      //   final.end_date = moment(data["type"]["end_date"]).format("YYYY-MM-DD");
      // }
      final.end_date = moment(data['type']['end_date']).format('YYYY-MM-DD');
    }

    final.color_code = data['type']['background_color'];
    final._event_id = data['type']['events'].map((item) => item._event_id);

    if (data.type._batch_course_id.length !== 0) {
      final._batch_course_id = data.type._batch_course_id;
    } else {
      final._batch_course_id = [];
    }

    // if (data.update_method === "edit") {
    //   final._id = data.type._id;
    // }

    if (!error) {
      setLoaderState(true);
      cb(final, data['course_add_mode'], push, NotificationManager, setLoaderState);
    }
  };
  const temp = () => (
    <ModalWrapper>
      <ModalBackgroundWrapper>
        <h3 className="text-left">
          {nonRotation.modal_mode === 'add'
            ? t('role_management.role_actions.Add Event')
            : t('events.edit_event')}
        </h3>
        <p className="text-left">
          <Trans i18nKey={'select_date_to_sync'}></Trans>
        </p>
        <AddEvent
          data={nonRotation.modal_content}
          method={setNonRotation}
          min_len={nonRotation.type.start_date}
          max_len={nonRotation.type.end_date}
          levelStartDate={''}
          levelEndDate={''}
        />
        <FlexWrapper>
          <Null />
          <PrimaryButton className="light" onClick={() => setNonRotation({ type: 'OFF_MODAL' })}>
            <Trans i18nKey={'cancel'}></Trans>
          </PrimaryButton>
          <PrimaryButton
            className="bordernone"
            onClick={() => {
              if (nonRotation.modal_mode === 'add') {
                dataEventAlign(
                  nonRotation.modal_content,
                  nonRotation.type,
                  nonRotation.course_flow,
                  nonRotation.type.events
                );
              } else {
                updateEvent(
                  nonRotation.modal_content,
                  nonRotation.type._course_id,
                  nonRotation.course_flow,
                  nonRotation.work_level_number
                );
              }
            }}
          >
            <Trans i18nKey={'save'}></Trans>
          </PrimaryButton>
        </FlexWrapper>
      </ModalBackgroundWrapper>
    </ModalWrapper>
  );
  return (
    <div className="main">
      <Loader isLoading={isLoading} />
      <Loader isLoading={loaderState} />
      {/* {apiCalled && loaded && <CheckIds load={loaded} />} */}
      <Fragment>{nonRotation.modal && temp()}</Fragment>
      <Fragment>
        <FlexWrapper>
          <Padding
            className="back"
            onClick={() => {
              changeTitle('');
              history.push(
                `/program-calendar/${match.params.id}/${match.params.year}?year=${urlYear}&programid=${programId}&pname=${urlName}`
              );
              toggleModal();
              // localStorage.removeItem("activeYear");
              localStorage.removeItem('courseInputData');
            }}
          >
            <i className={`fas ${lang !== 'ar' ? 'fa-arrow-left' : 'fa-arrow-right'}`}></i>
          </Padding>
          <Padding
            className={`${lang !== 'ar' ? '' : 'ar-padding'}`}
            style={{ paddingLeft: '0px', fontSize: '20px' }}
          >
            {edit
              ? t('program_calendar.edit_course')
              : t('role_management.role_actions.Add Course')}
          </Padding>
          <Null />
          {nonRotation.type.start_date && nonRotation.type.end_date && (
            <PrimaryButton
              className={'bordernone'}
              disabled={''}
              onClick={() => {
                if (nonRotation.update_method === 'add' && nonRotation.course_flow === 'normal') {
                  dataAlign(nonRotation, courseSave, setRedirect);
                } else if (
                  nonRotation.update_method === 'edit' &&
                  nonRotation.course_flow === 'normal'
                ) {
                  dataAlign(nonRotation, editCourseSave, setRedirect);
                } else if (
                  nonRotation.update_method === 'add' &&
                  nonRotation.course_flow === 'rotation'
                ) {
                  rotationalDataAlign(nonRotation, rotationalSave, setRedirect);
                } else if (
                  nonRotation.update_method === 'edit' &&
                  nonRotation.course_flow === 'rotation'
                ) {
                  rotationalDataAlign(nonRotation, editRotationalSave, setRedirect);
                }

                changeTitle();
                toggleModal();
              }}
            >
              save
            </PrimaryButton>
          )}
        </FlexWrapper>
        <FlexWrapper mg={lang !== 'ar' ? '0 50px 0px 65px' : '0 65px 0px 50px'}>
          <ChooseLevel data={non_rotation} load={localLoad} />
          <Null />
          {nonRotation.title && <BackgroundSelect data={non_rotation} />}
        </FlexWrapper>
        {nonRotation.work_level_is_rotation && (
          <CourseAddMode
            data={non_rotation}
            getRotationalCourses={getRotationalCourses}
            NotificationManager={NotificationManager}
            setLoaderState={setLoaderState}
            id={props[active]['id'] !== undefined ? props[active]['id'] : ''}
            work_level_number={nonRotation.work_level_number}
          />
        )}
        {nonRotation.title !== '' &&
          (nonRotation.work_level_is_rotation ? (
            nonRotation.type.rotation_count ? (
              <CourseTitle data={non_rotation} />
            ) : null
          ) : (
            <CourseTitle data={non_rotation} />
          ))}
        {nonRotation.title &&
          nonRotation.course_flow === 'rotation' &&
          nonRotation.type.rotation_count !== 0 &&
          nonRotation['batch_courses_id'].length !== 0 &&
          nonRotation['course_add_mode'] !== 'manual' && <CourseGroup data={non_rotation} />}
        {nonRotation.type._course_id && nonRotation.type.model && (
          <CourseDuration data={non_rotation} />
        )}
        {nonRotation.type.start_date && nonRotation.type.end_date && (
          <Fragment>
            {' '}
            <FlexWrapper mg="20px 30px 10px 30px">
              <p>
                <Trans i18nKey={'list_of_events_that_occur'}></Trans>.
              </p>
              <Null />
              <PrimaryButton
                className="light"
                onClick={() => {
                  setNonRotation({ type: 'SHOW_MODAL', payload: 'add' });
                  LocalStorageService.setCustomToken(
                    'courseInputData',
                    JSON.stringify(nonRotation)
                  );
                }}
              >
                {' '}
                <i className="fas fa-plus" /> <Trans i18nKey={'program_calendar.events'}></Trans>
              </PrimaryButton>
            </FlexWrapper>
            <EventWrapper>
              <EventRows show="title" />
              {nonRotation.type.events?.length ? (
                nonRotation.type.events
                  .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
                  .map((item, i) => (
                    <EventRows
                      show="content"
                      i={i}
                      key={i}
                      content={item}
                      edit={() => {
                        setNonRotation({
                          type: 'EDIT_EVENT',
                          payload: i,
                          event: item,
                        });
                        LocalStorageService.setCustomToken(
                          'courseInputData',
                          JSON.stringify(nonRotation)
                        );
                      }}
                      editHide={(() => {
                        return nonRotation.edit[nonRotation['work_event']]
                          .map((item) => item._event_id)
                          .includes(item._event_id);
                      })()}
                      del={() => setNonRotation({ type: 'DELETE_EVENT', payload: i })}
                    />
                  ))
              ) : (
                <FlexWrapper mg="0px auto" pad="20px">
                  <Trans i18nKey={'program_calendar.no_weeks'}></Trans>
                </FlexWrapper>
              )}
            </EventWrapper>{' '}
          </Fragment>
        )}
      </Fragment>
    </div>
  );
};

CourseInput.propTypes = {
  id_array: PropTypes.object,
  changeTitle: PropTypes.func,
  courseSave: PropTypes.func,
  _calendar_id: PropTypes.string,
  courses: PropTypes.object,
  isLoading: PropTypes.bool,
  toggleModal: PropTypes.func,
  editCourseSave: PropTypes.func,
  saveCourseEvents: PropTypes.func,
  updateCourseEvents: PropTypes.func,
  getData: PropTypes.func,
  apiCalled: PropTypes.bool,
  edit: PropTypes.bool,
  rotationalSave: PropTypes.bool,
  getCourses: PropTypes.bool,
  getBatchCourses: PropTypes.bool,
  editRotationalSave: PropTypes.bool,
  getRotationalCourses: PropTypes.func,
  activeInstitutionCalendar: PropTypes.object,
  content: PropTypes.object,
  batch_courses: PropTypes.array,
};

const mapStateToProps = function (state) {
  // ({ calender }) => ({
  const { calender } = state;
  return {
    id_array: calender.acad_array,
    apiCalled: calender.commonApiCalled,
    edit: calender.course_editing,
    content: calender.edit_content,
    isLoading: calender.isLoading,
    courses: calender.add_courses,
    batch_courses: calender.rotational_batch_courses_id,
    _calendar_id: calender.program_calender_id,
    year2: calender.year2,
    year3: calender.year3,
    year4: calender.year4,
    year5: calender.year5,
    year6: calender.year6,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, {
  changeTitle,
  courseSave,
  toggleModal,
  editCourseSave,
  saveCourseEvents,
  updateCourseEvents,
  rotationalSave,
  getCourses,
  getBatchCourses,
  editRotationalSave,
  getData,
  getRotationalCourses,
})(CourseInput);
