import React, { Component } from 'react';
import { Map, fromJS } from 'immutable';
import PropTypes from 'prop-types';
import { Button, Modal, Table, Badge } from 'react-bootstrap';
import * as actions from '../../../_reduxapi/student/action';
import { connect } from 'react-redux';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import {
  selectActiveTerm,
  selectActiveYear,
  selectActiveLevel,
  selectActiveGender,
  selectTotalScheduleCount,
} from '../../../_reduxapi/student/selectors';
import { AUTOMATIC_TYPES } from '../../../constants';
import Input from '../../../Widgets/FormElements/Input/Input';
import { Trans } from 'react-i18next';
import { studentGroupUrl, studentGenderMode } from 'utils';
import { record_session_status } from 'Modules/GlobalConfiguration/utils';
import LocalStorageService from 'LocalStorageService';

class GroupButton extends Component {
  state = {
    groupEnable: false,
    automaticEnable: false,
    courseSetting: true,
    selectAutoOption: AUTOMATIC_TYPES[0][1],
    selectManual: 'group1',
    mode: 'auto',
    back: false,
    automatic: false,
    manual: false,
    delivery: [],
    scheduleStatus: '',
  };

  automatic = () => {
    this.setState({
      automatic: true,
      manual: false,
      mode: 'auto',
    });
  };

  manual = () => {
    this.setState({
      automatic: false,
      manual: true,
      mode: 'manual',
    });
  };

  wantToCallScheduleCountApi = () => {
    const { componentName } = this.props.GroupModalData;
    const data =
      componentName === 'foundationCourse' ||
      componentName === 'individualCourse' ||
      componentName === 'rotationCourse';
    return data;
  };

  getParams = (delivery) => {
    let { deliveryTypes, programId, courseId } = this.props.GroupModalData;
    if (delivery) {
      deliveryTypes = delivery;
    }
    const { activeTerm, activeLevel, activeYear, activeGender } = this.props;

    const activeInstitutionCalendar = LocalStorageService.getCustomToken(
      'activeInstitutionCalendar'
    );
    const calenderId = activeInstitutionCalendar ? JSON.parse(activeInstitutionCalendar)?._id : '';
    const params = {
      institutionCalendarId: calenderId,
      programId: programId,
      courseId,
      year: activeYear.get('year'),
      level: activeLevel.get('level'),
      term: activeTerm.get('term'),
      deliveryTypes: deliveryTypes
        ? deliveryTypes.map((item) => item.get('session_symbol', '')).toJS()
        : [],
      selectedGroups: deliveryTypes
        ? deliveryTypes.map((item) => item.get('selected', 1)).toJS()
        : [],
      gender: activeGender,
    };
    return params;
  };
  groupClick = () => {
    const { data, componentName, deliveryTypes, studentGroupedList } = this.props.GroupModalData;
    const { getScheduleCount } = this.props;
    if (
      componentName !== 'foundationCourse' &&
      componentName !== 'individualCourse' &&
      componentName !== 'rotationCourse' &&
      componentName !== 'electiveCourse'
    ) {
      var totalOccupancy =
        data.get('groups_list') &&
        data
          .get('groups_list')
          .entrySeq()
          .map((e) => {
            if (e[0] !== 'ungrouped' && e[0] !== 'all') {
              return e[1];
            } else {
              return 0;
            }
          })
          .reduce((acc, el) => {
            return acc + el;
          }, 0);
      if (totalOccupancy === 0) {
        this.setState({ groupEnable: true, automaticEnable: true });
        this.automatic();
      } else {
        this.setState({ groupEnable: true, automaticEnable: false });
        this.manual();
      }
    } else {
      const deliveryT = deliveryTypes && deliveryTypes.size > 0 && deliveryTypes.toJS();
      this.setState({
        delivery: deliveryT,
        groupEnable: true,
      });

      if (studentGroupedList.size === 0) {
        this.setState({
          automaticEnable: true,
        });
        this.automatic();
      } else {
        this.setState({
          automaticEnable: false,
        });
        this.manual();
      }
    }
    const params = this.getParams();
    if (this.wantToCallScheduleCountApi()) {
      getScheduleCount(params);
    }
  };

  manualSettings = () => {
    const { data, componentName } = this.props.GroupModalData;
    if (data.has('groups_list_capacity')) {
      return data
        .get('groups_list_capacity')
        .entrySeq()
        .map((e) => {
          let occupiedCount = data.getIn(['groups_list', e[0]], '');
          let changeName =
            componentName === 'foundation'
              ? e[0].replace('group', 'Foundation Group ')
              : e[0].replace('group', 'Rotation Group ');
          return [`${changeName} (${occupiedCount}/${e[1]})`, e[0]];
        })
        .toJS();
    }
    return [];
  };

  saveGrouping = () => {
    const wantToCallScheduleCountApi = this.wantToCallScheduleCountApi();
    const {
      activeTerm,
      activeLevel,
      activeGender,
      activeYear,
      GroupModalData,
      manualrequest,
      autorequest,
      resetMessage,
      totalScheduleCount,
    } = this.props;
    const { mode, selectAutoOption, delivery, scheduleStatus, automatic } = this.state;
    if (
      scheduleStatus === '' &&
      parseInt(totalScheduleCount) > 0 &&
      !automatic &&
      wantToCallScheduleCountApi
    ) {
      return resetMessage('Select the Session Status');
    }
    const {
      selectedStudents,
      resetStudentArray,
      callBackFn,
      componentName,
      activeGroup,
      courseId,
    } = GroupModalData;

    if (this.state.manual) {
      let requestBody = {
        _id: activeYear.get('_id'),
        batch: activeTerm.get('term'),
        level: activeLevel.get('level'),
        gender: activeGender,
        mode: mode,
        _student_ids: selectedStudents,
      };
      if (scheduleStatus !== '' && parseInt(totalScheduleCount) > 0 && wantToCallScheduleCountApi) {
        requestBody['status'] = scheduleStatus;
      }
      let URL = `/${studentGroupUrl()}/grouping${studentGenderMode()}`;
      if (componentName === 'foundationCourse' || componentName === 'rotationCourse') {
        requestBody._course_id = courseId;
        requestBody.master_group = activeGroup.substring(5);
        requestBody.delivery_group =
          delivery &&
          delivery.map((item) => {
            return {
              session_type: item.session_symbol,
              group_no: item.selected !== undefined ? parseInt(item.selected) : 1,
            };
          });
        URL = `/${studentGroupUrl()}/sub_course_grouping${studentGenderMode()}`;
      } else if (componentName === 'individualCourse' || componentName === 'electiveCourse') {
        requestBody._course_id = courseId;
        requestBody.delivery_group =
          delivery &&
          delivery.map((item) => {
            return {
              session_type: item.session_symbol,
              group_no: item.selected !== undefined ? parseInt(item.selected) : 1,
            };
          });
        URL = `/${studentGroupUrl()}/individual_course_grouping/${studentGenderMode()}`;
      } else if (componentName === 'foundation') {
        requestBody.group_no = this.state.selectManual.substring(5);
      } else if (componentName === 'rotation') {
        requestBody.group_no = this.state.selectManual.substring(5);
        URL = `/${studentGroupUrl()}/rotation_grouping${studentGenderMode()}`;
      }
      manualrequest(URL, requestBody, () => {
        resetStudentArray();
        this.backClick();
        callBackFn();
      });
    } else {
      let requestBody = {
        _id: activeYear.get('_id'),
        batch: activeTerm.get('term'),
        level: activeLevel.get('level'),
        gender: activeGender,
        mode: mode,
        method: selectAutoOption,
        _student_ids: selectedStudents,
      };
      if (scheduleStatus !== '' && parseInt(totalScheduleCount) > 0 && wantToCallScheduleCountApi) {
        requestBody['status'] = scheduleStatus;
      }
      let URL = `/${studentGroupUrl()}/grouping/${studentGenderMode()}`;

      if (componentName === 'foundationCourse' || componentName === 'rotationCourse') {
        requestBody._course_id = courseId;
        requestBody.master_group = activeGroup.substring(5);
        requestBody.session_type = delivery && delivery.map((item) => item.session_symbol);
        URL = `/${studentGroupUrl()}/sub_course_grouping${studentGenderMode()}`;
      } else if (componentName === 'individualCourse' || componentName === 'electiveCourse') {
        requestBody._course_id = courseId;
        requestBody.session_type = delivery && delivery.map((item) => item.session_symbol);
        URL = `/${studentGroupUrl()}/individual_course_grouping/${studentGenderMode()}`;
      } else if (componentName === 'rotation') {
        URL = `/${studentGroupUrl()}/rotation_grouping${studentGenderMode()}`;
      }
      autorequest(URL, requestBody, () => {
        resetStudentArray();
        this.backClick();
        callBackFn();
      });
    }
  };

  backClick = () => {
    this.setState({
      groupEnable: false,
      selectManual: 'group1',
      selectAutoOption: AUTOMATIC_TYPES[0][1],
    });
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'selectAuto') {
      this.setState({
        selectAutoOption: e.target.value,
        selectAutoOptionError: '',
      });
    }

    if (name === 'selectManual') {
      this.setState({
        selectManual: e.target.value,
        selectManualError: '',
      });
    }
  };

  updateGroups = (e, index) => {
    const { componentName, isElective } = this.props.GroupModalData;
    const copyState = [...this.state.delivery];
    const { getScheduleCount } = this.props;
    let delivery;
    if (
      componentName === 'electiveCourse' ||
      (componentName === 'individualCourse' && isElective)
    ) {
      delivery = copyState.map((item) => {
        return { ...item, selected: e.target.value };
      });
      this.setState({ delivery: delivery });
    } else {
      const copyIndex = copyState[index];
      copyIndex['selected'] = e.target.value;
      copyState[index] = copyIndex;
      delivery = copyState;
      this.setState({ delivery: copyState });
    }
    const params = this.getParams(fromJS(delivery));
    if (this.wantToCallScheduleCountApi()) {
      getScheduleCount(params);
    }
  };

  render() {
    const {
      isEnabled,
      selectedStudents,
      data,
      componentName,
      isElective,
    } = this.props.GroupModalData;
    const wantToCallScheduleCountApi = this.wantToCallScheduleCountApi();
    const { totalScheduleCount } = this.props;
    const {
      groupEnable,
      manual,
      automaticEnable,
      automatic,
      selectManual,
      selectManualError,
      selectAutoOption,
      selectAutoOptionError,
      delivery,
      scheduleStatus,
    } = this.state;

    return (
      <>
        <Button
          variant={isEnabled ? 'primary' : 'secondary'}
          disabled={!isEnabled}
          onClick={this.groupClick}
        >
          <Trans i18nKey={'student_grouping.group'} />
        </Button>
        {isEnabled && groupEnable && (
          <Modal show={true} size="lg">
            <div className="container">
              <p className="f-20 mb-2 pt-3">
                {' '}
                {componentName !== 'foundationCourse' &&
                componentName !== 'individualCourse' &&
                componentName !== 'rotationCourse' &&
                componentName !== 'electiveCourse'
                  ? `Group ${componentName} year students`
                  : 'Group students'}{' '}
              </p>
            </div>

            <Modal.Body>
              <div className="model-main">
                <div className="container">
                  <ul id="menu-grouping">
                    {automaticEnable && (
                      <a
                        href="##"
                        style={{ cursor: 'pointer' }}
                        onClick={this.automatic}
                        className={`tabaligment-blue ${automatic ? 'tabactive-blue' : ''}`}
                      >
                        <Trans i18nKey={'student_grouping.automatic'} />{' '}
                      </a>
                    )}

                    <a
                      href="##"
                      style={{ cursor: 'pointer' }}
                      onClick={this.manual}
                      className={`tabaligment-blue ${manual ? 'tabactive-blue' : ''}`}
                    >
                      <Trans i18nKey={'student_grouping.manual'} />{' '}
                    </a>
                  </ul>

                  {automatic === true && (
                    <div className="row pb-4">
                      <div className="col-md-12">
                        <p className="pt-3 mb-1 f-16">
                          {' '}
                          <Trans i18nKey={'student_grouping.existing_grouping'} />
                        </p>
                        <Input
                          elementType={'radio3'}
                          elementConfig={AUTOMATIC_TYPES}
                          className={'form-radio1'}
                          selected={selectAutoOption}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'selectAuto')}
                          feedback={selectAutoOptionError}
                        />
                      </div>
                      <div className="col-md-12">
                        <p className="text-lightgray mb-0 pt-3">
                          <Trans i18nKey={'student_grouping.students_selected'} />:{' '}
                          {selectedStudents.length}
                        </p>
                      </div>

                      <div className="col-md-12 pt-3">
                        <p className="">Current groups availability</p>
                        <div className="table-shadow">
                          <Table responsive className="text-center">
                            <thead className="group_table_top">
                              {componentName === 'foundation' || componentName === 'rotation' ? (
                                <tr>
                                  {data.get('groups_list_capacity') &&
                                    data
                                      .get('groups_list_capacity')
                                      .entrySeq()
                                      .map((e, i) => {
                                        return (
                                          <th className="border_color_blue" key={i}>
                                            {`${componentName === 'foundation' ? 'Fndn.' : 'Rtn.'}`}{' '}
                                            Group {e[0].substring(5)}
                                          </th>
                                        );
                                      })}
                                </tr>
                              ) : componentName === 'foundationCourse' ||
                                componentName === 'individualCourse' ||
                                componentName === 'rotationCourse' ||
                                componentName === 'electiveCourse' ? (
                                <tr>
                                  {delivery &&
                                    delivery.length > 0 &&
                                    delivery.map((e, i) => {
                                      return (
                                        <th key={i} className="border_color_blue">
                                          {e.session_symbol}
                                        </th>
                                      );
                                    })}
                                </tr>
                              ) : (
                                ''
                              )}
                            </thead>

                            <tbody>
                              {componentName === 'foundation' || componentName === 'rotation' ? (
                                <tr className="">
                                  {data
                                    .get('groups_list_capacity')
                                    .filter((v, k) => k !== 'ungrouped' || k !== 'all')
                                    .valueSeq()
                                    .map((e, index) => {
                                      let occupiedCount = data.getIn(
                                        ['groups_list', 'group' + (index + 1)],
                                        0
                                      );
                                      let remainingCount = e - occupiedCount;
                                      return (
                                        <td key={index}>
                                          <h5>
                                            <Badge
                                              variant="light"
                                              style={{
                                                width: '5em',
                                                padding: '8px',
                                                backgroundColor: '#e7e7e7',
                                              }}
                                            >
                                              {remainingCount < 0 ? 0 : remainingCount}
                                            </Badge>{' '}
                                          </h5>
                                        </td>
                                      );
                                    })}
                                </tr>
                              ) : componentName === 'foundationCourse' ||
                                componentName === 'individualCourse' ||
                                componentName === 'rotationCourse' ||
                                componentName === 'electiveCourse' ? (
                                <tr>
                                  {delivery &&
                                    delivery.length > 0 &&
                                    delivery.map((e, i) => {
                                      return (
                                        <td key={i}>
                                          <h5>
                                            {Array.from(Array(e.no_of_groups), (x, i) => i + 1).map(
                                              (group, ins) => (
                                                <p className="mb-1" key={ins}>
                                                  <Badge
                                                    variant="light"
                                                    style={{
                                                      width: '5em',
                                                      padding: '8px',
                                                      backgroundColor: '#e7e7e7',
                                                    }}
                                                  >
                                                    <tr>{`G${group}(${e.no_of_student})`}</tr>
                                                  </Badge>
                                                </p>
                                              )
                                            )}
                                          </h5>
                                        </td>
                                      );
                                    })}
                                </tr>
                              ) : (
                                ''
                              )}
                            </tbody>
                          </Table>
                        </div>
                      </div>
                    </div>
                  )}

                  {manual === true && (
                    <div className="row pb-4">
                      <div className="col-md-12">
                        <p className="pt-3 pb-1  f-16">
                          <Trans i18nKey={'student_grouping.students_selected'} />:{' '}
                          {selectedStudents.length}
                        </p>
                        {componentName !== 'foundationCourse' &&
                        componentName !== 'individualCourse' &&
                        componentName !== 'rotationCourse' &&
                        componentName !== 'electiveCourse' ? (
                          <p className="pt-1 mb-1 f-16">
                            {' '}
                            <Trans i18nKey={'student_grouping.select_student_group'} />{' '}
                          </p>
                        ) : (
                          <>
                            <p className="mb-2 f-16">
                              {' '}
                              <Trans i18nKey={'student_grouping.add_selected_students'} />{' '}
                            </p>
                            <p className="mb-2 f-16">
                              {' '}
                              <Trans i18nKey={'student_grouping.select_delivery'} />{' '}
                            </p>
                          </>
                        )}
                        {componentName !== 'foundationCourse' &&
                        componentName !== 'individualCourse' &&
                        componentName !== 'rotationCourse' &&
                        componentName !== 'electiveCourse' ? (
                          <Input
                            elementType={'radio3'}
                            elementConfig={this.manualSettings()}
                            className={'form-radio1'}
                            selected={selectManual}
                            labelclass="radio-label2"
                            onChange={(e) => this.onRadioGroupChange(e, 'selectManual')}
                            feedback={selectManualError}
                          />
                        ) : (
                          delivery &&
                          delivery.map((item, index) => {
                            let groupArray = [];
                            let totalCount = item.no_of_groups;
                            let totalStudents = item.no_of_student;
                            for (var i = 1; i <= totalCount; i++) {
                              groupArray.push({ name: `Group ${i} (${totalStudents})`, value: i });
                            }
                            return (
                              <div className="row" key={index}>
                                <div className="col-md-1 pt-2">
                                  <p className="pt-4"> {item.session_symbol} </p>
                                </div>
                                <div className="col-md-6">
                                  <Input
                                    elementType={'floatingselect'}
                                    elementConfig={{
                                      options: groupArray,
                                    }}
                                    value={item.selected !== undefined ? item.selected : ''}
                                    changed={(e) => this.updateGroups(e, index)}
                                    disabled={
                                      (componentName === 'electiveCourse' && index !== 0) ||
                                      (componentName === 'individualCourse' &&
                                        index !== 0 &&
                                        isElective)
                                    }
                                  />
                                </div>
                              </div>
                            );
                          })
                        )}
                      </div>
                    </div>
                  )}
                  {parseInt(totalScheduleCount) > 0 && !automatic && wantToCallScheduleCountApi && (
                    <div>
                      <p>
                        <span className="f-16 gray-neutral">
                          Record the session status for the previously completed session :
                        </span>{' '}
                        <span className="f-14 text-light-grey">
                          Total Completed Session - {totalScheduleCount}
                        </span>{' '}
                      </p>
                      <TextField
                        sx={{ minWidth: '180px' }}
                        id="outlined-select-currency"
                        select
                        onChange={(e) => this.setState({ scheduleStatus: e.target.value })}
                        label="Select Status"
                      >
                        {record_session_status.map((option) => (
                          <MenuItem
                            key={option.value}
                            value={option.value}
                            selected={scheduleStatus === option.value}
                          >
                            {option.name}
                          </MenuItem>
                        ))}
                      </TextField>
                    </div>
                  )}
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <div className="container">
                <div className="row">
                  <div className="col-md-8">
                    {/* {manual === true && (
                      <p className="mb-0 text-lightgray mt-2">
                        {selectedStudents.length} students remaining
                      </p>
                    )} */}
                  </div>

                  <div className="col-md-4">
                    <div className="float-right">
                      <b className="pr-3">
                        <Button variant="outline-secondary" onClick={this.backClick}>
                          <Trans i18nKey={'student_grouping.back'} />
                        </Button>
                      </b>

                      <b className="pr-2">
                        <Button variant="primary" onClick={this.saveGrouping}>
                          <Trans i18nKey={'student_grouping.save'} />
                        </Button>
                      </b>
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Footer>
          </Modal>
        )}
      </>
    );
  }
}

GroupButton.propTypes = {
  activeTerm: PropTypes.instanceOf(Map),
  activeYear: PropTypes.instanceOf(Map),
  activeLevel: PropTypes.instanceOf(Map),
  activeGender: PropTypes.instanceOf(Map),
  GroupModalData: PropTypes.instanceOf(Map),
  manualrequest: PropTypes.func,
  getScheduleCount: PropTypes.func,
  resetMessage: PropTypes.func,
  totalScheduleCount: PropTypes.number,
  autorequest: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    activeTerm: selectActiveTerm(state),
    activeYear: selectActiveYear(state),
    activeLevel: selectActiveLevel(state),
    activeGender: selectActiveGender(state),
    totalScheduleCount: selectTotalScheduleCount(state),
  };
};

export default connect(mapStateToProps, actions)(GroupButton);
