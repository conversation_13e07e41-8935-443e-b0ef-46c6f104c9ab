import React from 'react';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { useStylesFunction } from './designUtils';
import SettingsIcon from '@mui/icons-material/Settings';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import dotIcon from '../../../Assets/dotIcon.svg';
import {
  Menu,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Divider,
  FormControlLabel,
} from '@mui/material';

function AttainmentReport(props) {
  const classes = useStylesFunction();
  const program = [
    {
      name: 'program01',
      value: 'C01',
    },
    {
      name: 'program02',
      value: 'C02',
    },
  ];
  const regulation = [
    {
      name: 'regulation Name',
      value: 'C01',
    },
    {
      name: 'regulation 2',
      value: 'C02',
    },
  ];

  const co = [
    {
      name: 'co',
      value: 'co',
    },
    {
      name: 'co 2',
      value: 'co',
    },
  ];

  const year = [
    {
      name: 'AY - 2021 - 2022',
      value: 'AY - 2021 - 2022',
    },
    {
      name: 'AY - 2012 - 2023',
      value: 'AY - 2021 - 2022',
    },
  ];

  const regular = [
    {
      name: 'Regular',
      value: 'AY - 2021 - 2022',
    },
    {
      name: 'Regular2',
      value: 'AY - 2021 - 2022',
    },
  ];
  const level = [
    {
      name: 'L1',
      value: 'AY - 2021 - 2022',
    },
    {
      name: 'L2',
      value: 'AY - 2021 - 2022',
    },
  ];
  const Course = [
    {
      name: 'Course',
      value: 'AY - 2021 - 2022',
    },
    {
      name: 'Course2 ',
      value: 'AY - 2021 - 2022',
    },
  ];

  const Class = [
    {
      name: 'Class Attainment',
      value: 'AY - 2021 - 2022',
    },
    {
      name: 'Class Attainment 2 ',
      value: 'AY - 2021 - 2022',
    },
  ];
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const ITEM_HEIGHT = 48;

  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="container">
        <div className="d-flex flex-row pt-3 pb-1">
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: program }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: regulation }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: co }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: year }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: regular }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: level }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: Course }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
          <div className="w-13 pr-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: Class }}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>
        </div>
        <div className="d-flex justify-content-end border-bottom pb-3">
          <div className="pr-2">
            <MButton
              variant="contained"
              color={'white'}
              className="bold mb-2 mt-3"
              startIcon={<SettingsIcon />}
            >
              {' '}
              Manage Attainment
            </MButton>
          </div>

          <div className="pr-2">
            <MButton variant="contained" color={'blue'} className="bold mb-2 mt-3">
              {' '}
              EXPORT
            </MButton>
          </div>
        </div>

        <div className="attainment_table mt-4 mb-3">
          <table align="left">
            <thead>
              <tr>
                <th scope="col" className="borderNone">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">Node</p>
                    </div>
                    <div className="col-6 pl-0">
                      <p className="thHeaderReport text-left">Overall Attainment</p>
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">Intern (50) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>

                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">Select MT </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">IT 1 (100) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">External(50) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">UE (50) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th scope="col" className="attainment_border_bottom bg-gray">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">Attainment</p>
                    </div>
                    <div className="col-6 pl-0">
                      <p className="thHeaderReport ">%</p>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>
              </tr>

              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">90%</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                              L3
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">---</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue">---</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">---</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue">---</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <Menu
            id="long-menu"
            anchorEl={anchorEl}
            keepMounted
            open={open}
            onClose={handleClose}
            PaperProps={{
              style: {
                maxHeight: ITEM_HEIGHT * 10.5,
                width: '31.2ch',
              },
            }}
          >
            <div className="">
              <p className="bold pt-2 pl-3 mb-2"> Mid Term</p>
              <ListItem button>
                <ListItemIcon className="minWidth-32">
                  <Checkbox
                    edge="start"
                    // checked={}
                    tabIndex={-1}
                    disableRipple
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText
                  primary="Mid Term 1"
                  secondary={
                    <div>
                      {' '}
                      <span className=""> M 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Q 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> 17 Aug 2022 </span>
                    </div>
                  }
                />
              </ListItem>
              <Divider />
              <ListItem button>
                <ListItemIcon className="minWidth-32">
                  <Checkbox
                    edge="start"
                    // checked={}
                    tabIndex={-1}
                    disableRipple
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText
                  primary="Mid Term 2"
                  secondary={
                    <div>
                      {' '}
                      <span className=""> M 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Q 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> 17 Aug 2022 </span>
                    </div>
                  }
                />
              </ListItem>
              <Divider />
              <ListItem button>
                <ListItemIcon className="minWidth-32">
                  <Checkbox
                    edge="start"
                    // checked={}
                    tabIndex={-1}
                    disableRipple
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText
                  primary="Mid Term 3"
                  secondary={
                    <div>
                      {' '}
                      <span className=""> M 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Q 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> 17 Aug 2022 </span>
                    </div>
                  }
                />
              </ListItem>
            </div>
          </Menu>
        </div>

        <div className="border border-radious-6 mt-4 mb-3">
          <div className="attainment_table">
            <table align="left">
              <thead>
                <tr>
                  <th scope="col" className="borderNone">
                    <div className="cw_300 row">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport text-left">Node</p>
                      </div>
                      <div className="col-6 pl-0">
                        <p className="thHeaderReport text-left">Overall Attainment</p>
                      </div>
                    </div>
                  </th>
                  <th scope="col" className="borderNone">
                    <div
                      className="cw_160 d-flex justify-content-between align-items-center bg-white border-radious-4  m-2"
                      onClick={handleClick}
                    >
                      <p className="thHeaderReport  pl-2">MT 1 (100) </p>
                      <div className="pt-1 remove_hover">
                        <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                      </div>
                    </div>
                  </th>

                  <th scope="col" className="borderNone">
                    <div
                      className="cw_160 d-flex justify-content-between align-items-center bg-white border-radious-4  m-2"
                      onClick={handleClick}
                    >
                      <p className="thHeaderReport  pl-2">IT 1 (100) </p>
                      <div className="pt-1 remove_hover">
                        <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                      </div>
                    </div>
                  </th>
                  <th scope="col" className="borderNone">
                    <div
                      className="cw_160 d-flex justify-content-between align-items-center bg-white border-radious-4  m-2"
                      onClick={handleClick}
                    >
                      <p className="thHeaderReport  pl-2">A 1 (100) </p>
                      <div className="pt-1 remove_hover">
                        <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                      </div>
                    </div>
                  </th>
                  <th scope="col" className="borderNone">
                    <div
                      className="cw_160 d-flex justify-content-between align-items-center bg-white border-radious-4  m-2"
                      onClick={handleClick}
                    >
                      <p className="thHeaderReport  pl-2">Intern (50) </p>
                      <div className="pt-1 remove_hover">
                        <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                      </div>
                    </div>
                  </th>
                  <th scope="col" className="borderNone">
                    <div
                      className="cw_160 d-flex justify-content-between align-items-center bg-white border-radious-4  m-2"
                      onClick={handleClick}
                    >
                      <p className="thHeaderReport pl-2">UE (50) </p>
                      <div className="pt-1 remove_hover">
                        <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                      </div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <th scope="col" className="attainment_border_bottom bg-gray">
                    <div className="cw_300 row">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport text-left">Attainment</p>
                      </div>
                      <div className="col-6 pl-0">
                        <p className="thHeaderReport ">%</p>
                      </div>
                    </div>
                  </th>

                  <td className="attainment_border_bottom bg-gray">
                    <p className="thHeaderAttainment f-15">%</p>
                  </td>

                  <td className="attainment_border_bottom bg-gray">
                    <p className="thHeaderAttainment f-15">%</p>
                  </td>

                  <td className="attainment_border_bottom bg-gray">
                    <p className="thHeaderAttainment f-15">Attainment</p>
                  </td>

                  <td className="attainment_border_bottom bg-gray">
                    <p className="thHeaderAttainment f-15">Attainment</p>
                  </td>

                  <td className="attainment_border_bottom bg-gray">
                    <p className="thHeaderAttainment f-15">%</p>
                  </td>
                </tr>

                <tr>
                  <th scope="col" className="attainment_border_bottom bg-white">
                    <div className="cw_300 row">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport text-left">C01</p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="row align-items-center">
                          <div className="col-6 pr-0">
                            <p className="thHeaderReport" style={{ color: 'green' }}>
                              90%
                            </p>
                          </div>
                          <div className="col-6 pl-0">
                            <div className="d-flex justify-content-center ">
                              <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                                L3
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </th>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: 'green' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#D97706' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#FDE68A' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#147AFC' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#E1F5FA' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#DC2626' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#FECACA' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: 'green' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <th scope="col" className="attainment_border_bottom bg-white">
                    <div className="cw_300 row">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport text-left">C02</p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="row align-items-center">
                          <div className="col-6 pr-0">
                            <p className="thHeaderReport" style={{ color: 'green' }}>
                              10%
                            </p>
                          </div>
                          <div className="col-6 pl-0">
                            <div className="d-flex justify-content-center ">
                              <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                                L3
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </th>
                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: 'green' }}>
                          15%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                            L1
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#DC2626' }}>
                          10%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#FECACA' }}>
                            L1
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: 'green' }}>
                          80%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                            L0
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#D97706' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#FDE68A' }}>
                            L5
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#147AFC' }}>
                          3%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#E1F5FA' }}>
                            L2
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>

                <tr>
                  <th scope="col" className="attainment_border_bottom bg-white">
                    <div className="cw_300 row">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport text-left">C01</p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="row align-items-center">
                          <div className="col-6 pr-0">
                            <p className="thHeaderReport" style={{ color: 'green' }}>
                              90%
                            </p>
                          </div>
                          <div className="col-6 pl-0">
                            <div className="d-flex justify-content-center ">
                              <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                                L3
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </th>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#DC2626' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#FECACA' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: 'green' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#D97706' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#FDE68A' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: '#147AFC' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#E1F5FA' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="attainment_border_bottom ">
                    <div className="row align-items-center">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport" style={{ color: 'green' }}>
                          90%
                        </p>
                      </div>
                      <div className="col-6 pl-0">
                        <div className="d-flex justify-content-center ">
                          <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                            L3
                          </p>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="d-flex justify-content-end">
            <FormControlLabel
              value="end"
              control={<Checkbox color="default" size="small" className={classes.green} checked />}
              label="Attainment Achived"
              labelPlacement="end"
              className="mb-0"
            />

            <FormControlLabel
              value="end"
              control={<Checkbox color="default" size="small" className={classes.green} />}
              label="Attainment Not Achived"
              labelPlacement="end"
              className="mb-0"
            />
          </div>
        </div>

        <div className="attainment_table mt-4 mb-3">
          <table align="left">
            <thead>
              <tr>
                <th scope="col" className="borderNone">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">Node</p>
                    </div>
                    <div className="col-6 pl-0">
                      <p className="thHeaderReport text-left">Overall Attainment</p>
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">Intern (50) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>

                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">Select MT </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">IT 1 (100) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">External(50) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
                <th scope="col" className="borderNone">
                  <div
                    className="cw_160 d-flex justify-content-between align-items-center bg-gray border-radious-4  m-2"
                    onClick={handleClick}
                  >
                    <p className="thHeaderReport text-NewWarning pl-2">UE (50) </p>
                    <div className="pt-1 remove_hover">
                      <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
                    </div>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th scope="col" className="attainment_border_bottom bg-gray">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">Attainment</p>
                    </div>
                    <div className="col-6 pl-0">
                      <p className="thHeaderReport ">%</p>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>
              </tr>

              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">90%</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                              L3
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">---</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue">---</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">---</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue">---</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>

            <tbody>
              <tr>
                <th scope="col" className="attainment_border_bottom bg-gray">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">Attainment</p>
                    </div>
                    <div className="col-6 pl-0">
                      <p className="thHeaderReport ">%</p>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>

                <td className="attainment_border_bottom bg-gray">
                  <p className="thHeaderAttainment f-15">%</p>
                </td>
              </tr>

              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">90%</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue" style={{ backgroundColor: '#BBF7D0' }}>
                              L3
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">---</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue">---</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <th scope="col" className="attainment_border_bottom bg-white">
                  <div className="cw_300 row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">C01</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport">---</p>
                        </div>
                        <div className="col-6 pl-0">
                          <div className="d-flex justify-content-center ">
                            <p className="innerValue">---</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </th>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>

                <td className="attainment_border_bottom ">
                  <div className="row align-items-center">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport">---</p>
                    </div>
                    <div className="col-6 pl-0">
                      <div className="d-flex justify-content-center ">
                        <p className="innerValue">---</p>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <Menu
            id="long-menu"
            anchorEl={anchorEl}
            keepMounted
            open={open}
            onClose={handleClose}
            PaperProps={{
              style: {
                maxHeight: ITEM_HEIGHT * 10.5,
                width: '31.2ch',
              },
            }}
          >
            <div className="">
              <p className="bold pt-2 pl-3 mb-2"> Mid Term</p>
              <ListItem button>
                <ListItemIcon className="minWidth-32">
                  <Checkbox
                    edge="start"
                    // checked={}
                    tabIndex={-1}
                    disableRipple
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText
                  primary="Mid Term 1"
                  secondary={
                    <div>
                      {' '}
                      <span className=""> M 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Q 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> 17 Aug 2022 </span>
                    </div>
                  }
                />
              </ListItem>
              <Divider />
              <ListItem button>
                <ListItemIcon className="minWidth-32">
                  <Checkbox
                    edge="start"
                    // checked={}
                    tabIndex={-1}
                    disableRipple
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText
                  primary="Mid Term 2"
                  secondary={
                    <div>
                      {' '}
                      <span className=""> M 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Q 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> 17 Aug 2022 </span>
                    </div>
                  }
                />
              </ListItem>
              <Divider />
              <ListItem button>
                <ListItemIcon className="minWidth-32">
                  <Checkbox
                    edge="start"
                    // checked={}
                    tabIndex={-1}
                    disableRipple
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText
                  primary="Mid Term 3"
                  secondary={
                    <div>
                      {' '}
                      <span className=""> M 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> Q 10 </span>
                      <img src={dotIcon} alt="Deactivated" />
                      <span className="mr-1 ml-1 f-14"> 17 Aug 2022 </span>
                    </div>
                  }
                />
              </ListItem>
            </div>
          </Menu>
        </div>
      </div>
    </div>
  );
}

export default AttainmentReport;
