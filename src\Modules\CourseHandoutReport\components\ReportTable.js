import React, { useEffect, useRef, useState } from 'react';
import { List, Set, Map } from 'immutable';
import moment from 'moment';
import { getInfraName, getStatusColor } from 'Modules/InstitutionReport/utils';
import { getVersionName, jsUcfirstAll } from 'utils';
import MergeIcon from 'Assets/merge_new.svg';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import RectangleRoundedIcon from '@mui/icons-material/RectangleRounded';
import Pagination from 'Modules/StudentGrouping/components/Pagination';
import {
  Menu,
  MenuItem,
  Checkbox,
  IconButton,
  TextField,
  InputAdornment,
  Badge,
  Button,
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import SearchIcon from '@mui/icons-material/Search';
import { CapitalizeAll } from 'Modules/ReportsAndAnalytics/utils';

function ReportTable({ courseReport, paginationData, resetFiltersTrigger }) {
  const initialState = Map({
    Programs: Set(),
    Course: Set(),
    Delivery: Set(),
    Staff: Set(),
    Mode: Set(),
    'Scheduled Info': Set(),
    'Course Handouts': Set(),
    // Remarks: Set(),
    Status: Set(),
  });
  // const actualData = courseReport.get('scheduleList', List());
  const handoutList = courseReport.get('courseHandout', List());
  const fullData = courseReport.get('fullScheduleList', List());
  const [anchorEl, setAnchorEl] = useState(null);
  const [activeColumn, setActiveColumn] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState(initialState);

  useEffect(() => {
    if (paginationData.onFullLastClick) {
      paginationData.onFullLastClick();
    }
  }, [filters]);

  useEffect(() => {
    setFilters(initialState);
    setSearchText('');
  }, [resetFiltersTrigger]);

  const handleFilterClick = (event, column) => {
    setAnchorEl(event.currentTarget);
    setActiveColumn(column);
    setSearchText('');
  };

  const handleFilterClose = () => {
    setAnchorEl(null);
    setActiveColumn(null);
    setSearchText('');
  };

  const inputRef = useRef(null);

  useEffect(() => {
    searchText && setSearchText('');
    activeColumn && inputRef.current?.focus();
  }, [activeColumn]);

  const handleFilterChange = (value) => {
    setFilters((prev) => {
      const currentSet = prev.get(activeColumn);
      const newSet = currentSet.has(value) ? currentSet.delete(value) : currentSet.add(value);
      return prev.set(activeColumn, newSet);
    });
  };

  const handleSearchChange = (event) => {
    event.stopPropagation();
    const value = event.target.value.trim();
    setSearchText(value);
  };

  const getFilteredDataForColumn = (excludeColumn) => {
    return fullData.filter((item) => {
      return filters.every((values, column) => {
        if (column === excludeColumn || values.isEmpty()) return true;
        let colorStatus;
        switch (column) {
          case 'Programs':
            return values.has(item.get('program_name', ''));
          case 'Course':
            return values.has(getCourseNameWithRotation(item));
          case 'Delivery':
            return values.has(renderDelivery(item).props.children[1]);
          case 'Staff':
            return item
              .get('staffs', List())
              .some((staff) =>
                values.has(
                  `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
                    ['staff_name', 'last'],
                    ''
                  )}`
                )
              );
          case 'Mode':
            return values.has(getInfraName(item));
          case 'Scheduled Info':
            return values.has(getScheduleDateTime(item).date);
          case 'Course Handouts':
            colorStatus = handoutList.find(
              (detail) => detail.get('colorCode') === item.get('handoutColor', '')
            );
            return values.has(colorStatus?.get('status', '') || '---');
          // case 'Remarks':
          //   return item.get('sessionStatus', List()).some((status) => values.has(status));
          case 'Status':
            return values.has(getStatusColor(item).name);
          default:
            return true;
        }
      });
    });
  };

  const getColumnValues = (column) => {
    const filteredData = getFilteredDataForColumn(column);
    switch (column) {
      case 'Programs':
        return filteredData
          .map((item) => item.get('program_name', ''))
          .toSet()
          .toList();
      case 'Course':
        return filteredData
          .map((item) => getCourseNameWithRotation(item))
          .toSet()
          .toList();
      case 'Delivery':
        return filteredData
          .map((item) => renderDelivery(item).props.children[1])
          .toSet()
          .toList();
      case 'Staff':
        return filteredData
          .flatMap((item) =>
            item
              .get('staffs', List())
              .map(
                (staff) =>
                  `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
                    ['staff_name', 'last'],
                    ''
                  )}`
              )
          )
          .toSet()
          .toList();
      case 'Mode':
        return filteredData
          .map((item) => getInfraName(item))
          .toSet()
          .toList();
      case 'Scheduled Info':
        return filteredData
          .map((item) => getScheduleDateTime(item).date)
          .toSet()
          .toList();
      case 'Course Handouts':
        return filteredData
          .map((item) => {
            const colorStatus = handoutList.find(
              (detail) => detail.get('colorCode') === item.get('handoutColor', '')
            );
            return colorStatus?.get('status', '') || '---';
          })
          .filter((status) => status && status !== '---')
          .toSet()
          .toList();
      // case 'Remarks':
      //   return filteredData
      //     .flatMap((item) => item.get('sessionStatus', List()))
      //     .toSet()
      //     .toList();
      case 'Status':
        return filteredData
          .map((item) => getStatusColor(item).name)
          .toSet()
          .toList();
      default:
        return List();
    }
  };

  const getFilteredColumnValues = (column) => {
    const values = getColumnValues(column);
    if (!searchText) return values;

    const searchLower = searchText.toLowerCase();
    return values.filter((value) => {
      if (!value) return false;
      const valueStr = String(value).toLowerCase();
      return valueStr.includes(searchLower);
    });
  };

  const getScheduleDateTime = (item) => {
    const start = moment(item.get('scheduleStartDateAndTime'));
    const end = moment(item.get('scheduleEndDateAndTime'));
    return {
      date: start.format('Do MMM - YYYY'),
      time: `${start.format('h:mm A')} - ${end.format('h:mm A')}`,
    };
  };

  const getCourseNameWithRotation = (item) =>
    `${item.get('course_code', '')} - ${item.get('course_name', '')}` +
    (item.get('rotation_count') ? ` / Rotation ${item.get('rotation_count')}` : '');

  const renderDelivery = (item) => {
    const base = `${item.get('title', '')}${item.getIn(['session', 'delivery_symbol'], '')}`;
    const merged = item
      .get('merge_with', List())
      .map((mergeItem, index) => {
        const delivery = `${mergeItem.getIn(['schedule_id', 'session', 'delivery_symbol'], '')}`;
        return `${index === 0 ? ', ' : ''}${delivery}`;
      })
      .join(', ');
    return (
      <div className="aw-100">
        {item.get('merge_with', List()).size > 0 && (
          <img src={MergeIcon} alt="merge" className="mr-2" />
        )}
        {base}
        {merged}
      </div>
    );
  };

  const renderDeliveryText = (item) => {
    const base = `${item.get('title', '')}${item.getIn(
      ['session', 'delivery_symbol'],
      ''
    )}${item.getIn(['session', 'delivery_no'], '')}`;
    const merged = item
      .get('merge_with', List())
      .map((mergeItem, index) => {
        const delivery = `${mergeItem.getIn(
          ['schedule_id', 'session', 'delivery_symbol'],
          ''
        )}${mergeItem.getIn(['schedule_id', 'session', 'delivery_no'], '')}`;
        return `${index === 0 ? ', ' : ''}${delivery}`;
      })
      .join(', ');
    return (
      <div className="aw-100">
        {item.get('merge_with', List()).size > 0 && (
          <img src={MergeIcon} alt="merge" className="mr-2" />
        )}
        {base}
        {merged}
      </div>
    );
  };

  const renderStaffs = (staffList) =>
    staffList.map((staff, index) => (
      <div className="d-flex align-items-center mb-1" key={index}>
        <Tooltips title={`Contact Info - ${staff.getIn(['_staff_id', 'mobile'], 'N/A')}`}>
          <AccountCircleIcon fontSize="small" className="digi-gray" />
        </Tooltips>
        <p className="mb-0 ml-2">
          {jsUcfirstAll(
            `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
              ['staff_name', 'middle'],
              ''
            )} ${staff.getIn(['staff_name', 'last'], '')}`.toLowerCase()
          )}
        </p>
      </div>
    ));

  const renderHandout = (item) => {
    const colorCode = item.get('handoutColor', '')?.replace(/^['"]|['"]$/g, '');
    const colorStatus = handoutList.find(
      (detail) => detail.get('colorCode') === item.get('handoutColor', '')
    );

    return !colorCode ? (
      '---'
    ) : (
      <>
        <RectangleRoundedIcon sx={{ color: colorCode }} />
        <div className="text-capitalize">{colorStatus?.get('status', '')}</div>
      </>
    );
  };

  const renderStatusBlock = (item) => {
    const status = getStatusColor(item);
    return <div className={`aw-100 text-capitalize ${status.color}`}>{status.name}</div>;
  };

  const filteredData = fullData.filter((item) => {
    return filters.every((values, column) => {
      if (values.isEmpty()) return true;

      let colorStatus;

      switch (column) {
        case 'Programs':
          return values.has(item.get('program_name', ''));
        case 'Course':
          return values.has(getCourseNameWithRotation(item));
        case 'Delivery':
          return values.has(renderDelivery(item).props.children[1]);
        case 'Staff':
          return item
            .get('staffs', List())
            .some((staff) =>
              values.has(
                `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
                  ['staff_name', 'last'],
                  ''
                )}`
              )
            );
        case 'Mode':
          return values.has(getInfraName(item));
        case 'Scheduled Info':
          return values.has(getScheduleDateTime(item).date);
        case 'Course Handouts':
          colorStatus = handoutList.find(
            (detail) => detail.get('colorCode') === item.get('handoutColor', '')
          );
          return values.has(colorStatus?.get('status', '') || '---');
        // case 'Remarks':
        //   return item.get('sessionStatus', List()).some((status) => values.has(status));
        case 'Status':
          return values.has(getStatusColor(item).name);
        default:
          return true;
      }
    });
  });

  const pageCount = paginationData.pageCount || 10;
  const currentPage = paginationData.currentPage || 1;
  const paginatedData = filteredData.slice((currentPage - 1) * pageCount, currentPage * pageCount);
  const totalFilteredPages = Math.ceil(filteredData.size / pageCount);

  const handleClearAll = () => {
    setFilters(initialState);
    setSearchText('');
    if (paginationData.onFullLastClick) {
      paginationData.onFullLastClick();
    }
  };

  return (
    <div className="addSubTheme_bg">
      <div className="container pt-3 mb-5 pb-3">
        <div className="p-3 bg-white border-radious-4 mb-5">
          <div className="d-flex justify-content-between align-items-center border-bottom pb-3">
            <p className="mb-0 f-18 bold">All Handouts Detail</p>
            <Button variant="outlined" color="primary" onClick={handleClearAll} size="small">
              Clear All
            </Button>
          </div>
          <div className="go-wrapper">
            <table className="table">
              <thead>
                <tr>
                  {[
                    'Programs',
                    'Course',
                    'Delivery',
                    'Staff',
                    'Mode',
                    'Scheduled Info',
                    'Course Handouts',
                    // 'Remarks',
                    'Status',
                  ].map((header, idx) => (
                    <th key={idx}>
                      <div
                        className={`aw-${
                          [150, 150, 100, 200, 100, 150, 100, 150, 100][idx]
                        } d-flex align-items-center remove_hover`}
                      >
                        {header}
                        <IconButton
                          size="small"
                          onClick={(e) => handleFilterClick(e, header)}
                          className="ml-2"
                        >
                          <Badge
                            badgeContent={filters.get(header).size}
                            color="primary"
                            invisible={filters.get(header).size === 0}
                          >
                            <FilterListIcon fontSize="small" />
                          </Badge>
                        </IconButton>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="go-wrapper-height">
                {filteredData.size === 0 ? (
                  <tr className="tr-change f-18 digi-gray">
                    <td colSpan="10" className="text-center">
                      No Data Found ...
                    </td>
                  </tr>
                ) : (
                  paginatedData.map((item, index) => {
                    const { date, time } = getScheduleDateTime(item);
                    return (
                      <tr className="tr-change remove_hover f-15 digi-gray" key={index}>
                        <td>
                          <div className="aw-150">{item.get('program_name', '')}</div>
                        </td>
                        <td>
                          <div className="aw-150">
                            {getCourseNameWithRotation(item)}
                            {getVersionName(item)}
                          </div>
                        </td>
                        <td>{renderDeliveryText(item)}</td>
                        <td>
                          <div className="aw-200">{renderStaffs(item.get('staffs', List()))}</div>
                        </td>
                        <td>
                          <div className="aw-100 text-capitalize">{getInfraName(item)}</div>
                        </td>
                        <td>
                          <div className="aw-150">
                            <div>{date}</div>
                            <div>{time}</div>
                          </div>
                        </td>
                        <td>
                          <div className="aw-100">{renderHandout(item)}</div>
                        </td>
                        {/* <td>
                          <div className="aw-150">
                            {item.get('sessionStatus', List()).map((status, idx) => (
                              <div key={idx} className={status.replace(' ', '-')}>
                                {status}
                              </div>
                            ))}
                          </div>
                        </td> */}
                        <td>{renderStatusBlock(item)}</td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
            <Menu
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleFilterClose}
              PaperProps={{
                style: {
                  maxHeight: 300,
                  width: 250,
                },
              }}
            >
              <div className="p-2">
                <TextField
                  fullWidth
                  size="small"
                  placeholder="Search..."
                  value={searchText}
                  onChange={handleSearchChange}
                  onKeyDown={(e) => e.stopPropagation()}
                  onClick={(e) => e.stopPropagation()}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    ),
                  }}
                  inputRef={inputRef}
                />
              </div>
              {activeColumn &&
                getFilteredColumnValues(activeColumn).map((value) => (
                  <MenuItem
                    key={value}
                    style={{ whiteSpace: 'normal' }}
                    onClick={() => handleFilterChange(value)}
                  >
                    <Checkbox checked={filters.get(activeColumn).has(value)} />
                    <div style={{ wordBreak: 'break-word', paddingLeft: '8px' }}>
                      {CapitalizeAll(value)}
                    </div>
                  </MenuItem>
                ))}
            </Menu>
          </div>
          <Pagination
            pagination={paginationData.pagination}
            onNextClick={paginationData.onNextClick}
            pagevalue={paginationData.pageCount}
            onBackClick={paginationData.onBackClick}
            onFullLastClick={paginationData.onFullLastClick}
            onFullForwardClick={paginationData.onFullForwardClick}
            data={totalFilteredPages}
            currentPage={paginationData.currentPage}
          />
        </div>
      </div>
    </div>
  );
}

export default ReportTable;
