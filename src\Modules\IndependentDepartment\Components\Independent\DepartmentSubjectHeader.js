import React, { useState, useContext, Suspense, useEffect, useRef, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import { departmentValidation } from 'Modules/ProgramInput/v2/piUtil';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import { styled } from '@mui/material/styles';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { getShortString, GetShortString } from 'Modules/Shared/v2/Configurations';
import { getProgramData, getProgramName } from '../../utils';
import i18n from '../../../../i18n';

const AddDepartment = React.lazy(() =>
  import('Modules/ProgramInput/v2/ConfigurationIndex/Departments/Modal/AddDepartment')
);

const IOSSwitch = styled((props) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 42,
  height: 26,
  padding: 0,
  '& .MuiSwitch-switchBase': {
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&.Mui-checked': {
      transform: 'translateX(16px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-focusVisible .MuiSwitch-thumb': {
      color: '#33cf4d',
      border: '6px solid #fff',
    },
    '&.Mui-disabled .MuiSwitch-thumb': {
      color: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600],
    },
    '&.Mui-disabled + .MuiSwitch-track': {
      opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 22,
    height: 22,
  },
  '& .MuiSwitch-track': {
    borderRadius: 26 / 2,
    backgroundColor: theme.palette.mode === 'light' ? '#E9E9EA' : '#39393D',
    opacity: 1,
    transition: theme.transitions.create(['background-color'], {
      duration: 500,
    }),
  },
}));

function DepartmentSubjectHeader({
  showShared,
  setShowShared,
  search,
  setSearch,
  clearAll,
  fetchIndependentDepartmentApi,
}) {
  const addDepartment = useContext(parentContext.departmentContext);
  const {
    departmentsOperation,
    setData,
    translateInput,
    isIndependent,
    getProgramList,
    programInputList,
    departmentTabValue,
  } = addDepartment;

  const [addShow, setAddShow] = useState(false);
  const initialSharedRender = useRef(true);
  const initialSearchRender = useRef(true);

  const programList = useMemo(() => {
    return getProgramData(programInputList);
  }, [programInputList]);

  useEffect(() => {
    if (initialSharedRender.current) {
      initialSharedRender.current = false;
    } else {
      fetchIndependentDepartmentApi();
    }
  }, [showShared]); // eslint-disable-line

  useEffect(() => {
    if (initialSearchRender.current) {
      initialSearchRender.current = false;
    } else {
      const timeout = setTimeout(() => {
        fetchIndependentDepartmentApi();
      }, 500);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [search]); // eslint-disable-line

  const handleSearchChange = (event) => {
    setSearch(event);
  };

  const onClickSaved = (departmentName, departmentType, programId) => {
    if (departmentType === 'academic') {
      const programName = getProgramName(programInputList, programId);
      if (departmentValidation(departmentName, setData))
        departmentsOperation(
          departmentName,
          setAddShow,
          'add',
          '',
          departmentType,
          programId,
          programName
        );
    } else {
      if (departmentValidation(departmentName, setData))
        departmentsOperation(departmentName, setAddShow, 'add', '', departmentType);
    }
  };

  useEffect(() => {
    getProgramList();
  }, []); // eslint-disable-line

  const handleChange = () => {
    setShowShared(!showShared);
    clearAll();
  };

  return (
    <>
      <div className="row align-items-center">
        <div className="col-md-12 col-xl-6 col-lg-6 col-12">
          {departmentTabValue === 'academic' && (
            <div className="row mt-3">
              <div className="col-md-12 col-xl-9 col-lg-9 col-12">
                <div className="d-flex justify-content-between digi-border-gray rounded align-items-center">
                  <p className="digi-mb-0  digi-pl-20">
                    <Trans
                      values={{
                        departmentName: getShortString(translateInput.departmentName, 10),
                        subjectName: getShortString(translateInput.subjectName, 8),
                      }}
                    >
                      Show_shared_and
                    </Trans>
                  </p>
                  <div className="">
                    <FormControlLabel
                      control={
                        <IOSSwitch
                          sx={{ m: 1 }}
                          defaultChecked={showShared}
                          onChange={handleChange}
                        />
                      }
                      label=""
                      className="mb-0"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="col-md-12 col-xl-6 col-lg-6 col-12">
          <div className="row float-xl-right align-items-center mt-3 pt-1">
            <div className="col-md-7 col-xl-6 col-7 col-lg-6 col-7">
              <div className="">
                <MaterialInput
                  elementType={'materialSearch'}
                  placeholder={i18n.t('user_management.Search')}
                  changed={(event) => {
                    handleSearchChange(event.target.value);
                  }}
                />{' '}
              </div>
            </div>
            <div className="col-md-5 col-xl-6 col-5 col-lg-6 col-sm-5 ">
              <MButton
                variant="outlined"
                className="digi-blue-button pb-2 pt-2 f-16"
                fullWidth={true}
                clicked={() => {
                  setAddShow(true);
                }}
              >
                <div className="d-flex">
                  <div>
                    <Trans
                      components={{
                        departmentName: (
                          <GetShortString el={translateInput.departmentName} length={11} />
                        ),
                      }}
                    >
                      department_restructured.add_new_department
                    </Trans>
                  </div>
                </div>
              </MButton>
            </div>
          </div>
        </div>
      </div>
      {addShow && (
        <Suspense fallback="">
          <AddDepartment
            show={addShow}
            setShow={setAddShow}
            onClickSaved={onClickSaved}
            isAdd={true}
            departmentName={''}
            translateInput={translateInput}
            isIndependent={isIndependent}
            programList={programList}
            programId={''}
            departmentType={''}
            departmentTabValue={departmentTabValue}
          />
        </Suspense>
      )}
    </>
  );
}

DepartmentSubjectHeader.propTypes = {
  children: PropTypes.array,
  showShared: PropTypes.bool,
  setShowShared: PropTypes.func,
  search: PropTypes.string,
  setSearch: PropTypes.func,
  departmentFilter: PropTypes.array,
  programFilter: PropTypes.array,
  subjectFilter: PropTypes.array,
  fetchIndependentDepartmentApi: PropTypes.func,
  clearAll: PropTypes.func,
};

export default React.memo(DepartmentSubjectHeader);
