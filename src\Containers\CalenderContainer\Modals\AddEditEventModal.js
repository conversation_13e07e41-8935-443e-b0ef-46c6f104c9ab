import React from 'react';
import { Modal } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import Input from 'Widgets/FormElements/Input/Input';
import DatePicker from 'react-datepicker';
import { getLang, showArabicMailShow, timeFormat } from 'utils';
import moment from 'moment';
import axios from '../../../axios';
import { t } from 'i18next';
const AddEditEventModal = (props) => {
  const {
    academicYearStartDate,
    academicYearEndDate,
    handleState,
    state,
    eventClose,
    fetchApi,
    show,
    flag,
    setData,
  } = props;
  const genderinput = [
    ['Male Only', 'male'],
    ['Female Only', 'female'],
  ];
  const lang = getLang();
  const iconForm = lang === 'ar' ? 'icon-form-arabic' : 'icon-form';
  const iconFormCalender = lang === 'ar' ? 'icon-form-calender-arabic' : 'icon-form-calender';
  const showArabic = showArabicMailShow();
  let event_whom = 'both';
  let event_gender;
  const validation = (e) => {
    let eventNameError = '';
    let eventDetailError = '';
    let selectTypeError = '';
    let startDateError = '';
    let endDateError = '';
    let selectVenueError = '';
    let genderError = '';
    if (state.choosegender === true) {
      if (state.selectgender === 'l') {
        genderError = t('chooseGender');
      }
    }
    if (state.selectType === '') {
      selectTypeError = t('chooseEventType');
    }
    if (state.valueCheck === true) {
      if (state.selectVenue === '') {
        selectVenueError = t('chooseVenueType');
      }
    }
    if (state.startDate === '') {
      startDateError = t('chooseStartDate');
    } else if (new Date(state.startDate) > new Date(state.endDate)) {
      startDateError = t('endDateGreat');
    }
    if (state.oneDay === false) {
      if (state.endDate === '') {
        endDateError = t('chooseEndDate');
      }
    }
    if (!state.eventName) {
      eventNameError = t('eventNameRequired');
    } else if (state.eventName.length <= 2) {
      eventNameError = t('minimumThreeChar');
    }
    if (
      eventNameError ||
      eventDetailError ||
      selectTypeError ||
      startDateError ||
      endDateError ||
      selectVenueError ||
      genderError
    ) {
      handleState({
        eventNameError,
        eventDetailError,
        selectTypeError,
        startDateError,
        endDateError,
        selectVenueError,
        genderError,
      });
      return false;
    }
    return true;
  };
  const handleEventSubmit = (e) => {
    let id = state.calendarId;
    localStorage.removeItem('eventDate');
    e.preventDefault();
    const { choosegender, selectgender } = state;
    let startDate = moment(state.startDate).format('YYYY-MM-DD');
    let endDate = moment(state.endDate).format('YYYY-MM-DD');
    if (state.oneDay === true) {
      handleState({
        endDate: state.startDate,
      });
      endDate = moment(state.startDate).format('YYYY-MM-DD');
    }
    let startTime = timeFormat(moment(state.startTime).format('H:mm') + ':00');
    let endTime = timeFormat(moment(state.endTime).format('H:mm') + ':00');
    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();
    if (!choosegender) {
      event_gender = 'both';
    } else {
      event_gender = selectgender;
    }
    let error = false;
    if (state.startDate !== '' && state.endDate !== '') {
      if (st.getTime() >= et.getTime()) {
        handleState({ timeError: t('endTimeGreat') });
        error = true;
      } else {
        handleState({ timeError: '' });
      }
    }
    if (validation() && !error) {
      const data = {
        event_calendar: 'institution',
        event_type: state.selectType,
        event_name: {
          first_language: state.eventName.trim(),
          second_language: state.arab1.trim(),
        },
        event_description: {
          first_language: state.eventDetail,
          second_language: state.arab2,
        },
        event_date: startDate,
        start_time: st.getTime(),
        end_time: et.getTime(),
        end_date: endDate,
        _infrastructure_id: state.selectVenue,
        _calendar_id: id,
        event_gender: event_gender,
        event_whom: event_whom,
      };
      handleState({
        isLoading: true,
      });
      axios
        .post(`institution_calendar_event`, data)
        .then((res) => {
          handleState({
            isLoading: false,
            selectType: '',
            eventName: '',
            arab1: '',
            eventDetail: '',
            arab2: '',
            startDate: '',
            startTime: new Date(),
            endTime: new Date(),
            endDate: '',
            selectVenue: '',
            oneDay: false,
            valueCheck: false,
            eventShow: false,
          });
          setData({ message: t('event_created_successfully') });
          setTimeout(() => {
            fetchApi();
          }, 200);
        })
        .catch((error) => {
          setData({ message: error.response.data.message });
          handleState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };
  const handleEditEventSubmit = (e) => {
    e.preventDefault();
    let startDate = moment(state.startDate).format('YYYY-MM-DD');
    let endDate = moment(state.endDate).format('YYYY-MM-DD');
    if (state.oneDay === true) {
      handleState({
        endDate: state.startDate,
      });
      endDate = moment(state.startDate).format('YYYY-MM-DD');
    }
    let st = '';
    let et = '';
    if (state.editStartTime === '') {
      let startTime = timeFormat(moment(state.startTimeView).format('H:mm'));
      st = moment(startDate + 'T' + startTime).toDate();
    } else {
      let startTime = timeFormat(moment(state.editStartTime).format('H:mm'));
      st = moment(startDate + 'T' + startTime).toDate();
    }
    if (state.editEndTime === '') {
      let endTime = timeFormat(moment(state.endTimeView).format('H:mm'));
      et = moment(endDate + 'T' + endTime).toDate();
    } else {
      let endTime = timeFormat(moment(state.editEndTime).format('H:mm'));
      et = moment(endDate + 'T' + endTime).toDate();
    }
    let error = false;
    if (state.startDate !== '' && state.endDate !== '') {
      if (st.getTime() >= et.getTime()) {
        handleState({ timeError: t('endTimeGreat') });
        error = true;
      } else {
        handleState({ timeError: '' });
      }
    }
    let eventDetail = state.eventDetail;
    if (state.eventDetail === '') {
      eventDetail = ' ';
    }
    if (validation() && !error) {
      const data = {
        event_calendar: 'institution',
        event_type: state.selectType,
        event_name: {
          first_language: state.eventName.trim(),
          second_language: state.arab1.trim(),
        },
        event_description: {
          first_language: eventDetail,
          second_language: state.arab2,
        },
        event_date: startDate,
        end_date: endDate,
        start_time: st.getTime(),
        end_time: et.getTime(),
      };
      handleState({
        isLoading: true,
      });
      axios
        .put(`institution_calendar_event/${state.evId}`, data)
        .then((res) => {
          handleState({
            isLoading: false,
            selectType: '',
            eventName: '',
            arab1: '',
            eventDetail: '',
            arab2: '',
            selectVenue: '',
            oneDay: false,
            valueCheck: false,
            eventEdit: false,
          });
          setData({ message: t('event_updated_successfully') });
          setTimeout(() => {
            fetchApi();
          }, 200);
        })
        .catch((error) => {
          setData({ message: error.response.data.message });
          handleState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };
  const handleChange = (e, name) => {
    if (name === 'eventName') {
      handleState({
        eventName: e.target.value,
        eventNameError: '',
      });
    }
    if (name === 'eventDetail') {
      handleState({
        eventDetail: e.target.value,
        eventDetailError: '',
      });
    }
    if (name === 'arab1') {
      handleState({
        arab1: e.target.value,
        arab1Error: '',
      });
    }
    if (name === 'arab2') {
      handleState({
        arab2: e.target.value,
        arab2Error: '',
      });
    }
    if (name === 'startDate') {
      handleState({
        startDate: e.target.value,
        startDateError: '',
      });
    }
    if (name === 'endDate') {
      handleState({
        endDate: e.target.value,
        endDateError: '',
      });
    }
    if (name === 'startTime') {
      handleState({
        startTime: e.target.value,
        startTimeError: '',
      });
    }
  };
  const handleCheck = (event, name) => {
    if (name === 'day') {
      handleState({
        oneDay: event.target.checked,
        endDate: state.startDate,
      });
    }
    if (name === 'value') {
      handleState({
        valueCheck: event.target.checked,
      });
    }
    if (name === 'choosegender') {
      handleState({
        choosegender: event.target.checked,
        selectgender: 'male',
      });
    }
    if (name === 'chooseStaff') {
      handleState({
        chooseStaff: event.target.checked,
      });
      event_whom = 'staff';
    }
    if (name === 'chooseStudent') {
      handleState({
        chooseStudent: event.target.checked,
      });
      event_whom = 'student';
    }
  };
  const handleSelect = (e, name) => {
    if (name === 'eventType') {
      handleState({
        selectType: e.target.value,
        selectTypeError: '',
      });
    }
    if (name === 'venue') {
      handleState({
        selectVenue: e.target.value,
        selectVenueError: '',
      });
    }
  };
  const onRadioGroupChange = (e, name) => {
    if (name === 'input')
      handleState({
        selectedRadioButton: e.target.value,
      });
    if (name === 'inputs')
      handleState({
        selectedRadioButtons: e.target.value,
      });
    if (name === 'genderinput') {
      handleState({
        selectgender: e.target.value,
        genderError: '',
      });
      event_gender = e.target.value;
    }
  };
  const handleStartDate = (date) => {
    handleState({
      startDate: date,
      endDate: date,
      startDateError: '',
    });
  };
  const handleEndDate = (date) => {
    handleState({
      endDate: date,
      endDateError: '',
    });
  };
  const setStartTime = (date) => {
    handleState({
      startTime: date,
    });
  };
  const setEndTime = (date) => {
    handleState({
      endTime: date,
    });
  };
  const editStartTime = (date) => {
    handleState({
      editStartTime: date,
    });
  };
  const editEndTime = (date) => {
    handleState({
      editEndTime: date,
    });
  };

  let dd = moment(state.startTimeView).format('hh:mm A');
  let ss = moment(state.endTimeView).format('hh:mm A');
  return (
    <Modal show={show} centered size="lg" onHide={eventClose}>
      <Modal.Header closeButton>
        <div className="row w-100">
          <div className="col-md-12 pt-1 f-18 font-weight-bold">
            {flag === 'create' ? (
              <Trans i18nKey={'events.create_event'}></Trans>
            ) : (
              <Trans i18nKey={'events.edit_event'}></Trans>
            )}
          </div>
        </div>
      </Modal.Header>
      <Modal.Body>
        <form>
          <div className="managetext p-3">
            <div className="row ">
              <div className="col-lg-6 col-md-6 col-xl-6 pb-2 ">
                <Input
                  elementType={'floatingselect'}
                  elementConfig={{
                    options: state.eventType,
                  }}
                  value={state.selectType}
                  floatingLabel={t('events.event_type')}
                  changed={(e) => handleSelect(e, 'eventType')}
                  feedback={state.selectTypeError}
                />
              </div>
            </div>
            {flag === 'create' && (
              <>
                <div className="row">
                  <div className="col-xl-2 mt-3 ">
                    <p className="f-16 mb-0 mt-3">
                      <Trans i18nKey={'events.event_for'}></Trans> :
                    </p>
                  </div>
                  <div className="col-xl-2 mt-3 ">
                    <p className="mt-3">
                      <span className="mr-2">
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => handleCheck(event, 'chooseStaff')}
                          value={state.chooseStaff}
                        />
                      </span>
                      <Trans i18nKey={'events.staff'}></Trans>
                    </p>
                  </div>
                  <div className="col-xl-2 mt-3 ">
                    <p className="mt-3">
                      <span className="mr-2">
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => handleCheck(event, 'chooseStudent')}
                          value={state.chooseStudent}
                        />
                      </span>
                      <Trans i18nKey={'events.student'}></Trans>
                    </p>
                  </div>
                </div>
                <div className="row">
                  <div className="col-xl-3 mt-3 ">
                    <p className="mt-3">
                      <span className="mr-2">
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => handleCheck(event, 'choosegender')}
                          value={state.choosegender}
                        />
                      </span>
                      <Trans i18nKey={'events.gender_specific'}></Trans>
                    </p>
                  </div>
                  {state.choosegender === true && (
                    <div className="col-xl-7 mt-35 ">
                      <Input
                        elementType={'radio'}
                        elementConfig={genderinput}
                        className={'form-radio1'}
                        selected={state.selectgender}
                        labelclass="radio-label2"
                        onChange={(e) => onRadioGroupChange(e, 'genderinput')}
                        feedback={state.genderError}
                      />
                    </div>
                  )}
                </div>
              </>
            )}
            <div className="row ">
              <div
                className={`${
                  showArabic ? 'col-lg-6 col-md-6 col-xl-6' : 'col-lg-12 col-md-12 col-xl-12'
                } pb-2 pt-3`}
              >
                <Input
                  elementType={'floatinginput'}
                  elementConfig={{
                    type: 'text',
                  }}
                  // className={'ddd'}
                  // inputclassName={'ddd1'}
                  maxLength={100}
                  value={state.eventName}
                  floatingLabel={t('events.event_name')}
                  changed={(e) => handleChange(e, 'eventName')}
                  feedback={state.eventNameError}
                />
              </div>
              {showArabic && (
                <div className="col-lg-6 col-md-6 col-xl-6 pb-2 pt-3 ">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={100}
                    value={state.arab1}
                    dd={'rtl-text'}
                    floatingLabel={'يريد العالم أن'}
                    changed={(e) => handleChange(e, 'arab1')}
                    feedback={state.arab1Error}
                  />
                </div>
              )}
              <div
                className={`${
                  showArabic ? 'col-lg-6 col-md-6 col-xl-6' : 'col-lg-12 col-md-12 col-xl-12'
                } pb-2 pt-3`}
              >
                <Input
                  elementType={'floatinginput'}
                  elementConfig={{
                    type: 'text',
                  }}
                  maxLength={200}
                  value={state.eventDetail}
                  floatingLabel={t('events.event_details')}
                  changed={(e) => handleChange(e, 'eventDetail')}
                  feedback={state.eventDetailError}
                />
              </div>
              {showArabic && (
                <div className="col-lg-6 col-md-6 col-xl-6 pb-2 pt-3 ">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={200}
                    value={state.arab2}
                    dd={'rtl-text'}
                    floatingLabel={'تسجّل الآن'}
                    changed={(e) => handleChange(e, 'arab2')}
                    feedback={state.arab2Error}
                  />
                </div>
              )}
              <div className="col-lg-7 col-md-7 col-xl-7 pb-2">
                <div className="row">
                  <div className="col-6 col-md-6">
                    <div className="">
                      <p className="mb-2">
                        <Trans i18nKey={'events.start_date'}></Trans>
                      </p>
                      <i className="fa fa-calendar-check-o calender1" aria-hidden="true"></i>
                      <DatePicker
                        selected={state.startDate !== '' ? new Date(state.startDate) : ''}
                        onChange={handleStartDate}
                        value={state.startDate !== '' ? state.startDate : ''}
                        minDate={new Date(academicYearStartDate)}
                        maxDate={new Date(academicYearEndDate)}
                        dateFormat="d MMM yyyy"
                        className={`form-control ${iconFormCalender}`}
                        showMonthDropdown
                        showYearDropdown
                        {...(lang === 'ar' && { locale: 'ar' })}
                      />
                      <div className="InvalidFeedback">{state.startDateError}</div>
                    </div>
                    <div className="pt-3">
                      <p className="mb-2">
                        <Trans i18nKey={'events.end_date'}></Trans>
                      </p>
                      <i className="fa fa-calendar-check-o calender1" aria-hidden="true"></i>
                      <DatePicker
                        selected={state.endDate !== '' ? new Date(state.endDate) : ''}
                        onChange={handleEndDate}
                        value={state.endDate !== '' ? state.endDate : ''}
                        minDate={
                          new Date(
                            state.startDate !== ''
                              ? moment(state.startDate).format('YYYY-MM-DD')
                              : academicYearStartDate
                          )
                        }
                        maxDate={
                          new Date(
                            state.oneDay === true
                              ? moment(state.startDate).format('YYYY-MM-DD')
                              : academicYearEndDate
                          )
                        }
                        dateFormat="d MMM yyyy"
                        className={`form-control ${iconFormCalender}`}
                        showMonthDropdown
                        showYearDropdown
                        {...(lang === 'ar' && { locale: 'ar' })}
                      />
                      <div className="InvalidFeedback">{state.endDateError}</div>
                    </div>
                  </div>
                  <div className="col-6 col-md-6">
                    <div className="">
                      <p className="mb-2">
                        <Trans i18nKey={'events.start_time'}></Trans>
                      </p>
                      <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                      {/* <DatePicker
                        // value={ss}
                        selected={
                          flag
                            ? new Date(moment(state.startTime).format('hh:mm A'))
                            : ''
                        }
                        onChange={setStartTime}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        dateFormat="h:mm aa"
                        timeFormat="h:mm aa"
                        className={`form-control ${iconForm}`}
                        {...(lang === 'ar' && { locale: 'ar' })}
                      /> */}
                      {flag === 'edit' ? (
                        state.editStartTime === '' ? (
                          <DatePicker
                            selected={state.editStartTime}
                            onChange={editStartTime}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            dateFormat="h:mm aa"
                            timeFormat="h:mm aa"
                            className={`form-control ${iconForm}`}
                            value={dd}
                            {...(lang === 'ar' && { locale: 'ar' })}
                          />
                        ) : (
                          <DatePicker
                            selected={state.editStartTime}
                            onChange={editStartTime}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            dateFormat="h:mm aa"
                            timeFormat="h:mm aa"
                            className={`form-control ${iconForm}`}
                            {...(lang === 'ar' && { locale: 'ar' })}
                          />
                        )
                      ) : (
                        <DatePicker
                          selected={state.startTime}
                          onChange={setStartTime}
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={15}
                          dateFormat="h:mm aa"
                          timeFormat="h:mm aa"
                          className={`form-control ${iconForm}`}
                          {...(lang === 'ar' && { locale: 'ar' })}
                        />
                      )}
                    </div>
                    <div className="pt-3">
                      <p className="mb-2 pt-2">
                        <Trans i18nKey={'events.end_time'}></Trans>
                      </p>
                      <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                      {/* <DatePicker
                        value={dd}
                        // selected={moment(state.endTime).format('hh:mm A')}
                        onChange={setEndTime}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeCaption="End Time"
                        dateFormat="h:mm aa"
                        timeFormat="h:mm aa"
                        className={`form-control ${iconForm}`}
                        {...(lang === 'ar' && { locale: 'ar' })}
                      /> */}
                      {flag === 'edit' ? (
                        state.editEndTime === '' ? (
                          <DatePicker
                            selected={state.editEndTime}
                            onChange={editEndTime}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            dateFormat="h:mm aa"
                            timeFormat="h:mm aa"
                            className={`form-control ${iconForm}`}
                            value={ss}
                            {...(lang === 'ar' && { locale: 'ar' })}
                          />
                        ) : (
                          <DatePicker
                            selected={state.editEndTime}
                            onChange={editEndTime}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            dateFormat="h:mm aa"
                            timeFormat="h:mm aa"
                            className={`form-control ${iconForm}`}
                            {...(lang === 'ar' && { locale: 'ar' })}
                          />
                        )
                      ) : (
                        <DatePicker
                          selected={state.endTime}
                          onChange={setEndTime}
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={15}
                          dateFormat="h:mm aa"
                          timeFormat="h:mm aa"
                          className={`form-control ${iconForm}`}
                          {...(lang === 'ar' && { locale: 'ar' })}
                        />
                      )}
                      <div className="InvalidFeedback">{state.timeError}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-lg-5 col-md-5 col-xl-5 pb-2">
                <div className="row">
                  <p className="ml-4 mt-5">
                    <span className="mr-2">
                      <input
                        type="checkbox"
                        className="calendarFormRadio"
                        onChange={(event) => handleCheck(event, 'day')}
                        value={state.oneDay}
                        {...(flag === 'edit' && { checked: state.oneDay })}
                      />
                    </span>
                    <Trans i18nKey={'events.onde_day_event'}></Trans>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </form>
      </Modal.Body>
      <Modal.Footer>
        <span className="remove_hover btn btn-outline-primary" onClick={eventClose}>
          <Trans i18nKey={'events.cancel'}></Trans>
        </span>
        <span
          className="remove_hover btn btn-primary"
          onClick={(e) => {
            flag === 'create' ? handleEventSubmit(e) : handleEditEventSubmit(e);
          }}
        >
          <Trans i18nKey={'events.submit'}></Trans>
        </span>
      </Modal.Footer>
    </Modal>
  );
};
AddEditEventModal.propTypes = {
  academicYearStartDate: PropTypes.string,
  academicYearEndDate: PropTypes.string,
  handleState: PropTypes.func,
  state: PropTypes.object,
  eventClose: PropTypes.func,
  fetchApi: PropTypes.func,
  flag: PropTypes.string,
  show: PropTypes.bool,
  setData: PropTypes.func,
};
export default AddEditEventModal;
