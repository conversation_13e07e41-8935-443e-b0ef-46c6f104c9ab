import React from 'react';
import PropTypes from 'prop-types';
import { Box, Pagination, Select, MenuItem, Typography } from '@mui/material';

const paginationSx = {
  '& .MuiPaginationItem-root.Mui-selected': {
    backgroundColor: 'transparent',
    border: '1px solid #00000061',
  },
  '& .MuiPaginationItem-root.Mui-selected:hover': {
    backgroundColor: 'transparent',
  },
};

const limitOptions = [10, 25, 50, 100];

const MuiPagination = ({
  totalItems,
  limit,
  page,
  handleLimitChange,
  handlePageChange,
  sx = {},
}) => {
  return (
    <Box display="flex" alignItems="center" justifyContent="space-between">
      <Typography sx={sx}>
        Showing {limit} out of {totalItems} items
      </Typography>

      <Box display="flex" alignItems="center" gap={2}>
        <Typography sx={sx}>Items per page:</Typography>
        <Select value={limit} onChange={handleLimitChange} size="small">
          {limitOptions.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>

        <Pagination
          sx={paginationSx}
          count={Math.ceil(totalItems / limit)}
          page={page}
          onChange={handlePageChange}
          shape="rounded"
        />
      </Box>
    </Box>
  );
};

MuiPagination.propTypes = {
  totalItems: PropTypes.number,
  limit: PropTypes.number,
  page: PropTypes.number,
  handleLimitChange: PropTypes.func,
  handlePageChange: PropTypes.func,
  sx: PropTypes.object,
};

export default MuiPagination;
