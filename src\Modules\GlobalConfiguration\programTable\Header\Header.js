import React, { useContext, useEffect, useRef } from 'react';
import i18n from 'i18next';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import * as actions from '_reduxapi/global_configuration/actions';
import parentContext from '../Context/Context';
import { connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { getInstitutionHeader } from 'v2/utils';
import { selectProgramInput } from '_reduxapi/global_configuration/selectors';
import { getLang } from 'utils';
import { Trans } from 'react-i18next';
import { programTypeListForFilter } from '../../../ProgramInput/v2/piUtil';
import { getLabelName } from 'Modules/Shared/v2/Configurations';

function Header({ programInputSettings, getProgramInput }) {
  const details = useContext(parentContext.PIL_Context);
  const tabData = useContext(parentContext.searchFilterContext);
  const { getSearchedAndFilteredData } = details;
  const { tabValue, search, setSearch, filter, setFilter } = tabData;
  const firstSearch = useRef(true);
  const secondSearch = useRef(true);
  const history = useHistory();
  const institutionHeader = getInstitutionHeader(history);
  const programTypeOptions = programInputSettings.get('programType', List());

  useEffect(() => {
    getProgramInput({ header: institutionHeader });
  }, []); // eslint-disable-line

  useEffect(() => {
    if (firstSearch.current) {
      firstSearch.current = false;
      return;
    }
    const timeout = setTimeout(() => {
      /*  if (search) */ getSearchedAndFilteredData(search, tabValue, filter);
    }, 500);
    return () => {
      clearTimeout(timeout);
    };
  }, [search]); // eslint-disable-line

  useEffect(() => {
    if (secondSearch.current) {
      secondSearch.current = false;
      return;
    }
    getSearchedAndFilteredData(search, tabValue, filter);
  }, [filter]); // eslint-disable-line

  return (
    <>
      <h4 className="d-flex">
        <Trans i18nKey={'all'} />
        &nbsp;
        {getLabelName({ label: 'program', length: 10 })}
      </h4>
      <div className="row align-items-center">
        <div className="col-7 col-xl-9 col-lg-8 col-md-8 pl-2 pt-3 mt-2">
          <MaterialInput
            elementType={'materialSearch'}
            value={search}
            size={'small'}
            placeholder={i18n.t('search_code_type')}
            changed={(event) => setSearch(event.target.value)}
          />
        </div>
        <div className="col-5 col-md-4 col-xl-3 col-lg-4">
          <div className={`pr-3 ${getLang() === 'ar' ? 'mt--20' : ''}`}>
            <label className="mb-0 f-14">
              {getLabelName({ label: 'program', length: 10 })} <Trans i18nKey={'type'}></Trans>
            </label>
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{
                options: programTypeListForFilter(
                  programTypeOptions,
                  getLabelName({ label: 'Pre-Requisite Course', isToolTip: false })
                ),
              }}
              // label={`${i18n.t(`program_type`)}`}
              changed={(event) => setFilter(event.target.value)}
            />
          </div>
        </div>
      </div>
    </>
  );
}

Header.propTypes = {
  programInputSettings: PropTypes.instanceOf(Map),
  getProgramInput: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    programInputSettings: selectProgramInput(state),
  };
};

export default connect(mapStateToProps, actions)(Header);
