export function filterBySearch(item, search) {
  const deliverySymbol = `${item.get('name', '')}`;
  const searchField = search.toLowerCase();
  const deliverySymbolField = deliverySymbol.toLowerCase();
  if (searchField === '') {
    return true;
  } else {
    return deliverySymbolField.includes(searchField);
  }
}

export function filterBy(item, option) {
  if (option === 'All') return true;
  if (option === 'Active') return item.get('isActive', true);
  if (option === 'Archive') return !item.get('isActive', false);
}
