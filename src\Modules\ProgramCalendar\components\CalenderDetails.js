import React, { Fragment, useState, Suspense, useCallback, useContext } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { withRouter } from 'react-router-dom';
import Tooltip from '../../../_components/UI/Tooltip/Tooltip';
import {
  PrimaryButton,
  FlexWrapper,
  Null,
  DetailWrapper,
  Title,
  HeaderRowWrapper,
} from '../Styled';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getFormattedHijiriYear } from 'utils';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import useCalendar from 'Hooks/useCalendarHook';
import { selectProgramCalendarLanding } from '_reduxapi/program_calendar/selectors';
import useProgramDepartment from 'Hooks/useProgramDepartment';

import NavRowTermsButtons from './NavRowTermsButtons';
import NavRowYearsButtons from './NavRowYearsButtons';
import { PCContext } from '../index';
const EventsListModal = React.lazy(() => import('../modal/EventList'));

const CalenderDetails = (props) => {
  const { history, clickedPDF } = props;
  const indexData = useContext(PCContext);
  const {
    activeInstitutionCalendar,
    programCalendarLanding,
    programName,
    iframeShow,
    programId,
    activeYear,
    activeTerm,
    programCalendarDashboard,
  } = indexData;
  const [eventListShow, setEventListShow] = useState(false);

  const { isCurrentCalendar } = useCalendar();
  const currentCalendar = isCurrentCalendar();
  const currentProgramCalendarId = programCalendarLanding.getIn([0, '_id'], '');
  const publishedStatus = programCalendarLanding.getIn([0, 'status'], '');

  const { hasProgramAccess } = useProgramDepartment();
  const eventsListToggle = () => setEventListShow(!eventListShow);

  let eventData = programCalendarLanding
    .filter((item) => item.get('year', '') === activeYear)
    .reduce((_, el) => el.get('level', List()), List());

  const currentPGAccess = hasProgramAccess(programId);
  const eventBtnActive = useCallback(() => {
    return eventData.every((item) => item.get('events', List()).size > 0);
  }, [eventData]);
  const eventBtnActiveCall = eventBtnActive();

  const reviewBtnActive = useCallback(() => {
    return programCalendarLanding.some((calendarData) => {
      return calendarData
        .get('level', List())
        .some(
          (item) =>
            item.get('start_date', '') !== '' &&
            item.get('end_date', '') !== '' &&
            (item.get('course', List()).size > 0 || item.get('rotation_course', List()).size > 0)
        );
    });
  }, [programCalendarLanding]);
  const reviewBtnActiveCall = reviewBtnActive();

  const exportBtnActive = useCallback(() => {
    return eventData.every(
      (item) =>
        item.get('start_date', '') !== '' &&
        item.get('end_date', '') !== '' &&
        (item.get('course', List()).size > 0 || item.get('rotation_course', List()).size > 0)
    );
  }, [eventData]);
  const exportBtnActiveCall = exportBtnActive();

  // console.log(
  //   'hasProgramAccess',
  //   currentPGAccess,
  //   currentProgramCalendarId,
  //   iframeShow,
  //   eventBtnActiveCall,
  //   programCalendarLanding,
  //   eventData,
  //   activeYear,
  //   reviewBtnActiveCall
  // );

  function goToSettings() {
    const location = history.location;
    const pathSearch = location.search;
    history.push(`/pc/calendar-settings${pathSearch}`);
  }

  function goToCourse() {
    const location = history.location;
    const pathSearch = location.search;
    history.push(`/pc/courses${pathSearch}`);
  }

  function getAcademicYearName() {
    return `Academic Year ${getFormattedHijiriYear(
      activeInstitutionCalendar.get('calendar_name', ''),
      activeInstitutionCalendar.get('start_date', ''),
      activeInstitutionCalendar.get('end_date', '')
    )}`;
  }

  const academicYearName = getAcademicYearName();
  const isInterim = programCalendarDashboard.get('interim', false);
  return (
    <Fragment>
      {eventListShow === true && (
        <Suspense fallback={''}>
          <EventsListModal
            clicked={eventsListToggle}
            data={eventData.toJS()}
            academicYearName={academicYearName}
            urlProgramName={programName}
            iframeShow={iframeShow}
            programId={programId}
            urlYear={activeYear}
            activeTerm={activeTerm}
          />
        </Suspense>
      )}
      <div style={{ marginBottom: '1em' }}></div>
      <FlexWrapper className="nav_bg">
        <NavRowTermsButtons theme="active" color="white" />
      </FlexWrapper>
      <FlexWrapper className="nav_bg">
        <NavRowYearsButtons theme="active" color="white" />
      </FlexWrapper>
      <DetailWrapper>
        {!iframeShow && (
          <Title>
            <div className="ml-5">{academicYearName}</div>
          </Title>
        )}
        <Null />

        <Tooltip title={t('program_calendar.events_list')}>
          <PrimaryButton
            className={
              eventBtnActiveCall && currentProgramCalendarId !== '' ? 'light' : 'light disable'
            }
            disabled={!(eventBtnActiveCall && currentProgramCalendarId !== '')}
            onClick={eventsListToggle}
          >
            {' '}
            <Trans i18nKey={'program_calendar.events_list'}></Trans>{' '}
          </PrimaryButton>
        </Tooltip>

        {currentPGAccess && !iframeShow && (
          <Tooltip title={t('program_calendar.set_calendar')}>
            {currentCalendar && (
              <PrimaryButton
                className={
                  CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
                    ? 'bordernone'
                    : 'light disable'
                }
                disabled={
                  !CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
                }
                onClick={() => {
                  if (
                    CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
                  ) {
                    goToSettings();
                  }
                }}
              >
                {' '}
                <Trans i18nKey={'program_calendar.calendar_settings'}></Trans>
              </PrimaryButton>
            )}
          </Tooltip>
        )}
      </DetailWrapper>
      <HeaderRowWrapper>
        <div style={{ float: 'left' }}></div>
        <FlexWrapper>
          <Null />
          <Fragment>
            {currentPGAccess && !iframeShow && (
              <>
                <Tooltip title={t('role_management.role_actions.Add Course')}>
                  {currentCalendar && (
                    <PrimaryButton
                      className={
                        eventData.getIn([0, 'start_date'], '') !== '' &&
                        CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')
                          ? 'light'
                          : 'light disable'
                      }
                      disabled={
                        !CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')
                      }
                      onClick={() => {
                        if (
                          CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')
                        ) {
                          //changeTitle('Add Course');
                          //toggleModal();
                          goToCourse();
                        }
                      }}
                    >
                      {' '}
                      <i className="fas fa-plus"></i>{' '}
                      <Trans i18nKey={'program_calendar.course'}></Trans>
                    </PrimaryButton>
                  )}
                </Tooltip>

                {currentCalendar && publishedStatus !== 'published' && (
                  <Tooltip title={t('role_management.role_actions.Review Calendar')}>
                    <PrimaryButton
                      className={
                        reviewBtnActiveCall &&
                        currentPGAccess &&
                        currentProgramCalendarId !== '' &&
                        CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Review Calendar')
                          ? 'bordernone'
                          : 'bgdisabled'
                      }
                      disabled={
                        !(
                          reviewBtnActiveCall &&
                          currentPGAccess &&
                          currentProgramCalendarId !== null &&
                          currentProgramCalendarId !== '' &&
                          CheckPermission(
                            'pages',
                            'Program Calendar',
                            'Dashboard',
                            'Review Calendar'
                          )
                        )
                      }
                      onClick={() => {
                        history.push(
                          `/programcalendar/vice-dean?id=${currentProgramCalendarId}&title=${activeInstitutionCalendar.get(
                            'calendar_name',
                            ''
                          )}&year=${activeYear}&name=${programName}&icd=${activeInstitutionCalendar.get(
                            '_id',
                            ''
                          )}&interim=${isInterim}&programid=${programId}`
                        );
                      }}
                    >
                      <Trans i18nKey={'role_management.role_actions.Review Calendar'}></Trans>
                    </PrimaryButton>
                  </Tooltip>
                )}
              </>
            )}

            {!iframeShow && (
              <Tooltip title={t('program_calendar.export_as_pdf')}>
                <PrimaryButton
                  className={
                    exportBtnActiveCall &&
                    currentProgramCalendarId !== null &&
                    currentProgramCalendarId !== ''
                      ? //CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Export')
                        'light'
                      : 'light disable'
                  }
                  disabled={
                    !(
                      exportBtnActiveCall &&
                      currentProgramCalendarId !== null &&
                      currentProgramCalendarId !== ''
                    )
                  }
                  onClick={clickedPDF}
                >
                  {' '}
                  <Trans i18nKey={'program_calendar.export'}></Trans>
                </PrimaryButton>
              </Tooltip>
            )}
          </Fragment>
        </FlexWrapper>
      </HeaderRowWrapper>
    </Fragment>
  );
};

CalenderDetails.propTypes = {
  programCalendarLanding: PropTypes.instanceOf(List),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  indexData: PropTypes.object,
  clickedPDF: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    programCalendarLanding: selectProgramCalendarLanding(state),
  };
};

export default connect(mapStateToProps)(withRouter(CalenderDetails));
