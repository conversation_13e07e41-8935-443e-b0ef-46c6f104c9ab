import React, { Fragment } from 'react';
import { List, Map } from 'immutable';

import { Checkbox, Divider, Radio, Chip } from '@mui/material';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import MaterialInput from 'Widgets/FormElements/material/Input';
import CloseIcon from '@mui/icons-material/Close';

import CircleIcon from '@mui/icons-material/Circle';

import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';

import { numbers, monthNames } from './utils';
import { useSelector } from 'react-redux';
import { selectQaPcSetting, selectTermList } from '_reduxapi/q360/selectors';

function ConditionalWrapper({ condition, children }) {
  return <>{condition ? children : null}</>;
}

export default function ConfiguredDetails({
  course,
  commonAttemptTypeKey,
  index,
  level,
  statusTab,
  handleChange,
}) {
  const {
    handleChangeAttemptData,
    handleCheckTag,
    handleCheckSubTag,
    updateGroupsCount,
    handleDelete,
    handleChangeTermAndAttempt,
    handleCheckTermAndAttempt,
  } = handleChange;
  const configuredActions = course.getIn(['occurrences', 'actions'], Map());
  const courseType = course.get('course_type', '');
  const actions = {
    studentGroups: configuredActions.get('studentGroups', false),
    everyAcademic: configuredActions.get('everyAcademic', false),
    occurrenceConfiguration: configuredActions.get('occurrenceConfiguration', false),
    academicTerms: configuredActions.get('academicTerms', false),
    attemptType: configuredActions.get('attemptType', false),
  };

  const getTagData = () => {
    return course.getIn(['occurrences', 'tags'], List());
  };
  const isProgramAndInstitution = ['course'];
  const tagData = getTagData();
  const qapcSetting = useSelector(selectQaPcSetting);
  const termList = useSelector(selectTermList);

  const getTagDetails = () => {
    return qapcSetting
      .get('tagData', List())
      .filter((tag) => tag.get('isDefault', false))
      .filter((tag) => tag.get('level', false) === level);
  };

  const tagDetails = getTagDetails();

  return (
    <AccordionDetails onClick={(e) => e.stopPropagation()}>
      <section>
        <div className="d-flex gap-8">
          <div className="flex-grow-1">
            <label className="f-12 fw-400 text-mGrey">Institution Type</label>
            <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">University</div>
          </div>
          {isProgramAndInstitution.includes(level) && (
            <div className="flex-grow-1">
              <label className="f-12 fw-400 text-mGrey">Course Type</label>
              <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey text-capitalize">
                {courseType}
              </div>
            </div>
          )}
          {level === 'course' && (
            <ConditionalWrapper condition={actions.studentGroups}>
              <div className="flex-grow-1">
                <label className="f-12 fw-400 text-mGrey">Students Groups *</label>
                <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
                  {course.getIn(['occurrences', 'studentGroups'], List()).size
                    ? course.getIn(['occurrences', 'studentGroups'], List()).join(',')
                    : 'No Group'}
                </div>
              </div>
            </ConditionalWrapper>
          )}
        </div>
      </section>

      <ConditionalWrapper condition={statusTab !== 'Pending'}>
        <Divider className="my-3" />
        <section className="f-14 fw-400 text-dGrey" onClick={(e) => e.stopPropagation()}>
          <div>Occurrences *</div>

          <ConditionalWrapper condition={actions.studentGroups}>
            <div>
              <div className="f-12 fw-400 text-dGrey my-2">Students Groups</div>
              <div className="w-20">
                <MaterialInput
                  elementType={'materialInput'}
                  value={course.getIn(['occurrences', 'studentGroups'], List()).size}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  placeholder={'Enter Numbers'}
                  changed={updateGroupsCount({
                    key: commonAttemptTypeKey.concat(['occurrences', 'studentGroups']),
                    isEdited: commonAttemptTypeKey.concat(['isEdited']),
                    attemptTypes: commonAttemptTypeKey.concat(['occurrences', 'attemptTypes']),
                  })}
                />
              </div>
              <div className="d-flex flex-wrap align-items-center gap-10 mt-1 my-2">
                {course.getIn(['occurrences', 'studentGroups'], List()).map((group, groupIndex) => {
                  return (
                    <Chip
                      key={groupIndex}
                      label={group}
                      // onClick={handleClick}
                      onDelete={handleDelete({
                        key: commonAttemptTypeKey.concat(['occurrences', 'studentGroups']),
                        isEdited: commonAttemptTypeKey.concat(['isEdited']),
                        index: groupIndex,
                      })}
                      deleteIcon={<CloseIcon />}
                      size="small"
                      className="select-color"
                    />
                  );
                })}
              </div>
            </div>
          </ConditionalWrapper>

          <ConditionalWrapper condition={actions.academicTerms}>
            <div>
              <div className="f-14 fw-400 text-dGrey my-2">Academic Term</div>
              <div className="d-flex align-items-center ml-1 mt-1">
                <div className="d-flex align-items-center my-1">
                  <div className="mr-2">
                    <div className="d-flex align-item-center">
                      <Radio
                        checked={course.getIn(['occurrences', 'academicTerm'], 'all') === 'all'}
                        onChange={() =>
                          handleChangeTermAndAttempt({
                            value: 'all',
                            isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            key: commonAttemptTypeKey.concat(['occurrences']),
                            typeKey: 'academicTerm',
                            actions,
                          })
                        }
                        name="radio-buttons"
                        inputProps={{ 'aria-label': 'A' }}
                        size="small"
                        className="m-0 p-0"
                      />
                      <div className="f-14 ml-2">All Term</div>
                    </div>
                  </div>
                </div>
                <div className="d-flex align-items-center my-1 ml-2">
                  <div className="mr-2">
                    <div className="d-flex align-item-center">
                      <Radio
                        checked={course.getIn(['occurrences', 'academicTerm'], 'every') === 'every'}
                        onChange={() =>
                          handleChangeTermAndAttempt({
                            value: 'every',
                            isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            key: commonAttemptTypeKey.concat(['occurrences']),
                            typeKey: 'academicTerm',
                            actions,
                          })
                        }
                        name="radio-buttons"
                        inputProps={{ 'aria-label': 'B' }}
                        size="small"
                        className="m-0 p-0"
                      />
                      <div className="f-14 ml-2">Every Term</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="d-flex align-items-center ml-1 mt-1">
                {termList.map((term, index) => (
                  <div className="d-flex align-items-center my-1 mr-2" key={index}>
                    <div className="mr-2">
                      <div className="d-flex align-item-center">
                        <Checkbox
                          checked={
                            course
                              .getIn(['occurrences', 'selectedAcademicTerm'], List())
                              .includes(term) ||
                            course.getIn(['occurrences', 'academicTerm'], '') === 'all'
                          }
                          onChange={(e) =>
                            handleCheckTermAndAttempt({
                              e,
                              value: term,
                              isEdited: commonAttemptTypeKey.concat(['isEdited']),
                              key: commonAttemptTypeKey.concat(['occurrences']),
                              typeKey: 'academicTerm',
                              actions,
                            })
                          }
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'A' }}
                          size="small"
                          className="m-0 p-0"
                          disabled={course.getIn(['occurrences', 'academicTerm'], '') === 'all'}
                        />
                        <div className="f-14 ml-2 text-capitalize">{term}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <Divider className="my-3" />
          </ConditionalWrapper>

          <ConditionalWrapper condition={actions.attemptType}>
            <div>
              <div className="f-14 fw-400 text-dGrey my-2">Attempt Type</div>
              <div className="d-flex align-items-center ml-1 mt-1">
                <div className="d-flex align-items-center my-1">
                  <div className="mr-2">
                    <div className="d-flex align-item-center">
                      <Radio
                        checked={course.getIn(['occurrences', 'attemptType'], 'all') === 'all'}
                        onChange={() =>
                          handleChangeTermAndAttempt({
                            value: 'all',
                            isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            key: commonAttemptTypeKey.concat(['occurrences']),
                            typeKey: 'attemptType',
                            actions,
                          })
                        }
                        name="radio-buttons"
                        inputProps={{ 'aria-label': 'A' }}
                        size="small"
                        className="m-0 p-0"
                      />
                      <div className="f-14 ml-2">All Type</div>
                    </div>
                  </div>
                </div>
                <div className="d-flex align-items-center my-1 ml-2">
                  <div className="mr-2">
                    <div className="d-flex align-item-center">
                      <Radio
                        checked={course.getIn(['occurrences', 'attemptType'], 'every') === 'every'}
                        onChange={() =>
                          handleChangeTermAndAttempt({
                            value: 'every',
                            isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            key: commonAttemptTypeKey.concat(['occurrences']),
                            typeKey: 'attemptType',
                            actions,
                          })
                        }
                        name="radio-buttons"
                        inputProps={{ 'aria-label': 'B' }}
                        size="small"
                        className="m-0 p-0"
                      />
                      <div className="f-14 ml-2">Every Type</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="d-flex align-items-center ml-1 mt-1">
                {qapcSetting.get('attemptData', List()).map((attemptType, index) => (
                  <div className="d-flex align-items-center my-1" key={index}>
                    <div className="mr-2">
                      <div className="d-flex align-item-center">
                        <Checkbox
                          checked={
                            course
                              .getIn(['occurrences', 'selectedAttemptType'], List())
                              .includes(attemptType.get('name', '')) ||
                            course.getIn(['occurrences', 'attemptType'], '') === 'all'
                          }
                          onChange={(e) =>
                            handleCheckTermAndAttempt({
                              e,
                              value: attemptType.get('name', ''),
                              isEdited: commonAttemptTypeKey.concat(['isEdited']),
                              key: commonAttemptTypeKey.concat(['occurrences']),
                              typeKey: 'attemptType',
                              actions,
                            })
                          }
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'A' }}
                          size="small"
                          className="m-0 p-0"
                          disabled={course.getIn(['occurrences', 'attemptType'], '') === 'all'}
                        />
                        <div className="f-14 ml-2 text-capitalize">
                          {attemptType.get('name', '')}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <Divider className="my-3" />
          </ConditionalWrapper>

          {course.getIn(['occurrences', 'attemptTypes'], List()).map((attempt, attemptKey) => (
            <Fragment key={attemptKey}>
              <ConditionalWrapper condition={level !== 'institution' && actions.academicTerms}>
                <div className="text-capitalize">
                  {attempt.get('termName', '') !== 'none' ? attempt.get('termName', '') : ''} Term
                </div>
              </ConditionalWrapper>

              <div className="ml-3 mt-1">
                <ConditionalWrapper condition={level !== 'institution' && actions.attemptType}>
                  <div className="text-capitalize">
                    {attempt.get('typeName', '') !== 'none' ? attempt.get('typeName', '') : ''}{' '}
                    Attempt
                  </div>
                </ConditionalWrapper>

                {/* <ConditionalWrapper condition={actions.everyAcademic}>
                  <div className="d-flex align-items-center mt-2">
                    <div className="mr-2">
                      <Checkbox
                        size="small"
                        checked={attempt.get('executionsPer', false)}
                        onChange={handleChangeAttemptData({
                          key: commonAttemptTypeKey.concat([
                            'occurrences',
                            'attemptTypes',
                            attemptKey,
                            'executionsPer',
                          ]),
                          value: !attempt.get('executionsPer', false),
                          isEdited: commonAttemptTypeKey.concat(['isEdited']),
                        })}
                        className="m-0 p-0"
                      />
                    </div>
                    <div>Executions Per Academic Year </div>
                  </div>

                   <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                  <div className="d-flex align-items-center my-1">
                    <div className="mr-2">
                      <Radio
                        checked={attempt.get('academicYear', '') === 'all'}
                        onChange={handleChangeAttemptData({
                          key: commonAttemptTypeKey.concat([
                            'occurrences',
                            'attemptTypes',
                            attemptKey,
                            'academicYear',
                          ]),
                          value: 'all',
                          isEdited: commonAttemptTypeKey.concat(['isEdited']),
                        })}
                        value="all"
                        name="radio-buttons"
                        inputProps={{
                          'aria-label': 'A',
                        }}
                        size="small"
                        className="m-0 p-0"
                        disabled={true}
                      />
                    </div>
                    <div>For All Academic Year</div>
                  </div>
                  <div className="d-flex align-items-center my-1 ml-2">
                    <div className="mr-2">
                      <Radio
                        checked={attempt.get('academicYear', '') === 'every'}
                        onChange={handleChangeAttemptData({
                          key: commonAttemptTypeKey.concat([
                            'occurrences',

                            'attemptTypes',
                            attemptKey,
                            'academicYear',
                          ]),
                          value: 'every',
                          isEdited: commonAttemptTypeKey.concat(['isEdited']),
                        })}
                        value="every"
                        name="radio-buttons"
                        inputProps={{
                          'aria-label': 'B',
                        }}
                        size="small"
                        className="m-0 p-0"
                      />
                    </div>
                    <div>For Every Academic Year</div>
                  </div>
                </div> 
                </ConditionalWrapper> */}

                <ConditionalWrapper condition={actions.studentGroups}>
                  <div className="d-flex align-items-center pl-4 ml-1 mt-1 mb-2">
                    <div className="d-flex align-items-center my-1">
                      <div className="mr-2">
                        <Radio
                          checked={attempt.get('group', '') === 'all'}
                          onChange={handleChangeAttemptData({
                            key: commonAttemptTypeKey.concat([
                              'occurrences',
                              'attemptTypes',
                              attemptKey,
                              'group',
                            ]),
                            value: 'all',
                            isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            studentGroupKey: commonAttemptTypeKey.concat(['occurrences']),
                          })}
                          value="all"
                          name="radio-buttons"
                          inputProps={{
                            'aria-label': 'A',
                          }}
                          size="small"
                          className="m-0 p-0"
                        />
                      </div>
                      <div>All Group</div>
                    </div>
                    <div className="d-flex align-items-center my-1 ml-2">
                      <div className="mr-2">
                        <Radio
                          checked={attempt.get('group', '') === 'individual'}
                          onChange={handleChangeAttemptData({
                            key: commonAttemptTypeKey.concat([
                              'occurrences',
                              'attemptTypes',
                              attemptKey,
                              'group',
                            ]),
                            value: 'individual',
                            isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            studentGroupKey: commonAttemptTypeKey.concat(['occurrences']),
                          })}
                          value="individual"
                          name="radio-buttons"
                          inputProps={{
                            'aria-label': 'B',
                          }}
                          size="small"
                          className="m-0 p-0"
                        />
                      </div>
                      <div>Individual Group</div>
                    </div>
                  </div>
                </ConditionalWrapper>
                {level === 'institution' ||
                attempt.get('group', '') === 'all' ||
                attempt.get('group', '') === 'none' ? (
                  attempt.get('groupType', List()).map((group, i) => (
                    <Fragment key={index}>
                      <div className="d-flex align-items-center pl-4 pt-1">
                        <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                        <div>Minimum</div>
                        <div className="mx-2">
                          <MaterialInput
                            elementType={'materialSelect'}
                            type={'text'}
                            variant={'standard'}
                            value={group.get('minimum', 0)}
                            size={'small'}
                            elementConfig={{
                              options: numbers,
                            }}
                            disabled={!actions.occurrenceConfiguration}
                            sx={{
                              paddingBottom: 0,
                            }}
                            changed={handleChangeAttemptData({
                              key: commonAttemptTypeKey.concat([
                                'occurrences',
                                'attemptTypes',
                                attemptKey,
                                'groupType',
                                0,
                                'minimum',
                              ]),
                              isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            })}
                          />
                        </div>
                        <div>Times</div>
                      </div>
                      <div className="d-flex align-items-center pl-4 pt-2">
                        <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                        <div>Duration</div>
                      </div>
                      <div className="d-flex align-items-center pl-4 mlt-12 gap-10 py-2 w-30">
                        <div className="flex-grow-1">
                          <MaterialInput
                            elementType={'materialSelect'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                            placeholder="Start Month"
                            elementConfig={{
                              options: monthNames,
                            }}
                            value={group.get('startMonth', '')}
                            changed={handleChangeAttemptData({
                              key: commonAttemptTypeKey.concat([
                                'occurrences',
                                'attemptTypes',
                                attemptKey,
                                'groupType',
                                0,
                                'startMonth',
                              ]),
                              isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            })}
                          />
                        </div>
                        <div className="flex-grow-1">
                          <MaterialInput
                            elementType={'materialSelect'}
                            type={'text'}
                            variant={'outlined'}
                            elementConfig={{
                              options: monthNames,
                            }}
                            value={group.get('endMonth', '')}
                            placeholder="End Month"
                            changed={handleChangeAttemptData({
                              key: commonAttemptTypeKey.concat([
                                'occurrences',
                                'attemptTypes',
                                attemptKey,
                                'groupType',
                                0,
                                'endMonth',
                              ]),
                              isEdited: commonAttemptTypeKey.concat(['isEdited']),
                            })}
                            size="small"
                          />
                        </div>
                      </div>
                    </Fragment>
                  ))
                ) : (
                  <div className="d-flex pl-4 mlt-12 gap-10" style={{ flexWrap: 'wrap' }}>
                    {attempt.get('groupType', List()).map((groupValue, groupIndex) => {
                      return (
                        <div
                          className="align-self-start flex-grow-1"
                          style={{
                            flexBasis: '200px',
                          }}
                          key={groupIndex}
                        >
                          <Accordion
                            className="bg-white"
                            elevation={0}
                            variant="outlined"
                            disableGutters
                            expanded={true}
                            // expanded={expanded === groupIndex}
                            // onChange={() => setExpanded(groupIndex)}
                          >
                            <AccordionSummary
                              // expandIcon={<ExpandMoreIcon />}
                              aria-controls="panel3-content"
                              id="panel3-header"
                            >
                              {groupValue.get('name', '')}
                            </AccordionSummary>
                            <Divider className="mx-3" />
                            <AccordionDetails>
                              <div>
                                <div className="d-flex align-items-center gap-5">
                                  <div>Minimum</div>
                                  <div>
                                    <MaterialInput
                                      elementType={'materialSelect'}
                                      type={'text'}
                                      changed={handleChangeAttemptData({
                                        key: commonAttemptTypeKey.concat([
                                          'occurrences',
                                          'attemptTypes',
                                          attemptKey,
                                          'groupType',
                                          groupIndex,
                                          'minimum',
                                        ]),
                                        isEdited: commonAttemptTypeKey.concat(['isEdited']),
                                      })}
                                      variant={'standard'}
                                      size={'small'}
                                      elementConfig={{
                                        options: numbers,
                                      }}
                                      value={groupValue.get('minimum', 0)}
                                      disabled={!actions.occurrenceConfiguration}
                                    />
                                  </div>
                                  <div>Times</div>
                                </div>
                                <div className="d-flex align-items-center ">
                                  <div>Duration</div>
                                </div>
                                <div className="d-flex align-items-center gap-10 py-2">
                                  <MaterialInput
                                    elementType={'materialSelect'}
                                    type={'text'}
                                    variant={'outlined'}
                                    changed={handleChangeAttemptData({
                                      key: commonAttemptTypeKey.concat([
                                        'occurrences',
                                        'attemptTypes',
                                        attemptKey,
                                        'groupType',
                                        groupIndex,
                                        'startMonth',
                                      ]),
                                      isEdited: commonAttemptTypeKey.concat(['isEdited']),
                                    })}
                                    size={'small'}
                                    placeholder="Start Month "
                                    elementConfig={{
                                      options: monthNames,
                                    }}
                                    value={groupValue.get('startMonth', '')}
                                  />
                                  <MaterialInput
                                    elementType={'materialSelect'}
                                    type={'text'}
                                    variant={'outlined'}
                                    size={'small'}
                                    changed={handleChangeAttemptData({
                                      key: commonAttemptTypeKey.concat([
                                        'occurrences',
                                        'attemptTypes',
                                        attemptKey,
                                        'groupType',
                                        groupIndex,
                                        'endMonth',
                                      ]),
                                      isEdited: commonAttemptTypeKey.concat(['isEdited']),
                                    })}
                                    placeholder="End Month"
                                    elementConfig={{
                                      options: monthNames,
                                    }}
                                    value={groupValue.get('endMonth', '')}
                                  />
                                </div>
                              </div>
                            </AccordionDetails>
                          </Accordion>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </Fragment>
          ))}
        </section>
      </ConditionalWrapper>
      <ConditionalWrapper condition={statusTab !== 'Pending'}>
        <Divider className="my-3" />
        <div className="f-12 fw-400 text-dGrey mt-3 mb-2">Q360 - Tags</div>

        {tagDetails.map((item, index) => {
          const tagId = item.get('_id', '');
          const subTag = item.get('subTag', List()).size;
          const findData = tagData.find((item) => item.get('_id', '') === tagId) || Map();
          const findSubTag = findData.get('subTag', List()).size;
          const hasSubTags = subTag > 0;
          const checkedTag = tagData.filter((data) => typeof data !== 'undefined');
          const withoutSubTagCheck = checkedTag.some(
            (data) => data.get('_id', '') === item.get('_id', '')
          );

          return (
            <>
              <div key={index} className="d-flex align-items-center mt-2">
                <div className="mr-2">
                  <Checkbox
                    size="small"
                    className="m-0 p-0"
                    checked={hasSubTags ? findSubTag === subTag : withoutSubTagCheck}
                    indeterminate={
                      hasSubTags
                        ? findSubTag > 0 &&
                          findSubTag !== 0 &&
                          findData.get('subTag', List()).size !== subTag
                        : false
                    }
                    onClick={(event) =>
                      handleCheckTag(
                        commonAttemptTypeKey.concat(['occurrences', 'tags']),
                        index,
                        event.target.checked,
                        item.get('name', ''),
                        item.get('_id', ''),
                        tagDetails,
                        commonAttemptTypeKey.concat(['isEdited']),
                        hasSubTags
                      )
                    }
                  />
                </div>
                <div className="f-14 fw-400 text-dGrey">
                  {item.get('name', '')}{' '}
                  <EnableOrDisable valid={item.get('isDefault', false)}>
                    <span className="color-lt-gray">(Default)</span>
                  </EnableOrDisable>
                </div>
              </div>
              <div className="d-flex">
                {item.get('subTag', List()).map((subTag, subTagIndex) => (
                  <div key={subTagIndex} className="d-flex align-items-center mt-2 ml-3">
                    <Checkbox
                      size="small"
                      className="m-0 p-0"
                      checked={findData.get('subTag', List()).includes(subTag)}
                      onClick={(event) =>
                        handleCheckSubTag(
                          commonAttemptTypeKey.concat(['occurrences', 'tags']),
                          index,
                          subTagIndex,
                          event.target.checked,
                          item.get('_id', ''),
                          tagDetails,
                          commonAttemptTypeKey.concat(['isEdited'])
                        )
                      }
                    />
                    <div className="f-14 fw-400 text-dGrey ml-2">{subTag}</div>
                  </div>
                ))}
              </div>
            </>
          );
        })}
      </ConditionalWrapper>
    </AccordionDetails>
  );
}
