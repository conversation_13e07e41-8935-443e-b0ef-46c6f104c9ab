import React, { lazy, Suspense, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { getLang } from 'utils';
import arLocale from '@fullcalendar/core/locales/ar';
import moment from 'moment';
import './calendar.css';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { Box, IconButton, Popover, Typography } from '@mui/material';
import { Map } from 'immutable';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import CloseIcon from '@mui/icons-material/Close';
import KeyboardArrowLeftOutlinedIcon from '@mui/icons-material/KeyboardArrowLeftOutlined';
import KeyboardArrowRightOutlinedIcon from '@mui/icons-material/KeyboardArrowRightOutlined';
import { SecondaryText, SessionDetails } from './utils';
import { useDispatch } from 'react-redux';
import { deleteSchedule } from '_reduxapi/course_scheduling/action';

const DeleteSessionModal = lazy(() => import('./Modals/DeleteSessionModal'));

const sessionTimingSx = {
  padding: '2px 5px',
  fontSize: 10,
  borderRadius: '4px',
  backgroundColor: '#E5E7EB',
};
const paperPropsSx = {
  padding: '8px 20px 16px',
  borderRadius: '24px',
  backgroundColor: '#F4F5FA',
  color: '#4B5563',
  minWidth: '500px',
};
const iconSx = {
  color: '#4B5563',
};

const dayHeaderContent = (dt) => {
  return (
    <div className="custom-day-header">
      <p className="bold">{moment(dt.date).format('ddd')}</p>
      <p>{moment(dt.date).format('D')}</p>
    </div>
  );
};

const slotLabelContent = (dt) => {
  return <div className="custom-slot-label">{moment(dt.date).format('h A')}</div>;
};

const renderEventContent = (eventInfo) => {
  const { start, end, title, extendedProps } = eventInfo.event;
  const { isSession, sessionInfo, eventType, count } = extendedProps;

  if (!isSession) {
    return (
      <>
        <div>{title}</div>
        {eventType && <div>{eventType}</div>}
      </>
    );
  }

  return (
    <div>
      <Typography mb="2px" lineHeight={1.25}>
        <Typography component="span" sx={sessionTimingSx}>
          {moment(start).format('h:mm A')} - {moment(end).format('h:mm A')}
        </Typography>
      </Typography>

      <Typography fontSize={12} mb="2px" lineHeight={1.25}>
        {getShortString(title, 30)}
      </Typography>

      <Typography fontSize={10} color="#6B7280" lineHeight={1.25}>
        {getShortString(sessionInfo, 66)}

        {count > 1 && (
          <Typography component="span" fontSize="inherit" color="#0064C8" fontWeight={500} ml="4px">
            +{count - 1}
          </Typography>
        )}
      </Typography>
    </div>
  );
};

const EventPopover = ({
  anchorEl,
  scheduleList,
  currentScheduleId,
  handleClose,
  handleDelete,
  handleClickEdit,
  navigateSchedule,
}) => {
  const open = Boolean(anchorEl);
  const id = open ? 'event-popover' : undefined;
  const scheduleData = scheduleList.get(currentScheduleId, Map());
  const isSupportOrEvent = ['support_session', 'event'].includes(scheduleData.get('type'));

  const currentIndex = useMemo(() => {
    if (!currentScheduleId) return;

    const index = scheduleList.keySeq().findIndex((key) => key === currentScheduleId);
    return index + 1;
  }, [scheduleList, currentScheduleId]);

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
      transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      slotProps={{ paper: { sx: paperPropsSx } }}
    >
      <Box display="flex" alignItems="center" justifyContent="end">
        <IconButton sx={iconSx} onClick={() => handleDelete(scheduleData)}>
          <DeleteOutlineIcon />
        </IconButton>
        {!isSupportOrEvent && (
          <IconButton sx={iconSx} onClick={() => handleClickEdit(scheduleData)}>
            <EditOutlinedIcon />
          </IconButton>
        )}
        <IconButton sx={iconSx} onClick={handleClose}>
          <CloseIcon />
        </IconButton>
      </Box>

      <div className="mb-3">
        <SessionDetails scheduleData={scheduleData} />
      </div>

      {/* <Typography fontSize={12} color="#6B7280" fontStyle="italic" mt={2}>
        Scheduled by: Dr. Abdul Hameed, 12th Nov 2024, 4:50pm
      </Typography> */}

      {scheduleList.size > 1 && (
        <Box display="flex" alignItems="center" justifyContent="space-between" mt={1}>
          <Box
            display="flex"
            alignItems="center"
            color="#6B7280"
            className="cursor-pointer"
            onClick={() => navigateSchedule('previous')}
          >
            <KeyboardArrowLeftOutlinedIcon />
            <Typography fontSize={14} fontWeight={500}>
              Previous
            </Typography>
          </Box>

          <SecondaryText fontSize={14} text={`${currentIndex}/${scheduleList.size}`} />

          <Box
            display="flex"
            alignItems="center"
            color="#0064C8"
            className="cursor-pointer"
            onClick={() => navigateSchedule('next')}
          >
            <Typography fontSize={14} fontWeight={500}>
              Next
            </Typography>
            <KeyboardArrowRightOutlinedIcon />
          </Box>
        </Box>
      )}
    </Popover>
  );
};
EventPopover.propTypes = {
  anchorEl: PropTypes.object,
  scheduleList: PropTypes.instanceOf(Map),
  currentScheduleId: PropTypes.string,
  handleClose: PropTypes.func,
  handleDelete: PropTypes.func,
  handleClickEdit: PropTypes.func,
  navigateSchedule: PropTypes.func,
};

const ScheduleCalendar = ({
  calendarComponentRef,
  validRange,
  events,
  scheduleList,
  initialDate,
  fetchEventList,
  handleTimeSelect,
  handleClickEdit,
}) => {
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState(null);
  const [deleteModalData, setDeleteModalData] = useState(Map());
  const [currentScheduleId, setCurrentScheduleId] = useState(null);

  useEffect(() => {
    if (!anchorEl) setCurrentScheduleId(null);
  }, [anchorEl]);

  const filteredEvents = useMemo(() => {
    let prevRange = null;
    let sessions = [];

    events.forEach((item) => {
      if (!item.isSession) return sessions.push(item);

      const currentRange = [new Date(item.start).getTime(), new Date(item.end).getTime()];
      if (prevRange && currentRange[0] >= prevRange[0] && currentRange[0] < prevRange[1])
        return sessions[sessions.length - 1].count++;

      sessions.push({ ...item, count: 1 });
      prevRange = currentRange;
    });

    return sessions; // filtered to remove multi schedules in same time slot
  }, [events]);

  const { minTime, maxTime } = useMemo(() => {
    let minHours = 7;
    let maxHours = 19;
    filteredEvents.forEach((item) => {
      if (item.start) {
        const startTime = moment(item.start).hour();
        if (startTime < minHours) minHours = startTime;
      }

      if (item.end) {
        const endTime = Number(moment(item.end).format('HH.mm'));
        if (endTime > maxHours) maxHours = Math.ceil(endTime);
      }
    });

    return { minTime: `${minHours}:00:00`, maxTime: `${maxHours}:00:00` };
  }, [filteredEvents]);

  const handleClose = () => setAnchorEl(null);

  const handleEventClick = (eventInfo) => {
    const { isSession, scheduleId } = eventInfo.event.extendedProps;
    if (!isSession) return;

    setCurrentScheduleId(scheduleId);
    setAnchorEl(eventInfo.el);
  };

  const navigateSchedule = (direction) => {
    if (!currentScheduleId || scheduleList.isEmpty()) return;
    const scheduleIds = scheduleList.keySeq().toArray();
    const currentIndex = scheduleIds.indexOf(currentScheduleId);
    const totalSchedules = scheduleIds.length;
    let newIndex;
    if (direction === 'previous') {
      newIndex = currentIndex === 0 ? totalSchedules - 1 : currentIndex - 1;
    } else if (direction === 'next') {
      newIndex = currentIndex === totalSchedules - 1 ? 0 : currentIndex + 1;
    } else {
      return;
    }
    const targetScheduleId = scheduleIds[newIndex];
    const targetSchedule = scheduleList.get(targetScheduleId, Map());
    if (targetSchedule) setCurrentScheduleId(targetScheduleId);
  };

  const handleDeleteModal = (scheduleData) => {
    handleClose();
    setDeleteModalData(Map({ show: true, scheduleData }));
  };

  const deleteCallback = () => {
    setDeleteModalData(Map());
    fetchEventList();
  };

  const handleDelete = () => {
    const scheduleData = deleteModalData.get('scheduleData', Map());
    const scheduleId = scheduleData.get('_id');
    const session = scheduleData.get('session', Map());
    const deliverySymbol = session.get('delivery_symbol', '');
    const deliveryNo = session.get('delivery_no', '');
    const sessionTopic = session.get('session_topic', '');
    const topicName = `${deliverySymbol}${deliveryNo} - ${sessionTopic}`;

    dispatch(deleteSchedule({ scheduleId, topicName, callback: deleteCallback }));
  };

  const onClickEdit = (scheduleData) => {
    handleClose();
    handleClickEdit(scheduleData);
  };

  return (
    <>
      <FullCalendar
        ref={calendarComponentRef}
        plugins={[timeGridPlugin, interactionPlugin]}
        headerToolbar={false}
        dayHeaderContent={dayHeaderContent}
        initialView="timeGridWeek"
        slotMinTime={minTime}
        slotMaxTime={maxTime}
        slotDuration="01:00:00"
        slotLabelContent={slotLabelContent}
        events={filteredEvents}
        dayHeaderFormat={{ day: 'numeric', weekday: 'short' }}
        validRange={validRange}
        scrollTime="08:00:00"
        eventOverlap={true}
        editable={false}
        selectable={true}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
        contentHeight="auto"
        select={handleTimeSelect}
        eventContent={renderEventContent}
        locale={getLang() === 'ar' ? arLocale : null}
        slotEventOverlap={true}
        eventClick={handleEventClick}
        allDaySlot={false}
        longPressDelay={100}
        initialDate={initialDate}
      />

      <EventPopover
        anchorEl={anchorEl}
        scheduleList={scheduleList}
        currentScheduleId={currentScheduleId}
        handleClose={handleClose}
        handleDelete={handleDeleteModal}
        handleClickEdit={onClickEdit}
        navigateSchedule={navigateSchedule}
      />

      {deleteModalData.get('show') && (
        <Suspense fallback="">
          <DeleteSessionModal
            open
            data={deleteModalData.get('scheduleData', Map())}
            handleClose={() => setDeleteModalData(Map())}
            handleDelete={handleDelete}
          />
        </Suspense>
      )}
    </>
  );
};

export default ScheduleCalendar;
