import React, { lazy, Suspense, useState } from 'react';
import PropTypes from 'prop-types';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { getLang } from 'utils';
import arLocale from '@fullcalendar/core/locales/ar';
import moment from 'moment';
import './calendar.css';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { Box, IconButton, Popover, Typography } from '@mui/material';
import { List, Map } from 'immutable';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import CloseIcon from '@mui/icons-material/Close';
import KeyboardArrowLeftOutlinedIcon from '@mui/icons-material/KeyboardArrowLeftOutlined';
import KeyboardArrowRightOutlinedIcon from '@mui/icons-material/KeyboardArrowRightOutlined';
import { SessionDetails } from './utils';
import { useDispatch } from 'react-redux';
import { deleteSchedule } from '_reduxapi/course_scheduling/action';

const DeleteSessionModal = lazy(() => import('./Modals/DeleteSessionModal'));

const sessionTimingSx = {
  padding: '2px 8px',
  fontSize: 10,
  borderRadius: '4px',
  backgroundColor: '#E5E7EB',
};
const paperPropsSx = {
  padding: '8px 20px 16px',
  borderRadius: '24px',
  backgroundColor: '#F4F5FA',
  color: '#4B5563',
  minWidth: '500px',
};
const iconSx = {
  color: '#4B5563',
};

const dayHeaderContent = (dt) => {
  return (
    <div className="custom-day-header">
      <p className="bold">{moment(dt.date).format('ddd')}</p>
      <p>{moment(dt.date).format('D')}</p>
    </div>
  );
};

const slotLabelContent = (dt) => {
  return <div className="custom-slot-label">{moment(dt.date).format('h A')}</div>;
};

const renderEventContent = (eventInfo) => {
  const { start, end, title, extendedProps } = eventInfo.event;
  const { isSession, sessionInfo, eventType } = extendedProps;

  if (!isSession) {
    return (
      <>
        <div>{title}</div>
        <div>{eventType}</div>
      </>
    );
  }

  return (
    <div>
      <Typography mb="2px" lineHeight={1.25}>
        <Typography component="span" sx={sessionTimingSx}>
          {moment(start).format('h A')} - {moment(end).format('h A')}
        </Typography>
      </Typography>

      <Typography fontSize={12} mb="2px" lineHeight={1.25}>
        {getShortString(title, 30)}
      </Typography>

      <Typography fontSize={10} color="#6B7280" lineHeight={1.25}>
        {getShortString(sessionInfo, 66)}
      </Typography>
    </div>
  );
};

const EventPopover = ({ anchorEl, eventList, handleClose, handleDelete }) => {
  const open = Boolean(anchorEl);
  const id = open ? 'event-popover' : undefined;
  const scheduleData = eventList.get(0, Map());

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
      transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      slotProps={{ paper: { sx: paperPropsSx } }}
    >
      <Box display="flex" alignItems="center" justifyContent="end">
        <IconButton sx={iconSx} onClick={() => handleDelete(scheduleData)}>
          <DeleteOutlineIcon />
        </IconButton>
        <IconButton sx={iconSx} onClick={() => {}}>
          <EditOutlinedIcon />
        </IconButton>
        <IconButton sx={iconSx} onClick={handleClose}>
          <CloseIcon />
        </IconButton>
      </Box>

      <div className="mb-3">
        <SessionDetails scheduleData={scheduleData} />
      </div>

      {/* <Typography fontSize={12} color="#6B7280" fontStyle="italic" mt={2}>
        Scheduled by: Dr. Abdul Hameed, 12th Nov 2024, 4:50pm
      </Typography> */}

      <Box display="flex" alignItems="center" mt={1}>
        <Box display="flex" alignItems="center" color="#6B7280" className="cursor-pointer">
          <KeyboardArrowLeftOutlinedIcon />
          <Typography fontSize={14} fontWeight={500}>
            Previous
          </Typography>
        </Box>

        <Box display="flex" alignItems="center" color="#0064C8" className="cursor-pointer ml-auto">
          <Typography fontSize={14} fontWeight={500}>
            Next
          </Typography>
          <KeyboardArrowRightOutlinedIcon />
        </Box>
      </Box>
    </Popover>
  );
};
EventPopover.propTypes = {
  anchorEl: PropTypes.object,
  eventList: PropTypes.instanceOf(List),
  handleClose: PropTypes.func,
  handleDelete: PropTypes.func,
};

const ScheduleCalendar = ({
  calendarComponentRef,
  validRange,
  events,
  scheduleList,
  initialDate,
  fetchEventList,
}) => {
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState(null);
  const [scheduleView, setScheduleView] = useState(Map());
  const [deleteModalData, setDeleteModalData] = useState(Map());

  const handleClose = () => {
    setAnchorEl(null);
    setScheduleView(Map());
  };

  const handleEventClick = (eventInfo) => {
    const { isSession, scheduleId } = eventInfo.event.extendedProps;
    if (!isSession) return;

    setAnchorEl(eventInfo.el);
    setScheduleView(List([scheduleList.get(scheduleId, Map())]));
  };

  const handleDeleteModal = (scheduleData) => {
    handleClose();
    setDeleteModalData(Map({ show: true, scheduleData }));
  };

  const deleteCallback = () => {
    setDeleteModalData(Map());
    fetchEventList();
  };

  const handleDelete = () => {
    const scheduleData = deleteModalData.get('scheduleData', Map());
    const scheduleId = scheduleData.get('_id');
    const session = scheduleData.get('session', Map());
    const deliverySymbol = session.get('delivery_symbol', '');
    const deliveryNo = session.get('delivery_no', '');
    const sessionTopic = session.get('session_topic', '');
    const topicName = `${deliverySymbol}${deliveryNo} - ${sessionTopic}`;

    dispatch(deleteSchedule({ scheduleId, topicName, callback: deleteCallback }));
  };

  return (
    <>
      <FullCalendar
        ref={calendarComponentRef}
        plugins={[timeGridPlugin, interactionPlugin]}
        headerToolbar={false}
        dayHeaderContent={dayHeaderContent}
        initialView="timeGridWeek"
        slotMinTime="08:00:00"
        slotMaxTime="18:00:00"
        slotDuration="01:00:00"
        slotLabelContent={slotLabelContent}
        events={events}
        dayHeaderFormat={{ day: 'numeric', weekday: 'short' }}
        validRange={validRange}
        scrollTime="08:00:00"
        eventOverlap={true}
        editable={false}
        selectable={true}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
        contentHeight="auto"
        select={() => {}}
        eventContent={renderEventContent}
        locale={getLang() === 'ar' ? arLocale : null}
        slotEventOverlap={false}
        eventClick={handleEventClick}
        allDaySlot={false}
        longPressDelay={100}
        initialDate={initialDate}
      />

      <EventPopover
        anchorEl={anchorEl}
        eventList={scheduleView}
        handleClose={handleClose}
        handleDelete={handleDeleteModal}
      />

      {deleteModalData.get('show') && (
        <Suspense fallback="">
          <DeleteSessionModal
            open
            data={deleteModalData.get('scheduleData', Map())}
            handleClose={() => setDeleteModalData(Map())}
            handleDelete={handleDelete}
          />
        </Suspense>
      )}
    </>
  );
};

export default ScheduleCalendar;
