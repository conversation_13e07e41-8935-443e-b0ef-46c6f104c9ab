import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter, useHistory, Link } from 'react-router-dom';

import AllColleges from './AllColleges';
import NewCollege from './NewCollege';
import parentContext from '.././Context/university-context';
import AuthIndex from '../../index';
import { selectColleges, selectBreadCrumb } from '_reduxapi/institution/selectors';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import * as actions from '_reduxapi/institution/actions';
import Breadcrumb from 'Widgets/Breadcrumb/v2/Breadcrumb';

function AllCollegeIndex({
  allCollegeData,
  getColleges,
  loggedInUserData,
  setBreadCrumb,
  breadcrumbs,
}) {
  const history = useHistory();
  const [limit, setLimit] = useState(10);
  const [pageNo, setPageNo] = useState(1);

  useEffect(() => {
    if (!loggedInUserData.isEmpty()) {
      const isUniversityAdmin = loggedInUserData.getIn(['university', 'status'], false);
      if (!isUniversityAdmin) {
        history.push('/university-details');
      }
    }
  }, [loggedInUserData, history]);
  const getUserID = () => {
    return loggedInUserData.getIn(['university', 'institutionId'], '');
  };
  const callId = getUserID();

  useEffect(() => {
    getColleges({ id: callId, pageNo, limit });
  }, [limit]); // eslint-disable-line

  useEffect(() => {
    setBreadCrumb([{ to: '#', label: `Colleges` }]);
  }, [setBreadCrumb]);

  return (
    <>
      <Breadcrumb>
        <Link
          className="breadcrumb-icon"
          style={{ color: '#fff' }}
          key={breadcrumbs.getIn([0, 'to'], '')}
          to={breadcrumbs.getIn([0, 'to'], '')}
        >
          {breadcrumbs.getIn([0, 'label'], '')}
        </Link>
      </Breadcrumb>
      <AuthIndex />{' '}
      {allCollegeData.get('childInstitutesCount', 0) ? (
        <parentContext.addCollegeContext.Provider
          value={{ allCollegeData, getColleges, callId, limit, setLimit, pageNo, setPageNo }}
        >
          <AllColleges />
        </parentContext.addCollegeContext.Provider>
      ) : (
        <NewCollege parentInstitute={callId} />
      )}
    </>
  );
}

AllCollegeIndex.propTypes = {
  allCollegeData: PropTypes.oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  getColleges: PropTypes.func,
  loggedInUserData: PropTypes.instanceOf(Map),
  setBreadCrumb: PropTypes.func,
  breadcrumbs: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    allCollegeData: selectColleges(state),
    loggedInUserData: selectUserInfo(state),
    breadcrumbs: selectBreadCrumb(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(AllCollegeIndex);
