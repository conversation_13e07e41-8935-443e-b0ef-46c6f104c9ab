import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Switch from 'react-switch';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import Tooltip from '@mui/material/Tooltip';
// import { Multiselect } from 'multiselect-react-dropdown';
import { List, Map } from 'immutable';
import { Modal, Form, Button } from 'react-bootstrap';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Chip from '@mui/material/Chip';
import EditIcon from '@mui/icons-material/Edit';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import success from '../../../../Assets/alert2.png';
import { getFormattedGroupName } from '../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import {
  getURLParams,
  isIndGroup,
  isManualAttendanceEnabled,
  isMissedSessionModuleEnabled,
  studentGroupRename,
} from 'utils';
import Checkbox from '@mui/material/Checkbox';
import { useStyles } from './designUtils';
import Typography from '@mui/material/Typography';
import ManualAttendanceModal from './ManualAttendanceModal';
import DeactivateManualAttendancesPopup from './DeactivateManualAttendancesPopup';
import { formattedFullName } from 'Modules/ReportsAndAnalytics/utils';
import SessionStatusManager from './SessionStatusManager';
function AdvanceSettings({
  advancedSettings,
  saveAdvanceSetting,
  manageTopics,
  autoAssignCourseDelivery,
  autoAssignCourseDeliveryList,
  setData,
  saveAssignCourseDelivery,
  isLoading,
  paginationCourseMetaData,
  isRotation,
  courseSchedule,
  currentCalendar,
  manualStaffSaveSetting,
  saveMissedSession,
}) {
  const timeWindow = [5, 10, 15];
  const classes = useStyles();
  const [breakSession, setBreakSession] = useState(false);
  const [autoAssign, setAutoAssign] = useState(false);
  // const [deliveryGroup, setDeliveryGroup] = useState(false);
  // const [deliveryType, setDeliveryType] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [mainTab, setMainTab] = useState(1);
  const [deliveryTypeList, setDeliveryTypeList] = useState(List());
  const [activeDelivery, setActiveDelivery] = useState(Map());
  const [activeSession, setActiveSession] = useState(Map());
  const [checked, setChecked] = useState(false);
  const [unSelect, setUnSelect] = useState(true);
  const [rotationCount, setRotationCount] = useState('');
  const [autoEndAttendance, setAutoEndAttendance] = useState(
    advancedSettings.getIn(['course_setting', 'auto_end_attendance_in'], 0)
  );
  const [manualModalOpen, setManualModalOpen] = useState(false);
  const [manualIsEdit, setManualIsEdit] = useState('');
  const [manualDeactivateModal, setManualDeactivateModal] = useState(false);
  const [changeAttendance, setChangeAttendance] = useState(
    advancedSettings.getIn(['course_setting', 'isMissedToSchedule'], false)
  );
  const initialState = Map({
    status: false,
    staffs: List(),
    selectedStaff: List(),
    manualAttendance: false,
  });
  const [manualStaff, setManualStaff] = useState(initialState);

  const programId = getURLParams('programId', true);

  useEffect(() => {
    setManualStaff(
      manualStaff
        .set('staffs', advancedSettings.getIn(['course_setting', 'assignedStaffIds'], List()))
        .set(
          'selectedStaff',
          advancedSettings
            .getIn(['course_setting', 'assignedStaffIds'], List())
            .map((item) => item.get('staffId'))
        )
        .set(
          'manualAttendance',
          advancedSettings.getIn(['course_setting', 'manualAttendance'], false)
        )
        .set('status', advancedSettings.getIn(['course_setting', 'isStaffAssigned'], false))
    );
  }, [advancedSettings]); //eslint-disable-line

  const handleClose = (status = '') => {
    if (manualIsEdit !== 'edit') {
      handleChange(false, 'manualAttendance', 'ifCancelFromManualModal', status);
    } else {
      setManualModalOpen(false);
    }
  };

  const [infiniteRef] = useInfiniteScroll({
    loading: isLoading,
    hasNextPage: checkIfHasNextPage(),
    onLoadMore: () => {
      const { currentPage, totalPages } = getPaginationCourseMetaData();
      if (currentPage === null) return;
      if (currentPage === totalPages) return;
      autoAssignCourseDelivery({
        page: paginationCourseMetaData.get('currentPage') + 1,
        deliveryId: activeDelivery.get('id'),
        isRefresh: false,
        rotationCount,
      });
    },
  });

  function checkIfHasNextPage() {
    const { currentPage, totalPages } = getPaginationCourseMetaData();
    if (currentPage === null) return false;
    return currentPage !== totalPages;
  }

  function getPaginationCourseMetaData() {
    return {
      currentPage: paginationCourseMetaData.get('currentPage'),
      totalPages: paginationCourseMetaData.get('totalPages'),
    };
  }

  useEffect(() => {
    setUnSelect(true);
  }, []);

  useEffect(() => {
    if (!activeDelivery.isEmpty()) {
      setChecked(false);
      setData(
        Map({
          autoAssignCourseDeliveryList: List(),
          paginationCourseMetaData: Map({
            totalPages: null,
            currentPage: null,
          }),
        })
      );
      setTimeout(() => {
        autoAssignCourseDelivery({
          page: 1,
          pageSize: 10,
          deliveryId: activeDelivery.get('id'),
          isRefresh: true,
          rotationCount,
        });
      }, 500);
    }
    // eslint-disable-next-line
  }, [activeDelivery, autoAssignCourseDelivery, setData]);

  useEffect(() => {
    if (!advancedSettings.isEmpty()) {
      setBreakSession(advancedSettings.getIn(['course_setting', 'break_session_flow'], false));
      setAutoAssign(
        advancedSettings.getIn(['course_setting', 'auto_assign_session_default_settings'], false)
      );
      // setDeliveryGroup(
      //   advancedSettings.getIn(['course_setting', 'same_time_for_delivery_group', 'status'], false)
      // );
      // const deliveryType = advancedSettings
      //   .getIn(['course_setting', 'same_time_for_delivery_group', 'delivery_type'], List())
      //   .map((item) => {
      //     const getDetails = getDeliveryTypes(item.get('delivery_type_id'));
      //     return {
      //       id: item.get('delivery_type_id'),
      //       name: item.get('delivery_type_name'),
      //       symbol: getDetails[0]?.symbol,
      //     };
      //   })
      //   .toJS();
      // setDeliveryType(deliveryType);
      getDeliveryTypes();
    }
    // eslint-disable-next-line
  }, [advancedSettings]);

  // function handleSelect(selectedList) {
  //   const modifiedItem = selectedList.map((item) => {
  //     return { delivery_type_id: item.id, delivery_type_name: item.name };
  //   });
  //   handleChange(modifiedItem, 'deliveryType');
  // }

  // function handleRemove(selectedList) {
  //   const modifiedItem = selectedList.map((item) => {
  //     return { delivery_type_id: item.id, delivery_type_name: item.name };
  //   });
  //   handleChange(modifiedItem, 'deliveryType');
  // }

  function handleCheck(checked, index, name) {
    if (name === 'delivery') {
      const updateData = deliveryTypeList.setIn([index, 'checked'], checked);
      setDeliveryTypeList(updateData);
    }
  }

  function disabledCheck() {
    return courseSchedule.get('rotation', '') === 'yes'
      ? rotationCount !== '' &&
          deliveryTypeList.filter((item) => item.get('checked', false) === true).size > 0
      : deliveryTypeList.filter((item) => item.get('checked', false) === true).size > 0;
  }

  function handleChange(checked, name, key = '', status = '') {
    let data = {};
    switch (name) {
      case 'breakSession':
        setBreakSession(checked);
        data = {
          break_session_flow: checked,
        };
        break;
      case 'autoAssign':
        setAutoAssign(checked);
        data = {
          auto_assign_session_default_settings: checked,
        };
        if (checked) {
          setModalOpen(true);
        } else {
          setModalOpen(false);
        }
        break;
      // case 'deliveryGroup':
      //   setDeliveryGroup(checked);
      //   data = {
      //     same_time_for_delivery_group: {
      //       status: checked,
      //     },
      //   };
      //   if (!checked) {
      //     data.same_time_for_delivery_group.delivery_type = [];
      //   }
      //   break;
      case 'deliveryType':
        //setDeliveryType(checked);
        data = {
          same_time_for_delivery_group: {
            delivery_type: checked,
          },
        };
        break;
      case 'autoEndAttendance':
        setAutoEndAttendance(checked);
        data = {
          auto_end_attendance_in: checked,
        };
        break;
      case 'manualAttendance':
        if (status === 'onClose' && manualIsEdit !== 'edit') {
          setManualStaff(initialState);
        } else {
          setManualStaff(manualStaff.set('manualAttendance', checked));
        }
        if (checked) {
          setManualModalOpen(true);
        } else {
          setManualModalOpen(false);
          if (key === 'ifCancelFromManualModal') {
            setManualStaff(initialState);
            setManualDeactivateModal(false);
          } else {
            setManualDeactivateModal(true);
          }
        }
        break;
      case 'changeAttendance':
        setChangeAttendance(checked);
        data = {
          isMissedToSchedule: checked,
        };
        break;

      default:
        break;
    }
    if (name === 'changeAttendance') saveMissedSession(data);
    else {
      if (name !== 'manualAttendance') saveAdvanceSetting(data);
    }
  }

  function continueSchedule() {
    setMainTab(2);
    const data = deliveryTypeList.filter((item) => item.get('checked', false) === true);
    setActiveDelivery(data.get(0));
  }

  function checkAll(checked) {
    setChecked(checked);
    const updateData = autoAssignCourseDeliveryList.update((course) => {
      return course.map((item) => item.set('checked', checked));
    });
    setData(Map({ autoAssignCourseDeliveryList: updateData }));
  }

  function disabledSave() {
    return (
      autoAssignCourseDeliveryList.filter((item) => item.get('checked', false) === true).size > 0
    );
  }

  function getDeliveryTypes() {
    //filter = ''
    if (!advancedSettings.isEmpty()) {
      // let filterDeliveryId = [];
      // if (manageTopics && manageTopics.size > 0)
      //   filterDeliveryId = manageTopics
      //     .reduce((acc, c) => {
      //       const deliveryTypeDetails = c.get('delivery_type_details', List());
      //       if (deliveryTypeDetails.isEmpty()) {
      //         return acc;
      //       }
      //       return acc.concat(
      //         deliveryTypeDetails.reduce((acc1, a) => {
      //           return acc1.push(a.get('delivery_type_id'));
      //         }, List())
      //       );
      //     }, List())
      //     .toJS();
      // }
      const deliveryTypeList = advancedSettings
        .get('delivery_type', List())
        //.filter((item) => filterDeliveryId.includes(item.get('_delivery_id')))
        // .filter((item) => (filter !== '' ? item.get('_delivery_id') === filter : item))
        .map((item) => {
          return Map({
            id: item.get('_delivery_id'),
            name: item.get('delivery_type'),
            symbol: item.get('delivery_symbol'),
            checked: false,
          });
        });
      setDeliveryTypeList(deliveryTypeList);
    }
  }

  function handleSingleCheck(checked, index) {
    const updateData = autoAssignCourseDeliveryList.setIn([index, 'checked'], checked);
    setData(Map({ autoAssignCourseDeliveryList: updateData }));
  }

  // function getGroupedStudentGroups(studentGroups) {
  //   return studentGroups.groupBy((sGroup) => sGroup.get('group_no')).toMap();
  // }

  function formatArrayGroups(sGroup) {
    const arrayGroup = [];
    sGroup &&
      sGroup.map((sGrp) => {
        const studentGroupName = getFormattedGroupName(
          studentGroupRename(sGrp.get('group_name', ''), programId),
          isIndGroup(sGrp.get('group_name', '')) ? 1 : 2
        );
        const sessionGroupNames = sGrp
          .get('session_group', List())
          .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
          .join(', ');
        const formattedGroupName = `${studentGroupName} - ${sessionGroupNames}`;
        arrayGroup.push(formattedGroupName);
        return sGrp;
      });
    const merge = sGroup.get(0, Map()).merge(Map({ formattedGroupName: arrayGroup.join(', ') }));
    return List([merge]);
  }

  function renderStudentGroupsInSettings(session) {
    // let studentGroups = session.get('student_groups', List()).map((studentGroup) =>
    //   studentGroup.getIn(['groups', 0], Map()).merge(
    //     Map({
    //       status: studentGroup.get('status'),
    //       error_response: studentGroup.get('error_response', List()),
    //     })
    //   )
    // );
    //let arrayOfGroup = [];
    const studentGroupData = session.get('student_groups', List()).map((studentGroup) =>
      studentGroup.get('groups').map((item) => {
        const mergeData = {
          status: studentGroup.get('status'),
          error_response: studentGroup.get('error_response', List()),
        };
        //arrayOfGroup.push({ ...item.toJS(), ...mergeData });
        return item.merge(Map(mergeData));
      })
    );
    let studentGroups = studentGroupData;
    // if (isRotation) {
    //   studentGroups = getGroupedStudentGroups(studentGroupData.get(0, List())).entrySeq(); //need to check
    // }
    // const formattedStudentGroupList = studentGroups.map((studentGroup) => {
    //   const groupNo = isRotation ? studentGroup[0] : '';
    //   const sGroups = isRotation ? studentGroup[1] : List([studentGroup]);
    //   return Map({
    //     ...(isRotation && { groupNo }),
    //     groups: sGroups.map((sGroup) => {
    //       const studentGroupName = getFormattedGroupName(sGroup.get('group_name', ''), 2);
    //       const sessionGroupNames = sGroup
    //         .get('session_group', List())
    //         .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
    //         .join(', ');
    //       const formattedGroupName = `${studentGroupName} - ${sessionGroupNames}`;
    //       return sGroup.merge(Map({ formattedGroupName }));
    //     }),
    //   });
    // });

    const formattedStudentGroupList = studentGroups.map((studentGroup, i) => {
      const groupNo = isRotation ? studentGroup.getIn([0, 'group_no'], '') : '';
      return Map({
        ...(isRotation && i === 0 && { groupNo }),
        groups: formatArrayGroups(studentGroup),
        // groups: !isRotation
        //   ? formatArrayGroups(sGroups)
        //   : sGroups.map((sGroup) => {
        //       const studentGroupName = getFormattedGroupName(sGroup.get('group_name', ''), 2);
        //       const sessionGroupNames = sGroup
        //         .get('session_group', List())
        //         .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
        //         .join(', ');
        //       const formattedGroupName = `${studentGroupName} - ${sessionGroupNames}`;
        //       return sGroup.merge(Map({ formattedGroupName }));
        //     }),
      });
    });
    return formattedStudentGroupList.map((group, index) => (
      <React.Fragment key={`${session.get('_id')}-${index}`}>
        {isRotation && group.get('groupNo', '') !== '' && (
          <div className="pt-0 pb-2 pl-4">{`R${group.get('groupNo', '')}`}</div>
        )}
        {group.get('groups', List()).map((studentGroup) => {
          return (
            <div
              className="d-flex justify-content-start pt-0 pb-2 pl-4"
              key={studentGroup.get('_id')}
            >
              <span className="pr-2 pl-3">
                <i className="fa fa-level-up fa-rotate-90 f-12 mr-2" aria-hidden="true"></i>
              </span>
              <span>
                {studentGroup.get('status', false) ? (
                  <img src={success} alt="" title="" />
                ) : (
                  <i className="fa fa-exclamation-circle text-gray" aria-hidden="true"></i>
                )}
              </span>
              <span className="pl-2 ">{studentGroup.get('formattedGroupName', '')}</span>
              {studentGroup.get('error_response', '').size > 0 && (
                <Tooltip
                  title={
                    <table>
                      {studentGroup.get('error_response', '').map((item, i) => (
                        <tr key={i}>{item.get('message', '')}.</tr>
                      ))}
                    </table>
                  }
                >
                  <span>
                    <i
                      className="fa fa-exclamation-circle text-red pl-2 remove_hover"
                      aria-hidden="true"
                    ></i>
                  </span>
                </Tooltip>
              )}
            </div>
          );
        })}
      </React.Fragment>
    ));
  }

  return (
    <div className="p-3">
      {isManualAttendanceEnabled() && (
        <div className="pb-3">
          <div className="border border-radious-8 p-3">
            <div className="d-flex justify-content-between">
              <div className="">
                <p className="mb-0 bold">Secondary Attendance</p>
                <span className="f-14 text-gray">
                  The Secondary Attendance Flow enables users to take multiple attendance manually.
                </span>
              </div>
              <Switch
                checked={manualStaff.get('manualAttendance', false)}
                onChange={(checked) => handleChange(checked, 'manualAttendance')}
                onColor="#86d3ff"
                onHandleColor="#2693e6"
                handleDiameter={20}
                uncheckedIcon={false}
                checkedIcon={false}
                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                height={19}
                width={40}
                className="react-switch"
                id="material-switch"
              />
            </div>
            {manualStaff.get('staffs', List()).size > 0 && (
              <div className="mb-1">
                {`${
                  manualStaff.get('staffs', List()).size < 2 ? 'Assigned staff' : 'Assigned staffs'
                }`}
              </div>
            )}
            <div className="d-flex">
              {manualStaff.get('staffs', List()).size > 0 &&
                manualStaff.get('staffs', List()).map((staff, staffIndex) => {
                  return (
                    <Chip
                      key={staffIndex}
                      label={formattedFullName(staff.get('staffName', Map()).toJS())}
                      variant="outlined"
                      className="mr-1"
                    />
                  );
                })}
              {manualStaff.get('manualAttendance', false) &&
                manualStaff.get('selectedStaff', List()).size > 0 && (
                  <EditIcon
                    className="ml-auto"
                    alt="edit"
                    onClick={() => {
                      setManualModalOpen(true);
                      setManualIsEdit('edit');
                    }}
                  />
                )}
            </div>
          </div>
        </div>
      )}
      <div className="pb-3">
        <div className="border border-radious-8 p-3">
          <div className="d-flex justify-content-between">
            <div className="">
              <p className="mb-0 bold">
                <Trans i18nKey={'course_schedule.break_session_flow'}></Trans>
              </p>
              <span className="f-14 text-gray">
                {' '}
                <Trans i18nKey={'course_schedule.break_session_flow_desc'}></Trans>
              </span>
            </div>
            <Switch
              disabled={
                !CheckPermission(
                  'subTabs',
                  'Schedule Management',
                  'Course Scheduling',
                  '',
                  'Schedule',
                  '',
                  'Manage Course',
                  'Advance Settings Edit'
                ) && !currentCalendar
              }
              checked={breakSession}
              onChange={(checked) => (currentCalendar ? handleChange(checked, 'breakSession') : {})}
              onColor="#86d3ff"
              onHandleColor="#2693e6"
              handleDiameter={20}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={19}
              width={40}
              className="react-switch"
              id="material-switch"
            />
          </div>
        </div>
      </div>
      <div className="pb-3">
        <div className="border border-radious-8 p-3">
          <div className="d-flex justify-content-between">
            <div className="">
              <p className="mb-0 bold">
                <Trans i18nKey={'course_schedule.session_default_settings'}></Trans>
              </p>
              <span className="f-14 text-gray">
                {' '}
                <Trans i18nKey={'course_schedule.session_default_settings_desc'}></Trans>
              </span>
              {/* <span className="f-14 text-gray"> {capitalize(course.get('course_type', ''))}</span>
              <span className="f-14 text-gray"> | {getFormattedCreditHours(course)}</span> */}
            </div>
            <Switch
              disabled={
                !CheckPermission(
                  'subTabs',
                  'Schedule Management',
                  'Course Scheduling',
                  '',
                  'Schedule',
                  '',
                  'Manage Course',
                  'Advance Settings Edit'
                ) && !currentCalendar
              }
              checked={autoAssign}
              onChange={(checked) => (currentCalendar ? handleChange(checked, 'autoAssign') : {})}
              onColor="#86d3ff"
              onHandleColor="#2693e6"
              handleDiameter={20}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={19}
              width={40}
              className="react-switch"
              id="material-switch"
            />
          </div>
        </div>
      </div>
      <div className="pb-3">
        <div className="border border-radious-8 p-3">
          <div className="d-flex justify-content-between">
            <div className="">
              <p className="mb-0 bold">
                <Trans i18nKey={'Auto-end Attendance in'}></Trans>
              </p>
              <span className="f-14 text-gray">
                <Trans
                  i18nKey={'Attendance will be end, Based on the Min Window selection'}
                ></Trans>
              </span>
            </div>
          </div>
          <div className="row">
            {timeWindow.map((item, index) => (
              <div
                key={index}
                onClick={() => {
                  if (
                    CheckPermission(
                      'subTabs',
                      'Schedule Management',
                      'Course Scheduling',
                      '',
                      'Schedule',
                      '',
                      'Manage Course',
                      'Advance Settings Edit'
                    ) &&
                    currentCalendar
                  ) {
                    handleChange(item, 'autoEndAttendance');
                  }
                }}
                className={`d-flex align-items-center remove_hover ml-3 col-lg-2 col-md-3 pl-0 rounded border ${
                  autoEndAttendance === item && 'border-primary'
                }`}
              >
                <Checkbox
                  checked={autoEndAttendance === item}
                  className={classes.checked}
                  disabled={
                    !CheckPermission(
                      'subTabs',
                      'Schedule Management',
                      'Course Scheduling',
                      '',
                      'Schedule',
                      '',
                      'Manage Course',
                      'Advance Settings Edit'
                    ) && !currentCalendar
                  }
                />
                <Typography
                  sx={{ whiteSpace: 'nowrap' }}
                  variant="subtitle2"
                  className={`${classes.Text} ${
                    autoEndAttendance === item ? 'text-dark' : 'text-muted'
                  }`}
                >
                  {' '}
                  {`${item} Min Window`}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </div>

      {CheckPermission(
        'pages',
        'Session Missed To Completed Status',
        'Manage Course Advance Settings',
        'Edit Advance Setting Missed To Complete'
      ) &&
        isMissedSessionModuleEnabled() &&
        currentCalendar && (
          <div className="pb-3">
            <div className="border border-radious-8 p-3">
              <div className="d-flex justify-content-between">
                <div className="">
                  <p className="mb-0 bold">Session Status Change</p>
                  <span className="f-14 text-gray">
                    {' '}
                    Are scheduled staff able to change missed sessions to completed sessions?
                  </span>
                </div>

                <Switch
                  checked={changeAttendance}
                  onChange={(checked) => handleChange(checked, 'changeAttendance')}
                  onColor="#86d3ff"
                  onHandleColor="#2693e6"
                  handleDiameter={20}
                  uncheckedIcon={false}
                  checkedIcon={false}
                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                  height={19}
                  width={40}
                  className="react-switch"
                  id="material-switch"
                />
              </div>
            </div>
          </div>
        )}
      <SessionStatusManager />

      {/* <div className="pb-3">
        <div className="border border-radious-8 p-3">
          <div className="d-flex justify-content-between">
            <div className="">
              <p className="mb-0 bold"> Same time for delivery group</p>
              <span className="f-14 text-gray">
                {' '}
                Same date and time will be populated for all Delivery types
              </span>
              {deliveryGroup && (
                <div className="pt-2">
                  <span className=""> Delivery type </span>
                  <Multiselect
                    options={getDeliveryTypes()}
                    displayValue="name"
                    showCheckbox={true}
                    style={style}
                    closeIcon="close"
                    placeholder="Select Delivery Type"
                    onSelect={handleSelect}
                    onRemove={handleRemove}
                    selectedValues={deliveryType}
                    closeOnSelect={false}
                  />
                </div>
              )}
            </div>

            <Switch
              checked={deliveryGroup}
              onChange={(checked) => handleChange(checked, 'deliveryGroup')}
              onColor="#86d3ff"
              onHandleColor="#2693e6"
              handleDiameter={20}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={19}
              width={40}
              className="react-switch"
              id="material-switch"
            />
          </div>
        </div>
      </div> */}
      {manualModalOpen && (
        <ManualAttendanceModal
          open={manualModalOpen}
          handleClose={handleClose}
          manualStaff={manualStaff}
          setManualStaff={setManualStaff}
          manualStaffSaveSetting={manualStaffSaveSetting}
          setManualModalOpen={setManualModalOpen}
        />
      )}
      {manualDeactivateModal && (
        <DeactivateManualAttendancesPopup
          manualDeactivateModal={manualDeactivateModal}
          setManualDeactivateModal={setManualDeactivateModal}
          manualStaffSaveSetting={manualStaffSaveSetting}
          manualStaff={manualStaff}
          setManualStaff={setManualStaff}
        />
      )}
      {modalOpen && (
        <Modal show={modalOpen} dialogClassName="model-600" centered>
          <Modal.Body className="pt-1 pl-0 pr-0 pb-0">
            <div className="p-2 border-bottom">
              <p className="mb-1 pl-2 bold">
                {' '}
                {mainTab === 1
                  ? t('course_schedule.choose_delivery_types')
                  : t('course_schedule.auto_assign_session_default_settings')}{' '}
                {mainTab === 1 && courseSchedule.get('rotation', '') === 'yes' && (
                  <div style={{ display: 'inline-flex' }}>
                    <FormControl fullWidth variant="outlined" size="small">
                      <Select
                        native
                        value={rotationCount}
                        onChange={(e) => setRotationCount(e.target.value)}
                      >
                        <option value="">Select Rotation</option>
                        {courseSchedule.get('rotation_dates', Map()).map((option) => (
                          <option
                            key={option.get('rotation_count', 0)}
                            value={option.get('rotation_count', 0)}
                          >
                            R{option.get('rotation_count', 0)}
                          </option>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                )}
              </p>
            </div>
            <div className="pl-3 pr-3">
              <div className="row" style={{ minHeight: '200px' }}>
                <div className={`col-md-${mainTab === 2 ? '4 border-right' : '12'} pl-0 pr-0`}>
                  {mainTab === 2 && (
                    <p className="mb-2 mt-2 f-14 text-gray ml-2 text-uppercase">
                      <Trans i18nKey={'course_schedule.delivery_types'}></Trans>
                    </p>
                  )}
                  {deliveryTypeList.size > 0 &&
                    deliveryTypeList
                      .filter((item) =>
                        mainTab === 2 ? item.get('checked', false) === true : item
                      )
                      .map((delivery, index) => {
                        return (
                          <div
                            className={`model_hover remove_hover ${
                              delivery.get('id') === activeDelivery.get('id') ? 'model_active' : ''
                            }`}
                            key={index}
                            onClick={() => {
                              if (mainTab === 2) {
                                setActiveDelivery(delivery);
                              }
                            }}
                          >
                            <div className="pl-2 pb-2">
                              <Form.Check
                                type="checkbox"
                                label={delivery.get('name')}
                                value={delivery.get('value')}
                                className="pr-1 pr-2 pt-2 pl-4"
                                checked={delivery.get('checked', false)}
                                onChange={(e) => {
                                  if (mainTab === 1) {
                                    handleCheck(e.target.checked, index, 'delivery');
                                  }
                                }}
                              />
                            </div>
                          </div>
                        );
                      })}
                </div>
                {mainTab === 2 && (
                  <div className="col-md-8">
                    <p className="mb-2 mt-2 f-14 text-gray text-uppercase">
                      {' '}
                      <Trans i18nKey={'course_schedule.scheduled_session'}></Trans>
                      <Form.Check
                        type="checkbox"
                        checked={checked}
                        onChange={(e) => checkAll(e.target.checked)}
                        className="pr-1 float-right"
                      />
                    </p>
                    {autoAssignCourseDeliveryList.size === 0 && (
                      <div className="text-center text-gray">
                        <Trans i18nKey={'course_schedule.no_session_found'}></Trans>
                      </div>
                    )}
                    <div className="roles_height" style={{ maxHeight: '500px', padding: '5px' }}>
                      {autoAssignCourseDeliveryList.size > 0 &&
                        autoAssignCourseDeliveryList.map((session, sIndex) => {
                          const totalSGGroup = session.get('student_groups', List()).size;
                          const totalActiveSGGroup = session
                            .get('student_groups', List())
                            .filter((item) => item.get('status', false) === true).size;
                          return (
                            <div className="border-bottom" key={sIndex}>
                              <div className="d-flex justify-content-between pt-2 pb-2">
                                <div className="d-flex justify-content-around pb-2">
                                  <Form.Check
                                    type="checkbox"
                                    label=""
                                    checked={session.get('checked', false)}
                                    onChange={(e) => handleSingleCheck(e.target.checked, sIndex)}
                                    className="pr-1"
                                  />
                                  <span>
                                    {' '}
                                    {totalSGGroup === totalActiveSGGroup ? (
                                      <img src={success} alt="" title="" />
                                    ) : (
                                      <i
                                        className="fa fa-exclamation-circle text-gray"
                                        aria-hidden="true"
                                      ></i>
                                    )}{' '}
                                  </span>
                                  <span
                                    className="pl-2 remove_hover"
                                    onClick={() => setActiveSession(session)}
                                  >{`${session.get('delivery_symbol', '')}${session.get(
                                    'delivery_no',
                                    ''
                                  )}${' - '}${session.get('session_topic', '')}`}</span>
                                </div>
                                <b
                                  className="f-19 remove_hover"
                                  onClick={() => setActiveSession(session)}
                                >
                                  <i
                                    className={`fa fa-angle-${
                                      session.get('_session_id', '') ===
                                      activeSession.get('_session_id', '')
                                        ? 'up'
                                        : 'down'
                                    } text-skyblue`}
                                    aria-hidden="true"
                                  ></i>
                                </b>
                              </div>
                              {session.get('_session_id', '') ===
                              activeSession.get('_session_id', '')
                                ? renderStudentGroupsInSettings(session)
                                : null}
                              {checkIfHasNextPage() && <div ref={infiniteRef} />}
                            </div>
                          );
                        })}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <div className="container">
              <div className="row">
                <div className="col-md-12">
                  <div className="float-right">
                    <b className="pr-3">
                      <Button
                        variant="outline-primary"
                        onClick={() => {
                          setMainTab(1);
                          setData(Map({ autoAssignCourseDeliveryList: List() }));
                          setActiveDelivery(Map());
                          setActiveSession(Map());
                          setRotationCount('');
                          if (unSelect) {
                            handleChange(false, 'autoAssign');
                          } else {
                            setModalOpen(false);
                          }
                        }}
                      >
                        <Trans i18nKey={'close'}></Trans>
                      </Button>
                    </b>
                    {mainTab === 2 && (
                      <b className="pr-3">
                        <Button
                          variant="outline-primary"
                          onClick={() => {
                            setMainTab(1);
                            setActiveDelivery(Map());
                          }}
                        >
                          <Trans i18nKey={'back'}></Trans>
                        </Button>
                      </b>
                    )}
                    <b className="">
                      {mainTab === 1 ? (
                        <Button
                          variant="primary"
                          disabled={!disabledCheck()}
                          onClick={continueSchedule}
                          className="f-14"
                        >
                          <Trans i18nKey={'continue'}></Trans>
                        </Button>
                      ) : (
                        <Button
                          variant="primary"
                          disabled={!disabledSave()}
                          onClick={() => {
                            saveAssignCourseDelivery('create', rotationCount);
                            setUnSelect(false);
                          }}
                          className="f-14"
                        >
                          <Trans i18nKey={'schedule'}></Trans>
                        </Button>
                      )}
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Footer>
        </Modal>
      )}
    </div>
  );
}

AdvanceSettings.propTypes = {
  saveAdvanceSetting: PropTypes.func,
  autoAssignCourseDelivery: PropTypes.func,
  setData: PropTypes.func,
  saveAssignCourseDelivery: PropTypes.func,
  isRotation: PropTypes.bool,
  isLoading: PropTypes.bool,
  courseSchedule: PropTypes.instanceOf(Map),
  advancedSettings: PropTypes.instanceOf(Map),
  paginationCourseMetaData: PropTypes.instanceOf(Map),
  manualStaffSaveSetting: PropTypes.instanceOf(Map),
  manageTopics: PropTypes.instanceOf(List),
  autoAssignCourseDeliveryList: PropTypes.instanceOf(List),
  currentCalendar: PropTypes.bool,
  saveMissedSession: PropTypes.func,
};

export default AdvanceSettings;
