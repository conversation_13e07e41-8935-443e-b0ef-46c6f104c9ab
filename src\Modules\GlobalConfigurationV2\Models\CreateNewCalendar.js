import React, { useCallback, useRef, useState } from 'react';
import { Map } from 'immutable';
import {
  <PERSON><PERSON>,
  Button,
  Dialog,
  TextField,
  DialogTitle,
  DialogContent,
  DialogActions,
  ClickAwayListener,
} from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';

import { MenuItem, Select, FormControl, IconButton } from '@mui/material';
import { setData } from '_reduxapi/q360/actions';
import { useDispatch } from 'react-redux';
import { fullMonthNames } from '../utils/jsUtils';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { createCalenderDesign } from '../components/Q360Configuration/ConfigureTemplate/CreateSettings/designUtils';

const {
  dialogSX,
  menuItemStyles,
  placeholderStyles,
  iconButtonStyles,
  widthModifier,
  selectArrowIconSx,
} = createCalenderDesign;

//----------------------------------JS Utils Start--------------------------------------------------
const monthNamesToIndex = {
  jan: 0,
  feb: 1,
  mar: 2,
  apr: 3,
  may: 4,
  jun: 5,
  jul: 6,
  aug: 7,
  sep: 8,
  oct: 9,
  nov: 10,
  dec: 11,
};

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const isDataEqual = (calendarData, cloneCalendar, formState) => {
  const formattedCalendarData = calendarData
    .set('start_date', new Date(calendarData.get('start_date')))
    .set('end_date', new Date(calendarData.get('end_date')));
  const formattedFormState = cloneCalendar
    .set('start_date', new Date(formState.get('start_date')))
    .set('end_date', new Date(formState.get('end_date')));
  const isDataEqual = formattedCalendarData.equals(formattedFormState);
  return isDataEqual;
};

const monthPickerRenderValue = (selected) =>
  selected || <span className="text-divider-color f-14">Select Month</span>;

const useCreateCalendar = (createCalendarForm, calendarListsHook) => {
  const dispatch = useDispatch();
  const { formState, closeForm } = createCalendarForm;
  const [calendarData, setCalendarData] = useState(formState);
  const [cloneCalendar] = useState(formState);
  const calendarId = calendarData.get('_id', '');
  const end_date = calendarData.get('end_date', '');
  const start_date = calendarData.get('start_date', '');
  const updateIndex = calendarData.get('calenderIndex', -1);
  const calendar_name = calendarData.get('calendar_name', '');

  const startMonth = new Date(start_date).getMonth();
  const startYear = new Date(start_date).getFullYear();
  const endMonth = new Date(end_date).getMonth();
  const endYear = new Date(end_date).getFullYear();

  const isCreate = Boolean(calendar_name && start_date && end_date);
  const isUpdated = isDataEqual(calendarData, cloneCalendar, formState);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCalendarData((prev) => prev.set(name, value));
  };

  const handleCloseForm = () => {
    setCalendarData(Map());
    closeForm(Map());
  };

  const handleCreateAndUpdateCalendar = (stateUpdateFunction) => () => {
    if (startMonth >= endMonth) {
      dispatch(setData(Map({ message: 'Start month should be before end month' })));
      return;
    }
    if (calendar_name.trim() === '') {
      dispatch(setData(Map({ message: 'Calendar Name is Required' })));
      return;
    }
    let updatedCalender = calendarData.set('batch', `${startYear}-${endYear}`);
    if (calendarId) {
      updatedCalender = calendarData.set('isEdited', true);
    }
    handleCloseForm();
    stateUpdateFunction(updatedCalender);
  };
  return {
    isCreate,
    isUpdated,
    updateIndex,
    calendarData,
    handleCloseForm,
    handleInputChange,
    handleCreateAndUpdateCalendar,
    calendarId,
  };
};

const useDatePicker = ({ value, onChange, name }) => {
  const initialYear = value ? new Date(value).getFullYear() : new Date().getFullYear();
  const monthAbbreviation = fullMonthNames.getIn([new Date(value).getMonth(), 'abbreviation'], '');

  const [open, setOpen] = useState(false);
  const [year, setYear] = useState(initialYear);
  const anchorRef = useRef(null);

  const handleChange = useCallback(
    (event) => {
      const month = event.currentTarget.getAttribute('value');
      const monthName = month.toLowerCase();
      const monthIndex = monthNamesToIndex[monthName];
      let date;
      if (name === 'start_date') {
        date = new Date(year, monthIndex, 1);
      } else {
        date = new Date(year, monthIndex + 1, 0);
      }
      onChange({ target: { name, value: formatDate(date) } });
      setOpen(false);
    },
    [year, onChange, name]
  );

  const handleToggle = useCallback(() => {
    setOpen((prevOpen) => !prevOpen);
  }, []);

  const handleClose = useCallback((event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  }, []);

  const handleYearChange = useCallback(
    (direction) => {
      setYear((prevYear) => prevYear + direction);
      onChange({
        target: {
          name,
          value: formatDate(new Date(year + direction, monthNamesToIndex[value.toLowerCase()], 1)),
        },
      });
    },
    [year, onChange, name, value]
  );

  return {
    open,
    year,
    anchorRef,
    handleChange,
    handleToggle,
    handleClose,
    handleYearChange,
    monthAbbreviation,
  };
};

//----------------------------------JS Utils End----------------------------------------------------

//----------------------------------CmponentStart--------------------------------------------------
const SelectArrowIcon = ({ open, sx }) => {
  return open ? <KeyboardArrowUpIcon sx={sx} /> : <KeyboardArrowDownIcon sx={sx} />;
};

const MonthPicker = ({ label, name, value, onChange }) => {
  const {
    open,
    year,
    anchorRef,
    handleClose,
    handleChange,
    handleToggle,
    handleYearChange,
    monthAbbreviation,
  } = useDatePicker({ value, onChange, name });

  return (
    <div className="w-100 d-flex flex-column">
      <label className="f-12 mb-1">{label}</label>
      <FormControl fullWidth>
        <Select
          open={false}
          size="small"
          displayEmpty
          ref={anchorRef}
          className="f-14"
          onClick={handleToggle}
          value={monthAbbreviation}
          IconComponent={(props) => <SelectArrowIcon open={open} sx={selectArrowIconSx} />}
          renderValue={monthPickerRenderValue}
        >
          {fullMonthNames.map((month, index) => (
            <MenuItem key={index} value={month.get('abbreviation', '')}>
              {month.get('abbreviation', '')}
            </MenuItem>
          ))}
        </Select>
        <Popper
          open={open}
          anchorEl={anchorRef.current}
          placement="bottom-start"
          className="monthPicker-popper"
          modifiers={widthModifier}
        >
          <ClickAwayListener onClickAway={handleClose}>
            <div className="monthPicker-drop-menu py-2">
              <div className="d-flex gap-8 align-items-center justify-content-between px-2">
                <IconButton
                  disableRipple
                  sx={iconButtonStyles}
                  onClick={() => handleYearChange(-1)}
                >
                  <ArrowBackIosNewIcon className="f-16" />
                </IconButton>
                <div className="f-16 bold">{year}</div>
                <IconButton disableRipple sx={iconButtonStyles} onClick={() => handleYearChange(1)}>
                  <ArrowForwardIosIcon className="f-16" />
                </IconButton>
              </div>
              <div className="monthPicker-menu-items px-1 mx-1 mt-2 g-config-scrollbar">
                {fullMonthNames.map((month, index) => (
                  <MenuItem
                    key={index}
                    sx={menuItemStyles}
                    onClick={handleChange}
                    value={month.get('abbreviation', '')}
                    className="d-flex justify-content-center p-0"
                  >
                    {month.get('abbreviation', '')}
                  </MenuItem>
                ))}
              </div>
            </div>
          </ClickAwayListener>
        </Popper>
      </FormControl>
    </div>
  );
};
const CalendarName = ({ calendarFormHook }) => {
  const { calendarData, handleInputChange } = calendarFormHook;
  const calendarName = calendarData.get('calendar_name', '');
  return (
    <>
      <label className="f-12 mb-0">Calendar Name</label>
      <TextField
        fullWidth
        size="small"
        name="calendar_name"
        value={calendarName}
        sx={placeholderStyles}
        onChange={handleInputChange}
        placeholder="Enter Calendar Name"
      />
    </>
  );
};
const CalendarMonth = ({ calendarFormHook }) => {
  const { calendarData, handleInputChange } = calendarFormHook;
  const endDate = calendarData.get('end_date', '');
  const startDate = calendarData.get('start_date', '');
  return (
    <div className="row m-0">
      <div className="col-md-6 col-12 pl-0 pr-0 pr-md-1 mb-md-0 mb-2">
        <MonthPicker
          label="Start Month"
          name="start_date"
          value={startDate}
          onChange={handleInputChange}
        />
      </div>
      <div className="col-md-6 col-12 pr-0 pl-0 pl-md-1">
        <MonthPicker
          label="End Month"
          name="end_date"
          value={endDate}
          onChange={handleInputChange}
        />
      </div>
    </div>
  );
};
const CalendarActionButtons = ({ calendarFormHook, calendarListsHook }) => {
  const {
    isCreate,
    isUpdated,
    handleCloseForm,
    handleCreateAndUpdateCalendar,
    updateIndex,
  } = calendarFormHook;
  const { handleCalendarCreation, handleCalendarEdit } = calendarListsHook;

  const buttonProperties = {
    create: {
      text: 'Create',
      disabled: !isCreate,
      onClick: handleCreateAndUpdateCalendar(handleCalendarCreation),
    },
    update: {
      text: 'Update',
      disabled: isUpdated,
      onClick: handleCreateAndUpdateCalendar(handleCalendarEdit),
    },
  };
  const { disabled, onClick, text } = buttonProperties[updateIndex !== -1 ? 'update' : 'create']; //assigning button properties based on action type (create, edit)
  return (
    <>
      <Button
        variant="outlined"
        onClick={handleCloseForm}
        className="text-capitalize px-4 text-secondary border-secondary bold"
      >
        Cancel
      </Button>
      <Button
        variant="contained"
        disabled={disabled}
        className="text-capitalize px-4"
        onClick={onClick}
      >
        {text}
      </Button>
    </>
  );
};

const CreateNewCalendar = ({ Open, createCalendarForm, calendarListsHook }) => {
  const calendarFormHook = useCreateCalendar(createCalendarForm, calendarListsHook);

  const { updateIndex } = calendarFormHook;
  return (
    <Dialog fullWidth open={Open} PaperProps={{ sx: dialogSX }}>
      <DialogTitle className="gray-neutral">
        {updateIndex !== -1 ? 'Update Calender' : 'Create New Calendar'}{' '}
      </DialogTitle>
      <DialogContent className="gray-neutral d-flex flex-column gap-10">
        <CalendarName calendarFormHook={calendarFormHook} />
        <CalendarMonth calendarFormHook={calendarFormHook} />
      </DialogContent>
      <DialogActions className="px-4 pb-4 gap-2">
        <CalendarActionButtons
          calendarFormHook={calendarFormHook}
          calendarListsHook={calendarListsHook}
        />
      </DialogActions>
    </Dialog>
  );
};

export default CreateNewCalendar;
