import React, { Component } from 'react';
import { with<PERSON>out<PERSON> } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';

import { Button, Accordion, Card, Form } from 'react-bootstrap';
import Switch from 'react-switch';

import * as actions from '../../../_reduxapi/leave_management/actions';
import { selectStaffList, selectLocations } from '../../../_reduxapi/leave_management/selectors';
import { selectUserId } from '../../../_reduxapi/Common/Selectors';

import '../../../Assets/css/grouping.css';
import StaffModal from '../modal/StaffModal';

class Reviewer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      arrow1: false,
      value: false,
      firstName: '',
      lastName: '',
      name: '',
      name1: '',
      searchText: '',
      isactive: false,
      roleId: null,
      role_id: null,
      enable: true,
      staff_type: '',
      staff_type1: '',
      firstName1: '',
      enablebutton: false,
      updateKey: '',
      enablebuttonadmin: false,
    };
  }
  iconPostion1 = () => {
    this.setState({
      arrow1: !this.state.arrow1,
    });
  };

  handleChange = (id, role, name) => {
    if (name === 'academic') {
      role === 'Custom'
        ? this.setState({ enablebutton: true, roleId: id, staff_type: name })
        : this.setState({ roleId: id, staff_type: name, enablebutton: false });
    } else {
      role === 'Custom'
        ? this.setState({ enablebuttonadmin: true, staff_type: name, role_id: id, enable: false })
        : this.setState({ role_id: id, staff_type: name, enable: false, enablebuttonadmin: false });
    }
  };

  handletoggle = () => {
    this.setState((prevstate) => ({ ...prevstate, isactive: !this.state.isactive }));
  };

  handleSearch = (e) => {
    this.setState(
      {
        searchText: e.target.value,
      },
      () => {
        this.props.getAllStaff('completed', this.state.searchText);
      }
    );
  };
  changeHandler = (value, id, name, firstName, middlename, lastName) => {
    if (this.state.updateKey === 'left') {
      if (value) {
        this.setState({
          selectedId: id,
          name: name,
          showIcon: !this.state.showIcon,
          firstName: firstName,
          lastName: lastName,
        });
      }
    } else {
      if (value) {
        this.setState({
          selectedId: id,
          name1: firstName + middlename + lastName,
          showIcon: !this.state.showIcon,

          firstName1: firstName,
          lastName1: lastName,
        });
      }
    }
  };
  removename = () => {
    this.setState({ name: '', firstName: '', lastName: ' ' });
  };
  removename1 = () => {
    this.setState({ name1: '', firstName: '', lastName: ' ' });
  };
  componentDidMount() {
    this.props.getAllStaff('completed');
    let enpoint = '/lms/list';
    this.props.getAllLeaveClassifications(enpoint);
  }

  handlecancel = () => {
    window.location.reload();
  };
  modalOpen = (status) => {
    this.setState({
      searchView: !this.state.searchView,
      removalIcon: true,
      searchText: '',
      updateKey: status,
    });
    this.state.searchText === '' && this.props.getAllStaff('completed');
  };
  modalClose = () => {
    this.setState({
      searchView: !this.state.searchView,

      selectedId: 0,
      searchText: '',
    });
  };
  handleSave = (revieweracademic, revieweradmin, active) => {
    this.setState({
      enable: false,
    });

    const obj = {
      data: [
        {
          _id: revieweracademic._id,
          isActive: true,
          _role_id: this.state.roleId,
          user_id: [
            {
              user_id: this.props.userId,
              name: {
                frist: this.state.firstName,
                last: this.state.lastName,
              },
            },
          ],
        },
        {
          _id: revieweradmin._id,
          isActive: active,
          _role_id: this.state.role_id,
          user_id: [
            {
              user_id: this.props.userId,
              name: {
                frist: this.state.firstName1,
                last: this.state.lastName1,
              },
            },
          ],
        },
      ],
    };

    this.props.roleassign(obj);
  };
  render() {
    const StaffModalData = {
      searchView: this.state.searchView,
      searchText: this.state.searchText,
      changeHandler: this.changeHandler.bind(this),
      handleSearch: this.handleSearch.bind(this),
      staffList: this.props.staffList,
      modalOpen: this.modalOpen,
      modalClose: this.modalClose,
      selectedId: this.state.selectedId,
    };

    const { leavedetials } = this.props;
    const list = leavedetials.toJS();
    var reviewer_forwarder_approver = list?.reviewer_forwarder_approver;

    const reviewerAcademic = reviewer_forwarder_approver
      ?.filter((data) => data.flow === 'reviewer')
      .filter((data) => data.staff_type === 'academic');

    const reviewerAdmim = reviewer_forwarder_approver
      ?.filter((data) => data.flow === 'reviewer')
      .filter((data) => data.staff_type === 'admin');

    return (
      <React.Fragment>
        <Accordion defaultActiveKey="">
          <Card className="rounded">
            <Accordion.Toggle
              as={Card.Header}
              eventKey="0"
              className="icon_remove_leave"
              onClick={this.iconPostion1}
            >
              <div className="d-flex justify-content-between">
                <p className="mb-0">Reviewers</p>

                <p className="mb-0">
                  {this.state.arrow1 === true ? (
                    <i className="fa fa-chevron-down f-14" aria-hidden="true"></i>
                  ) : (
                    <i className="fa fa-chevron-up fa-rotate-90 f-14" aria-hidden="true"></i>
                  )}
                </p>
              </div>
            </Accordion.Toggle>
            <Accordion.Collapse eventKey="0" className="bg-white ">
              <Card.Body className=" innerbodyLeave border-top-remove">
                <div className="container-fluid ">
                  <div className="row">
                    <div className="col-md-6 col-xl-5">
                      <div className="mb-3">
                        <div className="border border-radious-8 p-3">
                          <p className="f-16 mb-2 font-weight-bold">
                            {' '}
                            Academic only and Academic & Administrative Staff
                          </p>

                          <p className="f-14 text-gray">
                            {' '}
                            Select a reviewer for permission/leave/on-duty application
                          </p>
                          <div className="">
                            {reviewerAcademic &&
                              reviewerAcademic[0]?.roles.map((data) => {
                                return (
                                  <Form.Check
                                    type="radio"
                                    label={data.role}
                                    name="formHorizontalRadio"
                                    id="formHorizontalRadios1"
                                    className="mt-1 ml-2"
                                    onChange={(e) =>
                                      this.handleChange(data._id, data.role, 'academic')
                                    }
                                  />
                                );
                              })}
                          </div>

                          {this.state.enablebutton && (
                            <div className="pt-1 ml-3">
                              <div className="col-md-5">
                                <div className="d-flex justify-content-between">
                                  <p className="f-15 mb-2">{this.state.name} </p>

                                  {this.state.name && (
                                    <p className="f-15 mb-2">
                                      <i
                                        className="fa fa-trash mr-2"
                                        aria-hidden="true"
                                        onClick={() => this.removename()}
                                      ></i>
                                    </p>
                                  )}
                                </div>

                                <div className="pt-1 pb-2">
                                  <div className="sb-example-2">
                                    <div className="search">
                                      <input
                                        type="text"
                                        className="searchTerm"
                                        placeholder="Search"
                                        value={this.state.searchText}
                                        maxLength={10}
                                        onChange={(e) => this.handleChangeSearch(e)}
                                      />
                                      <button type="submit" className="searchButton">
                                        <i
                                          className="fa fa-search"
                                          onClick={(e) => this.modalOpen('left')}
                                        ></i>
                                      </button>
                                      {this.state.searchView && (
                                        <StaffModal StaffModalData={StaffModalData} />
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="col-md-6 col-xl-5">
                      <div className="mb-3">
                        <div className="border border-radious-8 p-3">
                          <div className="d-flex justify-content-between">
                            <p className="f-16 mb-2 font-weight-bold"> Administrative Staff Only</p>
                            <label>
                              <Switch
                                onChange={this.handletoggle}
                                // handleDiameter={30}
                                checked={this.state.isactive}
                                onColor="#4698e7"
                                onHandleColor="#fff"
                                uncheckedIcon={true}
                                checkedIcon={true}
                                className="react_switch_custome"
                                height={16}
                                width={36}
                                id="material-switch"
                              />
                            </label>
                          </div>

                          <p className="f-14 text-gray">
                            {' '}
                            Select a reviewer for permission/leave/on-duty application
                          </p>
                          <div className="">
                            {reviewerAdmim &&
                              reviewerAdmim[0]?.roles.map((data) => {
                                return (
                                  <Form.Check
                                    type="radio"
                                    label={data.role}
                                    name="formHorizontalRadios"
                                    id="formHorizontalRadios1"
                                    className="mt-1 ml-2"
                                    disabled={this.state.isactive ? false : true}
                                    onChange={(e) =>
                                      this.handleChange(data._id, data.role, 'admin')
                                    }
                                  />
                                );
                              })}
                          </div>

                          <div className="pt-1 ml-3">
                            <div className="col-md-5">
                              <div className="d-flex justify-content-between">
                                <p className="f-15 mb-2">{this.state.name1} </p>

                                {this.state.name1 && (
                                  <p className="f-15 mb-2">
                                    <i
                                      className="fa fa-trash mr-2"
                                      aria-hidden="true"
                                      onClick={() => this.removename1()}
                                    ></i>
                                  </p>
                                )}
                              </div>
                              <div className="pt-1 pb-2">
                                {this.state.enablebuttonadmin && (
                                  <div className="sb-example-2">
                                    <div className="search">
                                      <input
                                        type="text"
                                        className="searchTerm"
                                        placeholder="Search"
                                        value={this.state.searchText}
                                        maxLength={10}
                                        onChange={(e) => this.handleChangeSearch(e)}
                                      />
                                      <button type="submit" className="searchButton">
                                        {this.state.isactive ? (
                                          <i
                                            className="fa fa-search"
                                            onClick={() => this.modalOpen('right')}
                                          ></i>
                                        ) : (
                                          <i className="fa fa-search"></i>
                                        )}
                                      </button>
                                    </div>
                                    {this.state.searchView && (
                                      <StaffModal StaffModalData={StaffModalData} />
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-12">
                    <div className="float-right pb-2">
                      <b className="pr-3">
                        <Button variant="outline-secondary" onClick={() => this.handlecancel()}>
                          CANCEL
                        </Button>
                      </b>

                      <b className="">
                        <Button
                          variant={this.state.enable ? 'secondary' : 'primary'}
                          disabled={this.state.enable ? true : false}
                          onClick={() =>
                            this.handleSave(
                              reviewerAcademic[0],
                              reviewerAdmim[0],
                              this.state.active
                            )
                          }
                        >
                          SAVE
                        </Button>
                      </b>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Accordion.Collapse>
          </Card>
        </Accordion>
      </React.Fragment>
    );
  }
}

const mapStateToProps = function (state) {
  return {
    staffList: selectStaffList(state),
    leavedetials: selectLocations(state),
    userId: selectUserId(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Reviewer);
