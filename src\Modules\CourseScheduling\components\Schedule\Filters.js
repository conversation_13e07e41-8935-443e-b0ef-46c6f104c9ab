import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import { getFormattedGroupName } from '../utils';
import { capitalize, getURLParams, isIndGroup, studentGroupRename } from '../../../../utils';

function Filters({
  data,
  filter,
  handleFilterChange,
  triggerActiveType,
  supportSessionTypes,
  checkMissedToComplete,
}) {
  const programId = getURLParams('programId', true);

  const activeViewType = filter.get('view', '');
  function handleChange(value, name) {
    if (name === 'view') {
      triggerActiveType(value);
      return handleFilterChange(
        filter.merge(Map({ [name]: value, status: '', deliveryType: '', studentGroup: '' }))
      );
    }
    handleFilterChange(filter.set(name, value));
  }

  function getViews() {
    return ['regular', 'support', 'events'].map((view) => {
      return {
        name: `${capitalize(view)}${view === 'events' ? '' : ' Session'}`,
        value: view,
      };
    });
  }

  function getStatuses() {
    const statusList = ['pending', 'scheduled', 'completed', 'missed', 'canceled'];
    let missedStatus = [];
    if (checkMissedToComplete) {
      missedStatus = ['missed to completed'];
    }
    return [...statusList, ...missedStatus].map((status) => {
      return {
        name: capitalize(status),
        value: status,
      };
    });
  }

  function getDeliveryTypes() {
    if (activeViewType === 'regular') {
      const grouped = data.get('session_flow', List()).groupBy((s) => s.get('delivery_type'));
      const deliveryTypes = grouped.keySeq().toList().sort();
      return deliveryTypes.toJS();
    } else if (activeViewType === 'support') {
      return supportSessionTypes;
    } else if (activeViewType === 'events') {
      return ['general', 'exam', 'training'].map((type) => ({
        name: capitalize(type),
        value: type,
      }));
    }
    return [];
  }

  function getStudentGroups() {
    return data
      .get('session_flow', List())
      .reduce((acc, session) => {
        return acc.concat(
          session.get(`student_group${activeViewType !== 'regular' ? 's' : ''}`, List()).map((g) =>
            Map({
              name: getFormattedGroupName(
                studentGroupRename(g.get('group_name', ''), programId),
                isIndGroup(g.get('group_name', '')) ? 1 : 2
              ),
              value: g.get(activeViewType === 'regular' ? '_id' : 'group_id'),
            })
          )
        );
      }, List())
      .reduce((acc, group) => acc.set(group.get('value'), group), Map())
      .valueSeq()
      .toList()
      .sort((c1, c2) => {
        const v1 = c1.get('name', '');
        const v2 = c2.get('name', '');
        return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
      })
      .toJS();
  }

  function getDeliveryTypeTitle() {
    if (activeViewType === 'events') return t('event_type');
    if (activeViewType === 'support') return t('type');
    return t('Delivery_Type');
  }

  return (
    <div className="row">
      <div className="col-md-3">
        <div className="f-14 text-uppercase">
          <Trans i18nKey={'view'}></Trans>
        </div>
        <FormControl fullWidth variant="outlined" size="small">
          <Select
            native
            value={filter.get('view', '')}
            onChange={(e) => handleChange(e.target.value, 'view')}
          >
            {getViews().map((option) => (
              <option key={option.value} value={option.value}>
                {t(option.name)}
              </option>
            ))}
          </Select>
        </FormControl>
      </div>
      <div className="col-md-9">
        <div className="row align-items-end">
          <div className="col-md-4 pl-2 pr-1">
            <div className="f-14 text-uppercase">
              <Trans i18nKey={'status'}></Trans>
            </div>
            <FormControl fullWidth variant="outlined" size="small">
              <Select
                native
                value={filter.get('status', '')}
                onChange={(e) => handleChange(e.target.value, 'status')}
              >
                <option value="">{t('all')}</option>
                {getStatuses().map((option) => (
                  <option key={option.value} value={option.value}>
                    {t(option.name)}
                  </option>
                ))}
              </Select>
            </FormControl>
          </div>
          <div className="col-md-3 pl-2 pr-1">
            <div className="f-14 text-uppercase">{getDeliveryTypeTitle()}</div>
            <FormControl fullWidth variant="outlined" size="small">
              <Select
                native
                value={filter.get('deliveryType', '')}
                onChange={(e) => handleChange(e.target.value, 'deliveryType')}
              >
                <option value="">{t('all')}</option>
                {getDeliveryTypes().map((deliveryType) => {
                  const value = ['events', 'support'].includes(activeViewType)
                    ? deliveryType.value
                    : deliveryType;
                  return (
                    <option key={value} value={value}>
                      {activeViewType === 'regular' ? deliveryType : deliveryType.name}
                    </option>
                  );
                })}
              </Select>
            </FormControl>
          </div>
          <div className="col-md-4 col-xl-3 col-lg-3 col-4 pl-2 pr-1">
            <div className="f-14 text-uppercase">
              <Trans i18nKey={'student_group'}></Trans>
            </div>
            <FormControl fullWidth variant="outlined" size="small">
              <Select
                native
                value={filter.get('studentGroup', '')}
                onChange={(e) => handleChange(e.target.value, 'studentGroup')}
              >
                <option value="">{t('all')}</option>
                {getStudentGroups().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.name}
                  </option>
                ))}
              </Select>
            </FormControl>
          </div>
        </div>
      </div>
    </div>
  );
}

Filters.propTypes = {
  data: PropTypes.instanceOf(Map),
  filter: PropTypes.instanceOf(Map),
  handleFilterChange: PropTypes.func,
  supportSessionTypes: PropTypes.array,
  triggerActiveType: PropTypes.func,
  checkMissedToComplete: PropTypes.bool,
};

export default Filters;
