import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import Swal from 'sweetalert2';
import Webcam from 'webcam-easy';
import { Modal, Button } from 'react-bootstrap';

import { loadModels, getFullFaceDescription, isFaceDetectionModelLoaded } from '../../../api/face';
import axios from '../../../axios';
import Loader from '../../../Widgets/Loader/Loader';
import faceImage from '../../../Assets/facial-placeholder-front.png';
import FacialAuth from '../../../Assets/Facial_Auth.png';
import { Trans } from 'react-i18next';
import { allowSupportAction } from 'utils';

const tickColor = {
  background: 'grey',
  padding: '3px',
  position: 'absolute',
  right: '8px',
  top: '-8px',
  color: 'white',
  borderRadius: '15px',
};

const tickColorActive = {
  background: 'green',
  padding: '3px',
  position: 'absolute',
  right: '8px',
  top: '-8px',
  color: 'white',
  borderRadius: '15px',
};

function dataURLtoFile(dataurl, filename) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = window.atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

const INIT_STATE = {
  imageURL: null,
  fullDesc: null,
  error: null,
  loading: false,
};

class Webcams extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      webcame: [],
      value: '',
      userImg: [],
      video: '',
      isLoading: false,
      alert: '',
      data: null,
      ...INIT_STATE,
      isModelLoaded: !!isFaceDetectionModelLoaded(),
      firstImageDesc: '',
      imageType: 'UPLOAD',
      uploadImage: null,
      verifiedImage: false,
    };
    this.inputRef = React.createRef();
  }

  UNSAFE_componentWillMount() {
    this.resetState();
    this.mounting();
  }

  mounting = async () => {
    await loadModels();
  };

  resetState = () => {
    this.setState({ ...INIT_STATE });
  };

  componentDidMount() {
    this.getWebcamList();
    this.getWebCameraList();
    this.findVideoDevice();
    this.getWebCam();
  }

  gotStream = (stream) => {
    window.stream = stream;
    this.webcamElement.srcObject = stream;
    return navigator.mediaDevices.enumerateDevices();
  };

  findVideoDevice = () => {
    if (window.stream) {
      window.stream.getTracks().forEach((track) => {
        track.stop();
      });
    }
    const videoSource = this.videoSelect.value;
    const constraints = {
      video: { deviceId: videoSource ? { exact: videoSource } : undefined },
    };
    navigator.mediaDevices
      .getUserMedia(constraints)
      .then(this.gotStream)
      .then(this.gotDevices)
      .catch(this.handleError);
  };

  gotDevices = (deviceInfos) => {
    // Handles being called several times to update labels. Preserve values.
    const values = this.selectors.map((select) => select.value);
    this.selectors.forEach((select) => {
      while (select.firstChild) {
        select.removeChild(select.firstChild);
      }
    });
    for (let i = 0; i !== deviceInfos.length; ++i) {
      const deviceInfo = deviceInfos[i];
      const option = document.createElement('option');
      option.value = deviceInfo.deviceId;
      if (deviceInfo.kind === 'videoinput') {
        option.text = deviceInfo.label || `camera ${this.videoSelect.length + 1}`;
        this.videoSelect.appendChild(option);
      }
    }
    this.selectors.forEach((select, selectorIndex) => {
      if (
        Array.prototype.slice.call(select.childNodes).some((n) => n.value === values[selectorIndex])
      ) {
        select.value = values[selectorIndex];
      }
    });
  };

  handleError = (error) => {
    console.log('navigator.MediaDevices.getUserMedia error: ', error.message, error.name); //eslint-disable-line
  };

  getWebCameraList = async () => {
    this.videoSelect = document.querySelector('select#videoSource');
    this.selectors = [this.videoSelect];
    // const devices = await navigator.mediaDevices
    //   .enumerateDevices()
    //   .then(this.gotDevices)
    //   .catch(this.handleError);
  };

  click = () => {
    this.setState({
      isLoading: true,
    });
    let formdata = new FormData();

    let file1 = dataURLtoFile(this.state.userImg[0], '01.png');
    let file2 = dataURLtoFile(this.state.userImg[1], '02.png');
    let file3 = dataURLtoFile(this.state.userImg[2], '03.png');
    let file4 = dataURLtoFile(this.state.userImg[3], '04.png');

    formdata.append('center', file1);
    formdata.append('left', file2);
    formdata.append('right', file3);
    formdata.append('up', file4);
    formdata.append('_id', this.props.id);

    axios
      .post('/user/user_face_registration', formdata)
      .then((res) => {
        var data = res.data;

        this.setState({
          isLoading: false,
          data: data,
        });

        Swal.fire({
          icon: 'success',
          title: 'Registration Successful...',
          text: 'Biometric Registration successful!',
        }).then(() => {
          this.offCamera();
          this.props.close();
          //this.props.history.go();
        });
      })
      .catch((error) =>
        this.setState({
          isLoading: false,
        })
      );
  };

  playStream = async (type, deviceId) => {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const cameras =
      (await deviceId) === ''
        ? devices.filter((device) => device.kind === type)
        : devices.filter((device) => device.kind === type && device.deviceId === deviceId);
    if (cameras.length) {
      this.setState({ isLoading: true });

      const stream = await this.openCamera(cameras[0].deviceId, 1280, 720);
      this.video.srcObject = stream;
      this.setState({ isLoading: false });
    } else {
      alert('no camera device found!');
    }
    this.getWebcamList();
  };

  openCamera = async (cameraId, minWidth, minHeight) => {
    const constraints = {
      audio: false,
      video: {
        deviceId: cameraId,
        width: { min: minWidth },
        height: { min: minHeight },
      },
    };
    return await navigator.mediaDevices.getUserMedia(constraints);
  };

  change = (event) => {
    Promise.all([this.setState({ value: event.target.value })]).then(() => {
      this.playStream('videoinput', this.state.value);
    });
  };

  getWebcamList = async () => {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const cameras = await devices.filter((device) => device.kind === 'videoinput');
    this.setState({ webcame: cameras });
  };

  getWebCam = () => {
    this.webcamElement = document.getElementById('webcam');
    this.canvasElement = document.getElementById('canvas');
    this.webcam1 = new Webcam(this.webcamElement, 'user', this.canvasElement, null);
    this.setState({ isLoading: true });
    this.onCamera();
  };

  onCamera = () => {
    this.webcam1
      .start()
      .then((result) => {
        this.setState({ isLoading: false });
      })
      .catch((err) => {
        this.setState({ isLoading: false });
      });
  };

  retakePicture = () => {
    let temp = [...this.state.userImg];
    temp.pop();
    this.setState({
      userImg: temp,
    });
    if (temp.length === 0) {
      this.setState({
        firstImageDesc: '',
      });
    }
  };

  takePicture = async () => {
    const { isSkip } = this.props;
    let picture = this.webcam1.snap();
    if (isSkip) {
      let temp = [...this.state.userImg];
      temp.push(picture);
      this.setState({
        userImg: temp,
      });
    } else {
      let dataURLFile = dataURLtoFile(picture);
      this.resetState();
      await this.setState({
        imageURL: URL.createObjectURL(dataURLFile),
        loading: true,
      });
      this.handleImageCheck(picture);
    }
  };

  handleImageCheck = async (imageDataURL, image = this.state.imageURL) => {
    const { firstImageDesc } = this.state;
    await getFullFaceDescription(image, firstImageDesc).then(({ fullDesc, distance }) => {
      if (fullDesc && fullDesc.length === 1) {
        if (firstImageDesc === '') {
          this.setState({ firstImageDesc: fullDesc[0].descriptor });
        }
        if (distance <= 0.5) {
          let temp = [...this.state.userImg];
          temp.push(imageDataURL);
          this.setState({
            userImg: temp,
          });
        } else {
          Swal.fire({
            icon: 'error',
            title: '',
            text: 'Current picture does not match with previous picture. Please retake the picture',
          }).then(() => {});
        }
      } else if (fullDesc && fullDesc.length > 1) {
        Swal.fire({
          icon: 'error',
          title: '',
          text: 'Multiple faces detected. Please take only one face at that time.',
        }).then(() => {});
      } else {
        Swal.fire({
          icon: 'error',
          title: '',
          text: 'Could not detect face or does not have clarity. Please retake the picture.',
        }).then(() => {});
      }
      this.setState({ fullDesc, loading: false });
    });
  };

  offCamera = () => {
    if (window.stream) {
      window.stream.getTracks().forEach((track) => {
        track.stop();
      });
    }
    this.webcam1.stop();
  };

  handleUpload = (type) => {
    this.setState({ imageType: type }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.inputRef.current?.click();
    });
  };

  successMsg = () => {
    Swal.fire({
      icon: 'success',
      title: 'Verification Successful...',
      text: 'Biometric Image matched successful!',
    }).then(() => {});
    this.setState({
      isLoading: false,
      verifiedImage: true,
    });
  };

  errorMsg = () => {
    Swal.fire({
      icon: 'error',
      title: 'Verification Failed...',
      text: 'Biometric Image not matched !',
    }).then(() => {});
    this.setState({
      isLoading: false,
      verifiedImage: false,
    });
  };

  handleDisplayFileDetails = () => {
    if (this.inputRef.current?.files) {
      const selectedImage = this.inputRef.current?.files[0];
      if (selectedImage) {
        const size = Math.ceil(parseFloat(selectedImage.size / 1024));
        if (size > 5000) {
          Swal.fire({
            icon: 'error',
            title: 'Upload Error...',
            text: 'Image size should not be greater than 5 mb',
          }).then(() => {});
          return;
        }
        const reader = new FileReader();
        reader.onload = async (e) => {
          const imageFile = e.target.result;
          this.setState({
            isLoading: true,
          });
          let formData = new FormData();
          const { imageType } = this.state;
          const { id, employeeId } = this.props;
          if (imageType === 'UPLOAD') {
            let file1 = dataURLtoFile(imageFile, '01.png');
            let file2 = dataURLtoFile(imageFile, '02.png');
            let file3 = dataURLtoFile(imageFile, '03.png');
            let file4 = dataURLtoFile(imageFile, '04.png');

            formData.append('center', file1);
            formData.append('left', file2);
            formData.append('right', file3);
            formData.append('up', file4);
            formData.append('_id', id);

            axios
              .post('/user/user_face_registration', formData)
              .then((res) => {
                var data = res.data;
                this.setState({
                  isLoading: false,
                  data: data,
                  uploadImage: imageFile,
                });
                Swal.fire({
                  icon: 'success',
                  title: 'Registration Successful...',
                  text: 'Biometric Image Upload successful!',
                }).then(() => {});
              })
              .catch((error) =>
                this.setState({
                  isLoading: false,
                })
              );
          } else if (imageType === 'VERIFY') {
            const URL = `${process.env.REACT_APP_AUTH_URL}auth/facial-labeled-descriptors?employeeOrAcademicId=${employeeId}`;
            axios
              .get(`${URL}`)
              .then(async (res) => {
                // this.setState({
                //   isLoading: false,
                // });
                if (res?.data?.success) {
                  this.trainImage(imageFile, res?.data?.data?.descriptors[0]);
                  setTimeout(() => {
                    this.setState({
                      isLoading: false,
                    });
                  }, 2000);
                } else {
                  this.errorMsg();
                }
                // if (res?.data?.success) {
                //   Swal.fire({
                //     icon: 'success',
                //     title: 'Verification Successful...',
                //     text: 'Biometric Image matched successful!',
                //   }).then(() => {});
                // } else {
                //   Swal.fire({
                //     icon: 'error',
                //     title: 'Verification Failed...',
                //     text: 'Biometric Image not matched !',
                //   }).then(() => {});
                // }
              })
              .catch((error) => {
                this.setState({
                  isLoading: false,
                });
                this.errorMsg();
              });
          }
        };
        reader.readAsDataURL(selectedImage);
        this.inputRef.current.value = null;
      }
    }
  };

  trainImage = async (image, res) => {
    await getFullFaceDescription(image, res).then(({ fullDesc, distance }) => {
      if (distance <= 0.5) {
        this.successMsg();
      } else {
        this.errorMsg();
      }
    });
  };

  render() {
    const { userImg, uploadImage, verifiedImage } = this.state;
    const { handleFaceClose } = this.props;
    const allowSupport = allowSupportAction();
    return (
      <React.Fragment>
        <Loader isLoading={this.state.isLoading} />
        <Loader isLoading={this.state.loading} />
        <Modal
          show={true}
          onHide={() => {
            this.offCamera();
            handleFaceClose();
          }}
          dialogClassName="model-700"
          centered
        >
          <div className="container">
            <div className="d-flex justify-content-between">
              <div className="pt-3">
                <p className="f-20 mb-2 ">
                  <Trans i18nKey={'biometric.captureFace'}></Trans>
                </p>
              </div>
              <div className="pt-3">
                <b className="pr-2">
                  <Button
                    variant="outline-primary"
                    className="border-radious-8"
                    onClick={() => {
                      this.offCamera();
                      handleFaceClose();
                    }}
                  >
                    {' '}
                    <Trans i18nKey={'cancel_upper'}></Trans>
                  </Button>{' '}
                </b>
                {this.state.userImg.length !== 0 && (
                  <b>
                    <Button
                      variant="primary"
                      className="border-radious-8"
                      onClick={this.retakePicture}
                    >
                      {' '}
                      <i className="fa fa-retweet pr-2" aria-hidden="true"></i>
                      <Trans i18nKey={'biometric.retake'}></Trans>
                    </Button>{' '}
                  </b>
                )}
                <b>
                  <Button
                    variant="primary"
                    className="border-radious-8"
                    disabled={userImg?.length === 4}
                    onClick={this.takePicture}
                  >
                    {' '}
                    <i className="fa fa-camera pr-2" aria-hidden="true"></i>
                    <Trans i18nKey={'biometric.capture'}></Trans>
                  </Button>{' '}
                </b>

                {userImg?.length === 4 && (
                  <b>
                    <Button variant="primary" className="border-radious-8" onClick={this.click}>
                      {' '}
                      <i className="fa fa-save pr-2" aria-hidden="true"></i>
                      SAVE
                    </Button>{' '}
                  </b>
                )}
              </div>
            </div>
          </div>
          <Modal.Body>
            <div className="contanier">
              <div className="model-main">
                <div className="row pt-2">
                  <div className="col-md-12 text-center">
                    <div className="text-center bold f-20" title={''}>
                      <Trans i18nKey={'biometric.bio_capture_txt'}></Trans>
                    </div>
                    <div className="digi-img-capture position-relative">
                      <div className="transparent-image">
                        <img src={FacialAuth} alt="transparent" />
                      </div>
                      <video style={{ height: '480px' }} id="webcam" autoPlay playsInline></video>
                      <canvas id="canvas" className="d-none"></canvas>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div className="row justify-content-center pb-4">
                  <div className="col-md-6">
                    <div className="select">
                      <select
                        className="select-text"
                        id="videoSource"
                        onChange={this.findVideoDevice}
                      ></select>
                      <span className="select-highlights"></span>
                      <span className="select-bar"></span>
                      <label className="select-label floatinglabel"></label>
                    </div>
                  </div>
                </div>

                <div className="row ">
                  {[0, 1, 2, 3].map((item) => {
                    return (
                      <div className="col-md-3" key={item}>
                        <div className="bg-gray p-2">
                          <i
                            className={`fa fa-check`}
                            style={
                              userImg && userImg.length > 0 && userImg[item] !== undefined
                                ? tickColorActive
                                : tickColor
                            }
                            aria-hidden="true"
                          ></i>
                          <img
                            width="100%"
                            src={
                              userImg && userImg.length > 0 && userImg[item] !== undefined
                                ? userImg[0]
                                : faceImage
                            }
                            alt="#"
                            height="77px"
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            {allowSupport && (
              <>
                <hr />
                For Support
                <hr />
                <div className="pt-3">
                  <input
                    ref={this.inputRef}
                    accept="image/png, image/jpeg"
                    className="d-none"
                    type="file"
                    onChange={this.handleDisplayFileDetails}
                  />
                  <Button
                    component="label"
                    variant="outline-primary"
                    className="m-2"
                    onClick={() => this.handleUpload('UPLOAD')}
                  >
                    Upload Image
                  </Button>

                  <Button
                    component="label"
                    variant="outline-primary"
                    className="m-2"
                    onClick={() => this.handleUpload('VERIFY')}
                  >
                    Verify Image
                  </Button>
                </div>
                <hr />
                {uploadImage !== null && (
                  <div className="col-md-3">
                    <div className="bg-gray p-2">
                      <i
                        className={`fa fa-check`}
                        style={verifiedImage ? tickColorActive : tickColor}
                        aria-hidden="true"
                      ></i>
                      <img width="100%" src={uploadImage} alt="#" height="77px" />
                    </div>
                  </div>
                )}
              </>
            )}
          </Modal.Body>
        </Modal>
      </React.Fragment>
    );
  }
}

Webcams.propTypes = {
  id: PropTypes.string,
  close: PropTypes.func,
  handleFaceClose: PropTypes.func,
  isSkip: PropTypes.bool,
};

export default Webcams;
