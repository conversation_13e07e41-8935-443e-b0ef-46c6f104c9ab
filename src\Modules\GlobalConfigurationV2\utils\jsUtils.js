import { fromJS } from 'immutable';

/**
 * The function `formatToTwoDigitString` pads a given value with a leading zero if it is a single
 * digit.
 * @param value - The `value` parameter in the `formatToTwoDigitString` function is the input value
 * that you want to format into a two-digit string.
 * @returns The function `formatToTwoDigitString` returns a string value that is padded with a leading
 * zero if the input value is less than 10.
 */

export const publicPath = process.env.REACT_APP_BASE_PATH || '';

export const formatToTwoDigitString = (value) => {
  if (value === 0) return value;
  const paddedValue = String(value).padStart(2, '0');
  return value ? paddedValue : value;
};

export const getSections = (iframeRef) => {
  if (iframeRef.current) {
    const document = iframeRef.current.contentWindow.document;
    return Array.from(document.querySelectorAll('.table-of-contents td'))
      .filter((data) => data.className === 'p-4 pl-12')
      .map((data) => data.textContent);
  }
  return [];
};

export const fullMonthNames = fromJS([
  { fullName: 'January', abbreviation: 'Jan' },
  { fullName: 'February', abbreviation: 'Feb' },
  { fullName: 'March', abbreviation: 'Mar' },
  { fullName: 'April', abbreviation: 'Apr' },
  { fullName: 'May', abbreviation: 'May' },
  { fullName: 'June', abbreviation: 'Jun' },
  { fullName: 'July', abbreviation: 'Jul' },
  { fullName: 'August', abbreviation: 'Aug' },
  { fullName: 'September', abbreviation: 'Sep' },
  { fullName: 'October', abbreviation: 'Oct' },
  { fullName: 'November', abbreviation: 'Nov' },
  { fullName: 'December', abbreviation: 'Dec' },
]);

/*
@params    - Array
@usability - ArrayOfObject change to params structure return data 
@output    - number=0&value=user
*/
export const generateParams = (paramsArray = []) => {
  let params = '';
  paramsArray.forEach((element, index) => {
    const key = Object.keys(element)[0];
    const value = element[key];
    if (index > 0 && value) params += '&';
    if (value) params += `${key}=${value}`;
  });

  return params;
};

export const getChanges = (obj1, obj2) => {
  const changes = {};
  function compareValues(value1, value2) {
    if (
      typeof value1 === 'object' &&
      value1 !== null &&
      typeof value2 === 'object' &&
      value2 !== null
    ) {
      return getChanges(value1, value2);
    }
    return value1 !== value2;
  }

  const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
  allKeys.forEach((key) => {
    if (compareValues(obj1[key], obj2[key])) {
      changes[key] = obj2[key];
    }
  });
  return changes;
};

export const alphabetArray = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];
