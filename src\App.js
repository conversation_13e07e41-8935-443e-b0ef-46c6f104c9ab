import React, { Component, Suspense } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Redirect, withRouter } from 'react-router-dom';
import 'react-datepicker/dist/react-datepicker.css';
import { NotificationContainer } from 'react-notifications';

import './App.css';
import './Appv2.css';
import './spacing.css';
import './style.css';
import './Assets/slick.css';
import './Assets/slick-theme.css';

import 'react-notifications/lib/notifications.css';
import '@szhsin/react-menu/dist/index.css';

import * as actions from '../src/_reduxapi/actions/index';
import {
  selectUserId,
  selectIsInstitutionOnboarded,
  selectUserInfo,
} from './_reduxapi/Common/Selectors';
import { selectInstitution } from './_reduxapi/institution/selectors';
import { getInstitution } from './_reduxapi/institution/actions';
import {
  jsUcfirstAll,
  setDSCookie,
  getEnvAppEnvironment,
  getDSCookie,
  getLang,
  isModuleEnabled,
  getURLParams,
} from './utils';

import MainRoutes from './Routes/MainRoutes';
import withLandingHocHooks from 'Hooks/withLandingHocHooks';
import LocalStorageService from 'LocalStorageService';

const ArabicCss = React.lazy(() => import('./ArabicCss.js'));
class App extends Component {
  constructor(props) {
    super(props);
    this.state = {
      iframeCount: 0,
      iframeShow: false,
      iframeName: '',
      domainRedirection: getURLParams('redirect-domain', true),
    };
  }

  componentDidMount() {
    const { location, onAuthCheckState, onAuthCrossSiteCheckState } = this.props; //isAuthenticated,
    const { domainRedirection } = this.state;
    const { pathname, search } = location;
    const activeVersion = process.env.REACT_APP_ACTIVE_VERSION;
    const v2Enabled = isModuleEnabled('V2_ENABLE');
    if (v2Enabled) {
      const hasCookie = getDSCookie('version') !== '';
      if (!hasCookie && activeVersion === 'v2' && !['/v1', '/v2'].includes(pathname)) {
        setDSCookie('version', '/' + activeVersion);
      } else if (
        ['/v1', '/v2'].includes(pathname) &&
        getEnvAppEnvironment() !== 'production' &&
        !hasCookie
      ) {
        setDSCookie('version', pathname === '/v1' ? '/' : pathname);
      }
    }
    LocalStorageService.setCustomToken('redirectPath', pathname + search);
    if (domainRedirection === 'digi-class-admin') onAuthCrossSiteCheckState();
    else onAuthCheckState();
  }

  componentDidUpdate() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    var iframeShow = params.get('iframeShow');
    var iframeName = params.get('iframeName');
    if (this.state.iframeCount === 0 && iframeShow !== null) {
      this.setState({ iframeCount: 1, iframeShow: true, iframeName: iframeName });
    }
  }

  render() {
    const {
      // isAdmin,
      // isInstitutionOnBoarded,
      institution,
      // loggedInUserData,
      location,
      isAuthenticated,
      history,
    } = this.props;
    const { pathname, search } = location;
    const { iframeShow, iframeName } = this.state;
    let redirectUrl = null;
    if (!isAuthenticated) {
      if (
        pathname === '/signup' ||
        pathname === '/forgot-password' ||
        pathname === '/staffupload/' ||
        pathname === '/staffprofile' ||
        pathname === '/student/profile' ||
        pathname === '/staffupload' ||
        pathname === '/student/upload' ||
        pathname === '/user-verification/staff/profile' ||
        pathname === '/user-verification/staff/upload_document' ||
        pathname === '/user-verification/staff/biometric' ||
        pathname === '/user-verification/staff/completed' ||
        pathname === '/user-verification/staff/pending' ||
        pathname === '/user-verification/staff/invalid' ||
        pathname === '/user-verification/student/profile' ||
        pathname === '/user-verification/student/upload_document' ||
        pathname === '/user-verification/student/biometric' ||
        pathname === '/user-verification/student/completed' ||
        pathname === '/user-verification/student/pending' ||
        pathname === '/user-verification/student/invalid'
      ) {
        let path = pathname + search;
        redirectUrl = <Redirect to={path} />;
      } else {
        redirectUrl = <Redirect to="/login" />;
      }
    } else if (isAuthenticated && pathname === '/') {
      redirectUrl = <Redirect to="/overview" />;
    }
    let url = pathname;
    let navBarTitle = '';
    if (
      url.indexOf('program-calendar') > -1 ||
      url === 'calendar' ||
      url.indexOf('interim') > -1 ||
      url.indexOf('calender') > -1 ||
      url.indexOf('/course/') > -1 ||
      url.indexOf('/course-v1/') > -1
    ) {
      navBarTitle = jsUcfirstAll(this.props.navBarTitle);
    }
    //const showSidebar = isAdmin || isInstitutionOnBoarded;
    // if (!loggedInUserData.isEmpty() && getDSCookie('version') !== '/v2') {
    //   window.FreshworksWidget('disable', 'ticketForm', ['name', 'email']);
    //   window.FreshworksWidget('identify', 'ticketForm', {
    //     name: loggedInUserData.getIn(['name', 'first'], ''),
    //     email: loggedInUserData.get('email', ''),
    //   });
    //   window.FreshworksWidget('prefill', 'ticketForm', {
    //     priority: 4,
    //   });
    // }
    const publicPath = process.env.REACT_APP_BASE_PATH || '';
    return (
      <React.Fragment>
        <link rel="icon" href={`${publicPath}/favicon.png`} />
        <link rel="manifest" href={`${publicPath}/manifest.json`} />
        {redirectUrl}
        <MainRoutes
          isAuthenticated={isAuthenticated}
          iframeShow={iframeShow}
          iframeName={iframeName}
          institution={institution}
          navBarTitle={navBarTitle}
          history={history}
        />
        <NotificationContainer />
        {getLang() === 'ar' && (
          <Suspense fallback="">
            <ArabicCss />
          </Suspense>
        )}
      </React.Fragment>
    );
  }
}

App.propTypes = {
  history: PropTypes.object,
  isAdmin: PropTypes.bool,
  institution: PropTypes.instanceOf(Map),
  getInstitution: PropTypes.func,
  isInstitutionOnBoarded: PropTypes.bool,
  userId: PropTypes.string,
  isAuthenticated: PropTypes.bool,
  location: PropTypes.object,
  onAuthCheckState: PropTypes.func,
  loggedInUserData: PropTypes.instanceOf(Map),
  navBarTitle: PropTypes.string,
  onAuthCrossSiteCheckState: PropTypes.func,
};

const mapStateToProps = (state) => {
  let title = '';

  if (state.calender?.nav_bar_title !== null && state.calender?.nav_bar_title !== undefined) {
    title = state.calender?.nav_bar_title;
  } else if (
    state.interimCalendar?.nav_bar_title !== null &&
    state.interimCalendar?.nav_bar_title !== undefined
  ) {
    title = state.interimCalendar?.nav_bar_title;
  }
  return {
    isAuthenticated: state.auth.token !== null,
    navBarTitle: title,
    userId: selectUserId(state),
    isAdmin: state.auth.isAdmin,
    isInstitutionOnBoarded: selectIsInstitutionOnboarded(state),
    institution: selectInstitution(state),
    loggedInUserData: selectUserInfo(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    onAuthCheckState: () => dispatch(actions.authCheckState()),
    getInstitution: (userId) => dispatch(getInstitution(userId)),
    onAuthCrossSiteCheckState: () => dispatch(actions.authCrossSiteCheckState()),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(withLandingHocHooks(App)));
