import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Map } from 'immutable';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import DashboardList from './components/Home/Index';
//import DashboardDetail from './components/DashboardDetail';
import NewDashboardDetail from './components/NewDashboardDetail';
import { selectLoading, selectMessage } from '../../_reduxapi/dashboard/selectors';
import './css/dashboard.css';
import { t } from 'i18next';

class Dashboard extends Component {
  isLoading() {
    const { history } = this.props;
    const { location } = history;
    return (
      location.pathname !== '/dashboard' && this.props.loading.valueSeq().some((value) => value)
    );
  }

  render() {
    const { message } = this.props;
    const labelName = t('role_management.modules_list.Dashboard');
    const items = [{ to: '#', label: labelName }];
    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={this.isLoading()} />
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <Switch>
          <Route exact path="/dashboard/details" component={NewDashboardDetail} />
          <Route path="/dashboard" component={DashboardList}></Route>
        </Switch>
      </div>
    );
  }
}

Dashboard.propTypes = {
  message: PropTypes.string,
  history: PropTypes.object,
  loading: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  return {
    loading: selectLoading(state),
    message: selectMessage(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(Dashboard);
