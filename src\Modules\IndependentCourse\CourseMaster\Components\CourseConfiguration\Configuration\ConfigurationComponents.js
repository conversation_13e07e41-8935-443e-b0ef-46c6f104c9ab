import React, { useState, useEffect, useRef } from 'react';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import MaterialInput from 'Widgets/FormElements/material/Input';
import SelectAllSharpIcon from '@mui/icons-material/SelectAllSharp';
import { List, Map } from 'immutable';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Typography, AccordionSummary, AccordionDetails, Accordion } from '@mui/material';
import { useStylesFunction } from 'Modules/ProgramInput/v2/Utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';

import SharingDetailsIcon from 'Assets/img/v2/share.svg';
import {
  ShareCourse,
  CourseDuration,
} from 'Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/CourseConfiguration/Component/LevelConfiguration/LevelConfigComponents';
export function OnlyDuration({ durationData, setData }) {
  const { duration, setDuration } = durationData;
  const handleWeekDurationChange = (event) => {
    const value = event.target.value;
    if (value === '') return setDuration(value);
    let parsedValue = Number(value);
    if (isNaN(parsedValue))
      return setData(Map({ message: t('program_input.error_strings.durationNotValid') }));
    if (parsedValue > 52)
      return setData(Map({ message: `${t('program_input.error_strings.durationMax')} 52` }));
    setDuration(parsedValue);
  };
  return (
    <>
      <div className="d-flex mt-4 ">
        <div className="mr-2">
          <SelectAllSharpIcon />
        </div>
        <div className="">
          <p className="bold f-18 mb-1">
            <Trans>duration</Trans>
          </p>
        </div>
      </div>

      <div className="pt-2 pb-2 f-15">
        <MaterialInput
          elementType={'materialInput'}
          type={'text'}
          variant={'outlined'}
          size={'small'}
          label={<Trans i18nKey={'course_master.durationWeek'}></Trans>}
          value={duration}
          changed={(event) => {
            handleWeekDurationChange(event);
          }}
        />
      </div>
    </>
  );
}

OnlyDuration.propTypes = {
  durationData: PropTypes.object,
  setData: PropTypes.func,
};

export function SharingAndDuration({
  configLabels,
  fullCourseDetails,
  setData,
  duration,
  programCurriculum,
  sharingAndDurationRef,
}) {
  const [expanded, setExpanded] = useState('panel1');
  const courseDetailsRef = useRef();
  const courseDurationRef = useRef();
  const classes = useStylesFunction();
  const handleAccordion = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  useEffect(() => {
    sharingAndDurationRef.current = returnData;
  });
  const returnData = () => {
    const courseDetail = courseDetailsRef.current;
    const courseDetailRequest = courseDetail.map((item) => {
      let filteredProgramData = programCurriculum.filter(
        (pItem) => pItem.get('_id', '') === item.get('programId', '')
      );
      let filteredCurriculumData = filteredProgramData
        .getIn([0, 'curriculumData'], List())
        .filter((cItem) => cItem.get('_id', '') === item.get('curriculumId', ''));
      let filteredYearData = filteredCurriculumData
        .getIn([0, 'yearLevel'], List())
        .filter((yItem) => yItem.get('_id', '') === item.get('yearId', ''));
      let filteredLevelData = filteredYearData
        .getIn([0, 'levels'], List())
        .filter((lItem) => lItem.get('_id', '') === item.get('levelId', ''));
      return {
        _program_id: filteredProgramData.getIn(['0', '_id'], ''),
        programName: filteredProgramData.getIn(['0', 'name'], ''),
        _curriculum_id: filteredCurriculumData.getIn(['0', '_id'], ''),
        curriculumName: filteredCurriculumData.getIn(['0', 'curriculumName'], ''),
        _year_id: filteredYearData.getIn(['0', '_id'], ''),
        year: filteredYearData.getIn(['0', 'yType'], ''),
        _level_id: filteredLevelData.getIn(['0', '_id'], ''),
        levelNo: filteredLevelData.getIn(['0', 'levelName'], ''),
      };
    });
    let Data = {
      courseSharedWith: courseDetailRequest.toJS(),
      courseDuration: courseDurationRef.current,
    };
    return Data;
  };
  const getOptions = ({
    programId = '',
    curriculumId = '',
    yearId = '',
    labelKey,
    labelName = '',
  }) => {
    let options = programCurriculum;
    if (programId)
      options = options
        .filter((item) => item.get('_id') === programId)
        .getIn(['0', 'curriculumData'], List());

    if (curriculumId)
      options = options
        .filter((item) => item.get('_id') === curriculumId)
        .getIn(['0', 'yearLevel'], List());

    if (yearId)
      options = options.filter((item) => item.get('_id') === yearId).getIn(['0', 'levels'], List());

    const optionsList = options.map((item) => {
      const optionLabel = labelName
        ? `${getShortString(configLabels.get(labelName, ''), 4, false)} ${item.get(labelKey)}`
        : getShortString(item.get(labelKey, ''), 9, false);
      const optionName = labelName
        ? `${configLabels.get(labelName, '')} ${item.get(labelKey)}`
        : item.get(labelKey, '');
      return { name: optionLabel, value: item.get('_id'), fullName: optionName };
    });

    return optionsList;
  };
  return (
    <Accordion
      className={`${classes.boxShadowNone} ${classes.accordionHeight}`}
      expanded={expanded === 'panel1'}
      onChange={handleAccordion('panel1')}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1bh-content"
        id="panel1bh-header"
      >
        <Typography className="font-weight-bold">
          <img src={SharingDetailsIcon} alt="icon" className="mr-2 ml--20" />{' '}
          <Trans i18nKey={'duration_sharing'} />
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <div className="w-100">
          <CourseDuration
            classes={classes}
            configLabels={configLabels}
            fullCourseDuration={duration}
            setData={setData}
            courseType={fullCourseDetails.get('courseType', '')}
            courseAssignedDetails={fullCourseDetails.get('courseAssignedDetails', List())}
            startWeek={1}
            endWeek={52}
            courseDurationRef={courseDurationRef}
            _level_id={''}
          />
          {programCurriculum.size !== 0 && (
            <ShareCourse
              classes={classes}
              configLabels={configLabels}
              courseDetailsRef={courseDetailsRef}
              fullCourseDetails={fullCourseDetails}
              _level_id={''}
              setData={setData}
              getOptions={getOptions}
            />
          )}
        </div>
      </AccordionDetails>
    </Accordion>
  );
}

SharingAndDuration.propTypes = {
  configLabels: PropTypes.instanceOf(Map),
  sharingAndDurationRef: PropTypes.object,
  fullCourseDetails: PropTypes.instanceOf(Map),
  programCurriculum: PropTypes.instanceOf(List),
  setData: PropTypes.func,
  duration: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};
