import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import { List, Map, fromJS } from 'immutable';
import PropTypes from 'prop-types';

import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import MButton from 'Widgets/FormElements/material/Button';
import { FormControlLabel, Paper, Radio, RadioGroup, Switch } from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { Trans } from 'react-i18next';

//utils
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';

//redux
import {
  addTardis,
  getTardis,
  deleteTardis,
  setData,
  getRemarksVisibility,
  updateRemarksVisibility,
} from '_reduxapi/global_configuration/v1/actions';
import { selectIsLoading } from '_reduxapi/global_configuration/v1/selectors';
import { crucial_constants } from '../../../constants';

//----------------------------------UI Utils Start--------------------------------------------------
const inputStyle = {
  '& fieldset': { border: 'none' },
  '& .MuiOutlinedInput-input': { padding: '0px' },
  '& .MuiInputBase-input.Mui-disabled': {
    WebkitTextFillColor: 'black',
    background: 'white',
  },
};
const radioIconStyle = {
  '& .MuiSvgIcon-root': {
    fontSize: '18px',
  },
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const radioOptionList = fromJS([
  { value: 'true', i18nKey: 'Disciplinary_Remarks.optionWithComment', className: 'm-0' },
  {
    value: 'false',
    i18nKey: 'Disciplinary_Remarks.optionWithoutComment',
    className: 'm-0 px-2',
  },
]);
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------
const useRemarksVisibility = () => {
  const dispatch = useDispatch();
  const [remarksData, setRemarksData] = useState(Map());
  const studentVisibility = remarksData.get('studentVisibility', false);

  const fetchCallBack = useCallback((getData) => {
    setRemarksData(getData);
  }, []);

  const fetchRemarksVisibility = useCallback(() => {
    dispatch(getRemarksVisibility(fetchCallBack));
  }, [dispatch, fetchCallBack]);

  const handleOptionChange = useCallback(
    (event) => {
      const requestBody = Map({
        commentVisibility: event.target.value,
        studentVisibility,
      });
      dispatch(updateRemarksVisibility(requestBody, fetchRemarksVisibility));
    },
    [dispatch, studentVisibility, fetchRemarksVisibility]
  );

  const toggleChecked = useCallback(() => {
    const requestBody = Map({
      commentVisibility: !studentVisibility,
      studentVisibility: !studentVisibility,
    });
    dispatch(updateRemarksVisibility(requestBody, fetchRemarksVisibility));
  }, [dispatch, studentVisibility, fetchRemarksVisibility]);

  useEffect(() => {
    fetchRemarksVisibility();
  }, [fetchRemarksVisibility]);

  return {
    remarksData,
    toggleChecked,
    handleOptionChange,
  };
};
//----------------------------------custom hooks end------------------------------------------------
//----------------------------------componentStart--------------------------------------------------
const RadioControl = (props) => (
  <Radio {...props} disableRipple size="small" sx={radioIconStyle} className="pl-0 py-0" />
);
const RadioOption = ({ value, i18nKey, className }) => (
  <FormControlLabel
    value={value}
    label={<Trans i18nKey={i18nKey} />}
    className={className}
    control={<RadioControl />}
  />
);
RadioOption.propTypes = {
  value: PropTypes.string.isRequired,
  i18nKey: PropTypes.string.isRequired,
  className: PropTypes.string,
};

const CommentRadio = ({ selectedOption, onChange }) => {
  return (
    <RadioGroup row value={selectedOption} onChange={onChange} className="mt-2">
      {radioOptionList.map((radioOption, optionIndex) => {
        const value = radioOption.get('value', '');
        const i18nKey = radioOption.get('i18nKey', '');
        const className = radioOption.get('className', '');
        return (
          <RadioOption key={optionIndex} value={value} i18nKey={i18nKey} className={className} />
        );
      })}
    </RadioGroup>
  );
};
CommentRadio.propTypes = {
  selectedOption: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

const VisibilitySwitch = ({ isVisible, toggleVisibility }) => {
  return (
    <div className="d-flex align-items-center">
      <Switch checked={isVisible} onChange={toggleVisibility} />
      <div>
        {isVisible ? (
          <Trans i18nKey="Disciplinary_Remarks.switchOn" />
        ) : (
          <Trans i18nKey="Disciplinary_Remarks.switchOff" />
        )}
      </div>
    </div>
  );
};

const RemarksLabel = () => {
  const { remarksData, toggleChecked, handleOptionChange } = useRemarksVisibility();
  const studentVisibility = remarksData.get('studentVisibility', false);
  const commentVisibility = remarksData.get('commentVisibility', false);

  return (
    <div className="row m-0 justify-content-center mt-5">
      <div className="col-9 border rounded p-3">
        <div className="d-flex align-items-center">
          <div className="flex-grow-1">
            <div className="bold mb-1">
              <Trans i18nKey={'Disciplinary_Remarks.remarkLabel'} />
            </div>
            <p className="m-0 f-12 digi-light-gray_1">
              <Trans i18nKey={'Disciplinary_Remarks.description'} />
            </p>
          </div>
          <VisibilitySwitch isVisible={studentVisibility} toggleVisibility={toggleChecked} />
        </div>
        {studentVisibility && (
          <CommentRadio selectedOption={commentVisibility} onChange={handleOptionChange} />
        )}
      </div>
    </div>
  );
};
//----------------------------------componentEnd----------------------------------------------------

const TardisEnabled = () => {
  const dispatch = useDispatch();
  const { globalConfigurationV1 } = useSelector((state) => state);
  const history = useHistory();
  const tardis = globalConfigurationV1.get('tardis', List());
  const { tardis: tardisLabel } = crucial_constants;
  const isLoading = useSelector(selectIsLoading);
  const [allData, setAllData] = useState(List());
  const [status, setStatus] = useState('');
  const [storeInput, setStoreInput] = useState('');
  const [shortCode, setShortCode] = useState('');
  const [deleteObj, setDeleteObj] = useState(Map());

  useEffect(() => {
    dispatch(getTardis());
  }, []); //eslint-disable-line

  useEffect(() => {
    setAllData(tardis.map((item) => item.set('storeValue', '')));
  }, [tardis]); //eslint-disable-line
  const handleChange = (e, i, key) => {
    setAllData(allData.setIn([i, key], e.target.value));
  };

  const [show, setShow] = useState(false);
  const handleShow = (i) => {
    setShow(true);
  };
  const handleModalClose = () => {
    setShow(false);
  };

  const validation = (type, inputValue, inputShortCode, index = -1) => {
    let error = '';
    const regexImplement = (testBy, wantToTest, option = 'i') => {
      return new RegExp(`^${testBy}$`, option).test(wantToTest);
    };

    const name = (type === 'edit' ? inputValue : storeInput).trim();
    const short_code = (type === 'edit' ? inputShortCode : shortCode).trim();

    if (name === '' || short_code === '') {
      error = 'Name or Short code should not be empty';
    } else if (
      allData
        .filter((_, i) => i !== index)
        .some(
          (item) =>
            regexImplement(name, item.get('name', '')) ||
            regexImplement(short_code, item.get('short_code', ''))
        )
    ) {
      error = 'Name or Short code Already Exist';
    } else if (
      index !== -1 &&
      regexImplement(name, tardis.getIn([index, 'name'], ''), 'g') &&
      regexImplement(short_code, tardis.getIn([index, 'short_code'], ''), 'g')
    ) {
      error = 'You should change something for Update in tardle List';
    }
    if (error !== '') {
      dispatch(setData({ message: error }));
      return false;
    }

    return true;
  };

  const onSubmit = () => {
    if (validation()) {
      dispatch(
        addTardis(
          {
            _id: null,
            name: storeInput.trim(),
            short_code: shortCode.trim(),
          },
          () => {
            setStatus('');
            setStoreInput('');
            setShortCode('');
          }
        )
      );
    }
  };

  const handleEdit = (i) => {
    const inputValue = allData.getIn([i, 'storeValue']);
    const inputShortCode = allData.getIn([i, 'short_code']);
    if (validation('edit', inputValue, inputShortCode, i)) {
      dispatch(
        addTardis(
          {
            _id: allData.getIn([i, '_id'], ''),
            name: inputValue.trim(),
            short_code: inputShortCode.trim(),
          },
          () => {
            setStatus('');
            setShortCode('');
          }
        )
      );
    }
  };

  const handleDelete = () => {
    dispatch(deleteTardis(deleteObj.get('_id', '')));
    setShow(false);
  };

  return (
    <div className="container">
      <div className="d-flex align-items-center border-bottom pt-3 pb-1">
        <HomeIcon fontSize="small" className="digi-gray-neutral" />
        <span
          className="ml-2 mr-2 remove_hover"
          onClick={() => history.push('/globalConfiguration-v1/institution')}
        >
          All Modules
        </span>
        <KeyboardArrowRightIcon fontSize="" />
        <span className="ml-2 text-skyblue">Disciplinary Remarks</span>
      </div>
      <div className="row justify-content-center">
        <div className="col-9">
          <div className="d-flex  pt-3">
            <div className="w-100 mr-4">
              <div
                onClick={() => {
                  setStatus('');
                }}
                className="row"
              >
                <div className="col-8">
                  <MaterialInput
                    elementType={'materialInput'}
                    fullWidth
                    value={storeInput}
                    changed={(e) => setStoreInput(e.target.value)}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'Enter Name'}
                  />
                </div>
                <div className="col-4">
                  <MaterialInput
                    elementType={'materialInput'}
                    fullWidth
                    value={shortCode}
                    changed={(e) => setShortCode(e.target.value)}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'Enter Short Code'}
                  />
                </div>
              </div>
            </div>
            <div>
              <MButton className="ml-auto" variant="contained" color="primary" clicked={onSubmit}>
                <div className="pt-1">Save</div>
              </MButton>
            </div>
          </div>
          <div className="mt-4">
            <Paper>
              <div className="scroll-table-tardis">
                <table className="table">
                  <thead className="table-tardis-sticky">
                    <tr>
                      <th>S.No</th>
                      <th>Name</th>
                      <th className="width_150">Short Code</th>
                      <th className="text-center">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {allData.size !== 0 ? (
                      <>
                        {allData.map((CopyData, i) => {
                          return (
                            <tr key={i}>
                              <td>{i + 1}</td>
                              <td>
                                <div>
                                  <MaterialInput
                                    elementType={'materialInput'}
                                    fullWidth
                                    type={'text'}
                                    variant={'outlined'}
                                    size={'small'}
                                    disabled={status !== i}
                                    placeholder={'Enter Name '}
                                    value={
                                      status !== i
                                        ? CopyData.get('name', '')
                                        : CopyData.get('storeValue', '')
                                    }
                                    changed={(e) => handleChange(e, i, 'storeValue')}
                                    sx={status !== i ? inputStyle : {}}
                                  />
                                </div>
                              </td>
                              <td>
                                <div>
                                  <MaterialInput
                                    elementType={'materialInput'}
                                    type={'text'}
                                    variant={'outlined'}
                                    size={'small'}
                                    disabled={status !== i}
                                    placeholder={'Enter Short Code '}
                                    value={CopyData.get('short_code', '')}
                                    changed={(e) => handleChange(e, i, 'short_code')}
                                    sx={status !== i ? inputStyle : {}}
                                  />
                                </div>
                              </td>
                              <td>
                                <div className="d-flex justify-content-center">
                                  {status !== i ? (
                                    <>
                                      <div
                                        className="border rounded d-flex p-1 mr-4 cursor-pointer"
                                        onClick={() => {
                                          setAllData(
                                            allData.setIn(
                                              [i, 'storeValue'],
                                              allData.getIn([i, 'name'], '')
                                            )
                                          );
                                          setStatus(i);
                                        }}
                                      >
                                        <ModeEditIcon color="primary" />
                                      </div>
                                      <div className="border rounded d-flex p-1 cursor-pointer">
                                        <DeleteIcon
                                          sx={{ color: 'red' }}
                                          onClick={(e) => {
                                            setDeleteObj(CopyData);
                                            handleShow(i);
                                          }}
                                        />
                                      </div>
                                    </>
                                  ) : (
                                    <>
                                      <div className="border rounded d-flex p-1 mr-4 cursor-pointer">
                                        <CheckIcon
                                          color="success"
                                          onClick={() => {
                                            handleEdit(i);
                                          }}
                                        />
                                      </div>
                                      <div className="border rounded d-flex p-1 cursor-pointer">
                                        <CloseIcon
                                          sx={{ color: 'red' }}
                                          onClick={() => {
                                            setStatus('');
                                            setAllData(tardis);
                                          }}
                                        />
                                      </div>
                                    </>
                                  )}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </>
                    ) : (
                      <tr>
                        <td colSpan={4}>
                          <div className="h6 bold mb-0 text-center mr-5">
                            {isLoading ? 'Loading...' : 'No Data Found'}
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </Paper>
            <div>
              {show && (
                <MaterialDialog
                  show={show}
                  onClose={handleModalClose}
                  maxWidth={'xs'}
                  fullWidth={true}
                >
                  <div className="px-4 pt-3 pb-4">
                    <div className="d-flex align-items-center">
                      <div className="mt-1">
                        <ErrorOutlineIcon sx={{ color: 'red', fontSize: 30 }} />
                      </div>
                      <div className="pl-3 h5 mb-0 bold">Delete {tardisLabel}</div>
                    </div>
                    <div className="mt-2 text-center">
                      Are you sure you want to delete the {tardisLabel}
                    </div>
                    <div className="d-flex mt-4">
                      <div className="d-flex justify-content-end ml-auto">
                        <MButton
                          variant="text"
                          className="mr-2 border px-3 bold text-dark"
                          sx={{ textTransform: 'none' }}
                          size={'small'}
                          clicked={() => setShow(false)}
                        >
                          Cancel
                        </MButton>
                        <MButton
                          variant="contained"
                          className="ml-1"
                          sx={{ textTransform: 'none' }}
                          size={'small'}
                          clicked={() => handleDelete()}
                        >
                          Confirm
                        </MButton>
                      </div>
                    </div>
                  </div>
                </MaterialDialog>
              )}
            </div>
          </div>
        </div>
      </div>
      <RemarksLabel />
    </div>
  );
};

TardisEnabled.propTypes = {
  setData: PropTypes.func,
};

export default TardisEnabled;
