import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { NavButton } from '../../Styled';
import {
  interimChangeYear,
  interimChangeSemester,
} from '../../../../_reduxapi/actions/interimCalendar';
import { t } from 'i18next';

const NavRowButtons = (props) => {
  const {
    active,
    theme,
    interimChangeYear,
    color,
    institutionCalendarId,
    curriculumYear,
    programId,
    interimChangeSemester,
  } = props;
  const history = useHistory();

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlProgramName = params.get('pname');

  return (
    <Fragment>
      {curriculumYear &&
        curriculumYear.length > 0 &&
        curriculumYear.map((year, index) => {
          return (
            <NavButton
              key={index}
              className={active === 'year' + year ? `${theme}` : null}
              onClick={() => {
                interimChangeYear('year' + year);
                interimChangeSemester('semester1');
                history.push(
                  `?year=year${year}&icd=${institutionCalendarId}&programid=${programId}&pname=${urlProgramName}`
                );
              }}
              color={color}
            >
              {t('year')} {year}
            </NavButton>
          );
        })}
    </Fragment>
  );
};

NavRowButtons.propTypes = {
  active: PropTypes.bool,
  theme: PropTypes.string,
  color: PropTypes.string,
  programId: PropTypes.string,
  curriculumYear: PropTypes.array,
  institutionCalendarId: PropTypes.string,
  interimChangeYear: PropTypes.func,
  interimChangeSemester: PropTypes.func,
};

const mapStateToProps = ({ interimCalendar, calender }) => ({
  active: interimCalendar.active_year,
  institutionCalendarId: interimCalendar.institution_Calender_Id,
  curriculumYear: interimCalendar.curriculum_years,
  programId: calender.programId,
});

export default connect(mapStateToProps, { interimChangeYear, interimChangeSemester })(
  NavRowButtons
);
