import React from 'react';
import { useHistory } from 'react-router-dom';
import SettingsIcon from '@mui/icons-material/Settings';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { fromJS } from 'immutable';
import { isModuleEnabled, isTagMasterEnabled } from 'utils';
import BasicDetail from '../../../Assets/basicDetail.svg';
import { CheckPermission } from 'Modules/Shared/Permissions';
import attendance_config from 'Assets/attendance_config.svg';
import attendance_excluded from 'Assets/attendance_excluded.svg';
import leader_board from 'Assets/user_activity_board/leader_board.svg';
import anomaly_board from 'Assets/user_activity_board/anomaly_icon.svg';
import TagIcon from 'Assets/TagIcon.svg';
import q360 from 'Assets/q360.svg';
import { useTranslation } from 'react-i18next';
import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';

//----------------------------------UI Utils Start--------------------------------------------------
const settingsIconSX = { fontSize: '55px', color: '#6b7280' };
const iconComponents = {
  settings: <SettingsIcon sx={settingsIconSX} />,
  report: <ReportProblemOutlinedIcon sx={settingsIconSX} />,
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const checkConditions = (configuration) => {
  const conditions = configuration.get('conditions', () => true);
  return conditions();
};
const getConfigurationItems = (Trans) => {
  const isTagMaster = isTagMasterEnabled();

  return fromJS([
    {
      header: Trans('global_configuration.configuration_info.headerBasicDetail'),
      imageSrc: BasicDetail,
      imageAlt: 'BasicDetail',
      description: Trans('global_configuration.configuration_info.descriptionBasicDetail'),
      path: '/globalConfiguration-v1/basicDetail',
      conditions: () =>
        CheckPermission('tabs', 'Global Configuration', 'Institution', '', 'Basic Details', 'View'),
    },
    {
      header: Trans('global_configuration.configuration_info.headerDisciplinaryRemarks'),
      description: Trans('global_configuration.configuration_info.descriptionDisciplinaryRemarks'),
      icon: 'report',
      path: '/globalConfiguration-v1/disciplinary_remarks',
      conditions: () => isModuleEnabled('TARDIS_MODULE'),
    },
    {
      header: Trans('global_configuration.configuration_info.headerAttendanceConfig'),
      imageSrc: attendance_config,
      imageAlt: 'attendance_config',
      description: Trans('global_configuration.configuration_info.descriptionAttendanceConfig'),
      path: '/globalConfiguration-v1/attendance_configuration',
      conditions: () => isModuleEnabled('LATE_ABSENT_CONFIGURATION'),
    },
    {
      header: Trans('global_configuration.configuration_info.headerSessionStatusManager'),
      imageSrc: attendance_excluded,
      imageAlt: 'attendance_excluded',
      description: Trans('global_configuration.configuration_info.descriptionSessionStatusManager'),
      path: '/globalConfiguration-v1/session_status_adjustment',
      conditions: () => isModuleEnabled('TARDIS_MODULE'),
    },
    {
      header: Trans('global_configuration.configuration_info.headerLeaderBoard'),
      imageSrc: leader_board,
      imageAlt: 'leader_board',
      description: Trans('global_configuration.configuration_info.descriptionLeaderBoard'),
      path: '/globalConfiguration-v1/leader_board',
      conditions: () => process.env.REACT_APP_CLIENT_NAME === 'sbmch-staging',
    },
    {
      header: Trans('global_configuration.configuration_info.headerAnomalyBoard'),
      imageSrc: anomaly_board,
      imageAlt: 'anomaly_board',
      description: Trans('global_configuration.configuration_info.descriptionAnomalyBoard'),
      path: '/globalConfiguration-v1/anomaly_board',
      conditions: () => process.env.REACT_APP_CLIENT_NAME === 'sbmch-staging',
    },
    {
      header: Trans('global_configuration.configuration_info.headerSettings'),
      icon: 'settings',
      description: Trans('global_configuration.configuration_info.descriptionSettings'),
      path: '/globalConfiguration-v1/settings',
      conditions: () =>
        CheckPermission(
          'tabs',
          'Global Configuration',
          'Institution',
          '',
          'Schedule Setting',
          'View'
        ),
    },
    {
      header: Trans('global_configuration.configuration_info.headerSurvey'),
      imageSrc: TagIcon,
      imageAlt: 'anomaly_board',
      description: Trans('global_configuration.configuration_info.descriptionSurvey'),
      path: '/globalConfiguration-v1/tagMasters',
      conditions: () => !isTagMaster,
    },
    {
      header: 'Q360 Configuration',
      imageSrc: q360,
      imageAlt: 'q360',
      description: 'Academic Quality Assurance Life Cycle process configuration',
      path: '/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration',
      conditions: () => isModuleEnabled('Q360'),
    },
  ]);
};
//----------------------------------JS Utils End----------------------------------------------------

const GlobalConfiguration = () => {
  const history = useHistory();
  const { t } = useTranslation();

  const configurationItems = getConfigurationItems(t);

  return (
    <React.Fragment>
      {configurationItems.map((configuration, configurationIndex) => {
        if (checkConditions(configuration)) {
          const header = configuration.get('header', '');
          const imageSrc = configuration.get('imageSrc', '');
          const imageAlt = configuration.get('imageAlt', '');
          const iconKey = configuration.get('icon', 'settings');
          const renderIcon = iconComponents[iconKey];
          const path = configuration.get('path', '');
          const description = configuration.get('description', '');
          const handleClick = () => {
            history.push(path);
          };
          return (
            <div
              className="universityCard_sub innerUniverCard cursor-pointer"
              onClick={handleClick}
              key={configurationIndex}
            >
              <div className="d-flex align-items-center">
                {imageSrc ? <img src={imageSrc} alt={imageAlt} /> : renderIcon}
                <div className="ml-3">
                  <p className="mb-1 bold">{header}</p>
                  {description && <p className="mb-0 digi-gray-neutral">{description}</p>}
                </div>
                <ArrowForwardIosIcon className="ml-auto remove_hover" />
              </div>
            </div>
          );
        }
        return null;
      })}
    </React.Fragment>
  );
};

export default GlobalConfiguration;
