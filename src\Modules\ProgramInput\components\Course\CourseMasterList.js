import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import { Checkbox, ListItemIcon, ListItemText, Menu, MenuItem, Select } from '@mui/material';
import { ThemeProvider } from '@mui/styles';
import DropdownMenu from 'Widgets/FormElements/material/DropdownMenu';
import ImportFromCourseFile from './ImportCourseFile';
import Loader from '../../../../Widgets/Loader/Loader';
import { FILTER_MENU_PROPS, CHECKBOX_THEME } from './utils';
import CourseMasterListTable from './CourseMasterListTable';
import AlertConfirmModal from '../../modal/AlertConfirmModal';
import Input from '../../../../Widgets/FormElements/Input/Input';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  selectCourseListById,
  selectIsCourseMasterListLoading,
} from '../../../../_reduxapi/program_input/selectors';
import {
  getURLParams,
  removeURLParams,
  jsUcfirstAll,
  jsUcfirst,
  eString,
  levelRename,
} from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import '../../css/program.css';
import { Trans, withTranslation } from 'react-i18next';
import { t } from 'i18next';
import { getLang, indVerRename } from '../../../../utils';
import AddVersionModal from 'Modules/ProgramInput/modal/AddVersionModal';

const ITEM_HEIGHT = 48;
const lang = getLang();
const initialFilters = {
  courseName: '',
  courseType: '',
  courseStatus: '',
  assignedIn: [],
};

class CourseMasterListComponent extends Component {
  constructor(props) {
    super(props);
    const { t } = this.props;
    this.state = {
      activeTab: getURLParams('courseActiveTab') === '1' ? 1 : 0,
      curriculumId: getURLParams('_curriculum_id', true),
      programId: getURLParams('_id', true),
      curriculumName: getURLParams('_curriculum_name', true),
      modalData: {
        show: false,
      },
      importshow: false,
      filters: initialFilters,

      options: [
        { name: 'Srigar', id: 1 },
        { name: 'Sam', id: 2 },
      ],
      TABS: [t('constant.added'), t('constant.drafts')],
      COURSE_TYPES: [
        {
          name: jsUcfirst(indVerRename('standard', this.programId)),
          value: 'standard',
        },
        {
          name: jsUcfirst(indVerRename('selective', this.programId)),
          value: 'selective',
        },
      ],
      COURSE_STATUS: [
        { name: t('constant.assigned'), value: 'assigned' },
        { name: t('constant.not_assigned'), value: 'not_assigned' },
      ],
      showCourseVersion: false,
      courseName: '',
      courseId: '',
      versioned: false,
      versionedFrom: '',
      anchorInfo: { element: null },
    };
    this.style = {
      searchBox: {
        border: 'none',
        'border-bottom': '1px solid blue',
        'border-radius': '0px',
      },
    };
    this.unlistenHistory = props.history.listen(({ action, location }) => {
      this.setState({
        curriculumId: getURLParams('_curriculum_id', true),
        curriculumName: getURLParams('_curriculum_name', true),
        filters: initialFilters,
      });
    });
  }

  componentDidMount() {
    const programId = getURLParams('_id', true);
    const { curriculumId } = this.state;
    this.props.getCourseListByCurriculumId(programId, curriculumId);
    //this.props.getCourseList();
  }

  componentDidUpdate(prevProps, prevState) {
    const { curriculumId } = this.state;
    if (prevState.curriculumId !== curriculumId) {
      const programId = getURLParams('_id', true);
      this.props.getCourseListByCurriculumId(programId, curriculumId);
    }
  }
  onImportchange(status) {
    this.setState({ importshow: status });
  }
  componentWillUnmount() {
    this.unlistenHistory();
  }

  handleTabChange = (index) => {
    this.setState({ activeTab: index });
    this.props.history.replace(
      `/program-input/configuration/course-masterlist/${removeURLParams(this.props.location, [
        'courseActiveTab',
      ])}`
    );
  };

  getActiveInactiveCount() {
    const active = this.getCourseListWithoutActive(this.props.courseListById).filter(
      (value) => value.get('isActive', true) === true
    ).size;
    const inactive = this.getCourseListWithoutActive(this.props.courseListById).size - active;

    return { active, inactive };
  }

  getCourseListWithoutActive(courseList = List()) {
    const {
      filters: { courseName, courseType, courseStatus, assignedIn },
    } = this.state;
    return courseList
      .filter((c) => {
        if (!courseName && !courseType && !courseStatus && !assignedIn.length) {
          return true;
        }
        let incCourseType = true;
        let incCourseName = true;
        let incCourseStatus = true;
        let incAssignedIn = true;
        if (courseType) {
          incCourseType = c.get('course_type') === courseType;
        }
        if (courseName) {
          incCourseName =
            c.get('course_name', '').toLowerCase().includes(courseName.toLowerCase()) ||
            c.get('course_code', '').toLowerCase().includes(courseName.toLowerCase());
        }
        if (courseStatus) {
          incCourseStatus =
            courseStatus === 'assigned'
              ? !c.get('course_assigned_details', List()).isEmpty()
              : c.get('course_assigned_details', List()).isEmpty();
        }
        if (assignedIn.length) {
          if (c.get('course_assigned_details', List()).isEmpty()) {
            incAssignedIn = false;
          } else {
            const assignedLevelIds = c
              .get('course_assigned_details')
              .map((ca) => ca.get('level_no'));
            const presenceStatus = assignedIn.reduce((acc, asId) => {
              return acc.push(assignedLevelIds.includes(asId));
            }, List());
            incAssignedIn = presenceStatus.some((v) => v === true);
          }
        }
        return incCourseType && incCourseName && incCourseStatus && incAssignedIn;
      })
      .toList();
  }

  getCourseList(courseList = List(), curriculumId, isActive) {
    return this.getCourseListWithoutActive(courseList, curriculumId, isActive)
      .filter((c) => {
        if (c.get('isActive', false) === isActive) return true;

        return false;
      })
      .toList();
  }

  handleAddCourse = (withDetails = true) => {
    const {
      location: { search },
    } = this.props;
    const { curriculumId } = this.state;
    const courseType = withDetails ? 'course' : 'course-without-details';
    this.props.history.push(`/program-input/curriculum/${curriculumId}/${courseType}/new${search}`);
  };

  handleCourseVersion = (item) => {
    this.setState({
      showCourseVersion: true,
      courseName: item.get('course_code', '') + ' - ' + item.get('course_name', ''),
      courseId: item.get('_id', ''),
      versioned: item.get('versioned', false),
      versionedFrom: item.get('versionedFrom', ''),
    });
  };

  closeCourseVersion = () => {
    this.setState({
      showCourseVersion: false,
    });
  };

  handleSave = (versionName) => {
    const { courseId, curriculumId, versioned, versionedFrom } = this.state;
    const params = {
      versionName: versionName,
      versionedFrom: versioned ? versionedFrom : courseId,
    };

    const {
      location: { search },
    } = this.props;

    const callBack = () => {
      this.props.history.push(
        `/program-input/curriculum/${curriculumId}/course/${
          versioned ? versionedFrom : courseId
        }${search}&versionName=${eString(versionName)}&versionType=${eString('create')}`
      );
    };
    this.props.checkDuplicateVersion({ params, callBack });
  };

  handleEditCourse = (course, withDetails = true) => {
    const {
      location: { search },
      history,
    } = this.props;

    const curriculumId = course.get('_curriculum_id', '');
    const courseId = course.get('_id', '');
    const versionName = course.get('versionName', '');
    const courseType = withDetails ? 'course' : 'course-without-details';
    history.push(
      `/program-input/curriculum/${curriculumId}/${courseType}/${courseId}${search}${
        versionName && versionName !== 'default'
          ? `&versionName=${eString(versionName)}&versionType=${eString('update')}`
          : ''
      }`
    );
  };

  handleDeleteCourse = (course, isConfirmed) => {
    const programId = getURLParams('_id', true);
    const { curriculumId } = this.state;
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: t('program_input.modals.delete_course_modal.title'),
        body: (
          <div>
            {t('program_input.modals.delete_course_modal.body') +
              `${course.get('course_code', '')} - ${course.get('course_name', '')}" ${t(
                'question_mark'
              )}`}
          </div>
        ),
        variant: 'confirm',
        data: { data: course, operation: 'delete' },
        confirmButtonLabel: t('delete'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    // this.props.saveCourse({
    //   operation: 'delete',
    //   _id: course.get('_id'),
    //   programId: programId,
    //   curriculumId: curriculumId,
    // });

    const params = {
      courseId: course.get('_id'),
      isVersioned: course.get('versioned', false),
      ...(course.get('versionedFrom', '') && { defaultCourseId: course.get('versionedFrom', '') }),
    };
    this.props.deleteVersionCourse({
      params,
      programId: programId,
      curriculumId: curriculumId,
    });
  };

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ data, operation }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => {
        switch (operation) {
          case 'delete': {
            return this.handleDeleteCourse(data, true);
          }
          default:
            return;
        }
      }
    );
  }

  setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(titleIcon && { titleIcon }),
        ...(body && { body }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  handleFilterChange = (e, name) => {
    const value = e.target.value;
    this.setState((state) => {
      return {
        filters: {
          ...state.filters,
          [name]: value,
        },
      };
    });
  };

  getAssignedInOptions() {
    const { courseListById } = this.props;
    const { curriculumId, programId } = this.state;
    const assigned = courseListById
      .filter((c) => c.get('_curriculum_id') === curriculumId)
      .reduce((acc, c) => {
        const courseAssignedDetails = c.get('course_assigned_details', List());
        if (courseAssignedDetails.isEmpty()) {
          return acc;
        }
        return acc.concat(
          courseAssignedDetails.reduce((acc1, a) => {
            return acc1.push(
              Map({
                name: levelRename(a.get('level_no'), programId),
                value: a.get('level_no'),
              })
            );
          }, List())
        );
      }, List());
    let uniqueLevels = Map();
    assigned.forEach((a) => {
      uniqueLevels = uniqueLevels.set(a.get('value'), a);
    });
    return uniqueLevels
      .valueSeq()
      .toJS()
      .sort((a, b) => {
        const levelA = parseInt(a.name.replace(/\D/g, ''), 10);
        const levelB = parseInt(b.name.replace(/\D/g, ''), 10);
        return levelA - levelB;
      });
  }

  getAddCourseOptions() {
    return [
      { name: t('add_course_with_details'), callBack: this.handleAddCourse },
      { name: t('add_course_without_details'), callBack: () => this.handleAddCourse(false) },
    ];
  }

  handleMenuClick = (e, course) => {
    this.setState({ anchorInfo: { element: e.currentTarget, course } });
  };

  handleMenuClose = () => this.setState({ anchorInfo: { element: null } });

  render() {
    const {
      activeTab,
      curriculumName,
      curriculumId,
      programId,
      modalData,
      filters,
      importshow,
      showCourseVersion,
      courseName,
      anchorInfo,
    } = this.state;
    const { courseListById, isCourseMasterListLoading, t } = this.props;
    const { active, inactive } = this.getActiveInactiveCount(courseListById, curriculumId);
    const assignedInOptions = this.getAssignedInOptions();
    const anchorEl = anchorInfo.element;

    return (
      <React.Fragment>
        {isCourseMasterListLoading && <Loader isLoading />}
        <div className="main bg-gray pb-5">
          <div className="mt-3">
            <div className="d-flex justify-content-between mb-2">
              <p className="f-18 font-weight-bold mb-1">
                <Trans i18nKey={'curriculum'}></Trans> / {''}
                <Trans i18nKey={'course_master_list'}></Trans> / {jsUcfirstAll(curriculumName)}
              </p>
            </div>
            <div className="p-3 bg-white rounded">
              <p
                className={
                  lang === 'en'
                    ? 'f-16 font-weight-bold mb-1'
                    : 'f-16 font-weight-bold mb-1 text-left'
                }
              >
                <Trans i18nKey={'course_master_list'}></Trans>
              </p>
              <div className="d-flex justify-content-between mb-2">
                <div className="col-md-8 pl-0">
                  <div className="row">
                    <div className="col-md-3">
                      <div className="mt--15">
                        <Input
                          elementType="floatinginput2"
                          elementConfig={{
                            type: 'text',
                          }}
                          value={filters.courseName}
                          changed={(e) => this.handleFilterChange(e, 'courseName')}
                          placeholder={t('constant.search_code_name')}
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mt--15">
                        <Input
                          elementType="floatingselect"
                          elementConfig={{
                            options: [{ name: '', value: '' }, ...this.state.COURSE_TYPES],
                          }}
                          value={filters.courseType}
                          floatingLabel={<Trans i18nKey={'course_type'}></Trans>}
                          changed={(e) => this.handleFilterChange(e, 'courseType')}
                        />
                      </div>
                    </div>

                    <div className="col-md-3">
                      <div className="mt--15">
                        <Input
                          elementType={'floatingselect'}
                          elementConfig={{
                            options: [{ name: '', value: '' }, ...this.state.COURSE_STATUS],
                          }}
                          value={filters.courseStatus}
                          floatingLabel={<Trans i18nKey={'course_status'}></Trans>}
                          changed={(e) => this.handleFilterChange(e, 'courseStatus')}
                        />
                      </div>
                    </div>

                    <div className="col-md-3 pt-1">
                      <div className="pt-2">
                        <Select
                          multiple
                          fullWidth
                          variant="standard"
                          size="small"
                          displayEmpty
                          value={filters.assignedIn}
                          onChange={(e) => this.handleFilterChange(e, 'assignedIn')}
                          renderValue={(selected) => {
                            return !selected.length ? (
                              <div style={{ color: 'rgba(0, 0, 0, 0.54)' }}>
                                {' '}
                                <Trans i18nKey={'assigned_in'}></Trans>
                              </div>
                            ) : (
                              assignedInOptions
                                .filter((a) => selected.includes(a.value))
                                .map((a) => a.name)
                                .join(', ')
                            );
                          }}
                          MenuProps={FILTER_MENU_PROPS}
                        >
                          {assignedInOptions.map((option) => (
                            <MenuItem
                              className="white-space-normal"
                              key={option.value}
                              value={option.value}
                            >
                              <ListItemIcon>
                                <ThemeProvider theme={CHECKBOX_THEME}>
                                  <Checkbox
                                    color="primary"
                                    checked={filters.assignedIn.indexOf(option.value) > -1}
                                  />
                                </ThemeProvider>
                              </ListItemIcon>
                              <ListItemText primary={option.name} />
                            </MenuItem>
                          ))}
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="col-md-4 mt-1 pr-0">
                  <div className="d-flex justify-content-end">
                    {(CheckPermission('pages', 'Program Input', 'Programs', 'Add Program') ||
                      CheckPermission(
                        'pages',
                        'Program Input',
                        'Programs',
                        'Add Pre-requisite'
                      )) && (
                      <DropdownMenu
                        className="ml-2"
                        title={t('add_new_course')}
                        options={this.getAddCourseOptions()}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-2">
              <div className={`course_master ${lang === 'ar' ? 'float-right' : ''}`}>
                <ul id="menu-grouping" className={lang !== 'en' && 'float-left'}>
                  {this.state.TABS.map((tab, i) => (
                    <span
                      key={`${tab}-${i}`}
                      onClick={() => this.handleTabChange(i)}
                      className={`cursor-pointer tabaligment-blue ${
                        activeTab === i ? 'tabactive-blue' : ''
                      }`}
                    >
                      {`${tab} (${i === 0 ? active : inactive})`}
                    </span>
                  ))}
                </ul>
                <CourseMasterListTable
                  courseList={this.getCourseList(courseListById, curriculumId, activeTab === 0)}
                  handleEdit={this.handleEditCourse}
                  handleDelete={this.handleDeleteCourse}
                  handleClone={this.handleCourseVersion}
                  programId={programId}
                  curriculumId={curriculumId}
                  handleMenuClick={this.handleMenuClick}
                />
              </div>
            </div>
          </div>
        </div>
        {importshow && <ImportFromCourseFile closed={this.onImportchange.bind(this)} />}
        <AlertConfirmModal
          show={modalData.show}
          title={modalData.title || ''}
          titleIcon={modalData.titleIcon}
          body={modalData.body || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />

        {showCourseVersion && (
          <AddVersionModal
            show={showCourseVersion}
            // handleCourseVersion={this.handleCourseVersion}
            courseName={courseName}
            handleSave={this.handleSave}
            closeCourseVersion={this.closeCourseVersion}
          />
        )}

        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={this.handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: ITEM_HEIGHT * 4.5,
              width: '20ch',
            },
          }}
        >
          <MenuItem onClick={() => this.handleEditCourse(anchorInfo.course, false)}>
            <Trans i18nKey="edit_course_author" />
          </MenuItem>
        </Menu>
      </React.Fragment>
    );
  }
}

const CourseMasterList = withTranslation()(CourseMasterListComponent);

CourseMasterListComponent.propTypes = {
  getCourseList: PropTypes.func,
  courseListById: PropTypes.instanceOf(List),
  history: PropTypes.object,
  location: PropTypes.object,
  saveCourse: PropTypes.func,
  t: PropTypes.func,
  getCourseListByCurriculumId: PropTypes.func,
  isCourseMasterListLoading: PropTypes.bool,
  checkDuplicateVersion: PropTypes.func,
  deleteVersionCourse: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    courseListById: selectCourseListById(state),
    isCourseMasterListLoading: selectIsCourseMasterListLoading(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(CourseMasterList);
