import React, { Fragment, useState } from 'react';
import ParameterModal from './ParameterModal';

export default function AddParameter() {
  const [open, setOpen] = useState(false);
  return (
    <Fragment>
      <div
        className="text-center p-3 text-blue add_parameter_border bold"
        onClick={() => setOpen(true)}
      >
        + Add Parameters
      </div>
      {open && <ParameterModal onClose={() => setOpen(false)} />}
    </Fragment>
  );
}
