import React, { useState, useEffect, Suspense } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
//redux
import { selectLmsSettings } from '_reduxapi/leave_management/selectors';
import * as actions from '_reduxapi/leave_management/actions';
// utils
import MButton from 'Widgets/FormElements/material/Button';
// components
import WarningSettings from './Modal/WarningSettingsPopup';
import WarningTypeList from './WarningTypeList';
import { Radio } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import Tooltip from '@mui/material/Tooltip';
import { iscomprehensiveMode, useIsWarningValue } from '../utils';

const WarningAndNotifications = ({
  lmsSettings,
  updateWarningConfig,
  isEditWarning,
  getLmsSettings,
}) => {
  const [open, setOpen] = useState(false);
  const [operation, setOperation] = useState('');
  const [disableIndex, setDisableIndex] = useState('');
  const sessionWise = lmsSettings.get('leaveApplicationCriteria', '');
  const [confirmPopupOpen, setConfirmPopupOpen] = useState(Map());
  const warningMode = lmsSettings.get('warningMode', '');
  const iscomprehensive = iscomprehensiveMode(warningMode);
  const isWarningValue = useIsWarningValue(iscomprehensive);
  const iscomprehensiveCondition = isWarningValue;
  const configKey = iscomprehensiveCondition ? 'comprehensiveWarningConfig' : 'warningConfig';
  const ComprehensiveConfirmPopup = React.lazy(() => import('./Modal/ComprehensiveConfirmPopup'));
  useEffect(() => {
    getLmsSettings('leave');
  }, [getLmsSettings]);

  const handleClickOpen = (type, index) => {
    setOpen(true);
    setOperation(type);
    setDisableIndex(index);
  };

  const handleChangeActive = (active, id) => {
    const requestData = {
      settingId: lmsSettings.get('_id', ''),
      warningConfigId: id,
      isActive: active,
    };
    updateWarningConfig(requestData, configKey);
  };

  const handleChangeWorkingSystem = (event) => {
    const currentValue = event.target.value;
    setConfirmPopupOpen(
      Map({
        ConfirmPopupOpen: true,
        ChangeWorkingSystem: currentValue,
      })
    );
  };

  const TooltipComponent = ({ comprehensiveData }) => {
    return (
      <Tooltip
        placement="top"
        followCursor
        arrow
        fontSize="small"
        title={
          <div>
            {comprehensiveData === 'comprehensive' ? (
              <>Warning calculations performed on cumulative basis across all courses</>
            ) : (
              <> Warning calculations performed on course-by-course basis</>
            )}
          </div>
        }
      >
        <InfoIcon />
      </Tooltip>
    );
  };

  return (
    <React.Fragment>
      <div className="d-flex">
        <h5>Warning and Denial Configuration</h5>
        <div className="flex-grow-1"></div>
        {lmsSettings.get('warningConfig', List()).size > 0 && (
          <MButton
            size="small"
            variant={'text'}
            disabled={!isEditWarning}
            color={'blue'}
            clicked={() => handleClickOpen('updateNew')}
          >
            + Add New Type
          </MButton>
        )}
      </div>
      <div className="d-flex align-items-center py-2">
        <div className="d-flex align-items-center pr-2">
          <Radio
            checked={warningMode === 'course'}
            value="course"
            onChange={handleChangeWorkingSystem}
          />
          <div className="f-17 pr-2">Course Warning System</div>
          <TooltipComponent comprehensiveData={'course'} />
        </div>
        <div className="d-flex align-items-center pl-2">
          <Radio
            checked={warningMode === 'comprehensive'}
            value="comprehensive"
            onChange={handleChangeWorkingSystem}
          />
          <div className="f-17 pr-2">Comprehensive Warning System</div>
          <TooltipComponent comprehensiveData={'comprehensive'} />
        </div>
      </div>

      {confirmPopupOpen.get('ConfirmPopupOpen', false) && (
        <Suspense fallback="">
          <ComprehensiveConfirmPopup
            confirmPopupOpen={confirmPopupOpen}
            setConfirmPopupOpen={setConfirmPopupOpen}
            lmsSettings={lmsSettings}
            getLmsSettings={getLmsSettings}
          />
        </Suspense>
      )}
      {lmsSettings.get('warningConfig', List()).size === 0 && (
        <div className="d-flex justify-content-center">
          <MButton
            variant="contained"
            color={'blue'}
            disabled={!isEditWarning}
            clicked={() => handleClickOpen('create')}
          >
            Create
          </MButton>
        </div>
      )}

      {open && isEditWarning && (
        <WarningSettings
          open={open}
          setOpen={setOpen}
          operation={operation}
          disableIndex={disableIndex}
        />
      )}
      <WarningTypeList
        lmsSettings={lmsSettings}
        isEditWarning={isEditWarning}
        handleClickOpen={handleClickOpen}
        handleChangeActive={handleChangeActive}
        sessionWise={sessionWise}
      />
    </React.Fragment>
  );
};

WarningAndNotifications.propTypes = {
  lmsSettings: PropTypes.instanceOf(Map),
  updateWarningConfig: PropTypes.func,
  isEditWarning: PropTypes.bool,
  getLmsSettings: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    lmsSettings: selectLmsSettings(state),
  };
};

export default connect(mapStateToProps, actions)(WarningAndNotifications);
