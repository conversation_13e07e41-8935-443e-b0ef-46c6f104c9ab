import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON>ccordion, Card, Form } from 'react-bootstrap';
import 'react-responsive-modal/styles.css';
import { Modal } from 'react-responsive-modal';
import PropTypes, { oneOfType } from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { connect } from 'react-redux';
import * as actions from '../../../_reduxapi/user_management/action';
import * as actions1 from '../../../_reduxapi/actions/auth';

import {
  selectRolesList,
  selectRolesAssignedList,
  selectIsLoading,
  selectMessage,
  selectDepartmentsList,
  selectSelectedStaff,
  selectAssignRoleData,
} from '../../../_reduxapi/user_management/selectors';
import { selectAllProgramLists } from '../../../_reduxapi/Common/Selectors';
import Input from '../../../Widgets/FormElements/Input/Input';
import Switch from 'react-switch';
import { getLang, jsUcfirstAll } from '../../../utils';
import swal from 'sweetalert2';
import SearchModal from './SearchModal';
import { DYNAMIC_LOCKED_ROLES } from '../../../constants';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import { Autocomplete, TextField, Checkbox, Chip } from '@mui/material';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

const renderMuiMultiselect = (
  options,
  selectedValues,
  onSelect,
  onRemove,
  placeholder,
  displayValue = 'name'
) => {
  const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
  const checkedIcon = <CheckBoxIcon fontSize="small" />;

  return (
    <Autocomplete
      multiple
      options={options}
      value={selectedValues}
      getOptionLabel={(option) => option[displayValue] || ''}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      disableCloseOnSelect
      renderOption={(props, option, { selected }) => (
        <li {...props}>
          <Checkbox
            icon={icon}
            checkedIcon={checkedIcon}
            style={{ marginRight: 8 }}
            checked={selected}
          />
          {option[displayValue]}
        </li>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined"
          placeholder={placeholder}
          sx={{
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                borderColor: '#1976d2',
              },
              '&:hover fieldset': {
                borderColor: '#1976d2',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
              },
            },
          }}
        />
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            key={option.id || index}
            variant="outlined"
            label={option[displayValue]}
            {...getTagProps({ index })}
            sx={{
              margin: '2px',
              '& .MuiChip-deleteIcon': {
                color: '#666',
              },
            }}
          />
        ))
      }
      onChange={(event, newValue) => {
        if (newValue.length > selectedValues.length) {
          const addedItem = newValue.find(
            (item) => !selectedValues.some((selected) => selected.id === item.id)
          );
          onSelect([...selectedValues, addedItem]);
        } else {
          const removedItem = selectedValues.find(
            (item) => !newValue.some((newItem) => newItem.id === item.id)
          );
          const filteredValues = selectedValues.filter((item) => item.id !== removedItem.id);
          onRemove(filteredValues);
        }
      }}
      sx={{
        '& .MuiAutocomplete-inputRoot': {
          padding: '8px 12px',
        },
      }}
    />
  );
};

const renderMuiDepartmentMultiselect = (
  options,
  selectedValues,
  onSelect,
  onRemove,
  placeholder,
  displayValue = 'name'
) => {
  const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
  const checkedIcon = <CheckBoxIcon fontSize="small" />;

  return (
    <Autocomplete
      multiple
      options={options}
      value={selectedValues}
      getOptionLabel={(option) => option[displayValue] || ''}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      disableCloseOnSelect
      groupBy={(option) => option.programName || 'Other'}
      renderOption={(props, option, { selected }) => (
        <li {...props}>
          <Checkbox
            icon={icon}
            checkedIcon={checkedIcon}
            style={{ marginRight: 8 }}
            checked={selected}
          />
          {option[displayValue]}
        </li>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined"
          placeholder={placeholder}
          sx={{
            '& .MuiOutlinedInput-root': {
              '& fieldset': {
                borderColor: '#1976d2',
              },
              '&:hover fieldset': {
                borderColor: '#1976d2',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
              },
            },
          }}
        />
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            key={option.id || index}
            variant="outlined"
            label={option[displayValue]}
            {...getTagProps({ index })}
            sx={{
              margin: '2px',
              '& .MuiChip-deleteIcon': {
                color: '#666',
              },
            }}
          />
        ))
      }
      onChange={(event, newValue) => {
        if (newValue.length > selectedValues.length) {
          const addedItem = newValue.find(
            (item) => !selectedValues.some((selected) => selected.id === item.id)
          );
          onSelect([...selectedValues, addedItem]);
        } else {
          const removedItem = selectedValues.find(
            (item) => !newValue.some((newItem) => newItem.id === item.id)
          );
          const filteredValues = selectedValues.filter((item) => item.id !== removedItem.id);
          onRemove(filteredValues);
        }
      }}
      sx={{
        '& .MuiAutocomplete-inputRoot': {
          padding: '8px 12px',
        },
      }}
    />
  );
};

class AssignRoles extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeIndex: 0,
      roleView: false,
      arrow: true,
      type: 'Add',
    };

    this.style = {
      searchBox: {
        border: 'none',
        'border-bottom': '1px solid blue',
        'border-radius': '0px',
      },
    };
    this.styleAr = {
      searchBox: {
        border: 'none',
        'border-bottom': '1px solid blue',
        'border-radius': '0px',
      },
      // optionListContainer: {
      //   'text-align': 'right',
      // },
      // optionContainer: {
      //   // To change css for option container
      //   border: '2px solid',
      //   'text-align': 'right',
      // },
      // checkbox: {
      //   margin: '10px',
      // },
      // inputField: {
      //   // To change input field position or margin
      //   margin: '10px',
      //   color: 'red',
      // },
      option: {
        'text-align': 'right',
      },
    };
  }

  roleClick = () => {
    this.setState({ roleView: !this.state.roleView });
  };

  roleClickOn = () => {
    const {
      getRolesList,
      userData,
      getAssignRoleData,
      //resetMessage,
      listingPublishedPrograms,
    } = this.props;
    //resetMessage('');
    getRolesList();
    listingPublishedPrograms();
    if (userData.id !== '') {
      getAssignRoleData(userData.id);
    }

    this.setState({ roleView: !this.state.roleView });
  };

  handleRole = (e, name, index) => {
    const { rolesAssignedList, handleAssignedRole, rolesList } = this.props;
    const rolesAssignedListJS = rolesAssignedList.toJS();
    if (name === '_role_id') {
      const updateValue = rolesAssignedListJS.map((item, ind) => {
        let value = item[name];
        let role_name = item['role_name'];
        if (ind === index) {
          const filterName = rolesList
            .filter((role) => role.get('value') === e.target.value)
            .reduce((_, el) => el.get('name', ''), '');
          value = e.target.value;
          role_name = filterName;
        }
        return { ...item, [name]: value, role_name: role_name };
      });
      handleAssignedRole(fromJS(updateValue));
      setTimeout(() => {
        this.applyDepartment(index);
      }, 500);
    }
  };

  handleSwitch = (index, name) => {
    const { rolesAssignedList, handleAssignedRole } = this.props;
    const rolesAssignedListJS = rolesAssignedList.toJS();
    const updateValue = rolesAssignedListJS.map((item, ind) => {
      let status = item[name];
      if (ind === index) {
        status = !item[name];
      }
      return { ...item, [name]: status };
    });
    handleAssignedRole(fromJS(updateValue));
    if (name === 'is_department') {
      setTimeout(() => {
        this.applyDepartment(index);
      }, 500);
    }
  };

  applyDepartment = (index) => {
    const { rolesAssignedList, postDepartmentList, handleAssignedRole } = this.props;
    const rolesAssignedListJS = rolesAssignedList.toJS();
    const updateValue = rolesAssignedListJS
      .filter((_, ind) => ind === index)
      .reduce((_, el) => el, {});
    if (
      updateValue._role_id !== '' &&
      updateValue._program_ids.length > 0 &&
      updateValue.is_department
    ) {
      const data = {
        _role_id: updateValue._role_id,
        _program_ids:
          updateValue._program_ids &&
          updateValue._program_ids.length > 0 &&
          updateValue._program_ids.map((item) => item.id),
      };
      postDepartmentList(data, index);
    } else {
      const updateValue1 = rolesAssignedListJS.map((item, ind) => {
        let value = item['_department_ids'];
        if (ind === index) {
          value = [];
        }
        return { ...item, _department_ids: value };
      });
      handleAssignedRole(fromJS(updateValue1));
    }
  };

  addRoleList = () => {
    const { addRoleList, userData, publishedProgramLists } = this.props;
    let program_ids =
      userData.program_ids && userData.program_ids.length > 0
        ? userData.program_ids.map((item) => {
            return {
              id: item,
              name: publishedProgramLists
                .filter((pgm) => pgm.id === item)
                .reduce((_, el) => el.name, ''),
            };
          })
        : [];
    addRoleList(program_ids);
  };

  onSelect = (selectedList) => {
    const { activeIndex } = this.state;
    const { rolesAssignedList, handleAssignedRole, setSelectedStaff } = this.props;
    const rolesAssignedListJS = rolesAssignedList.toJS();
    const updateValue = rolesAssignedListJS.map((item, ind) => {
      let value = item['_program_ids'];
      if (ind === activeIndex) {
        value = selectedList;
      }
      return { ...item, _program_ids: value };
    });
    handleAssignedRole(fromJS(updateValue));
    setTimeout(() => {
      this.applyDepartment(activeIndex);
      setSelectedStaff(Map({}));
    }, 500);
  };

  onRemove = (selectedList) => {
    const { activeIndex } = this.state;
    const { rolesAssignedList, handleAssignedRole, setSelectedStaff } = this.props;
    const rolesAssignedListJS = rolesAssignedList.toJS();
    const updateValue = rolesAssignedListJS.map((item, ind) => {
      let value = item['_program_ids'];
      if (ind === activeIndex) {
        value = selectedList;
      }
      return { ...item, _program_ids: value };
    });
    handleAssignedRole(fromJS(updateValue));
    setTimeout(() => {
      this.applyDepartment(activeIndex);
      setSelectedStaff(Map({}));
    }, 500);
  };

  onSelectDepartment = (selectedList) => {
    const { activeIndex } = this.state;
    const { rolesAssignedList, handleAssignedRole } = this.props;
    const rolesAssignedListJS = rolesAssignedList.toJS();
    const updateValue = rolesAssignedListJS.map((item, ind) => {
      let value = item['_department_ids'];
      if (ind === activeIndex) {
        value = selectedList;
      }
      return { ...item, _department_ids: value };
    });
    handleAssignedRole(fromJS(updateValue));
  };

  onRemoveDepartment = (selectedList) => {
    const { activeIndex } = this.state;
    const { rolesAssignedList, handleAssignedRole } = this.props;
    const rolesAssignedListJS = rolesAssignedList.toJS();
    const updateValue = rolesAssignedListJS.map((item, ind) => {
      let value = item['_department_ids'];
      if (ind === activeIndex) {
        value = selectedList;
      }
      return { ...item, _department_ids: value };
    });
    handleAssignedRole(fromJS(updateValue));
  };

  removeRole = (index) => {
    swal
      .fire({
        title: t('sure_delete'),
        text: t('once_deleted_cant_recover'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: t('yes_delete'),
        cancelButtonText: t('no_keep'),
        dangerMode: true,
      })
      .then((res) => {
        if (res.isConfirmed) {
          const { rolesAssignedList, handleAssignedRole } = this.props;
          const rolesAssignedListJS = rolesAssignedList.toJS();
          const updateValue = rolesAssignedListJS.filter((_, ind) => ind !== index);
          handleAssignedRole(fromJS(updateValue));
        }
      });
  };

  setActiveIndex = (index) => {
    this.setState({ activeIndex: index });
  };

  static getDerivedStateFromProps(props, state) {
    if (props.assignRoleData.isEmpty()) {
      return {
        type: 'Add',
      };
    } else {
      return {
        type: 'Edit',
      };
    }
  }

  disableCheck = () => {
    const { rolesAssignedList } = this.props;
    return rolesAssignedList.size > 0; //selectedStaff.get('_id', '') !== '' &&
  };

  assignRoleSubmit = () => {
    const { assignRoleSubmit, userData, rolesAssignedList, selectedStaff } = this.props;
    const { type } = this.state;

    var isDefaultCheck = 0;
    var isRoleNameDuplicate = [];
    var isDepartmentApply = 0;
    var isHasRoleName = [];
    let preparedData = rolesAssignedList.map((item) => {
      let programs =
        item.get('_program_ids', List()) !== false
          ? item.get('_program_ids', List()).map((pgm) => pgm.get('id', ''))
          : List();
      let departments = item.get('_department_ids', List()).map((dept) => dept.get('id', ''));
      if (item.get('is_default', false) === true) {
        isDefaultCheck++;
      }
      isRoleNameDuplicate.push(item.get('_role_id', ''));
      isHasRoleName.push(item.get('role_name', ''));
      if (item.get('is_department', false) === true && departments.size === 0) {
        isDepartmentApply++;
      }
      return {
        is_default: item.get('is_default', false),
        is_admin: item.get('is_admin') !== undefined ? item.get('is_admin', false) : false,
        role_name: item.get('role_name'),
        _role_id: item.get('_role_id'),
        _department_ids: departments,
        _program_ids: programs,
      };
    });
    var uniqueRoleName = isRoleNameDuplicate.filter((v, i, a) => a.indexOf(v) === i);
    var roleNameLength = isHasRoleName.filter((item) => item !== '');
    let error = false;
    if (isDefaultCheck === 0 || isDefaultCheck > 1) {
      this.props.resetMessage('A single role should be marked as the default.');
      error = true;
    } else if (isRoleNameDuplicate.length !== uniqueRoleName.length) {
      this.props.resetMessage('Role name should be unique.');
      error = true;
    } else if (isDepartmentApply !== 0) {
      this.props.resetMessage('Choose department or remove apply department option.');
      error = true;
    } else if (roleNameLength.length !== preparedData.size) {
      this.props.resetMessage('Role name is required.');
      error = true;
    }

    if (!error) {
      const data = {
        _user_id: userData.id,
        roles: preparedData.toJS(),
        _report_to: selectedStaff.get('_id', ''),
      };
      assignRoleSubmit(data, type, () => {
        this.roleClick();
      });
    }
  };

  render() {
    const { roleView } = this.state;
    const {
      userData,
      rolesAssignedList,
      rolesList,
      publishedProgramLists,
      departmentsList,
      selectedStaff,
    } = this.props;
    const FirstName = userData.first !== undefined && userData.first !== '' ? userData.first : '';
    const MiddleName =
      userData.middle !== undefined && userData.middle !== '' ? userData.middle : '';
    const LastName = userData.last !== undefined && userData.last !== '' ? userData.last : '';

    let programIds = [];
    if (rolesAssignedList && rolesAssignedList.size > 0) {
      rolesAssignedList.map((item) => {
        item.get('_program_ids', List()).size > 0 &&
          item.get('_program_ids', List()).map((pgm) => {
            programIds.push(pgm.get('id', ''));
            return pgm.get('id', '');
          });
        return item;
      });
    }

    var programName = 'Not Assigned';
    if (
      !selectedStaff.isEmpty() &&
      selectedStaff.get('program', List()) &&
      selectedStaff.get('program', List()).size > 0
    ) {
      var pgName = selectedStaff.get('program', List()).map((item) => item.get('program_name', ''));
      if (pgName && pgName.size > 0) {
        programName = pgName.toJS().join(',');
      }
    }
    return (
      <React.Fragment>
        <i className="fa fa-shield" onClick={this.roleClickOn}></i>
        {roleView && (
          <Modal open={roleView} onClose={this.roleClick}>
            <div className="mb-5">
              <p className="f-18 mb-1 font-weight-bold d-flex">
                <Trans i18nKey={'user_management.assign_roles'} />
              </p>
              <p className="f-15 mb-1 d-flex">
                {jsUcfirstAll(FirstName + ' ' + MiddleName + ' ' + LastName).trim()}
              </p>
              <p className="f-15 mb-2 d-flex">
                <Trans i18nKey={'global_configuration.employee_id'} /> #{userData.employee_id}
              </p>

              <div className="row pt-3 justify-content-center">
                <div className="col-md-8 pb-2">
                  <div className="d-flex  justify-content-between">
                    <div>
                      <div className="pl-2">
                        <p className="assignRole mb-1"> 1</p>
                      </div>
                      <div>
                        <small className="text-center">
                          <Trans i18nKey={'role_management.role_actions.Assign Role'} />
                        </small>
                      </div>
                    </div>
                    <div>
                      <hr className="line_roles"></hr>
                    </div>
                    <div>
                      <div className="pl-2">
                        <p className="assignRole mb-1"> 2</p>
                      </div>
                      <small className="text-center">
                        {' '}
                        <Trans i18nKey={'user_management.reports_to'} />
                      </small>
                    </div>
                  </div>
                </div>
              </div>

              <div className="row">
                <div
                  className={`col-md-8 ${getLang() === 'ar' ? 'border-left-1' : ' border-right'}`}
                >
                  <Accordion defaultActiveKey="0">
                    {rolesAssignedList &&
                      rolesAssignedList.size > 0 &&
                      rolesAssignedList.map((role, index) => {
                        return (
                          <GetAccordionList
                            roleData={role}
                            key={index}
                            roleIndex={index}
                            handleSwitch={this.handleSwitch}
                            handleRole={this.handleRole}
                            style={getLang() === 'ar' ? this.styleAr : this.style}
                            rolesList={rolesList && rolesList.size > 0 ? rolesList.toJS() : []}
                            publishedProgramLists={publishedProgramLists}
                            onSelect={this.onSelect}
                            onRemove={this.onRemove}
                            setActiveIndex={this.setActiveIndex}
                            departmentsList={
                              departmentsList.get(index, List()) &&
                              departmentsList.get(index, List()).size > 0
                                ? departmentsList.get(index, List()).toJS()
                                : []
                            }
                            onSelectDepartment={this.onSelectDepartment}
                            onRemoveDepartment={this.onRemoveDepartment}
                            removeRole={this.removeRole}
                          />
                        );
                      })}
                  </Accordion>

                  <div className="pl-3 d-flex remove_hover" onClick={this.addRoleList}>
                    <i className="fa fa-plus pr-2 pt-1" aria-hidden="true">
                      {' '}
                    </i>
                    <span>
                      <Trans i18nKey={'role_management.modals.Add'}></Trans>
                    </span>
                  </div>
                </div>
                <div className="col-md-4 ">
                  <div className="row">
                    <div className="col-md-4">
                      <p className="mb-0 mt-3">
                        {' '}
                        <Trans i18nKey={'user_management.reports_to'} />
                      </p>
                    </div>

                    <div className="col-md-8">
                      <div className="pt-1 pb-2">
                        <div className="sb-example-2">
                          <SearchModal
                            title={t('user_management.reports_to')}
                            searchData={programIds}
                            employeeId={userData.employee_id}
                          />
                        </div>
                      </div>
                    </div>
                    {selectedStaff.isEmpty() ? (
                      ''
                    ) : (
                      <div className="col-md-12 pt-3">
                        {selectedStaff.getIn(['name', 'first'], '') !== '' && (
                          <div className="bg-gray p-3 border-radious-8">
                            <span className="">
                              {selectedStaff.getIn(['name', 'first'], '')}{' '}
                              {selectedStaff.getIn(['name', 'middle'], '')}{' '}
                              {selectedStaff.getIn(['name', 'last'], '')}{' '}
                            </span>

                            <span className="float-right">
                              #{selectedStaff.get('user_id', '')}{' '}
                            </span>
                            <p className="mb-0">
                              {' '}
                              <Trans i18nKey={'user_management.institute_role'} />:{' '}
                              {selectedStaff.get('role', 'Not Assigned') !== ''
                                ? selectedStaff.get('role', 'Not Assigned')
                                : 'Not Assigned'}
                            </p>
                            <p className="mb-0">
                              {' '}
                              <Trans i18nKey={'program'} />: {programName}
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="container">
              <div className="row">
                <div className="col-md-12">
                  <div className="float-right">
                    <b className="pr-3">
                      <Button variant="outline-primary" onClick={this.roleClick}>
                        <Trans i18nKey={'cancel_upper'} />
                      </Button>
                    </b>

                    <b className="pr-2">
                      <Button
                        variant={this.disableCheck() ? 'primary' : 'secondary'}
                        onClick={this.disableCheck() ? this.assignRoleSubmit : () => {}}
                        disabled={!this.disableCheck()}
                      >
                        <Trans i18nKey={'curriculum_keys.save'} />
                      </Button>
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal>
        )}
      </React.Fragment>
    );
  }
}

AssignRoles.propTypes = {
  rolesList: PropTypes.instanceOf(List),
  rolesAssignedList: PropTypes.instanceOf(List),
  selectedStaff: PropTypes.instanceOf(Map),
  getRolesList: PropTypes.func,
  getAssignRoleData: PropTypes.func,
  listingPublishedPrograms: PropTypes.func,
  handleAssignedRole: PropTypes.func,
  postDepartmentList: PropTypes.func,
  setSelectedStaff: PropTypes.func,
  addRoleList: PropTypes.func,
  assignRoleSubmit: PropTypes.func,
  resetMessage: PropTypes.func,
  userData: PropTypes.object,
  assignRoleData: PropTypes.any,
  noOfRolesAssigned: PropTypes.number,
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  departmentsList: PropTypes.instanceOf(List),
  publishedProgramLists: oneOfType([PropTypes.instanceOf(List), PropTypes.array]),
};

const mapStateToProps = function (state) {
  return {
    rolesList: selectRolesList(state),
    rolesAssignedList: selectRolesAssignedList(state),
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    publishedProgramLists: selectAllProgramLists(state),
    departmentsList: selectDepartmentsList(state),
    selectedStaff: selectSelectedStaff(state),
    assignRoleData: selectAssignRoleData(state),
  };
};

function GetAccordionList({
  roleData,
  style,
  handleSwitch,
  roleIndex,
  rolesList,
  handleRole,
  publishedProgramLists,
  onSelect,
  onRemove,
  setActiveIndex,
  departmentsList,
  onSelectDepartment,
  onRemoveDepartment,
  removeRole,
}) {
  let roleStatus =
    rolesList &&
    rolesList.length > 0 &&
    rolesList
      .filter((item) => item.value === roleData.get('_role_id'))
      .reduce((_, el) => el.isActive, 'false');
  console.log({ roleData });
  const surveyUnlockedRoles = !DYNAMIC_LOCKED_ROLES.filter(
    (surveyList) => !surveyList.includes('survey')
  ).includes(roleData.get('role_name', '').toLowerCase());

  return (
    <Card className="border-radious-8 border-none" onClick={() => setActiveIndex(roleIndex)}>
      {roleData.get('role_name', '') !== '' && (
        <Accordion.Toggle as={Card.Header} eventKey="0" className="roles_assign">
          <div className="d-flex justify-content-between">
            <p className="mb-0">
              <b className="pl-1">
                {roleData.get('role_name', '')}{' '}
                <span className={roleStatus ? 'text-green' : 'text-red'}>
                  {roleStatus
                    ? `(${t('program_input.active')})`
                    : `(${t('user_management.de_active')})`}
                </span>
              </b>
            </p>
            {/* <p className="mb-0">
            {this.state.arrow === true ? (
              <i className="fa fa-chevron-down f-14" aria-hidden="true"></i>
            ) : (
              <i className="fa fa-chevron-up fa-rotate-90 f-14" aria-hidden="true"></i>
            )}
          </p> */}
          </div>
        </Accordion.Toggle>
      )}

      <Accordion.Collapse eventKey="0">
        <Card.Body className="bg-white body_padding">
          <div className="roles_body">
            <div className="d-flex justify-content-end">
              {surveyUnlockedRoles && (
                <>
                  <p className={`f-14 mb-0 ${getLang() === 'ar' ? 'px--1' : 'mr-3'}`}>
                    <Trans i18nKey={'user_management.admin_role'} />
                  </p>
                  <label className="mb-0">
                    <Switch
                      checked={roleData.get('is_admin', false)}
                      onChange={() => handleSwitch(roleIndex, 'is_admin')}
                      onColor="#4698e7"
                      onHandleColor="#fff"
                      uncheckedIcon={false}
                      checkedIcon={false}
                      height={16}
                      width={36}
                      id="material-switch"
                    />
                  </label>
                </>
              )}
              <p className={`f-14 mb-0 ${getLang() === 'ar' ? 'px--1' : 'mr-3 ml-3'}`}>
                {' '}
                <Trans i18nKey={'user_management.default_role'} />
              </p>
              <label className="mb-0">
                <Switch
                  checked={roleData.get('is_default', false)}
                  onChange={() => handleSwitch(roleIndex, 'is_default')}
                  onColor="#4698e7"
                  onHandleColor="#fff"
                  uncheckedIcon={false}
                  checkedIcon={false}
                  height={16}
                  width={36}
                  id="material-switch"
                />
              </label>
              {surveyUnlockedRoles && (
                <label>
                  <i
                    className="fa fa-trash trashClass pl-3"
                    onClick={() => removeRole(roleIndex)}
                  ></i>
                </label>
              )}
            </div>
            {surveyUnlockedRoles && (
              <div className="row">
                <div className="col-md-8 pb-4">
                  <div className="mt--18">
                    <Input
                      elementType={'floatingselect'}
                      elementConfig={{
                        options:
                          rolesList.length > 0
                            ? rolesList
                                .sort((previous, next) => previous.name.localeCompare(next.name))
                                .filter(
                                  (item) =>
                                    !DYNAMIC_LOCKED_ROLES.filter((dynamicRoles) => {
                                      !dynamicRoles.includes('survey') ||
                                        !dynamicRoles.includes('year level');
                                    }).includes(item.name.toLowerCase())
                                )
                            : [],
                      }}
                      value={roleData.get('_role_id', '')}
                      floatingLabel={<Trans i18nKey={'role_management.modals.role_name'} />}
                      changed={(e) => handleRole(e, '_role_id', roleIndex)}
                    />
                  </div>
                </div>

                <div className="col-md-8 pb-4">
                  <div className="">
                    {renderMuiMultiselect(
                      publishedProgramLists,
                      roleData.get('_program_ids', List()).size > 0
                        ? roleData
                            .get('_program_ids')
                            .filter((v, i, a) => a.indexOf(v) === i)
                            .toJS()
                        : [],
                      onSelect,
                      onRemove,
                      roleData.get('_program_ids', List()).size > 0
                        ? ''
                        : t('user_management.select_program'),
                      'name'
                    )}
                  </div>
                </div>

                <div className="col-md-8 pb-3 d-flex">
                  <Form.Check
                    type="checkbox"
                    label={<Trans i18nKey={'user_management.apply_department'} />}
                    checked={roleData.get('is_department', false)}
                    onClick={() => handleSwitch(roleIndex, 'is_department')}
                  />
                </div>

                {roleData.get('is_department', false) === true && (
                  <div className="col-md-8 pb-3">
                    <div className="">
                      {renderMuiDepartmentMultiselect(
                        departmentsList,
                        roleData.get('_department_ids', List()).size > 0
                          ? roleData.get('_department_ids').toJS()
                          : [],
                        onSelectDepartment,
                        onRemoveDepartment,
                        roleData.get('_department_ids', List()).size > 0
                          ? ''
                          : t('user_management.select_department'),
                        'name'
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
            {DYNAMIC_LOCKED_ROLES.includes(roleData.get('role_name', '').toLowerCase()) && (
              <div className="text-center">
                <Trans i18nKey={'user_management.not_allow_dynamic_role'} />
              </div>
            )}
          </div>
        </Card.Body>
      </Accordion.Collapse>
    </Card>
  );
}

GetAccordionList.propTypes = {
  roleData: PropTypes.instanceOf(Map),
  rolesList: PropTypes.array,
  publishedProgramLists: PropTypes.array,
  departmentsList: PropTypes.array,
  style: PropTypes.object,
  handleSwitch: PropTypes.func,
  handleRole: PropTypes.func,
  onSelect: PropTypes.func,
  onRemove: PropTypes.func,
  setActiveIndex: PropTypes.func,
  onSelectDepartment: PropTypes.func,
  onRemoveDepartment: PropTypes.func,
  removeRole: PropTypes.func,
  roleIndex: PropTypes.number,
};

export default connect(mapStateToProps, { ...actions, ...actions1 })(AssignRoles);
