import React from 'react';
import PropTypes from 'prop-types';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';

const SearchInput = ({ placeholder, value, changed, sx }) => {
  return (
    <MaterialInput
      elementType="materialInput"
      type="text"
      variant="outlined"
      size="small"
      placeholder={placeholder}
      inputAdornment={
        value !== '' ? (
          <IconButton size="small" edge="end" onClick={() => changed('')}>
            <CloseIcon color="action" sx={{ fontSize: '16px' }} />
          </IconButton>
        ) : (
          <SearchIcon fontSize="small" color="action" />
        )
      }
      value={value}
      changed={(e) => changed(e.target.value)}
      sx={sx}
    />
  );
};
SearchInput.propTypes = {
  placeholder: PropTypes.string,
  value: PropTypes.string,
  changed: PropTypes.func,
  sx: PropTypes.object,
};

export default SearchInput;
