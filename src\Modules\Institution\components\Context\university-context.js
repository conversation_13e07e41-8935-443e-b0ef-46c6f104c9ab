import React from 'react';
const universityContext = React.createContext({
  institutionDetails: {},
});

const addCollegeContext = React.createContext({
  allCollegeData: {},
});
const filterSearchContext = React.createContext({
  search: '',
  option: '',
  setSearch: () => {},
  setSelectOption: () => {},
});

export default { addCollegeContext, universityContext, filterSearchContext }; //eslint-disable-line
