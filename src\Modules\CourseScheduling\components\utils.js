import React from 'react';
import { makeStyles } from '@mui/styles';
import { endOfDay, format, differenceInDays, isValid, set, startOfDay } from 'date-fns';
import moment from 'moment';
import { List, Map } from 'immutable';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import ArabicFond from '../../../Assets/fonts/Amiri-Regular.ttf';
import { useTheme } from '@mui/material/styles';
import {
  capitalize,
  getVersionName,
  indVerRename,
  isArabic,
  levelRename,
  studentGroupRename,
  studentGroupViewList,
} from '../../../utils';

export const muiMenuProps = {
  getContentAnchorEl: null,
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'center',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'center',
  },
  variant: 'menu',
};

export const useMuiMultiSelectStyles = makeStyles(() => {
  const theme = useTheme();
  return {
    single: {
      paddingLeft: theme.spacing(1),
      paddingRight: theme.spacing(2),
    },
    nested: {
      paddingLeft: theme.spacing(3),
      paddingRight: theme.spacing(2),
      paddingTop: '2px',
      paddingBottom: '2px',
    },
    listItemIcon: {
      minWidth: theme.spacing(6),
    },
    singleMenuItemLabel: {
      fontWeight: '500',
    },
    selectedAll: {
      backgroundColor: 'rgba(0, 0, 0, 0.08)',
      '&:hover': {
        backgroundColor: 'rgba(0, 0, 0, 0.08)',
      },
    },
  };
});

export function getWeeks(startDate, endDate) {
  startDate = new Date(startDate);
  endDate = new Date(endDate);
  if (!isValid(startDate) || !isValid(endDate)) return 0;
  endDate = endOfDay(endDate);
  const days = differenceInDays(endDate, startDate);
  let weeks = days / 7;
  if (!Number.isInteger(weeks)) {
    weeks = Math.trunc(weeks) + 1;
  }
  return weeks;
}

export function getFormattedCreditHours(course) {
  const creditHours = course.get('credit_hours', List()).map((c) => c.get('credit_hours', 0));
  return `Credit Hours: ${creditHours.reduce(
    (acc, creditHour) => acc + creditHour,
    0
  )} (${creditHours.join(' + ')})`;
}

export function getFormattedCourseDuration(course) {
  const startDate = new Date(course.get('start_date'));
  let endDate = new Date(course.get('end_date'));
  if (!isValid(startDate) || !isValid(endDate)) return '';
  endDate = endOfDay(endDate);
  const weeks = getWeeks(course.get('start_date'), course.get('end_date'));
  return `${format(startDate, 'MMM dd yyyy')} - ${format(endDate, 'MMM dd yyyy')} (${weeks} week${
    weeks > 1 ? 's' : ''
  })`;
}

export function getCourseDuration(course) {
  const isRotation = course.get('rotation', '') === 'yes';
  if (!isRotation) {
    return getFormattedCourseDuration(course);
  }
  return course
    .get('rotation_dates', List())
    .map((rotation) => {
      const formattedDuration = getFormattedCourseDuration(rotation);
      return `R${rotation.get('rotation_count', '')} - ${formattedDuration}`;
    })
    .join(', ');
}

export function getRotationDates(course) {
  return course.get('rotation_dates', List()).map((rotation) =>
    rotation.merge(
      Map({
        start_date: startOfDay(new Date(rotation.get('start_date'))),
        end_date: endOfDay(new Date(rotation.get('end_date'))),
      })
    )
  );
}

export function getFormattedGroupName(name, offset) {
  if (typeof name !== 'string') return name;
  const splitted = name.split('-');
  return splitted.slice(splitted.length - offset, splitted.length).join('-');
}

export function extractedRequiredDate(fromDate, toDate, days) {
  var start = new Date(fromDate);
  var end = new Date(toDate);
  const dateArray = [];
  for (var d = start; d <= end; d.setDate(d.getDate() + 1)) {
    var loopDay = new Date(d);
    const formatDate = moment(loopDay).format('YYYY-MM-DD');
    if (days.includes(moment(formatDate).format('dddd').toLowerCase())) {
      dateArray.push(formatDate);
    }
  }
  return dateArray;
}

export const convertTime12to24 = (time12h) => {
  const [time, modifier] = time12h.split(' ');
  let [hours, minutes] = time.split(':');
  if (hours === '12') {
    hours = '00';
  }
  if (modifier === 'PM') {
    hours = parseInt(hours, 10) + 12;
  }
  return `${hours}:${minutes}:00`;
};

export function getWeeksArray(fromDate, toDate) {
  const totalWeeks = getWeeks(fromDate, toDate);
  var prepareStartDate = fromDate;
  var arrayList = [];
  for (var i = 1; i <= totalWeeks; i++) {
    let prepareEndDate = moment(prepareStartDate).add(6, 'days').format('YYYY-MM-DD');
    const preparedDate =
      moment(prepareStartDate).format('MMM D') +
      ' - ' +
      moment(prepareEndDate).format('MMM D YYYY');
    arrayList.push({
      formattedDate: preparedDate,
      date: moment(prepareStartDate).format('YYYY-MM-DD'),
      weekNumber: i,
      endDate: moment(prepareEndDate).format('YYYY-MM-DD'),
    });
    prepareStartDate = moment(prepareStartDate).add(7, 'days').format('YYYY-MM-DD');
  }
  return arrayList;
}

export function renderEventContent(eventInfo) {
  return (
    <div className={'text-center calendarFont'}>
      {eventInfo.event.title !== '' && (
        <>
          <b>{eventInfo.event.title}</b>
          <br />
        </>
      )}
      <i>{eventInfo.timeText}</i>
    </div>
  );
}

export function getFirstLastDate(date) {
  var curr = new Date(date); // get current date
  var first = curr.getDate() - curr.getDay(); // First day is the day of the month - the day of the week
  var last = first + 6; // last day is the first day + 6
  var firstDay = new Date(curr.setDate(first)).toUTCString();
  var lastDay = new Date(curr.setDate(last)).toUTCString();
  return {
    firstDate: firstDay,
    lastDate: lastDay,
  };
}

export function getHour(date) {
  if (date.isEmpty()) return 0;
  const meridiem = date.get('format');
  const hour = date.get('hour');
  return meridiem === 'AM' ? (hour === 12 ? 0 : hour) : hour === 12 ? 12 : hour + 12;
}

export function getStartEndDate(date) {
  if (date.isEmpty()) return null;
  return set(startOfDay(new Date()), { hours: getHour(date), minutes: date.get('minute') });
}

export function transformDateToCustomObject(date) {
  if (!isValid(date)) return Map();
  const hours = +format(date, 'h');
  const minutes = +format(date, 'mm');
  const meridiem = format(date, 'a');
  return Map({ hour: hours, minute: minutes, format: meridiem });
}

export function formatStartEndDate(dateArr) {
  let startDate = dateArr.get('firstDate', '');
  let endDate = dateArr.get('lastDate', '');
  if (startDate !== '' && endDate !== '') {
    return moment(startDate).format('MMM D') + ' - ' + moment(endDate).format('MMM D YYYY');
  }
  return '';
}

function prefixZero(num) {
  if (typeof num !== 'number') return num;
  return num < 10 ? `0${num}` : num;
}

function getScheduleTime(schedule) {
  const start = schedule.get('start', Map());
  const end = schedule.get('end', Map());
  return `${start.get('hour')}:${prefixZero(start.get('minute', 0))} ${start.get(
    'format'
  )} - ${end.get('hour')}:${prefixZero(end.get('minute', 0))} ${end.get('format')}`;
}

export function exportCourseScheduleAsPDF(courseSchedule) {
  const courseDetails = courseSchedule.get('courseDetails', Map());
  const course = courseSchedule.get('course', Map());
  const programName = courseDetails.get('program_name', '');
  const programId = courseDetails.get('_program_id', '');
  const courseName = courseDetails.get('course_name', '');
  const courseCode = courseDetails.get('course_code', '');
  const versionName = getVersionName(courseDetails);

  const courseType = capitalize(indVerRename(courseDetails.get('course_type', ''), programId));
  const courseDuration = getFormattedCourseDuration(course);
  const subjects = courseDetails
    .get('participating', List())
    .map((p) => p.get('subject_name'))
    .join(', ');
  const creditHours = getFormattedCreditHours(course);
  const year = course.get('year_no', '').split('year').join('Year ');
  const level = levelRename(course.get('level_no', ''), programId);
  const term = `${capitalize(course.get('term', ''))} ${indVerRename('Term', programId)}`;
  const academicYear = `Academic Year - ${courseDetails
    .get('academicYear', '')
    .split('-')
    .join(' to ')}`;
  const adminDepartment = courseDetails.getIn(['administration', 'department_name'], '');
  const studentGroups = courseSchedule.get('student_groups', List()).join(', ');
  const exportType = courseSchedule.getIn(['schedule', 0, 'type'], 'regular');

  const schedule = courseSchedule
    .get('schedule', List())
    .map((s) => {
      return exportType === 'regular'
        ? List([
            `${s.get('delivery_symbol')}${s.get('delivery_no')} ${s.get('delivery_topic')}`,
            s
              .get('student_groups', List())
              .reduce((sgAcc, sg) => {
                const sessionGroups = sg
                  .get('session_group', List())
                  .reduce(
                    (sessionGroupAcc, sessionGroup) =>
                      sessionGroupAcc.push(getFormattedGroupName(sessionGroup, 3)),
                    List()
                  )
                  .join(', ');
                return sgAcc.push(
                  `${getFormattedGroupName(
                    studentGroupRename(sg.get('group_name', ''), programId),
                    2
                  )} - ${sessionGroups}`
                );
              }, List())
              .join(', '),
            s.get('subjects', List()).join(', '),
            capitalize(indVerRename(s.get('mode', ''), programId)),
            s
              .get('staffs', List())
              .map(
                (staff) =>
                  `${staff.get('first', '')} ${staff.get('middle', '')} ${staff.get('last', '')}`
              )
              .join(', '),
            s.get('infra_name', ''),
            format(new Date(s.get('schedule_date')), 'd LLL yyyy'),
            getScheduleTime(s),
          ])
        : List([
            `${capitalize(s.get('title'))}`,
            `${studentGroupViewList(s.get('student_groups', List()), programId)
              .entrySeq()
              .map(
                ([groupName, sGroup]) =>
                  getFormattedGroupName(studentGroupRename(groupName, programId), 2) +
                  (sGroup.get('delivery_symbol', '') !== ''
                    ? `-${sGroup.get('session_group')}`
                    : '')
              )
              .join(', ')}`,
            `${capitalize(s.get('sub_type'))}`,
            s.get('subjects', List()).join(', '),
            capitalize(indVerRename(s.get('mode', ''), programId)),
            s
              .get('staffs', List())
              .map(
                (staff) =>
                  `${staff.get('first', '')} ${staff.get('middle', '')} ${staff.get('last', '')}`
              )
              .join(', '),
            s.get('infra_name', ''),
            format(new Date(s.get('schedule_date')), 'd LLL yyyy'),
            getScheduleTime(s),
          ]);
    })
    .toJS();

  const isArabicText = schedule.length > 0 ? isArabic(schedule[0][0]) : false;

  let tableHeaders = [
    ['Delivery Type', 'Student Group', 'Subject', 'Mode', 'Staff', 'Infra', 'Date', 'Time'],
  ];
  if (exportType !== 'regular') {
    tableHeaders = [
      [
        exportType === 'support_session' ? 'Session Title' : 'Event Title',
        'Student Group',
        'Type',
        'Subject',
        'Mode',
        'Staff',
        'Infra',
        'Date',
        'Time',
      ],
    ];
  }
  const doc = new jsPDF('landscape');
  doc.addFont(ArabicFond, 'Amiri', 'normal');
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(15);
  doc.text(`${programName}: ${courseCode} - ${courseName}${versionName}`, 10, 10);
  doc.setFont('helvetica', 'normal', 400);
  doc.setFontSize(10);
  doc.text(`${courseType}  |  ${creditHours}  |  ${courseDuration}`, 10, 20);
  doc.text(`${year} ${level}, ${term}  |  ${academicYear}`, 10, 30);
  doc.text(`Admin Department - ${adminDepartment}`, 10, 40);
  doc.text(`Subject - ${subjects}`, 10, 50);
  if (exportType === 'regular') {
    doc.text(`Student Group - ${studentGroupRename(studentGroups, programId)}`, 10, 60);
  }
  doc.setFont('helvetica', 'bold');
  doc.text(
    exportType === 'regular'
      ? 'Regular Session'
      : exportType === 'support_session'
      ? 'Support Session'
      : 'Events',
    280,
    60,
    null,
    null,
    'right'
  );
  doc.line(0, 70, 300, 70);
  doc.autoTable({
    startY: 75,
    styles: { fillColor: false, textColor: '#000000' },
    columnStyles: {
      0: {
        font: isArabicText ? 'Amiri' : '',
        halign: isArabicText ? 'right' : '  ',
        cellWidth: 70,
      },
      6: { cellWidth: 23 },
      7: { cellWidth: 34 },
    },
    headStyles: { lineWidth: 0.5, lineColor: [189, 195, 199] },
    bodyStyles: { fontSize: 9 },
    rowPageBreak: 'avoid',
    // tableLineColor: [189, 195, 199],
    // tableLineWidth: 0.5,
    head: tableHeaders,
    body: schedule.length ? schedule : [['No data found']],
    theme: 'grid',
  });
  doc.save(`${courseCode}-${courseName}-${exportType}.pdf`);
}

export function transformDateTimeToUTC(setDate, setHour, setMinute, format) {
  const setSecond = '00';
  const mergeTime = `${setHour}:${setMinute}:${setSecond} ${format}`;
  const formattedDate = moment(setDate).format('YYYY/MM/DD');
  const mergeDateAndTime = `${formattedDate} ${mergeTime}`;
  let utcDate = moment(new Date(mergeDateAndTime)).utc().format();
  return utcDate;
}
