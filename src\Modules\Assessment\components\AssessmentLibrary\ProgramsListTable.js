import React from 'react';
import { useHistory } from 'react-router-dom';
import MButton from 'Widgets/FormElements/material/Button';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import { eString } from '../../../../utils';
import { CheckPermission } from 'Modules/Shared/Permissions';

const ProgramsListTable = (props) => {
  const { programList, view } = props;
  const history = useHistory();

  const assessmentPlanPermission = CheckPermission(
    'tabs',
    'Assessment Management',
    'Assessment Library',
    '',
    'Assessment Plan',
    'View'
  );
  function goToDetails(item) {
    const redirectUrl =
      view === 'regulations'
        ? '/attainment-calculator/attainment-settings/regulations'
        : `/assessment-management/assessment_details${assessmentPlanPermission ? '/planning' : ''}`;
    const assessmentPlan = ``;
    // !assessmentPlanPermission ? `&year=${eString('assess')}` : ``;

    history.push(
      `${redirectUrl}?pid=${eString(item.get('_id', ''))}&pname=${eString(
        item.get('programName', '')
      )}${assessmentPlan}&type=${eString('program')}`
    );
  }

  const viewActionPermission =
    view === 'regulations'
      ? CheckPermission('pages', 'Attainment Calculator', 'Attainment Setting', 'View Action')
      : CheckPermission('pages', 'Assessment Management', 'Assessment Library', 'View Action');
  return (
    <div className="program_table">
      <table align="left">
        <thead>
          <tr>
            <th>
              <div className=""> Code</div>
            </th>

            <th className="w-18">
              <div className=""> Program Name</div>
            </th>

            <th className="w-18">
              <div className=""> Program Type</div>
            </th>
            <th className="w-15">
              <div className=""> Degree Name</div>
            </th>
            <th>
              <div className=""> Direct Assessment</div>
            </th>
            <th>
              <div className=""> Indirect Assessment</div>
            </th>
            {viewActionPermission && (
              <th>
                <div className="">Action</div>
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {programList?.size > 0 ? (
            programList?.map((item, index) => (
              <tr key={index} className="tr-change">
                <td className="">
                  <div className="mt-2">{item.get('programCode', '')}</div>
                </td>
                <td className="">
                  <div className="mt-2">{item.get('programName', '--')}</div>
                </td>
                <td className="">
                  <div className="mt-2">{item.get('type', item.get('program_type', ''))}</div>
                </td>
                <td className="">
                  <div className="mt-2">{item.get('programDegree', '--')}</div>
                </td>
                <td className="">
                  <div className="mt-2">{item.get('direct', 0)}</div>
                </td>
                <td className="">
                  <div className="mt-2">{item.get('indirect', 0)}</div>
                </td>
                {viewActionPermission && (
                  <td className="">
                    <div className="d-flex justify-content-between ">
                      <MButton variant="outlined" color={'gray'} clicked={() => goToDetails(item)}>
                        View
                      </MButton>
                    </div>
                  </td>
                )}
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={7} className="text-center">
                No record found
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
ProgramsListTable.propTypes = {
  programList: PropTypes.instanceOf(List),
  goToDetails: PropTypes.func,
  view: PropTypes.string,
};
export default ProgramsListTable;
