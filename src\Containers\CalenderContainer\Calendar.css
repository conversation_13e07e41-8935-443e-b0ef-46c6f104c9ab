.title {
  font-size: 13px;
  color: #818ea3;
}
.hijriMonthName {
  font-size: 13px;
  color: #808080;
}
.table {
  display: table;
  color: #1a1919;
  font-size: 13px;
}
.tr {
  display: table-row;
}

.td {
  display: table-cell;
  padding-top: 3px;
}

/* .disabled{
    color: lightgray;
    pointer-events: none;
}

.selected { 
    color: #ffffff;
    width: 30px;
    padding: 5px;
    border-radius: 7px;
    background-color: #fe0f58;
} */

/* .hijriNumber{
    font-size: 10px;
    color: #808080;
    margin-bottom: 6px;
    margin-top: -4px;
} */
.mb-0 {
  margin-bottom: 0px;
}
.border-none {
  border: none;
}
.calenderTopContent {
  position: relative;
}
.calendarDropdown {
  margin-top: 0px;
  height: 32px !important;
  -webkit-appearance: none;
  width: 90px;
  padding: 4px 0px;
  /* padding: 5px; */
  padding-left: 12px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0);
  border: solid 1px #a9b9c6;
}
.settingWidth {
  width: 18%;
}
.caretDownColor {
  color: #a9b9c6;
}
.dropdownBorder {
  border: solid 1px #a9b9c6 !important;
}
.calendarCard {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 17px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}
.calenderLineBorder {
  margin-left: -35px;
  margin-right: -35px;
}
.calendarTodayBorder {
  border: 1px solid #ebeced;
  padding: 8px;
  border-radius: 10px;
  font-size: 12px;
}
.calendarMt-28 {
  margin-top: 28px;
}
.dropDownColor {
  color: #3d5170;
}
.calendarArrow {
  border: 1px solid #ebeced;
  padding: 8px;
  border-radius: 10px;
  font-size: 12px;
}
@media screen and (min-width: 320px) and (max-width: 767px) {
  .settingWidth {
    width: 20%;
    margin-right: -20px;
  }
  .calendarCard {
    padding: 6px;
  }
  .calenderLineBorder {
    margin-left: -6px;
    margin-right: -6px;
  }
}
.calender-list {
  height: calc(100vh - 300px);
  overflow-y: auto;
}
.calender-list-textField {
  width: 232px;
}
@media (min-width: 768px) and (max-width: 1024px) {
  .calender-list-textField {
    width: 100%;
  }
}
