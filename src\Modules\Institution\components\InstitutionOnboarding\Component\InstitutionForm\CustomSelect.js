import React from 'react';
import PropTypes from 'prop-types';

import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import InputLabel from '@mui/material/InputLabel';

function CustomSelect({
  label,
  name,
  options,
  value,
  handleChange,
  setID = () => {},
  getOptions = () => {},
  reset = () => {},
  previousID,
}) {
  return (
    <FormControl variant="outlined" size="small" fullWidth>
      <InputLabel htmlFor={`outlined-${name}-native-simple`}>{label}</InputLabel>
      <Select
        native
        value={value}
        onChange={(event) => {
          setID({ previous: previousID, current: event.target.selectedIndex });
          handleChange(event.target.value);
          reset();
          getOptions(name);
        }}
        label={label}
        inputProps={{
          name,
          id: `outlined-${name}-native-simple`,
        }}
      >
        <option aria-label="None" value="">
          Select
        </option>
        {options.map((o, i) => (
          <option key={`${o.value}-${i}`} value={o.value}>
            {o.name}
          </option>
        ))}
      </Select>
    </FormControl>
  );
}

CustomSelect.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string,
  options: PropTypes.array,
  value: PropTypes.string,
  handleChange: PropTypes.func,
  getOptions: PropTypes.func,
  previousID: PropTypes.number,
  setID: PropTypes.func,
  reset: PropTypes.func,
};

export default CustomSelect;
