import { styled } from '@mui/material/styles';
import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import { IconButton, Tooltip } from '@mui/material';
import React from 'react';
import { fromJS } from 'immutable';
export const drawerWidth = 240;

export const Accordion = styled((props) => <MuiAccordion {...props} />)(
  ({ theme, rankHolder }) => ({
    '&:before': {
      display: 'none',
    },
    '&.Mui-expanded': {
      margin: 0,
    },
  })
);
export const AccordionSummary = styled(MuiAccordionSummary)(({ theme }) => ({
  minHeight: '0px',
  '&.Mui-expanded': {
    minHeight: '0px',
  },
  '& .MuiAccordionSummary-content.Mui-expanded': {
    margin: '0px',
  },
  '& .MuiAccordionSummary-content': {
    margin: 0,
  },
}));

export const CustomIconButton = ({ children, hoverColor, tooltipText, ...props }) => {
  const iconButtonStyles = {
    borderRadius: '8px',
    width: '36px',
    height: '36px',
    '&:hover': {
      backgroundColor: hoverColor || '#F5F5F5',
    },
  };

  return (
    <Tooltip title={tooltipText}>
      <IconButton sx={iconButtonStyles} {...props} disableRipple>
        {children}
      </IconButton>
    </Tooltip>
  );
};

export const placeholderStyles = {
  '& input::placeholder': {
    fontSize: 'clamp(0.75em, 2vw, 0.93em)',
  },
  '& input': {
    fontSize: 'clamp(0.75em, 2vw, 0.93em)',
  },
  '& textarea::placeholder': {
    fontSize: 'clamp(0.75em, 2vw, 0.93em)',
  },
};

export const cardAvatarColors = fromJS([
  {
    bgColors: '#E1F5FA',
    color: '#2159BA',
  },
  {
    bgColors: '#DCFCE7',
    color: '#166534',
  },
  {
    bgColors: '#E9F7FD',
    color: '#0E638A',
  },
  {
    bgColors: '#FFEFD5',
    color: '#FF6347',
  },
  {
    bgColors: '#F0F8FF',
    color: '#4682B4',
  },
  {
    bgColors: '#E6E6FA',
    color: '#6A5ACD',
  },
  {
    bgColors: '#FFE4B5',
    color: '#8B4513',
  },
  {
    bgColors: '#FFFACD',
    color: '#FFD700',
  },
  {
    bgColors: '#E0FFFF',
    color: '#00CED1',
  },
  {
    bgColors: '#FFDAB9',
    color: '#FF6347',
  },
]);

export const generateAbbreviation = (item) => {
  const words = item.split(' ');
  const initials = words.map((word) => word.charAt(0).toUpperCase());
  return initials.join('');
};

export const dialogSX = {
  boxShadow: '0',
  borderRadius: '8px',
};

export const createCalenderDesign = {
  dialogSX: {
    boxShadow: '0',
    borderRadius: '8px',
    width: '480px',
  },
  menuItemStyles: {
    fontSize: '14px',
    '&.MuiMenuItem-root': {
      minHeight: 30,
    },
  },
  placeholderStyles: {
    '& input::placeholder': {
      fontSize: '14px',
      color: '#D1D5DB',
      fontWeight: 500,
    },
  },
  iconButtonStyles: {
    width: '26px',
    height: '26px',
    borderRadius: '8px',
    border: '1px solid #D1D5DB',
  },
  widthModifier: [
    {
      name: 'widthModifier',
      enabled: true,
      phase: 'beforeWrite',
      requires: ['computeStyles'],
      fn: ({ state }) => {
        state.styles.popper.width = `${state.rects.reference.width}px`;
      },
    },
  ],
  selectArrowIconSx: { fontSize: '18px', color: '#9CA3AF !important', marginRight: '10px' },
};
