import React, { useState, useEffect } from 'react';
import { fromJS } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import Drawer from '@mui/material/Drawer';
import {
  Box,
  Typography,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import {
  upsertBonusAttendance,
  getStudentBonusAttendance,
  deleteBonusAttendance,
} from '_reduxapi/bonus_attendance/actions';
import { generateSignedUrl } from '_reduxapi/program_input/action';
import { selectBonusAttendanceData } from '_reduxapi/bonus_attendance/selectors';
import useFileUpload from '../../../Hooks/useFileUpload';

const FilePreviewDialog = ({ open, onClose, previewFile }) => {
  return (
    <Dialog open={open} onClose={onClose} sx={{ zIndex: 1500 }} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">{previewFile?.name || 'File Preview'}</Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        {previewFile && (
          <Box sx={{ textAlign: 'center' }}>
            {previewFile.viewUrl ? (
              previewFile.type === 'pdf' ? (
                <iframe
                  src={previewFile.viewUrl}
                  width="100%"
                  height="500px"
                  title={previewFile.name}
                  style={{ border: 'none' }}
                />
              ) : ['jpg', 'jpeg', 'png', 'gif'].includes(previewFile.type) ? (
                <img
                  src={previewFile.viewUrl}
                  alt={previewFile.name}
                  style={{ maxWidth: '100%', maxHeight: '500px' }}
                />
              ) : (
                <Box sx={{ p: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    Preview not available for this file type
                  </Typography>
                  <Button
                    variant="contained"
                    href={previewFile.viewUrl}
                    target="_blank"
                    sx={{ mt: 2 }}
                  >
                    Download File
                  </Button>
                </Box>
              )
            ) : (
              <Typography variant="body1" color="text.secondary">
                File URL not available
              </Typography>
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

const drawerPaperW40 = {
  '& .MuiDrawer-paper': { width: '40em' },
  zIndex: 1301,
};

const BonusAttendanceDrawer = ({
  show,
  onClose,
  studentId,
  programId,
  courseId = null,
  year = null,
  level = null,
  term = null,
  rotationCount = null,
}) => {
  const dispatch = useDispatch();
  const bonusAttendanceList = useSelector(selectBonusAttendanceData) || [];
  const authDataArray = useSelector((state) => state?.auth);
  const authData = fromJS(authDataArray);
  const userId = authData.getIn(['loggedInUserData', '_id'], '');
  const [formData, setFormData] = useState({
    percentage: '',
    reason: '',
  });
  const [editingId, setEditingId] = useState(null);
  const [formErrors, setFormErrors] = useState({});
  const [attachment, setAttachment] = useState(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);

  const { upload, uploading, fileData, reset } = useFileUpload();

  useEffect(() => {
    if (show && studentId) {
      dispatch(
        getStudentBonusAttendance(studentId, programId, courseId, year, level, term, rotationCount)
      );
    }
  }, [show, studentId, programId, courseId, year, level, term, rotationCount, dispatch]);

  const validateForm = () => {
    const errors = {};
    if (!formData.percentage || formData.percentage <= 0 || formData.percentage > 100) {
      errors.percentage = 'Percentage must be between 1 and 100';
    }
    if (!formData.reason || formData.reason.trim().length === 0) {
      errors.reason = 'Reason is required';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleSubmit = () => {
    if (!validateForm()) return;
    const attachmentData = attachment
      ? {
          name: attachment.name,
          url: attachment.url,
          type: attachment.type,
        }
      : null;
    const payload = {
      programId: programId,
      courseId: courseId,
      year: year,
      level: level,
      term: term,
      rotationCount: rotationCount,
      studentId: studentId,
      percentage: Number(formData.percentage),
      reason: formData.reason.trim(),
      approvedBy: userId,
      ...(attachmentData && { attachment: attachmentData }),
    };

    const callBack = () => {
      dispatch(
        getStudentBonusAttendance(studentId, programId, courseId, year, level, term, rotationCount)
      );
    };
    dispatch(upsertBonusAttendance(payload, editingId, callBack));
    setEditingId(null);
    setFormData({ percentage: '', reason: '' });
    setFormErrors({});
    setAttachment(null);
  };

  const handleEdit = (item) => {
    setEditingId(item._id);
    setFormData({
      percentage: item.percentage.toString(),
      reason: item.reason,
    });
    setAttachment(item.attachment || null);
  };

  const handleDelete = (id) => {
    const callBack = () => {
      dispatch(
        getStudentBonusAttendance(studentId, programId, courseId, year, level, term, rotationCount)
      );
    };
    dispatch(deleteBonusAttendance(id, callBack));
  };

  const handleCancel = () => {
    setEditingId(null);
    setFormData({ percentage: '', reason: '' });
    setFormErrors({});
    setAttachment(null);
  };

  const handleFileUpload = async (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      try {
        await upload({
          files: [file],
          component: 'documents',
          folderPathExtension: 'bonus-attendance',
        });
      } catch (error) {
        console.error('File upload failed:', error);
      }
    }
  };

  useEffect(() => {
    if (fileData && fileData.length > 0) {
      const attachmentData = {
        name: fileData[0].name,
        url: fileData[0].unSignedUrl,
        viewUrl: fileData[0].signedUrl,
        type: fileData[0].type,
      };
      setAttachment(attachmentData);
      reset();
    }
  }, [fileData, reset]);

  const handleDeleteAttachment = () => {
    setAttachment(null);
  };

  const handlePreview = (file) => {
    if (file.viewUrl) {
      setPreviewFile(file);
      setPreviewDialogOpen(true);
    } else if (file.url) {
      dispatch(
        generateSignedUrl(file.url, (signedUrl) => {
          const fileWithSignedUrl = {
            ...file,
            viewUrl: signedUrl,
          };
          setPreviewFile(fileWithSignedUrl);
          setPreviewDialogOpen(true);
        })
      );
    }
  };

  const handleClosePreview = () => {
    setPreviewDialogOpen(false);
    setPreviewFile(null);
  };

  return (
    <Drawer sx={drawerPaperW40} anchor="right" open={show} onClose={onClose}>
      <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" component="h3" fontWeight="bold">
            Bonus Attendance
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            {editingId ? 'Edit Bonus' : 'Add New Bonus'}
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
              label="Percentage (%)"
              type="number"
              value={formData.percentage}
              onChange={(e) => handleInputChange('percentage', e.target.value)}
              error={!!formErrors.percentage}
              helperText={formErrors.percentage}
              sx={{ flex: 1 }}
              inputProps={{ min: 1, max: 100 }}
            />
            <Button
              variant="contained"
              onClick={handleSubmit}
              startIcon={<AddIcon />}
              sx={{ minWidth: 120 }}
            >
              {editingId ? 'Update' : 'Add Bonus'}
            </Button>
            {editingId && (
              <Button variant="outlined" onClick={handleCancel}>
                Cancel
              </Button>
            )}
          </Box>

          <TextField
            label="Reason"
            multiline
            rows={3}
            value={formData.reason}
            onChange={(e) => handleInputChange('reason', e.target.value)}
            error={!!formErrors.reason}
            helperText={formErrors.reason}
            fullWidth
          />
          <Box sx={{ mb: 2 }} className="d-flex flex-row justify-content-between">
            <Box>
              <Typography variant="subtitle2" sx={{ mt: 2, mb: 1, color: 'text.secondary' }}>
                Attached Documents:
              </Typography>
              <input
                type="file"
                style={{ display: 'none' }}
                id="file-upload"
                accept=".jpg,.jpeg,.png,.pdf"
                onChange={handleFileUpload}
                disabled={uploading}
              />
              <label htmlFor="file-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<AttachFileIcon />}
                  disabled={uploading}
                  sx={{ mb: 2 }}
                >
                  {uploading ? 'Uploading...' : 'Upload Document'}
                </Button>
              </label>
            </Box>
            <Box>
              {attachment && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Uploaded Document:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    <Chip
                      icon={<i className="fa fa-eye" aria-hidden="true"></i>}
                      label={attachment.name}
                      onDelete={handleDeleteAttachment}
                      onClick={() => handlePreview(attachment)}
                      clickable
                      variant="outlined"
                      sx={{ cursor: 'pointer' }}
                    />
                  </Box>
                </Box>
              )}
            </Box>
          </Box>
        </Paper>
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Bonus Attendance History
          </Typography>

          <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell align="center">%</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Attachments</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bonusAttendanceList.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No bonus attendance records found
                    </TableCell>
                  </TableRow>
                ) : (
                  bonusAttendanceList.map((item) => (
                    <TableRow key={item._id}>
                      <TableCell>{item.formattedDate}</TableCell>
                      <TableCell align="center">{item.formattedPercentage}</TableCell>
                      <TableCell>{item.reason}</TableCell>
                      <TableCell>
                        {item.attachment ? (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            <Chip
                              icon={<i className="fa fa-eye" aria-hidden="true"></i>}
                              label={item.attachment.name}
                              onClick={() => handlePreview(item.attachment)}
                              clickable
                              size="small"
                              variant="outlined"
                              sx={{ cursor: 'pointer', fontSize: '0.75rem' }}
                            />
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No attachment
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <IconButton size="small" onClick={() => handleEdit(item)} color="primary">
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(item._id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>

      <FilePreviewDialog
        open={previewDialogOpen}
        onClose={handleClosePreview}
        previewFile={previewFile}
      />
    </Drawer>
  );
};

export default BonusAttendanceDrawer;
