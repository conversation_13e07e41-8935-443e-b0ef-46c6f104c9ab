import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectBasicDetails } from '_reduxapi/global_configuration/selectors';
import { Checkbox, FormControlLabel, FormGroup, IconButton, Menu, MenuItem } from '@mui/material';
import { Trans } from 'react-i18next';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MButton from 'Widgets/FormElements/material/Button';
import VaccineCategoryModal from '../../modal/VaccineCategoryModal';
import VaccineDetailsModal from '../../modal/VaccineDetailsModal';
import { List, Map } from 'immutable';
import DeleteModal from 'Containers/Modal/Delete';
import { t } from 'i18next';
import { AccordionProgramInput } from '../ReusableComponent';
import VaccineDetails from './VaccineDetails';
import { checkBoxStyles } from 'Modules/GlobalConfiguration/utils';

const Vaccination = ({
  settingId,
  getVaccinationDetails,
  basicDetails,
  postVaccineCategory,
  toggleAllowVaccine,
  postVaccinationDetails,
  setAccordionHeader,
  accordionOpen,
  setAccordionOpen,
  setData,
  count,
  institutionHeader,
}) => {
  const [categoryModal, setCategoryModal] = useState(false);
  const [deleteCategoryModal, setDeleteCategoryModal] = useState(false);
  const [detailsModal, setDetailsModal] = useState(false);
  const [deleteDetailsModal, setDeleteDetailsModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState({
    vaccineId: '',
    categoryName: '',
  });
  const [selectedDetails, setSelectedDetails] = useState({
    vaccineId: '',
    vaccineDetailsId: '',
    details: Map(),
  });
  const classes = checkBoxStyles();

  useEffect(() => {
    settingId &&
      accordionOpen &&
      count !== 0 &&
      getVaccinationDetails({ settingId, headers: institutionHeader });
  }, [settingId, accordionOpen, count]); // eslint-disable-line

  const openCategoryModal = (vaccine, type = 'add') => {
    setSelectedCategory({
      vaccineId: vaccine.get('_id', ''),
      categoryName: vaccine.get('categoryName', ''),
    });
    type === 'delete' ? setDeleteCategoryModal(true) : setCategoryModal(true);
  };

  const openDetailsModal = (vaccineId, details, type = 'add') => {
    setSelectedDetails({ vaccineId, vaccineDetailsId: details.get('_id', ''), details });
    type === 'delete' ? setDeleteDetailsModal(true) : setDetailsModal(true);
  };

  const handleVaccineCategory = (operation, formData = {}) => {
    const { vaccineId } = selectedCategory;
    postVaccineCategory({
      operation,
      formData,
      settingId,
      vaccineId,
      callBack: () => {
        operation === 'delete' ? setDeleteCategoryModal(false) : setCategoryModal(false);
      },
      headers: institutionHeader,
    });
  };

  const handleChangeAllowVaccine = (e, vaccineId) => {
    toggleAllowVaccine({
      formData: {
        settingId,
        vaccineId,
        allow: e.target.checked,
      },
      headers: institutionHeader,
    });
  };

  const handleVaccineDetails = (operation, formData = {}) => {
    const { vaccineId, vaccineDetailsId } = selectedDetails;
    postVaccinationDetails({
      operation,
      formData,
      settingId,
      vaccineId,
      vaccineDetailsId,
      callBack: () => {
        operation === 'delete' ? setDeleteDetailsModal(false) : setDetailsModal(false);
      },
      headers: institutionHeader,
    });
  };

  const MenuOptions = ({ data = Map() }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const menuOpen = Boolean(anchorEl);
    const handleMenuClose = () => setAnchorEl(null);

    const handleMenuAction = (type) => {
      handleMenuClose();
      openCategoryModal(data, type);
    };

    return (
      <>
        <IconButton
          aria-label="more"
          aria-controls="long-menu"
          aria-haspopup="true"
          onClick={(e) => setAnchorEl(e.currentTarget)}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={menuOpen}
          onClose={handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: 48 * 4.5,
              width: '20ch',
            },
          }}
        >
          <MenuItem onClick={() => handleMenuAction('edit')}>
            <Trans i18nKey={'edit'} />
          </MenuItem>
          <MenuItem onClick={() => handleMenuAction('delete')}>
            <Trans i18nKey={'delete'} />
          </MenuItem>
        </Menu>
      </>
    );
  };
  MenuOptions.propTypes = {
    data: PropTypes.instanceOf(Map),
  };
  const detailsComponent = () => {
    const vaccinationDetails = basicDetails.get('vaccineConfiguration', List());
    return (
      <div className="container">
        {vaccinationDetails.size ? (
          <>
            {vaccinationDetails.map((item, index) => (
              <div key={index} className="digi-pl-12">
                <div className="d-flex justify-content-between align-items-center">
                  <div className="bold">{item.get('categoryName', '')}</div>
                  <div className="d-flex align-items-center ml-3">
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            color="primary"
                            classes={{ root: classes.root }}
                            checked={item.get('allowMixedVaccine', false)}
                            onChange={(e) => handleChangeAllowVaccine(e, item.get('_id'))}
                          />
                        }
                        label={t('global_configuration.allow_mixed_vaccine')}
                        className="mb-0"
                      />
                    </FormGroup>
                    <MenuOptions data={item} />
                  </div>
                </div>
                <hr className="my-2" />

                {item.get('vaccineDetails', List()).map((details, vdIndex) => (
                  <VaccineDetails
                    key={vdIndex}
                    details={details}
                    openDetailsModal={(type) => openDetailsModal(item.get('_id'), details, type)}
                  />
                ))}

                <MButton
                  variant="text"
                  className="text-blue mt-2 mb-3"
                  clicked={() => openDetailsModal(item.get('_id'), Map())}
                >
                  <Trans i18nKey={'global_configuration.add_new_vaccination'} />
                </MButton>
              </div>
            ))}
            <div className="digi-mx--31">
              <MButton
                variant="outlined"
                className="digi-blue-button"
                clicked={() => openCategoryModal(Map())}
              >
                + <Trans i18nKey={'global_configuration.add_new_vaccine_category'} />
              </MButton>
            </div>
          </>
        ) : (
          <div className="btn-description-border text-center digi-mx--31 mt-2 p-5">
            <MButton variant="outlined" color="gray" clicked={() => openCategoryModal(Map())}>
              <Trans i18nKey={'global_configuration.add_new_vaccine_category'} />
            </MButton>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <AccordionProgramInput
        expanded={accordionOpen || false}
        onClick={setAccordionOpen}
        summaryChildren={setAccordionHeader(
          'global_configuration.vaccination_configuration',
          `${count} ${t('userManagement.configured')}`,
          !accordionOpen
        )}
        detailChildren={detailsComponent()}
      />
      <hr />

      {categoryModal && (
        <VaccineCategoryModal
          show={categoryModal}
          onClose={() => setCategoryModal(false)}
          handleSave={handleVaccineCategory}
          setData={setData}
          {...selectedCategory}
        />
      )}

      {deleteCategoryModal && (
        <DeleteModal
          show={deleteCategoryModal}
          setShow={() => setDeleteCategoryModal(false)}
          title={'userManagement.category'}
          description={'delete_messages.vaccine_category'}
          deleteName={t('userManagement.category')}
          deleteSelected={() => handleVaccineCategory('delete')}
        />
      )}

      {detailsModal && (
        <VaccineDetailsModal
          show={detailsModal}
          onClose={() => setDetailsModal(false)}
          handleSave={handleVaccineDetails}
          setData={setData}
          {...selectedDetails}
        />
      )}

      {deleteDetailsModal && (
        <DeleteModal
          show={deleteDetailsModal}
          setShow={() => setDeleteDetailsModal(false)}
          title={'global_configuration.vaccination_configuration'}
          description={'delete_messages.vaccine_details'}
          deleteName={t('global_configuration.vaccination_configuration')}
          deleteSelected={() => handleVaccineDetails('delete')}
        />
      )}
    </>
  );
};
Vaccination.propTypes = {
  settingId: PropTypes.string,
  getVaccinationDetails: PropTypes.func,
  basicDetails: PropTypes.instanceOf(Map),
  postVaccineCategory: PropTypes.func,
  toggleAllowVaccine: PropTypes.func,
  postVaccinationDetails: PropTypes.func,
  setAccordionHeader: PropTypes.func,
  accordionOpen: PropTypes.bool,
  setAccordionOpen: PropTypes.func,
  setData: PropTypes.func,
  count: PropTypes.number,
  institutionHeader: PropTypes.object,
};

const mapStateToProps = function (state) {
  return {
    basicDetails: selectBasicDetails(state),
  };
};

export default connect(mapStateToProps, actions)(Vaccination);
