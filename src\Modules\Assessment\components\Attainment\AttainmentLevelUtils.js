import React, { useState, useContext } from 'react';
import { fromJS, List, Map } from 'immutable';
import PropTypes from 'prop-types';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useStylesFunction } from '../../css/designUtils';
import { ListItemButton } from '@mui/material';
import {
  Menu,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Divider,
  Typography,
  AccordionSummary,
} from '@mui/material';
import SubdirectoryArrowRightOutlinedIcon from '@mui/icons-material/SubdirectoryArrowRightOutlined';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { ucFirst, indVerRename } from 'utils';

export const optionWithSelect = ({ options, header = 'Select' }) => {
  return [{ name: header, value: '' }, ...options];
};

const levelsArray = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const rangeArray = ['Range', 'Fixed'];
export const AttainmentLevelContext = React.createContext({
  state: {},
  setState: {},
});
const Levels = optionWithSelect({
  options: levelsArray.map((level) => ({ name: level, value: level })),
  header: '---',
});
const ValuesIn = optionWithSelect({
  options: rangeArray.map((range) => ({ name: range, value: range.toLowerCase() })),
  header: '---',
});

const Greater = [
  { name: '≥', value: 'greater_equal' },
  { name: '>', value: 'equal' },
];
export const color = [
  '#DC2626',
  '#D97706',
  '#147AFC',
  '#16A34A',
  '#2626DC',
  '#DC8126',
  ' #26DC81',
  '#f05635',
  '#b3cf19',
  '#a2e946',
  '#72053b',
];
export const getOutComeOptions = (attainmentDetails, programId) => {
  const options = attainmentDetails
    .get('outcomes', List())
    .map((outCome) => ({ name: indVerRename(outCome.toUpperCase(), programId), value: outCome }));
  return optionWithSelect({ options, header: '---' });
};
export const getColor = (levelStart, levelEnd) =>
  Array(levelEnd - levelStart + 1)
    .fill(0)
    .map((_, index) => ({
      level: `Level ${index + Number(levelStart)}`,
      color: color[index + Number(levelStart)],
    }));
const inputProps = {
  elementType: 'materialSelect',
  type: 'text',
  variant: 'outlined',
  size: 'small',
  labelclass: 'mb-0 f-14',
  id: '#bg-white',
};

export const BenchMarkTableHeader = () => (
  <thead>
    <tr>
      <th scope="col" className="borderNone">
        <div className="cw_200">
          <p className="thHeader text-left">Node</p>
        </div>
      </th>
      <th scope="col" className="borderNone">
        <div className="cw_200 custom_space  justify-content-center align-items-center">
          <p className="thHeader mr-2">Set Benchmark</p>
        </div>
      </th>
    </tr>
  </thead>
);
export const LevelTableHeader = () => {
  const { state } = useContext(AttainmentLevelContext);
  const { levelStart, levelEnd } = state;
  const totalLevels = levelEnd - levelStart + 1;
  return (
    <thead>
      <tr>
        <th scope="col" className="borderNone">
          <div className="cw_200">
            <p className="thHeader text-left">Levels</p>
          </div>
        </th>
        {Array(totalLevels)
          .fill(0)
          .map((_, index) => {
            return (
              <th key={index} scope="col" className="borderNone">
                <div
                  className={`${
                    totalLevels <= 3 ? 'w-100' : 'cw_200'
                  } custom_space d-flex justify-content-center align-items-center`}
                >
                  <p className="thHeader mr-2">Level {Number(levelStart) + index}</p>
                  <div className="pt-1">
                    <FiberManualRecordRoundedIcon
                      className="p-0"
                      fontSize={`large`}
                      htmlColor={color[Number(levelStart) + index]}
                    />
                  </div>
                </div>
              </th>
            );
          })}
      </tr>
    </thead>
  );
};
export const LevelDropdown = () => {
  const { state, setState } = useContext(AttainmentLevelContext);
  const { levelStart, levelEnd, valuesIn } = state;
  const { setValuesIn, setLevelEnd, setLevelStart, setConstructedData, showError } = setState;
  const onValueInChange = (e) => {
    setValuesIn(e.target.value);
  };
  const onLevelEndChange = (e) => {
    const value = Number(e.target.value);
    if (levelStart === '') return showError('Select Level Start');
    if (value < levelStart)
      return showError('Levels End should be greater or equal to Levels Start');
    setLevelEnd(value);
  };
  const setDataTemplate = (data) =>
    data.map((item) => {
      item = item.set('levelData', List()).set('benchMark', '');
      if (item.get('children', List()).size === 0) return item;
      return item.set('children', setDataTemplate(item.get('children', List())));
    });
  const onLevelStartChange = (e) => {
    setLevelStart(Number(e.target.value));
    if (levelEnd !== '') {
      setLevelEnd('');
      setConstructedData((previouslySelected) => setDataTemplate(previouslySelected));
    }
  };
  return (
    <div className="d-flex pb-4">
      <div className="mr-4">
        <MaterialInput
          label={'Levels Start'}
          elementConfig={{ options: Levels }}
          value={levelStart}
          changed={onLevelStartChange}
          {...inputProps}
        />
      </div>
      <div className="mr-3">
        <MaterialInput
          elementConfig={{ options: Levels }}
          label={'Levels End'}
          value={levelEnd}
          changed={onLevelEndChange}
          {...inputProps}
        />
      </div>

      <div className="pl-4 pr-5 border-left">
        <MaterialInput
          label={'Values in'}
          elementConfig={{ options: ValuesIn }}
          value={valuesIn}
          changed={onValueInChange}
          {...inputProps}
        />
      </div>
      <SelectNode />
    </div>
  );
};
export const SelectNode = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const { state, setState } = useContext(AttainmentLevelContext);
  const { constructedData } = state;
  const { setConstructedData } = setState;
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const ITEM_HEIGHT = 40;
  //const [open1] = useState(true);
  const onSelectNode = (isSelected, index, node) => {
    const isChecked = !isSelected;
    const parentName = node.get('parentName', '');
    if (parentName === 'startingLevel' || parentName === 'nodeAtStarting') {
      if (isChecked)
        setConstructedData((previouslySelected) =>
          previouslySelected
            .setIn([index, 'isSelected'], isChecked)
            .setIn([index, 'showBenchmarkChildren'], isChecked)
            .setIn([index, 'showLevelChildren'], isChecked)
        );
      else
        setConstructedData((previouslySelected) =>
          previouslySelected
            .setIn([index, 'isSelected'], isChecked)
            .setIn([index, 'showBenchmarkChildren'], isChecked)
            .setIn([index, 'showLevelChildren'], isChecked)
            .setIn(
              [index, 'children'],
              previouslySelected.getIn([index, 'children'], List()).map((item) =>
                item
                  .set('isSelected', false)
                  .set('showLevelChildren', false)
                  .set('showBenchmarkChildren', false)
                  .set(
                    'children',
                    item
                      .get('children', List())
                      .map((sItem) => sItem.set('isSelected', false))
                      .set('showLevelChildren', false)
                      .set('showBenchmarkChildren', false)
                  )
              )
            )
        );
      return;
    }
    if (parentName === 'tree') {
      const getSetData = (key) => [node.get('parentIndex', ''), 'children', index, key];
      if (isChecked) {
        setConstructedData((previouslySelected) =>
          previouslySelected
            .setIn(getSetData('isSelected'), isChecked)
            .setIn(getSetData('showLevelChildren'), isChecked)
            .setIn(getSetData('showBenchmarkChildren'), isChecked)
        );
      } else
        setConstructedData((previouslySelected) =>
          previouslySelected
            .setIn(getSetData('isSelected'), isChecked)
            .setIn(getSetData('showLevelChildren'), isChecked)
            .setIn(getSetData('showBenchmarkChildren'), isChecked)
            .setIn(
              [node.get('parentIndex', ''), 'children', index, 'children'],
              previouslySelected
                .getIn([node.get('parentIndex', ''), 'children', index, 'children'], List())
                .map((item) =>
                  item
                    .set('isSelected', false)
                    .set('showLevelChildren', false)
                    .set('showBenchmarkChildren', false)
                )
            )
        );
      return;
    }
    if (parentName === 'subTree') {
      const getSetData = (key) => [
        node.get('grandParentIndex', ''),
        'children',
        node.get('parentIndex', ''),
        'children',
        index,
        key,
      ];
      setConstructedData((previouslySelected) =>
        previouslySelected
          .setIn(getSetData('isSelected'), isChecked)
          .setIn(getSetData('showLevelChildren'), isChecked)
          .setIn(getSetData('showBenchmarkChildren'), isChecked)
      );
    }
  };
  const showOptions = (index, currentChild) => {
    let pl = 2;
    if (currentChild.get('parentName', '') === 'tree') pl = 4;
    if (currentChild.get('parentName', '') === 'subTree') pl = 8;
    return (
      <React.Fragment key={index}>
        <ListItemButton
          sx={{ pl }}
          onClick={() => onSelectNode(currentChild.get('isSelected', false), index, currentChild)}
        >
          <ListItemIcon className="minWidth-32">
            <Checkbox
              edge="start"
              checked={currentChild.get('isSelected', false)}
              tabIndex={-1}
              disableRipple
              color="primary"
              disabled={currentChild.get('name', '') === 'Overall Attainment'}
            />
          </ListItemIcon>
          <ListItemText primary={getShortString(ucFirst(currentChild.get('name', '')), 20)} />
        </ListItemButton>
        <Divider />
        {currentChild.get('isSelected', false) &&
          currentChild.get('currentLevelName', '') !== 'node' &&
          currentChild.get('children', List()).map((sItem, sIndex) => showOptions(sIndex, sItem))}
      </React.Fragment>
    );
  };
  const getCount = () => {
    let count = 0;
    constructedData.map((item) => {
      if (item.get('isSelected', false)) count += 1;
      item.get('children', List()).map((sItem) => {
        if (sItem.get('isSelected', false)) count += 1;
        sItem.get('children', List()).map((dItem) => {
          if (dItem.get('isSelected', false)) count += 1;
          return '';
        });
        return '';
      });
      return '';
    });
    return count;
  };
  const selectedCount = getCount(constructedData);
  return (
    <div className="ml-auto mr-2 mt-4 ">
      <Typography
        component="div"
        className="remove_hover digi-blue-bg border-radious-4"
        aria-label="more"
        aria-controls="long-menu"
        aria-haspopup="true"
        onClick={handleClick}
      >
        <div className="d-flex justify-content-between p-2 digi-color-white">
          <p className="mb-0 pl-2 f-17 digi-color-white">Select Node </p>
          <div className="digi-circle ml-2 ">{selectedCount}</div>
          <AccountTreeIcon className="digi-color-white ml-4" />
        </div>
      </Typography>

      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        keepMounted
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            maxHeight: ITEM_HEIGHT * 10.5,
            width: '28.2ch',
          },
        }}
      >
        <div>{constructedData.map((item, index) => showOptions(index, item))}</div>
      </Menu>
    </div>
  );
};

export const AccordionTile = ({ title }) => {
  const classes = useStylesFunction();
  return (
    <AccordionSummary
      expandIcon={<ExpandMoreIcon fontSize={`large`} className="m-0 p-0" />}
      aria-controls="panel1bh-content"
      id="panel1bh-header"
      className={classes.accordionArrowPosition}
    >
      <p className="mb-0 bold f-17">{title}</p>
    </AccordionSummary>
  );
};
AccordionTile.propTypes = {
  title: PropTypes.string,
};

export const setValueInState = (index, node, type, setConstructedData, event) => {
  const getValue = (previouslySelected, data) =>
    type === 'benchMark' ? event.target.value : !previouslySelected.getIn(data, true);
  if (
    node.get('parentName', '') === 'startingLevel' ||
    node.get('parentName', '') === 'Overall Attainment' ||
    node.get('parentName', '') === 'nodeAtStarting'
  ) {
    let data = [index, type];
    setConstructedData((previouslySelected) =>
      previouslySelected.setIn(data, getValue(previouslySelected, data))
    );
    return;
  }
  if (node.get('parentName', '') === 'tree') {
    let data = [node.get('parentIndex', ''), 'children', index, type];
    setConstructedData((previouslySelected) =>
      previouslySelected.setIn(data, getValue(previouslySelected, data))
    );
    return;
  }
  if (node.get('parentName', '') === 'subTree') {
    let data = [
      node.get('grandParentIndex', ''),
      'children',
      node.get('parentIndex', ''),
      'children',
      index,
      type,
    ];
    setConstructedData((previouslySelected) =>
      previouslySelected.setIn(data, getValue(previouslySelected, data))
    );
    return;
  }
};

export const FirstColumn = ({ index, item, showChildren, setConstructedData }) => {
  return (
    <th scope="col" className="attainment_border_bottom bg-white">
      <div
        className="d-flex justify-content-between custom_space align-items-center remove_hover"
        onClick={() => setValueInState(index, item, showChildren, setConstructedData)}
      >
        <Title item={item} showChildren={showChildren} />
      </div>
    </th>
  );
};
const Title = ({ item, showChildren }) => {
  let paddingLeft = '';
  if (item.get('parentName', '') === 'tree') paddingLeft = 'pl-3';
  if (item.get('parentName', '') === 'subTree') paddingLeft = 'pl-5';
  return (
    <>
      <div className={`d-flex align-items-center ${paddingLeft}`}>
        {item.get('name', '') !== 'Overall Attainment' && (
          <SubdirectoryArrowRightOutlinedIcon className="mr-2 p-0 text-gray" fontSize={`small`} />
        )}
        <p className="thHeader text-left"> {getShortString(ucFirst(item.get('name', '')), 20)}</p>
      </div>
      {item.get('currentLevelName', '') !== 'node' && (
        <ExpandMoreIcon
          fontSize={`medium`}
          className={`m-0 p-0 text-skyblue  ${item.get(showChildren, false) && 'rotate_180'}`}
        />
      )}
    </>
  );
};
FirstColumn.propTypes = {
  item: PropTypes.instanceOf(Map),
  showChildren: PropTypes.string,
  index: PropTypes.number,
  setConstructedData: PropTypes.func,
};
Title.propTypes = {
  item: PropTypes.instanceOf(Map),
  showChildren: PropTypes.string,
};
const onLevelDataChange = (e, props, inputIndex, type) => {
  const { index, item, setConstructedData, levelStart, showError } = props;
  const value = e.target.value;
  const convertedValue = type !== 'condition' ? Number(value) : value;
  if (value !== '' && type !== 'condition' && isNaN(convertedValue))
    return showError('Enter Numbers only');
  if (convertedValue > 100) return showError('Enter Numbers between 0-100');
  const parentName = item.get('parentName', '');
  const getPreviousValue = (previouslySelected, data) => {
    let value = previouslySelected.getIn(data, Map());
    return value ? value : Map();
  };

  if (
    parentName === 'startingLevel' ||
    parentName === 'Overall Attainment' ||
    parentName === 'nodeAtStarting'
  ) {
    let data = [index, 'levelData', inputIndex];

    setConstructedData((previouslySelected) =>
      previouslySelected.setIn(
        data,
        getPreviousValue(previouslySelected, data)
          .set('level', `Level ${inputIndex + Number(levelStart)}`)
          .set(type, convertedValue)
      )
    );
    return;
  }
  if (parentName === 'tree') {
    let data = [item.get('parentIndex', ''), 'children', index, 'levelData', inputIndex];
    setConstructedData((previouslySelected) =>
      previouslySelected.setIn(
        data,
        getPreviousValue(previouslySelected, data)
          .set('level', `Level ${inputIndex + Number(levelStart)}`)
          .set(type, convertedValue)
      )
    );
    return;
  }
  if (parentName === 'subTree') {
    let data = [
      item.get('grandParentIndex', ''),
      'children',
      item.get('parentIndex', ''),
      'children',
      index,
      'levelData',
      inputIndex,
    ];
    setConstructedData((previouslySelected) =>
      previouslySelected.setIn(
        data,
        getPreviousValue(previouslySelected, data)
          .set('level', `Level ${inputIndex + Number(levelStart)}`)
          .set(type, convertedValue)
      )
    );
    return;
  }
};

export const LevelInput = (props) => {
  const { valuesIn, levelEnd, levelStart, item } = props;
  const isRange = valuesIn === 'range';
  let Props = {};
  const firstType = isRange ? 'min' : 'condition';
  const secondType = isRange ? 'max' : 'percentage';
  const inputType = isRange ? 'materialInput' : 'materialSelect';
  if (isRange) Props = { placeholder: '---' };
  else Props = { id: '#attainmentSelect', elementConfig: { options: Greater } };
  return Array(levelEnd - levelStart + 1)
    .fill(0)
    .map((_, inputIndex) => {
      return (
        <td key={inputIndex} className="attainment_border_bottom">
          <div className="row custom_space">
            <div className="col-md-6">
              <MaterialInput
                elementType={inputType}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                value={item.getIn(['levelData', inputIndex, firstType], '')}
                changed={(e) => onLevelDataChange(e, props, inputIndex, firstType)}
                {...Props}
              />
            </div>
            <div className="col-md-6">
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                placeholder={'---'}
                value={item.getIn(['levelData', inputIndex, secondType], '')}
                changed={(e) => onLevelDataChange(e, props, inputIndex, secondType)}
              />
            </div>
          </div>
        </td>
      );
    });
};
export const RangeRow = (props) => {
  return (
    <tr>
      <th scope="col" className="borderNone bg-gray">
        <div className="custom_space">
          <p className="thHeader text-left">Range</p>
        </div>
      </th>
      <RangeRemainingColumn {...props} />
    </tr>
  );
};
const MinMax = (
  <>
    <div className="col-md-6">
      <p className="thHeaderAttainment f-15">Min%</p>
    </div>
    <div className="col-md-6">
      <p className="thHeaderAttainment f-15">Max%</p>
    </div>
  </>
);
const Percent = (
  <div className="col-md-12">
    <p className="d-flex pt-2 justify-content-center align-items-center f-15">Perc %</p>
  </div>
);
const RangeRemainingColumn = ({ levelStart, levelEnd, valuesIn }) => {
  return Array(levelEnd - levelStart + 1)
    .fill(0)
    .map((_, index) => {
      return (
        <td key={index} className="borderNone bg-gray">
          <div className="row custom_space">{valuesIn === 'range' ? MinMax : Percent}</div>
        </td>
      );
    });
};

export const constructRequest = (item, requestKey, requiredData) => {
  const subBranch = () =>
    item.get('children', List()).map((item) => constructRequest(item, requestKey, requiredData));
  const isSelected = (key) => item.get('parentName') === key && item.get('isSelected');
  const isNodeAtSubtree = item.get('nodeId', '') !== '';
  const defaultData = {
    typeName: item.get('name', ''),
    ...(item.get('name') !== 'Overall Attainment' && { typeId: item.get('typeId', '') }),
    ...(requestKey === 'levels' && { levelValues: getLevelList(item, requiredData) }),
    ...(requestKey === 'targetBenchMark' && { benchMark: item.get('benchMark', '') }),
  };
  if (item.get('parentName') === 'Overall Attainment') return defaultData;

  if (isSelected('startingLevel')) {
    return {
      ...defaultData,
      subTree: subBranch().filter((item) => item),
    };
  }
  if (isSelected('tree') && !isNodeAtSubtree) {
    return { ...defaultData, node: subBranch().filter((item) => item) };
  }
  if (
    isSelected('subtree') ||
    isSelected('nodeAtStarting') ||
    (isNodeAtSubtree && item.get('isSelected'))
  ) {
    return {
      ...defaultData,
      typeName: item.get('typeName', ''),
      nodeName: item.get('name', ''),
      nodeId: item.get('nodeId', ''),
    };
  }
};

const getLevelList = (item, requiredData) => {
  const { valuesIn, levelStart, levelEnd } = requiredData;
  const lastIndex = levelEnd - levelStart;
  const firstType = valuesIn === 'range' ? 'min' : 'condition';
  const secondType = valuesIn === 'range' ? 'max' : 'percentage';
  return item
    .get('levelData', List())
    .filter((_, index) => index <= lastIndex)
    .map((sItem) => {
      if (!sItem) return {};
      return {
        level: sItem.get('level', ''),
        [firstType]: sItem.get(firstType, firstType === 'min' ? '' : 'greater_equal'),
        [secondType]: sItem.get(secondType, ''),
      };
    });
};

export const getConstructData = (item, indexes, currentKey, parentName) => {
  const defaultData = {
    currentLevelName: currentKey,
    name: item.get('typeName', ''),
    typeId: item.get('typeId', ''),
    showLevelChildren: false,
    showBenchmarkChildren: false,
    levelData: List(),
    benchMark: '',
    isSelected: false,
  };
  if (currentKey === 'tree') {
    const { index } = indexes;
    return fromJS({
      ...defaultData,
      parentName: 'startingLevel',
      children: item
        .get('subTree', List())
        .map((sItem, sIndex) =>
          getConstructData(sItem, { index, sIndex }, getKey(sItem), currentKey)
        ),
    });
  }
  if (currentKey === 'subTree') {
    const { index, sIndex } = indexes;
    return fromJS({
      ...defaultData,
      parentIndex: index + 1,
      parentName,
      children: item
        .get('node', List())
        .map((sItem) => getConstructData(sItem, { index, sIndex }, 'node', currentKey)),
    });
  }
  if (currentKey === 'node') {
    const { index, sIndex } = indexes;
    return fromJS({
      ...defaultData,
      parentName: parentName ? parentName : 'nodeAtStarting',
      parentIndex: parentName === 'subTree' ? sIndex : index + 1,
      grandParentIndex: parentName === 'subTree' ? index + 1 : null,
      name: item.get('nodeName', ''),
      typeName: item.get('typeName', ''),
      nodeId: item.get('_id', ''),
    });
  }
};

const getSelectedAttainment = (attainmentDetails, key, outCome) =>
  attainmentDetails
    .get(key, List())
    .filter((item) => item.get('outComeType', '') === outCome)
    .get(0, Map());

const getKey = (item) => {
  let key = 'tree';
  if (item.has('node')) key = 'subTree';
  if (item.get('nodeName', '') !== '') key = 'node';
  return key;
};

export const getDataFromEvaluationPlan = (attainmentDetails, outCome) =>
  getSelectedAttainment(attainmentDetails, 'evaluationPlan', outCome)
    .get('tree', List())
    .map((item, index) => getConstructData(item, { index }, getKey(item)));

const getFilterTypeId = (value, item, type) =>
  value
    .map((vItem) =>
      vItem.get(type, List()).filter((sItem) => sItem.get('typeId', '') === item.get('typeId', ''))
    )
    .filter((item) => item.size)
    .get(0, List());

export const getDataFromAttainmentLevel = (attainmentDetails, outCome, type) => {
  const options = getDataFromEvaluationPlan(attainmentDetails, outCome);
  let data = options.unshift(overAllAttainment);
  const selectedAttainment = getSelectedAttainment(attainmentDetails, 'attainmentLevel', outCome);
  let start = selectedAttainment.get('start', 0);
  let end = selectedAttainment.get('end', 0);
  let valuesAs = selectedAttainment.get('valuesAs', '');
  let constructData = data.map((item) => {
    if (item.get('name', '') === 'Overall Attainment') {
      item = item
        .set('levelData', selectedAttainment.getIn(['levels', '0', 'levelValues']))
        .set('benchMark', selectedAttainment.getIn(['targetBenchMark', '0', 'benchMark']));
      return item;
    }
    let { NewItem, isSelected, valueData, benchMark } = getSelected(selectedAttainment, item, type);
    if (!isSelected && NewItem.get('children', List()).size !== 0) return NewItem;
    NewItem = NewItem.set(
      'children',
      getChildren(NewItem.get('children', List()), valueData, benchMark, 'subTree', type)
    );
    return NewItem;
  });
  return { constructData, start, end, valuesAs };
};

const getChildren = (children, value, mark, type, attainmentType) => {
  return children.map((item) => {
    let { NewItem, isSelected, valueData, benchMark } = getSelected(
      fromJS({
        levels: getFilterTypeId(value, item, type),
        targetBenchMark: getFilterTypeId(mark, item, type),
      }),
      item,
      attainmentType
    );
    if (!isSelected && NewItem.get('children', List()).size === 0) return NewItem;
    NewItem = NewItem.set(
      'children',
      getChildren(NewItem.get('children', List()), valueData, benchMark, 'node')
    );
    return NewItem;
  });
};

const checkAttainmentType = (valueData, benchMark, type) => {
  return type === 'course' ? valueData.size !== 0 : valueData.size !== 0 && benchMark.size !== 0;
};

const getSelected = (selectedAttainment, item, type) => {
  let valueData = selectedAttainment
    .get('levels', List())
    .filter((fItem) => item.get('typeId', '') === fItem.get('typeId', ''));
  let benchMark = selectedAttainment
    .get('targetBenchMark', List())
    .filter((fItem) => item.get('typeId', '') === fItem.get('typeId', ''));
  const isSelected = checkAttainmentType(valueData, benchMark, type);
  let NewItem = item
    .set('isSelected', isSelected)
    .set('showLevelChildren', isSelected)
    .set('showBenchmarkChildren', isSelected)
    .set('levelData', valueData.getIn([0, 'levelValues'], List()))
    .set('benchMark', benchMark.getIn([0, 'benchMark'], ''));
  return { NewItem, isSelected, valueData, benchMark };
};

export const getIdFromAttainmentLevel = (attainmentDetails, outCome) =>
  getSelectedAttainment(attainmentDetails, 'attainmentLevel', outCome).get('_id', '');

export const noValuesInAttainment = (attainmentDetails, outCome, type) => {
  const selectedOutCome = getSelectedAttainment(attainmentDetails, 'attainmentLevel', outCome);
  const checkDetails =
    selectedOutCome.get('nodes', List).size === 0 &&
    selectedOutCome.get('levels', List).size === 0 &&
    selectedOutCome.get('levelColors', List).size === 0;
  if (type === 'course') {
    return checkDetails;
  } else {
    return checkDetails && selectedOutCome.get('targetBenchMark', List).size === 0;
  }
};

export let overAllAttainment = fromJS({
  parentName: 'Overall Attainment',
  name: 'Overall Attainment',
  isSelected: true,
  showLevelChildren: true,
  showBenchmarkChildren: true,
  levelData: List(),
  benchMark: '',
});

const filterOnlySelected = (item) => item.get('isSelected', false);

const isDataEmpty = (item, key) => (item ? item.get(key, '') === '' : true);

const atValue = (item, index) => `at "Level ${index}" on "${item.get('name', '')}"`;
const getMessage = (data, item, index) => `${data} is empty at ${atValue(item, index)}`;

const isLevelValid = (item, requiredData) => {
  const { valuesIn, levelStart, levelEnd } = requiredData;
  const totalLevels = levelEnd - levelStart + 1;
  if (item.get('levelData', List()).size === 0)
    return { isNotValid: true, message: `Level value is empty at "${item.get('name', '')}"` };
  if (item.get('levelData', List()).size < totalLevels)
    return { isNotValid: true, message: `Level Data is incomplete` };

  let validationMessage = '';
  let range = [];
  const inCompleteData = item.get('levelData', List()).some((sItem, sIndex) => {
    if (valuesIn === 'fixed') {
      if (isDataEmpty(sItem, 'percentage')) {
        validationMessage = getMessage('Percentage', item, levelStart + sIndex);
        return true;
      }
      if (range.includes(sItem.get('percentage', ''))) {
        validationMessage = `percentage is repeated on "${item.get('name', '')}"`;
        return true;
      }
      range.push(sItem.get('percentage', ''));
    }

    if (valuesIn === 'range') {
      if (isDataEmpty(sItem, 'min')) {
        validationMessage = getMessage('Min', item, levelStart + sIndex);
        return true;
      }
      if (isDataEmpty(sItem, 'max')) {
        validationMessage = getMessage('Max', item, levelStart + sIndex);
        return true;
      }
      if (sItem.get('min', '') > sItem.get('max', '')) {
        validationMessage = `Max is less than Min ${atValue(item, levelStart + sIndex)}`;
        return true;
      }
      if (range.length === 0) {
        range.push([sItem.get('min', ''), sItem.get('max', '')]);
        return false;
      }
      let ValueExist = range.some(
        (item) =>
          (item[0] <= sItem.get('min', '') && item[1] >= sItem.get('min', '')) ||
          (item[0] <= sItem.get('max', '') && item[1] >= sItem.get('max', ''))
      );
      if (ValueExist) {
        validationMessage = `Range already existed`;
        return true;
      }
      range.push([sItem.get('min', ''), sItem.get('max', '')]);
    }
    return false;
  });
  return { isNotValid: inCompleteData, message: validationMessage };
};

const checkCoPoTargetIsEmpty = (manageTargetBenchMark) => {
  return manageTargetBenchMark.every((s) => s.get('values', ''));
};

const checkConditions = (item, requiredData, targetAttainment, manageTargetBenchMark) => {
  const { isNotValid, message } = isLevelValid(item, requiredData);

  if (isNotValid) return { isNotValid, message };
  if (targetAttainment === 'assessment') {
    if (isDataEmpty(item, 'benchMark'))
      return { isNotValid: true, message: `Benchmark is empty at "${item.get('name', '')}"` };
  }
  if (['clo', 'plo'].includes(targetAttainment)) {
    if (!checkCoPoTargetIsEmpty(manageTargetBenchMark))
      return { isNotValid: true, message: `Benchmark is empty at "${targetAttainment}"` };
  }

  if (item.get('children', List()).size) {
    const { inCompleteData, validationMessage } = validation(
      item.get('children', List()),
      requiredData
    );
    return { isNotValid: inCompleteData, message: validationMessage };
  }
  return { isNotValid: false, message: '' };
};

export const validation = (data, requiredData, targetAttainment, manageTargetBenchMark) => {
  let validationMessage;
  let inCompleteData = data.filter(filterOnlySelected).some((item) => {
    const { isNotValid, message } = checkConditions(
      item,
      requiredData,
      targetAttainment,
      manageTargetBenchMark
    );
    validationMessage = message;
    return isNotValid;
  });
  return { inCompleteData, validationMessage };
};

export const getData = (constructedData, key, requiredData) =>
  constructedData
    .map((item) => constructRequest(item, key, requiredData))
    .filter((item) => item)
    .toJS();
