import React, { useEffect, useState } from 'react';
import { PropTypes } from 'prop-types';
import { List, Map, fromJS } from 'immutable';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';

import settings from 'Assets/settings.svg';
import courseSpec from 'Assets/courseSpec.svg';
import courseSpecActive from 'Assets/courseSpecActive.svg';
import courseReport from 'Assets/courseReport.svg';
import courseReportActive from 'Assets/courseReportActive.svg';
import programSpec from 'Assets/programSpec.svg';
import programSpecActive from 'Assets/programSpecActive.svg';
import programReport from 'Assets/programReport.svg';
import programReportActive from 'Assets/programReportActive.svg';
import archive from 'Assets/archive.svg';
import deleteIcon from 'Assets/delete.svg';
import folderEmpty from 'Assets/folderEmpty.svg';
import staffName from 'Assets/staffName.svg';
import date_range from 'Assets/date_range.svg';
import courseLevel from 'Assets/courseLevel.svg';

// import AddIcon from '@mui/icons-material/Add';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Box from '@mui/material/Box';
import Checkbox from '@mui/material/Checkbox';
import Divider from '@mui/material/Divider';
import FormControlLabel from '@mui/material/FormControlLabel';
import Paper from '@mui/material/Paper';
import { makeStyles } from '@mui/styles';
import { styled } from '@mui/material/styles';
import DeleteIcon from '@mui/icons-material/Delete';
import Category from '@mui/icons-material/Category';
import InfoIcon from '@mui/icons-material/Info';
import CircleIcon from '@mui/icons-material/Circle';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import OutlinedInput from '@mui/material/OutlinedInput';
import SearchIcon from '@mui/icons-material/Search';
import { InputAdornment } from '@mui/material';

import {
  getCategoryForms,
  updateDuplicateForm,
  setData,
} from '_reduxapi/global_configuration/v1/actions';
import { selectCategoryForm } from '_reduxapi/global_configuration/v1/selectors';

import Button from 'Widgets/FormElements/material/Button';
import CustomizedSteppers from './stepper';
import { useSearchParams } from '../QaPcConfiguration';
import ConfigureModal from '../Models/CourseSpecificationConfigure';
import DefaultForm from './DefaultForm';
import { jsUcfirstAll } from 'utils';
import { useConfigureTemplate } from '../utils';
import EditCategoryForm from './CategoryFormCrud/EditCategoryForm';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';

const configureImg = {
  'Course Specification+false': courseSpec,
  'Course Reports+false': courseReport,
  'Program Specification+false': programSpec,
  'Program Reports+false': programReport,
  'Course Specification+true': courseSpecActive,
  'Course Reports+true': courseReportActive,
  'Program Specification+true': programSpecActive,
  'Program Reports+true': programReportActive,
  archive: archive,
  delete: deleteIcon,
};
export const circleSX = { fontSize: 6, color: 'grey' };

export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '14px', // Adjust the font size as needed
    // You can also add other styles here, such as fontFamily, fontWeight, etc.
  },
}));

export default function ConfigureTemplate() {
  const [searchParams] = useSearchParams();
  const currentCategoryIndex = Number(searchParams.get('currentCategoryIndex') ?? 0);
  const history = useHistory();
  const onClickSettings = () => {
    history.push(
      `/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration+configure_template+settings`
    );
  };
  const [configureTemplate] = useConfigureTemplate();
  const onCategoryClick = (index) => () => {
    history.push(
      `/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration+configure_template&currentCategoryIndex=` +
        index
    );
  };

  return (
    <>
      <section
        className="d-flex align-items-center cursor-pointer setting_icon_position"
        onClick={onClickSettings}
      >
        <img src={settings} alt="settings" className="ml-auto mr-5" />
      </section>
      <section className="">
        <main className="text-dGrey grid-container">
          <aside className="p-3 bg-color-darkGray">
            <div className="pl-2">
              <OutlinedInput
                id="outlined-adornment-weight"
                placeholder={'Search for a form'}
                startAdornment={
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: '#9CA3AF', fontSize: '16px' }} />
                  </InputAdornment>
                }
                aria-describedby="outlined-weight-helper-text"
                inputProps={{
                  'aria-label': 'weight',
                }}
                sx={{
                  width: '100%',
                  height: '45px',
                  fontSize: '14px',
                  background: 'white',
                  color: '#9CA3AF',
                }}
              />
            </div>
            <Divider className="py-2" />
            <section className="d-flex py-3">
              <div className="flex-grow-1 f-14">Categories</div>
              {/* <Paper className="d-flex" sx={{ borderRadius: '50px' }} elevation={0}>
                <AddIcon color="primary" sx={{ padding: '3px' }} />
              </Paper>{' '}
              <span className="text-primary pl-2 f-12 fw-400">New Categories</span> */}
            </section>
            {configureTemplate.map((category, index) => {
              const isCategorySelected = currentCategoryIndex === index;
              return (
                <Paper
                  key={category.get('_id', index)}
                  className={`${
                    !isCategorySelected ? ' bg-transparent ' : 'bg-white'
                  } cursor-pointer mt-2  categories-hover `}
                  // elevation={isCategorySelected ? 1 : 0}
                  onClick={onCategoryClick(index)}
                  sx={{
                    borderRadius: '0px !important',
                    boxShadow: 'none',
                    background: 'none',
                  }}
                >
                  <section
                    className={`${isCategorySelected ? 'bl-2 border-primary' : ''} d-flex p-2`}
                  >
                    <span className="fw-400 flex-grow-1 f-12">
                      <img
                        src={
                          configureImg[`${category.get('categoryName', '')}+${isCategorySelected}`]
                        }
                        alt="courseSpec"
                      />{' '}
                      {category.get('categoryName', '')}
                    </span>
                    <div className="text-grey f-10">DEFAULT</div>
                  </section>
                </Paper>
              );
            })}
          </aside>

          <section className="bg-lite-greenShade  border-left p-3 scroll">
            <Categories category={configureTemplate.get(currentCategoryIndex, Map())} />
          </section>
        </main>
      </section>
    </>
  );
}

const FolderEmpty = ({ category }) => {
  const [open, setOpen] = useState(false);
  const handleOpenOrClose = () => setOpen((prev) => !prev);
  const lowerName = category.get('categoryName', '').toLowerCase();
  const level = lowerName.includes('course')
    ? 'course'
    : lowerName.includes('program')
    ? 'program'
    : 'institution';
  return (
    <main>
      <section className="d-flex">
        <div className="flex-grow-1 f-24 text-capitalize">{category.get('categoryName', '')} </div>
        <div
          className="text_underline text-primary f-14 cursor-pointer"
          onClick={handleOpenOrClose}
        >
          Configure
        </div>
      </section>
      <Divider className="py-2" />
      <section className="text-center pt-3">
        <img src={folderEmpty} alt="folderEmpty" />
        <div className="f-36 pt-2">This folder is empty.</div>
        <div className="f-16 text-grey">Start a new form to fill up this folder.</div>
      </section>
      {open && (
        <ConfigureModal
          category={category.set('level', level)}
          open={open}
          handleClose={handleOpenOrClose}
        />
      )}
    </main>
  );
};

FolderEmpty.propTypes = {
  category: PropTypes.instanceOf(Map),
};

const Categories = ({ category }) => {
  const constructedCategory = category.update('attemptType', List(), (attemptType) => {
    return attemptType.map((att) => att.get('attemptId', ''));
  });
  if (!category.get('isConfigure', false)) {
    return <FolderEmpty category={constructedCategory} />;
  }
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();
  const history = useHistory();
  const routeToCreateSettings = ({ categoryFormName, categoryFormIndex, categoryFormId, step }) => {
    const newQuery = `${searchParams.get('query')?.split(' ').join('+')}+${categoryFormName
      .split(' ')
      .join(',')}`;
    searchParams.delete('query');
    const updatedSearch = searchParams.toString();
    const updatedUrl = `/globalConfiguration-v1/qa_pc_configuration?${updatedSearch}&query=${newQuery}&categoryFormIndex=${categoryFormIndex}&categoryFormId=${categoryFormId}&step=${
      step - 1
    }&categoryId=${category.get('_id', '')}`;
    history.push(updatedUrl);
  };
  const categoryForms = useSelector(selectCategoryForm);
  useEffect(() => {
    const categoryId = category.get('_id', '');
    if (categoryId)
      dispatch(
        getCategoryForms({
          params: {
            categoryId,
          },
        })
      );
  }, [category]);
  return (
    <main>
      <Header category={constructedCategory} />
      <Divider className="py-2" />
      <DefaultForm category={category} />
      <Divider className="py-2" />
      {categoryForms.map((categoryForm, categoryIndex) => (
        <FormData
          key={categoryForm.get('', '')}
          categoryForms={categoryForms}
          dispatch={dispatch}
          index={categoryIndex}
          categoryForm={categoryForm}
          routeToCreateSettings={routeToCreateSettings}
        />
      ))}
    </main>
  );
};

Category.propTypes = {
  category: PropTypes.instanceOf(Map),
};
const Header = ({ category }) => {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  function handleConfigured() {
    setOpen((prev) => !prev);
  }
  return (
    <header className="d-flex align-items-center">
      <section className="flex-grow-1">
        <div className="f-26 d-flex align-items-center">
          {category.get('categoryName', '')}
          <span
            onClick={handleConfigured}
            className="text_underline text-primary f-12 cursor-pointer fw-500  rounded p-1 bgLight ml-2"
          >
            Configured
          </span>
        </div>
        <div className="d-flex align-items-center gap-8 text-lGrey f-14 fw-400">
          <img src={staffName} alt={'staffName'} />
          <span className="text-capitalize">
            {' '}
            {category.getIn(['createdBy', 'name', 'first'], '') +
              ' ' +
              category.getIn(['createdBy', 'name', 'last'], '')}
          </span>
          <CircleIcon sx={circleSX} />
          <img src={date_range} alt={'date_range'} />
          <span>
            {moment(category.get('updatedAt', new Date())).format('DD MMM YYYY - HH:MM A')}
          </span>
          <CircleIcon sx={circleSX} />
          <img src={courseLevel} alt={'courseLevel'} />
          <span>Level: {jsUcfirstAll(category.get('level', 'course'))}</span>
          <CircleIcon sx={circleSX} />
          <span>
            Category For: {category.get('categoryFor', '') === 'annual' ? 'Annual' : 'Periodic'}{' '}
          </span>
        </div>
        <div className="d-flex p-1 ">
          <FormControlLabel
            control={
              <Checkbox
                className="py-0"
                checked={category.get('incorporateMandatory', false)}
                color="default"
                size="small"
                sx={{
                  '& .MuiSvgIcon-root': {
                    width: '15px',
                    height: '15px',
                    color: '#9CA3AF',
                  },
                }}
              />
            }
            label="Incorporate details as mandatory."
            className="mb-0 text-lGrey digi-pointer-events-none"
            classes={{ label: classes.label }}
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={category.get('displayMandatory', false)}
                className="py-0"
                color="default"
                size="small"
                sx={{
                  '& .MuiSvgIcon-root': {
                    width: '15px',
                    height: '15px',
                    color: '#9CA3AF',
                  },
                }}
              />
            }
            label="Display capture as mandatory."
            className="mb-0 text-lGrey digi-pointer-events-none"
            classes={{ label: classes.label }}
          />
        </div>
        {category.get('describe', '').trim() && (
          <div className="d-flex text-lGrey f-14 fw-400 text-capitalize">
            Describe: {category.get('describe', '')}
          </div>
        )}
      </section>
      <section>
        <Button disabled>+ Build Form</Button>
        <div className="text_underline text-lGrey mt-2 f-14 fw-500 text-center">+ Add Template</div>
      </section>
      {open && (
        <ConfigureModal
          isEdit={true}
          category={category}
          open={open}
          handleClose={handleConfigured}
        />
      )}
    </header>
  );
};
const statusData = fromJS({
  draft: {
    statusCss: 'bg-secondary text-white',
    display: 'draft',
    showAction: false,
    showPublish: true,
  },
  published: {
    statusCss: 'bgLight text-primary',
    display: 'Published',
    showAction: false,
    showPublish: false,
  },
});
// const getSelectedProgram = (categoryForm) => {};
const FormData = ({ dispatch, categoryForm, categoryForms, routeToCreateSettings, index }) => {
  const status = statusData.get(categoryForm.get('status', ''), Map());
  const showPublish = status.get('showPublish', false) && categoryForm.get('step', 0) === 4;
  const onClickRoute = (step) => () => {
    routeToCreateSettings({
      categoryFormId: categoryForm.get('_id', ''),
      categoryFormIndex: index,
      categoryFormName: categoryForm.get('name', ''),
      step: step ?? categoryForm.get('step', 0),
    });
  };
  const callBack = () => {
    dispatch(
      setData({ categoryForms: categoryForms.set(index, categoryForm.set('status', 'published')) })
    );
  };
  const handlePublish = () => {
    dispatch(updateDuplicateForm(categoryForm.set('status', 'published'), callBack));
  };
  const handleDelete = () => {
    dispatch(updateDuplicateForm(categoryForm.set('isActive', false)));
  };
  return (
    <section className="d-flex align-items-center p-3 border rounded mt-3">
      <div className="flex-grow-1 w-50">
        <div className="f-16 fw-500 cursor-pointer" onClick={onClickRoute()}>
          {categoryForm.get('name', '')}
          <span className={`${status.get('statusCss', '')} text-capitalize  rounded f-10 p-1 ml-2`}>
            {status.get('display', '')}
          </span>
        </div>
        <div className="d-flex align-items-center text-lGrey f-14 fw-400 gap-8 py-1 ">
          <img src={courseLevel} />
          <span>Assigned:</span>
          <SelectedProgram formOccurrence={categoryForm.get('formOccurrence', '')} />
        </div>
        {categoryForm.get('describe', '').trim() && (
          <div className="text-lGrey f-14 fw-400 py-1">
            Describe: {categoryForm.get('describe', '')}
          </div>
        )}
      </div>
      <div className="flex-grow-1">
        <CustomizedSteppers categoryForm={categoryForm} onClickRoute={onClickRoute} />
      </div>
      <EditCategoryForm categoryForm={categoryForm} />
      <div className="pl-3">
        <DeleteIcon fontSize="small" sx={{ color: 'red' }} onClick={handleDelete} />
      </div>
      {showPublish && (
        <Button className="ml-3" clicked={handlePublish}>
          publish
        </Button>
      )}
    </section>
  );
};
FormData.propTypes = {
  categoryForm: PropTypes.instanceOf(Map),
  routeToCreateSettings: PropTypes.function,
  index: PropTypes.Number,
};
const SelectedProgram = ({ formOccurrence }) => {
  return (
    <div className="d-flex flex-wrap gap-8">
      {formOccurrence.map((occurrence) => {
        return (
          <div className="d-flex align-items-center" key={occurrence.get('_id')}>
            <span>{occurrence.get('program_name', '')}</span>
            <BootstrapTooltip
              title={
                <AccordionComponent
                  programName={occurrence.get('program_name', '')}
                  formOccurrence={formOccurrence}
                />
              }
            >
              <InfoIcon sx={{ fontSize: '14px' }} className="ml-2" />
            </BootstrapTooltip>
          </div>
        );
      })}
    </div>
  );
};
SelectedProgram.propTypes = {
  categoryForm: PropTypes.instanceOf(List),
};
const BootstrapTooltip = styled(({ className, ...props }) => (
  <Tooltip
    {...props}
    arrow
    classes={{ popper: className }}
    sx={{
      maxWidth: '400px', // Set your desired width here
    }}
  />
))(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#374151',
    maxWidth: 'none',
  },
}));
const AccordionComponent = ({ formOccurrence, programName }) => {
  // const [expanded, setExpanded] = useState(-1);

  // // const handleChange = (id) => {
  // //   const open = id === expanded ? -1 : id;
  // //   setExpanded(open);
  // // };
  return (
    <Box sx={{ maxHeight: 200, overflowY: 'scroll', width: '25em' }} className="mx-2 my-2 px-1">
      <div className="d-flex justify-content-between align-items-center">
        <div>{programName}</div>
        <CloseRoundedIcon />
      </div>
      {formOccurrence.map((occurrence) => {
        return (
          <div key={occurrence.get('_id', '')} className="pb-3">
            <div className="pb-3">{occurrence.getIn(['_program_id', 'name'], '')}</div>
            {occurrence.get('curriculum', List()).map((curriculum, index) => {
              const totalCourses = curriculum
                .get('years', List())
                .flatMap((innerList) => innerList.get('courses', List()));
              return (
                <Accordion
                  key={curriculum.get('_curriculum_id', '')}
                  defaultExpanded
                  elevation={0}
                  sx={{
                    '&.MuiAccordion-root': {
                      backgroundColor: '#4B5563 !important',
                    },
                  }}
                  className="w-100 rounded"
                >
                  <AccordionSummary
                    expandIcon={<ArrowDropDownIcon sx={{ color: '#F9FAFB', fontSize: 20 }} />}
                    className="px-2 py-0 my-0 "
                    sx={{
                      '& .MuiAccordionSummary-content.Mui-expanded': {
                        margin: '0px',
                      },
                      '& .MuiAccordionSummary-content': {
                        margin: '0px',
                        // minHeight: '37px',
                      },
                      '&.MuiAccordionSummary-root': {
                        minHeight: '37px',
                      },
                    }}
                  >
                    <div className="text-white  fw-500 f-14">
                      {' '}
                      {curriculum.get('curriculum_name', '')}
                    </div>
                  </AccordionSummary>
                  <AccordionDetails
                    sx={{ backgroundColor: '#6b7280', color: '#ffffff' }}
                    className="rounded-bottom"
                  >
                    <div className="text-grey ">COURSE LIST {`(${totalCourses.size})`}</div>
                    {totalCourses.map((course, index) => {
                      const lastIndex = totalCourses.size - 1 === index;
                      return (
                        <div
                          key={course.get('_id', '')}
                          className={`${
                            lastIndex ? 'pt-2' : 'py-2 border-bottom  border-white'
                          } fw-400`}
                        >
                          {course.get('course_name', '')}
                        </div>
                      );
                    })}
                  </AccordionDetails>
                </Accordion>
              );
            })}
          </div>
        );
      })}
    </Box>
  );
};
