import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Map } from 'immutable';
import PropTypes, { oneOfType } from 'prop-types';
import ProgressBarModal from './ProgressBar';
import { uploadAttachment, setData } from '_reduxapi/global_configuration/v1/actions';
import { Button, Divider, Paper } from '@mui/material';
import UploadIcon from '@mui/icons-material/Upload';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import InsertPhotoIcon from '@mui/icons-material/InsertPhoto';
import DeleteIcon from '@mui/icons-material/Delete';

const UploadDragAndDrop = ({ files, setFiles }) => {
  const dispatch = useDispatch();
  const [showProgress, setShowProgress] = useState(false);
  const [importPercent, setImportPercent] = useState(0);
  const [fileNameOn, setFileNameOn] = useState('');
  const [openModalIndex, setOpenModalIndex] = useState(null);
  let file = document.getElementById('upload-file-progress-drop');

  if (file) {
    file.onclick = function () {
      this.value = null;
    };
  }

  const handleDrop = (e) => {
    e.preventDefault();
    handleFileUpload(e.dataTransfer.files[0]);
  };
  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleFileUpload = (file) => {
    const type = file.type.split('/')[1];
    if (!['pdf', 'jpeg', 'jpg', 'png'].includes(type)) {
      return dispatch(setData(Map({ message: 'Upload only pdf, jpeg, jpg, png' })));
    }

    if (file.size > 5 * 1024 * 1024) {
      return dispatch(setData(Map({ message: 'File size exceeds the limit of 5MB' })));
    }

    setFileNameOn(file.name);
    const successCallBack = (data) => {
      setFiles([...files, data.delete('size')]);
      setShowProgress(false);
      setImportPercent(0);
    };

    const failureCallBack = () => {
      setShowProgress(false);
      setImportPercent(0);
    };
    const options = {
      onUploadProgress: ({ loaded, total }) => {
        setShowProgress(true);
        const percent = Math.floor((loaded * 100) / total);
        setImportPercent(percent);
      },
    };
    const formData = new FormData();
    formData.append('file', file);
    dispatch(uploadAttachment(formData, options, successCallBack, failureCallBack));
  };

  const handleCheckFile = (file) => {
    if (file) return handleFileUpload(file);
    return;
  };
  const handleDelete = (e, index) => {
    e.stopPropagation();
    const updatedFiles = [...files];
    updatedFiles.splice(index, 1);
    setFiles(updatedFiles);
  };
  return (
    <div>
      <div>
        <div className="f-14 fw-400 text-dGrey">Attached Reference Files</div>
        <label
          htmlFor="file-upload"
          onDragOver={handleDragOver}
          onDrop={showProgress ? (e) => e.preventDefault() : handleDrop}
          className="w-22 mb-0"
        >
          <Paper
            sx={{ '&.MuiPaper-root': { border: '1px dashed #D1D5DB', borderRadius: '6px' } }}
            variant="outlined"
            className="d-flex p-0 my-3"
          >
            <div className="px-4 py-2 mr-auto my-auto">
              <div className="f-14 fw-400 text-dGrey">Upload file (Max 5)</div>
              <div className="f-10 text-lGrey">PDF, JPG, Video, IMG... up to 5MB</div>
            </div>
            {showProgress && (
              <div className="time_line_dot_Margin_Active">
                <ProgressBarModal fileName={fileNameOn} importPercent={importPercent} />
              </div>
            )}
            <Divider orientation="vertical" flexItem />
            <div className="px-3 py-3 bg-lGreen d-flex align-items-center cursor-pointer">
              <div>
                <div className="d-flex">
                  <input
                    type="file"
                    style={{ display: 'none' }}
                    id="file-upload"
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={(e) => handleCheckFile(e.target.files[0])}
                  />
                  <UploadIcon color="primary" className="mx-auto" />
                </div>
                <div className="text-primary">Upload</div>
              </div>
            </div>
          </Paper>
        </label>

        <div className="d-flex align-items-center gap-10 ProgressBar">
          {files.map((eachFile, index) => (
            <Paper
              key={index}
              className="d-flex align-items-center mb-3 px-3 py-2 cursor-pointer"
              onClick={() => setOpenModalIndex(index)}
            >
              {eachFile.get('url', '').includes('.pdf') ? (
                <PictureAsPdfIcon fontSize="small" className="mr-2" />
              ) : (
                <InsertPhotoIcon fontSize="small" className="mr-2" />
              )}
              <div>{getShortString(eachFile.get('name', ''), 20)}</div>
              <DeleteIcon
                fontSize="small"
                className="ml-3"
                sx={{ color: 'red' }}
                onClick={(e) => handleDelete(e, index)}
              />
            </Paper>
          ))}
          <DialogModal
            maxWidth={'sm'}
            fullWidth={true}
            show={openModalIndex !== null}
            onClose={() => setOpenModalIndex(null)}
          >
            <>
              {openModalIndex !== null && (
                <div className="m-3">
                  <div className="img_overflow_scroll">
                    {files[openModalIndex].get('url', '').includes('.pdf') ? (
                      <iframe
                        src={files[openModalIndex].get('signedUrl', '')}
                        title="pdf"
                        className="d-flex w-100 ht-40vh"
                      ></iframe>
                    ) : (
                      <img
                        src={files[openModalIndex].get('signedUrl', '')}
                        alt=""
                        width="100%"
                        height="40vh"
                        className="p-0 img-fluid"
                      />
                    )}
                  </div>
                  <div className="d-flex w-100">
                    <div className="ml-auto">
                      <Button onClick={() => setOpenModalIndex(null)} className="mx-2">
                        Close
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </>
          </DialogModal>
        </div>
      </div>
    </div>
  );
};

UploadDragAndDrop.propTypes = {
  files: oneOfType([PropTypes.func, PropTypes.object]),
  setFiles: PropTypes.func,
};

export default UploadDragAndDrop;
