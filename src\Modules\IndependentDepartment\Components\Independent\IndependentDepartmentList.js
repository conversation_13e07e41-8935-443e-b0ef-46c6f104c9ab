import React, { useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Tabs, Tab } from '@mui/material';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { makeStyles } from '@mui/styles';
import i18n from 'i18next';
import { List } from 'immutable';
import * as actions from '_reduxapi/program_input/v2/actions';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import DepartmentSubjectHeader from './DepartmentSubjectHeader';
import DepartmentSubjectBody from './DepartmentSubjectBody';
import DepartmentTableHeader from './DepartmentTableHeader';
import FilterDetails from './FilterDetails';

function IndependentDepartmentList() {
  const department = useContext(parentContext.departmentContext);
  const {
    setDepartmentTabValue,
    departmentTabValue,
    institutionId,
    getIndependentDepartment,
    departmentList,
    departmentRef,
  } = department;
  const [showShared, setShowShared] = useState(true);
  const [search, setSearch] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState([]);
  const [programFilter, setProgramFilter] = useState([]);
  const [subjectFilter, setSubjectFilter] = useState([]);
  const [sharedWithFilter, setSharedWithFilter] = useState([]);
  const [sharedWithFromFilter, setSharedWithFromFilter] = useState([]);
  const [sharedRadio, setSharedRadio] = useState('sharedWith');

  const useStylesFunction = makeStyles(() => ({
    root: {
      textTransform: 'none !important',
    },
  }));

  const classes = useStylesFunction();
  useEffect(() => {
    setSearch('');
    clearAll();
  }, [departmentTabValue]);

  const clearAll = () => {
    setDepartmentFilter([]);
    setProgramFilter([]);
    setSubjectFilter([]);
    setSharedWithFilter([]);
    setSharedWithFromFilter([]);
  };

  const handleChange = (event, newValue) => {
    setDepartmentTabValue(newValue);
  };

  const fetchIndependentDepartmentApi = () => {
    let requestBody = {
      filterDepartmentIds: departmentFilter,
      filterProgramIds: programFilter,
      filterSubjectIds: subjectFilter,
    };
    if (!showShared) {
      requestBody.filterSharedWithIds = sharedWithFilter;
    } else {
      if (sharedRadio === 'sharedWith') {
        requestBody.filterSharedWithIds = sharedWithFromFilter;
      } else {
        requestBody.filterSharedFromIds = sharedWithFromFilter;
      }
    }
    getIndependentDepartment(
      institutionId,
      {
        pageNo: 1,
        limit: 50,
        type: departmentTabValue,
        showShared: showShared,
        searchKey: search,
      },
      requestBody
    );
  };

  function getFilterLength() {
    return departmentFilter.concat(
      programFilter,
      subjectFilter,
      sharedWithFilter,
      sharedWithFromFilter
    );
  }

  useEffect(() => {
    departmentRef.current = callBackFn;
  }); // eslint-disable-line

  const callBackFn = () => fetchIndependentDepartmentApi();

  const filterData = {
    departmentFilter,
    setDepartmentFilter,
    programFilter,
    setProgramFilter,
    subjectFilter,
    setSubjectFilter,
    sharedWithFilter,
    setSharedWithFilter,
    sharedWithFromFilter,
    setSharedWithFromFilter,
  };

  function displayTable() {
    return (
      <div>
        <DepartmentSubjectHeader
          search={search}
          setSearch={setSearch}
          showShared={showShared}
          setShowShared={setShowShared}
          clearAll={clearAll}
          fetchIndependentDepartmentApi={fetchIndependentDepartmentApi}
        />
        <div className="pt-3 pb-5">
          <div className="container">
            {getFilterLength().length > 0 && (
              <FilterDetails
                filterData={filterData}
                sharedRadio={sharedRadio}
                clearAll={clearAll}
              />
            )}
            <DepartmentTableHeader
              search={search}
              showShared={showShared}
              filterData={filterData}
              fetchIndependentDepartmentApi={fetchIndependentDepartmentApi}
              sharedRadio={sharedRadio}
              setSharedRadio={setSharedRadio}
            />
            {departmentList.get('departments', List()).size > 0 ? (
              <DepartmentSubjectBody />
            ) : (
              <div className="mt-2 text-center">There is No data</div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="d-flex border-bottom">
        <Tabs
          value={departmentTabValue}
          indicatorColor="primary"
          textColor="primary"
          onChange={handleChange}
          aria-label="disabled tabs example"
          fullwidth="true"
        >
          <Tab
            label={`${i18n.t('user_management.academic')}`}
            value={'academic'}
            className={classes.root}
          />
          <Tab label={`${i18n.t('admin')}`} value={'admin'} className={classes.root} />
        </Tabs>
      </div>
      <div className="program_table">
        {departmentTabValue === 'academic' && displayTable()}
        {departmentTabValue === 'admin' && displayTable()}
      </div>
    </>
  );
}
IndependentDepartmentList.propTypes = {
  children: PropTypes.array,
};
const mapStateToProps = (state) => {
  return {};
};

export default compose(withRouter, connect(mapStateToProps, actions))(IndependentDepartmentList);
