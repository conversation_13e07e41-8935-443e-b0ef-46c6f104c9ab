import React /* useContext */ from 'react';
import PropTypes from 'prop-types';
import InstitutionAddDetails from '../../../InstitutionOnboarding/Component/InstitutionForm/InstitutionAddDetails';
/* import { Trans } from 'react-i18next';

import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select'; */

//import UniversityContext from '../../Context/university-context';
function EditDetails({
  updatedDetails,
  setUpdatedDetails,
  childFunc,
  fetchData,
  selectedType,
  setIsChanged,
}) {
  // const details = useContext(UniversityContext);
  //const { updateDetails } = details;
  /* const {
    type,
    name,
    code,
    noOfColleges,
    accreditation,
    address,
     city,
    state,
    country, 
    zipCode,
  } = updatedDetails;*/
  return (
    <div className="p-3">
      <InstitutionAddDetails
        isGroup={selectedType === 'group'}
        childFunc={childFunc}
        getData={fetchData}
        updatedDetails={updatedDetails}
        setIsChanged={setIsChanged}
      />
    </div>
  );
}
EditDetails.propTypes = {
  setUpdatedDetails: PropTypes.func,
  fetchData: PropTypes.func,
  updatedDetails: PropTypes.object,
  childFunc: PropTypes.object,
  selectedType: PropTypes.string,
  setIsChanged: PropTypes.func,
};
export default EditDetails;
