import { useSelector } from 'react-redux';
import { List, Map, fromJS } from 'immutable';

function useCalendar() {
  //const dispatch = useDispatch();
  const authDataArray = useSelector((state) => state?.auth);
  const authData = fromJS(authDataArray);
  const institutionCalendarLists = authData.get('institutionCalendarLists', List());
  const activeInstitutionCalendar = authData.get('activeInstitutionCalendar', Map());
  const activeCalendarId = activeInstitutionCalendar.get('_id', '');
  //   if (academicYears.size === 0) dispatch(getAcademicYears());
  // }, [dispatch]); //eslint-disable-line

  const isCurrentCalendar = (calendarId = '') => {
    return institutionCalendarLists.some(
      (item) =>
        (calendarId !== ''
          ? item.get('_id', '') === calendarId
          : item.get('_id', '') === activeCalendarId) && item.get('isActive', false) === true
    );
  };
  return { institutionCalendarLists, activeCalendarId, isCurrentCalendar };
}

export default useCalendar;

// import { useSelector } from 'react-redux';
// import { List, Map, fromJS } from 'immutable';

// function useCalendar() {
//   //const dispatch = useDispatch();
//   const authDataArray = useSelector((state) => state?.auth);
//   const authData = fromJS(authDataArray);
//   const institutionCalendarLists = authData.get('institutionCalendarLists', List());
//   const activeInstitutionCalendar = authData.get('activeInstitutionCalendar', Map());
//   const activeCalendarId = activeInstitutionCalendar.get('_id', '');
//   const currentCalendarId = institutionCalendarLists.getIn([0, '_id'], '');
//   // useEffect(() => {
//   //   if (academicYears.size === 0) dispatch(getAcademicYears());
//   // }, [dispatch]); //eslint-disable-line

//   const isCurrentCalendar = (calendarId = '') => {
//     if (activeCalendarId === currentCalendarId && calendarId === '') {
//       return true;
//     } else if (calendarId !== '') {
//       if (calendarId === currentCalendarId) {
//         return true;
//       } else {
//         const idArray = institutionCalendarLists.map((item) => item.get('_id', '')).toJS();
//         if (!idArray.includes(calendarId)) {
//           return true;
//         }
//       }
//     }
//     return false;
//   };
//   return { institutionCalendarLists, activeCalendarId, isCurrentCalendar };
// }

// export default useCalendar;
