import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { getFormattedCreditHours, getCourseDuration } from '../utils';
import {
  capitalize,
  getTranslatedDuration,
  getVersionName,
  indVerRename,
  levelRename,
} from '../../../../utils';
import { t } from 'i18next';

function CourseInfo({ course, modalClick, programId }) {
  const [expand, setExpand] = useState(false);

  function getCourseSharedWith() {
    const shared = course.getIn(['course_assigned_details', 'course_shared_with'], List());
    return shared
      .map(
        (s) =>
          `${s.get('program_name', '')} / ${s.get('curriculum_name', '')} / ${s.get(
            'year',
            ''
          )} / ${levelRename(s.get('level_no', ''), programId)}`
      )
      .join(' • ');
  }

  function deliveryDuration(c) {
    if (c.get('delivery_type', List()).size > 0) {
      const delivery_duration = c
        .get('delivery_type', List())
        .filter((item) => item.get('isActive') === true)
        .map((item) => item.get('duration', 0))
        .toJS();
      const actualCount = delivery_duration.length;
      const uniqueCount = delivery_duration.filter((v, i, a) => a.indexOf(v) === i).length;
      const allEqual = (arr) => arr.every((v) => v === arr[0]);
      if (actualCount === uniqueCount) {
        return actualCount === 0
          ? c.get('duration', '') !== null
            ? c.get('duration', '') + ' Min'
            : '-'
          : !allEqual(delivery_duration)
          ? 'Mixed'
          : Math.max(...delivery_duration) + ' Min';
      } else {
        return !allEqual(delivery_duration) ? 'Mixed' : Math.max(...delivery_duration) + ' Min';
      }
    } else {
      return c.get('duration', '') !== null ? c.get('duration', '') + ' Min' : '-';
    }
  }

  return (
    <div className="pt-4 pb-4">
      <div className="program_card bg-white" style={{ minHeight: '100px' }}>
        <div className="session_card">
          <div className="row ">
            <div className="col-md-7 col-xl-7 mb-2">
              <p className="mb-1 bold text-left">
                {' '}
                {`${course.get('courses_number', '')} - ${course.get('courses_name', '')}`}
                {getVersionName(course)}
              </p>
              <span className="pr-2">
                {capitalize(indVerRename(course.get('course_type', ''), programId))}{' '}
              </span>
              <span className="border-left">
                <span className="pl-2">
                  {getFormattedCreditHours(course).replace(
                    'Credit Hours',
                    t('curriculum_keys.credit_hours')
                  )}{' '}
                  | {getTranslatedDuration(getCourseDuration(course))}
                </span>
              </span>
            </div>

            <div className="col-md-5 col-xl-5 mb-2">
              <div className="d-flex justify-content-end align-items-center">
                <span className="text-skyblue f-14 pr-3 remove_hover" onClick={modalClick}>
                  {' '}
                  {t('view_credit_hours')}
                </span>
                <span
                  className="text-skyblue f-14 expend remove_hover"
                  onClick={() => setExpand(!expand)}
                >
                  {' '}
                  {t('standard_range_setting.expand')}{' '}
                  <i
                    className={`fa fa-caret-${!expand ? 'down' : 'up'} pl-2`}
                    aria-hidden="true"
                  ></i>
                </span>
              </div>
            </div>
          </div>

          {expand && (
            <>
              <div className="row pt-2 pb-2">
                <div className="col-md-3 mb-2">
                  <p className="mb-1 f-15 text-gray">
                    {' '}
                    {t('program_input.participating_subjects')}
                  </p>
                  <b className="pr-2">
                    {' '}
                    {course
                      .get('participating', List())
                      .map((p) => p.get('subject_name'))
                      .join(', ')}{' '}
                  </b>
                </div>
                <div className="col-md-3 mb-2">
                  <p className="mb-1 f-15 text-gray"> {t('reports_analytics.admin_subject')}</p>
                  <b className="pr-2"> {course.getIn(['administration', 'subject_name'], '')} </b>
                </div>
                <div className="col-md-3 mb-2">
                  <p className="mb-1 f-15 text-gray">
                    {' '}
                    {t('program_input.administrating_department')}
                  </p>
                  <b className="pr-2">{course.getIn(['administration', 'department_name'], '')} </b>
                </div>
              </div>

              <div className="pt-2 pb-2">
                <p className="mb-1 f-15 text-gray">
                  {' '}
                  {t('configure_levels.sessionType_deliveryTypes')}
                </p>

                <div className="row ">
                  {course.get('credit_hours', List()).map((c) => {
                    if (c.get('credit_hours', '') !== 0) {
                      return (
                        <div key={c.get('_id')} className="col-md-3 mb-2">
                          <div>
                            <b className="pr-2">
                              {`${indVerRename(c.get('type_symbol', ''), programId)} Cr.H - ${c.get(
                                'credit_hours',
                                ''
                              )} • Co.H - ${c.get('contact_hours', '')} • Dur - `}

                              {
                                // c.get('duration_split', false)
                                //   ? 'Mixed'
                                //   : String(c.get('duration', '')) + ' Min'
                              }
                              {deliveryDuration(c)}
                            </b>
                          </div>

                          {c
                            .get('delivery_type', List())
                            .filter((item) => item.get('isActive') === true)
                            .map((d) => (
                              <div key={d.get('_id')}>
                                <b>{`${d.get('delivery_symbol', '')} Dur - ${d.get(
                                  'duration',
                                  ''
                                )} Min`}</b>
                              </div>
                            ))}
                        </div>
                      );
                    } else {
                      return <></>;
                    }
                  })}
                </div>
                <div className="row pt-2">
                  <div className="col-md-3 mb-2">
                    <p className="mb-1 f-15 text-gray">
                      {' '}
                      Allow to edit credit hours while scheduling?
                    </p>
                    <b className="pr-2">
                      {`${course.get('allow_editing') ? 'Allow' : "Don't Allow"} Editing ${
                        course.get('achieve_target') ? '• Should achieve targeted credit hours' : ''
                      }`}{' '}
                    </b>
                  </div>
                  <div className="col-md-3 mb-2">
                    <p className="mb-1 f-15 text-gray"> {levelRename('Level', programId)}</p>
                    <b className="pr-2">{levelRename(course.get('level_no', ''), programId)} </b>
                  </div>
                  <div className="col-md-3 mb-2">
                    <p className="mb-1 f-15 text-gray"> Shared with</p>
                    <b className="pr-2">{getCourseSharedWith()}</b>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

CourseInfo.propTypes = {
  course: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  activeScheduleView: PropTypes.oneOf(['list', 'calendar']),
  studentGroupStatus: PropTypes.bool,
  modalClick: PropTypes.func,
  programId: PropTypes.string,
};

export default CourseInfo;
