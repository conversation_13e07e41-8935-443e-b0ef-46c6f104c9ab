import LocalStorageService from 'LocalStorageService';
import axios from '../../axios';
import {
  constructDataProgramWise,
  constructDataCourseWise,
  createAction,
  getConcatCalendarList,
} from '../util';
import { fromJS, Map } from 'immutable';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}
// export const SET_BREADCRUMB_SUCCESS = 'SET_BREADCRUMB_SUCCESS';
// const setBreadCrumbSuccess = createAction(SET_BREADCRUMB_SUCCESS, 'breadcrumbs');
// export const setBreadCrumb = (arr) => {
//   return (dispatch) => {
//     dispatch(setBreadCrumbSuccess(arr));
//   };
// };

export const UPLOAD_ATTACHMENT_REQUEST = 'UPLOAD_ATTACHMENT_REQUEST';
export const UPLOAD_ATTACHMENT_SUCCESS = 'UPLOAD_ATTACHMENT_SUCCESS';
export const UPLOAD_ATTACHMENT_FAILURE = 'UPLOAD_ATTACHMENT_FAILURE';
const uploadAttachmentRequest = createAction(UPLOAD_ATTACHMENT_REQUEST);
const uploadAttachmentSuccess = createAction(UPLOAD_ATTACHMENT_SUCCESS, 'data');
const uploadAttachmentFailure = createAction(UPLOAD_ATTACHMENT_FAILURE, 'error');
export function uploadAttachment(file, cb) {
  return function (dispatch) {
    dispatch(uploadAttachmentRequest());
    axios
      .post(`/lmsStudentSetting/upload`, file)
      .then((res) => {
        dispatch(uploadAttachmentSuccess());
        cb && cb(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(uploadAttachmentFailure(error));
      });
  };
}

export const GET_STEP_TWO_DETAILS_REQUEST = 'GET_STEP_TWO_DETAILS_REQUEST';
export const GET_STEP_TWO_DETAILS_SUCCESS = 'GET_STEP_TWO_DETAILS_SUCCESS';
export const GET_STEP_TWO_DETAILS_FAILURE = 'GET_STEP_TWO_DETAILS_FAILURE';

const getStepTwoDetailsRequest = createAction(GET_STEP_TWO_DETAILS_REQUEST, 'requestBody');
const getStepTwoDetailsSuccess = createAction(
  GET_STEP_TWO_DETAILS_SUCCESS,
  'data',
  'setDataIntoReducer'
);
const getStepTwoDetailsFailure = createAction(GET_STEP_TWO_DETAILS_FAILURE, 'error');

export function getStepTwoDetails(categoryFormId, cb, setDataIntoReducer = true) {
  return function (dispatch) {
    dispatch(getStepTwoDetailsRequest());
    axios
      .get(`/qapcCategoryForm_v2/getFormAttachment?categoryFormId=${categoryFormId}`)
      .then((res) => {
        const data = fromJS(res.data.data);
        dispatch(getStepTwoDetailsSuccess(data, setDataIntoReducer));
        cb && cb(data);
      })
      .catch((error) => dispatch(getStepTwoDetailsFailure(error)));
  };
}

export const GENERATE_URL_REQUEST = 'GENERATE_URL_REQUEST';
export const GENERATE_URL_SUCCESS = 'GENERATE_URL_SUCCESS';
export const GENERATE_URL_FAILURE = 'GENERATE_URL_FAILURE';

const generateUrlRequest = createAction(GENERATE_URL_REQUEST);
const generateUrlSuccess = createAction(GENERATE_URL_SUCCESS, 'data');
const generateUrlFailure = createAction(GENERATE_URL_FAILURE, 'error');

export function generateUrl(url) {
  return function (dispatch) {
    dispatch(generateUrlRequest());
    axios
      .get(`/qapcCategoryForm_v2/qapcGuideResources`)
      .then((res) => dispatch(generateUrlSuccess(res.data.data)))
      .catch((error) => dispatch(generateUrlFailure(error)));
  };
}

export const UPDATE_CATEGORY_FORM_REQUEST = 'UPDATE_CATEGORY_FORM_REQUEST';
export const UPDATE_CATEGORY_FORM_SUCCESS = 'UPDATE_CATEGORY_FORM_SUCCESS';
export const UPDATE_CATEGORY_FORM_FAILURE = 'UPDATE_CATEGORY_FORM_FAILURE';

const updateCategoryFormRequest = createAction(UPDATE_CATEGORY_FORM_REQUEST, 'requestBody');
const updateCategoryFormSuccess = createAction(UPDATE_CATEGORY_FORM_SUCCESS, 'data');
const updateCategoryFormFailure = createAction(UPDATE_CATEGORY_FORM_FAILURE, 'error');

export function updateCategoryForm(data, cb) {
  return function (dispatch) {
    dispatch(updateCategoryFormRequest());
    axios
      .put(`/qapcCategoryForm_v2/updateCategoryForm`, data)
      .then((res) => {
        dispatch(updateCategoryFormSuccess(res.data.data));
        cb && cb(fromJS(res.data.data));
      })
      .catch((error) => dispatch(updateCategoryFormFailure(error)));
  };
}

export const GET_CATEGORY_FORM_REQUEST = 'GET_CATEGORY_FORM_REQUEST';
export const GET_CATEGORY_FORM_SUCCESS = 'GET_CATEGORY_FORM_SUCCESS';
export const GET_CATEGORY_FORM_FAILURE = 'GET_CATEGORY_FORM_FAILURE';

const getCategoryFormRequest = createAction(GET_CATEGORY_FORM_REQUEST);
const getCategoryFormSuccess = createAction(GET_CATEGORY_FORM_SUCCESS, 'data');
const getCategoryFormFailure = createAction(GET_CATEGORY_FORM_FAILURE, 'error');

export function getCategoryForm(params, cb) {
  return function (dispatch) {
    dispatch(getCategoryFormRequest());
    axios
      .get(`/qapcCategoryForm_v2/getCategoryForm?${params}`)
      .then((res) => {
        dispatch(getCategoryFormSuccess({ data: res.data, params }));
        cb && cb(res.data.data.formData);
      })
      .catch((error) => dispatch(getCategoryFormFailure(error)));
  };
}

export const GET_VARIETY_OF_FORMS_REQUEST = 'GET_VARIETY_OF_FORMS_REQUEST';
export const GET_VARIETY_OF_FORMS_SUCCESS = 'GET_VARIETY_OF_FORMS_SUCCESS';
export const GET_VARIETY_OF_FORMS_FAILURE = 'GET_VARIETY_OF_FORMS_FAILURE';

const getVarietyOfFormsRequest = createAction(GET_VARIETY_OF_FORMS_REQUEST);
const getVarietyOfFormsSuccess = createAction(GET_VARIETY_OF_FORMS_SUCCESS, 'data');
const getVarietyOfFormsFailure = createAction(GET_VARIETY_OF_FORMS_FAILURE, 'error');

export function getVarietyOfForms(params, cb) {
  return function (dispatch) {
    dispatch(getVarietyOfFormsRequest());
    axios
      .get(`/qapcCategoryForm_v2/categoryTypeForm?${params}`)
      .then((res) => {
        dispatch(getVarietyOfFormsSuccess({ data: res.data, params }));
        cb && cb(res.data.data.categoryFormData);
      })
      .catch((error) => dispatch(getVarietyOfFormsFailure(error)));
  };
}

export const QA_PC_SAVE_SETTING_REQUEST = 'QA_PC_SAVE_SETTING_REQUEST';
export const QA_PC_SAVE_SETTING_SUCCESS = 'QA_PC_SAVE_SETTING_SUCCESS';
export const QA_PC_SAVE_SETTING_FAILURE = 'QA_PC_SAVE_SETTING_FAILURE';

const qaPcSaveSettingRequest = createAction(QA_PC_SAVE_SETTING_REQUEST, 'requestBody');
const qaPcSaveSettingSuccess = createAction(QA_PC_SAVE_SETTING_SUCCESS, 'data');
const qaPcSaveSettingFailure = createAction(QA_PC_SAVE_SETTING_FAILURE, 'error');

export function qaPcSaveSetting(payload, callBack) {
  return function (dispatch) {
    dispatch(qaPcSaveSettingRequest());
    axios
      .post('/qapcSetting_v2/saveConfigure', payload)
      .then((res) => {
        dispatch(qaPcSaveSettingSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((error) => dispatch(qaPcSaveSettingFailure(error)));
  };
}

export const GET_QA_PC_SETTING_REQUEST = 'GET_QA_PC_SETTING_REQUEST';
export const GET_QA_PC_SETTING_SUCCESS = 'GET_QA_PC_SETTING_SUCCESS';
export const GET_QA_PC_SETTING_FAILURE = 'GET_QA_PC_SETTING_FAILURE';

const getQaPcSettingRequest = createAction(GET_QA_PC_SETTING_REQUEST, 'requestBody');
const getQaPcSettingSuccess = createAction(GET_QA_PC_SETTING_SUCCESS, 'data');
const getQaPcSettingFailure = createAction(GET_QA_PC_SETTING_FAILURE, 'error');

export function getQaPcSetting() {
  return function (dispatch) {
    dispatch(getQaPcSettingRequest());
    axios
      .get('/qapcSetting_v2/getConfigureSetting')
      .then((res) => {
        dispatch(getQaPcSettingSuccess(res.data.data));
      })
      .catch((error) => dispatch(getQaPcSettingFailure(error)));
  };
}
export const GET_INSTITUTION_CALENDAR_LIST_REQUEST = 'GET_INSTITUTION_CALENDAR_LIST_REQUEST';
export const GET_INSTITUTION_CALENDAR_LIST_SUCCESS = 'GET_INSTITUTION_CALENDAR_LIST_SUCCESS';
export const GET_INSTITUTION_CALENDAR_LIST_FAILURE = 'GET_INSTITUTION_CALENDAR_LIST_FAILURE';

const getInstitutionCalendarListRequest = createAction(GET_INSTITUTION_CALENDAR_LIST_REQUEST);
const getInstitutionCalendarListSuccess = createAction(
  GET_INSTITUTION_CALENDAR_LIST_SUCCESS,
  'data'
);
const getInstitutionCalendarListFailure = createAction(
  GET_INSTITUTION_CALENDAR_LIST_FAILURE,
  'error'
);
export function getInstitutionCalendarList(callBack) {
  return function (dispatch) {
    dispatch(getInstitutionCalendarListRequest());
    axios
      .get('/qapcSetting_v2/institutionCalendarList')
      .then((res) => {
        dispatch(getInstitutionCalendarListSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getInstitutionCalendarListFailure(error)));
  };
}
export const POST_INSTITUTION_CALENDAR_LIST_REQUEST = 'POST_INSTITUTION_CALENDAR_LIST_REQUEST';
export const POST_INSTITUTION_CALENDAR_LIST_SUCCESS = 'POST_INSTITUTION_CALENDAR_LIST_SUCCESS';
export const POST_INSTITUTION_CALENDAR_LIST_FAILURE = 'POST_INSTITUTION_CALENDAR_LIST_FAILURE';

const postInstitutionCalendarListRequest = createAction(POST_INSTITUTION_CALENDAR_LIST_REQUEST);
const postInstitutionCalendarListSuccess = createAction(
  POST_INSTITUTION_CALENDAR_LIST_SUCCESS,
  'data'
);
const postInstitutionCalendarListFailure = createAction(
  POST_INSTITUTION_CALENDAR_LIST_FAILURE,
  'error'
);

export function postInstitutionCalendarList(data, callBack) {
  return function (dispatch) {
    dispatch(postInstitutionCalendarListRequest());
    axios
      .post('/qapcSetting_v2/createCalendar', data)
      .then((res) => {
        dispatch(postInstitutionCalendarListSuccess());
        callBack && callBack();
      })
      .catch((error) => dispatch(postInstitutionCalendarListFailure(error)));
  };
}

export const UPDATE_CATEGORY_CONFIGURE_REQUEST = 'UPDATE_CATEGORY_CONFIGURE_REQUEST';
export const UPDATE_CATEGORY_CONFIGURE_SUCCESS = 'UPDATE_CATEGORY_CONFIGURE_SUCCESS';
export const UPDATE_CATEGORY_CONFIGURE_FAILURE = 'UPDATE_CATEGORY_CONFIGURE_FAILURE';

const updateCategoryConfigureRequest = createAction(
  UPDATE_CATEGORY_CONFIGURE_REQUEST,
  'requestBody'
);
const updateCategoryConfigureSuccess = createAction(UPDATE_CATEGORY_CONFIGURE_SUCCESS, 'data');
const updateCategoryConfigureFailure = createAction(UPDATE_CATEGORY_CONFIGURE_FAILURE, 'error');

export function updateCategoryConfigure(payload, cb) {
  return function (dispatch) {
    dispatch(updateCategoryConfigureRequest());
    axios
      .put('/qapcCategory_v2/updateCategoryConfigure', payload)
      .then((res) => {
        dispatch(updateCategoryConfigureSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => {
        const statusCode = error.response.status;
        dispatch(updateCategoryConfigureFailure(error));
        cb && cb(statusCode);
      });
  };
}

export const GET_ATTEMPT_TYPE_REQUEST = 'GET_ATTEMPT_TYPE_REQUEST';
export const GET_ATTEMPT_TYPE_SUCCESS = 'GET_ATTEMPT_TYPE_SUCCESS';
export const GET_ATTEMPT_TYPE_FAILURE = 'GET_ATTEMPT_TYPE_FAILURE';

const getAttemptTypeRequest = createAction(GET_ATTEMPT_TYPE_REQUEST);
const getAttemptTypeSuccess = createAction(GET_ATTEMPT_TYPE_SUCCESS, 'data');
const getAttemptTypeFailure = createAction(GET_ATTEMPT_TYPE_FAILURE, 'error');

export function getAttemptType(cb) {
  return function (dispatch) {
    dispatch(getAttemptTypeRequest());
    axios
      .get(`/qapcCategory_v2/getAttemptType`)
      .then((res) => {
        dispatch(getAttemptTypeSuccess());
        cb && cb(res.data.data);
      })
      .catch((error) => dispatch(getAttemptTypeFailure(error)));
  };
}

export const GET_SINGLE_CONFIG_REQUEST = 'GET_SINGLE_CONFIG_REQUEST';
export const GET_SINGLE_CONFIG_SUCCESS = 'GET_SINGLE_CONFIG_SUCCESS';
export const GET_SINGLE_CONFIG_FAILURE = 'GET_SINGLE_CONFIG_FAILURE';

const getSingleConfigRequest = createAction(GET_SINGLE_CONFIG_REQUEST, 'requestBody');
const getSingleConfigSuccess = createAction(GET_SINGLE_CONFIG_SUCCESS, 'data', 'index');
const getSingleConfigFailure = createAction(GET_SINGLE_CONFIG_FAILURE, 'error');

export function getSingleConfig(id, positionIndex, cb) {
  return function (dispatch) {
    dispatch(getSingleConfigRequest());
    axios
      .get(`/qapcCategory_v2/singleCategoryConfig?categoryId=${id}`)
      .then((res) => {
        dispatch(getSingleConfigSuccess(res.data.data, positionIndex));
        cb && cb();
      })
      .catch((error) => dispatch(getSingleConfigFailure(error)));
  };
}

export const UPDATE_DUPLICATE_FORM_REQUEST_V2 = 'UPDATE_DUPLICATE_FORM_REQUEST_V2';
export const UPDATE_DUPLICATE_FORM_SUCCESS_V2 = 'UPDATE_DUPLICATE_FORM_SUCCESS_V2';
export const UPDATE_DUPLICATE_FORM_FAILURE_V2 = 'UPDATE_DUPLICATE_FORM_FAILURE_V2';

const updateDuplicateFormRequest = createAction(UPDATE_DUPLICATE_FORM_REQUEST_V2);
const updateDuplicateFormSuccess = createAction(UPDATE_DUPLICATE_FORM_SUCCESS_V2, 'data', 'method');
const updateDuplicateFormFailure = createAction(UPDATE_DUPLICATE_FORM_FAILURE_V2, 'error');

export function updateDuplicateForm(payload, cb) {
  return function (dispatch) {
    dispatch(updateDuplicateFormRequest());
    axios
      .put('/qapcCategoryForm_v2/updateCategoryForm', payload)
      .then((res) => {
        dispatch(updateDuplicateFormSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(updateDuplicateFormFailure(error)));
  };
}

export const CREATE_DUPLICATE_FORM_REQUEST = 'CREATE_DUPLICATE_FORM_REQUEST';
export const CREATE_DUPLICATE_FORM_SUCCESS = 'CREATE_DUPLICATE_FORM_SUCCESS';
export const CREATE_DUPLICATE_FORM_FAILURE = 'CREATE_DUPLICATE_FORM_FAILURE';

const createDuplicateFormRequest = createAction(CREATE_DUPLICATE_FORM_REQUEST);
const createDuplicateFormSuccess = createAction(CREATE_DUPLICATE_FORM_SUCCESS, 'data', 'method');
const createDuplicateFormFailure = createAction(CREATE_DUPLICATE_FORM_FAILURE, 'error');

export function createDuplicateForm(payload, params, method = 'method', cb) {
  return function (dispatch) {
    dispatch(createDuplicateFormRequest());
    axios
      .post('/qapcCategoryForm_v2/createDuplicateForm', payload)
      .then((res) => {
        dispatch(createDuplicateFormSuccess({ data: res.data.data, params }, method));
        cb && cb();
      })
      .catch((error) => dispatch(createDuplicateFormFailure(error)));
  };
}

export const GET_CONFIGURE_TEMPLATE_REQUEST = 'GET_CONFIGURE_TEMPLATE_REQUEST';
export const GET_CONFIGURE_TEMPLATE_SUCCESS = 'GET_CONFIGURE_TEMPLATE_SUCCESS';
export const GET_CONFIGURE_TEMPLATE_FAILURE = 'GET_CONFIGURE_TEMPLATE_FAILURE';

const getConfigureTemplateRequest = createAction(GET_CONFIGURE_TEMPLATE_REQUEST, 'requestBody');
const getConfigureTemplateSuccess = createAction(GET_CONFIGURE_TEMPLATE_SUCCESS, 'data');
const getConfigureTemplateFailure = createAction(GET_CONFIGURE_TEMPLATE_FAILURE, 'error');

export function getConfigureTemplate() {
  return function (dispatch) {
    dispatch(getConfigureTemplateRequest());
    axios
      .get('qapcCategory_v2/getConfigureTemplate')
      .then((res) => {
        dispatch(getConfigureTemplateSuccess(res.data.data));
      })
      .catch((error) => dispatch(getConfigureTemplateFailure(error)));
  };
}
export const GET_APPROVAL_HIERARCHY_REQUEST = 'GET_APPROVAL_HIERARCHY_REQUEST';
export const GET_APPROVAL_HIERARCHY_SUCCESS = 'GET_APPROVAL_HIERARCHY_SUCCESS';
export const GET_APPROVAL_HIERARCHY_FAILURE = 'GET_APPROVAL_HIERARCHY_FAILURE';

const getApprovalHierarchyRequest = createAction(GET_APPROVAL_HIERARCHY_REQUEST);
const getApprovalHierarchySuccess = createAction(GET_APPROVAL_HIERARCHY_SUCCESS, 'data');
const getApprovalHierarchyFailure = createAction(GET_APPROVAL_HIERARCHY_FAILURE, 'error');

export function getApprovalHierarchy(categoryFormId, callBack) {
  return function (dispatch) {
    dispatch(getApprovalHierarchyRequest());
    axios
      .get(`/qapcCategoryForm_v2/getApprovalHierarchy?categoryFormId=${categoryFormId}`)
      .then((res) => {
        dispatch(getApprovalHierarchySuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getApprovalHierarchyFailure(error)));
  };
}

export const GET_CONCLUDING_PHASE_REQUEST = 'GET_CONCLUDING_PHASE_REQUEST';
export const GET_CONCLUDING_PHASE_SUCCESS = 'GET_CONCLUDING_PHASE_SUCCESS';
export const GET_CONCLUDING_PHASE_FAILURE = 'GET_CONCLUDING_PHASE_FAILURE';

const getConcludingPhaseRequest = createAction(GET_CONCLUDING_PHASE_REQUEST);
const getConcludingPhaseSuccess = createAction(GET_CONCLUDING_PHASE_SUCCESS, 'data');
const getConcludingPhaseFailure = createAction(GET_CONCLUDING_PHASE_FAILURE, 'error');

export function getConcludingPhase(categoryFormId, callBack) {
  return function (dispatch) {
    dispatch(getConcludingPhaseRequest());
    axios
      .get(`/qapcCategoryForm_v2/concludingPhase?categoryFormId=${categoryFormId}`)
      .then((res) => {
        dispatch(getConcludingPhaseSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getConcludingPhaseFailure(error)));
  };
}

export const UPDATE_CATEGORY_REQUEST = 'UPDATE_CATEGORY_REQUEST';
export const UPDATE_CATEGORY_SUCCESS = 'UPDATE_CATEGORY_SUCCESS';
export const UPDATE_CATEGORY_FAILURE = 'UPDATE_CATEGORY_FAILURE';

const updateCategoryRequest = createAction(UPDATE_CATEGORY_REQUEST);
const updateCategorySuccess = createAction(UPDATE_CATEGORY_SUCCESS, 'data');
const updateCategoryFailure = createAction(UPDATE_CATEGORY_FAILURE, 'error');

export function updateCategory(payload, cb) {
  return function (dispatch) {
    dispatch(updateCategoryRequest());
    axios
      .post(`/qapcCategory_v2/createCategoryConfigure`, payload)
      .then((res) => {
        dispatch(updateCategorySuccess());
        dispatch(getConfigureTemplate());
        cb && cb(res.status);
      })
      .catch((error) => {
        const statusCode = error.response.status;
        dispatch(updateCategoryFailure());
        cb && cb(statusCode);
      });
  };
}

export const PUT_RESET_FORM_COURSE_SETTING_REQUEST = 'PUT_RESET_FORM_COURSE_SETTING_REQUEST';
export const PUT_RESET_FORM_COURSE_SETTING_SUCCESS = 'PUT_RESET_FORM_COURSE_SETTING_SUCCESS';
export const PUT_RESET_FORM_COURSE_SETTING_FAILURE = 'PUT_RESET_FORM_COURSE_SETTING_FAILURE';

const putResetFormCourseSettingRequest = createAction(PUT_RESET_FORM_COURSE_SETTING_REQUEST);
const putResetFormCourseSettingSuccess = createAction(
  PUT_RESET_FORM_COURSE_SETTING_SUCCESS,
  'data'
);
const putResetFormCourseSettingFailure = createAction(
  PUT_RESET_FORM_COURSE_SETTING_FAILURE,
  'error'
);

export function resetFormCourseSetting(categoryId, cb) {
  return function (dispatch) {
    dispatch(putResetFormCourseSettingRequest());
    axios
      .put(`/qapcCategoryForm_v2/resetFormCourseSetting?categoryId=${categoryId}`)
      .then((res) => {
        dispatch(putResetFormCourseSettingSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(putResetFormCourseSettingFailure(error)));
  };
}

export const GET_INSTITUTION_REQUEST = 'GET_INSTITUTION_REQUEST';
export const GET_INSTITUTION_SUCCESS = 'GET_INSTITUTION_SUCCESS';
export const GET_INSTITUTION_FAILURE = 'GET_INSTITUTION_FAILURE';

const getInstitutionsRequest = createAction(GET_INSTITUTION_REQUEST);
const getInstitutionsSuccess = createAction(GET_INSTITUTION_SUCCESS, 'data');
const getInstitutionsFailure = createAction(GET_INSTITUTION_FAILURE, 'error');

export function getInstitutionsList() {
  return function (dispatch) {
    dispatch(getInstitutionsRequest());
    axios
      .get('/qapcCategoryForm_v2/getInstitution')
      .then((res) => {
        dispatch(getInstitutionsSuccess(res.data.data));
      })
      .catch((error) => dispatch(getInstitutionsFailure(error)));
  };
}

export const QA_PC_CURRICULUM_DETAILS_REQUEST_V1 = 'QA_PC_CURRICULUM_DETAILS_REQUEST_V1';
export const QA_PC_CURRICULUM_DETAILS_SUCCESS_V1 = 'QA_PC_CURRICULUM_DETAILS_SUCCESS_V1';
export const QA_PC_CURRICULUM_DETAILS_FAILURE_V1 = 'QA_PC_CURRICULUM_DETAILS_FAILURE_V1';

const qaPcCurriculumDetailsRequest = createAction(
  QA_PC_CURRICULUM_DETAILS_REQUEST_V1,
  'requestBody'
);
const qaPcCurriculumDetailsSuccess = createAction(
  QA_PC_CURRICULUM_DETAILS_SUCCESS_V1,
  'data',
  'checked'
);
const qaPcCurriculumDetailsFailure = createAction(QA_PC_CURRICULUM_DETAILS_FAILURE_V1, 'error');

export function getCurriculumDetails({ params, cb, checked, program_name, programId }) {
  return function (dispatch) {
    dispatch(qaPcCurriculumDetailsRequest());
    axios
      .get('/qapcCategoryForm_v2/getCurriculum', { params })
      .then((res) => {
        const constructedData = constructDataProgramWise(
          fromJS(res.data.data),
          checked,
          programId,
          program_name
        );
        dispatch(qaPcCurriculumDetailsSuccess(constructedData));
        cb && cb(constructedData);
      })
      .catch((error) => dispatch(qaPcCurriculumDetailsFailure(error)));
  };
}

export const QA_PC_PROGRAM_DETAILS_REQUEST_V1 = 'QA_PC_PROGRAM_DETAILS_REQUEST_V1';
export const QA_PC_PROGRAM_DETAILS_SUCCESS_V1 = 'QA_PC_PROGRAM_DETAILS_SUCCESS_V1';
export const QA_PC_PROGRAM_DETAILS_FAILURE_V1 = 'QA_PC_PROGRAM_DETAILS_FAILURE_V1';

const qaPcProgramDetailsRequest = createAction(QA_PC_PROGRAM_DETAILS_REQUEST_V1, 'requestBody');
const qaPcProgramDetailsSuccess = createAction(QA_PC_PROGRAM_DETAILS_SUCCESS_V1, 'data');
const qaPcProgramDetailsFailure = createAction(QA_PC_PROGRAM_DETAILS_FAILURE_V1, 'error');

export function getProgramDetails({ params, cb, checked }) {
  return function (dispatch) {
    dispatch(qaPcProgramDetailsRequest());
    axios
      .get('/qapcCategoryForm_v2/programDetails', { params })
      .then((res) => {
        const constructData = constructDataCourseWise(fromJS(res.data.data), checked);
        dispatch(qaPcProgramDetailsSuccess(constructData));
        cb && cb(constructData);
      })
      .catch((error) => dispatch(qaPcProgramDetailsFailure(error)));
  };
}

export const QA_PC_PROGRAM_LIST_REQUEST = 'QA_PC_PROGRAM_LIST_REQUEST';
export const QA_PC_PROGRAM_LIST_SUCCESS = 'QA_PC_PROGRAM_LIST_SUCCESS';
export const QA_PC_PROGRAM_LIST_FAILURE = 'QA_PC_PROGRAM_LIST_FAILURE';

const qaPcProgramListRequest = createAction(QA_PC_PROGRAM_LIST_REQUEST, 'requestBody');
const qaPcProgramListSuccess = createAction(
  QA_PC_PROGRAM_LIST_SUCCESS,
  'data',
  'query',
  'currentPaginationCount'
);
const qaPcProgramListFailure = createAction(QA_PC_PROGRAM_LIST_FAILURE, 'error');

export function getProgramListForQAPC({ params, currentPaginationCount }) {
  return function (dispatch) {
    dispatch(qaPcProgramListRequest());
    axios
      .get('/qapcCategoryForm_v2/getProgramList', {
        params,
      })
      .then((res) => {
        dispatch(qaPcProgramListSuccess(res.data.data, params.searchKey, currentPaginationCount));
      })
      .catch((error) => dispatch(qaPcProgramListFailure(error)));
  };
}

export const GET_SINGLE_FORM_OCCURRENCE_REQUEST = 'GET_SINGLE_FORM_OCCURRENCE_REQUEST';
export const GET_SINGLE_FORM_OCCURRENCE_SUCCESS = 'GET_SINGLE_FORM_OCCURRENCE_SUCCESS';
export const GET_SINGLE_FORM_OCCURRENCE_FAILURE = 'GET_SINGLE_FORM_OCCURRENCE_FAILURE';

const getSingleFormOccurrenceRequest = createAction(GET_SINGLE_FORM_OCCURRENCE_REQUEST);
const getSingleFormOccurrenceSuccess = createAction(
  GET_SINGLE_FORM_OCCURRENCE_SUCCESS,
  'data',
  'formId'
);
const getSingleFormOccurrenceFailure = createAction(GET_SINGLE_FORM_OCCURRENCE_FAILURE);

export function getSingleFormOccurrence(id) {
  return function (dispatch) {
    dispatch(getSingleFormOccurrenceRequest());
    axios
      .get(`/qapcCategoryForm_v2/singleFormOccurrence?categoryFormId=${id}`)
      .then((res) => {
        dispatch(getSingleFormOccurrenceSuccess(res.data.data, id));
      })
      .catch((error) => dispatch(getSingleFormOccurrenceFailure(error)));
  };
}

export const GET_MATCHING_FORM_REQUEST = 'GET_MATCHING_FORM_REQUEST';
export const GET_MATCHING_FORM_SUCCESS = 'GET_MATCHING_FORM_SUCCESS';
export const GET_MATCHING_FORM_FAILURE = 'GET_MATCHING_FORM_FAILURE';

const getMatchingFormsRequest = createAction(GET_MATCHING_FORM_REQUEST);
const getMatchingFormsSuccess = createAction(GET_MATCHING_FORM_SUCCESS);
const getMatchingFormsFailure = createAction(GET_MATCHING_FORM_FAILURE, 'error');

export function getMatchingForms(url, cb) {
  return function (dispatch) {
    dispatch(getMatchingFormsRequest());
    axios
      .get(url)
      .then((res) => {
        dispatch(getMatchingFormsSuccess());
        cb && cb(fromJS(res.data.data));
      })
      .catch((error) => dispatch(getMatchingFormsFailure(error)));
  };
}

export const MULTIPLE_FILE_UPLOAD_URL_REQUEST = 'MULTIPLE_FILE_UPLOAD_URL_REQUEST';
export const MULTIPLE_FILE_UPLOAD_URL_SUCCESS = 'MULTIPLE_FILE_UPLOAD_URL_SUCCESS';
export const MULTIPLE_FILE_UPLOAD_URL_FAILURE = 'MULTIPLE_FILE_UPLOAD_URL_FAILURE';

const multipleFileUploadRequest = createAction(MULTIPLE_FILE_UPLOAD_URL_REQUEST);
const multipleFileUploadSuccess = createAction(MULTIPLE_FILE_UPLOAD_URL_SUCCESS, 'data');
const multipleFileUploadFailure = createAction(MULTIPLE_FILE_UPLOAD_URL_FAILURE, 'error');

export function multipleFileUpload(payload, cb) {
  return function (dispatch) {
    dispatch(multipleFileUploadRequest());
    axios
      .post('/qapcCategoryForm_v2/qapcGuideResources', payload)
      .then((res) => {
        const immutableResponse = fromJS(res.data.data);
        dispatch(multipleFileUploadSuccess());
        cb && cb(immutableResponse);
      })
      .catch((error) => dispatch(multipleFileUploadFailure(error)));
  };
}

export const GET_SIGNED_URL_REQUEST = 'GET_SIGNED_URL_REQUEST';
export const GET_SIGNED_URL_SUCCESS = 'GET_SIGNED_URL_SUCCESS';
export const GET_SIGNED_URL_FAILURE = 'GET_SIGNED_URL_FAILURE';

const getSignedUrlRequest = createAction(GET_SIGNED_URL_REQUEST);
const getSignedUrlSuccess = createAction(GET_SIGNED_URL_SUCCESS, 'data');
const getSignedUrlFailure = createAction(GET_SIGNED_URL_FAILURE, 'error');

export function getSignedUrl(url, cb) {
  return function (dispatch) {
    dispatch(getSignedUrlRequest());
    axios
      .get(`/qapcCategoryForm_v2/qapcSignedUrl?url=${url}`)
      .then((res) => {
        cb & cb(res.data.data);
        dispatch(getSignedUrlSuccess());
      })
      .catch((error) => dispatch(getSignedUrlFailure(error)));
  };
}

export const GET_TERM_LIST_REQUEST = 'GET_TERM_LIST_REQUEST';
export const GET_TERM_LIST_SUCCESS = 'GET_TERM_LIST_SUCCESS';
export const GET_TERM_LIST_FAILURE = 'GET_TERM_LIST_FAILURE';

const getTermListRequest = createAction(GET_TERM_LIST_REQUEST);
const getTermListSuccess = createAction(GET_TERM_LIST_SUCCESS, 'data');
const getTermListFailure = createAction(GET_TERM_LIST_FAILURE, 'error');

export function getTermList(programId) {
  return function (dispatch) {
    dispatch(getTermListRequest());
    axios
      .get(`qapcCategoryForm_v2/termList?programId=${programId}`)
      .then((res) => {
        dispatch(getTermListSuccess(res.data.data));
      })
      .catch((error) => dispatch(getTermListFailure(error)));
  };
}

export const POST_CONFIGURE_REQUEST = 'POST_CONFIGURE_REQUEST';
export const POST_CONFIGURE_SUCCESS = 'POST_CONFIGURE_SUCCESS';
export const POST_CONFIGURE_FAILURE = 'POST_CONFIGURE_FAILURE';

const postConfigureRequest = createAction(POST_CONFIGURE_REQUEST);
const postConfigureSuccess = createAction(POST_CONFIGURE_SUCCESS, 'data');
const postConfigureFailure = createAction(POST_CONFIGURE_FAILURE, 'error');

export function postConfigure(requestBody, cb) {
  return function (dispatch) {
    dispatch(postConfigureRequest());
    axios
      .post(`qapcCategoryForm_v2/createGroups`, requestBody)
      .then((res) => {
        dispatch(postConfigureSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(postConfigureFailure(error)));
  };
}

export const GET_FORM_CONFIGURATION_LIST_REQUEST = 'GET_FORM_CONFIGURATION_LIST_REQUEST';
export const GET_FORM_CONFIGURATION_LIST_SUCCESS = 'GET_FORM_CONFIGURATION_LIST_SUCCESS';
export const GET_FORM_CONFIGURATION_LIST_FAILURE = 'GET_FORM_CONFIGURATION_LIST_FAILURE';

const getFormConfigurationListRequest = createAction(GET_FORM_CONFIGURATION_LIST_REQUEST);
const getFormConfigurationListSuccess = createAction(GET_FORM_CONFIGURATION_LIST_SUCCESS, 'data');
const getFormConfigurationListFailure = createAction(GET_FORM_CONFIGURATION_LIST_FAILURE, 'error');

export function getFormConfigurationList(categoryFormCourseId, cb) {
  return function (dispatch) {
    dispatch(getFormConfigurationListRequest());
    axios
      .get(`qapcCategoryForm_v2/formConfigureList?categoryFormCourseId=${categoryFormCourseId}`)
      .then((res) => {
        dispatch(getFormConfigurationListSuccess(res.data.data));
        const data = fromJS(res.data.data);
        cb && cb(data);
      })
      .catch((error) => dispatch(getFormConfigurationListFailure(error)));
  };
}

export const UPDATE_CONFIGURE_REQUEST = 'UPDATE_CONFIGURE_REQUEST';
export const UPDATE_CONFIGURE_SUCCESS = 'UPDATE_CONFIGURE_SUCCESS';
export const UPDATE_CONFIGURE_FAILURE = 'UPDATE_CONFIGURE_FAILURE';

const updateConfigureRequest = createAction(UPDATE_CONFIGURE_REQUEST);
const updateConfigureSuccess = createAction(UPDATE_CONFIGURE_SUCCESS, 'data');
const updateConfigureFailure = createAction(UPDATE_CONFIGURE_FAILURE, 'error');

export function updateConfigure(requestBody, cb) {
  return function (dispatch) {
    dispatch(updateConfigureRequest());
    axios
      .put(`qapcCategoryForm_v2/editConfigure`, requestBody)
      .then((res) => {
        dispatch(updateConfigureSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(updateConfigureFailure(error)));
  };
}

export const GET_FORM_CONFIGURE_LIST_REQUEST = 'GET_FORM_CONFIGURE_LIST_REQUEST';
export const GET_FORM_CONFIGURE_LIST_SUCCESS = 'GET_FORM_CONFIGURE_LIST_SUCCESS';
export const GET_FORM_CONFIGURE_LIST_FAILURE = 'GET_FORM_CONFIGURE_LIST_FAILURE';

const getFormConfigureListRequest = createAction(GET_FORM_CONFIGURE_LIST_REQUEST);
const getFormConfigureListSuccess = createAction(GET_FORM_CONFIGURE_LIST_SUCCESS, 'data', 'id');
const getFormConfigureListFailure = createAction(GET_FORM_CONFIGURE_LIST_FAILURE, 'error');

export function getFormConfigureList(id) {
  return function (dispatch) {
    dispatch(getFormConfigureListRequest());
    axios
      .get(`qapcCategoryForm_v2/formConfigureList?categoryFormCourseId=${id}`)
      .then((res) => {
        dispatch(getFormConfigureListSuccess(res.data.data, id));
      })
      .catch((error) => dispatch(getFormConfigureListFailure(error)));
  };
}

export const GET_SINGLE_FORM_LIST_REQUEST = 'GET_SINGLE_FORM_LIST_REQUEST';
export const GET_SINGLE_FORM_LIST_SUCCESS = 'GET_SINGLE_FORM_LIST_SUCCESS';
export const GET_SINGLE_FORM_LIST_FAILURE = 'GET_SINGLE_FORM_LIST_FAILURE';

const getSingleFormListRequest = createAction(GET_SINGLE_FORM_LIST_REQUEST);
const getSingleFormListSuccess = createAction(GET_SINGLE_FORM_LIST_SUCCESS, 'data');
const getSingleFormListFailure = createAction(GET_SINGLE_FORM_LIST_FAILURE, 'error');

export function getSingleFormList({ formInitiatorId, cb }) {
  return function (dispatch) {
    dispatch(getSingleFormListRequest());
    axios
      .get('/formInitiator/singleFormList?formInitiatorId=' + formInitiatorId)
      .then((res) => {
        dispatch(getSingleFormListSuccess(res.data.data));
        cb && cb(fromJS(res.data.data));
      })
      .catch((error) => dispatch(getSingleFormListFailure(error)));
  };
}

export const GET_FORM_ADDENDUM_REQUEST = 'GET_FORM_ADDENDUM_REQUEST';
export const GET_FORM_ADDENDUM_SUCCESS = 'GET_FORM_ADDENDUM_SUCCESS';
export const GET_FORM_ADDENDUM_FAILURE = 'GET_FORM_ADDENDUM_FAILURE';

const getFormAddendumRequest = createAction(GET_FORM_ADDENDUM_REQUEST);
const getFormAddendumSuccess = createAction(GET_FORM_ADDENDUM_SUCCESS, 'data');
const getFormAddendumFailure = createAction(GET_FORM_ADDENDUM_FAILURE, 'error');

export function getFormAddendum({ formInitiatorId, cb }) {
  return function (dispatch) {
    dispatch(getFormAddendumRequest());
    axios
      .get('/formInitiator/getFormAttachment?formInitiatorId=' + formInitiatorId)
      .then((res) => {
        const data = res.data.data ?? Map();
        dispatch(getFormAddendumSuccess(data));
        cb && cb(fromJS(data));
      })
      .catch((error) => dispatch(getFormAddendumFailure(error)));
  };
}

export const UPDATE_FORM_CONFIGURE_REQUEST = 'UPDATE_FORM_CONFIGURE_REQUEST';
export const UPDATE_FORM_CONFIGURE_SUCCESS = 'UPDATE_FORM_CONFIGURE_SUCCESS';
export const UPDATE_FORM_CONFIGURE_FAILURE = 'UPDATE_FORM_CONFIGURE_FAILURE';

const updateFormConfigRequest = createAction(UPDATE_FORM_CONFIGURE_REQUEST, 'requestBody');
const updateFormConfigSuccess = createAction(UPDATE_FORM_CONFIGURE_SUCCESS, 'data');
const updateFormConfigFailure = createAction(UPDATE_FORM_CONFIGURE_FAILURE, 'error');

export function updateFormConfig(payload, cb) {
  return function (dispatch) {
    dispatch(updateFormConfigRequest());
    axios
      .put('/formInitiator/formAttachment', payload)
      .then((res) => {
        dispatch(updateFormConfigSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => {
        // const statusCode = error.response.status;
        dispatch(updateFormConfigFailure(error));
        cb && cb();
      });
  };
}

export const GET_FORM_SETTING_LIST_REQUEST = 'GET_FORM_SETTING_LIST_REQUEST';
export const GET_FORM_SETTING_LIST_SUCCESS = 'GET_FORM_SETTING_LIST_SUCCESS';
export const GET_FORM_SETTING_LIST_FAILURE = 'GET_FORM_SETTING_LIST_FAILURE';

const getFormSettingListRequest = createAction(GET_FORM_SETTING_LIST_REQUEST);
const getFormSettingListSuccess = createAction(GET_FORM_SETTING_LIST_SUCCESS, 'data');
const getFormSettingListFailure = createAction(GET_FORM_SETTING_LIST_FAILURE, 'error');

export function getFormSettingList(subModuleType = 'Form Initiator', callBack) {
  return function (dispatch) {
    dispatch(getFormSettingListRequest());
    axios
      .get(`formInitiator/getCreateForm?subModuleType=${subModuleType}`)
      .then((res) => {
        dispatch(getFormSettingListSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((error) => dispatch(getFormSettingListFailure(error)));
  };
}

export const GET_ACADEMIC_YEARS_REQUEST = 'GET_ACADEMIC_YEARS_REQUEST';
export const GET_ACADEMIC_YEARS_SUCCESS = 'GET_ACADEMIC_YEARS_SUCCESS';
export const GET_ACADEMIC_YEARS_FAILURE = 'GET_ACADEMIC_YEARS_FAILURE';

const getAcademicYearsRequest = createAction(GET_ACADEMIC_YEARS_REQUEST);
const getAcademicYearsSuccess = createAction(GET_ACADEMIC_YEARS_SUCCESS, 'data');
// const getAcademicYearsFailure = createAction(GET_ACADEMIC_YEARS_FAILURE, 'error');

export function getAcademicYears() {
  const storedData = LocalStorageService.getCustomToken('rolePermission_qapcAcademicYear', true);
  if (storedData) {
    return function (dispatch) {
      dispatch(setData(fromJS({ academicYears: storedData })));
    };
  }
  return function (dispatch) {
    dispatch(getAcademicYearsRequest());
    axios
      .get('rolePermission/getAcademicYear')
      .then((res) => {
        const concatInstitutionCalendarLists = getConcatCalendarList({ response: res.data.data });
        dispatch(getAcademicYearsSuccess(concatInstitutionCalendarLists));
        LocalStorageService.setCustomToken(
          'rolePermission_qapcAcademicYear',
          JSON.stringify(concatInstitutionCalendarLists)
        );
      })
      .catch((error) => {
        const concatInstitutionCalendarLists = getConcatCalendarList({ response: [] });
        LocalStorageService.setCustomToken(
          'rolePermission_qapcAcademicYear',
          JSON.stringify(concatInstitutionCalendarLists)
        );
        dispatch(getAcademicYearsSuccess(concatInstitutionCalendarLists));
        // dispatch(getAcademicYearsFailure(error));
      });
  };
}

export const UPDATE_FORM_REQUEST = 'UPDATE_FORM_REQUEST';
export const UPDATE_FORM_SUCCESS = 'UPDATE_FORM_SUCCESS';
export const UPDATE_FORM_FAILURE = 'UPDATE_FORM_FAILURE';

const updateFormRequest = createAction(UPDATE_FORM_REQUEST);
const updateFormSuccess = createAction(UPDATE_FORM_SUCCESS, 'data');
const updateFormFailure = createAction(UPDATE_FORM_FAILURE, 'error');

export function updateForm(requestData, cb, operation = 'create') {
  const method = {
    create: 'post',
    update: 'put',
  };
  return function (dispatch) {
    dispatch(updateFormRequest());
    axios[method[operation]](
      `formInitiator/${operation === 'create' ? 'createNewForm' : 'updateCreateNewForm'}`,
      requestData
    )
      .then((res) => {
        dispatch(updateFormSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(updateFormFailure(error)));
  };
}

export const GET_FORM_LIST_REQUEST = 'GET_FORM_LIST_REQUEST';
export const GET_FORM_LIST_SUCCESS = 'GET_FORM_LIST_SUCCESS';
export const GET_FORM_LIST_FAILURE = 'GET_FORM_LIST_FAILURE';

const getFormListRequest = createAction(GET_FORM_LIST_REQUEST);
const getFormListSuccess = createAction(GET_FORM_LIST_SUCCESS, 'data');
const getFormListFailure = createAction(GET_FORM_LIST_FAILURE, 'error');

export function getFormList(params, cb) {
  return function (dispatch) {
    dispatch(getFormListRequest());
    axios
      .get(`formInitiator/qapcLogUserList`, { params })
      .then((res) => {
        dispatch(getFormListSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(getFormListFailure(error)));
  };
}

export const UPDATE_FORM_STATUS_REQUEST = 'UPDATE_FORM_STATUS_REQUEST';
export const UPDATE_FORM_STATUS_SUCCESS = 'UPDATE_FORM_STATUS_SUCCESS';
export const UPDATE_FORM_STATUS_FAILURE = 'UPDATE_FORM_STATUS_FAILURE';

const updateFormStatusRequest = createAction(UPDATE_FORM_STATUS_REQUEST);
const updateFormStatusSuccess = createAction(UPDATE_FORM_STATUS_SUCCESS, 'data', 'message');
const updateFormStatusFailure = createAction(UPDATE_FORM_STATUS_FAILURE, 'error');

export function updateFormStatus(requestBody, cb, type) {
  return function (dispatch) {
    dispatch(updateFormStatusRequest());
    axios
      .put(`formInitiator/updateFormStatus`, requestBody)
      .then((res) => {
        dispatch(updateFormStatusSuccess(res.data.data, type));
        cb && cb();
      })
      .catch((error) => dispatch(updateFormStatusFailure(error)));
  };
}

export const GET_SINGLE_FORM_REQUEST = 'GET_SINGLE_FORM_REQUEST';
export const GET_SINGLE_FORM_SUCCESS = 'GET_SINGLE_FORM_SUCCESS';
export const GET_SINGLE_FORM_FAILURE = 'GET_SINGLE_FORM_FAILURE';

const getSingleFormRequest = createAction(GET_SINGLE_FORM_REQUEST);
const getSingleFormSuccess = createAction(GET_SINGLE_FORM_SUCCESS, 'data');
const getSingleFormFailure = createAction(GET_SINGLE_FORM_FAILURE, 'error');

export function getSingleForm(id) {
  return function (dispatch) {
    dispatch(getSingleFormRequest());
    axios
      .get(`formInitiator/singleFormList?formInitiatorId=${id}`)
      .then((res) => {
        dispatch(getSingleFormSuccess(res.data.data));
      })
      .catch((error) => dispatch(getSingleFormFailure(error)));
  };
}

export const GET_SETTING_TAGS_REQUEST = 'GET_SETTING_TAGS_REQUEST';
export const GET_SETTING_TAGS_SUCCESS = 'GET_SETTING_TAGS_SUCCESS';
export const GET_SETTING_TAGS_FAILURE = 'GET_SETTING_TAGS_FAILURE';

const getSettingTagsRequest = createAction(GET_SETTING_TAGS_REQUEST);
const getSettingTagsSuccess = createAction(GET_SETTING_TAGS_SUCCESS, 'data');
const getSettingTagsFailure = createAction(GET_SETTING_TAGS_FAILURE, 'error');

export function getSettingTags(id, level) {
  return function (dispatch) {
    dispatch(getSettingTagsRequest());
    axios
      .get(`/formInitiator/settingTag?categoryFormCourseId=${id}&formLevel=${level}`)
      .then((res) => {
        const data = res.data.data;
        dispatch(getSettingTagsSuccess(data));
      })
      .catch((error) => dispatch(getSettingTagsFailure(error)));
  };
}

//InCorporate Section Start
export const GET_INCORPORATE_FILTER_REQUEST = 'GET_INCORPORATE_FILTER_REQUEST';
export const GET_INCORPORATE_FILTER_SUCCESS = 'GET_INCORPORATE_FILTER_SUCCESS';
export const GET_INCORPORATE_FILTER_FAILURE = 'GET_INCORPORATE_FILTER_FAILURE';

// const getIncorporateFilterDataRequest = createAction(GET_INCORPORATE_FILTER_REQUEST);
const getIncorporateFilterDataSuccess = createAction(
  GET_INCORPORATE_FILTER_SUCCESS,
  'data',
  'storeKey'
);
const getIncorporateFilterDataFailure = createAction(GET_INCORPORATE_FILTER_FAILURE, 'error');

export function getIncorporateFilterData({
  endPoint,
  storeKey,
  carryResponseToInvokedPlace,
  loadingStatus,
  isApiCalledDone,
}) {
  return async function (dispatch) {
    try {
      loadingStatus && loadingStatus(true);
      const res = await axios.get('/formInitiator/' + endPoint);
      const immutableData = fromJS(res.data.data);
      dispatch(getIncorporateFilterDataSuccess(immutableData, storeKey));
      carryResponseToInvokedPlace && carryResponseToInvokedPlace(immutableData);
    } catch (e) {
      dispatch(getIncorporateFilterDataFailure(e));
    } finally {
      loadingStatus && loadingStatus(false);
      isApiCalledDone && isApiCalledDone();
    }
  };
}

export const GET_INCORPORATE_SECTION_REQUEST = 'GET_INCORPORATE_SECTION_REQUEST';
export const GET_INCORPORATE_SECTION_SUCCESS = 'GET_INCORPORATE_SECTION_SUCCESS';
export const GET_INCORPORATE_SECTION_FAILURE = 'GET_INCORPORATE_SECTION_FAILURE';

const getIncorporateSectionDataRequest = createAction(GET_INCORPORATE_SECTION_REQUEST);
const getIncorporateSectionDataSuccess = createAction(
  GET_INCORPORATE_SECTION_SUCCESS,
  'data',
  'storeKey'
);
const getIncorporateSectionDataFailure = createAction(GET_INCORPORATE_SECTION_FAILURE, 'error');

export function getIncorporateSectionData({ params }) {
  return function (dispatch) {
    dispatch(getIncorporateSectionDataRequest());
    axios
      .get('/formInitiator/getIncorporateSection', {
        params,
      })
      .then((res) => {
        const data = res.data.data;
        dispatch(getIncorporateSectionDataSuccess(data));
      })
      .catch((error) => dispatch(getIncorporateSectionDataFailure(error)));
  };
}

export const SET_INCORPORATE_SECTION_REQUEST = 'SET_INCORPORATE_SECTION_REQUEST';
export const SET_INCORPORATE_SECTION_SUCCESS = 'SET_INCORPORATE_SECTION_SUCCESS';
export const SET_INCORPORATE_SECTION_FAILURE = 'SET_INCORPORATE_SECTION_FAILURE';

const setIncorporateSectionDataRequest = createAction(SET_INCORPORATE_SECTION_REQUEST);
const setIncorporateSectionDataSuccess = createAction(
  SET_INCORPORATE_SECTION_SUCCESS,
  'data',
  'storeKey'
);
const setIncorporateSectionDataFailure = createAction(SET_INCORPORATE_SECTION_FAILURE, 'error');

export function updateIncorporateSectionData({ url, payload, params, successCallback }) {
  return function (dispatch) {
    dispatch(setIncorporateSectionDataRequest());
    axios
      .put(url, payload)
      .then((res) => {
        const data = res.data.data;
        dispatch(setIncorporateSectionDataSuccess(data));
        dispatch(getIncorporateSectionData({ params }));
        successCallback && successCallback();
      })
      .catch((error) => dispatch(setIncorporateSectionDataFailure(error)));
  };
}

export const GET_RP_ACTION_REQUEST = 'GET_RP_ACTION_REQUEST';
export const GET_RP_ACTION_SUCCESS = 'GET_RP_ACTION_SUCCESS';
export const GET_RP_ACTION_FAILURE = 'GET_RP_ACTION_FAILURE';

const getRpActionRequest = createAction(GET_RP_ACTION_REQUEST);
const getRpActionSuccess = createAction(GET_RP_ACTION_SUCCESS, 'data');
const getRpActionFailure = createAction(GET_RP_ACTION_FAILURE, 'error');

export function getRpAction() {
  const storedData = LocalStorageService.getCustomToken('approverActions', true);
  if (storedData) {
    return function (dispatch) {
      dispatch(setData(fromJS({ rpActions: storedData })));
    };
  }
  return function (dispatch) {
    const response = LocalStorageService.getCustomToken('rolePermission_qapcAction');
    dispatch(getRpActionRequest());
    if (!response) {
      axios
        .get(`/rolePermission/qapcAction`)
        .then((res) => {
          LocalStorageService.setCustomToken(
            'rolePermission_qapcAction',
            JSON.stringify(res.data.data)
          );
          dispatch(getRpActionSuccess(res.data.data));
        })
        .catch((error) => dispatch(getRpActionFailure(error)));
    } else {
      let responseParse = JSON.parse(response);
      dispatch(getRpActionSuccess(responseParse));
    }
  };
}

export const GET_QAPC_ROLES_REQUEST = 'GET_QAPC_ROLES_REQUEST';
export const GET_QAPC_ROLES_SUCCESS = 'GET_QAPC_ROLES_SUCCESS';
export const GET_QAPC_ROLES_FAILURE = 'GET_QAPC_ROLES_FAILURE';

const getQapcRolesRequest = createAction(GET_QAPC_ROLES_REQUEST);
const getQapcRolesSuccess = createAction(GET_QAPC_ROLES_SUCCESS, 'data');
const getQapcRolesFailure = createAction(GET_QAPC_ROLES_FAILURE, 'error');

export function getQapcRoles(cb) {
  return function (dispatch) {
    dispatch(getQapcRolesRequest());
    axios
      .get(`/rolePermission/getUserQAPCRole`)
      .then((res) => {
        dispatch(getQapcRolesSuccess(res.data.data));
        cb && cb(res.data.data.qapcRoleIds);
      })
      .catch((error) => dispatch(getQapcRolesFailure(error)));
  };
}

export const GET_REF_ACADEMIC_YEAR_REQUEST = 'GET_REF_ACADEMIC_YEAR_REQUEST';
export const GET_REF_ACADEMIC_YEAR_SUCCESS = 'GET_REF_ACADEMIC_YEAR_SUCCESS';
export const GET_REF_ACADEMIC_YEAR_FAILURE = 'GET_REF_ACADEMIC_YEAR_FAILURE';

const getAcademicYearRequest = createAction(GET_REF_ACADEMIC_YEAR_REQUEST);
const getAcademicYearSuccess = createAction(GET_REF_ACADEMIC_YEAR_SUCCESS, 'data');
const getAcademicYearFailure = createAction(GET_REF_ACADEMIC_YEAR_FAILURE, 'error');

export const getReferDocAcademicYear = ({ level }) => {
  return (dispatch) => {
    dispatch(getAcademicYearRequest());
    axios
      .get(`formInitiator/referenceDocumentAcademicYear?level=${level}`)
      .then((res) => {
        dispatch(
          getAcademicYearSuccess(
            res.data.data?.uniqueCalenderIds.length > 0 ? res.data.data.uniqueCalenderIds : {}
          )
        );
      })
      .catch((error) => {
        dispatch(getAcademicYearFailure(error));
      });
  };
};

export const SEARCH_REFERENCE_DOCUMENT_REQUEST = 'SEARCH_REFERENCE_DOCUMENT_REQUEST';
export const SEARCH_REFERENCE_DOCUMENT_SUCCESS = 'SEARCH_REFERENCE_DOCUMENT_SUCCESS';
export const SEARCH_REFERENCE_DOCUMENT_FAILURE = 'SEARCH_REFERENCE_DOCUMENT_FAILURE';

const searchReferenceDocumentRequest = createAction(SEARCH_REFERENCE_DOCUMENT_REQUEST);
const searchReferenceDocumentSuccess = createAction(SEARCH_REFERENCE_DOCUMENT_SUCCESS, 'data');
const searchReferenceDocumentFailure = createAction(SEARCH_REFERENCE_DOCUMENT_FAILURE, 'error');

export const searchReferenceDocument = (payload) => {
  return (dispatch) => {
    dispatch(searchReferenceDocumentRequest());
    axios
      .get('formInitiator/searchReferenceDocument', { params: payload })
      .then((res) => {
        dispatch(searchReferenceDocumentSuccess(res.data.data));
      })
      .catch((error) => {
        dispatch(searchReferenceDocumentFailure(error));
      });
  };
};

export const GET_REFERENCE_FORM_ATTACHMENT_REQUEST = 'GET_REFERENCE_FORM_ATTACHMENT_REQUEST';
export const GET_REFERENCE_FORM_ATTACHMENT_SUCCESS = 'GET_REFERENCE_FORM_ATTACHMENT_SUCCESS';
export const GET_REFERENCE_FORM_ATTACHMENT_FAILURE = 'GET_REFERENCE_FORM_ATTACHMENT_FAILURE';

const getReferenceFormAttachmentRequest = createAction(GET_REFERENCE_FORM_ATTACHMENT_REQUEST);
const getReferenceFormAttachmentSuccess = createAction(
  GET_REFERENCE_FORM_ATTACHMENT_SUCCESS,
  'data',
  'id'
);
const getReferenceFormAttachmentFailure = createAction(
  GET_REFERENCE_FORM_ATTACHMENT_FAILURE,
  'error'
);
export const getReferenceFormAttachment = ({ formInitiatorId, callBack }) => {
  return (dispatch) => {
    dispatch(getReferenceFormAttachmentRequest());
    axios
      .get(`/formInitiator/referenceFormAttachment?formInitiatorId=${formInitiatorId}`)
      .then((res) => {
        dispatch(getReferenceFormAttachmentSuccess(res.data.data, formInitiatorId));
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(getReferenceFormAttachmentFailure(error));
      });
  };
};

export const GET_INCORPORATE_FROM_WITH_REQUEST = 'GET_INCORPORATE_FROM_WITH_REQUEST';
export const GET_INCORPORATE_FROM_WITH_SUCCESS = 'GET_INCORPORATE_FROM_WITH_SUCCESS';
export const GET_INCORPORATE_FROM_WITH_FAILURE = 'GET_INCORPORATE_FROM_WITH_FAILURE';

const getIncorporateFromWithRequest = createAction(GET_INCORPORATE_FROM_WITH_REQUEST);
const getIncorporateFromWithSuccess = createAction(GET_INCORPORATE_FROM_WITH_SUCCESS, 'data', 'id');
const getIncorporateFromWithFailure = createAction(GET_INCORPORATE_FROM_WITH_FAILURE, 'error');
export const getIncorporateFromWithData = ({ url }) => {
  return (dispatch) => {
    dispatch(getIncorporateFromWithRequest());
    axios
      .get(url)
      .then((res) => {
        dispatch(getIncorporateFromWithSuccess(res.data.data));
      })
      .catch((error) => {
        dispatch(getIncorporateFromWithFailure(error));
      });
  };
};

export const GET_TODO_MISSED_REQUEST = 'GET_TODO_MISSED_REQUEST';
export const GET_TODO_MISSED_SUCCESS = 'GET_TODO_MISSED_SUCCESS';
export const GET_TODO_MISSED_FAILURE = 'GET_TODO_MISSED_FAILURE';

const getTodoMissedRequest = createAction(GET_TODO_MISSED_REQUEST);
const getTodoMissedSuccess = createAction(GET_TODO_MISSED_SUCCESS, 'data');
const getTodoMissedFailure = createAction(GET_TODO_MISSED_FAILURE, 'error');

export const getTodoMissedData = (payload) => {
  return (dispatch) => {
    dispatch(getTodoMissedRequest());
    axios
      .get('/formInitiator/todoMissed', { params: payload })
      .then((res) => {
        dispatch(getTodoMissedSuccess(res.data.data));
      })
      .catch((error) => {
        dispatch(getTodoMissedFailure(error));
      });
  };
};

export const GET_CATEGORY_LIST_REQUEST = 'GET_CATEGORY_LIST_REQUEST';
export const GET_CATEGORY_LIST_SUCCESS = 'GET_CATEGORY_LIST_SUCCESS';
export const GET_CATEGORY_LIST_FAILURE = 'GET_CATEGORY_LIST_FAILURE';

const getCategoryListRequest = createAction(GET_CATEGORY_LIST_REQUEST);
const getCategoryListSuccess = createAction(GET_CATEGORY_LIST_SUCCESS, 'data');
const getCategoryListFailure = createAction(GET_CATEGORY_LIST_FAILURE, 'error');

export const getCategoryListData = () => {
  return (dispatch) => {
    dispatch(getCategoryListRequest());
    axios
      .get('/formInitiator/categoryList')
      .then((res) => {
        dispatch(getCategoryListSuccess(res.data.data));
      })
      .catch((error) => {
        dispatch(getCategoryListFailure(error));
      });
  };
};

export const GET_EVIDENCE_DOCUMENT_REQUEST = 'GET_EVIDENCE_DOCUMENT_REQUEST';
export const GET_EVIDENCE_DOCUMENT_SUCCESS = 'GET_EVIDENCE_DOCUMENT_SUCCESS';
export const GET_EVIDENCE_DOCUMENT_FAILURE = 'GET_EVIDENCE_DOCUMENT_FAILURE';

const getEvidenceDocumentsRequest = createAction(GET_EVIDENCE_DOCUMENT_REQUEST);
const getEvidenceDocumentsSuccess = createAction(
  GET_EVIDENCE_DOCUMENT_SUCCESS,
  'data',
  'formInitiatorId'
);
const getEvidenceDocumentsFailure = createAction(GET_EVIDENCE_DOCUMENT_FAILURE, 'error');

export const getEvidenceDocuments = ({ formInitiatorId }) => {
  return (dispatch) => {
    dispatch(getEvidenceDocumentsRequest());
    axios
      .get('/formInitiator/getFormViewAttachment?formInitiatorId=' + formInitiatorId)
      .then((res) => {
        dispatch(getEvidenceDocumentsSuccess(res.data.data, formInitiatorId));
      })
      .catch((error) => {
        dispatch(getEvidenceDocumentsFailure(error));
      });
  };
};

export const GET_APPROVAL_CATEGORY_LIST_REQUEST = 'GET_APPROVAL_CATEGORY_LIST_REQUEST';
export const GET_APPROVAL_CATEGORY_LIST_SUCCESS = 'GET_APPROVAL_CATEGORY_LIST_SUCCESS';
export const GET_APPROVAL_CATEGORY_LIST_FAILURE = 'GET_APPROVAL_CATEGORY_LIST_FAILURE';
const getApprovalCategoryListRequest = createAction(GET_APPROVAL_CATEGORY_LIST_REQUEST);
const getApprovalCategoryListSuccess = createAction(GET_APPROVAL_CATEGORY_LIST_SUCCESS, 'data');
const getApprovalCategoryListFailure = createAction(GET_APPROVAL_CATEGORY_LIST_FAILURE, 'error');
export const getApprovalCategoryList = (payload, cb) => {
  return (dispatch) => {
    dispatch(getApprovalCategoryListRequest());
    axios
      .get('/formApprover/approverCategoryList', { params: payload })
      .then((res) => {
        dispatch(getApprovalCategoryListSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => {
        dispatch(getApprovalCategoryListFailure(error));
      });
  };
};
export const GET_APPROVER_LIST_REQUEST = 'GET_APPROVER_LIST_REQUEST';
export const GET_APPROVER_LIST_SUCCESS = 'GET_APPROVER_LIST_SUCCESS';
export const GET_APPROVER_LIST_FAILURE = 'GET_APPROVER_LIST_FAILURE';

const getApproverListRequest = createAction(GET_APPROVER_LIST_REQUEST);
const getApproverListSuccess = createAction(GET_APPROVER_LIST_SUCCESS, 'data', 'categoryId');
const getApproverListFailure = createAction(GET_APPROVER_LIST_FAILURE, 'error');

export const getApproverList = (params) => {
  const { categoryId = '' } = params;
  return (dispatch) => {
    dispatch(getApproverListRequest());
    axios
      .get('/formApprover/qualityAssuranceApproverList', { params })
      .then((res) => {
        dispatch(getApproverListSuccess(res.data.data, categoryId));
      })
      .catch((error) => {
        dispatch(getApproverListFailure(error));
      });
  };
};

export const GET_FROM_APPLICATION_LIST_REQUEST = 'GET_FROM_APPLICATION_LIST_REQUEST';
export const GET_FROM_APPLICATION_LIST_SUCCESS = 'GET_FROM_APPLICATION_LIST_SUCCESS';
export const GET_FROM_APPLICATION_LIST_FAILURE = 'GET_FROM_APPLICATION_LIST_FAILURE';

const getFromApplicationListRequest = createAction(GET_FROM_APPLICATION_LIST_REQUEST);
const getFromApplicationListSuccess = createAction(
  GET_FROM_APPLICATION_LIST_SUCCESS,
  'data',
  'categoryId'
);
const getFromApplicationListFailure = createAction(GET_FROM_APPLICATION_LIST_FAILURE, 'error');

export const getFromApplicationList = (payload) => {
  const { formInitiatorIds = [], categoryTab = '' } = payload;
  return (dispatch) => {
    dispatch(getFromApplicationListRequest());
    axios
      .get('/formApprover/formApplicationList', { params: { formInitiatorIds } })
      .then((res) => {
        dispatch(getFromApplicationListSuccess(res.data.data, categoryTab));
      })
      .catch((error) => {
        dispatch(getFromApplicationListFailure(error));
      });
  };
};

export const GET_LEVEL_ROLE_USER_LIST_REQUEST = 'GET_LEVEL_ROLE_USER_LIST_REQUEST';
export const GET_LEVEL_ROLE_USER_LIST_SUCCESS = 'GET_LEVEL_ROLE_USER_LIST_SUCCESS';
export const GET_LEVEL_ROLE_USER_LIST_FAILURE = 'GET_LEVEL_ROLE_USER_LIST_FAILURE';

const getLevelRoleUserListRequest = createAction(GET_LEVEL_ROLE_USER_LIST_REQUEST);
const getLevelRoleUserListSuccess = createAction(
  GET_LEVEL_ROLE_USER_LIST_SUCCESS,
  'data',
  'formInitiatorId'
);
const getLevelRoleUserListFailure = createAction(GET_LEVEL_ROLE_USER_LIST_FAILURE, 'error');

export const getLevelRoleUserList = (params, cb) => {
  const { formInitiatorId = '' } = params;
  cb && cb();
  return (dispatch) => {
    dispatch(getLevelRoleUserListRequest());
    axios
      .get('/formApprover/levelRoleUserList', { params })
      .then((res) => {
        dispatch(getLevelRoleUserListSuccess(res.data.data, formInitiatorId));
      })
      .catch((error) => {
        dispatch(getLevelRoleUserListFailure(error));
      });
  };
};

export const PUT_UPDATE_APPROVER_USER_REQUEST = 'PUT_UPDATE_APPROVER_USER_REQUEST';
export const PUT_UPDATE_APPROVER_USER_SUCCESS = 'PUT_UPDATE_APPROVER_USER_SUCCESS';
export const PUT_UPDATE_APPROVER_USER_FAILURE = 'PUT_UPDATE_APPROVER_USER_FAILURE';

const putUpdateApproverUserRequest = createAction(PUT_UPDATE_APPROVER_USER_REQUEST);
const putUpdateApproverUserSuccess = createAction(PUT_UPDATE_APPROVER_USER_SUCCESS);
const putUpdateApproverUserFailure = createAction(PUT_UPDATE_APPROVER_USER_FAILURE, 'error');

export const updateApproverUser = (payload, callBack) => {
  const { status = '' } = payload;
  return (dispatch) => {
    dispatch(putUpdateApproverUserRequest());
    axios
      .put('/formApprover/updateApproverBy', payload)
      .then(() => {
        dispatch(putUpdateApproverUserSuccess());
        callBack && callBack(status);
      })
      .catch((error) => {
        dispatch(putUpdateApproverUserFailure(error));
      });
  };
};

export const GET_INCORPORATE_REQUEST = 'GET_INCORPORATE_REQUEST';
export const GET_INCORPORATE_SUCCESS = 'GET_INCORPORATE_SUCCESS';
export const GET_INCORPORATE_FAILURE = 'GET_INCORPORATE_FAILURE';

const getIncorporateRequest = createAction(GET_INCORPORATE_REQUEST);
const getIncorporateSuccess = createAction(GET_INCORPORATE_SUCCESS);
const getIncorporateFailure = createAction(GET_INCORPORATE_FAILURE, 'error');

export const getIncorporate = (payload, callBack) => {
  return (dispatch) => {
    dispatch(getIncorporateRequest());
    axios
      .get('/formInitiator/getIncorporate', { params: payload })
      .then((res) => {
        dispatch(getIncorporateSuccess());
        callBack && callBack(res.data.data);
      })
      .catch((error) => {
        dispatch(getIncorporateFailure(error));
      });
  };
};

export const QA_PC_DASHBOARD_CREATE_CATEGORY_REQUEST = 'QA_PC_DASHBOARD_CREATE_CATEGORY_REQUEST';
export const QA_PC_DASHBOARD_CREATE_CATEGORY_SUCCESS = 'QA_PC_DASHBOARD_CREATE_CATEGORY_SUCCESS';
export const QA_PC_DASHBOARD_CREATE_CATEGORY_FAILURE = 'QA_PC_DASHBOARD_CREATE_CATEGORY_FAILURE';

const qapcDashboardCreateItemRequest = createAction(
  QA_PC_DASHBOARD_CREATE_CATEGORY_REQUEST,
  'requestBody'
);
const qapcDashboardCreateItemSuccess = createAction(
  QA_PC_DASHBOARD_CREATE_CATEGORY_SUCCESS,
  'data'
);
const qapcDashboardCreateItemFailure = createAction(
  QA_PC_DASHBOARD_CREATE_CATEGORY_FAILURE,
  'error'
);

export function qapcDashboardCreateItem(payload, callBack) {
  return function (dispatch) {
    dispatch(qapcDashboardCreateItemRequest());
    axios
      .post('/qapcDashboard/createCategoryItems', payload)
      .then((res) => {
        dispatch(qapcDashboardCreateItemSuccess(res.data.data));
        callBack && callBack(res.data.data._id);
      })
      .catch((error) => dispatch(qapcDashboardCreateItemFailure(error)));
  };
}

export const QAPC_GET_DASHBOARD_SETTINGS_REQUEST = 'QAPC_GET_DASHBOARD_SETTINGS_REQUEST';
export const QAPC_GET_DASHBOARD_SETTINGS_SUCCESS = 'QAPC_GET_DASHBOARD_SETTINGS_SUCCESS';
export const QAPC_GET_DASHBOARD_SETTINGS_FAILURE = 'QAPC_GET_DASHBOARD_SETTINGS_FAILURE';

const getQapcDashboardSettingsRequest = createAction(
  QAPC_GET_DASHBOARD_SETTINGS_REQUEST,
  'requestBody'
);
const getQapcDashboardSettingsSuccess = createAction(QAPC_GET_DASHBOARD_SETTINGS_SUCCESS, 'data');
const getQapcDashboardSettingsFailure = createAction(QAPC_GET_DASHBOARD_SETTINGS_FAILURE, 'error');

export function getQapcDashboardSettings({ institutionCalendarId, callBack }) {
  return function (dispatch) {
    dispatch(getQapcDashboardSettingsRequest());
    axios
      .get(
        `/qapcDashboard/getDashboardCategorySettings?institutionCalendarId=${institutionCalendarId}`
      )
      .then((res) => {
        dispatch(getQapcDashboardSettingsSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(getQapcDashboardSettingsFailure(error));
        callBack && callBack();
      });
  };
}

export const QA_PC_DASHBOARD_UPDATE_CATEGORY_REQUEST = 'QA_PC_DASHBOARD_UPDATE_CATEGORY_REQUEST';
export const QA_PC_DASHBOARD_UPDATE_CATEGORY_SUCCESS = 'QA_PC_DASHBOARD_UPDATE_CATEGORY_SUCCESS';
export const QA_PC_DASHBOARD_UPDATE_CATEGORY_FAILURE = 'QA_PC_DASHBOARD_UPDATE_CATEGORY_FAILURE';

const qapcDashboardUpdateItemRequest = createAction(
  QA_PC_DASHBOARD_UPDATE_CATEGORY_REQUEST,
  'requestBody'
);
const qapcDashboardUpdateItemSuccess = createAction(
  QA_PC_DASHBOARD_UPDATE_CATEGORY_SUCCESS,
  'message'
);
const qapcDashboardUpdateItemFailure = createAction(
  QA_PC_DASHBOARD_UPDATE_CATEGORY_FAILURE,
  'error'
);

export function qapcDashboardUpdateItem(payload, callBack) {
  return function (dispatch) {
    dispatch(qapcDashboardUpdateItemRequest());
    axios
      .put('/qapcDashboard/updateCategoryItems', payload)
      .then((res) => {
        dispatch(qapcDashboardUpdateItemSuccess(res.data.message));
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(qapcDashboardUpdateItemFailure(error));
        callBack && callBack();
      });
  };
}

export const QAPC_GET_DASHBOARD_REQUEST = 'QAPC_GET_DASHBOARD_REQUEST';
export const QAPC_GET_DASHBOARD_SUCCESS = 'QAPC_GET_DASHBOARD_SUCCESS';
export const QAPC_GET_DASHBOARD_FAILURE = 'QAPC_GET_DASHBOARD_FAILURE';

const getQADashboardRequest = createAction(QAPC_GET_DASHBOARD_REQUEST, 'requestBody');
const getQADashboardSuccess = createAction(QAPC_GET_DASHBOARD_SUCCESS, 'data');
const getQADashboardFailure = createAction(QAPC_GET_DASHBOARD_FAILURE, 'error');

export function getQADashboardGraphData({
  institutionCalendarId,
  callBack,
  startMonth = 1,
  endMonth = 12,
  queryFormInitiatorIds = [],
  queryCategoryFormGroupIds = [],
}) {
  return function (dispatch) {
    dispatch(getQADashboardRequest());
    axios
      .get(
        `/qapcDashboard/getQADashboard?institutionCalendarId=${institutionCalendarId}&startMonth=${startMonth}&endMonth=${endMonth}&queryFormInitiatorIds=${queryFormInitiatorIds}&queryCategoryFormGroupIds=${queryCategoryFormGroupIds}`
      )
      .then((res) => {
        dispatch(getQADashboardSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(getQADashboardFailure(error));
      });
  };
}

export const POST_INCORPORATE_SECTION_REQUEST = 'POST_INCORPORATE_SECTION_REQUEST';
export const POST_INCORPORATE_SECTION_SUCCESS = 'POST_INCORPORATE_SECTION_SUCCESS';
export const POST_INCORPORATE_SECTION_FAILURE = 'POST_INCORPORATE_SECTION_FAILURE';

const getIncorporateSectionRequest = createAction(POST_INCORPORATE_SECTION_REQUEST, 'requestBody');
const getIncorporateSectionSuccess = createAction(POST_INCORPORATE_SECTION_SUCCESS, 'data');
const getIncorporateSectionFailure = createAction(POST_INCORPORATE_SECTION_FAILURE, 'error');

export function getIncorporateSection(payload, callBack) {
  return function (dispatch) {
    dispatch(getIncorporateSectionRequest());
    axios
      .post('/qapcDashboard/getIncorporateSection', payload)
      .then((res) => {
        dispatch(getIncorporateSectionSuccess({ data: res.data.data, payload }));
        callBack && callBack();
      })
      .catch((error) => dispatch(getIncorporateSectionFailure(error)));
  };
}

export const GET_FORM_INITIATOR_USER_LIST_REQUEST = 'GET_FORM_INITIATOR_USER_LIST_REQUEST';
export const GET_FORM_INITIATOR_USER_LIST_SUCCESS = 'GET_FORM_INITIATOR_USER_LIST_SUCCESS';
export const GET_FORM_INITIATOR_USER_LIST_FAILURE = 'GET_FORM_INITIATOR_USER_LIST_FAILURE';

const getFormInitiatorUserListRequest = createAction(GET_FORM_INITIATOR_USER_LIST_REQUEST);
const getFormInitiatorUserListSuccess = createAction(GET_FORM_INITIATOR_USER_LIST_SUCCESS);
const getFormInitiatorUserListFailure = createAction(GET_FORM_INITIATOR_USER_LIST_FAILURE, 'error');

export const getFormInitiatorUserList = (payload, cb) => {
  return (dispatch) => {
    dispatch(getFormInitiatorUserListRequest());
    axios
      .get('/formApprover/formInitiatorUserList', { params: payload })
      .then((res) => {
        dispatch(getFormInitiatorUserListSuccess());
        cb && cb(res.data.data);
      })
      .catch((error) => {
        dispatch(getFormInitiatorUserListFailure(error));
      });
  };
};

export const PUT_SEND_EMAIL_TO_FORM_INITIATOR_REQUEST = 'PUT_SEND_EMAIL_TO_FORM_INITIATOR_REQUEST';
export const PUT_SEND_EMAIL_TO_FORM_INITIATOR_SUCCESS = 'PUT_SEND_EMAIL_TO_FORM_INITIATOR_SUCCESS';
export const PUT_SEND_EMAIL_TO_FORM_INITIATOR_FAILURE = 'PUT_SEND_EMAIL_TO_FORM_INITIATOR_FAILURE';

const putSendEmailToFormInitiatorRequest = createAction(PUT_SEND_EMAIL_TO_FORM_INITIATOR_REQUEST);
const putSendEmailToFormInitiatorSuccess = createAction(PUT_SEND_EMAIL_TO_FORM_INITIATOR_SUCCESS);
const putSendEmailToFormInitiatorFailure = createAction(
  PUT_SEND_EMAIL_TO_FORM_INITIATOR_FAILURE,
  'error'
);

export const putSendEmailToFormInitiator = (payload, cb) => {
  return (dispatch) => {
    dispatch(putSendEmailToFormInitiatorRequest());
    axios
      .post('/formApprover/sendEmailToFormInitiator', payload)
      .then(() => {
        dispatch(putSendEmailToFormInitiatorSuccess());
        cb && cb();
      })
      .catch((error) => {
        dispatch(putSendEmailToFormInitiatorFailure(error));
      });
  };
};

export const ADD_COMMENT_REQUEST = 'ADD_COMMENT_REQUEST';
export const ADD_COMMENT_SUCCESS = 'ADD_COMMENT_SUCCESS';
export const ADD_COMMENT_FAILURE = 'ADD_COMMENT_FAILURE';

const addCommentRequest = createAction(ADD_COMMENT_REQUEST);
const addCommentSuccess = createAction(ADD_COMMENT_SUCCESS);
const addCommentFailure = createAction(ADD_COMMENT_FAILURE, 'error');

export const addComment = (payload, cb) => {
  return (dispatch) => {
    dispatch(addCommentRequest());
    axios
      .post('/formApprover/addComment', payload)
      .then((res) => {
        dispatch(addCommentSuccess());
        cb && cb(res.data.data);
      })
      .catch((error) => {
        dispatch(addCommentFailure(error));
      });
  };
};
export const GET_SEARCH_DOCUMENT_LIST_REQUEST = 'GET_SEARCH_DOCUMENT_LIST_REQUEST';
export const GET_SEARCH_DOCUMENT_LIST_SUCCESS = 'GET_SEARCH_DOCUMENT_LIST_SUCCESS';
export const GET_SEARCH_DOCUMENT_LIST_FAILURE = 'GET_SEARCH_DOCUMENT_LIST_FAILURE';

const getSearchDocumentListRequest = createAction(GET_SEARCH_DOCUMENT_LIST_REQUEST);
const getSearchDocumentListSuccess = createAction(GET_SEARCH_DOCUMENT_LIST_SUCCESS, 'data');
const getSearchDocumentListFailure = createAction(GET_SEARCH_DOCUMENT_LIST_FAILURE, 'error');

export function getSearchDocumentList(params, cb) {
  return function (dispatch) {
    dispatch(getSearchDocumentListRequest());
    axios
      .get('/formInitiator/searchDocumentList', { params }) // Replace with the correct endpoint if needed
      .then((res) => {
        dispatch(getSearchDocumentListSuccess(res.data));
        cb && cb(res.data.data);
      })
      .catch((error) => dispatch(getSearchDocumentListFailure(error)));
  };
}

export const PUT_CREATE_DUPLICATE_FORM_REQUEST = 'PUT_CREATE_DUPLICATE_FORM_REQUEST';
export const PUT_CREATE_DUPLICATE_FORM_SUCCESS = 'PUT_CREATE_DUPLICATE_FORM_SUCCESS';
export const PUT_CREATE_DUPLICATE_FORM_FAILURE = 'PUT_CREATE_DUPLICATE_FORM_FAILURE';

const putCreateDuplicateFormRequest = createAction(PUT_CREATE_DUPLICATE_FORM_REQUEST);
const putCreateDuplicateFormSuccess = createAction(PUT_CREATE_DUPLICATE_FORM_SUCCESS, 'data');
const putCreateDuplicateFormFailure = createAction(PUT_CREATE_DUPLICATE_FORM_FAILURE, 'error');

export function createDuplicateForms(params, cb) {
  return function (dispatch) {
    dispatch(putCreateDuplicateFormRequest());
    axios
      .get('/formInitiator/createDuplicateForm', { params })
      .then((res) => {
        dispatch(putCreateDuplicateFormSuccess(res.data.message));
        cb && cb();
      })
      .catch((error) => dispatch(putCreateDuplicateFormFailure(error)));
  };
}

export const PUT_UNPUBLISHED_FORM_REQUEST = 'PUT_UNPUBLISHED_FORM_REQUEST';
export const PUT_UNPUBLISHED_FORM_SUCCESS = 'PUT_UNPUBLISHED_FORM_SUCCESS';
export const PUT_UNPUBLISHED_FORM_FAILURE = 'PUT_UNPUBLISHED_FORM_FAILURE';

const putUnpublishedFormRequest = createAction(PUT_UNPUBLISHED_FORM_REQUEST);
const putUnpublishedFormSuccess = createAction(PUT_UNPUBLISHED_FORM_SUCCESS, 'data');
const putUnpublishedFormFailure = createAction(PUT_UNPUBLISHED_FORM_FAILURE, 'error');

export function createUnpublishedForm(categoryFormId, cb) {
  return function (dispatch) {
    dispatch(putUnpublishedFormRequest());
    axios
      .put(`/qapcCategoryForm_v2/unPublishedForm?categoryFormId=${categoryFormId}`)
      .then((res) => {
        dispatch(putUnpublishedFormSuccess(res.data.message));
        cb && cb();
      })
      .catch((error) => dispatch(putUnpublishedFormFailure(error)));
  };
}

export const CREATE_DISCUSSION_REQUEST = 'CREATE_DISCUSSION_REQUEST';
export const CREATE_DISCUSSION_SUCCESS = 'CREATE_DISCUSSION_SUCCESS';
export const CREATE_DISCUSSION_FAILURE = 'CREATE_DISCUSSION_FAILURE';

const createDiscussionRequest = createAction(CREATE_DISCUSSION_REQUEST);
const createDiscussionSuccess = createAction(CREATE_DISCUSSION_SUCCESS, 'data');
const createDiscussionFailure = createAction(CREATE_DISCUSSION_FAILURE, 'error');

export function createDiscussion(requestData, cb, cbCount) {
  const { channel_id } = requestData;
  return function (dispatch) {
    dispatch(createDiscussionRequest());
    axios
      .post('/digiclass/discussions/create', requestData)
      .then((res) => {
        dispatch(createDiscussionSuccess(res.data.data));
        cb && cb(channel_id);
        cbCount && cbCount();
      })
      .catch((error) => dispatch(createDiscussionFailure(error)));
  };
}

export const GET_DISCUSSION_REQUEST = 'GET_DISCUSSION_REQUEST';
export const GET_DISCUSSION_SUCCESS = 'GET_DISCUSSION_SUCCESS';
export const GET_DISCUSSION_FAILURE = 'GET_DISCUSSION_FAILURE';

const getDiscussionRequest = createAction(GET_DISCUSSION_REQUEST);
const getDiscussionSuccess = createAction(GET_DISCUSSION_SUCCESS, 'data');
const getDiscussionFailure = createAction(GET_DISCUSSION_FAILURE, 'error');

export function getDiscussion(channelId) {
  return function (dispatch) {
    dispatch(getDiscussionRequest());
    axios
      .get(`/digiclass/discussions/getLists?channelId=${channelId}`)
      .then((res) => {
        dispatch(getDiscussionSuccess(res.data.data.discussionLists));
      })
      .catch((error) => dispatch(getDiscussionFailure(error)));
  };
}

export const GET_DISCUSSION_COUNT_REQUEST = 'GET_DISCUSSION_COUNT_REQUEST';
export const GET_DISCUSSION_COUNT_SUCCESS = 'GET_DISCUSSION_COUNT_SUCCESS';
export const GET_DISCUSSION_COUNT_FAILURE = 'GET_DISCUSSION_COUNT_FAILURE';

const getDiscussionCountRequest = createAction(GET_DISCUSSION_COUNT_REQUEST);
const getDiscussionCountSuccess = createAction(GET_DISCUSSION_COUNT_SUCCESS, 'data');
const getDiscussionCountFailure = createAction(GET_DISCUSSION_COUNT_FAILURE, 'error');

export function getDiscussionCount(params) {
  return function (dispatch) {
    dispatch(getDiscussionCountRequest());
    axios
      .get(`/digiclass/discussions/unReadCounts`, { params })
      .then((res) => {
        dispatch(getDiscussionCountSuccess(res.data.data));
      })
      .catch((error) => dispatch(getDiscussionCountFailure(error)));
  };
}
