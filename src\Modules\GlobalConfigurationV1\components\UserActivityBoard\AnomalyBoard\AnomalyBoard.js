import React, { useEffect, useRef } from 'react';
import Header from '../Header';
import ManualAttendance from './ManualAttendance/ManualAttendance';
import AnomalyLevels from './AnomalyLevels/AnomalyLevels';
import { useDispatch, useSelector } from 'react-redux';
import { getParameter, saveLeaderBoard, setData } from '_reduxapi/global_configuration/v1/actions';
import { selectLeaderBoardSetting } from '_reduxapi/global_configuration/v1/selectors';
import { Map, List } from 'immutable';
import { anomalyManualAttendanceValidate } from '../utils';

export default function AnomalyBoard() {
  const dispatch = useDispatch();
  const anomalyBoard = useSelector(selectLeaderBoardSetting);
  // const anomalyBoard = leaderBoardSetting.get('anomalyBoard', Map());

  const criteriaRef = useRef(List());
  const badgeRef = useRef(List());

  const onValidate = () => {
    let message = anomalyManualAttendanceValidate(criteriaRef.current, badgeRef.current);
    if (message !== '') {
      return dispatch(setData(Map({ message })));
    }
    const payload = {
      anomalyBoard: {
        parameters: anomalyBoard.setIn(['parameters', 0, 'criteria'], criteriaRef.current).toJS(),
        anomalyLevel: badgeRef.current?.toJS(),
      },
    };

    dispatch(saveLeaderBoard(payload, 'anomaly'));
  };

  const wantToSetApiResponse = () => {
    dispatch(
      setData({
        leaderBoardSetting: anomalyBoard.set(
          'triggerRender',
          !anomalyBoard.get('triggerRender', false)
        ),
      })
    );
  };

  useEffect(() => {
    dispatch(getParameter('anomaly'));
  }, []); //eslint-disable-line

  return (
    <div className="m-3">
      <Header
        boardName="Anomaly Board"
        validate={onValidate}
        wantToSetApiResponse={wantToSetApiResponse}
      />
      <ManualAttendance anomalyBoard={anomalyBoard} ref={criteriaRef} />
      <AnomalyLevels anomalyBoard={anomalyBoard} ref={badgeRef} />
    </div>
  );
}
