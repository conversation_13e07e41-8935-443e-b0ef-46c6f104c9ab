import * as Constants from '../../constants';

export function signUpValidation() {
  const { onSetData } = this.props;
  const { email, password } = this.state;
  const emailVal = Constants.EMAIL_VALIDATION;

  if (!email) {
    onSetData({ message: 'Email is Required' });
    return false;
  }
  if (!emailVal.test(email)) {
    onSetData({ message: 'Please enter Valid Email Address' });
    return false;
  }
  if (!password) {
    onSetData({ message: 'Password is Required' });
    return false;
  }
  if (password.length <= 7) {
    onSetData({ message: 'Minimum 8 character is required' });
    return false;
  }

  return true;
}
