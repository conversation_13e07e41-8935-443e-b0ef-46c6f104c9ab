import React from 'react';
// import DeleteIcon from '@mui/icons-material/Delete';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { MobileTimePicker } from '@mui/x-date-pickers/MobileTimePicker';
import TextField from '@mui/material/TextField';
import PropTypes from 'prop-types';
import { List } from 'immutable';

import MaterialInput from 'Widgets/FormElements/material/Input';

function SessionTiming({ sessionList, handleChange, removeSession, dayIndex }) {
  return (
    <React.Fragment>
      {sessionList !== null &&
        sessionList.map((session, index) => (
          <div
            className="d-flex align-items-center justify-content-between border-bottom p-12"
            key={index}
          >
            <div className="  mt-1 pt-1" style={{ width: '300px' }}>
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                placeholder={'Session Name'}
                value={session.get('sessionName')}
                changed={(e) => handleChange(e, index, 'sessionName', dayIndex)}
              />
            </div>
            <div className="">
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <MobileTimePicker
                  className="timePicker"
                  value={session.get('sessionStart')}
                  onChange={(e) => handleChange(e, index, 'sessionStart', dayIndex)}
                  renderInput={(params) => (
                    <TextField
                      sx={{
                        width: '20ch',
                        input: { cursor: 'pointer' },
                      }}
                      size="small"
                      placeholder="START TIME"
                      {...params}
                    />
                  )}
                />
              </LocalizationProvider>
            </div>
            <div className="">
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <MobileTimePicker
                  className="timePicker"
                  value={session.get('sessionEnd', '')}
                  onChange={(e) => handleChange(e, index, 'sessionEnd', dayIndex)}
                  renderInput={(params) => (
                    <TextField
                      sx={{
                        width: '20ch',
                        input: { cursor: 'pointer' },
                      }}
                      size="small"
                      placeholder="END TIME"
                      {...params}
                    />
                  )}
                />
              </LocalizationProvider>
            </div>
            {/* <div className="ml-auto">
              <DeleteIcon className="remove_hover" onClick={() => removeSession(index, dayIndex)} />
            </div> */}
          </div>
        ))}
    </React.Fragment>
  );
}

SessionTiming.propTypes = {
  sessionList: PropTypes.instanceOf(List()),
  handleChange: PropTypes.func,
  removeSession: PropTypes.func,
  dayIndex: PropTypes.number,
};

export default SessionTiming;
