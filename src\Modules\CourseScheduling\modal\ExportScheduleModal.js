import React from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import { List, Map } from 'immutable';
import {
  Button,
  Checkbox,
  FormControl,
  FormGroup,
  FormControlLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import { ThemeProvider } from '@mui/styles';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import { MUI_CHECKBOX_THEME, getURLParams, studentGroupRename } from '../../../utils';

const sessionTypes = [
  { value: 'regular', label: 'Regular Session' },
  { value: 'support_session', label: 'Support Session' },
  { value: 'event', label: 'Events' },
];
const exportTypes = [
  { value: 'list', label: 'List' },
  // { value: 'calendar', label: 'Calendar' },
];

function ExportScheduleModal({ show, onHide, onChange, onExport, data, studentGroups }) {
  const programId = getURLParams('programId', true);
  return (
    <Modal show={show} onHide={() => onHide(false)} dialogClassName="model-300" centered>
      <ThemeProvider theme={MUI_CHECKBOX_THEME}>
        <Modal.Body>
          <div>
            <div className="mb-3">
              <p className="mb-2 bold">
                <Trans i18nKey={'course_schedule.what_to_export'}></Trans>
              </p>

              <FormControl component="fieldset">
                <RadioGroup
                  row
                  value={data.get('sessionTypes', '')}
                  onChange={(event) => onChange('sessionTypes', event.target.value)}
                >
                  {sessionTypes.map((sessionType) => (
                    <FormControlLabel
                      key={sessionType.value}
                      value={sessionType.value}
                      control={<Radio size="small" color="primary" />}
                      label={t(sessionType.label)}
                      disabled={studentGroups.size === 0 && sessionType.value === 'regular'}
                    />
                  ))}
                </RadioGroup>
              </FormControl>

              {/* <FormControl component="fieldset">
                <FormGroup>
                  {sessionTypes.map((sessionType) => (
                    <FormControlLabel
                      key={sessionType.value}
                      value={sessionType.value}
                      control={
                        <Checkbox
                          size="small"
                          color="primary"
                          checked={data.get('sessionTypes', List()).includes(sessionType.value)}
                          onChange={(event) => {
                            const sessionTypes = data.get('sessionTypes', List());
                            const value = event.target.value;
                            onChange(
                              'sessionTypes',
                              sessionTypes.includes(value)
                                ? sessionTypes.filter((sessionType) => sessionType !== value)
                                : sessionTypes.push(value)
                            );
                          }}
                        />
                      }
                      label={sessionType.label}
                      labelPlacement="end"
                    />
                  ))}
                </FormGroup>
              </FormControl> */}
            </div>
            <div className="mb-3">
              <p className="mb-2 bold">
                <Trans i18nKey={'course_schedule.export_type'}></Trans>
              </p>
              <FormControl component="fieldset">
                <RadioGroup
                  row
                  value={data.get('exportType', '')}
                  onChange={(event) => onChange('exportType', event.target.value)}
                >
                  {exportTypes.map((exportType) => (
                    <FormControlLabel
                      key={exportType.value}
                      value={exportType.value}
                      control={<Radio size="small" color="primary" />}
                      label={exportType.label}
                      disabled={exportType.value === 'calendar'}
                    />
                  ))}
                </RadioGroup>
              </FormControl>
            </div>
            {data.get('sessionTypes', '') === 'regular' && (
              <div className="mb-1">
                <p className="mb-2 bold">
                  <Trans i18nKey={'student_groups'}></Trans>
                </p>
                <FormControl component="fieldset">
                  <FormGroup row>
                    {studentGroups.map((studentGroup) => (
                      <FormControlLabel
                        key={studentGroup.get('_id')}
                        value={studentGroup.get('_id')}
                        control={
                          <Checkbox
                            size="small"
                            color="primary"
                            checked={data
                              .get('studentGroups', List())
                              .includes(studentGroup.get('_id'))}
                            onChange={(event) => {
                              const studentGroups = data.get('studentGroups', List());
                              const value = event.target.value;
                              onChange(
                                'studentGroups',
                                studentGroups.includes(value)
                                  ? studentGroups.filter((studentGroup) => studentGroup !== value)
                                  : studentGroups.push(value)
                              );
                            }}
                          />
                        }
                        label={studentGroupRename(studentGroup.get('name'), programId)}
                        labelPlacement="end"
                      />
                    ))}
                  </FormGroup>
                </FormControl>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <div className="d-flex justify-content-end">
            <div className="mr-3">
              <Button
                color="primary"
                variant="outlined"
                className="text-uppercase"
                onClick={() => onHide(false)}
              >
                <Trans i18nKey={'cancel'}></Trans>
              </Button>
            </div>
            <div>
              <Button
                color="primary"
                variant="contained"
                className="text-uppercase"
                onClick={onExport}
              >
                <Trans i18nKey={'export'}></Trans>
              </Button>
            </div>
          </div>
        </Modal.Footer>
      </ThemeProvider>
    </Modal>
  );
}

ExportScheduleModal.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  onChange: PropTypes.func,
  onExport: PropTypes.func,
  data: PropTypes.instanceOf(Map),
  studentGroups: PropTypes.instanceOf(List),
};

export default ExportScheduleModal;
