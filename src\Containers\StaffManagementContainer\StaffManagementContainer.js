import React, { Component, Suspense, lazy } from 'react';
import PropTypes from 'prop-types';
import { Badge } from 'react-bootstrap';
import { withRouter } from 'react-router-dom';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { getLang, isModuleEnabled } from 'utils';

const AllStaff = lazy(() => import('../../Components/StaffManagement/AllStaff'));
const SubmittedStaff = lazy(() => import('../../Components/StaffManagement/SubmittedStaff'));
const MismatchStaff = lazy(() => import('../../Components/StaffManagement/MismatchStaff'));
const ValidStaff = lazy(() => import('../../Components/StaffManagement/ValidStaff'));
const InvalidStaff = lazy(() => import('../../Components/StaffManagement/InvalidStaff'));
const ExpiredStaff = lazy(() => import('../../Components/StaffManagement/ExpiredStaff'));
const ImportedStaff = lazy(() => import('../../Components/StaffManagement/ImportedStaff'));
const InvitedStaff = lazy(() => import('../../Components/StaffManagement/InvitedStaff'));

const CompletedStaff = lazy(() => import('../../Components/StaffManagement/CompletedStaff'));
const InactiveStaff = lazy(() => import('../../Components/StaffManagement/InactiveStaff'));
const ReRegisterRequests = lazy(() =>
  import('../../Components/StaffManagement/ReRegisterRequests')
);

class StaffManagementContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      key: 1,
      programName: '',
      isLoading: false,
      totalDoc: '',
      selectedTab: 0,
      completeView: true,
      pendingView: false,
      inactiveView: false,
      reRegisterView: false,
      tab: [
        {
          name: 'All',
        },
        {
          name: 'Imported',
        },
        {
          name: 'Invited',
        },
        {
          name: 'Submitted',
        },
        {
          name: 'Mismatch',
        },
        {
          name: 'Invalid',
        },
        {
          name: 'Valid',
        },
        {
          name: 'Expired',
        },
      ],
      faceReRegisterCount: 0,
    };
  }

  componentDidMount() {
    if (
      CheckPermission('tabs', 'User Management', 'Staff Management', '', 'Registered', 'View') ||
      CheckPermission(
        'tabs',
        'User Management',
        'Staff Management',
        '',
        'Registration Pending',
        'View'
      ) ||
      CheckPermission('tabs', 'User Management', 'Staff Management', '', 'Inactive', 'View')
    ) {
      this.fetchApi();
    }
    if (this.props.location.state) {
      this.setState(this.props.location.state);
    } else {
      if (
        CheckPermission('tabs', 'User Management', 'Staff Management', '', 'Registered', 'View')
      ) {
        this.completeTab();
      } else if (
        CheckPermission(
          'tabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          'View'
        )
      ) {
        this.pendingTab();
      } else if (
        CheckPermission('tabs', 'User Management', 'Staff Management', '', 'Inactive', 'View')
      ) {
        this.inactiveTab();
      }

      if (
        CheckPermission(
          'tabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          'View'
        )
      ) {
        var tabValue = this.state.tab.map((data, index) => {
          if (
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              data.name,
              'View'
            ) === true
          ) {
            return index;
          } else {
            return null;
          }
        });
        tabValue = tabValue.filter((tab) => tab !== null);
        this.setState({ selectedTab: tabValue.length > 0 ? tabValue[0] : 0 });
      }
    }
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });
    axios.get(`user/get_all/staff/all?limit=10&pageNo=1`).then((res) => {
      this.setState({
        inactive_user_count: res.data.count.inactive_user_count,
        completed_count: res.data.count.completed_count,
        all_active_user_count: res.data.count.all_active_user_count,
        faceReRegisterCount: res.data.count.faceReRegisterCount,
        isLoading: false,
      });
    });
  };

  handleSelect = (key) => {
    this.setState({ key: key });
  };

  onSelect = (index) => {
    this.setState({
      selectedTab: index,
    });
  };

  completeTab = () => {
    this.setState({
      completeView: true,
      pendingView: false,
      inactiveView: false,
      reRegisterView: false,
    });
  };

  pendingTab = () => {
    this.setState({
      completeView: false,
      pendingView: true,
      inactiveView: false,
      reRegisterView: false,
    });
  };

  inactiveTab = () => {
    this.setState({
      completeView: false,
      pendingView: false,
      inactiveView: true,
      reRegisterView: false,
    });
  };

  reResisterTab = () => {
    this.setState({
      reRegisterView: true,
      completeView: false,
      pendingView: false,
      inactiveView: false,
    });
  };

  handleReRequestCount = () => {
    const { faceReRegisterCount } = this.state;
    if (faceReRegisterCount === 0) return;
    this.setState((prevState) => ({
      faceReRegisterCount: prevState.faceReRegisterCount - 1,
    }));
  };

  render() {
    const {
      selectedTab,
      completeView,
      pendingView,
      inactiveView,
      reRegisterView,
      completed_count,
      all_active_user_count,
      inactive_user_count,
      tab,
      isLoading,
      faceReRegisterCount,
    } = this.state;
    return (
      <React.Fragment>
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'staff_management'}></Trans>
        </div>

        <div className="customize_tab">
          <ul id="menu">
            {CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              'Registered',
              'View'
            ) && (
              <a
                href="##"
                onClick={this.completeTab}
                className={`tabaligment ${completeView ? 'tabactive' : ''}`}
              >
                <Trans i18nKey={'registered'}></Trans>{' '}
                <Badge variant="success">
                  {completed_count === undefined ? 0 : completed_count}
                </Badge>
              </a>
            )}
            {CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              'View'
            ) && (
              <a
                href="##"
                onClick={this.pendingTab}
                className={`tabaligment ${pendingView ? 'tabactive' : ''}`}
              >
                <Trans i18nKey={'registration_pending'}></Trans>{' '}
                {CheckPermission(
                  'subTabs',
                  'User Management',
                  'Staff Management',
                  '',
                  'Registration Pending',
                  '',
                  'All',
                  'View'
                ) && (
                  <Badge variant="success">
                    {all_active_user_count === undefined ? 0 : all_active_user_count}{' '}
                  </Badge>
                )}
              </a>
            )}
            {CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              'Inactive',
              'View'
            ) && (
              <a
                href="##"
                onClick={this.inactiveTab}
                className={`tabaligment ${inactiveView ? 'tabactive' : ''}`}
              >
                <Trans i18nKey={'inactive'}></Trans>{' '}
                <Badge variant="success">
                  {' '}
                  {inactive_user_count === undefined ? 0 : inactive_user_count}{' '}
                </Badge>{' '}
              </a>
            )}
            {CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              'Re-Register Request',
              'View'
            ) &&
              isModuleEnabled('FACE_RE_REGISTER') && (
                <a
                  href="##"
                  onClick={this.reResisterTab}
                  className={`tabaligment ${reRegisterView ? 'tabactive' : ''}`}
                >
                  Re-Register Requests{' '}
                  <Badge variant="success">
                    {' '}
                    {faceReRegisterCount === undefined ? 0 : faceReRegisterCount}{' '}
                  </Badge>{' '}
                </a>
              )}
          </ul>
        </div>

        {/* tab view start  */}
        <React.Fragment>
          {completeView === true &&
          CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            'Registered',
            'View'
          ) ? (
            <Suspense fallback="">
              <CompletedStaff />
            </Suspense>
          ) : (
            ''
          )}

          {pendingView === true && (
            <React.Fragment>
              <div className={`tabs_main ${getLang() === 'ar' ? 'text-left' : ''}`}>
                {tab.map((data, index) => {
                  if (
                    CheckPermission(
                      'subTabs',
                      'User Management',
                      'Staff Management',
                      '',
                      'Registration Pending',
                      '',
                      data.name,
                      'View'
                    ) === true
                  ) {
                    return (
                      <span
                        key={index}
                        className={
                          'tab_custom ' + (selectedTab === index ? 'tab_custom_active' : '')
                        }
                        onClick={() => this.onSelect(index)}
                      >
                        <Trans i18nKey={`user_management.tabs.${data.name}`}></Trans>
                      </span>
                    );
                  } else {
                    return <React.Fragment key={index}></React.Fragment>;
                  }
                })}
              </div>

              {selectedTab === 0 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'All',
                'View'
              ) ? (
                <Suspense fallback="">
                  <AllStaff fetchApi={this.fetchApi} />
                </Suspense>
              ) : (
                ''
              )}
              {selectedTab === 1 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Imported',
                'View'
              ) ? (
                <Suspense fallback="">
                  <ImportedStaff fetchApi={this.fetchApi} selectedTab={selectedTab} />
                </Suspense>
              ) : (
                ''
              )}
              {selectedTab === 2 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Invited',
                'View'
              ) ? (
                <Suspense fallback="">
                  <InvitedStaff selectedTab={selectedTab} />
                </Suspense>
              ) : (
                ''
              )}
              {selectedTab === 3 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Submitted',
                'View'
              ) ? (
                <Suspense fallback="">
                  <SubmittedStaff selectedTab={selectedTab} />
                </Suspense>
              ) : (
                ''
              )}
              {selectedTab === 4 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Mismatch',
                'View'
              ) ? (
                <Suspense fallback="">
                  <MismatchStaff />
                </Suspense>
              ) : (
                ''
              )}
              {selectedTab === 5 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Invalid',
                'View'
              ) ? (
                <Suspense fallback="">
                  <InvalidStaff selectedTab={selectedTab} />
                </Suspense>
              ) : (
                ''
              )}
              {selectedTab === 6 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Valid',
                'View'
              ) ? (
                <Suspense fallback="">
                  <ValidStaff selectedTab={selectedTab} />
                </Suspense>
              ) : (
                ''
              )}
              {selectedTab === 7 &&
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Expired',
                'View'
              ) ? (
                <Suspense fallback="">
                  <ExpiredStaff fetchApi={this.fetchApi} selectedTab={selectedTab} />
                </Suspense>
              ) : (
                ''
              )}
            </React.Fragment>
          )}

          {inactiveView === true &&
          CheckPermission('tabs', 'User Management', 'Staff Management', '', 'Inactive', 'View') ? (
            <Suspense fallback="">
              <InactiveStaff fetchApi={this.fetchApi} />
            </Suspense>
          ) : (
            ''
          )}
          {reRegisterView ? (
            <Suspense fallback="">
              <ReRegisterRequests userType="staff" countCallBack={this.handleReRequestCount} />
            </Suspense>
          ) : (
            ''
          )}
        </React.Fragment>
        {/* tab view end  */}

        <Loader isLoading={isLoading} />
      </React.Fragment>
    );
  }
}

StaffManagementContainer.propTypes = {
  location: PropTypes.object,
};

export default withRouter(StaffManagementContainer);
