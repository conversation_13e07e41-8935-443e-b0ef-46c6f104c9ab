import React, { Component } from 'react';
import { Accordion, Card, Table } from 'react-bootstrap';
import { List, Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { t } from 'i18next';

import { getRandomId } from '../../InfrastructureManagement/utils';
import {
  selectAddList,
  selectLocations,
  selectEditList,
} from '../../../_reduxapi/leave_management/selectors';
import Input from '../../../Widgets/FormElements/Input/Input';
import '../../../Assets/css/grouping.css';
import * as actions from '../../../_reduxapi/leave_management/actions';
import AlertModal from '../../InfrastructureManagement/modal/AlertModal';
import '../../../Assets/css/leave_management.css';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getLang } from 'utils';
const lang = getLang();
class WarningSettings extends Component {
  constructor() {
    super();
    this.StudentTable = React.createRef();
    this.state = {
      isLoading: false,
      arrow: false,
      arrow1: false,
      modalData: {
        show: false,
      },
      studentId: '',
      data: [],
      warningOptions: [
        { name: '', value: '' },
        { name: '1st Warning', value: '1st Warning' },
        { name: '2nd Warning', value: '2nd Warning' },
        { name: '3rd Warning', value: '3rd Warning' },
        { name: '4th Warning', value: '4th Warning' },
        { name: '5th Warning', value: '5th Warning' },
        { name: 'Denial', value: 'Denial' },
      ],
    };
  }

  componentDidMount() {
    const { getAllLeaveClassifications } = this.props;
    getAllLeaveClassifications('/lms/list');
  }

  handleSelect = (value) => {
    this.setState({
      hour: value,
    });
  };

  handleChange(event, name, id, operation, group) {
    const value = event.target.value;
    const { addList, editedWarning } = this.props;
    this.props.setData(
      Map({
        ...(operation === 'create' &&
          ['warning', 'warning_message'].includes(name) && {
            addList: addList.setIn([id, name], value),
          }),
        ...(operation === 'create' &&
          ['absence_percentage', 'conducted_session_percentage'].includes(name) && {
            addList: addList.setIn([id, name], value > 100 ? 100 : Math.round(value)),
          }),
        ...(operation === 'update' &&
          ['warning', 'warning_message'].includes(name) && {
            editedWarning: editedWarning.setIn([id, name], value),
          }),
        ...(operation === 'update' &&
          ['absence_percentage', 'conducted_session_percentage'].includes(name) && {
            editedWarning: editedWarning.setIn([id, name], value > 100 ? 100 : Math.round(value)),
          }),
      })
    );
  }

  handleEditClick(row) {
    const { editedWarning } = this.props;
    this.props.setData(
      Map({
        editedWarning: editedWarning.set(row.get('_id'), row),
      })
    );
  }

  handleCancelClick(row, operation) {
    const id = row.get('_id');
    const { addList, editedWarning } = this.props;
    this.props.setData(
      Map({
        ...(operation === 'create' && { addList: addList.delete(id) }),
        ...(operation === 'update' && { editedWarning: editedWarning.delete(id) }),
      })
    );
  }

  handleSaveClick(id, operation, isConfirmed = false) {
    const { addList, editedWarning, leavedetials } = this.props;
    const data = operation === 'create' ? addList.get(id) : editedWarning.get(id);
    if (operation !== 'delete') {
      const absencePercentageArray = leavedetials
        .get('student_warning_absence_calculation', List())
        .filter((item) => (operation === 'create' ? item : item.get('_id', '') !== id))
        .map((item) => item.get('absence_percentage', 0));
      const totalArray = absencePercentageArray.filter(
        (item) => item === data.get('absence_percentage', 0)
      );
      const checkUniqueValue = totalArray.size === 0;
      if (!checkUniqueValue) {
        this.setModalData({
          show: true,
          title: 'Alert: Validation error',
          description: 'Absence percentage should not be equal',
          variant: 'alert',
          cancelButtonLabel: 'OK',
        });
        return;
      }
    }

    const requestBody = {
      _id: id,
      ...(operation !== 'delete' && {
        warning: data.get('warning'),
        absence_percentage: data.get('absence_percentage'),
        // conducted_session_percentage: data.get('conducted_session_percentage'),
        warning_message: data.get('warning_message'),
      }),
    };
    if (operation === 'create') {
      if (
        !data.get('warning') ||
        !data.get('absence_percentage') ||
        // !data.get('conducted_session_percentage') ||
        !data.get('warning_message')
      ) {
        let message = `${
          !data.get('warning')
            ? `Warning`
            : !data.get('absence_percentage')
            ? `Absence Percentage `
            : // : !data.get('conducted_session_percentage')
              // ? `Conducted Percentage`
              !data.get('warning_message') && `Warning Message`
        }  is Required `;
        this.setModalData({
          show: true,
          title: 'Alert: Validation error',
          description: message,
          variant: 'alert',
          cancelButtonLabel: 'OK',
        });
        return;
      }
      this.props.addWarning(requestBody, () => {
        let enpoint = '/lms/list';
        this.props.getAllLeaveClassifications(enpoint);
        this.props.setData(
          Map({
            ...(operation === 'create' && { addList: addList.delete(id) }),
          })
        );
      });
    }
    if (operation === 'delete') {
      this.props.deleteWarning(id, () => {
        let enpoint = '/lms/list';
        this.props.getAllLeaveClassifications(enpoint);
      });
    }

    if (operation === 'update') {
      if (
        !data.get('warning') ||
        !data.get('absence_percentage') ||
        // !data.get('conducted_session_percentage') ||
        !data.get('warning_message')
      ) {
        let message = `${
          !data.get('warning')
            ? `Warning`
            : !data.get('absence_percentage')
            ? `Absence Percentage `
            : // : !data.get('conducted_session_percentage')
              // ? `Conducted Percentage`
              !data.get('warning_message') && `Warning Message`
        }  is Required `;
        this.setModalData({
          show: true,
          title: 'Alert: Validation error',
          description: message,
          variant: 'alert',
          cancelButtonLabel: 'OK',
        });
        return;
      }
      const commonId = leavedetials.get('_id');

      const studentId = data.get('_id');
      this.props.editWarning(requestBody, commonId, studentId, () => {
        let enpoint = '/lms/list';
        this.props.getAllLeaveClassifications(enpoint);
        this.props.setData(
          Map({
            ...(operation === 'update' && { editedWarning: editedWarning.delete(id) }),
          })
        );
      });
    }
  }

  handleDeleteClick(data) {
    const studentId = data.get('_id');

    this.setModalData({
      show: true,
      title: t('confirm_delete'),
      description: t('leaveManagement.sureDelete'),
      variant: 'confirm',
      cancelButtonLabel: t('student_grouping.cancel'),
      data: studentId,
    });
    return;
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
      },
      studentId: data,
    });
  }
  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm(id) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.handleSaveClick(id, 'delete')
    );
  }
  addStudentTable() {
    const _id = getRandomId();
    const { addList, setData } = this.props;

    setData(
      Map({
        addList: addList.set(
          _id,
          Map({
            _id,
            warning: '',
            absence_percentage: '',
            conducted_session_percentage: '',
            warning_message: '',
          })
        ),
      })
    );
  }
  getaddStudentTable() {
    const { addList } = this.props;
    return addList.entrySeq();
  }
  iconPosition = () => {
    this.setState({
      arrow: !this.state.arrow,
    });
  };
  render() {
    const { modalData, studentId, warningOptions } = this.state;
    const { addList, leavedetials, editedWarning } = this.props;
    return (
      <React.Fragment>
        <Accordion defaultActiveKey="">
          <Card className="rounded">
            <Accordion.Toggle
              as={Card.Header}
              eventKey="8"
              className="icon_remove_leave"
              onClick={this.iconPosition}
            >
              <div className="d-flex justify-content-between">
                <p className="mb-0">{t('leaveManagement.warningAndAbsenceCalc')}</p>

                <p className="mb-0">
                  {this.state.arrow === true ? (
                    <i className="fa fa-chevron-down f-14" aria-hidden="true"></i>
                  ) : (
                    <i
                      className={`fa fa-chevron-${lang === 'ar' ? 'down' : 'up'} fa-rotate-90 f-14`}
                      aria-hidden="true"
                    ></i>
                  )}
                </p>
              </div>
            </Accordion.Toggle>

            <Accordion.Collapse eventKey="8" className="bg-white ">
              <Card.Body className=" innerbodyLeave border-top-remove">
                <div className="container-fluid  ">
                  {CheckPermission(
                    'subTabs',
                    'Leave Management',
                    'Leave Settings',
                    '',
                    'Student',
                    '',
                    'Warning and Absence Calculation',
                    'Add'
                  ) && (
                    <p
                      className="f-12 text-right pt-1 mb-3 text-skyblue remove_hover"
                      onClick={this.addStudentTable.bind(this)}
                    >
                      <i className="fa fa-plus pr-2" aria-hidden="true"></i>
                      {t('leaveManagement.addWarning')}
                    </p>
                  )}

                  <div className="dash-table border">
                    <Table responsive hover>
                      <thead className="th-change">
                        <tr>
                          <th className="">
                            <div className="">
                              <b className=""> {t('leaveManagement.warning')} </b>
                            </div>
                          </th>
                          <th className="">
                            <div className="text-center">
                              <b className="">{t('leaveManagement.absence')}</b>
                            </div>
                          </th>
                          {/* <th className="text-center">
                            <div className="">
                              <b className="">Conducted Session% </b>
                            </div>
                          </th> */}
                          <th className="">
                            <div className="">
                              <b className="">{t('leaveManagement.warningMessage')}</b>
                            </div>
                          </th>

                          <th className="text-center">
                            <div className=""></div>
                          </th>
                        </tr>
                      </thead>

                      <tbody>
                        {this.getaddStudentTable().map((row, i) => (
                          <EditableStudentList
                            key={i}
                            row={row[1]}
                            data={addList}
                            handleChange={this.handleChange.bind(this)}
                            handleCancelClick={this.handleCancelClick.bind(this)}
                            handleSaveClick={this.handleSaveClick.bind(this)}
                            operation="create"
                            warningOptions={warningOptions}
                          />
                        ))}

                        {leavedetials
                          ?.get('student_warning_absence_calculation', List())
                          ?.map((data, i) => {
                            return !editedWarning.has(data.get('_id')) ? (
                              <tr key={i} className="tr-change">
                                <td>
                                  <div className=""> {data.get('warning')}</div>
                                </td>
                                <td>
                                  <div className="text-center">
                                    {data.get('absence_percentage')}
                                  </div>
                                </td>
                                {/* <td>
                                  <div className="text-center">
                                    {data.get('conducted_session_percentage')}
                                  </div>
                                </td> */}
                                <td>
                                  <div className="">{data.get('warning_message')}</div>
                                </td>
                                <td>
                                  <div className="d-flex justify-content-between w-30 ">
                                    <b className="float-left pr-2 f-16">
                                      {CheckPermission(
                                        'subTabs',
                                        'Leave Management',
                                        'Leave Settings',
                                        '',
                                        'Student',
                                        '',
                                        'Warning and Absence Calculation',
                                        'Edit'
                                      ) && (
                                        <i
                                          className="fa fa-pencil"
                                          aria-hidden="true"
                                          onClick={this.handleEditClick.bind(this, data)}
                                        />
                                      )}
                                    </b>

                                    <b className="float-left pl-2 f-16">
                                      {CheckPermission(
                                        'subTabs',
                                        'Leave Management',
                                        'Leave Settings',
                                        '',
                                        'Student',
                                        '',
                                        'Warning and Absence Calculation',
                                        'Delete'
                                      ) && (
                                        <i
                                          className="fa fa-trash"
                                          aria-hidden="true"
                                          onClick={this.handleDeleteClick.bind(this, data)}
                                        />
                                      )}
                                    </b>
                                  </div>
                                </td>
                              </tr>
                            ) : (
                              <EditableStudentList
                                key={data.get('_id')}
                                row={data}
                                data={editedWarning}
                                handleChange={this.handleChange.bind(this)}
                                handleCancelClick={this.handleCancelClick.bind(this)}
                                handleSaveClick={this.handleSaveClick.bind(this)}
                                operation="update"
                                warningOptions={warningOptions}
                              />
                            );
                          })}
                      </tbody>
                    </Table>
                  </div>
                  <p className="f-12  pt-2 mb-3 ">{t('leaveManagement.absencePercent')}</p>
                </div>

                <AlertModal
                  show={modalData.show}
                  title={modalData.title || ''}
                  description={modalData.description || ''}
                  variant={modalData.variant || 'confirm'}
                  confirmButtonLabel={modalData.confirmButtonLabel || t('YES')}
                  cancelButtonLabel={modalData.cancelButtonLabel || t('NO')}
                  onClose={this.onModalClose.bind(this)}
                  onConfirm={this.onConfirm.bind(this)}
                  data={studentId}
                />
              </Card.Body>
            </Accordion.Collapse>
          </Card>
        </Accordion>
      </React.Fragment>
    );
  }
}
const mapStateToProps = function (state) {
  return {
    addList: selectAddList(state),
    editedWarning: selectEditList(state),

    leavedetials: selectLocations(state),
  };
};
WarningSettings.propTypes = {
  addList: PropTypes.instanceOf(Map),
  leavedetials: PropTypes.instanceOf(Map),
  editWarning: PropTypes.func,
  addWarning: PropTypes.func,
  deleteWarning: PropTypes.func,
  editedWarning: PropTypes.object,
  getAllLeaveClassifications: PropTypes.func,
  setData: PropTypes.func,
};
export default compose(withRouter, connect(mapStateToProps, actions))(WarningSettings);

function EditableStudentList(props) {
  const {
    row,
    data,
    handleChange,
    operation,
    handleSaveClick,
    handleCancelClick,
    warningOptions,
  } = props;
  const id = row.get('_id');

  return (
    <>
      <tr className="tr-change">
        <td className="">
          <div style={{ width: '50%' }}>
            <div className="mt--25">
              <Input
                elementType={'floatingselect'}
                elementConfig={{
                  options: warningOptions,
                }}
                value={data.getIn([id, 'warning'])}
                floatingLabel={''}
                changed={(e) => handleChange(e, 'warning', id, operation)}
              />
            </div>
          </div>
        </td>
        <td className="">
          <div className="mx-auto w-100px">
            <div className="mt--25">
              <Input
                elementType={'floatinginput'}
                elementConfig={{
                  type: 'number',
                }}
                min={0}
                max={100}
                maxLength={3}
                step={1}
                value={data.getIn([id, 'absence_percentage'])}
                changed={(e) => handleChange(e, 'absence_percentage', id, operation)}
                // feedback={this.state.extensionError}
              />
            </div>
          </div>
        </td>
        {/* <td className="">
          <div className="mx-auto w-45px">
            <div className="mt--25">
              <Input
                elementType={'floatinginput'}
                elementConfig={{
                  type: 'number',
                }}
                min={0}
                maxLength={20}
                value={data.getIn([id, 'conducted_session_percentage'])}
                changed={(e) => handleChange(e, 'conducted_session_percentage', id, operation)}
                // feedback={this.state.extensionError}
              />
            </div>
          </div>
        </td>
        */}
        <td>
          <div style={{ width: '50%' }}>
            <div className="mt--25">
              <Input
                elementType={'floatinginput'}
                elementConfig={{
                  type: 'text',
                }}
                maxLength={20}
                value={data.getIn([id, 'warning_message'])}
                changed={(e) => handleChange(e, 'warning_message', id, operation)}
              />
            </div>
          </div>
        </td>
        <td>
          <div className="d-flex justify-content-between w-30 ">
            <b className="float-left pr-2 f-16">
              <i
                className="fa fa-times-circle"
                aria-hidden="true"
                onClick={() => handleCancelClick(row, operation)}
              />
            </b>

            <b className="float-left pl-2 f-16">
              <i
                className="fa fa-floppy-o"
                aria-hidden="true"
                onClick={() => handleSaveClick(id, operation, false)}
              />
            </b>
          </div>
        </td>
      </tr>
    </>
  );
}
EditableStudentList.propTypes = {
  row: PropTypes.instanceOf(Map),
  data: PropTypes.instanceOf(Map),
  operation: PropTypes.string,
  handleSaveClick: PropTypes.func,
  handleCancelClick: PropTypes.func,
  handleChange: PropTypes.func,
  HrContact: PropTypes.func,
  warningOptions: PropTypes.array,
};
