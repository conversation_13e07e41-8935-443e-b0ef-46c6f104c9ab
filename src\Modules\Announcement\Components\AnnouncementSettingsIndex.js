import React, { useState, useEffect } from 'react';
import {
  Box,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  MenuItem,
  Paper,
  Select,
} from '@mui/material';
import { List, Map, fromJS } from 'immutable';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { useDispatch, useSelector } from 'react-redux';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import {
  userPermissionList,
  ProgramList,
  setData,
  SingleProgramDetails,
  getUserSetting,
  selectUserPrograms,
  userModuleList,
  userModuleUser,
} from '_reduxapi/Announcement/actions';
import {
  selectProgramList,
  selectModuleList,
  selectModuleUser,
  selectProgramDetails,
} from '_reduxapi/Announcement/selector';
import ProgramDetailsList from './ProgramDetailsList';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { isAnnouncementEnabled, isDigiSurveyEnabled } from 'utils';

const AnnouncementSettingsIndex = () => {
  const userPermission = useSelector(selectModuleUser);
  const ProgramLists = useSelector(selectProgramList);
  const [userProgramDetails, setUserProgramDetails] = useState(List());
  const [userIndex, setUserIndex] = useState(null);
  const [userSearch, setUserSearch] = useState('');
  const [listSearch, setListSearch] = useState('');
  const [programId, setProgramId] = useState('');
  const [selectedCourses, setSelectedCourse] = useState(List());
  const [calledUsers, setCalledUsers] = useState([]);
  const programDetails = useSelector(selectProgramDetails);
  const [selectedIconIds, setSelectedIconIds] = useState(List());
  const [selectedItem, setSelectedItem] = useState('');

  const authData = useSelector(selectUserInfo);
  const moduleList = useSelector(selectModuleList);

  const dispatch = useDispatch();

  const isSurveyEnabled = CheckPermission('pages', 'User Module Permission', 'Survey', 'view');
  const isAnnouncementEnable = CheckPermission(
    'pages',
    'User Module Permission',
    'Announcement',
    'view'
  );
  const isAnnouncementModuleEnabled = isAnnouncementEnabled();
  const isSurveyModuleEnabled = isDigiSurveyEnabled();
  const filteredModuleList = moduleList.filter((moduleListItem) => {
    if (
      moduleListItem.get('name', '') === 'Survey Management' &&
      isSurveyEnabled &&
      isSurveyModuleEnabled
    )
      return true;
    if (
      moduleListItem.get('name', '') === 'Announcement Management' &&
      isAnnouncementEnable &&
      isAnnouncementModuleEnabled
    )
      return true;
    return false;
  });

  useEffect(() => {
    dispatch(userPermissionList());
    dispatch(ProgramList());
    dispatch(userModuleList());
  }, []); //eslint-disable-line

  useEffect(() => {
    setUserProgramDetails(
      userPermission.map((userPermissionItem) =>
        userPermissionItem
          .set(
            'programList',
            ProgramLists.map((ProgramListsItem) =>
              ProgramListsItem.set('isOpen', false).set('status', false)
            )
          )
          .set('allProgramStatus', false)
      )
    );

    setSelectedCourse(
      userPermission.map((userPermissionItem) =>
        userPermissionItem.set('programList', ProgramLists)
      )
    );
  }, [userPermission, ProgramLists]); //eslint-disable-line

  const checkAllData = (programDetailsMap, event) => {
    return programDetailsMap.map((programDetailsItem) =>
      programDetailsItem.set('status', event).set(
        'term',
        programDetailsItem.get('term', List()).map((termItem) =>
          termItem.set('status', event).set(
            'curriculum',
            termItem.get('curriculum', List()).map((curriculumItem) =>
              curriculumItem.set('status', event).set(
                'yearLevel',
                curriculumItem.get('yearLevel', List()).map((yearLevelItem) =>
                  yearLevelItem.set('status', event).set(
                    yearLevelItem.get('rotation', '') === 'yes'
                      ? 'rotation_course'
                      : 'regularCourse',
                    yearLevelItem
                      .get(
                        yearLevelItem.get('rotation', '') === 'yes'
                          ? 'rotation_course'
                          : 'regularCourse',
                        List()
                      )
                      .map((rotationItem) =>
                        rotationItem.set('status', event).set(
                          'course',
                          rotationItem
                            .get('course', List())
                            .map((courseItem) => courseItem.set('status', event))
                        )
                      )
                  )
                )
              )
            )
          )
        )
      )
    );
  };

  const handleUserSearch = (event) => {
    setUserSearch(event.target.value);
    setUserIndex(null);
  };

  const filteredUsers = userSearch
    ? userProgramDetails.filter((user, index) => {
        const firstName = user.getIn(['_user_id', 'name', 'first'], '');
        const lastName = user.getIn(['_user_id', 'name', 'last'], '');

        const userName = `${firstName} ${lastName}`.toLowerCase();
        const userId = user.getIn(['_user_id', 'user_id'], '');

        return userName?.includes(userSearch.toLowerCase()) || userId?.includes(userSearch);
      })
    : userProgramDetails;

  const handleListSearch = (event) => {
    setListSearch(event.target.value);
  };

  const filteredList = listSearch
    ? userProgramDetails.getIn([userIndex, 'programList'], List()).filter((list) => {
        const ListName = list.get('name', '').toLowerCase();
        const code = list.get('code', '');
        return (
          ListName.toLowerCase().includes(listSearch.toLowerCase()) || code.includes(listSearch)
        );
      })
    : userProgramDetails.getIn([userIndex, 'programList'], List());

  const handleChange = (item) => {
    setProgramId(programId !== item.get('_id', '') ? item.get('_id', '') : '');
    if (item.get('programDetail', List()).size === 0) {
      dispatch(SingleProgramDetails(item.get('_id', '')));
    }
  };

  const handleIsChecked = (e, item) => {
    const itemIndex = userProgramDetails
      .getIn([userIndex, 'programList'], List())
      .findIndex((program) => program.get('_id', '') === item.get('_id', ''));

    let updatedUserProgramDetails = userProgramDetails
      .setIn([userIndex, 'programList', itemIndex, 'status'], e)
      .setIn(
        [userIndex, 'programList', itemIndex, 'programDetail'],
        checkAllData(
          userProgramDetails.getIn([userIndex, 'programList', itemIndex, 'programDetail'], List()),
          e
        )
      );

    const isAllChecked = updatedUserProgramDetails
      .getIn([userIndex, 'programList'], List())
      .every((item) => item.get('status', false));
    setUserProgramDetails(
      updatedUserProgramDetails.setIn([userIndex, 'allProgramStatus'], isAllChecked)
    );
    setSelectedCourse(
      selectedCourses.setIn([userIndex, 'programList', itemIndex, 'selectedCourseList'], List())
    );
  };

  const userCallBack = (data, userData) => {
    const { selectedPrograms } = data;
    const selectedCoursesRes = data.selectedCourses;
    const { index, userId } = userData;
    let iconIds = selectedIconIds;
    const userProgramList = userProgramDetails.getIn([index, 'programList'], List());
    if (selectedPrograms.length > 0) {
      const selectedProgramsId = selectedPrograms.map(
        (selectedProgramsIds) => selectedProgramsIds._id
      );
      const updateProgramStatus = userProgramList.map((userProgramIds) => {
        return userProgramIds.set('status', selectedProgramsId.includes(userProgramIds.get('_id')));
      });

      let updatedData = userProgramDetails
        .setIn([index, 'programList'], updateProgramStatus)
        .setIn([index, 'selectedUser'], true);

      updatedData = updatedData.map((updatedDataList, updatedDataINdex) => {
        return index === updatedDataINdex
          ? updatedDataList.set('checkColor', true)
          : updatedDataList.set('checkColor', false);
      });

      setUserProgramDetails(updatedData);
      iconIds = iconIds.push(
        ...selectedPrograms.map((selectedProgramId) => userId + selectedProgramId._id)
      );
    }

    if (selectedCoursesRes.length > 0) {
      const selectCourses = fromJS(selectedCoursesRes);

      const matchingCoursesForProgram = userProgramList.map((course) => {
        const data = selectCourses.filter((programId) => {
          const programIds = programId.get('_program_id', List());
          if (List.isList(programIds)) {
            return programIds.getIn([0, '_id']) === course.get('_id');
          }
          return programIds.get('_id', '') === course.get('_id');
        });
        return course.set('selectedCourseList', data);
      });
      const updatedData = selectedCourses.setIn([index, 'programList'], matchingCoursesForProgram);
      // .setIn([index, 'selectedColor'], true);
      setSelectedCourse(updatedData);

      iconIds = iconIds.push(
        ...selectCourses.map(
          (selectedCourses) => userId + selectedCourses.getIn(['_program_id', 0, '_id'])
        )
      );
    }
    setSelectedIconIds(
      iconIds.filter((value, index, self) => {
        return self.indexOf(value) === index;
      })
    );

    const copyUsers = [...calledUsers, userId];
    setCalledUsers(copyUsers);
  };

  const handleClick = (user, index) => {
    const userIndex = userProgramDetails.findIndex(
      (item) => item.getIn(['_user_id', '_id']) === user.getIn(['_user_id', '_id'])
    );
    setUserIndex(userIndex);
    setProgramId('');

    let userProgramDetail = userProgramDetails.setIn([userIndex, 'selectedUser'], true);
    userProgramDetail = userProgramDetail.map((ProgramDetailList, ListIndex) => {
      return userIndex === ListIndex
        ? ProgramDetailList.set('checkColor', true)
        : ProgramDetailList.set('checkColor', false);
    });

    setUserProgramDetails(userProgramDetail);

    if (!calledUsers.includes(user.getIn(['_user_id', '_id']))) {
      dispatch(
        getUserSetting(
          user.getIn(['_user_id', '_id']),
          userCallBack,
          {
            index: userIndex,
            userId: user.getIn(['_user_id', '_id'], ''),
          },
          selectedItem
        )
      );
    } else {
      dispatch(setData(Map({ isLoading: true })));
      setTimeout(() => {
        dispatch(setData(Map({ isLoading: false })));
      }, 500);
    }
  };

  const handleAllChange = (e) => {
    setUserProgramDetails(
      userProgramDetails.setIn([userIndex, 'allProgramStatus'], e.target.checked).setIn(
        [userIndex, 'programList'],
        userProgramDetails
          .getIn([userIndex, 'programList'])
          .map((userDetails) =>
            userDetails
              .set('status', e.target.checked)
              .set(
                'programDetail',
                checkAllData(userDetails.get('programDetail', List()), e.target.checked)
              )
          )
      )
    );
    setSelectedCourse(
      selectedCourses.setIn(
        [userIndex, 'programList'],
        selectedCourses
          .getIn([userIndex, 'programList'])
          .map((selectedCourse) => selectedCourse.set('selectedCourseList', List()))
      )
    );
  };

  const handlePostApi = () => {
    const constructUserProgramDetails = (updatedDetail) => {
      return updatedDetail.map((updatedData) => {
        const selectedCoursesAr = updatedData
          .get('programList', List())
          .filter((programFilter) => !programFilter.get('status', false))
          .filter(
            (programFilterDetails) => programFilterDetails.get('programDetail', List()).size !== 0
          )
          .flatMap((program) =>
            program.get('programDetail', List()).flatMap((programDetail) =>
              programDetail.get('term', List()).flatMap((term) =>
                term.get('curriculum', List()).map((curriculum) => {
                  return Map({
                    _program_id: program.get('_id', ''),
                    _institution_calendar_id: programDetail.get('_institution_calendar_id', ''),
                    level: curriculum.get('yearLevel', List()).map((yearLevel) => {
                      let course = List();

                      if (yearLevel.get('rotation', '') === 'no') {
                        course = yearLevel.get('regularCourse', List()).flatMap((regularCourse) =>
                          regularCourse
                            .get('course', List())
                            .filter((course) => course.get('status', false) === true)
                            .map((course) =>
                              Map({
                                _course_id: course.get('_course_id', ''),
                                courses_name: course.get('courses_name', ''),
                                courses_number: course.get('courses_number', ''),
                              })
                            )
                        );
                      } else {
                        course = yearLevel.get('rotation_course', List()).map((regularCourse) => {
                          return Map({
                            course: regularCourse
                              .get('course', List())
                              .filter((course) => course.get('status', false) === true)
                              .map((course) =>
                                Map({
                                  _course_id: course.get('_course_id', ''),
                                  courses_name: course.get('courses_name', ''),
                                  courses_number: course.get('courses_number', ''),
                                })
                              ),
                            rotation: yearLevel.get('rotation', ''),
                            rotation_count: regularCourse.get('rotation_count', ''),
                          });
                        });
                      }

                      const coursesKey =
                        yearLevel.get('rotation', '') === 'no' ? 'course' : 'rotation_course';
                      return Map({
                        term: yearLevel.get('term', ''),
                        year: yearLevel.get('year', ''),
                        level_no: yearLevel.get('level_no', ''),
                        curriculum: yearLevel.get('curriculum', ''),
                        rotation: yearLevel.get('rotation', ''),
                        [coursesKey]:
                          yearLevel.get('rotation', '') === 'no'
                            ? course
                            : course.filter(
                                (courseFilter) => courseFilter.get('course', List()).size > 0
                              ),
                      });
                    }),
                  });
                })
              )
            )
          );

        const findUser = updatedDetail?.find(
          (userDetail) =>
            userDetail.getIn(['_user_id', '_id'], '') === updatedData.getIn(['_user_id', '_id'], '')
        );

        const user = selectedCourses?.find(
          (user) => user.get('_user_id', Map()) === updatedData.get('_user_id', Map())
        );

        let concatSelectedCourse = List();
        if (user !== undefined) {
          concatSelectedCourse = findUser.get('programList', List()).flatMap((item, itemIndex) => {
            if (item.get('programDetail') === undefined) {
              return item.get('selectedCourseList', List());
            }
          });
        }

        return Map({
          userId: updatedData.getIn(['_user_id', '_id']),
          selectedUser: updatedData.get('selectedUser', false),
          type: updatedData.get('allProgramStatus', false) ? 'all' : 'mixed',
          selectedCourses: selectedCoursesAr.concat(concatSelectedCourse),
          selectedPrograms: updatedData
            .get('programList', List())
            .filter((filteredProgram) => filteredProgram.get('status', false))
            .map((programDetails) => programDetails.get('_id')),
        });
      });
    };

    const updatedDetail = userProgramDetails
      .filter((filterSelectedUser) => filterSelectedUser.get('selectedUser', false))
      .map((user) => {
        const userData = selectedCourses.find(
          (userData) => userData.get('_user_id', Map()) === user.get('_user_id', Map())
        );

        if (userData) {
          return user.update('programList', (programList) => {
            return programList.map((program) => {
              const programData = userData
                .get('programList')
                .find((programData) => programData.get('_id') === program.get('_id'));

              if (programData) {
                return program.set(
                  'selectedCourseList',
                  programData
                    .get('selectedCourseList', List())
                    .map((its) => its.set('_program_id', its.getIn(['_program_id', 0, '_id'], '')))
                );
              }

              return program;
            });
          });
        }

        return user;
      });

    const constructData = constructUserProgramDetails(updatedDetail);

    const constructedData = constructData.map((course) => {
      let mergedData = Map();
      course.get('selectedCourses', List()).forEach((course) => {
        const institutionId = course.getIn(['_institution_calendar_id', '_id'], '');
        const programId = course.get('_program_id', '');
        const calendarName = course.getIn(['_institution_calendar_id', 'calendar_name'], '');
        const key = `${institutionId}_${programId}_${calendarName}`;
        if (!mergedData.has(key)) {
          mergedData = mergedData.set(key, List());
        }
        mergedData = mergedData.update(key, (levelList) =>
          levelList.concat(course.get('level', ''))
        );
      });
      const mergedCourses = mergedData
        .entrySeq()
        .map(([key, value]) => {
          const [institutionId, programId, calendarName] = key.split('_');
          return Map({
            _institution_calendar_id: Map({ _id: institutionId, calendar_name: calendarName }),
            _program_id: programId,
            level: value,
          });
        })
        .toList();

      return course.set(
        'selectedCourses',
        mergedCourses.filter(
          (filteredCourse) => filteredCourse.getIn(['_institution_calendar_id', '_id'], '') !== ''
        )
      );
    });

    const filteredDataList = constructedData.map((filteredConstructed) => {
      const filteredSelectedCourse = filteredConstructed
        .get('selectedCourses', List())
        .map((courseObj) => {
          let filteredLevel = List();

          filteredLevel = courseObj.get('level', List())?.filter((level) => {
            if (level.get('rotation_course', List()).size > 0) {
              return (
                level
                  .get('rotation_course', List())
                  .filter((rotation) => rotation.get('course', List()).size > 0).size > 0
              );
            }
            return level.get('course', List()).size > 0;
          });
          return courseObj.set('level', filteredLevel);
        });
      return filteredConstructed.set(
        'selectedCourses',
        filteredSelectedCourse.filter((level) => level.get('level', List()).size > 0)
      );
    });

    const filterDataList = filteredDataList
      .filter((filteredList) => filteredList.get('selectedUser', false))
      .map((filteredDetails) => filteredDetails.delete('selectedUser').delete('level'));

    const requestBody = {
      manageUser: authData.get('_id', ''),
      moduleName: selectedItem,
      selectedUserProgram: filterDataList.toJS(),
    };

    const callBack = () => {
      let iconIds = List();
      filteredDataList
        .filter((filteredData) => filteredData.get('selectedUser', false))
        .map((user) => {
          const programIds = user
            .get('selectedPrograms', List())
            .map((selectedDetails) => user.get('userId', '') + selectedDetails);
          const courseProgramIds = user
            .get('selectedCourses', List())
            .map(
              (selectedDetails) => user.get('userId', '') + selectedDetails.get('_program_id', '')
            );

          iconIds = iconIds.push(...programIds, ...courseProgramIds);

          return user;
        });

      setSelectedIconIds(
        iconIds.filter((value, index, self) => {
          return self.indexOf(value) === index;
        })
      );
    };
    dispatch(selectUserPrograms(requestBody, callBack));
  };

  const checkAllProgramStatus = () => {
    return userIndex !== null
      ? userProgramDetails
          .getIn([userIndex, 'programList'], List())
          .every((item) => item.get('status') === true)
      : false;
  };

  const handleUserClose = () => {
    setUserSearch('');
  };

  const handleProgramClose = () => {
    setListSearch('');
  };

  const handleChangeModuleList = (event) => {
    const moduleName = event.target.value;
    setSelectedItem(event.target.value);
    setCalledUsers([]);
    setSelectedIconIds(List());
    dispatch(userModuleUser(moduleName));
  };

  useEffect(() => {
    if (moduleList.size) {
      const getName = filteredModuleList.getIn([0, 'name']);
      setSelectedItem(getName);
      dispatch(userModuleUser(getName));
    }
  }, [moduleList]); //eslint-disable-line

  const selectedUserId = userProgramDetails.getIn([userIndex, '_user_id', '_id'], '');
  const selectedValue = userPermission.getIn([userIndex, '_user_id', '_id'], '');
  const singleUserCount = selectedIconIds.filter((newData) => newData.includes(selectedValue));

  return (
    <div className="bg-light" style={{ height: '85vh' }}>
      <Paper className="d-flex">
        <div className="m-3">
          <h4>{selectedItem} User Settings</h4>
        </div>
        <div className="d-flex ml-auto">
          <div>
            <Box sx={{ minWidth: 200, marginTop: '7px' }}>
              <FormControl sx={{ m: 1, minWidth: 250 }} size="small">
                <Select
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  value={selectedItem}
                  onChange={handleChangeModuleList}
                >
                  {filteredModuleList.map((moduleListItem, moduleListIndex) => {
                    return (
                      <MenuItem key={moduleListIndex} value={moduleListItem.get('name', '')}>
                        {moduleListItem.get('name', '')}
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </Box>
          </div>
          <div className="m-3">
            <MButton
              disabled={
                !userProgramDetails.some((userDetails) => userDetails.get('selectedUser', false))
              }
              className="mr-2"
              variant="contained"
              color="primary"
              clicked={handlePostApi}
            >
              SAVE & UPDATE
            </MButton>
          </div>
        </div>
      </Paper>
      <div className="row my-3 mx-1">
        {userProgramDetails.size !== 0 ? (
          <>
            <div className="col-md-6 pr-1">
              <Paper sx={{ height: '64vh' }} className="inner-overflow">
                <div className="d-flex">
                  <div className="ml-3 mt-4">User</div>
                  <div className="mt-3 mr-2 ml-auto mb-1">
                    <MaterialInput
                      elementType="materialSearch"
                      value={userSearch}
                      placeholder="Search by user name, ID"
                      changed={handleUserSearch}
                      addClass={'rounded-border'}
                      removeIcon={true}
                      handleClear={handleUserClose}
                    />
                  </div>
                </div>
                <Divider className="mx-3" />
                {filteredUsers.size !== 0 ? (
                  <div className="mx-3 mt-2 Auto-OverFlow">
                    {filteredUsers.map((user, index) => (
                      <div key={index}>
                        <div
                          className={` px-2 pt-2 ${
                            user.get('checkColor', false) ? 'bg-lite-green' : 'bg-lite'
                          }`}
                          onClick={() => handleClick(user, index)}
                        >
                          <div className="f-15 bold">
                            {user.getIn(['_user_id', 'name', 'first'])}{' '}
                            {user.getIn(['_user_id', 'name', 'last'])}
                          </div>
                          <div className="text-muted f-13">
                            {user.getIn(['_user_id', 'user_id'])}
                          </div>
                          <div className="f-14 mt-1 pb-1">
                            {user
                              .get('roles', List())
                              .map((userName) => userName.getIn(['_role_id', 'name']))
                              .join(',')}
                          </div>
                        </div>
                        <Divider />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div
                    className=" f6 d-flex justify-content-center py-3"
                    style={{ height: '50vh' }}
                  >
                    No Data Found
                  </div>
                )}
              </Paper>
            </div>
            <div className="col-md-6 position-relative pl-1">
              {userIndex !== null ? (
                <Paper sx={{ height: '64vh' }} className="inner-overflow">
                  <div>
                    <div className="d-flex" style={{ zIndex: '99999', top: '0px' }}>
                      <FormGroup>
                        <FormControlLabel
                          control={<Checkbox checked={checkAllProgramStatus()} />}
                          label="Program"
                          onChange={(e) => handleAllChange(e)}
                          className="mx-3 mt-3"
                        />
                      </FormGroup>
                      <div className="mt-3 mr-2 ml-auto ">
                        <MaterialInput
                          elementType="materialSearch"
                          className="pt-2 pb-2"
                          value={listSearch}
                          placeholder="Search by code, name"
                          changed={handleListSearch}
                          addClass={'rounded-border'}
                          removeIcon={true}
                          handleClear={handleProgramClose}
                        />
                      </div>
                    </div>
                    <Divider className="mx-3 mb-2" />
                  </div>
                  <div className="Auto-OverFlow_program">
                    <div className="mx-3">
                      {singleUserCount.size !== 0 ? (
                        <div
                          style={{
                            zIndex: '99',
                            top: '0px',
                            backgroundColor: '#F3F4F6',
                            position: 'sticky',
                          }}
                        >
                          <div className="px-3 py-2 f-13">
                            N0. Of Program Assigned : {singleUserCount.size}
                          </div>
                          <Divider />
                        </div>
                      ) : (
                        ''
                      )}
                      {filteredList.size !== 0 ? (
                        <div>
                          {filteredList.map((filteredDetails, filteredIndex) => {
                            return (
                              <>
                                <Accordion
                                  expanded={programId === filteredDetails.get('_id', '')}
                                  onChange={(e) => handleChange(filteredDetails, filteredIndex)}
                                  elevation={0}
                                  sx={{
                                    '& .MuiPaper-root-MuiAccordion-root.Mui-expanded': {
                                      margin: '0px',
                                    },
                                    '&:not(:last-child)': {
                                      borderBottom: '1px solid #D1D5DB',
                                      marginBottom: '0px',
                                    },
                                    '&:before': {
                                      display: 'none',
                                    },
                                    '& .MuiAccordion-root.Mui-expanded': {
                                      margin: '0px',
                                    },
                                  }}
                                >
                                  <AccordionSummary
                                    expandIcon={<ArrowDropDownIcon color="primary" />}
                                    className="px-0 my-0"
                                    sx={{
                                      '& .MuiAccordionSummary-content': { margin: '0px' },
                                      '&.MuiAccordionSummary-root.Mui-expanded': {
                                        minHeight: '40px',
                                      },
                                      '& .MuiAccordionSummary-content.Mui-expanded': {
                                        margin: '0px',
                                      },
                                    }}
                                  >
                                    <div className="d-flex align-items-center w-100">
                                      <Checkbox
                                        checked={filteredDetails.get('status', false)}
                                        onChange={(e) =>
                                          handleIsChecked(e.target.checked, filteredDetails)
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                      />
                                      <div className="f-15">
                                        <div className="d-sm-block d-md-none d-sm-none d-lg-block d-xl-block">
                                          {filteredDetails.get('code')} -{' '}
                                          {getShortString(filteredDetails.get('name'), 40)}
                                        </div>
                                        <div className="d-sm-none d-md-block d-sm-none d-lg-none d-xl-none">
                                          {filteredDetails.get('code')} -{' '}
                                          {getShortString(filteredDetails.get('name'), 17)}
                                        </div>
                                      </div>
                                      {selectedIconIds.includes(
                                        selectedUserId + filteredDetails.get('_id', '')
                                      ) && (
                                        <div className="ml-auto mt-2">
                                          <CheckCircleIcon color="success" />
                                        </div>
                                      )}
                                    </div>
                                  </AccordionSummary>
                                  <AccordionDetails
                                    sx={{
                                      '&.MuiAccordionDetails-root': {
                                        padding: '0px 0px 10px 15px',
                                      },
                                    }}
                                  >
                                    {programId === filteredDetails.get('_id', '') && (
                                      <ProgramDetailsList
                                        userProgramDetails={userProgramDetails}
                                        setUserProgramDetails={setUserProgramDetails}
                                        userIndex={userIndex}
                                        programDetails={programDetails}
                                        programId={programId}
                                        checkAllData={checkAllData}
                                        selectedCourses={selectedCourses}
                                        handleIsChecked={handleIsChecked}
                                        setSelectedCourse={setSelectedCourse}
                                      />
                                    )}
                                  </AccordionDetails>
                                </Accordion>
                              </>
                            );
                          })}
                        </div>
                      ) : (
                        <div className=" f6 d-flex justify-content-center py-3">No Data Found</div>
                      )}
                    </div>
                  </div>
                </Paper>
              ) : (
                <Paper
                  className="d-flex align-items-center pageDisableMode"
                  sx={{ height: '64vh' }}
                >
                  <div className="d-flex justify-content-center w-100">{`"Please Select User"`}</div>
                </Paper>
              )}
            </div>
          </>
        ) : (
          <>
            <div className="w-100 text-center">
              <div
                className="d-flex justify-content-center align-items-center w-100"
                style={{ height: '70vh' }}
              >
                {`"We couldn't find the information because it seems the user hasn't been assigned to
                it."`}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AnnouncementSettingsIndex;
