import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

const institutionKeys = {
  group_institute_name: 'University Name',
  individual_institute_name: 'College Name',
  no_of_college: 'No of colleges allowed',
  address_details: {
    address: 'Address',
    country: 'Country',
    state: 'State / Region',
    city: 'City',
    district: 'District',
    zipcode: 'ZIP Code',
  },
};

function InstitutionView({ institution, showBoxShadow }) {
  return (
    <div className={`outter-login ${showBoxShadow ? '' : 'institution-view'}`}>
      <div className="row pt-2 pb-2">
        <div className="col-md-6">
          <div className="logo_view">
            <img
              src={institution.get('logo')}
              alt="logo"
              className="img-fluid logo-img-border height_150px width_150px"
            />
          </div>
        </div>
      </div>
      <div className="row pt-4">
        <div className="col-md-5">
          <p className="mb-3 f-15 text-gray">
            {institutionKeys[`${institution.get('institute_type', '')}_institute_name`]}
          </p>
        </div>
        <div className="col-md-7">
          <p className="mb-3 f-16 bold">{institution.get('institute_name', '')}</p>
        </div>
        {institution.get('institute_type', '') === 'group' && (
          <>
            <div className="col-md-5">
              <p className="mb-3 f-15 text-gray">{institutionKeys['no_of_college']}</p>
            </div>
            <div className="col-md-7">
              <p className="mb-3 f-16 bold">{institution.get('no_of_college', '')}</p>
            </div>
          </>
        )}
        <div className="col-md-12">
          <p className="pt-2 pb-4 mb-0 bold f-16">Address Details</p>
        </div>
        {Object.keys(institutionKeys.address_details).map((k, i) => (
          <React.Fragment key={`${k}-${i}`}>
            <div className="col-md-5">
              <p className="mb-3 f-15 text-gray">{institutionKeys.address_details[k]}</p>
            </div>
            <div className="col-md-7">
              <p className="mb-3 f-16 bold">{institution.getIn(['address_details', k], '')}</p>
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

InstitutionView.propTypes = {
  institution: PropTypes.instanceOf(Map),
  showBoxShadow: PropTypes.bool,
};

export default InstitutionView;
