import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Map } from 'immutable';
import PropTypes from 'prop-types';
import { with<PERSON>out<PERSON>, <PERSON> } from 'react-router-dom';

import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import Programs from './components/Programs';
import ExtraCurricularAndBreak from './components/ExtraCurricularAndBreak/Index';
import RemoteRooms from './components/RemoteRooms/RemoteRooms';
import CourseCoordinators from './components/CourseCoordinators/CourseCoordinators';
import Schedule from './components/Schedule/Schedule';
import { selectIsLoading } from '../../_reduxapi/program_input/selectors';
import { selectLoading, selectMessage } from '../../_reduxapi/course_scheduling/selectors';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import './css/courseSchedule.css';
import './css/styles.css';
import { t } from 'i18next';

const breadcrumbItems = [
  { path: 'schedule', label: 'Course Scheduling' },
  { path: 'course-coordinators', label: 'Assign Course Coordinator' },
  { path: 'extra-curricular-break', label: 'Extra curricular & Break' },
  { path: 'remote', label: 'Infrastructure Management - Remote' },
];

class CourseScheduling extends Component {
  isLoading() {
    return this.props.loading.valueSeq().some((value) => value);
  }

  getLabel() {
    const { location } = this.props;
    const matched = breadcrumbItems.find((item) => location.pathname.includes(item.path));
    if (!matched) return 'Course Scheduling';
    return matched.label;
  }

  render() {
    const { message, isProgramsLoading } = this.props;
    const items = [{ to: '#', label: this.getLabel() }];
    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={this.isLoading() || isProgramsLoading} />
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {t(`infra_management.bread_crumb.${label}`)}
            </Link>
          ))}
        </Breadcrumb>
        <Switch>
          {(CheckPermission(
            'tabs',
            'Schedule Management',
            'Extra Curricular and Break',
            '',
            'Extra Curricular',
            'View'
          ) ||
            CheckPermission(
              'tabs',
              'Schedule Management',
              'Extra Curricular and Break',
              '',
              'Break',
              'View'
            )) && (
            <Route
              exact
              path="/course-scheduling/extra-curricular-break/list"
              component={ExtraCurricularAndBreak}
            />
          )}
          {CheckPermission(
            'tabs',
            'Infrastructure Management',
            'Remote',
            '',
            'Level List',
            'View'
          ) && <Route exact path="/course-scheduling/remote/list" component={RemoteRooms} />}
          {CheckPermission(
            'tabs',
            'Schedule Management',
            'Assign Course Coordinator',
            '',
            'Course List',
            'View'
          ) && (
            <Route
              exact
              path="/course-scheduling/course-coordinators/list"
              component={CourseCoordinators}
            />
          )}
          {(CheckPermission(
            'tabs',
            'Schedule Management',
            'Course Scheduling',
            '',
            'Schedule',
            'Course View'
          ) ||
            CheckPermission(
              'tabs',
              'Schedule Management',
              'Course Scheduling',
              '',
              'TimeTable',
              'View'
            )) && <Route exact path="/course-scheduling/schedule/list" component={Schedule} />}

          {(CheckPermission(
            'pages',
            'Schedule Management',
            'Extra Curricular and Break',
            'List View'
          ) ||
            CheckPermission(
              'pages',
              'Schedule Management',
              'Assign Course Coordinator',
              'List View'
            ) ||
            CheckPermission('pages', 'Schedule Management', 'Course Scheduling', 'List View') ||
            CheckPermission(
              'tabs',
              'Infrastructure Management',
              'Remote',
              '',
              'Level List',
              'View'
            )) && <Route path="/course-scheduling/:type" component={Programs}></Route>}
        </Switch>
      </div>
    );
  }
}

CourseScheduling.propTypes = {
  location: PropTypes.object,
  message: PropTypes.string,
  loading: PropTypes.instanceOf(Map),
  isProgramsLoading: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    loading: selectLoading(state),
    message: selectMessage(state),
    isProgramsLoading: selectIsLoading(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(CourseScheduling);
