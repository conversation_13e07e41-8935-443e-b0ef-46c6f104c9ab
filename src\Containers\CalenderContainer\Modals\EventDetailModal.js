import React from 'react';
import PropTypes from 'prop-types';
import { Badge, Modal } from 'react-bootstrap';
import { t } from 'i18next';
import moment from 'moment';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { jsUcfirstAll } from '../../../utils';

function EventDetailModal({
  show,
  currentCalendar,
  eventState,
  onClose,
  getTranslatedDate,
  eventEdit,
  eventDeleteConfirm,
}) {
  const {
    evType,
    evName,
    evArab1,
    evDetail,
    evArab2,
    evStartDate,
    evEndDate,
    evStartTime,
    evEndTime,
  } = eventState;
  return (
    <Modal show={show} centered size="lg" onHide={onClose}>
      <React.Fragment>
        <Modal.Header closeButton>
          <div className="row w-100">
            <div className="col-md-10 pt-1">{t('events.event_details')}</div>
            <div className="col-md-2 pt-1 ">
              {currentCalendar &&
                CheckPermission(
                  'tabs',
                  'Calendar',
                  'Institution Calendar',
                  '',
                  'Event details',
                  'Edit'
                ) && (
                  <span
                    className="remove_hover btn btn-outline-primary float-right"
                    onClick={eventEdit}
                  >
                    {t('edit')}
                  </span>
                )}
            </div>
          </div>
        </Modal.Header>
        <Modal.Body>
          <h5>
            <Badge variant="secondary">{t(`events.event_types.${evType}`)}</Badge>
          </h5>
          <div className="row pt-4">
            <div className="col-md-6">
              <h3 className="f-22"> {jsUcfirstAll(evName)}</h3>
              <p className="f-18"> {evArab1} </p>
            </div>
            <div className="col-md-6">
              <div className=" f-22 rtl-text float-right">
                <h3 className=" f-22 "> {evDetail}</h3>
                <p className="f-18"> {evArab2} </p>
              </div>
            </div>
          </div>
          <p className="f-14 mb-2">
            {' '}
            {t('Date')} : {getTranslatedDate(evStartDate, evEndDate)}
            {/* {evStartDate !== ''
                    ? moment(evStartDate).format('MMM Do')
                    : 'Loading...'}{' '}
                  -{' '}
                  {evEndDate !== ''
                    ? moment(evEndDate).format('Do MMM YY')
                    : 'Loading...'}{' '} */}
          </p>
          <p className="f-14 mb-2">
            {t('global_configuration.time')} :
            {evStartTime !== '' ? moment(evStartTime).format('hh:mm  A') : 'Loading...'} -{' '}
            {evEndTime !== '' ? moment(evEndTime).format('hh:mm  A') : 'Loading...'}{' '}
          </p>
          {/* <p className="f-14 m-0">Venue: </p> */}
        </Modal.Body>
        <Modal.Footer>
          {currentCalendar &&
            CheckPermission(
              'tabs',
              'Calendar',
              'Institution Calendar',
              '',
              'Event details',
              'Delete'
            ) && (
              <span className="remove_hover btn btn-danger" onClick={eventDeleteConfirm}>
                {t('delete')}
              </span>
            )}
          <span className="remove_hover btn btn-primary" onClick={onClose}>
            {t('cancel')}
          </span>
        </Modal.Footer>
      </React.Fragment>
    </Modal>
  );
}

EventDetailModal.propTypes = {
  show: PropTypes.bool,
  currentCalendar: PropTypes.bool,
  eventState: PropTypes.object,
  onClose: PropTypes.func,
  getTranslatedDate: PropTypes.func,
  eventEdit: PropTypes.func,
  eventDeleteConfirm: PropTypes.func,
};

export default EventDetailModal;
