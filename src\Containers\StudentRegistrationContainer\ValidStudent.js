/* eslint-disable react/jsx-key */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Table, Modal, Button } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import Search from '../../Widgets/Search/Search';
import Pagination from '../../Components/StaffManagement/Pagination';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import * as actions from '../../_reduxapi/user_management/action';
import Export from './Export';
import { getEnvCollegeName } from 'utils';
import CKEditor5 from 'Widgets/CkEditor/CkEditor';

class ValidStudent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      registrationConfirmationShow: false,
      mailId: '',
      message: '',
      searchText: '',
      limit: 10,
      totalPages: 1,
      pageLength: 1,
    };
    this.timeout = 0;
  }

  componentDidMount() {
    this.fetchApi({});
  }

  successTrigger = (res) => {
    const data = res.data.data.map((data) => {
      return {
        id: data._id,
        family: data.name.family,
        academic: data.academic,
        email: data.email,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        gender: data.gender,
        nationality_id: data.address.nationality_id,
        enrollment_year: data.enrollment_year,
        program_no: data.program_no,
        isChecked: false,
      };
    });
    this.setState({
      data: data,
      isLoading: false,
      totalPages: res.data.totalPages,
    });
  };

  fetchApi = ({ limit = 10, pageLength = 1 }) => {
    const { getUserManagementList } = this.props;
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });

    getUserManagementList(
      {
        searchText: searchText,
        type: 'student',
        status: 'valid',
        limit: limit,
        pageLength: pageLength,
        slug: 'get_all',
      },
      this.successTrigger,
      () => {
        this.setState({
          data: [],
          isLoading: false,
          totalPages: 1,
          pageLength: 1,
        });
      }
    );
  };

  handleClickRegistrationConfirmation = () => {
    this.setState({
      registrationConfirmationShow: true,
    });
  };

  handleClickRegistrationConfirmationClose = () => {
    this.setState({
      registrationConfirmationShow: false,
    });
  };

  pagination = (e) => {
    this.setState(
      {
        limit: e.target.value,
        pageLength: 1,
      },
      () => {
        setTimeout(() => {
          this.fetchApi({ limit: this.state.limit });
        }, 500);
      }
    );
  };

  pageCount = (value) => {
    this.setState(
      {
        pageLength: value,
      },
      () => {
        this.fetchApi({ pageLength: value, limit: this.state.limit });
      }
    );
  };

  handleAllChecked = (event) => {
    let data = this.state.data;
    data.map((data) => (data.isChecked = event.target.checked));
    this.setState({ data: data });
  };

  handleCheckChieldElement = (event, index) => {
    let data = this.state.data;

    data[index].isChecked = event.target.checked;
    this.setState({ data: data });
  };

  handleClickMailPush = () => {
    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });
    if (mailData.length > 0) {
      let id = mailData.map((data) => {
        return data.id;
      });

      this.setState({
        registrationConfirmationShow: true,
        mailId: id,
        message: `<p>Dear User,<br><br>
        Greetings from ${getEnvCollegeName()}<br><br>
        Kindly visit admin office to complete your bio-metric and registration process
        <br><br>
        Best Regards<br>
        ${getEnvCollegeName()}</p>`,
      });
    } else {
      NotificationManager.error(t(`user_management.Choose_atLeast_one_checkbox`), '', 2000);
    }
  };

  handleSendMail = (e) => {
    const data = {
      type: 'valid',
      message: this.state.message,
      id: this.state.mailId,
    };
    this.setState({
      isLoading: true,
    });
    axios
      .post(`user/user_mail_push`, data)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
            registrationConfirmationShow: false,
          },
          () => {
            this.fetchApi({});
          }
        );

        NotificationManager.success(t(`user_management.Mail_Sent_Successfully`));
      })
      .catch((error) => {
        NotificationManager.warning(`${error.response.data.message}`);
        this.setState({
          isLoading: false,
        });
      });
  };

  handleClickView = (data) => {
    this.props.history.push({
      pathname: '/studentvalid/all',
      search: '?id=' + data.id + '&tabs=6',
    });
  };

  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      const { limit } = this.state;
      setTimeout(() => {
        this.fetchApi({ limit });
        this.setState({ pageLength: 1 });
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState({ searchText: e.target.value }, () => this.doSearch(e));
  };

  checkProfilePermission = () => {
    return (
      CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'Valid',
        'Profile View'
      ) ||
      CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'Valid',
        'Biometric View'
      )
    );
  };

  render() {
    const header = [
      <Trans i18nKey={'academic_no'}></Trans>,
      <Trans i18nKey={'email'}></Trans>,
      <Trans i18nKey={'first_name'}></Trans>,
      'Middle Name',
      'Last Name',
      <Trans i18nKey={'gender'}></Trans>,
      <Trans i18nKey={'national_id'}></Trans>,
    ];

    const totalSelect = this.state.mailId.length;
    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });

    return (
      <div className="main bg-gray pt-2 pb-5">
        <Loader isLoading={this.state.isLoading} />

        <div className="container-fluid">
          <div className="">
            <div className="float-left  pt-2 pl-3">
              <span className="mr-0">
                {CheckPermission(
                  'subTabs',
                  'User Management',
                  'Student Management',
                  '',
                  'Registration Pending',
                  '',
                  'Valid',
                  'Send Mail'
                ) && (
                  <Button
                    disabled={mailData.length > 0 ? false : true}
                    className={mailData.length > 0 ? '' : 'disabled-icon'}
                    onClick={(e) => this.handleClickMailPush(e)}
                  >
                    <Trans i18nKey={'send_mail'}></Trans>{' '}
                    <i className="fa fa-envelope" aria-hidden="true"></i>{' '}
                  </Button>
                )}
              </span>

              {/* mail funtion start   */}

              <Modal
                show={this.state.registrationConfirmationShow}
                centered
                size="lg"
                onHide={this.handleClickRegistrationConfirmationClose}
              >
                <Modal.Header closeButton>
                  <div className="row w-100">
                    <div className="col-md-6 pt-1 font-weight-bold">
                      {totalSelect} Student Selected on this page
                    </div>
                    <div className="col-md-6">
                      <div className="float-right">
                        <Button onClick={(e) => this.handleSendMail(e)}>
                          <Trans i18nKey={'send_mail'}></Trans>{' '}
                          <i className="fa fa-envelope" aria-hidden="true"></i>{' '}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Modal.Header>

                <Modal.Body>
                  <CKEditor5
                    data={`<p>Dear User,<br><br>
                    Greetings from ${getEnvCollegeName()}<br><br>
                    Kindly visit admin office to complete your bio-metric and registration process
                    <br><br>
                    Best Regards<br>
                    ${getEnvCollegeName()}</p>`}
                    onChange={(editor) => {
                      const data = editor.getData();
                      const data1 = data
                        .replace('http://college.com/students', 'v_link')
                        .replace('AutoGeneratedTemporaryPassword', 'v_password')
                        .replace('72hrs', 'v_date_expired')
                        .replace('Sender', 'v_admin_sign');
                      this.setState({
                        message: data1,
                      });
                    }}
                  />
                </Modal.Body>
              </Modal>

              {/* mail funtion end */}
            </div>
            <div className="float-right p-2 d-flex">
              <Export userType="student" status="valid" />
              {CheckPermission(
                'subTabs',
                'User Management',
                'Student Management',
                '',
                'Registration Pending',
                '',
                'Valid',
                'Search'
              ) && (
                <Search
                  value={this.state.searchText}
                  onChange={(e) => this.handleSearch(e)}
                  type="student"
                />
              )}
            </div>
            <div className="clearfix"> </div>
            <React.Fragment>
              <div className="p-3">
                <div className="dash-table">
                  <Table responsive hover>
                    <thead className="th-change">
                      <tr>
                        <th>
                          {' '}
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={this.handleAllChecked}
                            value="checkedall"
                          />
                        </th>
                        {header.map((header, i) => (
                          <th key={i}>{header}</th>
                        ))}
                      </tr>
                    </thead>
                    {this.state.data.length === 0 ? (
                      <TableEmptyMessage />
                    ) : (
                      <tbody>
                        {this.state.data.map((data, index) => (
                          <tr
                            key={index}
                            className="tr-change"
                            style={{
                              background: data.isChecked === true ? '#D1F4FF' : '',
                            }}
                          >
                            {data.isChecked !== true ? (
                              <td>
                                <input
                                  type="checkbox"
                                  className="calendarFormRadio"
                                  onClick={(event) => this.handleCheckChieldElement(event, index)}
                                  value="checkedall"
                                />
                              </td>
                            ) : (
                              <td>
                                <input
                                  type="checkbox"
                                  className="calendarFormRadio"
                                  onClick={(event) => this.handleCheckChieldElement(event, index)}
                                  value="checkedall"
                                  checked
                                />
                              </td>
                            )}
                            <td
                              className={this.checkProfilePermission() ? 'profile_view' : ''}
                              onClick={
                                this.checkProfilePermission()
                                  ? (e) => this.handleClickView(data)
                                  : () => {}
                              }
                            >
                              {data.academic}
                            </td>
                            <td>{data.email}</td>
                            <td>{data.first}</td>
                            <td>{data.middle}</td>
                            <td>{data.last}</td>
                            <td>{data.gender}</td>
                            <td>{data.nationality_id}</td>
                          </tr>
                        ))}
                      </tbody>
                    )}
                  </Table>
                </div>
                <Pagination
                  totalPages={this.state.totalPages}
                  switchPagination={this.pagination}
                  switchPageCount={this.pageCount}
                  pageLength={this.state.pageLength}
                />
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  }
}

ValidStudent.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
};

export default connect(null, actions)(withRouter(ValidStudent));
