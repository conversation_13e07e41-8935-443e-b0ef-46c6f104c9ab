import React, { Suspense, useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { List, Map, fromJS } from 'immutable';
import TableFilter from './tableFilter';
import {
  getNotStartedUpcoming,
  getStatusColor,
  getNonStartedSchedule,
  getInfraName,
} from '../utils';
import moment from 'moment';
import { getTimestamp, getVersionName, jsUcfirstAll } from 'utils';
import MergeIcon from 'Assets/merge_new.svg';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import Pagination from 'Modules/StudentGrouping/components/Pagination';
import { Badge } from '@mui/material';

const SingleViewModal = React.lazy(() => import('./modal/singleViewModal'));

export const getCourseNameWithCode = (item) => {
  return `${item.get('course_code', '')} - ${item.get('course_name', '')}`;
};

function TableModule({
  tableList,
  type,
  lateStarted,
  earlyEnded,
  sessionStatus,
  termYear,
  programStatus,
  courseStatus,
  CheckPermission,
  search,
  currentDate,
  setExportCurrent,
  exportCurrent,
  actualFilterData,
  setActualFilterData,
}) {
  const [showFilter, setShowFilter] = useState(null);
  const [filterType, setFilterType] = useState(null);
  const [singleView, setSingleView] = useState(false);
  const [statusData, setStatusData] = useState();
  const [sStatus, setStatus] = useState(fromJS([]));
  const [imgData, setImgData] = useState();

  const filterValue = {
    allData: {
      programFilter: [],
      courseFilter: [],
      deliveryFilter: [],
      modeFilter: [],
      statusFilter: [],
      remarkFilter: [],
      staffFilter: [],
      termFilter: {
        term: '',
        years: [],
        levels: [],
      },
    },
    curricularData: {
      programFilter: [],
      courseFilter: [],
      deliveryFilter: [],
      modeFilter: [],
      statusFilter: [],
      remarkFilter: [],
      staffFilter: [],
      termFilter: {
        term: '',
        years: [],
        levels: [],
      },
    },
  };
  const [checkedFilter, setCheckedFilter] = useState(fromJS(filterValue));

  const [cPagination, setCPagination] = useState({
    pageCount: 10,
    currentPage: 1,
  });

  const [aPagination, setAPagination] = useState({
    pageCount: 10,
    currentPage: 1,
  });

  const allDataFilter = checkedFilter.get('allData', Map());
  const curricularDataFilter = checkedFilter.get('curricularData', Map());
  const didMount2 = useRef(true);
  useEffect(() => {
    if (didMount2.current) {
      didMount2.current = false;
      return;
    }
    const copyPage = { ...aPagination };
    copyPage.currentPage = 1;
    copyPage.pageCount = 10;
    setAPagination(copyPage);
  }, [allDataFilter]); //eslint-disable-line

  const didMount1 = useRef(true);
  useEffect(() => {
    if (didMount1.current) {
      didMount1.current = false;
      return;
    }
    const copyPage = { ...cPagination };
    copyPage.currentPage = 1;
    copyPage.pageCount = 10;
    setCPagination(copyPage);
  }, [curricularDataFilter]); //eslint-disable-line

  const didMount = useRef(true);
  useEffect(() => {
    if (didMount.current) {
      didMount.current = false;
      return;
    }
    const copyPage = { ...aPagination };
    copyPage.currentPage = 1;
    setAPagination(copyPage);
    const cCopyPage = { ...cPagination };
    cCopyPage.currentPage = 1;
    setCPagination(copyPage);
    setCheckedFilter(fromJS(filterValue));
  }, [tableList]); //eslint-disable-line
  const openFilter = Boolean(showFilter);
  const handleClickFilter = (event, type) => {
    setShowFilter(event.currentTarget);
    setFilterType(type);
  };
  // const getTimeFormat = (item) => {
  //   let hoursZero = item.get('hour', '') < 10 ? '0' + item.get('hour', '') : item.get('hour', '');
  //   let minutesZero =
  //     item.get('minute', '') < 10 ? '0' + item.get('minute', '') : item.get('minute', '');
  //   return hoursZero + ':' + minutesZero + ' ' + item.get('format', '');
  // };

  const getData = () => {
    return tableList
      .get('scheduleList', List())
      .filter((item) =>
        type === 'curricularData'
          ? ['missed', 'pending', 'cancelled'].includes(item.get('status')) ||
            item.get('isActive', false) === false
          : true
      )
      .filter((item) =>
        (type === 'curricularData' && item.get('status', '') === 'pending') ||
        item.get('isActive', false) === false
          ? item.get('isActive', false) === false
            ? item.get('isActive', false) === false
            : getNonStartedSchedule(item)
          : true
      )
      .map((item) => {
        if (
          type === '' &&
          lateStarted !== '' &&
          item.getIn(['sessionDetail', 'start_time'], '') !== ''
        ) {
          const lateStartCheckTime = moment(item.get('scheduleStartDateAndTime', new Date()))
            .utc()
            .add(lateStarted, 'minutes')
            .format();
          return Date.parse(lateStartCheckTime) <
            Date.parse(item.getIn(['sessionDetail', 'start_time'], ''))
            ? item.set('sessionStatus', fromJS(['Late Started']))
            : item;
        }
        return item;
      })
      .map((item) => {
        if (
          type === '' &&
          earlyEnded !== '' &&
          item.getIn(['sessionDetail', 'stop_time'], '') !== '' &&
          item.get('status', '') === 'completed'
        ) {
          const earlyEndCheckTime = moment(item.get('scheduleEndDateAndTime', new Date()))
            .utc()
            .subtract(earlyEnded, 'minutes')
            .format();
          let sessionStatus = item.get('sessionStatus', List()).toJS();
          sessionStatus = [...sessionStatus, 'Early Ended'];
          return Date.parse(earlyEndCheckTime) >
            Date.parse(new Date(item.getIn(['sessionDetail', 'stop_time'], '')))
            ? item.set('sessionStatus', fromJS(sessionStatus))
            : item;
        }
        return item;
      })
      .map((item) => {
        let sessionStatus = item.get('sessionStatus', List()).toJS();
        sessionStatus = [...sessionStatus, 'Merged'];
        return item.get('merge_with', List()).size > 0
          ? item.set('sessionStatus', fromJS(sessionStatus))
          : item;
      });
  };

  const aPageChange = (value) => {
    const copyPage = { ...aPagination };
    copyPage.pageCount = value;
    copyPage.currentPage = 1;
    setAPagination(copyPage);
  };

  const cPageChange = (value) => {
    const copyPage = { ...cPagination };
    copyPage.pageCount = value;
    copyPage.currentPage = 1;
    setCPagination(copyPage);
  };

  const paginationData = {
    pageCount: type === '' ? aPagination.pageCount : cPagination.pageCount,
    currentPage: type === '' ? aPagination.currentPage : cPagination.currentPage,
    pagination: type === '' ? aPageChange : cPageChange,
  };

  useEffect(() => {
    if (type === '') {
      setExportCurrent(
        exportCurrent
          .set('allCurrentPage', aPagination.currentPage)
          .set('allPageCount', aPagination.pageCount)
      );
    } else {
      setExportCurrent(
        exportCurrent
          .set('curriculumCurrentPage', cPagination.currentPage)
          .set('curriculumPageCount', cPagination.pageCount)
      );
    }
  }, [setExportCurrent, aPagination, cPagination, type, exportCurrent]);

  const mapValue = type === '' ? 'allData' : 'curricularData';
  const actualData = getData()
    .filter((item) => {
      return search !== ''
        ? item.get('program_name', '').toLowerCase().includes(search.toLowerCase())
        : item;
    })
    .filter((item) => {
      const hasProgram = checkedFilter.getIn([mapValue, 'programFilter'], List());
      return hasProgram.size > 0 ? hasProgram.includes(item.get('program_name')) : item;
    })
    .filter((item) => {
      const hasCourse = checkedFilter.getIn([mapValue, 'courseFilter'], List());
      return hasCourse.size > 0 ? hasCourse.includes(getCourseNameWithCode(item)) : item;
    })
    .filter((item) => {
      const hasMode = checkedFilter.getIn([mapValue, 'modeFilter'], List());
      return hasMode.size > 0
        ? hasMode.includes(item.get('mode')) ||
            hasMode.includes(item.get('classModeType', '')) ||
            hasMode.includes(item.get('scheduleStartFrom', ''))
        : item;
    })
    .filter((item) => {
      const hasStatus = checkedFilter.getIn([mapValue, 'statusFilter'], List());
      return hasStatus.size > 0
        ? hasStatus.includes(
            item.get('isActive', false) === false
              ? 'cancelled'
              : item.get('status', '') !== 'pending'
              ? item.get('status', '')
              : getNotStartedUpcoming(item)
          )
        : item;
    })
    .filter((item) => {
      const hasStatus = checkedFilter.getIn([mapValue, 'remarkFilter'], List());
      const sessionStatus = item.get('sessionStatus', List());
      return hasStatus.size > 0 ? hasStatus.some((i) => sessionStatus.includes(i)) : item;
    })
    .filter((item) => {
      const hasDelivery = checkedFilter.getIn([mapValue, 'deliveryFilter'], List());
      return hasDelivery.size > 0
        ? hasDelivery.includes(item.getIn(['session', 'delivery_symbol'], '')) ||
            hasDelivery.includes(item.get('type', ''))
        : item;
    })
    .filter((item) => {
      const hasStaff = checkedFilter.getIn([mapValue, 'staffFilter'], List());
      const staffItems = item
        .get('staffs', List())
        .map((item) =>
          jsUcfirstAll(
            `${item.getIn(['staff_name', 'first'], '')} ${item.getIn(
              ['staff_name', 'middle'],
              ''
            )} ${item.getIn(['staff_name', 'last'], '')}`
          )
        );
      return hasStaff.size > 0 ? hasStaff.some((i) => staffItems.includes(i)) : item;
    })
    .filter((item) => {
      const hasTerm = checkedFilter.getIn([mapValue, 'termFilter', 'term'], '');
      const hasYears = checkedFilter.getIn([mapValue, 'termFilter', 'years'], List());
      const hasLevels = checkedFilter.getIn([mapValue, 'termFilter', 'levels'], List());

      if (hasLevels.size > 0) {
        return (
          item.get('term', '') === hasTerm &&
          hasYears.includes(item.get('year_no', '')) &&
          hasLevels.includes(item.get('level_no', ''))
        );
      } else if (hasYears.size > 0) {
        return item.get('term', '') === hasTerm && hasYears.includes(item.get('year_no', ''));
      } else if (hasTerm !== '') {
        return hasTerm !== '' ? item.get('term', '') === hasTerm : item;
      }
      return item;
    });
  const handleCloseFilter = () => {
    setShowFilter(null);
    setActualFilterData(
      actualFilterData.set(type === '' ? 'allFilter' : 'curriculumFilter', actualData)
    );
  };
  const totalPages =
    actualData?.size % paginationData.pageCount === 0
      ? actualData?.size / paginationData.pageCount
      : Math.floor(actualData?.size / paginationData.pageCount) + 1;
  paginationData.totalPages = totalPages;

  const onANextClick = () => {
    const copyPage = { ...aPagination };
    if (copyPage.currentPage < paginationData.totalPages) {
      copyPage.currentPage += 1;
      setAPagination(copyPage);
    }
  };
  const onCNextClick = () => {
    const copyPage = { ...cPagination };
    if (copyPage.currentPage < paginationData.totalPages) {
      copyPage.currentPage += 1;
      setCPagination(copyPage);
    }
  };
  const onABackClick = () => {
    const copyPage = { ...aPagination };
    if (copyPage.currentPage > 1) {
      copyPage.currentPage -= 1;
      setAPagination(copyPage);
    }
  };
  const onCBackClick = () => {
    const copyPage = { ...cPagination };
    if (copyPage.currentPage > 1) {
      copyPage.currentPage -= 1;
      setCPagination(copyPage);
    }
  };
  const onAFullLastClick = () => {
    const copyPage = { ...aPagination };
    if (copyPage.currentPage !== 1) {
      copyPage.currentPage = 1;
      setAPagination(copyPage);
    }
  };
  const onCFullLastClick = () => {
    const copyPage = { ...cPagination };
    if (copyPage.currentPage !== 1) {
      copyPage.currentPage = 1;
      setCPagination(copyPage);
    }
  };

  const onAFullForwardClick = () => {
    const copyPage = { ...aPagination };
    if (copyPage.currentPage !== paginationData.totalPages) {
      copyPage.currentPage = paginationData.totalPages;
      setAPagination(copyPage);
    }
  };
  const onCFullForwardClick = () => {
    const copyPage = { ...cPagination };
    if (copyPage.currentPage !== paginationData.totalPages) {
      copyPage.currentPage = paginationData.totalPages;
      setCPagination(copyPage);
    }
  };
  paginationData.onNextClick = type === '' ? onANextClick : onCNextClick;
  paginationData.onBackClick = type === '' ? onABackClick : onCBackClick;
  paginationData.onFullLastClick = type === '' ? onAFullLastClick : onCFullLastClick;
  paginationData.onFullForwardClick = type === '' ? onAFullForwardClick : onCFullForwardClick;

  function getStatusHtml(item) {
    const status = getStatusColor(item);
    return <div className={`aw-100 text-capitalize ${status.color}`}>{status.name}</div>;
  }
  function getStatusName(item) {
    const status = getStatusColor(item);
    return status.name.toLowerCase();
  }
  const handleSingleViewOpen = (e, status, item) => {
    setSingleView(true);
    setStatusData(e);
    setImgData(status);
    setStatus(item);
  };
  const handleSingleViewClose = () => {
    setSingleView(false);
    setStatus(fromJS([]));
  };

  const hasViewPermission = CheckPermission(
    'pages',
    'Curriculum Monitoring',
    'Dashboard',
    type === '' ? 'All Deliveries View' : 'Curricular Delivery View'
  );

  const hasFilterPermission = CheckPermission(
    'pages',
    'Curriculum Monitoring',
    'Dashboard',
    type === '' ? 'All Deliveries Filter' : 'Curricular Delivery Filter'
  );

  const getFilterCount = (data, value) => {
    if (value === 'termFilter') {
      const hasTerm = checkedFilter.getIn([data, value, 'term'], ``) !== '' ? 1 : 0;
      const hasYears = checkedFilter.getIn([data, value, 'years'], List()).size;
      const hasLevels = checkedFilter.getIn([data, value, 'levels'], List()).size;
      const total = hasTerm + hasYears + hasLevels;
      if (total !== 0)
        return (
          <Badge
            badgeContent={total}
            sx={{
              '& .MuiBadge-badge': {
                color: 'white',
                backgroundColor: '#aba3a3',
              },
            }}
            className="ml-4 mr-2"
          ></Badge>
        );
    } else {
      if (checkedFilter.getIn([data, value], ``).size !== 0)
        return (
          <Badge
            badgeContent={checkedFilter.getIn([data, value], ``).size}
            sx={{
              '& .MuiBadge-badge': {
                color: 'white',
                backgroundColor: '#aba3a3',
              },
            }}
            className="ml-4 mr-2"
          ></Badge>
        );
    }
  };

  function getCurrentDateTime() {
    const currentTime = moment().format('H:mm:ss');
    let date = `${currentDate} ${currentTime}`;
    return new Date(date.replace(' ', 'T'));
  }

  function isCurrentDate() {
    const todayDate = moment().format('YYYY-MM-DD');
    return currentDate === todayDate;
  }

  const currentDateTime = getTimestamp(getCurrentDateTime());

  return (
    <React.Fragment>
      <div className="go-wrapper">
        <table className="table">
          <thead className="">
            <tr>
              {programStatus.size === 0 && courseStatus === '' && (
                <th>
                  <div
                    className="aw-150 d-flex align-items-center remove_hover"
                    onClick={(e) => hasFilterPermission && handleClickFilter(e, 'programFilter')}
                  >
                    Programs
                    {getFilterCount(`curricularData`, `programFilter`)}
                    {getFilterCount(`allData`, `programFilter`)}
                    {hasFilterPermission && <ArrowDropDownIcon />}{' '}
                  </div>
                </th>
              )}
              {termYear === 'all' && courseStatus === '' && (
                <th>
                  <div
                    className="aw-150 d-flex align-items-center remove_hover"
                    onClick={(e) => hasFilterPermission && handleClickFilter(e, 'courseFilter')}
                  >
                    Course
                    {getFilterCount(`curricularData`, `courseFilter`)}
                    {getFilterCount(`allData`, `courseFilter`)}
                    {hasFilterPermission && <ArrowDropDownIcon />}
                  </div>
                </th>
              )}

              {programStatus.size === 1 && termYear === 'all' && courseStatus === '' && (
                <th>
                  <div
                    className="aw-150 d-flex align-items-center remove_hover"
                    onClick={(e) => hasFilterPermission && handleClickFilter(e, 'termFilter')}
                  >
                    Term / Year / Level
                    {getFilterCount(`curricularData`, `termFilter`)}
                    {getFilterCount(`allData`, `termFilter`)}
                    {hasFilterPermission && <ArrowDropDownIcon />}
                  </div>
                </th>
              )}
              <th>
                <div
                  className="aw-100 d-flex align-items-center remove_hover"
                  onClick={(e) => hasFilterPermission && handleClickFilter(e, 'deliveryFilter')}
                >
                  Delivery
                  {getFilterCount(`curricularData`, `deliveryFilter`)}
                  {getFilterCount(`allData`, `deliveryFilter`)}
                  {hasFilterPermission && <ArrowDropDownIcon />}
                </div>
              </th>

              <th>
                <div
                  className={`remove_hover ${
                    programStatus.size === 0 && courseStatus === '' ? `aw-200` : `aw-300`
                  } d-flex align-items-center remove_hover`}
                  onClick={(e) => hasFilterPermission && handleClickFilter(e, 'staffFilter')}
                >
                  Staff
                  {getFilterCount(`curricularData`, `staffFilter`)}
                  {getFilterCount(`allData`, `staffFilter`)}
                  {hasFilterPermission && <ArrowDropDownIcon />}
                </div>
              </th>
              <th>
                <div
                  className="aw-100 d-flex align-items-center remove_hover"
                  onClick={(e) => hasFilterPermission && handleClickFilter(e, 'modeFilter')}
                >
                  Mode
                  {getFilterCount(`curricularData`, `modeFilter`)}
                  {getFilterCount(`allData`, `modeFilter`)}
                  {hasFilterPermission && <ArrowDropDownIcon />}
                </div>
              </th>
              <th>
                <div
                  className={`${
                    programStatus.size === 0 && courseStatus === '' ? `aw-150` : `aw-300`
                  }`}
                >
                  Scheduled Info
                </div>
              </th>
              {(sessionStatus.includes('late') ||
                sessionStatus.includes('early') ||
                sessionStatus.includes('merged')) &&
              type === '' ? (
                <th>
                  <div
                    className="aw-150 d-flex align-items-center remove_hover"
                    onClick={(e) => hasFilterPermission && handleClickFilter(e, 'remarkFilter')}
                  >
                    Remarks
                    {getFilterCount(`curricularData`, `remarkFilter`)}
                    {getFilterCount(`allData`, `remarkFilter`)}
                    {hasFilterPermission && <ArrowDropDownIcon />}
                  </div>
                </th>
              ) : (
                ''
              )}
              <th>
                <div
                  className="aw-100 d-flex align-items-center remove_hover"
                  onClick={(e) => hasFilterPermission && handleClickFilter(e, 'statusFilter')}
                >
                  Status
                  {getFilterCount(`curricularData`, `statusFilter`)}
                  {getFilterCount(`allData`, `statusFilter`)}
                  {hasFilterPermission && <ArrowDropDownIcon />}
                </div>
              </th>
              {hasViewPermission && (
                <th>
                  <div className="aw-50"></div>
                </th>
              )}
            </tr>
          </thead>
          <tbody className="go-wrapper-height">
            {actualData.size === 0 && (
              <tr className="tr-change f-18 digi-gray">
                <td colSpan="8">
                  <div className="noData text-center">{'No Data Found ...'}</div>
                </td>
              </tr>
            )}

            {actualData
              .sort((a, b) => {
                if (isCurrentDate()) {
                  if (
                    getTimestamp(a.get('scheduleEndDateAndTime')) < currentDateTime &&
                    getTimestamp(b.get('scheduleEndDateAndTime')) > currentDateTime
                  )
                    return 1;
                  else if (
                    getTimestamp(a.get('scheduleEndDateAndTime')) > currentDateTime &&
                    getTimestamp(b.get('scheduleEndDateAndTime')) < currentDateTime
                  )
                    return -1;
                  else if (
                    getTimestamp(a.get('scheduleEndDateAndTime')) <
                    getTimestamp(b.get('scheduleEndDateAndTime'))
                  )
                    return -1;
                }
                return 1;
              })
              // .sort((a, b) => {
              //   return (
              //     getTimestamp(b.get('scheduleStartDateAndTime')) -
              //     getTimestamp(a.get('scheduleStartDateAndTime'))
              //   );
              // })
              .filter(
                (_, i) =>
                  i >= paginationData.pageCount * (paginationData.currentPage - 1) &&
                  i < paginationData.pageCount * paginationData.currentPage
              )
              .map((item, i) => {
                const scheduleDate = moment(item.get('scheduleStartDateAndTime')).format(
                  'Do MMM - YYYY'
                );
                const scheduleStartTime = moment(item.get('scheduleStartDateAndTime')).format(
                  'h:mm A'
                );
                const scheduleEndTime = moment(item.get('scheduleEndDateAndTime')).format('h:mm A');
                const scheduleTime = scheduleStartTime + ' - ' + scheduleEndTime;
                return (
                  <tr className="tr-change remove_hover f-15 digi-gray" key={i}>
                    {programStatus.size === 0 && courseStatus === '' && (
                      <td className="">
                        <div className="aw-150">{item.get('program_name', '')}</div>
                      </td>
                    )}
                    {termYear === 'all' && courseStatus === '' && (
                      <td className="">
                        <div className="aw-150">
                          {getCourseNameWithCode(item)}
                          {getVersionName(item)}
                        </div>
                      </td>
                    )}
                    {programStatus.size === 1 && termYear === 'all' && courseStatus === '' && (
                      <td className="">
                        <div className="aw-150 text-capitalize">
                          {item.get('term', '') === '' ? (
                            '--'
                          ) : (
                            <React.Fragment>
                              {item.get('term', '')} /{' '}
                              {'Y' + item.get('year_no', '').replace('year', '')} /{' '}
                              {'L' + item.get('level_no', '').replace('Level', '')}
                            </React.Fragment>
                          )}
                        </div>
                      </td>
                    )}

                    <td className="">
                      <div className="aw-100">
                        {item.get('merge_with', List()).size > 0 && (
                          <img src={MergeIcon} alt="merge" className="mr-2" />
                        )}
                        {item.get('title', '')}
                        {item.getIn(['session', 'delivery_symbol'], '')}
                        {item.getIn(['session', 'delivery_no'], '')}
                        {item
                          .get('merge_with', List())
                          .map((item, index) => {
                            return `${index === 0 ? ',' : ''} ${item.getIn(
                              ['schedule_id', 'session', 'delivery_symbol'],
                              ''
                            )}${item.getIn(['schedule_id', 'session', 'delivery_no'], '')}`;
                          })
                          .join(', ')}
                      </div>
                    </td>
                    <td className="">
                      <div
                        className={`${
                          programStatus.size === 0 && courseStatus === '' ? `aw-200` : `aw-300`
                        }`}
                      >
                        {item.get('staffs', '').map((data, index) => (
                          <div className="d-flex align-items-center mb-1" key={index}>
                            <Tooltips
                              title={`Contact Info - ${data.getIn(['_staff_id', 'mobile'], 'N/A')}`}
                            >
                              <AccountCircleIcon fontSize="small" className="digi-gray" />
                            </Tooltips>
                            <p className="mb-0 ml-2">
                              {jsUcfirstAll(
                                `${data.getIn(['staff_name', 'first'], '')} ${data.getIn(
                                  ['staff_name', 'middle'],
                                  ''
                                )} ${data.getIn(['staff_name', 'last'], '')}`.toLowerCase()
                              )}
                            </p>
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="">
                      <div className="aw-100 text-capitalize">{getInfraName(item)}</div>
                    </td>
                    <td className="">
                      <div
                        className={
                          programStatus.size === 0 && courseStatus === '' ? `aw-150` : `aw-300`
                        }
                      >
                        <div>{scheduleDate}</div>
                        <div>{scheduleTime}</div>
                        {/* {getTimeFormat(item.get('start', Map()))} -{' '}
                        {getTimeFormat(item.get('end', Map()))} */}
                      </div>
                    </td>
                    {(sessionStatus.includes('late') ||
                      sessionStatus.includes('early') ||
                      sessionStatus.includes('merged')) &&
                    type === '' ? (
                      <td className="">
                        <div className={`aw-150`}>
                          {item.get('sessionStatus', List()).map((status, i) => {
                            return (
                              <div key={i} className={`${status.replace(' ', '-')}`}>
                                {status}
                                {/* {item.get('sessionStatus', '') !== '' &&
                                item.get('sessionStatus', '') !== 'Merged' &&
                                item.get('merge_with', List()).size > 0 ? (
                                  <>
                                    <br />
                                    <span className="text-black">(Merged)</span>
                                  </>
                                ) : (
                                  ''
                                )} */}
                              </div>
                            );
                          })}
                        </div>
                      </td>
                    ) : (
                      ''
                    )}
                    <td className="">{getStatusHtml(item)}</td>
                    {hasViewPermission && (
                      <td
                        className=""
                        onClick={(e) =>
                          handleSingleViewOpen(
                            item.get('_id', ''),
                            getStatusName(item),
                            item.get('sessionStatus', List())
                          )
                        }
                      >
                        <div
                          className={`${
                            programStatus.size === 0 && courseStatus === '' ? `aw-50` : `aw-150`
                          } d-flex align-items-center text-skyblue remove_hover`}
                        >
                          View
                        </div>
                      </td>
                    )}
                  </tr>
                );
              })}
          </tbody>
        </table>
      </div>
      <TableFilter
        show={showFilter}
        open={openFilter}
        type={filterType}
        handleCloseFilter={handleCloseFilter}
        data={getData()}
        checkedFilter={checkedFilter}
        setCheckedFilter={setCheckedFilter}
        tableType={type}
      />
      {singleView && (
        <Suspense fallback="">
          <SingleViewModal
            show={singleView}
            handleSingleViewClose={handleSingleViewClose}
            statusData={statusData}
            imgData={imgData}
            sessionStatus={sStatus}
          />
        </Suspense>
      )}

      {paginationData.totalPages > 0 && (
        <Pagination
          pagination={paginationData.pagination}
          onNextClick={paginationData.onNextClick}
          pagevalue={paginationData.pageCount}
          onBackClick={paginationData.onBackClick}
          onFullLastClick={paginationData.onFullLastClick}
          onFullForwardClick={paginationData.onFullForwardClick}
          data={paginationData.totalPages}
          currentPage={paginationData.currentPage}
        />
      )}
    </React.Fragment>
  );
}

TableModule.propTypes = {
  tableList: PropTypes.instanceOf(Map),
  type: PropTypes.string,
  lateStarted: PropTypes.string,
  earlyEnded: PropTypes.string,
  sessionStatus: PropTypes.instanceOf(List),
  termYear: PropTypes.string,
  programStatus: PropTypes.instanceOf(List),
  courseStatus: PropTypes.string,
  checkedFilter: PropTypes.instanceOf(Map),
  CheckPermission: PropTypes.func,
  search: PropTypes.string,
  currentDate: PropTypes.string,
  setExportCurrent: PropTypes.func,
  exportCurrent: PropTypes.instanceOf(Map),
  actualFilterData: PropTypes.instanceOf(Map),
  setActualFilterData: PropTypes.func,
};

export default TableModule;
