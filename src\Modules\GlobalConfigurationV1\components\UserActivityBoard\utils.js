import { List, fromJS, Map } from 'immutable';
import isEqual from 'lodash/fp/isEqual';

export const criteriaOptions = [
  { label: 'Staff Attendance', year: 'staff_attendance' },
  { label: 'Engager', year: 'engager' },
  // { label: 'Quiz', year: 'quiz' },
  // { label: 'Support Session', year: 'support_session' },
];

export const criteriaLabels = {
  staff_attendance: 'Staff Attendance',
  engager: 'Engager',
  quiz: 'Quiz',
  support_session: 'Support Session',
  overall_manual: 'Overall Manual Attendance Rate',
  manually_conducted: 'Manually Conducted Attendance Sessions',
};

export const badgeStyles = ['flower_badge', 'star_badge', 'ninja_badge'];

export const badgeStylesObject = {
  flower_badge: 'Flower Badge',
  star_badge: 'Star Badge',
  ninja_badge: 'Ninja Badge',
};

export const checkObjectIsEqual = (object1, object2) => {
  return isEqual(fromJS(object1).toJS(), fromJS(object2).toJS());
};

export const commonParamValidation = (criteria, parameter) => {
  let message = '';
  if (
    criteria.getIn(['consideration', 'isActive'], false) &&
    (criteria.getIn(['consideration', 'noOfConsideration'], '') === '' ||
      Number(criteria.getIn(['consideration', 'noOfConsideration'], '')) > 100)
  ) {
    message = `Enter the consideration and should not be greater than 100`;
  } else if (
    criteria.getIn(['allowance', 'isActive'], false) &&
    (criteria.getIn(['allowance', 'noOfAllowance'], '') === '' ||
      Number(criteria.getIn(['allowance', 'noOfAllowance'], '')) > 100)
  ) {
    message = `Enter the allowance and should  not be greater than 100`;
  } else if (
    criteria.getIn(['score', 'isActive'], false) &&
    (criteria.getIn(['score', 'noOfScore'], '') === '' ||
      Number(criteria.getIn(['score', 'noOfScore'], '')) > 100)
  ) {
    message = `Enter the score and should not be greater than 100`;
  } else if (
    criteria.has('sessionPercentage') &&
    !criteria.get('noOfSession', false) &&
    (criteria.get('sessionPercentage', '') === '' ||
      Number(criteria.get('sessionPercentage', '') > 100))
  ) {
    message = `Enter the session Percentage and should not be greater than 100`;
  } else if (
    criteria.has('weight') &&
    (criteria.get('weight', '') === '' || Number(criteria.get('weight', '') > 100))
  ) {
    message = `Enter the Weightage and should not be greater than 100`;
  }
  if (message !== '') {
    message += ` in ${criteriaLabels[criteria.get('criteriaName', '')]} at ${parameter.get(
      'name',
      ''
    )} parameter`;
  }
  return message;
};

export const onParameterValidate = (parameters) => {
  let message = '';
  for (const parameter of parameters) {
    if (message !== '') return message;
    for (const criteria of parameter.get('criteria', List())) {
      if (message !== '') return message;

      //------------------staff_attendance-----------section
      if (criteria.get('criteriaName', '') === 'staff_attendance') {
        if (!criteria.get('noOfSession', false)) {
          if (criteria.getIn(['sessionStarted', 'noOfStudent'], '') === '') {
            return (message =
              'Enter the minimum student count in staff attendance at ' +
              parameter.get('name', '') +
              ' parameter');
          }
          if (
            criteria.getIn(['sessionStarted', 'minsOfStart'], '') === '' ||
            Number(criteria.getIn(['sessionStarted', 'minsOfStart'], '')) > 60
          ) {
            return (message =
              'Enter the minimum student participated count and should not be greater than 60mins in staff attendance at ' +
              parameter.get('name', '') +
              ' parameter');
          }
        }
        if (
          criteria.getIn(['bufferTime', 'isActive'], false) &&
          (criteria.getIn(['bufferTime', 'noOfBuffer'], '') === '' ||
            Number(criteria.getIn(['bufferTime', 'noOfBuffer'], '')) > 60)
        ) {
          return (message =
            'Enter the buffer time staff attendance and should not be greater than 60mins' +
            ' in staff attendance at ' +
            parameter.get('name', '') +
            ' parameter');
        }
        message = commonParamValidation(criteria, parameter);
      }

      if (message !== '') return message;
      //------------------engager-----------section
      else if (criteria.get('criteriaName', '') === 'engager') {
        if (!criteria.get('noOfEngager', false)) {
          if (criteria.get('studentParticipated', '') === '') {
            return (message =
              'Enter the minimum student participated count in engager at ' +
              parameter.get('name', '') +
              ' parameter');
          }
        }
        message = commonParamValidation(criteria, parameter);
      }

      if (message !== '') return message;
      //------------------quiz-----------section
      else if (criteria.get('criteriaName', '') === 'quiz') {
        message = commonParamValidation(criteria, parameter);
      }

      if (message !== '') return message;

      //------------------support_session-----------section
      if (criteria.get('criteriaName', '') === 'support_session') {
        if (
          !criteria.getIn(['supportSession', 'regular'], false) &&
          !criteria.getIn(['supportSession', 'regular'], false)
        ) {
          return (message =
            'Select At least one session type in support session at ' +
            parameter.get('name', '') +
            ' parameter');
        }
        message = commonParamValidation(criteria, parameter);
      }
      if (message !== '') return message;
    }
  }
  return message;
};

export const initialBadgeList = fromJS([
  {
    name: 'BRONZE',
    startRange: '',
    endRange: '',
  },
  {
    name: 'SILVER',
    startRange: '',
    endRange: '',
  },
  {
    name: 'GOLD',
    startRange: '',
    endRange: '',
  },
  {
    name: 'PLATINUM',
    startRange: '',
    endRange: '',
  },
  {
    name: 'DIAMOND',
    startRange: '',
    endRange: '',
  },
]);

export const onBadgeValidate = (badges, sectionName = 'Badges') => {
  for (const [index, badge] of badges.entries()) {
    const badgeName = badge.get('name', null) ?? `Row ${index + 1}`;
    if (badge.has('label') && badge.get('label', '') === '') {
      return `enter the label in ${badgeName} at ${sectionName} `;
    }
    if (badge.has('starRange') && badge.getIn(['starRange', 'noOfRange'], '') === '') {
      return `select the star range in ${badgeName} at ${sectionName} `;
    }
    if (
      (sectionName === 'anomaly level' && badge.get('endRange', '') === '') ||
      (badge.get('startRange', '') !== '' && badge.get('endRange', '') === '')
    ) {
      return `enter the end range in ${badgeName} at ${sectionName} `;
    }
    if (
      (sectionName === 'anomaly level' && badge.get('startRange', '') === '') ||
      (badge.get('startRange', '') === '' && badge.get('endRange', '') !== '')
    ) {
      return `enter the start range in ${badgeName} at ${sectionName} `;
    }
    if (
      badge.get('startRange', '') !== '' &&
      Number(badge.get('startRange', '')) >= Number(badge.get('endRange', ''))
    ) {
      return `start range should be less than end range in ${badgeName} at ${sectionName} `;
    }
    if (badge.get('endRange', '') !== '' && Number(badge.get('endRange', '')) > 100) {
      return `end range should be less than 100 in ${badgeName} at ${sectionName} `;
    }
  }
  let operatedBadges = badges.filter((item) => item.get('startRange', '') !== '');
  if (operatedBadges.size === 0) return `enter At least one Percentage range in ${sectionName}`;
  if (operatedBadges.size === 1) return '';
  operatedBadges = operatedBadges.sort((a, b) => a.get('startRange', '') - b.get('startRange', ''));
  const badgeContainer = [];
  for (const [index, badge] of operatedBadges.entries()) {
    const badgeName = badge.get('name', null) ?? `Row ${index + 1}`;

    if (!badgeContainer.length) {
      badgeContainer.push(badge.get('startRange', ''), badge.get('endRange', ''));
      continue;
    }
    if (badgeContainer[badgeContainer.length - 1] < badge.get('startRange', '')) {
      badgeContainer.push(badge.get('startRange', ''), badge.get('endRange', ''));
    } else {
      return (
        sectionName +
        ` ${badgeName} start range value should not comes under the existing range values `
      );
    }
  }
  return '';
};

export const anomalyManualAttendanceValidate = (criteriaAsList, badgeAsList) => {
  let message = '';
  for (const criteria of criteriaAsList) {
    if (criteria.get('isActive', false)) {
      message = commonParamValidation(criteria, Map({ name: 'Manual Attendance' }));
      if (message) return message;
    }
    continue;
  }
  message = onBadgeValidate(badgeAsList, 'anomaly level');
  if (message) return message;

  return '';
};
