import { createAction } from '../util';
import axios from '../../axios';
import { fromJS } from 'immutable';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const GET_USER_PROGRAMS_REQUEST = 'GET_USER_PROGRAMS_REQUEST';
export const GET_USER_PROGRAMS_SUCCESS = 'GET_USER_PROGRAMS_SUCCESS';
export const GET_USER_PROGRAMS_FAILURE = 'GET_USER_PROGRAMS_FAILURE';

const getUserProgramsRequest = createAction(GET_USER_PROGRAMS_REQUEST);
const getUserProgramsSuccess = createAction(GET_USER_PROGRAMS_SUCCESS, 'data');
const getUserProgramsFailure = createAction(GET_USER_PROGRAMS_FAILURE, 'error');

export function getUserPrograms(params) {
  return function (dispatch) {
    dispatch(getUserProgramsRequest());
    axios
      .get('studentGroupV2/userAuthorProgramList', { params })
      .then((res) => dispatch(getUserProgramsSuccess(res.data.data)))
      .catch((error) => dispatch(getUserProgramsFailure(error)));
  };
}

export const GET_PROGRAM_YEAR_LEVEL_REQUEST = 'GET_PROGRAM_YEAR_LEVEL_REQUEST';
export const GET_PROGRAM_YEAR_LEVEL_SUCCESS = 'GET_PROGRAM_YEAR_LEVEL_SUCCESS';
export const GET_PROGRAM_YEAR_LEVEL_FAILURE = 'GET_PROGRAM_YEAR_LEVEL_FAILURE';

const getProgramYearLevelRequest = createAction(GET_PROGRAM_YEAR_LEVEL_REQUEST);
const getProgramYearLevelSuccess = createAction(GET_PROGRAM_YEAR_LEVEL_SUCCESS, 'data');
const getProgramYearLevelFailure = createAction(GET_PROGRAM_YEAR_LEVEL_FAILURE, 'error');

export function getProgramYearLevel(params) {
  return function (dispatch) {
    dispatch(getProgramYearLevelRequest());
    axios
      .get('studentGroupV2/userAuthorProgramYearLevelList', { params })
      .then((res) => dispatch(getProgramYearLevelSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramYearLevelFailure(error)));
  };
}

export const GET_DELIVERY_TYPES_REQUEST = 'GET_DELIVERY_TYPES_REQUEST';
export const GET_DELIVERY_TYPES_SUCCESS = 'GET_DELIVERY_TYPES_SUCCESS';
export const GET_DELIVERY_TYPES_FAILURE = 'GET_DELIVERY_TYPES_FAILURE';

const getDeliveryTypesRequest = createAction(GET_DELIVERY_TYPES_REQUEST);
const getDeliveryTypesSuccess = createAction(GET_DELIVERY_TYPES_SUCCESS, 'data');
const getDeliveryTypesFailure = createAction(GET_DELIVERY_TYPES_FAILURE, 'error');

export function getDeliveryTypes(params) {
  return function (dispatch) {
    dispatch(getDeliveryTypesRequest());
    axios
      .get('studentGroupV2/courseDeliveryType', { params })
      .then((res) => dispatch(getDeliveryTypesSuccess(res.data.data)))
      .catch((error) => dispatch(getDeliveryTypesFailure(error)));
  };
}

export const ADD_GROUP_SETTINGS_REQUEST = 'ADD_GROUP_SETTINGS_REQUEST';
export const ADD_GROUP_SETTINGS_SUCCESS = 'ADD_GROUP_SETTINGS_SUCCESS';
export const ADD_GROUP_SETTINGS_FAILURE = 'ADD_GROUP_SETTINGS_FAILURE';

const addGroupSettingsRequest = createAction(ADD_GROUP_SETTINGS_REQUEST);
const addGroupSettingsSuccess = createAction(ADD_GROUP_SETTINGS_SUCCESS);
const addGroupSettingsFailure = createAction(ADD_GROUP_SETTINGS_FAILURE, 'error');

export function addGroupSettings({ requestBody, callback }) {
  return function (dispatch) {
    dispatch(addGroupSettingsRequest());
    axios
      .post('studentGroupV2/selectedGroupSetting', requestBody)
      .then(() => {
        dispatch(addGroupSettingsSuccess());
        callback();
      })
      .catch((error) => dispatch(addGroupSettingsFailure(error)));
  };
}

export const GET_GROUP_SETTINGS_REQUEST = 'GET_GROUP_SETTINGS_REQUEST';
export const GET_GROUP_SETTINGS_SUCCESS = 'GET_GROUP_SETTINGS_SUCCESS';
export const GET_GROUP_SETTINGS_FAILURE = 'GET_GROUP_SETTINGS_FAILURE';

const getGroupSettingsRequest = createAction(GET_GROUP_SETTINGS_REQUEST);
const getGroupSettingsSuccess = createAction(GET_GROUP_SETTINGS_SUCCESS, 'data');
const getGroupSettingsFailure = createAction(GET_GROUP_SETTINGS_FAILURE, 'error');

export function getGroupSettings({ params, callback = () => {} }) {
  return function (dispatch) {
    dispatch(getGroupSettingsRequest());
    axios
      .get('studentGroupV2/selectedProgramList', { params })
      .then((res) => {
        dispatch(getGroupSettingsSuccess({ data: res.data.data, params }));
        callback(fromJS(res.data.data));
      })
      .catch((error) => dispatch(getGroupSettingsFailure(error)));
  };
}

export const EDIT_GROUP_SETTINGS_REQUEST = 'EDIT_GROUP_SETTINGS_REQUEST';
export const EDIT_GROUP_SETTINGS_SUCCESS = 'EDIT_GROUP_SETTINGS_SUCCESS';
export const EDIT_GROUP_SETTINGS_FAILURE = 'EDIT_GROUP_SETTINGS_FAILURE';

const editGroupSettingsRequest = createAction(EDIT_GROUP_SETTINGS_REQUEST);
const editGroupSettingsSuccess = createAction(EDIT_GROUP_SETTINGS_SUCCESS, 'data');
const editGroupSettingsFailure = createAction(EDIT_GROUP_SETTINGS_FAILURE, 'error');

export function editGroupSettings({ requestBody, callback }) {
  return function (dispatch) {
    dispatch(editGroupSettingsRequest());
    axios
      .put('studentGroupV2/editGroupSetting', requestBody)
      .then(() => {
        dispatch(editGroupSettingsSuccess(requestBody));
        callback();
      })
      .catch((error) => dispatch(editGroupSettingsFailure(error)));
  };
}

export const GET_STUDENTS_LIST_REQUEST = 'GET_STUDENTS_LIST_REQUEST';
export const GET_STUDENTS_LIST_SUCCESS = 'GET_STUDENTS_LIST_SUCCESS';
export const GET_STUDENTS_LIST_FAILURE = 'GET_STUDENTS_LIST_FAILURE';

const getStudentsListRequest = createAction(GET_STUDENTS_LIST_REQUEST);
const getStudentsListSuccess = createAction(GET_STUDENTS_LIST_SUCCESS);
const getStudentsListFailure = createAction(GET_STUDENTS_LIST_FAILURE, 'error');

export function getStudentsList({ params, callback }) {
  return function (dispatch) {
    dispatch(getStudentsListRequest());
    axios
      .get('studentGroupV2/importedStudentViewList', { params })
      .then((res) => {
        dispatch(getStudentsListSuccess());
        callback(fromJS(res.data.data));
      })
      .catch((error) => dispatch(getStudentsListFailure(error)));
  };
}

export const GET_GENERATED_GROUP_NAME_REQUEST = 'GET_GENERATED_GROUP_NAME_REQUEST';
export const GET_GENERATED_GROUP_NAME_SUCCESS = 'GET_GENERATED_GROUP_NAME_SUCCESS';
export const GET_GENERATED_GROUP_NAME_FAILURE = 'GET_GENERATED_GROUP_NAME_FAILURE';

const getGeneratedGroupNameRequest = createAction(GET_GENERATED_GROUP_NAME_REQUEST);
const getGeneratedGroupNameSuccess = createAction(GET_GENERATED_GROUP_NAME_SUCCESS, 'data');
const getGeneratedGroupNameFailure = createAction(GET_GENERATED_GROUP_NAME_FAILURE, 'error');

export function getGeneratedGroupName(params) {
  return function (dispatch) {
    dispatch(getGeneratedGroupNameRequest());
    axios
      .get('studentGroupV2/autoGenerateGroupNames', { params })
      .then((res) => dispatch(getGeneratedGroupNameSuccess(res.data.data)))
      .catch((error) => dispatch(getGeneratedGroupNameFailure(error)));
  };
}

export const GROUP_STUDENTS_REQUEST = 'GROUP_STUDENTS_REQUEST';
export const GROUP_STUDENTS_SUCCESS = 'GROUP_STUDENTS_SUCCESS';
export const GROUP_STUDENTS_FAILURE = 'GROUP_STUDENTS_FAILURE';

const groupStudentsRequest = createAction(GROUP_STUDENTS_REQUEST);
const groupStudentsSuccess = createAction(GROUP_STUDENTS_SUCCESS, 'data');
const groupStudentsFailure = createAction(GROUP_STUDENTS_FAILURE, 'error');

export function groupStudents({ requestBody, selectedCount, callback }) {
  return function (dispatch) {
    dispatch(groupStudentsRequest());
    axios
      .put('studentGroupV2/groupingStudent', requestBody)
      .then(() => {
        dispatch(groupStudentsSuccess({ requestBody, selectedCount }));
        callback();
      })
      .catch((error) => dispatch(groupStudentsFailure(error)));
  };
}

export const GET_STUDENT_DETAILS_REQUEST = 'GET_STUDENT_DETAILS_REQUEST';
export const GET_STUDENT_DETAILS_SUCCESS = 'GET_STUDENT_DETAILS_SUCCESS';
export const GET_STUDENT_DETAILS_FAILURE = 'GET_STUDENT_DETAILS_FAILURE';

const getStudentDetailsRequest = createAction(GET_STUDENT_DETAILS_REQUEST);
const getStudentDetailsSuccess = createAction(GET_STUDENT_DETAILS_SUCCESS, 'data');
const getStudentDetailsFailure = createAction(GET_STUDENT_DETAILS_FAILURE, 'error');

export function getStudentDetails(params) {
  return function (dispatch) {
    dispatch(getStudentDetailsRequest());
    axios
      .get('studentGroupV2/getSingleStudentDetails', { params })
      .then((res) => dispatch(getStudentDetailsSuccess(res.data.data)))
      .catch((error) => dispatch(getStudentDetailsFailure(error)));
  };
}

export const ADD_STUDENT_REQUEST = 'ADD_STUDENT_REQUEST';
export const ADD_STUDENT_SUCCESS = 'ADD_STUDENT_SUCCESS';
export const ADD_STUDENT_FAILURE = 'ADD_STUDENT_FAILURE';

const addStudentRequest = createAction(ADD_STUDENT_REQUEST);
const addStudentSuccess = createAction(ADD_STUDENT_SUCCESS);
const addStudentFailure = createAction(ADD_STUDENT_FAILURE, 'error');

export function addStudent({ requestBody, callback }) {
  return function (dispatch) {
    dispatch(addStudentRequest());
    axios
      .put('studentGroupV2/addSingleStudent', requestBody)
      .then(() => {
        dispatch(addStudentSuccess());
        callback();
      })
      .catch((error) => dispatch(addStudentFailure(error)));
  };
}

export const DELETE_STUDENTS_REQUEST = 'DELETE_STUDENTS_REQUEST';
export const DELETE_STUDENTS_SUCCESS = 'DELETE_STUDENTS_SUCCESS';
export const DELETE_STUDENTS_FAILURE = 'DELETE_STUDENTS_FAILURE';

const deleteStudentsRequest = createAction(DELETE_STUDENTS_REQUEST);
const deleteStudentsSuccess = createAction(DELETE_STUDENTS_SUCCESS);
const deleteStudentsFailure = createAction(DELETE_STUDENTS_FAILURE, 'error');

export function deleteStudents({ requestBody, callback }) {
  return function (dispatch) {
    dispatch(deleteStudentsRequest());
    axios
      .put('studentGroupV2/deleteStudents', requestBody)
      .then(() => {
        dispatch(deleteStudentsSuccess());
        callback();
      })
      .catch((error) => dispatch(deleteStudentsFailure(error)));
  };
}

export const PUBLISH_SETTINGS_REQUEST = 'PUBLISH_SETTINGS_REQUEST';
export const PUBLISH_SETTINGS_SUCCESS = 'PUBLISH_SETTINGS_SUCCESS';
export const PUBLISH_SETTINGS_FAILURE = 'PUBLISH_SETTINGS_FAILURE';

const publishSettingsRequest = createAction(PUBLISH_SETTINGS_REQUEST);
const publishSettingsSuccess = createAction(PUBLISH_SETTINGS_SUCCESS, 'data');
const publishSettingsFailure = createAction(PUBLISH_SETTINGS_FAILURE, 'error');

export function publishSettings({ requestBody, callback, type, notifyType }) {
  return function (dispatch) {
    dispatch(publishSettingsRequest());
    axios
      .put('studentGroupV2/publishedStudentGrouping', requestBody)
      .then(() => {
        dispatch(publishSettingsSuccess({ type, notifyType }));
        callback();
      })
      .catch((error) => dispatch(publishSettingsFailure(error)));
  };
}

export const GET_TOTAL_COMPLETED_SESSION_REQUEST = 'GET_TOTAL_COMPLETED_SESSION_REQUEST';
export const GET_TOTAL_COMPLETED_SESSION_SUCCESS = 'GET_TOTAL_COMPLETED_SESSION_SUCCESS';
export const GET_TOTAL_COMPLETED_SESSION_FAILURE = 'GET_TOTAL_COMPLETED_SESSION_FAILURE';

const getTotalCompletedSessionRequest = createAction(GET_TOTAL_COMPLETED_SESSION_REQUEST);
const getTotalCompletedSessionSuccess = createAction(GET_TOTAL_COMPLETED_SESSION_SUCCESS, 'data');
const getTotalCompletedSessionFailure = createAction(GET_TOTAL_COMPLETED_SESSION_FAILURE, 'error');

export function getTotalCompletedSession({ requestBody, cacheKey }) {
  return function (dispatch) {
    dispatch(getTotalCompletedSessionRequest());
    axios
      .post('studentGroupV2/totalCompletedSession', requestBody)
      .then((res) => dispatch(getTotalCompletedSessionSuccess({ count: res.data.data, cacheKey })))
      .catch((error) => dispatch(getTotalCompletedSessionFailure(error)));
  };
}

export const GET_YEAR_WISE_STUDENTS_REQUEST = 'GET_YEAR_WISE_STUDENTS_REQUEST';
export const GET_YEAR_WISE_STUDENTS_SUCCESS = 'GET_YEAR_WISE_STUDENTS_SUCCESS';
export const GET_YEAR_WISE_STUDENTS_FAILURE = 'GET_YEAR_WISE_STUDENTS_FAILURE';

const getYearWiseStudentsRequest = createAction(GET_YEAR_WISE_STUDENTS_REQUEST);
const getYearWiseStudentsSuccess = createAction(GET_YEAR_WISE_STUDENTS_SUCCESS);
const getYearWiseStudentsFailure = createAction(GET_YEAR_WISE_STUDENTS_FAILURE, 'error');

export function getYearWiseStudents({ params, callback }) {
  return function (dispatch) {
    dispatch(getYearWiseStudentsRequest());
    axios
      .get('studentGroupV2/yearWiseImportedStudentList', { params })
      .then((res) => {
        dispatch(getYearWiseStudentsSuccess());
        callback(fromJS(res.data.data));
      })
      .catch((error) => dispatch(getYearWiseStudentsFailure(error)));
  };
}
