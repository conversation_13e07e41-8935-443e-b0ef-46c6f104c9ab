import React, { useEffect } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON>, useParams } from 'react-router-dom';
import Breadcrumb from 'Widgets/Breadcrumb/v2/Breadcrumb';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';
import {
  selectIsLoading,
  selectMessage,
  selectBreadCrumb,
} from '_reduxapi/program_input/v2/selectors';
import * as actions from '_reduxapi/program_input/v2/actions';
import * as globalConfigActions from '_reduxapi/global_configuration/actions';
import IndependentDepartmentIndex from './Components/IndependentDepartmentIndex';
import i18n from '../../i18n';

function Department({ history, message, setBreadCrumb, breadcrumbs, isLoading }) {
  const params = useParams();
  useEffect(() => {
    setBreadCrumb([{ to: '#', label: i18n.t('global_configuration.department_subject') }]);
  }, [setBreadCrumb]);

  const items = [];
  if (breadcrumbs && breadcrumbs.size > 0) {
    breadcrumbs.map((bread) => {
      items.push({ to: bread.get('to'), label: bread.get('label') });
      return bread;
    });
  }

  return (
    <div>
      {message !== '' && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>
        {items.map(({ to, label }) => (
          <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
            {label}
          </Link>
        ))}
      </Breadcrumb>
      <Switch>
        <Route
          path={`/:type/:id/:name/independent/department`}
          exact
          render={() => {
            return <IndependentDepartmentIndex {...params} />;
          }}
        />
      </Switch>
    </div>
  );
}

Department.propTypes = {
  isLoading: PropTypes.bool,
  setBreadCrumb: PropTypes.func,
  selectBreadCrumb: PropTypes.func,
  getProgramInput: PropTypes.func,
  history: PropTypes.object,
  match: PropTypes.object,
  breadcrumbs: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  message: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    breadcrumbs: selectBreadCrumb(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, { ...actions, ...globalConfigActions })
)(Department);
