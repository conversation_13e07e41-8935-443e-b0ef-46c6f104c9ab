import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  getCategoryForms,
  getCurriculumDetails,
  setData,
} from '_reduxapi/global_configuration/v1/actions';
import { List, Map, fromJS } from 'immutable';
import {
  getProgramDetails,
  getProgramListForQAPC,
} from '_reduxapi/global_configuration/v1/actions';
import {
  selectProgramDetails,
  selectProgramList,
  selectProgramCount,
  selectCategoryForm,
} from '_reduxapi/global_configuration/v1/selectors';
import { selectedConfigureTemplate } from '_reduxapi/q360/selectors';
import { getConfigureTemplate } from '_reduxapi/q360/actions';

import { useSearchParams } from './QaPcConfiguration';
import { useIsReduxEmpty } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
export const useManipulateDataForTags = (existData, handleClose, setTagData, editIndex) => {
  const [tags, setTags] = useState(
    fromJS([
      Map({
        name: existData.get('name', ''),
        isDefault: existData.get('isDefault', false),
        level: existData.get('level', ''),
        isActive: true,
        isEdited: true,
        ...(existData.get('_id', '') && {
          _id: existData.get('_id', ''),
        }),
      }),
    ])
  );

  const dispatch = useDispatch();

  const setMessage = (message) => {
    dispatch(setData({ message }));
  };

  const handleValidate = () => {
    for (const [index, tag] of tags.entries()) {
      if (tag.get('name').trim() === '') {
        return setMessage('tag name required at tag' + (index + 1));
      }
      if (tag.get('level') === '') {
        return setMessage('select the level at tag' + (index + 1));
      }
    }
    if (editIndex !== undefined) {
      setTagData((prev) => prev.set(editIndex, tags.get(0, Map())));
      return handleClose();
    }
    setTagData((prev) => prev.merge(tags));
    handleClose();
  };
  const handleDelete = (index) => {
    setTags((prev) => prev.filter((_, i) => i !== index));
  };
  return [tags, setTags, handleValidate, handleDelete];
};

export const useDispatchAndSelectorFunctionsQlc = () => {
  const dispatch = useDispatch();
  const fetchProgramList = (data) => {
    dispatch(getProgramListForQAPC(data));
  };
  const fetchProgramDetails = (data) => {
    dispatch(getProgramDetails(data));
  };

  const fetchCurriculumDetails = (data) => {
    dispatch(getCurriculumDetails(data));
  };
  const programList = useSelector(selectProgramList);
  const categoryForms = useSelector(selectCategoryForm);
  const programDetails = useSelector(selectProgramDetails);
  const selectedProgramIds = categoryForms.get('selectedProgram', List());
  const matchingForm = categoryForms.get('matchingForm', List());
  const programCount = useSelector(selectProgramCount);
  const [configureTemplate] = useIsReduxEmpty(selectedConfigureTemplate, getConfigureTemplate);
  const [searchParams] = useSearchParams();
  const currentCategoryIndex = Number(searchParams.get('currentCategoryIndex') ?? 0);
  const setMessage = (message) => {
    dispatch(setData({ message }));
  };
  return {
    dispatch,
    fetchProgramList,
    fetchProgramDetails,
    programList,
    programDetails,
    programCount,
    configureTemplate,
    currentCategoryIndex,
    matchingForm,
    setMessage,
    selectedProgramIds,
    fetchCurriculumDetails,
  };
};

export const useConfigureTemplate = () => {
  const configureTemplate = useSelector(selectedConfigureTemplate);
  const dispatch = useDispatch();
  useEffect(() => {
    if (configureTemplate.size) return;
    dispatch(getConfigureTemplate());
  }, []);
  return [configureTemplate];
};

export const useCategoryForms = (categoryId) => {
  const dispatch = useDispatch();
  const [state, setState] = useState(Map());
  const categoryForms = useSelector(selectCategoryForm);
  useEffect(() => {
    if (categoryId)
      if (!state.has(categoryId))
        dispatch(
          getCategoryForms({
            params: {
              categoryId,
            },
          })
        );
  }, [categoryId]);
  useEffect(() => {
    if (categoryId) setState((prev) => prev.set(categoryId, constructRequest(categoryForms)));
  }, [categoryForms]);
  return [state, dispatch];
};

function constructRequest(allForms) {
  let allOptions = Map();
  let formOptions = List();
  allForms.forEach((form) => {
    // forms.forEach(form=>{
    const formId = form.get('_id', '');
    formOptions = formOptions.push(
      Map({
        name: form.get('name', ''),
        _id: formId,
      })
    );
    form.get('formOccurrence', List()).forEach((allProgram, programIndex) => {
      allProgram.get('curriculum', List()).forEach((curriculum, curriculumIndex) => {
        curriculum.get('years', List()).forEach((years, yearIndex) => {
          years.get('courses', List()).forEach((course, courseIndex) => {
            allOptions = allOptions.updateIn([formId, 'courseOptions'], List(), (options) =>
              options.push(
                course
                  .set('program_name', allProgram.get('program_name', ''))
                  .set('_program_id', allProgram.get('_program_id', ''))
                  .set('programIndex', programIndex)
                  .set('curriculumIndex', curriculumIndex)
                  .set('curriculum_name', curriculum.get('curriculum_name', ''))
                  .set('yearIndex', yearIndex)
                  .set('courseIndex', courseIndex)
              )
            );
            const firstAttempt = course.getIn(['occurrences', 'attemptType', 0, 'typeName'], '');
            if (firstAttempt === '' || firstAttempt === 'none') {
              allOptions = allOptions.setIn(
                [formId, course.get('_id', ''), 'attempt'],
                firstAttempt
              );
            }
            course.getIn(['occurrences', 'attemptType'], List()).forEach((attemptType) => {
              const attemptTypeId = attemptType.get('_id', '');
              allOptions = allOptions.updateIn(
                [formId, course.get('_id', ''), 'attemptTypeOptions'],
                List(),
                (data) =>
                  data.push(
                    Map({
                      name: attemptType.get('typeName', ''),
                      value: attemptTypeId,
                    })
                  )
              );
              const groupType = attemptType.get('group', '');

              attemptType.get('groupType', List()).forEach((group) => {
                allOptions = allOptions.updateIn(
                  [formId, course.get('_id', ''), attemptTypeId, 'groupOptions'],
                  List(),
                  (data) => data.push(group)
                );
              });
              const academicYear = attemptType.get('academicYear', '');
              allOptions = allOptions.setIn(
                [formId, course.get('_id', ''), attemptTypeId, 'academicYearOptions'],
                academicYear
              );
              allOptions = allOptions.setIn(
                [formId, course.get('_id', ''), attemptTypeId, 'groupType'],
                groupType
              );
            });
          });
        });
      });
    });
    // })
  });
  return allOptions.set('formOptions', formOptions);
}
