import React from 'react';
import { Divider } from '@mui/material';
import Checkbox from '@mui/material/Checkbox';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { jsUcfirst } from 'utils';

function AttainmentCourseList({ attainmentCourse, onChangeCheckbox }) {
  return (
    <>
      {attainmentCourse.size > 0 &&
        attainmentCourse.map((item, index) => (
          <div className="mb-3" key={index}>
            <div className="pl-4 ml-3 mt-3">
              <h6>
                Year {item.get('year', '').replace('year', '')}, {item.get('levelNo', '')}
              </h6>
              <p className="f-12 mb-0">{item.get('calendar_name', '')}</p>
            </div>
            {item.get('courses', List()).map((course, courseIndex) => (
              <>
                <div className="d-flex justify-content-between">
                  <div className="pt-2 pl-4 ml-2">
                    <Checkbox
                      checked={course.get('status', false)}
                      onChange={(e) =>
                        onChangeCheckbox(e, courseIndex, index, course.get('_course_id', ''))
                      }
                    />
                  </div>
                  <div className="col-md-4 pt-3">
                    {course.get('courses_number', '')} - {course.get('courses_name', '')}
                  </div>
                  <div className="col-md-7 pt-3">{jsUcfirst(course.get('model', ''))}</div>
                </div>
                <Divider />
              </>
            ))}
          </div>
        ))}
    </>
  );
}

AttainmentCourseList.propTypes = {
  attainmentCourse: PropTypes.instanceOf(Map),
  onChangeCheckbox: PropTypes.func,
};

export default AttainmentCourseList;
