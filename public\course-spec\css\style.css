html,
body {
  margin: 0;
  padding: 0;
}

table {
  border-collapse: collapse;
}
.points-recommondation {
  display: none;
}

.fill-all,
.askheba {
  position: relative;
  bottom: 10px;
  left: 20px;
}
/* CSS */
.askheba,
.fill-all,
.export-course-report-button {
  align-items: center;
  appearance: none;
  background-color: #3eb2fd;
  background-image: linear-gradient(1deg, #4f58fd, #149bf3 99%);
  background-size: calc(100% + 20px) calc(100% + 20px);
  border-radius: 100px;
  border-width: 0;
  box-shadow: none;
  box-sizing: border-box;
  color: #ffffff;
  cursor: pointer;
  display: inline-flex;
  font-family: CircularStd, sans-serif;
  font-size: 1rem;
  height: auto;
  justify-content: center;
  line-height: 1.5;
  padding: 6px 20px;
  position: relative;
  text-align: center;
  text-decoration: none;
  transition: background-color 0.2s, background-position 0.2s;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  vertical-align: top;
  white-space: nowrap;
}

.askheba:active,
.askheba:focus {
  outline: none;
}

.askheba:hover {
  background-position: -20px -20px;
}

.askheba:focus:not(:active) {
  box-shadow: rgba(40, 170, 255, 0.25) 0 0 0 0.125em;
}
/*only for development*/
.dc-filter {
  display: none !important;
}
.dc-filter .acad-year {
  width: 215px;
}
.da-filter {
  display: none;
}
.da-filter .exam-type,
.da-filter .attemptType {
  width: 200px;
  margin-bottom: 20px;
  margin-bottom: 40px;
}

.dc-filter .term,
.dc-filter .level {
  width: 100px;
}

body {
  font-family: 'Roboto', sans-serif;
}

@media print {
  html,
  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  .dc-filter,
  .da-filter,
  .askheba,
  .fill-all,
  .export-course-report-button {
    display: none !important;
  }
}

table,
th,
td {
  border: 1px solid black;
}
/*
table {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #000000;
}

td,
th {
  border: 1px solid #000000;
  text-align: left;
}
td {
  width: 300px;
  color: #000000;
  line-height: 22px;
  text-align: center;
  border: 1px solid #000000;
}
.text-left td {
  text-align: left;
  border: 1px solid #000000;
} */

h1 {
  color: #4e3d8d;
  font-size: 22px;
  font-weight: normal;
  padding: 6px 0px 0px 12px;
  margin: 0 0 4px 0px;
}

h2 {
  color: #0fbccd;
  font-size: 20px;
  padding: 6px 0px 0px 12px;
  margin: 0 0 0px 0px;
  font-weight: normal;
}

h4 {
  color: #000000;
  margin: 0px;
}

h6 {
  color: #00b7c4;
  text-transform: uppercase;
  font-size: 18px;
  font-weight: 500;
  margin: 0px;
  padding: 0px;
}

.tbl-of-index {
  color: #0072c0;
  font-size: 25px;
  padding: 8px 0px 8px 12px;
  margin: 0 0 0px 0px;
}

p {
  margin: 0px;
  padding: 3px 0 3px 10px;
  font-weight: 500;
}

.recommendations-text {
  width: 98%;
  height: 250px;
  margin: 0 10px;
  background-color: #f2f2f2;
  border: 2px solid #000000;
  font-family: 'Roboto', sans-serif;
  font-size: 15px;
}
/******spacing css start***********/
.ptb-5 {
  padding: 5px 0;
}

.ptb-8 {
  padding: 8px 0;
}

.p-8 {
  padding: 8px;
}

.p-4 {
  padding: 4px;
}

.p-16 {
  padding: 16px;
}

.pl-15 {
  padding-left: 20px;
}

.ptb-45 {
  padding: 40px 0 45px 0;
}

.pl-12 {
  padding-left: 12px;
}

.pt-15 {
  padding-top: 15px;
}

.pb-4 {
  padding-bottom: 4px;
}

.pr-4 {
  padding-right: 4px;
}

.pl-32 {
  padding-left: 32px;
}

.pb-40 {
  padding-bottom: 40px;
}

.ptb-30 {
  padding: 30px 0 30px 0;
}

.pb-30 {
  padding-bottom: 30px;
}

.ml-5 {
  margin-left: 5px;
}

.w-100 {
  width: 100%;
}

.w-80 {
  width: 80%;
}

.w-40 {
  width: 40%;
}

.w-30 {
  width: 30%;
}

.w-42 {
  width: 42%;
}

.w-20 {
  width: 20%;
}
/******spacing css end***********/
.check-box-margin {
  margin: 2px 0 0 17px;
}

.clo-bg {
  background-color: #4e3d8d;
}

.report-label {
  color: #3292cd;
  font-size: 16px;
  font-weight: 500;
  padding: 15px;
  box-sizing: border-box;
}

.report-label-student {
  color: #3292cd;
  font-size: 13px;
  font-weight: 500;
  padding: 15px 5px 15px 15px;
}

.report-input {
  font-size: 16px;
  font-weight: 500;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid transparent !important;
  outline: none !important;
  background: #f2f2f2;
  flex: 1;
}

.report-input-content {
  font-size: 16px;
  padding: 15px;
  outline: none !important;
  background: #f2f2f2;
  flex: 1;
}

.report-input-college,
.report-input-total-students {
  font-size: 16px;
  padding: 15px;
  border-radius: 8px;
  border: none;
  background: #f2f2f2;
}

.report-input-total-students-number {
  font-size: 16px;
  padding: 12px;
  border-radius: 8px;
  border: none;
  background: #f2f2f2;
}

.report-input-Starting {
  font-size: 16px;
  font-weight: 500;
  padding: 15px;
  border-radius: 8px;
  border: none;
  background: #f2f2f2;
  margin: 4px 0 4px 52px;
}

.report-input-institution {
  font-size: 16px;
  padding: 15px;
  border-radius: 8px;
  border: none;
  background: #f2f2f2;
}

.report-input-Instructor {
  width: 360px;
  font-size: 16px;
  font-weight: 500;
  padding: 10px;
  border-radius: 8px;
  border: none;
  background: #f2f2f2;
}

.report-input-institution:focus,
.report-input-Instructor:focus,
.report-input-Instructor:focus,
.report-input-college:focus,
.report-input-total-students:focus,
.report-input-Starting:focus,
.report-input:focus,
.report-indirect:focus,
.report-indirect-bg:focus {
  outline: none;
  border: none;
}

.form-left {
  float: left;
  width: 50%;
  padding: 5px;
  box-sizing: border-box;
  border-right: 1px solid #584893;
  display: flex;
}

.form-right {
  float: right;
  width: 50%;
  padding: 5px;
  box-sizing: border-box;
  display: flex;
}

.form-full {
  width: 100%;
  clear: both;
  padding: 5px;
  box-sizing: border-box;
  display: flex;
}

.clr {
  clear: both;
}

.f-normal {
  font-weight: normal;
}

.f-bold {
  font-weight: 500;
}

.text-center {
  text-align: center !important;
}

.text-center1 {
  text-align: center;
  background-color: #4e3d8d;
  color: #ffffff;
  font-weight: normal;
}

.text-center2 {
  text-align: center;
  background-color: #4e3d8d;
  color: #ffffff;
  font-weight: normal;
}

.grade-table {
  background-color: #9298cb;
  color: #ffffff;
  font-weight: normal;
}

.course-table {
  background-color: #4e3d8d;
  color: #ffffff;
  padding: 4px;
}

.tbl-width {
  width: 100%;
  margin-top: 10px;
  font-size: 0.9em;
}
.tbl-td-green {
  background-color: #00b7c4;
  padding: 5px 20px 5px 0px;
  color: #ffffff;
}

.tbl-td1 {
  background-color: #00b7c4;
  color: #ffffff;
  font-weight: 500;
}

.tbl-td-version {
  padding: 0px 5px 0 0;
  color: #000000;
}

.tbl-td-dot {
  background-color: #9298cb;
  padding: 7px;
  color: #ffffff;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.tbl-border {
  border: 1px solid #584893;
  margin: 10px;
  background-color: #f2f2f2;
}

.tbl-border-bottom {
  border-bottom: 1px solid #584893;
  display: flex;
}

.tbl-border-bottom:last-child {
  border-bottom: 0;
}

/*****dropdown css start*******/
h3 {
  color: #4e3d8d;
  font-size: 20px;
  padding: 20px;
  margin: 0 0 4px 0px;
}

.select-year {
  width: 200px;
}

.select-filter {
  /* width: 200px; */
  padding: 10px;
  margin: 12px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #fff;
  position: relative;
  display: inline-block;
}

.select-filter option {
  padding: 8px;
  font-size: 18px;
  margin: 16px 2px;
}

.select-filter::after {
  content: '\25BC';
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  pointer-events: none;
}

.select-filter:focus {
  outline: none;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

.button {
  display: inline-block;
  padding: 10px 12px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  border: 1px solid #4e3d8d;
  color: #4e3d8d;
  border-radius: 5px;
  background-color: #fff;
  transition: background-color 0.3s, color 0.3s;
  margin-left: 6px;
}

/*****dropdown css end*******/

.button:hover {
  background-color: #4e3d8d;
  color: #fff;
}

.bg-purple-dark {
  background-color: #4e3d8d;
  color: #ffffff;
}

.bg {
  background-color: #f1f1f1;
}

.topics-tbl {
  color: #4f5c63;
}

.topics-tbl tr th {
  text-align: center;
  padding: 12px;
  width: 24%;
  font-weight: normal;
}

.course-tbl {
  color: #4f5c63;
}

.course-tbl tr th {
  text-align: center;
  padding: 15px;
  width: 28%;
  font-weight: normal;
}

.grades {
  padding-left: 25px;
}

.grades P {
  padding-left: 15px;
  font-size: 13px;
  font-style: italic;
  color: #000000;
}

.instruction {
  padding: 0px;
}

.tbl-bg {
  background-color: #d9d9d9;
}

.instruction ul {
  padding: 15px 25px;
  border: 1px solid #000000;
  margin: 10px;
  font-size: 15px;
  line-height: 1.5;
}

.content-border {
  padding: 8px;
  margin: 8px;
  font-size: 15px;
  line-height: 1.4;
  border: 2px solid #000000;
}

.logo-bg {
  text-align: right;
  background-color: #ffffff;
}

.tbl-of-contents {
  font-weight: 500;
  padding: 10px;
  font-size: 15px;
  margin-right: 10px;
}
.grade-tbl {
  width: 100%;
  font-size: 1rem;
}
.bg-gray {
  background-color: #d9d9d9;
}

.f-normal {
  font-weight: normal;
}

.gray-dot {
  background-color: #9298cb;
  padding: 5px 7px;
  color: #ffffff;
}

.heading-space {
  padding: 40px 0 45px 0;
}

.box-d-flx {
  display: flex;
}
.checkbox-1 {
  color: #6f7178;
  padding: 10px;
}

.checkbox-2 {
  color: #6f7178;
  padding-left: 30px;
}

.tbl-header {
  padding: 6px 6px 6px 0;
}

.location {
  display: flex;
  align-items: center;

  .da-outcome-table > tr:nth-child(even) {
    background-color: red !important; /* Example styling */
  }
}

.desc {
  margin-left: 4px;
  text-align: left;
}

.askheba {
  margin-bottom: 4px;
}
.display-inline {
  display: inline;
  margin-left: 20px;
}

.heba-students-grade,
.heba-recommondation-text,
.course-improvement-plan-resp {
  height: 350px;
  width: 96%;
  margin: 0 10px;
  padding: 10px;
  background-color: #f2f2f2;
  border: 1px solid #ffffff;
}

.course-improvement-plan-resp {
  width: 96%;
  margin: 0 10px;
  padding: 10px;
  background-color: #f2f2f2;
  border: 1px solid #ffffff;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.content {
  padding: 4px;
  border: 1px solid #000000;
}

.tbl-borer {
  border: 1px solid #000000;
}

.bg-tbl {
  background-color: #d9d9d9;
}

.instruction {
  padding: 10px;
}

.instruction ul {
  border: 1px solid #000000;
}

textarea {
  font-family: inherit;
  font-size: medium;
}

.export-course-report-button {
  float: right;
  margin-top: 30px;
  margin-bottom: 30px;
  margin-right: 44%;
}

.loader,
.loader-recommondation,
.loader-course,
.loader-comments,
.loader-student {
  border: 6px solid #f3f3f3;
  border-radius: 50%;
  border-top: 6px solid #533b8b;
  width: 40px;
  height: 40px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.direct-col {
  border-bottom-style: double;
  padding: 5px 0 20px 0;
}

.indirect-col {
  padding: 5px 0 20px 0;
}

.direct-total {
  border-bottom-style: double;
  padding: 5px 0 10px 0;
}

.indirect-total {
  padding: 5px 0 10px 0;
}

.dir-ver {
  vertical-align: top;
}

.bg-clo {
  background-color: #d9d9d9;
}

.bg-total {
  background-color: #8f98cb;
}

.assessment-result,
.outcome-analysis {
  background-color: #f2f2f2;
  color: #000000;
  font-family: 'Roboto', sans-serif;
  font-size: 15px;
  outline: none;
  min-height: 220px;
  text-align: left;
  padding: 10px;
}

#columnchart_material {
  width: 75%;
  margin-left: 20px;
  margin-right: auto;
  margin-bottom: 20px;
}

.report-indirect {
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  text-align: center;
}

.report-indirect-bg {
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  text-align: center;
  background-color: #8b98cb;
}

.error {
  color: #525359;
  font-weight: 500;
}

.border-black {
  border: 1px solid #000000;
}

.percentage-bg {
  background-color: #d9d9d9;
}

.da-filter-container {
  padding: 10px 0px 20px 6px;
  display: flex;
  align-items: baseline;
}

.select2-container--default .select2-results__option--selected {
  background-color: #5f5291 !important;
  color: #ffffff !important;
  border-bottom: 1px solid #ffffff;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #4e3d8d !important;
  border-radius: 8px;
  padding: 3px;
  color: #ffffff;
}

.select2-container .select2-selection--multiple {
  min-height: 38px !important;
  padding-top: 3px !important;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: 1px solid #4e3d8d !important;
}

.select2-container--default .select2-selection--multiple {
  border: 1px solid #4e3d8d !important;
}

.select2-container {
  width: 290px !important;
  color: #4e3d8d !important;
  padding: 0 8px 0 0px;
  margin: 8px 0 !important;
}

.build-date-time {
  padding: 15px 0;
}

button.comments {
  position: static;
  margin: 5px !important;
  cursor: pointer;
}

.digi-header {
  background: #ffffff;
  display: flex;
}

.digi-logo-container {
  padding: 5px 0;
  box-sizing: border-box;
}

.digi-logo {
  width: 170px;
  height: auto;
}

.digi-title-text {
  color: #000000;
  font-size: 14px;
  margin: -8px 0 0 11px;
  letter-spacing: 0.5px;
}

.digi-clo-description {
  width: 100px;
}

.digi-clo-table th {
  padding: 4px;
}

.digi-heba-cell {
  text-align: center;
  width: 300px;
  vertical-align: top;
  background: white;
}

.digi-plo-code {
  width: 80px;
}

.digi-comment-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.d-flex {
  display: flex;
}
.justify-content{
  justify-content: end;
}
.margin-l{
  margin-left: auto;
}
.gap-10{
  gap: 10px;
}
.w-50 {
  width: 50%;
}
.border-right {
  border-right: 1px solid;
}

.py-5 {
  padding-top: 5px;
  padding-bottom: 5px;
}
.px-5 {
  padding-left: 5px;
  padding-right: 5px;
}

.my-15 {
  margin-top: 15px;
  margin-bottom: 15px;
}

.margin-x-y-15{
  margin: 10px 10px;
} 

.bullet-list {
  list-style-type: disc; /* Specifies the type of bullet to use (in this case, filled circles) */
}

.outcome-row {
  background-color: #00b7c4;
  color: #ffffff;
}
.w-75 {
  width: 75%;
}

.end {
  margin-bottom: 50px;
}
.custom-scrollbar{
  height: 100vh;
  overflow-y: scroll;
}
.custom-scrollbar::-webkit-scrollbar {
  height: 7px !important;
  width: 7px !important;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cfcfcf !important;
  border-radius: 10px !important;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9e9e9e !important;
}
.pl-0 {
  padding-left: 0;
}

.content-input {
  font-size: 16px;
  padding: 15px;
  outline: none !important;
  flex: 1;
}
.content-label {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  padding: 15px;
  box-sizing: border-box;
}
.creditHoursText {
  padding: 10px;
  background-color: #f2f2f2;
  border: 1px solid #ffffff;
}
.mx-10 {
  margin: 0 10px;
}

.textArea {
  background: #f2f2f2;
}

.py-15 {
  padding-top: 15px;
  padding-bottom: 15px;
}
