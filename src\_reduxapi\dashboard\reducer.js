import { fromJS } from 'immutable';
import * as actions from './action';

const initialState = fromJS({
  message: '',
  loading: {},
  leaveRequestsLists: {},
  programsLists: [],
  configuredProgramLists: [],
  departmentSubjectLists: [],
  mySessionLists: [],
  totalStaff: 0,
  academicYearList: {},
  getMonitoringCourses: {},
  getMonitoringCourseDetail: {},
});

//eslint-disable-next-line
export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_LEAVE_REQUESTS_LIST_REQUEST: {
      return state
        .setIn(['loading', 'GET_LEAVE_REQUESTS_LIST'], true)
        .set('leaveRequestsLists', fromJS({ pending: [] }));
    }
    case actions.GET_LEAVE_REQUESTS_LIST_SUCCESS: {
      return state
        .setIn(['loading', 'GET_LEAVE_REQUESTS_LIST'], false)
        .set(
          'leaveRequestsLists',
          fromJS(
            action.data !== undefined && action.data !== 'No Leaves Found'
              ? action.data
              : { pending: [] }
          )
        );
    }
    case actions.GET_LEAVE_REQUESTS_LIST_FAILURE: {
      //const errorMessage = getErrorMessage(action);
      return state.setIn(['loading', 'GET_LEAVE_REQUESTS_LIST'], false);
      //.set('message', errorMessage);
    }
    case actions.GET_PROGRAMS_LIST_REQUEST: {
      return state.setIn(['loading', 'GET_PROGRAMS_LIST'], true).set('programsLists', fromJS([]));
    }
    case actions.GET_PROGRAMS_LIST_SUCCESS: {
      return state
        .setIn(['loading', 'GET_PROGRAMS_LIST'], false)
        .set('programsLists', fromJS(action.data !== undefined ? action.data : []));
    }
    case actions.GET_PROGRAMS_LIST_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state.setIn(['loading', 'GET_PROGRAMS_LIST'], false).set('message', errorMessage);
    }
    case actions.GET_CONFIGURE_SETTINGS_REQUEST: {
      return state
        .setIn(['loading', 'GET_CONFIGURE_SETTINGS'], true)
        .set('configuredProgramLists', fromJS([]))
        .set('departmentSubjectLists', fromJS([]));
    }
    case actions.GET_CONFIGURE_SETTINGS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_CONFIGURE_SETTINGS'], false)
        .set(
          'configuredProgramLists',
          fromJS(action.data !== undefined ? action.data.PCalenders : [])
        )
        .set(
          'departmentSubjectLists',
          fromJS(action.data !== undefined ? action.data.DeptSubs : [])
        );
    }
    case actions.GET_CONFIGURE_SETTINGS_FAILURE: {
      //const errorMessage = getErrorMessage(action);
      return state.setIn(['loading', 'GET_CONFIGURE_SETTINGS'], false); //.set('message', errorMessage);
    }
    case actions.UPDATE_CONFIGURATION_SETTINGS_REQUEST: {
      return state.setIn(['loading', 'UPDATE_CONFIGURATION_SETTINGS'], true);
    }
    case actions.UPDATE_CONFIGURATION_SETTINGS_SUCCESS: {
      return state
        .setIn(['loading', 'UPDATE_CONFIGURATION_SETTINGS'], false)
        .set('message', 'Settings Updated Successfully');
    }
    case actions.UPDATE_CONFIGURATION_SETTINGS_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state
        .setIn(['loading', 'UPDATE_CONFIGURATION_SETTINGS'], false)
        .set('message', errorMessage);
    }
    case actions.GET_MY_SESSION_LIST_REQUEST: {
      return state
        .setIn(['loading', 'GET_MY_SESSION_LIST'], true)
        .set('mySessionLists', fromJS([]));
    }
    case actions.GET_MY_SESSION_LIST_SUCCESS: {
      return state
        .setIn(['loading', 'GET_MY_SESSION_LIST'], false)
        .set('mySessionLists', fromJS(action.data !== undefined ? action.data : []));
    }
    case actions.GET_MY_SESSION_LIST_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state.setIn(['loading', 'GET_MY_SESSION_LIST'], false).set('message', errorMessage);
    }
    case actions.GET_ACADEMIC_YEAR_LEVEL_REQUEST: {
      return state
        .setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL'], true)
        .set('academicYearList', fromJS({}))
        .set('departmentSubjectLists', fromJS([]));
    }
    case actions.GET_ACADEMIC_YEAR_LEVEL_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL'], false)
        .set('academicYearList', fromJS(action.data !== undefined ? action.data.PCalenders : {}));
    }
    case actions.GET_ACADEMIC_YEAR_LEVEL_FAILURE: {
      //const errorMessage = getErrorMessage(action);
      return state.setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL'], false); //.set('message', errorMessage);
    }
    case actions.GET_DEPARTMENT_SUBJECT_REQUEST: {
      return state
        .setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL'], true)
        .set('departmentSubjectLists', fromJS([]))
        .set('totalStaff', 0);
    }
    case actions.GET_DEPARTMENT_SUBJECT_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL'], false)
        .set(
          'departmentSubjectLists',
          fromJS(action.data !== undefined ? action.data.DeptSubs.departmentSubjectCourses : [])
        )
        .set('totalStaff', action.data?.DeptSubs.staffCount);
    }
    case actions.GET_DEPARTMENT_SUBJECT_FAILURE: {
      //const errorMessage = getErrorMessage(action);
      return state.setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL'], false); //.set('message', errorMessage);
    }
    case actions.GET_MONITORING_COURSE_LIST_REQUEST: {
      return state
        .setIn(['loading', 'GET_MONITORING_COURSE_LIST'], true)
        .set('getMonitoringCourses', fromJS({}));
    }
    case actions.GET_MONITORING_COURSE_LIST_SUCCESS: {
      return state
        .setIn(['loading', 'GET_MONITORING_COURSE_LIST'], false)
        .set('getMonitoringCourses', fromJS(action.data !== undefined ? action.data : {}));
    }
    case actions.GET_MONITORING_COURSE_LIST_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state
        .setIn(['loading', 'GET_MONITORING_COURSE_LIST'], false)
        .set('message', errorMessage);
    }
    case actions.GET_MONITORING_COURSE_DETAIL_REQUEST: {
      return state;
    }
    case actions.GET_MONITORING_COURSE_DETAIL_SUCCESS: {
      return state.setIn(['getMonitoringCourseDetail', action.data.key], fromJS(action.data.value));
    }
    case actions.GET_MONITORING_COURSE_DETAIL_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state.set('message', errorMessage);
    }
    default:
      return state;
  }
}

function getErrorMessage(action) {
  const { response: { data: { message = '' } = {} } = {} } = action.error;
  return message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
}
