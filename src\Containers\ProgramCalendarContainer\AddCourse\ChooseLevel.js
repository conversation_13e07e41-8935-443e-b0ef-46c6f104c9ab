import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { BlockWrapper, FlexWrapper, Label, Input } from '../Styled';
import { connect } from 'react-redux';
import { useRouteMatch, Link } from 'react-router-dom';
import { getCourses } from '../../../_reduxapi/actions/calender';
import { NotificationManager } from 'react-notifications';

const ChooseLevel = (props) => {
  const { data, getCourses, loading, load } = props;
  const match = useRouteMatch();
  const active = match.params.year || 'year2';
  const [nonRotation, setNonRotation] = data;
  const setLoad = load[1];
  return (
    <Fragment>
      <BlockWrapper>
        {!loading ? (
          (props[active] &&
            props[active]['level_one_start_date'] &&
            props[active]['level_one_end_date']) ||
          (props[active] &&
            props[active]['level_two_show'] &&
            props[active]['level_two_start_date'] &&
            props[active]['level_two_end_date']) ? (
            <h5>Select a level for the course.</h5>
          ) : (
            <Link to={`/calender/${match.params.id}/${match.params.year}`}>
              <h5>Set level start dates in calendar settings</h5>
            </Link>
          )
        ) : null}
        {props[active] &&
          props[active]['level_one_start_date'] &&
          props[active]['level_one_end_date'] && (
            <FlexWrapper mg="10px 0">
              <Input
                type="radio"
                name="title"
                value="level_one_courses"
                id="level_one"
                disabled={nonRotation.loading ? true : nonRotation.disable_level}
                checked={nonRotation.title === 'level_one_courses' ? true : false}
                onChange={(e) => {
                  if (props[active]['level_one_is_rotation'] === 'yes') {
                    // getBatchCourses(
                    //   props[active]['level_one_is_rotation'],
                    //   props[active]['id'],
                    //   'regular',
                    //   nonRotation['edit']['level_one_no'],
                    //   NotificationManager
                    // );
                  } else {
                    setLoad(true);
                    getCourses(
                      props[active]['id'],
                      'regular',
                      nonRotation['edit']['level_one_no'],
                      NotificationManager,
                      setLoad
                    );
                  }
                  setNonRotation({
                    type: 'LEVEL_SELECT',
                    payload: e.target.value,
                    name: e.target.name,
                    year: active,
                  });
                }}
              />
              <Label mg="0px" fs="18px" htmlFor="level_one">
                {props[active] && props[active]['level_one_title']}
              </Label>
            </FlexWrapper>
          )}
        {props[active] &&
          props[active]['level_two_show'] &&
          props[active]['level_two_start_date'] &&
          props[active]['level_two_end_date'] && (
            <FlexWrapper mg="10px 0">
              <Input
                type="radio"
                name="title"
                value="level_two_courses"
                id="level_two"
                disabled={nonRotation.loading ? true : nonRotation.disable_level}
                checked={nonRotation.title === 'level_two_courses' ? true : false}
                onChange={(e) => {
                  if (props[active]['level_two_is_rotation'] === 'yes') {
                    // getBatchCourses(
                    //   props[active]['level_two_is_rotation'],
                    //   props[active]['id'],
                    //   'regular',
                    //   nonRotation['edit']['level_one_no'],
                    //   NotificationManager
                    // );
                  } else {
                    setLoad(true);
                    getCourses(
                      props[active]['id'],
                      'regular',
                      nonRotation['edit']['level_two_no'],
                      NotificationManager,
                      setLoad
                    );
                  }
                  setNonRotation({
                    type: 'LEVEL_SELECT',
                    payload: e.target.value,
                    name: e.target.name,
                    year: active,
                  });
                }}
              />
              <Label fs="18px" mg="0px" htmlFor="level_two">
                {props[active] && props[active]['level_two_title']}
              </Label>
            </FlexWrapper>
          )}
      </BlockWrapper>
    </Fragment>
  );
};

ChooseLevel.propTypes = {
  data: PropTypes.object,
  getCourses: PropTypes.func,
  loading: PropTypes.bool,
  load: PropTypes.array,
};

const mapStateToProps = ({ calender }) => ({
  // active: LocalStorageService.getCustomToken("activeYear"),
  loading: calender.isLoading,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
});

export default connect(mapStateToProps, { getCourses })(ChooseLevel);
