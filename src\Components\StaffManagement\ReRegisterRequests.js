import React, { useState, lazy, Suspense, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Table } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { List, Map } from 'immutable';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import {
  Menu,
  ListItemButton,
  List as ListView,
  ListItemText,
  ListSubheader,
  Checkbox,
} from '@mui/material';

import Loader from '../../Widgets/Loader/Loader';
import Search from '../../Widgets/Search/Search';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import { getReRegisterFaceList } from '_reduxapi/user_management/action';
import Pagination from '../StaffManagement/Pagination';
import { jsUcfirst } from 'utils';
import useDebounce from 'Hooks/useDebounceHook';
import { CheckPermission } from 'Modules/Shared/Permissions';

const ReRegisterViewModal = lazy(() => import('./ReRegisterViewModal'));

function ReRegisterRequests({ countCallBack, userType }) {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [userId, setUserId] = useState(null);

  const [tableData, setTableData] = useState(
    Map({
      search: '',
      limit: 10,
      pageNo: 1,
    })
  );

  const searchState = tableData.get('search', '');
  const debounceSearch = useDebounce(searchState, 1000);

  useEffect(() => {
    dispatch(
      getReRegisterFaceList({
        userType: userType,
        limit: tableData.get('limit'),
        pageNo: tableData.get('pageNo'),
        filter: searchState,
      })
    );
  }, [debounceSearch]); //eslint-disable-line

  const reRegisterFaceList = useSelector(({ userManagement }) =>
    userManagement.get('reRegisterFaceList', Map())
  );
  const isLoading = useSelector(({ userManagement }) => userManagement.get('isLoading', false));

  const dataLists = reRegisterFaceList.get('data', List());
  const totalPages = reRegisterFaceList.get('totalPages', 1);

  function handleSearch(e) {
    const updatedData = tableData.set('search', e.target.value).set('pageNo', 1);
    setTableData(updatedData);
  }

  const header = [
    <Trans key={Math.random()} i18nKey={'employee_id'}></Trans>,
    <Trans key={Math.random()} i18nKey={'email'}></Trans>,
    <Trans key={Math.random()} i18nKey={'first_name'}></Trans>,
    'Middle Name',
    'Last Name',
    'Gender',
    'Session Count',
    'Status',
  ];
  const listViewPermission = CheckPermission(
    'tabs',
    'User Management',
    'Staff Management',
    '',
    'Re-Register Request',
    'List View'
  );
  if (listViewPermission) {
    header.push('Action');
  }

  const openModal = (userId, status) => {
    setOpen((open) => !open);
    setUserId(!open ? userId : null);
  };

  const pagination = (e) => {
    const updatedData = tableData.set('limit', e.target.value).set('pageNo', 1);
    setTableData(updatedData);
    dispatch(
      getReRegisterFaceList({
        limit: e.target.value,
        pageNo: 1,
        filter: searchState,
        userType: userType,
      })
    );
  };

  const pageCount = (value) => {
    const updatedData = tableData.set('pageNo', value);
    setTableData(updatedData);
    dispatch(
      getReRegisterFaceList({
        limit: tableData.get('limit'),
        pageNo: value,
        filter: searchState,
        userType: userType,
      })
    );
  };

  const [showFilter, setShowFilter] = useState(null);
  const [statusFilter, setStatusFilter] = useState([]);

  const openFilter = Boolean(showFilter);
  const handleClickFilter = (event, type) => {
    setShowFilter(event.currentTarget);
  };
  const handleCloseFilter = () => {
    setShowFilter(null);
  };

  const handleCheckbox = (checked, item) => {
    const filteredValue = statusFilter;
    let newValue = [];
    if (checked) {
      newValue = [...filteredValue, item];
    } else {
      newValue = [...filteredValue.filter((value) => value !== item)];
    }
    setStatusFilter(newValue);
    const updatedData = tableData.set('pageNo', 1);
    setTableData(updatedData);
    dispatch(
      getReRegisterFaceList({
        limit: tableData.get('limit'),
        pageNo: 1,
        filter: searchState,
        statusFilter: newValue,
        userType: userType,
      })
    );
  };

  return (
    <div className="main bg-gray pt-2 pb-5">
      <Loader isLoading={isLoading} />
      <div className="container-fluid">
        {CheckPermission(
          'tabs',
          'User Management',
          'Staff Management',
          '',
          'Re-Register Request',
          'Search'
        ) && (
          <div className="float-right p-2 d-flex">
            <Search value={searchState} onChange={(e) => handleSearch(e)} />
          </div>
        )}

        <div className="clearfix"> </div>
        <React.Fragment>
          <Menu
            id={'statusFilter'}
            anchorEl={showFilter}
            keepMounted
            open={openFilter}
            onClose={handleCloseFilter}
            PaperProps={{
              style: {
                width: '20ch',
              },
            }}
          >
            <ListView
              component="div"
              className="align-items-center"
              disablePadding
              subheader={<ListSubheader className="f-16">Select Status</ListSubheader>}
            >
              <React.Fragment>
                {['pending', 'approved', 'rejected'].map((item, j) => (
                  <ListItemButton
                    dense
                    key={j}
                    onClick={(e) => handleCheckbox(!statusFilter.includes(item), item)}
                  >
                    <React.Fragment>
                      <Checkbox
                        edge="start"
                        checked={statusFilter.includes(item)}
                        tabIndex={-1}
                        disableRipple
                        onClick={(e) => handleCheckbox(e.target.checked, item)}
                      />
                      <ListItemText className="text-capitalize" primary={jsUcfirst(item)} />
                    </React.Fragment>
                  </ListItemButton>
                ))}
              </React.Fragment>
            </ListView>
          </Menu>
        </React.Fragment>
        <React.Fragment>
          <div className="p-3">
            <div className="dash-table">
              <Table responsive hover>
                <thead className="th-change">
                  <tr>
                    {header.map((header, i) => (
                      <th
                        key={i}
                        onClick={(e) => header === 'Status' && handleClickFilter(e, 'statusFilter')}
                      >
                        {header}
                        {header === 'Status' && (
                          <span className="icon" style={{ position: 'relative', top: 6 }}>
                            <ArrowDropDownIcon />
                          </span>
                        )}
                      </th>
                    ))}
                  </tr>
                </thead>
                {dataLists.size === 0 ? (
                  <TableEmptyMessage />
                ) : (
                  <tbody>
                    {dataLists.map((item, index) => (
                      <tr className="tr-change" key={index}>
                        <td>{item.getIn(['userData', 'user_id'], '')}</td>
                        <td>{item.getIn(['userData', 'email'], '')}</td>
                        <td>{jsUcfirst(item.getIn(['userData', 'name', 'first'], ''))}</td>
                        <td>{jsUcfirst(item.getIn(['userData', 'name', 'middle'], ''))}</td>
                        <td>{jsUcfirst(item.getIn(['userData', 'name', 'last'], ''))}</td>
                        <td>{jsUcfirst(item.getIn(['userData', 'gender'], ''))}</td>
                        {userType === 'staff' && <td>{item.get('scheduleCount', '')}</td>}
                        {/* {userType === 'staff' && ( */}
                        <td
                          className={
                            item.get('status', '') === 'approved'
                              ? 'text-green bold'
                              : item.get('status', '') === 'rejected'
                              ? 'text-red bold'
                              : 'bold'
                          }
                        >
                          {jsUcfirst(item.get('status', ''))}
                        </td>
                        {/* )} */}
                        {listViewPermission && (
                          <td>
                            <b
                              className="text-blue remove_hover"
                              onClick={() =>
                                openModal(item.get('_id', null), item.get('status', null))
                              }
                            >
                              {' '}
                              View{' '}
                            </b>
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                )}
              </Table>
              <Pagination
                totalPages={totalPages}
                switchPagination={pagination}
                switchPageCount={pageCount}
                pageLength={tableData.get('pageNo', 1)}
              />
            </div>
          </div>
        </React.Fragment>
      </div>
      {open && userId !== null ? (
        <Suspense fallback="">
          <ReRegisterViewModal
            userId={userId}
            callBack={() => openModal()}
            countCallBack={countCallBack}
          />
        </Suspense>
      ) : (
        ''
      )}
    </div>
  );
}

ReRegisterRequests.propTypes = {
  countCallBack: PropTypes.func,
  userType: PropTypes.string,
};

export default ReRegisterRequests;
