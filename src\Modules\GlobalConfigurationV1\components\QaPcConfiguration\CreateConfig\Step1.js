import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Checkbox, Chip, Divider, Paper, Radio } from '@mui/material';
import { getQaPcSetting } from '_reduxapi/q360/actions';
import MButton from 'Widgets/FormElements/material/Button';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import CircleIcon from '@mui/icons-material/Circle';
import { List, Map as IMap, fromJS } from 'immutable';
import { switchSX, useStyles } from '../ConfigureTemplate/TemplateSettings/Tag';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import CloseIcon from '@mui/icons-material/Close';
import { selectQaPcSetting } from '_reduxapi/q360/selectors';
import { useConfigureTemplate, useDispatchAndSelectorFunctionsQlc } from '../utils';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import { jsUcfirstAll } from 'utils';
import { courseTypeHeading } from 'Modules/QAPC/components/QualityAssuranceProcess/FormList/utils';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import { useSelector } from 'react-redux';

const tabBorderNone = {
  borderBottom: 'none !important',
  '& .MuiTab-root': {
    padding: '0px',
    margin: '0px 40px 0px 0px',
    minWidth: '0px',
  },
};

const textTransform = {
  textTransform: 'none',
  color: '#6B7280',
  fontSize: '14px',
  fontWeight: '400',
};

const tabPadding = {
  '&.MuiTabPanel-root': {
    padding: '0px',
  },
};

const getCourseListSize = (courseList) => {
  const shared_with_others = courseList.get('shared_with_others', IMap()).size;
  const shared_from = courseList.get('shared_from', IMap()).size;
  const standard = courseList.get('standard', IMap()).size;
  const selective = courseList.get('selective', IMap()).size;
  return shared_with_others + shared_from + standard + selective;
};

const numbers = [
  { name: 'Select', value: 0 },
  {
    name: 1,
    value: 1,
  },
  {
    name: 2,
    value: 2,
  },
  {
    name: 3,
    value: 3,
  },
  {
    name: 4,
    value: 4,
  },
];
const monthNames = [
  { name: 'January', value: 'January' },
  { name: 'February', value: 'February' },
  { name: 'March', value: 'March' },
  { name: 'April', value: 'April' },
  { name: 'May', value: 'May' },
  { name: 'June', value: 'June' },
  { name: 'July', value: 'July' },
  { name: 'August', value: 'August' },
  { name: 'September', value: 'September' },
  { name: 'October', value: 'October' },
  { name: 'November', value: 'November' },
  { name: 'December', value: 'December' },
];
export default function Step1({ state, setState }) {
  const classes = useStyles();
  const [checkedData, setCheckedData] = useState(IMap());
  const [expanded, setExpanded] = useState(0);
  const [modal, setModal] = useState(false);
  const onClickAccordion = (id) => () => {
    const open = id === expanded ? -1 : id;
    setExpanded(open);
  };
  const handleChangeAttemptData = (data) => (e) => {
    e.stopPropagation();
    let { key, value } = data;
    if (value === undefined) value = e.target.value;
    setState((prevState) => {
      let result = IMap();
      result = prevState.setIn(key, value);
      if (key.includes('group')) {
        key[key.length - 1] = 'groupType';
        if (value === 'all') {
          result = result.setIn(key, List([singleGroup]));
        } else {
          const studentGroups = result.getIn([...key.slice(0, 10), 'studentGroups'], List());
          result = result.setIn(
            key,
            studentGroups.map((groupName) => singleGroup.set('name', groupName))
          );
        }
      }
      return result;
    });
  };
  const [courseExpanded, setCourseExpanded] = useState(0);
  const onClickAccordionCourse = (id) => (e) => {
    e.stopPropagation();
    const open = id === courseExpanded ? -1 : id;
    setCourseExpanded(open);
  };
  const [curriculumTab, setCurriculumTab] = useState(0);
  const onClickCurriculumTab = (_, newValue) => {
    setCurriculumTab(newValue);
  };
  const [yearTab, setYearTab] = useState(0);
  const onClickYearTab = (_, newValue) => {
    setYearTab(newValue);
  };
  const [statusTab, setStatusTab] = useState('Pending');
  const onClickStatusTab = (_, newValue) => {
    setStatusTab(newValue);
  };
  const checkedDataCacheRef = useRef(new Map());
  const handleCheckedCourseTypeWise = (paramKey, value, programIndex, curriculumTab, yearTab) => (
    e
  ) => {
    const key = `${programIndex}/${curriculumTab}/${yearTab}/${paramKey}`;
    const checked = e.target.checked;
    if (checked) {
      if (checkedDataCacheRef.current.get(key)) {
        return setCheckedData((prev) => prev.set(key, checkedDataCacheRef.current.get(key)));
      }
      const constructedKeys = value.map(
        (item) =>
          `${item.get('courseId', '')}/${item.get('course_name', '')} ${item.get('course_code')}`
      );
      checkedDataCacheRef.current.set(key, constructedKeys);
      return setCheckedData((prev) => prev.set(key, constructedKeys));
    }
    setCheckedData((prev) => prev.delete(key));
  };

  const handleCheckedIndividual = (paramKey, courseKey, programIndex, curriculumTab, yearTab) => (
    e
  ) => {
    e.stopPropagation();
    const key = `${programIndex}/${curriculumTab}/${yearTab}/${paramKey}`;
    const checked = e.target.checked;
    const updatedCourse = checkedData.get(key, List()).filter((course) => course !== courseKey);
    setCheckedData((prev) => {
      if (checked) {
        return prev.set(key, prev.get(key, List()).push(courseKey));
      }
      if (updatedCourse.size) {
        return prev.set(key, updatedCourse);
      }
      return prev.delete(key);
    });
  };
  const handleToggle = (constructKey, value, courseIndex = -1) => (e) => {
    e.stopPropagation();
    const checked = e.target.checked;
    const [programIndex, curriculumTab, yearTab, key] = constructKey.split('/');
    let setInKeyArray = [
      'formOccurrence',
      programIndex,
      'curriculum',
      curriculumTab,
      'years',
      yearTab,
      'courses',
      0,
      key,
    ];
    if (courseIndex > -1) {
      return setState((prev) =>
        prev.updateIn(setInKeyArray.concat(courseIndex), IMap(), (course) =>
          course.set('isEnable', checked)
        )
      );
    }
    setState((prev) =>
      prev.setIn(
        setInKeyArray,
        value.map((course) => course.set('isEnable', checked))
      )
    );
  };
  const openOrCloseModal = () => {
    setModal((prev) => !prev);
  };

  const constructingForConfiguration = (modalCheckedData = IMap(), modalState = IMap()) => () => {
    let stateInstance = state;
    modalCheckedData.map((courses, courseKey) => {
      const [programIndex, curriculumTab, yearTab, key] = courseKey.split('/');
      stateInstance = stateInstance.updateIn(
        [
          'formOccurrence',
          programIndex,
          'curriculum',
          curriculumTab,
          'years',
          yearTab,
          'courses',
          // 1,
          // key,
        ],
        List(),
        (combinedCourse) => {
          let pendingCourses = combinedCourse.getIn([0, key], List());
          let configuredCourses = List();

          pendingCourses = pendingCourses.filter((c) => {
            const constructCourseKey = `${c.get('courseId', '')}/${c.get(
              'course_name',
              ''
            )} ${c.get('course_code')}`;
            if (courses.includes(constructCourseKey)) {
              configuredCourses = configuredCourses.push(
                c.set('isConfigure', true).set('occurrences', modalState)
              );
              return false;
            }
            return true;
          });
          return combinedCourse
            .setIn([0, key], pendingCourses)
            .updateIn([1, key], List(), (existingConfiguredCourse) => {
              return existingConfiguredCourse.concat(configuredCourses);
            });
        }
      );
    });
    setState(stateInstance);
    setCheckedData(IMap());
    openOrCloseModal();
  };
  const getProgramName = () => {
    const key = checkedData.keySeq().first(); // keySeq() returns an Iterable of keys
    const [programIndex] = key.split('/');
    return state.getIn(['formOccurrence', programIndex, 'program_name'], '');
  };
  const preventPropagation = (e) => e.stopPropagation();
  return (
    <>
      {List([]).map((program, programIndex) => {
        const yearsList = program.getIn(['curriculum', curriculumTab, 'years'], List());
        const courseList = yearsList.getIn([yearTab, 'courses'], List());
        const checkBoxKeyData = [programIndex, curriculumTab, yearTab];
        const accordionExpanded1 = program.getIn(['_program_id', '_id'], '') === expanded;
        const actualExpanded = accordionExpanded1 || (expanded === 0 && programIndex === 0);
        return (
          <>
            <Accordion
              key={programIndex}
              className="bg-white"
              sx={{ borderRadius: '8px' }}
              expanded={actualExpanded}
              onChange={onClickAccordion(program.getIn(['_program_id', '_id'], ''))}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel3-content"
                id="panel3-header"
              >
                <div className="d-flex align-items-center w-100 mt-2">
                  <div className="fw-400 f-20 text-mGrey">{program.get('program_name', '')}</div>
                  <div className="ml-auto flex-bases-350 mr-4">
                    <MaterialInput
                      elementType={'materialSearch'}
                      type={'text'}
                      size={'small'}
                      placeholder={'Search Course Name & ID...'}
                      labelclass={'searchLeft'}
                      addClass="f-12"
                    />
                  </div>
                </div>
              </AccordionSummary>
              <Divider className="mx-3" />
              {actualExpanded && (
                <AccordionDetails>
                  <TabContext value={curriculumTab}>
                    <TabList sx={tabBorderNone} onChange={onClickCurriculumTab}>
                      {program.get('curriculum', List()).map((curriculumData, curriculumIndex) => {
                        return (
                          <Tab
                            label={curriculumData.get('curriculum_name', '')}
                            key={curriculumIndex}
                            value={curriculumIndex}
                            sx={textTransform}
                          />
                        );
                      })}
                    </TabList>
                    <TabPanel sx={tabPadding} value={curriculumTab}>
                      <TabContext value={yearTab}>
                        <TabList sx={tabBorderNone} onChange={onClickYearTab}>
                          {yearsList.map((yearData, yearIndex) => {
                            return (
                              <Tab
                                label={yearData.get('year', '')}
                                key={yearIndex}
                                value={yearIndex}
                                sx={textTransform}
                              />
                            );
                          })}
                        </TabList>
                        <TabPanel sx={tabPadding} value={yearTab}>
                          <TabContext value={statusTab}>
                            <TabList sx={tabBorderNone} onChange={onClickStatusTab}>
                              <Tab
                                label={`Pending (${getCourseListSize(courseList.get(0, List()))})`}
                                value="Pending"
                                sx={textTransform}
                              />
                              <Tab
                                label={`Configured (${getCourseListSize(
                                  courseList.get(1, List())
                                )})`}
                                value="Configured"
                                sx={textTransform}
                              />
                            </TabList>
                            <TabPanel sx={tabPadding} value={statusTab}>
                              {
                                courseList
                                  .get(statusTab === 'Pending' ? 0 : 1, IMap())
                                  .isEmpty() ? (
                                  <div className="f-14 text-grey my-2">No Course Found...</div>
                                ) : (
                                  courseList
                                    .get(statusTab === 'Pending' ? 0 : 1, IMap())
                                    .entrySeq()
                                    .map(([key, value], index) => {
                                      if (value.size === 0) return null;
                                      const constructKey = `${programIndex}/${curriculumTab}/${yearTab}/${key}`;
                                      const isToggleChecked = value.every((item) =>
                                        item.get('isEnable', false)
                                      );
                                      return (
                                        <div key={key} className="my-3">
                                          <div className="d-flex align-items-center my-3">
                                            <div className="d-flex align-items-center flex-grow-1">
                                              <EnableOrDisable valid={statusTab === 'Pending'}>
                                                <Checkbox
                                                  checked={
                                                    checkedData.get(constructKey, List()).size ===
                                                    value.size
                                                  }
                                                  onChange={handleCheckedCourseTypeWise(
                                                    key,
                                                    value,
                                                    ...checkBoxKeyData
                                                  )}
                                                  size="small"
                                                  className="p-0 pr-2"
                                                />
                                              </EnableOrDisable>
                                              <div className="f-14 fw-400 text-dGrey ">
                                                {courseTypeHeading[key]}
                                              </div>
                                            </div>
                                            {index === 0 &&
                                              checkedData.size !== 0 &&
                                              statusTab === 'Pending' && (
                                                <>
                                                  <MButton
                                                    variant="contained"
                                                    color="primary"
                                                    size={'small'}
                                                    clicked={openOrCloseModal}
                                                  >
                                                    Configure
                                                  </MButton>
                                                  <FormControlLabel
                                                    // checked={tag.get('isDefault', false)}
                                                    className="m-0 ml-2"
                                                    control={
                                                      <Switch
                                                        checked={isToggleChecked}
                                                        onChange={handleToggle(constructKey, value)}
                                                        size="small"
                                                        sx={switchSX}
                                                      />
                                                    }
                                                    classes={{ label: classes.label }}
                                                    label={isToggleChecked ? 'ON' : 'OFF'}
                                                  />
                                                </>
                                              )}
                                          </div>

                                          {value.map((c, index) => {
                                            const courseKey = `${c.get('courseId', '')}/${c.get(
                                              'course_name',
                                              ''
                                            )} ${c.get('course_code')}`;
                                            const commonAttemptTypeKey = [
                                              'formOccurrence',
                                              programIndex,
                                              'curriculum',
                                              curriculumTab,
                                              'years',
                                              yearTab,
                                              'courses',
                                              1,
                                              key,
                                              index,
                                            ];
                                            const generatingUniqueKeys = constructKey + courseKey;
                                            return (
                                              <div
                                                key={generatingUniqueKeys}
                                                onClick={onClickAccordionCourse(
                                                  generatingUniqueKeys
                                                )}
                                              >
                                                <Accordion
                                                  className="bg-white"
                                                  expanded={generatingUniqueKeys === courseExpanded}
                                                  elevation={0}
                                                  variant="outlined"
                                                >
                                                  <AccordionSummary
                                                    expandIcon={<ExpandMoreIcon />}
                                                    sx={{
                                                      '& .MuiAccordionSummary-content': {
                                                        margin: 0,
                                                      },
                                                    }}
                                                  >
                                                    <div className="d-flex w-100 align-items-center">
                                                      <div
                                                        className="flex-grow-1 d-flex align-items-center"
                                                        onClick={preventPropagation}
                                                      >
                                                        <EnableOrDisable
                                                          valid={statusTab === 'Pending'}
                                                        >
                                                          <Checkbox
                                                            size="small"
                                                            className="p-0 pr-2"
                                                            checked={checkedData
                                                              .get(constructKey, List())
                                                              .includes(courseKey)}
                                                            onChange={handleCheckedIndividual(
                                                              key,
                                                              courseKey,
                                                              ...checkBoxKeyData
                                                            )}
                                                          />
                                                        </EnableOrDisable>
                                                        <div className="d-flex align-items-center">
                                                          {c.get('course_name', '') ===
                                                            'Shared From Course' && (
                                                            <ShareOutlinedIcon
                                                              color="primary"
                                                              sx={{ fontSize: 15 }}
                                                              className="mr-2"
                                                            />
                                                          )}
                                                          <div className="f-14 fw-500 text-dGrey ">
                                                            {c.get('course_name', '')}
                                                          </div>
                                                        </div>
                                                      </div>
                                                      <div
                                                        className="pr-3"
                                                        onClick={handleToggle(
                                                          constructKey,
                                                          value,
                                                          index
                                                        )}
                                                      >
                                                        <FormControlLabel
                                                          // checked={tag.get('isDefault', false)}
                                                          className="m-0"
                                                          control={
                                                            <Switch
                                                              size="small"
                                                              checked={c.get('isEnable', false)}
                                                              sx={switchSX}
                                                            />
                                                          }
                                                          classes={{ label: classes.label }}
                                                          label={
                                                            c.get('isEnable', false) ? 'ON' : 'OFF'
                                                          }
                                                        />
                                                      </div>
                                                    </div>
                                                  </AccordionSummary>
                                                  <Divider className="mx-3" />
                                                  {generatingUniqueKeys === courseExpanded && (
                                                    <AccordionDetails>
                                                      <section>
                                                        <div className="d-flex gap-8">
                                                          <div className="flex-grow-1">
                                                            <label className="f-12 fw-400 text-mGrey">
                                                              Institution Type
                                                            </label>
                                                            <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
                                                              university
                                                            </div>
                                                          </div>
                                                          <div className="flex-grow-1">
                                                            <label className="f-12 fw-400 text-mGrey">
                                                              Course Type
                                                            </label>
                                                            <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
                                                              {key === 'standard'
                                                                ? 'Standard'
                                                                : 'Selective'}
                                                            </div>
                                                          </div>
                                                          <div className="flex-grow-1">
                                                            <label className="f-12 fw-400 text-mGrey">
                                                              Students Groups *
                                                            </label>
                                                            <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
                                                              {c.getIn(
                                                                ['occurrences', 'studentGroups'],
                                                                List()
                                                              ).size
                                                                ? c
                                                                    .getIn(
                                                                      [
                                                                        'occurrences',
                                                                        'studentGroups',
                                                                      ],
                                                                      List()
                                                                    )
                                                                    .join(',')
                                                                : 'Selective'}
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </section>
                                                      <Divider className="my-3" />
                                                      <section
                                                        className="f-14 fw-400 text-dGrey"
                                                        onClick={(e) => e.stopPropagation()}
                                                      >
                                                        <div>Occurrences *</div>
                                                        {c
                                                          .getIn(
                                                            ['occurrences', 'attemptType'],
                                                            List()
                                                          )
                                                          .map((attempt, attemptKey) => (
                                                            <Fragment key={attemptKey}>
                                                              {attempt.get('typeName', '') !==
                                                              'none'
                                                                ? attempt.get('typeName', '')
                                                                : ''}
                                                              <div className="d-flex align-items-center mt-2">
                                                                <div className="mr-2">
                                                                  <Checkbox
                                                                    size="small"
                                                                    checked={attempt.get(
                                                                      'executionPer',
                                                                      false
                                                                    )}
                                                                    onChange={handleChangeAttemptData(
                                                                      {
                                                                        key: commonAttemptTypeKey.concat(
                                                                          [
                                                                            'attemptType',
                                                                            attemptKey,
                                                                            'executionPer',
                                                                          ]
                                                                        ),
                                                                        value: !attempt.get(
                                                                          'executionPer',
                                                                          false
                                                                        ),
                                                                      }
                                                                    )}
                                                                    className="m-0 p-0"
                                                                  />
                                                                </div>
                                                                <div>
                                                                  Executions Per Academic Year{' '}
                                                                </div>
                                                              </div>
                                                              <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                                                                <div className="d-flex align-items-center my-1">
                                                                  <div className="mr-2">
                                                                    <Radio
                                                                      checked={
                                                                        attempt.get(
                                                                          'academicYear',
                                                                          ''
                                                                        ) === 'all'
                                                                      }
                                                                      onChange={handleChangeAttemptData(
                                                                        {
                                                                          key: commonAttemptTypeKey.concat(
                                                                            [
                                                                              'attemptType',
                                                                              attemptKey,
                                                                              'academicYear',
                                                                            ]
                                                                          ),
                                                                          value: 'all',
                                                                        }
                                                                      )}
                                                                      value="all"
                                                                      name="radio-buttons"
                                                                      inputProps={{
                                                                        'aria-label': 'A',
                                                                      }}
                                                                      size="small"
                                                                      className="m-0 p-0"
                                                                    />
                                                                  </div>
                                                                  <div>For Every Academic Year</div>
                                                                </div>
                                                                <div className="d-flex align-items-center my-1 ml-2">
                                                                  <div className="mr-2">
                                                                    <Radio
                                                                      checked={
                                                                        attempt.get(
                                                                          'academicYear',
                                                                          ''
                                                                        ) === 'every'
                                                                      }
                                                                      onChange={handleChangeAttemptData(
                                                                        {
                                                                          key: commonAttemptTypeKey.concat(
                                                                            [
                                                                              'attemptType',
                                                                              attemptKey,
                                                                              'academicYear',
                                                                            ]
                                                                          ),
                                                                          value: 'every',
                                                                        }
                                                                      )}
                                                                      value="every"
                                                                      name="radio-buttons"
                                                                      inputProps={{
                                                                        'aria-label': 'B',
                                                                      }}
                                                                      size="small"
                                                                      className="m-0 p-0"
                                                                    />
                                                                  </div>
                                                                  <div>For All Academic Year</div>
                                                                </div>
                                                              </div>
                                                              <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                                                                <div className="d-flex align-items-center my-1">
                                                                  <div className="mr-2">
                                                                    <Radio
                                                                      checked={
                                                                        attempt.get('group', '') ===
                                                                        'all'
                                                                      }
                                                                      onChange={handleChangeAttemptData(
                                                                        {
                                                                          key: commonAttemptTypeKey.concat(
                                                                            [
                                                                              'attemptType',
                                                                              attemptKey,
                                                                              'group',
                                                                            ]
                                                                          ),
                                                                          value: 'all',
                                                                        }
                                                                      )}
                                                                      value="all"
                                                                      name="radio-buttons"
                                                                      inputProps={{
                                                                        'aria-label': 'A',
                                                                      }}
                                                                      size="small"
                                                                      className="m-0 p-0"
                                                                    />
                                                                  </div>
                                                                  <div>All Group</div>
                                                                </div>
                                                                <div className="d-flex align-items-center my-1 ml-2">
                                                                  <div className="mr-2">
                                                                    <Radio
                                                                      checked={
                                                                        attempt.get('group', '') ===
                                                                        'individual'
                                                                      }
                                                                      onChange={handleChangeAttemptData(
                                                                        {
                                                                          key: commonAttemptTypeKey.concat(
                                                                            [
                                                                              'attemptType',
                                                                              attemptKey,
                                                                              'group',
                                                                            ]
                                                                          ),
                                                                          value: 'individual',
                                                                        }
                                                                      )}
                                                                      value="individual"
                                                                      name="radio-buttons"
                                                                      inputProps={{
                                                                        'aria-label': 'B',
                                                                      }}
                                                                      size="small"
                                                                      className="m-0 p-0"
                                                                    />
                                                                  </div>
                                                                  <div>Individual Group</div>
                                                                </div>
                                                              </div>

                                                              {attempt.get('group', '') ===
                                                              'all' ? (
                                                                attempt
                                                                  .get('groupType', List())
                                                                  .map((group, i) => (
                                                                    <Fragment key={index}>
                                                                      <div className="d-flex align-items-center pl-4 pt-1">
                                                                        <CircleIcon
                                                                          sx={{ fontSize: 7 }}
                                                                          className="mx-1"
                                                                        />
                                                                        <div>Minimum</div>
                                                                        <div className="mx-2">
                                                                          <MaterialInput
                                                                            elementType={
                                                                              'materialSelect'
                                                                            }
                                                                            type={'text'}
                                                                            variant={'standard'}
                                                                            value={group.get(
                                                                              'minimum',
                                                                              0
                                                                            )}
                                                                            size={'small'}
                                                                            elementConfig={{
                                                                              options: numbers,
                                                                            }}
                                                                            sx={{
                                                                              paddingBottom: 0,
                                                                            }}
                                                                            changed={handleChangeAttemptData(
                                                                              {
                                                                                key: commonAttemptTypeKey.concat(
                                                                                  [
                                                                                    'attemptType',
                                                                                    attemptKey,
                                                                                    'groupType',
                                                                                    0,
                                                                                    'minimum',
                                                                                  ]
                                                                                ),
                                                                              }
                                                                            )}
                                                                          />
                                                                        </div>
                                                                        <div>Times</div>
                                                                      </div>
                                                                      <div className="d-flex align-items-center pl-4 pt-2">
                                                                        <CircleIcon
                                                                          sx={{ fontSize: 7 }}
                                                                          className="mx-1"
                                                                        />
                                                                        <div>Duration</div>
                                                                      </div>
                                                                      <div className="d-flex align-items-center pl-4 mlt-12 gap-10 py-2 w-30">
                                                                        <div className="flex-grow-1">
                                                                          <MaterialInput
                                                                            elementType={
                                                                              'materialSelect'
                                                                            }
                                                                            type={'text'}
                                                                            variant={'outlined'}
                                                                            size={'small'}
                                                                            placeholder="Start Month "
                                                                            elementConfig={{
                                                                              options: monthNames,
                                                                            }}
                                                                            value={group.get(
                                                                              'startMonth',
                                                                              ''
                                                                            )}
                                                                            changed={handleChangeAttemptData(
                                                                              {
                                                                                key: commonAttemptTypeKey.concat(
                                                                                  [
                                                                                    'attemptType',
                                                                                    attemptKey,
                                                                                    'groupType',
                                                                                    0,
                                                                                    'startMonth',
                                                                                  ]
                                                                                ),
                                                                              }
                                                                            )}
                                                                            sx={{
                                                                              '&.MuiInputBase-root': {
                                                                                fontSize:
                                                                                  '10px !important',
                                                                              },
                                                                            }}
                                                                          />
                                                                        </div>
                                                                        <div className="flex-grow-1">
                                                                          <MaterialInput
                                                                            elementType={
                                                                              'materialSelect'
                                                                            }
                                                                            type={'text'}
                                                                            variant={'outlined'}
                                                                            elementConfig={{
                                                                              options: monthNames,
                                                                            }}
                                                                            value={group.get(
                                                                              'endMonth',
                                                                              ''
                                                                            )}
                                                                            placeholder="End Month "
                                                                            changed={handleChangeAttemptData(
                                                                              {
                                                                                key: commonAttemptTypeKey.concat(
                                                                                  [
                                                                                    'attemptType',
                                                                                    attemptKey,
                                                                                    'groupType',
                                                                                    0,
                                                                                    'endMonth',
                                                                                  ]
                                                                                ),
                                                                              }
                                                                            )}
                                                                            size="small"
                                                                            sx={{
                                                                              '&.MuiInputBase-root': {
                                                                                fontSize:
                                                                                  '10px !important',
                                                                              },
                                                                            }}
                                                                          />
                                                                        </div>
                                                                      </div>
                                                                    </Fragment>
                                                                  ))
                                                              ) : (
                                                                <div
                                                                  className="d-flex pl-4 mlt-12 gap-10"
                                                                  style={{ flexWrap: 'wrap' }}
                                                                >
                                                                  {attempt
                                                                    .get('groupType', List())
                                                                    .map(
                                                                      (groupValue, groupIndex) => {
                                                                        return (
                                                                          <div
                                                                            className="align-self-start flex-grow-1"
                                                                            style={{
                                                                              flexBasis: '200px',
                                                                            }}
                                                                            key={groupIndex}
                                                                          >
                                                                            <Accordion
                                                                              className="bg-white"
                                                                              elevation={0}
                                                                              variant="outlined"
                                                                              disableGutters
                                                                              // expanded={expanded === groupIndex}
                                                                              // onChange={() => setExpanded(groupIndex)}
                                                                            >
                                                                              <AccordionSummary
                                                                                expandIcon={
                                                                                  <ExpandMoreIcon />
                                                                                }
                                                                                aria-controls="panel3-content"
                                                                                id="panel3-header"
                                                                              >
                                                                                {groupValue.get(
                                                                                  'name',
                                                                                  ''
                                                                                )}
                                                                              </AccordionSummary>
                                                                              <Divider className="mx-3" />
                                                                              <AccordionDetails>
                                                                                <div>
                                                                                  <div className="d-flex align-items-center gap-5">
                                                                                    <div>
                                                                                      Minimum
                                                                                    </div>
                                                                                    <div>
                                                                                      <MaterialInput
                                                                                        elementType={
                                                                                          'materialSelect'
                                                                                        }
                                                                                        type={
                                                                                          'text'
                                                                                        }
                                                                                        changed={handleChangeAttemptData(
                                                                                          {
                                                                                            key: commonAttemptTypeKey.concat(
                                                                                              [
                                                                                                'attemptType',
                                                                                                attemptKey,
                                                                                                'groupType',
                                                                                                groupIndex,
                                                                                                'minimum',
                                                                                              ]
                                                                                            ),
                                                                                          }
                                                                                        )}
                                                                                        variant={
                                                                                          'standard'
                                                                                        }
                                                                                        size={
                                                                                          'small'
                                                                                        }
                                                                                        elementConfig={{
                                                                                          options: numbers,
                                                                                        }}
                                                                                        value={groupValue.get(
                                                                                          'minimum',
                                                                                          0
                                                                                        )}
                                                                                      />
                                                                                    </div>
                                                                                    <div>Times</div>
                                                                                  </div>
                                                                                  <div className="d-flex align-items-center ">
                                                                                    <div>
                                                                                      Duration
                                                                                    </div>
                                                                                  </div>
                                                                                  <div className="d-flex align-items-center gap-10 py-2">
                                                                                    <MaterialInput
                                                                                      elementType={
                                                                                        'materialSelect'
                                                                                      }
                                                                                      type={'text'}
                                                                                      variant={
                                                                                        'outlined'
                                                                                      }
                                                                                      changed={handleChangeAttemptData(
                                                                                        {
                                                                                          key: commonAttemptTypeKey.concat(
                                                                                            [
                                                                                              'attemptType',
                                                                                              attemptKey,
                                                                                              'groupType',
                                                                                              groupIndex,
                                                                                              'startMonth',
                                                                                            ]
                                                                                          ),
                                                                                        }
                                                                                      )}
                                                                                      size={'small'}
                                                                                      placeholder="Start Month "
                                                                                      elementConfig={{
                                                                                        options: monthNames,
                                                                                      }}
                                                                                      value={groupValue.get(
                                                                                        'startMonth',
                                                                                        ''
                                                                                      )}
                                                                                      // changed={handleChangeAttemptData({
                                                                                      //   key: [
                                                                                      //     'attemptType',
                                                                                      //     attemptKey,
                                                                                      //     'groupType',
                                                                                      //     0,
                                                                                      //     'startMonth',
                                                                                      //   ],
                                                                                      // })}
                                                                                      // sx={{
                                                                                      //   '&.MuiInputBase-root': {
                                                                                      //     fontSize: '10px !important',
                                                                                      //   },
                                                                                      // }}
                                                                                    />
                                                                                    <MaterialInput
                                                                                      elementType={
                                                                                        'materialSelect'
                                                                                      }
                                                                                      type={'text'}
                                                                                      variant={
                                                                                        'outlined'
                                                                                      }
                                                                                      size={'small'}
                                                                                      changed={handleChangeAttemptData(
                                                                                        {
                                                                                          key: commonAttemptTypeKey.concat(
                                                                                            [
                                                                                              'attemptType',
                                                                                              attemptKey,
                                                                                              'groupType',
                                                                                              groupIndex,
                                                                                              'endMonth',
                                                                                            ]
                                                                                          ),
                                                                                        }
                                                                                      )}
                                                                                      placeholder="End Month "
                                                                                      elementConfig={{
                                                                                        options: monthNames,
                                                                                      }}
                                                                                      value={groupValue.get(
                                                                                        'endMonth',
                                                                                        ''
                                                                                      )}
                                                                                      //   changed={handleChangeAttemptData({
                                                                                      //     key: [
                                                                                      //       'attemptType',
                                                                                      //       attemptKey,
                                                                                      //       'groupType',
                                                                                      //       0,
                                                                                      //       'startMonth',
                                                                                      //     ],
                                                                                      //   })}
                                                                                      //   sx={{
                                                                                      //     '&.MuiInputBase-root': {
                                                                                      //       fontSize: '10px !important',
                                                                                      //     },
                                                                                      //   }}
                                                                                    />
                                                                                  </div>
                                                                                </div>
                                                                              </AccordionDetails>
                                                                            </Accordion>
                                                                          </div>
                                                                        );
                                                                      }
                                                                    )}
                                                                </div>
                                                              )}
                                                            </Fragment>
                                                          ))}
                                                      </section>
                                                    </AccordionDetails>
                                                  )}
                                                </Accordion>
                                              </div>
                                            );
                                          })}
                                        </div>
                                      );
                                    })
                                )
                                // .map((course) => {
                                //   return (
                                //     <Fragment key={course.get('courseId', '')}>
                                //       {courseList.get('standard', List()).map(course=>{
                                //         <div>

                                //         </div>
                                //       })}
                                //     </Fragment>
                                //   );
                                // })
                              }
                            </TabPanel>
                          </TabContext>
                        </TabPanel>
                      </TabContext>
                    </TabPanel>
                  </TabContext>
                </AccordionDetails>
              )}
            </Accordion>
          </>
        );
      })}
      {modal && (
        <ConfigureModal
          modal={modal}
          openOrCloseModal={openOrCloseModal}
          constructingForConfiguration={constructingForConfiguration}
          checkedData={checkedData}
          programName={getProgramName()}
        />
      )}
    </>
  );
}

const singleGroup = IMap({
  name: 'all',
  minimum: 1,
  startMonth: '',
  endMonth: '',
});

const initialAttemptType = fromJS({
  typeName: 'none',
  executionsPer: true,
  academicYear: 'all',
  group: 'all',
  groupType: [singleGroup],
});

const ConfigureModal = ({
  modal,
  openOrCloseModal,
  checkedData,
  constructingForConfiguration,
  programName,
}) => {
  const [configureTemplate] = useConfigureTemplate();
  const attemptTypeIndCheck = (typeName) => (e) => {
    const checked = e.target.checked;
    if (checked) {
      setState((prev) =>
        prev.update('attemptType', List(), (attemptType) => {
          if (attemptType.getIn([0, 'typeName'], '') === 'none') {
            return List().push(initialAttemptType.set('typeName', typeName));
          }
          return attemptType.push(initialAttemptType.set('typeName', typeName));
        })
      );
    } else {
      setState((prev) =>
        prev.update('attemptType', List(), (attemptType) => {
          let filteredData = attemptType.filter((item) => item.get('typeName', '') !== typeName);
          if (!filteredData.size) {
            filteredData = filteredData.set(0, initialAttemptType);
          }
          return filteredData;
        })
      );
    }
  };
  const { currentCategoryIndex, dispatch } = useDispatchAndSelectorFunctionsQlc();
  const category = configureTemplate.get(currentCategoryIndex, IMap());
  const attemptType = category.get('attemptType', List());
  const attemptNames = attemptType.map((item) => item.get('name', List()));
  const [state, setState] = useState(
    fromJS({
      attemptType: [initialAttemptType],
    })
  );
  const attemptTypeBulkCheck = (e) => {
    const checked = e.target.checked;
    if (checked) {
      return setState((prev) =>
        prev.update('attemptType', List(), (stateAttemptType) => {
          if (stateAttemptType.getIn([0, 'typeName'], '') === 'none') {
            for (const [index, name] of attemptNames.entries()) {
              if (index === 0) {
                stateAttemptType = stateAttemptType.setIn([index, 'typeName'], name);
              } else {
                stateAttemptType = stateAttemptType.set(
                  index,
                  initialAttemptType.set('typeName', name)
                );
              }
            }
          } else {
            let attemptName = [...attemptNames];
            stateAttemptType.map((item) => {
              if (attemptName.includes(item.get('typeName', ''))) {
                attemptName = attemptName.filter((name) => name !== item.get('typeName', ''));
              }
            });

            for (const name of attemptName) {
              stateAttemptType = stateAttemptType.push(initialAttemptType.set('typeName', name));
            }
          }
          return stateAttemptType;
        })
      );
    }
    setState((prev) =>
      prev.update('attemptType', List(), () => {
        return fromJS([initialAttemptType]);
      })
    );
  };
  const [modalCheckedData, setModalCheckedData] = useState(checkedData);
  const handleDelete = (index) => () => {
    setState((prevState) => prevState.update('studentGroups', (group) => group.delete(index)));
  };
  const updateGroupsCount = (e) => {
    const length = e.target.value;
    const result = Array.from({ length }, (_, index) => `Gr-${String(index + 1).padStart(2, '0')}`);
    setState((prevState) => prevState.set('studentGroups', fromJS(result)));
  };
  const handleChangeAttemptData = (data) => (e) => {
    let { key, value } = data;
    if (value === undefined) value = e.target.value;
    setState((prevState) => {
      let result = IMap();
      result = prevState.setIn(key, value);
      if (key.includes('group')) {
        key[key.length - 1] = 'groupType';
        if (value === 'all') {
          result = result.setIn(key, List([singleGroup]));
        } else {
          const studentGroups = result.get('studentGroups', List());
          result = result.setIn(
            key,
            studentGroups.map((groupName) => singleGroup.set('name', groupName))
          );
        }
      }
      return result;
    });
  };

  const handleChipDelete = (key, index) => () => {
    setModalCheckedData((prev) =>
      prev.update(key, List(), (courseKeys) => {
        return courseKeys.filter((_, i) => i !== index);
      })
    );
  };
  function gettingSelectedCourseCount() {
    let count = 0;
    modalCheckedData.map((course) => {
      count += course.size;
    });
    return count;
  }
  const qaPcSetting = useSelector(selectQaPcSetting);
  const handleCheckBox = ({ checked, value }) => () => {
    setState((prev) => {
      if (checked) return prev.update('tags', List(), (tag) => tag.push(value));
      return prev.update('tags', List(), (tag) => tag.filter((data) => data !== value));
    });
  };
  useEffect(() => {
    if (qaPcSetting.get('tags', List()).size) return;
    dispatch(getQaPcSetting());
  }, []);
  return (
    <DialogModal show={modal} onClose={openOrCloseModal} maxWidth={'md'} fullWidth={true}>
      <div className="px-4 py-3">
        <div className="h5 fw-400 q360-color-gray">{programName}</div>
        <div className="f-14 text-mGrey fw-400">
          Selected Courses ({gettingSelectedCourseCount()})
        </div>
        <div className="d-flex flex-wrap align-items-center gap-10 pt-2 pb-3">
          {modalCheckedData.entrySeq().map(([key, courses]) =>
            courses.map((courseKey, index) => {
              const [courseId, courseName] = courseKey.split('/');
              return (
                <Chip
                  label={courseName}
                  key={courseId}
                  // onClick={handleClick}
                  onDelete={handleChipDelete(key, index)}
                  deleteIcon={<CloseIcon />}
                  size="small"
                  className="select-color"
                />
              );
            })
          )}
        </div>
        <Paper elevation={0} variant="outlined">
          <div className="popup-q360-overflow px-3 py-3">
            <div className="f-16 fw-500 text-dGrey mb-2">Occurrences</div>
            <div className="f-12 fw-400 text-dGrey my-2">Students Groups</div>
            <div className="w-20">
              <MaterialInput
                elementType={'materialInput'}
                value={state.get('studentGroups', List()).size}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                // elementConfig={{ options: numbers }}
                placeholder={'Enter Numbers'}
                changed={updateGroupsCount}
              />
            </div>

            <div className="d-flex flex-wrap align-items-center gap-10 mt-1 my-2">
              {state.get('studentGroups', List()).map((group, groupIndex) => {
                return (
                  <Chip
                    key={groupIndex}
                    label={group}
                    // onClick={handleClick}
                    onDelete={handleDelete(groupIndex)}
                    deleteIcon={<CloseIcon />}
                    size="small"
                    className="select-color"
                  />
                );
              })}
            </div>
            {attemptType.size > 0 && (
              <>
                <div className="d-flex align-items-center">
                  <div>
                    <Checkbox
                      size="small"
                      checked={
                        state.getIn(['attemptType', 0, 'typeName'], '') !== 'none' &&
                        attemptType.size === state.get('attemptType', List()).size
                      }
                      className="m-0 p-0 pr-2"
                      onChange={attemptTypeBulkCheck}
                    />
                  </div>
                  <div className="f-14 fw-400 text-dGrey">Attempt Type</div>
                </div>

                <div className="d-flex align-items-center gap-5 pl-4">
                  {attemptType.map((attempt) => (
                    <div className="d-flex align-items-center mt-2" key={attempt.get('_id', '')}>
                      <div>
                        <Checkbox
                          onChange={attemptTypeIndCheck(attempt.get('name', ''))}
                          size="small"
                          className="m-0 p-0 pr-2"
                          checked={Boolean(
                            state
                              .get('attemptType', List())
                              .find((item) => item.get('typeName', '') === attempt.get('name', ''))
                          )}
                        />
                      </div>
                      <div className="f-14 fw-400 text-dGrey text-capitalize">
                        {attempt.get('name', '')}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}

            <Divider className="my-3" />
            {state.get('attemptType', List()).map((attempt, attemptKey) => {
              return (
                <div className="" key={attemptKey}>
                  <div className="">
                    {attempt.get('typeName', '') !== 'none' ? attempt.get('typeName', '') : ''}
                  </div>
                  <section className="f-14 fw-400 text-dGrey">
                    <div className="d-flex align-items-center mt-2">
                      <div className="mr-2">
                        <Checkbox
                          size="small"
                          className="m-0 p-0"
                          checked={attempt.get('executionsPer', false)}
                          onChange={handleChangeAttemptData({
                            key: ['attemptType', attemptKey, 'executionsPer'],
                            value: !attempt.get('executionsPer', false),
                          })}
                        />
                      </div>
                      <div>Executions Per Academic Year </div>
                    </div>
                    <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                      <div className="d-flex align-items-center my-1">
                        <div className="mr-2">
                          <Radio
                            checked={attempt.get('academicYear', '') === 'every'}
                            onChange={handleChangeAttemptData({
                              key: ['attemptType', attemptKey, 'academicYear'],
                              value: 'every',
                            })}
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'A' }}
                            size="small"
                            className="m-0 p-0"
                          />
                        </div>
                        <div>For Every Academic Year</div>
                      </div>
                      <div className="d-flex align-items-center my-1 ml-2">
                        <div className="mr-2">
                          <Radio
                            checked={attempt.get('academicYear', '') === 'all'}
                            onChange={handleChangeAttemptData({
                              key: ['attemptType', attemptKey, 'academicYear'],
                              value: 'all',
                            })}
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'B' }}
                            size="small"
                            className="m-0 p-0"
                          />
                        </div>
                        <div>For All Academic Year</div>
                      </div>
                    </div>
                    <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                      <div className="d-flex align-items-center my-1">
                        <div className="mr-2">
                          <Radio
                            checked={attempt.get('group', '') === 'all'}
                            onChange={handleChangeAttemptData({
                              key: ['attemptType', attemptKey, 'group'],
                              value: 'all',
                            })}
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'A' }}
                            size="small"
                            className="m-0 p-0"
                          />
                        </div>
                        <div>All Group</div>
                      </div>
                      <div className="d-flex align-items-center my-1 ml-2">
                        <div className="mr-2">
                          <Radio
                            checked={attempt.get('group', '') === 'individual'}
                            onChange={handleChangeAttemptData({
                              key: ['attemptType', attemptKey, 'group'],
                              value: 'individual',
                            })}
                            value="b"
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'B' }}
                            size="small"
                            className="m-0 p-0"
                          />
                        </div>
                        <div>Individual Group</div>
                      </div>
                    </div>
                    {attempt.get('group', '') === 'all' ? (
                      <>
                        <div className="d-flex align-items-center pl-4 pt-1">
                          <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                          <div>Minimum</div>
                          <div className="mx-2">
                            <MaterialInput
                              value={attempt.getIn(['groupType', 0, 'minimum'], 0)}
                              changed={handleChangeAttemptData({
                                key: ['attemptType', attemptKey, 'groupType', 0, 'minimum'],
                              })}
                              elementType={'materialSelect'}
                              type={'text'}
                              variant={'standard'}
                              size={'small'}
                              elementConfig={{ options: numbers }}
                              sx={{
                                paddingBottom: 0,
                              }}
                            />
                          </div>
                          <div>Times</div>
                        </div>
                        <div className="d-flex align-items-center pl-4 pt-2">
                          <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                          <div>Duration</div>
                        </div>
                        <div className="d-flex align-items-center pl-4 mlt-12 gap-10 py-2 w-30">
                          <div className="flex-grow-1">
                            <MaterialInput
                              elementType={'materialSelect'}
                              type={'text'}
                              variant={'outlined'}
                              size={'small'}
                              placeholder="Start Month "
                              elementConfig={{ options: monthNames }}
                              value={attempt.getIn(['groupType', 0, 'startMonth'], 0)}
                              changed={handleChangeAttemptData({
                                key: ['attemptType', attemptKey, 'groupType', 0, 'startMonth'],
                              })}
                              sx={{
                                '&.MuiInputBase-root': {
                                  fontSize: '10px !important',
                                },
                              }}
                            />
                          </div>
                          <div className="flex-grow-1">
                            <MaterialInput
                              elementType={'materialSelect'}
                              type={'text'}
                              variant={'outlined'}
                              elementConfig={{ options: monthNames }}
                              placeholder="End Month "
                              value={attempt.getIn(['groupType', 0, 'endMonth'], 0)}
                              changed={handleChangeAttemptData({
                                key: ['attemptType', attemptKey, 'groupType', 0, 'endMonth'],
                              })}
                              size="small"
                              sx={{
                                '&.MuiInputBase-root': {
                                  fontSize: '10px !important',
                                },
                              }}
                            />
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="d-flex pl-4 mlt-12 gap-10" style={{ flexWrap: 'wrap' }}>
                        {attempt.get('groupType', List()).map((groupValue, groupIndex) => {
                          return (
                            <div
                              className="align-self-start flex-grow-1"
                              style={{ flexBasis: '200px' }}
                              key={groupIndex}
                            >
                              <Accordion
                                className="bg-white"
                                elevation={0}
                                variant="outlined"
                                disableGutters
                                // expanded={expanded === groupIndex}
                                // onChange={() => setExpanded(groupIndex)}
                              >
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel3-content"
                                  id="panel3-header"
                                >
                                  {groupValue.get('name', '')}
                                </AccordionSummary>
                                <Divider className="mx-3" />
                                <AccordionDetails>
                                  <div>
                                    <div className="d-flex align-items-center gap-5">
                                      <div>Minimum</div>
                                      <div>
                                        <MaterialInput
                                          elementType={'materialSelect'}
                                          type={'text'}
                                          changed={handleChangeAttemptData({
                                            key: [
                                              'attemptType',
                                              attemptKey,
                                              'groupType',
                                              groupIndex,
                                              'minimum',
                                            ],
                                          })}
                                          variant={'standard'}
                                          size={'small'}
                                          elementConfig={{ options: numbers }}
                                          value={groupValue.get('minimum', 0)}
                                        />
                                      </div>
                                      <div>Times</div>
                                    </div>
                                    <div className="d-flex align-items-center ">
                                      <div>Duration</div>
                                    </div>
                                    <div className="d-flex align-items-center gap-10 py-2">
                                      <MaterialInput
                                        elementType={'materialSelect'}
                                        type={'text'}
                                        variant={'outlined'}
                                        changed={handleChangeAttemptData({
                                          key: [
                                            'attemptType',
                                            attemptKey,
                                            'groupType',
                                            groupIndex,
                                            'startMonth',
                                          ],
                                        })}
                                        size={'small'}
                                        placeholder="Start Month "
                                        elementConfig={{ options: monthNames }}
                                        value={groupValue.get('startMonth', '')}
                                        // changed={handleChangeAttemptData({
                                        //   key: [
                                        //     'attemptType',
                                        //     attemptKey,
                                        //     'groupType',
                                        //     0,
                                        //     'startMonth',
                                        //   ],
                                        // })}
                                        // sx={{
                                        //   '&.MuiInputBase-root': {
                                        //     fontSize: '10px !important',
                                        //   },
                                        // }}
                                      />
                                      <MaterialInput
                                        elementType={'materialSelect'}
                                        type={'text'}
                                        variant={'outlined'}
                                        size={'small'}
                                        changed={handleChangeAttemptData({
                                          key: [
                                            'attemptType',
                                            attemptKey,
                                            'groupType',
                                            groupIndex,
                                            'endMonth',
                                          ],
                                        })}
                                        placeholder="End Month "
                                        elementConfig={{ options: monthNames }}
                                        value={groupValue.get('endMonth', '')}
                                        //   changed={handleChangeAttemptData({
                                        //     key: [
                                        //       'attemptType',
                                        //       attemptKey,
                                        //       'groupType',
                                        //       0,
                                        //       'startMonth',
                                        //     ],
                                        //   })}
                                        //   sx={{
                                        //     '&.MuiInputBase-root': {
                                        //       fontSize: '10px !important',
                                        //     },
                                        //   }}
                                      />
                                    </div>
                                  </div>
                                </AccordionDetails>
                              </Accordion>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </section>
                </div>
              );
            })}
            <Divider />
            <div className="f-12 fw-400 text-dGrey mt-3 mb-2">Tags</div>
            {qaPcSetting.get('tags', List()).map((item, index) => (
              <div key={index} className="d-flex align-items-center mt-2">
                <div className="mr-2">
                  <Checkbox
                    size="small"
                    className="m-0 p-0"
                    checked={state.get('tags', List()).includes(item.get('_id', ''))}
                    onChange={handleCheckBox({
                      checked: !state.get('tags', List()).includes(item.get('_id', '')),
                      value: item.get('_id', ''),
                    })}
                  />
                </div>
                <div className="f-14 fw-400 text-dGrey">
                  {jsUcfirstAll(item.get('name', ''))}{' '}
                  <EnableOrDisable valid={item.get('isDefault', false)}>
                    <span className="color-lt-gray">(default)</span>
                  </EnableOrDisable>
                </div>
              </div>
            ))}
          </div>
        </Paper>
        <div className="d-flex align-items-center mt-3 gap-20">
          <MButton
            variant="outlined"
            className="px-4 ml-auto "
            size={'small'}
            clicked={openOrCloseModal}
            color={'gray'}
          >
            Cancel
          </MButton>
          <MButton
            variant="contained"
            className="px-4 "
            color="primary"
            size={'small'}
            clicked={constructingForConfiguration(modalCheckedData, state)}
          >
            Save
          </MButton>
        </div>
      </div>
    </DialogModal>
  );
};
