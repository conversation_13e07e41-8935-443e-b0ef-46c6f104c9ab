import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import { Accordion, Card } from 'react-bootstrap';

import * as actions from '../../../_reduxapi/leave_management/actions';
import { selectLeaveCategories } from '../../../_reduxapi/leave_management/selectors';

import CategoryCreationModal from '../modal/CategoryCreationModal';
import '../../../Assets/css/grouping.css';
import Categories from './Categories';
import { jsUcfirst, getLang } from '../../../utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
const lang = getLang();

class OnDutyClassification extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      arrow: false,

      categoryModal: {
        show: false,
      },
    };
  }

  iconPostion = () => {
    this.setState({
      arrow: !this.state.arrow,
    });
  };

  componentDidMount() {
    this.props.getLeaveCategories();
  }

  getLeaveCategoriesList() {
    const { type } = this.props;
    const { leaveCategories } = this.props;
    const categoryList = leaveCategories.get('category', List());
    const selectedCategories = categoryList?.filter(
      (cl) => cl.get('category_type') === 'on_duty' && cl.get('category_to') === type
    );
    const filteredCategoryList = selectedCategories.map((c) => c.get('leave_type'));
    const filteredCategoryByLeaveType = filteredCategoryList.toJS();
    const categoryNames = [...selectedCategories.map((c) => c.get('category_name'))];
    const categoryIds = [...selectedCategories.map((c) => c.get('_id'))];
    return { categoryNames, filteredCategoryByLeaveType, categoryIds };
  }

  handleClick = () => {
    this.setState({
      categoryModal: {
        show: true,
      },
    });
  };

  onModalClose = () => {
    this.setState({
      categoryModal: { show: false },
    });
  };

  onModalSave = (categoryName) => {
    const requestBody = {
      categoryId: '',
      operation: 'create',
      to: this.props.type,
      category_type: 'on_duty',
      category_name: categoryName,
    };

    this.props.updateLeaveCategory(requestBody);
    this.setState({
      categoryModal: { show: false },
    });
  };

  render() {
    const { categoryModal } = this.state;
    const { type } = this.props;
    const {
      categoryNames,
      filteredCategoryByLeaveType,
      categoryIds,
    } = this.getLeaveCategoriesList();

    let leaveClassificationCategory = null;

    if (filteredCategoryByLeaveType) {
      leaveClassificationCategory = filteredCategoryByLeaveType.map((category, index) => {
        return (
          <Categories
            key={index}
            leaveTypes={category}
            categoryName={categoryNames[index]}
            categoryId={categoryIds[index]}
            type={this.props.type}
            ComponentName={'on_duty'}
            permissionName={'On Duty Classification'}
          />
        );
      });
    }

    return (
      <React.Fragment>
        <Accordion defaultActiveKey="">
          <Card className="rounded">
            <Accordion.Toggle
              as={Card.Header}
              eventKey="1"
              className="icon_remove_leave"
              onClick={this.iconPostion}
            >
              <div className="d-flex justify-content-between">
                <p className="mb-0">{t('leaveManagement.onDutyClassifications')}</p>

                <p className="mb-0">
                  {this.state.arrow === true ? (
                    <i className="fa fa-chevron-down f-14" aria-hidden="true"></i>
                  ) : (
                    <i
                      className={`fa fa-chevron-${lang === 'ar' ? 'down' : 'up'} fa-rotate-90 f-14`}
                      aria-hidden="true"
                    ></i>
                  )}
                </p>
              </div>
            </Accordion.Toggle>
            {categoryModal.show === true && (
              <CategoryCreationModal
                show={categoryModal.show}
                onClose={this.onModalClose}
                onSave={this.onModalSave}
                ComponentName={'on_duty'}
              />
            )}
            <Accordion.Collapse eventKey="1" className="bg-white ">
              <Card.Body className=" innerbodyLeave border-top-remove">
                <div className="container-fluid ">
                  {CheckPermission(
                    'subTabs',
                    'Leave Management',
                    'Leave Settings',
                    '',
                    jsUcfirst(type),
                    '',
                    'On Duty Classification',
                    'Add Category'
                  ) && (
                    <p onClick={this.handleClick} className="f-12 text-skyblue remove_hover">
                      <i className="fa fa-plus pr-2" aria-hidden="true"></i>
                      {t('role_management.role_actions.Add Category')}
                    </p>
                  )}
                  {leaveClassificationCategory}
                </div>
              </Card.Body>
            </Accordion.Collapse>
          </Card>
        </Accordion>
      </React.Fragment>
    );
  }
}

const mapStateToProps = function (state) {
  return {
    leaveCategories: selectLeaveCategories(state),
  };
};
OnDutyClassification.propTypes = {
  getLeaveCategories: PropTypes.func,
  leaveCategories: PropTypes.instanceOf(Map),
  type: PropTypes.string,
  updateLeaveCategory: PropTypes.func,
};
export default compose(withRouter, connect(mapStateToProps, actions))(OnDutyClassification);
