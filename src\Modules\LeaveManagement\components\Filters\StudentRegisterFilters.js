import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { t } from 'i18next';
import { indVerRename, levelRename, stringToUC, getEnvLabelChanged, getLang } from 'utils';
const lang = getLang();
function StudentRegisterFilters({ options, filters, handleChange, handleApplyFilters }) {
  function getOptions(type) {
    return options.get(type, List());
  }
  const programId = filters.get('program', '');

  function handleFilterChange(name, value) {
    const updatedFilters = filters.merge(
      Map({
        [name]: value,
        ...(name === 'term' && {
          year: '',
          level: '',
          courseId: '',
          courseName: '',
          courseNumber: '',
          isRotation: false,
          rotationCount: null,
        }),
        ...(name === 'year' && {
          level: '',
          courseId: '',
          courseName: '',
          courseNumber: '',
          isRotation: false,
          rotationCount: null,
        }),
        ...(name === 'level' && {
          courseId: '',
          courseName: '',
          courseNumber: '',
          isRotation: false,
          rotationCount: null,
        }),
      })
    );
    handleChange(updatedFilters);
  }

  return (
    <div className="row align-items-center flex-grow-1">
      <p className="pl-3">{t('leaveManagement.programCourseList')}</p>
      <div className="col-md-12 pl-3 pr-0 row align-items-center">
        <div className={`col-md-2 text-left ${lang === 'ar' ? 'pl-3' : 'pr-1'}`}>
          <FormControl fullWidth variant="outlined" size="small">
            <div className="f-12">
              {getEnvLabelChanged()
                ? stringToUC(indVerRename('Term', programId))
                : t('leaveManagement.term')}
            </div>
            <Select
              labelId="term-label"
              native
              value={filters.get('term', '')}
              onChange={(e) => handleFilterChange('term', e.target.value)}
            >
              <option value=""></option>
              {getOptions('terms').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
        <div className={`col-md-2 text-left ${lang === 'ar' ? 'pr-4' : 'pl-2 pr-1'}`}>
          <div className="f-12">{t('master_graph.year')}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('year', '')}
              onChange={(e) => handleFilterChange('year', e.target.value)}
            >
              <option value=""></option>
              {getOptions('years').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
        <div className={`col-md-2 text-left ${lang === 'ar' ? 'pl-2' : 'pl-2 pr-1'}`}>
          <div className="f-12">
            {getEnvLabelChanged()
              ? stringToUC(indVerRename('Level', programId))
              : t('leaveManagement.level')}
          </div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('level', '')}
              onChange={(e) => handleFilterChange('level', e.target.value)}
            >
              <option value=""></option>
              {getOptions('levels').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {levelRename(option.get('name'), programId)}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
        <div className={`col-md-2 text-left ${lang === 'ar' ? 'pr-4' : 'pl-2 pr-1'}`}>
          <div className="f-12">{t('leaveManagement.gender')}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('gender', '')}
              onChange={(e) => handleFilterChange('gender', e.target.value)}
            >
              {getOptions('genders').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
      </div>
    </div>
  );
}

StudentRegisterFilters.propTypes = {
  options: PropTypes.instanceOf(Map),
  filters: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  handleApplyFilters: PropTypes.func,
};

export default StudentRegisterFilters;
