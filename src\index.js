import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON>rows<PERSON><PERSON>outer as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
// import ReactGA from 'react-ga4';
import { createBrowserHistory } from 'history';
import App from './App';
import i18n from './i18n';
import './index.css';
// import 'font-awesome/css/font-awesome.min.css';
// import HelpWidget from './Modules/DigivalHelpWidget/HelpWidget';
import store from './_reduxapi/store';
import Modal from './Containers/ProgramCalendarContainer/Modal/Modal';

// ReactGA.initialize(process.env.REACT_APP_GTAG_ID);

const history = createBrowserHistory();

// history.listen((location) => {
//   ReactGA.send({
//     hitType: 'pageview',
//     page: location.pathname,
//   });
// });
const publicPath = process.env.REACT_APP_BASE_PATH || '';
const container = document.getElementById('root');
const root = createRoot(container);
i18n.init(i18n.config, () => {
  root.render(
    <Provider store={store}>
      <Router basename={publicPath} history={history}>
        <App />
        <Modal />
      </Router>
      {/* <HelpWidget /> */}
    </Provider>
  );
});
