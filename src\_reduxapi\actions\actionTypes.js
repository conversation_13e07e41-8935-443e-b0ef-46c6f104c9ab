export const DASHBOARD = 'DASHBOARD';
export const CLICK_POP_UP = 'CLICK_POP_UP';
export const YEAR_CHANGE = 'YEAR_CHANGE';
export const TERM_CHANGE = 'TERM_CHANGE';
export const TITLE_CHANGE = 'TITLE_CHANGE';
export const TOGGLE_MODAL = 'TOGGLE_MODAL';
export const CURRICULUM_SAVE = 'CURRICULUM_SAVE';
export const CHANGE_INDEX = 'CHANGE_INDEX';
export const ACADEMIC_EVENTS_SAVE = 'ACADEMIC_EVENTS_SAVE';
export const SHOW_MODAL = 'SHOW_MODAL';
export const EDIT_COURSE = 'EDIT_COURSE';
export const INSTITUTION_CALENDAR_EVENTS = 'INSTITUTION_CALENDAR_EVENTS';
export const SAVE_FAILED = 'SAVE_FAILED';
export const SAVE_SUCCESS = 'SAVE_SUCCESS';
export const SAVE_START = 'SAVE_START';
export const RESET_MESSAGE = 'RESET_MESSAGE';
export const AUTH_START = 'AUTH_START';
export const AUTH_SUCCESS = 'AUTH_SUCCESS';
export const AUTH_FAIL = 'AUTH_FAIL';
export const AUTH_LOGOUT = 'AUTH_LOGOUT';
export const SET_ONBOARDED_STATUS = 'SET_ONBOARDED_STATUS';
export const Q360_USER_ROLES_STATUS = 'Q360_USER_ROLES_STATUS';

export const ADD_DEPARTMENT_START = 'ADD_DEPARTMENT_START';
export const ADD_DEPARTMENT_SUCCESS = 'ADD_DEPARTMENT_SUCCESS';
export const ADD_DEPARTMENT_FAILED = 'ADD_DEPARTMENT_FAILED';
export const GET_YEAR_DATA = 'GET_YEAR_DATA';
export const PROGRAM_ID = 'PROGRAM_ID';
export const LANDING_API = 'LANDING_API';
export const YEAR_CLEAR = 'YEAR_CLEAR';
export const ROTATION_SAVE = 'ROTATION_SAVE';

export const GET_YEAR_INTERIM_DATA = 'GET_YEAR_INTERIM_DATA';
export const CHANGE_INTERIM_INDEX = 'CHANGE_INTERIM_INDEX';
export const YEAR_INTERIM_CHANGE = 'YEAR_INTERIM_CHANGE';
export const SEMESTER_CHANGE = 'SEMESTER_CHANGE';
export const SHOW_INTERIM_MODAL = 'SHOW_INTERIM_MODAL';
export const TOGGLE_INTERIM_MODAL = 'TOGGLE_INTERIM_MODAL';
export const CURRICULUM_INTERIM_SAVE = 'CURRICULUM_INTERIM_SAVE';
export const INTERIM_DASHBOARD = 'INTERIM_DASHBOARD';
export const INSTITUTION_INTERIM_CALENDAR = 'INSTITUTION_INTERIM_CALENDAR';
export const RESET_INTERIM_MESSAGE = 'RESET_INTERIM_MESSAGE';
export const SET_PROGRAM_CALENDAR_ID = 'SET_PROGRAM_CALENDAR_ID';
export const GET_COURSES_ID = 'GET_COURSES_ID';
export const RESET_COURSES_ID = 'RESET_COURSES_ID';
export const GET_BATCH_COURSES_ID = 'GET_BATCH_COURSES_ID';
export const RESET_BATCH_COURSES_ID = 'RESET_BATCH_COURSES_ID';
export const GET_COURSE_EVENTS = 'GET_COURSE_EVENTS';
export const RESET_COURSE_EVENTS = 'RESET_COURSE_EVENTS';
export const COURSE_EVENTS_LIST = 'COURSE_EVENTS_LIST';
export const CLICK_POP_UP_INTERIM = 'CLICK_POP_UP_INTERIM';
export const EDIT_INTERIM_COURSE = 'EDIT_INTERIM_COURSE';

export const COURSE_EVENTS_LIST_COPY = 'COURSE_EVENTS_LIST_COPY';
export const RESET_CALENDAR = 'RESET_CALENDAR';
export const CALENDERS_DATA_GET = 'CALENDERS_DATA_GET';
export const DASHBOARD_RESET = 'DASHBOARD_RESET';
export const UPDATE_CALENDERS_DATA = 'UPDATE_CALENDERS_DATA';
export const INTERIM_STATE_SET = 'INTERIM_STATE_SET';
export const LOAD_OFF = 'LOAD_OFF';
export const LOAD_ON = 'LOAD_ON';
export const DASHBOARD_INTERIM = 'DASHBOARD_INTERIM';
export const YEAR_DATA_GET_INTERIM = 'YEAR_DATA_GET_INTERIM';
export const CALENDER_ID_SET = 'CALENDER_ID_SET';
export const LEVEL_DATE_SAVE = 'LEVEL_DATE_SAVE';
export const SET_CURRICULUM_LEVEL_GET = 'SET_CURRICULUM_LEVEL_GET';
export const SET_DEFAULT_PARAMS = 'SET_DEFAULT_PARAMS';

export const PROGRAM_LISTING_START = 'PROGRAM_LISTING_START';
export const PROGRAM_LISTING_SUCCESS = 'PROGRAM_LISTING_SUCCESS';
export const PROGRAM_LISTING_FAILED = 'PROGRAM_LISTING_FAILED';

export const SAVE_FAILED_C = 'SAVE_FAILED_C';
export const SAVE_SUCCESS_C = 'SAVE_SUCCESS_C';
export const SAVE_START_C = 'SAVE_START_C';
export const RESET_CALENDAR_REGULAR = 'RESET_CALENDAR_REGULAR';

export const SWITCH_ROLE_SUCCESS = 'SWITCH_ROLE_SUCCESS';
export const CLICK_POP_UP_REGULAR = 'CLICK_POP_UP_REGULAR';
export const SWITCH_CALENDAR_SUCCESS = 'SWITCH_CALENDAR_SUCCESS';

export const SET_BREADCRUMB_NAME = 'SET_BREADCRUMB_NAME';

export const AUTH_START_V2 = 'AUTH_START_V2';
export const AUTH_SUCCESS_V2 = 'AUTH_SUCCESS_V2';
export const AUTH_FAIL_V2 = 'AUTH_FAIL_V2';

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';

export const PROGRAM_LIST_WITH_TYPE_START = 'PROGRAM_LIST_WITH_TYPE_START';
export const PROGRAM_LIST_WITH_TYPE_SUCCESS = 'PROGRAM_LIST_WITH_TYPE_SUCCESS';
export const PROGRAM_LIST_WITH_TYPE_FAILED = 'PROGRAM_LIST_WITH_TYPE_FAILED';

export const FORGET_PASS_START_V2 = 'FORGET_PASS_START_V2';
export const FORGET_PASS_SUCCESS_V2 = 'FORGET_PASS_SUCCESS_V2';
export const FORGET_PASS_FAIL_V2 = 'FORGET_PASS_FAIL_V2';
export const SET_DATA_UPDATED_SUCCESS = 'SET_DATA_UPDATED_SUCCESS';

export const AUTH_LOGOUT_SUCCESS = 'AUTH_LOGOUT_SUCCESS';
export const AUTH_LOGOUT_FAIL = 'AUTH_LOGOUT_FAIL';
