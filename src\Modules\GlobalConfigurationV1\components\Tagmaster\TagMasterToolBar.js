import React from 'react';
import PropTypes from 'prop-types';

import { Divider, Popover } from '@mui/material';

const TagMasterToolBar = ({
  handleClickPopup,
  type,
  moreIcon,
  handleMoreIconClose,
  handleDeletePopup,
}) => {
  const open = Boolean(moreIcon);
  const id = open ? 'simple-popover' : undefined;
  return (
    <Popover
      id={id}
      open={open}
      anchorEl={moreIcon}
      onClose={handleMoreIconClose}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      <div className="p-3 popover-TagList cursor-pointer">
        <div
          onClick={() => {
            handleClickPopup(type, 'update');
          }}
        >
          Edit
        </div>
        <Divider className="mt-1 mb-1" />
        <div
          onClick={() => {
            handleDeletePopup(type, 'delete');
          }}
        >
          Delete
        </div>
      </div>
    </Popover>
  );
};

export default TagMasterToolBar;

TagMasterToolBar.propTypes = {
  handleClickPopup: PropTypes.func,
  type: PropTypes.string,
  moreIcon: PropTypes.object,
  handleMoreIconClose: PropTypes.func,
  handleDeletePopup: PropTypes.func,
};
