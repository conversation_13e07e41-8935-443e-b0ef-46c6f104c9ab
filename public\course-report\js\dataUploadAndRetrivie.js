function getLearningOutcomeTable() {
  const jsonData = [];

  $('#cloTeachingAndAssessment tbody tr').each(function () {
    const cells = $(this).find('td');
    const sno = cells.eq(0).text().trim();
    const outcomes = cells.eq(1).text().trim();
    const relatedPLOCode = cells.eq(2).text().trim();
    const assessmentMethods = cells.eq(3).text().trim();
    const targetedLevel = cells.eq(4).text().trim();
    const actualLevel = cells.eq(5).text().trim();
    const commentAssessmentResults = cells.eq(6).text().trim();
    const data = {
      sno,
      outcomes,
      relatedPLOCode,
      assessmentMethods,
      targetedLevel,
      actualLevel,
      commentAssessmentResults,
    };
    jsonData.push(data);
  });

  return jsonData;
}
function getGradeDistribution(tableId, headingRow = 0) {
  var myTable = [];

  var $th = $(`#${tableId} tr:eq(${headingRow}) th`);
  $(`#${tableId} tbody tr`).each(function (i, tr) {
    var obj = {},
      $tds = $(tr).find('td');
    $th.each(function (index, th) {
      obj[$(th).text()] = $tds.eq(index).text();
    });
    myTable.push(obj);
  });
  return myTable;
}
function getLocation() {
  let location = '';
  $(".location input[type='checkbox']").each(function () {
    if ($(this).is(':checked')) {
      location = $(this).val();
      return false; // Exit loop if a checkbox is checked
    }
  });
  return location;
}
function dataRetrieve() {
  const json = {
    courseTitle: $('#courseTitle').html().trim(),
    courseCode: $('#courseCode').html().trim(),
    program: $('#program').html().trim(),
    department: $('#department').html().trim(),
    college: $('#college').html().trim(),
    institution: $('#institution').html().trim(),
    location: getLocation(),
    academicYear: $('#academicYear').html().trim(),
    semester: $('#semester').html().trim(),
    courseInstructor: $('#courseInstructor').html().trim(),
    courseCoordinator: $('#courseCoordinator').html().trim(),
    section: $('#section').html().trim(),
    noOfStudentsStarted: $('#noOfStudentsStarted').html().trim(),
    reportDate: $('#reportDate').val(),
    noOfStudentsRegistered: $('#noOfStudentsRegistered').html().trim(),
    noOfStudentsCompleted: $('#noOfStudentsCompleted').html().trim(),
    noOfStudentsCompletedSelectedExam: $('#noOfStudentsCompletedSelectedExam').html().trim(),
    commentOnStudentGrades: $('#commentOnStudentGrades').val().trim(),
    recommendations: $('#recommendations').val().trim(),
    gradeDistribution: getGradeDistribution('gradeDistribution', 1),
    cloTeachingAndAssessment: getLearningOutcomeTable('cloTeachingAndAssessment'),
    topicsNotCovered: getGradeDistribution('topicsNotCovered'),
    courseImprovementPlan: getGradeDistribution('courseImprovementPlan'),
  };
  return json;
  // window.parent.postMessage({ type: 'DATA_RETRIEVAL', data: json }, '*');
}
$('#deserializeBtn').on('click', dataRetrieve);

//----------------------------------------load data-------------------------------------------------
function loadTableData(id, data, setRow) {
  const $tbody = $(`#${id} tbody`);
  $tbody.empty();
  data.forEach((item) => {
    var $row = $('<tr>'); // Create a new table row
    Object.values(item).forEach((value, index) => {
      $row.append(setRow(value, index));
    });
    $tbody.append($row);
  });
}
function populateLearningOutcomeTable(data) {
  var tbody = $('#cloTeachingAndAssessment tbody');
  tbody.empty();
  $.each(data, function (index, item) {
    var row = $('<tr>');
    if (index % 4 === 0) row.addClass('outcome-row');
    row.append($('<td >').text(item.sno));
    var col1 = $('<td >').text(item.outcomes);
    if (index % 4 === 0) {
      col1.attr('colspan', 6);
    }
    row.append(col1);
    if (index % 4 !== 0) {
      row.append($('<td >').text(item.relatedPLOCode));
      row.append($('<td >').text(item.assessmentMethods));
      row.append($('<td >').text(item.targetedLevel));
      row.append($('<td >').text(item.actualLevel));
      row.append($('<td >').text(item.commentAssessmentResults));
    }
    // Append the row to the table body
    tbody.append(row);
  });
}
function setGradeDistribution(value, index) {
  if (index === 0) return $('<td>').text(value).addClass('bg-purple-dark');
  else return $('<td>').text(value).attr('contenteditable', true);
}
function setTopicNotCovered(value, index) {
  return $('<td>').text(value).attr('contenteditable', true).addClass('topic-height');
}
const loadText = (data) => {
  $('#academicYear').html(data.academicYear);
  $('#commentOnStudentGrades').html(data.commentOnStudentGrades);
  $('#courseCode').html(data.courseCode);
  $('#courseCoordinator').html(data.courseCoordinator);
  $('#courseInstructor').html(data.courseInstructor);
  $('#courseTitle').html(data.courseTitle);
  $('#department').html(data.department);
  $('#institution').html(data.institution);
  $('#noOfStudentsCompleted').html(data.noOfStudentsCompleted);
  $('#noOfStudentsCompletedSelectedExam').html(data.noOfStudentsCompletedSelectedExam);
  $('#noOfStudentsRegistered').html(data.noOfStudentsRegistered);
  $('#noOfStudentsStarted').html(data.noOfStudentsStarted);
  $('#program').html(data.program);
  $('#recommendations').html(data.recommendations);
  $('#section').html(data.section);
  $('#semester').html(data.semester);
  $('#reportDate').html(data.reportDate);
  $('#college').html(data.college);

  $(`input[value~=${data.location}]`).prop('checked', true);
};
const loadDataBack = (sampleData) => {
  loadText(sampleData);
  if (sampleData.gradeDistribution)
    loadTableData('gradeDistribution', sampleData.gradeDistribution, setGradeDistribution);
  if (sampleData.topicsNotCovered)
    loadTableData('topicsNotCovered', sampleData.topicsNotCovered, setTopicNotCovered);
  if (sampleData.courseImprovementPlan)
    loadTableData('courseImprovementPlan', sampleData.courseImprovementPlan, setTopicNotCovered);
  if (sampleData.cloTeachingAndAssessment)
    populateLearningOutcomeTable(sampleData.cloTeachingAndAssessment);
};
window.addEventListener('message', function (event) {
  // Check origin to ensure message is from a trusted source
  const { values, from } = event.data;
  if (from === 'fromDC') loadDataBack(values);
});
// const sampleData = {
//   location: 'branch',
//   courseTitle: 'dsa',
//   courseCode: 'eqw',
//   program: 'ter',
//   department: 'rew',
//   college: 'eg',
//   institution: 'gd',
//   academicYear: 'tre',
//   semester: 'ewq',
//   courseInstructor: 'frfet',
//   courseCoordinator: 'ter',
//   section: 'gf',
//   noOfStudentsStarted: 'ter',
//   reportDate: '2024-04-04',
//   noOfStudentsRegistered: 'rew',
//   noOfStudentsCompleted: 'rw',
//   noOfStudentsCompletedSelectedExam: 'eq',
//   commentOnStudentGrades: 'SADSAfdsfsdfsd',
//   recommendations: 'dsaasdadad',
//   gradeDistribution: [
//     {
//       '': 'Number of Students',
//       A: 'das',
//       'A+': 'jhjh',
//       B: 'jh',
//       'B+': 'ghj',
//       C: 'hjg',
//       'C+': 'hjg',
//       D: 'hjg',
//       'D+': 'ghj',
//       F: 'ghjg',
//       'Denied Entry': 'jhg',
//       'In Progress': 'jhgj',
//       Incomplete: 'hgh',
//       Pass: 'jg',
//       Fail: 'hgj',
//       Withdrawn: 'ds',
//     },
//     {
//       '': 'Percentage',
//       A: 'g',
//       'A+': 'hg',
//       B: 'jhg',
//       'B+': 'gjg',
//       C: 'kh',
//       'C+': 'g',
//       D: 'g',
//       'D+': 'gh',
//       F: 'gh',
//       'Denied Entry': 'ds',
//       'In Progress': 'hgj',
//       Incomplete: 'hh',
//       Pass: 'ghj',
//       Fail: 'gh',
//       Withdrawn: 'ds',
//     },
//   ],
//   cloTeachingAndAssessment: [
//     {
//       sno: '1.0',
//       outcomes: 'Knowledge and understanding',
//       relatedPLOCode: '',
//       assessmentMethods: '',
//       targetedLevel: '',
//       actualLevel: '',
//       commentAssessmentResults: '',
//     },
//     {
//       sno: '1.1',
//       outcomes: '1',
//       relatedPLOCode: '2',
//       assessmentMethods: '3',
//       targetedLevel: '4',
//       actualLevel: '5',
//       commentAssessmentResults: '6',
//     },
//     {
//       sno: '1.2',
//       outcomes: '11',
//       relatedPLOCode: '22',
//       assessmentMethods: '33',
//       targetedLevel: '44',
//       actualLevel: '55',
//       commentAssessmentResults: '66',
//     },
//     {
//       sno: '1.3',
//       outcomes: '111',
//       relatedPLOCode: '122',
//       assessmentMethods: '321',
//       targetedLevel: '432',
//       actualLevel: '543',
//       commentAssessmentResults: '654',
//     },
//     {
//       sno: '1.0',
//       outcomes: 'Skills',
//       relatedPLOCode: '',
//       assessmentMethods: '',
//       targetedLevel: '',
//       actualLevel: '',
//       commentAssessmentResults: '',
//     },
//     {
//       sno: '2.1',
//       outcomes: '54',
//       relatedPLOCode: '543',
//       assessmentMethods: 'hgf',
//       targetedLevel: 'q3',
//       actualLevel: 'tre',
//       commentAssessmentResults: 'tre',
//     },
//     {
//       sno: '2.2',
//       outcomes: '54',
//       relatedPLOCode: 'tre',
//       assessmentMethods: 'ytr',
//       targetedLevel: '342',
//       actualLevel: '342ter',
//       commentAssessmentResults: 'tre',
//     },
//     {
//       sno: '2.3',
//       outcomes: '54',
//       relatedPLOCode: 're',
//       assessmentMethods: 'erw',
//       targetedLevel: 're',
//       actualLevel: 'tre',
//       commentAssessmentResults: 'tre',
//     },
//     {
//       sno: '1.0',
//       outcomes: 'Values, autonomy, and responsibility',
//       relatedPLOCode: '',
//       assessmentMethods: '',
//       targetedLevel: '',
//       actualLevel: '',
//       commentAssessmentResults: '',
//     },
//     {
//       sno: '3.1',
//       outcomes: 'tr',
//       relatedPLOCode: 'tre',
//       assessmentMethods: '324',
//       targetedLevel: '2432',
//       actualLevel: '423',
//       commentAssessmentResults: '342',
//     },
//     {
//       sno: '3.2',
//       outcomes: '43',
//       relatedPLOCode: 'tre',
//       assessmentMethods: '432',
//       targetedLevel: '432',
//       actualLevel: '432',
//       commentAssessmentResults: '324',
//     },
//     {
//       sno: '3.3',
//       outcomes: '231',
//       relatedPLOCode: 'tre',
//       assessmentMethods: '434',
//       targetedLevel: '42',
//       actualLevel: '432',
//       commentAssessmentResults: '432',
//     },
//   ],
//   topicsNotCovered: [
//     {
//       Topic: 'das',
//       'Reason for Not Covering/discrepancies': 'das',
//       'Extent of their Impact on Learning Outcomes': 'das',
//       'Compensating Action': 'das',
//     },
//     {
//       Topic: 'sa',
//       'Reason for Not Covering/discrepancies': 'd',
//       'Extent of their Impact on Learning Outcomes': 'das',
//       'Compensating Action': 'das',
//     },
//     {
//       Topic: 'das',
//       'Reason for Not Covering/discrepancies': 'dsa',
//       'Extent of their Impact on Learning Outcomes': 'dsa',
//       'Compensating Action': 'dsa',
//     },
//     {
//       Topic: 'das',
//       'Reason for Not Covering/discrepancies': 'das',
//       'Extent of their Impact on Learning Outcomes': 'dsa',
//       'Compensating Action': 'dsa',
//     },
//   ],
//   courseImprovementPlan: [
//     {
//       Recommendations: '1',
//       Actions: '2',
//       'Needed Support': '3',
//     },
//     {
//       Recommendations: 'da',
//       Actions: 'dsa',
//       'Needed Support': 'das',
//     },
//     {
//       Recommendations: 'ewr',
//       Actions: 'fds',
//       'Needed Support': 'rwe',
//     },
//     {
//       Recommendations: 'fds',
//       Actions: 'fgd',
//       'Needed Support': 'fsd',
//     },
//   ],
// };
// loadDataBack(sampleData);
