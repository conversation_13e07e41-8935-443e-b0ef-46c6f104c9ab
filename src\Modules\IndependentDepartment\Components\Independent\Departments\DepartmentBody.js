import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { List } from 'immutable';
import { useStylesFunction } from 'Modules/ProgramInput/v2/piUtil';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import Chip from '@mui/material/Chip';
import Typography from '@mui/material/Typography';

function DepartmentBody({ sharedWith, departmentId, sharedFromDepartment, sharedFromProgramName }) {
  const classes = useStylesFunction();
  const addDepartment = useContext(parentContext.departmentContext);
  const { departmentsOperation, departmentTabValue } = addDepartment;
  const removeSharedSubjects = (event, selectedId, status) => {
    event.stopPropagation();
    let finalData = sharedWith
      .map((item) => {
        return {
          _program_id: item.get('_program_id', ''),
          programName: item.get('programName', ''),
        };
      })
      .toJS();

    let request = {
      departmentId,
      sharedPrograms: finalData.filter((item) => item._program_id !== selectedId),
      status,
    };
    departmentsOperation(request, null, 'share', '');
  };

  function displaySharedWith() {
    return (
      <>
        <Trans i18nKey={'configuration.share_with'}></Trans>
        <span className="digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded break-word">
          {sharedWith.size
            ? sharedWith.map((item, index) => (
                <Chip
                  key={index}
                  label={item.get('programName', '')}
                  variant="outlined"
                  className="mr-2 mb-2"
                  onDelete={(event) =>
                    removeSharedSubjects(event, item.get('_program_id', ''), 'remove')
                  }
                />
              ))
            : '-NA-'}
        </span>
      </>
    );
  }

  function displaySharedFrom() {
    return (
      <>
        <Trans i18nKey={'shared_from_col'}></Trans>
        <span className="digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded break-word">
          <span className="digi-course-bg digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded break-word">
            {sharedFromProgramName}
          </span>
        </span>
      </>
    );
  }

  return (
    <Typography
      className={
        departmentTabValue === 'admin' ? classes.programSharedAdmin : classes.programShared
      }
      component="span"
    >
      {!sharedFromDepartment ? displaySharedWith() : displaySharedFrom()}
    </Typography>
  );
}

DepartmentBody.propTypes = {
  sharedWith: PropTypes.instanceOf(List),
  departmentId: PropTypes.string,
  sharedFromDepartment: PropTypes.bool,
  sharedFromProgramName: PropTypes.string,
};
export default DepartmentBody;
