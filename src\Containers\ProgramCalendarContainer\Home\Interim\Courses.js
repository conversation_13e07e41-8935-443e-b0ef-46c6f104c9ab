import React, { Fragment, useEffect } from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';
import { PrimaryButton, TextContainer, Text } from '../../Styled';
import Level from './Level';
import CourseContent from './CourseContent';
import CalenderWeeks from '../CalenderWeeks';
import DateRanges from '../DateRanges';
import AcademicWeeks from '../AcademicWeeks';
import { find_sun_to_sat } from '../../../../_utils/function';
import {
  ucFirst,
  jsUcfirstAll,
  levelRename,
  indVerRename,
  getLang,
  getEnvCollegeName,
} from '../../../../utils';
import { coursePopUp, resetInterimMessage } from '../../../../_reduxapi/actions/interimCalendar';
import { NotificationManager } from 'react-notifications';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

const lang = getLang();
const Table = styled.div`
  display: grid;
  grid-template-columns: auto 75px;
  margin-left: 25px;
  margin-bottom: 50px;
`;

const TableWOLevel = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 12fr;
  border-radius: 0px 16px 16px 16px;
  border: 2px solid ${(props) => props.background};
  overflow: hidden;
`;

const HeaderSplit = styled.div`
  display: grid;
  grid-template-rows: 80px auto;
`;

const CourseHeader = styled.div`
  display: grid;
  background-color: #f3f3f3;
  border-left: 1px solid rgba(0, 0, 0, 0.36);
  border-bottom: 1px solid rgba(0, 0, 0, 0.36);
  text-align: center;

  &.left {
    border-left: none;
  }
`;

const HeaderCells = styled.div`
  padding: 20px;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.015em;
  color: black;
`;

const RotationalWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(${(props) => `${props.rep}`}, 1fr);
  overflow: auto;
`;

const RotationalHeader = styled.div`
  border-left: 1px solid rgba(0, 0, 0, 0.36);
  padding: 20px;
  background-color: #fff;
  border-radius: 4px 4px 0px 0px;
  color: black;
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: auto 75px;
  margin-left: 25px;
`;

const TableWOHeaderLevel = styled.div`
  display: grid;
  width: 15em;
  border-radius: 16px 16px 0px 0px;
  overflow: hidden;
`;

const CourseHeader1 = styled.div`
  display: grid;
  background-color: #f3f3f3;
  text-align: center;

  &.left {
    border-left: none;
  }
`;

const HeaderCells1 = styled.div`
  padding: 25px 20px 10px;
  font-size: 20px;
  background: ${(props) => props.background};
  color: white;
  line-height: 24px;
  letter-spacing: 0.25px;
`;

const emptyDivRender = () => {
  return (
    <>
      <Table>
        <TableWOLevel background={'#f3f3f3'}>
          <HeaderSplit>
            <CourseHeader className="left" style={{ border: 'none' }}>
              <HeaderCells className="pdfFont" style={{ minWidth: '10em', paddingTop: '30px' }}>
                <Trans i18nKey={'program_calendar.date_range'}></Trans>
              </HeaderCells>
            </CourseHeader>
          </HeaderSplit>
          <HeaderSplit>
            <CourseHeader style={{ border: 'none' }}>
              <HeaderCells className="pdfFont">
                <Trans i18nKey={'program_calendar.calendar_week'}></Trans>
              </HeaderCells>
            </CourseHeader>
          </HeaderSplit>
          <HeaderSplit>
            <CourseHeader style={{ border: 'none' }}>
              <HeaderCells className="pdfFont">
                <Trans i18nKey={'program_calendar.academic_week'}></Trans>
              </HeaderCells>
            </CourseHeader>
          </HeaderSplit>
          <HeaderSplit>
            <CourseHeader style={{ border: 'none' }}>
              <HeaderCells className="pdfFont" style={{ paddingTop: '30px' }}>
                <Trans i18nKey={'program_calendar.courses'}></Trans>
              </HeaderCells>
            </CourseHeader>
          </HeaderSplit>
        </TableWOLevel>
      </Table>
      <TextContainer>
        <Text>
          <Trans i18nKey={'program_calendar.no_calendar'}></Trans>
        </Text>{' '}
        <Text>
          <Trans i18nKey={'program_calendar.click'}></Trans>
          <PrimaryButton className="dim ml-2 mr-2">
            <Trans i18nKey={'program_calendar.calendar_settings'}></Trans>
          </PrimaryButton>
          <Trans i18nKey={'program_calendar.define_dates_and_curriculum'}></Trans>
        </Text>
      </TextContainer>
    </>
  );
};

const rotational_heading = (
  rotation,
  len,
  active,
  start_date,
  end_date,
  courses,
  events,
  coursePopUp,
  level_no,
  term,
  activeSemester,
  iframeShow,
  currentPGAccess
) => {
  let header_array = [];
  for (let i = 0; i < rotation; i++) {
    if (i === 0) {
      header_array.push(`ROTATION ${i + 1} COURSES`);
    } else {
      header_array.push(`ROTATION ${i + 1} COURSES`);
    }
  }
  return (
    <Fragment>
      {header_array &&
        header_array.length > 0 &&
        header_array.map((item, i) => (
          <HeaderSplit key={i}>
            <CourseHeader>
              <RotationalHeader className="pdfFont" style={{ minWidth: '10em' }} key={item + i}>
                {item}
              </RotationalHeader>
            </CourseHeader>
            <CourseContent
              length={len}
              number={i}
              rotational={true}
              active={active}
              start_date={start_date}
              end_date={end_date}
              courses={courses}
              events={events}
              coursePopUp={coursePopUp}
              level_no={level_no}
              term={term}
              activeSemester={activeSemester}
              iframeShow={iframeShow}
              currentPGAccess={currentPGAccess}
            />
          </HeaderSplit>
        ))}

      {header_array && header_array.length === 0 && (
        <HeaderSplit>
          <CourseHeader>
            <HeaderCells style={{ paddingTop: '30px' }}>
              <Trans i18nKey={'program_calendar.courses'}></Trans>
            </HeaderCells>
          </CourseHeader>
          <CourseContent
            length={len}
            active={active}
            start_date={start_date}
            end_date={end_date}
            courses={courses}
            events={events}
            coursePopUp={coursePopUp}
            level_no={level_no}
            term={term}
            activeSemester={activeSemester}
            iframeShow={iframeShow}
            currentPGAccess={currentPGAccess}
          />
        </HeaderSplit>
      )}
    </Fragment>
  );
};

const check_rotational = (
  rotation,
  value,
  len,
  active,
  start_date,
  end_date,
  courses,
  events,
  coursePopUp,
  level_no,
  term,
  activeSemester,
  iframeShow,
  currentPGAccess
) => {
  switch (rotation) {
    case 'yes':
      return rotational_heading(
        value,
        len,
        active,
        start_date,
        end_date,
        courses,
        events,
        coursePopUp,
        level_no,
        term,
        activeSemester,
        iframeShow,
        currentPGAccess
      );
    default:
      return (
        <HeaderSplit>
          <CourseHeader>
            <HeaderCells className="pdfFont" style={{ paddingTop: '30px' }}>
              <Trans i18nKey={'program_calendar.courses'}></Trans>
            </HeaderCells>
          </CourseHeader>
          <CourseContent
            length={len}
            active={active}
            start_date={start_date}
            end_date={end_date}
            courses={courses}
            events={events}
            coursePopUp={coursePopUp}
            level_no={level_no}
            term={term}
            activeSemester={activeSemester}
            iframeShow={iframeShow}
            currentPGAccess={currentPGAccess}
          />
        </HeaderSplit>
      );
  }
};

const Courses = (props) => {
  const {
    active,
    active_semesters,
    groupYears,
    coursePopUp,
    academic_year_name,
    nav_bar_title,
    iframeShow,
    currentPGAccess,
    error,
    success,
    resetInterimMessage,
    programId,
  } = props;

  let activeYear = active;
  let activeSemester = active_semesters;
  let countOfSemesters = groupYears[activeYear]['semesters'];
  let currentActiveSemester = countOfSemesters[activeSemester];

  useEffect(() => {
    if (error !== null) {
      NotificationManager.error(error);
      setTimeout(() => {
        resetInterimMessage();
      }, 2000);
    } else if (success != null) {
      NotificationManager.success(success);
      setTimeout(() => {
        resetInterimMessage();
      }, 2000);
    }
  }, [error, success, resetInterimMessage]);

  return (
    <Fragment>
      {currentActiveSemester !== undefined &&
      currentActiveSemester &&
      currentActiveSemester.length > 0 &&
      currentActiveSemester[0].level_no !== '' ? (
        <>
          {currentActiveSemester && currentActiveSemester.length > 0 && (
            <>
              {currentActiveSemester.map((current, activeIndex) => {
                let level_no = current.level_no !== '' ? current.level_no : '';
                let term =
                  current.term === 'interim'
                    ? current.term.replace('interim', t('interim'))
                    : current.term.replace('regular', t('regular'));
                let level_title =
                  current.term !== ''
                    ? ucFirst(term + ' - ' + levelRename(level_no, programId))
                    : '';
                let level_title_rotation =
                  current.term !== ''
                    ? ucFirst(levelRename(level_no, programId) + ' (' + term + ')')
                    : '';
                let start_date = current.start_date !== undefined ? current.start_date : '';
                let end_date = current.end_date !== undefined ? current.end_date : '';
                let rotation = current.rotation;
                let rotation_count = current.rotation_count;
                let events = current.events;
                let courses = current.course;

                let dates = [];
                let run = start_date !== '' && end_date !== '' ? true : false;
                if (run) {
                  dates = find_sun_to_sat(start_date, end_date);
                }

                let total = 0;
                if (current.rotation === 'no') {
                  if (current.course && current.course.length > 0) {
                    current.course.forEach((cs) => {
                      const added_credit =
                        cs.credit_hours &&
                        cs.credit_hours.length > 0 &&
                        cs.credit_hours.reduce((sum, el) => {
                          return el.credit_hours + sum;
                        }, 0);
                      total = total + added_credit;
                    });
                  }
                } else {
                  if (current.rotation_course && current.rotation_course.length > 0) {
                    current.rotation_course.forEach((cs) => {
                      const added_credit =
                        cs.credit_hours &&
                        cs.credit_hours.length > 0 &&
                        cs.credit_hours.reduce((sum, el) => {
                          return el.credit_hours + sum;
                        }, 0);
                      total = total + added_credit;
                    });
                  }
                }

                return (
                  <Fragment key={activeIndex}>
                    <TableHeader className="printViewHide">
                      <TableWOHeaderLevel>
                        <CourseHeader1 className="left">
                          <HeaderCells1 background={activeIndex === 0 ? '#7145cd' : '#b61e82'}>
                            {level_title}
                          </HeaderCells1>
                          {level_title !== '' && (
                            <div
                              className={`${
                                lang !== 'ar' ? 'interim-credit-hours' : 'interim-credit-hours-ar'
                              }`}
                            >
                              <Trans i18nKey={'total_credit'}></Trans>: {total}
                            </div>
                          )}
                        </CourseHeader1>
                      </TableWOHeaderLevel>
                    </TableHeader>
                    <div className="container-fluid printTitle">
                      <div className="col-lg-12">
                        <div className="row">
                          <div className="col-lg-8">
                            <h5>
                              {getEnvCollegeName()
                                .replace('for Medical Studies', ' - curriculum map')
                                .toUpperCase()}
                            </h5>
                            <h5>
                              {nav_bar_title !== ''
                                ? jsUcfirstAll(nav_bar_title.replace('Calendar', ''))
                                : ''}
                              {'  '}|{'  '}Year{'  '}
                              {activeYear.replace('year', '')}
                              {'  '}|{'  '}
                              {term !== '' ? ucFirst(term) : ''}
                              {'  '}
                              {indVerRename('Term', programId)}
                              {'  '}|{'  '}Semester{'  '}
                              {active_semesters.replace('semester', '')}
                            </h5>
                          </div>
                          <div className="col-lg-4">
                            <div className="float-right" style={{ margin: '35px 40px 0px 35px' }}>
                              <h5>Academic Year {academic_year_name} G</h5>
                            </div>
                          </div>
                        </div>
                      </div>
                      <br />
                    </div>
                    <Table>
                      <TableWOLevel background={activeIndex === 0 ? '#7145cd' : '#b61e82'}>
                        <HeaderSplit>
                          <CourseHeader className="left">
                            <HeaderCells
                              className="pdfFont"
                              style={{ minWidth: '10em', paddingTop: '30px' }}
                            >
                              <Trans i18nKey={'program_calendar.date_range'}></Trans>
                            </HeaderCells>
                          </CourseHeader>
                          {run && <DateRanges data={dates} />}
                        </HeaderSplit>
                        <HeaderSplit>
                          <CourseHeader>
                            <HeaderCells className="pdfFont">
                              <Trans i18nKey={'program_calendar.calendar_week'}></Trans>
                            </HeaderCells>
                          </CourseHeader>
                          {run && <CalenderWeeks data={dates} />}
                        </HeaderSplit>
                        <HeaderSplit>
                          <CourseHeader>
                            <HeaderCells className="pdfFont">
                              <Trans i18nKey={'program_calendar.academic_week'}></Trans>
                            </HeaderCells>
                          </CourseHeader>
                          {run && <AcademicWeeks data={dates} />}
                        </HeaderSplit>
                        <RotationalWrapper rep={rotation_count !== 0 ? rotation_count : 1}>
                          {check_rotational(
                            rotation,
                            rotation_count,
                            dates.length,
                            activeYear,
                            start_date,
                            end_date,
                            courses,
                            events,
                            coursePopUp,
                            level_no,
                            term,
                            activeSemester,
                            iframeShow,
                            currentPGAccess
                          )}
                        </RotationalWrapper>
                      </TableWOLevel>
                      <HeaderSplit>
                        <div></div>
                        <Level
                          start_date={start_date}
                          end_date={end_date}
                          level_title={level_title_rotation}
                          background={activeIndex === 0 ? '#7145cd' : '#b61e82'}
                        />
                      </HeaderSplit>
                    </Table>
                  </Fragment>
                );
              })}
            </>
          )}
        </>
      ) : (
        emptyDivRender()
      )}
    </Fragment>
  );
};

Courses.propTypes = {
  active: PropTypes.string,
  active_semesters: PropTypes.string,
  groupYears: PropTypes.object,
  coursePopUp: PropTypes.func,
  academic_year_name: PropTypes.string,
  nav_bar_title: PropTypes.string,
  iframeShow: PropTypes.bool,
  currentPGAccess: PropTypes.bool,
  error: PropTypes.object,
  success: PropTypes.object,
  resetMessage: PropTypes.func,
  resetInterimMessage: PropTypes.func,
  programId: PropTypes.string,
};

const mapStateToProps = ({ interimCalendar, auth, calender }) => ({
  id: interimCalendar.institution_Calender_Id,
  title: interimCalendar.academic_year_name,
  status: interimCalendar.curriculum,
  active: interimCalendar.active_year,
  active_semesters: interimCalendar.active_semesters,
  isLoading: interimCalendar.isLoading,
  isAuthenticated: auth.token !== null,
  token: auth.token !== null ? auth.token.replace(/"/g, '') : null,
  //programId: calender.programId,
  userRole: auth.loggedInUserData.role,
  groupYears: {
    year1: interimCalendar.year1,
    year2: interimCalendar.year2,
    year3: interimCalendar.year3,
    year4: interimCalendar.year4,
    year5: interimCalendar.year5,
    year6: interimCalendar.year6,
  },
  academic_year_name: interimCalendar.academic_year_name,
  nav_bar_title: calender.nav_bar_title,
  error: interimCalendar.error,
  success: interimCalendar.success,
});

export default connect(mapStateToProps, { coursePopUp, resetInterimMessage })(Courses);
