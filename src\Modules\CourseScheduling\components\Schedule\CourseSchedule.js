import React, { useState, useEffect, useRef, Suspense } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map, Set, fromJS } from 'immutable';
import moment from 'moment';
import {
  format,
  isValid,
  isSameHour,
  isSameMinute,
  isBefore,
  differenceInMinutes,
  addMinutes,
  subMilliseconds,
  areIntervalsOverlapping,
} from 'date-fns';
import Dropdown from 'react-bootstrap/Dropdown';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { ThemeProvider } from '@mui/styles';
import Button from '@mui/material/Button';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import Filters from './Filters';
import ScheduleListView from './ScheduleListView';
import ScheduleCalendar from './ScheduleCalendar';
import AddEditScheduleModal from '../../modal/AddEditScheduleModal';
import AddEditSupportSessionAndEvents from '../../modal/AddEditSupportSessionAndEvents';
import BreakSessionFlow from '../../modal/BreakSessionFlow';
import MergeScheduleModal from '../../modal/MergeScheduleModal';
import SelectStudentGroupsModal from '../../modal/SelectStudentGroupsModal';
import AlertConfirmModal from '../../modal/AlertConfirmModal';
import { getRemotePlatform, identifyValueSchedule, validateSchedule } from './utils';
import { getStartEndDate, transformDateTimeToUTC } from '../utils';
import {
  allowedTimeInterval,
  capitalize,
  getURLParams,
  isIndGroup,
  isMissedSessionModuleEnabled,
  isModuleEnabled,
  MUI_THEME,
} from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import useCalendar from 'Hooks/useCalendarHook';
import {
  getScheduleStaffOptionList,
  getStudentGroupBasedDeliveryTypes,
} from '_reduxapi/course_scheduling/action';
import { getFormattedGroupName } from 'Modules/ReportsAndAnalytics/utils';
import useCountry from 'Hooks/useCountryCodeHook';
import AddEditScheduleModalDrawer from './AddEditScheduleModalDrawer';
import {
  selectStudentBasedDeliveryTypes,
  selectExternalStaff,
} from '_reduxapi/course_scheduling/selectors';
import { selectScheduledExternalStaff } from '_reduxapi/course_scheduling/selectors';
import uniqWith from 'lodash/uniqWith';
import MergeScheduleModalDrawer from 'Modules/CourseScheduling/modal/MergeScheduleModalDrawer';

const MissedToCompleteModal = React.lazy(() => import('./MissedToCompleteModal'));
const RevokeModal = React.lazy(() => import('./RevokeModal'));

function CourseSchedule({
  course,
  activeScheduleView,
  setData,
  activeSessionFlow,
  institutionCalendarId,
  saveSchedule,
  scheduleAvailability,
  getScheduleAvailability,
  fetchCourseSchedule,
  paginationMetaData,
  loading,
  programName,
  getAdvancedSettings,
  saveAdvanceSettings,
  advancedSettings,
  cancelSchedule,
  mergeSchedule,
  getMergeSchedule,
  saveMergeSchedule,
  studentGroupsWithStudents,
  saveStudentGroupsWithStudents,
  saveSupportAndEventsSchedule,
  updateMissedSession,
}) {
  const studentBasedDeliveryTypes = useSelector(selectStudentBasedDeliveryTypes);
  const dispatch = useDispatch();
  const manualStaffOptions = useSelector((state) =>
    state.courseScheduling.getIn(['staffScheduleOptionList', 0], Map())
  );

  const initialModalData = Map({
    show: false,
    sessionFlowId: '',
    studentGroupId: '',
    mode: 'create',
    isReassign: false,
    schedule: Map({
      type: '',
      student_groups: List(),
      schedule_date: '',
      start: Map(),
      end: Map(),
      mode: '',
      subjects: List(),
      ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') === true
        ? {
            staffs: List(),
            checkStaff: List(),
            checkclassLeaderData: List(),
            checkExternalList: List(),
          }
        : {
            staffs: List(),
          }),
      selfAttendance: false,
      _infra_id: '',
      infra_name: '',
      topic: '',
      _topic_id: '',
      remotePlatform: '',
      manualStaffs: List(),
      mobileNumberOrEmail: '',
      ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
        outsideCampus: false,
        selectNumOrEmail: '',
      }),
    }),
    errors: List(),
  });
  const initialMergeModalData = Map({
    show: false,
    mode: 'create',
    scheduleId: '',
    sessionFlowId: '',
    schedule: Map({
      schedule_date: '',
      merge_ids: List(),
      start: Map(),
      end: Map(),
      mode: '',
      subjects: List(),
      ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') === true
        ? {
            staffs: List(),
            checkStaff: List(),
            checkclassLeaderData: List(),
            checkExternalList: List(),
          }
        : {
            staffs: List(),
          }),
      selfAttendance: false,
      _infra_id: '',
      infra_name: '',
      topic: '',
      _topic_id: '',
      remotePlatform: '',
      manualStaffs: List(),
      ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
        outsideCampus: false,
        selectNumOrEmail: '',
      }),
    }),
    existingSchedule: Map(),
    scheduleList: List(),
    errors: List(),
  });
  const initialSupportAndEventsModalData = Map({
    show: false,
    existingSchedule: Map(),
    mode: 'create',
    isReassign: false,
    schedule: Map({
      type: '',
      sub_type: '',
      schedule_date: '',
      start: Map(),
      end: Map(),
      mode: '',
      subjects: List(),
      ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') === true
        ? {
            staffs: List(),
            checkStaff: List(),
            checkclassLeaderData: List(),
            checkExternalList: List(),
          }
        : {
            staffs: List(),
          }),
      _infra_id: '',
      infra_name: '',
      remotePlatform: '',
      manualStaffs: List(),
    }),
    errors: List(),
  });
  const initialStudentGroupModalData = Map({
    show: false,
    type: '',
    mode: 'create',
    sessionId: '',
    activeStudentGroupId: null,
    readOnly: false,
    data: Map({ title: '', studentGroups: List() }),
  });

  const initialState = Map({
    // state: Map({ group: '', deliveryType: '', groupNo: '', studentIds: List() }),
    group: '',
    deliveryType: '',
    groupNo: '',
    studentIds: List(),
    selectAll: Map(),
    selectedStudents: List(),
    rotation_count: '',
  });
  const [filter, setFilter] = useState(
    Map({
      view: 'regular',
      status: '',
      deliveryType: '',
      studentGroup: '',
    })
  );

  const staffId = useSelector((state) => state.auth.loggedInUserData._id);

  const [modalData, setModalData] = useState(initialModalData);

  const [alertConfirmModalData, setAlertConfirmModalData] = useState({ show: false });
  const [deleteData, setDeleteData] = useState(Map({ session: Map(), schedule: Map() }));
  const [showBreakSessionFlowModal, setShowBreakSessionFlowModal] = useState(false);
  const [showMergeCheckbox, setShowMergeCheckbox] = useState(false);
  const [showMissedToComplete, setShowMissedToComplete] = useState(false);
  const [showRevoke, setShowRevoke] = useState(false);

  const [mergeModalData, setMergeModalData] = useState(initialMergeModalData);
  const [anchorEl, setAnchorEl] = useState(null);
  const [studentGroupModalData, setStudentGroupModalData] = useState(initialStudentGroupModalData);
  const [supportAndEventsModalData, setSupportAndEventsModalData] = useState(
    initialSupportAndEventsModalData
  );
  const [missedToCompleteOpen, setMissedToCompleteOpen] = useState(false);
  const [revokeOpen, setRevokeOpen] = useState(false);
  const addEventsAndSupportSessionRef = useRef();
  const isRotation = course.get('rotation', '') === 'yes';
  const isAllowedToSchedule = getIsAllowedToSchedule();
  const { isCurrentCalendar } = useCalendar();
  const currentCalendar = isCurrentCalendar();
  const programId = getURLParams('programId', true);

  const [state, setState] = useState(initialState);
  const GetExternalStaff = useSelector(selectExternalStaff);
  const getscheduledExternalStaff = useSelector(selectScheduledExternalStaff);

  useEffect(() => {
    if (isModuleEnabled('OUTSIDE_CAMPUS_V2') === true) {
      if (
        modalData.getIn(['schedule', 'checkExternalList'], List()).size === 0 &&
        modalData.get('show', false)
      ) {
        const checkDataUpdateExternal =
          getscheduledExternalStaff?.get('externalStaffs', List()) || List();
        const checkedId = checkDataUpdateExternal.map((sessionFlow) => sessionFlow.get('_id', ''));
        setModalData((pre) =>
          pre.setIn(
            ['schedule', 'checkExternalList'],
            GetExternalStaff.map((staff) => {
              if (checkedId.includes(staff.get('_id', ''))) {
                return staff.set('isChecked', true);
              }
              return staff;
            })
          )
        );
      }
      if (
        mergeModalData.getIn(['schedule', 'checkExternalList'], List()).size === 0 &&
        mergeModalData.get('show', false)
      ) {
        const checkDataUpdateExternal =
          getscheduledExternalStaff?.get('externalStaffs', List()) || List();
        const checkedId = checkDataUpdateExternal.map((sessionFlow) => sessionFlow.get('_id', ''));
        setMergeModalData((pre) =>
          pre.setIn(
            ['schedule', 'checkExternalList'],
            GetExternalStaff.map((staff) => {
              if (checkedId.includes(staff.get('_id', ''))) {
                return staff.set('isChecked', true);
              }
              return staff;
            })
          )
        );
      }
      return () => {
        setData(Map({ scheduledExternalStaff: List() }));
      };
    }
  }, [GetExternalStaff, modalData, mergeModalData]); //eslint-disable-line
  const [searchValue, setSearchValue] = useState('');
  useEffect(() => {
    if (!course.isEmpty() && course.has('_course_id') && advancedSettings.isEmpty()) {
      getAdvancedSettings(programId, course.get('_course_id'), course.get('level_no'));
      dispatch(
        getScheduleStaffOptionList(
          programId,
          course.get('_course_id', ''),
          course.get('level_no', '')
        )
      );
    }
  }, [course, advancedSettings, getAdvancedSettings, programId]);

  const activeViewType = filter.get('view');

  function changeActiveType(value) {
    setData(Map({ courseSchedule: Map(), mainView: value }));
    fetchCourseSchedule({
      type: value === 'support' ? 'support_session' : value === 'events' ? 'event' : value,
      isRefresh: true,
    });
  }

  function getActiveViewType() {
    return activeViewType === 'support'
      ? 'support_session'
      : activeViewType === 'events'
      ? 'event'
      : activeViewType;
  }

  function getCourse() {
    const isRegular = activeViewType === 'regular';
    let filteredSessionFlow = course
      .get('session_flow', List())
      .filter((session) => {
        const status = filter.get('status');
        const deliveryType = filter.get('deliveryType');
        if (!status && !deliveryType) {
          return true;
        }

        let incStatus = true;
        let incDeliveryType = true;
        if (status) {
          const studentGroupId = filter.get('studentGroup');
          if (status === 'scheduled') {
            if (studentGroupId && isRegular) {
              incStatus = session
                .get('student_group', List())
                .filter((studentGroup) => studentGroup.get('_id') === studentGroupId)
                .some((sg) => sg.get('schedule_status'));
            } else {
              incStatus = isRegular
                ? session.get('student_group', List()).every((sg) => sg.get('schedule_status'))
                : Boolean(session.get('schedule_date'));
            }
          } else if (status === 'pending') {
            if (studentGroupId && isRegular) {
              incStatus = session
                .get('student_group', List())
                .filter((studentGroup) => studentGroup.get('_id') === studentGroupId)
                .some((sg) => !sg.get('schedule_status'));
            } else {
              incStatus = isRegular
                ? session.get('student_group', List()).some((sg) => !sg.get('schedule_status'))
                : !session.get('schedule_date');
            }
          } else if (status === 'missed') {
            if (isRegular) {
              incStatus = session
                .get('schedule', List())
                .some(
                  (status) => status.get('status', '') === 'missed' && status.get('isActive', false)
                );
            } else {
              incStatus = session.get('status', '') === 'missed' && session.get('isActive', false);
            }
          } else if (status === 'completed') {
            if (isRegular) {
              incStatus = session
                .get('schedule', List())
                .some(
                  (status) =>
                    status.get('status', '') === 'completed' && status.get('isActive', false)
                );
            } else {
              incStatus =
                session.get('status', '') === 'completed' && session.get('isActive', false);
            }
          } else if (status === 'missed to completed') {
            if (isRegular) {
              incStatus = session
                .get('schedule', List())
                .some((status) => status.get('isMissedToComplete', false));
            } else {
              incStatus =
                session.get('isMissedToComplete', false) && session.get('isActive', false);
            }
          } else if (status === 'canceled') {
            if (isRegular) {
              incStatus = session
                .get('schedule', List())
                .some((status) => !status.get('isActive', false));
            } else {
              incStatus = !session.get('isActive', false);
            }
          }
        }
        if (deliveryType) {
          incDeliveryType = session.get(isRegular ? 'delivery_type' : 'sub_type') === deliveryType;
        }
        return incStatus && incDeliveryType;
      })
      .toList();
    const studentGroupId = filter.get('studentGroup');
    if (studentGroupId) {
      if (isRegular) {
        filteredSessionFlow = filteredSessionFlow.map((sessionFlow) => {
          const studentGroups = sessionFlow
            .get('student_group', List())
            .filter((studentGroup) => studentGroup.get('_id') === studentGroupId)
            .toList();
          const schedule = sessionFlow
            .get('schedule', List())
            .filter((schedule) =>
              Boolean(
                schedule.get('student_groups', List())
                // .find((sg) => sg.get('group_id') === studentGroupId)
              )
            )
            .toList();

          return sessionFlow.merge(Map({ student_group: studentGroups, schedule }));
        });
      } else {
        filteredSessionFlow = filteredSessionFlow
          .filter((sessionFlow) => {
            const studentGroupIdList = sessionFlow
              .get('student_groups', List())
              .map((studentGroup) => studentGroup.get('group_id'))
              .toList();
            return studentGroupIdList.includes(studentGroupId);
          })
          .toList();
      }
    }
    return course.set('session_flow', filteredSessionFlow);
  }

  function getIsAllowedToSchedule() {
    const isShared = course.get('isShared');
    if (!isShared) return true;
    return Boolean(course.get('isAllowedToSchedule'));
  }

  function handleCloseModal() {
    setModalData(initialModalData);
    setMergeModalData(initialMergeModalData);
    setSupportAndEventsModalData(initialSupportAndEventsModalData);
    setShowMergeCheckbox(false);
    setData(Map({ scheduleAvailability: Map(), mergeSchedule: Map() }));
  }

  function handleAddEditSchedule(
    sessionFlow,
    mode,
    existingSchedule,
    defaultDate = { start: Map(), end: Map(), schedule_date: '' },
    shouldGetAvailability = true,
    isReassign = false
  ) {
    dispatch(
      getScheduleStaffOptionList(
        programId,
        course.get('_course_id', ''),
        course.get('level_no', '')
      )
    );
    if (mode === 'create') {
      let updatedModalData = modalData.merge(
        Map({
          show: true,
          mode,
          sessionFlowId: sessionFlow.get('_id'),
          studentGroupId: sessionFlow.get('_student_group_id'),
          schedule: modalData.get('schedule').merge(Map(defaultDate)),
        })
      );
      if (isRotation) {
        updatedModalData = updatedModalData.setIn(['schedule', 'rotation_count'], null);
      }
      setModalData(updatedModalData);
      getAvailability('update', updatedModalData);
    }
    if (mode === 'update') {
      if (existingSchedule === undefined || existingSchedule.isEmpty()) return;
      const schedule = Map({
        type: existingSchedule.get('type'),
        student_groups: existingSchedule.get('student_groups', List()).map((studentGroup) => {
          return studentGroup
            .set('_id', studentGroup.get('group_id'))
            .delete('group_id')
            .set(
              'session_group',
              studentGroup
                .get('session_group', List())
                .map((sessionGroup) =>
                  sessionGroup
                    .set('_id', sessionGroup.get('session_group_id'))
                    .delete('session_group_id')
                )
            );
        }),
        schedule_date: existingSchedule.get('schedule_date', ''),
        start: existingSchedule.get('start', Map()),
        end: existingSchedule.get('end', Map()),
        mode: existingSchedule.get('mode'),
        subjects: getExistingSubjects(sessionFlow, existingSchedule),
        ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') === true
          ? {
              [`${
                existingSchedule.get('outsideCampus', false) ? 'checkStaff' : 'staffs'
              }`]: existingSchedule.get('staffs', List()).map((staff) =>
                Map({
                  _id: staff.get('_staff_id'),
                  _staff_id: staff.get('_staff_id'),
                  name: staff.get('staff_name'),
                  staff_name: staff.get('staff_name'),
                })
              ),
            }
          : {
              staffs: existingSchedule.get('staffs', List()).map((staff) =>
                Map({
                  _id: staff.get('_staff_id'),
                  _staff_id: staff.get('_staff_id'),
                  name: staff.get('staff_name'),
                  staff_name: staff.get('staff_name'),
                })
              ),
            }),
        ...(existingSchedule.get('outsideCampus', false) &&
          isModuleEnabled('OUTSIDE_CAMPUS_V2') && {
            checkclassLeaderData: existingSchedule.get('classLeaders', List()).map((staff) =>
              Map({
                _staff_id: staff,
              })
            ),
          }),
        ...(existingSchedule.get('outsideCampus', false) && {
          selfAttendance: existingSchedule.get('selfAttendance', false),
        }),
        // checkExternalList: existingSchedule.get('externalStaffs', List()).map((externalStaff) =>
        //   Map({
        //     _staff_id: externalStaff,
        //   })
        // ),
        _infra_id: existingSchedule.get('_infra_id', ''),
        infra_name: existingSchedule.get('infra_name', ''),
        remotePlatform: getRemotePlatform(existingSchedule),
        ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
          outsideCampus: existingSchedule.get('outsideCampus', false),
          mobileNumberOrEmail:
            existingSchedule.getIn(['campusDetails', 'mobile'], '') ||
            existingSchedule.getIn(['campusDetails', 'email'], ''),
        }),
        _topic_id: existingSchedule.get('_topic_id', ''),
        topic: existingSchedule.get('topic', ''),
        ...(isRotation && { rotation_count: existingSchedule.get('rotation_count', null) }),
        ...(existingSchedule.get('attendanceTakingStaff', List()).size > 0 && {
          manualStaffs: existingSchedule
            .get('attendanceTakingStaff', List())
            .map((item) => item.get('staffId', '')),
        }),
      });
      const updatedModalData = modalData.merge(
        Map({
          show: true,
          mode,
          isReassign,
          sessionFlowId: sessionFlow.get('_id'),
          scheduleId: existingSchedule.get('_id'),
          studentGroupId: sessionFlow.get('_student_group_id'),
          schedule,
        })
      );
      setModalData(updatedModalData);
      if (shouldGetAvailability) {
        getAvailability('update', updatedModalData, existingSchedule);
      }
      setDeleteData(Map({ session: sessionFlow, schedule: existingSchedule }));
    }
  }

  function getExistingSubjects(sessionFlow, schedule) {
    const existingSubjectIds = schedule
      .get('subjects', List())
      .map((subject) => subject.get('_subject_id'));
    return sessionFlow
      .get('subjects', List())
      .filter((subject) => existingSubjectIds.includes(subject.get('_subject_id')))
      .toList();
  }

  function autoChange(
    mode,
    studentGroups,
    subjects,
    staffs,
    _infra_id,
    _infra_name,
    remotePlatform
  ) {
    let schedule = modalData.get('schedule', Map());
    schedule = schedule.merge(
      Map({
        mode: mode,
        student_groups: studentGroups,
        subjects: subjects,
        staffs: staffs,
        _infra_id: _infra_id,
        infra_name: _infra_name,
        remotePlatform,
      })
    );
    const updatedModalData = modalData.set('schedule', schedule);
    setModalData(updatedModalData);
  }

  function handleScheduleDataChange(name, value) {
    let schedule = modalData.get('schedule', Map());
    if (name === 'schedule_date') {
      schedule = initialModalData.get('schedule', Map()).merge(
        Map({
          schedule_date: value,
          topic: schedule.get('topic', ''),
          _topic_id: schedule.get('_topic_id', ''),
        })
      );
      if (isRotation) {
        schedule = schedule.set('rotation_count', null);
      }
    } else if (['start', 'end'].includes(name)) {
      const { start, end } = value;
      schedule = schedule.merge(
        Map({
          ...(name === 'start' && { start, end }),
          ...(name === 'end' && { end }),
          student_groups: List(),
          mode: '',
          subjects: List(),
          staffs: List(),
          _infra_id: '',
          infra_name: '',
          remotePlatform: '',
          ...(isRotation && { rotation_count: null }),
        })
      );
    } else if (name === 'mode') {
      if (value === 'remote') schedule = schedule.set('manualStaffs', fromJS([]));
      schedule = schedule.merge(
        Map({ mode: value, _infra_id: '', infra_name: '', remotePlatform: '', outsideCampus: '' })
      );
    } else if (name === 'infra') {
      schedule = schedule.merge(
        Map({
          _infra_id: value.get('value', ''),
          infra_name: value.get('name', ''),
          remotePlatform: value.get('remotePlatform', ''),
          ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
            outsideCampus: value.get('outsideCampus', false),
          }),
        })
      );
    } else if (name === '_topic_id') {
      schedule = schedule.merge(
        Map({ _topic_id: value.get('value', ''), topic: value.get('name', '') })
      );
    } else if (name === 'student_groups') {
      if (isRotation) {
        const rotationCount = schedule.get('rotation_count');
        if (value.size === 0) {
          schedule = schedule.set('rotation_count', null);
        } else if (rotationCount === null) {
          const currentRotationCount = value.getIn([0, 'group_no']);
          if (currentRotationCount) {
            schedule = schedule.set('rotation_count', currentRotationCount);
          }
        }
      }
      schedule = schedule.set(name, value).set('mode', '').set('subjects', List());
    } else if (name === 'manualStaffs') {
      schedule = schedule.set(name, fromJS(value));
    } else {
      schedule = schedule.set(name, value);
    }
    if (['schedule_date', 'start', 'end'].includes(name)) {
      setData(Map({ curriculumsWithFramework: List() }));
    }

    const updatedModalData = modalData.set('schedule', schedule);
    if (name === 'start') {
      getAvailability('update', updatedModalData);
    }
    setModalData(updatedModalData);
  }

  const manualStaffObject = (scheduleData) => {
    const staffObjectArray = scheduleData
      .get('manualStaffs', List())
      .map((staff) =>
        manualStaffOptions
          .get('assignedStaffIds', List())
          .find((staffObj) => staffObj.get('staffId', '') === staff)
      );
    return staffObjectArray;
  };

  const { countryCodeLength } = useCountry();

  const filterExternalList = modalData.getIn(['schedule', 'checkExternalList'], List());
  const filterExternalListmergeModal = mergeModalData.getIn(
    ['schedule', 'checkExternalList'],
    List()
  );
  const checkUpdateExternal = filterExternalList.filter(
    (item) => item.get('isChecked', false) === true
  );

  const checkUpdateExternalListMergeModal = filterExternalListmergeModal.filter(
    (item) => item.get('isChecked', false) === true
  );

  function handleSaveSchedule(mode, isConfirmed = false, topics = []) {
    const scheduleData = modalData.get('schedule', Map());
    const valueToCheck = scheduleData.get('mobileNumberOrEmail', '');
    const checkedValue = identifyValueSchedule(valueToCheck, countryCodeLength);
    if (scheduleData.isEmpty()) return;
    const requestBody = {
      program_name: programName,
      course_name: course.get('courses_name'),
      course_code: course.get('courses_number'),
      _institution_calendar_id: institutionCalendarId,
      type: 'regular',
      _program_id: programId,
      _course_id: course.get('_course_id'),
      _session_id: modalData.get('sessionFlowId'),
      term: course.get('term'),
      _student_group_id: modalData.get('studentGroupId'),
      year_no: course.get('year_no'),
      level_no: course.get('level_no'),
      student_groups: scheduleData
        .get('student_groups', List())
        .map((studentGroup) => {
          return studentGroup
            .set('group_id', studentGroup.get('_id'))
            .delete('_id')
            .delete('schedule_status')
            .set(
              'session_group',
              studentGroup
                .get('session_group', List())
                .map((sessionGroup) =>
                  sessionGroup.set('session_group_id', sessionGroup.get('_id')).delete('_id')
                )
            );
        })
        .toJS(),
      rotation: isRotation ? 'yes' : 'no',
      ...(isRotation && { rotation_count: scheduleData.get('rotation_count') }),
      schedule_date: moment(scheduleData.get('schedule_date')).format('YYYY-MM-DD'),
      start: scheduleData.get('start').toJS(),
      end: scheduleData.get('end').toJS(),
      scheduleStartDateAndTime: transformDateTimeToUTC(
        scheduleData.get('schedule_date'),
        scheduleData.getIn(['start', 'hour']),
        scheduleData.getIn(['start', 'minute']),
        scheduleData.getIn(['start', 'format'])
      ),
      scheduleEndDateAndTime: transformDateTimeToUTC(
        scheduleData.get('schedule_date'),
        scheduleData.getIn(['end', 'hour']),
        scheduleData.getIn(['end', 'minute']),
        scheduleData.getIn(['end', 'format'])
      ),
      mode: scheduleData.get('mode'),
      subjects: scheduleData
        .get('subjects', List())
        .map((subject) =>
          Map({
            _subject_id: subject.get('_subject_id'),
            subject_name: subject.get('subject_name'),
          })
        )
        .toJS(),

      ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') === true
        ? {
            staffs: scheduleData
              .get(scheduleData.get('outsideCampus', false) ? 'checkStaff' : 'staffs', List())
              .map((staff) =>
                Map({ staff_name: staff.get('staff_name'), _staff_id: staff.get('_staff_id') })
              )
              .toJS(),
          }
        : {
            staffs: scheduleData
              .get('staffs', List())
              .map((staff) =>
                Map({ staff_name: staff.get('staff_name'), _staff_id: staff.get('_staff_id') })
              )
              .toJS(),
          }),
      _infra_id: scheduleData.get('_infra_id'),
      infra_name: scheduleData.get('infra_name'),
      isActive: true,
      ...(scheduleData.get('mode') === 'remote' && scheduleData.get('remotePlatform') !== null
        ? {
            remotePlatform: scheduleData.get('remotePlatform'),
          }
        : { remotePlatform: 'zoom' }),
      ...(manualStaffOptions.get('manualAttendance', false) && {
        attendanceTakingStaff: manualStaffObject(scheduleData).toJS(),
      }),

      ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') && {
        outsideCampus: scheduleData.get('outsideCampus', false),
        ...(scheduleData.get('outsideCampus', false) === true && {
          classLeaders: scheduleData
            .get('checkclassLeaderData', List())
            .map((item) => item.get('_staff_id', '')),
          externalStaffs: checkUpdateExternal.map((item) => item.get('_id', '')),
          selfAttendance: scheduleData.get('selfAttendance', false),
        }),
        ...(scheduleData.get('outsideCampus', false) === true && {
          campusDetails: {
            mobile:
              checkedValue === 'MobileNumber' ? scheduleData.get('mobileNumberOrEmail', '') : '',
            email: checkedValue === 'Gmail' ? scheduleData.get('mobileNumberOrEmail', '') : '',
          },
        }),
      }),
    };
    const checkTopicIsRequired = topics.length > 0;
    if (scheduleData.get('_topic_id', '') !== '') {
      requestBody._topic_id = scheduleData.get('_topic_id', '');
      requestBody.topic = scheduleData.get('topic', '');
    }
    const validationErrorMsg = validateSchedule(
      requestBody,
      false,
      checkTopicIsRequired,
      false,
      programId
    );
    if (validationErrorMsg) {
      setData(Map({ message: validationErrorMsg }));
      return;
    }
    if (mode === 'create' && !isConfirmed) {
      const isAllowedToSchedule = isAllowedToBreakSessionFlow();
      if (!isAllowedToSchedule) {
        setShowBreakSessionFlowModal(true);
        return;
      }
    }
    saveSchedule({
      mode,
      isReassign: modalData.get('isReassign', false),
      ...(mode === 'update' && { _id: modalData.get('scheduleId') }),
      requestBody,
      page: getActiveSessionFlowPage(modalData.get('sessionFlowId')),
      callback: handleCloseModal,
      errCallback: setScheduleErrors,
    });
    setScheduleErrors(List());
  }

  function handleDeleteSchedule({ sessionFlow, schedule }, isConfirmed) {
    if (!isConfirmed) {
      setAlertModalData({
        show: true,
        title: t('course_schedule.delete_schedule'),
        body: <div>{t('course_schedule.delete_schedule_desc')}</div>,
        variant: 'confirm',
        data: { data: { sessionFlow, schedule }, operation: 'delete' },
        confirmButtonLabel: t('delete'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    saveSchedule({
      mode: 'delete',
      _id: schedule.get('_id'),
      page: getActiveSessionFlowPage(sessionFlow.get('_id')),
      callback: handleCloseModal,
      requestBody: {
        _institution_calendar_id: institutionCalendarId,
        _program_id: programId,
        _course_id: course.get('_course_id'),
        term: course.get('term'),
        level_no: course.get('level_no'),
        _session_id: sessionFlow.get('_id'),
      },
    });
  }

  function handleCancelSchedule({ sessionFlow, schedule }, isConfirmed) {
    if (!isConfirmed) {
      setAlertModalData({
        show: true,
        title: t('course_schedule.cancel_schedule'),
        body: <div>{t('course_schedule.cancel_schedule_desc')}</div>,
        variant: 'confirm',
        data: { data: { sessionFlow, schedule }, operation: 'cancel' },
        confirmButtonLabel: t('confirm'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    cancelSchedule({
      _id: schedule.get('_id'),
      page: getActiveSessionFlowPage(sessionFlow.get('_id')),
      requestBody: {
        _institution_calendar_id: institutionCalendarId,
        _program_id: programId,
        _course_id: course.get('_course_id'),
        term: course.get('term'),
        level_no: course.get('level_no'),
        _session_id: sessionFlow.get('_id'),
        isActive: false,
      },
    });
  }

  function getActiveSessionFlowPage(sessionFlowId) {
    const sessionFlowIndex =
      course.get('session_flow', List()).findIndex((sf) => sf.get('_id') === sessionFlowId) + 1;
    return Math.ceil(sessionFlowIndex / 10);
  }

  function setScheduleErrors(errors) {
    setModalData(modalData.set('errors', errors));
  }

  function setMergeScheduleErrors(errors) {
    setMergeModalData(mergeModalData.set('errors', errors));
  }

  function setSupportAndEventsScheduleErrors(errors) {
    setSupportAndEventsModalData(supportAndEventsModalData.set('errors', errors));
  }

  function checkIfTimeSlotOverlapsExcludedTimes(data) {
    const sessionDate = data
      .getIn(['schedule', 'schedule_date'], '')
      .split('T')
      .slice(0, 1)
      .join('');
    const start = data.getIn(['schedule', 'start'], Map());
    const end = data.getIn(['schedule', 'end'], Map());
    if (!sessionDate) return { error: true };
    if (start.isEmpty() || end.isEmpty()) return { error: true };
    const startDate = getStartEndDate(start);
    const endDate = getStartEndDate(end);
    if (!isValid(startDate) || !isValid(endDate)) return { error: true };
    if (isSameHour(startDate, endDate) && isSameMinute(startDate, endDate)) return { error: true };
    if (isBefore(endDate, startDate)) return { error: true };
    const excludedTimeRanges = getExcludedTimes(sessionDate);
    let isOverlapping = false;
    excludedTimeRanges.forEach((excluded) => {
      if (isOverlapping) return;
      isOverlapping = areIntervalsOverlapping(
        { start: excluded.get('start'), end: excluded.get('end') },
        { start: startDate, end: endDate }
      );
    });
    return { sessionDate, startDate, endDate, isOverlapping, error: false };
  }

  function getAvailability(mode, updatedModalData, schedule) {
    let data = modalData;
    if (mode === 'update') {
      data = updatedModalData;
    }
    setTimeout(() => {
      const {
        sessionDate,
        startDate,
        endDate,
        isOverlapping,
        error,
      } = checkIfTimeSlotOverlapsExcludedTimes(data);
      if (error) return;
      if (isOverlapping) {
        setData(
          Map({ message: 'Start and End time should not overlap Extracurricular/Break time' })
        );
        return;
      }

      getScheduleAvailability({
        programId: programId,
        institutionCalendarId,
        courseId: course.get('_course_id'),
        term: course.get('term'),
        level: course.get('level_no'),
        sessionId: data.get('sessionFlowId'),
        scheduleId: data.get('scheduleId'),
        startDate: `${sessionDate}T${format(startDate, 'HH')}:${format(startDate, 'mm')}:00.000Z`,
        endDate: `${sessionDate}T${format(endDate, 'HH')}:${format(endDate, 'mm')}:00.000Z`,
        mode: data.get('mode'),
        isReassign: data.get('isReassign', false),
        ...(data.get('mode') === 'update' && {
          callback: handleAddEditSchedule,
          schedule,
        }),
      });
    }, 200);
  }

  function getExcludedTimes(date) {
    const extraCurricularAndBreaks = course.get('extra_curricular_break_timing', List());
    if (extraCurricularAndBreaks.isEmpty()) return List();
    const scheduleDate = new Date(date);
    if (!isValid(scheduleDate)) return List();
    const day = format(scheduleDate, 'EEEE').toLowerCase();
    const filteredData = extraCurricularAndBreaks.filter(
      (e) =>
        e.get('gender') === 'both' &&
        e.get('days', List()).includes(day) &&
        (e.get('type') === 'break' || !e.get('allowCourseCoordinatesToEdit', false))
    );
    const allowedInterval = allowedTimeInterval();
    return filteredData.reduce((acc, e) => {
      const startDate = getStartEndDate(e.get('startTime', Map()));
      const endDate = getStartEndDate(e.get('endTime', Map()));
      if (!isValid(startDate) || !isValid(endDate)) return acc;
      const durationInMinutes = differenceInMinutes(endDate, startDate);
      let each15MinutesOfInterval = List([addMinutes(startDate, 1)]);
      for (let i = allowedInterval; i < durationInMinutes; i = i + allowedInterval) {
        each15MinutesOfInterval = each15MinutesOfInterval.push(addMinutes(startDate, i));
      }
      const end = subMilliseconds(addMinutes(startDate, durationInMinutes), 1);
      each15MinutesOfInterval = each15MinutesOfInterval.push(end);
      return acc.push(
        Map({
          start: startDate,
          end,
          excludedTimes: each15MinutesOfInterval,
        })
      );
    }, List());
  }

  function onModalClose() {
    setAlertConfirmModalData({ show: false });
  }

  function onConfirm({ data, operation }) {
    setAlertConfirmModalData({ show: false });
    switch (operation) {
      case 'delete': {
        return handleDeleteSchedule(data, true);
      }
      case 'cancelDeleteSupportAndEvents': {
        return handleCancelDeleteSupportAndEventsSchedule(data, true);
      }
      case 'cancel': {
        return handleCancelSchedule(data, true);
      }
      case 'detach': {
        return handleDetachSchedule(true);
      }
      default:
        return;
    }
  }

  function setAlertModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    setAlertConfirmModalData({
      show,
      ...(title && { title }),
      ...(titleIcon && { titleIcon }),
      ...(body && { body }),
      ...(variant && { variant }),
      ...(confirmButtonLabel && { confirmButtonLabel }),
      ...(cancelButtonLabel && { cancelButtonLabel }),
      ...(data && { data }),
    });
  }

  function getScheduledCourseGroups() {
    const sessionFlowId = modalData.get('sessionFlowId');
    if (!sessionFlowId) return Map();
    const mode = modalData.get('mode');
    const scheduleId = modalData.get('scheduleId');
    const session = getCourse()
      .get('session_flow', List())
      .find((sf) => sf.get('_id') === sessionFlowId);
    if (!session) return Map();
    const schedule = session
      .get('schedule', List())
      // .filter((s) => s.get('isActive'))
      .toList();
    if (schedule.isEmpty()) return Map();
    const groupedSchedule = schedule.reduce((sAcc, s) => {
      return s.get('student_groups', List()).reduce((sgAcc, sg) => {
        const existingSessionGroupIds = sgAcc.get(sg.get('group_id'), List());
        const sessionGroupIds = Set(
          sg
            .get('session_group', List())
            .map((sessionGroup) => sessionGroup.get('session_group_id'))
        ).toList();
        return sgAcc.set(
          sg.get('group_id'),
          Set(existingSessionGroupIds.concat(sessionGroupIds)).toList()
        );
      }, sAcc);
    }, Map());
    const groupedStudentGroups = scheduleAvailability
      .get('student_group', List())
      .reduce((sgAcc, sg) => {
        const existingSessionGroupIds = sgAcc.get(sg.get('_id'), List());
        const sessionGroupIds = Set(
          sg.get('session_group', List()).map((sessionGroup) => sessionGroup.get('_id'))
        ).toList();
        return sgAcc.set(
          sg.get('_id'),
          Set(existingSessionGroupIds.concat(sessionGroupIds)).toList()
        );
      }, Map());
    let groupedStudentGroupsWithStatus = groupedStudentGroups
      .entrySeq()
      .reduce((acc, [key, value]) => {
        const scheduledDeliveryGroupIds = groupedSchedule.get(key, List());
        const deliveryGroupIdStatus = value.reduce(
          (dgAcc, deliveryGroupId) =>
            dgAcc.set(deliveryGroupId, scheduledDeliveryGroupIds.includes(deliveryGroupId)),
          Map()
        );
        return acc.set(
          key,
          deliveryGroupIdStatus.merge(
            Map({
              disabled: deliveryGroupIdStatus.valueSeq().every((status) => status === true),
              deliveryGroupParentDisabled: deliveryGroupIdStatus
                .valueSeq()
                .some((status) => status === true),
            })
          )
        );
      }, Map());
    if (mode === 'create') {
      return groupedStudentGroupsWithStatus;
    }
    const currentSchedule = schedule.find((s) => s.get('_id') === scheduleId);
    if (!currentSchedule) return groupedStudentGroupsWithStatus;
    currentSchedule
      .get('student_groups', List())
      .reduce((sgAcc, sg) => {
        const existingSessionGroupIds = sgAcc.get(sg.get('group_id'), List());
        const sessionGroupIds = Set(
          sg
            .get('session_group', List())
            .map((sessionGroup) => sessionGroup.get('session_group_id'))
        ).toList();
        return sgAcc.set(
          sg.get('group_id'),
          Set(existingSessionGroupIds.concat(sessionGroupIds)).toList()
        );
      }, Map())
      .entrySeq()
      .forEach(([key, value]) => {
        const deliveryGroupIds = groupedStudentGroupsWithStatus
          .get(key, Map())
          .delete('disabled')
          .delete('deliveryGroupParentDisabled')
          .keySeq()
          .toList();
        const deliveryGroupIdStatus = deliveryGroupIds.reduce(
          (dgAcc, deliveryGroupId) =>
            dgAcc.set(
              deliveryGroupId,
              !value.includes(deliveryGroupId)
                ? groupedStudentGroupsWithStatus.getIn([key, deliveryGroupId])
                : false
            ),
          Map()
        );
        groupedStudentGroupsWithStatus = groupedStudentGroupsWithStatus.set(
          key,
          deliveryGroupIdStatus.merge(
            Map({
              disabled: deliveryGroupIdStatus.valueSeq().every((status) => status === true),
              deliveryGroupParentDisabled: deliveryGroupIdStatus
                .valueSeq()
                .some((status) => status === true),
            })
          )
        );
      });

    return groupedStudentGroupsWithStatus;
  }

  function isAllowedToBreakSessionFlow() {
    const isAllowed = advancedSettings.getIn(['course_setting', 'break_session_flow'], false);
    if (!isAllowed) {
      const sessionFlowId = modalData.get('sessionFlowId');
      const sessionFlowIndex = course
        .get('session_flow', List())
        .findIndex((sessionFlow) => sessionFlow.get('_id') === sessionFlowId);
      if ([-1, 0].includes(sessionFlowIndex)) {
        return true;
      }
      return course
        .get('session_flow', List())
        .slice(0, sessionFlowIndex)
        .map(
          (sessionFlow) =>
            sessionFlow.get('student_group', List()).size > 0 &&
            sessionFlow
              .get('student_group', List())
              .every((sGroup) => sGroup.get('schedule_status'))
        )
        .every((status) => status === true);
    }
    return true;
  }

  function handleBreakSessionFlowModalClose(triggerSchedule = false, topics = []) {
    setShowBreakSessionFlowModal(false);
    if (triggerSchedule) {
      handleSaveSchedule('create', true, topics);
    }
  }

  function handleBreakSessionFlow() {
    saveAdvanceSettings(
      { break_session_flow: true },
      programId,
      course.get('_course_id'),
      course.get('level_no'),
      'schedule',
      handleBreakSessionFlowModalClose
    );
  }

  function hasMergePossible() {
    const scheduleList = mergeModalData.get('scheduleList', List());
    const data = scheduleList
      .reduce((sAcc, s) => {
        return sAcc.push(
          `${s
            .get('student_groups', List())
            .map((studentGroup) => {
              const studentGroupName = getFormattedGroupName(
                studentGroup.get('group_name', ''),
                isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
              );
              const sessionGroupNames = studentGroup
                .get('session_group', List())
                .map(
                  (sessionGroup) =>
                    `${s.getIn(['session', 'delivery_symbol'], '')}${s.getIn(
                      ['session', 'delivery_no'],
                      ''
                    )} - ${studentGroupName} - ${getFormattedGroupName(
                      sessionGroup.get('group_name', ''),
                      3
                    )}`
                )
                .join(', ');
              return `${sessionGroupNames}`;
            })
            .join(', ')}`
        );
      }, List())
      .toJS();
    const preparedData = [];
    data.forEach((item) => {
      const itemData = item.split(', ');
      itemData.forEach((i1) => {
        preparedData.push(i1);
      });
    });
    const result = [];
    preparedData.forEach((item) => {
      const [sessionName, sessionGroup, subGroup] = item.split(' - ');
      const existingSession = result.find((session) => session.sessionName === sessionName);
      if (existingSession) {
        existingSession.sessionGroups.push(`${sessionGroup}${subGroup}`);
        existingSession.topGroups.push(sessionGroup);
      } else {
        result.push({
          sessionName,
          sessionGroups: [`${sessionGroup}${subGroup}`],
          topGroups: [sessionGroup],
        });
      }
    });
    if (result.length === 1) {
      return true;
    } else if (result.length > 1) {
      const sessionNameArray = result.map((element) => element.sessionName.replace(/\d/g, ''));
      const allSessionNameSame = sessionNameArray.every((item) => item === sessionNameArray[0]);
      let valuesToCheck = result.reduce((max, item) => {
        if (item.topGroups.length > max.topGroups.length) {
          return item;
        }
        return max;
      }, result[0])?.[allSessionNameSame ? 'sessionGroups' : 'topGroups'];
      const allValuesExist = valuesToCheck?.every((value) =>
        result.every((session) =>
          allSessionNameSame
            ? session.sessionGroups.includes(value)
            : session.topGroups.includes(value)
        )
      );
      if (allValuesExist) {
        return true;
      } else {
        setData(
          Map({
            message: (
              <span>
                Student Groups of {result[0].sessionName}{' '}
                <b style={{ color: 'red' }}>({valuesToCheck.join(', ')})</b> not matching with other
                sessions.{' '}
              </span>
            ),
          })
        );
        return false;
      }

      // const firstLength = result[0].sessionGroups.length;
      // const allHaveSameLength = result.every(
      //   (session) => session.sessionGroups.length === firstLength
      // );
      // const valuesToCheck = result[0].sessionGroups;
      // if (allHaveSameLength) {
      //   const allValuesExist = valuesToCheck.every((value) =>
      //     result.every((session) => session.sessionGroups.includes(value))
      //   );
      //   if (allValuesExist) {
      //     return true;
      //   } else {
      //     setData(
      //       Map({
      //         message: (
      //           <span>
      //             Student Groups of {result[0].sessionName}{' '}
      //             <b style={{ color: 'red' }}>({valuesToCheck.join(', ')})</b> not matching with
      //             other sessions.{' '}
      //           </span>
      //         ),
      //       })
      //     );
      //     return false;
      //   }
      // } else {
      //   setData(
      //     Map({
      //       message: (
      //         <span>
      //           Student Groups of {result[0].sessionName}{' '}
      //           <b style={{ color: 'red' }}>({valuesToCheck.join(', ')})</b> not matching with other
      //           sessions.{' '}
      //         </span>
      //       ),
      //     })
      //   );
      //   return false;
      // }
    } else {
      return false;
    }
  }

  function handleMergeClick() {
    if (!hasMergePossible()) {
      return;
    }
    setMergeModalData(mergeModalData.set('show', true));
    getMergeSchedule({
      programId: programId,
      institutionCalendarId,
      courseId: course.get('_course_id'),
      term: course.get('term'),
      level: course.get('level_no'),
      scheduleIdList: mergeModalData
        .get('scheduleList', List())
        .map((s) => s.get('_id'))
        .toJS(),
      sessionIdList: Set(
        mergeModalData
          .get('scheduleList', List())
          .map((s) => s.getIn(['session', '_session_id'], ''))
      ).toJS(),
    });
  }

  function cancelMerge() {
    setShowMergeCheckbox(false);
    setShowMissedToComplete(false);
    setShowRevoke(false);
    setMergeModalData(initialMergeModalData);
  }

  function handleMergeScheduleCheckboxChange(checked, schedule) {
    if (checked) {
      setMergeModalData(
        mergeModalData.set(
          'scheduleList',
          mergeModalData.get('scheduleList', List()).push(schedule)
        )
      );
    } else {
      setMergeModalData(
        mergeModalData.set(
          'scheduleList',
          mergeModalData
            .get('scheduleList', List())
            .filter((s) => s.get('_id') !== schedule.get('_id'))
            .toList()
        )
      );
    }
  }

  function handleMissedCheckboxChange(checked, schedule) {
    if (checked) {
      const filteredSchedules = course.get('session_flow', List()).reduce((result, session) => {
        const matchingSchedules = session.get('schedule', List()).filter((item) => {
          return item
            .get('merge_with', List())
            .some((merge) => merge.get('schedule_id', '') === schedule.get('_id', ''));
        });
        if (matchingSchedules.size > 0) {
          result.push({
            schedules: matchingSchedules,
          });
        }
        return result;
      }, []);

      const filteredSchedule = fromJS(filteredSchedules);

      const combineSchedules = filteredSchedule.reduce((acc, element) => {
        return acc.concat(element.get('schedules'));
      }, List());

      setMergeModalData(
        mergeModalData.set(
          'scheduleList',
          schedule.get('merge_status', false)
            ? mergeModalData.get('scheduleList', List()).merge(combineSchedules).push(schedule)
            : mergeModalData.get('scheduleList', List()).push(schedule)
        )
      );
    } else {
      const filteredSchedule = mergeModalData
        .get('scheduleList', List())
        .filter(
          (item) =>
            !item
              .get('merge_with')
              .some((merge) => merge.get('schedule_id') === schedule.get('_id', ''))
        );

      setMergeModalData(
        mergeModalData.set(
          'scheduleList',
          schedule.get('merge_status', false)
            ? filteredSchedule.filter((s) => s.get('_id') !== schedule.get('_id')).toList()
            : mergeModalData
                .get('scheduleList', List())
                .filter((s) => s.get('_id') !== schedule.get('_id'))
                .toList()
        )
      );
    }
  }

  function updateExistingSubjectsForMerge(sessionFlow, updatedModalData) {
    const existingSubjectIds = updatedModalData
      .getIn(['existingSchedule', 'subjects'], List())
      .map((subject) => subject.get('_subject_id'));

    const subjectIdList = [];
    const subjects = sessionFlow
      .reduce((sAcc, s) => sAcc.concat(s.get('subjects', List())), List())
      .filter((s) => {
        const subjectId = s.get('_subject_id');
        if (subjectIdList.includes(subjectId)) return false;
        subjectIdList.push(subjectId);
        return true;
      });

    setMergeModalData(
      updatedModalData.setIn(
        ['schedule', 'subjects'],
        subjects
          .filter((subject) => existingSubjectIds.includes(subject.get('_subject_id')))
          .toList()
      )
    );
  }
  // handleEditMergeSchedule
  function handleEditMergeSchedule(existingSchedule, sessionFlow) {
    if (existingSchedule === undefined || existingSchedule.isEmpty()) return;
    const schedule = Map({
      schedule_date: existingSchedule.get('schedule_date', ''),
      start: existingSchedule.get('start', Map()),
      end: existingSchedule.get('end', Map()),
      mode: existingSchedule.get('mode'),
      subjects: List(),
      // staffs: existingSchedule.get('staffs', List()).map((staff) =>
      //   Map({
      //     _id: staff.get('_staff_id'),
      //     _staff_id: staff.get('_staff_id'),
      //     name: staff.get('staff_name'),
      //     staff_name: staff.get('staff_name'),
      //   })
      // ),
      [`${
        existingSchedule.get('outsideCampus', false) ? 'checkStaff' : 'staffs'
      }`]: existingSchedule.get('staffs', List()).map((staff) =>
        Map({
          _id: staff.get('_staff_id'),
          _staff_id: staff.get('_staff_id'),
          name: staff.get('staff_name'),
          staff_name: staff.get('staff_name'),
        })
      ),
      ...(existingSchedule.get('outsideCampus', false) && {
        checkclassLeaderData: existingSchedule.get('classLeaders', List()).map((staff) =>
          Map({
            _staff_id: staff,
          })
        ),
      }),
      ...(existingSchedule.get('outsideCampus', false) && {
        selfAttendance: existingSchedule.get('selfAttendance', false),
      }),
      ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
        outsideCampus: existingSchedule.get('outsideCampus', false),
      }),
      _infra_id: existingSchedule.get('_infra_id', ''),
      infra_name: existingSchedule.get('infra_name', ''),
      remotePlatform: getRemotePlatform(existingSchedule),
      ...(existingSchedule.get('attendanceTakingStaff', List()).size > 0 && {
        manualStaffs: existingSchedule
          .get('attendanceTakingStaff', List())
          .map((item) => item.get('staffId', '')),
      }),
    });
    const scheduleList = existingSchedule
      .get('merge_with', List())
      .concat(
        List([
          Map({
            schedule_id: existingSchedule.get('_id'),
            session_id: existingSchedule.getIn(['session', '_session_id'], ''),
          }),
        ])
      )
      .map((m) => Map({ _id: m.get('schedule_id'), _session_id: m.get('session_id') }));
    const updatedModalData = modalData.merge(
      Map({
        show: true,
        mode: 'update',
        scheduleId: existingSchedule.get('_id'),
        schedule,
        existingSchedule,
        scheduleList,
        sessionFlowId: sessionFlow.get('_id'),
      })
    );
    setMergeModalData(updatedModalData);
    getMergeSchedule({
      programId: programId,
      institutionCalendarId,
      courseId: course.get('_course_id'),
      term: course.get('term'),
      level: course.get('level_no'),
      scheduleIdList: scheduleList.map((s) => s.get('_id')).toJS(),
      // idlist
      sessionIdList: Set(scheduleList.map((s) => s.get('_session_id'))).toJS(),
      callback: (sessionFlow) => {
        updateExistingSubjectsForMerge(sessionFlow, updatedModalData);
      },
    });
  }

  function handleMergeSessionDataChange(name, value) {
    let schedule = mergeModalData.get('schedule', Map());
    if (name === 'schedule_date') {
      schedule = initialMergeModalData.get('schedule', Map()).set('schedule_date', value);
    } else if (['start', 'end'].includes(name)) {
      schedule = schedule.merge(
        Map({
          ...(name === 'start' && { start: value, end: Map() }),
          ...(name === 'end' && { end: value }),
          _infra_id: '',
          infra_name: '',
          remotePlatform: '',
        })
      );
    } else if (name === 'mode') {
      if (value === 'remote') schedule = schedule.set('manualStaffs', fromJS([]));
      schedule = schedule.merge(
        Map({ mode: value, _infra_id: '', infra_name: '', remotePlatform: '' })
      );
    } else if (name === 'infra') {
      schedule = schedule.merge(
        Map({
          _infra_id: value.get('value', ''),
          infra_name: value.get('name', ''),
          remotePlatform: value.get('remotePlatform', ''),
          ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
            outsideCampus: value.get('outsideCampus', false),
          }),
        })
      );
    } else if (name === 'manualStaffs') {
      schedule = schedule.set(name, fromJS(value));
    } else {
      schedule = schedule.set(name, value);
    }
    setMergeModalData(mergeModalData.set('schedule', schedule));
  }

  function handleSaveMergeSchedule(mode) {
    const scheduleData = mergeModalData.get('schedule', Map());
    const valueToCheckMerge = scheduleData.get('mobileNumberOrEmail', '');
    const checkedValueMerge = identifyValueSchedule(valueToCheckMerge, countryCodeLength);
    if (scheduleData.isEmpty()) return;
    const requestBody = {
      ...(manualStaffOptions.get('manualAttendance', false) && {
        attendanceTakingStaff: manualStaffObject(scheduleData)
          .map((item) => item.delete('_id'))
          .toJS(),
      }),
      ...(mode === 'create' && {
        merge_ids: mergeModalData
          .get('scheduleList', List())
          .map((s) =>
            Map({ schedule_id: s.get('_id'), session_id: s.getIn(['session', '_session_id'], '') })
          )
          .toJS(),
      }),
      ...(mode === 'update' && {
        ids: mergeModalData.get('scheduleList', List()).map((s) => s.get('_id')),
      }),
      _institution_calendar_id: institutionCalendarId,
      schedule_date: moment(scheduleData.get('schedule_date')).format('YYYY-MM-DD'),
      start: scheduleData.get('start').toJS(),
      end: scheduleData.get('end').toJS(),
      scheduleStartDateAndTime: transformDateTimeToUTC(
        scheduleData.get('schedule_date'),
        scheduleData.getIn(['start', 'hour']),
        scheduleData.getIn(['start', 'minute']),
        scheduleData.getIn(['start', 'format'])
      ),
      scheduleEndDateAndTime: transformDateTimeToUTC(
        scheduleData.get('schedule_date'),
        scheduleData.getIn(['end', 'hour']),
        scheduleData.getIn(['end', 'minute']),
        scheduleData.getIn(['end', 'format'])
      ),
      mode: scheduleData.get('mode'),
      subjects: scheduleData
        .get('subjects', List())
        .map((subject) =>
          Map({
            _subject_id: subject.get('_subject_id'),
            subject_name: subject.get('subject_name'),
          })
        )
        .toJS(),
      ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') === true
        ? {
            staffs: scheduleData
              .get(scheduleData.get('outsideCampus', false) ? 'checkStaff' : 'staffs', List())
              .map((staff) =>
                Map({ staff_name: staff.get('staff_name'), _staff_id: staff.get('_staff_id') })
              )
              .toJS(),
          }
        : {
            staffs: scheduleData
              .get('staffs', List())
              .map((staff) =>
                Map({ staff_name: staff.get('staff_name'), _staff_id: staff.get('_staff_id') })
              )
              .toJS(),
          }),
      _infra_id: scheduleData.get('_infra_id'),
      infra_name: scheduleData.get('infra_name'),
      isActive: true,
      ...(scheduleData.get('mode') === 'remote' && scheduleData.get('remotePlatform') !== null
        ? {
            remotePlatform: scheduleData.get('remotePlatform'),
          }
        : { remotePlatform: 'zoom' }),
      ...(isModuleEnabled('OUTSIDE_CAMPUS_V2') && {
        outsideCampus: scheduleData.get('outsideCampus', false),
        ...(scheduleData.get('outsideCampus', false) === true && {
          classLeaders: scheduleData
            .get('checkclassLeaderData', List())
            .map((item) => item.get('_staff_id', '')),
          externalStaffs: checkUpdateExternalListMergeModal.map((item) => item.get('_id', '')),
          selfAttendance: scheduleData.get('selfAttendance', false),
        }),
      }),
      ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
        outsideCampus: scheduleData.get('outsideCampus', false),
        ...(scheduleData.get('outsideCampus', false) === true && {
          campusDetails: {
            mobile:
              checkedValueMerge === 'MobileNumber'
                ? scheduleData.get('mobileNumberOrEmail', '')
                : '',
            email: checkedValueMerge === 'Gmail' ? scheduleData.get('mobileNumberOrEmail', '') : '',
          },
        }),
      }),
    };
    const validationErrorMsg = validateSchedule(requestBody, true, false, false, programId);
    if (validationErrorMsg) {
      setData(Map({ message: validationErrorMsg }));
      return;
    }
    const { isOverlapping, error } = checkIfTimeSlotOverlapsExcludedTimes(mergeModalData);
    if (error) return;
    if (isOverlapping) {
      setData(Map({ message: 'Start and End time should not overlap Extracurricular/Break time' }));
      return;
    }
    saveMergeSchedule({
      mode,
      _institution_calendar_id: institutionCalendarId,
      _program_id: programId,
      _course_id: course.get('_course_id'),
      term: course.get('term'),
      level_no: course.get('level_no'),
      ...(mode === 'update' && {
        _id: mergeModalData.get('scheduleId'),
      }),
      requestBody,
      callback: handleCloseModal,
      errCallback: setMergeScheduleErrors,
    });
    setMergeScheduleErrors(List());
  }
  // handleEditMergeSchedule
  function openScheduleModal(sessionId, sessionFlow) {
    const session = sessionFlow.find((s) => s.get('_id') === sessionId);
    if (!session) return;
    handleAddEditSchedule(session, 'create');
  }

  function handleDetachSchedule(isConfirmed) {
    if (!isConfirmed) {
      setAlertModalData({
        show: true,
        title: 'Detach Sessions',
        body: (
          <div>
            Are you sure you want to detach all the merged sessions? <br />
            <span className="text-red">
              Note : You should reschedule the detached sessions once again.
            </span>
          </div>
        ),
        variant: 'confirm',
        data: { data: {}, operation: 'detach' },
        confirmButtonLabel: 'DETACH',
        cancelButtonLabel: 'CANCEL',
      });
      return;
    }
    saveMergeSchedule({
      mode: 'delete',
      _id: mergeModalData.get('scheduleId'),
      page: getActiveSessionFlowPage(mergeModalData.get('sessionFlowId')),
      callback: handleCloseModal,
      openScheduleModal: openScheduleModal,
      _institution_calendar_id: institutionCalendarId,
      _program_id: programId,
      _course_id: course.get('_course_id'),
      term: course.get('term'),
      level_no: course.get('level_no'),
      _session_id: mergeModalData.get('sessionFlowId'),
    });
  }

  function handleStudentGroupModalClose() {
    setStudentGroupModalData(initialStudentGroupModalData);
    setState(initialState);
    setSearchValue('');
  }

  function handleStudentGroupModalOpen(
    eventName,
    { readOnly = false, session = Map(), mode = 'create' }
  ) {
    setAnchorEl(null);

    const callBack = (data) => {
      if (mode === 'create') {
        const uniqueProgramNames = fromJS(data.masterGroup);

        const defaultState = Map({
          group: uniqueProgramNames.getIn([0, 'group_name'], ''),
          deliveryType: uniqueProgramNames.getIn([0, 'delivery_symbol'], ''),
          groupNo: uniqueProgramNames.getIn([0, 'session_group', 0, 'group_name'], ''),
          studentIds: uniqueProgramNames.getIn([0, 'session_group', 0, '_student_ids'], List()),
          rotation_count: uniqueProgramNames.getIn([0, 'rotation_count'], ''),
          selectAll: Map(),
          selectedStudents: List(),
        });

        setState(defaultState);
      } else {
        const group = session.getIn(['student_groups', 0, 'group_name'], '');
        const deliveryType = session.getIn(['student_groups', 0, 'delivery_symbol'], '');
        const sessionGroup = session.getIn(
          ['student_groups', 0, 'session_group', 0, 'group_name'],
          ''
        );
        const findMatchingObject = (studentBasedDeliveryTypes) => {
          const matchingObject = studentBasedDeliveryTypes
            .get('masterGroup', List())
            .find((groupObject) => {
              return (
                groupObject.get('group_name', '') === group &&
                groupObject.get('delivery_symbol', '') === deliveryType
              );
            });

          const matchingSessionGroup = matchingObject
            .get('session_group', List())
            .find((sessionGroups) => {
              return sessionGroups.get('group_name', '') === sessionGroup;
            });
          return matchingSessionGroup.get('_student_ids', List());
        };

        const matchingObject = findMatchingObject(fromJS(data));

        let array = List();
        session.get('student_groups', List()).forEach((data2Element) => {
          data2Element.get('students', List()).forEach((studentElement) => {
            const matchingObject = fromJS(data)
              .get('masterGroup', List())
              .find((groupObject) => {
                return (
                  groupObject.get('group_name', '') === data2Element.get('group_name', '') &&
                  groupObject.get('delivery_symbol', '') === data2Element.get('delivery_symbol', '')
                );
              });

            const matchingSessionGroup = matchingObject
              .get('session_group', List())
              .find((sessionGroups) => {
                return (
                  sessionGroups.get('group_name', '') ===
                  data2Element.getIn(['session_group', 0, 'group_name'], '')
                );
              });

            const data2ElementWithoutStudents = Map(data2Element).delete('students');
            const studentMap = Map(studentElement);
            array = array.push(
              studentMap.merge(
                data2ElementWithoutStudents
                  .set('group', data2ElementWithoutStudents.get('group_name', ''))
                  .set('deliveryType', data2ElementWithoutStudents.get('delivery_symbol', ''))
                  .set(
                    'groupNo',
                    data2ElementWithoutStudents.getIn(['session_group', 0, 'group_name'], '')
                  )
                  .set('user_id', studentMap.get('academic_no', ''))
                  .set('studentIds', matchingSessionGroup.get('_student_ids', List()))
                  .set('rotation_count', session.get('rotation_count', ''))
              )
            );
          });
        });

        const detail = session.get('student_groups', List()).reduce((result, group) => {
          const groupName = group.get('group_name');
          const deliverySymbol = group.get('delivery_symbol');
          const sessionGroup = group.getIn(['session_group', 0, 'group_name'], '');
          return result.set(
            groupName + deliverySymbol + sessionGroup,
            group.get('selected_students') === group.get('total_students')
          );
        }, Map());

        setState(
          state
            .set('group', session.getIn(['student_groups', 0, 'group_name'], ''))
            .set('deliveryType', session.getIn(['student_groups', 0, 'delivery_symbol'], ''))
            .set(
              'groupNo',
              session.getIn(['student_groups', 0, 'session_group', 0, 'group_name'], '')
            )
            .set('rotation_count', session.get('rotation_count', ''))
            .set('studentIds', matchingObject)
            .set('selectedStudents', array)
            .set('selectAll', detail)
        );
      }
    };

    dispatch(
      getStudentGroupBasedDeliveryTypes({
        _program_id: programId,
        _institution_calendar_id: institutionCalendarId,
        _course_id: course.get('_course_id'),
        term: course.get('term'),
        level_no: course.get('level_no'),
        callBack,
      })
    );

    const studentGroups = session.get('student_groups', List());

    setStudentGroupModalData(
      initialStudentGroupModalData.merge(
        Map({
          show: true,
          mode,
          type: eventName,
          readOnly,
          ...(isRotation && {
            groupNo: mode === 'create' ? null : studentGroups.getIn([0, 'group_no']),
          }),
          ...((readOnly || mode === 'update') && {
            sessionId: session.get('_id'),
            data: Map({
              session: session,
              title: session.get('title', ''),
              studentGroups: session.get('student_groups', List()).map((sGroup) => {
                sGroup = sGroup.delete('_id').delete('session_group');
                sGroup = sGroup.set(
                  'students',
                  sGroup
                    .get('students', List())
                    .map((student) => student.delete('_id'))
                    .toList()
                );
                return sGroup;
              }),
            }),
          }),
        })
      )
    );
  }

  function handleStudentGroupDataChange(name, value) {
    if (name === 'activeStudentGroupId') {
      return setStudentGroupModalData(studentGroupModalData.set(name, value));
    }
    let data = studentGroupModalData.setIn(['data', name], value);
    if (name === 'studentGroups') {
      if (isRotation) {
        const rotationCount = data.get('groupNo');
        if (value.size === 0) {
          data = data.set('groupNo', null);
        } else if (rotationCount === null) {
          const currentRotationCount = value.getIn([0, 'group_no']);
          if (currentRotationCount) {
            data = data.set('groupNo', currentRotationCount);
          }
        }
      }
    }
    return setStudentGroupModalData(data);
  }

  function handleSaveStudentGroup() {
    const type = studentGroupModalData.get('type');

    function convertToDetails2Immutable(details1) {
      const details1Immutable = List(details1);

      const details2Immutable = details1Immutable.map((group) => {
        const selGroupId = studentBasedDeliveryTypes
          .get('masterGroup', List())
          .find((grpId) => grpId.get('group_name', '') === group.get('group'));
        const transformedGroup = Map({
          gender: selGroupId?.get('gender'),
          state: group.get('state'),
          studentIds: group.get('studentIds'),
          group_id: selGroupId?.get('_id'),
          group_name: group.get('group'),
          delivery_symbol: group.get('deliveryType'),
          group_no: Number(getFormattedGroupName(group.get('group'), 1).replace(/\D/g, '')),
          session_group: fromJS([
            {
              session_group_id: selGroupId?.get('_id'),
              group_no: getFormattedGroupName(group.get('groupNo'), 1).replace(/\D/g, ''),
              group_name: group.get('groupNo', ''),
            },
          ]),
        });

        const filteredStudentData = state
          .get('selectedStudents', List())
          .filter(
            (s) =>
              s.get('groupNo', '') === group.get('groupNo', '') &&
              s.get('group', '') === group.get('group', '') &&
              s.get('deliveryType', '') === group.get('deliveryType', '')
          )
          .filter((item) => {
            const ids = group.get('studentIds');
            return ids.includes(item.get('_student_id', ''));
          })
          .map((stu) => {
            return {
              academic_no: stu.get('user_id', ''),
              name: stu.get('name', ''),
              gender: stu.get('gender', ''),
              _student_id: stu.get('_student_id', ''),
            };
          });

        const studentsList = uniqWith(filteredStudentData.toJS(), (obj1, obj2) => {
          return obj1._student_id === obj2._student_id;
        });

        return transformedGroup.set('students', fromJS(studentsList));
      });

      return details2Immutable; // Convert back to plain JavaScript
    }
    const convertedDetails2Immutable = convertToDetails2Immutable(
      state.get('selectedStudents', List())
    );

    const hasDuplicate = (list, obj) => {
      return list.some((item) => {
        return (
          item.getIn(['session_group', 0, 'group_name'], '') ===
            obj.getIn(['session_group', 0, 'group_name'], '') &&
          (item.get('group') === obj.get('group_name') ||
            item.get('group_name') === obj.get('group_name')) &&
          (item.get('deliveryType') === obj.get('delivery_symbol') ||
            item.get('delivery_symbol') === obj.get('delivery_symbol'))
        );
      });
    };

    const filteredDetail = fromJS(convertedDetails2Immutable).filter(
      (item, index, list) => !hasDuplicate(list.slice(0, index), item)
    );

    const studentGroupData = filteredDetail.map((item) => {
      const totalStudents = Set(
        studentBasedDeliveryTypes.get('sgStudentList', List()).filter((item1) => {
          const ids = item.get('studentIds', List());
          return ids.includes(item1.get('_student_id', ''));
        })
      ).toList();
      return (
        item
          .set('selected_students', item.get('students', List()).size)
          .set('total_students', totalStudents.size)
          // .set('group_no', getFormattedGroupName(item.get('group_no'), 1).replace(/\D/g, ''))
          .delete('studentIds')
          .delete('state')
          .delete('rotation_count')
      );
    });

    const requestBody = {
      _institution_calendar_id: institutionCalendarId,
      _program_id: programId,
      program_name: programName,
      _course_id: course.get('_course_id'),
      course_name: course.get('courses_name'),
      course_code: course.get('courses_number'),
      term: course.get('term'),
      year_no: course.get('year_no'),
      level_no: course.get('level_no'),
      title: studentGroupModalData.getIn(['data', 'title'], '').trim(),
      type: type === 'support' ? 'support_session' : 'event',
      ...(type !== 'support' && { sub_type: type }),
      student_groups: studentGroupData,
      rotation: isRotation ? 'yes' : 'no',
      ...(isRotation && { rotation_count: state.get('rotation_count', '') }),
    };

    const messages = [];
    if (!requestBody.title) {
      messages.push(t('course_schedule.validation.title_is_required'));
    }
    if (!requestBody.student_groups.size) {
      messages.push(t('course_schedule.validation.at_least_one_student_is_required'));
    }
    if (messages.length) {
      return setData(Map({ message: messages[0] }));
    }

    saveStudentGroupsWithStudents({
      type: type === 'support' ? 'support_session' : 'event',
      mode: studentGroupModalData.get('mode'),
      _id: studentGroupModalData.get('sessionId'),
      requestBody,
      callback: handleStudentGroupModalClose,
    });
  }

  function handleAddEditSupportAndEvents({
    schedule,
    mode,
    isReassign = false,
    defaultDate = { start: Map(), end: Map(), schedule_date: '' },
  }) {
    setSupportAndEventsModalData(
      initialSupportAndEventsModalData.merge(
        Map({
          show: true,
          mode,
          existingSchedule: schedule,
          isReassign,
          schedule: Map({
            type: getActiveViewType(),
            sub_type: schedule.get('sub_type', '') || '',
            schedule_date:
              schedule.get('schedule_date', '') ||
              (mode === 'create' && defaultDate.schedule_date ? defaultDate.schedule_date : ''),
            start:
              schedule.get('start', '') ||
              (mode === 'create' && !defaultDate.start.isEmpty() ? defaultDate.start : Map()),
            end:
              schedule.get('end', '') ||
              (mode === 'create' && !defaultDate.end.isEmpty() ? defaultDate.end : Map()),
            mode: schedule.get('mode', '') || '',
            subjects: getExistingSubjects(course, schedule),
            staffs: schedule.get('staffs', List()).map((staff) =>
              Map({
                _id: staff.get('_staff_id'),
                _staff_id: staff.get('_staff_id'),
                name: staff.get('staff_name'),
                staff_name: staff.get('staff_name'),
              })
            ),
            _infra_id: schedule.get('_infra_id', '') || '',
            infra_name: schedule.get('infra_name', '') || '',
            remotePlatform: getRemotePlatform(schedule),
            ...(schedule.get('attendanceTakingStaff', List()).size > 0 && {
              manualStaffs: schedule
                .get('attendanceTakingStaff', List())
                .map((item) => item.get('staffId', '')),
            }),
          }),
        })
      )
    );
    setDeleteData(Map({ session: Map(), schedule: schedule }));
  }

  function handleSupportAndEventDataChange(name, value) {
    let schedule = supportAndEventsModalData.get('schedule', Map());
    if (name === 'schedule_date') {
      schedule = supportAndEventsModalData.get('schedule', Map()).set('schedule_date', value);
    } else if (['start', 'end'].includes(name)) {
      schedule = schedule.merge(
        Map({
          ...(name === 'start' && { start: value, end: Map() }),
          ...(name === 'end' && { end: value }),
          _infra_id: '',
          infra_name: '',
          remotePlatform: '',
        })
      );
    } else if (name === 'mode') {
      if (value === 'remote') schedule = schedule.set('manualStaffs', fromJS([]));
      schedule = schedule.merge(
        Map({ mode: value, _infra_id: '', infra_name: '', remotePlatform: '' })
      );
    } else if (name === 'infra') {
      schedule = schedule.merge(
        Map({
          _infra_id: value.get('value', ''),
          infra_name: value.get('name', ''),
          remotePlatform: value.get('remotePlatform', ''),
          ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
            outsideCampus: value.get('outsideCampus', false),
          }),
        })
      );
    } else if (name === 'manualStaffs') {
      schedule = schedule.set(name, fromJS(value));
    } else {
      schedule = schedule.set(name, value);
    }
    setSupportAndEventsModalData(supportAndEventsModalData.set('schedule', schedule));
  }

  function handleSaveSupportAndEventsSchedule(mode) {
    const scheduleData = supportAndEventsModalData.get('schedule', Map());
    // const valueToCheckSchedule = scheduleData.get('mobileNumberOrEmail', '');
    // const checkedValueSchedule = identifyValueSchedule(valueToCheckSchedule, countryCodeLength);

    if (scheduleData.isEmpty()) return;
    const requestBody = {
      ...(manualStaffOptions.get('manualAttendance', false) && {
        attendanceTakingStaff: manualStaffObject(scheduleData)
          .map((item) => item.delete('_id'))
          .toJS(),
      }),
      _institution_calendar_id: institutionCalendarId,
      sub_type: scheduleData.get('sub_type'),
      schedule_date: moment(scheduleData.get('schedule_date')).format('YYYY-MM-DD'),
      start: scheduleData.get('start').toJS(),
      end: scheduleData.get('end').toJS(),
      scheduleStartDateAndTime: transformDateTimeToUTC(
        scheduleData.get('schedule_date'),
        scheduleData.getIn(['start', 'hour']),
        scheduleData.getIn(['start', 'minute']),
        scheduleData.getIn(['start', 'format'])
      ),
      scheduleEndDateAndTime: transformDateTimeToUTC(
        scheduleData.get('schedule_date'),
        scheduleData.getIn(['end', 'hour']),
        scheduleData.getIn(['end', 'minute']),
        scheduleData.getIn(['end', 'format'])
      ),
      mode: scheduleData.get('mode'),
      subjects: scheduleData
        .get('subjects', List())
        .map((subject) =>
          Map({
            _subject_id: subject.get('_subject_id'),
            subject_name: subject.get('subject_name'),
          })
        )
        .toJS(),
      staffs: scheduleData
        .get('staffs', List())
        .map((staff) =>
          Map({ staff_name: staff.get('staff_name'), _staff_id: staff.get('_staff_id') })
        )
        .toJS(),
      _infra_id: scheduleData.get('_infra_id'),
      infra_name: scheduleData.get('infra_name'),
      isActive: true,
      ...(scheduleData.get('mode') === 'remote' && scheduleData.get('remotePlatform') !== null
        ? {
            remotePlatform: scheduleData.get('remotePlatform'),
          }
        : { remotePlatform: 'zoom' }),
      // ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
      //   outsideCampus: scheduleData.get('outsideCampus', false),
      //   ...(scheduleData.get('outsideCampus', false) === true && {
      //     campusDetails: {
      //       mobile:
      //         checkedValueSchedule === 'MobileNumber'
      //           ? scheduleData.get('mobileNumberOrEmail', '')
      //           : '',
      //       email:
      //         checkedValueSchedule === 'Gmail' ? scheduleData.get('mobileNumberOrEmail', '') : '',
      //     },
      //   }),
      // }),
    };
    const validationErrorMsg = validateSchedule(requestBody, true, false, true, programId);
    if (validationErrorMsg) {
      setData(Map({ message: validationErrorMsg }));
      return;
    }
    const { isOverlapping, error } = checkIfTimeSlotOverlapsExcludedTimes(
      supportAndEventsModalData
    );
    if (error) return;
    if (isOverlapping) {
      setData(Map({ message: 'Start and End time should not overlap Extracurricular/Break time' }));
      return;
    }
    const scheduleId = supportAndEventsModalData.getIn(['existingSchedule', '_id']);
    saveSupportAndEventsSchedule({
      mode,
      type: getActiveViewType(),
      _institution_calendar_id: institutionCalendarId,
      _program_id: programId,
      _course_id: course.get('_course_id'),
      term: course.get('term'),
      level_no: course.get('level_no'),
      _id: scheduleId,
      isReassign: supportAndEventsModalData.get('isReassign', false),
      requestBody,
      page: getActiveSessionFlowPage(scheduleId),
      callback: handleCloseModal,
      errCallback: setSupportAndEventsScheduleErrors,
    });
    setSupportAndEventsScheduleErrors(List());
  }

  function handleCancelDeleteSupportAndEventsSchedule({ schedule, mode }, isConfirmed) {
    if (!isConfirmed) {
      setAlertModalData({
        show: true,
        title: `${capitalize(mode)} Schedule`,
        body: <div>Are you sure you want to {mode} the selected schedule?</div>,
        variant: 'confirm',
        data: { data: { schedule, mode }, operation: 'cancelDeleteSupportAndEvents' },
        confirmButtonLabel: mode === 'delete' ? 'DELETE' : 'CONFIRM',
        cancelButtonLabel: 'CANCEL',
      });
      return;
    }

    saveSupportAndEventsSchedule({
      mode,
      type: getActiveViewType(),
      _id: schedule.get('_id'),
      _institution_calendar_id: institutionCalendarId,
      _program_id: programId,
      _course_id: course.get('_course_id'),
      term: course.get('term'),
      level_no: course.get('level_no'),
      ...(mode !== 'delete'
        ? { page: getActiveSessionFlowPage(schedule.get('_id')) }
        : { requestBody: { isActive: false } }),
      callback: handleCloseModal,
    });
  }

  function getSupportSessionTypes() {
    return course
      .get('session_delivery_type', List())
      .map((dt) => Map({ name: dt, value: dt }))
      .toJS();
  }

  const handleSave = ({ status, isRevoke = false }) => {
    const requestData = {
      schedule_id: mergeModalData
        .get('scheduleList', List())
        .map((s) => s.get('_id'))
        .toJS(),
      staff_id: staffId,
      ...(isRevoke === false && { attendanceStatus: status }),
      isRevoke: isRevoke,
    };

    const callBack = () => {
      fetchCourseSchedule({
        isRefresh: true,
      });
      setMissedToCompleteOpen(false);
      setRevokeOpen(false);
      setMergeModalData(mergeModalData.set('scheduleList', List()));
      setShowRevoke(false);
      setShowMissedToComplete(false);
    };
    updateMissedSession(requestData, callBack);
  };

  const handleSelectAll = () => {
    const scheduleData = course.get('session_flow', List()).map((sessionFlow) =>
      sessionFlow
        .get('schedule', List())
        .filter((item) =>
          showRevoke ? item.get('isMissedToComplete', false) : item.get('status') === 'missed'
        )
        .map((schedule) => {
          return schedule;
        })
    );
    const flatList = scheduleData.flatMap((entry) => entry);
    setMergeModalData(mergeModalData.set('scheduleList', flatList));
  };

  const checkMissedToComplete =
    course.get('isMissedToSchedule', false) &&
    isMissedSessionModuleEnabled() &&
    CheckPermission(
      'pages',
      'Session Missed To Completed Status',
      'Manage Course Advance Settings',
      'Edit Advance Setting Missed To Complete'
    );

  const filteredCourse = getCourse();
  const courseId = course.get('_course_id');
  const scheduleId = modalData.get('scheduleId', '');
  const mergeScheduleId = mergeModalData.get('scheduleId', '');
  return (
    <div className="pt-4 pb-4">
      <div className="bg-white border-radious-8 pb-5">
        <div className="p-4">
          <div className="row">
            <div
              className={`${
                showMergeCheckbox === true || showMissedToComplete === true
                  ? `col-md-12 col-xl-9 col-12 col-lg-9 pb-2`
                  : `col-md-11 col-xl-9 col-11 col-lg-9 pb-2`
              }`}
            >
              <Filters
                data={course}
                filter={filter}
                handleFilterChange={setFilter}
                triggerActiveType={changeActiveType}
                supportSessionTypes={getSupportSessionTypes()}
                checkMissedToComplete={checkMissedToComplete}
              />
            </div>
            <div
              className={`${
                showMergeCheckbox === true || showMissedToComplete === true
                  ? `col-md-12 col-xl-3 col-12 col-lg-3 d-flex justify-content-xl-end justify-content-lg-end justify-content-md-center align-items-center`
                  : `col-md-1 col-xl-3 col-1 col-lg-3 d-flex  justify-content-end align-items-center`
              }`}
            >
              <ThemeProvider theme={MUI_THEME}>
                {showMergeCheckbox && (
                  <div className="d-flex mr-2">
                    <div className="mr-2">
                      <Button variant="outlined" color="primary" size="small" onClick={cancelMerge}>
                        <Trans i18nKey={'cancel_upper'}></Trans>
                      </Button>
                    </div>
                    <div>
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        className="text-uppercase"
                        disabled={mergeModalData.get('scheduleList', List()).size < 2}
                        onClick={handleMergeClick}
                      >
                        <Trans i18nKey={'merge'}></Trans>
                      </Button>
                    </div>
                  </div>
                )}

                {(showMissedToComplete || showRevoke) && (
                  <div className="d-flex mr-2">
                    <div className="mr-2">
                      <Button
                        variant="outlined"
                        color="primary"
                        size="small"
                        onClick={() => {
                          setShowRevoke(false);
                          setShowMissedToComplete(false);
                          setMergeModalData(mergeModalData.set('scheduleList', List()));
                        }}
                      >
                        <Trans i18nKey={'cancel_upper'}></Trans>
                      </Button>
                    </div>
                    <div className="mr-2">
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        className="text-uppercase text-nowrap"
                        onClick={() => {
                          mergeModalData.get('scheduleList', List()).size === 0
                            ? handleSelectAll()
                            : setMergeModalData(mergeModalData.set('scheduleList', List()));
                        }}
                      >
                        {mergeModalData.get('scheduleList', List()).size === 0
                          ? 'Select All'
                          : 'Unselect All'}
                      </Button>
                    </div>
                    {showRevoke && (
                      <div>
                        <Button
                          variant="contained"
                          color="primary"
                          size="small"
                          className="text-uppercase text-nowrap"
                          disabled={mergeModalData.get('scheduleList', List()).size <= 0}
                          onClick={() => {
                            setRevokeOpen(true);
                          }}
                        >
                          Revoke
                        </Button>
                      </div>
                    )}
                    {showMissedToComplete && (
                      <div>
                        <Button
                          variant="contained"
                          color="primary"
                          size="small"
                          className="text-uppercase text-nowrap"
                          disabled={mergeModalData.get('scheduleList', List()).size <= 0}
                          onClick={() => {
                            setMissedToCompleteOpen(true);
                          }}
                        >
                          Missed To Complete
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {currentCalendar &&
                  isAllowedToSchedule &&
                  ['support', 'events'].includes(filter.get('view')) && (
                    <>
                      {CheckPermission(
                        'subTabs',
                        'Schedule Management',
                        'Course Scheduling',
                        '',
                        'Schedule',
                        '',
                        'Course Schedule',
                        'Add'
                      ) && (
                        <div>
                          <Button
                            ref={addEventsAndSupportSessionRef}
                            variant="contained"
                            color="primary"
                            size="small"
                            className="text-uppercase"
                            classes={{ label: 'align-items-baseline' }}
                            {...(filter.get('view') === 'events' && {
                              endIcon: <i className="fa fa-sort-down"></i>,
                            })}
                            onClick={(event) => {
                              if (filter.get('view') === 'events') {
                                setAnchorEl(event.currentTarget);
                              } else {
                                handleStudentGroupModalOpen('support', {
                                  readOnly: false,
                                  mode: 'create',
                                });
                              }
                            }}
                          >
                            <Trans i18nKey={'add'}></Trans>
                          </Button>
                          <Menu
                            open={Boolean(anchorEl)}
                            anchorEl={anchorEl}
                            onClose={() => setAnchorEl(null)}
                            getContentAnchorEl={null}
                            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
                            transformOrigin={{ vertical: 'top', horizontal: 'center' }}
                          >
                            {['general', 'exam', 'training'].map((event) => (
                              <MenuItem
                                key={event}
                                value={event}
                                onClick={() =>
                                  handleStudentGroupModalOpen(event, {
                                    readOnly: false,
                                    mode: 'create',
                                  })
                                }
                              >
                                {capitalize(t(event))}
                              </MenuItem>
                            ))}
                          </Menu>
                        </div>
                      )}
                    </>
                  )}
              </ThemeProvider>
              {filteredCourse.get('session_flow', List()).size > 0 &&
                currentCalendar &&
                isAllowedToSchedule &&
                filter.get('view') === 'regular' &&
                CheckPermission(
                  'subTabs',
                  'Schedule Management',
                  'Course Scheduling',
                  '',
                  'Schedule',
                  '',
                  'Course Schedule',
                  'Merge'
                ) && (
                  <div>
                    <Dropdown>
                      <Dropdown.Toggle
                        variant=""
                        id="dropdown-table"
                        className="table-dropdown pt-3"
                        size="sm"
                        disabled={showMergeCheckbox}
                      >
                        <div className="f-16">
                          <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                        </div>
                      </Dropdown.Toggle>
                      <Dropdown.Menu renderOnMount={false}>
                        {filter.get('status', '') !== 'missed to completed' && (
                          <Dropdown.Item
                            onClick={() => {
                              setShowMergeCheckbox(true);
                              setShowRevoke(false);
                              setShowMissedToComplete(false);
                            }}
                          >
                            <Trans i18nKey={'merge_sessions'}></Trans>
                          </Dropdown.Item>
                        )}
                        {filter.get('status', '') !== 'missed to completed' &&
                          checkMissedToComplete && (
                            <Dropdown.Item
                              onClick={() => {
                                setShowMissedToComplete(true);
                                setShowRevoke(false);
                                setShowMergeCheckbox(false);
                              }}
                            >
                              Missed to Complete Sessions
                            </Dropdown.Item>
                          )}
                        {filter.get('status', '') === 'missed to completed' &&
                          checkMissedToComplete && (
                            <Dropdown.Item
                              onClick={() => {
                                setShowRevoke(true);
                                setShowMissedToComplete(false);
                                setShowMergeCheckbox(false);
                              }}
                            >
                              Revoke
                            </Dropdown.Item>
                          )}
                      </Dropdown.Menu>
                    </Dropdown>
                  </div>
                )}
            </div>
          </div>
          {mergeModalData.get('scheduleList', List()).size > 0 && (
            <div className="text-right mr-4">
              Selected: {mergeModalData.get('scheduleList', List()).size}
            </div>
          )}
          {missedToCompleteOpen && (
            <Suspense fallback="">
              <MissedToCompleteModal
                open={missedToCompleteOpen}
                setMissedToCompleteOpen={setMissedToCompleteOpen}
                handleSave={handleSave}
              />
            </Suspense>
          )}

          {revokeOpen && (
            <Suspense fallback="">
              <RevokeModal
                open={revokeOpen}
                setRevokeOpen={setRevokeOpen}
                handleSave={handleSave}
              />
            </Suspense>
          )}
        </div>
        {activeScheduleView === 'list' ? (
          <ScheduleListView
            activeViewType={filter.get('view', '')}
            course={filteredCourse}
            isAllowedToSchedule={isAllowedToSchedule}
            handleAddEditSchedule={handleAddEditSchedule}
            studentGroupStatus={getURLParams('studentGroupStatus', true) === 'true'}
            fetchCourseSchedule={fetchCourseSchedule}
            paginationMetaData={paginationMetaData}
            isLoading={loading.get('GET_COURSE_SCHEDULE', false)}
            handleDeleteSchedule={handleDeleteSchedule}
            handleCancelSchedule={handleCancelSchedule}
            showMergeCheckbox={showMergeCheckbox}
            mergeScheduleList={mergeModalData.get('scheduleList', List())}
            handleMergeScheduleCheckboxChange={handleMergeScheduleCheckboxChange}
            handleEditMergeSchedule={handleEditMergeSchedule}
            handleAddEditSupportAndEvents={handleAddEditSupportAndEvents}
            handleCancelDeleteSupportAndEventsSchedule={handleCancelDeleteSupportAndEventsSchedule}
            handleStudentGroupModalOpen={handleStudentGroupModalOpen}
            addEventsAndSupportSessionRef={addEventsAndSupportSessionRef}
            supportSessionTypes={getSupportSessionTypes()}
            isRotation={isRotation}
            studentGroupId={filter.get('studentGroup', '')}
            currentCalendar={currentCalendar}
            programId={programId}
            showMissedToComplete={showMissedToComplete}
            showRevoke={showRevoke}
            status={filter.get('status', '')}
            handleMissedCheckboxChange={handleMissedCheckboxChange}
          />
        ) : (
          <ScheduleCalendar
            activeViewType={filter.get('view', '')}
            course={getCourse()}
            setData={setData}
            activeSessionFlow={activeSessionFlow}
            handleAddEditSchedule={handleAddEditSchedule}
            fetchCourseSchedule={fetchCourseSchedule}
            paginationMetaData={paginationMetaData}
            isLoading={loading.get('GET_COURSE_SCHEDULE', false)}
            handleAddEditSupportAndEvents={handleAddEditSupportAndEvents}
            handleCancelDeleteSupportAndEventsSchedule={handleCancelDeleteSupportAndEventsSchedule}
            handleStudentGroupModalOpen={handleStudentGroupModalOpen}
            studentGroupId={filter.get('studentGroup', '')}
            programId={programId}
          />
        )}
      </div>

      {isModuleEnabled('OUTSIDE_CAMPUS_V2') === true ? (
        <AddEditScheduleModalDrawer
          show={modalData.get('show', false)}
          institutionCalendarId={institutionCalendarId}
          onHide={handleCloseModal}
          mode={modalData.get('mode', 'create')}
          data={modalData}
          course={getCourse()}
          onChange={handleScheduleDataChange}
          onSave={handleSaveSchedule}
          autoChange={autoChange}
          setData={setData}
          activeSessionFlow={scheduleAvailability}
          getScheduleAvailability={getAvailability}
          excludedTimes={getExcludedTimes(modalData.getIn(['schedule', 'schedule_date'], ''))
            .reduce((acc, excluded) => acc.concat(excluded.get('excludedTimes', List())), List())
            .toJS()}
          studentGroupScheduleStatus={getScheduledCourseGroups()}
          handleDeleteSchedule={() =>
            handleDeleteSchedule(
              {
                sessionFlow: deleteData.get('session', {}),
                schedule: deleteData.get('schedule', {}),
              },
              false
            )
          }
          isRotation={isRotation}
          programId={programId}
          courseId={courseId}
          setModalData={setModalData}
          courseData={course}
          scheduleId={scheduleId}
          checkUpdateExternal={checkUpdateExternal}
        />
      ) : (
        <AddEditScheduleModal
          show={modalData.get('show', false)}
          onHide={handleCloseModal}
          mode={modalData.get('mode', 'create')}
          data={modalData}
          course={getCourse()}
          onChange={handleScheduleDataChange}
          onSave={handleSaveSchedule}
          autoChange={autoChange}
          setData={setData}
          activeSessionFlow={scheduleAvailability}
          getScheduleAvailability={getAvailability}
          excludedTimes={getExcludedTimes(modalData.getIn(['schedule', 'schedule_date'], ''))
            .reduce((acc, excluded) => acc.concat(excluded.get('excludedTimes', List())), List())
            .toJS()}
          studentGroupScheduleStatus={getScheduledCourseGroups()}
          handleDeleteSchedule={() =>
            handleDeleteSchedule(
              {
                sessionFlow: deleteData.get('session', {}),
                schedule: deleteData.get('schedule', {}),
              },
              false
            )
          }
          isRotation={isRotation}
          programId={programId}
        />
      )}
      <AddEditSupportSessionAndEvents
        show={supportAndEventsModalData.get('show', false)}
        mode={supportAndEventsModalData.get('mode', 'create')}
        data={supportAndEventsModalData}
        course={getCourse()}
        onHide={handleCloseModal}
        onChange={handleSupportAndEventDataChange}
        onSave={handleSaveSupportAndEventsSchedule}
        excludedTimes={getExcludedTimes(
          supportAndEventsModalData.getIn(['schedule', 'schedule_date'], '')
        )
          .reduce((acc, excluded) => acc.concat(excluded.get('excludedTimes', List())), List())
          .toJS()}
        handleDeleteSchedule={() =>
          handleCancelDeleteSupportAndEventsSchedule(
            { schedule: deleteData.get('schedule', {}), mode: 'delete' },
            false
          )
        }
        supportSessionTypes={getSupportSessionTypes()}
        isRotation={isRotation}
        deliveryTypes={advancedSettings.get('delivery_type', List())}
        programId={programId}
      />
      <BreakSessionFlow
        show={showBreakSessionFlowModal}
        onHide={handleBreakSessionFlowModalClose}
        schedule={modalData.get('schedule', Map())}
        onConfirm={handleBreakSessionFlow}
      />
      {isModuleEnabled('OUTSIDE_CAMPUS_V2') === true ? (
        <MergeScheduleModalDrawer
          show={mergeModalData.get('show', false)}
          onHide={handleCloseModal}
          mode={mergeModalData.get('mode', 'create')}
          data={mergeModalData}
          course={getCourse()}
          onChange={handleMergeSessionDataChange}
          onSave={handleSaveMergeSchedule}
          setData={setData}
          excludedTimes={getExcludedTimes(mergeModalData.getIn(['schedule', 'schedule_date'], ''))
            .reduce((acc, excluded) => acc.concat(excluded.get('excludedTimes', List())), List())
            .toJS()}
          sessionFlowList={mergeSchedule.get('session_list', List())}
          scheduleList={mergeSchedule.get('schedule_list', List())}
          handleDetachSchedule={handleDetachSchedule}
          isRotation={isRotation}
          programId={programId}
          setMergeModalData={setMergeModalData}
          institutionCalendarId={institutionCalendarId}
          courseId={courseId}
          courseData={course}
          scheduleId={mergeScheduleId}
          checkUpdateExternal={checkUpdateExternalListMergeModal}
        />
      ) : (
        <MergeScheduleModal
          show={mergeModalData.get('show', false)}
          onHide={handleCloseModal}
          mode={mergeModalData.get('mode', 'create')}
          data={mergeModalData}
          course={getCourse()}
          onChange={handleMergeSessionDataChange}
          onSave={handleSaveMergeSchedule}
          setData={setData}
          excludedTimes={getExcludedTimes(mergeModalData.getIn(['schedule', 'schedule_date'], ''))
            .reduce((acc, excluded) => acc.concat(excluded.get('excludedTimes', List())), List())
            .toJS()}
          sessionFlowList={mergeSchedule.get('session_list', List())}
          scheduleList={mergeSchedule.get('schedule_list', List())}
          handleDetachSchedule={handleDetachSchedule}
          isRotation={isRotation}
          programId={programId}
        />
      )}
      <SelectStudentGroupsModal
        show={studentGroupModalData.get('show', false)}
        mode={studentGroupModalData.get('mode', 'create')}
        readOnly={studentGroupModalData.get('readOnly', false)}
        onHide={handleStudentGroupModalClose}
        studentGroups={studentBasedDeliveryTypes}
        activeStudentGroupId={studentGroupModalData.get('activeStudentGroupId')}
        data={studentGroupModalData}
        handleChange={handleStudentGroupDataChange}
        handleSave={handleSaveStudentGroup}
        isRotation={isRotation}
        state={state}
        setState={setState}
        setSearchValue={setSearchValue}
        searchValue={searchValue}
      />
      {alertConfirmModalData.show && (
        <AlertConfirmModal
          show={alertConfirmModalData.show}
          title={alertConfirmModalData.title || ''}
          titleIcon={alertConfirmModalData.titleIcon}
          body={alertConfirmModalData.body || ''}
          variant={alertConfirmModalData.variant || 'confirm'}
          confirmButtonLabel={alertConfirmModalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={alertConfirmModalData.cancelButtonLabel || 'NO'}
          onClose={onModalClose}
          onConfirm={onConfirm}
          data={alertConfirmModalData.data}
        />
      )}
    </div>
  );
}

CourseSchedule.propTypes = {
  course: PropTypes.instanceOf(Map),
  activeScheduleView: PropTypes.oneOf(['list', 'calendar']),
  setData: PropTypes.func,
  activeSessionFlow: PropTypes.instanceOf(Map),
  institutionCalendarId: PropTypes.string,
  saveSchedule: PropTypes.func,
  scheduleAvailability: PropTypes.instanceOf(Map),
  getScheduleAvailability: PropTypes.func,
  fetchCourseSchedule: PropTypes.func,
  paginationMetaData: PropTypes.instanceOf(Map),
  loading: PropTypes.instanceOf(Map),
  programName: PropTypes.string,
  getAdvancedSettings: PropTypes.func,
  saveAdvanceSettings: PropTypes.func,
  advancedSettings: PropTypes.instanceOf(Map),
  cancelSchedule: PropTypes.func,
  getMergeSchedule: PropTypes.func,
  mergeSchedule: PropTypes.instanceOf(Map),
  saveMergeSchedule: PropTypes.func,
  studentGroupsWithStudents: PropTypes.instanceOf(List),
  getStudentGroupsWithStudents: PropTypes.func,
  saveStudentGroupsWithStudents: PropTypes.func,
  saveSupportAndEventsSchedule: PropTypes.func,
  updateMissedSession: PropTypes.func,
};

export default CourseSchedule;
