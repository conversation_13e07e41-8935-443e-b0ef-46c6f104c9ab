import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { DialogContent, DialogActions, Stack, Checkbox, FormControlLabel } from '@mui/material';

import MButton from 'Widgets/FormElements/material/Button';
import Input from 'Widgets/FormElements/Input/Input';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Autocomplete from '../AutoComplete';
import {
  categories,
  approvalConfigurations,
  turnAroundTime,
  getLeaveTypeName,
  BootstrapDialog,
  BootstrapDialogTitle,
  approvalRoleConfigurations,
} from '../../utils';
import { ClassificationsContext } from '../Classifications';

const ApprovalLevelPopup = ({
  open,
  handleClose,
  permissionApproval,
  setPermissionApproval,
  handleSubmit,
  getOptions,
  handleChange,
  setSearchKey,
  searchKey,
  allowSkipping,
}) => {
  const { type } = useContext(ClassificationsContext);
  const categoryType = permissionApproval.get('category', 'Role Based');

  return (
    <React.Fragment>
      <BootstrapDialog aria-labelledby="customized-dialog-title" open={open}>
        <BootstrapDialogTitle id="customized-dialog-title" onClose={handleClose}>
          {getLeaveTypeName(type)} Approval Levels
        </BootstrapDialogTitle>
        <DialogContent dividers>
          <div>
            <div className="mb-4 w-50">
              <div className="digi-light-gray mb-1">Level:</div>
              <div>
                <MaterialInput
                  elementType={'materialInput'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  value={permissionApproval.get('level', '')}
                  changed={(e) =>
                    setPermissionApproval(permissionApproval.set('level', e.target.value))
                  }
                  placeholder={'Enter level name'}
                />
              </div>
            </div>

            <div className="mb-4">
              <div className="digi-light-gray">Category:</div>
              <div className="radio-adjustment">
                <Input
                  elementType={'radio'}
                  elementConfig={categories}
                  className={'form-radio1'}
                  labelclass="radio-label2"
                  selected={permissionApproval.get('category', 'Role Based')}
                  onChange={(e) =>
                    setPermissionApproval(
                      permissionApproval
                        .set('category', e.target.value)
                        .set(
                          'approvalConfiguration',
                          e.target.value === 'Role Based'
                            ? approvalRoleConfigurations(permissionApproval.get('role', List()))[0]
                                .value
                            : approvalConfigurations(permissionApproval.get('user', List()))[0]
                                .value
                        )
                        .set('role', List())
                        .set('user', List())
                    )
                  }
                  disabled={true}
                />
              </div>
            </div>

            <div className="mb-4 w-50">
              <div className="digi-light-gray mb-1">
                {categoryType === 'Role Based' ? 'Roles:' : 'Users:'}
              </div>
              <div>
                {categoryType === 'Role Based' ? (
                  <Autocomplete
                    activeIndex={2}
                    setSearchKey={setSearchKey}
                    searchKey={searchKey}
                    options={getOptions('role')}
                    handleChange={handleChange}
                    permissionApproval={permissionApproval}
                    type={'role'}
                    value={permissionApproval.get('role', List())}
                    name={'Role'}
                  />
                ) : (
                  <Autocomplete
                    activeIndex={3}
                    setSearchKey={setSearchKey}
                    searchKey={searchKey}
                    options={getOptions('user')}
                    handleChange={handleChange}
                    permissionApproval={permissionApproval}
                    type={'user'}
                    value={permissionApproval.get('user', List())}
                    name={'User'}
                  />
                )}
              </div>
            </div>

            <div className="mb-4 digi-w-80">
              <div className="digi-light-gray mb-1">Approval Configuration:</div>
              <div>
                <MaterialInput
                  elementType={'materialSelect'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{
                    options:
                      categoryType === 'Role Based'
                        ? approvalRoleConfigurations(permissionApproval.get('role', List()))
                        : approvalConfigurations(permissionApproval.get('user', List())),
                  }}
                  value={permissionApproval.get('approvalConfiguration', '')}
                  changed={(e) =>
                    setPermissionApproval(
                      permissionApproval.set('approvalConfiguration', e.target.value)
                    )
                  }
                  disabled={
                    categoryType === 'Role Based'
                      ? permissionApproval.get('role', '').size <= 1
                      : permissionApproval.get('user', '').size <= 1
                  }
                />
              </div>
            </div>

            <div className="w-50">
              <div className="digi-light-gray mb-1">TAT (Turn Around Time)</div>
              <div>
                <MaterialInput
                  elementType={'materialSelect'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: turnAroundTime }}
                  value={permissionApproval.get('turnAroundTime', '')}
                  changed={(e) =>
                    setPermissionApproval(permissionApproval.set('turnAroundTime', e.target.value))
                  }
                />
              </div>
            </div>

            <div className="mt-3">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={permissionApproval.get('escalateRequest', false)}
                    onChange={(e) =>
                      setPermissionApproval(
                        permissionApproval.set('escalateRequest', e.target.checked)
                      )
                    }
                  />
                }
                label="Escalate to next level, if no response"
              />
            </div>

            {!allowSkipping && (
              <div>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={permissionApproval.get('skipPreviousLevelApproval', false)}
                      onChange={(e) =>
                        setPermissionApproval(
                          permissionApproval.set('skipPreviousLevelApproval', e.target.checked)
                        )
                      }
                    />
                  }
                  label="Allow skipping all previous Approval levels"
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={permissionApproval.get('overwritePreviousLevelApproval', false)}
                      onChange={(e) =>
                        setPermissionApproval(
                          permissionApproval.set('overwritePreviousLevelApproval', e.target.checked)
                        )
                      }
                    />
                  }
                  label="Allow to overwrite all previous level rejected applications"
                />
              </div>
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <Stack spacing={2} direction="row">
            <MButton variant="text" color={'blue'} clicked={handleClose}>
              CANCEL
            </MButton>
            <MButton
              variant="contained"
              color={'blue'}
              clicked={(e) =>
                handleSubmit(
                  '',
                  permissionApproval.get('levelId', ''),
                  permissionApproval.get('gender', '')
                )
              }
            >
              {permissionApproval.get('levelId', '') ? 'SAVE' : 'ADD'}
            </MButton>
          </Stack>
        </DialogActions>
      </BootstrapDialog>
    </React.Fragment>
  );
};

ApprovalLevelPopup.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  permissionApproval: PropTypes.instanceOf(Map),
  setPermissionApproval: PropTypes.func,
  handleSubmit: PropTypes.func,
  getOptions: PropTypes.func,
  handleChange: PropTypes.func,
  setSearchKey: PropTypes.func,
  searchKey: PropTypes.string,
  allowSkipping: PropTypes.bool,
};

export default ApprovalLevelPopup;
