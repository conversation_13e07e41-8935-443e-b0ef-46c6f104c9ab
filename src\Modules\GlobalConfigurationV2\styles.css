.bg-light-red {
  background-color: #fee2e2;
}
.bg-light-yellow {
  background-color: #fffbeb;
}
.text-dark-red {
  color: #f87171;
}
.bg-draft {
  background-color: #9ca3af;
}
.border-lite-gray {
  border: '1px solid #D1D5DB';
}
.stepper-disable {
  background-color: #6b7280 !important;
  color: #ffffff !important;
}
.close-btn {
  position: absolute;
  z-index: 1300 !important;
  top: 53px;
  top: 53px;
  right: 100%;
  display: flex;
  width: 35px;
  height: 35px;
  background-color: #147afc;
  background-color: #147afc;
  justify-content: center;
  align-items: center;
  color: white;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  cursor: pointer;
}

.close-btn-bottom {
  position: absolute;
  z-index: 1300 !important;
  top: 100%;
  right: 0;
  width: 0;
  height: 0;
  border-left: 0px solid transparent;
  border-right: 35px solid #233971;
  border-bottom: 13px solid transparent;
}

.rightDrawer {
  position: relative;
  z-index: 1300 !important;
}

/* -------------------------------------------------------------------------------------------------*/
.academicYear-btn-position {
  position: absolute;
  top: 8px;
  right: 0px;
}

.monthPicker-popper {
  z-index: 1300;
}

.monthPicker-menu-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
  grid-gap: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.monthPicker-drop-menu {
  border-radius: 5px;
  background-color: #ffffff;
  box-shadow: 0px 4px 6px rgba(17, 24, 39, 0.2) !important;
}
.approver-container-Concluding {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-auto-flow: row;
  grid-gap: 10px;
}

.attachment-files-Concluding {
  width: 190px;
  height: 80px;
  overflow: hidden;
}
.attachment-container {
  position: relative;
  overflow: hidden;
}
.attachment-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.5) 85%);
  backdrop-filter: blur(0px);
}
.file-viewer {
  width: -webkit-fill-available;
  height: 100px;
}
.object-fit-contain {
  object-fit: contain;
}
.approval-card {
  min-width: 370px;
}

.occurrence-details {
  width: 100%;
  max-width: 250px;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
}

.occurrence-text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* -----------------------------------scrollbar start-----------------------------------------------*/
.g-config-scrollbar::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}
.g-config-scrollbar::-webkit-scrollbar-thumb {
  background: #c8c8c8;
  border-radius: 15px;
}
.g-config-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 15px;
}
/* -----------------------------------scrollbar end-------------------------------------------------*/

/* -----------------------------------Html ToolTip style-------------------------------------------------*/
.htmlToolTip[data-title]:hover:after {
  content: '';
  position: absolute;
  top: -3px;
  left: 8px;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-top: 7px solid #374151;
}
.htmlToolTip[data-title]:hover::before {
  content: attr(data-title);
  position: absolute;
  bottom: 20px;
  min-width: 200px;
  text-align: center;
  word-wrap: break-word;
  padding: 7px;
  border-radius: 4px;
  background-color: #374151;
  color: white;
  z-index: 99999;
}
/* -----------------------------------Html ToolTip style-------------------------------------------------*/

.course-scroll {
  overflow-y: auto;
  padding: 0px 5px;
  max-height: 200px;
}

.q360-approval-message-chat {
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0px 5px;
  max-height: 300px;
}
.ck-editor-content img{
 width: 100%;
 height: 300px;
 object-fit: cover;
 object-position: bottom;
}
.more-details-accordion-scroll {
  overflow-y: auto;
  padding: 0px 5px;
  max-height: 100%;
}
.category-scroll {
  overflow-y: auto;
  padding: 0px 5px;
  max-height: 200px;
}
.q360-forms-scroll {
  overflow-y: auto;
  padding: 0px 0px 5px;
  max-height: 35vh;
}
.q360-iframe-scroll {
  overflow-y: auto;
  padding: 0px 0px 5px;
  max-height: 100vh;
}
.paddingRight {
  padding-right: 2px;
}

.height_40px {
  height: 40px;
}

.doc_view_attach {
  display: flex;
  justify-content: space-between;
  width: 62px;
  font-size: 14px;
  align-items: center;
}

.doc_view_div1 {
  border: 1px solid rgba(17, 24, 39, 0.2);
  border-radius: 5px;
  /* height: 100px; */
  box-shadow: 6px 6px 6px -8px grey;
  width: 230px;
}
.doc_view_div1:hover {
  /* border: 1px solid rgba(17, 24, 39, 0.2);
  border-radius: 5px; */
  /* height: 100px; */
  box-shadow: 6px 6px 6px -5px grey;
  /* width: 230px; */
}

.doc_view_div2 {
  height: 110px;
  justify-content: center;
  align-items: center;
  box-shadow: 10px 10px 30px 15px #edf6ff inset;
}

.doc_view_div3 {
  font-size: 14px;
  font-weight: 500;
  color: rgba(55, 65, 81, 1);
}

.gap_doc_view {
  gap: 20px;
}

.border_top_grey_view {
  border-top: 1px solid rgba(17, 24, 39, 0.2);
}

.ht_80vh {
  height: 80vh;
}

.form-action-grid {
  display: grid;
  grid-gap: 8px;
  grid-template-columns: 56% 20% auto;
}
.grid-self-center {
  align-items: center;
}

.form_heading_creation {
  color: rgba(75, 85, 99, 1);
  font-size: 24px;
  font-weight: 400;
}

.img_overflow_scroll_sub {
  overflow: auto;
}

.forum_preview_image {
  width: -webkit-fill-available;
  height: 100px;
}

.forum_doc_sub {
  border: 2px solid #d9dde2;
  border-radius: 5px;
  color: #384252;
  max-width: 250px;
}

.forum_preview_image::-webkit-scrollbar {
  display: none;
}

.gradient-container {
  position: relative;
  overflow: hidden;
}

.gradient-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.5) 85%);
  /* linear-gradient(45deg, black, transparent) */
  backdrop-filter: blur(0px); /* Adjust the blur strength as needed */
}

.attached_more_file {
  padding: 5px;
  border: 1px solid rgba(209, 213, 219, 1);
  margin-top: 5px;
  border-radius: 5px;
  color: rgba(156, 163, 175, 1);
  font-size: 10px;
  text-align: center;
}

.height_19px {
  height: 19px;
}

.width_fit_content {
  width: fit-content;
}

.cursor-pointer-context-menu {
  cursor: context-menu !important;
}

.q360-message-input {
  position: relative;
}
.q360-message-reply {
  position: absolute;
  bottom: var(--data-);
  width: 334px;
  background-color: #f3f4f6;
  transition: 0.5s;
}
.inner-reply-message {
  border-left: 4px solid #6b7280;
  background-color: #d1d5db;
}
.main {
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: 0.5;
}

.q360-message-input {
  position: relative;
}
.q360-message-reply {
  position: absolute;
  bottom: var(--data-);
  width: 334px;
  background-color: #f3f4f6;
  transition: 0.5s;
}
.inner-reply-message {
  border-left: 4px solid #6b7280;
  background-color: #d1d5db;
}
.main {
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: 0.5;
}

.shadow-hover:hover {
  box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
}
.box-shadow-static:hover {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px !important;
}

.slider-grid{
display: grid;
grid-template-columns: 5% 90% 5%;
height: 100vh;
background-color:#f1f1f1;

}
.slider-left,.slider-right{
  align-self: center;
  padding-left: 20px;
}
.items-scroll{
  align-self: center;
  height: 78vh;
  overflow-y: scroll;
}
