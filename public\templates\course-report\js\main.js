$(document).ready(function () {
  $('#section-e-add-row').on('click', function () {
    const selectedSubheaderIndex = $('#section-e-select')[0].options.selectedIndex;

    // Find all subheader rows
    const subheaderRows = $('tr.bg-teal-500');

    // Ensure the index is within bounds
    const newRow = $('<tr></tr>');

    $('#section-e-table tbody tr:nth-child(2) td').each(function (index) {
      if (index === 0) {
        newRow.append(
          '<td contenteditable="true" class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold">....</td>'
        );
      } else {
        newRow.append('<td contenteditable="true" class="border border-gray-300 px-4 py-2"></td>');
      }
    });
    if (selectedSubheaderIndex >= 0 && selectedSubheaderIndex < subheaderRows.length - 1) {
      // Get the target subheader based on the index
      const targetSubheader = subheaderRows.eq(selectedSubheaderIndex + 1);

      // Insert a new row after the target subheader

      targetSubheader.before(newRow);
    } else {
      alert('Invalid subheader index.');
      const tbody = $('#section-e-table tbody');
      tbody.append(newRow);
    }
  });

  // Populate the select dropdown based on the subheaders
  $('tr.bg-teal-500').each(function () {
    const text = $(this).find('td.section-e-group-header').text().trim();
    $('#section-e-select').append(`<option>${text}</option>`);
  });

  $('#section-c-add-column').click(function () {
    const count = $('#section-c-sub-header-count').val();
    console.log();
    const headerCount = parseInt(count) || 0;
    const rowSpan = headerCount ? 1 : 2;
    $('#section-c-table thead tr:first').append(
      `<th contenteditable="true" class="left-header" rowSpan="${rowSpan}"  colSpan="${count}">New Header </th>`
    );

    // Loop to add headers and subheaders
    for (let i = 0; i < headerCount; i++) {
      // Add a new header cell in the first row
      // Add a new subheader cell in the second row
      $('#section-c-table thead tr:nth-child(2)').append(
        `<th  contenteditable="true">New Subheader ${i + 1}</th>`
      );
      // Add a new cell in the body rows for the new header
      $('#section-c-table tbody tr').each(function () {
        $(this).append(`<td contenteditable="true"></td>`);
      });
    }
    if (headerCount === 0) {
      $('#section-c-table tbody tr').each(function () {
        $(this).append(`<td contenteditable="true"></td>`);
      });
    }
  });

  $('#section-c-add-row').click(function () {
    const rowCount = $('#section-c-row-count').val();
    const newRowCount = parseInt(rowCount) || 1;

    for (let i = 0; i < newRowCount; i++) {
      const newRow = $('<tr></tr>');
      newRow.append('<td contenteditable="true" class="left-header font-bold">New Row</td>');

      // Append empty cells for all columns in the row
      $('#section-c-table thead tr:nth-child(2) th').each(function () {
        newRow.append('<td contenteditable="true" class=" px-4 py-2"></td>');
      });

      $('#section-c-table tbody').append(newRow);
    }
  });
  $('#section-e-add-column').click(function () {
    const count = $('#section-e-sub-header-count').val();
    const headerCount = parseInt(count) || 0;
    const rowSpan = headerCount ? 1 : 2;
    $('#section-e-table thead tr:first').append(
      `<th contenteditable="true" class="left-header"  rowSpan="${rowSpan}" colSpan="${count}">New Header </th>`
    );

    // Loop to add headers and subheaders
    for (let i = 0; i < headerCount; i++) {
      // Add a new header cell in the first row
      // Add a new subheader cell in the second row
      $('#section-e-table thead tr:nth-child(2)').append(
        `<th contenteditable="true">New Subheader ${i + 1}</th>`
      );
      // Add a new cell in the body rows for the new header
      $('#section-e-table tbody tr').each(function () {
        if (this.className === 'bg-teal-500 text-white') return;
        $(this).append(`<td class="border border-gray-300" contenteditable="true"></td>`);
      });
    }
    if (headerCount === 0) {
      $('#section-e-table tbody tr').each(function () {
        if (this.className === 'bg-teal-500 text-white') return;
        $(this).append(`<td class="border border-gray-300" contenteditable="true"></td>`);
      });
    }
    $('.section-e-group-header').each(function () {
      this.colSpan += headerCount || 1;
    });
  });

  //section-f
  $('#section-f-add-row').on('click', function () {
    const numColumns = $(this).closest('.section-f-content').find('table thead tr th').length; // Get the number of columns from the header
    let newRow = '<tr>';

    // Create new cells for the new row based on the number of columns
    for (let i = 0; i < numColumns; i++) {
      newRow += '<td contenteditable="true"></td>';
    }
    newRow += '</tr>';

    $(this).closest('.section-f-content').find('table tbody').append(newRow);
  });

  // Add Column
  $('#section-f-add-column').on('click', function () {
    const newHeader = `<th contenteditable="true">New Column</th>`;
    const section = $(this).closest('.section-f-content'); // Get the closest section-f-content

    section.find('table thead tr').append(newHeader); // Add header

    // Add new cell to each existing row
    section.find('table tbody tr').each(function () {
      const newCell = `<td contenteditable="true"></td>`;
      $(this).append(newCell); // Add cell to each row
    });
  });

  //section-f1
  $('#section-f1-add-row').on('click', function () {
    const numColumns = $(this).closest('.section-f1-content').find('table thead tr th').length; // Get the number of columns from the header
    const rowsLength = $(this).closest('.section-f1-content').find('table tbody tr').length;
    let newRow = '<tr>';
    // Create new cells for the new row based on the number of columns
    for (let i = 0; i < numColumns; i++) {
      newRow += `<td contenteditable="true">${i === 0 ? i + 1 + rowsLength + '.' : ''}</td>`;
    }
    newRow += '</tr>';

    $(this).closest('.section-f1-content').find('table tbody').append(newRow);
  });

  // Add Column
  $('#section-f1-add-column').on('click', function () {
    const newHeader = `<th contenteditable="true">New Column</th>`;
    const section = $(this).closest('.section-f1-content'); // Get the closest section-f-content

    section.find('table thead tr').append(newHeader); // Add header

    // Add new cell to each existing row
    section.find('table tbody tr').each(function () {
      const newCell = `<td contenteditable="true"></td>`;
      $(this).append(newCell); // Add cell to each row
    });
  });
});
