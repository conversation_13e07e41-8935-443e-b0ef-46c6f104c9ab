import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Stack,
  Checkbox,
  FormControlLabel,
  Divider,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
} from '@mui/material';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Fade from '@mui/material/Fade';
import Chip from '@mui/material/Chip';

import MButton from 'Widgets/FormElements/material/Button';
import { useStylesFunction } from '../designUtils';
import { List, Map } from 'immutable';
import { ClassificationsContext } from './Classifications';
import NoteIcon from 'Assets/note.svg';
import Tooltip from 'Widgets/FormElements/material/Tooltip';
import { IOSSwitch } from '../utils';

import Bin from 'Assets/bin.png';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import GenderSegregationModal from './Modal/GenderSegregationModal';

function FadeMenu({ item }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const [genderSegModalOpen, setGenderSegModalOpen] = useState(false);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSwitch = () => {
    setGenderSegModalOpen(true);
  };

  const setGenderSegModalClose = () => {
    setGenderSegModalOpen(false);
  };

  return (
    <div>
      <Button
        id="fade-button"
        aria-controls={open ? 'fade-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={(e) => {
          e.stopPropagation();
          handleClick(e);
        }}
      >
        <SettingsOutlinedIcon sx={{ marginRight: 1 }} /> Advanced Settings
      </Button>
      <Menu
        id="fade-menu"
        MenuListProps={{
          'aria-labelledby': 'fade-button',
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
      >
        <MenuItem onClick={handleClose}>
          <span className="mr-3">Gender Segregation</span>
          <IOSSwitch
            checked={
              ['M', 'F', 'male', 'female'].includes(item.getIn(['level', 0, 'gender'], '')) ||
              item.get('genderSegregation', false)
            }
            onChange={() => {
              // e.stopPropagation();
              // handleSwitch(e, item.get('_id', ''));
              handleSwitch();
            }}
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </MenuItem>
      </Menu>

      {genderSegModalOpen && (
        <GenderSegregationModal
          open={genderSegModalOpen}
          setGenderSegModalClose={setGenderSegModalClose}
          item={item}
        />
      )}
    </div>
  );
}

FadeMenu.propTypes = {
  item: PropTypes.instanceOf(Map),
  handleSwitch: PropTypes.func,
};

function ToolBarMenu({ item, index }) {
  const { handleClickOpenProgram, setProgramDeleteShow, setLevelApproverId } = useContext(
    ClassificationsContext
  );
  const [anchorEl, setAnchorEl] = useState({});

  const handleClick = (e, index) => {
    e.stopPropagation();
    setAnchorEl({ [index]: e.currentTarget });
  };

  const handleClose = (e) => {
    e.stopPropagation();
    setAnchorEl({});
  };

  const menuopen = Boolean(anchorEl[index]);

  return (
    <div className="ml-2 mb-2 mt-1">
      <MoreVertIcon
        color="primary"
        id={`basic-button`}
        aria-controls={menuopen ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={menuopen ? 'true' : undefined}
        onClick={(e) => handleClick(e, index)}
      />
      <Menu
        id="basic-menu"
        anchorEl={anchorEl[index]}
        open={menuopen}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
        className="pr-5 pl-5"
      >
        <MenuItem
          id={`basic-item`}
          className=" pr-4 pl-4 text-primary"
          onClick={(e) => {
            e.stopPropagation();
            handleClickOpenProgram('update', item.get('_id', ''));
            handleClose(e);
          }}
        >
          Edit
        </MenuItem>
        <Divider />
        <MenuItem
          className=" pr-4 pl-4 text-danger"
          onClick={(e) => {
            e.stopPropagation();
            setProgramDeleteShow(true);
            setLevelApproverId(item.get('_id', ''));
            handleClose(e);
          }}
        >
          Delete
        </MenuItem>
      </Menu>
    </div>
  );
}

ToolBarMenu.propTypes = {
  item: PropTypes.instanceOf(Map),
  handleClickOpenProgram: PropTypes.func,
  handleDeleteOpenProgram: PropTypes.func,
  index: PropTypes.number,
};

function ProgramDetails({ setGender, isEditGeneralConfig, isEditApproveLevels }) {
  const { accordion } = useStylesFunction();

  const {
    type,
    levelApprover,
    handleSwitch,
    handleClickOpenProgram,
    handleDeleteOpenProgram,
  } = useContext(ClassificationsContext);

  const getData = (item, type) => {
    return item
      .get(type, List())
      .map((item) => item.get('name', ''))
      .join(' • ');
  };

  return (
    <>
      {levelApprover.map((item, index) => (
        <Accordion className={`${accordion}`} key={index}>
          <AccordionSummary
            expandIcon={
              <div className="mt-2">
                <ExpandMoreIcon />
              </div>
            }
            aria-controls="panel1bh-content"
            id="panel1bh-header"
            className={`panelSummary`}
          >
            <div
              className={`${
                isEditApproveLevels ? 'pt-1' : 'pb-2'
              } mt-1 digi-lms-perm-divider flex-grow-1 bold text-dark`}
            >
              {item.get('programIds', List()).size} Programs{' '}
              <Tooltip
                title={
                  <>
                    <div className="font-weight-bole f-16 mb-2">Programs</div>
                    {getData(item, 'programIds')}
                  </>
                }
                color="white"
                maxWidth={1000}
                placement="top-end"
              >
                <span className="ml-2">
                  <img src={NoteIcon} width="15px" alt="Bin" />{' '}
                  {index !== 0 && `(${'Exception Program'})`}
                </span>
              </Tooltip>
            </div>
            <div className="digi-lms-perm-divider">
              {(type === 'leave' ? isEditGeneralConfig : isEditApproveLevels) && (
                <Stack spacing={1} direction="row">
                  <FadeMenu handleSwitch={handleSwitch} item={item} />
                  {index !== 0 && (
                    <ToolBarMenu
                      handleClickOpenProgram={handleClickOpenProgram}
                      handleDeleteOpenProgram={handleDeleteOpenProgram}
                      item={item}
                      index={index}
                    />
                  )}
                </Stack>
              )}
            </div>
            <Divider className="mb-3" />
          </AccordionSummary>
          <>
            {!item.get('genderSegregation', false) ? (
              <LevelsOfApproval
                programDetails={item.get('level', List())}
                isEditGeneralConfig={isEditGeneralConfig}
                isEditApproveLevels={isEditApproveLevels}
                levelApproverId={item.get('_id', '')}
                title="BOTH"
                setGender={setGender}
              />
            ) : (
              <>
                <LevelsOfApproval
                  isEditGeneralConfig={isEditGeneralConfig}
                  isEditApproveLevels={isEditApproveLevels}
                  programDetails={item
                    .get('level', List())
                    .filter((level) => ['M', 'male', 'Male'].includes(level.get('gender', 'M')))}
                  title="Male"
                  levelApproverId={item.get('_id', '')}
                  setGender={setGender}
                />
                <LevelsOfApproval
                  isEditGeneralConfig={isEditGeneralConfig}
                  isEditApproveLevels={isEditApproveLevels}
                  programDetails={item
                    .get('level', List())
                    .filter((level) =>
                      ['F', 'female', 'Female'].includes(level.get('gender', 'F'))
                    )}
                  title="Female"
                  levelApproverId={item.get('_id', '')}
                  setGender={setGender}
                />
              </>
            )}
          </>
        </Accordion>
      ))}
    </>
  );
}

ProgramDetails.propTypes = {
  setGender: PropTypes.func,
  isEditApproveLevels: PropTypes.bool,
  isEditGeneralConfig: PropTypes.bool,
};

export default ProgramDetails;

function LevelsOfApproval({
  programDetails,
  title,
  isEditGeneralConfig,
  isEditApproveLevels,
  levelApproverId,
  setGender,
}) {
  const { accordion, BottomPadding } = useStylesFunction();
  const {
    type,
    handleClickOpen,
    setPermissionApproval,
    permissionApproval,
    setAllowSkipping,
  } = useContext(ClassificationsContext);

  return (
    <AccordionDetails className={`${BottomPadding}`}>
      <Accordion className={`${accordion}`}>
        <AccordionSummary
          expandIcon={
            <div className="mt-2">
              <ExpandMoreIcon />
            </div>
          }
          aria-controls="panel1bh-content"
          id="panel1bh-header"
          className={`panelSummary`}
        >
          <div className="pt-2 mt-1 digi-lms-perm-divider flex-grow-1 bold text-dark">
            Levels Of Approval {title !== 'BOTH' && <span>({title})</span>}
            <Tooltip
              title={<div>You can add multiple levels of approval for selected programs</div>}
              color="white"
              maxWidth={1000}
              placement="top-end"
            >
              <span className="ml-2">
                <img src={NoteIcon} width="15px" alt="Bin" />
              </span>
            </Tooltip>
          </div>
          <div className="digi-lms-perm-divider">
            <Stack spacing={2} direction="row">
              <MButton
                className="mb-2"
                disabled={type === 'leave' ? !isEditGeneralConfig : !isEditApproveLevels}
                variant="contained"
                color={'blue'}
                clicked={(e) => {
                  e.stopPropagation();
                  handleClickOpen(levelApproverId);
                  setPermissionApproval(permissionApproval.set('gender', title));
                  setAllowSkipping(programDetails.size === 0);
                  setGender(title);
                }}
              >
                Add Levels
              </MButton>
            </Stack>
          </div>
          <Divider className="mb-3" />
        </AccordionSummary>
        <LevelsDetails
          isEditGeneralConfig={isEditGeneralConfig}
          isEditApproveLevels={isEditApproveLevels}
          programDetails={programDetails}
          levelApproverId={levelApproverId}
        />
      </Accordion>
    </AccordionDetails>
  );
}

LevelsOfApproval.propTypes = {
  programDetails: PropTypes.instanceOf(List),
  title: PropTypes.string,
  levelApproverId: PropTypes.string,
  isEditApproveLevels: PropTypes.bool,
  isEditGeneralConfig: PropTypes.bool,
  setGender: PropTypes.func,
};

function LevelsDetails({
  programDetails,
  isEditGeneralConfig,
  isEditApproveLevels,
  levelApproverId,
}) {
  const { accordion, accordionSummaryBorder, BottomPadding, MarginBottom } = useStylesFunction();
  const {
    type,
    handleEdit,
    setShow,
    setLevelId,
    setLevelApproverId,
    setToBeDeletedName,
    setAllowSkipping,
  } = useContext(ClassificationsContext);

  return (
    <AccordionDetails className={`${BottomPadding}`}>
      {programDetails &&
        programDetails.map((item, index) => (
          <Accordion key={index} className={`${accordion}`}>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="panel1bh-content"
              id="panel1bh-header"
              className={`panelSummary ${accordionSummaryBorder}`}
            >
              <div className="bold text-body mt-1 pt-1 digi-text-ellipsis-accordion digi-text-ellipsis">
                {item.get('levelName', '')}
              </div>
              <div className="flex-grow-1 mr-3 ml-3 mt-3 pt-1">
                <Divider className="digi-divider-mdl" />
              </div>
              <div className="d-flex">
                <Stack spacing={2} direction="row">
                  <MButton
                    variant="text"
                    disabled={type === 'leave' ? !isEditGeneralConfig : !isEditApproveLevels}
                    color={'blue'}
                    clicked={(e) => {
                      e.stopPropagation();
                      handleEdit(item.get('_id'), levelApproverId);
                      setAllowSkipping(index === 0);
                    }}
                  >
                    <ModeEditOutlineOutlinedIcon sx={{ fontSize: 16, marginRight: 1 }} />
                    <span className="mt-1">Edit</span>
                  </MButton>
                  <MButton
                    variant="text"
                    color={'red'}
                    disabled={type === 'leave' ? !isEditGeneralConfig : !isEditApproveLevels}
                    clicked={(e) => {
                      e.stopPropagation();
                      setShow(true);
                      setLevelId(item.get('_id'));
                      setLevelApproverId(levelApproverId);
                      setToBeDeletedName(item.get('levelName', ''));
                    }}
                  >
                    <div className="mr-2  f-14">
                      <img src={Bin} width="10px" alt="Bin" />
                    </div>
                    <span className="mt-1">Delete</span>
                  </MButton>
                </Stack>
              </div>
            </AccordionSummary>
            <AccordionDetails className={`${BottomPadding}`}>
              <div className="">
                <FormControl>
                  <FormLabel id="demo-row-radio-buttons-group-label">Category:</FormLabel>
                  <RadioGroup
                    row
                    aria-labelledby="demo-row-radio-buttons-group-label"
                    name="row-radio-buttons-group"
                  >
                    {['Role Based'].map((cat, catIndex) => (
                      <FormControlLabel
                        key={catIndex}
                        checked={item.get('categoryBased', '') === cat}
                        control={<Radio />}
                        label={cat}
                        disabled={true}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>
                <div className="d-flex align-items-center mb-3">
                  {item.get('categoryBased', '') === 'Role Based' ? (
                    <div className="mr-5">
                      <div className="digi-light-gray">Role:</div>
                      <Stack direction="row" spacing={1} className="d-inline">
                        {item.get('roleIds', List()).map((item, roleIndex) => {
                          return (
                            <Chip
                              key={roleIndex}
                              label={item.get('name', '')}
                              variant="outlined"
                              className="digi-role-bg-chip mb-2"
                            />
                          );
                        })}
                      </Stack>
                    </div>
                  ) : (
                    <div className="mr-5">
                      <div className="digi-light-gray">User:</div>
                      {item.get('userIds', List()).map((item, roleIndex) => {
                        return (
                          <Chip
                            key={roleIndex}
                            label={
                              item.getIn(['name', 'first'], '') + item.getIn(['name', 'last'], '')
                            }
                            variant="outlined"
                            className="digi-role-bg-chip mb-2"
                          />
                        );
                      })}
                    </div>
                  )}
                </div>
                <div className="mb-3">
                  <div className="digi-light-gray">Approval Configuration:</div>
                  <div>{item.get('approvalConfig', '')}</div>
                </div>
                <div className="mb-3">
                  <div className="digi-light-gray">TAT (Turn around time):</div>
                  <div>{item.get('turnAroundTime', '')} Days</div>
                </div>
                <div className="d-flex align-items-center digi-light-gray">
                  <FormControlLabel
                    className={`${MarginBottom}`}
                    control={<Checkbox checked={item.get('escalateRequest', false)} disabled />}
                    label="Escalate to next level, if no response"
                  />
                </div>
                {index !== 0 && (
                  <>
                    <div className="d-flex align-items-center digi-light-gray">
                      <FormControlLabel
                        className={`${MarginBottom}`}
                        control={
                          <Checkbox
                            checked={item.get('skipPreviousLevelApproval', false)}
                            disabled
                          />
                        }
                        label="Allow skipping the previous level of Approvals"
                      />
                    </div>
                    <div className="d-flex align-items-center digi-light-gray">
                      <FormControlLabel
                        className={`${MarginBottom}`}
                        control={
                          <Checkbox
                            checked={item.get('overwritePreviousLevelApproval', false)}
                            disabled
                          />
                        }
                        label="Allow to overwrite all previous level rejected applications"
                      />
                    </div>
                  </>
                )}
              </div>
            </AccordionDetails>
          </Accordion>
        ))}
    </AccordionDetails>
  );
}

LevelsDetails.propTypes = {
  programDetails: PropTypes.instanceOf(List),
  levelApproverId: PropTypes.string,
  isEditApproveLevels: PropTypes.bool,
  isEditGeneralConfig: PropTypes.bool,
};
