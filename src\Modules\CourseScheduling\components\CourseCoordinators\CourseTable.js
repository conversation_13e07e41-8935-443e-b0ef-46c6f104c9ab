import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { Table } from 'react-bootstrap';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { t } from 'i18next';

import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import {
  capitalize,
  formatFullName,
  getVersionName,
  indVerRename,
  levelRename,
} from '../../../../utils';

function CourseTable({
  courses,
  institutionCalendarId,
  setMessage,
  assignCoordinator,
  currentCalendar,
  programId,
}) {
  return (
    <div className="min_h p-3">
      <Table hover>
        <thead className="bg-white thead_border">
          <tr>
            <th>{t('course')}</th>
            <th>{indVerRename('Term', programId)}</th>
            <th>
              {t('year')} / {indVerRename('Level', programId)}
            </th>
            <th>{t('curriculum')}</th>
            <th>{t('assign_staff')}</th>
            <th> </th>
          </tr>
        </thead>
        <tbody>
          {courses.isEmpty() ? (
            <tr>
              <td colSpan="5">{t('no_course_found')}</td>
            </tr>
          ) : (
            courses.map((course) => (
              <CourseItem
                key={`${course.get('term')}-${course.get('year')}-${course.get(
                  'level_no'
                )}-${course.get('_course_id')}`}
                course={course}
                institutionCalendarId={institutionCalendarId}
                setMessage={setMessage}
                assignCoordinator={assignCoordinator}
                currentCalendar={currentCalendar}
                programId={programId}
              />
            ))
          )}
        </tbody>
      </Table>
    </div>
  );
}

function CourseItem({
  course,
  institutionCalendarId,
  setMessage,
  assignCoordinator,
  currentCalendar,
  programId,
}) {
  const [isEditable, setIsEditable] = useState(false);
  const [selectedCoordinator, setSelectedCoordinator] = useState(Map());
  const existingCoordinator = course.get('coordinators', Map());
  const staffs = getStaffs();
  const isShared = course.get('isShared');

  function getCoordinatorName(name) {
    if (name === undefined || name.isEmpty()) return '---';
    return formatFullName(name.toJS());
    //return `${name.get('first', '')} ${name.get('last', '')}`;
  }

  function handleEditCancelClick(isEdit) {
    setIsEditable(isEdit);
    if (isEdit) {
      setSelectedCoordinator(existingCoordinator);
    } else {
      setSelectedCoordinator(Map());
    }
  }

  function getStaffs() {
    const staffs = course.get('staffs', List());
    return staffs.map((staff) => {
      return Map({
        user_name: staff.get('name', Map()),
        _user_id: staff.get('_id'),
      });
    });
  }

  function handleCoordinatorChange(event) {
    const value = event.target.value;
    if (!value) return setSelectedCoordinator(Map());
    const coordinator = staffs.find((staff) => staff.get('_user_id') === value);
    if (!coordinator) return;
    setSelectedCoordinator(coordinator);
  }

  function handleClearSelection() {
    setSelectedCoordinator(Map());
  }

  function handleSaveClick() {
    if (!existingCoordinator.isEmpty()) {
      if (existingCoordinator.get('_user_id') === selectedCoordinator.get('_user_id')) {
        return setMessage('You have not made any change');
      }
    }
    const requestBody = {
      _course_id: course.get('_course_id'),
      term: course.get('term'),
      _user_id: selectedCoordinator.get('_user_id', ''),
      _institution_calendar_id: institutionCalendarId,
      year: course.get('year'),
      level_no: course.get('level_no'),
    };
    const coordinator = Map({
      user_name: selectedCoordinator.get('user_name'),
      term: course.get('term'),
      _user_id: selectedCoordinator.get('_user_id'),
    });
    assignCoordinator(
      requestBody,
      coordinator,
      handleEditCancelClick,
      institutionCalendarId,
      programId
    );
  }

  function showEdit(course) {
    if (isShared) {
      const name = course.getIn(['coordinators', 'user_name'], Map()) || Map();
      return name.isEmpty();
    }
    return true;
  }

  return (
    <tr className="table-row-item">
      <td>
        <div className="pt-1">
          {isShared && <span className="shared-course-chip mr-2">S</span>}
          {`${course.get('courses_number', '')} - ${course.get('courses_name', '')}${getVersionName(
            course
          )}`}
        </div>
      </td>
      <td>
        <div className="pt-1">{capitalize(course.get('term'))}</div>
      </td>
      <td>
        <div className="pt-1">
          {`Year ${course.get('year', '').split('year').join('')} / ${levelRename(
            course.get('level_no', ''),
            programId
          )}`}
        </div>
      </td>
      <td>
        <div className="pt-1">{course.get('curriculum', '')}</div>
      </td>
      <td>
        {isEditable ? (
          <div className="d-flex align-items-center">
            <div className="w-75">
              <FormControl fullWidth size="small" variant="standard">
                <Select
                  native
                  className="remove_hover"
                  value={selectedCoordinator.get('_user_id', '')}
                  onChange={handleCoordinatorChange}
                  MenuProps={{
                    PaperProps: { style: { maxHeight: 280 } },
                  }}
                >
                  <option className="remove_hover" value=""></option>
                  {staffs.map((staff) => (
                    <option
                      className="remove_hover"
                      key={staff.get('_user_id')}
                      value={staff.get('_user_id')}
                    >
                      {getCoordinatorName(staff.get('user_name', Map()))}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>
            {!selectedCoordinator.isEmpty() && (
              <div className="ml-2">
                <i
                  className="fa fa-times cursor-pointer"
                  aria-hidden="true"
                  onClick={handleClearSelection}
                ></i>
              </div>
            )}
          </div>
        ) : (
          <div>{getCoordinatorName(course.getIn(['coordinators', 'user_name'], Map()))}</div>
        )}
      </td>
      <td className="w-120 vertical-align-middle">
        {isEditable ? (
          <div className="d-flex justify-content-between">
            <div className="mr-2 digi-blue cursor-pointer text-uppercase" onClick={handleSaveClick}>
              {t('save')}
            </div>
            <div
              className="color-light-gray cursor-pointer text-uppercase"
              onClick={() => handleEditCancelClick(false)}
            >
              {t('cancel')}
            </div>
          </div>
        ) : (
          <div className="f-16 table-action-button-container">
            {currentCalendar &&
              CheckPermission(
                'tabs',
                'Schedule Management',
                'Assign Course Coordinator',
                '',
                'Course List',
                'Edit'
              ) &&
              showEdit(course) && (
                <i
                  className="fa fa-pencil cursor-pointer"
                  aria-hidden="true"
                  onClick={() => handleEditCancelClick(true)}
                ></i>
              )}
          </div>
        )}
      </td>
    </tr>
  );
}

CourseTable.propTypes = {
  courses: PropTypes.instanceOf(List),
  setMessage: PropTypes.func,
  assignCoordinator: PropTypes.func,
  institutionCalendarId: PropTypes.string,
  currentCalendar: PropTypes.bool,
  programId: PropTypes.string,
};

CourseItem.propTypes = {
  course: PropTypes.instanceOf(Map),
  assignCoordinator: PropTypes.func,
  institutionCalendarId: PropTypes.string,
  setMessage: PropTypes.func,
  currentCalendar: PropTypes.bool,
  programId: PropTypes.string,
};

export default CourseTable;
