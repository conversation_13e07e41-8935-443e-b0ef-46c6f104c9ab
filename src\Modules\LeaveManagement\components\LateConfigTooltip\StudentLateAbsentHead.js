import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import InfoImage from 'Assets/info.svg';
import { useDispatch, useSelector } from 'react-redux';
import { getAttendanceConfig } from '_reduxapi/leave_management/actions';
import { selectAttendanceLmsConfig } from '_reduxapi/leave_management/selectors';
import Tooltips from 'Widgets/FormElements/material/Tooltip';

export default function StudentLateAbsentHead() {
  const reducerData = useSelector(selectAttendanceLmsConfig);
  const range = reducerData.getIn(['getAttendanceData', 'lateConfig', 0, 'range'], List());
  const dispatch = useDispatch();
  useEffect(() => {
    if (!reducerData.isEmpty()) return;
    dispatch(getAttendanceConfig());
  }, []); //eslint-disable-line

  return (
    <Tooltips
      title={
        <div>
          <table className="p-2">
            <thead>
              <th className="text_no_wrap">Late Label</th>
              <th className="text_no_wrap px-2">No of Late</th>
              <th className="text_no_wrap ">No Of Absent</th>
            </thead>
            <tbody className="Gray_Neutral_100 ">
              {range.map((item, i) => (
                <tr className="white_border_bottom  last_child_border_remove" key={i}>
                  <td className="table_hash_gray py-2 text-capitalize ">
                    {item.get('lateLabel', '')}
                  </td>
                  <td className="table_hash_gray py-2 text-center">{item.get('noOfLate')}</td>
                  <td className="table_hash_gray py-2 text-center">{item.get('noOfAbsent', '')}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      }
    >
      <img className="pl-2 r emove_hover" src={InfoImage} alt="info" />
    </Tooltips>
  );
}
StudentLateAbsentHead.propTypes = {
  lateConfig: PropTypes.instanceOf(List),
};
