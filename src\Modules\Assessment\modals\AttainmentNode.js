import React, { useState } from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { List, Map } from 'immutable';
import ListSubheader from '@mui/material/ListSubheader';
import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import { MenuItem } from '@mui/material';
import CancelModal from 'Containers/Modal/Cancel';
import { useStylesFunction } from '../utils';
import { ucFirst } from 'utils';

const AttainmentNodeModal = ({ show, onClose, handleSave, attainmentNodeTypes, data }) => {
  const [edited, setEdited] = useState(false);
  const classes = useStylesFunction();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [nodeData, setNodeData] = useState(
    Map({
      nodeType: data.get('nodeType', ''),
      nodeName: data.get('nodeName', ''),
      nodePercent: data.get('nodePercent', ''),
    })
  );
  let nodeTypes = [];
  let subTypes = [];
  let assessmentTypes = [];
  attainmentNodeTypes.forEach((item) => {
    nodeTypes.push({
      name: item.get('name', ''),
      value: item.get('_id', ''),
      typeName: item.get('name', ''),
    });
    item.get('subTypes', List()).forEach((sType) => {
      subTypes.push({
        name: `${item.get('name', '')} - ${sType.get('name', '')}`,
        value: sType.get('_id', ''),
        typeName: sType.get('name', ''),
        parentId: item.get('_id'),
      });
      sType.get('assessmentTypes', List()).forEach((aType) =>
        assessmentTypes.push({
          name: `${item.get('name', '')} - ${sType.get('name', '')} - ${aType.get('name', '')}`,
          value: aType.get('_id', ''),
          typeName: aType.get('name', ''),
          parentId: item.get('_id'),
          subTreeId: sType.get('_id'),
          assessmentNode: true,
        })
      );
    });
  });

  const getNodeTypes = () => {
    return data.get('parentType', '') === ''
      ? nodeTypes.filter(
          (item) =>
            !data
              .get('levelsList', List())
              .find((sType) => item.value === sType.get('typeId', '')) &&
            (!data.get('subTypes', List()).size || item.value === data.get('nodeType', ''))
        )
      : [];
  };

  const getSubTypes = () => {
    let assessmentTypeNode = null;
    const parentType = data.get('parentType', '');
    const typeName = nodeTypes.filter((item) => item.value === parentType);
    const subTypeNode = data
      .get('subTypes', List())
      .some((item) => subTypes.find((sType) => sType.value === item.get('typeId', '')));
    const currentNode = subTypes.filter((item) => item.value === data.get('nodeType', ''));

    if (!subTypeNode && !currentNode.length && data.get('subTypes', List()).size > 0) {
      assessmentTypeNode = assessmentTypes.find(
        (item) => item.value === data.getIn(['subTypes', 0, 'typeId'], '')
      );
    }

    return !subTypeNode && (!parentType || typeName.length)
      ? subTypes.filter(
          (item) =>
            !data
              .get('levelsList', List())
              .find((sType) => item.value === sType.get('typeId', '')) &&
            (!typeName.length || item.parentId === parentType) &&
            (!data.get('subTypes', List()).size ||
              (assessmentTypeNode
                ? item.value === assessmentTypeNode.subTreeId
                : item.value === data.get('nodeType', '')))
        )
      : [];
  };

  const getAssessmentTypes = () => {
    const parentType = data.get('parentType', '');
    const parentNode = nodeTypes.filter((item) => item.value === parentType);
    const subTreeNode = subTypes.filter((item) => item.value === parentType);
    const assessmentTypeNode = data
      .get('subTypes', List())
      .some(
        (item) =>
          subTypes.find((sType) => sType.value === item.get('typeId', '')) ||
          assessmentTypes.find((aType) => aType.value === item.get('typeId', ''))
      );
    return !assessmentTypeNode
      ? assessmentTypes.filter(
          (item) =>
            !data
              .get('levelsList', List())
              .find((sType) => item.value === sType.get('typeId', '')) &&
            (!parentNode.length || item.parentId === parentType) &&
            (!subTreeNode.length || item.subTreeId === parentType)
        )
      : [];
  };

  const handleChange = (e, key) => {
    setNodeData(nodeData.set(key, e.target.value));
    setEdited(true);
  };

  const handleCancel = () => {
    edited ? setShowCancelModal(true) : onClose();
  };

  const getTypeName = () => {
    const nodeType = nodeData.get('nodeType', '');
    if (!nodeType) return Map();
    let typeName = nodeTypes.find((item) => item.value === nodeType);
    if (!typeName) typeName = subTypes.find((item) => item.value === nodeType);
    if (!typeName) typeName = assessmentTypes.find((item) => item.value === nodeType);
    return Map(typeName);
  };

  const onSaveNode = () => {
    const typeDetails = getTypeName();
    handleSave({
      typeName: typeDetails.get('typeName', ''),
      typeId: nodeData.get('nodeType', ''),
      weightage: parseInt(nodeData.get('nodePercent', '')),
      ...(typeDetails.get('assessmentNode', false) && {
        nodeName: nodeData.get('nodeName', '').trim(),
      }),
    });
  };

  return (
    <>
      <DialogModal show={show} onClose={handleCancel} maxWidth={'xs'} fullWidth={true}>
        <div className="w-100 p-4">
          <p className="mb-3 bold f-19">{data.get('type', 'Add')} Node</p>
          <div className="mt-2 mb-2">
            <label className="f-14 mb-1">Node Type</label>
            <FormControl
              fullWidth
              variant={'outlined'}
              size={'small'}
              className={classes.selectRoot}
            >
              <Select
                id="grouped-select"
                value={nodeData.get('nodeType', '')}
                onChange={(e) => handleChange(e, 'nodeType')}
              >
                {getNodeTypes().map((item) => (
                  <MenuItem key={item.value} value={item.value}>
                    {ucFirst(item.name)}
                  </MenuItem>
                ))}
                {getSubTypes().map((item) => (
                  <MenuItem key={item.value} value={item.value}>
                    {ucFirst(item.name)}
                  </MenuItem>
                ))}
                {getAssessmentTypes().length > 0 && <ListSubheader>Assessment Type</ListSubheader>}
                {getAssessmentTypes().map((item) => (
                  <MenuItem key={item.value} value={item.value}>
                    {ucFirst(item.name)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>

          {getTypeName().get('assessmentNode', false) && (
            <div className="mt-2 mb-2 ">
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                labelclass={'mb-1 f-14'}
                label={'Node Name'}
                placeholder={'Node Name'}
                value={nodeData.get('nodeName', '')}
                changed={(e) => handleChange(e, 'nodeName')}
              />
            </div>
          )}

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              labelclass={'mb-1 f-14'}
              maxLength={100}
              label={'Node Weightage (%)'}
              placeholder={'Node Weightage'}
              value={nodeData.get('nodePercent', '')}
              changed={(e) => {
                if (
                  e.target.value &&
                  (isNaN(e.target.value) || e.target.value < 1 || e.target.value > 100)
                )
                  return;
                handleChange(e, 'nodePercent');
              }}
            />
          </div>

          <div className="d-flex justify-content-end mt-3">
            <MButton variant="outlined" color="gray" className={'mr-2'} clicked={handleCancel}>
              Cancel
            </MButton>
            <MButton
              variant="contained"
              color="primary"
              disabled={
                !edited ||
                !nodeData.get('nodeType', '') ||
                (getTypeName().get('assessmentNode', false) && !nodeData.get('nodeName', '')) ||
                !nodeData.get('nodePercent', '')
              }
              clicked={onSaveNode}
            >
              Save
            </MButton>
          </div>
        </div>
      </DialogModal>

      <CancelModal showCancel={showCancelModal} setCancel={setShowCancelModal} setShow={onClose} />
    </>
  );
};

AttainmentNodeModal.propTypes = {
  show: PropTypes.bool,
  onClose: PropTypes.func,
  handleSave: PropTypes.func,
  attainmentNodeTypes: PropTypes.instanceOf(List),
  data: PropTypes.instanceOf(Map),
};

export default AttainmentNodeModal;
