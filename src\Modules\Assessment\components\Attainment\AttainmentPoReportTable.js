import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import dotIcon from 'Assets/dotIcon.svg';

import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { trimFractionDigits, ucFirst, indVerRename } from 'utils';
import { getBenchmarkValues } from '../../utils';
import AttainmentAchieved from './AttainmentAchieved';

const AttainmentPoReportTable = (props) => {
  const { courseProgramReport, selectedNode, selectValues } = props;
  const programId = selectValues.getIn(['program', 'value'], '');
  const getMarkColor = (details, percent) => {
    const filteredBenchMarkValue = getBenchmarkValues(
      details.get('typeId', ''),
      courseProgramReport
    );
    const markValue =
      courseProgramReport.get('attainmentValuesAs', '') === 'fixed'
        ? filteredBenchMarkValue.get('percentage', '')
        : filteredBenchMarkValue.get('min', '');
    const markColor = trimFractionDigits(percent) > markValue ? '#16A34A' : '#6B7280';
    return markColor;
  };

  const CourseDetails = ({ details }) => {
    const percent = details.get('percentage', 0);
    const level = details.get('level', '');

    const levelColor = courseProgramReport
      .get('levelColors', List())
      .find((item) => item.get('level', '') === level);

    const markColor = getMarkColor(details, percent);

    return (
      <div className="row align-items-center">
        <div className="col-6 pr-0">
          <p className="thHeaderReport" style={{ color: markColor }}>
            {percent !== '' && percent !== null && percent !== 0
              ? `${trimFractionDigits(percent)}%`
              : '--'}
          </p>
        </div>
        <div className="col-6">
          <div className="d-flex justify-content-center ">
            <p
              className="innerValue"
              {...(level &&
                levelColor && {
                  style: { backgroundColor: levelColor.get('color', ''), color: '#fff' },
                })}
            >
              {level.replace('Level', 'L') || '--'}
            </p>
          </div>
        </div>
      </div>
    );
  };
  CourseDetails.propTypes = {
    details: PropTypes.instanceOf(Map),
    tree: PropTypes.string,
  };

  return (
    selectedNode !== '-' && (
      <div className="border border-radious-6 mt-4 mb-5">
        <div className="attainment_table_plo">
          <table align="left">
            <thead>
              <tr>
                <th scope="col" className="borderNone cw_300">
                  <div className="cw_300">
                    <div className="row">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport text-left bold">Node</p>
                      </div>
                      <div className="col-6 pl-0">
                        <p className="thHeaderReport text-left bold">Overall Attainment</p>
                      </div>
                    </div>
                  </div>
                </th>
                {courseProgramReport.get('curriculumPLOs', List()).map((one, oneIndex) => (
                  <React.Fragment key={oneIndex}>
                    <td className="borderNone cw_150">
                      {' '}
                      <div className="cw_150">
                        <p className="thHeaderReport">
                          <Tooltips title={one.get('name', '')}>
                            <span className="thHeaderReport bold">
                              {indVerRename('PLO', programId)} {one.get('no', '')}
                            </span>
                          </Tooltips>
                        </p>
                      </div>
                    </td>
                  </React.Fragment>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr>
                <th className="attainment_border_bottom bg-gray cw_300">
                  <div className="cw_300">
                    <div className="row">
                      <div className="col-6 pr-0">
                        <p className="thHeaderReport text-left">Attainment</p>
                      </div>
                      <div className="col-6 pl-0">
                        <p className="thHeaderReport ">%</p>
                      </div>
                    </div>
                  </div>
                </th>
                {courseProgramReport.get('curriculumPLOs', List()).map((temp, i) => (
                  <td key={i} className="attainment_border_bottom bg-gray cw_150 text-center">
                    <div className="cw_150">%</div>
                  </td>
                ))}
              </tr>
              <tr>
                <th className="attainment_border_bottom bg-gray cw_300">
                  <div className="row">
                    <div className="col-6 pr-0">
                      <p className="thHeaderReport text-left">Avg of All Courses</p>
                    </div>
                    <div className="col-6 pr-0">
                      <CourseDetails
                        details={courseProgramReport.get('curriculumCoursesAverage', Map())}
                      />
                    </div>
                  </div>
                </th>
                {courseProgramReport.get('curriculumPLOs', List()).map((temp, i) => (
                  <td key={i} className={'attainment_border_bottom bg-gray'}>
                    <CourseDetails details={temp} />
                  </td>
                ))}
              </tr>

              {courseProgramReport.get('curriculumCourses', List()).map((item, index) => (
                <tr key={index}>
                  <th className="attainment_border_bottom bg-white cw_300">
                    <div className="cw_300">
                      <div className="row align-items-center">
                        <div className="col-6 pr-0">
                          <p className="thHeaderReport text-left f-14">
                            {item.get('coursesName', '')}
                          </p>
                          <span className="f-12">{ucFirst(item.get('year', ''))}</span>{' '}
                          <img src={dotIcon} alt="Deactivated" />
                          <span className="f-12 pl-1">{ucFirst(item.get('levelNo', ''))}</span>{' '}
                        </div>
                        <div className="col-6 pr-0">
                          <CourseDetails details={item} />
                        </div>
                      </div>
                    </div>
                  </th>
                  {courseProgramReport.get('curriculumPLOs', List()).map((temp, i) => {
                    let ploItem = item
                      .get('plo', List())
                      .filter((fItem, fIndex) => fItem.get('_id', '') === temp.get('_id', ''));

                    const percent = ploItem.getIn([0, 'percentage'], 0);
                    const markColor = getMarkColor(item, percent);

                    return (
                      <td key={i} className="attainment_border_bottom cw_150">
                        <div className="cw_150">
                          <div className="row align-items-center">
                            <div className="col-6 pr-0">
                              <p className="thHeaderReport" style={{ color: markColor }}>
                                {ploItem.getIn([0, 'percentage'], 0) !== '' &&
                                ploItem.getIn([0, 'percentage'], 0) !== null &&
                                ploItem.getIn([0, 'percentage'], '-') !== '-' &&
                                ploItem.getIn([0, 'percentage'], '-') !== 0
                                  ? `${trimFractionDigits(ploItem.getIn([0, 'percentage'], 0))}%`
                                  : '--'}
                              </p>
                            </div>
                            <div className="col-6">
                              <div className="d-flex justify-content-center ">
                                <p
                                  className="innerValue"
                                  {...(ploItem.getIn([0, 'level'], 0) &&
                                    courseProgramReport
                                      .get('levelColors', List())
                                      .find(
                                        (item) =>
                                          item.get('level', '') === ploItem.getIn([0, 'level'], 0)
                                      ) && {
                                      style: {
                                        backgroundColor: courseProgramReport
                                          .get('levelColors', List())
                                          .find(
                                            (item) =>
                                              item.get('level', '') ===
                                              ploItem.getIn([0, 'level'], 0)
                                          )
                                          .get('color', ''),
                                        color: '#fff',
                                      },
                                    })}
                                >
                                  {ploItem.getIn([0, 'level'], '--').replace('Level', 'L') || '--'}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <AttainmentAchieved />
      </div>
    )
  );
};
AttainmentPoReportTable.propTypes = {
  outcome: PropTypes.string,
  courseProgramReport: PropTypes.instanceOf(Map),
  selectValues: PropTypes.instanceOf(Map),
  selectedNode: PropTypes.string,
};
export default AttainmentPoReportTable;
