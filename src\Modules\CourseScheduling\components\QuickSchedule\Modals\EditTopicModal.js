import React, { useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import { List, Map } from 'immutable';
import { buttonSx, MuiDatePicker, MuiTimePicker, ScheduleLabel, SecondaryText } from '../utils';
import CustomSelect, { MuiSelect } from '../CustomSelect';
import CustomMultiSelect, { StaffSelect } from '../CustomMultiSelect';
import { differenceInMinutes } from 'date-fns';
import EditIcon from '@mui/icons-material/Edit';
import ErrorIcon from '@mui/icons-material/Error';

const titleSx = {
  padding: '11px 16px',
  color: '#374151',
};
const titleIconSx = {
  padding: '8px',
  backgroundColor: '#EFF9FB',
  borderRadius: '50%',
  marginRight: '12px',
  color: '#0064C8',
};

const subjectOptions = List();
const courseGroupOptions = List();
const deliveryGroupOptions = List();
const modeOptions = List();
const infrastructureOptions = List();
const staffOptions = List();

const EditTopicModal = ({ open, topicData, handleClose }) => {
  const [scheduleState, setScheduleState] = useState(Map());
  const courseStartDate = topicData.get('courseStartDate', '');
  const courseEndDate = topicData.get('courseEndDate', '');
  const startTime = scheduleState.get('startTime', '');
  const endTime = scheduleState.get('endTime', '');

  const sessionDuration = useMemo(() => {
    if (!startTime || !endTime) return 0;
    return differenceInMinutes(endTime, startTime);
  }, [startTime, endTime]);

  const handleChange = (key) => (val) => {
    if (key === 'mode') val = val.target.value;
    setScheduleState((prev) => prev.set(key, val));
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center">
          <Box display="flex" sx={titleIconSx}>
            <EditIcon />
          </Box>
          <div>
            <p>Editing</p>
            <Typography variant="body2">L1-Maternal and Child Health</Typography>
          </div>
        </Box>
      </DialogTitle>
      <DialogContent className="p-3">
        <Typography variant="body2" color="#374151">
          All fields mandatory
          <Typography variant="body2" component="span" color="#D97706">
            *
          </Typography>
        </Typography>

        <div className="row mt-2">
          <div className="col-12 mb-2">
            <CustomSelect
              label="Subject"
              options={subjectOptions}
              value={scheduleState.get('subject', '')}
              onChange={handleChange('subject')}
              searchPlaceholder="Search subject"
              toggleBtnText="Show scheduled subjects"
              emptyErrorMessage="No subjects found"
            />
          </div>

          <div className="col-12">
            <Box display="flex" gap={2}>
              <Box width={185} mb={1}>
                <ScheduleLabel label="Session date" />
                <MuiDatePicker
                  value={scheduleState.get('sessionDate', '')}
                  onChange={handleChange('sessionDate')}
                  minDate={courseStartDate}
                  maxDate={courseEndDate}
                />
              </Box>

              <div className="mb-2">
                <div className="d-flex align-items-end text-NewLightgray">
                  <Box width={165}>
                    <ScheduleLabel label="Start time" />
                    <MuiTimePicker value={startTime} onChange={handleChange('startTime')} />
                  </Box>

                  <span className="mx-2 mb-2">to</span>
                  <Box width={165}>
                    <ScheduleLabel label="End time" />
                    <MuiTimePicker
                      value={endTime}
                      onChange={handleChange('endTime')}
                      disabled={!startTime}
                    />
                  </Box>
                </div>
                {startTime && endTime && (
                  <Box mt="2px">
                    <SecondaryText fontSize={14} text={`${sessionDuration} minutes duration`} />
                  </Box>
                )}
              </div>
            </Box>
          </div>

          <div className="col-md-6 mb-2">
            <CustomMultiSelect
              label="Course groups"
              options={courseGroupOptions}
              value={scheduleState.get('courseGroups', List())}
              onChange={handleChange('courseGroups')}
              searchPlaceholder="Search group"
              toggleBtnText="Show scheduled groups"
              emptyErrorMessage="No groups found"
              showScheduledStatus
            />
          </div>
          <div className="col-md-6 mb-2">
            <CustomMultiSelect
              label="Delivery groups"
              options={deliveryGroupOptions}
              value={scheduleState.get('deliveryGroups', List())}
              onChange={handleChange('deliveryGroups')}
              searchPlaceholder="Search group"
              toggleBtnText="Show scheduled groups"
              emptyErrorMessage="No groups found"
              showScheduledStatus
            />
            <Box display="flex" alignItems="center" color="#D97706" mt={0.5}>
              <ErrorIcon className="f-16 mr-1" />
              <Typography fontSize={12}>M-L-1 scheduled in another session!</Typography>
            </Box>
          </div>

          <div className="col-md-6 mb-2">
            <ScheduleLabel label="Mode" />
            <MuiSelect
              placeholder="- Select -"
              options={modeOptions}
              value={scheduleState.get('mode', '')}
              changed={handleChange('mode')}
            />
          </div>
          <div className="col-md-6 mb-2">
            <CustomSelect
              label="Infrastructure (Optional)"
              options={infrastructureOptions}
              value={scheduleState.get('infrastructure', '')}
              onChange={handleChange('infrastructure')}
              searchPlaceholder="Search infrastructure"
              toggleBtnText="Show scheduled infrastructure"
              emptyErrorMessage="No infrastructure found"
              showScheduledStatus
            />
            <Box display="flex" alignItems="center" color="#D97706" mt={0.5}>
              <ErrorIcon className="f-16 mr-1" />
              <Typography fontSize={12}>Already scheduled in another session!</Typography>
            </Box>
          </div>

          <div className="col-12 mb-1">
            <StaffSelect
              options={staffOptions}
              value={scheduleState.get('staffs', List())}
              onChange={handleChange('staffs')}
              showScheduledStatus
            />
            <Box display="flex" alignItems="center" color="#D97706" mt={0.5}>
              <ErrorIcon className="f-16 mr-1" />
              <Typography fontSize={12}>Already scheduled in another session!</Typography>
            </Box>
          </div>
        </div>
      </DialogContent>
      <DialogActions className="p-3 border-top justify-content-between">
        <MButton variant="outlined" color="gray" sx={buttonSx} clicked={() => {}}>
          Reset
        </MButton>
        <div>
          <MButton variant="outlined" color="gray" sx={buttonSx} clicked={handleClose}>
            Cancel
          </MButton>
          <MButton
            variant="contained"
            color="primary"
            className="ml-2"
            sx={buttonSx}
            clicked={() => {}}
          >
            Save
          </MButton>
        </div>
      </DialogActions>
    </Dialog>
  );
};

EditTopicModal.propTypes = {
  open: PropTypes.bool,
  topicData: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
};

export default EditTopicModal;
