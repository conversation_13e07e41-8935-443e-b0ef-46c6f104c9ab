import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import { fromJS, List, Map } from 'immutable';
import {
  buttonSx,
  getScheduledOptions,
  MuiDatePicker,
  MuiMobileTimePicker,
  // MuiTimePicker,
  ScheduleLabel,
  SecondaryText,
} from '../utils';
import CustomSelect, { MuiSelect } from '../CustomSelect';
import CustomMultiSelect, { StaffSelect } from '../CustomMultiSelect';
import { addMinutes, differenceInMinutes } from 'date-fns';
import EditIcon from '@mui/icons-material/Edit';
import ErrorIcon from '@mui/icons-material/Error';
import {
  constructGroupOptions,
  getDeliveryGroups,
  getInfrastructures,
  getStaffs,
} from '../ScheduleDrawer';
import { useSelector } from 'react-redux';
import { selectAvailableList, selectCourseTopics } from '_reduxapi/course_scheduling/selectors';
import { getModes } from 'utils';

const titleSx = {
  padding: '11px 16px',
  color: '#374151',
};
const titleIconSx = {
  padding: '8px',
  backgroundColor: '#EFF9FB',
  borderRadius: '50%',
  marginRight: '12px',
  color: '#0064C8',
};

const EditTopicModal = ({ open, selectedCourse, topicData, handleClose }) => {
  const topics = useSelector(selectCourseTopics);
  const availableList = useSelector(selectAvailableList);
  const [scheduleState, setScheduleState] = useState(Map());
  const courseStartDate = new Date(selectedCourse.get('start_date', new Date()));
  const courseEndDate = new Date(selectedCourse.get('end_date', new Date()));
  const programId = scheduleState.get('programId', '');
  const topicId = scheduleState.get('sessionId', '');
  const deliverySymbol = scheduleState.get('deliverySymbol', '');
  const deliveryNo = scheduleState.get('deliveryNo', '');
  const deliveryTopic = scheduleState.get('deliveryTopic', '');
  const subjectId = scheduleState.get('subject', '');
  const startTime = scheduleState.get('startTime', '');
  const endTime = scheduleState.get('endTime', '');
  const courseGroupIds = scheduleState.get('courseGroups', List());
  const deliveryGroupIds = scheduleState.get('deliveryGroups', List());
  const mode = scheduleState.get('mode', '');
  const infraId = scheduleState.get('infrastructure', '');
  const staffIds = scheduleState.get('staffs', '');
  const selectedTerm = '';
  const selectedLevel = '';

  useEffect(() => {
    setScheduleState(topicData);
  }, [topicData]);

  const { subjectOptions, topicDuration } = useMemo(() => {
    const topicData = topics.find((item) => item.get('sessionId') === topicId);
    if (!topicData) return { subjectOptions: List(), topicDuration: 0 };

    const topicDuration = topicData.get('duration', 0);
    const subjectOptions = topicData
      .get('subjects', List())
      .map((subject) =>
        Map({ name: subject.get('subject_name', ''), value: subject.get('_subject_id', '') })
      );

    return { subjectOptions, topicDuration };
  }, [topics, topicId]);

  const { courseGroupOptions, deliveryGroupOptions } = useMemo(() => {
    const groups = availableList.get('student_group', List()).filter((studentGroup) => {
      return studentGroup.get('delivery_symbol') === deliverySymbol;
    });

    return constructGroupOptions(groups, programId);
  }, [availableList, programId, deliverySymbol]);

  const infraOptions = useMemo(() => {
    return getInfrastructures({
      subjectIds: List([subjectId]),
      mode,
      courseGroupOptions,
      courseGroupIds,
      availableList,
      selectedTerm,
      selectedLevel,
    });
  }, [
    subjectId,
    mode,
    courseGroupOptions,
    courseGroupIds,
    availableList,
    selectedTerm,
    selectedLevel,
  ]);

  const staffOptions = useMemo(() => {
    return getStaffs({ subjectIds: List([subjectId]), availableList });
  }, [availableList, subjectId]);

  const modeOptions = useMemo(() => fromJS(getModes(programId)), [programId]);

  const sessionDuration = useMemo(() => {
    if (!startTime || !endTime) return 0;
    return differenceInMinutes(endTime, startTime);
  }, [startTime, endTime]);

  const deliveryGroups = useMemo(() => {
    return getDeliveryGroups(deliveryGroupOptions, courseGroupIds);
  }, [deliveryGroupOptions, courseGroupIds]);

  const scheduledDeliveryGroups = useMemo(() => {
    return getScheduledOptions(deliveryGroups, deliveryGroupIds);
  }, [deliveryGroups, deliveryGroupIds]);

  const scheduledInfra = useMemo(() => {
    return getScheduledOptions(infraOptions, infraId);
  }, [infraOptions, infraId]);

  const scheduledStaffs = useMemo(() => {
    return getScheduledOptions(staffOptions, staffIds);
  }, [staffOptions, staffIds]);

  const handleChange = (key) => (val) => {
    if (key === 'mode') val = val.target.value;

    let updatedValue = scheduleState.set(key, val);
    if (key === 'startTime' && val) {
      const endTime = addMinutes(val, topicDuration);
      updatedValue = updatedValue.set('endTime', endTime);
    }

    setScheduleState(updatedValue);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center">
          <Box display="flex" sx={titleIconSx}>
            <EditIcon />
          </Box>
          <div>
            <p>Editing</p>
            <Typography variant="body2">
              {deliverySymbol}
              {deliveryNo} - {deliveryTopic}
            </Typography>
          </div>
        </Box>
      </DialogTitle>
      <DialogContent className="p-3">
        <Typography variant="body2" color="#374151">
          All fields mandatory
          <Typography variant="body2" component="span" color="#D97706">
            *
          </Typography>
        </Typography>

        <div className="row mt-2">
          <div className="col-12 mb-2">
            <CustomSelect
              label="Subject"
              options={subjectOptions}
              value={subjectId}
              onChange={handleChange('subject')}
              searchPlaceholder="Search subject"
              toggleBtnText="Show scheduled subjects"
              emptyErrorMessage="No subjects found"
              disabled
            />
          </div>

          <div className="col-12">
            <Box display="flex" gap={2}>
              <Box width={185} mb={1}>
                <ScheduleLabel label="Session date" />
                <MuiDatePicker
                  value={scheduleState.get('sessionDate', '')}
                  onChange={handleChange('sessionDate')}
                  minDate={courseStartDate}
                  maxDate={courseEndDate}
                />
              </Box>

              <div className="mb-2">
                <div className="d-flex align-items-end text-NewLightgray">
                  <Box width={165}>
                    <ScheduleLabel label="Start time" />
                    <MuiMobileTimePicker
                      value={startTime}
                      onChange={handleChange('startTime')}
                      showIcon
                    />
                  </Box>

                  <span className="mx-2 mb-2">to</span>
                  <Box width={165}>
                    <ScheduleLabel label="End time" />
                    <MuiMobileTimePicker
                      value={endTime}
                      onChange={handleChange('endTime')}
                      disabled={!startTime}
                      minTime={startTime}
                      showIcon
                    />
                  </Box>
                </div>
                {startTime && endTime && (
                  <Box mt="2px">
                    <SecondaryText fontSize={14} text={`${sessionDuration} minutes duration`} />
                  </Box>
                )}
              </div>
            </Box>
          </div>

          <div className="col-md-6 mb-2">
            <CustomMultiSelect
              label="Course groups"
              options={courseGroupOptions}
              value={courseGroupIds}
              onChange={handleChange('courseGroups')}
              searchPlaceholder="Search group"
              toggleBtnText="Show scheduled groups"
              emptyErrorMessage="No groups found"
              showScheduledStatus
              disabled
            />
          </div>
          <div className="col-md-6 mb-2">
            <CustomMultiSelect
              label="Delivery groups"
              options={deliveryGroups}
              value={scheduleState.get('deliveryGroups', List())}
              onChange={handleChange('deliveryGroups')}
              searchPlaceholder="Search group"
              toggleBtnText="Show scheduled groups"
              emptyErrorMessage="No groups found"
              showScheduledStatus
              disabled
            />
            {scheduledDeliveryGroups.size > 0 && (
              <Box display="flex" alignItems="center" color="#D97706" mt={0.5}>
                <ErrorIcon className="f-16 mr-1" />
                <Typography fontSize={12}>
                  {scheduledDeliveryGroups.join(', ')} scheduled in another session!
                </Typography>
              </Box>
            )}
          </div>

          <div className="col-md-6 mb-2">
            <ScheduleLabel label="Mode" />
            <MuiSelect
              placeholder="- Select -"
              options={modeOptions}
              value={mode}
              changed={handleChange('mode')}
              disabled
            />
          </div>
          <div className="col-md-6 mb-2">
            <CustomSelect
              label="Infrastructure (Optional)"
              options={infraOptions}
              value={scheduleState.get('infrastructure', '')}
              onChange={handleChange('infrastructure')}
              searchPlaceholder="Search infrastructure"
              toggleBtnText="Show scheduled infrastructure"
              emptyErrorMessage="No infrastructure found"
              showScheduledStatus
              disabled
            />
            {scheduledInfra.size > 0 && (
              <Box display="flex" alignItems="center" color="#D97706" mt={0.5}>
                <ErrorIcon className="f-16 mr-1" />
                <Typography fontSize={12}>Already scheduled in another session!</Typography>
              </Box>
            )}
          </div>

          <div className="col-12 mb-1">
            <StaffSelect
              options={staffOptions}
              value={scheduleState.get('staffs', List())}
              onChange={handleChange('staffs')}
              showScheduledStatus
              disabled
            />
            {scheduledStaffs.size > 0 && (
              <Box display="flex" alignItems="center" color="#D97706" mt={0.5}>
                <ErrorIcon className="f-16 mr-1" />
                <Typography fontSize={12}>Already scheduled in another session!</Typography>
              </Box>
            )}
          </div>
        </div>
      </DialogContent>
      <DialogActions className="p-3 border-top justify-content-between">
        <MButton variant="outlined" color="gray" sx={buttonSx} clicked={() => {}}>
          Reset
        </MButton>
        <div>
          <MButton variant="outlined" color="gray" sx={buttonSx} clicked={handleClose}>
            Cancel
          </MButton>
          <MButton
            variant="contained"
            color="primary"
            className="ml-2"
            sx={buttonSx}
            clicked={() => {}}
          >
            Save
          </MButton>
        </div>
      </DialogActions>
    </Dialog>
  );
};

EditTopicModal.propTypes = {
  open: PropTypes.bool,
  selectedCourse: PropTypes.instanceOf(Map),
  topicData: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
};

export default EditTopicModal;
