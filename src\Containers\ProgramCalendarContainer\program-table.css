.right-border {
  border-right: 1px solid #dee2e6 !important;
  text-align: center;
}
th.border-none {
  border: none;
  color: #2d2d2c;
  font-size: 13px;
}
.table {
  margin-bottom: 0rem;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  color: #2d2d2c;
}
td.program-tables {
  padding: 10px 3px 10px 3px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
}
th.program-tables {
  padding: 10px 3px 10px 3px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
}
td.red {
  background: red;
  color: white;
}
td.darkgray {
  background: #3d5170;
  color: white;
}
th.orange {
  background: #f37233;
  color: white;
}
td.orange {
  background: #f37233;
  color: white;
}
td.gray {
  background: #f9f9f9;
}
td.full-gray {
  background: #bfcaca;
}
td.full-gray {
  background: #bfcaca;
  padding: 26px 10px 10px 10px;
  border: 0px;
}
td.border-none {
  border: 0px;
  text-align: center;
  font-size: 13px;
}

td.light-gray {
  background: #e6e2e2;
  padding: 26px 10px 10px 10px;
  border: 0px;
}
table.bg-white {
  background: white;
}

.program-space {
  padding-right: 14px;
  padding-bottom: 14px;
}
.program-button {
  padding: 2px 5px 2px 5px;
  background: #f8f9fa;
  border-radius: 16px !important;
}
.program-scroll {
  overflow-x: scroll;
}

body {
  color: #3d5170;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.slide-in-left {
  -webkit-animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes slide-in-left {
  0% {
    -webkit-transform: translateX(-1000px);
    transform: translateX(-1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slide-in-left {
  0% {
    -webkit-transform: translateX(-1000px);
    transform: translateX(-1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-right {
  -webkit-animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

/* .sidenav {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 2;
  top: 0;
  left: 0;
  background-color: #fff;
  overflow-x: hidden;
  transition: 0.5s;
  box-shadow: 7px 0 5px -2px #88888830;
} */

.sidenav h6 {
  padding: 14px 8px 14px 32px;
  text-decoration: none;
  font-size: 14px;
  color: #000;
  display: block;
  transition: 0.3s;
  border-bottom: 1px solid #dac2c2;
}

.sidenav h6 div {
  font-size: 10px;
}

.sidenav i {
  display: inline-block;
  padding-left: 10px;
  padding-right: 15px;
  vertical-align: middle;
  float: right;
  color: #928f8f;
}
.sidenav a {
  padding: 8px 8px 18px 32px;
  text-decoration: none;
  font-size: 14px;
  color: #818181;
  display: block;
  transition: 0.3s;
  font-weight: 500;
  display: block;
  align-items: center;
}

.sidenav a:hover,
.offcanvas a:focus {
  color: #0047cc;
}

.closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px !important;
  margin-left: 50px;
}

#main {
  transition: margin-left 0.5s;
  padding: 16px;
}

@media screen and (max-height: 450px) {
  /* .sidenav {padding-top: 15px;} */
  /* .sidenav a {font-size: 18px;} */
}

.sidenav_icon {
  width: 15px;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .container {
    max-width: 1300px;
  }
}

.pl_30 {
  padding-left: 30px;
}

.pt_5 {
  padding-top: 5px;
}

.pd_4 {
  padding-top: 4px;
}

.ham_nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: block;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.ham_nav2 {
  float: right;
}

.wd_250 {
  width: 250px;
}

.wd_0 {
  width: 0px;
}

.ml_250 {
  margin-left: 250px;
  transition: 0.5s;
}
.ml_0 {
  margin-left: 0px;
  transition: 0.5s;
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .wd_0 {
    width: 0px;
    white-space: nowrap;
  }

  .ml_0 {
    margin-left: 0px;
    transition: 0.5s;
  }

  .m-ham {
    float: right;
  }

  .ml_250 {
    margin-left: 0px;
    transition: 0.5s;
  }
  .wd_250 {
    width: 250px;
    white-space: nowrap;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .wd_250 {
    width: 250px;
    white-space: nowrap;
  }
  .wd_0 {
    width: 0px;
    white-space: nowrap;
  }
}

/* @media only screen and (min-width: 768px) {
   
    
  }Medium devices (landscape tablets, 768px and up) */

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .wd_250 {
    width: 210px;
  }
  .wd_0 {
    width: 0px;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .wd_250 {
    width: 250px;
  }
  .wd_0 {
    width: 0px;
  }
}

/*header start */

.user-button {
  color: #8a8a8a;
  background-color: transparent;
  border-color: transparent;
}
.user-button:hover {
  color: #77797b;
  background-color: #e2e6ea36;
  border-color: #dae0e51c;
}
.show.user-button.dropdown-toggle {
  color: #212529;
  background-color: #dae0e53b;
  border-color: #dae0e53b;
}

.usericon {
  background: #d3d3d3;
  padding: 2px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
}
.user-name {
  padding-left: 5px;
  color: #8a8a8a;
}
.headerbar {
  padding: 15px 20px 17px 20px;
  background: #fff;
  background: linear-gradient(
    84.06deg,
    rgba(26, 123, 220, 0.85) 13%,
    rgba(86, 184, 255, 0.85) 107.28%
  );
  background-image: -webkit-linear-gradient(
    84.06deg,
    rgba(26, 123, 220, 0.85) 13%,
    rgba(86, 184, 255, 0.85) 107.28%
  );
}

.side-layer {
  border-left: 1px solid #d4cece9c;
  margin-top: -10px !important;
  margin-bottom: -6px !important;
  padding-right: 20px;
}

/*header end */

/* main start */

/* .main{
    background: #f5f6f8;
  } */

.header-bottom {
  text-align: left;
  padding-top: 20px;
}
.header-bottom span {
  font-size: 13px;
  color: #818ea3;
}

.dash-inner {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 17px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}

.dash-inner span {
  font-size: 13px;
}
.dash-inner h3 {
  font-size: 30px;
  margin-bottom: 0px;
}
.dash-inner h3 b {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 0px;
}

.show.btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e536;
  border-color: #d3d9df14;
}

.btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0rem rgba(87, 89, 90, 0.15);
}

.center-dropdown {
  padding: 10px 10px 0px 0px;
  background: transparent;
}
.status {
  font-size: 14px;
  border-radius: 7px;
  background-color: #a9b9c66e;
  color: #878a8e;
  padding: 7px;
  font-weight: bold;
}

.dash-content h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 0px;
  color: #797b7a;
}
.outter {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 17px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}

.bar1,
.bar2 {
  width: 18px;
  height: 2px;
  background-color: #8c8585c4;
  margin: 5px 0px;
}

hr.line-border {
  margin-left: -35px;
  margin-right: -35px;
}

.img-manage {
  background: #d3d3d3;
  padding: 2px;
  border-radius: 50%;
  width: 40px;
  /* height: 30px; */
}
.manage {
  font-size: 14px;
  color: #939596;
}
.managetext h3 {
  font-size: 12px;
}
.managetext b {
  font-size: 12px;
}
.managetext span {
  font-size: 12px;
  color: #818288;
}
.managebutton {
  border: 1px solid #e4e2e2;
  border-radius: 3px;
  background-color: #ffffff;
  color: #77797b;
}
.si {
  border-right: 2px solid #becad6;
  background-color: white;
}

.btn-group-sm > .btn,
.btn-sm {
  padding: 1px 9px 2px 9px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 0.2rem;
}
button#bg-nested-dropdown {
  background: #ffffff;
  color: #212529;
  border: transparent;
  box-shadow: 0 0 0 0rem rgba(87, 89, 90, 0.15);
  outline: 0;
}

button#bg-nested-dropdown.dropdown-toggle::after {
  border-bottom: 0;
  border-left: 0em solid transparent;
  margin-left: 1px;
  margin-right: 2px;
  border: none;
}

#dropdown-item-button {
  background: #f8f9fa;
  color: black;
  border: transparent;
  box-shadow: 0 0 0 0rem rgba(87, 89, 90, 0.15);
  outline: 0;
  font-size: 12px;
}

#dropdown-item-button::after {
  margin-left: 1.255em;
}
.footer-sort {
  padding-bottom: 0.8cm;
}
.managebutton::before {
  padding-left: 9px;
  font-size: 12px;
}
.green {
  color: #3bc34e;
}
.red {
  color: #e66363;
}
.full-view {
  font-size: 12px;
  color: #323c47;
}

.img-activity {
  padding-top: 8px;
}

.tables {
  padding-top: 1cm;
}

.tables h3 {
  font-size: 24px;
  padding-bottom: 20px;
  padding-top: 20px;
  color: #797b7a;
}

.dash-table {
  background: white;
}

thead.th-change {
  background: #f4f5f5;
}
/* tbody span {
    color: #868e96;
  } */
.table th {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e629;
  font-size: 14px;
  color: #a9b9c6;
}
#dropdown-table.dropdown-toggle::after {
  border-right: 0em solid transparent;
  border-bottom: 0;
  border-left: 0em solid transparent;
}
.table-dropdown {
  margin-top: -4px;
  background: transparent;
}
.vu {
  padding: 3px 11px 1px 0px;
}

.page-link {
  position: relative;
  display: block;
  padding: 5px 11px;
  margin-left: -1px;
  line-height: 1.25;
  color: #8e98a1;
  border-radius: 50%;
  border: 1px solid #dee2e600;
  background-color: #fff0;
}
.page-item.active .page-link {
  background-color: #0047cc;
  border-color: #0047cc;
}

/* main start */

/* modal container start */

.modal-top {
  color: #9ba5b6;
}

.calender {
  position: absolute;
  padding: 19px 13px 17px 25px;
  color: #c4c6cb;
  z-index: 99;
}
.icon-form {
  padding: 15px 13px 15px 61px;
}

.icon-form-ar {
  padding: 15px 36px 15px 13px;
}
.form-radio {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: inline-block;
  position: relative;
  background-color: #ffffff;
  color: #fff;
  top: 10px;
  height: 27px;
  width: 27px;
  border: 1px solid #3d5170;
  border-radius: 5px;
  cursor: pointer;
  margin-right: 7px;
  outline: none;
}
.form-radio:checked::before {
  position: absolute;
  font: 14px/0.8 'Open Sans', sans-serif;
  left: 5px;
  top: 8px;
  content: '\f00c';
  font-family: FontAwesome;
  transform: rotate(7deg);
}
.form-radio:hover {
  background-color: #d5cacb8c;
}
.form-radio:checked {
  background-color: #0047cc;
}

.radio-label {
  padding-right: 29px;
  padding-left: 13px;
  padding-top: 13px;
}
button.save {
  padding: 15px 58px 17px 58px;
  font-size: 17px;
  border-radius: 17px;
}
.date-pickers {
  border: 1px solid #ccc6c6;
  width: 130%;
  position: absolute;
  padding: 20px;
  background: #fff;
}

/* .modal-header{
  font-size: 22px;
  padding: 0px;
  border-bottom: 1px solid #e9ecef00;
  
  } */

/* .modal-header::before {
    content: "\f066";
    font-family: FontAwesome;
   
  }
  
  .modal-header::after {
    content: "\f00d";
    font-family: FontAwesome;
    cursor: pointer;
   } */

.calender-icons::before {
  content: '\f053';
  font-family: FontAwesome;
  padding-right: 52px;
}

.calender-icons::after {
  content: '\f054';
  font-family: FontAwesome;
  padding-left: 52px;
}

.modal-dialog {
  /* max-width: 60%;
    margin: 5cm 1cm 1cm 9cm; */
}

.modal-backdrop {
  background-color: #f5efef;
}

/* modal container end */

/* input type 2 start */

.input2 {
  border-bottom: 1px solid #ced4da;
  display: block;
  width: 100%;
  padding: 0.375rem 0rem;
  font-size: 20px;
  line-height: 1.5;
  color: #929394;
  background-color: #fff;
  background-clip: padding-box;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  border-left: 1px solid #fff;
  border-top: 1px solid #fff;
  border-right: 1px solid #fff;
  margin-bottom: 24px;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
}

.input2:focus {
  border-bottom: 1px solid #c3c6ca;
}

.modal-form {
  padding: 9px 13px 9px 9px;
}

.modal-position {
  padding-right: 12px;
  padding-left: 5px;
}
.time-label {
  font-size: 15px;
  padding-left: 5px;
}
.model-box {
  padding: 10px !important;
  border: 1px solid #ced4da;
}

.model-box1 {
  border: 1px solid #ced4da;
  height: calc(46px + 2px) !important;
}

.save-model {
  text-align: center !important;
  padding-top: 20px;
}

/* input type 2 end */

/* body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
} */

/*  custome font size */

/* custome font size */

@media only screen and (min-width: 320px) and (max-width: 767px) {
  .modal-header {
    font-size: 15px !important;
  }
  .form-radio {
    top: 5px !important;
    height: 17px !important;
    width: 17px !important;
  }
  .form-radio:checked::before {
    font: 12px/0.8 'Open Sans', sans-serif !important;
    left: 4px !important;
    top: 3px !important;
  }
  .radio-label {
    padding-right: 5px !important;
    padding-left: 0px !important;
    padding-top: 13px !important;
    font-size: 13px;
  }
  .date-pickers {
    width: 100% !important;
    padding: 10px !important;
  }
  /* .modal-dialog {
    max-width: 95% !important ;
    margin: 1cm !important;
  } */

  .model-box {
    padding: 10px !important;
    border: 1px solid #ced4da;
    font-size: 11px;
  }

  .model-box1 {
    border: 1px solid #ced4da;
    height: calc(46px + 2px) !important;
    font-size: 11px;
  }
}

@media only screen and (min-width: 320px) and (max-width: 359px) {
  .dash-inner h3 b {
    font-size: 13px !important;
  }

  .dash-inner span {
    font-size: 12px !important;
  }
  .center-dropdown {
    padding: 2px 10px 0px 0px !important;
  }
  .dash-inner h3 {
    font-size: 21px !important;
  }
  .headerbar {
    padding: 10px 10px 23px 11px !important;
  }
  .nav-item {
    padding-left: 0px !important;
  }

  .tables h3 {
    font-size: 14px !important;
  }
  .vu {
    padding: 7px 10px 1px 0px !important;
    font-size: 12px !important;
  }
  .btn-group-sm > .btn,
  .btn-sm {
    font-size: 11px !important;
  }
  b.pl-3 {
    padding-left: 0px !important;
  }
}

@media only screen and (min-width: 360px) and (max-width: 767px) {
  .dash-inner h3 b {
    font-size: 16px !important;
  }
  .m-ham {
    float: right;
  }
  .dash-inner span {
    font-size: 12px !important;
  }
  .center-dropdown {
    padding: 2px 10px 0px 0px;
  }
  .dash-inner h3 {
    font-size: 22px !important;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .headerbar {
    padding: 10px 10px 23px 11px !important;
  }
  .nav-item {
    padding-left: 0px !important;
  }
  b.pl-3 {
    padding-left: 0px !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 900px) {
  .ml_250 {
    margin-left: 200px !important;
  }
  .wd_250 {
    width: 230px !important;
  }
  .dash-inner span {
    font-size: 10px !important;
  }
  .dash-inner h3 {
    font-size: 19px !important;
  }
  .dash-inner h3 b {
    font-size: 11px !important;
  }
  .center-dropdown {
    padding: 0px 10px 0px 0px !important;
  }
  b.pl-3 {
    padding-left: 0px !important;
  }
  .date-pickers {
    width: 104% !important;
  }
  /* .modal-dialog {
    max-width: 95% !important;
    margin: 1cm !important;
  } */
}

@media only screen and (min-width: 990px) and (max-width: 1200px) {
  .ml_250 {
    margin-left: 200px !important;
  }
  .wd_250 {
    width: 200px !important;
  }
  .date-pickers {
    width: 100% !important;
  }
  /* .modal-dialog {
    max-width: 95% !important ;
    margin: 1cm !important;
  } */
}

.fr::after {
  content: '\f054';
  font-family: FontAwesome;
  padding-left: 52px;
}

.programAddCoursesCard {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 17px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}
.addItemColor {
  color: #c4c6cb;
}
.pl-8 {
  padding-left: 8px;
}
.pl-60 {
  padding-left: 60px;
}
.pr-60 {
  padding-right: 60px;
}
.addButton {
  background-color: white !important;
  /* position: fixed; */
  border: 1px solid #ebeced;
  padding: 12px 12px;
  text-align: center;
  text-decoration: none;
  display: flex;
  align-items: center;
  /* margin: 4px 2px; */
  cursor: pointer;
  width: 100%;
}
.custom-range {
  width: 100%;
  height: 1.4rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none !important;
  -moz-appearance: none;
  appearance: none;
}

.calendarFormRadio {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: inline-block;
  position: relative;
  background-color: #ffffff;
  color: #fff;
  top: 4px;
  height: 20px;
  width: 20px;
  border: 1px solid #707070;
  border-radius: 5px;
  cursor: pointer;
  outline: none;
}
.calenderIconPadding {
  margin: 16px 0px 0px 24px;
  color: #c4c6cb;
}
.weekPadding {
  height: 50px !important;
  width: 60%;
  border-radius: 0px;
  padding: 13px 13px 9px 46px;
}
.calendarFormRadio:checked::before {
  position: absolute;
  font: 10px/0.8 'Open Sans', sans-serif;
  left: 4px;
  top: 4px;
  content: '\f00c';
  font-family: FontAwesome;
  transform: rotate(45deg);
  transform: rotate(7deg);
}
.calendarFormRadio:hover {
  border: 1px solid #3d5170;
  background-color: #d5cacb8c;
}
.calendarFormRadio:checked {
  background-color: #0079ff;
}
.calendarFormRadio:disabled {
  background-color: #c8c4c4;
  cursor: not-allowed;
}

.radio-label {
  padding-right: 29px;
  padding-left: 13px;
  padding-top: 13px;
}
.weekDropDown {
  -webkit-appearance: none;
  padding: 4px 8px 0px 53px;
  height: 44px !important;
  border-radius: 0;
  border: 1px solid #ebeced;
}
.arrowDownIcon {
  margin: 12px 0px 0px 74%;
  font-size: 20px;
  color: #c4c6cb;
}
.nextButton {
  background-color: white !important;
  /* position: fixed; */
  border: 1px solid #0047cc;
  border-radius: 10px;
  color: #0047cc;
  padding: 10px 12px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  /* margin: 4px 2px; */
  cursor: pointer;
  width: 50%;
}
.saveButton {
  background-color: #0047cc !important;
  /* position: fixed; */
  border: none;
  border-radius: 10px;
  color: white;
  padding: 14px 6px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  /* margin: 4px 2px; */
  cursor: pointer;
  width: 60%;
}
.programCoursesCard {
  background-color: #fff;
  text-align: left;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  height: 5px;
  border-radius: 5px;
  background: #0047cc;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.slider:hover {
  opacity: 1;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #0047cc;
  cursor: pointer;
  border: 2px solid#ffffff;
}

.slider::-moz-range-thumb {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #0047cc;
  cursor: pointer;
}
.addItemBorder {
  border: 1px solid #ebeced;
  height: 50px;
}
.bookIcon {
  padding-left: 26px;
  padding-top: 13px;
}
.addIcon {
  float: right;
  padding-right: 18px;
  padding-top: 12px;
}

.addcourse {
  color: #0047cc;
  align-items: center;
  padding: 4px 26px 4px;
  text-decoration: none;
  display: inline-block;
  font-size: 11px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 5px;
  border: solid 1px #074bc9;
  background: white;
}
.weekPadding option {
  font-size: 18px;
}
@media only screen and (min-width: 320px) and (max-width: 767px) {
  .weekPadding {
    width: 100% !important;
  }
  .nextButton {
    width: 100% !important;
  }
  .saveButton {
    width: 100% !important;
  }
  .programAddCoursesCard {
    padding: 17px 18px 17px 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 1200px) {
  .weekPadding {
    width: 100% !important;
  }
  .nextButton {
    width: 100% !important;
  }
  .saveButton {
    width: 100% !important;
  }
}
