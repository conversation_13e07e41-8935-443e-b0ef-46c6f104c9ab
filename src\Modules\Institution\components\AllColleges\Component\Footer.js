import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Link } from 'react-router-dom';

import Exit from 'Assets/exit.png';
import { eString } from 'utils';
function CollegeListFooter({ college }) {
  return (
    <>
      <div className="p-1" />
      <div className="border-top" />

      {/* <div className="float-left pb-3 pl-3">
        <span className="pr-3">
          <span className="badge badge-light badge_college ">30 Programs</span>
        </span>
        <span className="pr-3">
          <span className="badge badge-light badge_college">59 Departments</span>
        </span>
        <span className="pr-3">
          <span className="badge badge-light badge_college">100 Subjects</span>
        </span>
      </div> */}
      <div className="float-right pt-2">
        <Link
          to={`/i/${eString(college.get('_id', ''))}/${eString(college.get('name', ''))}/dashboard`}
          target="_blank"
          rel="noopener noreferrer"
        >
          <div className="d-flex pl-2">
            <div className="">
              <img className="pl-2 pr-2 text-blue" alt="" src={Exit} />
            </div>
            <p className="text-blue bold pt-2_3 remove_hover mr-3  f-14">
              <Trans i18nKey={'view'}></Trans>
            </p>
          </div>
        </Link>
      </div>

      <div className="clearfix"></div>
    </>
  );
}

CollegeListFooter.propTypes = {
  college: PropTypes.object,
};

export default CollegeListFooter;
