import React, { useContext, useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { useStylesFunction } from 'Modules/Styles/Utils';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import { Badge } from '@mui/material';
import Filter from './Filter';
import { getDepartments, getPrograms, getSubjects } from '../../utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { Trans } from 'react-i18next';
import i18n from '../../../../i18n';
function DepartmentTableHeader({
  search,
  showShared,
  sharedRadio,
  setSharedRadio,
  fetchIndependentDepartmentApi,
  filterData,
}) {
  const classes = useStylesFunction();
  const department = useContext(parentContext.departmentContext);
  const { departmentTabValue, departmentDetails, translateInput } = department;

  const {
    departmentFilter,
    setDepartmentFilter,
    programFilter,
    setProgramFilter,
    subjectFilter,
    setSubjectFilter,
    sharedWithFilter,
    setSharedWithFilter,
    sharedWithFromFilter,
    setSharedWithFromFilter,
  } = filterData;

  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedFilter, setSelectedFilter] = useState('');
  const initialFilterRender = useRef(true);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getFilterState = (name) => {
    if (name === 'department') return departmentFilter;
    else if (name === 'program') return programFilter;
    else if (name === 'subject') return subjectFilter;
    else if (name === 'sharedWith') return sharedWithFilter;
    else if (name === 'sharedWithFrom') return sharedWithFromFilter;
  };

  const onSelect = (event, name) => {
    const filterState = getFilterState(name);
    let temp = [];
    temp = filterState.includes(event)
      ? filterState.filter((item) => item !== event)
      : [...filterState, event];
    if (name === 'department') return setDepartmentFilter(temp);
    else if (name === 'program') return setProgramFilter(temp);
    else if (name === 'subject') return setSubjectFilter(temp);
    else if (name === 'sharedWith') return setSharedWithFilter(temp);
    else if (name === 'sharedWithFrom') return setSharedWithFromFilter(temp);
  };

  const onRadioChange = (event) => {
    setSharedRadio(event);
    setSharedWithFilter([]);
    setSharedWithFromFilter([]);
  };

  useEffect(() => {
    if (initialFilterRender.current) {
      initialFilterRender.current = false;
    } else {
      fetchIndependentDepartmentApi();
    }
  }, [departmentFilter, programFilter, subjectFilter, sharedWithFilter, sharedWithFromFilter]); // eslint-disable-line

  return (
    <>
      <div className="d-flex justify-content-start">
        <div className={`${classes.heading}`}>
          <span className="mr-2">{getShortString(translateInput.departmentName, 15)} </span>
          <span className="mr-2">
            <Badge
              badgeContent={departmentFilter.length}
              color="error"
              overlap="rectangular"
            ></Badge>
          </span>
          <span className="digi-vertical-middle">
            <ArrowDropDownIcon
              onClick={(e) => {
                handleClick(e);
                setSelectedFilter('department');
              }}
            />
          </span>
        </div>
        {selectedFilter === 'department' && (
          <Filter
            anchorEl={anchorEl}
            options={getDepartments(departmentDetails, departmentTabValue)}
            selectedValues={departmentFilter}
            allFunc={{ handleClose, onSelect }}
            header={getShortString(translateInput.departmentName, 15)}
            selectedFilter={selectedFilter}
          />
        )}
        {departmentTabValue === 'academic' && (
          <div className={classes.secondaryHeading}>
            <span className="mr-2"> {getShortString(translateInput.program, 15)} </span>
            <span className="mr-2">
              <Badge
                badgeContent={programFilter.length}
                color="error"
                overlap="rectangular"
              ></Badge>
            </span>
            <span className="digi-vertical-middle">
              <ArrowDropDownIcon
                onClick={(e) => {
                  handleClick(e);
                  setSelectedFilter('program');
                }}
              />
            </span>
          </div>
        )}
        {selectedFilter === 'program' && (
          <Filter
            anchorEl={anchorEl}
            options={getPrograms(departmentDetails)}
            selectedValues={programFilter}
            allFunc={{ handleClose, onSelect }}
            header={getShortString(translateInput.program, 15)}
            selectedFilter={selectedFilter}
          />
        )}
        <div className={classes.subjectHeading}>
          <span className="mr-2"> {getShortString(translateInput.subjectName, 15)} </span>
          <span className="mr-2">
            <Badge badgeContent={subjectFilter.length} color="error" overlap="rectangular"></Badge>
          </span>
          <span className="digi-vertical-middle">
            <ArrowDropDownIcon
              onClick={(e) => {
                handleClick(e);
                setSelectedFilter('subject');
              }}
            />
          </span>
        </div>

        {selectedFilter === 'subject' && (
          <Filter
            anchorEl={anchorEl}
            options={getSubjects(departmentDetails, departmentTabValue)}
            selectedValues={subjectFilter}
            allFunc={{ handleClose, onSelect }}
            header={getShortString(translateInput.subjectName, 15)}
            selectedFilter={selectedFilter}
          />
        )}

        <div className={classes.thirdHeading}>
          <span className={showShared ? 'mr-3' : 'mr-2'}>
            <Trans i18nKey={'reports_analytics.shared_with'} />{' '}
            {showShared && departmentTabValue === 'academic'
              ? i18n.t('reports_analytics.from')
              : ''}
          </span>
          <span className="mr-2">
            <Badge
              overlap="rectangular"
              badgeContent={!showShared ? sharedWithFilter.length : sharedWithFromFilter.length}
              color="error"
            ></Badge>
          </span>

          <span className="digi-vertical-middle">
            <ArrowDropDownIcon
              onClick={(e) => {
                handleClick(e);
                setSelectedFilter(showShared ? 'sharedWithFrom' : 'sharedWith');
              }}
            />
          </span>
        </div>

        {(selectedFilter === 'sharedWith' || selectedFilter === 'sharedWithFrom') && (
          <Filter
            anchorEl={anchorEl}
            options={getPrograms(departmentDetails)}
            selectedValues={showShared ? sharedWithFromFilter : sharedWithFilter}
            allFunc={{ handleClose, onSelect, onRadioChange }}
            header={showShared && departmentTabValue === 'academic' ? 'Only Show' : 'Shared With'}
            selectedFilter={selectedFilter}
            sharedRadio={sharedRadio}
            showShared={showShared}
            departmentTabValue={departmentTabValue}
          />
        )}

        <div className={classes.ActionHeading}>
          <Trans i18nKey={'action'} />{' '}
        </div>
      </div>
    </>
  );
}
DepartmentTableHeader.propTypes = {
  search: PropTypes.string,
  showShared: PropTypes.bool,
  fetchIndependentDepartmentApi: PropTypes.func,
  filterData: PropTypes.object,
  sharedRadio: PropTypes.string,
  setSharedRadio: PropTypes.func,
};

export default DepartmentTableHeader;
