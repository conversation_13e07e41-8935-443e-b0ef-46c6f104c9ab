import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Button } from 'react-bootstrap';
import { List, Map } from 'immutable';
import moment from 'moment';
import { startOfDay } from 'date-fns';
import { t } from 'i18next';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import {
  selectAddList,
  selectLocations,
  selectScheduleStatus,
  selectStaffList,
  selectReviewerList,
  selectLeave,
  selectLeaveList,
} from '../../../_reduxapi/leave_management/selectors';
import { selectUserId } from '../../../_reduxapi/Common/Selectors';
import Input from '../../../Widgets/FormElements/Input/Input';
//import Warning from '../../../Assets/alert5.png';

import * as actions from '../../../_reduxapi/leave_management/actions';
import ApproveReportModal from '../modal/ApproveReportmodal';
import { getLang } from 'utils';
const Leavetype = [
  ['Noticed', 'noticed'],
  ['UnNoticed', 'unnoticed'],
];
const DATES = [
  { type: 'startDate', label: 'Start Date' },
  { type: 'endDate', label: 'End Date' },
];
class ReportAbsenceSuspenseLeave extends Component {
  constructor(props) {
    super(props);
    this.state = {
      searchView: false,
      removalIcon: false,
      searchText: '',
      name: '',
      selectedId: 0,
      noticed: true,
      selectedname: Leavetype[0][1],
      selectStaffId: null,
      reason: null,
      startDate: '',
      endDate: '',
      selectedAttachment: null,
      selectedAttachmentUrl: null,
      substituteStaff: Map(),
      show: false,
      flow: null,
      req: null,
      populated: null,
      id: null,
      comment: null,
      personid: null,
    };
  }
  componentDidMount() {
    this.props.setBreadCrumbName(t('leaveManagement.staff_report_absence_breadcrumb'));
    let search = window.location.search;
    let params = new URLSearchParams(search);
    var _id = params.get('_id');
    this.props.reviewer('report_absence', 'staff', _id);
    const id = this.props.match.params.id;

    this.interval = setInterval(() => {
      const { reviewerList } = this.props;
      if (reviewerList && reviewerList.size > 0) {
        const list = reviewerList
          .filter((item) => item.get('_id') === id)
          .reduce((_, el) => el, Map());
        const name =
          list.getIn(['user_data', 'name', 'first'], '') +
          list.getIn(['user_data', 'name', 'middle'], '') +
          list.getIn(['user_data', 'name', 'last'], '');

        const startDate = moment(list.get('from', '')).format('DDMMMYYYY');
        const endDate = moment(list.get('to', '')).format('DDMMMYYYY');
        this.fetchScheduleStatus(list.get('from', ''), list.get('to', ''));
        this.setState({
          id,
          selectStaffId: list.get('_user_id', null),
          name: name ? name : '',
          noticed: list.get('is_noticed', false),
          startDate: list.get('from', '') !== '' ? new Date(startDate) : '',
          endDate: list.get('to', '') !== '' ? new Date(endDate) : '',
          reason: list.get('reason', ''),
          selectedAttachment: list.get('_leave_reason_doc', null),
          selectedAttachmentUrl: list.get('_leave_reason_doc_url', null),
          personid: list.getIn(['created_by', '_person_id'], null),
        });
        clearInterval(this.interval);
      }
    }, 500);
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  static getDerivedStateFromProps(props, state) {
    if (props.leave.isEmpty()) return { populated: false };

    if (!state.populated) {
      const { leave, userId, activeInstitutionCalendar } = props;
      props.getScheduleStatus({
        institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
        userId,
        startDate: leave.get('from'),
        endDate: leave.get('to'),
      });
      const substituteStaff = leave.get('session', List()).reduce((acc, s) => {
        if (s.get('session_no')) {
          return acc.set(s.get('session_no'), s);
        }
        return acc;
      }, Map());
      return {
        categoryId: leave.getIn(['leave_category', '_leave_category_id'], ''),
        typeId: leave.getIn(['leave_type', '_leave_type_id'], ''),
        startDate: new Date(leave.get('from')),
        endDate: new Date(leave.get('to')),
        startTime: new Date(leave.get('from')),
        endTime: new Date(leave.get('to')),
        reason: leave.get('reason'),
        selectedAttachment: leave.get('_leave_reason_doc', null),
        selectedAttachmentUrl: leave.get('_leave_reason_doc_url', null),
        substituteStaff,
        populated: true,
      };
    }
    return null;
  }
  getMinDate(type) {
    const { startDate } = this.state;
    if (type === 'startDate') return startOfDay(new Date());
    return startDate ? startOfDay(startDate) : startOfDay(new Date());
  }
  modalClose = () => {
    this.setState({
      searchView: !this.state.searchView,
      name: '',
      removalIcon: false,
      selectedId: 0,
      searchText: '',
    });
  };
  handleClickback = () => {
    this.props.history.goBack();
  };

  handleSelect = (e) => {
    if (e.target.value === 'noticed') {
      this.setState({ noticed: true, selectedname: e.target.value });
    } else if (e.target.value === 'unnoticed') {
      this.setState({ noticed: false, selectedname: e.target.value });
    } else {
      this.setState({ reason: e.target.value });
    }
  };

  removeicon = () => {
    this.setState({
      name: '',
      removalIcon: false,
      selectedId: 0,
      searchText: '',
    });
  };

  handleFileChange(files) {
    if (!files) files = [];
    this.setState({
      selectedAttachment: files.length ? files[0] : null,
    });
  }

  changeHandler = (value, id, name, firstname, middlename, lastname, staffid) => {
    if (value) {
      this.setState({
        selectedId: id,
        name: name,
        selectStaffId: staffid,
      });
    }
  };

  fetchScheduleStatus(startDate, endDate) {
    const { activeInstitutionCalendar, getScheduleStatus, userId } = this.props;
    getScheduleStatus({
      institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
      userId: userId,
      startDate,
      endDate,
    });
  }
  handleChange(e) {
    this.setState({
      comment: e.target.value,
    });
  }

  getDates() {
    return DATES;
  }

  disableSubmit() {
    const { comment } = this.state;

    if (comment === null) {
      return true;
    }

    return false;
  }
  apply = (status, flow) => {
    const request = {
      comment: this.state.comment,
      _person_id: this.state.personid,
    };
    this.props.SuspendReport(request, this.state.id, () => {
      this.props.history.push(`/leave-management/report`);
    });
  };
  handleSubstituteStaffChange(staffId, session) {
    const session_no = session.get('session_no');
    if (!staffId) {
      this.setState((state) => {
        return { substituteStaff: state.substituteStaff.delete(session_no) };
      });
      return;
    }
    const staffs = session.get('substitute_staff', List());
    const selectedStaff = staffs.find((s) => s.get('_staff_id') === staffId);
    if (selectedStaff) {
      this.setState((state) => {
        return {
          substituteStaff: state.substituteStaff.set(
            session_no,
            Map({
              program: session.get('program'),
              course: session.get('course'),
              delivery_type: session.get('delivery_type'),
              session_no: session.get('session_no'),
              _substitute_staff_id: selectedStaff.get('_staff_id'),
            })
          ),
        };
      });
    }
  }
  render() {
    const { leavedata } = this.props;
    const {
      name,
      noticed,
      startDate,
      endDate,
      reason,
      selectedAttachment,
      selectedAttachmentUrl,
    } = this.state;
    return (
      <div className="main pt-3 pb-5 ">
        <div className="container">
          {' '}
          <div className="p-2">
            <div className="d-flex justify-content-between">
              <div className="">
                <b className="pr-3" onClick={this.handleClickback}>
                  {' '}
                  <i
                    className={`fa fa-arrow-${
                      getLang() === 'ar' ? 'right' : 'left'
                    } remove_hover  f-16`}
                    aria-hidden="true"
                  ></i>
                </b>
                <b className="mb-2 f-16"> {t('leaveManagement.Suspend_Absence_Report')}</b>
              </div>
              {leavedata.flow !== 'row' && (
                <div className="">
                  <Button
                    variant={this.disableSubmit() ? 'secondary' : 'primary'}
                    className="f-14"
                    disabled={this.disableSubmit()}
                    onClick={(e) => this.apply()}
                  >
                    {t('role_management.role_actions.Suspend')}
                  </Button>
                </div>
              )}
            </div>
            {this.state.show && (
              <ApproveReportModal
                startDate={this.state.startDate}
                endDate={this.state.endDate}
                staffId={this.state.selectStaffId}
                isnoticed={this.state.noticed}
                reason={this.state.reason}
                attachement={this.state.selectedAttachment.name}
                closed={this.apply.bind(this)}
                id={this.state.id}
                name={name}
                componentname="reportAbsence"
              />
            )}
            <div className="pl-35 pt-3">
              <div className="row">
                <div className="col-md-12">
                  <small className="mb-2 d-flex">
                    <span className="text-red"> * </span>
                    {t('leaveManagement.Mandatory_information')}
                  </small>
                </div>
              </div>

              <div className="row">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">{t('leaveManagement.Select_the_staff')} </label>
                </div>

                <div className={`col-md-3 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">{name}</label>
                </div>
              </div>

              <div className="row">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">{t('leaveManagement.selectLeave')} </label>
                </div>
                <div className={`col-md-3 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">
                    {noticed ? t('leaveManagement.Noticed') : t('leaveManagement.Un_noticed')}
                  </label>
                </div>
              </div>

              <div className="row">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">{t('leaveManagement.dates_')} </label>
                </div>
                <div className={`col-md-3 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">
                    {moment(startDate).format('DD MMM YYYY')}-
                    {moment(endDate).format('DD MMM YYYY')}
                  </label>
                </div>
              </div>

              <div className="row">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0"> {t('leaveManagement.Reason')} </label>
                </div>
                <div className={`col-md-3 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">{reason}</label>
                </div>
              </div>

              <div className="row">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0">
                    {' '}
                    {t('leaveManagement.attachment')}
                    {t('leaveManagement.s')}{' '}
                  </label>
                </div>
                <div className={`col-md-3 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27 mb-0 ">
                    {' '}
                    {selectedAttachment !== '' && selectedAttachment !== 'null' ? (
                      <a href={selectedAttachmentUrl} target="_blank" rel="noopener noreferrer">
                        {selectedAttachment?.split('/').slice(-1)}
                      </a>
                    ) : (
                      'N/A'
                    )}{' '}
                  </label>
                </div>
              </div>
              {leavedata.flow !== 'row' && (
                <div className="row mb-2">
                  <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                    <label className="mt-27"> {t('leaveManagement.Suspend_reason')} </label>
                  </div>
                  <div className={`col-md-3 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={25}
                      changed={(e) => this.handleChange(e)}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        {
          // <StaffSchedule
          //   selectedSubstituteStaff={this.state.substituteStaff}
          //   scheduleStatus={this.props.scheduleStatus}
          //   handleSubstituteStaffChange={this.handleSubstituteStaffChange.bind(this)}
          // />
        }
      </div>
    );
  }
}
// function StaffSchedule(props) {
//   function getSubstituteStaffOptions(staffs) {
//     return List([Map({ name: 'Select staff', value: '' })])
//       .concat(
//         staffs.map((s) =>
//           Map({
//             name: `${s.getIn(['name', 'first'], '')} ${s.getIn(['name', 'last'], '')}`,
//             value: s.get('_staff_id'),
//           })
//         )
//       )
//       .toJS();
//   }

//   function formatSessionTime(start, end) {
//     if (!start || !end) return '';
//     start = new Date(start);
//     end = new Date(end);
//     return `${format(start, 'hh:mm')} - ${format(end, 'hh:mm a')}, ${format(start, 'd MMM yyyy')}`;
//   }

//   const { selectedSubstituteStaff, scheduleStatus, handleSubstituteStaffChange } = props;
//   return (
//     <div className="bg-gray p-3 rounded">
//       <div className="row ">
//         <div className="col-md-12">
//           <b className="f-15 ">List of all leaves applied and taken </b>

//           <div className="leaveManage bg-white mb-2">
//             <table className="table">
//               <thead className="group_table_top">
//                 <tr>
//                   <th className="border_color_blue">
//                     <div className="aw-50">
//                       <b>S.NO </b>
//                     </div>
//                   </th>

//                   <th className="border_color_blue">
//                     <div className="aw-150">
//                       <b> Schedule</b>
//                     </div>
//                   </th>
//                   <th className="border_color_blue">
//                     <div className="aw-100">
//                       <b> Program</b>
//                     </div>
//                   </th>

//                   <th className="border_color_blue">
//                     <div className="aw-100">
//                       <b className="">Course</b>{' '}
//                     </div>
//                   </th>
//                   <th className="border_color_blue">
//                     <div className="aw-100">
//                       <b className="">Delivery Type</b>
//                     </div>
//                   </th>
//                   <th className="border_color_blue">
//                     <div className="aw-100">
//                       <b className="">Subject</b>
//                     </div>
//                   </th>
//                   <th className="border_color_blue">
//                     <div className="aw-150">
//                       <b className="">Topic</b>
//                     </div>
//                   </th>
//                   <th className="border_color_blue">
//                     <div className="aw-150">
//                       <b className="">Substitute Staff </b>
//                     </div>
//                   </th>
//                   <th className="border_color_blue">
//                     <div className="aw-118"></div>
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="leaveManage-height">
//                 {scheduleStatus.get('data', List()).map((s, i) => (
//                   <tr key={s.get('session_no')} className="tr-bottom-border">
//                     <td>
//                       <div className="aw-50">
//                         <b>{i + 1}</b>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-150">
//                         <b>{formatSessionTime(s.get('start'), s.get('end'))}</b>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-100">
//                         <b>{s.get('program', '')}</b>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-100">
//                         <b>{s.get('course', '')}</b>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-100">
//                         <b>{s.get('delivery_type', '')}</b>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-100">
//                         <b>{s.get('subject', '')}</b>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-150">
//                         <b>{s.get('topic', '')}</b>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-150">
//                         <div className="mt--25">
//                           <Input
//                             elementType="floatingselect"
//                             elementConfig={{
//                               options: getSubstituteStaffOptions(s.get('substitute_staff', List())),
//                             }}
//                             value={selectedSubstituteStaff.getIn(
//                               [s.get('session_no'), '_substitute_staff_id'],
//                               ''
//                             )}
//                             changed={(e) => handleSubstituteStaffChange(e.target.value, s)}
//                           />
//                         </div>
//                       </div>
//                     </td>
//                     <td>
//                       <div className="aw-118">
//                         {s.get('substitute_staff', List()).isEmpty() && (
//                           <img alt="" src={Warning} className="pr-2 mt-2" />
//                         )}
//                       </div>
//                     </td>
//                   </tr>
//                 ))}
//               </tbody>
//             </table>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
const mapStateToProps = function (state) {
  return {
    leave: selectLeave(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),

    addedwarning: selectAddList(state),
    leavedetials: selectLocations(state),
    scheduleStatus: selectScheduleStatus(state),
    reviewerList: selectReviewerList(state),
    leavedata: selectLeaveList(state),

    staffList: selectStaffList(state),
    userId: selectUserId(state),
  };
};

ReportAbsenceSuspenseLeave.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  leave: PropTypes.instanceOf(Map),
  reviewerList: PropTypes.instanceOf(List),
  getScheduleStatus: PropTypes.func,
  setBreadCrumbName: PropTypes.func,
  reviewer: PropTypes.func,
  SuspendReport: PropTypes.func,
  userId: PropTypes.string,
  match: PropTypes.object,
  history: PropTypes.object,
  leavedata: PropTypes.object,
};

export default compose(withRouter, connect(mapStateToProps, actions))(ReportAbsenceSuspenseLeave);
