.timeDayMerge {
  border-radius: 4px;
  background: white;
  padding: 10px 12px 10px 12px;
  margin-bottom: 15px;
}
.w-11 {
  width: 11%;
}
.w-65 {
  width: 65%;
}
.filtersSelect {
  background: #f9fafb;
  padding: 6px 10px 6px 10px;
  border-bottom: 1px solid #ddd1d1;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  margin-bottom: 10px;
}
.text-missed {
  color: #850000;
}
.noData {
  width: 1170px;
}
.digi-scroll {
  overflow-y: auto;
  max-height: calc(35vh - 80px);
}

.digi-scroll::-webkit-scrollbar {
  width: 10px;
}

.digi-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 2px grey;
  border-radius: 10px;
}

.digi-scroll::-webkit-scrollbar-thumb {
  background: #becad6;
  border-radius: 10px;
}

.program_list {
  border: 1px solid #147afc;
  margin-right: 15px;
  background: white;
  padding: 7px 10px 7px 10px;
  border: 1px solid #147afc;
  border-radius: 4px;
  color: #147afc;
  font-weight: 500;
}

.Late-Started {
  padding: 0px 8px !important;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #e144ef;
}

.Early-Ended {
  padding: 0px 8px !important;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #f50b89;
}

.Merged {
  padding: 0px 8px !important;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

.echart_custom_height {
  height: 372px !important;
}
.color-LateStarted {
  color: #e144ef;
}
.color-earlyEnded {
  color: #f50b89;
}
div#d_flex_remove {
  display: block;
}
.z-1 {
  z-index: 1;
}
