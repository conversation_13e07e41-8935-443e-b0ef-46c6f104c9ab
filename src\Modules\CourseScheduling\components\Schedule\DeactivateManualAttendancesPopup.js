import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { DialogActions } from '@mui/material';
import { Stack } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import { useStyles } from './designUtils';
import DeactiveteIcon from '../../../../Assets/Deactivate_icon.svg';

const DeactivateManualAttendancesPopup = ({
  manualDeactivateModal,
  setManualDeactivateModal,
  manualStaffSaveSetting,
  manualStaff,
  setManualStaff,
}) => {
  const classes = useStyles();

  const handleClose = (key) => {
    setManualDeactivateModal(false);
    key === 'onCancel' && setManualStaff((prev) => prev.set('manualAttendance', true));
  };

  const handleConfirm = () => {
    const payLoad = {
      manualAttendance: false,
      isStaffAssigned: false,
      assignedStaffIds: [],
    };
    setManualStaff(
      Map({
        status: false,
        staffs: List(),
        selectedStaff: List(),
        manualAttendance: false,
      })
    );
    manualStaffSaveSetting(payLoad, handleClose);
  };

  return (
    <div>
      <Dialog
        className="p-2"
        maxWidth="md"
        open={manualDeactivateModal}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <div className="m-2">
          <div className="d-flex flex-column mt-2">
            <div className="d-flex flex-row justify-content-start mt-2 ml-2">
              <div className="rounded-circle p-1 pt-2">
                <img src={DeactiveteIcon} alt="Deactivate Icon" className="remove_hover" />
              </div>
              <div>
                <DialogTitle className="pl-1" id="alert-dialog-title">
                  Secondary Attendance
                </DialogTitle>
              </div>
            </div>
            <div>
              <DialogContent className={classes.deactivateManualAttendancePopUpPadding}>
                <DialogContentText id="alert-dialog-description">
                  <span className="d-flex justify-content-center mb-1">
                    <span className="text-center mb-0">
                      Are you sure you want to deactivate secondary attendance?
                    </span>
                  </span>
                </DialogContentText>
              </DialogContent>
            </div>
          </div>
          <div className="d-flex justify-content-end">
            <DialogActions>
              <Stack spacing={2} direction="row">
                <MButton
                  variant="text"
                  color={'blue'}
                  clicked={() => handleClose('onCancel')}
                  className="text-dark"
                >
                  CANCEL
                </MButton>
                <MButton variant="contained" color={'red'} clicked={handleConfirm}>
                  CONFIRM
                </MButton>
              </Stack>
            </DialogActions>
          </div>
        </div>
      </Dialog>
    </div>
  );
};

DeactivateManualAttendancesPopup.propTypes = {
  manualDeactivateModal: PropTypes.bool,
  setManualDeactivateModal: PropTypes.func,
  manualStaffSaveSetting: PropTypes.func,
  setManualStaff: PropTypes.func,
  courseId: PropTypes.string,
  programId: PropTypes.string,
  manualStaff: PropTypes.instanceOf(Map),
};

export default DeactivateManualAttendancesPopup;
