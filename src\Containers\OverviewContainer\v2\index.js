import React, { Component } from 'react';
import { withRouter, Redirect } from 'react-router-dom';
import PropTypes from 'prop-types';
class OverviewContainer extends Component {
  render() {
    const { loggedInUserData } = this.props;
    return (
      <div>
        {loggedInUserData.get('user_type', 'staff') !== 'student' ? (
          <Redirect
            to={`${
              loggedInUserData.getIn(['university', 'institutionId'], '') !== ''
                ? '/university-details'
                : '/institution/onboarding'
            }`}
          />
        ) : (
          <Redirect to="/InstitutionCalendar" />
        )}
      </div>
    );
  }
}

OverviewContainer.propTypes = {
  loggedInUserData: PropTypes.instanceOf(Map),
};

export default withRouter(OverviewContainer);
