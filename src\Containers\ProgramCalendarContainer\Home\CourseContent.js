import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';
import styled from 'styled-components';

import { PrimaryButton, TextContainer, Text } from '../Styled';
import LevelOneCourses from './LevelOneCourses';
//import AcademicEvents from "./AcademicEvents";
import useDataFromStore from '../UtilityComponents/useDataFromStore';
import { Trans } from 'react-i18next';

const ContentBlock = styled.div`
  display: grid;
  text-align: center;
  grid-template-rows: repeat(${(props) => props.len || 0}, 50px);
`;

const CourseContent = (props) => {
  const { length, number, rotational, is_interim, choose, iframeShow, currentPGAccess } = props;
  const match = useRouteMatch();
  const active = match.params.year || 'year2';

  const { start, end, start1, end1 } = useDataFromStore();

  const common_start = choose === 0 ? start : start1;
  const common_end = choose === 0 ? end : end1;

  return (
    <Fragment>
      {common_start && common_end ? (
        <ContentBlock len={length}>
          <LevelOneCourses
            number={number}
            rotational={rotational}
            index={0}
            choose={choose}
            iframeShow={iframeShow}
            currentPGAccess={currentPGAccess}
          />
          {/* <AcademicEvents rotate={false} choose={choose} /> */}
          {false /* based on new requirement */ &&
            !is_interim &&
            props[active]['level_two_show'] && (
              <LevelOneCourses
                index={1}
                choose={choose}
                iframeShow={iframeShow}
                currentPGAccess={currentPGAccess}
              />
            )}
        </ContentBlock>
      ) : (
        <TextContainer>
          <Text>
            <Trans i18nKey={'program_calendar.no_calendar'}></Trans>
          </Text>{' '}
          <Text>
            <Trans i18nKey={'program_calendar.click'}></Trans>
            <PrimaryButton className="dim ml-2 mr-2">
              <Trans i18nKey={'program_calendar.calendar_settings'}></Trans>
            </PrimaryButton>
            <Trans i18nKey={'program_calendar.define_dates_and_curriculum'}></Trans>
          </Text>
        </TextContainer>
      )}
    </Fragment>
  );
};

CourseContent.defaultProps = {
  number: -1,
  rotational: false,
};

const mapStateToProps = ({ calender }) => ({
  // active: calender.active_year,
  is_interim: calender.interim,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
});

export default connect(mapStateToProps)(CourseContent);
