/* eslint-disable react/jsx-key */
/* eslint-disable react/jsx-no-undef */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Table, DropdownButton, Button, Dropdown, Modal } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import <PERSON> from 'papaparse';
import Swal from 'sweetalert2';
import { CSVLink } from 'react-csv';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import Search from '../../Widgets/Search/Search';
import Pagination from '../StaffManagement/Pagination';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import * as actions from '../../_reduxapi/user_management/action';
import Export from '../../Containers/StudentRegistrationContainer/Export';
import { addDefaultLastName } from 'utils';
class AllStaff extends Component {
  constructor(props) {
    super(props);
    this.state = {
      importShow: false,
      loadingShow: false,
      csvFile: undefined,
      data: [],
      isLoading: false,
      confirmDelete: false,
      searchText: '',
      limit: 10,
      totalPages: 1,
      pageLength: 1,
      user_state: '',
    };
    this.updateData = this.updateData.bind(this);
    this.timeout = 0;
  }

  componentDidMount() {
    this.fetchApi({});
  }

  successTrigger = (res) => {
    const data = res.data.data.map((data) => {
      return {
        id: data._id,
        username: data.family,
        employee_id: data.employee_id,
        email: data.email,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        gender: data.gender,
        nationality_id: data.address.nationality_id,
        user_state: data.user_state,
        isChecked: false,
      };
    });
    this.setState({
      data: data,
      isLoading: false,
      totalPages: res.data.totalPages,
    });
  };

  fetchApi = ({ limit = 10, pageLength = 1 }) => {
    const { getUserManagementList } = this.props;
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });
    getUserManagementList(
      {
        searchText: searchText,
        type: 'staff',
        status: 'all',
        limit: limit,
        pageLength: pageLength,
        slug: 'get_all',
      },
      this.successTrigger,
      () => {
        this.setState({
          data: [],
          isLoading: false,
          totalPages: 1,
          pageLength: 1,
        });
      }
    );
  };

  handleImport = (e) => {
    this.setState({
      importShow: true,
    });
  };

  handleImportClose = () => {
    this.setState({
      importShow: false,
    });
  };

  handleLoadingImportClose = () => {
    this.setState({
      loadingShow: false,
    });
  };

  handleChange = (event) => {
    this.setState({
      csvFile: event.target.files[0],
    });
  };

  importCSV = () => {
    const { csvFile } = this.state;
    Papa.parse(csvFile, {
      complete: this.updateData,
      header: true,
    });
  };

  pagination = (e) => {
    this.setState(
      {
        limit: e.target.value,
        pageLength: 1,
      },
      () => {
        setTimeout(() => {
          this.fetchApi({ limit: this.state.limit });
        }, 500);
      }
    );
  };

  pageCount = (value) => {
    this.setState(
      {
        pageLength: value,
      },
      () => {
        this.fetchApi({ pageLength: value, limit: this.state.limit });
      }
    );
  };

  updateData(result) {
    var csvData = result.data;
    this.setState({
      importShow: false,
      loadingShow: true,
    });
    let data = csvData.map((data) => {
      if (data.Email !== '' && data.FirstName !== '') {
        return {
          email: data.Email,
          first_name: data.FirstName,
          last_name: addDefaultLastName(data?.LastName),
          middle_name: data.MiddleName,
          gender: data.Gender,
          employee_id: data.EmployeeID,
          nationality_id: data.NationalID,
        };
      } else {
        return {};
      }
    });
    data = data.filter((value) => Object.keys(value).length !== 0);
    let dataArray = { user_type: 'staff', data };
    axios
      .post(`user/import`, dataArray)
      .then((res) => {
        if (res.data.status_code === 200) {
          Swal.fire({
            title: '',
            icon: 'info',
            html:
              `<b>Imported Records Count - ${
                res.data.data.imported_records_count !== undefined
                  ? res.data.data.imported_records_count
                  : '0'
              }</b>` +
              `<br/><br/>` +
              `<b>Invalid Records Count - ${
                res.data.data.invalid_records_count !== undefined
                  ? res.data.data.invalid_records_count
                  : '0'
              }</b>`,
          });
          this.setState(
            {
              csvFile: undefined,
              loadingShow: false,
            },
            () => {
              this.fetchApi({});
            }
          );
          this.props.fetchApi();
        }
      })
      .catch((error) => {
        if (
          error.response !== undefined &&
          error.response.data !== undefined &&
          error.response.data.data !== undefined
        ) {
          NotificationManager.error(`${error.response.data.data}`);
        } else {
          NotificationManager.error(t(`user_management.Error_CSV_file`));
        }
        this.setState({
          loadingShow: false,
          csvFile: undefined,
        });
      });
  }

  handleClickView = (data) => {
    this.props.history.push({
      pathname: '/staff/profile',
      search: '?id=' + data.id,
    });
  };

  handleSingleShow = () => {
    this.props.history.push({
      pathname: '/staff/profile/add',
    });
  };

  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      const { limit } = this.state;
      setTimeout(() => {
        this.fetchApi({ limit });
        this.setState({ pageLength: 1 });
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState({ searchText: e.target.value }, () => this.doSearch(e));
  };

  render() {
    const header = [
      <Trans i18nKey={'employee_id'}></Trans>,
      <Trans i18nKey={'email'}></Trans>,
      <Trans i18nKey={'first_name'}></Trans>,
      'Middle Name',
      'Last Name',
      <Trans i18nKey={'gender'}></Trans>,
      <Trans i18nKey={'national_id'}></Trans>,
      <Trans i18nKey={'status'}></Trans>,
    ];

    const csvHeader = [
      ['FirstName', 'MiddleName', 'LastName', 'Gender', 'Email', 'EmployeeID', 'NationalID'],
    ];
    return (
      <div className="main bg-gray pt-2 pb-5">
        <Loader isLoading={this.state.isLoading} />
        <div className="container-fluid">
          <div className="float-right p-2 d-flex">
            <Export userType="staff" status="all" />
            {CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              'All',
              'Search'
            ) && <Search value={this.state.searchText} onChange={(e) => this.handleSearch(e)} />}
          </div>
          <div className="">
            <div className="float-right pt-2 pr-2">
              {(CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'All',
                'Add Single'
              ) ||
                CheckPermission(
                  'subTabs',
                  'User Management',
                  'Staff Management',
                  '',
                  'Registration Pending',
                  '',
                  'All',
                  'Add Bulk'
                )) && (
                <DropdownButton
                  id="dropdown-basic-button"
                  title={<Trans i18nKey={'add_new'}></Trans>}
                >
                  {CheckPermission(
                    'subTabs',
                    'User Management',
                    'Staff Management',
                    '',
                    'Registration Pending',
                    '',
                    'All',
                    'Add Single'
                  ) && (
                    <Dropdown.Item href="" onClick={(e) => this.handleSingleShow(e)}>
                      <Trans i18nKey={'single_entry'}></Trans>
                    </Dropdown.Item>
                  )}
                  {CheckPermission(
                    'subTabs',
                    'User Management',
                    'Staff Management',
                    '',
                    'Registration Pending',
                    '',
                    'All',
                    'Add Bulk'
                  ) && (
                    <Dropdown.Item href="" onClick={(e) => this.handleImport(e)}>
                      <Trans i18nKey={'bulk_import'}></Trans>
                    </Dropdown.Item>
                  )}
                </DropdownButton>
              )}
            </div>
            <div className="clearfix"> </div>
            <Modal show={this.state.importShow} centered onHide={this.handleImportClose}>
              <Modal.Header closeButton>
                <Modal.Title id="example-modal-sizes-title-sm">
                  <Trans i18nKey={'user_management.import_staff_list'} />{' '}
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div className="col-md-12 text-right">
                  <CSVLink data={csvHeader} filename={t('sample_import_staff')}>
                    {' '}
                    <i className="fa fa-download pr-2" aria-hidden="true"></i>
                    <Trans i18nKey={'download_template'} />
                  </CSVLink>
                </div>
                <div className="row  justify-content-center pt-2 pb-2">
                  <div className="col-md-3"></div>

                  <div className="col-md-6">
                    {this.state.csvFile !== undefined && (
                      <div className="text-center pb-2">{this.state.csvFile.name}</div>
                    )}
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary btn-block rounded-pill shadow"
                    >
                      <i className="fa fa-upload mr-2"></i>{' '}
                      <Trans i18nKey={'user_management.import_file'} />
                      <input
                        id="fileUpload"
                        type="file"
                        accept=".csv"
                        onChange={this.handleChange}
                      />
                    </label>
                  </div>
                  <div className="col-md-3"></div>

                  {/* <div className="col-md-6 text-center pt-2 ">
                    <p> Staff.CSV file Data</p>
                  </div> */}
                  <div>
                    <hr />
                  </div>
                </div>
                {/* <input
                className="csv-input"
                type="file"
                 onChange={this.handleChange}
              /> */}
              </Modal.Body>
              <Modal.Footer>
                {this.state.csvFile !== undefined ? (
                  <Button onClick={this.importCSV}>
                    <Trans i18nKey={'confirm'} />
                  </Button>
                ) : (
                  <Button disabled>
                    <Trans i18nKey={'confirm'} />
                  </Button>
                )}
              </Modal.Footer>
            </Modal>

            <Modal show={this.state.loadingShow} centered onHide={this.handleImportClose}>
              <Modal.Header closeButton>
                {/* <Modal.Title id="example-modal-sizes-title-sm">
                  Import Staff DB From Banner{" "}
                </Modal.Title> */}
              </Modal.Header>
              <Modal.Body style={{ background: '#e7e0e0' }}>
                <div className="success_notification">
                  <Trans i18nKey={'user_management.importing_data'} />
                  <img
                    alt="importing-data"
                    src={require('../../Assets/elipsis.svg')}
                    className="notification-item-img"
                  />{' '}
                </div>
              </Modal.Body>
              <Modal.Footer>
                {this.state.loadingShow === true ? (
                  <Button onClick={this.handleLoadingImportClose}>
                    {' '}
                    <Trans i18nKey={'events.close'} />
                  </Button>
                ) : (
                  <Button disabled>
                    {' '}
                    <Trans i18nKey={'events.close'} />
                  </Button>
                )}
              </Modal.Footer>
            </Modal>

            {/* Delete model start  */}

            <Modal show={this.state.confirmDelete} onHide={this.handleConfirmDeleteClose}>
              <Modal.Header closeButton>
                <Modal.Title id="example-modal-sizes-title-sm">
                  <Trans i18nKey={'user_management.delete_staff'} />
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div className="row  justify-content-center">
                  <div className="col-md-7 pt-2">
                    <p className="pt-2">
                      {' '}
                      <Trans i18nKey={'user_management.confirm_delete_staff'} />{' '}
                    </p>
                  </div>
                  <div className="col-md-5 pt-2">
                    <Button btnType="Success" onClick={(e) => this.handleDelete(e)}>
                      <Trans i18nKey={'confirm'} />
                    </Button>{' '}
                    <Button btnType="Success" onClick={this.handleConfirmDeleteClose}>
                      {' '}
                      <Trans i18nKey={'events.cancel'} />
                    </Button>{' '}
                  </div>
                </div>
              </Modal.Body>
            </Modal>

            {/* Delete model end  */}

            <div className="clearfix"> </div>
            <div className="clearfix"> </div>
            <React.Fragment>
              <div className="p-3">
                <div className="dash-table">
                  <Table responsive hover>
                    <thead className="th-change">
                      <tr>
                        {header.map((header, headerIndex) => (
                          <th key={headerIndex}>{header}</th>
                        ))}
                      </tr>
                    </thead>
                    {this.state.data.length === 0 ? (
                      <TableEmptyMessage />
                    ) : (
                      <tbody>
                        {this.state.data.map((data, index) => (
                          <tr className="tr-change" key={index}>
                            <td>{data.employee_id}</td>
                            <td>{data.email}</td>
                            <td>{data.first}</td>
                            <td>{data.middle}</td>
                            <td>{data.last}</td>
                            <td>{data.gender}</td>
                            <td>{data.nationality_id}</td>
                            <td>{data.user_state}</td>
                          </tr>
                        ))}
                      </tbody>
                    )}
                  </Table>
                </div>
                <Pagination
                  totalPages={this.state.totalPages}
                  switchPagination={this.pagination}
                  switchPageCount={this.pageCount}
                  pageLength={this.state.pageLength}
                />
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  }
}

AllStaff.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
  fetchApi: PropTypes.func,
};

export default connect(null, actions)(withRouter(AllStaff));
