import React from 'react';
import MButton from 'Widgets/FormElements/material/Button';
import { Menu, MenuItem, IconButton } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';

function RegulationName(props) {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const options = ['Delete', 'Edit'];
  const ITEM_HEIGHT = 48;

  return (
    <div className="main pb-5">
      <div className="container">
        <div className="pt-5 pb-5">
          <div className="course_master">
            <div className="d-flex justify-content-between mb-3">
              <h6 className="pl-2 bold f-20">Regulations</h6>
              <div className="d-flex justify-content-end">
                <div className="col-md-12">
                  <MButton variant="contained" color="primary">
                    Create New Regulation
                  </MButton>
                </div>
              </div>
            </div>

            <div className="program_table">
              <table align="left">
                <thead>
                  <tr>
                    <th>
                      <div className=""> Regulation Name</div>
                    </th>

                    <th>
                      <div className=""> Root Node</div>
                    </th>

                    <th>
                      <div className=""> Action</div>
                    </th>
                    <th></th>
                  </tr>
                </thead>

                <tbody>
                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">Regulation - 2022.Version 1.0</div>
                    </td>
                    <td className="d-flex pb-3">
                      <div className="mt-2 ml-1 mr-1 f-14 pt-2 pl-3 pr-3 pb-2 remove_hover digi-root-node">
                        CO
                      </div>
                      <div className="mt-2 ml-1 mr-1 f-14 pt-2 pl-3 pr-3 pb-2 remove_hover digi-root-node">
                        PO
                      </div>
                    </td>
                    <td className="">
                      <div className="d-flex justify-content-between">
                        <p className="mb-0 remove_hover f-15 bold AssessmentActive"> View </p>
                      </div>
                    </td>

                    <td className="">
                      <MoreVertIcon />
                    </td>
                  </tr>

                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">Regulation - 2022.Version 2.0</div>
                    </td>
                    <td className="d-flex pb-3">
                      <div className="mt-2 ml-1 mr-1 f-14 pt-2 pl-3 pr-3 pb-2 remove_hover digi-root-node">
                        CO
                      </div>
                    </td>
                    <td className="">
                      <div className="d-flex justify-content-between">
                        <p className="mb-0 remove_hover f-15 bold AssessmentActive"> View </p>
                      </div>
                    </td>

                    <td className="">
                      <MoreVertIcon />
                    </td>
                  </tr>

                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">Regulation - 2020</div>
                    </td>
                    <td className="d-flex pb-3">
                      <div className="mt-2 ml-1 mr-1 f-14 pt-2 pl-3 pr-3 pb-2 remove_hover digi-root-node">
                        PO
                      </div>
                    </td>
                    <td className="">
                      <div className="d-flex justify-content-between">
                        <p className="mb-0 remove_hover f-15 bold AssessmentActive"> View </p>
                      </div>
                    </td>

                    <td className="">
                      <MoreVertIcon />
                    </td>
                  </tr>

                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">Regulation - 2018</div>
                    </td>
                    <td className="d-flex pb-3">
                      <div className="mt-2 ml-1 mr-1 f-14 pt-2 pl-3 pr-3 pb-2 remove_hover digi-root-node">
                        CO
                      </div>
                      <div className="mt-2 ml-1 mr-1 f-14 pt-2 pl-3 pr-3 pb-2 remove_hover digi-root-node">
                        PO
                      </div>
                      <div className="mt-2 ml-1 mr-1 f-14 pt-2 pl-3 pr-3 pb-2 remove_hover digi-root-node">
                        PSO
                      </div>
                    </td>
                    <td className="">
                      <div className="d-flex justify-content-between">
                        <p className="mb-0 remove_hover f-15 bold AssessmentActive"> View </p>
                      </div>
                    </td>

                    <td className="">
                      <MoreVertIcon />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RegulationName;
