import React, { useState } from 'react';
import PropTypes from 'prop-types';
import InstitutionHeader from './InstitutionHeader';
import InstitutionLogo from './InstitutionLogo';
import InstitutionAddDetails from './InstitutionAddDetails';
import '../../../../css/institution.css';

function InstitutionIndex({ handleBack, onSubmit, type, name, setData, setIsChanged }) {
  const childFunc = React.useRef(null);
  const [logo, setLogo] = useState(null);

  const onClickSaved = () => {
    childFunc.current();
  };
  const fetchData = (data) => {
    onSubmit({ ...data, logo });
  };
  return (
    <div className="row justify-content-center pt-3">
      <div>
        <div className="outter-login add-university">
          <InstitutionHeader handleBack={handleBack} onSubmit={onClickSaved} name={name} />
          <InstitutionLogo
            logo={logo}
            setLogo={setLogo}
            isCollege={!(type === 'group')}
            setData={setData}
          />
          <InstitutionAddDetails
            isGroup={type === 'group'}
            childFunc={childFunc}
            getData={fetchData}
            setIsChanged={setIsChanged}
          />
          <div className="d-flex pt-3 jc-space-between"></div>
        </div>
      </div>
    </div>
  );
}

InstitutionIndex.propTypes = {
  handleBack: PropTypes.func,
  onSubmit: PropTypes.func,
  setData: PropTypes.func,
  type: PropTypes.string,
  name: PropTypes.string,
  setIsChanged: PropTypes.func,
};
export default InstitutionIndex;
