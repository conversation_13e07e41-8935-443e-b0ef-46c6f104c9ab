import React, { lazy, Suspense, useEffect, useMemo, useRef, useState } from 'react';
import { Badge, Box, IconButton } from '@mui/material';
import moment from 'moment';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { fromJS, List, Map } from 'immutable';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  AddScheduleButton,
  concatenateSubjects,
  CustomCalendar,
  formatStartEndDate,
  getFirstLastDate,
  getInitialDate,
  getFirstValue,
  getCurrentDate,
  MuiSelect,
  SearchInput,
} from './utils';
import {
  getTranslatedDuration,
  getURLParams,
  isIndGroup,
  levelRename,
  studentGroupRename,
} from 'utils';
import ScheduleCalendar from './ScheduleCalendar';
import { useDispatch, useSelector } from 'react-redux';
import { selectCourseList, selectEventList } from '_reduxapi/course_scheduling/selectors';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { getEventList, setData } from '_reduxapi/course_scheduling/action';
import { getFormattedGroupName } from '../utils';
import { getSelectedLevelData } from './ScheduleDrawer';

const ScheduleDrawer = lazy(() => import('./ScheduleDrawer'));

const containerSx = {
  marginTop: '8px',
  marginBottom: '24px',
  px: '16px',
  color: '#374151',
};
const holidaysBoxSx = {
  margin: '8px 0',
  padding: '12px 8px',
  fontSize: 14,
  backgroundColor: 'white',
  borderRadius: '8px',
};
const circleSx = {
  marginRight: '8px',
  width: '8px',
  height: '8px',
  borderRadius: '50%',
  backgroundColor: '#F59E0B',
};
const weekArrowsSx = {
  padding: '4px',
  color: '#4B5563',
  backgroundColor: '#F3F4F6',
};
const filterIconSx = {
  my: '5px',
  padding: '7px',
  border: '1px solid rgba(0, 0, 0, 0.23)',
  borderRadius: '4px',
};
const badgeSx = {
  '& .MuiBadge-badge': {
    backgroundColor: '#EF4444',
    color: '#fff',
  },
};

const getLevelKey = (term, year) => `${term}+${year}`;

const constructOption = (key, value, programId) => {
  let name = value;
  if (key === 'year') name = name.replace('year', 'Year ');
  else if (key == 'level') name = levelRename(name, programId);

  return Map({ name, value });
};

const getFilterOptions = (data, programId) => {
  const termOptions = data.get('term', List()).map((term) => constructOption('term', term));
  const levelDates = data.get('level_term_dates', List()).reduce((acc, item) => {
    const term = item.get('term');
    const year = item.get('year');
    const level = item.get('level_no');
    const key = [term, year, level].join('+');
    const dateRange = Map({ start: item.get('start_date', ''), end: item.get('end_date', '') });
    return acc.set(key, dateRange);
  }, Map());

  const options = data.get('courses', List()).reduce((acc, course) => {
    const term = course.get('term');
    const year = course.get('year');
    const level = course.get('level_no');
    const levelKey = getLevelKey(term, year);
    const levelDataKey = getLevelKey(levelKey, level);

    return acc
      .setIn(['yearOptions', term, year], constructOption('year', year))
      .setIn(['levelOptions', levelKey, level], constructOption('level', level, programId))
      .setIn(['levelData', levelDataKey], course);
  }, Map());

  return {
    termOptions,
    yearOptions: options.get('yearOptions', Map()).map((opt) => opt.valueSeq().toList()),
    levelOptions: options.get('levelOptions', Map()).map((opt) => opt.valueSeq().toList()),
    levelData: options.get('levelData', Map()),
    levelDates,
  };
};

const constructStudentGroups = (schedule, programId) => {
  const groups = schedule.get('student_groups', List()).reduce((acc, group) => {
    const groupName = group.get('group_name', '');
    const offset = isIndGroup(groupName) ? 1 : 2;
    const formattedName = getFormattedGroupName(studentGroupRename(groupName, programId), offset);
    const sessionGroups = group.get('session_group', List()).map((sg) => {
      const sessionGroupName = sg.get('group_name', '');
      return studentGroupRename(getFormattedGroupName(sessionGroupName, 3), programId);
    });

    return acc
      .update('studentGroups', List(), (prev) => prev.push(formattedName))
      .update('sessionGroups', List(), (prev) => prev.concat(sessionGroups));
  }, Map());

  return {
    courseGroups: groups.get('studentGroups', List()).join(', '),
    deliveryGroups: groups.get('sessionGroups', List()).join(', '),
  };
};

const useGetEventList = () => {
  const dispatch = useDispatch();
  const eventList = useSelector(selectEventList);

  const clearEventList = (withSchedule) => {
    const key = withSchedule ? 'eventsAndSchedules' : 'events';
    dispatch(setData(Map({ eventList: eventList.delete(key) })));
  };

  return {
    eventList: eventList.get('events', List()),
    eventAndScheduleList: eventList.get('eventsAndSchedules', Map()),
    clearEventList,
  };
};

const QuickSchedule = () => {
  const programId = getURLParams('programId', true);
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const courseList = useSelector(selectCourseList);
  const { eventList, eventAndScheduleList, clearEventList } = useGetEventList();
  const [filters, setFilters] = useState(Map());
  const [search, setSearch] = useState(Map());
  const [dateRange, setDateRange] = useState(Map());
  const [scheduleData, setScheduleData] = useState(Map());
  const [midDate, setMidDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(null);
  const calendarComponentRef = useRef(null);
  const institutionCalendarId = activeInstitutionCalendar.get('_id');
  const selectedTerm = filters.get('term', '');
  const selectedYear = filters.get('year', '');
  const levelKey = getLevelKey(selectedTerm, selectedYear);

  useEffect(() => {
    setCurrentMonth(moment(midDate).format('YYYY-MM'));
  }, [midDate]);

  const { termOptions, yearOptions, levelOptions, levelData, levelDates } = useMemo(() => {
    return getFilterOptions(courseList, programId);
  }, [courseList, programId]);

  useEffect(() => {
    const term = getFirstValue(termOptions);
    const year = getFirstValue(yearOptions.get(term));
    const level = getDefaultLevel(term, year);
    setFilters(Map({ term, year, level }));
  }, [termOptions, yearOptions, levelOptions]);

  const { validDateRange, initialDate } = useMemo(() => {
    const validDateRange = getSelectedLevelData(levelDates, filters);
    const minDate = validDateRange.get('start');
    const maxDate = validDateRange.get('end');
    const initialDate = getInitialDate({ minDate, maxDate });
    return { validDateRange, initialDate: moment(initialDate).format('YYYY-MM-DD') };
  }, [levelDates, filters]);

  useEffect(() => {
    updateWeekStartEndDate(initialDate);
  }, [initialDate]);

  useEffect(() => {
    fetchEventList();
  }, [institutionCalendarId, programId, filters, dateRange]);

  useEffect(() => {
    currentMonth && fetchEventList(false);
  }, [institutionCalendarId, programId, filters, currentMonth]);

  const fetchEventList = (withSchedule = true) => {
    clearEventList(withSchedule);
    let startDate;
    let endDate;
    if (withSchedule) {
      startDate = dateRange.get('firstDate');
      endDate = dateRange.get('lastDate');
    } else {
      startDate = `${currentMonth}-01`;
      endDate = moment(startDate).endOf('month');
    }

    const level = filters.get('level');
    if (
      institutionCalendarId &&
      programId &&
      startDate &&
      endDate &&
      selectedTerm &&
      selectedYear &&
      level
    ) {
      const params = {
        institutionCalendarId,
        programId,
        startDate: moment(startDate).format('YYYY-MM-DD'),
        endDate: moment(endDate).format('YYYY-MM-DD'),
        term: selectedTerm,
        year: selectedYear,
        level,
        withSchedule,
      };
      dispatch(getEventList(params));
    }
  };

  const { eventsAndSchedules, scheduleList } = useMemo(() => {
    const events = eventAndScheduleList.get('eventList', List()).map((event) => ({
      title: event.get('eventName', ''),
      eventType: event.get('eventType', ''),
      start: event.get('eventStartTime', ''),
      end: event.get('eventEndTime', ''),
      className: 'event-col',
    }));

    let scheduleList = Map();
    const schedules = eventAndScheduleList.get('updatedScheduleData', List()).map((event) => {
      const scheduleId = event.get('_id', '');
      const session = event.get('session', Map());
      const deliverySymbol = session.get('delivery_symbol', '');
      const deliveryNo = session.get('delivery_no', '');
      const courseCode = event.get('course_code', '');
      const subjects = concatenateSubjects(event.get('subjects', List()));
      const { courseGroups, deliveryGroups } = constructStudentGroups(event, programId);
      const maleCount = event.get('maleCount', 0);
      const femaleCount = event.get('femaleCount', 0);

      scheduleList = scheduleList.set(scheduleId, event);

      return {
        title: `${deliverySymbol}${deliveryNo} - ${session.get('session_topic', '')}`,
        start: event.get('scheduleStartDateAndTime', new Date()),
        end: event.get('scheduleEndDateAndTime', new Date()),
        className: 'session-col',
        scheduleId,
        sessionInfo: `${courseCode} • ${deliverySymbol} • ${subjects} • ${courseGroups} • ${deliveryGroups} (${maleCount} male ${femaleCount} female)`,
        isSession: true,
      };
    });

    return { eventsAndSchedules: [...events, ...schedules], scheduleList };
  }, [eventAndScheduleList]);

  const eventDates = useMemo(() => {
    return eventList.map((event) => moment(event.get('eventStartTime', '')).format('YYYY-MM-DD'));
  }, [eventList]);

  const getDefaultLevel = (term, year) => {
    const levelKey = getLevelKey(term, year);
    return getFirstValue(levelOptions.get(levelKey));
  };

  const handleFilters = (key) => (e) => {
    const value = e.target.value;
    if (key === 'term') {
      const year = getFirstValue(yearOptions.get(value));
      const level = getDefaultLevel(value, year);
      return setFilters(Map({ [key]: value, year, level }));
    }

    if (key === 'year') {
      const level = getDefaultLevel(selectedTerm, value);
      return setFilters((prev) => prev.set(key, value).set('level', level));
    }

    setFilters((prev) => prev.set(key, value));
  };

  const handleSearch = (key) => (e) => {
    if (key === 'show') return setSearch((prev) => prev.update(key, (show) => !show));
    setSearch((prev) => prev.set('value', e.target.value));
  };

  const prevClick = () => {
    const calendarApi = calendarComponentRef.current.getApi();
    calendarApi.prev();
    updateWeekStartEndDate(calendarApi.getDate());
  };

  const nextClick = () => {
    const calendarApi = calendarComponentRef.current.getApi();
    calendarApi.next();
    updateWeekStartEndDate(calendarApi.getDate());
  };

  const goToDate = (date) => {
    const calendarApi1 = calendarComponentRef.current.getApi();
    calendarApi1.gotoDate(date);
  };

  const updateWeekStartEndDate = (currentDate, updateMidDate = true) => {
    const range = getFirstLastDate(currentDate);
    const midDate = getCurrentDate(range.firstDate, range.lastDate);
    setDateRange(fromJS(range));
    if (updateMidDate) setMidDate(midDate);
  };

  const handleAddSchedule = (type) => {
    setScheduleData(Map({ show: true, type }));
  };

  const handleChangeDate = (val) => {
    setMidDate(val);
    goToDate(val);
    updateWeekStartEndDate(val, false);
  };

  const isPrevDisabled = () => {
    const minDate = validDateRange.get('start');
    const weekStartDate = dateRange.get('firstDate');
    if (!minDate || !weekStartDate) return true;

    return !moment(minDate).isBefore(moment(weekStartDate), 'date');
  };

  const isNextDisabled = () => {
    const maxDate = validDateRange.get('end');
    const weekEndDate = dateRange.get('lastDate');
    if (!maxDate || !weekEndDate) return true;

    return !moment(maxDate).isAfter(moment(weekEndDate), 'date');
  };

  return (
    <Box display="flex" gap={1} sx={containerSx}>
      <div className="mb-3">
        <CustomCalendar
          eventDates={eventDates}
          value={midDate}
          onChange={handleChangeDate}
          onMonthChange={(val) => setCurrentMonth(val)}
          minDate={validDateRange.get('start', '')}
          maxDate={validDateRange.get('end', '')}
        />
        {!eventList.isEmpty() && (
          <Box sx={holidaysBoxSx}>
            {/* <p className="bold mb-2">Holidays</p>
          <Box display="flex" flexDirection="column" gap={0.5} className="text-light-grey pl-2">
            <p>
              <span className="mr-2">•</span>All Saturdays
            </p>
            <p>
              <span className="mr-2">•</span>All Sundays
            </p>
          </Box> */}
            <p className="bold mb-2">Events</p>
            <Box display="flex" flexDirection="column" gap={0.5} className="text-light-grey pl-2">
              {eventList.map((event, index) => (
                <div key={index} className="d-flex align-items-center">
                  <Box sx={circleSx} />
                  <p>
                    {moment(event.get('eventStartTime', '')).format('DD, ddd')} -{' '}
                    {event.get('eventName', '')}
                  </p>
                </div>
              ))}
            </Box>
          </Box>
        )}
        <p className="mt-2 p-2 f-14 gray-neutral text-center">
          Academic Calendar : {activeInstitutionCalendar.get('calendar_name', '')}
        </p>
      </div>

      <Box borderRadius="12px" className="bg-white p-3 flex-grow-1">
        <div className="d-flex align-items-center justify-content-between mb-1">
          <Box display="flex" alignItems="center" gap={1}>
            <IconButton sx={weekArrowsSx} onClick={prevClick} disabled={isPrevDisabled()}>
              <ChevronLeftIcon />
            </IconButton>
            <IconButton sx={weekArrowsSx} onClick={nextClick} disabled={isNextDisabled()}>
              <ChevronRightIcon />
            </IconButton>
            <p className="f-14">{getTranslatedDuration(formatStartEndDate(dateRange))}</p>
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {search.get('show') ? (
              <div>
                <SearchInput
                  value={search.get('value', '')}
                  changed={handleSearch()}
                  handleClose={handleSearch('show')}
                />
              </div>
            ) : (
              <Box display="flex" alignItems="center" gap={1}>
                <MuiSelect
                  options={termOptions}
                  value={selectedTerm}
                  changed={handleFilters('term')}
                />
                <MuiSelect
                  options={yearOptions.get(selectedTerm, List())}
                  value={selectedYear}
                  changed={handleFilters('year')}
                />
                <MuiSelect
                  options={levelOptions.get(levelKey, List())}
                  value={filters.get('level', '')}
                  changed={handleFilters('level')}
                />
                <IconButton sx={filterIconSx} onClick={handleSearch('show')}>
                  <SearchIcon />
                </IconButton>
              </Box>
            )}
            <Badge badgeContent={8} sx={badgeSx}>
              <IconButton sx={filterIconSx}>
                <FilterListIcon />
              </IconButton>
            </Badge>
            <AddScheduleButton onClick={handleAddSchedule} />
          </Box>
        </div>

        <div className="quick-schedule-calendar">
          <ScheduleCalendar
            calendarComponentRef={calendarComponentRef}
            validRange={validDateRange.toJS()}
            events={eventsAndSchedules}
            scheduleList={scheduleList}
            initialDate={initialDate}
            fetchEventList={fetchEventList}
          />
        </div>
      </Box>

      {scheduleData.get('show') && (
        <Suspense fallback="">
          <ScheduleDrawer
            show
            handleClose={() => setScheduleData(Map())}
            sessionType={scheduleData.get('type')}
            filters={filters}
            levelData={levelData}
            fetchEventList={fetchEventList}
          />
        </Suspense>
      )}
    </Box>
  );
};

export default QuickSchedule;
