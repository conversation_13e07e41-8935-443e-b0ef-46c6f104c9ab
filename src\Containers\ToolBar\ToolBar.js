import React, { Component } from "react";
import {
    withRouter
} from "react-router-dom";
import { Nav, Dropdown } from 'react-bootstrap';


class ToolBar extends Component {
    constructor(props) {
        super(props);
        this.state = {
        };
    }


    render() {
        return (

            <div className="">

                <Nav className="justify-content-start headerbar" activeKey="/home">

                    <span className="justify-content-start" style={{ fontSize: '30px', cursor: 'pointer' }} onClick={this.toggleClick}>☰</span>

                    {/* <span className="justify-content-start" style={{ fontSize: '30px', cursor: 'pointer' }} onClick={this.toggleClick}>☰</span> */}
                    <Nav className="justify-content-end" >

                        <div className="side-layer d-none d-md-block"></div>
                        <Nav.Item className="pl-2">
                            <Nav.Link ><img src={require("../../Assets/comment.png")} /> </Nav.Link>
                        </Nav.Item >
                        <Nav.Item className="pl-2">
                            <Nav.Link eventKey="link-1"> <img src={require("../../Assets/bell.png")} /> </Nav.Link>
                        </Nav.Item>

                        <Nav.Item className="pl-5">
                            <Dropdown>
                                <Dropdown.Toggle variant="light" id="dropdown-basic" className="user-button">
                                    <img src={require("../../Assets/avater.png")} className="usericon" />
                                    <span className="user-name"> sivakumar </span> </Dropdown.Toggle>
                                <Dropdown.Menu>
                                    <Dropdown.Item href="#/action-1">Setting</Dropdown.Item>
                                    <Dropdown.Item href="#/action-1">Profile</Dropdown.Item>
                                    <Dropdown.Item href="#/action-2">Logout</Dropdown.Item>
                                </Dropdown.Menu>
                            </Dropdown>
                        </Nav.Item>
                    </Nav>
                </Nav>

            </div>
        );
    }
}
export default withRouter(ToolBar);


