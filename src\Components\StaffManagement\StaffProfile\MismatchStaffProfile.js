import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import axios from '../../../axios';
import Loader from '../../../Widgets/Loader/Loader';
import { Accordion, Card, Button } from 'react-bootstrap';
import ProfileText from '../../../Widgets/ProfileText';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import { NotificationManager } from 'react-notifications';
import moment from 'moment';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { clickArrow } from '../../utils';
import { t } from 'i18next';
import { isModuleEnabled } from 'utils';

let id;
class MismatchStaffProfile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      first: '',
      last: '',
      middle: '',
      _nationality_id: '',
      nationality: '',
      building: '',
      city: '',
      district: '',
      zip_code: '',
      unit: '',
      passport_no: '',
      username: '',
      email: '',
      mobile: '',
      office_extension: '',

      _appointment_order_doc: '',
      _employee_id_doc: '',
      _address_doc: '',
      _degree_doc: '',
      _nationality_id_doc: '',

      middleError: '',
      firstError: '',
      office_room: '',
      uploadedDoc: [],
      employeeId: '',
      selectedIndex: '',
    };
  }

  componentDidMount() {
    id = this.props.location.search.split('=')[1];
    this.fetchApi();
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });
    axios.get(`user/staff/${id}`).then((res) => {
      const data = res.data.data;
      this.setState({
        data: data,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        family: data.name.family,
        _nationality_id: data.address._nationality_id,
        nationality: data.address.nationality,
        building: data.address.building,
        city: data.address.city,
        district: data.address.district,
        zip_code: data.address.zip_code,
        unit: data.address.unit,
        passport_no: data.address.passport_no,
        username: data.username,
        email: data.email,
        mobile: data.mobile,
        gender: data.gender,
        employeeId: data.employee_id,
        office_extension: data.office.office_extension,
        office_room: data.office.office_room_no,

        _address_doc: data.address._address_doc,
        _nationality_id_doc: data.address._nationality_id_doc,
        _appointment_order_doc: data.enrollment._appointment_order_doc,
        _employee_id_doc: data.id_doc._employee_id_doc,
        _degree_doc:
          data.qualifications.degree.length > 0 ? data.qualifications.degree[0]._degree_doc : '',

        correction_first_name: data.correction_first_name === true ? 'Correction Required' : '',
        correction_middle_name: data.correction_middle_name === true ? 'Correction Required' : '',
        correction_last_name: data.correction_last_name === true ? 'Correction Required' : '',
        correction_gender: data.correction_gender === true ? 'Correction Required' : '',
        correction_employee_id: data.correction_employee_id === true ? 'Correction Required' : '',
        correction_nationality_id:
          data.correction_nationality_id === true ? 'Correction Required' : '',
        isLoading: false,
        uploadedDoc: [
          {
            name: undefined,
          },
          {
            image: data.address._nationality_id_doc,
            name: 'National ID / Resident ID',
            isOpen: false,
          },
          {
            image: data.id_doc._employee_id_doc,
            name: 'Employee ID',
            isOpen: false,
          },
          {
            image: data.address._address_doc,
            name: `Postal address`,
            isOpen: false,
          },
          {
            image: data.enrollment._appointment_order_doc,
            name: 'Appointment order',
            isOpen: false,
          },
          {
            image:
              data.qualifications.degree.length > 0
                ? data.qualifications.degree[0]._degree_doc
                : '',
            name: 'Degree Name',
            isOpen: false,
          },
        ],
      });
    });
  };

  handleClickInvalid = () => {
    const data = {
      id: id,
      data: 'invalid',
    };
    this.setState({
      isLoading: true,
    });

    axios.post(`user/user_profile_validation`, data).then((res) => {
      this.setState({
        isLoading: false,
      });
      NotificationManager.success(t('user_management.success'));
      this.props.history.push({
        pathname: '/staff/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
          selectedTab: 4,
        },
      });
    });
  };
  handleClickValid = () => {
    const data = {
      id: id,
      data: 'valid',
    };
    this.setState({
      isLoading: true,
    });

    axios.post(`user/user_profile_validation`, data).then((res) => {
      this.props.history.push({
        pathname: '/staff/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
          selectedTab: 4,
        },
      });
      this.setState({
        isLoading: false,
      });
    });
  };

  handleClickOpen = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = true;
    this.setState({
      uploadedDoc,
    });
  };

  handleClickClose = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = false;
    this.setState({
      uploadedDoc,
    });
  };

  handleGoBack = () => {
    this.props.history.push({
      pathname: '/staff/management',
      state: {
        completeView: false,
        pendingView: true,
        inactiveView: false,
        selectedTab: 4,
      },
    });
  };

  render() {
    const { data, selectedIndex } = this.state;
    // let nationality = '';
    let nationality = '';
    let nationalId = '';
    let dob = '';

    if (data) {
      nationality = data.address._nationality_id !== undefined ? data.address._nationality_id : '';
      nationalId = data.address.nationality_id !== undefined ? data.address.nationality_id : '';
      dob = data.dob !== '' ? moment(String(data.dob).slice(0, 10)).format('DD-MM-YYYY') : '';
    }
    const userSensitiveData = isModuleEnabled('USER_SENSITIVE');
    const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
    const documentSecNeed = isModuleEnabled('DOCUMENT_SEC_NEED');
    const userDOBValidation = isModuleEnabled('USER_DOB');
    return (
      <React.Fragment>
        <Loader isLoading={this.state.isLoading} />
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'management_staff_profile'}></Trans>
        </div>
        <div className="main p-2">
          <div className="container">
            <div className="d-flex justify-content-end pt-3">
              <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                <Trans i18nKey={'back'}></Trans>
              </Button>
              {CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                'Mismatch',
                'Update Invalid'
              ) && (
                <Button className="m-2" onClick={this.handleClickInvalid}>
                  <Trans i18nKey={'user_management.tabs.Invalid'}></Trans>
                </Button>
              )}
            </div>
            <div className="clearfix"></div>
            <div className="white p-4 mb-5">
              <div className="row">
                <div className={documentSecNeed ? 'col-md-5' : 'col-md-6'}>
                  <div className="mt-0">
                    <h4 className="bold">
                      <Trans i18nKey={'personal'}></Trans>
                    </h4>
                    <ProfileText
                      title={<Trans i18nKey={'first_name'}></Trans>}
                      value={this.state.first}
                      error={this.state.correction_first_name}
                      className="border-bottom pb-1"
                    />
                    <ProfileText
                      title={<Trans i18nKey={'middle_name'}></Trans>}
                      value={this.state.middle ? this.state.middle : 'N/A'}
                      error={this.state.correction_middle_name}
                    />
                    <ProfileText
                      title={<Trans i18nKey={'last_name'}></Trans>}
                      value={this.state.last ? this.state.last : 'N/A'}
                      error={this.state.correction_last_name}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'family_name'}></Trans>}
                      value={this.state.family ? this.state.family : 'N/A'}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'gender'}></Trans>}
                      value={this.state.gender}
                      error={this.state.correction_gender}
                    />
                    {userDOBValidation && (
                      <ProfileText title={<Trans i18nKey={'dob'}></Trans>} value={dob} />
                    )}
                    <ProfileText
                      title={<Trans i18nKey={'employee_id'}></Trans>}
                      value={this.state.employeeId}
                      error={this.state.correction_employee_id}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'emailId'}></Trans>}
                      value={this.state.email}
                    />
                    {userSensitiveData && (
                      <ProfileText
                        title={
                          <Trans
                            i18nKey={'national/residence'}
                            values={{ optional: !hasNationalIdValidation ? '(Optional)' : '' }}
                          ></Trans>
                        }
                        value={nationalId ? nationalId : 'N/A'}
                        error={this.state.correction_nationality_id}
                      />
                    )}
                    <ProfileText
                      title={<Trans i18nKey={'nationality_optional'}></Trans>}
                      value={nationality ? nationality : 'N/A'}
                    />

                    {userSensitiveData && (
                      <ProfileText
                        title={<Trans i18nKey={'passport_number'}></Trans>}
                        value={this.state.passport_no}
                      />
                    )}
                  </div>
                  {userSensitiveData && (
                    <div className="mt-5">
                      <h4 className="bold">
                        <Trans i18nKey={'address'}></Trans>
                      </h4>
                      <ProfileText
                        title={<Trans i18nKey={'building_no'}></Trans>}
                        value={this.state.building}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'city_name'}></Trans>}
                        value={this.state.city}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'district_name'}></Trans>}
                        value={this.state.district}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'zip_code'}></Trans>}
                        value={this.state.zip_code}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'floor_no'}></Trans>}
                        value={this.state.unit}
                      />
                    </div>
                  )}
                  <div className="mt-5">
                    <h4 className="bold">
                      {' '}
                      <Trans i18nKey={'contact_details'}></Trans>
                    </h4>

                    <ProfileText
                      title={<Trans i18nKey={'office_room'}></Trans>}
                      value={this.state.office_room ? this.state.office_room : 'N/A'}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'office_extension'}></Trans>}
                      value={this.state.office_extension ? this.state.office_extension : 'N/A'}
                    />
                    <ProfileText
                      title={<Trans i18nKey={'mobile_number'}></Trans>}
                      value={this.state.mobile}
                    />
                  </div>
                </div>
                {documentSecNeed && (
                  <div className="col-md-7">
                    <div className="mt-0">
                      <h4 className="bold d-flex">
                        {' '}
                        <Trans i18nKey={'uploaded_documents'}></Trans>{' '}
                      </h4>
                      <Accordion defaultActiveKey="">
                        {this.state.uploadedDoc.map((data, index) => (
                          <React.Fragment key={index}>
                            {data.name !== undefined && (
                              <Card>
                                <Accordion.Toggle
                                  as={Card.Header}
                                  eventKey={index}
                                  onClick={() => clickArrow.call(this, index)}
                                  className="card-header-icon"
                                >
                                  <div className="float-left">{data.name}</div>
                                  <div className="float-right">
                                    <i
                                      className={`fa fa-chevron-${
                                        selectedIndex !== index ? 'down' : 'up'
                                      } f-14`}
                                    ></i>
                                  </div>
                                </Accordion.Toggle>

                                <Accordion.Collapse eventKey={index}>
                                  <Card.Body className="bg-white">
                                    {data.image !== '' && data.image !== undefined ? (
                                      <img
                                        className="w-100"
                                        onClick={() => this.handleClickOpen(index)}
                                        src={data.image}
                                        alt="#"
                                      />
                                    ) : (
                                      <div className="float-left">
                                        {' '}
                                        <Trans i18nKey={'no_image'}></Trans>
                                      </div>
                                    )}

                                    {data.isOpen === true && (
                                      <Lightbox
                                        clickOutsideToClose={false}
                                        mainSrc={data.image}
                                        onCloseRequest={() => this.handleClickClose(index)}
                                      />
                                    )}
                                  </Card.Body>
                                </Accordion.Collapse>
                              </Card>
                            )}
                          </React.Fragment>
                        ))}
                      </Accordion>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

MismatchStaffProfile.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
};

export default withRouter(MismatchStaffProfile);
