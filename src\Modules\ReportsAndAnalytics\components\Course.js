import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useParams, useHistory, Redirect } from 'react-router-dom';
import { connect } from 'react-redux';
import { Map } from 'immutable';

import CourseSideNav from './CourseSideNav';
import CourseOverview from './CourseOverview';
import CourseSessionStatus from './CourseSessionStatus';
import CourseStudentDetails from './CourseStudentDetails';
import CourseStaffDetails from './CourseStaffDetails';
import AttendanceLog from './AttendanceLog';
import Activity from '../Activity';
import * as actions from '../../../_reduxapi/reports_and_analytics/action';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import { selectCourseOverviewData } from '../../../_reduxapi/reports_and_analytics/selectors';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getActiveLink } from '../utils';
import { getURLParams, dString, getVersionName } from '../../../utils';
import Settings from './Settings';
import { getLang } from 'utils';
const lang = getLang();

function Course(props) {
  const { courseOverview, activeInstitutionCalendar, getCourseOverviewData } = props;
  const params = useParams();
  const history = useHistory();

  const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id');

  const programId = params.programId ? dString(params.programId) : '';
  const courseId = params.courseId ? dString(params.courseId) : '';
  const level = getURLParams('level', true);
  const term = getURLParams('term', true);
  const isRotation = getURLParams('rotation', true) === 'yes';
  const rotationCount = getURLParams('rotationCount', true);

  useEffect(() => {
    if (activeInstitutionCalendarId && programId && courseId && level && term) {
      getCourseOverviewData({
        institutionCalendarId: activeInstitutionCalendarId,
        programId,
        courseId,
        level,
        term,
        ...(isRotation && { rotationCount }),
      });
    }
  }, [
    getCourseOverviewData,
    activeInstitutionCalendarId,
    programId,
    courseId,
    level,
    term,
    isRotation,
    rotationCount,
  ]);

  const courseNo = courseOverview.getIn(['course_details', 'course_no'], '');
  const courseName = courseOverview.getIn(['course_details', 'course_name'], '');
  const versionName = getVersionName(courseOverview.get('course_details', Map()));

  function getMatchedComponentData() {
    const key = params.category;
    switch (key) {
      case 'overview': {
        return {
          pageTitle: `${courseNo} - ${courseName}${versionName}`,
          sideBar: <CourseSideNav />,
          matchedComponent: <CourseOverview />,
        };
      }
      case 'activity': {
        return {
          pageTitle: `${courseNo} - ${courseName}${versionName}`,
          sideBar: <CourseSideNav />,
          matchedComponent: <Activity />,
        };
      }
      case 'attendance-log': {
        return {
          pageTitle: `${courseNo} - ${courseName}${versionName}`,
          sideBar: <CourseSideNav />,
          matchedComponent: <AttendanceLog />,
        };
      }
      case 'session-status': {
        return {
          pageTitle: `${courseNo} - ${courseName}${versionName}`,
          sideBar: <CourseSideNav />,
          matchedComponent: <CourseSessionStatus />,
        };
      }
      case 'students': {
        return {
          pageTitle: `${courseNo} - ${courseName}${versionName}`,
          sideBar: <CourseSideNav />,
          matchedComponent: <CourseStudentDetails />,
        };
      }
      case 'staffs': {
        return {
          pageTitle: `${courseNo} - ${courseName}${versionName}`,
          sideBar: <CourseSideNav />,
          matchedComponent: <CourseStaffDetails />,
        };
      }
      case 'settings': {
        return {
          pageTitle: `${courseNo} - ${courseName}${versionName}`,
          sideBar: <CourseSideNav />,
          matchedComponent: <Settings />,
        };
      }

      default:
        return {
          pageTitle: '',
          sideBar: <div></div>,
          matchedComponent: <div>404 | Page Not Found</div>,
        };
    }
  }

  function handleBackClick() {
    history.goBack();
  }

  const { pageTitle, sideBar, matchedComponent } = getMatchedComponentData();

  const linkActive = getActiveLink(CheckPermission) !== '';
  let redirectUrl = '';
  if (!linkActive) {
    redirectUrl = <Redirect to="/InstitutionCalendar" />;
  }
  return (
    <div className="main bg-gray pb-5">
      {redirectUrl}
      <div
        className="bg-white border-bottom pt-3"
        style={{ boxShadow: '0px 0px 6px 1px rgb(0 0 0 / 23%)' }}
      >
        <div className="container-fluid">
          <div className="row">
            <div className="col-md-12">
              <p className="font-weight-bold mb-3 f-17  text-left">
                <i
                  className={`fa ${
                    lang === 'ar' ? 'fa-arrow-right' : 'fa-arrow-left'
                  }  mr-3 cursor-pointer`}
                  aria-hidden="true"
                  onClick={() => handleBackClick()}
                ></i>
                {pageTitle}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container-fluid">
        <div className="row">
          <div className="col-md-2 program_sidebar pb-3">{sideBar}</div>
          <div className="col-md-10">{matchedComponent}</div>
        </div>
      </div>
    </div>
  );
}

Course.propTypes = {
  courseOverview: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getCourseOverviewData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    courseOverview: selectCourseOverviewData(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, actions)(Course);
