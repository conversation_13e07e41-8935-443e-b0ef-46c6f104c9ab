import { t } from 'i18next';
import React, { Fragment, useEffect, useReducer } from 'react';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { getLang, indVerRename } from '../../../../utils';

import { toggleModal, saveCurriculum } from '../../../../_reduxapi/actions/calender';
import { PrimaryButton, Null, FlexWrapper, BlockWrapper } from '../../Styled';
import { initialState_curriculum, curriculum_reducer } from './reducers';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import { yearReplace } from '_reduxapi/util';
const lang = getLang();
const dataAlign = (data, dispatchFn, userId, programId, activeInstitutionCalendar) => {
  let final = {};
  final._program_id = programId;
  final._institution_calendar_id = activeInstitutionCalendar;
  final._creater_id = userId;
  final.data = data.preparingData;
  return dispatchFn(final);
};

const CurriculumModal = ({
  toggleModal,
  saveCurriculum,
  userId,
  programId,
  curriculum_years,
  curriculum_levels,
  curriculums,
  activeInstitutionCalendar,
  terms,
}) => {
  const [curriculumVersion, setCurriculumVersion] = useReducer(
    curriculum_reducer,
    initialState_curriculum
  );

  useEffect(() => {
    const filteredLevel =
      curriculum_levels &&
      curriculum_levels.length > 0 &&
      curriculum_levels.map((item) => {
        return { year_level: item, version: curriculums.length > 0 ? curriculums[0] : '' };
      });
    const filteredYear =
      curriculum_years &&
      curriculum_years.length > 0 &&
      curriculum_years.map((item) => {
        return {
          year_level: item,
          version: curriculums.length > 0 ? curriculums[0] : '',
        };
      });

    const preparingData =
      terms.length > 0
        ? terms.map((term) => {
            return {
              batch: term.term_name,
              type: '',
              curriculum: [],
            };
          })
        : [];
    setCurriculumVersion({
      type: 'INITIAL_LOAD_MODIFY',
      curriculum_years: filteredYear,
      curriculum_levels: filteredLevel,
      preparingData,
    });
  }, [setCurriculumVersion, curriculum_years, curriculum_levels, curriculums, terms]);

  function getCurriculumOptions() {
    let curriculumOptions = [];
    if (curriculums && curriculums.length > 0) {
      curriculumOptions = curriculums.map((list) => (
        <option key={list} value={list}>
          {list}
        </option>
      ));
    }
    return curriculumOptions;
  }

  function saveEnabled() {
    return !curriculumVersion.preparingData.some((item) => item.type === '');
  }

  return (
    <Fragment>
      <BlockWrapper>
        <p className={`set-versions ${lang === 'ar' ? 'text-left' : ''}`}>
          <Trans i18nKey={'program_calendar.set_curriculum_versions'}></Trans>
        </p>
        {curriculumVersion.preparingData.length > 0 &&
          curriculumVersion.preparingData.map((data, index) => {
            return (
              <React.Fragment key={index}>
                <div className="row">
                  <div className="col-md-5">
                    <p className={`pt-2 mb-0 black-normal ${lang === 'ar' ? 'text-left' : ''}`}>
                      {' '}
                      {data.batch} {indVerRename('Term', programId)}
                    </p>
                  </div>
                  <div className="col-md-7">
                    <select
                      onChange={(e) =>
                        setCurriculumVersion({
                          type: 'CURRICULUM_CHANGE_MODIFY_NEW',
                          payload: e.target.value,
                          terms: data.batch,
                        })
                      }
                      value={data.type}
                      className="p-2 w-100"
                    >
                      <option value="">{t('program_calendar.select')}</option>
                      <option value="level">
                        {t('program_calendar.level_wise_view', {
                          Level: indVerRename('Level', programId),
                        })}
                      </option>
                      <option value="year">{t('program_calendar.year_wise_view')}</option>
                    </select>
                  </div>
                </div>

                <Fragment>
                  {data.curriculum &&
                    data.curriculum.length > 0 &&
                    data.curriculum.map((item, index) => {
                      return (
                        <div className="row pt-4" key={index}>
                          <div className="col-md-5">
                            <p className="pt-2 mb-0 black-normal">{yearReplace(item.year_level)}</p>
                          </div>
                          <div className="col-md-7">
                            <select
                              onChange={(e) =>
                                setCurriculumVersion({
                                  type: 'VALUE_CHANGE_MODIFY_NEW',
                                  terms: data.batch,
                                  payload: e.target.value,
                                  name: index,
                                })
                              }
                              value={item.version}
                              className="p-2 w-100"
                            >
                              {getCurriculumOptions()}
                            </select>
                          </div>
                        </div>
                      );
                    })}
                </Fragment>

                <hr />
              </React.Fragment>
            );
          })}
      </BlockWrapper>

      <FlexWrapper className="ji_end">
        <Null />
        <PrimaryButton className="light" onClick={() => toggleModal()}>
          <Trans i18nKey={'cancel'}></Trans>
        </PrimaryButton>
        {saveEnabled() && (
          <PrimaryButton
            className="bordernone"
            onClick={() =>
              dataAlign(
                curriculumVersion,
                saveCurriculum,
                userId,
                programId,
                activeInstitutionCalendar.get('_id', '')
              )
            }
          >
            <Trans i18nKey={'save'}></Trans>
          </PrimaryButton>
        )}
      </FlexWrapper>
    </Fragment>
  );
};

CurriculumModal.propTypes = {
  id: PropTypes.string,
  toggleModal: PropTypes.func,
  saveCurriculum: PropTypes.func,
  userId: PropTypes.string,
  programId: PropTypes.string,
  curriculum_years: PropTypes.object,
  curriculum_levels: PropTypes.object,
  curriculums: PropTypes.object,
  activeInstitutionCalendar: PropTypes.object,
  terms: PropTypes.array,
};

const mapStateToProps = function (state) {
  // ({ calender, auth }) => ({
  const { calender, auth } = state;
  return {
    userId: auth.token.replace(/"/g, ''),
    programId: calender.programId,
    curriculum_years: calender.curriculum_years,
    curriculum_levels: calender.curriculum_levels,
    curriculums: calender.curriculum && calender.curriculum.length > 0 ? calender.curriculum : [],
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    terms: calender?.term !== undefined ? calender?.term : [],
  };
};

export default connect(mapStateToProps, { toggleModal, saveCurriculum })(CurriculumModal);
