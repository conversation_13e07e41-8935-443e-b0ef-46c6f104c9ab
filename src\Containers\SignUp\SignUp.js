import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Button } from 'react-bootstrap';
import Loader from '../../Widgets/Loader/Loader';
import Input from '../../Widgets/FormElements/Input/Input';
import { NotificationManager } from 'react-notifications';
import * as Constants from '../../constants';
import axios from '../../axios';
import Digiclass_white from 'Assets/Digiclass_white.svg';
import { envSignUpService, isStrongPassword, setDSCookie } from 'utils';
import Footer from 'Shared/Footer';
import useUnifyServiceHook from 'Hooks/useUnifyServiceHook';
import { StrongPassword } from 'Containers/Login/ForgotPassword';

const emailVal = Constants.EMAIL_VALIDATION;
const Number = Constants.NUMBER_VALIDATION;

const toasterMessage = (msg) => {
  NotificationManager.success(msg);
  // store.addNotification({
  //   content: (
  //     <div className="success_notification">
  //       {msg}
  //       <img
  //         alt={msg}
  //         src={require('../../Assets/elipsis.svg')}
  //         className="notification-item-img"
  //       />{' '}
  //     </div>
  //   ),
  //   container: 'top-right',
  //   animationIn: ['animated', 'fadeIn'],
  //   animationOut: ['animated', 'zoomOut'],
  //   dismiss: {
  //     duration: 2000,
  //   },
  // });
};
class SignUp extends Component {
  constructor() {
    super();
    this.state = {
      isLoading: false,
      emailError: '',
      pswError: '',
      newPswError: '',
      confirmPswError: '',
      emailVerifyTap: true,
      createPswTap: false,
      createMobileTap: false,
      resendOtp: false,
      email: '',
      psw: '',
      signInMessage: '',
      newPsw: '',
      confirmPsw: '',
      mobile: '',
      minutes: 3,
      seconds: 0,
      staffId: '',
      user_type: '',
    };
    this.emailRef = React.createRef();
  }

  componentDidMount() {
    if (this.emailRef.current !== null) {
      this.emailRef.current.focus();
    }
  }

  componentWillUnmount() {
    clearInterval(this.myInterval);
  }

  timer = () => {
    this.myInterval = setInterval(() => {
      const { seconds, minutes } = this.state;
      if (seconds > 0) {
        this.setState(({ seconds }) => ({
          seconds: seconds - 1,
        }));
      }
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(this.myInterval);
        } else {
          this.setState(({ minutes }) => ({
            minutes: minutes - 1,
            seconds: 59,
          }));
        }
      }
    }, 1000);
  };

  logIn = () => {
    this.props.history.push('/login');
  };

  onChange = (e, name) => {
    e.preventDefault();
    if (name === 'email') {
      this.setState({
        email: e.target.value,
        emailError: '',
      });
    }
    if (name === 'psw') {
      this.setState({
        psw: e.target.value,
        pswError: '',
      });
    }
    if (name === 'newPsw') {
      this.setState({
        newPsw: e.target.value,
        newPswError: '',
      });
    }
    if (name === 'confirmPsw') {
      this.setState({
        confirmPsw: e.target.value,
        confirmPswError: '',
      });
    }
    if (name === 'mobile') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mobile: e.target.value,
        mobileError: '',
      });
    }
    if (name === 'otp') {
      if (isNaN(e.target.value)) return;
      this.setState({
        otp: e.target.value,
        otpError: '',
      });
    }
  };

  validation = () => {
    let emailError = '';
    let pswError = '';

    if (!this.state.email) {
      emailError = 'Email is Required';
    } else if (!emailVal.test(this.state.email)) {
      emailError = 'Pls enter Valid Email Address';
    }

    if (!this.state.psw) {
      pswError = 'Password is Required';
    } else if (this.state.psw.length <= 7) {
      pswError = 'Minimum 8 character is required ';
    }

    if (emailError || pswError) {
      this.setState({
        emailError,
        pswError,
      });
      return false;
    }
    return true;
  };

  createPswTapValidation = () => {
    let newPswError = '';
    let confirmPswError = '';

    if (!this.state.newPsw) {
      newPswError = 'New Password is Required';
    } else if (this.state.newPsw.length <= 7) {
      newPswError = 'Minimum 8 character is required ';
    }

    if (!this.state.confirmPsw) {
      confirmPswError = 'Confirm Password is Required';
    } else if (this.state.confirmPsw.length <= 7) {
      confirmPswError = 'Minimum 8 character is required ';
    } else if (this.state.confirmPsw !== this.state.newPsw) {
      confirmPswError = 'Confirm Password Not Match ';
    }

    if (newPswError || confirmPswError) {
      this.setState({
        newPswError,
        confirmPswError,
      });
      return false;
    }
    return true;
  };

  mobileValidation = () => {
    let mobileError = '';
    const countryCodeLength = envSignUpService('REACT_APP_COUNTRY_CODE_LENGTH', false);
    if (!this.state.mobile) {
      mobileError = 'Mobile is Required';
    } else if (parseInt(String(this.state.mobile).length) !== parseInt(countryCodeLength)) {
      mobileError = `Should be ${countryCodeLength} character is required `;
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Numbers Only Allow ';
    } else if (parseInt(this.state.mobile) > 1000000000000) {
      mobileError = 'Pls Enter Safe Mobile Number ';
    }
    if (mobileError) {
      this.setState({
        mobileError,
      });
      return false;
    }
    return true;
  };

  otpValidation = () => {
    let mobileError = '';
    let otpError = '';
    const countryCodeLength = envSignUpService('REACT_APP_COUNTRY_CODE_LENGTH', false);
    if (!this.state.mobile) {
      mobileError = 'Mobile is Required';
    } else if (parseInt(String(this.state.mobile).length) !== parseInt(countryCodeLength)) {
      mobileError = `Should be ${countryCodeLength} character is required `;
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Numbers Only Allow ';
    } else if (parseInt(this.state.mobile) > 1000000000000) {
      mobileError = 'Pls Enter Safe Mobile Number ';
    }

    if (!this.state.otp) {
      otpError = 'OTP is Required';
    } else if (this.state.otp.length <= 3) {
      otpError = 'Minimum 4 character is required ';
    } else if (!Number.test(this.state.otp)) {
      otpError = 'Numbers Only Allow ';
    }

    if (mobileError || otpError) {
      this.setState({
        mobileError,
        otpError,
      });
      return false;
    }
    return true;
  };

  handleSignIn = (e) => {
    e.preventDefault();
    if (this.validation()) {
      const signIn = {
        email: this.state.email,
        password: this.state.psw,
      };

      this.setState({ isLoading: true });
      axios
        .post(`/user/signup`, signIn)
        .then((res) => {
          if (res.data.status_code === 200) {
            let user_type = res.data.data.user_type;
            let staffId = res.data.data._id;
            setDSCookie('login-services', JSON.stringify(res.data.data.services));
            toasterMessage('Verified Successfully');
            this.setState({
              isLoading: false,
              signInMessage: '',
              emailVerifyTap: false,
              createPswTap: true,
              staffId: staffId,
              user_type: user_type,
            });
          } else {
            this.setState({
              signInMessage: res.data.message,
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          this.setState({
            signInMessage: error.response.data.message,
            isLoading: false,
          });
        });
    }
  };

  handlePasswordSubmit = (e) => {
    e.preventDefault();
    if (this.createPswTapValidation()) {
      const setPassword = {
        // email: this.state.email,
        id: this.state.staffId,
        old_password: this.state.psw,
        new_password: this.state.confirmPsw,
      };
      const passwordInvalid =
        this.state.newPsw !== this.state.confirmPsw || !isStrongPassword(this.state.newPsw);
      if (passwordInvalid) {
        return;
      }

      this.setState({ isLoading: true });

      axios
        .post(`/user/set_password`, setPassword)
        .then((res) => {
          if (res.data.status_code === 200) {
            toasterMessage('Created Password Successfully');
            this.setState({
              isLoading: false,
              signInMessage: '',
              emailVerifyTap: false,
              createPswTap: false,
              createMobileTap: true,
            });
          } else {
            this.setState({
              signInMessage: res.data.message,
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          this.setState({
            signInMessage: error.response.data.message,
            isLoading: false,
          });
        });
    }
  };

  handleMobileSubmit = (e) => {
    e.preventDefault();
    if (this.mobileValidation()) {
      const countryCode = envSignUpService('REACT_APP_COUNTRY_CODE', false);
      const mobileNumber = {
        // email: this.state.email,
        id: this.state.staffId,
        mobile: countryCode + this.state.mobile,
      };
      this.setState({ isLoading: true });
      axios
        .post(`/user/register_mobile`, mobileNumber)
        .then((res) => {
          if (res.data.status_code === 200) {
            toasterMessage('OTP Sent Successfully');
            this.setState(
              {
                isLoading: false,
                signInMessage: '',
                emailVerifyTap: false,
                createPswTap: false,
                createMobileTap: true,
                resendOtp: true,
              },
              () => {
                this.timer();
              }
            );
          } else {
            this.setState({
              signInMessage: res.data.message,
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          this.setState({
            signInMessage: error.response.data.message,
            isLoading: false,
          });
        });
    }
  };

  handleResendOtp = (e) => {
    e.preventDefault();
    // const signIn = {
    //   email: this.state.email,
    //   id: this.state.staffId,
    //   password: this.state.psw,
    //   otp_mode: 'mobile',
    // };
    const countryCode = envSignUpService('REACT_APP_COUNTRY_CODE', false);
    const mobileNumber = {
      id: this.state.staffId,
      mobile: countryCode + this.state.mobile,
    };
    this.setState({ isLoading: true, minutes: 3, seconds: 0, otp: '', otpError: '' });
    axios
      .post(`/user/register_mobile`, mobileNumber)
      .then((res) => {
        if (res.data.status_code === 200) {
          toasterMessage('OTP Sent Successfully');
          this.setState(
            {
              isLoading: false,
              signInMessage: '',
              emailVerifyTap: false,
              otpVerificationTap: true,
            },
            () => {
              this.timer();
            }
          );
        } else {
          this.setState({
            signInMessage: res.data.message,
            isLoading: false,
          });
        }
      })
      .catch((error) => {
        this.setState({
          signInMessage: error.response.data.message,
          isLoading: false,
        });
      });
  };

  handleSubmit = (e) => {
    e.preventDefault();
    if (this.mobileValidation()) {
      const countryCode = envSignUpService('REACT_APP_COUNTRY_CODE', false);
      const mobileNumber = {
        id: this.state.staffId,
        mobile: countryCode + this.state.mobile,
      };
      this.setState({ isLoading: true });
      axios
        .post(`/user/register_mobile`, mobileNumber)
        .then((res) => {
          if (res.data.status_code === 200) {
            if (this.state.user_type === 'staff') {
              this.props.history.push({
                pathname: '/staffprofile',
                search: '?id=' + this.state.staffId,
              });
            } else if (this.state.user_type === 'student') {
              this.props.history.push({
                pathname: '/student/profile',
                search: '?id=' + this.state.staffId,
              });
            }
            toasterMessage('Verified Successfully');
            this.setState({
              isLoading: false,
            });
          } else {
            this.setState({
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          this.setState({
            signInMessage: error.response.data.message,
            isLoading: false,
          });
        });
    }
  };

  handleOtpSubmit = (e) => {
    e.preventDefault();
    if (this.otpValidation()) {
      const mobileNumber = {
        id: this.state.staffId,
        otp: this.state.otp,
      };
      this.setState({ isLoading: true });
      axios
        .post(`/user/otp_verify`, mobileNumber)
        .then((res) => {
          if (res.data.status_code === 200) {
            if (this.state.user_type === 'staff') {
              this.props.history.push({
                pathname: '/staffprofile',
                search: '?id=' + this.state.staffId,
              });
            } else if (this.state.user_type === 'student') {
              this.props.history.push({
                pathname: '/student/profile',
                search: '?id=' + this.state.staffId,
              });
            }
            toasterMessage('OTP Verified Successfully');
            this.setState({
              isLoading: false,
              signInMessage: '',
              emailVerifyTap: false,
              createPswTap: false,
              createMobileTap: true,
              resendOtp: true,
            });
          } else {
            this.setState({
              signInMessage: res.data.message,
              isLoading: false,
            });
          }
        })
        .catch((error) => {
          this.setState({
            signInMessage: error.response.data.message,
            isLoading: false,
          });
        });
    }
  };

  isButtonDisabled = () => {
    const { newPsw, confirmPsw } = this.state;
    return !newPsw || !confirmPsw || newPsw !== confirmPsw || !isStrongPassword(newPsw);
  };

  handlePasswordKeyDown = (e) => {
    if (e.key === ' ' || e.keyCode === 32) {
      e.preventDefault();
      return false;
    }
  };

  render() {
    const { minutes, seconds } = this.state;
    const { unifyData } = this.props;
    const verifyMobile = envSignUpService('MOBILE', true);
    const countryCodeLength = envSignUpService('REACT_APP_COUNTRY_CODE_LENGTH', false);
    const isDisabled = this.isButtonDisabled();
    return (
      <div className="">
        <Loader isLoading={this.state.isLoading} />
        <div className="login-bg">
          <div className="container ">
            <div className="row justify-content-center pt-5">
              <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                <h3 className="text-center">
                  {' '}
                  <img src={Digiclass_white} alt="DigiClass" width={'70%'} />
                </h3>
              </div>
            </div>

            <div className="row justify-content-center pt-4">
              <div className="col-xl-4 col-lg-5 col-md-7 col-7">
                <div className="outter-login width-420">
                  <div className="row">
                    <div className="col-xl-7 col-lg-7 col-md-7 col-7">
                      <p className="f-20 pt-3 text-skyblue"> Sign Up </p>
                    </div>
                    <div className="col-xl-5 col-lg-5 col-md-5 col-5">
                      {unifyData.get('collegeLogo', '') !== '' && (
                        <img
                          src={unifyData.get('collegeLogo', '')}
                          alt="Digi-scheduler"
                          className="univ-logo"
                        />
                      )}
                    </div>
                  </div>

                  {/* 1 signIn Verification check start */}

                  {this.state.emailVerifyTap === true && (
                    <React.Fragment>
                      {this.state.signInMessage === '' ? (
                        ''
                      ) : (
                        <div className="bg-gray">
                          <p className="text-red text-center p-1">
                            <i className="fa fa-exclamation-circle" aria-hidden="true">
                              {' '}
                            </i>{' '}
                            {this.state.signInMessage}
                          </p>
                        </div>
                      )}

                      <div className="pt-2">
                        <Input
                          elementType={'input'}
                          elementConfig={{
                            type: 'text',
                            placeholder: 'Enter Email Id',
                            ref: this.emailRef,
                          }}
                          value={this.state.email}
                          label={'Email'}
                          maxLength={50}
                          feedback={this.state.emailError}
                          changed={(e) => this.onChange(e, 'email')}
                          className={'form-control'}
                        />
                      </div>

                      <div className="pt-2">
                        <Input
                          elementType={'input'}
                          elementConfig={{
                            type: 'password',
                            placeholder: 'Enter Password',
                          }}
                          value={this.state.psw}
                          label={'Password'}
                          maxLength={30}
                          feedback={this.state.pswError}
                          changed={(e) => this.onChange(e, 'psw')}
                          className={'form-control'}
                        />
                      </div>

                      {/* <div className="pt-4">
                        <p className="f-17 text-right text-blue" onClick={this.logIn} style={{cursor:'pointer'}}>
                          {" "}
                          Login
                        </p>
                      </div> */}

                      {/* <div className="pt-4">
                        <p className="f-17 text-right text-blue">
                          {" "}
                          Forgot Password?
                        </p>
                      </div> */}

                      <div className="pt-3">
                        <Button
                          disabled={this.state.isLoading}
                          variant="primary"
                          size="lg"
                          block
                          onClick={this.handleSignIn}
                          className="f-14"
                        >
                          SIGN UP
                        </Button>
                      </div>
                    </React.Fragment>
                  )}

                  {/* 1 signIn Verification check end */}

                  {/* 2 signIn new password insert start */}

                  {this.state.createPswTap === true && (
                    <React.Fragment>
                      {this.state.signInMessage === '' ? (
                        ''
                      ) : (
                        <div className="bg-gray">
                          <p className="text-red text-center p-1">
                            <i className="fa fa-exclamation-circle" aria-hidden="true">
                              {' '}
                            </i>{' '}
                            {this.state.signInMessage}
                          </p>
                        </div>
                      )}

                      <div className="pt-2">
                        <Input
                          elementType={'input'}
                          elementConfig={{
                            type: 'password',
                            placeholder: 'Create New Password',
                            onKeyDown: this.handlePasswordKeyDown,
                          }}
                          value={this.state.newPsw}
                          label={'Create New Password'}
                          maxLength={30}
                          feedback={this.state.newPswError}
                          changed={(e) => this.onChange(e, 'newPsw')}
                          className={'form-control'}
                        />
                      </div>

                      <div className="pt-2">
                        <Input
                          elementType={'input'}
                          elementConfig={{
                            type: 'password',
                            placeholder: 'Confirm Password',
                            onKeyDown: this.handlePasswordKeyDown,
                          }}
                          value={this.state.confirmPsw}
                          label={'Confirm Password'}
                          maxLength={30}
                          feedback={this.state.confirmPswError}
                          changed={(e) => this.onChange(e, 'confirmPsw')}
                          className={'form-control'}
                        />
                      </div>

                      <StrongPassword
                        newPsw={this.state.newPsw}
                        confirmPsw={this.state.confirmPsw}
                      />

                      <div className="pt-5">
                        <Button
                          size="lg"
                          block
                          onClick={this.handlePasswordSubmit}
                          className="f-14"
                          disabled={isDisabled}
                          variant={isDisabled ? 'secondary' : 'primary'}
                        >
                          SAVE AND CONTINUE{' '}
                        </Button>
                      </div>
                    </React.Fragment>
                  )}

                  {/* 2 signIn new password insert end */}

                  {/* 3 signIn mobile number insert Otp start */}

                  {this.state.createMobileTap === true && (
                    <React.Fragment>
                      {this.state.signInMessage === '' ? (
                        ''
                      ) : (
                        <div className="bg-gray">
                          <p className="text-red text-center p-1">
                            <i className="fa fa-exclamation-circle" aria-hidden="true">
                              {' '}
                            </i>{' '}
                            {this.state.signInMessage}
                          </p>
                        </div>
                      )}

                      <div className="pt-2">
                        <Input
                          elementType={'input'}
                          elementConfig={{
                            type: 'text',
                            placeholder: 'Enter Mobile No',
                          }}
                          value={this.state.mobile}
                          label={'Enter Mobile Number'}
                          maxLength={countryCodeLength !== undefined ? countryCodeLength : 10}
                          // disabled={this.state.resendOtp === true && 'disabled'  }
                          feedback={this.state.mobileError}
                          changed={(e) => this.onChange(e, 'mobile')}
                          className={'form-control'}
                        />
                      </div>

                      {verifyMobile && (
                        <div className="pt-2">
                          <Input
                            elementType={'input'}
                            elementConfig={{
                              type: 'text',
                              placeholder: 'Enter 4 Digit OTP',
                            }}
                            value={this.state.otp}
                            label={'Enter OTP'}
                            maxLength={4}
                            disabled={this.state.resendOtp === true ? '' : 'disabled'}
                            feedback={this.state.otpError}
                            changed={(e) => this.onChange(e, 'otp')}
                            className={'form-control'}
                          />
                        </div>
                      )}

                      <div className="row pt-5">
                        {this.state.resendOtp === true && (
                          <div className="col-md-12">
                            {minutes === 0 && seconds === 0 ? (
                              ''
                            ) : (
                              <h1 className="f-16">
                                Resend OTP In: {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
                              </h1>
                            )}
                          </div>
                        )}
                        {verifyMobile && (
                          <div className="col-md-6">
                            {minutes === 0 && seconds === 0 ? (
                              <Button
                                variant="primary"
                                size="lg"
                                block
                                onClick={this.handleResendOtp}
                                className="f-14"
                              >
                                Resend OTP{' '}
                              </Button>
                            ) : (
                              <React.Fragment>
                                {this.state.resendOtp === true ? (
                                  <Button
                                    variant="primary"
                                    size="lg"
                                    block
                                    className="f-14"
                                    disabled
                                  >
                                    Send OTP{' '}
                                  </Button>
                                ) : (
                                  <Button
                                    variant="primary"
                                    size="lg"
                                    block
                                    onClick={this.handleMobileSubmit}
                                    className="f-14"
                                  >
                                    Send OTP{' '}
                                  </Button>
                                )}
                              </React.Fragment>
                            )}

                            {/* {this.state.resendOtp === true ? (
                            <Button
                              variant="primary"
                              size="lg"
                              block
                              className="f-14"
                              disabled
                            >
                              send OTP{" "}
                            </Button>
                          ) : (
                            <Button
                              variant="primary"
                              size="lg"
                              block
                              onClick={this.handleMobileSubmit}
                              className="f-14"
                            >
                              send OTP{" "}
                            </Button>
                          )} */}
                          </div>
                        )}
                        <div className={`col-md-${!verifyMobile ? '12' : '6'}`}>
                          <Button
                            variant="primary"
                            size="lg"
                            block
                            onClick={!verifyMobile ? this.handleSubmit : this.handleOtpSubmit}
                            className="f-14"
                            disabled={!this.state.resendOtp && verifyMobile}
                          >
                            Done{' '}
                          </Button>
                        </div>
                      </div>

                      {/* 
                      <div className="row pt-5">
                        <div className="col-md-12">
                          {minutes === 0 && seconds === 0 ? (
                            ""
                          ) : (
                            <h1 className="f-16">
                              Expires in: {minutes}:
                              {seconds < 10 ? `0${seconds}` : seconds}
                            </h1>
                          )}
                        </div>
                        {minutes === 0 && seconds === 0 ? (
                          <div className="col-md-6">
                            <Button
                              variant="primary"
                              size="lg"
                              block
                              className="f-14"
                              disabled
                            >
                              Resend Otp{" "}
                            </Button>
                          </div>
                        ) : (
                          <div className="col-md-6">
                            <React.Fragment>
                              <Button
                                variant="primary"
                                size="lg"
                                block
                                onClick={this.handleResendOtp}
                                className="f-14"
                              >
                                Resend Otp{" "}
                              </Button>
                            </React.Fragment>
                          </div>
                        )}
                        <div className="col-md-6">
                          <Button
                            variant="primary"
                            size="lg"
                            block
                            onClick={this.handleOtpSubmit}
                            className="f-14"
                          >
                            Done{" "}
                          </Button>
                        </div>
                     
                     
                     
                     
                     
                     
                      </div>
              */}
                    </React.Fragment>
                  )}

                  {/* 3 signIn mobile number insert Otp end */}
                </div>
              </div>
            </div>
            <Footer />
          </div>
        </div>
      </div>
    );
  }
}

SignUp.propTypes = {
  history: PropTypes.object,
};

const ConnectedSignUp = SignUp;

const SignUpWrapper = (props) => {
  const { unifyData, loading } = useUnifyServiceHook();

  return (
    <>
      <Loader isLoading={loading} />
      <ConnectedSignUp {...props} unifyData={unifyData} />
    </>
  );
};

export default SignUpWrapper;
