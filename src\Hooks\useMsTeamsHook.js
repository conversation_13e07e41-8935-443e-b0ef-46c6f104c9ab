import { initializeMsal as initializeMsalAuth } from 'authConfig';

const useMsTeamsHook = ({ unifyData }) => {
  const { msalInstance, loginRequest } = initializeMsalAuth(unifyData);
  async function initializeMsal() {
    try {
      await msalInstance.initialize();
      console.log('MSAL initialized successfully');
    } catch (error) {
      console.error('Error initializing MSAL:', error);
    }
  }

  return {
    initializeMsal,
    msalInstance,
    loginRequest,
  };
};

export default useMsTeamsHook;
