import { t } from 'i18next';
import { fromJS, List, Map } from 'immutable';
import * as actions from './action';

const initialState = fromJS({
  message: '',
  loading: {},
  programList: [],
  extraCurricularAndBreakTiming: [],
  remoteRooms: [],
  courseCoordinators: {},
  courseList: {},
  activeCourse: {},
  activeScheduleView: 'list',
  courseSchedule: {},
  activeSessionFlow: {},
  scheduleAvailability: {},
  paginationMetaData: {
    totalPages: null,
    currentPage: null,
  },
  activeTab: 'Schedule',
  timeTableData: {},
  courseScheduleExport: {},
  individualCourseDetails: {},
  individualSessionDetails: [],
  mcActiveTab: 'Session Default Settings',
  courseGroupSettingDetails: {},
  advancedSettings: {},
  manageTopics: [],
  activeSettingsView: 'list',
  mergeSchedule: {},
  autoAssignCourseDeliveryList: [],
  studentGroupsWithStudents: [],
  paginationCourseMetaData: {
    totalPages: null,
    currentPage: null,
  },
  mainView: 'regular',
  staffScheduleOptionList: {},
  staffOptionList: [],
  externalStaff: [],
  classLeader: [],
  courseSessionStatusManagement: {},
  studentBasedDeliveryTypes: {},
  postClassLeader: [],
  scheduledExternalStaff: [],
  mergeScheduledClassLeader: [],
  levelWiseCourses: [],
  courseTopics: [],
  availableList: {},
  courseDeliveryTypes: [],
  scheduleAnalysis: {},
  eventList: {},
});

//eslint-disable-next-line
export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }

    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_EXTRA_CURRICULAR_LIST_REQUEST: {
      return state.setIn(['loading', 'GET_EXTRA_CURRICULAR_LIST'], true);
    }
    case actions.GET_EXTRA_CURRICULAR_LIST_SUCCESS: {
      return state
        .setIn(['loading', 'GET_EXTRA_CURRICULAR_LIST'], false)
        .set('extraCurricularAndBreakTiming', fromJS(action.data !== undefined ? action.data : []));
    }
    case actions.GET_EXTRA_CURRICULAR_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_EXTRA_CURRICULAR_LIST'], false)
        .set('message', errorMessage);
    }
    case actions.GET_REMOTE_ROOMS_REQUEST: {
      return state.setIn(['loading', 'GET_REMOTE_ROOMS'], true);
    }
    case actions.GET_REMOTE_ROOMS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_REMOTE_ROOMS'], false)
        .set('remoteRooms', fromJS(action.data));
    }
    case actions.GET_REMOTE_ROOMS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_REMOTE_ROOMS'], false).set('message', errorMessage);
    }
    case actions.SAVE_REMOTE_ROOM_REQUEST: {
      return state.setIn(['loading', 'SAVE_REMOTE_ROOM'], true);
    }
    case actions.SAVE_REMOTE_ROOM_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_REMOTE_ROOM'], false)
        .set(
          'message',
          action.operation === 'Added'
            ? t('infra_management.remote.response_strings.room_created')
            : action.operation === 'Edited'
            ? t('infra_management.remote.response_strings.room_updated')
            : t('infra_management.remote.response_strings.room_deleted')
        );
    }
    case actions.SAVE_REMOTE_ROOM_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'SAVE_REMOTE_ROOM'], false).set('message', errorMessage);
    }
    case actions.GET_COURSE_COORDINATORS_REQUEST: {
      return state.setIn(['loading', 'GET_COURSE_COORDINATORS'], true);
    }
    case actions.GET_COURSE_COORDINATORS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_COORDINATORS'], false)
        .set('courseCoordinators', fromJS(action.data));
    }
    case actions.GET_COURSE_COORDINATORS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'GET_COURSE_COORDINATORS'], false)
        .set('message', errorMessage);
    }
    case actions.ASSIGN_COURSE_COORDINATOR_REQUEST: {
      return state.setIn(['loading', 'ASSIGN_COURSE_COORDINATOR'], true);
    }
    case actions.ASSIGN_COURSE_COORDINATOR_SUCCESS: {
      let courseCoordinators = state.get('courseCoordinators');
      const courseIndex = courseCoordinators
        .get('courses', List())
        .findIndex(
          (course) =>
            course.get('_course_id') === action.data.get('_course_id') &&
            course.get('term') === action.data.getIn(['coordinator', 'term'])
        );
      if (courseIndex !== -1) {
        courseCoordinators = courseCoordinators.setIn(
          ['courses', courseIndex, 'coordinators'],
          action.data.get('coordinator')
        );
      }

      const message =
        action.data.getIn(['coordinator', '_user_id'], '') !== '' &&
        action.data.getIn(['coordinator', '_user_id'], '') !== undefined
          ? 'Staff Assigned Successfully'
          : 'Staff Removed Successfully';
      return state
        .setIn(['loading', 'ASSIGN_COURSE_COORDINATOR'], false)
        .set('courseCoordinators', courseCoordinators)
        .set('message', message);
    }
    case actions.ASSIGN_COURSE_COORDINATOR_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'ASSIGN_COURSE_COORDINATOR'], false)
        .set('message', errorMessage);
    }
    case actions.SAVE_EXTRA_CURRICULAR_REQUEST: {
      return state.setIn(['loading', 'SAVE_EXTRA_CURRICULAR'], true);
    }
    case actions.SAVE_EXTRA_CURRICULAR_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_EXTRA_CURRICULAR'], false)
        .set(
          'message',
          `${action.data === 'extra_curricular' ? 'Extra Curricular' : 'Break'} ${
            action.operation
          } Successfully`
        );
    }
    case actions.SAVE_EXTRA_CURRICULAR_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'SAVE_EXTRA_CURRICULAR'], false).set('message', errorMessage);
    }
    case actions.GET_COURSE_LIST_REQUEST: {
      return state.setIn(['loading', 'GET_COURSE_LIST'], true);
    }
    case actions.GET_COURSE_LIST_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_LIST'], false)
        .set('courseList', fromJS(action.data));
    }
    case actions.GET_COURSE_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_COURSE_LIST'], false).set('message', errorMessage);
    }
    case actions.GET_COURSE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], true);
      // .set('activeTab', 'Manage Course')
      // .set('mcActiveTab', 'Session Default Settings');
    }
    case actions.GET_COURSE_SCHEDULE_SUCCESS: {
      const { data, totalPages = null, currentPage = null, isRefresh } = action.data;
      const newCourseSchedule = fromJS(data);
      const newSessionFlow = newCourseSchedule.get('session_flow', List());
      let updatedState = state.setIn(['loading', 'GET_COURSE_SCHEDULE'], false);

      if (isRefresh) {
        let updatedSessionFlow = state.getIn(['courseSchedule', 'session_flow'], List());
        let index = currentPage === 1 ? 0 : (currentPage - 1) * 10;
        newSessionFlow.forEach((sf) => {
          updatedSessionFlow = updatedSessionFlow.set(index, sf);
          index = index + 1;
        });

        const updatedCourseState = newCourseSchedule.update((key) => {
          return key.set('session_flow', updatedSessionFlow);
        });
        updatedState = updatedState
          .set('courseSchedule', updatedCourseState)
          .mergeIn(
            ['paginationMetaData'],
            Map({ totalPages, currentPage: totalPages === 0 ? 0 : currentPage })
          );

        // updatedState = updatedState
        //   .setIn(['courseSchedule', 'session_flow'], updatedSessionFlow)
        //   .setIn(['courseSchedule', 'topics'], newTopics)
        //   .setIn(['courseSchedule', 'assigned_topics'], newAssignedTopic)
        //   .setIn(['courseSchedule', 'extra_curricular_break_timing'], extraCurricularAndBreaks)
        //   .mergeIn(
        //     ['paginationMetaData'],
        //     Map({ totalPages, currentPage: totalPages === 0 ? 0 : currentPage })
        //   );
      } else {
        // const sessionFlow = state
        //   .getIn(['courseSchedule', 'session_flow'], List())
        //   .concat(newSessionFlow);
        const sessionFlow = newSessionFlow;
        updatedState = updatedState
          .set('courseSchedule', newCourseSchedule.set('session_flow', sessionFlow))
          .mergeIn(['paginationMetaData'], Map({ totalPages, currentPage }));
      }
      return updatedState;
    }
    case actions.GET_COURSE_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], false).set('message', errorMessage);
    }
    case actions.SAVE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'SAVE_SCHEDULE'], true);
    }
    case actions.SAVE_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_SCHEDULE'], false)
        .set('message', `Schedule ${action.operation} Successfully`);
    }
    case actions.SAVE_SCHEDULE_FAILURE: {
      return state.setIn(['loading', 'SAVE_SCHEDULE'], false);
    }
    case actions.GET_SCHEDULE_AVAILABILITY_REQUEST: {
      return state.setIn(['loading', 'GET_SCHEDULE_AVAILABILITY'], true);
    }
    case actions.GET_SCHEDULE_AVAILABILITY_SUCCESS: {
      return state
        .setIn(['loading', 'GET_SCHEDULE_AVAILABILITY'], false)
        .set('scheduleAvailability', fromJS(action.data));
    }
    case actions.GET_SCHEDULE_AVAILABILITY_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : 'An error occurred while fetching the availability. Please try again.';
      return state
        .setIn(['loading', 'GET_SCHEDULE_AVAILABILITY'], false)
        .set('message', errorMessage);
    }
    case actions.GET_MERGE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'GET_MERGE_SCHEDULE'], true);
    }
    case actions.GET_MERGE_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'GET_MERGE_SCHEDULE'], false)
        .set('mergeSchedule', fromJS(action.data));
    }
    case actions.GET_MERGE_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_MERGE_SCHEDULE'], false).set('message', errorMessage);
    }
    case actions.SAVE_MERGE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'SAVE_MERGE_SCHEDULE'], true);
    }
    case actions.SAVE_MERGE_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_MERGE_SCHEDULE'], false)
        .set('message', `Schedule ${action.operation} Successfully`);
    }
    case actions.SAVE_MERGE_SCHEDULE_FAILURE: {
      return state.setIn(['loading', 'SAVE_MERGE_SCHEDULE'], false);
    }
    case actions.SAVE_SUPPORT_AND_EVENTS_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'SAVE_SUPPORT_AND_EVENTS_SCHEDULE'], true);
    }
    case actions.SAVE_SUPPORT_AND_EVENTS_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_SUPPORT_AND_EVENTS_SCHEDULE'], false)
        .set(
          'message',
          `${action.viewType === 'event' ? 'Event' : 'Support Session'} ${
            action.operation
          } Successfully`
        );
    }
    case actions.SAVE_SUPPORT_AND_EVENTS_SCHEDULE_FAILURE: {
      return state.setIn(['loading', 'SAVE_SUPPORT_AND_EVENTS_SCHEDULE'], false);
    }
    case actions.GET_TIME_TABLE_DATA_REQUEST: {
      return state.setIn(['loading', 'GET_TIME_TABLE_DATA'], true).set('timeTableData', Map());
    }
    case actions.GET_TIME_TABLE_DATA_SUCCESS: {
      return state
        .setIn(['loading', 'GET_TIME_TABLE_DATA'], false)
        .set('timeTableData', fromJS(action.data !== undefined ? action.data : []));
    }
    case actions.GET_TIME_TABLE_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_TIME_TABLE_DATA'], false).set('message', errorMessage);
    }
    case actions.GET_COURSE_SCHEDULE_EXPORT_REQUEST: {
      return state
        .setIn(['loading', 'GET_COURSE_SCHEDULE_EXPORT'], true)
        .set('courseScheduleExport', Map());
    }
    case actions.GET_COURSE_SCHEDULE_EXPORT_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_SCHEDULE_EXPORT'], false)
        .set('courseScheduleExport', fromJS(action.data));
    }
    case actions.GET_COURSE_SCHEDULE_EXPORT_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'GET_COURSE_SCHEDULE_EXPORT'], false)
        .set('message', errorMessage);
    }
    case actions.PUBLISH_COURSE_COORDINATOR_REQUEST: {
      return state.setIn(['loading', 'PUBLISH_COURSE_COORDINATOR'], true);
    }
    case actions.PUBLISH_COURSE_COORDINATOR_SUCCESS: {
      return state
        .setIn(['loading', 'PUBLISH_COURSE_COORDINATOR'], false)
        .set('message', `Published Successfully`);
    }
    case actions.PUBLISH_COURSE_COORDINATOR_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : 'An error occurred during publish. Please try again.';
      return state
        .setIn(['loading', 'PUBLISH_COURSE_COORDINATOR'], false)
        .set('message', errorMessage);
    }
    case actions.PUBLISH_COURSE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'PUBLISH_COURSE_SCHEDULE'], true);
    }
    case actions.PUBLISH_COURSE_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'PUBLISH_COURSE_SCHEDULE'], false)
        .set('message', `Published Successfully`);
    }
    case actions.PUBLISH_COURSE_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : 'An error occurred during publish. Please try again.';
      return state
        .setIn(['loading', 'PUBLISH_COURSE_SCHEDULE'], false)
        .set('message', errorMessage);
    }

    case actions.GET_INDIVIDUAL_COURSE_REQUEST: {
      return state
        .setIn(['loading', 'GET_INDIVIDUAL_COURSE'], true)
        .set('individualCourseDetails', Map())
        .set('individualSessionDetails', List());
    }
    case actions.GET_INDIVIDUAL_COURSE_SUCCESS: {
      return state
        .setIn(['loading', 'GET_INDIVIDUAL_COURSE'], false)
        .set('individualCourseDetails', fromJS(action.data.course_details))
        .set('individualSessionDetails', fromJS(action.data.session_delivery))
        .setIn(['courseSchedule', 'session_flow'], List());
    }
    case actions.GET_INDIVIDUAL_COURSE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_INDIVIDUAL_COURSE'], false).set('message', errorMessage);
    }
    case actions.GET_MANAGE_COURSE_SETTINGS_REQUEST: {
      return state
        .setIn(['loading', 'GET_MANAGE_COURSE_SETTINGS'], true)
        .set('courseGroupSettingDetails', Map());
    }
    case actions.GET_MANAGE_COURSE_SETTINGS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_MANAGE_COURSE_SETTINGS'], false)
        .set('courseGroupSettingDetails', fromJS(action.data));
    }
    case actions.GET_MANAGE_COURSE_SETTINGS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'GET_MANAGE_COURSE_SETTINGS'], false)
        .set('message', errorMessage);
    }
    case actions.SAVE_MANAGE_COURSE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'SAVE_MANAGE_COURSE_SCHEDULE'], true);
    }
    case actions.SAVE_MANAGE_COURSE_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_MANAGE_COURSE_SCHEDULE'], false)
        .set('message', `Session Default Settings ${action.operation} Successfully`)
        .set('courseGroupSettingDetails', Map());
    }
    case actions.SAVE_MANAGE_COURSE_SCHEDULE_FAILURE: {
      return state.setIn(['loading', 'SAVE_MANAGE_COURSE_SCHEDULE'], false);
    }
    case actions.SAVE_ADVANCE_SETTING_REQUEST: {
      return state.setIn(['loading', 'SAVE_ADVANCE_SETTING'], true);
    }
    case actions.SAVE_ADVANCE_SETTING_SUCCESS: {
      if (action.data !== '') {
        return state.setIn(['loading', 'SAVE_ADVANCE_SETTING'], false).set('message', action.data);
      } else {
        return state.setIn(['loading', 'SAVE_ADVANCE_SETTING'], false);
      }
    }
    case actions.SAVE_ADVANCE_SETTING_FAILURE: {
      return state.setIn(['loading', 'SAVE_ADVANCE_SETTING'], false);
    }
    case actions.GET_ADVANCED_SETTINGS_REQUEST: {
      return state.setIn(['loading', 'GET_ADVANCED_SETTINGS'], true);
    }
    case actions.GET_ADVANCED_SETTINGS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ADVANCED_SETTINGS'], false)
        .set('advancedSettings', fromJS(action.data !== undefined ? action.data : []));
    }
    case actions.GET_ADVANCED_SETTINGS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_ADVANCED_SETTINGS'], false).set('message', errorMessage);
    }
    case actions.GET_MANAGE_TOPICS_REQUEST: {
      return state.setIn(['loading', 'GET_MANAGE_TOPICS'], true).set('manageTopics', List());
    }
    case actions.GET_MANAGE_TOPICS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_MANAGE_TOPICS'], false)
        .set('manageTopics', fromJS(action.data !== undefined ? action.data : []));
    }
    case actions.GET_MANAGE_TOPICS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'GET_MANAGE_TOPICS'], false).set('message', errorMessage);
    }
    case actions.SAVE_MANAGE_TOPICS_REQUEST: {
      return state.setIn(['loading', 'SAVE_MANAGE_TOPICS'], true);
    }
    case actions.SAVE_MANAGE_TOPICS_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_MANAGE_TOPICS'], false)
        .set('message', `Topics ${action.operation} Successfully`);
    }
    case actions.SAVE_MANAGE_TOPICS_FAILURE: {
      return state.setIn(['loading', 'SAVE_MANAGE_TOPICS'], false);
    }

    case actions.SAVE_MANAGE_COURSE_DAY_TIME_REQUEST: {
      return state.setIn(['loading', 'SAVE_MANAGE_COURSE_DAY_TIME'], true);
    }
    case actions.SAVE_MANAGE_COURSE_DAY_TIME_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_MANAGE_COURSE_DAY_TIME'], false)
        .set('message', `Schedule ${action.operation} Successfully`);
      // .set('courseGroupSettingDetails', Map());
    }
    case actions.SAVE_MANAGE_COURSE_DAY_TIME_FAILURE: {
      return state.setIn(['loading', 'SAVE_MANAGE_COURSE_DAY_TIME'], false);
    }

    case actions.CANCEL_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'CANCEL_SCHEDULE'], true);
    }
    case actions.CANCEL_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'CANCEL_SCHEDULE'], false)
        .set('message', 'Schedule Canceled Successfully');
    }
    case actions.CANCEL_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'CANCEL_SCHEDULE'], false).set('message', errorMessage);
    }
    case actions.GET_AUTO_ASSIGN_COURSE_DELIVERY_REQUEST: {
      return state.setIn(['loading', 'GET_AUTO_ASSIGN_COURSE_DELIVERY'], true);
    }
    case actions.GET_AUTO_ASSIGN_COURSE_DELIVERY_SUCCESS: {
      const { data, totalPages = null, currentPage = null, isRefresh } = action.data;
      const newData = fromJS(data);
      let updatedState = state.setIn(['loading', 'GET_AUTO_ASSIGN_COURSE_DELIVERY'], false);
      if (isRefresh) {
        const dataList = newData;
        updatedState = updatedState
          .set('autoAssignCourseDeliveryList', dataList)
          .mergeIn(
            ['paginationCourseMetaData'],
            Map({ totalPages, currentPage: totalPages === 0 ? 0 : currentPage })
          );
      } else {
        const dataList = state.get('autoAssignCourseDeliveryList', List()).concat(newData);
        updatedState = updatedState
          .set('autoAssignCourseDeliveryList', dataList)
          .mergeIn(['paginationCourseMetaData'], Map({ totalPages, currentPage }));
      }
      return updatedState;
    }
    case actions.GET_AUTO_ASSIGN_COURSE_DELIVERY_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'GET_AUTO_ASSIGN_COURSE_DELIVERY'], false)
        .set('message', errorMessage);
    }
    case actions.SAVE_ASSIGN_COURSE_DELIVERY_REQUEST: {
      return state.setIn(['loading', 'SAVE_ASSIGN_COURSE_DELIVERY'], true);
    }
    case actions.SAVE_ASSIGN_COURSE_DELIVERY_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_ASSIGN_COURSE_DELIVERY'], false)
        .set('message', `Auto Scheduling Completed Successfully`); //Assign Course Settings ${action.operation} Successfully
    }
    case actions.SAVE_ASSIGN_COURSE_DELIVERY_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'SAVE_ASSIGN_COURSE_DELIVERY'], false)
        .set('message', errorMessage);
    }
    case actions.GET_STUDENT_GROUPS_WITH_STUDENTS_REQUEST: {
      return state
        .setIn(['loading', 'GET_STUDENT_GROUPS_WITH_STUDENTS'], true)
        .set('studentGroupsWithStudents', List());
    }
    case actions.GET_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_STUDENT_GROUPS_WITH_STUDENTS'], false)
        .set('studentGroupsWithStudents', fromJS(action.data));
    }
    case actions.GET_STUDENT_GROUPS_WITH_STUDENTS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'GET_STUDENT_GROUPS_WITH_STUDENTS'], false)
        .set('message', errorMessage);
    }

    case actions.SAVE_STUDENT_GROUPS_WITH_STUDENTS_REQUEST: {
      return state.setIn(['loading', 'SAVE_STUDENT_GROUPS_WITH_STUDENTS'], true);
    }
    case actions.SAVE_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS: {
      return state.setIn(['loading', 'SAVE_STUDENT_GROUPS_WITH_STUDENTS'], false);
    }
    case actions.SAVE_STUDENT_GROUPS_WITH_STUDENTS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state
        .setIn(['loading', 'SAVE_STUDENT_GROUPS_WITH_STUDENTS'], false)
        .set('message', errorMessage);
    }
    case actions.GET_PROGRAM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('programList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_STAFF_OPTION_LIST_REQUEST: {
      return state.set('isLoading', true).set('staffOptionList', fromJS([]));
    }
    case actions.GET_STAFF_OPTION_LIST_SUCCESS: {
      return state.set('isLoading', false).set('staffOptionList', fromJS(action.data));
    }
    case actions.GET_STAFF_OPTION_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage = message && typeof message === 'string' ? message : '';
      return state
        .set('isLoading', false)
        .set(
          'message',
          errorMessage === 'MANUAL_ATTENDANCE_ROLE_NOT_FOUND' &&
            'Secondary Role has not been assigned to academic staff'
        );
    }
    case actions.SET_STAFF_LIST_REQUEST: {
      return state.setIn(['loading', 'SET_STAFF_LIST'], true);
    }
    case actions.SET_STAFF_LIST_SUCCESS: {
      return state.setIn(['loading', 'SET_STAFF_LIST'], false).set('message', action.data.message); //Assign Course Settings ${action.operation} Successfully
    }
    case actions.SET_STAFF_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage = message && typeof message === 'string' ? message : '';
      return state.setIn(['loading', 'SET_STAFF_LIST'], false).set('message', errorMessage);
    }
    case actions.GET_SCHEDULE_STAFF_OPTION_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SCHEDULE_STAFF_OPTION_LIST_SUCCESS: {
      return state.set('isLoading', false).set('staffScheduleOptionList', fromJS(action.data));
    }
    case actions.GET_SCHEDULE_STAFF_OPTION_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.SAVE_MISSED_SESSION_REQUEST: {
      return state.setIn(['loading', 'SAVE_MISSED_SESSION'], true);
    }
    case actions.SAVE_MISSED_SESSION_SUCCESS: {
      return state
        .setIn(['loading', 'SAVE_MISSED_SESSION'], false)
        .set('message', 'Saved Successfully');
    }
    case actions.SAVE_MISSED_SESSION_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'SAVE_MISSED_SESSION'], false).set('message', errorMessage);
    }

    case actions.UPDATE_MISSED_SESSION_REQUEST: {
      return state.setIn(['loading', 'UPDATE_MISSED_SESSION'], true);
    }
    case actions.UPDATE_MISSED_SESSION_SUCCESS: {
      return state
        .setIn(['loading', 'UPDATE_MISSED_SESSION'], false)
        .set('message', 'Saved Successfully');
    }
    case actions.UPDATE_MISSED_SESSION_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.setIn(['loading', 'UPDATE_MISSED_SESSION'], false).set('message', errorMessage);
    }

    case actions.SYNC_SCHEDULE_MODAL_DATA_REQUEST: {
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], true);
    }
    case actions.SYNC_SCHEDULE_MODAL_DATA_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_SCHEDULE'], false)
        .set('message', 'Data Synced Successfully');
    }
    case actions.SYNC_SCHEDULE_MODAL_DATA_FAILURE: {
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], false).set('message', 'Error in Syn');
    }
    case actions.POST_Add_EXTERNAL_Staff_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_Add_EXTERNAL_Staff_SUCCESS: {
      let newState = state.set('isLoading', false).set('message', 'Updated Successfully');
      return newState;
    }
    case actions.POST_Add_EXTERNAL_Staff_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage).delete(action.index);
    }
    case actions.GET_EXTERNAL_STAFF_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_EXTERNAL_STAFF_SUCCESS: {
      return state.set('isLoading', false).set('externalStaff', fromJS(action.data));
    }
    case actions.GET_EXTERNAL_STAFF_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state.set('isLoading', false).set('message', errorMessage);
    }

    // case actions.GET_CLASSLEADER_REQUEST: {
    //   return state.set('isLoading', true);
    // }
    // case actions.GET_CLASSLEADER_SUCCESS: {
    //   return state.set('isLoading', false).set('classLeader', fromJS(action.data));
    // }
    // case actions.GET_CLASSLEADER_FAILURE: {
    //   const errorMessage = getErrorMessage(action);
    //   return state.set('isLoading', false).set('message', errorMessage);
    // }

    case actions.POST_CLASSLEADER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_CLASSLEADER_SUCCESS: {
      return state.set('isLoading', false).set('postClassLeader', fromJS(action.data));
    }
    case actions.POST_CLASSLEADER_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.GET_SCHEDULED_EXTERNAL_STAFF_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SCHEDULED_EXTERNAL_STAFF_SUCCESS: {
      return state.set('isLoading', false).set('scheduledExternalStaff', fromJS(action.data));
    }
    case actions.GET_SCHEDULED_EXTERNAL_STAFF_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.GET_MERGE_SCHEDULED_CLASSLEADER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_MERGE_SCHEDULED_CLASSLEADER_SUCCESS: {
      return state.set('isLoading', false).set('mergeScheduledClassLeader', fromJS(action.data));
    }
    case actions.GET_MERGE_SCHEDULED_CLASSLEADER_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.GET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST: {
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], true);
    }
    case actions.GET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_SCHEDULE'], false)
        .set('courseSessionStatusManagement', fromJS(action.data));
    }
    case actions.GET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE: {
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], false).set('error', action.error);
    }
    case actions.SET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST: {
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], true);
    }
    case actions.SET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_SCHEDULE'], false)
        .set('message', 'Updated SuccessFully');
    }
    case actions.SET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE: {
      return state.setIn(['loading', 'GET_COURSE_SCHEDULE'], false).set('error', action.error);
    }

    case actions.GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_SUCCESS: {
      return state.set('isLoading', false).set('studentBasedDeliveryTypes', fromJS(action.data));
    }
    case actions.GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_LEVEL_WISE_COURSES_REQUEST: {
      return state.setIn(['loading', 'GET_LEVEL_WISE_COURSES'], true);
    }
    case actions.GET_LEVEL_WISE_COURSES_SUCCESS: {
      return state
        .setIn(['loading', 'GET_LEVEL_WISE_COURSES'], false)
        .set('levelWiseCourses', fromJS(action.data));
    }
    case actions.GET_LEVEL_WISE_COURSES_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_LEVEL_WISE_COURSES'], false).set('message', errorMessage);
    }
    case actions.GET_COURSE_TOPICS_REQUEST: {
      return state.setIn(['loading', 'GET_COURSE_TOPICS'], true).set('courseTopics', List());
    }
    case actions.GET_COURSE_TOPICS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_TOPICS'], false)
        .set('courseTopics', fromJS(action.data));
    }
    case actions.GET_COURSE_TOPICS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_COURSE_TOPICS'], false).set('message', errorMessage);
    }
    case actions.GET_AVAILABLE_LIST_REQUEST: {
      return state.setIn(['loading', 'GET_AVAILABLE_LIST'], true).set('availableList', Map());
    }
    case actions.GET_AVAILABLE_LIST_SUCCESS: {
      return state
        .setIn(['loading', 'GET_AVAILABLE_LIST'], false)
        .set('availableList', fromJS(action.data));
    }
    case actions.GET_AVAILABLE_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_AVAILABLE_LIST'], false).set('message', errorMessage);
    }
    case actions.GET_COURSE_DELIVERY_TYPES_REQUEST: {
      return state
        .setIn(['loading', 'GET_COURSE_DELIVERY_TYPES'], true)
        .set('courseDeliveryTypes', List());
    }
    case actions.GET_COURSE_DELIVERY_TYPES_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_DELIVERY_TYPES'], false)
        .set('courseDeliveryTypes', fromJS(action.data));
    }
    case actions.GET_COURSE_DELIVERY_TYPES_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_COURSE_DELIVERY_TYPES'], false)
        .set('message', errorMessage);
    }
    case actions.POST_SCHEDULE_ANALYZE_REQUEST: {
      return state.setIn(['loading', 'POST_SCHEDULE_ANALYZE'], true).set('scheduleAnalysis', Map());
    }
    case actions.POST_SCHEDULE_ANALYZE_SUCCESS: {
      return state
        .setIn(['loading', 'POST_SCHEDULE_ANALYZE'], false)
        .set('scheduleAnalysis', fromJS(action.data));
    }
    case actions.POST_SCHEDULE_ANALYZE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'POST_SCHEDULE_ANALYZE'], false).set('message', errorMessage);
    }
    case actions.POST_MULTI_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'POST_MULTI_SCHEDULE'], true);
    }
    case actions.POST_MULTI_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'POST_MULTI_SCHEDULE'], false)
        .set('message', `${action.data} Topics - Multi-scheduled successfully!`);
    }
    case actions.POST_MULTI_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'POST_MULTI_SCHEDULE'], false).set('message', errorMessage);
    }
    case actions.GET_EVENT_LIST_REQUEST: {
      return state.setIn(['loading', 'GET_EVENT_LIST'], true);
    }
    case actions.GET_EVENT_LIST_SUCCESS: {
      let { data, withSchedule } = action.data;
      const key = withSchedule ? 'eventsAndSchedules' : 'events';
      // if (!withSchedule) data = data.eventList;
      return state
        .setIn(['loading', 'GET_EVENT_LIST'], false)
        .setIn(['eventList', key], fromJS(data));
    }
    case actions.GET_EVENT_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message === 'No data found!'
            ? ''
            : message
          : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_EVENT_LIST'], false).set('message', errorMessage);
    }
    case actions.POST_SINGLE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'POST_SINGLE_SCHEDULE'], true);
    }
    case actions.POST_SINGLE_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'POST_SINGLE_SCHEDULE'], false)
        .set('message', `${action.data} - Scheduled successfully!`);
    }
    case actions.POST_SINGLE_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'POST_SINGLE_SCHEDULE'], false).set('message', errorMessage);
    }
    case actions.DELETE_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'DELETE_SCHEDULE'], true);
    }
    case actions.DELETE_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'DELETE_SCHEDULE'], false)
        .set('message', `${action.data} - Deleted successfully!`);
    }
    case actions.DELETE_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'DELETE_SCHEDULE'], false).set('message', errorMessage);
    }
    case actions.EDIT_SCHEDULE_REQUEST: {
      return state.setIn(['loading', 'EDIT_SCHEDULE'], true);
    }
    case actions.EDIT_SCHEDULE_SUCCESS: {
      return state
        .setIn(['loading', 'EDIT_SCHEDULE'], false)
        .set('message', `${action.data} - Edited successfully!`);
    }
    case actions.EDIT_SCHEDULE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'EDIT_SCHEDULE'], false).set('message', errorMessage);
    }
    default:
      return state;
  }
}
function getErrorMessage(action) {
  const { response: { data: { message = '' } = {} } = {} } = action.error;
  return message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
}
