import React from "react";
import PropTypes from "prop-types";
import {
  DialogActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  Button,
  TextField,
} from "@mui/material";
import { dialogSX } from "./ModelsUtils";

const CreateNewCalendar = ({ open, handleClose }) => {
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="xs"
      fullWidth
      PaperProps={{ sx: dialogSX }}
    >
      <DialogTitle className="responsiveFontSizeMedium">
        Create New Calendar
      </DialogTitle>
      <DialogContent className="course-qlc-scrollbar mx-2 my-1 px-3 pb-4">
        <DialogContentText className="d-flex flex-column gap-3">
          <div className="d-flex flex-column gap-1">
            <label className="f-14">Calendar Name</label>
            <TextField size="small" />
          </div>
          <div className="d-flex gap-2">
            <div className="d-flex flex-column gap-1">
              <label className="f-13">Start Month</label>
              <TextField size="small" />
            </div>
            <div className="d-flex flex-column gap-1">
              <label className="f-13">End Month</label>
              <TextField size="small" />
            </div>
          </div>
        </DialogContentText>
      </DialogContent>
      <DialogActions className="px-4 pb-4 gap-2">
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall text-secondary border-secondary bold"
          variant="outlined"
          onClick={handleClose}
        >
          Cancel
        </Button>
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall bg-primary"
          variant="contained"
        >
          Create
        </Button>
      </DialogActions>
    </Dialog>
  );
};

CreateNewCalendar.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
};

export default CreateNewCalendar;
