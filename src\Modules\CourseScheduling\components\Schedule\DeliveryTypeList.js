import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import success from '../../../../Assets/alert2.png';
import { getFormattedGroupName } from '../utils';
import { capitalize, getURLParams, isIndGroup, studentGroupRename } from '../../../../utils';

function DeliveryTypeList({
  course,
  fetchCourseSchedule,
  paginationMetaData,
  isLoading,
  activeSessionFlow,
  setData,
  triggerWeek,
  activeViewType,
  activeRotation,
  setActiveRotation,
  getWeeksArray,
}) {
  const programId = getURLParams('programId', true);

  const [infiniteRef] = useInfiniteScroll({
    loading: isLoading,
    hasNextPage: checkIfHasNextPage(),
    onLoadMore: () => {
      const { currentPage, totalPages } = getPaginationMetaData();
      if (currentPage === null) return;
      if (currentPage === totalPages) return;
      fetchCourseSchedule({ page: paginationMetaData.get('currentPage') + 1 });
    },
  });

  function checkIfHasNextPage() {
    const { currentPage, totalPages } = getPaginationMetaData();
    if (currentPage === null) return false;
    return currentPage !== totalPages;
  }

  function getPaginationMetaData() {
    return {
      currentPage: paginationMetaData.get('currentPage'),
      totalPages: paginationMetaData.get('totalPages'),
    };
  }

  function overAllSessionStatus(session) {
    if (activeViewType !== 'regular') {
      return session.get('schedule_date') ? true : false;
    }
    const studentGroups = session.get('student_group', List());
    if (
      studentGroups.size > 0 &&
      studentGroups.size ===
        studentGroups.filter((item) => item.get('schedule_status', false) === true).size
    ) {
      return true;
    }
    return false;
  }

  function getSessionTitle(session) {
    if (activeViewType === 'regular') {
      return `${session.get('delivery_symbol', '')}${session.get('delivery_no', '')} ${session.get(
        'delivery_topic',
        ''
      )}`;
    }
    return `${session.get('title', 'N/A')} ${
      activeViewType === 'events' ? '/ ' + capitalize(session.get('sub_type', 'N/A')) : ''
    }`;
  }

  function getRotationHtml(sessionFlow) {
    return course.get('rotation_dates', List()).map((rotation, rotationIndex) => {
      return (
        <div
          className={`course_sidebar_inner remove_hover ${
            activeRotation.get('rotation_count', '') === rotation.get('rotation_count')
              ? 'course_active'
              : ''
          }`}
          key={rotationIndex}
          onClick={() => {
            setActiveRotation(rotation);
            getWeeksArray(rotation.get('start_date', ''), rotation.get('end_date', ''));
          }}
        >
          <div className="pl-4 ">R{rotation.get('rotation_count')}</div>
          {getStudentGroups(
            sessionFlow
              .get('schedule', List())
              .filter((item) => item.get('rotation_count', 0) === rotation.get('rotation_count'))
          )}
        </div>
      );
    });
  }

  function getStudentGroups(schedule) {
    return schedule.map((schedule, index) => (
      <div className="course_sidebar_inner" key={index}>
        <div className="pl-4 ">
          {'  '}
          {course.get('rotation', 'no') === 'yes' && (
            <span className="pr-2`">
              <i className="fa fa-level-up fa-rotate-90 f-12 mr-2" aria-hidden="true"></i>
            </span>
          )}
          {`${schedule
            .get('student_groups', List())
            .map((studentGroup) => {
              const studentGroupName = getFormattedGroupName(
                studentGroupRename(studentGroup.get('group_name', ''), programId),
                isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
              );
              const sessionGroupNames = studentGroup
                .get('session_group', List())
                .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
                .join(', ');
              return `${studentGroupName} - ${sessionGroupNames}`;
            })
            .join(', ')}`}
        </div>
      </div>
    ));
  }

  return (
    <div className="max_hs p-1 roles_height">
      {course.get('session_flow', List()).isEmpty() ? (
        <div className="mt-3 mb-3 text-center">
          {`No ${activeViewType === 'regular' ? 'session flow' : 'data'} found`}
        </div>
      ) : (
        course.get('session_flow', List()).map((sessionFlow) => (
          <React.Fragment key={sessionFlow.get('_id')}>
            <div
              className={`course_sidebar remove_hover ${
                activeSessionFlow.get('_id', '') === sessionFlow.get('_id', '')
                  ? 'course_active'
                  : ''
              }`}
              onClick={() => {
                triggerWeek(sessionFlow.get('week', 1));
                setData(Map({ activeSessionFlow: sessionFlow }));
                if (sessionFlow.get('type', 'regular') === 'regular') {
                  setActiveRotation(Map());
                } else {
                  if (course.get('rotation', 'yes') === 'yes') {
                    const currentRotation = course
                      .get('rotation_dates', List())
                      .find(
                        (item) => item.get('rotation_count') === sessionFlow.get('rotation_count')
                      );
                    setActiveRotation(currentRotation);
                    getWeeksArray(
                      currentRotation.get('start_date', ''),
                      currentRotation.get('end_date', '')
                    );
                  }
                }
              }}
            >
              <span>
                {overAllSessionStatus(sessionFlow) ? (
                  <img src={success} alt="" title="" />
                ) : (
                  <i className="fa fa-exclamation-circle text-gray" aria-hidden="true"></i>
                )}
              </span>
              <span className="pl-2">{getSessionTitle(sessionFlow)}</span>
            </div>
            {checkIfHasNextPage() && <div ref={infiniteRef} />}
            {activeViewType === 'regular' &&
              activeSessionFlow.get('_id', '') === sessionFlow.get('_id', '') && (
                <>
                  {course.get('rotation', 'yes') === 'no' &&
                    getStudentGroups(sessionFlow.get('schedule', List()))}
                  {course.get('rotation', 'yes') === 'yes' && getRotationHtml(sessionFlow)}
                </>
              )}
          </React.Fragment>
        ))
      )}
    </div>
  );
}

DeliveryTypeList.propTypes = {
  course: PropTypes.instanceOf(Map),
  fetchCourseSchedule: PropTypes.func,
  setData: PropTypes.func,
  triggerWeek: PropTypes.func,
  getWeeksArray: PropTypes.func,
  paginationMetaData: PropTypes.instanceOf(Map),
  activeSessionFlow: PropTypes.instanceOf(Map),
  activeRotation: PropTypes.instanceOf(Map),
  setActiveRotation: PropTypes.instanceOf(Map),
  isLoading: PropTypes.bool,
  activeViewType: PropTypes.string,
};

export default DeliveryTypeList;
