import { useSelector } from 'react-redux';
import { List, Map, fromJS } from 'immutable';

function useProgramDepartment() {
  const authDataArray = useSelector((state) => state?.auth);
  const authData = fromJS(authDataArray);
  const selectedRole = authData.get('selectedRole', Map());
  const selectedProgram = selectedRole.get('program', List());
  const selectedDepartment = selectedRole.get('department', Map());

  const hasProgramAccess = (programId = '', isDepartmentEmpty = false) => {
    if (programId !== '') {
      const filterProgram = selectedProgram.filter((item) => item.get('_program_id') === programId);
      if (!isDepartmentEmpty) {
        return filterProgram.size > 0;
      } else {
        return filterProgram.size > 0 && selectedDepartment.size === 0;
      }
    }
    return false;
  };
  return { hasProgramAccess };
}

export default useProgramDepartment;
