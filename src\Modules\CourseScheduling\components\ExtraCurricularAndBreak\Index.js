import React, { Suspense, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';
import { Table, DropdownButton, Dropdown } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import Input from '../../../../Widgets/FormElements/Input/Input';
import '../../css/courseSchedule.css';
import BreadcrumbHeader from '../Shared/BreadCrumbHeader';
import * as actions from '../../../../_reduxapi/course_scheduling/action';
import { selectExtraCurricularList } from '../../../../_reduxapi/course_scheduling/selectors';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import {
  jsUcfirst,
  getURLParams,
  formatDays,
  formatTwoString,
  setDate,
  getTranslatedDuration,
} from '../../../../utils';
import { DAYS_LIST } from '../Shared/Constants';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import {
  validateData,
  getTrimmedValues,
  formatDateObject,
  formatDateTime,
  sortDays,
} from './utils';
import useCalendar from 'Hooks/useCalendarHook';

const AddEditExtraCurricularAndTiming = React.lazy(() =>
  import('../../modal/AddEditExtraCurricularAndTiming')
);

const AlertConfirmModal = React.lazy(() => import('../../modal/AlertConfirmModal'));
function ExtraCurricular(props) {
  const backUrl = '/course-scheduling/extra-curricular-break';
  const [showModal, setShowModal] = useState(false);
  const [mode, setMode] = useState('');
  const [type, setType] = useState('');
  const [activeFilter, setActiveFilter] = useState(
    Map({ selectedType: '', selectedGender: '', selectedStatus: '' })
  );
  const [activeData, setActiveData] = useState(Map());
  const [alertConfirmModalData, setAlertConfirmModalData] = useState({ show: false });
  const { getExtraCurricularList, activeInstitutionCalendar } = props;
  const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id', '');

  const { isCurrentCalendar } = useCalendar();
  const currentCalendar = isCurrentCalendar();

  useEffect(() => {
    const programId = getURLParams('programId', true);
    if (activeInstitutionCalendarId !== '') {
      getExtraCurricularList(programId, activeInstitutionCalendarId);
    }
  }, [activeInstitutionCalendarId, getExtraCurricularList]);

  function handleAddEditExtraClick(mode, type, data) {
    let copyData = data;
    if (mode === 'update') {
      copyData = copyData.update((key) => {
        return key
          .set('startTime', formatDateTime(data.get('startTime')))
          .set('endTime', formatDateTime(data.get('endTime')));
      });
    } else {
      copyData = copyData.update((key) => {
        return key.set('startTime', setDate(8)).set('endTime', setDate(17));
      });
    }

    setMode(mode);
    setType(type);
    setActiveData(copyData);
    setShowModal(true);
  }

  function handleCancelClick() {
    setMode('');
    setType('');
    setActiveData(Map());
    setShowModal(false);
  }

  function handleChange(name, value) {
    let val = value;
    if (name === 'days') {
      const daysArray = activeData.get('days', List());
      if (activeData.has('days')) {
        if (daysArray.size > 0) {
          if (daysArray.toJS().includes(value)) {
            let copyArray = [...daysArray.toJS()];
            const filteredArray = copyArray.filter((val) => val !== value);
            val = fromJS(filteredArray);
          } else {
            let copyArray = [...daysArray.toJS()];
            copyArray.push(value);
            val = fromJS(copyArray);
          }
        } else {
          val = fromJS([value]);
        }
      } else {
        val = fromJS([value]);
      }
    }
    if (name === 'mode' && value === 'daily') {
      setActiveData(activeData.set(name, val).set('days', fromJS(DAYS_LIST)));
    } else if (name === 'mode' && val === 'weekly') {
      setActiveData(activeData.set(name, val).set('days', fromJS([])));
    } else {
      setActiveData(activeData.set(name, val));
    }
  }

  function renderEmptyHtml() {
    return (
      <div className="emptyRender m-5">
        <div className="text-center">
          <i className="fa fa-info-circle mr-1"></i>
          <Trans i18nKey={'course_schedule.items_not_created'}></Trans>
          <br />
          <Trans i18nKey={'course_schedule.to_create_click_add_button'}></Trans>
        </div>
      </div>
    );
  }

  function setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    setAlertConfirmModalData({
      show,
      ...(title && { title }),
      ...(titleIcon && { titleIcon }),
      ...(body && { body }),
      ...(variant && { variant }),
      ...(confirmButtonLabel && { confirmButtonLabel }),
      ...(cancelButtonLabel && { cancelButtonLabel }),
      ...(data && { data }),
    });
  }

  function handleDeleteData(data, isConfirmed) {
    if (!isConfirmed) {
      setModalData({
        show: true,
        title: t('course_schedule.delete_extra_curricular_title'),
        body: (
          <div>
            {`${t('course_schedule.delete_extra_curricular_desc')} "${data.get('title', '')}"?`}
          </div>
        ),
        variant: 'confirm',
        data: { data: data, operation: 'delete' },
        confirmButtonLabel: t('delete'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    const programId = getURLParams('programId', true);
    const activeInstitutionCalendarId = props.activeInstitutionCalendar.get('_id', '');
    props.saveExtraCurricularAndTiming({
      mode: 'delete',
      programId,
      _id: data.get('_id'),
      ...(activeInstitutionCalendarId && { instCalId: activeInstitutionCalendarId }),
      type: data.get('type', ''),
      callback: handleCancelClick,
    });
  }

  function handleStatus(data, isConfirmed) {
    if (!isConfirmed) {
      const status = data.get('isActive', false) === true ? 'Disable' : 'Enable';
      setModalData({
        show: true,
        title: t(status.toLowerCase()) + ` ${t('course_schedule.extra_curricular_break')} `,
        body: (
          <div>
            {`Are you sure you want to ${status} the selected data "${data.get('title', '')}"?`}
          </div>
        ),
        variant: 'confirm',
        data: { data: data, operation: 'status' },
        confirmButtonLabel: t(status.toLowerCase()).toUpperCase(),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    const programId = getURLParams('programId', true);
    const activeInstitutionCalendarId = props.activeInstitutionCalendar.get('_id', '');
    const requestBody = {
      activeStatus: !data.get('isActive', false),
    };
    props.saveExtraCurricularAndTiming({
      mode: 'status',
      programId,
      _id: data.get('_id'),
      ...(activeInstitutionCalendarId && { instCalId: activeInstitutionCalendarId }),
      requestBody,
      type: data.get('type', ''),
      callback: handleCancelClick,
    });
  }

  function onModalClose() {
    setAlertConfirmModalData({ show: false });
  }

  function onConfirm({ data, operation }) {
    setAlertConfirmModalData({ show: false });
    switch (operation) {
      case 'delete': {
        return handleDeleteData(data, true);
      }
      case 'status': {
        return handleStatus(data, true);
      }
      default:
        return;
    }
  }

  function tableList() {
    const { extraCurricularAndBreakTiming } = props;
    return (
      <>
        <Table hover>
          <thead className="bg-white thead_border ">
            <tr className="text-uppercase">
              <th>
                <Trans i18nKey={'s_no'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'title'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'type'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'gender'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'day'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'course_schedule.start_time'}></Trans>
              </th>
              <th>
                <Trans i18nKey={'course_schedule.end_time'}></Trans>
              </th>
              <th> </th>
            </tr>
          </thead>
          <tbody>
            {extraCurricularAndBreakTiming.size > 0 &&
              extraCurricularAndBreakTiming
                .filter(
                  (item) =>
                    (activeFilter.get('selectedType', '') !== ''
                      ? item.get('type') === activeFilter.get('selectedType')
                      : item) &&
                    (activeFilter.get('selectedGender', '') !== ''
                      ? item.get('gender') === activeFilter.get('selectedGender')
                      : item) &&
                    (activeFilter.get('selectedStatus', '') !== ''
                      ? item.get('isActive') === (activeFilter.get('selectedStatus') === 'true')
                        ? true
                        : false
                      : item)
                )
                // eslint-disable-next-line array-callback-return
                .filter((item) => {
                  if (
                    CheckPermission(
                      'tabs',
                      'Schedule Management',
                      'Extra Curricular and Break',
                      '',
                      'Extra Curricular',
                      'View'
                    ) &&
                    CheckPermission(
                      'tabs',
                      'Schedule Management',
                      'Extra Curricular and Break',
                      '',
                      'Break',
                      'Edit'
                    )
                  ) {
                    return item;
                  } else if (
                    CheckPermission(
                      'tabs',
                      'Schedule Management',
                      'Extra Curricular and Break',
                      '',
                      'Break',
                      'View'
                    )
                  ) {
                    return item.get('type') === 'break';
                  } else if (
                    CheckPermission(
                      'tabs',
                      'Schedule Management',
                      'Extra Curricular and Break',
                      '',
                      'Extra Curricular',
                      'View'
                    )
                  ) {
                    return item.get('type') === 'extra_curricular';
                  }
                })
                .map((list, index) => {
                  return (
                    <tr
                      key={index}
                      className={list.get('isActive', false) === false ? 'disable-grey-tr' : ''}
                    >
                      <td>
                        <div className="pt-1">{index + 1}</div>
                      </td>
                      <td>
                        <div className="pt-1">{list.get('title', '')}</div>
                      </td>
                      <td>
                        <div className="pt-1">
                          {list.get('type', '') === 'extra_curricular'
                            ? t('course_schedule.extra_curricular')
                            : t('course_schedule.break')}
                        </div>
                      </td>
                      <td>
                        <div className="pt-1">{jsUcfirst(list.get('gender', 'male'))}</div>
                      </td>
                      <td>
                        <div className="pt-1">{formatDays(list.get('days', List()))}</div>
                      </td>
                      <td>
                        <div className="pt-1">
                          {formatTwoString(list.getIn(['startTime', 'hour'], ''))}:
                          {formatTwoString(list.getIn(['startTime', 'minute'], ''))}{' '}
                          {getTranslatedDuration(
                            list.getIn(['startTime', 'format'], '').toLowerCase()
                          ).toUpperCase()}
                        </div>
                      </td>
                      <td>
                        <div className="pt-1">
                          {formatTwoString(list.getIn(['endTime', 'hour'], ''))}:
                          {formatTwoString(list.getIn(['endTime', 'minute'], ''))}{' '}
                          {getTranslatedDuration(
                            list.getIn(['endTime', 'format'], '').toLowerCase()
                          ).toUpperCase()}
                        </div>
                      </td>
                      <td className="">
                        <div className="pt-1">
                          {currentCalendar &&
                            (CheckPermission(
                              'tabs',
                              'Schedule Management',
                              'Extra Curricular and Break',
                              '',
                              'Extra Curricular',
                              'Enable/Disable'
                            ) ||
                              CheckPermission(
                                'tabs',
                                'Schedule Management',
                                'Extra Curricular and Break',
                                '',
                                'Break',
                                'Enable/Disable'
                              )) &&
                            (CheckPermission(
                              'tabs',
                              'Schedule Management',
                              'Extra Curricular and Break',
                              '',
                              'Extra Curricular',
                              'Delete'
                            ) ||
                              CheckPermission(
                                'tabs',
                                'Schedule Management',
                                'Extra Curricular and Break',
                                '',
                                'Break',
                                'Delete'
                              )) &&
                            (CheckPermission(
                              'tabs',
                              'Schedule Management',
                              'Extra Curricular and Break',
                              '',
                              'Extra Curricular',
                              'Edit'
                            ) ||
                              CheckPermission(
                                'tabs',
                                'Schedule Management',
                                'Extra Curricular and Break',
                                '',
                                'Break',
                                'Edit'
                              )) && (
                              <small className="f-18">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    variant=""
                                    id="dropdown-table"
                                    className="table-dropdown"
                                    size="sm"
                                  >
                                    <div className="f-16">
                                      <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                                    </div>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu>
                                    {(CheckPermission(
                                      'tabs',
                                      'Schedule Management',
                                      'Extra Curricular and Break',
                                      '',
                                      'Extra Curricular',
                                      'Enable/Disable'
                                    ) ||
                                      CheckPermission(
                                        'tabs',
                                        'Schedule Management',
                                        'Extra Curricular and Break',
                                        '',
                                        'Break',
                                        'Enable/Disable'
                                      )) && (
                                      <Dropdown.Item onClick={() => handleStatus(list, false)}>
                                        {list.get('isActive', false) === true
                                          ? t('disable')
                                          : t('enable')}
                                      </Dropdown.Item>
                                    )}
                                    {(CheckPermission(
                                      'tabs',
                                      'Schedule Management',
                                      'Extra Curricular and Break',
                                      '',
                                      'Extra Curricular',
                                      'Delete'
                                    ) ||
                                      CheckPermission(
                                        'tabs',
                                        'Schedule Management',
                                        'Extra Curricular and Break',
                                        '',
                                        'Break',
                                        'Delete'
                                      )) && (
                                      <Dropdown.Item onClick={() => handleDeleteData(list, false)}>
                                        {t('delete')}
                                      </Dropdown.Item>
                                    )}
                                    {(CheckPermission(
                                      'tabs',
                                      'Schedule Management',
                                      'Extra Curricular and Break',
                                      '',
                                      'Extra Curricular',
                                      'Edit'
                                    ) ||
                                      CheckPermission(
                                        'tabs',
                                        'Schedule Management',
                                        'Extra Curricular and Break',
                                        '',
                                        'Break',
                                        'Edit'
                                      )) && (
                                      <Dropdown.Item
                                        onClick={() =>
                                          handleAddEditExtraClick('update', list.get('type'), list)
                                        }
                                      >
                                        {t('edit')}
                                      </Dropdown.Item>
                                    )}
                                  </Dropdown.Menu>
                                </Dropdown>{' '}
                              </small>
                            )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
          </tbody>
        </Table>
        {extraCurricularAndBreakTiming.size === 0 && renderEmptyHtml()}
      </>
    );
  }

  function handleSelect(e, name) {
    e.preventDefault();
    setActiveFilter(activeFilter.set(name, e.target.value));
  }

  function handleSaveClick() {
    const {
      extraCurricularAndBreakTiming,
      resetMessage,
      saveExtraCurricularAndTiming,
      activeInstitutionCalendar,
    } = props;
    const programId = getURLParams('programId', true);
    const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id', '');
    let filteredData = extraCurricularAndBreakTiming;
    const currentData = getTrimmedValues(activeData);
    const currentId = activeData.get('_id', '');
    const validationMessage = validateData(currentData, filteredData, currentId);

    if (validationMessage) {
      return resetMessage(validationMessage);
    }
    const requestBody = {
      ...currentData.toJS(),
      ...{ days: sortDays(currentData.get('days', '')) },
      ...{ startTime: formatDateObject(currentData.get('startTime', '')) },
      ...{ endTime: formatDateObject(currentData.get('endTime', '')) },
      type,
    };
    saveExtraCurricularAndTiming({
      mode,
      programId,
      ...(currentId && { _id: currentId }),
      ...(activeInstitutionCalendarId && { instCalId: activeInstitutionCalendarId }),
      requestBody,
      type,
      callback: handleCancelClick,
    });
  }

  const TYPE_LIST = [
    { name: t('all'), value: '' },
    { name: t('course_schedule.type_list.break'), value: 'break' },
    {
      name: t('course_schedule.type_list.extra_curricular'),
      value: 'extra_curricular',
    },
  ];

  const GENDER_LIST = [
    { name: t('select'), value: '' },
    { name: t('course_schedule.gender_list.male'), value: 'male' },
    { name: t('course_schedule.gender_list.female'), value: 'female' },
    { name: t('course_schedule.gender_list.both'), value: 'both' },
  ];

  const STATUS_LIST = [
    { name: t('all'), value: '' },
    { name: t('course_schedule.status_list.enable'), value: true },
    { name: t('course_schedule.status_list.disable'), value: false },
  ];

  return (
    <div className="main bg-gray pb-5">
      <BreadcrumbHeader callBack={backUrl} {...props} />
      <div className="bg-gray">
        <div className="container-fluid">
          <div className="row pb-4">
            <div className="col-md-12">
              <div className="p-5">
                <div className="pt-4 pb-4">
                  <div className="bg-white border-radious-8 pb-5">
                    <div className="p-4">
                      <div className="row">
                        <div className="col-md-10">
                          <div className="row">
                            <div className="col-md-3 pl-2 pr-1">
                              <Input
                                elementType={'select'}
                                elementConfig={{
                                  options: TYPE_LIST,
                                }}
                                value={activeFilter.get('selectedType', '')}
                                className={'selectArrow'}
                                label={t('type')}
                                labelclass={'f-13'}
                                changed={(e) => handleSelect(e, 'selectedType')}
                              />
                            </div>

                            <div className="col-md-3 pl-2 pr-1">
                              <Input
                                elementType={'select'}
                                elementConfig={{
                                  options: GENDER_LIST,
                                }}
                                value={activeFilter.get('selectedGender', '')}
                                className={'selectArrow'}
                                label={t('gender')}
                                labelclass={'f-13'}
                                changed={(e) => handleSelect(e, 'selectedGender')}
                              />
                            </div>

                            <div className="col-md-3 pl-2 pr-1">
                              <Input
                                elementType={'select'}
                                elementConfig={{
                                  options: STATUS_LIST,
                                }}
                                value={activeFilter.get('selectedStatus', '')}
                                className={'selectArrow'}
                                label={t('course_schedule.status')}
                                labelclass={'f-13'}
                                changed={(e) => handleSelect(e, 'selectedStatus')}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-md-2">
                          <div className="float-right pt-4">
                            {currentCalendar &&
                              (CheckPermission(
                                'tabs',
                                'Schedule Management',
                                'Extra Curricular and Break',
                                '',
                                'Extra Curricular',
                                'Add'
                              ) ||
                                CheckPermission(
                                  'tabs',
                                  'Schedule Management',
                                  'Extra Curricular and Break',
                                  '',
                                  'Break',
                                  'Add'
                                )) && (
                                <DropdownButton id="dropdown-basic-button1" title={t('add')}>
                                  {CheckPermission(
                                    'tabs',
                                    'Schedule Management',
                                    'Extra Curricular and Break',
                                    '',
                                    'Extra Curricular',
                                    'Add'
                                  ) && (
                                    <Dropdown.Item
                                      onClick={() =>
                                        handleAddEditExtraClick('create', 'extra_curricular', Map())
                                      }
                                    >
                                      <Trans i18nKey={'course_schedule.extra_curricular'}></Trans>
                                    </Dropdown.Item>
                                  )}
                                  {CheckPermission(
                                    'tabs',
                                    'Schedule Management',
                                    'Extra Curricular and Break',
                                    '',
                                    'Break',
                                    'Add'
                                  ) && (
                                    <Dropdown.Item
                                      onClick={() =>
                                        handleAddEditExtraClick('create', 'break', Map())
                                      }
                                    >
                                      <Trans i18nKey={'course_schedule.break'}></Trans>
                                    </Dropdown.Item>
                                  )}
                                </DropdownButton>
                              )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="min_h p-1">{tableList()}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {showModal && (
        <Suspense fallback="">
          <AddEditExtraCurricularAndTiming
            show={showModal}
            mode={mode}
            type={type}
            data={activeData}
            onHide={handleCancelClick}
            onSave={handleSaveClick}
            onChange={handleChange}
          />
        </Suspense>
      )}
      {alertConfirmModalData.show && (
        <Suspense fallback="">
          <AlertConfirmModal
            show={alertConfirmModalData.show}
            title={alertConfirmModalData.title || ''}
            titleIcon={alertConfirmModalData.titleIcon}
            body={alertConfirmModalData.body || ''}
            variant={alertConfirmModalData.variant || 'confirm'}
            confirmButtonLabel={alertConfirmModalData.confirmButtonLabel || 'YES'}
            cancelButtonLabel={alertConfirmModalData.cancelButtonLabel || 'NO'}
            onClose={onModalClose}
            onConfirm={onConfirm}
            data={alertConfirmModalData.data}
          />
        </Suspense>
      )}
    </div>
  );
}

ExtraCurricular.propTypes = {
  extraCurricularAndBreakTiming: PropTypes.instanceOf(List),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getExtraCurricularList: PropTypes.func,
  saveExtraCurricularAndTiming: PropTypes.func,
  resetMessage: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    extraCurricularAndBreakTiming: selectExtraCurricularList(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, actions)(ExtraCurricular);
