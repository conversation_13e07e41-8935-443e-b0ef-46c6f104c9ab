import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import Chip from '@mui/material/Chip';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import { getDepartments, getPrograms, getSubjects } from '../../utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';

function FilterDetails({ filterData, sharedRadio, clearAll }) {
  const department = useContext(parentContext.departmentContext);
  const { departmentDetails, translateInput, departmentTabValue } = department;
  const {
    departmentFilter,
    setDepartmentFilter,
    programFilter,
    setProgramFilter,
    subjectFilter,
    setSubjectFilter,
    sharedWithFilter,
    setSharedWithFilter,
    sharedWithFromFilter,
    setSharedWithFromFilter,
  } = filterData;

  const removeSelectedDepartment = (selectedItem) => {
    setDepartmentFilter(departmentFilter.filter((item) => item !== selectedItem));
  };
  const removeSelectedProgram = (selectedItem) => {
    setProgramFilter(programFilter.filter((item) => item !== selectedItem));
  };
  const removeSelectedSubject = (selectedItem) => {
    setSubjectFilter(subjectFilter.filter((item) => item !== selectedItem));
  };
  const removeSelectedSharedWith = (selectedItem) => {
    setSharedWithFilter(sharedWithFilter.filter((item) => item !== selectedItem));
  };
  const removeSelectedSharedWithFrom = (selectedItem) => {
    setSharedWithFromFilter(sharedWithFromFilter.filter((item) => item !== selectedItem));
  };

  return (
    <div className="d-flex justify-content-between">
      <div className={'pt-3 pb-3'}>
        {departmentFilter.map((item, index) => (
          <Chip
            key={index}
            label={
              <>
                {getShortString(translateInput.departmentName, 15)} -{' '}
                {
                  getDepartments(departmentDetails, departmentTabValue).filter(
                    (dItem) => dItem.value === item
                  )?.[0]?.name
                }
              </>
            }
            variant="outlined"
            className={'mr-2 mb-2'}
            onDelete={() => removeSelectedDepartment(item)}
          />
        ))}
        {programFilter.map((item, index) => (
          <Chip
            key={index}
            label={
              <>
                {getShortString(translateInput.program, 15)} -{' '}
                {getPrograms(departmentDetails).filter((dItem) => dItem.value === item)?.[0]?.name}
              </>
            }
            variant="outlined"
            className={'mr-2 mb-2'}
            onDelete={() => removeSelectedProgram(item)}
          />
        ))}
        {subjectFilter.map((item, index) => (
          <Chip
            key={index}
            label={
              <>
                {getShortString(translateInput.subjectName, 15)} -{' '}
                {
                  getSubjects(departmentDetails, departmentTabValue).filter(
                    (dItem) => dItem.value === item
                  )?.[0]?.name
                }
              </>
            }
            variant="outlined"
            className={'mr-2 mb-2'}
            onDelete={() => removeSelectedSubject(item)}
          />
        ))}
        {sharedWithFilter.map((item, index) => (
          <Chip
            key={index}
            label={
              <>
                Shared With -{' '}
                {getPrograms(departmentDetails).filter((dItem) => dItem.value === item)?.[0]?.name}
              </>
            }
            variant="outlined"
            className={'mr-2 mb-2'}
            onDelete={() => removeSelectedSharedWith(item)}
          />
        ))}
        {sharedWithFromFilter.map((item, index) => (
          <Chip
            key={index}
            label={
              <>
                {`Shared ${sharedRadio === 'sharedWith' ? 'With' : 'From'}`} -{' '}
                {getPrograms(departmentDetails).filter((dItem) => dItem.value === item)?.[0]?.name}
              </>
            }
            variant="outlined"
            className={'mr-2 mb-2'}
            onDelete={() => removeSelectedSharedWithFrom(item)}
          />
        ))}
      </div>
      <p className="mt-4 mb-2 mr-2 text-skyblue remove_hover" onClick={clearAll}>
        {' '}
        Clear Filter
      </p>
    </div>
  );
}

FilterDetails.propTypes = {
  filterData: PropTypes.object,
  sharedRadio: PropTypes.string,
  clearAll: PropTypes.func,
};
export default FilterDetails;
