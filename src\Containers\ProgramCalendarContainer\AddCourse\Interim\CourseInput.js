import React, { Fragment, useReducer, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { connect } from 'react-redux';
import { NotificationManager } from 'react-notifications';
import Loader from '../../../../Widgets/Loader/Loader';
import swal from 'sweetalert2';
import {
  Null,
  PrimaryButton,
  FlexWrapper,
  Padding,
  EventWrapper,
  ModalWrapper,
  ModalBackgroundWrapper,
} from '../../Styled';
import moment from 'moment';
import PropTypes from 'prop-types';

import {
  interimDashboard,
  interimLandingApi,
  getCourses,
  listCourseEvents,
  saveCourseEvents,
  resetInterimMessage,
  updateCourseEvents,
  courseSave,
  courseUpdate,
  interimChangeTitle,
  // interimActivateModal,
  //loadInterimDataFromInstitution
} from '../../../../_reduxapi/actions/interimCalendar';

import { nonRotational } from './InitialState';
import { rootNonRotationalReducer } from './CourseReducer';

/* import rootInterimReducer from '../../CalenderSettings/Interim/SettingsReducer';
import { level_initial_interim_state } from '../../CalenderSettings/Interim/InitialState'; */

import ChooseLevel from './ChooseLevel';
import BackgroundSelect from '../BackgroundSelect';
import CourseTitle from './CourseTitle';
import CourseDuration from './CourseDuration';
import EventRows from '../../UtilityComponents/EventRows';
import AddEvent from '../../Modal/Events/AddEvent';
import { getLang, jsUcfirstAll, timeFormat } from '../../../../utils';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
const lang = getLang();

const CourseInput = (props) => {
  const {
    interimDashboard,
    interimLandingApi,
    id,
    groupYears,
    active,
    active_semesters,
    getCourses,
    currentProgramCalendarId,
    courses,
    listCourseEvents,
    saveCourseEvents,
    error,
    success,
    isLoading,
    resetInterimMessage,
    course_events,
    updateCourseEvents,
    courseSave,
    courseUpdate,
    course_events_copy,
    interimChangeTitle,
    activeInstitutionCalendar,
  } = props;

  const non_rotation = useReducer(rootNonRotationalReducer, nonRotational);
  const [nonRotation, setNonRotation] = non_rotation;

  const history = useHistory();

  const [count1, setCount1] = useState(0);
  const [count2, setCount2] = useState(0);

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let programId = params.get('programid');
  let calendarid = params.get('calendarid');
  let urlYear = params.get('year');
  let urlSemester = params.get('semester');
  let urlName = params.get('pname');

  let editCourse = params.get('edit');
  let _id = params.get('_id');
  let titleHeader = t('role_management.role_actions.Add Course');
  let saveButton = t('add');
  if (editCourse !== null) {
    titleHeader = t('program_calendar.edit_course');
    saveButton = t('save');
  }

  let activeYear = active;
  let activeSemester = active_semesters;
  if (urlYear !== null) {
    activeYear = urlYear;
  }
  if (urlSemester !== null) {
    activeSemester = urlSemester;
  }
  let countOfSemesters = groupYears[activeYear]['semesters'];
  let currentActiveSemester = countOfSemesters[activeSemester];
  useEffect(() => {
    interimChangeTitle(titleHeader);
  }, [interimChangeTitle, titleHeader]);

  useEffect(() => {
    if (id === null) {
      if (count1 === 0 && activeInstitutionCalendar && !activeInstitutionCalendar.isEmpty()) {
        interimDashboard(programId, calendarid, activeYear, activeInstitutionCalendar.get('_id'));
        setCount1(1);
      }
    }
    if (
      count2 === 0 &&
      id !== null &&
      activeInstitutionCalendar &&
      !activeInstitutionCalendar.isEmpty()
    ) {
      interimLandingApi(programId, calendarid, activeInstitutionCalendar.get('_id'));
      setCount2(1);
    }
    setNonRotation({
      type: 'ADD_COURSES',
      payload: courses,
    });
  }, [
    props,
    activeYear,
    setNonRotation,
    interimDashboard,
    count1,
    count2,
    interimLandingApi,
    id,
    programId,
    calendarid,
    groupYears,
    activeSemester,
    courses,
    activeInstitutionCalendar,
  ]);

  useEffect(() => {
    if (editCourse !== null) {
      let activeArray = [];
      if (currentActiveSemester && currentActiveSemester.length > 0) {
        activeArray = currentActiveSemester.filter((list) => {
          return list._id === _id;
        });
      }
      if (activeArray && activeArray.length > 0) {
        let actArray = activeArray[0];
        setNonRotation({
          type: 'INITIAL_LOAD_EDIT_COURSE',
          payload: actArray,
          course_id: editCourse,
        });
      }
    }
  }, [editCourse, currentActiveSemester, _id, setNonRotation]);

  useEffect(() => {
    if (
      nonRotation.title !== '' &&
      nonRotation.type.start_date !== '' &&
      nonRotation.type.end_date !== '' &&
      nonRotation.type._course_id !== '' &&
      nonRotation.triggerLists &&
      editCourse === null
    ) {
      let splitTitle = nonRotation.title.split('-');
      let level_no = splitTitle[1];
      listCourseEvents(
        currentProgramCalendarId,
        level_no,
        nonRotation.type._course_id,
        nonRotation.type.start_date,
        nonRotation.type.end_date,
        'add'
      );
      setNonRotation({ type: 'STOP_LISTING', payload: false });
    } else if (
      nonRotation.title !== '' &&
      nonRotation.type.start_date !== '' &&
      nonRotation.type.end_date !== '' &&
      nonRotation.type._course_id !== '' &&
      nonRotation.triggerLists &&
      editCourse !== null
    ) {
      let splitTitle = nonRotation.title.split('-');
      let level_no = splitTitle[1];
      listCourseEvents(
        currentProgramCalendarId,
        level_no,
        nonRotation.type._course_id,
        nonRotation.type.start_date,
        nonRotation.type.end_date,
        'edit'
      );
      setNonRotation({ type: 'STOP_LISTING', payload: false });
    }
  }, [nonRotation, setNonRotation, editCourse, currentProgramCalendarId, listCourseEvents]);

  useEffect(() => {
    setNonRotation({
      type: 'EVENT_ATTACH',
      payload: course_events,
    });
  }, [setNonRotation, course_events]);

  useEffect(() => {
    if (editCourse !== null) {
      setNonRotation({
        type: 'EVENT_COPY_TO_DELETE',
        payload: course_events_copy,
      });
    }
  }, [course_events_copy, editCourse, setNonRotation]);

  useEffect(() => {
    if (error !== null) {
      NotificationManager.error(error);
      setTimeout(() => {
        resetInterimMessage();
      }, 2000);
    } else if (success !== null) {
      //NotificationManager.success(success);
      setNonRotation({
        type: 'OFF_MODAL',
      });
      // setTimeout(() => {
      //   resetInterimMessage();
      // }, 2000);
      if (
        success === jsUcfirstAll('Program calendar course added successfully') ||
        success === jsUcfirstAll('Program calendar course updated successfully')
      ) {
        history.push(
          `/program-calendar?year=${urlYear}&icd=${calendarid}&programid=${programId}&pname=${urlName}`
        );
      } else if (
        success === 'Program Calendar Course Event Added Successfully' ||
        success === 'Program Calendar Course Event Updated Successfully'
      ) {
        NotificationManager.success(success);
        setTimeout(() => {
          resetInterimMessage();
        }, 2000);
      }
    }
  }, [
    error,
    success,
    resetInterimMessage,
    calendarid,
    history,
    programId,
    setNonRotation,
    urlName,
    urlYear,
  ]);

  const dataAlign = (state, rotation, rotationCount, dispatchFn) => {
    let data = {};
    let levelTitle = state.title.split('-');
    let courseId = state.type._course_id;
    let error = false;
    if (state.type.start_date === '' || state.type.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (courseId === '') {
      NotificationManager.error('Course id is required');
      error = true;
    }

    let events = [];
    if (state.events && state.events.length > 0) {
      events = state.events;
    }

    data._calendar_id = currentProgramCalendarId;
    data.level_no = levelTitle[1];
    data.batch = levelTitle[0];
    data._course_id = courseId;
    data.start_date = moment(state.type.start_date).format('YYYY-MM-DD');
    data.end_date = moment(state.type.end_date).format('YYYY-MM-DD');
    data.color_code = state.type.background_color;
    data._event_id = events.filter((item) => item._event_id).map((item) => item._event_id);
    if (rotation === 'yes') {
      data.rotation_count = rotationCount;
      data.by = 'date';
      data._batch_course_id = [];
    }
    if (!error) {
      dispatchFn(data, rotation);
      interimChangeTitle('');
    }
  };

  const eventDeleteConfirm = (i, event_id) => {
    swal
      .fire({
        title: t('sure_delete'),
        text: t('once_deleted_cant_recover'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: t('yes_delete'),
        cancelButtonText: t('no_keep'),
        dangerMode: true,
      })
      .then((res) => {
        if (res.isConfirmed) {
          setNonRotation({
            type: 'DELETE_EVENT',
            payload: i,
            event_id: event_id,
          });
          NotificationManager.success('Program Calendar Course Event Deleted Successfully');
          setTimeout(() => {
            resetInterimMessage();
          }, 2000);
        }
      });
  };

  const dataEventAlign = (content, rotation, rotationCount) => {
    let error = false;
    let final = { event_name: {} };
    let data = content.modal_content;

    let levelTitle = content.title.split('-');
    let courseId = content.type._course_id;

    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (courseId === '') {
      NotificationManager.error('Course id is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.start_date) > Date.parse(data.end_date)) {
      error = true;
      NotificationManager.error('End date should be greater than start date');
    }

    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    if (rotation === 'yes') {
      final.rotation_count = rotationCount;
    }
    final.event_calendar = 'course';
    final.year = activeYear;
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final.end_date = endDate;
    final._calendar_id = currentProgramCalendarId;
    final.batch = levelTitle[0];
    final._course_id = courseId;
    if (!error) {
      saveCourseEvents(
        final,
        currentProgramCalendarId,
        levelTitle[1],
        courseId,
        content.type.start_date,
        content.type.end_date
      );
    }
  };

  const updateEvent = (content, rotation, rotationCount) => {
    let error = false;
    let final = { event_name: {} };

    let data = content.modal_content;
    let levelTitle = content.title.split('-');
    let courseId = content.type._course_id;
    const start = data.start_date + ' ' + data.start_time + ':00:000';
    const end = data.end_date + ' ' + data.end_time + ':00:000';

    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (courseId === '') {
      NotificationManager.error('Course id is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.end_date) < Date.parse(data.start_date)) {
      NotificationManager.error('Start date should not greater than End date');
      error = true;
    } else if (Date.parse(start) > Date.parse(end) || Date.parse(start) === Date.parse(end)) {
      NotificationManager.error('End time should be greater than Start time');
      error = true;
    }

    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    //final.year = active.slice(4);
    final.event_calendar = 'course';
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final._event_id = data._id;
    final.end_date = endDate;
    final._calendar_id = currentProgramCalendarId;
    final.batch = levelTitle[0];
    final.level_no = levelTitle[1];
    final._course_id = courseId;
    if (rotation === 'yes') {
      final.rotation_count = rotationCount;
    }

    if (!error) {
      updateCourseEvents(
        final,
        rotation,
        currentProgramCalendarId,
        levelTitle[1],
        courseId,
        content.type.start_date,
        content.type.end_date
      );
    }
  };

  const temp = (rotation, rotationCount) => (
    <ModalWrapper>
      <ModalBackgroundWrapper>
        <h3 className="text-left">
          {nonRotation.modal_mode === 'add'
            ? t('role_management.role_actions.Add Event')
            : t('events.edit_event')}
        </h3>
        <p className="text-left">
          <Trans i18nKey={'select_date_to_sync'}></Trans>{' '}
        </p>
        <AddEvent
          data={nonRotation.modal_content}
          method={setNonRotation}
          min_len={nonRotation.type.start_date}
          max_len={nonRotation.type.end_date}
          levelStartDate={nonRotation.type.start_date}
          levelEndDate={nonRotation.type.end_date}
        />
        <FlexWrapper>
          <Null />
          <PrimaryButton className="light" onClick={() => setNonRotation({ type: 'OFF_MODAL' })}>
            <Trans i18nKey={'cancel'}></Trans>
          </PrimaryButton>
          <PrimaryButton
            className="bordernone"
            onClick={() => {
              if (nonRotation.modal_mode === 'add') {
                dataEventAlign(nonRotation, rotation, rotationCount);
              } else {
                updateEvent(nonRotation, rotation, rotationCount);
              }
            }}
          >
            <Trans i18nKey={'save'}></Trans>
          </PrimaryButton>
        </FlexWrapper>
      </ModalBackgroundWrapper>
    </ModalWrapper>
  );

  let startDate = '';
  let endDate = '';
  let rotation = 'no';
  let rotationCount = 0;
  let batch = '';
  let level_no = '';
  if (nonRotation.title !== '' && currentActiveSemester && currentActiveSemester.length) {
    let splitTitle = nonRotation.title.split('-');
    batch = splitTitle[0];
    level_no = splitTitle[1];
    let currentLevelData = currentActiveSemester
      .filter((list) => {
        return list.level_no === level_no && list.term === batch;
      })
      .reduce((_, el) => {
        return el;
      }, {});
    startDate =
      currentLevelData.start_date !== undefined && currentLevelData.start_date !== ''
        ? moment(currentLevelData.start_date).format('YYYY-MM-DD')
        : '';
    endDate =
      currentLevelData.end_date !== undefined && currentLevelData.end_date !== ''
        ? moment(currentLevelData.end_date).format('YYYY-MM-DD')
        : '';
    rotation = currentLevelData.rotation;
    rotationCount = currentLevelData.rotation_count;
  }

  let currentLevelNo = '';
  let currentTerm = '';
  if (nonRotation.title !== undefined && nonRotation.title !== null && nonRotation.title !== '') {
    let splitTitle = nonRotation.title.split('-');
    currentLevelNo = splitTitle[1];
    currentTerm = splitTitle[0];
  }

  return (
    <div className="main">
      <Loader isLoading={isLoading} />
      <Fragment>{nonRotation.modal && temp(rotation, rotationCount)}</Fragment>
      <Fragment>
        <FlexWrapper>
          <Padding
            className="back"
            onClick={() => {
              interimChangeTitle('');
              history.push(
                `/program-calendar?year=${urlYear}&icd=${calendarid}&programid=${programId}&pname=${urlName}`
              );
            }}
          >
            <i className={`fas ${lang !== 'ar' ? 'fa-arrow-left' : 'fa-arrow-right'}`}></i>
          </Padding>
          <Padding
            className={`${lang !== 'ar' ? '' : 'ar-padding'}`}
            style={{ paddingLeft: '0px', fontSize: '20px' }}
          >
            {titleHeader}
          </Padding>
          <Null />
          {nonRotation.type.start_date && nonRotation.type.end_date && (
            <PrimaryButton
              className={'bordernone'}
              disabled={''}
              onClick={() => {
                if (nonRotation.update_method === 'add') {
                  dataAlign(nonRotation, rotation, rotationCount, courseSave);
                } else {
                  dataAlign(nonRotation, rotation, rotationCount, courseUpdate);
                }
              }}
            >
              {saveButton}
            </PrimaryButton>
          )}
        </FlexWrapper>
        <FlexWrapper mg={lang !== 'ar' ? '0 50px 0px 65px' : '0 65px 0px 50px'}>
          <ChooseLevel
            data={non_rotation}
            currentData={currentActiveSemester}
            active={activeYear}
            getCourses={getCourses}
            currentProgramCalendarId={currentProgramCalendarId}
            programId={programId}
          />
          <Null />
          {nonRotation.title && <BackgroundSelect data={non_rotation} />}
        </FlexWrapper>
        {nonRotation.title && <CourseTitle data={non_rotation} programId={programId} />}

        {nonRotation.type._course_id && nonRotation.type.model && (
          <CourseDuration
            data={non_rotation}
            startDate={startDate}
            endDate={endDate}
            rotation={rotation}
            programId={programId}
          />
        )}
        {nonRotation.type.start_date && nonRotation.type.end_date && (
          <Fragment>
            {' '}
            <FlexWrapper mg="20px 30px 10px 30px">
              <p style={{ marginBottom: '-1rem' }}>
                <Trans i18nKey={'list_of_events_that_occur'}></Trans>.
              </p>
              <Null />
              <PrimaryButton
                className="light"
                onClick={() => {
                  setNonRotation({ type: 'SHOW_MODAL', payload: 'add' });
                  // LocalStorageService.setCustomToken(
                  //   "courseInputData",
                  //   JSON.stringify(nonRotation)
                  // );
                }}
              >
                {' '}
                <i className="fas fa-plus" /> <Trans i18nKey={'program_calendar.events'}></Trans>
              </PrimaryButton>
            </FlexWrapper>
            <EventWrapper of_scroll="auto" className="go-wrapper-width">
              <EventRows show="title" />
              <div className="go-wrapper-height">
                {nonRotation.events &&
                  nonRotation.events
                    .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
                    .map((item, i) => {
                      let editEnable = true;
                      let filteredEvent = [];
                      if (currentLevelNo !== '' && currentTerm !== '') {
                        if (
                          currentActiveSemester !== undefined &&
                          currentActiveSemester &&
                          currentActiveSemester.length > 0
                        ) {
                          filteredEvent = currentActiveSemester.filter((list) => {
                            return list.term === currentTerm && list.level_no === currentLevelNo;
                          });
                        }
                      }
                      let filterValue = 0;
                      if (filteredEvent[0].events && filteredEvent[0].events.length > 0) {
                        filterValue = filteredEvent[0].events
                          .filter((list) => {
                            return list._event_id === item._event_id;
                          })
                          .reduce((_, el) => {
                            return el._id;
                          }, 0);
                      }

                      if (filterValue !== 0) {
                        editEnable = false;
                      }

                      return (
                        <EventRows
                          show="content"
                          i={i}
                          key={i}
                          content={item}
                          editHide={!editEnable}
                          edit={() => {
                            setNonRotation({
                              type: 'EDIT_EVENT',
                              payload: i,
                              event: item,
                            });
                          }}
                          del={() => eventDeleteConfirm(i, item._event_id)}
                        />
                      );
                    })}
              </div>
            </EventWrapper>{' '}
          </Fragment>
        )}
      </Fragment>
    </div>
  );
};

CourseInput.propTypes = {
  interimDashboard: PropTypes.func,
  interimLandingApi: PropTypes.func,
  id: PropTypes.string,
  groupYears: PropTypes.object,
  active: PropTypes.string,
  active_semesters: PropTypes.string,
  getCourses: PropTypes.func,
  currentProgramCalendarId: PropTypes.string,
  courses: PropTypes.object,
  listCourseEvents: PropTypes.func,
  saveCourseEvents: PropTypes.func,
  error: PropTypes.object,
  success: PropTypes.object,
  isLoading: PropTypes.bool,
  resetInterimMessage: PropTypes.func,
  course_events: PropTypes.object,
  updateCourseEvents: PropTypes.func,
  courseSave: PropTypes.func,
  courseUpdate: PropTypes.func,
  course_events_copy: PropTypes.object,
  interimChangeTitle: PropTypes.func,
  activeInstitutionCalendar: PropTypes.object,
};

const mapStateToProps = function (state) {
  // ({ calender, auth, interimCalendar }) => ({
  const { calender, auth, interimCalendar } = state;
  return {
    edit: calender.course_editing,
    id: interimCalendar.institution_Calender_Id,
    title: interimCalendar.academic_year_name,
    status: interimCalendar.curriculum,
    active: interimCalendar.active_year,
    active_semesters: interimCalendar.active_semesters,
    isLoading: interimCalendar.isLoading,
    isAuthenticated: auth.token !== null,
    token: auth.token !== null ? auth.token.replace(/"/g, '') : null,
    currentProgramCalendarId: interimCalendar.currentProgramCalendarId,
    userRole: auth.loggedInUserData.role,
    groupYears: {
      year1: interimCalendar.year1,
      year2: interimCalendar.year2,
      year3: interimCalendar.year3,
      year4: interimCalendar.year4,
      year5: interimCalendar.year5,
      year6: interimCalendar.year6,
    },
    courses: interimCalendar.add_courses,
    error: interimCalendar.error,
    success: interimCalendar.success,
    course_events: interimCalendar.course_events,
    course_events_copy: interimCalendar.course_events_copy,
    year1: interimCalendar.year1,
    year2: interimCalendar.year2,
    year3: interimCalendar.year3,
    year4: interimCalendar.year4,
    year5: interimCalendar.year5,
    year6: interimCalendar.year6,
    index: interimCalendar.acad_index,
    total: interimCalendar.total_academic_year_inDB,
    programId: calender.programId,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, {
  interimDashboard,
  interimLandingApi,
  getCourses,
  listCourseEvents,
  saveCourseEvents,
  resetInterimMessage,
  updateCourseEvents,
  courseSave,
  courseUpdate,
  interimChangeTitle,
})(CourseInput);
