import React, { useEffect, useState, Suspense } from 'react';
import EmailConfigure from 'Modules/GlobalConfiguration/modal/EmailConfigure';
import PropTypes from 'prop-types';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectMailConfiguration } from '_reduxapi/global_configuration/selectors';
import { connect } from 'react-redux';
import { IconButton, Menu, MenuItem } from '@mui/material';
import { Trans } from 'react-i18next';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { List, Map } from 'immutable';
import { AccordionProgramInput } from '../ReusableComponent';
import { t } from 'i18next';
import Alert from '@mui/material/Alert';
import { selectBasicDetails } from '_reduxapi/global_configuration/selectors';
import ResetModal from 'Modules/GlobalConfiguration/modal/ResetAlertModal';

const EmailIdAddEditPopUp = React.lazy(() => import('../EmailIdAddEditPopUp'));
const EmailContent = (props) => {
  const {
    getMailConfiguration,
    mailConfigurationDetails,
    emailIdConFiguration,
    settingId,
    accordionOpen,
    setAccordionOpen,
    setAccordionHeader,
    count,
    institutionHeader,
    basicDetails,
    type,
    resetMailContent,
    resetParticularMailContent,
  } = props;
  const emailIdConfig = basicDetails.get('emailIdConfiguration', Map());
  const id = emailIdConfig.get('_id', '');
  const [openCreate, setOpen] = useState(false);
  const [show, setShow] = useState(false);
  const handleClose = () => {
    setOpen(false);
  };
  const [selectedSetting, setselectedSetting] = useState(Map());
  const [emailOpen, setEmailOpen] = useState(false);
  const handleEmailOpen = () => {
    setEmailOpen(true);
  };

  const handleEmailOpenClose = () => {
    setEmailOpen(false);
  };

  useEffect(() => {
    settingId &&
      accordionOpen &&
      !mailConfigurationDetails.size &&
      count !== 0 &&
      getMailConfiguration({ settingId: settingId, headers: institutionHeader, type });
  }, [settingId, accordionOpen, count]); //eslint-disable-line

  const handleResetAlertModal = (e) => {
    e.stopPropagation();
    setShow(true);
  };
  const callBack = () => {
    setShow(false);
  };
  const handleParticularMailReset = () => {
    const requestBody = {
      labelName: `${selectedSetting.get('labelName', '')}`,
    };
    resetParticularMailContent({
      settingId,
      headers: institutionHeader,
      type,
      requestBody,
      callBack,
    });
  };
  const handleReset = (e) => {
    e.stopPropagation();
    resetMailContent({ settingId, headers: institutionHeader, type, callBack });
  };
  const DetailsComponent = () => {
    return (
      <div className="container">
        <div className="digi-pl-12">
          {!emailIdConfig.size && (
            <div className="mb-3">
              <Alert
                severity="warning"
                className="warningAccent"
                action={
                  <div
                    className="f-14 warningAccent custom_space swal2-title mb-0"
                    onClick={() => setOpen(true)}
                  >
                    <Trans i18nKey={'add_colleges.configure_s'} />
                  </div>
                }
              >
                <p className="mb-0 f-15 warningAccent">
                  <Trans i18nKey={'global_configuration.email_configuration_pending'} />
                </p>
              </Alert>
            </div>
          )}
          {mailConfigurationDetails?.map((setting) => (
            <div
              key={setting.get('_id', '')}
              className="d-flex justify-content-between align-items-center bold"
            >
              <p>{setting.get('labelName', '')}</p>
              <MenuOptions setting={setting} />
            </div>
          ))}
        </div>
      </div>
    );
  };

  const MenuOptions = ({ setting }) => {
    MenuOptions.propTypes = {
      setting: PropTypes.instanceOf(Map),
    };
    const [anchorEl, setAnchorEl] = useState(null);
    const menuOpen = Boolean(anchorEl);
    const handleMenuClose = () => setAnchorEl(null);

    return (
      <div onClick={(e) => e.stopPropagation()}>
        <IconButton
          aria-label="more"
          aria-controls="long-menu"
          aria-haspopup="true"
          onClick={(e) => {
            setAnchorEl(e.currentTarget);
          }}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={menuOpen}
          onClose={handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: 48 * 4.5,
              width: '20ch',
            },
          }}
        >
          <MenuItem
            onClick={() => {
              setselectedSetting(setting);

              handleEmailOpen();
            }}
          >
            <Trans i18nKey={'edit'} />
          </MenuItem>
          <MenuItem
            onClick={(e) => {
              setselectedSetting(setting);

              handleResetAlertModal(e);
            }}
          >
            <Trans i18nKey={'global_configuration.reset'} />
          </MenuItem>
        </Menu>
      </div>
    );
  };
  const handleAccordionClick = () => {
    setAccordionOpen();
  };
  return (
    <>
      <Suspense fallback="">
        <EmailIdAddEditPopUp
          emailIdConfig={emailIdConfig}
          handleClose={handleClose}
          settingId={settingId}
          openCreate={openCreate}
          emailIdConFiguration={emailIdConFiguration}
          id={id}
          header={institutionHeader}
          manageType={type}
        />
      </Suspense>
      <AccordionProgramInput
        expanded={accordionOpen || false}
        onClick={handleAccordionClick}
        summaryChildren={setAccordionHeader(
          'userManagement.mail_content',
          `${count} ${t('mail')}`,
          !accordionOpen,
          <p
            className="text-skyblue mr-3"
            onClick={(e) => {
              setselectedSetting(Map());
              handleResetAlertModal(e);
            }}
          >
            {t('global_configuration.reset_all')}
          </p>
        )}
        detailChildren={<DetailsComponent />}
      />

      {emailOpen && selectedSetting.size && (
        <EmailConfigure
          open={emailOpen}
          setting={selectedSetting}
          handleClose={handleEmailOpenClose}
          settingId={settingId}
          institutionHeader={institutionHeader}
          type={type}
        />
      )}
      <hr />
      {show && (
        <ResetModal
          show={show}
          setShow={setShow}
          handleReset={selectedSetting.size ? handleParticularMailReset : handleReset}
          selected={
            selectedSetting.size ? selectedSetting.get('labelName', '') : 'all mail contents'
          }
        />
      )}
    </>
  );
};

EmailContent.propTypes = {
  handleEmailOpen: PropTypes.func,
  handleEmailOpenClose: PropTypes.func,
  emailOpen: PropTypes.bool,
  getMailConfiguration: PropTypes.func,
  mailConfigurationDetails: PropTypes.instanceOf(List),
  settingId: PropTypes.string,
  type: PropTypes.string,
  accordionOpen: PropTypes.bool,
  setAccordionOpen: PropTypes.func,
  emailIdConFiguration: PropTypes.func,
  setAccordionHeader: PropTypes.func,
  basicDetails: PropTypes.instanceOf(Map),
  count: PropTypes.number,
  institutionHeader: PropTypes.object,
  resetMailContent: PropTypes.func,
  resetParticularMailContent: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    mailConfigurationDetails: selectMailConfiguration(state),
    basicDetails: selectBasicDetails(state),
  };
};

export default connect(mapStateToProps, actions)(EmailContent);
