import React from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import LabelDetailRow from './LabelDetailRow';
import { AccordionProgramInput } from '../ReusableComponent';

const LabelDetail = ({
  detailsAccordionOpen,
  setDetailsAccordion,
  summaryChildren,
  noBorder,
  details,
  subDetails,
  labelType,
  labelStatusUpdateProps,
}) => {
  const DetailsComponent = () => {
    return (
      <div className={`container digi-ml-${labelType === 'vaccineDetails' ? '32' : '16'}`}>
        {details.map((item, index) => (
          <LabelDetailRow
            key={index}
            data={item}
            labelType={labelType}
            {...labelStatusUpdateProps}
          />
        ))}
        {subDetails &&
          subDetails.map((sub, subIndex) => (
            <div key={subIndex}>
              <div className="bold f-14 mt-4 mb-2">{sub.get('title')}</div>
              {sub.get('data').map((item, index) => (
                <LabelDetailRow
                  key={index}
                  data={item}
                  labelType={sub.get('labelType')}
                  {...labelStatusUpdateProps}
                />
              ))}
            </div>
          ))}
      </div>
    );
  };

  return (
    <>
      <AccordionProgramInput
        expanded={detailsAccordionOpen || false}
        onClick={setDetailsAccordion}
        summaryChildren={summaryChildren}
        detailChildren={<DetailsComponent />}
      />
      {!noBorder && <hr />}
    </>
  );
};

LabelDetail.propTypes = {
  detailsAccordionOpen: PropTypes.bool,
  setDetailsAccordion: PropTypes.func,
  summaryChildren: PropTypes.object,
  noBorder: PropTypes.bool,
  details: PropTypes.instanceOf(List),
  subDetails: PropTypes.instanceOf(List),
  labelType: PropTypes.string,
  labelStatusUpdateProps: PropTypes.object,
};

export default LabelDetail;
