import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { addParameter } from '_reduxapi/global_configuration/v1/actions';
import { setData } from '_reduxapi/global_configuration/v1/actions';
import { Chip, DialogActions, TextField } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import Buttons from 'Widgets/FormElements/material/Button';
import Checkbox from '@mui/material/Checkbox';
import Autocomplete from '@mui/material/Autocomplete';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import PropTypes from 'prop-types';
import delete_warning_leader from 'Assets/user_activity_board/delete_warning_leader.svg';
import { Map, List, fromJS } from 'immutable';
import { checkObjectIsEqual, criteriaLabels, criteriaOptions } from '../../utils';
import { selectIsLoading } from '_reduxapi/global_configuration/v1/selectors';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

export default function ParameterModal({ onClose, parameter = Map() }) {
  const dispatch = useDispatch();
  const isLoading = useSelector(selectIsLoading);
  const initialList = parameter.get('criteria', List()).reduce((acc, cur) => {
    acc = acc
      .set(
        'ObjectWise',
        acc.get('ObjectWise', List()).push(
          Map({
            label: criteriaLabels[cur.get('criteriaName', '')],
            year: cur.get('criteriaName', ''),
          })
        )
      )
      .set(
        'arrayWise',
        acc.get('arrayWise', List()).push(cur.get('criteriaName', '')),
        Map({
          ObjectWise: List(),
          arrayWise: List(),
        })
      );
    return acc;
  }, Map());
  const [select, setSelect] = useState(initialList.get('ObjectWise', List()));
  const [name, setName] = useState(parameter.get('name', ''));
  const [weight, setWeight] = useState(parameter.get('weight', ''));
  const [showWarning, setShowWarning] = useState(false);

  function onCreate() {
    let message = '';
    if (name === '') {
      message = 'Enter the Parameter Name';
    } else if (!select.size) {
      message = 'Select the criteria';
    } else if (weight === '' || Number(weight) > 100) {
      message = 'Enter the weightage and should be less than 100 ';
    }
    if (message) return dispatch(setData({ message: message }));
    let payload = {
      name: name,
      weight: Number(weight),
      ...(parameter.get('_id', '') && {
        parameterId: parameter.get('_id', ''),
      }),
      criteria: select.map((item) => ({ criteriaName: item.get('year', '') })).toJS(),
    };
    if (parameter.isEmpty()) {
      payload = { parameters: [payload] };
    }

    dispatch(addParameter(parameter.isEmpty() ? 'post' : 'put', payload, onClose));
  }

  const handleWeight = (e) => {
    const value = e.target.value;
    if (value !== '' && (Number(value) < 0 || isNaN(Number(value)))) return;
    setWeight(value);
  };

  function onCriteriaChange(value) {
    const recentSelectedValue = value[value.length - 1] ?? {};
    let updatedSelect;
    if (JSON.stringify(select.toJS()).includes(recentSelectedValue.year)) {
      updatedSelect = select.filter((item) => item.get('year', '') !== recentSelectedValue.year);
      if (initialList.get('arrayWise', List()).includes(recentSelectedValue.year)) {
        setShowWarning(true);
      }
    } else {
      updatedSelect = select.push(fromJS(recentSelectedValue));
    }
    setSelect(updatedSelect);
  }

  useEffect(() => {
    let timeout;
    if (showWarning) {
      timeout = setTimeout(() => {
        setShowWarning(false);
      }, 3000);
    }
    return () => clearTimeout(timeout);
  }, [showWarning]);
  const disabled = isLoading
    ? true
    : parameter.isEmpty()
    ? false
    : checkObjectIsEqual(
        { select, name, weight: Number(weight) },
        {
          select: initialList.get('ObjectWise', List()),
          name: parameter.get('name', ''),
          weight: parameter.get('weight', ''),
        }
      );
  return (
    <Dialog
      sx={{
        '& .css-1t1j96h-MuiPaper-root-MuiDialog-paper': {
          width: '550px',
        },
      }}
      onClick={(e) => e.stopPropagation()}
      onClose={(e) => {
        e.stopPropagation();
        onClose();
      }}
      open={true}
    >
      <DialogTitle> {parameter.isEmpty() ? 'Create New ' : 'Update the '} Parameters</DialogTitle>
      <DialogContent>
        {showWarning && (
          <div className="text-center mb-3">
            <img src={delete_warning_leader} alt="delete_warning_leader" />
          </div>
        )}
        <div>Name</div>
        <TextField
          fullWidth
          value={name}
          onChange={(e) => {
            e.stopPropagation();
            setName(e.target.value);
          }}
        />
        <div className="mt-2">Weightage%</div>
        <TextField
          fullWidth
          value={weight}
          type="number"
          onChange={(e) => {
            e.stopPropagation();
            handleWeight(e);
          }}
        />

        <div className="mt-2">{"Criteria's"}</div>
        <Autocomplete
          className="mt-2"
          multiple
          id="checkboxes-tags-demo"
          options={criteriaOptions}
          onChange={(e, value) => {
            e.stopPropagation();
            onCriteriaChange(value);
          }}
          disableCloseOnSelect
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip
                label={option.label}
                key={index}
                className="mr-1 mb-1"
                // {...getTagProps({ index })}
                // disabled
                // deleteIcon={null} // This will remove the close icon
              />
            ))
          }
          value={select.toJS()}
          getOptionLabel={(option) => option.label}
          renderOption={(props, option, { selected }) => {
            const isChecked = JSON.stringify(select).includes(option.year);
            props['aria-selected'] = isChecked;
            return (
              <li {...props}>
                <Checkbox
                  icon={icon}
                  checkedIcon={checkedIcon}
                  style={{ marginRight: 8 }}
                  checked={isChecked}
                />
                {option.label}
              </li>
            );
          }}
          // style={{ width: 500 }}
          renderInput={(params) => {
            return <TextField {...params} label="Select the criteria" />;
          }}
        />
      </DialogContent>
      <DialogActions>
        <Buttons
          variant="outlined"
          color="gray"
          className="mr-2   px-3 bold text-dark"
          sx={{ textTransform: 'none' }}
          clicked={(e) => {
            e.stopPropagation();
            onClose();
          }}
        >
          Cancel
        </Buttons>
        <Buttons
          color="primary"
          variant="contained"
          clicked={(e) => {
            e.stopPropagation();
            onCreate();
          }}
          disabled={disabled}
        >
          {parameter.isEmpty() ? 'Create' : 'Save'}
        </Buttons>
      </DialogActions>
    </Dialog>
  );
}
ParameterModal.propTypes = {
  onClose: PropTypes.func,
  parameter: PropTypes.instanceOf(Map),
};
