import { t } from 'i18next';
import React, { Fragment } from 'react';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';

import { getLang } from 'utils';
import { Label, Select } from '../../Styled';
import { indVerRename } from 'utils';
const lang = getLang();

export default function CourseTitle({ data, programId }) {
  const [nonRotation, setNonRotation] = data;
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let CourseTitle = params.get('name');
  return (
    <Fragment>
      <div className="container-fluid">
        <div className="p-40">
          <div className="row">
            <div className="col-md-6 col-xl-3 col-lg-6 col-md-6">
              <Label className={`${lang !== 'ar' ? 'pl-2' : 'text-left pr-2'}`}>
                <Trans i18nKey={'program_calendar.model_type'}></Trans>
              </Label>
              <Select
                name="model"
                value={nonRotation.type.model}
                disabled={nonRotation.disable_items}
                className={'styled-select'}
                onChange={(e) =>
                  setNonRotation({
                    type: 'ON_CHANGE',
                    payload: e.target.value,
                    name: e.target.name,
                  })
                }
              >
                <option value="">{t('master_graph.select')}</option>
                <option value="standard">
                  {t(`constant.${indVerRename('standard', programId).toLowerCase()}`)}
                </option>
                <option value="selective">{indVerRename('selective', programId)}</option>
              </Select>
            </div>

            <div className="col-md-6 col-xl-3 col-lg-6 col-md-6">
              {nonRotation.type.model !== '' && !nonRotation.disable_items && (
                <Fragment>
                  <Label className={`${lang !== 'ar' ? 'pl-2' : 'text-left pr-2'}`}>
                    <Trans i18nKey={'course_name_small'}></Trans>
                  </Label>
                  <Select
                    name="_course_id"
                    value={nonRotation.type._course_id}
                    className={'styled-select'}
                    onChange={(e) =>
                      setNonRotation({
                        type: 'ON_CHANGE',
                        payload: e.target.value,
                        name: e.target.name,
                      })
                    }
                  >
                    {nonRotation.add_courses['course']?.['status'] ? (
                      <Fragment>
                        <Fragment>
                          <option key="null" value="">
                            {t('program_calendar.select_course')}
                          </option>
                        </Fragment>
                        {nonRotation.add_courses['course']['data']
                          .filter((item) => item.course_type === nonRotation.type.model)
                          .map((item, i) => (
                            <option key={i} value={item._id}>
                              {item.courses_name}
                            </option>
                          ))}
                      </Fragment>
                    ) : (
                      <Fragment>
                        <option value="">
                          {t('program_calendar.no_course_in')}
                          {nonRotation.type.model}
                        </option>
                      </Fragment>
                    )}
                  </Select>
                </Fragment>
              )}

              {nonRotation.type.model !== '' && nonRotation.disable_items && (
                <>
                  <Label className="pl-2">
                    <Trans i18nKey={'course_name_small'}></Trans>
                  </Label>
                  <Select disabled={nonRotation.disable_items}>
                    <option value="">{CourseTitle}</option>
                  </Select>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
}

CourseTitle.propTypes = {
  data: PropTypes.object,
  programId: PropTypes.string,
};
