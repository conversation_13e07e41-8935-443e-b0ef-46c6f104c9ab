export const initialState_curriculum = {
  curriculumYears: [],
  curriculumLevels: [],
  preparingData: [],
};

const initialCallModify = (state, years, levels, preparingData) => {
  return {
    ...state,
    curriculumYears: years,
    curriculumLevels: levels,
    preparingData: preparingData,
  };
};

const setTermsNew = (state, payload, terms) => {
  const copyData = [...state.preparingData];
  const findIndexData = copyData.findIndex((item) => item.batch === terms);
  copyData[findIndexData].type = payload;
  copyData[findIndexData].curriculum =
    payload === 'level' ? state.curriculumLevels : payload === 'year' ? state.curriculumYears : [];
  return {
    ...state,
    copyData,
  };
};

const setTermsValueNew = (state, terms, payload, name) => {
  const copyData = [...state.preparingData];
  const findIndexData = copyData.findIndex((item) => item.batch === terms);
  const copyCurriculum = [...copyData[findIndexData].curriculum];
  copyCurriculum[name].version = payload;
  return {
    ...state,
    copyData,
  };
};

export const curriculum_reducer = (state, action) => {
  const { type, payload, name, curriculum_years, curriculum_levels, terms, preparingData } = action;
  switch (type) {
    case 'INITIAL_LOAD_MODIFY':
      return initialCallModify(state, curriculum_years, curriculum_levels, preparingData);
    case 'CURRICULUM_CHANGE_MODIFY_NEW':
      return setTermsNew(state, payload, terms);
    case 'VALUE_CHANGE_MODIFY_NEW':
      return setTermsValueNew(state, terms, payload, name);
    default:
      return {
        ...state,
      };
  }
};
