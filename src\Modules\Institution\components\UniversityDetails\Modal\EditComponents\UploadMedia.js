import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Map } from 'immutable';
import { Tooltip, OverlayTrigger } from 'react-bootstrap';
import { t } from 'i18next';

import Attachment from 'Assets/pin.svg';
import deleteIcon from 'Assets/delete.svg';
function UploadMedia({ media, setMediaFiles, setData, name }) {
  const checkValidation = (input) => {
    const fileSize = input.size / 1024 / 1024; // in MiB
    const fileType = ['image/jpeg', 'image/jpg', 'image/png', 'video/mp4', 'audio/mpeg'];
    if (!fileType.includes(input.type)) {
      setData(Map({ message: t(`add_colleges.about_media_format`) }));
      return;
    }
    if (fileSize > 250) {
      setData(Map({ message: t(`add_colleges.about_media_size`) }));
      return;
    }
    setMediaFiles(input);
  };
  return (
    <>
      <div className="col-md-12 p-0">
        <div className="float-left d-flex pt-3">
          <div className="f-18 bold">
            <Trans i18nKey={'add_colleges.Upload_Media'}></Trans>
          </div>
          <div className="ml-2">
            <Trans i18nKey={'add_colleges.Accepted_formats'}></Trans>
          </div>
          <OverlayTrigger
            placement="bottom"
            overlay={
              <Tooltip id="button-tooltip-2" className="tooltip-alert">
                jpg, png, jpeg, mp3, mp4
              </Tooltip>
            }
          >
            <i className="ml-2 mt-1 fa fa-exclamation-circle" aria-hidden="true"></i>
          </OverlayTrigger>
        </div>
        <label htmlFor="logo-upload" className="float-right">
          <div className=" pt-3">
            <div className="text-blue remove_hover mb-0">
              {!media ? (
                <>
                  <i className="fa fa-plus pr-2" aria-hidden="true"></i>
                  <Trans i18nKey={'global_configuration.add_media'}></Trans>
                </>
              ) : (
                <div>
                  <Trans i18nKey={'edit'}></Trans>
                </div>
              )}

              <input
                style={{ display: 'none' }}
                id="logo-upload"
                type="file"
                accept=".jpg,.jpeg,.png,.mp4,.mp3"
                onChange={(e) => checkValidation(...e.target.files)}
              />
            </div>
          </div>
        </label>
        <div className="clearfix"></div>
      </div>
      {media && (
        <div className="model-border br-10 p-3">
          <div className="float-left">
            {' '}
            <p className="mb-0 break-word">
              <img src={Attachment} alt="attachment" />{' '}
              {typeof media === 'string' ? name.split('/').pop() : media.name}
            </p>
          </div>
          <div className="float-right" onClick={() => setMediaFiles('')}>
            <img className="mr-2" src={deleteIcon} alt="delete" />
          </div>
          <div className="clearfix"></div>
        </div>
      )}
    </>
  );
}

UploadMedia.propTypes = {
  media: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  setData: PropTypes.func,
  setMediaFiles: PropTypes.func,
  name: PropTypes.string,
};
export default UploadMedia;
