import React, { useState } from 'react';

import { List, Map } from 'immutable';

import {
  But<PERSON>,
  Checkbox,
  Di<PERSON>r,
  Drawer,
  FormControl,
  FormControlLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
} from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

import { useDispatch } from 'react-redux';
import { setData } from '_reduxapi/qapc/actions';
import LocalStorageService from 'LocalStorageService';

//----------------------------------UI Utils Start--------------------------------------------------
const drawerPaperSx = {
  '& .MuiDrawer-paper': {
    width: '58vw',
    boxShadow: '0',
    overflow: 'hidden !important',
    color: '#4B5563',
  },
};
const radioIconStyle = {
  '& .MuiSvgIcon-root': {
    fontSize: '16px',
  },
};
const MenuProps = {
  PaperProps: {
    sx: {
      maxHeight: 200,
      '&::-webkit-scrollbar': {
        width: '8px',
        height: '10px',
      },
      '&::-webkit-scrollbar-track': {
        background: '#f1f1f1',
        borderRadius: '15px',
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#c8c8c8',
        borderRadius: '15px',
      },
    },
  },
};
const menuItemStyles = {
  fontSize: '12px',
  '&.MuiMenuItem-root': {
    minHeight: 'auto',
  },
};
const checkBoxLarge = {
  '& .MuiSvgIcon-root': { fontSize: 19 },
};
const placeholderSX = {
  '& input::placeholder': {
    fontSize: '0.75em',
  },
};
const userTextFieldSX = {
  width: '35px',
  '& .MuiInputBase-input': {
    padding: '3px 5px',
    textAlign: 'center',
  },
};
const selectArrowIconSx = { fontSize: '18px', color: '#9CA3AF !important', marginRight: '10px' };
const drawerStyle = { overflowY: 'auto' };
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const options = List([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
const renderSelectValue = (selected, placeholder) => {
  return selected ? selected : <div className="f-12 text-divider-color">{placeholder}</div>;
};
const renderMultiSelect = (selected, placeholder) => {
  return selected.length ? (
    selected.join(',')
  ) : (
    <span className="f-12 text-divider-color">{placeholder}</span>
  );
};
const validateForm = (formData, dispatch) => {
  const levelName = formData.get('name', '');
  const category = formData.get('category', 'entire');
  const minimum_user = formData.get('minimum_user', 0);
  const requireMinium = formData.get('requireMinium', false);
  const specificSections = formData.get('specificSections', List());
  let message = '';
  if (!levelName) {
    message = 'Please enter the approval name';
  } else if (!(formData.get('turnAroundTime', 0) > 0)) {
    message = 'Select the TAT (Turn Around Time)';
  } else if (requireMinium && !(minimum_user > 0)) {
    message = 'Minimum users should be greater than 0';
  } else if (category === 'specific' && specificSections.size === 0) {
    message = 'Select the column: specificSections.';
  }
  if (message) {
    dispatch(setData(Map({ message })));
    return false;
  }
  return true;
};
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------
const useFormDataHandlers = ({ approvalHook, drawerStore, approvalLevelState, ref }) => {
  const dispatch = useDispatch();
  const { handleSave } = approvalHook;
  const { isDrawerOpen, toggleDrawer } = drawerStore;

  const approvalIndex = isDrawerOpen === 0 ? 0 : isDrawerOpen - 1;
  const getApprovalLevel = approvalLevelState.get(approvalIndex, Map());

  const [formData, setFormData] = useState(getApprovalLevel);

  const handleFormDataChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevFormData) => prevFormData.set(name, value));
  };
  const handleChangeRadio = (e, aspect) => {
    const { name } = e.target;
    if (aspect === 'Require') {
      setFormData((prev) =>
        prev
          .set('requireAll', false)
          .set('requireMinium', false)
          .set('minimum_user', '')
          .set(name, true)
      );
    } else {
      setFormData((prev) => prev.set('category', name).set('specificSections', List()));
    }
  };
  const handleCheckboxChange = (event) => {
    const { name, checked } = event.target;
    setFormData((prev) => prev.set(name, checked));
  };
  const handleSpecificSections = (value) => {
    setFormData((prev) => prev.set('specificSections', List(value)).set('category', 'specific'));
  };

  const handleFormDataSave = (formData, approvalIndex) => () => {
    if (validateForm(formData, dispatch)) {
      handleSave(formData, approvalIndex);
      toggleDrawer(null)();
      ref.current.boolean = { isEdited: true };
    }
  };

  return {
    formData,
    handleFormDataChange,
    handleChangeRadio,
    handleCheckboxChange,
    handleSpecificSections,
    handleFormDataSave,
  };
};
//----------------------------------custom hooks end------------------------------------------------
//----------------------------------componentStart--------------------------------------------------
const MuiSelect = ({ name = '', value, placeholder, options, onChange, disabled }) => {
  return (
    <FormControl fullWidth size="small">
      <Select
        disabled={disabled}
        displayEmpty
        value={value}
        name={name}
        className="f-14"
        onChange={onChange}
        MenuProps={MenuProps}
        IconComponent={(props) => <KeyboardArrowDownIcon {...props} sx={selectArrowIconSx} />}
        renderValue={(selected) => renderSelectValue(selected, placeholder)}
      >
        <MenuItem value="">select</MenuItem>
        {options.map((option) => (
          <MenuItem key={option} value={option} sx={menuItemStyles} className="flex-wrap">
            {option}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};
export const MultiSelect = ({ options, size = 'small', values, onChangeSection, placeholder }) => {
  const handleChange = (event) => {
    onChangeSection(event.target.value);
  };

  return (
    <FormControl fullWidth size={size}>
      <Select
        multiple
        displayEmpty
        value={values}
        className="f-14"
        MenuProps={MenuProps}
        onChange={handleChange}
        IconComponent={(props) => <KeyboardArrowDownIcon {...props} sx={selectArrowIconSx} />}
        renderValue={(selected) => renderMultiSelect(selected, placeholder)}
      >
        {options.map((option) => (
          <MenuItem key={option} value={option} sx={menuItemStyles}>
            {option}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

const RenderCheckbox = ({ name, checked, label, handleCheckboxChange, isEditable }) => (
  <label className="d-inline-flex align-items-center m-0 width-content-fit">
    <Checkbox
      className="p-0 mr-2"
      disableRipple
      sx={checkBoxLarge}
      disabled={isEditable}
      name={name}
      checked={checked}
      onChange={handleCheckboxChange}
    />
    <span className="cursor-pointer">{label}</span>
  </label>
);
const CheckboxContainer = ({ formDataHandlers, isDrawerOpen, isEditable }) => {
  const { formData, handleCheckboxChange } = formDataHandlers;
  const escalateRequest = formData.get('escalateRequest', false);
  const allowToSkipping = formData.get('allowToSkipping', false);
  const allowToOverwrite = formData.get('allowToOverwrite', false);

  const checkboxConfigs = [
    {
      name: 'escalateRequest',
      checked: escalateRequest,
      label: 'Escalate to next level, if no response',
    },
    {
      name: 'allowToSkipping',
      checked: allowToSkipping,
      label: 'Allow skipping all previous levels of Approval',
      condition: isDrawerOpen !== 1,
    },
    {
      name: 'allowToOverwrite',
      checked: allowToOverwrite,
      label: 'Allow to overwrite all previous level rejected applications',
      condition: isDrawerOpen !== 1,
    },
  ];

  return (
    <div className="d-flex flex-column gap-8">
      {checkboxConfigs
        .filter((config) => config.condition !== false)
        .map((config, index) => (
          <RenderCheckbox
            isEditable={isEditable}
            key={index}
            name={config.name}
            checked={config.checked}
            label={config.label}
            handleCheckboxChange={handleCheckboxChange}
          />
        ))}
    </div>
  );
};
const ApproverLevelName = ({ formDataHandlers, isEditable }) => {
  const { formData, handleFormDataChange } = formDataHandlers;
  const levelName = formData.get('name', '');

  return (
    <>
      <label className="mb-1 f-12">Approver Level Name</label>
      <TextField
        fullWidth
        size="small"
        name="name"
        disabled={isEditable}
        value={levelName}
        placeholder="Enter name here"
        sx={placeholderSX}
        onChange={handleFormDataChange}
      />
    </>
  );
};
const TATSelect = ({ formDataHandlers, isEditable }) => {
  const { formData, handleFormDataChange } = formDataHandlers;
  const turnAroundTime = formData.get('turnAroundTime', 0);

  return (
    <div className="mb-3">
      <label className="mb-1 f-12">TAT (Turn Around Time)</label>
      <MuiSelect
        disabled={isEditable}
        options={options}
        name="turnAroundTime"
        value={turnAroundTime}
        placeholder="Select Number"
        onChange={handleFormDataChange}
      />
    </div>
  );
};
const ApprovalConfiguration = ({ formDataHandlers, isEditable }) => {
  const { formData, handleFormDataChange, handleChangeRadio } = formDataHandlers;
  const requireAll = formData.get('requireAll', true);

  return (
    <div className="my-3">
      <label className="mb-1 f-12">Approval Configuration</label>
      <div className="d-flex flex-wrap align-items-center gap-10">
        <div>
          <RadioGroup
            row
            className="d-flex flex-wrap gap-8"
            value={requireAll ? 'requireAll' : 'requireMinium'}
            onChange={(e) => handleChangeRadio(e, 'Require')}
          >
            <FormControlLabel
              value="requireAll"
              control={<Radio disableRipple sx={radioIconStyle} className="p-0 mr-2" />}
              label="Approval Required From All User"
              className="m-0"
              name="requireAll"
              disabled={isEditable}
            />
            <FormControlLabel
              value="requireMinium"
              control={<Radio disableRipple sx={radioIconStyle} className="p-0 mr-2" />}
              label="Approval Required Minimum"
              className="m-0"
              name="requireMinium"
              disabled={isEditable}
            />
          </RadioGroup>
        </div>
        <MinimumUser
          formData={formData}
          handleFormDataChange={handleFormDataChange}
          isEditable={isEditable}
        />
      </div>
    </div>
  );
};
const MinimumUser = ({ formData, handleFormDataChange, isEditable }) => {
  const requireAll = formData.get('requireAll', true);
  const minimum_user = formData.get('minimum_user', '');

  return (
    <div className="d-flex gap-10 align-items-center">
      <TextField
        type="number"
        size="small"
        placeholder="0"
        name="minimum_user"
        value={minimum_user}
        sx={userTextFieldSX}
        disabled={requireAll || isEditable}
        onChange={handleFormDataChange}
        className={`approval-user-input ${requireAll ? 'disabled' : ''}`}
      />
      <span>Users</span>
    </div>
  );
};

const AdvanceSetting = ({ children, settings, condition }) => {
  return settings === condition ? children : <></>;
};

const AdvanceSettings = ({ formDataHandlers, approverLevel, isEditable }) => {
  const { formData, handleChangeRadio, handleSpecificSections } = formDataHandlers;
  const category = formData.get('category', 'entire');
  const categoryFormType = approverLevel.get('categoryFormType', '');
  const formType = approverLevel.get('formType', '');
  const sectionsTemplateWise = LocalStorageService.getCustomToken('sections', true) || [];
  const sectionsFormWise = approverLevel
    .get('sectionAttachments', List())
    .map((section) => section.get('sectionName', ''));
  return (
    <>
      <AdvanceSetting condition="template" settings={categoryFormType}>
        {/* <label className="mb-2 f-12 bold text-primary-font">Advance Settings</label> */}
        <div>
          <label className="mb-1 f-10">Category</label>
          <RadioGroup
            disabled={isEditable}
            className="d-flex flex-column gap-5"
            onChange={handleChangeRadio}
            value={category}
          >
            <FormControlLabel
              value="entire"
              control={<Radio disableRipple sx={radioIconStyle} className="p-0 mr-2" />}
              label="Entire Forum"
              className="m-0"
              name="entire"
            />
            {/* <FormControlLabel
              value="specific"
              control={<Radio disableRipple sx={radioIconStyle} className="p-0 mr-2" />}
              label="Specific Sections"
              className="m-0"
              name="specific"
            /> */}
          </RadioGroup>
          <SpecificSections
            isEditable={isEditable}
            formData={formData}
            onChange={handleSpecificSections}
            sections={sectionsTemplateWise}
          />
        </div>
      </AdvanceSetting>
      <AdvanceSetting condition="form" settings={categoryFormType}>
        <AdvanceSetting condition="section" settings={formType}>
          <div className="mb-2 f-12 bold text-primary-font">Advance Settings</div>
          <div className="mb-1 f-10">Sections</div>
          <SpecificSections
            formData={formData}
            onChange={handleSpecificSections}
            sections={sectionsFormWise}
            approverLevel={approverLevel}
            isEditable={isEditable}
          />
        </AdvanceSetting>
      </AdvanceSetting>
    </>
  );
};
const SpecificSections = ({ formData, onChange, sections, approverLevel = Map(), isEditable }) => {
  const category = formData.get('category', 'entire');
  const formType = approverLevel.get('formType', 'complete');
  const specificSections = formData.get('specificSections', List());
  if (category === 'specific' || formType === 'section') {
    return (
      <div className="my-1">
        <MultiSelect
          disabled={isEditable}
          options={sections}
          placeholder="Select Column"
          values={specificSections.toArray()}
          onChangeSection={onChange}
        />
      </div>
    );
  }
  return <></>;
};

const DrawerTitle = () => {
  return (
    <div className="px-4 pt-4">
      <h4 className="font-weight-normal">Tell us about your Forms</h4>
    </div>
  );
};
const DrawerAction = ({ formDataHandlers, approvalIndex, toggleDrawer, isEditable }) => {
  const { formData, handleFormDataSave } = formDataHandlers;
  return (
    <div className="px-4 pb-4 d-flex justify-content-end gap-8 mt-auto">
      <Button variant="outlined" className="px-4" onClick={toggleDrawer(null)}>
        <span className="px-2">cancel</span>
      </Button>
      <Button
        variant="contained"
        className="px-4"
        disabled={isEditable}
        onClick={handleFormDataSave(formData, approvalIndex)}
      >
        <span className="px-2">save</span>
      </Button>
    </div>
  );
};
const DrawerContent = ({ children }) => {
  return (
    <div className="px-4 pb-4 m-2 g-config-scrollbar" style={drawerStyle}>
      {children}
    </div>
  );
};
//----------------------------------componentEnd----------------------------------------------------

const StepThreeDrawer = React.forwardRef(
  ({ drawerStore, approvalLevelState, approvalHook, isEditable }, ref) => {
    const { isDrawerOpen, toggleDrawer } = drawerStore;
    const approvalIndex = isDrawerOpen === 0 ? 0 : isDrawerOpen - 1;
    const formDataHandlers = useFormDataHandlers({
      approvalHook,
      drawerStore,
      approvalLevelState,
      ref,
    });
    const { approverLevel } = approvalHook;
    return (
      <Drawer anchor="right" open={isDrawerOpen} sx={drawerPaperSx} onClose={toggleDrawer(null)}>
        <DrawerTitle />
        <DrawerContent>
          <ApproverLevelName formDataHandlers={formDataHandlers} isEditable={isEditable} />
          <ApprovalConfiguration formDataHandlers={formDataHandlers} isEditable={isEditable} />
          <TATSelect formDataHandlers={formDataHandlers} isEditable={isEditable} />
          <CheckboxContainer
            formDataHandlers={formDataHandlers}
            isDrawerOpen={isDrawerOpen}
            isEditable={isEditable}
          />
          <Divider className="my-3" />
          <AdvanceSettings
            formDataHandlers={formDataHandlers}
            approverLevel={approverLevel}
            isEditable={isEditable}
          />
        </DrawerContent>
        <DrawerAction
          isEditable={isEditable}
          toggleDrawer={toggleDrawer}
          approvalIndex={approvalIndex}
          formDataHandlers={formDataHandlers}
        />
      </Drawer>
    );
  }
);

export default StepThreeDrawer;
