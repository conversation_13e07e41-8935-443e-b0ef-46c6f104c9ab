//-------------------------------html to json conversion--------------------------------------------
function getSpecificationApproval() {
  const jsonData = {};

  $('#specificationApproval tbody tr').each(function () {
    const cells = $(this).find('td');
    const col1 = cells.eq(0).text().trim();
    const col2 = cells.eq(1).text().trim();
    jsonData[col1] = col2;
  });

  return jsonData;
}
function getAssessmentCourseQuality() {
  const jsonData = [];

  $('#assessmentCourseQuality tbody tr').each(function () {
    const cells = $(this).find('td');
    const area = cells.eq(0).text().trim();
    const assessor = cells.eq(1).text().trim();
    const methods = cells.eq(2).text().trim();
    const data = {
      assessmentAreasOrIssues: area,
      assessor: assessor,
      assessmentMethods: methods,
    };
    jsonData.push(data);
  });

  return jsonData;
}
function getRequiredFacilitiesEquipment() {
  const jsonData = {};

  $('#requiredFacilitiesEquipment tbody tr').each(function () {
    const cells = $(this).find('td');
    const col1 = cells.eq(0).text().trim();
    const col2 = cells.eq(1).text().trim();
    jsonData[col1] = col2;
  });

  return jsonData;
}
function getLearningResourceTable() {
  const jsonData = {};

  $('#learningResourceTable tbody tr').each(function () {
    const cells = $(this).find('td');
    const col1 = cells.eq(0).text().trim();
    const col2 = cells.eq(1).text().trim();
    jsonData[col1] = col2;
  });

  return jsonData;
}
function getStudentAssessmentActivities() {
  const jsonData = [];

  $('#studentAssessmentActivities tbody tr').each(function () {
    const cells = $(this).find('td');
    const no = cells.eq(0).text().trim();
    const assessmentActivities = cells.eq(1).text().trim();
    const assessmentTiming = cells.eq(2).text().trim();
    const totalPercent = cells.eq(3).text().trim();
    const data = {
      no: no,
      assessmentActivities: assessmentActivities,
      assessmentTiming: assessmentTiming,
      totalPercent: totalPercent,
    };
    jsonData.push(data);
  });

  return jsonData;
}
function getCourseContent() {
  const jsonData = [];

  $('#courseContent tbody tr').each(function () {
    const cells = $(this).find('td');
    const No = cells.eq(0).text().trim();
    const topic = cells.eq(1).text().trim();
    const contactHours = cells.eq(2).text().trim();
    const data = {
      No: No,
      topic: topic,
      contactHours: contactHours,
    };
    jsonData.push(data);
  });
  return jsonData;
}
function getCloTeachingAndAssessment() {
  const jsonData = [];
  $('#cloTeachingAndAssessment tbody tr').each(function () {
    const cells = $(this).find('td');
    const code = cells.eq(0).text().trim(); // Extracting code without decimals
    const courseLearningOutcomes = cells.eq(1).text().trim();
    const codeOfCLO = cells.eq(2).text().trim();
    const teachingStrategies = cells.eq(3).text().trim();
    const assessmentMethods = cells.eq(4).text().trim();
    const data = {
      Code: code,
      courseLearningOutcomes: courseLearningOutcomes,
      codeOfCLO: codeOfCLO,
      teachingStrategies: teachingStrategies,
      assessmentMethods: assessmentMethods,
    };
    jsonData.push(data);
  });
  return jsonData;
}
function getContactHours() {
  const jsonData = [];
  const contactHoursTable = $('#contactHoursTable tbody tr');
  contactHoursTable.each(function (index) {
    if (index < contactHoursTable.length - 1) {
      const cells = $(this).find('td');
      const activity = cells.eq(1).text().trim();
      const contactHours = cells.eq(2).text().trim();
      const data = {
        activity: activity,
        contactHours: contactHours,
      };
      jsonData.push(data);
    }
  });
  return jsonData;
}
function constructTeachingMode() {
  const jsonData = [];
  $('#teachingModeTable tbody tr').each(function () {
    const row = {};
    row.No = $(this).find('td:eq(0)').text().trim();
    row.modeOfInstruction = $(this).find('td:eq(1)').text().trim();
    row.contactHours = $(this).find('td:eq(2)').text().trim();
    row.Percentage = $(this).find('td:eq(3)').text().trim();
    jsonData.push(row);
  });
  return jsonData;
}
function getCourseType() {
  const courseType = {};
  $("#courseType thead input[type='checkbox']").each(function () {
    if ($(this).is(':checked')) {
      courseType.a = $(this).val();
      return false; // Exit loop if a checkbox is checked
    }
  });

  // Check checkboxes in tbody
  $("#courseType tbody input[type='checkbox']").each(function () {
    if ($(this).is(':checked')) {
      courseType.b = $(this).val();
      return false; // Exit loop if a checkbox is checked
    }
  });
  return courseType;
}
function dataRetrieve() {
  const json = {
    title: $('#title').html().trim(),
    courseCode: $('#courseCode').html().trim(),
    program: $('#program').html().trim(),
    department: $('#department').html().trim(),
    college: $('#college').html().trim(),
    institution: $('#institution').html().trim(),
    version: $('#version').html().trim(),
    revisionDate: $('#revisionDate').html().trim(),
    creditHours: $('#creditHours').html().trim(),
    courseType: getCourseType(),
    generalDescription: $('#generalDescriptionDetail').html().trim(),
    levelYearDetail: $('#levelYearDetail').html().trim(),
    generalDescriptionDetail: $('#generalDescriptionDetail').html().trim(),
    preRequirements: $('#preRequirements').html().trim(),
    coRequisites: $('#coRequisites').html().trim(),
    courseMainObjective: $('#courseMainObjective').html().trim(),
    teachingModeTable: constructTeachingMode(),
    contactHours: getContactHours(),
    cloTeachingAndAssessment: getCloTeachingAndAssessment(),
    courseContent: getCourseContent(),
    studentAssessmentActivities: getStudentAssessmentActivities(),
    learningResourceFacilities: getLearningResourceTable(),
    requiredFacilitiesEquipment: getRequiredFacilitiesEquipment(),
    assessmentCourseQuality: getAssessmentCourseQuality(),
    specificationApproval: getSpecificationApproval(),
  };
  return json;
}
$('#deserializeBtn').on('click', dataRetrieve);
// document.getElementById('addRowBtn').addEventListener('click', function () {});

function TeachingAddRow() {
  var table = document.getElementById('teachingModeTable').getElementsByTagName('tbody')[0];
  var newRow = table.insertRow(table.rows.length);
  var serialNumberCell = newRow.insertCell(0);
  serialNumberCell.textContent = table.rows.length;
  for (var i = 1; i < 4; i++) {
    var newCell = newRow.insertCell(i);
    newCell.contentEditable = true;
  }
}
$('#addRowBtn').on('click', TeachingAddRow);

function contactHours() {
  var table = document.getElementById('contactHoursTable').getElementsByTagName('tbody')[0];
  var newRow = document.createElement('tr');
  var serialNumberCell = document.createElement('td');
  serialNumberCell.textContent = table.rows.length;
  serialNumberCell.classList.add('text-center', 'py-5');
  newRow.appendChild(serialNumberCell);

  for (var i = 0; i < 2; i++) {
    var newCell = document.createElement('td');
    newCell.contentEditable = true;
    newRow.appendChild(newCell);
  }

  var lastRow = table.querySelector('.outcome-row');
  table.insertBefore(newRow, lastRow);
}
$('#addRowContactHoursBtn').on('click', contactHours);

function addCourseContentRow() {
  var table = document.getElementById('courseContent').getElementsByTagName('tbody')[0];

  var newRow = document.createElement('tr');

  var serialNumberCell = document.createElement('td');
  serialNumberCell.textContent = table.rows.length;
  serialNumberCell.classList.add('text-center', 'py-5');
  newRow.appendChild(serialNumberCell);

  for (var i = 0; i < 2; i++) {
    var newCell = document.createElement('td');
    newCell.contentEditable = true;
    newRow.appendChild(newCell);
  }

  var lastRow = table.querySelector('.outcome-row');
  table.insertBefore(newRow, lastRow);
}
$('#addRowCourseContentBtn').on('click', addCourseContentRow);

function AssessmentActivities() {
  var table = document
    .getElementById('studentAssessmentActivities')
    .getElementsByTagName('tbody')[0];
  var newRow = table.insertRow(table.rows.length);
  var serialNumberCell = newRow.insertCell(0);
  serialNumberCell.textContent = table.rows.length;
  for (var i = 1; i < 4; i++) {
    var newCell = newRow.insertCell(i);
    newCell.contentEditable = true;
  }
}
$('#addRowAssessmentActivitiesBtn').on('click', AssessmentActivities);

function learningResource() {
  var table = document.getElementById('learningResourceTable').getElementsByTagName('tbody')[0];

  var newRow = table.insertRow(table.rows.length);

  var essentialReferencesCell = newRow.insertCell(0);
  essentialReferencesCell.classList.add('py-5', 'bg-purple-dark', 'text-center');
  essentialReferencesCell.contentEditable = true;

  var contentCell = newRow.insertCell(1);
  contentCell.classList.add('w-75');
  contentCell.contentEditable = true;
}
$('#addRowLearningResourcesBtn').on('click', learningResource);

function RowRequiredFacilities() {
  var table = document
    .getElementById('requiredFacilitiesEquipment')
    .getElementsByTagName('tbody')[0];
  var newRow = table.insertRow(table.rows.length);
  var itemsCell = newRow.insertCell(0);
  itemsCell.classList.add('text-center');
  itemsCell.contentEditable = true;
  var resourcesCell = newRow.insertCell(1);
  resourcesCell.classList.add('text-center');
  resourcesCell.contentEditable = true;
}
$('#addRowRequiredFacilitiesBtn').on('click', RowRequiredFacilities);

function assessmentCourse() {
  var table = document.getElementById('assessmentCourseQuality').getElementsByTagName('tbody')[0];
  var newRow = table.insertRow(table.rows.length);
  var areasIssuesCell = newRow.insertCell(0);
  areasIssuesCell.classList.add('py-5');
  areasIssuesCell.contentEditable = true;
  for (var i = 1; i < 3; i++) {
    var newCell = newRow.insertCell(i);
    newCell.contentEditable = true;
  }
}
$('#addRowCourseQualityBtn').on('click', assessmentCourse);

function specificationApproval() {
  var table = document.getElementById('specificationApproval').getElementsByTagName('tbody')[0];

  var newRow = table.insertRow(table.rows.length);

  var essentialReferencesCell = newRow.insertCell(0);
  essentialReferencesCell.classList.add('py-5', 'bg-purple-dark', 'text-center');
  essentialReferencesCell.contentEditable = true;

  var contentCell = newRow.insertCell(1);
  contentCell.classList.add('w-75');
  contentCell.contentEditable = true;
}
$('#addSpecificationApprovalBtn').on('click', specificationApproval);

function CourseLearningOutcomes(event) {
  var table = document.getElementById('cloTeachingAndAssessment').getElementsByTagName('tbody')[0];
  var newRow = table.insertRow(table.rows.length);
  var codeCell = newRow.insertCell(0);
  codeCell.classList.add('text-center');
  codeCell.contentEditable = true;
  var cloCell = newRow.insertCell(1);
  cloCell.contentEditable = true;
  var cloCodeCell = newRow.insertCell(2);
  cloCodeCell.contentEditable = true;
  var teachingStrategiesCell = newRow.insertCell(3);
  teachingStrategiesCell.contentEditable = true;
  var assessmentMethodsCell = newRow.insertCell(4);
  assessmentMethodsCell.contentEditable = true;
  if (event.target.id === 'addRowCourseLearningHeadingsBtn') {
    newRow.classList.add('outcome-row');
    codeCell.rowSpan = 1;
    cloCell.colSpan = 4;
    cloCodeCell.style.display = 'none';
    teachingStrategiesCell.style.display = 'none';
    assessmentMethodsCell.style.display = 'none';
  }
}

$('#addRowCourseLearningHeadingsBtn').on('click', CourseLearningOutcomes);
$('#addRowCourseLearningRowBtn').on('click', CourseLearningOutcomes);

//-------------------------------html to json conversion--------------------------------------------

//-------------------------------json to html conversion--------------------------------------------
function populateSpanData(data) {
  $('#title').html(data.title);
  $('#courseCode').html(data.courseCode);
  $('#program').html(data.program);
  $('#department').html(data.department);
  $('#college').html(data.college);
  $('#institution').html(data.institution);
  $('#revisionDate').html(data.revisionDate);
  $('#creditHours').html(data.creditHours);
  $('#version').html(data.version);
  for (var key in data.courseType) {
    $(`input[value~=${data.courseType[key]}]`).prop('checked', true);
  }
  $('#levelYearDetail').html(data.levelYearDetail);
  $('#generalDescriptionDetail').html(data.generalDescriptionDetail);
  $('#preRequirements').html(data.preRequirements);
  $('#coRequisites').html(data.coRequisites);
  $('#courseMainObjective').html(data.courseMainObjective);
}
function populateTeachingTable(data) {
  var tbody = $('#teachingModeTable tbody');
  tbody.empty();
  $.each(data, function (index, item) {
    var row = $('<tr>');
    // Add data to each cell of the row
    row.append($('<td>').text(item.No));
    row.append($('<td>').text(item.modeOfInstruction));
    row.append($('<td>').text(item.contactHours));
    row.append($('<td>').text(item.Percentage));
    // Append the row to the table body
    tbody.append(row);
  });
}
function populateContactHours(data) {
  var tbody = $('#contactHoursTable  tbody');
  if (data) {
    tbody.empty();
    $.each(data, function (index, item) {
      var row = $('<tr>');
      if (index === data.length - 1) row.addClass('outcome-row');
      // Add data to each cell of the row
      if (index !== data.length - 1)
        row.append(
          $('<td >')
            .text(index + 1)
            .addClass('py-5')
        );

      var col1 = $('<td>').text(item.activity);
      if (index === data.length - 1) col1.attr('colspan', 2).addClass('py-5');
      row.append(col1);
      row.append($('<td >').text(item.contactHours));
      // Append the row to the table body
      tbody.append(row);
    });
  }
}
function populateCourseContent(data) {
  var tbody = $('#courseContent tbody');
  if (data) {
    tbody.empty();
    $.each(data, function (index, item) {
      var row = $('<tr>');
      if (index === data.length - 1) row.addClass('outcome-row');
      // Add data to each cell of the row
      if (index !== data.length - 1)
        row.append(
          $('<td >')
            .text(index + 1)
            .addClass('py-5')
        );

      var col1 = $('<td>').text(item.topic);
      if (index === data.length - 1) col1.attr('colspan', 2).addClass('py-5').text(item.No);
      row.append(col1);
      row.append($('<td >').text(item.contactHours));
      // Append the row to the table body
      tbody.append(row);
    });
  }
}
function populateLearningOutcomeTable(data) {
  var tbody = $('#cloTeachingAndAssessment tbody');
  tbody.empty();
  $.each(data, function (index, item) {
    var row = $('<tr>');
    if (index % 4 === 0) row.addClass('outcome-row');
    row.append($('<td >').text(item.Code));
    var col1 = $('<td >').text(item.courseLearningOutcomes);
    if (index % 4 === 0) col1.attr('colspan', 4);
    row.append(col1);
    if (index % 4 !== 0) {
      row.append($('<td >').text(item.codeOfCLO));
      row.append($('<td >').text(item.teachingStrategies));
      row.append($('<td >').text(item.assessmentMethods));
    }
    // Append the row to the table body
    tbody.append(row);
  });
}
function populateStudentActivities(data) {
  var tbody = $('#studentAssessmentActivities tbody');
  tbody.empty();
  $.each(data, function (index, item) {
    var row = $('<tr>');
    row.append($('<td >').text(item.no));
    row.append($('<td >').text(item.assessmentActivities));
    row.append($('<td >').text(item.assessmentTiming));
    row.append($('<td >').text(item.totalPercent));
    // Append the row to the table body
    tbody.append(row);
  });
}
function populateReferenceAndLearning(data) {
  var tbody = $('#learningResourceTable tbody');
  tbody.empty();
  $.each(Object.entries(data), function (index, [key, value]) {
    var row = $('<tr>');
    row.append($('<td >').text(key)).addClass('py-5 bg-purple-dark text-center');
    row.append($('<td >').text(value).addClass('w-75'));
    // Append the row to the table body
    tbody.append(row);
  });
}
function populateRequiredFacilitiesEquipment(data) {
  var tbody = $('#requiredFacilitiesEquipment tbody');
  tbody.empty();
  $.each(Object.entries(data), function (index, [key, value]) {
    var row = $('<tr>');
    row.append($('<td >').html(key)).addClass('text-center');
    row.append($('<td >').text(value).addClass('text-center'));
    // Append the row to the table body
    tbody.append(row);
  });
}
function populateAssessmentOfCourseQuality(data) {
  var tbody = $('#assessmentCourseQuality tbody');
  tbody.empty();
  $.each(data, function (index, item) {
    var row = $('<tr>');
    // Add data to each cell of the row
    row.append($('<td>').text(item.assessmentAreasOrIssues).addClass('py-5'));
    row.append($('<td>').text(item.assessor));
    row.append($('<td>').text(item.assessmentMethods));
    // Append the row to the table body
    tbody.append(row);
  });
}
function populateSpecificationApproval(data) {
  var tbody = $('#specificationApproval tbody');
  tbody.empty();
  $.each(Object.entries(data), function (index, [key, value]) {
    var row = $('<tr>');
    row.append($('<td >').text(key).addClass('py-5 bg-purple-dark text-center'));
    row.append($('<td >').text(value).addClass('w-75 py-5')) /* .attr('contentEditable', true) */;
    // Append the row to the table body
    tbody.append(row);
  });
}

function freezingDomIfReadOnlyAccess() {
  $(':button').each(function () {
    this.style.display = 'none'; //hiding buttons
  });
  $('[contenteditable="true"]').each(function () {
    if (this.contentEditable) {
      this.contentEditable = false;
    }
  });
}

const updateData = (data) => {
  if (data.isReadOnly) freezingDomIfReadOnlyAccess(); //if user have only read access freeze the template
  populateSpanData(data);
  if (data.teachingModeTable) populateTeachingTable(data.teachingModeTable);
  if (data.contactHours) populateContactHours(data.contactHours);
  if (data.cloTeachingAndAssessment) populateLearningOutcomeTable(data.cloTeachingAndAssessment);
  if (data.courseContent) populateCourseContent(data.courseContent);
  if (data.studentAssessmentActivities) populateStudentActivities(data.studentAssessmentActivities);
  if (data.learningResourceFacilities)
    populateReferenceAndLearning(data.learningResourceFacilities);
  if (data.requiredFacilitiesEquipment)
    populateRequiredFacilitiesEquipment(data.requiredFacilitiesEquipment);
  if (data.assessmentCourseQuality) populateAssessmentOfCourseQuality(data.assessmentCourseQuality);
  if (data.specificationApproval) populateSpecificationApproval(data.specificationApproval);
};
window.addEventListener('message', function (event) {
  // Check origin to ensure message is from a trusted source
  const { values, from } = event.data;
  if (from === 'fromDC') updateData(values);
});
//sample data

// var data = {
//   title: 'rwe',
//   courseCode: 'fsd',
//   program: 'vxc',
//   department: 'erw',
//   college: 'try',
//   institution: 'yrt',
//   version: 'fg',
//   revisionDate: 'hfg',
//   creditHours: 'gdf',
//   courseType: {
//     a: 'Track',
//     b: 'Elective',
//   },
//   generalDescription: 'rwe',
//   levelYearDetail: 'ter',
//   generalDescriptionDetail: 'rwe',
//   preRequirements: 'qrew',
//   coRequisites: 'rew',
//   courseMainObjective: 'rew',
//   teachingModeTable: [
//     {
//       No: '1',
//       modeOfInstruction: 'Traditional classroom',
//       contactHours: 'rwe',
//       Percentage: 'rwe',
//     },
//     {
//       No: '2',
//       modeOfInstruction: 'E-learning',
//       contactHours: 'rwe',
//       Percentage: 'rwe',
//     },
//     {
//       No: '3',
//       modeOfInstruction:
//         'Hybrid\n                  \n                    Traditional classroom\n                    E-learning',
//       contactHours: 'rew',
//       Percentage: 'rwe',
//     },
//     {
//       No: '4',
//       modeOfInstruction: 'Distant Learning',
//       contactHours: 'rwe',
//       Percentage: 'rwe',
//     },
//   ],
//   contactHours: [
//     {
//       activity: 'Lectures',
//       contactHours: 'rw',
//     },
//     {
//       activity: 'Laboratory/Studio',
//       contactHours: 'ewr',
//     },
//     {
//       activity: 'Field',
//       contactHours: 'rew',
//     },
//     {
//       activity: 'Tutorial',
//       contactHours: 'rw',
//     },
//     {
//       activity: 'Others (specify)',
//       contactHours: 'rw',
//     },
//   ],
//   cloTeachingAndAssessment: [
//     {
//       Code: '1.0',
//       courseLearningOutcomes: 'Knowledge and understanding',
//       codeOfCLO: '',
//       teachingStrategies: '',
//       assessmentMethods: '',
//     },
//     {
//       Code: '1.1',
//       courseLearningOutcomes: '1',
//       codeOfCLO: 'g',
//       teachingStrategies: 'g',
//       assessmentMethods: 'g',
//     },
//     {
//       Code: '1.2',
//       courseLearningOutcomes: 'b',
//       codeOfCLO: 'n',
//       teachingStrategies: 'm',
//       assessmentMethods: ',',
//     },
//     {
//       Code: '1.3',
//       courseLearningOutcomes: 'j',
//       codeOfCLO: 'k',
//       teachingStrategies: 'k',
//       assessmentMethods: 'l',
//     },
//     {
//       Code: '2.0',
//       courseLearningOutcomes: 'Skills',
//       codeOfCLO: '',
//       teachingStrategies: '',
//       assessmentMethods: '',
//     },
//     {
//       Code: '2.1',
//       courseLearningOutcomes: 'i',
//       codeOfCLO: 'i',
//       teachingStrategies: 'i',
//       assessmentMethods: 'i',
//     },
//     {
//       Code: '2.2',
//       courseLearningOutcomes: 'i',
//       codeOfCLO: 'i',
//       teachingStrategies: 'i',
//       assessmentMethods: 'i',
//     },
//     {
//       Code: '2.3',
//       courseLearningOutcomes: 'i',
//       codeOfCLO: 'i',
//       teachingStrategies: 'i',
//       assessmentMethods: 'i',
//     },
//     {
//       Code: '3.0',
//       courseLearningOutcomes: 'Values, autonomy, and responsibility',
//       codeOfCLO: '',
//       teachingStrategies: '',
//       assessmentMethods: '',
//     },
//     {
//       Code: '3.1',
//       courseLearningOutcomes: 'h',
//       codeOfCLO: 'h',
//       teachingStrategies: 'h',
//       assessmentMethods: 'h',
//     },
//     {
//       Code: '3.2',
//       courseLearningOutcomes: 'h',
//       codeOfCLO: 'h',
//       teachingStrategies: 'h',
//       assessmentMethods: 'h',
//     },
//     {
//       Code: '3.3',
//       courseLearningOutcomes: 'h',
//       codeOfCLO: 'h',
//       teachingStrategies: 'h',
//       assessmentMethods: 'h',
//     },
//   ],
//   courseContent: [
//     {
//       No: '1',
//       topic: 'b',
//       contactHours: 'b',
//     },
//     {
//       No: '2',
//       topic: 'b',
//       contactHours: 'b',
//     },
//     {
//       No: '3',
//       topic: 'b',
//       contactHours: 'b',
//     },
//     {
//       No: 'Total',
//       topic: '',
//       contactHours: '',
//     },
//   ],
//   studentAssessmentActivities: [
//     {
//       no: '1',
//       assessmentActivities: 'i',
//       assessmentTiming: 'k',
//       totalPercent: 'k',
//     },
//     {
//       no: '2',
//       assessmentActivities: 'j',
//       assessmentTiming: 'b',
//       totalPercent: 'v',
//     },
//     {
//       no: '3',
//       assessmentActivities: 'v',
//       assessmentTiming: 'v',
//       totalPercent: 'v',
//     },
//     {
//       no: '4',
//       assessmentActivities: 'v',
//       assessmentTiming: 'v',
//       totalPercent: 'v',
//     },
//   ],
//   learningResourceFacilities: {
//     'Essential References': 'h',
//     'Supportive References': 'h',
//     'Electronic Materials': 'h',
//     'Other Learning Materials': 'h',
//   },
//   requiredFacilitiesEquipment: {
//     'facilities\n                (Classrooms, laboratories, exhibition rooms, simulation rooms, etc.)':
//       'j',
//     'Technology equipment\n                (projector, smart board, software)': 'j',
//     'Other equipment\n                (depending on the nature of the specialty)': 'j',
//   },
//   assessmentCourseQuality: [
//     {
//       assessmentAreasOrIssues: 'Effectiveness of teaching',
//       assessor: 'j',
//       assessmentMethods: 'j',
//     },
//     {
//       assessmentAreasOrIssues: 'Effectiveness of Students assessment',
//       assessor: 'j',
//       assessmentMethods: 'j',
//     },
//     {
//       assessmentAreasOrIssues: 'Quality of learning resources',
//       assessor: 'j',
//       assessmentMethods: 'j',
//     },
//     {
//       assessmentAreasOrIssues: 'The extent to which CLOs have been achieved',
//       assessor: 'j',
//       assessmentMethods: 'l',
//     },
//   ],
//   specificationApproval: {
//     'COUNCIL /COMMITTEE': 'jo',
//     'REFERENCE NO.': 'jo',
//     DATE: 'oj',
//   },
// };
