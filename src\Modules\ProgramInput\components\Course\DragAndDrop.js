import React, { useState } from 'react';
import { useDispatch } from 'react-redux';

import { fromJS, Map } from 'immutable';
import PropTypes, { oneOfType } from 'prop-types';

//ui-icons
import DeleteIcon from '@mui/icons-material/Delete';
import FileUploadIcon from '@mui/icons-material/FileUpload';

//redux
import { setData } from '_reduxapi/program_input/action';

//components
import ProgressBarModal from './ProgressBarModal';

//utils
import { getShortString } from 'Modules/Shared/v2/Configurations';
import useFileUpload from 'Hooks/useFileUpload';
// import { selectEditedSessionFlow } from '_reduxapi/program_input/selectors';

const DragAndDrop = ({ files, setFiles }) => {
  const dispatch = useDispatch();
  // const editedSessionFlow = useSelector(selectEditedSessionFlow());
  const [showProgress, setShowProgress] = useState(false);
  const [importPercent, setImportPercent] = useState(0);
  const [fileNameOn, setFileNameOn] = useState('');
  const { upload } = useFileUpload();
  let file = document.getElementById('upload-file-progress-drop');

  if (file) {
    file.onclick = function () {
      this.value = null;
    };
  }
  const handleDrop = (e) => {
    e.preventDefault();
    handleFileUpload(e.dataTransfer.files[0]);
  };
  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleFileUpload = async (file) => {
    const type = file?.type.split('/')[1];
    if (!['pdf'].includes(type)) {
      return dispatch(setData(Map({ message: 'Upload only pdf' })));
    }
    if (file.size > 5 * 1024 * 1024) {
      return dispatch(setData(Map({ message: 'File size exceeds the limit of 5MB' })));
    }
    if (files.length > 4) {
      return dispatch(setData(Map({ message: 'Max 5 is allowed' })));
    }
    try {
      setFileNameOn(file.name);
      setShowProgress(true);
      setImportPercent(0);
      const options = {
        onUploadProgress: ({ loaded, total }) => {
          const percent = Math.floor((loaded * 100) / total);
          setImportPercent(percent);
        },
      };
      const uploadedFilesData = await upload({
        files: [file],
        component: 'sessions',
        folderPathExtension: '',
        options,
      });
      const uploadedFile = fromJS(uploadedFilesData[0]);
      setFiles([
        ...files,
        uploadedFile
          .delete('signedUrl')
          .delete('size')
          .delete('type')
          .delete('fileName')
          .set('fileName', file.name)
          .set('url', uploadedFile.get('unSignedUrl')),
      ]);
      setShowProgress(false);
      setImportPercent(0);
    } catch (err) {
      setShowProgress(false);
      setImportPercent(0);
      dispatch(setData(Map({ message: err.message })));
    }
  };

  const deleteImagePdf = (files, index) => {
    const filterFiles = files.filter((_, i) => i !== index);
    setFiles(filterFiles);
  };
  return (
    <>
      <input
        type="file"
        style={{ display: 'none' }}
        id="file-upload"
        accept=".pdf"
        onChange={(e) => handleFileUpload(e.target.files[0])}
      />

      <label
        htmlFor="file-upload"
        onDragOver={handleDragOver}
        onDrop={showProgress ? (e) => e.preventDefault() : handleDrop}
        className="w-100"
      >
        <div className="Dashed-border-drawer w-100">
          <div className="d-flex align-items-center w-100">
            <div className="p-3 ">
              <div className="f-15 bold">Upload file (Max 5)</div>
              <div className="f-13 text-secondary">Pdf up to 5MB</div>
            </div>

            <div className="text-center p-3 upload-BgColor border-left ml-auto">
              <div className="cursor">
                <FileUploadIcon color="primary" />
                <p className="text-primary mb-0 f-15">Upload</p>
              </div>
            </div>
          </div>
        </div>
      </label>

      {showProgress && (
        <div className="time_line_dot_Margin_Active">
          <ProgressBarModal fileName={fileNameOn} importPercent={importPercent} />
        </div>
      )}
      <div className={'Attachments-Scroll mt-1 mb-3'}>
        {files
          .filter((i) => i)
          .map((eachFile, index) => {
            return (
              <div className="d-flex p-3 bold border rounded mt-2" key={index}>
                <span className="mr-auto">
                  {getShortString(eachFile.get('fileName', '').split('-').pop(), 50)}
                </span>
                <DeleteIcon
                  className="cursor-pointer ml-3"
                  onClick={() => {
                    deleteImagePdf(files, index);
                  }}
                  sx={{ color: '#147AFC' }}
                />
              </div>
            );
          })}
      </div>
    </>
  );
};
DragAndDrop.propTypes = {
  files: oneOfType([PropTypes.func, PropTypes.object]),
  setFiles: PropTypes.func,
};
DragAndDrop.displayName = 'DragAndDrop';
export default DragAndDrop;
