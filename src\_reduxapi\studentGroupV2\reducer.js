import { fromJS, List, Map } from 'immutable';
import * as actions from './action';
import { t } from 'i18next';
import { formatToTwoDigitString } from 'utils';

const initialState = fromJS({
  isLoading: false,
  message: '',
  userPrograms: [],
  programYearLevel: [],
  deliveryTypes: [],
  groupSettings: {},
  generatedGroupName: '',
  singleStudentDetails: {},
  totalCompletedSession: {},
  invalidEntries: [],
});

export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_USER_PROGRAMS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_USER_PROGRAMS_SUCCESS: {
      return state.set('isLoading', false).set('userPrograms', fromJS(action.data));
    }
    case actions.GET_USER_PROGRAMS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GET_PROGRAM_YEAR_LEVEL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_YEAR_LEVEL_SUCCESS: {
      return state.set('isLoading', false).set('programYearLevel', fromJS(action.data));
    }
    case actions.GET_PROGRAM_YEAR_LEVEL_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GET_DELIVERY_TYPES_REQUEST: {
      return state.set('isLoading', true).set('deliveryTypes', List());
    }
    case actions.GET_DELIVERY_TYPES_SUCCESS: {
      return state.set('isLoading', false).set('deliveryTypes', fromJS(action.data));
    }
    case actions.GET_DELIVERY_TYPES_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.ADD_GROUP_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_GROUP_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('message', t('saved_successfully'));
    }
    case actions.ADD_GROUP_SETTINGS_FAILURE: {
      const { response: { data: { data = [], message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .set('isLoading', false)
        .set('message', !data.length ? errorMessage : '')
        .set('invalidEntries', fromJS(data));
    }
    case actions.GET_GROUP_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_GROUP_SETTINGS_SUCCESS: {
      const { data } = action.data;

      const cacheData = fromJS(data).reduce((newObj, item) => {
        const selectedType = item.get('selectedType', '');
        const year = item.get('year', '');
        const level = item.get('level', '');
        const courseId = item.getIn(['courseIds', 0], '');
        const key =
          selectedType === 'year'
            ? [year, 'settings']
            : selectedType === 'level'
            ? [year, 'levels', level]
            : [year, 'courses', `${level}+${courseId}`];

        return newObj.setIn(key, item);
      }, Map());

      return state
        .set('isLoading', false)
        .update('groupSettings', Map(), (prev) => prev.merge(cacheData));
    }
    case actions.GET_GROUP_SETTINGS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message === 'No data found!'
            ? ''
            : message
          : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.EDIT_GROUP_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.EDIT_GROUP_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('message', t('saved_successfully'));
    }
    case actions.EDIT_GROUP_SETTINGS_FAILURE: {
      const { response: { data: { data = [], message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .set('isLoading', false)
        .set('message', !data.length ? errorMessage : '')
        .set('invalidEntries', fromJS(data));
    }
    case actions.GET_STUDENTS_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_STUDENTS_LIST_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_STUDENTS_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message === 'No data found!'
            ? ''
            : message
          : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GET_GENERATED_GROUP_NAME_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_GENERATED_GROUP_NAME_SUCCESS: {
      return state.set('isLoading', false).set('generatedGroupName', action.data);
    }
    case actions.GET_GENERATED_GROUP_NAME_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GROUP_STUDENTS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GROUP_STUDENTS_SUCCESS: {
      const { requestBody, selectedCount } = action.data;
      const moveTo = requestBody.get('moveTo');
      const formattedCount = formatToTwoDigitString(selectedCount);
      const message = moveTo
        ? `${formattedCount} students are moved successfully!`
        : `${formattedCount} students are grouped successfully!`;
      return state.set('isLoading', false).set('message', message);
    }
    case actions.GROUP_STUDENTS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GET_STUDENT_DETAILS_REQUEST: {
      return state.set('isLoading', true).set('singleStudentDetails', Map());
    }
    case actions.GET_STUDENT_DETAILS_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('singleStudentDetails', fromJS({ data: action.data }));
    }
    case actions.GET_STUDENT_DETAILS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message === 'student academic no not found'
            ? t('student_grouping.no_data')
            : message === 'student is already exist in this group'
            ? t('student_grouping.student_exists')
            : message
          : 'An error occurred. Please try again.';
      return state
        .set('isLoading', false)
        .set('singleStudentDetails', Map({ error: errorMessage }));
    }
    case actions.ADD_STUDENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_STUDENT_SUCCESS: {
      return state.set('isLoading', false).set('message', t('saved_successfully'));
    }
    case actions.ADD_STUDENT_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.DELETE_STUDENTS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_STUDENTS_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Deleted Successfully');
    }
    case actions.DELETE_STUDENTS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.PUBLISH_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.PUBLISH_SETTINGS_SUCCESS: {
      const { type, notifyType } = action.data;
      const settings = type === 'deliveryGroups' ? 'Grouping settings' : 'Grouped students';
      const notified =
        notifyType === 'sms'
          ? 'notified via SMS'
          : notifyType === 'email'
          ? 'notified via Email'
          : 'notification disabled';
      const message = `${settings} published & ${notified} successfully!`;
      return state.set('isLoading', false).set('message', message);
    }
    case actions.PUBLISH_SETTINGS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GET_TOTAL_COMPLETED_SESSION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_TOTAL_COMPLETED_SESSION_SUCCESS: {
      const { cacheKey, count } = action.data;
      return state.set('isLoading', false).setIn(['totalCompletedSession', cacheKey], count);
    }
    case actions.GET_TOTAL_COMPLETED_SESSION_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GET_YEAR_WISE_STUDENTS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_YEAR_WISE_STUDENTS_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.GET_YEAR_WISE_STUDENTS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message === 'No data found!'
            ? ''
            : message
          : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }

    default:
      return state;
  }
}
