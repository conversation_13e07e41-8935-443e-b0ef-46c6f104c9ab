import React from 'react';
import PropTypes from 'prop-types';
import { List, Map, Set } from 'immutable';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import Typography from '@mui/material/Typography';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import TableContainer from '@mui/material/TableContainer';
import Table from '@mui/material/Table';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import TableFooter from '@mui/material/TableFooter';
import { makeStyles, ThemeProvider } from '@mui/styles';

import {
  capitalize,
  indVerRename,
  MUI_THEME,
  trimFractionDigits,
  isIndGroup,
  studentGroupRename,
  isLateAbsentEnabled,
} from '../../../utils';
import { exportStaffStudentAttendanceReportAsPDF, getFormattedGroupName } from '../utils';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getInfraName } from 'Modules/InstitutionReport/utils';

const useStylesFunction = makeStyles({
  container: {
    width: '100%',
    maxHeight: 400,
    borderTop: '1px solid rgba(224, 224, 224, 1)',
    borderLeft: '1px solid rgba(224, 224, 224, 1)',
  },
  cell: { backgroundColor: '#FFFFFF', borderRight: '1px solid rgba(224, 224, 224, 1)' },
  canceled: { backgroundColor: 'rgba(0, 0, 0, 0.06)' },
  summary: { backgroundColor: '#A0AAB5', color: '#FFFFFF', fontSize: '0.875rem' },
  attendanceSummaryData: { backgroundColor: '#F8FAFC', color: '#000000', fontSize: '0.875rem' },
  stickyFooter: { bottom: 0, left: 0, zIndex: 2, position: 'sticky', padding: 8 },
});

function StaffStudentAttendanceReportModal({
  open,
  onClose,
  userType,
  userData,
  attendanceReport,
  programId,
}) {
  const classes = useStylesFunction();
  const scheduleList = getSchedule();
  const subjectList = getSubjects();
  const columns = [
    { label: t('reports_analytics.s_no') },
    { label: t('reports_analytics.session_details'), minWidth: 225 },
    { label: t('reports_analytics.attendance'), minWidth: 170, align: 'center' },
  ];

  const isLateEnabled = isLateAbsentEnabled();

  function getSchedule() {
    return attendanceReport.get('schedule', List()).map((schedule, idx) => {
      const isMerged = schedule.get('merge_status', false);
      const session = schedule.get('session', Map());
      const deliverySymbol = session.get('delivery_symbol', '');
      const deliveryNumber = session.get('delivery_no', '');
      let sessionName = `${deliverySymbol}${deliveryNumber} - ${session.get('session_topic', '')}`;
      if (isMerged) {
        sessionName = List([`${deliverySymbol}${deliveryNumber}`])
          .concat(
            schedule.get('merge_sessions', List()).map((s) => {
              const deliverySymbol = s.getIn(['session', 'delivery_symbol'], '');
              const deliveryNumber = s.getIn(['session', 'delivery_no'], '');
              return `${deliverySymbol}${deliveryNumber}`;
            })
          )
          .join(', ');
      }
      const mode = capitalize(indVerRename(getInfraName(schedule), programId));
      const subjects = schedule
        .get('subjects', List())
        .map((subject) => subject.get('subject_name', ''))
        .join(', ');
      let groups = schedule
        .get('student_groups', List())
        .map((studentGroup) => {
          const groupName = getFormattedGroupName(
            studentGroupRename(studentGroup.get('group_name', ''), programId),
            isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
          );
          const sessionGroups = studentGroup
            .get('session_group', List())
            .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3));
          return `${groupName} - ${sessionGroups.join(', ')}`;
        })
        .join(', ');
      if (isMerged) {
        const mergedStudentGroups = schedule.get('merge_sessions', List()).map((s) =>
          s
            .get('student_groups', List())
            .map((studentGroup) => {
              const groupName = getFormattedGroupName(
                studentGroupRename(studentGroup.get('group_name', ''), programId),
                isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
              );
              const sessionGroups = studentGroup
                .get('session_group', List())
                .map((sessionGroup) =>
                  getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                );
              return `${groupName} - ${sessionGroups.join(', ')}`;
            })
            .join(', ')
        );
        groups = `${groups}, ${mergedStudentGroups.join(', ')}`;
      }
      const attendanceStatus = schedule.getIn(
        [userType === 'staff' ? 'staffs' : 'students', 'status'],
        ''
      );
      return schedule.merge(
        Map({
          meta: Map({
            sNo: idx + 1,
            details: `${sessionName}\n${mode}${
              userType === 'staff' ? ` | ${subjects} | ${groups}` : ''
            }`,
            attendanceStatus,
            isCanceled: schedule.get('isActive') === false,
          }),
          details: (
            <div>
              <p className="mb-0 f-14">{sessionName}</p>
              <p className="mb-0 f-12 color-light-gray">
                {`${mode}${userType === 'staff' ? ` • ${subjects} • ${groups}` : ''}`}
              </p>
            </div>
          ),
          attendanceStatus:
            schedule.get('isActive') === false ? (
              'SESSION CANCELED'
            ) : (
              <div>
                <div>{getAttendanceStatusIcon(attendanceStatus)}</div>
                {schedule.get('lateLabel', null) === null ? (
                  ''
                ) : (
                  <div className="text-center text-secondary">{`(${schedule.get(
                    'lateLabel',
                    ''
                  )})`}</div>
                )}
              </div>
            ),
        })
      );
    });
  }

  function getAttendanceStatusIcon(status) {
    switch (status) {
      case 'present':
        return <i className="fa fa-check color-success" />;
      case 'leave':
      case 'absent':
        return <p className="mb-0 denial-color">{status.charAt(0).toUpperCase()}</p>;
      case 'permission':
        return <p className="mb-0 color-success">PER</p>;
      case 'on_duty':
        return <p className="mb-0 color-success">OD</p>;
      case 'exclude':
        return <p className="mb-0 session_attendance_excluded">E</p>;
      default:
        return '---';
    }
  }

  function getAttendanceSummary(json = false) {
    const summary = attendanceReport.get('summary', Map());
    const present = summary.get('present_count', 0) || 0;
    const completed = summary.get('session_completed_count', 0) || 0;
    const leave = summary.get('leave_count', 0) || 0;
    const absent = summary.get('absent_count', 0) || 0;
    const permission = summary.get('permission_count', 0) || 0;
    const onDuty = summary.get('onduty_count', 0) || 0;
    const studentLateAbsent = summary.get('studentLateAbsent', 0) || 0;
    const presentPercentage = summary.get('presentPercentage', 0) || 0;
    const percentage = trimFractionDigits(
      ((present + onDuty + permission - studentLateAbsent) / completed) * 100,
      2
    );
    if (json) {
      if (completed === 0) {
        return {
          percentage: `0% - 0/0 ${t('reports_analytics.attended')}`,
          info: `P-0, A-0, PER-0, L-0, OD-0`,
        };
      } else {
        return {
          percentage: `${
            presentPercentage ? presentPercentage : percentage
          }% - ${present}/${completed} ${t('reports_analytics.attended')}`,
          info: `P-${present}, A-${absent}, PER-${permission}, L-${leave}, OD-${onDuty}, ${
            isLateEnabled ? `LA-${studentLateAbsent}` : ''
          }`,
        };
      }
    }
    if (completed === 0) {
      return (
        <div>
          <p className="mb-0">
            <span className="font-weight-bold">{`0% - `}</span>
            0/0 {t('reports_analytics.attended')}
          </p>
          <p className="mb-0">{`P-0, A-0, PER-0, L-0, OD-0`}</p>
        </div>
      );
    } else {
      return (
        <div>
          <p className="mb-0">
            <span className="font-weight-bold">{`${percentage}% - `}</span>
            {present}/{completed} {t('reports_analytics.attended')}
          </p>
          <p className="mb-0">{`P-${present}, A-${absent}, PER-${permission}, L-${leave}, OD-${onDuty}, LA-${studentLateAbsent}`}</p>
        </div>
      );
    }
  }

  function handleExportClick() {
    exportStaffStudentAttendanceReportAsPDF({
      course: attendanceReport.get('course_details', Map()),
      user: Map({
        name: userData.get('formattedName', ''),
        id: attendanceReport.getIn(['user_details', 'user_id'], '') || 'N/A',
        gender: userData.get('gender', ''),
        ...(userType === 'staff' && { subjects: subjectList.join(', ') }),
      }),
      userType,
      body: scheduleList
        .map((schedule) => {
          const meta = schedule.get('meta', Map());
          const attendanceStatus = meta.get('attendanceStatus');
          const isCanceled = meta.get('isCanceled');
          const lateLabel = schedule.get('lateLabel', null);
          let formattedStatus = '---';
          if (isCanceled) {
            formattedStatus = 'SESSION CANCELED';
          } else if (attendanceStatus === 'permission') {
            formattedStatus = 'PER';
          } else if (attendanceStatus === 'on_duty') {
            formattedStatus = 'OD';
          } else if (attendanceStatus !== 'pending') {
            formattedStatus = attendanceStatus.charAt(0).toUpperCase();
          }
          return meta.set('status', formattedStatus).set('lateLabel', lateLabel);
        })
        .toJS(),
      summary: getAttendanceSummary(true),
    });
  }
  function getSubjects() {
    return Set(
      scheduleList
        .reduce((acc, schedule) => acc.concat(schedule.get('subjects', List())), List())
        .map((subject) => subject.get('subject_name', ''))
    ).toList();
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom">
        <div className="d-flex justify-content-between align-items-center">
          <Typography variant="h6">
            <Trans
              i18nKey={
                userType === 'staff'
                  ? 'reports_analytics.staff_attendance_report'
                  : 'reports_analytics.student_attendance_report'
              }
            />
          </Typography>
          <div>
            <IconButton onClick={onClose} size="small">
              <i className="fa fa-times"></i>
            </IconButton>
          </div>
        </div>
      </DialogTitle>
      <DialogContent>
        <div className="mb-3 text-left">
          <p className="color-light-gray f-12 mb-1">
            <Trans
              i18nKey={
                userType === 'staff'
                  ? 'reports_analytics.staff_name'
                  : 'reports_analytics.student_name'
              }
            />
          </p>
          <p className="f-14 mb-0">{userData.get('formattedName', '')}</p>
          <p className="color-light-gray f-12 mb-0">
            {`${attendanceReport.getIn(['user_details', 'user_id'], '') || 'N/A'}, ${userData.get(
              'gender',
              ''
            )}`}
          </p>
          {userType === 'staff' && <p className="f-14 mb-0 mt-1">{subjectList.join(', ')}</p>}
        </div>
        <TableContainer className={classes.container}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.label}
                    {...(column.align && { align: column.align })}
                    style={{ ...(column.minWidth && { minWidth: column.minWidth }) }}
                    className={classes.cell}
                  >
                    {column.label}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {scheduleList.isEmpty() ? (
                <TableRow>
                  <TableCell colSpan={3} className={classes.cell} align="center">
                    <Trans i18nKey={'reports_analytics.no_schedule'} />
                  </TableCell>
                </TableRow>
              ) : (
                scheduleList.map((schedule, i) => {
                  const isCanceled = schedule.get('isActive') === false;
                  return (
                    <TableRow key={`${schedule.get('_id', '')}-${i}`}>
                      <TableCell
                        className={`${classes.cell} ${isCanceled ? classes.canceled : ''}`}
                        align="center"
                      >
                        {i + 1}
                      </TableCell>
                      <TableCell
                        className={`${classes.cell} ${isCanceled ? classes.canceled : ''}`}
                      >
                        {schedule.get('details', '')}
                      </TableCell>
                      <TableCell
                        className={`${classes.cell} ${isCanceled ? classes.canceled : ''}`}
                        align="center"
                      >
                        {schedule.get('attendanceStatus', '')}
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  colSpan={2}
                  align="center"
                  className={`${classes.cell} ${classes.stickyFooter} ${classes.summary}`}
                >
                  <Trans i18nKey={'reports_analytics.summary'} />
                </TableCell>
                <TableCell
                  align="center"
                  className={`${classes.cell} ${classes.stickyFooter} ${classes.attendanceSummaryData}`}
                >
                  {getAttendanceSummary()}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <ThemeProvider theme={MUI_THEME}>
          <Button variant="outlined" color="primary" onClick={onClose}>
            <Trans i18nKey={'reports_analytics.cancel'} />
          </Button>
          <Button variant="contained" color="primary" onClick={handleExportClick}>
            <Trans i18nKey={'reports_analytics.export_pdf'} />
          </Button>
        </ThemeProvider>
      </DialogActions>
    </Dialog>
  );
}

StaffStudentAttendanceReportModal.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  userType: PropTypes.oneOf(['staff', 'student']),
  userData: PropTypes.instanceOf(Map),
  attendanceReport: PropTypes.instanceOf(Map),
  programId: PropTypes.string,
};

export default StaffStudentAttendanceReportModal;
