import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { Table } from 'react-bootstrap';
import Tooltips from '../../../../_components/UI/Tooltip/Tooltip';
import { capitalize } from '../../../InfrastructureManagement/utils';
import AlertIcon from '../../../../Assets/alert6.png';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getVersionName, indVerRename, jsUcfirst, levelRename } from 'utils';
import { Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';

function CourseMasterListTable({
  courseList,
  handleEdit,
  handleDelete,
  programId,
  curriculumId,
  handleClone,
}) {
  const [isShow, setIsShow] = useState(-1);

  // const search = history.location;

  const search = new URLSearchParams(window.location.search);

  function getSharedLevelNo(c) {
    return c.get('shared_with_others', false)
      ? c.get('course_assigned_details', List()).map((l) =>
          l
            .get('course_shared_with', List())
            .filter(
              (item) =>
                item.get('_program_id') === programId && item.get('_curriculum_id') === curriculumId
            )
            .map((s) => (
              <b key={s.get('_id')} className="pr-1 pt-1">
                <span className="badge badge-light version_bg">
                  {levelRename(s.get('level_no', ''), programId) || t('constant.na')}
                </span>
              </b>
            ))
        )
      : c.get('course_assigned_details', List()).map((l) => (
          <b key={l.get('_id')} className="pr-1 pt-1">
            <span className="badge badge-light version_bg">
              {levelRename(l.get('level_no', ''), programId) || t('constant.na')}
            </span>
          </b>
        ));
  }

  return (
    <Table responsive hover>
      <thead className="th-change">
        <tr>
          <th></th>
          <th>
            {' '}
            <Trans i18nKey={'s_no'}></Trans>.
          </th>
          <th>
            <Trans i18nKey={'type'}></Trans>
          </th>
          <th>
            <Trans i18nKey={'code'}></Trans>
          </th>
          <th>
            <Trans i18nKey={'course_name_small'}></Trans>
          </th>
          <th>
            {' '}
            <Trans i18nKey={'no_of_weeks'}></Trans>
          </th>
          {/* <th>
            {' '}
            <Trans i18nKey={'occurrence'}></Trans>
          </th> */}
          <th>
            {' '}
            <Trans
              i18nKey={'levels_assigned'}
              values={{ Level: indVerRename('Level', programId) }}
            ></Trans>{' '}
          </th>
        </tr>
      </thead>
      <tbody>
        {courseList.isEmpty() && (
          <tr>
            <td colSpan="9" className="text-center">
              <Trans i18nKey={'no_record_found'}></Trans>
            </td>
          </tr>
        )}
        {courseList.map((c, i) => {
          // let isAlertIcon = c.get('course_assigned_details', List()).size !== 0;
          // if (capitalize(c.get('course_type', '')) === 'Selective') {
          //   isAlertIcon =
          //     c.get('course_assigned_details', List()).size ===
          //     c.get('course_recurring', List()).size;
          // }

          const checkOldCourse = c.getIn(['course_assigned_details', 0], Map()).has('isConfigured');

          return (
            <tr
              key={c.get('_id')}
              className="tr-change"
              onMouseEnter={() => setIsShow(i)}
              onMouseLeave={() => setIsShow(-1)}
            >
              <td>
                {c.get('shared_with_others', false) === true && (
                  <div className="pr-1 pt-1">
                    <span className="badge badge-light version_bg">S</span>
                  </div>
                )}
                {/* {!isAlertIcon && (
                  <div className="mt-2">
                    <img src={AlertIcon} alt="alert" className="mx-auto d-block" />
                  </div>
                )} */}
              </td>
              <td>
                <div className="mt-2">{i + 1}</div>
              </td>
              <td>
                <div className="mt-2">
                  {jsUcfirst(indVerRename(c.get('course_type', ''), programId))}
                </div>
              </td>
              <td>
                <div className="mt-2">
                  <div className="w-100px">{c.get('course_code', '') || t('constant.na')}</div>
                </div>
              </td>
              <td>
                <div className="mt-2">
                  {capitalize(c.get('course_name', '')) || t('constant.na')}
                  {getVersionName(c)}
                </div>
              </td>
              <td>
                <div className="mt-2">{c.get('duration', '') || t('constant.na')}</div>
              </td>
              {/* <td>
                <div className="mt-2">
                  {c.get('course_recurring', List()).isEmpty()
                    ? t('constant.na')
                    : c
                        .get('course_recurring', List())
                        .map((l) => levelRename(l.get('level_no', ''), programId))
                        .join(', ')}
                </div>
              </td> */}
              <td>
                {checkOldCourse === false ? (
                  <div className="mt-2">
                    {c.get('course_assigned_details', List()).isEmpty()
                      ? t('constant.not_assigned')
                      : getSharedLevelNo(c)}
                  </div>
                ) : (
                  <div>
                    {c
                      .get(
                        c.get('course_type', '') === 'standard'
                          ? 'course_occurring'
                          : 'course_recurring',
                        List()
                      )
                      .map((item) => {
                        const isConfigured = c
                          .get('course_assigned_details', List())
                          ?.find((s) => s.get('level_no', '') === item.get('level_no', ''));

                        return (
                          <>
                            <div className="d-flex flex-wrap">
                              <div className="mr-2 mb-1 mt-1">
                                <div
                                  className={
                                    isConfigured?.get('isConfigured', false)
                                      ? 'configure-course'
                                      : 'configure-course-alert'
                                  }
                                  // "icon_schedule"
                                >
                                  {!isConfigured?.get('isConfigured', false) && (
                                    <img
                                      src={AlertIcon}
                                      alt="alert"
                                      className="pr-1"
                                      style={{ width: '25%' }}
                                    />
                                  )}
                                  {levelRename(item.get('level_no', ''), programId)}
                                </div>
                              </div>
                            </div>
                            {/* <Chip
                            key={index}
                            label={
                              <div className="d-flex">
                                {isConfigured === undefined && (
                                  <div className="mt-2">
                                    <img src={AlertIcon} alt="alert" className="mx-auto d-block" />
                                  </div>
                                )}
                                <div>{item.get('level_no', '')}</div>
                              </div>
                            }
                            color={isConfigured === undefined ? 'primary' : 'default'}
                            variant="outlined"
                          /> */}
                          </>
                        );
                      })}
                  </div>
                )}
              </td>

              <td>
                <Link
                  // to={`/program-input/configuration/course-masterlist/view?${search}`}
                  to={`/program-input/course/configuration?courseId=${c.get('_id', '')}&${search}`}
                  style={{ textDecoration: 'none' }}
                >
                  <Button variant="outline-warning" block className="border-radious-8 f-14">
                    {t('view')}
                  </Button>
                </Link>
              </td>

              <td style={{ width: '125px' }}>
                {i === isShow && c.get('shared_with_others', false) !== true && (
                  <div className="mt-2">
                    {CheckPermission(
                      'tabs',
                      'Program Input',
                      'Programs',
                      '',
                      'Active Programs',
                      'Edit'
                    ) && (
                      <Tooltips title={<Trans i18nKey={'edit'}></Trans>}>
                        <i
                          className="fa fa-pencil faStyle fa-lg remove_hover mr-2 f-14"
                          aria-hidden="true"
                          onClick={() =>
                            handleEdit(
                              c.get('_curriculum_id'),
                              c.get('_id'),
                              c.get('versionName', '')
                            )
                          }
                        ></i>
                      </Tooltips>
                    )}
                    {CheckPermission(
                      'tabs',
                      'Program Input',
                      'Programs',
                      '',
                      'Active Programs',
                      'Delete'
                    ) && (
                      <>
                        {/* {c.get('course_assigned_details', List()).size === 0 && ( */}
                        <Tooltips title={t('delete')}>
                          <i
                            className="fa fa-trash faStyle fa-lg remove_hover mr-2 f-14"
                            aria-hidden="true"
                            onClick={() => handleDelete(c, false)}
                          ></i>
                        </Tooltips>
                        {/* )} */}
                      </>
                    )}

                    <>
                      <Tooltips title={'Copy as new version'}>
                        <i
                          className="fa fa-clone faStyle fa-lg remove_hover f-14"
                          aria-hidden="true"
                          onClick={() => handleClone(c)}
                        ></i>
                      </Tooltips>
                    </>
                  </div>
                )}
              </td>
            </tr>
          );
        })}
      </tbody>
    </Table>
  );
}

CourseMasterListTable.propTypes = {
  courseList: PropTypes.instanceOf(List),
  handleEdit: PropTypes.func,
  handleDelete: PropTypes.func,
  programId: PropTypes.string,
  curriculumId: PropTypes.string,
  handleClone: PropTypes.func,
};

export default CourseMasterListTable;
