import React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import academicCapIcon from 'Assets/academicCapIcon.svg';
import WarningAmberTwoToneIcon from '@mui/icons-material/WarningAmberTwoTone';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import merge_new from 'Assets/merge_new.svg';
import { Divider, Grid } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { getVersionName, jsUcfirstAll } from 'utils';
import { useDispatch, useSelector } from 'react-redux';
import { selectIndividualStudentStatus } from '_reduxapi/LeaveManagementReports/selectors';
import { useEffect } from 'react';
import { getIndividualStudentStatus, setData } from '_reduxapi/LeaveManagementReports/action';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import moment from 'moment';
import {
  CustomTooltip,
  addingZeroToPrefixOver,
  durationWithSingularAndPlural,
} from 'Modules/LeaveManagementReports/utils';

const useStyles = makeStyles(() => ({
  customTooltip: {
    padding: 0,
    '& .MuiTooltip-arrow': {
      color: '#1F2937',
    },
  },
}));

const ApplicationModal = ({
  open,
  formatCreatedDate,
  appliedFor,
  handleClose,
  item,
  yearWithProgram,
  studentNameCodeWise,
}) => {
  //*************Selectors******************************* */
  const individualStudentStatus = useSelector(selectIndividualStudentStatus).filter(
    (item) => item.get
  );

  //*************StorageContainers******************** */
  const dispatch = useDispatch();
  const classes = useStyles();
  const url = `/lmsStudent/report/getStudentApplicationStatus?studentId=${item.getIn(
    ['studentId', '_id'],
    ''
  )}&programId=${item.getIn(['programId', '_id'], '')}&year=${item.get(
    'year',
    ''
  )}&level=${item.get('level', '')}&term=${item.get('term', '')}&startDate=${item.getIn(
    ['dateAndTimeRange', 'startDate'],
    ''
  )}&endDate=${item.getIn(
    ['dateAndTimeRange', 'endDate'],
    ''
  )}&_institution_calendar_id=${yearWithProgram.getIn(['primaryData', 'academicYear', '_id'], '')}`;

  //*************Effects****************************** */
  useEffect(() => {
    dispatch(setData(Map({ individualStudentStatus: List() })));
    dispatch(getIndividualStudentStatus(url));
  }, []); //eslint-disable-line

  return (
    <Dialog
      sx={{
        '& .MuiDialog-paper': {
          overflow: 'hidden',
        },
      }}
      fullWidth={true}
      maxWidth={'lg'}
      open={open}
      onClose={handleClose}
    >
      <div className="d-flex px-3 pt-3">
        <div className="digi-reports-h6 pb-1 mb-0">
          {' '}
          {studentNameCodeWise(item.get('studentId', Map()))}
        </div>
        <div
          className={`ml-auto lms-btn-color-${
            item.get('approvalStatus', '').includes('Pending')
              ? 'Pending'
              : item.get('approvalStatus', '')
          }  rounded f-14 px-3 py-2`}
        >
          Status :{' '}
          {item.get('approvalStatus', '').includes('Pending')
            ? 'Pending'
            : item.get('approvalStatus', '')}
        </div>
      </div>
      <div className="Dialog_scroll_lms">
        <div className="px-3">
          <div className="d-flex align-items-center">
            <div>
              <img width="20" height="20" src={academicCapIcon} alt="academicCapIcon" />
            </div>
            <div className="f-15 ml-2 mt-1 text-muted">
              {item.getIn(['programId', 'name'], '')}/{jsUcfirstAll(item.get('term', ''))}/{' '}
              {item.get('year', '').replace('year', 'Year ')}/{item.get('level', '')}
            </div>
          </div>
          <div className="d-flex">
            <div>
              <span className="f-14 text-muted">Applied Date</span>
              <span className="ml-2 mr-1">:</span>
              <span className="f-14 text-muted">{formatCreatedDate}</span>
            </div>
            <Divider className="mx-2 my-1" orientation="vertical" flexItem />
            <div>
              <span className="f-14 text-muted">Taken Date</span>
              <span className="ml-2 mr-1">:</span>
              <span className="f-14 text-muted">{appliedFor} </span>
            </div>
          </div>
          <div className="d-flex">
            <div>
              <span className="f-14 text-muted">Category Type</span>
              <span className="ml-2 mr-1">:</span>
              <span className="f-14 text-muted text-capitalize">
                {' '}
                {item.get('categoryName', '')}
              </span>
            </div>
            <Divider className="mx-2 my-1" orientation="vertical" flexItem />
            <div>
              <span className="f-14 text-muted">Duration</span>
              <span className="ml-2 mr-1">:</span>
              <span className="f-14 text-muted">
                {' '}
                {item.get('noOfHours', 0) <= 9
                  ? '0' + item.get('noOfHours', 0)
                  : item.get('noOfHours', 0)}{' '}
                {durationWithSingularAndPlural(
                  item.get('classificationType', ''),
                  item.get('noOfHours', 0)
                )}
              </span>
            </div>
          </div>
          <div className="mt-2 h6">Affected Courses During Absents</div>
          <Grid
            container
            className={`d-flex  flex-nowrap ${
              individualStudentStatus.size !== 1 ? 'table-height' : 'table-height-41vh'
            }  table--xy`}
          >
            <div className="table_wrapper position-Ab row flex-nowrap w-100 ml-0 LmsTableContainer">
              <table className="table back-color LmsTable border-radious-4">
                <thead className="LmsHeader">
                  <tr className="border-bottom">
                    <th className="font-weight-normal Lms-data-width">
                      <div className="digi-gray font-weight-500">S No.</div>
                    </th>
                    <th className="font-weight-normal">
                      <div className="digi-gray font-weight-500 ">Delivery Type & Topic</div>
                    </th>
                    <th className="font-weight-normal">
                      <div className="digi-gray font-weight-500">Subject </div>
                    </th>
                    <th className="font-weight-normal">
                      <div className="digi-gray font-weight-500">Staff Name</div>
                    </th>
                    <th className="font-weight-normal">
                      <div className="digi-gray font-weight-500">Date</div>
                    </th>
                    <th className="font-weight-normal">
                      <div className="digi-gray font-weight-500">Scheduled Info</div>
                    </th>
                  </tr>
                </thead>
                {individualStudentStatus.size ? (
                  individualStudentStatus.map((session, index) => (
                    <React.Fragment key={index}>
                      <thead>
                        <div className="digi-report-table-border">
                          <div className=" m-3 d-flex justify-content-between">
                            <div className="d-flex">
                              <div className="bold f-14 text-capitalize">
                                {session.get('course_name')} -{' '}
                                {jsUcfirstAll(session.get('course_code', ''))}
                                {getVersionName(session)} ({session.get('schedules', List()).size})
                              </div>
                              {session.get('currentWarning', '') !== '' && (
                                <div className="ml-4">
                                  <div className="d-flex align-self-center ">
                                    <div>
                                      {/* <img width="20" height="20" src={warningLms} alt="warningLms" /> */}
                                      <WarningAmberTwoToneIcon
                                        sx={{ color: session.get('warningColorCode', '') }}
                                      />
                                    </div>
                                    <div
                                      className="f-14 font-weight-normal pl-1"
                                      style={{ color: session.get('warningColorCode', '') }}
                                    >
                                      Current Warning (
                                      {jsUcfirstAll(session.get('currentWarning', ''))})
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                            {item.get('approvalStatus', '').includes('Pending') &&
                              session.get('isDenial', false) &&
                              session.get('currentWarning', '').toLowerCase().indexOf('denial') ===
                                -1 && (
                                <div>
                                  <div className="text-danger f-14 bold">
                                    If approved, he will be moved to Denial.
                                  </div>
                                </div>
                              )}
                          </div>
                        </div>
                      </thead>
                      <tbody className="LmsBody">
                        {session.get('schedules', List()).map((schedule, secIndex) => (
                          <tr key={secIndex}>
                            <td className="Lms-data-width">
                              {addingZeroToPrefixOver(secIndex + 1)}.
                            </td>
                            {schedule.get('mergeStatus', false) ? (
                              <td>
                                <div className="d-flex align-items-center">
                                  <div className="">
                                    <img width="20" height="20" src={merge_new} alt="merge_new" />
                                  </div>
                                  <div className="TextEllipsis ml-2 mr-2 f-14">
                                    {schedule
                                      .get('mergedSchedule', List())
                                      .slice(0, 3)
                                      .map(
                                        (mergedItem) =>
                                          jsUcfirstAll(mergedItem.get('delivery_symbol', '')) +
                                          mergedItem.get('delivery_no')
                                      )
                                      .join(', ')}
                                    {schedule.get('mergedSchedule', List()).size < 3 ? '' : '...'}
                                  </div>
                                  <CustomTooltip
                                    tooltipClass={classes.customTooltip}
                                    mergedSchedule={schedule.get('mergedSchedule', List())}
                                  />
                                </div>
                              </td>
                            ) : (
                              <td>
                                <div className="text-capitalize">
                                  {schedule.get('delivery_symbol', '') +
                                    schedule.get('delivery_no')}
                                </div>
                              </td>
                            )}
                            <td>
                              <div className="text-capitalize f-14">
                                {getShortString(
                                  schedule.get('subject_name', List()).join(', '),
                                  30
                                )}
                              </div>
                            </td>
                            <td>
                              {schedule.get('staff_name', List()).map((staff, index) => (
                                <div className="text-capitalize f-14" key={index}>
                                  {getShortString(
                                    staff.get('first', '') +
                                      ' ' +
                                      jsUcfirstAll(staff.get('last', '')),
                                    45
                                  )}
                                </div>
                              ))}
                            </td>
                            <td>
                              <div className=" f-14">
                                {moment(schedule.get('schedule_date', '')).format('DD MMM YYYY')}
                              </div>
                            </td>
                            <td>
                              <div className=" f-14">
                                {moment(schedule.get('scheduleStartDateAndTime', '')).format(
                                  'hh:mm A'
                                )}{' '}
                                -{' '}
                                {moment(schedule.get('scheduleEndDateAndTime', '')).format(
                                  'hh:mm A'
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </React.Fragment>
                  ))
                ) : (
                  <tbody className="LmsBody">
                    <tr className="tr-change remove_hover f-15 digi-gray">
                      <td className="text-center" colSpan={11}>
                        No Schedule Found...
                      </td>
                    </tr>
                  </tbody>
                )}
              </table>
            </div>
          </Grid>
        </div>
      </div>
      <DialogActions className="pb-3">
        <Button
          variant="outlined"
          className="border text-dark border-secondary px-4 py-1 mr-2 text-uppercase"
          onClick={handleClose}
        >
          close
        </Button>
      </DialogActions>
    </Dialog>
  );
};
ApplicationModal.propTypes = {
  open: PropTypes.bool,
  formatCreatedDate: PropTypes.string,
  appliedFor: PropTypes.string,
  studentNameCodeWise: PropTypes.func,
  handleClose: PropTypes.func,
  item: PropTypes.instanceOf(Map),
  yearWithProgram: PropTypes.instanceOf(Map),
};

export default ApplicationModal;
