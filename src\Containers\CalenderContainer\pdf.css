table {
    height: 100%;
  }
  
  tbody {
    vertical-align: inherit;
  }
  
  tfoot {
    vertical-align: bottom;
  }
  
  .pdf-wrapper {
    margin: 0 25px;
    margin-top: 15px;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 90vh;
  }
  
  .pdf-main {
    flex: 1;
  }
  
  .pdf-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }
  
  .logo-pdf {
    width: 100%
  }
  
  .pdf .address {
    line-height: 1.5;
    font-size: 12px;
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  
  .header-title {
    flex: 0 0 60%;
    max-width: 60%;
    text-align: center;
  }
  
  .section-header {
    /* background: linear-gradient(101deg, #b0c0c7, #090979); */
    background:  #5f0079;
    padding: 10px;
    padding-left: 10px;
    margin-bottom: 5px;
  }
  
  .section-header h2 {
    color: #fff;
    text-transform: uppercase;
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    margin: 0;
  }
  
  .section-content {
    margin-top: 5px;
    margin-bottom: 5px;
  }
  
  .section-meta {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  
  .section-meta-item {
    flex: 0 0 50%;
    max-width: 50%;
    display: flex;
    justify-content: space-between;
  }
  
  .section-meta-item label {
    line-height: 1.2;
    font-weight: 700;
  }
  
  .section-meta-item p {
    margin: 0;
    text-align: right;
    line-height: 1.2;
  }
  
  .section-meta-item-fullwidth {
    flex: 0 0 100%;
    max-width: 100%;
    display: flex;
    justify-content: space-between;
  }
  
  .section-meta-item-fullwidth label {
    font-weight: 700;
  }
  
  .section-meta-item-fullwidth p {
    margin: 0;
    line-height: 1.2;
  }
  
  .section-image {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 20px;
    margin-bottom: 20px;
  }
  
  .section-image img {
    margin-bottom: 5px;
  }
  
  .pdf label {
    font-weight: 700;
    font-size: 12px;
  }
  
  .pdf p {
    margin: 0;
    text-align: right;
    font-size: 12px;
  }
  
  .spacing {
    border: solid 5px transparent;
    box-sizing: border-box;
    position: relative;
  }
  
  .spacing::before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
  
  .signature {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    margin-top: 20px;
    margin-bottom: 5px;
  }
  
  .signature input {
    border: none;
    border-bottom: 1px solid grey;
    display: block;
    width: 100%;
    margin-bottom: 5px;
  }
  
  .pdf .signature label {
    display: block;
    text-align: center;
  }
  
  .pdf .title-center {
    text-align: center;
    font-size: 16px;
    text-transform: uppercase;
  }
  
  .pdf p {
    page-break-inside: avoid;
  }
  
  .footer-details {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .footer-details p {
    font-size: 12px;
    line-height: 1.2;
  }
  
  .footer-details img {
    border: 5px solid transparent;
  }
  
  
  
  ol, ul {
    list-style: none;
  }
  
  table {
    border-collapse: collapse;
    border-spacing: 0;
  }
  
  caption, th, td {
    text-align: left;
    font-weight: normal;
    vertical-align: middle;
  }
  
  q, blockquote {
    quotes: none;
  }
  q:before, q:after, blockquote:before, blockquote:after {
    content: "";
    content: none;
  }
  
  a img {
    border: none;
  }
  
  article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
    display: block;
  }
  
  
  header figure {
    float: left;
    margin-right: 10px;
    border-radius: 50%;
    text-align: center;
  }
  ​
  header .company-address {
    float: left;
    max-width: 600px;
    line-height: 1.7em;
    text-align: center;
  }
  header .company-address .title {
    color: #abafa6;
    font-weight: 400;
    font-size: 1.5em;
    text-transform: uppercase;
  }
  header .company-contact {
    float: right;
    padding: 17px 10px;
  }
  header .company-contact span {
    display: inline-block;
    vertical-align: middle;
  }
  header .company-contact .circle {
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    text-align: center;
  }
  header .company-contact .circle img {
    vertical-align: middle;
  }
  header .company-contact .phone {
    height: 100%;
    margin-right: 20px;
  }
  header .company-contact .email {
    height: 100%;
    min-width: 100px;
    text-align: right;
  }
  
  section .details {
    margin-bottom: 30px;
    margin-top: 30px;
  }
  section .details .client {
    width: 50%;
    line-height: 20px;
  }
  section .details .client .name {
    color: #7f8677;
  }
  section .details .data {
    width: 50%;
    text-align: right;
  }
  section .details .title {
    margin-bottom: 15px;
    color: #7f8677;
    font-size: 2em;
    font-weight: 400;
    text-transform: uppercase;
  }
  section table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 0.9166em;
    border: 1px solid;
  }
  section table .qty, section table .unit, section table .total {
    width: 15%;
  }
  section table .desc {
    width: 15%;
  }
  section table .serial {
    width: 1%;
  }
  section table thead {
    display: table-header-group;
    vertical-align: middle;
    border-color: inherit;
  }
  section table thead th {
    padding: 5px 10px;  
    background: #abafa6;
    /* border-bottom: 1px solid #777777;
    border-right: 1px solid #abafa6; */
    color: white;
    font-weight: 400;
    text-transform: uppercase;
    border: 2px solid #777777;
  }
  section table thead th:last-child {
    border-right: none;
  }
  section table thead .desc {
    text-align: left;
  }
  
  section table tbody td {
    padding: 10px;
    background: #E8F3DB;
    color: #777777;
    /* border-bottom: 5px solid #FFFFFF;
    border-right: 4px solid #E8F3DB; */
    border: 1px solid;
  }
  section table tbody td:last-child {
    border-right: none;
  }
  section table tbody h3 {
    margin-bottom: 5px;
    color: #858882;
    font-size: 16px;
    font-weight: 600;
  }
  section table tbody .desc {
  }
  section table tbody .qty {
    /* text-align: center; */
  }
  section table.grand-total {
    margin-bottom: 45px;
  }
  section table.grand-total td {
    padding: 5px 10px;
    border: none;
    color: #777777;
    text-align: right;
  }
  section table.grand-total .desc {
    background-color: transparent;
  }
  section table.grand-total tr:last-child td {
    font-weight: 600;
    color: #7f8677;
    font-size: 1.18181818181818em;
  }
  
  footer {
    margin-bottom: 20px;
  }
  footer .thanks {
    margin-bottom: 40px;
    color: #7f8677;
    font-size: 1.16666666666667em;
    font-weight: 600;
  }
  footer .notice {
    margin-bottom: 25px;
  }
  footer .end {
    padding-top: 5px;
    /* border-top: 2px solid #7f8677; */
    text-align: center;
  }
  .right-2 {
    padding-right: 13px;
  }
  .center{
      text-align: center;
  }
  .right{
    text-align: right;
  }
  .borders{
      border-bottom: 1px solid #7f8677;
  }
  
  .fs18{
    font-size: 18px;
  }
  
  .fs15{
    font-size : 15px;
  }
  .fs14{
    font-size : 14px;
  }