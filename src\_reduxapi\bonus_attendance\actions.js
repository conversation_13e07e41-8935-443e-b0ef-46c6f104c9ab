import { createAction } from '../util';
import axios from '../../axios';
import { resetMessage } from '../leave_management/actions';

export const UPSERT_BONUS_ATTENDANCE_REQUEST = 'UPSERT_BONUS_ATTENDANCE_REQUEST';
export const UPSERT_BONUS_ATTENDANCE_SUCCESS = 'UPSERT_BONUS_ATTENDANCE_SUCCESS';
export const UPSERT_BONUS_ATTENDANCE_FAILURE = 'UPSERT_BONUS_ATTENDANCE_FAILURE';

const upsertBonusAttendanceRequest = createAction(UPSERT_BONUS_ATTENDANCE_REQUEST);
const upsertBonusAttendanceSuccess = createAction(UPSERT_BONUS_ATTENDANCE_SUCCESS, 'data');
const upsertBonusAttendanceFailure = createAction(UPSERT_BONUS_ATTENDANCE_FAILURE, 'error');

export function upsertBonusAttendance(data, id = null, callBack = null) {
  return function (dispatch) {
    dispatch(upsertBonusAttendanceRequest());
    const endpoint = id ? `/bonus-attendance/?bonusAttendanceId=${id}` : '/bonus-attendance';
    axios
      .post(endpoint, data)
      .then((res) => {
        dispatch(upsertBonusAttendanceSuccess(res.data));
        const message = id
          ? 'Bonus attendance updated successfully'
          : 'Bonus attendance created successfully';
        dispatch(resetMessage(message));
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(upsertBonusAttendanceFailure(error));
        const operation = id ? 'update' : 'create';
        const errorMessage =
          error.response?.data?.message || `Failed to ${operation} bonus attendance`;
        dispatch(resetMessage(errorMessage));
      });
  };
}

export const DELETE_BONUS_ATTENDANCE_REQUEST = 'DELETE_BONUS_ATTENDANCE_REQUEST';
export const DELETE_BONUS_ATTENDANCE_SUCCESS = 'DELETE_BONUS_ATTENDANCE_SUCCESS';
export const DELETE_BONUS_ATTENDANCE_FAILURE = 'DELETE_BONUS_ATTENDANCE_FAILURE';

const deleteBonusAttendanceRequest = createAction(DELETE_BONUS_ATTENDANCE_REQUEST);
const deleteBonusAttendanceSuccess = createAction(DELETE_BONUS_ATTENDANCE_SUCCESS, 'data');
const deleteBonusAttendanceFailure = createAction(DELETE_BONUS_ATTENDANCE_FAILURE, 'error');

export function deleteBonusAttendance(id, callBack) {
  return function (dispatch) {
    dispatch(deleteBonusAttendanceRequest());
    axios
      .delete(`/bonus-attendance/?bonusAttendanceId=${id}`)
      .then((res) => {
        dispatch(deleteBonusAttendanceSuccess({ id, ...res.data }));
        dispatch(resetMessage('Bonus attendance deleted successfully'));
        callBack();
      })
      .catch((error) => {
        dispatch(deleteBonusAttendanceFailure(error));
        const errorMessage = error.response?.data?.message || 'Failed to delete bonus attendance';
        dispatch(resetMessage(errorMessage));
      });
  };
}

export const GET_STUDENT_BONUS_ATTENDANCE_REQUEST = 'GET_STUDENT_BONUS_ATTENDANCE_REQUEST';
export const GET_STUDENT_BONUS_ATTENDANCE_SUCCESS = 'GET_STUDENT_BONUS_ATTENDANCE_SUCCESS';
export const GET_STUDENT_BONUS_ATTENDANCE_FAILURE = 'GET_STUDENT_BONUS_ATTENDANCE_FAILURE';

const getStudentBonusAttendanceRequest = createAction(GET_STUDENT_BONUS_ATTENDANCE_REQUEST);
const getStudentBonusAttendanceSuccess = createAction(GET_STUDENT_BONUS_ATTENDANCE_SUCCESS, 'data');
const getStudentBonusAttendanceFailure = createAction(
  GET_STUDENT_BONUS_ATTENDANCE_FAILURE,
  'error'
);

export function getStudentBonusAttendance(
  studentId,
  programId = null,
  courseId = null,
  year = null,
  level = null,
  term = null,
  rotationCount = null
) {
  return function (dispatch) {
    dispatch(getStudentBonusAttendanceRequest());

    let endpoint = `/bonus-attendance/student/?studentId=${studentId}`;
    if (programId) endpoint += `&programId=${programId}`;
    if (courseId) endpoint += `&courseId=${courseId}`;
    if (year) endpoint += `&year=${year}`;
    if (level) endpoint += `&level=${level}`;
    if (term) endpoint += `&term=${term}`;
    if (rotationCount) endpoint += `&rotationCount=${rotationCount}`;

    axios
      .get(endpoint)
      .then((res) => dispatch(getStudentBonusAttendanceSuccess(res.data)))
      .catch((error) => {
        dispatch(getStudentBonusAttendanceFailure(error));
        const errorMessage = error.response?.data?.message || 'Failed to fetch bonus attendance';
        dispatch(resetMessage(errorMessage));
      });
  };
}
