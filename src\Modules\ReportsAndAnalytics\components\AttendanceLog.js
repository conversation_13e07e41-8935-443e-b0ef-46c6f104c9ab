import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import { useParams } from 'react-router-dom';
// import { isAfter, isValid, isWithinInterval, startOfDay } from 'date-fns';

import StaffStudentAttendanceLog from './StaffStudentAttendanceLog';
import AttendanceLogFilters from './Filters/AttendanceLogFilters';

import * as actions from '../../../_reduxapi/reports_and_analytics/action';
import {
  selectActiveInstitutionCalendar,
  selectUserInfo,
} from '../../../_reduxapi/Common/Selectors';
import {
  selectAttendanceLog,
  selectCourseOverviewData,
} from '../../../_reduxapi/reports_and_analytics/selectors';
import { capitalize, getURLParams, dString, isIndGroup, studentGroupRename } from '../../../utils'; //trimFractionDigits,
//import AttendanceLogExportModal from '../modal/AttendanceLogExportModal';
import {
  // exportStaffStudentAttendanceLogAsPDF,
  getFirstAndLastDateOfMonth,
  getMonthsArray,
} from '../utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { t } from 'i18next';
import { exportExcel } from './ExcelExport';
import moment from 'moment';
import { getFormattedGroupName } from 'Modules/CourseScheduling/components/utils';

// const initialExportOptions = Map({
//   userType: '',
//   studentGroup: '',
//   start: null,
//   end: null,
//   onlyCompletedSessions: false,
// });

function AttendanceLog(props) {
  const {
    activeInstitutionCalendar,
    attendanceLog,
    getAttendanceLog,
    // resetMessage,
    // setData,
    courseOverview,
    loggedInUserData,
    getAttendanceLogExport,
  } = props;

  const params = useParams();
  const [filters, setFilters] = useState(
    Map({
      studentGroup: List(),
      gender: '',
      viewType: 'student',
      search: '',
      isPopulated: false,
      month: '',
      tableFilter: List(['user_id', 'delivery_group', 'schedule_date', 'monthly']),
      date: 'all',
      startDate: '',
      endDate: '',
    })
  );

  useEffect(() => {
    if (!courseOverview.isEmpty()) {
      const monthArray = getMonthArray();
      const currentMonth = moment(new Date()).format('MMMM YYYY');
      const hasMonthInArray = monthArray.map((item) => item.name).includes(currentMonth);
      const groupName = getFormattedGroupName(
        courseOverview.getIn(['group_data', 0, 'group_name'], ''),
        isIndGroup(courseOverview.getIn(['group_data', 0, 'group_name'], '')) ? 1 : 2
      );
      setFilters(
        filters.merge(
          Map({
            studentGroup: List([groupName]),
            viewType: 'student',
            month: hasMonthInArray
              ? currentMonth
              : monthArray.length > 0
              ? monthArray[0].value
              : '',
          })
        )
      );
    }
  }, [courseOverview]); //eslint-disable-line

  const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id');
  const programId = params.programId ? dString(params.programId) : '';
  const courseId = params.courseId ? dString(params.courseId) : '';
  const level = getURLParams('level', true);
  const term = getURLParams('term', true);
  const isRotation = getURLParams('rotation', true) === 'yes';
  const rotationCount = getURLParams('rotationCount', true);
  const studentGroup = filters.get('studentGroup', List());

  const tableFilter = filters.get('tableFilter', List());
  const month = filters.get('month', '');
  const startDate = filters.get('startDate', '');
  const endDate = filters.get('endDate', '');
  const courseDetails = courseOverview.get('course_details', Map());

  const { firstDate, lastDate } = getFirstAndLastDateOfMonth(courseDetails, month);
  const urlParams = {
    type: filters.get('viewType', ''),
    ...(isRotation && { rotation_count: rotationCount }),
    tableFilter: tableFilter.toJS(),
    ...(studentGroup.size > 0 && { groupName: studentGroup.toJS() }),
    //gender: gender === '' ? ['male', 'female'] : gender,
    ...(startDate === '' && { firstDate, lastDate }),
    ...(startDate !== '' && { firstDate: startDate }),
    ...(endDate !== '' && { lastDate: endDate }),
  };

  useEffect(
    () => {
      if (
        studentGroup.size > 0 &&
        activeInstitutionCalendarId &&
        programId &&
        courseId &&
        level &&
        term
      ) {
        getAttendanceLog({
          institutionCalendarId: activeInstitutionCalendarId,
          programId,
          courseId,
          level,
          term,
          params: urlParams,
        });
      }
    },
    //eslint-disable-next-line
    [
      activeInstitutionCalendarId,
      programId,
      courseId,
      level,
      term,
      getAttendanceLog,
      filters.get('studentGroup'), //eslint-disable-line
      filters.get('viewType'), //eslint-disable-line
      filters.get('isPopulated'), //eslint-disable-line
      filters.get('month'), //eslint-disable-line
      filters.get('tableFilter'), //eslint-disable-line
      filters.get('date'), //eslint-disable-line
      filters.get('startDate'), //eslint-disable-line
      filters.get('endDate'), //eslint-disable-line
    ]
  ); //eslint-disable-line

  function handleTableFilter(tableFilter) {
    setFilters(
      filters.merge(
        Map({
          tableFilter: tableFilter,
        })
      )
    );
  }

  function getActiveStudentGroup() {
    // const studentGroupId = filters.get('studentGroup', '');
    return attendanceLog.get('attendance_log', List());
    // .find((sg) => sg.get('group_id') === studentGroupId) || Map()
    // .find((sg) => sg.get('group_name') === studentGroupId) || Map()
    // .filter((sg) => studentGroupId.includes(sg.get('group_name'))) || Map()
  }

  function getGenders() {
    return List([t('reports_analytics.male'), t('reports_analytics.female')]).map((gender) =>
      Map({ name: capitalize(gender), value: gender.toLowerCase() })
    );
  }

  function getViewTypes() {
    return List([
      Map({ name: t('reports_analytics.student_details'), value: 'student' }),
      Map({ name: t('reports_analytics.staff_details'), value: 'staff' }),
    ]);
  }

  function getStudentGroupOptions() {
    return courseOverview.get('group_data', List()).map((studentGroup) => {
      const groupName = getFormattedGroupName(
        studentGroup.get('group_name', ''),
        isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
      );
      return Map({
        name: studentGroupRename(groupName, programId),
        value: groupName,
        gender: '',
      });
    });
  }

  function getFilterOptions() {
    return Map({ studentGroups: getStudentGroupOptions(), genders: getGenders() });
  }

  function handleViewTypeChange(viewType) {
    let updatedFilters = filters.set('viewType', viewType).set('gender', '');
    if (updatedFilters.get('viewType') === 'student') {
      const studentGroup =
        getStudentGroupOptions().find(
          (studentGroup) => studentGroup.get('value') === updatedFilters.get('studentGroup')
        ) || Map();
      updatedFilters = updatedFilters.set('gender', studentGroup.get('gender', ''));
    }
    setFilters(updatedFilters);
  }

  function getMonthArray() {
    const startDate = courseOverview.getIn(['course_details', 'start_date'], '');
    const endDate = courseOverview.getIn(['course_details', 'end_date'], '');
    if (startDate !== '' && endDate !== '') {
      return getMonthsArray(startDate, endDate);
    }

    return [];
  }

  function minMaxDate() {
    const { firstDate, lastDate } = getFirstAndLastDateOfMonth(courseDetails, month);
    return { minDate: firstDate, maxDate: lastDate };
  }

  function handleExport() {
    const params = {
      type: 'student',
      ...(studentGroup.size > 0 && { groupName: studentGroup.toJS() }),
      //gender: gender === '' ? ['male', 'female'] : gender,
      ...(startDate === '' && { firstDate, lastDate }),
      ...(startDate !== '' && { firstDate: startDate }),
      ...(endDate !== '' && { lastDate: endDate }),
    };

    function exportData(data) {
      exportExcel({
        filters,
        activeInstitutionCalendar,
        courseOverview,
        authData: loggedInUserData,
        studentGroup: getActiveStudentGroup(),
        detailedData: data,
        programId,
      });
    }

    getAttendanceLogExport({
      institutionCalendarId: activeInstitutionCalendarId,
      programId,
      courseId,
      level,
      term,
      params,
      exportData,
    });
  }

  return (
    <div className="mt-3 mb-3 bg-white border-radious-8">
      <AttendanceLogFilters
        options={getFilterOptions()}
        filters={filters}
        handleChange={setFilters}
        handleExport={handleExport}
        isExportActive={CheckPermission(
          'tabs',
          'Reports and Analytics',
          'Course Details',
          '',
          'Attendance Log',
          'Export'
        )}
        monthArray={getMonthArray()}
        handleTableFilter={handleTableFilter}
        setFilters={setFilters}
        tableFilter={tableFilter}
        minMaxDate={minMaxDate()}
      />
      <StaffStudentAttendanceLog
        viewTypeOptions={getViewTypes()}
        activeViewType={filters.get('viewType', '')}
        activeGender={filters.get('gender', '')}
        searchTerm={filters.get('search', '').toLowerCase()}
        handleViewTypeChange={handleViewTypeChange}
        studentGroup={getActiveStudentGroup()}
        tableFilter={tableFilter}
        month={month}
      />
      {/* <AttendanceLogExportModal
        open={dialogData.get('open')}
        options={dialogData.get('options', Map())}
        handleOptionsChange={handleOptionsChange}
        onClose={handleDialogClose}
        handleExportClick={handleExportClick}
        studentGroupOptions={getStudentGroupOptions()}
        errors={dialogData.get('errors', Map())}
        course={attendanceLog.get('course_details', Map())}
      /> */}
    </div>
  );
}

AttendanceLog.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  attendanceLog: PropTypes.instanceOf(Map),
  getAttendanceLog: PropTypes.func,
  resetMessage: PropTypes.func,
  setData: PropTypes.func,
  courseOverview: PropTypes.instanceOf(Map),
  loggedInUserData: PropTypes.instanceOf(Map),
  getAttendanceLogExport: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    attendanceLog: selectAttendanceLog(state),
    courseOverview: selectCourseOverviewData(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps, actions)(AttendanceLog);
