import React, { useEffect, useState } from 'react';
import SettingsIcon from '@mui/icons-material/Settings';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useStylesFunction } from '../../css/designUtils';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Menu,
  MenuItem,
  IconButton,
} from '@mui/material';
import PropTypes from 'prop-types';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import * as actions from '../../../../_reduxapi/assessment/action';

import AddEditAssessmentTypeModal from '../../modals/AddEditAssessmentType';
import AssessmentHierarchySettingsModal from '../../modals/AssessmentHierarchySettings';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { selectAssessmentTypes } from '../../../../_reduxapi/assessment/selector';
import { List, Map } from 'immutable';
import DeleteModal from '../../modals/DeleteModal';
import { ucFirst } from '../../../../utils';
import { CheckPermission } from 'Modules/Shared/Permissions';

const IndividualAssessment = ({
  activeSubType,
  getAssessmentTypesList,
  removeAssessmentType,
  type,
  typeId,
  subType,
  updateAssessmentType,
  assessmentTypes,
  setData,
}) => {
  const [deleteModalState, setDeleteModalState] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState(false);
  const [editShow, setEditShow] = useState(false);
  const assmtTypes = activeSubType.get('assessmentTypes', List());

  const handleCloseDelete = () => {
    setDeleteModalState(false);
  };
  const handleEditModalClose = () => {
    setEditShow(false);
  };
  const DropMenu = ({ assmtType }) => {
    const [anchorEl, setAnchorEl] = useState(null);

    const open = Boolean(anchorEl);
    const ITEM_HEIGHT = 48;

    const handleClose = () => {
      setAnchorEl(null);
    };
    const handleOpenDelete = (event, item) => {
      handleClose();
      setSelectedAssessment(item);
      setDeleteModalState(true);
    };
    const handleEditModalOpen = (e, item) => {
      handleClose();
      setSelectedAssessment(item);
      setEditShow(true);
    };

    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };
    return (
      <div className="">
        <IconButton
          aria-label="more"
          aria-controls="long-menu"
          aria-haspopup="true"
          onClick={handleClick}
          size="small"
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={open}
          onClose={handleClose}
          PaperProps={{
            style: {
              maxHeight: ITEM_HEIGHT * 4.5,
              width: '20ch',
            },
          }}
        >
          {CheckPermission('pages', 'Assessment Management', 'Assessment Type', 'Edit') && (
            <MenuItem onClick={(e) => handleEditModalOpen(e, assmtType)}>Edit</MenuItem>
          )}
          {CheckPermission('pages', 'Assessment Management', 'Assessment Type', 'Delete') && (
            <MenuItem onClick={(e) => handleOpenDelete(e, assmtType)}>Delete</MenuItem>
          )}
        </Menu>
      </div>
    );
  };
  DropMenu.propTypes = {
    assmtType: PropTypes.instanceOf(Map),
  };
  return (
    <>
      {assmtTypes.map((assmtType, assmtIndex) => (
        <div
          key={assmtIndex}
          className="bg-white pl-3 pr-3 pt-2 pb-2 border-radious-4 mb-2 assessment-box"
        >
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <div className="digi-Shared-bg p-3 border-radious-8 alphabet-icon">
                <p className="mb-0 f-15 pl-1 pr-1 text-center alphabet-icon-text">
                  {assmtType
                    .get('name', '')
                    .split(' ', 3)
                    .map((item, index) => {
                      return item.charAt(0);
                    })}
                </p>
              </div>
              <p className="mb-0 pl-3">{assmtType.get('name', '')}</p>
            </div>
            {(CheckPermission('pages', 'Assessment Management', 'Assessment Type', 'Edit') ||
              CheckPermission('pages', 'Assessment Management', 'Assessment Type', 'Delete')) && (
              <>{!assmtType.get('isDefault', false) && <DropMenu assmtType={assmtType} />}</>
            )}
          </div>
        </div>
      ))}
      {deleteModalState && (
        <DeleteModal
          show={deleteModalState}
          cancel={handleCloseDelete}
          assessment={selectedAssessment}
          handleDelete={removeAssessmentType}
          callback={getAssessmentTypesList}
          typeId={typeId}
          type={type.get('typeName', '')}
          subType={subType}
          fileName={selectedAssessment.get('name', '')}
          componentName={'Assessment Type'}
        />
      )}
      {editShow && (
        <AddEditAssessmentTypeModal
          show={editShow}
          handleModalClose={handleEditModalClose}
          selectedSubType={activeSubType.get('typeName', '')}
          selectedType={type.get('typeName', '')}
          typeId={typeId}
          handleSubmit={updateAssessmentType}
          getAssessmentTypesList={getAssessmentTypesList}
          assessment={selectedAssessment}
          assessmentTypes={assessmentTypes}
          mode="edit"
          setData={setData}
        />
      )}
    </>
  );
};
IndividualAssessment.propTypes = {
  getAssessmentTypesList: PropTypes.func,
  assessmentTypes: PropTypes.instanceOf(Map),
  removeAssessmentType: PropTypes.func,
  updateAssessmentType: PropTypes.func,
  setData: PropTypes.func,
  type: PropTypes.instanceOf(Map),
  typeId: PropTypes.string,
  subType: PropTypes.string,
  activeSubType: PropTypes.instanceOf(Map),
  assmtType: PropTypes.instanceOf(Map),
};
const AssessmentSubTypes = ({
  type,
  typeId,
  addAssessmentType,
  getAssessmentTypesList,
  removeAssessmentType,
  updateAssessmentType,
  assessmentTypes,
  setData,
}) => {
  const [addShow, setAddShow] = useState(false);
  const [selectedSubType, setSelectedSubType] = useState(Map());
  const handleAddShow = (item) => {
    setSelectedSubType(item);
    setAddShow(true);
  };
  const handleAddModalClose = () => {
    setAddShow(false);
  };
  const activeSubTypes = type
    .get('subTypes', List())
    .filter((sub, index) => sub.get('isActive', false) === true);

  return (
    <div className="w-100">
      <>
        {activeSubTypes?.map((activeSubType, index) => (
          <div key={index}>
            <div className="d-flex justify-content-between mb-3 mt-3">
              <p className="mb-0 f-15">{ucFirst(activeSubType?.get('typeName', ''))}</p>
              {CheckPermission('pages', 'Assessment Management', 'Assessment Type', 'Add') && (
                <div
                  className="d-flex text-skyblue bold remove_hover"
                  onClick={() => handleAddShow(activeSubType.get('typeName', ''))}
                >
                  <AddIcon />
                  <p className="mb-0 f-15 padding-top-2px">Add New</p>
                </div>
              )}
            </div>
            <IndividualAssessment
              activeSubType={activeSubType}
              removeAssessmentType={removeAssessmentType}
              getAssessmentTypesList={getAssessmentTypesList}
              type={type}
              typeId={typeId}
              subType={activeSubType.get('typeName', '')}
              selectedSubType={selectedSubType}
              updateAssessmentType={updateAssessmentType}
              assessmentTypes={assessmentTypes}
              setData={setData}
            />
          </div>
        ))}
      </>
      {addShow && (
        <AddEditAssessmentTypeModal
          show={addShow}
          handleModalClose={handleAddModalClose}
          selectedSubType={selectedSubType}
          selectedType={type.get('typeName', '')}
          typeId={typeId}
          handleSubmit={addAssessmentType}
          getAssessmentTypesList={getAssessmentTypesList}
          assessmentTypes={assessmentTypes}
          mode="add"
          setData={setData}
        />
      )}
    </div>
  );
};
AssessmentSubTypes.propTypes = {
  getAssessmentTypesList: PropTypes.func,
  assessmentTypes: PropTypes.instanceOf(Map),
  toggleAssessmentSubType: PropTypes.func,
  addAssessmentType: PropTypes.func,
  removeAssessmentType: PropTypes.func,
  updateAssessmentType: PropTypes.func,
  setData: PropTypes.func,
  type: PropTypes.instanceOf(Map),
  typeId: PropTypes.string,
};
const AssessmentTypes = ({
  getAssessmentTypesList,
  assessmentTypes,
  toggleAssessmentSubType,
  addAssessmentType,
  removeAssessmentType,
  updateAssessmentType,
  setData,
}) => {
  const classes = useStylesFunction();
  const typeId = assessmentTypes.get('_id', '');
  const [expanded, setExpanded] = useState(true);
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  useEffect(() => {
    getAssessmentTypesList();
  }, []); // eslint-disable-line

  const [show, setShow] = useState(false);
  const handleShow = () => {
    setShow(true);
  };
  const handleModalClose = () => {
    setShow(false);
  };

  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="container">
        <div className="p-5">
          <div className="d-flex justify-content-between align-items-center">
            <p className="mb-2 bold f-19"> Assessment Types</p>
            {CheckPermission(
              'pages',
              'Assessment Management',
              'Assessment Type',
              'Assessment Hierarchy Settings'
            ) && (
              <div className="d-flex">
                <p className="mb-2 bold f-15 mr-2"> Assessment Hierarchy</p>
                <SettingsIcon onClick={handleShow} className="remove_hover" />
              </div>
            )}
          </div>

          {assessmentTypes.get('types', List()).map((parent, index) => (
            <div className="pt-3" key={index}>
              <Accordion
                expanded={expanded === `panel${index}`}
                onChange={handleChange(`panel${index}`)}
                className={classes.accordionBorderUnset}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon fontSize={`large`} />}
                  aria-controls="panel1bh-content"
                  id="panel1bh-header"
                >
                  <p className="mb-0 f-19">{ucFirst(parent.get('typeName', ''))}</p>
                </AccordionSummary>
                <AccordionDetails>
                  <AssessmentSubTypes
                    type={parent}
                    typeId={typeId}
                    addAssessmentType={addAssessmentType}
                    getAssessmentTypesList={getAssessmentTypesList}
                    removeAssessmentType={removeAssessmentType}
                    updateAssessmentType={updateAssessmentType}
                    assessmentTypes={assessmentTypes}
                    setData={setData}
                  />
                </AccordionDetails>
              </Accordion>
            </div>
          ))}
        </div>
      </div>

      {show && (
        <AssessmentHierarchySettingsModal
          show={show}
          handleModalClose={handleModalClose}
          assessmentTypesList={assessmentTypes}
          toggleAssessmentSubType={toggleAssessmentSubType}
          getAssessmentTypesList={getAssessmentTypesList}
        />
      )}
    </div>
  );
};
const mapStateToProps = function (state) {
  return {
    assessmentTypes: selectAssessmentTypes(state),
  };
};
AssessmentTypes.propTypes = {
  getAssessmentTypesList: PropTypes.func,
  assessmentTypes: PropTypes.instanceOf(Map),
  toggleAssessmentSubType: PropTypes.func,
  addAssessmentType: PropTypes.func,
  removeAssessmentType: PropTypes.func,
  updateAssessmentType: PropTypes.func,
  setData: PropTypes.func,
};
export default compose(withRouter, connect(mapStateToProps, actions))(AssessmentTypes);
