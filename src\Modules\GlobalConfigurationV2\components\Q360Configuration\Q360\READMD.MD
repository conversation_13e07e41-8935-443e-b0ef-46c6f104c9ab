-------------------------Q360 MODULUE-------------------

SECTION NAME- ASSIGNED COURSE IN NEW FORM CREATING MODAL AND EDIT FORM MODAL
FILE NAME: FromconfigurationRedirect.JS
PURPOSE OF MODAL:
IN THIS MODAL WE ARE ASSIGNING COURSE OR PROGRAM OR INSTITUTION BASED ON LEVEL WISE TO ALL FORMS
WHAT ARE THE MAIN STATE WE HAVE IN THIS FILE
STATE1 : pgmDetailsState
WHEN USER SELECT OR DESELECT PROGRAM OR COURS WE ARE MAINTIANING
THAT USER SELECTED DATA IN THIS STATE
STURCTURE:
NOTE: THIS STRUCTURE SAME AS programDetails in redux state
FOR COURSE LEVEL:
{
"65e9b090f0c31800c1fce7ff": {
"program_name": "Medicine Program",
"selectedProgramCount":0,
"constructedProgramCount":10,
"curriculum": {
"65e9b2f8f0c31800c1fd4232": {
"curriculum_name": "Medicine 1.0",
"years": {
"year2": {
"constructedCourseCount":10,
"selectedCourseCount":0,
"courseIds":{
"shared_with_others": [
{
"courseId": "65ea9c7540e1a870ca3f0fed",
"course_code": "OP002",
"course_name": "Orthopedics",
"course_type": "standard",
"shared_with_others": false,
"shared_from_others": false,
"isChecked":false
},
{
"courseId": "65ea9d5240e1a870ca3fd978",
"course_code": "ANT002",
"course_name": "Anatomy",
"course_type": "standard",
"shared_with_others": false,
"shared_from_others": false,
"isChecked":false
}
],
"shared_from":[],
"standard":[],
"selective":[],
}

                    },

                    "year4": {
                        "courseIds": []
                    },

                }
            }
        }
    }

}

FOR PROGRAM LEVEL:
{
"65e9b090f0c31800c1fce7ff": {
"program_name": "Medicine Program",
"selectedProgramCount":0,
"constructedProgramCount":10,
"curriculum": {
"65e9b2f8f0c31800c1fd4232": {
"curriculum_name": "Medicine 1.0",
"isChecked":false
}
}
}
}
state 2:
formData
{
formName: existingData.get('formName', ''),
describe: existingData.get('describe', ''),
}
this is maintaining the forn and descirbe
state 3:
pgmDetailsParentState
THIS IS FOR MAINTAINING THE DATA WHEN USER CLICK CANCEL AND APPLY BUTTON
EXAMPLE:
SUPPOSE LET USER CLICKED THE APPLY BUTTON
THEN AGAIN USER OPEN THE MODAL DO SOME CHANGES AND HIT CANCEL BUTTON , NOW WE SOULD SET THE EXISTING SELECTED DATA INTO STATE
state 4
selectedProgramIds
WE SHOULD MAINTAIN THE PROGRAM IDS RECIVING FROM BACKEND
WHY WE SHOULD MAINTAIN :

WE ARE SHOWING THE DATA USING TWO APIS
FIRST : programList
SECOND: programDetails
INITIALLY WE ARE LISTING PROGRAMS USING PROGRAMLIST
AND WHEN USER EXPAND THE ACCORDION THAT TIME WE SHOULD CALL THE PROGRAM DETAILS API THEN WE SOULD LOAD RESPONSE
BUT WE SHOULD SHOW THE SELECTED COUNT AND PROGRAM IS CHECKED OR NOT
FOR CHECKED - IF PROGRAMID IS INCLUDED in selectedProgramIds checked is true
FOR SELECTEDCOUNT - constructedPgmCreation.get(programId,List()).size

state 5
constructedPgmCreation
we are constructing the matching form recieved from categoryForm
matchingForm structure is
[
{
"_id": "666186c4924b7e6430d2423d",
"categoryFormId": "666186c4924b7e6430d2423a",
"programId": "65606e4f01fe4248e23d151e",
"programName": "Cinematography",
"curriculumId": "65606f2801fe42b1693d199f",
"curriculumName": "Cinemato_001",
"year": "year1",
"courseId": "6579862b7f6b90c1c9379888",
"courseName": "Chemistry",
"courseCode": "Chem100",
"sharedWithOthers": false,
"sharedFormOthers": false,
"courseType": "standard"
},
]
constructedPgmCreation structure is
{
666186c4924b7e6430d2423d:[
{
"_id": "666186c4924b7e6430d2423d",
"categoryFormId": "666186c4924b7e6430d2423a",
"programId": "65606e4f01fe4248e23d151e",
"programName": "Cinematography",
"curriculumId": "65606f2801fe42b1693d199f",
"curriculumName": "Cinemato_001",
"year": "year1",
"courseId": "6579862b7f6b90c1c9379888",
"courseName": "Chemistry",
"courseCode": "Chem100",
"sharedWithOthers": false,
"sharedFormOthers": false,
"courseType": "standard"
},
]
}
state 6:
when user the unchecked the checkbox and it is includes in previous user response(constructedPgmCreation)
then we should maintain the document ids along with programId
structure:
\_program_id:{
\_manipulate_id:'1234567890'
}
functions and uses

constructingMatchingForm(){}
this function convert matchingForm into constructedPgmCreation
matchingForm recieved from categoryForm

fetchApi(){}
this is for fetching programDetails when user expand the accordion

checkedProcessCallBack(){}
when the user click the checkbox
and the checked === true then we should cal the function
we are setting selectedProgramcount from constructingProgramCount
make the course.isChecked =true

accordionIsExpanded(){}\
when

clearAll(){}
when we click clear button we should set the existing state value into current state( pgmDetailsState)

5)onSave(){}
when we click save button
we should set the pgmDetailsState into pgmDetailsParentState
6)handleConstruction(){}
this is for payload construction
7)handleCreateCallback(){}
once update api call done we should this function as a callback get api getCategoryForms
8)handleCreate(){}
we are the update and create api inthis function with validation
9)handleInput(){}
this is formName and describe
act as onChange function handleInput
10)deleteExistingProgramCreationData(){}
line1-setConstructedPgmCreation((prev) => prev.delete(programId))=> this is when we delete program using x icon
(we are listed the programs outside of modal )
note: this program is not there in pgmDetailsState
line2-
selectedProgramIds((prev) => prev.filter((pgmId) => pgmId !== programId)) =>
we are removing programid from selectedProgramids

because programCheckbox is true =>pgmDetailsstate.constructedcount===selectedProgamCount ||
selectedProgramIds.includes(programId)

line 3-
setDeletedManipulatedIds((prev) => prev.set(programId, ids));
(for reference check purpose of deletedManipulatedIds)
11)removingProgramFrom_deletedManipulatedIds(){}
purpose removingProgramFrom_deletedManipulatedIds
12)constructDataCourseWise(){}
this function for construct programDetails response
when we recive from backend
what we are doing in this consturction
setting constructedProgramCount
setting selectedProgramCount =>when user checked the checkbox directly we should set the selectedProgramCount=constructedProgramCount or if user only expand the accordion selectedProgramCount=0
13)set_program_details_from_redux_state(){}
this function is called below condition is met:
when user click the programCheckbox with checked should true and also it program id details should be placed in programDetails
what we are doing:
line1- we check programdetails in redux is already have checked state
if(selectedProgramCount===constructedProgramCount)
then we set directly set redux state in to pgmDetailsState
else
we should set the count
selectedProgramCount=constructedProgramCount in to pgmDetailsState

line2- removingProgramFrom_deletedManipulatedIds()

push_programId_redux_to_local_state(){}

this is for when user does not expend accordion
and also programid is selected previously (is includes in reduxselectedprogramIds)
checked ===true then we will call the function
what we did
1)push programId from reduxselectedprogramIds into selectedprogramid
2)removingProgramFrom_deletedManipulatedIds()
15)incorporate_existing_assigned_course_with_current_redux_response(){}
scenario:
we have 10 course in one program
so user previously selected 3 course (we will get 3 course as 3 document and stored in constructedPgmCreation)
now:
when user expand the accordion we should call the program details api
in this function we are mergin that documentid to the current programDetails response (meanwhile addging 3 documentid from 10 course and documentId named by \_manipulate_id)
16)syncing_previous_selected_checkbox_data_with_current_state
take above incorporate_existing_assigned_course_with_current_redux_response function scenario
we are just maked checked true for 3 existingly selected course into current
redux response
also we are adding 3 count in to selectedProgramcount
17)handleProgramCheckedRedirect(){}
comments added original file wheere it is located
