.program_card {
  box-shadow: 0px 1px 5px 0px #0000005c;
  border-radius: 8px;
  border: 1px solid #eeee;
  min-height: 500px;
}

.bg-lightwhite {
  background: #fafafa;
}

.min_h {
  min-height: 200px;
}

.min_h_500 {
  min-height: 500px;
}

.version_bg {
  padding: 10px 15px 10px 15px;
  color: #007bff;
  background-color: #ffffff;
  border-radius: 16px;
  cursor: pointer;
  border: 1px solid;
}

hr.line_roles {
  margin-left: -205px;
  margin-right: -205px;
  border-top: 2px solid rgb(189 192 195 / 38%);
}

.assignRole {
  background: #107cec;
  color: white;
  padding: 7px 10px 7px 10px;
  border-radius: 50%;
  width: 39px;
  text-align: center;
}

.roles_assign.card-header::before {
  content: '';
}

.roles_assign {
  background: white;
  border: 1px solid #e2e1e1;
}

.body_padding.card-body {
  padding: 0rem !important;
}

.roles_body {
  border-left: 1px solid #e2e1e1;
  border-right: 1px solid #e2e1e1;
  border-bottom: 1px solid #e2e1e1;
  padding: 10px 20px 10px 20px;
}

.roles_remove_icon {
  position: absolute;
  top: 1.1em;
  right: 18px;
  cursor: pointer;
}

.program_sidebar {
  border-top: 1px solid #d0d1d373;
  box-shadow: 2px 5px 5px 0px rgb(0 0 0 / 26%);
  min-height: 500px;
  background: #ffff;
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.rotate_180 {
  transform: rotate(180deg);
}

.justify-content-flex-end {
  justify-content: flex-end;
}

.framework-domain-actions {
  width: 130px;
  text-align: right;
}

.mw-150 {
  min-width: 150px;
}

.min-width-100 {
  min-width: 100px;
}

.min-width-150 {
  min-width: 150px;
}

.max-width-175 {
  max-width: 175px;
}

.max-width-150 {
  max-width: 150px;
}

.max-width-130 {
  max-width: 130px;
}

.max-width-120 {
  max-width: 120px;
}

.border-radius-25 {
  border-radius: 25px !important;
}

.font-size-12 {
  font-size: 12px !important;
}

.shared-course-chip {
  font-size: 16px;
  color: #367fc1;
  background-color: rgba(54, 127, 193, 0.05);
  border: 1px solid rgba(54, 127, 193, 0.31);
  border-radius: 25px;
  padding: 3px 15px;
}

.color-blue, .text-color-blue {
  color: rgb(0, 100, 200);
}

.framework-graph-radio-group {
  background-color: rgba(245, 245, 245, 1);
  border-radius: 10px;
}

.framework-mini-graph {
  width: 100px;
  height: 40px;
  overflow: hidden;
  border-radius: 5px;
}

.program_hover {
  padding: 15px 15px 15px 15px;
  cursor: pointer;
  border-right: 4px solid #ffff;
}

.program_hover:hover {
  background: #edf6ff;
  padding: 15px 15px 15px 15px;
  border-right: 4px solid #4598e6;
}

.subject_hover {
  padding: 15px 15px 15px 15px;
  cursor: pointer;
  /* border-right: 2px solid #d1d5db; */
}

.subject_hover:hover {
  background: #edf6ff;
  padding: 15px 15px 15px 15px;
  /* border-right: 4px solid #4598e6; */
}

.subject_hover_pi {
  padding: 15px 15px 15px 15px;
  cursor: pointer;
}

.subject_hover_pi:hover {
  background: #edf6ff;
  padding: 15px 15px 15px 15px;
}

.program_border {
  border-bottom: 2px solid #d1d5db;
}

.program_active {
  border-right: 4px solid #4598e6;
  background: #edf6ff;
}

.inner_drop_program {
  padding: 15px 15px 15px 15px;
  cursor: pointer;
}

.inner_drop_program:hover {
  background: #edf6ff;
  padding: 15px 15px 15px 15px;
  border-right: 4px solid #4598e6;
}

.inner_drop_active {
  box-shadow: inset 0px 1px 4px 0px rgb(0 0 0 / 24%);
  background: #edf6ff;
  padding: 15px 15px 15px 15px;
  border-right: 4px solid #4598e6;
}

.sub_drop_program {
  background: #f5f6f8;
  padding: 15px 5px 15px 40px;
  cursor: pointer;
}

.sub_drop_program:hover {
  box-shadow: inset 2px 2px 4px 0px rgb(0 0 0 / 24%);
  background: #edf6ff;
  padding: 15px 5px 15px 40px;
  border-right: 4px solid #4598e6;
}

.sub_drop_active {
  box-shadow: inset 2px 2px 4px 0px rgb(0 0 0 / 24%);
  background: #edf6ff;
  padding: 15px 5px 15px 40px;
  border-right: 4px solid #4598e6;
}

.subLevel_drop_program {
  background: #f5f6f8;
  padding: 15px 5px 15px 77px;
  cursor: pointer;
}

.subLevel_drop_program:hover {
  box-shadow: inset 2px 2px 4px 0px rgb(0 0 0 / 24%);
  background: #edf6ff;
  padding: 15px 5px 15px 77px;
  border-right: 4px solid #4598e6;
}

.subLevel_drop_active {
  box-shadow: inset 2px 2px 4px 0px rgb(0 0 0 / 24%);
  background: #edf6ff;
  padding: 15px 5px 15px 77px;
  border-right: 4px solid #4598e6;
}

.tabaligment-program {
  color: #a59f9f !important;
  text-align: center;
  padding: 13px 22px;
  text-decoration: none;
  font-size: 17px;
  border-bottom: 3px solid #cecccc;
}

.tabaligment-program:hover {
  color: #005cbf;
  text-decoration: none;
  border-bottom: 3px solid #005cbf;
}

.tabactive-program {
  border-bottom: 3px solid #005cbf;
  color: #005cbf !important;
}

.mt--10 {
  margin-top: -10px;
}

.mr--10 {
  margin-right: -10px;
}
.ml--10 {
  margin-left: -10px;
}

.digi-font-500 {
  font-weight: 500;
}

.digi-black {
  color: rgba(0, 0, 0, 0.87);
}

.digi-dark-black {
  color: #333333;
}

.digi-grey-btn {
  background: #dddddd;
  border-radius: 4px;
  border-color: #dddddd !important;
}

.digi-grey-btn:hover {
  background: #dddddd;
}

.digi-set-row {
  padding: 25px 15px;
  box-sizing: border-box;
}

.digi-brown {
  color: #666666;
}

.digi-circle-data {
  border-radius: 50%;
  padding: 2px 9px;
  background: #999999;
  color: #ffffff;
}

.circle-active {
  background: #3e95ef;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.25);
}

.digi-bdr {
  border-bottom: 1px solid #d3d3d3;
  width: 80px;
  margin: 10px;
}

.digi-border-top {
  border-top: 1px solid transparent;
}

.digi-blue {
  color: #3e95ef;
}

.digi-gray {
  color: #6b7280;
}

.digi-table {
  background: #fafafa;
  border: 1px solid #eeeeee;
  box-sizing: border-box;
  border-radius: 5px;
}

.digi-tbl-pad {
  padding: 0 14px 10px 40px;
  box-sizing: border-box;
}

.badge_grouping_white {
  border: 1px solid #707070;
  color: #666666;
  font-size: 12px;
}

/* .badge_grouping {
  padding: 10px 15px 10px 15px;
  box-sizing: border-box;
  cursor: pointer;
  border-radius: 15px;
  background: white;
} */

.badge_group_active {
  border: 2px solid #3e95ef;
  color: #333333;
}

.badge_group_normal {
  letter-spacing: 0.15px;
  color: #333333;
  border: 1px solid #dddddd;
  font-weight: normal;
}

.digi-add-btn button.dropdown-toggle.btn.btn-primary,
.digi-add-btn button.dropdown-toggle.btn.btn-primary:active {
  background: white;
  border: 1px solid transparent;
  color: rgba(0, 0, 0, 0.54);
  box-shadow: none;
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
}

.digi-lite-brown {
  color: rgba(0, 0, 0, 0.54);
}

.digi-add-btn .dropdown-toggle::after {
  display: none;
}

.digi-share-btn .dropdown-toggle::after {
  display: none;
}

.digi-share-btn button.dropdown-toggle.btn.btn-primary,
.digi-share-btn button.dropdown-toggle.btn.btn-primary:active {
  color: rgba(0, 0, 0, 0.54);
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  background: #fafafa;
  border: 1px solid transparent;
  box-shadow: none;
}

.digi-p-10 {
  padding: 10px;
}

.mt--5 {
  margin-top: -5px;
}

.digi-configur-pad {
  padding: 0px 10px 10px 45px;
}

.digi-set-table {
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  padding: 25px 16px;
  margin: 1px 6px;
  background: #fff;
  border: 1px solid #e6e7e8;
}

.digi-set-table:hover {
  background: #edf6ff;
}

.collegeView {
  display: none;
}

.digi-set-table:hover .collegeView {
  display: unset;
}

.digi-ac {
  align-items: center;
}

.digi-list-item {
  border: 1px solid #b0b0b0;
  box-sizing: border-box;
  padding: 4px 15px;
  background: white;
}

.digi-list-item.active {
  background: #edf6ff;
}

.digi-list-item .img-active {
  color: #3e95ef;
}

.digi-unset {
  box-shadow: none;
  border-radius: unset;
}

.digi-bdr-btm {
  border-bottom: 1px solid #eeeeee;
}

.bg-dark-grey {
  background: #e5e5e5;
}

.border-right-1 {
  border-right: 1px solid #b0b0b0;
}

.digi-curriculum th {
  border: 1px solid #e3e3e3 !important;
  text-align: center;
  color: #666666 !important;
  font-weight: bold !important;
  font-size: 16px !important;
}

.digi-curriculum td {
  border-top: 0px solid !important;
  text-align: center;
  overflow: hidden;
}

.digi-rowspan {
  border: 1px solid #e3e3e3;
  font-size: 16px;
  width: 100px;
  color: #666666;
  vertical-align: middle !important;
  text-align: center !important;
  background: #ffffff;
}

.digi-curriculum-section {
  text-align: center;
  border-top: 4.5px solid #999999;
  background: #ffffff;
  padding: 15px;
  margin-bottom: -99999px;
  padding-bottom: 99999px;
}

.digi-curriculum-section:hover {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-top: 4.5px solid #3e95ef;
  cursor: pointer;
}

.digi-curriculum-section div:nth-child(1) {
  font-weight: bold;
  font-size: 16px;
}

.digi-curriculum-section div:nth-child(2) {
  font-size: 16px;
  color: #5a5a5a;
}

.digi-curriculum-section div:nth-child(3) {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.38);
}

.digi-view-icons i {
  padding: 12px 22px;
}

.digi-view-icons {
  border: 1px solid #b0b0b0;
  border-radius: 4px;
}

.digi-view-icons {
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.digi-view-icons .active {
  background: #d1f4ff;
  color: #3e95ef;
  border-radius: 4px;
  cursor: pointer;
}

/* button#dropdown-basic-button {
  background: transparent;
  color: rgba(0, 0, 0, 0.54);
  border: 0px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.54);
  box-shadow: none;
  border-radius: 0px;
} */
.digi-group-course {
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.05);
  /* padding: 0 45px; */
}

.digi-sno-box {
  background: rgba(0, 0, 0, 0.06);
  width: fit-content;
  border-radius: 3px;
  padding: 3px 5px;
}

.digi-group-course input[type='checkbox'] {
  width: 18px;
  height: 18px;
}

.digi-ungroup-btn {
  color: #3e95ef;
  font-weight: 500;
  text-transform: uppercase;
  cursor: pointer;
}

#digi-table {
  overflow: auto;
  display: inline-block;
  width: 100%;
}

.digi-table-header {
  padding: 0 10px;
  background: #efefef;
}

.bg-lightgray {
  background: #efefef;
  padding-top: 10px;
}

.digi-table-body {
  padding: 10px;
}

.digi-table-body-content {
  width: max-content;
  width: fit-content;
}

.digi-table-body-content:hover,
.if-checked {
  background: #d1f4ff !important;
  cursor: pointer;
}

.tds {
  min-width: 150px;
  max-width: 150px;
}

.td0 {
  max-width: 18px;
  min-width: 18px;
  height: 18px;
  margin: 15px;
}

.td1 {
  min-width: 50px;
  text-align: justify;
}

.td4 {
  min-width: 200px;
  max-width: 200px;
}
.td5 {
  min-width: 200px;
}

.td7 {
  min-width: 125px;
  max-width: 125px;
}

.if-grouped {
  background: #edf6ff;
  width: fit-content;
  width: max-content;
}

.if-grouped input[type='checkbox'] {
  display: none;
}

.if-grouped .digi-table-body-content,
.if-grouped .digi-table-body-content:hover {
  background: #ffffff !important;
}

.course_master {
  background: white;
  padding: 11px 3px 1px 3px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.close_badge {
  background: #efeeee;
  padding: 5px 10px 5px 10px;
  border-radius: 25px;
  font-size: 14px;
}

.master_text {
  margin-bottom: 0px;
  font-size: 14px;
  font-weight: 500;
}

.bold {
  font-weight: 500 !important;
}

.course_master_card {
  padding: 10px 19px 10px 20px;
}

.session_card {
  padding: 20px 25px 15px 25px;
}

.mt--2 {
  margin-top: -2px;
}

.mt--18 {
  margin-top: -18px;
}

.w-100px {
  width: 100px;
}

.w-60px {
  width: 60px;
}

.w-150px {
  width: 150px;
}

thead.th-graychange {
  background: #f5f6f8 !important;
  border-bottom: 3px solid #52a5ee;
}

#dropdown-basic {
  padding: 1px 4px;
  border-radius: 8px;
  color: #2589e1;
  font-size: 14px;
}

#dropdown-basic-text {
  padding: 1px 4px;
  border-radius: 8px;
  color: #2589e1;
  font-size: 14px;
  min-width: 70px;
}

.framework-dropdown-item.active {
  background-color: #007bff !important;
  color: #ffffff;
}

.framework-course-slo {
  padding-left: 4.5rem;
}

.pt-3px {
  padding-top: 3px;
}

span.frameWork_head {
  font-size: 13px;
  font-weight: 400;
  /* word-wrap: break-word; */
}

.disable_framwork {
  pointer-events: none;
  opacity: 0.5;
}

.borderBottom_blue {
  border-bottom: 2px solid #52a5ee;
}

.custome_table {
  padding: 10px 20px 10px 20px;
  border-bottom: 1px solid #babbbbc7;
}

.custome_table:hover {
  background-color: #d1f4ff80;
}

/* width */
.program-table::-webkit-scrollbar {
  height: 9px;
  width: 9px;
}

/* Handle */
.program-table::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.program-table::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.program-table-height::-webkit-scrollbar {
  width: 9px;
}

/* Handle */
.program-table-height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.program-table-height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.program-table-width::-webkit-scrollbar {
  height: 7px;
}

/* Handle */
.program-table-width::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.program-table-width::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.program-table-height {
  overflow-y: auto;
  /* max-height: 250px; */
  /* max-height: 340px; */
}

.program-table {
  overflow-x: auto;
  width: 100%;
}

.program-table table {
  width: 100%;
}

.program-table table tbody {
  display: block;
  min-height: 150px;
  overflow-x: hidden;
}

.program-table table thead {
  display: table;
}

.program-table table tfoot {
  display: table;
}

.program-table table thead tr,
.program-table table tbody tr,
.program-table table tfoot tr {
  display: table-row;
}

/* .program-table table th,
.program-table table td { 
  white-space: nowrap; 
} */

.program-table .aw-50 {
  min-height: 1px;
  width: 50px;
  word-wrap: break-word;
  text-align: center;
}

.program-table .aw-75 {
  min-height: 1px;
  width: 75px;
  word-wrap: break-word;
  text-align: center;
}

.program-table .aw-100 {
  min-height: 1px;
  width: 100px;
  word-wrap: break-word;
}

.program-table .aw-150 {
  min-height: 1px;
  width: 150px;
  word-wrap: break-word;
}

.program-table .aw-200 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}

.program-table .aw-300 {
  min-height: 1px;
  width: 300px;
  word-wrap: break-word;
}

.program-table .aw-400 {
  min-height: 1px;
  width: 400px;
  word-wrap: break-word;
}

.program_select {
  height: 34px !important;
  padding: 0px 4px 1px 4px;
  border: 1px solid #52a5ee;
  background: #00ffff00;
  border-radius: 22px;
}

.program_select:focus {
  background: #00ffff00;
}

.content-map-select-border,
.content-map-select-border:focus {
  border: 1px solid #07c07e;
}

.content-map-select-border-disabled {
  border: 1px solid rgba(0, 0, 0, 0.06);
  color: rgba(0, 0, 0, 0.06);
}

.content-map-mapped-value {
  color: rgba(0, 0, 0, 0.54);
  font-weight: bold;
  font-size: 1rem;
}

.vertical-align-middle {
  vertical-align: middle !important;
}

.bg_green_I,
.bg_green_I:focus {
  background: rgba(137, 233, 204, 1);
  color: #ffffff;
}

.bg_green_P,
.bg_green_P:focus {
  background: rgba(39, 232, 174, 1);
  color: #ffffff;
}

.bg_green_A,
.bg_green_A:focus {
  background: rgba(0, 232, 148, 1);
  color: #ffffff;
}

.bg_blue_H,
.bg_blue_H:focus {
  background: rgba(22, 136, 254, 1);
  color: #ffffff;
}

.bg_blue_M,
.bg_blue_M:focus {
  background: rgba(99, 176, 255, 1);
  color: #ffffff;
}

.bg_blue_L,
.bg_blue_L:focus {
  background: rgba(164, 209, 255, 1);
  color: #ffffff;
}

.color-black {
  color: #000000;
}

.color-black-1 {
  color: #1a1919;
}

.color-partial-black {
  color: rgba(0, 0, 0, 0.54);
}

.mt--20 {
  margin-top: -20px;
}

.border_all {
  border-bottom: 1px solid #cfcfd291;
  border-right: 1px solid #cfcfd261;
}

.border_all_bottom {
  border-bottom: 1px solid #cfcfd291;
}

.border-radius-4 {
  border-radius: 4px;
}

.flex-grow-1 {
  flex-grow: 1;
}

.input-clear-suffix {
  width: 50px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 18px;
}

.dropdown-categorised-section {
  background-color: #ffffff;
}

.dropdown-categorised-ul {
  background-color: inherit;
  padding: 0;
}

.dropdown-title {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.5);
  padding: 10px 15px 0px;
}

.dropdown-subtitle {
  color: #333333;
  padding: 5px 15px 5px;
  font-weight: 500 !important;
}

.badge-cursor {
  cursor: default;
}

.department-subject-badge:hover {
  border: 2px solid #3e95ef;
  color: #333333;
}

.department-subject-badge-delete {
  display: none;
}

.department-subject-badge:hover .department-subject-badge-delete {
  display: unset;
}

.table-action-button-container {
  visibility: hidden;
}

.table-row-item:hover .table-action-button-container {
  visibility: unset;
}

.error-container {
  text-align: center;
  margin-top: 10%;
}

.border_bootom {
  border-bottom: 3px solid #3e95ef;
}

.border_right_dark {
  border-right: 1px solid #ababaf;
}

.border_right_dark_left {
  border-right: 1px solid #ababaf;
  position: relative;
  left: -5px;
}

.bg_blue_H {
  background: #1688fe;
  color: white;
}

.bg_blue_M {
  color: white;
  background: #63b0ff;
}

.bg_blue_L {
  background: #a4d1ff;
  color: white;
}

.program_circle_header {
  border: 1px solid #dadadc;
  border-radius: 68px;
}

.program_tabheader {
  color: #3d5170;
  border-bottom: 7px solid white;
}

.program_menu {
  margin-left: -40px;
  margin-bottom: 5px;
}

.program_tab.active {
  color: #514f4f !important;
  text-align: center;
  padding: 10px 15px 15px 15px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  background: white;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.program_tab {
  color: #3e95ef !important;
  text-align: center;
  padding: 10px 15px 20px 15px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.digi-circle-data2 {
  border-radius: 50%;
  padding: 3px 7px;
  background: #3e95ef;
  color: #ffffff;
}

dot-pink {
  display: inline-block;
  height: 8px;
  width: 8px;
  margin-right: 8px;
  border-radius: 5px;
  background: #ff0099;
}

.dot-blue {
  display: inline-block;
  height: 8px;
  width: 8px;
  margin-right: 8px;
  border-radius: 5px;
  background: #7000ff;
}

.mt--11 {
  margin-top: -11px !important;
}

.min-height-0 {
  min-height: 0px !important;
}

.pt-25 {
  padding-top: 25px !important;
}

.faStyle {
  color: #6d7175;
  font-size: 25px !important;
  cursor: pointer;
  padding-right: 5px;
  padding-bottom: 10px;
}

.faStyle:hover {
  color: #007bff;
}

.defaultLevel {
  font-size: 20px;
  padding-top: 15px;
}

.white-space-normal {
  white-space: normal !important;
}

.dot-circle {
  font-size: 5px !important;
  padding: 0px 5px;
}

.text-mandatory {
  font-style: italic;
  font-weight: 500;
  font-size: 15px;
  line-height: 24px;
  float: right;
  letter-spacing: 0.15px;
  color: #c9c9c9;
  padding-top: 5px;
}

.version_badge {
  background: rgba(54, 127, 193, 0.05);
  border: 1px solid rgba(54, 127, 193, 0.31);
  box-sizing: border-box;
  border-radius: 60px;
  color: #367fc1;
  cursor: pointer;
}

.faStyleDisabled {
  font-size: 24px !important;
  color: #dddddd;
}

.tabactive-program {
  border-bottom: 3px solid #005cbf;
  color: #333333 !important;
}

.framework_label {
  width: 100%;
  text-align: center;
  padding-bottom: 5px;
}

.yearList {
  border-bottom: 1px solid #dee2e6 !important;
}

.yearList:last-child {
  border: none !important;
}

.font-style-italic {
  font-style: italic;
}

/* width */
#digi-table::-webkit-scrollbar {
  width: 5px;
  height: 7px;
}

/* Handle */
#digi-table::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
#digi-table::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.version_bg1 {
  background: rgba(54, 127, 193, 0.05);
  border: 1px solid rgba(54, 127, 193, 0.31);
  box-sizing: border-box;
  border-radius: 60px;
  font-size: 18px;
  line-height: 21px;
  color: #367fc1;
}

.cursor-default {
  cursor: default !important;
}

.border-none {
  border: none;
}

.word-wrap-break-word {
  word-wrap: break-word;
}

.overflow_height {
  overflow-y: auto;
  min-height: 450px;
  max-height: 451px;
  overflow-x: hidden;
}

/* Handle */
.overflow_height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.overflow_height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.overflow_height::-webkit-scrollbar {
  width: 10px;
}

/* Handle */
.overflow_height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.graph_shadoww {
  box-shadow: 2px 2px 3px 1px rgb(0 0 0 / 13%);
  border: 1px solid #dedddd;
  border-radius: 5px;
}

.graph_active {
  border: 2px solid #3e95ef;
}

.select_padding {
  padding: 5px 6px 4px 6px;
  height: 34px;
}

.select_domain {
  padding: 5px 6px 4px 6px !important;
  height: 34px !important;
}

.PrivateSwitchBase-root-5 {
  padding: 0px 4px 0px 14px !important;
}

.learning_outcome_table_height {
  overflow-y: auto;
  max-height: 400px;
}

.buttonAsLink {
  background: none !important;
  border-top: none;
  border-left: none;
  border-right: none;
  outline: none !important;
}

/* survey-style */

.setting-radio {
  -webkit-appearance: none;
  appearance: none;
  display: inline-block;
  position: relative;
  background-color: #ffffff;
  color: #fff;
  top: 4px;
  height: 18px;
  width: 18px;
  border: 1px solid #707070;
  border-radius: 3px;
  cursor: pointer;
  outline: none;
}

.setting-radio:hover {
  border: 1px solid #3d5170;
  background-color: #d5cacb8c;
}

.border-right-2 {
  border-right: 2px solid #dee2e6 !important;
}

.survey-highlight {
  background: rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.survey-back-btn {
  cursor: pointer;
  color: #147afc;
}

.digi-bdr-survey {
  border-bottom: 1px dashed #999999;
  width: 80px;
  margin: 10px;
}

.survey-circle-data {
  border-radius: 50%;
  padding: 4px 10px;
  background: #ffffff;
  color: #147afc;
  border: 1px solid #147afc;
}

.survey-custom-form-control {
  display: block;
  width: 70px;
  padding: 0px 10px;
  position: relative;
  bottom: 7px;
  background: #edf6ff;
  border: 1px solid #147afc;
  box-sizing: border-box;
  border-radius: 16px;
}

.survey-radio-label {
  font-size: 14px;
  font-variant-caps: all-small-caps;
  color: rgba(0, 0, 0, 0.54);
}

.survey-radio-large {
  height: 20px;
  width: 20px;
}

.range-color {
  color: #007bff;
  width: 24px;
  height: 24px;
}

.height_75px {
  height: 75px;
}
#height_25px {
  height: 25px;
}

.survey-input-label {
  font-variant-caps: all-small-caps;
  color: rgba(0, 0, 0, 0.54);
}

.ca-switch {
  background: #edf6ff;
  border: 1px solid #147afc;
  box-sizing: border-box;
  border-radius: 16px;
  padding-left: 10px;
  color: #147afc;
  font-size: 14px;
}

.ca-switch-disable {
  border: 1px solid rgba(0, 0, 0, 0.38);
  box-sizing: border-box;
  border-radius: 16px;
  padding-left: 10px;
  color: rgba(0, 0, 0, 0.38);
  font-size: 14px;
}

.digi-circle-check {
  border-radius: 50%;
  padding: 8px 10px;
  background: #999999;
  color: #ffffff;
}

.digi-circle-empty {
  border-radius: 50%;
  padding: 8px 16px;
  background: #ffffff;
  border: 1px solid #d1d5db;
}

.circle-check {
  background: #3e95ef;
  box-shadow: 0px 1px 1px rgb(0 0 0 / 25%);
  color: #ffffff;
  border: 1px solid #3e95ef;
}

.digi-bdr-solid {
  border-top: 1px solid #147afc;
  width: 80px;
  margin: 10px;
}

.digi-bdr-gray {
  border-bottom: 1px solid #6b7280;
  width: 80px;
  margin: 10px;
}

#fullWidth_dropdown {
  width: 100%;
}
/*   */

.program_table {
  position: relative;
  width: 100%;
  z-index: 1;
  margin: auto;
  overflow: auto;
  height: auto;
  min-height: 100px;
}
.program_table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.program_table tbody tr:hover {
  background: #eff9fb;
}

.program_table td,
.program_table th {
  padding: 7px 7px 5px 7px;
  border-bottom: 1px solid #d1d5db;
  /* background: #fff; */
  vertical-align: middle;
}

.program_table thead th {
  background: #fff;
  color: #000;
  position: sticky;
  top: 0;
  vertical-align: middle;
}
/* safari and ios need the tfoot itself to be position:sticky also */
.program_table tfoot,
.program_table tfoot th,
.program_table tfoot td {
  position: sticky;
  bottom: 0;
  background: #fff;
  color: #000;
  z-index: 4;
}

.program_table th:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 2;
}
.program_table thead th:first-child,
.program_table tfoot th:first-child {
  z-index: 5;
}

.bg_lightRed {
  background: #fff0f1 !important;
}

/* Handle on hover */
.program_table::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}
.program_table::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Handle */
.program_table::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
select.select-text.ovel_dropdown {
  border: 1px solid #757575;
  border-radius: 16px;
  padding: 4px 4px 4px 9px;
  font-size: 15px;
}
button:focus {
  outline: 0px dotted !important;
  outline: 0px auto -webkit-focus-ring-color !important;
}
.border-bottom-dark {
  border-bottom: 1px solid #c4c4c4 !important;
}
.accordion_hover {
  padding: 13px 36px 13px 36px;
  background: #f9fafb;
  border-radius: 3px;
  border-bottom: 1px solid #d1d5db;
}
.accordion_table {
  padding: 13px 18px 13px 18px !important;
}
.accordion_hover:hover {
  background: #eff9fb;
}
.accordion_active {
  background-color: #eff9fb !important;
}
.delivery_first {
  flex-basis: 22.33%;
  flex-shrink: 0;
}
/* .MuiPaper-root, */
.bg-box {
  background-color: #eff9fb;
  border-radius: 8px;
}

.border-blue {
  border: 1px solid #d1d5db;
}

.curr2 {
  background-color: #ffffff !important;
  padding: 0 !important;
}

/* .MuiFormControl-root {
  min-width: 100% !important;
} */

.w-43 {
  width: 43%;
}

.departView {
  padding: 7px 10px 7px 10px;
  border: 1px solid #dcdcdc;
  border-radius: 35px;
  margin: 0px 10px 3px 0px;
  cursor: pointer;
}
.departView:hover {
  background: #eff9fb;
  border: 1px solid #d1d5db;
}
.departView.active {
  background: #eff9fb;
  border: 1px solid #d1d5db;
  margin: 0px 10px 3px 0px;
  cursor: pointer;
}
.pi_sidebar {
  width: 240px;
  min-height: calc(700px - 81px);
}
.pi_sidebar_right_border {
  height: auto;
  border-right: 2px solid #d1d5db !important;
}
.p-15 {
  padding: 15px;
}
/* .MuiInput-underline:before {
  border-bottom: none !important;
} */
.padding-6 {
  padding-top: 6px;
}
.padding-3 {
  padding-top: 3px;
}
.border-waring {
  border: 1px solid #fbbf24;
  color: #fbbf24;
  padding: 1px 4px 1px 4px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  margin-right: 10px;
}
.border-gray {
  border: 1px solid #374151;
  color: #374151;
  padding: 1px 4px 1px 4px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  margin-right: 10px;
}
.br-10 {
  border-radius: 10px;
}
.digi-new-border {
  border-bottom: 1px solid #d3d3d3;
  width: 80px;
  margin: 0px 12px 17px 12px;
}
.custom-checkbox-root .MuiSvgIcon-root {
  width: 19px;
  height: 19px;
}
.PrivateSwitchBase-root-31 {
  padding: 1px 11px 0px 11px !important;
}
.MuiCheckbox-colorPrimary.Mui-checked {
  color: #147afc !important;
}
.MuiTab-textColorPrimary.Mui-selected {
  color: #2a7afc !important;
}
.PrivateTabIndicator-colorPrimary-4 {
  background-color: #147afc;
}
.advanceSetting {
  padding: 5px 10px 5px 10px;
  border: 1px solid #f0f0f0;
  border-radius: 22px;
  margin-right: 15px;
  background: rgba(0, 0, 0, 0.06);
  font-size: 15px;
  color: #000000de;
}
.custom.progress {
  height: 10px;
}
.MuiBadge-colorError {
  color: #fff !important;
  background-color: #9ca3af !important;
}
.emptyBox {
  height: 30px;
  width: 30px;
  border-radius: 4px;
}
.share_icon {
  background: #e1f5fa;
  border-radius: 3px;
  padding: 3px 6px 3px 6px;
}
.share_icon_none {
  border-radius: 3px;
  padding: 6px 6px 3px 6px;
}
.w-30px {
  width: 30px;
}
.box_lightShadow {
  background-color: #f9fafb;
  padding: 12px;
}
.custom_padding {
  padding: 12px 15px 12px 15px;
}
.addTheme_first {
  flex-basis: 48%;
  flex-shrink: 0;
  padding-right: 10px;
}
.addTheme_second {
  flex-basis: 22%;
  flex-shrink: 0;
  padding-right: 10px;
}
.addTheme_third {
  flex-basis: 10%;
  flex-shrink: 0;
}
.addTheme {
  padding: 20px;
  border-radius: 4px;
  background: #f3f4f6;
  cursor: pointer;
  color: #3e95ef !important;
  font-size: 17px;
  margin-top: 20px;
}
.addTheme:hover {
  background: #e0e2e7;
  font-size: 18px;
}
.addSubTheme_bg {
  background: #f3f4f6 !important;
}
.delivery_type_inner_table_padding {
  padding: 13px 52px;
}
.staffTab {
  border: 0px solid #80808000 !important;
  width: 50%;
  text-transform: capitalize;
}
@media only screen and (min-width: 768px) and (max-width: 900px) {
  .pi_sidebar {
    width: 200px;
    min-height: calc(1100px - 81px);
  }

  .ipad_width {
    width: 100px;
  }
  .padding-5px {
    padding-left: 5px;
    padding-right: 5px;
  }
  .level_configure {
    width: 30% !important;
  }
  .responsive_Mainview {
    width: 75% !important;
  }
  .staffTab {
    width: auto;
  }
}
@media screen and (min-width: 320px) and (max-width: 767px) {
  .pi_sidebar {
    width: 190px;
    min-height: calc(1200px - 81px);
  }
  .ipad_width {
    width: 100px;
  }
  .level_configure {
    width: 30% !important;
    padding-left: 5px;
    padding-right: 5px;
  }
  .padding-5px {
    padding-left: 5px;
    padding-right: 5px;
  }
  .responsive_Mainview {
    width: 75% !important;
  }
  .staffTab {
    width: auto;
  }
}
@media only screen and (min-width: 1020px) and (max-width: 1200px) {
  .padding-5px {
    padding-left: 5px;
    padding-right: 5px;
  }
  .level_configure {
    width: 25% !important;
  }
  .ipad_width {
    width: 120px;
  }
  .responsive_Mainview {
    width: 75% !important;
  }
  .staffTab {
    width: 65%;
  }
}

.level_configure {
  width: 20%;
}
.responsive_Mainview {
  width: 100%;
}
.courseMasterButton {
  padding-left: 28px !important;
  padding-right: 28px !important;
}
.staffHeaderBorder {
  border-bottom: 1px solid #d1d5db;
  padding: 0px 25px 0px 25px;
}

.digi-refresh {
  background: #ffffff !important;
  box-shadow: 0px 1px 3px rgba(17, 24, 39, 0.2);
  border: 2px solid #147afc !important;
}

.digi-table-border-bottom {
  border-bottom: 1px solid #dee2e6;
}

.digi-reports-h5 {
  color: #4b5563;
  font-size: 24px;
  font-weight: normal;
}

.digi-reports-h6 {
  color: #4b5563;
  font-size: 20px;
  font-weight: 500;
}

.digi-reports-box {
  box-shadow: 0px 1px 3px rgba(17, 24, 39, 0.2);
}

.digi-report-table-border {
  border-top: 1px solid #d1d5db;
}

.digi-typo {
  padding: 10px 0 10px 0;
}

.digi-date-select {
  cursor: pointer;
}

.digi-date-select:hover {
  background: #eff9fb;
}

.configure-course {
  border: 1px solid #374151;
  border-radius: 10px;
  padding: 2px 5px 2px 5px;
  background: white;
}

.configure-course-alert {
  border: 1px solid #f59e0b;
  border-radius: 10px;
  padding: 2px 5px 2px 5px;
  background: white;
}

.drawer-heading-24px {
  color: #374151;
  font-size: 24px;
}
.drawer-heading-14px {
  color: #374151;
  font-size: 14px;
}
.Dashed-border-drawer {
  border: 1px solid #d1d5db;
  border-style: dashed;
  border-radius: 8px;
}
.Attachments-Scroll {
  overflow: auto;
  max-height: calc(100vh - 400px) !important;
}
.Attachments-Scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: #f1f1f1;
}
.Attachments-Scroll::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #a9a9a9;
}
.Img-view-height {
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 55em;
}
.pdf-iframe-height {
  height: 80vh;
}
.close-text-transform {
  text-transform: none !important;
}
.gray-text-tooltip {
  color: #9ca3af;
  font-size: 10px;
}
.gray-text-tooltip-title {
  color: #4b5563;
  font-size: 12px;
}
.image-border-program-inputs {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}
.view-border {
  color: #22c55e;
  background-color: #4b5563;
  border: 1px solid #4b5563;
  border-radius: 4px;
}
.mt--8 {
  margin-top: -8px;
}
