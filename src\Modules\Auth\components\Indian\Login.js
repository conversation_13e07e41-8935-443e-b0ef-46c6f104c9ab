import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Map } from 'immutable';
import { Redirect, with<PERSON>out<PERSON>, <PERSON> } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';
import MButton from 'Widgets/FormElements/material/Button';
import Digiclass_white from 'Assets/Digiclass_white.svg';
import * as Constants from '../../../../constants';
import * as actions from '_reduxapi/actions/index';
import AuthIndex from '../../index';
//import { getUniversityLogo } from '../../../../utils';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import Footer from 'Shared/Footer';
import LocalStorageService from 'LocalStorageService';
import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';
import OtherLoginOptions from './OtherLoginOptions';
import useUnifyServiceHook from 'Hooks/useUnifyServiceHook';
import Loader from 'Widgets/Loader/Loader';

const emailVal = Constants.EMAIL_VALIDATION;

class Login extends Component {
  constructor() {
    super();
    this.state = {
      email: '',
      psw: '',
      delayMsg: true,
      passwordType: 'password',
    };
    this.emailRef = React.createRef();
    this.passwordRef = React.createRef();
  }

  componentDidMount() {
    if (this.emailRef.current !== null) {
      this.emailRef.current.focus();
    }

    this.timer = setTimeout(() => {
      const loadingProgress = LocalStorageService.getCustomCookie('landing-progress');
      console.log('loadingProgress', loadingProgress); //eslint-disable-line
      if (loadingProgress === 'for loading') {
        LocalStorageService.setCustomCookie('landing-progress', '');
      }
    }, 10000);
  }

  componentWillUnmount() {
    // Clean up the timer when the component unmounts
    clearTimeout(this.timer);
  }

  showPassword = () => {
    if (this.passwordRef.current !== null) {
      const type = this.passwordRef.current.type;
      const updatedValue = type === 'password' ? 'text' : 'password';
      this.passwordRef.current.type = updatedValue;
      this.setState({ passwordType: updatedValue });
    }
  };

  onChange = (e, name) => {
    e.preventDefault();
    this.setState({
      [name]: e.target.value.trim(),
    });
  };

  validation = () => {
    const { onSetData } = this.props;
    const { email, psw } = this.state;

    if (!email) {
      onSetData({ message: 'Email is Required' });
      return false;
    }
    if (!emailVal.test(email)) {
      onSetData({ message: 'Please enter Valid Email Address' });
      return false;
    }
    if (!psw) {
      onSetData({ message: 'Password is Required' });
      return false;
    }
    if (psw.length <= 7) {
      onSetData({ message: 'Minimum 8 character is required' });
      return false;
    }

    return true;
  };

  handleSignIn = (e) => {
    e.preventDefault();
    if (this.validation()) {
      const { onAuthLogin } = this.props;
      const authData = {
        email: this.state.email.toLowerCase(),
        password: this.state.psw,
        device_type: 'web',
      };
      onAuthLogin({ ...authData });
    }
  };

  renderForgotPassword(isAdminLogin = false) {
    return (
      <Link exact="true" to="/forgot-password">
        <p
          className={`pt-${isAdminLogin ? '4' : '2'} mb-0 ${
            isAdminLogin ? 'text-center' : 'text-right'
          }`}
        >
          <Trans i18nKey={'forgot'}></Trans>
        </p>
      </Link>
    );
  }

  render() {
    const { passwordType } = this.state;
    const { isAdminLogin, t, unifyData } = this.props;

    let redirectPath = LocalStorageService.getCustomToken('redirectPath');
    let redirectUrl = null;
    if (this.props.isAuthenticated && this.state.delayMsg) {
      if (redirectPath !== '' && redirectPath !== null && redirectPath !== '/login') {
        redirectUrl = <Redirect to={redirectPath} />;
      } else {
        redirectUrl = <Redirect to="/overview" />;
      }
    }
    const loadingProgress = LocalStorageService.getCustomCookie('landing-progress');
    if (loadingProgress !== '') {
      return (
        <React.Fragment>
          You will be redirected to your preferred domain...
          <br />
          <Box sx={{ width: '100%' }}>
            <LinearProgress />
          </Box>
        </React.Fragment>
      );
    }
    return (
      <div>
        <AuthIndex />
        <div>
          {redirectUrl}
          <div className="login-bg">
            <div className="grandParentContainer">
              <div className="parentContainer">
                <div className="row justify-content-center pt-3">
                  <div className="col-12 col-xl-12 col-lg-12 col-md-12">
                    <h3 className="text-center">
                      {' '}
                      <img src={Digiclass_white} alt="DigiClass" width={'70%'} />
                    </h3>
                  </div>
                </div>

                <div className="row justify-content-center">
                  <div className="col-12 col-xl-12 col-lg-12 col-md-12">
                    <div className="outter-login">
                      <div className="row">
                        <div className="col-xl-7 col-lg-7 col-md-7 col-7">
                          <p className="f-20 pt-3 text-skyblue"> Login</p>
                        </div>
                        <div className="col-xl-5 col-lg-5 col-md-5 col-5">
                          {' '}
                          {unifyData.get('collegeLogo', '') !== '' && (
                            <img
                              src={unifyData.get('collegeLogo', '')}
                              alt="Digi-scheduler"
                              className="univ-logo"
                            />
                          )}
                        </div>
                      </div>
                      <React.Fragment>
                        <form>
                          <div className="pt-2">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'text'}
                              placeholder={t('enter_email')}
                              variant={'outlined'}
                              size={'small'}
                              label={<Trans i18nKey={'email'}></Trans>}
                              changed={(e) => this.onChange(e, 'email')}
                              value={this.state.email}
                              elementConfig={{
                                ref: this.emailRef,
                                autoFocus: true,
                              }}
                            />
                          </div>
                          <div className="pt-2 position-relative">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={passwordType}
                              placeholder={t('enter_password')}
                              variant={'outlined'}
                              size={'small'}
                              label={<Trans i18nKey={'password'}></Trans>}
                              changed={(e) => this.onChange(e, 'psw')}
                              value={this.state.psw}
                              elementConfig={{
                                ref: this.passwordRef,
                              }}
                            />

                            <span
                              className={`remove_hover ${
                                process.env.REACT_APP_LNG === 'ar' ? 'p-viewer-ar' : 'p-viewer'
                              }`}
                            >
                              <i
                                className={`fa fa-eye${passwordType === 'text' ? '-slash' : ''}`}
                                aria-hidden="true"
                                onClick={this.showPassword}
                              >
                                {' '}
                              </i>
                            </span>
                            {!isAdminLogin && this.renderForgotPassword()}
                          </div>
                          <div className="pt-3">
                            <MButton
                              type="submit"
                              clicked={this.handleSignIn}
                              fullWidth
                              disabled={unifyData.size === 0}
                            >
                              <Trans i18nKey={'login'}></Trans>
                            </MButton>
                          </div>
                          <OtherLoginOptions unifyData={unifyData} />
                        </form>
                      </React.Fragment>
                    </div>
                  </div>
                </div>
                <div className="row justify-content-center pt-4 pb-2">
                  <Footer />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

Login.propTypes = {
  isAdminLogin: PropTypes.bool,
  isAdmin: PropTypes.bool,
  isInstitutionOnBoarded: PropTypes.bool,
  t: PropTypes.func,
  isAuthenticated: PropTypes.bool,
  history: PropTypes.object,
  onAuth: PropTypes.func,
  authLogin: PropTypes.func,
  onAuthLogin: PropTypes.func,
  onSetData: PropTypes.func,
  loggedInUserData: PropTypes.instanceOf(Map),
};

Login.defaultProps = {
  isAdminLogin: false,
};

const mapStateToProps = (state) => {
  return {
    isAuthenticated: state.auth.token !== null,
    isAdmin: state.auth.isAdmin,
    isInstitutionOnBoarded: state.auth.isInstitutionOnboarded,
    loggedInUserData: selectUserInfo(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    onAuth: (data) => dispatch(actions.auth(data)),
    onAuthLogin: (data) => dispatch(actions.authLogin(data)),
    onSetData: (data) => dispatch(actions.setData(data)),
  };
};

const ConnectedLogin = connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(withTranslation()(Login)));

const LoginWrapper = (props) => {
  const { unifyData, loading } = useUnifyServiceHook();

  return (
    <>
      <Loader isLoading={loading} />
      <ConnectedLogin {...props} unifyData={unifyData} />
    </>
  );
};

export default LoginWrapper;
