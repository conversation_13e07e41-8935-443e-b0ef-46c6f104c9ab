import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import { selectIsLoading, selectMessage } from '../../_reduxapi/program_input/selectors';
import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import Program from './components/Program/Program';
import ProgramConfiguration from './components/Department/ProgramConfiguration';
import AddCourse from './components/Course/AddCourse';
import AssignCourse from './components/Course/AssignCourse';
import AssignedCourseView from './components/Course/AssignedCourseView';
import PloCloSlo from './components/Framework/PloCloSlo';
import Configuration from './components/Configuration/index';
import DepartmentView from './components/Department/DepartmentView';
import '../../Assets/css/grouping.css';
import './css/program.css';
import Framework from './components/Framework/Index';
import ContentMap from './components/Framework/ContentMap';
import CloSloMap from './components/Framework/CloSloMap';
import PloCloMappingTree from './components/Framework/PloCloMappingTree';
import PloCloMap from './components/Framework/PloCloMap';
// import NewProgramInputs from './Design/program_input/index';
// import DepartmentDesign from './Design/program_input/Department';
// import SessionType from './Design/program_input/SessionType';
// import DepartmentSubject from './Design/department_subject/index';
// import AddCurriculum from './Design/addcurriculum/index';
// import CourseMaster from './Design/courseMaster/index';
// import CourseTable from './Design/courseMaster/courseTable';
// import Standard from './Design/courseMaster/standard';
// import AllCurriculum from './Design/addcurriculum/AllCurriculum';
// import Selective from './Design/courseMaster/selective';
// import CourseTab from './Design/courseMaster/courseTab';
// import GroupCourse from './Design/groupCourse/index';
// import AddNewTheme from './Design/theme/index';
// import ThemeView from './Design/theme/themeView';
// import ManageCourse from './Design/manageCourse/index';
// import CourseTabModal from './Design/manageCourse/courseTabModal';
// import BasicDetailsInfo from './Design/configure_levels/BasicDetailsInfo';
// import ConfigureSubject from './Design/configure_levels/ConfigureSubject';
// import AddNewCourse from './Design/configure_levels/AddNewCourse';
// import ConfigureLevel from './Design/configure_levels/ConfigureLevel';
// import AddNewSession from './Design/linkSession/AddNewSession';
// import LinkSessions from './Design/linkSession/LinkSessions';
// import SessionVersion from './Design/linkSession/SessionVersion';
// import CreateNewCurriculum from './Design/addcurriculum/CreateNewCurriculum';
// import NewCurriculum from './Design/addcurriculum/NewCurriculum';
// import CurriculumVersion from './Design/addcurriculum/CurriculumVersion';
// import SessionFlow from './Design/configure_levels/SessionFlow';
// import Register from './Design/staffManagement/register';
// import SingleEntry from './Design/staffManagement/singleEntry';
// import MultipleEntry from './Design/staffManagement/multipleEntry';
// import RegisterModals from './Design/staffManagement/registerModals';
// import ConfigurationTable from './Design/courseConfiguration/ConfigurationTable';
// import ConfigurationModal from './Design/courseConfiguration/ConfigurationModal';
// import DetailsPage from './Design/staffManagement/registerpending-sai/basicDetails';
// import StaffProfile from './Design/staffManagement/signup-kalil/Profile';
// import ValidStaff from './Design/staffManagement/validStaff';
// import IdImageModal from './Design/registrationPendingModals/AllModals';
// import Biometric from './Design/staffManagement/registerpending-sai/biometric';
// import ProfileDetails from './Design/staffManagement/registerpending-sai/profile-details';
// import PendingDetails from './Design/staffManagement/registerpending-sai/pendingDetails';
// import ActivateProfile from './Design/staffManagement/ActivateProfile';
// import DocumentUpload from './Design/staffManagement/documentUpload';
// import PendingAssign from './Design/staffManagement/pendingAssign';
// import UpdateValidation from './Design/staffManagement/updateValidation';
// import AssignRole from './Design/staffManagement/assignRole';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import * as actions from '../../_reduxapi/actions/calender';
import { getEnvCollegeName } from 'utils';
import CourseConfiguration from './components/Course/CourseConfiguration';
class ProgramInput extends Component {
  componentDidMount() {
    this.props.setBreadCrumbName();
  }

  render() {
    const items = [
      {
        to: '#',
        label: getEnvCollegeName(),
      },
    ];
    const { message } = this.props;
    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={this.props.isLoading} />
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <Switch>
          <Route path="/program-input/framework" component={Framework}></Route>
          <Route
            exact
            path="/program-input/frameworks/plo"
            render={(props) => <PloCloSlo {...props} type="plo" />}
          ></Route>
          <Route
            exact
            path="/program-input/frameworks/clo"
            render={(props) => <PloCloSlo {...props} type="clo" />}
          ></Route>
          <Route
            exact
            path="/program-input/frameworks/slo"
            render={(props) => <PloCloSlo {...props} type="slo" />}
          ></Route>
          <Route exact path="/program-input/frameworks/content-map" component={ContentMap}></Route>
          <Route exact path="/program-input/department-view" component={DepartmentView}></Route>
          <Route exact path="/program-input/frameworks/clo-slo-map" component={CloSloMap}></Route>
          <Route
            exact
            path="/program-input/frameworks/plo-clo-mapping-tree"
            component={PloCloMappingTree}
          ></Route>
          <Route exact path="/program-input/frameworks/plo-clo-map" component={PloCloMap}></Route>
          {CheckPermission('pages', 'Program Input', 'Programs', 'View') && (
            <Route exact path="/program-input/program" component={Program}></Route>
          )}
          {/* <Route exact path="/program-input/design/addprogram" component={NewProgramInputs}></Route>
          <Route exact path="/program-input/design/sessionType" component={SessionType}></Route>
          <Route
            exact
            path="/program-input/design/departmentsubject"
            component={DepartmentSubject}
          ></Route>
          <Route exact path="/program-input/design/addcurriculum" component={AddCurriculum}></Route>
          <Route exact path="/program-input/design/department" component={DepartmentDesign}></Route>
          <Route exact path="/program-input/design/coursemaster" component={CourseMaster}></Route>
          <Route exact path="/program-input/design/courseTable" component={CourseTable}></Route>
          <Route exact path="/program-input/design/allcurriculum" component={AllCurriculum}></Route>
          <Route exact path="/program-input/design/standard" component={Standard}></Route>
          <Route exact path="/program-input/design/selective" component={Selective}></Route>
          <Route exact path="/program-input/design/courseTab" component={CourseTab}></Route>
          <Route exact path="/program-input/design/groupCourse" component={GroupCourse}></Route>
          <Route exact path="/program-input/design/theme" component={AddNewTheme}></Route>
          <Route exact path="/program-input/design/themeView" component={ThemeView}></Route>
          <Route exact path="/program-input/design/AddNewSession" component={AddNewSession}></Route>
          <Route exact path="/program-input/design/LinkSessions" component={LinkSessions}></Route>
          <Route
            exact
            path="/program-input/design/SessionVersion"
            component={SessionVersion}
          ></Route>
          <Route
            exact
            path="/program-input/design/CreateCurriculum"
            component={CreateNewCurriculum}
          ></Route>
          <Route
            exact
            path="/program-input/design/CurriculumVersion"
            component={CurriculumVersion}
          ></Route>
          <Route exact path="/program-input/design/NewCurriculum" component={NewCurriculum}></Route>
          <Route
            exact
            path="/program-input/design/ConfigureLevel"
            component={ConfigureLevel}
          ></Route>
          <Route
            exact
            path="/program-input/design/BasicDetailsInfo"
            component={BasicDetailsInfo}
          ></Route>
          <Route exact path="/program-input/design/AddNewCourse" component={AddNewCourse}></Route>
          <Route exact path="/program-input/design/SessionFlow" component={SessionFlow}></Route>
          <Route
            exact
            path="/program-input/design/ConfigureSubject"
            component={ConfigureSubject}
          ></Route>
          <Route exact path="/program-input/design/manageCourse" component={ManageCourse}></Route>
          <Route
            exact
            path="/program-input/design/courseTabModal"
            component={CourseTabModal}
          ></Route>
          <Route
            exact
            path="/program-input/design/configurationTable"
            component={ConfigurationTable}
          ></Route>
          <Route
            exact
            path="/program-input/design/configurationModal"
            component={ConfigurationModal}
          ></Route>
          <Route exact path="/program-input/design/register" component={Register}></Route>
          <Route
            exact
            path="/program-input/design/register-details"
            component={DetailsPage}
          ></Route>
          <Route
            exact
            path="/program-input/design/profile-details"
            component={ProfileDetails}
          ></Route>
          <Route
            exact
            path="/program-input/design/pending-details"
            component={PendingDetails}
          ></Route>
          <Route exact path="/program-input/design/biometric" component={Biometric}></Route>
          <Route exact path="/program-input/design/singleEntry" component={SingleEntry}></Route>
          <Route exact path="/program-input/design/multipleEntry" component={MultipleEntry}></Route>
          <Route
            exact
            path="/program-input/design/registerModals"
            component={RegisterModals}
          ></Route>
          <Route exact path="/program-input/design/validStaff" component={ValidStaff}></Route>
          <Route exact path="/program-input/design/validModal" component={IdImageModal}></Route>
          <Route
            exact
            path="/program-input/design/updateValidation"
            component={UpdateValidation}
          ></Route>
          <Route
            exact
            path="/program-input/design/documentUpload"
            component={DocumentUpload}
          ></Route>
          <Route exact path="/program-input/design/PendingAssign" component={PendingAssign}></Route>

          <Route
            exact
            path="/program-input/design/ActivateProfile"
            component={ActivateProfile}
          ></Route>
          <Route
            exact
            path="/program-input/design/user-management/signup"
            component={StaffProfile}
          ></Route>
          <Route exact path="/program-input/design/assignRole" component={AssignRole}></Route> */}
          {CheckPermission(
            'tabs',
            'Program Input',
            'Programs',
            '',
            'Active Programs',
            'Config View'
          ) && (
            <>
              <Route path="/program-input/configuration" component={Configuration}></Route>
              <Route
                exact
                path="/program-input/program/:id/configure"
                component={ProgramConfiguration}
              ></Route>

              <Route
                exact
                path="/program-input/curriculum/:curriculumId/course/:courseId"
                component={AddCourse}
              ></Route>
              <Route
                exact
                path="/program-input/curriculum/:curriculumId/course/:courseId/assign/:assignedId"
                component={AssignCourse}
              ></Route>
              <Route
                exact
                path="/program-input/curriculum/:curriculumId/course/:courseId/assign/:assignedId/view"
                component={AssignedCourseView}
              ></Route>

              <Route
                exact
                path="/program-input/course/configuration"
                component={CourseConfiguration}
              ></Route>
            </>
          )}
        </Switch>
      </div>
    );
  }
}

ProgramInput.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  setBreadCrumbName: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ProgramInput);
