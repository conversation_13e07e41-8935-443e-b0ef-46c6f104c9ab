import CryptoJS from 'crypto-js';
import { isModuleEnabled } from 'utils';
import LocalStorageService from './LocalStorageService';
import { initializeMsal } from 'authConfig';

const CRYPTO_LOCK = 'ThzWs8tss6fB6AywyQ3Jo8na9m71oeB8';
const IV_SIZE = 16;

export const PUBLIC_KY_PEM =
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6d1cZiQcp5YK4d9dOy1p\
UqfFHZbyx1O6g5isE72LGwA8Z/r5UZGQRlzDiAuAVsNM0G208Dc+h33Tsyqu7XnQ\
OevcnQ3zTABEbtN3dPr1GrvSxEHDsLOiFKSvb4TbLyDaEnzM9CJ/SyFmORE597KB\
P9Aiiui5nyXyGIHrJ3C+MgOBZcap3s9MBRQJrddo2Js5yEBMcN26iAy0Ih6cjQwR\
5Fck0YL8zf+qC1vT5XOQqt/q80qam9I9jgsLwmPg5bFlPJWCgd9R3qxeZuOJ9mWB\
sXgkk3qp3JiZKL4QksDDfl4ps4/rL8hawvp+ujgCETWXCGHLwsuS2MhtPVY0zcZs\
uwIDAQAB';

const encryptedAuthUrls = [
  { route: 'digiclass/user/authLogin', method: 'POST', applicable: ['RESPONSE'] },
  { route: 'user/authLoggedIn/:id', method: 'GET', applicable: ['RESPONSE'] },
  { route: 'user/signup', method: 'POST', applicable: ['RESPONSE'] },
  { route: 'user/set_password', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/register_mobile', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/staff/:id', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/otp_verify', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/student/:id', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'digi_program', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'country', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/profile_update', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'vaccination', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/forget_send_otp', method: 'POST', applicable: ['RESPONSE'] },
  { route: 'user/forget_set_password', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/logout', method: 'POST', applicable: ['REQUEST'] },
];

// Encrypt Data
export const encryptData = ({ content = {}, handledBy = 'SERVER' }) => {
  try {
    const iv = CryptoJS.lib.WordArray.random(IV_SIZE);

    const keyString = handledBy === 'SERVER' ? CRYPTO_LOCK : CRYPTO_LOCK; // Customize keys if needed
    const key = CryptoJS.enc.Utf8.parse(keyString);

    const encryptedContent =
      typeof content === 'object'
        ? JSON.stringify(content)
        : typeof content === 'boolean' || typeof content === 'number'
        ? content.toString()
        : content;

    const encrypted = CryptoJS.AES.encrypt(encryptedContent, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    const combined = encrypted.ciphertext.concat(iv);
    return { data: combined.toString(CryptoJS.enc.Hex) };
  } catch (error) {
    throw new Error('Failed to encrypt data');
  }
};

// Decrypt Data
export const decryptData = ({
  content = '',
  handledBy = 'SERVER',
  parsed = true,
  clearToken = false,
}) => {
  try {
    // Decode the hex-encoded string into a WordArray
    const combined = CryptoJS.enc.Hex.parse(content);

    // Extract the IV (last IV_SIZE_BYTES bytes of the combined data)
    const iv = CryptoJS.lib.WordArray.create(combined.words.slice(-(IV_SIZE / 4)), IV_SIZE);

    // Extract the ciphertext (everything except the IV)
    const ciphertext = CryptoJS.lib.WordArray.create(
      combined.words.slice(0, -IV_SIZE / 4),
      combined.sigBytes - IV_SIZE
    );

    // Prepare the decryption key
    const keyString = handledBy === 'SERVER' ? CRYPTO_LOCK : CRYPTO_LOCK;
    const key = CryptoJS.enc.Utf8.parse(keyString);

    // Decrypt the ciphertext
    const decrypted = CryptoJS.AES.decrypt({ ciphertext }, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    // Convert the decrypted data to a UTF-8 string
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    return parsed ? JSON.parse(decryptedText) : decryptedText;
  } catch (error) {
    logout(clearToken);
    return null;
    // console.error('Error decrypting data:', error);
    // throw new Error('Failed to decrypt data');
  }
};

export const hasEncrypt = () => {
  return isModuleEnabled('ENCRYPT_PAYLOAD');
};

export const getEncryptedRoutes = () => {
  const response = LocalStorageService.getCustomToken('response', true);
  if (response !== null) {
    return response.encryptedRoutes;
  }
  return [];
};

const isAuthEncrypted = () => {
  const response = LocalStorageService.getCustomCookie('unifyData', true);
  return response?.services?.ENCRYPT_PAYLOAD;
};

const checkRouteMatch = (config, isEncryptedRoutes = true) => {
  // const configUrl = config.url.replace(/^\/|\/$/g, '');
  // const routes = isEncryptedRoutes ? getEncryptedRoutes() : encryptedAuthUrls;
  // return routes.find((item) => {
  //   const routeUrl = item.route.replace('/api/v1/', '');
  //   return (
  //     configUrl.startsWith(routeUrl) && item.method.toUpperCase() === config.method.toUpperCase()
  //   );
  // });

  const urlWithoutQueryParams = config.url.split('?')[0];
  const configUrl = urlWithoutQueryParams.replace(/^\/|\/$/g, '');
  const routes = isEncryptedRoutes ? getEncryptedRoutes() : encryptedAuthUrls;
  return routes.find((item) => {
    const routeUrl = item.route.replace('/api/v1/', '');
    // const routeRegex = new RegExp(
    //   `^${routeUrl.toLowerCase().replace(/:\w+/g, '[^/]+')}(?:/[^/]+)*$`
    // );
    const routeRegex = new RegExp(`^${routeUrl.toLowerCase().replace(/:\w+/g, '[^/]+')}$`);
    return (
      routeRegex.test(configUrl.toLowerCase()) &&
      item.method.toUpperCase() === config.method.toUpperCase()
    );
  });
};

export const checkEncryptedRoute = (config, type = 'REQUEST') => {
  const isAuthMatch = checkRouteMatch(config, false);
  if (isAuthMatch) return isAuthMatch?.applicable.includes(type) && isAuthEncrypted();
  if (!hasEncrypt()) return false;

  const routeMatch = checkRouteMatch(config);
  return routeMatch?.applicable.includes(type);
};

const logout = (clearToken) => {
  const ssoId = LocalStorageService.getSSOAccountId();
  const { msalInstance } = initializeMsal();
  if (ssoId) {
    const accounts = msalInstance.getAllAccounts();
    const currentAccount = accounts[0];
    // Automatically on page load
    msalInstance.logoutRedirect({
      account: currentAccount,
      onRedirectNavigate: () => {
        // Return false to stop navigation after local logout
        return false;
      },
    });
  }

  // Step 2: Clear tokens
  if (clearToken === false) {
    LocalStorageService.clearToken();
  }
};

function pemToArrayBuffer(pem) {
  const b64 = pem
    .replace(/-----BEGIN PUBLIC KEY-----/, '')
    .replace(/-----END PUBLIC KEY-----/, '')
    .replace(/\s/g, '');
  const binaryDerString = atob(b64);
  const binaryDer = new Uint8Array(binaryDerString.length);
  for (let i = 0; i < binaryDerString.length; i++) {
    binaryDer[i] = binaryDerString.charCodeAt(i);
  }
  return binaryDer.buffer;
}

function arrayBufferToBase64(buffer) {
  const byteArray = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < byteArray.byteLength; i++) {
    binary += String.fromCharCode(byteArray[i]);
  }
  return btoa(binary);
}

let sessionObject;

export async function generateSessionObject() {
  const rawKey = window.crypto.getRandomValues(new Uint8Array(32)); // 256-bit AES
  const iv = window.crypto.getRandomValues(new Uint8Array(16)); // 128-bit IV

  const cryptoKey = await window.crypto.subtle.importKey(
    'raw',
    rawKey,
    { name: 'AES-CBC' },
    false,
    ['encrypt', 'decrypt']
  );

  sessionObject = {
    sessionKey: rawKey, // Uint8Array
    sessionKeyHex: Array.from(rawKey)
      .map((b) => b.toString(16).padStart(2, '0'))
      .join(''),
    sessionIv: iv,
    sessionIvHex: Array.from(iv)
      .map((b) => b.toString(16).padStart(2, '0'))
      .join(''),
    cryptoKey,
    createdAt: new Date().toISOString(),
    client: process.env.REACT_APP_CLIENT_NAME,
  };

  return sessionObject;
}

await generateSessionObject();

export async function encryptPayload(data) {
  const encoded = new TextEncoder().encode(JSON.stringify(data));

  const encryptedBuffer = await window.crypto.subtle.encrypt(
    { name: 'AES-CBC', iv: sessionObject.sessionIv },
    sessionObject.cryptoKey,
    encoded
  );

  return {
    iv: sessionObject.sessionIvHex,
    data: btoa(String.fromCharCode(...new Uint8Array(encryptedBuffer))),
  };
}

export async function decryptPayload(encryptedBase64) {
  const encryptedBytes = Uint8Array.from(atob(encryptedBase64), (c) => c.charCodeAt(0));
  const decryptedBuffer = await window.crypto.subtle.decrypt(
    { name: 'AES-CBC', iv: sessionObject.sessionIv },
    sessionObject.cryptoKey,
    encryptedBytes
  );
  return JSON.parse(new TextDecoder().decode(decryptedBuffer));
}

export async function encryptSessionKey() {
  const publicKey = await window.crypto.subtle.importKey(
    'spki',
    pemToArrayBuffer(PUBLIC_KY_PEM),
    {
      name: 'RSA-OAEP',
      hash: 'SHA-256',
    },
    true,
    ['encrypt']
  );

  const encrypted = await window.crypto.subtle.encrypt(
    { name: 'RSA-OAEP' },
    publicKey,
    new TextEncoder().encode(
      JSON.stringify({
        sessionKey: sessionObject.sessionKeyHex,
        sessionIv: sessionObject.sessionIvHex,
        createdAt: sessionObject.createdAt,
        client: sessionObject.client,
      })
    )
  );

  return arrayBufferToBase64(encrypted);
}
