import CryptoJS from 'crypto-js';
import { isModuleEnabled } from 'utils';
import LocalStorageService from './LocalStorageService';
import { msalInstance } from 'authConfig';

const CRYPTO_KEY = 'ThzWs8tss6fB6AywyQ3Jo8na9m71oeB8';
const IV_SIZE = 16;

const encryptedAuthUrls = [
  { route: 'digiclass/user/authLogin', method: 'POST', applicable: ['RESPONSE'] },
  { route: 'user/authLoggedIn/:id', method: 'GET', applicable: ['RESPONSE'] },
  { route: 'user/signup', method: 'POST', applicable: ['RESPONSE'] },
  { route: 'user/set_password', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/register_mobile', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/staff/:id', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/otp_verify', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/student/:id', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'digi_program', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'country', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/profile_update', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'vaccination', method: 'GET', applicable: ['REQUEST', 'RESPONSE'] },
  { route: 'user/forget_send_otp', method: 'POST', applicable: ['RESPONSE'] },
  { route: 'user/forget_set_password', method: 'POST', applicable: ['REQUEST', 'RESPONSE'] },
];

// Encrypt Data
export const encryptData = ({ content = {}, handledBy = 'SERVER' }) => {
  try {
    const iv = CryptoJS.lib.WordArray.random(IV_SIZE);

    const keyString = handledBy === 'SERVER' ? CRYPTO_KEY : CRYPTO_KEY; // Customize keys if needed
    const key = CryptoJS.enc.Utf8.parse(keyString);

    const encryptedContent =
      typeof content === 'object'
        ? JSON.stringify(content)
        : typeof content === 'boolean' || typeof content === 'number'
        ? content.toString()
        : content;

    const encrypted = CryptoJS.AES.encrypt(encryptedContent, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    const combined = encrypted.ciphertext.concat(iv);
    return { data: combined.toString(CryptoJS.enc.Hex) };
  } catch (error) {
    throw new Error('Failed to encrypt data');
  }
};

// Decrypt Data
export const decryptData = ({ content = '', handledBy = 'SERVER', parsed = true }) => {
  try {
    // Decode the hex-encoded string into a WordArray
    const combined = CryptoJS.enc.Hex.parse(content);

    // Extract the IV (last IV_SIZE_BYTES bytes of the combined data)
    const iv = CryptoJS.lib.WordArray.create(combined.words.slice(-(IV_SIZE / 4)), IV_SIZE);

    // Extract the ciphertext (everything except the IV)
    const ciphertext = CryptoJS.lib.WordArray.create(
      combined.words.slice(0, -IV_SIZE / 4),
      combined.sigBytes - IV_SIZE
    );

    // Prepare the decryption key
    const keyString = handledBy === 'SERVER' ? CRYPTO_KEY : CRYPTO_KEY;
    const key = CryptoJS.enc.Utf8.parse(keyString);

    // Decrypt the ciphertext
    const decrypted = CryptoJS.AES.decrypt({ ciphertext }, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    // Convert the decrypted data to a UTF-8 string
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    return parsed ? JSON.parse(decryptedText) : decryptedText;
  } catch (error) {
    logout();
    return null;
    // console.error('Error decrypting data:', error);
    // throw new Error('Failed to decrypt data');
  }
};

export const hasEncrypt = () => {
  return isModuleEnabled('ENCRYPT_PAYLOAD');
};

export const getEncryptedRoutes = () => {
  const response = LocalStorageService.getCustomToken('response', true);
  if (response !== null) {
    return response.encryptedRoutes;
  }
  return [];
};

const isAuthEncrypted = () => {
  const response = LocalStorageService.getCustomCookie('unifyData', true);
  return response?.services?.ENCRYPT_PAYLOAD;
};

const checkRouteMatch = (config, isEncryptedRoutes = true) => {
  // const configUrl = config.url.replace(/^\/|\/$/g, '');
  // const routes = isEncryptedRoutes ? getEncryptedRoutes() : encryptedAuthUrls;
  // return routes.find((item) => {
  //   const routeUrl = item.route.replace('/api/v1/', '');
  //   return (
  //     configUrl.startsWith(routeUrl) && item.method.toUpperCase() === config.method.toUpperCase()
  //   );
  // });

  const urlWithoutQueryParams = config.url.split('?')[0];
  const configUrl = urlWithoutQueryParams.replace(/^\/|\/$/g, '');
  const routes = isEncryptedRoutes ? getEncryptedRoutes() : encryptedAuthUrls;
  return routes.find((item) => {
    const routeUrl = item.route.replace('/api/v1/', '');
    // const routeRegex = new RegExp(
    //   `^${routeUrl.toLowerCase().replace(/:\w+/g, '[^/]+')}(?:/[^/]+)*$`
    // );
    const routeRegex = new RegExp(`^${routeUrl.toLowerCase().replace(/:\w+/g, '[^/]+')}$`);
    return (
      routeRegex.test(configUrl.toLowerCase()) &&
      item.method.toUpperCase() === config.method.toUpperCase()
    );
  });
};

export const checkEncryptedRoute = (config, type = 'REQUEST') => {
  const isAuthMatch = checkRouteMatch(config, false);
  if (isAuthMatch) return isAuthMatch?.applicable.includes(type) && isAuthEncrypted();
  if (!hasEncrypt()) return false;

  const routeMatch = checkRouteMatch(config);
  return routeMatch?.applicable.includes(type);
};

const logout = () => {
  const ssoId = LocalStorageService.getSSOAccountId();
  if (ssoId) {
    const accounts = msalInstance.getAllAccounts();
    const currentAccount = accounts[0];
    // Automatically on page load
    msalInstance.logoutRedirect({
      account: currentAccount,
      onRedirectNavigate: () => {
        // Return false to stop navigation after local logout
        return false;
      },
    });
  }

  LocalStorageService.clearToken();
};
