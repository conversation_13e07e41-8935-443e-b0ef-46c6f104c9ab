import useFileUpload from 'Hooks/useFileUpload';
import React from 'react';

function withFileUploadHocHooks(Component) {
  const InjectedCurrentData = function (props) {
    const { uploading, error, fileData, uploadProgress, upload, reset } = useFileUpload();
    return (
      <Component
        {...props}
        uploading={uploading}
        error={error}
        fileData={fileData}
        uploadProgress={uploadProgress}
        upload={upload}
        reset={reset}
      />
    );
  };
  return InjectedCurrentData;
}

export default withFileUploadHocHooks;
