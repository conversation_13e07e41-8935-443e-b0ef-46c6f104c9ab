import React from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';

function ProgramTabs({ data = List(), activeProgramId, onTabClick }) {
  return (
    <div className="customize_tab">
      <ul id="menu_student_group">
        {data.map((item, i) => (
          <span
            key={`${item.get('value')}-${i}`}
            className={`tabaligment ${item.get('value') === activeProgramId ? 'tabactive' : ''}`}
            onClick={() => onTabClick(item.get('value'), item.get('name', ''))}
          >
            {item.get('name', '')}
          </span>
        ))}
      </ul>
    </div>
  );
}

ProgramTabs.propTypes = {
  data: PropTypes.instanceOf(List),
  activeProgramId: PropTypes.string,
  onTabClick: PropTypes.func,
};

export default ProgramTabs;
