/* eslint-disable react/jsx-key */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Dropdown, DropdownButton, Modal, But<PERSON> } from 'react-bootstrap';
import <PERSON> from 'papaparse';
import { NotificationManager } from 'react-notifications';
import Swal from 'sweetalert2';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import StudentList from './studentList';
import Modalsingle from '../../_components/Modal/modal';
import Search from '../../Widgets/Search/Search';
import Pagination from '../../Components/StaffManagement/Pagination';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { jsUcfirst } from '../../utils';
import * as actions from '../../_reduxapi/user_management/action';
import Export from './Export';
import MailContentModal from 'Containers/Modal/MailModal';
class AllStudent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      importShow: false,
      loadingShow: false,
      csvFile: undefined,
      data: [],
      isLoading: false,
      progress: 0,
      searchText: '',
      limit: 10,
      totalPages: 1,
      pageLength: 1,
      mailId: '',
      user_state: '',
    };
    this.updateData = this.updateData.bind(this);
    this.timeout = 0;
  }

  componentDidMount() {
    this.fetchApi({});
  }

  successTrigger = (res) => {
    const data = res.data.data.map((data) => {
      return {
        id: data._id,
        family: data.name.family,
        academic: data.academic,
        email: data.email,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        gender: data.gender,
        nationality_id: data.address.nationality_id,
        enrollment_year: data.enrollment_year,
        program_no: data.program_no,
        user_state: data.user_state,
        isChecked: false,
      };
    });
    this.setState({
      data: data,
      isLoading: false,
      totalPages: res.data.totalPages,
    });
  };

  fetchApi = ({ limit = 10, pageLength = 1 }) => {
    const { getUserManagementList } = this.props;
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });

    getUserManagementList(
      {
        searchText: searchText,
        type: 'student',
        status: this.props.name,
        limit: limit,
        pageLength: pageLength,
        slug: 'get_all',
      },
      this.successTrigger,
      () => {
        this.setState({
          data: [],
          isLoading: false,
          totalPages: 1,
          pageLength: 1,
        });
      }
    );
  };

  handleImport = () => {
    this.setState({
      importShow: !this.state.importShow,
    });
  };

  handleLoadingImportClose = () => {
    this.setState({
      loadingShow: false,
    });
  };

  handleChange = (event) => {
    this.setState({
      csvFile: event.target.files[0],
    });
  };

  importCSV = () => {
    const { csvFile } = this.state;
    Papa.parse(csvFile, {
      complete: this.updateData,
      header: true,
    });
  };

  updateData(result) {
    var csvData = result.data;
    let progress = this.state.progress;
    this.setState({
      importShow: false,
      loadingShow: true,
      progress: progress * 100,
    });

    let studentProfile = csvData.map((data) => {
      if (data.FirstName !== '') {
        return {
          first_name: data.FirstName,
          middle_name: data.MiddleName,
          last_name: data?.LastName?.trim() !== '' ? data.LastName : '.',
          family_name: data.FamilyName,
          gender: data.Gender,
          email: data.Email,
          academic_no: data.AcademicNo,
          batch: data.SelectedBatch,
          program_no: data.ProgramNo,
          nationality_id: data.NationalID,
        };
      } else {
        return {};
      }
    });

    studentProfile = studentProfile.filter((value) => Object.keys(value).length !== 0);

    const data = {
      user_type: 'student',
      data: studentProfile,
    };

    axios
      .post(`user/import`, data)
      .then((res) => {
        if (res.data.status_code === 200) {
          Swal.fire({
            title: '',
            icon: 'info',
            html:
              `<b>Imported Records Count - ${
                res.data.data.imported_records_count !== undefined
                  ? res.data.data.imported_records_count
                  : '0'
              }</b>` +
              `<br/><br/>` +
              `<b>Invalid Records Count - ${
                res.data.data.invalid_records_count !== undefined
                  ? res.data.data.invalid_records_count
                  : '0'
              }</b>`,
          });
          this.setState(
            {
              csvFile: undefined,
              loadingShow: false,
            },
            () => {
              this.fetchApi({});
            }
          );
          this.props.fetchApi();
        }
      })
      .catch((error) => {
        if (
          error.response !== undefined &&
          error.response.data !== undefined &&
          error.response.data.data !== undefined
        ) {
          NotificationManager.error(`${error.response.data.data}`);
        } else {
          NotificationManager.error(t(`user_management.Error_CSV_file`));
        }

        this.setState({
          loadingShow: false,
          csvFile: undefined,
        });
      });
  }

  handleSingleShow = () => {
    this.props.history.push({
      pathname: '/student/profile/add',
    });
  };

  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      const { limit } = this.state;
      setTimeout(() => {
        this.fetchApi({ limit });
        this.setState({ pageLength: 1 });
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState({ searchText: e.target.value }, () => this.doSearch(e));
  };

  pagination = (e) => {
    this.setState(
      {
        limit: e.target.value,
        pageLength: 1,
      },
      () => {
        setTimeout(() => {
          this.fetchApi({ limit: this.state.limit });
        }, 500);
      }
    );
  };

  pageCount = (value) => {
    this.setState(
      {
        pageLength: value,
      },
      () => {
        this.fetchApi({ pageLength: value, limit: this.state.limit });
      }
    );
  };

  handleAllChecked = (event) => {
    let data = this.state.data;
    data.map((data) => (data.isChecked = event.target.checked));
    this.setState({ data: data });
  };

  handleCheckFieldElement = (event, index) => {
    let data = this.state.data;
    data[index].isChecked = event.target.checked;
    this.setState({ data: data });
  };

  handleClickMailPush = () => {
    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });
    if (mailData.length > 0) {
      let id = mailData.map((data) => {
        return data.id;
      });

      this.setState({
        registrationConfirmationShow: true,
        mailId: id,
      });
    } else {
      NotificationManager.error(t(`user_management.Choose_atLeast_one_checkbox`), '', 2000);
    }
  };

  handleEdit = (data) => {
    this.props.history.push({
      pathname: '/student/profile/edit',
      search: '?id=' + data.id,
      state: this.props.selectedTab,
    });
  };

  handleConfirmDeleteShow = (e, data) => {
    this.setState({
      confirmDelete: true,
      deleteId: data.id,
    });
  };

  handleConfirmDeleteClose = (e) => {
    this.setState({
      confirmDelete: false,
    });
  };

  handleDelete = (e) => {
    this.setState({
      isLoading: true,
    });

    axios
      .delete(`user/${this.state.deleteId}`)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
            confirmDelete: false,
          },
          () => {
            this.fetchApi({});
          }
        );
        this.props.fetchApi();
        NotificationManager.success(t('deleted_successfully'));
      })
      .catch((error) => {
        NotificationManager.danger(`${error.response.data.message}`);
        this.setState({ isLoading: false });
      });
  };

  handleGoBack = () => {
    this.props.history.push({
      pathname: '/student/management',
      state: {
        completeView: false,
        pendingView: true,
        inactiveView: false,
        selectedTab: this.props.state !== undefined ? this.props.state : 0,
      },
    });
  };

  handleClickRegistrationConfirmationClose = () => {
    this.setState({
      registrationConfirmationShow: false,
    });
  };

  handleState = (data1) => {
    this.setState(data1);
  };
  render() {
    const functionObject = {
      show: this.state.importShow,
      handleImportClose: this.handleImport,
      handleChange: this.handleChange,
      csvfile: this.state.csvFile,
      loading: this.state.loadingShow,
      handleLoadingImportClose: this.handleLoadingImportClose,
      progress: this.state.progress,
      importCSV: this.importCSV,
    };

    const { name } = this.props;

    const header = [
      <Trans i18nKey={'academic_no'}></Trans>,
      <Trans i18nKey={'email'}></Trans>,
      <Trans i18nKey={'first_name'}></Trans>,
      'Middle Name',
      'Last Name',
      <Trans i18nKey={'gender'}></Trans>,
      <Trans i18nKey={'national_id'}></Trans>,
    ];

    const totalSelect = this.state.mailId.length;

    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });

    return (
      <div className="main bg-gray pt-2 pb-5">
        <Loader isLoading={this.state.isLoading} />
        <div className="container-fluid">
          {name === 'invited' && (
            <div className="float-left  pt-2 pl-3">
              <span className="mr-0">
                {CheckPermission(
                  'subTabs',
                  'User Management',
                  'Student Management',
                  '',
                  'Registration Pending',
                  '',
                  'Invited',
                  'Send Mail'
                ) && (
                  <Button
                    disabled={mailData.length > 0 ? false : true}
                    className={mailData.length > 0 ? '' : 'disabled-icon'}
                    onClick={(e) => this.handleClickMailPush(e)}
                  >
                    <Trans i18nKey={'send_mail'}></Trans>{' '}
                    <i className="fa fa-envelope" aria-hidden="true"></i>
                  </Button>
                )}
              </span>

              {/* mail funtion start   */}

              {this.state.registrationConfirmationShow && (
                <MailContentModal
                  show={this.state.registrationConfirmationShow}
                  hide={this.handleClickRegistrationConfirmationClose}
                  type={'Student'}
                  state={this.state}
                  setState={this.handleState}
                  totalSelect={totalSelect}
                  fetchApi={this.fetchApi}
                  linkText={'Signup Link'}
                />
              )}

              {/* mail funtion end */}
            </div>
          )}
          <div className="float-right p-2 d-flex">
            <Export userType="student" status={name} />
            {CheckPermission(
              'subTabs',
              'User Management',
              'Student Management',
              '',
              'Registration Pending',
              '',
              jsUcfirst(name),
              'Search'
            ) && (
              <Search
                value={this.state.searchText}
                onChange={(e) => this.handleSearch(e)}
                type="student"
              />
            )}
          </div>
          <div className="">
            {name === 'all' && (
              <div className="float-right pt-2 pr-2">
                {(CheckPermission(
                  'subTabs',
                  'User Management',
                  'Student Management',
                  '',
                  'Registration Pending',
                  '',
                  'All',
                  'Add Single'
                ) ||
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'All',
                    'Add Bulk'
                  )) && (
                  <DropdownButton
                    id="dropdown-basic-button"
                    title={<Trans i18nKey={'add_new'}></Trans>}
                  >
                    {CheckPermission(
                      'subTabs',
                      'User Management',
                      'Student Management',
                      '',
                      'Registration Pending',
                      '',
                      'All',
                      'Add Single'
                    ) && (
                      <Dropdown.Item href="" onClick={(e) => this.handleSingleShow(e)}>
                        <Trans i18nKey={'single_entry'}></Trans>
                      </Dropdown.Item>
                    )}
                    {CheckPermission(
                      'subTabs',
                      'User Management',
                      'Student Management',
                      '',
                      'Registration Pending',
                      '',
                      'All',
                      'Add Bulk'
                    ) && (
                      <Dropdown.Item href="" onClick={(e) => this.handleImport(e)}>
                        <Trans i18nKey={'bulk_import'}></Trans>
                      </Dropdown.Item>
                    )}
                  </DropdownButton>
                )}
              </div>
            )}
            <div className="clearfix"> </div>

            <Modalsingle modalobj={functionObject} />
            {/* Delete model start  */}

            <Modal show={this.state.confirmDelete} onHide={this.handleConfirmDeleteClose}>
              <Modal.Header closeButton>
                <Modal.Title id="example-modal-sizes-title-sm">
                  <Trans i18nKey={'user_management.delete_student'} />{' '}
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div className="row">
                  <div className="col-md-7 pt-2">
                    <p className="pt-2">
                      {' '}
                      <Trans i18nKey={'user_management.confirm_delete_student'} />
                    </p>
                  </div>
                </div>
              </Modal.Body>
              <Modal.Footer>
                <Button btnType="Success" onClick={this.handleConfirmDeleteClose}>
                  <Trans i18nKey={'events.cancel'} />
                </Button>{' '}
                <Button btnType="Success" onClick={(e) => this.handleDelete(e)}>
                  {' '}
                  <Trans i18nKey={'confirm'} />
                </Button>{' '}
              </Modal.Footer>
            </Modal>

            {/* Delete model end  */}

            <div className="clearfix"> </div>
            <React.Fragment>
              <div className="p-3">
                <StudentList
                  header={header}
                  name={this.props.name}
                  data={this.state.data}
                  handleAllChecked={this.handleAllChecked}
                  handleCheckFieldElement={this.handleCheckFieldElement}
                  handleEdit={this.handleEdit}
                  handleConfirmDeleteShow={this.handleConfirmDeleteShow}
                  handleGoBack={this.handleGoBack}
                />
                <Pagination
                  totalPages={this.state.totalPages}
                  switchPagination={this.pagination}
                  switchPageCount={this.pageCount}
                  pageLength={this.state.pageLength}
                />
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  }
}

AllStudent.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
  fetchApi: PropTypes.func,
  name: PropTypes.string,
  selectedTab: PropTypes.number,
  state: PropTypes.number,
};

export default connect(null, actions)(withRouter(AllStudent));
