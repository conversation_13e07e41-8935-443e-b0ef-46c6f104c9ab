import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import { List, Map } from 'immutable';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import { Trans } from 'react-i18next';
import { languages } from '../utils';
import MButton from 'Widgets/FormElements/material/Button';
function RenameModal({
  show,
  handleClose,
  labelName,
  labelConfiguration,
  updateLabel,
  selectedLanguage,
  institutionHeader,
}) {
  const [labels, setLabels] = useState(List());

  function getSelectedLabel() {
    let languageArray = selectedLanguage.map((item) => item.get('code', '')).toJS();
    return (
      labelConfiguration &&
      labelConfiguration
        .get('labelConfiguration', List())
        .filter((item) => languageArray.includes(item.get('language', '')))
        .map((data) => {
          const selectedLabel = data
            .get('labels', List())
            .find((label) => label.get('name', '') === labelName);
          return selectedLabel.merge({ language: data.get('language', '') });
        })
    );
  }

  const getSelectedLabels = getSelectedLabel();

  useEffect(() => {
    setLabels(getSelectedLabels);
  }, []); // eslint-disable-line

  const handleChange = (event, i) => {
    setLabels(labels.setIn([i, 'translatedInput'], event.target.value));
  };

  const handleSubmit = () => {
    const requestData = {
      labelName: labels.getIn([0, 'defaultInput'], ''),
      translatedLabels: labels
        .filter((label) => label.get('translatedInput', '') !== '')
        // eslint-disable-next-line
        .map((data) => {
          if (data.get('translatedInput', '') !== '') {
            return {
              label: data.get('translatedInput', '').trim(),
              language: data.get('language', ''),
            };
          }
        })
        .toJS(),
    };
    updateLabel({
      id: labelConfiguration.get('_id'),
      requestData,
      cb: handleClose,
      header: institutionHeader,
    });
  };

  function getLanguage(key) {
    return languages.filter((item) => item.value === key).reduce((_, el) => el.key, 'N/A');
  }

  function isDisable() {
    return labels.every((item) => !item.get('translatedInput', ''));
  }

  return (
    <div>
      <Modal show={show} centered onHide={handleClose}>
        <Modal.Header className="border-none pb-0">
          <Modal.Title className="f-20">
            <Trans i18nKey={'global_configuration.rename'}></Trans>
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="pt-4">
          {labels &&
            labels.size > 0 &&
            labels.map((data, i) => (
              <div key={i}>
                <div className="mb-3">
                  <FormLabel component="legend">
                    <span className="ml-1">
                      <Trans i18nKey={'global_configuration.label_name'}></Trans>
                    </span>
                    <span className="float-right mr-1">
                      {getLanguage(data.get('language', ''))}
                    </span>
                  </FormLabel>
                  <FormControl variant="outlined" className="wd-100">
                    <TextField
                      className={`outline-text-input`}
                      onChange={(event) => handleChange(event, i)}
                      value={data.get('translatedInput', '')}
                      labelWidth={0}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      placeholder={getLanguage(data.get('language', ''))}
                      inputProps={{ maxLength: 50 }}
                    />
                  </FormControl>
                </div>
              </div>
            ))}
        </Modal.Body>

        <Modal.Footer className="border-none">
          <MButton color="inherit" variant="outlined" clicked={handleClose}>
            <Trans i18nKey={'cancel'}></Trans>
          </MButton>

          <MButton disabled={isDisable()} clicked={() => handleSubmit()}>
            <Trans i18nKey={'save'}></Trans>
          </MButton>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

RenameModal.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
  labelName: PropTypes.string,
  labelConfiguration: PropTypes.instanceOf(Map),
  selectedLanguage: PropTypes.instanceOf(List),
  updateLabel: PropTypes.func,
  institutionHeader: PropTypes.object,
};

export default RenameModal;
