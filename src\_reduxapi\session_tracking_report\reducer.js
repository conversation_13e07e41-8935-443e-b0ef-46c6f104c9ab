import { fromJS, Map } from 'immutable';
import * as actions from './action';
import { t } from 'i18next';

const initialState = fromJS({
  message: '',
  loading: false,
  sessionReport: {},
  singleSessionReport: {},
  programList: [],
  programCourseList: [],
  exportReport: {},
});

//eslint-disable-next-line
export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_SESSION_REPORT_LIST_REQUEST: {
      return state.set('loading', true).set('sessionReport', Map());
    }
    case actions.GET_SESSION_REPORT_LIST_SUCCESS: {
      return state.set('loading', false).set('sessionReport', fromJS(action.data));
    }
    case actions.GET_SESSION_REPORT_LIST_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.GET_SESSION_REPORT_SINGLE_REQUEST: {
      return state.set('loading', true).set('singleSessionReport', fromJS({}));
    }
    case actions.GET_SESSION_REPORT_SINGLE_SUCCESS: {
      return state.set('loading', false).set('singleSessionReport', fromJS(action.data));
    }
    case actions.GET_SESSION_REPORT_SINGLE_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.GET_PROGRAM_LIST_REQUEST: {
      return state.set('loading', true);
    }
    case actions.GET_PROGRAM_LIST_SUCCESS: {
      return state.set('loading', false).set('programList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_LIST_FAILURE: {
      return state.set('loading', false).set('error', action.error);
    }

    case actions.GET_PROGRAM_COURSE_LIST_REQUEST: {
      return state.set('loading', true);
    }
    case actions.GET_PROGRAM_COURSE_LIST_SUCCESS: {
      return state.set('loading', false).set('programCourseList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_COURSE_LIST_FAILURE: {
      return state.set('loading', false).set('error', action.error);
    }

    case actions.GET_EXPORT_REPORT_LIST_REQUEST: {
      return state.set('loading', true).set('exportReport', Map());
    }
    case actions.GET_EXPORT_REPORT_LIST_SUCCESS: {
      return state.set('loading', false).set('exportReport', fromJS(action.data));
    }
    case actions.GET_EXPORT_REPORT_LIST_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.SCHEDULE_DOC_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SCHEDULE_DOC_LIST_SUCCESS: {
      return state.set('isLoading', false).set('scheduleDocList', fromJS(action.data.data));
    }
    case actions.SCHEDULE_DOC_LIST_FAILURE: {
      return state.set('isLoading', false);
    }

    default:
      return state;
  }
}
function prepareErrorMessage(action, state) {
  const { response: { data: { message = '' } = {} } = {} } = action.error;
  const errorMessage =
    message && typeof message === 'string' ? message : t('an_error_occured_try_again');
  return state.set('isLoading', false).set('isSaving', false).set('message', errorMessage);
}
