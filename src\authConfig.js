import { PublicClientApplication } from '@azure/msal-browser';

export const initializeMsal = (unifyData) => {
  const clientId = unifyData?.get('ssoClientId', '');
  const tenantId = unifyData?.get('ssoTenantId', '');

  const config = {
    auth: {
      clientId: clientId,
      authority: `https://login.microsoftonline.com/${tenantId}/v2.0`,
      redirectUri: '/',
      // postLogoutRedirectUri: '/',
      // navigateToLoginRequestUrl: false, // Prevent automatic redirection
    },
    cache: {
      cacheLocation: 'localStorage', // Store tokens in session storage
      storeAuthStateInCookie: false,
    },
    // system: {
    //   allowRedirectInIframe: true,
    // },
  };

  const loginRequest = {
    scopes: ['openid', 'profile', 'user.read'],
  };

  const msalInstance = new PublicClientApplication(config);
  return { config, msalInstance, loginRequest };
};
