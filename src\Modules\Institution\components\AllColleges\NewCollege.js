import React, { useState } from 'react';
import { withRouter } from 'react-router-dom';
import { Trans } from 'react-i18next';
import { fromJS } from 'immutable';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import MButton from 'Widgets/FormElements/material/Button';

import EditCollege from 'Assets/editcollege.png';
import AddEdit from '../AllColleges/Modal/AddEdit/AddEdit';
import * as actions from '_reduxapi/institution/actions';
import { checkValidation } from '../UniversityDetails/udUtil';
import CancelModal from '../AllColleges/Modal/Cancel';

function NewCollege({ history, addInstitution, getColleges, setData, parentInstitute }) {
  const [show, setShow] = useState(false);
  const [cancelShow, setCancelShow] = useState(false);
  const [isChanged, setIsChanged] = useState(false);

  const callBackFunctions = () => {
    getColleges({ id: parentInstitute });
    setShow(false);
    setIsChanged(false);
  };
  const fetchData = (data) => {
    if (checkValidation(data, 'college', setData)) {
      const formData = new FormData();
      for (const [key, value] of Object.entries(data)) {
        if (key !== 'accreditation') formData.append(key, value);
      }
      formData.append('type', 'College');
      formData.append('parentInstitute', parentInstitute);
      addInstitution({
        operation: 'create',
        formData,
        history,
        callBack: callBackFunctions,
        calledOn: 'addCollege',
      });
    }
  };

  const handleBack = () => {
    setShow(isChanged ? true : false);
    setCancelShow(isChanged ? true : false);
  };

  return (
    <>
      {show && (
        <AddEdit
          show={show}
          setShow={setShow}
          college={fromJS({})}
          fetchData={fetchData}
          title={'add_new_college'}
          setData={setData}
          cancelShow={cancelShow}
          setIsChanged={setIsChanged}
          handleBack={handleBack}
        />
      )}
      {cancelShow && (
        <CancelModal
          show={cancelShow}
          setCancelShow={setCancelShow}
          setShow={setShow}
          type="edit"
          setIsChanged={setIsChanged}
        />
      )}
      <div className="main pb-5 bg-gray">
        <div className="container">
          <div className="p-3">
            <div className="row justify-content-center pt-3">
              <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                <div className="text-center">
                  <img src={EditCollege} alt="" />
                </div>
                <h5 className="font-weight-normal text-center mt-1 ml-0 mb-0">
                  <Trans i18nKey={'add_colleges.No_Colleges_Added_Yet'}></Trans>
                </h5>
                <div className="row justify-content-md-center text-center mt-3">
                  <div className="col-md-7">
                    <MButton
                      clicked={() => {
                        setShow('edit');
                      }}
                      fullWidth
                    >
                      <Trans i18nKey={'add_colleges.add_new_college'}></Trans>
                    </MButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

NewCollege.propTypes = {
  history: PropTypes.object,
  onSubmit: PropTypes.func,
  setData: PropTypes.func,
  addInstitution: PropTypes.func,
  getColleges: PropTypes.func,
  type: PropTypes.string,
  parentInstitute: PropTypes.string,
};
export default compose(withRouter, connect(null, actions))(NewCollege);
