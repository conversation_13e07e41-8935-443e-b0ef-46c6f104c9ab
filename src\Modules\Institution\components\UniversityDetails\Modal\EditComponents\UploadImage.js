import React /* useContext */ from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { getLang } from 'utils';
//import UniversityContext from '../../Context/university-context';
import camera from '../../../../../../Assets/camera.svg';

function UploadImage({ updatedDetails, setUpdatedDetails, page = 'editBasicDetails', setData }) {
  //const details = useContext(UniversityContext);
  const { logo } = updatedDetails;
  function getLogoUrl() {
    if (typeof logo === 'string') {
      return logo;
    }
    return URL.createObjectURL(logo);
  }
  function getBrowseButton() {
    const hasNoLogo = !logo;
    return (
      <div className={hasNoLogo ? 'mb-0 pt-4 mt-3 text-center' : 'pt-3'}>
        <label
          htmlFor="logo-upload"
          //className="file-upload btn btn-outline-primary border-radious-8 f-14"
        >
          <div className={`${getLang() === 'ar' ? 'text-center' : ''}`}>
            {hasNoLogo && <img src={camera} alt="camera" />}{' '}
          </div>
          <div
            style={{
              color: '#147AFC',
              fontSize: '16px',
              cursor: 'pointer',
            }}
          >
            {hasNoLogo ? 'Add Photo' : 'Change'}
          </div>
          <input
            style={{ display: 'none' }}
            id="logo-upload"
            type="file"
            accept=".jpg,.jpeg,.png"
            onChange={(e) => setLogo(e.target.files)}
          />
        </label>
      </div>
    );
  }
  function getLogoInfo() {
    /* if (!logo) { */
    return (
      <>
        <div className={`${page === 'AddEdit' ? 'mt-2' : 'mt-3'} mb-0 pt-4 bold f-18`}>
          {`${!logo ? 'Upload' : 'Update'} your Logo`}
        </div>
        <div className="f-13 bold">
          The file should be less than 1 MB and format can be JPG, JPEG, PNG
        </div>
        {logo ? getBrowseButton() : ''}
      </>
    );
    /*}
     return (
      <>
        <p className="mb-0 pt-5 font-weight-500 f-14 text-black file-name">
          {`File Name: ${logo.name ? logo.name : 'Previously selected Image'}`}
        </p>
        {getBrowseButton()}
      </>
    ); */
  }

  //logo.name to save details
  const setLogo = (event) => {
    const fileSize = event[0].size / 1024 / 1024; // in MiB
    if (!['image/jpeg', 'image/jpg', 'image/png'].includes(event[0].type)) {
      setData(Map({ message: `JPG, JPEG, PNG format only allowed` }));
      return;
    } else if (fileSize > 1) {
      setData(Map({ message: `Upload File less than 1mb` }));
      setUpdatedDetails({ ...updatedDetails, logo: null });
      return;
    }
    setUpdatedDetails({ ...updatedDetails, logo: event.length ? event[0] : null });
  };
  return (
    <>
      <div className="col-md-3 col-sm-4">
        {logo ? (
          <div className="logo_view mt-4">
            <img
              src={getLogoUrl()}
              alt="logo"
              className="img-fluid logo-img-border height_150px width_150px"
            />
          </div>
        ) : (
          <div className={`${page === 'AddEdit' ? 'mt-2' : 'mt-5'} upload_logo`}>
            {getBrowseButton()}
          </div>
        )}

        <React.Fragment>
          {logo && (
            <div className="f-11 pt-2">
              {logo.size > 1000000 && <div className="text-red">Logo exceeds 1 MB limit</div>}
            </div>
          )}
        </React.Fragment>
      </div>
      <div className="col-md-4 col-sm-4">{getLogoInfo()}</div>
    </>
  );
}
UploadImage.propTypes = {
  setUpdatedDetails: PropTypes.func,
  setData: PropTypes.func,
  page: PropTypes.string,
  updatedDetails: PropTypes.object,
};
export default UploadImage;
