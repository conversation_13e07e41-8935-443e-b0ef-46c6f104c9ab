import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';

function CourseTabs({ data = List(), filters, onTabClick }) {
  function isActive(item) {
    const courseId = item.get('courseId');
    const isRotation = item.get('isRotation');
    const rotationCount = item.get('rotationCount');

    const activeCourseId = filters.get('courseId');
    const activeIsRotation = filters.get('isRotation');
    const activeRotationCount = filters.get('rotationCount');

    return (
      courseId === activeCourseId &&
      isRotation === activeIsRotation &&
      rotationCount === activeRotationCount
    );
  }

  return (
    <div className="tabs text-left">
      <ol className="tab-list-white">
        {data.map((item, i) => (
          <li
            key={`${item.get('courseId')}-${i}`}
            className={`tab-list-item-white ${isActive(item) ? 'tab-list-active-white' : ''}`}
            onClick={() =>
              onTabClick(
                item.get('courseId'),
                item.get('courseName'),
                item.get('courseNumber'),
                item.get('isRotation'),
                item.get('rotationCount'),
                item.get('versionName')
              )
            }
          >
            <span className="tab-bg">
              {`${item.get('isRotation') ? `R${item.get('rotationCount', '')} -` : ''} ${item.get(
                'courseNumber',
                ''
              )}${item.get('versionName', '')}`}
            </span>
          </li>
        ))}
      </ol>
    </div>
  );
}

CourseTabs.propTypes = {
  data: PropTypes.instanceOf(List),
  filters: PropTypes.instanceOf(Map),
  onTabClick: PropTypes.func,
};

export default CourseTabs;
