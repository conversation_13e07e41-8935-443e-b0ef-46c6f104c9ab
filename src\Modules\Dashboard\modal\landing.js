import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';
import DigiClassAdminImage from '../../../Assets/DigiClassAdmin.svg';
import DigiClassImage from '../../../Assets/DigiClass.svg';
import DigiAssessImage from '../../../Assets/DigiAssess.svg';
import DigiAssessRiyadhuImage from '../../../Assets/DigiAsses_Riyadhu.png';
import DigiAssessJithaImage from '../../../Assets/DigiAsses_Jitha.png';
import LocalStorageService from 'LocalStorageService';
import { getSiteUrl } from 'utils';
import DigiTecImg from 'Assets/DigiTecImg.jpg';
import DigiBgImg from 'Assets/DigiBgImg.jpg';
import DigiLogo from 'Assets/DigiLogo.png';
import { Map } from 'immutable';

const mainContainer = {
  width: '100%',
  height: '100%',
  bgcolor: 'background.paper',
};

const gridContainer = {
  display: 'grid',
  gridTemplateColumns: '0.8fr 1fr',
};

const landingPageStyle = {
  backgroundImage: `url(${DigiBgImg}), url(${DigiLogo})`,
  backgroundPosition: 'center, center bottom -70px',
  backgroundRepeat: 'no-repeat, no-repeat',
  backgroundSize: 'auto 100%, 43em 15em, auto',
  backgroundBlendMode: 'color-burn',
  opacity: '1%',
};
const textContent = { position: 'absolute', width: '44.5%', height: '100vh' };

const btnStyle = {
  height: '60px',
  width: '210px',
  boxShadow: '0 9px 6px rgba(0,0,0,0.1)',
  borderRadius: '10px',
  overflow: 'hidden',
  backgroundColor: '#ffff',
  cursor: 'pointer',
  ':hover': {
    border: '1px solid steelblue',
    boxShadow: '0 2px 6px steelblue',
  },
};

const positionStyle = {
  position: 'relative',
};

const containerStyleDigiClassAdmin = {
  height: '60px',
  width: '252px',
  position: 'absolute',
  left: '-12px',
  borderRadius: '10px',
  overflow: 'hidden',
  backgroundColor: '#ffff',
};
const beforeStyle = {
  backgroundImage: 'conic-gradient(#04b0ee 20deg, transparent 120deg)',
  height: '120%',
  width: '130%',
  position: 'absolute',
  left: '-25%',
  top: '-25%',
  animation: 'rotate 2s infinite linear',
};
const afterStyle = {
  height: '87%',
  width: '80%',
  position: 'absolute',
  backgroundColor: '#ffff',
  borderRadius: '10px',
  top: '5%',
  left: '3%',
  color: '#04b0ee',
  display: 'grid',
  placeItems: 'center',
  fontSize: '20px',
  letterSpacing: '6px',
};

export default function KeepMountedModal({ callBack }) {
  const [open, setOpen] = useState(false);
  useEffect(() => {
    setOpen(true);
  }, []);

  const handleClose = () => setOpen(false);

  const handleRedirect = (type) => {
    if (type === 'dc-admin') {
      LocalStorageService.setCustomCookie('site-id-name', 'dc-admin');
      LocalStorageService.setCustomCookie('landed', true);
      callBack && callBack();
      handleClose();
    } else {
      LocalStorageService.setCustomCookie('site-id-name', 'dc');
      LocalStorageService.setCustomCookie('landed', true);
      LocalStorageService.setCustomCookie('landing-progress', 'for loading');
      callBack && callBack();
      const siteURL = getSiteUrl();
      localStorage.clear();
      window.location = siteURL.LANDING_URL;
    }
  };

  const handleRedirectDigiAssess = (url = '') => {
    handleClose();
    callBack && callBack();
    window.open(url, '_blank');
  };

  const [isHovered, setIsHovered] = useState(
    Map({
      digiClassAdmin: false,
      digiClass: false,
      digiAssess: false,
    })
  );

  const handleHover = (key) => {
    setIsHovered((prev) => prev.set(key, true));
  };

  const handleLeave = (key) => {
    setIsHovered((prev) => prev.set(key, false));
  };

  KeepMountedModal.propTypes = {
    callBack: PropTypes.func,
  };

  const responsivefontSize = {
    fontSize: {
      xs: '24px',
      sm: '32px',
      md: '42px',
      lg: '52px',
    },
  };
  const siteURL = getSiteUrl();
  const digiAssessArray = [
    {
      url: siteURL.DA_URL,
      image:
        siteURL && siteURL.DA_URL_1 && siteURL.DA_URL_1 !== ''
          ? DigiAssessRiyadhuImage
          : DigiAssessImage,
    },
  ];
  if (siteURL && siteURL.DA_URL_1 && siteURL.DA_URL_1 !== '') {
    digiAssessArray.push({
      url: siteURL.DA_URL_1,
      image: DigiAssessJithaImage,
    });
  }

  return (
    <div>
      <Modal
        keepMounted
        open={open}
        onClose={handleClose}
        aria-labelledby="keep-mounted-modal-title"
        aria-describedby="keep-mounted-modal-description"
        style={{ zIndex: 9999 }}
      >
        <Box sx={mainContainer}>
          <div style={gridContainer}>
            <div style={landingPageStyle} />
            <div style={textContent}>
              <div className="d-flex justify-content-center m-5">
                <img
                  src={DigiLogo}
                  style={{ width: '30%', height: '10vh' }}
                  alt="LandingImg"
                  useMap="#LandingMap"
                />
              </div>
              <div
                className="d-flex justify-content-center h3 pt-5"
                style={{ fontFamily: 'sans-serif', fontSize: '35px', color: 'black' }}
              >
                Welcome
              </div>
              <div
                className="d-flex justify-content-center h3"
                style={{
                  fontFamily: 'sans-serif',
                  fontWeight: 'bold',
                  color: 'black',
                }}
              >
                <Box sx={responsivefontSize}> To Digival Platform! </Box>
              </div>
              <div
                className=" d-flex justify-content-center py-1"
                style={{ fontSize: '15px', color: 'black' }}
              >
                choose a user to proceed with the login
              </div>

              <div className="row justify-content-center">
                <Box
                  sx={btnStyle}
                  className="mx-2 px-2 my-2"
                  onMouseEnter={() => handleHover('digiClassAdmin')}
                  onMouseLeave={() => handleLeave('digiClassAdmin')}
                  onClick={() => handleRedirect('dc-admin')}
                >
                  <div style={positionStyle}>
                    <div style={containerStyleDigiClassAdmin}>
                      {isHovered.get('digiClassAdmin', false) && <div style={beforeStyle}></div>}
                      <div style={afterStyle} className="px-2">
                        <img
                          src={DigiClassAdminImage}
                          alt="DigiClass - Admin"
                          style={{ width: '100%' }}
                        />
                      </div>
                    </div>
                  </div>
                </Box>
                <Box
                  sx={btnStyle}
                  className="mx-2 px-2 my-2"
                  onMouseEnter={() => handleHover('digiClass')}
                  onMouseLeave={() => handleLeave('digiClass')}
                  onClick={() => handleRedirect('dc')}
                >
                  <div style={positionStyle}>
                    <div style={containerStyleDigiClassAdmin}>
                      {isHovered.get('digiClass', false) && <div style={beforeStyle}></div>}
                      <div style={afterStyle} className="px-2 py-2">
                        <img src={DigiClassImage} alt="DigiClass" style={{ width: '100%' }} />
                      </div>
                    </div>
                  </div>
                </Box>
              </div>
              <div className="row justify-content-center">
                {digiAssessArray.map((item, index) => {
                  return (
                    <Box
                      key={index}
                      sx={btnStyle}
                      className="mx-2 px-2 my-2"
                      onMouseEnter={() => handleHover('digiAssess' + index)}
                      onMouseLeave={() => handleLeave('digiAssess' + index)}
                      onClick={() => handleRedirectDigiAssess(item.url)}
                    >
                      <div style={positionStyle}>
                        <div style={containerStyleDigiClassAdmin}>
                          {isHovered.get(`digiAssess${index}`, false) && (
                            <div style={beforeStyle}></div>
                          )}
                          <div style={afterStyle} className="px-2 py-2">
                            <img
                              src={item.image}
                              alt="DigiAssess"
                              style={{ width: '100%', marginTop: '-5px' }}
                            />
                          </div>
                        </div>
                      </div>
                    </Box>
                  );
                })}
              </div>
            </div>
            <img
              src={DigiTecImg}
              style={{ width: '100%', height: '100vh' }}
              alt="LandingImg"
              useMap="#LandingMap"
            />
          </div>
        </Box>
      </Modal>
    </div>
  );
}
