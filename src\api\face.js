import * as faceapi from 'face-api.js';

export async function loadModels() {
  const publicPath = process.env.REACT_APP_BASE_PATH || '';
  const MODEL_URL = publicPath + '/models';
  await faceapi.loadTinyFaceDetectorModel(MODEL_URL);
  await faceapi.loadFaceLandmarkTinyModel(MODEL_URL);
  await faceapi.loadFaceRecognitionModel(MODEL_URL);
}

export async function getFullFaceDescription(blob, firstImageDesc, inputSize = 160) {
  let scoreThreshold = 0.5;
  const OPTION = new faceapi.TinyFaceDetectorOptions({
    inputSize,
    scoreThreshold,
  });
  const useTinyModel = true;

  let img = await faceapi.fetchImage(blob);

  let fullDesc = await faceapi
    .detectAllFaces(img, OPTION)
    .withFaceLandmarks(useTinyModel)
    .withFaceDescriptors();

  // const fullDesc = await faceapi
  //   .detectSingleFace(img, OPTION)
  //   .withFaceLandmarks(useTinyModel)
  //   .withFaceDescriptor();

  let distance = 0;
  if (firstImageDesc !== '' && fullDesc && fullDesc.length > 0) {
    distance = await faceapi.euclideanDistance(fullDesc[0].descriptor, firstImageDesc);
  }
  return {
    fullDesc: fullDesc,
    distance: distance,
  };
}

export function isFaceDetectionModelLoaded() {
  return !!faceapi.nets.tinyFaceDetector.params;
}
