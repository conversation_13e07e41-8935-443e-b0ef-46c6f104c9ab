import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Table } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';
import { Map, List } from 'immutable';
import { format, isValid } from 'date-fns';
import moment from 'moment';
import SnackBars from 'Modules/Utils/Snackbars';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import InfoIcon from '@mui/icons-material/Info';

import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
//eslint-disable-next-line
import {
  getReRegisterScheduleList,
  faceGet,
  setData,
  updateReRegisterStatus,
} from '_reduxapi/user_management/action';
import Loader from '../../Widgets/Loader/Loader';
import { jsUcfirst } from 'utils';

import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DialogContentText from '@mui/material/DialogContentText';
import Grid from '@mui/material/Grid';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { CheckPermission } from 'Modules/Shared/Permissions';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(2),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

function ReRegisterViewModal({ callBack, userId, countCallBack }) {
  const [open, setOpen] = useState(false);
  const [passCode, setPassCode] = useState('');
  const [faceId, setFaceId] = useState('');
  const [statusUpdated, setStatusUpdated] = useState(false);
  const [userStatus, setUserStatus] = useState(null);

  const dispatch = useDispatch();

  const [rejectOpen, setRejectOpen] = useState(false);
  const [reason, setReason] = useState('');

  const reRegisterScheduleList = useSelector(({ userManagement }) =>
    userManagement.get('reRegisterScheduleList', Map())
  );
  const isLoading = useSelector(({ userManagement }) =>
    userManagement.get('isScheduleLoading', false)
  );
  const message = useSelector(({ userManagement }) => userManagement.get('message', false));

  const faceGetData = useSelector(({ userManagement }) => userManagement.get('faceGetData', Map()));

  const scheduleList = reRegisterScheduleList
    .get('userBasedDatas', List())
    .filter((item) => item.get('scheduleId', null) !== null && item.get('faceURL', '') !== '');

  useEffect(() => {
    dispatch(getReRegisterScheduleList({ userId }));
  }, [dispatch]);

  ReRegisterViewModal.propTypes = {
    callBack: PropTypes.func,
    userId: PropTypes.string,
    countCallBack: PropTypes.func,
  };

  function getScheduleDate(schedule_date) {
    const scheduleDate = new Date(schedule_date);
    if (!isValid(scheduleDate)) return '';
    return format(scheduleDate, 'd LLL E');
  }

  const openModal = () => {
    setOpen((open) => !open);
  };

  const openRejectModal = () => {
    setRejectOpen((rejectOpen) => !rejectOpen);
    setReason('');
  };

  const rejectApprove = (e) => {
    e.preventDefault();
    const data = {
      status: 'rejected',
      userId,
      reasonToReject: reason.trim(),
      faceId,
    };
    dispatch(
      updateReRegisterStatus(data, () => {
        openRejectModal();
        setStatusUpdated(true);
        countCallBack();
      })
    );
  };

  const openModalPassword = ({ id, status = null }) => {
    setFaceId(id);
    openModal();
    setUserStatus(status);
  };

  const getBiometricFace = (e) => {
    e.preventDefault();
    const data = {
      faceId,
      userId,
      passCode,
    };

    const callBack = () => setPassCode('');

    dispatch(faceGet(data, callBack));
  };

  const statusUpdate = (type = '') => {
    const data = {
      status: type,
      userId,
      faceId,
    };
    dispatch(
      updateReRegisterStatus(data, () => {
        setStatusUpdated(true);
        countCallBack();
      })
    );
  };

  const header = [
    'Delivery Type',
    'Schedule Date & Time',
    'Program Name / Course / Year / Level / Term',
    'Mode',
    'Status',
    'Action',
  ];

  return (
    <React.Fragment>
      <Loader isLoading={isLoading} />
      {message && <SnackBars show={true} message={message} />}
      <BootstrapDialog
        maxWidth={'lg'}
        fullWidth={true}
        onClose={() => callBack()}
        aria-labelledby="customized-dialog-title"
        open={true}
      >
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          Staff Schedule Details
        </DialogTitle>
        <div className="dash-table">
          <Table responsive>
            <thead className="th-change">
              <tr>
                {header.map((header, i) => (
                  <th key={i}>{header}</th>
                ))}
              </tr>
            </thead>
          </Table>
        </div>
        <DialogContent dividers>
          <div className="dash-table">
            <Table responsive hover style={{ marginTop: '-1px' }}>
              {/* <thead className="th-change">
                <tr>
                  {header.map((header, i) => (
                    <th key={i}>{header}</th>
                  ))}
                </tr>
              </thead> */}
              {scheduleList.size === 0 ? (
                <TableEmptyMessage />
              ) : (
                <tbody>
                  {scheduleList.map((item, index) => (
                    <tr className="tr-change" key={index}>
                      <td>
                        {item.getIn(
                          ['scheduleId', 'session', 'delivery_symbol'],
                          jsUcfirst(item.getIn(['scheduleId', 'type'], '').replace('_', ' '))
                        )}
                        {item.getIn(['scheduleId', 'session', 'delivery_no'], '')}{' '}
                        {item.getIn(['scheduleId', 'session', 'session_topic'], '')}
                      </td>
                      <td>
                        {getScheduleDate(
                          item.getIn(['scheduleId', 'scheduleStartDateAndTime'], '')
                        )}{' '}
                        {moment(item.getIn(['scheduleId', 'scheduleStartDateAndTime'], '')).format(
                          'h:mm a'
                        )}{' '}
                        -{' '}
                        {moment(item.getIn(['scheduleId', 'scheduleEndDateAndTime'], '')).format(
                          'h:mm a'
                        )}
                      </td>
                      <td>
                        {item.getIn(['scheduleId', 'program_name'], '')} /{' '}
                        {item.getIn(['scheduleId', 'course_code'], '')} -{' '}
                        {item.getIn(['scheduleId', 'course_name'], '')} /{' '}
                        {item.getIn(['scheduleId', 'year_no'], '').replace('year', 'Year ')} /{' '}
                        {item.getIn(['scheduleId', 'level_no'], '')} /{' '}
                        {item.getIn(['scheduleId', 'term'], '')}{' '}
                        {`${
                          item.getIn(['scheduleId', 'rotation_count'], '') !== ''
                            ? ` / R${item.getIn(['scheduleId', 'rotation_count'], '')}`
                            : ''
                        }`}
                      </td>
                      <td>{jsUcfirst(item.getIn(['scheduleId', 'mode'], ''))}</td>
                      <td
                        className={
                          item.get('status', '') === 'approved'
                            ? 'text-green bold'
                            : item.get('status', '') === 'rejected'
                            ? 'text-red bold'
                            : 'bold'
                        }
                      >
                        <span className="float-left">{jsUcfirst(item.get('status', ''))}</span>{' '}
                        {item.get('status', '') === 'rejected' && (
                          <Tooltips title={item.get('reasonToReject', 'N/A')}>
                            <InfoIcon
                              className="newText-gray ml-2 remove_hover"
                              sx={{ marginTop: '-3px' }}
                            />
                          </Tooltips>
                        )}
                      </td>
                      <td>
                        {item.get('faceURL', '') !== '' ? (
                          <b
                            className="text-blue remove_hover"
                            onClick={() =>
                              openModalPassword({
                                // faceURL: item.get('faceURL', ''),
                                id: item.get('_id', ''),
                                status: item.get('status', ''),
                              })
                            }
                          >
                            {' '}
                            View{' '}
                          </b>
                        ) : (
                          'N/A'
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </Table>
          </div>
        </DialogContent>
        <DialogActions>
          <MButton color="inherit" variant="outlined" className="mr-3" clicked={() => callBack()}>
            <Trans i18nKey={'cancel'}></Trans>
          </MButton>
        </DialogActions>
      </BootstrapDialog>
      {open ? (
        <BootstrapDialog
          maxWidth={faceGetData.isEmpty() ? 'xs' : 'md'}
          fullWidth={true}
          aria-labelledby="customized-dialog-title"
          open={true}
        >
          <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
            {faceGetData.isEmpty() ? 'Passcode' : 'Uploaded Photo'}
          </DialogTitle>
          {CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            'Re-Register Request',
            'Approve/Reject'
          ) &&
            userStatus === 'pending' &&
            !statusUpdated &&
            !faceGetData.isEmpty() && (
              <div style={{ position: 'absolute', right: 0, top: 15 }}>
                <MButton
                  color="success"
                  disabled={scheduleList.size === 0}
                  className="mr-3"
                  clicked={() => statusUpdate('approved')}
                >
                  Approve
                </MButton>
                <MButton
                  color="error"
                  disabled={scheduleList.size === 0}
                  className="mr-3"
                  clicked={() => openRejectModal()}
                >
                  Reject
                </MButton>
              </div>
            )}
          <form autoComplete="off">
            <DialogContent dividers>
              {faceGetData.isEmpty() ? (
                <React.Fragment>
                  <DialogContentText>
                    To view the uploaded photo, please enter your passcode here.
                  </DialogContentText>
                  <MaterialInput
                    elementType={'materialInput'}
                    type={passCode === '' ? 'text' : 'password'}
                    variant={'outlined'}
                    size={'small'}
                    labelclass={'mb-1 f-14'}
                    maxLength={10}
                    label={''}
                    placeholder={'Enter Passcode *'}
                    value={passCode}
                    changed={(e) => setPassCode(e.target.value)}
                    inputRef={(input) => input && input.focus()}
                  />{' '}
                </React.Fragment>
              ) : (
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                  sx={{ overflowY: 'scroll', height: '20em' }}
                >
                  <Grid item xs={6}>
                    <div className="text-center pb-3">Original Photo</div>
                    <img
                      style={{ width: '100%', padding: '2px' }}
                      alt="Old Photo"
                      src={faceGetData.get('userOldPhoto', '')}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <div className="text-center pb-3">New Photo</div>
                    {faceGetData.get('signedURLs', List()).map((signedURL, index) => (
                      <img
                        key={index}
                        style={{ width: '50%', padding: '2px' }}
                        alt="New Photo"
                        src={signedURL}
                      />
                    ))}
                  </Grid>
                </Grid>
              )}
            </DialogContent>
            <DialogActions>
              <MButton
                color="inherit"
                variant="outlined"
                className="mr-3"
                clicked={() => {
                  openModal();
                  dispatch(setData(Map({ faceGetData: Map() })));
                  setUserStatus(null);
                  setPassCode('');
                }}
              >
                <Trans i18nKey={'cancel'}></Trans>
              </MButton>
              {faceGetData.isEmpty() ? (
                <MButton
                  className="mr-3"
                  type="submit"
                  disabled={!(passCode.length > 3)}
                  clicked={(e) => getBiometricFace(e)}
                >
                  Submit
                </MButton>
              ) : (
                ''
              )}
            </DialogActions>
          </form>
        </BootstrapDialog>
      ) : (
        ''
      )}
      {rejectOpen ? (
        <BootstrapDialog
          maxWidth={'xs'}
          fullWidth={true}
          onClose={() => openRejectModal()}
          aria-labelledby="customized-dialog-title"
          open={true}
        >
          <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
            Enter Reason
          </DialogTitle>
          <form autoComplete="off">
            <DialogContent dividers>
              <React.Fragment>
                <DialogContentText>Please enter reject reason here.</DialogContentText>
                <MaterialInput
                  elementType={'materialTextArea'}
                  changed={(e) => setReason(e.target.value)}
                  type={'text'}
                  value={reason}
                  placeholder={'Enter Reason *'}
                  minRows={3}
                  inputProps={{ maxLength: 500 }}
                  inputRef={(input) => input && input.focus()}
                />{' '}
              </React.Fragment>
            </DialogContent>
            <DialogActions>
              <MButton
                color="inherit"
                variant="outlined"
                className="mr-3"
                clicked={() => {
                  openRejectModal();
                }}
              >
                <Trans i18nKey={'cancel'}></Trans>
              </MButton>

              <MButton
                type="submit"
                className="mr-3"
                disabled={reason.trim() === ''}
                clicked={(e) => rejectApprove(e)}
              >
                Reject
              </MButton>
            </DialogActions>
          </form>
        </BootstrapDialog>
      ) : (
        ''
      )}
    </React.Fragment>
  );
}

export default ReRegisterViewModal;
