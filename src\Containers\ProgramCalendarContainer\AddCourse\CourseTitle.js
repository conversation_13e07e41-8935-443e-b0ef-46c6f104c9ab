import { t } from 'i18next';
import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Label, Select } from '../Styled';

export default function CourseTitle({ data }) {
  const [nonRotation, setNonRotation] = data;

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let CourseTitle = params.get('name');

  return (
    <Fragment>
      <div className="container-fluid">
        <div className="p-40">
          <div className="row">
            <div className="col-md-6 col-xl-3 col-lg-6 col-md-6">
              <Fragment>
                {nonRotation.check_courses && <Label className="pl-2">Model type</Label>}

                {nonRotation.check_courses && (
                  <Select
                    name="model"
                    value={nonRotation.type.model}
                    disabled={nonRotation.disable_title}
                    className={'styled-select'}
                    onChange={(e) =>
                      setNonRotation({
                        type: 'ON_CHANGE',
                        payload: e.target.value,
                        name: e.target.name,
                      })
                    }
                  >
                    <option value="">{t('master_graph.select')}</option>
                    <option value="standard">{t('constant.standard')}</option>
                    <option value="selective">{t('constant.selective')}</option>
                  </Select>
                )}
              </Fragment>
            </div>

            <div className="col-md-6 col-xl-3 col-lg-6">
              {nonRotation.type.model !== '' && !nonRotation.disable_title && (
                <Fragment>
                  <Label className="pl-2">Course Name</Label>
                  {nonRotation.check_courses && (
                    <Select
                      name="_course_id"
                      value={nonRotation.type._course_id}
                      disabled={nonRotation.disable_title}
                      className={'styled-select'}
                      onChange={(e) =>
                        setNonRotation({
                          type: 'ON_CHANGE',
                          payload: e.target.value,
                          name: e.target.name,
                        })
                      }
                    >
                      {nonRotation.add_courses['course']?.['status'] ? (
                        <Fragment>
                          <Fragment>
                            <option key="null" value="">
                              {t('program_calendar.select_course')}
                            </option>
                          </Fragment>
                          {nonRotation.add_courses['course']['data']
                            .filter((item) => item.course_type === nonRotation.type.model)
                            .map((item, i) => (
                              <option key={i} value={item._id}>
                                {item.courses_name}
                              </option>
                            ))}
                          <Fragment>
                            {nonRotation.update_method === 'edit' &&
                              nonRotation.type.course_name !== '' && (
                                <option key="_edit" value={nonRotation.type._course_id}>
                                  {nonRotation.type.course_name}
                                </option>
                              )}
                          </Fragment>
                        </Fragment>
                      ) : (
                        <Fragment>
                          <option value="">
                            {' '}
                            {t('program_calendar.no_course_in')}
                            {nonRotation.type.model}
                          </option>
                        </Fragment>
                      )}
                    </Select>
                  )}
                </Fragment>
              )}

              {nonRotation.type.model !== '' && nonRotation.disable_title && (
                <>
                  <Label className="pl-2">
                    <Trans i18nKey={'course_name_small'}></Trans>
                  </Label>
                  <Select disabled={nonRotation.disable_title}>
                    <option value="">{CourseTitle}</option>
                  </Select>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
}

CourseTitle.propTypes = {
  data: PropTypes.object,
};
