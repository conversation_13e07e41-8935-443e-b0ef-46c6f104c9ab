import React, { Component, Suspense } from 'react';
import { connect } from 'react-redux';
import { Menu, MenuItem, SubMenu, MenuDivider } from '@szhsin/react-menu';
import { withRouter } from 'react-router-dom';
import { Nav } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';

import {
  selectUserId,
  selectUserRoles,
  selectSelectedRole,
  selectInstitutionCalendar,
  selectActiveInstitutionCalendar,
  selectUserInfo,
} from '../../_reduxapi/Common/Selectors';
import * as actions from '../../_reduxapi/actions/auth';
import { isModuleEnabled, jsUcfirstAll, removeURLParams } from '../../utils';
import SwitchLanguages from './SwitchLanguages';
import LocalStorageService from 'LocalStorageService';
import moment from 'moment';
import { initializeMsal } from 'authConfig';
import { getQapcRoles } from '_reduxapi/q360/actions';
import { q360SetRoles } from '_reduxapi/actions/auth';
import useUnifyServiceHook from 'Hooks/useUnifyServiceHook';

const LandingModal = React.lazy(() => import('Modules/Dashboard/modal/landing'));

const NavigationWithUnify = (props) => {
  const { unifyData } = useUnifyServiceHook();
  return <NavigationComponent {...props} unifyData={unifyData} />;
};

class NavigationComponent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      open: false,
      isQ360ApiCalled: false,
      msalInstance: null,
    };
  }

  async componentDidMount() {
    const { unifyData } = this.props;
    if (unifyData) {
      try {
        const { msalInstance } = initializeMsal(unifyData);
        await msalInstance.initialize();
        this.setState({ msalInstance });
      } catch (error) {
        console.error('Error initializing MSAL:', error);
      }
    }
  }

  fetchQapcRoles = (cb) => {
    const { getQapcRoles, q360SetRoles } = this.props;
    const callBack = (res) => {
      const qapcRoles = fromJS(res);
      const qapcRoleIds = qapcRoles.map((s) => {
        return Map({
          _id: s.get('_id', ''),
          role_name: `Q360 ${s.get('roleName', '')}`,
          type: 'Q360',
          _role_id: Map({
            name: s.get('roleName', ''),
            _id: s.get('_id', ''),
          }),
        });
      });
      q360SetRoles(qapcRoleIds);
      cb && cb();
    };
    getQapcRoles(callBack);
  };

  switchRole = (selectedRole) => {
    const { setSwitchRole, location } = this.props;
    const url = location.pathname;
    setSwitchRole(selectedRole);
    const role_id = selectedRole.getIn(['_role_id', '_id'], '');
    LocalStorageService.setCustomToken('role_id', role_id);
    if (!url.includes('/qapc/')) {
      setTimeout(() => {
        this.props.history.push('/overview');
      }, 200);
    }
  };

  switchCalendar = (item) => {
    const { setSwitchCalendar } = this.props;
    setSwitchCalendar(item);
    const { location, history } = this.props;
    const url = location.pathname;
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let pid = params.get('pid');
    let cid = params.get('cid');
    let year = params.get('year');
    if (url.includes('/course-scheduling')) {
      history.push(`${url}${removeURLParams(location, ['courseId', 'courseName'])}`);
    } else if (url.includes('/student-grouping/grouping')) {
      history.push(`/student-grouping/dashboard?pid=${pid}&cid=${cid}&year=${year}`);
    } else if (url.includes('/program-calendar')) {
      window.location.reload(true);
    }
  };

  checkEnableAcademicYear = () => {
    const { location } = this.props;
    const url = location.pathname;
    return (
      !url.includes('/InstitutionCalendar') &&
      !url.includes('/reviewevent') &&
      !url.includes('/reviewaccept') &&
      !url.includes('/eventList') &&
      // !url.includes('/programcalendar') &&
      !url.includes('/programcalendar/vice-dean') &&
      !url.includes('/calendar') &&
      !url.includes('/interim') &&
      !url.includes('/course-v1') &&
      !url.includes('/roles/dashboard') &&
      !url.includes('/infrastructure-management') &&
      !url.includes('/staff/management') &&
      !url.includes('/student/management') &&
      !url.includes('/calender') &&
      !url.includes('/attainment-calculator/attainment-reports') &&
      !url.includes('/attainment-calculator/attainment-courses') &&
      !url.includes('/InstitutionSessionReport') &&
      !url.includes('/lmsReports') &&
      !url.includes('/course-scheduling/remote') &&
      !url.includes('/program-input') &&
      !url.includes('/all-calendar-list') &&
      !url.includes('/reports/programs') &&
      !url.includes('/staff/profile') &&
      !url.includes('/student/profile') &&
      !url.includes('/studentvalid/all') &&
      !url.includes('/UserModulePermissions') &&
      !url.includes('/lms') &&
      !url.includes('/pc/courses') &&
      !url.includes('/globalConfiguration-v1/institution') &&
      !url.includes('/globalConfiguration-v1/basicDetail') &&
      !url.includes('/leave-management/settings') &&
      !url.includes('/lms') &&
      !url.includes('/UserGlobalSearch') &&
      !url.includes('/globalConfiguration-v1/settings') &&
      !url.includes('/globalConfiguration-v1/tagMasters') &&
      !url.includes('/qapc/QualityAssuranceProcess') &&
      !url.includes('/qapc/QualityAssurance/create') &&
      !url.includes('/qapc/QualityAssurance/concluding_phase') &&
      !url.includes('/qapc/QualityAssuranceApproval') &&
      !url.includes('/q360_dashboard') &&
      !url.includes('/globalConfiguration-v1') &&
      !url.includes('/qapc/rolesAndPermission') &&
      !url.includes('/qapc/Dashboard') &&
      !url.includes('/course-input') &&
      !url.includes('/courseHandoutReport') &&
      !url.includes('/year-level-author')
    );
  };

  switchOpenApp = () => {
    this.setState({ open: true });
  };

  switchCloseApp = () => {
    this.setState({ open: false });
  };

  handleLogout = async () => {
    const { history, authLogout, userInfo } = this.props;
    const { msalInstance } = this.state;

    const data = {
      id: userInfo.get('_id', ''),
      device_type: 'web',
    };

    const callBack = () => {
      history.push('/logout');
    };

    authLogout(data, callBack);
    const ssoId = LocalStorageService.getSSOAccountId();
    if (ssoId && msalInstance) {
      try {
        const accounts = msalInstance.getAllAccounts();
        const currentAccount = accounts[0];
        await msalInstance.logoutRedirect({
          account: currentAccount,
          onRedirectNavigate: () => {
            return false;
          },
        });
      } catch (error) {
        console.error('Error during MSAL logout:', error);
      }
    }
  };

  handleProfileClick = () => {
    const { userRoles } = this.props;
    const { isQ360ApiCalled } = this.state;
    const isQ360Roles = userRoles.filter((role) => role.get('type', '') === 'Q360');
    const cb = () => {
      this.setState({
        isQ360ApiCalled: true,
      });
    };

    if (isQ360Roles.size === 0 && !isQ360ApiCalled) {
      this.fetchQapcRoles(cb);
    }
  };

  render() {
    const {
      clicked,
      iframeShow,
      iframeName,
      navBarTitle,
      userRoles,
      selectedRole,
      institutionCalendarLists,
      activeInstitutionCalendar,
      history,
    } = this.props;
    const { open, isQ360ApiCalled } = this.state;

    return (
      <React.Fragment>
        {open && (
          <Suspense fallback="">
            <LandingModal history={history} callBack={() => this.switchCloseApp()} />
          </Suspense>
        )}
        <Nav className="ham_nav" activeKey="/home">
          <div className="d-flex justify-content-between">
            <div>
              <span
                className="justify-content-start m-ham"
                style={{
                  fontSize: '27px',
                  fontWeight: '900',
                  cursor: 'pointer',
                  color: '#fff',
                }}
                onClick={clicked}
              >
                ☰
              </span>{' '}
              <span className={'nav-bar-title'}>
                {iframeShow ? jsUcfirstAll(iframeName) : navBarTitle}
              </span>
            </div>
            <div>
              {!iframeShow && (
                <div className="d-flex justify-content-between ltr_Arabic">
                  {isModuleEnabled('TRANSLATION') && (
                    <div className="mt--20">
                      <SwitchLanguages />
                    </div>
                  )}
                  <div className="">
                    {this.checkEnableAcademicYear()
                      ? academicLists(
                          institutionCalendarLists,
                          activeInstitutionCalendar,
                          this.switchCalendar
                        )
                      : ''}
                  </div>
                  <div className="">
                    {userRoles && userRoles.size > 0 && !selectedRole.isEmpty() && (
                      <div
                        style={{
                          float: 'right',
                          color: 'white',
                          fontWeight: '500',
                          fontSize: '15px',
                        }}
                      >
                        <Trans i18nKey={'you_are'}></Trans>
                        <br />
                        <span style={{ fontSize: '17px' }}>
                          {selectedRole.get('role_name', '') !== ''
                            ? jsUcfirstAll(selectedRole.get('role_name', ''))
                            : ''}
                        </span>
                      </div>
                    )}
                    <Menu
                      direction={'left'}
                      menuButton={
                        <i
                          className="fa fa-user-circle rc-user-profile"
                          onClick={() => {
                            !isQ360ApiCalled && this.handleProfileClick();
                          }}
                        ></i>
                      }
                    >
                      {/* <MenuItem>Profile</MenuItem> */}
                      {userRoles && userRoles.size > 0 ? (
                        <SubMenu label={`Logged in as ${selectedRole.get('role_name', '')}`}>
                          {userRoles &&
                            userRoles.size > 0 &&
                            userRoles.map((item, index) => (
                              <MenuItem
                                key={index}
                                onClick={
                                  item.get('_id') === selectedRole.get('_id')
                                    ? () => {}
                                    : () => this.switchRole(item)
                                }
                                styles={
                                  item.get('_id') === selectedRole.get('_id')
                                    ? {
                                        backgroundColor: '#ebebeb',
                                      }
                                    : {}
                                }
                              >
                                {jsUcfirstAll(item.get('role_name', ''))}
                              </MenuItem>
                            ))}
                        </SubMenu>
                      ) : (
                        <MenuItem label="" styles={{ display: 'none' }}></MenuItem>
                      )}
                      <MenuDivider />
                      <MenuItem onClick={() => this.switchOpenApp()}>Switch App</MenuItem>
                      <MenuDivider />
                      <MenuItem onClick={() => this.handleLogout()}>
                        <Trans i18nKey={'logout'}></Trans>
                      </MenuItem>
                    </Menu>
                  </div>
                </div>
              )}
            </div>
          </div>
        </Nav>
      </React.Fragment>
    );
  }
}

function getStatus(item) {
  if (item.get('isActive', false) === true) {
    const endDate = Date.parse(moment(item.get('end_date', new Date())).format('YYYY-MM-DD'));
    const currentDate = Date.parse(moment().format('YYYY-MM-DD'));
    return endDate < currentDate ? (
      <div className="menu-ri text-success">Completed</div>
    ) : (
      <div className="menu-ri text-primary">On-going</div>
    );
  } else {
    return <div className="menu-ri text-grey">Inactive</div>;
  }
}

function academicLists(institutionCalendarLists, activeInstitutionCalendar, callBack) {
  if (institutionCalendarLists.size > 0) {
    return (
      <span
        style={{
          // position: 'absolute',
          // right: '10em',
          // top: '1.5em',
          padding: '10px',
          float: 'right',
          color: '#FFFFFF',
          fontWeight: '500',
        }}
      >
        <Trans i18nKey={'academic_year'}></Trans>{' '}
        {activeInstitutionCalendar.get('calendar_name', '')}
        <Menu
          direction="left"
          overflow="auto"
          position="initial"
          menuButton={<i className="fa fa-caret-down ml-2 cursor-pointer"></i>}
          className="calendar-menu-list-height"
        >
          {institutionCalendarLists.map((item, index) => {
            return (
              <MenuItem
                key={index}
                onClick={() => callBack(item)}
                styles={
                  item.get('_id') === activeInstitutionCalendar.get('_id')
                    ? {
                        backgroundColor: '#ebebeb',
                      }
                    : {}
                }
              >
                <Trans i18nKey={'academic_year'}></Trans> {item.get('calendar_name', '')} (
                {moment(item.get('start_date', new Date())).format('D MMM YY')} -{' '}
                {moment(item.get('end_date', new Date())).format('D MMM YY')}){getStatus(item)}
              </MenuItem>
            );
          })}
        </Menu>
      </span>
    );
  } else {
    return '';
  }
}

NavigationComponent.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  setSwitchRole: PropTypes.func,
  setSwitchCalendar: PropTypes.func,
  fetchCourseList: PropTypes.func,
  clicked: PropTypes.func,
  iframeShow: PropTypes.bool,
  iframeName: PropTypes.string,
  navBarTitle: PropTypes.string,
  userRoles: PropTypes.instanceOf(List),
  selectedRole: PropTypes.instanceOf(Map),
  institutionCalendarLists: PropTypes.instanceOf(List),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  userInfo: PropTypes.instanceOf(Map),
  unifyData: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  return {
    currentUserId: selectUserId(state),
    userRoles: selectUserRoles(state),
    selectedRole: selectSelectedRole(state),
    institutionCalendarLists: selectInstitutionCalendar(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    userInfo: selectUserInfo(state),
  };
};

const Navigation = connect(mapStateToProps, { ...actions, getQapcRoles, q360SetRoles })(
  withRouter(React.memo(NavigationWithUnify))
);

export default Navigation;
