import { getDay } from 'date-fns';
import { selectGlobalSessionSetting } from '_reduxapi/global_configuration/v1/selectors';
import { useDispatch, useSelector } from 'react-redux';
import { List } from 'immutable';
import { useEffect } from 'react';
import { getGlobalSessionSetting } from '_reduxapi/global_configuration/v1/actions';

const useGlobalConfigDateHooks = () => {
  const globalSessionTiming = useSelector(selectGlobalSessionSetting);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getGlobalSessionSetting());
  }, []); //eslint-disable-line

  const isWeekday = (date) => {
    const index = [];
    globalSessionTiming.get('workingDays', List()).map((item, i) => {
      if (item.get('isActive', '') === true) {
        index.push(i);
      }
      return '';
    });
    const day = getDay(date);
    return index.includes(day);
  };

  return { isWeekday };
};

export default useGlobalConfigDateHooks;
