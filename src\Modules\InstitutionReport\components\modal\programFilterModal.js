import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { List } from 'immutable';
import {
  ListItemButton,
  List as ListView,
  Radio,
  ListItemText,
  Checkbox,
  FormControlLabel,
  RadioGroup,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/session_tracking_report/action';
import {
  selectProgramList,
  selectProgramCourseList,
} from '_reduxapi/session_tracking_report/selectors';
import moment from 'moment';
import { getVersionName } from 'utils';

function ProgramFilterModal({
  show,
  handleProgramFilterClose,
  getProgramList,
  programList,
  courseList,
  getProgramCourseList,
  handleApply,
  programStatus,
  CheckPermission,
  institutionCalendarId,
}) {
  const hasProgramAccess = CheckPermission(
    'pages',
    'Curriculum Monitoring',
    'Dashboard',
    'Filter Program'
  );
  const hasCourseAccess = CheckPermission(
    'pages',
    'Curriculum Monitoring',
    'Dashboard',
    'Filter Course'
  );
  useEffect(() => {
    const params = {
      ...(institutionCalendarId !== '' && { institutionCalendarId: institutionCalendarId }),
    };
    getProgramList(params);
  }, [getProgramList]); //eslint-disable-line

  const [checked, setChecked] = useState(List());
  const [loadOnce, setLoadOnce] = useState(true);
  const selectProgram = [
    {
      name: 'Program',
      value: 'program',
    },
    {
      name: 'Course',
      value: 'course',
    },
  ]
    .filter((item) => (!hasProgramAccess ? item.name !== 'Program' : item))
    .filter((item) => (!hasCourseAccess ? item.name !== 'Course' : item));
  const [type, setType] = useState(selectProgram[0].value);
  const allProgram = programList.map((data) => {
    return {
      name: data.get('name', ''),
      value: data.get('_id', ''),
    };
  });
  const [programName, setProgramName] = useState();
  const [courseYear, setCourseYear] = useState('all');
  const [courseLevel, setCourseLevel] = useState('all');
  const [levelList, setLevelList] = useState([]);
  const [selectCourseData, setSelectCourseData] = useState(null);
  const [searchText, setSearchText] = useState('');
  const initialProgram = allProgram.getIn([0, 'value'], '');
  useEffect(() => {
    setProgramName(initialProgram);
  }, [initialProgram]);

  useEffect(() => {
    setChecked(programStatus);
  }, [programStatus]);

  useEffect(() => {
    if (!hasProgramAccess && hasCourseAccess && programName && loadOnce) {
      const currentDate = moment(new Date()).format('YYYY-MM-DD'); //'2025-02-13';
      const params = {
        ...(programName && { programIds: programName }),
        ...(institutionCalendarId !== '' && { institutionCalendarId: institutionCalendarId }),
      };
      getProgramCourseList(currentDate, params);
      setLoadOnce(false);
    }
  }, [getProgramCourseList, programName]); //eslint-disable-line

  const handleSelect = (e, type) => {
    setSelectCourseData(null);
    setType(e.target.value);
    setChecked(List());
    if (type === 'course') {
      const currentDate = moment(new Date()).format('YYYY-MM-DD'); //'2025-02-13';
      const params = {
        ...(programName && { programIds: programName }),
        ...(institutionCalendarId !== '' && { institutionCalendarId: institutionCalendarId }),
      };
      programName && getProgramCourseList(currentDate, params);
    }
  };

  const handleSelectCourse = (e, name) => {
    if (name === 'programData') {
      setProgramName(e.target.value);
      const currentDate = moment(new Date()).format('YYYY-MM-DD'); //'2025-02-13';
      const params = {
        ...(programName && { programIds: e.target.value }),
        ...(institutionCalendarId !== '' && { institutionCalendarId: institutionCalendarId }),
      };
      getProgramCourseList(currentDate, params);
    }
    if (name === 'yearData') {
      setCourseYear(e.target.value);
      const levelFilter = [];
      courseList
        .filter((item) => item.get('year', '') === e.target.value)
        .map((data) => {
          if (
            levelFilter.some(
              (item) =>
                item.name === data.get('level_no', '') || item.value === data.get('level_no', '')
            )
          ) {
            return null;
          } else
            levelFilter.push({ name: data.get('level_no', ''), value: data.get('level_no', '') });
          return data;
        });
      setLevelList(levelFilter);
    }
    if (name === 'levelData') {
      setCourseLevel(e.target.value);
    }
  };

  const onChangeSearch = (event) => {
    setSearchText(event.target.value);
  };
  const yearFilter = [{ name: 'All', value: 'all' }];
  courseList.map((data) => {
    if (
      yearFilter.some(
        (item) =>
          item.name === 'Year' + data.get('year', '').replace('year', '') ||
          item.value === data.get('year', '')
      )
    ) {
      return null;
    } else
      yearFilter.push({
        name: 'Year' + data.get('year', '').replace('year', ''),
        value: data.get('year', ''),
      });
    return data;
  });

  const handleChange = (e, item, name) => {
    if (name === 'programData') {
      if (e.target.checked) setChecked(checked.push(item));
      else setChecked(checked.filter((value) => value.get('_id') !== item.get('_id')));
    }
    if (name === 'courseData') {
      setSelectCourseData({
        programIds: programName,
        programName: allProgram.find((item) => item?.value === programName)?.name,
        courseId: item.get('_course_id', ''),
        term: item.get('term', ''),
        year: item.get('year', ''),
        levelNo: item.get('level_no', ''),
        courseName: item.get('courses_name', ''),
        courseNumber: item.get('courses_number', ''),
        versionName: getVersionName(item),
        ...(item.get('rotation_no', '') && { rotationCount: item.get('rotation_no', '') }),
      });
    }
  };

  const handleChangeAll = (e) => {
    const all = programList.map((data) => data);
    if (e.target.checked) setChecked(all);
    else setChecked(List());
  };

  const children = (
    <ListView component="div" className="align-items-center" disablePadding>
      {programList
        .filter((item) => {
          if (searchText === '') return true;
          return item.get('name', '').toLowerCase().includes(searchText.toLowerCase());
        })
        .map((data, i) => (
          <ListItemButton dense key={i}>
            <FormControlLabel
              value="end"
              control={
                <Checkbox
                  checked={checked.map((s) => s.get('_id', '')).includes(data.get('_id', ''))}
                  tabIndex={-1}
                  disableRipple
                  onChange={(e) => handleChange(e, data, 'programData')}
                />
              }
              label={data.get('name', '')}
              labelPlacement="end"
            />
          </ListItemButton>
        ))}
    </ListView>
  );
  const filterData = (item) => {
    if (courseYear === 'all' && courseLevel === 'all' && searchText === '') {
      return true;
    }
    let courseYearStatus = true;
    let courseLevelStatus = true;
    let searchStatus = true;
    if (courseYear !== 'all') {
      courseYearStatus = item.get('year', '') === courseYear;
    }
    if (courseLevel !== 'all') {
      courseLevelStatus = item.get('level_no', '') === courseLevel;
    }
    if (searchText !== '') {
      searchStatus = item.get('courses_name', '').toLowerCase().includes(searchText.toLowerCase());
    }

    return courseYearStatus && courseLevelStatus && searchStatus;
  };
  const applyData = () => {
    if (type === 'program') {
      handleApply(checked, type);
    } else handleApply(selectCourseData, type);
  };

  return (
    <MaterialDialog show={show} onClose={handleProgramFilterClose} maxWidth={'sm'} fullWidth={true}>
      <div className="w-100 p-4">
        <div className="d-flex align-items-center justify-content-between">
          <div className="text-primary f-18 bold"> Filter by</div>
          <div className="w-25">
            <MaterialInput
              elementType={'materialSelectNew'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: selectProgram }}
              defaultValue={selectProgram[0].value}
              changed={(e) => handleSelect(e, 'course')}
              value={type}
            />
          </div>
        </div>

        <div className="mb-2 mt-2">
          <MaterialInput
            elementType={'materialSearch'}
            type={'text'}
            size={'small'}
            placeholder={type === 'course' ? 'Search Course...' : 'Search Programs...'}
            labelclass={'searchLeft'}
            changed={onChangeSearch}
          />
        </div>

        {type === 'program' ? (
          <React.Fragment>
            <div className="d-flex justify-content-between align-items-center  filtersSelect">
              <div className="text-primary bold">All Programs</div>
              <div>
                <FormControlLabel
                  label="Select All"
                  className="m-0"
                  control={
                    <Checkbox
                      size="small"
                      checked={programList.size === checked.size}
                      onChange={handleChangeAll}
                    />
                  }
                />
              </div>
            </div>
            <div className="digi-scroll">{children}</div>
          </React.Fragment>
        ) : (
          <React.Fragment>
            <div className="row mb-2">
              <div className="col-md-6">
                <MaterialInput
                  elementType={'materialSelectNew'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: allProgram }}
                  changed={(e) => handleSelectCourse(e, 'programData')}
                  value={programName}
                  label={'Program'}
                  labelclass={'mb-0 f-14'}
                />
              </div>
              <div className="col-md-3">
                <MaterialInput
                  elementType={'materialSelectNew'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: yearFilter }}
                  changed={(e) => handleSelectCourse(e, 'yearData')}
                  value={courseYear}
                  label={'Year'}
                  labelclass={'mb-0 f-14'}
                />
              </div>
              <div className="col-md-3">
                <MaterialInput
                  elementType={'materialSelectNew'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: [{ name: 'All', value: 'all' }, ...levelList] }}
                  changed={(e) => handleSelectCourse(e, 'levelData')}
                  value={courseLevel}
                  label={'Level'}
                  labelclass={'mb-0 f-14'}
                />
              </div>
            </div>

            <div className="d-flex justify-content-between align-items-center  filtersSelect">
              <div className="text-primary bold pt-1 pb-1 ">All courses</div>
            </div>
            <div className="digi-scroll">
              <ListView component="div" className="align-items-center" disablePadding>
                <RadioGroup
                  aria-labelledby="demo-radio-buttons-group-label"
                  name="radio-buttons-group"
                >
                  {courseList.filter(filterData).map((data, i) => (
                    <ListItemButton dense key={i} id={'d_flex_remove'}>
                      <ListItemText
                        primary={
                          <FormControlLabel
                            value={data.get('_course_id', '')}
                            control={<Radio className={'pl-0 pr-2 pb-0 pt-0'} />}
                            onChange={(e) => handleChange(e, data, 'courseData')}
                            label={`${data.get('courses_number', '')} - ${data.get(
                              'courses_name',
                              ''
                            )}${getVersionName(data)}`}
                            checked={
                              `${data.get('_course_id', '')}-${data.get('term', '')}` ===
                              `${selectCourseData?.courseId}-${selectCourseData?.term}`
                            }
                            className={'mr-0 mt-0 mb-0'}
                          />
                        }
                      />
                      <p className="mb-0 f-14 pl-3 ml-1 digi-gray text-capitalize">
                        {data.get('term', '')} / {data.get('year', '')} / {data.get('level_no', '')}
                      </p>
                    </ListItemButton>
                  ))}
                </RadioGroup>
              </ListView>
            </div>
          </React.Fragment>
        )}

        <div className="d-flex justify-content-end pt-3 border-top">
          <MButton
            variant="outlined"
            color="darkGray"
            clicked={handleProgramFilterClose}
            className="mr-3"
          >
            Cancel
          </MButton>
          <MButton
            variant="contained"
            disabled={
              type === 'program'
                ? checked.size === 0
                : type === 'course'
                ? selectCourseData === null
                : false
            }
            clicked={applyData}
          >
            Apply
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
}

ProgramFilterModal.propTypes = {
  show: PropTypes.bool,
  handleProgramFilterClose: PropTypes.func,
  getProgramList: PropTypes.func,
  getProgramCourseList: PropTypes.func,
  programList: PropTypes.instanceOf(List),
  courseList: PropTypes.instanceOf(List),
  handleApply: PropTypes.func,
  programStatus: PropTypes.instanceOf(List),
  CheckPermission: PropTypes.func,
  institutionCalendarId: PropTypes.string,
};
const mapStateToProps = function (state) {
  return {
    programList: selectProgramList(state),
    courseList: selectProgramCourseList(state),
  };
};

export default connect(mapStateToProps, actions)(ProgramFilterModal);
