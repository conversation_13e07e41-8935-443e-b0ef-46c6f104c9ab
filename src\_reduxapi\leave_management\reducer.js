import { fromJS, List, Map } from 'immutable';
import * as actions from './actions';
import { jsUcfirstAll } from '../../utils';
import { t } from 'i18next';

const initialState = fromJS({
  isLoading: false,
  error: {},
  message: '',
  permissionList: {},
  studentBlackBoxContent: {},
  isSaving: false,
  leaveCategories: {},
  editedPermission: {},
  institutionCalendar: [],
  activeInstitutionCalendar: {},
  myLeaveList: [],
  leaveOverview: [],
  permissionOverview: [],
  leave: {},
  leaveError: {},
  scheduleStatus: {},
  locations: {},
  leaveClassifications: '',
  StudentList: [],
  addwarning: {},
  addList: {},
  editedWarning: {},
  reviewerlist: [],
  leaavedata: {},
  rolesList: [],
  role: '',
  approverList: [],
  addApprover: [],
  editedApproverList: {},
  editedname: {},
  studentLeaveList: {},
  ReportList: {},
  breadcrumbs: '',
  programCourseList: [],
  studentRegisterList: {},
  criteriaManipulation: [],
  studentAttnSheetDetails: {},
  lmsSettings: {},
  programList: [],
  userList: [],
  signedUrl: '',
  attendanceLmsConfig: {},
  getLateExcludeStatus: false,
});

//eslint-disable-next-line
export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_PERMISSION_LIST_REQUEST: {
      return state.set('isLoading', true).set('editedPermission', Map());
    }
    case actions.ABSENCE_REPORT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ABSENCE_REPORT_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.ABSENCE_REPORT_FAILURE: {
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', action.error.response.data.message);
    }

    case actions.GET_PERMISSION_LIST_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('permissionList', fromJS(action.data.data[0] || {}))
        .set('editedPermission', Map());
    }
    case actions.GET_PERMISSION_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_LEAVE_CATEGORIES_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_LEAVE_CATEGORIES_SUCCESS: {
      return state.set('isLoading', false).set('leaveCategories', fromJS(action.data.data));
    }
    case actions.GET_LEAVE_CATEGORIES_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_LMS_APPROVER_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_LMS_APPROVER_LIST_SUCCESS: {
      return state.set('isLoading', false).set('approverList', fromJS(action.data));
    }
    case actions.GET_LMS_APPROVER_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.DELETE_APPROVER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_APPROVER_SUCCESS: {
      const { LevelName } = action.data;
      return state.set('isLoading', false).set(
        'message',
        LevelName !== undefined
          ? t('leaveManagement.successMsg.leaveApprovalMessage', {
              name: `"${jsUcfirstAll(LevelName)}"`,
              type: t(`leaveManagement.successMsg.delete`),
            })
          : t('deleted_successfully')
      );
    }
    case actions.DELETE_APPROVER_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.POST_ADD_APPROVER_REQUEST: {
      return state.set('isLoading', true);
    }

    case actions.POST_ADD_APPROVER_SUCCESS: {
      const { type, level } = action.data;
      return state

        .set('isLoading', false)
        .set('addApprover', fromJS(action.data.data))
        .set(
          'message',
          t('leaveManagement.successMsg.leaveApprovalMessage', {
            name: `"${jsUcfirstAll(level)}"`,
            type: t(`leaveManagement.successMsg.${type}`),
          })
        );
    }
    case actions.POST_ADD_APPROVER_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : t('an_error_occured_try_again'));
    }
    case actions.GET_SEARCH_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SEARCH_SUCCESS: {
      return state.set('isLoading', false).set('reviewerlist', fromJS(action.data.data));
    }
    case actions.GET_SEARCH_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.SET_LEAVE_DATA_SUCCESS: {
      return state.set('leaavedata', action.data);
    }

    case actions.GET_PROGRAMS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAMS_SUCCESS: {
      const { institution_calendar = [], programs = [] } = action.data;

      return state
        .set('isLoading', false)
        .set('institutionCalendar', fromJS(institution_calendar))
        .set('programList', fromJS(programs))
        .set(
          'activeInstitutionCalendar',
          fromJS(institution_calendar && institution_calendar.length ? institution_calendar[0] : {})
        );
    }
    case actions.GET_PROGRAMS_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }

    case actions.GET_LEAVE_CLASSIFICATIONS_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_LEAVE_CLASSIFICATIONS_LIST_SUCCESS: {
      return state.set('isLoading', false).set('locations', fromJS(action.data.data));
    }
    case actions.GET_LEAVE_CLASSIFICATIONS_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_STUDENT_REQUEST: {
      return state.set('isLoading', true).set('StudentList', fromJS([]));
    }
    case actions.GET_STUDENT_SUCCESS: {
      return state.set('isLoading', false).set('StudentList', fromJS(action.data.data));
    }
    case actions.GET_STUDENT_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_ROLES_LIST_REQUEST: {
      return state.set('isLoading', true).set('rolesList', List());
    }
    case actions.GET_ROLES_LIST_SUCCESS: {
      return state.set('isLoading', false).set('rolesList', fromJS(action.data));
    }
    case actions.GET_ROLES_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_REVIEWER_REQUEST: {
      return state.set('isLoading', true).set('reviewerlist', fromJS([])).set('role', '');
    }
    case actions.GET_REVIEWER_SUCCESS: {
      if (action.data.type === 'report_absence') {
        return state
          .set('isLoading', false)
          .set('reviewerlist', fromJS(action.data.data.data))
          .set('role', fromJS(action.data.data.role))
          .set('scheduleStatus', fromJS({}));
      } else {
        return state
          .set('isLoading', false)
          .set('reviewerlist', fromJS(action.data.data.data))
          .set('role', fromJS(action.data.data.role));
      }
    }
    case actions.GET_REVIEWER_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.POST_LEAVE_REVIEW_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_LEAVE_REVIEW_SUCCESS: {
      const { type, status } = action.data;
      return state
        .set('isLoading', false)
        .set('data', fromJS(action.data.data))
        .set(
          'message',
          `${
            type === 'on_duty'
              ? t('leaveManagement.tabs.On-Duty')
              : jsUcfirstAll(t(`leaveManagement.tabs.${type}`))
          } ${
            status === 'rejected' ? t('leaveManagement.Rejected') : t('leaveManagement.approved')
          } ${t('messages.successfully')}`
        );
    }
    case actions.POST_LEAVE_REVIEW_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.message}` : t('an_error_occured_try_again'));
    }
    case actions.EDIT_WARNING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.EDIT_WARNING_SUCCESS: {
      const operation = t('update');
      return state
        .set('isLoading', false)
        .set('data', fromJS(action.data.data))
        .set('message', t('leaveManagement.successMsg.permissionUpdated', { operation }));
    }
    case actions.EDIT_WARNING_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.DELETE_WARNING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_WARNING_SUCCESS: {
      const operation = t('delete');
      return state
        .set('isLoading', false)
        .set('data', fromJS(action.data.data))
        .set('message', t('leaveManagement.successMsg.permissionUpdated', { operation }));
    }
    case actions.DELETE_WARNING_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.POST_LEAVE_REPORT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_LEAVE_REPORT_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('reportdata', fromJS(action.data.data))
        .set('message', action.data.data)
        .set('scheduleStatus', fromJS({}));
    }
    case actions.POST_LEAVE_REPORT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set(
          'message',
          response && typeof response.data.data === 'string'
            ? response.data.data
            : response.data.message
        );
    }
    case actions.ROLE_ASSIGN_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ROLE_ASSIGN_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('role', fromJS(action.data.data))
        .set('message', action.data.data);
    }
    case actions.ROLE_ASSIGN_FAILURE: {
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', action.error.response.data.message);
    }

    case actions.GET_STAFF_REQUEST: {
      return state.set('isLoading', true).set('StaffList', fromJS([]));
    }
    case actions.GET_STAFF_SUCCESS: {
      return state.set('isLoading', false).set('StaffList', fromJS(action.data.data));
    }
    case actions.GET_STAFF_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_ABSENCE_REPORT_REQUEST: {
      return state.set('isLoading', true).set('ReportList', fromJS({}));
    }
    case actions.GET_ABSENCE_REPORT_SUCCESS: {
      return state.set('isLoading', false).set('ReportList', fromJS(action.data.data));
    }
    case actions.GET_ABSENCE_REPORT_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.ADD_WARNING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_WARNING_SUCCESS: {
      const operation = t('update');
      return state
        .set('isLoading', false)
        .set('addwarning', fromJS(action.data.data))
        .set('message', t('leaveManagement.successMsg.permissionUpdated', { operation }));
    }
    case actions.ADD_WARNING_FAILURE: {
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', action.error.response.data.message);
    }

    case actions.UPDATE_HR_CONTACT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_HR_CONTACT_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('hrmail', fromJS(action.data.data))
        .set('message', t('leaveManagement.successMsg.hrMailStatus'));
    }
    case actions.UPDATE_HR_CONTACT_FAILURE: {
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', action.error.response.data.message);
    }
    case actions.GET_MY_LEAVES_LIST_REQUEST: {
      return state
        .set('isLoading', true)
        .set('myLeaveList', List())
        .set('leave', Map())
        .set('scheduleStatus', Map());
    }
    case actions.GET_MY_LEAVES_LIST_SUCCESS: {
      return state.set('isLoading', false).set('myLeaveList', fromJS(action.data.data));
    }
    case actions.GET_MY_LEAVES_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }
    case actions.GET_LEAVE_OVERVIEW_REQUEST: {
      return state.set('isLoading', true).set('leaveOverview', List());
    }
    case actions.GET_LEAVE_OVERVIEW_SUCCESS: {
      return state.set('isLoading', false).set('leaveOverview', fromJS(action.data.data));
    }
    case actions.GET_LEAVE_OVERVIEW_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }
    case actions.GET_PERMISSION_OVERVIEW_REQUEST: {
      return state.set('isLoading', true).set('permissionOverview', List());
    }
    case actions.GET_PERMISSION_OVERVIEW_SUCCESS: {
      return state.set('isLoading', false).set('permissionOverview', fromJS(action.data.data));
    }
    case actions.GET_PERMISSION_OVERVIEW_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }
    case actions.GET_SCHEDULE_STATUS_REQUEST: {
      return state.set('isLoading', true).set('scheduleStatus', Map());
    }
    case actions.GET_SCHEDULE_STATUS_SUCCESS: {
      return state.set('isLoading', false).set('scheduleStatus', fromJS(action.data));
    }
    case actions.GET_SCHEDULE_STATUS_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }
    case actions.GET_STUDENT_LIST_REQUEST: {
      return state.set('isLoading', true).set('studentLeaveList', fromJS([]));
    }
    case actions.GET_STUDENT_LIST_SUCCESS: {
      return state.set('isLoading', false).set('studentLeaveList', fromJS(action.data.data));
    }
    case actions.GET_STUDENT_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }
    case actions.GET_STAFF_PROFILE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_STAFF_PROFILE_SUCCESS: {
      return state.set('isLoading', false).set('staffprofile', fromJS(action.data));
    }
    case actions.GET_STAFF_PROFILE_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }
    case actions.APPLY_LEAVE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.APPLY_LEAVE_SUCCESS: {
      const { operation, title } = action.data;
      return state
        .set('isLoading', false)
        .set(
          'message',
          `${jsUcfirstAll(t(`leaveManagement.tabs.${title}`))} ${
            operation === 'create'
              ? t('leaveManagement.successMsg.applied')
              : t('leaveManagement.successMsg.Updated')
          } ${t('messages.successfully')}.`
        );
    }
    case actions.APPLY_LEAVE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', fromJS(action.error))
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('global_configuration.action_response_messages.an_error_occured_try_again')
        );
    }
    case actions.GET_LEAVE_BY_ID_REQUEST: {
      return state
        .set('isLoading', true)
        .set('leave', Map())
        .set('leaveError', Map())
        .set('scheduleStatus', Map());
    }
    case actions.GET_LEAVE_BY_ID_SUCCESS: {
      return state.set('isLoading', false).set('leave', fromJS(action.data.data));
    }
    case actions.GET_LEAVE_BY_ID_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('leaveError', fromJS(response || action.error))
        .set('message', response && `${response.data.message}`);
    }
    case actions.CANCEL_LEAVE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.CANCEL_LEAVE_SUCCESS: {
      const { title } = action.data;
      return state
        .set('isLoading', false)
        .set(
          'message',
          `${jsUcfirstAll(title)} ${t('leaveManagement.successMsg.Cancelled_Successfully')}.`
        );
    }
    case actions.CANCEL_LEAVE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response ? `${response.data.message}` : 'Error while cancelling the leave.'
        );
    }
    case actions.UPDATE_PERMISSION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_PERMISSION_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('leaveManagement.successMsg.permissionUpdated'));
    }
    case actions.UPDATE_PERMISSION_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.UPDATE_LEAVE_CATEGORY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_LEAVE_CATEGORY_SUCCESS: {
      const { type, componentName } = action.data;
      let getType = t(
        `leaveManagement.successMsg.${
          type === 'on_duty'
            ? 'On-Duty'
            : !type
            ? jsUcfirstAll(componentName === 'on_duty' ? 'On-Duty' : componentName)
            : jsUcfirstAll(type)
        }`
      );
      let operation = t(`leaveManagement.successMsg.${action.data.operation}`);
      return state
        .set('isLoading', false)
        .set(
          'message',
          t('leaveManagement.successMsg.categorySuccess', { type: getType, operation })
        );
    }
    case actions.UPDATE_LEAVE_CATEGORY_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : t('an_error_occured_try_again'));
    }
    case actions.UPDATE_LEAVE_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_LEAVE_TYPE_SUCCESS: {
      const { ComponentName } = action.data;
      let getType = t(
        `leaveManagement.successMsg.${ComponentName === 'on_duty' ? 'On-Duty' : 'Leave'}`
      );
      let operation = t(`leaveManagement.successMsg.${action.data.operation}`);
      return state
        .set('isLoading', false)
        .set('message', t('leaveManagement.successMsg.typeSuccess', { type: getType, operation }));
    }
    case actions.UPDATE_LEAVE_TYPE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : t('an_error_occured_try_again'));
    }
    case actions.DELETE_LEAVE_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_LEAVE_TYPE_SUCCESS: {
      let type = t('leaveManagement.successMsg.Leave');
      let operation = t('leaveManagement.successMsg.delete');
      return state.set('isLoading', false).set(
        'message',
        t('leaveManagement.successMsg.typeSuccess', {
          type,
          operation,
        })
      );
    }
    case actions.DELETE_LEAVE_TYPE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : t('an_error_occured_try_again'));
    }
    case actions.DEACTIVATION_LEAVE_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DEACTIVATION_LEAVE_TYPE_SUCCESS: {
      const { isActive } = action.data.requestBody;
      const leaveUpdate = isActive
        ? t('leaveManagement.successMsg.activated')
        : t('leaveManagement.successMsg.deactivated');
      return state
        .set('isLoading', false)
        .set('message', t('leaveManagement.successMsg.onlyTypeSuccess', { leaveUpdate }));
    }
    case actions.DEACTIVATION_LEAVE_TYPE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : t('an_error_occured_try_again'));
    }
    case actions.SET_BREADCRUMB_SUCCESS: {
      return state.set('breadcrumbs', fromJS(action.breadcrumbs));
    }
    case actions.UPDATE_RP_ABSENCE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_RP_ABSENCE_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('leaveManagement.successMsg.reportAbsenceSettingMsg'));
    }
    case actions.UPDATE_RP_ABSENCE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : t('an_error_occured_try_again'));
    }
    case actions.GET_STUDENT_REGISTER_PROGRAM_LIST_REQUEST: {
      return state.set('isLoading', true).set('programCourseList', List());
    }
    case actions.GET_STUDENT_REGISTER_PROGRAM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('programCourseList', fromJS(action.data));
    }
    case actions.GET_STUDENT_REGISTER_PROGRAM_LIST_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : t('an_error_occured_try_again');
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GET_STUDENT_REGISTER_LIST_REQUEST: {
      return state.set('isLoading', true).set('studentRegisterList', Map());
    }
    case actions.GET_STUDENT_REGISTER_LIST_SUCCESS: {
      return state.set('isLoading', false).set('studentRegisterList', fromJS(action.data));
    }
    case actions.GET_STUDENT_REGISTER_LIST_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.GET_CRITERIA_MANIPULATION_REQUEST: {
      return state.set('isLoading', true).set('criteriaManipulation', List());
    }
    case actions.GET_CRITERIA_MANIPULATION_SUCCESS: {
      return state.set('isLoading', false).set('criteriaManipulation', fromJS(action.data));
    }
    case actions.GET_CRITERIA_MANIPULATION_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.UPDATE_ST_ABSENCE_PERCENTAGE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_ST_ABSENCE_PERCENTAGE_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_ST_ABSENCE_PERCENTAGE_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.GET_STUDENT_ATTN_SHEET_DETAILS_REQUEST: {
      return state.set(!action.isLoading ? 'isSaving' : 'isLoading', true);
    }
    case actions.GET_STUDENT_ATTN_SHEET_DETAILS_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('isSaving', false)
        .set('studentAttnSheetDetails', fromJS(action.data));
    }
    case actions.GET_STUDENT_ATTN_SHEET_DETAILS_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.GET_BLACK_BOX_CONTENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_BLACK_BOX_CONTENT_SUCCESS: {
      return state.set('isLoading', false).set('studentBlackBoxContent', fromJS(action.data));
    }
    case actions.GET_BLACK_BOX_CONTENT_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.UPDATE_REASON_REQUEST: {
      return state.set('isSaving', true);
    }
    case actions.UPDATE_REASON_SUCCESS: {
      return state.set('isSaving', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_REASON_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.GET_LMS_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_LMS_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('lmsSettings', fromJS(action.data));
    }
    case actions.GET_LMS_SETTINGS_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.UPDATE_GLOBAL_CONFIG_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_GLOBAL_CONFIG_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_GLOBAL_CONFIG_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.UPDATE_LEVEL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_LEVEL_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_LEVEL_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.VALIDATION_ERROR: {
      return state.set('isLoading', false).set('message', action.error.message);
    }

    case actions.GET_USER_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_USER_SUCCESS: {
      return state.set('isLoading', false).set('userList', fromJS(action.data));
    }
    case actions.GET_USER_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.ADD_CATEGORY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_CATEGORY_SUCCESS: {
      return state.set('isLoading', false).set('message', t('saved_successfully'));
    }
    case actions.ADD_CATEGORY_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.DELETE_CATEGORY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_CATEGORY_SUCCESS: {
      return state.set('isLoading', false).set('message', t('deleted_successfully'));
    }
    case actions.DELETE_CATEGORY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.ADD_CATEGORY_TYPES_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_CATEGORY_TYPES_SUCCESS: {
      return state.set('isLoading', false).set('message', t('saved_successfully'));
    }
    case actions.ADD_CATEGORY_TYPES_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.UPDATE_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_TYPE_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_TYPE_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.DELETE_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_TYPE_SUCCESS: {
      return state.set('isLoading', false).set('message', t('deleted_successfully'));
    }
    case actions.DELETE_TYPE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_TERMS_AND_CONDITIONS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_TERMS_AND_CONDITIONS_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_TERMS_AND_CONDITIONS_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.UPDATE_WARNING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_WARNING_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_WARNING_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_CATEGORY_STATUS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_CATEGORY_STATUS_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_CATEGORY_STATUS_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_CATEGORY_NAME_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_CATEGORY_NAME_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_CATEGORY_NAME_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_CATEGORY_TYPES_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_CATEGORY_TYPES_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_CATEGORY_TYPES_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_GENDER_SEGREGATION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_GENDER_SEGREGATION_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_GENDER_SEGREGATION_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_EXCEPTIONAL_PROGRAM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_EXCEPTIONAL_PROGRAM_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_EXCEPTIONAL_PROGRAM_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.UPDATE_WARNING_CONFIG_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_WARNING_CONFIG_SUCCESS: {
      const configKey = action.data.configKey;
      const findIndex = state.getIn(['lmsSettings', configKey], List()).findIndex((item) => {
        return item.get('_id', '') === action.data.warningConfigId;
      });
      return state
        .set('isLoading', false)
        .set('message', t('updated_successfully'))
        .setIn(['lmsSettings', configKey, findIndex, 'isActive'], action.data.isActive);
    }
    case actions.UPDATE_WARNING_CONFIG_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.ADD_LEAVE_POLICY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_LEAVE_POLICY_SUCCESS: {
      return state.set('isLoading', false).set('message', t('saved_successfully'));
    }
    case actions.ADD_LEAVE_POLICY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.EDIT_LEAVE_POLICY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.EDIT_LEAVE_POLICY_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.EDIT_LEAVE_POLICY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.DELETE_LEAVE_POLICY_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_LEAVE_POLICY_SUCCESS: {
      return state.set('isLoading', false).set('message', t('deleted_successfully'));
    }
    case actions.DELETE_LEAVE_POLICY_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.UPLOAD_ATTACHMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPLOAD_ATTACHMENT_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPLOAD_ATTACHMENT_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.DELETE_EXCEPTIONAL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_EXCEPTIONAL_SUCCESS: {
      return state.set('isLoading', false).set('message', t('deleted_successfully'));
    }
    case actions.DELETE_EXCEPTIONAL_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.DELETE_WARNING_CONFIG_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_WARNING_CONFIG_SUCCESS: {
      return state.set('isLoading', false).set('message', t('deleted_successfully'));
    }
    case actions.DELETE_WARNING_CONFIG_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GENERATE_URL_REQUEST: {
      return state.set('isLoading', true).set('signedUrl', null);
    }
    case actions.GENERATE_URL_SUCCESS: {
      return state.set('isLoading', false).set('signedUrl', action.data.signedUrl);
    }
    case actions.GENERATE_URL_FAILURE: {
      return state.set('isLoading', false);
    }

    case actions.RESET_DENIAL_CONDITION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.RESET_DENIAL_CONDITION_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Reset Successfully');
    }
    case actions.RESET_DENIAL_CONDITION_FAILURE: {
      return state.set('isLoading', false).set('error', fromJS(action.error));
    }
    case actions.UPDATE_ATTENDANCE_CONFIGURATION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_ATTENDANCE_CONFIGURATION_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Updated SuccessFully');
    }
    case actions.UPDATE_ATTENDANCE_CONFIGURATION_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.ATTENDANCE_CONFIGURATION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ATTENDANCE_CONFIGURATION_SUCCESS: {
      return state.set('isLoading', false).set('attendanceLmsConfig', fromJS(action.data));
    }
    case actions.ATTENDANCE_CONFIGURATION_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.GET_LATE_EXCLUDE_STATUS_REQUEST: {
      return state.set('getLateExcludeStatus', false);
    }
    case actions.GET_LATE_EXCLUDE_STATUS_SUCCESS: {
      return state.set('getLateExcludeStatus', fromJS(action.data));
    }
    case actions.GET_LATE_EXCLUDE_STATUS_FAILURE: {
      return prepareErrorMessage(action, state);
    }
    case actions.UPDATE_LATE_EXCLUDE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_LATE_EXCLUDE_SUCCESS: {
      return state.set('isLoading', false).set('data', fromJS(action.data.data));
    }
    case actions.UPDATE_LATE_EXCLUDE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.DELETE_LMS_SETTINGS_DATA_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_LMS_SETTINGS_DATA_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Deleted SuccessFully');
    }
    case actions.DELETE_LMS_SETTINGS_DATA_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    case actions.UPDATE_COURSEORCOMPREHENSIVE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_COURSEORCOMPREHENSIVE_SUCCESS: {
      return state.set('isLoading', false).set('message', t('updated_successfully'));
    }
    case actions.UPDATE_COURSEORCOMPREHENSIVE_FAILURE: {
      return prepareErrorMessage(action, state);
    }

    default:
      return state;
  }
}

export function prepareErrorMessage(action, state) {
  const { response: { data: { message = '' } = {} } = {} } = action.error;
  const errorMessage =
    message && typeof message === 'string' ? message : t('an_error_occured_try_again');
  return state
    .set('isLoading', false)
    .set('isSaving', false)
    .set('isLoadingType', '')
    .set('message', errorMessage);
}
