import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Trans } from 'react-i18next';
import University_logo from '../../../../../../Assets/university_req.svg';
import individualCollege from '../../../../../../Assets/individualCollege.svg';
import camera from '../../../../../../Assets/camera.svg';
import { t } from 'i18next';

function InstitutionLogo({ isCollege, logo, setLogo, setData }) {
  function getLogoUrl() {
    if (logo) {
      return URL.createObjectURL(logo);
    }
    return logo;
  }
  function getBrowseButton() {
    const hasNoLogo = !logo;
    return (
      <div className={hasNoLogo ? 'mb-0 pt-4 mt-3 text-center' : 'pt-1'}>
        <label
          htmlFor="logo-upload"
          //className="file-upload btn btn-outline-primary border-radious-8 f-14"
        >
          <div> {hasNoLogo && <img src={camera} alt="camera" />} </div>
          <div
            style={{
              color: '#147AFC',
              fontSize: '16px',
              cursor: 'pointer',
            }}
          >
            {hasNoLogo ? 'ADD PHOTO' : 'CHANGE'}
          </div>
          <input
            style={{ display: 'none' }}
            id="logo-upload"
            type="file"
            accept=".jpg,.jpeg,.png"
            onChange={(e) => checkValidation(...e.target.files)}
          />
        </label>
      </div>
    );
  }
  function getLogoInfo() {
    /* if (!logo) { */
    return (
      <>
        <p className="mb-0 pt-2 mt-3 bold f-14">{`${
          !logo ? t('upload') : t('update')
        } your Logo`}</p>
        <p className="f-10 text-gray">
          <Trans i18nKey={'add_colleges.file_format'}></Trans>
        </p>
        {logo ? getBrowseButton() : ''}
      </>
    );
    /* }
    return (
      <>
        <p className="mb-0 pt-3 font-weight-500 f-14 text-black file-name">
          {`File Name: ${logo ? logo.name : ''}`}
        </p>
      </>
    ); */
  }
  const checkValidation = (input) => {
    const fileSize = input.size / 1024 / 1024; // in MiB
    if (!['image/jpeg', 'image/jpg', 'image/png'].includes(input.type)) {
      setData(Map({ message: `JPG, JPEG, PNG format only allowed` }));
      setLogo('');
      return;
    } else if (fileSize > 1) {
      setData(Map({ message: `Upload File less than 1mb` }));
      setLogo('');
      return;
    }
    setLogo(input);
  };
  return (
    <div className="d-flex mt-4">
      <div>
        {logo ? (
          <div className="logo_view">
            <img
              src={getLogoUrl()}
              alt="logo"
              className="img-fluid logo-img-border height_150px width_150px"
            />
          </div>
        ) : (
          <div className="upload_logo">{getBrowseButton()}</div>
        )}
      </div>
      <div className="ml-3" style={{ width: '30%' }}>
        {getLogoInfo()}
      </div>
      <div className="text-right">
        <img
          src={isCollege ? individualCollege : University_logo}
          width="80%"
          height="80%"
          alt="universityLogo"
        />
      </div>
    </div>
  );
}

InstitutionLogo.propTypes = {
  isCollege: PropTypes.bool,
  logo: PropTypes.object,
  setLogo: PropTypes.func,
  setData: PropTypes.func,
};
export default InstitutionLogo;
