import React, { Fragment, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { PropTypes } from 'prop-types';

import { List, Map } from 'immutable';
import preview_image from 'Assets/q360_dashboard/doc_view/preview_image.svg';
// import ImageIcon from '@mui/icons-material/Image';
import CircleIcon from '@mui/icons-material/Circle';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Avatar,
  // Avatar,
  Box,
  Button,
  Checkbox,
  // Chip,
  // Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  InputAdornment,
  OutlinedInput,
  Radio,
  Switch,
  // FormControlLabel,
  // Radio,
  // Switch,
  Tooltip,
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';

import {
  getConcludingPhase,
  getSignedUrl,
  getFormConfigureList,
  updateCategoryForm,
} from '_reduxapi/q360/actions';
// import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { formatToTwoDigitString } from 'Modules/GlobalConfigurationV2/utils/jsUtils';
import { useCurrentPage, useSearchParams } from '../..';
import { DrawerRight, FileViewAtDrawer } from './Step2';
// import { ExpandMoreOutlined } from '@mui/icons-material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import {
  useBooleanHook,
  useCallApiHook,
  useInputHook,
  useNestedHook,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { selectFormConfigureList } from '_reduxapi/q360/selectors';
import { useSelector } from 'react-redux';
import { ExpandMore, ExpandMoreOutlined, Search } from '@mui/icons-material';
import { numberMonth, useConfigureTemplate } from './Step1/utils';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import { ConditionalWrapper } from './Step1/ConfigureModal2';

//----------------------------------UI Utils Start--------------------------------------------------
const CircleIconSx = {
  fontSize: '6px',
  marginX: '4px',
};
const ToolTipSpecificSx = { maxHeight: '100px', overflowY: 'auto' };

// const specificSections = List([
//   'A. General information about the course:',
//   '\n              B. Course Learning Outcomes (CLOs), Teaching Strategies and Assessment Methods\n            ',
// ]);

const searchInputSx = {
  width: '300px',
  height: '35px',
  fontSize: '12px',
  '&:focus': {
    outline: 'none !important',
  },
  '&:hover': {
    border: '1px solid #D1D5DB !important',
  },
};
const buttonSx = {
  backgroundColor: '#147AFC !important',
  color: '#FFFFFF',
  padding: '4px 30px',
  display: 'block',
  width: '100%',
};

const switchSX = {
  '& .MuiSwitch-thumb': { background: '#147afc', width: '17px', height: '17px' },
  '& .MuiSwitch-track': {
    height: '85%',
    width: '90%',
    background: '#D1D5DB !important',
  },
  '& .MuiSwitch-switchBase:not(.Mui-checked) .MuiSwitch-thumb': {
    background: '#F3F4F6',
  },
};
// const AccordionSummaryMargin = {
//   '& .MuiAccordionSummary-content': {
//     margin: '5px 0px',
//   },
//   '&.MuiAccordionSummary-root': {
//     padding: '4px 16px',
//   },
// };
// const switchGrayColor = {
//   '& .MuiSwitch-switchBase.Mui-checked': {
//     color: '#9CA3AF',
//   },
//   '& .MuiSwitch-switchBase.Mui-checked+.MuiSwitch-track': {
//     backgroundColor: '#E5E7EB',
//   },
// };
//----------------------------------UI Utils End----------------------------------------------------

const numbers = [
  { name: 'Select', value: 0 },
  {
    name: 1,
    value: 1,
  },
  {
    name: 2,
    value: 2,
  },
  {
    name: 3,
    value: 3,
  },
  {
    name: 4,
    value: 4,
  },
];

// const Loader = () => {
//   return (
//     <React.Fragment>
//       <svg width={0} height={0}>
//         <defs>
//           <linearGradient id="my_gradient" x1="0%" y1="0%" x2="0%" y2="100%">
//             <stop offset="0%" stopColor="#e01cd5" />
//             <stop offset="100%" stopColor="#1CB5E0" />
//           </linearGradient>
//         </defs>
//       </svg>
//       <CircularProgress
//         sx={{
//           'svg circle': { stroke: 'url(#my_gradient)' },
//           width: '15px',
//           height: '15px',
//           marginLeft: '10px',
//         }}
//       />
//     </React.Fragment>
//   );
// };
// const fileIconRender = {
//   jpg: <ImageIcon className="f-16" />,
//   jpeg: <ImageIcon className="f-16" />,
//   png: <ImageIcon className="f-16" />,
//   pdf: <PictureAsPdfIcon className="f-16" />,
//   mp4: <VideoLibraryIcon className="f-16" />,
// };
//----------------------------------JS Utils Start--------------------------------------------------
const constructOccurrenceDetails = (data, level = 'course') => {
  if (level === 'institution') {
    return data.map((course) => Map({ name: course.get('institutionName', '') }));
  }
  const programData = data.reduce((acc, course) => {
    const programId = course.get('programId', '');
    const programName = course.get('programName', '');
    const curriculumId = course.get('curriculumId', '');
    const curriculumName = course.get('curriculumName', '');
    const programKey = `${programId}/${programName}`;
    const curriculumKey = `${curriculumId}/${curriculumName}`;

    const programMap = acc.has(programKey)
      ? acc.get(programKey)
      : Map({
          name: programName,
          curriculums: Map(),
        });

    const curriculumMap = programMap
      .getIn(['curriculums', curriculumKey], Map({ name: curriculumName, courseCount: 0 }))
      .update('courseCount', (count) => count + 1);

    const updatedProgramMap = programMap.setIn(['curriculums', curriculumKey], curriculumMap);

    return acc.set(programKey, updatedProgramMap);
  }, Map());
  return programData
    .map((program, key) => {
      const programId = key.split('/')[0];
      return Map({
        name: program.get('name', ''),
        programId: programId,
        curriculum: program
          .get('curriculums')
          .map((curriculum) => (level !== 'course' ? curriculum.delete('courseCount') : curriculum))
          .toList(),
      });
    })
    .toList();
};
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------
//----------------------------------custom hooks end------------------------------------------------
const ToolTipSpecific = ({ specificSections }) => {
  return (
    <div>
      <div className="bold f-14">Specific Sections</div>
      <Box sx={ToolTipSpecificSx}>
        {specificSections.map((sections) => (
          <div key={sections} className="f-10 ml-3">
            {sections}
          </div>
        ))}
      </Box>
    </div>
  );
};
//----------------------------------componentStart--------------------------------------------------
const ConclusionTemplate = ({ children }) => {
  return (
    <Box boxShadow="1" className="bg-white rounded p-4" component="div">
      {children}
    </Box>
  );
};
// const FileViewRender = ({ SignedUrl, type }) => {
//   if (type === 'pdf') {
//     return (
//       <div className="attachment-container">
//         <iframe src={SignedUrl} className="file-viewer" />
//       </div>
//     );
//   } else if (type === 'mp4') {
//     return <video src={SignedUrl} className="w-100 h-100" />;
//   } else {
//     return (
//       <img alt="No-attachment" src={SignedUrl} className="h-100 w-100 object-fit-contain rounded" />
//     );
//   }
// };
const CategorySection = ({ category, specificSections }) => {
  const lowerCaseCategory = category.toLowerCase();
  if (lowerCaseCategory === 'entire') {
    return (
      <>
        <Divider orientation="vertical" flexItem sx={{ borderRightWidth: 1.5 }} />
        <div className="f-12 text-capitalize">{category} Form</div>
      </>
    );
  }
  const isSpecific = category == 'specific';
  const isEntire = category === 'entire';
  const isAnyOne = isSpecific || isEntire;
  return (
    <>
      {isAnyOne && <Divider orientation="vertical" flexItem sx={{ borderRightWidth: 1.5 }} />}
      <div className="d-flex align-items-center">
        {isSpecific && <div className="f-12">Specific Form</div>}
        <Tooltip
          arrow
          placement="top"
          title={<ToolTipSpecific specificSections={specificSections} />}
        >
          {isSpecific && <InfoOutlinedIcon className="f-12 ml-1" />}
        </Tooltip>
      </div>
    </>
  );
};
const TurnAroundTimeDisplay = ({ turnAroundTime }) => {
  return (
    <div className="d-flex gap-8 align-items-center flex-nowrap">
      <div className="f-12">
        <span>TAT : </span>
        <span>{formatToTwoDigitString(turnAroundTime)} Days</span>
      </div>
    </div>
  );
};
const BasicInfo = ({ Conclusion, handleChange, formName }) => {
  return (
    <div className="flex-grow-1 ta-header d-flex flex-column gap-8">
      <div className="bold text-primary-font">Basic Info</div>
      <div>
        <label className="m-0 f-12">Form Name :</label>
        <div className="bold f-14 text-capitalize">{formName.replaceAll('**', '&')}</div>
      </div>
      <BasicInfo.AssignOccurrenceDetails Conclusion={Conclusion} handleChange={handleChange} />
    </div>
  );
};
BasicInfo.AssignOccurrenceDetails = ({ Conclusion, handleChange }) => {
  const formCourseSettingData = Conclusion.get('formCourseSettingData', List());
  const [params] = useSearchParams();
  const level = params.get('level');
  if (formCourseSettingData.hasIn([0, 'institutionName'], '')) {
    return formCourseSettingData.map((institution, i) => {
      const institutionName = institution.get('institutionName', '');
      const assignedInstitutionId = institution.get('assignedInstitutionId', '');
      return (
        <div
          className="d-flex "
          key={i}
          onClick={() => handleChange(institutionName, assignedInstitutionId)}
        >
          <div className="occurrence-details px-3 py-2 l-blue rounded cursor-pointer f-12">
            {institution.get('institutionName', '')}
          </div>
        </div>
      );
    });
  }
  const occurrenceDetails = constructOccurrenceDetails(formCourseSettingData);
  if (!occurrenceDetails.size) {
    return null;
  }
  return (
    <>
      <div className="d-flex flex-column">
        <label className="m-0 f-12 mb-1">Assign & Occurrence Details :</label>
        {level === 'program' || level === 'institution' ? (
          <div className="d-inline-flex gap-10 flex-wrap">
            {occurrenceDetails.map((occurrence, occurrenceIndex) => {
              const programName = occurrence.get('name', '');
              const programId = occurrence.get('programId', '');
              const curriculum = occurrence.get('curriculum', List());
              return (
                <div
                  className="occurrence-details px-3 py-2 l-blue rounded cursor-pointer f-12"
                  key={occurrenceIndex}
                  onClick={() => handleChange(programName, programId)}
                >
                  <div className="m-0 p-0 bold">{programName}</div>
                  <div className="occurrence-text-truncate">
                    {curriculum.map((curriculumItems, curriculumIndex) => {
                      const curriculumName = curriculumItems.get('name', '');
                      const shouldRenderCircleIcon = curriculumIndex !== curriculum.size - 1;
                      return (
                        <React.Fragment key={curriculumIndex}>
                          {curriculumName}
                          {shouldRenderCircleIcon && <CircleIcon sx={CircleIconSx} />}
                        </React.Fragment>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="d-inline-flex gap-10 flex-wrap">
            {occurrenceDetails.map((occurrence, occurrenceIndex) => {
              const programName = occurrence.get('name', '');
              const programId = occurrence.get('programId', '');
              const curriculum = occurrence.get('curriculum', List());
              return (
                <div
                  className="occurrence-details px-3 py-2 l-blue rounded cursor-pointer f-12"
                  key={occurrenceIndex}
                  onClick={() => handleChange(programName, programId)}
                >
                  <div className="m-0 p-0 bold">{programName}</div>
                  <div className="occurrence-text-truncate">
                    {curriculum.map((curriculumItems, curriculumIndex) => {
                      const curriculumName = curriculumItems.get('name', '');
                      const courseCount = curriculumItems.get('courseCount', 0);
                      const shouldRenderCircleIcon = curriculumIndex !== curriculum.size - 1;
                      const courseCountString = courseCount
                        ? ` ${formatToTwoDigitString(courseCount)} Courses `
                        : '';
                      return (
                        <React.Fragment key={curriculumIndex}>
                          {curriculumName}
                          {courseCountString}
                          {shouldRenderCircleIcon && <CircleIcon sx={CircleIconSx} />}
                        </React.Fragment>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </>
  );
};
const AttachmentFiles = ({ attachments }) => {
  // const lastAttachment = attachments.size - 1;
  // const attachmentSize = formatToTwoDigitString(attachments.size);
  // const attachmentLastName = attachments.getIn([lastAttachment, 'name'], '');
  // const attachmentSignedUrl = attachments.getIn([lastAttachment, 'signedUrl'], '');
  // const attachmentUrl = attachments.getIn([lastAttachment, 'url'], '');
  // const urlParts = attachmentUrl.split('.');
  // const lastIndex = urlParts.len/gth - 1;
  // const type = urlParts[lastIndex].toLowerCase();
  // const fileIcon = fileIconRender[type];
  return (
    // <div className="d-flex flex-column">
    //   <label className="m-1 txt-slate-gray f-12">Guide Resources</label>
    //   <div className="d-flex flex-column gap-8">
    //     <div className=" flex-grow-1 rounded border d-flex flex-column cursor-pointer">
    //       <div className="attachment-files-Concluding">
    //         <FileViewRender SignedUrl={attachmentSignedUrl} type={type} />
    //       </div>
    //       <div className="d-flex align-items-center gap-5 py-1 px-3 border-top text-start f-14 conclusion-text-truncate">
    //         {fileIcon} {getShortString(attachmentLastName, 12)}
    //       </div>
    //     </div>
    //     <div className="text-cGrey border rounded px-2 py-1 text-center f-10 cursor-pointer text-uppercase">
    //       Attached {attachmentSize} files
    //     </div>
    //   </div>
    // </div>
    <AttachedDocsChatSection attachments={attachments} />
  );
};
export const ApproverHierarchy = ({
  approvalLevel,
  headerClass = 'bold text-primary-font mb-2',
}) => {
  return (
    <>
      <div className={headerClass}>Approver Hierarchy</div>
      <div className="approver-container-Concluding">
        {approvalLevel.map((levelData, levelIndex) => {
          const levelName = levelData.get('name', '');
          const category = levelData.get('category', '');
          const turnAroundTime = levelData.get('turnAroundTime', 0);
          const specificSections = levelData.get('specificSections', 0);
          return (
            <div key={levelIndex}>
              <div className="bold f-14 mb-2">{levelName}</div>
              <div className="d-flex gap-8">
                <TurnAroundTimeDisplay turnAroundTime={turnAroundTime} />
                <CategorySection category={category} specificSections={specificSections} />
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};
//----------------------------------componentEnd----------------------------------------------------

const Step4 = () => {
  const dispatch = useDispatch();
  const categoryFormId = useCurrentPage('categoryFormId');
  const formDetailPageQuery = useCurrentPage('query');
  const formName = formDetailPageQuery.split(' ').pop().replaceAll(',', ' ');
  const [Conclusion, setConclusion] = useState(Map());
  const [isOpen, setIsOpen] = useBooleanHook(false);
  const [program, setProgram] = useState(Map());
  const [currenCategory] = useConfigureTemplate();

  const [updateForm] = useCallApiHook(updateCategoryForm);

  const categoryId = currenCategory.get('_id', '');
  const approvalLevel = Conclusion.getIn(['formSettingData', 'approvalLevel'], List());
  const attachments = Conclusion.getIn(['formSettingData', 'attachments'], List());
  const handleClose = () => setIsOpen();
  const handleChange = (program, programId) => {
    setProgram((prev) => prev.set('programName', program).set('programId', programId));
    handleClose();
  };
  const onFetchSuccess = (data) => {
    setConclusion(data);
  };
  useEffect(() => {
    dispatch(getConcludingPhase(categoryFormId, onFetchSuccess));
    return () => {
      updateForm({
        categoryId,
        categoryFormId,
        step: 4,
      });
    };
  }, []);
  const programName = program.get('programName', '');
  const programId = program.get('programId', '');
  return (
    <div className="mb-3">
      <ConclusionTemplate>
        <div className="d-flex gap-5 flex-sm-nowrap flex-wrap-reverse">
          <BasicInfo formName={formName} Conclusion={Conclusion} handleChange={handleChange} />
          {attachments.size !== 0 && <AttachmentFiles attachments={attachments} />}
        </div>
        <Divider className="my-3" />
        <ProgramConfigurationView
          programName={programName}
          programId={programId}
          conclusion={Conclusion}
          isOpen={isOpen}
          setIsOpen={handleClose}
        />
        <ApproverHierarchy approvalLevel={approvalLevel} />
      </ConclusionTemplate>
    </div>
  );
};

export default Step4;

////////////////////////Documnet preview
export function AttachedDocsChatSection({ attachments, drawerName = 'Guide Resources' }) {
  const initialDoc = attachments.get(0, Map());
  const [openModalIndex, setOpenModalIndex] = useState(0);
  const initialDocFileType = initialDoc.get('name', '').split('.').pop();
  const [open, setOpen] = useState(
    Map({
      openType: '', //displayAttach,popOver
      attach: Map(),
      viewType: '', //single,multiple
    })
  );
  const [signedUrl, setSignedUrl] = useState('');
  function handleAttachClose() {
    setOpen(() =>
      Map({
        openType:
          // prev.get('viewType', '') === 'multiple' &&
          // discussion.get('attachments', List()).size > 2
          //   ? 'popOver':
          '',
      })
    );
  }
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(
      getSignedUrl(initialDoc.get('url', ''), (data) => {
        setSignedUrl(data);
      })
    );
  }, []); //eslint-disable-line
  return (
    <div>
      {signedUrl !== '' && (
        <div
          className="forum_doc_sub remove_hover"
          onClick={(e) => {
            e.stopPropagation();
            setOpen((prev) => prev.set('openType', 'single'));
          }}
        >
          <div className="gradient-container">
            {initialDocFileType !== 'pdf' ? (
              <img
                src={signedUrl}
                alt={`Uploaded Images was Expired (${initialDoc.get('name', '')})`}
                className="forum_preview_image"
                // width="100%"
              />
            ) : (
              <iframe title="docs" src={signedUrl} className="forum_preview_image" />
            )}
          </div>
          <div className="d-flex px-4 p-2 f-14">
            {initialDocFileType === 'pdf' ? (
              <PictureAsPdfIcon className="mb--6" />
            ) : (
              <img src={preview_image} alt="image2" className="height_19px" />
            )}
            <div className="pl-2 bold text-wrap">
              {getShortString(initialDoc.get('name', ''), 18)}
            </div>
          </div>
        </div>
      )}

      {signedUrl !== '' && attachments.size > 1 && (
        <DrawerRight showDelete={false} filesData={attachments}>
          <div
            className="attached_more_file remove_hover"
            // onClick={(e) => {
            //   e.stopPropagation();
            //   setOpen(
            //     Map({
            //       openType: 'multiple',
            //     })
            //   );
            // }}
          >
            ATTACHED {attachments.size - 1} FILES
          </div>
        </DrawerRight>
      )}
      {/* <UploadModal
        file={initialDoc}
        open={open.get('openType', '') === 'single'}
        handleClose={handleAttachClose}
      /> */}
      <FileViewAtDrawer
        file={attachments.get(openModalIndex, Map())}
        open={open.get('openType', '') === 'single'}
        // formName={formName}
        drawerName={drawerName}
        index={openModalIndex}
        filesData={attachments}
        setOpenModalIndex={setOpenModalIndex}
        handleClose={handleAttachClose}
      />
    </div>
  );
}
AttachedDocsChatSection.propTypes = {
  discussion: PropTypes.instanceOf(Map),
  authData: PropTypes.instanceOf(Map),
  preview: PropTypes.bool,
};

const ProgramConfigurationView = ({ programName, conclusion, isOpen, setIsOpen, programId }) => {
  const [params] = useSearchParams();
  const level = params.get('level');
  const courses = conclusion
    .get('formCourseSettingData', List())
    .filter((course) =>
      level === 'institution'
        ? course.get('assignedInstitutionId', '') === programId
        : course.get('programId', '') === programId
    );
  const [filteredCourses, setFilterCourses] = useState(List());
  useEffect(() => {
    setFilterCourses(courses);
  }, []);

  return (
    <ProgramDialog open={isOpen} handleChange={setIsOpen}>
      <ProgramHeader programName={programName} />
      {level === 'course' && <SearchCourse setFilterCourses={setFilterCourses} courses={courses} />}
      {level === 'course' ? (
        <>
          {filteredCourses.size > 0 && <CombinedAccordionCourse data={filteredCourses} />}
          {filteredCourses.size === 0 && (
            <div className="f-14 text-gray text-center">! No Data Found...</div>
          )}
        </>
      ) : level === 'program' ? (
        <>
          {courses.size > 0 && <CombinedAccordionCurriculum data={courses} />}
          {courses.size === 0 && (
            <div className="f-14 text-gray text-center">! No Data Found...</div>
          )}
        </>
      ) : (
        <>
          {courses.size > 0 && <CombinedInstitutionCompound data={courses} />}
          {courses.size === 0 && (
            <div className="f-14 text-gray text-center">! No Data Found...</div>
          )}
        </>
      )}
    </ProgramDialog>
  );
};

const ProgramDialog = ({ children, open, handleChange }) => {
  return (
    <Dialog
      open={open}
      onClose={handleChange}
      fullWidth={true}
      maxWidth={'md'}
      PaperProps={{
        style: {
          minHeight: '500px',
          maxHeight: '80vh',
        },
      }}
    >
      <DialogTitle className="d-flex f-14 justify-content-between align-items-center">
        {children[0]}
        {children[1]}
      </DialogTitle>
      <DialogContent>{children[2]}</DialogContent>
      <DialogActions>
        <Button onClick={handleChange} sx={buttonSx} className="mx-3 my-2">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const ProgramHeader = ({ programName }) => {
  return (
    <div className="d-flex flex-column">
      <div className="f-20 txt-slate-gray">{programName}</div>
      <div className="f-12 grey_font_color">Assign & Occurrence Details :</div>
    </div>
  );
};

const SearchCourse = ({ setFilterCourses, courses }) => {
  const [search, handleChange] = useInputHook('');
  useEffect(() => {
    setFilterCourses(() =>
      courses.filter((course) => {
        const courseName = course.get('courseName', '').toLowerCase();
        const searchQuery = search.toLowerCase();
        return courseName.includes(searchQuery);
      })
    );
  }, [search]);
  return (
    <OutlinedInput
      id="outlined-adornment-weight"
      placeholder={'Search Course Name & Course Id...'}
      startAdornment={
        <InputAdornment position="start">
          <Search sx={{ color: '#9CA3AF', fontSize: '16px' }} />
        </InputAdornment>
      }
      value={search}
      onChange={handleChange}
      aria-describedby="outlined-weight-helper-text"
      inputProps={{
        'aria-label': 'weight',
      }}
      sx={searchInputSx}
    />
  );
};

const CombinedAccordionCourse = ({ data }) => {
  const groupByCourseType = data.groupBy((course) => course.get('courseType', ''));
  const groupByProgram = data.groupBy((course) => {
    const courseName = course.get('courseName', '');
    const _id = course.get('_id', '');
    return `${courseName}+${_id}`;
  });
  return (
    <>
      <LoopCompound loopData={groupByCourseType} variant="courseType">
        <ProgramCoursesAccordion>
          <LoopCompound loopData={groupByProgram} variant="courseConfigure">
            <ProgramCoursesAccordion>
              <ConfigureCardList />
            </ProgramCoursesAccordion>
          </LoopCompound>
        </ProgramCoursesAccordion>
      </LoopCompound>
    </>
  );
};

const CombinedAccordionCurriculum = ({ data }) => {
  const [accordionState, handleChange] = useNestedHook(Map());
  return (
    <>
      {data.map((curriculum, index) => {
        const curriculumName = curriculum.get('curriculumName', '');
        const categoryFormCourseId = curriculum.get('_id', '');
        return (
          <CoursesAccordion
            index={index}
            key={index}
            handleChange={handleChange}
            accordionState={accordionState}
          >
            <CourseTypeDesign type={curriculumName} isCurriculum={true} />
            <CurriculumConfigureList categoryFormCourseId={categoryFormCourseId} />
          </CoursesAccordion>
        );
      })}
    </>
  );
};

const CombinedInstitutionCompound = ({ data }) => {
  return (
    <>
      {data.map((curriculum, index) => {
        const categoryFormCourseId = curriculum.get('_id', '');
        return <InstitutionConfigureList categoryFormCourseId={categoryFormCourseId} key={index} />;
      })}
    </>
  );
};

const CurriculumConfigureList = ({ categoryFormCourseId }) => {
  const courseConfigure = useSelector(selectFormConfigureList);
  const [configureList] = useCallApiHook(getFormConfigureList);
  const getSingleConfigureList = courseConfigure.get(categoryFormCourseId, Map());
  const Institution = courseConfigure.get('institutionType', 'University');
  useEffect(() => {
    if (getSingleConfigureList.size === 0) {
      configureList(categoryFormCourseId);
    }
  }, [categoryFormCourseId]);
  return (
    <>
      <div className="d-flex gap-8 text-capitalize">
        <div className="flex-grow-1">
          <label className="f-12 fw-400 text-mGrey">Institution Type</label>
          <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">{Institution}</div>
        </div>
      </div>
      <Divider className="mt-3 mb-2" />
      <Occurrences configureList={getSingleConfigureList} />
    </>
  );
};

const InstitutionConfigureList = ({ categoryFormCourseId }) => {
  const courseConfigure = useSelector(selectFormConfigureList);
  const [configureList] = useCallApiHook(getFormConfigureList);
  const getSingleConfigureList = courseConfigure.get(categoryFormCourseId, Map());
  const Institution = courseConfigure.get('institutionType', 'University');
  useEffect(() => {
    if (getSingleConfigureList.size === 0) {
      configureList(categoryFormCourseId);
    }
  }, [categoryFormCourseId]);
  return (
    <>
      <div className="d-flex gap-8 text-capitalize">
        <div className="flex-grow-1">
          <label className="f-12 fw-400 text-mGrey">Institution Type</label>
          <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">{Institution}</div>
        </div>
      </div>
      <Divider className="mt-3 mb-2" />
      <Occurrences configureList={getSingleConfigureList} />
    </>
  );
};

const ProgramCoursesAccordion = ({ type, size, data, children, _id, index }) => {
  const condition = typeof size !== 'undefined';
  const [accordionState, handleChange] = useNestedHook(Map());
  const isShow = accordionState.getIn([index, 'isOpen'], false);
  const categoryFormCourseId = accordionState.getIn([index, '_id'], '');
  return (
    <CoursesAccordion
      accordionState={accordionState}
      handleChange={handleChange}
      index={index}
      _id={_id}
    >
      {condition ? (
        <CourseTypeDesign type={type} size={size} />
      ) : (
        <div className="d-flex justify-content-between align-items-center  w-100">
          <div className="f-14 text-capitalize txt-slate-gray fw-500 d-flex align-items-center ">
            {type}
          </div>
          <Switch checked={false} sx={switchSX} />
        </div>
      )}
      <div className="g-config-scrollbar course-scroll">
        {isShow &&
          React.cloneElement(children, {
            categoryFormCourseId,
            renderType: type,
            data,
          })}
      </div>
    </CoursesAccordion>
  );
};
const CourseTypeDesign = ({ size, type, isLoading, isCurriculum = false }) => {
  return (
    <>
      <Avatar
        alt="No-icon"
        className="cursor-pointer"
        variant="rounded"
        style={{
          backgroundColor: '#FEE2E2',
          color: '#991B1B',
        }}
      >
        <div className="f-12">SC</div>
      </Avatar>
      {isCurriculum ? (
        <section className="d-flex align-items-center f-12 ml-2">{type}</section>
      ) : (
        <section className="ml-2 flex-grow-1">
          <div className="d-flex">
            <p className="m-0 f-12 fw-500 text-capitalize">
              {type} <span>courses</span>
            </p>
          </div>
          {size && (
            <p className="m-0 f-12 fw-400 text-lGrey">
              {size} <span>courses</span>
            </p>
          )}
        </section>
      )}
    </>
  );
};
const CoursesAccordion = ({ children, accordionState, handleChange, index, _id }) => {
  const isOpen = accordionState.getIn([index, 'isOpen'], false);
  return (
    <Accordion
      disableGutters
      className={`my-2 box-shadow ${isOpen ? 'box-shadow-static' : ''}`}
      elevation={0}
      variant="outlined"
      onChange={() => handleChange(index, Map({ isOpen: !isOpen, _id }))}
      expanded={isOpen}
    >
      <AccordionSummary expandIcon={<ExpandMoreOutlined />}>{children[0]}</AccordionSummary>
      <AccordionDetails>{children[1]}</AccordionDetails>
    </Accordion>
  );
};
const LoopCompound = ({ loopData, renderType, children, variant }) => {
  const condition = variant === 'courseConfigure';
  return (
    <>
      {loopData.entrySeq().map(([type, data], index) => {
        if (condition && data.getIn([0, 'courseType'], '') !== renderType) {
          return null;
        }
        const _id = type.split('+')[1];
        const name = type.split('+')[0];
        const categoryFormCourseId = condition ? _id : undefined;
        let types = {
          courseType: { type, size: data.size, index },
          courseConfigure: { type: name, data, _id: categoryFormCourseId, index },
        };
        return <div key={index}>{React.cloneElement(children, types[variant])}</div>;
      })}
    </>
  );
};

const ConfigureCardList = ({ categoryFormCourseId, data }) => {
  const courseConfigure = useSelector(selectFormConfigureList);
  const [configureList] = useCallApiHook(getFormConfigureList);
  const getSingleConfigureList = courseConfigure.get(categoryFormCourseId, Map());
  useEffect(() => {
    if (getSingleConfigureList.size === 0) configureList(categoryFormCourseId);
  }, []);
  return (
    <div className="px-3 py-2">
      <CourseSettings configureList={getSingleConfigureList} courseData={data} />
      <Divider className="mt-3 mb-2" />
      <Occurrences configureList={getSingleConfigureList} />
    </div>
  );
};

const CourseSettings = ({ courseData, configureList }) => {
  const studentGroups = configureList.getIn(['formCourseSettingData', 'numberOfGroups'], List());
  const joinStudentGroups = studentGroups.join(',');
  const shared_from_others = courseData.getIn([0, 'shared_from_others'], false);
  const shared_with_others = courseData.getIn([0, 'shared_with_others'], false);
  const courseType = courseData.getIn([0, 'courseType'], '');
  const Institution = courseData.get('institutionType', 'University');
  const isShared = shared_from_others || shared_with_others;
  const sharedCourseType = isShared === true ? 'shared' : '';
  const standardOrSelective = sharedCourseType === '' ? courseType : '';
  return (
    <section>
      <div className="d-flex gap-8 text-capitalize">
        <div className="flex-grow-1">
          <label className="f-12 fw-400 text-mGrey">Institution Type</label>
          <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">{Institution}</div>
        </div>
        <div className="flex-grow-1">
          <label className="f-12 fw-400 text-mGrey">Course Type</label>
          <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
            {`${standardOrSelective}${sharedCourseType}`}
          </div>
        </div>
        <div className="flex-grow-1">
          <label className="f-12 fw-400 text-mGrey">Students Groups *</label>
          <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
            {studentGroups.size ? joinStudentGroups : 'No Group'}
          </div>
        </div>
      </div>
    </section>
  );
};

const Occurrences = ({ configureList }) => {
  const formCourseSettingData = configureList.get('formCourseSettingData', Map());
  const formGroupData = configureList.get('formGroupData', List());
  const tagData = formCourseSettingData.get('tags', List());
  const action = formCourseSettingData.get('actions', Map());
  const groups = formCourseSettingData.get('numberOfGroups', List());
  const academicTerms = action.get('academicTerms', false);
  const studentGroups = action.get('studentGroups', false);
  const attemptType = action.get('attemptType', false);
  const everyAcademic = action.get('everyAcademic', false);
  const occurrenceConfiguration = action.get('occurrenceConfiguration', false);
  const [searchParams] = useSearchParams();
  const level = searchParams.get('level');
  const actions = {
    studentGroups: studentGroups,
    everyAcademic: everyAcademic,
    occurrenceConfiguration: occurrenceConfiguration,
    academicTerms: academicTerms,
    attemptType: attemptType,
  };
  const groupByData = formGroupData.groupBy(
    (data) => `${data.get('term', '')}+${data.get('attemptTypeName')}`
  );
  return (
    <section className="f-14 fw-400 text-dGrey" onClick={(e) => e.stopPropagation()}>
      <div>Occurrences *</div>
      <div className="popup-q360-overflow px-3 py-3">
        {groupByData.entrySeq().map(([_, group]) => {
          return group
            .slice(0, 1)
            .entrySeq()
            .map(([_, attempt], index) => {
              const minimumCount = attempt.get('minimum', 0);
              const startMonth = attempt.get('startMonth', 0);
              const endMonth = attempt.get('endMonth', 0);
              return (
                <div key={index}>
                  <div>
                    <div className="text-capitalize f-14">
                      <ConditionalWrapper condition={actions.academicTerms}>
                        {attempt.get('term', '') !== 'none' ? attempt.get('term', '') : ''} Term
                      </ConditionalWrapper>
                    </div>
                    <div className="ml-3 mt-1 f-14">
                      <ConditionalWrapper condition={actions.attemptType}>
                        <div className="text-capitalize">
                          {attempt.get('attemptTypeName', '') !== 'none'
                            ? attempt.get('attemptTypeName', '')
                            : ''}{' '}
                          Attempt
                        </div>
                      </ConditionalWrapper>
                      <section className="f-14 fw-400 text-dGrey">
                        <ConditionalWrapper condition={actions.everyAcademic}>
                          <div className="d-flex align-items-center mt-2">
                            <div className="mr-2">
                              <Checkbox
                                size="small"
                                className="m-0 p-0"
                                disabled
                                sx={{ '&.Mui-disabled': { color: '#9CA3AF !important' } }}
                                checked={attempt.get('executionsPer', false)}
                              />
                            </div>
                            <div>Executions Per Academic Year </div>
                          </div>
                          <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                            <ConditionalWrapper
                              condition={attempt.get('academicYear', '') === 'every'}
                            >
                              <div className="d-flex align-items-center my-1">
                                <div className="mr-2">
                                  <Radio
                                    checked={attempt.get('academicYear', '') === 'every'}
                                    name="radio-buttons"
                                    inputProps={{ 'aria-label': 'A' }}
                                    size="small"
                                    className="m-0 p-0"
                                    sx={{ '&.Mui-disabled': { color: '#9CA3AF !important' } }}
                                    disabled
                                  />
                                </div>
                                <div>For Every Academic Year</div>
                              </div>
                            </ConditionalWrapper>
                            <ConditionalWrapper
                              condition={attempt.get('academicYear', '') === 'all'}
                            >
                              <div className="d-flex align-items-center my-1">
                                <div className="mr-2">
                                  <Radio
                                    checked={attempt.get('academicYear', '') === 'all'}
                                    name="radio-buttons"
                                    inputProps={{ 'aria-label': 'B' }}
                                    size="small"
                                    className="m-0 p-0"
                                    sx={{ '&.Mui-disabled': { color: '#9CA3AF !important' } }}
                                    disabled
                                  />
                                </div>
                                <div>For All Academic Year</div>
                              </div>
                            </ConditionalWrapper>
                          </div>
                        </ConditionalWrapper>

                        <ConditionalWrapper condition={actions.studentGroups}>
                          <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                            <ConditionalWrapper
                              condition={attempt.get('group', '') === 'all' && groups.size}
                            >
                              <div className="d-flex align-items-center my-1">
                                <div className="mr-2">
                                  <Radio
                                    checked={attempt.get('group', '') === 'all'}
                                    name="radio-buttons"
                                    inputProps={{ 'aria-label': 'A' }}
                                    size="small"
                                    className="m-0 p-0"
                                    sx={{ '&.Mui-disabled': { color: '#9CA3AF !important' } }}
                                    disabled
                                  />
                                </div>
                                <div>All Group</div>
                              </div>
                            </ConditionalWrapper>
                            <ConditionalWrapper
                              condition={attempt.get('group', '') === 'individual' && groups.size}
                            >
                              <div className="d-flex align-items-center my-1 ml-2">
                                <div className="mr-2">
                                  <Radio
                                    checked={attempt.get('group', '') === 'individual'}
                                    value="b"
                                    name="radio-buttons"
                                    inputProps={{ 'aria-label': 'B' }}
                                    size="small"
                                    className="m-0 p-0"
                                    sx={{ '&.Mui-disabled': { color: '#9CA3AF !important' } }}
                                    disabled
                                  />
                                </div>
                                <div>Individual Group</div>
                              </div>
                            </ConditionalWrapper>
                          </div>
                        </ConditionalWrapper>
                        {level === 'institution' || attempt.get('group', '') === 'all' ? (
                          <>
                            <div className="d-flex align-items-center pl-4 pt-1">
                              <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                              <div>Minimum</div>
                              <div className="mx-2">
                                <MaterialInput
                                  value={minimumCount}
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'standard'}
                                  size={'small'}
                                  elementConfig={{ options: numbers }}
                                  disabled={true}
                                  sx={{
                                    paddingBottom: 0,
                                  }}
                                />
                              </div>
                              <div>Times</div>
                            </div>
                            <div className="d-flex align-items-center pl-4 pt-2">
                              <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                              <div>Duration</div>
                            </div>
                            <div className="d-flex align-items-center pl-4 mlt-12 gap-10 py-2 w-30">
                              <div className="flex-grow-1">
                                <div
                                  className="f-14 p-2 pr-5 rounded"
                                  style={{ background: '#F3F4F6' }}
                                >
                                  {numberMonth[startMonth]}
                                </div>
                              </div>
                              <div className="flex-grow-1">
                                <div
                                  className="f-14 p-2 pr-5 rounded"
                                  style={{ background: '#F3F4F6' }}
                                >
                                  {numberMonth[endMonth]}
                                </div>
                              </div>
                            </div>
                          </>
                        ) : (
                          <div className="w-100 d-flex flex-row flex-wrap gap-10">
                            {group.map((groupValue, groupIndex) => {
                              return (
                                <div
                                  className="align-self-start"
                                  style={{ width: '47%' }}
                                  key={groupIndex}
                                >
                                  <Accordion
                                    className="bg-white"
                                    elevation={0}
                                    variant="outlined"
                                    disableGutters
                                    expanded={true}
                                  >
                                    <AccordionSummary
                                      expandIcon={<ExpandMore />}
                                      aria-controls="panel3-content"
                                      id="panel3-header"
                                    >
                                      {groupValue.get('groupName', '')}
                                    </AccordionSummary>
                                    <Divider className="mx-3" />
                                    <AccordionDetails>
                                      <div className="d-flex align-items-center pl-4 pt-1">
                                        <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                                        <div>Minimum</div>
                                        <div className="mx-2">
                                          <MaterialInput
                                            value={groupValue.get('minimum', 0)}
                                            elementType={'materialSelect'}
                                            type={'text'}
                                            variant={'standard'}
                                            size={'small'}
                                            elementConfig={{ options: numbers }}
                                            disabled={true}
                                            sx={{
                                              paddingBottom: 0,
                                            }}
                                          />
                                        </div>
                                        <div>Times</div>
                                      </div>
                                      <div className="d-flex align-items-center pl-4 pt-2">
                                        <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                                        <div>Duration</div>
                                      </div>
                                      <div className="d-flex align-items-center pl-4 mlt-12 gap-10 py-2 w-30">
                                        <div className="flex-grow-1">
                                          <div
                                            className="f-14 p-2 pr-5 rounded"
                                            style={{ background: '#F3F4F6' }}
                                          >
                                            {numberMonth[groupValue.get('startMonth', 0)]}
                                          </div>
                                        </div>
                                        <div className="flex-grow-1">
                                          <div
                                            className="f-14 p-2 pr-5 rounded"
                                            style={{ background: '#F3F4F6' }}
                                          >
                                            {numberMonth[groupValue.get('endMonth', 0)]}
                                          </div>
                                        </div>
                                      </div>
                                    </AccordionDetails>
                                  </Accordion>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </section>
                    </div>
                  </div>

                  <Divider className="mt-2" />
                </div>
              );
            });
        })}

        {tagData.size > 0 && <div className="f-12 fw-400 text-dGrey mt-3 mb-2">Q360 - Tags</div>}
        {tagData.map((item, index) => {
          return (
            <div key={index}>
              <div className="d-flex align-items-center mt-2">
                <div className="mr-2">
                  <Checkbox
                    size="small"
                    className="m-0 p-0"
                    sx={{ '&.Mui-disabled': { color: '#9CA3AF !important' } }}
                    defaultChecked
                    disabled
                  />
                </div>
                <div className="f-14 fw-400 text-dGrey text-capitalize">
                  {item.get('name', '')}{' '}
                  <EnableOrDisable valid={item.get('isDefault', false)}>
                    <span className="color-lt-gray">(Default)</span>
                  </EnableOrDisable>
                </div>
              </div>
              <div className="d-flex">
                {item.get('subTag', List()).map((subTag, subTagIndex) => (
                  <div key={subTagIndex} className="d-flex align-items-center mt-2 ml-3">
                    <Checkbox
                      size="small"
                      className="m-0 p-0"
                      sx={{ '&.Mui-disabled': { color: '#9CA3AF !important' } }}
                      defaultChecked
                      disabled
                    />
                    <div className="f-14 fw-400 text-dGrey ml-2 text-capitalize">{subTag}</div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
};

// const Tags = () => {
//   return;
// };

// const AccordionCourses = ({ AllCourseData }) => {
//   // const classes = useStyles();
//   const numbers = [
//     { name: 'Select', value: 0 },
//     {
//       name: 1,
//       value: 1,
//     },
//     {
//       name: 2,
//       value: 2,
//     },
//     {
//       name: 3,
//       value: 3,
//     },
//     {
//       name: 4,
//       value: 4,
//     },
//   ];
//   return (
//     <>
//       {List([]).map((CourseData, index) => {
//         const FindSharedCourse =
//           CourseData.get('shared_from_others', '') || CourseData.get('shared_with_others', '');
//         const FindSharedCourseType = FindSharedCourse === true ? 'shared' : '';
//         const FindStanderOrSelective =
//           FindSharedCourseType === '' ? CourseData.get('course_type', '') : '';
//         const courseSize = String(AllCourseData.size).padStart(2, '0');
//         return (
//           <div key={index}>
//             <Accordion elevation={1} disableGutters className="mb-3">
//               <AccordionSummary
//                 expandIcon={<ExpandMoreOutlined />}
//                 className="m-0"
//                 sx={AccordionSummaryMargin}
//               >
//                 <Avatar
//                   sx={{ bgcolor: '#FEE2E2', color: '#991B1B' }}
//                   variant="rounded"
//                   className="mr-2"
//                 >
//                   <div className="f-12">SC</div>
//                 </Avatar>
//                 <div>
//                   <div className="text-capitalize f-15">{`${FindStanderOrSelective}${FindSharedCourseType} Courses`}</div>
//                   <div className="f-12">{`${courseSize} Courses`}</div>
//                 </div>
//               </AccordionSummary>
//               <Accordion disableGutters className="mx-3 my-2" elevation={0} variant="outlined">
//                 <AccordionSummary expandIcon={<ExpandMoreOutlined />} sx={AccordionSummaryMargin}>
//                   <div className="d-flex align-items-center w-100">
//                     <div className="text-capitalize f-15">{CourseData.get('course_name', '')}</div>
//                     <div className="f-12 ml-auto">
//                       <FormControlLabel
//                         className="m-0 pr-3"
//                         control={
//                           <Switch
//                             sx={switchGrayColor}
//                             size="small"
//                             checked={CourseData.get('isEnable', false)}
//                           />
//                         }
//                         // classes={{ label: classes.label }}
//                         label={CourseData.get('isEnable', false) ? 'ON' : 'OFF'}
//                       />
//                     </div>
//                   </div>
//                 </AccordionSummary>
//                 <Divider className="mx-3" />
//                 <div className="px-3 py-2">
//                   <section>
//                     <div className="d-flex gap-8 text-capitalize">
//                       <div className="flex-grow-1">
//                         <label className="f-12 fw-400 text-mGrey">Institution Type</label>
//                         <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
//                           {CourseData.get('institution_type', '')}
//                         </div>
//                       </div>
//                       <div className="flex-grow-1">
//                         <label className="f-12 fw-400 text-mGrey">Course Type</label>
//                         <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
//                           {`${FindStanderOrSelective}${FindSharedCourseType}`}
//                         </div>
//                       </div>
//                       <div className="flex-grow-1">
//                         <label className="f-12 fw-400 text-mGrey">Students Groups *</label>
//                         <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
//                           {CourseData.getIn(['occurrences', 'studentGroups'], List()).size
//                             ? CourseData.getIn(['occurrences', 'studentGroups'], List()).join(',')
//                             : 'Selective'}
//                         </div>
//                       </div>
//                     </div>
//                   </section>
//                   <Divider className="mt-3 mb-2" />
//                   <section className="f-14 fw-400 text-dGrey" onClick={(e) => e.stopPropagation()}>
//                     <div>Occurrences *</div>
//                     {CourseData.getIn(['occurrences', 'attemptType'], List()).map(
//                       (attempt, attemptKey) => (
//                         <Fragment key={attemptKey}>
//                           {attempt.get('typeName', '') !== 'none'
//                             ? attempt.get('typeName', '')
//                             : ''}
//                           <div className="d-flex align-items-center mt-2">
//                             <div className="mr-2">
//                               <Checkbox
//                                 size="small"
//                                 checked={attempt.get('executionsPer', false)}
//                                 disabled
//                                 sx={{
//                                   color: '#DADADA !important',
//                                   '&.Mui-checked': {
//                                     color: '#DADADA !important',
//                                   },
//                                 }}
//                                 className="m-0 p-0"
//                               />
//                             </div>
//                             <div>Executions Per Academic Year </div>
//                           </div>
//                           <div className="d-flex align-items-center pl-4 ml-1 mt-1">
//                             <div className="d-flex align-items-center my-1">
//                               <div className="mr-2">
//                                 <Radio
//                                   checked={attempt.get('academicYear', '') === 'every'}
//                                   color="default"
//                                   disabled
//                                   value="all"
//                                   name="radio-buttons"
//                                   inputProps={{
//                                     'aria-label': 'A',
//                                   }}
//                                   size="small"
//                                   className="m-0 p-0"
//                                 />
//                               </div>
//                               <div>For Every Academic Year</div>
//                             </div>
//                             <div className="d-flex align-items-center my-1 ml-2">
//                               <div className="mr-2">
//                                 <Radio
//                                   checked={attempt.get('academicYear', '') === 'all'}
//                                   color="default"
//                                   disabled
//                                   value="every"
//                                   name="radio-buttons"
//                                   inputProps={{
//                                     'aria-label': 'B',
//                                   }}
//                                   size="small"
//                                   className="m-0 p-0"
//                                 />
//                               </div>
//                               <div>For All Academic Year</div>
//                             </div>
//                           </div>
//                           <div className="d-flex align-items-center pl-4 ml-1 mt-1">
//                             <div className="d-flex align-items-center my-1">
//                               <div className="mr-2">
//                                 <Radio
//                                   checked={attempt.get('group', '') === 'all'}
//                                   color="default"
//                                   disabled
//                                   value="all"
//                                   name="radio-buttons"
//                                   inputProps={{
//                                     'aria-label': 'A',
//                                   }}
//                                   size="small"
//                                   className="m-0 p-0"
//                                 />
//                               </div>
//                               <div>All Group</div>
//                             </div>
//                             <div className="d-flex align-items-center my-1 ml-2">
//                               <div className="mr-2">
//                                 <Radio
//                                   checked={attempt.get('group', '') === 'individual'}
//                                   color="default"
//                                   disabled
//                                   value="individual"
//                                   name="radio-buttons"
//                                   inputProps={{
//                                     'aria-label': 'B',
//                                   }}
//                                   size="small"
//                                   className="m-0 p-0"
//                                 />
//                               </div>
//                               <div>Individual Group</div>
//                             </div>
//                           </div>

//                           {attempt.get('group', '') === 'all' ? (
//                             attempt.get('groupType', List()).map((group, i) => (
//                               <Fragment key={index}>
//                                 <div className="d-flex align-items-center pl-4 pt-1">
//                                   <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
//                                   <div>Minimum</div>
//                                   <div className="mx-2">
//                                     <MaterialInput
//                                       elementType={'materialSelect'}
//                                       type={'text'}
//                                       variant={'standard'}
//                                       value={group.get('minimum', 0)}
//                                       size={'small'}
//                                       elementConfig={{
//                                         options: numbers,
//                                       }}
//                                       sx={{
//                                         paddingBottom: 0,
//                                       }}
//                                   </div>
//                                   <div>Times</div>
//                                 </div>
//                                 <div className="d-flex align-items-center pl-4 pt-2">
//                                   <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
//                                   <div>Duration</div>
//                                 </div>
//                                 <div className="d-flex align-items-center pl-4 mlt-12 gap-10 py-2 w-30">
//                                   <div className="flex-grow-1">
//                                     <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
//                                       {group.get('startMonth', '')}
//                                     </div>
//                                   </div>
//                                   <div className="flex-grow-1">
//                                     <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
//                                       {group.get('endMonth', '')}
//                                     </div>
//                                   </div>
//                                 </div>
//                               </Fragment>
//                             ))
//                           ) : (
//                             <div className="d-flex pl-4 mlt-12 gap-10" style={{ flexWrap: 'wrap' }}>
//                               {attempt.get('groupType', List()).map((groupValue, groupIndex) => {
//                                 return (
//                                   <div
//                                     className="align-self-start flex-grow-1"
//                                     style={{
//                                       flexBasis: '200px',
//                                     }}
//                                     key={groupIndex}
//                                   >
//                                     <Accordion
//                                       className="bg-white"
//                                       elevation={0}
//                                       variant="outlined"
//                                       disableGutters
//                                     >
//                                       <AccordionSummary
//                                         expandIcon={<ExpandMoreOutlined />}
//                                         aria-controls="panel3-content"
//                                         id="panel3-header"
//                                       >
//                                         {groupValue.get('name', '')}
//                                       </AccordionSummary>
//                                       <Divider className="mx-3" />
//                                       <div>
//                                         <div className="d-flex align-items-center gap-5">
//                                           <div>Minimum</div>
//                                           <div>
//                                             <MaterialInput
//                                               elementType={'materialSelect'}
//                                               type={'text'}
//                                               variant={'standard'}
//                                               size={'small'}
//                                               elementConfig={{
//                                                 options: numbers,
//                                               }}
//                                               value={groupValue.get('minimum', 0)}
//                                             />
//                                           </div>
//                                           <div>Times</div>
//                                         </div>
//                                         <div className="d-flex align-items-center ">
//                                           <div>Duration</div>
//                                         </div>
//                                         <div className="d-flex align-items-center gap-10 py-2">
//                                           <div className="flex-grow-1">
//                                             <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
//                                               {groupValue.get('startMonth', '')}
//                                             </div>
//                                           </div>
//                                           <div className="flex-grow-1">
//                                             <div className="border bg-grey rounded f-14 fw-500 p-2 text-lGrey">
//                                               {groupValue.get('endMonth', '')}
//                                             </div>
//                                           </div>
//                                         </div>
//                                       </div>
//                                     </Accordion>
//                                   </div>
//                                 );
//                               })}
//                             </div>
//                           )}
//                         </Fragment>
//                       )
//                     )}
//                   </section>
//                 </div>
//               </Accordion>
//             </Accordion>
//           </div>
//         );
//       })}
//     </>
//   );
// };
