import React from 'react';
import PropTypes from 'prop-types';
import { DialogContent, DialogActions, Stack } from '@mui/material';
import { connect } from 'react-redux';
import { Map, List, fromJS } from 'immutable';

// utils
import { useStylesFunction } from '../../designUtils';
import MButton from 'Widgets/FormElements/material/Button';
import { BootstrapDialog, BootstrapDialogTitle } from '../../utils';

// components
import LeavePolicyDetails from '../LeavePolicyDetails';
import useFileUpload from 'Hooks/useFileUpload';

// redux
import * as actions from '_reduxapi/leave_management/actions';

const AddDocumentPopup = ({
  handlePopup,
  isOpen,
  typeAction,
  uploadAttachment,
  setPolicy,
  policy,
  handleDocChange,
  handleAddDocDetails,
  setData,
}) => {
  const { docModalWidth, permissionTypePopup } = useStylesFunction();
  const { upload } = useFileUpload();

  // const handleChange = (event) => {
  //   let file = event.target.files[0];
  //   const temp = ['image/png', 'image/jpeg', 'application/pdf'];
  //   if (!temp.includes(file.type))
  //     return setData({ message: 'You can only upload png, jpeg and pdf files' });

  //   const formData = new FormData();
  //   formData.append(`file`, file);
  //   const callBack = (data) => {
  //     // setPolicy(policy.setIn(['attachmentDocument', 0], data));
  //     setPolicy(
  //       policy.set('attachmentDocument', policy.get('attachmentDocument', List()).push(data))
  //     );
  //   };
  //   uploadAttachment(formData, callBack);
  // };

  const handleChange = async (event) => {
    try {
      let file = event.target.files[0];
      const temp = ['image/png', 'image/jpeg', 'application/pdf'];
      if (!temp.includes(file.type)) {
        return setData({ message: 'You can only upload png, jpeg and pdf files' });
      }
      setData({ isLoading: true });
      const uploadedFilesData = await upload({
        files: [file],
        component: 'userData',
        folderPathExtension: 'digiclass',
      });
      const formattedFiles = uploadedFilesData.map(({ signedUrl, unSignedUrl, ...fileData }) => {
        return {
          ...fileData,
          url: unSignedUrl,
          signedUrl,
        };
      });
      setPolicy(
        policy.set(
          'attachmentDocument',
          policy.get('attachmentDocument', List()).push(fromJS(formattedFiles[0]))
        )
      );

      setData({ isLoading: false });
    } catch (err) {
      setData({ isLoading: false, message: err.message });
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    handleChange({ target: e.dataTransfer });
  };
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  return (
    <div>
      <BootstrapDialog
        className={`${docModalWidth} ${permissionTypePopup}`}
        aria-labelledby="customized-dialog-title"
        open={isOpen}
      >
        <BootstrapDialogTitle onClose={(e) => handlePopup(e, false)} id="customized-dialog-title">
          {typeAction} Document
        </BootstrapDialogTitle>

        <DialogContent dividers>
          <LeavePolicyDetails
            handleChange={handleChange}
            handleDrop={handleDrop}
            handleDrag={handleDrag}
            policy={policy}
            handleDocChange={handleDocChange}
            disabled={false}
            setPolicy={setPolicy}
          />
        </DialogContent>

        <DialogActions>
          <Stack spacing={2} direction="row">
            <MButton variant="text" color={'blue'} clicked={(e) => handlePopup(e, false)}>
              CANCEL
            </MButton>
            <MButton
              className="text-uppercase"
              variant="contained"
              color={'blue'}
              clicked={() => handleAddDocDetails()}
            >
              {typeAction === 'Add' ? typeAction : 'Save'}
            </MButton>
          </Stack>
        </DialogActions>
      </BootstrapDialog>
    </div>
  );
};

AddDocumentPopup.propTypes = {
  handlePopup: PropTypes.func,
  isOpen: PropTypes.bool,
  typeAction: PropTypes.string,
  policy: PropTypes.instanceOf(Map),
  handleDocChange: PropTypes.func,
  handleAddDocDetails: PropTypes.func,
  uploadAttachment: PropTypes.func,
  upload: PropTypes.func,
  setPolicy: PropTypes.func,
  setData: PropTypes.func,
};

export default connect(null, actions)(AddDocumentPopup);
