/* @font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Roboto'), local('Roboto-Regular'),
    url('Assets/fonts/Roboto/Roboto-Regular.ttf') format('truetype');
} */

/* @font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  src: local('Roboto Medium'), local('Roboto-Medium'),
    url('./Assets/fonts/Roboto/Roboto-Medium.ttf') format('truetype');
} */

/* @font-face {
  font-family: "FONTSFREE-NET-SFPROTEXT-BOLD";
  src: url("./Assets/fonts/FONTSFREE-NET-SFPROTEXT-BOLD.TTF") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "FONTSFREE-NET-SFPROTEXT-HEAVY-1";
  src: url("./Assets/fonts/FONTSFREE-NET-SFPROTEXT-HEAVY-1.TTF")
    format("truetype");
  font-weight: bold;
  font-style: bold;
}

@font-face {
  font-family: "FONTSFREE-NET-SFPROTEXT-REGULAR";
  src: url("./Assets/fonts/FONTSFREE-NET-SFPROTEXT-REGULAR.TTF")
    format("truetype");
  font-weight: bold;
  font-style: bold;
}

@font-face {
  font-family: "FONTSFREE-NET-SFPROTEXT-SEMIBOLD";
  src: url("./Assets/fonts/FONTSFREE-NET-SFPROTEXT-SEMIBOLD.TTF")
    format("truetype");
  font-weight: bold;
  font-style: bold;
}

@font-face {
  font-family: "SF-PRO-TEXT-MEDIUM";
  src: url("./Assets/fonts/SF-PRO-TEXT-MEDIUM.TTF") format("truetype");
  font-weight: bold;
  font-style: bold;
} */

body {
  -webkit-text-size-adjust: 100%;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif;
  color: #3d5170;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.slide-in-left {
  -webkit-animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-in-left 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes slide-in-left {
  0% {
    -webkit-transform: translateX(-1000px);
    transform: translateX(-1000px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    -webkit-transform: translateX(-1000px);
    transform: translateX(-1000px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-right {
  -webkit-animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}

/* .sidenav {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 2;
  top: 0;
  left: 0 !important;
  background-color: #fff;
  transition: 0.5s;
  box-shadow: 7px 0 5px -2px #88888830;
} 
 */

*:focus {
  outline: 0;
}

.sidenav h6 {
  padding: 14px 8px 14px 32px;
  text-decoration: none;
  font-size: 14px;
  color: #000;
  display: block;
  transition: 0.3s;
  border-bottom: 1px solid #dac2c2;
}

.sidenav h6 div {
  font-size: 10px;
}

.sidenav i {
  display: inline-block;
  padding-left: 10px;
  padding-right: 15px;
  vertical-align: middle;
  float: right;
  color: #928f8f;
}

.sidenav a {
  padding: 8px 8px 18px 32px;
  text-decoration: none;
  font-size: 14px;
  color: #818181;
  display: block;
  transition: 0.3s;
  font-weight: 500;
  display: block;
  align-items: center;
}

.sidenav a:hover,
.offcanvas a:focus {
  color: #0047cc;
}

.closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px !important;
  margin-left: 50px;
}

#main {
  transition: margin-left 0.5s;
  padding: 16px;
}

@media screen and (max-height: 450px) {
  /* .sidenav {padding-top: 15px;} */
  /* .sidenav a {font-size: 18px;} */
}

.sidenav_icon {
  width: 15px;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .container {
    max-width: 1300px;
  }
}

.pl_30 {
  padding-left: 30px;
}

.pt_5 {
  padding-top: 5px;
}

.pd_4 {
  padding-top: 4px;
}

.ham_nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: block;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.ham_nav2 {
  float: right;
}

.wd_120 {
  width: 120px;
}

.w-12 {
  width: 12%;
}

.wd_0 {
  width: 0px;
}

.ml_250 {
  margin-left: 250px;
  transition: 0.5s;
}

.ml_0 {
  margin-left: 0px;
  transition: 0.5s;
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .wd_0 {
    width: 0px;
    white-space: nowrap;
  }

  .ml_0 {
    margin-left: 0px;
    transition: 0.5s;
  }

  .m-ham {
    float: right;
  }

  .ml_250 {
    margin-left: 0px;
    transition: 0.5s;
  }

  .wd_250 {
    width: 250px;
    white-space: nowrap;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .wd_250 {
    width: 250px;
    white-space: nowrap;
  }

  .wd_0 {
    width: 0px;
    white-space: nowrap;
  }
}

/* @media only screen and (min-width: 768px) {
 
  
}Medium devices (landscape tablets, 768px and up) */

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .wd_250 {
    width: 210px;
  }

  .wd_0 {
    width: 0px;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .wd_250 {
    width: 250px;
  }

  .wd_0 {
    width: 0px;
  }
}

/*header start */

.user-button {
  color: #8a8a8a;
  background-color: transparent;
  border-color: transparent;
}

.user-button:hover {
  color: #77797b;
  background-color: #e2e6ea36;
  border-color: #dae0e51c;
}

.show.user-button.dropdown-toggle {
  color: #212529;
  background-color: #dae0e53b;
  border-color: #dae0e53b;
}

.usericon {
  background: #d3d3d3;
  padding: 2px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
}

.user-name {
  padding-left: 5px;
  color: #8a8a8a;
}

.headerbar {
  padding: 12px 20px 17px 20px;
  background-image: linear-gradient(
    84.06deg,
    rgba(26, 123, 220, 0.85) 13%,
    rgba(86, 184, 255, 0.85) 107.28%
  );
}

.headerbar-back {
  padding: 12px 20px 20px 20px;
  /* background: linear-gradient(84.06deg, rgba(26, 123, 220, 0.85) 13%, rgb(106 189 252) 107.28%); */
  background-image: linear-gradient(
    84.06deg,
    rgba(26, 123, 220, 0.85) 13%,
    rgba(86, 184, 255, 0.85) 107.28%
  );
}

.side-layer {
  border-left: 1px solid #d4cece9c;
  margin-top: -10px !important;
  margin-bottom: -6px !important;
  padding-right: 20px;
}

/*header end */

/* main start */

.main {
  /* background: #f5f6f8; */
  min-height: 700px;
  padding-bottom: 75px;
}

.header-bottom {
  text-align: left;
  padding-top: 20px;
}

.header-bottom span {
  font-size: 13px;
  color: #818ea3;
  letter-spacing: 2px;
  text-transform: uppercase;
  font-weight: 500;
}

.header-bottom h3 {
  font-size: 30px;
}

.dash-inner {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 17px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}

.dash-inner span {
  font-size: 13px;
}

.dash-inner h3 {
  font-size: 30px;
  margin-bottom: 0px;
}

.dash-inner h3 b {
  font-size: 18px;
  margin-bottom: 0px;
}

.show.btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e536;
  border-color: #d3d9df14;
}

.btn.focus,
.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0rem rgba(87, 89, 90, 0.15);
}

.center-dropdown {
  padding: 10px 10px 0px 0px;
  background: transparent;
}

.status {
  font-size: 14px;
  border-radius: 7px;
  background-color: #a9b9c66e;
  color: #878a8e;
  padding: 7px;
  font-weight: bold;
}

.dash-content h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 0px;
  color: #797b7a;
}

.outter {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 35px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}

.outter-profile {
  background-color: #fff;
  text-align: left;
  padding: 17px 15px 17px 15px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}

.bar1,
.bar2 {
  width: 18px;
  height: 2px;
  background-color: #8c8585c4;
  margin: 5px 0px;
}

hr.line-border {
  margin-left: -35px;
  margin-right: -35px;
}

.img-manage {
  background: #d3d3d3;
  padding: 2px;
  border-radius: 50%;
  width: 40px;
  /* height: 30px; */
}

.manage {
  font-size: 14px;
  color: #939596;
}

.managetext h3 {
  font-size: 12px;
}

.managetext b {
  font-size: 12px;
}

.managetext span {
  font-size: 12px;
  color: #818288;
}

.managebutton {
  border: 1px solid #e4e2e2;
  border-radius: 3px;
  background-color: #ffffff;
  color: #77797b;
}

.rightBorder {
  border-right: 2px solid #becad6;
  background-color: white;
}

.si-ar {
  border-left: 2px solid #becad6;
  background-color: white;
}

.btn-group-sm > .btn,
.btn-sm {
  padding: 1px 9px 2px 9px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: 0.2rem;
}

button#bg-nested-dropdown {
  background: #ffffff;
  color: #212529;
  border: transparent;
  box-shadow: 0 0 0 0rem rgba(87, 89, 90, 0.15);
  outline: 0;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif !important;
}

button#bg-nested-dropdown.dropdown-toggle::after {
  border-bottom: 0;
  border-left: 0em solid transparent;
  margin-left: 1px;
  margin-right: 2px;
  border: none;
}

.ml-100px {
  margin-left: 100px;
}

#dropdown-item-button {
  background: #f8f9fa;
  color: black;
  border: transparent;
  box-shadow: 0 0 0 0rem rgba(87, 89, 90, 0.15);
  outline: 0;
  font-size: 12px;
}

#dropdown-item-button::after {
  margin-left: 1.255em;
}

.footer-sort {
  padding-bottom: 0.8cm;
}

.managebutton::before {
  padding-left: 9px;
  font-size: 12px;
}

.green {
  color: #3bc34e;
}

.red {
  color: #e66363;
}

.full-view {
  font-size: 12px;
  color: #323c47;
}

.img-activity {
  padding-top: 8px;
}

.tables {
  padding-top: 1cm;
}

.tables h3 {
  font-size: 24px;
  padding-bottom: 20px;
  padding-top: 20px;
  color: #797b7a;
}

.dash-table {
  background: white;
}

td.tr-change {
  /* padding: 18px 0px 18px 0px; */
  border-top: 6px solid #f4f5f5;
  color: #5a6169;
  font-size: 13px;
}

thead.th-change {
  background: #f4f5f5;
}

/* tbody span {
  color: #868e96;
} */

.table th {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e629;
  font-size: 14px;
  color: #a9b9c6;
}

#dropdown-table.dropdown-toggle::after {
  border-right: 0em solid transparent;
  border-bottom: 0;
  border-left: 0em solid transparent;
}

.table-dropdown {
  margin-top: -4px;
  background: transparent;
  margin-bottom: -20px;
  margin-right: -10px;
}

tr.tr-change:hover {
  /* cursor: pointer;
    box-shadow: 3px 1px 15px 4px rgba(0, 0, 0, 0.42); */
}

.vu {
  padding: 3px 11px 1px 0px;
}

.page-link {
  position: relative;
  display: block;
  padding: 5px 11px;
  margin-left: -1px;
  line-height: 1.25;
  color: #8e98a1;
  border-radius: 50%;
  border: 1px solid #dee2e600;
  background-color: #fff0;
}

.page-item.active .page-link {
  background-color: #0047cc;
  border-color: #0047cc;
}

/* main start */

/* modal container start */

.modal-top {
  color: #9ba5b6;
}

.calender {
  position: absolute;
  padding: 19px 13px 17px 16px;
  color: #c4c6cb;
  z-index: 99;
}

.icon-form {
  padding: 15px 13px 15px 42px;
}

.icon-form-calender {
  padding: 17px 10px 14px 42px;
}

/* radio design 1 start*/

.form-radio {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  display: inline-block;
  position: relative;
  background-color: #ffffff;
  color: #fff;
  top: 10px;
  height: 27px;
  width: 27px;
  border: 1px solid #3d5170;
  border-radius: 5px;
  cursor: pointer;
  margin-right: 7px;
  outline: none;
}

.form-radio:checked::before {
  position: absolute;
  font: 15px/0.8 'Open Sans', sans-serif;
  left: 5px;
  top: 7px;
  content: '\f00c';
  font-weight: bold;
  font-family: FontAwesome;
  transform: rotate(7deg);
}

.form-radio:hover {
  background-color: #d5cacb8c;
}

.form-radio:checked {
  background-color: #3e95ef;
}

.radio-label {
  padding-right: 29px;
  padding-left: 13px;
  padding-top: 13px;
}

/* radio design 1 end*/

/* radio design 2 start*/

.form-radio1 {
  /* -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  position: relative; */

  display: inline-block;
  color: #fff;
  /* top: 4px;
  height: 18px;
  width: 18px; */
  border: 1px solid #0047cc;
  border-radius: 9px;
  cursor: pointer;
  outline: none;

  width: 1em;
  height: 1em;
  top: 3px;
  position: relative;
}

.form-radio1:checked::before {
  /* position: absolute;
  font: 14px/0.8 'Open Sans', sans-serif;
  left: 2px;
  top: 3px;
  content: '\f111';
  font-weight: bold;
  font-family: FontAwesome; */
  color: #0047cc;
  /* transform: rotate(7deg); */
}

.form-radio1:hover {
  background-color: #d5cacb8c;
}

.form-radio1:checked {
  background-color: #ffffff;
}

.radio-label1 {
  padding: 0px 10px 0px 6px;
  margin-bottom: 0px;
}

/* radio design 2 end*/

button.save {
  padding: 15px 58px 17px 58px;
  font-size: 17px;
  border-radius: 17px;
}

.date-pickers {
  border: 1px solid #ccc6c6;
  width: 130%;
  position: absolute;
  padding: 20px;
  background: #fff;
}

/* .modal-header{
font-size: 22px;
padding: 0px;
border-bottom: 1px solid #e9ecef00;

} */

/* .modal-header::before {
  content: "\f066";
  font-family: FontAwesome;
 
}

.modal-header::after {
  content: "\f00d";
  font-family: FontAwesome;
  cursor: pointer;
 } */

.calender-icons::before {
  content: '\f053';
  font-family: FontAwesome;
  padding-right: 52px;
}

.calender-icons::after {
  content: '\f054';
  font-family: FontAwesome;
  padding-left: 52px;
}

.modal-dialog {
  /* max-width: 60%;
  margin: 2cm 1cm 1cm 9cm; */
}

.modal-backdrop {
  background-color: #f5efef;
}

.modal-content {
  border: 1px solid rgba(0, 0, 0, 0.09);
  box-shadow: -2px 4px 25px 2px rgba(0, 0, 0, 0.45);
}

/* modal container end */

/* input type 2 start */

.input2 {
  border-bottom: 1px solid #ced4da;
  display: block;
  width: 100%;
  padding: 0.375rem 0rem;
  font-size: 20px;
  line-height: 1.5;
  color: #929394;
  background-color: #fff;
  background-clip: padding-box;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  border-left: 1px solid #fff;
  border-top: 1px solid #fff;
  border-right: 1px solid #fff;
  margin-bottom: 24px;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
}

.input2:focus {
  border-bottom: 1px solid #c3c6ca;
}

.modal-form {
  padding: 9px 13px 9px 9px;
}

.modal-position {
  padding-right: 12px;
  padding-left: 5px;
}

.time-label {
  font-size: 15px;
  padding-left: 5px;
}

.model-box {
  padding: 10px !important;
  border: 1px solid #ced4da;
}

.model-box1 {
  border: 1px solid #ced4da;
  height: calc(46px + 2px) !important;
}

.save-model {
  text-align: center !important;
  padding-top: 20px;
}

/* input type 2 end */
.SF-PRO-TEXT-MEDIUM {
  font-family: 'Roboto', 'Helvetica Neue', sans-serif !important;
  font-weight: bold;
  font-style: bold;
}

.nav-tabs {
  border-bottom: 3px solid rgba(86, 184, 255, 0.85);
  background-image: linear-gradient(84.06deg, rgb(61 143 226) 13%, rgb(107 190 253) 107.28%);
}

.breadcrumb-icon:after {
  content: ' > ';
  font-size: 18px;
}

.breadcrumb-icon:last-child:after {
  content: none;
}

.breadcrumb-icon:hover {
  text-decoration: none !important;
}

.breadcrumb-icon:last-child,
.digi-pointer-events-none {
  pointer-events: none;

  /* for "disabled" effect */
  opacity: 1;
}

.breadcrumb-icon {
  font-weight: 500;
  font-size: 24px;
  line-height: 24px;
  letter-spacing: 0.0125em;
}

.headerbar_breadcrumb {
  margin-top: -78px;
  margin-left: 48px;
  background: #fff0 !important;
  color: #ffffff;
  padding-left: 1em;
  font-weight: 500;
  font-size: 24px;
  line-height: 30px;
  letter-spacing: 0.0125em;
}

/* .headerbar_breadcrumb{
      padding: 10px 20px 10px 20px !important;
     } */

.nav-tabs .nav-item {
  padding: 17px 18px !important;
  font-size: 18px;
  font-weight: 500;
}

.agree-button {
  border-radius: 30px;
  padding: 1px 28px 1px 28px;
}

ul.demo {
  list-style-type: none;
  margin: 0;
  padding: 0;
  margin-top: 20px;
}

.demo li {
  margin-bottom: 10px;
  font-size: 14px;
}

.customize_tab {
  padding: 1px 10px 1px 0px;
  background-image: linear-gradient(84.06deg, rgb(60 143 225) 13%, rgb(106 190 252) 100.28%);
}

.tabactive {
  border-bottom: 3px solid #ffffff;
  padding-right: 4px;
  margin-bottom: 2px;
}

.tabaligment {
  color: #fff !important;
  text-align: center;
  padding: 14px 30px;
  text-decoration: none;
  font-size: 17px;
  font-weight: 500;
}

.tabaligment:hover {
  color: #fff;
  text-decoration: none;
  /* border-bottom: 3px solid #ffffff; */
}

ul#menu li {
  display: inline;
}

.rtl-text {
  direction: rtl;
}

.logo {
  margin-top: -17px;
  float: right;
  border-radius: 8px;
}

.sideNavActive {
  color: #0047cc !important;
}

.btn-custom {
  border: none;
  background: none;
  color: red;
  font-size: 30px !important;
  cursor: pointer !important;
  padding: 6px;
  margin-top: -6px;
}

.btn-custom:active {
  background: none !important;
  border: none !important;
  color: red !important;
}

.nav-bar-title {
  color: #ffffff;
  padding-left: 1em;
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.0125em;
}

#sync,
#manual {
  margin-top: -7px;
  margin-right: 7px;
}

.interim-credit-hours {
  position: absolute;
  margin-left: 16em;
  margin-top: 28px;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  opacity: 0.6;
}

.interim-credit-hours-ar {
  position: absolute;
  margin-right: 16em;
  margin-top: 28px;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.25px;
  opacity: 0.6;
}

.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  text-align: center;
  background-color: #f7f5f5;
  z-index: 1;
}

.export-table-height {
  max-height: 250px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* width */
.export-table-height::-webkit-scrollbar {
  width: 10px;
}

/* Handle */
.export-table-height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.export-table-height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.strightView {
  /* transform: rotate(270deg);
    position: relative;
    word-break: break-word;
    text-align: center;
    width: 100%;
    height: 100%; */
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 99%;
}

.rotateView {
  /* position: absolute;
  left: 15%;
  top: 50%;
  transform: rotate(270deg) translate(50%, 0%); */

  position: absolute;
  left: 50%;
  top: 65%;
  transform: rotate(270deg) translate(25%, -260%);
  width: 500px;
}

/* width */
.go-wrapper::-webkit-scrollbar {
  height: 10px;
  width: 10px;
}

/* Handle */
.go-wrapper::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.go-wrapper::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.go-wrapper-height::-webkit-scrollbar {
  width: 7px;
}

/* Handle */
.go-wrapper-height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.go-wrapper-height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.go-wrapper-width::-webkit-scrollbar {
  height: 7px;
}

/* Handle */
.go-wrapper-width::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.go-wrapper-width::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.go-wrapper-height {
  overflow-y: auto;
  /* max-height: 250px; */
  max-height: 340px;
}

.go-wrapper {
  overflow-x: auto;
  width: 100%;
}

.go-wrapper table {
  width: 100%;
}

.go-wrapper table tbody {
  display: block;
  min-height: 150px;
  overflow-x: hidden;
}

.go-wrapper table thead {
  display: table;
}

.go-wrapper table tfoot {
  display: table;
}

.go-wrapper table thead tr,
.go-wrapper table tbody tr,
.go-wrapper table tfoot tr {
  display: table-row;
}

/* .go-wrapper table th,
.go-wrapper table td { 
  white-space: nowrap; 
} */

.go-wrapper .aw-50 {
  min-height: 1px;
  width: 50px;
  word-wrap: break-word;
}

.go-wrapper .aw-75 {
  min-height: 1px;
  width: 75px;
  word-wrap: break-word;
}

.go-wrapper .aw-100 {
  min-height: 1px;
  width: 100px;
  word-wrap: break-word;
}

.go-wrapper .aw-125 {
  min-height: 1px;
  width: 125px;
  word-wrap: break-word;
}

.go-wrapper .aw-150 {
  min-height: 1px;
  width: 150px;
  word-wrap: break-word;
}

.go-wrapper .aw-200 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}

.go-wrapper .aw-300 {
  min-height: 1px;
  width: 300px;
  word-wrap: break-word;
}

.go-wrapper .aw-400 {
  min-height: 1px;
  width: 400px;
  word-wrap: break-word;
}

.tb-50 {
  min-height: 1px;
  width: 50px;
}

.tb-100 {
  min-height: 1px;
  width: 100px;
}

.tb-150 {
  min-height: 1px;
  width: 150px;
}

.tb-200 {
  min-height: 1px;
  width: 200px;
}

.tb-250 {
  min-height: 1px;
  width: 250px;
}

.styled-select {
  width: 100%;
  text-align-last: left !important;
  border-radius: 0px;
  padding-inline-start: 5px !important;
  -moz-padding-end: 30px !important;
}

.styled-select-auto {
  width: auto;
  text-align-last: left !important;
  border-radius: 0px;
  padding-inline-start: 5px !important;
  -webkit-padding-end: 30px !important;
}

.custom-date-picker {
  border: none;
  margin: 5px;
  display: inline-block;
  font-size: 15px;
  border-bottom: 1px solid;
  background: none;
  width: 100%;
}

.calendarIcon {
  position: absolute;
  padding: 8px 0px 0px 0px;
  z-index: 1;
  right: 0;
}

.calendarIconAr {
  position: absolute;
  padding: 8px 0px 0px 0px;
  z-index: 1;
  left: 0;
}

.readonly-datepicker {
  border: none;
  margin: 4px;
}

.disabled-icon {
  cursor: not-allowed;
}

i.fas.fa-plus {
  padding-right: 5px;
}

.set-versions {
  font-size: 20px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.87);
}

.black-normal {
  font-size: 18px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.87);
  opacity: 0.87;
}

.fond-bold-normal,
b,
strong,
.font-weight-bold {
  font-weight: 500 !important;
}

.table thead th {
  color: black;
  font-weight: 500;
}

.badge {
  text-transform: capitalize;
}

.font-opacity {
  color: black;
  opacity: 0.5;
}

.btn-primary {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  background: #3e95ef;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  text-transform: uppercase;
}

.btn-red {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  background: red;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  text-transform: uppercase;
  color: #ffffff;
}

.btn-outline-primary {
  color: #3e95ef;
  background: white;
  border-color: #3e95ef;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  text-transform: uppercase;
}

.btn-danger {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  text-transform: uppercase;
}

.btn-outline-primary:hover {
  color: #ffff;
  background: #3e95ef;
}

.btn-outline-secondary {
  border-radius: 8px;
}

.login-bg {
  /* background: linear-gradient(84.06deg, rgba(26, 123, 220, 0.85) 13%, rgba(86, 184, 255, 0.85) 107.28%);
  height: 1000px;
   */
  background: url(./Assets/bg.jpg) no-repeat center center fixed;
  background-size: cover;
  width: 100%;
  height: 100%;
  position: fixed;
  overflow-y: auto;
}

.login-bg-add_uni {
  background: url(./Assets/bg.jpg) no-repeat center center fixed;
  background-size: cover;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.outter-login {
  background-color: #fff;
  text-align: left;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
  padding: 15px 40px 25px 40px;
  width: 400px;
}

.width-420 {
  width: 450px !important;
}

.swal2-title {
  font-weight: 500 !important;
}

.floatinglabelcustom {
  color: #5161c3;
  font-size: 14px;
  font-weight: normal;
  position: relative;
  pointer-events: none;
  left: 5px;
  top: 5px;
  transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
}

.customeDatepick {
  font-size: 17px;
  padding: 6px 5px 7px 5px;
  display: block;
  width: 100%;
  height: 30px;
  background-color: transparent;
  border: none;
  border-bottom: 1px solid #757575;
  color: black;
}

.modal-900 {
  max-width: 900px;
}

.modal-970 {
  max-width: 970px;
}

.modal-400 {
  max-width: 400px;
}

.model-480 {
  max-width: 480px;
}

.modal-500 {
  max-width: 500px;
}

.modal-520 {
  max-width: 520px;
}

.model-960 {
  max-width: 960px;
}

.model-1050 {
  max-width: 1050px;
}

.calenderCustom {
  position: absolute;
  top: 2.5em;
  right: 24px;
}

.customeDatePickWrapper .react-datepicker-wrapper {
  width: 100%;
}

.customeDatepick::placeholder {
  font-size: 14px;
}

#example-modal-sizes-title-sm {
  font-size: 20px;
}

.table-hover tbody tr:hover {
  background-color: #d1f4ff80;
}

.w-30 {
  width: 30%;
}

@media only screen and (max-width: 1111px) {
  .w-30 {
    width: 50%;
  }
}

.w-35 {
  width: 35%;
}

.card-header-open {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/*Student Grouping Start*/
.border-radious-8 {
  border-radius: 8px !important;
}

.border-radious-6 {
  border-radius: 6px !important;
}

.search_group input[type='text'] {
  padding: 8px;
  font-size: 15px;
  float: left;
  background: #ffffff;
  margin: 0px 0px 0px 0px;
  border: 0.5px solid rgba(0, 0, 0, 0.38);
  border-radius: 8px;
  width: 100%;
}

.border-radious-5 {
  border-radius: 5px !important;
}

.search_group_icon {
  margin-left: -23px;
  margin-top: 12px;
  color: #afadac;
  font-size: 22px;
}

.search_group::placeholder {
  color: rgba(0, 0, 0, 0.38);
  opacity: 1;
  /* Firefox */
  font-size: 15px;
}

.sb-example-1 .search {
  width: 100%;
  position: relative;
  display: flex;
}

.sb-example-1 .searchTerm {
  width: 100%;
  border-right: none;
  padding: 4px 2px 4px 2px;
  border-radius: 8px 0 0 8px;
  outline: none;
  color: #9e9e9e;
  border: 0.5px solid rgba(0, 0, 0, 0.38);
  border-right: none;
}

.modal-900 {
  max-width: 900px;
}

.modal-600 {
  max-width: 600px;
}

.modal-450 {
  max-width: 450px;
}

.modal-350 {
  max-width: 350px;
}

.model-900 {
  max-width: 900px;
}

.model-1000 {
  max-width: 1000px;
}

.model-700 {
  max-width: 700px;
}

.model-780 {
  max-width: 780px;
}

.model-600 {
  max-width: 600px;
}

.model-800 {
  max-width: 800px;
}

.modal-700 {
  max-width: 700px;
}

.model-400 {
  max-width: 400px;
}

.bg-lightgray {
  background-color: #e9eaec;
}

.bg-lightdark {
  background-color: #dadadc !important;
}

.bg-milghtdark {
  background: #edeef1 !important;
}

.bg-lightgreen {
  background: #d1f4ff;
}

.import_remove_icon {
  float: right;
  padding: 3px 10px 3px 10px;
}

.sb-example-1 .searchTerm::placeholder {
  color: rgba(0, 0, 0, 0.38);
  opacity: 1;
  /* Firefox */
  font-size: 13px;
}

.sb-example-1 .searchButton {
  width: 30px;
  height: 37px;
  border: 0.5px solid rgba(0, 0, 0, 0.38);
  background: #ffffff;
  text-align: center;
  color: #9e9e9e;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  font-size: 14px;
  border-left: none;
}

button.searchButton {
  outline: 0px auto -webkit-focus-ring-color !important;
}

.small_textbox {
  width: 10%;
}

.mt-10 {
  margin-top: 10px;
}

.mt--25 {
  margin-top: -25px;
}

.mt-27 {
  margin-top: 27px;
}

.mt-30 {
  margin-top: 30px;
}

/*Student Grouping End*/

.search-list {
  width: 100%;
  position: relative;
  display: flex;
}

.searchTerm-list {
  min-width: 440px;
  width: 100%;
  padding: 4px 34px 4px 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-sizing: border-box;
  border-radius: 8px;
  height: 38px;
}

.searchButton-list {
  color: #bcc0c0;
  position: relative;
  top: 10px;
  right: 26px;
}

::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(0, 0, 0, 0.38);
}

::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(0, 0, 0, 0.38);
}

:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(0, 0, 0, 0.38);
}

:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(0, 0, 0, 0.38);
}

.jumbotron-view {
  padding: 12px;
  background-color: #e9ecef;
  border-radius: 0.3rem;
  min-height: 120px;
}

.jumbotron-publish {
  padding: 12px;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}

.max-wd-70 {
  max-width: 70%;
}

@media (min-width: 576px) {
  .modal-sm-90 {
    max-width: 380px;
  }

  .container {
    max-width: none;
  }
}

.light-pink {
  background: rgba(235, 87, 87, 0.1);
}

.fc-grey {
  color: rgba(0, 0, 0, 0.6);
}

.outline-primary {
  background: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
}

.primary {
  background: #3e95ef;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
}

.search-card {
  background: #ffffff;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
}

.hr {
  margin-top: 8px;
  margin-bottom: 8px;
  width: 100%;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

.custom-form-control {
  display: block;
  /* width: 100%; */
  padding: 0px 10px;
  position: relative;
  bottom: 7px;
  /* font-size: 1rem; */
  /* line-height: 1.5; */
  /* color: #495057; */
  /* background-color: #f5f6f8; */
  background-clip: padding-box;
  border: none;
  /* border-radius: .25rem; */
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.custom-scroll {
  overflow-y: auto;
  height: 300px;
  padding: 2px 4px 2px 2px;
}

.custom-scroll::-webkit-scrollbar {
  width: 8px;
}

/* Track */
.custom-scroll::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 10px;
}

/* Handle */
.custom-scroll::-webkit-scrollbar-thumb {
  background: rgba(196, 196, 196, 0.5);
}

/* Handle on hover */
.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(196, 196, 196, 0.5);
}

.error-red {
  color: #ff0000;
}

.badge {
  font-weight: 500 !important;
}

.btn-secondary {
  color: #fff;
  background: #6c757d;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  text-transform: uppercase;
}

.btn-ins-login {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  background: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  text-transform: uppercase;
  border: 1px solid #9ca3af;
}

.btn-description {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #9ca3af;
}

.btn-description-border {
  /* border: 1px dashed rgba(0, 0, 0, 0.1); */
  border: 2px dashed #9ca3af85;
  border-radius: 4px;
}

/*Student Grouping End*/

.table-border {
  border: 1px solid #efeaea;
  /* -webkit-box-shadow: 5px 5px 15px 5px rgba(0,0,0,0.61); */
  box-shadow: 0px 1px 11px -7px rgb(0 0 0 / 38%);
  border-radius: 8px;
}

/* width */
.infra_table::-webkit-scrollbar {
  height: 10px;
  width: 10px;
}

/* Handle */
.infra_table::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.infra_table::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.infra_table_height::-webkit-scrollbar {
  width: 7px;
}

/* Handle */
.infra_table_height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.infra_table_height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.infra_table_width::-webkit-scrollbar {
  height: 7px;
}

/* Handle */
.infra_table_width::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.infra_table_width::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.infra_table_height {
  overflow-y: auto;
  max-height: 351px;
}

.infra_table {
  overflow-x: auto;
  width: 100%;
}

.infra_table table {
  width: 100%;
}

.infra_table table tbody {
  display: block;
  min-height: 350px;
  overflow-x: hidden;
}

.infra_table table thead {
  display: table;
}

.infra_table table tfoot {
  display: table;
}

.infra_table table thead tr,
.infra_table table tbody tr,
.infra_table table tfoot tr {
  display: table-row;
}

.infra_table .aw-50 {
  min-height: 1px;
  width: 50px;
  word-wrap: break-word;
}

.infra_table .aw-100 {
  min-height: 1px;
  width: 100px;
  word-wrap: break-word;
}

.infra_table .aw-150 {
  min-height: 1px;
  width: 150px;
  word-wrap: break-word;
}

.infra_table .aw-200 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}

.infra_table .aw-300 {
  min-height: 1px;
  width: 300px;
  word-wrap: break-word;
}

.infra_table .aw-400 {
  min-height: 1px;
  width: 400px;
  word-wrap: break-word;
}

.infra_table_shadows {
  border: 1px solid #e2e0e0;
  border-radius: 8px;
  box-shadow: inset 7px -6px 5px -5px rgb(0 0 0 / 38%);
}

/* width */
.infra_tableview::-webkit-scrollbar {
  height: 10px;
  width: 10px;
}

/* Handle */
.infra_tableview::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.infra_tableview::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.infra_tableview_height::-webkit-scrollbar {
  width: 7px;
}

/* Handle */
.infra_tableview_height::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.infra_tableview_height::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

/* width */
.infra_tableview_width::-webkit-scrollbar {
  height: 7px;
}

/* Handle */
.infra_tableview_width::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

/* Handle on hover */
.infra_tableview_width::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.infra_tableview_height {
  overflow-y: auto;
  max-height: 700px;
}

.infra_tableview {
  overflow-x: auto;
  width: 100%;
}

.infra_tableview table {
  width: 100%;
}

.infra_tableview table tbody {
  display: block;
  min-height: 200px;
  overflow-x: hidden;
}

.infra_tableview table thead {
  display: table;
}

.infra_tableview table tfoot {
  display: table;
}

.infra_tableview table thead tr,
.infra_tableview table tbody tr,
.infra_tableview table tfoot tr {
  display: table-row;
}

.infra_tableview .aw-50 {
  min-height: 1px;
  width: 50px;
  word-wrap: break-word;
}

.infra_tableview .aw-100 {
  min-height: 1px;
  width: 100px;
  word-wrap: break-word;
}

.infra_tableview .aw-150 {
  min-height: 1px;
  width: 150px;
  word-wrap: break-word;
}

.infra_tableview .aw-200 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}

.infra_tableview .aw-250 {
  min-height: 1px;
  width: 250px;
  word-wrap: break-word;
}

.infra_tableview .aw-300 {
  min-height: 1px;
  width: 300px;
  word-wrap: break-word;
}

.infra_tableview .aw-400 {
  min-height: 1px;
  width: 400px;
  word-wrap: break-word;
}

.min-height-300 {
  min-height: 300px;
}

.height-300 {
  height: 300px;
  overflow: auto;
}

.height-unset {
  height: unset;
}

span.chip {
  white-space: break-spaces !important;
  margin-bottom: 1px;
}

th.remove_border_bottom {
  border-bottom: 1px solid #fff0 !important;
}

#icon_space {
  list-style: none;
  -webkit-padding-start: 0px;
}

#icon_space_li {
  font-size: 1em;
  vertical-align: middle;
  margin-right: 10px;
  padding-top: 9px;
  list-style-type: none;
}

/* img {
  pointer-events: none;
} */

.badge_bg_white {
  background: white;
  padding: 10px 15px 10px 15px;
  border: 1px solid #c7c4c4;
  border-radius: 15px;
  font-size: 14px;
  color: #3493e6;
  cursor: pointer;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #757575;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 0%);
}

.w-15 {
  width: 15%;
}

.dropdown-item {
  background-color: white !important;
}

.dropdown-item:focus,
.dropdown-item:hover {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa !important;
}

.optionListContainer {
  overflow-y: auto;
  z-index: 10;
}

.height_150px {
  height: 150px;
}

.width_150px {
  width: 150px;
}

.overflow_wrap {
  overflow-wrap: anywhere;
}

.sidenav-parent-item {
  padding: 8px 8px 18px 32px;
  text-decoration: none;
  font-size: 14px;
  color: #818181;
  transition: 0.3s;
  font-weight: 500;
  display: block;
  cursor: pointer;
}

.sidenav-parent-item:hover {
  color: #0047cc;
}

.model-956 {
  max-width: 956px;
}

td.td-border {
  border: 1px solid #f4f5f5 !important;
}

.my--80_5 {
  margin-right: -5px;
  margin-left: -110px;
}

.ml--5 {
  margin-left: -5px;
}

.model-1200 {
  max-width: 1200px;
}

.digi-img-capture {
  width: 640px;
  margin: auto;
  border-radius: 16px;
  margin-top: 1em;
}

.transparent-image {
  position: absolute;
  z-index: 9;
}

.extra_bold {
  font-weight: 600;
  color: #0c0c0cd6;
}

#menu_student_group {
  display: flex;
  padding-left: 20px;
  margin-bottom: 0px;
  overflow-x: scroll;
}

/* width */
#menu_student_group::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}

/* Handle */
#menu_student_group::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.font-family-roboto {
  font-family: 'Roboto';
}

.no-padding-left {
  padding-left: 0px;
}

.no-padding-right {
  padding-right: 0px;
}

.digi-text-justify {
  text-align: justify;
}

.digi-camera {
  background-color: #f3f4f6;
  border: 1px dashed #d1d5db;
}

.digi-editor-border {
  border: 1px solid #e9ecef;
}

.bg-select-none,
.bg-select-none:focus {
  background: none;
  border: none;
  color: white;
}

.p-viewer {
  z-index: 9999;
  position: absolute;
  right: 10px;
  top: 45px;
}

.error-show-ar {
  z-index: 9999;
  position: absolute;
  top: 40px;
  left: 25px;
}

.input-section-border-red {
  border: solid 1.5px red;
}

.error-show {
  z-index: 9999;
  position: absolute;
  right: 25px;
  top: 38px;
}

.search-icon-show {
  position: absolute;
  right: 7px;
  top: 10px;
  background: white;
}

.p-viewer-ar {
  z-index: 9999;
  position: absolute;
  top: 45px;
  left: 10px;
}

.add-university {
  width: 55%;
  margin: auto;
}

@media (max-width: 850px) {
  .add-university {
    width: 85%;
  }
}

.add-college {
  width: 65%;
  margin: auto;
}

@media (max-width: 850px) {
  .add-college {
    width: 100%;
  }
}

.MuiOutlinedInput-root {
  border-radius: 24px;
}

.text-capitalize {
  text-transform: capitalize;
}

.model-border {
  border: 1px solid #9ca3af;
}

.MuiButton-root {
  text-transform: capitalize !important;
}

.w-100 {
  width: 100%;
}

/* .MuiButton-containedPrimary {
  background-color: #147afc !important;
  box-shadow: none !important;
}

.MuiButton-containedPrimary:hover {
  box-shadow: none !important;
} */

.text-wrap-file {
  word-wrap: break-word;
}

.card-header-icon::before {
  content: '' !important;
}

.load-more-slo {
  position: absolute;
  bottom: 1px;
  right: 38px;
}

.datepickerAr div {
  text-align: center !important;
}

.w-55 {
  width: 55%;
}

.overFlow_dots {
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
  width: 55%;
}

.assessment-box {
  box-shadow: 0px 1px 3px rgba(17, 24, 39, 0.2);
}

.alphabet-icon {
  width: 65px !important;
}

.alphabet-icon-text {
  color: #53aef9;
}

.grandParentContainer {
  display: table;
  height: 100%;
  margin: 0 auto;
}

.parentContainer {
  display: table-cell;
  vertical-align: middle;
}

.staff_model_bold {
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.75;
}

.assessment_outcome_select {
  max-width: 330px;
}

.univ-logo {
  width: 65px;
  float: right;
}

/* react-error-overlay-issue covered by this line*/
iframe[style='position: fixed; top: 0px; left: 0px; width: 100%; height: 100%; border: none; z-index: 2147483647;'] {
  pointer-events: none;
}

/* react-error-overlay-issue covered by this line*/
.left-shift {
  left: 10px !important;
  right: 0px !important;
  background: transparent !important;
}

.card-hide {
  position: absolute;
  visibility: hidden;
}

.card-show {
  position: relative;
  visibility: visible;
}

.attainment-report-chart {
  position: relative;
}

.attainment-report-chart .chart-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 15px;
  font-weight: 500;
}

.attainment-achieved {
  background: #4ade80;
  border: 1px solid #16a34a;
  border-radius: 2px;
  width: 11px;
  height: 11px;
  position: absolute;
  margin-top: 5px;
}

.attainment-not-achieved {
  background: #e5e7eb;
  border: 1px solid #6b7280;
  border-radius: 2px;
  width: 11px;
  height: 11px;
  position: absolute;
  margin-top: 5px;
}

.w-120px {
  width: 120px;
}

.w-9 {
  width: 9%;
}

.digi-expandless {
  height: 13px;
}

.color-pointer:hover {
  background-color: #eff9fb;
  cursor: pointer;
}

.color-pointer-active {
  background-color: #eff9fb;
}

.overflow-y-lms {
  overflow: auto;
  max-height: 20em;
}

.overflow-y-lms::-webkit-scrollbar {
  width: 10px;
}

.overflow-y-lms::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.overflow-y-lms::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.go-wrapper .aw-15 {
  min-height: 1px;
  width: 15%;
  word-wrap: break-word;
}

.lms-report-table-overflow-auto {
  overflow: auto;
  white-space: nowrap;
}

.lms-report-table-overflow-auto::-webkit-scrollbar {
  height: 8px;
}

.lms-report-table-overflow-auto::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.lms-report-table-overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.border_lmsReport_table table {
  border-top: 2px solid #e5e7eb !important;
}

.w-78 {
  width: 78%;
}

.date_border {
  border: 1px solid gray;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 14px;
}

.Text_WordBrake {
  text-wrap: wrap;
}

.menu-ri {
  font-weight: 100;
  margin-left: auto;
  padding-left: 10px;
}

.calendar-menu-list-height {
  max-height: 30vh;
  overflow: auto;
}

.tardis_color {
  color: black !important;
}

.digi-cursor-no-drop {
  cursor: no-drop;
  opacity: 0.5 !important;
}
