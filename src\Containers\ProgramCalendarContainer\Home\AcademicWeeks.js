import React, { Fragment } from 'react';
import PropTypes, { oneOfType } from 'prop-types';

import { WeekWrapper, WeekRowWrapper } from '../Styled';

const AcademicWeeks = ({ data }) => {
  return (
    <Fragment>
      <WeekWrapper len={data.length}>
        <Fragment>
          {data.map((item, i) => {
            return (
              <WeekRowWrapper key={i} className="right">
                <div className="pdfFont">{i + 1}</div>
              </WeekRowWrapper>
            );
          })}
        </Fragment>
      </WeekWrapper>
    </Fragment>
  );
};

AcademicWeeks.propTypes = {
  data: oneOfType([PropTypes.array, PropTypes.object]),
};

export default AcademicWeeks;
