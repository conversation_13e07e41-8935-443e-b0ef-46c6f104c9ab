import React, { useState, Suspense, useEffect } from 'react';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';
// import FormControl from '@mui/material/FormControl';
// import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import ThemeProvider from '@mui/styles/ThemeProvider';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
// import Popover from '@mui/material/Popover';
import Typography from '@mui/material/Typography';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import FilterIcon from '../../../../Assets/sort.png';
import ColumnIcon from '../../../../Assets/columns.svg';
import { MUI_THEME } from '../../../../utils';
import { t } from 'i18next';

import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { Menu } from '@mui/material';
import moment from 'moment';

const ITEM_HEIGHT = 48;
const ManageColumn = React.lazy(() => import('../../modal/ManageColumn'));
function AttendanceLogFilters({
  options,
  filters,
  handleChange,
  handleExport,
  isExportActive,
  monthArray,
  handleTableFilter,
  setFilters,
  tableFilter,
  minMaxDate,
}) {
  // const [anchorEl, setAnchorEl] = useState(null);
  const [openPopup, setOpen] = useState(false);

  // const handleClick = (event) => {
  //   setAnchorEl(event.currentTarget);
  // };

  // const handleClose = () => {
  //   setAnchorEl(null);
  // };

  const [anchorEl4, setAnchorEl4] = React.useState(null);
  const open4 = Boolean(anchorEl4);
  const handleClick4 = (event) => {
    setAnchorEl4(event.currentTarget);
  };
  const handleClose4 = () => {
    setAnchorEl4(null);
  };

  const [date, setDate] = useState(
    Map({
      type: 'all',
    })
  );
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  function handleDate(name) {
    if (name === 'all') {
      setDate(date.set('type', 'all'));
      setStartDate(null);
      setEndDate(null);
      setFilters(filters.set('date', 'all').set('startDate', '').set('endDate', ''));
      handleClose4();
    } else {
      setDate(date.set('type', 'customize'));
    }
  }

  function handleApply() {
    const startD = moment(new Date(startDate)).format('YYYY-MM-DD');
    const endD = moment(new Date(endDate)).format('YYYY-MM-DD');
    setFilters(
      filters.merge(
        Map({
          startDate: startD,
          endDate: endD,
        })
      )
    );
    handleClose4();
  }

  function getOptions(type) {
    return options.get(type, List());
  }

  const month = filters.get('month', '');
  useEffect(() => {
    setDate(date.set('type', 'all'));
    setStartDate(null);
    setEndDate(null);
  }, [month]); //eslint-disable-line

  function handleFilterChange(name, value) {
    let updatedFilters = filters.merge(
      Map({ [name]: name === 'studentGroup' ? fromJS(value) : value })
    );
    if (name === 'studentGroup' && filters.get('viewType') === 'student') {
      const studentGroup =
        getOptions('studentGroups').find((studentGroup) => studentGroup.get('value') === value) ||
        Map();
      updatedFilters = updatedFilters.set('gender', studentGroup.get('gender', ''));
    } else if (name === 'month') {
      updatedFilters = updatedFilters.set('date', 'all').set('startDate', '').set('endDate', '');
    }
    handleChange(updatedFilters);
  }

  const sDate = filters.get('startDate', '');
  const eDate = filters.get('endDate', '');

  function getCustomDate() {
    if (sDate !== '' && eDate !== '') {
      const startYear = moment(sDate).format('YYYY');
      const endYear = moment(eDate).format('YYYY');
      const sameYear = startYear === endYear;
      const st = moment(new Date(sDate)).format(`MMM Do`);
      const ed = moment(new Date(eDate)).format(`MMM Do, YYYY`);
      return `${st}${sameYear ? '' : `, ${startYear}`} - ${ed}`;
    }
    return 'All Date';
  }

  return (
    <div className="p-2">
      <div className="d-flex align-items-center justify-content-end">
        <div>
          <MButton
            variant="contained"
            color="darkGray"
            clicked={() => setOpen(true)}
            className="mr-1 f-14 p-2"
          >
            <img alt="ColumnIcon" src={ColumnIcon} className="mr-1" />
            <span className="f-12">Manage Columns</span>
          </MButton>
        </div>

        <div>
          <div className="text-center">
            {isExportActive && (
              <ThemeProvider theme={MUI_THEME}>
                <Button variant="contained" color="primary" onClick={handleExport}>
                  {t('reports_analytics.export').toUpperCase()}
                </Button>
              </ThemeProvider>
            )}
          </div>
        </div>
      </div>

      <div className="col-md-12 pl-0 pr-0">
        <div className="row align-items-center pr-3 pl-3 align-items-end">
          <div className={`col-md-2 px-2`}>
            <div className="f-14">{t('reports_analytics.student_group').toUpperCase()}</div>
            {/* <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filters.get('studentGroup', '')}
                  onChange={(e) => handleFilterChange('studentGroup', e.target.value)}
                >
                  {getOptions('studentGroups').map((option) => (
                    <option key={option.get('value')} value={option.get('value')}>
                      {option.get('name')}
                    </option>
                  ))}
                </Select>
              </FormControl> */}
            <MaterialInput
              elementType={'materialSelectMultiple'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{
                options: [
                  // { name: t('reports_analytics.all'), value: 'all' },
                  ...getOptions('studentGroups').toJS(),
                ],
              }}
              label={''}
              labelclass={'mb-0 f-14'}
              // placeholder={`---Select---`}
              changed={(e) => handleFilterChange('studentGroup', e.target.value)}
              value={filters.get('studentGroup', List()).toJS()}
              isAllSelected={false}
              multiple={true}
              displayName="Select Student Group"
            />
          </div>
          <div className="col-md-1 px-0">
            <div className="f-14">&nbsp;</div>
            <div className="course_sort_icon bg-gray">
              <img src={FilterIcon} alt="sort" className="img-fluid" />
            </div>
          </div>
          <div className="col-md-2 px-2">
            <div className="f-14">{t('reports_analytics.gender').toUpperCase()}</div>
            {/* <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filters.get('gender', '')}
                  onChange={(e) => handleFilterChange('gender', e.target.value)}
                  disabled={filters.get('viewType') === 'student'}
                >
                  <option value="">{t('reports_analytics.all')}</option>
                  {getOptions('genders').map((option) => (
                    <option key={option.get('value')} value={option.get('value')}>
                      {option.get('name')}
                    </option>
                  ))}
                </Select>
              </FormControl> */}
            <MaterialInput
              elementType={'materialSelectNew'}
              // disabled={filters.get('viewType') === 'student'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{
                options: [
                  { name: t('reports_analytics.all'), value: '' },
                  ...getOptions('genders').toJS(),
                ],
              }}
              changed={(e) => handleFilterChange('gender', e.target.value)}
              value={filters.get('gender', '')}
              displayEmpty={true}
              displayName="Select Gender"
            />
          </div>
          <div className="col-md-2 px-2">
            <div className="f-14">MONTH</div>
            <MaterialInput
              elementType={'materialSelectNew'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{
                options: [
                  { name: 'All Month', value: '', disabled: !tableFilter.includes('overall') },
                  ...monthArray,
                ],
              }}
              changed={(e) => handleFilterChange('month', e.target.value)}
              value={filters.get('month', '')}
              displayEmpty={true}
              displayName="Select Month"
            />
          </div>

          <div className={`col-md-${startDate === null ? '2' : '3'} px-2`}>
            <div className="f-14">DATE</div>
            <Typography
              component="div"
              className="border rounded remove_hover mb-1 bg-white"
              aria-label="more"
              aria-controls="long-menu"
              aria-haspopup="true"
              onClick={handleClick4}
            >
              <div className="d-flex justify-content-between digi-typo">
                <p className="mb-0 f-16 pl-3"> {getCustomDate()}</p>
                <ArrowDropDownIcon className="text-gray" />
              </div>
            </Typography>

            <Menu
              id="long-menu"
              anchorEl={anchorEl4}
              onClose={handleClose4}
              keepMounted
              open={open4}
              PaperProps={{
                style: {
                  maxHeight: ITEM_HEIGHT * 10.5,
                  width: '30ch',
                },
              }}
            >
              <p className="mb-0 f-14 pl-3">Select Date</p>
              <div className="px-3 pb-2 pt-1 f-14">
                <p
                  className="mb-0 f-16 py-2 pl-4 digi-date-select"
                  onClick={() => handleDate('all')}
                  style={{
                    background: date.get('type', '') === 'all' ? '#EFF9FB' : '#FFFFFF',
                  }}
                >
                  All Date
                </p>

                <div onClick={() => handleDate('customize')}>
                  <Accordion elevation={0}>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls="panel1a-content"
                      id="panel1a-header"
                      className="px-0 mb-1"
                      sx={{
                        background: date.get('type', '') === 'customize' ? '#EFF9FB' : '#FFFFFF',
                        mb: 2,
                        borderTop: '1px solid #E0E0E0;',
                        borderBottom: '1px solid #E0E0E0;',
                        '& .MuiAccordionSummary-content': { justifyContent: 'center' },
                      }}
                    >
                      <Typography>Customize Date Range</Typography>
                    </AccordionSummary>
                    {date.get('type', '') === 'customize' && (
                      <AccordionDetails sx={{ py: 1, px: 0 }}>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <div className="f-14">Start Date</div>
                          <div className="date_border">
                            <DatePicker
                              value={startDate}
                              onChange={(e) => {
                                setStartDate(e);
                                setEndDate(e);
                              }}
                              inputFormat="DD/MM/YYYY"
                              renderInput={({ inputRef, inputProps, InputProps }) => (
                                <div className="d-flex align-items-center">
                                  <input
                                    ref={inputRef}
                                    {...inputProps}
                                    className="border-none w-78"
                                  />
                                  {InputProps?.endAdornment}
                                </div>
                              )}
                              minDate={minMaxDate?.minDate}
                              maxDate={minMaxDate?.maxDate}
                            />
                          </div>
                          <div className="f-14">End Date</div>
                          <div className="date_border">
                            <DatePicker
                              value={endDate}
                              onChange={(e) => setEndDate(e)}
                              // value={date.get('endDate', '')}
                              // onChange={(e) => handleDateChange(e, 'endDate')}
                              inputFormat="DD/MM/YYYY"
                              renderInput={({ inputRef, inputProps, InputProps }) => (
                                <div className="d-flex align-items-center">
                                  <input
                                    ref={inputRef}
                                    {...inputProps}
                                    className="border-none w-78"
                                  />
                                  {InputProps?.endAdornment}
                                </div>
                              )}
                              minDate={minMaxDate?.minDate}
                              maxDate={minMaxDate?.maxDate}
                            />
                          </div>
                        </LocalizationProvider>
                      </AccordionDetails>
                    )}
                  </Accordion>
                </div>
                {date.get('type', '') === 'customize' && (
                  <div className="row justify-content-around pt-1">
                    <Button
                      size="small"
                      variant="outlined"
                      sx={{
                        '&:focus': {
                          outline: 'none',
                        },
                      }}
                      onClick={() => handleDate('all')}
                      className="col-5 border text-dark border-secondary"
                    >
                      Cancel
                    </Button>
                    <Button
                      disabled={startDate === null || endDate === null}
                      onClick={() => handleApply()}
                      size="small"
                      variant="contained"
                      className="col-5"
                    >
                      apply
                    </Button>
                  </div>
                )}
              </div>
            </Menu>
          </div>

          <div className={`col-md-${startDate === null ? '3' : '2'} px-2`}>
            <div className="f-14">&nbsp;</div>
            <div className="bg-white dash_box_head2 border border-radious-5 p-1">
              <div className="sb-example-3">
                <div className="search">
                  <input
                    type="text"
                    className="searchTerm pl-3"
                    placeholder={t('reports_analytics.search_name_id')}
                    value={filters.get('search', '')}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                  />
                  <button type="submit" className="searchButton">
                    <i className="fa fa-search"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {openPopup && (
        <Suspense fallback="">
          <ManageColumn
            show={openPopup}
            modalClose={() => setOpen(false)}
            handleTableFilter={handleTableFilter}
            filteredTableFilter={filters.get('tableFilter', List())}
            viewType={filters.get('viewType', 'student')}
          />
        </Suspense>
      )}
    </div>
  );
}

AttendanceLogFilters.propTypes = {
  options: PropTypes.instanceOf(Map),
  filters: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  resetLabelName: PropTypes.string,
  handleExport: PropTypes.func,
  isExportActive: PropTypes.bool,
  monthArray: PropTypes.array,
  handleTableFilter: PropTypes.func,
  setFilters: PropTypes.func,
  tableFilter: PropTypes.instanceOf(List),
  minMaxDate: PropTypes.object,
};

export default AttendanceLogFilters;
