import { createAction } from '../util';
import axios from '../../axios';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}
export const GET_USER_AND_COURSE_REQUEST = 'GET_USER_AND_COURSE_REQUEST';
export const GET_USER_AND_COURSE_SUCCESS = 'GET_USER_AND_COURSE_SUCCESS';
export const GET_USER_AND_COURSE_FAILURE = 'GET_USER_AND_COURSE_FAILURE';

const getUserAndCourseRequest = createAction(GET_USER_AND_COURSE_REQUEST);
const getUserAndCourseSuccess = createAction(GET_USER_AND_COURSE_SUCCESS, 'data');
const getUserAndCourseFailure = createAction(GET_USER_AND_COURSE_FAILURE, 'error');

export function getUserAndCourse(params) {
  return function (dispatch) {
    dispatch(getUserAndCourseRequest());
    axios
      .get(`/digiclass/courseHandout/userAndCourseSearchList`, { params })
      .then((res) => dispatch(getUserAndCourseSuccess(res.data.data)))
      .catch((error) => dispatch(getUserAndCourseFailure(error)));
  };
}

export const GET_HANDOUT_REPORT_REQUEST = 'GET_HANDOUT_REPORT_REQUEST';
export const GET_HANDOUT_REPORT_SUCCESS = 'GET_HANDOUT_REPORT_SUCCESS';
export const GET_HANDOUT_REPORT_FAILURE = 'GET_HANDOUT_REPORT_FAILURE';

const getHandoutReportRequest = createAction(GET_HANDOUT_REPORT_REQUEST);
const getHandoutReportSuccess = createAction(GET_HANDOUT_REPORT_SUCCESS, 'data');
const getHandoutReportFailure = createAction(GET_HANDOUT_REPORT_FAILURE, 'error');

export function getHandoutReport(params) {
  return function (dispatch) {
    dispatch(getHandoutReportRequest());
    axios
      .get(`/digiclass/courseHandout/handoutReports`, { params })
      .then((res) => dispatch(getHandoutReportSuccess(res.data.data)))
      .catch((error) => dispatch(getHandoutReportFailure(error)));
  };
}
