import * as React from 'react';
import PropTypes from 'prop-types';
import { styled } from '@mui/material/styles';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import DoneIcon from '@mui/icons-material/Done';
import StepConnector, { stepConnectorClasses } from '@mui/material/StepConnector';
import { makeStyles } from '@mui/styles';

export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '10px', // Adjust the font size as needed
    marginTop: '4px !important',
    // You can also add other styles here, such as fontFamily, fontWeight, etc.
  },
}));
const ConnectingLine = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 14,
    left: 'calc(-50% + 14px)',
    right: 'calc(50% + 6px)',
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : '#62a6fb',
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : '#62a6fb',
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: '.1rem',
    border: 0,
    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : '#62a6fb',
    borderRadius: 1,
  },
}));

const CompleteRootIcon = styled('div')(() => ({
  backgroundColor: '#147afc',
  zIndex: 1,
  color: '#c6defe',
  width: '20px',
  height: '20.09px',
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  paddingRight: '0px',
}));
const PendingRootIcon = styled('div')(() => ({
  width: '20px',
  height: '20.09px',
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  paddingRight: '0px',
  color: '#a8aeb9',
  background: 'white',
  border: '.1rem solid #a8aeb9',
}));
function StepIcon(props) {
  const { active, completed, className } = props;
  if (completed)
    return (
      <CompleteRootIcon ownerState={{ completed, active }} className={className}>
        <DoneIcon sx={{ fontSize: '12px' }} />
      </CompleteRootIcon>
    );
  else
    return (
      <PendingRootIcon>
        <FiberManualRecordIcon sx={{ fontSize: '9px' }} />
      </PendingRootIcon>
    );
}

StepIcon.propTypes = {
  /**
   * Whether this step is active.
   * @default false
   */
  active: PropTypes.bool,
  className: PropTypes.string,
  /**
   * Mark the step as completed. Is passed to child components.
   * @default false
   */
  completed: PropTypes.bool,
  /**
   * The label displayed in the step icon.
   */
  icon: PropTypes.node,
};

const steps = ['STEP 1', 'STEP 2', 'STEP 3', 'STEP 4'];
const dummy = () => {};
export default function CustomizedSteppers({ categoryForm, onClickRoute }) {
  const step = categoryForm.get('step', 1);
  const show = step === 4 ? step : step - 1;
  const classes = useStyles();
  return (
    <Stepper alternativeLabel activeStep={show} connector={<ConnectingLine />}>
      {steps.map((label, i) => (
        <Step key={label} className="pr-0">
          <StepLabel
            classes={{ label: classes.label }}
            onClick={i <= step - 1 ? onClickRoute(i + 1) : dummy}
            className="m-1"
            StepIconComponent={StepIcon}
          >
            {label}
          </StepLabel>
        </Step>
      ))}
    </Stepper>
  );
}
