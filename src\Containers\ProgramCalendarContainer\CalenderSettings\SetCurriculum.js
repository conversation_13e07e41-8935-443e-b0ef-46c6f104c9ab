import React, { Fragment, useReducer, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { NotificationManager } from 'react-notifications';
import PropTypes from 'prop-types';

import { FlexWrapper, PrimaryButton, Null, Padding } from '../Styled';
import {
  changeTitle,
  activateModal,
  getData,
  dashboardReset,
  commonApiCall,
  institutionEventsGet,
  changeTerm,
  changeYear,
  // getLevelNumbers,
} from '../../../_reduxapi/actions/calender';
import NavRowButtons from '../UtilityComponents/NavRowButtons';
import LevelField from './LevelField';
import rootReducer from './SettingsReducer';
import { level_initial_state } from './InitialState';
import Loader from '../../../Widgets/Loader/Loader';
// import CheckIds from '../UtilityComponents/CheckIds';
import Tooltips from '../../../_components/UI/Tooltip/Tooltip';
import { getFormattedHijiriYear, getLang } from '../../../utils';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
const lang = getLang();
const SetCurriculum = (props) => {
  const {
    id,
    year1,
    year2,
    year3,
    year4,
    year5,
    year6,
    apiCalled,
    commonApiCall,
    changeTitle,
    activateModal,
    isLoading,
    _calender_id,
    token,
    getData,
    institutionEventsGet,
    // getLevelNumbers,
    programId,
    activeInstitutionCalendar,
    changeTerm,
    changeYear,
  } = props;
  const eventsState = useReducer(rootReducer, level_initial_state);
  const history = useHistory();
  const match = useRouteMatch();
  const active = match.params.year || 'year1';

  const [level, setLevel] = eventsState; // eslint-disable-line
  const [loaded, setLoaded] = useState(false);
  const [disable, setDisable] = useState(false);
  const [count, setCount] = useState(0);

  const start = props[active]?.['raw_data']?.['level']?.[0]?.['start_date'];
  const end = props[active]?.['raw_data']?.['level']?.[0]?.['end_date'];

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let p_id = params.get('programid');
  let p_name = params.get('pname');
  let urlTerm = params.get('term');
  let urlYear = params.get('year');
  useEffect(() => {
    changeTerm(urlTerm);
    changeYear(urlYear);
  }, [urlTerm, changeTerm, urlYear, changeYear]);

  useEffect(() => {
    if (p_id !== null && p_name !== null && activeInstitutionCalendar.get('_id', '') !== '') {
      commonApiCall(
        token,
        NotificationManager,
        p_id,
        p_name,
        activeInstitutionCalendar.get('_id', '')
      );
    }
  }, [commonApiCall, p_id, p_name, token, activeInstitutionCalendar]);
  // useEffect(() => {
  //   if (!apiCalled && token) {
  //     commonApiCall(token, NotificationManager);
  //   }
  // }, [token, apiCalled, commonApiCall]);

  useEffect(() => {
    if (_calender_id) {
      setDisable(true);
    } else {
      setDisable(false);
    }
  }, [_calender_id]);

  useEffect(() => {
    if (apiCalled && !loaded && activeInstitutionCalendar && !activeInstitutionCalendar.isEmpty()) {
      console.log('landing6'); //eslint-disable-line
      getData(
        null,
        NotificationManager,
        setLoaded,
        match.params.id,
        activeInstitutionCalendar.get('_id')
      );
      //getLevelNumbers(programId);
    }
  }, [
    apiCalled,
    loaded,
    getData,
    // getLevelNumbers,
    programId,
    match.params.id,
    setLoaded,
    activeInstitutionCalendar,
  ]);

  useEffect(() => {
    if (apiCalled && loaded && count === 0 && activeInstitutionCalendar) {
      institutionEventsGet(id, activeInstitutionCalendar.get('_id'));
      setCount(1);
    }
  }, [apiCalled, count, id, institutionEventsGet, loaded, activeInstitutionCalendar]);

  useEffect(() => {
    setLevel({
      type: 'INITIAL_LOAD',
      payload: {
        year1: year1,
        year2: year2,
        year3: year3,
        year4: year4,
        year5: year5,
        year6: year6,
      },
      _calender_id,
    });
  }, [_calender_id, setLevel, year1, year2, year3, year4, year5, year6]);

  useEffect(() => {
    changeTitle(t('role_management.role_actions.Calendar Settings'));
  });

  // const check_save =
  //   (props.year2.year_show
  //     ? props.year2.level_one_start_date && props.year2.level_one_end_date
  //     : true) &&
  //   (props.year3.year_show
  //     ? props.year3.level_one_start_date && props.year3.level_one_end_date
  //     : true) &&
  //   (props.year4.year_show
  //     ? props.year4.level_one_start_date && props.year4.level_one_end_date
  //     : true) &&
  //   (props.year5.year_show
  //     ? props.year5.level_one_start_date && props.year5.level_one_end_date
  //     : true) &&
  //   (props.year6.year_show
  //     ? props.year6.level_one_start_date && props.year6.level_one_end_date
  //     : true) &&
  //   true;

  const dynamic_go = () => {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let urlPg = params.get('programid');
    let urlYear = params.get('year');
    let urlName = params.get('pname');
    let urlTerm = params.get('term');
    if (match.params.sem) {
      // history.push(
      //   `/program-calendar/${match.params.id}/${match.params.year}/${match.params.sem}`
      // );
    } else {
      history.push(
        `/program-calendar/${match.params.id}/${match.params.year}?programid=${urlPg}&year=${urlYear}&pname=${urlName}&term=${urlTerm}`
      );
    }
  };

  function getEventStartEndDate(date) {
    const levels = level?.[active]?.level.filter((item) => item?.term === urlTerm);
    if (levels.length > 0) {
      const filterLevel = levels;
      if (filterLevel.length > 0) {
        const dateArray = filterLevel
          .map((item) => item?.[date])
          .filter((item) => item !== undefined && item !== '');
        if (dateArray.length > 0) {
          if (date === 'start_date') {
            return dateArray.reduce(function (a, b) {
              return a < b ? a : b;
            });
          } else {
            return dateArray.reduce(function (a, b) {
              return a > b ? a : b;
            });
          }
        }
        return '';
      }
    }
    return '';
  }

  const eventMinDate = getEventStartEndDate('start_date');
  const eventMaxDate = getEventStartEndDate('end_date');

  return (
    <Fragment>
      <Loader isLoading={isLoading} />
      {/* {apiCalled && loaded && <CheckIds load={loaded} />} */}
      <FlexWrapper>
        <Padding
          className="back"
          onClick={() => {
            // dashboardReset();
            changeTitle();
            dynamic_go();
          }}
        >
          <i
            className={`fa remove_hover ${lang !== 'ar' ? 'fa-arrow-left' : 'fa-arrow-right'}`}
          ></i>
        </Padding>
        <Padding>
          <Trans i18nKey={'program_calendar.program_calendar_settings'}></Trans>
        </Padding>
        <Null />
        {/* <Tooltips
          title={
            check_save
              ? "Go to Program Calender Page"
              : "Set level dates first!"
          }
        >
          <PrimaryButton
            className={check_save ? "" : "disable"}
            disabled={!check_save}
            onClick={() => {
              dynamic_go();
              changeTitle();
            }}
          >
            save
          </PrimaryButton>
        </Tooltips> */}
      </FlexWrapper>
      <FlexWrapper className="wrap" mg="0 25px 25px 25px">
        <Padding style={{ fontSize: '16px' }} pd="25px 40px">
          {`${t('academic_year')} ${getFormattedHijiriYear(
            activeInstitutionCalendar.get('calendar_name', ''),
            activeInstitutionCalendar.get('start_date', ''),
            activeInstitutionCalendar.get('end_date', '')
          )}`}
        </Padding>
        <Null />
        <Tooltips
          title={
            disable
              ? t('program_calendar.curriculum_already_set')
              : t('program_calendar.set_new_curriculum')
          }
        >
          <PrimaryButton
            className={disable ? 'light disable' : 'light'}
            disabled={disable}
            onClick={() => activateModal('curriculum')}
          >
            <Trans i18nKey={'program_calendar.set_curriculum'}></Trans>
          </PrimaryButton>
        </Tooltips>
      </FlexWrapper>
      <Fragment>
        {_calender_id !== '' ? (
          <Fragment>
            <FlexWrapper mg="0 25px">
              <NavRowButtons theme="light" type="term" />
            </FlexWrapper>
            <FlexWrapper mg="0 25px">
              <NavRowButtons theme="light" />
            </FlexWrapper>
            <FlexWrapper mg="0 25px">
              <Null />
              {/* <PrimaryButton
              className={"light disable"}
              onClick={() => activateModal("repeat_events")}
              disabled
            >
              <i className="far fa-copy"></i> Repeat Events in other years
            </PrimaryButton> */}
              <PrimaryButton
                className={
                  (start && end) ||
                  props[active]['level_one_date'] ||
                  props[active]['level_two_date']
                    ? 'light'
                    : 'light disable'
                }
                onClick={() => activateModal('events', { eventMinDate, eventMaxDate })}
                disabled={
                  (start && end) ||
                  props[active]['level_one_date'] ||
                  props[active]['level_two_date']
                    ? false
                    : true
                }
              >
                <i className="fas fa-plus"></i> <Trans i18nKey={'program_calendar.events'}></Trans>
              </PrimaryButton>
            </FlexWrapper>
            <LevelField
              eventsState={eventsState}
              programId={p_id}
              levelStartDate={eventMinDate}
              levelEndDate={eventMaxDate}
            />
          </Fragment>
        ) : (
          <Null />
        )}
      </Fragment>
    </Fragment>
  );
};

SetCurriculum.propTypes = {
  id: PropTypes.string,
  year1: PropTypes.object,
  year2: PropTypes.object,
  year3: PropTypes.object,
  year4: PropTypes.object,
  year5: PropTypes.object,
  year6: PropTypes.object,
  apiCalled: PropTypes.bool,
  commonApiCall: PropTypes.func,
  changeTitle: PropTypes.func,
  activateModal: PropTypes.func,
  isLoading: PropTypes.bool,
  _calender_id: PropTypes.string,
  token: PropTypes.string,
  getData: PropTypes.func,
  institutionEventsGet: PropTypes.func,
  programId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.object,
  changeTerm: PropTypes.func,
  changeYear: PropTypes.func,
};

const mapStateToProps = function (state) {
  //({ calender, auth }) => ({
  const { calender, auth } = state;
  return {
    _calender_id: calender.program_calender_id,
    apiCalled: calender.commonApiCalled,
    year1: calender.year1,
    year2: calender.year2,
    year3: calender.year3,
    year4: calender.year4,
    year5: calender.year5,
    year6: calender.year6,
    isLoading: calender.isLoading,
    isAuthenticated: auth.token !== null,
    index: calender.acad_index,
    total: calender.total_academic_year_inDB,
    id: calender.institution_Calender_Id,
    programId: calender.programId,
    token: auth.token !== null ? auth.token.replace(/"/g, '') : null,
    userRole: auth.loggedInUserData.role,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, {
  changeTitle,
  activateModal,
  getData,
  commonApiCall,
  dashboardReset,
  institutionEventsGet,
  changeTerm,
  changeYear,
  // getLevelNumbers,
})(SetCurriculum);
