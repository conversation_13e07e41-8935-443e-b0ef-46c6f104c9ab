import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Map, List } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { Button } from '@mui/material';
import { ThemeProvider } from '@mui/styles';

import CourseTabs from './CourseTabs';
import CourseInfo from './CourseInfo';
import CourseSchedule from './CourseSchedule';
import CourseTimeTable from './CourseTimeTable';
import ManageCourse from './ManageCourse';
import ExportScheduleModal from '../../modal/ExportScheduleModal';
import AlertConfirmModal from '../../modal/AlertConfirmModal';
import { exportCourseScheduleAsPDF, getFormattedGroupName } from '../utils';
import * as actions from '../../../../_reduxapi/course_scheduling/action';
import {
  selectActiveScheduleView,
  selectActiveSessionFlow,
  selectLoading,
  selectPaginationMetaData,
  selectScheduleAvailability,
  selectActiveTab,
  selectTimeTableData,
  selectCourseSchedule,
  selectAdvancedSettings,
  selectMergeSchedule,
  selectStudentGroupsWithStudents,
  selectIndividualCourseDetails,
  selectCurrentMainView,
} from '../../../../_reduxapi/course_scheduling/selectors';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import { getURLParams, removeURLParams, MUI_THEME, isIndGroup, getLang } from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import '../../css/courseSchedule.css';
import { t } from 'i18next';
import withCalendarHooks from 'Hoc/withCalendarHooks';
// import { CustomBreadcrumbs } from '../QuickSchedule/utils';

const lang = getLang();

const initialExportModalData = Map({
  show: false,
  data: Map({
    sessionTypes: 'regular',
    exportType: 'list',
    studentGroups: List(),
  }),
});

class Course extends Component {
  constructor() {
    super();
    this.state = {
      programId: getURLParams('programId', true),
      programName: getURLParams('programName', true),
      courseId: getURLParams('courseId', true),
      term: getURLParams('term', true),
      level: getURLParams('level', true),
      exportModalData: initialExportModalData,
      modalData: {
        show: false,
      },
    };
    this.fetchCourseSchedule = this.fetchCourseSchedule.bind(this);
    this.handleShowHideExportModal = this.handleShowHideExportModal.bind(this);
    this.handleExportDataChange = this.handleExportDataChange.bind(this);
    this.handleExport = this.handleExport.bind(this);
    this.generatePDF = this.generatePDF.bind(this);
  }

  componentDidMount() {
    this.fetchCourseSchedule({ page: 1, pageSize: 10 });
    this.checkPermission();
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.activeInstitutionCalendar.get('_id') !==
      prevProps.activeInstitutionCalendar.get('_id')
    ) {
      this.props.setData(
        Map({
          courseSchedule: Map(),
          paginationMetaData: Map({
            totalPages: null,
            currentPage: null,
          }),
        })
      );
      this.fetchCourseSchedule({ page: 1, pageSize: 10 });
      this.checkPermission();
    }
  }

  checkPermission = () => {
    const { setData } = this.props;
    if (getURLParams('courseId', true) !== '') {
      if (
        CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Course Schedule',
          'View'
        )
      ) {
        setData(Map({ activeTab: 'Schedule', activeScheduleView: 'list' }));
      } else if (
        CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Course TimeTable',
          'View'
        )
      ) {
        setData(Map({ activeTab: 'TimeTable' }));
      } else if (
        CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Manage Course',
          'View'
        )
      ) {
        setData(
          Map({
            activeTab: 'Manage Course',
            activeSettingsView: 'list',
            paginationMetaData: Map({
              totalPages: null,
              currentPage: null,
            }),
          })
        );
      }
    }
  };

  fetchCourseSchedule({ page = 1, pageSize = 10, isRefresh = false, type = 'regular' }) {
    const { programId, courseId, term, level } = this.state;
    const { activeInstitutionCalendar } = this.props;
    if (!activeInstitutionCalendar.isEmpty()) {
      this.props.getCourseSchedule({
        programId,
        courseId,
        term,
        institutionCalendarId: activeInstitutionCalendar.get('_id'),
        level,
        page,
        pageSize,
        isRefresh,
        type,
      });
    }
  }

  handleShowHideExportModal(show) {
    const { currentMainView } = this.props;
    const type =
      currentMainView === 'regular'
        ? 'regular'
        : currentMainView === 'support'
        ? 'support_session'
        : 'event';
    let exportModalData = initialExportModalData
      .set('show', show)
      .setIn(['data', 'sessionTypes'], type);
    this.setState({ exportModalData });
  }

  handleExportDataChange(name, value) {
    this.setState((state) => {
      return {
        exportModalData: state.exportModalData.setIn(['data', name], value),
      };
    });
  }

  getStudentGroups() {
    return this.props.courseSchedule
      .getIn(['session_flow', 0, 'student_group'], List())
      .map((studentGroup) =>
        Map({
          _id: studentGroup.get('_id'),
          name: getFormattedGroupName(
            studentGroup.get('group_name', ''),
            isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
          ),
        })
      )
      .toList();
  }

  handleExport() {
    const { exportModalData } = this.state;
    const sessionTypes = exportModalData.getIn(['data', 'sessionTypes'], '');
    const exportType = exportModalData.getIn(['data', 'exportType'], '');
    const studentGroups = exportModalData.getIn(['data', 'studentGroups'], List()).toJS();
    if (!sessionTypes) {
      return this.props.setData(Map({ message: 'Please select atleast one session type' }));
    }
    if (!exportType) {
      return this.props.setData(Map({ message: 'Please select an export type' }));
    }
    if (!studentGroups.length && sessionTypes === 'regular') {
      return this.props.setData(
        Map({ message: t('course_schedule.validation.please_select_atleast_one_student_group') })
      );
    }
    const { courseSchedule } = this.props;
    const studentGroupId = courseSchedule.getIn(['session_flow', 0, '_student_group_id'], '');
    if (!studentGroupId && sessionTypes === 'regular') {
      return this.props.setData(Map({ message: 'No data to export' }));
    }
    this.props.getCourseScheduleExport({
      institutionCalendarId: this.props.activeInstitutionCalendar.get('_id', ''),
      courseId: courseSchedule.get('_course_id'),
      studentGroupId,
      exportType,
      groupId: studentGroups,
      type: sessionTypes,
      term: courseSchedule.get('term'),
      callback: this.generatePDF,
    });
  }

  generatePDF(exportData) {
    const studentGroupIds = this.state.exportModalData.getIn(['data', 'studentGroups'], List());
    this.handleShowHideExportModal(false);
    const { activeInstitutionCalendar, courseSchedule } = this.props;
    const academicYear = activeInstitutionCalendar.get('calendar_name', '');
    exportCourseScheduleAsPDF(
      exportData
        .set('course', courseSchedule)
        .set(
          'student_groups',
          courseSchedule
            .getIn(['session_flow', 0, 'student_group'], List())
            .reduce(
              (acc, studentGroup) =>
                studentGroupIds.includes(studentGroup.get('_id'))
                  ? acc.push(
                      getFormattedGroupName(
                        studentGroup.get('group_name'),
                        isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                      )
                    )
                  : acc,
              List()
            )
            .sort((a, b) => {
              const v1 = String(a);
              const v2 = String(b);
              return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(
                v1,
                v2
              );
            })
        )
        .setIn(['courseDetails', 'academicYear'], academicYear)
        .setIn(['courseDetails', 'program_name'], getURLParams('programName', true))
    );
  }

  publish(isConfirmed) {
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: t('course_schedule.confirm_publish'),
        body: <div>{t('course_schedule.confirm_publish_desc')}</div>,
        variant: 'confirm',
        data: { data: {}, operation: 'publish' },
        confirmButtonLabel: t('publish'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    const { programId, term, level } = this.state;
    this.props.publishCourseSchedule({
      inst_cal_id: this.props.activeInstitutionCalendar.get('_id', ''),
      program_id: programId,
      _course_id: this.props.courseSchedule.get('_course_id'),
      term: term,
      level: level,
    });
  }

  setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(titleIcon && { titleIcon }),
        ...(body && { body }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ data, operation }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => {
        switch (operation) {
          case 'publish': {
            this.publish(true);
            return;
          }
          default:
            return;
        }
      }
    );
  }

  getIsAllowedToSchedule() {
    const { courseSchedule } = this.props;
    const isShared = courseSchedule.get('isShared');
    if (!isShared) return true;
    return Boolean(courseSchedule.get('isAllowedToSchedule'));
  }

  render() {
    const {
      history,
      location,
      courseSchedule,
      activeScheduleView,
      activeSessionFlow,
      activeInstitutionCalendar,
      activeTab,
      setData,
      getTimeTableData,
      timeTableData,
      individualCourseDetails,
      getIndividualCourseList,
      fetchCourseList,
      isCurrentCalendar,
      // breadcrumbList,
    } = this.props;
    const {
      programId,
      courseId,
      term,
      level,
      exportModalData,
      modalData,
      programName,
    } = this.state;
    const isAllowedToSchedule = this.getIsAllowedToSchedule();
    const currentCalendar = isCurrentCalendar();
    return (
      <div className="main bg-gray pb-5">
        <div className="bg-white">
          <div className="container-fluid">
            <div className="row align-items-center pt-2">
              <div className="col-md-12 col-xl-3 col-12 col-lg-12 mb-2">
                <div className="font-weight-bold f-17 d-flex">
                  <p className="mb-0">
                    <i
                      className={`fa cursor-pointer mr-3 ${
                        lang !== 'ar' ? 'fa-arrow-left ' : 'fa-arrow-right'
                      } `}
                      aria-hidden="true"
                      onClick={() => {
                        fetchCourseList();
                        setData(Map({ activeTab: 'Schedule' }));
                        history.replace(
                          `/course-scheduling/schedule/list/${removeURLParams(location, [
                            'courseId',
                            'courseName',
                            'versionName',
                          ])}`
                        );
                      }}
                    ></i>
                  </p>
                  <p className="mb-0">
                    {getURLParams('courseName', true)}
                    {getURLParams('versionName', true)}
                  </p>
                  {/* <div>
                    <CustomBreadcrumbs breadcrumbList={breadcrumbList} />
                    <Typography fontSize={20} fontWeight={500} color="#374151">
                      Course Scheduling
                    </Typography>
                  </div> */}
                </div>
              </div>
              <div className="col-md-8 col-xl-6 col-lg-8 col-8">
                <CourseTabs
                  activeTab={activeTab}
                  setData={setData}
                  isAllowedToSchedule={isAllowedToSchedule}
                  clicked={this.fetchCourseSchedule}
                />
              </div>
              <div className="col-md-4 col-xl-3 col-lg-4 col-4">
                {activeTab === 'Schedule' && (
                  <ThemeProvider theme={MUI_THEME}>
                    <div className="d-flex justify-content-end">
                      {CheckPermission(
                        'tabs',
                        'Schedule Management',
                        'Course Scheduling',
                        '',
                        'Schedule',
                        'Export'
                      ) && (
                        <div className="mr-2">
                          <Button
                            variant="outlined"
                            color="primary"
                            onClick={() => this.handleShowHideExportModal(true)}
                            startIcon={<i className="fas fa-download" aria-hidden="true"></i>}
                          >
                            {t('role_management.role_actions.Export')}
                          </Button>
                        </div>
                      )}
                      {currentCalendar &&
                        isAllowedToSchedule &&
                        CheckPermission(
                          'tabs',
                          'Schedule Management',
                          'Course Scheduling',
                          '',
                          'Schedule',
                          'Publish'
                        ) && (
                          <div>
                            <Button
                              variant="contained"
                              color="primary"
                              startIcon={<i className="fa fa-globe" aria-hidden="true"></i>}
                              onClick={() => this.publish(false)}
                              disabled={getURLParams('scheduledStatus', true) !== 'true'}
                            >
                              {t('role_management.role_actions.Publish')}
                            </Button>
                          </div>
                        )}
                    </div>
                  </ThemeProvider>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="bg-gray">
          <div className="container-fluid">
            <div className="row pb-4">
              <div className="col-md-12">
                {activeTab === 'Schedule' ? (
                  <div className="p-2">
                    <CourseInfo
                      course={courseSchedule}
                      isAllowedToSchedule={isAllowedToSchedule}
                      setData={setData}
                      activeScheduleView={activeScheduleView}
                      studentGroupStatus={getURLParams('studentGroupStatus', true) === 'true'}
                      currentCalendar={currentCalendar}
                      programId={programId}
                      callback={() => this.fetchCourseSchedule({ page: 1, pageSize: 10 })}
                    />
                    <CourseSchedule
                      activeScheduleView={activeScheduleView}
                      course={courseSchedule}
                      setData={setData}
                      activeSessionFlow={activeSessionFlow}
                      institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
                      saveSchedule={this.props.saveSchedule}
                      getScheduleAvailability={this.props.getScheduleAvailability}
                      scheduleAvailability={this.props.scheduleAvailability}
                      fetchCourseSchedule={this.fetchCourseSchedule}
                      paginationMetaData={this.props.paginationMetaData}
                      loading={this.props.loading}
                      programName={programName}
                      getAdvancedSettings={this.props.getAdvancedSettings}
                      saveAdvanceSettings={this.props.saveAdvanceSetting}
                      advancedSettings={this.props.advancedSettings}
                      cancelSchedule={this.props.cancelSchedule}
                      mergeSchedule={this.props.mergeSchedule}
                      getMergeSchedule={this.props.getMergeSchedule}
                      saveMergeSchedule={this.props.saveMergeSchedule}
                      studentGroupsWithStudents={this.props.studentGroupsWithStudents}
                      getStudentGroupsWithStudents={this.props.getStudentGroupsWithStudents}
                      saveStudentGroupsWithStudents={this.props.saveStudentGroupsWithStudents}
                      saveSupportAndEventsSchedule={this.props.saveSupportAndEventsSchedule}
                      updateMissedSession={this.props.updateMissedSession}
                    />
                  </div>
                ) : activeTab === 'TimeTable' ? (
                  <CourseTimeTable
                    timeTableFor="course"
                    course={courseSchedule}
                    courseDetails={individualCourseDetails}
                    programId={programId}
                    courseId={courseId}
                    term={term}
                    level={level}
                    institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
                    getTimeTableData={getTimeTableData}
                    timeTableData={timeTableData}
                    setData={setData}
                    academicYear={activeInstitutionCalendar.get('calendar_name', '')}
                    programName={getURLParams('programName', true)}
                    getIndividualCourseList={getIndividualCourseList}
                  />
                ) : (
                  <ManageCourse
                    course={courseSchedule}
                    activeSessionFlow={activeSessionFlow}
                    programId={programId}
                    courseId={courseId}
                    term={term}
                    level={level}
                    institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
                    programName={programName}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        <ExportScheduleModal
          show={exportModalData.get('show')}
          onHide={this.handleShowHideExportModal}
          onChange={this.handleExportDataChange}
          data={exportModalData.get('data')}
          studentGroups={this.getStudentGroups()}
          onExport={this.handleExport}
        />
        <AlertConfirmModal
          show={modalData.show}
          title={modalData.title || ''}
          titleIcon={modalData.titleIcon}
          body={modalData.body || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />
      </div>
    );
  }
}

Course.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  setData: PropTypes.func,
  activeScheduleView: PropTypes.string,
  courseSchedule: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getCourseSchedule: PropTypes.func,
  activeSessionFlow: PropTypes.instanceOf(Map),
  saveSchedule: PropTypes.func,
  scheduleAvailability: PropTypes.instanceOf(Map),
  getScheduleAvailability: PropTypes.func,
  paginationMetaData: PropTypes.instanceOf(Map),
  loading: PropTypes.instanceOf(Map),
  activeTab: PropTypes.string,
  timeTableData: PropTypes.instanceOf(Map),
  getTimeTableData: PropTypes.func,
  courseScheduleExport: PropTypes.instanceOf(Map),
  getCourseScheduleExport: PropTypes.func,
  publishCourseSchedule: PropTypes.func,
  advancedSettings: PropTypes.instanceOf(Map),
  getAdvancedSettings: PropTypes.func,
  saveAdvanceSetting: PropTypes.func,
  cancelSchedule: PropTypes.func,
  getMergeSchedule: PropTypes.func,
  mergeSchedule: PropTypes.instanceOf(Map),
  saveMergeSchedule: PropTypes.func,
  studentGroupsWithStudents: PropTypes.instanceOf(List),
  getStudentGroupsWithStudents: PropTypes.func,
  saveStudentGroupsWithStudents: PropTypes.func,
  saveSupportAndEventsSchedule: PropTypes.func,
  individualCourseDetails: PropTypes.instanceOf(Map),
  currentMainView: PropTypes.string,
  getIndividualCourseList: PropTypes.func,
  fetchCourseList: PropTypes.func,
  isCurrentCalendar: PropTypes.func,
  updateMissedSession: PropTypes.func,
  breadcrumbList: PropTypes.array,
};

const mapStateToProps = (state) => {
  return {
    activeScheduleView: selectActiveScheduleView(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    courseSchedule: selectCourseSchedule(state),
    activeSessionFlow: selectActiveSessionFlow(state),
    scheduleAvailability: selectScheduleAvailability(state),
    paginationMetaData: selectPaginationMetaData(state),
    loading: selectLoading(state),
    activeTab: selectActiveTab(state),
    timeTableData: selectTimeTableData(state),
    advancedSettings: selectAdvancedSettings(state),
    mergeSchedule: selectMergeSchedule(state),
    studentGroupsWithStudents: selectStudentGroupsWithStudents(state),
    individualCourseDetails: selectIndividualCourseDetails(state),
    currentMainView: selectCurrentMainView(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(withCalendarHooks(Course));
