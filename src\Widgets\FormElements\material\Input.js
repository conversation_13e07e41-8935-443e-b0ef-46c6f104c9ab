/* eslint-disable */
import React from 'react';
import PropTypes from 'prop-types';
import TextField from '@mui/material/TextField';
// import TextareaAutosize from '@mui/joy/Textarea';
//import TextareaAutosize from '@mui/base/TextareaAutosize';
import Select from '@mui/material/Select';
import FormControl from '@mui/material/FormControl';
import 'react-datepicker/dist/react-datepicker.css';
import InputBase from '@mui/material/InputBase';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Checkbox from '@mui/material/Checkbox';
import Radio from '@mui/material/Radio';
import { useTheme } from '@mui/material/styles';
import { makeStyles } from '@mui/styles';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import { getLang } from 'utils';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import InputLabel from '@mui/material/InputLabel';
import moment from 'moment';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';

const Input = (props) => {
  const {
    helperText,
    elementType,
    type,
    placeholder,
    variant,
    size,
    id,
    value,
    name,
    changed,
    codeChanged,
    inputProps,
    elementConfig,
    labelclass,
    label,
    feedback,
    correction,
    maxRows,
    minRows,
    defaultValue,
    defaultCode,
    disabled,
    inputAdornment,
    maxLength,
    startAdornment,
    title,
    error,
    defaultErrorMessage,
    isValid,
    disableDropdown,
    errorShow,
    minDate,
    maxDate,
    isAllSelected,
    multiple,
    renderValue,
    displayEmpty,
    iconType = 'checkBox',
    bgWhite,
    displayName,
    addClass = '',
    isCalendarList = false,
    autoWidth,
    handleClear,
    removeIcon,
    sx,
    inputRef,
    MenuProps,
    menuItemStyles,
    className,
    selectiveError,
    stopPropagation = false,
  } = props;

  const BootstrapInput = styled(InputBase)(({ theme }) => ({
    'label + &': {
      marginTop: theme.spacing(3),
    },
    '& .MuiInputBase-input': {
      borderRadius: 4,
      position: 'relative',
      backgroundColor: theme.palette.background.paper,
      border: '1px solid #ced4da',
      fontSize: 16,
      padding: '10px 26px 10px 12px',
      transition: theme.transitions.create(['border-color', 'box-shadow']),
      // Use the system font instead of the default Roboto font.
      fontFamily: [
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        'Roboto',
        '"Helvetica Neue"',
        'Arial',
        'sans-serif',
        '"Apple Color Emoji"',
        '"Segoe UI Emoji"',
        '"Segoe UI Symbol"',
      ].join(','),
      '&:focus': {
        borderRadius: 4,
        borderColor: '#80bdff',
        boxShadow: '0 0 0 0.2rem rgba(0,123,255,.25)',
      },
    },
  }));

  let inputElement = null;
  const theme = useTheme();
  const blockInvalidChar = (e, type) =>
    type === 'number' && ['e', 'E', '+', '-'].includes(e.key) && e.preventDefault();
  const useStylesFunction = makeStyles(() => ({
    root: {
      padding: '2px 4px',
      display: 'flex',
      alignItems: 'center',
      width: 'auto',
      boxShadow: 'none',
      border: '1px solid #c4c4c4',
      borderRadius: '6px',
      maxWidth: 'none !important',
    },
    inputFocus: {
      '& .MuiOutlinedInput-root': {
        '&.Mui-focused fieldset': {
          borderColor: '#147AFC',
        },
      },
      textAlign: 'center',
    },
    input: {
      marginLeft: theme.spacing(1),
      flex: 1,
    },
    iconButton: {
      padding: 6,
      pointerEvents: 'none',
    },
    divider: {
      height: 28,
      margin: 4,
    },
    radious: {
      borderRadius: '20px',
    },
    inputRoot: {
      '&$disabled': {
        background: '#f1f1f1',
      },
      direction: getLang() === 'ar' ? 'rtl' : 'ltr',
    },
    disabled: {},
    iconPosition: {
      position: `${getLang() === 'ar' ? 'initial' : 'absolute'}`,
    },
    selectIcon: {
      position: getLang() === 'ar' ? 'initial' : 'absolute',
    },
    direction: {
      direction: getLang() === 'ar' ? 'rtl' : 'ltr',
    },
    selectRoot: {
      '& .MuiInputBase-root': {
        '&.Mui-focused fieldset': {
          borderColor: '#147AFC',
        },
      },
      ...(autoWidth && {
        '&.MuiFormControl-root': {
          width: 'fit-content',
          maxWidth: 'fit-content',
          minWidth: '100%',
        },
      }),
    },
    BorderTransparent: {
      borderColor: 'transparent',
    },
    txtCenter: {
      textAlign: 'center',
    },
    formControl: {
      margin: theme.spacing(1),
      width: 300,
    },
    indeterminateColor: {
      color: '#f50057',
    },
    selectAllText: {
      fontWeight: 500,
    },
    selectedAll: {
      backgroundColor: 'rgba(0, 0, 0, 0.08)',
      '&:hover': {
        backgroundColor: 'rgba(0, 0, 0, 0.08)',
      },
    },
  }));

  const classes = useStylesFunction();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuPropsMulti = {
    // style: { position: 'absolute' },
    disableScrollLock: true,
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
    getContentAnchorEl: null,
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'center',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'center',
    },
    variant: 'menu',
  };
  const MenuPropsSingle = {
    // style: { position: 'absolute' },
    disableScrollLock: true,
  };

  switch (elementType) {
    case 'materialInput':
      inputElement = (
        <TextField
          onKeyDown={(e) => {
            stopPropagation && e.stopPropagation();
            blockInvalidChar(e, type);
          }}
          type={type}
          placeholder={placeholder}
          variant={variant}
          size={size}
          fullWidth
          id={id}
          value={value ? value : ''}
          name={name}
          onChange={changed}
          defaultValue={defaultValue}
          inputProps={{
            ...inputProps,
            ...(type === 'number' && { min: 0, max: 99999, step: 1 }),
            ...(type === 'date' && { min: minDate, max: maxDate }),
            maxLength: maxLength,
          }}
          helperText={helperText}
          {...elementConfig}
          className={`${classes.inputFocus} ${bgWhite && 'bg-white'}`}
          disabled={disabled}
          InputProps={{
            classes: {
              root: classes.inputRoot,
              disabled: classes.disabled,
            },
            endAdornment: inputAdornment,
            startAdornment: startAdornment,
          }}
          error={error}
          inputRef={inputRef}
          sx={sx}
        />
      );
      break;
    case 'materialInputTransparent':
      inputElement = (
        <TextField
          type={type}
          placeholder={placeholder}
          variant={variant}
          size={size}
          fullWidth
          id={id}
          value={value}
          name={name}
          onChange={changed}
          defaultValue={defaultValue}
          {...elementConfig}
          className={classes.inputFocus}
          disabled={disabled}
          InputProps={{
            classes: {
              root: classes.inputRoot,
              disabled: classes.disabled,
              notchedOutline: classes.BorderTransparent,
              input: classes.txtCenter,
            },
          }}
          error={error}
        />
      );
      break;
    case 'materialSelect':
      inputElement = (
        <FormControl
          fullWidth
          variant={variant}
          size={size}
          className={classes.selectRoot}
          error={errorShow}
        >
          <Select
            labelId="term-label"
            native
            value={value ? value : ''}
            onChange={changed}
            name={name}
            id={id}
            disabled={disabled}
            title={title}
            classes={{
              icon: classes.iconPosition,
            }}
            sx={sx}
            defaultValue={defaultValue}
            renderValue={renderValue}
          >
            {placeholder && (
              <option disabled value="">
                {placeholder}
              </option>
            )}
            {elementConfig.options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.name}
              </option>
            ))}
          </Select>
        </FormControl>
      );
      break;

    case 'materialSearch':
      inputElement = (
        <div className="materialSearch">
          <Paper component="form" className={`${classes.root} ${addClass}`}>
            {labelclass === 'searchLeft' && (
              <IconButton className={classes.iconButton} aria-label="search">
                <SearchIcon />
              </IconButton>
            )}
            <InputBase
              className={classes.input}
              placeholder={placeholder}
              inputProps={{ 'aria-label': 'search google maps' }}
              onChange={changed}
              value={value}
              maxLength={maxLength}
              labelclass={labelclass}
            />
            {labelclass !== 'searchLeft' && (
              <>
                {removeIcon ? (
                  <>
                    {value ? (
                      <IconButton sx={{ padding: '6px' }} onClick={handleClear}>
                        <CloseRoundedIcon />
                      </IconButton>
                    ) : (
                      <IconButton className={classes.iconButton} aria-label="search">
                        <SearchIcon />
                      </IconButton>
                    )}
                  </>
                ) : (
                  <IconButton className={classes.iconButton} aria-label="search">
                    <SearchIcon />
                  </IconButton>
                )}
              </>
            )}
          </Paper>
        </div>
      );

      break;

    case 'materialTextArea':
      inputElement = (
        <div className="w-100">
          <TextField
            multiline
            maxRows={maxRows}
            minRows={minRows}
            placeholder={placeholder}
            defaultValue={defaultValue}
            value={value}
            name={name}
            inputProps={inputProps}
            {...elementConfig}
            onChange={changed}
            id={id}
            disabled={disabled}
            className={` ${bgWhite ? 'bg-white w-100' : 'custom-multi-select'}`}
            inputRef={inputRef}
            sx={sx}
          />
        </div>
      );
      break;
    case 'materialTelephoneNumber':
      inputElement = (
        <div className="input-group">
          <div className="input-group-prepend">
            <select
              disabled={disabled}
              onChange={codeChanged}
              className={`custom-select ${errorShow && 'input-section-border-red'}`}
              id="inputGroupSelect01"
              value={defaultCode}
            >
              {elementConfig.options.map((option) => (
                <option key={option.value} value={option.value} className="custom-option">
                  {option.name}
                </option>
              ))}
            </select>
          </div>
          <input
            type="number"
            className={`form-control ${errorShow && 'input-section-border-red'}`}
            placeholder={placeholder}
            id="validationCustom01"
            onChange={changed}
            defaultValue={defaultValue}
            value={value}
          />
        </div>
      );
      break;
    case 'materialSelectNew':
      inputElement = (
        <FormControl
          fullWidth
          variant={variant}
          size={size}
          className={classes.selectRoot}
          error={errorShow}
        >
          <Select
            id={id}
            input={<BootstrapInput />}
            title={title}
            value={value}
            onChange={changed}
            name={name}
            disabled={disabled}
            MenuProps={MenuPropsSingle}
            renderValue={renderValue}
            displayEmpty={displayEmpty}
            defaultValue={defaultValue}
          >
            {placeholder && (
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
            )}
            {displayName !== undefined && displayName !== '' && (
              <MenuItem disabled value="">
                {displayName}
              </MenuItem>
            )}
            {elementConfig.options.map((option, index) => {
              let endDate, currentDate;
              if (option.isActive === true) {
                endDate = Date.parse(moment(option.end_date).format('YYYY-MM-DD'));
                currentDate = Date.parse(moment().format('YYYY-MM-DD'));
              }

              return (
                <MenuItem
                  key={index}
                  disabled={option?.disabled !== undefined ? option.disabled : false}
                  value={option.value}
                >
                  <div className="d-flex justify-content-between w-100">
                    <div>{option.name}</div>
                    {isCalendarList &&
                      (option.isActive ? (
                        endDate < currentDate ? (
                          <div className="menu-ri text-success">Completed</div>
                        ) : (
                          <div className="menu-ri text-primary">On-going</div>
                        )
                      ) : (
                        <div className="menu-ri text-grey">Inactive</div>
                      ))}
                  </div>
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
      );
      break;
    case 'MuiSelect':
      inputElement = (
        <FormControl fullWidth size={size}>
          <Select
            title={title}
            value={value}
            onChange={changed}
            disabled={disabled}
            className={className}
            MenuProps={MenuProps}
            renderValue={renderValue}
            displayEmpty={displayEmpty}
            error={selectiveError}
          >
            {placeholder && (
              <MenuItem value="" disabled hidden>
                {placeholder}
              </MenuItem>
            )}
            {elementConfig.map((option) => {
              const value = option.get('value', '');
              const name = option.get('name', '');
              return (
                <MenuItem key={value} value={value} sx={menuItemStyles}>
                  {name}
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
      );
      break;
    case 'materialSelectMultiple':
      inputElement = (
        <FormControl
          fullWidth
          variant={variant}
          size={size}
          className={classes.selectRoot}
          error={errorShow}
        >
          {placeholder && !value.length && <InputLabel shrink={false}>{placeholder}</InputLabel>}
          <Select
            id={id}
            labelId="mutiple-select-label"
            multiple={multiple}
            disabled={disabled}
            value={value}
            onChange={changed}
            renderValue={(selected) => {
              if (displayName !== undefined && displayName !== '' && selected.length === 0) {
                return displayName;
              }
              return elementConfig?.options
                .reduce((acc, o) => (selected.includes(o.value) ? [...acc, o.name] : acc), [])
                .join(', ');
            }}
            MenuProps={MenuPropsMulti}
            displayEmpty={displayEmpty !== undefined ? displayEmpty : false}
          >
            {/* <MenuItem
                value="all"
                classes={{
                  root: isAllSelected ? classesMultiple.selectedAll : '',
                }}
              >
                <ListItemIcon>
                  <Checkbox
                    classes={{ indeterminate: classesMultiple.indeterminateColor }}
                    checked={isAllSelected}
                    indeterminate={value.length > 0 && value.length < elementConfig.options.length}
                  />
                </ListItemIcon>
                <ListItemText
                  classes={{ primary: classesMultiple.selectAllText }}
                  primary="Select All"
                  color="primary"
                  variant="primary"
                />
              </MenuItem> */}
            {displayName !== undefined && displayName !== '' && (
              <MenuItem disabled value="">
                {displayName}
              </MenuItem>
            )}
            {elementConfig?.options.map((option, index) => (
              <MenuItem key={index} value={option.value}>
                <ListItemIcon>
                  {iconType === 'radio' ? (
                    <Radio color="primary" checked={value.indexOf(option.value) > -1} />
                  ) : (
                    <Checkbox color="primary" checked={value.indexOf(option.value) > -1} />
                  )}
                </ListItemIcon>
                <ListItemText primary={option.name} />
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      );
      break;

    case 'materialTimePicker':
      inputElement = (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <TimePicker
            renderInput={(params) => (
              <TextField
                fullWidth={true}
                {...params}
                sx={{
                  '& .MuiOutlinedInput-input': {
                    padding: '9.5px 14px !important',
                  },
                }}
                inputProps={{
                  ...params.inputProps,
                  placeholder: { placeholder },
                }}
              />
            )}
            value={value}
            onChange={changed}
          />
        </LocalizationProvider>
      );
      break;

    default:
  }

  return (
    <div className="form-group">
      {label && (
        <label className={`form-label ${labelclass} ${getLang() === 'ar' && 'float-left'}`}>
          {label}
        </label>
      )}
      {inputElement}
      <div className="InvalidFeedback">{feedback}</div>
      <div className={`${errorShow ? 'helper-text-error' : 'f-14 error'}`}>{correction}</div>
    </div>
  );
};

Input.propTypes = {
  elementType: PropTypes.string,
  type: PropTypes.string,
  placeholder: PropTypes.string,
  variant: PropTypes.string,
  size: PropTypes.string,
  id: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.array]),
  name: PropTypes.string,
  helperText: PropTypes.string,
  changed: PropTypes.func,
  codeChanged: PropTypes.func,
  elementConfig: PropTypes.object,
  inputProps: PropTypes.object,
  labelclass: PropTypes.string,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  correction: PropTypes.string,
  feedback: PropTypes.string,
  maxRows: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  minRows: PropTypes.string,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  defaultCode: PropTypes.string,
  inputAdornment: PropTypes.oneOfType([PropTypes.object, PropTypes.bool, PropTypes.string]),
  maxLength: PropTypes.number,
  startAdornment: PropTypes.object,
  title: PropTypes.string,
  defaultErrorMessage: PropTypes.string,
  isValid: PropTypes.bool,
  disableDropdown: PropTypes.bool,
  autoWidth: PropTypes.bool,
  minDate: PropTypes.string,
  maxDate: PropTypes.string,
  isAllSelected: PropTypes.bool,
  multiple: PropTypes.bool,
  disabled: PropTypes.bool,
  displayEmpty: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  iconType: PropTypes.string,
  displayName: PropTypes.string,
  handleClear: PropTypes.func,
  sx: PropTypes.object,
  MenuProps: PropTypes.object,
  menuItemStyles: PropTypes.object,
  className: PropTypes.string,
  selectiveError: PropTypes.bool,
  stopPropagation: PropTypes.bool,
};

export default Input;
