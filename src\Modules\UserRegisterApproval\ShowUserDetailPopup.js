import React, { useEffect, useMemo, useState } from 'react';
import axios from '../../axios';

import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import TextField from '@mui/material/TextField';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import { getProgramName } from './Functions';
import ReusableSnackbar, { ShowParticularUserDetail } from './Components';
import LocalStorageService from 'LocalStorageService';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { useSelector } from 'react-redux';

const ImportantStar = () => {
  return <span className="text-danger">{`  *`}</span>;
};
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(2),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

export const responsiveFontSizeHeading = {
  fontSize: {
    xs: '12px !important',
    sm: '13px !important',
    md: '14px !important',
    lg: '15px !important',
  },
};

const gap10 = {
  gap: '10px',
};

const radioIconStyle = {
  '& .MuiSvgIcon-root': {
    fontSize: '16px',
  },
};
export default function ShowUserDetail({
  user,
  handleClosePopup,
  open,
  setOpen,
  deleteUser,
  setApproved,
  setRejected,
  setSubmited,
}) {
  const authData = useSelector(selectUserInfo);
  const [reason, setReason] = useState('');
  const [showReasonInput, setShowReasonInput] = useState(false);
  const [rejectStatus, setRejectStatus] = useState(false);

  const [firstName, setFirstName] = useState(user.name.first);
  const [middleName, setMiddleName] = useState(user.name.middle);
  const [lastName, setLastName] = useState(user.name.last);
  const [gender, setGender] = useState(user.gender);
  const [academicID, setAcademicID] = useState(user.user_id);
  const [mobileNumber, setMobileNumber] = useState(user.mobile);
  const [programName, setProgramName] = useState([]);
  const [selectedProgram, setSelectedProgram] = useState(null);
  const [userProgram, setUserProgram] = useState(user.program?._program_id.name);
  const [validateInput, setValidateInput] = useState('');
  const [duplicateValidate, setDuplicateValidate] = useState('');
  const [error, setError] = useState(false);
  const [terms, setTerms] = useState([]);
  const [selectedTerm, setSelectedTerm] = useState(user.batch);

  const numberLength = user.mobile.toString().length;

  const programNameRequired = useMemo(() => {
    return authData.getIn(['services', 'SELF_REGISTRATION_PROGRAM'], '') === 'true';
  }, [authData]);

  const handleChangeNumber = (e) => {
    const inputValue = e.target.value;
    setMobileNumber(inputValue);

    setError(inputValue.length !== Number(numberLength));
  };

  const transformedData = programName.map((item) => ({
    label: item.name,
    ...item,
  }));
  const handleTermChange = (event) => {
    setSelectedTerm(event.target.value);
  };
  const matchedProgram = programName.find((program) => program.name === userProgram);

  const handleProgramChange = (event, value) => {
    if (value) {
      setUserProgram(value.name);

      const transformedProgram = {
        program_no: value.code,
        _program_id: value._id,
      };
      setSelectedProgram(transformedProgram);
      setTerms(value.term);
    } else {
      setSelectedProgram(null);
      setTerms([]);
    }
  };
  useEffect(() => {
    handleProgramChange(event, matchedProgram);
  }, [programName]);

  const handleClose = () => {
    setOpen(false);
  };
  const handleAccept = async (user, callback) => {
    const data = {
      userId: user._id,
      approvalStatus: 'Approved',
    };
    setProgress(true);

    try {
      const response = await axios.post(`userRegistration/DataApprovalResult`, data, {});

      if (response.status === 200) {
        deleteUser(user._id);
        setApproved(true);
        setOpen(false);
      }
    } catch (error) {
      return false;
    }
    setProgress(false);
  };

  const handleReject = async (user) => {
    if (showReasonInput && reason.trim() === '') {
      setRejectStatus(true);
      return;
    }
    const data = {
      userId: user._id,
      approvalStatus: 'Rejected',
      Reason: reason,
    };
    setProgress(true);
    try {
      const response = await axios.post(`userRegistration/DataApprovalResult`, data, {});

      if (response.status === 200) {
        setRejected(true);
        deleteUser(user._id);
        setOpen(false);
      }
    } catch (error) {
      return false;
    }
    setProgress(false);
  };

  const handleChange = (event) => {
    setReason(event.target.value);
  };

  const handleSendReason = () => {
    setShowReasonInput(false);
    handleReject(user);
  };

  const [showEdit, setShowEdit] = useState(false);
  const [progress, setProgress] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const openEdit = async () => {
    setIsLoading(true);
    const storedProgramName = LocalStorageService.getCustomToken('AllProgramName');

    if (storedProgramName) {
      const parsedProgramName = JSON.parse(storedProgramName);
      setProgramName(parsedProgramName);
    } else {
      const AllProgramName = await getProgramName();
      const jsonData = JSON.stringify(AllProgramName);
      LocalStorageService.setCustomToken('AllProgramName', jsonData);
      setProgramName(AllProgramName);
    }
    setIsLoading(false);
    editInfo();
  };

  const saveData = async () => {
    const validation = (input, fieldName) => {
      if (input === '' || input === null) {
        setValidateInput('Required ' + fieldName);

        return false;
      }
      return true;
    };

    if (!validation(firstName, 'first name')) return;
    if (!validation(lastName, 'last name')) return;
    if (!validation(mobileNumber, 'mobile number')) return;
    if (!validation(academicID, 'Academic ID')) return;
    // if (!validation(selectedProgram, 'ProgramName')) return;
    if (programNameRequired && !validation(selectedTerm, 'Batch')) return;

    if (mobileNumber.toString().length !== parseInt(numberLength)) {
      setValidateInput('Invalid Number');
      return false;
    }

    setProgress(true);

    try {
      const response = await axios.post(
        `userRegistration/updateUserDetailInApprovalFlow`,
        {
          userId: user._id,
          name: { first: firstName, last: lastName, middle: middleName },
          gender,
          user_id: academicID,
          mobile: mobileNumber,
          ...(programNameRequired && { program: selectedProgram, term: selectedTerm }),
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      setProgress(false);

      const data = response.data;
      if (data.data.key === 'Data retrieved') {
        editInfo();
        deleteUser(user._id);
        setOpen(false);
        setSubmited(true);
      }
    } catch (error) {
      setProgress(false);
      if (error.response.data.data.errorKey === 'mobileNo') {
        setDuplicateValidate('Mobile Number already registered');
      }
      if (error.response.data.data.errorKey === 'userId') {
        setDuplicateValidate('Academic_ID already registered');
      }
      console.error('Error:', error);
    }
  };

  const editInfo = () => setShowEdit((show) => !show);

  const validInput = validateInput !== '';
  const duplicateValid = duplicateValidate !== '';
  const duplicateValidClose = () => {
    setDuplicateValidate('');
  };
  const ValidateInputClose = () => {
    setValidateInput(null);
  };
  const actionDuplicateValidate = (
    <React.Fragment>
      <IconButton
        size="small"
        aria-label="close"
        color="inherit"
        onClick={() => setDuplicateValidate('')}
      >
        <CloseIcon fontSize="small" />
      </IconButton>
    </React.Fragment>
  );

  const handleChangeGender = (event) => {
    setGender(event.target.value);
  };

  return (
    <BootstrapDialog onClose={handleClose} open={open} fullWidth={true} maxWidth={'sm'}>
      <div>
        {progress && (
          <CircularProgress
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 999,
            }}
          />
        )}
        <div className="pb-4 pt-2 px-3">
          <div className="d-flex justify-content-between align-items-center pb-1">
            <div>
              <div className="d-flex align-items-center" style={gap10}>
                <AccountCircleIcon sx={{ fontSize: 50 }} color="action" />
                <div>
                  <div className="f-15">
                    {user.name.first} {user.name.last}
                  </div>
                  <div className="f-15">{user.email}</div>
                </div>
              </div>
            </div>
            {!showEdit && (
              <div className="mr-5">
                {isLoading ? (
                  <CircularProgress size={24} />
                ) : (
                  <Button variant="outlined" size="small" onClick={openEdit} className="px-2">
                    Edit
                  </Button>
                )}
              </div>
            )}
            <IconButton
              aria-label="close"
              onClick={handleClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
              }}
            >
              <CloseIcon />
            </IconButton>
          </div>
          {!showEdit ? (
            <ShowParticularUserDetail user={user} />
          ) : (
            <div className="px-2 mb-1">
              <Divider />
              <div className="d-flex align-items-center mt-3">
                <div className="w-50 mr-3">
                  <Box sx={responsiveFontSizeHeading} className="">
                    First Name
                    <ImportantStar />
                  </Box>
                  <TextField
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="First Name *"
                    className="input-text"
                    autoFocus
                    size="small"
                    fullWidth
                  />
                </div>
                <div className="w-50 ml-3">
                  <Box sx={responsiveFontSizeHeading} className="">
                    Middle Name
                  </Box>
                  <TextField
                    type="text"
                    value={middleName}
                    onChange={(e) => setMiddleName(e.target.value)}
                    placeholder="Middle Name"
                    className="input-text"
                    size="small"
                    fullWidth
                  />
                </div>
              </div>

              <div className="d-flex align-items-center mt-4">
                <div className="w-50 mr-3">
                  <Box sx={responsiveFontSizeHeading} className="">
                    Last Name
                    <ImportantStar />
                  </Box>
                  <TextField
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Middle Name *"
                    className="input-text"
                    size="small"
                    fullWidth
                  />
                </div>
                <div className="w-50 ml-3">
                  <div className="">
                    <div>
                      <Box sx={responsiveFontSizeHeading}>
                        Gender
                        <ImportantStar />
                      </Box>
                    </div>

                    <RadioGroup
                      className="d-flex "
                      onChange={handleChangeGender}
                      value={gender}
                      row
                    >
                      <FormControlLabel
                        value="male"
                        control={<Radio disableRipple sx={radioIconStyle} className="p-0 mr-2" />}
                        label="Male"
                        className="m-0"
                      />
                      <FormControlLabel
                        value="female"
                        control={
                          <Radio disableRipple sx={radioIconStyle} className="p-0 ml-3 mr-2" />
                        }
                        label="Female"
                        className="m-0"
                      />
                    </RadioGroup>
                  </div>
                </div>
              </div>

              <div className="d-flex align-items-center mt-4">
                <div className="w-50 mr-3">
                  <Box sx={responsiveFontSizeHeading} className="">
                    Academic ID
                    <ImportantStar />
                  </Box>
                  <TextField
                    type="text"
                    value={academicID}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
                      setAcademicID(value);
                    }}
                    placeholder="Academic ID *"
                    className="input-text"
                    size="small"
                    fullWidth
                  />
                </div>
                <div className="w-50 ml-3">
                  <Box sx={responsiveFontSizeHeading} className="">
                    Mobile Number
                    <ImportantStar />
                  </Box>
                  <TextField
                    type="number"
                    value={mobileNumber}
                    onChange={handleChangeNumber}
                    placeholder="Mobile Number *"
                    className="input-text"
                    size="small"
                    fullWidth
                    error={error}
                  />
                </div>
              </div>

              {programNameRequired && (
                <div className="d-flex align-items-center mt-4 mb-4">
                  <div className="w-50 mr-3">
                    <Box sx={responsiveFontSizeHeading} className="">
                      Program Name
                      <ImportantStar />
                    </Box>
                    <Autocomplete
                      disablePortal
                      size="small"
                      id="combo-box-demo"
                      options={transformedData}
                      value={userProgram}
                      onChange={handleProgramChange}
                      clearIcon={null}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          fullWidth
                          label={!userProgram ? 'Select Program' : ''}
                          InputLabelProps={{
                            shrink: false, // Prevent label from shrinking
                          }}
                          sx={{
                            '& .MuiInputBase-root': {
                              padding: '3px', // Adjust padding for smaller input box
                            },
                            '& .MuiInputLabel-root': {
                              fontSize: '0.875rem', // Adjust label size
                            },
                          }}
                        />
                      )}
                    />
                  </div>
                  <div className="w-50 ml-3">
                    <Box sx={responsiveFontSizeHeading}>
                      Batch
                      <ImportantStar />
                    </Box>
                    <FormControl fullWidth>
                      <InputLabel
                        id="demo-simple-select-autowidth-label"
                        shrink={!!selectedTerm}
                        sx={{
                          fontSize: '0.875rem', // Adjust label size
                        }}
                      >
                        {!userProgram ? 'Select Term' : ''}
                      </InputLabel>
                      <Select
                        labelId="demo-simple-select-autowidth-label"
                        id="demo-simple-select-autowidth"
                        value={selectedTerm}
                        onChange={handleTermChange}
                        autoWidth
                        size="small"
                      >
                        {!terms.some((term) => term.term_name === selectedTerm) && (
                          <MenuItem value={selectedTerm}>
                            {selectedProgram === null && (
                              <MenuItem value={selectedTerm}>
                                <em>{selectedTerm}</em>
                              </MenuItem>
                            )}
                          </MenuItem>
                        )}
                        {terms.map((term, index) => (
                          <MenuItem key={index} value={term.term_name}>
                            {term.term_name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                </div>
              )}
              <Divider />
            </div>
          )}
          <div className="d-flex mt-3" style={gap10}>
            <>
              {!showEdit ? (
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => handleAccept(user, handleClosePopup)}
                  className="ml-auto px-2"
                >
                  Accept
                </Button>
              ) : (
                <Button
                  variant="contained"
                  size="small"
                  onClick={saveData}
                  className="ml-auto px-2"
                >
                  SAVE
                </Button>
              )}
            </>
            {!showEdit ? (
              <Button
                variant="outlined"
                size="small"
                onClick={() => setShowReasonInput(true)}
                className="px-2"
              >
                Reject
              </Button>
            ) : (
              <Button variant="outlined" size="small" onClick={editInfo} className=" px-2">
                CLOSE
              </Button>
            )}
          </div>
          <div>
            {showReasonInput && (
              <div className="d-flex mt-3" style={gap10}>
                <TextField
                  size="small"
                  value={reason}
                  onChange={handleChange}
                  placeholder="Enter reason"
                  variant="outlined"
                  className="ml-auto"
                />
                <Button variant="contained" onClick={handleSendReason}>
                  Send
                </Button>
              </div>
            )}
          </div>
          {rejectStatus && (
            <Snackbar
              open={rejectStatus}
              autoHideDuration={300}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
              <Alert
                onClose={() => setRejectStatus(false)}
                severity="error"
                variant="filled"
                sx={{ width: '100%' }}
              >
                Please provide a reason
              </Alert>
            </Snackbar>
          )}
          {validateInput && (
            <ReusableSnackbar
              open={validInput}
              message={validateInput}
              onClose={ValidateInputClose}
            />
          )}
          {duplicateValidate && (
            <ReusableSnackbar
              open={duplicateValid}
              onClose={duplicateValidClose}
              message={duplicateValidate}
              action={actionDuplicateValidate}
            />
          )}
        </div>
      </div>
    </BootstrapDialog>
  );
}
