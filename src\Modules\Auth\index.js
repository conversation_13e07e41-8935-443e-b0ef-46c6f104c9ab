import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';

import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';

import * as actions from '../../_reduxapi/actions/auth';

class Auth extends Component {
  render() {
    const { message, isLoading } = this.props;
    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={isLoading} />
      </div>
    );
  }
}

Auth.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    isLoading: state.auth.isLoading,
    message: state.auth.message,
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Auth);
