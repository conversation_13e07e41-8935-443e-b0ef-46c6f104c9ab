import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Trans } from 'react-i18next';

function UserManageNav({ toggleClick, active, to, staffActive, studentActive }) {
  const [open, setOpen] = useState(active === 'sideNavActive');
  return (
    <>
      <Link to="#" onClick={() => setOpen(!open)} active={active} className={active}>
        <span>
          <Trans i18nKey={'side_nav.menus.user_management'} />
        </span>
        <span>
          <i className={`fa fa-caret-${!open ? 'down' : 'up'} pd_4`}></i>
        </span>
      </Link>

      {open && (
        <>
          <Link to={`${to}/staff`} className={staffActive} onClick={toggleClick}>
            {' '}
            <span className="pl_30">
              <Trans i18nKey={'staff_management'} />
            </span>
          </Link>

          <Link to={`${to}/student`} className={studentActive} onClick={toggleClick}>
            {' '}
            <span className="pl_30">
              <Trans i18nKey={'student_management'} />
            </span>
          </Link>
        </>
      )}
    </>
  );
}

UserManageNav.propTypes = {
  to: PropTypes.string,
  active: PropTypes.string,
  toggleClick: PropTypes.func,
  staffActive: PropTypes.string,
  studentActive: PropTypes.string,
};
export default UserManageNav;
