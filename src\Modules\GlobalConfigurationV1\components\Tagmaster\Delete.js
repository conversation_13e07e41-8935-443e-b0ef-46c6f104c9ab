import React from 'react';

//components
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';

//props
import PropTypes from 'prop-types';

//icons
import redWarning from 'Assets/redWarning.svg';

const Delete = ({ isDelete, handleDeleteCancel, handleDeleteConfirm, type }) => {
  const currentType = type === 'families' ? 'Family' : type === 'groups' ? 'Group' : 'Tag';
  return (
    <div>
      <DialogModal fullWidth={true} maxWidth={'sm'} onClose={() => {}} show={isDelete}>
        <div className="px-4 pt-3 pb-4">
          <div className="d-flex align-items-center">
            <div>
              <img src={redWarning} alt="redWarning" />
            </div>
            <div className="pl-3 h-5 bold">{`Delete ${currentType}`}</div>
          </div>
          <div className="mt-2">
            {` This action will permanently remove the selected ${currentType} and its configurations from
            the application!`}
          </div>
          <div className="mt-2">{`Are you sure you want to delete the selected ${currentType}?`}</div>
          <div className="d-flex mt-4">
            <div className="d-flex justify-content-end ml-auto">
              <MButton
                variant="text"
                className="mr-2 border px-3 bold text-dark"
                sx={{ textTransform: 'none' }}
                size={'small'}
                clicked={handleDeleteCancel}
              >
                Cancel
              </MButton>
              <MButton
                color={'red'}
                variant="contained"
                className="ml-1"
                sx={{ textTransform: 'none' }}
                size={'small'}
                clicked={handleDeleteConfirm}
              >
                Delete
              </MButton>
            </div>
          </div>
        </div>
      </DialogModal>
    </div>
  );
};

export default Delete;

Delete.propTypes = {
  handleDeleteCancel: PropTypes.func,
  handleDeleteConfirm: PropTypes.func,
  isDelete: PropTypes.bool,
  type: PropTypes.string,
};
