import React, { useEffect, useState } from 'react';
import { fromJS, List, Map } from 'immutable';
import { FormControl, MenuItem, Select, Autocomplete, TextField } from '@mui/material';
import { getHandoutReport, getUserAndCourse, setData } from '_reduxapi/courseHandout/action';
import { useDispatch, useSelector } from 'react-redux';
import { selectCourseReport, selectUserAndCourse } from '_reduxapi/courseHandout/selectors';
import { selectInstitutionCalendar } from '_reduxapi/Common/Selectors';
import ReactECharts from 'echarts-for-react';
import ReportTable from './ReportTable';
import useDebounce from 'Hooks/useDebounceHook';

export function getInstitutionData(institutionCalendarLists) {
  if (institutionCalendarLists.size > 0) {
    return institutionCalendarLists.map((data) => {
      return Map({
        name: `AY  ` + data.get('calendar_name', ''),
        value: data.get('_id'),
      });
    });
  }
  return List();
}

const Dropdown = ({ value, options, onChange }) => (
  <FormControl fullWidth>
    <Select value={value} onChange={onChange} displayEmpty>
      {options.map((opt) => (
        <MenuItem key={opt.get('value', '')} value={opt.get('value', '')}>
          {opt.get('name', '')}
        </MenuItem>
      ))}
    </Select>
  </FormControl>
);

const YearAutocomplete = ({ value, onChange, onSelect, label = 'Course', options }) => {
  return (
    <Autocomplete
      freeSolo
      options={options}
      value={value}
      onChange={(e, newValue) => {
        if (newValue) {
          onSelect(newValue);
        }
        onChange(newValue);
      }}
      onInputChange={(e, newInputValue, reason) => {
        if (reason === 'input') {
          onChange(newInputValue);
        }
      }}
      getOptionLabel={(option) => (typeof option === 'string' ? option : option?.name || '')}
      renderInput={(params) => (
        <TextField {...params} placeholder={label} variant="outlined" fullWidth />
      )}
    />
  );
};

const CourseHandoutReport = () => {
  const dispatch = useDispatch();
  const userAndCourse = useSelector(selectUserAndCourse);
  const institutionCalendarLists = useSelector(selectInstitutionCalendar);
  const courseReport = useSelector(selectCourseReport);
  const institutionData = getInstitutionData(institutionCalendarLists);
  const [filters, setFilters] = useState(
    Map({
      calendarId: institutionData.getIn([0, 'value'], ''),
      type: 'course',
      searchKey: '',
    })
  );
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [resetTableFilters, setResetTableFilters] = useState(0);

  const calendarId = filters.get('calendarId', '');
  const type = filters.get('type', '');
  const searchKey = filters.get('searchKey', '') || filters.get('searchKey', '')?.name || '';
  const debounceSearchKey = useDebounce(searchKey, 1000);

  const scheduleList = courseReport?.get('scheduleList', List()) || List();
  const totalItems = scheduleList.size || 0;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  const paginatedData = Map({
    scheduleList: scheduleList.slice(startIndex, endIndex),
    courseHandout: courseReport?.get('courseHandout', List()) || List(),
    fullScheduleList: scheduleList,
  });

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handlePageSizeChange = (newSize) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  const paginationData = {
    pagination: (newSize) => handlePageSizeChange(Number(newSize)),
    onNextClick: () => handlePageChange(currentPage + 1),
    pageCount: pageSize,
    onBackClick: () => handlePageChange(currentPage - 1),
    onFullLastClick: () => handlePageChange(1),
    onFullForwardClick: () => handlePageChange(totalPages),
    totalPages: totalPages,
    currentPage: currentPage,
  };

  useEffect(() => {
    setSelectedCourse(null);
    setCurrentPage(1);
    setResetTableFilters((prev) => prev + 1);
    dispatch(setData(Map({ courseReport: Map(), userAndCourse: Map() })));
  }, []);

  useEffect(() => {
    const params = {
      institutionCalendarId: calendarId,
      searchType: type,
      searchKey: searchKey,
    };
    if (calendarId && type && searchKey && typeof searchKey === 'string') {
      dispatch(getUserAndCourse(params));
    }
  }, [calendarId, type, debounceSearchKey]);

  useEffect(() => {
    const courseDetails = fromJS(selectedCourse);
    if (courseDetails !== null && courseDetails.size && filters.get('searchKey', '')) {
      const [courseId, rotationCount] = (selectedCourse?.value || '').split('_');
      const params = {
        institutionCalendarId: calendarId,
        ...(filters.get('type', '') === 'course'
          ? {
              programId: courseDetails.get('programId', ''),
              courseId: courseId,
              year: courseDetails.get('year', ''),
              levelNo: courseDetails.get('levelNo', ''),
              term: courseDetails.get('term', ''),
            }
          : {
              staffId: courseDetails.get('value', ''),
              searchType: filters.get('type', ''),
            }),
      };
      if (rotationCount) params.rotationCount = rotationCount;
      dispatch(getHandoutReport(params));
    }
  }, [selectedCourse, calendarId, filters]);

  const handleChange = (key) => (e) => {
    setFilters((prev) => {
      let prevState = prev.set(key, e.target.value);
      prevState = prevState.set('searchKey', '');
      return prevState;
    });
    setSelectedCourse(null);
    setCurrentPage(1);
    setResetTableFilters((prev) => prev + 1);
    dispatch(setData(Map({ courseReport: Map(), userAndCourse: Map() })));
  };

  const handleAutoYearChange = (value) => {
    setFilters((prev) => prev.set('searchKey', value));
    setCurrentPage(1);
    setResetTableFilters((prev) => prev + 1);
    dispatch(setData(Map({ courseReport: Map(), userAndCourse: Map() })));
  };

  const handleCourseSelect = (selectedValue) => {
    setSelectedCourse(selectedValue);
  };

  const getOptions = (type) => {
    switch (type) {
      case 'year':
        return institutionData;
      case 'type':
        return fromJS([
          {
            name: 'Course',
            value: 'course',
          },
          {
            name: 'Staff',
            value: 'staff',
          },
        ]);
      case 'userAndCourse':
        return filters.get('type', '') === 'course'
          ? userAndCourse
              .get('matchingCourseList', List())
              .map((detail) =>
                detail
                  .set(
                    'name',
                    `${detail.get('coursesName', '')} / ${detail.get(
                      'programName',
                      ''
                    )} / ${detail.get('year', '')} / ${detail.get('levelNo', '')} / ${detail.get(
                      'term',
                      ''
                    )}` +
                      (detail.get('rotationCount')
                        ? ` / Rotation ${detail.get('rotationCount')}`
                        : '')
                  )
                  .set(
                    'value',
                    detail.get('courseId', '') +
                      (detail.get('rotationCount') ? `_${detail.get('rotationCount')}` : '')
                  )
              )
          : userAndCourse
              .get('filteredUserData', List())
              .map((detail) =>
                detail
                  .set(
                    'name',
                    `${detail.getIn(['name', 'first'], '')} ${detail.getIn(['name', 'last'], '')}`
                  )
                  .set('value', detail.get('_id', ''))
              );
      default:
        return List();
    }
  };

  const getReportDetails = () => {
    const immutableRes = courseReport;
    let output = List();

    immutableRes.get('scheduleList', List()).forEach((schedule) => {
      const handoutColor = schedule.get('handoutColor', '');
      const courseName = schedule.get('course_name', '');
      const courseId = schedule.get('_course_id', '');
      let staffs = schedule.get('staffs', List());
      if (filters.get('type', '') === 'staff') {
        staffs = staffs.filter(
          (st) => st.getIn(['_staff_id', '_id'], '') === filters.get('searchKey', '')?.value
        );
      }
      staffs.forEach((staff) => {
        const staffId = staff.getIn(['_staff_id', '_id'], '');
        const index = output.findIndex(
          (entry) =>
            entry.getIn(['staff', '_staff_id', '_id'], '') === staffId &&
            entry.get('courseId', '') === courseId
        );

        if (index === -1) {
          const newEntry = Map({
            staff,
            course_name: courseName,
            courseId,
            overallCount: 1,
            courseHandoutCount: immutableRes.get('courseHandout').map((handout) =>
              Map({
                colorCode: handout.get('colorCode', ''),
                status: handout.get('status', ''),
                count: handout.get('colorCode', '') === handoutColor ? 1 : 0,
              })
            ),
          });
          output = output.push(newEntry);
        } else {
          output = output.update(index, (entry) => {
            const updatedCountList = entry
              .get('courseHandoutCount', List())
              .map((item) =>
                item.get('colorCode', '') === handoutColor
                  ? item.update('count', (c) => c + 1)
                  : item
              );
            return entry
              .update('overallCount', (count) => count + 1)
              .set('courseHandoutCount', updatedCountList);
          });
        }
      });
    });

    return output;
  };
  const reportDetails = getReportDetails();

  return (
    <div className="main mt-3 m-3">
      <div className="d-flex flex-wrap align-items-center justify-content-between gap-3">
        <p className="mb-0 bold f-21 mt-2">Course Handout Report</p>
        <div className="d-flex gap-3 mt-3" style={{ minWidth: '800px' }}>
          <div className="flex-grow-1">
            <Dropdown
              label="Year"
              value={filters.get('calendarId', '')}
              options={getOptions('year')}
              onChange={handleChange('calendarId')}
            />
          </div>
          <div className="flex-grow-1">
            <Dropdown
              label="Type"
              value={filters.get('type', '')}
              options={getOptions('type')}
              onChange={handleChange('type')}
            />
          </div>
          <div className="flex-grow-1">
            <YearAutocomplete
              label={filters.get('type', '') === 'course' ? 'Course' : 'Staff'}
              value={filters.get('searchKey')}
              onChange={handleAutoYearChange}
              onSelect={handleCourseSelect}
              options={getOptions('userAndCourse').toJS()}
            />
          </div>
        </div>
      </div>

      <div className="row mt-3 card">
        <div className="col-md-12 col-12 col-xl-12 col-lg-12">
          <div className="p-3 bg-white border-radious-4 mb-4" id="delivery-status-chart">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <p className="bold f-18 mb-0">Course Handout</p>
            </div>

            <ReactECharts
              notMerge={true}
              lazyUpdate={true}
              option={getOption(reportDetails, filters.get('type', ''))}
              className={'echart_custom_height'}
            />
          </div>
        </div>
      </div>
      <ReportTable
        courseReport={paginatedData}
        paginationData={paginationData}
        resetFiltersTrigger={resetTableFilters}
      />
    </div>
  );
};

export default CourseHandoutReport;

export function getOption(reportDetails, type) {
  const getStaffName = (item) =>
    item.getIn(['staff', 'staff_name', 'first'], '') +
    item.getIn(['staff', 'staff_name', 'last'], '');
  const getCourseName = (item) => item.get('course_name', '');
  const getHandoutCount = (item, index) => item.getIn(['courseHandoutCount', index, 'count'], 0);
  const courseOrStaff = reportDetails.map(type === 'course' ? getStaffName : getCourseName);
  const overallCount = reportDetails.map((item) => item.get('overallCount', 0));
  const notUploadedCount = reportDetails.map((item) => getHandoutCount(item, 0));
  const uploadedCount = reportDetails.map((item) => getHandoutCount(item, 1));
  const lateUploadedCount = reportDetails.map((item) => getHandoutCount(item, 3));

  const seriesConfig = (name, data, color = '#6B72D2') => ({
    name,
    type: 'bar',
    emphasis: { focus: 'series' },
    data: data.toJS(),
    itemStyle: {
      color,
    },
    barWidth: 35,
  });

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
    },
    legend: {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [{ type: 'category', data: courseOrStaff.toJS() }],
    yAxis: [{ type: 'value' }],
    series: [
      seriesConfig('Overall', overallCount),
      seriesConfig('Not Uploaded', notUploadedCount, '#e50000'),
      seriesConfig('Uploaded', uploadedCount, '#008000'),
      seriesConfig('Late Uploaded', lateUploadedCount, '#FF6E00'),
    ],
  };
}
