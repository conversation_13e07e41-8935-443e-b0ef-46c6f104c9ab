import React from 'react';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';

function AssessmentType(props) {
  const programType = [
    {
      name: 'all',
      value: 'all',
    },
    {
      name: 'undergraduate',
      value: 'undergraduate',
    },
    {
      name: 'postgraduate',
      value: 'postgraduate',
    },
    {
      name: 'pre-requisite',
      value: 'pre-requisite',
    },
  ];

  return (
    <div className="main pb-5">
      <div className="container">
        <div className="pt-5 pb-5">
          <div className="course_master">
            <div className="d-flex justify-content-between mb-3">
              <div className="pl-2">
                <MaterialInput elementType={'materialSearch'} placeholder={'Search'} />
              </div>
              <div className="d-flex justify-content-end">
                <div className="col-md-8">
                  <MaterialInput
                    elementType={'materialSelect'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    elementConfig={{ options: programType }}
                    label={'Program Type'}
                  />
                </div>
              </div>
            </div>

            <div className="program_table">
              <table align="left">
                <thead>
                  <tr>
                    <th>
                      <div className=""> Code</div>
                    </th>

                    <th>
                      <div className=""> Program Name</div>
                    </th>

                    <th>
                      <div className=""> Program Type</div>
                    </th>
                    <th>
                      <div className=""> Degree Name</div>
                    </th>
                    <th>
                      <div className=""> Direct Assessment</div>
                    </th>
                    <th>
                      <div className=""> Indirect Assessment</div>
                    </th>
                    <th>
                      <div className="">Action</div>
                    </th>
                  </tr>
                </thead>

                <tbody>
                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">MED12345</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Medicine Program</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Undergraduate</div>
                    </td>
                    <td className="">
                      <div className="mt-2">MBBS</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>

                    <td className="">
                      <div className="d-flex justify-content-between ">
                        <MButton variant="outlined" color={'gray'}>
                          {' '}
                          View
                        </MButton>
                      </div>
                    </td>
                  </tr>
                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">MED12345</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Medicine Program</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Undergraduate</div>
                    </td>
                    <td className="">
                      <div className="mt-2">MBBS</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>

                    <td className="">
                      <div className="d-flex justify-content-between ">
                        <MButton variant="outlined" color={'gray'}>
                          {' '}
                          View
                        </MButton>
                      </div>
                    </td>
                  </tr>
                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">MED12345</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Medicine Program</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Undergraduate</div>
                    </td>
                    <td className="">
                      <div className="mt-2">MBBS</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>

                    <td className="">
                      <div className="d-flex justify-content-between ">
                        <MButton variant="outlined" color={'gray'}>
                          {' '}
                          View
                        </MButton>
                      </div>
                    </td>
                  </tr>
                  <tr className="tr-change">
                    <td className="">
                      <div className="mt-2">MED12345</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Medicine Program</div>
                    </td>
                    <td className="">
                      <div className="mt-2">Undergraduate</div>
                    </td>
                    <td className="">
                      <div className="mt-2">MBBS</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>
                    <td className="">
                      <div className="mt-2">10</div>
                    </td>

                    <td className="">
                      <div className="d-flex justify-content-between ">
                        <MButton variant="outlined" color={'gray'}>
                          {' '}
                          View
                        </MButton>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AssessmentType;
