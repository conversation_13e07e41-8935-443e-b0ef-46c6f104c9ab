import React from 'react';
import PropTypes from 'prop-types';
//react library
import {
  DialogActions,
  Dialog,
  DialogTitle,
  DialogContent,
  Switch,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Typography,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
//mui Imports
import { Map } from 'immutable';
import { useManipulateDataForTags } from './Utils';
//style Imports
import { makeStyles } from '@mui/styles';
import { dialogSX, placeholderStyles } from './designUtils';

//----------------------------------UI Utils Start--------------------------------------------------
export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '14px',
    color: '#4B5563',
    fontWeight: 400,
    paddingLeft: '5px',
  },
}));
const radioIconStyle = {
  '& .MuiSvgIcon-root': {
    fontSize: 'clamp(0.875em, 2vw, 1.0625em)',
  },
};
const switchSX = {
  '& .MuiSwitch-thumb': { background: '#147afc', width: '17px', height: '17px' },
  '& .MuiSwitch-track': {
    height: '85%',
    width: '90%',
    background: '#D1D5DB !important',
  },
  '& .MuiSwitch-switchBase:not(.Mui-checked) .MuiSwitch-thumb': {
    background: '#F3F4F6',
  },
};
const switchLabelSx = {
  '& .MuiTypography-root': {
    padding: '0px !important',
  },
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
export const label_levels_qlc = ['Institution Level', 'Program Level', 'Course Level'];
export const value_label_qlc = ['institution', 'program', 'course'];
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------componentStart--------------------------------------------------
const SubTag = ({ SubData, handleChangeInput, handleDelete }) => {
  return (
    <>
      {SubData.map((inputField, index) => (
        <div className="ml-3" key={index}>
          <div className="d-flex align-items-center my-2">
            <div className="f-12 fw-400 pt-100 text-mGrey">{`Sub Tag Name ${index + 1}`}</div>
          </div>
          <div className="d-flex align-items-center gap-15">
            <TextField
              className="w-100 pb-2 textColor"
              type="text"
              value={inputField}
              onChange={(event) => handleChangeInput(index, event)}
              placeholder="Enter Sub Tag"
              size="small"
              sx={placeholderStyles}
            />
            <DeleteIcon
              fontSize="small"
              className="mb-2"
              sx={{ color: 'red', cursor: 'pointer' }}
              onClick={() => handleDelete(index)}
            />
          </div>
        </div>
      ))}
    </>
  );
};

const radioControl = () => {
  return <Radio size="small" disableRipple sx={radioIconStyle} className="p-0" />;
};
//----------------------------------componentEnd----------------------------------------------------
const CreateTags = ({ editIndex, open, existData = Map(), handleClose, setTagData, isEdit }) => {
  //----------------------------------custom hooks start----------------------------------------------
  const [
    tags,
    setTags,
    handleValidate,
    handleAddSubTag,
    inputFields,
    handleChangeInput,
    handleDelete,
  ] = useManipulateDataForTags(existData, handleClose, setTagData, editIndex, isEdit);
  //----------------------------------custom hooks end------------------------------------------------
  const classes = useStyles();
  return (
    <Dialog fullWidth open={open} onClose={handleClose} PaperProps={{ sx: dialogSX }}>
      <DialogTitle className="text-mGrey pb-0 ">
        <h5 className="pt-3 fw-400  f-24 pl-1 ">
          {editIndex === undefined ? 'Create ' : 'Edit '} Q360-Tags
        </h5>
      </DialogTitle>
      <DialogContent>
        {tags.map((tag, index) => {
          return (
            <section key={index} className="pt-1 px-1">
              <div>
                <div className="d-flex align-items-center">
                  <div className="mr-2 f-12 fw-400 text-mGrey">Name</div>
                  <div className="f-14 fw-400 text-lGrey ml-auto">Default</div>
                  <FormControlLabel
                    className="m-0 ml-2"
                    control={
                      <Switch
                        checked={tag.get('isDefault', false)}
                        sx={switchSX}
                        onChange={(e) =>
                          setTags((prev) => prev.setIn([index, 'isDefault'], e.target.checked))
                        }
                      />
                    }
                    classes={{ label: classes.label }}
                    label={
                      <Typography className="f-14" sx={switchLabelSx}>
                        {tag.get('isDefault', false) ? 'ON' : 'OFF'}
                      </Typography>
                    }
                  />
                </div>
                <TextField
                  value={tag.get('name', '')}
                  onChange={(e) => setTags((prev) => prev.setIn([index, 'name'], e.target.value))}
                  placeholder="Enter Name"
                  size="small"
                  fullWidth
                  sx={placeholderStyles}
                />
              </div>
              <div className="pt-2 ">
                <label className="m-0 f-12 fw-400 text-mGrey">Level</label>
                <RadioGroup
                  value={tag.get('level', '')}
                  row
                  className="d-flex gap-8 flex-wrap"
                  onChange={(e) => setTags((prev) => prev.setIn([index, 'level'], e.target.value))}
                >
                  {value_label_qlc.map((option, i) => (
                    <FormControlLabel
                      className="m-0 d-flex gap-2"
                      key={option}
                      value={option}
                      control={radioControl()}
                      label={label_levels_qlc[i]}
                      classes={{ label: classes.label }}
                    />
                  ))}
                </RadioGroup>
                <div
                  className="f-14 fw-400 text-primary pt-2 cursor-pointer"
                  onClick={() => {
                    handleAddSubTag();
                  }}
                >
                  + Add Sub Tag
                </div>
                <SubTag
                  SubData={inputFields}
                  handleChangeInput={handleChangeInput}
                  handleDelete={handleDelete}
                />
              </div>
            </section>
          );
        })}
      </DialogContent>
      <DialogActions className="px-4 pb-4 gap-2">
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall text-secondary border-secondary bold"
          variant="outlined"
          onClick={() => {
            handleClose();
          }}
        >
          Cancel
        </Button>
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall color-blue"
          variant="contained"
          onClick={() => {
            handleValidate();
          }}
        >
          {editIndex === undefined ? 'Create' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

CreateTags.propTypes = {
  open: PropTypes.bool,
  editIndex: PropTypes.number,
  handleClose: PropTypes.func,
  existData: PropTypes.instanceOf(Map),
  setTagData: PropTypes.func,
};

export default CreateTags;
