import React from 'react';
import AddIcon from '@mui/icons-material/Add';

function RegulationVersion(props) {
  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="bg-white p-3 border-bottom-2px">
        <div className="container-fluid">
          <p className="font-weight-bold mb-0 text-left f-17">
            <i className="fa fa-arrow-left pr-3 remove_hover" aria-hidden="true"></i>Regulation -
            2022.V2
          </p>
        </div>
      </div>

      <div className="container pl-0">
        <div className="p-1 mt-2 d-flex">
          <div className="digi-Eevaluation-bg d-flex">
            <div className="m-1 f-14 pt-1 pl-3 pr-3 pb-1 AssessmentActive text-center remove_hover bg-white digi-co-width digi-root-node">
              CO
            </div>
            <div className="m-1 f-14 pt-1 pl-3 pr-3 pb-1  AssessmentActive text-center remove_hover digi-co-width digi-po-bg digi-root-node">
              PO
            </div>
          </div>

          <div className="d-flex pt-1 text-skyblue pl-3 bold remove_hover">
            <AddIcon />
            <p className="mb-0 f-15 padding-top-2px">Add New</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RegulationVersion;
