import React, { Fragment, useEffect, useState, useMemo, Suspense } from 'react';
import {
  Accordion,
  AccordionDetails,
  Box,
  Checkbox,
  Divider,
  Paper,
  FormControl,
  MenuItem,
  Select,
  Typography,
} from '@mui/material';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { Tooltip } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import moment from 'moment';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import DoneIcon from '@mui/icons-material/Done';
import CloseIcon from '@mui/icons-material/Close';
import QuestionMarkIcon from '@mui/icons-material/QuestionMark';
import { useHistory } from 'react-router-dom';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { tabsClasses } from '@mui/material/Tabs';

import CreateNewFormQaPc from '../../Models/CreateNewFormV2';
import { tabBorderNone } from '../../QualityAssuranceApprover/approvalFlow';
import { EnableOrDisable as ShowOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import { selectSelectedRole } from '_reduxapi/Common/Selectors';

import {
  selectApprovalCategoryList,
  selectCategoryList,
  selectRpActions,
  selectTodoMissedData,
  selectAcademicYear,
} from '_reduxapi/q360/selectors';
import { getAcademicYears } from '_reduxapi/q360/actions';

const tabListSX = {
  [`& .${tabsClasses.scrollButtons}`]: {
    '&.Mui-disabled': { opacity: 0.3 },
  },
  '& .MuiTabs-flexContainer': {
    gap: '10px',
  },
};

const paperSx = {
  width: 30,
  height: 30,
  borderRadius: '50%',
  padding: '3px',
  right: -15,
  bottom: -27,
  zIndex: 2,
};

const tabPadding = {
  '&.MuiTabPanel-root': {
    padding: '0px 0px',
  },
};
const CategoryTabSx = {
  '& .MuiTab-root': {
    margin: '0px 20px 0px 0px',
    padding: '0px 0px',
    minWidth: '0px',
  },
};

const useTabState = (defaultValue) => {
  const [tab, setTab] = useState(defaultValue);
  const updateTab = (_, value) => setTab(value);
  return [tab, updateTab];
};
const EnableOrDisable = ({ callFrom, children }) => {
  return callFrom !== 'dashboard' ? children : <></>;
};
function QualityAssuranceProcess({ callFrom = 'parent' }) {
  const [tab1, updateTab1] = useTabState('overview');
  const [tab, updateTab] = useTabState('submitted');

  const dispatch = useDispatch();
  const categoryList = useSelector(selectApprovalCategoryList);
  const [getCategoryList] = useCallApiHook(getApprovalCategoryList);
  const [callApi, setCallApi] = useState(false);

  const academicYear = useSelector(selectAcademicYear);
  useEffect(() => {
    if (!academicYear.size) {
      dispatch(getAcademicYears());
    }
  }, []);

  useEffect(() => {
    dispatch(getRpAction());
  }, []);

  const selectedRole = useSelector(selectSelectedRole);
  const roleId = selectedRole.getIn(['_role_id', '_id'], '');
  useEffect(() => {
    if (roleId) {
      dispatch(getFormSettingList());
    }
  }, [roleId]);

  const { open, openOrClose } = useCustomFormModalHook();
  const {
    calendarDetails,
    onChangeCalendar,
    selectedCalendar,
    setSelectedCalendar,
  } = useCustomCalendarListHook();
  const payload = { institutionCalenderId: selectedCalendar, subModuleType: 'Form Initiator' };
  // useEffect(() => {
  //   if (selectedCalendar) getCategoryList(payload);
  // }, [selectedRole, selectedCalendar]);

  const [todoMissed] = useCallApiHook(getTodoMissedData);

  const formAssuranceList = useSelector(selectFormAssuranceList);

  const [
    pagination,
    formId,
    switchPagination,
    skipPagination,
    handleChangePagination,
    resetPagination,
  ] = usePaginationHook(
    formAssuranceList.get(tab === 'submitted' ? 'submittedCount' : 'archiveCount', 0)
  );

  const [searchKey, handleChange] = useSearch();

  const debouncedSearchKey = useDebounce(searchKey, 500);

  useEffect(() => {
    resetPagination();
  }, [searchKey, tab, tab1]);

  const selectedCategoryTab = tab1 !== 'overview' ? tab1 : '';

  const resetFormDetails = (e) => {
    const value = e;
    setSelectedCalendar(value);
    updateTab('', 'submitted');
    updateTab1('', 'overview');
    handleChange('');
    resetPagination();
    history.push(`?calendar=${value}`);
  };

  const params = {
    institutionCalenderId: selectedCalendar,
    limit: pagination.get('perPageLimit', 5),
    formType: tab,
    searchKey: debouncedSearchKey,
    categoryId: selectedCategoryTab,
    pageNo: pagination.get('currentPage', 1),
  };

  useEffect(() => {
    if (selectedCalendar) {
      const callback = () => {
        todoMissed({ institutionCalenderId: selectedCalendar, subModuleType: 'Form Initiator' });
        dispatch(getFormList(params));
      };
      getCategoryList(payload, callback);
    }
  }, [selectedRole, selectedCalendar]);

  const currentPage = pagination.get('currentPage', 1);
  const perPageLimit = pagination.get('perPageLimit', 5);

  useEffect(() => {
    if (selectedCalendar && roleId) {
      dispatch(getFormList(params));
    }
  }, [tab1, debouncedSearchKey, tab, currentPage, perPageLimit, callApi]);

  useEffect(() => {
    updateTab1('', 'overview');
    updateTab('', 'submitted');
  }, [selectedCalendar, selectedRole]);

  const handleFilters = {
    categoryTab: tab1,
    tab,
    updateTab,
    searchKey,
    handleChange,
    pagination,
    switchPagination,
    skipPagination,
    handleChangePagination,
  };
  return (
    <main className="p-4 full-height-q360">
      <EnableOrDisable callFrom={callFrom}>
        <header className="d-flex">
          <section className="f-24 text-mGrey flex-grow-1">Quality Assurance Process</section>
          <div className="ml-auto mr-2" style={{ flexBasis: '200' }}>
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              size={'small'}
              elementConfig={{ options: calendarDetails.toJS() }}
              labelclass={'mb-1 f-15'}
              changed={(e) => onChangeCalendar(e.target.value)}
              value={selectedCalendar}
            />
          </div>
          <CreateNewFormQaPc
            selectedCalendar={selectedCalendar}
            open={open}
            openOrClose={openOrClose}
            calendarDetails={calendarDetails}
            categoryTab={tab1}
            setCallApi={setCallApi}
            callApi={callApi}
            resetFormDetails={resetFormDetails}
          />
        </header>
      </EnableOrDisable>

      <section className="mt-2 sticky-header-q360">
        <TabContext value={tab1}>
          <Box>
            <TabList
              aria-label="lab API tabs example"
              // variant="scrollable"
              // scrollButtons="auto"
              // allowScrollButtonsMobile
              sx={{ ...tabBorderNone, ...CategoryTabSx, ...tabListSX }}
              style={{
                minWidth: 0,
                minHeight: 10,
                // padding: '5px',
              }}
              onChange={updateTab1}
              className="mb-3"
            >
              <Tab
                label="Overview"
                value="overview"
                sx={{ textTransform: 'none', paddingLeft: 0 }}
              />
              {categoryList.map((categories) => {
                return (
                  <Tab
                    key={categories.get('_id', '')}
                    label={categories.get('categoryName', '')}
                    value={categories.get('_id', '')}
                    sx={{ textTransform: 'none' }}
                  />
                );
              })}
            </TabList>
          </Box>
          <TabPanel value={tab1} sx={tabPadding}>
            <section className="d-flex">
              <SideBar openOrClose={openOrClose} tab={tab1} selectedCalendar={selectedCalendar} />
              <MainTable
                callFrom={callFrom}
                selectedCalendar={selectedCalendar}
                calendarDetails={calendarDetails}
                setCallApi={setCallApi}
                formId={formId}
                callApi={callApi}
                handleFilters={handleFilters}
                params={params}
                categoryList={categoryList}
                tab1={tab1}
                selectedRole={selectedRole}
              />
            </section>
          </TabPanel>
        </TabContext>
      </section>
    </main>
  );
}
export default QualityAssuranceProcess;
const getTextColor = (value) => {
  switch (value) {
    case '':
      return '#9CA3AF'; // Set text color to white for red option
    default:
      return '#303030';
  }
};

export const MuiSelect = ({
  changed,
  value,
  defaultValue,
  options,
  label,
  disabled = false,
  placeholder,
}) => {
  return (
    <div>
      <FormControl fullWidth sx={{ backgroundColor: 'ffffff' }}>
        {label && <label className="f-14 mb-1 gray-neutral">{label}</label>}
        <Select
          id="demo-simple-select"
          onChange={changed}
          disabled={disabled}
          value={value}
          defaultValue={defaultValue}
          size="small"
          style={{ color: getTextColor(value), backgroundColor: '#fff' }}
          displayEmpty
          sx={{ backgroundColor: 'ffffff' }}
        >
          <MenuItem value="" hidden>
            <Typography sx={{ fontSize: '12px', color: '#9CA3AF' }} variant="MenuItem">
              {placeholder}
            </Typography>
          </MenuItem>
          {options.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              <Typography sx={{ fontSize: '12px' }} variant="MenuItem">
                {option.name}
              </Typography>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </div>
  );
};

import ExpandMoreIcon from '@mui/icons-material/ArrowDropDown';
import { List, Map, fromJS } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import LockIcon from '@mui/icons-material/Lock';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Menu from '@mui/material/Menu';
import view_column from 'Assets/img/qapc/view_column.svg';
import Fade from '@mui/material/Fade';
import {
  useCustomHooksForTable,
  useCustomCalendarListHook,
  useCustomFormModalHook,
  useCustomHooksForConfirmation,
  useGetDiscussionCount,
} from './utils';
import {
  selectQualityAssuranceFilters,
  selectQualityAssuranceList,
} from '_reduxapi/qapc/selectors';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import {
  getFormList,
  updateFormStatus,
  getRpAction,
  setData,
  getFormSettingList,
  getTodoMissedData,
  getApprovalCategoryList,
  createDuplicateForms,
} from '_reduxapi/q360/actions';
import { selectFormAssuranceList } from '_reduxapi/q360/selectors';

import PaginationQ360 from './PaginationQ360';
import LocalStorageService from 'LocalStorageService';
import { useQ360PermissionHook } from '../../Utils';
import { eString } from 'utils';
import {
  useCallApiHook,
  useSearchParams,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import {
  Add,
  ArrowRight,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  PriorityHighRounded,
} from '@mui/icons-material';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import ShowEvidenceDocument from '../CategoryOverview/Modal/ShowEvidenceDocument';
import useDebounce from 'Hooks/useDebounceHook';
const ConfirmationModal = React.lazy(() => import('./ConfirmationModal'));
import { MessagePopup } from '../../QualityAssuranceApproverFlow/approvalUtils';
import Discussion from './Discussion';

const expandOpenSX = { color: '#374151', transform: 'rotate(180deg)' };
const expandClosedSX = { color: '#374151', transform: 'rotate(90deg)' };
export function SideBarAccord({ expanded, setExpanded, configureTemplate, tab }) {
  const overviewData = useSelector(selectQualityAssuranceFilters);

  const onClickExpanded = (data) => () => {
    setExpanded((prev) => {
      if (prev.includes(data)) return prev.filter((item) => item !== data);
      return prev.push(data);
    });
  };
  const isOpened = expanded.size !== 0;
  let parentClass, childClass;
  if (isOpened) {
    parentClass = 'flex-column';
    childClass = 'p-2 ';
  } else {
    childClass = 'h-50 qapGrid-child py-2';
    parentClass = 'flex-column';
  }
  function count() {
    const missed = overviewData.get('missedList', List());
    const toDo = overviewData.get('todoList', List());
    if (tab === 'overview') {
      let missedCount = missed.reduce((count, data) => count + data.get('count', 0), 0);
      let todoCount = toDo.reduce((count, data) => count + data.get('count', 0), 0);
      return { missedCount, todoCount };
    }
    return { missedCount: missed.size, todoCount: toDo.size, missed, toDo };
  }
  const { missedCount, todoCount, missed = List(), toDo = List() } = count();
  const handleUpdate = (e, type) => {
    e.stopPropagation();
    let sampleArr = List(['missed', 'toDo']);
    if (type === 'set') return setExpanded(sampleArr);
    return setExpanded(List());
  };
  return (
    <section className={`d-flex ${parentClass} gap-10 sidebar-height`}>
      {!expanded.includes('missed') ? (
        <div
          className={`d-flex ${childClass} border rounded align-items-center  missed cursor-pointer`}
          onClick={onClickExpanded('missed')}
        >
          <div className="flex-grow-1 f-14">{`Missed ! (${missedCount})`}</div>
          <ExpandMoreIcon sx={isOpened ? expandOpenSX : expandClosedSX} />
        </div>
      ) : (
        <section className="position-relative ">
          <div
            className={`d-flex ${childClass} border rounded align-items-center  missed cursor-pointer`}
            onClick={onClickExpanded('missed')}
          >
            <div className="flex-grow-1 f-14">{`Missed ! (${missedCount})`}</div>
            <ExpandMoreIcon sx={isOpened ? expandOpenSX : expandClosedSX} />
          </div>

          {expanded.includes('missed') && (
            <section className="border border-top-0 rounded bg-white px-3 py-2 ">
              <div className="f-12 fw-400 text-mGrey py-1">Category List</div>
              <Divider />
              {tab === 'overview' &&
                configureTemplate.map((categories, categoryIndex) => {
                  const selectedData = overviewData
                    .get('missedList', List())
                    .find((data) => data.get('_id', '') === categories.get('_id', ''));
                  const count = selectedData ? selectedData.get('count', 0) : 0;
                  return (
                    <Fragment key={categories.get('_id', '')}>
                      <div className="d-flex align-items-center py-1">
                        <div className="f-14 text-mGrey fw-500 flex-grow-1">
                          <div>{categories.get('categoryName', '')}</div>
                          <div className="f-10 fw-400 text-lGrey">Sessions in Progress.</div>
                        </div>
                        <div className="border rounded p-1 bgLGrey text-dGrey f-14">{count}</div>
                      </div>
                      {configureTemplate.size - 1 !== categoryIndex && <Divider />}
                    </Fragment>
                  );
                })}
              {tab !== 'overview' &&
                missed.map((data, index) => {
                  return (
                    <Fragment key={index}>
                      <div className="d-flex align-items-center justify-content-between py-1">
                        <div className="f-14 text-mGrey fw-500">{data.get('course_name', '')}</div>
                        <div className="p-1 fw-500 pb-0 text-primary f-14 border-bottom border-primary ">
                          + Create Form
                        </div>
                      </div>
                      {missed.size - 1 !== index && <Divider />}
                    </Fragment>
                  );
                })}
            </section>
          )}

          {expanded.includes('missed') && expanded.includes('toDo') && (
            <div className="to-do-list-end cursor-pointer" onClick={(e) => handleUpdate(e)}>
              <KeyboardArrowRightIcon className="f-16" />
            </div>
          )}
        </section>
      )}
      {!expanded.includes('toDo') ? (
        <div
          className={`d-flex ${childClass} border rounded align-items-center  todo cursor-pointer position-relative`}
          onClick={onClickExpanded('toDo')}
        >
          <div className="flex-grow-1 f-14">{`To Do (${todoCount})`}</div>
          <ExpandMoreIcon sx={isOpened ? expandOpenSX : expandClosedSX} />
          {!expanded.includes('missed') && !expanded.includes('toDo') && (
            <div
              className="to-do-list-start cursor-pointer"
              onClick={(e) => handleUpdate(e, 'set')}
            >
              <KeyboardArrowLeftIcon className="f-16 text-black" />
            </div>
          )}
        </div>
      ) : (
        <section>
          <div
            className={`d-flex ${childClass} border rounded align-items-center  todo cursor-pointer`}
            onClick={onClickExpanded('toDo')}
          >
            <div className="flex-grow-1 f-14">{`To Do (${todoCount})`}</div>
            <ExpandMoreIcon sx={isOpened ? expandOpenSX : expandClosedSX} />
          </div>
          {expanded.includes('toDo') && (
            <section className="border border-top-0 rounded bg-white px-3 py-2 ">
              <div className="f-12 fw-400 text-mGrey py-1">Category List</div>
              <Divider />
              {tab === 'overview' &&
                configureTemplate.map((categories, categoryIndex) => {
                  const selectedData = overviewData
                    .get('todoList', List())
                    .find((data) => data.get('_id', '') === categories.get('_id', ''));
                  const count = selectedData ? selectedData.get('count', 0) : 0;
                  return (
                    <Fragment key={categories.get('_id', '')}>
                      <div className="d-flex align-items-center py-1">
                        <div className="f-14 text-mGrey fw-500 flex-grow-1">
                          <div>{categories.get('categoryName', '')}</div>
                          <div className="f-10 fw-400 text-lGrey">Sessions in Progress.</div>
                        </div>
                        <div className="border rounded p-1 bgLGrey text-dGrey f-14">{count}</div>
                      </div>
                      {configureTemplate.size - 1 !== categoryIndex && <Divider />}
                    </Fragment>
                  );
                })}
              {tab !== 'overview' &&
                toDo.map((data, index) => {
                  return (
                    <Fragment key={index}>
                      <div className="d-flex align-items-center justify-content-between py-1">
                        <div className="f-14 text-mGrey fw-500">{data.get('course_name', '')}</div>
                        <div className="p-1 fw-500 pb-0 text-primary f-14 border-bottom border-primary">
                          + Create Form
                        </div>
                      </div>
                      {toDo.size - 1 !== index && <Divider />}
                    </Fragment>
                  );
                })}
            </section>
          )}
        </section>
      )}
    </section>
  );
}

/*-------------------------------usePageNationHook-----------------------------------*/
export const usePaginationHook = (
  selectedFormIds
  // selectedFormIds, overviewData, searchKey, parentTab
) => {
  const [pagination, setPagination] = useState(
    fromJS({
      currentPage: 1,
      perPageLimit: 5,
      disableButton: { previous: true, next: true },
    })
  );
  // const [formIds, setFormIds] = useState(selectedFormIds);
  const currentPage = pagination.get('currentPage', 0);
  const perPageLimit = pagination.get('perPageLimit', 0);

  const switchPagination = (type) => {
    if (type === 'next') {
      setPagination((previous) => previous.set('currentPage', previous.get('currentPage', 0) + 1));
    } else {
      setPagination((previous) => previous.set('currentPage', previous.get('currentPage', 0) - 1));
    }
  };
  const skipPagination = (type) => {
    if (type === 'firstPage') {
      setPagination((previous) => previous.set('currentPage', 1));
    } else {
      setPagination((previous) =>
        previous.set('currentPage', Math.ceil(selectedFormIds / perPageLimit))
      );
    }
  };
  const handleChangePagination = (e) => {
    setPagination((previous) => previous.set('perPageLimit', e.target.value));
  };
  useEffect(() => {
    const previousStatus = currentPage === 1;
    const nextStatus = selectedFormIds > currentPage * perPageLimit;

    setPagination((previous) =>
      previous
        .setIn(['disableButton', 'previous'], previousStatus)
        .setIn(['disableButton', 'next'], !nextStatus)
    );
    // if (selectedFormIds.size === 0) setFormIds(selectedFormIds.slice(0, perPageLimit));
  }, [pagination, selectedFormIds]);

  const resetPagination = () => {
    setPagination((previous) => previous.set('currentPage', 1));
  };

  // useEffect(() => {
  //   setFormIds(selectedFormIds.slice(0, perPageLimit));
  // }, [perPageLimit]);

  return [
    pagination,
    selectedFormIds,
    switchPagination,
    skipPagination,
    handleChangePagination,
    resetPagination,
  ];
};
/*-------------------------------usePageNationHook-----------------------------------*/

const useSearch = () => {
  const [searchKey, setSearchKey] = useState('');
  const handleChange = (e) => {
    setSearchKey(e);
  };

  return [searchKey, handleChange];
};

function MainTable({
  filterQuery,
  callFrom,
  selectedCalendar,
  calendarDetails,
  setCallApi,
  formId,
  handleFilters,
  params,
  categoryList,
  tab1,
  selectedRole,
}) {
  const {
    categoryTab,
    tab,
    updateTab,
    searchKey,
    handleChange,
    pagination,
    switchPagination,
    skipPagination,
    handleChangePagination,
  } = handleFilters;
  // const [tab, updateTab] = useTabState('submitted');
  const history = useHistory();
  const overviewData = useSelector(selectQualityAssuranceFilters) || Map();
  const { columnFilter, handleCheckbox, columnData } = useCustomHooksForTable();
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState(null);
  const qualityAssuranceList = useSelector(selectQualityAssuranceList);
  const formAssuranceList = useSelector(selectFormAssuranceList);
  const formIds = overviewData.get('qapcFormIds', List()).map((item) => item.get('_id', ''));
  const [searchParams] = useSearchParams();
  const calendar = searchParams.get('calendar');
  const { totalUnreadCount } = useGetDiscussionCount(selectedCalendar, categoryTab, selectedRole);

  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  // const selectedRole = useSelector(selectSelectedRole);
  // const roleId = selectedRole.getIn(['_role_id', '_id'], '');
  const selectedCategoryTab = categoryTab !== 'overview' ? categoryTab : '';
  // const params = {
  //   institutionCalenderId: selectedCalendar,
  //   limit: pagination.get('perPageLimit', 5),
  //   formType: tab,
  //   searchKey: debouncedSearchKey,
  //   categoryId: selectedCategoryTab,
  //   pageNo: pagination.get('currentPage', 1),
  // };

  // useEffect(() => {
  //   if (selectedCalendar && roleId) {
  //     dispatch(getFormList(params));
  //   }
  // }, [tab, selectedCalendar, roleId, debouncedSearchKey, categoryTab, pagination, callApi]);

  const routeToUpdate = ({
    index,
    id,
    categoryFormId,
    formAttachment,
    mergedFormId,
    level,
    hasAccess,
    status,
    submissionStatus,
  }) => () => {
    const type = categoryFormId.get('categoryFormType', '');
    const referenceDocument = hasAccess.referenceDocument;
    const displayCreation = hasAccess.displayCreation;
    const incorporation = hasAccess.incorporation;
    const edit = hasAccess.edit;
    history.push(
      `/qapc/QualityAssurance/create?formIndex=${index}&formId=${id}&formAttachment=${eString(
        formAttachment
      )}
      &level=${level}&type=${type}&categoryFormId=${categoryFormId.get(
        '_id',
        ''
      )}&incorporateMandatory=${categoryFormId.get(
        'incorporateMandatory',
        false
      )}&reference_document=${referenceDocument}&incorporation_settings=${incorporation}&display_creation=${displayCreation}&edit=${edit}&status=${status}&calendar=${calendar}&submissionStatus=${submissionStatus}`
    );
    dispatch(
      setData(
        Map({
          incorporateFromWithData: Map(),
          apiCallDoneForIncorporate: false,
          formAddendum: Map(),
          settingTags: Map(),
        })
      )
    );
    LocalStorageService.setCustomToken('mergedFormId', JSON.stringify(mergedFormId));
  };

  return (
    <section className="bg-white rounded p-3 w-100">
      <header className="d-flex align-items-center">
        <div className="f-20 fw-500 flex-grow-1">All Categories</div>
        <div className="w-25 mr-2">
          <MaterialInput
            value={searchKey}
            changed={(e) => handleChange(e.target.value)}
            elementType={'materialSearch'}
            placeholder={'Search Program, Course Name...'}
            labelclassName={'searchLeft'}
            labelclass={'searchLeft'}
            addClass="f-14 fw-400 color-lt-gray p-0 flex-grow-1"
          />
        </div>
        {/* <ReferenceForm /> */}
        <div className="border rounded columnImg mb-1 bgLGrey p-2">
          <img src={view_column} onClick={handleClick} className="cursor-pointer" />
        </div>
        <MenuColumnOrder
          anchorEl={anchorEl}
          handleClose={handleClose}
          columns={columnData}
          columnFilter={columnFilter}
          handleCheckbox={handleCheckbox}
          open={open}
        />
      </header>
      <section className="pt-2">
        <TabContext value={tab}>
          <EnableOrDisable callFrom={callFrom}>
            <Box>
              <TabList
                aria-label="lab API tabs example"
                sx={{ ...tabBorderNone /* ...PendingSx */ }}
                onChange={updateTab}
              >
                <Tab
                  label={`Submitted (${formAssuranceList.get('submittedCount', 0)})`}
                  value="submitted"
                  sx={{ textTransform: 'none', paddingLeft: 0 }}
                />
                <Tab
                  label={`Discussion (${totalUnreadCount})`}
                  value="discussion"
                  sx={{ textTransform: 'none', paddingLeft: 0 }}
                />
                <Tab
                  label={`Archive (${formAssuranceList.get('archiveCount', 0)})`}
                  value="archive"
                  sx={{ textTransform: 'none', paddingLeft: 0 }}
                />
              </TabList>
            </Box>
          </EnableOrDisable>
          <TabPanel value={tab} sx={tabPadding}>
            {/* <CourseTable
              columns={columns}
              columnFilter={columnFilter}
              courseList={qualityAssuranceList.get('formListData', List())}
            /> */}

            {tab === 'discussion' ? (
              <Discussion
                categoryList={categoryList}
                selectedCalendar={selectedCalendar}
                categoryId={tab1}
              />
            ) : (
              <CategoriesFormList
                params={params}
                columns={columnData}
                columnFilter={columnFilter}
                formListData={qualityAssuranceList}
                routeToUpdate={routeToUpdate}
                filterQuery={filterQuery}
                pages={[
                  pagination,
                  formId,
                  switchPagination,
                  skipPagination,
                  handleChangePagination,
                ]}
                formIds={formIds}
                tab={tab}
                selectedCalendar={selectedCalendar}
                calendarDetails={calendarDetails}
                selectedCategoryTab={selectedCategoryTab}
                setCallApi={setCallApi}
              />
            )}
          </TabPanel>
        </TabContext>
      </section>
    </section>
  );
}

function MenuColumnOrder({ anchorEl, handleClose, columns, columnFilter, handleCheckbox, open }) {
  return (
    <div>
      <Menu
        id="fade-menu"
        MenuListProps={{
          'aria-labelledby': 'fade-button',
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
      >
        {columns.splice(-2).map((item) => (
          <ShowOrDisable key={item.get('name', '')} valid={item.get('name', '') !== ''}>
            <MenuItem className="f-12 fw-400 text-dGrey">
              {item.get('Disable', false) ? (
                <LockIcon className="pr-2 f-20" color="disabled" />
              ) : (
                <Checkbox
                  checked={columnFilter.get(item.get('name', ''), true)}
                  onChange={(e) => handleCheckbox(e, item.get('name', ''))}
                  className="p-0 f-20 pr-2"
                  size=""
                />
              )}
              {item.get('name', '')}
            </MenuItem>
          </ShowOrDisable>
        ))}
      </Menu>
    </div>
  );
}
function CategoriesFormList({
  params,
  columns,
  // formListData,
  routeToUpdate,
  pages,
  formIds,
  tab,
  selectedCalendar,
  calendarDetails,
  // selectedCategoryTab,
  setCallApi,
}) {
  const [duplicate] = useCallApiHook(createDuplicateForms);
  const [formList] = useCallApiHook(getFormList);
  const [formId, setFormId] = useState('');

  const [pagination, , switchPagination, skipPagination, handleChangePagination] = pages;

  const dispatch = useDispatch();

  const callBack = () => formList(params);
  const handleCreateDuplicate = (params) => duplicate(params, callBack);
  const { open, openOrClose } = useCustomFormModalHook();
  const handleEditClick = (id) => {
    openOrClose();
    setFormId(id);
  };

  const rpActions = useSelector(selectRpActions);

  const {
    editPermission,
    deletePermission,
    archivePermission,
    referenceDocumentPermission,
    displayCreationPermission,
    incorporationPermission,
  } = useQ360PermissionHook(rpActions);

  const formAssuranceList = useSelector(selectFormAssuranceList);

  const count = formAssuranceList.get(tab === 'submitted' ? 'submittedCount' : 'archiveCount', 0);

  const formListData = formAssuranceList.get('filterMatchingFormData', List());

  const renderDetail = (form) => {
    const level = form.get('level', '');
    const programName = form.get('programName', '');
    const curriculumName = form.get('curriculumName', '');
    const formName = form.get('formName', '');
    const selectedGroupName = form.get('selectedGroupName', List()).join(', ');

    const titles = {
      course: form.get('courseName', ''),
      program: form.get('programName', ''),
      institution: form.get('institutionName', ''),
    };

    const subTitles = {
      course: [programName, formName, selectedGroupName].filter(Boolean).join(' / '),
      program: [curriculumName, formName].filter(Boolean).join(' / '),
      institution: formName,
    };

    return (
      <>
        <div className="text-dBlue underlined-text fw-500 f-14">
          {getShortString(titles[level], 20)}
        </div>
        <div className="f-12 fw-400 text-lGrey">{getShortString(subTitles[level], 20)}</div>
      </>
    );
  };

  const MemoizedIcons = React.memo(({ form }) => {
    const categoryFormGroupId = form.get('categoryFormGroupId', '');
    const formInitiatedId = form.get('_id', '');
    const actionIds = form.get('actionIds', List());
    const hasAccess = useMemo(
      () => ({
        edit: actionIds.includes(editPermission),
        delete: actionIds.includes(deletePermission),
        archive: actionIds.includes(archivePermission),
      }),
      [actionIds, editPermission, deletePermission, archivePermission]
    );

    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);
    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
      setAnchorEl(null);
    };
    const ITEM_HEIGHT = 48;

    const { show, handleShow, formDetails, resetDialog } = useCustomHooksForConfirmation();

    const handleRemove = (type, form, isDelete = true) => {
      const selectedForm = isDelete ? formDetails.get('form', Map()) : form;
      const selectedType = isDelete ? formDetails.get('type', '') : type;

      const mergedFormId = selectedForm
        .get('mergedFormId', List())
        .map((id) => id.get('formInitiatorId', ''));

      const requestBody = {
        formInitiatorIds: [selectedForm.get('_id', ''), ...mergedFormId],
        archive: selectedType === 'archive',
        ...(selectedType === 'delete' && { isDeleted: true }),
      };

      const callBack = () => {
        setCallApi((prev) => !prev);
        resetDialog();
      };

      dispatch(updateFormStatus(requestBody, callBack, selectedType));
    };

    return (
      <>
        <div className="">
          <MoreVertIcon onClick={handleClick} className="cursor-pointer" />
          <Menu
            id="long-menu"
            anchorEl={anchorEl}
            keepMounted
            open={open}
            onClose={handleClose}
            PaperProps={{
              style: {
                maxHeight: ITEM_HEIGHT * 4.5,
                width: '20ch',
              },
            }}
          >
            {tab === 'submitted' && hasAccess.edit && (
              <MenuItem onClick={() => handleEditClick(form.get('_id', ''))}>Edit</MenuItem>
            )}
            {tab === 'submitted' && hasAccess.delete && (
              <MenuItem
                onClick={() => {
                  handleShow('delete', form);
                  handleClose();
                }}
              >
                Delete
              </MenuItem>
            )}
            {hasAccess.archive && (
              <MenuItem
                onClick={() => {
                  handleRemove(tab === 'submitted' ? 'archive' : 'unArchive', form, false);
                }}
              >
                {tab === 'submitted' ? 'Archive' : 'Revoke'}
              </MenuItem>
            )}
            <MenuItem
              onClick={() =>
                handleCreateDuplicate({
                  categoryFormGroupId,
                  formInitiatedId,
                  institutionCalenderId: selectedCalendar,
                })
              }
            >
              Duplicate
            </MenuItem>
          </Menu>
        </div>

        {show && (
          <Suspense fallback={''}>
            <ConfirmationModal
              handleRemove={handleRemove}
              show={show}
              resetDialog={resetDialog}
              formName={formDetails.getIn(['form', 'categoryName'], '')}
            />
          </Suspense>
        )}
      </>
    );
  });

  return (
    <>
      <div className="qapc-createTable">
        <table className="mt-4 qapcCreateTable">
          <thead>
            <tr>
              {columns.map((column) => {
                if (column.get('showCheck', false))
                  return (
                    <th
                      key={column}
                      className={`${column.get(
                        'class',
                        ''
                      )} qapcCreateTableTH f-14 fw-500 text-dGrey border-bottom`}
                    >
                      <div className={`${column.get('className', '')} my-2 mx-2`}>
                        {column.get('name')}
                      </div>
                    </th>
                  );
                else <></>;
              })}
            </tr>
          </thead>
          <tbody>
            {formListData.size === 0 ? (
              <tr>
                <td colSpan={20} className="bg-white border-none">
                  <div className="text-dGrey f-14 text-center w-100">No Data Found</div>
                </td>
              </tr>
            ) : (
              formListData
                // .entrySeq()
                // .filter(([key, _]) => formId.includes(key))
                // .map(([key, form], index) => {
                .map((form, index) => {
                  const actionIds = form.get('actionIds', List());
                  const hasAccess = {
                    edit: actionIds.includes(editPermission),
                    referenceDocument: actionIds.includes(referenceDocumentPermission),
                    displayCreation: actionIds.includes(displayCreationPermission),
                    incorporation: actionIds.includes(incorporationPermission),
                  };
                  const level = form.get('level', '');
                  const submissionCurrentStatus = form.get('submissionStatus', '');
                  const resubmissionLog = form.get('resubmissionLog', List());
                  const courseName = form.get('courseName', '');
                  const programName = form.get('programName', '');
                  const addComment = form.get('addComment', List());
                  const submissionData = submissionStatus.get(submissionCurrentStatus, Map());
                  const sNo = index + 1;
                  const tableBody = {
                    'S.NO': <>{sNo}</>,
                    'Course ID / Name': (
                      <>
                        <div
                          className="my-2 cursor-pointer"
                          onClick={routeToUpdate({
                            index,
                            id: form.get('_id', ''),
                            categoryFormId: form.get('categoryFormId', Map()) || Map(),
                            formAttachment: form.get('formAttachment', ''),
                            mergedFormId: fromJS(form.get('mergedFormId', List())).toJS(),
                            level,
                            hasAccess,
                            status: form.get('status', ''),
                            submissionStatus: form.get('submissionStatus', ''),
                          })}
                        >
                          <div className="f-12 fw-400 text-lGrey">
                            {getShortString(form.get('categoryName', ''), 30)}
                          </div>
                          {renderDetail(form)}
                        </div>
                      </>
                    ),
                    Display: (
                      <>
                        <BadgeText
                          data={
                            form.get('displayName', List()).size > 0
                              ? form.get('displayName', List())
                              : form.get('displaySection', List())
                          }
                        />
                      </>
                    ),
                    Incorporate: (
                      <>
                        <Incorporate form={form} />
                      </>
                    ),
                    Submission: (
                      <>
                        <div
                          className={'rounded px-2 py-1 f-12 fw-400 text-dGrey text-center'}
                          style={{
                            backgroundColor: submissionData.get('bgColor', ''),
                            color: submissionData.get('color', ''),
                          }}
                        >
                          {submissionData.get('text', '')}
                        </div>
                        <div className="rounded px-2 py-1 text-lGrey f-12 fw-400 text-center">
                          {moment(form.get('submissionDate', '')).format('DD MMM [at] h:mm A')}
                        </div>
                      </>
                    ),
                    Approvers: (
                      <>
                        <Approvers
                          approvers={form.getIn(['categoryFormId', 'approvalLevel'], List())}
                          approversData={form.get('approverList', List())}
                          submissionStatus={submissionCurrentStatus}
                          approverStatus={form.get('status', 'draft')}
                        />
                      </>
                    ),
                    Attachment: (
                      <>
                        {form.get('attachmentCount', 0) > 0 ? (
                          <div className="d-flex align-items-center">
                            <AttachFileIcon sx={{ fontSize: '16px' }} />
                            <div className="f-14 fw-400 text-dGrey pr-1 ">
                              {form.get('attachmentCount', 0)}
                            </div>{' '}
                            <ShowEvidenceDocument
                              formInitiatorId={form.get('_id')}
                              formName={form.get('formName', '')}
                            />
                          </div>
                        ) : (
                          <div className="ml-4">-</div>
                        )}
                      </>
                    ),
                    Status: (
                      <div className="row align-items-center">
                        <div className="col-8">
                          <Status approverStatus={form.get('status', 'draft')} />
                        </div>
                        <div className="col-4">
                          {(resubmissionLog.size !== 0 || addComment.size !== 0) && (
                            <MessagePopup
                              formDetails={{ programName, courseName }}
                              resubmissionLog={resubmissionLog}
                              addComment={addComment}
                              level={level}
                            />
                          )}
                        </div>
                      </div>
                    ),
                    ' ': (
                      <>
                        {/* <Badge badgeContent={4} sx={{ fontSize: '10px' }} color="primary">
                        <CommentIcon color="primary" />
                      </Badge> */}
                      </>
                    ),
                    '  ': <MemoizedIcons key={form.get('_id')} form={form} />,
                  };
                  return (
                    <tr key={form.get('_id', '')} className="py-2">
                      {columns.map((column) => {
                        if (column.get('showCheck', false))
                          return (
                            <th
                              key={column}
                              className={`${column.get(
                                'class',
                                ''
                              )} qapcCreateTableTH f-14 fw-500 text-dGrey border-bottom`}
                            >
                              {tableBody[column.get('name', '')]}
                            </th>
                          );
                        else <></>;
                      })}
                    </tr>
                  );
                })
            )}
          </tbody>
        </table>

        <CreateNewFormQaPc
          selectedCalendar={selectedCalendar}
          open={open}
          openOrClose={openOrClose}
          method="update"
          formId={formId}
          calendarDetails={calendarDetails}
          setCallApi={setCallApi}
        />
      </div>

      {pagination.get('perPageLimit', 5) < count && (
        <PaginationQ360
          pagination={pagination}
          switchPagination={switchPagination}
          skipPagination={skipPagination}
          handleChangePagination={handleChangePagination}
          formId={formIds}
          count={count}
        />
      )}
    </>
  );
}

const BadgeText = ({ data }) => {
  const visibleData = data.slice(0, 2);
  const extraCount = data.size > 2 ? data.size - 2 : 0;

  return (
    <>
      {visibleData.map((text, index) => {
        const titleData = data.map((d) => d).join(', ');
        return (
          <div
            className={`d-flex text-uppercase gap-8 align-items-center ${
              index !== 0 ? 'pt-2' : ''
            }`}
            key={index}
          >
            <div className="f-10 rounded px-2 py-1 l-blue d-flex justify-content-center align-items-center">
              {getShortString(text, 10)}
            </div>
            {visibleData.size === index + 1 && extraCount > 0 && (
              <Tooltip title={`${titleData}`}>
                <div className="f-10 d-flex justify-content-center align-items-center count">
                  {`+${extraCount}`}
                </div>
              </Tooltip>
            )}
          </div>
        );
      })}
    </>
  );
};

const Incorporate = ({ form }) => {
  const formatData = (key) => form.get(key, List()).map((data) => data.get('formName', ''));

  const renderData = (label, data) =>
    data.size ? (
      <div className="d-flex text-uppercase gap-8 align-items-center pt-2">
        <div className="f-10 text-lGrey fw-400">{label}:</div>
        <div className="f-10 rounded px-2 py-1 bgAsh d-flex justify-content-center align-items-center">
          {getShortString(data.get(0, ''), 15)}
        </div>
        {data.size > 1 && (
          <div className="f-10 d-flex justify-content-center align-items-center mr-2 count">
            <Tooltip title={data.join(', ')}>{`+${data.size - 1}`}</Tooltip>
          </div>
        )}
      </div>
    ) : null;

  const incorporateWith = formatData('incorporateWith');
  const incorporateFrom = formatData('incorporateFrom');

  return (
    <>
      {renderData('WITH', incorporateWith)}
      {renderData('FROM', incorporateFrom)}
    </>
  );
};

const submissionStatus = fromJS({
  draft: {
    text: 'Save as Draft',
    bgColor: '#F3F4F6',
    color: '#374151',
  },
  resubmit: {
    text: 'Resubmission',
    bgColor: '#fef3c7',
    color: '#D97706',
  },
  submitted: {
    text: 'Submitted',
    bgColor: '#EEF2FF',
    color: '#4338CA',
  },
  resubmission: {
    text: 'Resubmission',
    bgColor: '#fef3c7',
    color: '#D97706',
  },
});
const noLevel = '-- --';
const approversStatus = fromJS({
  notSubmitted: {
    text: noLevel,
    bgColor: '#FFFBEB',
    color: '#D97706',
    Icon() {
      return <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Submitted: {
    text: 'Level',
    bgColor: '#FFFBEB',
    color: '#D97706',
    Icon() {
      return <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Forwarded: {
    text: 'Level',
    bgColor: '#DCFCE7',
    color: '#166534',
    Icon() {
      return <DoneIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  published: {
    text: 'Level',
    bgColor: '#DCFCE7',
    color: '#166534',
    Icon() {
      return <DoneIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Approved: {
    text: 'Level',
    bgColor: '#DCFCE7',
    color: '#166534',
    Icon() {
      return <DoneIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  pending: {
    text: 'Level',
    bgColor: '#FFFBEB',
    color: '#D97706',
    Icon() {
      return <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Rejected: {
    text: 'Level',
    bgColor: '#fef2f2',
    color: '#b91c1c',
    Icon() {
      return <CloseIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  re_submit: {
    text: 'Level',
    bgColor: '#E0E7FF',
    color: '#4338CA',
    Icon() {
      return (
        <PriorityHighRounded sx={{ fontSize: '16px', color: '#818CF8' }} className="ml-2 pt-1" />
      );
    },
  },
});
const Dummy = () => <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
function Approvers({ approvers, approversData, submissionStatus }) {
  return (
    <div className="approvers mx-2 my-1">
      {approvers.map((element, elementIndex) => {
        const levelNumber = element.get('levelNumber', 0);
        const findApprover =
          approversData.find((approver) => approver.get('level', 0) === levelNumber) || Map();
        const levelStatus = findApprover.get('levelStatus', '');
        const Icon = approversStatus.getIn([levelStatus, 'Icon'], Dummy);
        return (
          <div
            key={elementIndex}
            className={`border rounded py-1 text-center  `}
            style={{
              backgroundColor: approversStatus.getIn([levelStatus, 'bgColor'], '#FFFBEB'),
              color: approversStatus.getIn([levelStatus, 'color'], '#D97706'),
            }}
          >
            {submissionStatus === 'submitted' || submissionStatus === 'resubmission' ? (
              <>
                Level <span>{levelNumber}</span>
              </>
            ) : (
              noLevel
            )}
            <span className="mt-1">
              <Icon />
            </span>
          </div>
        );
      })}
    </div>
  );
}

const finalStatus = fromJS({
  'Yet to Start': {
    color: '#4B5563',
    bgColor: '#F3F4F6',
    text: 'Yet to Start',
  },
  Rejected: {
    bgColor: '#FEF2F2',
    color: '#EF4444',
    text: 'Rejected',
  },
  draft: {
    color: '#fff',
    bgColor: '#4b5563',
    text: 'In Draft',
  },
  'In Review': {
    color: '#d97706',
    bgColor: '#fef3c7',
    text: 'In Review',
  },
  Approved: {
    color: '#16a34a',
    bgColor: '#dcfce7',
    text: 'Approved',
  },
  published: {
    color: '#fff',
    bgColor: '#147afc',
    text: 'Published',
  },
  re_submit: {
    color: '#d97706',
    bgColor: '#fef3c7',
    text: 'Re - Submit',
  },
});
function Status({ approverStatus }) {
  const data = finalStatus.get(approverStatus, Map());
  return (
    <div
      className={'rounded px-2 py-1 f-12 fw-400  text-center '}
      style={{
        backgroundColor: data.get('bgColor', ''),
        color: data.get('color', ''),
      }}
    >
      {data.get('text', '') === 'Re - Submit' && <div>Reviewer asked to</div>}
      {data.get('text', '') === 'Yet to Start' ? (
        <div>
          <div className="text-grey">Approver</div>
          <div className="fw-500">Yet to Start</div>
        </div>
      ) : (
        <div className="pr-2 fw-400 py-1">{data.get('text', '')}</div>
      )}
    </div>
  );
}

const SideBar = ({ tab, selectedCalendar, openOrClose }) => {
  const [rotate, setRotate] = useState(false);
  const [open, setOpen] = useState(Map({ 0: true, 1: true }));
  // const [todoMissed] = useCallApiHook(getTodoMissedData);
  const categoryData = useSelector(selectCategoryList);
  const todoMissedData = useSelector(selectTodoMissedData);
  // const selectedRole = useSelector(selectSelectedRole);
  const missedList = todoMissedData.get('missedList', List());
  const todoList = todoMissedData.get('toDoList', List());
  const categoryList = todoMissedData.get('categoryData', List());
  const groupByTodo = todoList.groupBy((todo) => todo.get('categoryId', ''));
  const groupByMissed = missedList.groupBy((missed) => missed.get('categoryId', ''));
  let groupByCategory = Map();
  categoryList.forEach((category) => {
    const _id = category.get('_id', '');
    groupByCategory = groupByCategory.set(_id, category);
  });
  let allCategory = Map();
  categoryData.forEach((category) => {
    const _id = category.get('_id', '');
    allCategory = allCategory.set(_id, category);
  });
  const categoryName = allCategory.getIn([tab, 'categoryName'], '');
  const missedAccordion = open.get('0', false);
  const toDoAccordion = open.get('1', false);
  let totalTodoCount = 0;
  let totalMissedCount = 0;
  let totalAllCategoryMissedCount = 0;
  let totalAllCategoryTodoCount = 0;
  groupByTodo.forEach((pending, key) => {
    pending.forEach((value) => {
      const remainingCount = value.get('remainingCount', 0);
      if (key === tab) totalTodoCount += remainingCount;
      else totalAllCategoryTodoCount += remainingCount;
    });
  });
  groupByMissed.forEach((pending, key) => {
    pending.forEach((value) => {
      const remainingCount = value.get('remainingCount', 0);
      if (key === tab) totalMissedCount += remainingCount;
      else totalAllCategoryMissedCount += remainingCount;
    });
  });
  const isOverView = tab === 'overview';
  const handleSetTimeOut = (func) => setTimeout(() => func(), 400);
  const handleOpenRotate = (value) => {
    const callBack = () => setOpen((pre) => pre.set(value, false));
    handleSetTimeOut(callBack);
    setRotate(!rotate);
  };
  const openOrCloseAll = () => {
    if (!rotate) {
      const callBack = () => setOpen((pre) => pre.set('0', rotate).set('1', rotate));
      handleSetTimeOut(callBack);
      setRotate(!rotate);
    } else {
      const callBack = () => setRotate(!rotate);
      handleSetTimeOut(callBack);
      setOpen((pre) => pre.set('0', rotate).set('1', rotate));
    }
  };

  // useEffect(() => {
  //   const payload = { institutionCalenderId: selectedCalendar };
  //   if (selectedCalendar) todoMissed(payload);
  // }, [selectedCalendar, selectedRole]);
  useEffect(() => {
    if (!!missedAccordion && !!toDoAccordion) {
      const callBack = () => setRotate(false);
      handleSetTimeOut(callBack);
    }
  }, [open]);
  return (
    <div className="main pr-2 q360-min-height-100">
      <div className="position-relative">
        <Accordion
          className=" box-shadow-static overflow-hidden"
          expanded={!missedAccordion}
          sx={{
            '&.MuiAccordion-root': {
              marginBottom: '0px !important',
            },
          }}
          style={{
            ...(rotate
              ? {
                  width: '55vh',
                  writingMode: 'horizontal-tb',
                  transition: '0.5s',
                  background: '#FEE2E2',
                }
              : {
                  height: '40vh',
                  width: '7vh',
                  alignSelf: 'center',
                  display: 'flex',
                  justifyContent: 'center',
                  writingMode: 'vertical-lr',
                  transition: '0.5s',
                  flexDirection: 'row-reverse',
                  background: '#FEE2E2',
                }),
          }}
        >
          {rotate ? (
            <div className="d-flex align-items-center justify-content-between gap-15 p-2 py-3">
              <div className="fw-500 text-danger ">
                Missed! ({isOverView ? totalAllCategoryMissedCount : totalMissedCount})
              </div>
              <ArrowDropDownIcon
                className="text-black cursor-pointer f-20"
                onClick={() => setOpen((pre) => pre.set('0', !missedAccordion))}
              />
            </div>
          ) : (
            <div className="d-flex justify-content-between flex-row-reverse flex-grow-1 align-items-center p-2">
              <div className="fw-500 text-danger rotate-180">
                Missed! ({isOverView ? totalAllCategoryMissedCount : totalMissedCount})
              </div>
              <ArrowRight
                className="text-black cursor-pointer f-20"
                onClick={() => handleOpenRotate('0')}
              />
            </div>
          )}
          <AccordionDetails className="bg-white">
            <div className="d-flex justify-content-between align-items-center border-bottom  py-1">
              <div className="f-14">{isOverView ? 'Category List' : `${categoryName} List`}</div>
            </div>
            <div>
              {isOverView && groupByMissed.size === 0 && (
                <div className="text-grey text-center f-12 mt-1">! No Data</div>
              )}
            </div>
            <div className="custom-scroll g-config-scrollbar">
              {groupByMissed.entrySeq().map(([key, element], index) => {
                const categoryName = groupByCategory.getIn([key, 'categoryName'], '');
                const level = groupByCategory.getIn([key, 'level'], '');
                const filterData = element.filter((item) => item.get('categoryId', '') === tab);
                const totalCategoryUnCreatedCount = element.reduce(
                  (accumulator, currentElement) =>
                    accumulator + currentElement.get('remainingCount', 0),
                  0,
                  []
                );
                return (
                  <div
                    className={`d-flex justify-content-between align-items-center  py-1 `}
                    key={index}
                  >
                    {isOverView ? (
                      <div className="d-flex justify-content-between  w-100">
                        <div>
                          <div className="f-14 fw-500 text-capitalize">{categoryName}</div>
                          <div className="f-12 text-grey">Sessions in Progress</div>
                        </div>
                        <div
                          className="rounded fw-500 text-center d-flex align-items-center justify-content-center"
                          style={{ width: 40, height: 40, background: '#F3F4F6' }}
                        >
                          {totalCategoryUnCreatedCount}
                        </div>
                      </div>
                    ) : (
                      <div className="w-100">
                        {filterData.map((item, index) => {
                          const courseName = item.get('courseName', '');
                          const programName = item.get('programName', '');
                          const institutionName = item.get('institutionName', '');
                          const remainingCount = item.get('remainingCount', 0);
                          const actions = item.get('actions', Map());
                          const isTerm = actions.get('academicTerms', false);
                          const isAttemptType = actions.get('attemptType', false);
                          const isGroup = actions.get('studentGroups', false);
                          const term = item.get('term', '');
                          const attemptType = item.get('attemptTypeName', '');
                          const group = item.get('groupName', '');
                          const curriculumName = item.get('curriculumName', '');
                          const condition = filterData.size - 1 !== index;
                          const checkLevel = {
                            course: courseName,
                            program: programName,
                            institution: institutionName,
                          };
                          return (
                            <div key={index}>
                              <div
                                key={index}
                                className={`d-flex justify-content-between w-100 my-1  py-2 ${
                                  condition && 'border-bottom'
                                }`}
                              >
                                <div className="w-100">
                                  <div className="f-14 fw-500">
                                    {getShortString(checkLevel[level], 20)}
                                  </div>
                                  <div className="f-12 text-grey d-flex gap-5">
                                    {curriculumName && (
                                      <div className="d-flex align-items-center f-12 text-grey">
                                        <div className="text-nowrap ">
                                          {getShortString(`${curriculumName.trim()}`, 7)}
                                        </div>
                                        <FiberManualRecordIcon
                                          sx={{ fontSize: 5 }}
                                          className="ml-1"
                                        />
                                      </div>
                                    )}
                                    {isTerm && (
                                      <div className="d-flex align-items-center f-12 text-grey">
                                        <div className="text-nowrap">
                                          {getShortString(`${term.trim()} Term`, 5)}
                                        </div>
                                        <FiberManualRecordIcon
                                          sx={{ fontSize: 5 }}
                                          className="ml-1"
                                        />
                                      </div>
                                    )}
                                    {isAttemptType && (
                                      <div className="d-flex align-items-center f-12 text-grey">
                                        <div className="text-nowrap ">
                                          {getShortString(`${attemptType.trim()} AttemptType`, 7)}
                                        </div>
                                        <FiberManualRecordIcon
                                          sx={{ fontSize: 5 }}
                                          className="ml-1"
                                        />
                                      </div>
                                    )}
                                    {isGroup && (
                                      <div className="d-flex align-items-center f-12 text-grey">
                                        <div className="f-12 text-nowrap">
                                          {getShortString(`${group.trim()} group`, 7)}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div
                                  className="text-primary gap-5 f-12 fw-500 d-flex justify-content-center align-items-center"
                                  onClick={openOrClose}
                                >
                                  <div
                                    className="rounded fw-500 text-center text-black d-flex align-items-center justify-content-center"
                                    style={{ width: 40, height: 40, background: '#F3F4F6' }}
                                  >
                                    {remainingCount}
                                  </div>
                                  <Add sx={{ fontSize: '14px' }} className="text-primary" />
                                  <div className="text-underline text-nowrap">Create Form</div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
              {!isOverView &&
                groupByMissed.flatMap((element) => {
                  return element.filter((item) => item.get('categoryId', '') === tab);
                }).size === 0 && <div className="text-grey text-center f-12">! No Data</div>}
            </div>
          </AccordionDetails>
        </Accordion>
        <Paper className="cursor-pointer position-absolute" onClick={openOrCloseAll} sx={paperSx}>
          {rotate ? (
            <KeyboardArrowLeft className="text-gray" />
          ) : (
            <KeyboardArrowRight className="text-gray" />
          )}
        </Paper>
      </div>
      <Accordion
        className=" box-shadow-static  overflow-hidden mt-3"
        expanded={!toDoAccordion}
        style={{
          ...(rotate
            ? {
                width: '55vh',
                writingMode: 'horizontal-tb',
                transition: '0.5s',
                background: '#FEF3C7',
              }
            : {
                height: '40vh',
                width: '7vh',
                writingMode: 'vertical-rl',
                transition: '0.5s',
                display: 'flex',
                justifyContent: 'center',
                flexDirection: 'row-reverse',
                background: '#FEF3C7',
              }),
        }}
      >
        {rotate ? (
          <div className="d-flex align-items-center justify-content-between gap-15 p-2 py-3">
            <div className="fw-500 text-warning ">
              To Do ({isOverView ? totalAllCategoryTodoCount : totalTodoCount})
            </div>
            <ArrowDropDownIcon
              className="text-black cursor-pointer f-20"
              onClick={() => setOpen((pre) => pre.set('1', !toDoAccordion))}
            />
          </div>
        ) : (
          <div className="d-flex justify-content-between flex-row-reverse flex-grow-1 align-items-center p-2">
            <div className="fw-500 text-warning rotate-180">
              To Do ({isOverView ? totalAllCategoryTodoCount : totalTodoCount})
            </div>
            <ArrowRight
              className="text-black cursor-pointer f-20"
              onClick={() => handleOpenRotate('1')}
            />
          </div>
        )}
        <AccordionDetails className="bg-white">
          <div className="d-flex justify-content-between align-items-center py-1 border-bottom">
            <div className="f-14">{isOverView ? 'Category List' : `${categoryName} List`}</div>
          </div>
          <div>
            {isOverView && groupByTodo.size === 0 && (
              <div className="text-grey text-center f-12 mt-1">! No Data</div>
            )}
          </div>
          <div className="custom-scroll g-config-scrollbar">
            {groupByTodo.entrySeq().map(([key, element], index) => {
              const categoryName = groupByCategory.getIn([key, 'categoryName'], '');
              const level = groupByCategory.getIn([key, 'level'], '');
              const filterData = element.filter((item) => item.get('categoryId', '') === tab);
              const condition = groupByTodo.size - 1 !== index;
              const totalCategoryUnCreatedCount = element.reduce(
                (accumulator, currentElement) =>
                  accumulator + currentElement.get('remainingCount', 0),
                0,
                []
              );
              return (
                <div
                  className={`d-flex justify-content-between w-100 align-items-center  py-1 `}
                  key={index}
                >
                  {isOverView ? (
                    <div
                      className={`d-flex justify-content-between ${
                        condition && 'border-bottom'
                      }  w-100`}
                    >
                      <div className="pb-2">
                        <div className="f-14 fw-500 text-capitalize">{categoryName}</div>
                        <div className="f-12 text-grey">Need to Create</div>
                      </div>
                      <div
                        className="rounded fw-500 text-center d-flex align-items-center justify-content-center"
                        style={{ width: 40, height: 40, background: '#F3F4F6' }}
                      >
                        {totalCategoryUnCreatedCount}
                      </div>
                    </div>
                  ) : (
                    <div className="w-100">
                      {filterData.map((item, index) => {
                        const courseName = item.get('courseName', '');
                        const programName = item.get('programName', '');
                        const institutionName = item.get('institutionName', '');
                        const remainingCount = item.get('remainingCount', 0);
                        const actions = item.get('actions', Map());
                        const isTerm = actions.get('academicTerms', false);
                        const isAttemptType = actions.get('attemptType', false);
                        const isGroup = actions.get('studentGroups', false);
                        const term = item.get('term', '');
                        const attemptType = item.get('attemptTypeName', '');
                        const group = item.get('groupName', '');
                        const curriculumName = item.get('curriculumName', '');
                        const condition = filterData.size - 1 !== index;
                        const checkLevel = {
                          course: courseName,
                          program: programName,
                          institution: institutionName,
                        };
                        return (
                          <div key={index}>
                            <div
                              className={`d-flex justify-content-between w-100 my-1 py-2 ${
                                condition && 'border-bottom'
                              }`}
                            >
                              <div>
                                <div className="f-14 fw-500 ">
                                  {getShortString(checkLevel[level], 20)}
                                </div>
                                <div className="f-12 text-grey d-flex gap-5">
                                  {curriculumName && (
                                    <div className="d-flex align-items-center f-12 text-grey">
                                      <div className="text-nowrap ">
                                        {getShortString(`${curriculumName.trim()}`, 7)}
                                      </div>
                                      <FiberManualRecordIcon
                                        sx={{ fontSize: 5 }}
                                        className="ml-1"
                                      />
                                    </div>
                                  )}
                                  {isTerm && (
                                    <div className="d-flex align-items-center f-12 text-grey">
                                      <div className="text-nowrap ">
                                        {getShortString(`${term.trim()} Term`, 7)}
                                      </div>
                                      {isAttemptType && (
                                        <FiberManualRecordIcon
                                          sx={{ fontSize: 5 }}
                                          className="ml-1"
                                        />
                                      )}
                                    </div>
                                  )}
                                  {isAttemptType && (
                                    <div className="d-flex align-items-center f-12 text-grey">
                                      <div className="text-nowrap ">
                                        {getShortString(`${attemptType.trim()} AttemptType`, 7)}
                                      </div>
                                      {isGroup && (
                                        <FiberManualRecordIcon
                                          sx={{ fontSize: 5 }}
                                          className="ml-1"
                                        />
                                      )}
                                    </div>
                                  )}
                                  {isGroup && (
                                    <div className="d-flex align-items-center f-12 text-grey">
                                      <div className="f-12 text-nowrap">
                                        {getShortString(`${group.trim()} Group`, 7)}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div
                                className="text-primary gap-5 f-12 fw-500 d-flex justify-content-center align-items-center"
                                onClick={openOrClose}
                              >
                                <div
                                  className="rounded fw-500 text-center text-black d-flex align-items-center justify-content-center"
                                  style={{ width: 40, height: 40, background: '#F3F4F6' }}
                                >
                                  {remainingCount}
                                </div>
                                <Add sx={{ fontSize: '14px' }} className="text-primary" />
                                <div className="text-underline text-nowrap">Create Form</div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
            {!isOverView &&
              groupByTodo.flatMap((element) => {
                return element.filter((item) => item.get('categoryId', '') === tab);
              }).size === 0 && <div className="text-grey text-center f-12">! No Data</div>}
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};
