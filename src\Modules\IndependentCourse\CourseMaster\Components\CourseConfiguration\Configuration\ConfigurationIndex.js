import React, { useContext, useRef, useState, useMemo } from 'react';
import { List, fromJS, Map } from 'immutable';
import PropTypes from 'prop-types';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import CourseHeader from 'Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/CourseConfiguration/Component/CourseHeader';
import AdvanceSetting from 'Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/CourseConfiguration/Component/AdvanceSetting/AdvanceSettingIndex';
import { OnlyDuration, SharingAndDuration } from './ConfigurationComponents';
import { t } from 'i18next';
import { sessionDeliveryTypeValidation } from 'Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/utils';
import {
  ParentComponent,
  SessionAndDeliveryTypes,
  AllowEditing,
} from 'Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/CourseConfiguration/Component/Configuration/ConfigurationComponents';
import { independentCourseContext, courseMasterContext } from '../../../../context';
import { checkCourseValidation, checkShareValidation } from '../../../../ICutil';
import { Body, Header } from 'Modules/IndependentCourse/CourseMaster/Modal/ModalComponent';

import { useEffect } from 'react';
function ConfigurationIndex() {
  const courseConfig = useContext(parentContext.courseConfigurationContext);
  const independentCourse = useContext(independentCourseContext);
  const { getToolTipData } = independentCourse;
  const {
    fullCourseDetails,
    setData,
    getSessionDeliveryTypes,
    getSessionFlowList,
    institutionID,
    _course_id,
    sessionDeliveryTypes,
    sessionFlowDetails,
    sessionDeliveryTypesStatus,
    isStandardMode,
    coursesList,
    programCurriculum,
    saveConfigRef,
    subjectsList,
    getIndependentSubjectsList,
  } = courseConfig;
  const isConfigured = fullCourseDetails.get('isConfigured', false);
  const sessionAndDeliveryRef = useRef();
  const allowEditingRef = useRef();
  const advanceSettingsRef = useRef();
  const sharingAndDurationRef = useRef();
  const courseDetailRef = useRef();
  const resetRef = useRef();
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    if (fullCourseDetails.size !== 0 && isConfigured) getIndependentSubjectsList(institutionID);
    setDuration(fullCourseDetails.get('duration', 0));
  }, [fullCourseDetails]); // eslint-disable-line
  useEffect(() => {
    getSessionDeliveryTypes(institutionID, 'independent');
    getSessionFlowList({ pageNo: 1, limit: 200, _course_id }, 'independent');
  }, []); // eslint-disable-line
  useEffect(() => {
    saveConfigRef.current = returnData;
  });
  const configLabels = useMemo(
    () =>
      fromJS({
        program: getToolTipData.programNT,
        programWT: getToolTipData.program,
        course: getToolTipData.courseNT,
        courseWT: getToolTipData.course,
        year: getToolTipData.yearNT,
        yearWT: getToolTipData.year,
        level: getToolTipData.levelNT,
        levelWT: getToolTipData.level,
      }),
    [getToolTipData]
  );
  const updatedCoursesList = useMemo(
    () =>
      coursesList
        .get('courses', List())
        .filter((item) => item.get('_id', '') !== _course_id && item.get('isActive', false)),
    [coursesList, _course_id]
  );
  function fetchPrerequisiteListApi() {}

  const returnData = (isDraft) => {
    const getCourseDetails = () => {
      if (isConfigured) return courseDetailRef.current();
      return {
        courseCode: fullCourseDetails.get('courseCode', ''),
        courseName: fullCourseDetails.get('courseName', ''),
        courseType: 'independent',
        administration: fullCourseDetails.get('administration', Map()).toJS(),
        participating: fullCourseDetails
          .get('participating', List())
          .map((item) => item.delete('_id'))
          .toJS(),
      };
    };
    const constructRequisiteDetails = (selectedRequisite) =>
      updatedCoursesList
        .filter((item) => selectedRequisite.includes(item.get('_id', '')))
        .map((mItem) => ({
          _course_id: mItem.get('_id', ''),
          courseName: mItem.get('courseName', ''),
          courseCode: mItem.get('courseCode', ''),
        }))
        .toJS();
    let courseData = getCourseDetails();
    if (!checkCourseValidation(courseData, setData, getToolTipData)) return false;
    if (!isDraft && (duration === '' || duration === 0)) {
      setData(Map({ message: t('course_validation.duration_weeks') }));
      return false;
    }
    const sessionDeliveryType = sessionAndDeliveryRef.current();
    if (!isDraft && sessionDeliveryTypeValidation({ sessionDeliveryType }, setData)) return false;
    const { courseSharedWith, courseDuration } = sharingAndDurationRef.current();
    if (!isDraft && checkShareValidation(courseSharedWith, courseDuration, setData, getToolTipData))
      return false;
    const [allowEditing, achieveTarget] = allowEditingRef.current();
    const { preRequisite, coRequisite } = advanceSettingsRef.current();
    const preRequisiteCourses = constructRequisiteDetails(preRequisite);
    const coRequisiteCourses = constructRequisiteDetails(coRequisite);
    let Data = {
      _course_id,
      duration,
      allowEditing,
      achieveTarget,
      sessionDeliveryType,
      preRequisiteCourses,
      coRequisiteCourses,
      courseAssignedDetails: [{ courseDuration, courseSharedWith }],
      ...courseData,
    };
    return Data;
  };
  const filteredDataWithoutCurriculum = programCurriculum.filter(
    (item) => item.get('curriculumData').size !== 0
  );
  return (
    <ParentComponent>
      <CourseHeader fullCourseDetails={fullCourseDetails} />
      {fullCourseDetails.size !== 0 && isConfigured && (
        <courseMasterContext.Provider value={{ subjectsList }}>
          <Header />
          <Body
            saveDetails={courseDetailRef}
            enableSaveButton={() => {}}
            isEdit={true}
            course={fullCourseDetails}
          />
        </courseMasterContext.Provider>
      )}
      <OnlyDuration durationData={{ duration, setDuration }} setData={setData} />
      <SessionAndDeliveryTypes
        fullCourseDetails={fullCourseDetails}
        sessionDeliveryTypes={sessionDeliveryTypes}
        isStandardMode={isStandardMode}
        setData={setData}
        sessionFlowDetails={sessionFlowDetails}
        duration={duration}
        sessionAndDeliveryRef={sessionAndDeliveryRef}
        sessionDeliveryTypesStatus={sessionDeliveryTypesStatus}
      />
      <AllowEditing fullCourseDetails={fullCourseDetails} allowEditingRef={allowEditingRef} />
      <SharingAndDuration
        configLabels={configLabels}
        fullCourseDetails={fullCourseDetails}
        setData={setData}
        duration={duration}
        programCurriculum={filteredDataWithoutCurriculum}
        sharingAndDurationRef={sharingAndDurationRef}
      />
      <AdvanceSetting
        fetchPrerequisiteListApi={fetchPrerequisiteListApi}
        preRequisiteCourseList={updatedCoursesList}
        coRequisiteCourseList={updatedCoursesList}
        usedRef={{ advanceSettingsRef, resetRef }}
      />
    </ParentComponent>
  );
}

ConfigurationIndex.propTypes = {
  children: PropTypes.array,
};
export default ConfigurationIndex;
