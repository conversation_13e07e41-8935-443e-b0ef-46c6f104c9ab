import React from 'react';
import PropTypes from 'prop-types';
import { useGoogleLogin } from '@react-oauth/google';
import axios from 'axios';
import GoogleIcon from 'Assets/google.png';

const LoginWithGoogle = ({ callback }) => {
  const login = useGoogleLogin({
    onSuccess: (response) => {
      const accessToken = response.access_token;
      if (accessToken) {
        axios
          .get(`https://www.googleapis.com/oauth2/v1/userinfo`, {
            params: { access_token: accessToken },
            headers: {
              Authorization: `Bearer ${accessToken}`,
              Accept: 'application/json',
            },
          })
          .then((res) => {
            const { id, email } = res.data;
            const requestData = {
              ssoProvider: 'google',
              msTeamToken: accessToken,
              userId: id,
              email,
            };
            callback(requestData);
          })
          .catch((error) => {});
      }
    },
    onError: (error) => {},
  });

  return (
    <div className="text-center pt-2">
      <div
        onClick={login}
        className="login-width d-flex align-items-center justify-content-center btnVia ViaTeams"
      >
        <div>
          <img src={GoogleIcon} alt="google" width={24} />
        </div>
        <div className="pl-2 f-15">Sign in with Google</div>
      </div>
    </div>
  );
};

LoginWithGoogle.propTypes = {
  callback: PropTypes.func,
};

export default LoginWithGoogle;
