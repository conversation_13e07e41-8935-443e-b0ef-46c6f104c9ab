import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setData } from '_reduxapi/global_configuration/v1/actions';
import { List, Map, fromJS } from 'immutable';

export const useManipulateDataForTags = (existData, handleClose, setTagData, editIndex, isEdit) => {
  const checkIsEdit = isEdit ? existData.get(editIndex, Map()) : existData;
  const [inputFields, setInputFields] = useState(checkIsEdit.get('subTag', List()));

  const handleAddSubTag = () => {
    setInputFields((previous) => previous.push(''));
  };

  const handleChangeInput = (index, event) => {
    setInputFields(inputFields.set(index, event.target.value));
  };

  const handleDelete = (index) => {
    setInputFields(inputFields.delete(index));
  };

  const [tags, setTags] = useState(
    fromJS([
      Map({
        name: checkIsEdit.get('name', ''),
        isDefault: checkIsEdit.get('isDefault', false),
        level: checkIsEdit.get('level', ''),
        isActive: true,
        isEdited: true,
        ...(checkIsEdit.get('_id', '') && {
          _id: checkIsEdit.get('_id', ''),
        }),
        subTag: inputFields,
      }),
    ])
  );

  useEffect(() => {
    setTags((prevTags) => prevTags.updateIn([0, 'subTag'], List(), () => inputFields));
  }, [inputFields]);

  const dispatch = useDispatch();
  const setMessage = (message) => {
    dispatch(setData({ message }));
  };
  const handleValidate = () => {
    const tagNames = existData
      .filter((_, index) => index !== editIndex)
      .map((tag) => tag.get('name', ''));
    for (const [index, tag] of tags.entries()) {
      const subTagList = tag.get('subTag', List());
      if (tag.get('name').trim() === '') {
        return setMessage('Tag Name is Required');
      }
      if (subTagList.some((field) => field.trim() === '')) {
        return setMessage('SubTag is required');
      }
      if (tagNames.includes(tags.getIn([0, 'name'], ''))) {
        return setMessage('Duplicate Tag is not allowed');
      }
      if (new Set(subTagList).size !== subTagList.size) {
        return setMessage('Duplicate SubTag is not allowed'); //hasDuplicates
      }
      if (tag.get('level') === '') {
        return setMessage('select the level at tag' + (index + 1));
      }
      // if (tag.get('isDefault', false) && subTagList.size === 0) {
      //   return setMessage('SubTag is required');
      // }
    }
    if (editIndex !== undefined) {
      setTagData((prev) => prev.set(editIndex, tags.get(0, Map())));
      return handleClose();
    }
    setTagData((prev) => prev.merge(tags));
    handleClose();
  };
  // const handleDelete = (index) => {
  //   setTags((prev) => prev.filter((_, i) => i !== index));
  // };
  return [
    tags,
    setTags,
    handleValidate,
    handleAddSubTag,
    inputFields,
    handleChangeInput,
    handleDelete,
    /*handleDelete*/
  ];
};
