import axios from '../../../axios';

export function handleChange(e, name) {
  e.preventDefault();

  if (name === 'fName') {
    this.setState({
      fName: e.target.value,
      fNameError: '',
    });
  }
  if (name === 'mName') {
    this.setState({
      mName: e.target.value,
      mNameError: '',
    });
  }
  if (name === 'lName') {
    this.setState({
      lName: e.target.value,
      lNameError: '',
    });
  }

  if (name === 'familyName') {
    this.setState({
      familyName: e.target.value,
      familyNameError: '',
    });
  }
  if (name === 'residendId') {
    if (isNaN(e.target.value)) return;
    this.setState({
      residendId: e.target.value,
      residendIdError: '',
    });
  }
  if (name === 'passpord') {
    // if (isNaN(e.target.value)) return;
    this.setState({
      passpord: e.target.value,
      passpordError: '',
    });
  }
  if (name === 'empId') {
    this.setState({
      empId: e.target.value,
      empIdError: '',
    });
    axios
      .get(`/staff/employee_id/${e.target.value}`)
      .then((res) => {
        const em = res.data.status;
        if (em === true) {
          this.setState({
            empIdError: 'This Employee Id is Already Exit',
          });
        } else {
          this.setState({
            empIdError: '',
          });
        }
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  }
  if (name === 'collegeId') {
    this.setState({
      collegeId: e.target.value,
      collegeIdError: '',
    });
  }
  if (name === 'buildingNo') {
    this.setState({
      buildingNo: e.target.value,
      buildingNoError: '',
    });
  }
  if (name === 'street') {
    this.setState({
      street: e.target.value,
      streetError: '',
    });
  }
  if (name === 'city') {
    this.setState({
      city: e.target.value,
      cityError: '',
    });
  }
  if (name === 'distric') {
    this.setState({
      distric: e.target.value,
      districError: '',
    });
  }

  if (name === 'zipCode') {
    if (isNaN(e.target.value)) return;
    this.setState({
      zipCode: e.target.value,
      zipCodeError: '',
    });
  }

  if (name === 'phone') {
    if (isNaN(e.target.value)) return;
    this.setState({
      phone: e.target.value,
      phoneError: '',
    });
    axios
      .get(`/staff/mobile/${e.target.value}`)
      .then((res) => {
        const mo = res.data.status;
        if (mo === true) {
          this.setState({
            phoneError: 'This Mobile Number is Already Exit',
          });
        } else {
          this.setState({
            phoneError: '',
          });
        }
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  }
  if (name === 'Additionalphone') {
    if (isNaN(e.target.value)) return;
    this.setState({
      Additionalphone: e.target.value,
      AdditionalphoneError: '',
    });
  }
  if (name === 'unit') {
    if (isNaN(e.target.value)) return;
    this.setState({
      unit: e.target.value,
      unitError: '',
    });
  }
  if (name === 'degree') {
    this.setState({
      degree: e.target.value,
      degreeError: '',
    });
  }

  if (name === 'faName') {
    this.setState({
      faName: e.target.value,
      faNameError: '',
    });
  }

  if (name === 'maName') {
    this.setState({
      maName: e.target.value,
      maNameError: '',
    });
  }

  if (name === 'fEmail') {
    this.setState({
      fEmail: e.target.value,
      fEmailError: '',
    });
  }

  if (name === 'mEmail') {
    this.setState({
      mEmail: e.target.value,
      mEmailError: '',
    });
  }

  if (name === 'fPhone') {
    if (isNaN(e.target.value)) return;
    this.setState({
      fPhone: e.target.value,
      fPhoneError: '',
    });
    axios
      .get(`/staff/mobile/${e.target.value}`)
      .then((res) => {
        const mo = res.data.status;
        if (mo === true) {
          this.setState({
            fPhoneError: 'This Mobile Number is Already Exit',
          });
        } else {
          this.setState({
            fPhoneError: '',
          });
        }
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  }

  if (name === 'mPhone') {
    if (isNaN(e.target.value)) return;
    this.setState({
      mPhone: e.target.value,
      mPhoneError: '',
    });
    axios
      .get(`/staff/mobile/${e.target.value}`)
      .then((res) => {
        const mo = res.data.status;
        if (mo === true) {
          this.setState({
            mPhoneError: 'This Mobile Number is Already Exit',
          });
        } else {
          this.setState({
            mPhoneError: '',
          });
        }
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  }

  if (name === 'gName') {
    this.setState({
      gName: e.target.value,
      gNameError: '',
    });
  }
  if (name === 'gPhone') {
    if (isNaN(e.target.value)) return;
    this.setState({
      gPhone: e.target.value,
      gPhoneError: '',
    });
  }
  if (name === 'gEmail') {
    this.setState({
      gEmail: e.target.value,
      gEmailError: '',
    });
  }
  if (name === 'studendRelation') {
    this.setState({
      studendRelation: e.target.value,
      studendRelationError: '',
    });
  }

  if (name === 'sName') {
    this.setState({
      sName: e.target.value,
      sNameError: '',
    });
  }
  if (name === 'sPhone') {
    if (isNaN(e.target.value)) return;
    this.setState({
      sPhone: e.target.value,
      sPhoneError: '',
    });
  }
  if (name === 'sEmail') {
    this.setState({
      sEmail: e.target.value,
      sEmailError: '',
    });
  }
}

export function handleSelect(e, name) {
  e.preventDefault();

  if (name === 'DOB') {
    this.setState({
      DOB: e.target.value,
      DOBError: '',
    });
  }
  if (name === 'country') {
    this.setState({
      selectedCountry: e.target.value,
      countryError: '',
    });
  }
}

export function onRadioGroupChange(e, name) {
  if (name === 'fnameinput') {
    this.setState({
      selectFname: e.target.value,
      selectFnameError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseFname: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseFname: false,
      });
    }
  }

  if (name === 'mnameinput') {
    this.setState({
      selectMname: e.target.value,
      selectMnameError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseMname: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseMname: false,
      });
    }
  }

  if (name === 'lnameinput') {
    this.setState({
      selectLname: e.target.value,
      selectLnameError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseLname: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseLname: false,
      });
    }
  }

  if (name === 'genderinput') {
    this.setState({
      selectgender: e.target.value,
      genderError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        choosegender: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        choosegender: false,
      });
    }
  }

  if (name === 'empinput') {
    this.setState({
      selectEmp: e.target.value,
      selectEmpError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseEmp: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseEmp: false,
      });
    }
  }

  if (name === 'nationalinput') {
    this.setState({
      selectNational: e.target.value,
      selectNationalError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseNational: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseNational: false,
      });
    }
  }

  if (name === 'contact') {
    this.setState({
      selectContact: e.target.value,
      contactError: '',
    });
    if (e.target.value === 'parent') {
      this.setState({
        chooseContact: 'parent',
      });
    } else if (e.target.value === 'guardien') {
      this.setState({
        chooseContact: 'guardien',
      });
    } else if (e.target.value === 'spouse') {
      this.setState({
        chooseContact: 'spouse',
      });
    }
  }

  if (name === 'Acadamic') {
    this.setState({
      selectAcadamic: e.target.value,
      acadamicError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseAcadamic: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseAcadamic: false,
      });
    }
  }

  if (name === 'Enrollment') {
    this.setState({
      selectEnrollment: e.target.value,
      enrollmentError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseEnrollment: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseEnrollment: false,
      });
    }
  }

  if (name === 'program') {
    this.setState({
      selectProgram: e.target.value,
      programError: '',
    });
    if (e.target.value === 'correct') {
      this.setState({
        chooseProgram: true,
      });
    } else if (e.target.value === 'wrong') {
      this.setState({
        chooseProgram: false,
      });
    }
  }
}
