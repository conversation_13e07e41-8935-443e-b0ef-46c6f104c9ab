import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory } from 'react-router-dom';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { Trans } from 'react-i18next';
import i18n from 'i18next';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import { getInstitutionHeader } from 'v2/utils';
import { useParams } from 'react-router-dom';
import { dString } from 'utils';
import BasicDetails from './BasicDetails';
import StaffUserManagement from './StaffUserManagement/StaffUserManagementIndex';
import '../styles.css';
import ProgramInput from './ProgramInput';
import * as actions from '_reduxapi/program_input/v2/actions';
import LearningOutcomeManagement from './LearningOutcomeManagement';
import { selectProgramDetails } from '_reduxapi/program_input/v2/selectors';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { makeStyles } from '@mui/styles';
function Program(props) {
  const { programDetails, getProgramDetails, getProgramSettings } = props;
  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const params = useParams();
  const history = useHistory();
  const institutionHeader = getInstitutionHeader(history);
  const institutionId = institutionHeader?._institution_id;
  const { programID } = params;
  const getProgramID = dString(programID);
  useEffect(() => {
    getProgramSettings({ institutionId, getProgramID });
    getProgramDetails(getProgramID);
  }, []); // eslint-disable-line

  const useStylesFunction = makeStyles(() => ({
    root: {
      borderBottom: '1px solid #e5e5e5 !important',
    },
    indicator: {
      backgroundColor: '#297fdc',
    },
  }));

  const classes = useStylesFunction();
  return (
    <div>
      <div className="bg-white rounded global-settings">
        <div className="">
          <div className="digi-breadcrumbs p-3">
            <Breadcrumbs
              separator={<NavigateNextIcon fontSize="small" className="digi-blue" />}
              aria-label="breadcrumb"
            >
              <Link
                color="inherit"
                onClick={() => {
                  history.push(
                    `/${params.type}/${params.id}/${params.name}/global-configuration/program`
                  );
                }}
                className="remove_hover"
              >
                <Trans i18nKey={'all_programs'}></Trans>
              </Link>
              <Typography className="digi-blue">
                {getShortString(programDetails.get('name', ''), 15)}&nbsp; - &nbsp;
                {getShortString(programDetails.get('code', ''), 15)}
              </Typography>
            </Breadcrumbs>
          </div>
          <h3 className="f-22 mb-0 mt-3 pl-3 d-flex">
            {getShortString(programDetails.get('name', ''), 15)}&nbsp; - &nbsp;
            {getShortString(programDetails.get('code', ''), 15)}
          </h3>
          <p className="mb-0 pl-3 my-2 d-flex">
            {getShortString(programDetails.get('degree', ''), 15)}
            {programDetails.get('degree', '') !== '' ? '  -  ' : ''}
            {getShortString(programDetails.get('programType', ''), 15)}
          </p>
        </div>
        <div className="ml-3">
          <Tabs
            value={value}
            onChange={handleChange}
            classes={{
              indicator: classes.indicator,
            }}
            textColor="primary"
            className={classes.root}
            variant="scrollable"
            scrollButtons={'off'}
          >
            <Tab label={i18n.t('global_configuration.basic_details')} value={0} />
            {!params.programID && (
              <Tab label={i18n.t('global_configuration.staff_user_management')} value={1} />
            )}
            <Tab label={i18n.t('global_configuration.program_input')} value={2} />
            {!params.programID && (
              <Tab
                label={i18n.t('global_configuration.learning_outcome_management')}
                className="tab-wrapper-max-300"
                value={3}
              />
            )}
          </Tabs>
          {value === 0 && <BasicDetails />}
          {value === 1 && <StaffUserManagement />}
          {value === 2 && <ProgramInput />}
          {value === 3 && <LearningOutcomeManagement />}
        </div>
      </div>
    </div>
  );
}
Program.propTypes = {
  programDetails: PropTypes.instanceOf(Map),
  getProgramDetails: PropTypes.func,
  getProgramSettings: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    programDetails: selectProgramDetails(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Program);
