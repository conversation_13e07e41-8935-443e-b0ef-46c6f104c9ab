import React, { Fragment, useEffect, useState, Suspense } from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';
import { useHistory, useRouteMatch } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Tooltip from '../../../_components/UI/Tooltip/Tooltip';
import { PrimaryButton, FlexWrapper, Null, HeaderRowWrapper } from '../Styled';
import {
  changeTitle,
  indexChange,
  getData,
  interimGetData,
} from '../../../_reduxapi/actions/calender';
import NavRowButtons from '../UtilityComponents/NavRowButtons';
import Loader from '../../../Widgets/Loader/Loader';
import { NotificationManager } from 'react-notifications';
//import CheckIds from '../UtilityComponents/CheckIds';
import CalenderButtons from '../UtilityComponents/CalenderButtons';
import useDataFromStore from '../UtilityComponents/useDataFromStore';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getFormattedHijiriYear } from '../../../utils';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import useCalendar from 'Hooks/useCalendarHook';

const EventsListModal = React.lazy(() => import('../Modal/EventsList/EventsList'));
const DetailWrapper = styled.div`
  display: flex;
  align-items: center;
`;
// margin: 20px 15px;

const Title = styled.div`
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  text-transform: capitalize;
  opacity: 60%;
`;

const CalenderDetails = (props) => {
  const {
    getData,
    id_array,
    apiCalled,
    load,
    total,
    // index,
    changeTitle,
    // indexChange,
    is_interim,
    interimGetData,
    clickedPDF,
    currentProgramCalendarId,
    iframeShow,
    currentPGAccess,
    activeInstitutionCalendar,
    activeTerm,
    publishedStatus,
  } = props;
  const history = useHistory();
  const match = useRouteMatch();
  const active = match.params.year || 'year1';

  const [loaded, setLoaded] = useState(false);
  const { apply1, apply2 } = useDataFromStore(); // eslint-disable-line
  const [eventListShow, setEventListShow] = useState(false);

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlYear = params.get('year');
  let urlName = params.get('pname');
  let urlPgId = params.get('programid');

  const { isCurrentCalendar } = useCalendar();
  const currentCalendar = isCurrentCalendar();
  const academicYearId = activeInstitutionCalendar.get('_id');

  useEffect(() => {
    if (
      apiCalled &&
      !is_interim &&
      activeInstitutionCalendar &&
      !activeInstitutionCalendar.isEmpty()
    ) {
      console.log('landing7'); //eslint-disable-line
      // && !loaded
      getData(
        null,
        NotificationManager,
        setLoaded,
        match.params.id,
        activeInstitutionCalendar.get('_id')
      );
    }
  }, [apiCalled, is_interim, getData, match.params.id, setLoaded, activeInstitutionCalendar]); //loaded

  useEffect(() => {
    if (
      apiCalled &&
      !loaded &&
      is_interim &&
      id_array.length !== 0 &&
      activeInstitutionCalendar &&
      !activeInstitutionCalendar.isEmpty()
    ) {
      interimGetData(
        id_array,
        NotificationManager,
        setLoaded,
        match.params.id,
        activeInstitutionCalendar.get('_id')
      );
    }
  }, [
    apiCalled,
    id_array,
    interimGetData,
    is_interim,
    loaded,
    match.params.id,
    activeInstitutionCalendar,
  ]);

  const eventsListToggle = () => setEventListShow(!eventListShow);

  let eventData =
    props[active] !== undefined && props[active].level !== undefined ? props[active].level : [];

  const [eventBtnActive, setEventBtnActive] = useState(false);

  useEffect(() => {
    let eventBtnActive1 = [];
    if (props[active] && props[active]['level'] && props[active]['level'].length > 0) {
      props[active]['level'].forEach((level) => {
        let reviewBtnActive1a = level.events.length > 0;
        eventBtnActive1.push(reviewBtnActive1a);
      });
    } else {
      eventBtnActive1.push(false);
    }
    let filterBtn = eventBtnActive1 && eventBtnActive1.includes(false) ? false : true;
    setEventBtnActive(filterBtn);
  }, [props]); // eslint-disable-line
  const isActive = !iframeShow
    ? publishedStatus === 'published' ||
      CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
    : iframeShow;
  return (
    <Fragment>
      {isActive ? (
        <>
          {eventListShow === true && (
            <Suspense fallback={''}>
              <EventsListModal
                show={eventListShow}
                clicked={eventsListToggle}
                data={eventData}
                currentSemester={''}
                isInterim={false}
                iframeShow={iframeShow}
                programId={urlPgId}
              />
            </Suspense>
          )}
          <Loader isLoading={load} />
          {/* {apiCalled && loaded && <CheckIds load={loaded} />} */}
          <FlexWrapper className="nav_bg">
            <NavRowButtons theme="active" color="white" type="term" />
          </FlexWrapper>
          <FlexWrapper className="nav_bg">
            <NavRowButtons theme="active" color="white" type="" />
          </FlexWrapper>

          <DetailWrapper>
            {!iframeShow && (
              <Title>
                <div className="ml-5">
                  {`Academic Year ${getFormattedHijiriYear(
                    activeInstitutionCalendar.get('calendar_name', ''),
                    activeInstitutionCalendar.get('start_date', ''),
                    activeInstitutionCalendar.get('end_date', '')
                  )}`}
                </div>
                {/* <IconWrapper
              className={index > 0 && 'avail'}
              onClick={() => {
                indexChange(index - 1, total - 1, id_array, match, history.push);
              }}
            >
              {' '}
              <i className={`fa ${lang === 'ar' ? 'fa-chevron-right' : 'fa-chevron-left'}`}></i>
            </IconWrapper>{' '}
            {`${t('academic_year')} ${(() => {
              if (title) {
                const [yearStart, yearEnd] = title.split('-');
                const hijiriYearStart = convertHijiriYear(yearStart).toString();
                const hijiriYearEnd = convertHijiriYear(yearEnd).toString();

                return `${yearStart} - ${yearEnd.substr(
                  2,
                  4
                )} G (${hijiriYearStart} - ${hijiriYearEnd.substr(2, 4)} H)`;
              }
            })()}`}
            <IconWrapper
              className={index < total - 1 && 'avail'}
              onClick={() => {
                indexChange(index + 1, total - 1, id_array, match, history.push);
              }}
            >
              {' '}
              <i className={`fa ${lang === 'ar' ? 'fa-chevron-left' : 'fa-chevron-right'}`}></i>
            </IconWrapper>{' '}*/}
              </Title>
            )}
            <Null />

            <Tooltip title={t('program_calendar.events_list')}>
              <PrimaryButton
                className={
                  eventBtnActive && currentProgramCalendarId !== null ? 'light' : 'light disable'
                }
                disabled={!(eventBtnActive && currentProgramCalendarId !== null)}
                onClick={eventsListToggle}
              >
                {' '}
                {/* <i className="fa fa-upload"></i> */}
                <Trans i18nKey={'program_calendar.events_list'}></Trans>{' '}
              </PrimaryButton>
            </Tooltip>

            {currentPGAccess && !iframeShow && (
              <Tooltip title={t('program_calendar.set_calendar')}>
                {currentCalendar && (
                  <PrimaryButton
                    className={
                      CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
                        ? 'bordernone'
                        : 'light disable'
                    }
                    disabled={
                      total === 0 &&
                      !CheckPermission(
                        'pages',
                        'Program Calendar',
                        'Dashboard',
                        'Calendar Settings'
                      )
                    }
                    onClick={() => {
                      if (
                        CheckPermission(
                          'pages',
                          'Program Calendar',
                          'Dashboard',
                          'Calendar Settings'
                        )
                      ) {
                        changeTitle(t('role_management.role_actions.Calendar Settings'));
                        history.push(
                          // `/calender/${id}/${match.params.year}?programid=${urlPgId}&year=${urlYear}&pname=${urlName}`
                          `/calender/${academicYearId}/${match.params.year}?programid=${urlPgId}&year=${urlYear}&pname=${urlName}&term=${activeTerm}`
                        );
                      }
                    }}
                  >
                    {' '}
                    <Trans i18nKey={'program_calendar.calendar_settings'}></Trans>
                  </PrimaryButton>
                )}
              </Tooltip>
            )}
          </DetailWrapper>
          <HeaderRowWrapper className={apply1}>
            {/* <Header className={apply2}>Regular term</Header> */}
            <div style={{ float: 'left' }}></div>
            <FlexWrapper>
              {/* <Text>
            Total Credit Hours:{' '}
            {(() => {
              if (props[active]) {
                let data = props[active]['level'];
                let total = 0;
                if (data) {
                  data.forEach((item) => {
                    if (item.rotation === 'no') {
                      item.course.forEach((course) => {
                        let added_credit =
                          course.credit_hours &&
                          course.credit_hours.length > 0 &&
                          course.credit_hours.reduce((sum, el) => {
                            return el.credit_hours + sum;
                          }, 0);

                        total = total + added_credit;
                      });
                    } else {
                      if (item.rotation_course[0]) {
                        item.rotation_course[0]['course'].forEach((course) => {
                          let added_credit =
                            course.credit_hours &&
                            course.credit_hours.length > 0 &&
                            course.credit_hours.reduce((sum, el) => {
                              return el.credit_hours + sum;
                            }, 0);
                          total = total + added_credit;
                        });
                      }
                    }
                  });
                }
                return total;
              }
            })()}
          </Text> */}
              <Null />
              {!is_interim && (
                <CalenderButtons
                  clickedPDF={clickedPDF}
                  iframeShow={iframeShow}
                  currentPGAccess={currentPGAccess}
                  currentCalendar={currentCalendar}
                />
              )}
            </FlexWrapper>
          </HeaderRowWrapper>
        </>
      ) : (
        <div className="pt-3">
          <div className="col-md-12">
            <div className="notpublished-screen">
              <div className="notpublished">
                <h2>
                  {' '}
                  <Trans i18nKey={'program_calendar.still_not_been_published'}></Trans>
                </h2>
              </div>
            </div>
          </div>
        </div>
      )}
    </Fragment>
  );
};

CalenderDetails.propTypes = {
  clickedPDF: PropTypes.func,
  iframeShow: PropTypes.bool,
  currentPGAccess: PropTypes.bool,
  currentProgramCalendarId: PropTypes.string,
  title: PropTypes.string,
  id: PropTypes.string,
  getData: PropTypes.func,
  id_array: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  apiCalled: PropTypes.bool,
  load: PropTypes.bool,
  total: PropTypes.number,
  changeTitle: PropTypes.func,
  is_interim: PropTypes.bool,
  interimGetData: PropTypes.func,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  activeTerm: PropTypes.string,
  publishedStatus: PropTypes.string,
};

const mapStateToProps = ({ calender, interimCalendar, auth }) => ({
  apiCalled: calender.commonApiCalled,
  is_interim: calender.interim,
  load: calender.isLoading,
  year1: calender.year1,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
  title: calender.academic_year_name,
  id: calender.institution_Calender_Id,
  index: calender.acad_index,
  total: calender.total_academic_year_inDB,
  id_array: calender.acad_array,
  currentProgramCalendarId: calender.program_calender_id,
  activeTerm: calender.active_term,
  curriculum_years: calender.curriculum_years,
  publishedStatus: calender.status,
});

export default connect(mapStateToProps, {
  changeTitle,
  indexChange,
  getData,
  interimGetData,
})(CalenderDetails);
