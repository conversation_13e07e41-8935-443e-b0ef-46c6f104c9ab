import React from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import { t } from 'i18next';

function DeleteEventModal({ show, onClose, onDelete, title }) {
  return (
    <Modal show={show} centered size="lg" onHide={onClose}>
      <React.Fragment>
        <Modal.Header closeButton>
          <div className="row w-100">
            <div className="col-md-12 pt-1">{title}</div>
          </div>
        </Modal.Header>
        <Modal.Body>
          <h4>{t('sureDelete')}? </h4>
        </Modal.Body>
        <Modal.Footer>
          <span className="remove_hover btn btn-danger" onClick={onDelete}>
            {t('delete')}
          </span>
          <span className="remove_hover btn btn-primary" onClick={onClose}>
            {t('cancel')}
          </span>
        </Modal.Footer>
      </React.Fragment>
    </Modal>
  );
}

DeleteEventModal.propTypes = {
  show: PropTypes.bool,
  onClose: PropTypes.func,
  onDelete: PropTypes.func,
  title: PropTypes.string,
};

export default DeleteEventModal;
