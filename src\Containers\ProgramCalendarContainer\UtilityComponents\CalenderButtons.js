import React, { Fragment, useState, useEffect } from 'react';
import { useRouteMatch, useHistory } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes, { oneOfType } from 'prop-types';

import { PrimaryButton } from '../Styled';
import Tooltip from '../../../_components/UI/Tooltip/Tooltip';
import { toggleModal, changeTitle } from '../../../_reduxapi/actions/calender';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { Map } from 'immutable';

const CalenderButtons = (props) => {
  const {
    toggleModal,
    is_interim,
    changeTitle,
    currentProgramCalendarId,
    clickedPDF,
    institutionId,
    curriculumYears,
    iframeShow,
    currentPGAccess,
    currentCalendar,
    activeInstitutionCalendar,
  } = props;

  const history = useHistory();
  const match = useRouteMatch();
  const active = match.params.year || 'year1';
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlYear = params.get('year');
  let urlName = params.get('pname');
  let urlPgId = params.get('programid');
  let urlTerm = params.get('term');

  const start = props[active]?.['raw_data']?.['level']?.filter((term) =>
    urlTerm !== '' && urlTerm !== null ? term.term === urlTerm : (urlTerm = 'Summer')
  )[0]?.['start_date'];
  const end = props[active]?.['raw_data']?.['level']?.filter((term) =>
    urlTerm !== '' && urlTerm !== null ? term.term === urlTerm : (urlTerm = 'Summer')
  )[0]?.['end_date'];

  const course1 = props[active]?.['raw_data']?.['level']?.[0]?.['course']?.length;
  const rotational_course =
    props[active]?.['raw_data']?.['level']?.[0]?.['rotation_course']?.[0]?.['course']?.length;

  const run =
    ((course1 && course1 !== 0) || (rotational_course && rotational_course !== 0)) && true;

  const [reviewBtnActive, setReviewBtnActive] = useState(false);

  useEffect(() => {
    let reviewBtnActive1 = [];
    if (curriculumYears && curriculumYears.length > 0) {
      curriculumYears.forEach((year) => {
        let activeYear = props[year.year];
        if (activeYear.level !== undefined && activeYear.level.length > 0) {
          activeYear.level.forEach((check) => {
            let reviewBtnActive1a =
              check.start_date !== undefined &&
              check.end_date !== undefined &&
              check.start_date !== '' &&
              check.end_date !== '' &&
              (check.course.length > 0 || check.rotation_course.length > 0);
            reviewBtnActive1.push(reviewBtnActive1a);
          });
        }
      });
    }
    // let filterReview = reviewBtnActive1 && reviewBtnActive1.includes(false) ? false : true;
    let filterReview = reviewBtnActive1 && reviewBtnActive1.includes(true) ? true : false;
    setReviewBtnActive(filterReview);
  }, [props, curriculumYears]);

  let publishedStatus = 'waiting';
  if (
    props !== undefined &&
    props[active] !== undefined &&
    props[active].status !== undefined &&
    props[active].status === 'published'
  ) {
    publishedStatus = 'published';
  }

  return (
    <Fragment>
      {currentPGAccess && !iframeShow && (
        <>
          <Tooltip title={t('role_management.role_actions.Add Course')}>
            {/* <PrimaryButton
              className={props[active] && start && end ? 'light' : 'none'}
              onClick={() => {
                changeTitle('Add Course');
                toggleModal();
                history.push(
                  `/course/${match.params.id}/${match.params.year}?programid=${urlPgId}&year=${urlYear}&pname=${urlName}`
                );
                localStorage.removeItem('courseInputData');
              }}
            >
              {' '}
              <i className="fas fa-plus"></i> Course
            </PrimaryButton> */}
            {currentCalendar && (
              <PrimaryButton
                className={
                  props[active] &&
                  start &&
                  end &&
                  CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')
                    ? 'light'
                    : 'light disable'
                }
                disabled={!CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')}
                onClick={() => {
                  if (CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')) {
                    changeTitle('Add Course');
                    toggleModal();
                    history.push(
                      `/course-v1/${match.params.id}/${match.params.year}?programid=${urlPgId}&year=${urlYear}&pname=${urlName}&term=${urlTerm}`
                    );
                    localStorage.removeItem('courseInputData');
                  }
                }}
              >
                {' '}
                <i className="fas fa-plus"></i> <Trans i18nKey={'program_calendar.course'}></Trans>
              </PrimaryButton>
            )}
          </Tooltip>
          {currentCalendar && publishedStatus !== 'published' && (
            <Tooltip title={t('role_management.role_actions.Review Calendar')}>
              <PrimaryButton
                className={
                  reviewBtnActive &&
                  currentPGAccess &&
                  currentProgramCalendarId !== null &&
                  currentProgramCalendarId !== '' &&
                  CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Review Calendar')
                    ? 'bordernone'
                    : 'bgdisabled'
                }
                // disabled ={true}
                disabled={
                  !(
                    reviewBtnActive &&
                    currentPGAccess &&
                    currentProgramCalendarId !== null &&
                    currentProgramCalendarId !== '' &&
                    CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Review Calendar')
                  )
                }
                onClick={() => {
                  history.push(
                    `/programcalendar/vice-dean?id=${currentProgramCalendarId}&title=${activeInstitutionCalendar.get(
                      'calendar_name',
                      ''
                    )}&year=${urlYear}&name=${urlName}&icd=${institutionId}&interim=${is_interim}&programid=${urlPgId}`
                  );
                }}
              >
                <Trans i18nKey={'role_management.role_actions.Review Calendar'}></Trans>
              </PrimaryButton>
            </Tooltip>
          )}
        </>
      )}

      {!iframeShow && (
        <Tooltip title={t('program_calendar.export_as_pdf')}>
          <PrimaryButton
            className={
              run &&
              reviewBtnActive &&
              currentProgramCalendarId !== null &&
              currentProgramCalendarId !== ''
                ? //CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Export')
                  'light'
                : 'light disable'
            }
            disabled={
              !(
                run &&
                reviewBtnActive &&
                currentProgramCalendarId !== null &&
                currentProgramCalendarId !== ''
              )
            }
            onClick={clickedPDF}
          >
            {' '}
            {/* <i className="fa fa-upload"></i> */}
            <Trans i18nKey={'program_calendar.export'}></Trans>
          </PrimaryButton>
        </Tooltip>
      )}
    </Fragment>
  );
};

CalenderButtons.propTypes = {
  toggleModal: PropTypes.func,
  is_interim: PropTypes.bool,
  changeTitle: PropTypes.func,
  currentProgramCalendarId: PropTypes.string,
  clickedPDF: PropTypes.func,
  institutionId: PropTypes.string,
  curriculumYears: oneOfType([PropTypes.array, PropTypes.object]),
  iframeShow: PropTypes.bool,
  currentPGAccess: PropTypes.bool,
  currentCalendar: PropTypes.bool,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  const { calender } = state;
  return {
    year1: calender.year1,
    year2: calender.year2,
    year3: calender.year3,
    year4: calender.year4,
    year5: calender.year5,
    year6: calender.year6,
    is_interim: calender.interim,
    currentProgramCalendarId: calender.program_calender_id,
    institutionId: calender.institution_Calender_Id,
    curriculumYears: calender.year_level,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, { toggleModal, changeTitle })(CalenderButtons);
