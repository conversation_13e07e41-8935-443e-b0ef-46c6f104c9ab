import { Map, List } from 'immutable';
const q360State = (state) => state.q360;

const selectedIsLoading = (state) => q360State(state).get('isLoading', false);
const selectMessage = (state) => q360State(state).get('message', '');
const selectedForms = (state) => q360State(state).get('categoryForms', Map());
const selectedConfigureTemplate = (state) => q360State(state).get('configureTemplate', List());
const selectedSignedUrl = (state) => q360State(state).get('signedUrl', Map());
const selectStepTwoDetails = (state) => q360State(state).get('getStepTwoDetails', Map());
const selectQaPcSetting = (state) => q360State(state).get('qaPcSetting', Map());
const selectFormOccurrence = (state) => q360State(state).get('formOccurrences', List());
const selectProgramList = (state) => q360State(state).get('qapcPrograms');
const selectCategoryForm = (state) => q360State(state).get('categoryForms');
const selectProgramDetails = (state) => q360State(state).get('qapcProgramDetails');
const selectProgramCount = (state) => q360State(state).get('programCount');
const selectConfigureTemplate = (state) => q360State(state).get('configureTemplate');
const selectInstitutionList = (state) => q360State(state).get('institutionList');
const selectTermList = (state) => q360State(state).get('termList', List());
const selectIsProgramDetailsLoading = (state) => q360State(state).get('isProgramDetailsLoading');
const selectFormConfigureList = (state) => q360State(state).get('formConfigureList', Map());
const selectSingleFormList = (state) => q360State(state).get('singleFormList', Map());
const selectFormAddendum = (state) => q360State(state).get('formAddendum', Map());
const selectFormAddendumApiCallStatus = (state) => q360State(state).get('formApiCallStatus', false);
const selectFormSettingData = (state) => q360State(state).get('formSettingData', Map());
const selectAcademicYear = (state) => q360State(state).get('academicYears', List());
const selectFormAssuranceList = (state) => q360State(state).get('formAssuranceList', List());
const selectSingleForm = (state) => q360State(state).get('singleForm', List());
const selectSettingTags = (state) => q360State(state).get('settingTags', List());
//INCORPORATE_DATA{
const selectCategoryFormOption = (state) =>
  q360State(state).getIn(['inCorporateFilters', 'categoryForm'], List());
const selectTermAndAttemptType = (state) =>
  q360State(state).getIn(['inCorporateFilters', 'termAndAttemptType'], List());
const selectInCorporateProgramList = (state) =>
  q360State(state).getIn(['inCorporateFilters', 'termAndAttemptType'], List());
const selectInCorporateSectionData = (state) =>
  q360State(state).get('inCorporateSectionData', List());
const selectApiCallDoneForIncorporate = (state) =>
  q360State(state).get('apiCallDoneForIncorporate', false);

//INCORPORATE_DATA}
const selectRpActions = (state) => q360State(state).get('rpActions', List());
const selectQapcRoles = (state) => q360State(state).get('qapcRoles', List());
const selectApprovalCategoryList = (state) => q360State(state).get('approvalCategoryList', List());
const selectApproverPending = (state) => q360State(state).get('approverPending', Map());
const selectFromApplicationList = (state) => q360State(state).get('fromApplicationList', Map());
const selectReferenceDocumentDetails = (state) =>
  q360State(state).get('referenceDocumentDetails', Map());
const selectSearchProgramDetails = (state) => q360State(state).get('searchProgramDetails', Map());
const selectLevelRoleUserList = (state) => q360State(state).get('levelRoleUserList', Map());
const selectReferenceFromAttachments = (state) =>
  q360State(state).get('referenceFromAttachments', Map());
const selectIncorporateFromWithData = (state) =>
  q360State(state).get('incorporateFromWithData', Map());
const selectTodoMissedData = (state) => q360State(state).get('todoMissedData', Map());
const selectCategoryList = (state) => q360State(state).get('categoryList', Map());
const selectEvidenceDocument = (state) => q360State(state).get('evidenceDocument', Map());

//QAPC DASHBOARD
const selectQapcDashboardSettings = (state) => q360State(state).get('dashboardSettings', Map());
const selectQapcDashboardGraphData = (state) => q360State(state).get('dashboardGraphData', Map());
const selectIncorporateTooltipData = (state) =>
  q360State(state).get('incorporateTooltipData', Map());
const selectedIsDashboardLoading = (state) => q360State(state).get('isDashboardLoading', false);
const selectedDiscussionLists = (state) => q360State(state).get('discussionLists', List());
const selectedDiscussionUnreadDetails = (state) =>
  q360State(state).get('discussionUnreadDetails', List());

export {
  selectedForms,
  selectedIsLoading,
  selectedSignedUrl,
  selectStepTwoDetails,
  selectQaPcSetting,
  selectMessage,
  selectedConfigureTemplate,
  selectFormOccurrence,
  selectProgramList,
  selectCategoryForm,
  selectProgramDetails,
  selectProgramCount,
  selectConfigureTemplate,
  selectInstitutionList,
  selectTermList,
  selectIsProgramDetailsLoading,
  selectFormConfigureList,
  selectSingleFormList,
  selectFormAddendum,
  selectFormSettingData,
  selectAcademicYear,
  selectFormAssuranceList,
  selectSingleForm,
  selectSettingTags,
  selectCategoryFormOption,
  selectTermAndAttemptType,
  selectInCorporateProgramList,
  selectFormAddendumApiCallStatus,
  selectInCorporateSectionData,
  selectRpActions,
  selectQapcRoles,
  selectReferenceDocumentDetails,
  selectSearchProgramDetails,
  selectReferenceFromAttachments,
  selectApiCallDoneForIncorporate,
  selectIncorporateFromWithData,
  selectTodoMissedData,
  selectCategoryList,
  selectEvidenceDocument,
  selectApprovalCategoryList,
  selectApproverPending,
  selectFromApplicationList,
  selectLevelRoleUserList,
  selectQapcDashboardSettings,
  selectQapcDashboardGraphData,
  selectIncorporateTooltipData,
  selectedIsDashboardLoading,
  selectedDiscussionLists,
  selectedDiscussionUnreadDetails,
};
