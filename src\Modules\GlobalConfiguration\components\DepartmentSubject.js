import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { Accordion, Card } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectBasicDetails, selectDepartment } from '_reduxapi/global_configuration/selectors';
import Mode1 from '../../../Assets/img/globalConfiguration/College1.svg';
import Mode2 from '../../../Assets/img/globalConfiguration/College2.svg';
import i18n from '../../../i18n';
function ProgramInput({
  globalSettings,
  getDepartmentSetting,
  department,
  updateDepartmentSetting,
}) {
  const [arrow, setArrow] = useState('');
  const [radioValue, setRadioValue] = useState(
    Map({
      departmentMode: '',
    })
  );
  const settingId = globalSettings.get('_id', '');

  useEffect(() => {
    settingId && getDepartmentSetting({ settingId });
  }, []); // eslint-disable-line

  useEffect(() => {
    if (department.size > 0) {
      const selectedDepartmentMode = department.find((item) => item.get('isActive', false));
      const updatedValue = radioValue.update((key) => {
        return key.set('departmentMode', selectedDepartmentMode?.get('labelName', ''));
      });
      setRadioValue(updatedValue);
    }
  }, [department]); // eslint-disable-line

  const displayRow = (key, heading) => {
    return (
      <>
        <div className="row">
          <div className="col-md-12">
            <Accordion.Toggle
              as={Card.Header}
              eventKey={key}
              className="icon_remove_leave pl-0"
              onClick={() => {
                setArrow({ [key]: !arrow[key] });
              }}
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex">
                  <span>
                    <i
                      className={`fa fa-chevron-${arrow[key] ? 'up' : 'down'} f-14 icon-blue`}
                      aria-hidden="true"
                    ></i>
                  </span>

                  <div className="pl-3">
                    <div className="f-16 digi-brown remove_hover">
                      <b>
                        <Trans i18nKey={heading}></Trans>
                      </b>
                      {!arrow[key] && (
                        <div className="text-grey f-15">
                          <span>
                            <>
                              {radioValue.get('departmentMode', '') === 'Mode 1'
                                ? i18n.t('Mode1')
                                : i18n.t('Mode2')}
                            </>
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </Accordion.Toggle>
          </div>
        </div>
      </>
    );
  };

  const handleRadioButton = (key, value, id) => {
    let updatedValue = radioValue.set(key, value);
    setRadioValue(updatedValue);
    updateDepartmentSetting({
      id,
      settingId,
    });
  };

  return (
    <React.Fragment>
      <div className="pl-3">
        <Accordion defaultActiveKey="">
          {displayRow('1', i18n.t('department_and_subjects'))}
          <Accordion.Collapse eventKey="1" className="bg-white">
            <Card.Body className="innerbodyLeave border-top-remove">
              <div className="container">
                <div className="float-left">
                  <FormControl component="fieldset">
                    <div className="mb-3">
                      {department.size > 0 &&
                        department.map((item, key) => (
                          <div className="mb-3" key={key}>
                            <FormControlLabel
                              className="float-left"
                              value={item.get('labelName', '')}
                              label={
                                item.get('labelName', '') === 'Mode 1'
                                  ? i18n.t('Mode1')
                                  : i18n.t('Mode2')
                              }
                              control={
                                <Radio
                                  color="primary"
                                  checked={
                                    radioValue.get('departmentMode', '') ===
                                    item.get('labelName', '')
                                  }
                                />
                              }
                              onChange={() =>
                                handleRadioButton(
                                  'departmentMode',
                                  item.get('labelName', ''),
                                  item.get('_id')
                                )
                              }
                            />
                            <div className="clearfix"> </div>
                            <div className="col-md-12 col-12 col-xl-12 col-lg-12">
                              <img
                                src={item.get('labelName', '') === 'Mode 1' ? Mode1 : Mode2}
                                alt="mode1"
                                className="img-fluid"
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  </FormControl>
                </div>
                <div className="clearfix"> </div>
              </div>
            </Card.Body>
          </Accordion.Collapse>
          <hr />
        </Accordion>
      </div>
    </React.Fragment>
  );
}
ProgramInput.propTypes = {
  globalSettings: PropTypes.instanceOf(Map),
  department: PropTypes.oneOfType([PropTypes.instanceOf(Map), PropTypes.instanceOf(List)]),
  getDepartmentSetting: PropTypes.func,
  updateDepartmentSetting: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    globalSettings: selectBasicDetails(state),
    department: selectDepartment(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ProgramInput);

// import React, { useState } from 'react';
// import PropTypes from 'prop-types';
// import { AccordionProgramInput } from './ReusableComponent';
// import ModeOfDepartment from './ModeOfDepartment';

// function DepartmentSubject() {
//   const [open, setOpen] = useState(false); //eslint-disable-line

//   return (
//     <AccordionProgramInput
//       summaryChildren={<div>Department and Subject</div>}
//       detailChildren={<ModeOfDepartment />}
//       onClick={() => setOpen((previousData) => !previousData)}
//       open={open}
//     />
//   );
// }
// DepartmentSubject.propTypes = {
//   children: PropTypes.array,
// };
// export default DepartmentSubject;
