import React, { useEffect, useState, useCallback, Suspense } from 'react';
import { connect } from 'react-redux';
import { withRout<PERSON>, <PERSON> } from 'react-router-dom';
import { compose } from 'redux';
import PropTypes from 'prop-types';

import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import { t } from 'i18next';
import { List, Map } from 'immutable';
import moment from 'moment';
import Switch from 'react-switch';
import DialogTitle from '@mui/material/DialogTitle';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import MButton from 'Widgets/FormElements/material/Button';
import useDebounce from 'Hooks/useDebounceHook';
import { setSwitchCalendar } from '_reduxapi/actions/auth';
import * as actions from '_reduxapi/institution_calendar/actions';
import AddNew from '../../Widgets/Button/AddNew';
import { selectAllCalendarLists, selectIsLoading } from '_reduxapi/institution_calendar/selectors';
import { jsUcfirstAll } from 'utils';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CalenderIndex from './index';
import { CheckPermission } from 'Modules/Shared/Permissions';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';

const DeleteEventModal = React.lazy(() => import('./Modals/DeleteEventModal'));

const StyledPaper = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.mode === 'dark' ? '#1A2027' : '#fff',
  ...theme.typography.body2,
  padding: theme.spacing(2),
  color: theme.palette.text.primary,
}));

const handleKeyPress = (event) => {
  const charCode = event.which ? event.which : event.keyCode;
  // Prevent typing numbers
  if (charCode >= 48 && charCode <= 57) {
    event.preventDefault();
  }
};
function AllCalendarList({
  getAllCalendarLists,
  calendarLists,
  updateAllCalendarLists,
  deleteInstitutionCalendar,
  setData,
  setSwitchCalendar,
  activeInstitutionCalendar,
  isLoading,
}) {
  useEffect(() => {
    getAllCalendarLists();
  }, [getAllCalendarLists]);

  const getTranslatedDate = (start, end) => {
    return `${`${moment(start).format('Do')} ${t(
      `calender.${moment(start).format('MMM')}`
    )} ${moment(start).format('YY')}`} - ${`${moment(end).format('Do')} ${t(
      `calender.${moment(end).format('MMM')}`
    )} ${moment(end).format('YY')}`}`;
  };

  // const dateFormatter = (start) => {
  //   return `${`${moment(start).format('Do')} ${t(
  //     `calender.${moment(start).format('MMM')}`
  //   )} ${moment(start).format('YY')}`}`;
  // };

  const [calendarName, setCalendarName] = useState(Map({ name: '', index: 0, isActive: '' }));
  const [changedInput, setChangedInput] = useState('');
  const [open, setOpen] = useState(false);
  const [editedData, setEditedData] = useState(Map());
  const [date, setDate] = useState(Map({ end_date: '' }));
  const [deleteData, setDeleteData] = useState(Map());
  // const [calendarDate, setCalendarDate] = useState(Map({ index: 0, start_date: '', end_date: '' }));

  const debouncedSearchTerm = useDebounce(calendarName, changedInput === 'TextField' ? 1500 : 0);
  // const debouncedDate = useDebounce(calendarDate, 500);

  function dateChange(newValue, type = 'start_date') {
    setDate(date.set(type, newValue));
    // const formatDate = moment(new Date(newValue)).format('YYYY-MM-DD');
    // if (type === 'start_date') {
    //   setCalendarDate(
    //     calendarDate.set('start_date', formatDate).set('end_date', '').set('index', index)
    //   );
    // } else {
    //   setCalendarDate(calendarDate.set('end_date', formatDate).set('index', index));
    // }
  }

  const updateCallBack = useCallback(() => {
    let data = {};

    const findData = calendarLists
      .find((_, index) => debouncedSearchTerm.get('index', '') === index)
      .get('_id', '');
    if (debouncedSearchTerm.get('action', '') === 'input') {
      const isCalendarNameAlreadyExists = calendarLists.some(
        (list, listIndex) =>
          listIndex !== debouncedSearchTerm.get('index', '') &&
          list.get('calendar_name').trim().toLowerCase() ===
            debouncedSearchTerm.get('name', '').trim().toLowerCase()
      );
      if (isCalendarNameAlreadyExists) {
        return setData({ message: 'Calendar name already exists' });
      }

      data.calendar_name = debouncedSearchTerm.get('name', '');
    } else if (debouncedSearchTerm.get('action', '') === 'switch') {
      data.isActive = debouncedSearchTerm.get('isActive', false);
    }
    updateAllCalendarLists(findData, data);

    let testCalendar = activeInstitutionCalendar.toJS();
    setSwitchCalendar({
      ...testCalendar,
      calendar_name: calendarName.get('name', '').trim(),
    });
  }, [debouncedSearchTerm, updateAllCalendarLists]); //eslint-disable-line

  useEffect(
    () => {
      if (debouncedSearchTerm.get('isActive', '') !== '') {
        updateCallBack();
      } else if (debouncedSearchTerm.get('name', '') !== '') {
        updateCallBack();
      }
    },
    [debouncedSearchTerm, updateCallBack] // Only call effect if debounced search term changes
  );

  const handleOpen = (calendar) => {
    setOpen(!open);
    setEditedData(calendar);
    setDate(date.set('end_date', ''));
  };

  const handleClose = () => {
    setOpen(!open);
  };

  const parseDate = (str) => {
    if (!str) return null;
    const date = new Date(str);
    return moment(date).format('YYYY-MM-DD') + 'T00:00:00.000Z';
  };

  const handleSubmit = () => {
    let data = {};
    data.start_date = editedData.get('start_date', '');
    data.end_date =
      date.get('end_date', '') !== ''
        ? parseDate(date.get('end_date', ''))
        : editedData.get('end_date', '');
    updateAllCalendarLists(editedData.get('_id'), data, () => handleClose());
  };

  const handleDelete = (calendar) => {
    setDeleteData(calendar);
  };

  const calendarDelete = () => {
    deleteInstitutionCalendar(deleteData.get('_id'), () => setDeleteData(Map()));
  };

  const handleDisabled = () => {
    return (
      date.get('end_date', '') === '' ||
      date.get('end_date', '') === null ||
      isNaN(Date.parse(date.get('end_date', '')))
    );
  };

  const calendarEdit = CheckPermission(
    'pages',
    'Calendar',
    'Institution Calendar',
    'Calendar Edit'
  );
  const calendarStatus = CheckPermission(
    'pages',
    'Calendar',
    'Institution Calendar',
    'Calendar Active and Inactive'
  );

  const items = [{ to: '#', label: 'Institution Calendar Lists' }];

  return (
    <React.Fragment>
      <Breadcrumb>
        {items &&
          items.map(({ to, label }, index) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={index} to={to}>
              {label}
            </Link>
          ))}
      </Breadcrumb>
      <CalenderIndex />
      <Grid
        container
        sx={{
          my: 1,
          mx: 'auto',
          p: 2,
          pl: 3,
        }}
      >
        <AddNew create={t('backToCalendar')} to={'/InstitutionCalendar'} />
      </Grid>
      <Box sx={{ flexGrow: 1, overflow: 'hidden', px: 3 }}>
        <StyledPaper
          sx={{
            my: 1,
            mx: 'auto',
            p: 2,
            pb: 4,
          }}
        >
          <Grid container wrap="nowrap" spacing={2} sx={{ fontWeight: 'bold' }}>
            <Grid item xs={3} md={3} sm={4}>
              Calendar Name
            </Grid>
            <Grid item xs={3} md={3} sm={3}>
              Start & End Date
            </Grid>
            <Grid item xs={2} md={2} sm={2}>
              Status
            </Grid>
            <Grid item xs={2} md={2} sm={2}>
              Active / InActive
            </Grid>
            <Grid item xs={2} md={2} sm={1}>
              Action
            </Grid>
          </Grid>
          <Grid container wrap="nowrap" spacing={2}></Grid>
        </StyledPaper>

        <div className="calender-list">
          {calendarLists.map((calendar, index) => {
            return (
              <StyledPaper
                key={index}
                sx={{
                  my: 1,
                  mx: 'auto',
                  p: 2,
                }}
              >
                <Grid container wrap="nowrap" spacing={2}>
                  <Grid item xs={3} md={3} sm={4}>
                    <FormControl className="calender-list-textField" variant="standard">
                      <TextField
                        inputProps={{
                          maxLength: 25,
                          readOnly: !calendar.get('isActive', false) || !calendarEdit,
                        }}
                        id="standard-basic"
                        variant="standard"
                        defaultValue={calendar.get('calendar_name', '')}
                        placeholder={'Enter Calendar Name'}
                        onChange={(e) => {
                          setChangedInput('TextField');
                          setCalendarName(
                            calendarName
                              .set('name', e.target.value.trim())
                              .set('index', index)
                              .set('isActive', '')
                              .set('action', 'input')
                          );
                        }}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={3} md={3} sm={3}>
                    {getTranslatedDate(
                      calendar.get('start_date', new Date()),
                      calendar.get('end_date', new Date())
                    )}
                  </Grid>
                  <Grid item xs={2} md={2} sm={2}>
                    {jsUcfirstAll(calendar.get('status', 'Not Published'))}
                  </Grid>
                  <Grid item xs={2} md={2} sm={2}>
                    <Switch
                      disabled={!calendarStatus}
                      checked={calendar.get('isActive', false)}
                      onChange={() => {
                        setChangedInput('Switch');
                        setCalendarName(
                          calendarName
                            .set('isActive', !calendar.get('isActive', false))
                            .set('index', index)
                            .set('name', calendar.get('calendar_name', ''))
                            .set('action', 'switch')
                        );
                      }}
                      onColor="#86d3ff"
                      onHandleColor="#2693e6"
                      handleDiameter={20}
                      uncheckedIcon={false}
                      checkedIcon={false}
                      boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                      activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                      height={19}
                      width={40}
                      className="react-switch"
                    />
                  </Grid>
                  <Grid item xs={2} md={2} sm={1}>
                    {calendarEdit && (
                      <EditIcon
                        sx={{ cursor: calendar.get('isActive', false) ? 'pointer' : 'not-allowed' }}
                        onClick={() =>
                          calendar.get('isActive', false) ? handleOpen(calendar) : {}
                        }
                      />
                    )}
                    {CheckPermission(
                      'pages',
                      'Calendar',
                      'Institution Calendar',
                      'Calendar Delete'
                    ) &&
                      calendar.get('status', '') !== 'published' && (
                        <DeleteIcon
                          sx={{ cursor: 'pointer', marginLeft: '0.5em' }}
                          onClick={() => handleDelete(calendar)}
                        />
                      )}
                  </Grid>
                </Grid>
              </StyledPaper>
            );
          })}
        </div>
      </Box>

      {open && (
        <Suspense fallback="">
          <Dialog onClose={handleClose} open={open}>
            <DialogTitle>Update Date ({editedData.get('calendar_name', '')})</DialogTitle>
            <DialogContent>
              <Grid item xs={2} md={2} sx={{ marginTop: '1em' }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="Start Date"
                    placeholder="Start Date"
                    value={
                      date.get('start_date', '') === ''
                        ? editedData.get('start_date', new Date())
                        : date.get('start_date', '')
                    }
                    // onChange={(newValue) => dateChange(newValue, 'start_date')}
                    onChange={() => {}}
                    renderInput={(params) => <TextField {...params} onKeyPress={handleKeyPress} />}
                    readOnly
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={2} md={2} sx={{ marginTop: '1em' }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="End Date"
                    placeholder="End Date"
                    value={
                      date.get('end_date', '') === ''
                        ? editedData.get('end_date', new Date())
                        : date.get('end_date', '')
                    }
                    minDate={editedData.get('end_date', new Date())}
                    onChange={(newValue) => dateChange(newValue, 'end_date')}
                    renderInput={(params) => <TextField {...params} onKeyPress={handleKeyPress} />}
                    PopperProps={{
                      placement: 'right',
                    }}
                  />
                </LocalizationProvider>
              </Grid>
            </DialogContent>
            <DialogActions>
              <MButton clicked={handleClose} variant="outlined" color="primary">
                Cancel
              </MButton>
              <MButton
                variant="contained"
                color="primary"
                disabled={handleDisabled()}
                clicked={handleSubmit}
              >
                Update
              </MButton>
            </DialogActions>
          </Dialog>
        </Suspense>
      )}
      {!deleteData.isEmpty() && (
        <Suspense fallback="">
          <DeleteEventModal
            show={true}
            onClose={() => setDeleteData(Map())}
            onDelete={() => calendarDelete()}
            title="Delete Institution Calendar"
          />
        </Suspense>
      )}
    </React.Fragment>
  );
}

AllCalendarList.propTypes = {
  calendarLists: PropTypes.instanceOf(List),
  getAllCalendarLists: PropTypes.func,
  updateAllCalendarLists: PropTypes.func,
  deleteInstitutionCalendar: PropTypes.func,
  setData: PropTypes.func,
  setSwitchCalendar: PropTypes.func,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  isLoading: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    calendarLists: selectAllCalendarLists(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    isLoading: selectIsLoading(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, { ...actions, setSwitchCalendar })
)(React.memo(AllCalendarList));
