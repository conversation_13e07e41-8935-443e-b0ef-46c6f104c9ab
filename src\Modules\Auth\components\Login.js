import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Map } from 'immutable';
import { Redirect, with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';
import MButton from 'Widgets/FormElements/material/Button';
import DS_logo from 'Assets/ds_logo.svg';
import * as Constants from '../../../constants';
import * as actions from '_reduxapi/actions/index';
import { getActiveVersion } from '../../../utils';
import AuthIndex from '../index';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import Footer from 'Shared/Footer';
import { getLang } from '../../../utils';
import i18n from '../../../i18n';
import LocalStorageService from 'LocalStorageService';

const emailVal = Constants.EMAIL_VALIDATION;
class Login extends Component {
  constructor() {
    super();
    this.state = {
      email: '',
      psw: '',
      delayMsg: true,
      passwordType: 'password',
    };
    this.emailRef = React.createRef();
    this.passwordRef = React.createRef();
  }

  componentDidMount() {
    if (this.emailRef.current !== null) {
      this.emailRef.current.focus();
    }
  }

  showPassword = () => {
    if (this.passwordRef.current !== null) {
      const type = this.passwordRef.current.type;
      const updatedValue = type === 'password' ? 'text' : 'password';
      this.passwordRef.current.type = updatedValue;
      this.setState({ passwordType: updatedValue });
    }
  };

  onChange = (e, name) => {
    e.preventDefault();
    this.setState({
      [name]: e.target.value,
    });
  };

  validation = () => {
    const { onSetData } = this.props;
    const { email, psw } = this.state;

    if (!email) {
      onSetData({ message: i18n.t('user_management.email_required') });
      return false;
    }
    if (!emailVal.test(email)) {
      onSetData({ message: i18n.t('leaveManagement.errorMsg.validMail') });
      return false;
    }
    if (!psw) {
      onSetData({ message: i18n.t('Auth_words.password_is_required') });
      return false;
    }
    if (psw.length <= 7) {
      onSetData({ message: i18n.t('Auth_words.min_eignt') });
      return false;
    }

    return true;
  };

  handleSignIn = (e) => {
    e.preventDefault();
    if (this.validation()) {
      const { onAuthLogin, history } = this.props;
      const authData = {
        email: this.state.email,
        password: this.state.psw,
        device_type: 'web',
      };
      onAuthLogin({ ...authData }, history);
    }
  };

  renderForgotPassword(isAdminLogin = false) {
    return (
      <p
        className={`pt-${isAdminLogin ? '4' : '3'} mb-0 ${
          isAdminLogin ? 'text-center' : 'text-center'
        }`}
      >
        <Link exact="true" to="/forgot-password">
          <Trans i18nKey={'forgot'}></Trans>
        </Link>
      </p>
    );
  }

  render() {
    const { passwordType } = this.state;
    const { isAdminLogin, isAdmin, isInstitutionOnBoarded, t } = this.props;

    let redirectPath = LocalStorageService.getCustomToken('redirectPath');
    let redirectUrl = null;
    if (this.props.isAuthenticated && this.state.delayMsg) {
      if (redirectPath !== '' && redirectPath !== null && redirectPath !== '/login') {
        redirectUrl = <Redirect to={redirectPath} />;
      } else if (isAdmin && !isInstitutionOnBoarded) {
        redirectUrl = <Redirect to="/institution/onboarding" />;
      } else {
        redirectUrl = !isAdmin ? (
          <Redirect to="/overview" />
        ) : getActiveVersion() === '/v2' ? (
          <Redirect to={`/university-Details`} />
        ) : (
          <Redirect to="/overview" />
        );
      }
    }

    return (
      <div>
        <AuthIndex />
        <div>
          {redirectUrl}
          <div className="login-bg">
            <div className="grandParentContainer">
              <div className="parentContainer">
                <div className="row justify-content-center">
                  <div className="col-12 col-xl-12 col-lg-12 col-md-12">
                    <h3 className="text-center">
                      {' '}
                      <img src={DS_logo} alt="Digischeduler" />
                    </h3>
                  </div>
                </div>

                <div className="row justify-content-center pt-1">
                  <div className="col-12 col-xl-12 col-lg-12 col-md-12">
                    <div className="outter-login">
                      <div>
                        <p className="f-20 pt-1 text-center text-gray text-uppercase">
                          <Trans i18nKey={'add_colleges.login'}></Trans>
                        </p>
                      </div>

                      <React.Fragment>
                        <form>
                          <div className="pt-2">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'text'}
                              placeholder={t('enter_email')}
                              variant={'outlined'}
                              size={'small'}
                              label={i18n.t('email')}
                              changed={(e) => this.onChange(e, 'email')}
                              value={this.state.email}
                              elementConfig={{
                                ref: this.emailRef,
                                autoFocus: true,
                              }}
                            />
                          </div>
                          <div className="pt-2 position-relative">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={passwordType}
                              placeholder={t('enter_password')}
                              variant={'outlined'}
                              size={'small'}
                              label={i18n.t('password')}
                              changed={(e) => this.onChange(e, 'psw')}
                              value={this.state.psw}
                              elementConfig={{
                                ref: this.passwordRef,
                              }}
                            />

                            <span
                              className={`remove_hover ${
                                getLang() === 'ar' ? 'p-viewer-ar' : 'p-viewer'
                              }`}
                            >
                              <i
                                className={`fa fa-eye${passwordType === 'text' ? '-slash' : ''}`}
                                aria-hidden="true"
                                onClick={this.showPassword}
                              >
                                {' '}
                              </i>
                            </span>
                          </div>
                          <div className="pt-3">
                            <MButton type="submit" clicked={this.handleSignIn} fullWidth>
                              <Trans i18nKey={'add_colleges.login'}></Trans>
                            </MButton>
                          </div>
                          {!isAdminLogin && this.renderForgotPassword()}
                          <hr />
                          <div className="d-flex justify-content-center">
                            <Trans i18nKey={'Auth_words.Don_t_have_an_account'}></Trans>{' '}
                            <Link exact="true" to="/signup">
                              <p className={'ml-2 mb-0 '}>
                                <Trans i18nKey={'Sign_Up'}></Trans>
                              </p>
                            </Link>
                          </div>
                        </form>
                      </React.Fragment>
                    </div>
                  </div>
                </div>
                <div className="row justify-content-center pt-1 pb-1">
                  <Footer />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

Login.propTypes = {
  isAdminLogin: PropTypes.bool,
  isAdmin: PropTypes.bool,
  isInstitutionOnBoarded: PropTypes.bool,
  t: PropTypes.func,
  isAuthenticated: PropTypes.bool,
  history: PropTypes.object,
  onAuth: PropTypes.func,
  authLogin: PropTypes.func,
  onAuthLogin: PropTypes.func,
  onSetData: PropTypes.func,
  loggedInUserData: PropTypes.instanceOf(Map),
};

Login.defaultProps = {
  isAdminLogin: false,
};

const mapStateToProps = (state) => {
  return {
    isAuthenticated: state.auth.token !== null,
    isAdmin: state.auth.isAdmin,
    isInstitutionOnBoarded: state.auth.isInstitutionOnboarded,
    loggedInUserData: selectUserInfo(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    onAuth: (data) => dispatch(actions.auth(data)),
    onAuthLogin: (data, history) => dispatch(actions.authLogin(data, history)),
    onSetData: (data) => dispatch(actions.setData(data)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(withTranslation()(Login)));
