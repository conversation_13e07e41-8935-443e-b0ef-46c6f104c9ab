import React, { useState } from 'react';
import { Trans } from 'react-i18next';
import { fromJS, Map } from 'immutable';
import { withRouter } from 'react-router-dom';

import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import AddEdit from '../Modal/AddEdit/AddEdit';
import * as actions from '_reduxapi/institution/actions';
import { checkValidation } from '../../UniversityDetails/udUtil';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import CancelModal from '../Modal/Cancel';
import { getInstituteDate } from 'v2/utils';

function CollegeHeading({
  addInstitution,
  history,
  getColleges,
  setData,
  loggedInUserData,
  count,
  searchFilter,
}) {
  const [show, setShow] = useState(false);
  const [cancelShow, setCancelShow] = useState(false);
  const [isChanged, setIsChanged] = useState(false);

  const getUserID = () => {
    return loggedInUserData.getIn(['university', 'institutionId'], '');
  };
  const callId = getUserID();
  const callBackFunctions = () => {
    const { search, option, sort } = searchFilter;
    getColleges({ id: callId, sort, status: option, search });
    setShow(false);
    setIsChanged(false);
  };
  const fetchData = (data) => {
    if (checkValidation(data, 'college', setData)) {
      const formData = new FormData();
      for (const [key, value] of Object.entries(data)) {
        formData.append(key, value);
      }
      formData.append('type', 'College');
      formData.append('parentInstitute', callId);
      addInstitution({
        operation: 'create',
        formData,
        history,
        callBack: callBackFunctions,
        calledOn: 'addCollege',
      });
    }
  };

  const handleBack = () => {
    setShow(isChanged ? true : false);
    setCancelShow(isChanged ? true : false);
  };
  const instituteDate = getInstituteDate();
  const hasEnabled = count < instituteDate.get('noOfColleges', 0);

  return (
    <>
      <div className="d-flex justify-content-between">
        <h5 className="font-weight-normal mt-3 ml-0 mb-2">
          <Trans i18nKey={'add_colleges.all_colleges'}></Trans>
        </h5>

        <div className="mt-2">
          {hasEnabled && (
            <p
              className="text-blue remove_hover mb-0 pt-2"
              onClick={() => {
                setShow(true);
              }}
            >
              <i className="fa fa-plus pr-2" aria-hidden="true"></i>
              <Trans i18nKey={'add_colleges.add_new_college'}></Trans>
            </p>
          )}
        </div>
      </div>
      <div className="clearfix"></div>
      {show && (
        <AddEdit
          show={show}
          setShow={setShow}
          college={fromJS({})}
          title={'add_new_college'}
          fetchData={fetchData}
          setData={setData}
          cancelShow={cancelShow}
          setIsChanged={setIsChanged}
          handleBack={handleBack}
        />
      )}

      {cancelShow && (
        <CancelModal
          show={cancelShow}
          setCancelShow={setCancelShow}
          setShow={setShow}
          setIsChanged={setIsChanged}
        />
      )}
    </>
  );
}

CollegeHeading.propTypes = {
  history: PropTypes.object,
  onSubmit: PropTypes.func,
  setData: PropTypes.func,
  addInstitution: PropTypes.func,
  getColleges: PropTypes.func,
  type: PropTypes.string,
  loggedInUserData: PropTypes.instanceOf(Map),
  count: PropTypes.number,
  searchFilter: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(CollegeHeading);
