import React, { useState } from 'react';
import SettingsIcon from '@mui/icons-material/Settings';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useStylesFunction } from './designUtils';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Menu,
  MenuItem,
  IconButton,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import MaterialInput from 'Widgets/FormElements/material/Input';

function AssessmentType(props) {
  const classes = useStylesFunction();
  const [expanded, setExpanded] = useState(true);
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  const [anchorEl, setAnchorEl] = React.useState(null);

  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const options = ['Delete', 'Edit'];
  const ITEM_HEIGHT = 48;

  const [show, setShow] = useState(false);
  const handleShow = () => {
    setShow(true);
  };
  const handleModalClose = () => {
    setShow(false);
  };
  const [AddShow, setAddShow] = useState(false);
  const handleAddShow = () => {
    setAddShow(true);
  };
  const handleAddModalClose = () => {
    setAddShow(false);
  };

  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="container">
        <div className="p-5">
          <div className="d-flex justify-content-between align-items-center">
            <p className="mb-2 bold f-19"> Assessment Types</p>
            <div className="d-flex">
              <p className="mb-2 bold f-15 mr-2"> Assessment hierarchy</p>
              <SettingsIcon onClick={handleShow} className="remove_hover" />
            </div>
          </div>

          <div className="pt-3">
            <Accordion
              expanded={expanded === 'panel1'}
              onChange={handleChange('panel1')}
              className={classes.accordionBorderUnset}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon fontSize={`large`} />}
                aria-controls="panel1bh-content"
                id="panel1bh-header"
              >
                <p className="mb-0 f-19">Direct</p>
              </AccordionSummary>
              <AccordionDetails>
                <div className="w-100">
                  <>
                    <div className="d-flex justify-content-between mb-3 mt-3">
                      <p className="mb-0 f-15">Basic Details</p>
                      <div
                        className="d-flex text-skyblue bold remove_hover"
                        onClick={handleAddShow}
                      >
                        <AddIcon />
                        <p className="mb-0 f-15 padding-top-2px">Add New</p>
                      </div>
                    </div>
                    <div className="bg-white pl-3 pr-3 pt-2 pb-2 border-radious-4">
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <div className="digi-Shared-bg p-3 border-radious-8">
                            <p className="mb-0 f-15 pl-1 pr-1"> Q</p>
                          </div>
                          <p className="mb-0 pl-3"> Quiz</p>
                        </div>

                        <div className=""></div>
                      </div>
                    </div>
                  </>
                  <>
                    <div className="d-flex justify-content-between mb-3 mt-3">
                      <p className="mb-0 f-15">External</p>
                      <div className="d-flex text-skyblue bold">
                        <AddIcon />
                        <p className="mb-0 f-15 padding-top-2px remove_hover remove_hover">
                          Add New
                        </p>
                      </div>
                    </div>
                    <div className="bg-white pl-3 pr-3 pt-2 pb-2 border-radious-4">
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <div className="digi-Shared-bg p-3 border-radious-8">
                            <p className="mb-0 f-15 pl-1 pr-1"> A</p>
                          </div>
                          <p className="mb-0 pl-3"> Assignment</p>
                        </div>

                        <div className=""></div>
                      </div>
                    </div>
                  </>
                </div>
              </AccordionDetails>
            </Accordion>

            <Accordion
              expanded={expanded === 'panel2'}
              onChange={handleChange('panel2')}
              className={classes.accordionBorderUnset}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon fontSize={`large`} />}
                aria-controls="panel1bh-content"
                id="panel1bh-header"
              >
                <p className="mb-0 f-19">Indirect</p>
              </AccordionSummary>
              <AccordionDetails>
                <div className="w-100">
                  <>
                    <div className="d-flex justify-content-between mb-3 mt-3">
                      <p className="mb-0 f-15">External</p>
                      <div className="d-flex text-skyblue bold">
                        <AddIcon />
                        <p className="mb-0 f-15 padding-top-2px remove_hover">Add New</p>
                      </div>
                    </div>
                    <div className="bg-white pl-3 pr-3 pt-2 pb-2 border-radious-4">
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <div className="digi-Shared-bg p-3 border-radious-8">
                            <p className="mb-0 f-15 pl-1 pr-1"> S</p>
                          </div>
                          <p className="mb-0 pl-3"> Survey</p>
                        </div>
                      </div>
                    </div>
                  </>
                  <div className="mt-2 mb-2">
                    <div className="bg-white pl-3 pr-3 pt-2 pb-2 border-radious-4">
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <div className="digi-Shared-bg p-3 border-radious-8">
                            <p className="mb-0 f-15 pl-1 pr-1"> P</p>
                          </div>
                          <p className="mb-0 pl-3"> Poll</p>
                        </div>
                      </div>
                    </div>
                    <div className="mt-2 mb-2">
                      <div className="bg-white pl-3 pr-3 pt-2 pb-2 border-radious-4">
                        <div className="d-flex justify-content-between align-items-center">
                          <div className="d-flex align-items-center">
                            <div className="digi-Shared-bg p-3 border-radious-8">
                              <p className="mb-0 f-15 pl-1 pr-1"> Q</p>
                            </div>
                            <p className="mb-0 pl-3"> Course Experince Survey</p>
                          </div>
                          <div className="">
                            <IconButton
                              aria-label="more"
                              aria-controls="long-menu"
                              aria-haspopup="true"
                              onClick={handleClick}
                              size="small"
                            >
                              <MoreVertIcon />
                            </IconButton>
                            <Menu
                              id="long-menu"
                              anchorEl={anchorEl}
                              keepMounted
                              open={open}
                              onClose={handleClose}
                              PaperProps={{
                                style: {
                                  maxHeight: ITEM_HEIGHT * 4.5,
                                  width: '20ch',
                                },
                              }}
                            >
                              {options.map((option) => (
                                <MenuItem
                                  key={option}
                                  selected={option === 'Pyxis'}
                                  onClick={handleClose}
                                >
                                  {option}
                                </MenuItem>
                              ))}
                            </Menu>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 mb-2">
                    <div className="bg-white pl-3 pr-3 pt-2 pb-2 border-radious-4">
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="d-flex align-items-center">
                          <div className="digi-Shared-bg p-3 border-radious-8">
                            <p className="mb-0 f-15 pl-1 pr-1"> A</p>
                          </div>
                          <p className="mb-0 pl-3"> Session Experince Survey</p>
                        </div>
                        <div className="">
                          <IconButton
                            aria-label="more"
                            aria-controls="long-menu"
                            aria-haspopup="true"
                            onClick={handleClick}
                            size="small"
                          >
                            <MoreVertIcon />
                          </IconButton>
                          <Menu
                            id="long-menu"
                            anchorEl={anchorEl}
                            keepMounted
                            open={open}
                            onClose={handleClose}
                            PaperProps={{
                              style: {
                                maxHeight: ITEM_HEIGHT * 4.5,
                                width: '20ch',
                              },
                            }}
                          >
                            {options.map((option) => (
                              <MenuItem
                                key={option}
                                selected={option === 'Pyxis'}
                                onClick={handleClose}
                              >
                                {option}
                              </MenuItem>
                            ))}
                          </Menu>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </div>
        </div>
      </div>

      {show && (
        <MaterialDialog show={show} onClose={handleModalClose} maxWidth={'xs'} fullWidth={true}>
          <div className="w-100 p-4">
            <p className="mb-3 pb-2 border-bottom bold f-19"> Assessment Hierarchy Settings</p>
            <div className="mt-2 mb-2">
              <p className="bold mb-1 f-15">Direct</p>
              <div className="d-flex">
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Internal"
                  labelPlacement="end"
                />

                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="External"
                  labelPlacement="end"
                />
              </div>
            </div>
            <div className="mt-2 mb-2">
              <p className="bold mb-1 f-15">Indirect</p>
              <div className="d-flex">
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="Internal"
                  labelPlacement="end"
                />
                <FormControlLabel
                  value="end"
                  control={<Checkbox color="primary" />}
                  label="External"
                  labelPlacement="end"
                />
              </div>
            </div>
            <div className="d-flex justify-content-end border-top pt-3">
              <MButton
                variant="outlined"
                color="primary"
                className={'mr-2'}
                clicked={handleModalClose}
              >
                Cancel
              </MButton>
              <MButton variant="contained" color="primary">
                Save
              </MButton>
            </div>
          </div>
        </MaterialDialog>
      )}

      {AddShow && (
        <MaterialDialog
          show={AddShow}
          onClose={handleAddModalClose}
          maxWidth={'xs'}
          fullWidth={true}
        >
          <div className="w-100 p-4">
            <p className="mb-3 pb-2 border-bottom bold f-19"> Internal - Create New Assessment</p>
            <div className="mt-2 mb-2 pb-2">
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                label={'Assessment Name'}
              />
            </div>

            <div className="d-flex justify-content-end border-top pt-3">
              <MButton
                variant="outlined"
                color="primary"
                className={'mr-2'}
                clicked={handleAddModalClose}
              >
                Cancel
              </MButton>
              <MButton variant="contained" color="primary">
                Save
              </MButton>
            </div>
          </div>
        </MaterialDialog>
      )}
    </div>
  );
}

export default AssessmentType;
