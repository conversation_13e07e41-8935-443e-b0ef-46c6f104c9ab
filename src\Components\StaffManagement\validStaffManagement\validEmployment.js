import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import 'react-datepicker/dist/react-datepicker.css';
import moment from 'moment';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import axios from '../../../axios';
import Input from '../../../Widgets/FormElements/Input/Input';
import Loader from '../../../Widgets/Loader/Loader';
import { FlexWrapper, NavButton } from '../../../_components/Styled/styled';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getLang, indVerRename } from 'utils';
import ar from 'date-fns/locale/ar';
import DatePicker, { registerLocale } from 'react-datepicker';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { connect } from 'react-redux';
registerLocale('ar', ar);

let id;
let idx;

function Days(props) {
  return (
    <span
      className={`mr-2 schedule_days ${props.selectedDay === true ? 'selected_days' : ''}`}
      onClick={props.onClick}
    >
      {props.day}
    </span>
  );
}
class ValidEmployment extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      staffType: [
        { name: 'Academic', isChecked: false },
        { name: 'Administration', isChecked: false },
      ],
      staffEmploymentTypeData: [
        {
          name: 'Select',
          value: 'Select',
        },
        {
          name: 'Full Time',
          value: 'full_time',
        },
        {
          name: 'Part Time',
          value: 'part_time',
        },
      ],
      scheduleTypeData: [
        {
          name: 'Select',
          value: 'Select',
        },
        {
          name: 'By Date',
          value: 'by_date',
        },
        {
          name: 'By Day',
          value: 'by_day',
        },
      ],
      academicYearData: [
        {
          name: 'Select',
          value: 'Select',
        },
        {
          name: '(2020 - 21)',
          value: '(2020 - 21)',
        },
        {
          name: '(2021 - 22)',
          value: '(2021 - 22)',
        },
      ],
      scheduleType: '',
      academicYear: '',
      staffEmploymentType: '',
      fullTimeScheduleShow: false,
      staffMode: [
        { name: 'On-site', isChecked: false },
        { name: 'Online', isChecked: false },
      ],
      days: [
        { day: 'SUN', isChecked: false, num: 0 },
        { day: 'MON', isChecked: false, num: 1 },
        { day: 'TUE', isChecked: false, num: 2 },
        { day: 'WED', isChecked: false, num: 3 },
        { day: 'THU', isChecked: false, num: 4 },
        { day: 'FRI', isChecked: false, num: 5 },
        { day: 'SAT', isChecked: false, num: 6 },
      ],
      days2: [
        { day: 'SUN', isChecked: false },
        { day: 'MON', isChecked: false },
        { day: 'TUE', isChecked: false },
        { day: 'WED', isChecked: false },
        { day: 'THU', isChecked: false },
        { day: 'FRI', isChecked: false },
        { day: 'SAT', isChecked: false },
      ],
      fullTimeData: [
        // {
        //   staffMode: "Online",
        //   selectedDays: [
        //     { day: "SUN", isChecked: true },
        //     { day: "MON", isChecked: true },
        //     { day: "TUE", isChecked: true },
        //     { day: "WED", isChecked: true },
        //     { day: "THU", isChecked: true },
        //     { day: "FRI", isChecked: false },
        //     { day: "SAT", isChecked: false },
        //   ],
        //   startTimeDuration: "10.00 AM",
        //   endTimeDuration: "4.00 PM",
        // },
      ],
      partTimeDayScheduleShow: false,
      partTimeDayData: [
        // {
        //   staffMode: "Online",
        //   selectedDays: "SUN",
        //   startTimeDuration: "10.00 AM",
        //   endTimeDuration: "4.00 PM",
        // },
      ],
      SUN: true,
      MON: false,
      TUE: false,
      WED: false,
      THU: false,
      FRI: false,
      SAT: false,
      partTimeDateScheduleShow: false,
      partTimeDateData: [],
      // partTimeDateSchedule:[],
      showDayOrHide: [],
      startTime: new Date(),
      endTime: new Date(),
      startDate: new Date(),
      endDate: new Date(),
      errorMsg: '',
      employment_check: false,
      user_type: '',
      academic_check: false,
      tabs: 0,
      userStatus: '',
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');
    let tab = params.get('tab');
    this.setState({ tabs: tab });
    this.fetchApi();
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });

    axios
      .get(`/user/staff/${id}`)
      .then((res) => {
        const data = res.data.data;
        this.setState({
          userStatus: data.status,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });

    axios
      .get(`user/staff_profile_details/${id}`)
      .then((res) => {
        // Safely handle potentially undefined arrays
        const employmentData = res.data.data.employment || {};
        const scheduleTimes = employmentData.schedule_times || {};
        let fullTime = [];
        if (scheduleTimes.full_time) {
          fullTime = scheduleTimes.full_time.map((data) => {
            return {
              startTimeDuration: data.start_time,
              endTimeDuration: data.end_time,
              staffMode: data.mode,
              selectedDays: data.days,
            };
          });
        }

        let partTimeByDay = [];
        if (scheduleTimes.by_day && Array.isArray(scheduleTimes.by_day)) {
          partTimeByDay = scheduleTimes.by_day.map((data) => {
            return {
              staffMode: data.mode,
              startTimeDuration: data.start_time,
              endTimeDuration: data.end_time,
              selectedDays: data.days,
            };
          });
        }

        let partTimeByDate = [];
        if (scheduleTimes.by_date && Array.isArray(scheduleTimes.by_date)) {
          partTimeByDate = scheduleTimes.by_date.map((data) => {
            return {
              startDateDuration:
                data.start_date !== '' &&
                data.start_date.indexOf('PM') === -1 &&
                data.start_date.indexOf('AM') === -1
                  ? new Date(data.start_date)
                  : '',
              endDateDuration:
                data.end_date !== '' &&
                data.end_date.indexOf('PM') === -1 &&
                data.end_date.indexOf('AM') === -1
                  ? new Date(data.end_date)
                  : '',
              partTimeDateSchedule: data.schedule.map((scheduleData) => {
                return {
                  staffMode: scheduleData.mode,
                  startTimeDuration: scheduleData.start_time,
                  endTimeDuration: scheduleData.end_time,
                  selectedDays: scheduleData.days,
                };
              }),
            };
          });
        }

        let user_type = res.data.data.user_type;
        let firstLetterCapitalized = user_type.charAt(0).toUpperCase() + user_type.slice(1);

        this.setState({
          // staffType:staffType,
          staffEmploymentType: employmentData.user_employment_type,
          scheduleType:
            scheduleTimes.by_date && scheduleTimes.by_date.length > 0 ? 'by_date' : 'by_day',
          fullTimeData: fullTime,
          partTimeDayData: partTimeByDay,
          partTimeDateData: partTimeByDate,
          isLoading: false,
          employment_check: res.data.data.employment_check,
          academic_check: res.data.data.academic_check,
          user_type: firstLetterCapitalized,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
          fullTimeData: [],
          partTimeDayData: [],
          partTimeDateData: [],
          employment_check: false,
          academic_check: false,
        });
      });
  };

  handleCheckStaffType = (event, index) => {
    let data = this.state.staffType;

    data[index].isChecked = event.target.checked;
    this.setState({ staffType: data });
  };

  handleCheckStaffMode = (event, index) => {
    let data = this.state.staffMode;

    data[index].isChecked = event.target.checked;
    this.setState({ staffMode: data, errorMsg: '' });
  };

  handleSelect = (e, name) => {
    e.preventDefault();
    if (name === 'staffEmploymentType') {
      this.setState({
        staffEmploymentType: e.target.value,
        partTimeDateData: [],
        fullTimeData: [],
        partTimeDayData: [],
      });
    }
    if (name === 'scheduleType') {
      this.setState({
        scheduleType: e.target.value,
        partTimeDateData: [],
        fullTimeData: [],
        partTimeDayData: [],
      });
    }

    if (name === 'academicYear') {
      this.setState({
        academicYear: e.target.value,
      });
    }
  };

  handleShow = (e, name, i) => {
    if (name === 'fullTimeScheduleShow') {
      this.setState({
        fullTimeScheduleShow: true,
      });
    }
    if (name === 'partTimeDayScheduleShow') {
      this.setState({
        partTimeDayScheduleShow: true,
      });
    }
    if (name === 'partTimeDateScheduleShow') {
      idx = i;
      this.setState({
        partTimeDateScheduleShow: true,
      });
      this.handleWeekDayShow(i);
    }

    this.setState({ startTime: new Date(), endTime: new Date(), errorMsg: '' });
  };

  handleClose = (e, name) => {
    if (name === 'fullTimeScheduleShow') {
      this.setState({
        fullTimeScheduleShow: false,
      });
    }
    if (name === 'partTimeDayScheduleShow') {
      this.setState({
        partTimeDayScheduleShow: false,
      });
    }
    if (name === 'partTimeDateScheduleShow') {
      this.setState({
        partTimeDateScheduleShow: false,
      });
    }
  };

  handleClickDate = (e, num) => {
    let days = this.state.days;

    days[num].isChecked = !days[num].isChecked;

    this.setState({
      days: days,
      errorMsg: '',
    });
  };

  handleClick = (e, index) => {
    let days = this.state.days;
    days[index].isChecked = !days[index].isChecked;
    this.setState({
      days: days,
      errorMsg: '',
    });
  };

  handleClick2 = (e, name) => {
    if (name === 'SUN') {
      this.setState({
        SUN: true,
        MON: false,
        TUE: false,
        WED: false,
        THU: false,
        FRI: false,
        SAT: false,
      });
    }
    if (name === 'MON') {
      this.setState({
        SUN: false,
        MON: true,
        TUE: false,
        WED: false,
        THU: false,
        FRI: false,
        SAT: false,
      });
    }
    if (name === 'TUE') {
      this.setState({
        SUN: false,
        MON: false,
        TUE: true,
        WED: false,
        THU: false,
        FRI: false,
        SAT: false,
      });
    }
    if (name === 'WED') {
      this.setState({
        SUN: false,
        MON: false,
        TUE: false,
        WED: true,
        THU: false,
        FRI: false,
        SAT: false,
      });
    }
    if (name === 'THU') {
      this.setState({
        SUN: false,
        MON: false,
        TUE: false,
        WED: false,
        THU: true,
        FRI: false,
        SAT: false,
      });
    }
    if (name === 'FRI') {
      this.setState({
        SUN: false,
        MON: false,
        TUE: false,
        WED: false,
        THU: false,
        FRI: true,
        SAT: false,
      });
    }
    if (name === 'SAT') {
      this.setState({
        SUN: false,
        MON: false,
        TUE: false,
        WED: false,
        THU: false,
        FRI: false,
        SAT: true,
      });
    }
  };

  addFullTime = () => {
    if (!this.validate()) return;

    let selectDays = this.state.days.filter((data) => {
      return data.isChecked === true;
    });

    let selectedDays = selectDays.map((data) => {
      return data.day;
    });

    let selectStaffMode = this.state.staffMode.filter((data) => {
      return data.isChecked === true;
    });

    let selectedStaffMode = selectStaffMode.map((data) => {
      return data.name;
    });

    let staffMode = selectedStaffMode.toString();
    let staffModeValue;

    if (staffMode === 'On-site') {
      staffModeValue = 'onsite';
    } else if (staffMode === 'Online') {
      staffModeValue = 'online';
    } else {
      staffModeValue = 'both';
    }

    let fullTimeData = this.state.fullTimeData;
    let startTime = moment(this.state.startTime).format('hh:mm A');
    let endTime = moment(this.state.endTime).format('hh:mm A');

    fullTimeData.push({
      staffMode: staffModeValue,
      selectedDays: selectedDays,
      startTimeDuration: startTime,
      endTimeDuration: endTime,
    });

    this.setState({
      fullTimeData: fullTimeData,
      fullTimeScheduleShow: false,
      days: [
        { day: 'SUN', isChecked: false, num: 0 },
        { day: 'MON', isChecked: false, num: 1 },
        { day: 'TUE', isChecked: false, num: 2 },
        { day: 'WED', isChecked: false, num: 3 },
        { day: 'THU', isChecked: false, num: 4 },
        { day: 'FRI', isChecked: false, num: 5 },
        { day: 'SAT', isChecked: false, num: 6 },
      ],
      staffMode: [
        { name: 'On-site', isChecked: false },
        { name: 'Online', isChecked: false },
      ],
    });
  };

  handleChangeStartTime = (date) => {
    if (date !== '') {
      this.setState({
        startTime: date,
      });
    }
  };

  handleChangeEndTime = (date) => {
    if (date !== '') {
      this.setState({
        endTime: date,
      });
    }
  };

  handleChangeStartDate = (date, i) => {
    if (date !== '' && date !== null && date !== undefined) {
      let copyState = [...this.state.partTimeDateData];
      let copyIndex = copyState[i];
      copyIndex['startDateDuration'] = moment(date).format('YYYY-MM-DD');
      copyIndex['endDateDuration'] = moment(date).format('YYYY-MM-DD');
      copyState[i] = copyIndex;
      this.setState(
        {
          partTimeDateData: copyState,
        },
        () => {
          this.handleWeekDayShow(i);
        }
      );
    }
  };

  handleChangeEndDate = (date, i) => {
    if (date !== '' && date !== null && date !== undefined) {
      let copyState = [...this.state.partTimeDateData];
      let copyIndex = copyState[i];
      copyIndex['endDateDuration'] = moment(date).format('YYYY-MM-DD');
      copyState[i] = copyIndex;
      this.setState(
        {
          partTimeDateData: copyState,
        },
        () => {
          this.handleWeekDayShow(i);
        }
      );
    }
  };

  handleWeekDayShow = (i) => {
    const startDate = new Date(this.state.partTimeDateData[i].startDateDuration);
    const endDate = new Date(this.state.partTimeDateData[i].endDateDuration);
    const fullDate = endDate - startDate;
    const dateEqual = fullDate / 86400000;
    let arr = [];
    if (dateEqual >= 7) {
      arr = [0, 1, 2, 3, 4, 5, 6];
    }

    if (dateEqual < 7) {
      let day_value = startDate.getDay() - 1;
      for (let i = 0; i <= dateEqual; i++) {
        if (day_value + 1 === 7) {
          day_value = 0;
          arr.push(0);
        } else {
          arr.push(day_value + 1);
          day_value++;
        }
      }
    }
    this.setState({
      showDayOrHide: arr,
    });
  };

  editFullTime = (index) => {
    let days = this.state.days;

    let fullTimeData = this.state.fullTimeData;
    let startTime = moment(this.state.startTime).format('hh:mm A');
    let endTime = moment(this.state.endTime).format('hh:mm A');

    let fullTimeDatas = {
      staffMode: 'Online',
      selectedDays: days,
      startTimeDuration: startTime,
      endTimeDuration: endTime,
    };

    fullTimeData[index] = fullTimeDatas;

    this.setState({
      fullTimeScheduleShow: true,
    });
  };

  removeFullTime = (index) => {
    let fullTimeData = this.state.fullTimeData;

    fullTimeData.splice(index, 1);

    this.setState({
      fullTimeData,
    });
  };

  removePartTimeByDay = (index) => {
    let partTimeDayData = this.state.partTimeDayData;
    partTimeDayData.splice(index, 1);

    this.setState({
      partTimeDayData,
    });
  };

  validate = () => {
    let selectStaffMode = this.state.staffMode.filter((data) => {
      return data.isChecked === true;
    });

    let selectedStaffMode = selectStaffMode.map((data) => {
      return data.name;
    });

    if (!selectedStaffMode.length > 0) {
      this.setState({
        errorMsg: 'Select staff mode',
      });
      return false;
    }

    let selectDays = this.state.days.filter((data) => {
      return data.isChecked === true;
    });

    let selectedDays = selectDays.map((data) => {
      return data.day;
    });
    if (!selectedDays.length > 0) {
      this.setState({
        errorMsg: 'Select atleast one day',
      });
      return false;
    }

    let st_date = moment(this.state.startTime).unix();
    let et_date = moment(this.state.endTime).unix();

    if (st_date >= et_date) {
      this.setState({
        errorMsg: 'End time should be greater than start time',
      });
      return false;
    }

    return true;
  };

  addPartTimeDay = () => {
    if (!this.validate1()) return;

    let day;
    if (this.state.SUN === true) {
      day = 'SUN';
    }
    if (this.state.MON === true) {
      day = 'MON';
    }
    if (this.state.TUE === true) {
      day = 'TUE';
    }
    if (this.state.WED === true) {
      day = 'WED';
    }
    if (this.state.THU === true) {
      day = 'THU';
    }
    if (this.state.FRI === true) {
      day = 'FRI';
    }
    if (this.state.SAT === true) {
      day = 'SAT';
    }

    let partTimeDayData = this.state.partTimeDayData;
    let startTime = moment(this.state.startTime).format('hh:mm A');
    let endTime = moment(this.state.endTime).format('hh:mm A');

    let selectStaffMode = this.state.staffMode.filter((data) => {
      return data.isChecked === true;
    });

    let selectedStaffMode = selectStaffMode.map((data) => {
      return data.name;
    });

    let staffMode = selectedStaffMode.toString();
    let staffModeValue;

    if (staffMode === 'On-site') {
      staffModeValue = 'onsite';
    } else if (staffMode === 'Online') {
      staffModeValue = 'online';
    } else {
      staffModeValue = 'both';
    }

    partTimeDayData.push({
      staffMode: staffModeValue,
      selectedDays: day,
      startTimeDuration: startTime,
      endTimeDuration: endTime,
    });

    this.setState({
      partTimeDayData: partTimeDayData,
      partTimeDayScheduleShow: false,
      staffMode: [
        { name: 'On-site', isChecked: false },
        { name: 'Online', isChecked: false },
      ],
    });
  };

  validate1 = () => {
    let selectStaffMode = this.state.staffMode.filter((data) => {
      return data.isChecked === true;
    });

    let selectedStaffMode = selectStaffMode.map((data) => {
      return data.name;
    });

    if (!selectedStaffMode.length > 0) {
      this.setState({
        errorMsg: 'Select staff mode',
      });
      return false;
    }

    let st_date = moment(this.state.startTime).unix();
    let et_date = moment(this.state.endTime).unix();

    if (st_date >= et_date) {
      this.setState({
        errorMsg: 'End time should be greater than start time',
      });
      return false;
    }

    return true;
  };

  addPartTimeDate = () => {
    let partTimeDateData = this.state.partTimeDateData;
    let startDate = moment(this.state.startDate).format('YYYY-MM-DD');
    let endDate = moment(this.state.endDate).format('YYYY-MM-DD');

    partTimeDateData.push({
      startDateDuration: startDate,
      endDateDuration: endDate,
      partTimeDateSchedule: [],
    });

    this.setState({
      partTimeDateData: partTimeDateData,
    });
  };

  addPartTimeDateSchedule = (e, i) => {
    if (!this.validate()) return;

    let startTime = moment(this.state.startTime).format('hh:mm A');
    let endTime = moment(this.state.endTime).format('hh:mm A');

    let selectDays = this.state.days.filter((data) => {
      return data.isChecked === true;
    });

    let selectedDays = selectDays.map((data) => {
      return data.day;
    });

    let selectStaffMode = this.state.staffMode.filter((data) => {
      return data.isChecked === true;
    });

    let selectedStaffMode = selectStaffMode.map((data) => {
      return data.name;
    });

    let staffMode = selectedStaffMode.toString();
    let staffModeValue;

    if (staffMode === 'On-site') {
      staffModeValue = 'onsite';
    } else if (staffMode === 'Online') {
      staffModeValue = 'online';
    } else {
      staffModeValue = 'both';
    }

    let partTimeDateData = this.state.partTimeDateData[idx].partTimeDateSchedule;

    partTimeDateData.push({
      staffMode: staffModeValue,
      selectedDays: selectedDays,
      startTimeDuration: startTime,
      endTimeDuration: endTime,
    });

    this.setState({
      days: [
        { day: 'SUN', isChecked: false, num: 0 },
        { day: 'MON', isChecked: false, num: 1 },
        { day: 'TUE', isChecked: false, num: 2 },
        { day: 'WED', isChecked: false, num: 3 },
        { day: 'THU', isChecked: false, num: 4 },
        { day: 'FRI', isChecked: false, num: 5 },
        { day: 'SAT', isChecked: false, num: 6 },
      ],
      staffMode: [
        { name: 'On-site', isChecked: false },
        { name: 'Online', isChecked: false },
      ],
      // partTimeDateData,
      // partTimeDateData: partTimeDateData,
      partTimeDateScheduleShow: false,
    });
  };

  deletePartTimeData = (i) => {
    let data = this.state.partTimeDateData.filter((_, index) => index !== i);
    this.setState({ partTimeDateData: data });
  };

  handleSubmit = () => {
    this.setState({
      isLoading: true,
    });

    let data;

    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');

    if (this.state.staffEmploymentType === 'full_time') {
      let fullTimeData = this.state.fullTimeData.map((data) => {
        return {
          mode: data.staffMode,
          start_time: data.startTimeDuration,
          end_time: data.endTimeDuration,
          days: data.selectedDays,
        };
      });

      data = {
        id: id,
        //user_type: staffTypeValue,
        //institution_role: "Course members",
        user_employment_type: this.state.staffEmploymentType,
        schedule_times: fullTimeData,
      };
    }
    if (this.state.staffEmploymentType === 'part_time' && this.state.scheduleType === 'by_day') {
      let partTimeDayData = this.state.partTimeDayData.map((data) => {
        return {
          mode: data.staffMode,
          start_time: data.startTimeDuration,
          end_time: data.endTimeDuration,
          days: data.selectedDays,
        };
      });
      data = {
        id: id,
        //user_type: staffTypeValue,
        //institution_role: "Course members",
        user_employment_type: this.state.staffEmploymentType,
        schedule_times: partTimeDayData,
        user_schedule_type: 'by_day',
      };
    }

    if (this.state.staffEmploymentType === 'part_time' && this.state.scheduleType === 'by_date') {
      let partTimeDateData = this.state.partTimeDateData.map((data) => {
        return {
          start_date: data.startDateDuration,
          end_date: data.endDateDuration,
          schedule: data.partTimeDateSchedule.map((data) => {
            return {
              mode: data.staffMode,
              start_time: data.startTimeDuration,
              end_time: data.endTimeDuration,
              days: data.selectedDays,
            };
          }),
        };
      });
      data = {
        id: id,
        //user_type: staffTypeValue,
        //institution_role: "Course members",
        user_employment_type: this.state.staffEmploymentType,
        schedule_times: partTimeDateData,
        user_schedule_type: 'by_date',
      };
    }
    const { loggedInUserData } = this.props;
    data = { ...data, staffFacial: loggedInUserData.get('staffFacial', false) };

    axios
      .post(`user/user_employment`, data)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
          },
          () => this.fetchApi()
        );

        NotificationManager.success(t(`user_management.Employment_Added_Successfully`));
      })
      .catch((error) => {
        NotificationManager.error(`${error.response.data.message}`);
        this.setState({
          isLoading: false,
        });
      });
  };

  handleGoBack = () => {
    if (this.state.userStatus === 'completed') {
      this.props.history.push({
        pathname: '/staff/management',
      });
    } else {
      this.props.history.push({
        pathname: '/staff/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
          selectedTab: this.state.tabs !== '' ? parseInt(this.state.tabs) : 0,
        },
      });
    }
  };

  render() {
    const { loggedInUserData } = this.props;
    const { tabs } = this.state;
    var partTimeDateData = true;
    if (this.state.partTimeDateData && this.state.partTimeDateData.length > 0) {
      this.state.partTimeDateData.map((item) => {
        if (item.partTimeDateSchedule && item.partTimeDateSchedule.length === 0) {
          partTimeDateData = false;
        }
        return null;
      });
    }

    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';

    return (
      <React.Fragment>
        <Loader isLoading={this.state.isLoading} />
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'user_management.staff_employment'}></Trans>
        </div>
        <FlexWrapper className="nav_bg">
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Profile View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Profile View'
            )) && (
            <Link exact to={`/staff/management/profile${this.props.location.search}`}>
              <NavButton
                className={this.props.location.pathname === '/staff/management/profile' && 'active'}
                color="white"
              >
                <Trans i18nKey={'user_management.profile'}></Trans>
              </NavButton>
            </Link>
          )}

          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Academic View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Academic View'
            )) && (
            <Link exact to={`/staff/management/academic${this.props.location.search}`}>
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/academic' && 'active'
                }
                color="white"
              >
                <Trans i18nKey={'user_management.academic'}></Trans>
              </NavButton>
            </Link>
          )}
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Employment View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Employment View'
            )) && (
            <Link exact to={`/staff/management/employment${this.props.location.search}`}>
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/employment' && 'active'
                }
                color="white"
                disabled={false}
              >
                <Trans i18nKey={'user_management.employment'}></Trans>
              </NavButton>
            </Link>
          )}
          {loggedInUserData.get('staffFacial', false) &&
            (CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Biometric View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Biometric View'
              )) && (
              <Link
                exact
                to={
                  this.state.employment_check
                    ? `/staff/management/biometric${this.props.location.search}`
                    : `/staff/management/employment${this.props.location.search}`
                }
              >
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/biometric' && 'active'
                  }
                  color="white"
                  disabled={!this.state.employment_check}
                >
                  <Trans i18nKey={'user_management.biometric'}></Trans>
                </NavButton>
              </Link>
            )}
        </FlexWrapper>
        <div className="pt-5 pb-5 main">
          <Loader isLoading={this.state.isLoading} />
          <NotificationContainer />

          <div className="container">
            <div className="mb-2" style={{ justifyContent: 'flex-end', display: 'flex' }}>
              <Button variant="outline-primary" className="mr-3" onClick={this.handleGoBack}>
                <Trans i18nKey={'back'}></Trans>
              </Button>
              {(CheckPermission(
                'tabs',
                'User Management',
                'Staff Management',
                '',
                permissionName,
                'Employment Edit'
              ) ||
                CheckPermission(
                  'subTabs',
                  'User Management',
                  'Staff Management',
                  '',
                  'Registration Pending',
                  '',
                  permissionName,
                  'Employment Edit'
                )) && (
                <Button
                  disabled={
                    !(
                      (this.state.fullTimeData && this.state.fullTimeData.length > 0) ||
                      (this.state.partTimeDayData && this.state.partTimeDayData.length > 0) ||
                      (this.state.partTimeDateData &&
                        this.state.partTimeDateData.length > 0 &&
                        partTimeDateData)
                    )
                  }
                  onClick={this.handleSubmit}
                >
                  <Trans i18nKey={'save'}></Trans>
                </Button>
              )}
            </div>

            <div className="p-5 white">
              <div className="row">
                <div className="mb-4 col-md-12">
                  <Input
                    elementType={'selectbar'}
                    elementConfig={{
                      options: this.state.staffEmploymentTypeData.map((item) => ({
                        ...item,
                        name: t(`user_management.${item.name}`),
                      })),
                    }}
                    value={this.state.staffEmploymentType}
                    label={<Trans i18nKey={'employment.staff_employment'}></Trans>}
                    labelclass="d-flex"
                    changed={(e) => this.handleSelect(e, 'staffEmploymentType')}
                  />
                </div>

                {this.state.staffEmploymentType === 'part_time' && (
                  <div className="mb-4 col-md-12">
                    <Input
                      elementType={'selectbar'}
                      elementConfig={{
                        options: this.state.scheduleTypeData,
                      }}
                      value={this.state.scheduleType}
                      label={<Trans i18nKey={'employment.schedule_type'}></Trans>}
                      labelclass="d-flex"
                      changed={(e) => this.handleSelect(e, 'scheduleType')}
                    />
                  </div>
                )}
                {/* {this.state.staffEmploymentType === "part_time" &&
                  this.state.scheduleType === "by_day" && (
                    <div className="mb-4 col-md-12">
                      <Input
                        elementType={"selectbar"}
                        elementConfig={{
                          options: this.state.academicYearData,
                        }}
                        value={this.state.academicYear}
                        label={"Academic Year"}
                        changed={(e) => this.handleSelect(e, "academicYear")}
                      />
                    </div>
                  )} */}

                {/* full_time - start */}
                {this.state.staffEmploymentType === 'full_time' && (
                  <div className="mb-4 col-md-12">
                    <label className="form-label d-flex">
                      {' '}
                      <Trans i18nKey={'employment.employment_schedule'}></Trans>
                    </label>
                    <table className="mb-4">
                      <tbody>
                        {this.state.fullTimeData.map((data, index) => (
                          <tr key={index}>
                            <td className="pr-4">
                              {data.selectedDays.map((day) => day).join(', ')}
                            </td>
                            <td className="pr-4">
                              {data.startTimeDuration} <Trans i18nKey={'to'} />{' '}
                              {data.endTimeDuration}
                            </td>
                            {/* <td onClick={() => this.editFullTime(index)}>Edit</td> */}
                            <td className="pr-4">
                              <i
                                className="fa fa-times-circle"
                                onClick={() => this.removeFullTime(index)}
                              ></i>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    <div
                      className="remove_hover d-flex"
                      onClick={(e) => this.handleShow(e, 'fullTimeScheduleShow')}
                    >
                      <i className="fa fa-plus-circle remove_hover"></i>
                      <span className={`${getLang() === 'ar' ? 'digi-mr-10' : 'digi-ml-10'}`}>
                        <Trans i18nKey={'employment.add_schedule'}></Trans>
                      </span>
                    </div>
                    <Modal
                      show={this.state.fullTimeScheduleShow}
                      centered
                      onHide={(e) => this.handleClose(e, 'fullTimeScheduleShow')}
                    >
                      <Modal.Header closeButton>
                        <Modal.Title id="example-modal-sizes-title-sm">
                          <h5>
                            <Trans i18nKey={'employment.add_schedule'}></Trans>
                          </h5>
                        </Modal.Title>
                      </Modal.Header>
                      <Modal.Body>
                        {this.state.errorMsg !== '' && (
                          <div className="bg-gray">
                            <p className="p-1 text-center text-red">
                              <i className="fa fa-exclamation-circle" aria-hidden="true"></i>{' '}
                              {this.state.errorMsg}
                            </p>
                          </div>
                        )}
                        <div className="mb-3">
                          <label>
                            <Trans i18nKey={'employment.staff_mode'}></Trans>
                          </label>
                          <div>
                            {this.state.staffMode.map((data, index) => (
                              <span className="mr-2" key={index}>
                                <input
                                  type="checkbox"
                                  className="calendarFormRadio"
                                  onClick={(event) => this.handleCheckStaffMode(event, index)}
                                  value="checkedall"
                                />{' '}
                                <Trans
                                  i18nKey={`user_management.${data.name}`}
                                  values={{ onsite: indVerRename('onsite') }}
                                />
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="mb-3">
                          <label>
                            <Trans i18nKey={'employment.select_day'}></Trans>
                          </label>
                          <div>
                            {this.state.days.map((data, index) => (
                              <span
                                key={index}
                                className={`mr-2 schedule_days remove_hover ${
                                  data.isChecked === true ? 'selected_days' : ''
                                }`}
                                onClick={(e) => this.handleClick(e, index)}
                              >
                                <Trans i18nKey={`user_management.${data.day.toLowerCase()}`} />
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="mb-3 mr-2">
                          <label>
                            {' '}
                            <Trans i18nKey={'employment.select_duration'}></Trans>
                          </label>
                          <div className="row justify-content-center">
                            <div className="col-md-5">
                              <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                              <DatePicker
                                selected={this.state.startTime}
                                onChange={this.handleChangeStartTime}
                                {...(getLang() === 'ar' && { locale: 'ar' })}
                                showTimeSelect
                                showTimeSelectOnly
                                timeIntervals={15}
                                timeCaption={t('global_configuration.time')}
                                dateFormat="h:mm aa"
                                className={`form-control ${
                                  getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                }`}
                              />
                            </div>
                            <div className="pt-3 text-center col-md-2">
                              <Trans i18nKey={'to'}></Trans>
                            </div>
                            <div className="col-md-5">
                              <i className="fa fa-clock-o calender" aria-hidden="true"></i>

                              <DatePicker
                                selected={this.state.endTime}
                                {...(getLang() === 'ar' && { locale: 'ar' })}
                                onChange={this.handleChangeEndTime}
                                showTimeSelect
                                showTimeSelectOnly
                                timeIntervals={15}
                                timeCaption={t('global_configuration.time')}
                                dateFormat="h:mm aa"
                                className={`form-control ${
                                  getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                }`}
                              />
                            </div>
                          </div>
                        </div>
                      </Modal.Body>
                      <Modal.Footer>
                        <Button
                          onClick={(e) => this.handleClose(e, 'fullTimeScheduleShow')}
                          className="mr-3"
                        >
                          <Trans i18nKey={'cancel'}></Trans>
                        </Button>
                        <Button onClick={this.addFullTime}>
                          <Trans i18nKey={'save'}></Trans>
                        </Button>
                      </Modal.Footer>
                    </Modal>
                  </div>
                )}
                {/* full_time - end */}

                {/* part_time by_day - start */}
                {this.state.staffEmploymentType === 'part_time' &&
                  this.state.scheduleType === 'by_day' && (
                    <div className="mb-4 col-md-12">
                      <label className="form-label">
                        {' '}
                        <Trans i18nKey={'employment.employment_schedule'}></Trans>{' '}
                      </label>
                      <table className="mb-4">
                        <tbody>
                          {this.state.partTimeDayData.map((data, index) => (
                            <tr key={index}>
                              <td className="pr-4">
                                <span>{data.selectedDays}</span>
                              </td>
                              <td className="pr-4">
                                {data.startTimeDuration} <Trans i18nKey={'to'}></Trans>
                                {data.endTimeDuration}
                              </td>
                              {/* <td onClick={() => this.editFullTime(index)}>Edit</td> */}
                              <td className="pr-4">
                                <i
                                  className="fa fa-times-circle"
                                  onClick={() => this.removePartTimeByDay(index)}
                                ></i>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      <div
                        className="remove_hover"
                        onClick={(e) => this.handleShow(e, 'partTimeDayScheduleShow')}
                      >
                        <i className="fa fa-plus-circle remove_hover"></i>{' '}
                        <Trans i18nKey={'employment.add_schedule'}></Trans>{' '}
                      </div>

                      <Modal
                        show={this.state.partTimeDayScheduleShow}
                        centered
                        // size="lg"
                        onHide={(e) => this.handleClose(e, 'partTimeDayScheduleShow')}
                      >
                        <Modal.Header closeButton>
                          <Modal.Title id="example-modal-sizes-title-sm">
                            <h5>
                              <Trans i18nKey={'employment.add_schedule'}></Trans>
                            </h5>
                          </Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          {this.state.errorMsg !== '' && (
                            <div className="bg-gray">
                              <p className="p-1 text-center text-red">
                                <i className="fa fa-exclamation-circle" aria-hidden="true"></i>{' '}
                                {this.state.errorMsg}
                              </p>
                            </div>
                          )}
                          <div className="mb-3">
                            <label>
                              <Trans i18nKey={'employment.staff_mode'}></Trans>
                            </label>
                            <div>
                              {this.state.staffMode.map((data, index) => (
                                <span className="mr-2" key={index}>
                                  <input
                                    type="checkbox"
                                    className="calendarFormRadio"
                                    onClick={(event) => this.handleCheckStaffMode(event, index)}
                                    value="checkedall"
                                  />{' '}
                                  <Trans
                                    i18nKey={`user_management.${data.name}`}
                                    values={{ onsite: indVerRename('onsite') }}
                                  />
                                </span>
                              ))}
                            </div>
                          </div>
                          <div className="mb-3">
                            <label>
                              {' '}
                              <Trans i18nKey={'employment.select_day'}></Trans>
                            </label>
                            <div>
                              <Days
                                selectedDay={this.state.SUN}
                                onClick={(e) => this.handleClick2(e, 'SUN')}
                                day="SUN"
                              />
                              <Days
                                selectedDay={this.state.MON}
                                onClick={(e) => this.handleClick2(e, 'MON')}
                                day="MON"
                              />
                              <Days
                                selectedDay={this.state.TUE}
                                onClick={(e) => this.handleClick2(e, 'TUE')}
                                day="TUE"
                              />
                              <Days
                                selectedDay={this.state.WED}
                                onClick={(e) => this.handleClick2(e, 'WED')}
                                day="WED"
                              />
                              <Days
                                selectedDay={this.state.THU}
                                onClick={(e) => this.handleClick2(e, 'THU')}
                                day="THU"
                              />
                              <Days
                                selectedDay={this.state.FRI}
                                onClick={(e) => this.handleClick2(e, 'FRI')}
                                day="FRI"
                              />
                              <Days
                                selectedDay={this.state.SAT}
                                onClick={(e) => this.handleClick2(e, 'SAT')}
                                day="SAT"
                              />
                            </div>
                          </div>
                          <div className="mb-3">
                            <label>
                              {' '}
                              <Trans i18nKey={'employment.select_duration'}></Trans>
                            </label>
                            <div className="row">
                              <div className="col-md-5">
                                <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                                <DatePicker
                                  selected={this.state.startTime}
                                  {...(getLang() === 'ar' && { locale: 'ar' })}
                                  onChange={this.handleChangeStartTime}
                                  showTimeSelect
                                  showTimeSelectOnly
                                  timeIntervals={15}
                                  timeCaption={t('global_configuration.time')}
                                  dateFormat="h:mm aa"
                                  className={`form-control ${
                                    getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                  }`}
                                />
                              </div>
                              <div className="pt-3 text-center col-md-2">
                                {' '}
                                <Trans i18nKey={'to'}></Trans>
                              </div>
                              <div className="col-md-5">
                                <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                                <DatePicker
                                  selected={this.state.endTime}
                                  {...(getLang() === 'ar' && { locale: 'ar' })}
                                  onChange={this.handleChangeEndTime}
                                  showTimeSelect
                                  showTimeSelectOnly
                                  timeIntervals={15}
                                  timeCaption={t('global_configuration.time')}
                                  dateFormat="h:mm aa"
                                  className={`form-control ${
                                    getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                  }`}
                                />
                              </div>
                            </div>
                          </div>
                        </Modal.Body>
                        <Modal.Footer>
                          <Button onClick={(e) => this.handleClose(e, 'partTimeDayScheduleShow')}>
                            <Trans i18nKey={'cancel'}></Trans>
                          </Button>
                          <Button onClick={this.addPartTimeDay}>
                            {' '}
                            <Trans i18nKey={'save'}></Trans>
                          </Button>
                        </Modal.Footer>
                      </Modal>
                    </div>
                  )}
                {/* part_time by_day - end */}

                {/* part_time by_date - start */}
                {this.state.staffEmploymentType === 'part_time' &&
                  this.state.scheduleType === 'by_date' && (
                    <div className="mb-4 col-md-12">
                      <label className="form-label">
                        <Trans i18nKey={'employment.employment_schedule'}></Trans>
                      </label>
                      {this.state.partTimeDateData.map((data, i) => (
                        <div className="mb-4 employement_define" key={i}>
                          <div className="mb-4">
                            <label className="form-label">
                              <Trans i18nKey={'employment.define_date'}></Trans>{' '}
                            </label>
                            <i
                              className="fa fa-trash"
                              onClick={() => this.deletePartTimeData(i)}
                              style={{
                                float: 'right',
                                color: 'red',
                                cursor: 'pointer',
                              }}
                            ></i>
                            <div className="row">
                              <div className="col-md-3">
                                <i className="fa fa-calendar calender" aria-hidden="true"></i>
                                <DatePicker
                                  selected={
                                    data.startDateDuration !== ''
                                      ? new Date(data.startDateDuration)
                                      : ''
                                  }
                                  onChange={(date) => this.handleChangeStartDate(date, i)}
                                  className={`form-control ${
                                    getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                  }`}
                                />
                              </div>
                              <div className="pt-3 col-md-1">
                                <span className="pl-3">
                                  {' '}
                                  <Trans i18nKey={'to'}></Trans>
                                </span>
                              </div>
                              <div className="col-md-3">
                                <i className="fa fa-calendar calender" aria-hidden="true"></i>
                                <DatePicker
                                  selected={
                                    data.endDateDuration !== ''
                                      ? new Date(data.endDateDuration)
                                      : ''
                                  }
                                  minDate={
                                    data.startDateDuration !== ''
                                      ? new Date(data.startDateDuration)
                                      : ''
                                  }
                                  onChange={(date) => this.handleChangeEndDate(date, i)}
                                  className={`form-control ${
                                    getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                  }`}
                                />
                              </div>
                            </div>
                          </div>

                          <div>
                            <label className="form-label">
                              <Trans i18nKey={'employment.define_timings'}></Trans>{' '}
                            </label>
                            <table className="mb-4">
                              <tbody>
                                {data.partTimeDateSchedule.map((data, index) => (
                                  <tr key={index}>
                                    <td className="pr-4">
                                      {data.selectedDays.map((day, dayIndex) => (
                                        // day.isChecked === true &&
                                        <span key={dayIndex}>{day} </span>
                                      ))}
                                    </td>
                                    <td className="pr-4">
                                      {data.startTimeDuration} <Trans i18nKey={'to'}></Trans>{' '}
                                      {data.endTimeDuration}
                                    </td>
                                    {/* <td className="pr-4">
                                  <span>{data.selectedDays}</span>
                                </td>
                                <td className="pr-4">
                                  {data.startTimeDuration} to{" "}
                                  {data.endTimeDuration}
                                </td>
                                <td className="pr-4">
                                  <i
                                    className="fa fa-times-circle"
                                    onClick={() => this.removeFullTime(index)}
                                  ></i>
                                </td> */}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>

                          <div
                            className="remove_hover"
                            onClick={(e) => this.handleShow(e, 'partTimeDateScheduleShow', i)}
                          >
                            <i className="fa fa-plus-circle remove_hover"></i>{' '}
                            <Trans i18nKey={'employment.add_schedule'}></Trans>
                          </div>
                          <Modal
                            show={this.state.partTimeDateScheduleShow}
                            centered
                            // size="lg"
                            onHide={(e) => this.handleClose(e, 'partTimeDateScheduleShow')}
                          >
                            <Modal.Header closeButton>
                              <Modal.Title id="example-modal-sizes-title-sm">
                                <h5>
                                  {' '}
                                  <Trans i18nKey={'employment.add_schedule'}></Trans>
                                </h5>
                              </Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                              {/* <h5>Add schedule</h5> */}
                              {this.state.errorMsg !== '' && (
                                <div className="bg-gray">
                                  <p className="p-1 text-center text-red">
                                    <i className="fa fa-exclamation-circle" aria-hidden="true"></i>{' '}
                                    {this.state.errorMsg}
                                  </p>
                                </div>
                              )}
                              <div className="mb-3">
                                <label>
                                  {' '}
                                  <Trans i18nKey={'employment.staff_mode'}></Trans>
                                </label>
                                <div>
                                  {this.state.staffMode.map((data, index) => (
                                    <span className="mr-2" key={index}>
                                      <input
                                        type="checkbox"
                                        className="calendarFormRadio"
                                        onClick={(event) => this.handleCheckStaffMode(event, index)}
                                        value="checkedall"
                                      />{' '}
                                      <Trans
                                        i18nKey={`user_management.${data.name}`}
                                        values={{ onsite: indVerRename('onsite') }}
                                      />
                                    </span>
                                  ))}
                                </div>
                              </div>
                              <div className="mb-3">
                                <label>
                                  {' '}
                                  <Trans i18nKey={'employment.select_day'}></Trans>
                                </label>
                                <div>
                                  {this.state.days
                                    .filter((item) => this.state.showDayOrHide.includes(item.num))
                                    .map((data, index) => (
                                      <span
                                        key={index}
                                        className={`mr-2 schedule_days ${
                                          data.isChecked === true ? 'selected_days' : ''
                                        }`}
                                        onClick={(e) => this.handleClickDate(e, data.num)}
                                      >
                                        <Trans
                                          i18nKey={`user_management.${data.day.toLowerCase()}`}
                                        />
                                      </span>
                                    ))}
                                </div>
                              </div>
                              <div className="mb-3">
                                <label>
                                  {' '}
                                  <Trans i18nKey={'employment.select_duration'}></Trans>
                                </label>
                                <div className="row">
                                  <div className="col-md-5">
                                    <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                                    <DatePicker
                                      selected={this.state.startTime}
                                      onChange={this.handleChangeStartTime}
                                      {...(getLang() === 'ar' && { locale: 'ar' })}
                                      showTimeSelect
                                      showTimeSelectOnly
                                      timeIntervals={15}
                                      timeCaption={t('global_configuration.time')}
                                      dateFormat="h:mm aa"
                                      className={`form-control ${
                                        getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                      }`}
                                    />
                                  </div>
                                  <div className="pt-3 text-center col-md-2">
                                    {' '}
                                    <Trans i18nKey={'to'}></Trans>
                                  </div>
                                  <div className="col-md-5">
                                    <i className="fa fa-clock-o calender" aria-hidden="true"></i>
                                    <DatePicker
                                      selected={this.state.endTime}
                                      {...(getLang() === 'ar' && { locale: 'ar' })}
                                      onChange={this.handleChangeEndTime}
                                      showTimeSelect
                                      showTimeSelectOnly
                                      timeIntervals={15}
                                      timeCaption={t('global_configuration.time')}
                                      dateFormat="h:mm aa"
                                      className={`form-control ${
                                        getLang() === 'ar' ? 'icon-form-ar' : 'icon-form'
                                      }`}
                                    />
                                  </div>
                                </div>
                              </div>
                            </Modal.Body>
                            <Modal.Footer>
                              <Button
                                onClick={(e) => this.handleClose(e, 'partTimeDateScheduleShow')}
                              >
                                <Trans i18nKey={'cancel'}></Trans>
                              </Button>
                              <Button onClick={(e) => this.addPartTimeDateSchedule(e, i)}>
                                <Trans i18nKey={'save'}></Trans>
                              </Button>
                            </Modal.Footer>
                          </Modal>
                        </div>
                      ))}

                      <div onClick={this.addPartTimeDate} className="pt-3 remove_hover">
                        <i className="fa fa-plus-circle remove_hover"></i>{' '}
                        <Trans i18nKey={'employment.add_term'}></Trans>
                      </div>
                    </div>
                  )}
                {/* part_time by_date - end */}
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

Days.propTypes = {
  selectedDay: PropTypes.bool,
  onClick: PropTypes.func,
  day: PropTypes.string,
};

ValidEmployment.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(ValidEmployment));
