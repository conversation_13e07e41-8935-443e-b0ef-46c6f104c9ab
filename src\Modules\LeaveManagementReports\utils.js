import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import PropTypes from 'prop-types';
import info from 'Assets/info.svg';
// import Badge from '@mui/material/Badge';
import Tooltip from '@mui/material/Tooltip';
import React from 'react';
import CloseIcon from '@mui/icons-material/Close';
import Menu from '@mui/material/Menu';
import { Map, List, fromJS } from 'immutable';
import Loader from 'Widgets/Loader/Loader';
import moment from 'moment';
import MButton from 'Widgets/FormElements/material/Button';
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined';
import { jsUcfirstAll } from 'utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { CheckPermission } from 'Modules/Shared/Permissions';

//*************StorageContainers******************** */
const ApplicationModal = React.lazy(() =>
  import('./CollectiveReports/LeaveManagement/ApplicationModal')
);

export const dateSelectedTypeLMS = Object.freeze({
  till_date: 'Till Date',
  current_month: 'Current Month',
  last_month: 'Last Month',
  specific_date: 'Specific Date',
});

export const statusLMS = Object.freeze({
  Approved: 'Approved',
  Pending: 'Pending',
  Reject: 'Reject',
  Withdrawn: 'Withdrawn',
});
export const statusLMSQueryParams = Object.freeze({
  Approved: 'totalApprovedPercentage',
  Pending: 'totalPendingPercentage',
  Reject: 'totalRejectedPercentage',
});

export const classificationLMS = Object.freeze({
  leave: 'Leave',
  permission: 'Permission',
  on_duty: 'On-Duty',
});

export const ITEM_HEIGHT = 48;

export const AccordionLms = styled((props) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '&:not(:last-child)': {
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  '&:before': {
    display: 'none',
  },
}));

export const AccordionSummaryLms = styled((props) => (
  <MuiAccordionSummary
    expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
    {...props}
  />
))(({ theme }) => ({
  flexDirection: 'row-reverse',
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(90deg)',
  },
  '& .MuiAccordionSummary-content': {
    margin: '0px',
  },
}));

export const AccordionDetailsLms = styled(MuiAccordionDetails)(({ theme }) => ({
  borderTop: '1px solid rgba(0, 0, 0, .125)',
}));

//*************Functions**************************** */

export const queryParamsConstruct = (key, queryArray) => {
  let formattedString = '';
  if (!fromJS(queryArray).size) return formattedString;
  queryArray.map((type) => {
    return (formattedString += `&%${key}[]=${type}`);
  });
  return formattedString;
};

export const addingZeroToPrefixOver = (item) => {
  const splitItem = item.toString().split('.');
  const updatedItem = Number(splitItem[1]) > 0 ? item : splitItem[0];
  if (Number(updatedItem) === 0) return updatedItem;
  return Number(updatedItem) <= 9 ? '0' + updatedItem : updatedItem;
};

// export const badgeCountShow = (key, headerFIlter) => {
//   return (
//     headerFIlter.getIn(['applyBtnProceededData', key], List()).size !== 0 && (
//       <Badge
//         sx={{
//           '& .MuiBadge-badge': {
//             color: 'white',
//             backgroundColor: '#aba3a3',
//           },
//         }}
//         badgeContent={headerFIlter.getIn(['applyBtnProceededData', key], List()).size}
//         className="ml-4 mr-2"
//       />
//     )
//   );
// };
export const studentNameCodeWise = (studentId) => {
  return `${studentId.get('user_id', '')} - ${studentId.getIn(
    ['name', 'first'],
    ''
  )} ${studentId.getIn(['name', 'last'], '')}`;
};

export function getNumberWithOrdinal(n) {
  var s = ['th', 'st', 'nd', 'rd'],
    v = n % 100;
  return n + (s[(v - 20) % 10] || s[v] || s[0]);
}

export const dateAndTimeRangeLms = (dateAndTimeRange) => {
  const startDate = moment(dateAndTimeRange.get('startDate', '')).format('DD MMM YYYY').split(' ');
  const endDate = moment(dateAndTimeRange.get('endDate', '')).format('DD MMM YYYY').split(' ');
  if (startDate[2] !== endDate[2])
    return `${getNumberWithOrdinal(startDate[0])} ${startDate[1]} ${
      startDate[2]
    } to ${getNumberWithOrdinal(endDate[0])} ${endDate[1]} ${endDate[2]}`;
  if (startDate[1] !== endDate[1])
    return `${getNumberWithOrdinal(startDate[0])} ${startDate[1]}
   to ${getNumberWithOrdinal(endDate[0])} ${endDate[1]}, ${endDate[2]}`;
  return `${getNumberWithOrdinal(startDate[0])} to ${getNumberWithOrdinal(endDate[0])} ${
    endDate[1]
  }, ${endDate[2]}`;
};

//*************Components**************************** */
const PaperReuse = ({ key = '', label, handleClick }) => {
  return (
    <Paper
      elevation={0}
      key={key}
      className="p-2 mr-2 border border-primary LmsReport-content-Width"
    >
      <div className="d-flex align-items-center">
        <div className="text-primary bold f-15">{label}</div>
        <CloseIcon
          color="primary"
          className="ml-2 cursor-pointer"
          sx={{ fontSize: 18 }}
          onClick={() => handleClick()}
        />
      </div>
    </Paper>
  );
};
PaperReuse.propTypes = {
  key: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  label: PropTypes.string,
  handleClick: PropTypes.func,
};
export const ApplicationTableChip = ({
  headerFIlter,
  allStudents,
  allPrograms,
  setHeaderFilter,
}) => {
  return (
    <div className="d-flex flex-wrap mt-1 mb-1">
      {headerFIlter.getIn(['applyBtnProceededData', 'student'], List()).size > 0 &&
        allStudents.size !==
          headerFIlter.getIn(['applyBtnProceededData', 'student'], List()).size && (
          <PaperReuse
            label={`Selected Student (${
              headerFIlter.getIn(['applyBtnProceededData', 'student'], List()).size
            })`}
            handleClick={() =>
              setHeaderFilter((prev) =>
                prev
                  .setIn(['applyBtnProceededData', 'student'], List())
                  .setIn(['applyBtnProceededData', 'programIdsOfStudents'], List())
                  .setIn(['applyBtnProceededData', 'studentsObject'], List())
                  .setIn(['currentData', 'student'], List())
                  .setIn(['currentData', 'programIdsOfStudents'], List())
                  .setIn(['currentData', 'studentsObject'], List())
              )
            }
          />
        )}
      {headerFIlter.getIn(['applyBtnProceededData', 'program'], List()).size > 0 &&
        allPrograms.size !==
          headerFIlter.getIn(['applyBtnProceededData', 'program'], List()).size && (
          <PaperReuse
            label={`Selected Program (${
              headerFIlter.getIn(['applyBtnProceededData', 'program'], List()).size
            })`}
            handleClick={() =>
              setHeaderFilter((prev) =>
                prev
                  .setIn(['applyBtnProceededData', 'program'], List())
                  .setIn(['currentData', 'program'], List())
              )
            }
          />
        )}
      {!headerFIlter.getIn(['applyBtnProceededData', 'term'], Map()).isEmpty() &&
        headerFIlter
          .getIn(['applyBtnProceededData', 'term'], Map())
          .entrySeq()
          .map(([key, value]) => {
            return (
              <PaperReuse
                key={key}
                label={`${jsUcfirstAll(key)} (${value.size})`}
                handleClick={() =>
                  setHeaderFilter((prev) =>
                    prev
                      .deleteIn(['applyBtnProceededData', 'term', key], '')
                      .deleteIn(['currentData', 'term', key], '')
                  )
                }
              />
            );
          })}
      {[1, 2].includes(
        headerFIlter.getIn(['applyBtnProceededData', 'classification'], List()).size
      ) && (
        <PaperReuse
          label={`Selected Classification (${
            headerFIlter.getIn(['applyBtnProceededData', 'classification'], List()).size
          })`}
          handleClick={() =>
            setHeaderFilter((prev) =>
              prev
                .setIn(['applyBtnProceededData', 'classification'], List())
                .setIn(['currentData', 'classification'], List())
            )
          }
        />
      )}
      {[1, 2, 3].includes(headerFIlter.getIn(['applyBtnProceededData', 'status'], List()).size) && (
        <PaperReuse
          label={`Selected Status (${
            headerFIlter.getIn(['applyBtnProceededData', 'status'], List()).size
          })`}
          handleClick={() =>
            setHeaderFilter((prev) =>
              prev
                .setIn(['applyBtnProceededData', 'status'], List())
                .setIn(['currentData', 'status'], List())
            )
          }
        />
      )}
    </div>
  );
};
ApplicationTableChip.propTypes = {
  headerFIlter: PropTypes.instanceOf(Map),
  allPrograms: PropTypes.instanceOf(List),
  allStudents: PropTypes.instanceOf(List),
  setHeaderFilter: PropTypes.func,
};

export const MenuReuse = ({ children, open, setOpen, isManualClose }) => {
  return (
    <Menu
      id="long-menu"
      anchorEl={open.get('anchorEl', null)}
      open={open.get('isOpen', '')}
      onClose={isManualClose ? setOpen : () => {}}
      PaperProps={{
        style: {
          maxHeight: ITEM_HEIGHT * 10.5,
        },
      }}
    >
      {children}
    </Menu>
  );
};
MenuReuse.propTypes = {
  children: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  setOpen: PropTypes.func,
  width: PropTypes.string,
  isManualClose: PropTypes.bool,
  open: PropTypes.instanceOf(Map),
};

export const ApplicationTableSubHeader = ({ applicationHeaders }) => {
  const addingZeroPrefix = (key) => {
    return appCount.get(key, 0) <= 9 ? '0' + appCount.get(key, 0) : appCount.get(key, 0);
  };
  const appCount = applicationHeaders.getIn(['applicationStatusCount', 0], Map());

  return (
    <div className="d-flex justify-content-between border-bottom pb-3">
      <div>
        <p className="digi-reports-h6 mb-0">Application Status</p>
        <span className="pr-2 f-14 text-approved"> Approved : </span>
        <span className="pr-2 f-14"> {addingZeroPrefix('totalApproved')}</span>
        <span className="pr-2 pl-1 f-14 text-divider-color"> | </span>

        <span className="pr-2 f-14 text-pending"> Pending : </span>
        <span className="pr-2 f-14">{addingZeroPrefix('totalPending')}</span>
        <span className="pr-2 pl-1 f-14 text-divider-color"> | </span>
        <span className="pr-2 f-14 text-reject"> Reject : </span>
        <span className="pr-2 f-14">{addingZeroPrefix('totalReject')}</span>
        <span className="pr-2 pl-1 f-14 text-divider-color"> | </span>
        <span className="pr-2 f-14 text-withdraw"> Withdraw : </span>
        <span className="pr-2 f-14">{addingZeroPrefix('totalWithDrawn')}</span>
      </div>
      {CheckPermission(
        'subTabs',
        'Leave Management',
        'Student Report',
        'View',
        'Student Leave Management Reports',
        'View',
        'Leave Management',
        'Export'
      ) && (
        <div>
          <MButton
            variant="contained"
            color="primary"
            startIcon={<FileUploadOutlinedIcon />}
            aria-label="more"
            aria-controls="long-menuAll"
            aria-haspopup="true"
          >
            Export
          </MButton>
        </div>
      )}
    </div>
  );
};
ApplicationTableSubHeader.propTypes = {
  applicationHeaders: PropTypes.instanceOf(Map),
};

export const durationWithSingularAndPlural = (classification, duration) => {
  let data = classification === 'permission' ? 'Hour' : 'Day';
  if (Number(duration) > 1) {
    data += 's';
  }
  return data;
};

export const ApplicationTableBody = ({
  applicationStatus,
  yearWithProgram,
  isLoadingType,
  dispatch,
  open,
  isLoading,
  setOpen,
}) => {
  if (!applicationStatus.size) {
    return (
      <tbody className="">
        <tr className="tr-change remove_hover f-15 digi-gray">
          <td className="text-center" colSpan={11}>
            {isLoading && isLoadingType === 'application' ? 'Loading...' : 'No Data Found...'}
          </td>
        </tr>
      </tbody>
    );
  }
  const studentView = CheckPermission(
    'subTabs',
    'Leave Management',
    'Student Report',
    'View',
    'Student Leave Management Reports',
    'View',
    'Leave Management',
    'Student View'
  );
  return (
    <tbody className="">
      {applicationStatus.map((item, index) => {
        const createdDate = moment(new Date(item.get('createdAt', '')))
          .format('DD MMM YYYY')
          .split(' ');
        const formatCreatedDate = `${getNumberWithOrdinal(createdDate[0])} ${createdDate[1]}, ${
          createdDate[2]
        }`;
        const appliedFor = dateAndTimeRangeLms(item.get('dateAndTimeRange', Map()));
        return (
          <tr className="tr-change f-14 digi-gray" key={item.get('_id', index)}>
            <td className="">
              <div
                className={`d-flex align-items-center ${
                  studentView ? 'text-skyblue  text_underline' : ''
                } remove_hover`}
                onClick={() => {
                  if (studentView) {
                    setOpen((prev) => prev.set('type', 'Modal').set('index', index));
                  }
                }}
              >
                {studentNameCodeWise(item.get('studentId', Map()))}
              </div>
            </td>

            <td className="">
              <div>{getShortString(item.getIn(['programId', 'name'], ''), 35)}</div>
            </td>

            <td className="">
              <div className="text-capitalize">
                {item.get('year', '')?.replace('year', 'Year ')}/{item.get('level', '')}/
                {jsUcfirstAll(item.get('term', ''))}
              </div>
            </td>

            <td className="">
              <div className="">{formatCreatedDate}</div>
            </td>
            <td className="">
              <div className="">{appliedFor}</div>
            </td>
            <td className="">
              <div className="text-capitalize">
                {addingZeroToPrefixOver(item.get('noOfHours', 0))}{' '}
                {durationWithSingularAndPlural(
                  item.get('classificationType', ''),
                  item.get('noOfHours', 0)
                )}
              </div>
            </td>

            <td className="">
              <div>
                {getShortString(
                  jsUcfirstAll(item.get('categoryName', '')) +
                    '/' +
                    classificationLMS[item.get('classificationType', '')],
                  60
                )}
              </div>
            </td>

            <td className="">
              <div
                className={`d-flex align-items-center lms-color-${
                  item.get('approvalStatus', '').includes('Pending')
                    ? 'Pending'
                    : item.get('approvalStatus', '')
                } remove_hover`}
              >
                {item.get('approvalStatus', '').includes('Pending')
                  ? 'Pending'
                  : item.get('approvalStatus', '')}
              </div>
            </td>
            {open.get('type', '') === 'Modal' && open.get('index', '') === index && (
              <React.Suspense fallback={<Loader isLoading={true} />}>
                <ApplicationModal
                  open={true}
                  dispatch={dispatch}
                  yearWithProgram={yearWithProgram}
                  formatCreatedDate={formatCreatedDate}
                  appliedFor={appliedFor}
                  handleClose={() => setOpen(Map())}
                  item={item}
                  studentNameCodeWise={studentNameCodeWise}
                />
              </React.Suspense>
            )}
          </tr>
        );
      })}
    </tbody>
  );
};

ApplicationTableBody.propTypes = {
  applicationHeaders: PropTypes.instanceOf(Map),
  applicationStatus: PropTypes.instanceOf(List),
  yearWithProgram: PropTypes.instanceOf(Map),
  dispatch: PropTypes.func,
  isLoading: PropTypes.bool,
  isLoadingType: PropTypes.string,
  open: PropTypes.instanceOf(Map),
  setOpen: PropTypes.func,
};

export const CustomTooltip = ({ mergedSchedule, tooltipClass }) => {
  const longText = (
    <div className="ToolTipColorBorder f-13 font-weight-normal text-light">
      <div className="py-2 px-3 tooltip-head-color">Delivery Type & Topic</div>
      <div className="color-tooltip">
        {mergedSchedule.map((item, index) => (
          <div className="py-2 px-3" key={index}>
            <span className="mr-3 text-capitalize">
              {item.get('delivery_symbol', '') + item.get('delivery_no')}
            </span>
            <span className="text-capitalize">{item.get('session_topic', '')}</span>
          </div>
        ))}
      </div>
    </div>
  );
  return (
    <div>
      <Tooltip classes={{ tooltip: tooltipClass }} arrow title={longText} placement="bottom-end">
        <img width="15" height="15" src={info} alt="info" />
      </Tooltip>
    </div>
  );
};
CustomTooltip.propTypes = {
  mergedSchedule: PropTypes.instanceOf(List),
  tooltipClass: PropTypes.object,
  isLoadingType: PropTypes.string,
};
