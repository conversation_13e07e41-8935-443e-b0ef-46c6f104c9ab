import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import TableContainer from '@mui/material/TableContainer';
import Table from '@mui/material/Table';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import MuiTableCell from '@mui/material/TableCell';
import TableBody from '@mui/material/TableBody';
import { ThemeProvider, withStyles } from '@mui/styles';

import CheckCircleGreenIcon from '../../../Assets/alert2.png';
import { MUI_THEME } from '../../../utils';

const TableCell = withStyles({ root: { padding: 10 } })(MuiTableCell);

function ViewCourseScheduleStatus({ show, handleClose, courses }) {
  function getCourseCoordinatorName(course) {
    const courseCoordinator = course.getIn(['course_coordinator', 'user_name'], Map());
    return `${courseCoordinator.get('first', '')} ${courseCoordinator.get('last', '')}`;
  }

  return (
    <Dialog open={show} onClose={handleClose} disableAutoFocus>
      <DialogContent>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Course Code / Name</TableCell>
                <TableCell>Course Coordinator</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {courses.map((row) => (
                <TableRow key={row.get('_course_id')}>
                  <TableCell>
                    {row.get('schedule_status') ? (
                      <img src={CheckCircleGreenIcon} alt="check" className="mr-1" />
                    ) : (
                      <i
                        className="fa fa-exclamation-circle f-14 text-gray mr-1"
                        aria-hidden="true"
                      ></i>
                    )}
                    {`${row.get('courses_number')} - ${row.get('courses_name')}`}
                  </TableCell>
                  <TableCell>{getCourseCoordinatorName(row)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions>
        <ThemeProvider theme={MUI_THEME}>
          <Button autoFocus onClick={handleClose} color="primary" variant="outlined">
            CLOSE
          </Button>
        </ThemeProvider>
      </DialogActions>
    </Dialog>
  );
}

ViewCourseScheduleStatus.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
  courses: PropTypes.instanceOf(List),
};

export default ViewCourseScheduleStatus;
