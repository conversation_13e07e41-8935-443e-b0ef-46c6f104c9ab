import React, { Fragment } from 'react';
import { Block<PERSON>rapper, FlexWrapper, Label, Input } from '../../Styled';
import { connect } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';
import { getCourses } from '../../../../_reduxapi/actions/calender';
import { NotificationManager } from 'react-notifications';
import PropTypes from 'prop-types';
import { indVerRename, levelRename, ucFirst } from '../../../../utils';
import { Trans } from 'react-i18next';

const ChooseLevel = (props) => {
  const { data, getCourses, load, program_calender_id, programId, urlTerm } = props;
  const match = useRouteMatch();
  const active = match.params.year || 'year1';
  const [nonRotation, setNonRotation] = data;
  const setLoad = load[1];

  const currentData =
    props[active] !== undefined && props[active]['level'] !== undefined
      ? props[active]['level'].filter((item) => item.term === urlTerm)
      : [];

  return (
    <Fragment>
      <BlockWrapper>
        <h6>
          <Trans
            i18nKey={'program_calendar.select_a_level'}
            values={{ level: indVerRename('Level', programId).toLowerCase() }}
          ></Trans>
          {urlTerm !== '' ? `(${urlTerm})(${active.replace('year', 'Year ')})` : ''}
        </h6>
        {currentData && currentData.length > 0 && (
          <>
            {currentData.map((current, activeIndex) => {
              let level_title = current.term !== '' ? ucFirst(current.level_no) : '';
              let titleName = current.level_no;
              let term = current.term;
              let level_no = current.level_no;
              let start_date = current.start_date !== undefined ? current.start_date : '';
              let end_date = current.end_date !== undefined ? current.end_date : '';
              return (
                <FlexWrapper mg="10px 0" key={activeIndex}>
                  {level_title !== '' && (
                    <>
                      <Input
                        type="radio"
                        name="title"
                        value={titleName}
                        id="level_one"
                        disabled={nonRotation.disable_items}
                        checked={nonRotation.title === titleName ? true : false}
                        onChange={(e) => {
                          if (current.rotation_count === 0) {
                            setLoad(true);
                            getCourses(
                              program_calender_id,
                              term,
                              level_no,
                              NotificationManager,
                              setLoad
                            );
                          }
                          setNonRotation({
                            type: 'LEVEL_SELECT',
                            payload: e.target.value,
                            name: e.target.name,
                            year: active,
                            start_date: start_date,
                            end_date: end_date,
                            levels: currentData,
                          });
                        }}
                      />
                      <Label mg="0px" fs="18px" htmlFor="level_one">
                        {levelRename(level_title, programId)}
                      </Label>
                    </>
                  )}
                </FlexWrapper>
              );
            })}
          </>
        )}
      </BlockWrapper>
    </Fragment>
  );
};

ChooseLevel.propTypes = {
  data: PropTypes.object,
  getCourses: PropTypes.func,
  load: PropTypes.object,
  program_calender_id: PropTypes.string,
  urlTerm: PropTypes.string,
  programId: PropTypes.string,
};

const mapStateToProps = ({ calender }) => ({
  year1: calender.year1,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
  program_calender_id: calender.program_calender_id,
});

export default connect(mapStateToProps, { getCourses })(ChooseLevel);
