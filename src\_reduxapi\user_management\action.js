import { createAction } from '../util';
import axios from '../../axios';
import * as FileSaver from 'file-saver';
import { t } from 'i18next';
import { envSignUpService } from 'utils';

export const GETTING_ROLES_LIST_REQUEST = 'GETTING_ROLES_LIST_REQUEST';
export const GETTING_ROLES_LIST_SUCCESS = 'GETTING_ROLES_LIST_SUCCESS';
export const GETTING_ROLES_LIST_FAILURE = 'GETTING_ROLES_LIST_FAILURE';

const getRolesListRequest = createAction(GETTING_ROLES_LIST_REQUEST, 'isLoading');
const getRolesListSuccess = createAction(GETTING_ROLES_LIST_SUCCESS, 'data');
const getRolesListFailure = createAction(GETTING_ROLES_LIST_FAILURE, 'error');

export function getRolesList() {
  return function (dispatch) {
    dispatch(getRolesListRequest(true));
    axios
      .get(`role/role_list`)
      .then((res) => {
        const lists =
          res.data.data &&
          res.data.data.length > 0 &&
          res.data.data
            // .filter((item) => item.isActive === true)
            .map((item) => {
              return { name: item.name, value: item._id, isActive: item.isActive };
            });
        lists.unshift({ name: '', value: '' });
        dispatch(getRolesListSuccess(lists));
      })
      .catch((error) => dispatch(getRolesListFailure(error)));
  };
}

export const ADD_ROLE_LIST_SUCCESS = 'ADD_ROLE_LIST_SUCCESS';

const addRoleListSuccess = createAction(ADD_ROLE_LIST_SUCCESS, 'data');

export function addRoleList(ids) {
  return function (dispatch) {
    dispatch(addRoleListSuccess(ids));
  };
}
//-----------------------------------------------------------------
export const USER_EXPORT_REQUEST = 'USER_EXPORT_REQUEST';
export const USER_EXPORT_REQUEST_FAILURE = 'USER_EXPORT_REQUEST_FAILURE';
export const USER_EXPORT_REQUEST_SUCCESS = 'USER_EXPORT_REQUEST_SUCCESS';

const userExportRequest = createAction(USER_EXPORT_REQUEST, 'isLoading');
const userExportRequestSuccess = createAction(USER_EXPORT_REQUEST_SUCCESS, 'message');
const userExportRequestFailure = createAction(USER_EXPORT_REQUEST_FAILURE, 'error');

export function exportHandler(userType, status) {
  return function (dispatch) {
    dispatch(userExportRequest(true));
    axios
      .get(
        `/user/export/${userType}/${status}/?fields=["name","family_name","user_type","academic","gender","email","batch","enrollment_year","dob","mobile","nationality_id","user_state"]`,
        { responseType: 'arraybuffer' }
      )
      .then((res) => {
        const message = t('user_management.exported_successfully');
        const current = new Date();
        const date = `${current.getDate()}/${current.getMonth() + 1}/${current.getFullYear()}`;
        var blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(
          blob,
          `${t(`user_management.${userType}`)}-${t(`user_management.${status}`)}-${date}.xlsx`
        );
        dispatch(userExportRequestSuccess(message));
      })
      .catch((error) => dispatch(userExportRequestFailure(error)));
  };
}

export const HANDLE_ASSIGNED_ROLE_SUCCESS = 'HANDLE_ASSIGNED_ROLE_SUCCESS';

const handleAssignedRoleSuccess = createAction(HANDLE_ASSIGNED_ROLE_SUCCESS, 'data');

export function handleAssignedRole(data) {
  return function (dispatch) {
    dispatch(handleAssignedRoleSuccess(data));
  };
}

export const POST_DEPARTMENT_LIST_REQUEST = 'POST_DEPARTMENT_LIST_REQUEST';
export const POST_DEPARTMENT_LIST_SUCCESS = 'POST_DEPARTMENT_LIST_SUCCESS';
export const POST_DEPARTMENT_LIST_FAILURE = 'POST_DEPARTMENT_LIST_FAILURE';

const postDepartmentListRequest = createAction(POST_DEPARTMENT_LIST_REQUEST, 'isLoading', 'index');
const postDepartmentListSuccess = createAction(POST_DEPARTMENT_LIST_SUCCESS, 'data', 'index');
const postDepartmentListFailure = createAction(POST_DEPARTMENT_LIST_FAILURE, 'error', 'index');

export const postDepartmentList = (data, index) => {
  return (dispatch) => {
    dispatch(postDepartmentListRequest(true, index));
    return axios
      .post('/role_assign/department_list', data)
      .then((res) => {
        let data = [];
        if (res.data.data && res.data.data.length > 0) {
          data = res.data.data.map((item) => {
            let programName = item.program_name.split(' ');
            return { name: item.department_title, id: item._id, programName: programName[0] };
          });
        }
        dispatch(postDepartmentListSuccess(data, index));
      })
      .catch((errors) => dispatch(postDepartmentListFailure(errors, index)));
  };
};

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_SELECTED_STAFF_SUCCESS = 'SET_SELECTED_STAFF_SUCCESS';
const setSelectedStaffSuccess = createAction(SET_SELECTED_STAFF_SUCCESS, 'data');
export function setSelectedStaff(data) {
  return function (dispatch) {
    dispatch(setSelectedStaffSuccess(data));
  };
}

export const ASSIGN_ROLE_LIST_REQUEST = 'ASSIGN_ROLE_LIST_REQUEST';
export const ASSIGN_ROLE_LIST_SUCCESS = 'ASSIGN_ROLE_LIST_SUCCESS';
export const ASSIGN_ROLE_LIST_FAILURE = 'ASSIGN_ROLE_LIST_FAILURE';

const assignRoleSubmitRequest = createAction(ASSIGN_ROLE_LIST_REQUEST, 'isLoading');
const assignRoleSubmitSuccess = createAction(ASSIGN_ROLE_LIST_SUCCESS, 'data');
const assignRoleSubmitFailure = createAction(ASSIGN_ROLE_LIST_FAILURE, 'error');

export const assignRoleSubmit = (data, type, callBack) => {
  return (dispatch) => {
    dispatch(assignRoleSubmitRequest(true));
    return axios
      .post('/role_assign', data)
      .then((res) => {
        dispatch(assignRoleSubmitSuccess(type));
        setTimeout(() => {
          callBack();
        }, 1000);
      })
      .catch((errors) => dispatch(assignRoleSubmitFailure(errors)));
  };
};

export const GET_ASSIGN_ROLE_REQUEST = 'GET_ASSIGN_ROLE_REQUEST';
export const GET_ASSIGN_ROLE_SUCCESS = 'GET_ASSIGN_ROLE_SUCCESS';
export const GET_ASSIGN_ROLE_FAILURE = 'GET_ASSIGN_ROLE_FAILURE';

const getAssignRoleDataRequest = createAction(GET_ASSIGN_ROLE_REQUEST, 'isLoading');
const getAssignRoleDataSuccess = createAction(GET_ASSIGN_ROLE_SUCCESS, 'data');
const getAssignRoleDataFailure = createAction(GET_ASSIGN_ROLE_FAILURE, 'error');

export function getAssignRoleData(id) {
  return function (dispatch) {
    dispatch(getAssignRoleDataRequest(true));
    axios
      .get(`role_assign/${id}`)
      .then((res) => {
        dispatch(getAssignRoleDataSuccess(res.data.data));
      })
      .catch((error) => dispatch(getAssignRoleDataFailure(error)));
  };
}

export const REPORTS_TO_LIST_REQUEST = 'REPORTS_TO_LIST_REQUEST';
export const REPORTS_TO_LIST_SUCCESS = 'REPORTS_TO_LIST_SUCCESS';
export const REPORTS_TO_LIST_FAILURE = 'REPORTS_TO_LIST_FAILURE';

const reportsToListRequest = createAction(REPORTS_TO_LIST_REQUEST, 'isLoading');
const reportsToListSuccess = createAction(REPORTS_TO_LIST_SUCCESS, 'data');
const reportsToListFailure = createAction(REPORTS_TO_LIST_FAILURE, 'error');

export const searchReportsTo = (data) => {
  return (dispatch) => {
    dispatch(reportsToListRequest(true));
    return axios
      .post('/role_assign/report_to_list', data)
      .then((res) => {
        dispatch(reportsToListSuccess(res.data.data));
      })
      .catch((errors) => dispatch(reportsToListFailure(errors)));
  };
};

export const GET_VACCINATION_TYPE_LIST_REQUEST = 'GET_VACCINATION_TYPE_LIST_REQUEST';
export const GET_VACCINATION_TYPE_LIST_SUCCESS = 'GET_VACCINATION_TYPE_LIST_SUCCESS';
export const GET_VACCINATION_TYPE_LIST_FAILURE = 'GET_VACCINATION_TYPE_LIST_FAILURE';

const getVaccinationTypeListRequest = createAction(GET_VACCINATION_TYPE_LIST_REQUEST, 'isLoading');
const getVaccinationTypeListSuccess = createAction(GET_VACCINATION_TYPE_LIST_SUCCESS, 'data');
const getVaccinationTypeListFailure = createAction(GET_VACCINATION_TYPE_LIST_FAILURE, 'error');

export function getVaccinationTypeList(headers = {}) {
  return function (dispatch) {
    dispatch(getVaccinationTypeListRequest(true));
    axios
      .get(`vaccination`, headers)
      .then((res) => {
        dispatch(getVaccinationTypeListSuccess(res.data.data.data));
      })
      .catch((error) => dispatch(getVaccinationTypeListFailure(error)));
  };
}

export const SAVE_VACCINE_DATA_REQUEST = 'SAVE_VACCINE_DATA_REQUEST';
export const SAVE_VACCINE_DATA_SUCCESS = 'SAVE_VACCINE_DATA_SUCCESS';
export const SAVE_VACCINE_DATA_FAILURE = 'SAVE_VACCINE_DATA_FAILURE';

const saveVaccineDataRequest = createAction(SAVE_VACCINE_DATA_REQUEST);
const saveVaccineDataSuccess = createAction(SAVE_VACCINE_DATA_SUCCESS, 'data', 'operation');
const saveVaccineDataFailure = createAction(SAVE_VACCINE_DATA_FAILURE, 'error');

export function saveVaccineData({ mode, requestBody, id, callback, headers = {} }) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveVaccineDataRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](
        `user_vaccination_details${mode === 'update' ? '/' + id : ''}`,
        requestBody,
        headers
      )
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
          };
          callback && callback();
          dispatch(saveVaccineDataSuccess(res.data.data, operation[mode]));
        })
        .catch((error) => {
          dispatch(saveVaccineDataFailure(error));
        });
    };
  }
}

export const GET_USER_VACCINATION_DETAIL_REQUEST = 'GET_USER_VACCINATION_DETAIL_REQUEST';
export const GET_USER_VACCINATION_DETAIL_SUCCESS = 'GET_USER_VACCINATION_DETAIL_SUCCESS';
export const GET_USER_VACCINATION_DETAIL_FAILURE = 'GET_USER_VACCINATION_DETAIL_FAILURE';

const getUserVaccinationDetailRequest = createAction(
  GET_USER_VACCINATION_DETAIL_REQUEST,
  'isLoading'
);
const getUserVaccinationDetailSuccess = createAction(GET_USER_VACCINATION_DETAIL_SUCCESS, 'data');
const getUserVaccinationDetailFailure = createAction(GET_USER_VACCINATION_DETAIL_FAILURE, 'error');

export function getUserVaccinationDetail(id, headers = {}) {
  return function (dispatch) {
    dispatch(getUserVaccinationDetailRequest(true));
    axios
      .get(`/user_vaccination_details/${id}`, headers)
      .then((res) => {
        dispatch(getUserVaccinationDetailSuccess(res.data.data.data));
      })
      .catch((error) => dispatch(getUserVaccinationDetailFailure(error)));
  };
}

export const SAVE_USER_DOCUMENT_REQUEST = 'SAVE_USER_DOCUMENT_REQUEST';
export const SAVE_USER_DOCUMENT_SUCCESS = 'SAVE_USER_DOCUMENT_SUCCESS';
export const SAVE_USER_DOCUMENT_FAILURE = 'SAVE_USER_DOCUMENT_FAILURE';

const saveUserDocumentsRequest = createAction(SAVE_USER_DOCUMENT_REQUEST, 'isLoading');
const saveUserDocumentsSuccess = createAction(SAVE_USER_DOCUMENT_SUCCESS, 'data');
const saveUserDocumentsFailure = createAction(SAVE_USER_DOCUMENT_FAILURE, 'error');

export const saveUserDocuments = ({ permissionName, data, userId, userType, callBack }) => {
  return (dispatch) => {
    dispatch(saveUserDocumentsRequest(true));
    const institutionId = envSignUpService('REACT_APP_INSTITUTION_ID', false);
    const headers = institutionId
      ? {
          headers: {
            _institution_id: institutionId,
          },
        }
      : {};
    return axios
      .post(
        `/user_vaccination_details/document_upload/?user_id=${userId}&user_type=${userType}`,
        data,
        headers
      )
      .then((res) => {
        callBack && callBack();
        dispatch(saveUserDocumentsSuccess(permissionName));
      })
      .catch((errors) => dispatch(saveUserDocumentsFailure(errors)));
  };
};

export const SAVE_VACCINE_TYPE_REQUEST = 'SAVE_VACCINE_TYPE_REQUEST';
export const SAVE_VACCINE_TYPE_SUCCESS = 'SAVE_VACCINE_TYPE_SUCCESS';
export const SAVE_VACCINE_TYPE_FAILURE = 'SAVE_VACCINE_TYPE_FAILURE';

const saveVaccineTypeRequest = createAction(SAVE_VACCINE_TYPE_REQUEST);
const saveVaccineTypeSuccess = createAction(SAVE_VACCINE_TYPE_SUCCESS, 'data', 'operation');
const saveVaccineTypeFailure = createAction(SAVE_VACCINE_TYPE_FAILURE, 'error');

export function saveVaccineType({ mode, requestBody, id, callback }) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveVaccineTypeRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](`vaccination${mode !== 'create' ? '/' + id : ''}`, requestBody)
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
          };
          callback && callback();
          dispatch(saveVaccineTypeSuccess(res.data.data, operation[mode]));
        })
        .catch((error) => {
          dispatch(saveVaccineTypeFailure(error));
        });
    };
  }
}

export const GET_UML_REQUEST = 'GET_UML_REQUEST';
export const GET_UML_SUCCESS = 'GET_UML_SUCCESS';
export const GET_UML_FAILURE = 'GET_UML_FAILURE';

const getUserManagementListRequest = createAction(GET_UML_REQUEST, 'isLoading');
const getUserManagementListSuccess = createAction(GET_UML_SUCCESS, 'data');
const getUserManagementListFailure = createAction(GET_UML_FAILURE, 'error');

export function getUserManagementList(
  { searchText = '', type, status, limit = 10, pageLength = 1, slug = '' },
  successCallBack = () => {},
  errorCallBack = () => {}
) {
  return function (dispatch) {
    dispatch(getUserManagementListRequest(true));
    let endPoint;
    if (searchText !== '') {
      endPoint = `user/user_search/${type}/${status}/${searchText}?limit=${limit}&pageNo=${pageLength}`;
    } else {
      endPoint = `user/${slug}/${type}/${status}?limit=${limit}&pageNo=${pageLength}`;
    }
    axios
      .get(`${endPoint}`)
      .then((res) => {
        dispatch(getUserManagementListSuccess(res.data.data));
        successCallBack && successCallBack(res);
      })
      .catch((error) => {
        dispatch(getUserManagementListFailure(error));
        errorCallBack && errorCallBack();
      });
  };
}

export const GETTING_RE_REGISTER_FACE_LIST_REQUEST = 'GETTING_RE_REGISTER_FACE_LIST_REQUEST';
export const GETTING_RE_REGISTER_FACE_LIST_SUCCESS = 'GETTING_RE_REGISTER_FACE_LIST_SUCCESS';
export const GETTING_RE_REGISTER_FACE_LIST_FAILURE = 'GETTING_RE_REGISTER_FACE_LIST_FAILURE';

const getReRegisterFaceListRequest = createAction(
  GETTING_RE_REGISTER_FACE_LIST_REQUEST,
  'isLoading'
);
const getReRegisterFaceListSuccess = createAction(GETTING_RE_REGISTER_FACE_LIST_SUCCESS, 'data');
const getReRegisterFaceListFailure = createAction(GETTING_RE_REGISTER_FACE_LIST_FAILURE, 'error');

export function getReRegisterFaceList({ limit = 4, pageNo = 1, filter = '', statusFilter = [] }) {
  return function (dispatch) {
    dispatch(getReRegisterFaceListRequest(true));
    let statusArray = '';
    if (statusFilter.length > 0) {
      statusArray = statusFilter.map((status) => `statusFilter[]=${status}`).join('&');
    }
    axios
      .get(
        `/digiclass/faceReRegister/reRegisterFaceList?limit=${limit}&pageNo=${pageNo}${
          filter !== '' ? `&filter=${filter}` : ``
        }${statusArray !== '' ? `&${statusArray}` : ''}`
      )
      .then((res) => {
        dispatch(getReRegisterFaceListSuccess(res.data));
      })
      .catch((error) => dispatch(getReRegisterFaceListFailure(error)));
  };
}

export const GETTING_RE_REGISTER_SCHEDULE_LIST_REQUEST =
  'GETTING_RE_REGISTER_SCHEDULE_LIST_REQUEST';
export const GETTING_RE_REGISTER_SCHEDULE_LIST_SUCCESS =
  'GETTING_RE_REGISTER_SCHEDULE_LIST_SUCCESS';
export const GETTING_RE_REGISTER_SCHEDULE_LIST_FAILURE =
  'GETTING_RE_REGISTER_SCHEDULE_LIST_FAILURE';

const getReRegisterScheduleListRequest = createAction(
  GETTING_RE_REGISTER_SCHEDULE_LIST_REQUEST,
  'isLoading'
);
const getReRegisterScheduleListSuccess = createAction(
  GETTING_RE_REGISTER_SCHEDULE_LIST_SUCCESS,
  'data'
);
const getReRegisterScheduleListFailure = createAction(
  GETTING_RE_REGISTER_SCHEDULE_LIST_FAILURE,
  'error'
);

export function getReRegisterScheduleList({ userId, status = true }) {
  return function (dispatch) {
    dispatch(getReRegisterScheduleListRequest(status));
    axios
      .get(`/digiclass/faceReRegister/userBasedScheduleList?userId=${userId}`)
      .then((res) => {
        dispatch(getReRegisterScheduleListSuccess(res.data.data));
      })
      .catch((error) => dispatch(getReRegisterScheduleListFailure(error)));
  };
}

export const FACE_GET_REQUEST = 'FACE_GET_REQUEST';
export const FACE_GET_SUCCESS = 'FACE_GET_SUCCESS';
export const FACE_GET_FAILURE = 'FACE_GET_FAILURE';

const faceGetRequest = createAction(FACE_GET_REQUEST, 'isLoading');
const faceGetSuccess = createAction(FACE_GET_SUCCESS, 'data');
const faceGetFailure = createAction(FACE_GET_FAILURE, 'error');

export const faceGet = (data, callBack) => {
  return (dispatch) => {
    dispatch(faceGetRequest(true));
    return axios
      .post('/digiclass/faceReRegister/faceGet', data)
      .then((res) => {
        dispatch(faceGetSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((errors) => dispatch(faceGetFailure(errors)));
  };
};

export const SET_USER_DATA_SUCCESS = 'SET_USER_DATA_SUCCESS';
const setDataSuccess = createAction(SET_USER_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const RE_REGISTER_STATUS_REQUEST = 'RE_REGISTER_STATUS_REQUEST';
export const RE_REGISTER_STATUS_SUCCESS = 'RE_REGISTER_STATUS_SUCCESS';
export const RE_REGISTER_STATUS_FAILURE = 'RE_REGISTER_STATUS_FAILURE';

const updateReRegisterStatusRequest = createAction(RE_REGISTER_STATUS_REQUEST, 'isLoading');
const updateReRegisterStatusSuccess = createAction(RE_REGISTER_STATUS_SUCCESS, 'data');
const updateReRegisterStatusFailure = createAction(RE_REGISTER_STATUS_FAILURE, 'error');

export const updateReRegisterStatus = (data, callBack) => {
  return (dispatch) => {
    dispatch(updateReRegisterStatusRequest(true));
    return axios
      .post('/digiclass/faceReRegister/faceRegisterApproval', data)
      .then((res) => {
        dispatch(
          updateReRegisterStatusSuccess({
            message: res.data.message,
            status: data.status,
            userId: data.userId,
            reasonToReject: data?.reasonToReject,
          })
        );
        callBack && callBack();
      })
      .catch((errors) => dispatch(updateReRegisterStatusFailure(errors)));
  };
};
