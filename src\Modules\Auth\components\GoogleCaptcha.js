import React from 'react';
import PropTypes from 'prop-types';
import { GoogleReCaptchaProvider, GoogleReCaptchaCheckbox } from '@google-recaptcha/react';
import { Map } from 'immutable';

const GoogleCaptcha = ({ captchaRef, failedAttempts, onCaptchaChange, unifyData }) => {
  const recaptcha = unifyData.get('reCaptcha');
  const RECAPTCHA_SITE_KEY = recaptcha;
  return (
    <GoogleReCaptchaProvider
      siteKey={RECAPTCHA_SITE_KEY}
      useRecaptchaNet
      scriptProps={{ async: true, defer: true }}
      type="v2-checkbox"
      key={`captcha-${failedAttempts}`}
    >
      <div className="mt-3">
        <GoogleReCaptchaCheckbox
          ref={captchaRef}
          onChange={onCaptchaChange}
          className="g-recaptcha"
        />
      </div>
    </GoogleReCaptchaProvider>
  );
};

GoogleCaptcha.propTypes = {
  captchaRef: PropTypes.object,
  failedAttempts: PropTypes.number,
  onCaptchaChange: PropTypes.func,
  unifyData: PropTypes.instanceOf(Map),
};

export default GoogleCaptcha;
