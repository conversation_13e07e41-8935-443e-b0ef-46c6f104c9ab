import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Popover,
  Typography,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import ErrorIcon from '@mui/icons-material/Error';
import CloseIcon from '@mui/icons-material/Close';
import { List, Map } from 'immutable';
import {
  buttonSx,
  getFullName,
  // SecondaryText
} from '../utils';

const dialogSx = {
  '& .MuiDialog-paper': {
    maxWidth: '800px',
  },
};
const titleSx = {
  padding: '12px 16px',
  color: '#374151',
};
const warningInfoSx = {
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  marginTop: '8px',
  padding: '6px 12px',
  color: '#D97706',
  backgroundColor: '#FFFBEB',
  border: '1px solid #FBBF24',
  borderRadius: '8px',
};
const tableBoxSx = {
  marginTop: '16px',
  border: '1px solid #E5E7EB',
  borderRadius: '8px',
};
const paperPropsSx = {
  padding: '16px',
  borderRadius: '16px',
  width: 300,
  backgroundColor: '#F9FAFB',
  maxHeight: 350,
};
const viewLinkSx = {
  marginLeft: '4px',
  fontSize: '14px',
  fontWeight: 500,
  color: '#0064C8',
  textDecoration: 'underline',
  cursor: 'pointer',
};
const moreButtonSx = {
  padding: '2px',
  fontSize: 12,
  fontWeight: 400,
  color: '#0064C8',
};

const getAssignedCounts = ({ studentList, staffList, infraList }) => {
  let assigned = [];

  if (studentList.size) {
    const hasEmptyStudents = studentList.some((s) => !s.get('students', List()).size);
    if (hasEmptyStudents) assigned.push(`(${studentList.size}) groups`);

    const noOfStudents = studentList.reduce((acc, s) => (acc += s.get('students').size), 0);
    if (noOfStudents) assigned.push(`(${noOfStudents}) students`);
  }
  if (staffList.size) assigned.push(`(${staffList.size}) staffs`);
  if (infraList.size) assigned.push(`(${infraList.size}) infrastructures`);

  if (assigned.length === 1) return { assignedCounts: assigned[0] };

  const last = assigned.pop();
  return { assignedCounts: `${assigned.join(', ')} & ${last}` };
};

const StudentPopover = ({ anchorEl, studentList, handleClose }) => {
  const [showAll, setShowAll] = useState(false);
  const open = Boolean(anchorEl);
  const id = open ? 'student-popover' : undefined;

  useEffect(() => {
    if (!open) setShowAll(false);
  }, [open]);

  const studentsToShow = useMemo(() => {
    return showAll ? studentList : studentList.slice(0, 4);
  }, [studentList, showAll]);

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      slotProps={{ paper: { sx: paperPropsSx } }}
    >
      <div className="d-flex align-items-center justify-content-between mb-1">
        <Typography variant="body2" color="#6B7280">
          {studentList.size} Students
        </Typography>
        <IconButton sx={{ p: '2px', color: '#4B5563' }} onClick={handleClose}>
          <CloseIcon />
        </IconButton>
      </div>

      {studentsToShow.map((student, index) => (
        <div key={index} className="mb-1">
          <Typography variant="body2" color="#374151">
            {getFullName(student)}
          </Typography>
          <Typography className="text-capitalize" fontSize={12} color="#9CA3AF">
            {student.get('user_id', '')} • {student.get('gender', '')} •{' '}
            {student.get('groupName', '')}
          </Typography>
        </div>
      ))}

      {!showAll && studentList.size > 4 && (
        <MButton variant="text" sx={moreButtonSx} clicked={() => setShowAll(!showAll)}>
          +{studentList.size - 4} more
        </MButton>
      )}
    </Popover>
  );
};
StudentPopover.propTypes = {
  anchorEl: PropTypes.object,
  studentList: PropTypes.instanceOf(List),
  handleClose: PropTypes.func,
};

const ScheduleIssuesModal = ({ open, conflicts, handleConfirm, handleClose }) => {
  const [studentPopperData, setStudentPopperData] = useState(Map());
  const studentList = conflicts.get('studentList', List());
  const staffList = conflicts.get('staffList', List());
  const infraList = conflicts.get('infraList', List());

  const { assignedCounts } = useMemo(() => {
    return getAssignedCounts({ studentList, staffList, infraList });
  }, [studentList, staffList, infraList]);

  const handleClickView = (e, students) => {
    setStudentPopperData(Map({ anchorEl: e.currentTarget, students }));
  };

  const handleCloseView = () => setStudentPopperData(Map());

  return (
    <Dialog open={open} sx={dialogSx} fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        Scheduling Issues Detected!
      </DialogTitle>
      <DialogContent className="p-3">
        <Box sx={warningInfoSx}>
          <ErrorIcon />
          <Typography>{assignedCounts} are already assigned to other sessions!</Typography>
        </Box>

        {!studentList.isEmpty() && (
          <Box sx={tableBoxSx}>
            <div className="table-responsive border-radious-8">
              <table className="table schedule-issues-table">
                <thead>
                  <tr>
                    <th width="37.5%">Student Details</th>
                    <th width="62.5%">Scheduled To </th>
                    {/* <th width="25%">Scheduled At</th> */}
                  </tr>
                </thead>
                <tbody>
                  {studentList.map((group, index) => {
                    const students = group.get('students', List());
                    return (
                      <tr key={index}>
                        <td>
                          {group.get('name', '')}
                          {!students.isEmpty() && (
                            <Typography
                              component="span"
                              sx={viewLinkSx}
                              onClick={(e) => handleClickView(e, students)}
                            >
                              View
                            </Typography>
                          )}
                        </td>
                        <td>
                          {group.get('errorInfo', '')}
                          {/* <p>PBL1 - Child Health Nursing</p>
                    <SecondaryText text="Course name 1 • Year 1 • Level 1" /> */}
                        </td>
                        {/* <td>
                    <p>9 AM - 10 AM</p>
                    <SecondaryText text="Lecture" />
                  </td> */}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Box>
        )}

        {!staffList.isEmpty() && (
          <Box sx={tableBoxSx}>
            <div className="table-responsive border-radious-8">
              <table className="table schedule-issues-table">
                <thead>
                  <tr>
                    <th width="37.5%">Staff Details</th>
                    <th width="62.5%">Scheduled To </th>
                    {/* <th width="25%">Scheduled At</th> */}
                  </tr>
                </thead>
                <tbody>
                  {staffList.map((staff, index) => (
                    <tr key={index}>
                      <td>
                        <p>{staff.get('name', '')}</p>
                        <Typography className="text-capitalize" fontSize={12} color="#9CA3AF">
                          {staff.get('userId', '')} • {staff.get('gender', '')}
                        </Typography>
                      </td>
                      <td>
                        {staff.get('errorInfo', '')}
                        {/* <p>PBL1 - Child Health Nursing</p>
                    <SecondaryText text="Course name 1 • Year 1 • Level 1" /> */}
                      </td>
                      {/* <td>
                    <p>9 AM - 10 AM</p>
                    <SecondaryText text="Lecture" />
                  </td> */}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Box>
        )}

        {!infraList.isEmpty() && (
          <Box sx={tableBoxSx}>
            <div className="table-responsive border-radious-8">
              <table className="table schedule-issues-table">
                <thead>
                  <tr>
                    <th width="37.5%">Infrastructure Details</th>
                    <th width="62.5%">Scheduled To </th>
                    {/* <th width="25%">Scheduled At</th> */}
                  </tr>
                </thead>
                <tbody>
                  {infraList.map((infra, index) => (
                    <tr key={index}>
                      <td>{infra.get('name', '')}</td>
                      <td>
                        {infra.get('errorInfo', '')}
                        {/* <p>Course name 1</p>
                    <SecondaryText text="Year 1 • Level 1" /> */}
                      </td>
                      {/* <td>
                    <p>9 AM - 10 AM</p>
                    <SecondaryText text="Lecture" />
                  </td> */}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Box>
        )}

        <Typography mt={2} fontWeight={500} color="#374151">
          Proceeding will assign the existing schedules to this session as well.
        </Typography>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
          Back
        </MButton>
        <MButton
          variant="contained"
          color="primary"
          clicked={handleConfirm}
          sx={{ ...buttonSx, minWidth: 160 }}
        >
          Schedule Anyway
        </MButton>
      </DialogActions>

      <StudentPopover
        anchorEl={studentPopperData.get('anchorEl', null)}
        studentList={studentPopperData.get('students', List())}
        handleClose={handleCloseView}
      />
    </Dialog>
  );
};
ScheduleIssuesModal.propTypes = {
  open: PropTypes.bool,
  conflicts: PropTypes.instanceOf(Map),
  handleConfirm: PropTypes.func,
  handleClose: PropTypes.func,
};

export default ScheduleIssuesModal;
