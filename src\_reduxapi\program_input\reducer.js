import { fromJS, Map, List } from 'immutable';
import * as actions from './action';
import { jsUcfirst, jsUcfirstAll } from '../../utils';
import { t } from 'i18next';
import LocalStorageService from 'LocalStorageService';

const initialState = fromJS({
  isLoading: false,
  message: '',
  addStudentStatus: false,
  publishStatus: false,
  collegeList: [],
  programList: [],
  program: {},
  programError: {},
  programDeptList: [],
  departmentList: [],
  isDepartmentsLoading: false,
  showAddDepartmentModal: false,
  showAddSubjectModal: false,
  sessionTypeList: [],
  showAddSessionDeliveryTypeModal: false,
  curriculum: {},
  editedCurriculum: {},
  course: {},
  editedCourse: {},
  curriculumList: [],
  preRequisiteList: [],
  frameworkLists: [],
  impactLists: [],
  impactMapList: [],
  contentMapList: [],
  showImpactModal: false,
  editedImpact: {},
  activeStep: 0,
  sessionFlow: {},
  editedSessionFlow: [],
  editedAdditionalSessionFlow: [],
  courseList: [],
  assignedCourse: {},
  assignedCourseList: [],
  groupedCourseList: [],
  ungroupedCourseList: [],
  programCurriculumList: [],
  courseRecurringLevelList: [],
  selectedCoursesToGroup: {},
  programInputList: [],
  mappingMatrix: [],
  frameworkDashboard: {},
  frameworkDashboardList: [],
  mappingTree: [],
  courseListById: [],
  deliveringSubject: [],
  frameworkStandardSettings: [],
  frameworkProgramSettings: [],
  frameworkMappingGraph: {},
  curriculumsWithFramework: [],
  cloSloMappingGraph: [],
  isCourseMasterListLoading: false,
  isFrameworksDashboardLoading: false,
  isMappingGraphLoading: false,
  masterGraphData: {
    activeCurriculum: {},
    activeViewType: 'domain',
    activeSettingType: 'standard',
    activeDomain: {},
    activeYear: {},
    activeColor: '#FF0000',
    activeCurriculumFramework: {},
  },
  validSLODataList: [],
  InvalidSLODataList: [],
  configureSettings: {},
  courseSessionOrderModules: [],
  versionCourseDetail: {},
  versionCourseId: '',
  getSignedUrl: '',
});

//eslint-disable-next-line
export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_ADD_STUDENT_ACTIVE: {
      return state.set('addStudentStatus', action.status);
    }
    case actions.POST_COLLEGE_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.college_added_successfully'));
    }
    case actions.POST_COLLEGE_FAILURE: {
      return state.set('isLoading', false).set('message', t('error'));
    }
    case actions.POST_COLLEGE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_PROGRAM_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.UPDATE_PROGRAM_FAILURE: {
      return state.set('isLoading', false).set('message', action.error.response.data.message);
    }
    case actions.UPDATE_PROGRAM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_COLLEGE_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_COLLEGE_LIST_SUCCESS: {
      return state.set('isLoading', false).set('collegeList', fromJS(action.data));
    }
    case actions.GET_COLLEGE_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.DELETE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('delete', fromJS(action.data))
        .set(
          'message',
          action.message === 'Framework'
            ? t('program_input.response_strings.framework_deleted_successfully')
            : action.message === 'Impact Mapping'
            ? t('messages.impact_deleted')
            : action.message === 'Content Mapping'
            ? t('messages.content_deleted')
            : action.message === 'Program'
            ? t('messages.delete_program')
            : action.message === 'Pre-Requisite'
            ? t('messages.delete_pre_requisite')
            : t('messages.curriculum_deleted')
        );
    }
    case actions.DELETE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.POST_PROGRAM_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.POST_PROGRAM_FAILURE: {
      return state.set('isLoading', false).set('message', action.error.response.data.message);
    }
    case actions.POST_PROGRAM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_UPDATE_PLO_CLO_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_UPDATE_PLO_CLO_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.POST_UPDATE_PLO_CLO_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.GET_PROGRAM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('programList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_PROGRAM_BY_ID_REQUEST: {
      return state.merge(
        Map({
          isLoading: true,
          program: Map(),
          programError: Map(),
          departmentList: List(),
          sessionTypeList: List(),
        })
      );
    }
    case actions.GET_PROGRAM_BY_ID_SUCCESS: {
      return state.set('isLoading', false).set('program', fromJS(action.data));
    }
    case actions.GET_PROGRAM_BY_ID_FAILURE: {
      return state
        .set('isLoading', false)
        .set('programError', action.error)
        .set('message', t('error_program'));
    }

    case actions.GET_PROGRAM_DEPT_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_DEPT_LIST_SUCCESS: {
      return state.set('isLoading', false).set('programDeptList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_DEPT_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_DEPARTMENT_LIST_REQUEST: {
      return state.set('isDepartmentsLoading', true);
    }
    case actions.GET_DEPARTMENT_LIST_SUCCESS: {
      return state.set('isDepartmentsLoading', false).set('departmentList', fromJS(action.data));
    }
    case actions.GET_DEPARTMENT_LIST_FAILURE: {
      return state.set('isDepartmentsLoading', false).set('error', action.error);
    }

    case actions.ADD_DEPARTMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_DEPARTMENT_SUCCESS: {
      let message = action.data.operation === 'update' ? 'edite' : action.data.operation;
      return state
        .set('isLoading', false)
        .set('showAddDepartmentModal', false)
        .set(
          'message',
          jsUcfirst(message) === 'Create'
            ? t('program_input.response_strings.department_created_successfully')
            : jsUcfirst(message) === 'Edite'
            ? t('program_input.response_strings.department_edited_successfully')
            : t('program_input.response_strings.department_deleted_successfully')
        );
    }
    case actions.ADD_DEPARTMENT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.ADD_SUBJECT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_SUBJECT_SUCCESS: {
      let message = action.data.operation === 'update' ? 'edite' : action.data.operation;
      return state
        .set('isLoading', false)
        .set('showAddSubjectModal', false)
        .set(
          'message',
          jsUcfirst(message) === 'Create'
            ? t('program_input.response_strings.subject_created_successfully')
            : jsUcfirst(message) === 'Edite'
            ? t('program_input.response_strings.subject_edited_successfully')
            : t('program_input.response_strings.subject_deleted_successfully')
        );
    }
    case actions.ADD_SUBJECT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.SHARE_DEPARTMENT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SHARE_DEPARTMENT_SUCCESS: {
      // return state.set('isLoading', false).set('message', action.message);
      // let msg = 'Department Shared/Removed Successfully.';
      return state
        .set('isLoading', false)
        .set(
          'message',
          action.msg === 'department_share'
            ? t('messages.department_shared_successful')
            : action.msg === 'department_remove'
            ? t('messages.department_un_shared_successful')
            : t('updated_successfully')
        );
    }
    case actions.SHARE_DEPARTMENT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.SHARE_SUBJECT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SHARE_SUBJECT_SUCCESS: {
      return state
        .set('isLoading', false)
        .set(
          'message',
          action.message === 'subject_remove'
            ? t('program_input.response_strings.subject_un_shared_successfully')
            : action.message === 'subject_share'
            ? t('program_input.response_strings.subject_shared_successfully')
            : t('updated_successfully')
        );
    }
    case actions.SHARE_SUBJECT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.GET_SESSION_TYPE_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SESSION_TYPE_LIST_SUCCESS: {
      return state.set('isLoading', false).set('sessionTypeList', fromJS(action.data));
    }
    case actions.GET_SESSION_TYPE_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.ADD_SESSION_DELIVERY_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_SESSION_DELIVERY_TYPE_SUCCESS: {
      let message = action.data.operation === 'update' ? 'edit' : action.data.operation;
      return state
        .set('isLoading', false)
        .set('showAddSessionDeliveryTypeModal', false)
        .set(
          'message',
          action.data.type === 'Session'
            ? jsUcfirst(message) === 'Create'
              ? t('program_input.response_strings.session_type_created_successfully')
              : jsUcfirst(message) === 'Edit'
              ? t('program_input.response_strings.session_type_edited_successfully')
              : t('program_input.response_strings.session_type_deleted_successfully')
            : action.data.type === 'Delivery'
            ? jsUcfirst(message) === 'Create'
              ? t('program_input.response_strings.delivery_type_created_successfully')
              : jsUcfirst(message) === 'Edit'
              ? t('program_input.response_strings.delivery_type_edited_successfully')
              : t('program_input.response_strings.delivery_type_deleted_successfully')
            : ''
        );
    }
    case actions.ADD_SESSION_DELIVERY_TYPE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.SAVE_CURRICULUM_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.SAVE_CURRICULUM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${jsUcfirstAll(response.data.message)}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.SAVE_CURRICULUM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_CURRICULUM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('curriculumList', fromJS(action.data));
    }
    case actions.GET_CURRICULUM_LIST_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.GET_CURRICULUM_LIST_REQUEST: {
      return state.set('isLoading', true).set('curriculumList', fromJS([]));
    }
    case actions.ARCHIVE_CURRICULUM_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.ARCHIVE_CURRICULUM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.ARCHIVE_CURRICULUM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_CURRICULUM_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.DELETE_CURRICULUM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.DELETE_CURRICULUM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PREREQUISITE_LIST_SUCCESS: {
      return state.set('isLoading', false).set('preRequisiteList', fromJS(action.data));
    }
    case actions.GET_PREREQUISITE_LIST_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.GET_PREREQUISITE_LIST_REQUEST: {
      return state.set('isLoading', true).set('preRequisiteList', fromJS([]));
    }
    case actions.GET_CURRICULUM_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_CURRICULUM_SUCCESS: {
      return state.set('isLoading', false).set('curriculum', fromJS(action.data));
    }
    case actions.GET_CURRICULUM_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_FRAMEWORK_LIST_REQUEST: {
      if (action.id !== null) {
        return state.set('isLoading', true);
      } else {
        return state.set('isLoading', true).set('frameworkLists', fromJS([]));
      }
    }
    case actions.GET_FRAMEWORK_LIST_SUCCESS: {
      return state.set('isLoading', false).set('frameworkLists', fromJS(action.data));
    }
    case actions.GET_FRAMEWORK_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_IMPACT_LIST_REQUEST: {
      return state.merge(
        Map({
          ...(action.showLoading && { isLoading: true }),
          impactLists: List(),
        })
      );
    }
    case actions.GET_IMPACT_LIST_SUCCESS: {
      return state.merge(
        Map({
          ...(action.data.showLoading && { isLoading: false }),
          impactLists: fromJS(action.data.data),
        })
      );
    }
    case actions.GET_IMPACT_LIST_FAILURE: {
      return state.merge(
        Map({
          ...(action.error.showLoading && { isLoading: false }),
          error: action.error.error,
        })
      );
    }

    case actions.POST_FRAMEWORK_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_FRAMEWORK_SUCCESS: {
      return state
        .set('isLoading', false)
        .set(
          'message',
          action.data === null
            ? t('program_input.response_strings.framework_added_successfully')
            : t('program_input.response_strings.framework_edited_successfully')
        );
    }
    case actions.POST_FRAMEWORK_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.set('isLoading', false).set('message', jsUcfirst(errorMessage));
    }

    case actions.POST_DOMAIN_SUCCESS: {
      return state
        .set('isLoading', false)
        .set(
          'message',
          `${
            jsUcfirst(action.data) === 'Added'
              ? t('program_input.response_strings.domain_added_successfully')
              : jsUcfirst(action.data) === 'Edited'
              ? t('program_input.response_strings.domain_edited_successfully')
              : t('program_input.response_strings.domain_deleted_successfully')
          }`
        );
    }
    case actions.POST_DOMAIN_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.set('isLoading', false).set('message', jsUcfirst(errorMessage));
    }

    case actions.SET_SESSION_TYPES_IN_COURSE: {
      return state.setIn(['editedCourse', 'credit_hours'], fromJS(action.data));
    }
    case actions.GET_COURSE_LIST_REQUEST: {
      return state.set('isLoading', true).set('courseList', fromJS([]));
    }
    case actions.GET_COURSE_LIST_SUCCESS: {
      return state.set('isLoading', false).set('courseList', fromJS(action.data));
    }
    case actions.GET_COURSE_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_COURSE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_COURSE_SUCCESS: {
      return state.set('isLoading', false).set('course', fromJS(action.data));
    }
    case actions.GET_COURSE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('program_input.error_strings.error_fetching_course_details')
        );
    }
    case actions.SAVE_COURSE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_COURSE_SUCCESS: {
      let message =
        action.data.operation === 'update'
          ? 'edite'
          : action.data.operation === 'delete'
          ? 'delete'
          : action.data.operation;
      if (action.data.isActive !== undefined && !action.data.isActive) {
        message = 'drafte';
      }
      return state
        .set('isLoading', false)
        .set(
          'message',
          message === 'edite'
            ? t('program_input.response_strings.course_edited_successfully')
            : message === 'delete'
            ? t('program_input.response_strings.course_deleted_successfully')
            : message === 'create'
            ? t('program_input.response_strings.course_created_successfully')
            : t('program_input.response_strings.course_drafted_successfully')
        )
        .set('versionCourseId', action.data._id);
    }
    case actions.SAVE_COURSE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.GET_SESSION_FLOW_REQUEST: {
      return state.merge(
        Map({
          isLoading: true,
          ...(action.data.clearDataOnFetch && { sessionFlow: Map() }),
        })
      );
    }
    case actions.GET_SESSION_FLOW_SUCCESS: {
      return state.set('isLoading', false).set('sessionFlow', fromJS(action.data));
    }
    case actions.GET_SESSION_FLOW_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('program_input.error_strings.error_fetching_session_flow_details')
        );
    }
    case actions.SAVE_SESSION_FLOW_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_SESSION_FLOW_SUCCESS: {
      let message = action.data.operation === 'update' ? 'edite' : action.data.operation;
      if (action.data.isActive !== undefined && !action.data.isActive) {
        message = 'drafte';
      }
      return state
        .set('isLoading', false)
        .set(
          'message',
          action.data.data.edit_status === 1
            ? action.data.assign !== undefined && action.data.assign && message === 'edite'
              ? t('program_input.response_strings.sessionFlow.edite')
              : action.data.assign !== undefined && action.data.assign && message === 'delete'
              ? t('program_input.response_strings.sessionFlow.delete')
              : t(`program_input.response_strings.sessionFlow.${message}`)
            : action.data.message
        );
    }
    case actions.SAVE_SESSION_FLOW_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.POST_DOMAIN_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_CURRICULUM_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_CURRICULUM_LIST_SUCCESS: {
      return state.set('isLoading', false).set('programCurriculumList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_CURRICULUM_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_ASSIGNED_COURSE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_ASSIGNED_COURSE_SUCCESS: {
      return state.set('isLoading', false).set('assignedCourse', fromJS(action.data));
    }
    case actions.GET_ASSIGNED_COURSE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.ASSIGN_COURSE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ASSIGN_COURSE_SUCCESS: {
      let message =
        action.data.operation === 'create'
          ? t('program_input.response_strings.course_assigned_successfully')
          : action.data.operation === 'update'
          ? t('program_input.response_strings.assignedCourse_edited_successfully')
          : t('program_input.response_strings.assignedCourse_removed_successfully');
      if (action.data.operation === 'create') {
        LocalStorageService.setCustomToken('activeStep', 1);
      }
      return state.set('isLoading', false).set('message', message).set('activeStep', 1);
    }
    case actions.ASSIGN_COURSE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.GET_ASSIGNED_COURSE_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_ASSIGNED_COURSE_LIST_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('assignedCourseList', fromJS(action.data.assigned_course_data))
        .set('groupedCourseList', fromJS(action.data.grouped_data))
        .set('ungroupedCourseList', fromJS(action.data.ungrouped_data));
    }
    case actions.GET_ASSIGNED_COURSE_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_COURSE_RECURRING_LEVEL_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_COURSE_RECURRING_LEVEL_LIST_SUCCESS: {
      return state.set('isLoading', false).set('courseRecurringLevelList', fromJS(action.data));
    }
    case actions.GET_COURSE_RECURRING_LEVEL_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.SAVE_COURSE_GROUP_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_COURSE_GROUP_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', `Courses ${jsUcfirst(action.data.operation)}ed Successfully.`);
    }
    case actions.SAVE_COURSE_GROUP_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.GET_PROGRAM_INPUT_LIST_REQUEST: {
      return state.set('isLoading', true).set('programInputList', fromJS([]));
    }
    case actions.GET_PROGRAM_INPUT_LIST_SUCCESS: {
      return state.set('isLoading', false).set('programInputList', fromJS(action.data));
    }
    case actions.GET_PROGRAM_INPUT_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.POST_IMPACT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_IMPACT_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('editedImpact', Map())
        .set('showImpactModal', false)
        .set('message', action.data);
    }
    case actions.POST_IMPACT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.message}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.SAVE_PLO_CLO_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_PLO_CLO_SUCCESS: {
      const { label, operation, type } = action.data;
      return state.set('isLoading', false).set(
        'message',
        `${
          type === 'plo'
            ? jsUcfirst(operation) === 'Add'
              ? t('program_input.response_strings.plo_added_successfully', {
                  PLO: label.ploLabel,
                })
              : jsUcfirst(operation) === 'Edit'
              ? t('program_input.response_strings.plo_edited_successfully', {
                  PLO: label.ploLabel,
                })
              : t('program_input.response_strings.plo_deleted_successfully', {
                  PLO: label.ploLabel,
                })
            : type === 'clo'
            ? jsUcfirst(operation) === 'Add'
              ? t('program_input.response_strings.clo_added_successfully', {
                  CLO: label.cloLabel,
                })
              : jsUcfirst(action.data.operation) === 'Edit'
              ? t('program_input.response_strings.clo_edited_successfully', {
                  CLO: label.cloLabel,
                })
              : t('program_input.response_strings.clo_deleted_successfully', {
                  CLO: label.cloLabel,
                })
            : 'action not found'
        } `
      );
    }
    case actions.SAVE_PLO_CLO_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.SAVE_SLO_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_SLO_SUCCESS: {
      const { label, operation } = action.data;
      return state.set('isLoading', false).set(
        'message',
        `${
          jsUcfirst(operation) === 'Add'
            ? t('program_input.response_strings.slo_added_successfully', {
                SLO: label.sloLabel,
              })
            : jsUcfirst(operation) === 'Edit'
            ? t('program_input.response_strings.slo_edited_successfully', {
                SLO: label.sloLabel,
              })
            : t('program_input.response_strings.slo_deleted_successfully', {
                SLO: label.sloLabel,
              })
        }`
      );
    }
    case actions.SAVE_SLO_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.GET_FRAMEWORK_DASHBOARD_LIST_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FRAMEWORK_DASHBOARD_LIST_SUCCESS: {
      return state.set('isLoading', false).set('frameworkDashboardList', fromJS(action.data));
    }
    case actions.GET_FRAMEWORK_DASHBOARD_LIST_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_REQUEST: {
      return state.set('isFrameworksDashboardLoading', true);
    }
    case actions.GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_SUCCESS: {
      return state
        .set('isFrameworksDashboardLoading', false)
        .set('frameworkDashboard', fromJS(action.data));
    }
    case actions.GET_FRAMEWORK_DASHBOARD_DATA_BY_PROGRAM_ID_FAILURE: {
      return state.set('isFrameworksDashboardLoading', false).set('error', action.error);
    }
    case actions.ADD_REMOVE_FRAMEWORK_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.ADD_REMOVE_FRAMEWORK_SUCCESS: {
      return state
        .set('isLoading', false)
        .set(
          'message',
          action.data.operation === 'add'
            ? t('program_input.response_strings.framework_selected_successfully')
            : t('program_input.response_strings.framework_deselected_successfully')
        );
    }
    case actions.ADD_REMOVE_FRAMEWORK_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.UPDATE_MAP_TYPE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPDATE_MAP_TYPE_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data.message);
    }
    case actions.UPDATE_MAP_TYPE_FAILURE: {
      const { response = {} } = action.error;
      const message =
        response && typeof response.data.message === 'string'
          ? response.data.message
          : t('infra_management.remote.error_msgs.an_error_occured_try_again');
      return state.set('isLoading', false).set('message', message);
    }
    case actions.GET_MAPPING_TREE_REQUEST: {
      return state.set('isLoading', true).set('mappingTree', List());
    }
    case actions.GET_MAPPING_TREE_SUCCESS: {
      return state.set('isLoading', false).set('mappingTree', fromJS(action.data));
    }
    case actions.GET_MAPPING_TREE_FAILURE: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.error_strings.error_fetching_programs'));
    }
    case actions.GET_MAP_CLO_PLO_REQUEST: {
      return state.set('isLoading', true).set('mappingMatrix', List());
    }
    case actions.GET_MAP_CLO_PLO_SUCCESS: {
      return state.set('isLoading', false).set('mappingMatrix', fromJS(action.data));
    }
    case actions.GET_MAP_CLO_PLO_FAILURE: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.error_strings.error_fetching_mapping_data'));
    }
    case actions.GET_MAPPING_MATRIX_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_MAPPING_MATRIX_SUCCESS: {
      return state.set('isLoading', false).set('mappingMatrix', fromJS(action.data));
    }
    case actions.GET_MAPPING_MATRIX_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.SAVE_CONTENT_MAP_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_CONTENT_MAP_SUCCESS: {
      return state.set('isLoading', false);
    }
    case actions.SAVE_CONTENT_MAP_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.GET_COURSE_LIST_BY_CURRICULUM_REQUEST: {
      return state
        .set('isCourseMasterListLoading', true)
        .set('courseListById', fromJS([]))
        .set('sessionFlow', fromJS({}));
    }
    case actions.GET_COURSE_LIST_BY_CURRICULUM_SUCCESS: {
      return state
        .set('isCourseMasterListLoading', false)
        .set('courseListById', fromJS(action.data));
    }
    case actions.GET_COURSE_LIST_BY_CURRICULUM_FAILURE: {
      return state.set('isCourseMasterListLoading', false).set('error', action.error);
    }
    case actions.GET_DELIVERING_SUBJECT_REQUEST: {
      return state.set('isLoading', true).set('deliveringSubject', fromJS([]));
    }
    case actions.GET_DELIVERING_SUBJECT_SUCCESS: {
      return state.set('isLoading', false).set('deliveringSubject', fromJS(action.data));
    }
    case actions.GET_DELIVERING_SUBJECT_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.GET_FRAMEWORK_STANDARD_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FRAMEWORK_STANDARD_SETTINGS_SUCCESS: {
      return state.set('isLoading', false).set('frameworkStandardSettings', fromJS(action.data));
    }
    case actions.GET_FRAMEWORK_STANDARD_SETTINGS_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.SAVE_FRAMEWORK_STANDARD_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_FRAMEWORK_STANDARD_SETTINGS_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.settings_saved_successfully'));
    }
    case actions.SAVE_FRAMEWORK_STANDARD_SETTINGS_FAILURE: {
      const { response = {} } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          `${
            (response.data && response.data.error) ||
            t('infra_management.remote.error_msgs.an_error_occured_try_again')
          }`
        );
    }

    case actions.GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_SUCCESS: {
      return state.set('isLoading', false).set('frameworkProgramSettings', fromJS(action.data));
    }
    case actions.GET_FRAMEWORK_SETTINGS_BY_PROGRAM_ID_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.GET_FRAMEWORK_MAPPING_GRAPH_REQUEST: {
      return state.set('isMappingGraphLoading', true);
    }
    case actions.GET_FRAMEWORK_MAPPING_GRAPH_SUCCESS: {
      return state
        .set('isMappingGraphLoading', false)
        .set('frameworkMappingGraph', fromJS(action.data));
    }
    case actions.GET_FRAMEWORK_MAPPING_GRAPH_FAILURE: {
      const { response = {} } = action.error;
      return state
        .set('isMappingGraphLoading', false)
        .set(
          'message',
          `${
            (response.data && response.data.message) ||
            t('infra_management.remote.error_msgs.an_error_occured_try_again')
          }`
        );
    }
    case actions.GET_CURRICULUM_WITHOUT_FRAMEWORK_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_CURRICULUM_WITHOUT_FRAMEWORK_SUCCESS: {
      return state
        .set('isLoading', false)
        .set(
          'curriculumsWithFramework',
          state.get('curriculumsWithFramework', List()).push(fromJS(action.data))
        );
    }
    case actions.GET_CURRICULUM_WITHOUT_FRAMEWORK_FAILURE: {
      const { response = {} } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          `${
            (response.data && response.data.message) ||
            t('infra_management.remote.error_msgs.an_error_occured_try_again')
          }`
        );
    }
    case actions.GET_CLO_SLO_MAPPING_GRAPH_REQUEST: {
      return state.set('isLoading', true).set('cloSloMappingGraph', List());
    }
    case actions.GET_CLO_SLO_MAPPING_GRAPH_SUCCESS: {
      return state.set('isLoading', false).set('cloSloMappingGraph', fromJS(action.data));
    }
    case actions.GET_CLO_SLO_MAPPING_GRAPH_FAILURE: {
      const { response = {} } = action.error;
      return state
        .set('isLoading', false)
        .set(
          'message',
          `${
            (response.data && response.data.message) ||
            t('infra_management.remote.error_msgs.an_error_occured_try_again')
          }`
        );
    }
    case actions.IMPORT_PROGRAM_REQUEST: {
      return state.set('isLoading', true).set('InvalidDataList', fromJS(action.data));
    }
    case actions.IMPORT_PROGRAM_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('validDataList', fromJS(action.data.valid_data))
        .set('InvalidDataList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_PROGRAM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_COURSE_REQUEST: {
      return state.set('isLoading', true).set('InvalidDataList', fromJS(action.data));
    }
    case actions.IMPORT_COURSE_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('validCourseDataList', fromJS(action.data.valid_data))
        .set('InvalidCourseDataList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_COURSE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidCourseDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_SESSION_REQUEST: {
      return state.set('isLoading', true).set('InvalidSessionDataList', fromJS(action.data));
    }
    case actions.IMPORT_SESSION_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('validSessionDataList', fromJS(action.data.valid_data))
        .set('InvalidSessionDataList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_SESSION_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidSessionDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_DEPT_REQUEST: {
      return state.set('isLoading', true).set('InvalidDeptDataList', fromJS(action.data));
    }
    case actions.IMPORT_DEPT_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('validDeptDataList', fromJS(action.data.valid_data))
        .set('InvalidDeptDataList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_DEPT_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidDeptDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_SESSION_TYPE_REQUEST: {
      return state.set('isLoading', true).set('InvalidSessionList', fromJS(action.data));
    }
    case actions.IMPORT_SESSION_TYPE_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('ValidSessionList', fromJS(action.data.valid_data))
        .set('InvalidSessionList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_SESSION_TYPE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidSessionList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_DELIVERY_TYPE_REQUEST: {
      return state.set('isLoading', true).set('InvalidDeliveryList', fromJS(action.data));
    }
    case actions.IMPORT_DELIVERY_TYPE_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('ValidDeliveryList', fromJS(action.data.valid_data))
        .set('InvalidDeliveryList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_DELIVERY_TYPE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidDeliveryList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_CURRICULUM_REQUEST: {
      return state.set('isLoading', true).set('InvalidCurricullumList', fromJS(action.data));
    }
    case actions.IMPORT_CURRICULUM_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('ValidCurricullumList', fromJS(action.data.valid_data))
        .set('InvalidCurricullumList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_CURRICULUM_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidDeliveryList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidDataList', fromJS(action.data));
    }
    case actions.IMPORT_RECORD_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.program_imported_successfully'))
        .set('validDataList', fromJS(action.data.valid_data))
        .set('InvalidDataList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_COURSE_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidDataList', fromJS(action.data));
    }
    case actions.IMPORT_COURSE_RECORD_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.course_imported_successfully'));
    }
    case actions.IMPORT_COURSE_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidCourseDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_SESSION_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidSessionDataList', fromJS(action.data));
    }
    case actions.IMPORT_SESSION_RECORD_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.session_imported_successfully'));
    }
    case actions.IMPORT_SESSION_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidSessionDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_SESSION_TYPE_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidSessionDataList', fromJS(action.data));
    }
    case actions.IMPORT_SESSION_TYPE_RECORD_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.session_type_imported_successfully'));
    }
    case actions.IMPORT_SESSION_TYPE_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidSessionList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_DELIVERY_TYPE_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidDeliveryList', fromJS(action.data));
    }
    case actions.IMPORT_DELIVERY_TYPE_RECORD_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.delivery_type_imported_successfully'));
    }
    case actions.IMPORT_DELIVERY_TYPE_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidDeliveryList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_DEPT_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidDeptDataList', fromJS(action.data));
    }
    case actions.IMPORT_DEPT_RECORD_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.department_imported_successfully'));
    }
    case actions.IMPORT_DEPT_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidDeptDataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.IMPORT_CURRICULUM_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidCurricullumList', fromJS(action.data));
    }
    case actions.IMPORT_CURRICULUM_RECORD_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', t('program_input.response_strings.curriculum_imported_successfully'));
    }
    case actions.IMPORT_CURRICULUM_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidCurricullumList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.IMPORT_SLO_REQUEST: {
      return state.set('isLoading', true).set('InvalidSLODataList', fromJS(action.data));
    }
    case actions.IMPORT_SLO_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('validSLODataList', fromJS(action.data.valid_data))
        .set('InvalidSLODataList', fromJS(action.data.invalid_data));
    }
    case actions.IMPORT_SLO_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidSLODataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }

    case actions.IMPORT_SLO_RECORD_REQUEST: {
      return state.set('isLoading', true).set('InvalidSLODataList', fromJS(action.data));
    }
    case actions.IMPORT_SLO_RECORD_SUCCESS: {
      return state.set('isLoading', false).set(
        'message',
        t('program_input.response_strings.slo_imported_successfully', {
          SLO: 'SLO',
        })
      );
    }
    case actions.IMPORT_SLO_RECORD_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('InvalidSLODataList', fromJS(action.data))
        .set(
          'message',
          response
            ? `${response.data.data}`
            : t('infra_management.remote.error_msgs.an_error_occured_try_again')
        );
    }
    case actions.GET_CONFIGURE_SETTING_REQUEST: {
      return state.set('isLoading', true).set('configureSettings', fromJS({}));
    }
    case actions.GET_CONFIGURE_SETTING_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('configureSettings', fromJS(action.data !== null ? action.data : {}));
    }
    case actions.GET_CONFIGURE_SETTING_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.SAVE_CONFIGURE_SETTINGS_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_CONFIGURE_SETTINGS_SUCCESS: {
      const { data, message } = action.data;
      return state
        .set('isLoading', false)
        .set('configureSettings', fromJS(data))
        .set('message', message);
    }
    case actions.SAVE_CONFIGURE_SETTINGS_FAILURE: {
      const message = action?.error?.response?.data?.data[0];
      return state
        .set('isLoading', false)
        .set(
          'message',
          message[0]
            .replace('description', 'Criteria for Scoring Descriptions')
            .replace('content', 'Questions')
        );
    }
    case actions.POST_BENCHMARK_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_BENCHMARK_SUCCESS: {
      const { data, message } = action.data;
      const status =
        message === 'saved' ? t('settings.response_msg.saved') : t('settings.response_msg.drafted');
      return state
        .set('isLoading', false)
        .set('configureSettings', fromJS(data))
        .set(
          'message',
          `${t('settings.response_msg.benchmark_settings')} ${status} ${t(
            'settings.response_msg.successfully'
          )}`
        );
    }
    case actions.POST_BENCHMARK_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.POST_COURSE_SETTING_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.POST_COURSE_SETTING_SUCCESS: {
      const { data } = action.data;
      return state.set('isLoading', false).set('configureSettings', fromJS(data));
      // .set('message', `Benchmark Settings ${status} Successfully`);
    }
    case actions.POST_COURSE_SETTING_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_SESSION_ORDER_MODULE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_SESSION_ORDER_MODULE_SUCCESS: {
      return state.set('isLoading', false).set('courseSessionOrderModules', fromJS(action.data));
    }
    case actions.GET_SESSION_ORDER_MODULE_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.SAVE_SESSION_ORDER_MODULE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.SAVE_SESSION_ORDER_MODULE_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.SAVE_SESSION_ORDER_MODULE_FAILURE: {
      const message = action?.error?.response?.data?.message;
      return state.set('isLoading', false).set('error', action.error).set('message', message);
    }

    case actions.CHECK_DUPLICATE_VERSION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.CHECK_DUPLICATE_VERSION_SUCCESS: {
      return state.set('isLoading', false).set('message', action.data);
    }
    case actions.CHECK_DUPLICATE_VERSION_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    case actions.VIEW_COURSE_DETAIL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.VIEW_COURSE_DETAIL_SUCCESS: {
      return state.set('isLoading', false).set('versionCourseDetail', fromJS(action.data));
    }
    case actions.VIEW_COURSE_DETAIL_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }
    case actions.DELETE_VERSION_COURSE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.DELETE_VERSION_COURSE_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Deleted Successfully');
    }
    case actions.DELETE_VERSION_COURSE_FAILURE: {
      const message = action?.error?.response?.data?.message;
      return state.set('isLoading', false).set('error', action.error).set('message', message);
    }
    case actions.UPLOAD_SESSION_DOC_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.UPLOAD_SESSION_DOC_SUCCESS: {
      return state.set('isLoading', false).set('message', 'Saved Successfully');
    }
    case actions.UPLOAD_SESSION_DOC_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.set('isLoading', false).set('message', errorMessage);
    }
    case actions.GENERATE_SIGNED_URL_REQUEST: {
      return state.set('isLoading', true).set('getSignedUrl', fromJS(''));
    }
    case actions.GENERATE_SIGNED_URL_SUCCESS: {
      return state.set('isLoading', false).set('getSignedUrl', fromJS(action.data));
    }
    case actions.GENERATE_SIGNED_URL_FAILURE: {
      return state.set('isLoading', false).set('error', action.error);
    }

    default:
      return state;
  }
}
