import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import { Map } from 'immutable';
import { Trans } from 'react-i18next';
import i18n from 'i18next';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { programTypeValidation } from '../utils';
import Cancel from 'Containers/Modal/Cancel';

function AddEditProgramTypeModal(props) {
  const {
    show,
    handleClose,
    type,
    selectedProgramType,
    updateProgramType,
    settingId,
    setData,
    institutionHeader,
  } = props;
  const [programType, setProgramType] = useState(
    Map({
      name: type === 'update' ? selectedProgramType.get('name', '') : '',
      code: type === 'update' ? selectedProgramType.get('code', '') : '',
    })
  );

  const [showCancel, setCancel] = useState(false);
  const [edited, setEdited] = useState(false);

  useEffect(() => {
    if (selectedProgramType) {
      setProgramType(selectedProgramType);
    }
  }, [selectedProgramType]);

  const handleChange = (event, name) => {
    setProgramType(programType.set(name, event));
    setEdited(true);
  };

  const handleSubmit = () => {
    const requestData = {
      name: programType.get('name', '').trim(),
      settingId: settingId,
      code: programType.get('code', '').trim(),
    };
    if (programTypeValidation(requestData, setData)) {
      updateProgramType({
        id: programType.get('_id'),
        requestData,
        operation: type !== 'update' ? 'create' : 'update',
        cb: handleClose,
        header: institutionHeader,
      });
    }
  };

  function isDisable() {
    return !programType.get('name', '') || !programType.get('code', '') || !edited;
  }

  const handleCancel = () => {
    if (edited) setCancel(true);
    else handleClose();
  };

  return (
    <div>
      <Modal show={show} onHide={handleClose} centered>
        <Modal.Header className="border-none pb-0">
          <Modal.Title className="f-20">
            {type === 'update'
              ? i18n.t('global_configuration.edit_program_type')
              : i18n.t('global_configuration.add_program_type')}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="pt-4">
          <div className="mb-2">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              value={programType.get('code', '')}
              changed={(e) => handleChange(e.target.value, 'code')}
              label={<Trans i18nKey={'global_configuration.short_code'}></Trans>}
              inputProps={{ maxLength: 10 }}
            />
          </div>
          <div className="mb-2">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              value={programType.get('name', '')}
              changed={(e) => handleChange(e.target.value, 'name')}
              label={<Trans i18nKey={'global_configuration.program_type'}></Trans>}
              inputProps={{ maxLength: 50 }}
            />
          </div>
        </Modal.Body>

        <Modal.Footer className="border-none">
          <MButton color="inherit" variant="outlined" clicked={handleCancel}>
            <Trans i18nKey={'cancel'}></Trans>
          </MButton>

          <MButton disabled={isDisable()} clicked={() => handleSubmit()}>
            <Trans i18nKey={'save'}></Trans>
          </MButton>
        </Modal.Footer>
      </Modal>

      {showCancel && <Cancel showCancel={showCancel} setCancel={setCancel} setShow={handleClose} />}
    </div>
  );
}

AddEditProgramTypeModal.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
  updateProgramType: PropTypes.func,
  type: PropTypes.string,
  settingId: PropTypes.string,
  selectedProgramType: PropTypes.object,
  setData: PropTypes.func,
  institutionHeader: PropTypes.object,
};

export default AddEditProgramTypeModal;
