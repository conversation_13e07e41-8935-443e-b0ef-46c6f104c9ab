import { List, Map } from 'immutable';
const globalConfigurationState = (state) => state.globalConfigurationV1;

const selectIsLoading = (state) => globalConfigurationState(state).get('isLoading');
const selectMessage = (state) => globalConfigurationState(state).get('message');
const selectBreadCrumb = (state) => globalConfigurationState(state).get('breadcrumbs');
const selectGlobalSessionSetting = (state) =>
  globalConfigurationState(state).get('globalSessionSetting');
const selectTardis = (state) => globalConfigurationState(state).get('tardis');
const selectLateConfig = (state) => globalConfigurationState(state).get('late_config');
const selectSessionStatusDetails = (state) =>
  globalConfigurationState(state).get('sessionStatusDetails');
const selectLeaderBoardSetting = (state) =>
  globalConfigurationState(state).get('leaderBoardSetting');
const selectPerformanceTableFilter = (state) =>
  globalConfigurationState(state).get('performanceTableFilter');
const selectPerformanceTableBody = (state) =>
  globalConfigurationState(state).get('performanceTableBody');
const selectTagMaster = (state) => globalConfigurationState(state).get('tagMasterSettings');
const selectCalenderList = (state) => globalConfigurationState(state).get('calenderList');
const selectConfigureTemplate = (state) =>
  globalConfigurationState(state).get('configureTemplate', List());
const selectAttemptType = (state) => globalConfigurationState(state).get('attemptType');
const selectProgramList = (state) => globalConfigurationState(state).get('qapcPrograms');
const selectProgramDetails = (state) => globalConfigurationState(state).get('qapcProgramDetails');
const selectProgramCount = (state) => globalConfigurationState(state).get('programCount');
const selectRoleUserListQ360 = (state) => globalConfigurationState(state).get('roleUserList');
const selectCategoryForm = (state) => globalConfigurationState(state).get('categoryForms');
const selectUploadFileUrl = (state) => globalConfigurationState(state).get('uploadFileUrl');
const selectCategoryFormIndex = (state, index) =>
  globalConfigurationState(state).getIn(['categoryForms', index], Map());
const selectReferenceForm = (state) => globalConfigurationState(state).get('referenceForm');
const selectCourseHandout = (state) => globalConfigurationState(state).get('courseHandout', Map());

export {
  selectIsLoading,
  selectMessage,
  selectGlobalSessionSetting,
  selectBreadCrumb,
  selectTardis,
  selectLateConfig,
  selectSessionStatusDetails,
  selectLeaderBoardSetting,
  selectProgramList,
  selectPerformanceTableFilter,
  selectPerformanceTableBody,
  selectTagMaster,
  selectCalenderList,
  selectConfigureTemplate,
  selectAttemptType,
  selectProgramDetails,
  selectProgramCount,
  selectRoleUserListQ360,
  selectCategoryForm,
  selectCategoryFormIndex,
  selectUploadFileUrl,
  selectReferenceForm,
  selectCourseHandout,
};
