import React from 'react';
import MButton from 'Widgets/FormElements/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { useDispatch } from 'react-redux';
import {
  // deleteLmsSettingsData,
  setData,
  updateTermsAndConditions,
} from '_reduxapi/leave_management/actions';

const ConfirmationPopup = ({
  setConfirmationModalOpen,
  confirmationModalOpen,
  lmsSettings,
  setSessionWise,
  // type,
  sessionWise,
  termsAndCondition,
  leaveCalculation,
}) => {
  const dispatch = useDispatch();
  const handleClose = () => {
    setConfirmationModalOpen((prev) => prev.set('isOpen', false));
  };
  const handleConfirmPopup = () => {
    const requestData = {
      settingId: lmsSettings.get('_id', ''),
      termsAndCondition,
      leaveCalculation,
      leaveApplicationCriteria: confirmationModalOpen.get('value', ''),
    };

    const callBack = () => {
      dispatch(
        setData(
          Map({
            lmsSettings: lmsSettings
              .set('termsAndCondition', termsAndCondition)
              .set('leaveCalculation', leaveCalculation)
              .set('leaveApplicationCriteria', sessionWise),
          })
        )
      );
      setSessionWise(confirmationModalOpen.get('value', ''));
      handleClose(); // Close modal after successful update
    };

    dispatch(updateTermsAndConditions(requestData, callBack));
  };
  return (
    <React.Fragment>
      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={confirmationModalOpen.get('isOpen', false)}
      >
        <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
          Confirmation Popup
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={(theme) => ({
            position: 'absolute',
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>
        <DialogContent dividers>
          Changing settings will remove all existing student applications.
        </DialogContent>
        <DialogActions>
          <MButton variant="outlined" color={'gray'} clicked={handleClose}>
            Close
          </MButton>
          <MButton clicked={() => handleConfirmPopup()}>Confirm</MButton>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
};

export default ConfirmationPopup;
ConfirmationPopup.propTypes = {
  confirmationModalOpen: PropTypes.instanceOf(Map),
  setConfirmationModalOpen: PropTypes.func,
  setSessionWise: PropTypes.func,
  sessionWise: PropTypes.string,
  lmsSettings: PropTypes.instanceOf(Map),
  termsAndCondition: PropTypes.string,
  leaveCalculation: PropTypes.string,
};
