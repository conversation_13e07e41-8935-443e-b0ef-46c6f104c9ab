import React from 'react';

import Input from '../../../Widgets/FormElements/Input/Input';
import { ucFirst } from '../../../utils';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';

export default function ContactDetails({
  contactsError,
  contact,
  countryCodeLength,
  handleChange,
}) {
  return (
    <div className="row">
      {contact &&
        contact.map((data, index) => {
          return (
            <React.Fragment key={index}>
              <div className="col-md-6 pt-1">
                <Input
                  elementType={'floatinginput'}
                  elementConfig={{
                    type: 'text',
                  }}
                  maxLength={30}
                  value={data.name}
                  floatingLabel={`${
                    data.relation_type === 'guardian'
                      ? 'Guardian'
                      : data.relation_type === 'spouse'
                      ? 'Spouse'
                      : data.relation_type === undefined
                      ? ''
                      : ucFirst(data.relation_type)
                  } Name`}
                  changed={(e) => handleChange(e, 'name', index)}
                  feedback={contactsError[index].nameError}
                />
                <Input
                  elementType={'floatinginput'}
                  elementConfig={{
                    type: 'text',
                  }}
                  // feedback={contactsError[index].mobileError}
                  maxLength={countryCodeLength}
                  value={data.mobile !== '' && data.mobile !== undefined ? data.mobile : ''}
                  floatingLabel={
                    <>
                      <span className="text-capitalize">{data.relation_type}</span>
                      &nbsp;
                      <Trans i18nKey={'mobile_number'}></Trans>
                    </>
                  }
                  changed={(e) => handleChange(e, 'mobile', index)}
                  feedback={contactsError[index].mobileError}
                />
                <Input
                  elementType={'floatinginput'}
                  elementConfig={{
                    type: 'email',
                  }}
                  maxLength={100}
                  value={data.email}
                  floatingLabel={
                    <>
                      <span className="text-capitalize">{data.relation_type}</span>
                      &nbsp;
                      <Trans i18nKey={'email'}></Trans>
                    </>
                  }
                  feedback={contactsError[index].emailError}
                  changed={(e) => handleChange(e, 'email', index)}
                />
              </div>
            </React.Fragment>
          );
        })}
    </div>
  );
}
ContactDetails.propTypes = {
  contactsError: PropTypes.func,
  contact: PropTypes.func,
  countryCodeLength: PropTypes.func,
  handleChange: PropTypes.func,
};
