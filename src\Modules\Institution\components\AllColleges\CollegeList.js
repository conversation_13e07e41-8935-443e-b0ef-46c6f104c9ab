import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import parentContext from '.././Context/university-context';
import CollegeHeading from './Component/CollegeHeading';
import FilterAndSearch from './Component/FilterAndSearch';
import IndividualCollege from './Component/IndividualList';
import Pagination from 'Widgets/FormElements/material/Pagination';
import { t } from 'i18next';

function CollegeList() {
  const details = useContext(parentContext.addCollegeContext);
  const searchFilter = useContext(parentContext.filterSearchContext);
  const { allCollegeData, getColleges, callId, setPageNo, limit, setLimit } = details;
  const { search, option, sort } = searchFilter;

  /*  const handleFetchCollege = (pageNo = 1) => {
    getColleges({ id: callId, sort, pageNo, status: option, search });
  }; */

  /* useEffect(() => {
    handleFetchCollege();
    // eslint-disable-next-line
  }, [sort, option]); */

  /* useEffect(() => {
    const timer = setTimeout(() => handleFetchCollege(), 1000);
    return () => clearTimeout(timer);
    // eslint-disable-next-line
  }, [search]); */

  const handleChange = (e, pageNo) => {
    setPageNo(pageNo);
    getColleges({ id: callId, sort, pageNo, status: option, search, limit });
  };

  return (
    <div className="add-college">
      <div className="mt-3">
        <CollegeHeading
          count={allCollegeData.get('childInstitutesCount', 0)}
          searchFilter={searchFilter}
        />
        <FilterAndSearch />
        {allCollegeData.get('institutes', List()).size ? (
          <>
            {allCollegeData.get('institutes').map((item, index) => {
              return <IndividualCollege key={index} college={item} searchFilter={searchFilter} />;
            })}
          </>
        ) : (
          <div className="mt-3 text-center">{t('no_match_found')}</div>
        )}
        <div
          className={` ${
            allCollegeData.get('institutes', List()).size
              ? 'mt-3 d-flex justify-content-end'
              : 'display-none'
          }`}
        >
          <Pagination
            count={allCollegeData.get('totalPages', 1)}
            page={allCollegeData.get('currentPage', 1)}
            onChange={handleChange}
            switchLimit={(val) => setLimit(val)}
          />
        </div>
      </div>
    </div>
  );
}

CollegeList.propTypes = {
  overViewDetails: PropTypes.instanceOf(Map),
  getOverView: PropTypes.func,
};

export default CollegeList;
