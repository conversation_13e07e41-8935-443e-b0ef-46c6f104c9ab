import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import SnackBars from '../../Modules/Utils/Snackbars';
import Loader from '../../Widgets/Loader/Loader';
import { selectMessage, selectIsLoading } from '_reduxapi/institution_calendar/selectors';
class CalendarIndex extends Component {
  render() {
    const { message, isLoading } = this.props;
    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={isLoading} />
      </div>
    );
  }
}

CalendarIndex.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    message: selectMessage(state),
    isLoading: selectIsLoading(state),
  };
};

export default compose(with<PERSON>out<PERSON>, connect(mapStateToProps))(CalendarIndex);
