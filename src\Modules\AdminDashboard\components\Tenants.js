import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Loader from '../../../Widgets/Loader/Loader';
import Snackbars from '../../../Modules/Utils/Snackbars';
import * as actions from '../../../_reduxapi/admin_dashboard/actions';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

const Tenants = () => {
  const dispatch = useDispatch();
  const { tenants, isLoading, error, migration } = useSelector((state) => state.adminDashboard);

  useEffect(() => {
    dispatch(actions.getTenantList());
  }, [dispatch]);

  if (isLoading) return <Loader isLoading={isLoading} />;

  const handleMigration = (subdomain) => {
    dispatch(actions.migrateCollections(subdomain));
  };

  return (
    <div className="mt-5">
      <h3 className="mb-2">Tenants</h3>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 700 }} aria-label="customized table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Tenant Name</StyledTableCell>
              <StyledTableCell>Subdomain</StyledTableCell>
              <StyledTableCell>Onboarded On</StyledTableCell>
              <StyledTableCell>Disable/Enable</StyledTableCell>
              <StyledTableCell>Migrate default collections</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tenants.map((row) => (
              <StyledTableRow key={row.name}>
                <StyledTableCell component="th" scope="row">
                  {row.name}
                </StyledTableCell>
                <StyledTableCell>{row.subdomain}</StyledTableCell>
                <StyledTableCell>{new Date(row.createdAt).toLocaleString()}</StyledTableCell>
                <StyledTableCell align="right">
                  <button className="btn btn-success" disabled={true}>
                    Enable/Disable
                  </button>
                </StyledTableCell>
                <StyledTableCell align="right">
                  <button
                    className="btn btn-success"
                    onClick={() => handleMigration(row.subdomain)}
                  >
                    Migrate
                  </button>
                </StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      {error !== '' ? <Snackbars message={error} /> : null}
      {migration ? <Snackbars message={'Migration running'} /> : null}
    </div>
  );
};

export default Tenants;
