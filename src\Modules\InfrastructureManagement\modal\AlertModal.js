import React from 'react';
import PropTypes from 'prop-types';
import { Button, Modal } from 'react-bootstrap';

function AlertModal(props) {
  const {
    show,
    variant = 'confirm',
    title = '',
    description = '',
    confirmButtonLabel = 'YES',
    cancelButtonLabel = 'NO',
    onClose,
    onConfirm,
    data,
    isAlertText = false,
    optionalText = '',
  } = props;
  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Body>
        <div className=" container pt-2 pb-2 border-radious-8">
          <p className="mb-3 f-20 font-weight-bold">{title}</p>
          <p className="mb-0 f-17">{description}</p>
          {isAlertText && <p className="text-red"> {optionalText} </p>}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="pr-2">
          <Button variant="outline-secondary" onClick={onClose}>
            {cancelButtonLabel}
          </Button>
        </div>
        {variant === 'confirm' && (
          <div className="pr-2">
            <Button variant="primary" onClick={() => onConfirm(data)}>
              {confirmButtonLabel}
            </Button>
          </div>
        )}
      </Modal.Footer>
    </Modal>
  );
}

AlertModal.propTypes = {
  show: PropTypes.bool,
  variant: PropTypes.oneOf(['alert', 'confirm']),
  title: PropTypes.string,
  description: PropTypes.string,
  confirmButtonLabel: PropTypes.string,
  cancelButtonLabel: PropTypes.string,
  onClose: PropTypes.func,
  onConfirm: PropTypes.func,
  data: PropTypes.any,
  isAlertText: PropTypes.bool,
  optionalText: PropTypes.string,
};

export default AlertModal;
