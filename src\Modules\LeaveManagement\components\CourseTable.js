import React, { useEffect, useState, Suspense, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import Table from 'react-bootstrap/Table';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import jsPDF from 'jspdf';
import Checkbox from '@mui/material/Checkbox';

import { formatFullName, indVerRename, isLateAbsentEnabled, ucFirst } from '../../../utils';
// import HandsOffImage from '../../../Assets/hands-off.svg';
// import HandsOnImage from '../../../Assets/hands-on.svg';
import InfoImage from '../../../Assets/info.svg';
import { CheckPermission } from '../../Shared/Permissions';
import Pagination from '../../StudentGrouping/components/Pagination';
import Tooltip from '../../../Shared/Tooltip';
import { fixedTwoDigit, addPdfFooter } from '../utils';
import { t } from 'i18next';
import MButton from 'Widgets/FormElements/material/Button';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import {
  getLateExcludeStatus,
  updateLateExclude,
} from '../../../_reduxapi/leave_management/actions';
import { selectGetLateExcludeStatus } from '../../../_reduxapi/leave_management/selectors';

import StudentAttendanceDesignModal from '../modal/StudentAttendanceDesignModal';
import StudentLateAbsentBody from './LateConfigTooltip/StudentLateAbsentBody';
import StudentLateAbsentHead from './LateConfigTooltip/StudentLateAbsentHead';
import { useSelector, useDispatch } from 'react-redux';

const CriteriaManipulationModal = React.lazy(() => import('../modal/CriteriaManipulationModal'));
const StudentProfileModal = React.lazy(() => import('../modal/StudentProfileModal'));
//const StudentAttendanceModal = React.lazy(() => import('../modal/StudentAttendanceModal'));
const NewStudentAttendanceModal = React.lazy(() => import('../modal/NewStudentAttendanceModal'));

function CourseTable(props) {
  const {
    metaData,
    activeInstitutionCalendar,
    getStudentRegisterList,
    absencePercentage,
    studentRegisterList,
    getCriteriaManipulation,
    criteriaManipulationList,
    updateStudentAbsencePercentage,
    getStudentAttendanceSheetDetails,
    userInfo,
    studentAttnSheetDetails,
    updateReason,
    setData,
    studentRegisteredData,
    studentBlackBoxContent,
    getStudentBlackBoxContent,
    activeProgram,
  } = props;
  const programId = metaData.get('program', '');
  // const [isShow, setIsShow] = useState(-1);
  const [search, setSearch] = useState('');
  const [activeBadge, setActiveBadge] = useState('');
  const [criteriaModal, setCriteriaModal] = useState(false);
  const [profileModal, setProfileModal] = useState({ status: false, studentId: '' });
  const [attendanceModal, setAttendanceModal] = useState(Map({ status: false, data: Map() }));
  const [newDesignModal, setNewDesignModal] = useState(false); //eslint-disable-line

  const [pageCount, setPageCount] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectAll, setSelectAll] = useState([]);
  const [open, setOpen] = useState(false);
  const [typeWise, setTypeWise] = useState('');

  const isLateEnabled = isLateAbsentEnabled();
  const dispatch = useDispatch();
  const LateExcludeStatus = useSelector(selectGetLateExcludeStatus);

  const params = {
    institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
    programId: metaData.get('program', ''),
    courseId: metaData.get('courseId', ''),
    levelNo: metaData.get('level', ''),
    term: metaData.get('term', ''),
    gender: metaData.get('gender', ''),
    rotationCount: metaData.get('rotationCount', null),
    typeWiseUpdate: 'course_wise',
  };

  const studentRegisterListFilter = studentRegisterList;
  // .filter((item) =>
  //   activeBadge !== '' ? item.get('warning', '') === activeBadge : item
  // );

  const filteredBadge = studentRegisterListFilter.filter((item) =>
    activeBadge !== '' ? item.get('warning', '') === activeBadge : item
  );

  const filteredData = filteredBadge.filter((item) =>
    search !== ''
      ? item.get('academic_no', '').includes(search) ||
        item.getIn(['name', 'first'], '').toLowerCase().includes(search) ||
        item.getIn(['name', 'middle'], '').toLowerCase().includes(search) ||
        item.getIn(['name', 'last'], '').toLowerCase().includes(search)
      : item
  );

  let totalPages =
    filteredData.size % pageCount === 0
      ? filteredData.size / pageCount
      : Math.floor(filteredData.size / pageCount) + 1;

  const fetchApi = useCallback(() => {
    const fetchTermFromLevel = activeProgram
      .get('level', List())
      .filter(
        (item) =>
          item.get('year', '') === metaData.get('year', '') &&
          item.get('level_no', '') === metaData.get('level', '') &&
          item.get('term', '').toLowerCase() === metaData.get('term', '').toLowerCase()
      );
    getStudentRegisterList({
      institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
      programId: metaData.get('program', ''),
      courseId: metaData.get('courseId', ''),
      levelNo: metaData.get('level', ''),
      term: fetchTermFromLevel.getIn([0, 'term'], metaData.get('term', '')),
      gender: metaData.get('gender', ''),
      rotationCount: metaData.get('rotationCount', null),
    });
    dispatch(getLateExcludeStatus({ params }));
  }, [metaData, activeInstitutionCalendar, getStudentRegisterList, getLateExcludeStatus]); //eslint-disable-line

  useEffect(() => {
    if (
      activeInstitutionCalendar.get('_id', '') !== '' &&
      metaData.get('courseId', '') !== '' &&
      metaData.get('level', '') !== '' &&
      metaData.get('program', '') !== '' &&
      metaData.get('term', '') !== ''
    ) {
      fetchApi();
      setCurrentPage(1);
      setPageCount(10);
      setActiveBadge('');
    }
  }, [metaData, activeInstitutionCalendar, getStudentRegisterList, fetchApi]); //eslint-disable-line

  function onNextClick() {
    if (currentPage + 1 >= totalPages) {
      setCurrentPage(totalPages);
    } else {
      setCurrentPage(currentPage + 1);
    }
  }

  function pagination(value) {
    setPageCount(value);
    setCurrentPage(1);
  }

  function onFullLastClick() {
    setCurrentPage(1);
  }

  function onFullForwardClick() {
    setCurrentPage(totalPages);
  }

  function onBackClick() {
    if (currentPage - 1 === 0) {
      setCurrentPage(1);
    } else {
      setCurrentPage(currentPage - 1);
    }
  }

  function handleSearch(e) {
    setSearch(e.target.value.toLowerCase());
    setCurrentPage(1);
  }

  function calculateAttendance(item) {
    return fixedTwoDigit(item.get('attendance', 0));
  }

  function getLeaveCount(item) {
    return item.get('leave_types', List()).reduce((acc, el) => el.get('count', 0) + acc, 0);
  }

  function getLeaveHtml(item) {
    return (
      <React.Fragment>
        <b>{getLeaveCount(item)}</b>
        <hr className="tooltip-hr" />
        <table>
          {item.get('leave_types', List()).map((it, i) => (
            <tr key={i}>
              <td>{ucFirst(it.get('type', '-'))}</td>
              <td>-</td>
              <td>{it.get('count', 0)}</td>
            </tr>
          ))}
        </table>
      </React.Fragment>
    );
  }

  function getAbsenceHtml(item) {
    return (
      <React.Fragment>
        <b>{fixedTwoDigit(item.get('warning_absence', 0))}%</b>
        <hr className="tooltip-hr" />
        <table>
          {item.get('absence', List()).map((it, i) => (
            <tr key={i}>
              <td>{indVerRename(it.get('type_symbol', '-'), programId)}</td>
              <td>-</td>
              <td>{fixedTwoDigit(it.get('completed_percentage', 0))}</td>
            </tr>
          ))}
        </table>
      </React.Fragment>
    );
  }

  function handleAllCheckboxChange(e) {
    let updatedData = [];
    if (e.target.checked) {
      updatedData = filteredData.map((item) => item.get('_student_id', '')).toJS();
    }
    setSelectAll(updatedData);
  }

  function handleCheckboxChange(e, id) {
    let selectedArray = [...selectAll];
    if (e.target.checked) {
      selectedArray.push(id);
    } else {
      selectedArray = selectAll.filter((e) => e !== id);
    }
    setSelectAll(selectedArray);
  }

  function getCreditHours(item) {
    return (
      <React.Fragment>
        <b>{fixedTwoDigit(item.get('absent', 0))}%</b>
        <hr className="tooltip-hr" />
        <table>
          {item.get('credit_hours', List()).map((it, i) => (
            <tr key={i}>
              <td>{indVerRename(it.get('type_symbol', '-'), programId)}</td>
              <td>-</td>
              <td>{fixedTwoDigit(it.get('completed_percentage', 0))}</td>
            </tr>
          ))}
        </table>
      </React.Fragment>
    );
  }

  function getLists() {
    let startingIndex = (currentPage - 1) * pageCount;

    if (filteredData.size > 0) {
      return (
        filteredData
          // .filter((items) => (activeBadge !== '' ? items.get('warning', '') === activeBadge : items))
          // .filter((item) =>
          //   search !== ''
          //     ? item.get('academic_no', '').includes(search) ||
          //       item.getIn(['name', 'first'], '').toLowerCase().includes(search) ||
          //       item.getIn(['name', 'middle'], '').toLowerCase().includes(search) ||
          //       item.getIn(['name', 'last'], '').toLowerCase().includes(search)
          //     : item
          // )
          .filter((_, i) => i >= pageCount * (currentPage - 1) && i < pageCount * currentPage)
          .map((item, index) => {
            // const absencePercentages =
            //   item.get('absence', 0) !== 'NaN' ? (item.get('absence', 0) * 100) / 100 : '0';
            return (
              <tr
                className="tr-bottom-border"
                key={index}
                // onMouseEnter={() => setIsShow(index)}
                // onMouseLeave={() => setIsShow(-1)}
              >
                <td className="ptop-checkbox">
                  <Checkbox
                    color="primary"
                    checked={selectAll.includes(item.get('_student_id'))}
                    size="medium"
                    onClick={(e) => handleCheckboxChange(e, item.get('_student_id'))}
                  />
                </td>
                <td>{startingIndex + index + 1}</td>
                <td className="">
                  {CheckPermission(
                    'tabs',
                    'Leave Management',
                    'Student Register',
                    '',
                    'Attendance Sheet',
                    'View'
                  ) && (
                    <>
                      <div
                        className="remove_hover fb-icon"
                        onClick={() => {
                          setAttendanceModal(Map({ status: true, data: item }));
                        }}
                      >
                        <span></span>
                      </div>
                      {/* <img
                          className="remove_hover"
                           src={index === isShow ? HandsOnImage : HandsOffImage}
                          alt="In Active"
                          // onClick={() => setAttendanceModal(Map({ status: true, data: item }))}
                          onClick={() => {
                            setAttendanceModal(Map({ status: true, data: item }));
                          }}
                        /> */}
                    </>
                  )}
                  {/* <br />
                  <p
                    className="f-20 remove_hover"
                    onClick={() => setNewDesignModal(!newDesignModal)}
                  >
                    {' '}
                    siva
                  </p>
                  <br />
                  <p
                    className="f-20 remove_hover"
                    onClick={() => {
                      setAttendanceModal(Map({ status: true, data: item }));
                    }}
                  >
                    {' '}
                    deepak
                  </p> */}
                </td>
                <td
                  onClick={() => {
                    if (
                      CheckPermission(
                        'pages',
                        'Leave Management',
                        'Student Register',
                        'Profile View'
                      )
                    ) {
                      setProfileModal({ status: true, studentId: item.get('_student_id', '') });
                    }
                  }}
                  className={`${
                    CheckPermission('pages', 'Leave Management', 'Student Register', 'Profile View')
                      ? 'text-blue'
                      : ''
                  } bold remove_hover`}
                >
                  {item.get('academic_no', '')}
                </td>
                <td>{formatFullName(item.get('name', Map()).toJS())}</td>
                <td>
                  {item.get('session_attended', '')} / {item.get('session_conducted', '')}{' '}
                  {`(P - ${item.getIn(
                    ['scheduleAttendance', 'present_count'],
                    0
                  )}, A - ${item.getIn(
                    ['scheduleAttendance', 'absent_count'],
                    0
                  )}, PER - ${item.getIn(
                    ['scheduleAttendance', 'permission_count'],
                    0
                  )}, L - ${item.getIn(
                    ['scheduleAttendance', 'leave_count'],
                    0
                  )}, OD - ${item.getIn(
                    ['scheduleAttendance', 'onduty_count'],
                    0
                  )}, LA - ${item.getIn(['scheduleAttendance', 'studentLateAbsent'], 0)}) `}
                </td>
                <td>{item.get('total_session', '')}</td>
                <td>{calculateAttendance(item)}%</td>
                <td>
                  {fixedTwoDigit(item.get('absent', 0))}%{' '}
                  <Tooltip title={getCreditHours(item)} placement="top" arrow>
                    <img className="pl-2 remove_hover" src={InfoImage} alt="info" />
                  </Tooltip>
                </td>
                <td>
                  {getLeaveCount(item)}
                  <Tooltip title={getLeaveHtml(item)} placement="top" arrow>
                    <img className="pl-2 remove_hover" src={InfoImage} alt="info" />
                  </Tooltip>
                </td>
                <td>
                  {fixedTwoDigit(item.get('warning_absence', 0))}%
                  <Tooltip title={getAbsenceHtml(item)} placement="top" arrow>
                    <img className="pl-2 remove_hover" src={InfoImage} alt="info" />
                  </Tooltip>
                </td>
                {/* <td>
                  {item.get('manipulationStatus', false)
                    ? fixedTwoDigit(item.get('manipulationPercentage', 0)) + '%'
                    : '-'}
                </td> */}
                {isLateEnabled && (
                  <td>
                    {item.get('studentLateAbsent', 0)}
                    {item.get('lateConfig', List()).size > 0 && (
                      <StudentLateAbsentBody lateConfig={item.get('lateConfig', List())} />
                    )}
                  </td>
                )}

                <td className="text-red">
                  {item.get('warning', '') !== '' ? (
                    <>
                      <i className="fa fa-exclamation-triangle"></i> {item.get('warning', '')}
                    </>
                  ) : (
                    ''
                  )}
                </td>
              </tr>
            );
          })
      );
    }
    return <></>;
  }

  function exportPdf() {
    const doc = new jsPDF('landscape');

    let tableHeaders = [
      [
        'S.NO.',
        'ACADEMIC #',
        'NAME',
        'ATTENDED / CONDUCTED',
        'TOTAL SESSION',
        'ATTENDANCE %',
        'ABSENT %',
        'NO OF LEAVE',
        'WARNING ABSENT %',
        // 'CRITERIA MANIPULATED %',
        'STATUS',
      ],
    ];
    if (isLateEnabled) {
      tableHeaders[0].splice(10, 0, 'STUDENT LATE ABSENT');
    }

    const data = filteredData
      .map((item, index) => {
        return [
          index + 1,
          item.get('academic_no', ''),
          formatFullName(item.get('name', Map()).toJS()),
          `${item.get('session_attended', 0)} / ${item.get(
            'session_conducted',
            0
          )} (P - ${item.getIn(['scheduleAttendance', 'present_count'], 0)}, A - ${item.getIn(
            ['scheduleAttendance', 'absent_count'],
            0
          )}, PER - ${item.getIn(['scheduleAttendance', 'permission_count'], 0)}, L - ${item.getIn(
            ['scheduleAttendance', 'leave_count'],
            0
          )}, OD - ${item.getIn(['scheduleAttendance', 'onduty_count'], 0)}, LA - ${item.getIn(
            ['scheduleAttendance', 'studentLateAbsent'],
            0
          )})`,
          item.get('total_session', ''),
          calculateAttendance(item) + '%',
          fixedTwoDigit(item.get('absent', 0)) + '%',
          getLeaveCount(item),
          fixedTwoDigit(item.get('warning_absence', 0)) + '%',
          // item.get('manipulationStatus', false)
          //   ? fixedTwoDigit(item.get('manipulationPercentage', 0)) + '%'
          //   : '-',
          item.get('warning', ''),
          isLateEnabled ? item.get('studentLateAbsent', '') : '',
        ];
      })
      .toJS();

    doc.setFont('helvetica', 'bold');
    doc.setFontSize(15);
    doc.text(`Program : ${metaData.get('programName', '')}`, 15, 15);
    doc.text(
      `${metaData.get('courseNumber', '')} - ${metaData.get('courseName', '')}${metaData.get(
        'versionName',
        ''
      )}
`,
      15,
      22
    );
    doc.text(
      `Enrolled students : ${studentRegisterListFilter.size} | ${studentRegisteredData.get(
        'denialLabelName',
        'Denial'
      )} - Absence Percentage : ${studentRegisteredData.get('absence_percentage', 0)}% ${
        activeBadge !== '' ? ` | ${activeBadge}` : ''
      }`,
      15,
      29
    );
    // doc.text(
    //   `Session completed - ${studentRegisteredData.get(
    //     'overall_completed_schedule',
    //     0
    //   )} / ${studentRegisteredData.get(
    //     'overall_total_schedule',
    //     0
    //   )} (${getCompletedPercentage()}%)`,
    //   15,
    //   36
    // );
    doc.line(0, 36, 300, 36); //43
    doc.autoTable({
      startY: 43, //52
      styles: { fillColor: false, textColor: '#000000' },
      columnStyles: {
        1: { cellWidth: 27 },
        5: { cellWidth: 32 },
        6: { cellWidth: 30 },
        9: { cellWidth: 33 },
      },
      headStyles: { lineWidth: 0.5, lineColor: [189, 195, 199] },
      bodyStyles: { fontSize: 9 },
      rowPageBreak: 'avoid',
      head: tableHeaders,
      body: data.length > 0 ? data : [['No data found']],
      theme: 'grid',
    });
    addPdfFooter(doc);
    doc.save(
      `${metaData.get('courseNumber', '')} - ${metaData.get('courseName', '')} - Student List.pdf`
    );
  }

  function getWarningsFilter() {
    if (studentRegisterListFilter.size > 0) {
      const value = studentRegisterListFilter
        .map((item) => item.get('warning', ''))
        .toJS()
        .reduce((unique, item) => (unique.includes(item) ? unique : [...unique, item]), [])
        .filter((el) => el !== '');
      const withWarning = value
        .filter((data) => {
          return !data.includes('Denial');
        })
        .sort((a, b) => (a > b ? 1 : a < b ? -1 : 0));
      const withoutWarning = value
        .filter((data) => {
          return data.includes('Denial');
        })
        .sort((a, b) => (a > b ? 1 : a < b ? -1 : 0));
      const filterValue = [...withWarning, ...withoutWarning];

      if (filterValue.length > 0) {
        return filterValue.map((item, index) => {
          return (
            <span className="pr-3" key={index}>
              <Badge
                variant={`light badge_grouping ${
                  activeBadge === item ? 'badge_grouping_active' : ''
                }`}
                size="sm"
                className="mb-1"
                onClick={() => {
                  setActiveBadge(item);
                  setCurrentPage(1);
                }}
              >
                {ucFirst(item.trim())}
              </Badge>
            </span>
          );
        });
      }
      return value;
    }
    return [];
  }

  // function getCompletedPercentage() {
  //   return !studentRegisteredData.isEmpty()
  //     ? fixedTwoDigit(
  //         (studentRegisteredData.get('overall_completed_schedule', 0) /
  //           studentRegisteredData.get('overall_total_schedule', 0)) *
  //           100
  //       )
  //     : 0;
  // }

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSubmit = () => {
    const requestBody = {
      institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
      programId: metaData.get('program', ''),
      courseId: metaData.get('courseId', ''),
      levelNo: metaData.get('level', ''),
      term: metaData.get('term', ''),
      gender: metaData.get('gender', ''),
      rotationCount: metaData.get('rotationCount', null),
      typeWiseUpdate: 'course_wise',
      excludeStatus: LateExcludeStatus === true ? false : true,
    };

    setTypeWise('course_wise');

    const callBack = () => {
      const fetchTermFromLevel = activeProgram
        .get('level', List())
        .filter(
          (item) =>
            item.get('year', '') === metaData.get('year', '') &&
            item.get('level_no', '') === metaData.get('level', '') &&
            item.get('term', '').toLowerCase() === metaData.get('term', '').toLowerCase()
        );

      dispatch(getLateExcludeStatus({ params }));

      getStudentRegisterList({
        institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
        programId: metaData.get('program', ''),
        courseId: metaData.get('courseId', ''),
        levelNo: metaData.get('level', ''),
        term: fetchTermFromLevel.getIn([0, 'term'], metaData.get('term', '')),
        gender: metaData.get('gender', ''),
        rotationCount: metaData.get('rotationCount', null),
      });
    };

    dispatch(updateLateExclude(requestBody, callBack));
    setOpen(false);
  };
  return (
    <React.Fragment>
      {attendanceModal.get('status', false) && (
        <Suspense fallback="">
          {/* <StudentAttendanceModal
            attendanceModal={attendanceModal}
            setAttendanceModal={setAttendanceModal}
            getStudentAttendanceSheetDetails={getStudentAttendanceSheetDetails}
            institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
            metaData={metaData}
            userInfo={userInfo}
            studentAttnSheetDetails={studentAttnSheetDetails}
            updateReason={updateReason}
            setData={setData}
            callBack={fetchApi}
            studentRegisteredData={studentRegisteredData}
            setCriteriaModal={() => {
              setCriteriaModal(true);
            }}
          /> */}
          <NewStudentAttendanceModal
            studentBlackBoxContent={studentBlackBoxContent}
            getStudentBlackBoxContent={getStudentBlackBoxContent}
            attendanceModal={attendanceModal}
            setAttendanceModal={setAttendanceModal}
            getStudentAttendanceSheetDetails={getStudentAttendanceSheetDetails}
            institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
            metaData={metaData}
            userInfo={userInfo}
            studentAttnSheetDetails={studentAttnSheetDetails}
            updateReason={updateReason}
            setData={setData}
            callBack={fetchApi}
            studentRegisteredData={studentRegisteredData}
            setCriteriaModal={() => {
              setCriteriaModal(true);
            }}
            activeProgram={activeProgram}
            // LateExcludeStatus={LateExcludeStatus}
            typeWise={typeWise}
            setTypeWise={setTypeWise}
          />
        </Suspense>
      )}
      {criteriaModal && (
        <Suspense fallback="">
          <CriteriaManipulationModal
            criteriaModal={criteriaModal}
            setCriteriaModal={setCriteriaModal}
            absencePercentage={absencePercentage}
            getCriteriaManipulation={getCriteriaManipulation}
            updateStudentAbsencePercentage={updateStudentAbsencePercentage}
            institutionCalendarId={activeInstitutionCalendar.get('_id', '')}
            metaData={metaData}
            criteriaManipulationList={criteriaManipulationList}
            selectAll={selectAll}
            userInfo={userInfo}
            setSelectAll={() => setSelectAll([])}
            attendanceModal={attendanceModal}
            studentAttnSheetDetails={studentAttnSheetDetails}
          />
        </Suspense>
      )}
      {profileModal.status && (
        <Suspense fallback="">
          <StudentProfileModal
            profileModal={profileModal}
            setProfileModal={() => setProfileModal({ status: false, studentId: '' })}
          />
        </Suspense>
      )}

      {newDesignModal && <StudentAttendanceDesignModal open={newDesignModal} />}

      <div className="border-light bg-white p-3">
        <div className="pt-2 pb-4 text-left">
          <p className="mb-1 font-weight-bold">
            {`${metaData.get('courseNumber', '')} - ${metaData.get('courseName', '')}${metaData.get(
              'versionName',
              ''
            )}`}
          </p>
          {/* <div className="d-flex justify-content-between"> */}
          <p className="mb-2">
            {t('leaveManagement.enRolledStudents')} - {studentRegisterListFilter.size} |{' '}
            {studentRegisteredData.get('denialLabelName', 'Denial')} - Absence Percentage:{' '}
            {studentRegisteredData.get('absence_percentage', 0)}%{' '}
            {studentRegisteredData.get('manipulationStatus', false) && (
              <span className="criteria_manipulated">
                {t('leaveManagement.criteriaManipulated')}
              </span>
            )}
          </p>
          {/* <p className="mb-2 ">
            Session completed - {studentRegisteredData.get('overall_completed_schedule', 0)}/
            {studentRegisteredData.get('overall_total_schedule', 0)} ({getCompletedPercentage()}%)
          </p> */}
          {/* <p className="mb-2">Updating ...</p> */}
          {/* </div> */}
          <div>
            <span className="pr-3">
              <Badge
                variant={`light badge_grouping ${
                  activeBadge === '' ? 'badge_grouping_active' : ''
                }`}
                size="sm"
                className="mb-1"
                onClick={() => {
                  setActiveBadge('');
                  setCurrentPage(1);
                }}
              >
                {t('leaveManagement.allStudents')}
              </Badge>
            </span>
            {getWarningsFilter()}
          </div>
          <div className="row text-right">
            <div className="col-lg-4"></div>
            <div className="col-lg-4">
              {CheckPermission('pages', 'Leave Management', 'Student Register', 'Search') && (
                <div className="search-list">
                  <input
                    type="text"
                    className="searchTerm-list"
                    placeholder={t('user_management.Search')}
                    onChange={(e) => handleSearch(e)}
                  />
                  <i className="fa fa-search searchButton-list"></i>
                </div>
              )}
            </div>
            <div className="col-lg-4">
              {/* {CheckPermission(
                'tabs',
                'Leave Management',
                'Student Register',
                '',
                'Criteria Manipulation',
                'View'
              ) && (
                <Button
                  variant="outline-primary"
                  className="border-radious-8 f-15 mr-2"
                  onClick={() => setCriteriaModal(true)}
                >
                  {t('leaveManagement.criteriaManipulation')}
                </Button>
              )} */}
              {CheckPermission('pages', 'Leave Management', 'Student Register', 'Export') && (
                <Button
                  disabled={studentRegisterListFilter.size === 0}
                  variant="outline-primary"
                  className="border-radious-8 f-15"
                  onClick={exportPdf}
                >
                  {t('role_management.role_actions.Export')}
                </Button>
              )}
            </div>
            <div className="ml-auto mr-3 mt-2">
              {isLateEnabled && (
                <Button
                  disabled={studentRegisterListFilter.size === 0}
                  variant="primary"
                  className="border-radious-8 f-15"
                  onClick={handleClickOpen}
                >
                  Exclude Late Attendance
                </Button>
              )}
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-md-12 ">
            <Table responsive hover className="bg-white ">
              <thead className="group_table_top text_no_wrap">
                <tr>
                  <th className="border_color_blue ptop-checkbox1">
                    <Checkbox
                      color="primary"
                      checked={filteredData.size > 0 && filteredData.size === selectAll.length}
                      size="medium"
                      onClick={(e) => handleAllCheckboxChange(e)}
                    />
                  </th>
                  <th className="border_color_blue">
                    <b>{t('student_grouping.serial_no')}</b>
                  </th>
                  <th className="border_color_blue"></th>
                  <th className="border_color_blue" style={{ width: '115px' }}>
                    <b>{t('leaveManagement.academic')}</b>
                  </th>

                  <th className="border_color_blue">
                    <b>{t('leaveManagement.name')}</b>
                  </th>
                  <th className="border_color_blue" style={{ width: '280px' }}>
                    <b>{t('leaveManagement.attendedConducted')}</b>
                  </th>
                  <th className="border_color_blue" style={{ width: '130px' }}>
                    <b>{t('leaveManagement.totalSession')}</b>
                  </th>
                  <th className="border_color_blue" style={{ width: '130px' }}>
                    <b>{t('leaveManagement.attendance')}</b>
                  </th>
                  <th className="border_color_blue" style={{ width: '115px' }}>
                    <b>{t('leaveManagement.absent')}</b>
                  </th>
                  <th className="border_color_blue" style={{ width: '115px' }}>
                    <b>{t('leaveManagement.noOfLeave')}</b>
                  </th>
                  <th className="border_color_blue">
                    <b>{t('leaveManagement.warningAbsent')}</b>
                  </th>
                  {/* <th className="border_color_blue">
                    <b>{t('leaveManagement.CriteriaManipulation')}</b>
                  </th> */}
                  {isLateEnabled && (
                    <th className="border_color_blue" style={{ width: '200px' }}>
                      <b>{t('leaveManagement.studentLateAbsent')}</b>
                      <StudentLateAbsentHead />
                    </th>
                  )}
                  <th className="border_color_blue" style={{ width: '200px' }}>
                    <b>{t('leaveManagement.status')}</b>
                  </th>
                </tr>
              </thead>

              <tbody className="text_no_wrap">
                {studentRegisterListFilter.size === 0 && (
                  <tr className="tr-bottom-border">
                    <td colSpan="12"> {t('no_data')}</td>
                  </tr>
                )}
                {studentRegisterListFilter.size > 0 && getLists()}
              </tbody>
            </Table>
            {studentRegisterListFilter.size > 0 && (
              <Pagination
                pagination={pagination}
                onNextClick={onNextClick}
                pagevalue={pageCount}
                onBackClick={onBackClick}
                onFullLastClick={onFullLastClick}
                onFullForwardClick={onFullForwardClick}
                data={totalPages}
                currentPage={currentPage}
              />
            )}
            <DialogModal show={open} onClose={handleClose} fullWidth={true} maxWidth={'sm'}>
              <h5 className="m-2">
                {`Are you sure want to ${
                  LateExcludeStatus === true ? 'enable' : 'disable'
                } late absentees for this course?`}
              </h5>
              <div className="d-flex m-3">
                <div className="ml-auto">
                  <MButton clicked={handleClose}>Cancel</MButton>
                </div>
                <div className="ml-2">
                  <MButton clicked={handleSubmit}>Submit</MButton>
                </div>
              </div>
            </DialogModal>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}

CourseTable.propTypes = {
  metaData: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getStudentRegisterList: PropTypes.func,
  studentRegisterList: PropTypes.instanceOf(List),
  absencePercentage: PropTypes.string,
  getCriteriaManipulation: PropTypes.func,
  updateStudentAbsencePercentage: PropTypes.func,
  criteriaManipulationList: PropTypes.instanceOf(List),
  getStudentAttendanceSheetDetails: PropTypes.func,
  userInfo: PropTypes.instanceOf(Map),
  studentAttnSheetDetails: PropTypes.instanceOf(Map),
  updateReason: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  studentRegisteredData: PropTypes.instanceOf(Map),
  studentBlackBoxContent: PropTypes.instanceOf(Map),
  getStudentBlackBoxContent: PropTypes.func,
  activeProgram: PropTypes.instanceOf(Map),
};

export default CourseTable;
