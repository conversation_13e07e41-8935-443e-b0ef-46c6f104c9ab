import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { List, Map, Set } from 'immutable';
import { useParams } from 'react-router-dom';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { differenceInMinutes } from 'date-fns';
import moment from 'moment';
import InfoChart from './InfoChart';
import TableWithPagination from './TableWithPagination';
import CourseSessionStatusFilters from './Filters/CourseSessionStatusFilters';
import Tooltip from '../../../Shared/Tooltip';
import SessionStatusModal from '../modal/sessionStatusView';

import * as actions from '../../../_reduxapi/reports_and_analytics/action';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import { selectCourseSessionStatusData } from '../../../_reduxapi/reports_and_analytics/selectors';
import {
  COUNT_INFO_GENERIC_TYPES,
  getCourseDeliveryTypesChartData,
  getFormattedGroupName,
} from '../utils';
import {
  capitalize,
  getURLParams,
  dString,
  getTranslatedDuration,
  indVerRename,
  isIndGroup,
  getEnvLabelChanged,
  studentGroupRename,
  studentGroupViewList,
} from '../../../utils';
import { getStartEndDate } from '../../CourseScheduling/components/utils';

import MergeIcon from '../../../Assets/merge.svg';
import { t } from 'i18next';

function CourseSessionStatus(props) {
  const { activeInstitutionCalendar, getCourseSessionStatusData, courseSessionStatus } = props;
  const [modalData, setModalData] = useState();
  const [show, setShow] = useState(false);
  const params = useParams();
  const [filters, setFilters] = useState(
    Map({
      studentGroup: '',
      status: '',
      subject: '',
      deliveryType: '',
      mode: '',
      isPopulated: false,
    })
  );

  const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id');
  const programId = params.programId ? dString(params.programId) : '';
  const courseId = params.courseId ? dString(params.courseId) : '';
  const level = getURLParams('level', true);
  const term = getURLParams('term', true);
  const isRotation = getURLParams('rotation', true) === 'yes';
  const rotationCount = getURLParams('rotationCount', true);

  useEffect(() => {
    if (activeInstitutionCalendarId && programId && courseId && level && term) {
      getCourseSessionStatusData({
        institutionCalendarId: activeInstitutionCalendarId,
        programId,
        courseId,
        level,
        term,
        ...(isRotation && { rotationCount }),
      });
    }
  }, [
    getCourseSessionStatusData,
    activeInstitutionCalendarId,
    programId,
    courseId,
    level,
    term,
    isRotation,
    rotationCount,
  ]);

  useEffect(() => {
    const studentGroups = courseSessionStatus.get('session_status', List());
    if (studentGroups.size) {
      // && !filters.get('isPopulated')
      setFilters(
        filters.merge(
          Map({
            studentGroup: studentGroups.getIn([0, 'group_id'], ''),
            // isPopulated: true,
          })
        )
      );
    }
  }, [courseSessionStatus]); // eslint-disable-line

  function getActiveStudentGroup() {
    const studentGroupId = filters.get('studentGroup', '');
    return (
      courseSessionStatus
        .get('session_status', List())
        .find((sg) => sg.get('group_id') === studentGroupId) || Map()
    );
  }

  function handleFilterChange(name, value) {
    setFilters(
      filters.merge(
        Map({
          [name]: value,
          ...(name === 'studentGroup' && { status: '', subject: '', deliveryType: '', mode: '' }),
        })
      )
    );
  }
  function getStudentGroupOptions() {
    return courseSessionStatus
      .get('session_status', List())
      .map((group) => Map({ name: group.get('group_name', ''), value: group.get('group_id', '') }));
  }

  function getCountInfoCardData() {
    const studentGroup = getActiveStudentGroup();
    const total = studentGroup.get('no_of_sessions', 0);
    const completed = studentGroup.get('completed', 0);
    const pending = studentGroup.get('pending', 0);
    const merged = studentGroup.get('merged', 0);
    const canceled = studentGroup.get('cancelled', 0);
    const missed = studentGroup.get('missed', 0);

    return [
      COUNT_INFO_GENERIC_TYPES.getScheduledSessionCardData({ type: 'session', count: total }),
      COUNT_INFO_GENERIC_TYPES.getSessionStatusCompletePending({
        completed: `${completed} ${t('reports_analytics.session_s')}`,
        pending: `${pending} ${t('reports_analytics.session_s')}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionStatusMissed({
        missed: `${missed} ${t('reports_analytics.session_s')}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionStatusMergedCanceled({
        merged: `${merged} ${t('reports_analytics.session_s')}`,
        canceled: `${canceled} ${t('reports_analytics.session_s')}`,
      }),
    ];
  }

  function getDeliveryTypeChartData() {
    return getCourseDeliveryTypesChartData(getDeliveryTypes());
  }

  function getDeliveryTypes() {
    const studentGroup = getActiveStudentGroup();
    return studentGroup.get('sessions', List());
  }

  function getSubjects() {
    const studentGroup = getActiveStudentGroup();
    return studentGroup
      .get('schedule_data', List())
      .reduce((acc, schedule) => acc.concat(schedule.get('subjects', List())), List())
      .reduce((acc, subject) => acc.set(subject.get('_subject_id'), subject), Map())
      .valueSeq()
      .toList();
  }

  function getFilterOptions() {
    const statuses = List([
      Map({ name: t('reports_analytics.completed'), value: 'completed' }),
      Map({ name: t('reports_analytics.pending'), value: 'pending' }),
      Map({ name: t('reports_analytics.merged'), value: 'merged' }),
      Map({ name: t('reports_analytics.canceled'), value: 'canceled' }),
      Map({ name: t('reports_analytics.missed'), value: 'missed' }),
    ]);
    const modes = List([
      Map({
        name: getEnvLabelChanged()
          ? indVerRename('Onsite', programId)
          : t('reports_analytics.onsite'),
        value: 'onsite',
      }),
      Map({ name: t('reports_analytics.remote'), value: 'remote' }),
      Map({ name: 'Offline', value: 'offline' }),
      Map({ name: 'Web', value: 'web' }),
    ]);
    const deliveryTypes = Set(
      getDeliveryTypes().map((d) =>
        Map({
          name: d.get('session_type', ''),
          value: d.get('session_type', ''),
        })
      )
    );
    const subjects = getSubjects().map((subject) =>
      Map({
        name: subject.get('subject_name', ''),
        value: subject.get('_subject_id', ''),
      })
    );

    return Map({ statuses, subjects, deliveryTypes, modes });
  }

  function filterTableData(tableData = List()) {
    const status = filters.get('status', '');
    const subject = filters.get('subject', '');
    const deliveryType = filters.get('deliveryType', '');
    const mode = filters.get('mode', '');

    return tableData
      .filter((schedule) => {
        let incStatus = true;
        let incSubject = true;
        let incDeliveryType = true;
        let incMode = true;

        if (status) {
          if (status === 'canceled') {
            incStatus = schedule.get('isActive') === false;
          } else if (status === 'merged') {
            incStatus = schedule.get('merge_status') === true;
          } else {
            incStatus = schedule.get('status') === status;
          }
        }
        if (subject) {
          incSubject = schedule
            .get('subjects', List())
            .map((subject) => subject.get('_subject_id', ''))
            .includes(subject);
        }
        if (deliveryType) {
          if (deliveryType === 'support_session'.toLowerCase()) {
            incDeliveryType = schedule.get('type', '').toLowerCase() === deliveryType.toLowerCase();
          } else {
            incDeliveryType =
              schedule.getIn(['session', 'session_type'], '').toLowerCase() ===
              deliveryType.toLowerCase();
          }
        }
        if (mode) {
          incMode =
            schedule.get('mode', '') === mode ||
            schedule.get('classModeType', '') === mode ||
            schedule.get('scheduleStartFrom', '') === mode;
        }

        return incStatus && incSubject && incDeliveryType && incMode;
      })
      .toList();
  }

  function getTableColumns() {
    return [
      { name: t('reports_analytics.delivery_type'), key: 'formattedDeliveryType' },
      { name: t('reports_analytics.student_group'), key: 'formattedStudentGroups' },
      { name: t('reports_analytics.subject'), key: 'formattedSubjects' },
      { name: t('reports_analytics.mode'), key: 'formattedMode' },
      { name: t('reports_analytics.staff'), key: 'formattedStaffs' },
      { name: t('reports_analytics.date_time'), key: 'scheduleDate' },
      { name: t('reports_analytics.start_time'), key: 'startTime' },
      { name: t('reports_analytics.end_time'), key: 'endTime' },
      { name: t('reports_analytics.duration'), key: 'duration' },
      { name: t('reports_analytics.status'), key: 'statusIcon' },
    ];
  }

  function prepareTableData(data = List()) {
    return data.map((schedule) => {
      const session = schedule.get('session', Map());
      const isMerged = schedule.get('merge_status', false);
      const deliverySymbol = session.get('delivery_symbol', '');
      const deliveryNumber = session.get('delivery_no', '');
      let formattedDeliveryType = `${deliverySymbol}${deliveryNumber} ${session.get(
        'session_topic',
        ''
      )}`;
      if (session.isEmpty()) {
        formattedDeliveryType = schedule.get('title', '');
      }
      if (isMerged) {
        formattedDeliveryType = List([`${deliverySymbol}${deliveryNumber}`])
          .concat(
            schedule.get('merge_sessions', List()).map((s) => {
              const deliverySymbol = s.getIn(['session', 'delivery_symbol'], '');
              const deliveryNumber = s.getIn(['session', 'delivery_no'], '');
              return `${deliverySymbol}${deliveryNumber}`;
            })
          )
          .join(', ');
      }
      const formattedStaffs = schedule
        .get('staffs', List())
        .map((staff) => {
          const staffName = staff.get('staff_name', Map());
          const first = staffName.get('first', '');
          const middle = staffName.get('middle', '');
          const last = staffName.get('last', '');
          return `${first} ${middle} ${last}`;
        })
        .join(', ');
      const formattedSubjects = schedule
        .get('subjects', List())
        .map((subject) => subject.get('subject_name', ''))
        .join(', ');
      let formattedStudentGroups =
        schedule.get('type', '') === 'regular'
          ? schedule
              .get('student_groups', List())
              .map((studentGroup) => {
                const groupName = getFormattedGroupName(
                  studentGroupRename(studentGroup.get('group_name', ''), programId),
                  isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                );
                const sessionGroups = studentGroup
                  .get('session_group', List())
                  .map((sessionGroup) =>
                    getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                  );
                return `${groupName} - ${sessionGroups.join(', ')}`;
              })
              .join(', ')
          : `${studentGroupViewList(schedule.get('student_groups', List()), programId)
              .entrySeq()
              .map(([groupName, sGroup]) => {
                return (
                  getFormattedGroupName(
                    studentGroupRename(groupName, session.get('_program_id', '')),
                    2
                  ) +
                  (sGroup.get('delivery_symbol', '') !== ''
                    ? `-${sGroup.get('session_group')}`
                    : '')
                );
              })
              .join(', ')}`;
      if (isMerged) {
        const mergedStudentGroups = schedule.get('merge_sessions', List()).map((s) =>
          s
            .get('student_groups', List())
            .map((studentGroup) => {
              const groupName = getFormattedGroupName(
                studentGroup.get('group_name', ''),
                isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
              );
              const sessionGroups = studentGroup
                .get('session_group', List())
                .map((sessionGroup) =>
                  getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                );
              return `${groupName} - ${sessionGroups.join(', ')}`;
            })
            .join(', ')
        );
        formattedStudentGroups =
          schedule.get('type', '') === 'regular'
            ? `${formattedStudentGroups}, ${mergedStudentGroups.join(', ')}`
            : `${studentGroupViewList(schedule.get('student_groups', List()), programId)
                .entrySeq()
                .map(([groupName, sGroup]) => {
                  return (
                    getFormattedGroupName(
                      studentGroupRename(groupName, session.get('_program_id', '')),
                      2
                    ) +
                    (sGroup.get('delivery_symbol', '') !== ''
                      ? `-${sGroup.get('session_group')}`
                      : '')
                  );
                })
                .join(', ')}`;
      }

      let startTime = '--';
      let endTime = '--';
      const sessionTimeDetail = schedule.get('sessionDetail', Map());
      if (!sessionTimeDetail.isEmpty()) {
        startTime = moment(sessionTimeDetail.get('start_time', new Date())).format('h:mm a');
        endTime = moment(sessionTimeDetail.get('stop_time', new Date())).format('h:mm a');
      }
      const scheduleStartTime = moment(schedule.get('scheduleStartDateAndTime')).format('h:mm a');
      const scheduleEndTime = moment(schedule.get('scheduleEndDateAndTime')).format('h:mm a');
      const scheduleDate = getTranslatedDuration(
        moment(schedule.get('scheduleStartDateAndTime')).format('Do MMM YY') +
          ', ' +
          scheduleStartTime +
          ' - ' +
          scheduleEndTime
      );

      const duration =
        getScheduleDuration('executed', schedule) +
        '/' +
        ' ' +
        getScheduleDuration('planned', schedule);
      return schedule.merge(
        Map({
          formattedDeliveryType: isMerged ? (
            <React.Fragment>
              <img src={MergeIcon} alt="merged" className="mr-1" /> {formattedDeliveryType}
            </React.Fragment>
          ) : (
            formattedDeliveryType
          ),
          formattedStudentGroups,
          formattedSubjects,
          formattedMode: capitalize(indVerRename(schedule.get('mode', ''), programId)),
          formattedStaffs,
          scheduleDate,
          startTime,
          endTime,
          duration,
          statusIcon: getScheduleStatusIcon(schedule),
        })
      );
    });
  }

  function getScheduleStatusIcon(schedule) {
    const status = schedule.get('isActive') ? schedule.get('status', '') : 'canceled';
    const statusIconNameMap = {
      completed: 'check-circle',
      canceled: 'times-circle',
      pending: 'exclamation-circle',
      missed: 'remove',
    };
    const statusIconColorMap = { completed: '#3BC10C', canceled: '#FF0000', pending: '#9E9E9E' };
    return (
      <Tooltip
        title={status === 'pending' ? 'Not Started' : capitalize(status)}
        placement="top"
        arrow
      >
        <span style={{ color: statusIconColorMap[status], fontSize: '1.25rem' }}>
          <i className={`fa fa-${statusIconNameMap[status]}`} />
        </span>
      </Tooltip>
    );
  }

  function getScheduleDuration(type, schedule) {
    let start = new Date(),
      end = new Date();
    if (type === 'planned') {
      start = getStartEndDate(schedule.get('start', Map()));
      end = getStartEndDate(schedule.get('end', Map()));
    } else {
      if (schedule.get('status', '') === 'completed') {
        start = new Date(schedule.getIn(['sessionDetail', 'start_time'], ''));
        end = new Date(schedule.getIn(['sessionDetail', 'stop_time'], ''));
      }
    }
    if (start && end) {
      return `${differenceInMinutes(end, start)} `;
    }
    return '---';
  }

  function getScheduleList() {
    const studentGroup = getActiveStudentGroup();
    return studentGroup.get('schedule_data', List());
  }

  function handleRowClick(rowData) {
    setModalData(rowData);
    setShow(true);
  }
  function handleClose() {
    setShow(false);
  }

  return (
    <div className="mt-3 mb-3 bg-white border-radious-8">
      <p className="font-weight-500 digi-black f-18 p-3">{t('reports_analytics.session_status')}</p>
      <div className="mt-3 d-flex align-items-center pl-3 pr-3">
        <p className="font-weight-500 digi-black f-18 mr-2 mb-0">
          {t('reports_analytics.student_group')}
        </p>
        <FormControl variant="outlined" size="small" className="w-100px">
          <Select
            native
            value={filters.get('studentGroup', '')}
            onChange={(e) => handleFilterChange('studentGroup', e.target.value)}
          >
            {getStudentGroupOptions().map((studentGroup) => (
              <option key={studentGroup.get('value')} value={studentGroup.get('value')}>
                {studentGroupRename(studentGroup.get('name'), programId)}
              </option>
            ))}
          </Select>
        </FormControl>
      </div>
      <InfoChart
        countInfoCardData={getCountInfoCardData()}
        chartData={getDeliveryTypeChartData()}
        reportView={true}
      />
      <CourseSessionStatusFilters
        options={getFilterOptions()}
        filters={filters}
        handleChange={setFilters}
        resetLabelName={t('reports_analytics.all')}
      />
      <TableWithPagination
        columns={getTableColumns()}
        data={filterTableData(prepareTableData(getScheduleList()))}
        handleRowClick={handleRowClick}
      />
      {show === true && (
        <SessionStatusModal
          show={show}
          data={modalData}
          CourseData={courseSessionStatus.get('course_details', List())}
          handleClose={handleClose}
          programId={programId}
        />
      )}
    </div>
  );
}

CourseSessionStatus.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  courseSessionStatus: PropTypes.instanceOf(Map),
  getCourseSessionStatusData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    courseSessionStatus: selectCourseSessionStatusData(state),
  };
};

export default connect(mapStateToProps, actions)(CourseSessionStatus);
