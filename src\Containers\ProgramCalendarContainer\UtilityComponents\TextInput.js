import React, { Fragment } from "react";
import { FlexWrapper, Label, Input } from "../Styled";

export default function TextInput({ title, place, edit, ...other }) {
  return (
    <Fragment>
      <FlexWrapper className="column" mg="20px 30px">
        <Label>{title}</Label>
        <Input
          type="text"
          placeholder={place}
          onChange={(e) => edit(e.target.value)}
          {...other}
        />
      </FlexWrapper>
    </Fragment>
  );
}
