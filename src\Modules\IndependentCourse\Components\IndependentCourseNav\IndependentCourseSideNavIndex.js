import React from 'react';
import PropTypes from 'prop-types';

import 'Assets/css/grouping.css';

import { OverViewTab, SessionTypesTab, CourseMasterTab } from './DirectNavOption';

function IndependentCourseSideNav({ active }) {
  return (
    <div className="pb-3">
      <OverViewTab ovActive={'overView'} />
      <SessionTypesTab sessionActive={'session-types'} />
      <CourseMasterTab ceActive={'course-master-list'} />
    </div>
  );
}

IndependentCourseSideNav.propTypes = {
  active: PropTypes.object,
};

export default IndependentCourseSideNav;
