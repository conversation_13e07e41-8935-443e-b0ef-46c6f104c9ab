import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import CompleteFrame from 'Assets/CompleteFrame.svg';
import CancelledFrame from 'Assets/CancelledFrame.svg';
import MissedFrame from 'Assets/MissedFrame.svg';
import Upcoming from 'Assets/Upcoming.svg';
import Inprogress from 'Assets/Inprograss.svg';
import NotStart from 'Assets/NotStart.svg';
import { Divider } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/session_tracking_report/action';
import { selectSingleSessionReport } from '_reduxapi/session_tracking_report/selectors';
import { List } from 'immutable';
import moment from 'moment';
import { getFormattedGroupName } from '../../../CourseScheduling/components/utils';
import {
  getVersionName,
  isIndGroup,
  jsUcfirstAll,
  studentGroupRename,
  studentGroupViewList,
} from 'utils';
import { getInfraName } from 'Modules/InstitutionReport/utils';

function SingleViewModal({
  show,
  handleSingleViewClose,
  statusData,
  getSingleSessionReport,
  singleData,
  imgData,
  sessionStatus,
}) {
  useEffect(
    () => {
      const params = {
        ...(statusData && { scheduleId: statusData }),
      };

      getSingleSessionReport(params);
    },
    // eslint-disable-next-line
    [statusData]
  );

  const timeDif = (actualTime, sessionTime) => {
    const time = moment(singleData.get(actualTime));
    const time2 = moment(singleData.getIn(['sessionDetail', sessionTime], ''));
    const startEndTime = moment(
      singleData.getIn(['sessionDetail', sessionTime], new Date())
    ).format('hh:mm:ss A');
    const diffTime = time2.diff(time, 'minutes');
    return !isNaN(Math.abs(diffTime)) ? `${startEndTime} (${Math.abs(diffTime)} Min)` : '';
  };

  const mergeData = () => {
    const mergedData = singleData
      .get('merge_with', List())
      .map((item) => {
        return `${item.getIn(['schedule_id', 'session', 'delivery_symbol'], '')}${item.getIn(
          ['schedule_id', 'session', 'delivery_no'],
          ''
        )}`;
      })
      .join(', ');
    return (
      singleData.getIn(['session', 'delivery_symbol'], ``) +
      singleData.getIn(['session', 'delivery_no'], ``) +
      ', ' +
      mergedData
    );
  };

  return (
    <MaterialDialog show={show} onClose={handleSingleViewClose} maxWidth={'xs'} fullWidth={true}>
      <div className="w-100 pl-4 pr-4 pt-3 pb-3">
        <IconButton
          aria-label="close"
          onClick={handleSingleViewClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
        <div className="d-flex justify-content-center align-items-center mb-3">
          <img
            src={
              imgData === 'completed'
                ? CompleteFrame
                : imgData === 'inprogress'
                ? Inprogress
                : imgData === 'missed'
                ? MissedFrame
                : imgData === 'cancelled'
                ? CancelledFrame
                : imgData === 'not started'
                ? NotStart
                : imgData === 'upcoming' && Upcoming
            }
            alt="StatusImage"
            className="w-40"
          />
        </div>

        <div>
          <label className="bold f-15">{singleData.get('program_name', '')}</label>
          <div className="d-flex">
            <div className="f-15 text-capitalize">{singleData.get('term', '')}</div>
            <Divider flexItem orientation="vertical" className="mr-2 ml-2 mt-1 mb-1" />
            <div className="f-15">Year : {singleData.get('year_no', '').replace('year', '')}</div>
            <Divider flexItem orientation="vertical" className="mr-2 ml-2 mt-1 mb-1" />
            <div className="f-15">
              Level : {singleData.get('level_no', '').replace('Level', '')}
            </div>
          </div>
          <div className="mt-2">
            <label className="f-15 bold">
              {singleData.get('course_code', '')} - {singleData.get('course_name', '')}
              {getVersionName(singleData)}
            </label>
            <div className="f-15">
              {singleData.get('merge_with', List()).size > 0 ? (
                mergeData()
              ) : (
                <>
                  {singleData.getIn(['session', 'delivery_symbol'], ``)}
                  {singleData.getIn(['session', 'delivery_no'], ``)}{' '}
                  {singleData.get('type', '') === 'regular'
                    ? singleData.getIn(['session', 'session_topic'], '')
                    : ''}
                </>
              )}
            </div>
          </div>
          <div className="">
            <label className="f-15 bold mb-0">Session Details</label>
            <div className="mt-2 d-flex justify-content-between">
              <div className="f-15">Session Category :</div>
              <div className="f-15 text-capitalize">{singleData.get('type', '')}</div>
            </div>
            <div className="mt-2 row">
              <div className="f-15 col-6">Student Group :</div>
              <div className="f-15 col-6 text-right">
                {/* {getFormattedGroupName()} */}
                {singleData.get('type', '') !== 'regular'
                  ? studentGroupViewList(
                      singleData.get('student_groups', List()),
                      singleData.get('_program_id', '')
                    )
                      .entrySeq()
                      .map(
                        ([groupName, sGroup]) =>
                          getFormattedGroupName(
                            studentGroupRename(groupName, singleData.get('_program_id', '')),
                            2
                          ) + `-${sGroup.get('session_group')}`
                      )
                      .join(', ')
                  : `${singleData
                      .get('student_groups', List())
                      .map((studentGroup) => {
                        const studentGroupName = getFormattedGroupName(
                          studentGroupRename(
                            studentGroup.get('group_name', ''),
                            singleData.get('_program_id', '')
                          ),
                          isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                        );
                        const sessionGroupNames = studentGroup
                          .get('session_group', List())
                          .map((sessionGroup) =>
                            getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                          )
                          .join(', ');
                        return `${studentGroupName} - ${sessionGroupNames}`;
                      })
                      .join(', ')}`}
              </div>
            </div>
            <div className="mt-2 d-flex justify-content-between">
              <div className="f-15">Scheduled Date :</div>
              <div className="f-15">
                {singleData.get('scheduleStartDateAndTime', '') &&
                  moment(singleData.get('scheduleStartDateAndTime')).format('Do MMM - YYYY')}
              </div>
            </div>
            <div className="mt-2 d-flex justify-content-between">
              <div className="f-15">Scheduled Time :</div>
              <div className="f-15">
                {singleData.get('scheduleStartDateAndTime', '') && (
                  <React.Fragment>
                    {moment(singleData.get('scheduleStartDateAndTime', '')).format('h:mm a')} -{' '}
                    {moment(singleData.get('scheduleEndDateAndTime', '')).format('h:mm a')}
                  </React.Fragment>
                )}
              </div>
            </div>
            <div className="mt-2 d-flex justify-content-between">
              <div className="f-15">Mode :</div>
              <div className="f-15 text-capitalize">{getInfraName(singleData)}</div>
            </div>

            <div className="mt-2 row">
              <div className="f-15 col-md-6">Staff :</div>
              <div className="f-15 col-md-6">
                <p className="mb-1 f-15 text-right">
                  {' '}
                  {singleData.get('staffs', List()).map((staff, s) => (
                    <div key={s}>
                      {jsUcfirstAll(
                        `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
                          ['staff_name', 'middle'],
                          ''
                        )} ${staff.getIn(['staff_name', 'last'], '')}`
                      )}
                    </div>
                  ))}
                </p>
              </div>
            </div>

            <div className="mt-2 d-flex justify-content-between">
              <div className="f-15">Infra :</div>
              <div className="f-15">{singleData.get('infra_name', '')}</div>
            </div>
            {sessionStatus.size > 0 && (
              <div className="mt-3">
                <label className="f-17 mb-1 bold">Remarks</label>
                {sessionStatus.map((item, index) => {
                  return (
                    <div key={index} className="mt-2 d-flex justify-content-between">
                      <div
                        className={`${
                          item === `Early Ended`
                            ? `color-earlyEnded`
                            : item === `Late Started`
                            ? `color-LateStarted`
                            : ` `
                        } f-15`}
                      >
                        {item}
                      </div>
                      <div className="f-15">
                        {item === `Early Ended` ? (
                          timeDif(`scheduleEndDateAndTime`, `stop_time`)
                        ) : item === `Late Started` ? (
                          timeDif(`scheduleStartDateAndTime`, `start_time`)
                        ) : item === `Merged` ? (
                          <React.Fragment>{mergeData()}</React.Fragment>
                        ) : (
                          ` `
                        )}{' '}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          {imgData === 'completed' || imgData === 'inprogress' ? (
            <div className="mt-2">
              <label className="f-17 mb-1 bold">Attendance Reports</label>
              <div className="mt-2 d-flex justify-content-between">
                <div className="f-15 text-primary">Total Strength :</div>
                <div className="f-15">{singleData.get('studentCount', '')}</div>
              </div>
              <div className="mt-1 d-flex justify-content-between">
                <div className="f-15 text-success">Present :</div>
                <div className="f-15">
                  (Auto - {singleData.getIn(['studentAttendanceReport', 'present', 'auto'])}),
                  (Manual - {singleData.getIn(['studentAttendanceReport', 'present', 'manual'])})
                </div>
              </div>
              <div className="mt-1 d-flex justify-content-between">
                <div className="f-15 text-danger">Absent :</div>
                <div className="f-15">
                  {singleData.getIn(['studentAttendanceReport', 'absent'])}
                </div>
              </div>
            </div>
          ) : (
            <div className="mt-2">
              <div className="mt-1 d-flex justify-content-between">
                <label className="f-15 bold mb-0">Student Strength :</label>
                <div className="f-15">{singleData.get('studentCount', '')}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </MaterialDialog>
  );
}

const mapStateToProps = function (state) {
  return {
    singleData: selectSingleSessionReport(state),
  };
};

SingleViewModal.propTypes = {
  show: PropTypes.bool,
  handleSingleViewClose: PropTypes.func,
  statusData: PropTypes.string,
  singleData: PropTypes.func,
  getSingleSessionReport: PropTypes.func,
  imgData: PropTypes.string,
  sessionStatus: PropTypes.instanceOf(List),
};

export default connect(mapStateToProps, actions)(SingleViewModal);
