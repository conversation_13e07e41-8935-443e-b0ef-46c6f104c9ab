import React from 'react';
import PropTypes from 'prop-types';
import { Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
// import CloseIcon from '@mui/icons-material/Close';
import { List, Map } from 'immutable';
import { getModalSubtitle } from './DeliveryGroupsModal';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
// const closeIconSx = {
//   position: 'absolute',
//   right: 8,
//   top: 11,
// };
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};

const ImportConfirmModal = ({
  open,
  data,
  importedList,
  handleClose,
  handleConfirm,
  isDeliveryGroups = false,
}) => {
  const levelWiseList = importedList.get('levels', List());
  const courseWiseList = importedList.get('courses', List());
  const levelLabels = levelWiseList.map((level) => level.get('label', '')).join(', ');
  const courseLabels = courseWiseList.map((course) => course.get('label', '')).join(', ');

  return (
    <Dialog open={open} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        {isDeliveryGroups ? 'Define Student Groups per Delivery' : 'Import Students'}
      </DialogTitle>
      {/* <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
        <CloseIcon />
      </IconButton> */}
      <DialogContent className="p-3">
        <p className="mb-2 gray-neutral">
          You’re {isDeliveryGroups ? 'defining delivery groups to' : 'importing through'}{' '}
          {getModalSubtitle(data)}, which will delete the previously{' '}
          {levelLabels && `level-wise imported students from ${levelLabels}`}
          {levelLabels && courseLabels && ' and '}
          {courseLabels && `course-wise imported students from ${courseLabels}`}.
        </p>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" sx={buttonSx} clicked={handleClose}>
          Cancel
        </MButton>
        <MButton variant="contained" color="primary" sx={buttonSx} clicked={handleConfirm}>
          Confirm
        </MButton>
      </DialogActions>
    </Dialog>
  );
};

ImportConfirmModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  importedList: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
  handleConfirm: PropTypes.func,
  isDeliveryGroups: PropTypes.bool,
};

export default ImportConfirmModal;
