import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Dropdown, Table } from 'react-bootstrap';
import { List, Map } from 'immutable';

import ListViewStudentGroups from './ListViewStudentGroups';

import { getFormattedGroupName } from '../utils';
import { capitalize, indVerRename, isIndGroup, studentGroupRename } from '../../../../utils';
import Tooltips from '../../../../_components/UI/Tooltip/Tooltip';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { t } from 'i18next';

function ManageScheduleListView({
  studentGroupStatus,
  isRotation,
  individualSessionDetails,
  handleAddEditSchedule,
  handleDeleteSchedule,
  manageTopics,
  currentCalendar,
  programId,
}) {
  const [expanded, setExpanded] = useState(Map());
  const [topicList, setTopicList] = useState(List());

  useEffect(() => {
    let topics = List();
    if (manageTopics && manageTopics.size > 0) {
      topics = manageTopics.reduce((acc, c) => {
        const tLists = c.get('topics', List());
        if (tLists.isEmpty()) {
          return acc;
        }
        return acc.concat(
          tLists.reduce((acc1, a) => {
            return acc1.push(Map({ _id: a.get('_id'), title: a.get('title') }));
          }, List())
        );
      }, List());
    }
    setTopicList(topics);
  }, [manageTopics]);

  function getTopicName(id) {
    if (topicList && topicList.size > 0) {
      return topicList
        .filter((item) => item.get('_id') === id)
        .reduce((_, el) => capitalize(el.get('title', '')), '');
    }
    return '';
  }
  return (
    <div className="min_h p-1">
      <Table responsive hover>
        <thead className="bg-white ">
          <tr>
            <th className="tb-250">{t('student_grouping.session_delivery_type')}</th>
            <th className="tb-100">{t('topic')}</th>
            <th colSpan="2">
              <div className="row">
                <div className="col-md-4">{t('reports_analytics.student_group')}</div>
                <div className="col-md-8">{t('configuration.subject')}</div>
              </div>
            </th>
            <th>{t('reports_analytics.mode')}</th>
            <th>{t('reports_analytics.infra')}</th>
            <th>{t('reports_analytics.staff')}</th>
            <th>{t('schedule')}</th>
          </tr>
        </thead>
        <tbody>
          {individualSessionDetails.isEmpty() ? (
            <tr>
              <td colSpan="10">
                <div className="mt-3 mb-3 text-center">{t('no_session_delivery_found')}</div>
              </td>
            </tr>
          ) : (
            individualSessionDetails.map((session) => (
              <React.Fragment key={session.get('session_id')}>
                <tr>
                  <td colSpan="8">
                    <b className="f-18">
                      {indVerRename(session.get('session_name', ''), programId)}
                    </b>
                  </td>
                </tr>
                {session.get('delivery', List()).size > 0 &&
                  session.get('delivery', List()).map((delivery, dIndex) => {
                    return (
                      <React.Fragment key={dIndex}>
                        <tr>
                          <td>
                            <div className="pt-1 max-width-300">
                              <i
                                className="fa fa-level-up fa-rotate-90 f-12 mr-2"
                                aria-hidden="true"
                              ></i>
                              {`${delivery.get('delivery_name', '')}`}
                            </div>
                          </td>
                          <td></td>
                          <td colSpan="2">
                            <ListViewStudentGroups
                              studentGroupStatus={studentGroupStatus}
                              isRotation={isRotation}
                              studentGroups={delivery
                                .get('student_group', List())
                                .reduce((sgAcc, sg) => sgAcc.set(sg.get('_id'), sg), Map())
                                .valueSeq()
                                .toList()}
                            />
                          </td>
                          <td colSpan="3">
                            <div className="pt-1"></div>
                          </td>
                          <td>
                            {studentGroupStatus && (
                              <div className="d-flex justify-content-between align-items-center">
                                {currentCalendar &&
                                  CheckPermission(
                                    'subTabs',
                                    'Schedule Management',
                                    'Course Scheduling',
                                    '',
                                    'Schedule',
                                    '',
                                    'Manage Course',
                                    'Session Default Settings Add'
                                  ) && (
                                    <i
                                      className="far fa-calendar-alt text-skyblue cursor-pointer mr-3 ml-3 f-16 calendar-icon"
                                      aria-hidden="true"
                                      onClick={() =>
                                        handleAddEditSchedule(
                                          delivery,
                                          session.get('session_id'),
                                          'create'
                                        )
                                      }
                                    ></i>
                                  )}
                                <i
                                  className={`fa fa-chevron-${
                                    expanded.get(delivery.get('delivery_id')) ? 'up' : 'down'
                                  }  ${!currentCalendar ? 'pl-4' : ''} text-skyblue cursor-pointer`}
                                  aria-hidden="true"
                                  onClick={() =>
                                    setExpanded(
                                      expanded.set(
                                        delivery.get('delivery_id'),
                                        !expanded.get(delivery.get('delivery_id'), false)
                                      )
                                    )
                                  }
                                ></i>
                              </div>
                            )}
                          </td>
                        </tr>

                        {expanded.get(delivery.get('delivery_id')) &&
                          delivery.get('settings', List()).isEmpty() && (
                            <tr>
                              <td colSpan="10">
                                <div className="mt-3 mb-3 text-center">No schedule found</div>
                              </td>
                            </tr>
                          )}
                        {expanded.get(delivery.get('delivery_id')) &&
                          delivery.get('settings', List()).map((setting) => {
                            let arrayList = [];
                            setting
                              .get('session', List())
                              .map((sessionGroup) => {
                                return sessionGroup
                                  .get('student_groups', List())
                                  .map((studentGroup) => {
                                    const studentGroupName = getFormattedGroupName(
                                      studentGroupRename(
                                        studentGroup.get('group_name', ''),
                                        programId
                                      ),
                                      isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                                    );
                                    const sessionGroupNames = studentGroup
                                      .get('session_group', List())
                                      .map((sessionGroup) =>
                                        getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                                      )
                                      .join(', ');
                                    const value = `${studentGroupName} - ${sessionGroupNames}`;
                                    arrayList.push(value);
                                    return value;
                                  })
                                  .toJS();
                              })
                              .toJS();

                            const deliveries = setting
                              .get('session', List())
                              .map(
                                (ses) =>
                                  `${ses.get('delivery_symbol', '')}${ses.get('delivery_no', '')}`
                              );

                            return (
                              <tr key={setting.get('_id')} className="bg-gray table-row-item">
                                <td>
                                  <div className="pt-1">
                                    <Tooltips title={deliveries.join(', ')} className="text-black">
                                      {deliveries.size > 5
                                        ? deliveries.slice(0, 5).join(', ') + '...'
                                        : deliveries.join(', ')}
                                    </Tooltips>
                                  </div>
                                </td>
                                <td>
                                  <div className="pt-1">
                                    {getTopicName(setting.get('_topic_id', ''))}
                                  </div>
                                </td>
                                <td colSpan="2">
                                  <div>
                                    <div className="row p-1">
                                      <div className="col-md-4">
                                        <p className="f-13 mb-0 pl-1">
                                          {arrayList
                                            .filter(
                                              (value, index, self) => self.indexOf(value) === index
                                            )
                                            .join(', ')}
                                        </p>
                                      </div>
                                      <div className="col-md-8">
                                        <div className="d-flex flex-wrap">
                                          <p className="f-13 mb-0">
                                            {setting
                                              .get('subjects', List())
                                              .map((subject) => subject.get('subject_name'))
                                              .join(', ') || 'NA'}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </td>
                                <td>
                                  <div className="pt-1">
                                    {capitalize(indVerRename(setting.get('mode', ''), programId))}
                                  </div>
                                </td>
                                <td>
                                  <div className="pt-1">{setting.get('infra_name', '')}</div>
                                </td>
                                <td>
                                  <div className="pt-1">
                                    {setting
                                      .get('staffs', List())
                                      .map(
                                        (staff) =>
                                          `${staff.getIn(
                                            ['staff_name', 'first'],
                                            ''
                                          )} ${staff.getIn(['staff_name', 'last'], '')}`
                                      )
                                      .join(', ')}
                                  </div>
                                </td>
                                <td>
                                  <div className="pt-1 d-flex justify-content-between table-action-button-container">
                                    {currentCalendar &&
                                      CheckPermission(
                                        'subTabs',
                                        'Schedule Management',
                                        'Course Scheduling',
                                        '',
                                        'Schedule',
                                        '',
                                        'Manage Course',
                                        'Session Default Settings Edit'
                                      ) && (
                                        <i
                                          className="fa fa-pencil cursor-pointer mt-1 ml-3"
                                          aria-hidden="true"
                                          onClick={() =>
                                            handleAddEditSchedule(
                                              delivery,
                                              session.get('session_id'),
                                              'update',
                                              setting
                                            )
                                          }
                                        ></i>
                                      )}
                                    {currentCalendar &&
                                      CheckPermission(
                                        'subTabs',
                                        'Schedule Management',
                                        'Course Scheduling',
                                        '',
                                        'Schedule',
                                        '',
                                        'Manage Course',
                                        'Session Default Settings Delete'
                                      ) && (
                                        <small className="f-18 mr-1">
                                          <Dropdown>
                                            <Dropdown.Toggle
                                              variant=""
                                              id="dropdown-table"
                                              className="table-dropdown"
                                              size="sm"
                                            >
                                              <div className="f-16">
                                                <i
                                                  className="fa fa-ellipsis-v"
                                                  aria-hidden="true"
                                                ></i>
                                              </div>
                                            </Dropdown.Toggle>
                                            <Dropdown.Menu renderOnMount={false}>
                                              <Dropdown.Item
                                                onClick={() =>
                                                  handleDeleteSchedule(
                                                    { setting },
                                                    false,
                                                    'schedule'
                                                  )
                                                }
                                              >
                                                Delete
                                              </Dropdown.Item>
                                            </Dropdown.Menu>
                                          </Dropdown>
                                        </small>
                                      )}
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                      </React.Fragment>
                    );
                  })}
              </React.Fragment>
            ))
          )}
        </tbody>
      </Table>
    </div>
  );
}

ManageScheduleListView.propTypes = {
  individualSessionDetails: PropTypes.instanceOf(List),
  handleAddEditSchedule: PropTypes.func,
  handleDeleteSchedule: PropTypes.func,
  studentGroupStatus: PropTypes.bool,
  isRotation: PropTypes.bool,
  manageTopics: PropTypes.instanceOf(List),
  currentCalendar: PropTypes.bool,
  programId: PropTypes.string,
};

export default ManageScheduleListView;
