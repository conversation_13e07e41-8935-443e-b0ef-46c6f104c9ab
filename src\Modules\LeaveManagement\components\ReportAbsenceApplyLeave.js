import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import DatePicker, { registerLocale } from 'react-datepicker';
import { Button } from 'react-bootstrap';
import { List, Map } from 'immutable';
import moment from 'moment';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import { startOfDay, endOfDay, isValid } from 'date-fns';
import StaffModal from '../modal/StaffModal';
import {
  selectScheduleStatus,
  selectStaffList,
  selectReviewerList,
  selectPermissionList,
} from '../../../_reduxapi/leave_management/selectors';
import { selectUserId, selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import Input from '../../../Widgets/FormElements/Input/Input';
import * as actions from '../../../_reduxapi/leave_management/actions';
import ApproveReportModal from '../modal/ApproveReportmodal';
import StaffSchedule from './StaffSchedule';
import { getScheduleListWithSubstituteStaff, getScheduleIdArray } from '../utils';
import { getLang } from 'utils';
import ar from 'date-fns/locale/ar';
import WithGlobalConfigDateHooks from 'Hoc/withGlobalConfigDateHooks';
registerLocale('ar', ar);
const lang = getLang();
const Leavetype = [
  ['Noticed', 'noticed'],
  ['Un-noticed', 'unnoticed'],
];
class ReportAbsenceApplyLeave extends Component {
  constructor(props) {
    super(props);
    this.state = {
      searchView: false,
      removalIcon: false,
      searchText: '',
      name: '',
      selectedId: 0,
      noticed: true,
      selectedname: Leavetype[0][1],
      selectStaffId: null,
      reason: '',
      startDate: '',
      endDate: '',
      selectedAttachment: null,
      selectedAttachmentUrl: null,
      substituteStaff: Map(),
      shouldFetchSessions: false,
      isSubstituteStaffPopulated: false,
      show: false,
      req: null,
      id: null,
      selectedAttachmentError: '',
      substituteStaffList: [],
    };
    this.timeoutStaff = 0;
  }

  componentDidMount() {
    this.props.setBreadCrumbName(t('leaveManagement.staff_report_absence_breadcrumb'));
    const { getAllPermissionList } = this.props;
    let search = window.location.search;
    let params = new URLSearchParams(search);
    var _id = params.get('_id');
    if (_id !== null) {
      this.props.reviewer('report_absence', 'staff', _id);
    }
    getAllPermissionList('staff');
    const id = this.props.match.params.id;
    if (id !== 1) {
      // this.setState({
      //   id: id,
      // });
      this.interval = setInterval(() => {
        const { reviewerList } = this.props;
        if (reviewerList && reviewerList.size > 0) {
          const list = reviewerList
            .filter((item) => item.get('_id') === id)
            .reduce((_, el) => el, Map());
          const name =
            list.getIn(['user_data', 'name', 'first'], '') +
            list.getIn(['user_data', 'name', 'middle'], '') +
            list.getIn(['user_data', 'name', 'last'], '');
          const startDate = moment(list.get('from', '')).format('DDMMMYYYY');
          const endDate = moment(list.get('to', '')).format('DDMMMYYYY');
          this.setState({
            id: id,
            selectStaffId: list.get('_user_id', null),
            name: name ? name : '',
            noticed: list.get('is_noticed', false),
            selectedname: list.get('is_noticed', false) === false ? 'unnoticed' : 'noticed',
            startDate: list.get('from', '') !== '' ? new Date(startDate) : '',
            endDate: list.get('to', '') !== '' ? new Date(endDate) : '',
            reason: list.get('reason', ''),
            selectedAttachment: list.get('_leave_reason_doc'),
            selectedAttachmentUrl: list.get('_leave_reason_doc_url', null),
          });
          clearInterval(this.interval);
        }
      }, 500);
    }
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  static getDerivedStateFromProps(props, state) {
    const { reviewerList, scheduleStatus, activeInstitutionCalendar } = props;
    const { id } = state;
    if (reviewerList.size === 0 || id === null) return null;
    const leave = reviewerList
      .filter((item) => item.get('_id') === id)
      .reduce((_, el) => el, Map());
    if (!state.shouldFetchSessions && !leave.isEmpty()) {
      const from = new Date(leave.get('from'));
      const to = new Date(leave.get('to'));
      if (!isValid(from) || !isValid(to)) return null;
      props.getScheduleStatus({
        institutionCalendarId: activeInstitutionCalendar.get('_id'),
        userId: leave.get('_user_id'),
        startDate: leave.get('from'),
        endDate: leave.get('to'),
      });
      return { shouldFetchSessions: true };
    }
    if (!state.isSubstituteStaffPopulated && scheduleStatus && !scheduleStatus.isEmpty()) {
      const substituteStaff = scheduleStatus.get('data', List()).reduce((acc, s) => {
        const scheduleId = s.get('_id');
        const assignedSubstituteStaff = s.get('assigned_substitute_staff', Map());
        if (assignedSubstituteStaff.isEmpty()) return acc;
        return acc.set(
          scheduleId,
          Map({
            schedule_id: scheduleId,
            substitute_staff: Map({
              name: assignedSubstituteStaff.get('substitute_staff_name'),
              _staff_id: assignedSubstituteStaff.get('_substitute_staff_id'),
            }),
          })
        );
      }, Map());
      return { substituteStaff, isSubstituteStaffPopulated: true };
    }
    return null;
  }

  getMinDate(type) {
    const { startDate } = this.state;
    if (type === 'startDate') return startOfDay(new Date());
    return startDate ? startOfDay(startDate) : startOfDay(new Date());
  }

  modalClose = () => {
    this.setState({
      searchView: !this.state.searchView,
      searchText: '',
      name: '',
      removalIcon: false,
      selectedId: 0,
    });
  };

  handleClickBack = () => {
    this.props.history.goBack();
  };

  modalOpen = () => {
    this.setState(
      {
        searchView: !this.state.searchView,
        removalIcon: true,
        searchText: '',
      },
      () => {
        this.props.getAllStaff('completed');
      }
    );
  };

  handleSelect = (e) => {
    if (e.target.value === 'noticed') {
      this.setState({ noticed: true, selectedname: e.target.value });
    } else if (e.target.value === 'unnoticed') {
      this.setState({ noticed: false, selectedname: e.target.value });
    }
  };

  removeIcon = () => {
    this.setState({
      name: '',
      removalIcon: false,
      selectedId: 0,
      searchText: '',
      startDate: '',
      endDate: '',
      substituteStaff: Map(),
    });
    this.props.setData(Map({ scheduleStatus: Map() }));
  };

  changeHandler = (value, id, name, staffid) => {
    if (value) {
      this.setState({
        selectedId: id,
        name: name,
        selectStaffId: staffid,
      });
    }
  };

  fetchScheduleStatus(startDate, endDate) {
    const { activeInstitutionCalendar, getScheduleStatus } = this.props;
    getScheduleStatus({
      institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
      userId: this.state.selectStaffId,
      startDate,
      endDate,
    });
  }

  handleChange(value, name) {
    if (name === 'startDate' && value) value = startOfDay(value);
    if (name === 'endDate' && value) value = endOfDay(value);
    this.setState(
      {
        [name]: value,

        ...(name === 'startDate' && { endDate: '', startTime: '', endTime: '' }),
      },
      () => {
        const { startDate, endDate } = this.state;
        if (name === 'startDate') this.props.setData(Map({ scheduleStatus: Map() }));
        if (startDate && endDate && name === 'endDate') {
          this.fetchScheduleStatus(startDate.toISOString(), endDate.toISOString());
        }
      }
    );
  }

  doSearchStaff = (evt) => {
    if (this.timeoutStaff) clearTimeout(this.timeoutStaff);
    this.timeoutStaff = setTimeout(() => {
      setTimeout(() => {
        this.props.getAllStaff('completed', this.state.searchText);
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState(
      {
        searchText: e.target.value,
      },
      () => this.doSearchStaff(e)
    );
  };

  getDates() {
    const DATES = [
      { type: 'startDate', label: t('leaveManagement.Start Date') },
      { type: 'endDate', label: t('leaveManagement.End Date') },
    ];
    return DATES;
  }

  handleFileChange(files) {
    if (!files) files = [];
    if (
      (files !== undefined && files[0].type === 'image/jpg') ||
      files[0].type === 'image/jpeg' ||
      files[0].type === 'image/png' ||
      files[0].type === 'application/pdf'
    ) {
      this.setState({
        selectedAttachment: files.length ? files[0] : '',
        selectedAttachmentError: '',
      });
    } else {
      this.setState({
        selectedAttachmentError: 'Image and pdf file only allowed',
      });
    }
  }

  handleRemoveAttachment() {
    this.setState({
      selectedAttachment: '',
    });
  }

  disableSubmit() {
    const { startDate, endDate, name, reason, selectedAttachment } = this.state;
    const { permissionlist } = this.props;

    if (
      name === '' ||
      (!selectedAttachment && permissionlist.get('report_absence_document')) ||
      startDate === '' ||
      reason === '' ||
      endDate === ''
    ) {
      return true;
    }

    return false;
  }

  apply = (status, flow) => {
    if (flow === 'rejected' || !status || this.isSubstituteStaffAssignedToSessions()) {
      const { scheduleStatus } = this.props;
      this.setState((state) => ({
        show: status,
        substituteStaffList: status
          ? !state.substituteStaff.isEmpty()
            ? getScheduleListWithSubstituteStaff(
                state.substituteStaff,
                scheduleStatus.get('data', List())
              )
                .valueSeq()
                .toList()
                .toJS()
            : getScheduleIdArray(scheduleStatus)
          : [],
      }));
    }
  };

  hasSchedule = () => {
    const { scheduleStatus } = this.props;
    const scheduleList = scheduleStatus.get('data', List());
    return !scheduleList.isEmpty();
  };

  handleSubstituteStaffChange(staffId, schedule) {
    const scheduleId = schedule.get('_id');
    if (!staffId) {
      this.setState((state) => {
        return { substituteStaff: state.substituteStaff.delete(scheduleId) };
      });
      return;
    }
    const staffs = schedule.get('substitute_staff', List());
    const selectedStaff = staffs.find((s) => s.get('_staff_id') === staffId);
    if (selectedStaff) {
      this.setState((state) => {
        return {
          substituteStaff: state.substituteStaff.set(
            scheduleId,
            Map({
              schedule_id: scheduleId,
              substitute_staff: selectedStaff,
            })
          ),
        };
      });
    }
  }

  isSubstituteStaffAssignedToSessions = () => {
    const { scheduleStatus } = this.props;
    const { substituteStaff } = this.state;

    let scheduleList = scheduleStatus.get('data', List());
    if (scheduleList.isEmpty()) return true;

    scheduleList = scheduleList.map((s) => {
      const scheduleId = s.get('_id');
      const isSubstituteStaffRequired =
        s.get('staffs', List()).filter((staff) => staff.get('status') === 'pending').size <= 1;
      const isSubstituteStaffSelected = isSubstituteStaffRequired
        ? !substituteStaff.get(scheduleId, Map()).isEmpty()
        : true;

      return s.merge(Map({ isSubstituteStaffRequired, isSubstituteStaffSelected }));
    });
    const scheduleWithoutSubstituteStaff = scheduleList
      .filter((s) => !s.get('isSubstituteStaffSelected'))
      .toList();
    if (scheduleWithoutSubstituteStaff.size) {
      this.props.resetMessage(t('leaveManagement.staff_required_sessions'));
    }

    return scheduleWithoutSubstituteStaff.size === 0;
  };

  render() {
    Leavetype[0][0] = t('leaveManagement.Noticed');
    Leavetype[1][0] = t('leaveManagement.Un_noticed');
    const StaffModalData = {
      searchView: this.state.searchView,
      searchText: this.state.searchText,
      changeHandler: this.changeHandler.bind(this),
      handleSearch: this.handleSearch.bind(this),
      staffList: this.props.staffList,
      modalOpen: this.modalOpen,
      modalClose: this.modalClose,
      selectedId: this.state.selectedId,
    };
    const { selectedAttachment, startDate, endDate, id } = this.state;
    const {
      permissionlist,
      activeInstitutionCalendar,
      scheduleStatus,
      isGlobalConfigDate,
    } = this.props;

    let parseStartDate = '';
    if (startDate !== '') {
      parseStartDate = Date.parse(startDate);
    }
    let parseEndDate = '';
    if (endDate !== '') {
      parseEndDate = Date.parse(endDate);
    }

    return (
      <div className="main pt-3 pb-5 ">
        <div className="container">
          {' '}
          <div className="p-2">
            <div className="d-flex justify-content-between">
              <div className="">
                <b className="pr-3" onClick={this.handleClickBack}>
                  {' '}
                  <i
                    className={`fa fa-arrow-${lang === 'ar' ? 'right' : 'left'} remove_hover  f-16`}
                    aria-hidden="true"
                  ></i>
                </b>
                <b className="mb-2 f-16"> {t('leaveManagement.Report_staff_absence')}</b>
              </div>
              <div className="">
                {(id === null || id === '1') && (
                  <Button
                    variant={this.disableSubmit() ? 'secondary' : 'primary'}
                    className="f-14"
                    disabled={this.disableSubmit()}
                    onClick={(e) => this.apply(true, 'report')}
                  >
                    {t('leaveManagement.report')}
                  </Button>
                )}
              </div>
            </div>
            {this.state.show && (
              <ApproveReportModal
                startDate={parseStartDate}
                endDate={parseEndDate}
                staffId={this.state.selectStaffId}
                isnoticed={this.state.noticed}
                reason={this.state.reason}
                attachement={this.state.selectedAttachment}
                closed={this.apply.bind(this)}
                id={this.state.id}
                name={this.state.name}
                componentname="reportAbsence"
                scheduleList={this.state.substituteStaffList}
              />
            )}
            <div className="pl-35 pt-3">
              <div className="row pb-2">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label>
                    {' '}
                    {t('leaveManagement.Select_the_staff')} <span className="text-red"> *</span>{' '}
                  </label>
                </div>
                <div className="col-md-3">
                  <div className="sb-example-2 mt--15 ">
                    <div className="search">
                      <input
                        type="text"
                        className="searchTerm"
                        placeholder={t('user_management.Search')}
                        value={this.state.name}
                        maxLength={10}
                      />
                      <button type="submit" className="searchButton">
                        {!this.state.removalIcon ? (
                          <i className="fa fa-search" onClick={() => this.modalOpen()}></i>
                        ) : (
                          <i className="fa fa-times" onClick={() => this.removeIcon()}></i>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {this.state.searchView && <StaffModal StaffModalData={StaffModalData} />}

              <div className="row pb-2 ">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <p className="pt-4 mb-0">
                    {' '}
                    {t('leaveManagement.selectLeave')} <span className="text-red"> *</span>{' '}
                  </p>
                </div>

                <div className="col-md-7 pt-4 d-flex">
                  <Input
                    elementType={'radio'}
                    elementConfig={Leavetype}
                    className={'form-radio1 radio1'}
                    selected={this.state.selectedname}
                    labelclass="radio-label2"
                    onChange={(e) => this.handleSelect(e)}
                    removeLabel={'label-remove1'}
                  />
                </div>
              </div>

              <div className="row pt-4">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-30">
                    {t('leaveManagement.dates_')} <span className="text-red"> *</span>
                  </label>
                </div>
                {this.getDates().map((d) => (
                  <div key={d.type} className="col-md-2">
                    <i className="fa fa-calendar-o leave_caleder" aria-hidden="true"></i>
                    <small className="d-flex">{d.label}</small>{' '}
                    <DatePicker
                      placeholderText={t('leaveManagement.Choose_date')}
                      {...(lang === 'ar' && { locale: 'ar' })}
                      //minDate={this.getMinDate(d.type)}
                      popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                      minDate={
                        d.type === 'endDate' && this.state.startDate !== ''
                          ? new Date(this.state.startDate)
                          : activeInstitutionCalendar.get('start_date', '') !== ''
                          ? new Date(activeInstitutionCalendar.get('start_date', ''))
                          : ''
                      }
                      maxDate={
                        activeInstitutionCalendar.get('end_date', '') !== ''
                          ? new Date(activeInstitutionCalendar.get('end_date', ''))
                          : ''
                      }
                      selected={this.state[d.type]}
                      onChange={(s) => this.handleChange(s, d.type)}
                      dateFormat="d MMM yyyy"
                      className="form-control customeDatepick"
                      showMonthDropdown
                      showYearDropdown
                      yearDropdownItemNumber={15}
                      filterDate={isGlobalConfigDate}
                    />
                  </div>
                ))}
              </div>

              <div className="row pt-2 mb-2">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-27">
                    {t('leaveManagement.Reason')} <span className="text-red"> *</span>
                  </label>
                </div>
                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    value={this.state.reason}
                    changed={(e) => this.setState({ reason: e.target.value })}
                    placeholder={t('leaveManagement.Reason')}
                    // feedback={this.state.yearError}
                  />
                </div>
              </div>

              <div className="row pt-5 mb-2">
                <div className={`col-md-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label className="mt-10">
                    {t('leaveManagement.attachment')}
                    {t('leaveManagement.s')}
                    {permissionlist.get('report_absence_document') && (
                      <span className="text-red"> *</span>
                    )}
                  </label>
                </div>
                <div className={`col-md-10 ${lang === 'ar' ? 'text-left' : ''}`}>
                  <p className="mb-0">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary browse-button import-padding border-radious-8"
                    >
                      {t('browse')}
                      <input
                        id="fileUpload"
                        type="file"
                        accept=".png,.pdf,.jpg,.jpeg"
                        onChange={(e) => this.handleFileChange(e.target.files)}
                      />
                    </label>
                    {!permissionlist.get('report_absence_document') && (
                      <span className="ml-2">{t('leaveManagement.optional')}</span>
                    )}
                  </p>
                  <p className="f-11 mb-0"> {t('leaveManagement.accepted_formet')} </p>

                  {selectedAttachment && typeof selectedAttachment === 'object' && (
                    <p className="f-11">
                      {selectedAttachment.name}
                      <b className="padding-left-5">
                        <i
                          className="fa fa-remove remove_hover f-16"
                          aria-hidden="true"
                          onClick={this.handleRemoveAttachment.bind(this)}
                        ></i>
                      </b>
                      {selectedAttachment.size > 2000000 && (
                        <b className="text-red">
                          <br />
                          {t('leaveManagement.Exceeds_2MB_limit')}.
                        </b>
                      )}
                    </p>
                  )}
                  {this.state.selectedAttachmentError && (
                    <p className="f-11">
                      <b className="text-red">
                        <br />
                        {this.state.selectedAttachmentError}
                      </b>
                    </p>
                  )}
                  {selectedAttachment && typeof selectedAttachment === 'string' && (
                    <>
                      {selectedAttachment !== 'null' && selectedAttachment !== 'undefined' && (
                        <p className="f-11">
                          <a
                            href={this.state.selectedAttachmentUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {this.state.selectedAttachment.split('/').slice(-1)}
                          </a>
                          <b className="padding-left-5">
                            <i
                              className="fa fa-remove remove_hover f-16"
                              aria-hidden="true"
                              onClick={this.handleRemoveAttachment.bind(this)}
                            ></i>
                          </b>
                        </p>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
          {this.hasSchedule() && this.state.endDate !== '' && (
            <div className="p-2">
              <StaffSchedule
                selectedSubstituteStaff={this.state.substituteStaff}
                scheduleStatus={scheduleStatus}
                handleSubstituteStaffChange={this.handleSubstituteStaffChange.bind(this)}
                isSubstituteStaffReadOnly={id === null || id === '1' ? false : true}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
}

const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    scheduleStatus: selectScheduleStatus(state),
    reviewerList: selectReviewerList(state),
    staffList: selectStaffList(state),
    userId: selectUserId(state),
    permissionlist: selectPermissionList(state),
  };
};

ReportAbsenceApplyLeave.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  scheduleStatus: PropTypes.instanceOf(Map),
  permissionlist: PropTypes.instanceOf(Map),
  reviewerList: PropTypes.instanceOf(List),
  staffList: PropTypes.instanceOf(List),
  getScheduleStatus: PropTypes.func,
  setBreadCrumbName: PropTypes.func,
  getAllPermissionList: PropTypes.func,
  reviewer: PropTypes.func,
  getAllStaff: PropTypes.func,
  setData: PropTypes.func,
  resetMessage: PropTypes.func,
  isGlobalConfigDate: PropTypes.func,
  match: PropTypes.object,
  history: PropTypes.object,
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions),
  WithGlobalConfigDateHooks
)(ReportAbsenceApplyLeave);
