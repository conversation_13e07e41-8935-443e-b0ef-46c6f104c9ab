import React, { useState, useEffect } from 'react';
import { List, Map } from 'immutable';
import PropTypes, { oneOfType } from 'prop-types';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';

import Input from '../../../../Widgets/FormElements/Input/Input';
import {
  jsUcfirst,
  getFormattedCourseDuration,
  indVerRename,
  levelRename,
  getTranslatedDuration,
  getEnvLabelChanged,
  jsUcfirstAll,
  getVersionName,
} from '../../../../utils';
import useWindowDimensions from '../../../../_components/UI/PageSize/PageSize';
import Tooltips from '../../../../_components/UI/Tooltip/Tooltip';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { getActiveLink } from '../../../ReportsAndAnalytics/utils';
import Loader from '../../../../Widgets/Loader/Loader';
import { preparePercentageNew } from '../Utils';
import { Trans } from 'react-i18next';
import { getLang } from '../../../../utils';
import { t } from 'i18next';

const lang = getLang();

export default function MonitoringCard({
  goToReports,
  loading,
  getMonitoringCourseList,
  roleId,
  institutionCalendarId,
  userId,
  monitoringCourses,
  getMonitoringCourseDetail,
  monitoringCourseDetail,
}) {
  const [search, setSearch] = useState('All');
  const [options, setOptions] = useState([]);
  var refId = 0;
  const [tabs, setTabs] = useState('Year / Level');
  const [showCourses, setShowCourses] = useState([]);
  var toggleObject = [];
  const [opened] = useState([]);
  const [departmentOpened] = useState([]);

  const [loader, setLoader] = useState('');

  const { height } = useWindowDimensions();
  const minHeight = height - 250;

  const configuredProgramLists = monitoringCourses.get('programCalendars', List());
  const departmentSubjectLists = monitoringCourses.get('departmentSubjects', List());
  MonitoringCard.propTypes = {
    configuredProgramLists: PropTypes.instanceOf(List),
    departmentSubjectLists: PropTypes.instanceOf(List),
    loading: PropTypes.object,
    goToReports: PropTypes.func,
    getMonitoringCourseList: PropTypes.func,
    roleId: PropTypes.string,
    institutionCalendarId: PropTypes.string,
    userId: PropTypes.string,
    monitoringCourses: oneOfType([PropTypes.instanceOf(Map), PropTypes.instanceOf(List)]),
    getMonitoringCourseDetail: PropTypes.func,
    monitoringCourseDetail: PropTypes.instanceOf(Map),
  };

  useEffect(() => {
    if (
      tabs === 'Year / Level' &&
      loader === '' &&
      toggleObject.length > 0 &&
      opened.length < 3 &&
      !opened.includes(opened.length + 1)
    ) {
      opened.push(opened.length + 1);
      if (toggleObject[opened.length - 1]?.course !== undefined) {
        toggleCourses(
          Map(toggleObject[opened.length - 1].course),
          Map(toggleObject[opened.length - 1].level)
        );
      }
    } else if (
      tabs !== 'Department / Subject' &&
      loader === '' &&
      toggleObject.length > 0 &&
      departmentOpened.length < 3 &&
      !departmentOpened.includes(departmentOpened.length + 1)
    ) {
      departmentOpened.push(departmentOpened.length + 1);
      if (toggleObject[opened.length - 1]?.course !== undefined) {
        toggleCourses(
          Map(toggleObject[departmentOpened.length - 1].course),
          Map(toggleObject[departmentOpened.length - 1].level)
        );
      }
    }
  }, [loader, tabs, toggleObject]); //eslint-disable-line

  useEffect(() => {
    const scroller = document.getElementById('roles_height');
    scroller.scrollTop = 0;
  }, [tabs]); // eslint-disable-line
  useEffect(() => {
    const scroller = document.getElementById('roles_height');
    scroller.scrollTop = 0;
    opened.splice(0, opened.length);
    departmentOpened.splice(0, departmentOpened.length);
    setLoader('');
  }, [search]); // eslint-disable-line

  useEffect(() => {
    if (institutionCalendarId !== '' && roleId !== '') {
      getMonitoringCourseList(userId, institutionCalendarId, roleId);
    }
  }, [roleId, userId, institutionCalendarId, getMonitoringCourseList]);

  useEffect(() => {
    let optionArray = [{ name: t('all'), value: 'All' }];
    if (configuredProgramLists && configuredProgramLists.size > 0) {
      configuredProgramLists.map((item) => {
        optionArray.push({ name: item.get('programName'), value: item.getIn(['_program_id', 0]) });
        return item;
      });
    }
    setOptions(optionArray);
  }, [configuredProgramLists]);

  const circularProgressBar = (data) => {
    const course = Map({
      completed_session: data.get('completed_session', 0),
      no_session: data.get('no_session', 0),
    });
    const value = preparePercentageNew(course);
    const color =
      course.get('completed_session', 0) === course.get('no_session', 0)
        ? '#3BC10C'
        : course.get('completed_session', 0) > 0 &&
          course.get('completed_session', 0) !== course.get('no_session', 0)
        ? '#4f95ef'
        : '';
    return (
      <div className="row mt-3">
        {course.get('no_session', 0) === 0 ? (
          <div className="col-md-12 scheduleProgress text-center">
            <Trans i18nKey={'dashboard_view.scheduling_in_progress'} />
          </div>
        ) : (
          <>
            <div className="col-md-4">
              <CircularProgressbar
                value={value}
                text={`${value}%`}
                styles={buildStyles({
                  pathColor: color,
                  textColor: color,
                })}
              />
            </div>

            <div className="col-md-8 align-self-center mt-4 mt-md-0">
              <p className="mb-0 f-14 text-lightblack">
                {course.get('completed_session', 0) === course.get('no_session', 0) &&
                course.get('no_session', 0) !== 0 ? (
                  <span style={{ color: '#3BC10C' }}>
                    <Trans i18nKey={'dashboard_view.course_completed'} />
                  </span>
                ) : course.get('completed_session', 0) > 0 &&
                  course.get('completed_session', 0) !== course.get('no_session', 0) ? (
                  <span>
                    <Trans i18nKey={'dashboard_view.session_complete'}></Trans>
                  </span>
                ) : (
                  <span style={{ color: '#ec5e85' }}>
                    <Trans i18nKey={'dashboard_view.course_not_started'} />
                  </span>
                )}
              </p>
              <p className="mb-0 f-15 bold">
                {course.get('completed_session', 0)} / {course.get('no_session', 0)}
              </p>
            </div>
          </>
        )}
      </div>
    );
  };
  function scrollHandle(event) {
    var middlePx = height / 2,
      tabId;
    tabs === 'Year / Level' ? (tabId = 'year') : (tabId = 'department');
    for (let i = 1; i <= refId; i++) {
      var position2 = document.getElementById(tabId + i).getBoundingClientRect().top;
      if (position2 >= middlePx - 80 && position2 <= middlePx + 180) {
        const selectedObject = toggleObject.filter((val) => val.refId === i)[0];
        const course = Map(selectedObject.course);
        const level = Map(selectedObject.level);
        const connector =
          course.get('_id') +
          level.get('term', '') +
          level.get('level_no', '') +
          course.get('rotation', '') +
          course.get('rotation_count', 0);
        if (
          loader === '' &&
          ((tabs === 'Year / Level' ? !opened.includes(i) : !departmentOpened.includes(i)) ||
            !showCourses.includes(connector))
        ) {
          //departmentOpened
          tabs === 'Year / Level' ? opened.push(i) : departmentOpened.push(i);
          toggleCourses(course, level);
        }
      }
    }
  }

  const getCoursesByType = (type, courses) => {
    return courses &&
      courses.filter((item) =>
        type === 'Admin Course'
          ? item.get('isConfigured') === true && item.get('AdminCourse') === true
          : item.get('isConfigured') === true && item.get('AdminCourse') === false
      ).size > 0 ? (
      <React.Fragment>
        <p className="bold mb-0 text-grey">
          <Trans
            i18nKey={
              type === 'Admin Course'
                ? 'dashboard_view.admin_course'
                : 'dashboard_view.participatory_course'
            }
          />
        </p>

        {courses.size > 0 &&
          courses
            .filter((item) =>
              type === 'Admin Course'
                ? item.get('isConfigured') === true && item.get('AdminCourse') === true
                : item.get('isConfigured') === true && item.get('AdminCourse') === false
            )
            .map((course, courseIndex) => {
              const levelArray = Map({
                _program_id: course.get('_program_id'),
                term: course.get('term'),
                level_no: course.get('level'),
                rotation_count: course.get('rotation_count', 0),
              });
              return (
                <div className="pb-3" key={courseIndex}>
                  {getCourseHtml(
                    course,
                    levelArray,
                    {
                      start_date: course.get('start_date'),
                      end_date: course.get('end_date'),
                    },
                    'department'
                  )}
                </div>
              );
            })}
      </React.Fragment>
    ) : (
      <></>
    );
  };

  const getProgramCourseVersion = (course) => {
    return (
      tabs === 'Department / Subject' && (
        <p className="mb-0 f-12 text-uppercase bold">
          {course.getIn(['administration', 'program_name'])} / {course.get('curriculum', '')} / Y
          {course.get('year', '').replace('year', '')} / {}{' '}
          {getEnvLabelChanged()
            ? levelRename(course.get('level', ''), course.get('_program_id'))
            : t('dashboard_view.level') + ' ' + course.get('level', '').replace('Level', '')}{' '}
          / {course.get('term', '')}
        </p>
      )
    );
  };

  function toggleCourses(course, level) {
    const courseId = course.get('_id');
    const connector =
      level.get('term', '') +
      level.get('level_no', '') +
      course.get('rotation', '') +
      course.get('rotation_count', 0);
    let updatedCourse = [...showCourses, courseId + connector];
    setLoader(courseId + connector);
    getMonitoringCourseDetail(
      userId,
      institutionCalendarId,
      roleId,
      course.get('_program_id'),
      courseId,
      level.get('term'),
      level.get('level_no'),
      course.get('rotation_count', 0),
      () => {
        setShowCourses(updatedCourse);
        setLoader('');
      }
    );
  }

  function printCourseShowMore(course, level) {
    const connector =
      userId +
      institutionCalendarId +
      roleId +
      course.get('_program_id') +
      course.get('_id') +
      level.get('term') +
      level.get('level_no') +
      course.get('rotation_count', 0);
    const data = monitoringCourseDetail.get(connector, Map());
    return (
      <div className="open-box-animations">
        {circularProgressBar(data)}
        {data.get('no_session', 0) !== 0 && (
          <div className="row m-2">
            <div className="mt-2">
              <span className="user_icon mr-3" style={{ background: '#FFFDED' }}>
                {' '}
                <i className="fa fa-user text-orange mr-3" aria-hidden="true"></i>
                <Trans i18nKey={'dashboard_view.final_warning'} /> -{' '}
                {data.get('final_warning', '0')}
              </span>
            </div>
            <div className="mt-2">
              <span className="user_icon  mr-3" style={{ background: '#FFEDED' }}>
                {' '}
                <i className="fa fa-user text-red mr-3" aria-hidden="true"></i>
                {jsUcfirstAll(data.get('denialLabel', 'Denial'))} - {data.get('denial', '0')}
              </span>
            </div>
          </div>
        )}
      </div>
    );
  }

  const getCourseHtml = (course, level, date, idName) => {
    const linkActiveUrl = getActiveLink(CheckPermission);
    const linkActive = linkActiveUrl !== '' && course.get('isAdmin', false);
    //CheckPermission('pages', 'Reports and Analytics', 'Course Details', 'Course View');
    refId++;
    let courseToJs = course.toJS();
    let levelToJs = level.toJS();
    toggleObject.push({ refId: refId, course: { ...courseToJs }, level: { ...levelToJs } });
    // if (!opened.includes(1)) {
    //   opened.push(1);
    //   toggleCourses(Map(toggleObject[0].course), Map(toggleObject[0].level));
    // }

    const connector =
      course.get('_id') +
      level.get('term', '') +
      level.get('level_no', '') +
      course.get('rotation', '') +
      course.get('rotation_count', 0);
    return (
      <div
        id={idName + refId}
        className={`position-relative dash_box ${linkActive ? 'remove_hover' : ''} `}
      >
        <div
          className={`${showCourses.includes(connector) ? 'border_bottom_dash' : ''}`}
          onClick={() => {
            if (linkActive) {
              goToReports(course, level, linkActiveUrl);
            }
          }}
        >
          {getProgramCourseVersion(course)}
          <p className="mb-1 f-15 bold">
            {' '}
            {course.get('courses_number', '')} - {course.get('courses_name', '')}
            {course.get('rotation', 'no') === 'yes' && ` - R${course.get('rotation_count', '')}`}
            {getVersionName(course)}
            {course.get('course_shared', false) && (
              <Tooltips
                title={`${t('dashboard_view.shared_from')} ${course.get(
                  'course_shared_program',
                  ''
                )}`}
                className="pl-2"
              >
                <span className="badge badge-light version_bg" style={{ padding: '5px 10px' }}>
                  S
                </span>
              </Tooltips>
            )}{' '}
          </p>
          <p className="mb-2 f-14 text-lightblack">
            {' '}
            {getEnvLabelChanged()
              ? jsUcfirst(indVerRename(course.get('model', ''), course.get('_program_id')))
              : t(
                  course.get('model', '') === 'standard'
                    ? 'constant.standard'
                    : 'constant.selective'
                )}{' '}
            | {getTranslatedDuration(getFormattedCourseDuration(course))}
          </p>
        </div>
        {showCourses.includes(connector) ? (
          <div
            onClick={() => {
              if (linkActive) {
                goToReports(course, linkActiveUrl);
              }
            }}
          >
            <React.Fragment>{printCourseShowMore(course, level)}</React.Fragment>
          </div>
        ) : (
          <div
            className={`c-middle${lang === 'ar' ? '-left' : ''} text-primary`}
            onClick={() => toggleCourses(course, level)}
          >
            <Tooltips title={t('dashboard_view.show_more')}>
              <i
                className={`fa fa-${loader === connector ? 'spinner card-visible' : 'card-hide'}`}
              ></i>
            </Tooltips>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="dash_box_col">
      <Loader pos="absolute" isLoading={loading.get('GET_MONITORING_COURSE_LIST')} />
      <div className="d-flex justify-content-between border-bottom dash_box_head">
        <div className="mt-2">
          <i className="fa fa-eye pr-2" aria-hidden="true"></i>
          <b>
            {' '}
            <Trans i18nKey={'dashboard_view.monitoring'}></Trans>{' '}
          </b>
        </div>
        <div className="mt--20 w-55">
          <Input
            elementType={'select'}
            elementConfig={{
              options: options,
            }}
            value={search}
            className={''}
            label={''}
            labelclass={'mt--20'}
            changed={(e) => {
              setLoader(true);
              setSearch(e.target.value);
            }}
          />
        </div>
      </div>

      <div className="bg-gray">
        <div className="d-flex pl-4 pr-4">
          <div className={`mt-3 mb-3 ${lang === 'ar' ? 'pl-3 ' : ''}`}>
            <div
              className={`remove_hover user_list mr-3 ${tabs === 'Year / Level' && 'active'}`}
              onClick={() => setTabs('Year / Level')}
            >
              <Trans
                i18nKey={'dashboard_view.year_level'}
                values={{ Level: indVerRename('Level') }}
              ></Trans>{' '}
            </div>
          </div>
          <div className={`mt-3 mb-3`}>
            <div
              className={`remove_hover user_list mr-3 ${
                tabs === 'Department / Subject' && 'active'
              }`}
              onClick={() => setTabs('Department / Subject')}
            >
              {' '}
              <Trans i18nKey={'dashboard_view.depart_subjcet'}></Trans>{' '}
              {/* <span className="ml-1 mr-1 alert_notification"> 02 </span> */}
            </div>
          </div>
        </div>
      </div>
      <div
        className="roles_height"
        id="roles_height"
        style={{ maxHeight: minHeight + 'px', minHeight: minHeight + 'px' }}
        onScroll={scrollHandle}
      >
        {((configuredProgramLists.size === 0 && tabs === 'Year / Level') ||
          (departmentSubjectLists.size === 0 && tabs !== 'Year / Level')) && (
          <div className="text-center mt-5">
            {loading?.get('GET_CONFIGURE_SETTINGS') ? (
              <Trans i18nKey={'fetching_data'}></Trans>
            ) : (
              <Trans i18nKey={'no_data'}></Trans>
            )}
          </div>
        )}
        {tabs === 'Year / Level' &&
          configuredProgramLists.size > 0 &&
          configuredProgramLists
            .filter((item) => (search !== 'All' ? item.getIn(['_program_id', 0]) === search : item))
            .map((program, pgIndex) => {
              let programCount = 0;
              return (
                <div key={pgIndex}>
                  {program
                    ?.get('level', List())
                    ?.filter((item) => item.get('isConfigured') === true)
                    ?.map((level, levelIndex) => {
                      programCount++;
                      return (
                        <div key={levelIndex} className="p-3">
                          {programCount === 1 ? (
                            <p className="bold mb-0"> {program.get('programName')} </p>
                          ) : (
                            ''
                          )}
                          <React.Fragment>
                            <p className="bold mb-0">
                              {' '}
                              {getEnvLabelChanged() ? (
                                <>
                                  <Trans i18nKey={'dashboard_view.year'}></Trans>{' '}
                                  {level?.get('year').replace('year', '')} /{' '}
                                  {levelRename(
                                    level?.get('level_no'),
                                    level?.get('_program_id', '')
                                  )}{' '}
                                </>
                              ) : (
                                <>
                                  {t('dashboard_view.year')} {level.get('year').replace('year', '')}{' '}
                                  / {t('dashboard_view.level')}{' '}
                                  {level.get('level_no').replace('Level', '')}
                                </>
                              )}
                            </p>
                            <p className="mb-3 f-15 text-lightblack">
                              {' '}
                              <Trans
                                i18nKey={'dashboard_view.term'}
                                values={{
                                  Term: indVerRename('Term', level.get('_program_id', '')),
                                }}
                              ></Trans>{' '}
                              - {level.get('term', 'regular')},{' '}
                              <Trans i18nKey={'dashboard_view.curriculum'}></Trans> -{' '}
                              {jsUcfirst(level.get('curriculum', ''))}{' '}
                            </p>
                          </React.Fragment>
                          {level
                            .get('course', List())
                            .filter((item) => item.get('isConfigured') === true)
                            .map((course, courseIndex) => {
                              return (
                                <div className="pb-3" key={courseIndex}>
                                  {getCourseHtml(
                                    course,
                                    level,
                                    {
                                      start_date: course.get('start_date'),
                                      end_date: course.get('end_date'),
                                    },
                                    'year'
                                  )}
                                </div>
                              );
                            })}
                        </div>
                      );
                    })}
                </div>
              );
            })}
        {tabs === 'Department / Subject' &&
          departmentSubjectLists.size > 0 &&
          departmentSubjectLists
            .filter((item) => (search !== 'All' ? item.get('program_id') === search : item))
            .map((program) => {
              let count = 0;
              return program
                .get('subject', List())
                .filter((item) => item.get('isConfigured') === true)
                .map((subject, subjectIndex) => {
                  count++;
                  return (
                    <div
                      key={subjectIndex}
                      className="p-3 text-left"
                      onMouseOverCapture={() => {
                        if (!departmentOpened.includes(1)) {
                          departmentOpened.push(1);
                          toggleCourses(Map(toggleObject[0].course), Map(toggleObject[0].level));
                        }
                      }}
                    >
                      {count === 1 ? (
                        <p className="bold mb-0"> {program.get('program_name')} </p>
                      ) : (
                        ''
                      )}
                      <p className="bold mb-0">
                        {' '}
                        {program.get('department_name')} / {subject.get('subject_name')}{' '}
                      </p>
                      {getCoursesByType('Admin Course', subject.get('courses', List()))}
                      {getCoursesByType('Participatory Course', subject.get('courses', List()))}
                    </div>
                  );
                });
            })}
      </div>
    </div>
  );
}
