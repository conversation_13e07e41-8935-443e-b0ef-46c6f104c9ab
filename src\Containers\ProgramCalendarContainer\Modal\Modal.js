import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import CurriculumModal from './Curriculum/CurriculumModal';
import CurriculumInterimModal from './Curriculum/Interim/CurriculumModal';
import EventsModal from './Events/EventsModal';
import EventsInterimModal from './Events/Interim/EventsModal';
import RepeatEventsModal from './RepeatEvents/RepeatEventsModal';
import CourseModal from './AddCourse/Interim/CourseModal';
import DeleteModal from './AddCourse/DeleteModal';
import CourseRegularModal from './AddCourse/Regular/CourseModal';
import { ModalWrapper, ModalBackgroundWrapper } from '../Styled';

const Modal = ({ show, content }) => {
  return (
    <Fragment>
      {show ? (
        <ModalWrapper>
          <ModalBackgroundWrapper>
            <Fragment>{content && content === 'curriculum' && <CurriculumModal />}</Fragment>
            <Fragment>
              {content && content === 'interimCurriculum' && <CurriculumInterimModal />}
            </Fragment>
            <Fragment>{content && content === 'events' && <EventsModal />}</Fragment>
            <Fragment>{content && content === 'interimEvents' && <EventsInterimModal />}</Fragment>

            <Fragment>{content && content === 'repeat_events' && <RepeatEventsModal />}</Fragment>
            <Fragment>{content && content === 'edit_course' && <DeleteModal />}</Fragment>
            <Fragment>
              {content && content === 'edit_course_regular' && <CourseRegularModal />}
            </Fragment>
            <Fragment>{content && content === 'edit_course_interim' && <CourseModal />}</Fragment>
          </ModalBackgroundWrapper>
        </ModalWrapper>
      ) : null}
    </Fragment>
  );
};

Modal.propTypes = {
  show: PropTypes.bool,
  content: PropTypes.string,
};

const mapStateToProps = ({ calender, interimCalendar }) => ({
  show: calender?.show_modal || interimCalendar?.show_modal,
  content: calender?.active_modal || interimCalendar?.active_modal,
});

export default connect(mapStateToProps)(Modal);
