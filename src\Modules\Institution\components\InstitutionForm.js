import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Button } from 'react-bootstrap';
import { connect } from 'react-redux';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import InputLabel from '@mui/material/InputLabel';

import * as actions from '../../../_reduxapi/institution/actions';
import {
  selectStateList,
  selectCityList,
  selectCountryList,
} from '../../../_reduxapi/institution/selectors';
import University_logo from '../../../Assets/university_req.svg';
import individualCollege from '../../../Assets/individualCollege.svg';
import camera from '../../../Assets/camera.svg';
import '../css/institution.css';

function InstitutionForm({
  institutionType: { type, name },
  institution,
  logo,
  handleChange,
  handleSubmit,
  handleBack,
  getCountryList,
  operation,
  stateList,
  cityList,
  countryList,
}) {
  const isGroup = type === 'group';
  const isCollege = type === 'individual';
  const [cID, setCID] = useState({ current: -1, previous: -1 });
  const [sID, setSID] = useState({ current: -1, previous: -1 });
  function getBrowseButton() {
    const hasNoLogo = !institution.get('logo') && !logo;
    return (
      <p className={hasNoLogo ? 'mb-0 pt-4 mt-3 text-center' : 'pt-3'}>
        <label
          htmlFor="logo-upload"
          //className="file-upload btn btn-outline-primary border-radious-8 f-14"
        >
          <div>{hasNoLogo && <img src={camera} alt="camera" />} </div>
          <div
            style={{
              color: '#147AFC',
              fontSize: '16px',
              cursor: 'pointer',
            }}
          >
            {hasNoLogo ? 'ADD PHOTO' : 'CHANGE'}
          </div>
          <input
            style={{ display: 'none' }}
            id="logo-upload"
            type="file"
            accept=".jpg,.jpeg,.png"
            onChange={(e) => handleChange(e.target.files, 'logo')}
          />
        </label>
      </p>
    );
  }

  function getLogoInfo() {
    if (!institution.get('logo') && !logo) {
      return (
        <>
          <p className="mb-0 pt-4 mt-3 bold f-14">Upload your Logo</p>
          <p className="f-10 text-gray">
            The file should be less than 1 MB and format can be JPG, JPEG, PNG
          </p>
        </>
      );
    }
    return (
      <>
        <p className="mb-0 pt-3 font-weight-500 f-14 text-black file-name">
          {`File Name: ${logo ? logo.name : institution.get('logo', '').split('/').slice(-1)}`}
        </p>
        {getBrowseButton()}
      </>
    );
  }

  function getCountries() {
    return countryList
      .map((c) =>
        Map({ name: c.get('name', ''), value: c.get('name', ''), countryID: c.get('id', '') })
      )
      .toJS();
  }

  function getStates() {
    return stateList
      .map((c) =>
        Map({ name: c.get('name', ''), value: c.get('name', ''), stateID: c.get('id', '') })
      )
      .toJS();
  }

  function getCities() {
    return cityList.map((c) => Map({ name: c.get('name', ''), value: c.get('name', '') })).toJS();
  }

  function getLogoUrl() {
    if (logo) {
      return URL.createObjectURL(logo);
    }
    return institution.get('logo');
  }

  function onSubmit() {
    const formData = new FormData();
    if (logo) {
      formData.append('logo', 'PJ4xm4KG2/delete.svg');
    }
    //formData.append('parent_institute', 'test1');
    formData.append('_country_id', '5f71f122cf2d1c9049d027fd');
    formData.append('language_preference', 'english');
    formData.append('timezone', 'GMT+5:30');
    formData.append('location', institution.getIn(['address_details', 'country'], 'country'));
    formData.append('address', 'address');
    formData.append('state', 'state');
    formData.append('stateId', 2);
    formData.append('countryId', 2);
    formData.append('country', 'dummy');
    formData.append('city', 'dummy');
    formData.append('district', 'dummy');
    formData.append('zipCode', 2321);
    formData.append('isUniversity', true);
    formData.append('name', institution.get('name'));
    formData.append('type', institution.get('type'));
    formData.append('code', institution.get('code'));
    formData.append('noOfColleges', 4);
    formData.append('accreditation', institution.get('accreditation'));
    /* Object.keys(institutionObject).forEach((k) => {
      if (k === 'logo') return;
      if (k === 'address_details') {
        Object.keys(institutionObject[k]).forEach((adk) => {
          formData.append(`address_details[${adk}]`, institutionObject.address_details[adk].trim());
        });
        return;
      }
      formData.append(k, institutionObject[k].trim());
    }); */
    handleSubmit({
      operation,
      formData,
      ...(operation !== 'create' && { _id: institution.get('_id') }),
    });
  }

  function disableSubmit() {
    const data = institution.toJS();
    let disabled = false;
    Object.keys(data).forEach((k) => {
      if (disabled) return;
      if (k === 'address_details' && data[k]) {
        Object.keys(data[k]).forEach((adk) => {
          if (disabled) return;
          const value = data.address_details[adk].trim();
          if (adk === 'district') return;
          if (['zipcode'].includes(adk) && !Number(value)) {
            disabled = true;
            return;
          }
          if (!value) disabled = true;
        });
        return;
      }
      if (['noOfColleges'].includes(k) && !Number(data[k])) {
        disabled = true;
        return;
      }
      /* if (!data[k].trim()) disabled = true; */
    });
    if (operation === 'create' && !logo) {
      disabled = true;
    }
    return false;
  }

  function isInDialog() {
    if (type === 'college') return true;
    return ['group', 'individual'].includes(type) && operation === 'update';
  }
  const getOptions = (name) => {
    if (name === 'state' && cID.previous !== cID.current) {
      setCID({ ...cID, previous: cID.current });
      getCountryList(
        'states?countryId=',
        'stateList',
        countryList.getIn([cID.current, 'id'], 0) - 1
      );
      return;
    }
    if (name === 'city' && sID.previous !== sID.current) {
      setCID({ ...sID, previous: sID.current });

      getCountryList('cities?stateId=', 'cityList', stateList.getIn([sID.current, 'id'], 0) - 1);
      return;
    }
    if (name === 'country' && !countryList.size) getCountryList();
  };
  return (
    <div className={!isInDialog() ? 'row justify-content-center pt-3' : ''}>
      <div className={!isInDialog() ? '' : ''}>
        <div className="outter-login add-university">
          <div className="d-flex p-2">
            <div className="f-17 pt-2 bold mr-auto">Add New {name || ''}</div>
            <Button variant="outline-primary" onClick={handleBack}>
              CANCEL
            </Button>
            <div className="ml-2">
              <Button
                variant={disableSubmit() ? 'secondary' : 'primary'}
                disabled={disableSubmit()}
                className="f-14 w-100px"
                onClick={onSubmit}
              >
                SAVE
              </Button>
            </div>
          </div>
          <div className="d-flex mt-4">
            <div>
              {logo || institution.get('logo') ? (
                <div className="logo_view">
                  <img
                    src={getLogoUrl()}
                    alt="logo"
                    className="img-fluid logo-img-border height_150px width_150px"
                  />
                </div>
              ) : (
                <div className="upload_logo">{getBrowseButton()}</div>
              )}
            </div>
            <div className="ml-3" style={{ width: '30%' }}>
              {getLogoInfo()}
            </div>
            <div className="text-right">
              <img
                src={isCollege ? individualCollege : University_logo}
                width="80%"
                height="80%"
                alt="universityLogo"
              />
            </div>
          </div>

          <div className="row pt-2">
            {!isCollege && (
              <div className="col-md-12">
                {/* <TextField
                  type="text"
                  variant="outlined"
                  size="small"
                  fullWidth
                  label={`${isGroup ? 'University' : 'Institution'} Name`}
                  value={institution.get('name', '')}
                  
                /> */}
                <div className="mt-3">
                  <label>{`${isGroup ? 'University' : 'Institution'} Name`}</label>
                  <TextField
                    type="text"
                    placeholder="Type Your University Name"
                    variant="outlined"
                    value={institution.get('name', '')}
                    size="small"
                    fullWidth
                    onChange={(e) => handleChange(e, 'name')}
                  />
                </div>
              </div>
            )}
            {!isCollege && (
              <div className="col-md-12 pt-3">
                <label>University Code</label>

                <TextField
                  type="text"
                  variant="outlined"
                  size="small"
                  fullWidth
                  value={institution.get('code', '')}
                  onChange={(e) => handleChange(e, 'code')}
                />
              </div>
            )}

            {isCollege && (
              <div className="col-md-12 pt-3">
                <label>College Name</label>
                <TextField
                  type="text"
                  variant="outlined"
                  size="small"
                  fullWidth
                  value={institution.get('college_name', '')}
                  onChange={(e) => handleChange(e, 'college_name')}
                />
              </div>
            )}
            <div className="col-md-12 pt-3">
              <label>Accreditation</label>
              <TextField
                type="text"
                variant="outlined"
                size="small"
                fullWidth
                value={institution.get('accreditation', '')}
                onChange={(e) => handleChange(e, 'accreditation')}
              />
            </div>
            {isGroup && (
              <div className="col-md-12 pt-3">
                <label>No of colleges under your university</label>
                <TextField
                  type="text"
                  variant="outlined"
                  size="small"
                  fullWidth
                  value={institution.get('noOfColleges', '')}
                  onChange={(e) => handleChange(e, 'noOfColleges')}
                />
              </div>
            )}

            <div className="col-md-12">
              <p className="pt-4 mb-0 bold f-14">Address Details</p>
            </div>
            <div className="col-md-12 pt-3">
              <label>Address Line 1</label>
              <TextField
                type="text"
                variant="outlined"
                size="small"
                fullWidth
                placeholder="Type Your Complete Address"
                value={institution.getIn(['address_details', 'address'], '')}
                onChange={(e) => handleChange(e, 'address')}
              />
            </div>
            <div className="col-md-6 pt-3">
              <label className="form-label">Country</label>
              <CustomSelect
                name="country"
                options={getCountries()}
                value={institution.getIn(['address_details', 'country'], '')}
                handleChange={handleChange}
                setID={setCID}
                getOptions={getOptions}
                previousID={cID.current}
              />
            </div>
            <div className="col-md-6 pr-0 pt-3">
              <label>State / Region</label>
              <CustomSelect
                name="state"
                options={getStates()}
                value={institution.getIn(['address_details', 'state'], '')}
                handleChange={handleChange}
                getOptions={getOptions}
                setID={setSID}
                previousID={sID.current}
              />
            </div>
            <div className="col-md-6 pt-3">
              <label>District</label>
              <TextField
                type="text"
                variant="outlined"
                size="small"
                fullWidth
                value={institution.getIn(['address_details', 'district'], '')}
                onChange={(e) => handleChange(e, 'district')}
              />
            </div>
            <div className="col-md-6 pr-0 pt-3">
              <label>City</label>
              <CustomSelect
                name="city"
                options={getCities()}
                value={institution.getIn(['address_details', 'city'], '')}
                handleChange={handleChange}
                getOptions={getOptions}
              />
            </div>
            <div className="col-md-6 pt-3">
              <label>ZIP Code</label>
              <TextField
                type="text"
                variant="outlined"
                size="small"
                fullWidth
                value={institution.getIn(['address_details', 'zipcode'], '')}
                onChange={(e) => handleChange(e, 'zipcode')}
              />
            </div>
          </div>

          <div
            className={`d-flex pt-3 ${
              operation === 'create' && !isCollege ? 'jc-space-between' : 'jc-flex-end'
            }`}
          ></div>
        </div>
      </div>
    </div>
  );
}

function CustomSelect({
  label,
  name,
  options,
  value,
  handleChange,
  setID = () => {},
  getOptions = () => {},
  previousID,
}) {
  return (
    <FormControl variant="outlined" size="small" fullWidth>
      <InputLabel htmlFor={`outlined-${name}-native-simple`}>{label}</InputLabel>
      <Select
        native
        value={value}
        onChange={(event) => {
          setID({ previous: previousID, current: event.target.selectedIndex });
          handleChange(event, name);
        }}
        label={label}
        inputProps={{
          name,
          id: `outlined-${name}-native-simple`,
        }}
        onClick={() => getOptions(name)}
      >
        <option aria-label="None" value="" />
        {options.map((o, i) => (
          <option key={`${o.value}-${i}`} value={o.value}>
            {o.name}
          </option>
        ))}
      </Select>
    </FormControl>
  );
}

InstitutionForm.propTypes = {
  institutionType: PropTypes.object,
  institution: PropTypes.instanceOf(Map),
  logo: PropTypes.instanceOf(File),
  handleChange: PropTypes.func,
  handleSubmit: PropTypes.func,
  handleBack: PropTypes.func,
  getCountryList: PropTypes.func,
  operation: PropTypes.string,
  stateList: PropTypes.instanceOf(Map),
  cityList: PropTypes.instanceOf(Map),
  countryList: PropTypes.instanceOf(Map),
};

CustomSelect.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string,
  options: PropTypes.array,
  value: PropTypes.string,
  handleChange: PropTypes.func,
  getOptions: PropTypes.func,
  previousID: PropTypes.number,
  setID: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    stateList: selectStateList(state),
    cityList: selectCityList(state),
    countryList: selectCountryList(state),
  };
};
export default connect(mapStateToProps, actions)(InstitutionForm);
