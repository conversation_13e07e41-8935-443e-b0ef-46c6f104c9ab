//import Config from '../config';
import { Map as IMap, List } from 'immutable';
import LocalStorageService from 'LocalStorageService';
export const createAction = (type, ...args) => {
  return function (...argsvalues) {
    let action = { type };
    args.forEach(function (key, index) {
      action[args[index]] = argsvalues[index];
    });
    return action;
  };
};

export const replaceVersion = (activeVersion = process.env.REACT_APP_ACTIVE_VERSION) =>
  process.env.REACT_APP_API_URL.replace('/v1', `/${activeVersion}`);

export const yearReplace = (item) => item.replace('year', 'Year ');

export const performanceTableBodyConstructing = (data) => {
  // for leaderBoard
  //in this function we are changing array of Objects to Object of object
  //constructing _id wise to reduce the loop count
  const constructedProgramData = {};
  const constructedUserData = {};
  const programData = data.programData;
  const userData = data.userData;
  const loopMaxSize = Math.max(programData.length, userData.length);

  for (let i = 0; i < loopMaxSize; i++) {
    if (programData[i] !== undefined)
      constructedProgramData[programData[i]['_id']] = programData[i];
    if (userData[i] !== undefined) constructedUserData[userData[i]['_id']] = userData[i];
  }
  data.constructedProgramData = constructedProgramData;
  data.constructedUserData = constructedUserData;
  return data;
};

const setSize_BasedOnCheckBoxStatus = (originSize, status) => {
  return status ? originSize : 0;
};

export const constructDataCourseWise = (programDetails, checked = false) => {
  // this is for q360 module
  return programDetails.map((program) => {
    let programBasedCourseCount = 0;
    return program
      .update('curriculum', IMap(), (curriculum) =>
        curriculum.map((curriculum) =>
          curriculum.update('years', IMap(), (years) =>
            years.map((year) => {
              programBasedCourseCount =
                programBasedCourseCount + year.get('courseIds', List()).size;
              return year
                .set('constructedCourseCount', year.get('courseIds', List()).size)
                .set(
                  'selectedCourseCount',
                  setSize_BasedOnCheckBoxStatus(year.get('courseIds', List()).size, checked)
                )
                .update('courseIds', List(), (courseIds) => {
                  let courseTypes = new IMap();
                  courseIds.forEach((course) => {
                    if (course.get('shared_from', false)) {
                      courseTypes = courseTypes.update('shared_with_others', List(), (data) =>
                        data.push(course.set('isChecked', checked))
                      );
                      return courseTypes;
                    }
                    if (course.get('course_type', '') === 'standard')
                      courseTypes = courseTypes.update('standard', List(), (data) =>
                        data.push(course.set('isChecked', checked))
                      );
                    else
                      courseTypes = courseTypes.update('selective', List(), (data) =>
                        data.push(course.set('isChecked', checked))
                      );
                  });
                  return courseTypes;
                });
            })
          )
        )
      )
      .set('constructedProgramCount', programBasedCourseCount)
      .set('selectedProgramCount', setSize_BasedOnCheckBoxStatus(programBasedCourseCount, checked));
  });
};

export const constructDataProgramWise = (
  programDetails,
  checked = false,
  programId,
  program_name
) => {
  let programMap = IMap();
  if (!programDetails.size) return;
  programMap = programMap
    .setIn([programId, 'selectedProgramCount'], checked ? programDetails.size : 0)
    .setIn([programId, 'constructedProgramCount'], programDetails.size)
    .setIn([programId, 'program_name'], programDetails.getIn([0, 'program_name'], ''));
  for (const cur of programDetails) {
    programMap = programMap.setIn(
      [programId, 'curriculum', cur.get('_id', '')],
      cur.set('isChecked', checked)
    );
  }
  return programMap;
};

export function getConcatCalendarList({ response }) {
  const calendarIdsArray = response.map((calendarElement) => calendarElement._id);
  const loginCalendarList = LocalStorageService.getCustomToken('staff_student_calendar_view');
  let responseParse = JSON.parse(loginCalendarList);
  if (responseParse && responseParse !== null) {
    const filteredCalendar = responseParse.institutionCalendarLists.filter(
      (calendarElement) => !calendarIdsArray.includes(calendarElement._id)
    );
    console.log(responseParse, 118);
    return response.concat(filteredCalendar);
  }
  return response;
}
