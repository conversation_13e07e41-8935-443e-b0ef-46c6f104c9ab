import { find_sun_to_sat } from '../../../_utils/function';
import moment from 'moment';
import { setMinutes, setHours } from 'date-fns';
const fromLocalStorage = (state, previous, payload) => {
  let initial_data = {
    ...state,
    ...previous,
    set_course_events: false,
    course_events: [],
  };

  let check_ids = previous.deleted_events.map((item) => item._event_id);
  let copy_events;

  if (check_ids.length === 0) {
    copy_events = previous.course_events;
  } else {
    copy_events = previous.course_events.filter((item) => !check_ids.includes(item._event_id));
  }

  initial_data.type.events = [...copy_events];

  return { ...initial_data };
};

const editLoad = (state, payload, data, year) => {
  const {
    modal_course_id,
    modal_course_type,
    modal_level,
    modal_level_column_name,
    modal_level_course_events,
    modal_level_end_date,
    modal_level_name,
    modal_level_number,
    modal_level_start_date,
    modal_rotational_number,
  } = data;
  let weeks = find_sun_to_sat(payload[modal_level_start_date], payload[modal_level_end_date]);
  let course_details, modal_course_index;

  if (modal_rotational_number === '') {
    modal_course_index = payload[modal_level].findIndex(
      (item) => item._course_id === modal_course_id
    );
    course_details = payload[modal_level][modal_course_index];
  } else {
    modal_course_index = payload[modal_level][modal_rotational_number][modal_course_type].findIndex(
      (item) => item._course_id === modal_course_id
    );
    course_details =
      payload[modal_level][modal_rotational_number][modal_course_type][modal_course_index];
  }

  return {
    ...state,
    update_method: 'edit',
    course_index: modal_course_index,
    disable_level: true,
    disable_title: true,
    loading: false,
    group_course: false,

    // group_course:
    //   course_details["_batch_course_id"]?.length !== 0 ? true : false,
    course_flow: modal_level === 'level_one_rotation_courses' ? 'rotation' : 'normal',
    title: modal_level === 'level_one_rotation_courses' ? 'level_one_courses' : modal_level,
    [year]: { ...payload },
    work_column: modal_level_column_name,
    work_course: modal_level,
    work_start_date: modal_level_start_date,
    work_end_date: modal_level_end_date,
    work_event: modal_level_course_events,
    work_min_date: payload[modal_level_start_date],
    work_max_date: payload[modal_level_end_date],
    work_level_number: modal_level_number,
    course_add_mode: 'manual',
    work_level_is_rotation: payload['level_one_is_rotation'] === 'yes' ? true : false,
    academic_weeks: [...weeks],
    copy_dates: [
      {
        courses_name: modal_level_name,
        start_date: payload[modal_level_start_date],
        end_date: payload[modal_level_end_date],
      },
      ...payload[modal_level],
    ],
    copy_events: [...payload[modal_level_course_events]],
    select_dates: 'choose_dates',
    custom_dates: 'dates',
    set_course_events: true,
    level_no: String(modal_level_name),
    type: {
      // title: payload[modal_level][modal_course_index]["title"],
      // sub_title: payload[modal_level][modal_course_index]["sub_title"],
      model: course_details['model'],
      _course_id: course_details['_course_id'],
      start_date: moment(course_details['start_date']).format('YYYY-MM-DD'),
      end_date: moment(course_details['end_date']).format('YYYY-MM-DD'),
      background_color: course_details['color_code'],
      //need to check
      events: course_details['courses_events'],
      _batch_course_id: course_details['_batch_course_id'],
      course_name: course_details['courses_name'],
      rotation_count: modal_rotational_number + 1,
      // _id: course_details["_id"],
      // .filter((item) =>
      //   payload[modal_level][modal_course_index]["_event_id"].includes(item._id)
      // ),
    },
    edit: {
      ...state.edit,
      ...payload,
    },
  };
};

const levelChoose = (state, name, payload, year) => {
  if (payload === 'level_one_courses') {
    let weeks = find_sun_to_sat(
      state[year]['level_one_start_date'],
      state[year]['level_one_end_date']
    );

    let copy_dates = [];
    let course_flow = 'normal';
    let work_course = '';
    if (state.edit.level_one_is_rotation === 'no') {
      copy_dates = [
        {
          courses_name: state[year]['level_one_title'],
          start_date: state[year]['level_one_start_date'],
          end_date: state[year]['level_one_end_date'],
        },
        ...state[year]['level_one_courses'],
      ];
      work_course = 'level_one_courses';
    } else {
      copy_dates = [
        {
          courses_name: state[year]['level_one_title'],
          start_date: state[year]['level_one_start_date'],
          end_date: state[year]['level_one_end_date'],
        },
        ...state[year]['level_one_rotation_courses'][0]['course'],
      ];
      course_flow = 'rotation';
      work_course = 'level_one_rotation_courses';
    }

    return {
      ...state,
      [name]: payload,
      work_column: 'level_one_columns',
      work_level_number: state[year]['level_one_no'],
      work_course,
      work_event: 'level_one_course_events',
      work_start_date: 'level_one_start_date',
      work_end_date: 'level_one_end_date',
      work_level_two_end_date: 'level_two_end_date',
      work_min_date: state[year]['level_one_start_date'],
      work_max_date: state[year]['level_one_end_date'],

      work_level_is_rotation: state[year]['level_one_is_rotation'] === 'yes' ? true : false,
      level_no: String(state[year]['level_one_title']),
      academic_weeks: [...weeks],
      copy_dates,
      copy_events: [...state[year]['level_one_course_events']],
      select_dates: '',
      course_flow,
      type: {
        ...state.type,
        events: [],
        start_date: '',
        end_date: '',
        week_start: -1,
        week_end: -1,
      },
      check_courses: false,
      add_courses: {
        course: null,
        elective: null,
        module: null,
      },
    };
  } else if (payload === 'level_two_courses') {
    let weeks = find_sun_to_sat(
      state[year]['level_two_start_date'],
      state[year]['level_two_end_date']
    );
    return {
      ...state,
      [name]: payload,
      work_column: 'level_two_columns',
      work_course: 'level_two_courses',
      work_event: 'level_two_course_events',
      work_start_date: 'level_two_start_date',
      work_end_date: 'level_two_end_date',
      work_min_date: state[year]['level_two_start_date'],
      work_max_date: state[year]['level_two_end_date'],
      work_level_number: state[year]['level_two_no'],
      work_level_is_rotation: state[year]['level_two_is_rotation'] === 'yes' ? true : false,
      level_no: String(state[year]['level_two_title']),
      academic_weeks: [...weeks],
      copy_dates: [
        {
          courses_name: state[year]['level_two_title'],
          start_date: state[year]['level_two_start_date'],
          end_date: state[year]['level_two_end_date'],
        },
        ...state[year]['level_two_courses'],
      ],
      copy_events: [...state[year]['level_two_course_events']],
      select_dates: '',
      type: {
        ...state.type,
        events: [],
        start_date: '',
        end_date: '',
        week_start: -1,
        week_end: -1,
      },
      check_courses: false,
      add_courses: {
        course: null,
        elective: null,
        module: null,
      },
    };
  }
};

const dateSelect = (state, name, payload) => {
  if (payload === 'same_dates') {
    return {
      ...state,
      [name]: payload,
      type: {
        ...state.type,
        start_date: state.copy_dates[state.copy_date_index]['start_date'],
        end_date: state.copy_dates[state.copy_date_index]['end_date'],
        events: [],
        week_start: 0,
        week_end: 0,
      },
      set_course_events: false,
      academic_week_end: 0,
      academic_week_end_end_date: '',
      academic_week_end_start_date: '',
      academic_week_start: 0,
      academic_week_start_end_date: '',
      academic_week_start_start_date: '',
    };
  } else if (payload === 'choose_dates') {
    return {
      ...state,
      [name]: payload,
      set_course_events: false,
      type: {
        ...state.type,
        start_date: '',
        end_date: '',
        events: [],
        week_start: 0,
        week_end: 0,
      },
      custom_dates: 'academic_week',
    };
  }
};

const syncEvent = (state) => {
  if (state.type.events.length !== 0) {
    let add = state.copy_events.filter((item) => {
      return Date.parse(item.event_date) >= Date.parse(state.type.start_date)
        ? Date.parse(item.end_date) <= Date.parse(state.type.end_date)
          ? true
          : false
        : false;
    });
    let modified = [...state.type.events];
    let check = modified.map((item) => item._id);
    let push_events = [];
    add.forEach((item) => {
      if (check.includes(item._id) === false) {
        push_events.push(item);
      }
    });
    return {
      ...state,
      type: {
        ...state.type,
        events: [...modified, ...push_events],
      },
    };
  } else {
    return {
      ...state,
      type: {
        ...state.type,
        events: state.copy_events.filter((item) => {
          return Date.parse(item.event_date) >= Date.parse(state.type.start_date)
            ? Date.parse(item.end_date) <= Date.parse(state.type.end_date)
              ? true
              : false
            : false;
        }),
      },
    };
  }
};

const eventSave = (state, payload) => {
  let data = {
    ...payload,
    event_name: {
      first_language: payload.title,
    },
    event_date: payload.start_date,
    start_time: payload.start_date + 'T' + payload.start_time + '00:000',
    end_time: payload.end_date + 'T' + payload.end_time + '00:000',
  };

  delete data.start_date;
  delete data.title;

  let modified_events = [];
  if (state.event_edit) {
    if (state.event_index === 0) {
      modified_events = [data, ...state.type.events.slice(state.event_index + 1)];
    } else if (state.type.events.length - 1 === state.event_index) {
      modified_events = [...state.type.events.slice(0, state.event_index), data];
    } else {
      modified_events = [
        ...state.type.events.slice(0, state.event_index),
        data,
        ...state.type.events.slice(state.event_index + 1),
      ];
    }
  } else {
    modified_events = [...state.type.events, data];
  }
  return {
    ...state,
    modal: false,
    modal_mode: '',
    event_edit: false,
    event_index: -1,
    modal_content: {
      description: '',
      event_type: 'exam',
      title: '',
      start_date: '',
      start_time: setHours(setMinutes(new Date(), 0), 8),
      end_date: '',
      end_time: setHours(setMinutes(new Date(), 0), 17),
    },
    type: {
      ...state.type,
      events: [...modified_events],
    },
  };
};

const eventCopyToDelete = (state, payload) => {
  let events = [...state.type.events];

  let separateEventIdFromEvents = [];
  if (events && events.length > 0) {
    separateEventIdFromEvents = events.map((list) => list._event_id);
  }

  let updatedEvents = [];
  if (payload && payload.length > 0) {
    updatedEvents = payload
      .filter((item) => {
        return separateEventIdFromEvents.includes(item._event_id) !== true;
      })
      .map((list) => list._event_id);
  }

  return {
    ...state,
    deleted_events: updatedEvents,
  };
};

const eventAttach = (state, payload) => {
  let deletedEvents = [...state.deleted_events];
  let updatedEvents = payload;
  if (deletedEvents && deletedEvents.length > 0) {
    updatedEvents = payload.filter((item) => {
      return deletedEvents.includes(item._event_id) !== true;
    });
  }
  return {
    ...state,
    set_course_events: true,
    type: {
      ...state.type,
      events: updatedEvents,
    },
  };
};

export const rootNonRotationalReducer = (state, action) => {
  const { type, payload, name, year, event, data, previous } = action;

  switch (type) {
    case 'FROM_LOCAL_STORAGE':
      return fromLocalStorage(state, previous, payload);
    case 'EDIT_EVENT':
      return {
        ...state,
        modal: true,
        event_edit: true,
        event_index: payload,
        modal_content: {
          ...state.modal_content,
          ...event,
          title:
            typeof event.event_name === 'object'
              ? event.event_name.first_language
              : event.event_name,
          start_date: moment(event.event_date).format('D MMM YYYY'),
          end_date: moment(event.end_date).format('D MMM YYYY'),
          start_time: event.start_time,
          end_time: event.end_time,
        },
      };
    case 'DELETE_EVENT':
      return {
        ...state,
        type: {
          ...state.type,
          events: state.type.events.filter((item, i) => i !== payload),
        },
        deleted_events: [
          ...state.deleted_events,
          ...state.type.events.filter(
            (item, i) =>
              i === payload && ![...state.deleted_events.map((item) => item._id)].includes(item._id)
          ),
        ],
      };
    case 'SAVE_EVENT':
      return eventSave(state, payload);
    case 'OFF_MODAL':
      return {
        ...state,
        modal: false,
        modal_mode: '',
        event_edit: false,
        event_index: -1,
        modal_content: {
          description: '',
          event_type: 'exam',
          title: '',
          start_date: '',
          start_time: setHours(setMinutes(new Date(), 0), 8),
          end_date: '',
          end_time: setHours(setMinutes(new Date(), 0), 17),
        },
      };
    case 'SHOW_MODAL':
      return {
        ...state,
        modal: true,
        modal_mode: payload,
      };
    case 'WEEK_END_CHANGE':
      return {
        ...state,
        [name]: payload,
        academic_week_end_start_date: state.academic_weeks[payload - 1]['start_date'],
        academic_week_end_end_date: state.academic_weeks[payload - 1]['end_date'],
        type: {
          ...state.type,
          end_date: state.academic_weeks[payload - 1]['end_date'],
          week_end: state.academic_weeks[payload - 1]['row_start'],
        },
      };
    case 'WEEK_START_CHANGE':
      return {
        ...state,
        [name]: payload,
        academic_week_start_start_date: state.academic_weeks[payload - 1]['start_date'],
        academic_week_start_end_date: state.academic_weeks[payload - 1]['end_date'],
        type: {
          ...state.type,
          start_date: state.academic_weeks[payload - 1]['start_date'],
          week_start: state.academic_weeks[payload - 1]['row_start'],
        },
      };
    case 'SELECT_CUSTOM_DATES':
      return {
        ...state,
        [name]: payload,
        academic_week_start: 0,
        academic_week_end: 0,
        set_course_events: false,
        type: {
          ...state.type,
          week_start: 0,
          week_end: 0,
          start_date: '',
          end_date: '',
          events: [],
        },
      };
    case 'EVENT_SYNC':
      return syncEvent(state);
    case 'LEVEL_SELECT':
      return levelChoose(state, name, payload, year);
    case 'SELECT_DATES':
      return dateSelect(state, name, payload);
    case 'COPY_PREVIOUS_DATES':
      return {
        ...state,
        [name]: payload,
        type: {
          ...state.type,
          start_date: state.copy_dates[payload]['start_date'],
          end_date: state.copy_dates[payload]['end_date'],
          events: [],
        },
      };
    case 'ON_TYPE':
      return {
        ...state,
        modal_content: {
          ...state.modal_content,
          [name]: payload,
          // event_type: "exam",
        },
      };
    case 'ON_CHANGE':
      return {
        ...state,
        type: {
          ...state.type,
          [name]: payload,
        },
      };
    case 'ON_TOGGLE':
      return {
        ...state,
        [name]: !state[name],
      };
    case 'INITIAL_LOAD_EDIT_COURSE':
      return editLoad(state, payload, data, year);
    case 'INITIAL_LOAD_ADD_COURSE':
      return {
        ...state,
        [year]: { ...payload },
        update_method: 'add',
        loading: false,
        edit: {
          ...state.edit,
          ...payload,
        },
      };
    case 'ADD_COURSES':
      return {
        ...state,
        add_courses: {
          ...state.add_courses,
          ...payload,
        },
        check_courses: true,
      };
    case 'ADD_BATCH_COURSES_ID':
      return {
        ...state,
        batch_courses_id: payload,
      };
    case 'SET_BATCH_COURSES_ID':
      return {
        ...state,
        type: {
          ...state.type,
          _batch_course_id: payload,
        },
      };
    case 'GET_COURSE_EVENTS':
      payload.filter((item) => {
        if (item.event_type === 'exam') {
          return true;
        } else {
          if (item._event_id === item._id) {
            return true;
          }
        }
        return false;

        // if (item.event_type === "holiday") {
        //   if (item._event_id === item._id) {
        //     return true;
        //   }
        //   return false;
        // }
        // return true;
      });
      return {
        ...state,
        course_events: payload,
        set_course_events: false,
      };
    case 'COPY_COURSE_EVENTS':
      return eventAttach(state, payload);
    // return {
    //   ...state,
    //   set_course_events: true,
    //   type: {
    //     ...state.type,
    //     events: payload,
    //   },
    // };
    case 'CHECK_PREVIOUS_STATE':
      return {
        ...state,
        check: {
          pre_start: state.type.start_date,
          pre_end: state.type.end_date,
        },
      };
    case 'ADD_COURSE_MANUAL':
      return {
        ...state,
        course_add_mode: 'manual',
        group_course: false,
        type: {
          ...state.type,
          rotation_count: 0,
          _batch_course_id: [],
        },
      };
    case 'ADD_COURSE_AUTO':
      return {
        ...state,
        course_add_mode: 'auto',
        type: {
          ...state.type,
          rotation_count: 1,
        },
      };
    case 'EVENT_COPY_TO_DELETE':
      return eventCopyToDelete(state, payload);
    default:
      return {
        ...state,
      };
  }
};
