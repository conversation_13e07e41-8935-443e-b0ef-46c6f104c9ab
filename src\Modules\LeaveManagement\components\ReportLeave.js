import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { <PERSON>, withRouter } from 'react-router-dom';
import * as actions from '../../../_reduxapi/leave_management/actions';
import moment from 'moment';

import { selectUserId, selectUserInfo } from '../../../_reduxapi/Common/Selectors';
import {
  selectLeaveCategories,
  selectPermissionList,
  selectLeave,
  selectInstitutionCalendar,
  selectScheduleStatus,
  selectActiveInstitutionCalendar,
  selectReviewerList,
} from '../../../_reduxapi/leave_management/selectors';

import { REVIEW_ROLES } from '../../../constants';

import { Dropdown, Button, Accordion, Card, Form, Modal, Badge, Table } from 'react-bootstrap';
import { addBusinessDays } from 'date-fns';
const TABS = ['Permission', 'Leave', 'On Duty'];
const LEAVE_TYPE = {
  0: 'permission',
  1: 'leave',
  2: 'on_duty',
};

class ReviewLeave extends Component {
  constructor(props) {
    super(props);
    this.state = {
      completeView: true,
      inactiveView: false,
      ondutyView: true,
      reviewed: false,
      rejected: false,
      pending: false,
      review: false,
      leaveList: [],
      activeTab: 1,
    };
  }

  inactiveTab = () => {
    this.setState({
      completeView: false,
      inactiveView: true,
      ondutyView: false,
    });
  };
  ondutyTab = () => {
    this.setState({
      completeView: false,
      inactiveView: false,
      ondutyView: true,
    });
  };
  completeTab = () => {
    this.setState({
      completeView: true,
      inactiveView: false,
      ondutyView: false,
    });
  };

  componentDidMount() {
    setTimeout(() => {
      const { activeinstitutionCalendar } = this.props;
      this.setState({ pending: true });
      const institution_id = activeinstitutionCalendar.get('_id');
      this.props.reviewer(LEAVE_TYPE[this.state.activeTab], institution_id, 'reviewer');
    }, 5000);
  }
  handlereview = (name) => {
    const { activeinstitutionCalendar } = this.props;
    if (name === 'reviewed') {
      this.setState({ reviewed: true, rejected: false, pending: false });
    }
    if (name === 'rejected') {
      this.setState({ rejected: true, reviewed: false, pending: false });
    }
    if (name === 'pending') {
      this.setState({ rejected: false, reviewed: false, pending: true });
    }

    const institution_id = activeinstitutionCalendar.get('_id');
    this.props.reviewer(LEAVE_TYPE[this.state.activeTab], institution_id, 'reviewer');
  };

  onTabChange(index) {
    this.setState(
      {
        activeTab: index,
      },
      () => {
        const institution_id = this.props.activeinstitutionCalendar.get('_id');
        this.props.reviewer(LEAVE_TYPE[this.state.activeTab], institution_id);
      }
    );
  }
  edit = (data, active) => {
    const datalist = data.toJS();
    const list = {
      ...datalist,
      activeTab: active,
      leave_flow: 'review',
    };

    this.props.setLeaveData(list);
  };
  render() {
    const { reviewerList } = this.props;
    const { pending, reviewed, rejected, activeTab } = this.state;
    const list = pending ? 'pending' : reviewed ? 'approved' : 'rejected';

    return (
      <>
        <div className="customize_tab">
          <ul id="menu">
            {TABS.map((tab, i) => (
              <span
                key={`${tab}-${i}`}
                onClick={this.onTabChange.bind(this, i)}
                className={`tabaligment${i === activeTab ? ' tabactive' : ''}`}
              >
                {tab}
              </span>
            ))}
          </ul>
        </div>

        <div className="main pt-3 pb-5 bg-white">
          <div className="container">
            <div className="row pb-3">
              <div className="col-md-5">
                <span className="pr-3" onClick={() => this.handlereview('pending')}>
                  <Badge
                    variant={
                      pending === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    Review Pending
                  </Badge>
                </span>
                <span className="pr-3" onClick={() => this.handlereview('reviewed')}>
                  <Badge
                    variant={
                      reviewed === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    Reviewed
                  </Badge>
                </span>
                <span className="pr-3" onClick={() => this.handlereview('rejected')}>
                  <Badge
                    variant={
                      rejected === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    Rejected
                  </Badge>
                </span>
              </div>

              <div className="col-md-7">
                <div className="row">
                  <div className="col-md-10">
                    <div className="sb-example-1">
                      <div className="search">
                        <input
                          type="text"
                          className="searchTerm"
                          placeholder="Search by employee id,name,.."
                        />
                        <button type="submit" className="searchButton">
                          <i className="fa fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-2">
                    <div className="float-right">
                      <Button variant="primary" className="f-14" onClick={this.apply}>
                        Export{' '}
                      </Button>{' '}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="row pt-4">
              <div className="col-md-12">
                <b className="f-15 ">List of all pending {LEAVE_TYPE[activeTab]} applications </b>

                <div className="leaveManage mb-2">
                  <table className="table">
                    <thead className="group_table_top">
                      <tr>
                        <th className="border_color_blue">
                          <div className="aw-50">
                            <b>S.NO </b>
                          </div>
                        </th>

                        <th className="border_color_blue">
                          <div className="aw-100">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className="fa fa-align-center remove_hover pr-2"
                                  aria-hidden="true"
                                ></i>
                                <b>Employee ID</b>
                              </li>
                            </div>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-100">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className="fa fa-align-center remove_hover pr-2"
                                  aria-hidden="true"
                                ></i>
                                <b>Employee Name</b>
                              </li>
                            </div>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-100">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className="fa fa-align-center remove_hover pr-2"
                                  aria-hidden="true"
                                ></i>
                                <b>Program</b>
                              </li>
                            </div>
                          </div>
                        </th>
                        {LEAVE_TYPE[activeTab] !== 'permission' && (
                          <>
                            <th className="border_color_blue">
                              <div className="aw-100">
                                <div id="icon_space">
                                  <li id="icon_space_li">
                                    {' '}
                                    <i
                                      className="fa fa-align-center remove_hover pr-2"
                                      aria-hidden="true"
                                    ></i>
                                    <b>From </b>
                                  </li>
                                </div>
                              </div>
                            </th>
                            <th className="border_color_blue">
                              <div className="aw-100">
                                <b className="">To</b>{' '}
                              </div>
                            </th>
                          </>
                        )}

                        <th className="border_color_blue">
                          <div className="aw-100">
                            <b className="">Duration</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-150">
                            <b className="">{LEAVE_TYPE[activeTab]} type</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Reason</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Salary</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Attachements</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Timestamp</b>
                          </div>
                        </th>
                      </tr>
                    </thead>

                    {reviewerList?.getIn([0, `${list}`])?.map((data, i) => {
                      return (
                        <tbody className="leaveManage-height">
                          <tr className="tr-bottom-border">
                            <td>
                              <div className="aw-50">
                                {' '}
                                <b className=""> {i + 1}</b>
                              </div>
                            </td>
                            <td>
                              <div className="aw-100">
                                {' '}
                                <b className=""> {data.getIn(['user_data', 0, 'user_id'])} </b>
                              </div>
                            </td>

                            <td>
                              <div className="aw-100">
                                {' '}
                                <b className="">
                                  {' '}
                                  {data.getIn(['user_data', 0, 'name', 'first'])}{' '}
                                </b>
                              </div>
                            </td>
                            <td>
                              <div className="aw-100">
                                {' '}
                                <b className="">
                                  {' '}
                                  {data.getIn(['user_data', 0, 'program', 'program_no'])}{' '}
                                </b>
                              </div>
                            </td>
                            {LEAVE_TYPE[activeTab] !== 'permission' && (
                              <>
                                <td>
                                  <div className="aw-100">
                                    {' '}
                                    <b className="">
                                      {' '}
                                      {moment(data.get('from')).format('DD MMM	YYYY')}
                                    </b>
                                  </div>
                                </td>
                                <td>
                                  {' '}
                                  <div className="aw-100">
                                    {' '}
                                    <b className="">
                                      {' '}
                                      {moment(data.get('to')).format('DD MMM	YYYY')}
                                    </b>
                                  </div>
                                </td>
                              </>
                            )}

                            <td>
                              {' '}
                              <div className="aw-100">
                                {' '}
                                <b className=""> {data.get('days')}days</b>
                              </div>
                            </td>
                            <td>
                              {' '}
                              <div className="aw-150">
                                {' '}
                                <b className=""> {data.getIn(['leave_category', 'name'])}</b>
                              </div>
                            </td>
                            <td>
                              {' '}
                              <div className="aw-200">
                                {' '}
                                <b className=""> {data.get('reason')}</b>
                              </div>
                            </td>
                            <td>
                              {' '}
                              <div className="aw-200">
                                {' '}
                                <b className=""> {data.get('payment_status')}</b>
                              </div>
                            </td>
                            <Link>
                              <td>
                                {' '}
                                <div className="aw-200">
                                  {' '}
                                  <b
                                    className=""
                                    onClick={() => {
                                      window.open(`${data.get('_leave_reason_doc')}`, '_blank');
                                    }}
                                  >
                                    {' '}
                                    View
                                  </b>
                                </div>
                              </td>
                            </Link>
                            <td>
                              {' '}
                              <div className="aw-200">
                                {' '}
                                <b className=""> {data.get('updatedAt')}</b>
                              </div>
                            </td>
                            <Link to="/leave-management/reviewapplication">
                              <td>
                                {' '}
                                <div
                                  className="aw-200"
                                  onClick={() => {
                                    this.edit(data, LEAVE_TYPE[activeTab]);
                                  }}
                                >
                                  {' '}
                                  <b className="">View</b>
                                </div>
                              </td>
                            </Link>
                          </tr>
                        </tbody>
                      );
                    })}
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    activeinstitutionCalendar: selectActiveInstitutionCalendar(state),
    reviewerList: selectReviewerList(state),
  };
};
export default compose(withRouter, connect(mapStateToProps, actions))(ReviewLeave);
