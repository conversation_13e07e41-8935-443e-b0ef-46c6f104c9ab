import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { Checkbox, FormControlLabel } from '@mui/material';
import Modal from '@mui/material/Modal';
import { useDispatch } from 'react-redux';

import ArchiveImg from 'Assets/archive.png';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Input from 'Widgets/FormElements/Input/Input';
import AutoCompleteSingle from 'Widgets/FormElements/material/Autocomplete';
import { IOSSwitch, iscomprehensiveMode, useFilteredOptions, useIsWarningValue } from '../utils';
import Autocomplete from './AutoComplete';
import HexColorPicker from './HexColorPicker';
import { capitalize } from 'utils';
import { useStylesFunction } from '../designUtils';
import MButton from 'Widgets/FormElements/material/Button';
import { resetDenialCondition } from '_reduxapi/leave_management/actions';

const WarningTypeDetails = ({
  warningTypes,
  setWarningTypes,
  activeIndex,
  getOptions,
  setData,
  setSearchKey,
  searchKey,
  warningMode,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [isSetTypeManual, setIsSetTypeManual] = useState(List());
  const { checkedDisabled } = useStylesFunction();
  const classes = useStylesFunction();

  const [open, setOpen] = useState(false);
  const [reset, setReset] = useState('');
  const handleOpenPopup = () => setOpen(true);
  const handleClosePopup = () => setOpen(false);
  const dispatch = useDispatch();
  const iscomprehensive = iscomprehensiveMode(warningMode);
  useEffect(() => {
    const editNotificationAuthority = [];
    warningTypes.map((data, warnIndex) => {
      if (data.getIn(['notificationToStudent', 'setType'], '') === 'manual') {
        return editNotificationAuthority.push(true);
      } else {
        return editNotificationAuthority.push(false);
      }
    });
    setIsSetTypeManual(fromJS(editNotificationAuthority));
  }, [warningTypes]); // eslint-disable-line

  const handleClick = (event, index) => {
    setAnchorEl(event.currentTarget);
    setCurrentIndex(index);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChange = (val, type) => {
    if (type === 'sendNotificationAuthority') {
      setWarningTypes(
        warningTypes.setIn(
          [activeIndex, 'notificationToStudent', 'sendNotificationAuthority'],
          fromJS(val)
        )
      );
    } else {
      setWarningTypes(warningTypes.setIn([activeIndex, type], fromJS(val)));
    }
  };

  const handleChangeDenial = (val, type) => {
    setWarningTypes(warningTypes.setIn([activeIndex, 'denialManagement', type], fromJS(val)));
  };

  const handleChangeType = (e, i, type, isCheckBox) => {
    const value = isCheckBox ? e.target.checked : type === 'colorCode' ? e : e.target.value;
    if (type === 'percentage') {
      if (value >= 100) setData(Map({ message: 'Percentage must be less than 100' }));
      const tempValue = value > 100 ? 100 : value;
      setWarningTypes(warningTypes.setIn([i, type], tempValue));
    } else if (type === 'warningValue') {
      setWarningTypes(warningTypes.setIn([i, type], value));
    } else {
      setWarningTypes(warningTypes.setIn([i, type], value));
    }
  };

  const handleChangePercentage = (e, type, catIndex) => {
    const value = e.target.value;

    if (value >= 100) setData(Map({ message: 'Percentage must be less than 100' }));
    const tempValue = value > 100 ? 100 : value;

    setWarningTypes(
      warningTypes.setIn([activeIndex, 'categoryWisePercentage', catIndex, type], tempValue)
    );
  };

  const handleDenialCondition = (e) => {
    const value = e.target.value;
    handleOpenPopup();
    setReset(value);
  };

  const handleReset = () => {
    const callback = () => {
      setWarningTypes(warningTypes.setIn([activeIndex, 'denialCondition'], reset));
      handleClosePopup();
    };
    dispatch(resetDenialCondition(callback));
  };

  const handleSendNotification = (e) => {
    setWarningTypes((prev) =>
      prev.setIn([activeIndex, 'notificationToStudent', 'isActive'], e.target.checked)
    );
  };

  const isDenial =
    warningTypes.getIn([activeIndex, 'typeName'], '') === warningTypes.size - 1 ||
    warningTypes.getIn([activeIndex, 'typeName'], '') === 'Denial';

  const filteredOptions = useFilteredOptions(iscomprehensive);
  const isWarningValue = useIsWarningValue(iscomprehensive);
  return (
    <React.Fragment>
      <div className="mt-1">
        <div className="d-flex justify-content-end">
          <div className="d-flex align-items-center">
            <div className="mt-1 mr-2">Warning Status</div>
            <div>
              <IOSSwitch
                checked={warningTypes.getIn([activeIndex, 'isActive'], false)}
                inputProps={{ 'aria-label': 'ant design' }}
                onChange={(e) => handleChangeType(e, activeIndex, 'isActive', true)}
              />
            </div>
          </div>
        </div>

        {isDenial && (
          <>
            <div className="mb-2">
              <div className="digi-light-gray">Denial Condition:</div>
              <div className="radio-adjustment">
                <Input
                  elementType={'radio'}
                  elementConfig={[
                    ['Cumulative', 'Cumulative'],
                    ['Individual', 'Individual'],
                  ]}
                  className={'form-radio1'}
                  labelclass="radio-label2"
                  disabled={true}
                  selected={warningTypes.getIn([activeIndex, 'denialCondition'], 'Cumulative')}
                  onChange={(e) => handleDenialCondition(e)}
                />
              </div>
            </div>

            {warningTypes.getIn([activeIndex, 'denialCondition'], '') === 'Individual' && (
              <>
                <div className="row mb-3">
                  {warningTypes
                    .getIn([activeIndex, 'categoryWisePercentage'], List())
                    .map((cat, catIndex) => (
                      <div className="col-md-6" key={catIndex}>
                        <div className="digi-light-gray mb-1 text-capitalize">
                          Set Absent % for {cat.get('categoryName', '')} (&gt;):
                        </div>
                        <div className="d-flex align-items-center">
                          <div className="mr-1">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'number'}
                              variant={'outlined'}
                              size={'small'}
                              value={warningTypes
                                .getIn(
                                  [activeIndex, 'categoryWisePercentage', catIndex, 'percentage'],
                                  0
                                )
                                ?.toString()}
                              changed={(e) => handleChangePercentage(e, 'percentage', catIndex)}
                              inputAdornment={'%'}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
                <div className="w-50 mb-3">
                  <div className="digi-light-gray mb-1">Unapplied leave will be considered as:</div>
                  <AutoCompleteSingle
                    placeholder={'Choose Leave Category'}
                    value={warningTypes.getIn([activeIndex, 'unappliedLeaveConsideredAs'], '')}
                    handleCloseee
                    isClearable={false}
                    options={warningTypes
                      .getIn([activeIndex, 'categoryWisePercentage'], List())
                      .map((opt) => capitalize(opt.get('categoryName', '')))
                      .toJS()}
                    onChange={(e, val) => {
                      setWarningTypes(
                        warningTypes.setIn([activeIndex, 'unappliedLeaveConsideredAs'], fromJS(val))
                      );
                    }}
                  />
                </div>
              </>
            )}
          </>
        )}

        <div className="d-flex align-items-top mb-3">
          <div className="mr-3">
            <div className="digi-light-gray mb-1">Label:</div>
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              value={warningTypes.getIn([activeIndex, 'labelName'], '')}
              changed={(e) => handleChangeType(e, activeIndex, 'labelName')}
            />
          </div>

          <div className="mr-3">
            <div className="digi-light-gray mb-1">
              {warningTypes.getIn([activeIndex, 'denialCondition'], '') === 'Individual' && 'Total'}
              {isWarningValue ? 'No of Sessions' : <>Absent Percentage (&gt;)</>}
            </div>
            <div className="d-flex align-items-center">
              <div className="w-50 mr-1">
                <MaterialInput
                  elementType={'materialInput'}
                  type={'number'}
                  variant={'outlined'}
                  size={'small'}
                  value={warningTypes.getIn([activeIndex, iscomprehensive], '')}
                  changed={(e) => handleChangeType(e, activeIndex, iscomprehensive)}
                  inputAdornment={iscomprehensive === 'percentage' ? '%' : ''}
                  disabled={true}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="mb-3">
          <div className="d-flex mb-2">
            <div>
              <div className="digi-light-gray mb-1">Warning Message (Optional):</div>
              <div className="">
                <MaterialInput
                  elementType={'materialTextArea'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  bgWhite={true}
                  placeholder={'Add text here...'}
                  value={warningTypes.getIn([activeIndex, 'message'], '')}
                  changed={(e) => handleChangeType(e, activeIndex, 'message')}
                  maxRows={3}
                />
              </div>
            </div>
            <div className="ml-3">
              <div className="digi-light-gray mb-1">Warning Color:</div>
              <div
                className="color-swatch"
                style={{ backgroundColor: warningTypes.getIn([activeIndex, 'colorCode'], '') }}
                onClick={(e) => handleClick(e, activeIndex)}
              />
              {currentIndex === activeIndex && (
                <HexColorPicker
                  anchorEl={anchorEl}
                  onClose={handleClose}
                  color={warningTypes.getIn([activeIndex, 'colorCode'], '')}
                  handleChange={handleChangeType}
                  index={activeIndex}
                  type={'colorCode'}
                />
              )}
            </div>
          </div>
        </div>

        {isDenial && (
          <div className="mb-3">
            <div className="digi-light-gray mb-1"> Denial Management</div>
            <div className="d-flex mb-2">
              <div>
                <div className="digi-light-gray">Access Type:</div>
                <div className="radio-adjustment">
                  <Input
                    elementType={'radio'}
                    elementConfig={[
                      ['Role', 'role'],
                      ['User', 'user'],
                    ]}
                    className={'form-radio1'}
                    labelclass="radio-label2"
                    selected={warningTypes.getIn(
                      [activeIndex, 'denialManagement', 'accessType'],
                      'role'
                    )}
                    onChange={(e) =>
                      setWarningTypes(
                        warningTypes.setIn(
                          [activeIndex, 'denialManagement', 'accessType'],
                          e.target.value
                        )
                      )
                    }
                  />
                </div>
              </div>
              <div className="digi-w-30 mt-3">
                {warningTypes.getIn([activeIndex, 'denialManagement', 'accessType'], '') !==
                'user' ? (
                  <Autocomplete
                    options={getOptions('role')}
                    handleChange={handleChangeDenial}
                    type={'roleIds'}
                    value={warningTypes.getIn([activeIndex, 'denialManagement', 'roleIds'], List())}
                    activeIndex={'role'}
                    setSearchKey={setSearchKey}
                    searchKey={searchKey}
                    name={'Role'}
                  />
                ) : (
                  <Autocomplete
                    options={getOptions('user')}
                    handleChange={handleChangeDenial}
                    type={'userIds'}
                    value={warningTypes.getIn([activeIndex, 'denialManagement', 'userIds'], List())}
                    activeIndex={'user'}
                    setSearchKey={setSearchKey}
                    searchKey={searchKey}
                    name={'User'}
                  />
                )}
              </div>
            </div>
          </div>
        )}

        {isWarningValue ? (
          <></>
        ) : (
          <div className="d-flex align-items-center">
            <div className="mr-2">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={warningTypes.getIn([activeIndex, 'notificationToStaff'], false)}
                    onChange={(e) => handleChangeType(e, activeIndex, 'notificationToStaff', true)}
                  />
                }
                label="Send Alert Notifications To Course Staffs"
              />
            </div>
          </div>
        )}

        <div className="mr-2">
          <FormControlLabel
            control={
              <Checkbox
                checked={warningTypes.getIn([activeIndex, 'isAdditionStaffNotify'], false)}
                onChange={(e) => {
                  handleChangeType(e, activeIndex, 'isAdditionStaffNotify', true);
                }}
              />
            }
            label="Which Additional Staff Gets Alert Notification About The Warning:"
          />
        </div>

        <div className="digi-w-30">
          <Autocomplete
            options={getOptions('role')}
            handleChange={handleChange}
            type={'notificationRoleIds'}
            value={warningTypes.getIn([activeIndex, 'notificationRoleIds'], List())}
            activeIndex={activeIndex}
            disabled={!warningTypes.getIn([activeIndex, 'isAdditionStaffNotify'], false)}
            name={'Role'}
          />
        </div>

        <div className="mr-2">
          <FormControlLabel
            control={
              <Checkbox
                checked={warningTypes.getIn(
                  [activeIndex, 'notificationToStudent', 'isActive'],
                  false
                )}
                onChange={(e) => handleSendNotification(e)}
              />
            }
            label="Send Alert Notifications To Students"
          />
        </div>
        {warningTypes.getIn([activeIndex, 'notificationToStudent', 'isActive'], false) && (
          <div className="d-flex mb-2">
            <div className="digi-light-gray mt-1 mr-2">Set Type:</div>
            <div className="radio-adjustment">
              <Input
                elementType={'radio'}
                elementConfig={filteredOptions}
                className={'form-radio1'}
                labelclass="radio-label2"
                selected={warningTypes.getIn(
                  [activeIndex, 'notificationToStudent', 'setType'],
                  'manual'
                )}
                onChange={(e) => {
                  const { value } = e.target;
                  value === 'manual'
                    ? setIsSetTypeManual(isSetTypeManual.set(activeIndex, true))
                    : setIsSetTypeManual(isSetTypeManual.set(activeIndex, false));
                  setWarningTypes(
                    warningTypes.setIn([activeIndex, 'notificationToStudent', 'setType'], value)
                  );
                }}
                // disabled={!warningTypes.getIn([activeIndex, 'notificationToStudent', 'isActive'])}
              />
            </div>
          </div>
        )}

        {warningTypes.getIn([activeIndex, 'notificationToStudent', 'isActive'], false) &&
          isSetTypeManual.get(activeIndex) && (
            <div className="d-flex">
              <div className="digi-light-gray mt-2">Set Who Can Send Notification:</div>
              <div className="digi-w-30 ml-2">
                <Autocomplete
                  options={getOptions('role')}
                  handleChange={handleChange}
                  type={'sendNotificationAuthority'}
                  value={warningTypes.getIn(
                    [activeIndex, 'notificationToStudent', 'sendNotificationAuthority'],
                    List()
                  )}
                  activeIndex={activeIndex}
                  name={'Role'}
                />
              </div>
            </div>
          )}

        <div className="d-flex align-items-top mb-2">
          <div className="mr-2">
            <FormControlLabel
              control={
                <Checkbox
                  checked={warningTypes.getIn([activeIndex, 'acknowledgeToStudent'], false)}
                  onChange={(e) => {
                    setWarningTypes(
                      warningTypes
                        .setIn([activeIndex, 'acknowledgeToStudent'], e.target.checked)
                        .setIn([activeIndex, 'markItMandatory'], false)
                    );
                    // handleChangeType(e, activeIndex, 'acknowledgeToStudent', true);
                  }}
                />
              }
              label="Acknowledge Warning To Students In Warning Alerts"
            />
          </div>
          <div className="mr-2">
            <FormControlLabel
              control={
                <Checkbox
                  checked={warningTypes.getIn([activeIndex, 'markItMandatory'], false)}
                  onChange={(e) => handleChangeType(e, activeIndex, 'markItMandatory', true)}
                  disabled={!warningTypes.getIn([activeIndex, 'acknowledgeToStudent'], false)}
                  className={`pl-0 ${
                    !warningTypes.getIn([activeIndex, 'acknowledgeToStudent'], false)
                      ? checkedDisabled
                      : ''
                  }`}
                />
              }
              label="Mark it Mandatory"
            />
          </div>
        </div>
        {isDenial && (
          <div className="mr-2">
            <div>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={warningTypes.getIn([activeIndex, 'restrictCourseAccess'], false)}
                    onChange={(e) => handleChangeType(e, activeIndex, 'restrictCourseAccess', true)}
                  />
                }
                label="Restrict Course Access"
              />
            </div>
            <div>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={warningTypes.getIn([activeIndex, 'adminRestrictedAttendance'], false)}
                    onChange={(e) =>
                      handleChangeType(e, activeIndex, 'adminRestrictedAttendance', true)
                    }
                    className={`${warningTypes.getIn(
                      [activeIndex, 'acknowledgeToStudent'],
                      false
                    )}`}
                  />
                }
                label="Admin Can Give Attendance in Restricted Schedules"
              />
            </div>
            <div>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={warningTypes.getIn(
                      [activeIndex, 'scheduleStaffRestrictedAttendance'],
                      false
                    )}
                    onChange={(e) =>
                      handleChangeType(e, activeIndex, 'scheduleStaffRestrictedAttendance', true)
                    }
                    className={`${warningTypes.getIn(
                      [activeIndex, 'acknowledgeToStudent'],
                      false
                    )}`}
                  />
                }
                label="Allow Schedule Staff To Give Attendance"
              />
            </div>
          </div>
        )}
        <div>
          <Modal open={open} onClose={handleClosePopup}>
            <div className={classes.lmsIndCummDeletePopup}>
              <div className="m-2">
                <div className="d-flex mt-3">
                  <div>
                    <img alt={'Archive'} src={ArchiveImg} />
                  </div>
                  <h5 className="pt-1 pl-2">Denial Condition Setting Changes</h5>
                </div>
                <div className="m-2">
                  Changing the setting from cumulative to individual will result in a reset of all
                  modified denial percentages for the active Academic Years.
                </div>
                <div className="my-3 d-flex justify-content-end">
                  <div>
                    <MButton variant="outlined" clicked={handleClosePopup} color="gray">
                      Cancel
                    </MButton>
                  </div>
                  <div>
                    <MButton variant="contained" clicked={handleReset} color="red" className="ml-3">
                      Continue
                    </MButton>
                  </div>
                </div>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </React.Fragment>
  );
};

WarningTypeDetails.propTypes = {
  warningTypes: PropTypes.instanceOf(List),
  setWarningTypes: PropTypes.func,
  activeIndex: PropTypes.number,
  getOptions: PropTypes.func,
  setData: PropTypes.func,
  setSearchKey: PropTypes.func,
  searchKey: PropTypes.string,
  warningMode: PropTypes.string,
};

export default WarningTypeDetails;
