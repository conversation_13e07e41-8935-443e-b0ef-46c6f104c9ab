import React, { useState } from 'react';
import PropTypes from 'prop-types';

import FormControlLabel from '@mui/material/FormControlLabel';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControl from '@mui/material/FormControl';

import { fromJS, List, Map } from 'immutable';

export default function EditStudent({
  show,
  cancel,
  editData,
  index,
  data,
  setData,
  toastFunc,
  studentIds,
  setList,
  list,
}) {
  const [studentData, setStudentData] = useState(
    fromJS({
      name: editData.getIn(['student', 'name'], ''),
      studentId: editData.getIn(['student', 'studentId'], ''),
      gender: editData.getIn(['student', 'gender'], ''),
      isDefault: false,
      attendance: true,
      studentMarks: editData.getIn(['student', 'studentMarks'], List()),
    })
  );
  const [edited, setEdited] = useState(false);
  const handleChange = (field, value) => {
    setEdited(true);
    if (field === 'studentId') {
      setStudentData(studentData.set(field, value));
      if (studentIds.includes(value)) {
        setEdited(false);
      }
    } else {
      setStudentData(studentData.set(field, value));
    }
  };
  const handleSave = () => {
    setData(data.setIn(['studentDetails', index], studentData));
    setList(list.setIn(['studentData', index], studentData));
    toastFunc(Map({ message: 'Updated Successfully' }));
    cancel();
  };
  return (
    <MaterialDialog show={show} onClose={cancel} maxWidth={'xs'} fullWidth={true}>
      <div className="w-100 p-4">
        <p className="mb-3 pb-2 border-bottom bold f-19"> Edit Student</p>
        <div className="mt-2 mb-2">
          <MaterialInput
            elementType={'materialInput'}
            type={'text'}
            variant={'outlined'}
            size={'small'}
            label={'Student Name'}
            placeholder={'Name'}
            labelclass={'mb-0 f-14'}
            value={studentData.get('name', '')}
            changed={(e) => handleChange('name', e.target.value)}
          />
        </div>
        <div className="mt-2 mb-2">
          <MaterialInput
            elementType={'materialInput'}
            type={'text'}
            variant={'outlined'}
            size={'small'}
            label={'Student ID'}
            placeholder={'ID'}
            labelclass={'mb-0 f-14'}
            value={studentData.get('studentId', '')}
            changed={(e) => handleChange('studentId', e.target.value, index)}
          />
        </div>

        <div className="mt-2 mb-2">
          <p className="bold mb-1 f-15">Gender</p>
          <div className="d-flex">
            <FormControl component="fieldset">
              <RadioGroup
                row
                aria-label="position"
                name="position"
                value={studentData.get('gender', '')}
                onChange={(e) => handleChange('gender', e.target.value, index)}
              >
                <FormControlLabel
                  value="female"
                  control={<Radio color="primary" />}
                  label={'Female'}
                />
                <FormControlLabel value="male" control={<Radio color="primary" />} label={'Male'} />
              </RadioGroup>
            </FormControl>
          </div>
        </div>
        <div className="d-flex justify-content-end border-top pt-3">
          <MButton variant="outlined" color="primary" className={'mr-2'} clicked={cancel}>
            Cancel
          </MButton>
          <MButton
            variant="contained"
            color="primary"
            clicked={handleSave}
            disabled={!edited || !(studentData.get('name', '') && studentData.get('studentId', ''))}
          >
            Save
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
}
EditStudent.propTypes = {
  show: PropTypes.bool,
  cancel: PropTypes.func,
  data: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  toastFunc: PropTypes.func,
  editData: PropTypes.instanceOf(Map),
  index: PropTypes.number,
  studentIds: PropTypes.instanceOf(List),
  list: PropTypes.instanceOf(Map),
  setList: PropTypes.func,
};
