import { jsPDF } from 'jspdf';
import { List, Map, fromJS } from 'immutable';
import { AlphabetsArray, capitalize, getCollegeLogo } from 'utils';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

export const exportExcel = async (DownloadData, splittingHeading, fileName) => {
  const workbook = new ExcelJS.Workbook();
  let worksheet = workbook.addWorksheet('My Sheet', {
    views: [{ state: 'frozen', ySplit: 8 }],
  });
  const header = DownloadData?.header[0];

  const headerWithKeys = header.map((head) => ({ header: head, key: head, width: 30 }));
  worksheet.columns = headerWithKeys;

  const emptyRows = [];
  const emptyRows1 = [];
  DownloadData.bodyData.map((body, firstIndex) => {
    let object = {};
    let newObj = {};
    // body structure [
    // {name:'ddd',age:45,gender:'male'}
    // {name:'ddddd',age:455,gender:'male'}
    // {name:'ddddd',age:455,gender:'female'}
    //]
    body.map((eachData, index) => {
      object[header[index]] = eachData;
      return '';
    });
    if (firstIndex === 0) {
      body.map((eachData, index) => {
        newObj[header[index]] = '';
        return '';
      });
      emptyRows1.push(newObj);
    }
    emptyRows.push(object);

    return '';
  });
  worksheet.addRows(emptyRows);
  worksheet.duplicateRow(1, 4, true);
  const headersLength = header.length <= 7 ? 7 : header.length;
  worksheet.getRow(4).values = [];
  worksheet.getRow(1).values = header;
  // worksheet.getRow(1).fill = {
  //   type: 'pattern',
  //   pattern: 'solid',
  //   fgColor: { argb: '318fd5' },
  // };
  // worksheet.getRow(1).font = { color: { argb: '318fd5' }, size: 15 };
  let Row2 = worksheet.getRow(2);
  Row2.values = [`                                           ${splittingHeading}`];

  worksheet.getRow(3).values = ['fdsfdsf'];
  for (let i = 0; i < 500; i++) {
    worksheet.addRow(emptyRows1[0]);
  }
  const borderLength = DownloadData.bodyData.length + 6;

  worksheet.getColumn('Attendance').eachCell((cell) => {
    // cell.protection = {
    //   locked: true,
    // };
    cell.dataValidation = {
      type: 'list',
      allowBlank: false,
      operator: 'equal',
      formulae: ['"P,A"'],
      showErrorMessage: true,
      errorStyle: 'error',
      errorTitle: "Don't type",
      error: 'Select value from dropdown',
    };
  });
  worksheet.getColumn('Gender').eachCell((cell) => {
    cell.dataValidation = {
      type: 'list',
      allowBlank: false,
      operator: 'equal',
      formulae: ['"Male,Female"'],
      showErrorMessage: true,
      errorStyle: 'error',
      errorTitle: "Don't type",
      error: 'Select value from dropdown',
    };
  });
  const questions = DownloadData.bodyData[1].slice(4, DownloadData.bodyData[1].length);
  DownloadData.gettingQuestions.map((question, index) => {
    return worksheet.getColumn(question).eachCell((cell) => {
      cell.dataValidation = {
        type: 'decimal',
        operator: 'between',
        showErrorMessage: true,
        formulae: [0, questions[index]],
        errorStyle: 'error',
        errorTitle: questions[index],
        error: `The value should be less than or equal to ${questions[index]}, if user absent leave empty cell`,
      };
    });
  });
  worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
    if (rowNumber === 5) {
      row.values = header;
      row.eachCell({ includeEmpty: false }, function (cell) {
        cell.style.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '318fd5' },
        };
        cell.style.font = { bold: true, color: { argb: 'FFFFFF' } };
        cell.style.border = {
          top: { style: 'thick', color: { argb: '0c0c0c00' } },
          left: { style: 'thick', color: { argb: '0c0c0c00' } },
          bottom: { style: 'thick', color: { argb: '0c0c0c00' } },
          right: { style: 'thick', color: { argb: '0c0c0c00' } },
        };
      });
    }
    if ([6, 7, 8].includes(rowNumber)) {
      row.eachCell({ includeEmpty: false }, function (cell) {
        cell.style.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D8E4BC' },
        };
      });
    }
    if (rowNumber > 5 && borderLength > rowNumber) {
      row.eachCell({ includeEmpty: false }, function (cell) {
        cell.style.border = {
          left: { style: 'thick', color: { argb: '0c0c0c00' } },
          right: { style: 'thick', color: { argb: '0c0c0c00' } },
        };
      });
    }
    if (rowNumber > 8) {
      row.protection = { locked: false, lockText: false };
    }
  });
  for (let i = 1; i < 4; i++) {
    for (let j = 0; j < headersLength; j++) {
      const currentCell = `${AlphabetsArray[j] + i}`;
      worksheet.getCell(currentCell).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '318fd5' },
      };
      worksheet.getCell(currentCell).font = {
        color: { argb: i === 2 ? 'FFFFFF' : '318fd5' },
        size: 15,
        bold: i === 2 ? true : false,
      };
    }
  }
  function toDataURL(src, callback, outputFormat) {
    let image = new Image();

    image.crossOrigin = 'Anonymous';
    image.onload = function () {
      let canvas = document.createElement('canvas');
      let ctx = canvas.getContext('2d');
      let dataURL;
      canvas.height = this.naturalHeight;
      canvas.width = this.naturalWidth;
      ctx.drawImage(this, 0, 0);
      dataURL = canvas.toDataURL(outputFormat);
      callback(dataURL);
    };
    image.src = src;
    if (image.complete || image.complete === undefined) {
      image.src = 'data:image/gif;base64, R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==';
      image.src = src;
    }
  }
  await worksheet.protect('', {
    selectLockedCells: false,
    selectUnlockedCells: true,
    formatCells: true,
    formatColumns: true,
    formatRows: true,
    insertRows: true,
    insertColumns: false,
    insertHyperlinks: true,
    deleteRows: true,
    deleteColumns: false,
    sort: true,
    autoFilter: true,
  });
  const imageSource = getCollegeLogo();
  toDataURL(imageSource, function (dataUrl) {
    const imageId2 = workbook.addImage({
      base64: dataUrl,
      extension: 'jpeg',
    });

    worksheet.addImage(imageId2, {
      tl: { col: 0, row: 0 },
      br: { col: 1, row: 3 },
      editAs: 'absolute',
    });
    workbook.xlsx
      .writeBuffer()
      .then((buffer) => saveAs(new Blob([buffer]), `${fileName}.xlsx`))
      .catch((err) => console.log('Error writing excel export', err)); //eslint-disable-line
  });
};

export const exportHandler = (
  userData = List(),
  labelData = Map(),
  profileInformationLabel = Map(),
  nameShow = false,
  tabValue = '',
  subTab = '',
  type = 'excel',
  callBack,
  userType,
  fileName,
  sheetName
) => {
  const columns = [
    {
      ...(nameShow && {
        header: userType === 'student' ? 'Student Name' : 'Employee Name',
        key: userType === 'student' ? 'Student Name' : 'Employee Name',
        width: 30,
      }),
    }, // width s
    {
      ...(labelData?.getIn(['user_id', 'isActive'], false) && {
        header: labelData?.getIn(['user_id', 'displayName'], ''),
        key: labelData?.getIn(['user_id', 'displayName'], ''),
        width: 30,
      }),
    }, // width cts
    {
      ...(userType === 'student' &&
        labelData?.getIn(['program_no', 'isActive'], false) && {
          header: labelData?.getIn(['program_no', 'displayName'], ''),
          key: labelData?.getIn(['program_no', 'displayName'], ''),
          width: 30,
        }),
    }, // width cts
    {
      ...(userType === 'student' &&
        labelData?.getIn(['batch', 'isActive'], false) && {
          header: labelData?.getIn(['batch', 'displayName'], ''),
          key: labelData?.getIn(['batch', 'displayName'], ''),
          width: 30,
        }),
    }, // width cts
    {
      ...(userType === 'student' &&
        labelData?.getIn(['enrollment_year', 'isActive'], false) && {
          header: labelData?.getIn(['enrollment_year', 'displayName'], ''),
          key: labelData?.getIn(['enrollment_year', 'displayName'], ''),
          width: 30,
        }),
    }, // width cts
    {
      ...(labelData?.getIn(['email', 'isActive'], false) && {
        header: labelData?.getIn(['email', 'displayName'], ''),
        key: labelData?.getIn(['email', 'displayName'], ''),
        width: 30,
      }),
    },
    {
      ...(profileInformationLabel.getIn(['_nationality_id', 'isActive'], false) && {
        header: profileInformationLabel?.getIn(['_nationality_id', 'displayName'], ''),
        key: profileInformationLabel?.getIn(['_nationality_id', 'displayName'], ''),
        width: 30,
      }),
    },
    {
      ...(labelData.getIn(['gender', 'isActive'], false) && {
        header: labelData?.getIn(['gender', 'displayName'], ''),
        key: labelData?.getIn(['gender', 'displayName'], ''),
        width: 30,
      }),
    },
    {
      ...(subTab === 'Pending' && {
        header: 'Status',
        key: 'Status',
        width: 30,
      }),
    },
  ].filter((item) => Object.entries(item).length);
  const onlyHeader = columns.map((item) => item.key);
  const data = fromJS(userData)
    .map((data, index) => [
      {
        ...(nameShow &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: `${data.getIn(['name', 'first', 'value'], '')} ${data.getIn(
                  ['name', 'middle', 'value'],
                  ''
                )} ${data.getIn(['name', 'last', 'value'], '')}`,
              })),
      },
      {
        ...(labelData.getIn(['user_id', 'isActive'], false) &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: data.getIn(['user_id', 'value'], '')
                  ? data.getIn(['user_id', 'value'], '')
                  : '',
              })),
      },
      {
        ...(userType === 'student' &&
          labelData?.getIn(['program_no', 'isActive'], false) &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: data.getIn(['program', 'value'], '')
                  ? data.getIn(['program', 'value'], '')
                  : '',
              })),
      },
      {
        ...(userType === 'student' &&
          labelData?.getIn(['batch', 'isActive'], false) &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: data.getIn(['batch', 'value'], '') ? data.getIn(['batch', 'value'], '') : '',
              })),
      },
      {
        ...(userType === 'student' &&
          labelData?.getIn(['enrollment_year', 'isActive'], false) &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: data.getIn(['enrollmentYear', 'value'], '')
                  ? data.getIn(['enrollmentYear', 'value'], '')
                  : '',
              })),
      },
      {
        ...(labelData.getIn(['email', 'isActive'], false) &&
          (index === 0 && type === 'excel'
            ? {
                value: `${capitalize(tabValue)}${tabValue === 'registered' ? '' : ' - '} ${subTab}`,
              }
            : {
                value: data.get('email', ''),
              })),
      },
      {
        ...(profileInformationLabel.getIn(['_nationality_id', 'isActive'], false) &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: data.getIn(['profileDetails', '_nationality_id', 'value'], '')
                  ? data.getIn(['profileDetails', '_nationality_id', 'value'], '')
                  : '',
              })),
      },

      {
        ...(labelData.getIn(['gender', 'isActive'], false) &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: data.getIn(['gender', 'value'], 'Female')
                  ? data.getIn(['gender', 'value'], 'Female')
                  : '',
              })),
      },
      {
        ...(subTab === 'Pending' &&
          (index === 0 && type === 'excel'
            ? {
                value: ``,
              }
            : {
                value: data.get('status', '') ? capitalize(data.get('status', '')) : '',
              })),
      },
    ])
    .map((data) => {
      return data.filter((filtered) => Object.entries(filtered).length);
    })
    .toJS();
  const dataWithKeys = [];

  const bodyData = fromJS(data)
    .map((value) => {
      let obj = {};
      const returnedData = value.map((item, index) => {
        obj[onlyHeader[index]] = item.get('value', '');
        return item.get('value', '');
      });
      dataWithKeys.push(obj);
      return returnedData;
    })
    .toJS();

  if (type === 'pdf') {
    const PdfConstructedData = { head: [onlyHeader], body: bodyData };
    return PdfConstructedData;
  } else {
    const workbook = new ExcelJS.Workbook();
    let worksheet = workbook.addWorksheet(sheetName, {
      views: [{ state: 'frozen', ySplit: 1 }],
    });
    worksheet.columns = columns;
    worksheet.addRows(dataWithKeys);
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber === 1) {
        row.eachCell({ includeEmpty: false }, function (cell) {
          cell.style.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '318fd5' },
          };
          cell.style.font = { bold: true, color: { argb: 'FFFFFF' } };
          cell.style.border = {
            top: { style: 'thick', color: { argb: '0c0c0c00' } },
            left: { style: 'thick', color: { argb: '0c0c0c00' } },
            bottom: { style: 'thick', color: { argb: '0c0c0c00' } },
            right: { style: 'thick', color: { argb: '0c0c0c00' } },
          };
        });
      }
      if (rowNumber === 2) {
        row.eachCell({ includeEmpty: false }, function (cell) {
          cell.style.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'D8E4BC' },
          };
          cell.style.font = { bold: true };
          cell.style.border = {
            top: { style: 'thick', color: { argb: '0c0c0c00' } },
          };
        });
      } else {
        row.eachCell({ includeEmpty: false }, function (cell) {
          cell.style.border = {
            left: { style: 'thick', color: { argb: '0c0c0c00' } },
            right: { style: 'thick', color: { argb: '0c0c0c00' } },
          };
        });
      }
    });
    workbook.xlsx
      .writeBuffer()
      .then((buffer) => saveAs(new Blob([buffer]), `${fileName}.xlsx`))
      .catch((err) => console.log('Error writing excel export', err)); //eslint-disable-line
  }

  callBack && callBack();
};
export const exportForErrorEntries = ({ columns, dataWithKeys, fileName }) => {
  const workbook = new ExcelJS.Workbook();
  let worksheet = workbook.addWorksheet('Sheet1');
  worksheet.columns = columns;
  worksheet.addRows(dataWithKeys);
  workbook.xlsx
    .writeBuffer()
    .then((buffer) => saveAs(new Blob([buffer]), `${fileName}.xlsx`))
    .catch((err) => console.log('Error writing excel export', err)); //eslint-disable-line
};
export const pdfExportHandler = (data, tabValue, subTab, current_date, callBack, userType) => {
  const doc = new jsPDF('landscape');
  jsPDF.autoTableSetDefaults({
    headStyles: { fillColor: '#e0e0e0', textColor: 0 },
    bodyStyles: { fillColor: '#ffffff', textColor: 0 },
  });
  var finalY = doc.lastAutoTable.finalY || 10;

  doc.setFont('helvetica', 'bold');
  doc.setFontSize(10);
  doc.text(`Status: ${tabValue} ${subTab !== '' ? '-' : ''} ${subTab}`, 14, finalY + 10);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(8);
  // doc.text(`Academic Year:2021`, 14, finalY + 3);
  doc.setFontSize(10);
  // doc.text('tnc college', 283, finalY + 4, 'right');
  // leavetype !== 'report_absence' &&
  doc.text(`Download Date: ${current_date}`, 200, finalY + 12, 'right');
  doc.line(14, finalY + 20, 283, finalY + 20);
  doc.setFontSize(6);
  doc.autoTable({
    startY: finalY + 28,
    head: data.head,
    //html: '#table',
    styles: {
      fontSize: 7,
      cellWidth: 30,
    },
    columnStyles:
      userType === 'student'
        ? {
            0: { cellWidth: 30 },
            1: { cellWidth: 30 },
            2: { cellWidth: 30 },
            3: { cellWidth: 30 },
            4: { cellWidth: 30 },
            5: { cellWidth: 30 },
            6: { cellWidth: 30 },
            7: { cellWidth: 30 },
            8: { cellWidth: 30 },
          }
        : {
            0: { cellWidth: 50 },
            1: { cellWidth: 50 },
            2: { cellWidth: 50 },
            3: { cellWidth: 50 },
            4: { cellWidth: 50 },
          },
    body: data.body.length ? data.body : [['No Data Found...']],
    tableLineColor: [189, 195, 199],
    rowPageBreak: 'avoid',
  });
  doc.save(`${tabValue}-${current_date}.pdf`);
  callBack && callBack();
  //this.props.closed(false);
};
