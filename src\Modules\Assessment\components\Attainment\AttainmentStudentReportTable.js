import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { trimFractionDigits, ucFirst, indVerRename } from 'utils';

function AttainmentStudentReportTable({
  courseProgramReport,
  selectedNode,
  selectedType,
  filterCourseReport,
  getMarkColor,
  getAttainmentTargetMarkColor,
  selectValues,
}) {
  const programId = selectValues.getIn(['program', 'value'], '');
  const manageTargetBenchMark = courseProgramReport.get('manageTargetBenchMark', Map());
  const NodeHeadingTitle = ({ details, disableClass, type }) => {
    const title =
      type === 'clo'
        ? `${indVerRename('CLO', programId)} ${details?.get('no', '')}`
        : details?.get('nodeName', '') || details?.get('typeName', '');

    return (
      <div className={`${!disableClass ? 'cw_160' : ''}`}>
        <p className="thHeaderReport bold">{title && ucFirst(title)} </p>
      </div>
    );
  };
  NodeHeadingTitle.propTypes = {
    details: PropTypes.instanceOf(Map),
    disableClass: PropTypes.bool,
    type: PropTypes.string,
  };

  const NodeHeading = ({ details, type }) => {
    return (
      <th scope="col" className="borderNone">
        <NodeHeadingTitle details={details} type={type} />
      </th>
    );
  };
  NodeHeading.propTypes = {
    details: PropTypes.instanceOf(Map),
    type: PropTypes.string,
  };

  const NodeParentHeading = () => {
    return (
      <th scope="col" className="borderNone">
        <div className="cw_300 row align-items-center">
          <div className="col-6 pr-0">
            <p className="thHeaderReportStudent text-left bold">Node</p>
          </div>
          {selectedNode ? (
            <div className="col-6 pl-0">
              <NodeHeadingTitle details={filterCourseReport} disableClass={true} />
            </div>
          ) : (
            <div className="col-6 pl-0">
              <p className="thHeaderReportStudent text-left bold">Overall Attainment</p>
            </div>
          )}
        </div>
      </th>
    );
  };

  const AttainmentDetails = ({ details }) => {
    const percent = details.get('percentage', '');
    const level = details.get('level', '');
    const cloId = details.get('_id', '');
    const levelColor = courseProgramReport
      .get('levelColors', List())
      .find((item) => item.get('level', '') === level);

    const selectedNode = selectedType
      ? filterCourseReport
      : courseProgramReport.getIn(['attainmentNodeList', 0], List());

    const markColor = manageTargetBenchMark.isEmpty()
      ? getMarkColor(selectedNode, percent)
      : getAttainmentTargetMarkColor(cloId, percent);
    // const markColor = getMarkColor(selectedNode, percent);

    return (
      <div className="row align-items-center">
        <div className="col-6 pr-0">
          <p className="thHeaderReport" style={{ color: markColor }}>
            {percent !== '' && percent !== null ? `${trimFractionDigits(percent)}%` : '---'}
          </p>
        </div>
        <div className="col-6 pl-0">
          <div className="d-flex justify-content-center ">
            <p
              className="innerValue"
              {...(level &&
                levelColor && {
                  style: { backgroundColor: levelColor.get('color', ''), color: '#fff' },
                })}
            >
              {level.replace('Level', 'L') || '---'}
            </p>
          </div>
        </div>
      </div>
    );
  };
  AttainmentDetails.propTypes = {
    details: PropTypes.instanceOf(Map),
    cloId: PropTypes.string,
  };

  const getStudentList = (studentList, studentId) => {
    const selectedStudent = studentList.find((item) => item.get('studentId') === studentId);
    return selectedStudent !== undefined ? selectedStudent : List();
  };

  const getStudentDetails = (studentId) => {
    if (selectedType) {
      return getStudentList(filterCourseReport.get('studentList', List()), studentId);
    } else {
      return getStudentList(
        courseProgramReport.getIn(['attainmentNodeList', 0, 'studentList'], List()),
        studentId
      );
    }
  };

  const ReportTableColumnPercentage = ({ details }) => {
    const studentId = details.get('studentId', '');
    const studentData = getStudentDetails(studentId);
    return <AttainmentDetails details={studentData} />;
  };
  ReportTableColumnPercentage.propTypes = {
    details: PropTypes.instanceOf(Map),
  };

  const ReportTableColumn = ({ item, disableClass }) => {
    const studentId = item.get('studentId', '');
    const ColumnType = ({ details }) => {
      return (
        <td className={`${!disableClass ? '' : 'cw_160'} attainment_border_bottom `}>
          <AttainmentDetails details={details} />
        </td>
      );
    };
    ColumnType.propTypes = {
      details: PropTypes.instanceOf(Map),
    };
    const studentCO = getStudentDetails(studentId);

    let mergedClo = courseProgramReport.get('courseCLO', List()).map((item) => {
      let findCLO = studentCO
        .get('studentCO', List())
        .find((item2) => item2.get('_id', '') === item.get('_id', ''));
      return item.merge(findCLO);
    });
    return mergedClo.map((one, oneIndex) => (
      <React.Fragment key={oneIndex}>
        <ColumnType details={one} />
      </React.Fragment>
    ));
  };
  ReportTableColumn.propTypes = {
    item: PropTypes.instanceOf(Map),
    disableClass: PropTypes.bool,
  };

  return (
    <>
      <table align="left">
        <thead>
          <tr>
            <NodeParentHeading />
            {courseProgramReport.get('courseCLO', List()).map((detail, index) => (
              <th scope="col" className="borderNone" key={index}>
                <NodeHeadingTitle details={detail} type="clo" />
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          <tr>
            <th scope="col" className="attainment_border_bottom bg-gray">
              <div className="cw_300 row">
                <div className="col-6 pr-0">
                  <p className="thHeaderReport text-left">Attainment</p>
                </div>
                <div className="col-6 pl-0">
                  <p className="thHeaderReport ">%</p>
                </div>
              </div>
            </th>

            {courseProgramReport.get('courseCLO', List()).map((_, index) => (
              <td className="attainment_border_bottom bg-gray" key={index}>
                <p className="thHeaderAttainment f-15">%</p>
              </td>
            ))}
          </tr>

          {courseProgramReport.get('studentList', List()).map((detail, index) => (
            <tr key={index}>
              <th scope="col" className="attainment_border_bottom bg-white">
                <div className="cw_300 row">
                  <div className="col-6 pr-0">
                    <p className="thHeaderReport text-left">
                      <span>{detail.get('name', '')}</span>
                      <br />
                      <span>{detail.get('studentId', '')}</span>
                    </p>
                  </div>
                  <div className="col-6 pl-0">
                    <ReportTableColumnPercentage details={detail} />
                  </div>
                </div>
              </th>
              <ReportTableColumn item={detail} disableClass={true} />
            </tr>
          ))}
        </tbody>
      </table>
    </>
  );
}

AttainmentStudentReportTable.propTypes = {
  courseProgramReport: PropTypes.instanceOf(Map),
  selectedNode: PropTypes.string,
  selectedType: PropTypes.string,
  filterCourseReport: PropTypes.func,
  getMarkColor: PropTypes.func,
  getAttainmentTargetMarkColor: PropTypes.func,
  selectValues: PropTypes.instanceOf(Map),
};

export default AttainmentStudentReportTable;
