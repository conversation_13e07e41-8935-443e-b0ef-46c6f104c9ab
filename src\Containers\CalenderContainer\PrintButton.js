import React, { useState } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { roundToNearestMinutes } from 'date-fns';

const pxToMm = (px) => {
  return Math.floor(px/document.getElementById('myMm').offsetHeight);
};

const mmToPx = (mm) => {
  return document.getElementById('myMm').offsetHeight*mm;
};

const range = (start, end) => {
    return Array(end-start).join(0).split(0).map(function(val, id) {return id+start});
};  


const PrintButton = ({id, label, pageName, title, eventsLength}) => {

  const [disabled , setDisabled] = useState(false);
  const disableFn = (st) => {
    setDisabled(st);
  }
  return (
    <div className="tc mb4 mt2">
      <div id="myMm" style={{height: "1mm"}} />
      <button
        disabled ={disabled}
        title ={title}
        className={(disabled) ? 'fa fa-spinner fa-spin pull-right btn-custom' : "fa fa-file-pdf-o pull-right btn-custom"}
        onClick={(e) => {
          disableFn(true);
          const input = document.getElementById(id);
          html2canvas(input)
          .then((canvas) => {
            var imgData = canvas.toDataURL('image/jpeg', 1.0);
            var imgWidth = 210;
            var imgHeight = canvas.height * imgWidth / canvas.width;
            var doc = new jsPDF('p', 'mm', "a4");
            if(eventsLength > 6){
              doc = new jsPDF('p', 'mm', [imgHeight, imgWidth]);
            }
            doc.addImage(imgData, 'jpeg', 0, 0, imgWidth, imgHeight);
            doc.save(pageName + '.pdf');
            disableFn(false);
          });
        }}
      >
        {label}
      </button>
    </div>)
};

export default PrintButton;