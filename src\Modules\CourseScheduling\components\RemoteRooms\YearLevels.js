import React from 'react';
import Button from '@mui/material/Button';
import { ThemeProvider } from '@mui/styles';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';

import Input from '../../../../Widgets/FormElements/Input/Input';
import { capitalize, indVerRename, levelRename, MUI_THEME, stringToUC } from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
function YearLevels({
  remoteRooms,
  yearLevels,
  activeTermName,
  handleViewClick,
  handleTermChange,
  programId,
}) {
  function getTerms() {
    return remoteRooms
      .map((term) => Map({ name: capitalize(term.get('termName')), value: term.get('termName') }))
      .toJS();
  }

  return (
    <div className="bg-white border-radious-8">
      <div className="p-3">
        <div className="row course_inner_list">
          <div className="col-md-3">
            <Input
              elementType="select"
              elementConfig={{
                options: getTerms(),
              }}
              value={activeTermName}
              label={t('infra_management.remote.term', {
                TERM: stringToUC(indVerRename('Term', programId)),
              })}
              labelclass="f-13"
              changed={(e) => handleTermChange(e.target.value)}
            />
          </div>
        </div>
        {yearLevels.isEmpty() && (
          <div className="mt-3 text-center">
            <Trans i18nKey={'no_data_found'}></Trans>
          </div>
        )}
        {yearLevels.entrySeq().map(([yearName, levels]) => (
          <div key={yearName} className="mt-3">
            <p className="bold pt-2 mb-2">{`${
              t('year') + '' + yearName.split('year').join('')
            }`}</p>
            {levels.map((level) => (
              <div key={level.get('level_id')} className="mt-2">
                <div className="level-row p-3">
                  <div className="row no-gutters justify-content-between align-items-center">
                    <div className="col-md-5">
                      <p className="bold mb-1 f-15">
                        {levelRename(level.get('level_name', ''), programId)}
                      </p>
                      <div>{`${t('infra_management.remote.number_of_rooms')} - ${
                        level.get('remote_schedule_data', List()).size
                      }`}</div>
                    </div>
                    <div className="col-md-2 d-flex justify-content-end">
                      {CheckPermission(
                        'subTabs',
                        'Infrastructure Management',
                        'Remote',
                        '',
                        'Level List',
                        '',
                        'Room Details',
                        'View'
                      ) && (
                        <div>
                          <ThemeProvider theme={MUI_THEME}>
                            <Button
                              variant="text"
                              color="primary"
                              onClick={() => handleViewClick(level)}
                            >
                              <Trans i18nKey={'view'}></Trans>
                            </Button>
                          </ThemeProvider>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}

YearLevels.propTypes = {
  remoteRooms: PropTypes.instanceOf(List),
  yearLevels: PropTypes.instanceOf(Map),
  activeTermName: PropTypes.string,
  handleViewClick: PropTypes.func,
  handleTermChange: PropTypes.func,
  programId: PropTypes.string,
};

export default YearLevels;
