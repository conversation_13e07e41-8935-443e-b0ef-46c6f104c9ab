// Base selector
const selectBonusAttendanceState = (state) => state?.bonusAttendance || null;

export const selectStudentBonusAttendance = (state) => {
  const stateData = selectBonusAttendanceState(state);
  return stateData?.get('studentBonusAttendance', [])?.toJS() || [];
};

export const selectBonusAttendanceData = (state) => {
  const list = selectStudentBonusAttendance(state);
  return (list || []).map((item) => ({
    ...item,
    formattedDate: item?.updatedAt ? new Date(item.updatedAt).toLocaleDateString() : 'N/A',
    formattedPercentage: `${item?.percentage || 0}%`,
    addedByName: item?.addedBy
      ? `${item.addedBy.name?.first || ''} ${item.addedBy.name?.last || ''}`.trim()
      : 'N/A',
  }));
};
