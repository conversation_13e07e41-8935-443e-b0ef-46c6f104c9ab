import React, { useState, Suspense } from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Menu, MenuItem, IconButton } from '@mui/material';
import { useRouteMatch } from 'react-router-dom';
import Tooltips from '_components/UI/Tooltip/Tooltip';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { Map } from 'immutable';
import { removeURLParams } from 'utils';

const PublishModal = React.lazy(() => import('../../modals/PublishModal'));
export function Header1({ history, programName, isConfigurePages, type }) {
  function routeURL() {
    if (type === 'program') {
      history.push(
        `/assessment-management/assessment_details${removeURLParams(history.location, [
          'year',
        ])}&year=YXNzZXNz`
      );
    } else if (type === 'cs') {
      history.push(
        `/assessment-management/assessment_details${removeURLParams(history.location, [
          'cId',
          'cName',
        ])}`
      );
    } else if (type === 'course') {
      history.push(
        `/assessment-management/assessment_details/configure/courses${removeURLParams(
          history.location,
          ['cId', 'cName']
        )}`
      );
    } else {
      history.goBack(-1);
    }
  }

  return (
    <div className="bg-white p-2 border-bottom-2px">
      <div className="container-fluid">
        <p className="font-weight-bold mb-2 mt-2 text-left f-17">
          <i
            className="fa fa-arrow-left pr-3 remove_hover assessment-planning-text"
            aria-hidden="true"
            onClick={() =>
              isConfigurePages
                ? routeURL()
                : history.push('/assessment-management/assessment_library')
            }
          ></i>
          {programName}
        </p>
      </div>
    </div>
  );
}

export const Header = React.memo(Header1);
Header1.propTypes = {
  history: PropTypes.object,
  programName: PropTypes.string,
  isConfigurePages: PropTypes.bool,
  type: PropTypes.string,
};
const DropMenu = ({ history }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const { location } = history;
  const { search } = location;
  const match = useRouteMatch();

  const handleEdit = (e) => {
    history.push(`${match.url}${search.replace('view', 'edit')}`);
    handleClose();
  };

  const ITEM_HEIGHT = 48;

  return (
    <div className="">
      <IconButton
        aria-label="more"
        aria-controls="long-menu"
        aria-haspopup="true"
        onClick={handleClick}
        size="small"
      >
        <MoreVertIcon />
      </IconButton>

      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        keepMounted
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            maxHeight: ITEM_HEIGHT * 4.5,
            width: '20ch',
          },
        }}
      >
        <MenuItem onClick={(e) => handleEdit(e)}>Edit</MenuItem>
      </Menu>
    </div>
  );
};
DropMenu.propTypes = {
  history: PropTypes.object,
};

export function MarksHeader1({
  history,
  programName,
  isConfigurePages,
  saveMarks,
  mode,
  setExportDownloadTrue,
  planName,
  publishCallBack,
  assessmentData,
}) {
  const [openPublish, setOpenPublish] = useState(false);
  return (
    <>
      <div className="bg-white p-1 border-bottom-2px">
        <div className="container-fluid">
          <div className="row ">
            <div className="col-md-6 pt-1">
              <p className="font-weight-bold mb-2 mt-2 text-left f-17">
                {' '}
                <i
                  className="fa fa-arrow-left pr-3 remove_hover"
                  aria-hidden="true"
                  onClick={() =>
                    isConfigurePages
                      ? history.push(
                          `/assessment-management/assessment_details/configure${removeURLParams(
                            history.location,
                            [
                              'mode',
                              'mid',
                              'mName',
                              'marks',
                              'tId',
                              'tName',
                              'stId',
                              'stName',
                              'atId',
                              'atName',
                            ]
                          )}`
                        )
                      : history.push('/assessment-management/assessment_library')
                  }
                ></i>
                {programName}
              </p>
            </div>
            <div className="col-md-6 pt-2 pb-2 d-flex justify-content-end">
              {mode === 'edit' ? (
                <>
                  {' '}
                  <MButton
                    variant="outlined"
                    color="primary"
                    className={'mr-2'}
                    clicked={() => history.goBack(-1)}
                  >
                    Cancel
                  </MButton>
                  <MButton variant="contained" color="primary" clicked={saveMarks}>
                    Save
                  </MButton>
                </>
              ) : (
                <div className="d-flex align-items-center">
                  {CheckPermission(
                    'subTabs',
                    'Assessment Management',
                    'Assessment Library',
                    '',
                    'Program / Course Assessments',
                    '',
                    'Assessment View',
                    'Export'
                  ) && (
                    <Tooltips title="Export - Assessment Details">
                      <MButton
                        variant="contained"
                        color="primary"
                        clicked={() => setExportDownloadTrue(true)}
                      >
                        Export
                      </MButton>
                    </Tooltips>
                  )}
                  {CheckPermission(
                    'subTabs',
                    'Assessment Management',
                    'Assessment Library',
                    '',
                    'Program / Course Assessments',
                    '',
                    'Assessment View',
                    'Publish'
                  ) && (
                    <>
                      {assessmentData.get('status', '') === 'published' ? (
                        <span
                          className="ml-2"
                          style={{ color: ' #374151', fontWeight: 500, display: 'flex' }}
                        >
                          <i className="fa fa-check-circle f-24 mr-1" aria-hidden="true"></i>{' '}
                          Published
                        </span>
                      ) : (
                        <Tooltips title="Publish - Assessment Details">
                          <MButton
                            className="ml-2"
                            variant="contained"
                            color="primary"
                            clicked={() => setOpenPublish(true)}
                          >
                            Publish
                          </MButton>
                        </Tooltips>
                      )}
                    </>
                  )}
                  {CheckPermission(
                    'subTabs',
                    'Assessment Management',
                    'Assessment Library',
                    '',
                    'Program / Course Assessments',
                    '',
                    'Assessment View',
                    'Edit'
                  ) && <DropMenu history={history} />}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {openPublish && (
        <Suspense fallback="">
          <PublishModal
            cancel={() => setOpenPublish(false)}
            planName={planName}
            callback={publishCallBack}
            assessmentData={assessmentData}
          />
        </Suspense>
      )}
    </>
  );
}

MarksHeader1.propTypes = {
  history: PropTypes.object,
  programName: PropTypes.string,
  isConfigurePages: PropTypes.bool,
  saveMarks: PropTypes.func,
  setExportDownloadTrue: PropTypes.func,
  mode: PropTypes.string,
  planName: PropTypes.string,
  publishCallBack: PropTypes.func,
  assessmentData: PropTypes.instanceOf(Map),
};

export const MarksHeader = React.memo(MarksHeader1);
