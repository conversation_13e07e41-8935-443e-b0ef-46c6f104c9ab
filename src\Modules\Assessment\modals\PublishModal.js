import React from 'react';
import { Modal } from 'react-bootstrap';
import PropTypes from 'prop-types';
import PublishImg from 'Assets/publish.svg';
import MButton from 'Widgets/FormElements/material/Button';
import { Map } from 'immutable';

const PublishModal = (props) => {
  const { cancel, callback, planName, assessmentData } = props;
  const handleSubmit = () => {
    callback(assessmentData.get('_id', ''), cancel);
  };
  return (
    <Modal show={true} size="lg" onHide={cancel} centered>
      <Modal.Body>
        <div className="d-flex mb-3">
          <img className="mr-2" alt={'Archive'} src={PublishImg} />
          <p className="mt-1 mb-0 f-21 bold"> {`Publish Assessment Confirmation`}</p>
        </div>

        <div className="p-2">
          <p className="mb-0 break-word">
            {`Are you sure you want to publish the "${planName}" assessment?`}
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <MButton variant="outlined" color="darkGray" className={'mr-2'} clicked={cancel}>
          Cancel
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleSubmit}>
          Confirm
        </MButton>
      </Modal.Footer>
    </Modal>
  );
};
PublishModal.propTypes = {
  callback: PropTypes.func,
  cancel: PropTypes.func,
  planName: PropTypes.string,
  assessmentData: PropTypes.instanceOf(Map),
};
export default PublishModal;
