import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { EventRowWrapper, Gap, EventRowWrapperHover } from '../Styled';
import moment from 'moment';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getTranslatedDuration } from 'utils';
const EventRows = ({ show, edit, icon, del, content, i, editHide }) => {
  let end_date = '';
  let event_date = '';
  let start_time = '';
  let end_time = '';
  if (content !== undefined) {
    end_date = moment(content.end_date).format('DD MMM YYYY');
    event_date = moment(content.event_date).format('DD MMM YYYY');
    start_time = moment(Date.parse(content.start_time)).format('hh:mm  A');
    end_time = moment(Date.parse(content.end_time)).format('hh:mm  A');
  }
  return (
    <Fragment>
      {show === 'title' && (
        <EventRowWrapper className="title">
          <div className="tb-100">
            <Trans i18nKey={'s_no'}></Trans>
          </div>
          <div className="tb-150">
            <Trans i18nKey={'event_title'}></Trans>
          </div>
          <div className="tb-150">
            <Trans i18nKey={'event_type'}></Trans>
          </div>
          <div className="tb-150">
            <Trans i18nKey={'events.start_date'}></Trans>
          </div>
          <div className="tb-150">
            <Trans i18nKey={'events.start_time'}></Trans>
          </div>
          <div className="tb-150">
            <Trans i18nKey={'events.end_date'}></Trans>
          </div>
          <div className="tb-100">
            <Trans i18nKey={'events.end_time'}></Trans>
          </div>
          <div className="tb-50">
            {icon === 'show' ? (
              <Fragment>
                <Gap />
              </Fragment>
            ) : (
              <Fragment />
            )}
          </div>
        </EventRowWrapper>
      )}
      {show === 'content' && (
        <EventRowWrapperHover>
          <div className="tb-100">{i + 1}</div>
          <div className="tb-150">
            {typeof content.event_name === 'object'
              ? content.event_name.first_language
              : content.event_name}
          </div>

          <div className="tb-150">{t(`events.event_types.${content.event_type}`)}</div>
          <div className="tb-150"> {getTranslatedDuration(event_date)}</div>
          <div className="tb-150">
            {' '}
            {start_time.includes('A')
              ? start_time.replace('AM', t('date.am'))
              : start_time.replace('PM', t('date.pm'))}
          </div>
          <div className="tb-150"> {getTranslatedDuration(end_date)}</div>
          <div className="tb-100">
            {' '}
            {end_time.includes('A')
              ? end_time.replace('AM', t('date.am'))
              : end_time.replace('PM', t('date.pm'))}
          </div>
          <div className="tb-50">
            {icon === 'show' ? (
              <Fragment>
                {!editHide ? (
                  <i className="fas fa-pencil-alt" onClick={() => edit()}></i>
                ) : (
                  <i
                    className="fas fa-pencil-alt"
                    style={{ color: 'white', visibility: 'hidden' }}
                  ></i>
                )}
                <i className="fas fa-trash pl-3" onClick={() => del()}></i>
              </Fragment>
            ) : (
              <Fragment />
            )}
          </div>
        </EventRowWrapperHover>
      )}
    </Fragment>
  );
};

EventRows.propTypes = {
  show: PropTypes.string,
  icon: PropTypes.string,
  i: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  edit: PropTypes.func,
  del: PropTypes.func,
  content: PropTypes.object,
  editHide: PropTypes.bool,
};

EventRows.defaultProps = {
  icon: 'show',
  editHide: false,
};

export default EventRows;
