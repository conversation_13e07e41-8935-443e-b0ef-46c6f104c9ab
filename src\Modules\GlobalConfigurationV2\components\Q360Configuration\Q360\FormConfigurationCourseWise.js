import React, { Fragment, useMemo, useRef, useState } from 'react';
import { List, Map as IMap, fromJS } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Tooltip from '@mui/material/Tooltip';
import Tabs, { tabsClasses } from '@mui/material/Tabs';
import {
  Checkbox,
  Divider,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import Button from 'Widgets/FormElements/material/Button';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import InfoIcon from '@mui/icons-material/Info';
import { TabList, TabPanel } from '@mui/lab';
import shared_icon from 'Assets/share_blue.svg';
import Tab from '@mui/material/Tab';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import { jsUcfirstAll } from 'utils';
import TabContext from '@mui/lab/TabContext';
import { t } from 'i18next';
import PropTypes from 'prop-types';
import { createDuplicateForm, getCategoryForm, updateDuplicateForm } from '_reduxapi/q360/actions';
import LoadSelectedChips, {
  AccordionLayoutProgramCreation,
  MenuWithOpenAndClose,
  // tabBorderNoneYear,
  textTransform,
  tabPadding,
  formatYear,
  useMatchingForms,
  useDispatchAndSelectorFunctionsQlc,
  form_configure_tab,
  describeSx,
  typeOfForms,
} from './utils';

//FOR FUTURE KT USE READ.MD FILE SECTION NAME IS "ASSIGNED COURSE IN NEW FORM CREATING MODAL AND EDIT FORM MODAL"
export default function FormConfigurationCourseWise({
  handleClose,
  existingData = IMap(),
  params,
}) {
  const [deletedManipulatedIds, setDeletedManipulatedIds] = useState(IMap());
  const [pgmDetailsState, setPgmDetailsState] = useState(IMap());
  const [pgmDetailsParentState, setPgmDetailsParentState] = useState(IMap());

  const [formData, setFormData] = useState(
    IMap({
      formName: existingData.get('formName', ''),
      describe: existingData.get('describe', ''),
      incorporateMandatory: existingData.get('incorporateMandatory', false),
      formType: existingData.get('formType', 'complete'),
    })
  );
  const {
    fetchProgramDetails,
    programDetails: reduxProgramDetails,
    dispatch,
    configureTemplate,
    currentCategoryIndex,
    setMessage,
  } = useDispatchAndSelectorFunctionsQlc();
  const reduxSelectedProgramIds = useMemo(function () {
    const uniqueProgramIds = new Set();
    for (const program of existingData.get('selectedProgram', List())) {
      if (program.get('all', false)) uniqueProgramIds.add(program.get('programId', ''));
    }
    return fromJS([...uniqueProgramIds]);
  }, []);
  const [constructedPgmCreation, setConstructedPgmCreation] = useMatchingForms(
    existingData.get('formId', '')
  );
  const [selectedProgramIds, setSelectedProgramIds] = useState(reduxSelectedProgramIds);
  const isIncorporateMandatory = configureTemplate.getIn(
    [currentCategoryIndex, 'actions', 'incorporateMandatory'],
    false
  );
  const categoryFormType = configureTemplate.getIn([currentCategoryIndex, 'categoryFormType'], '');
  const fetchApi = (pgmId, checked = false, callBack = null) => {
    fetchProgramDetails({
      params: { programId: [pgmId] }, //65606e4f01fe4248e23d151e
      checked,
      cb: callBack,
    });
  };

  function checkedProcessCallBack(programId, checked = true) {
    setPgmDetailsState((prev) => {
      return prev
        .update(programId, IMap(), (program) => {
          return program.update('curriculum', IMap(), (curriculum) => {
            return curriculum.map((cur) =>
              cur.update('years', IMap(), (years) => {
                return years.map(
                  (year) =>
                    year
                      .update('courseIds', IMap(), (courseIds) => {
                        return courseIds.map((courseArray) => {
                          const updatedCourseArray = courseArray.map((course) =>
                            course.set('isChecked', checked)
                          );
                          return updatedCourseArray;
                        });
                      })
                      .set(
                        'selectedCourseCount',
                        checked ? year.get('constructedCourseCount', 0) : 0
                      )
                  // .set('constructedCourseCount', year.get('constructedCourseCount', 0))
                );
              })
            );
          });
        })
        .updateIn([programId, 'selectedProgramCount'], () => {
          return checked ? prev.getIn([programId, 'constructedProgramCount'], 0) : 0;
        });
      // .setIn([programId, 'constructedProgramCount'],reduxState.getIn([programId, 'constructedProgramCount'], 0));
    });
  } //this is for make seelcted coutn full and checked true
  // const handleIndividualProgramChecked = (pgmId) => async (e) => {
  //   e.stopPropagation();
  //   const checked = e.target.checked;
  //   if (!pgmDetailsState.has(pgmIfd)) {
  //     return fetchApi(pgmId, checkedProcessCallBack, checked);
  //   }
  //   checkedProcessCallBack(pgmId, checked);
  // };
  function clearAll() {
    // if (pgmDetailsParentState.size || pgmDetailsState.size) {
    //   setPgmDetailsParentState(IMap());
    //   setPgmDetailsState(IMap());
    //   return;
    // }
    // manipulatingProgramData('');
    setPgmDetailsState(pgmDetailsParentState);
  }

  function onSave() {
    setPgmDetailsParentState(
      pgmDetailsState.filter((program) => program.get('selectedProgramCount', 0) > 0)
    );
  }

  function handleConstruction() {
    const selectCourses = [];
    const selectedProgram = [];
    const selectedProgramNewSet = [];
    for (const [programId, program] of pgmDetailsParentState.entrySeq()) {
      // if (program.get('selectedProgramCount', 0) === 0) continue;
      for (const [curId, cur] of program.get('curriculum', IMap()).entrySeq()) {
        for (const [yearNo, year] of cur.get('years', IMap()).entrySeq()) {
          // if (year.get('selectedCourseCount', 0) === 0) continue;
          //eslint-disable-next-line
          for (const [courseType, courseList] of year.get('courseIds', IMap()).entrySeq()) {
            for (const eachCourse of courseList) {
              const checkNewlySelectedCheckbox =
                eachCourse.get('isChecked', false) && !eachCourse.has('_manipulate_id', ''); // this is for when uesr checked the new checkbox with make sure doesn't exist in previous user respone
              const thisIsDeletedCheckBoxId =
                !eachCourse.get('isChecked', false) &&
                deletedManipulatedIds.hasIn([programId, eachCourse.get('_manipulate_id', '')]); //is user uncheck the checkbox and this checkbox is previously selected by user
              if (checkNewlySelectedCheckbox) {
                selectCourses.push({
                  programId: programId,
                  programName: reduxProgramDetails.getIn([programId, 'program_name'], ''),
                  curriculumId: curId,
                  curriculumName: cur.get('curriculum_name'),
                  year: yearNo,
                  // courseId: eachCourse.getIn(['courseId', '_id'], ''),
                  courseId: eachCourse.get('courseId', ''),
                  courseCode: eachCourse.get('course_code', ''),
                  courseName: eachCourse.get('course_name', ''),
                  courseType: eachCourse.get('course_type', ''),
                  sharedWithOthers: eachCourse.get('shared_with_others', false),
                  sharedFormOthers: eachCourse.get('shared_from_others', false),
                });
                continue;
              }
              if (thisIsDeletedCheckBoxId) {
                selectCourses.push({
                  _id: eachCourse.get('_manipulate_id', ''),
                  isDeleted: true,
                });
              }
            }
          }
        }
      }
      if (selectedProgramNewSet.includes(programId)) {
        continue;
      }
      selectedProgramNewSet.push(programId);
      selectedProgram.push({
        programId,
        programName: reduxProgramDetails.getIn([programId, 'program_name'], ''),

        all:
          program.get('selectedProgramCount', -1) ===
          reduxProgramDetails.getIn([programId, 'constructedProgramCount'], 0),
      });
    }
    for (const program of existingData.get('selectedProgram', List())) {
      const programId = program.get('programId', '');
      if (pgmDetailsParentState.has(programId)) {
        continue;
      }
      if (!deletedManipulatedIds.has(programId)) {
        selectedProgram.push({
          programId: programId,
          programName: program.get('programName', ''),
          all: selectedProgramIds.includes(programId),
        });
      }
      if (!selectedProgramIds.includes(programId)) {
        for (const eachExistingCourseId of deletedManipulatedIds.get(programId, IMap())) {
          selectCourses.push({
            _id: eachExistingCourseId[0],
            isDeleted: true,
          });
        }
      }
    }
    if (selectCourses.length === 0)
      return {
        selectedProgram,
      };
    return {
      selectCourses,
      selectedProgram,
    };
  }
  function handleCreateCallback() {
    dispatch(getCategoryForm(params));
    handleClose();
  }

  function handleCreate() {
    if (!formData.get('formName', '').trim()) {
      return setMessage('Enter the Form Name');
    }
    if (selectedProgramIds.size === 0 && pgmDetailsParentState.isEmpty()) {
      return setMessage('Select the course from program');
    }
    const payload = {
      [existingData.has('formId') ? 'categoryFormId' : 'categoryId']: existingData.get(
        'formId',
        configureTemplate.getIn([currentCategoryIndex, '_id'])
      ),
      ...(existingData.get('formName', '') !== formData.get('formName', '') && {
        formName: formData.get('formName', ''),
      }),
      describe: formData.get('describe', ''),
      formType: formData.get('formType', ''),
      categoryFormType: categoryFormType,
      incorporateMandatory: formData.get('incorporateMandatory', false),
      categoryId: configureTemplate.getIn([currentCategoryIndex, '_id'], ''),
      ...handleConstruction(),
    };
    if (existingData.has('formId'))
      return dispatch(updateDuplicateForm(payload, handleCreateCallback));
    payload['categorySettings'] = {
      level: configureTemplate.getIn([currentCategoryIndex, 'level'], ''),
    };
    const actions = {};
    actions['studentGroups'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'studentGroups'],
      false
    );
    actions['everyAcademic'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'everyAcademic'],
      false
    );
    actions['occurrenceConfiguration'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'occurrenceConfiguration'],
      false
    );
    actions['attemptType'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'attemptType'],
      false
    );
    actions['academicTerms'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'academicTerms'],
      false
    );
    payload['actions'] = actions;
    dispatch(createDuplicateForm(payload, params, 'create', handleCreateCallback));
  }

  const handleInput = (key) => (e) => {
    setFormData((prev) => prev.set(key, e.target.value));
  };

  const handleCheckboxInput = (key) => (e) => {
    setFormData((prev) => prev.set(key, e.target.checked));
  };

  const deleteExistingProgramCreationData = (programId) => () => {
    //we are deleting programIds already selected comes from backend not newly selected ones( edit purpose)
    setConstructedPgmCreation((prev) => prev.delete(programId));
    setSelectedProgramIds((prev) => prev.filter((pgmId) => pgmId !== programId));
    let ids = IMap();
    for (const course of constructedPgmCreation.get(programId, List())) {
      ids = ids.set(course.get('_id', ''), true);
    }
    setDeletedManipulatedIds((prev) => prev.set(programId, ids));
  };

  const removingProgramFrom_deletedManipulatedIds = (programId) => {
    setDeletedManipulatedIds((prev) => prev.delete(programId));
  };

  const set_program_details_from_redux_state = (programId) => {
    if (
      reduxProgramDetails.getIn([programId, 'selectedProgramCount'], 0) ===
      reduxProgramDetails.getIn([programId, 'constructedProgramCount'], -1)
    ) {
      setPgmDetailsState((prev) => prev.set(programId, reduxProgramDetails.get(programId, IMap())));
    } else {
      checkedProcessCallBack(programId);
    }
    removingProgramFrom_deletedManipulatedIds(programId);
  };
  const push_programId_redux_to_local_state = (programId) => {
    setSelectedProgramIds((prev) => prev.push(programId));
    removingProgramFrom_deletedManipulatedIds(programId);
  };
  const incorporate_existing_assigned_course_with_current_redux_response = (
    response,
    programId
  ) => {
    for (const eachCourse of constructedPgmCreation.get(programId, List())) {
      const courseIndexInCourseType = response
        .getIn(
          [
            programId,
            'curriculum',
            eachCourse.get('curriculumId'),
            'years',
            eachCourse.get('year', ''),
            'courseIds',
            eachCourse.get('courseType', ''),
          ],
          List()
        )
        .findIndex(
          (course) => course.get('courseId', '') === eachCourse.getIn(['courseId', '_id'], '')
        );
      if (courseIndexInCourseType !== -1) {
        response = response.setIn(
          [
            programId,
            'curriculum',
            eachCourse.get('curriculumId'),
            'years',
            eachCourse.get('year', ''),
            'courseIds',
            eachCourse.get('courseType', ''),
            courseIndexInCourseType,
            '_manipulate_id',
          ],
          eachCourse.get('_id', '')
        );
      }
    }
    return response;
  };
  const syncing_previous_selected_checkbox_data_with_current_state = (response, programId) => {
    for (const eachCourse of constructedPgmCreation.get(programId, List())) {
      const courseIndexInCourseType = response
        .getIn(
          [
            programId,
            'curriculum',
            eachCourse.get('curriculumId'),
            'years',
            eachCourse.get('year', ''),
            'courseIds',
            eachCourse.get('courseType', ''),
          ],
          List()
        )
        .findIndex(
          (course) => course.get('courseId', '') === eachCourse.getIn(['courseId', '_id'], '')
        );
      if (courseIndexInCourseType !== -1) {
        response = response
          .setIn(
            [
              programId,
              'curriculum',
              eachCourse.get('curriculumId'),
              'years',
              eachCourse.get('year', ''),
              'courseIds',
              eachCourse.get('courseType', ''),
              courseIndexInCourseType,
              'isChecked',
            ],
            true
          )
          .updateIn(
            [
              programId,
              'curriculum',
              eachCourse.get('curriculumId'),
              'years',
              eachCourse.get('year', ''),
              'selectedCourseCount',
            ],
            0,
            (count) => count + 1
          )
          .updateIn([programId, 'selectedProgramCount'], 0, (count) => count + 1);
      }
    }
    return response;
  };
  const handleProgramCheckedRedirect = (programId) => (e) => {
    const checked = e?.target?.checked;
    // purpose-1=> this is for when use check and uncheck the program checkbox we are doing something
    // purpose-2=> and this same function we can use for remove icon of outer layer of Popup
    //because it is similiar to unChecked action
    if (checked) {
      // condition 1  // if it is exist in redux just we set program data form redux to local state
      if (reduxProgramDetails.has(programId)) {
        return set_program_details_from_redux_state(programId);
      }
      //condition 2  this is for when user don't expand the accordion and he previously selected (for edit) data
      // then we push the programid from reduxSelectedProgramIds into selectedProgramIds
      if (reduxSelectedProgramIds.includes(programId)) {
        return push_programId_redux_to_local_state(programId);
      }
      //condition 3 this is does't exist pgmDetailsState and selectedProgramIds so we should call api for fresh thing
      return fetchApi(programId, checked, (response) => {
        response = incorporate_existing_assigned_course_with_current_redux_response(
          response,
          programId
        ); // check thepurpose of this functioon into read.md
        setPgmDetailsState((prev) => prev.merge(response));
      });
    }
    //this below block for unchecked
    checkedProcessCallBack(programId, false);
    // setPgmDetailsState((prev) => prev.delete(programId));
    if (selectedProgramIds.includes(programId)) {
      deleteExistingProgramCreationData(programId)();
    }
    // setSelectedProgramIds((prev) => prev.filter((pgmId) => pgmId !== programId));
    // const programIds = {};
    // for (const eachCourse of constructedPgmCreation.get(programId, List())) {
    //   programIds[eachCourse.get('_id', '')] = eachCourse.get('_id', '');
    // }
    // setDeletedManipulatedIds((prev) => prev.set(programId, fromJS(programIds)));
  };
  const handleChipClose = (programId) => () => {
    setPgmDetailsParentState((prev) => prev.delete(programId));
  };
  const isProgramChecked = (programId) => {
    if (pgmDetailsState.has(programId)) {
      return (
        reduxProgramDetails.getIn([programId, 'constructedProgramCount'], -1) ===
        pgmDetailsState.getIn([programId, 'selectedProgramCount'], 0)
      );
    }
    return selectedProgramIds.includes(programId);
  };
  const programSelectedCount = (programId) => {
    if (pgmDetailsState.has(programId)) {
      return pgmDetailsState.getIn([programId, 'selectedProgramCount'], 0);
    }
    return constructedPgmCreation.get(programId, List()).size;
  };
  const accordionIsExpanded = (programId) => {
    if (!reduxProgramDetails.has(programId)) {
      fetchApi(programId, false, (response) => {
        response = syncing_previous_selected_checkbox_data_with_current_state(response, programId);
        response = incorporate_existing_assigned_course_with_current_redux_response(
          response,
          programId
        );
        setPgmDetailsState((prev) => prev.merge(response));
      });
    }
    // if (selectedProgramIds.includes(programId)) {
    //   const response = syncing_previous_selected_checkbox_data_with_current_state(
    //     reduxProgramDetails,
    //     programId
    //   );
    //   checkedProcessCallBack(programId, response);
    // }
  };
  const parentDialogRef = useRef(null);
  const selectedPgmDetailsState = pgmDetailsState.filter(
    (program) => program.get('selectedProgramCount', 0) > 0
  );
  return (
    <Dialog
      open={true}
      onClose={handleClose}
      maxWidth={'sm'}
      fullWidth={true}
      // className={`${open ? 'invisible' : 'visibl e'}`}
    >
      <DialogTitle id="scroll-dialog-title">
        <div className="form_heading_creation">{existingData.get('dialogTitle', '')}</div>
      </DialogTitle>
      <DialogContent ref={parentDialogRef}>
        <div className="">
          <div>
            <MaterialInput
              changed={handleInput('formName')}
              elementType={'materialInput'}
              type={'text'}
              value={formData.get('formName', '')}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey">Form Name</div>}
              placeholder={'Enter Name'}
              sx={{
                '& .MuiInputBase-root': {
                  color: '#000000 !important',
                },
                '& .MuiInputBase-input': {
                  border: '1px solid #D1D5DB !important',
                  borderRadius: '4px',
                  padding: '8px 7px',
                },
              }}
            />
          </div>
          <div>
            <MaterialInput
              changed={handleInput('describe')}
              value={formData.get('describe', '')}
              elementType={'materialTextArea'}
              type={'text'}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey pt-2">Describe (Optional)</div>}
              placeholder={'Describe here'}
              maxRows={'4'}
              minRows={'4'}
              bgWhite={true}
              sx={describeSx}
            />
          </div>
          {isIncorporateMandatory && (
            <div>
              <div className="f-12 fw-400 text-mGrey pt-1">Incorporate Type</div>
              <div className="d-flex align-items-center gap-10 mt-1">
                <Checkbox
                  size="small"
                  checked={formData.get('incorporateMandatory', false)}
                  onChange={handleCheckboxInput('incorporateMandatory')}
                  sx={{
                    padding: '5px 0px',
                    '& .MuiSvgIcon-root': { width: 17, height: 17 },
                    color: '#768571',
                  }}
                />
                <div className="f-14">Incorporate is Mandatory</div>
                <Tooltip title="Incorporate is Mandatory">
                  <InfoIcon className="f-16" sx={{ color: '#6B7280' }} />
                </Tooltip>
              </div>
            </div>
          )}
          {categoryFormType === 'form' && (
            <>
              <div className="f-12 fw-400 text-mGrey mt-2">Form Type</div>
              <FormControl>
                <RadioGroup value={formData.get('formType', '')} onChange={handleInput('formType')}>
                  <div className="d-flex align-items-center gap-10 mt-2">
                    {typeOfForms.map((element, index) => (
                      <div className="d-flex align-items-center gap-10" key={index}>
                        <FormControlLabel
                          className="m-0 d-flex gap-2"
                          value={element.value}
                          control={
                            <Radio
                              size="small"
                              sx={{
                                padding: '0px',
                                '& .MuiSvgIcon-root': {
                                  width: '17px',
                                  height: '17px',
                                },
                              }}
                            />
                          }
                          label={<div className="f-14 ml-2">{element.label}</div>}
                        />
                        <Tooltip title={t(`q360FormCreation.${element.value}Form`)}>
                          <InfoIcon className="f-16" sx={{ color: '#6B7280' }} />
                        </Tooltip>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </FormControl>
            </>
          )}
        </div>
        <div className="mt-1">
          <div className="f-12 fw-400 text-dGrey mb-2 pt-2">Assign Courses</div>
          <div className=" rounded pl-3 q360-select-pd " style={{ border: '1px solid #D1D5DB' }}>
            <MenuWithOpenAndClose
              children1={(handleClick) => (
                <div className="d-flex align-items-center cursor-pointer" onClick={handleClick}>
                  <div style={{ color: '#D1D5DB' }}>Select Course</div>
                  <div className="ml-auto cursor">
                    <ArrowDropDownIcon className="pt-1" />
                  </div>
                </div>
              )}
            >
              {(handleClose) => (
                <AccordionLayoutProgramCreation
                  accordionIsExpanded={accordionIsExpanded}
                  handleClose={handleClose}
                  handleProgramCheckBox={handleProgramCheckedRedirect}
                  programSelectedCount={programSelectedCount}
                  clearAll={clearAll}
                  onSave={onSave}
                  isProgramChecked={isProgramChecked}
                  pgmDetailsState={pgmDetailsState}
                  height={parentDialogRef?.current?.offsetHeight}
                  isSaveButtonDisabled={
                    !selectedPgmDetailsState.size && !deletedManipulatedIds.size
                  }
                >
                  {(programId) => {
                    return (
                      <FormCurriculum
                        key={programId}
                        pgmDetailsState={pgmDetailsState}
                        domLoopPgmDetails={reduxProgramDetails}
                        setPgmDetailsState={setPgmDetailsState}
                        programId={programId}
                        deletedManipulatedIds={deletedManipulatedIds}
                        setDeletedManipulatedIds={setDeletedManipulatedIds}
                      />
                    );
                  }}
                </AccordionLayoutProgramCreation>
              )}
            </MenuWithOpenAndClose>
          </div>
          <div className="d-flex align-items-center text-overflow-wrap">
            {constructedPgmCreation.entrySeq().map(([programId, program]) => {
              if (pgmDetailsState.has(programId)) return null;
              return (
                <LoadSelectedChips
                  key={programId}
                  programId={programId}
                  programName={program.getIn([0, 'programName'], '')}
                  levelLabel={'Course'}
                  onDelete={deleteExistingProgramCreationData}
                  selectedCourseCount={program.size}
                />
              );
            })}

            {pgmDetailsParentState.entrySeq().map(([programId, program]) => {
              if (!program.get('selectedProgramCount', 0)) return null;
              return (
                <LoadSelectedChips
                  programId={programId}
                  levelLabel={'Course'}
                  key={programId}
                  programName={reduxProgramDetails.getIn([programId, 'program_name'], '')}
                  onDelete={handleChipClose}
                  selectedCourseCount={program.get('selectedProgramCount', 0)}
                />
              );
            })}
          </div>
        </div>
      </DialogContent>
      <DialogActions
        sx={{
          padding: '16px 24px',
        }}
      >
        <Button
          clicked={handleClose}
          variant="outlined"
          className="px-4"
          size={'small'}
          color={'gray'}
        >
          Cancel
        </Button>
        <Button
          clicked={handleCreate}
          variant="contained"
          className="px-4"
          color="primary"
          size={'small'}
        >
          {existingData.has('formId') ? 'Save' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

FormConfigurationCourseWise.propTypes = {
  existingData: PropTypes.instanceOf(IMap),
  handleClose: PropTypes.func,
  params: PropTypes.string,
  formType: PropTypes.string,
};

function FormCurriculum({
  programId,
  pgmDetailsState,
  deletedManipulatedIds,
  setDeletedManipulatedIds,
  setPgmDetailsState,
  domLoopPgmDetails,
}) {
  const curriculum = domLoopPgmDetails.getIn([programId, 'curriculum'], IMap()).entrySeq();
  const handleChangeCurriculum = (_, newValue) => {
    setCurriculumValue(newValue);
  };
  const [curriculumValue, setCurriculumValue] = React.useState(0);

  if (!curriculum.size) {
    return <div className="deepak_developer">No Data...</div>;
  }
  return (
    <TabContext value={curriculumValue}>
      <TabList sx={form_configure_tab} onChange={handleChangeCurriculum}>
        {curriculum.map(([curId, cur], curIndex) => (
          <Tab
            key={curId}
            label={cur.get('curriculum_name', '')}
            value={curIndex}
            sx={textTransform}
          />
        ))}
      </TabList>
      {curriculum.map(([curId, cur], curIndex) => {
        return (
          <TabPanel sx={tabPadding} value={curIndex} key={curId}>
            <FormYear
              cur={cur}
              key={curId}
              deletedManipulatedIds={deletedManipulatedIds}
              setDeletedManipulatedIds={setDeletedManipulatedIds}
              programId={programId}
              curId={curId}
              pgmDetailsState={pgmDetailsState}
              setPgmDetailsState={setPgmDetailsState}
            />
          </TabPanel>
        );
      })}
    </TabContext>
  );
}
FormCurriculum.propTypes = {
  pgmDetailsState: PropTypes.instanceOf(IMap),
  domLoopPgmDetails: PropTypes.instanceOf(IMap),
  deletedManipulatedIds: PropTypes.instanceOf(IMap),
  setDeletedManipulatedIds: PropTypes.func,
  setPgmDetailsState: PropTypes.func,
  programId: PropTypes.string,
};

export function FormYear({
  cur,
  curId,
  programId,
  pgmDetailsState,
  deletedManipulatedIds,
  setDeletedManipulatedIds,
  setPgmDetailsState,
}) {
  const [yearValue, setYearValue] = React.useState(0);
  const handleChangeYear = (_, newValue) => {
    setYearValue(newValue);
  };
  const years = cur.get('years', IMap()).entrySeq();

  if (!years.size) {
    return <div className="deepak_developer">No Data...</div>;
  }

  return (
    <TabContext value={yearValue}>
      {/* <TabList sx={form_configure_tab} onChange={handleChangeYear}> */}
      <Tabs
        value={yearValue}
        onChange={handleChangeYear}
        variant="scrollable"
        scrollButtons
        aria-label="visible arrows tabs example"
        sx={{
          [`& .${tabsClasses.scrollButtons}`]: {
            '&.Mui-disabled': { opacity: 0.3, display: 'none' },
          },
          '&.MuiTabs-root': {
            borderBottom: 'none !important',
          },
        }}
      >
        {years.map(([yearNo], yIndex) => (
          <Tab label={formatYear(yearNo)} value={yIndex} sx={textTransform} key={yearNo} />
        ))}
      </Tabs>
      {/* </TabList> */}

      {years.map(([yearNo, year], yIndex) => (
        <TabPanel sx={tabPadding} value={yIndex} key={curId + yearNo}>
          <FormCourse
            key={curId + yearNo}
            curId={curId}
            programId={programId}
            year={year}
            deletedManipulatedIds={deletedManipulatedIds}
            setDeletedManipulatedIds={setDeletedManipulatedIds}
            yearNo={yearNo}
            pgmDetailsState={pgmDetailsState}
            setPgmDetailsState={setPgmDetailsState}
          />
        </TabPanel>
      ))}
    </TabContext>
  );
}
FormYear.propTypes = {
  cur: PropTypes.instanceOf(IMap),
  pgmDetailsState: PropTypes.instanceOf(IMap),
  deletedManipulatedIds: PropTypes.instanceOf(IMap),
  setDeletedManipulatedIds: PropTypes.func,
  setPgmDetailsState: PropTypes.func,
  curId: PropTypes.string,
  programId: PropTypes.string,
};
function FormCourse({
  year,
  yearNo,
  programId,
  curId,
  pgmDetailsState,
  deletedManipulatedIds,
  setDeletedManipulatedIds,
  setPgmDetailsState,
}) {
  const courseIds = year.get('courseIds', IMap()).entrySeq();
  const gettingYearKey = [programId, 'curriculum', curId, 'years', yearNo];

  if (!courseIds.size) {
    return <div className="deepak_developer">No Data...</div>;
  }
  function handleAllCourse(e) {
    const checked = e.target.checked;
    setPgmDetailsState((prev) =>
      prev
        .setIn(
          [...gettingYearKey, 'selectedCourseCount'],
          checked ? prev.getIn([...gettingYearKey, 'constructedCourseCount'], 0) : 0
        )
        .updateIn([...gettingYearKey, 'courseIds'], IMap(), (courseIds) =>
          courseIds.map((cType) => cType.map((course) => course.set('isChecked', checked)))
        )
        .update(programId, (program) => {
          let increment = 0;
          program
            .get('curriculum', IMap())
            .map((cur) =>
              cur
                .get('years', IMap())
                .map((year) => (increment += year.get('selectedCourseCount', 0)))
            );
          return program.set('selectedProgramCount', increment);
        })
    );
    if (setDeletedManipulatedIds) {
      let edited = false;
      let deletedManipulatedIdsInstance = deletedManipulatedIds;
      //eslint-disable-next-line
      for (const [course_type, courseList] of courseIds) {
        for (let index = 0; index < courseList.size; index++) {
          const _manipulate_id = pgmDetailsState.getIn(
            [...gettingYearKey, 'courseIds', course_type, index, '_manipulate_id'],
            ''
          );
          if (_manipulate_id) {
            if (checked) {
              deletedManipulatedIdsInstance = deletedManipulatedIdsInstance.deleteIn([
                programId,
                _manipulate_id,
              ]);
            } else {
              deletedManipulatedIdsInstance = deletedManipulatedIdsInstance.setIn(
                [programId, _manipulate_id],
                _manipulate_id
              );
            }
            edited = true;
          }
        }
      }
      if (edited) {
        setDeletedManipulatedIds(deletedManipulatedIdsInstance);
      }
    }
  }

  const handleIndividualCourse = (courseType, index, course) => (e) => {
    const checked = e.target.checked;
    setPgmDetailsState((prev) =>
      prev
        .updateIn([...gettingYearKey, 'selectedCourseCount'], 0, (selectedCourseCount) => {
          if (checked) {
            return selectedCourseCount + 1;
          }
          return selectedCourseCount - 1;
        })
        .updateIn([...gettingYearKey, 'courseIds', courseType, index], IMap(), (course) =>
          course.set('isChecked', checked)
        )
        .updateIn(
          [programId, 'selectedProgramCount'],
          0,
          (count) => (count = checked ? count + 1 : count - 1)
        )
    );
    const _manipulate_id = pgmDetailsState.getIn(
      [...gettingYearKey, 'courseIds', courseType, index, '_manipulate_id'],
      ''
    );
    if (_manipulate_id) {
      setDeletedManipulatedIds((prev) => {
        if (checked) {
          prev = prev.deleteIn([programId, _manipulate_id]);
        } else {
          prev = prev.setIn([programId, _manipulate_id], _manipulate_id);
        }
        return prev;
      });
    }
  };
  const findingIndividualCourseIsChecked = (courseId, courseType) => {
    const course =
      pgmDetailsState
        .getIn([...gettingYearKey, 'courseIds', courseType], List())
        .find((course) => course.get('courseId', '') === courseId) || IMap();
    return course.get('isChecked', false);
  };
  return (
    <div>
      {courseIds.map(([courseType, courseList], cIndex) => {
        return (
          <Fragment key={curId + yearNo + courseType}>
            <div className="d-flex align-items-center mt-2">
              <div className="f-12 fw-400 text-lGrey ">{jsUcfirstAll(courseType)} Courses</div>
              {cIndex === 0 ? (
                <div className="ml-auto">
                  <div className="d-flex align-items-center">
                    <Checkbox
                      size="small"
                      checked={
                        pgmDetailsState.getIn([...gettingYearKey, 'selectedCourseCount'], -1) ===
                        pgmDetailsState.getIn([...gettingYearKey, 'constructedCourseCount'], 0)
                      }
                      onChange={handleAllCourse}
                    />
                    <div className="f-12 fw-400 text-dGrey">All Course</div>
                  </div>
                </div>
              ) : (
                <></>
              )}
            </div>

            {courseList.map((course, index) => (
              <Fragment key={curId + yearNo + courseType + course.get('courseId', index)}>
                <div className="d-flex align-items-center ">
                  <div className="mx-2">
                    <SubdirectoryArrowRightIcon
                      color="primary"
                      className="cursor-pointer"
                      sx={{ fontSize: 10 }}
                    />
                  </div>
                  <div className="d-flex align-items-center">
                    <div>
                      {/* <input
                        type="checkbox"
                        checked={findingIndividualCourseIsChecked(
                          course.get('courseId', ''),
                          courseType
                        )}
                        onChange={handleIndividualCourse(courseType, index, course)}
                      /> */}

                      {/* <input
                        type="checkbox"
                        onChange={handleIndividualCourse(courseType, index, course)}
                        size="small"
                        checked={findingIndividualCourseIsChecked(
                          course.get('courseId', ''),
                          courseType
                        )}
                      /> */}

                      <Checkbox
                        onChange={handleIndividualCourse(courseType, index, course)}
                        size="small"
                        checked={findingIndividualCourseIsChecked(
                          course.get('courseId', ''),
                          courseType
                        )}
                      />
                    </div>
                    {course.get('shared_from', false) && (
                      <div className="mx-2">
                        <img src={shared_icon} alt="shared_icon" />
                      </div>
                    )}
                    <div className="d-flex align-items-center">
                      {jsUcfirstAll(courseType) === 'Shared From' && (
                        <ShareOutlinedIcon color="primary" sx={{ fontSize: 15 }} className="mr-2" />
                      )}
                      <div className="f-14">
                        {course.get('course_code', '')} -{' '}
                        {jsUcfirstAll(course.get('course_name', ''))}
                      </div>
                      {course.get('shared_with_others', false) && (
                        <div className="ml-2">
                          <img src={shared_icon} alt="shared_icon" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {courseList.size - 1 === index && courseIds.size - 1 === cIndex ? (
                  <></>
                ) : (
                  <Divider />
                )}
              </Fragment>
            ))}
          </Fragment>
        );
      })}
    </div>
  );
}
FormCourse.propTypes = {
  year: PropTypes.instanceOf(IMap),
  pgmDetailsState: PropTypes.instanceOf(IMap),
  deletedManipulatedIds: PropTypes.instanceOf(IMap),
  setDeletedManipulatedIds: PropTypes.func,
  setPgmDetailsState: PropTypes.func,
  curId: PropTypes.string,
  yearNo: PropTypes.string,
  programId: PropTypes.string,
};
