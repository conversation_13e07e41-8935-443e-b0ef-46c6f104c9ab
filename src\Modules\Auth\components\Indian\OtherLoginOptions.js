import React from 'react';
import { useDispatch } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { authLogin } from '_reduxapi/actions';
import MicrosoftTeamsLogin from '../MicrosoftTeamsLogin';
import LoginWithGoogle from '../GoogleLogin';
import { GOOGLE_CLIENT_ID } from 'constants';

const OtherLoginOptions = ({ unifyData }) => {
  const dispatch = useDispatch();
  const history = useHistory();

  const loginCallback = (requestData) => {
    const payload = { device_type: 'web', ...requestData };
    dispatch(authLogin(payload, history));
  };

  if (!unifyData.get('sso', false)) return null;
  return (
    <>
      <div className="text-center pt-2 forgot-password">Or</div>
      {unifyData.get('ssoProvider', '') === 'teams' && (
        <MicrosoftTeamsLogin callback={loginCallback} />
      )}
      {unifyData.get('ssoProvider', '') === 'google' && (
        <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
          <LoginWithGoogle callback={loginCallback} />
        </GoogleOAuthProvider>
      )}
    </>
  );
};

export default OtherLoginOptions;
