import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import MaterialInput from 'Widgets/FormElements/material/Input';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import CancelModal from 'Containers/Modal/Cancel';
import { List, Map } from 'immutable';
import { documentValidation } from '../utils';

function CategoryModal({
  open,
  close,
  settingId,
  state,
  setOpen,
  postDocumentCategory,
  categoryId,
  updateDocumentCategory,
  documents,
  setData,
  handleCallBack,
  type,
}) {
  const [name, setName] = useState(state.get('documentCategory', ''));
  const [description, setDescription] = useState(state.get('documentCategoryDescription', ''));
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [edited, setEdited] = useState(false);
  const [namesArray, setNamesArray] = useState(List);

  useEffect(() => {
    let tempNamesArray = documents.get('chooseDocuments', List()).map((item, index) => {
      return item.get('documentCategory', '').toLowerCase();
    });
    tempNamesArray.size > 0 && setNamesArray(tempNamesArray);
  }, [documents]);

  const handleChange = (e, field) => {
    setEdited(true);
    if (field === 'name') {
      setName(e.target.value);
    } else {
      setDescription(e.target.value);
    }
  };
  const editCallBack = () => {
    close();
  };
  const handleCancel = () => {
    edited ? setShowCancelModal(true) : setOpen(false);
  };
  const handleSubmit = () => {
    const newNamesArray = namesArray.filter(
      (item) => item !== state.get('documentCategory', '').toLowerCase()
    );
    state.size > 0
      ? documentValidation(name, newNamesArray, setData, 'Category') &&
        updateDocumentCategory({
          operation: 'update',
          settingId,
          name,
          description,
          callBack: editCallBack,
          id: categoryId,
          type,
        })
      : documentValidation(name, namesArray, setData, 'Category') &&
        postDocumentCategory(
          {
            settingId,
            documentCategory: name,
            ...(description && { documentCategoryDescription: description }),
          },
          editCallBack,
          type
        );
  };
  return (
    <>
      <DialogModal show={open} onClose={handleCancel}>
        <div className="p-4">
          <div className="mb-2">
            <p className="f-22"> {state.size > 0 ? 'Edit Category' : 'Add New Category'}</p>
            <div>
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                label={'Category Name'}
                labelclass={'mb-1 f-15'}
                changed={(e) => handleChange(e, 'name')}
                value={name}
                maxLength={40}
              />
            </div>
            <div>
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                label={'Description'}
                labelclass={'mb-1 f-15'}
                changed={(e) => handleChange(e, 'description')}
                value={description}
                maxLength={50}
              />
            </div>
          </div>

          <div className="mt-2 text-right">
            <MButton className="mr-3" color="inherit" variant="outlined" clicked={handleCancel}>
              <Trans i18nKey={'cancel'}></Trans>
            </MButton>
            <MButton
              color="primary"
              variant="contained"
              clicked={() => handleSubmit()}
              disabled={!name.trim() || !edited}
            >
              <Trans i18nKey={'save'}></Trans>
            </MButton>
          </div>
        </div>
      </DialogModal>
      {showCancelModal && (
        <CancelModal
          showCancel={showCancelModal}
          setCancel={setShowCancelModal}
          setShow={setOpen(false)}
        />
      )}{' '}
    </>
  );
}

CategoryModal.propTypes = {
  open: PropTypes.bool,
  close: PropTypes.func,
  postDocumentCategory: PropTypes.func,
  getDesignations: PropTypes.func,
  settingId: PropTypes.string,
  type: PropTypes.string,
  state: PropTypes.instanceOf(Map),
  documents: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  getDocumentConfig: PropTypes.func,
  updateDocumentCategory: PropTypes.func,
  categoryId: PropTypes.string,
  setOpen: PropTypes.func,
  handleCallBack: PropTypes.func,
};

export default CategoryModal;
