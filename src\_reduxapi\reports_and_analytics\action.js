import { createAction } from '../util';
import axios from '../../axios';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const GET_REPORT_DASHBOARD_DATA_REQUEST = 'GET_REPORT_DASHBOARD_DATA_REQUEST';
export const GET_REPORT_DASHBOARD_DATA_SUCCESS = 'GET_REPORT_DASHBOARD_DATA_SUCCESS';
export const GET_REPORT_DASHBOARD_DATA_FAILURE = 'GET_REPORT_DASHBOARD_DATA_FAILURE';

const getDashboardDataRequest = createAction(GET_REPORT_DASHBOARD_DATA_REQUEST);
const getDashboardDataSuccess = createAction(GET_REPORT_DASHBOARD_DATA_SUCCESS, 'data');
const getDashboardDataFailure = createAction(GET_REPORT_DASHBOARD_DATA_FAILURE, 'error');

export function getDashboardData({ institutionCalendarId, userId, roleId }) {
  return function (dispatch) {
    dispatch(getDashboardDataRequest());
    axios
      .get(`/reports_analytics/dashboard/${institutionCalendarId}/${userId}/${roleId}`)
      .then((res) => dispatch(getDashboardDataSuccess(res.data.data)))
      .catch((error) => dispatch(getDashboardDataFailure(error)));
  };
}

export const GET_ACADEMIC_YEAR_LEVEL_DATA_REQUEST = 'GET_ACADEMIC_YEAR_LEVEL_DATA_REQUEST';
export const GET_ACADEMIC_YEAR_LEVEL_DATA_SUCCESS = 'GET_ACADEMIC_YEAR_LEVEL_DATA_SUCCESS';
export const GET_ACADEMIC_YEAR_LEVEL_DATA_FAILURE = 'GET_ACADEMIC_YEAR_LEVEL_DATA_FAILURE';

const getAcademicYearLevelDataRequest = createAction(GET_ACADEMIC_YEAR_LEVEL_DATA_REQUEST);
const getAcademicYearLevelDataSuccess = createAction(GET_ACADEMIC_YEAR_LEVEL_DATA_SUCCESS, 'data');
const getAcademicYearLevelDataFailure = createAction(GET_ACADEMIC_YEAR_LEVEL_DATA_FAILURE, 'error');

export function getAcademicYearLevelData({ institutionCalendarId, programId }) {
  return function (dispatch) {
    dispatch(getAcademicYearLevelDataRequest());
    axios
      .get(`/reports_analytics/program/${institutionCalendarId}/${programId}`)
      .then((res) => dispatch(getAcademicYearLevelDataSuccess(res.data.data)))
      .catch((error) => dispatch(getAcademicYearLevelDataFailure(error)));
  };
}

export const GET_ALL_STUDENT_DETAILS_REQUEST = 'GET_ALL_STUDENT_DETAILS_REQUEST';
export const GET_ALL_STUDENT_DETAILS_SUCCESS = 'GET_ALL_STUDENT_DETAILS_SUCCESS';
export const GET_ALL_STUDENT_DETAILS_FAILURE = 'GET_ALL_STUDENT_DETAILS_FAILURE';

const getAllStudentDetailsRequest = createAction(GET_ALL_STUDENT_DETAILS_REQUEST);
const getAllStudentDetailsSuccess = createAction(GET_ALL_STUDENT_DETAILS_SUCCESS, 'data');
const getAllStudentDetailsFailure = createAction(GET_ALL_STUDENT_DETAILS_FAILURE, 'error');

export function getAllStudentDetails({ institutionCalendarId, programId }) {
  return function (dispatch) {
    dispatch(getAllStudentDetailsRequest());
    axios
      .get(`/reports_analytics/student_list/${institutionCalendarId}/${programId}`)
      .then((res) => dispatch(getAllStudentDetailsSuccess(res.data.data)))
      .catch((error) => dispatch(getAllStudentDetailsFailure(error)));
  };
}

export const GET_STUDENT_DETAILS_BY_ID_REQUEST = 'GET_STUDENT_DETAILS_BY_ID_REQUEST';
export const GET_STUDENT_DETAILS_BY_ID_SUCCESS = 'GET_STUDENT_DETAILS_BY_ID_SUCCESS';
export const GET_STUDENT_DETAILS_BY_ID_FAILURE = 'GET_STUDENT_DETAILS_BY_ID_FAILURE';

const getStudentDetailsByIdRequest = createAction(GET_STUDENT_DETAILS_BY_ID_REQUEST);
const getStudentDetailsByIdSuccess = createAction(GET_STUDENT_DETAILS_BY_ID_SUCCESS, 'data');
const getStudentDetailsByIdFailure = createAction(GET_STUDENT_DETAILS_BY_ID_FAILURE, 'error');

export function getStudentDetailsById({ institutionCalendarId, programId, studentId }) {
  return function (dispatch) {
    dispatch(getStudentDetailsByIdRequest());
    axios
      .get(`/reports_analytics/student_details/${institutionCalendarId}/${programId}/${studentId}`)
      .then((res) => dispatch(getStudentDetailsByIdSuccess(res.data.data)))
      .catch((error) => dispatch(getStudentDetailsByIdFailure(error)));
  };
}

export const GET_DEPT_SUB_OVERVIEW_REQUEST = 'GET_DEPT_SUB_OVERVIEW_REQUEST';
export const GET_DEPT_SUB_OVERVIEW_SUCCESS = 'GET_DEPT_SUB_OVERVIEW_SUCCESS';
export const GET_DEPT_SUB_OVERVIEW_FAILURE = 'GET_DEPT_SUB_OVERVIEW_FAILURE';

const getDeptSubOverviewRequest = createAction(GET_DEPT_SUB_OVERVIEW_REQUEST);
const getDeptSubOverviewSuccess = createAction(GET_DEPT_SUB_OVERVIEW_SUCCESS, 'data');
const getDeptSubOverviewFailure = createAction(GET_DEPT_SUB_OVERVIEW_FAILURE, 'error');

export function getDeptSubOverview({ institutionCalendarId, programId }) {
  return function (dispatch) {
    dispatch(getDeptSubOverviewRequest());
    axios
      .get(`/reports_analytics/department_subject_list/${institutionCalendarId}/${programId}`)
      .then((res) => dispatch(getDeptSubOverviewSuccess(res.data.data)))
      .catch((error) => dispatch(getDeptSubOverviewFailure(error)));
  };
}

export const GET_DEPT_SUB_OVERVIEW_COURSE_DATA_REQUEST =
  'GET_DEPT_SUB_OVERVIEW_COURSE_DATA_REQUEST';
export const GET_DEPT_SUB_OVERVIEW_COURSE_DATA_SUCCESS =
  'GET_DEPT_SUB_OVERVIEW_COURSE_DATA_SUCCESS';
export const GET_DEPT_SUB_OVERVIEW_COURSE_DATA_FAILURE =
  'GET_DEPT_SUB_OVERVIEW_COURSE_DATA_FAILURE';

const getDeptSubOverviewCourseDataRequest = createAction(GET_DEPT_SUB_OVERVIEW_COURSE_DATA_REQUEST);
const getDeptSubOverviewCourseDataSuccess = createAction(
  GET_DEPT_SUB_OVERVIEW_COURSE_DATA_SUCCESS,
  'data'
);
const getDeptSubOverviewCourseDataFailure = createAction(
  GET_DEPT_SUB_OVERVIEW_COURSE_DATA_FAILURE,
  'error'
);

export function getDeptSubOverviewCourseData({
  institutionCalendarId,
  programId,
  departmentId,
  subjectId,
  callBack,
}) {
  return function (dispatch) {
    dispatch(getDeptSubOverviewCourseDataRequest());
    axios
      .get(
        `/reports_analytics/department_subject/${institutionCalendarId}/${programId}/${departmentId}/${subjectId}`
      )
      .then((res) => {
        dispatch(getDeptSubOverviewCourseDataSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(getDeptSubOverviewCourseDataFailure(error));
        callBack && callBack();
      });
  };
}

export const GET_ALL_STAFF_DETAILS_REQUEST = 'GET_ALL_STAFF_DETAILS_REQUEST';
export const GET_ALL_STAFF_DETAILS_SUCCESS = 'GET_ALL_STAFF_DETAILS_SUCCESS';
export const GET_ALL_STAFF_DETAILS_FAILURE = 'GET_ALL_STAFF_DETAILS_FAILURE';

const getAllStaffDetailsRequest = createAction(GET_ALL_STAFF_DETAILS_REQUEST);
const getAllStaffDetailsSuccess = createAction(GET_ALL_STAFF_DETAILS_SUCCESS, 'data');
const getAllStaffDetailsFailure = createAction(GET_ALL_STAFF_DETAILS_FAILURE, 'error');

export function getAllStaffDetails({ institutionCalendarId, programId }) {
  return function (dispatch) {
    dispatch(getAllStaffDetailsRequest());
    axios
      .get(`/reports_analytics/program_staff_list/${institutionCalendarId}/${programId}`)
      .then((res) => dispatch(getAllStaffDetailsSuccess(res.data.data)))
      .catch((error) => dispatch(getAllStaffDetailsFailure(error)));
  };
}

export const GET_STAFF_DETAILS_BY_ID_REQUEST = 'GET_STAFF_DETAILS_BY_ID_REQUEST';
export const GET_STAFF_DETAILS_BY_ID_SUCCESS = 'GET_STAFF_DETAILS_BY_ID_SUCCESS';
export const GET_STAFF_DETAILS_BY_ID_FAILURE = 'GET_STAFF_DETAILS_BY_ID_FAILURE';

const getStaffDetailsByIdRequest = createAction(GET_STAFF_DETAILS_BY_ID_REQUEST);
const getStaffDetailsByIdSuccess = createAction(GET_STAFF_DETAILS_BY_ID_SUCCESS, 'data');
const getStaffDetailsByIdFailure = createAction(GET_STAFF_DETAILS_BY_ID_FAILURE, 'error');

export function getStaffDetailsById({ institutionCalendarId, programId, staffId }) {
  return function (dispatch) {
    dispatch(getStaffDetailsByIdRequest());
    axios
      .get(`/reports_analytics/staff_details/${institutionCalendarId}/${programId}/${staffId}`)
      .then((res) => dispatch(getStaffDetailsByIdSuccess(res.data.data)))
      .catch((error) => dispatch(getStaffDetailsByIdFailure(error)));
  };
}

export const GET_COURSE_OVERVIEW_DATA_REQUEST = 'GET_COURSE_OVERVIEW_DATA_REQUEST';
export const GET_COURSE_OVERVIEW_DATA_SUCCESS = 'GET_COURSE_OVERVIEW_DATA_SUCCESS';
export const GET_COURSE_OVERVIEW_DATA_FAILURE = 'GET_COURSE_OVERVIEW_DATA_FAILURE';

const getCourseOverviewDataRequest = createAction(GET_COURSE_OVERVIEW_DATA_REQUEST);
const getCourseOverviewDataSuccess = createAction(GET_COURSE_OVERVIEW_DATA_SUCCESS, 'data');
const getCourseOverviewDataFailure = createAction(GET_COURSE_OVERVIEW_DATA_FAILURE, 'error');

export function getCourseOverviewData({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  rotationCount,
}) {
  return function (dispatch) {
    dispatch(getCourseOverviewDataRequest());
    axios
      .get(
        `/reports_analytics/course_overview/${institutionCalendarId}/${programId}/${courseId}/${level}/${term}`,
        { params: rotationCount ? { rotation_count: rotationCount } : {} }
      )
      .then((res) => dispatch(getCourseOverviewDataSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseOverviewDataFailure(error)));
  };
}

export const GET_COURSE_SESSION_STATUS_DATA_REQUEST = 'GET_COURSE_SESSION_STATUS_DATA_REQUEST';
export const GET_COURSE_SESSION_STATUS_DATA_SUCCESS = 'GET_COURSE_SESSION_STATUS_DATA_SUCCESS';
export const GET_COURSE_SESSION_STATUS_DATA_FAILURE = 'GET_COURSE_SESSION_STATUS_DATA_FAILURE';

const getCourseSessionStatusDataRequest = createAction(GET_COURSE_SESSION_STATUS_DATA_REQUEST);
const getCourseSessionStatusDataSuccess = createAction(
  GET_COURSE_SESSION_STATUS_DATA_SUCCESS,
  'data'
);
const getCourseSessionStatusDataFailure = createAction(
  GET_COURSE_SESSION_STATUS_DATA_FAILURE,
  'error'
);

export function getCourseSessionStatusData({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  rotationCount,
}) {
  return function (dispatch) {
    dispatch(getCourseSessionStatusDataRequest());
    axios
      .get(
        `/reports_analytics/course_sessions/${institutionCalendarId}/${programId}/${courseId}/${term}/regular/${level}`,
        { params: rotationCount ? { rotation_count: rotationCount } : {} }
      )
      .then((res) => dispatch(getCourseSessionStatusDataSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseSessionStatusDataFailure(error)));
  };
}

export const GET_COURSE_STUDENT_DETAILS_REQUEST = 'GET_COURSE_STUDENT_DETAILS_REQUEST';
export const GET_COURSE_STUDENT_DETAILS_SUCCESS = 'GET_COURSE_STUDENT_DETAILS_SUCCESS';
export const GET_COURSE_STUDENT_DETAILS_FAILURE = 'GET_COURSE_STUDENT_DETAILS_FAILURE';

const getCourseStudentDetailsRequest = createAction(GET_COURSE_STUDENT_DETAILS_REQUEST);
const getCourseStudentDetailsSuccess = createAction(GET_COURSE_STUDENT_DETAILS_SUCCESS, 'data');
const getCourseStudentDetailsFailure = createAction(GET_COURSE_STUDENT_DETAILS_FAILURE, 'error');

export function getCourseStudentDetails({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  rotationCount,
}) {
  return function (dispatch) {
    dispatch(getCourseStudentDetailsRequest());
    axios
      .get(
        `/reports_analytics/student_details/${institutionCalendarId}/${programId}/${courseId}/${term}/regular/${level}`,
        { params: rotationCount ? { rotation_count: rotationCount } : {} }
      )
      .then((res) => dispatch(getCourseStudentDetailsSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseStudentDetailsFailure(error)));
  };
}

export const GET_COURSE_STAFF_DETAILS_REQUEST = 'GET_COURSE_STAFF_DETAILS_REQUEST';
export const GET_COURSE_STAFF_DETAILS_SUCCESS = 'GET_COURSE_STAFF_DETAILS_SUCCESS';
export const GET_COURSE_STAFF_DETAILS_FAILURE = 'GET_COURSE_STAFF_DETAILS_FAILURE';

const getCourseStaffDetailsRequest = createAction(GET_COURSE_STAFF_DETAILS_REQUEST);
const getCourseStaffDetailsSuccess = createAction(GET_COURSE_STAFF_DETAILS_SUCCESS, 'data');
const getCourseStaffDetailsFailure = createAction(GET_COURSE_STAFF_DETAILS_FAILURE, 'error');

export function getCourseStaffDetails({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  rotationCount,
}) {
  return function (dispatch) {
    dispatch(getCourseStaffDetailsRequest());
    axios
      .get(
        `/reports_analytics/staff_details/${institutionCalendarId}/${programId}/${courseId}/${term}/regular/${level}`,
        { params: rotationCount ? { rotation_count: rotationCount } : {} }
      )
      .then((res) => dispatch(getCourseStaffDetailsSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseStaffDetailsFailure(error)));
  };
}
export const GET_ACTIVITY_INFO_REQUEST = 'GET_ACTIVITY_INFO_REQUEST';
export const GET_ACTIVITY_INFO_SUCCESS = 'GET_ACTIVITY_INFO_SUCCESS';
export const GET_ACTIVITY_INFO_FAILURE = 'GET_ACTIVITY_INFO_FAILURE';

const getActivityInfoRequest = createAction(GET_ACTIVITY_INFO_REQUEST);
const getActivityInfoSuccess = createAction(GET_ACTIVITY_INFO_SUCCESS, 'data');
const getActivityInfoFailure = createAction(GET_ACTIVITY_INFO_FAILURE, 'error');

export function getActivityInfo({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  rotationCount,
  activityType,
  userType,
}) {
  return function (dispatch) {
    dispatch(getActivityInfoRequest());
    axios
      .get(
        `/reports_analytics/activity_report/${institutionCalendarId}/${programId}/${courseId}/${level}/${term}/${userType}/${activityType}`,
        { params: rotationCount ? { rotation_count: rotationCount } : {} }
      )
      .then((res) => dispatch(getActivityInfoSuccess(res.data.data)))
      .catch((error) => dispatch(getActivityInfoFailure(error)));
  };
}

export const GET_ATTENDANCE_REPORT_BY_USER_ID_REQUEST = 'GET_ATTENDANCE_REPORT_BY_USER_ID_REQUEST';
export const GET_ATTENDANCE_REPORT_BY_USER_ID_SUCCESS = 'GET_ATTENDANCE_REPORT_BY_USER_ID_SUCCESS';
export const GET_ATTENDANCE_REPORT_BY_USER_ID_FAILURE = 'GET_ATTENDANCE_REPORT_BY_USER_ID_FAILURE';

const getAttendanceReportByUserIdRequest = createAction(GET_ATTENDANCE_REPORT_BY_USER_ID_REQUEST);
const getAttendanceReportByUserIdSuccess = createAction(
  GET_ATTENDANCE_REPORT_BY_USER_ID_SUCCESS,
  'data'
);
const getAttendanceReportByUserIdFailure = createAction(
  GET_ATTENDANCE_REPORT_BY_USER_ID_FAILURE,
  'error'
);

export function getAttendanceReportByUserId({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  userId,
  userType,
  rotationCount,
}) {
  return function (dispatch) {
    dispatch(getAttendanceReportByUserIdRequest());
    axios
      .get(
        `/reports_analytics/course_attendance_report/${institutionCalendarId}/${programId}/${courseId}/${level}/${term}/${userId}/${userType}`,
        { params: rotationCount ? { rotation_count: rotationCount } : {} }
      )
      .then((res) => dispatch(getAttendanceReportByUserIdSuccess(res.data.data)))
      .catch((error) => dispatch(getAttendanceReportByUserIdFailure(error)));
  };
}

export const GET_ATTENDANCE_LOG_REQUEST = 'GET_ATTENDANCE_LOG_REQUEST';
export const GET_ATTENDANCE_LOG_SUCCESS = 'GET_ATTENDANCE_LOG_SUCCESS';
export const GET_ATTENDANCE_LOG_FAILURE = 'GET_ATTENDANCE_LOG_FAILURE';

const getAttendanceLogRequest = createAction(GET_ATTENDANCE_LOG_REQUEST);
const getAttendanceLogSuccess = createAction(GET_ATTENDANCE_LOG_SUCCESS, 'data');
const getAttendanceLogFailure = createAction(GET_ATTENDANCE_LOG_FAILURE, 'error');

export function getAttendanceLog({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  // rotationCount,
  // group,
  // groupChanged,
  params,
}) {
  return function (dispatch) {
    let url;
    // if (groupChanged) {
    //   url = `/reports_analytics/attendance_log/${institutionCalendarId}/${programId}/${courseId}/${term}/regular/${level}?groupName=${group}`;
    // } else {
    // url = `/reports_analytics/attendance_log/${institutionCalendarId}/${programId}/${courseId}/${term}/regular/${level}`;
    url = `/reports_analytics/course-attendance-log/${institutionCalendarId}/${programId}/${courseId}/${term}/regular/${level}?tableFilter[]=gender`;
    //}
    dispatch(getAttendanceLogRequest());
    // const params = {
    //   ...(rotationCount && { rotation_count: rotationCount }),
    //   tableFilter: 'gender',
    // };
    axios
      .get(url, { params })
      .then((res) => dispatch(getAttendanceLogSuccess(res.data.data)))
      .catch((error) => dispatch(getAttendanceLogFailure(error)));
  };
}

export const GET_REVIEW_QUIZ_REQUEST = 'GET_REVIEW_QUIZ_REQUEST';
export const GET_REVIEW_QUIZ_SUCCESS = 'GET_REVIEW_QUIZ_SUCCESS';
export const GET_REVIEW_QUIZ_FAILURE = 'GET_REVIEW_QUIZ_FAILURE';
const getReviewQuizRequest = createAction(GET_REVIEW_QUIZ_REQUEST, 'isLoading');
const getReviewQuizSuccess = createAction(GET_REVIEW_QUIZ_SUCCESS, 'data');
const getReviewQuizFailure = createAction(GET_REVIEW_QUIZ_FAILURE, 'error');
export function getReviewQuiz(user_Id, activity_id, type, callBack) {
  return function (dispatch) {
    dispatch(getReviewQuizRequest(true));
    axios
      .get(`/activities_v2/${activity_id}/view-result?type=${type}&userId=${user_Id}`)
      .then((res) => {
        dispatch(getReviewQuizSuccess(res.data));
        callBack && callBack();
      })
      .catch((error) => dispatch(getReviewQuizFailure(error)));
  };
}

export const GET_QUIZ_REQUEST = 'GET_QUIZ_REQUEST';
export const GET_QUIZ_SUCCESS = 'GET_QUIZ_SUCCESS';
export const GET_QUIZ_FAILURE = 'GET_QUIZ_FAILURE';

const getQuizRequest = createAction(GET_QUIZ_REQUEST, 'isLoading');
const getQuizSuccess = createAction(GET_QUIZ_SUCCESS, 'data');
const getQuizFailure = createAction(GET_QUIZ_FAILURE, 'error');
export function getQuiz(id, callBack = () => {}) {
  return function (dispatch) {
    dispatch(getQuizRequest(true));
    axios
      .get(`/activities_v2/${id}`)
      .then((res) => {
        dispatch(getQuizSuccess(res.data));
        callBack && callBack();
      })
      .catch((error) => dispatch(getQuizFailure(error)));
  };
}

export const GET_STUDENT_ACTIVITY_DATA_REQUEST = 'GET_STUDENT_ACTIVITY_DATA_REQUEST';
export const GET_STUDENT_ACTIVITY_DATA_SUCCESS = 'GET_STUDENT_ACTIVITY_DATA_SUCCESS';
export const GET_STUDENT_ACTIVITY_DATA_FAILURE = 'GET_STUDENT_ACTIVITY_DATA_FAILURE';
const getStudentsActivityDataRequest = createAction(GET_STUDENT_ACTIVITY_DATA_REQUEST, 'isLoading');
const getStudentsActivityDataSuccess = createAction(GET_STUDENT_ACTIVITY_DATA_SUCCESS, 'data');
const getStudentsActivityDataFailure = createAction(GET_STUDENT_ACTIVITY_DATA_FAILURE, 'error');
export function getStudentsActivityData(activity_id) {
  return function (dispatch) {
    dispatch(getStudentsActivityDataRequest(true));
    axios
      .get(`/activities_v2/${activity_id}/get-students`)
      .then((res) => dispatch(getStudentsActivityDataSuccess(res.data)))
      .catch((error) => dispatch(getStudentsActivityDataFailure(error)));
  };
}

export const GET_SLO_REQUEST = 'GET_SLO_REQUEST';
export const GET_SLO_SUCCESS = 'GET_SLO_SUCCESS';
export const GET_SLO_FAILURE = 'GET_SLO_FAILURE';
const getSloDataRequest = createAction(GET_SLO_REQUEST, 'isLoading');
const getSloDataSuccess = createAction(GET_SLO_SUCCESS, 'data');
const getSloDataFailure = createAction(GET_SLO_FAILURE, 'error');
export function getSloData(requestBody) {
  return function (dispatch) {
    dispatch(getSloDataRequest(true));
    axios
      .post(`reports_analytics/learning-outcomes/clo`, requestBody)
      .then((res) => dispatch(getSloDataSuccess(res.data)))
      .catch((error) => dispatch(getSloDataFailure(error)));
  };
}
export const GET_PLO_REQUEST = 'GET_PLO_REQUEST';
export const GET_PLO_SUCCESS = 'GET_PLO_SUCCESS';
export const GET_PLO_FAILURE = 'GET_PLO_FAILURE';
const getPloDataRequest = createAction(GET_PLO_REQUEST, 'isLoading');
const getPloDataSuccess = createAction(GET_PLO_SUCCESS, 'data');
const getPloDataFailure = createAction(GET_PLO_FAILURE, 'error');
export function getPloData(requestBody) {
  return function (dispatch) {
    dispatch(getPloDataRequest(true));
    axios
      .post(`reports_analytics/learning-outcomes/plo`, requestBody)
      .then((res) => dispatch(getPloDataSuccess(res.data)))
      .catch((error) => dispatch(getPloDataFailure(error)));
  };
}

export const GET_REPORT_DASHBOARD_DATA_TYPE_REQUEST = 'GET_REPORT_DASHBOARD_DATA_TYPE_REQUEST';
export const GET_REPORT_DASHBOARD_DATA_TYPE_SUCCESS = 'GET_REPORT_DASHBOARD_DATA_TYPE_SUCCESS';
export const GET_REPORT_DASHBOARD_DATA_TYPE_FAILURE = 'GET_REPORT_DASHBOARD_DATA_TYPE_FAILURE';

const getDashboardDataTypeRequest = createAction(
  GET_REPORT_DASHBOARD_DATA_TYPE_REQUEST,
  'dataType'
);
const getDashboardDataTypeSuccess = createAction(GET_REPORT_DASHBOARD_DATA_TYPE_SUCCESS, 'data');
const getDashboardDataTypeFailure = createAction(GET_REPORT_DASHBOARD_DATA_TYPE_FAILURE, 'error');

export function getDashboardDataType({ institutionCalendarId, userId, roleId, dataType }) {
  return function (dispatch) {
    dispatch(getDashboardDataTypeRequest(dataType));
    axios
      .get(`/reports_analytics/dashboard/${institutionCalendarId}/${userId}/${roleId}/${dataType}`)
      .then((res) => dispatch(getDashboardDataTypeSuccess({ data: res.data.data, dataType })))
      .catch((error) => dispatch(getDashboardDataTypeFailure({ error: error, dataType })));
  };
}

export const GET_SUMMARY_DATA_REQUEST = 'GET_SUMMARY_DATA_REQUEST';
export const GET_SUMMARY_DATA_SUCCESS = 'GET_SUMMARY_DATA_SUCCESS';
export const GET_SUMMARY_DATA_FAILURE = 'GET_SUMMARY_DATA_FAILURE';

const getSummaryDataRequest = createAction(GET_SUMMARY_DATA_REQUEST);
const getSummaryDataSuccess = createAction(GET_SUMMARY_DATA_SUCCESS, 'data');
const getSummaryDataFailure = createAction(GET_SUMMARY_DATA_FAILURE, 'error');

export function getSummaryData({ institutionCalendarId, programId }) {
  return function (dispatch) {
    dispatch(getSummaryDataRequest());
    axios
      .get(`/reports_analytics/program/${institutionCalendarId}/${programId}/summary`)
      .then((res) => dispatch(getSummaryDataSuccess(res.data.data)))
      .catch((error) => dispatch(getSummaryDataFailure(error)));
  };
}

export const GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_REQUEST =
  'GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_REQUEST';
export const GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_SUCCESS =
  'GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_SUCCESS';
export const GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_FAILURE =
  'GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_FAILURE';

const getAcademicReportYearLevelDataRequest = createAction(
  GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_REQUEST
);
const getAcademicReportYearLevelDataSuccess = createAction(
  GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_SUCCESS,
  'data'
);
const getAcademicReportYearLevelDataFailure = createAction(
  GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_FAILURE,
  'error'
);

export function getAcademicReportYearLevelData({
  institutionCalendarId,
  programId,
  term,
  year,
  level,
}) {
  return function (dispatch) {
    dispatch(getAcademicReportYearLevelDataRequest());
    axios
      .get(
        `/reports_analytics/program/${institutionCalendarId}/${programId}/academic_report?term=${term}&year=${year}&level=${level}`
      )
      .then((res) => dispatch(getAcademicReportYearLevelDataSuccess(res.data.data)))
      .catch((error) => dispatch(getAcademicReportYearLevelDataFailure(error)));
  };
}

export const GET_ATTENDANCE_LOG_EXPORT_REQUEST = 'GET_ATTENDANCE_LOG_EXPORT_REQUEST';
export const GET_ATTENDANCE_LOG_EXPORT_SUCCESS = 'GET_ATTENDANCE_LOG_EXPORT_SUCCESS';
export const GET_ATTENDANCE_LOG_EXPORT_FAILURE = 'GET_ATTENDANCE_LOG_EXPORT_FAILURE';

const getAttendanceLogExportRequest = createAction(GET_ATTENDANCE_LOG_EXPORT_REQUEST);
const getAttendanceLogExportSuccess = createAction(GET_ATTENDANCE_LOG_EXPORT_SUCCESS, 'data');
const getAttendanceLogExportFailure = createAction(GET_ATTENDANCE_LOG_EXPORT_FAILURE, 'error');

export function getAttendanceLogExport({
  institutionCalendarId,
  programId,
  courseId,
  level,
  term,
  params,
  exportData,
}) {
  return function (dispatch) {
    let url = `/reports_analytics/course-attendance-log-export/${institutionCalendarId}/${programId}/${courseId}/${term}/regular/${level}`;
    dispatch(getAttendanceLogExportRequest());
    axios
      .get(url, { params })
      .then((res) => {
        dispatch(getAttendanceLogExportSuccess(res.data.data));
        exportData(res.data.data);
      })
      .catch((error) => {
        dispatch(getAttendanceLogExportFailure(error));
        exportData({});
      });
  };
}
