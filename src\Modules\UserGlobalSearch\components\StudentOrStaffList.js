import React, { useEffect, useState, lazy, Suspense } from 'react';
import { useParams } from 'react-router-dom';
import { useHistory } from 'react-router-dom';

import { Trans } from 'react-i18next';

import Typography from '@mui/material/Typography';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CircularProgress from '@mui/material/CircularProgress';
import { circularProgressClasses } from '@mui/material/CircularProgress';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';

import { t } from 'i18next';
import { fromJS, Map } from 'immutable';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import { getUserCourseDetails } from '_reduxapi/UserGlobalSearch/action';
import { selectUserCourseDetails } from '_reduxapi/UserGlobalSearch/selectors';
import {
  dString,
  capitalize,
  getFormattedCourseDuration,
  getVersionName,
  isModuleEnabled,
} from 'utils';
import { selectStudentAttnSheetDetails } from '_reduxapi/leave_management/selectors';
import {
  setData,
  updateReason,
  getStudentAttendanceSheetDetails,
} from '_reduxapi/leave_management/actions';
import { fixedTwoDigit } from 'Modules/LeaveManagement/utils';
import NewStudentAttendanceModal from 'Modules/LeaveManagement/modal/NewStudentAttendanceModal';
import AddDisciplinary from '../Models/AddDisciplinaryRemarks.js';
import useAuthHook from 'Hooks/useAuthHook.js';

const LevelsListComponent = lazy(() => import('./LevelsTab'));

//----------------------------------UI Utils Start--------------------------------------------------
const paperBoxSizeSm = {
  display: 'flex',
  flexWrap: 'wrap',
  backgroundColor: '#F3F4F6',
  '& > :not(style)': {
    margin: '20px',
    width: '100%',
  },
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const COURSE_STATUS_INFO = Map({
  'not-started': Map({ label: t('reports_analytics.not_started'), class: 'bg-danger' }),
  'in-progress': Map({ label: t('reports_analytics.on_going'), class: 'bg-warning' }),
  completed: Map({ label: t('complete'), class: 'bg-green' }),
});
const getCompleteStatus = (staffDetails) => {
  const completedSessions = staffDetails.get('totalCompletedSchedule', '');
  const totalSessions = staffDetails.get('totalSchedules', '');
  const endDate = staffDetails.getIn(['courseStartAndEndDate', 'endDate'], new Date());
  const endDateParse = Date.parse(endDate);
  const currentDateParse = Date.parse(new Date());
  const isExpired = endDateParse < currentDateParse;
  return isExpired && completedSessions !== 0 && totalSessions !== completedSessions
    ? t('reports_analytics.session_incomplete')
    : '';
};
const getSessionStatus = (course) => {
  const endDate = course.getIn(['courseStartAndEndDate', 'endDate'], new Date());
  const completedSessions = course.get('totalCompletedSchedule', 0);
  const totalSessions = course.get('totalSchedules', 0);
  const endDateParse = Date.parse(endDate);
  const currentDateParse = Date.parse(new Date());
  const isExpired = endDateParse < currentDateParse;
  return isExpired && completedSessions !== 0 && totalSessions !== completedSessions
    ? t('reports_analytics.session_incomplete')
    : '';
};
const totalValue = (creditHour) => {
  const totalCompletedCR = fixedTwoDigit(creditHour.get('totalCompletedCreditHours', 0));
  const totalCreditHours = fixedTwoDigit(creditHour.get('totalCreditHours', 0));
  const percentage = fixedTwoDigit((totalCompletedCR / totalCreditHours) * 100);
  return percentage === 'NaN' ? 0 : percentage;
};
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------
const useDisciplinaryDialog = () => {
  const [isOpen, setIsOpen] = useState(false);
  const toggleDialog = () => {
    setIsOpen(!isOpen);
  };
  return { isOpen, toggleDialog };
};
//----------------------------------custom hooks end------------------------------------------------
//----------------------------------componentStart--------------------------------------------------
const FacebookCircularProgress = (props) => {
  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <CircularProgress
        variant="determinate"
        sx={{
          color: (theme) => theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800],
        }}
        size={60}
        thickness={4}
        {...props}
        value={100}
      />
      <CircularProgress
        variant="determinate"
        disableShrink
        sx={{
          color: (theme) => (theme.palette.mode === 'light' ? '#1a90ff' : '#308fe8'),
          animationDuration: '550ms',
          position: 'absolute',
          left: 0,
          [`& .${circularProgressClasses.circle}`]: {
            strokeLinecap: 'round',
          },
        }}
        size={60}
        thickness={4}
        {...props}
      />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography variant="caption" component="div" className="text-primary">
          {`${Math.round(props.value)}%`}
        </Typography>
      </Box>
    </Box>
  );
};
FacebookCircularProgress.propTypes = {
  value: PropTypes.number.isRequired,
};
const DisciplinaryLogButton = ({ userType, onClick }) => {
  if (isModuleEnabled('TARDIS_MODULE') && userType === 'student') {
    return (
      <Button className="p-0" variant="contained" onClick={onClick}>
        <span className="px-2 py-1">
          <Trans i18nKey={'user_global_search.disciplinary_remarks.addViewLog'} />
        </span>
      </Button>
    );
  }
  return <></>;
};
DisciplinaryLogButton.propTypes = {
  userType: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
};

//----------------------------------componentEnd----------------------------------------------------

const StudentOrStaffList = () => {
  const params = useParams();
  const history = useHistory();
  const dispatch = useDispatch();
  const [programDetail, setProgramDetail] = useState(Map());
  const [attendanceModal, setAttendanceModal] = useState(Map({ status: false, data: Map() }));
  const { isComprehensiveMode } = useAuthHook();

  const userType = params.userType ? params.userType : '';
  const institutionCalendarId = params.institutionCalendarId
    ? dString(params.institutionCalendarId)
    : '';
  const userId = params.userId ? dString(params.userId) : '';
  const userCode = params.userCode ? dString(params.userCode) : '';
  const userName = params.userName ? params.userName : '';
  const gender = params.gender ? params.gender : '';
  const email = params.email ? params.email : '';
  const academicNo = params.academicNo ? dString(params.academicNo) : '';
  const userFirstName = params.userFirstName ? params.userFirstName : '';
  const userLastName = params.userLastName ? params.userLastName : '';
  const disciplinaryDialogHook = useDisciplinaryDialog();
  const toggleDialog = disciplinaryDialogHook.toggleDialog;
  const isOpen = disciplinaryDialogHook.isOpen;

  const [selectedLevel, setSelectedLevel] = useState(null); //eslint-disable-line
  const [levelsList, setLevelList] = useState([]); //eslint-disable-line
  const [courseData, setCourseData] = useState(Map()); //eslint-disable-line

  const goBack = () => {
    history.goBack();
  };

  const CourseDetails = useSelector(selectUserCourseDetails);
  const studentAttnSheetDetails = useSelector(selectStudentAttnSheetDetails);

  const getUniqueLevel = ({ courseLists }) => {
    const levels = courseLists
      .map((courseElement) => courseElement.level)
      .sort((a, b) => {
        const numA = parseInt(a.split(' ')[1]);
        const numB = parseInt(b.split(' ')[1]);
        return numB - numA;
      });
    return {
      uniqueLevels: [...new Set(levels)],
      courseData: courseLists.find((courseElement) => courseElement.level === levels[0]),
    };
  };

  const courseCallBack = (courseLists) => {
    if (userType === 'student') {
      const levelsArray = getUniqueLevel({ courseLists: courseLists.toJS() });
      setLevelList(levelsArray.uniqueLevels);
      setSelectedLevel(levelsArray.uniqueLevels.length > 0 ? levelsArray.uniqueLevels[0] : null);
      setCourseData(levelsArray.uniqueLevels.length > 0 ? fromJS(levelsArray.courseData) : Map());
    } else {
      setLevelList([]);
      setSelectedLevel(null);
      setCourseData(Map());
    }
  };

  useEffect(() => {
    if (CourseDetails) {
      courseCallBack(CourseDetails);
    }
  }, [CourseDetails]);

  useEffect(() => {
    if (CourseDetails.isEmpty()) {
      dispatch(getUserCourseDetails(institutionCalendarId, userType, userId));
    }
  }, [CourseDetails]); //eslint-disable-line

  const startAndEndDateWithWeekDifference = (staffDetails) => {
    const course = Map({
      start_date: staffDetails.getIn(['courseStartAndEndDate', 'startDate'], 0),
      end_date: staffDetails.getIn(['courseStartAndEndDate', 'endDate'], 0),
    });
    return getFormattedCourseDuration(course);
  };

  const handleShow = (userDetails) => {
    const courseData = Map({
      gender: 'male',
      program: userDetails.get('programId', ''),
      courseNumber: userDetails.get('courseCode', ''),
      level: userDetails.get('level', ''),
      courseName: userDetails.get('courseName', ''),
      isRotation: userDetails.get('rotation', ''),
      term: userDetails.get('term', ''),
      programName: userDetails.get('programName', ''),
      rotationCount: userDetails.get('rotation_count', ''),
      courseId: userDetails.get('courseId', ''),
      year: userDetails.get('year', ''),
      versionName: getVersionName(userDetails),
    });
    setProgramDetail(courseData);

    setAttendanceModal(
      attendanceModal.set('status', true).set(
        'data',
        Map({
          _student_id: userId,
          academic_no: academicNo,
          name: Map({
            first: userFirstName,
            last: userLastName,
          }),
        })
      )
    );
  };

  const constructData = (item) =>
    ` ${capitalize(item.get('term', ''))} / ${capitalize(item.get('year', ''))} / ${capitalize(
      item.get('level', '')
    )}`;

  const constructCourseDetail = CourseDetails.map((item) => {
    return constructData(item);
  });

  const uniqueNumbersList = constructCourseDetail.toSet().toList();

  function getStudentAttendanceSheetDetail(data) {
    dispatch(getStudentAttendanceSheetDetails(data));
  }

  function updateReasons(_, session, attendanceModal, lastData) {
    dispatch(updateReason(_, session, attendanceModal, lastData));
  }

  return (
    <>
      <div className="mt-1">
        <Paper
          className="px-3 py-2 d-flex flex-wrap justify-content-between align-items-center"
          square
        >
          <div className="d-flex ">
            <ArrowBackIcon onClick={goBack} className="mr-4 GlobalPointer" />
            <div>
              <div className="h5 pl-1 text-capitalize">
                {userCode} - {userName}
              </div>
              <div className="pl-1 mt-1 f-12 text-capitalize">
                {userType !== 'staff' ? (
                  <span>{capitalize(uniqueNumbersList.join(', '))} • </span>
                ) : (
                  ''
                )}
                {gender} • {email}
              </div>
            </div>
          </div>
          <DisciplinaryLogButton userType={userType} onClick={toggleDialog} />
        </Paper>
        {userType === 'staff' ? (
          <Box sx={paperBoxSizeSm}>
            <Paper elevation={3} className="w-100 px-3 mt-2  StuOrSfListHeight">
              <Table className="TableHeight GlobalOverFlow">
                <TableHead className="GlobalHeader-sticky">
                  <TableRow>
                    <TableCell>Course Name / ID </TableCell>
                    <TableCell className="text-center">Scheduled Sessions</TableCell>
                    <TableCell className="text-center">Attended Sessions</TableCell>
                    <TableCell className="text-center">Attendance</TableCell>
                    <TableCell className="text-center ">
                      <div className="mr-4 pr-3">Graph Status</div>
                    </TableCell>
                    <TableCell className="text-center">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {CourseDetails.map((staffDetails, staffIndex) => {
                    const completeStatus = getCompleteStatus(staffDetails);
                    const status =
                      completeStatus !== ''
                        ? 'completed'
                        : staffDetails.get('totalCompletedSchedule', '') === 0
                        ? 'not-started'
                        : staffDetails.get('totalSchedules', '') ===
                          staffDetails.get('totalCompletedSchedule', '')
                        ? 'completed'
                        : 'in-progress';
                    return (
                      <TableRow
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                        key={staffIndex}
                      >
                        <TableCell>
                          <div>
                            <div className="f-13 mb-1 text-capitalize">
                              {staffDetails.get('programCode', '')} -{' '}
                              {staffDetails.get('programName', '')} /{' '}
                              {capitalize(constructData(staffDetails))}
                            </div>
                            <div className="f-13 text-primary">
                              {staffDetails.get('courseCode', '')} -{' '}
                              {staffDetails.get('courseName', '')}
                              {getVersionName(staffDetails)}
                            </div>
                            <div className="f-12">
                              {staffDetails.get('courseType', '')} |{' '}
                              {startAndEndDateWithWeekDifference(staffDetails)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="f-13 text-center">
                          {staffDetails.get('totalSchedules', '')}
                        </TableCell>
                        <TableCell className="f-13 text-center">
                          {' '}
                          {staffDetails.get('totalPresent', '')} /{' '}
                          {staffDetails.get('totalCompletedSchedule', '')}
                        </TableCell>
                        <TableCell className="f-13 text-center">
                          {fixedTwoDigit(staffDetails.get('presentPercentage', ''))}%
                        </TableCell>
                        <TableCell className="f-13">
                          <div className="d-flex justify-content-center">
                            {staffDetails
                              .get('creditHourCalculations', '')
                              .map((creditHour, creditHourIndex) => (
                                <div className="mr-4" key={creditHourIndex}>
                                  <FacebookCircularProgress
                                    className="mx-3"
                                    value={totalValue(creditHour)}
                                  />
                                  <div className="f-11 text-center">
                                    {creditHour.get('typeName', '')}
                                  </div>
                                  <div className="f-11 text-center">
                                    CR.H -{' '}
                                    {fixedTwoDigit(creditHour.get('totalCompletedCreditHours', ''))}{' '}
                                    / {fixedTwoDigit(creditHour.get('totalCreditHours', ''))}
                                  </div>
                                  <div className="f-11 text-center">
                                    CO.H -{' '}
                                    {fixedTwoDigit(
                                      creditHour.get('totalCompletedContactHours', '')
                                    )}{' '}
                                    / {fixedTwoDigit(creditHour.get('totalContactHours', ''))}
                                  </div>
                                </div>
                              ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <p
                            className={`f-13 text-center text-white ${COURSE_STATUS_INFO.getIn(
                              [status, 'class'],
                              ''
                            )}`}
                          >
                            {COURSE_STATUS_INFO.getIn([status, 'label'], '')}
                          </p>
                          <p className="session-incomplete">{getSessionStatus(staffDetails)}</p>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </Paper>
          </Box>
        ) : (
          <Box sx={paperBoxSizeSm}>
            {levelsList && levelsList.length > 0 && (
              <Suspense fallback="Loading...">
                <LevelsListComponent
                  selectedLevel={selectedLevel}
                  setSelectedLevel={setSelectedLevel}
                  levelsList={levelsList}
                  courseData={courseData}
                  institutionCalendarId={institutionCalendarId}
                  userId={userId}
                />
              </Suspense>
            )}
            <Paper elevation={3} className="w-100 px-3 StuOrSfListHeight">
              <Table className="TableHeight GlobalOverFlow">
                <TableHead className="GlobalHeader-sticky">
                  <TableRow>
                    <TableCell>Course Code / Name </TableCell>
                    <TableCell className="text-center">Scheduled Sessions</TableCell>
                    <TableCell>No.Of Sessions Attended</TableCell>
                    <TableCell className="text-center">Attendance</TableCell>
                    <TableCell className="text-center">Absent</TableCell>
                    {!isComprehensiveMode ? (
                      <>
                        <TableCell className="text-center">Warning Absent</TableCell>
                        <TableCell>Warnings</TableCell>
                      </>
                    ) : (
                      <></>
                    )}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {CourseDetails.filter((courseElement) =>
                    selectedLevel !== null
                      ? courseElement.get('level', '') === selectedLevel
                      : courseElement
                  ).map((userDetails, userIndex) => (
                    <TableRow
                      key={userIndex}
                      sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                    >
                      <TableCell>
                        <div>
                          <div
                            className="f-13 text-primary mb-1 GlobalPointer"
                            onClick={() => handleShow(userDetails)}
                          >
                            <u>
                              {userDetails.get('courseCode', '')} -{' '}
                              {userDetails.get('courseName', '')}
                              {getVersionName(userDetails)}
                            </u>
                          </div>
                          <div className="f-12 bold text-capitalize">
                            {userDetails.get('programCode', '')} -{' '}
                            {userDetails.get('programName', '')} / {userDetails.get('term', '')} /{' '}
                            {userDetails.get('year', '')} /{userDetails.get('level', '')}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {userDetails.get('totalSchedules', '')}
                      </TableCell>
                      <TableCell className="f-13">
                        {userDetails.get('totalPresent', 0)} /{' '}
                        {userDetails.get('totalCompletedSchedule', 0)} (P -{' '}
                        {userDetails.get('totalPresent', 0)}, A -{' '}
                        {userDetails.get('totalAbsent', 0)}, PER -{' '}
                        {userDetails.get('totalPermission', 0)}, L -{' '}
                        {userDetails.get('totalLeave', 0)}, OD -{' '}
                        {userDetails.get('totalOnDuty', '')}, LA -{' '}
                        {userDetails.get('studentLateAbsent', 0)})
                      </TableCell>
                      <TableCell className="f-13 text-center">
                        {fixedTwoDigit(userDetails.get('presentPercentage', 0))}%
                      </TableCell>
                      <TableCell className="f-13 text-center">
                        {fixedTwoDigit(userDetails.get('absentPercentage', 0))}%
                      </TableCell>
                      {!isComprehensiveMode ? (
                        <>
                          <TableCell className="f-13 text-center">
                            {fixedTwoDigit(userDetails.get('warningAbsencePercentage', 0))}%
                          </TableCell>
                          <TableCell
                            className={`f-13 text-center`}
                            sx={{ color: userDetails.get('colorCode', '') }}
                          >
                            {userDetails.get('warning', '')}
                          </TableCell>{' '}
                        </>
                      ) : (
                        <></>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Paper>
            <div style={{ height: '15px' }}></div>
          </Box>
        )}
        {attendanceModal.get('status', false) && (
          <div>
            <NewStudentAttendanceModal
              metaData={programDetail}
              attendanceModal={attendanceModal}
              setAttendanceModal={setAttendanceModal}
              getStudentAttendanceSheetDetails={getStudentAttendanceSheetDetail}
              updateReason={updateReasons}
              setData={setData}
              institutionCalendarId={institutionCalendarId}
              studentAttnSheetDetails={studentAttnSheetDetails}
              isTermCheck={'termKey'}
            />
          </div>
        )}
      </div>
      {isOpen && <AddDisciplinary disciplinaryDialogHook={disciplinaryDialogHook} />}
    </>
  );
};

export default StudentOrStaffList;
