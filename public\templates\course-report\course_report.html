<!DOCTYPE html>
<html lang="en">
<head>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.0/jquery.min.js"></script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="./css/style.css">
    <title>My Vite Project</title>
</head>
<body>
    <img src="images/course-report-header.jpg" class="header-image pb-5" />
    <section id="section-a"  class="gray-100">
        <!-- <section id="section-a"  class="flex justify-center items-center min-h-screen bg-gray-100"> -->
            <div class="bg-white template-details-border">
                <div class="grid grid-cols-2 gap-0 template-details-border">
                    <div class="flex template-details-border p-2" contenteditable="true">
                        <span class="font-bold text-blue-custom-100">Course Title:</span>
                        <input  class="ml-2 " placeholder="Enter Course Title."></input>
                    </div>
                    <div class="flex template-details-border p-2" contenteditable="true">
                        <span class="font-bold text-blue-custom-100">Course Code:</span>
                        <input class="ml-2 " placeholder="Enter Course Code."></input >
                    </div>
                    <div   class="flex template-details-border p-2">
                        <span  contenteditable="true" class="font-bold text-blue-custom-100">Department:</span>
                        <input  class="user-input-italic ml-2  italic" placeholder="Enter Department Name."/>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <span class="font-bold text-blue-custom-100" contenteditable="true">Program:</span>
                        <input class="user-input-italic ml-2 " placeholder="Enter Program Name."></input>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <span class="font-bold text-blue-custom-100" contenteditable="true">College:</span>
                        <input class="user-input-italic  " placeholder="Enter College Name"></input>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <span class="font-bold text-blue-custom-100" contenteditable="true">Institution:</span>
                        <input class="user-input-italic  " placeholder="Enter Institution Name."/>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <div class="font-bold text-blue-custom-100" contenteditable="true">Academic Year:</div>
                        <input class="user-input-italic  " placeholder="Enter Academic Year"/>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <span class="font-bold text-blue-custom-100" contenteditable="true">Semester:</span>
                        <input class="user-input-italic  " placeholder="Enter Semester"></input>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <span class="font-bold text-blue-custom-100" contenteditable="true">Course Instructor:</span>  
                        <input class="user-input-italic  " placeholder="Enter Course Instructor Name"></input>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <span class="font-bold text-blue-custom-100" contenteditable="true">Course Coordinator:</span>
                        <input class="user-input-italic  " placeholder="Enter Course Coordinator"></input>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <div class="font-bold text-blue-custom-100" contenteditable="true">Location:</div>
                        <div>
                            <span class="user-input-italic  " contenteditable="true">Main campus <input type="checkbox" class="ml-1"></span>
                            <span class="ml-4" contenteditable="true">branch <input type="checkbox" class="ml-1"></span>
                        </div>
                    </div>
                    <div class="flex template-details-border p-2" >
                        <span class="font-bold text-blue-custom-100">Number of Section(s):</span>
                        <input class="user-input-italic  " placeholder="Enter Number of Sections"></input>
                    </div>
                    <div class="flex template-details-border p-2 col-span-2" >
                        <span class="font-bold text-blue-custom-100">Number of Students (Starting the Course):</span>
                        <input class="user-input-italic  " placeholder="Enter Number of Students Starting the Course"></input>
                    </div>
                    <div class="flex template-details-border p-2 col-span-2" >
                        <span class="font-bold text-blue-custom-100">Number of Students (Completed the Course):</span>
                        <input class="user-input-italic  " placeholder="Enter Number of Students Completed the Course"></input>
                    </div>
                    <div class="flex template-details-border p-2 col-span-2" >
                        <span class="font-bold text-blue-custom-100">Report Date:</span>
                        <input class="ms-3 text-gray-500 italic" type="date" placeholder="Pick Report Date"></input>
                    </div>
                </div>
                
            </div>
            
        </section>
    <section id="section-b" class="bg-gray-100 py-8">
        <div class="text-20">
            <div class="flex justify-between" contenteditable="true">
                <span class="font-bold">A. Student Results</span>
                <span>3</span>
            </div>
            <div class="ml-6">
                <div class="flex justify-between" contenteditable="true">
                    <span>1. Grade Distribution</span>
                    <span>3</span>
                </div>
                <div class="flex justify-between" contenteditable="true">
                    <span>2. Comment on Student Grades</span>
                    <span>3</span>
                </div>
            </div>
            <div class="flex justify-between mt-4" contenteditable="true">
                <span class="font-bold">B. Course Learning Outcomes</span>
                <span>3</span>
            </div>
            <div class="ml-6">
                <div class="flex justify-between" contenteditable="true">
                    <span>1. Course Learning Outcomes Assessment Results</span>
                    <span>3</span>
                </div>
                <div class="flex justify-between" contenteditable="true">
                    <span>2. Recommendations</span>
                    <span>4</span>
                </div>
            </div>
            <div class="flex justify-between mt-4" contenteditable="true">
                <span class="font-bold">C. Topics not covered.</span>
                <span>4</span>
            </div>
            <div class="flex justify-between mt-4" contenteditable="true">
                <span class="font-bold">D. Course Improvement Plan (if any)</span>
            </div>
        </div>
        
    </section>
    <section id="section-c">
        <p class="font-semibold sections-header text-xl pb-1" contenteditable="true">A. Student Results</p>
        <p class="font-semibold text-teal-500 text-lg" contenteditable="true">1. Grade Distribution</p>
    
        <table id="section-c-table">
            <thead>
                <tr>
                    <th rowspan="2" class="left-header" contenteditable="true"></th>
                    <th colspan="9" class="left-header" contenteditable="true">Grades</th>
                    <th colspan="5" class="left-header" contenteditable="true">Status Distributions</th>
                </tr>
                <tr>
                    <th contenteditable="true">A+</th>
                    <th contenteditable="true">A</th>
                    <th contenteditable="true">B+</th>
                    <th contenteditable="true">B</th>
                    <th contenteditable="true">C+</th>
                    <th contenteditable="true">C</th>
                    <th contenteditable="true">D+</th>
                    <th contenteditable="true">D</th>
                    <th contenteditable="true">F</th>
                    <th contenteditable="true">In Progress</th>
                    <th contenteditable="true">Incomplete</th>
                    <th contenteditable="true">Pass</th>
                    <th contenteditable="true">Fail</th>
                    <th contenteditable="true">Withdrawn</th>
                </tr>
              
               
            </thead>


            <tbody>
                <tr>
                    <td class="left-header font-bold">Number of Students</td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                </tr>
                <tr>
                    <td class="left-header font-bold">Percentage</td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                    <td contenteditable="true"></td>
                </tr>
            </tbody>
        </table>
        <div class="flex mt-3">
            <label class="ms-auto">Sub Header Count: &nbsp;</label>
            <input id="section-c-sub-header-count" type="number" max=5 />
            <button id="section-c-add-column" class="mx-3">+ Column &nbsp;|</button>
            <button id="section-c-add-row">+ Row</button>
        </div>
    <!-- </section>
        <section id="modal-reuse" class="w-1/2 mx-auto">
            <div>
            Header Count : <input type="number" /> <br/>
            SubHeader Count: <input type="number"/>
            </div>
            <div class="flex">
                <button class="ms-auto me-3">Cancel </button>
                <button>Save </button>

            </div>
        </section> -->
    

    <section id="section-d" class="py-8">
        <div>
            <h2  contenteditable="true" class="text-lg font-semibold text-teal-500 mb-2">2. Comment on Student Grades</h2>
            <p  contenteditable="true" class="font-semibold mb-2">Including special factors (if any) affecting the results</p>
            <textarea class="w-full min-h-12 max-h-24 p-2 border border-gray-300 rounded bg-gray-50"></textarea>
        </div>
    </section>
    <section id="section-e">
        <div>
            <p class="font-semibold sections-header text-xl pb-1" contenteditable="true">B. Course Learning Outcomes</p>
            <p class="font-semibold text-teal-500 text-lg" contenteditable="true">1. Course Learning Outcomes Assessment Results</p>
            <div class="overflow-x-auto">
                <table id="section-e-table" class="min-w-full border-collapse border border-gray-300">
                    <thead>
                        <tr class="text-white">
                            <th class="border border-gray-300 px-4 py-2 left-header" colspan="2" rowspan="2" contenteditable="true">Course Learning Outcomes (CLOs)</th>
                            <th class="border border-gray-300 px-4 py-2 left-header" rowspan="2" contenteditable="true">Related PLOs Code</th>
                            <th class="border border-gray-300 px-4 py-2 left-header" rowspan="2" contenteditable="true">Assessment Methods</th>
                            <th class="border border-gray-300 px-4 py-2 left-header" colspan="2" contenteditable="true">Assessment Results</th>
                            <th class="border border-gray-300 px-4 py-2 left-header" rowspan="2" contenteditable="true">Comment on Assessment Results</th>
                        </tr>
                        <tr class="text-white">
                            <th class="border border-gray-300 px-4 py-2 sub-header-bg" contenteditable="true">Targeted Level</th>
                            <th class="border border-gray-300 px-4 py-2 sub-header-bg" contenteditable="true">Actual Level</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr  class="bg-teal-500 text-white">
                            <td  class="section-e-group-header border text-teal-bg-500 border-gray-300 px-4 py-2 font-semibold" colspan="7" contenteditable="true">1 Knowledge and Understanding:</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">1.1</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">1.2</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">....</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                        <tr  class="bg-teal-500 text-white">
                            <td class="section-e-group-header border text-teal-bg-500 border-gray-300 px-4 py-2 font-semibold" colspan="7" contenteditable="true">2 Skills:</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">2.1</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">2.2</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">....</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr> 
                        <tr  class="bg-teal-500 text-white">
                            <td class="section-e-group-header border text-teal-bg-500 border-gray-300 px-4 py-2 font-semibold" colspan="7" contenteditable="true">3 Values, autonomy, and responsibility</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">3.1</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">3.2</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-2 sub-header-bg text-white font-semibold" contenteditable="true">....</td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                            <td class="border border-gray-300 px-4 py-2" contenteditable="true"></td>
                        </tr>
                    </tbody>
                </table>
                <div class="flex mt-3">
                    <label class="ms-auto">Sub Header Count: &nbsp;</label>
                    <input id="section-e-sub-header-count" type="number" max=5 />
                    <button id="section-e-add-column" class="mx-3">+ Column</button>
                    <button id="section-e-add-row" class="me-3"> &nbsp;| &nbsp;+ Row &nbsp;&nbsp;</button>

                    <select id="section-e-select" >
                        <!-- <option>2 Skills</option>
                        <option>3 Values, autonomy, and responsibility</option> -->
                    </select>
                </div>
            </div>
        </div>
    </section>
    
    <section id="section-f" class="py-8">
        <div class="section-f-content font-semibold">
            <h2 contenteditable="true">2. Recommendations</h2>
            <div class="box" contenteditable="true"
            ></div>
        </div>
    
        <div class="section-f-content">
            <h3 class="font-semibold" contenteditable="true">C. Topics not covered.</h3>
            <table>
                <thead>
                    <tr class="font-semibold table-header-color">
                        <th contenteditable="true">Topic</th>
                        <th contenteditable="true">Reason for Not Covering/discrepancies</th>
                        <th contenteditable="true">Extent of their Impact on Learning Outcomes</th>
                        <th contenteditable="true">Compensating Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                    </tr>
                    <tr>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                    </tr>
                    <tr>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                    </tr>
                </tbody>
            </table>  
            <div class="flex mt-3">
                <button id="section-f-add-column" class="mx-3 ms-auto">+ Column</button>
                <button id="section-f-add-row" class="me-3"> &nbsp;| &nbsp;+ Row &nbsp;&nbsp;</button>
            </div>  
        </div>
    
        <div class="section-f1-content">
            <h3 class="font-semibold" contenteditable="true">D. Course Improvement Plan (if any)</h3>
            <table>
                <thead>
                    <tr class="font-semibold table-header-color">
                        <th contenteditable="true">Recommendations</th>
                        <th contenteditable="true">Actions</th>
                        <th contenteditable="true">Needed Support</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td contenteditable="true">1.</td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                    </tr>
                    <tr>
                        <td contenteditable="true">2.</td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                    </tr>
                    <tr>
                        <td contenteditable="true">3.</td>
                        <td contenteditable="true"></td>
                        <td contenteditable="true"></td>
                    </tr>
                </tbody>
            </table>
            <div class="flex mt-3">
                <button id="section-f1-add-column" class="ms-auto mx-3">+ Column</button>
                <button id="section-f1-add-row" class="me-3"> &nbsp;| &nbsp;+ Row &nbsp;&nbsp;</button>
            </div>  
            <p class="note" contenteditable="true">Improvement plan should be discussed at the department council and included in the Annual Program Report.</p>
        </div>
    </section>
    
    
    <script src="./js/main.js" type="module"></script>
    <script>
      function dataRetrieve() {
       return {};
      }
    </script>
</body>
</html>
