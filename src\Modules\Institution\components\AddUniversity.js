import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Modal } from 'react-bootstrap';
import Camera from '../../../Assets/camera.png';
import College from '../../../Assets/college_icon.svg';
import Scheduler from '../../../Assets/digi-scheduler.png';
import Institution from '../../../Assets/inst_icon.png';
import University from '../../../Assets/university_icon.png';
import { Trans } from 'react-i18next';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import Select from '@mui/material/Select';

class AddUniversity extends Component {
  constructor() {
    super();
    this.state = {
      programType: [],
      selectedProgramType: 'all',
      searchText: '',
    };
  }

  handleClose = () => {
    this.setState({ show: false });
  };
  handleShow = () => {
    this.setState({ show: true });
  };

  render() {
    return (
      <React.Fragment>
        <div className="main pb-5 login-bg-add_uni">
          <div className="container">
            <div className="p-3">
              <div className="row justify-content-center pt-3">
                <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                  <h3 className="text-center">
                    <img src={Scheduler} alt="Digischeduler" />
                  </h3>
                </div>
              </div>

              <div className="row justify-content-center pt-3">
                <div className="col-xl-5 col-lg-5 col-md-7 col-12 ">
                  <div className="outter-login p-4">
                    <div className="col-xl-12 col-lg-8 col-md-12 col-7">
                      <h6>
                        <b>
                          <Trans i18nKey={'add_colleges.choose_institution'}></Trans>
                        </b>
                      </h6>
                      <p>
                        <Trans i18nKey={'add_colleges.setup'}></Trans>
                      </p>

                      <div className="row rounded digi-gray-border">
                        <div className="col-xl-2 col-lg-2 col-md-2 col-2 p-3">
                          <img src={University} alt="university" className="mt-2" />
                        </div>
                        <div className="col-xl-10 col-lg-10 col-md-10 col-10 pl-1 pt-3">
                          <h6 className="mb-0">
                            <Trans i18nKey={'add_colleges.group_institutions'}></Trans>
                          </h6>
                          <p className="f-12">
                            <Trans i18nKey={'add_colleges.choose_organisation'}></Trans>
                          </p>
                        </div>
                      </div>

                      <div className="row rounded digi-gray-border mt-4">
                        <div className="col-xl-2 col-lg-2 col-md-2 col-2 p-3">
                          <img src={Institution} alt="institution" className="mt-2" />
                        </div>
                        <div className="col-xl-10 col-lg-10 col-md-10 col-10 pl-1 pt-3">
                          <h6 className="mb-0">
                            <Trans i18nKey={'add_colleges.individual_institution'}></Trans>
                          </h6>
                          <p className="f-12">
                            <Trans i18nKey={'add_colleges.choose_this_institution'}></Trans>
                          </p>
                        </div>
                      </div>

                      <div className="row pt-3">
                        <div className="col-md-6 p-0">
                          <div className="float-left">
                            <button className="remove_hover btn btn-ins-login">
                              <Trans i18nKey={'add_colleges.login'}></Trans>
                            </button>
                          </div>
                        </div>
                        <div className="col-md-6 p-0">
                          <div className="float-right">
                            <button className="remove_hover btn btn-primary">
                              <Trans i18nKey={'configuration.next'}></Trans>
                            </button>
                          </div>
                        </div>
                        <div className="clearfix"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-md-12">
                <div className="mt-3">
                  <div className="p-3 bg-white rounded">
                    <div className="float-left p-3">
                      <i className="fa fa-long-arrow-left" aria-hidden="true"></i>
                    </div>
                    <div className="float-left">
                      <h5 className="font-weight-normal mt-3 ml-0 mb-0">
                        <Trans i18nKey={'add_colleges.add_university'}></Trans>
                      </h5>
                    </div>
                    <div className="float-right mt-2">
                      <button
                        className="remove_hover btn btn-ins-login mr-3"
                        onClick={this.handleShow}
                      >
                        <Trans i18nKey={'cancel'}></Trans>
                      </button>

                      <button className="remove_hover btn btn-primary">
                        <Trans i18nKey={'add_colleges.save'}></Trans>
                      </button>
                    </div>
                    <div className="clearfix"></div>

                    <Modal show={this.state.show} onHide={this.handleClose}>
                      <Modal.Header className="border-none">
                        <Modal.Title>Cancel</Modal.Title>
                      </Modal.Header>

                      <Modal.Body>
                        <p>Are you sure you want to cancel?</p>

                        <p>The changes will not be saved if you choose to cancel</p>
                      </Modal.Body>

                      <Modal.Footer className="border-none">
                        <button className="remove_hover btn btn-primary mr-3">
                          <Trans i18nKey={'add_colleges.yes_cancel'}></Trans>
                        </button>
                        <button
                          className="remove_hover btn btn-ins-login "
                          onClick={this.handleShow}
                        >
                          <Trans i18nKey={'add_colleges.no'}></Trans>
                        </button>
                      </Modal.Footer>
                    </Modal>

                    <div className="row">
                      <div className="col-md-2 col-sm-4">
                        <div className="float-left p-3 mt-5">
                          <div className="digi-camera p-4 rounded">
                            <div className="text-center">
                              <img src={Camera} alt="camera" />
                            </div>
                            <span>
                              <Trans i18nKey={'add_colleges.add_photo'}></Trans>
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="col-md-4 col-sm-4">
                        <div className="float-left mt-5 ml-2">
                          <h6 className="font-weight-normal mt-5 ml-0 mb-0">
                            <Trans i18nKey={'add_colleges.upload_logo'}></Trans>
                          </h6>
                          <span>
                            <Trans i18nKey={'add_colleges.file_format'}></Trans>
                          </span>
                        </div>
                      </div>

                      <div className="col-md-5 col-sm-4">
                        <div className="float-right">
                          <img src={College} alt="college" className="img-fluid" />
                        </div>
                      </div>
                      <div className="clearfix"></div>
                    </div>

                    <div className="col-md-12 pt-2">
                      <div className="mt-3">
                        <label className="form-label">
                          <Trans i18nKey={'add_colleges.university_name'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          placeholder="Type Your University Name"
                          variant="outlined"
                          size="small"
                          fullWidth
                        />
                      </div>

                      <div className="mt-3">
                        <label className="form-label">
                          <Trans i18nKey={'add_colleges.university_code'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          placeholder="12345"
                          variant="outlined"
                          size="small"
                          fullWidth
                        />
                      </div>

                      <div className="mt-3">
                        <label className="form-label">
                          <Trans i18nKey={'add_colleges.accreditation'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          placeholder="Eg : NAAC - A+++"
                          variant="outlined"
                          size="small"
                          fullWidth
                        />
                      </div>

                      <div className="mt-3">
                        <label className="form-label">
                          <Trans i18nKey={'add_colleges.collage_under_university'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          placeholder="05"
                          variant="outlined"
                          size="small"
                          fullWidth
                        />
                      </div>

                      <h6 className="font-weight-normal mt-5 ml-0 mb-0">
                        <Trans i18nKey={'add_colleges.address_details'}></Trans>
                      </h6>
                      <div className="mt-3">
                        <label className="form-label">
                          <Trans i18nKey={'add_colleges.address_line1'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          placeholder="Type your Complete Address"
                          variant="outlined"
                          size="small"
                          fullWidth
                        />
                      </div>
                      <div className="row">
                        <div className="col-md-6">
                          <div className="mt-3">
                            <FormControl fullWidth variant="outlined" size="small">
                              <label className="form-label">
                                <Trans i18nKey={'add_colleges.country'}></Trans>
                              </label>
                              <Select labelId="term-label" native>
                                <option value=""></option>
                                <option>Country</option>
                              </Select>
                            </FormControl>
                          </div>
                          <div className="mt-3">
                            <FormControl fullWidth variant="outlined" size="small">
                              <label className="form-label">
                                <Trans i18nKey={'add_colleges.district'}></Trans>
                              </label>
                              <Select labelId="term-label" native>
                                <option value=""></option>
                                <option>District</option>
                              </Select>
                            </FormControl>
                          </div>
                          <div className="mt-3">
                            <FormControl fullWidth variant="outlined" size="small">
                              <label className="form-label">
                                <Trans i18nKey={'add_colleges.zip_code'}></Trans>
                              </label>
                              <TextField
                                type="text"
                                placeholder="ZipCode"
                                variant="outlined"
                                size="small"
                              />
                            </FormControl>
                          </div>
                        </div>

                        <div className="col-md-6">
                          <div className="mt-3">
                            <FormControl fullWidth variant="outlined" size="small">
                              <label className="form-label">
                                <Trans i18nKey={'add_colleges.state_region'}></Trans>
                              </label>
                              <Select labelId="term-label" native>
                                <option value=""></option>
                                <option>State</option>
                              </Select>
                            </FormControl>
                          </div>
                          <div className="mt-3">
                            <FormControl fullWidth variant="outlined" size="small">
                              <label className="form-label">
                                <Trans i18nKey={'add_colleges.city'}></Trans>
                              </label>
                              <Select labelId="term-label" native>
                                <option value=""></option>
                                <option>City</option>
                              </Select>
                            </FormControl>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

export default withRouter(AddUniversity);
