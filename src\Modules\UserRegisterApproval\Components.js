import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Slide,
  Snackbar,
} from '@mui/material';
import PropTypes from 'prop-types';
import React, { Suspense, forwardRef, useState } from 'react';
import { Table } from 'react-bootstrap';
import DeleteIcon from '@mui/icons-material/Delete';
import axios from '../../axios';
import { List } from 'immutable';
import Config from '../../config';
import FaceIcon from '@mui/icons-material/Face';

const URL = Config.API_URL;

import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useSelector } from 'react-redux';

export const TableContent = ({
  setSelectedUser,
  setOpen,
  setProgress,
  setDeleted,
  deleteUser,
  setFailedToDelete,
  setFailedToDeleteDueToMapped,
  users,
  searchKey,
  concat,
  selectedUser,
  Content,
}) => {
  const [deletePop, setDeletePop] = useState(false);
  const handleUserClick = (user) => {
    setSelectedUser(user);
    setOpen(true);
  };

  const handleDeleteOpen = (user) => {
    setSelectedUser(user);
    setDeletePop(true);
  };

  const handleDeleteClose = () => {
    setDeletePop(false);
  };

  const handleDeleteClick = async (particularUser) => {
    setDeletePop(false);
    const data = {
      userId: particularUser._id,
    };
    setProgress(true);
    try {
      const response = await axios.delete(`${URL}/userRegistration/deleteUser`, {
        data: data,
      });

      if (response.status === 200) {
        setDeleted(true);
        deleteUser(particularUser._id);
      }
    } catch (error) {
      if (error.response.data.data.errorKey == 'studentMappedInCourse') {
        setFailedToDeleteDueToMapped(true);
      } else {
        setFailedToDelete(true);
      }
      setProgress(false);
      return false;
    }
    setProgress(false);
  };

  return (
    <>
      <Table responsive hover>
        <thead className="th-change">
          <tr>
            <th>Academic No</th>
            <th>Email</th>
            <th>First Name </th>
            <th>Middle Name </th>
            <th>Last Name </th>
            <th>Gender </th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          {users.getIn([searchKey, concat], List()).map((user, index) => (
            <tr className="tr-change" key={index}>
              <td>{user.get('user_id', '')}</td>
              <td>{user.get('email', '')}</td>
              <td>{user.getIn(['name', 'first'], '')}</td>
              <td>{user.getIn(['name', 'middle'], '')}</td>
              <td>{user.getIn(['name', 'last'], '')}</td>
              <td className="text-capitalize">{user.get('gender', '')}</td>

              <td>
                {Content === 'pending' ? (
                  <Button
                    variant="outlined"
                    size="small"
                    className="remove_hover"
                    onClick={() => handleUserClick(user)}
                  >
                    Pending
                  </Button>
                ) : (
                  Content
                )}
              </td>
              <td>
                <DeleteIcon
                  variant="danger"
                  size="small"
                  className="remove_hover"
                  onClick={() => handleDeleteOpen(user)}
                />
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
      {deletePop && (
        <Suspense fallback="">
          <Dialog open={deletePop} onClose={handleDeleteClose}>
            <DialogTitle>{'Delete user'}</DialogTitle>
            <DialogContent>
              <DialogContentText id="alert-dialog-slide-description">
                Do you want to delete this user ?
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleDeleteClose}>Cancel</Button>
              <Button onClick={() => handleDeleteClick(selectedUser.toJS())} autoFocus>
                Delete
              </Button>
            </DialogActions>
          </Dialog>
        </Suspense>
      )}
    </>
  );
};

export const ShowParticularUserDetail = ({ user }) => {
  const [imagesFile, setImagesFile] = useState(false);
  const [imageFileURL, setImageFileURL] = useState('');
  const [DocumentFileURL, setDocumentFileURL] = useState(false);
  const [password, setPassword] = useState('');
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [invalidPassKey, setInvalidPassKey] = useState(false);
  const [progress, setProgress] = useState(false);
  const loggedInUserData = useSelector(({ auth }) => auth.loggedInUserData);

  const { studentFacial = false, selfRegistrationDocument = false } = loggedInUserData;

  const biometricsLabel =
    studentFacial && selfRegistrationDocument
      ? 'Biometric & Document'
      : studentFacial
      ? 'Biometric'
      : selfRegistrationDocument
      ? 'Document'
      : '';

  const Transition = forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
  });

  const gap10 = {
    gap: '10px',
  };

  const handleImagesOpen = async (password) => {
    var data = '';
    setProgress(true);
    try {
      const response = await axios.post(
        URL + `/userRegistration/getDocument`,
        {
          userId: user._id,
          passKey: password,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      data = response.data;
      setImageFileURL(data.data.signedFaceURLs);
      setDocumentFileURL(data.data.signedDocumentURL);
      setImagesFile(true);
    } catch (error) {
      data = error.response.data;
      if (data.message === 'Password not Match') {
        setInvalidPassKey(true);
      }
    }
    setProgress(false);
  };

  const handlePasswordSubmit = () => {
    handleImagesOpen(password);
    handleClosePassword();
  };

  const handleImagesClose = () => {
    setImagesFile(false);
  };

  const [DocumentFile, setDocumentFile] = useState(false);
  const handleDocumentClose = () => {
    setDocumentFile(false);
  };

  const handleClickShowPassword = () => setShowPassword((show) => !show);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const handleClosePassword = () => {
    setIsPasswordDialogOpen(false);
    setPassword('');
  };

  return (
    <>
      <div>
        {progress && (
          <CircularProgress
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 999,
            }}
          />
        )}
        <Divider />
        {user?.program?._program_id.name && (
          <>
            <div className="d-flex justify-content-between my-3">
              <div className="f-15">Program</div>
              <div className="f-14">{user?.program?._program_id.name}</div>
            </div>
            <Divider />
            <div className="d-flex justify-content-between my-3">
              <div className="f-15">Batch</div>
              <div className="f-14">{user?.batch}</div>
            </div>
          </>
        )}

        <Divider />
        <div className="d-flex justify-content-between my-3">
          <div className="f-15">Mobile Number</div>
          <div className="f-14">{user?.mobile}</div>
        </div>
        <Divider />
        <div className="d-flex justify-content-between my-3">
          <div className="f-15">User ID</div>
          <div className="f-14"> {user?.user_id}</div>
        </div>
        <Divider />
        <div className="d-flex justify-content-between my-3">
          <div className="f-15">Gender</div>
          <div className="f-14">{user?.gender}</div>
        </div>
        <Divider />
        <div className="d-flex justify-content-between my-3 align-items-center">
          <div className="f-15">Biometrics</div>
          <div className="f-14 d-flex align-items-center" style={gap10}>
            <div>
              <div>
                {biometricsLabel ? (
                  <Chip
                    icon={<FaceIcon />}
                    label={biometricsLabel}
                    color="primary"
                    clickable
                    sx={{ fontSize: '12px' }}
                    onClick={() => setIsPasswordDialogOpen(true)}
                  />
                ) : (
                  '-'
                )}
              </div>
            </div>
          </div>
        </div>
        <Divider />
        {invalidPassKey && (
          <Snackbar
            open={invalidPassKey}
            autoHideDuration={300}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert
              onClose={() => setInvalidPassKey(false)}
              severity="error"
              variant="filled"
              sx={{ width: '100%' }}
            >
              Invalid Password
            </Alert>
          </Snackbar>
        )}
      </div>
      {isPasswordDialogOpen && (
        <Dialog open={isPasswordDialogOpen} onClose={handleClosePassword} fullWidth maxWidth="sm">
          <DialogTitle>Enter Password</DialogTitle>
          <Divider className="mx-4" />
          <DialogContent>
            <form
              onSubmit={(e) => e.preventDefault()}
              onInput={(e) => {
                if (e.target.name === 'admin-password') {
                  e.target.setAttribute('autocomplete', 'new-password');
                }
              }}
            >
              <OutlinedInput
                fullWidth
                size="small"
                placeholder="Enter Password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="off"
                name="admin-password"
                autoFocus
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleClickShowPassword}
                      onMouseDown={handleMouseDownPassword}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                }
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </form>
          </DialogContent>
          <Divider className="mx-4" />
          <DialogActions>
            <Button onClick={handleClosePassword}>Cancel</Button>
            <Button onClick={handlePasswordSubmit} disabled={password.length < 4}>
              Submit
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {imagesFile && (
        <Dialog
          open={imagesFile}
          TransitionComponent={Transition}
          keepMounted
          fullWidth={true}
          maxWidth={'lg'}
          sx={{
            '&.MuiDialog-paper': {
              backgroundColor: 'black',
            },
          }}
        >
          <DialogTitle>Biometrics</DialogTitle>
          <Divider className="mx-4" />
          <DialogContent>
            <div className="container">
              <div className="row">
                {studentFacial && (
                  <div className="col-6">
                    <div className="mb-1">Biometric-Face</div>
                    <div className="d-flex flex-wrap">
                      {imageFileURL.map((signedFaceURL, index) => (
                        <div key={index} className="w-50">
                          <img
                            src={signedFaceURL}
                            alt="User Face"
                            // height="400px"
                            // className="flex-grow-1"
                            style={{ maxWidth: '100%', height: '100%', padding: '2px' }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {selfRegistrationDocument && (
                  <div className="col-6">
                    {DocumentFileURL ? (
                      <div className="flex-grow-1">
                        <div className="flex-grow-1">
                          <div className="mb-1">Biometric-Document</div>
                          {JSON.stringify(DocumentFileURL).includes('.pdf') ? (
                            <div className="d-flex">
                              <iframe
                                src={DocumentFileURL}
                                title="pdf"
                                height="400px"
                                width="400px"
                                className="flex-grow-1"
                              ></iframe>
                            </div>
                          ) : (
                            <div className="d-flex">
                              <img
                                src={DocumentFileURL}
                                alt="S3 Document"
                                height="300px"
                                width="400px"
                                className="flex-grow-1"
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      !studentFacial && <div>No data available</div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
          <Divider className="mx-4" />
          <DialogActions>
            <Button onClick={handleImagesClose}>Close</Button>
          </DialogActions>
        </Dialog>
      )}
      {DocumentFile && (
        <Dialog
          open={DocumentFile}
          TransitionComponent={Transition}
          keepMounted
          fullWidth={true}
          maxWidth={'sm'}
        >
          <DialogTitle>{'Biometric Document'}</DialogTitle>
          <Divider className="mx-4" />
          <DialogContent>
            <div className="d-flex justify-content-center">
              <div>
                <img src={DocumentFileURL} alt="S3 Document" height="300px" width="400px" />
              </div>
            </div>
          </DialogContent>
          <Divider className="mx-4" />
          <DialogActions>
            <Button onClick={handleDocumentClose}>close</Button>
          </DialogActions>
        </Dialog>
      )}
    </>
  );
};

function ReusableSnackbar({ open, message, onClose }) {
  return (
    <Snackbar
      open={open}
      autoHideDuration={3000}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'center',
      }}
    >
      <Alert onClose={onClose} severity="info">
        {message}
      </Alert>
    </Snackbar>
  );
}

ReusableSnackbar.propTypes = {
  open: PropTypes.bool.isRequired,
  message: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default ReusableSnackbar;
