import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';
import ArchiveImg from 'Assets/archive.png';
import Button from 'Widgets/FormElements/material/Button';
import { t } from 'i18next';
function ResetModal({ show, setShow, handleReset, title, description, selected }) {
  return (
    <Modal show={show} centered onHide={() => setShow(false)} dialogClassName="modal-500">
      <Modal.Body>
        <div className="d-flex mb-3">
          <img className="mr-2" alt={'Archive'} src={ArchiveImg} />
          <p className="mb-0 f-22 bold"> {t('global_configuration.reset')}</p>
        </div>
        <div className="p-2">
          <p className="mb-0 break-word">
            {' '}
            <Trans i18nKey={'are_you_sure_you_want_to_reset'}></Trans>{' '}
            <span className="bold">{selected}</span>?
          </p>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none pt-0">
        <Button variant="outlined" color="inherit" clicked={() => setShow(false)}>
          <Trans i18nKey={'cancel'}></Trans>
        </Button>

        <Button variant="contained" color="red" clicked={handleReset}>
          <Trans i18nKey={'global_configuration.reset'}></Trans>
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

ResetModal.propTypes = {
  show: PropTypes.bool,
  title: PropTypes.string,
  description: PropTypes.string,
  deleteName: PropTypes.string,
  deleteSelected: PropTypes.func,
  setShow: PropTypes.func,
  handleReset: PropTypes.func,
  selected: PropTypes.string,
};
export default ResetModal;
