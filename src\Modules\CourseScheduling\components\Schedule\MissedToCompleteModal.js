import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';

function MissedToCompleteModal({ open, setMissedToCompleteOpen, handleSave }) {
  const [status, setStatus] = useState('present');

  function handleClose() {
    setMissedToCompleteOpen(false);
  }

  function handleSubmit() {
    handleSave({ status });
  }
  return (
    <Dialog fullWidth={true} maxWidth={'sm'} open={open} onClose={handleClose}>
      <div className="m-3">
        <div className="h5 pt-1">Student Attendance Status</div>

        <FormControl>
          <RadioGroup
            aria-labelledby=""
            defaultValue="present"
            name="radio-buttons-group"
            class="d-flex justify-content-between"
            value={status}
            onChange={(event) => setStatus(event.target.value)}
          >
            <FormControlLabel value="present" control={<Radio />} label="Present" />
            <FormControlLabel value="absent" control={<Radio />} label="Absent" />
          </RadioGroup>
        </FormControl>
        <div className="f-14 text-muted">
          Note: This changes will not be applicable for On Duty / Leave / Permission
        </div>
      </div>
      <DialogActions className="pb-2">
        <Button variant="outlined" onClick={handleClose}>
          CLOSE
        </Button>
        <Button variant="contained" color="primary" onClick={handleSubmit}>
          SAVE
        </Button>
      </DialogActions>
    </Dialog>
  );
}

MissedToCompleteModal.propTypes = {
  open: PropTypes.bool,
  setMissedToCompleteOpen: PropTypes.func,
  handleSave: PropTypes.func,
};

export default MissedToCompleteModal;
