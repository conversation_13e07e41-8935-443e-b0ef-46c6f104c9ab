import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';

import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import { ucFirst } from '../../../utils';
import UnCheckAlertModal from './UncheckAlert';

const ToggleAssessmentTypesModal = (props) => {
  const {
    show,
    cancel,
    assessmentPlanning,
    getAssessmentPlan,
    toggleAssessmentType,
    types,
    showMarks,
  } = props;
  const [alertModal, setAlertModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState({});
  const closeAlertModal = () => {
    setAlertModal(false);
  };
  const [checkedState, setCheckedState] = useState(Map());

  useEffect(() => {
    setCheckedState(
      types.get('assessmentTypes', List()).map((item) =>
        fromJS({
          status: item.get('isActive', false),
          assessmentId: item.get('_id', ''),
        })
      )
    );
  }, []); // eslint-disable-line
  const [edited, setEdited] = useState(false);

  const handleCheck = (e, index, fileName) => {
    setSelectedFile({ index, fileName });
    setEdited(true);
    const { target } = e;
    if (!target.checked) {
      setAlertModal(true);
    } else {
      setCheckedState(checkedState.setIn([index, 'status'], target.checked));
    }
  };
  const handleCancel = () => {
    closeAlertModal();
  };
  const handleAlertSubmit = () => {
    setCheckedState(checkedState.setIn([selectedFile.index, 'status'], false));
    closeAlertModal();
  };
  const toggleCallback = () => {
    getAssessmentPlan();
    cancel();
  };
  const handleSave = () => {
    const requestBody = fromJS({
      _id: assessmentPlanning.get('_id', ''),
      assessments: checkedState,
    });
    toggleAssessmentType(requestBody, toggleCallback, showMarks);
  };
  return (
    <>
      <MaterialDialog show={show} onClose={cancel} maxWidth={'xs'} fullWidth={true}>
        <div className="w-100 p-4">
          <p className="mb-3 pb-2  bold f-19">{` Set Assessment Type For ${ucFirst(
            types.get('subTypeName', '')
          )}`}</p>
          <div className="mt-2 mb-2">
            {assessmentPlanning
              .get('types', List())
              .filter((fItem) => fItem.get('typeName') === types.get('typeName', ''))
              .map((item, i) =>
                item
                  .get('subTypes', List())
                  .filter((nItem) => nItem.get('typeName') === types.get('subTypeName', ''))
                  .map((subItem, subIndex) =>
                    subItem.get('assessmentTypes', List()).map((assmtType, index) => (
                      <div key={index}>
                        <FormControlLabel
                          value={assmtType.get('_id', '')}
                          control={
                            <Checkbox
                              color="primary"
                              checked={checkedState?.getIn([index, 'status'], false)}
                            />
                          }
                          label={ucFirst(assmtType.get('name', ''))}
                          labelPlacement="end"
                          onChange={(event) =>
                            handleCheck(event, index, ucFirst(assmtType.get('name', '')))
                          }
                        />
                      </div>
                    ))
                  )
              )}
          </div>

          <div className="d-flex justify-content-end border-top pt-3">
            <MButton variant="outlined" color="primary" className={'mr-2'} clicked={cancel}>
              Cancel
            </MButton>
            <MButton variant="contained" color="primary" clicked={handleSave} disabled={!edited}>
              Save
            </MButton>
          </div>
        </div>
      </MaterialDialog>
      {alertModal && (
        <UnCheckAlertModal
          show={alertModal}
          close={closeAlertModal}
          componentName={ucFirst(types.get('subTypeName', ''))}
          file={selectedFile}
          handleSubmit={handleAlertSubmit}
          handleCancel={handleCancel}
        />
      )}
    </>
  );
};
ToggleAssessmentTypesModal.propTypes = {
  show: PropTypes.bool,
  cancel: PropTypes.func,
  assessmentPlanning: PropTypes.instanceOf(Map),
  getAssessmentPlan: PropTypes.func,
  toggleAssessmentType: PropTypes.func,
  types: PropTypes.instanceOf(Map),
  showMarks: PropTypes.bool,
};
export default ToggleAssessmentTypesModal;
