import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory, useParams } from 'react-router-dom';
import { Accordion, Card, Modal } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import i18n, { t } from 'i18next';
import DatePicker from 'react-datepicker';
import { dString } from 'utils';
import FormControl from '@mui/material/FormControl';
import Checkbox from '@mui/material/Checkbox';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormLabel from '@mui/material/FormLabel';
import Chip from '@mui/material/Chip';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';
import { selectProgramSettings } from '_reduxapi/program_input/v2/selectors';
import * as action from '_reduxapi/program_input/v2/actions';
import EmailIdConFiguration from './EmailIdConfiguration';

import {
  timeZones,
  days,
  independentHours,
  formatDateObject,
  formatDateTime,
  languages,
  daysOrder,
} from '../utils';
import AddEditBreakModal from '../modal/AddEditBreakModal';
import AddEditEventModal from '../modal/AddEditEventModal';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectBasicDetails } from '_reduxapi/global_configuration/selectors';
import DeleteModal from '../modal/DeleteModal';
import { addMinutes, setHours, setMinutes } from 'date-fns';
import AutoComplete from 'Widgets/FormElements/material/Autocomplete';
import { getHour, getInstitutionHeader } from 'v2/utils';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';

function BasicDetails(props) {
  const {
    basicDetails,
    getBasicDetails,
    editBasicDetails,
    updateBreak,
    updateEventType,
    updateWorkingDays,
    updateLanguage,
    setData,
    updateProgramBasicDetails,
    programSettings,
    getProgramSettings,
    updatePrivacySettings,
  } = props;
  const settings = programSettings.getIn(['programSettings', 'settings', 'basicDetails'], List());
  const id = basicDetails.get('_id', '');

  const history = useHistory();
  const params = useParams();
  const getProgramID = dString(params.programID ? params.programID : params.id);
  const [basicDetailsState, setBasicDetailsState] = useState(Map());
  const [arrow, setArrow] = useState(false);
  const [open, setOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [item, setItem] = useState(Map());
  const [type, setType] = useState('');
  const [checked, setChecked] = useState({});
  const [language, setLanguage] = useState({});
  const institutionHeader = getInstitutionHeader(history);
  const institutionId = institutionHeader?._institution_id;

  const getInstitutionData = (type) => {
    const getList = programSettings
      .getIn(['institutionSettings', 'globalConfiguration', 'basicDetails', type], List())
      .toJS();
    const updateList = getList.map((item) => {
      return {
        ...item,
        isInstitution: true,
      };
    });
    return fromJS(updateList);
  };

  const getBreaks = getInstitutionData('breaks');
  const getEvents = getInstitutionData('eventType');

  const programSettingBreaks = getBreaks.concat(settings.get('breaks', List()));
  const programSettingEvents = getEvents.concat(settings.get('eventType', List()));

  const CallingProgramSettings = ({ institutionId, programId }) => {
    return getProgramSettings({ institutionId, getProgramID });
  };

  useEffect(() => {
    !params.programID && getBasicDetails({ header: institutionHeader });
  }, []); // eslint-disable-line

  useEffect(() => {
    if (basicDetails.size) {
      let request = { ...basicDetails.toJS() };
      request.session.start.minute = parseInt(request.session.start.minute);
      request.session.end.minute = parseInt(request.session.end.minute);
      setBasicDetailsState(fromJS(request));
      setChecked(basicDetails.get('workingDays', List()));
      setLanguage(basicDetails.get('language', List()));
    }
  }, [basicDetails]);

  const handleWorkingDaysCheck = (e, key, type) => {
    let updatedDays = [...checked];
    let dayId = updatedDays[key].get('_id', '');
    if (type === 'independent') {
      handleChange({ name: 'workingDays', event: e.target.checked, dayId, type });
    } else handleChange({ name: 'workingDays', event: e.target.checked, dayId });
  };

  const handleLanguageCheck = (event) => {
    let languageName = languages.filter((lan) => lan.value === event.target.value)[0].key;
    let updatedLanguages = [...language];
    let languageId = updatedLanguages
      .filter((lan) => lan.get('code', '') === event.target.value)
      .reduce((_, el) => {
        return el.get('_id', '');
      }, '');
    handleChange({ name: 'language', event, languageName, languageId });
  };

  const addedMinutes = (event) => {
    let added = addMinutes(event, 15);
    return formatDateObject(added);
  };

  const addBreakEventReusable = (type, item, addType) => {
    return (
      <div className="row digi-tbl-pad pt-3 pl-2 digi-text-justify">
        <div className="col-md-12 btn-description-border text-center p-5">
          <button
            className="remove_hover btn btn-description mr-3"
            onClick={() => handleOpen(type, 'create', item)}
          >
            <Trans i18nKey={addType}></Trans>
          </button>
        </div>
      </div>
    );
  };

  const handleChange = ({ name, event, languageName, languageId, dayId, type, key }) => {
    if (basicDetails.size) {
      var request = { ...basicDetailsState.toJS() };
      delete request.eventType;
      delete request.breaks;
      request.session.start.minute = parseInt(request.session.start.minute);
      request.session.end.minute = parseInt(request.session.end.minute);
      request.workingDays.map((i) => (i.session.end.minute = parseInt(i.session.end.minute)));
      request.workingDays.map((i) => (i.session.start.minute = parseInt(i.session.start.minute)));

      var session = fromJS(request)
        .get('workingDays', Map())
        .map((item) => item.get('session'));
      var independentSessionId = basicDetails.getIn(['workingDays', `${key}`, '_id'], '');
    }
    if (name === 'timeZone') {
      !params.programID
        ? editBasicDetails({
            id,
            requestBody: { ...request, timeZone: event },
            operation: name,
            header: institutionHeader,
          })
        : updateProgramBasicDetails({
            programId: getProgramID,
            timeZone: event,
            header: institutionHeader,
            urlTIme: `/program-input/update-time-zone`,
            show: false,
            value: true,
            institutionId,
            CallingProgramSettings,
          });
    }
    if (name === 'language') {
      let checked = event.target.checked;
      let code = event.target.value;
      checked
        ? updateLanguage({
            operation: 'create',
            code,
            languageName,
            languageId: '',
            id,
            header: institutionHeader,
          })
        : updateLanguage({
            operation: 'delete',
            code,
            languageName,
            languageId,
            id,
            header: institutionHeader,
          });
    }
    if (name === 'start' && event) {
      editBasicDetails({
        id: id,
        requestBody: {
          ...request,
          session: {
            start: formatDateObject(event),
            end: addedMinutes(event),
          },
        },
        operation: name,
        header: institutionHeader,
        callBack: getBasicDetails,
      });
    }
    if (name === 'end' && event) {
      editBasicDetails({
        id: id,
        requestBody: {
          ...request,
          session: {
            end: formatDateObject(event),
            start: basicDetailsState.getIn(['session', 'start'], ''),
          },
        },
        operation: name,
        header: institutionHeader,
        callBack: getBasicDetails,
      });
    }
    if (name === 'isIndependentHours') {
      editBasicDetails({
        id: id,
        requestBody: { ...request, isIndependentHours: event },
        operation: name,
      });
      setBasicDetailsState(basicDetailsState.set('isIndependentHours', event));
    }
    if (name === 'workingDays') {
      if (type === 'independent') {
        if (event === true)
          updateWorkingDays({
            operation: 'add',
            settingId: id,
            id: dayId,
            session: {
              start: basicDetailsState.getIn(['session', 'start'], {
                hour: 8,
                minute: 0,
                format: t('date.am'),
              }),
              end: basicDetailsState.getIn(['session', 'end'], {
                hour: 6,
                minute: 0,
                format: t('date.pm'),
              }),
            },
            isIndependentHours: true,
            header: institutionHeader,
          });

        if (event === false)
          updateWorkingDays({
            operation: 'remove',
            settingId: id,
            id: dayId,
            isIndependentHours: true,
            header: institutionHeader,
          });
      } else {
        if (event === true)
          updateWorkingDays({
            operation: 'add',
            settingId: id,
            id: dayId,
            isIndependentHours: false,
            header: institutionHeader,
          });

        if (event === false)
          updateWorkingDays({
            operation: 'remove',
            settingId: id,
            id: dayId,
            isIndependentHours: false,
            header: institutionHeader,
          });
      }
    }
    if (name === 'independentStart') {
      updateWorkingDays({
        operation: '',
        id: independentSessionId,
        settingId: id,
        isIndependentHours: true,
        session: {
          start: formatDateObject(event),
          end: addedMinutes(event),
        },
        name,
        header: institutionHeader,
      });
    }
    if (name === 'independentEnd') {
      updateWorkingDays({
        operation: '',
        id: independentSessionId,
        isIndependentHours: true,
        settingId: id,
        session: {
          start: session.getIn([`${key}`, 'start'], '').toJS(),
          end: formatDateObject(event),
        },
        name,
        header: institutionHeader,
      });
    }

    if (name === 'isGenderSegregation') {
      !params.programID
        ? editBasicDetails({
            id: id,
            requestBody: { ...request, isGenderSegregation: event === 'separate' ? true : false },
            operation: name,
          })
        : updateProgramBasicDetails({
            programId: getProgramID,
            updateProgramBasicDetails,
            header: institutionHeader,
            isGenderSegregation: event === 'separate' ? true : false,
            show: true,
            urlGender: `/program-input/update-gender-segregation`,
            value: true,
            institutionId,
            CallingProgramSettings,
          });
      setBasicDetailsState(
        basicDetailsState.set('isGenderSegregation', event === 'separate' ? true : false)
      );
    }
    if (name === 'blurPhotos') {
      const settings = basicDetails.get('privacySettings', List());
      updatePrivacySettings({
        settingId: id,
        labelId: settings?.get(0)?.get('_id', ''),
        headers: institutionHeader,
      });
    }
  };

  const handleOpen = (name, operation, item) => {
    let popup = {
      ...open,
      [name]: !open[`${name}`],
    };
    setOpen(popup);
    setModalTitle(operation);
    if (operation === 'create') setItem(List());
    if (operation === 'edit' || operation === 'delete') setItem(item);
    setType(name);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const getDateFormat = (type, key) => {
    if (
      (type === 'start' || type === 'end') &&
      basicDetails.getIn(['session', 'start', 'hour'], '')
    ) {
      return formatDateTime(basicDetails.getIn(['session', `${type}`], ''));
    }
    if (
      (type === 'independentStart' || type === 'independentEnd') &&
      basicDetailsState.get('isIndependentHours', '') === true
    ) {
      let session = basicDetailsState.get('workingDays', Map()).map((item) => item.get('session'));
      return formatDateTime(
        session?.getIn([`${key}`, `${type}` === 'independentStart' ? 'start' : 'end'], Map())
      );
    }
    return new Date();
  };

  const getSubHeader = (heading, size) => {
    switch (heading) {
      case 'timeZone':
        return !params.programID ? basicDetails.get('timeZone', '') : settings.get('timeZone', '');
      case 'languages': {
        let language = basicDetails.get('language', List());
        return language.size + ' ' + t('global_configuration.languages');
      }
      case 'workingDays': {
        let workingDays = basicDetails
          .get('workingDays', List())
          .filter((i) => i.get('isActive', true) === true);
        return workingDays.size + ' ' + t('global_configuration.workingDays');
      }
      case 'breaks': {
        let breaks = basicDetails.get('breaks', List());
        // let programBreaks = settings.get('breaks', List());
        return !params.programID
          ? breaks.size + ' ' + t('global_configuration.breaks')
          : programSettingBreaks.size + ' ' + t('global_configuration.breaks');
      }
      case 'events': {
        let events = basicDetails.get('eventType', List());
        // let event = settings.get('eventType', List());
        return !params.programID
          ? events.size + ' ' + t('global_configuration.events')
          : programSettingEvents.size + ' ' + t('global_configuration.events');
      }
      case 'gender': {
        let gender = basicDetails.get('isGenderSegregation', true);
        let genders = settings.get('isGenderSegregation', true);
        return !params.programID
          ? gender
            ? t('global_configuration.groupMaleAndFemale')
            : t('global_configuration.no_gender_segregation')
          : genders
          ? t('global_configuration.groupMaleAndFemale')
          : t('global_configuration.no_gender_segregation');
      }
      case 'email': {
        return size + '  ' + t('configured');
      }
      case 'privacySettings': {
        return basicDetails.getIn(['privacySettings', 0, 'isActive'], false)
          ? t('global_configuration.candidate_photo_blurred')
          : t('global_configuration.candidate_photo_unblurred');
      }
      default:
        return 'new';
    }
  };

  const getMinMaxTime = (type, key, state) => {
    return type === 'endTime' && basicDetails.getIn(['session', 'start'], Map()).size
      ? {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 45, format: 'PM' }))
          ),
          minTime: formatDateTime(
            fromJS(addedMinutes(formatDateTime(basicDetails?.getIn(['session', 'start'], 15))))
          ),
        }
      : type === 'independentEndTime' &&
        basicDetails?.getIn(['workingDays', `${key}`, 'session'], {}).size
      ? {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 45, format: 'PM' }))
          ),
          minTime: formatDateTime(
            fromJS(
              addedMinutes(
                formatDateTime(
                  basicDetails?.getIn(['workingDays', `${key}`, 'session', 'start'], 15)
                )
              )
            )
          ),
        }
      : type === 'breakEnd' && state.size
      ? {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 30, format: 'PM' }))
          ),
          minTime: formatDateTime(
            fromJS(addedMinutes(formatDateTime(state.getIn(['session', 'start'], Map()))))
          ),
        }
      : {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 30, format: t('date.pm') }))
          ),
          minTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 6, minute: 30, format: t('date.am') }))
          ),
        };
  };

  const displayRow = (key, heading, subHeader, size) => {
    return (
      <div className="row">
        <div className="col-md-12">
          <Accordion.Toggle
            as={Card.Header}
            onClick={() => {
              setArrow({ [key]: !arrow[key] });
            }}
            eventKey={key}
            className="icon_remove_leave pl-0"
          >
            <div className="d-flex justify-content-between">
              <div className="d-flex">
                <i
                  className={`fa fa-chevron-${arrow[key] ? 'up' : 'down'} f-14 icon-blue mt-2`}
                  aria-hidden="true"
                ></i>
                <div className="pl-3">
                  <div className="f-16 digi-brown remove_hover">
                    <b>
                      <Trans i18nKey={heading}></Trans>
                    </b>
                    {!arrow[key] && (
                      <p>
                        <span>{getSubHeader(subHeader, size)}</span>
                      </p>
                    )}
                  </div>
                </div>
              </div>
              {key === '3' && arrow['3'] && (
                <div
                  className="float-right text-blue ml-auto remove_hover"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpen('break', 'create', item);
                  }}
                >
                  + <Trans i18nKey={'global_configuration.add_new_break'}></Trans>
                </div>
              )}
              {key === '4' && arrow['4'] && (
                <div
                  className="float-right text-blue ml-auto remove_hover"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpen('event', 'create', item);
                  }}
                >
                  + <Trans i18nKey={'global_configuration.add_new_event'}></Trans>
                </div>
              )}
              {/* {key === '6' && arrow['6'] && (
                <div
                  className="float-right text-blue ml-auto remove_hover"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpen('event', 'create', item);
                  }}
                >
                  <Trans i18nKey={'dashboard_view.configure'}></Trans>
                </div>
              )} */}
            </div>
          </Accordion.Toggle>
        </div>
      </div>
    );
  };

  return (
    <React.Fragment>
      <div>
        <div>
          <div className="pt-3 pl-3">
            <Accordion defaultActiveKey="" className="mt-3">
              {displayRow('0', 'global_configuration.time_zone', 'timeZone')}
              <Accordion.Collapse eventKey="0" className="bg-white">
                <Card.Body className=" innerbodyLeave border-top-remove ml-2">
                  <div className="container ">
                    <div className="row">
                      <div className="col-md-5 col-xl-3 col-lg-4 col-sm-6 col-xl-3">
                        <AutoComplete
                          placeholder={t('global_configuration.set_time_zone')}
                          value={
                            !params.programID
                              ? basicDetails.get('timeZone', '')
                              : settings.get('timeZone', '')
                          }
                          isClearable={false}
                          options={timeZones}
                          onChange={(e, val) => {
                            handleChange({ name: 'timeZone', event: val });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              <hr />

              {!params.programID &&
                displayRow('1', 'global_configuration.select_languages', 'languages')}
              {!params.programID && (
                <div>
                  <Accordion.Collapse eventKey="1" className="bg-white">
                    <Card.Body className=" innerbodyLeave border-top-remove ml-2">
                      <div className="container">
                        <div className="row">
                          <div className="col">
                            <FormLabel component="legend">
                              <div className="row justify-content-between ml-2">
                                <div className="mb-2 col-">
                                  <Trans i18nKey={'global_configuration.choose_languages'}></Trans>
                                </div>
                                <div className="mb-2 col-">
                                  <Trans i18nKey={'global_configuration.defaultLanguage'}></Trans>:{' '}
                                  {basicDetails
                                    .get('language', List())
                                    .filter((i) => i.get('isDefault', '') === true)
                                    .reduce((_, el) => el.get('name', 'N/A'), 'N/A')}
                                </div>
                              </div>
                            </FormLabel>
                            <FormControl component="fieldset">
                              <FormGroup aria-label="position" row>
                                <div className="row">
                                  {languages
                                    .filter((i) => i.value !== 'en')
                                    .map((language, key) => (
                                      <div className="col-sm-6 col-md-4 col-lg-3" key={key}>
                                        <FormControlLabel
                                          className="f-11 float-left"
                                          key={language.key}
                                          value={language.value}
                                          control={<Checkbox size="small" color="primary" />}
                                          label={language.key}
                                          labelPlacement="end"
                                          onChange={(e) => {
                                            handleLanguageCheck(e);
                                          }}
                                          checked={basicDetails
                                            .get('language', List())
                                            .map((i) => i.get('code', ''))
                                            .includes(language.value)}
                                        />{' '}
                                        <div className="clearfix"> </div>
                                      </div>
                                    ))}
                                </div>
                              </FormGroup>
                            </FormControl>
                          </div>
                        </div>
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                  <hr />
                </div>
              )}

              {!params.programID &&
                displayRow('2', 'global_configuration.working_days', 'workingDays')}
              {!params.programID && (
                <div>
                  <Accordion.Collapse eventKey="2" className="bg-white">
                    <Card.Body className="innerbodyLeave border-top-remove ml-2">
                      <div className="container ">
                        <div className="float-left">
                          <FormLabel component="legend">
                            {' '}
                            <Trans i18nKey={'global_configuration.working_hours'}></Trans>
                          </FormLabel>
                        </div>
                        <div className="clearfix"> </div>

                        <div className="schedule-date-picker-container d-flex">
                          <div>
                            <DatePicker
                              selected={getDateFormat('start')}
                              onChange={(date) => handleChange({ name: 'start', event: date })}
                              showTimeSelect
                              showTimeSelectOnly
                              timeIntervals={15}
                              timeCaption="Time"
                              dateFormat="h:mm aa"
                              className="global-date-picker-input"
                            />
                          </div>
                          <div className="mt-2 m-2">
                            <Trans i18nKey={'global_configuration.to'}></Trans>
                          </div>
                          <div>
                            <DatePicker
                              {...getMinMaxTime('endTime')}
                              selected={getDateFormat('end')}
                              onChange={(date) => handleChange({ name: 'end', event: date })}
                              showTimeSelect
                              showTimeSelectOnly
                              timeIntervals={15}
                              timeCaption="Time"
                              dateFormat="h:mm aa"
                              className="global-date-picker-input"
                            />
                          </div>
                        </div>
                        <div className="mt-2 float-left">
                          <FormControl component="fieldset">
                            <FormGroup aria-label="position" row>
                              <FormControlLabel
                                value={basicDetailsState.get('isIndependentHours', false)}
                                control={
                                  <Checkbox
                                    color="primary"
                                    checked={basicDetailsState.get('isIndependentHours', false)}
                                    onChange={(e) =>
                                      handleChange({
                                        name: 'isIndependentHours',
                                        event: e.target.checked,
                                      })
                                    }
                                  />
                                }
                                label={i18n.t('global_configuration.set_independent')}
                                labelPlacement="end"
                              />
                            </FormGroup>
                          </FormControl>
                        </div>
                        <div className="clearfix"> </div>
                        <div className="">
                          <div className="d-flex float-left">
                            <FormLabel className="">
                              <Trans i18nKey={'global_configuration.days.days'}></Trans>
                            </FormLabel>
                            {basicDetailsState.get('isIndependentHours', false) && (
                              <FormLabel component="legend">
                                <Trans i18nKey={'global_configuration.time'}></Trans>
                              </FormLabel>
                            )}
                          </div>
                          <div className="clearfix"> </div>
                          {!basicDetailsState.get('isIndependentHours', false) ? (
                            <>
                              {days.map((day, key) => (
                                <div key={key} className="min-wd-250">
                                  <FormGroup aria-label="position" row>
                                    <FormControlLabel
                                      checked={basicDetails
                                        .get('workingDays', List())
                                        .filter((y) => y.get('name', '') === day)
                                        .reduce((_, el) => {
                                          return el.get('isActive', false);
                                        }, false)}
                                      control={<Checkbox color="primary" />}
                                      onChange={(e) => handleWorkingDaysCheck(e, key)}
                                      label={day}
                                      labelPlacement="end"
                                    />
                                  </FormGroup>
                                </div>
                              ))}
                            </>
                          ) : (
                            <>
                              {independentHours.map((day, key) => (
                                <div key={key} className="row">
                                  <div className="col-md-3 col-3 col-sm-3">
                                    <FormGroup aria-label="position" row>
                                      <FormControlLabel
                                        checked={basicDetails
                                          .get('workingDays', List())
                                          .filter((y) => y.get('name', '') === day.day)
                                          .reduce((_, el) => {
                                            return el.get('isActive', false);
                                          }, false)}
                                        control={<Checkbox color="primary" />}
                                        label={day.day}
                                        labelPlacement="end"
                                        onChange={(e) =>
                                          handleWorkingDaysCheck(e, key, 'independent')
                                        }
                                      />
                                    </FormGroup>
                                  </div>
                                  <div className="col-md-9 col-9 col-sm-9">
                                    <div className="schedule-date-picker-container d-flex">
                                      <div>
                                        <DatePicker
                                          disabled={
                                            !basicDetails
                                              .get('workingDays', List())
                                              .filter((y) => y.get('name', '') === day.day)
                                              .reduce((_, el) => {
                                                return el.get('isActive', false);
                                              }, false)
                                          }
                                          selected={getDateFormat('independentStart', key)}
                                          onChange={(date) =>
                                            handleChange({
                                              name: 'independentStart',
                                              event: date,
                                              key,
                                            })
                                          }
                                          showTimeSelect
                                          showTimeSelectOnly
                                          timeIntervals={15}
                                          timeCaption="Time"
                                          dateFormat="h:mm aa"
                                          className="global-date-picker-input"
                                        />
                                      </div>
                                      <div className="mt-2 mb-2 ml-3 mr-3">
                                        <Trans i18nKey={'global_configuration.to'}></Trans>
                                      </div>
                                      <div>
                                        <DatePicker
                                          {...getMinMaxTime('independentEndTime', key)}
                                          disabled={
                                            !basicDetails
                                              .get('workingDays', List())
                                              .filter((y) => y.get('name', '') === day.day)
                                              .reduce((_, el) => {
                                                return el.get('isActive', false);
                                              }, false)
                                          }
                                          onChange={(date) =>
                                            handleChange({
                                              name: 'independentEnd',
                                              event: date,
                                              key,
                                            })
                                          }
                                          selected={getDateFormat('independentEnd', key)}
                                          showTimeSelect
                                          showTimeSelectOnly
                                          timeIntervals={15}
                                          timeCaption="Time"
                                          dateFormat="h:mm aa"
                                          className="global-date-picker-input"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </>
                          )}
                        </div>
                        <div className="clearfix"> </div>
                      </div>
                    </Card.Body>
                  </Accordion.Collapse>
                  <hr />
                </div>
              )}

              {displayRow('3', 'global_configuration.breaks', 'breaks')}
              <Accordion.Collapse eventKey="3" className="bg-white">
                <Card.Body className=" innerbodyLeave border-top-remove ml-2">
                  <div className="container">
                    <div>
                      {!params.programID
                        ? basicDetails.get('breaks', List()).size
                          ? basicDetails.get('breaks', List()).map((item, key) => (
                              <div key={key} className="mb-4 d-flex justify-content-between">
                                <div>
                                  <div className="bold mb-2">{item.get('name', '10')}</div>
                                  <div className="mb-2">
                                    {`${item.getIn(
                                      ['session', 'start', 'hour'],
                                      '00'
                                    )}:${item.getIn(
                                      ['session', 'start', 'minute'],
                                      'am'
                                    )} ${item.getIn(['session', 'start', 'format'], '06')}` +
                                      ' - ' +
                                      `${item.getIn(['session', 'end', 'hour'], '')}:${item.getIn(
                                        ['session', 'end', 'minute'],
                                        '00'
                                      )} ${item.getIn(['session', 'end', 'format'], 'pm')}`}
                                  </div>
                                  {item
                                    .get('workingDays', List)
                                    .sort(function (a, b) {
                                      return daysOrder[a] - daysOrder[b];
                                    })
                                    .map((workingDay, key) => (
                                      <Chip
                                        key={key}
                                        variant="outlined"
                                        className="mr-2"
                                        size="small"
                                        label={workingDay}
                                      />
                                    ))}
                                </div>
                                <div className="float-right mt-5">
                                  <img
                                    src={EditIcon}
                                    alt="edit"
                                    className="digi-pr-12 remove_hover"
                                    onClick={() => handleOpen('break', 'edit', item)}
                                  />
                                  <img
                                    src={DeleteIcon}
                                    alt="Delete"
                                    className="digi-pr-12 remove_hover"
                                    onClick={() => handleOpen('breakDelete', 'delete', item)}
                                  />
                                </div>
                              </div>
                            ))
                          : ''
                        : ''}

                      {params.programID
                        ? programSettingBreaks.size
                          ? programSettingBreaks.map((item, key) => (
                              <div key={key} className="mb-4 d-flex justify-content-between">
                                <div>
                                  <div className="bold mb-2">{item.get('name', '10')}</div>
                                  <div className="mb-2">
                                    {`${item.getIn(
                                      ['session', 'start', 'hour'],
                                      '00'
                                    )}:${item.getIn(
                                      ['session', 'start', 'minute'],
                                      'am'
                                    )} ${item.getIn(['session', 'start', 'format'], '06')}` +
                                      ' - ' +
                                      `${item.getIn(['session', 'end', 'hour'], '')}:${item.getIn(
                                        ['session', 'end', 'minute'],
                                        '00'
                                      )} ${item.getIn(['session', 'end', 'format'], 'pm')}`}
                                  </div>
                                  {item
                                    .get('workingDays', List)
                                    .sort(function (a, b) {
                                      return daysOrder[a] - daysOrder[b];
                                    })
                                    .map((workingDay, key) => (
                                      <Chip
                                        key={key}
                                        variant="outlined"
                                        className="mr-2"
                                        size="small"
                                        label={workingDay}
                                      />
                                    ))}
                                </div>

                                {item.get('isInstitution', false) ? (
                                  <span className="float-right title digi-mt-16">
                                    <Trans i18nKey={'break_existed'} />
                                  </span>
                                ) : (
                                  <div className="float-right mt-5">
                                    <img
                                      src={EditIcon}
                                      alt="edit"
                                      className="digi-pr-12 remove_hover"
                                      onClick={() => handleOpen('break', 'edit', item)}
                                    />
                                    <img
                                      src={DeleteIcon}
                                      alt="Delete"
                                      className="digi-pr-12 remove_hover"
                                      onClick={() => handleOpen('breakDelete', 'delete', item)}
                                    />
                                  </div>
                                )}
                              </div>
                            ))
                          : ''
                        : ''}
                    </div>
                    {!params.programID &&
                      !basicDetails.get('breaks', List()).size &&
                      addBreakEventReusable('break', item, 'add_break')}
                    {params.programID &&
                      !programSettingBreaks.size &&
                      addBreakEventReusable('break', item, 'add_break')}
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              {open['break'] && (
                <AddEditBreakModal
                  addedMinutes={addedMinutes}
                  settingId={id}
                  updateProgramBasicDetails={updateProgramBasicDetails}
                  getProgramID={getProgramID}
                  institutionId={institutionId}
                  programID={params.programID}
                  item={item}
                  open={open}
                  handleClose={() => handleClose()}
                  CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
                  title={modalTitle}
                  updateBreak={updateBreak}
                  getMinMaxTime={getMinMaxTime}
                  setData={setData}
                  totalBreaks={
                    !params.programID ? basicDetails.get('breaks', List()) : programSettingBreaks
                  }
                  // totalBreaks={programSettingBreaks}
                  defaultValues={basicDetails.get('session', List())}
                  institutionHeader={institutionHeader}
                />
              )}
              {open['breakDelete'] && (
                <DeleteModal
                  type={type}
                  updateProgramBasicDetails={updateProgramBasicDetails}
                  getProgramID={getProgramID}
                  CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
                  institutionId={institutionId}
                  programID={params.programID}
                  item={item}
                  settingId={id}
                  open={open}
                  handleClose={() => handleClose()}
                  updateBreak={updateBreak}
                  institutionHeader={institutionHeader}
                />
              )}
              <hr />
              {displayRow('4', 'global_configuration.events', 'events')}
              <Accordion.Collapse eventKey="4" className="bg-white">
                <Card.Body className=" innerbodyLeave border-top-remove ml-2">
                  <div className="container">
                    <div>
                      {!params.programID
                        ? basicDetails.get('eventType', List()).size
                          ? basicDetails.get('eventType', List()).map((item, key) => (
                              <div key={key} className="mb-4 d-flex justify-content-between">
                                <div>
                                  <div className="bold mb-2">{item.get('name', '')}</div>
                                  <div className="mb-2">
                                    <Trans i18nKey={'global_configuration.leave'}></Trans> :
                                    {item.get('isLeave', false) === true ? ' Yes' : ' No'}
                                  </div>
                                </div>

                                <div className="float-right mt-3">
                                  <img
                                    src={EditIcon}
                                    alt="edit"
                                    className="digi-pr-12 remove_hover"
                                    onClick={() => handleOpen('event', 'edit', item)}
                                  />
                                  <img
                                    src={DeleteIcon}
                                    alt="Delete"
                                    className="digi-pr-12 remove_hover"
                                    onClick={() => handleOpen('eventDelete', 'delete', item)}
                                  />
                                </div>
                              </div>
                            ))
                          : ''
                        : ''}
                      {params.programID
                        ? programSettingEvents.size
                          ? programSettingEvents.map((item, key) => (
                              <div key={key} className="mb-4 d-flex justify-content-between">
                                <div>
                                  <div className="bold mb-2">{item.get('name', '')}</div>
                                  <div className="mb-2">
                                    <Trans i18nKey={'global_configuration.leave'}></Trans> :
                                    {item.get('isLeave', false) === true ? ' Yes' : ' No'}
                                  </div>
                                </div>

                                {item.get('isInstitution', false) ? (
                                  <span className="float-right title digi-mt-16">
                                    <Trans i18nKey={'break_existed'} />
                                  </span>
                                ) : (
                                  <div className="float-right mt-5">
                                    <img
                                      src={EditIcon}
                                      alt="edit"
                                      className="digi-pr-12 remove_hover"
                                      onClick={() => handleOpen('event', 'edit', item)}
                                    />
                                    <img
                                      src={DeleteIcon}
                                      alt="Delete"
                                      className="digi-pr-12 remove_hover"
                                      onClick={() => handleOpen('eventDelete', 'delete', item)}
                                    />
                                  </div>
                                )}
                              </div>
                            ))
                          : ''
                        : ''}
                    </div>
                    {!params.programID &&
                      !basicDetails.get('eventType', List()).size &&
                      addBreakEventReusable('event', item, 'global_configuration.add_event')}
                    {params.programID &&
                      !programSettingEvents.size &&
                      addBreakEventReusable('event', item, 'global_configuration.add_event')}

                    {open['event'] && (
                      <AddEditEventModal
                        item={item}
                        settingId={id}
                        open={open}
                        handleClose={() => handleClose()}
                        title={modalTitle}
                        updateEventType={updateEventType}
                        updateProgramBasicDetails={updateProgramBasicDetails}
                        setData={setData}
                        institutionHeader={institutionHeader}
                        getProgramID={getProgramID}
                        CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
                        institutionId={institutionId}
                        programID={params.programID}
                      />
                    )}
                    {open['eventDelete'] && (
                      <DeleteModal
                        type={type}
                        item={item}
                        settingId={id}
                        updateProgramBasicDetails={updateProgramBasicDetails}
                        getProgramID={getProgramID}
                        CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
                        institutionId={institutionId}
                        programID={params.programID}
                        open={open}
                        handleClose={() => handleClose()}
                        updateEventType={updateEventType}
                        institutionHeader={institutionHeader}
                      />
                    )}
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              <hr />
              {displayRow('5', 'global_configuration.gender_segregation', 'gender')}
              <Accordion.Collapse eventKey="5" className="bg-white">
                <Card.Body className=" innerbodyLeave border-top-remove ml-0">
                  <div className="container">
                    <FormControl component="fieldset">
                      <RadioGroup
                        row
                        aria-label="position"
                        name="position"
                        value={
                          !params.programID
                            ? basicDetails.get('isGenderSegregation', true) === true
                              ? 'separate'
                              : 'mix'
                            : settings.get('isGenderSegregation', true) === true
                            ? 'separate'
                            : 'mix'
                        }
                        onChange={(e) =>
                          handleChange({ name: 'isGenderSegregation', event: e.target.value })
                        }
                      >
                        <FormControlLabel
                          value="separate"
                          control={<Radio color="primary" />}
                          label={t('global_configuration.groupMaleAndFemale')}
                        />
                        <FormControlLabel
                          value="mix"
                          control={<Radio color="primary" />}
                          label={t('global_configuration.no_gender_segregation')}
                        />
                      </RadioGroup>
                    </FormControl>
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              <hr />
              {!params.programID && (
                <EmailIdConFiguration
                  displayRow={displayRow}
                  settingId={id}
                  header={institutionHeader}
                  emailIdConfig={basicDetails.get('emailIdConfiguration', Map())}
                />
              )}
              {displayRow('7', 'global_configuration.privacy_settings', 'privacySettings')}
              <Accordion.Collapse eventKey="7">
                <Card.Body className="innerbodyLeave border-top-remove ml-4">
                  {basicDetails.size > 0 && (
                    <FormControlLabel
                      control={
                        <Checkbox
                          onChange={(e) =>
                            handleChange({ name: 'blurPhotos', event: e.target.checked })
                          }
                          name="blurPhotos"
                          color="primary"
                          defaultChecked={basicDetails.getIn(
                            ['privacySettings', 0, 'isActive'],
                            false
                          )}
                        />
                      }
                      label={t('global_configuration.blur_candidate_photos')}
                    />
                  )}
                </Card.Body>
              </Accordion.Collapse>
            </Accordion>
          </div>
        </div>
      </div>

      <Modal dialogClassName="model-800" centered>
        <Modal.Body>
          <div className="p-2">
            <div className="d-flex justify-content-between">
              <p className="f-20 mb-2">
                <Trans i18nKey={'global_configuration.email_configuration'}></Trans>
              </p>
            </div>

            <div className="row">
              <div className="col-sm-6 col-md-4 col-lg-6">
                <div className="digi-pb-12">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'Display Name'}
                    label={
                      <div>
                        <Trans i18nKey={'global_configuration.display_name'}></Trans>
                      </div>
                    }
                  />
                </div>

                <div className="digi-pb-12">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'Password'}
                    label={
                      <div>
                        <Trans i18nKey={'password'}></Trans>
                      </div>
                    }
                  />
                </div>

                <div className="digi-pb-12">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'SMTP Client'}
                    label={
                      <div>
                        <Trans i18nKey={'global_configuration.smtp_client'}></Trans>
                      </div>
                    }
                  />
                </div>
              </div>
              <div className="col-sm-6 col-md-4 col-lg-6">
                <div className="digi-pb-12">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'Email Id'}
                    label={
                      <div>
                        <Trans i18nKey={'emailId'}></Trans>
                      </div>
                    }
                  />
                </div>

                <div className="digi-pb-12">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'Re - Enter Password'}
                    label={
                      <div>
                        <Trans i18nKey={'global_configuration.re_enter_password'}></Trans>
                      </div>
                    }
                  />
                </div>

                <div className="digi-pb-12">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    placeholder={'Port Number'}
                    label={
                      <div>
                        <Trans i18nKey={'global_configuration.port_number'}></Trans>
                      </div>
                    }
                  />
                </div>
              </div>

              <FormGroup className="text-skyblue remove_hover pl-3 bold mb-0">
                <FormControlLabel
                  control={<Checkbox defaultChecked color="primary" />}
                  label="TLS/SSL"
                />
              </FormGroup>
            </div>
          </div>
        </Modal.Body>

        <Modal.Footer className="border-none">
          <MButton variant="outlined">
            <Trans i18nKey={'cancel'}></Trans>
          </MButton>

          <MButton variant="outlined" className="digi-blue-button">
            <Trans i18nKey={'global_configuration.send_test_email'}></Trans>
          </MButton>

          <b className="pr-2">
            <MButton variant="contained" color="primary">
              <Trans i18nKey={'save'}></Trans>
            </MButton>
          </b>
        </Modal.Footer>
      </Modal>
    </React.Fragment>
  );
}
BasicDetails.propTypes = {
  institution: PropTypes.instanceOf(List),
  getBasicDetails: PropTypes.func,
  editBasicDetails: PropTypes.func,
  updateBreak: PropTypes.func,
  updateEventType: PropTypes.func,
  updateWorkingDays: PropTypes.func,
  updateLanguage: PropTypes.func,
  basicDetails: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  programSettings: PropTypes.instanceOf(Map),
  getProgramSettings: PropTypes.func,
  updateProgramBasicDetails: PropTypes.func,
  updatePrivacySettings: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    basicDetails: selectBasicDetails(state),
    programSettings: selectProgramSettings(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, { ...actions, ...action })
)(BasicDetails);
