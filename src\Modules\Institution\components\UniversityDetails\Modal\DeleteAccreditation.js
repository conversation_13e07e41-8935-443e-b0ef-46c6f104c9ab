import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';

import ArchiveImg from '../../../../../Assets/archive.png';
import MButton from 'Widgets/FormElements/material/Button';

function DeleteAccreditation({ show, handleClose, type, handleDelete, accreditationDeleteName }) {
  const name =
    typeof accreditationDeleteName === 'string'
      ? accreditationDeleteName
      : accreditationDeleteName.value;
  return (
    <Modal show={show} centered>
      <Modal.Body className="pt-0 pb-0">
        <p className="mb-3 mt-3 f-22">
          <img className="mr-2" alt={'Archive'} src={ArchiveImg} />
          <Trans
            i18nKey={'add_colleges.Delete_Accreditation_Details'}
            values={{ type: type }}
          ></Trans>
        </p>
        <div>
          {' '}
          <Trans
            i18nKey={`add_colleges.Accreditation_Permanatly_Remove`}
            values={{ name: name }}
          ></Trans>
          <br />
          <br />
          <Trans
            i18nKey={'add_colleges.Accreditation_Permanatly_Remove2'}
            values={{ name: name }}
          ></Trans>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton className="mr-3" color="inherit" variant="outlined" clicked={() => handleClose()}>
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>

        <MButton
          color="red"
          clicked={() => {
            if (type === 'delete') {
              handleDelete('delete');
              handleClose();
            } else {
              handleDelete('');
              handleClose();
            }
          }}
        >
          <Trans i18nKey={`${type}`}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

DeleteAccreditation.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
  type: PropTypes.string,
  accreditationDeleteName: PropTypes.string,
  handleDelete: PropTypes.func,
};
export default DeleteAccreditation;
