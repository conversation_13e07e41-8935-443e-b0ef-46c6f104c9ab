import React, { useState, useContext, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import { fromJS, List, Map } from 'immutable';
import { CSVLink } from 'react-csv';
import { Trans } from 'react-i18next';
import { useDispatch } from 'react-redux';
import Papa from 'papaparse';
import FileUpload from '../../../../../Assets/fileupload.svg';
import GetAppIcon from '@mui/icons-material/GetApp';
import CloseIcon from '@mui/icons-material/Close';
import { setData } from '_reduxapi/user_management/v2/actions';
import { staffLabelContext } from 'Modules/UserManagement/v2/StaffManagement/index';
import { useStylesFunction } from 'Modules/ProgramInput/v2/piUtil';
import { ValidateImportData } from './AddStaffComponents';
import StaffDataValidationModal from '../AddNewStaff/StaffDataValidationModal';
import { staffDetailContext } from './AddSingleStaff';
import ImportingDataModal from './ImportingDataModal';
// import * as xlsx from 'xlsx';
const emptyStaffData = (type) =>
  Map({
    first_name: '',
    middle_name: '',
    last_name: '',
    family_name: '',
    code: '+91',
    mobile: '',
    email: '',
    gender: '',
    ...(type === 'student' && { programName: '', enrollmentYear: '', batch: '', academicNo: '' }),
    ...(type === 'staff' && { employeeId: '' }),
  });
let importData = {
  first_name: 'FirstName',
  middle_name: 'MiddleName',
  last_name: 'LastName',
  family_name: 'FamilyName',
  employeeId: 'EmployeeId',
  academicNo: 'AcademicNo',
  batch: 'Batch',
  enrollmentYear: 'EnrollmentYear',
  programName: 'ProgramName',
  mobile: 'Phone',
  email: 'Email',
  gender: 'Gender',
};

export const constructData = (labelData, importData, type) => {
  return Object.values({
    ...(labelData.getIn(['firstName', 'isActive'], false) && {
      FirstName: importData['first_name'],
    }),
    ...(labelData.getIn(['middleName', 'isActive'], false) && {
      MiddleName: importData['middle_name'],
    }),
    ...(labelData.getIn(['lastName', 'isActive'], false) && {
      LastName: importData['last_name'],
    }),
    ...(labelData.getIn(['familyName', 'isActive'], false) && {
      FamilyName: importData['family_name'],
    }),
    ...(labelData.getIn(['user_id', 'isActive'], false) && {
      [`${type === 'staff' ? 'EmployeeId' : 'AcademicNo'}`]: importData[
        `${type === 'staff' ? 'employeeId' : 'academicNo'}`
      ],
    }),
    ...(labelData.getIn(['no', 'isActive'], false) && { Phone: importData['mobile'] }),
    ...(labelData.getIn(['email', 'isActive'], false) && { Email: importData['email'] }),
    ...(labelData.getIn(['gender', 'isActive'], false) && { Gender: importData['gender'] }),
    ...(labelData.getIn(['enrollment_year', 'isActive'], false) && {
      EnrollmentYear: importData['enrollmentYear'],
    }),
    ...(labelData.getIn(['program_no', 'isActive'], false) && {
      ProgramName: importData['programName'],
    }),
    ...(labelData.getIn(['batch', 'isActive'], false) && { Batch: importData['batch'] }),
  });
};
export const importDataFunc = (labelData, type) => {
  const getColumnHeading = (labelName) => {
    return `${labelData.getIn([labelName, 'displayName'], '')}${
      !labelData.getIn([labelName, 'isMandatory'], false) ? ' (Optional)' : ''
    }`;
  };

  if (Object.values(labelData).length === 0) return [[]];
  importData = {
    first_name: getColumnHeading('firstName'),
    middle_name: getColumnHeading('middleName'),
    last_name: getColumnHeading('lastName'),
    family_name: getColumnHeading('familyName'),
    [`${type === 'staff' ? 'employeeId' : 'academicNo'}`]: getColumnHeading('user_id'),
    gender: getColumnHeading('gender'),
    email: getColumnHeading('email'),
    mobile: `${getColumnHeading('no')} (${labelData.getIn(['no', 'defaultValue'], '')})`,
    ...(type === 'student' && { programName: getColumnHeading('program_no') }),
    ...(type === 'student' && { batch: getColumnHeading('batch') }),
    ...(type === 'student' && { enrollmentYear: getColumnHeading('enrollment_year') }),
  };
  return [constructData(labelData, importData, type)];
};
function BulkImport({ addUpload, setAddUpload }) {
  const classes = useStylesFunction();
  const dispatch = useDispatch();

  const { labelData, type } = useContext(staffLabelContext);
  const [csvFile, setCsvFile] = useState('');
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [staffData, setStaffData] = useState(List([emptyStaffData(type)]));
  const [validatedData, setValidatedData] = useState(Map());
  const [importedData, setImportedData] = useState(List());
  const [showImportingModal, setShowImportingModal] = useState(false);
  const [importPercent, setImportPercent] = useState(0);
  const [fileInfo, setFileInfo] = useState({
    name: '',
    imported: 0,
    total: 0,
  });
  useEffect(() => {
    setCsvFile('');
  }, []);

  const handleAddUploadClose = () => {
    setAddUpload(false);
    setCsvFile('');
  };
  const handleChange = (event) => {
    let csv = event.target.files[0];
    const fileName = csv.name;
    const format = fileName.split('.').pop().toLowerCase();
    if (!['xlsx', 'csv'].includes(format)) {
      event.target.value = '';
      return dispatch(
        setData(
          Map({
            message: `You have uploaded an invalid file type. Accepted file formats are .csv & .exl`,
          })
        )
      );
    }

    const updateData = (result) => {
      if (result.data.length) {
        const labelHeaders = csvHeader[0];
        const sortedCSV = labelHeaders.sort();
        const excelColumn = Object.keys(result.data[0]).sort();
        const columnMatched = excelColumn.every((item, index) => item === sortedCSV[index]);
        if (!columnMatched) {
          dispatch(
            setData(Map({ message: `File headers are not matched, download template and proceed` }))
          );
          return;
        }
        setCsvFile(csv);
        const savedData = result.data.map((data) => ({
          ...(labelData.getIn(['firstName', 'isActive'], false) && {
            first_name: data[importData.first_name].toString().trim(),
          }),
          ...(labelData.getIn(['middleName', 'isActive'], false) && {
            middle_name: data[importData.middle_name].toString().trim(),
          }),
          ...(labelData.getIn(['lastName', 'isActive'], false) && {
            last_name: data[importData.last_name].toString().trim(),
          }),
          ...(labelData.getIn(['familyName', 'isActive'], false) && {
            family_name: data[importData.family_name].toString().trim(),
          }),
          ...(labelData.getIn(['user_id', 'isActive'], false) && {
            [`${type === 'staff' ? 'employeeId' : 'academicNo'}`]: data[
              importData[`${type === 'staff' ? 'employeeId' : 'academicNo'}`]
            ]
              .toString()
              .trim(),
          }),
          ...(labelData.getIn(['no', 'isActive'], false) && {
            code: labelData.getIn(['no', 'defaultValue'], ''),
            mobile: data[importData.mobile].toString().trim(),
          }),
          ...(labelData.getIn(['email', 'isActive'], false) && {
            email: data[importData.email].toString().trim(),
          }),
          ...(labelData.getIn(['gender', 'isActive'], false) && {
            gender: data[importData.gender].toString().trim(),
          }),
          ...(labelData.getIn(['program_no', 'isActive'], false) && {
            programName: data[importData.programName].toString().trim(),
          }),
          ...(labelData.getIn(['batch', 'isActive'], false) && {
            batch: data[importData.batch].toString().trim(),
          }),
          ...(labelData.getIn(['enrollment_year', 'isActive'], false) && {
            enrollmentYear: data[importData.enrollmentYear].toString().trim(),
          }),
        }));
        setStaffData(fromJS(savedData));
        return;
      }
      dispatch(setData(Map({ message: `Empty File` })));
    };
    // if (format === 'xlsx') {
    //   const reader = new FileReader();
    //   reader.onload = (e) => {
    //     const data = e.target.result;
    //     const workbook = xlsx.read(data, { type: 'array' });
    //     const sheetName = workbook.SheetNames[0];
    //     const worksheet = workbook.Sheets[sheetName];
    //     const json = xlsx.utils.sheet_to_json(worksheet);
    //     const result = { data: json };
    //     updateData(result);
    //   };
    //   reader.readAsArrayBuffer(csv);
    //   return;
    // }
    Papa.parse(csv, {
      complete: updateData,
      header: true,
      // delimiter: '\t',
      skipEmptyLines: true,
    });
    event.target.value = '';
  };
  const isDisableSave = () => (csvFile ? false : true);
  const dispatchSetData = (data) => dispatch(setData(data));

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    handleChange({ target: e.dataTransfer });
  };
  const handleDrag = function (e) {
    e.preventDefault();
    e.stopPropagation();
  };
  const values = {
    staffData,
    setImportedData,
    setValidatedData,
    showValidationModal,
    setShowValidationModal,
    isDisableSave,
    bulkImport: true,
    addUpload,
    handleAddUploadClose,
    setShowImportingModal,
    setImportPercent,
    setFileInfo,
    csvFile,
  };
  const validationModalProps = {
    importedData,
    validatedData,
    setData: dispatchSetData,
  };
  const csvHeader = useMemo(() => importDataFunc(labelData, type), [labelData, type]); //eslint-disable-line

  return (
    <staffDetailContext.Provider value={values}>
      {showValidationModal && (
        <StaffDataValidationModal
          show={showValidationModal}
          onClose={() => setShowValidationModal(false)}
          type={type}
          {...validationModalProps}
        />
      )}

      {showImportingModal && (
        <ImportingDataModal show={showImportingModal} percent={importPercent} fileInfo={fileInfo} />
      )}
      {addUpload && (
        <DialogModal
          show={addUpload}
          onClose={handleAddUploadClose}
          // maxWidth={'xs'}
          // fullWidth={true}
        >
          <div className="p-4">
            <div className="d-flex justify-content-between">
              <p className="font-weight-bold mb-3 text-left f-20 pt-1 pr-2">Bulk Import Staffs</p>
              <div className="d-flex align-items-center bold mb-3 f-15 pt-2 remove_hover text-skyblue">
                <CSVLink data={csvHeader} filename={'sample_import_staff.csv'}>
                  <div className="mb-2 d-flex">
                    <GetAppIcon className="mr-2" size="small" />
                    <span>Download template</span>
                  </div>
                </CSVLink>
              </div>
            </div>
            <div
              className="borderDashed border-radious-8"
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                accept={['.csv', '.xlsx']}
                onChange={handleChange}
                className={classes.inputButton}
                id="icon-button-file"
                type="file"
              />
              <label htmlFor="icon-button-file" className="w-100 mb-0 remove_hover">
                <div className="row row align-items-center">
                  <div className="col-9">
                    <div className="pl-3">
                      {csvFile === '' ? (
                        <>
                          <p className="font-weight-bold mb-0 text-left f-18 pt-2">
                            Drag or Drop the files
                          </p>
                          <p className="text-NewLightgray mb-0 f-14">
                            {' '}
                            .xlsx or .csv format accepted{' '}
                          </p>
                        </>
                      ) : (
                        <div className="d-flex align-items-center">
                          <p className="text-NewLightgray mb-0 f-14"> {csvFile.name}</p>
                          <CloseIcon
                            className="pl-2"
                            onClick={(event) => {
                              event.preventDefault();
                              setCsvFile('');
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="col-3">
                    <div className="uploadBg text-center text-skyblue">
                      <img alt="Importing data" src={FileUpload} className="mr-0 mb-0" />{' '}
                      <p className="bold mb-0 f-15 remove_hover text-skyblue">Upload</p>
                    </div>
                  </div>
                </div>
              </label>
            </div>

            <div className="d-flex justify-content-end pt-4">
              <MButton
                color="inherit"
                variant="outlined"
                className="mr-3"
                clicked={handleAddUploadClose}
              >
                <Trans i18nKey={'cancel'}></Trans>
              </MButton>
              <ValidateImportData />
            </div>
          </div>
        </DialogModal>
      )}
    </staffDetailContext.Provider>
  );
}

BulkImport.propTypes = {
  addUpload: PropTypes.bool,
  setAddUpload: PropTypes.func,
};
export default BulkImport;
