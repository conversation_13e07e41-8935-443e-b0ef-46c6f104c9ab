<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- <link rel="stylesheet" href="./css/style.css" /> -->
    <!-- <link rel="stylesheet" href="./css/style.css" /> -->
    <link rel="stylesheet" href="./css/style.css" />

    <title>DigiAssess</title>
    <link rel="icon" sizes="48x48" href="../icons/DA-icon.svg" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"
      rel="stylesheet"
    />
  </head>
  <body class="end custom-scrollbar">
    <!-- <button id="deserializeBtn">Convert JSON to HTML</button> -->
    <div class="pb-40">
      <!-- <img src="./image/CS_Header.jpg" class="w-100" /> -->
      <img src="./image/CS_Header.jpg" class="w-100" />
    </div>
    <div class="tbl-border">
      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="courseTitle" class="report-label" pl>Course Title:</label>
          <span
            class="report-input-content dc-course-title"
            id="title"
            contenteditable="true"
          ></span>
        </div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="courseCode" class="report-label">Course Code:</label>
          <span
            class="report-input-content report-input-college"
            id="courseCode"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="program" class="report-label">Program:</label>
          <span
            class="report-input-content report-input-college"
            id="program"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="department" class="report-label">Department:</label>
          <span
            class="report-input-content report-input-college"
            id="department"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="college" class="report-label">College:</label>
          <span
            class="report-input-content report-input-college"
            id="college"
            contenteditable="true"
          ></span>
        </div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="institution" class="report-label">Institution:</label>
          <span
            class="report-input-content report-input-institution"
            id="institution"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="version" class="report-label">Version:</label>
          <span
            class="report-input-content report-input-institution"
            id="version"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="lateRevision" class="report-label">Late Revision Date:</label>
          <span
            class="report-input-content report-input-institution"
            id="revisionDate"
            contenteditable="true"
          ></span>
        </div>
      </div>  
    </div>
    <div class="logo-bg">
      <!-- <img src="../course-report/images/course-report-logo.png" /> -->
      <img src="../course-report/images/course-report-logo.png" />
    </div>
    <div class="tbl-of-index">Table of Contents</div>
    <div class="mx-10">
      <div class="">
        <table class="w-100 table-of-contents">
          <tr class="bg-purple-dark">
            <th class="text-center f-normal p-4">Contents</th>
            <th class="text-center f-normal p-4" style="width: 10%">Page</th>
          </tr>

          <tr class="tbl-bg">
            <td class="p-4 pl-12">A. General information about the course:</td>
            <td class="text-center p-4">3</td>
          </tr>
          <tr class="tbl-bg">
            <td class="p-4 pl-12">
              B. Course Learning Outcomes (CLOs), Teaching Strategies and Assessment Methods
            </td>
            <td class="text-center p-4">4</td>
          </tr>
          <tr class="tbl-bg">
            <td class="p-4 pl-12">C. Course Content</td>
            <td class="text-center p-4">4</td>
          </tr>
          <tr class="tbl-bg">
            <td class="p-4 pl-12">D. Students Assessment Activities</td>
            <td class="text-center p-4">5</td>
          </tr>
          <tr class="tbl-bg">
            <td class="p-4 pl-12">E. Learning Resources and Facilities</td>
            <td class="text-center p-4">5</td>
          </tr>
          <tr class="tbl-bg">
            <td class="p-4 pl-12">F. Assessment of Course Quality</td>
            <td class="text-center p-4">5</td>
          </tr>
          <tr class="tbl-bg">
            <td class="p-4 pl-12">G. Specification Approval 6</td>
            <td class="text-center p-4">6</td>
          </tr>
        </table>
      </div>
    </div>
    <section id="sectionA">
    <h1>A. General information about the course:</h1>
    <h2 class="my-15">1. Course Identification</h2>
    <div class="mx-10">
      <div class="">
        <div class="tbl-border-bottom">
          <div class="form-full bg-purple-dark">
            <label for="courseTitle" class="content-label" pl>1. Credit hours</label>
            <span class="content-input" contenteditable="true"></span>
          </div>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <span
            class="report-input-content report-input-institution"
            id="creditHours"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="">
        <div class="tbl-border-bottom">
          <div class="form-full bg-purple-dark">
            <label for="courseTitle" class="content-label" pl>2. Course type</label>
            <span class="content-input" contenteditable="true"></span>
          </div>
        </div>
        <div class="w-100">
          <table class="bg-purple-dark w-100" id="courseType">
            <thead>
              <tr>
                <th class="text-center">A.</th>
                <th style="background-color: #f2f2f2" class="py-5">
                  <input
                    type="checkbox"
                    id="university"
                    name="university"
                    value="University"
                    
                  />
                  <label for="university" style="color: #6f7178">University</label>
                </th>
                <th style="background-color: #f2f2f2" class="py-5">
                  <input type="checkbox" id="college" name="college" value="College"  />
                  <label for="college" style="color: #6f7178">College</label>
                </th>
                <th style="background-color: #f2f2f2" class="py-5">
                  <input
                    type="checkbox"
                    id="department"
                    name="department"
                    value="Department"
                    
                  />
                  <label for="department" style="color: #6f7178">Department</label>
                </th>
                <th style="background-color: #f2f2f2" class="py-5">
                  <input type="checkbox" id="track" name="track" value="Track"  />
                  <label for="track" style="color: #6f7178">Track</label>
                </th>
                <th style="background-color: #f2f2f2" class="py-5">
                  <input type="checkbox" id="others" name="others" value="Others"  />
                  <label for="others" style="color: #6f7178">Others</label>
                </th>
              </tr>
            </thead>

            <tbody>
              <tr>
                <td class="text-center">B.</td>
                <td colspan="5" style="background-color: #f2f2f2">
                  <div class="w-100 d-flex">
                    <div class="w-50 border-right py-5">
                      <input
                        type="checkbox"
                        id="required"
                        name="required"
                        value="Required"
                        
                      />
                      <label for="required" style="color: #6f7178">Required</label>
                    </div>
                    <div class="py-5">
                      <input
                        type="checkbox"
                        id="elective"
                        name="elective"
                        value="Elective"
                        
                      />
                      <label for="elective" style="color: #6f7178">Elective</label>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="">
        <div class="tbl-border-bottom">
          <div class="form-full bg-purple-dark">
            <label for="courseTitle" class="content-label" pl
              >3. Level/year at which this course is offered:
            </label>
            <!-- <span class="content-input" id="LevelYear" contenteditable="true"></span> -->
          </div>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <span
            class="report-input-content report-input-institution"
            id="levelYearDetail"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="">
        <div class="tbl-border-bottom">
          <div class="form-full bg-purple-dark">
            <label for="courseTitle" class="content-label" pl>4. Course general Description:</label>
            <!-- <span class="content-input"  contenteditable="true"></span> -->
          </div>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <span
            class="report-input-content report-input-institution"
            id="generalDescriptionDetail"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="">
        <div class="tbl-border-bottom">
          <div class="form-full bg-purple-dark">
            <label for="courseTitle" class="content-label" pl
              >5. Pre-requirements for this course (if any):</label
            >
            <!-- <span class="content-input" contenteditable="true"></span> -->
          </div>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <span
            class="report-input-content report-input-institution"
            id="preRequirements"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="">
        <div class="tbl-border-bottom">
          <div class="form-full bg-purple-dark">
            <label for="courseTitle" class="content-label" pl
              >6. Co-requisites for this course (if any):</label
            >
            <!-- <span class="content-input" contenteditable="true"></span> -->
          </div>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <span
            class="report-input-content report-input-institution"
            id="coRequisites"
            contenteditable="true"
          ></span>
        </div>
      </div>
      <div class="">
        <div class="tbl-border-bottom">
          <div class="form-full bg-purple-dark">
            <label for="courseTitle" class="content-label" pl>7. Course Main Objective(s):</label>
            <!-- <span class="content-input" contenteditable="true"></span> -->
          </div>
        </div>
      </div>
      <div class="tbl-border-bottom">
        <div class="form-full">
          <span
            class="report-input-content report-input-institution"
            id="courseMainObjective"
            contenteditable="true"
          ></span>
        </div>
      </div>
    </div>
    <h2 class="my-15">
      2. Teaching mode <span style="color: black; font-size: 14px">(mark all that apply)</span>
    </h2>

    <div class="mx-10">
      <div class="w-100">
        <table class="w-100" id="teachingModeTable">
          <thead>
            <tr class="bg-purple-dark">
              <th class="text-center">No</th>
              <th class="text-center">Mode of Instruction</th>
              <th class="text-center">Contact Hours</th>
              <th class="text-center">Percentage</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td class="pl-12 py-5">Traditional classroom</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td>2</td>
              <td class="pl-12 py-5">E-learning</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td>3</td>
              <td class="pl-12 py-5">
                <div>
                  <div>Hybrid</div>
                  <ul class="bullet-list">
                    <li>Traditional classroom</li>
                    <li>E-learning</li>
                  </ul>
                </div>
              </td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td>4</td>
              <td class="pl-12 py-5">Distant Learning</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
     <div class="margin-x-y-15">
    <div class="d-flex justify-content w-100">
    <button id="addRowBtn">+ Row 's</button>
    </div>
     </div>
      <h2 class="my-15">
      3. Contact Hours
      <span style="color: black; font-size: 14px">(based on the academic semester)</span>
    </h2>
    <div class="mx-10">
      <div class="w-100">
        <table class="w-100" id="contactHoursTable">
          <thead>
            <tr class="bg-purple-dark">
              <th class="text-center">No</th>
              <th class="text-center w-75">Activity</th>
              <th class="text-center">Contact Hours</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-center py-5">1</td>
              <td>Lectures</td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center py-5">2</td>
              <td>Laboratory/Studio</td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center py-5">3</td>
              <td>Field</td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center py-5">4</td>
              <td>Tutorial</td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center py-5">5</td>
              <td>Others (specify)</td>
              <td contenteditable="true"></td>
            </tr>
            <tr class="outcome-row">
              <td class="py-5 pl-15" colspan="2">Total</td>
              <td>Others (specify)</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="margin-x-y-15">
      <div class="d-flex justify-content w-100">
      <button id="addRowContactHoursBtn">+ Row 's</button>
      </div>
       </div>
    <section id="sectionB">

    <div class="ptb-5">
      <h1 class="my-15">
        B. Course Learning Outcomes (CLOs), Teaching Strategies and Assessment Methods
      </h1>
      <div class="mx-10">
        <div class="w-100">
          <table class="w-100" id="cloTeachingAndAssessment">
            <thead>
              <tr class="bg-purple-dark">
                <th class="text-center">Code</th>
                <th class="text-center">Course Learning Outcomes</th>
                <th class="text-center">Code of CLOs aligned with program</th>
                <th class="text-center">Teaching Strategies</th>
                <th class="text-center">Assessment Methods</th>
              </tr>
            </thead>
            <tbody>
              <tr class="outcome-row">
                <td class="text-center">1.0</td>
                <td class="" colspan="4">Knowledge and understanding</td>
              </tr>
              <tr>
                <td class="text-center">1.1</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr>
                <td class="text-center">1.2</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr>
                <td class="text-center">1.3</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr class="outcome-row">
                <td class="text-center">2.0</td>
                <td class="" colspan="4">Skills</td>
              </tr>
              <tr>
                <td class="text-center">2.1</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr>
                <td class="text-center">2.2</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr>
                <td class="text-center">2.3</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr class="outcome-row">
                <td class="text-center">3.0</td>
                <td class="" colspan="4">Values, autonomy, and responsibility</td>
              </tr>
              <tr>
                <td class="text-center">3.1</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr>
                <td class="text-center">3.2</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
              <tr>
                <td class="text-center">3.3</td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
                <td contenteditable="true"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="margin-x-y-15">
        <div class="d-flex justify-content gap-10 w-100">
        <button id="addRowCourseLearningHeadingsBtn">+ Headings</button>
        <button id="addRowCourseLearningRowBtn">+ Row 's</button>
        </div>
      </div>
    </div>
  </section>
  <section id="sectionC">

    <h2 class="my-15">C. Course Content</h2>
    <div class="mx-10">
      <div class="w-100">
        <table class="w-100" id="courseContent">
          <thead>
            <tr class="bg-purple-dark">
              <th class="text-center">No</th>
              <th class="text-center w-75">List of Topics</th>
              <th class="text-center">Contact Hours</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-center py-5">1</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center py-5">2</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center py-5">3</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr class="outcome-row">
              <td class="py-5 pl-15" colspan="2">Total</td>
              <td contenteditable="true"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="margin-x-y-15">
      <div class="d-flex justify-content w-100">
      <button id="addRowCourseContentBtn">+ Row 's</button>
      </div>
    </div>
  </section>

    <section id="sectionD">

    <h2 class="my-15">D. Students Assessment Activities</h2>
    <div class="mx-10">
      <div class="w-100">
        <table class="w-100" id="studentAssessmentActivities">
          <thead>
            <tr class="bg-purple-dark">
              <th class="text-center px-5">No</th>
              <th class="text-center w-75">Assessment Activities *</th>
              <th class="text-center">Assessment timing (in week no)</th>
              <th class="text-center">Percentage of Total Assessment Score</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-center">1</td>
              <td class="pl-12 py-5" contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">2</td>
              <td class="pl-12 py-5" contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">3</td>
              <td class="pl-12 py-5" contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">4</td>
              <td class="pl-12 py-5" contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
          </tbody>
        </table>
        <p>
          *Assessment Activities (i.e., Written test, oral test, oral presentation, group project,
          essay, etc.).
        </p>
      </div>
    </div>
    <div class="margin-x-y-15">
      <div class="d-flex justify-content w-100">
      <button id="addRowAssessmentActivitiesBtn">+ Row 's</button>
      </div>
    </div>
  </section>
  <section id="sectionE">

    <h1>E. Learning Resources and Facilities</h1>
    <h2 class="my-15">1. References and Learning Resources</h2>
    <div class="mx-10">
      <div class="">
        <div class="w-100">
          <table class="w-100" id="learningResourceTable">
            <tbody>
              <tr class="">
                <td class="py-5 bg-purple-dark text-center">Essential References</td>
                <td class="w-75" contenteditable="true"></td>
              </tr>
              <tr class="">
                <td class="py-5 bg-purple-dark text-center">Supportive References</td>
                <td class="w-75" contenteditable="true"></td>
              </tr>
              <tr class="">
                <td class="py-5 bg-purple-dark text-center">Electronic Materials</td>
                <td class="w-75" contenteditable="true"></td>
              </tr>
              <tr class="">
                <td class="py-5 bg-purple-dark text-center">Other Learning Materials</td>
                <td class="w-75" contenteditable="true"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="margin-x-y-15">
      <div class="d-flex justify-content w-100">
      <button id="addRowLearningResourcesBtn">+ Row 's</button>
      </div>
    </div>
    <h2 class="my-15">2. Required Facilities and equipment</h2>
    <div class="mx-10">
      <div class="w-100">
        <table class="w-100" id="requiredFacilitiesEquipment">
          <thead>
            <tr class="bg-purple-dark py-5">
              <th class="text-center w-50">Items</th>
              <th class="text-center">Resources</th>
            </tr>
          </thead>
          <tbody>
            <tr class="">
              <td class="text-center">
                <div class="f-bold">facilities</div>
                <div>(Classrooms, laboratories, exhibition rooms, simulation rooms, etc.)</div>
              </td>
              <td class="text-center" contenteditable="true"></td>
            </tr>
            <tr class="">
              <td class="text-center">
                <div class="f-bold">Technology equipment</div>
                <div>(projector, smart board, software)</div>
              </td>
              <td class="text-center" contenteditable="true"></td>
            </tr>
            <tr class="">
              <td class="text-center">
                <div class="f-bold">Other equipment</div>
                <div>(depending on the nature of the specialty)</div>
              </td>
              <td class="text-center" contenteditable="true"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="margin-x-y-15">
      <div class="d-flex justify-content w-100">
      <button id="addRowRequiredFacilitiesBtn">+ Row 's</button>
      </div>
    </div>
  </section>
  <section id="sectionF">

    <h1 class="my-15">F. Assessment of Course Quality</h1>
    <div class="mx-10">
      <div class="w-100">
        <table class="w-100" id="assessmentCourseQuality">
          <thead>
            <tr class="bg-purple-dark">
              <th class="text-center py-5">Assessment Areas/Issues</th>
              <th class="text-center py-5">Assessor</th>
              <th class="text-center py-5">Assessment Methods</th>
            </tr>
          </thead>
          <tbody>
            <tr class="">
              <td class="py-5">Effectiveness of teaching</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr class="">
              <td class="py-5">Effectiveness of Students assessment</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr class="">
              <td class="py-5">Quality of learning resources</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr class="">
              <td>The extent to which CLOs have been achieved</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
          </tbody>
        </table>
        <p class="pl-0">
          <span style="color: #00b7c4">Assessors</span> (Students, Faculty, Program Leaders, Peer
          Reviewer, Others (specify) )
        </p>
        <p class="pl-0">
          <span style="color: #00b7c4">Assessors Methods </span> (Direct, Indirect)
        </p>
      </div>
    </div>
    <div class="margin-x-y-15">
      <div class="d-flex justify-content w-100">
      <button id="addRowCourseQualityBtn">+ Row 's</button>
      </div>
    </div>
  </section>
  <section id="sectionG">
    <h1 class="my-15">G. Specification Approval</h1>
    <div class="mx-10">
      <div class="w-100">
        <table class="w-100" id="specificationApproval">
          <tbody>
            <tr class="">
              <td class="py-5 bg-purple-dark text-center">COUNCIL /COMMITTEE</td>
              <td class="w-75 py-5" contenteditable="true"></td>
            </tr>
            <tr class="">
              <td class="py-5 bg-purple-dark text-center">REFERENCE NO.</td>
              <td class="w-75 py-5" contenteditable="true"></td>
            </tr>
            <tr class="">
              <td class="py-5 bg-purple-dark text-center">DATE</td>
              <td class="w-75 py-5" contenteditable="true"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="margin-x-y-15">
      <div class="d-flex justify-content w-100">
      <button id="addSpecificationApprovalBtn">+ Row 's</button>
      </div>
    </div>
  </section>
  <!-- <script>
    document.getElementById("addRowBtn").addEventListener("click", function() {
        var table = document.getElementById("teachingModeTable").getElementsByTagName('tbody')[0];
        var newRow = table.insertRow(table.rows.length);
        newRow.insertCell(0);
        newRow.insertCell(1);
        newRow.insertCell(2);
        newRow.insertCell(3);
    });
</script> -->
    <!-- <script src="./js/dataUploadAndUpdate.js"></script> -->
    <script src="./js/dataUploadAndRetrivie.js"></script>
    <!-- <script>console.log('his');</script> -->
  </body>
</html>