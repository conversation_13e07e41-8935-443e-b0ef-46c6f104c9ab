import React, { useEffect, useState } from 'react';

import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import { Button } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { List, Map, fromJS } from 'immutable';
import {
  getInstitutionCalendarList,
  postInstitutionCalendarList,
  setData,
} from '_reduxapi/q360/actions';
import { useDispatch } from 'react-redux';
import CreateNewCalendar from 'Modules/GlobalConfigurationV2/Models/CreateNewCalendar';
import { fullMonthNames } from 'Modules/GlobalConfigurationV2/utils/jsUtils';
import { useCheckEditAccess } from '.';

//----------------------------------UI Utils Start--------------------------------------------------
const iconSX = { height: '18px', width: '18px' };
const accordionSX = {
  '&::before': {
    display: 'none',
  },
};
const accordionSummarySX = {
  minHeight: '0px',
  '&.Mui-expanded': {
    minHeight: '0px',
  },
  '& .MuiAccordionSummary-content.Mui-expanded': {
    margin: '0px',
  },
  '& .MuiAccordionSummary-content': {
    margin: 0,
  },
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const getDaySuffix = (day) => {
  const suffixes = {
    1: 'st',
    2: 'nd',
    3: 'rd',
  };
  if (day >= 11 && day <= 13) {
    return 'th';
  }
  return suffixes[day % 10] || 'th';
};
const getAcademicYearStatus = (getDate, yearStatus) => {
  const dateString = new Date(getDate);
  const day = dateString.getDate();
  const monthIndex = dateString.getMonth();
  const fullNameMonth = fullMonthNames.getIn([monthIndex, 'fullName'], '');
  let fullMonthName;

  if (yearStatus === 'Upcoming') {
    fullMonthName = fullNameMonth;
  } else {
    fullMonthName = `${day}${getDaySuffix(day)} ${fullNameMonth}`;
  }
  return fullMonthName;
};
const filterList = (upcomingAcademicYear) => {
  let result = List();

  upcomingAcademicYear.forEach((academicYear) => {
    if (!academicYear.has('_id')) {
      result = result.push(academicYear);
    }

    if (academicYear.get('isDeleted', false)) {
      result = result.push(academicYear);
    }

    if (academicYear.has('isEdited')) {
      const modifiedYear = academicYear.delete('isEdited');
      result = result.push(modifiedYear);
    }
  });
  return fromJS({ upcomingInstitution: result });
};
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------
//----------------------------------custom hooks end------------------------------------------------
const useNewCalendarForm = () => {
  const initialOpen = {
    calendar_name: '',
    start_date: '',
    end_date: '',
  };
  const [formState, setFormState] = useState(Map());
  const openForm = (updateForm = null) => {
    const isUpdateForm = updateForm ? updateForm : initialOpen;
    setFormState(Map(isUpdateForm));
  };

  const closeForm = () => {
    setFormState(Map());
  };

  return {
    formState,
    openForm,
    closeForm,
  };
};
const useCalendarLists = () => {
  const dispatch = useDispatch();
  const [originalCalendarList, setOriginalCalendarList] = useState(Map());
  const [modifiedCalendarList, setModifiedCalendarList] = useState(Map());
  const upcomingAcademicYear = modifiedCalendarList.get('upcomingAcademicYear', List());
  const filteredCalendarList = filterList(upcomingAcademicYear);
  const onFetchSuccess = (data) => {
    setOriginalCalendarList(data);
    setModifiedCalendarList(data);
  };
  const fetchCalendarList = () => {
    dispatch(getInstitutionCalendarList(onFetchSuccess));
  };

  const handleCalendarCreation = (calendarData) => {
    let createCalendarData = modifiedCalendarList;
    createCalendarData = createCalendarData.update('upcomingAcademicYear', (list) =>
      list.unshift(calendarData)
    );
    setModifiedCalendarList(createCalendarData);
  };
  const handleCalendarDelete = (deleteCalendarData) => {
    setModifiedCalendarList(deleteCalendarData);
    dispatch(setData(fromJS({ message: 'Calender Deleted Successfully' })));
  };
  const handleCalendarEdit = (calendarData) => {
    const calenderIndex = calendarData.get('calenderIndex', 0);
    const removedIndexData = calendarData.delete('calenderIndex');
    let editCalendarData = modifiedCalendarList.setIn(
      ['upcomingAcademicYear', calenderIndex],
      removedIndexData
    );
    setModifiedCalendarList(editCalendarData);
  };
  const handleSaveCalendarList = () => {
    dispatch(postInstitutionCalendarList(filteredCalendarList, fetchCalendarList));
  };
  const handleCancelChanges = () => {
    setModifiedCalendarList(originalCalendarList);
  };
  useEffect(() => {
    fetchCalendarList();
  }, []);

  return {
    modifiedCalendarList,
    filteredCalendarList,
    handleSaveCalendarList,
    handleCalendarCreation,
    handleCalendarDelete,
    handleCancelChanges,
    handleCalendarEdit,
  };
};
//----------------------------------componentStart--------------------------------------------------
const RenderExpandIcon = ({ expanded }) => {
  return expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />;
};
const AddNewCalendarButton = ({ title, openForm }) => {
  if (title !== 'Upcoming') {
    return <></>;
  }
  return (
    <Button
      fullWidth
      className="text-capitalize btn-border-dotted mt-4 text-blue"
      onClick={() => openForm()}
    >
      <span className="f-14">+ Add New Calendar</span>
    </Button>
  );
};

const AcademicYearAccordion = ({ children }) => {
  const [expanded, setExpanded] = useState(false);

  const handleChange = () => {
    setExpanded((prev) => !prev);
  };

  const academicYearSummary = React.cloneElement(children[0], { expanded });

  return (
    <Accordion
      square
      elevation={0}
      disableGutters
      sx={accordionSX}
      expanded={expanded}
      onChange={handleChange}
      className="bg-transparent"
    >
      <AccordionSummary sx={accordionSummarySX} className="addSubTheme_bg px-1">
        {academicYearSummary}
      </AccordionSummary>
      <AccordionDetails className="p-0">{children[1]}</AccordionDetails>
    </Accordion>
  );
};
const AcademicYearSummary = ({ title, expanded, editAccess }) => {
  return (
    <div className="w-100 d-flex flex-wrap justify-content-between py-2 ">
      <div className="d-flex align-items-center gap-5 flex-grow-1">
        <RenderExpandIcon expanded={expanded} />
        <div className="bold f-14">{`${title} Academic Year`}</div>
      </div>
      {title === 'Upcoming' && editAccess && (
        <div className="ml-4 d-flex align-items-center gap-5 txt-sunshade-orange">
          <ErrorIcon className="f-18 txt-sunshade-orange" />
          <p className="m-0 f-14 mr-1">
            The Created Academic Calendar will Updated in Institution Calendar
          </p>
        </div>
      )}
    </div>
  );
};
const AcademicYearDetails = ({
  title,
  academicYearStatus,
  createCalendarForm,
  calendarListsHook,
  editAccess,
}) => {
  const { openForm } = createCalendarForm;
  const { modifiedCalendarList, handleCalendarDelete } = calendarListsHook;

  const handleDelete = (calendarIndex) => {
    const calendarId = academicYearStatus.getIn([calendarIndex, '_id'], '');
    if (calendarId) {
      let deleteCalendarData = modifiedCalendarList.setIn(
        ['upcomingAcademicYear', calendarIndex, 'isDeleted'],
        true
      );
      handleCalendarDelete(deleteCalendarData);
    } else {
      let deleteCalendarData = modifiedCalendarList.update('upcomingAcademicYear', (list) => {
        return list.delete(calendarIndex);
      });
      handleCalendarDelete(deleteCalendarData);
    }
  };

  const handleUpdate = (calendarIndex) => {
    const updateAcademicYear = academicYearStatus
      .get(calendarIndex, Map())
      .set('calenderIndex', calendarIndex);
    openForm(updateAcademicYear);
  };

  return (
    <div>
      {editAccess && <AddNewCalendarButton title={title} openForm={openForm} />}
      <ul className="list-group list-group-flush bg-white ta-header mt-4">
        {academicYearStatus.map((calendarItem, calendarIndex) => {
          const isDeleted = calendarItem.get('isDeleted', false);
          const end_date = calendarItem.get('end_date', '');
          const start_date = calendarItem.get('start_date', '');
          const calendar_name = calendarItem.get('calendar_name', '');
          const formatEndDate = getAcademicYearStatus(end_date, title);
          const formatStartDate = getAcademicYearStatus(start_date, title);
          if (isDeleted) {
            return <></>;
          }
          return (
            <li
              className="row m-0 list-group-item d-flex justify-content-between align-items-center border-top-0 border-bottom"
              key={calendarIndex}
            >
              <div className="col-sm-10 p-0 order-sm-0 order-1 mt-3 mt-sm-0">
                <div className="bold f-16">{`Academic Year ${calendar_name}`}</div>
                <p className="m-0 f-14">
                  Start : {formatStartDate} End : {formatEndDate}
                </p>
              </div>
              {title === 'Upcoming' && editAccess && (
                <div className="col-sm-2 d-flex justify-content-between justify-content-sm-end gap-20 align-items-center p-0">
                  <EditIcon
                    className="cursor-pointer"
                    sx={iconSX}
                    onClick={() => handleUpdate(calendarIndex)}
                  />
                  <DeleteIcon
                    className="txt-vivid-red cursor-pointer"
                    sx={iconSX}
                    onClick={() => handleDelete(calendarIndex)}
                  />
                </div>
              )}
            </li>
          );
        })}
      </ul>
    </div>
  );
};
//----------------------------------componentEnd----------------------------------------------------

const AcademicYear = () => {
  const createCalendarForm = useNewCalendarForm();
  const calendarListsHook = useCalendarLists();
  const handleSave = calendarListsHook.handleSaveCalendarList;
  const handleCancel = calendarListsHook.handleCancelChanges;
  const modifiedCalendarList = calendarListsHook.modifiedCalendarList;
  const filteredCalendarList = calendarListsHook.filteredCalendarList;

  const isCreateNewCalendar = createCalendarForm.formState.size !== 0;
  const isSaveDisabled = filteredCalendarList.get('upcomingInstitution', List()).size === 0;

  const upcomingAcademicYear = modifiedCalendarList.get('upcomingAcademicYear', List());
  const runningAcademicYear = modifiedCalendarList.get('runningAcademicYear', List());
  const editAccess = useCheckEditAccess('isEditAccessAcademicYear');

  return (
    <>
      {editAccess && (
        <div className="d-flex gap-15 cursor-pointer academicYear-btn-position mr-4">
          <Button variant="outlined" className="py-1 text-secondary border-secondary">
            <span className="px-2" onClick={() => handleCancel()}>
              Cancel
            </span>
          </Button>
          <Button
            variant="contained"
            className="p-0"
            onClick={handleSave}
            disabled={isSaveDisabled}
          >
            <span className="px-4">Save</span>
          </Button>
        </div>
      )}
      <div className="d-flex flex-column p-4">
        <AcademicYearAccordion>
          <AcademicYearSummary title="Upcoming" editAccess={editAccess} />
          <AcademicYearDetails
            title="Upcoming"
            editAccess={editAccess}
            calendarListsHook={calendarListsHook}
            createCalendarForm={createCalendarForm}
            academicYearStatus={upcomingAcademicYear}
          />
        </AcademicYearAccordion>

        <AcademicYearAccordion>
          <AcademicYearSummary title="Running" />
          <AcademicYearDetails
            title="Running"
            calendarListsHook={calendarListsHook}
            createCalendarForm={createCalendarForm}
            academicYearStatus={runningAcademicYear}
          />
        </AcademicYearAccordion>
      </div>
      {isCreateNewCalendar > 0 && (
        <CreateNewCalendar
          Open={isCreateNewCalendar}
          calendarListsHook={calendarListsHook}
          createCalendarForm={createCalendarForm}
        />
      )}
    </>
  );
};

export default AcademicYear;
