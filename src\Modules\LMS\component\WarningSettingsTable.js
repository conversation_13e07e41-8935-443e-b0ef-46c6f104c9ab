import React, { useState } from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { List, Map } from 'immutable';
// import Switch from '@mui/material/Switch';
import PropTypes from 'prop-types';
import Bin from '../../../Assets/bin.png';
import { useStylesFunction } from '../designUtils';
import MaterialInput from 'Widgets/FormElements/material/Input';
import HexColorPicker from './HexColorPicker';

import {
  StyledTableCell,
  StyledTableRow,
  IOSSwitch,
  warningName,
  iscomprehensiveMode,
  useIsWarningValue,
} from '../utils';

const LeaveCategoryTable = ({
  warningTypes,
  setWarningTypes,
  addTypes,
  deleteTypes,
  disableIndex,
  operation,
  setData,
  warningMode,
}) => {
  const {
    tableContainer,
    removeBorderInTableCell,
    tableCellAddingBorder,
    tableCellRemovingBackground,
    tableBody,
    tableCellBorderBottom,
  } = useStylesFunction();

  const [anchorEl, setAnchorEl] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(-1);

  const handleClick = (event, index) => {
    setAnchorEl(event.currentTarget);
    setCurrentIndex(index);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChangeType = (e, i, type) => {
    const value =
      type === 'isActive' ? e.target.checked : type === 'colorCode' ? e : e.target.value;
    if (type === 'percentage') {
      if (value > 100) setData(Map({ message: 'Percentage must be less than 100' }));
      const tempValue = value > 100 ? 100 : value;
      setWarningTypes(warningTypes.setIn([i, type], tempValue));
    } else if (type === 'warningValue') {
      setWarningTypes(warningTypes.setIn([i, type], value));
    } else {
      setWarningTypes(warningTypes.setIn([i, type], value));
    }
  };

  const disableItem = (i) =>
    operation === 'updateNew'
      ? operation === 'updateNew' && !warningTypes.getIn([i, 'edited'], false)
      : operation === 'update' && disableIndex + 1 !== i;

  const disableDelete = (i) =>
    operation === 'updateNew'
      ? operation === 'updateNew' && warningTypes.getIn([i, 'edited'], false)
      : operation !== 'update';

  const iscomprehensive = iscomprehensiveMode(warningMode);
  const isWarningValue = useIsWarningValue(iscomprehensive);
  return (
    <React.Fragment>
      <div className="digi-light-gray mb-1">Levels of Warning And Denial:</div>
      <TableContainer component={Paper} className={tableContainer}>
        <Table sx={{ minWidth: 650 }} aria-label="caption table">
          <TableHead>
            <TableRow>
              <StyledTableCell>Type</StyledTableCell>
              <StyledTableCell>Label</StyledTableCell>
              <StyledTableCell>{`${
                isWarningValue ? 'No of Sessions' : 'Set Percentage'
              } `}</StyledTableCell>
              <StyledTableCell>Color</StyledTableCell>
              <StyledTableCell>Status</StyledTableCell>
              <StyledTableCell className={removeBorderInTableCell}>&nbsp;</StyledTableCell>
              <StyledTableCell className={removeBorderInTableCell}>&nbsp;</StyledTableCell>
            </TableRow>
          </TableHead>
          <TableBody className={tableBody}>
            {warningTypes.map((type, i) => {
              return (
                <>
                  {i !== 0 && (
                    <StyledTableRow key={i}>
                      <TableCell className={tableCellBorderBottom}>
                        {warningName(warningTypes, i)}
                      </TableCell>
                      <TableCell className={tableCellBorderBottom}>
                        <MaterialInput
                          elementType={'materialInput'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          value={type.get('labelName', '')}
                          changed={(e) => handleChangeType(e, i, 'labelName')}
                          disabled={disableItem(i)}
                        />
                      </TableCell>
                      <TableCell className={tableCellBorderBottom}>
                        <div className="d-flex align-items-center">
                          <div className="mr-1">
                            <MaterialInput
                              elementType={'materialInput'}
                              type={'number'}
                              variant={'outlined'}
                              size={'small'}
                              value={type.get(iscomprehensive, '')}
                              changed={(e) => handleChangeType(e, i, iscomprehensive)}
                              inputAdornment={iscomprehensive === 'percentage' ? '%' : ''}
                              disabled={disableItem(i)}
                            />
                          </div>
                        </div>
                      </TableCell>

                      <TableCell
                        className={` ${tableCellBorderBottom} ${
                          disableItem(i) && 'digi-pointer-events'
                        }`}
                      >
                        <div
                          className="color-swatch"
                          style={{ backgroundColor: type.get('colorCode', '') }}
                          onClick={(e) => handleClick(e, i)}
                        />
                        {currentIndex === i && (
                          <HexColorPicker
                            anchorEl={anchorEl}
                            onClose={handleClose}
                            color={type.get('colorCode', '')}
                            handleChange={handleChangeType}
                            index={i}
                            type={'colorCode'}
                          />
                        )}
                      </TableCell>
                      <TableCell className={`${tableCellAddingBorder} ${tableCellBorderBottom}`}>
                        <IOSSwitch
                          checked={type.get('isActive', false)}
                          onChange={(e) => handleChangeType(e, i, 'isActive')}
                          inputProps={{ 'aria-label': 'controlled' }}
                          disabled={disableItem(i)}
                        />
                      </TableCell>
                      {disableDelete(i) && i !== 1 && warningTypes.size - 1 !== i && (
                        <TableCell
                          align="center"
                          className={`${removeBorderInTableCell} ${tableCellRemovingBackground} px-2 py-3 digi-cursor-pointer`}
                        >
                          <img
                            src={Bin}
                            onClick={() => {
                              deleteTypes(i, type.get('_id', '') !== '' ? type.get('_id', '') : '');
                            }}
                            alt="Bin"
                          />
                        </TableCell>
                      )}
                      {operation !== 'update' &&
                        warningTypes.size - 2 === i &&
                        warningTypes.size < 12 && (
                          <TableCell
                            align="center"
                            className={`${removeBorderInTableCell} ${tableCellRemovingBackground} px-2 py-3 digi-cursor-pointer`}
                          >
                            <AddCircleOutlineIcon
                              className="mt-2"
                              sx={{ color: '#4194E4', fontSize: 24 }}
                              onClick={() => addTypes(i)}
                            />
                          </TableCell>
                        )}
                    </StyledTableRow>
                  )}
                </>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </React.Fragment>
  );
};

LeaveCategoryTable.propTypes = {
  warningTypes: PropTypes.instanceOf(List),
  setWarningTypes: PropTypes.func,
  addTypes: PropTypes.func,
  deleteTypes: PropTypes.func,
  disableIndex: PropTypes.number,
  operation: PropTypes.string,
  setData: PropTypes.func,
  warningMode: PropTypes.string,
};

export default LeaveCategoryTable;
