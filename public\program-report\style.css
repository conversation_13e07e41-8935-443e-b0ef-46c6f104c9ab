* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #F2F2F2;
  margin: 0 auto;
  padding: 20px;
}

h1, h2, h3 {
  color: #4C3D8E;
}

h3 {
  color: #52B5C2;
}

.header {
  text-align: center;
}

ul {
  list-style-type: disc;
  margin-left: 40px;
  padding: 0;
}

li {
  margin-bottom: 5px;
}

table {
  width: 100%;
  border-collapse: collapse;
}
table tbody tr td , table thead tr th{
  border: 1.5px solid white;
}

#program-statistics table thead tr th,#program-assessment table:nth-child(1) thead tr th{
text-align: center;
}

#program-assessment table tbody th {
  background-color:#52B5C2 ;
}

table tbody tr td:nth-child(even){
  background-color: #D9D9D9;
}

th, td {
  border: 1px solid #ddd;
  height: 40px;
  min-width: 100px;
  padding: 0 5px;
  text-align: left;
}

th {
  background-color: #4C3D8E;
  color: white;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

tr:hover {
  background-color: #e8f5e9;
}

.header-row {
  background-color: #778899;
  color: white;
}

.sub-header-row {
  background-color: #DCDCDC;
  color: black;
}

.text-center {
  text-align: center;
}

.discussion {
  font-weight: bold;
}

#index{
  padding:0  0 150px 0;
}
#index h3,#index ol li{
  color: #0561C1;
  font-weight: 550;
}
.d-flex{
  display: flex;
  justify-content: space-between;
}
.text-black{
  color: black;
  font-weight: normal;
}
.report-label {
  color: #3292cd;
  font-size: 16px;
  font-weight: 500;
  padding: 10px;
  box-sizing: border-box;
}

.tbl-border-bottom {
  border-bottom: 2px solid #B6B0CD;
  display: flex;
}

.tbl-border {
  border: 2px solid #B6B0CD;
  margin: 10px;
  background-color: #f2f2f2;
}

.form-full {
  width: 100%;
  clear: both;
  padding: 5px;
  box-sizing: border-box;
  display: flex;
}
.report-label {
  color: #3292cd;
  font-size: 16px;
  font-weight: 500;
  /* padding: 15px; */
  box-sizing: border-box;
}
.report-input-content {
  font-size: 16px;
  padding: 15px;
  outline: none !important;
  background: #f2f2f2;
  flex: 1;
}

.text-secondary{
  background-color: #D9D9D9;
}
.front-page ul li{
  color: #52B5C2;
  border-bottom: 2.5px dotted #52B5C2;
}
.text-underline{
  text-decoration: underline #0561C1;
}

.custom-scrollbar{
  height: 100vh;
  overflow-y: scroll;
}
.custom-scrollbar::-webkit-scrollbar {
  height: 7px !important;
  width: 7px !important;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cfcfcf !important;
  border-radius: 10px !important;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9e9e9e !important;
}