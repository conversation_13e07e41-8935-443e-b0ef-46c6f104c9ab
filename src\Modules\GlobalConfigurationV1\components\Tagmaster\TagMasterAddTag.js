import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';

import DialogModal from 'Widgets/FormElements/material/DialogModal';

const TagMasterAddTag = ({
  handleClickTagClose,
  getSelectorValue,
  handleCheckbox,
  addTag,
  dataAr,
  groupName = Map(),
}) => {
  const addTagShow = addTag.get('show', false);
  const tagsList = getSelectorValue.getIn([...dataAr, 'tags'], List());
  return (
    <DialogModal show={addTagShow} onClose={handleClickTagClose} maxWidth={'xs'} fullWidth={true}>
      <h4 className="m-3">Select Tag</h4>
      <div className="scroll-overflow-modal">
        {getSelectorValue.get('tags', List()).map((tagName, index) => (
          <div key={index} className="d-flex m-3">
            <div>
              <input
                type="checkbox"
                checked={tagsList
                  .map((tagsListItem) => tagsListItem.get('_id'))
                  .includes(tagName.get('_id', ''))}
                onChange={() => handleCheckbox(tagName, dataAr, groupName)}
              />
            </div>
            <div className="tagName-font-size">
              <div className="ml-2 bold">
                {tagName.get('code', '')} - {tagName.get('name', '')}
              </div>
              <div className="ml-2 text-muted">{tagName.get('description', '')}</div>
            </div>
          </div>
        ))}
      </div>
    </DialogModal>
  );
};

export default TagMasterAddTag;

TagMasterAddTag.propTypes = {
  handleClickTagClose: PropTypes.func,
  getSelectorValue: PropTypes.array,
  handleCheckbox: PropTypes.func,
  addTag: PropTypes.func,
  dataAr: PropTypes.object,
  groupName: PropTypes.object,
};
