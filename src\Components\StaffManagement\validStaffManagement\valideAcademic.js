import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import Loader from '../../../Widgets/Loader/Loader';
import Input from '../../../Widgets/FormElements/Input/Input';
import ProfileText from '../../../Widgets/ProfileText';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import axios from '../../../axios';
import { Trans } from 'react-i18next';
import { Button } from 'react-bootstrap';
import { FlexWrapper, NavButton } from '../../../_components/Styled/styled';
//import { STAFF_TYPE,  } from '../../../constants';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getLang } from 'utils';
import { t } from 'i18next';
import { STAFF_TYPE, ACADEMIC_DESIGNATION } from 'Components/utils';
import { selectAllProgramLists, selectUserInfo } from '_reduxapi/Common/Selectors';
import { Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Checkbox,
  OutlinedInput,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Popover,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import FilterListIcon from '@mui/icons-material/FilterList';
import IconButton from '@mui/material/IconButton';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Badge from '@mui/material/Badge';

let id;
class ValideAcademic extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      showAux: false,
      checkboxHide: false,
      primarySub: false,
      defaultDepartmentList: [],
      programList: [],
      departmentList: [],
      divisionList: [],
      subjectList: [],
      courseList: [],
      originalCourseList: [], // Keep all courses for ID mapping
      isProgramSubjectAccordionOpen: false,
      isProgramCourseAccordionOpen: false,

      selectedProgram: '',
      selectedDepartment: '',
      selectedDivision: '',
      selectedSubject: '',
      addSubject: [],
      subjectAddName: '',
      auxValied: false,

      departmentList1: [],
      divisionList1: [],
      subjectList1: [],

      selectedProgram1: '',
      selectedDepartment1: '',
      selectedDivision1: '',
      selectedSubject1: '',

      addSubject1: [],
      addMoreAuxiallary: [],
      addAuxiallary: [],
      subjectAddName1: '',
      auxProgramName: '',
      auxDepartmentName: '',
      auxDivisionName: '',
      selectedProgramId: '',
      selectedCourseIds: [],
      selectedCourses: [],
      selectedProgramIdAux: '',
      employementDisable: true,
      primaryDataAr: [],
      auxiliaryDataAr: [],
      academic_check: false,
      employment_check: false,
      studyDivisionVisible: true,
      tabs: 0,
      user_type: null,
      institution_role: null,
      staffType: STAFF_TYPE.map((item) => ({
        ...item,
        name: t(`user_management.${item.name}`),
      })),
      userStatus: '',
      programError: '',
      selectedProgramCourse: '',
      selectedProgramIdCourse: '',
      selectedProgramCourses: [],
      columnFilters: {
        programName: {
          open: false,
          anchorEl: null,
          searchText: '',
          selectedValues: [],
        },
        courseName: {
          open: false,
          anchorEl: null,
          searchText: '',
          selectedValues: [],
        },
      },
      activeFilterColumn: null,
      filterAnchorEl: null,
      selectedTableRows: [],
      selectAllTableRows: false,
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');
    let tab = params.get('tab');
    this.setState({ tabs: tab });
    this.fetchApi();
  }

  getSubjectName = (data, id) => {
    let subjectName = '';
    if (data && data.length > 0) {
      subjectName =
        data && data.filter((item) => item._id === id).reduce((_, el) => el.subject_name, '');
    }
    return subjectName;
  };

  callProgram = () => {
    this.interval = setInterval(() => {
      const { publishedProgramLists } = this.props;
      if (publishedProgramLists.length > 0) {
        const formattedArray = publishedProgramLists.map((item) => {
          return { name: item.name, value: item.id, id: item.id };
        });
        formattedArray.unshift({
          name: '',
          value: '',
          id: '',
        });
        this.setState({
          programList: formattedArray,
          program: formattedArray[0].value,
        });
        this.editLoad();
        clearInterval(this.interval);
      }
    }, 200);
  };

  // Function to group courses by courseCode and remove duplicates for UI
  groupCoursesByCode = (courses) => {
    const grouped = courses.reduce((acc, course) => {
      const { courseCode } = course;

      if (!courseCode) return acc; // Skip if no courseCode

      if (!acc[courseCode]) {
        acc[courseCode] = {
          // Display data (use first course for UI display)
          displayCourse: {
            name: `${course.courseName} - ${courseCode}`,
            value: course.courseId,
            id: course.courseId,
            code: courseCode,
            courseName: course.courseName,
            originalCourse: course,
          },
          // All IDs for this courseCode (for API submission)
          allIds: [course.courseId],
          // All course objects for this courseCode
          allCourses: [course],
          // Count of versions
          versionCount: 1,
        };
      } else {
        // Add to existing group
        acc[courseCode].allIds.push(course.courseId);
        acc[courseCode].allCourses.push(course);
        acc[courseCode].versionCount++;

        // Keep the same display name (no version count shown)
        // Display name remains: "Finance - Acct 652"
      }

      return acc;
    }, {});

    // Convert to array and return display courses
    return Object.values(grouped).map((group) => group.displayCourse);
  };

  // Function to get all IDs for a given courseCode
  getAllIdsForCourseCode = (courseCode) => {
    if (!this.state.originalCourseList) return [];

    return this.state.originalCourseList
      .filter((course) => course.code === courseCode)
      .map((course) => course.value);
  };

  callCourse = (courseList, shouldCallEditLoad = true) => {
    if (courseList.length > 0) {
      // Create original formatted array (keep all courses for ID mapping)
      const originalFormattedArray = courseList.map((item) => {
        return {
          name: item.courseName,
          value: item.courseId,
          id: item.courseId,
          code: item.courseCode,
          courseName: item.courseName,
          originalData: item,
        };
      });

      // Create grouped array for UI display (remove duplicates by courseCode)
      const groupedCourses = this.groupCoursesByCode(courseList);

      this.setState({
        courseList: groupedCourses, // For UI display (deduplicated)
        originalCourseList: originalFormattedArray, // For ID mapping (all courses)
        course: groupedCourses[0] ? groupedCourses[0].value : '',
      });

      if (shouldCallEditLoad) {
        this.editLoad();
      }
    } else {
      this.setState({
        courseList: [],
        originalCourseList: [],
      });
    }
  };

  fetchApi = () => {
    this.setState({ isLoading: true });
    axios
      .get(`/user/staff/${id}`)
      .then((res) => {
        const data = res.data.data;
        this.setState({
          userStatus: data.status,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
    this.callProgram();
  };

  fetchCourseApi = () => {
    const programId = this.state.selectedProgramId;
    this.setState({ isLoading: true });
    axios
      .get(`/user/getProgramBasedCourseList?programId=${programId}`)
      .then((res) => {
        const data = res.data.data;
        this.callCourse(data, false); // Don't call editLoad to preserve existing table data
        this.setState({
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  editLoad = () => {
    axios
      .get(`user/staff_profile_details/${id}`)
      .then((res) => {
        let academic_allocation = res.data.data.academic_allocation;
        let academicCourses = res.data.data.academicCourses || [];

        // Transform academicCourses data into selectedProgramCourses format
        const transformedCourses = academicCourses
          .map((courseGroup) => {
            const programId = courseGroup.programId._id;
            const programName = courseGroup.programId.name;

            return courseGroup.courseIds.map((course) => ({
              programId: programId,
              programName: programName,
              courseId: course._id,
              courseName: course.course_name,
            }));
          })
          .flat();

        let primaryData =
          academic_allocation &&
          academic_allocation.length > 0 &&
          academic_allocation.filter((primary) => {
            return primary.allocation_type === 'primary';
          });

        let auxiliaryData =
          academic_allocation &&
          academic_allocation.length > 0 &&
          academic_allocation.filter((primary) => {
            return primary.allocation_type === 'auxiliary';
          });

        let selected_data =
          primaryData &&
          primaryData.length > 0 &&
          this.state.programList.length > 0 &&
          this.state.programList
            .filter((item) => item.id === primaryData[0]._program_id._id)
            .reduce((_, el) => {
              return el.value;
            }, '');

        let auxiliaryDataArray =
          auxiliaryData &&
          auxiliaryData.length > 0 &&
          auxiliaryData.map((item) => {
            return {
              allocation_type: 'auxiliary',
              auxDepartmentName:
                item._department_id !== undefined &&
                item._department_id.department_name !== undefined
                  ? item._department_id.department_name
                  : '',
              _department_id:
                item._department_id !== undefined && item._department_id._id !== undefined
                  ? item._department_id._id
                  : '',
              auxProgramName:
                item._program_id !== undefined && item._program_id.name !== undefined
                  ? item._program_id.name
                  : '',
              _program_id:
                item._program_id !== undefined && item._program_id._id !== undefined
                  ? item._program_id._id
                  : '',
              _department_division_id:
                item._department_division_id !== undefined &&
                item._department_division_id._id !== undefined
                  ? item._department_division_id._id
                  : '',
              auxDivisionName:
                item._department_division_id !== undefined &&
                item._department_division_id.title !== undefined
                  ? item._department_division_id.title
                  : '',
              subjectName:
                item._department_subject_id !== undefined &&
                item._department_subject_id.length > 0 &&
                item._department_subject_id.map((subject) =>
                  this.getSubjectName(item._department_id.subject, subject)
                ),
              _department_subject_id:
                item._department_subject_id !== undefined &&
                item._department_subject_id.length > 0 &&
                item._department_subject_id.map((subject) => subject),
            };
          });

        let staffType = this.state.staffType;

        if (res.data.data !== undefined && res.data.data.staff_employment_type !== '') {
          staffType = this.state.staffType.map((item) => {
            let isCheck = false;
            if (res.data.data.staff_employment_type === item.value) {
              isCheck = true;
            } else if (res.data.data.staff_employment_type === 'both') {
              isCheck = true;
            }
            return { ...item, isChecked: isCheck };
          });
        }

        this.setState(
          {
            isLoading: false,
            primaryDataAr: primaryData,
            addMoreAuxiallary:
              auxiliaryDataArray && auxiliaryDataArray.length > 0 ? auxiliaryDataArray : [],
            selectedProgram: selected_data !== false ? selected_data : '',
            selectedProgramId:
              primaryData && primaryData.length > 0 ? primaryData[0]._program_id._id : '',
            selectedDepartment:
              primaryData && primaryData.length > 0 ? primaryData[0]._department_id._id : '',
            selectedSubject:
              primaryData && primaryData.length > 0
                ? primaryData[0]._department_subject_id[0]._id
                : '',
            selectedDivision:
              primaryData &&
              primaryData.length > 0 &&
              primaryData[0]._department_division_id !== undefined
                ? primaryData[0]._department_division_id._id
                : '',
            academic_check: res.data.data.academic_check,
            employment_check:
              res.data.data.employment_check ||
              (primaryData && primaryData.length > 0) || // Enable if program & subject data exists
              (transformedCourses && transformedCourses.length > 0), // Enable if program & course data exists
            primarySub: primaryData && primaryData.length > 0,
            user_type:
              res.data.data.staff_employment_type !== undefined
                ? res.data.data.staff_employment_type
                : null,
            institution_role:
              res.data.data.institution_role !== undefined ? res.data.data.institution_role : null,
            staffType: staffType,
            selectedProgramCourses: transformedCourses,
          },
          () => {
            if (selected_data !== false && selected_data !== '') {
              axios
                .get(`/digi_department_subject/${selected_data}?limit=100&pageNo=1`)
                .then((res) => {
                  const departmentList =
                    res.data.data &&
                    res.data.data.length > 0 &&
                    res.data.data.map((data) => {
                      return {
                        name: data.department_name,
                        value: data._id,
                      };
                    });
                  departmentList.unshift({
                    name: '',
                    value: '',
                  });

                  const subjectList =
                    res.data.data &&
                    res.data.data.length > 0 &&
                    res.data.data
                      .filter((item) => item._id === primaryData[0]._department_id._id)
                      .reduce((_, el) => el.subject, [])
                      .filter((item) => item.isActive === true && item.isDeleted === false)
                      .map((sub) => {
                        return {
                          name: sub.subject_name,
                          value: sub._id,
                        };
                      });
                  subjectList.unshift({
                    name: '',
                    value: '',
                  });

                  let addSubject = [];
                  primaryData[0]._department_subject_id &&
                    primaryData[0]._department_subject_id.length > 0 &&
                    primaryData[0]._department_subject_id.forEach((item) => {
                      addSubject.push({
                        subjectView: item,
                        subjectName: this.getSubjectName(
                          primaryData[0]._department_id?.subject,
                          item
                        ),
                      });
                    });
                  this.setState({
                    departmentList: departmentList,
                    defaultDepartmentList:
                      res.data.data && res.data.data.length > 0 ? res.data.data : [],
                    depart: departmentList[0].value,
                    isLoading: false,
                    enable: true,
                    divisionList: [],
                    subjectList: subjectList,
                    addSubject,
                    selectedSubject: '',
                  });
                })
                .catch((error) => {
                  this.setState({
                    isLoading: false,
                  });
                });
            }
          }
        );
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
          primaryDataAr: [],
          addMoreAuxiallary: [],
        });
      });
  };

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  handleSelect = (e, name) => {
    e.preventDefault();

    if (name === 'institution_role') {
      this.setState({ institution_role: e.target.value });
    } else if (name === 'program') {
      let selectedProgramId;
      for (let i = 0; i < this.state.programList.length; i++) {
        if (this.state.programList[i].value === e.target.value) {
          selectedProgramId = this.state.programList[i].id;
        }
      }
      this.setState({
        selectedProgram: e.target.value,
        selectedProgramId: selectedProgramId,
        programError: '',
        isLoading: true,
      });

      axios
        .get(`/digi_department_subject/${e.target.value}?limit=100&pageNo=1`)
        .then((res) => {
          const departmentList = res.data.data.map((data) => {
            return {
              name: data.department_name,
              value: data._id,
            };
          });
          departmentList.unshift({
            name: '',
            value: '',
          });
          this.setState({
            departmentList: departmentList,
            defaultDepartmentList: res.data.data && res.data.data.length > 0 ? res.data.data : [],
            depart: departmentList[0].value,
            isLoading: false,
            addSubject: [],
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'department') {
      this.setState({
        selectedDepartment: e.target.value,
        departmentError: '',
        isLoading: true,
      });

      let subjectList = [];
      let selectedSubject = '';
      if (e.target.value !== '') {
        subjectList =
          this.state.defaultDepartmentList &&
          this.state.defaultDepartmentList.length > 0 &&
          this.state.defaultDepartmentList
            .filter((item) => item._id === e.target.value)
            .reduce((_, el) => el.subject, [])
            .filter((item) => item.isActive === true && item.isDeleted === false)
            .map((sub) => {
              return {
                name: sub.subject_name,
                value: sub._id,
              };
            });
        subjectList.unshift({
          name: '',
          value: '',
        });
        selectedSubject = subjectList[0].value;
      }

      this.setState({
        subjectList: subjectList,
        selectedSubject: selectedSubject,
        isLoading: false,
        addSubject: [],
      });
    } else if (name === 'division') {
      this.setState({
        selectedDivision: e.target.value,
        isLoading: true,
      });
      axios
        .get(`/department_division/subject/${e.target.value}`)
        .then((res) => {
          const subjectList = res.data.data[0].subject.map((sub) => {
            return {
              name: sub.title,
              value: sub._id,
            };
          });

          subjectList.unshift({
            name: '',
            value: '',
          });
          this.setState({
            subjectList: subjectList,
            selectedSubject: subjectList[0].value,
            isLoading: false,
            addSubject: [],
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'subject') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedSubject: e.target.value,
        subjectAddName: label,
        subjectListError: '',
      });
    } else if (name === 'programCourse') {
      let selectedProgramIdCourse;
      for (let i = 0; i < this.state.programList.length; i++) {
        if (this.state.programList[i].value === e.target.value) {
          selectedProgramIdCourse = this.state.programList[i].id;
        }
      }
      this.setState({
        selectedProgramCourse: e.target.value,
        selectedProgramIdCourse: selectedProgramIdCourse,
        programError: '',
        isLoading: true,
      });

      const programId = selectedProgramIdCourse;
      axios
        .get(`/user/getProgramBasedCourseList?programId=${programId}`)
        .then((res) => {
          const data = res.data.data;
          this.callCourse(data, false); // Don't call editLoad to preserve existing table data
          this.setState({
            isLoading: false,
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'course') {
      this.setState({
        selectedCourseId: e.target.value,
      });
    }
  };

  handleAccordionChange = () => {
    this.fetchCourseApi();
  };

  validation = (e, name) => {
    let programError = '';
    let departmentError = '';
    if (this.state.selectedProgram === '') {
      programError = t('user_management.program_field_required');
    }

    if (this.state.selectedDepartment === '') {
      departmentError = t('user_management.department_field_required');
    }
    // if (this.state.selectedSubject === "") {
    //   subjectListError = "Subject Field Is required";
    // }

    if (
      programError ||
      departmentError
      // subjectListError
    ) {
      this.setState({
        programError,
        departmentError,
        // subjectListError,
      });

      return false;
    }
    return true;
  };

  subjectValidation = (e, name) => {
    let subjectListError = '';

    if (this.state.selectedSubject === '') {
      subjectListError = 'Subject Field Is required';
    }

    if (subjectListError) {
      this.setState({
        subjectListError,
      });

      return false;
    }
    return true;
  };

  handleAddSubject = (e) => {
    e.preventDefault();
    if (!this.subjectValidation()) return true;
    let addSubject = this.state.addSubject;

    if (!(addSubject.filter((e) => e.subjectName === this.state.subjectAddName).length > 0)) {
      addSubject.push({
        subjectView: this.state.selectedSubject,
        subjectName: this.state.subjectAddName,
      });
    } else {
      NotificationManager.error(t('user_management.Subject_already_added'));
    }

    this.setState({
      addSubject,
      subjectName: '',
      primarySub: true,
      selectedSubject: this.state.subjectList[0].value,
      employment_check: true, // Enable employment tab when subjects are added
    });
  };

  subjectValidation1 = (e, name) => {
    let subjectList1Error = '';

    if (this.state.selectedSubject1 === '') {
      subjectList1Error = 'Subject Field Is required';
    }

    if (subjectList1Error) {
      this.setState({
        subjectList1Error,
      });

      return false;
    }
    return true;
  };

  handleAuxAddSubject = (e) => {
    e.preventDefault();
    if (!this.subjectValidation1()) return true;
    let addSubject1 = this.state.addSubject1;

    if (!(addSubject1.filter((e) => e.auxSubjectName === this.state.subjectAddName1).length > 0)) {
      addSubject1.push({
        auxSubjectView: this.state.selectedSubject1,
        auxSubjectName: this.state.subjectAddName1,
      });
    } else {
      NotificationManager.error(t('user_management.Subject_already_added'));
    }

    this.setState({
      addSubject1,
      // selectedSubject1:'',
      // subjectAddName1:''
      selectedSubject1: this.state.subjectList1[0].value,
    });
  };

  handleAuxSelect = (e, name) => {
    e.preventDefault();
    if (name === 'program1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;

      let selectedProgramIdAux;
      for (let i = 0; i < this.state.programList.length; i++) {
        if (this.state.programList[i].value === e.target.value) {
          selectedProgramIdAux = this.state.programList[i].id;
        }
      }
      this.setState({
        selectedProgram1: e.target.value,
        selectedProgramIdAux: selectedProgramIdAux,
        program1Error: '',
        auxProgramName: label,
        isLoading: true,
      });

      axios
        .get(`/digi_department_subject/${e.target.value}?limit=100&pageNo=1`)
        .then((res) => {
          const departmentList1 = res.data.data.map((data) => {
            return {
              name: data.department_name,
              value: data._id,
              subject: data.subject,
            };
          });
          departmentList1.unshift({
            name: '',
            value: '',
          });
          this.setState({
            departmentList1: departmentList1,
            depart1: departmentList1[0].value,
            addSubject1: [],
            isLoading: false,
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'department1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedDepartment1: e.target.value,
        department1Error: '',
        auxDepartmentName: label,
        isLoading: true,
      });

      let subjectList1 = [];
      let selectedSubject1 = '';
      if (e.target.value !== '') {
        subjectList1 =
          this.state.departmentList1 &&
          this.state.departmentList1.length > 0 &&
          this.state.departmentList1
            .filter((item) => item.value === e.target.value)
            .reduce((_, el) => el.subject, [])
            .filter((item) => item.isActive === true && item.isDeleted === false)
            .map((sub) => {
              return {
                name: sub.subject_name,
                value: sub._id,
              };
            });
        subjectList1.unshift({
          name: '',
          value: '',
        });
        selectedSubject1 = subjectList1[0].value;
      }

      this.setState({
        subjectList1: subjectList1,
        selectedSubject1: selectedSubject1,
        isLoading: false,
        addSubject1: [],
      });
    } else if (name === 'division1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedDivision1: e.target.value,
        auxDivisionName: label,
        isLoading: true,
      });
      axios
        .get(`/department_division/subject/${e.target.value}`)
        .then((res) => {
          const subjectList1 = res.data.data[0].subject.map((sub) => {
            return {
              name: sub.title,
              value: sub._id,
            };
          });

          subjectList1.unshift({
            name: '',
            value: '',
          });

          this.setState({
            subjectList1: subjectList1,
            selectedSubject1: subjectList1[0].value,
            isLoading: false,
            addSubject1: [],
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'subject1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedSubject1: e.target.value,
        subjectAddName1: label,
        subjectList1Error: '',
      });
    }
  };

  handleCheckStaffType = (event, index) => {
    let data = this.state.staffType;
    data[index].isChecked = event.target.checked;
    this.setState({ staffType: data });
  };

  getAuxSubject = () => {
    const { subjectList1, addSubject } = this.state;

    let addSubjectIds = [];
    if (addSubject && addSubject.length > 0) {
      addSubjectIds = addSubject.map((item) => item.subjectView);
    }
    const value =
      subjectList1 &&
      subjectList1.length > 0 &&
      subjectList1.filter((item) => !addSubjectIds.includes(item.value));
    return value && value.length > 0 ? value : subjectList1;
  };

  handleAuxFullSubmit = (e) => {
    // if (!this.auxValidation()) return true;
    const { auxProgramName, auxDepartmentName, auxDivisionName, subjectAddName1 } = this.state;
    let addMoreAuxiallary = this.state.addMoreAuxiallary;

    let mappingSubject = this.state.addSubject1.map((su) => {
      return su.auxSubjectView;
    });

    let mappingSubjectName = this.state.addSubject1.map((sus) => {
      return sus.auxSubjectName;
    });
    if (
      !(
        addMoreAuxiallary.filter(
          (e) =>
            e.auxProgramName === auxProgramName &&
            e.auxDepartmentName === auxDepartmentName &&
            e.auxDivisionName === auxDivisionName &&
            e.subjectName.includes(subjectAddName1)
        ).length > 0
      )
      // &&
      // !(addMoreAuxiallary.filter((e) => e.auxSubjectName === subjectAddName1).length > 0)
    ) {
      addMoreAuxiallary.push({
        allocation_type: 'auxiliary',
        _program_id: this.state.selectedProgramIdAux,
        auxProgramName: auxProgramName,

        _department_id: this.state.selectedDepartment1,
        auxDepartmentName: auxDepartmentName,

        _department_division_id: this.state.selectedDivision1,
        auxDivisionName: auxDivisionName,

        subjectName: mappingSubjectName,
        _department_subject_id: mappingSubject,
      });
    } else {
      NotificationManager.error(t('user_management.Subject_already_added'));
    }

    this.setState({
      addMoreAuxiallary,
      // addAuxiallary:addMoreAuxiallary,
      addSubject1: [],
      selectedProgram1: '',
      auxProgramName: '',
      selectedDepartment1: '',
      auxDepartmentName: '',
      selectedDivision1: '',
      auxDivisionName: '',
      mappingSubjectName: '',
      subject: '',
      selectedSubject1: '',
      subjectAddName1: '',
      auxValied: false,
      // checkboxHide: true,
    });
  };

  auxValidation = (e, name) => {
    let program1Error = '';
    let department1Error = '';
    let subjectList1Error = '';

    if (this.state.selectedProgram1 === '') {
      program1Error = t('user_management.program_field_required');
    }

    if (this.state.selectedDepartment1 === '') {
      department1Error = t('user_management.department_field_required');
    }
    if (this.state.selectedSubject1 === '') {
      subjectList1Error = t('user_management.subject_field_required');
    }

    if (program1Error || department1Error || subjectList1Error) {
      this.setState({
        program1Error,
        department1Error,
        subjectList1Error,
      });

      return false;
    }
    return true;
  };

  handleSubmit = (e, isAcademicEnabled, isAdministrativeEnabled) => {
    e.preventDefault();
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');

    // Check if we have either Program & Subject data OR Program & Course data
    const hasSubjectData = this.state.addSubject && this.state.addSubject.length > 0;
    const hasCourseData =
      this.state.selectedProgramCourses && this.state.selectedProgramCourses.length > 0;
    const hasValidSubjectData = hasSubjectData && this.validation();

    // For academic staff, we need either valid subject data OR course data
    // For administrative staff, no validation needed
    const canProceed =
      isAdministrativeEnabled || (isAcademicEnabled && (hasValidSubjectData || hasCourseData));

    if (canProceed) {
      let mappingValue =
        hasSubjectData &&
        this.state.addSubject.map((dd) => {
          return dd.subjectView;
        });

      this.setState({
        isLoading: true,
      });

      // Group academicCourses by programId
      const coursesByProgram = this.state.selectedProgramCourses.reduce((acc, row) => {
        if (!acc[row.programId]) {
          acc[row.programId] = {
            programId: row.programId,
            courseIds: [],
          };
        }
        acc[row.programId].courseIds.push(row.courseId);
        return acc;
      }, {});

      // Convert to array format
      const coursesPayload = Object.values(coursesByProgram);

      let primaryType = {
        allocation_type: 'primary',
      };

      // Only add subject-related fields if we have subject data
      if (hasSubjectData) {
        primaryType._program_id = this.state.selectedProgramId;
        primaryType._department_id = this.state.selectedDepartment;
        primaryType._department_division_id = this.state.selectedDivision;
        primaryType._department_subject_id = mappingValue;
      }

      // Add academicCourses data if academicCourses are selected
      if (coursesPayload.length > 0) {
        primaryType.academicCourses = coursesPayload;
      }

      let auxType = this.state.addMoreAuxiallary.map((aux) => {
        return {
          allocation_type: aux.allocation_type,
          _program_id: aux._program_id,
          _department_id: aux._department_id,
          _department_division_id: aux._department_division_id,
          _department_subject_id: aux._department_subject_id,
        };
      });

      let fullData;

      let userType = this.state.staffType.filter((item) => item.isChecked === true);
      let userTypeValue = '';
      if (userType.length === 2) {
        userTypeValue = 'both';
      } else {
        userTypeValue = userType[0].value;
      }

      if (this.state.showAux === true || auxType) {
        fullData = {
          id: id,
          data:
            userTypeValue === 'both' || userTypeValue === 'academic'
              ? [...auxType, primaryType]
              : [],
          user_type: userTypeValue,
          institution_role: this.state.institution_role,
          academicCourses: coursesPayload,
        };
      } else {
        fullData = {
          id: id,
          data: userTypeValue === 'both' || userTypeValue === 'academic' ? [primaryType] : [],
          user_type: userTypeValue,
          institution_role: this.state.institution_role,
          academicCourses: coursesPayload,
        };
      }

      axios
        .post(`/user/user_academic_allocation`, fullData)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              employementDisable: false,
              employment_check: true, // Enable employment tab after successful academic submission
              addSubject1: [],
              auxDepartmentName: '',
              auxProgramName: '',
              subjectList1: [],
              departmentList1: [],
              subjectListError: '',
              subjectList1Error: '',
            },
            () => {
              this.fetchApi();
            }
          );
          NotificationManager.success(t('user_management.Academic_Details_Entered_Successfully'));
        })
        .catch((error) => {
          NotificationManager.error(`${error?.response?.data?.message}`);
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data');
    }
  };

  handleAux = () => {
    this.setState({
      showAux: !this.state.showAux,
      auxValied: !this.state.auxValied,
    });
  };

  removePrimarySubject = (index) => {
    let addSubject = this.state.addSubject;
    addSubject.splice(index, 1);
    this.setState({
      addSubject,
    });
  };

  removeAuxiliarySubject = (index) => {
    let addSubject1 = this.state.addSubject1;
    addSubject1.splice(index, 1);
    this.setState({
      addSubject1,
    });
  };

  removeAuxiliary = (index) => {
    let addMoreAuxiallary = this.state.addMoreAuxiallary;
    addMoreAuxiallary.splice(index, 1);
    this.setState({
      addMoreAuxiallary,
    });
  };

  handleGoBack = () => {
    if (this.state.userStatus === 'completed') {
      this.props.history.push({
        pathname: '/staff/management',
      });
    } else {
      this.props.history.push({
        pathname: '/staff/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
          selectedTab: this.state.tabs !== '' ? parseInt(this.state.tabs) : 0,
        },
      });
    }
  };

  handleCourseMultiSelect = (event) => {
    const {
      target: { value },
    } = event;
    const selectedIds = typeof value === 'string' ? value.split(',') : value;

    // Filter out any academicCourses that are already in the table
    const filteredIds = selectedIds.filter(
      (id) => !this.state.selectedCourses.some((course) => course.value === id)
    );

    this.setState({
      selectedCourseIds: filteredIds,
    });
  };

  handleRemoveCourse = (courseValue) => {
    const selectedCourseIds = this.state.selectedCourseIds.filter((id) => id !== courseValue);
    const selectedCourses = this.state.selectedCourses.filter(
      (course) => course.value !== courseValue
    );
    this.setState({ selectedCourseIds, selectedCourses });
  };

  handleAddCourses = () => {
    const {
      selectedProgramIdCourse,
      selectedProgramCourse,
      selectedCourseIds,
      courseList,
      originalCourseList,
      programList,
    } = this.state;

    if (selectedProgramIdCourse && selectedCourseIds.length > 0) {
      const programName =
        (programList.find((p) => p.value === selectedProgramCourse) || {}).name || '';

      const newRows = [];

      selectedCourseIds.forEach((courseId) => {
        const selectedCourse = courseList.find((c) => c.value === courseId) || {};
        const courseCode = selectedCourse.code;

        if (courseCode && originalCourseList) {
          // Get all course IDs that have the same courseCode
          const allCourseIdsForCode = originalCourseList
            .filter((course) => course.code === courseCode)
            .map((course) => course.value);

          // Add a row for each course ID with the same courseCode
          allCourseIdsForCode.forEach((actualCourseId) => {
            const actualCourse = originalCourseList.find((c) => c.value === actualCourseId) || {};

            // Check if this course is already added to avoid duplicates
            const isAlreadyAdded = this.state.selectedProgramCourses.some(
              (row) => row.programId === selectedProgramIdCourse && row.courseId === actualCourseId
            );

            if (!isAlreadyAdded) {
              newRows.push({
                programId: selectedProgramIdCourse,
                programName,
                courseId: actualCourseId,
                courseName: actualCourse.name || selectedCourse.courseName || '',
                courseCode: courseCode, // Add courseCode for reference
              });
            }
          });
        } else {
          // Fallback: if no courseCode or originalCourseList, add the single course
          const isAlreadyAdded = this.state.selectedProgramCourses.some(
            (row) => row.programId === selectedProgramIdCourse && row.courseId === courseId
          );

          if (!isAlreadyAdded) {
            newRows.push({
              programId: selectedProgramIdCourse,
              programName,
              courseId,
              courseName: selectedCourse.name || '',
              courseCode: selectedCourse.code || '',
            });
          }
        }
      });

      if (newRows.length > 0) {
        this.setState((prevState) => ({
          selectedProgramCourses: [...prevState.selectedProgramCourses, ...newRows],
          selectedProgramCourse: '',
          selectedProgramIdCourse: '',
          selectedCourseIds: [],
          employment_check: true, // Enable employment tab when courses are added
        }));
      }
    }
  };

  handleRemoveProgramCourse = (programId, courseId) => {
    this.setState((prevState) => {
      // Find the course to be removed
      const courseToRemove = prevState.selectedProgramCourses.find(
        (row) => row.programId === programId && row.courseId === courseId
      );

      if (!courseToRemove) return prevState;

      const courseCode = courseToRemove.courseCode;

      // If courseCode exists, remove all courses with the same courseCode and programId
      // Otherwise, just remove the specific course
      const filteredCourses = prevState.selectedProgramCourses.filter((row) => {
        if (courseCode && row.courseCode === courseCode && row.programId === programId) {
          return false; // Remove all courses with same courseCode and programId
        } else if (!courseCode && row.programId === programId && row.courseId === courseId) {
          return false; // Remove specific course if no courseCode
        }
        return true; // Keep other courses
      });

      return {
        selectedProgramCourses: filteredCourses,
      };
    });
  };

  // Handle individual row checkbox selection
  handleTableRowSelect = (rowKey, isSelected) => {
    this.setState((prevState) => {
      const selectedTableRows = isSelected
        ? [...prevState.selectedTableRows, rowKey]
        : prevState.selectedTableRows.filter((key) => key !== rowKey);

      const filteredRows = this.getFilteredRows();
      const selectAllTableRows =
        filteredRows.length > 0 &&
        filteredRows.every((row) => selectedTableRows.includes(row.programId + '-' + row.courseId));

      return {
        selectedTableRows,
        selectAllTableRows,
      };
    });
  };

  // Handle select all checkbox
  handleSelectAllTableRows = (isSelected) => {
    const filteredRows = this.getFilteredRows();
    const allRowKeys = filteredRows.map((row) => row.programId + '-' + row.courseId);

    this.setState({
      selectedTableRows: isSelected ? allRowKeys : [],
      selectAllTableRows: isSelected,
    });
  };

  // Delete selected rows
  handleDeleteSelectedRows = () => {
    this.setState((prevState) => {
      // Get the grouped rows to find which course codes to delete
      const groupedRows = this.groupTableRowsByCourseCode(prevState.selectedProgramCourses);
      const selectedGroupedRows = groupedRows.filter((row) =>
        prevState.selectedTableRows.includes(row.programId + '-' + row.courseId)
      );

      // Collect all course codes and program IDs that should be deleted
      const courseCodesToDelete = new Set();
      const programCoursePairs = new Set();

      selectedGroupedRows.forEach((row) => {
        if (row.courseCode) {
          courseCodesToDelete.add(`${row.programId}-${row.courseCode}`);
        } else {
          programCoursePairs.add(`${row.programId}-${row.courseId}`);
        }
      });

      // Filter out all courses that match the selected course codes or specific course IDs
      const filteredCourses = prevState.selectedProgramCourses.filter((row) => {
        // If course has courseCode, check if its group is selected for deletion
        if (row.courseCode && courseCodesToDelete.has(`${row.programId}-${row.courseCode}`)) {
          return false;
        }
        // If no courseCode, check specific course ID
        if (!row.courseCode && programCoursePairs.has(`${row.programId}-${row.courseId}`)) {
          return false;
        }
        return true;
      });

      return {
        selectedProgramCourses: filteredCourses,
        selectedTableRows: [],
        selectAllTableRows: false,
      };
    });
  };

  handleFilterClick = (column) => (event) => {
    this.setState({
      activeFilterColumn: column,
      filterAnchorEl: event.currentTarget,
    });
  };

  handleFilterClose = (column) => () => {
    this.setState({
      activeFilterColumn: null,
      filterAnchorEl: null,
    });
  };

  handleFilterSearch = (column) => (event) => {
    this.setState((prevState) => ({
      columnFilters: {
        ...prevState.columnFilters,
        [column]: {
          ...prevState.columnFilters[column],
          searchText: event.target.value,
        },
      },
    }));
  };

  handleFilterCheckbox = (column) => (value) => (event) => {
    this.setState((prevState) => {
      const currentSelected = prevState.columnFilters[column].selectedValues;
      const newSelected = event.target.checked
        ? [...currentSelected, value]
        : currentSelected.filter((v) => v !== value);

      const newColumnFilters = {
        ...prevState.columnFilters,
        [column]: {
          ...prevState.columnFilters[column],
          selectedValues: newSelected,
        },
      };

      // If program filter changes, clear course filter to show only relevant courses
      if (column === 'programName') {
        newColumnFilters.courseName = {
          ...prevState.columnFilters.courseName,
          selectedValues: [], // Clear course filter when program filter changes
        };
      }

      return {
        columnFilters: newColumnFilters,
      };
    });
  };

  // Function to group table rows by courseCode for display
  groupTableRowsByCourseCode = (rows) => {
    const grouped = rows.reduce((acc, row) => {
      const key = `${row.programId}-${row.courseCode || row.courseName}`;

      if (!acc[key]) {
        acc[key] = {
          // Display row (use first row for table display)
          displayRow: {
            ...row,
            displayName: `${row.courseName} - ${row.courseCode || ''}`.trim().replace(/ - $/, ''),
          },
          // All course IDs for this group (for deletion/selection)
          allCourseIds: [row.courseId],
          // All rows for this group
          allRows: [row],
          // Count of versions
          versionCount: 1,
        };
      } else {
        // Add to existing group
        acc[key].allCourseIds.push(row.courseId);
        acc[key].allRows.push(row);
        acc[key].versionCount++;
      }

      return acc;
    }, {});

    // Convert to array and return display rows
    return Object.values(grouped).map((group) => group.displayRow);
  };

  getFilteredRows = () => {
    const { selectedProgramCourses, searchText, columnFilters } = this.state;

    const search = (searchText || '').trim().toLowerCase();

    const filteredRows = selectedProgramCourses.filter((row) => {
      // Global search filter
      const matchesSearch =
        !search ||
        (row.programName && row.programName.toLowerCase().includes(search)) ||
        (row.courseName && row.courseName.toLowerCase().includes(search));

      // Column filters
      const matchesProgramFilter =
        columnFilters.programName.selectedValues.length === 0 ||
        columnFilters.programName.selectedValues.includes(row.programName);

      const matchesCourseFilter =
        columnFilters.courseName.selectedValues.length === 0 ||
        columnFilters.courseName.selectedValues.includes(row.courseName);

      return matchesSearch && matchesProgramFilter && matchesCourseFilter;
    });

    // Group filtered rows by courseCode to remove duplicates in table display
    return this.groupTableRowsByCourseCode(filteredRows);
  };

  getUniqueColumnValues = (column) => {
    const { selectedProgramCourses, columnFilters } = this.state;

    // If we're getting course values and there are program filters selected,
    // only show courses from the selected programs
    if (column === 'courseName') {
      const selectedPrograms = columnFilters.programName.selectedValues;

      if (selectedPrograms.length > 0) {
        // Filter courses to only show those from selected programs
        const filteredCourses = selectedProgramCourses
          .filter((row) => selectedPrograms.includes(row.programName))
          .map((row) => row[column]);

        return [...new Set(filteredCourses)].filter(Boolean);
      }
    }

    // Default behavior for all columns or when no program filter is applied
    return [...new Set(selectedProgramCourses.map((row) => row[column]))].filter(Boolean);
  };

  renderColumnFilter = (column) => {
    const { columnFilters } = this.state;
    const filter = columnFilters[column];
    const uniqueValues = this.getUniqueColumnValues(column);
    const filteredValues = uniqueValues.filter((value) =>
      value.toLowerCase().includes(filter.searchText.toLowerCase())
    );

    return (
      <div style={{ padding: '16px', minWidth: '200px' }}>
        <OutlinedInput
          placeholder="Search..."
          value={filter.searchText}
          onChange={this.handleFilterSearch(column)}
          size="small"
          fullWidth
          style={{ marginBottom: '8px' }}
        />
        <List dense style={{ maxHeight: '200px', overflow: 'auto' }}>
          {filteredValues.map((value) => (
            <ListItem key={value} dense button>
              <ListItemIcon>
                <Checkbox
                  edge="start"
                  checked={filter.selectedValues.includes(value)}
                  onChange={this.handleFilterCheckbox(column)(value)}
                />
              </ListItemIcon>
              <ListItemText primary={value} />
            </ListItem>
          ))}
        </List>
        <Button
          size="small"
          onClick={() => {
            this.setState((prevState) => ({
              columnFilters: {
                ...prevState.columnFilters,
                [column]: {
                  ...prevState.columnFilters[column],
                  selectedValues: [],
                },
              },
            }));
          }}
          style={{ marginTop: '8px' }}
        >
          Clear Filters
        </Button>
      </div>
    );
  };

  render() {
    const ACADEMIC_DESIGNATION_TRANS = ACADEMIC_DESIGNATION.map((item) => ({
      ...item,
      name: t(`user_management.${item.name}`),
    }));
    const { loggedInUserData } = this.props;
    const { tabs } = this.state;
    let isAcademicEnabled = false;
    let isAdministrativeEnabled = false;
    if (this.state.staffType) {
      let academicCheck = this.state.staffType.filter(
        (item) => item.isChecked === true && item.value === 'academic'
      );
      if (academicCheck && academicCheck.length > 0) {
        isAcademicEnabled = true;
      }
      let administrativeCheck = this.state.staffType.filter(
        (item) => item.isChecked === true && item.value === 'administration'
      );
      if (administrativeCheck && administrativeCheck.length > 0) {
        isAdministrativeEnabled = true;
      }
    }
    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';
    const filteredRows = this.getFilteredRows();
    return (
      <React.Fragment>
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'management_staff_profile'}></Trans>
        </div>
        <Loader isLoading={this.state.isLoading} />
        <NotificationContainer />
        <FlexWrapper className="nav_bg">
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Profile View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Profile View'
            )) && (
            <Link exact to={`/staff/management/profile${this.props.location.search}`}>
              <NavButton
                className={this.props.location.pathname === '/staff/management/profile' && 'active'}
                color="white"
              >
                <Trans i18nKey={'user_management.profile'}></Trans>
              </NavButton>
            </Link>
          )}
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Academic View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Academic View'
            )) && (
            <Link exact to={`/staff/management/academic${this.props.location.search}`}>
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/academic' && 'active'
                }
                color="white"
              >
                <Trans i18nKey={'user_management.academic'}></Trans>
              </NavButton>
            </Link>
          )}
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Employment View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Employment View'
            )) && (
            <Link
              exact
              to={
                this.state.academic_check
                  ? `/staff/management/employment${this.props.location.search}`
                  : `/staff/management/academic${this.props.location.search}`
              }
              // to={((isAcademicEnabled && this.state.addSubject && this.state.addSubject.length > 0 && this.state.user_type!=="") || (isAdministrativeEnabled && !isAcademicEnabled)) ? `/staff/management/employment${this.props.location.search}` : `/staff/management/academic${this.props.location.search}` }
            >
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/employment' && 'active'
                }
                color="white"
                // disabled={!((isAcademicEnabled && this.state.addSubject && this.state.addSubject.length > 0 && this.state.user_type!=="") || (isAdministrativeEnabled && !isAcademicEnabled))}

                disabled={!this.state.academic_check}
              >
                <Trans i18nKey={'user_management.employment'}></Trans>
              </NavButton>
            </Link>
          )}
          {loggedInUserData.get('staffFacial', false) &&
            (CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Biometric View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Biometric View'
              )) && (
              <Link
                exact
                to={
                  this.state.employment_check
                    ? `/staff/management/biometric${this.props.location.search}`
                    : `/staff/management/academic${this.props.location.search}`
                }
              >
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/biometric' && 'active'
                  }
                  color="white"
                  disabled={!this.state.employment_check}
                >
                  <Trans i18nKey={'user_management.biometric'}></Trans>
                </NavButton>
              </Link>
            )}
        </FlexWrapper>
        <div className="main p-2">
          <div className="container">
            <React.Fragment>
              <div className="float-right">
                <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                  <Trans i18nKey={'back'}></Trans>
                </Button>
                {(CheckPermission(
                  'tabs',
                  'User Management',
                  'Staff Management',
                  '',
                  permissionName,
                  'Academic Edit'
                ) ||
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Staff Management',
                    '',
                    'Registration Pending',
                    '',
                    permissionName,
                    'Academic Edit'
                  )) && (
                  <Button
                    className="m-2"
                    onClick={(e) =>
                      this.handleSubmit(e, isAcademicEnabled, isAdministrativeEnabled)
                    }
                    disabled={
                      !(
                        (isAcademicEnabled &&
                          ((this.state.addSubject && this.state.addSubject.length > 0) ||
                            (this.state.selectedProgramCourses &&
                              this.state.selectedProgramCourses.length > 0))) ||
                        (isAdministrativeEnabled && !isAcademicEnabled)
                      )
                    }
                  >
                    <Trans i18nKey={'save'}></Trans>
                  </Button>
                )}

                {/* {this.state.auxValied === true ||
              this.state.primarySub === false ? ( */}
                {/* {this.state.addSubject.length === 0 ||
                  this.state.auxValied === true ? (
                    <Button className="m-2" disabled>
                      Save
                    </Button>
                  ) : (
                    <Button className="m-2" onClick={this.handleSubmit}>
                      Save
                    </Button>
                  )} */}
              </div>

              <div className="clearfix"></div>
              <div className="white p-4 mb-5">
                <div className="row">
                  <div className="col-md-12 mb-4">
                    <div className="d-flex">
                      {this.state.staffType.map((data, index) => {
                        return (
                          <span className="mr-4" key={index}>
                            <input
                              type="checkbox"
                              className="calendarFormRadio"
                              onClick={(event) => this.handleCheckStaffType(event, index)}
                              checked={data.isChecked}
                            />{' '}
                            {data.name}
                          </span>
                        );
                      })}
                    </div>

                    {isAcademicEnabled && (
                      <div className="d-flex">
                        <div className="mt-3 mb-2 col-md-3" style={{ paddingLeft: '0px' }}>
                          <Input
                            elementType={'floatingselect'}
                            elementConfig={{
                              options: ACADEMIC_DESIGNATION_TRANS,
                            }}
                            value={this.state.institution_role}
                            floatingLabel={
                              <Trans i18nKey={'user_management.academic_designation'}></Trans>
                            }
                            changed={(e) => this.handleSelect(e, 'institution_role')}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  {isAcademicEnabled && (
                    <Accordion
                      className="col-md-12 mb-4 py-2"
                      expanded={this.state.isProgramSubjectAccordionOpen}
                      onChange={(_, expanded) =>
                        this.setState({ isProgramSubjectAccordionOpen: expanded })
                      }
                      sx={{
                        borderRadius: 2,
                        boxShadow: 3,
                        borderLeft: this.state.isProgramSubjectAccordionOpen
                          ? '6px solid #1976d2'
                          : 'none',
                        background: '#fff',
                        mb: 2,
                        '&:before': { display: 'none' },
                      }}
                    >
                      <AccordionSummary
                        expandIcon={
                          <ExpandMoreIcon
                            sx={{
                              color: '#1976d2',
                              fontSize: 32,
                              transition: 'transform 0.2s',
                            }}
                          />
                        }
                        aria-controls="panel1-content"
                        id="panel1-header"
                        sx={{
                          minHeight: 56,
                          background: '#fff',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.06)',
                          },
                          borderRadius: 2,
                        }}
                      >
                        <div
                          style={{
                            fontWeight: 300,
                            color: '#000000',
                            fontSize: '1.1rem',
                            letterSpacing: 1,
                          }}
                        >
                          Program & Subject
                        </div>
                      </AccordionSummary>
                      <AccordionDetails>
                        <div className="d-flex">
                          <div className="col-md-4" style={{ textAlign: 'left' }}>
                            <div className="img_border">
                              <div className="col-md-12">
                                <h4 className="text-red f-16">
                                  <Trans i18nKey={'user_management.primary'}></Trans> *
                                </h4>
                              </div>
                              <div className="col-md-12 ">
                                <Input
                                  elementType={'floatingselect'}
                                  elementConfig={{
                                    options: this.state.programList,
                                  }}
                                  value={this.state.selectedProgram}
                                  floatingLabel={
                                    <Trans i18nKey={'user_management.select_program'}></Trans>
                                  }
                                  changed={(e) => this.handleSelect(e, 'program')}
                                  feedback={this.state.programError}
                                />
                              </div>

                              <div className="col-md-12  pt-2">
                                <Input
                                  elementType={'floatingselect'}
                                  elementConfig={{
                                    options: this.state.departmentList,
                                  }}
                                  value={this.state.selectedDepartment}
                                  floatingLabel={
                                    <Trans i18nKey={'user_management.select_department'}></Trans>
                                  }
                                  changed={(e) => this.handleSelect(e, 'department')}
                                  feedback={this.state.departmentError}
                                />
                              </div>

                              <div className="col-md-12  pt-2">
                                <Input
                                  elementType={'floatingselect'}
                                  elementConfig={{
                                    options: this.state.divisionList,
                                  }}
                                  value={this.state.selectedDivision}
                                  floatingLabel={
                                    <Trans i18nKey={'user_management.select_division'}></Trans>
                                  }
                                  changed={(e) => this.handleSelect(e, 'division')}
                                  feedback={this.state.divisionError}
                                />
                              </div>

                              <div className="col-md-12  pt-2">
                                <Input
                                  elementType={'floatingselect'}
                                  elementConfig={{
                                    options: this.state.subjectList,
                                  }}
                                  value={this.state.selectedSubject}
                                  floatingLabel={
                                    <Trans i18nKey={'user_management.select_subject'}></Trans>
                                  }
                                  changed={(e) => this.handleSelect(e, 'subject')}
                                  feedback={this.state.subjectListError}
                                />
                              </div>

                              <div className="col-md-12 pt-4">
                                <p className="remove_hover" onClick={this.handleAddSubject}>
                                  <i className="fa fa-plus-circle" aria-hidden="true"></i>{' '}
                                  <Trans i18nKey={'add_save'}></Trans>{' '}
                                  <Trans i18nKey={'configuration.subject'}></Trans>{' '}
                                </p>
                              </div>

                              {this.state.addSubject.map((su, index) => (
                                <>
                                  <div className="col-md-12">
                                    <div className="row">
                                      <div className="col-md-10  pt-2">
                                        <Input
                                          elementType={'floatinginput'}
                                          elementConfig={{
                                            type: 'text',
                                          }}
                                          disabled={'disabled'}
                                          value={su.subjectName}
                                          floatingLabel={
                                            <Trans
                                              i18nKey={'user_management.add_subject_name'}
                                            ></Trans>
                                          }
                                        />
                                      </div>
                                      <div className="col-md-2  mt-35">
                                        <i
                                          className="fa fa-times-circle remove_hover"
                                          onClick={() => this.removePrimarySubject(index)}
                                        ></i>
                                      </div>
                                    </div>
                                  </div>
                                </>
                              ))}
                            </div>
                          </div>

                          <div className={`col-md-4`}>
                            <div className={`img_border`}>
                              <div
                                className={`row ${
                                  this.state.checkboxHide === true && 'hide_content'
                                }`}
                              >
                                <div className="col-xl-10">
                                  <h4 className="text-red f-16">
                                    {' '}
                                    <Trans i18nKey={'user_management.auxiliary'}></Trans>{' '}
                                  </h4>
                                </div>
                                <div className={`col-xl-2`}>
                                  <label
                                    className={`switch ${getLang() === 'ar' ? 'float-right' : ''}`}
                                  >
                                    {this.state.primarySub === false ? (
                                      <input type="checkbox" disabled />
                                    ) : (
                                      <input type="checkbox" onClick={this.handleAux} />
                                    )}

                                    <span className="slider_check round"></span>
                                  </label>
                                </div>
                              </div>

                              <div className={`${this.state.showAux === false && 'hide_content'}`}>
                                <div className="col-md-12 ">
                                  <Input
                                    elementType={'floatingselect'}
                                    elementConfig={{
                                      options: this.state.programList,
                                    }}
                                    value={this.state.selectedProgram1}
                                    floatingLabel={
                                      <Trans i18nKey={'user_management.select_program'}></Trans>
                                    }
                                    changed={(e) => this.handleAuxSelect(e, 'program1')}
                                    feedback={this.state.program1Error}
                                  />
                                </div>

                                <div className="col-md-12  pt-2">
                                  <Input
                                    elementType={'floatingselect'}
                                    elementConfig={{
                                      options: this.state.departmentList1,
                                    }}
                                    value={this.state.selectedDepartment1}
                                    floatingLabel={
                                      <Trans i18nKey={'user_management.select_department'}></Trans>
                                    }
                                    changed={(e) => this.handleAuxSelect(e, 'department1')}
                                    feedback={this.state.department1Error}
                                  />
                                </div>

                                <div className="col-md-12  pt-2">
                                  <Input
                                    elementType={'floatingselect'}
                                    elementConfig={{
                                      options: this.state.divisionList1,
                                    }}
                                    value={this.state.selectedDivision1}
                                    floatingLabel={
                                      <Trans i18nKey={'user_management.select_division'}></Trans>
                                    }
                                    changed={(e) => this.handleAuxSelect(e, 'division1')}
                                    feedback={this.state.division1Error}
                                  />
                                </div>

                                <div className="col-md-12  pt-2">
                                  <Input
                                    elementType={'floatingselect'}
                                    elementConfig={{
                                      options: this.getAuxSubject(),
                                    }}
                                    value={this.state.selectedSubject1}
                                    floatingLabel={
                                      <Trans i18nKey={'user_management.select_subject'}></Trans>
                                    }
                                    changed={(e) => this.handleAuxSelect(e, 'subject1')}
                                    feedback={this.state.subjectList1Error}
                                  />
                                </div>

                                <div className="col-md-12 pt-4">
                                  <p className="remove_hover" onClick={this.handleAuxAddSubject}>
                                    <span className="pt-1">
                                      <i className="fa fa-plus-circle mr-2" aria-hidden="true"></i>
                                      <Trans i18nKey={'user_management.add_more_subject'}></Trans>
                                    </span>
                                  </p>
                                </div>
                                {this.state.addSubject1.map((su1, index) => (
                                  <>
                                    <div className="col-md-12">
                                      <div className="row">
                                        <div className="col-md-10  pt-2">
                                          <Input
                                            elementType={'floatinginput'}
                                            elementConfig={{
                                              type: 'text',
                                            }}
                                            disabled={'disabled'}
                                            value={su1.auxSubjectName}
                                            floatingLabel={
                                              <Trans
                                                i18nKey={'user_management.add_subject_name'}
                                              ></Trans>
                                            }
                                          />
                                        </div>
                                        <div className="col-md-2  mt-35">
                                          <i
                                            className="fa fa-times-circle remove_hover"
                                            onClick={() => this.removeAuxiliarySubject(index)}
                                          ></i>
                                        </div>
                                      </div>
                                    </div>
                                  </>
                                ))}

                                <div className="col-md-12">
                                  <Button
                                    style={{ width: '100%' }}
                                    onClick={this.handleAuxFullSubmit}
                                    disabled={
                                      !(
                                        this.state.addSubject1.length > 0 &&
                                        this.state.auxDepartmentName !== '' &&
                                        this.state.auxProgramName !== ''
                                      )
                                    }
                                  >
                                    {' '}
                                    <Trans i18nKey={'user_management.add_auxiliary'}></Trans>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="col-md-4">
                            {this.state.addMoreAuxiallary &&
                              this.state.addMoreAuxiallary.length > 0 &&
                              this.state.addMoreAuxiallary.map((sue, index) => (
                                <React.Fragment key={index}>
                                  <div className="img_border">
                                    <div className="col-md-12 ">
                                      <span className="text-blue f-16">
                                        {' '}
                                        <Trans i18nKey={'user_management.auxiliary'}></Trans>{' '}
                                      </span>
                                      <span className="float-right">
                                        <i
                                          className="fa fa-times-circle"
                                          onClick={() => this.removeAuxiliary(index)}
                                        ></i>
                                      </span>
                                    </div>
                                    <ProfileText
                                      title={<Trans i18nKey={'program_name'}></Trans>}
                                      value={sue.auxProgramName}
                                    />
                                    <ProfileText
                                      title={
                                        <Trans i18nKey={'user_management.department_name'}></Trans>
                                      }
                                      value={sue.auxDepartmentName}
                                    />

                                    <ProfileText
                                      title={<Trans i18nKey={'division_name'}></Trans>}
                                      value={sue.auxDivisionName}
                                    />

                                    <ProfileText
                                      title={<Trans i18nKey={'configuration.subject_name'}></Trans>}
                                      value={sue.subjectName.map((yy, index) => (
                                        <React.Fragment key={index}>
                                          {' '}
                                          {yy} <br />{' '}
                                        </React.Fragment>
                                      ))}
                                    />
                                  </div>
                                  <br />
                                  <br />
                                </React.Fragment>
                              ))}
                          </div>
                        </div>
                      </AccordionDetails>
                    </Accordion>
                  )}
                  {isAcademicEnabled && (
                    <Accordion
                      className="col-md-12 mb-4 py-2"
                      expanded={this.state.isProgramCourseAccordionOpen}
                      onChange={(_, expanded) =>
                        this.setState({ isProgramCourseAccordionOpen: expanded })
                      }
                      sx={{
                        borderRadius: 2,
                        boxShadow: 3,
                        borderLeft: this.state.isProgramCourseAccordionOpen
                          ? '6px solid #1976d2'
                          : 'none',
                        background: '#fff',
                        mb: 2,
                        '&:before': { display: 'none' },
                      }}
                    >
                      <AccordionSummary
                        expandIcon={
                          <ExpandMoreIcon
                            sx={{
                              color: '#1976d2',
                              fontSize: 32,
                              transition: 'transform 0.2s',
                            }}
                          />
                        }
                        aria-controls="panel1-content"
                        id="panel1-header"
                        sx={{
                          minHeight: 56,
                          background: '#fff',
                          '&:hover': {
                            background: 'rgba(25, 118, 210, 0.06)',
                          },
                          borderRadius: 2,
                        }}
                      >
                        <div
                          style={{
                            fontWeight: 300,
                            color: '#000000',
                            fontSize: '1.1rem',
                            letterSpacing: 1,
                          }}
                        >
                          Program & Course
                        </div>
                      </AccordionSummary>
                      <AccordionDetails sx={{ background: '#fff', borderRadius: 2 }}>
                        <div className="d-flex">
                          <div className="col-md-5" style={{ textAlign: 'left' }}>
                            <Autocomplete
                              options={this.state.programList.filter((p) => p.value)}
                              getOptionLabel={(option) => option.name || ''}
                              value={
                                this.state.programList.find(
                                  (p) => p.value === this.state.selectedProgramCourse
                                ) || null
                              }
                              onChange={(event, newValue) => {
                                if (newValue) {
                                  const fakeEvent = {
                                    target: {
                                      value: newValue.value,
                                    },
                                    preventDefault: () => {},
                                  };
                                  this.handleSelect(fakeEvent, 'programCourse');
                                } else {
                                  const fakeEvent = {
                                    target: {
                                      value: '',
                                    },
                                    preventDefault: () => {},
                                  };
                                  this.handleSelect(fakeEvent, 'programCourse');
                                  // Clear course selection as well
                                  this.setState({
                                    selectedCourseIds: [],
                                  });
                                }
                              }}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label={t('user_management.select_program')}
                                  placeholder={t('user_management.select_program')}
                                  variant="outlined"
                                  fullWidth
                                />
                              )}
                              isOptionEqualToValue={(option, value) => option.value === value.value}
                            />
                          </div>

                          <div className={`col-md-5`}>
                            <div>
                              <Autocomplete
                                multiple
                                disableCloseOnSelect
                                options={[
                                  { value: 'SELECT_ALL', name: 'Select All', isSelectAll: true },
                                  ...this.state.courseList,
                                ]}
                                getOptionLabel={(option) => option.name || ''}
                                value={this.state.courseList.filter((course) =>
                                  this.state.selectedCourseIds.includes(course.value)
                                )}
                                onChange={(event, newValue) => {
                                  // Check if "Select All" was clicked
                                  const selectAllClicked = newValue.some(
                                    (item) => item.isSelectAll
                                  );

                                  if (selectAllClicked) {
                                    // Get available courses (excluding already added ones)
                                    const availableCourses = this.state.courseList.filter(
                                      (course) =>
                                        !this.state.selectedProgramCourses.some(
                                          (row) =>
                                            row.courseId === course.value &&
                                            row.programId === this.state.selectedProgramIdCourse
                                        )
                                    );

                                    // Check if all available courses are currently selected
                                    const allAvailableSelected =
                                      availableCourses.length > 0 &&
                                      availableCourses.every((course) =>
                                        this.state.selectedCourseIds.includes(course.value)
                                      );

                                    if (allAvailableSelected) {
                                      // If all are selected, deselect all
                                      this.setState({ selectedCourseIds: [] });
                                    } else {
                                      // If not all are selected, select all available courses
                                      const allAvailableIds = availableCourses.map(
                                        (course) => course.value
                                      );
                                      this.setState({ selectedCourseIds: allAvailableIds });
                                    }
                                  } else {
                                    // Normal selection (filter out "Select All" option)
                                    const filteredValue = newValue.filter(
                                      (item) => !item.isSelectAll
                                    );
                                    const selectedIds = filteredValue.map((course) => course.value);
                                    this.setState({ selectedCourseIds: selectedIds });
                                  }
                                }}
                                isOptionEqualToValue={(option, value) =>
                                  option.value === value.value
                                }
                                renderOption={(props, option, { selected }) => {
                                  if (option.isSelectAll) {
                                    // Render "Select All" option
                                    const availableCourses = this.state.courseList.filter(
                                      (course) =>
                                        !this.state.selectedProgramCourses.some(
                                          (row) =>
                                            row.courseId === course.value &&
                                            row.programId === this.state.selectedProgramIdCourse
                                        )
                                    );
                                    const allSelected =
                                      availableCourses.length > 0 &&
                                      availableCourses.every((course) =>
                                        this.state.selectedCourseIds.includes(course.value)
                                      );
                                    const someSelected = availableCourses.some((course) =>
                                      this.state.selectedCourseIds.includes(course.value)
                                    );

                                    return (
                                      <li
                                        {...props}
                                        style={{
                                          borderBottom: '1px solid #e0e0e0',
                                          backgroundColor: '#f5f5f5',
                                          fontWeight: 'bold',
                                        }}
                                      >
                                        <Checkbox
                                          style={{ marginRight: 8 }}
                                          checked={allSelected}
                                          indeterminate={someSelected && !allSelected}
                                        />
                                        <span>{option.name}</span>
                                      </li>
                                    );
                                  }

                                  // Disable if already added for this program
                                  const isAlreadySelected = this.state.selectedProgramCourses.some(
                                    (row) =>
                                      row.courseId === option.value &&
                                      row.programId === this.state.selectedProgramIdCourse
                                  );
                                  return (
                                    <li
                                      {...props}
                                      style={
                                        isAlreadySelected
                                          ? { pointerEvents: 'none', opacity: 0.5 }
                                          : {}
                                      }
                                    >
                                      <Checkbox
                                        style={{ marginRight: 8 }}
                                        checked={selected}
                                        disabled={isAlreadySelected}
                                      />
                                      <span
                                        style={{
                                          color: isAlreadySelected ? '#999' : 'inherit',
                                        }}
                                      >
                                        {option.name}
                                      </span>
                                    </li>
                                  );
                                }}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    label={t('user_management.select_course')}
                                    placeholder={t('user_management.select_course')}
                                    variant="outlined"
                                    fullWidth
                                  />
                                )}
                                disabled={this.state.selectedProgramIdCourse === ''}
                              />
                            </div>
                          </div>

                          <div className="col-md-2 d-flex align-items-center">
                            <Button
                              variant="contained"
                              color="primary"
                              onClick={this.handleAddCourses}
                              disabled={
                                !this.state.selectedProgramIdCourse ||
                                this.state.selectedCourseIds.length === 0
                              }
                              style={{
                                backgroundColor: '#1976d2', // MUI blue
                                color: '#fff',
                                fontWeight: 300,
                                boxShadow: '0 2px 8px rgba(25, 118, 210, 0.15)',
                                borderRadius: 6,
                                textTransform: 'none',
                                letterSpacing: 1,
                                fontSize: '1rem',
                              }}
                            >
                              <Trans i18nKey={'add'} />
                            </Button>
                          </div>
                        </div>
                        <TableContainer component={Paper} style={{ marginTop: 24 }}>
                          <Table>
                            <TableHead>
                              <TableRow sx={{ backgroundColor: '#e3e3e3' }}>
                                <TableCell style={{ width: '50px' }}>
                                  <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <Checkbox
                                      checked={this.state.selectAllTableRows}
                                      indeterminate={
                                        this.state.selectedTableRows.length > 0 &&
                                        !this.state.selectAllTableRows
                                      }
                                      onChange={(e) =>
                                        this.handleSelectAllTableRows(e.target.checked)
                                      }
                                      size="small"
                                    />
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <b>Program Name</b>
                                    <Badge
                                      color="primary"
                                      badgeContent={
                                        this.state.columnFilters.programName.selectedValues.length
                                      }
                                      invisible={
                                        this.state.columnFilters.programName.selectedValues
                                          .length === 0
                                      }
                                      sx={{ marginLeft: 1 }}
                                    >
                                      <IconButton
                                        size="small"
                                        onClick={this.handleFilterClick('programName')}
                                        style={{ marginLeft: '4px' }}
                                      >
                                        <FilterListIcon fontSize="small" />
                                      </IconButton>
                                    </Badge>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <b>Course Name</b>
                                    <Badge
                                      color="primary"
                                      badgeContent={
                                        this.state.columnFilters.courseName.selectedValues.length
                                      }
                                      invisible={
                                        this.state.columnFilters.courseName.selectedValues
                                          .length === 0
                                      }
                                      sx={{ marginLeft: 1 }}
                                    >
                                      <IconButton
                                        size="small"
                                        onClick={this.handleFilterClick('courseName')}
                                        style={{ marginLeft: '4px' }}
                                      >
                                        <FilterListIcon fontSize="small" />
                                      </IconButton>
                                    </Badge>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {this.state.selectedTableRows.length > 0 ? (
                                    <IconButton
                                      size="small"
                                      onClick={this.handleDeleteSelectedRows}
                                      style={{ color: 'red', marginLeft: '4px' }}
                                      title="Delete Selected"
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  ) : (
                                    <b>Action</b>
                                  )}
                                </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {filteredRows.length > 0 ? (
                                filteredRows.map((row, idx) => {
                                  const rowKey = row.programId + '-' + row.courseId;
                                  const isSelected = this.state.selectedTableRows.includes(rowKey);

                                  return (
                                    <TableRow
                                      key={rowKey}
                                      sx={{ backgroundColor: idx % 2 === 0 ? '#f9f9f9' : '#fff' }}
                                    >
                                      <TableCell>
                                        <Checkbox
                                          checked={isSelected}
                                          onChange={(e) =>
                                            this.handleTableRowSelect(rowKey, e.target.checked)
                                          }
                                          size="small"
                                        />
                                      </TableCell>
                                      <TableCell>{row.programName} </TableCell>
                                      <TableCell>{row.courseName} </TableCell>
                                      <TableCell>
                                        <IconButton
                                          aria-label="delete"
                                          size="small"
                                          onClick={() =>
                                            this.handleRemoveProgramCourse(
                                              row.programId,
                                              row.courseId
                                            )
                                          }
                                          style={{ color: 'red', marginLeft: 8 }}
                                        >
                                          <DeleteIcon fontSize="small" />
                                        </IconButton>
                                      </TableCell>
                                    </TableRow>
                                  );
                                })
                              ) : (
                                <TableRow>
                                  <TableCell colSpan={4} align="center">
                                    No results found
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </AccordionDetails>
                    </Accordion>
                  )}
                </div>
              </div>
            </React.Fragment>
          </div>
        </div>
        {this.state.activeFilterColumn && (
          <Popover
            open={Boolean(this.state.activeFilterColumn)}
            anchorEl={this.state.filterAnchorEl}
            onClose={this.handleFilterClose(this.state.activeFilterColumn)}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            PaperProps={{
              style: {
                marginTop: '4px',
                minWidth: '200px',
              },
            }}
          >
            {this.renderColumnFilter(this.state.activeFilterColumn)}
          </Popover>
        )}
      </React.Fragment>
    );
  }
}

ValideAcademic.propTypes = {
  publishedProgramLists: PropTypes.array,
  history: PropTypes.object,
  location: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    publishedProgramLists: selectAllProgramLists(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(React.memo(ValideAcademic)));
