import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import Loader from '../../../Widgets/Loader/Loader';
import Input from '../../../Widgets/FormElements/Input/Input';
import ProfileText from '../../../Widgets/ProfileText';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import axios from '../../../axios';
import { Trans } from 'react-i18next';
import { Button } from 'react-bootstrap';
import { FlexWrapper, NavButton } from '../../../_components/Styled/styled';
//import { STAFF_TYPE,  } from '../../../constants';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { getLang } from 'utils';
import { t } from 'i18next';
import { STAFF_TYPE, ACADEMIC_DESIGNATION } from 'Components/utils';
import { selectAllProgramLists, selectUserInfo } from '_reduxapi/Common/Selectors';
let id;
class ValideAcademic extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      showAux: false,
      checkboxHide: false,
      primarySub: false,
      defaultDepartmentList: [],
      programList: [],
      departmentList: [],
      divisionList: [],
      subjectList: [],

      selectedProgram: '',
      selectedDepartment: '',
      selectedDivision: '',
      selectedSubject: '',
      addSubject: [],
      subjectAddName: '',
      auxValied: false,

      departmentList1: [],
      divisionList1: [],
      subjectList1: [],

      selectedProgram1: '',
      selectedDepartment1: '',
      selectedDivision1: '',
      selectedSubject1: '',

      addSubject1: [],
      addMoreAuxiallary: [],
      addAuxiallary: [],
      subjectAddName1: '',
      auxProgramName: '',
      auxDepartmentName: '',
      auxDivisionName: '',
      selectedProgramId: '',
      selectedProgramIdAux: '',
      employementDisable: true,
      primaryDataAr: [],
      auxiliaryDataAr: [],
      academic_check: false,
      employment_check: false,
      studyDivisionVisible: true,
      tabs: 0,
      user_type: null,
      institution_role: null,
      staffType: STAFF_TYPE.map((item) => ({
        ...item,
        name: t(`user_management.${item.name}`),
      })),
      userStatus: '',
      programError: '',
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');
    let tab = params.get('tab');
    this.setState({ tabs: tab });
    this.fetchApi();
  }

  getSubjectName = (data, id) => {
    let subjectName = '';
    if (data && data.length > 0) {
      subjectName =
        data && data.filter((item) => item._id === id).reduce((_, el) => el.subject_name, '');
    }
    return subjectName;
  };

  callProgram = () => {
    this.interval = setInterval(() => {
      const { publishedProgramLists } = this.props;
      if (publishedProgramLists.length > 0) {
        const formattedArray = publishedProgramLists.map((item) => {
          return { name: item.name, value: item.id, id: item.id };
        });
        formattedArray.unshift({
          name: '',
          value: '',
          id: '',
        });
        this.setState({
          programList: formattedArray,
          program: formattedArray[0].value,
        });
        this.editLoad();
        clearInterval(this.interval);
      }
    }, 200);
  };

  fetchApi = () => {
    this.setState({ isLoading: true });
    axios
      .get(`/user/staff/${id}`)
      .then((res) => {
        const data = res.data.data;
        this.setState({
          userStatus: data.status,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
    this.callProgram();
  };

  editLoad = () => {
    axios
      .get(`user/staff_profile_details/${id}`)
      .then((res) => {
        let academic_allocation = res.data.data.academic_allocation;

        let primaryData =
          academic_allocation &&
          academic_allocation.length > 0 &&
          academic_allocation.filter((primary) => {
            return primary.allocation_type === 'primary';
          });

        let auxiliaryData =
          academic_allocation &&
          academic_allocation.length > 0 &&
          academic_allocation.filter((primary) => {
            return primary.allocation_type === 'auxiliary';
          });

        let selected_data =
          primaryData &&
          primaryData.length > 0 &&
          this.state.programList.length > 0 &&
          this.state.programList
            .filter((item) => item.id === primaryData[0]._program_id._id)
            .reduce((_, el) => {
              return el.value;
            }, '');

        let auxiliaryDataArray =
          auxiliaryData &&
          auxiliaryData.length > 0 &&
          auxiliaryData.map((item) => {
            return {
              allocation_type: 'auxiliary',
              auxDepartmentName:
                item._department_id !== undefined &&
                item._department_id.department_name !== undefined
                  ? item._department_id.department_name
                  : '',
              _department_id:
                item._department_id !== undefined && item._department_id._id !== undefined
                  ? item._department_id._id
                  : '',
              auxProgramName:
                item._program_id !== undefined && item._program_id.name !== undefined
                  ? item._program_id.name
                  : '',
              _program_id:
                item._program_id !== undefined && item._program_id._id !== undefined
                  ? item._program_id._id
                  : '',
              _department_division_id:
                item._department_division_id !== undefined &&
                item._department_division_id._id !== undefined
                  ? item._department_division_id._id
                  : '',
              auxDivisionName:
                item._department_division_id !== undefined &&
                item._department_division_id.title !== undefined
                  ? item._department_division_id.title
                  : '',
              subjectName:
                item._department_subject_id !== undefined &&
                item._department_subject_id.length > 0 &&
                item._department_subject_id.map((subject) =>
                  this.getSubjectName(item._department_id.subject, subject)
                ),
              _department_subject_id:
                item._department_subject_id !== undefined &&
                item._department_subject_id.length > 0 &&
                item._department_subject_id.map((subject) => subject),
            };
          });

        let staffType = this.state.staffType;

        if (res.data.data !== undefined && res.data.data.staff_employment_type !== '') {
          staffType = this.state.staffType.map((item) => {
            let isCheck = false;
            if (res.data.data.staff_employment_type === item.value) {
              isCheck = true;
            } else if (res.data.data.staff_employment_type === 'both') {
              isCheck = true;
            }
            return { ...item, isChecked: isCheck };
          });
        }

        this.setState(
          {
            isLoading: false,
            primaryDataAr: primaryData,
            addMoreAuxiallary:
              auxiliaryDataArray && auxiliaryDataArray.length > 0 ? auxiliaryDataArray : [],
            selectedProgram: selected_data !== false ? selected_data : '',
            selectedProgramId:
              primaryData && primaryData.length > 0 ? primaryData[0]._program_id._id : '',
            selectedDepartment:
              primaryData && primaryData.length > 0 ? primaryData[0]._department_id._id : '',
            selectedSubject:
              primaryData && primaryData.length > 0
                ? primaryData[0]._department_subject_id[0]._id
                : '',
            selectedDivision:
              primaryData &&
              primaryData.length > 0 &&
              primaryData[0]._department_division_id !== undefined
                ? primaryData[0]._department_division_id._id
                : '',
            academic_check: res.data.data.academic_check,
            employment_check: res.data.data.employment_check,
            primarySub: primaryData && primaryData.length > 0,
            user_type:
              res.data.data.staff_employment_type !== undefined
                ? res.data.data.staff_employment_type
                : null,
            institution_role:
              res.data.data.institution_role !== undefined ? res.data.data.institution_role : null,
            staffType: staffType,
          },
          () => {
            if (selected_data !== false && selected_data !== '') {
              axios
                .get(`/digi_department_subject/${selected_data}?limit=100&pageNo=1`)
                .then((res) => {
                  const departmentList =
                    res.data.data &&
                    res.data.data.length > 0 &&
                    res.data.data.map((data) => {
                      return {
                        name: data.department_name,
                        value: data._id,
                      };
                    });
                  departmentList.unshift({
                    name: '',
                    value: '',
                  });

                  const subjectList =
                    res.data.data &&
                    res.data.data.length > 0 &&
                    res.data.data
                      .filter((item) => item._id === primaryData[0]._department_id._id)
                      .reduce((_, el) => el.subject, [])
                      .filter((item) => item.isActive === true && item.isDeleted === false)
                      .map((sub) => {
                        return {
                          name: sub.subject_name,
                          value: sub._id,
                        };
                      });
                  subjectList.unshift({
                    name: '',
                    value: '',
                  });

                  let addSubject = [];
                  primaryData[0]._department_subject_id &&
                    primaryData[0]._department_subject_id.length > 0 &&
                    primaryData[0]._department_subject_id.forEach((item) => {
                      addSubject.push({
                        subjectView: item,
                        subjectName: this.getSubjectName(
                          primaryData[0]._department_id?.subject,
                          item
                        ),
                      });
                    });
                  this.setState({
                    departmentList: departmentList,
                    defaultDepartmentList:
                      res.data.data && res.data.data.length > 0 ? res.data.data : [],
                    depart: departmentList[0].value,
                    isLoading: false,
                    enable: true,
                    divisionList: [],
                    subjectList: subjectList,
                    addSubject,
                    selectedSubject: '',
                  });
                })
                .catch((error) => {
                  this.setState({
                    isLoading: false,
                  });
                });
            }
          }
        );
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
          primaryDataAr: [],
          addMoreAuxiallary: [],
        });
      });
  };

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  handleSelect = (e, name) => {
    e.preventDefault();

    if (name === 'institution_role') {
      this.setState({ institution_role: e.target.value });
    } else if (name === 'program') {
      let selectedProgramId;
      for (let i = 0; i < this.state.programList.length; i++) {
        if (this.state.programList[i].value === e.target.value) {
          selectedProgramId = this.state.programList[i].id;
        }
      }
      this.setState({
        selectedProgram: e.target.value,
        selectedProgramId: selectedProgramId,
        programError: '',
        isLoading: true,
      });

      axios
        .get(`/digi_department_subject/${e.target.value}?limit=100&pageNo=1`)
        .then((res) => {
          const departmentList = res.data.data.map((data) => {
            return {
              name: data.department_name,
              value: data._id,
            };
          });
          departmentList.unshift({
            name: '',
            value: '',
          });
          this.setState({
            departmentList: departmentList,
            defaultDepartmentList: res.data.data && res.data.data.length > 0 ? res.data.data : [],
            depart: departmentList[0].value,
            isLoading: false,
            addSubject: [],
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'department') {
      this.setState({
        selectedDepartment: e.target.value,
        departmentError: '',
        isLoading: true,
      });

      let subjectList = [];
      let selectedSubject = '';
      if (e.target.value !== '') {
        subjectList =
          this.state.defaultDepartmentList &&
          this.state.defaultDepartmentList.length > 0 &&
          this.state.defaultDepartmentList
            .filter((item) => item._id === e.target.value)
            .reduce((_, el) => el.subject, [])
            .filter((item) => item.isActive === true && item.isDeleted === false)
            .map((sub) => {
              return {
                name: sub.subject_name,
                value: sub._id,
              };
            });
        subjectList.unshift({
          name: '',
          value: '',
        });
        selectedSubject = subjectList[0].value;
      }

      this.setState({
        subjectList: subjectList,
        selectedSubject: selectedSubject,
        isLoading: false,
        addSubject: [],
      });
    } else if (name === 'division') {
      this.setState({
        selectedDivision: e.target.value,
        isLoading: true,
      });
      axios
        .get(`/department_division/subject/${e.target.value}`)
        .then((res) => {
          const subjectList = res.data.data[0].subject.map((sub) => {
            return {
              name: sub.title,
              value: sub._id,
            };
          });

          subjectList.unshift({
            name: '',
            value: '',
          });
          this.setState({
            subjectList: subjectList,
            selectedSubject: subjectList[0].value,
            isLoading: false,
            addSubject: [],
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'subject') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedSubject: e.target.value,
        subjectAddName: label,
        subjectListError: '',
      });
    }
  };

  validation = (e, name) => {
    let programError = '';
    let departmentError = '';
    if (this.state.selectedProgram === '') {
      programError = t('user_management.program_field_required');
    }

    if (this.state.selectedDepartment === '') {
      departmentError = t('user_management.department_field_required');
    }
    // if (this.state.selectedSubject === "") {
    //   subjectListError = "Subject Field Is required";
    // }

    if (
      programError ||
      departmentError
      // subjectListError
    ) {
      this.setState({
        programError,
        departmentError,
        // subjectListError,
      });

      return false;
    }
    return true;
  };

  subjectValidation = (e, name) => {
    let subjectListError = '';

    if (this.state.selectedSubject === '') {
      subjectListError = 'Subject Field Is required';
    }

    if (subjectListError) {
      this.setState({
        subjectListError,
      });

      return false;
    }
    return true;
  };

  handleAddSubject = (e) => {
    e.preventDefault();
    if (!this.subjectValidation()) return true;
    let addSubject = this.state.addSubject;

    if (!(addSubject.filter((e) => e.subjectName === this.state.subjectAddName).length > 0)) {
      addSubject.push({
        subjectView: this.state.selectedSubject,
        subjectName: this.state.subjectAddName,
      });
    } else {
      NotificationManager.error(t('user_management.Subject_already_added'));
    }

    this.setState({
      addSubject,
      subjectName: '',
      primarySub: true,
      selectedSubject: this.state.subjectList[0].value,
    });
  };

  subjectValidation1 = (e, name) => {
    let subjectList1Error = '';

    if (this.state.selectedSubject1 === '') {
      subjectList1Error = 'Subject Field Is required';
    }

    if (subjectList1Error) {
      this.setState({
        subjectList1Error,
      });

      return false;
    }
    return true;
  };

  handleAuxAddSubject = (e) => {
    e.preventDefault();
    if (!this.subjectValidation1()) return true;
    let addSubject1 = this.state.addSubject1;

    if (!(addSubject1.filter((e) => e.auxSubjectName === this.state.subjectAddName1).length > 0)) {
      addSubject1.push({
        auxSubjectView: this.state.selectedSubject1,
        auxSubjectName: this.state.subjectAddName1,
      });
    } else {
      NotificationManager.error(t('user_management.Subject_already_added'));
    }

    this.setState({
      addSubject1,
      // selectedSubject1:'',
      // subjectAddName1:''
      selectedSubject1: this.state.subjectList1[0].value,
    });
  };

  handleAuxSelect = (e, name) => {
    e.preventDefault();
    if (name === 'program1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;

      let selectedProgramIdAux;
      for (let i = 0; i < this.state.programList.length; i++) {
        if (this.state.programList[i].value === e.target.value) {
          selectedProgramIdAux = this.state.programList[i].id;
        }
      }
      this.setState({
        selectedProgram1: e.target.value,
        selectedProgramIdAux: selectedProgramIdAux,
        program1Error: '',
        auxProgramName: label,
        isLoading: true,
      });

      axios
        .get(`/digi_department_subject/${e.target.value}?limit=100&pageNo=1`)
        .then((res) => {
          const departmentList1 = res.data.data.map((data) => {
            return {
              name: data.department_name,
              value: data._id,
              subject: data.subject,
            };
          });
          departmentList1.unshift({
            name: '',
            value: '',
          });
          this.setState({
            departmentList1: departmentList1,
            depart1: departmentList1[0].value,
            addSubject1: [],
            isLoading: false,
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'department1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedDepartment1: e.target.value,
        department1Error: '',
        auxDepartmentName: label,
        isLoading: true,
      });

      let subjectList1 = [];
      let selectedSubject1 = '';
      if (e.target.value !== '') {
        subjectList1 =
          this.state.departmentList1 &&
          this.state.departmentList1.length > 0 &&
          this.state.departmentList1
            .filter((item) => item.value === e.target.value)
            .reduce((_, el) => el.subject, [])
            .filter((item) => item.isActive === true && item.isDeleted === false)
            .map((sub) => {
              return {
                name: sub.subject_name,
                value: sub._id,
              };
            });
        subjectList1.unshift({
          name: '',
          value: '',
        });
        selectedSubject1 = subjectList1[0].value;
      }

      this.setState({
        subjectList1: subjectList1,
        selectedSubject1: selectedSubject1,
        isLoading: false,
        addSubject1: [],
      });
    } else if (name === 'division1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedDivision1: e.target.value,
        auxDivisionName: label,
        isLoading: true,
      });
      axios
        .get(`/department_division/subject/${e.target.value}`)
        .then((res) => {
          const subjectList1 = res.data.data[0].subject.map((sub) => {
            return {
              name: sub.title,
              value: sub._id,
            };
          });

          subjectList1.unshift({
            name: '',
            value: '',
          });

          this.setState({
            subjectList1: subjectList1,
            selectedSubject1: subjectList1[0].value,
            isLoading: false,
            addSubject1: [],
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    } else if (name === 'subject1') {
      let index = e.nativeEvent.target.selectedIndex;
      let label = e.nativeEvent.target[index].text;
      this.setState({
        selectedSubject1: e.target.value,
        subjectAddName1: label,
        subjectList1Error: '',
      });
    }
  };

  handleCheckStaffType = (event, index) => {
    let data = this.state.staffType;
    data[index].isChecked = event.target.checked;
    this.setState({ staffType: data });
  };

  getAuxSubject = () => {
    const { subjectList1, addSubject } = this.state;

    let addSubjectIds = [];
    if (addSubject && addSubject.length > 0) {
      addSubjectIds = addSubject.map((item) => item.subjectView);
    }
    const value =
      subjectList1 &&
      subjectList1.length > 0 &&
      subjectList1.filter((item) => !addSubjectIds.includes(item.value));
    return value && value.length > 0 ? value : subjectList1;
  };

  handleAuxFullSubmit = (e) => {
    // if (!this.auxValidation()) return true;
    const { auxProgramName, auxDepartmentName, auxDivisionName, subjectAddName1 } = this.state;
    let addMoreAuxiallary = this.state.addMoreAuxiallary;

    let mappingSubject = this.state.addSubject1.map((su) => {
      return su.auxSubjectView;
    });

    let mappingSubjectName = this.state.addSubject1.map((sus) => {
      return sus.auxSubjectName;
    });
    if (
      !(
        addMoreAuxiallary.filter(
          (e) =>
            e.auxProgramName === auxProgramName &&
            e.auxDepartmentName === auxDepartmentName &&
            e.auxDivisionName === auxDivisionName &&
            e.subjectName.includes(subjectAddName1)
        ).length > 0
      )
      // &&
      // !(addMoreAuxiallary.filter((e) => e.auxSubjectName === subjectAddName1).length > 0)
    ) {
      addMoreAuxiallary.push({
        allocation_type: 'auxiliary',
        _program_id: this.state.selectedProgramIdAux,
        auxProgramName: auxProgramName,

        _department_id: this.state.selectedDepartment1,
        auxDepartmentName: auxDepartmentName,

        _department_division_id: this.state.selectedDivision1,
        auxDivisionName: auxDivisionName,

        subjectName: mappingSubjectName,
        _department_subject_id: mappingSubject,
      });
    } else {
      NotificationManager.error(t('user_management.Subject_already_added'));
    }

    this.setState({
      addMoreAuxiallary,
      // addAuxiallary:addMoreAuxiallary,
      addSubject1: [],
      selectedProgram1: '',
      auxProgramName: '',
      selectedDepartment1: '',
      auxDepartmentName: '',
      selectedDivision1: '',
      auxDivisionName: '',
      mappingSubjectName: '',
      subject: '',
      selectedSubject1: '',
      subjectAddName1: '',
      auxValied: false,
      // checkboxHide: true,
    });
  };

  auxValidation = (e, name) => {
    let program1Error = '';
    let department1Error = '';
    let subjectList1Error = '';

    if (this.state.selectedProgram1 === '') {
      program1Error = t('user_management.program_field_required');
    }

    if (this.state.selectedDepartment1 === '') {
      department1Error = t('user_management.department_field_required');
    }
    if (this.state.selectedSubject1 === '') {
      subjectList1Error = t('user_management.subject_field_required');
    }

    if (program1Error || department1Error || subjectList1Error) {
      this.setState({
        program1Error,
        department1Error,
        subjectList1Error,
      });

      return false;
    }
    return true;
  };

  handleSubmit = (e, isAcademicEnabled, isAdministrativeEnabled) => {
    e.preventDefault();
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');

    if ((this.validation() && isAcademicEnabled) || isAdministrativeEnabled) {
      let mappingValue =
        this.state.addSubject &&
        this.state.addSubject.length > 0 &&
        this.state.addSubject.map((dd) => {
          return dd.subjectView;
        });
      this.setState({
        isLoading: true,
      });

      let primaryType = {
        allocation_type: 'primary',
        _program_id: this.state.selectedProgramId,
        _department_id: this.state.selectedDepartment,
        _department_division_id: this.state.selectedDivision,
        _department_subject_id: mappingValue,
      };

      let auxType = this.state.addMoreAuxiallary.map((aux) => {
        return {
          allocation_type: aux.allocation_type,
          _program_id: aux._program_id,
          _department_id: aux._department_id,
          _department_division_id: aux._department_division_id,
          _department_subject_id: aux._department_subject_id,
        };
      });

      let fullData;

      let userType = this.state.staffType.filter((item) => item.isChecked === true);
      let userTypeValue = '';
      if (userType.length === 2) {
        userTypeValue = 'both';
      } else {
        userTypeValue = userType[0].value;
      }

      if (this.state.showAux === true || auxType) {
        fullData = {
          id: id,
          data:
            userTypeValue === 'both' || userTypeValue === 'academic'
              ? [...auxType, primaryType]
              : [],
          user_type: userTypeValue,
          institution_role: this.state.institution_role,
        };
      } else {
        fullData = {
          id: id,
          data: userTypeValue === 'both' || userTypeValue === 'academic' ? [primaryType] : [],
          user_type: userTypeValue,
          institution_role: this.state.institution_role,
        };
      }

      //  return;

      // LocalStorageService.setCustomToken(
      //   `emp${this.props.location.search}`,
      //   JSON.stringify(this.state)
      // );

      axios
        .post(`/user/user_academic_allocation`, fullData)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              employementDisable: false,
              addSubject1: [],
              auxDepartmentName: '',
              auxProgramName: '',
              subjectList1: [],
              departmentList1: [],
              // addMoreAuxiallary: [],
              // addAuxiallary: [],
              // addSubject1: [],
              // addSubject: [],
              // selectedProgram: '',
              // selectedDepartment: '',
              // selectedDivision: '',
              // selectedSubject: '',
              // departmentList: [],
              // departmentList1: [],
              // defaultDepartmentList: [],
              // programError: '',
              // department1Error: '',
              subjectListError: '',
              subjectList1Error: '',
            },
            () => {
              this.fetchApi();
            }
          );
          NotificationManager.success(t('user_management.Academic_Details_Entered_Successfully'));

          //this.props.history.push(`/staff/management/employment${this.props.location.search}`)
        })
        .catch((error) => {
          NotificationManager.error(`${error?.response?.data?.message}`);

          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  handleAux = () => {
    this.setState({
      showAux: !this.state.showAux,
      auxValied: !this.state.auxValied,
    });
  };

  removePrimarySubject = (index) => {
    let addSubject = this.state.addSubject;
    addSubject.splice(index, 1);
    this.setState({
      addSubject,
    });
  };

  removeAuxiliarySubject = (index) => {
    let addSubject1 = this.state.addSubject1;
    addSubject1.splice(index, 1);
    this.setState({
      addSubject1,
    });
  };

  removeAuxiliary = (index) => {
    let addMoreAuxiallary = this.state.addMoreAuxiallary;
    addMoreAuxiallary.splice(index, 1);
    this.setState({
      addMoreAuxiallary,
    });
  };

  handleGoBack = () => {
    if (this.state.userStatus === 'completed') {
      this.props.history.push({
        pathname: '/staff/management',
      });
    } else {
      this.props.history.push({
        pathname: '/staff/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
          selectedTab: this.state.tabs !== '' ? parseInt(this.state.tabs) : 0,
        },
      });
    }
  };

  render() {
    const ACADEMIC_DESIGNATION_TRANS = ACADEMIC_DESIGNATION.map((item) => ({
      ...item,
      name: t(`user_management.${item.name}`),
    }));
    const { loggedInUserData } = this.props;
    const { tabs } = this.state;
    let isAcademicEnabled = false;
    let isAdministrativeEnabled = false;
    if (this.state.staffType) {
      let academicCheck = this.state.staffType.filter(
        (item) => item.isChecked === true && item.value === 'academic'
      );
      if (academicCheck && academicCheck.length > 0) {
        isAcademicEnabled = true;
      }
      let administrativeCheck = this.state.staffType.filter(
        (item) => item.isChecked === true && item.value === 'administration'
      );
      if (administrativeCheck && administrativeCheck.length > 0) {
        isAdministrativeEnabled = true;
      }
    }
    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';
    return (
      <React.Fragment>
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'management_staff_profile'}></Trans>
        </div>
        <Loader isLoading={this.state.isLoading} />
        <NotificationContainer />
        <FlexWrapper className="nav_bg">
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Profile View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Profile View'
            )) && (
            <Link exact to={`/staff/management/profile${this.props.location.search}`}>
              <NavButton
                className={this.props.location.pathname === '/staff/management/profile' && 'active'}
                color="white"
              >
                <Trans i18nKey={'user_management.profile'}></Trans>
              </NavButton>
            </Link>
          )}
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Academic View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Academic View'
            )) && (
            <Link exact to={`/staff/management/academic${this.props.location.search}`}>
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/academic' && 'active'
                }
                color="white"
              >
                <Trans i18nKey={'user_management.academic'}></Trans>
              </NavButton>
            </Link>
          )}
          {(CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            permissionName,
            'Employment View'
          ) ||
            CheckPermission(
              'subTabs',
              'User Management',
              'Staff Management',
              '',
              'Registration Pending',
              '',
              permissionName,
              'Employment View'
            )) && (
            <Link
              exact
              to={
                this.state.academic_check
                  ? `/staff/management/employment${this.props.location.search}`
                  : `/staff/management/academic${this.props.location.search}`
              }
              // to={((isAcademicEnabled && this.state.addSubject && this.state.addSubject.length > 0 && this.state.user_type!=="") || (isAdministrativeEnabled && !isAcademicEnabled)) ? `/staff/management/employment${this.props.location.search}` : `/staff/management/academic${this.props.location.search}` }
            >
              <NavButton
                className={
                  this.props.location.pathname === '/staff/management/employment' && 'active'
                }
                color="white"
                // disabled={!((isAcademicEnabled && this.state.addSubject && this.state.addSubject.length > 0 && this.state.user_type!=="") || (isAdministrativeEnabled && !isAcademicEnabled))}

                disabled={!this.state.academic_check}
              >
                <Trans i18nKey={'user_management.employment'}></Trans>
              </NavButton>
            </Link>
          )}
          {loggedInUserData.get('staffFacial', false) &&
            (CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Biometric View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Biometric View'
              )) && (
              <Link
                exact
                to={
                  this.state.employment_check
                    ? `/staff/management/biometric${this.props.location.search}`
                    : `/staff/management/academic${this.props.location.search}`
                }
              >
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/biometric' && 'active'
                  }
                  color="white"
                  disabled={!this.state.employment_check}
                >
                  <Trans i18nKey={'user_management.biometric'}></Trans>
                </NavButton>
              </Link>
            )}
        </FlexWrapper>
        <div className="main p-2">
          <div className="container">
            <React.Fragment>
              <div className="float-right">
                <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                  <Trans i18nKey={'back'}></Trans>
                </Button>
                {(CheckPermission(
                  'tabs',
                  'User Management',
                  'Staff Management',
                  '',
                  permissionName,
                  'Academic Edit'
                ) ||
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Staff Management',
                    '',
                    'Registration Pending',
                    '',
                    permissionName,
                    'Academic Edit'
                  )) && (
                  <Button
                    className="m-2"
                    onClick={(e) =>
                      this.handleSubmit(e, isAcademicEnabled, isAdministrativeEnabled)
                    }
                    disabled={
                      !(
                        (isAcademicEnabled &&
                          this.state.addSubject &&
                          this.state.addSubject.length > 0) ||
                        (isAdministrativeEnabled && !isAcademicEnabled)
                      )
                    }
                  >
                    <Trans i18nKey={'save'}></Trans>
                  </Button>
                )}

                {/* {this.state.auxValied === true ||
              this.state.primarySub === false ? ( */}
                {/* {this.state.addSubject.length === 0 ||
                  this.state.auxValied === true ? (
                    <Button className="m-2" disabled>
                      Save
                    </Button>
                  ) : (
                    <Button className="m-2" onClick={this.handleSubmit}>
                      Save
                    </Button>
                  )} */}
              </div>

              <div className="clearfix"></div>
              <div className="white p-4 mb-5">
                <div className="row">
                  <div className="col-md-12 mb-4">
                    <div className="d-flex">
                      {this.state.staffType.map((data, index) => {
                        return (
                          <span className="mr-4" key={index}>
                            <input
                              type="checkbox"
                              className="calendarFormRadio"
                              onClick={(event) => this.handleCheckStaffType(event, index)}
                              checked={data.isChecked}
                            />{' '}
                            {data.name}
                          </span>
                        );
                      })}
                    </div>

                    {isAcademicEnabled && (
                      <div className="mt-3 mb-2 col-md-3" style={{ paddingLeft: '0px' }}>
                        <Input
                          elementType={'floatingselect'}
                          elementConfig={{
                            options: ACADEMIC_DESIGNATION_TRANS,
                          }}
                          value={this.state.institution_role}
                          floatingLabel={
                            <Trans i18nKey={'user_management.academic_designation'}></Trans>
                          }
                          changed={(e) => this.handleSelect(e, 'institution_role')}
                        />
                      </div>
                    )}
                  </div>
                  {isAcademicEnabled && (
                    <>
                      <div className="col-md-4" style={{ textAlign: 'left' }}>
                        <div className="img_border">
                          <div className="col-md-12">
                            <h4 className="text-red f-16">
                              <Trans i18nKey={'user_management.primary'}></Trans> *
                            </h4>
                          </div>
                          <div className="col-md-12 ">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.programList,
                              }}
                              value={this.state.selectedProgram}
                              floatingLabel={
                                <Trans i18nKey={'user_management.select_program'}></Trans>
                              }
                              changed={(e) => this.handleSelect(e, 'program')}
                              feedback={this.state.programError}
                            />
                          </div>

                          <div className="col-md-12  pt-2">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.departmentList,
                              }}
                              value={this.state.selectedDepartment}
                              floatingLabel={
                                <Trans i18nKey={'user_management.select_department'}></Trans>
                              }
                              changed={(e) => this.handleSelect(e, 'department')}
                              feedback={this.state.departmentError}
                            />
                          </div>

                          <div className="col-md-12  pt-2">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.divisionList,
                              }}
                              value={this.state.selectedDivision}
                              floatingLabel={
                                <Trans i18nKey={'user_management.select_division'}></Trans>
                              }
                              changed={(e) => this.handleSelect(e, 'division')}
                              feedback={this.state.divisionError}
                            />
                          </div>

                          <div className="col-md-12  pt-2">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.subjectList,
                              }}
                              value={this.state.selectedSubject}
                              floatingLabel={
                                <Trans i18nKey={'user_management.select_subject'}></Trans>
                              }
                              changed={(e) => this.handleSelect(e, 'subject')}
                              feedback={this.state.subjectListError}
                            />
                          </div>

                          <div className="col-md-12 pt-4">
                            <p className="remove_hover" onClick={this.handleAddSubject}>
                              <i className="fa fa-plus-circle" aria-hidden="true"></i>{' '}
                              <Trans i18nKey={'add_save'}></Trans>{' '}
                              <Trans i18nKey={'configuration.subject'}></Trans>{' '}
                            </p>
                          </div>

                          {this.state.addSubject.map((su, index) => (
                            <>
                              <div className="col-md-12">
                                <div className="row">
                                  <div className="col-md-10  pt-2">
                                    <Input
                                      elementType={'floatinginput'}
                                      elementConfig={{
                                        type: 'text',
                                      }}
                                      disabled={'disabled'}
                                      value={su.subjectName}
                                      floatingLabel={
                                        <Trans i18nKey={'user_management.add_subject_name'}></Trans>
                                      }
                                    />
                                  </div>
                                  <div className="col-md-2  mt-35">
                                    <i
                                      className="fa fa-times-circle remove_hover"
                                      onClick={() => this.removePrimarySubject(index)}
                                    ></i>
                                  </div>
                                </div>
                              </div>
                            </>
                          ))}
                        </div>
                      </div>

                      <div className={`col-md-4`}>
                        <div className={`img_border`}>
                          <div
                            className={`row ${this.state.checkboxHide === true && 'hide_content'}`}
                          >
                            <div className="col-xl-10">
                              <h4 className="text-red f-16">
                                {' '}
                                <Trans i18nKey={'user_management.auxiliary'}></Trans>{' '}
                              </h4>
                            </div>
                            <div className={`col-xl-2`}>
                              <label
                                className={`switch ${getLang() === 'ar' ? 'float-right' : ''}`}
                              >
                                {this.state.primarySub === false ? (
                                  <input type="checkbox" disabled />
                                ) : (
                                  <input type="checkbox" onClick={this.handleAux} />
                                )}

                                <span className="slider_check round"></span>
                              </label>
                            </div>
                          </div>

                          <div className={`${this.state.showAux === false && 'hide_content'}`}>
                            <div className="col-md-12 ">
                              <Input
                                elementType={'floatingselect'}
                                elementConfig={{
                                  options: this.state.programList,
                                }}
                                value={this.state.selectedProgram1}
                                floatingLabel={
                                  <Trans i18nKey={'user_management.select_program'}></Trans>
                                }
                                changed={(e) => this.handleAuxSelect(e, 'program1')}
                                feedback={this.state.program1Error}
                              />
                            </div>

                            <div className="col-md-12  pt-2">
                              <Input
                                elementType={'floatingselect'}
                                elementConfig={{
                                  options: this.state.departmentList1,
                                }}
                                value={this.state.selectedDepartment1}
                                floatingLabel={
                                  <Trans i18nKey={'user_management.select_department'}></Trans>
                                }
                                changed={(e) => this.handleAuxSelect(e, 'department1')}
                                feedback={this.state.department1Error}
                              />
                            </div>

                            <div className="col-md-12  pt-2">
                              <Input
                                elementType={'floatingselect'}
                                elementConfig={{
                                  options: this.state.divisionList1,
                                }}
                                value={this.state.selectedDivision1}
                                floatingLabel={
                                  <Trans i18nKey={'user_management.select_division'}></Trans>
                                }
                                changed={(e) => this.handleAuxSelect(e, 'division1')}
                                feedback={this.state.division1Error}
                              />
                            </div>

                            <div className="col-md-12  pt-2">
                              <Input
                                elementType={'floatingselect'}
                                elementConfig={{
                                  options: this.getAuxSubject(),
                                }}
                                value={this.state.selectedSubject1}
                                floatingLabel={
                                  <Trans i18nKey={'user_management.select_subject'}></Trans>
                                }
                                changed={(e) => this.handleAuxSelect(e, 'subject1')}
                                feedback={this.state.subjectList1Error}
                              />
                            </div>

                            <div className="col-md-12 pt-4">
                              <p className="remove_hover" onClick={this.handleAuxAddSubject}>
                                <span className="pt-1">
                                  <i className="fa fa-plus-circle mr-2" aria-hidden="true"></i>
                                  <Trans i18nKey={'user_management.add_more_subject'}></Trans>
                                </span>
                              </p>
                            </div>
                            {this.state.addSubject1.map((su1, index) => (
                              <>
                                <div className="col-md-12">
                                  <div className="row">
                                    <div className="col-md-10  pt-2">
                                      <Input
                                        elementType={'floatinginput'}
                                        elementConfig={{
                                          type: 'text',
                                        }}
                                        disabled={'disabled'}
                                        value={su1.auxSubjectName}
                                        floatingLabel={
                                          <Trans
                                            i18nKey={'user_management.add_subject_name'}
                                          ></Trans>
                                        }
                                      />
                                    </div>
                                    <div className="col-md-2  mt-35">
                                      <i
                                        className="fa fa-times-circle remove_hover"
                                        onClick={() => this.removeAuxiliarySubject(index)}
                                      ></i>
                                    </div>
                                  </div>
                                </div>
                              </>
                            ))}

                            <div className="col-md-12">
                              <Button
                                style={{ width: '100%' }}
                                onClick={this.handleAuxFullSubmit}
                                disabled={
                                  !(
                                    this.state.addSubject1.length > 0 &&
                                    this.state.auxDepartmentName !== '' &&
                                    this.state.auxProgramName !== ''
                                  )
                                }
                              >
                                {' '}
                                <Trans i18nKey={'user_management.add_auxiliary'}></Trans>
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-md-4">
                        {this.state.addMoreAuxiallary &&
                          this.state.addMoreAuxiallary.length > 0 &&
                          this.state.addMoreAuxiallary.map((sue, index) => (
                            <React.Fragment key={index}>
                              <div className="img_border">
                                <div className="col-md-12 ">
                                  <span className="text-blue f-16">
                                    {' '}
                                    <Trans i18nKey={'user_management.auxiliary'}></Trans>{' '}
                                  </span>
                                  <span className="float-right">
                                    <i
                                      className="fa fa-times-circle"
                                      onClick={() => this.removeAuxiliary(index)}
                                    ></i>
                                  </span>
                                </div>
                                <ProfileText
                                  title={<Trans i18nKey={'program_name'}></Trans>}
                                  value={sue.auxProgramName}
                                />
                                <ProfileText
                                  title={
                                    <Trans i18nKey={'user_management.department_name'}></Trans>
                                  }
                                  value={sue.auxDepartmentName}
                                />

                                <ProfileText
                                  title={<Trans i18nKey={'division_name'}></Trans>}
                                  value={sue.auxDivisionName}
                                />

                                <ProfileText
                                  title={<Trans i18nKey={'configuration.subject_name'}></Trans>}
                                  value={sue.subjectName.map((yy, index) => (
                                    <React.Fragment key={index}>
                                      {' '}
                                      {yy} <br />{' '}
                                    </React.Fragment>
                                  ))}
                                />
                              </div>
                              <br />
                              <br />
                            </React.Fragment>
                          ))}
                      </div>
                    </>
                  )}
                </div>
              </div>
            </React.Fragment>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

ValideAcademic.propTypes = {
  publishedProgramLists: PropTypes.array,
  history: PropTypes.object,
  location: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    publishedProgramLists: selectAllProgramLists(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(React.memo(ValideAcademic)));
