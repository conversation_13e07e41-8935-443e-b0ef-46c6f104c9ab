import { makeStyles } from '@mui/styles';

export const useStylesFunction = makeStyles(() => ({
  accordionBorderUnset: {
    boxShadow: 'none',
    borderBottom: '1px solid #E5E7EB',
    borderRadius: 'unset !important',
    background: 'none',
  },
  accordionBackgroundNone: {
    boxShadow: 'none',
    borderBottom: 'none',
    borderRadius: 'unset !important',
    background: 'none',
  },
  accordionArrowPosition: {
    '&.MuiButtonBase-root': {
      padding: '0px',
      flexDirection: 'row-reverse',
      marginLeft: '-16px',
    },
  },
  indicator: {
    backgroundColor: '#147AFC',
  },
  green: {
    color: '#4ADE80 !important',
    '&$checked': {
      color: '#4ADE80 !important',
    },
  },
}));
