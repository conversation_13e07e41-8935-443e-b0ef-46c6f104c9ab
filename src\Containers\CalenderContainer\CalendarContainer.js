import React, { Component, Suspense } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { But<PERSON> } from 'react-bootstrap';
import { connect } from 'react-redux';
import { registerLocale } from 'react-datepicker';
import jsPDF from 'jspdf';
import moment from 'moment';
import * as dateFns from 'date-fns';
import ar from 'date-fns/locale/ar';
import * as actions from '../../_reduxapi/institution/actions';
import { Trans, withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { t } from 'i18next';
import CalenderIndex from './index';
import './CalendarContainer.css';
import Loader from '../../Widgets/Loader/Loader';
import {
  getDayName,
  timeFormat,
  jsUcfirstAll,
  getHijriDateFormat,
  getHijriDayFormat,
  getFormattedHijiriYear,
  getLang,
  showArabicMailShow,
  getEnvCollegeName,
  getEnvInstitutionId,
  formatFullName,
  eString,
} from '../../utils';
// import Input from '../../Widgets/FormElements/Input/Input';
import axios from '../../axios';
import MultiPage from './Multipage';
import MonthlyCalender from './MonthlyCalendar';
//import Logo from '../../Assets/logo-pdf.png';
import ArabicFond from '../../Assets/fonts/Amiri-Regular.ttf';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { selectUserInfo } from '../../_reduxapi/Common/Selectors';
import i18n from '../../i18n';
import withCalendarHooks from 'Hoc/withCalendarHooks';
const AddEditEventModal = React.lazy(() => import('./Modals/AddEditEventModal'));
const CreateInstitutionCalendar = React.lazy(() => import('./Modals/CreateInstitutionCalendar'));
const PublishInstitutionCalendar = React.lazy(() => import('./Modals/PublishInstitutionCalendar'));
const EventDetailModal = React.lazy(() => import('./Modals/EventDetailModal'));
const DeleteEventModal = React.lazy(() => import('./Modals/DeleteEventModal'));

registerLocale('ar', ar);

const lang = getLang();

const input = [
  ['Gregorian', 'gregorian'],
  // ["Hijri", "hijri"],
];
const genderinput = [
  ['Male Only', 'male'],
  ['Female Only', 'female'],
];
let event_gender;
let event_whom = 'both';
class CalenderContainerComponent extends Component {
  constructor(props) {
    super(props);
    const { t } = this.props;
    this.state = {
      data: [],
      selectgender: genderinput[0][1][2],
      chooseStaff: false,
      chooseStudent: false,
      choosegender: false,
      isLoading: false,
      eventShow: false,
      eventView: false,
      eventDelete: false,
      eventEdit: false,
      startTime: new Date(),
      endTime: new Date(),
      editStartTime: '',
      editEndTime: '',
      setStartDate: '',
      eventType: [
        { name: '', value: '' },
        { name: t('events.event_types.holiday'), value: 'holiday' },
        { name: t('events.event_types.exam'), value: 'exam' },
        { name: t('events.event_types.training'), value: 'training' },
        { name: t('events.event_types.orientation'), value: 'orientation' },
        { name: t('events.event_types.general'), value: 'general' },
      ],
      venue: [
        { name: '', value: '' },
        { name: t('events.venue.madurai'), value: '5ea93af58d49c51140fcf63e' },
        { name: t('events.venue.chennai'), value: '5ea93af58d49c51140fcf64e' },
      ],
      oneDay: false,
      valueCheck: false,
      eventName: '',
      eventDetail: '',
      arab1: '',
      arab2: '',
      selectType: '',
      startDate: '',
      endDate: '',
      selectVenue: '',
      evId: '',
      evType: '',
      evName: '',
      evArab1: '',
      evDetail: '',
      evArab2: '',
      evStartDate: '',
      evEndDate: '',
      evStartTime: '',
      evEndTime: '',
      startTimeView: '',
      endTimeView: '',
      selectedRadioButton: input[0][1],
      currentMonth: new Date(),
      selectedDate: new Date(),
      endMonth: new Date(),
      selectedEndDate: new Date(),
      startDateShow: false,
      endDateShow: false,
      show: false,
      calendarData: [],
      index: 0,
      timeError: '',
      completeEmail: '',
      completeSms: '',
      completeScheduler: '',
      completeClass: '',
      completeError: '',
      publishStaff: '',
      publishStudent: '',
      prevBtnDisabled: true,
      nextBtnDisabled: false,
      calendarN: '',
      calendarId: null,
      wdNames: [
        i18n.t('iweeddays.ahad'),
        i18n.t('iweeddays.ithnin'),
        i18n.t('iweeddays.thulatha'),
        i18n.t('iweeddays.arbaa'),
        i18n.t('iweeddays.khams'),
        i18n.t('iweeddays.jumuah'),
        i18n.t('iweeddays.sabt'),
      ],
      iMonthNames: [
        i18n.t('imonths.muharram'),
        i18n.t('imonths.safar'),
        i18n.t('imonths.rabi_ul_awwal'),
        i18n.t('imonths.rabi_ul_akhir'),
        i18n.t('imonths.jumadal_ula'),
        i18n.t('imonths.jumadal_akhira'),
        i18n.t('imonths.rajab'),
        i18n.t('imonths.sha_ban'),
        i18n.t('imonths.ramadan'),
        i18n.t('imonths.shawwal'),
        i18n.t('imonths.dhul_qa_ada'),
        i18n.t('imonths.dhul_hijja'),
      ],
    };
  }

  componentDidMount() {
    this.fetchApi(0);
  }

  async fetchApi(index) {
    const { loggedInUserData } = this.props;
    let calendarId = null;
    this.setState({
      isLoading: true,
    });
    await axios
      .get(`institution_calendar?limit=100&pageNo=1`)
      .then((res) => {
        const data = res.data.data.map((data) => {
          return {
            id: data._id,
            calendar_name: data.calendar_name,
            calendar_type: data.calendar_type,
            start_date: new Date(data.start_date),
            end_date: new Date(data.end_date),
            status: data.status,
            isActive: data.isActive,
          };
        });
        calendarId = res.data.data[index]._id;
        this.setState({
          calendarData: data,
          calendarId: res.data.data[index]._id,
          calendarN: res.data.data[index].calendar_name,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });

    if (calendarId !== null) {
      let url = `institution_calendar_event/list_calendar/${calendarId}`;
      const subRole = loggedInUserData.get('sub_role', List());
      if (
        CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Add Event') ||
        (subRole && subRole.size > 0 && subRole.indexOf('Institution_Calendar_Reviewer') !== -1)
      ) {
        url = `institution_calendar_event/list_event/${calendarId}`;
      }
      this.setState({
        data: [],
        isLoading: true,
      });
      await axios
        .get(url)
        .then((res) => {
          let data = [];
          if (res.data.data && res.data.data.length > 0) {
            data = res.data.data.map((item) => {
              return { showElem: true, ...item };
            });
          }
          this.setState({
            data: data,
            isLoading: false,
          });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    }
  }
  restrictCalendarSwitch = (index) => {
    let calendarLength = this.state.calendarData.length;
    if (parseInt(index) === 0) {
      this.setState({ prevBtnDisabled: true, nextBtnDisabled: false });
    } else if (index === calendarLength - 1) {
      this.setState({ prevBtnDisabled: false, nextBtnDisabled: true });
    } else {
      this.setState({ prevBtnDisabled: false, nextBtnDisabled: false });
    }
  };
  toggleNext = (e) => {
    let index = (this.state.index + 1) % this.state.calendarData.length;
    this.restrictCalendarSwitch(index);
    this.fetchApi(index);
    this.setState({ index: index });
  };
  togglePrev = () => {
    let index = this.state.index;
    if (index === 0) {
      index = this.state.calendarData.length;
    }
    index = index - 1;
    this.restrictCalendarSwitch(index);
    this.fetchApi(index);
    this.setState({ index: index });
  };
  getMonths = (fromDate, toDate) => {
    const fromYear = fromDate.getFullYear();
    const toYear = toDate.getFullYear();
    var startDate = moment(fromDate).format('YYYY-MM-DD');
    var endDate = moment(toDate).format('YYYY-MM-DD');
    var start = startDate.split('-');
    var end = endDate.split('-');
    var dates = [];
    for (var i = fromYear; i <= toYear; i++) {
      var endMonth = i !== toYear ? 11 : parseInt(end[1]) - 1;
      var startMon = i === fromYear ? parseInt(start[1]) - 1 : 0;
      for (var j = startMon; j <= endMonth; j = j > 12 ? j % 12 || 11 : j + 1) {
        var month = j + 1;
        var displayMonth = month < 10 ? '0' + month : month;
        dates.push([i, displayMonth, '01'].join('-'));
      }
    }
    return dates;
  };
  getMonthlyCalendar = (months, eventShow, academicYearStartDate, academicYearEndDate) => {
    let m = months.map((m, i) => {
      let monthFor = m.split('-')[1];
      let monthYear = m.split('-')[0];
      const currentMonth = new Date(`${monthYear}-${monthFor}-01`);
      return (
        <React.Fragment key={i}>
          <div className="col-md-4">
            <MonthlyCalender
              clicked={eventShow}
              currentMonth={currentMonth}
              academicYearStartDate={academicYearStartDate}
              academicYearEndDate={academicYearEndDate}
              isCurrentCalendar={this.getCalendarStatus()}
            />{' '}
          </div>
        </React.Fragment>
      );
    });
    return <div className="row">{m}</div>;
  };
  // setStartTime = (date) => {
  //   this.setState({
  //     startTime: date,
  //   });
  // };
  // setEndTime = (date) => {
  //   this.setState({
  //     endTime: date,
  //   });
  // };
  // editStartTime = (date) => {
  //   this.setState({
  //     editStartTime: date,
  //   });
  // };
  // editEndTime = (date) => {
  //   this.setState({
  //     editEndTime: date,
  //   });
  // };
  eventShow = () => {
    let eventStorageDate = localStorage.getItem('eventDate');
    let startDate = '';
    let endDate = '';
    if (eventStorageDate !== null) {
      startDate = moment(eventStorageDate).format('D MMM YYYY');
      endDate = moment(eventStorageDate).format('D MMM YYYY');
    }
    var stDate = new Date();
    stDate.setHours(8);
    stDate.setMinutes(0);
    var etDate = new Date();
    etDate.setHours(17);
    etDate.setMinutes(0);
    this.setState({
      eventShow: true,
      selectType: '',
      eventName: '',
      arab1: '',
      eventDetail: '',
      arab2: '',
      startDate: startDate,
      startTime: stDate,
      endTime: etDate,
      endDate: endDate,
      selectVenue: '',
      oneDay: false,
      valueCheck: false,
      eventEdit: false,
      eventNameError: '',
      eventDetailError: '',
      selectTypeError: '',
      startDateError: '',
      endDateError: '',
      selectVenueErro: '',
      genderError: '',
      timeError: '',
      choosegender: false,
      selectgender: '',
      startDa: startDate,
      endDa: endDate,
    });
  };
  showArabicMonth = (monthNames) => {
    return showArabicMailShow() ? (
      <p className="text-center hijriMonthName">{monthNames}</p>
    ) : (
      <></>
    );
  };
  eventClose = () => {
    localStorage.removeItem('eventDate');
    this.setState({
      eventShow: false,
    });
  };
  eventViewShow = (e, view) => {
    this.setState({
      isLoading: true,
      eventView: true,
    });
    axios
      .get(`institution_calendar_event/${view._id}`)
      .then((res) => {
        const eventSingleView = res.data.data;
        this.setState({
          evId: eventSingleView._id,
          evType: eventSingleView.event_type,
          evName: eventSingleView.event_name.first_language,
          evArab1: eventSingleView.event_name.second_language,
          evDetail: eventSingleView.event_description.first_language,
          evArab2: eventSingleView.event_description.second_language,
          evStartDate: eventSingleView.event_date,
          evEndDate: eventSingleView.end_date,
          evStartTime: eventSingleView.start_time,
          evEndTime: eventSingleView.end_time,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.props.setData({ message: error.response.data.message });
        this.setState({
          isLoading: false,
        });
      });
  };
  eventViewClose = (e) => {
    this.setState({
      eventView: false,
    });
  };
  eventDeleteConfirm = (e) => {
    this.setState({
      eventView: false,
      eventDelete: true,
    });
  };
  eventDelete = (e) => {
    this.setState({
      isLoading: true,
      eventDelete: false,
      eventView: false,
    });
    axios
      .delete(`institution_calendar_event/${this.state.evId}`)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
            evId: '',
            evType: '',
            evName: '',
            evArab1: '',
            evDetail: '',
            evArab2: '',
            evStartDate: '',
            evEndDate: '',
            evStartTime: '',
            evEndTime: '',
          },
          () => {
            this.fetchApi(this.state.index);
          }
        );
        this.props.setData({ message: t('event_deleted_successfully') });
      })
      .catch((error) => {
        this.props.setData({ message: error.response.data.message });
        this.setState({
          isLoading: false,
        });
      });
  };
  eventDeleteClose = (e) => {
    this.setState({
      eventDelete: false,
    });
  };
  eventEdit = (e, views) => {
    this.setState({
      isLoading: true,
      eventView: false,
      eventEdit: true,
      eventNameError: '',
      eventDetailError: '',
      selectTypeError: '',
      startDateError: '',
      endDateError: '',
      selectVenueErro: '',
      genderError: '',
      timeError: '',
    });
    axios
      .get(`institution_calendar_event/${this.state.evId}`)
      .then((res) => {
        const data = res.data.data;
        this.setState({
          selectType: data.event_type,
          eventName: data.event_name.first_language,
          arab1: data.event_name.second_language,
          eventDetail: data.event_description.first_language,
          arab2: data.event_description.second_language,
          startDa: data.event_date,
          endDa: data.end_date,
          startDate: moment(data.event_date).format('D MMM YYYY'),
          endDate: moment(data.end_date).format('D MMM YYYY'),
          startTimeView: data.start_time,
          endTimeView: data.end_time,
          isLoading: false,
          oneDay: moment(data.event_date).format('L') === moment(data.end_date).format('L'),
          editEndTime: '',
          editStartTime: '',
        });
      })
      .catch((error) => {
        this.props.setData({ message: `error` });
        this.setState({
          isLoading: false,
        });
      });
  };
  eventEditClose = () => {
    this.setState({
      eventEdit: false,
    });
  };
  handleCheck = (event, name) => {
    if (name === 'day') {
      this.setState({
        oneDay: event.target.checked,
        endDate: this.state.startDate,
      });
    }
    if (name === 'value') {
      this.setState({
        valueCheck: event.target.checked,
      });
    }
    if (name === 'choosegender') {
      this.setState({
        choosegender: event.target.checked,
        selectgender: 'male',
      });
    }
    if (name === 'chooseStaff') {
      this.setState({
        chooseStaff: event.target.checked,
      });
      event_whom = 'staff';
    }
    if (name === 'chooseStudent') {
      this.setState({
        chooseStudent: event.target.checked,
      });
      event_whom = 'student';
    }
  };
  handleChange = (e, name) => {
    if (name === 'eventName') {
      this.setState({
        eventName: e.target.value,
        eventNameError: '',
      });
    }
    if (name === 'eventDetail') {
      this.setState({
        eventDetail: e.target.value,
        eventDetailError: '',
      });
    }
    if (name === 'arab1') {
      this.setState({
        arab1: e.target.value,
        arab1Error: '',
      });
    }
    if (name === 'arab2') {
      this.setState({
        arab2: e.target.value,
        arab2Error: '',
      });
    }
    if (name === 'startDate') {
      this.setState({
        startDate: e.target.value,
        startDateError: '',
      });
    }
    if (name === 'endDate') {
      this.setState({
        endDate: e.target.value,
        endDateError: '',
      });
    }
    if (name === 'startTime') {
      this.setState({
        startTime: e.target.value,
        startTimeError: '',
      });
    }
  };
  handleSelect = (e, name) => {
    if (name === 'eventType') {
      this.setState({
        selectType: e.target.value,
        selectTypeError: '',
      });
    }
    if (name === 'venue') {
      this.setState({
        selectVenue: e.target.value,
        selectVenueError: '',
      });
    }
  };
  validation = (e) => {
    let eventNameError = '';
    let eventDetailError = '';
    let selectTypeError = '';
    let startDateError = '';
    let endDateError = '';
    let selectVenueError = '';
    let genderError = '';
    if (this.state.choosegender === true) {
      if (this.state.selectgender === 'l') {
        genderError = t('chooseGender');
      }
    }
    if (this.state.selectType === '') {
      selectTypeError = t('chooseEventType');
    }
    if (this.state.valueCheck === true) {
      if (this.state.selectVenue === '') {
        selectVenueError = t('chooseVenueType');
      }
    }
    if (this.state.startDate === '') {
      startDateError = t('chooseStartDate');
    } else if (new Date(this.state.startDate) > new Date(this.state.endDate)) {
      startDateError = t('endDateGreat');
    }
    if (this.state.oneDay === false) {
      if (this.state.endDate === '') {
        endDateError = t('chooseEndDate');
      }
    }
    if (!this.state.eventName) {
      eventNameError = t('eventNameRequired');
    } else if (this.state.eventName.length <= 2) {
      eventNameError = t('minimumThreeChar');
    }
    if (
      eventNameError ||
      eventDetailError ||
      selectTypeError ||
      startDateError ||
      endDateError ||
      selectVenueError ||
      genderError
    ) {
      this.setState({
        eventNameError,
        eventDetailError,
        selectTypeError,
        startDateError,
        endDateError,
        selectVenueError,
        genderError,
      });
      return false;
    }
    return true;
  };
  handleEventSubmit = (e) => {
    let id = this.state.calendarId;
    localStorage.removeItem('eventDate');
    e.preventDefault();
    const { choosegender, selectgender } = this.state;
    let startDate = moment(this.state.startDate).format('YYYY-MM-DD');
    let endDate = moment(this.state.endDate).format('YYYY-MM-DD');
    if (this.state.oneDay === true) {
      this.setState({
        endDate: this.state.startDate,
      });
      endDate = moment(this.state.startDate).format('YYYY-MM-DD');
    }
    let startTime = timeFormat(moment(this.state.startTime).format('H:mm') + ':00');
    let endTime = timeFormat(moment(this.state.endTime).format('H:mm') + ':00');
    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();
    if (!choosegender) {
      event_gender = 'both';
    } else {
      event_gender = selectgender;
    }
    let error = false;
    if (this.state.startDate !== '' && this.state.endDate !== '') {
      if (st.getTime() >= et.getTime()) {
        this.setState({ timeError: t('endTimeGreat') });
        error = true;
      } else {
        this.setState({ timeError: '' });
      }
    }
    if (this.validation() && !error) {
      const data = {
        event_calendar: 'institution',
        event_type: this.state.selectType,
        event_name: {
          first_language: this.state.eventName.trim(),
          second_language: this.state.arab1.trim(),
        },
        event_description: {
          first_language: this.state.eventDetail,
          second_language: this.state.arab2,
        },
        event_date: startDate,
        start_time: st.getTime(),
        end_time: et.getTime(),
        end_date: endDate,
        _infrastructure_id: this.state.selectVenue,
        _calendar_id: id,
        event_gender: event_gender,
        event_whom: event_whom,
      };
      this.setState({
        isLoading: true,
      });
      axios
        .post(`institution_calendar_event`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              selectType: '',
              eventName: '',
              arab1: '',
              eventDetail: '',
              arab2: '',
              startDate: '',
              startTime: new Date(),
              endTime: new Date(),
              endDate: '',
              selectVenue: '',
              oneDay: false,
              valueCheck: false,
              eventShow: false,
            },
            () => {
              this.fetchApi();
            }
          );
          this.props.setData({ message: t('event_created_successfully') });
        })
        .catch((error) => {
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };
  handleEditEventSubmit = (e) => {
    e.preventDefault();
    let startDate = moment(this.state.startDate).format('YYYY-MM-DD');
    let endDate = moment(this.state.endDate).format('YYYY-MM-DD');
    if (this.state.oneDay === true) {
      this.setState({
        endDate: this.state.startDate,
      });
      endDate = moment(this.state.startDate).format('YYYY-MM-DD');
    }
    let st = '';
    let et = '';
    if (this.state.editStartTime === '') {
      let startTime = timeFormat(moment(this.state.startTimeView).format('H:mm'));
      st = moment(startDate + 'T' + startTime).toDate();
    } else {
      let startTime = timeFormat(moment(this.state.editStartTime).format('H:mm'));
      st = moment(startDate + 'T' + startTime).toDate();
    }
    if (this.state.editEndTime === '') {
      let endTime = timeFormat(moment(this.state.endTimeView).format('H:mm'));
      et = moment(endDate + 'T' + endTime).toDate();
    } else {
      let endTime = timeFormat(moment(this.state.editEndTime).format('H:mm'));
      et = moment(endDate + 'T' + endTime).toDate();
    }
    let error = false;
    if (this.state.startDate !== '' && this.state.endDate !== '') {
      if (st.getTime() >= et.getTime()) {
        this.setState({ timeError: t('endTimeGreat') });
        error = true;
      } else {
        this.setState({ timeError: '' });
      }
    }
    let eventDetail = this.state.eventDetail;
    if (this.state.eventDetail === '') {
      eventDetail = ' ';
    }
    if (this.validation() && !error) {
      const data = {
        event_calendar: 'institution',
        event_type: this.state.selectType,
        event_name: {
          first_language: this.state.eventName.trim(),
          second_language: this.state.arab1.trim(),
        },
        event_description: {
          first_language: eventDetail,
          second_language: this.state.arab2,
        },
        event_date: startDate,
        end_date: endDate,
        start_time: st.getTime(),
        end_time: et.getTime(),
      };
      this.setState({
        isLoading: true,
      });
      axios
        .put(`institution_calendar_event/${this.state.evId}`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              selectType: '',
              eventName: '',
              arab1: '',
              eventDetail: '',
              arab2: '',
              selectVenue: '',
              oneDay: false,
              valueCheck: false,
              eventEdit: false,
            },
            () => {
              this.fetchApi();
            }
          );
          this.props.setData({ message: t('event_updated_successfully') });
        })
        .catch((error) => {
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };
  handleState = (data) => {
    this.setState(data);
  };
  listView = () => {
    const { calendarId, calendarData, index } = this.state;
    const isActive = calendarData[index].isActive;
    this.props.history.push({
      pathname: '/eventList',
      search: `?id=${eString(calendarId)}&s=${eString(isActive)}`,
    });
  };

  reviewEvent = () => {
    const { calendarId, calendarData, index, calendarN } = this.state;
    const isActive = calendarData[index].isActive;
    this.props.history.push({
      pathname: '/reviewevent',
      search: `?id=${eString(calendarId)}&name=${eString(calendarN)}&s=${eString(isActive)}`,
    });
  };

  reviewAccept = () => {
    const { calendarId, calendarData, index, calendarN } = this.state;
    const isActive = calendarData[index].isActive;
    this.props.history.push({
      pathname: '/reviewaccept',
      search: `?id=${eString(calendarId)}&name=${eString(calendarN)}&s=${eString(isActive)}`,
    });
  };

  getHijriHtml = (day, monthStart, selectedDate) => {
    return showArabicMailShow() ? (
      <p
        className={`hijriNumber ${
          !dateFns.isSameMonth(day, monthStart)
            ? 'disabled'
            : dateFns.isSameDay(day, selectedDate)
            ? 'selected'
            : ''
        }`}
      >
        {this.getHijriDayNumber(day)}
      </p>
    ) : (
      <></>
    );
  };
  renderCells() {
    const { currentMonth, selectedDate } = this.state;
    const monthStart = dateFns.startOfMonth(currentMonth);
    const monthEnd = dateFns.endOfMonth(monthStart);
    const startDate = dateFns.startOfWeek(monthStart);
    let endDate = dateFns.endOfWeek(monthEnd);
    const calendarWeeks = dateFns.differenceInCalendarWeeks(monthEnd, monthStart);
    if (calendarWeeks < 5) {
      endDate = dateFns.addWeeks(endDate, 1);
    }
    let arMonths = [];
    const dateFormat = 'd';
    const rows = [];
    let days = [];
    let day = startDate;
    let formattedDate = '';
    while (day <= endDate) {
      for (let i = 0; i < 7; i++) {
        formattedDate = dateFns.format(day, dateFormat);
        const cloneDay = day;
        arMonths.push(this.getHijriMonth(day));
        days.push(
          <React.Fragment key={i}>
            {this.state.selectedRadioButton === 'gregorian' ? (
              <div
                className={`td ${
                  !dateFns.isSameMonth(day, monthStart)
                    ? 'disabled'
                    : dateFns.isSameDay(day, selectedDate)
                    ? 'selected'
                    : ''
                }`}
                key={day}
                onClick={() => this.onDateClick(cloneDay)}
              >
                <span className="number">{formattedDate}</span>
                {this.getHijriHtml(day, monthStart, selectedDate)}
              </div>
            ) : (
              <div
                className={`td ${
                  !dateFns.isSameMonth(day, monthStart)
                    ? 'disabled'
                    : dateFns.isSameDay(day, selectedDate)
                    ? 'selected'
                    : ''
                }`}
                key={day}
                onClick={() => this.onDateClick(cloneDay)}
              >
                {this.getHijriHtml(day, monthStart, selectedDate)}
                <span className="number">{formattedDate}</span>
              </div>
            )}
          </React.Fragment>
        );
        day = dateFns.addDays(day, 1);
      }
      rows.push(
        <div className="tr" key={day}>
          {days}
        </div>
      );
      days = [];
    }
    const uniqMonths = new Set(arMonths);
    const at = [...uniqMonths];
    return {
      rows: <>{rows}</>,
      monthNames: at.join(' / '),
    };
  }
  onDateClick = (day) => {
    this.setState({
      selectedDate: day,
      startDateShow: false,
    });
  };
  nextMonth = () => {
    this.setState({
      currentMonth: dateFns.addMonths(this.state.currentMonth, 1),
    });
  };
  prevMonth = () => {
    this.setState({
      currentMonth: dateFns.subMonths(this.state.currentMonth, 1),
    });
  };
  gmod = (n, m) => {
    return ((n % m) + m) % m;
  };
  kuwaiticalendar = (date) => {
    let today = date;
    let adjust = 0;
    let adjustmili = 1000 * 60 * 60 * 24 * adjust;
    let todaymili = today.getTime() + adjustmili;
    today = new Date(todaymili);
    let day = today.getDate();
    let month = today.getMonth();
    let year = today.getFullYear();
    let m = month + 1;
    let y = year;
    if (m < 3) {
      y -= 1;
      m += 12;
    }
    let a = Math.floor(y / 100);
    let b = 2 - a + Math.floor(a / 4);
    if (y < 1583) b = 0;
    if (y === 1582) {
      if (m > 10) b = -10;
      if (m === 10) {
        b = 0;
        if (day > 4) b = -10;
      }
    }
    let jd = Math.floor(365.25 * (y + 4716)) + Math.floor(30.6001 * (m + 1)) + day + b - 1524;
    b = 0;
    if (jd > 2299160) {
      a = Math.floor((jd - 1867216.25) / 36524.25);
      b = 1 + a - Math.floor(a / 4);
    }
    let bb = jd + b + 1524;
    let cc = Math.floor((bb - 122.1) / 365.25);
    let dd = Math.floor(365.25 * cc);
    let ee = Math.floor((bb - dd) / 30.6001);
    day = bb - dd - Math.floor(30.6001 * ee);
    month = ee - 1;
    if (ee > 13) {
      cc += 1;
      month = ee - 13;
    }
    year = cc - 4716;
    let wd;
    if (adjust) {
      wd = this.gmod(jd + 1 - adjust, 7) + 1;
    } else {
      wd = this.gmod(jd + 1, 7) + 1;
    }
    let iyear = 10631 / 30;
    let epochastro = 1948084;
    let shift1 = 8.01 / 60;
    let z = jd - epochastro;
    let cyc = Math.floor(z / 10631);
    z = z - 10631 * cyc;
    let j = Math.floor((z - shift1) / iyear);
    let iy = 30 * cyc + j;
    z = z - Math.floor(j * iyear + shift1);
    let im = Math.floor((z + 28.5001) / 29.5);
    if (im === 13) im = 12;
    let id = z - Math.floor(29.5001 * im - 29);
    let myRes = new Array(8);
    myRes[0] = day; //calculated day (CE)
    myRes[1] = month - 1; //calculated month (CE)
    myRes[2] = year; //calculated year (CE)
    myRes[3] = jd - 1; //julian day number
    myRes[4] = wd - 1; //weekday number
    myRes[5] = id; //islamic date
    myRes[6] = im - 1; //islamic month
    myRes[7] = iy; //islamic year
    return myRes;
  };
  getHijriDate = (date) => {
    let iDate = this.kuwaiticalendar(date);
    let outputIslamicDate =
      this.state.wdNames[iDate[4]] +
      ', ' +
      iDate[5] +
      ' ' +
      this.state.iMonthNames[iDate[6]] +
      ' ' +
      iDate[7] +
      ' AH';
    return outputIslamicDate;
  };
  getHijriDayNumber = (date) => {
    let iDate = this.kuwaiticalendar(date);
    return iDate[5];
  };
  getHijriMonth = (date) => {
    let iDate = this.kuwaiticalendar(date);
    return this.state.iMonthNames[iDate[6]];
  };
  renderCellsEnd() {
    const { endMonth, selectedEndDate } = this.state;
    const monthStart = dateFns.startOfMonth(endMonth);
    const monthEnd = dateFns.endOfMonth(monthStart);
    const startDate = dateFns.startOfWeek(monthStart);
    let endDate = dateFns.endOfWeek(monthEnd);
    const calendarWeeks = dateFns.differenceInCalendarWeeks(monthEnd, monthStart);
    if (calendarWeeks < 5) {
      endDate = dateFns.addWeeks(endDate, 1);
    }
    let arMonths = [];
    const dateFormat = 'd';
    const rows = [];
    let days = [];
    let day = startDate;
    let formattedDate = '';
    while (day <= endDate) {
      for (let i = 0; i < 7; i++) {
        formattedDate = dateFns.format(day, dateFormat);
        const cloneDay = day;
        arMonths.push(this.getHijriMonth(day));
        days.push(
          <React.Fragment key={i}>
            {this.state.selectedRadioButton === 'gregorian' ? (
              <div
                className={`td ${
                  !dateFns.isSameMonth(day, monthStart)
                    ? 'disabled'
                    : dateFns.isSameDay(day, selectedEndDate)
                    ? 'selected'
                    : ''
                }`}
                key={day}
                onClick={() => this.onDateClickEnd(cloneDay)}
              >
                <span className="number">{formattedDate}</span>
                {this.getHijriHtml(day, monthStart, selectedEndDate)}
              </div>
            ) : (
              <div
                className={`td ${
                  !dateFns.isSameMonth(day, monthStart)
                    ? 'disabled'
                    : dateFns.isSameDay(day, selectedEndDate)
                    ? 'selected'
                    : ''
                }`}
                key={day}
                onClick={() => this.onDateClickEnd(cloneDay)}
              >
                {this.getHijriHtml(day, monthStart, selectedEndDate)}
                <span className="number">{formattedDate}</span>
              </div>
            )}
          </React.Fragment>
        );
        day = dateFns.addDays(day, 1);
      }
      rows.push(
        <div className="tr" key={day}>
          {days}
        </div>
      );
      days = [];
    }
    const uniqMonths = new Set(arMonths);
    const at = [...uniqMonths];
    return {
      rows2: <>{rows}</>,
      monthNames2: at.join(' / '),
    };
  }
  onDateClickEnd = (day) => {
    this.setState({
      selectedEndDate: day,
      endDateShow: false,
    });
  };
  nextMonthEnd = () => {
    this.setState({
      endMonth: dateFns.addMonths(this.state.endMonth, 1),
    });
  };
  prevMonthEnd = () => {
    this.setState({
      endMonth: dateFns.subMonths(this.state.endMonth, 1),
    });
  };
  onRadioGroupChange = (e, name) => {
    if (name === 'input')
      this.setState({
        selectedRadioButton: e.target.value,
      });
    if (name === 'inputs')
      this.setState({
        selectedRadioButtons: e.target.value,
      });
    if (name === 'genderinput') {
      this.setState({
        selectgender: e.target.value,
        genderError: '',
      });
      event_gender = e.target.value;
    }
  };
  onShow = () => {
    this.setState({
      show: true,
    });
  };
  onClose = () => {
    this.setState({
      show: false,
    });
  };
  startDateShow = () => {
    this.setState({
      startDateShow: !this.state.startDateShow,
    });
  };
  endDateShow = () => {
    this.setState({
      endDateShow: !this.state.endDateShow,
    });
  };
  onSubmit = () => {
    const { selectedDate, selectedEndDate } = this.state;
    const { loggedInUserData } = this.props;
    let st = Date.parse(selectedDate);
    let et = Date.parse(selectedEndDate);
    let error = false;
    if (st >= et) {
      error = true;
      this.props.setData({ message: t('endDateGreat') });
    }
    if (!error) {
      this.setState({
        isLoading: true,
      });
      const data = {
        calendar_name: `${moment(selectedDate).format('YYYY')}-${moment(selectedEndDate).format(
          'YYYY'
        )}`,
        calendar_type: 'primary',
        primary_calendar: 'gregorian',
        batch: `${moment(selectedDate).format('YY')}${moment(selectedEndDate).format('YY')}`,
        start_date: moment(selectedDate).format('YYYY-MM-DD'),
        end_date: moment(selectedEndDate).format('YYYY-MM-DD'),
        _institution_id: getEnvInstitutionId(),
        _creater_id: loggedInUserData.get('_id', ''),
      };
      axios
        .post(`institution_calendar`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              show: false,
              index: 0,
            },
            () => {
              this.fetchApi(0);
            }
          );
          this.props.setData({ message: t('calender_created_successfully') });
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    }
  };
  publishViewShow = (e) => {
    this.setState({
      publishView: true,
    });
  };
  publishViewClose = (e) => {
    this.setState({
      publishView: false,
    });
  };
  publishSubmit = (e) => {
    e.preventDefault();
    let id = this.state.calendarId;
    let type = '';
    const { calendarData, index } = this.state;
    const formattedYear = getFormattedHijiriYear(
      calendarData[index].calendar_name,
      calendarData[index].start_date,
      calendarData[index].end_date
    );
    if (this.state.publishStaff !== '' && this.state.publishStudent !== '') {
      type = 'both';
    } else if (this.state.publishStaff !== '') {
      type = 'staff';
    } else if (this.state.publishStudent !== '') {
      type = 'student';
    } else if (this.state.publishStudent === '' && this.state.publishStudent === '') {
      this.setState({
        publishError: t('chooseSendNotify'),
      });
    }
    if (
      this.state.completeEmail !== '' ||
      this.state.completeSms !== '' ||
      this.state.completeScheduler !== '' ||
      (this.state.completeClass !== '' && type !== '')
    ) {
      const data = {
        to: 'publish',
        type: type,
        _calendar_id: id,
        message: `<p>Dear User,<br><br>
        ${formatFullName(
          this.props.loggedInUserData.get('name', Map()).toJS()
        )} Published the Institution Calendar for the Academic year ${formattedYear}
        <br><br>
        Best Regards<br>
        ${getEnvCollegeName()}</p>`,
        notify_via: [
          this.state.completeEmail,
          this.state.completeSms,
          this.state.completeScheduler,
          this.state.completeClass,
        ],
      };
      this.setState({
        isLoading: true,
      });
      axios
        .post(`institution_calendar_event/send_notification`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              publishView: false,
              completeEmail: '',
              completeSms: '',
              completeScheduler: '',
              completeClass: '',
              publishStaff: '',
              publishStudent: '',
              type: '',
            },
            () => {
              this.fetchApi(this.state.index);
            }
          );
          this.props.setData({ message: res.data.message });
        })
        .catch((error) => {
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      this.setState({
        completeError: t('chooseNotification'),
      });
    }
  };
  handleAllCheckBox = (event, name) => {
    let data = this.state.selectStaffList;
    if (name === 'email') {
      data.map((data) => (data.isCheckedEmail = event.target.checked));
      this.setState({ selectStaffList: data });
    }
    if (name === 'sms') {
      data.map((data) => (data.isCheckedSms = event.target.checked));
      this.setState({ selectStaffList: data });
    }
    if (name === 'scheduler') {
      data.map((data) => (data.isCheckedScheduler = event.target.checked));
      this.setState({ selectStaffList: data });
    }
    if (name === 'class') {
      data.map((data) => (data.isCheckedClass = event.target.checked));
      this.setState({ selectStaffList: data });
    }
    if (name === 'completeemail') {
      let email = event.target.checked;
      if (email === true) {
        this.setState({
          completeEmail: 'email',
          completeError: '',
        });
      } else {
        this.setState({
          completeEmail: '',
        });
      }
    }
    if (name === 'completesms') {
      let sms = event.target.checked;
      if (sms === true) {
        this.setState({
          completeSms: 'sms',
          completeError: '',
        });
      } else {
        this.setState({
          completeSms: '',
        });
      }
    }
    if (name === 'completescheduler') {
      let Scheduler = event.target.checked;
      if (Scheduler === true) {
        this.setState({
          completeScheduler: 'digiclass',
          completeError: '',
        });
      } else {
        this.setState({
          completeScheduler: '',
        });
      }
    }
    if (name === 'completeclass') {
      let digiclass = event.target.checked;
      if (digiclass === true) {
        this.setState({
          completeClass: 'digiclass',
          completeError: '',
        });
      } else {
        this.setState({
          completeClass: '',
        });
      }
    }
    if (name === 'staff') {
      let staff = event.target.checked;
      if (staff === true) {
        this.setState({
          publishStaff: 'staff',
          publishError: '',
        });
      } else {
        this.setState({
          publishStaff: '',
        });
      }
    }
    if (name === 'student') {
      let student = event.target.checked;
      if (student === true) {
        this.setState({
          publishStudent: 'student',
          publishError: '',
        });
      } else {
        this.setState({
          publishStudent: '',
        });
      }
    }
  };
  handleStartDate = (date) => {
    this.setState({
      startDate: date,
      endDate: date,
      startDateError: '',
    });
  };
  handleEndDate = (date) => {
    this.setState({
      endDate: date,
      endDateError: '',
    });
  };
  editStartDate = (date) => {
    this.setState({
      startDate: date,
      endDate: date,
    });
  };
  editEndDate = (date) => {
    this.setState({
      endDate: date,
    });
  };
  headRows = () => {
    return [{ no: 'S.NO', date: 'EVENT DATE', time: 'START DAY', title: 'EVENT TITLE' }];
  };
  bodyRows = () => {
    let data = this.state.data;
    let pushArray = [];
    if (data && data.length > 0) {
      data.sort((a, b) => Date.parse(a.event_date) - Date.parse(b.end_date));
      if (data && data.length > 0) {
        data.map((list, index) => {
          let oneDayEvent =
            moment(list.event_date).format('L') === moment(list.end_date).format('L');
          let eventDate = moment(list.event_date).format('MMM Do'); //+' - '+moment(list.end_date).format("MMM Do, YYYY");
          //let hijiriYear = writeHijri(new Date(list.event_date))+' - '+ writeHijri(new Date(list.end_date));
          //let hijiriYear = writeHijri(new Date(list.event_date))+' - '+ writeHijri(new Date(list.end_date));
          let hijiriYear = getHijriDateFormat(list.event_date); //+' - '+ getHijriDateFormat(list.end_date);
          if (oneDayEvent) {
            eventDate = moment(list.event_date).format('MMM Do, YYYY');
            //hijiriYear = writeHijri(new Date(list.event_date));
            hijiriYear = getHijriDateFormat(list.event_date);
          }
          let arabicDay = getHijriDayFormat(list.event_date);
          let eventTitle =
            typeof list.event_name === 'object' ? list.event_name.first_language : list.event_name;
          //let time = moment(list.start_time).format("hh:mm A")+" - "+moment(list.end_time).format("hh:mm A")
          let time = getDayName(new Date(list.event_date));
          let text = list.event_name.second_language;
          // let array = [{ no: index+1, date: eventDate, time: time, title: eventTitle},{ no: '', date: hijiriYear.replace(/ʻ/g, 'a'), time: '', title: text}];
          let array = [
            { no: index + 1, date: eventDate, time: time, title: eventTitle },
            { no: '', date: hijiriYear.replace(/ʻ/g, 'a'), time: '', title: text },
          ];
          pushArray.push({ no: index + 1, date: eventDate, time: time, title: eventTitle });
          // pushArray.push({ no: '', date: hijiriYear.replace(/ʻ/g, 'a'), time: '', title: text});
          if (showArabicMailShow()) {
            pushArray.push({
              no: '',
              date: hijiriYear.replace(/ʻ/g, 'a'),
              time: arabicDay,
              title: text,
            });
          }

          return array;
        });
      }
      return pushArray;
    }
  };
  printListPDF = (pdfName) => {
    var doc = new jsPDF();
    doc.addFont(ArabicFond, 'Amiri', 'normal');
    let calendarData = this.state.calendarData[this.state.index];
    //if (isDemoVer() || isIndVer()) {
    doc.setFontSize(25);
    doc.text(getEnvCollegeName(), 105, 25, null, null, 'center');
    // } else {
    //   doc.addImage(Logo, 'JPEG', 15, 15, 180, 25);
    // }
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text('Academic Year : ' + calendarData.calendar_name, 15, 50);
    doc.text(
      'Start Date : ' + moment(calendarData.start_date).format('MMM Do,YYYY'),
      195,
      50,
      null,
      null,
      'right'
    );
    doc.text(
      'End Date : ' + moment(calendarData.end_date).format('MMM Do,YYYY'),
      194,
      56,
      null,
      null,
      'right'
    );
    doc.setFont('helvetica', 'bold');
    doc.text('Event Details', 100, 62, null, null, 'center');
    jsPDF.autoTableSetDefaults({
      headStyles: { fillColor: '#e0e0e0', textColor: 0 },
      bodyStyles: { fillColor: '#ffffff', textColor: 0 },
    });
    doc.setFontSize(30);
    doc.autoTable({
      startY: 70,
      head: this.headRows(),
      body: this.bodyRows(),
      tableLineColor: [189, 195, 199],
      //tableLineWidth: 0.50,
      theme: 'grid',
      // styles: {font: "Amiri"}
      columns: [
        { dataKey: 'no', header: 'S.NO' },
        { dataKey: 'date', header: 'EVENT DATE' },
        { dataKey: 'time', header: 'START DAY' },
        { dataKey: 'title', header: 'EVENT TITLE' },
      ],
      columnStyles: {
        title: {
          font: 'Amiri',
          halign: 'right',
        },
        date: {
          font: 'Amiri',
        },
        time: {
          font: 'Amiri',
        },
      },
      headStyles: {
        fillColor: '#acafa6',
        fontSize: 13,
        textColor: '#ffffff',
      },
      bodyStyles: {
        fillColor: '#e5f3db',
      },
    });
    this.addFooters(doc);
    doc.save(`${pdfName}.pdf`);
  };
  addFooters = (doc) => {
    const pageCount = doc.internal.getNumberOfPages();
    doc.setFontSize(10);
    doc.text(
      `© Copyright ${new Date().getFullYear()}, DigiScheduler Developed & Maintained by Digival IT Solutions`,
      doc.internal.pageSize.width / 2,
      272,
      null,
      null,
      'center'
    );
    doc.text(
      ` - UAE (in technology partnership with Digival Solutions Pvt Ltd, India).`,
      doc.internal.pageSize.width / 2,
      277,
      null,
      null,
      'center'
    );
    doc.setFont('helvetica', 'italic');
    doc.setFontSize(8);
    for (var i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.text(
        'Page ' + String(i) + ' of ' + String(pageCount),
        doc.internal.pageSize.width / 2,
        287,
        {
          align: 'right',
        }
      );
    }
  };
  toggleEvent = (i) => {
    let copyData = [...this.state.data];
    let copyIndex = copyData[i];
    copyIndex['showElem'] = !copyData[i]['showElem'];
    copyData[i] = copyIndex;
    this.setState({ data: copyData });
  };

  getCalendarStatus = () => {
    const { calendarData, index } = this.state;
    // const { isCurrentCalendar } = this.props;
    if (calendarData.length > 0) {
      // const currentId = calendarData[index].id;
      const isActive = calendarData[index].isActive;
      // const status = calendarData[index].status;
      return isActive;
      //return status !== '' ? isCurrentCalendar(currentId) : isActive;
      // const currentId = calendarData[index].id;
      // return isCurrentCalendar(currentId);
    }
    return true;
  };

  render() {
    // let dd = moment(this.state.startTimeView).format('hh:mm A');
    // let ss = moment(this.state.endTimeView).format('hh:mm A');
    const dateFormat = 'MMM yyyy';
    let gregorianMonthYear = dateFns.format(this.state.currentMonth, dateFormat);
    let gregorianMonthYearEnd = dateFns.format(this.state.endMonth, dateFormat);
    const { rows, monthNames } = this.renderCells();
    const { rows2, monthNames2 } = this.renderCellsEnd();
    let hasICReviewerPermission = false;
    const { t, loggedInUserData } = this.props;
    const input = [
      [t('gregorian'), 'gregorian'],
      // ["Hijri", "hijri"],
    ];
    // const genderinput = [
    //   [t('maleOnly'), 'male'],
    //   [t('femaleOnly'), 'female'],
    // ];
    let splitStartValue = gregorianMonthYear.split(' ');
    let splitEndValue = gregorianMonthYearEnd.split(' ');
    gregorianMonthYear = `${t(`calender.${splitStartValue[0]}`)} ${splitStartValue[1]}`;
    gregorianMonthYearEnd = `${t(`calender.${splitEndValue[0]}`)} ${splitEndValue[1]}`;
    const loggedInRole = loggedInUserData.get('role', '');
    const subRole = loggedInUserData.get('sub_role', List());
    if (subRole && subRole.size > 0) {
      hasICReviewerPermission = subRole.indexOf('Institution_Calendar_Reviewer') !== -1;
    }
    const userType = loggedInUserData.get('user_type', '');
    const academicYear = this.state.calendarData[this.state.index];
    let months = [];
    let loggedInStatus;
    let academicYearStartDate = '';
    let academicYearEndDate = '';
    let status = '';
    let calendarName = '';
    let hijiriYear = '';
    if (academicYear !== undefined) {
      loggedInStatus = academicYear.status;
      months = this.getMonths(academicYear.start_date, academicYear.end_date);
      academicYearStartDate =
        academicYear.start_date !== '' ? moment(academicYear.start_date).format('YYYY-MM-DD') : '';
      academicYearEndDate =
        academicYear.end_date !== '' ? moment(academicYear.end_date).format('YYYY-MM-DD') : '';
      status = academicYear.status;
      calendarName = academicYear.calendar_name;
      if (academicYear.calendar_name !== '' && academicYear.calendar_name !== null) {
        hijiriYear = getFormattedHijiriYear(
          academicYear.calendar_name,
          academicYear.start_date,
          academicYear.end_date
        );
      }
    }
    let pdfProps = {
      collegeName: getEnvCollegeName().toUpperCase(),
      academicYear: calendarName,
      events: this.state.data,
      academicYearStartDate: academicYearStartDate,
      academicYearEndDate: academicYearEndDate,
    };
    const items = [{ to: '#', label: t('side_nav.menus.institution_calendar') }];
    const currentCalendar = this.getCalendarStatus();
    const iconForm = lang === 'ar' ? 'icon-form-arabic' : 'icon-form';
    // const iconFormCalender = lang === 'ar' ? 'icon-form-calender-arabic' : 'icon-form-calender';
    const chevronLeft = `fa fa-chevron-${lang === 'ar' ? 'right' : 'left'}`;
    const chevronRight = `fa fa-chevron-${lang === 'ar' ? 'left' : 'right'}`;
    const getTranslatedDate = (eventDate, endDate) => {
      return `${`${t(`calender.${moment(eventDate).format('MMM')}`)} ${moment(eventDate).format(
        'Do'
      )}`} - ${`${moment(endDate).format('Do')} ${t(
        `calender.${moment(endDate).format('MMM')}`
      )} ${moment(endDate).format('YY')}`}`;
    };

    return (
      <React.Fragment>
        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={index} to={to}>
                {label}
              </Link>
            ))}
        </Breadcrumb>
        <div className="main">
          <Loader isLoading={this.state.isLoading} />
          <CalenderIndex />
          <div className="container pt-2" style={{ fontSize: '20px' }}>
            <div className="col-lg-12">
              <MultiPage id={'multiPage'} props={pdfProps} />
            </div>
            <div className="col-lg-12 pt-3">
              <div className="row">
                <div className="col-lg-6 p-2 col-md-7">
                  <div className="d-flex justify-content-between">
                    <div className=" p-2">
                      {/* && (loggedInStatus === "published" || loggedInRole === "Dean" || hasICReviewerPermission) */}
                      {this.state.calendarData && this.state.calendarData.length > 0 && (
                        <>
                          {this.state.prevBtnDisabled === false && (
                            <span onClick={this.togglePrev}>
                              <i className={`pr-3 ${chevronLeft}`} aria-hidden="true"></i>
                            </span>
                          )}
                          {academicYear !== undefined && (
                            <span>
                              {t('academic_year')} {hijiriYear}
                            </span>
                          )}
                          {this.state.nextBtnDisabled === false &&
                            this.state.calendarData &&
                            this.state.calendarData.length > 1 && (
                              <span onClick={this.toggleNext}>
                                <i className={`pl-3 ${chevronRight}`} aria-hidden="true"></i>
                              </span>
                            )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="col-lg-2 p-2 col-md-3">
                  {CheckPermission(
                    'pages',
                    'Calendar',
                    'Institution Calendar',
                    'Calendar List View'
                  ) && (
                    <Link to="/all-calendar-list">
                      <Button className="pull-right"> Calendar Lists</Button>
                    </Link>
                  )}
                </div>
                <div className="col-lg-1 p-2 col-md-2">
                  {this.state.calendarData &&
                    this.state.calendarData.length > 0 &&
                    status === 'published' && (
                      <>
                        {/* <button
                          title={t('program_calendar.export_as_pdf')}
                          className={'fa fa-file-pdf-o pull-right btn-custom'}
                          onClick={() => this.printListPDF('Academic-Year-' + calendarName)}
                        ></button> */}
                        <Button
                          variant="outline-primary"
                          className="border-radious-8 f-14"
                          onClick={() => this.printListPDF('Academic-Year-' + calendarName)}
                        >
                          <i className="fa fa-download pr-2" aria-hidden="true"></i>
                          <Trans i18nKey={'student_grouping.export'} />
                        </Button>
                      </>
                    )}
                </div>
                <div className="col-lg-3 p-2">
                  {currentCalendar &&
                    CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Create') && (
                      <Button onClick={this.onShow} className="pull-right">
                        {' '}
                        <i className="fa fa-plus mr-2" aria-hidden="true"></i>
                        <Trans
                          i18nKey={'role_management.modules_list.Institution Calendar'}
                        ></Trans>
                      </Button>
                    )}
                </div>
              </div>
            </div>
          </div>
          <div className="p-2">
            <div className="container">
              <div className="clearfix"></div>
              {this.state.show && (
                <Suspense fallback="">
                  <CreateInstitutionCalendar
                    show={this.state.show}
                    calendarState={this.state}
                    input={input}
                    iconForm={iconForm}
                    chevronLeft={chevronLeft}
                    chevronRight={chevronRight}
                    rows={rows}
                    rows2={rows2}
                    monthNames={monthNames}
                    monthNames2={monthNames2}
                    gregorianMonthYear={gregorianMonthYear}
                    gregorianMonthYearEnd={gregorianMonthYearEnd}
                    showArabicMonth={this.showArabicMonth}
                    onRadioGroupChange={this.onRadioGroupChange}
                    handleStartDateShow={this.startDateShow}
                    handleEndDateShow={this.endDateShow}
                    prevMonth={this.prevMonth}
                    nextMonth={this.nextMonth}
                    prevMonthEnd={this.prevMonthEnd}
                    nextMonthEnd={this.nextMonthEnd}
                    onSubmit={this.onSubmit}
                    onClose={this.onClose}
                  />
                </Suspense>
              )}
              <div className="bg-white">
                {CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Add Event') ||
                hasICReviewerPermission ||
                ((userType === 'student' || userType === 'staff') &&
                  loggedInStatus === 'published') ? (
                  <div className="row">
                    <div
                      className={
                        this.state.calendarData && this.state.calendarData.length > 0
                          ? 'col-md-9'
                          : 'col-md-12'
                      }
                    >
                      {this.state.calendarData && this.state.calendarData.length > 0 ? (
                        <>
                          <div className="calendarCard">
                            {currentCalendar &&
                            CheckPermission(
                              'pages',
                              'Calendar',
                              'Institution Calendar',
                              'Add Event'
                            ) ? (
                              <div className="">
                                {this.getMonthlyCalendar(
                                  months,
                                  this.eventShow,
                                  academicYearStartDate,
                                  academicYearEndDate
                                )}
                              </div>
                            ) : (
                              <div className="">
                                {this.getMonthlyCalendar(
                                  months,
                                  '',
                                  academicYearStartDate,
                                  academicYearEndDate
                                )}
                              </div>
                            )}
                          </div>
                        </>
                      ) : (
                        <div className="row">
                          <div className="col-md-12">
                            <div className="notpublished-screen">
                              <div className="notpublished">
                                <h2>
                                  <Trans i18nKey={'program_calendar.no_calendar'}></Trans>
                                </h2>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    {this.state.calendarData && this.state.calendarData.length > 0 && (
                      <>
                        {loggedInRole === '' ? (
                          ''
                        ) : (
                          <div className="col-md-3">
                            <div
                              className="calenderTopContent"
                              style={{ marginTop: '15px' }}
                              // onClick={this.eventShow}
                            >
                              <h3 className="remove_hover f-18 ">
                                <Trans i18nKey={'program_calendar.events'}></Trans>
                                {currentCalendar &&
                                  CheckPermission(
                                    'pages',
                                    'Calendar',
                                    'Institution Calendar',
                                    'Add Event'
                                  ) && (
                                    <i
                                      className="fa fa-plus float-right mr-3 "
                                      aria-hidden="true"
                                      onClick={this.eventShow}
                                    ></i>
                                  )}
                              </h3>
                              {currentCalendar &&
                                CheckPermission(
                                  'pages',
                                  'Calendar',
                                  'Institution Calendar',
                                  'Add Event'
                                ) && (
                                  <small>
                                    {' '}
                                    <Trans
                                      i18nKey={'program_calendar.click_to_create_event'}
                                    ></Trans>
                                  </small>
                                )}
                            </div>
                            <div className="openDefaultActive">
                              {this.state.data.map((view, i) => (
                                <div className="card" key={i}>
                                  <div
                                    className="bg-white card-header-open"
                                    onClick={() => this.toggleEvent(i)}
                                  >
                                    <p className="f-14 m-0">
                                      {getTranslatedDate(view.event_date, view.end_date)}
                                      {view.showElem === true ? (
                                        <i
                                          className="fa fa-chevron-up"
                                          style={{ float: `${lang === 'ar' ? 'left' : 'right'}` }}
                                        ></i>
                                      ) : (
                                        <i
                                          className="fa fa-chevron-down"
                                          style={{ float: `${lang === 'ar' ? 'left' : 'right'}` }}
                                        ></i>
                                      )}
                                    </p>
                                  </div>
                                  {view.showElem === true && (
                                    <div>
                                      <div className="bg-white card-body">
                                        <p className="f-14 m-0">
                                          {moment(view.start_time)
                                            .format('hh:mm A')
                                            .replace('AM', t('date.am'))}{' '}
                                          -{' '}
                                          {moment(view.end_time)
                                            .format('hh:mm A')
                                            .replace('PM', t('date.pm'))}{' '}
                                        </p>
                                        <p className="f-14 mb-1">
                                          {t('type')} : {t(`events.event_types.${view.event_type}`)}
                                        </p>
                                        <hr />
                                        <p className="f-14 mb-1 text-blue">
                                          {t('events.event_name')}:
                                        </p>
                                        <p className="f-14 mb-1">
                                          {jsUcfirstAll(view.event_name.first_language)}
                                        </p>
                                        {view.event_name.second_language !== '' ? (
                                          <p className="f-14 mb-1 text-right">
                                            {view.event_name.second_language}
                                          </p>
                                        ) : (
                                          ''
                                        )}
                                        <hr />
                                        <p className="f-14 mb-1 text-blue">
                                          {' '}
                                          {t('eventDescription')}:{' '}
                                        </p>
                                        <p className="f-14 mb-1">
                                          {' '}
                                          {view.event_description.first_language}
                                        </p>
                                        {view.event_description.second_language !== '' ? (
                                          <p className="f-14 mb-1 text-right">
                                            {view.event_description.second_language}
                                          </p>
                                        ) : (
                                          ''
                                        )}
                                        <Button
                                          variant="primary"
                                          size="sm"
                                          onClick={(e) => this.eventViewShow(e, view)}
                                        >
                                          <Trans i18nKey={'view'}></Trans>
                                        </Button>{' '}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                            {CheckPermission(
                              'pages',
                              'Calendar',
                              'Institution Calendar',
                              'View All'
                            ) && (
                              <p
                                className="f-16 mr-3 text-right remove_hover"
                                onClick={this.listView}
                              >
                                {' '}
                                {t('view_all')}
                              </p>
                            )}
                            <React.Fragment>
                              {this.state.data.length > 0 ? (
                                <React.Fragment>
                                  {currentCalendar &&
                                    CheckPermission(
                                      'pages',
                                      'Calendar',
                                      'Institution Calendar',
                                      'Review'
                                    ) &&
                                    status !== 'published' && (
                                      <div className="col-md-12">
                                        <Button
                                          variant="outline-primary"
                                          block
                                          onClick={this.reviewEvent}
                                        >
                                          {t('reviewEvent')}
                                        </Button>
                                      </div>
                                    )}
                                  {currentCalendar &&
                                    CheckPermission(
                                      'pages',
                                      'Calendar',
                                      'Institution Calendar',
                                      'Publish'
                                    ) && ( //&& status!=='published'
                                      <div className="col-md-12 pt-3">
                                        <Button
                                          variant="primary"
                                          onClick={this.publishViewShow}
                                          block
                                        >
                                          {t('role_management.role_actions.Publish')}
                                        </Button>
                                        {this.state.publishView && (
                                          <Suspense fallback="">
                                            <PublishInstitutionCalendar
                                              show={this.state.publishView}
                                              currentCalendar={currentCalendar}
                                              publishState={this.state}
                                              onClose={this.publishViewClose}
                                              handleAllCheckBox={this.handleAllCheckBox}
                                              publishSubmit={this.publishSubmit}
                                            />
                                          </Suspense>
                                        )}
                                      </div>
                                    )}
                                  {currentCalendar &&
                                    hasICReviewerPermission &&
                                    status !== 'published' && (
                                      <div className="col-md-12 pt-3">
                                        <Button
                                          variant="outline-primary"
                                          block
                                          onClick={this.reviewAccept}
                                        >
                                          {t('reviewAccept')}
                                        </Button>
                                      </div>
                                    )}
                                </React.Fragment>
                              ) : (
                                ''
                              )}
                            </React.Fragment>
                            {/* } */}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                ) : (
                  <div className="row">
                    <div className="col-md-12">
                      <div className="notpublished-screen">
                        <div className="notpublished">
                          <h2>
                            <Trans i18nKey={'program_calendar.still_not_been_published'}></Trans>
                          </h2>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* edit event  */}
          {this.state.eventEdit && (
            <Suspense fallback="">
              <AddEditEventModal
                academicYearStartDate={academicYearStartDate}
                academicYearEndDate={academicYearEndDate}
                handleState={this.handleState}
                state={this.state}
                show={this.state.eventEdit}
                eventClose={this.eventEditClose}
                fetchApi={() => this.fetchApi(this.state.index)}
                setData={this.props.setData}
                flag={'edit'}
              />
            </Suspense>
          )}
          {this.state.eventDelete && (
            <Suspense fallback="">
              <DeleteEventModal
                show={this.state.eventDelete}
                onClose={this.eventDeleteClose}
                onDelete={(e) => this.eventDelete(e)}
                title={t('global_configuration.delete_event')}
              />
            </Suspense>
          )}
          {this.state.eventView && (
            <Suspense fallback="">
              <EventDetailModal
                show={this.state.eventView}
                currentCalendar={currentCalendar}
                eventState={this.state}
                onClose={this.eventViewClose}
                getTranslatedDate={getTranslatedDate}
                eventEdit={this.eventEdit}
                eventDeleteConfirm={this.eventDeleteConfirm}
              />
            </Suspense>
          )}
          {/* create event */}
          {this.state.eventShow && (
            <Suspense fallback="">
              <AddEditEventModal
                academicYearStartDate={academicYearStartDate}
                academicYearEndDate={academicYearEndDate}
                handleState={this.handleState}
                show={this.state.eventShow}
                state={this.state}
                eventClose={this.eventClose}
                fetchApi={() => this.fetchApi(this.state.index)}
                setData={this.props.setData}
                flag={'create'}
              />
            </Suspense>
          )}
        </div>
      </React.Fragment>
    );
  }
}

CalenderContainerComponent.propTypes = {
  loggedInUserData: PropTypes.instanceOf(Map),
  t: PropTypes.func,
  history: PropTypes.object,
  isCurrentCalendar: PropTypes.func,
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

const CalenderContainer = withTranslation()(CalenderContainerComponent);
export default connect(mapStateToProps, actions)(withRouter(withCalendarHooks(CalenderContainer)));
