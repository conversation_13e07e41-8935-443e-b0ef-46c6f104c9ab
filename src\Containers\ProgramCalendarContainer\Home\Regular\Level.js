import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { find_sun_to_sat } from '../../../../_utils/function';

const LevelHeadingWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-items: center;
  position: relative;
`;

const LevelIndicator = styled.div`
  width: 40px;
  height: ${(props) => props.len};
  margin-left: 35px;
  margin-top: ${(props) => props.top};
  background-color: ${(props) => props.background};
  border-radius: 16px 0px 0px 16px;
  position: relative;
  font-size: 24px;
  line-height: 24px;
  letter-spacing: 4.75px;
  &.no_data {
    height: 700px;
    margin: 50px 0 50px 35px;
  }
  display: flex;
  justify-content: center;
  align-items: center;
`;

//margin: ${(props) => props.len} 0 0 0;
const LevelIndicatorTitle = styled.div`
  white-space: nowrap;
  transform: rotate(270deg);
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 5px;
  color: white;
  &.no_data {
    margin: 350px 0 0 0;
  }
`;

const Level = (props) => {
  const { level_title, end_date, start_date, background } = props;
  const level_one_length = [...find_sun_to_sat(start_date, end_date)].length;
  return (
    <Fragment>
      <LevelHeadingWrapper>
        <LevelIndicator
          className={level_one_length ? '' : 'no_data'}
          len={`${level_one_length * 50}px`}
          background={background}
        >
          <LevelIndicatorTitle
            className={level_one_length ? '' : 'no_data'}
            len={`${level_one_length * 50}px`}
          >
            {level_title !== '' ? level_title : 'Level'}
          </LevelIndicatorTitle>{' '}
        </LevelIndicator>
      </LevelHeadingWrapper>
    </Fragment>
  );
};

Level.propTypes = {
  level_title: PropTypes.string,
  end_date: PropTypes.string,
  start_date: PropTypes.string,
  background: PropTypes.string,
};

export default Level;
