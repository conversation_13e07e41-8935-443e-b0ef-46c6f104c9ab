import * as actionTypes from '../actions/actionTypes';
import { updateObject } from '../../utils';
import { fromJS, Map, List } from 'immutable';
import LocalStorageService from 'LocalStorageService';

const initialState = {
  token: null,
  error: null,
  success: null,
  loading: false,
  loggedInUserData: null,
  publishedProgramLists: [],
  allProgramsList: [],
  userRoles: List(),
  selectedRole: Map(),
  reportsTo: Map(),
  institutionCalendarLists: List(),
  activeInstitutionCalendar: Map(),
  isLoading: false,
  message: '',
  programListWithType: Map(),
};

const authStart = (state, action) => {
  return updateObject(state, { error: null, success: null });
};

const authSuccess = (state, action) => {
  const selectedRole = fromJS(action.selectedRole);
  let activeSelectedRole = {};
  if (selectedRole.get('_id', '') !== '') {
    const getLocalStorageRole = LocalStorageService.getCustomToken('activeSelectedRole', true);
    if (getLocalStorageRole !== null) {
      activeSelectedRole = getLocalStorageRole;
    } else {
      LocalStorageService.setCustomToken('activeSelectedRole', JSON.stringify(selectedRole));
      activeSelectedRole = selectedRole;
    }
  }

  return updateObject(state, {
    token: action.token,
    loggedInUserData: action.loggedInUserData,
    success: 'success',
    userRoles: fromJS(action.userRoles),
    selectedRole: fromJS(activeSelectedRole),
    reportsTo: fromJS(action.reportsTo),
    isAdmin: action.isAdmin,
    isInstitutionOnboarded: action.isInstitutionOnboarded,
  });
};

const authFail = (state, action) => {
  return updateObject(state, {
    error: action.error,
  });
};
const forgetPassStartV2 = (state) => {
  return updateObject(state, { error: null, success: null, isLoading: true });
};
const forgetPassSuccessV2 = (state) => {
  return updateObject(state, {
    error: null,
    message: 'Reset Link Send Successfully',
    isLoading: false,
  });
};
const forgetPassFailV2 = (state, action) => {
  return updateObject(state, {
    error: action.error,
    message: action.error,
    isLoading: false,
  });
};
const authStartV2 = (state, action) => {
  return updateObject(state, { error: null, success: null, isLoading: true });
};

const authSuccessV2 = (state, action) => {
  const selectedRole = fromJS(action.selectedRole);
  let activeSelectedRole = {};
  if (selectedRole.get('_id', '') !== '') {
    const getLocalStorageRole = LocalStorageService.getCustomToken('activeSelectedRole', true);
    if (getLocalStorageRole !== null) {
      activeSelectedRole = getLocalStorageRole;
    } else {
      LocalStorageService.setCustomToken('activeSelectedRole', JSON.stringify(selectedRole));
      activeSelectedRole = selectedRole;
    }
  }

  return updateObject(state, {
    token: action.token,
    loggedInUserData: action.loggedInUserData,
    success: 'success',
    userRoles: fromJS(action.userRoles),
    selectedRole: fromJS(activeSelectedRole),
    reportsTo: fromJS(action.reportsTo),
    isAdmin: action.isAdmin,
    isInstitutionOnboarded: action.isInstitutionOnboarded,
    isLoading: false,
  });
};

const authFailV2 = (state, action) => {
  return updateObject(state, {
    error: action.error,
    message: action.error,
    isLoading: false,
  });
};

const authLogout = (state, action) => {
  return updateObject(state, {
    token: null,
    isLoading: false,
    // message: '',
    institutionCalendarLists: List(),
    selectedRole: Map(),
  });
};

const q360SetRoles = (state, action) => {
  return updateObject(state, {
    userRoles: fromJS(state.userRoles.push(...action?.userRoles)),
  });
};

const programListingStart = (state, action) => {
  return updateObject(state, { error: null, success: null, loading: true });
};

const programListingSuccess = (state, action) => {
  const institutionCalendarLists = fromJS(action.institutionCalendarLists);
  let activeInstitutionCalendar = Map();
  if (institutionCalendarLists.size > 0) {
    const getLocalStorageCalendar = LocalStorageService.getCustomToken(
      'activeInstitutionCalendar',
      true
    );
    if (getLocalStorageCalendar !== null) {
      activeInstitutionCalendar = Map(getLocalStorageCalendar);
    } else {
      LocalStorageService.setCustomToken(
        'activeInstitutionCalendar',
        JSON.stringify(institutionCalendarLists.get(0))
      );
      activeInstitutionCalendar = institutionCalendarLists.get(0);
    }
  }
  return updateObject(state, {
    publishedProgramLists: action.data,
    loading: false,
    allProgramsList: action.allPrograms,
    institutionCalendarLists: institutionCalendarLists,
    activeInstitutionCalendar: activeInstitutionCalendar,
  });
};

const programListingFailed = (state, action) => {
  return updateObject(state, {
    publishedProgramLists: [],
    loading: false,
  });
};

const programListWithTypeStart = (state, action) => {
  return updateObject(state, {
    error: null,
    success: null,
    loading: true,
    programListWithType: Map(),
  });
};

const programListWithTypeSuccess = (state, action) => {
  return updateObject(state, {
    programListWithType: fromJS(action.data),
    loading: false,
  });
};

const programListWithTypeFailed = (state, action) => {
  return updateObject(state, {
    programListWithType: Map(),
    loading: false,
  });
};

const ComprehensiveUpdatedState = (state, action) => {
  const updatedState = { ...state, ...action.data };
  return updatedState;
};

const authReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.AUTH_START:
      return authStart(state, action);
    case actionTypes.AUTH_SUCCESS:
      return authSuccess(state, action);
    case actionTypes.AUTH_FAIL:
      return authFail(state, action);
    case actionTypes.AUTH_LOGOUT:
      return authLogout(state, action);
    case actionTypes.PROGRAM_LISTING_START:
      return programListingStart(state, action);
    case actionTypes.PROGRAM_LISTING_SUCCESS:
      return programListingSuccess(state, action);
    case actionTypes.PROGRAM_LISTING_FAILED:
      return programListingFailed(state, action);
    case actionTypes.SWITCH_ROLE_SUCCESS: {
      LocalStorageService.setCustomToken('activeSelectedRole', JSON.stringify(action.data));
      return updateObject(state, {
        selectedRole: action.data,
      });
    }
    case actionTypes.SWITCH_CALENDAR_SUCCESS: {
      LocalStorageService.setCustomToken('activeInstitutionCalendar', JSON.stringify(action.data));
      return updateObject(state, {
        activeInstitutionCalendar: action.data,
      });
    }
    case actionTypes.SET_ONBOARDED_STATUS:
      return updateObject(state, {
        isInstitutionOnboarded: action.status,
        loggedInUserData: action.data,
      });

    case actionTypes.AUTH_START_V2:
      return authStartV2(state, action);
    case actionTypes.AUTH_SUCCESS_V2:
      return authSuccessV2(state, action);
    case actionTypes.AUTH_FAIL_V2:
      return authFailV2(state, action);
    case actionTypes.SET_DATA_SUCCESS:
      return updateObject(state, {
        message: action.message,
      });
    case actionTypes.RESET_MESSAGE_SUCCESS:
      return updateObject(state, {
        isLoading: false,
        message: '',
      });
    case actionTypes.PROGRAM_LIST_WITH_TYPE_START:
      return programListWithTypeStart(state, action);
    case actionTypes.PROGRAM_LIST_WITH_TYPE_SUCCESS:
      return programListWithTypeSuccess(state, action);
    case actionTypes.PROGRAM_LIST_WITH_TYPE_FAILED:
      return programListWithTypeFailed(state, action);
    case actionTypes.FORGET_PASS_START_V2:
      return forgetPassStartV2(state);
    case actionTypes.FORGET_PASS_SUCCESS_V2:
      return forgetPassSuccessV2(state);
    case actionTypes.FORGET_PASS_FAIL_V2:
      return forgetPassFailV2(state, action);
    case actionTypes.Q360_USER_ROLES_STATUS:
      return q360SetRoles(state, action);
    case actionTypes.SET_DATA_UPDATED_SUCCESS: {
      return ComprehensiveUpdatedState(state, action);
    }
    default:
      return state;
  }
};
export default authReducer;
