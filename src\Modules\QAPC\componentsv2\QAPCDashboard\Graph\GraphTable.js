import React, { useState, useEffect } from 'react';
import { Box, Checkbox } from '@mui/material';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { tabsClasses } from '@mui/material/Tabs';
import {
  useCustomCalendarListHook,
  useCustomHooksForTable,
} from '../../QualityAssuranceProcess/FormList/utils';
import { useSelector, useDispatch } from 'react-redux';
import { Map, List, fromJS } from 'immutable';
import { usePaginationHook } from '../../QualityAssuranceProcess/FormList/QualityAssuranceProcess';
import { selectSelectedRole } from '_reduxapi/Common/Selectors';
import { selectFormAssuranceList } from '_reduxapi/q360/selectors';
import { selectQualityAssuranceList } from '_reduxapi/qapc/selectors';
import MaterialInput from 'Widgets/FormElements/material/Input';
import view_column from 'Assets/img/qapc/view_column.svg';
import Menu from '@mui/material/Menu';
import Fade from '@mui/material/Fade';
import { EnableOrDisable as ShowOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import { MenuItem } from '@mui/material';
import { tabBorderNone } from '../../QualityAssuranceApprover/approvalFlow';
import LockIcon from '@mui/icons-material/Lock';
// import useCustomCookie from 'Hooks/useCookieHook';
// import { useQ360PermissionHook } from '../../Utils';
import PaginationQ360 from '../../QualityAssuranceProcess/FormList/PaginationQ360';

// import EditIcon from '@mui/icons-material/Edit';
// import DeleteIcon from '@mui/icons-material/Delete';
// import ArchiveIcon from '@mui/icons-material/Archive';
// import UnarchiveIcon from '@mui/icons-material/Unarchive';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import DoneIcon from '@mui/icons-material/Done';
import CloseIcon from '@mui/icons-material/Close';
import QuestionMarkIcon from '@mui/icons-material/QuestionMark';
import { getFormList, getRpAction } from '_reduxapi/q360/actions';
import useDebounce from 'Hooks/useDebounceHook';
import { PriorityHighRounded } from '@mui/icons-material';
import { Tooltip } from '@mui/material';
import ShowEvidenceDocument from '../../QualityAssuranceProcess/CategoryOverview/Modal/ShowEvidenceDocument';

const useTabState = (defaultValue) => {
  const [tab, setTab] = useState(defaultValue);
  const updateTab = (_, value) => setTab(value);
  return [tab, updateTab];
};

const CategoryTabSx = {
  '& .MuiTab-root': {
    margin: '-5px 20px 0px 0px',
    padding: '0px 0px',
    minWidth: '0px',
  },
};

const tabListSX = {
  [`& .${tabsClasses.scrollButtons}`]: {
    '&.Mui-disabled': { opacity: 0.3 },
  },
  '& .MuiTabs-flexContainer': {
    gap: '10px',
  },
};

const useSearch = (fromIds) => {
  const [searchKey, setSearchKey] = useState('');
  const handleChange = (e) => setSearchKey(e.target.value);
  const filterIds = fromIds
    .get('qapcFormIds', List())
    .filter(
      (Element) =>
        Element.get('program_name', '').includes(searchKey) &&
        Element.get('course_name', '').includes(searchKey)
    )
    .map((Element) => Element.get('_id', ''));
  return [searchKey, filterIds, handleChange];
};

function formatDate(date) {
  const options = {
    day: 'numeric',
    month: 'short',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  };
  return new Intl.DateTimeFormat('en-US', options).format(date);
}

function MenuColumnOrder({ anchorEl, handleClose, columns, columnFilter, handleCheckbox, open }) {
  return (
    <div>
      <Menu
        id="fade-menu"
        MenuListProps={{
          'aria-labelledby': 'fade-button',
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
      >
        {columns.splice(-2).map((item) => (
          <ShowOrDisable key={item.get('name', '')} valid={item.get('name', '') !== ''}>
            <MenuItem className="f-12 fw-400 text-dGrey">
              {item.get('Disable', false) ? (
                <LockIcon className="pr-2 f-20" color="disabled" />
              ) : (
                <Checkbox
                  checked={columnFilter.get(item.get('name', ''), true)}
                  onChange={(e) => handleCheckbox(e, item.get('name', ''))}
                  className="p-0 f-20 pr-2"
                  size=""
                />
              )}
              {item.get('name', '')}
            </MenuItem>
          </ShowOrDisable>
        ))}
      </Menu>
    </div>
  );
}

const tabPadding = {
  '&.MuiTabPanel-root': {
    padding: '0px 0px',
  },
};

function GraphTable({ categoryList, queryFormInitiatorIds = [], queryCategoryFormGroupIds = [] }) {
  const [apiCalled, setApiCalled] = useState(false);
  const [tab, updateTab] = useTabState(null);
  const overviewData = Map();
  const { columnFilter, handleCheckbox, columnData } = useCustomHooksForTable();
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState(null);

  const formIds = overviewData.get('qapcFormIds', List()).map((item) => item.get('_id', ''));
  const [searchKey, filterIds, handleChange] = useSearch(overviewData);
  const debouncedSearchKey = useDebounce(searchKey, 500);

  const [
    pagination,
    formId,
    switchPagination,
    skipPagination,
    handleChangePagination,
  ] = usePaginationHook(searchKey ? filterIds : formIds, overviewData, searchKey);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const selectedRole = useSelector(selectSelectedRole);
  const qualityAssuranceList = useSelector(selectQualityAssuranceList);
  const roleId = selectedRole.getIn(['_role_id', '_id'], '');
  const { selectedCalendar } = useCustomCalendarListHook();
  const [filterQuery] = useState(
    Map({
      program: '',
      course: '',
    })
  );

  const params = {
    institutionCalenderId: selectedCalendar,
    limit: pagination.get('perPageLimit', 5),
    formType: 'submitted',
    searchKey: debouncedSearchKey,
    categoryId: tab,
    pageNo: pagination.get('currentPage', 1),
    subModuleType: 'QA Dashboard',
    ...(queryFormInitiatorIds.length && { queryFormInitiatorIds }),
    ...(queryCategoryFormGroupIds.length && { queryCategoryFormGroupIds }),
  };

  useEffect(() => {
    if (tab !== null && apiCalled) {
      dispatch(getFormList(params));
    }
  }, [tab, roleId, debouncedSearchKey]);

  const handleMouseEnter = () => {
    if (!apiCalled && categoryList.getIn([0, '_id'], '') !== '') {
      dispatch(getRpAction());
      setApiCalled(true);
      updateTab('', categoryList.getIn([0, '_id']));
    }
  };

  return (
    <section className="bg-white rounded p-3" onMouseEnter={handleMouseEnter}>
      <header className="d-flex align-items-center gap-20">
        <div className="f-20 fw-500 flex-grow-1">All Categories</div>
        <div className="w-25">
          <MaterialInput
            value={searchKey}
            changed={handleChange}
            elementType={'materialSearch'}
            placeholder={'Search Program, Course Name...'}
            labelclassName={'searchLeft'}
            labelclass={'searchLeft'}
            addClass="f-14 fw-400 color-lt-gray p-0 flex-grow-1"
          />
        </div>

        <div className="border rounded columnImg mb-1 bgLGrey">
          <img src={view_column} onClick={handleClick} className="cursor-pointer" />
        </div>
        <MenuColumnOrder
          anchorEl={anchorEl}
          handleClose={handleClose}
          columns={columnData}
          columnFilter={columnFilter}
          handleCheckbox={handleCheckbox}
          open={open}
        />
      </header>
      <section className="pt-2">
        <TabContext value={tab}>
          <Box>
            <TabList
              aria-label="lab API tabs example"
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
              sx={{ ...tabBorderNone, ...CategoryTabSx, ...tabListSX }}
              style={{
                minWidth: 0,
                minHeight: 10,
                padding: '5px',
              }}
              onChange={updateTab}
            >
              {categoryList.map((categories) => {
                return (
                  <Tab
                    key={categories.get('_id', '')}
                    label={categories.get('categoryName', '')}
                    value={categories.get('_id', '')}
                    sx={{ textTransform: 'none' }}
                  />
                );
              })}
            </TabList>
          </Box>

          <TabPanel value={tab} sx={tabPadding}>
            {/* <CourseTable
              columns={columns}
              columnFilter={columnFilter}
              courseList={qualityAssuranceList.get('formListData', List())}
            /> */}
            <CategoriesFormList
              columns={columnData}
              columnFilter={columnFilter}
              formListData={qualityAssuranceList}
              routeToUpdate={() => {}}
              filterQuery={filterQuery}
              pages={[pagination, formId, switchPagination, skipPagination, handleChangePagination]}
              formIds={formIds}
              tab={tab}
              selectedCalendar={selectedCalendar}
              handleMouseEnter={handleMouseEnter}
            />
          </TabPanel>
        </TabContext>
      </section>
    </section>
  );
}
export default GraphTable;

const submissionStatus = fromJS({
  draft: {
    text: 'Save as Draft',
    bgColor: '#F3F4F6',
    color: '#374151',
  },
  resubmit: {
    text: 'Resubmission',
    bgColor: '#fef3c7',
    color: '#D97706',
  },
  submitted: {
    text: 'Submitted',
    bgColor: '#EEF2FF',
    color: '#4338CA',
  },
});

const BadgeText = ({ data }) => {
  const visibleData = data.slice(0, 2);
  const extraCount = data.size > 2 ? data.size - 2 : 0;

  return (
    <>
      {visibleData.map((text, index) => {
        const titleData = data.map((d) => d).join(', ');
        return (
          <div
            className={`d-flex text-uppercase gap-8 align-items-center ${
              index !== 0 ? 'pt-2' : ''
            }`}
            key={index}
          >
            <div className="f-10 rounded px-2 py-1 l-blue d-flex justify-content-center align-items-center">
              {text}
            </div>
            {visibleData.size === index + 1 && extraCount > 0 && (
              <Tooltip title={`${titleData}`}>
                <div className="f-10 d-flex justify-content-center align-items-center count">
                  {`+${extraCount}`}
                </div>
              </Tooltip>
            )}
          </div>
        );
      })}
    </>
  );
};

const Incorporate = ({ form }) => {
  const incorporateWith = form
    .get('incorporateWith', List())
    .map((data) => data.get('formName', ''));
  const incorporateFrom = form
    .get('incorporateFrom', List())
    .map((data) => data.get('formName', ''));

  return (
    <>
      {incorporateWith.size ? (
        <div className="d-flex text-uppercase gap-8 align-items-center pt-2">
          <div className="f-10 text-lGrey fw-400">WITH:</div>
          <div className="f-10 rounded px-2 py-1 bgAsh d-flex justify-content-center align-items-center">
            {incorporateWith.get(0, '')}
          </div>
          {incorporateWith.size > 1 ? (
            <div className="f-10 d-flex justify-content-center align-items-center mr-2 count">{`+${
              incorporateWith.size - 2
            }`}</div>
          ) : (
            <></>
          )}
        </div>
      ) : (
        <></>
      )}
      {incorporateFrom.size ? (
        <div className="d-flex text-uppercase gap-8 align-items-center pt-2">
          <div className="f-10 text-lGrey fw-400">FROM:</div>
          <div className="f-10 rounded px-2 py-1 bgAsh d-flex justify-content-center align-items-center">
            {incorporateFrom.get(0, '')}
          </div>
          {incorporateFrom.size > 1 ? (
            <div className="f-10 d-flex justify-content-center align-items-center mr-2 count">{`+${
              incorporateFrom.size - 1
            }`}</div>
          ) : (
            <></>
          )}
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

function CategoriesFormList({
  columns,
  routeToUpdate,
  pages,
  formIds,
  tab,
  selectedCalendar,
  handleMouseEnter,
}) {
  // const [formDetails, setFormDetails] = useState(Map());
  // const [formId, setFormId] = useState('');

  const [
    pagination,
    // formId,
    switchPagination,
    skipPagination,
    handleChangePagination,
  ] = pages;

  // const dispatch = useDispatch();

  // const handleRemove = (form, type) => {
  //   setFormDetails(formDetails);

  //   const mergedFormId = form
  //     .get('mergedFormId', List())
  //     .map((id) => id.get('formInitiatorId', ''));

  //   const requestBody = {
  //     formInitiatorIds: [form.get('_id', ''), ...mergedFormId],
  //     archive: type === 'archive' ? true : false,
  //     ...(type === 'delete' && { isDeleted: true }),
  //   };
  //   console.log({ dispatch, requestBody });
  //   // const callBack = () => {
  //   //   dispatch(getFormList(tab, selectedCalendar));
  //   // };

  //   // dispatch(updateFormStatus(requestBody, callBack, type));
  // };

  // const { open, openOrClose } = useCustomCookie();

  // const handleEditClick = (id) => {
  //   openOrClose();
  //   // dispatch(getSingleForm(id));
  //   setFormId(id);
  // };

  // const rpActions = useSelector(selectRpActions);

  // const { edit, delete2, archive } = useQ360PermissionHook(rpActions);

  const formAssuranceList = useSelector(selectFormAssuranceList);

  const formListData = formAssuranceList.get('filterMatchingFormData', List());

  const getCourseIdAndName = ({ form }) => {
    const level = form.get('level', '');
    switch (level) {
      case 'institution': {
        return (
          <>
            <div className="text-dBlue underlined-text fw-500 f-14">
              {form.get('institutionName', '')}
            </div>
            <div className="f-12 fw-400 text-lGrey">{form.get('formName', '')}</div>
          </>
        );
      }

      case 'program':
        return (
          <div className="f-12 fw-400 text-lGrey">
            {form.get('programName', '')} / <br />
            {form.get('formName', '')}
          </div>
        );
      default:
        return (
          <div className="f-12 fw-400 text-lGrey">
            {form.get('programName', '')} / <br />
            {form.get('formName', '')} / {form.get('selectedGroupName', List()).join(', ')}
          </div>
        );
    }
  };
  return (
    <React.Fragment>
      <div className="qapc-createTable">
        <table className="mt-4 qapcCreateTable">
          <thead>
            {columns.map((column) => {
              if (column.get('showCheck', false))
                return (
                  <th
                    key={column}
                    className={`${column.get(
                      'class',
                      ''
                    )} qapcCreateTableTH f-14 fw-500 text-dGrey border-bottom`}
                  >
                    <div className={`${column.get('className', '')} my-2`}>
                      {column.get('name')}
                    </div>
                  </th>
                );
              else <></>;
            })}
          </thead>
          <tbody>
            {formListData.size === 0 ? (
              <tr>
                <td colSpan={20} className="bg-white border-none">
                  <div className="text-dGrey f-14 text-center w-100">No Data Found</div>
                </td>
              </tr>
            ) : (
              formListData
                // .entrySeq()
                // .filter(([key, _]) => formId.includes(key))
                // .map(([key, form], index) => {
                .map((form, index) => {
                  // const editAccess = form.get('actionIds', List()).includes(edit);
                  // const deleteAccess = form.get('actionIds', List()).includes(delete2);
                  // const archiveAccess = form.get('actionIds', List()).includes(archive);
                  const level = form.get('level', '');
                  const submissionCurrentStatus = form.get('submissionStatus', '');
                  const submissionData = submissionStatus.get(submissionCurrentStatus, Map());
                  const tableBody = {
                    'S.NO': <>{index + 1}</>,
                    'Course ID / Name': (
                      <>
                        <div
                          className="my-2 cursor-pointer"
                          onClick={routeToUpdate({
                            index,
                            id: form.get('_id', ''),
                            categoryFormId: form.get('categoryFormId', ''),
                            formAttachment: form.get('formAttachment', ''),
                            mergedFormId: form.get('mergedFormId', ''),
                            level,
                            incorporateFrom: form.get('incorporateFrom', List()),
                            incorporateWith: form.get('incorporateWith', List()),
                          })}
                        >
                          <div className="f-12 fw-400 text-lGrey">
                            {form.get('categoryName', '')}
                          </div>
                          <div className="text-dBlue underlined-text fw-500 f-14">
                            {form.get('courseName', '') || form.get('curriculumName', '')}
                          </div>
                          {getCourseIdAndName({ form })}
                        </div>
                      </>
                    ),
                    Display: (
                      <>
                        <BadgeText
                          data={
                            form.get('displayName', List()).size > 0
                              ? form.get('displayName', List())
                              : form.get('displaySection', List())
                          }
                        />
                      </>
                    ),
                    Incorporate: (
                      <>
                        <Incorporate form={form} />
                      </>
                    ),
                    Submission: (
                      <>
                        <div
                          className={'rounded px-2 py-1 f-12 fw-400 text-dGrey text-center'}
                          style={{
                            backgroundColor: submissionData.get('bgColor', ''),
                            color: submissionData.get('color', ''),
                          }}
                        >
                          {submissionData.get('text', '')}
                        </div>
                        <div className="rounded px-2 py-1 text-lGrey f-12 fw-400 text-center">
                          {/* {submissionCurrentStatus !== 'draft'
                          ? formatDate(new Date(form.get('submissionDate', '')))
                          : ''} */}
                          {formatDate(new Date(form.get('submissionDate', '')))}
                        </div>
                      </>
                    ),
                    Approvers: (
                      <>
                        <Approvers
                          approvers={form.getIn(['categoryFormId', 'approvalLevel'], List())}
                          approversData={form.get('approverList', List())}
                          submissionStatus={submissionCurrentStatus}
                          approverStatus={form.get('status', 'draft')}
                        />
                      </>
                    ),
                    Attachment: (
                      <>
                        {form.get('attachmentCount', 0) > 0 ? (
                          <div className="d-flex align-items-center">
                            <AttachFileIcon sx={{ fontSize: '16px' }} />
                            <div className="f-14 fw-400 text-dGrey pr-1 ">
                              {form.get('attachmentCount', 0)}
                            </div>{' '}
                            <ShowEvidenceDocument
                              formInitiatorId={form.get('_id')}
                              formName={form.get('formName', '')}
                            />
                          </div>
                        ) : (
                          <div>-</div>
                        )}
                      </>
                    ),
                    Status: (
                      <>
                        <Status approverStatus={form.get('status', 'draft')} />
                      </>
                    ),
                    ' ': (
                      <>
                        {/* <Badge badgeContent={4} sx={{ fontSize: '10px' }} color="primary">
                        <CommentIcon color="primary" />
                      </Badge> */}
                      </>
                    ),
                    '  ': (
                      <>
                        {/* {tab === 'submitted' ? (
                          <>
                            {editAccess && (
                              <EditIcon
                                className="ml-1 f-16 cursor-pointer"
                                onClick={() => handleEditClick(form.get('_id', ''))}
                              />
                            )}

                            {deleteAccess && (
                              <DeleteIcon
                                className="ml-1 f-16 cursor-pointer"
                                onClick={() => handleRemove(form, 'delete')}
                              />
                            )}
                            {archiveAccess && (
                              <ArchiveIcon
                                className="ml-1 f-16 cursor-pointer"
                                onClick={() => handleRemove(form, 'archive')}
                              />
                            )}
                          </>
                        ) : (
                          <UnarchiveIcon
                            className="ml-1 f-16 cursor-pointer"
                            onClick={() => handleRemove(form, 'unArchive')}
                          />
                        )} */}
                        {/* <MoreVertIcon className="mt-1" /> */}
                      </>
                    ),
                  };
                  return (
                    <tr key={form.get('_id', '')} className="py-2">
                      {columns.map((column) => {
                        if (column.get('showCheck', false))
                          return (
                            <th
                              key={column}
                              className={`${column.get(
                                'class',
                                ''
                              )} qapcCreateTableTH f-14 fw-500 text-dGrey border-bottom ${
                                column.get('name', '') === 'Status' ? 'pr-2' : ''
                              }`}
                            >
                              {tableBody[column.get('name', '')]}
                            </th>
                          );
                        else <></>;
                      })}
                    </tr>
                  );
                })
            )}
          </tbody>
        </table>
      </div>
      <PaginationQ360
        pagination={pagination}
        switchPagination={switchPagination}
        skipPagination={skipPagination}
        handleChangePagination={handleChangePagination}
        formId={formIds}
        count={formAssuranceList.get('submittedCount', 0)}
      />
    </React.Fragment>
  );
}

const finalStatus = fromJS({
  'Yet to Start': {
    color: '#4B5563',
    bgColor: '#F3F4F6',
    text: 'Yet to Start',
  },
  Rejected: {
    bgColor: '#FEF2F2',
    color: '#EF4444',
    text: 'Rejected',
  },
  draft: {
    color: '#fff',
    bgColor: '#4b5563',
    text: 'In Draft',
  },
  'In Review': {
    color: '#d97706',
    bgColor: '#fef3c7',
    text: 'In Review',
  },
  Approved: {
    color: '#16a34a',
    bgColor: '#dcfce7',
    text: 'Approved',
  },
  published: {
    color: '#fff',
    bgColor: '#147afc',
    text: 'Published',
  },
  resubmit: {
    color: '#d97706',
    bgColor: '#fef3c7',
    text: 'Re - Submit',
  },
});

function Status({ approverStatus }) {
  const data = finalStatus.get(approverStatus, Map());
  return (
    <div
      className={'rounded px-2 py-1 f-12 fw-400  text-center'}
      style={{
        backgroundColor: data.get('bgColor', ''),
        color: data.get('color', ''),
      }}
    >
      {data.get('text', '') === 'Re - Submit' && <div>Reviewer asked to</div>}
      {data.get('text', '') === 'Yet to Start' ? (
        <div>
          <div className="text-grey">Approver</div>
          <div className="fw-500">Yet to Start</div>
        </div>
      ) : (
        <div className="pr-2 fw-400 py-1">{data.get('text', '')}</div>
      )}
    </div>
  );
}
const noLevel = '-- --';
const approversStatus = fromJS({
  notSubmitted: {
    text: noLevel,
    bgColor: '#FFFBEB',
    color: '#D97706',
    Icon() {
      return <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Submitted: {
    text: 'Level',
    bgColor: '#FFFBEB',
    color: '#D97706',
    Icon() {
      return <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Forwarded: {
    text: 'Level',
    bgColor: '#DCFCE7',
    color: '#166534',
    Icon() {
      return <DoneIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  published: {
    text: 'Level',
    bgColor: '#DCFCE7',
    color: '#166534',
    Icon() {
      return <DoneIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Approved: {
    text: 'Level',
    bgColor: '#DCFCE7',
    color: '#166534',
    Icon() {
      return <DoneIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  pending: {
    text: 'Level',
    bgColor: '#FFFBEB',
    color: '#D97706',
    Icon() {
      return <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  Rejected: {
    text: 'Level',
    bgColor: '#fef2f2',
    color: '#b91c1c',
    Icon() {
      return <CloseIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
    },
  },
  re_submit: {
    text: 'Level',
    bgColor: '#E0E7FF',
    color: '#4338CA',
    Icon() {
      return (
        <PriorityHighRounded sx={{ fontSize: '16px', color: '#818CF8' }} className="ml-2 pt-1" />
      );
    },
  },
});
const Dummy = () => <QuestionMarkIcon sx={{ fontSize: '16px' }} className="ml-2 pt-1" />;
function Approvers({ approvers, approversData, submissionStatus }) {
  return (
    <div className="approvers mx-2 my-1">
      {approvers.map((element, elementIndex) => {
        const levelNumber = element.get('levelNumber', 0);
        const findApprover =
          approversData.find((approver) => approver.get('level', 0) === levelNumber) || Map();
        const levelStatus = findApprover.get('levelStatus', '');
        const Icon = approversStatus.getIn([levelStatus, 'Icon'], Dummy);
        return (
          <div
            key={elementIndex}
            className={`border rounded py-1 text-center  `}
            style={{
              backgroundColor: approversStatus.getIn([levelStatus, 'bgColor'], '#FFFBEB'),
              color: approversStatus.getIn([levelStatus, 'color'], '#D97706'),
            }}
          >
            {submissionStatus === 'submitted' || submissionStatus === 'resubmission' ? (
              <>
                Level <span>{levelNumber}</span>
              </>
            ) : (
              noLevel
            )}
            <span className="mt-1">
              <Icon />
            </span>
          </div>
        );
      })}
    </div>
  );
}
