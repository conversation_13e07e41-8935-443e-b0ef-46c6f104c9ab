import React, { Fragment, forwardRef } from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Divider } from '@mui/material';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import DeleteParameterModal from '../../DeleteParameterModal';
import CriteriaLayout from './CriteriaLayout';
import { jsUcfirstAll } from 'utils';
import EditParameterModal from './EditParameterModal';

function Punctuality({ parameter, parameterIndex, render }, parameterRef) {
  return (
    <Fragment>
      <Accordion
        disableGutters
        sx={{
          background: 'white !important',
          boxShadow: 'none',
          marginTop: '16px',
          border: '1px solid #d1d5db',
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1a-content"
          id="panel1a-header"
          className="px-3 py-2"
        >
          <div>
            <div className="bold">{jsUcfirstAll(parameter.get('name', ''))}</div>
            <div>Weightage %:{parameter.get('weight', '')}</div>
          </div>
          <div className="ml-auto d-flex align-items-center">
            <EditParameterModal parameter={parameter} />
            <DeleteParameterModal name={parameter.get('name', '')} _id={parameter.get('_id', '')} />
          </div>
        </AccordionSummary>
        <AccordionDetails>
          <Divider />
          <div className="text-gray f-14 my-3">Default Criteria’s</div>

          <div className="grid_criteria_leader">
            {parameter.get('criteria', List()).map((criteria, index) => (
              <CriteriaLayout
                ref={parameterRef}
                key={criteria.get('_id') + index}
                render={render}
                criteria={criteria}
                criteriaIndex={index}
                parameterIndex={parameterIndex}
              />
            ))}
          </div>
        </AccordionDetails>
      </Accordion>
    </Fragment>
  );
}
export default forwardRef(Punctuality);
Punctuality.propTypes = {
  parameter: PropTypes.instanceOf(Map),
  parameterIndex: PropTypes.number,
  render: PropTypes.number,
};
