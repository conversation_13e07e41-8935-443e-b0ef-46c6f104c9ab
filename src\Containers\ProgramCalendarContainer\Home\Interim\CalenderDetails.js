import React, { Fragment, useEffect, useState, Suspense } from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';
import { useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';

import { convertHijiriYear } from '../../../../utils';
import Tooltip from '../../../../_components/UI/Tooltip/Tooltip';
import { PrimaryButton, FlexWrapper, Null, DisplaySemesterTag } from '../../Styled';
import {
  interimDashboard,
  interimLandingApi,
  interimYearDataGet,
  interimChangeYear,
  interimChangeTitle,
  interimChangeSemester,
  interimSetDefaultParams,
} from '../../../../_reduxapi/actions/interimCalendar';
import { ucFirst, showArabicMailShow } from '../../../../utils';
import NavRowButtons from '../../UtilityComponents/Interim/NavRowButtons';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import useCalendar from 'Hooks/useCalendarHook';
const EventsListModal = React.lazy(() => import('../../Modal/EventsList/EventsList'));

const DetailWrapper = styled.div`
  display: flex;
  align-items: center;
`;

const Title = styled.div`
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  text-transform: capitalize;
  opacity: 60%;
`;

const CalenderDetails = (props) => {
  const {
    interimDashboard,
    interimLandingApi,
    interimChangeTitle,
    interimChangeSemester,
    title,
    active,
    active_semesters,
    id,
    total,
    programId,
    token,
    currentProgramCalendarId,
    curriculumYears,
    isFoundation,
    clickedPDF,
    iframeShow,
    calendarStatus,
    currentPGAccess,
    activeInstitutionCalendar,
  } = props;
  const history = useHistory();

  const [sTabs, setSTabs] = useState(null);
  const [courseBtnActive, setCourseBtnActive] = useState(false);
  const [reviewBtnActive, setReviewBtnActive] = useState(false);
  const [eventBtnActive, setEventBtnActive] = useState(false);
  const [eventListShow, setEventListShow] = useState(false);

  const [hijiriTitle, setTitle] = useState(''); //eslint-disable-line

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlIcd = params.get('icd');
  let urlName = params.get('pname');
  let urlPgId = params.get('programid');
  const { isCurrentCalendar } = useCalendar();
  const currentCalendar = isCurrentCalendar();

  useEffect(() => {
    let defaultIcd = id;
    if (urlIcd) {
      defaultIcd = urlIcd;
    }
    if (defaultIcd !== null) {
      //history.push(`?year=${defaultYear}&icd=${defaultIcd}&programid=${programId}`);
      //interimSetDefaultParams(defaultYear,defaultIcd);
    }
  }, []); // eslint-disable-line

  useEffect(() => {
    if (isFoundation && id !== null) {
      //history.push(`?year=year1&icd=${id}&programid=${programId}`);
      //interimSetDefaultParams('year1',id);
    }
  }, [isFoundation, id]);

  useEffect(() => {
    if (id !== null && programId !== null) {
      //history.push(`?year=${active}&icd=${id}&programid=${programId}`);
    }
  }, [active, id, programId]);

  useEffect(() => {
    setTimeout(() => {
      if (
        programId !== null &&
        id !== null &&
        activeInstitutionCalendar &&
        !activeInstitutionCalendar.isEmpty()
      ) {
        interimLandingApi(programId, id, activeInstitutionCalendar.get('_id'));
      }
    }, 2000);
  }, [interimDashboard, programId, interimLandingApi, token, id, activeInstitutionCalendar]);

  useEffect(() => {
    let hijiriYear = '';
    if (title !== '' && title !== null && title !== undefined) {
      let splitYear = title.split('-');
      let year1 = splitYear[0];
      let year2a = splitYear[1];
      let sliceEndYear = convertHijiriYear(year2a);
      sliceEndYear = sliceEndYear.toString();
      sliceEndYear = sliceEndYear.substr(2, 4);
      hijiriYear =
        year1 +
        ' - ' +
        year2a.substr(2, 4) +
        ' G (' +
        convertHijiriYear(year1) +
        ' - ' +
        sliceEndYear +
        ' H)';
      setTitle(hijiriYear);
    }
  }, [title]);

  useEffect(() => {
    let activeSemsesters = props[active]['semesters'];
    let activeSemester = active_semesters;
    if (activeSemsesters !== undefined) {
      let semesterKeys = Object.keys(activeSemsesters);
      let semesterTabs = [];
      if (semesterKeys.length > 0) {
        for (let semCount = 0; semCount < semesterKeys.length; semCount++) {
          semesterTabs.push(
            <DisplaySemesterTag
              key={semCount}
              className={activeSemester === semesterKeys[semCount] ? `light` : null}
              onClick={() => interimChangeSemester(semesterKeys[semCount])}
            >
              {ucFirst(semesterKeys[semCount]).replaceAll('Semester', t('semester'))}
            </DisplaySemesterTag>
          );
        }
      }
      setSTabs(semesterTabs);

      let courseBtnActive1 = false;
      let eventBtnActive1 = false;
      if (
        activeSemsesters &&
        activeSemsesters !== undefined &&
        activeSemsesters[active_semesters] !== undefined
      ) {
        let semester1 = activeSemsesters[active_semesters];
        if (semester1 && semester1.length > 0) {
          semester1.forEach((check) => {
            courseBtnActive1 =
              check.start_date !== undefined &&
              check.end_date !== undefined &&
              check.start_date !== '' &&
              check.end_date !== '';
            eventBtnActive1 = check.events.length > 0;
          });
        }
      }
      setCourseBtnActive(courseBtnActive1);
      setEventBtnActive(eventBtnActive1);

      let reviewBtnActive1 = [];
      if (curriculumYears && curriculumYears.length > 0) {
        curriculumYears.forEach((year) => {
          let activeSemsesters1 = props['year' + year]['semesters'];
          if (activeSemsesters1 !== undefined) {
            let semesterKeys1 = Object.keys(activeSemsesters1);
            if (semesterKeys1.length > 0) {
              for (let semCount = 0; semCount < semesterKeys1.length; semCount++) {
                let checkSemester = activeSemsesters1[semesterKeys1[semCount]];
                if (checkSemester && checkSemester.length > 0) {
                  checkSemester.forEach((check) => {
                    let reviewBtnActive1a =
                      check.start_date !== undefined &&
                      check.end_date !== undefined &&
                      check.start_date !== '' &&
                      check.end_date !== '' &&
                      (check.course.length > 0 || check.rotation_course.length > 0);
                    reviewBtnActive1.push(reviewBtnActive1a);
                  });
                }
              }
            }
          }
        });
      }
      let filterReview = reviewBtnActive1 && reviewBtnActive1.includes(false) ? false : true;
      setReviewBtnActive(filterReview);
    }
  }, [props]); // eslint-disable-line

  const eventsListToggle = () => setEventListShow(!eventListShow);

  const eventData = props[active]['semesters'][active_semesters];
  return (
    <Fragment>
      {eventListShow === true && (
        <Suspense fallback={''}>
          <EventsListModal
            show={eventListShow}
            clicked={eventsListToggle}
            data={eventData}
            currentSemester={active_semesters}
            isInterim={true}
            iframeShow={iframeShow}
            programId={urlPgId}
          />
        </Suspense>
      )}

      <FlexWrapper className="nav_bg">
        <NavRowButtons theme="active" color="white" />
      </FlexWrapper>
      <DetailWrapper>
        {!iframeShow && (
          <Title>
            <div className="ml-5">
              {`Academic Year ${(() => {
                if (!activeInstitutionCalendar.isEmpty()) {
                  const [yearStart, yearEnd] = activeInstitutionCalendar
                    .get('calendar_name', '')
                    .split('-');
                  const hijiriYearStart = convertHijiriYear(yearStart).toString();
                  const hijiriYearEnd = convertHijiriYear(yearEnd).toString();

                  return `${yearStart} - ${yearEnd.substr(2, 4)}${
                    showArabicMailShow()
                      ? ` G (${hijiriYearStart} - ${hijiriYearEnd.substr(2, 4)} H)`
                      : ''
                  }`;
                }
              })()}`}
            </div>
            {/* <IconWrapper
              className={index > 0 && 'avail'}
              onClick={() => {
                if (institutionCalendar[index - 1] !== undefined) {
                  interimIndexChange(
                    index - 1,
                    total - 1,
                    urlPgId,
                    institutionCalendar[index - 1]._id
                  );
                  history.push(`?year=year2&icd=${id}&programid=${urlPgId}&pname=${urlName}`);
                }
              }}
            >
              {' '}
              <i className={`fa ${lang === 'ar' ? 'fa-chevron-right' : 'fa-chevron-left'}`}></i>
            </IconWrapper>{' '}
            <Trans i18nKey={'academic_year'}></Trans> {hijiriTitle}
            <IconWrapper
              className={index < total - 1 && 'avail'}
              onClick={() => {
                if (institutionCalendar[index + 1] !== undefined) {
                  interimIndexChange(
                    index + 1,
                    total - 1,
                    urlPgId,
                    institutionCalendar[index + 1]._id
                  );
                  history.push(`?year=year2&icd=${id}&programid=${urlPgId}&pname=${urlName}`);
                }
              }}
            >
              {' '}
              <i className={`fa ${lang === 'ar' ? 'fa-chevron-left' : 'fa-chevron-right'}`}></i>
            </IconWrapper>{' '}*/}
          </Title>
        )}
        <Null />
        <Tooltip title={t('program_calendar.events_list')}>
          <PrimaryButton
            className={
              eventBtnActive && currentProgramCalendarId !== null ? 'light' : 'light disable'
            }
            disabled={!(eventBtnActive && currentProgramCalendarId !== null)}
            onClick={eventsListToggle}
          >
            {' '}
            {/* <i className="fa fa-upload"></i> */}
            <Trans i18nKey={'program_calendar.events_list'}></Trans>{' '}
          </PrimaryButton>
        </Tooltip>

        {currentCalendar && currentPGAccess && !iframeShow && (
          <Tooltip title={t('program_calendar.set_calendar')}>
            <PrimaryButton
              className={
                CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
                  ? 'bordernone'
                  : 'light disable'
              }
              disabled={
                total === 0 &&
                !CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
              }
              onClick={() => {
                if (
                  CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings')
                ) {
                  interimChangeTitle(t('role_management.role_actions.Calendar Settings'));
                  //interimChangeYear("year2");
                  history.push(
                    `/interim-calender?programid=${urlPgId}&calendarid=${id}&year=${active}&pname=${urlName}`
                  );
                }
              }}
            >
              {' '}
              <Trans i18nKey={'program_calendar.calendar_settings'}></Trans>
            </PrimaryButton>
          </Tooltip>
        )}
      </DetailWrapper>
      {currentProgramCalendarId !== null && (
        <>
          {iframeShow ? (
            <FlexWrapper>{sTabs}</FlexWrapper>
          ) : (
            <FlexWrapper style={{ float: 'left' }}>{sTabs}</FlexWrapper>
          )}
          <FlexWrapper>
            <Null />
            {currentCalendar && currentPGAccess && !iframeShow && (
              <>
                <Tooltip
                  title={
                    courseBtnActive
                      ? t('role_management.role_actions.Add Course')
                      : t('program_calendar.set_level_in_calendar_settings')
                  }
                >
                  <PrimaryButton
                    className={
                      courseBtnActive &&
                      currentProgramCalendarId !== null &&
                      CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')
                        ? 'light'
                        : 'light disable'
                    }
                    disabled={
                      !(
                        courseBtnActive &&
                        currentProgramCalendarId !== null &&
                        CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')
                      )
                    }
                    onClick={() => {
                      interimChangeTitle('Add Course');
                      history.push(
                        `/interim-course?programid=${urlPgId}&calendarid=${id}&year=${active}&semester=${active_semesters}&pname=${urlName}`
                      );
                    }}
                  >
                    {' '}
                    <i className="fas fa-plus"></i>{' '}
                    <Trans i18nKey={'program_calendar.course'}></Trans>
                  </PrimaryButton>
                </Tooltip>

                {currentCalendar && calendarStatus !== 'published' && (
                  <Tooltip
                    title={
                      reviewBtnActive
                        ? t('role_management.role_actions.Review Calendar')
                        : t('program_calendar.set_level_in_calendar_settings')
                    }
                  >
                    {/* && calendarStatus!=='published' */}
                    <PrimaryButton
                      className={
                        reviewBtnActive &&
                        currentProgramCalendarId &&
                        CheckPermission(
                          'pages',
                          'Program Calendar',
                          'Dashboard',
                          'Review Calendar'
                        ) !== null
                          ? 'bordernone'
                          : 'bgdisabled'
                      }
                      disabled={
                        !(
                          reviewBtnActive &&
                          currentProgramCalendarId !== null &&
                          CheckPermission(
                            'pages',
                            'Program Calendar',
                            'Dashboard',
                            'Review Calendar'
                          )
                        )
                      }
                      onClick={() => {
                        history.push(
                          `/programcalendar/vice-dean?id=${currentProgramCalendarId}&title=${title}&year=${active}&name=${urlName}&programid=${urlPgId}`
                        );
                      }}
                    >
                      <Trans i18nKey={'role_management.role_actions.Review Calendar'}></Trans>
                    </PrimaryButton>
                  </Tooltip>
                )}
              </>
            )}

            {!iframeShow && (
              <Tooltip
                title={
                  courseBtnActive
                    ? t('program_calendar.export_as_pdf')
                    : t('program_calendar.set_level_in_calendar_settings')
                }
              >
                <PrimaryButton
                  className={
                    reviewBtnActive && currentProgramCalendarId !== null ? 'light' : 'light disable'
                  }
                  disabled={!(reviewBtnActive && currentProgramCalendarId !== null)}
                  onClick={clickedPDF}
                >
                  {' '}
                  {/* <i className="fa fa-upload"></i> */}
                  <Trans i18nKey={'program_calendar.export'}></Trans>
                </PrimaryButton>
              </Tooltip>
            )}
          </FlexWrapper>
        </>
      )}
    </Fragment>
  );
};

CalenderDetails.propTypes = {
  interimDashboard: PropTypes.func,
  interimLandingApi: PropTypes.func,
  interimChangeTitle: PropTypes.func,
  interimChangeSemester: PropTypes.func,
  title: PropTypes.string,
  active: PropTypes.string,
  active_semesters: PropTypes.string,
  id: PropTypes.string,
  total: PropTypes.number,
  index: PropTypes.number,
  programId: PropTypes.string,
  token: PropTypes.string,
  currentProgramCalendarId: PropTypes.string,
  institutionCalendar: PropTypes.object,
  curriculumYears: PropTypes.object,
  isFoundation: PropTypes.bool,
  clickedPDF: PropTypes.func,
  iframeShow: PropTypes.bool,
  calendarStatus: PropTypes.object,
  currentPGAccess: PropTypes.bool,
  activeInstitutionCalendar: PropTypes.object,
};

const mapStateToProps = ({ calender, auth, interimCalendar }) => ({
  active: interimCalendar.active_year,
  active_semesters: interimCalendar.active_semesters,
  year1: interimCalendar.year1,
  year2: interimCalendar.year2,
  year3: interimCalendar.year3,
  year4: interimCalendar.year4,
  year5: interimCalendar.year5,
  year6: interimCalendar.year6,
  title: interimCalendar.academic_year_name,
  navBarTitle: interimCalendar.nav_bar_title || calender.nav_bar_title,
  isFoundation:
    calender.nav_bar_title !== ''
      ? calender.nav_bar_title.toLowerCase().indexOf('foundation') > -1
      : false,
  id: interimCalendar.institution_Calender_Id,
  index: interimCalendar.acad_index,
  total: interimCalendar.total_academic_year_inDB,
  isAuthenticated: auth.token !== null,
  token: auth.token !== null ? auth.token.replace(/"/g, '') : null,
  programId: calender.programId,
  userRole: auth.loggedInUserData.role,
  currentProgramCalendarId: interimCalendar.currentProgramCalendarId,
  institutionCalendar: interimCalendar.institution_calendar,
  curriculumYears: interimCalendar.curriculum_years,
  calendarStatus:
    interimCalendar.calendarStatus !== undefined ? interimCalendar.calendarStatus : '',
});

export default connect(mapStateToProps, {
  interimDashboard,
  interimLandingApi,
  interimYearDataGet,
  interimChangeYear,
  interimChangeTitle,
  interimChangeSemester,
  interimSetDefaultParams,
})(CalenderDetails);
