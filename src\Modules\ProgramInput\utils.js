import ExcelJS from 'exceljs';
import FileSaver from 'file-saver';

/**
 * Generate and download an Excel file from an array of objects.
 * @param {Array} data - The data array where each object represents a row.
 * @param {string} fileName - The name of the Excel file to download.
 */
export const exportToExcel = (data, fileName) => {
  // Initialize a new workbook and worksheet
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('data');

  // Define columns dynamically from the sample data keys
  const columns = Object.keys(data[0]).map((key) => ({
    header: key,
    key: key,
    width: 30, // Adjust column width if needed
  }));

  worksheet.columns = columns;

  // Add rows to the worksheet
  data.forEach((row) => {
    worksheet.addRow(row);
  });

  // Generate and save the Excel file
  workbook.xlsx.writeBuffer().then((buffer) => {
    const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    const dataBlob = new Blob([buffer], { type: fileType });
    FileSaver.saveAs(dataBlob, `${fileName}.xlsx`);
  });
};
