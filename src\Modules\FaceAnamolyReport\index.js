import React from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { fromJS } from 'immutable';
import useWindowDimensions from '_components/UI/PageSize/PageSize';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
function FaceAnamolyReport() {
  const items = [{ to: '#', label: 'Face Anomaly Purification' }];
  const authDataArray = useSelector((state) => state?.auth);
  const authData = fromJS(authDataArray);
  const token = authData.getIn(['loggedInUserData', 'tokens', 'access', 'token'], '');
  const institutionId = authData.getIn(
    ['loggedInUserData', 'services', 'REACT_APP_INSTITUTION_ID'],
    ''
  );
  const { height } = useWindowDimensions();
  return (
    <React.Fragment>
      <Breadcrumb>
        {items &&
          items.map(({ to, label }, index) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={index} to={to}>
              {label}
            </Link>
          ))}
      </Breadcrumb>
      <div className="pb-5">
        <div className="container-fluid">
          <div className="row">
            <div className="col-md-12 p-0">
              <iframe
                src={`${
                  process.env.REACT_APP_BASE_PATH || '/'
                }/anamoly_approve_tool/app.html?baseUrl=${
                  process.env.REACT_APP_API_URL
                }&authToken=${token}&institutionId=${institutionId}`}
                title="Face Anomaly Report"
                width="100%"
                height={`${height}px`}
                frameBorder={0}
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}

export default FaceAnamolyReport;
