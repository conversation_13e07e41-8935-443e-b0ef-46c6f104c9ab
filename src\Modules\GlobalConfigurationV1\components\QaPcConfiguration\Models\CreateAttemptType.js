import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DialogActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  TextField,
  Button,
} from '@mui/material';
import { dialogSX } from './ModelsUtils';
import { placeholderStyles } from '../style/designUtils';
import { Map, fromJS } from 'immutable';
import { useDispatch } from 'react-redux';
import DeleteIcon from '@mui/icons-material/Delete';
import { setData } from '_reduxapi/global_configuration/v1/actions';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';

const layOut = Map({
  name: '',
  isEdited: true,
  isActive: true,
});

const CreateAttemptType = ({ editIndex, open, existData = Map(), handleClose, setAttemptData }) => {
  const [attempts, setAttempts] = useState(
    fromJS([
      Map({
        name: existData.get('name', ''),
        isEdited: existData.get('isEdited', false),
        isActive: true,
        ...(existData.get('_id', '') && {
          _id: existData.get('_id', ''),
        }),
      }),
    ])
  );
  const dispatch = useDispatch();

  const setMessage = (message) => {
    dispatch(setData({ message }));
  };

  const handleValidate = () => {
    for (const [index, attempt] of attempts.entries()) {
      if (attempt.get('name').trim() === '') {
        return setMessage('attempt name required at attempt' + (index + 1));
      }
    }
    if (editIndex !== undefined) {
      setAttemptData((prev) => prev.set(editIndex, attempts.get(0, Map())));
      return handleClose();
    }
    setAttemptData((prev) => prev.merge(attempts));
    handleClose();
  };

  const handleDelete = (index) => {
    setAttempts((prev) => prev.filter((_, i) => i !== index));
  };
  return (
    <Dialog fullWidth open={open} onClose={handleClose} PaperProps={{ sx: dialogSX }} maxWidth="xs">
      <DialogTitle className="text-mGrey pb-0 ">
        <h5 className="pt-2 fw-400  f-24 ">
          {editIndex === undefined ? 'Create ' : 'Edit '} Attempt Type
        </h5>
      </DialogTitle>

      <DialogContent className="course-qlc-scrollbar mx-2 my-1 px-3 py-0">
        <DialogContentText className="responsiveFontSizeSmall d-flex flex-column gap-2">
          <div className="d-flex flex-column gap-3">
            {attempts.map((attempt, index) => (
              <>
                <div className="d-flex align-items-center pt-3" key={index}>
                  <div className="flex-grow-1 f-12 fw-400 text-mGrey d-flex align-items-center">
                    <span className="mr-2">Name</span>
                    <EnableOrDisable valid={index !== 0}>
                      <DeleteIcon
                        sx={{ fontSize: '1rem', color: 'red' }}
                        onClick={() => handleDelete(index)}
                      />
                    </EnableOrDisable>
                  </div>
                </div>
                <TextField
                  value={attempt.get('name', '')}
                  onChange={(e) =>
                    setAttempts((prev) => prev.setIn([index, 'name'], e.target.value))
                  }
                  placeholder="Enter Name"
                  size="small"
                  fullWidth
                  sx={placeholderStyles}
                />
              </>
            ))}
            <div
              className="mt-1 f-14 fw-400 text-primary cursor-pointer"
              onClick={() => setAttempts((prev) => prev.push(layOut))}
            >
              + Add Type
            </div>
          </div>
        </DialogContentText>
      </DialogContent>
      <DialogActions className="px-4 pb-4 gap-2">
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall text-secondary border-secondary bold"
          variant="outlined"
          onClick={() => {
            handleClose();
          }}
        >
          Cancel
        </Button>
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall bg-primary"
          variant="contained"
          onClick={() => {
            handleValidate();
          }}
        >
          {editIndex === undefined ? 'Create' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

CreateAttemptType.propTypes = {
  open: PropTypes.bool,
  editIndex: PropTypes.number,
  handleClose: PropTypes.func,
  existData: PropTypes.instanceOf(Map),
  setAttemptData: PropTypes.func,
};

export default CreateAttemptType;
