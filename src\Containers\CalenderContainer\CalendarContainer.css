.title{
    font-size: 13px;
    color: #818ea3;
}
/* .mlcalender-0{
    margin-right: 0px !important; 
    margin-left: 1% !important;
} */
.hijriMonthName{
    font-size: 13px;
    color: #808080;
}
.table{
    display: table;
    color:#1a1919;
    font-size: 13px;
}
.tr{
    display: table-row;
}
.td{
    display: table-cell;
    padding-top: 3px;
}
.hijriNumber{
    font-size: 10px;
    color: #808080;
    margin-bottom: 6px;
    margin-top: -4px;
}
.mb-0{
    margin-bottom: 0px;
}
.border-none{
    border:none;
}
.calenderTopContent{
    position: relative;
}
.calendarDropdown{
    margin-top: 0px;
    height: 32px !important;
    -webkit-appearance: none;
    width: 90px;
    padding: 4px 0px;
    /* padding: 5px; */
    padding-left: 12px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0);
    border: solid 1px #a9b9c6;
}
.settingWidth{
    width: 18%;
}
.caretDownColor{
    color:#a9b9c6;
}
.dropdownBorder{
    border: solid 1px #a9b9c6 !important;
}

.calendarCard {
    padding: 30px 35px 35px 35px;
}

.eventCard{
    background-color: #fff;
    text-align: left;
    border-radius: 7px;
    box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14)
}
.calenderLineBorder{
    margin-left: -35px;
 margin-right: -35px;
}
.calendarTodayBorder{
    border: 1px solid #ebeced;
    padding: 8px;
    border-radius: 10px;
    font-size: 12px;
}
.calendarMt-28{
    margin-top: 28px;
}
.dropDownColor{
    color: #3d5170;
}
.eventCardHeadPadding{
    padding: 17px 12px 14px 12px;
}
.eventDetailsPadding{
    padding: 0 12px 0 12px;
}
.eventDetailsFont{
    font-size: 12px;
    color: #5a6169;
}
.calendarArrow{
    border: 1px solid #ebeced;
    padding: 8px;
    border-radius: 10px;
    font-size: 12px;
}
.eventEditButton{
    color: #0047cc;
    display: flex;
    align-items: center;
    padding: 3px 12px 3px 4px;
    text-decoration: none;
    display: inline-block;
    font-size: 12px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 5px;
    border: solid 2px #0047cc;
}
.eventBar{
    width: 14px;
    height: 2px;
    background-color: #8c858540;
    margin: 3px 0px;
}
.eventmt-4{
    margin-top: 4px;
}
.viewFull{
    font-size: 22px;
}
.mt-6per{
    margin-top: 6%
}
.publishButton{
    background-color: #0047cc !important;
    /* position: fixed; */
    border: none;
    border-radius: 10px;
    color: white;
    padding: 10px 12px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    /* margin: 4px 2px; */
    cursor: pointer;
    /* width: 100%; */
}
  .calender {
    position: absolute;
    padding: 19px 13px 17px 17px;
    color: #c4c6cb;
    z-index: 1;
  }

  .calender1 {
    position: absolute;
    padding: 22px 17px 18px 16px;
    color: #C4C6CB;
    z-index: 1;
  }

.scrollbar
{float: left;
    height: 88%;
    width: 100%;
    background: #F5F5F5;
    overflow-y: scroll;
    margin-bottom: 25px;
}
#style-1::-webkit-scrollbar-track
{
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	border-radius: 10px;
	background-color: #F5F5F5;
}

#style-1::-webkit-scrollbar
{
	width: 8px;
	background-color: #F5F5F5;
}

#style-1::-webkit-scrollbar-thumb
{
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
	background-color: #555;
}

.force-overflow
{
	min-height: 450px;
}
/* sss */
@media screen and (min-width: 320px) and (max-width:767px) {
 .settingWidth{
    width: 20%;
    margin-right: -20px;
 }.calendarCard{
    padding: 6px;
 }
 .calenderLineBorder{
 margin-left: -6px;
 margin-right: -6px;
 }
}


.style-3 {
    /* margin-left: 30px; */
    /* float: left; */
    height: 820px;
    width: 100%;
    /* background: #F5F5F5; */
    overflow-y: scroll;
}
    
.style-3::-webkit-scrollbar {
    width: 6px;
    background-color: #ffffff;
}

.style-3::-webkit-scrollbar-thumb {
    background-color: #9a9a9a;
}
.style-3::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    background-color: #ffffff;
}


.mb-6per{
    margin-bottom: 6%;
}

.mb-6per:last-child {
    margin-bottom: 1%;
  }