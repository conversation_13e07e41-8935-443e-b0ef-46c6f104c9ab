import React from 'react';
import PropTypes from 'prop-types';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import ArchiveImg from 'Assets/archive.png';
import MButton from 'Widgets/FormElements/material/Button';

const UnCheckAlertModal = (props) => {
  const { show, close, componentName, file, handleSubmit, handleCancel } = props;
  return (
    <MaterialDialog show={show} onClose={close} maxWidth={'md'}>
      <div className="w-100 p-4">
        <div className="d-flex align-items-center mb-2">
          <img className="mr-2 mb-1" alt={'Archive'} src={ArchiveImg} />
          <p className="f-24 text-gray mt-2">{componentName} - Assessment Type</p>
        </div>
        <p className="f-16 mb-2 light-gray ">
          This action will permanatly remove the enterd mark datas from the application!
        </p>
        <p className="f-16 mb-2 light-gray ">
          Are you sure you want to uncheck the {`${file.fileName}`} ?
        </p>
      </div>
      <div className="d-flex justify-content-end border-top pt-3 mb-2 mr-2">
        <MButton variant="outlined" color="primary" className={'mr-2'} clicked={handleCancel}>
          No
        </MButton>
        <MButton variant="contained" color="red" clicked={handleSubmit}>
          Yes
        </MButton>
      </div>
    </MaterialDialog>
  );
};
UnCheckAlertModal.propTypes = {
  show: PropTypes.bool,
  close: PropTypes.func,
  setData: PropTypes.func,
  componentName: PropTypes.string,
  file: PropTypes.object,
  handleSubmit: PropTypes.func,
  handleCancel: PropTypes.func,
};
export default UnCheckAlertModal;
