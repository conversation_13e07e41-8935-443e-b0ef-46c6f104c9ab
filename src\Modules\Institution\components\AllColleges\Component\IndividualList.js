import React, { /* useEffect,  */ useState } from 'react';
import { Trans } from 'react-i18next';
import { Dropdown } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
//import IconButton from '@mui/material/IconButton';
import CollegeImage from '../../../../../Assets/college_img.png';
import Footer from './Footer';
import DropdownMenu from './DropdownMenu';
import * as actions from '_reduxapi/institution/actions';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AddEdit from '../Modal/AddEdit/AddEdit';
import Archive from '../Modal/Archive';
import Restore from '../Modal/Restore';
import Delete from '../Modal/Delete';
import CancelModal from '../Modal/Cancel';
import { checkValidation, getFullAddress } from '../../UniversityDetails/udUtil';

const editArchive = [
  { showModal: 'edit', title: 'edit' },
  { showModal: 'archive', title: 'program_input.archive' },
];
const restoreDelete = [
  { showModal: 'restore', title: 'program_input.restore' },
  { showModal: 'delete', title: 'program_input.delete' },
];
function IndividualList({
  college,
  history,
  addInstitution,
  getColleges,
  archiveCollege,
  setData,
  loggedInUserData,
  searchFilter,
}) {
  const [show, setShow] = useState(false);
  const [cancelShow, setCancelShow] = useState(false);
  const [isChanged, setIsChanged] = useState(false);

  const getUserID = () => {
    return loggedInUserData.getIn(['university', 'institutionId'], '');
  };
  const callId = getUserID();
  const callBackFunctions = () => {
    const { search, option, sort } = searchFilter;
    getColleges({ id: callId, sort, status: option, search });
    setShow(false);
    setIsChanged(false);
  };
  const fetchData = (data) => {
    if (checkValidation(data, 'college', setData)) {
      const institutionDetails = college.toJS();
      const formData = new FormData();
      for (const [key, value] of Object.entries(data)) {
        if (key === 'logo' && value === institutionDetails.presignedLogoURL) {
          formData.append(key, institutionDetails.logo);
          continue;
        }
        formData.append(key, value);
      }
      formData.append(
        'type',
        institutionDetails.type /*  === 'group' ? 'University' : 'College' */
      );
      formData.append(
        'parentInstitute',
        institutionDetails.parentInstitute /*  === 'group' ? 'University' : 'College' */
      );
      addInstitution({
        operation: 'update',
        _id: institutionDetails._id,
        history,
        formData,
        callBack: callBackFunctions,
        calledOn: 'addCollege',
      });
    }
  };
  const setArchiveCollege = (type) => {
    archiveCollege(college.get('_id', ''), college.get('parentInstitute', ''), type, searchFilter);
  };

  const fullAddress = getFullAddress(college.get('address', Map()));

  const handleBack = () => {
    setShow(isChanged ? true : false);
    setCancelShow(isChanged ? true : false);
  };

  return (
    <div className="bg-white mt-2 rounded">
      <div className="d-flex">
        <div style={{ width: '18%' }} className="p-3">
          <img
            alt=""
            height="100%"
            width="100%"
            src={college.get('presignedLogoURL', CollegeImage)}
            className="rounded"
          />
        </div>
        <div style={{ width: '75%' }}>
          <div className="bold pt-3 f-16">
            {college.get('name', '')} - {college.get('code', '')}
          </div>
          <div className="pt-1 f-14 word_break">
            <Trans i18nKey={'address'}></Trans>: {fullAddress}
          </div>
          <div className="bold pt-1 f-15">
            {' '}
            <Trans i18nKey={'admin'}></Trans> -{' '}
            {college.get('admin', <Trans i18nKey={'na'}></Trans>)}
          </div>
        </div>
        <div style={{ width: '9%' }} className="text-right pt-3 ">
          {' '}
          <Dropdown>
            <Dropdown.Toggle variant="" id="dropdown-table" className="table-dropdown" size="sm">
              <MoreVertIcon className="digi-gray-neutral" />
              {/* <IconButton edge="start" color="inherit" aria-label="menu" id="light_gray">
                           </IconButton> */}
            </Dropdown.Toggle>
            <DropdownMenu
              menu={college.get('isActive', true) ? editArchive : restoreDelete}
              setShow={setShow}
            />
          </Dropdown>{' '}
        </div>
      </div>
      <Footer college={college} />
      {show === 'edit' && (
        <AddEdit
          show={show}
          setShow={setShow}
          college={college}
          title={'edit_college_details'}
          fetchData={fetchData}
          setData={setData}
          cancelShow={cancelShow}
          setIsChanged={setIsChanged}
          handleBack={handleBack}
        />
      )}
      {show === 'archive' && (
        <Archive
          show={show}
          setShow={setShow}
          status={false}
          setArchiveCollege={setArchiveCollege}
        />
      )}
      {show === 'restore' && (
        <Restore
          show={show}
          setShow={setShow}
          status={false}
          setArchiveCollege={setArchiveCollege}
        />
      )}
      {show === 'delete' && (
        <Delete
          show={show}
          setShow={setShow}
          status={false}
          setArchiveCollege={setArchiveCollege}
        />
      )}
      {cancelShow && (
        <CancelModal
          show={cancelShow}
          setCancelShow={setCancelShow}
          setShow={setShow}
          type="edit"
          setIsChanged={setIsChanged}
        />
      )}
    </div>
  );
}

IndividualList.propTypes = {
  college: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  addInstitution: PropTypes.func,
  setData: PropTypes.func,
  getColleges: PropTypes.func,
  archiveCollege: PropTypes.func,
  loggedInUserData: PropTypes.instanceOf(Map),
  searchFilter: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(IndividualList);
