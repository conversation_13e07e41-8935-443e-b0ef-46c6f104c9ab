import React from 'react';
import PropTypes from 'prop-types';
import { Checkbox, FormControlLabel } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import { Map } from 'immutable';
import { Trans } from 'react-i18next';
import MSwitch from 'Widgets/FormElements/material/Switch';
import AutoComplete from 'Widgets/FormElements/material/Autocomplete';
import { countries } from '../../../../constants';
import { t } from 'i18next';
import { checkBoxStyles } from 'Modules/GlobalConfiguration/utils';

const LabelDetailRow = ({
  data,
  labelType,
  settingId,
  openRenameModal,
  updateLabelStatus,
  updateVaccineStatus,
  updatePhoneLabelStatus,
  institutionHeader,
  type,
  getLabelConfigurationDetails,
}) => {
  const countryCodes = countries.map(({ code }) => `+${code}`);
  const classes = checkBoxStyles();

  const handleChange = (e, dataType) => {
    const value = e.target.checked;
    const changed = data.set(dataType, value);

    let isMandatory = changed.get('isMandatory');
    if (dataType === 'isActive' && !value) isMandatory = false;

    const callBack = () => {
      getLabelConfigurationDetails({ settingId, headers: institutionHeader, type });
    };

    if (labelType === 'vaccineDetails')
      updateVaccineStatus({
        formData: {
          isActive: changed.get('isActive'),
          isMandatory,
        },
        settingId,
        labelId: changed.get('_id'),
        headers: institutionHeader,
        type,
      });
    else
      updateLabelStatus({
        formData: {
          settingId,
          labelType,
          language: 'en',
          isActive: changed.get('isActive'),
          isMandatory,
        },
        labelId: changed.get('_id'),
        headers: institutionHeader,
        type,
        ...(['country', 'district', 'city'].includes(changed.get('mappingKey')) && {
          callBack: callBack,
        }),
      });
  };

  const handlePhoneStatus = (subType, value) => {
    const changed = data.set(subType, value);
    updatePhoneLabelStatus({
      formData: {
        defaultValue: changed.get('defaultValue'),
        allowToChange: changed.get('allowToChange'),
      },
      settingId,
      labelId: changed.get('_id'),
      headers: institutionHeader,
      type,
    });
  };

  return (
    <>
      <div className="row align-items-center mb-1">
        <div className="col-md-8 col-xl-9">
          <FormControlLabel
            {...(!data.get('isActive', false) && { classes: { label: classes.label } })}
            control={
              <Checkbox
                classes={{ root: classes.root }}
                color={data.get('isCompulsory', false) ? 'default' : 'primary'}
                checked={data.get('isActive', false)}
                disabled={data.get('isCompulsory', false)}
                onChange={(e) => handleChange(e, 'isActive')}
              />
            }
            label={
              labelType === 'vaccineDetails'
                ? data.get('categoryName', '')
                : data.get('translatedInput', '')
                ? data.get('translatedInput')
                : data.get('name', '')
            }
            className="mb-0 mr-0"
          />
        </div>
        <div className="col-md-2">
          {labelType !== 'vaccineDetails' && (
            <MButton
              variant="text"
              className={`text-blue ${
                !data.get('isActive', false) ||
                data.get('mappingKey', '') === 'program_no' ||
                data.get('mappingKey', '') === 'batch'
                  ? 'digi-opacity-50'
                  : ''
              }`}
              clicked={() => openRenameModal(labelType, data)}
              disabled={
                !data.get('isActive', false) ||
                data.get('mappingKey', '') === 'program_no' ||
                data.get('mappingKey', '') === 'batch'
              }
            >
              <Trans i18nKey={'global_configuration.rename'} />
            </MButton>
          )}
        </div>
        <div className="col-md-2 col-xl-1 text-right">
          <MSwitch
            checked={data.get('isMandatory', false)}
            disabled={data.get('isCompulsory', false) || !data.get('isActive', false)}
            onChange={(e) => handleChange(e, 'isMandatory')}
          />
        </div>
      </div>
      {labelType === 'basicDetails' && data.get('name', '') === 'Phone Number' && (
        <div className="digi-pl-32 mb-2">
          <div className="d-flex align-items-center">
            <span className="mr-3 mb-1">
              <Trans i18nKey={'global_configuration.default_country_code'} />
            </span>
            <AutoComplete
              options={countryCodes}
              value={data.get('defaultValue', '+91')}
              onChange={(e, val) => handlePhoneStatus('defaultValue', val)}
              placeholder={`Select`}
              isClearable={false}
              widthSync={true}
            />
          </div>
          <div>
            <FormControlLabel
              control={
                <Checkbox
                  classes={{ root: classes.root }}
                  color="primary"
                  checked={data.get('allowToChange', true)}
                  onChange={(e) => handlePhoneStatus('allowToChange', e.target.checked)}
                />
              }
              label={t('global_configuration.allow_to_change_country_code')}
              className="mb-0 mr-0"
            />
          </div>
        </div>
      )}
    </>
  );
};

LabelDetailRow.propTypes = {
  data: PropTypes.instanceOf(Map),
  labelType: PropTypes.string,
  settingId: PropTypes.string,
  type: PropTypes.string,
  openRenameModal: PropTypes.func,
  updateLabelStatus: PropTypes.func,
  updateVaccineStatus: PropTypes.func,
  updatePhoneLabelStatus: PropTypes.func,
  institutionHeader: PropTypes.object,
  getLabelConfigurationDetails: PropTypes.func,
};

export default LabelDetailRow;
