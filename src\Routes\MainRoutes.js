import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Switch, Route, useLocation } from 'react-router-dom';
//import { Trans } from 'react-i18next';
import { Map } from 'immutable';
import Drawer from '@mui/material/Drawer';
//import { SwipeableDrawer } from '@mui/material';
import SideNavigationV2 from 'Containers/SideNavigation/v2/SideNavigation';
//import Login from 'Containers/Login/Login';
import Login from 'Modules/Auth/components/Indian/Login';
import ForgotPassword from 'Containers/Login/ForgotPassword';
import Logout from 'Containers/Logout/Logout';
import SignUp from 'Containers/SignUp/SignUp';
import staffProfile from 'Components/Staff/staffProfile';
import staffUpload from 'Components/Staff/staffUpload';
import StudentProfileDetail from 'Components/Student/studentProfile';
import StudentUploadDocument from 'Containers/StudentRegistrationContainer/UploadDocument/UploadDocument';

import SideNavigation from 'Containers/SideNavigation/SideNavigation';

import DashboardRoutes from 'Routes/DashboardRoutes';
// import DashboardRoutesV2 from 'Routes/DashboardRoutes/v2';

import LoginV2 from 'Modules/Auth/components/Login';
//import LoginIndian from 'Modules/Auth/components/Indian/Login';
import ForgotPasswordV2 from 'Modules/Auth/components/ForgotPassword';
import SignUpV2 from 'Modules/Auth/components/SignUp';

import Footer from 'Containers/Footer/Footer';
import Navigation from 'Modules/Shared/Navigation';
import NavigationV2 from 'Modules/Shared/v2/Navigation';

// import UserVerificationIndex from 'Modules/UserManagement/v2/UserVerification';
import { getActiveVersion, dString, getLang, getEnvCollegeName } from 'utils';
import LocalStorageService from 'LocalStorageService';

function MainRoutes({
  isAuthenticated,
  iframeShow,
  iframeName,
  institution,
  navBarTitle,
  history,
}) {
  MainRoutes.propTypes = {
    isAuthenticated: PropTypes.bool,
    iframeShow: PropTypes.bool,
    iframeName: PropTypes.string,
    institution: PropTypes.instanceOf(Map),
    navBarTitle: PropTypes.string,
    history: PropTypes.object,
  };
  const location = useLocation();
  const component = location.pathname.split('/')[1];
  const id = location.pathname.split('/')[2];
  const name = location.pathname.split('/')[3];

  function getInstituteName() {
    if (component === 'i' && name !== undefined) {
      return dString(name);
    } else {
      const instituteData = LocalStorageService.getCustomToken('insData', true);
      if (instituteData !== null) {
        return instituteData.name !== undefined ? instituteData.name : 'N/A';
      }
      return institution.get('name', 'N/A');
    }
  }

  function isInstitute() {
    return component === 'i' && id !== undefined && name !== undefined;
  }

  const [sidebar, setSidebar] = useState(false);
  const toggleClick = () => {
    setSidebar(!sidebar);
  };
  const loadVersion = getActiveVersion();

  const showSidebarActive = () => {
    return !['/institution/onboarding'].includes(location.pathname);
  };

  const showSidebar = showSidebarActive();
  return (
    <React.Fragment>
      <div>
        {[
          '/user-verification/staff/profile',
          '/user-verification/staff/upload_document',
          '/user-verification/staff/biometric',
          '/user-verification/staff/completed',
          '/user-verification/staff/pending',
          '/user-verification/staff/invalid',
          '/user-verification/student/profile',
          '/user-verification/student/upload_document',
          '/user-verification/student/biometric',
          '/user-verification/student/completed',
          '/user-verification/student/pending',
          '/user-verification/student/invalid',
        ].includes(location.pathname) && (
          <>
            {loadVersion === '/v2' && !['/signup', '/login'].includes(location.pathname) && (
              <div className="bg-white">
                <div className={sidebar && !iframeShow && showSidebar ? 'ml_0' : 'ml_0'}>
                  {showSidebar && (
                    <>
                      <NavigationV2
                        clicked={toggleClick}
                        navBarTitle={navBarTitle}
                        userVerification={true}
                        history={history}
                      />
                    </>
                  )}
                </div>
              </div>
            )}
          </>
        )}
        <Switch>
          {!isAuthenticated ? (
            <>
              <Route path="/login" exact component={loadVersion === '/v2' ? LoginV2 : Login} />
              <Route path="/logout" exact component={Logout} />
              <Route
                path="/forgot-password"
                exact
                component={loadVersion === '/v2' ? ForgotPasswordV2 : ForgotPassword}
              />
              <Route path="/signup" exact component={loadVersion === '/v2' ? SignUpV2 : SignUp} />
              <Route path="/staffprofile" exact component={staffProfile} />
              <Route path="/staffupload" exact component={staffUpload} />
              <Route path="/student/profile" exact component={StudentProfileDetail} />
              <Route path="/student/upload" exact component={StudentUploadDocument} />
              {/* <Route path="/user-verification" component={UserVerificationIndex} /> */}
            </>
          ) : (
            <>
              <div>
                {!iframeShow && showSidebar && (
                  <Drawer
                    anchor={`${getLang() === 'ar' ? 'right' : 'left'}`}
                    open={sidebar}
                    onClose={toggleClick}
                  >
                    <div className={'sidenav ' + (sidebar ? 'wd_250' : 'wd_0')}>
                      <h6
                        className="d-flex align-items-center side-nav-institution-name cursor-pointer"
                        onClick={() => {
                          toggleClick();
                          if (isInstitute()) {
                            history.push(`/university-details`);
                          } else {
                            loadVersion === '/v2'
                              ? history.push(`/university-details`)
                              : history.push('/overview');
                          }
                        }}
                        style={{ wordBreak: 'break-word' }}
                      >
                        <span>
                          {loadVersion === '/v2'
                            ? getInstituteName()
                            : // <Trans i18nKey={'IBN'}></Trans>
                              getEnvCollegeName()}
                        </span>
                        <i className="fa fa-caret-right"></i>
                      </h6>
                      {loadVersion === '/v2' ? (
                        <SideNavigationV2 toggleClick={toggleClick} sidebar={sidebar} />
                      ) : (
                        <SideNavigation toggleClick={toggleClick} />
                      )}
                    </div>
                  </Drawer>
                )}

                {![
                  '/user-verification/staff/profile',
                  '/user-verification/staff/upload_document',
                  '/user-verification/staff/biometric',
                  '/user-verification/staff/completed',
                  '/user-verification/staff/pending',
                  '/user-verification/staff/invalid',
                  '/user-verification/student/profile',
                  '/user-verification/student/upload_document',
                  '/user-verification/student/biometric',
                  '/user-verification/student/completed',
                  '/user-verification/student/pending',
                  '/user-verification/student/invalid',
                ].includes(location.pathname) && (
                  <div className="bg-white">
                    <div className={sidebar && !iframeShow && showSidebar ? 'ml_0' : 'ml_0'}>
                      {showSidebar && (
                        <>
                          {loadVersion === '/v2' ? (
                            <NavigationV2
                              clicked={toggleClick}
                              navBarTitle={navBarTitle}
                              userVerification={false}
                              history={history}
                            />
                          ) : (
                            <div className="headerbar-back">
                              <Navigation
                                clicked={toggleClick}
                                iframeShow={iframeShow}
                                iframeName={iframeName}
                                navBarTitle={navBarTitle}
                              />
                            </div>
                          )}
                        </>
                      )}
                      {/* {loadVersion === 'v2' ? (
                      <Route path="/" component={DashboardRoutesV2} />
                    ) : (
                      <Route path="/" component={DashboardRoutes} />
                    )} */}
                      <Route path="/" component={DashboardRoutes} />
                      {/* <Route path="/" component={DashboardRoutesV2} /> */}
                    </div>
                  </div>
                )}
              </div>
              <Route path="/logout" exact component={Logout} />
              <Route path="/login" exact component={loadVersion === '/v2' ? LoginV2 : Login} />
              <Route path="/signup" exact component={loadVersion === '/v2' ? SignUpV2 : SignUp} />
              {/* <Route path="/user-verification" component={UserVerificationIndex} /> */}
            </>
          )}
        </Switch>
      </div>
      {isAuthenticated === true && showSidebar && (
        <Footer className={'ml_0'} loadVersion={loadVersion} />
      )}
    </React.Fragment>
  );
}

export default MainRoutes;
