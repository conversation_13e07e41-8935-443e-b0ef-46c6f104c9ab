import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';

import paginationLeft from '../.././Assets/pagination-left-end.png';
import paginationLeftCenter from '../../Assets/pagination-left.png';
import paginationEnd from '../../Assets/pagination-right-end.png';
import paginationEndCenter from '../../Assets/pagination-right.png';
import Input from '../../Widgets/FormElements/Input/Input';

export default function Pagination({
  totalPages,
  switchPagination,
  switchPageCount,
  pageLength,
  eachPageItemsSize,
  className = 'new_dropdown',
}) {
  const currentPage = [];
  let count = 0;
  let indexNumber = 1;

  Pagination.propTypes = {
    totalPages: PropTypes.number,
    pageLength: PropTypes.number,
    eachPageItemsSize: PropTypes.number,
    className: PropTypes.string,
    switchPagination: PropTypes.func,
    switchPageCount: PropTypes.func,
  };

  while (indexNumber <= totalPages) {
    indexNumber++;
    count++;
    currentPage.push(count);
  }

  const option = [
    // { name: '5', value: 5 },
    { name: '10', value: 10 },
    { name: '15', value: 15 },
    { name: '20', value: 20 },
    { name: '25', value: 25 },
  ];
  const currentEachPageItemsSize = option.find((item) => item.value === eachPageItemsSize)?.value;
  return (
    <div className={`col-md-12 ${className ? 'py-3 mt-1' : 'pt-4 pb-5'}`}>
      <div className="row">
        <div className="col-md-6">
          <div className="row">
            <div className="col-md-12 col-xl-8 col-lg-8 col-12 text-black">
              <div className="float-left pr-3">
                {' '}
                <Trans i18nKey={'item_per_page'}></Trans> :
              </div>

              <div className="float-left mt--30">
                <Input
                  elementType={'floatingselect'}
                  elementConfig={{
                    options: option,
                  }}
                  value={currentEachPageItemsSize || option.value}
                  floatingLabel={''}
                  className={className}
                  changed={(e) => switchPagination(e)}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-6">
          {
            <div className="float-right">
              <b className="pr-4 pt-1">
                {pageLength} of {totalPages !== 0 ? totalPages : 1}{' '}
              </b>
              <b
                className="remove_hover pr-4"
                onClick={() => {
                  switchPageCount(1);
                }}
              >
                <img src={paginationLeft} alt="DigivalSolutions" />
              </b>
              <b
                className="remove_hover pr-4 pl-2"
                onClick={() => {
                  let check = pageLength - 1;

                  switchPageCount(check === 0 ? 1 : check);
                }}
              >
                <img src={paginationLeftCenter} alt="Digival-Solutions" />
              </b>
              <b
                className="remove_hover pr-4 pl-2"
                onClick={() => {
                  let check = pageLength + 1;

                  switchPageCount(check > totalPages ? totalPages : check);
                }}
              >
                <img src={paginationEndCenter} alt="Digival-Solutions" />
              </b>
              <b
                className="remove_hover pl-2"
                onClick={() => {
                  switchPageCount(totalPages);
                }}
              >
                <img src={paginationEnd} alt="Digival-Solutions" />
              </b>
            </div>
          }
        </div>
      </div>
    </div>
  );
}
