import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Checkbox,
  FormControl,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  MenuItem,
  MenuList,
  Popover,
  Select,
  Typography,
} from '@mui/material';
import { fromJS, List } from 'immutable';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import MButton from 'Widgets/FormElements/material/Button';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { CustomSearch, customSelectBtnSx, getFilteredOptions, ToggleButton } from './CustomSelect';
import {
  checkboxSx,
  getScheduledOptions,
  ScheduledSelectionStatus,
  ScheduleLabel,
  ScheduleStatusIcon,
  SecondaryText,
  // StaffStatus
} from './utils';

const menuItemSx = {
  padding: '8px 12px !important',
  '&:hover': {
    backgroundColor: '#F3F4F6',
  },
};
const primaryTextSx = {
  sx: {
    color: '#4B5563',
  },
};
const secondaryTextSx = {
  sx: {
    color: '#4B5563',
    textAlign: 'right',
  },
};
const buttonSx = {
  width: '50%',
  boxShadow: 'none',
};
const staffItemSx = {
  padding: '4px 12px !important',
  minHeight: '48px !important',
  '&:hover': {
    backgroundColor: '#F3F4F6',
  },
};
const weekSelectSx = {
  '& .MuiSelect-select': {
    color: '#374151',
    '&.Mui-disabled': {
      WebkitTextFillColor: '#9CA3AF',
    },
  },
  '& .MuiSelect-iconOutlined': {
    color: '#374151',
    right: '8px !important',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#D1D5DB',
    borderRadius: '8px',
  },
  '&.Mui-disabled': {
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: '#E5E7EB',
    },
    '& .MuiSelect-iconOutlined': {
      color: '#9CA3AF',
    },
  },
};

const ActionButtons = ({ handleClose, handleApply }) => {
  return (
    <Box display="flex" gap={1} my={1} px="12px">
      <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
        Cancel
      </MButton>
      <MButton variant="contained" color="primary" clicked={handleApply} sx={buttonSx}>
        Apply
      </MButton>
    </Box>
  );
};
ActionButtons.propTypes = {
  handleClose: PropTypes.func,
  handleApply: PropTypes.func,
};

export const StaffSelect = ({
  options,
  value,
  onChange,
  disabled,
  showScheduledStatus,
  showScheduledSelection = false,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selected, setSelected] = useState(List());
  const [search, setSearch] = useState('');
  const [showScheduled, setShowScheduled] = useState(false);
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  useEffect(() => {
    if (!open) {
      setShowScheduled(false);
      setSearch('');
    } else setSelected(value);
  }, [open]);

  const scheduledOptions = useMemo(() => {
    if (!showScheduledSelection) return List();
    return getScheduledOptions(options, value);
  }, [showScheduledSelection, options, value]);

  const isAllChecked = useMemo(() => {
    if (!selected.size) return false;
    return options.every((option) => selected.includes(option.get('value')));
  }, [options, selected]);

  const filteredOptions = useMemo(() => {
    return getFilteredOptions({ options, search, showScheduled });
  }, [options, search, showScheduled]);

  const renderValue = useMemo(() => {
    return !value.size ? '- Select -' : `(${value.size}) selected`;
  }, [value]);

  const handleClick = (e) => setAnchorEl(e.currentTarget);

  const handleClose = () => setAnchorEl(null);

  const handleChange = (val) => {
    if (val === 'all') {
      const allValues = options.map((option) => option.get('value'));
      return setSelected(isAllChecked ? List() : allValues);
    }

    const isSelected = selected.includes(val);
    const newValue = isSelected ? selected.filter((id) => id !== val) : selected.push(val);
    setSelected(newValue);
  };

  const handleApply = () => {
    onChange(selected);
    handleClose();
  };

  return (
    <Box display="flex" alignItems="end" gap={0.5}>
      <Box flexGrow={1} maxWidth="100%">
        <ScheduleLabel label="Staff" />
        <MButton
          variant="outlined"
          clicked={handleClick}
          sx={customSelectBtnSx}
          disabled={disabled}
          fullWidth
        >
          <div className="d-flex align-items-center justify-content-between w-100">
            <Typography whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
              {renderValue}
            </Typography>
            {open ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
          </div>
        </MButton>
        <Popover
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleApply}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
          slotProps={{ paper: { sx: { maxHeight: 335, width: '100%', maxWidth: 460 } } }}
        >
          <Box sx={{ maxHeight: '282px', overflowY: 'auto' }}>
            <ListSubheader sx={{ padding: '8px 12px 0', lineHeight: 'normal' }}>
              <CustomSearch
                placeholder="Search staff"
                value={search}
                changed={setSearch}
                autoFocus
              />
            </ListSubheader>
            {showScheduledStatus && (
              <ListSubheader sx={{ padding: '0 12px 5px', lineHeight: 'normal' }} disableSticky>
                <ToggleButton
                  placeholder="Show scheduled staffs"
                  showScheduled={showScheduled}
                  handleClick={() => setShowScheduled(!showScheduled)}
                />
              </ListSubheader>
            )}
            {filteredOptions.size ? (
              <MenuList sx={{ p: 0 }}>
                <MenuItem onClick={() => handleChange('all')} sx={menuItemSx}>
                  <Checkbox
                    sx={checkboxSx}
                    checked={isAllChecked}
                    indeterminate={selected.size > 0 && !isAllChecked}
                  />
                  <ListItemText primary="All" primaryTypographyProps={primaryTextSx} />
                  <ListItemText
                    secondary={`${selected.size}/${options.size}`}
                    secondaryTypographyProps={secondaryTextSx}
                  />
                </MenuItem>
                {filteredOptions.map((option, index) => {
                  const optionValue = option.get('value', '');
                  const isSelected = selected.includes(optionValue);
                  return (
                    <MenuItem
                      key={index}
                      onClick={() => handleChange(optionValue)}
                      selected={isSelected}
                      sx={staffItemSx}
                    >
                      <Checkbox sx={checkboxSx} checked={isSelected} />
                      <ListItemText
                        primary={
                          <Typography fontSize={14} color="#4B5563">
                            {getShortString(option.get('name', ''), 35)}
                          </Typography>
                        }
                        secondary={<SecondaryText text={option.get('secondaryText', '')} />}
                        secondaryTypographyProps={{ sx: { textTransform: 'capitalize' } }}
                      />
                      {showScheduledStatus && (
                        <Box display="flex" alignItems="center" gap="8px">
                          {/* <StaffStatus primary={option.get('primary')} /> */}
                          {showScheduledStatus && (
                            <ScheduleStatusIcon
                              scheduled={option.get('scheduled')}
                              errorInfo={option.get('errorInfo')}
                            />
                          )}
                        </Box>
                      )}
                    </MenuItem>
                  );
                })}
              </MenuList>
            ) : (
              <ListSubheader sx={{ color: '#9CA3AF' }} disableSticky>
                No staffs found
              </ListSubheader>
            )}
          </Box>

          <ActionButtons handleClose={handleClose} handleApply={handleApply} />
        </Popover>
      </Box>

      {!scheduledOptions.isEmpty() && <ScheduledSelectionStatus scheduledList={scheduledOptions} />}
    </Box>
  );
};
StaffSelect.propTypes = {
  options: PropTypes.instanceOf(List),
  value: PropTypes.instanceOf(List),
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  showScheduledStatus: PropTypes.bool,
  showScheduledSelection: PropTypes.bool,
};

export const WeekSelect = ({ options, value, changed, disabled }) => {
  const { isAllChecked, isIndeterminate } = useMemo(() => {
    const isAllChecked = options.every((option) => value.includes(option.get('value')));
    return {
      isAllChecked: options.size > 0 && isAllChecked,
      isIndeterminate: value.length > 0 && !isAllChecked,
    };
  }, [options, value]);

  const handleChange = (e) => {
    const val = e.target.value;
    const newValue = val.includes('all')
      ? isAllChecked
        ? []
        : options.map((option) => option.get('value'))
      : val;
    return changed(fromJS(newValue));
  };

  return (
    <FormControl size="small" fullWidth>
      <Select
        value={value}
        onChange={handleChange}
        disabled={disabled}
        renderValue={(selected) => {
          if (!selected.length) return '- Select -';
          if (isAllChecked) return 'All days';
          return options
            .filter((option) => selected.includes(option.get('value')))
            .map((option) => option.get('name'))
            .join(', ');
        }}
        sx={weekSelectSx}
        displayEmpty
        multiple
      >
        <MenuItem value="all">
          <ListItemIcon>
            <Checkbox sx={checkboxSx} checked={isAllChecked} indeterminate={isIndeterminate} />
          </ListItemIcon>
          <ListItemText primary="All days" primaryTypographyProps={{ color: '#4B5563' }} />
        </MenuItem>
        {options.map((option, index) => {
          const optionValue = option.get('value', '');
          const name = option.get('fullName', '');
          return (
            <MenuItem key={index} value={optionValue}>
              <ListItemIcon>
                <Checkbox sx={checkboxSx} checked={value.includes(optionValue)} />
              </ListItemIcon>
              <ListItemText primary={name} primaryTypographyProps={{ color: '#4B5563' }} />
            </MenuItem>
          );
        })}
      </Select>
    </FormControl>
  );
};
WeekSelect.propTypes = {
  options: PropTypes.instanceOf(List),
  value: PropTypes.array,
  changed: PropTypes.func,
  disabled: PropTypes.bool,
};

const CustomMultiSelect = ({
  label,
  options,
  value,
  onChange,
  searchPlaceholder,
  toggleBtnText,
  disabled,
  showScheduledStatus = false,
  emptyErrorMessage,
  showScheduledSelection = false,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [paperWidth, setPaperWidth] = useState(null);
  const [selected, setSelected] = useState(List());
  const [search, setSearch] = useState('');
  const [showScheduled, setShowScheduled] = useState(false);
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  useEffect(() => {
    if (!open) {
      setShowScheduled(false);
      return setSearch('');
    }

    setPaperWidth(anchorEl.offsetWidth);
    setSelected(value);
  }, [open]);

  const scheduledOptions = useMemo(() => {
    if (!showScheduledSelection) return List();
    return getScheduledOptions(options, value);
  }, [showScheduledSelection, options, value]);

  const isAllChecked = useMemo(() => {
    if (!selected.size) return false;
    return options.every((option) => selected.includes(option.get('value')));
  }, [options, selected]);

  const filteredOptions = useMemo(() => {
    return getFilteredOptions({ options, search, showScheduled });
  }, [options, search, showScheduled]);

  const renderValue = useMemo(() => {
    if (!value.size) return '- Select -';

    const selectedOptions = options
      .filter((option) => value.includes(option.get('value')))
      .map((option) => option.get('name'));
    return selectedOptions.join(', ');
  }, [options, value]);

  const handleClick = (e) => setAnchorEl(e.currentTarget);

  const handleClose = () => setAnchorEl(null);

  const handleChange = (val) => {
    if (val === 'all') {
      const allValues = options
        .filter((option) => !option.get('disabled'))
        .map((option) => option.get('value'));
      return setSelected(isAllChecked ? List() : allValues);
    }

    const isSelected = selected.includes(val);
    const newValue = isSelected ? selected.filter((id) => id !== val) : selected.push(val);
    setSelected(newValue);
  };

  const handleApply = () => {
    onChange(selected);
    handleClose();
  };

  return (
    <Box display="flex" alignItems="end" gap={0.5}>
      <Box flexGrow={1} maxWidth="100%">
        <ScheduleLabel label={label} />
        <MButton
          variant="outlined"
          clicked={handleClick}
          sx={customSelectBtnSx}
          disabled={disabled}
          fullWidth
        >
          <div className="d-flex align-items-center justify-content-between w-100">
            <Typography whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
              {renderValue}
            </Typography>
            {open ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
          </div>
        </MButton>
        <Popover
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleApply}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
          slotProps={{ paper: { sx: { maxHeight: 335, minWidth: 360, width: paperWidth } } }}
        >
          <Box sx={{ maxHeight: '282px', overflowY: 'auto' }}>
            <ListSubheader sx={{ padding: '8px 12px 0', lineHeight: 'normal' }}>
              <CustomSearch
                placeholder={searchPlaceholder}
                value={search}
                changed={setSearch}
                autoFocus
              />
            </ListSubheader>
            {showScheduledStatus && (
              <ListSubheader sx={{ padding: '0 12px 5px', lineHeight: 'normal' }} disableSticky>
                <ToggleButton
                  placeholder={toggleBtnText}
                  showScheduled={showScheduled}
                  handleClick={() => setShowScheduled(!showScheduled)}
                />
              </ListSubheader>
            )}
            {filteredOptions.size ? (
              <MenuList sx={{ p: 0 }}>
                <MenuItem onClick={() => handleChange('all')} sx={menuItemSx}>
                  <Checkbox
                    sx={checkboxSx}
                    checked={isAllChecked}
                    indeterminate={selected.size > 0 && !isAllChecked}
                  />
                  <ListItemText primary="All" primaryTypographyProps={primaryTextSx} />
                  <ListItemText
                    secondary={`${selected.size}/${options.size}`}
                    secondaryTypographyProps={secondaryTextSx}
                  />
                </MenuItem>
                {filteredOptions.map((option, index) => {
                  const optionValue = option.get('value', '');
                  const isSelected = selected.includes(optionValue);
                  return (
                    <MenuItem
                      key={index}
                      onClick={() => handleChange(optionValue)}
                      selected={isSelected}
                      disabled={option.get('disabled', false)}
                      sx={menuItemSx}
                    >
                      <Checkbox sx={checkboxSx} checked={isSelected} />
                      <ListItemText
                        primary={getShortString(option.get('name', ''), 35)}
                        primaryTypographyProps={primaryTextSx}
                      />
                      {showScheduledStatus && (
                        <ScheduleStatusIcon
                          scheduled={option.get('scheduled')}
                          errorInfo={option.get('errorInfo')}
                        />
                      )}
                    </MenuItem>
                  );
                })}
              </MenuList>
            ) : (
              <ListSubheader sx={{ color: '#9CA3AF' }} disableSticky>
                {emptyErrorMessage}
              </ListSubheader>
            )}
          </Box>

          <ActionButtons handleClose={handleClose} handleApply={handleApply} />
        </Popover>
      </Box>

      {!scheduledOptions.isEmpty() && <ScheduledSelectionStatus scheduledList={scheduledOptions} />}
    </Box>
  );
};
CustomMultiSelect.propTypes = {
  label: PropTypes.string,
  options: PropTypes.instanceOf(List),
  value: PropTypes.instanceOf(List),
  onChange: PropTypes.func,
  searchPlaceholder: PropTypes.string,
  toggleBtnText: PropTypes.string,
  disabled: PropTypes.bool,
  showScheduledStatus: PropTypes.bool,
  emptyErrorMessage: PropTypes.string,
  showScheduledSelection: PropTypes.bool,
};

export default CustomMultiSelect;
