import { Divider } from '@mui/material';
import React, { useRef, useState } from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import Radio from '@mui/material/Radio';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import MButton from 'Widgets/FormElements/material/Button';
import FormControl from '@mui/material/FormControl';
import LateConfigurationTable from './LateConfigurationTable';
import { useSelector } from 'react-redux';
import { selectIsLoading } from '_reduxapi/leave_management/selectors';

export default function AttendanceConfiguration() {
  const [mode, setMode] = useState('auto');
  const ref = useRef(null);
  const isLoading = useSelector(selectIsLoading);
  function handleMode(e) {
    const newMode = e.target.value;
    setMode(newMode);
    ref.current && ref.current.onModeChange(newMode);
  }

  return (
    <div className="px-3">
      <div className="d-flex">
        <span className="f-18 bold"> Attendance Configuration</span>
        <MButton
          className="ml-auto"
          disabled={ref.current?.saveButtonDisable || isLoading}
          clicked={() => ref.current?.onSaveClicked()}
        >
          SAVE
        </MButton>
      </div>
      <Divider className="my-2" />
      <Accordion
        defaultExpanded
        disableGutters
        className="mt-2"
        sx={{
          boxShadow: 'none',
          background: 'transparent',
          '&::before': {
            display: 'none',
          },
        }}
      >
        <AccordionSummary
          expandIcon={<ArrowDropDownIcon />}
          aria-controls="panel1a-content"
          id="panel1a-header"
          sx={{ flexDirection: 'row-reverse', padding: '0px' }}
        >
          <Typography className="ml-1">Late Configuration:</Typography>
        </AccordionSummary>
        <AccordionDetails className="pl-4">
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              onChange={handleMode}
            >
              <FormControlLabel
                value="auto"
                control={<Radio />}
                checked={mode === 'auto'}
                label={
                  <div className="d-flex">
                    <div>Auto</div>
                    <InfoOutlinedIcon
                      className="ml-1 pt-1"
                      sx={{ fontSize: '20px' }}
                      size="small"
                    />
                  </div>
                }
              />
              <FormControlLabel
                checked={mode === 'manual'}
                value="manual"
                control={<Radio />}
                label="Manual"
              />
            </RadioGroup>
          </FormControl>
          <LateConfigurationTable ref={ref} mode={mode} setMode={setMode} />
        </AccordionDetails>
      </Accordion>
    </div>
  );
}
