import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { withRouter /* , Link */ } from 'react-router-dom';

import InstitutionOnboarding from './components/InstitutionOnboarding/OnboardingIndex';
import InstitutionDetails from './components/InstitutionDetails';
//import UniversityDetails from './components/UniversityDetails/UniversityDetails';
import AllColleges from './components/AllColleges/index';
//import NewCollege from './components/NewCollege';
import Colleges from './components/Colleges';
import Loader from '../../Widgets/Loader/Loader';
//import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import Snackbars from '../Utils/Snackbars';
import {
  selectIsLoading,
  selectMessage,
  selectInstitution,
} from '../../_reduxapi/institution/selectors';

class Institution extends Component {
  render() {
    const { message /* , institution, location */ } = this.props;
    return (
      <div>
        {message && <Snackbars show={true} message={message} />}
        <Loader isLoading={this.props.isLoading} />
        {/* {location.pathname !== '/institution/onboarding' && (
          <Breadcrumb>
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} to="">
              {institution.get('institute_name', '')}
            </Link>
          </Breadcrumb>
        )} */}
        <Switch>
          <Route path="/institution" exact component={InstitutionDetails}></Route>
          <Route path="/institution/onboarding" exact component={InstitutionOnboarding}></Route>
          <Route path="/institution/colleges" exact component={Colleges}></Route>
        </Switch>
        <Route path="/allCollege" exact component={AllColleges}></Route>
      </div>
    );
  }
}

Institution.propTypes = {
  location: PropTypes.object,
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  institution: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    institution: selectInstitution(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(Institution);
