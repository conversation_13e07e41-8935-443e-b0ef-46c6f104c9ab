import { setMinutes, setHours } from 'date-fns';
export const nonRotational = {
  modal: false,
  modal_mode: '',
  update_method: 'add',
  title: '',
  select_dates: '',
  events: [],
  disable_items: false,
  type: {
    // title: "",
    // sub_title: "",
    start_date: '',
    end_date: '',
    model: '',
    _course_id: '',
    background_color: '#FFF4BD',
    events: [],
  },
  check_courses: false,
  add_courses: {
    course: null,
    elective: null,
    module: null,
  },
  copy_dates: [],
  custom_dates: '',
  academic_week_start: 0,
  academic_week_end: 0,
  academic_week_start_start_date: '',
  academic_week_start_end_date: '',
  academic_week_end_start_date: '',
  academic_week_end_end_date: '',
  academic_weeks: [],
  work_start_date: '',
  work_end_date: '',
  modal_content: {
    description: '',
    event_type: 'exam',
    title: '',
    start_date: '',
    start_time: setHours(setMinutes(new Date(), 0), 8),
    end_date: '',
    end_time: setHours(setMinutes(new Date(), 0), 17),
  },
  event_edit: false,
  triggerLists: true,
  deletedEvents: [],
  year1: {},
  year2: {},
  year3: {},
  year4: {},
  year5: {},
  year6: {},
};
