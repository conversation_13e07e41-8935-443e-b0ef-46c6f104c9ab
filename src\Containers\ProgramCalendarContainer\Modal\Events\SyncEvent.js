import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import { EventRowWrapper, EventWrapper, EventRowWrapperHover, Label } from '../../Styled';

const EventContentWrapper = styled.div`
  overflow: auto;
`;

const SyncEvent = ({ data, method }) => {
  return (
    <Fragment>
      <EventWrapper mg="0 10px" of_scroll="auto" className="title go-wrapper-width">
        <Label htmlFor="select_all" mg="0px">
          <EventRowWrapper
            className="title"
            of_scroll="auto"
            tem_col="0fr 0fr 0fr 0fr 0fr 0fr 0fr 0fr 0fr"
          >
            <div className="tb-50">
              <input
                type="checkbox"
                name="select_all"
                id="select_all"
                onClick={(e) =>
                  method({
                    type: 'TOGGLE_SELECT_ALL',
                    payload: e.target.checked,
                  })
                }
              />
            </div>
            <div className="tb-50">S no.</div>
            <div className="tb-200">Event title</div>
            <div className="tb-100">Event type</div>
            <div className="tb-100">Start date</div>
            <div className="tb-100">Start time</div>
            <div className="tb-100">End date</div>
            <div className="tb-100">End time</div>
          </EventRowWrapper>
        </Label>
        <Fragment>
          <EventContentWrapper className="go-wrapper-height">
            {data.map((item, i) => {
              let end_date = moment(item.end_date).format('DD MMM YYYY');
              let event_date = moment(item.event_date).format('DD MMM YYYY');
              let start_time = moment(item.start_time).format('hh:mm A');
              let end_time = moment(item.end_time).format('hh:mm A');
              return (
                <Label key={item._id} htmlFor={item._id} mg="0px">
                  <EventRowWrapperHover
                    className={!item.isChecked && 'unselected '}
                    tem_col="0fr 0fr 0fr 0fr 0fr 0fr 0fr 0fr 0fr"
                  >
                    <div className="tb-50">
                      <input
                        type="checkbox"
                        name={item._id}
                        id={item._id}
                        checked={item.isChecked}
                        onChange={(e) =>
                          method({
                            type: 'INDIVIDUAL_TOGGLE',
                            payload: e.target.checked,
                            id: item._id,
                          })
                        }
                      />
                    </div>
                    <div className="tb-50">{i + 1}</div>
                    <div className="tb-200">{item.event_name.first_language}</div>
                    <div className="tb-100">{item.event_type} </div>
                    <div className="tb-100"> {event_date} </div>
                    <div className="tb-100"> {start_time}</div>
                    <div className="tb-100"> {end_date} </div>
                    <div className="tb-100">{end_time} </div>
                  </EventRowWrapperHover>
                </Label>
              );
            })}
          </EventContentWrapper>
        </Fragment>
      </EventWrapper>
    </Fragment>
  );
};

SyncEvent.propTypes = {
  data: PropTypes.array,
  method: PropTypes.func,
};

export default SyncEvent;
