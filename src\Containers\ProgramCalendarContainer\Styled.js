import styled from 'styled-components';
import dropdownicon from '../../Assets/dropdown.png';

export const PrimaryButton = styled.button`
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  background: #3e95ef;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  border-radius: 8px;
  color: white;
  padding: 8px 16px;
  text-transform: uppercase;
  margin: ${(props) => props.mg || '15px'};

  &.bordernone {
    border: none;
  }

  &.bgdisabled {
    background-color: rgba(0, 0, 0, 0.12);
    box-shadow: none;
    border: none;
    color: #ffffff;
  }

  &:focus {
    outline: none;
  }

  &:disabled {
    background-color: rgba(0, 0, 0, 0.12);
    box-shadow: none;
  }

  &.dim {
    background-color: rgba(78, 90, 235, 0.4);
    border: none;
    margin: 0px;
  }

  &.light {
    color: #3e95ef;
    background: white;
    border: 2px solid #3e95ef;
    box-shadow: none;
    align-self: ${(props) => props.alse || 'none'};
  }

  &.disable {
    color: rgba(0, 0, 0, 0.12);
    border-color: rgba(0, 0, 0, 0.12);
  }

  &.none {
    display: none;
  }
`;

export const NavButtonTerm = styled.button`
  color: ${(props) => props.color || '#3E95EF'};
  background: none;
  border: none;
  margin: 5px 15px 5px 15px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  font-size: 18px;
  font-weight: 500;
  padding: 0 8px 8px 8px;
  border-bottom: 3px solid rgba(0, 0, 0, 0);
  &:focus {
    outline: none;
  }
  &.active {
    border-bottom: 3px solid;
  }
  &.light {
    color: #3e95ef;
    border-bottom: 3px solid #3e95ef;
  }
`;
export const NavButton = styled.button`
  color: ${(props) => props.color || '#3E95EF'};
  background: none;
  border: none;
  margin: 5px 15px 5px 15px;
  line-height: 24px;
  letter-spacing: 0.0125em;
  font-size: 18px;
  font-weight: 500;
  padding: 0 8px 8px 8px;
  border-bottom: 3px solid rgba(0, 0, 0, 0);
  &:focus {
    outline: none;
  }
  &.active {
    border-bottom: 3px solid;
  }
  &.light {
    color: #3e95ef;
    border-bottom: 3px solid #3e95ef;
  }
`;
export const FlexWrapper = styled.div`
  display: flex;
  flex: row no-wrap;
  align-items: center;
  margin: ${(props) => props.mg || 0};
  padding: ${(props) => props.pad || 0};

  &.nav_bg {
    background: linear-gradient(
      84.06deg,
      rgba(26, 123, 220, 0.85) 13%,
      rgba(86, 184, 255, 0.85) 107.28%
    );
    background-image: linear-gradient(
      84.06deg,
      rgba(26, 123, 220, 0.85) 13%,
      rgba(86, 184, 255, 0.85) 107.28%
    );
  }

  &.wrap {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25), 0px 0px 4px rgba(0, 0, 0, 0.25);
    border-radius: 8px;
  }

  &.ji_end {
    justify-items: flex-end;
  }

  &.column {
    flex-direction: column;
    align-items: flex-start;
  }
  &.ai_end {
    align-items: flex-end;
  }
`;

export const Input = styled.input`
  border: none;
  margin: 5px;
  display: inline-block;
  font-size: 14px;
  border-bottom: 1px solid;
  //text-align: center;
  // padding-inline-start: 20px;
  // padding-inline-end: 20px;
  background: none;
  width: auto;

  &:focus {
    outline: none;
  }
  &.none {
    display: none;
  }
`;

export const Select = styled.select`
  border: none;
  margin: 5px;
  font-size: 15px;
  display: inline-block;
  text-align-last: center;
  border-bottom: 1px solid;
  padding-inline-start: 20px;
  padding-inline-end: 20px;
  // background: none;
  background: url(${dropdownicon}) no-repeat 98% 50%;
  -moz-appearance: none;
  -webkit-appearance: none;

  &:focus {
    outline: none;
  }
`;

export const TextArea = styled.textarea`
  border: none;
  margin: 5px;
  display: inline-block;
  border-bottom: 1px solid;
  text-align: center;
  padding-inline-start: 20px;
  padding-inline-end: 20px;

  &:focus {
    outline: none;
  }
`;

export const Label = styled.label`
  font-size: 14px;
  display: block;
  margin: ${(props) => props.mg};
  &.color {
    width: 24px;
    height: 24px;
    background-color: ${(props) => props.bg};
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.16);
  }
  &.selected {
    border: 2px solid rgba(0, 0, 0, 0.87);
  }
`;

export const Null = styled.div`
  flex: 1;
`;

export const BlockWrappers = styled.div`
  display: block;
  margin: 10px;
`;

export const InlineBlockWrapper = styled.div`
  display: inline-block;
  margin: 10px;
`;

export const BlockWrapper = styled.div`
  display: block;
  position: relative;
  margin: ${(props) => props.mg || 0};
`;

export const EventWrapper = styled.div`
  display: grid;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  margin: ${(props) => props.mg || '0 18px 50px 18px'};
  overflow: ${(props) => props.of_scroll || 'hidden'};
`;

export const EventRowWrapper = styled.div`
  display: grid;
  padding: 10px 25px;
  grid-template-columns: ${(props) => props.tem_col || '1fr 5fr 5fr 5fr 5fr 5fr 5fr 1fr 1fr'};
  align-items: center;
  justify-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  &.title {
    border-bottom: 2px solid #7178cd;
  }
`;

export const EventRowWrapperHover = styled(EventRowWrapper)`
  &:hover {
    background-color: rgba(78, 90, 235, 0.1);
  }
  &.unselected {
    color: rgba(0, 0, 0, 0.5);
  }
`;

export const ModalWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 5;
`;

export const ModalBackgroundWrapper = styled.div`
  display: block;
  max-width: 90vw;
  max-height: 90vh;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  background-color: white;
  align-content: center;
  z-index: 10;
  position: relative;
  overflow: auto;
  border: 1px solid rgba(0, 0, 0, 0.09);
  box-shadow: -2px 4px 25px 2px rgba(0, 0, 0, 0.45);
  min-width: 50%;
`;

export const WeekWrapper = styled.div`
  display: grid;
  grid-template-rows: repeat(${(props) => props.len}, 50px);
`;

export const WeekRowWrapper = styled.div`
  display: grid;
  align-items: center;
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  &.right {
    border-right: 1px solid rgba(0, 0, 0, 0.12);
  }
`;

export const Level = styled.div`
  grid-column: 1 / -1;
  grid-row: ${(props) => `${props.st_date} / ${props.end_date}`};
  display: grid;
  grid-auto-flow: column;
  grid-auto-flow: column;
  grid-template-rows: repeat(${(props) => props.len || 0}, 50px);
`;
// grid-template-columns: repeat(${(props) => props.col}, 1fr);

// grid-template-columns: repeat(
//   ${(props) => props.col},
//   calc(
//     ${(props) => `${75 / props.no_of_rotation}vw`} / ${(props) => props.col}
//   )
// );
// grid-template-rows: repeat(
//   ${(props) => props.end_date - props.st_date},
//   50px
// );

export const RotationalLevel = styled(Level)`
  grid-column: 1 / span 1;
  grid-row: 1 / -1;
`;

export const TextContainer = styled.div`
  margin: 100px auto;
  text-align: center;
`;

export const Text = styled.div`
  color: rgba(0, 0, 0, 0.36);
  padding: 5px;
`;

export const Padding = styled.div`
  padding: ${(props) => props.pd || '25px'};
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.15px;
  &.back {
    color: rgba(0, 0, 0, 0.54);
  }
`;

export const Gap = styled.div`
  padding: 20px;
`;

export const RotationalCourse = styled.div`
  grid-row: ${(props) => `${props.row_st_date} / ${props.row_end_date}`};
  grid-column: ${(props) => `${props.col_st_date} / ${props.col_end_date}`};
  background-color: ${(props) => props.bg};
  position: relative;
  min-width: 7em;
`;

export const Course = styled.div`
  grid-row: ${(props) => `${props.row_st_date} / ${props.row_end_date}`};
  grid-column: ${(props) => `${props.col_st_date} / ${props.col_end_date}`};
  background-color: ${(props) => props.bg};
  position: relative;
  min-width: 10em;
`;

export const CourseAlter = styled.div`
  grid-row: ${(props) => `${props.row_st_date} / ${props.row_end_date}`};
  grid-column: ${(props) => `${props.col_st_date} / ${props.col_end_date}`};
  background-color: ${(props) => props.bg};
  position: relative;
  min-width: 0em;
  display: grid;
  grid-gap: 1rem;
  grid-template-columns: repeat(1, 100px);
  align-items: center;
  justify-items: center;
`;

// display: grid;
// grid-template-rows: repeat(${(props) => props.row_len}, 50px);

export const CourseEvent = styled.div`
  display: grid;
  align-items: center;
  justify-items: center;
  grid-row: ${(props) => `${props.st_date} / ${props.end_date}`};
  grid-column: 1 / -1;
`;
// grid-column: 1 / span 1;

export const CourseEventContent = styled.div`
  padding: 5px 15px;
  border-radius: 22px;
  background-color: rgba(0, 0, 0, 0.15);
`;

export const Margin = styled.div`
  margin: ${(props) => props.mg};
`;

export const DisplayTag = styled.div`
  color: #3e95ef;
  background-color: rgba(0, 0, 0, 0.06);
  border-radius: 22px;
  padding: 10px 15px;
  margin: 0px 10px 10px 10px;
`;

export const Heading = styled.div`
  font-weight: ${(props) => props.fw};
  font-size: ${(props) => props.fs};
  margin: ${(props) => props.mg};
  padding: ${(props) => props.pd};
`;

export const Paragraph = styled.p`
  margin: 10px 0;
  font-size: 16px;
`;

export const LineSpace = styled.div`
  display: block;
`;
export const Line = styled.div`
  border: 1px solid grey;
  height: 50px;
`;

export const DisplaySemesterTag = styled.div`
  color: ${(props) => props.color || '#3E95EF'};
  background-color: rgba(0, 0, 0, 0.06);
  border-radius: 22px;
  padding: 8px 15px;
  margin: 10px 5px 20px 20px;
  cursor: pointer;
  line-height: 20px;
  letter-spacing: 0.25px;
  &:focus {
    outline: none;
  }
  &.active {
    border: 2px solid;
  }
  &.light {
    color: #3e95ef;
    border: 2px solid #3e95ef;
  }
`;

export const HeaderRowWrapper = styled.div`
  display: grid;
  grid-template-columns: auto;
  align-items: end;
  margin-left: 25px;

  &.design {
    grid-template-columns: min-content auto;
  }
`;

export const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: min-content;
  background-color: #7145cd;
  border-radius: 16px 16px 0 0;
  color: white;
  padding: 15px 25px;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0.25px;
  width: 10em;

  &.none {
    display: none;
  }
`;
