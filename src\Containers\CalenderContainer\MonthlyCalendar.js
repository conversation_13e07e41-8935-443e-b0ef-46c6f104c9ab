import React from 'react';
// import dateFns from "date-fns";
import * as dateFns from 'date-fns';
import './Calendar.css';
import moment from 'moment';
import { NotificationManager } from 'react-notifications';
import { Trans, withTranslation } from 'react-i18next';
import i18n from '../../i18n';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import { showArabicMailShow } from 'utils';
class CalenderComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentMonth: new Date(),
      selectedDate: new Date(),
      loadOnce: true,
      wdNames: [
        i18n.t('iweeddays.ahad'),
        i18n.t('iweeddays.ithnin'),
        i18n.t('iweeddays.thulatha'),
        i18n.t('iweeddays.arbaa'),
        i18n.t('iweeddays.khams'),
        i18n.t('iweeddays.jumuah'),
        i18n.t('iweeddays.sabt'),
      ],
      iMonthNames: [
        i18n.t('imonths.muharram'),
        i18n.t('imonths.safar'),
        i18n.t('imonths.rabi_ul_awwal'),
        i18n.t('imonths.rabi_ul_akhir'),
        i18n.t('imonths.jumadal_ula'),
        i18n.t('imonths.jumadal_akhira'),
        i18n.t('imonths.rajab'),
        i18n.t('imonths.sha_ban'),
        i18n.t('imonths.ramadan'),
        i18n.t('imonths.shawwal'),
        i18n.t('imonths.dhul_qa_ada'),
        i18n.t('imonths.dhul_hijja'),
      ],
    };
  }

  componentDidMount() {
    let currentMonth = new Date(this.props.currentMonth);
    this.setState({
      currentMonth,
    });
  }

  // componentDidUpdate(){
  // 	const clearTimer = () => clearInterval(interval);
  // 	const interval = setTimeout(() => {
  // 		if(this.state.loadOnce){
  // 			let currentMonth = new Date(this.props.currentMonth)
  // 			this.setState({currentMonth:currentMonth,loadOnce:false});
  // 			clearTimer();
  // 		}
  // 	}, 3000);
  // }

  renderHeader() {
    return (
      <div className="header row flex-middle">
        <div className="col col-start">
          <div className="icon" onClick={this.prevMonth}>
            LE
          </div>
        </div>
        <div className="col col-center">
          {/* <span>{dateFns.format(this.state.currentMonth, dateFormat)}</span>
					<span>{this.getHijriDate(this.state.currentMonth)}</span> */}
        </div>
        <div className="col col-end" onClick={this.nextMonth}>
          <div className="icon">RI</div>
        </div>
      </div>
    );
  }

  renderDays() {
    const dateFormat = 'dddd';
    const days = [];

    let startDate = dateFns.startOfWeek(this.props.currentMonth);

    for (let i = 0; i < 7; i++) {
      days.push(
        <div className="col col-center" key={i}>
          {dateFns.format(dateFns.addDays(startDate, i), dateFormat)}
        </div>
      );
    }

    return <div className="days row">{days}</div>;
  }

  renderCells() {
    const { selectedDate } = this.state;
    const currentMonth = this.props.currentMonth;
    const monthStart = dateFns.startOfMonth(currentMonth);
    const monthEnd = dateFns.endOfMonth(monthStart);
    const startDate = dateFns.startOfWeek(monthStart);
    let endDate = dateFns.endOfWeek(monthEnd);

    const calendarWeeks = dateFns.differenceInCalendarWeeks(monthEnd, monthStart);

    if (calendarWeeks < 5) {
      endDate = dateFns.addWeeks(endDate, 1);
    }

    let arMonths = [];
    const dateFormat = 'd';
    const rows = [];

    let days = [];
    let day = startDate;
    let formattedDate = '';

    while (day <= endDate) {
      for (let i = 0; i < 7; i++) {
        formattedDate = dateFns.format(day, dateFormat);

        const cloneDay = day;
        arMonths.push(this.getHijriMonth(day));
        days.push(
          <div
            onClick={() => {
              this.onDateClick(cloneDay);
            }}
            className={`td ${
              !dateFns.isSameMonth(day, monthStart)
                ? 'disabled'
                : dateFns.isSameDay(day, selectedDate)
                ? 'selected-date' //selected
                : ''
            }`}
            key={day}
            // onClick={() => this.onDateClick(cloneDay)}
          >
            <span className="number">{formattedDate}</span>
            {showArabicMailShow() && (
              <p
                className={`hijriNumber ${
                  !dateFns.isSameMonth(day, monthStart)
                    ? 'disabled'
                    : dateFns.isSameDay(day, selectedDate)
                    ? 'selected-date'
                    : '' //selected
                }`}
              >
                {this.getHijriDayNumber(day)}
              </p>
            )}
          </div>
        );
        day = dateFns.addDays(day, 1);
      }
      rows.push(
        <div className="tr" key={day}>
          {days}
        </div>
      );
      days = [];
    }

    const uniqMonths = new Set(arMonths);
    const at = [...uniqMonths];

    return {
      rows: <React.Fragment>{rows}</React.Fragment>,
      monthNames: at.join(' / '),
    };
  }

  onDateClick = (day) => {
    this.setState({
      selectedDate: day,
    });

    let selectedDate = moment(day).format('YYYY-MM-DD');
    let academicYearStartDate = moment(this.props.academicYearStartDate).format('YYYY-MM-DD');
    let academicYearEndDate = moment(this.props.academicYearEndDate).format('YYYY-MM-DD');

    if (
      Date.parse(academicYearStartDate) <= Date.parse(selectedDate) &&
      Date.parse(selectedDate) <= Date.parse(academicYearEndDate)
    ) {
      localStorage.setItem('eventDate', day);
      if (this.props.isCurrentCalendar && this.props.clicked !== '') {
        this.props.clicked();
      }
    } else {
      NotificationManager.error(
        `${t('eventDateBetween', {
          startDate: moment(academicYearStartDate).format('Do MMM YY'),
          endDate: moment(academicYearEndDate).format('Do MMM YY'),
        })}`
      );
    }
  };

  nextMonth = () => {
    this.setState({
      currentMonth: dateFns.addMonths(this.props.currentMonth, 1),
    });
  };

  prevMonth = () => {
    this.setState({
      currentMonth: dateFns.subMonths(this.props.currentMonth, 1),
    });
  };

  gmod = (n, m) => {
    return ((n % m) + m) % m;
  };

  kuwaiticalendar = (date) => {
    let today = date;

    let adjust = 0;
    let adjustmili = 1000 * 60 * 60 * 24 * adjust;
    let todaymili = today.getTime() + adjustmili;
    today = new Date(todaymili);

    let day = today.getDate();
    let month = today.getMonth();
    let year = today.getFullYear();
    let m = month + 1;
    let y = year;
    if (m < 3) {
      y -= 1;
      m += 12;
    }

    let a = Math.floor(y / 100);
    let b = 2 - a + Math.floor(a / 4);
    if (y < 1583) b = 0;
    if (y === 1582) {
      if (m > 10) b = -10;
      if (m === 10) {
        b = 0;
        if (day > 4) b = -10;
      }
    }

    let jd = Math.floor(365.25 * (y + 4716)) + Math.floor(30.6001 * (m + 1)) + day + b - 1524;

    b = 0;
    if (jd > 2299160) {
      a = Math.floor((jd - 1867216.25) / 36524.25);
      b = 1 + a - Math.floor(a / 4);
    }
    let bb = jd + b + 1524;
    let cc = Math.floor((bb - 122.1) / 365.25);
    let dd = Math.floor(365.25 * cc);
    let ee = Math.floor((bb - dd) / 30.6001);
    day = bb - dd - Math.floor(30.6001 * ee);
    month = ee - 1;
    if (ee > 13) {
      cc += 1;
      month = ee - 13;
    }
    year = cc - 4716;

    let wd;

    if (adjust) {
      wd = this.gmod(jd + 1 - adjust, 7) + 1;
    } else {
      wd = this.gmod(jd + 1, 7) + 1;
    }

    let iyear = 10631 / 30;
    let epochastro = 1948084;

    let shift1 = 8.01 / 60;

    let z = jd - epochastro;
    let cyc = Math.floor(z / 10631);
    z = z - 10631 * cyc;
    let j = Math.floor((z - shift1) / iyear);
    let iy = 30 * cyc + j;
    z = z - Math.floor(j * iyear + shift1);
    let im = Math.floor((z + 28.5001) / 29.5);
    if (im === 13) im = 12;
    let id = z - Math.floor(29.5001 * im - 29);

    let myRes = new Array(8);

    myRes[0] = day; //calculated day (CE)
    myRes[1] = month - 1; //calculated month (CE)
    myRes[2] = year; //calculated year (CE)
    myRes[3] = jd - 1; //julian day number
    myRes[4] = wd - 1; //weekday number
    myRes[5] = id; //islamic date
    myRes[6] = im - 1; //islamic month
    myRes[7] = iy; //islamic year

    return myRes;
  };

  getHijriDate = (date) => {
    let iDate = this.kuwaiticalendar(date);
    let outputIslamicDate =
      this.state.wdNames[iDate[4]] +
      ', ' +
      iDate[5] +
      ' ' +
      this.state.iMonthNames[iDate[6]] +
      ' ' +
      iDate[7] +
      ' AH';
    return outputIslamicDate;
  };

  getHijriDayNumber = (date) => {
    var dt = new Date(date);
    dt.setDate(dt.getDate() - 1);
    let iDate = this.kuwaiticalendar(dt);
    return iDate[5];
  };

  getHijriMonth = (date) => {
    let iDate = this.kuwaiticalendar(date);
    //let outputIslamicDate = wdNames[iDate[4]] + ", " + iDate[5] + " " + iMonthNames[iDate[6]] + " " + iDate[7] + " AH";
    return this.state.iMonthNames[iDate[6]];
  };

  render() {
    const { t } = this.props;
    const dateFormat = 'MMM yyyy';
    let gregorianMonthYear = dateFns.format(this.props.currentMonth, dateFormat);
    const monthYear = gregorianMonthYear.split(' ');
    const month = monthYear[0];
    gregorianMonthYear = `${t('calender.' + month)}  ${monthYear[1]}`;
    const { rows, monthNames } = this.renderCells();
    return (
      <div className="mb-4">
        <div className="monthCalendar">
          {/* <div>{this.renderHeader()}</div> */}
          <div className="justify-content-between">
            {/* <span>
								<i
									className="fa fa-chevron-left"
									onClick={this.prevMonth}
									aria-hidden="true"
								></i>
							</span> */}
            <span>
              <p className="text-center m-0">{gregorianMonthYear}</p>
              {showArabicMailShow() && <p className="text-center hijriMonthName">{monthNames}</p>}
            </span>
            {/* <span>
								<i
									className="fa fa-chevron-right float-right pt-5px"
									onClick={this.nextMonth}
									aria-hidden="true"
								></i>
							</span> */}
          </div>
          <div className="card border-none">
            <div className="table text-center" style={{ marginTop: '4%' }}>
              <div className="tr">
                <div className="td">
                  <Trans i18nKey={'calender.sat'}></Trans>
                </div>
                <div className="td">
                  <Trans i18nKey={'calender.mon'}></Trans>
                </div>
                <div className="td">
                  <Trans i18nKey={'calender.tus'}></Trans>
                </div>
                <div className="td">
                  <Trans i18nKey={'calender.wed'}></Trans>
                </div>
                <div className="td">
                  <Trans i18nKey={'calender.thu'}></Trans>
                </div>
                <div className="td">
                  <Trans i18nKey={'calender.fri'}></Trans>
                </div>
                <div className="td">
                  <Trans i18nKey={'calender.sat'}></Trans>
                </div>
              </div>
              {rows}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

CalenderComponent.propTypes = {
  currentMonth: PropTypes.instanceOf(Date),
  isCurrentCalendar: PropTypes.bool,
  clicked: PropTypes.func,
  t: PropTypes.func,
  academicYearStartDate: PropTypes.string,
  academicYearEndDate: PropTypes.string,
};
const Calender = withTranslation()(CalenderComponent);

export default Calender;
