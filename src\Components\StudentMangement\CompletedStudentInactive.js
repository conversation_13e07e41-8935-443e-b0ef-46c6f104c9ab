import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import Input from '../../Widgets/FormElements/Input/Input';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import { getEnvCountryCode, getEnvCountryLength, maxDateSet, ucFirst } from '../../utils';
import { Accordion, Card, Button, Modal } from 'react-bootstrap';
import ProfileText from '../../Widgets/ProfileText';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import { BATCH } from '../../constants';
import Select from 'react-select';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { isIndVer, isMobileVerifyMandatory } from 'utils';

const gender = [
  ['Male', 'male'],
  ['Female', 'female'],
];
let id;
// let pageNum;
class studentActive extends Component {
  constructor(props) {
    super(props);
    this.state = {
      first: '',
      last: '',
      middle: '',
      family: '',
      empId: '',
      _nationality_id: [],
      _nationality_name: '',
      countries: [],
      buildingNo: '',
      nationalId: '',
      city: '',
      distric: '',
      zipCode: '',
      unit: '',
      passport_no: '',
      username: '',
      email: '',
      mobile: '',
      office_extension: '',
      _nationality_id_doc: '',
      _address_doc: '',
      _college_id_doc: '',
      _school_certificate_doc: '',
      _entrance_exam_certificate_doc: '',
      _admission_order_doc: '',
      edit: false,
      selectGender: gender[0][1][2],
      firstError: '',
      middleError: '',
      lastError: '',
      empIdError: '',
      nationalIdError: '',
      selectGenderError: '',
      program_no: '',
      programName: '',
      enrollment_year: '',
      academic: '',
      dob: '',
      mobileView: false,
      minutes: 3,
      seconds: 0,
      resendOtp: false,
      reason: '',
      otp: '',
      confirmMobile: false,
      active: false,
      uploadedDoc: [],
      programData: [],
      contact: [],
      relation_type: '',
      selectedBatch: '',
    };
  }

  componentDidMount() {
    id = this.props.location.search.split('=')[1];
    this.fetchApi(id);
  }

  componentWillUnmount() {
    clearInterval(this.myInterval);
  }
  timer = () => {
    this.myInterval = setInterval(() => {
      const { seconds, minutes } = this.state;
      if (seconds > 0) {
        this.setState(({ seconds }) => ({
          seconds: seconds - 1,
        }));
      }
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(this.myInterval);
        } else {
          this.setState(({ minutes }) => ({
            minutes: minutes - 1,
            seconds: 59,
          }));
        }
      }
    }, 1000);
  };

  fetchApi = (id) => {
    this.setState({
      isLoading: true,
    });
    let field = { field: ['name', 'no'] };
    const countryCode = getEnvCountryCode();
    axios
      .get(`/digi_program?limit=200&pageNo=1`, field)
      .then((res) => {
        const data = res.data.data.map((data) => {
          return {
            name: data.name,
            value: data.program_no,
          };
        });
        data.unshift({
          name: 'Select program',
          value: '',
        });
        this.setState({
          programData: data,
          isLoading: false,
        });
      })
      .catch((ex) => {
        this.setState({
          programData: [],
          isLoading: false,
        });
      });

    axios
      .get(`country?limit=300&pageNo=1`)
      .then((res) => {
        const duplicateAr = res.data.data.filter(
          (v, i, a) => a.findIndex((t) => t.name === v.name) === i
        );

        const countries = duplicateAr.map((data) => {
          return {
            label: data.name,
            value: data.name,
            id: data._id,
          };
        });
        this.setState({
          countries: countries,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          countries: [],
          isLoading: false,
        });
      });

    axios
      .get(`/user/student/${id}`)
      .then((res) => {
        const data = res.data.data;
        let country = [];
        if (data.address._nationality_id !== null) {
          country = [
            {
              label: data.address._nationality_id,
              value: data.address._nationality_id,
            },
          ];
        }

        this.setState({
          data: data,
          first: data.name.first,
          last: data.name.last,
          middle: data.name.middle,
          family: data.name.family,
          academic: data.academic,
          nationalId: data.address.nationality_id,
          _nationality_id: country,
          _nationality_name: data.address._nationality_id,
          buildingNo: data.address.building,
          city: data.address.city,
          distric: data.address.district,
          zipCode: data.address.zip_code,
          unit: data.address.unit,
          passport_no: data.address.passport_no,
          program_no: data.program_no,
          enrollment_year: data.enrollment_year,
          email: data.email,
          mobile: data.mobile !== '' ? String(data.mobile).replace(countryCode, '') : '',
          selectGender: data.gender,
          dob: data.dob,
          contact: data.contact,
          uploadedDoc: [
            {
              name: undefined,
            },
            {
              image:
                data.student_docs._school_certificate_doc !== undefined &&
                data.student_docs._school_certificate_doc !== ''
                  ? data.student_docs._school_certificate_doc
                  : '',
              name: t('user_management.school_certificate'),
              isOpen: false,
            },
            {
              image:
                data.student_docs._entrance_exam_certificate_doc !== undefined &&
                data.student_docs._entrance_exam_certificate_doc !== ''
                  ? data.student_docs._entrance_exam_certificate_doc
                  : '',
              name: t('user_management.entrance_exam_certificate'),
              isOpen: false,
            },
            {
              image:
                data.enrollment._admission_order_doc !== undefined &&
                data.enrollment._admission_order_doc !== ''
                  ? data.enrollment._admission_order_doc
                  : '',
              name: t('user_management.admission_documents'),
              isOpen: false,
            },
            {
              image:
                data.id_doc._college_id_doc !== undefined && data.id_doc._college_id_doc !== ''
                  ? data.id_doc._college_id_doc
                  : '',
              name: t('user_management.college_id'),
              isOpen: false,
            },
            {
              image:
                data.address._nationality_id_doc !== undefined &&
                data.address._nationality_id_doc !== ''
                  ? data.address._nationality_id_doc
                  : '',
              name: t('user_management.national_resident_id'),
              isOpen: false,
            },
            {
              image:
                data.address._address_doc !== undefined && data.address._address_doc !== ''
                  ? data.address._address_doc
                  : '',
              name: t('user_management.national_address'),
              isOpen: false,
            },
          ],
          isLoading: false,
          selectedBatch: data.batch,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };
  handleEdit = () => {
    this.setState({
      edit: true,
    });
  };

  handleChangeText = (e, name) => {
    if (name === 'first') {
      this.setState({
        first: e.target.value,
        firstError: '',
      });
    }

    if (name === 'middle') {
      this.setState({
        middle: e.target.value,
        middleError: '',
      });
    }
    if (name === 'last') {
      this.setState({
        last: e.target.value,
        lastError: '',
      });
    }

    if (name === 'empId') {
      this.setState({
        empId: e.target.value,
        empIdError: '',
      });
    }
    if (name === 'nationalId') {
      this.setState({
        nationalId: e.target.value,
        nationalIdError: '',
      });
    }
    if (name === 'family') {
      this.setState({
        family: e.target.value,
        familyError: '',
      });
    }

    if (name === 'academic') {
      this.setState({
        academic: e.target.value,
        academicError: '',
      });
    }

    if (name === 'passport') {
      this.setState({
        passport_no: e.target.value,
        passportError: '',
      });
    }
    if (name === 'program_no') {
      this.setState({
        program_no: e.target.value,
        program_noError: '',
      });
    }

    if (name === 'DOB') {
      this.setState({
        dob: e,
        dobError: '',
      });
    }

    if (name === 'buildingNo') {
      this.setState({
        buildingNo: e.target.value,
        buildingNoError: '',
      });
    }
    if (name === 'street') {
      this.setState({
        street: e.target.value,
        streetError: '',
      });
    }
    if (name === 'city') {
      this.setState({
        city: e.target.value,
        cityError: '',
      });
    }
    if (name === 'distric') {
      this.setState({
        distric: e.target.value,
        districError: '',
      });
    }

    if (name === 'zipCode') {
      if (isNaN(e.target.value)) return;
      this.setState({
        zipCode: e.target.value,
        zipCodeError: '',
      });
    }

    if (name === 'unit') {
      if (isNaN(e.target.value)) return;
      this.setState({
        unit: e.target.value,
        unitError: '',
      });
    }

    if (name === 'mobiles') {
      if (isNaN(e.target.value)) return;
      this.setState({
        mobile: e.target.value,
        mobileError: '',
      });
    }

    if (name === 'reason') {
      this.setState({
        reason: e.target.value,
        reasonError: '',
      });
    }
    if (name === 'otp') {
      if (isNaN(e.target.value)) return;
      this.setState({
        otp: e.target.value,
        otpError: '',
      });
    }
  };

  validation = () => {
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    //const dotAlpha = /^[a-zA-Z0-9/. -]*$/;
    const AlphaNum = /^[a-zA-Z0-9]+$/;
    const Number = /^[0-9]+$/;
    let firstError = '';
    let middleError = '';
    let lastError = '';
    //let userNameError = '';
    let nationalIdError = '';
    //let emailError = '';
    let selectGenderError = '';
    let academicError = '';
    let dobError = '';
    let passportError = '';
    let program_noError = '';
    let mobileError = '';
    let buildingNoError = '';
    let cityError = '';
    let districError = '';
    let zipCodeError = '';
    let unitError = '';
    let selectedBatchError = '';
    const countryCodeLength = getEnvCountryLength();
    if (this.state.selectGender === 'l') {
      selectGenderError = 'Choose Gender';
    }
    if (!this.state.selectGender) {
      selectGenderError = 'Choose Gender';
    }
    if (this.state.dob === '') {
      dobError = 'DOB Field is Required';
    }

    if (!this.state.mobile) {
      mobileError = 'Mobile no is Required';
    } else if (!space.test(this.state.mobile)) {
      mobileError = 'Space not allowed beginning & end';
    } else if (parseInt(String(this.state.mobile).length) !== parseInt(countryCodeLength)) {
      mobileError = `Should be ${countryCodeLength} character is required`;
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Number Only Allow';
    }

    if (!this.state.academic) {
      academicError = 'Academic ID is Required';
    }
    if (!space.test(this.state.academic)) {
      academicError = 'Space not allowed beginning & end';
    } else if (!AlphaNum.test(this.state.academic)) {
      academicError = 'Special character are not allowed';
    } else if (this.state.academic.length <= 0) {
      academicError = 'Minimum 1 character is required ';
    }

    if (!this.state.program_no) {
      program_noError = 'Program Number is Required';
    }
    // if (!space.test(this.state.program_no)) {
    //   program_noError = "Space not allowed beginning & end";
    // } else if (!AlphaNum.test(this.state.program_no)) {
    //   program_noError = "Text Only allowed";
    // } else if (this.state.program_no.length <= 2) {
    //   program_noError = "Minimum 3 character is required ";
    // }

    if (!this.state.first) {
      firstError = 'First Name is Required';
    }
    if (!space.test(this.state.first)) {
      firstError = 'Space not allowed beginning & end';
    }
    // else if (!spaceAlpha.test(this.state.first)) {
    //   firstError = 'Text Only allowed';
    // }
    else if (this.state.first.length <= 2) {
      firstError = 'Minimum 3 character is required ';
    }

    //   if (this.state.middle) {
    //     if (!space.test(this.state.middle)) {
    //     middleError = "Space not allowed beginning & end";
    //   } else if (!spaceAlpha.test(this.state.middle)) {
    //     middleError = "Text Only allowed";
    //   } else if (this.state.middle.length <= 2) {
    //     middleError = "Minimum 3 character is required ";
    //   }
    // }

    if (!this.state.last) {
      lastError = 'Last Name is Required';
    } else if (!space.test(this.state.last)) {
      lastError = 'Space not allowed beginning & end';
    }
    //  else if (!spaceAlpha.test(this.state.last)) {
    //   lastError = 'Text Only allowed';
    // }
    else if (!isIndVer() && this.state.last.length <= 2) {
      lastError = 'Minimum 3 character is required ';
    }

    if (!this.state.nationalId) {
      nationalIdError = 'National/Residence ID is Required';
    }
    if (!space.test(this.state.nationalId)) {
      nationalIdError = 'Space not allowed beginning & end';
    } else if (!AlphaNum.test(this.state.nationalId)) {
      nationalIdError = 'Special character are not allowed';
    } else if (this.state.nationalId.length <= 4) {
      nationalIdError = 'Minimum 5 character is required ';
    }

    if (this.state.passport_no) {
      if (!space.test(this.state.passport_no)) {
        passportError = 'Space not allowed beginning & end';
      } else if (!AlphaNum.test(this.state.passport_no)) {
        passportError = 'Special Charcter not Alowed';
      } else if (this.state.passport_no.length <= 2) {
        passportError = 'Minimum 3 character is required ';
      }
    }

    if (!this.state.buildingNo) {
      buildingNoError = 'Building No, Street Name Field is Required';
    } else if (!space.test(this.state.buildingNo)) {
      buildingNoError = 'Space not allowed beginning & end';
    } else if (this.state.buildingNo.length <= 2) {
      buildingNoError = 'Minimum 3 character is required ';
    }

    if (!this.state.city) {
      cityError = 'City Name Field is Required';
    } else if (!space.test(this.state.city)) {
      cityError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.city)) {
      cityError = 'Text Only allowed';
    } else if (this.state.city.length <= 2) {
      cityError = 'Minimum 3 character is required ';
    }

    if (!this.state.distric) {
      districError = 'District Name Field is Required';
    } else if (!space.test(this.state.distric)) {
      districError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.distric)) {
      districError = 'Text Only allowed';
    } else if (this.state.distric.length <= 2) {
      districError = 'Minimum 3 character is required ';
    }

    if (!this.state.zipCode) {
      zipCodeError = 'Zip Code Field is Required';
    } else if (!space.test(this.state.zipCode)) {
      zipCodeError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.zipCode)) {
      zipCodeError = 'Numeric Only allowed';
    } else if (this.state.zipCode.length <= 2) {
      zipCodeError = 'Minimum 3 character is required ';
    } else if (parseInt(this.state.zipCode) > 1000000) {
      zipCodeError = 'Pls Enter 1000000 Lesser than value ';
    } else if (parseInt(this.state.zipCode) === 0) {
      zipCodeError = 'Pls Enter 0 Greater than value ';
    }

    if (!this.state.unit) {
      unitError = 'Floor Number Field is Required';
    } else if (!space.test(this.state.unit)) {
      unitError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.unit)) {
      unitError = 'Numeric Only allowed';
    } else if (parseInt(this.state.unit) > 50) {
      unitError = 'Pls Enter 50 Lesser than value ';
    } else if (parseInt(this.state.unit) === 0) {
      unitError = 'Pls Enter 0 Greater than value ';
    }

    if (this.state.selectedBatch === 'select') {
      selectedBatchError = `${isIndVer() ? 'Intake' : 'Batch'} is Required`;
    }

    if (
      firstError ||
      middleError ||
      lastError ||
      nationalIdError ||
      selectGenderError ||
      academicError ||
      passportError ||
      program_noError ||
      mobileError ||
      buildingNoError ||
      cityError ||
      districError ||
      zipCodeError ||
      unitError ||
      dobError ||
      selectedBatchError
    ) {
      this.setState({
        firstError,
        middleError,
        lastError,
        nationalIdError,
        selectGenderError,
        academicError,
        passportError,
        program_noError,
        mobileError,
        buildingNoError,
        cityError,
        districError,
        zipCodeError,
        unitError,
        dobError,
        selectedBatchError,
      });
      return false;
    }
    return true;
  };

  handleSingleSubmit = (e) => {
    id = this.props.location.search.split('=')[1];
    e.preventDefault();

    let selectedCountry;
    const countryCode = getEnvCountryCode();
    if (this.state._nationality_id !== null) {
      let countries = this.state.countries.filter((data) => {
        return data.label === this.state._nationality_id.label;
      });

      let defaultCountry = this.state.countries.find(
        (o) => o.label === this.state._nationality_name
      );

      if (countries[0] !== undefined) {
        selectedCountry = countries[0].id;
      } else if (defaultCountry !== undefined) {
        selectedCountry = defaultCountry.id;
      } else {
        selectedCountry = '';
      }
    } else {
      selectedCountry = '';
    }

    let contact = this.state.contact;

    let contacts;
    if (contact[0] !== undefined) {
      if (contact[0].relation_type === 'father' || contact[0].relation_type === 'mother') {
        contacts = {
          type: 'parent',
          father_name: contact[0].name,
          father_mobile:
            contact[0].mobile !== ''
              ? countryCode + String(contact[0].mobile).replace(countryCode, '')
              : '',
          father_email: contact[0].email,
          mother_name: contact[1].name,
          mother_mobile:
            contact[1].mobile !== ''
              ? countryCode + String(contact[1].mobile).replace(countryCode, '')
              : '',
          mother_email: contact[1].email,
        };
      } else if (contact[0].relation_type === 'guardian') {
        contacts = {
          type: 'guardian',
          // relation:contact[0].studendRelation,
          guardian_name: contact[0].name,
          guardian_mobile:
            contact[0].mobile !== ''
              ? countryCode + String(contact[0].mobile).replace(countryCode, '')
              : '',
          guardian_email: contact[0].email,
        };
      } else if (contact[0].relation_type === 'spouse') {
        contacts = {
          type: 'spouse',
          spouse_name: contact[0].name,
          spouse_mobile:
            contact[0].mobile !== ''
              ? countryCode + String(contact[0].mobile).replace(countryCode, '')
              : '',
          spouse_email: contact[0].email,
        };
      }
    }

    this.setState({
      isValidate: false,
    });
    if (this.validation()) {
      const data = {
        id: id,
        academic_no: this.state.academic,
        first_name: this.state.first,
        last_name: this.state.last,
        middle_name: this.state.middle,
        gender: this.state.selectGender,
        nationality_id: this.state.nationalId,
        _nationality_id: selectedCountry,
        // mobile:this.state.mobile,
        building: this.state.buildingNo,
        city: this.state.city,
        district: this.state.distric,
        zip_code: this.state.zipCode,
        unit: this.state.unit,
        passport_no: this.state.passport_no,
        program_no: this.state.program_no,
        contact: contacts,
        batch: this.state.selectedBatch,
      };

      if (this.state.dob !== '') {
        data.dob = moment(this.state.dob).format('YYYY-MM-DD');
      }

      this.setState({
        isLoading: true,
      });
      axios
        .put(`/user/user_edit`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              edit: false,
            },
            () => {
              this.fetchApi(id);
            }
          );

          NotificationManager.success(t('user_management.Profile_Edited_Successfully'));
        })
        .catch((error) => {
          NotificationManager.error(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
        selectGenderError: '',
      });
    }
  };

  mobileVerify = () => {
    this.setState({
      mobileView: true,
    });
  };

  mobileVerifyClose = () => {
    this.setState({
      mobileView: false,
      resendOtp: false,
      minutes: 3,
      seconds: 0,
      reason: '',
      otp: '',
    });
  };

  mobileValidtion = () => {
    const Number = /^[0-9]+$/;
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    let mobileError = '';
    let reasonError = '';
    const countryCodeLength = getEnvCountryLength();
    if (!this.state.mobile) {
      mobileError = 'Mobile No is Required';
    } else if (!Number.test(this.state.mobile)) {
      mobileError = 'Mobile No Numbers Only Allow';
    } else if (parseInt(String(this.state.mobile).length) !== parseInt(countryCodeLength)) {
      mobileError = `Mobile No ${countryCodeLength} character is required`;
    }

    if (this.state.reason === '') {
      reasonError = 'Reason Field is Required';
    }
    if (!space.test(this.state.reason)) {
      reasonError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.reason)) {
      reasonError = 'Text Only allowed';
    } else if (this.state.reason.length <= 2) {
      reasonError = 'Minimum 3 character is required ';
    }

    if (mobileError || reasonError) {
      this.setState({
        mobileError,
        reasonError,
      });
      return false;
    }
    return true;
  };

  otpValidation = () => {
    let otpError = '';
    const Number = /^[0-9]+$/;

    if (!this.state.otp) {
      otpError = 'OTP is Required';
    } else if (this.state.otp.length <= 3) {
      otpError = 'Minimum 4 character is required ';
    } else if (!Number.test(this.state.otp)) {
      otpError = 'Numbers Only Allow ';
    }

    if (otpError) {
      this.setState({
        otpError,
      });
      return false;
    }
    return true;
  };

  sendOTP = (e) => {
    e.preventDefault();
    const countryCode = getEnvCountryCode();
    id = this.props.location.search.split('=')[1];
    if (this.mobileValidtion()) {
      const data = {
        id: id,
        mobile: countryCode + this.state.mobile.toString(),
      };

      this.setState({
        isLoading: true,
        minutes: 3,
        seconds: 0,
      });
      axios
        .post(`/user/send_mobile_otp`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              resendOtp: true,
            },
            () => {
              this.timer();
            }
          );

          NotificationManager.success(t(`user_management.OTP_has_Been_Send`));
        })
        .catch((error) => {
          // let errorMessage='';
          NotificationManager.error(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };

  handleMobileSubmit = (e) => {
    e.preventDefault();
    const countryCode = getEnvCountryCode();
    id = this.props.location.search.split('=')[1];
    if (this.otpValidation()) {
      const data = {
        id: id,
        mobile: countryCode + this.state.mobile.toString(),
        reason: this.state.reason,
        otp: this.state.otp,
        admin_id: id,
      };

      this.setState({
        isLoading: true,
      });
      axios
        .post(`/user/change_mobile`, data)
        .then((res) => {
          this.setState({
            isLoading: false,
            resendOtp: false,
            confirmMobile: true,
            mobileView: false,
            otp: '',
            reason: '',
            minutes: 3,
            seconds: 0,
          });

          NotificationManager.success(t(`user_management.Phone_Number_Updated_Successfully`));
        })
        .catch((error) => {
          NotificationManager.error(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    }
  };
  handleActive = () => {
    this.setState({
      active: false,
      isLoading: true,
    });

    const data = {
      id: id,
      status: this.state.active,
    };

    axios
      .post(`/user/active_inactive`, data)
      .then((res) => {
        this.setState({
          isLoading: false,
        });
        this.props.history.push({
          pathname: '/student/management',
          state: {
            completeView: false,
            pendingView: true,
            inactiveView: false,
          },
        });

        NotificationManager.success(t('user_management.Student_In_Active_Successfully'));
      })
      .catch((error) => {
        // let errorMessage='';
        NotificationManager.error(`${error.response.data.message}`);
        this.setState({
          isLoading: false,
        });
      });
  };

  handleClickOpen = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = true;
    this.setState({
      uploadedDoc,
    });
  };

  handleClickClose = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = false;
    this.setState({
      uploadedDoc,
    });
  };

  handleSelect = (e, name) => {
    e.preventDefault();
    if (name === 'program') {
      this.setState({
        program_no: e.target.value,
        program_noError: '',
      });
    }

    if (name === 'batch') {
      this.setState({
        selectedBatch: e.target.value,
        selectedBatchError: '',
      });
    }
  };

  handleChange = (e, name, index) => {
    let contact = this.state.contact;

    if (name === 'name') {
      contact[index].name = e.target.value;
    }

    if (name === 'mobile') {
      contact[index].mobile = e.target.value;
    }

    if (name === 'email') {
      contact[index].email = e.target.value;
    }
    this.setState({
      contact,
    });
  };
  handleSelectedNationality = (_nationality_id) => {
    this.setState({
      _nationality_id,
    });
  };
  render() {
    const { minutes, seconds } = this.state;

    let selectedProgramName;
    for (let i = 0; i < this.state.programData.length; i++) {
      if (this.state.programData[i].value === this.state.program_no) {
        selectedProgramName = this.state.programData[i].name;
      }
    }

    const verifyMobile = isMobileVerifyMandatory();
    const countryCode = getEnvCountryCode();
    const countryCodeLength = getEnvCountryLength();

    return (
      <React.Fragment>
        <NotificationContainer />
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'user_management.student_management_profile'}></Trans>{' '}
        </div>

        <Loader isLoading={this.state.isLoading} />

        <div className="main pt-4">
          <div className="container">
            <div className="">
              <div className="float-left border border-radious-30">
                <div className="pt-2 ">
                  <div className="float-left pr-3 pl-3">
                    <p style={{ marginBottom: '11px' }}>
                      {' '}
                      <Trans i18nKey={'status_inactive'}></Trans>{' '}
                    </p>
                  </div>
                  <div className="float-left pr-3">
                    <label className="switch mb-0">
                      <input
                        checked={this.state.active}
                        type="checkbox"
                        onClick={this.handleActive}
                      />
                      <span className="slider_check round"></span>
                    </label>
                  </div>
                </div>
              </div>

              {this.state.edit === true ? (
                <div className="float-right">
                  <Button className="m-2" onClick={this.handleSingleSubmit}>
                    <Trans i18nKey={'save'}></Trans>
                  </Button>
                </div>
              ) : (
                <React.Fragment>
                  <div className="float-right">
                    <Button className="m-2" onClick={this.handleEdit}>
                      <Trans i18nKey={'edit'}></Trans>
                    </Button>
                  </div>
                </React.Fragment>
              )}

              <div className="float-right">
                <Button
                  variant="outline-primary"
                  className="m-2"
                  onClick={() => this.props.history.goBack()}
                >
                  <Trans i18nKey={'back'}></Trans>
                </Button>
              </div>
            </div>

            <div className="clearfix"></div>

            <div className="white p-4 mb-5">
              <div className="row">
                <div className="col-md-5">
                  {this.state.edit === true ? (
                    <React.Fragment>
                      {/* edit field start */}
                      <div className="mt-0">
                        <div className="row">
                          <h4 className="bold">
                            {' '}
                            <Trans i18nKey={'edit_personal_details'}></Trans>
                          </h4>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.first}
                              floatingLabel={<Trans i18nKey={'first_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'first')}
                              feedback={this.state.firstError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.middle}
                              floatingLabel={<Trans i18nKey={'middle_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'middle')}
                              feedback={this.state.middleError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.last}
                              floatingLabel={<Trans i18nKey={'last_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'last')}
                              feedback={this.state.lastError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.family}
                              floatingLabel={<Trans i18nKey={'family'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'family')}
                              feedback={this.state.familyError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.nationalId}
                              floatingLabel={<Trans i18nKey={'national/residence'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'nationalId')}
                              feedback={this.state.nationalIdError}
                            />
                          </div>
                          <div className="col-md-12">
                            <p className="floatinglabelcustom">
                              {' '}
                              <Trans i18nKey={'nationalty'}></Trans>
                            </p>
                            <Select
                              value={this.state._nationality_id}
                              options={this.state.countries}
                              onChange={this.handleSelectedNationality}
                              isClearable={true}
                            />
                          </div>

                          <div className="col-md-12">
                            <label>
                              {' '}
                              <Trans i18nKey={'gender'}></Trans>
                            </label>
                            <Input
                              elementType={'radio'}
                              elementConfig={gender}
                              className={'form-radio1'}
                              selected={this.state.selectGender}
                              labelclass="radio-label2"
                              onChange={(e) => this.onRadioGroupChange(e, 'selectGender')}
                              feedback={this.state.selectGenderError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.academic}
                              floatingLabel={<Trans i18nKey={'academic_no'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'academic')}
                              feedback={this.state.academicError}
                            />
                          </div>

                          <div className="col-md-12 customeDatePickWrapper">
                            <p className="floatinglabelcustom">
                              <Trans i18nKey={'dob'}></Trans>{' '}
                            </p>
                            <i className="fa fa-clock-o calenderCustom" aria-hidden="true"></i>
                            <DatePicker
                              placeholderText="Date Of Birth"
                              selected={this.state.dob !== '' ? new Date(this.state.dob) : ''}
                              onChange={(e) => this.handleChangeText(e, 'DOB')}
                              value={
                                this.state.dob !== ''
                                  ? moment(this.state.dob).format('D MMM YYYY')
                                  : moment(maxDateSet()).format('D MMM YYYY')
                              }
                              dateFormat="d MMM yyyy"
                              className={'form-control customeDatepick'}
                              showMonthDropdown
                              showYearDropdown
                              maxDate={maxDateSet()}
                              yearDropdownItemNumber={25}
                            />
                            <div className="InvalidFeedback">{this.state.dobError}</div>
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.passport_no}
                              floatingLabel={<Trans i18nKey={'passport_number'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'passport')}
                              feedback={this.state.passportError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: this.state.programData,
                              }}
                              value={this.state.program_no}
                              className={'customize_dropdown'}
                              label={<Trans i18nKey={'program_name'}></Trans>}
                              changed={(e) => this.handleSelect(e, 'program')}
                              feedback={this.state.program_noError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: BATCH,
                              }}
                              value={this.state.selectedBatch}
                              label={<Trans i18nKey={'user_management.batch'}></Trans>}
                              className={'customize_dropdown'}
                              changed={(e) => this.handleSelect(e, 'batch')}
                              feedback={this.state.selectedBatchError}
                            />
                          </div>

                          <h4 className="bold pt-3">Address Details</h4>

                          {/* <h3 className="f-16 pt-4 ">Address Details</h3> */}

                          <div className="col-md-12 pt-1">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              value={this.state.buildingNo}
                              floatingLabel={<Trans i18nKey={'building_no'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'buildingNo')}
                              feedback={this.state.buildingNoError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.distric}
                              floatingLabel={<Trans i18nKey={'district_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'distric')}
                              feedback={this.state.districError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={25}
                              value={this.state.city}
                              floatingLabel={<Trans i18nKey={'city_name'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'city')}
                              feedback={this.state.cityError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={10}
                              value={this.state.zipCode}
                              floatingLabel={<Trans i18nKey={'zip_code'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'zipCode')}
                              feedback={this.state.zipCodeError}
                            />
                          </div>

                          <div className="col-md-12">
                            <Input
                              elementType={'floatinginput'}
                              elementConfig={{
                                type: 'text',
                              }}
                              maxLength={2}
                              value={this.state.unit}
                              floatingLabel={<Trans i18nKey={'floor_no'}></Trans>}
                              changed={(e) => this.handleChangeText(e, 'unit')}
                              feedback={this.state.unitError}
                            />
                          </div>

                          <h4 className="bold pt-3">Contact Details </h4>
                          <div className="col-md-12">
                            <div className="row">
                              <div className={`col-md-${verifyMobile ? '8' : '12'}`}>
                                <Input
                                  elementType={'floatinginput'}
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  maxLength={countryCodeLength}
                                  value={String(this.state.mobile).replace(countryCode, '')}
                                  floatingLabel={<Trans i18nKey={'mobile_number'}></Trans>}
                                  changed={(e) => this.handleChangeText(e, 'mobiles')}
                                  // disabled={
                                  //   this.state.confirmMobile === true
                                  //     ? "disabled"
                                  //     : ""
                                  // }
                                  feedback={this.state.mobileError}
                                />
                              </div>

                              {verifyMobile && (
                                <div className="col-md-4">
                                  <p
                                    className="text-blue remove_hover mb-0 pt-4"
                                    onClick={
                                      String(this.state.mobile).replace(countryCode, '').length ===
                                      parseInt(countryCodeLength)
                                        ? this.mobileVerify
                                        : () => {}
                                    }
                                  >
                                    {' '}
                                    <Trans i18nKey={'change_verify'}></Trans>{' '}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="col-md-12">
                            <div className="row">
                              {this.state.contact &&
                                this.state.contact.map((data, index) => {
                                  return (
                                    <React.Fragment key={index}>
                                      <div className="col-md-6 pt-1">
                                        <Input
                                          elementType={'floatinginput'}
                                          elementConfig={{
                                            type: 'text',
                                          }}
                                          maxLength={30}
                                          value={data.name}
                                          floatingLabel={`${
                                            data.relation_type === 'guardian'
                                              ? 'Guardian'
                                              : data.relation_type === 'spouse'
                                              ? 'Spouse'
                                              : data.relation_type === undefined
                                              ? ''
                                              : data.relation_type
                                          } Name`}
                                          changed={(e) => this.handleChange(e, 'name', index)}
                                        />
                                        <Input
                                          elementType={'floatinginput'}
                                          elementConfig={{
                                            type: 'text',
                                          }}
                                          maxLength={countryCodeLength}
                                          value={
                                            data.mobile !== ''
                                              ? String(data.mobile).replace(countryCode, '')
                                              : ''
                                          }
                                          floatingLabel={'Mobile No'}
                                          changed={(e) => this.handleChange(e, 'mobile', index)}
                                        />
                                        <Input
                                          elementType={'floatinginput'}
                                          elementConfig={{
                                            type: 'text',
                                          }}
                                          maxLength={30}
                                          value={data.email}
                                          floatingLabel={<Trans i18nKey={'email'}></Trans>}
                                          changed={(e) => this.handleChange(e, 'email', index)}
                                        />
                                      </div>
                                    </React.Fragment>
                                  );
                                })}
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* edit field end */}
                    </React.Fragment>
                  ) : (
                    <React.Fragment>
                      {/* view field start */}
                      <div className="mt-0">
                        <h4 className="bold">
                          {' '}
                          <Trans i18nKey={'personal'}></Trans>{' '}
                        </h4>

                        <ProfileText
                          title={<Trans i18nKey={'first_name'}></Trans>}
                          value={this.state.first}
                          error={this.state.correction_first_name}
                          className="border-bottom pb-1"
                        />
                        <ProfileText
                          title={<Trans i18nKey={'middle_name'}></Trans>}
                          value={this.state.middle}
                          error={this.state.correction_middle_name}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'last_name'}></Trans>}
                          value={this.state.last}
                          error={this.state.correction_last_name}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'family_name'}></Trans>}
                          value={this.state.family}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'gender'}></Trans>}
                          value={this.state.selectGender}
                          error={this.state.correction_gender}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'national_id'}></Trans>}
                          value={this.state.nationalId}
                          error={this.state.correction_nationality_id}
                        />

                        <ProfileText
                          title={<Trans i18nKey={'academic_no'}></Trans>}
                          value={this.state.academic}
                          error={this.state.correction_academic_no}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'dob'}></Trans>}
                          value={
                            this.state.dob &&
                            this.state.dob !== undefined &&
                            moment(this.state.dob).format('D MMM YYYY')
                          }
                        />
                        <ProfileText
                          title={<Trans i18nKey={'email_id'}></Trans>}
                          value={this.state.email}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'nationality_optional'}></Trans>}
                          value={this.state._nationality_name}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'program_name'}></Trans>}
                          value={selectedProgramName}
                        />

                        <ProfileText
                          title="Batch"
                          value={
                            this.state.selectedBatch !== '' ? ucFirst(this.state.selectedBatch) : ''
                          }
                        />
                        <ProfileText title="Passport No" value={this.state.passport_no} />
                      </div>
                      <div className="mt-5">
                        <h4 className="bold">
                          <Trans i18nKey={'address'}></Trans>
                        </h4>
                        <ProfileText
                          title="Building No, Street Name"
                          value={this.state.buildingNo}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'city_name'}></Trans>}
                          value={this.state.city}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'district_name'}></Trans>}
                          value={this.state.distric}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'zip_code'}></Trans>}
                          value={this.state.zipCode}
                        />
                        <ProfileText
                          title={<Trans i18nKey={'floor_no'}></Trans>}
                          value={this.state.unit}
                        />
                      </div>
                      <div className="mt-5">
                        <h4 className="bold">
                          <Trans i18nKey={'contact_details'}></Trans>
                        </h4>

                        <ProfileText
                          title={<Trans i18nKey={'mobile_number'}></Trans>}
                          value={countryCode + this.state.mobile}
                          // error="ssss"
                        />
                        <div className="row">
                          {this.state.contact &&
                            this.state.contact
                              .filter((item) => item.relation_type !== 'guardian')
                              .map((data, i) => (
                                <div className="col-md-6" key={i}>
                                  <ProfileText
                                    title={`${
                                      data.relation_type === 'guardian'
                                        ? 'Guardian'
                                        : data.relation_type === 'spouse'
                                        ? 'Spouse'
                                        : data.relation_type === undefined
                                        ? ''
                                        : data.relation_type
                                    } Name`}
                                    value={data.name}
                                  />
                                  <ProfileText
                                    title={<Trans i18nKey={'mobile_number'}></Trans>}
                                    value={data.mobile}
                                  />
                                  <ProfileText
                                    title={<Trans i18nKey={'email'}></Trans>}
                                    value={data.email}
                                  />
                                </div>
                              ))}
                        </div>
                      </div>
                      {/* view field end */}
                    </React.Fragment>
                  )}
                </div>
                <div className="col-md-7">
                  <div className="mt-0">
                    <h4 className="bold d-flex">
                      <Trans i18nKey={'uploaded_documents'}></Trans>{' '}
                    </h4>{' '}
                    <Accordion defaultActiveKey="">
                      {this.state.uploadedDoc.map((data, index) => (
                        <React.Fragment key={index}>
                          {data.name !== undefined && (
                            <Card>
                              <Accordion.Toggle as={Card.Header} eventKey={index}>
                                {data.name}
                              </Accordion.Toggle>
                              <Accordion.Collapse eventKey={index}>
                                <Card.Body className="bg-white">
                                  {data.image !== '' && data.image !== undefined ? (
                                    <img
                                      className="w-100"
                                      onClick={() => this.handleClickOpen(index)}
                                      src={data.image}
                                      alt="#"
                                    />
                                  ) : (
                                    <div className="float-left">
                                      <Trans i18nKey={'no_image'}></Trans>
                                    </div>
                                  )}
                                  {data.isOpen === true && (
                                    <Lightbox
                                      clickOutsideToClose={false}
                                      mainSrc={data.image}
                                      onCloseRequest={() => this.handleClickClose(index)}
                                    />
                                  )}
                                </Card.Body>
                              </Accordion.Collapse>
                            </Card>
                          )}
                        </React.Fragment>
                      ))}
                    </Accordion>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* mobile view entry funtion start   */}

        <Modal show={this.state.mobileView} centered size="md" onHide={this.mobileVerify}>
          <Modal.Body>
            <div className=" pt-2">
              {' '}
              <Trans i18nKey={'user_management.enter_reason'}></Trans>
            </div>

            <div className="">
              <Input
                elementType={'floatinginput'}
                elementConfig={{
                  type: 'text',
                }}
                maxLength={40}
                value={this.state.reason}
                floatingLabel={<Trans i18nKey={'type_reason'}></Trans>}
                changed={(e) => this.handleChangeText(e, 'reason')}
                feedback={this.state.reasonError}
              />
            </div>

            <div className="pt-2">
              <b>
                <Trans i18nKey={'mobile_verify'}></Trans>{' '}
              </b>
            </div>
            <div className="pt-2">
              <b>
                <Trans i18nKey={'user_management.verify_text'}></Trans>
              </b>
              <br />
              <p className="text-gray pt-3 mb-0">
                {' '}
                <Trans i18nKey={'sent_mobile'}></Trans> {countryCode + this.state.mobile}
              </p>
            </div>

            <div className="pt-2">
              <p className="red mb-0">{this.state.mobileError} </p>
            </div>

            <div className=" row pt-2">
              <div className="col-md-6">
                {minutes === 0 && seconds === 0 ? (
                  <Button variant="outline-primary" onClick={this.sendOTP} className="f-14">
                    <Trans i18nKey={'resend_otps'}></Trans>{' '}
                  </Button>
                ) : (
                  <React.Fragment>
                    {this.state.resendOtp === true ? (
                      <Button variant="outline-primary" className="f-14" disabled>
                        <Trans i18nKey={'resend_otps'}></Trans>{' '}
                      </Button>
                    ) : (
                      <Button variant="outline-primary" onClick={this.sendOTP} className="f-14">
                        <Trans i18nKey={'sendotps'}></Trans>{' '}
                      </Button>
                    )}
                  </React.Fragment>
                )}
              </div>

              <div className="col-md-6">
                {this.state.resendOtp === true && (
                  <div className="col-md-12">
                    {minutes === 0 && seconds === 0 ? (
                      ''
                    ) : (
                      <h1 className="f-16 float-right pt-2">
                        <Trans i18nKey={'resend_otp'}></Trans> : {minutes}:
                        {seconds < 10 ? `0${seconds}` : seconds}
                      </h1>
                    )}
                  </div>
                )}
              </div>

              {this.state.resendOtp === true && (
                <div className="col-md-12 pt-2">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={4}
                    value={this.state.otp}
                    floatingLabel={<Trans i18nKey={'enterotp'}></Trans>}
                    changed={(e) => this.handleChangeText(e, 'otp')}
                    feedback={this.state.otpError}
                  />
                </div>
              )}
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline-primary" onClick={(e) => this.mobileVerifyClose(e)}>
              Close
            </Button>

            {this.state.resendOtp === true ? (
              <Button
                variant="primary"
                onClick={(e) => this.handleMobileSubmit(e)}
                className="f-14"
              >
                <Trans i18nKey={'submit'}></Trans>{' '}
              </Button>
            ) : (
              <Button variant="primary" className="f-14" disabled>
                <Trans i18nKey={'submit'}></Trans>{' '}
              </Button>
            )}
          </Modal.Footer>
        </Modal>

        {/* mobile view entry funtion start  */}
      </React.Fragment>
    );
  }
}

studentActive.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
};

export default withRouter(studentActive);
