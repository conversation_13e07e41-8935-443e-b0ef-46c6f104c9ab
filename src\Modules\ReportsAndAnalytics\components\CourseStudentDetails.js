import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { List, Map, fromJS } from 'immutable';
import { useParams } from 'react-router-dom';

import TableWithPagination from './TableWithPagination';
import CourseStudentDetailsFilters from './Filters/CourseStudentDetailsFilters';
import StaffStudentAttendanceReportModal from '../modal/StaffStudentAttendanceReportModal';

import * as actions from '../../../_reduxapi/reports_and_analytics/action';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import {
  selectCourseStudentDetails,
  selectUserAttendanceReport,
} from '../../../_reduxapi/reports_and_analytics/selectors';
import {
  capitalize,
  getURLParams,
  sortImmutableAlphaNumeric,
  dString,
  studentGroupRename,
} from '../../../utils';
import { calculatePercentage, exportStudentDetailsAsPDF, nameFormatIfMiddleName } from '../utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { t } from 'i18next';
function CourseStudentDetails(props) {
  const {
    activeInstitutionCalendar,
    getCourseStudentDetails,
    courseStudentDetails,
    getAttendanceReportByUserId,
    userAttendanceReport,
  } = props;

  const params = useParams();
  const [filters, setFilters] = useState(
    Map({
      studentGroup: '',
      gender: '',
      warning: '',
      isPopulated: false,
      searchInput: '',
    })
  );
  const [sort, setSort] = useState(Map({ sortBy: 'formattedName', sortDirection: 'asc' }));
  const [dialogData, setDialogData] = useState(Map({ open: false, student: Map() }));

  const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id');
  const programId = params.programId ? dString(params.programId) : '';
  const courseId = params.courseId ? dString(params.courseId) : '';
  const level = getURLParams('level', true);
  const term = getURLParams('term', true);
  const isRotation = getURLParams('rotation', true) === 'yes';
  const rotationCount = getURLParams('rotationCount', true);

  useEffect(() => {
    if (activeInstitutionCalendarId && programId && courseId && level && term) {
      getCourseStudentDetails({
        institutionCalendarId: activeInstitutionCalendarId,
        programId,
        courseId,
        level,
        term,
        ...(isRotation && { rotationCount }),
      });
    }
  }, [
    getCourseStudentDetails,
    activeInstitutionCalendarId,
    programId,
    courseId,
    level,
    term,
    isRotation,
    rotationCount,
  ]);

  useEffect(() => {
    const studentGroups = courseStudentDetails.get('student_details', List());
    if (studentGroups.size) {
      // && !filters.get('isPopulated')
      setFilters(
        filters.merge(
          Map({
            studentGroup: studentGroups.getIn([0, 'group_id'], ''),
            //isPopulated: true,
          })
        )
      );
    }
  }, [courseStudentDetails]); // eslint-disable-line

  function getActiveStudentGroup() {
    const studentGroupId = filters.get('studentGroup', '');
    return (
      courseStudentDetails
        .get('student_details', List())
        .find((sg) => sg.get('group_id') === studentGroupId) || Map()
    );
  }

  function getStudentGroupOptions() {
    return courseStudentDetails.get('student_details', List()).map((group) =>
      Map({
        name: studentGroupRename(group.get('group_name', ''), programId),
        value: group.get('group_id', ''),
      })
    );
  }

  function getSortableColumns() {
    return List([
      List(['formattedName', t('reports_analytics.student_name')]),
      List(['academicNo', t('reports_analytics.academic_no')]),
    ]).map((column) => Map({ value: column.get(0), name: column.get(1) }));
  }

  function getGenders() {
    return List([t('reports_analytics.male'), t('reports_analytics.female')]).map((gender) =>
      Map({ name: capitalize(gender), value: gender })
    );
  }

  function getWarningsList() {
    const studentList = getStudentList();
    if (studentList.size > 0) {
      const value = studentList
        .map((item) => item.get('no_of_warning', ''))
        .toJS()
        .reduce((unique, item) => (unique.includes(item) ? unique : [...unique, item]), [])
        .sort((a, b) => (a - b ? 1 : -1))
        .filter((el) => el !== '');
      if (value.length > 0) {
        const formattedValue = value.map((item) => {
          return { name: item, value: item };
        });
        return fromJS(formattedValue);
      }
      return List();
    }
    return List();
  }

  function getFilterOptions() {
    const warnings = getWarningsList();
    return Map({
      studentGroups: getStudentGroupOptions(),
      genders: getGenders(),
      warnings,
      sortableColumns: getSortableColumns(),
    });
  }

  function filterTableData(tableData = List()) {
    const gender = filters.get('gender', '');
    const warning = filters.get('warning', '');
    const searchInput = filters.get('searchInput', '');
    return tableData
      .filter((student) => {
        let incGender = true;
        let incWarning = true;
        if (gender) {
          incGender = student.get('gender', '').toLowerCase() === gender.toLowerCase();
        }
        if (warning) {
          incWarning = String(student.get('formattedWarning', '')) === warning;
        }

        return incGender && incWarning;
      })
      .filter((item) => {
        if (searchInput === '') return true;
        const studentName = item.get('formattedName', '') + ' ' + item.get('academicNo', '');
        return studentName.toLowerCase().trim().includes(searchInput.toLowerCase().trim());
      })
      .toList();
  }

  function sortTableData(tableData = List()) {
    return tableData.sort((item1, item2) => {
      const sortColumn = sort.get('sortBy');
      const sortDirection = sort.get('sortDirection');

      return sortImmutableAlphaNumeric(item1, item2, sortColumn, sortDirection);
    });
  }

  function getTableColumns() {
    return [
      { name: t('reports_analytics.student_name'), key: 'formattedName' },
      { name: t('reports_analytics.academic_no'), key: 'academicNo' },
      { name: t('reports_analytics.gender'), key: 'gender' },
      { name: t('reports_analytics.noOfSessions'), key: 'total_session' },
      { name: t('reports_analytics.noOfSessionsAttended'), key: 'formattedSessionsAttended' },
      { name: t('reports_analytics.noOfWarning'), key: 'formattedWarning' },
      { name: t('reports_analytics.attendance_percentage'), key: 'formattedAttendancePercentage' },
    ];
  }

  function prepareTableData(data = List()) {
    return data.map((student) => {
      const first = student.getIn(['name', 'first'], '');
      const middle = student.getIn(['name', 'middle'], '');
      const last = student.getIn(['name', 'last'], '');
      const noOfAttendedSessions = student.get('no_of_session_attended', 0);
      const noOfCompletedSessions = student.get('completed_session', 0);
      const permissionCount = student.get('permission_count', 0);
      const leaveCount = student.get('leave_count', 0);
      const onDutyCount = student.get('onduty_count', 0);
      const studentLateAbsent = student.get('studentLateAbsent', 0);
      const absentCount = student.get('absent_count', 0);
      const attendancePercentage = student.get('attendance_percentage', 0);
      return student.merge(
        Map({
          formattedName: nameFormatIfMiddleName(first, middle, last),
          academicNo: student.get('academic_no', ''),
          gender: capitalize(student.get('gender', '')),
          formattedSessionsAttended: `${noOfAttendedSessions} / ${noOfCompletedSessions} (P - ${noOfAttendedSessions}, A - ${absentCount}, PER - ${permissionCount}, L - ${leaveCount}, OD - ${onDutyCount}, LA - ${studentLateAbsent})`,
          formattedWarning: student.get('no_of_warning', '') || '---',
          formattedAttendancePercentage: `${
            attendancePercentage === null
              ? calculatePercentage(
                  noOfAttendedSessions + permissionCount + onDutyCount - studentLateAbsent,
                  noOfCompletedSessions,
                  false
                )
              : attendancePercentage.toFixed(2)
          }%`,
        })
      );
    });
  }

  function getStudentList() {
    const studentGroup = getActiveStudentGroup();
    return studentGroup.get('students', List());
  }

  function handleExportClick() {
    exportStudentDetailsAsPDF(
      getTableColumns(),
      courseStudentDetails.get('course_details', Map()),
      filterTableData(prepareTableData(getStudentList()))
    );
  }

  function handleStudentClick(student) {
    getAttendanceReportByUserId({
      institutionCalendarId: activeInstitutionCalendarId,
      programId,
      courseId,
      level,
      term,
      userId: student.get('_id', ''),
      userType: 'student',
      ...(isRotation && { rotationCount }),
    });
    setDialogData(Map({ open: true, student }));
  }

  function handleDialogClose() {
    setDialogData(Map({ open: false, student: Map() }));
  }

  return (
    <div className="mt-3 mb-3 bg-white border-radious-8">
      <CourseStudentDetailsFilters
        options={getFilterOptions()}
        filters={filters}
        sort={sort}
        onFilterChange={setFilters}
        onSortChange={setSort}
        resetLabelName={t('reports_analytics.all')}
        handleExport={handleExportClick}
        isExportActive={CheckPermission(
          'tabs',
          'Reports and Analytics',
          'Course Details',
          '',
          'Student Details',
          'Export'
        )}
      />
      <TableWithPagination
        columns={getTableColumns()}
        data={sortTableData(filterTableData(prepareTableData(getStudentList())))}
        handleRowClick={handleStudentClick}
      />
      <StaffStudentAttendanceReportModal
        open={dialogData.get('open', false)}
        onClose={handleDialogClose}
        userType="student"
        userData={dialogData.get('student', Map())}
        attendanceReport={userAttendanceReport}
        programId={programId}
      />
    </div>
  );
}

CourseStudentDetails.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  courseStudentDetails: PropTypes.instanceOf(Map),
  getCourseStudentDetails: PropTypes.func,
  userAttendanceReport: PropTypes.instanceOf(Map),
  getAttendanceReportByUserId: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    courseStudentDetails: selectCourseStudentDetails(state),
    userAttendanceReport: selectUserAttendanceReport(state),
  };
};

export default connect(mapStateToProps, actions)(CourseStudentDetails);
