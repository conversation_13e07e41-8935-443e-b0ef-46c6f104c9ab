import React from 'react';
import { useHistory, useParams } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { makeStyles } from '@mui/styles';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import { getLabelName } from 'Modules/Shared/v2/Configurations';
import i18n from '../../../i18n';
import { useTheme } from '@mui/material/styles';
function GlobalConfigSidebar({ loggedInUserData }) {
  const params = useParams();
  const { type, id, name } = params;

  const theme = useTheme();
  const useStylesFunction = makeStyles(() => ({
    root: {
      width: '100%',
      height: '50px',
      backgroundColor: theme.palette.background.paper,
      paddingTop: '0px',
      '&$selected': {
        backgroundColor: '#EFF9FB',
        '&:hover': {
          backgroundColor: '#EFF9FB',
        },
      },
      '&:hover': {
        backgroundColor: '#EFF9FB',
      },
    },
    nested: {
      paddingLeft: theme.spacing(4),
    },
    selected: {},
  }));

  const classes = useStylesFunction();
  const history = useHistory();

  const usity = history.location.pathname.includes('/u-sity') ? 0 : 1;
  const program = history.location.pathname.includes('/program') ? 0 : 1;
  const college = history.location.pathname.includes('/college') ? 0 : 1;

  // const [open, setOpen] = React.useState({
  //   college: false,
  //   program: true,
  // });

  // const handleClick = (name, index) => {
  //   setOpen({ ...open, [name]: !open[name] });
  //   setSelectedIndex(index);
  // };

  const isUniversityAdmin = loggedInUserData.getIn(['university', 'status'], false);

  return (
    <>
      <List component="nav" aria-labelledby="nested-list-subheader" className={classes.root}>
        <ListItem
          selected={usity === 0}
          classes={{ root: classes.root, selected: classes.selected }}
          button
          onClick={() => history.push(`/${type}/${id}/${name}/global-configuration/u-sity`)}
        >
          <ListItemText
            primary={!isUniversityAdmin || type === 'i' ? i18n.t('College') : i18n.t('University')}
          />
        </ListItem>
        <ListItem
          selected={program === 0}
          classes={{ root: classes.root, selected: classes.selected }}
          button
          onClick={() => history.push(`/${type}/${id}/${name}/global-configuration/program`)}
        >
          <ListItemText
            primary={
              getLabelName({ label: 'program' })
                ? getLabelName({ label: 'program', isToolTip: true, length: 15 })
                : 'Program'
            }
          />
        </ListItem>
        {type !== 'i' && (
          <ListItem
            selected={college === 0}
            classes={{ root: classes.root, selected: classes.selected }}
            button
            onClick={() => history.push(`/${type}/${id}/${name}/global-configuration/college`)}
          >
            <ListItemText
              primary={i18n.t('infra_management.add_location_modal.building_type.College')}
            />
          </ListItem>
        )}

        {/* <ListItem button onClick={() => handleClick('program', 1)} selected={selectedIndex === 1}>
        <ListItemText primary="Program" />
        {open['program'] ? <ExpandLess /> : <ExpandMore />}
      </ListItem>
      <Collapse in={open['program']} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          <ListItem button className={classes.nested}>
            <ListItemText primary="All Program" />
          </ListItem>
        </List>
      </Collapse> */}
        {/*
      <ListItem button onClick={() => handleClick('college', 2)} selected={selectedIndex === 2}>
        <ListItemText primary="College" />
        {open['college'] ? <ExpandLess /> : <ExpandMore />}
      </ListItem>
      <Collapse in={open['college']} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          <ListItem button className={classes.nested}>
            <ListItemText primary="All College" />
          </ListItem>
        </List>
      </Collapse> */}
      </List>
    </>
  );
}

GlobalConfigSidebar.propTypes = {
  type: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  loggedInUserData: PropTypes.instanceOf(Map),
};

export default GlobalConfigSidebar;
