import {
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TablePagination,
} from '@mui/material';
import Button from 'Widgets/FormElements/material/Button';
// import Menu from '@mui/material/Menu';
import React, { useEffect, useState } from 'react';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Accordion, AccordionDetails, AccordionSummary, Checkbox } from '@mui/material';
import { DebouncedSearch } from 'Widgets/FormElements/material/DebouncedSearch';
import { List, Map as IMap } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import {
  getProgramDetails,
  getCurriculumDetails,
  getProgramListForQAPC,
  setData,
  getMatchingForms,
  getConfigureTemplate,
} from '_reduxapi/q360/actions';
import {
  selectCategoryForm,
  selectProgramList,
  selectProgramDetails,
  selectProgramCount,
  selectedConfigureTemplate,
  selectIsProgramDetailsLoading,
} from '_reduxapi/q360/selectors';
import {
  useIsReduxEmpty,
  useSearchParams,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { PendingSx } from '../ConfigureTemplate/ConfigureTemplate';

export const useDispatchAndSelectorFunctionsQlc = () => {
  const dispatch = useDispatch();
  const fetchProgramList = (data) => {
    dispatch(getProgramListForQAPC(data));
  };
  const fetchProgramDetails = (data) => {
    dispatch(getProgramDetails(data));
  };

  const fetchCurriculumDetails = (data) => {
    dispatch(getCurriculumDetails(data));
  };
  const clearProgramDetails = () => {
    dispatch(setData(IMap({ qapcProgramDetails: IMap() })));
  };
  const programList = useSelector(selectProgramList);
  const categoryForms = useSelector(selectCategoryForm);
  const programDetails = useSelector(selectProgramDetails);
  const selectedProgramIds = categoryForms.get('selectedProgram', List());
  const matchingForm = categoryForms.get('matchingForm', List());
  const programCount = useSelector(selectProgramCount);
  const [configureTemplate] = useIsReduxEmpty(selectedConfigureTemplate, getConfigureTemplate);
  const [searchParams] = useSearchParams();
  const currentCategoryIndex = Number(searchParams.get('currentCategoryIndex') ?? 0);
  const setMessage = (message) => {
    dispatch(setData({ message }));
  };
  return {
    dispatch,
    fetchProgramList,
    fetchProgramDetails,
    programList,
    programDetails,
    programCount,
    configureTemplate,
    currentCategoryIndex,
    matchingForm,
    setMessage,
    selectedProgramIds,
    fetchCurriculumDetails,
    clearProgramDetails,
  };
};

// const programMenuOption = {
//   keepMounted: true,
//   PaperProps: {
//     style: {
//       maxHeight: 38 * 40.5,
//       width: '65ch',
//     },
//   },
//   anchorOrigin: {
//     vertical: 'bottom',
//     horizontal: 'center',
//   },
//   transformOrigin: {
//     vertical: 'bottom',
//     horizontal: 'center',
//   },
//   BackdropProps: { style: { backgroundColor: 'rgba(0, 0, 0, 0.5)' } },
// };

export const MenuWithOpenAndClose = ({ children, children1 }) => {
  const [anchorEl1, setAnchorEl1] = useState(null);
  const open1 = Boolean(anchorEl1);
  const handleClose1 = () => {
    setAnchorEl1(null);
  };
  const handleClick1 = (e) => {
    setAnchorEl1(e.currentTarget);
  };
  // if (!open1) return null;
  return (
    <>
      {children1(handleClick1)}
      {/* <Menu anchorEl={anchorEl1} open={open1} onClose={handleClose1} {...programMenuOption}>
        {children(handleClose1)}
      </Menu> */}
      <Dialog
        open={open1}
        // onClose={handleClose}
        // sx={{
        //   '.css-uonvmd-MuiModal-root-MuiDialog-root .MuiPaper-root': {
        //     height: 'calc(100% - 64px)',
        //   },
        // }}
        maxWidth={'sm'}
        fullWidth={true}
        // className={`${open ? 'invisible' : 'visible'}`}
      >
        {/* <DialogContent
          sx={{
            padding: '20px 0px',
            height: height,
          }}
        > */}
        {children(handleClose1)}
        {/* </DialogContent> */}
      </Dialog>
    </>
  );
};

// export const DialogWithOpenAndClose = ({ children }) => {
//   const [anchorEl1, setAnchorEl1] = useState(null);
//   const open1 = Boolean(anchorEl1);
//   const handleClose1 = () => {
//     setAnchorEl1(null);
//   };
//   return (
//     {open1&&(

//       <DialogModal
//       show={open}
//       onClose={handleClose}
//       maxWidth={'sm'}
//       fullWidth={true}
//       className={`${open ? 'invisible' : 'visible'}`}
//     >
//       {children}
//     </DialogModal>
//     )}
//   );
// };

export const addingNomsOfSingularAndPlural = (count, label) => {
  if (count > 1) {
    return label + 's';
  }
  return label;
};

function LoadSelectedChips({ programName, programId, selectedCourseCount, onDelete, levelLabel }) {
  return (
    <div>
      <Chip
        // key={programId}
        label={
          <div className="py-2">
            <div className="f-12 fw-400 text-dGrey text-capitalize">{programName}</div>
            <div className="f-10 text-mGrey fw-400">
              {selectedCourseCount +
                ' ' +
                addingNomsOfSingularAndPlural(selectedCourseCount, levelLabel)}
            </div>
          </div>
        }
        onDelete={onDelete(programId)}
        // deleteIcon={<CloseIcon className="f-18" />}
        sx={{
          '&.MuiButtonBase-root.MuiChip-root': {
            borderRadius: '6px',
            color: '#000000',
            height: '45px',
          },
        }}
        className="select-color mt-2 mr-3"
      />
    </div>
  );
}
export default LoadSelectedChips;
export const AccordionLayoutProgramCreation = ({
  children,
  handleProgramCheckBox,
  programSelectedCount,
  isProgramChecked,
  handleClose,
  accordionIsExpanded,
  onSave,
  clearAll,
  height,
  isSaveButtonDisabled,
}) => {
  const [programId, setProgramId] = useState('');
  const [query, setQuery] = useState('');
  const { programList, fetchProgramList, programCount } = useDispatchAndSelectorFunctionsQlc();
  const [pagination, setPagination] = useState(
    IMap({
      pageNo: 0,
      limit: 10,
    })
  );

  const currentPaginationCount = (pagination.get('pageNo', 0) + 1) * pagination.get('count', 10);
  const cacheProgramCount = programCount.getIn([query, currentPaginationCount], 1);

  useEffect(() => {
    manipulatingProgramData();
  }, [pagination, query]);

  const handleChange = (e, pageNo) => {
    setPagination((prev) => prev.set('pageNo', pageNo));
  };
  const handleChangeRowsPerPage = (event) => {
    setPagination((prev) => prev.set('limit', parseInt(event.target.value)).set('pageNo', 0));
  };
  function manipulatingProgramData() {
    const checkCountKeyIsExceeded = programList.hasIn([query, currentPaginationCount]);
    if (checkCountKeyIsExceeded) return;
    fetchProgramList({
      params: {
        ...pagination.toJS(),
        pageNo: pagination.get('pageNo', 0) + 1,
        searchKey: query,
      },
      currentPaginationCount: currentPaginationCount,
    });
  }
  const handleAccordion = (_id) => () => {
    let updatedId;
    setProgramId((prev) => {
      updatedId = prev === _id ? '' : _id;
      return updatedId;
    });
    if (updatedId !== '') {
      accordionIsExpanded(updatedId);
    }
  };
  const searchCallBack = (query) => {
    setPagination((prev) => prev.set('pageNo', 0));
    setQuery(query);
  };
  const isProgramDetailsLoading = useSelector(selectIsProgramDetailsLoading);
  return (
    <>
      <DialogTitle sx={{ padding: '16px 16px 0px 16px' }}>
        <DebouncedSearch placeholder={'Search a Name, ID...'} doSomething={searchCallBack} />
      </DialogTitle>
      <DialogContent sx={{ padding: '0px 8px', height: height + 'px' }}>
        {programList.getIn([query, currentPaginationCount], List()).size === 0 && (
          <div
            className="f-12 py-2 d-flex align-items-center justify-content-center"
            style={{ height: '50vh' }}
          >
            No Data Found...
          </div>
        )}
        {programList.getIn([query, currentPaginationCount], List()).size !== 0 && (
          <div>
            <div className="d-flex align-items-center my-2">
              <div className="ml-3 f-12 fw-400 text-lGrey">Select Programs</div>
              {/* <div className="ml-auto">
        <div className="d-flex align-items-center">
          <div>
            <Checkbox onClick={handleAllProgramCheckBox} size="small" checked={pgmDetailsState.get('constructedProgramCount',0)===pgmDetailsState.get('selectedProgramCount',0)} />
          </div>
          <div className="f-12 fw-500 text-dGrey">All Program</div>
        </div>
      </div> */}
            </div>
            <div className="">
              {programList.getIn([query, currentPaginationCount], List()).map((program) => (
                <Accordion
                  expanded={program.get('_id', '') === programId}
                  key={program.get('_id', '')}
                  className="bg-white"
                  onChange={handleAccordion(program.get('_id', ''), program.get('name', ''))}
                  sx={{
                    borderRadius: '8px',
                    '& .MuiAccordionDetails-root': {
                      padding: '8px',
                    },
                    '& .css-ohg2zj-MuiButtonBase-root-MuiAccordionSummary-root.MuiAccordionSummary-root': {
                      background:
                        program.get('_id', '') === programId ? 'rgba(243, 244, 246, 1)' : '',
                    },
                  }}
                  elevation={0}
                  disableGutters
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    className="px-0 my-0"
                    sx={{
                      '&.MuiAccordionSummary-root': {
                        minHeight: '42px',
                        backgroundColor: program.get('_id', '') === programId ? '#f3f4f6' : '',
                      },
                      '.MuiAccordionSummary-content': {
                        margin: '0px',
                      },
                    }}
                  >
                    <div className="d-flex align-items-center w-100">
                      <div className="d-flex align-items-center">
                        <div onClick={(e) => e.stopPropagation()}>
                          <Checkbox
                            size="small"
                            onClick={handleProgramCheckBox(
                              program.get('_id', ''),
                              program.get('name', '')
                            )}
                            checked={isProgramChecked(program.get('_id', ''))}
                          />
                        </div>
                        <div className="fw-400 f-14 text-dGrey text-capitalize">
                          {program.get('name', '')}
                        </div>
                        {isProgramDetailsLoading && program.get('_id', '') === programId && (
                          <CircularProgress
                            sx={{
                              width: '20px !important',
                              height: '20px !important',
                              marginLeft: '10px',
                            }}
                          />
                        )}
                      </div>
                      <div className="ml-auto f-12 color-lt-gray">
                        {' '}
                        {programSelectedCount(program.get('_id', ''))} selected{' '}
                      </div>
                    </div>
                  </AccordionSummary>
                  {program.get('_id', '') === programId && (
                    <AccordionDetails>{children(programId)}</AccordionDetails>
                  )}
                </Accordion>
              ))}
            </div>
          </div>
        )}
      </DialogContent>
      <DialogActions sx={{ padding: '8px 16px', justifyContent: 'flex-start' }}>
        <div className="d-flex align-items-center justify-content-between w-100">
          <TablePagination
            component={'div'}
            className="mt-0 mb-0"
            sx={{
              '& .MuiTablePagination-selectLabel': {
                fontSize: '12px',
              },
              '& .MuiInputBase-root-MuiTablePagination-select': {
                fontSize: '12px',
              },
              '& .MuiTablePagination-displayedRows': {
                fontSize: '12px',
              },
              '& .MuiIconButton-root': {
                fontSize: '10px',
              },
              '& .MuiTablePagination-toolbar': {
                paddingLeft: '0px !important',
              },
            }}
            count={cacheProgramCount}
            page={pagination.get('pageNo', 0)}
            onPageChange={handleChange}
            rowsPerPage={pagination.get('limit', 10)}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
          <div className="d-flex gap-15 ml-4">
            <Button
              clicked={() => {
                clearAll();
                handleClose();
              }}
              variant="outlined"
              className=" px-4"
              size={'small'}
              color={'gray'}
            >
              Cancel
            </Button>
            <Button
              clicked={() => {
                onSave();
                handleClose();
              }}
              variant="contained"
              disabled={isSaveButtonDisabled}
              className="px-4"
              color="primary"
              size={'small'}
            >
              Save
            </Button>
          </div>
        </div>
      </DialogActions>
    </>
  );
};

export const tabBorderNone = {
  borderBottom: 'none !important',
  '& .MuiTab-root': {
    padding: '0px',
    margin: '0px 40px 0px 0px',
  },
};

export const tabBorderNoneYear = {
  borderBottom: 'none !important',
  '& .MuiTab-root': {
    padding: '0px',
    margin: '0px 30px 0px 0px',
    minWidth: '0px',
  },
};

export const textTransform = {
  textTransform: 'none',
};

export const tabPadding = {
  '&.MuiTabPanel-root': {
    padding: '0px',
  },
};

export const formatYear = (year) => {
  const number = year.split('r')[1];
  return 'Year ' + number;
};

export const useMatchingForms = (categoryFormId) => {
  const dispatch = useDispatch();
  const [state, setState] = useState(IMap());
  const callBack = (matchingForm) => {
    if (!matchingForm.size) return IMap();
    let programWiseMap = IMap();
    for (const doc of matchingForm) {
      programWiseMap = programWiseMap.update(doc.get('programId', ''), List(), (courseList) =>
        courseList.push(doc)
      );
    }
    setState(programWiseMap);
  };

  useEffect(() => {
    if (categoryFormId) {
      dispatch(
        getMatchingForms(
          `/qapcCategoryForm_v2/singleFormOccurrence?categoryFormId=${categoryFormId}`,
          callBack,
          categoryFormId
        )
      );
    }
  }, []);
  return [state, setState];
};

export function programShortCode(programName) {
  const splittedPrograms = programName.split(' ');
  if (splittedPrograms.length > 1) {
    return splittedPrograms[0][0] + splittedPrograms[1][0];
  }
  return splittedPrograms[0][0] + 'P';
}

export const typeOfForms = [
  { label: 'Complete Form', value: 'complete' },
  { label: 'Section Wise Form', value: 'section' },
];

export const describeSx = {
  '& .MuiInputBase-root': {
    color: '#000000 !important',
    padding: '0px',
  },
  '& .MuiInputBase-input': {
    border: '1px solid #D1D5DB !important',
    borderRadius: '4px',
    padding: '8px 7px',
  },
};
export const form_configure_tab = { ...PendingSx, ...tabBorderNone };

export const checkCurrentCompletedStep = (step) => {
  // Purpose: Check the Current User Completed Step So far.
  // Conditions:
  // If activeStep  1  means 1 should be included  in step
  // If activeStep  2  means 1,2 should be included  in step
  // If activeStep  3  means 1,2,3 should be included  in step
  // If activeStep  4  means 1,2,3,4 should be included  in step, so user is completed all steps successFully
  //*************  Scenarios:  ******************** */
  //1)
  //step=[1,2,4] activeStep=2 because user only completed 2 step contigeosuly , even user completed 4 th step he doesn't completed 3 rd step,so activeStep is 2
  //2)
  //step=[1,4] activeStep=1
  //3)
  //step=[2,3,4] activeStep=0 because user doesn't complete 1st step
  let activeStep = 0;
  if (step.size === 0) return activeStep; //so far no step is completed . so we returned as 0
  const stepContiner = [null, null, null, null];
  for (let i = 0; i < step.size; i++) {
    const iteratedValue = step.get(i) - 1; //convet step as index
    stepContiner[iteratedValue] = iteratedValue;
  }
  for (let i = 0; i < 4; i++) {
    if (stepContiner[i] === null) break;
    activeStep = i + 1; //convert index as step
  }
  return activeStep;
};
