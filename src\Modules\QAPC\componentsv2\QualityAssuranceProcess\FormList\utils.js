import { useEffect, useState } from 'react';
import { Map, List, fromJS } from 'immutable';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { selectCalenderList } from '_reduxapi/global_configuration/v1/selectors';
import { getQaPcCalender } from '_reduxapi/global_configuration/v1/actions';
import { selectAcademicYear, selectedDiscussionUnreadDetails } from '_reduxapi/q360/selectors';
import { getAcademicYears, getDiscussionCount, setData } from '_reduxapi/q360/actions';
import { useHistory } from 'react-router-dom';
import { useSearchParams } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { selectUserId } from '_reduxapi/Common/Selectors';

export const useCustomHooksForTable = (resData) => {
  const [statusBar, setStatusBar] = useState(
    Map({
      showStatusBar: false,
      showMissed: false,
      showTodo: false,
    })
  );
  const [columnData, setColumnData] = useState(
    fromJS([
      { name: 'S.NO', Disable: true, showCheck: true },
      { name: 'Course ID / Name', Disable: true, showCheck: true },
      { name: 'Display', Disable: false, showCheck: true },
      { name: 'Incorporate', Disable: false, showCheck: true },
      { name: 'Submission', Disable: true, showCheck: true },
      { name: 'Approvers', Disable: true, showCheck: true, className: 'pl-3' },
      { name: 'Attachment', Disable: false, showCheck: true },
      { name: 'Status', Disable: true, showCheck: true },
      { name: ' ', Disable: true, showCheck: true },
      { name: '  ', Disable: true, showCheck: true },
    ])
  );
  const [columnFilter, setColumnFilter] = useState(Map());

  const handleClick = (type) => {
    setStatusBar(statusBar.set(type, !statusBar.get(type, false)));
  };

  const handleCheckbox = (e, type) => {
    const checked = e.target.checked;
    setColumnFilter(columnFilter.set(type, checked));
    const updatedColumnData = columnData.map((column) => {
      if (column.get('name') === type) {
        return column.set('showCheck', checked);
      }
      return column;
    });
    setColumnData(updatedColumnData);
  };
  return { statusBar, handleClick, columnFilter, handleCheckbox, columnData };
};

export const useCalenderQaPc = () => {
  const calenderList = useSelector(selectCalenderList);
  const dispatch = useDispatch();
  useEffect(() => {
    if (calenderList.size) return;
    dispatch(getQaPcCalender());
  }, []);
  return [calenderList, dispatch];
};

export const useCalenderQaPc2 = () => {
  const calenderList = useSelector(selectAcademicYear);
  const dispatch = useDispatch();
  useEffect(() => {
    if (calenderList.size) return;
    dispatch(getAcademicYears());
  }, []);
  return [calenderList, dispatch];
};

export const courseTypeHeading = {
  shared_with_others: 'Shared With Course',
  shared_from: 'Shared From Course',
  standard: 'All Standard Course',
  selective: 'All Selective Course',
};

export const useCustomHooksForIncorporateSection = () => {
  const [show, setShow] = useState(false);
  const [incorporateSectionsTo, setIncorporateSectionsTo] = useState(Map());
  const [anchorEl, setAnchorEl] = useState(
    Map({
      category: null,
      course: null,
    })
  );
  const [selectedCalendar, setSelectedCalendar] = useState('');
  const [currentSection, setCurrentSection] = useState('');
  const [incorporateSectionList, setIncorporateSectionList] = useState(List());

  const onChangeCalendar = (e) => {
    const value = e.target.value;
    setSelectedCalendar(value);
  };

  const handleClickMenu = (event, type) => {
    setAnchorEl(anchorEl.set(type, event.currentTarget));
  };
  const handleCloseMenu = (type) => {
    setAnchorEl(anchorEl.set(type, null));
  };

  const handleDragEnd = (result) => {
    const { destination, draggableId } = result;
    if (destination && destination.droppableId === 'incorporateSectionsTo') {
      setShow(true);
      setCurrentSection(draggableId);
    }
  };

  const handleClose = () => {
    setShow(false);
  };

  const handleDelete = (index) => {
    setIncorporateSectionList(incorporateSectionList.delete(index));
  };

  return {
    show,
    setShow,
    handleDragEnd,
    handleClose,
    incorporateSectionsTo,
    setIncorporateSectionsTo,
    handleClickMenu,
    handleCloseMenu,
    anchorEl,
    setAnchorEl,
    selectedCalendar,
    onChangeCalendar,
    currentSection,
    incorporateSectionList,
    setIncorporateSectionList,
    handleDelete,
    setSelectedCalendar,
  };
};

export const constructFormData = (formData) => {
  const dataWithIndexes = formData.map((program) =>
    program.update('formOccurrence', (formOccurrence) =>
      formOccurrence.map((curriculum, programIndex) =>
        curriculum
          .update('curriculum', (curriculums) =>
            curriculums.map((curriculumItem, curriculumIndex) =>
              curriculumItem
                .update('years', (yearsAr) =>
                  yearsAr.map((year, yearIndex) =>
                    year
                      .update('courses', (coursesAr) =>
                        coursesAr.map((course, courseIndex) => course.set('index', courseIndex))
                      )
                      .set('index', yearIndex)
                  )
                )
                .set('index', curriculumIndex)
            )
          )
          .set('index', programIndex)
      )
    )
  );

  return dataWithIndexes;
};

export const constructData = (programDetails) => {
  return programDetails.map((program) => {
    let programBasedCourseCount = 0;
    return program
      .update('curriculum', Map(), (curriculum) =>
        curriculum.map((curriculum) =>
          curriculum
            .update('years', Map(), (years) =>
              years.map((year) => {
                programBasedCourseCount += year.get('courseIds', List()).size;
                return year
                  .set('constructedCourseCount', year.get('courseIds', List()).size)
                  .update('courseIds', List(), (courseIds) => {
                    let courseTypes = new Map();
                    courseIds.forEach((course) => {
                      if (course.get('shared_with_others', false)) {
                        courseTypes = courseTypes.update('shared_from', List(), (data) =>
                          data.push(course)
                        );
                        return courseTypes;
                      }
                      if (course.get('shared_from', false)) {
                        courseTypes = courseTypes.update('shared_with_others', List(), (data) =>
                          data.push(course)
                        );
                        return courseTypes;
                      }
                      if (course.get('course_type', '') === 'standard')
                        courseTypes = courseTypes.update('standard', List(), (data) =>
                          data.push(course)
                        );
                      else
                        courseTypes = courseTypes.update('selective', List(), (data) =>
                          data.push(course)
                        );
                    });
                    return courseTypes;
                  })
                  .set('index', year.get('index'));
              })
            )
            .set('index', curriculum.get('index'))
        )
      )

      .set('constructedProgramCount', programBasedCourseCount)
      .set('index', program.get('index'));
  });
};

export const getCategoryName = (categoryDetails, categoryId) => {
  const findCategoryName = categoryDetails.find((cat) => cat.get('_id') === categoryId);
  return findCategoryName?.get('categoryName', '');
};

export const getCategoryFormName = (categoryDetails, formId) => {
  for (const category of categoryDetails) {
    for (const formData of category.get('formData', List())) {
      if (formData.get('_id') === formId) {
        return formData.get('name', '');
      }
    }
  }
};

export const useCalendarListHook = () => {
  const [calenderList] = useCalenderQaPc();
  const [selectedCalendar, setSelectedCalendar] = useState('');
  const getCalendarList = () => {
    return calenderList.map((calender) =>
      calender.set('name', calender.get('calendar_name', '')).set('value', calender.get('_id', ''))
    );
  };
  const calendarDetails = getCalendarList();

  const onChangeCalendar = (e) => {
    const value = e.target.value;
    setSelectedCalendar(value);
  };

  useEffect(() => {
    if (calenderList.size) setSelectedCalendar(calenderList.getIn([0, '_id'], ''));
  }, [calenderList]);
  return { calendarDetails, onChangeCalendar, selectedCalendar };
};

export const useCustomCalendarListHook = () => {
  const history = useHistory();
  const [searchParams] = useSearchParams();
  const calendar = searchParams.get('calendar');
  const calenderList = useSelector(selectAcademicYear);
  const [selectedCalendar, setSelectedCalendar] = useState('');
  const getCalendarList = () => {
    return calenderList.map((calender) =>
      calender.set('name', calender.get('calendar_name', '')).set('value', calender.get('_id', ''))
    );
  };
  const calendarDetails = getCalendarList();

  const onChangeCalendar = (e) => {
    const value = e;
    setSelectedCalendar(value);
    history.push(`?calendar=${value}`);
  };

  useEffect(() => {
    if (calendar) setSelectedCalendar(calendar);
    else {
      setSelectedCalendar(calenderList.getIn([0, '_id'], ''));
      history.push(`?calendar=${calenderList.getIn([0, '_id'], '')}`);
    }
  }, [calenderList, calendar]);
  return { calendarDetails, onChangeCalendar, selectedCalendar, setSelectedCalendar };
};

export const useCustomFormModalHook = () => {
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();
  const openOrClose = () => {
    setOpen((prev) => !prev);
    if (open) {
      dispatch(setData(Map({ singleForm: List(), formSettingData: Map() })));
    }
  };
  return { open, openOrClose };
};

export const useCustomHooksForConfirmation = () => {
  const [show, setShow] = useState(false);
  const [formDetails, setFormDetails] = useState(Map());

  const handleShow = (type, form) => {
    setShow(true);
    setFormDetails(formDetails.set('form', form).set('type', type));
  };

  const resetDialog = () => {
    setShow(false);
    setFormDetails(Map());
  };

  return {
    show,
    handleShow,
    formDetails,
    resetDialog,
  };
};

export const SearchIconSx = { color: '#9CA3AF', fontSize: '16px' };
export const SearchInputSx = (reply) => {
  return {
    width: '100%',
    height: '45px',
    fontSize: '12px',
    background: '#F3F4F6',
    ...(reply && { borderTopLeftRadius: 0, borderTopRightRadius: 0 }),
    '& .MuiInputBase-input::placeholder': {
      color: '#9CA3AF',
      opacity: 1,
    },
    '& .MuiInputBase-input': {
      color: '#9CA3AF',
      fontSize: 12,
    },
    '& .MuiSvgIcon-root': {
      fontSize: '16px',
      color: '#6B7280',
    },
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
  };
};

const colorPalette = [
  '#FFAB00',
  '#FF5252',
  '#FF4081',
  '#E040FB',
  '#7C4DFF',
  '#536DFE',
  '#448AFF',
  '#40C4FF',
  '#18FFFF',
  '#64FFDA',
  '#69F0AE',
  '#B2FF59',
  '#EEFF41',
  '#FFD740',
  '#FF6E40',
];

export const getColorFromName = (name) => {
  const charCodeSum = name.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
  return colorPalette[charCodeSum % colorPalette.length];
};

export function formatDate(createdAt) {
  const date = moment(createdAt);
  if (date.isSame(moment(), 'day')) {
    return `Today, ${date.format('MMMM D')}`;
  } else {
    return date.format('dddd, MMMM D');
  }
}

export const useGetDiscussionCount = (selectedCalendar, categoryTab, selectedRole) => {
  const channelId = `q360_${selectedCalendar}_${categoryTab}`;
  const dispatch = useDispatch();
  const discussionUnreadDetails = useSelector(selectedDiscussionUnreadDetails);
  const userId = useSelector(selectUserId);

  useEffect(() => {
    const params = {
      type: 'all',
      institutionCalendarId: selectedCalendar,
    };
    if (selectedCalendar && categoryTab) {
      dispatch(getDiscussionCount(params));
    }
  }, [dispatch, selectedCalendar, categoryTab, selectedRole]);

  const calculateUnreadCount = (data, filterChannelId = null) =>
    (data || List()).reduce((total, item) => {
      if (filterChannelId && item.get('channelId') !== filterChannelId) return total;

      const userIds = item.get('userIds', List());
      return (
        total +
        userIds
          .filter((user) => user.get('userId') === userId)
          .reduce((userTotal, user) => userTotal + user.get('unReadCount', 0), 0)
      );
    }, 0);

  const overallUnreadCount = calculateUnreadCount(discussionUnreadDetails);
  const specificChannelUnreadCount = calculateUnreadCount(discussionUnreadDetails, channelId);

  const totalUnreadCount =
    categoryTab === 'overview' ? overallUnreadCount : specificChannelUnreadCount;

  return { totalUnreadCount };
};
