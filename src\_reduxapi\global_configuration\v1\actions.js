import { constructDataCourseWise, constructDataProgramWise, createAction } from '../../util';
import axios from '../../../axios';
import { Map, fromJS } from 'immutable';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';

const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');

export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';

const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');

export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const SET_BREADCRUMB_SUCCESS = 'SET_BREADCRUMB_SUCCESS';
const setBreadCrumbSuccess = createAction(SET_BREADCRUMB_SUCCESS, 'breadcrumbs');
export const setBreadCrumb = (arr) => {
  return (dispatch) => {
    dispatch(setBreadCrumbSuccess(arr));
  };
};

export const GET_GLOBAL_SESSION_SETTING_REQUEST = 'GET_GLOBAL_SESSION_SETTING_REQUEST';
export const GET_GLOBAL_SESSION_SETTING_SUCCESS = 'GET_GLOBAL_SESSION_SETTING_SUCCESS';
export const GET_GLOBAL_SESSION_SETTING_FAILURE = 'GET_GLOBAL_SESSION_SETTING_FAILURE';

const getGlobalSessionSettingRequest = createAction(GET_GLOBAL_SESSION_SETTING_REQUEST);
const getGlobalSessionSettingSuccess = createAction(GET_GLOBAL_SESSION_SETTING_SUCCESS, 'data');
const getGlobalSessionSettingFailure = createAction(GET_GLOBAL_SESSION_SETTING_FAILURE, 'error');

export function getGlobalSessionSetting() {
  return function (dispatch) {
    dispatch(getGlobalSessionSettingRequest());
    axios
      .get(`/global-session-settings/get-institution-session-setting`)
      .then((res) => {
        dispatch(getGlobalSessionSettingSuccess(res.data.data));
      })
      .catch((error) => dispatch(getGlobalSessionSettingFailure(error)));
  };
}

export const POST_GLOBAL_SESSION_SETTING_REQUEST = 'POST_GLOBAL_SESSION_SETTING_REQUEST';
export const POST_GLOBAL_SESSION_SETTING_SUCCESS = 'POST_GLOBAL_SESSION_SETTING_SUCCESS';
export const POST_GLOBAL_SESSION_SETTING_FAILURE = 'POST_GLOBAL_SESSION_SETTING_FAILURE';

const postGlobalSessionSettingRequest = createAction(POST_GLOBAL_SESSION_SETTING_REQUEST);
const postGlobalSessionSettingSuccess = createAction(POST_GLOBAL_SESSION_SETTING_SUCCESS, 'data');
const postGlobalSessionSettingFailure = createAction(POST_GLOBAL_SESSION_SETTING_FAILURE, 'error');

export function postGlobalSessionSetting(requestBody) {
  return function (dispatch) {
    dispatch(postGlobalSessionSettingRequest());
    axios
      .post(`/global-session-settings/create-institution-session-setting`, requestBody)
      .then((res) => {
        dispatch(postGlobalSessionSettingSuccess(res.data.data));
        dispatch(getGlobalSessionSetting());
      })
      .catch((error) => dispatch(postGlobalSessionSettingFailure(error)));
  };
}

export const ADD_TARDIS_REQUEST = 'ADD_TARDIS_REQUEST';
export const ADD_TARDIS_SUCCESS = 'ADD_TARDIS_SUCCESS';
export const ADD_TARDIS_FAILURE = 'ADD_TARDIS_FAILURE';

const addTardisRequest = createAction(ADD_TARDIS_REQUEST, 'requestBody');
const addTardisSuccess = createAction(ADD_TARDIS_SUCCESS, 'data');
const addTardisFailure = createAction(ADD_TARDIS_FAILURE, 'error');

export function addTardis(body, cb) {
  return function (dispatch) {
    dispatch(addTardisRequest());
    axios
      .post(`/global-session-settings/update-tardis`, body)
      .then((res) => {
        dispatch(addTardisSuccess(res.data.message));
        dispatch(getTardis());
        cb && cb();
      })
      .catch((error) => dispatch(addTardisFailure(error)));
  };
}

export const GET_TARDIS_REQUEST = 'GET_TARDIS_REQUEST';
export const GET_TARDIS_SUCCESS = 'GET_TARDIS_SUCCESS';
export const GET_TARDIS_FAILURE = 'GET_TARDIS_FAILURE';

const getTardisRequest = createAction(GET_TARDIS_REQUEST, 'requestBody');
const getTardisSuccess = createAction(GET_TARDIS_SUCCESS, 'data');
const getTardisFailure = createAction(GET_TARDIS_FAILURE, 'error');

export function getTardis() {
  return function (dispatch) {
    dispatch(getTardisRequest());
    axios
      .get(`global-session-settings/get-tardis`)
      .then((res) => {
        dispatch(getTardisSuccess(res.data.data));
      })
      .catch((error) => dispatch(getTardisFailure(error)));
  };
}

export const DELETE_TARDIS_REQUEST = 'DELETE_TARDIS_REQUEST';
export const DELETE_TARDIS_SUCCESS = 'DELETE_TARDIS_SUCCESS';
export const DELETE_TARDIS_FAILURE = 'DELETE_TARDIS_FAILURE';

const deleteTardisRequest = createAction(DELETE_TARDIS_REQUEST);
const deleteTardisSuccess = createAction(DELETE_TARDIS_SUCCESS, 'data');
const deleteTardisFailure = createAction(DELETE_TARDIS_FAILURE, 'error');

export function deleteTardis(_id) {
  return function (dispatch) {
    dispatch(deleteTardisRequest());
    axios
      .delete(`/global-session-settings/delete-tardis`, { data: { _id } })
      .then((res) => {
        dispatch(deleteTardisSuccess(res.data.data));
        dispatch(getTardis());
      })
      .catch((error) => dispatch(deleteTardisFailure(error)));
  };
}

export const GET_LATE_CONFIG_REQUEST = 'GET_LATE_CONFIG_REQUEST';
export const GET_LATE_CONFIG_SUCCESS = 'GET_LATE_CONFIG_SUCCESS';
export const GET_LATE_CONFIG_FAILURE = 'GET_LATE_CONFIG_FAILURE';

const getLateConfigRequest = createAction(GET_LATE_CONFIG_REQUEST, 'requestBody');
const getLateConfigSuccess = createAction(GET_LATE_CONFIG_SUCCESS, 'data');
const getLateConfigFailure = createAction(GET_LATE_CONFIG_FAILURE, 'error');

export function getLateConfig() {
  return function (dispatch) {
    dispatch(getLateConfigRequest());
    axios
      .get('/global-session-settings/lateConfig')
      .then((res) => {
        dispatch(getLateConfigSuccess(res.data.data));
      })
      .catch((error) => dispatch(getLateConfigFailure(error)));
  };
}

export const SET_LATE_CONFIG_REQUEST = 'SET_LATE_CONFIG_REQUEST';
export const SET_LATE_CONFIG_SUCCESS = 'SET_LATE_CONFIG_SUCCESS';
export const SET_LATE_CONFIG_FAILURE = 'SET_LATE_CONFIG_FAILURE';

const setLateConfigRequest = createAction(SET_LATE_CONFIG_REQUEST, 'requestBody');
const setLateConfigSuccess = createAction(SET_LATE_CONFIG_SUCCESS, 'data');
const setLateConfigFailure = createAction(SET_LATE_CONFIG_FAILURE, 'error');

export function setLateConfig(payload) {
  return function (dispatch) {
    dispatch(setLateConfigRequest());
    axios
      .put('/global-session-settings/lateConfig', payload)
      .then((res) => {
        dispatch(setLateConfigSuccess(res.data.data));
        dispatch(getLateConfig());
      })
      .catch((error) => dispatch(setLateConfigFailure(error)));
  };
}

export const GET_SESSION_STATUS_DETAILS_REQUEST = 'GET_SESSION_STATUS_DETAILS_REQUEST';
export const GET_SESSION_STATUS_DETAILS_SUCCESS = 'GET_SESSION_STATUS_DETAILS_SUCCESS';
export const GET_SESSION_STATUS_DETAILS_FAILURE = 'GET_SESSION_STATUS_DETAILS_FAILURE';

const getSessionStatusDetailsRequest = createAction(
  GET_SESSION_STATUS_DETAILS_REQUEST,
  'requestBody'
);
const getSessionStatusDetailsSuccess = createAction(GET_SESSION_STATUS_DETAILS_SUCCESS, 'data');
const getSessionStatusDetailsFailure = createAction(GET_SESSION_STATUS_DETAILS_FAILURE, 'error');

export function getSessionStatusDetails() {
  return function (dispatch) {
    dispatch(getSessionStatusDetailsRequest());
    axios
      .get('/global-session-settings/getGlobalSessionStatusDetails')
      .then((res) => {
        dispatch(getSessionStatusDetailsSuccess(res.data.data));
      })
      .catch((error) => dispatch(getSessionStatusDetailsFailure(error)));
  };
}

export const SET_SESSION_STATUS_DETAILS_REQUEST = 'SET_SESSION_STATUS_DETAILS_REQUEST';
export const SET_SESSION_STATUS_DETAILS_SUCCESS = 'SET_SESSION_STATUS_DETAILS_SUCCESS';
export const SET_SESSION_STATUS_DETAILS_FAILURE = 'SET_SESSION_STATUS_DETAILS_FAILURE';

const setSessionStatusDetailsRequest = createAction(
  SET_SESSION_STATUS_DETAILS_REQUEST,
  'requestBody'
);
const setSessionStatusDetailsSuccess = createAction(SET_SESSION_STATUS_DETAILS_SUCCESS, 'data');
const setSessionStatusDetailsFailure = createAction(SET_SESSION_STATUS_DETAILS_FAILURE, 'error');

export function setSessionStatusDetails(payload) {
  return function (dispatch) {
    dispatch(setSessionStatusDetailsRequest());
    axios
      .put('/global-session-settings/updateGlobalSessionStatusDetails', {
        sessionStatusDetails: payload,
      })
      .then((res) => {
        dispatch(setSessionStatusDetailsSuccess(res.data.data));
        dispatch(getSessionStatusDetails());
      })
      .catch((error) => dispatch(setSessionStatusDetailsFailure(error)));
  };
}
// ------------------------------------LeaderBoard setting

export const GET_PARAMETER_REQUEST = 'GET_PARAMETER_REQUEST';
export const GET_PARAMETER_SUCCESS = 'GET_PARAMETER_SUCCESS';
export const GET_PARAMETER_FAILURE = 'GET_PARAMETER_FAILURE';

const getParameterRequest = createAction(GET_PARAMETER_REQUEST, 'requestBody');
const getParameterSuccess = createAction(GET_PARAMETER_SUCCESS, 'data');
const getParameterFailure = createAction(GET_PARAMETER_FAILURE, 'error');

export function getParameter(type = 'leader') {
  return function (dispatch) {
    dispatch(getParameterRequest());
    axios
      .get('/leader_board/getParameters?boardType=' + type)
      .then((res) => {
        dispatch(getParameterSuccess(res.data.data));
      })
      .catch((error) => dispatch(getParameterFailure(error)));
  };
}

export const ADD_PARAMETER_REQUEST = 'ADD_PARAMETER_REQUEST';
export const ADD_PARAMETER_SUCCESS = 'ADD_PARAMETER_SUCCESS';
export const ADD_PARAMETER_FAILURE = 'ADD_PARAMETER_FAILURE';

const addParameterRequest = createAction(ADD_PARAMETER_REQUEST, 'requestBody');
const addParameterSuccess = createAction(ADD_PARAMETER_SUCCESS, 'data', 'method');
const addParameterFailure = createAction(ADD_PARAMETER_FAILURE, 'error');

export function addParameter(method, payload, cb) {
  return function (dispatch) {
    dispatch(addParameterRequest());
    axios[method](
      `/leader_board/${method === 'post' ? 'createParameter' : 'editParameters'}`,
      payload
    )
      .then((res) => {
        dispatch(addParameterSuccess(res.data.data, method));
        dispatch(getParameter());
        cb && cb();
      })
      .catch((error) => dispatch(addParameterFailure(error)));
  };
}

export const DELETE_PARAMETER_REQUEST = 'DELETE_PARAMETER_REQUEST';
export const DELETE_PARAMETER_SUCCESS = 'DELETE_PARAMETER_SUCCESS';
export const DELETE_PARAMETER_FAILURE = 'DELETE_PARAMETER_FAILURE';

const deleteParameterRequest = createAction(DELETE_PARAMETER_REQUEST);
const deleteParameterSuccess = createAction(DELETE_PARAMETER_SUCCESS, 'data');
const deleteParameterFailure = createAction(DELETE_PARAMETER_FAILURE, 'error');

export function deleteParameter(parameterId, cb) {
  return function (dispatch) {
    dispatch(deleteParameterRequest());
    axios
      .put(`/leader_board/deleteParameters?parameterId=${parameterId}`)
      .then((res) => {
        dispatch(deleteParameterSuccess(res.data.data));
        dispatch(getParameter());
        cb && cb();
      })
      .catch((error) => dispatch(deleteParameterFailure(error)));
  };
}

export const SAVE_LEADER_BOARD_REQUEST = 'SAVE_LEADER_BOARD_REQUEST';
export const SAVE_LEADER_BOARD_SUCCESS = 'SAVE_LEADER_BOARD_SUCCESS';
export const SAVE_LEADER_BOARD_FAILURE = 'SAVE_LEADER_BOARD_FAILURE';

const saveLeaderBoardRequest = createAction(SAVE_LEADER_BOARD_REQUEST);
const saveLeaderBoardSuccess = createAction(SAVE_LEADER_BOARD_SUCCESS, 'data');
const saveLeaderBoardFailure = createAction(SAVE_LEADER_BOARD_FAILURE, 'error');

export function saveLeaderBoard(payload, type = 'leader') {
  return function (dispatch) {
    dispatch(saveLeaderBoardRequest());
    axios
      .post('/leader_board/saveLeaderBoard', payload)
      .then((res) => {
        dispatch(saveLeaderBoardSuccess(res.data.data));
        dispatch(getParameter(type));
      })
      .catch((error) => dispatch(saveLeaderBoardFailure(error)));
  };
}

export const GET_PROGRAM_LIST_REQUEST = 'GET_PROGRAM_LIST_REQUEST';
export const GET_PROGRAM_LIST_SUCCESS = 'GET_PROGRAM_LIST_SUCCESS';
export const GET_PROGRAM_LIST_FAILURE = 'GET_PROGRAM_LIST_FAILURE';

const getProgramListRequest = createAction(GET_PROGRAM_LIST_REQUEST);
const getProgramListSuccess = createAction(GET_PROGRAM_LIST_SUCCESS, 'data');
const getProgramListFailure = createAction(GET_PROGRAM_LIST_FAILURE, 'error');

export function getProgramList() {
  return function (dispatch) {
    dispatch(getProgramListRequest(true));
    axios
      .get('session-report/userProgramList')
      .then((res) => dispatch(getProgramListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramListFailure(error)));
  };
}

export const GET_PERFORMANCE_TABLE_FILTER_REQUEST = 'GET_PERFORMANCE_TABLE_FILTER_REQUEST';
export const GET_PERFORMANCE_TABLE_FILTER_SUCCESS = 'GET_PERFORMANCE_TABLE_FILTER_SUCCESS';
export const GET_PERFORMANCE_TABLE_FILTER_FAILURE = 'GET_PERFORMANCE_TABLE_FILTER_FAILURE';

const getPerformanceTableFilterRequest = createAction(GET_PERFORMANCE_TABLE_FILTER_REQUEST);
const getPerformanceTableFilterSuccess = createAction(GET_PERFORMANCE_TABLE_FILTER_SUCCESS, 'data');
const getPerformanceTableFilterFailure = createAction(
  GET_PERFORMANCE_TABLE_FILTER_FAILURE,
  'error'
);

export function getPerformanceTableFilter(url, cb) {
  return function (dispatch) {
    dispatch(getPerformanceTableFilterRequest(true));
    axios
      .get(url)
      .then((res) => {
        dispatch(getPerformanceTableFilterSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(getPerformanceTableFilterFailure(error)));
  };
}

export const GET_PERFORMANCE_TABLE_BODY_REQUEST = 'GET_PERFORMANCE_TABLE_BODY_REQUEST';
export const GET_PERFORMANCE_TABLE_BODY_SUCCESS = 'GET_PERFORMANCE_TABLE_BODY_SUCCESS';
export const GET_PERFORMANCE_TABLE_BODY_FAILURE = 'GET_PERFORMANCE_TABLE_BODY_FAILURE';

const getPerformanceTableBodyRequest = createAction(GET_PERFORMANCE_TABLE_BODY_REQUEST);
const getPerformanceTableBodySuccess = createAction(GET_PERFORMANCE_TABLE_BODY_SUCCESS, 'data');
const getPerformanceTableBodyFailure = createAction(GET_PERFORMANCE_TABLE_BODY_FAILURE, 'error');

export function getPerformanceTableBody(url) {
  return function (dispatch) {
    dispatch(getPerformanceTableBodyRequest(true));
    axios
      .get(url)
      .then((res) => dispatch(getPerformanceTableBodySuccess(res.data.data)))
      .catch((error) => dispatch(getPerformanceTableBodyFailure(error)));
  };
}

export const SAVE_SCHEDULE_SETTINGS_REQUEST = 'SAVE_SCHEDULE_SETTINGS_REQUEST';
export const SAVE_SCHEDULE_SETTINGS_SUCCESS = 'SAVE_SCHEDULE_SETTINGS_SUCCESS';
export const SAVE_SCHEDULE_SETTINGS_FAILURE = 'SAVE_SCHEDULE_SETTINGS_FAILURE';

const saveScheduleSettingsRequest = createAction(SAVE_SCHEDULE_SETTINGS_REQUEST);
const saveScheduleSettingsSuccess = createAction(SAVE_SCHEDULE_SETTINGS_SUCCESS, 'data');
const saveScheduleSettingsFailure = createAction(SAVE_SCHEDULE_SETTINGS_FAILURE, 'error');

export function saveScheduleSettings({ payload, callBack }) {
  return function (dispatch) {
    dispatch(saveScheduleSettingsRequest(true));
    axios
      .post('/course_schedule/dayScheduleOnSiteToRemote', payload)
      .then((res) => {
        dispatch(saveScheduleSettingsSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((error) => dispatch(saveScheduleSettingsFailure(error)));
  };
}

export const POST_TAG_MASTER_SETTINGS_REQUEST = 'POST_TAG_MASTER_SETTINGS_REQUEST';
export const POST_TAG_MASTER_SETTINGS_SUCCESS = 'POST_TAG_MASTER_SETTINGS_SUCCESS';
export const POST_TAG_MASTER_SETTINGS_FAILURE = 'POST_TAG_MASTER_SETTINGS_FAILURE';

const postTagMasterRequest = createAction(POST_TAG_MASTER_SETTINGS_REQUEST);
const postTagMasterSuccess = createAction(POST_TAG_MASTER_SETTINGS_SUCCESS, 'data');
const postTagMasterFailure = createAction(POST_TAG_MASTER_SETTINGS_FAILURE, 'error');

export function postSelectedTagMaster(requestBody, callback, operation) {
  return function (dispatch) {
    const method = {
      create: 'post',
      update: 'put',
    };
    dispatch(postTagMasterRequest());
    axios[method[operation]](
      `/globalConfiguration/tagMaster/${
        operation === 'create'
          ? 'manageTagMasterGroupFamilySettings'
          : operation === 'update'
          ? 'updatedTagMasterSettings'
          : ''
      }`,
      requestBody
    )
      .then((res) => {
        dispatch(postTagMasterSuccess(res.data.data));
        callback && callback();
      })
      .catch((error) => dispatch(postTagMasterFailure(error)));
  };
}

export const GET_TAG_MASTER_SETTINGS_REQUEST = 'GET_TAG_MASTER_SETTINGS_REQUEST';
export const GET_TAG_MASTER_SETTINGS_SUCCESS = 'GET_TAG_MASTER_SETTINGS_SUCCESS';
export const GET_TAG_MASTER_SETTINGS_FAILURE = 'GET_TAG_MASTER_SETTINGS_FAILURE';

const getTagMasterRequest = createAction(GET_TAG_MASTER_SETTINGS_REQUEST);
const getTagMasterSuccess = createAction(GET_TAG_MASTER_SETTINGS_SUCCESS, 'data');
const getTagMasterFailure = createAction(GET_TAG_MASTER_SETTINGS_FAILURE, 'error');

export function getSelectedTagMaster() {
  return function (dispatch) {
    dispatch(getTagMasterRequest());
    let URL = `/globalConfiguration/tagMaster/getTagMasterSettings`;
    axios
      .get(URL)
      .then((res) => {
        dispatch(getTagMasterSuccess(res.data.data));
      })
      .catch((error) => dispatch(getTagMasterFailure(error)));
  };
}

export const DELETE_TAG_MASTER_SETTINGS_REQUEST = 'DELETE_TAG_MASTER_SETTINGS_REQUEST';
export const DELETE_TAG_MASTER_SETTINGS_SUCCESS = 'DELETE_TAG_MASTER_SETTINGS_SUCCESS';
export const DELETE_TAG_MASTER_SETTINGS_FAILURE = 'DELETE_TAG_MASTER_SETTINGS_FAILURE';

const deleteTagMasterRequest = createAction(DELETE_TAG_MASTER_SETTINGS_REQUEST);
const deleteTagMasterSuccess = createAction(DELETE_TAG_MASTER_SETTINGS_SUCCESS, 'data');
const deleteTagMasterFailure = createAction(DELETE_TAG_MASTER_SETTINGS_FAILURE, 'error');

export function deleteSelectedTagMaster(params, callback) {
  return function (dispatch) {
    dispatch(deleteTagMasterRequest());
    axios
      .delete(
        `/globalConfiguration/tagMaster/deleteTagMasterSettings?tagGroupId=${params.tagGroupId}&type=${params.type}`
      )
      .then((res) => {
        dispatch(deleteTagMasterSuccess(res.data.data));
        callback && callback();
      })
      .catch((error) => dispatch(deleteTagMasterFailure(error)));
  };
}

export const UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_REQUEST =
  'UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_REQUEST';
export const UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_SUCCESS =
  'UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_SUCCESS';
export const UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_FAILURE =
  'UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_FAILURE';

const updateOverallTagMasterSettingsDataRequest = createAction(
  UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_REQUEST
);
const updateOverallTagMasterSettingsDataSuccess = createAction(
  UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_SUCCESS,
  'data'
);
const updateOverallTagMasterSettingsDataFailure = createAction(
  UPDATE_OVERALL_TAG_MASTER_SETTINGS_DATA_FAILURE,
  'error'
);

export function updateOverallTagMaster(requestBody, callback) {
  return function (dispatch) {
    dispatch(updateOverallTagMasterSettingsDataRequest());
    axios
      .put(
        `/globalConfiguration/tagMaster/updatedOverAllTagMasterSettings/${requestBody.get(
          '_id',
          ''
        )}`,
        requestBody
      )
      .then((res) => {
        dispatch(updateOverallTagMasterSettingsDataSuccess(res.data.data));
        callback && callback();
      })
      .catch((error) => dispatch(updateOverallTagMasterSettingsDataFailure(error)));
  };
}
export const UPDATE_REMARKS_VISIBILITY_REQUEST = 'UPDATE_REMARKS_VISIBILITY_REQUEST';
export const UPDATE_REMARKS_VISIBILITY_SUCCESS = 'UPDATE_REMARKS_VISIBILITY_SUCCESS';
export const UPDATE_REMARKS_VISIBILITY_FAILURE = 'UPDATE_REMARKS_VISIBILITY_FAILURE';

const updateRemarksVisibilityRequest = createAction(UPDATE_REMARKS_VISIBILITY_REQUEST);
const updateRemarksVisibilitySuccess = createAction(UPDATE_REMARKS_VISIBILITY_SUCCESS, 'data');
const updateRemarksVisibilityFailure = createAction(UPDATE_REMARKS_VISIBILITY_FAILURE, 'error');

export function updateRemarksVisibility(requestBody, callBack) {
  return function (dispatch) {
    dispatch(updateRemarksVisibilityRequest());
    axios
      .put(`/global-session-settings/disciplinary_remarks_visibility`, requestBody)
      .then(() => {
        dispatch(updateRemarksVisibilitySuccess());
        callBack && callBack();
      })
      .catch((error) => dispatch(updateRemarksVisibilityFailure(error)));
  };
}
export const GET_REMARKS_VISIBILITY_REQUEST = 'GET_REMARKS_VISIBILITY_REQUEST';
export const GET_REMARKS_VISIBILITY_SUCCESS = 'GET_REMARKS_VISIBILITY_SUCCESS';
export const GET_REMARKS_VISIBILITY_FAILURE = 'GET_REMARKS_VISIBILITY_FAILURE';

const getRemarksVisibilityRequest = createAction(GET_REMARKS_VISIBILITY_REQUEST);
const getRemarksVisibilitySuccess = createAction(GET_REMARKS_VISIBILITY_SUCCESS, 'data');
const getRemarksVisibilityFailure = createAction(GET_REMARKS_VISIBILITY_FAILURE, 'error');

export function getRemarksVisibility(callBack) {
  return function (dispatch) {
    dispatch(getRemarksVisibilityRequest());
    axios
      .get(`/global-session-settings/disciplinary_remarks_visibility`)
      .then((res) => {
        dispatch(getRemarksVisibilitySuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getRemarksVisibilityFailure(error)));
  };
}

export const PUT_UPDATE_SCHEDULE_PERMISSION_REQUEST = 'PUT_UPDATE_SCHEDULE_PERMISSION_REQUEST';
export const PUT_UPDATE_SCHEDULE_PERMISSION_SUCCESS = 'PUT_UPDATE_SCHEDULE_PERMISSION_SUCCESS';
export const PUT_UPDATE_SCHEDULE_PERMISSION_FAILURE = 'PUT_UPDATE_SCHEDULE_PERMISSION_FAILURE';

const putUpdateSchedulePermissionRequest = createAction(PUT_UPDATE_SCHEDULE_PERMISSION_REQUEST);
const putUpdateSchedulePermissionSuccess = createAction(
  PUT_UPDATE_SCHEDULE_PERMISSION_SUCCESS,
  'data'
);
const putUpdateSchedulePermissionFailure = createAction(
  PUT_UPDATE_SCHEDULE_PERMISSION_FAILURE,
  'error'
);

export function updateSchedulePermission(shedulePermissionBool, callBack) {
  const { create, edit } = shedulePermissionBool.toJS();
  return function (dispatch) {
    dispatch(putUpdateSchedulePermissionRequest());
    axios
      .put(`/global-session-settings/updateSchedulePermission?create=${create}&edit=${edit}`)
      .then(() => {
        dispatch(putUpdateSchedulePermissionSuccess());
        callBack && callBack(shedulePermissionBool);
      })
      .catch((error) => dispatch(putUpdateSchedulePermissionFailure(error)));
  };
}

// staff , student , self registration document toggle button
export const UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_REQUEST =
  'UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_REQUEST';
export const UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_SUCCESS =
  'UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_SUCCESS';
export const UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_FAILURE =
  'UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_FAILURE';

const updateFacialAndSelfRegisterDocToggleRequest = createAction(
  UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_REQUEST
);
const updateFacialAndSelfRegisterDocToggleSuccess = createAction(
  UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_SUCCESS
);
const updateFacialAndSelfRegisterDocToggleFailure = createAction(
  UPDATE_FACIAL_AND_SELF_REGISTER_DOCUMENT_TOGGLE_FAILURE
);

export function updateFacialAndSelfRegisterDocumentToggle({
  isFacial,
  isSelfRegistration,
  type,
  callBack,
}) {
  let data;
  let url = '/global-session-settings';
  if (type === 'staff') {
    data = `staffFacial=${isFacial}`;
    url = `${url}/updateStaffFacial`;
  } else if (type === 'student') {
    data = `isFacial=${isFacial}`;
    url = `${url}/student-facial`;
  } else {
    data = `isSelfRegistration=${isSelfRegistration}`;
    url = `${url}/student-self-registration`;
  }

  return function (dispatch) {
    dispatch(updateFacialAndSelfRegisterDocToggleRequest());
    axios
      .put(`${url}?${data}`)
      .then(() => {
        dispatch(updateFacialAndSelfRegisterDocToggleSuccess());
        callBack && callBack();
      })
      .catch((error) => dispatch(updateFacialAndSelfRegisterDocToggleFailure(error)));
  };
}

//-------------------------Qa Process Configuration

export const GET_CALENDER_REQUEST = 'GET_CALENDER_REQUEST';
export const GET_CALENDER_SUCCESS = 'GET_CALENDER_SUCCESS';
export const GET_CALENDER_FAILURE = 'GET_CALENDER_FAILURE';

const getQaPcCalenderRequest = createAction(GET_CALENDER_REQUEST, 'requestBody');
const getQaPcCalenderSuccess = createAction(GET_CALENDER_SUCCESS, 'data');
const getQaPcCalenderFailure = createAction(GET_CALENDER_FAILURE, 'error');

export function getQaPcCalender() {
  return function (dispatch) {
    dispatch(getQaPcCalenderRequest());
    axios
      .get('/qapcSetting/institutionCalendarList')
      .then((res) => {
        dispatch(getQaPcCalenderSuccess(res.data.data));
      })
      .catch((error) => dispatch(getQaPcCalenderFailure(error)));
  };
}

export const GET_CONFIGURE_TEMPLATE_REQUEST = 'GET_CONFIGURE_TEMPLATE_REQUEST';
export const GET_CONFIGURE_TEMPLATE_SUCCESS = 'GET_CONFIGURE_TEMPLATE_SUCCESS';
export const GET_CONFIGURE_TEMPLATE_FAILURE = 'GET_CONFIGURE_TEMPLATE_FAILURE';

const getConfigureTemplateRequest = createAction(GET_CONFIGURE_TEMPLATE_REQUEST, 'requestBody');
const getConfigureTemplateSuccess = createAction(GET_CONFIGURE_TEMPLATE_SUCCESS, 'data');
const getConfigureTemplateFailure = createAction(GET_CONFIGURE_TEMPLATE_FAILURE, 'error');

export function getConfigureTemplate() {
  return function (dispatch) {
    dispatch(getConfigureTemplateRequest());
    axios
      .get('qapcCategory_v2/getConfigureTemplate')
      .then((res) => {
        dispatch(getConfigureTemplateSuccess(res.data.data));
      })
      .catch((error) => dispatch(getConfigureTemplateFailure(error)));
  };
}

export const UPDATE_CATEGORY_CONFIGURE_REQUEST = 'UPDATE_CATEGORY_CONFIGURE_REQUEST';
export const UPDATE_CATEGORY_CONFIGURE_SUCCESS = 'UPDATE_CATEGORY_CONFIGURE_SUCCESS';
export const UPDATE_CATEGORY_CONFIGURE_FAILURE = 'UPDATE_CATEGORY_CONFIGURE_FAILURE';

const updateCategoryConfigureRequest = createAction(
  UPDATE_CATEGORY_CONFIGURE_REQUEST,
  'requestBody'
);
const updateCategoryConfigureSuccess = createAction(UPDATE_CATEGORY_CONFIGURE_SUCCESS, 'data');
const updateCategoryConfigureFailure = createAction(UPDATE_CATEGORY_CONFIGURE_FAILURE, 'error');

export function updateCategoryConfigure(payload, cb) {
  return function (dispatch) {
    dispatch(updateCategoryConfigureRequest());
    axios
      .put('/qapcCategory/updateCategoryConfigure', payload)
      .then((res) => {
        dispatch(updateCategoryConfigureSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(updateCategoryConfigureFailure(error)));
  };
}

export const GET_SINGLE_CONFIG_REQUEST = 'GET_SINGLE_CONFIG_REQUEST';
export const GET_SINGLE_CONFIG_SUCCESS = 'GET_SINGLE_CONFIG_SUCCESS';
export const GET_SINGLE_CONFIG_FAILURE = 'GET_SINGLE_CONFIG_FAILURE';

const getSingleConfigRequest = createAction(GET_SINGLE_CONFIG_REQUEST, 'requestBody');
const getSingleConfigSuccess = createAction(GET_SINGLE_CONFIG_SUCCESS, 'data');
const getSingleConfigFailure = createAction(GET_SINGLE_CONFIG_FAILURE, 'error');

export function getSingleConfig(url) {
  return function (dispatch) {
    dispatch(getSingleConfigRequest());
    axios
      .get(url)
      .then((res) => {
        dispatch(getSingleConfigSuccess(res.data.data));
      })
      .catch((error) => dispatch(getSingleConfigFailure(error)));
  };
}

export const GET_ATTEMPT_TYPE_REQUEST = 'GET_ATTEMPT_TYPE_REQUEST';
export const GET_ATTEMPT_TYPE_SUCCESS = 'GET_ATTEMPT_TYPE_SUCCESS';
export const GET_ATTEMPT_TYPE_FAILURE = 'GET_ATTEMPT_TYPE_FAILURE';

const getAttemptTypeRequest = createAction(GET_ATTEMPT_TYPE_REQUEST, 'requestBody');
const getAttemptTypeSuccess = createAction(GET_ATTEMPT_TYPE_SUCCESS, 'data');
const getAttemptTypeFailure = createAction(GET_ATTEMPT_TYPE_FAILURE, 'error');

export function getAttemptType(url) {
  return function (dispatch) {
    dispatch(getAttemptTypeRequest());
    axios
      .get(url)
      .then((res) => {
        dispatch(getAttemptTypeSuccess(res.data.data));
      })
      .catch((error) => dispatch(getAttemptTypeFailure(error)));
  };
}

export const QA_PC_PROGRAM_DETAILS_REQUEST = 'QA_PC_PROGRAM_DETAILS_REQUEST';
export const QA_PC_PROGRAM_DETAILS_SUCCESS = 'QA_PC_PROGRAM_DETAILS_SUCCESS';
export const QA_PC_PROGRAM_DETAILS_FAILURE = 'QA_PC_PROGRAM_DETAILS_FAILURE';

const qaPcProgramDetailsRequest = createAction(QA_PC_PROGRAM_DETAILS_REQUEST, 'requestBody');
const qaPcProgramDetailsSuccess = createAction(QA_PC_PROGRAM_DETAILS_SUCCESS, 'data');
const qaPcProgramDetailsFailure = createAction(QA_PC_PROGRAM_DETAILS_FAILURE, 'error');

export function getProgramDetails({ params, cb, checked }) {
  return function (dispatch) {
    dispatch(qaPcProgramDetailsRequest());
    axios
      .get('/qapcCategoryForm/programDetails', { params })
      .then((res) => {
        const constructData = constructDataCourseWise(fromJS(res.data.data), checked);
        dispatch(qaPcProgramDetailsSuccess(constructData));
        cb && cb(constructData);
      })
      .catch((error) => dispatch(qaPcProgramDetailsFailure(error)));
  };
}

export const GET_Role_User_List_Q360_REQUEST = 'GET_Role_User_List_Q360_REQUEST';
export const GET_Role_User_List_Q360_SUCCESS = 'GET_Role_User_List_Q360_SUCCESS';
export const GET_Role_User_List_Q360_FAILURE = 'GET_Role_User_List_Q360_FAILURE';

const getRoleUserListQ360Request = createAction(GET_Role_User_List_Q360_REQUEST);
const getRoleUserListQ360Success = createAction(GET_Role_User_List_Q360_SUCCESS, 'data');
const getRoleUserListQ360Failure = createAction(GET_Role_User_List_Q360_FAILURE, 'error');

export function roleUserListQ360() {
  return function (dispatch) {
    dispatch(getRoleUserListQ360Request());
    let URL = `/qapcCategoryForm/roleUserList`;
    axios
      .get(URL)
      .then((res) => {
        dispatch(getRoleUserListQ360Success(res.data.data));
      })
      .catch((error) => dispatch(getRoleUserListQ360Failure(error)));
  };
}

export const CREATE_DUPLICATE_FORM_REQUEST_V1 = 'CREATE_DUPLICATE_FORM_REQUEST_V1';
export const CREATE_DUPLICATE_FORM_SUCCESS_V1 = 'CREATE_DUPLICATE_FORM_SUCCESS_V1';
export const CREATE_DUPLICATE_FORM_FAILURE_V1 = 'CREATE_DUPLICATE_FORM_FAILURE_V1';

const createDuplicateFormRequest = createAction(CREATE_DUPLICATE_FORM_REQUEST_V1);
const createDuplicateFormSuccess = createAction(CREATE_DUPLICATE_FORM_SUCCESS_V1, 'data', 'method');
const createDuplicateFormFailure = createAction(CREATE_DUPLICATE_FORM_FAILURE_V1, 'error');

export function createDuplicateForm(requestBody, method = 'create', cb) {
  return function (dispatch) {
    dispatch(createDuplicateFormRequest());
    axios
      .post('/qapcCategoryForm/createDuplicateForm', requestBody)
      .then((res) => {
        dispatch(createDuplicateFormSuccess(res.data.data, method));
        cb && cb();
      })
      .catch((error) => dispatch(createDuplicateFormFailure(error)));
  };
}

export const QA_PC_CATEGORY_FORM_REQUEST = 'QA_PC_CATEGORY_FORM_REQUEST';
export const QA_PC_CATEGORY_FORM_SUCCESS = 'QA_PC_CATEGORY_FORM_SUCCESS';
export const QA_PC_CATEGORY_FORM_FAILURE = 'QA_PC_CATEGORY_FORM_FAILURE';

const qaPcCategoryFormsRequest = createAction(QA_PC_CATEGORY_FORM_REQUEST);
const qaPcCategoryFormsSuccess = createAction(QA_PC_CATEGORY_FORM_SUCCESS, 'data');
const qaPcCategoryFormsFailure = createAction(QA_PC_CATEGORY_FORM_FAILURE, 'error');

export function getCategoryForms({ params }) {
  return function (dispatch) {
    dispatch(qaPcCategoryFormsRequest());
    axios
      .get('/qapcCategoryForm_v2/getCategoryForm', { params })
      .then((res) => {
        dispatch(qaPcCategoryFormsSuccess(res.data.data));
      })
      .catch((error) => dispatch(qaPcCategoryFormsFailure(error)));
  };
}

export const UPDATE_DUPLICATE_FORM_REQUEST = 'UPDATE_DUPLICATE_FORM_REQUEST';
export const UPDATE_DUPLICATE_FORM_SUCCESS = 'UPDATE_DUPLICATE_FORM_SUCCESS';
export const UPDATE_DUPLICATE_FORM_FAILURE = 'UPDATE_DUPLICATE_FORM_FAILURE';

const updateDuplicateFormRequest = createAction(UPDATE_DUPLICATE_FORM_REQUEST);
const updateDuplicateFormSuccess = createAction(UPDATE_DUPLICATE_FORM_SUCCESS, 'data', 'method');
const updateDuplicateFormFailure = createAction(UPDATE_DUPLICATE_FORM_FAILURE, 'error');

export function updateDuplicateForm(requestBody, cb) {
  return function (dispatch) {
    dispatch(updateDuplicateFormRequest());
    axios
      .put('/qapcCategoryForm/updateCategoryForm', requestBody)
      .then((res) => {
        dispatch(updateDuplicateFormSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(updateDuplicateFormFailure(error)));
  };
}

export const UPLOAD_ATTACHMENT_REQUEST = 'UPLOAD_ATTACHMENT_REQUEST';
export const UPLOAD_ATTACHMENT_SUCCESS = 'UPLOAD_ATTACHMENT_SUCCESS';
export const UPLOAD_ATTACHMENT_FAILURE = 'UPLOAD_ATTACHMENT_FAILURE';
const uploadAttachmentRequest = createAction(UPLOAD_ATTACHMENT_REQUEST);
const uploadAttachmentSuccess = createAction(UPLOAD_ATTACHMENT_SUCCESS, 'data');
const uploadAttachmentFailure = createAction(UPLOAD_ATTACHMENT_FAILURE, 'error');
export function uploadAttachment(file, options, successCallBack, failureCallBack) {
  return function (dispatch) {
    dispatch(uploadAttachmentRequest());
    axios
      .post(`/lmsStudentSetting/upload`, file, options)
      .then((res) => {
        dispatch(uploadAttachmentSuccess(res.data.data));
        successCallBack && successCallBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(uploadAttachmentFailure(error));
        failureCallBack && failureCallBack();
      });
  };
}

export const QA_PC_PROGRAM_LIST_REQUEST = 'QA_PC_PROGRAM_LIST_REQUEST';
export const QA_PC_PROGRAM_LIST_SUCCESS = 'QA_PC_PROGRAM_LIST_SUCCESS';
export const QA_PC_PROGRAM_LIST_FAILURE = 'QA_PC_PROGRAM_LIST_FAILURE';

const qaPcProgramListRequest = createAction(QA_PC_PROGRAM_LIST_REQUEST, 'requestBody');
const qaPcProgramListSuccess = createAction(
  QA_PC_PROGRAM_LIST_SUCCESS,
  'data',
  'query',
  'currentPaginationCount'
);
const qaPcProgramListFailure = createAction(QA_PC_PROGRAM_LIST_FAILURE, 'error');

export function getProgramListForQAPC({ params, currentPaginationCount }) {
  return function (dispatch) {
    dispatch(qaPcProgramListRequest());
    axios
      .get('/qapcCategoryForm/getProgramList', {
        params,
      })
      .then((res) => {
        dispatch(qaPcProgramListSuccess(res.data.data, params.searchKey, currentPaginationCount));
      })
      .catch((error) => dispatch(qaPcProgramListFailure(error)));
  };
}

export const GET_REFERENCE_FORM_REQUEST = 'GET_REFERENCE_FORM_REQUEST';
export const GET_REFERENCE_FORM_SUCCESS = 'GET_REFERENCE_FORM_SUCCESS';
export const GET_REFERENCE_FORM_FAILURE = 'GET_REFERENCE_FORM_FAILURE';

const getReferenceFormRequest = createAction(GET_REFERENCE_FORM_REQUEST);
const getReferenceFormSuccess = createAction(GET_REFERENCE_FORM_SUCCESS, 'data');
const getReferenceFormFailure = createAction(GET_REFERENCE_FORM_FAILURE, 'error');

export function getReferenceForm({ CategoryId, selectedCalendarData, FormId }) {
  return function (dispatch) {
    dispatch(getReferenceFormRequest());

    const URL =
      CategoryId && FormId
        ? `/qapcform/getReferenceForm?categoryId=${CategoryId}&_institution_calendar_id=${selectedCalendarData}&categoryFormId=${FormId}`
        : `/qapcform/getReferenceForm?_institution_calendar_id=${selectedCalendarData}`;
    axios
      .get(URL)
      .then((res) => {
        dispatch(getReferenceFormSuccess(res.data.data));
      })
      .catch((error) => dispatch(getReferenceFormFailure(error)));
  };
}

export const UPDATE_CATEGORY_REQUEST = 'UPDATE_CATEGORY_REQUEST';
export const UPDATE_CATEGORY_SUCCESS = 'UPDATE_CATEGORY_SUCCESS';
export const UPDATE_CATEGORY_FAILURE = 'UPDATE_CATEGORY_FAILURE';

const updateCategoryRequest = createAction(UPDATE_CATEGORY_REQUEST);
const updateCategorySuccess = createAction(UPDATE_CATEGORY_SUCCESS, 'data');
const updateCategoryFailure = createAction(UPDATE_CATEGORY_FAILURE, 'error');

export function updateCategory(payload, cb) {
  return function (dispatch) {
    dispatch(updateCategoryRequest());
    axios
      .post(`/qapcCategory_v2/createCategoryConfigure`, payload)
      .then((res) => {
        dispatch(updateCategorySuccess(res.data.data));
        dispatch(getConfigureTemplate());
        cb && cb();
      })
      .catch((error) => {
        dispatch(updateCategoryFailure(error));
      });
  };
}

export const QA_PC_CURRICULUM_DETAILS_REQUEST = 'QA_PC_CURRICULUM_DETAILS_REQUEST';
export const QA_PC_CURRICULUM_DETAILS_SUCCESS = 'QA_PC_CURRICULUM_DETAILS_SUCCESS';
export const QA_PC_CURRICULUM_DETAILS_FAILURE = 'QA_PC_CURRICULUM_DETAILS_FAILURE';

const qaPcCurriculumDetailsRequest = createAction(QA_PC_CURRICULUM_DETAILS_REQUEST, 'requestBody');
const qaPcCurriculumDetailsSuccess = createAction(
  QA_PC_CURRICULUM_DETAILS_SUCCESS,
  'data',
  'checked'
);
const qaPcCurriculumDetailsFailure = createAction(QA_PC_CURRICULUM_DETAILS_FAILURE, 'error');

export function getCurriculumDetails({ params, cb, checked, program_name, programId }) {
  return function (dispatch) {
    dispatch(qaPcCurriculumDetailsRequest());
    axios
      .get('/qapcCategoryForm_v2/getCurriculum', { params })
      .then((res) => {
        const constructedData = constructDataProgramWise(
          fromJS(res.data.data),
          checked,
          programId,
          program_name
        );
        dispatch(qaPcCurriculumDetailsSuccess(constructedData));
        cb && cb(constructedData);
      })
      .catch((error) => dispatch(qaPcCurriculumDetailsFailure(error)));
  };
}

export const UPDATE_HANDOUT_REQUEST = 'UPDATE_HANDOUT_REQUEST';
export const UPDATE_HANDOUT_SUCCESS = 'UPDATE_HANDOUT_SUCCESS';
export const UPDATE_HANDOUT_FAILURE = 'UPDATE_HANDOUT_FAILURE';

const updateHandoutRequest = createAction(UPDATE_HANDOUT_REQUEST);
const updateHandoutSuccess = createAction(UPDATE_HANDOUT_SUCCESS, 'data');
const updateHandoutFailure = createAction(UPDATE_HANDOUT_FAILURE, 'error');

export function updateHandout(requestBody) {
  return function (dispatch) {
    dispatch(updateHandoutRequest());
    axios
      .put(`global-session-settings/updateHandout`, requestBody)
      .then(() => {
        dispatch(updateHandoutSuccess());
      })
      .catch((error) => dispatch(updateHandoutFailure(error)));
  };
}

export const GET_HANDOUT_REQUEST = 'GET_HANDOUT_REQUEST';
export const GET_HANDOUT_SUCCESS = 'GET_HANDOUT_SUCCESS';
export const GET_HANDOUT_FAILURE = 'GET_HANDOUT_FAILURE';

const getHandoutRequest = createAction(GET_HANDOUT_REQUEST);
const getHandoutSuccess = createAction(GET_HANDOUT_SUCCESS, 'data');
const getHandoutFailure = createAction(GET_HANDOUT_FAILURE, 'error');

export function getHandout() {
  return function (dispatch) {
    dispatch(getHandoutRequest());
    axios
      .get(`global-session-settings/getHandOut`)
      .then((res) => {
        dispatch(getHandoutSuccess(res.data.data));
      })
      .catch((error) => dispatch(getHandoutFailure(error)));
  };
}
