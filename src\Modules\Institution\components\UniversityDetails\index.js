import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';

import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';

import { selectIsLoading, selectMessage } from '_reduxapi/institution/selectors';

class Institution extends Component {
  render() {
    const { message, isLoading } = this.props;
    return (
      <div>
        {message !== '' && false && <SnackBars show={true} message={message} />}
        <Loader isLoading={isLoading} />
      </div>
    );
  }
}

Institution.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(Institution);
