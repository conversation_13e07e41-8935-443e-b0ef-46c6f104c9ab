import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import CloseIcon from '@mui/icons-material/Close';
import SettingsIcon from '@mui/icons-material/Settings';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
const subTitleSx = {
  marginRight: '40px',
  padding: '4px 8px',
  fontSize: '14px',
  borderRadius: '4px',
  backgroundColor: '#E0E7FF',
  color: '#4338CA',
};
const titleIconSx = {
  padding: '8px',
  backgroundColor: '#EFF9FB',
  borderRadius: '50%',
  marginRight: '16px',
  color: '#0064C8',
};
const closeIconSx = {
  position: 'absolute',
  right: 8,
  top: 16,
};

const GenderSegregationModal = ({ open, handleClose, handleSave }) => {
  const [gender, setGender] = useState('');

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Box display="flex" sx={titleIconSx}>
              <SettingsIcon />
            </Box>
            Gender Segregation
          </Box>
          <Typography sx={subTitleSx}>Applicable for All Year</Typography>
        </Box>
      </DialogTitle>
      <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
        <CloseIcon />
      </IconButton>
      <DialogContent className="p-3 f-14">
        <p className="mb-1 bold">One Time Set-up!</p>
        <p className="mb-1 text-gray">
          Choose a Student Gender Segregation Method for Student Grouping:
        </p>
        <RadioGroup value={gender} onChange={(e) => setGender(e.target.value)}>
          <FormControlLabel
            value="separate"
            control={<Radio />}
            label={
              <>
                <p className="f-14">Separate by Gender</p>
                <p className="f-12 text-gray">
                  Sets individual delivery group setting for male and female students.
                </p>
              </>
            }
          />
          <FormControlLabel
            value="mix"
            control={<Radio />}
            label={
              <>
                <p className="f-14">Mix Male and Female Together</p>
                <p className="f-12 text-gray">
                  Uses the same delivery groups setting for all students.
                </p>
              </>
            }
          />
        </RadioGroup>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" clicked={handleClose}>
          Cancel
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleSave} disabled={!gender}>
          Save
        </MButton>
      </DialogActions>
    </Dialog>
  );
};

GenderSegregationModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  handleSave: PropTypes.func,
};

export default GenderSegregationModal;
