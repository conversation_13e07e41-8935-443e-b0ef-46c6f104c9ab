import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import axios from '../../../axios';
import Loader from '../../../Widgets/Loader/Loader';
import { Accordion, Card, Button } from 'react-bootstrap';
import ProfileText from '../../../Widgets/ProfileText';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import { NotificationManager } from 'react-notifications';
import { isIndVer, ucFirst, isModuleEnabled } from '../../../utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import VaccinationDetails from '../../../Modules/UserManagement/VaccinationDetails';
import { Trans } from 'react-i18next';
import { clickArrow } from '../../utils';
import { t } from 'i18next';
let id;
// let pageNum;
class MismatchStudentProfile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      first: '',
      last: '',
      middle: '',
      _nationality_id: '',
      building: '',
      city: '',
      district: '',
      zip_code: '',
      unit: '',
      passport_no: '',
      username: '',
      email: '',
      mobile: '',
      office_extension: '',
      _appointment_order_doc: '',
      _employee_id_doc: '',
      middleError: '',
      firstError: '',
      programName: '',
      uploadedDoc: [],
      contact: [],
      relation_type: '',
      tabs: '',
      selectedBatch: '',
      id: '',
      selectedIndex: '',
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');
    let tab = params.get('tab');
    this.setState({ tabs: tab, id: id });
    this.fetchApi();
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });
    axios
      .get(`/user/student/${id}`)
      .then((res) => {
        const data = res.data.data;
        this.setState({
          data: data,
          first: data.name.first,
          last: data.name.last,
          middle: data.name.middle,
          family: data.name.family,
          dob: data.dob,
          academic: data.academic,
          _nationality_id: data.address._nationality_id,
          nationality_id: data.address.nationality_id,
          building: data.address.building,
          city: data.address.city,
          district: data.address.district,
          zip_code: data.address.zip_code,
          unit: data.address.unit,
          passport_no: data.address.passport_no,
          email: data.email,
          mobile: data.mobile,
          gender: data.gender,
          programName: data.program_name,
          contact: data.contact,
          uploadedDoc: [
            {
              name: undefined,
            },
            {
              image:
                data.student_docs._school_certificate_doc !== undefined &&
                data.student_docs._school_certificate_doc !== ''
                  ? data.student_docs._school_certificate_doc
                  : '',
              name: t('user_management.school_certificate'),
              isOpen: false,
            },
            {
              image:
                data.student_docs._entrance_exam_certificate_doc !== undefined &&
                data.student_docs._entrance_exam_certificate_doc !== ''
                  ? data.student_docs._entrance_exam_certificate_doc
                  : '',
              name: t('user_management.entrance_exam_certificate'),
              isOpen: false,
            },
            {
              image:
                data.enrollment._admission_order_doc !== undefined &&
                data.enrollment._admission_order_doc !== ''
                  ? data.enrollment._admission_order_doc
                  : '',
              name: t('user_management.admission_documents'),
              isOpen: false,
            },
            {
              image:
                data.id_doc._college_id_doc !== undefined && data.id_doc._college_id_doc !== ''
                  ? data.id_doc._college_id_doc
                  : '',
              name: t('user_management.college_id'),
              isOpen: false,
            },
            {
              image:
                data.address._nationality_id_doc !== undefined &&
                data.address._nationality_id_doc !== ''
                  ? data.address._nationality_id_doc
                  : '',
              name: t('user_management.national_resident_id'),
              isOpen: false,
            },
            {
              image:
                data.address._address_doc !== undefined && data.address._address_doc !== ''
                  ? data.address._address_doc
                  : '',
              name: t('user_management.national_address'),
              isOpen: false,
            },
          ],
          correction_first_name: data.correction_first_name === true ? 'Correction Required' : '',
          correction_middle_name: data.correction_middle_name === true ? 'Correction Required' : '',
          correction_last_name: data.correction_last_name === true ? 'Correction Required' : '',
          correction_gender: data.correction_gender === true ? 'Correction Required' : '',
          correction_academic_no: data.correction_academic_no === true ? 'Correction Required' : '',
          correction_nationality_id:
            data.correction_nationality_id === true ? 'Correction Required' : '',
          correction_program_no: data.correction_program_no === true ? 'Correction Required' : '',
          isLoading: false,
          selectedBatch: data.batch,
        });
      })
      .catch((error) => {
        this.setState({
          uploadedDoc: [],
        });
      });
  };

  handleClickInvalid = () => {
    const data = {
      id: id,
      data: 'invalid',
    };
    this.setState({
      isLoading: true,
    });

    axios.post(`/user/user_profile_validation`, data).then((res) => {
      this.setState({
        isLoading: false,
      });
      NotificationManager.success(t(`user_management.success`));
      this.handleGoBack();
    });
  };
  handleClickValid = () => {
    const data = {
      id: id,
      data: 'valid',
    };
    this.setState({
      isLoading: true,
    });

    axios.post(`/user/user_profile_validation`, data).then((res) => {
      this.props.history.push({
        pathname: '/student/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
        },
      });
      this.setState({
        isLoading: false,
      });
    });
  };

  handleClickOpen = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = true;
    this.setState({
      uploadedDoc,
    });
  };

  handleClickClose = (index) => {
    const uploadedDoc = this.state.uploadedDoc;
    uploadedDoc[index].isOpen = false;
    this.setState({
      uploadedDoc,
    });
  };

  handleGoBack = () => {
    this.props.history.push({
      pathname: '/student/management',
      state: {
        completeView: false,
        pendingView: true,
        inactiveView: false,
        selectedTab: this.state.tabs !== '' ? parseInt(this.state.tabs) : 0,
        name: 'mismatch',
      },
    });
  };

  render() {
    const { selectedIndex, data } = this.state;
    const userSensitiveData = isModuleEnabled('USER_SENSITIVE');
    const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
    const documentSecNeed = isModuleEnabled('DOCUMENT_SEC_NEED');
    let nationality = '';
    let nationalId = '';

    if (data) {
      nationality = data.address._nationality_id !== undefined ? data.address._nationality_id : '';
      nationalId = data.address.nationality_id !== undefined ? data.address.nationality_id : '';
    }
    return (
      <React.Fragment>
        <Loader isLoading={this.state.isLoading} />
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'user_management.student_management_profile'}></Trans>{' '}
        </div>
        <div className="main pt-4">
          <div className="container">
            <div className="float-right">
              <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                <Trans i18nKey={'back'}></Trans>{' '}
              </Button>
              {CheckPermission(
                'subTabs',
                'User Management',
                'Student Management',
                '',
                'Registration Pending',
                '',
                'Mismatch',
                'Update Invalid'
              ) && (
                <Button className="m-2" onClick={this.handleClickInvalid}>
                  <Trans i18nKey={'user_management.tabs.Invalid'}></Trans>{' '}
                </Button>
              )}
            </div>
            <div className="clearfix"></div>
            <div className="white p-4 mb-5">
              <div className="row">
                <div className={documentSecNeed ? 'col-md-5' : 'col-md-6'}>
                  <div className="mt-0">
                    <h4 className="bold">
                      {' '}
                      <Trans i18nKey={'personal'}></Trans>
                    </h4>

                    <ProfileText
                      title={<Trans i18nKey={'first_name'}></Trans>}
                      value={this.state.first}
                      error={this.state.correction_first_name}
                      className="border-bottom pb-1"
                    />
                    <ProfileText
                      title={<Trans i18nKey={'middle_name'}></Trans>}
                      value={this.state.middle ? this.state.middle : 'N/A'}
                      error={this.state.correction_middle_name}
                    />
                    <ProfileText
                      title={<Trans i18nKey={'last_name'}></Trans>}
                      value={this.state.last ? this.state.last : 'N/A'}
                      error={this.state.correction_last_name}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'family_name'}></Trans>}
                      value={this.state.family ? this.state.family : 'N/A'}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'gender'}></Trans>}
                      value={this.state.gender}
                      error={this.state.correction_gender}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'academic_id'}></Trans>}
                      value={this.state.academic}
                      error={this.state.correction_academic_no}
                    />

                    <ProfileText
                      title={<Trans i18nKey={'emailId'}></Trans>}
                      value={this.state.email}
                    />
                    {userSensitiveData && (
                      <ProfileText
                        title={
                          <Trans
                            i18nKey={'national/residence'}
                            values={{ optional: !hasNationalIdValidation ? '(Optional)' : '' }}
                          ></Trans>
                        }
                        value={nationalId ? nationalId : 'N/A'}
                        error={this.state.correction_nationality_id}
                      />
                    )}
                    <ProfileText
                      title={<Trans i18nKey={'nationality_optional'}></Trans>}
                      value={nationality ? nationality : 'N/A'}
                    />
                    {userSensitiveData && (
                      <ProfileText
                        title={<Trans i18nKey={'passport_number'}></Trans>}
                        value={this.state.passport_no}
                      />
                    )}
                    <ProfileText
                      title={<Trans i18nKey={'program_name'}></Trans>}
                      value={this.state.programName}
                      error={this.state.correction_program_no}
                    />

                    <ProfileText
                      title={isIndVer() ? 'Intake' : 'Batch'}
                      value={
                        this.state.selectedBatch !== '' ? ucFirst(this.state.selectedBatch) : ''
                      }
                    />
                  </div>
                  {userSensitiveData && (
                    <div className="mt-5">
                      <h4 className="bold">
                        {' '}
                        <Trans i18nKey={'address'}></Trans>
                      </h4>
                      <ProfileText
                        title={<Trans i18nKey={'building_no'}></Trans>}
                        value={this.state.buildingNo}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'city_name'}></Trans>}
                        value={this.state.city}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'district_name'}></Trans>}
                        value={this.state.distric}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'zip_code'}></Trans>}
                        value={this.state.zipCode}
                      />
                      <ProfileText
                        title={<Trans i18nKey={'floor_no'}></Trans>}
                        value={this.state.unit}
                      />
                    </div>
                  )}
                  <div className="mt-5">
                    <h4 className="bold">
                      {' '}
                      <Trans i18nKey={'contact_details'}></Trans>
                    </h4>

                    <ProfileText
                      title={<Trans i18nKey={'mobile_number'}></Trans>}
                      value={this.state.mobile}
                    />
                    <div className="row">
                      {this.state.contact &&
                        this.state.contact.map((data) => (
                          <>
                            {data.relation_type && data.relation_type !== undefined && (
                              <div className="col-md-6">
                                {data.name && data.name !== undefined && (
                                  <ProfileText
                                    title={`${
                                      data.relation_type === 'guardian'
                                        ? 'Guardian'
                                        : data.relation_type === 'spouse'
                                        ? 'Spouse'
                                        : data.relation_type === undefined
                                        ? ''
                                        : data.relation_type
                                    } Name`}
                                    value={data.name}
                                  />
                                )}
                                {data.mobile && data.mobile !== undefined && (
                                  <ProfileText
                                    title={<Trans i18nKey={'mobile_number'}></Trans>}
                                    value={data.mobile}
                                  />
                                )}
                                {data.email && data.email !== undefined && (
                                  <ProfileText
                                    title={<Trans i18nKey={'email'}></Trans>}
                                    value={data.email}
                                  />
                                )}
                              </div>
                            )}
                          </>
                        ))}
                    </div>
                  </div>
                  {userSensitiveData && this.state.id !== '' && (
                    <VaccinationDetails
                      edit={false}
                      userType="student"
                      permissionName={'Mismatch'}
                      userId={this.state.id}
                      type="content"
                    />
                  )}
                </div>
                {documentSecNeed && (
                  <div className="col-md-7">
                    <div className="mt-0">
                      <h4 className="bold d-flex">
                        <Trans i18nKey={'uploaded_documents'}></Trans>{' '}
                      </h4>{' '}
                      <Accordion defaultActiveKey="">
                        {this.state.uploadedDoc.map((data, index) => (
                          <React.Fragment key={index}>
                            {data.name !== undefined && (
                              <Card>
                                <Accordion.Toggle
                                  as={Card.Header}
                                  eventKey={index}
                                  onClick={() => clickArrow.call(this, index)}
                                  className="card-header-icon"
                                >
                                  <div className="float-left">{data.name}</div>
                                  <div className="float-right">
                                    <i
                                      className={`fa fa-chevron-${
                                        selectedIndex !== index ? 'down' : 'up'
                                      } f-14`}
                                    ></i>
                                  </div>
                                </Accordion.Toggle>
                                <Accordion.Collapse eventKey={index}>
                                  <Card.Body className="bg-white">
                                    {data.image !== '' && data.image !== undefined ? (
                                      <img
                                        className="w-100"
                                        onClick={() => this.handleClickOpen(index)}
                                        src={data.image}
                                        alt="#"
                                      />
                                    ) : (
                                      <div className="float-left">
                                        <Trans i18nKey={'no_image'}></Trans>
                                      </div>
                                    )}
                                    {data.isOpen === true && (
                                      <Lightbox
                                        clickOutsideToClose={false}
                                        mainSrc={data.image}
                                        onCloseRequest={() => this.handleClickClose(index)}
                                      />
                                    )}
                                  </Card.Body>
                                </Accordion.Collapse>
                              </Card>
                            )}
                          </React.Fragment>
                        ))}
                      </Accordion>
                      {userSensitiveData && this.state.id !== '' && (
                        <VaccinationDetails
                          edit={false}
                          userType="student"
                          permissionName={'Mismatch'}
                          userId={this.state.id}
                          type="image"
                        />
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

MismatchStudentProfile.propTypes = {
  history: PropTypes.object,
};

export default withRouter(MismatchStudentProfile);
