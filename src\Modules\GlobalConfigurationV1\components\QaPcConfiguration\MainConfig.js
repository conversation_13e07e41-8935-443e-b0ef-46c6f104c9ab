import React from 'react';
import { Avatar, Box } from '@mui/material';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { generateAbbreviation, q360cardAvatarColors } from './style/designUtils';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import { useCurrentPage } from './QaPcConfiguration';
import { CheckPermission } from 'Modules/Shared/Permissions';

const QAconfiguration = ['Academic Year', 'Configure Template'];
const queryString = {
  'Academic Year': 'academic_year',
  'Configure Template': 'configure_template',
};

export default function MainConfig() {
  const query = useCurrentPage();
  const history = useHistory();
  const handleNavigate = (page) => {
    const updatedQuery = `${query}+${queryString[page]}`;
    history.push(`/globalConfiguration-v1/qa_pc_configuration?query=${updatedQuery}`);
  };
  return (
    <>
      <section className="QAPCchild-container w-30">
        {/* <Buttons>save</Buttons> <Buttons>save</Buttons> */}
      </section>
      <section className="QAPCchild-container">
        <div className="d-flex flex-column gap-3 px-3 py-3">
          {QAconfiguration.map((page, index) => {
            if (!CheckPermission('pages', 'Q360', page, 'View')) {
              return null;
            }
            const colorCombination = q360cardAvatarColors.get(index % q360cardAvatarColors.size);
            const bgColors = colorCombination.get('bgColors');
            const color = colorCombination.get('color');
            return (
              <Box
                sx={{ mt: 3 }}
                key={page}
                className="box-shadow rounded p-3 d-flex align-items-center cursor-pointer bg-white"
                onClick={() => handleNavigate(page)}
              >
                <Avatar
                  alt="No-icon"
                  className="cursor-pointer"
                  variant="rounded"
                  style={{
                    backgroundColor: bgColors,
                    color,
                  }}
                >
                  <div className="f-12">{generateAbbreviation(page)}</div>
                </Avatar>
                <section className="ml-2 flex-grow-1">
                  <p className="m-0 f-12 fw-500">{page}</p>
                  <p className="m-0 f-12 fw-400 text-lGrey">
                    Course Learning, Teaching & Assessment planning configuration.
                  </p>
                </section>
                <KeyboardArrowRightIcon className="f-18" color="#6B7280" />
              </Box>
            );
          })}
        </div>
      </section>
    </>
  );
}
