import React, { useState, useEffect, useMemo } from 'react';
import { Box, Divider, FormControl, Menu, MenuItem, Paper } from '@mui/material';
import UserGlobalIcon from 'Assets/UserGlobalIcon.svg';
import MButton from 'Widgets/FormElements/material/Button';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import {
  selectActiveInstitutionCalendar,
  selectInstitutionCalendar,
} from '_reduxapi/Common/Selectors';
import { selectUserGlobalSearch } from '_reduxapi/UserGlobalSearch/selectors';
import {
  getUserGlobalSearch,
  getUserCourseDetails,
  setData,
} from '_reduxapi/UserGlobalSearch/action';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { fromJS, Map } from 'immutable';
import { eString, jsUcfirst, jsUcfirstAll } from '../../../utils';
import useDebounce from 'Hooks/useDebounceHook';
import AutoComplete from 'Widgets/FormElements/material/Autocomplete';

const useAnchorHook = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorEl1, setAnchorEl1] = useState(null);
  const [anchorEl2, setAnchorEl2] = useState(null);

  const handleOpenOrClose = (setAnchorEl, data) => {
    setAnchorEl(data);
  };
  return {
    anchorEl,
    anchorEl1,
    anchorEl2,
    handleOpenOrClose: (data) => handleOpenOrClose(setAnchorEl, data),
    handleOpenOrClose1: (data) => handleOpenOrClose(setAnchorEl1, data),
    handleOpenOrClose2: (data) => handleOpenOrClose(setAnchorEl2, data),
  };
};

function UserGlobalSearch() {
  const { anchorEl1, handleOpenOrClose1, anchorEl2, handleOpenOrClose2 } = useAnchorHook();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const institutionCalendar = useSelector(selectInstitutionCalendar);
  const UserGlobalSearchList = useSelector(selectUserGlobalSearch);
  const [selectedCalendar, setSelectedCalendar] = useState(Map());
  const [selectedUserType, setSelectedUserType] = useState('student');
  const [searchGlobalList, setSearchGlobalList] = useState('');
  const [selectedUser, setSelectedUser] = useState(Map());
  const [open, setOpen] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  const paperBoxSize = {
    display: 'flex',
    flexWrap: 'wrap',
    '& > :not(style)': {
      margin: '20px',
      width: '100%',
    },
  };
  const PaperHeight = 38;

  useEffect(() => {
    if (!activeInstitutionCalendar.isEmpty()) handleMenuItemClick(activeInstitutionCalendar);
  }, [activeInstitutionCalendar]);

  const handleUserMenuItemClick = (e, user) => {
    if (!user) return setSelectedUser(Map());

    const foundUser = UserGlobalSearchList.find((User) => User.get('_id') === user.value);
    setSelectedUser(foundUser);
  };
  const handleMenuItemClick = (item) => {
    const calendarId = item.get('_id', '');
    const foundCalendar = institutionCalendar.find(
      (calendar) => calendar.get('_id') === calendarId
    );
    setSelectedCalendar(foundCalendar);
    anchorEl1 && handleOpenOrClose1(null);
  };
  const handleUserTypeClick = (value) => {
    setSelectedUserType(value);
    handleOpenOrClose2(null);
    setSelectedUser(Map());
    setSearchGlobalList('');
  };
  const handleSearchData = (e, val, reason) => {
    if (['input', 'clear'].includes(reason) || !val) setSearchGlobalList(val);
    setOpen(false);
    dispatch(setData(fromJS({ UserGlobalSearch: [] })));
  };
  const handleSearch = (val) => {
    if (val) {
      setIsSearching(true);
      setOpen(true);
    }
  };
  const dispatch = useDispatch();
  const history = useHistory();
  const institutionCalendarId = selectedCalendar.get('_id', '');
  const userId = selectedUser.get('_id', '');
  const academicNo = selectedUser.get('user_id', '');
  const debounceSearchGlobalList = useDebounce(searchGlobalList, 1000, handleSearch);

  useEffect(() => {
    if (selectedUserType && searchGlobalList) {
      dispatch(getUserGlobalSearch(selectedUserType, searchGlobalList, callback));
    }
  }, [selectedUserType, debounceSearchGlobalList]); //eslint-disable-line

  const callback = () => setIsSearching(false);

  const getUserFullName = (GlobalList) => {
    const firstName = GlobalList.getIn(['name', 'first'], '');
    const middleName = GlobalList.getIn(['name', 'middle'], '');
    const lastName =
      GlobalList.getIn(['name', 'last'], '') !== '' ? GlobalList.getIn(['name', 'last'], '') : ' ';
    return jsUcfirstAll(`${firstName} ${middleName} ${lastName}`).trim();
  };

  const userOptions = useMemo(() => {
    return UserGlobalSearchList.map((user) => {
      const label = `${user.get('user_id', '')} - ${getUserFullName(user)}`;
      return Map({ label, value: user.get('_id', '') });
    }).toJS();
  }, [UserGlobalSearchList]);

  const userCode = selectedUser.get('user_id', '');
  const userName = getUserFullName(selectedUser);
  const gender = selectedUser.get('gender', '');
  const email = selectedUser.get('email', '');
  const userFirstName = selectedUser.getIn(['name', 'first'], '');
  const userLastName =
    selectedUser.getIn(['name', 'last'], '') !== ''
      ? selectedUser.getIn(['name', 'last'], '')
      : ' ';

  const handleSearchNow = () => {
    const callback = () => {
      history.push(
        `/UserGlobalSearch/StudentOrStaffList/${selectedUserType}/${eString(
          institutionCalendarId
        )}/${eString(userId)}/${eString(userCode)}/${userName}/${gender}/${email}/${eString(
          academicNo
        )}/${userFirstName}/${userLastName}`
      );
    };
    dispatch(getUserCourseDetails(institutionCalendarId, selectedUserType, userId, callback));
  };

  return (
    <div>
      <div className="bg-light globalHeight">
        <Box sx={paperBoxSize}>
          <Paper elevation={0} className="subGlobalHeight">
            <div className="GlobalOverflow d-flex justify-content-center align-items-center">
              <div>
                <div className="d-flex justify-content-center align-items-center mt-3 mb-3 text-center">
                  <img
                    src={UserGlobalIcon}
                    className="rounded UserGlobalIcon"
                    alt="UserGlobalIcon"
                  />
                </div>
                <div className="mt-2 ">
                  <div className="row justify-content-center align-items-center mb-3">
                    <FormControl sx={{ width: 280, mr: 2 }}>
                      <div className="text-dark f-14">Academic Year</div>
                      <div className="border border-secondary rounded pl-3 pr-2 announce-select-pd">
                        <div
                          className="d-flex pt-2"
                          onClick={(event) => {
                            handleOpenOrClose1(event.currentTarget);
                          }}
                        >
                          <div>
                            {selectedCalendar.isEmpty() ? (
                              <div className="digi-pointer text-muted f-14">Select Calendar</div>
                            ) : (
                              <div className="f-16">
                                {selectedCalendar.get('calendar_name', '')}
                              </div>
                            )}
                          </div>
                          <div className="ml-auto cursor">
                            <ArrowDropDownIcon />
                          </div>
                        </div>
                        <Menu
                          id="long-menu"
                          anchorEl={anchorEl1}
                          keepMounted
                          open={Boolean(anchorEl1)}
                          onClose={() => handleOpenOrClose1(null)}
                          PaperProps={{
                            style: {
                              maxHeight: PaperHeight * 10.5,
                              width: '31ch',
                            },
                          }}
                          anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'center',
                          }}
                          transformOrigin={{
                            vertical: 'top',
                            horizontal: 'center',
                          }}
                        >
                          <div>
                            <div className="mx-3 chooseUserOverflow">
                              {institutionCalendar.map((item, index) => {
                                const calendarName = item.get('calendar_name', '');
                                const selectMenuBgColor = {
                                  ...(calendarName === selectedCalendar
                                    ? {
                                        backgroundColor: '#EFF9FB',
                                      }
                                    : {}),
                                };
                                return (
                                  <div key={index}>
                                    <MenuItem
                                      onClick={() => handleMenuItemClick(item)}
                                      sx={selectMenuBgColor}
                                    >
                                      {item.get('calendar_name', '')}
                                    </MenuItem>
                                    {index < institutionCalendar.size - 1 && <Divider />}
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </Menu>
                      </div>
                    </FormControl>
                    <FormControl sx={{ width: 210 }}>
                      <div className="text-dark f-14">User Type</div>
                      <div className="border border-secondary rounded pl-3 pr-2 announce-select-pd">
                        <div
                          className="d-flex pt-2"
                          onClick={(event) => {
                            handleOpenOrClose2(event.currentTarget);
                          }}
                        >
                          <div>
                            {selectedUserType === '' ? (
                              <div className="digi-pointer text-muted f-14">Select Type</div>
                            ) : (
                              <div className="f-16">{jsUcfirst(selectedUserType)}</div>
                            )}
                          </div>
                          <div className="ml-auto cursor">
                            <ArrowDropDownIcon />
                          </div>
                        </div>
                        <Menu
                          id="long-menu"
                          anchorEl={anchorEl2}
                          keepMounted
                          open={Boolean(anchorEl2)}
                          onClose={() => handleOpenOrClose2(null)}
                          PaperProps={{
                            style: {
                              maxHeight: PaperHeight * 10.5,
                              width: '24ch',
                            },
                          }}
                          anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'center',
                          }}
                          transformOrigin={{
                            vertical: 'top',
                            horizontal: 'center',
                          }}
                        >
                          <div>
                            <div className="mx-3 chooseUserOverflow">
                              <div>
                                <MenuItem
                                  sx={{
                                    backgroundColor:
                                      selectedUserType === 'staff' ? '#EFF9FB' : 'inherit',
                                  }}
                                  onClick={() => handleUserTypeClick('staff')}
                                >
                                  Staff
                                </MenuItem>
                                <Divider />
                                <MenuItem
                                  sx={{
                                    backgroundColor:
                                      selectedUserType === 'student' ? '#EFF9FB' : 'inherit',
                                  }}
                                  onClick={() => handleUserTypeClick('student')}
                                >
                                  Student
                                </MenuItem>
                              </div>
                            </div>
                          </div>
                        </Menu>
                      </div>
                    </FormControl>
                  </div>
                  <div className="row justify-content-center mt-1">
                    <FormControl fullWidth>
                      <div className="text-dark f-14">Choose User</div>
                      <AutoComplete
                        placeholder={`Search ${
                          selectedUserType ? jsUcfirst(selectedUserType) : 'User'
                        }`}
                        value={selectedUser.isEmpty() ? '' : `${userCode} - ${userName}`}
                        isClearable={!selectedUser.isEmpty()}
                        options={userOptions}
                        onChange={handleUserMenuItemClick}
                        onInputChange={handleSearchData}
                        loading={isSearching}
                        open={open}
                        onClose={() => setOpen(false)}
                        renderOption={(props, option, { index }) => (
                          <>
                            <MenuItem {...props} key={option.id}>
                              <span>{option.label}</span>
                            </MenuItem>
                            {index < userOptions.length - 1 && <Divider />}
                          </>
                        )}
                        PaperComponent={(props) => (
                          <Paper
                            {...props}
                            sx={{ boxShadow: '0px 4px 10px rgba(17, 24, 39, 0.2)' }}
                          >
                            <div className="p-2 mx-2">{props.children}</div>
                          </Paper>
                        )}
                      />
                      {/* <div className="border border-secondary rounded pl-3 pr-2 announce-select-pd">
                        <div
                          className="d-flex pt-2"
                          onClick={(event) => {
                            handleOpenOrClose(event.currentTarget);
                          }}
                        >
                          <div>
                            {selectedUser.isEmpty() ? (
                              <div className="digi-pointer text-muted f-14">
                                Select{' '}
                                {selectedUserType !== '' ? jsUcfirst(selectedUserType) : 'User'}
                              </div>
                            ) : (
                              <div className="f-16">
                                {selectedUser.get('user_id', '')} - {getUserFullName(selectedUser)}
                              </div>
                            )}
                          </div>
                          <div className="ml-auto cursor">
                            <ArrowDropDownIcon />
                          </div>
                        </div>

                        <Popover
                          anchorEl={anchorEl}
                          keepMounted
                          open={Boolean(anchorEl)}
                          onClose={() => handleOpenOrClose(null)}
                          PaperProps={{
                            style: {
                              maxHeight: PaperHeight * 10.5,
                              width: '65ch',
                            },
                          }}
                          anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'center',
                          }}
                          transformOrigin={{
                            vertical: 'top',
                            horizontal: 'center',
                          }}
                        >
                          <div className="p-2">
                            <div className="mx-2 px-1 my-3">
                              <MaterialInput
                                elementType={'materialSearch'}
                                placeholder={`Search ${
                                  selectedUserType !== '' ? selectedUserType : 'user'
                                }`}
                                id={'#rowReverse'}
                                labelclass={'searchRight'}
                                className="remove_hover"
                                changed={(event) => {
                                  handleSearchData(event);
                                }}
                                value={searchGlobalList}
                              />
                            </div>
                            <div className="mx-2 chooseUserOverflow">
                              {UserGlobalSearchList.map((GlobalList, GlobalListIndex) => {
                                return (
                                  <div key={GlobalListIndex}>
                                    <MenuItem
                                      onClick={() => handleUserMenuItemClick(GlobalList)}
                                      sx={{
                                        backgroundColor:
                                          selectedUser &&
                                          selectedUser.get('user_id') === GlobalList.get('user_id')
                                            ? '#EFF9FB'
                                            : 'transparent',
                                      }}
                                    >
                                      <span>{GlobalList.get('user_id', '')}</span>
                                      <span className="mx-1">-</span>
                                      <span>{getUserFullName(GlobalList)}</span>
                                    </MenuItem>
                                    <Divider />
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </Popover>
                      </div> */}
                    </FormControl>
                  </div>
                  <div className="row justify-content-center mt-4">
                    <FormControl sx={{ width: 500 }}>
                      <MButton
                        clicked={handleSearchNow}
                        className="w-100"
                        disabled={!selectedCalendar.size || !selectedUserType || !selectedUser.size}
                      >
                        SEARCH NOW
                      </MButton>
                    </FormControl>
                  </div>
                </div>
              </div>
            </div>
          </Paper>
        </Box>
      </div>
    </div>
  );
}

export default UserGlobalSearch;
