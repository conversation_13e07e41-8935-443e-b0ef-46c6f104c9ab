.search_box input[type="text"] {
  padding: 8px;
  font-size: 15px;
  float: left;
  width: 75%;
  background: #ffffff;
  margin: 15px 0px 0px 0px;
  border: 0.5px solid rgba(0, 0, 0, 0.38);
  border-radius: 8px;
}

.search_icon {
  margin-left: -34px;
  margin-top: 27px;
  color: #afadac;
  font-size: 22px;
}

.search_box button:hover {
  background: #0b7dda;
}

.search_box ::placeholder {
  color: rgba(0, 0, 0, 0.38);
  opacity: 1; /* Firefox */
  font-size: 15px;
}
.f-15 {
  font-size: 15px !important;
}
.pt_23 {
  padding-top: 23px;
}
thead.th-white {
  background: #ffffff;
}
th.border_color_blue {
  border-bottom: 2px solid #007bff !important;
  color: #636262 !important;
}
.text-lightgray {
  color: #686462 !important;
}
.search_box_badge {
  font-size: 16px;
  color: blue;
  padding: 10px 15px 10px 15px;
  border-radius: 15px;
  margin: 1px 0px 5px 0px;
}
.text-checked-gray:checked {
  background-color: gray !important;
}
.text-lightgray {
  color: #8c8989 !important;
}
tr.tr-bottom-border {
  border: 1px solid #e8e8e8 !important;
}
.reviewer-clock {
  float: right;
  margin-top: -35px;
  margin-right: 5px;
  margin-left: -15px;
  color: black !important;
  font-size: 20px;
}
.reviewer_comment {
  word-break: break-word;
}
.button-shadow {
  -webkit-box-shadow: -1px 9px 5px 0px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: -1px 9px 5px 0px rgba(0, 0, 0, 0.75);
  box-shadow: -1px 5px 5px 0px rgba(0, 0, 0, 0.27);
}

.reviewer-status {
  background-color: #fff;
  text-align: left;
  padding: 12px 34px 12px 35px;
  border-radius: 7px;
  word-break: break-word;
  border: 1px solid #dadada;
}

.notification_bar {
  background-color: #fff;
  /* height: 650px; */
  width: 480px;
  /* overflow-y: scroll; */
  padding: 15px 3px 15px 3px;
  box-shadow: -7px 1px 8px -4px rgba(0, 0, 0, 0.75);
}

.deadline {
  background: rgb(185, 234, 250);
  border-radius: 8px;
  padding: 13px 5px 13px 5px;
}
.program_notification {
  background: rgba(243, 241, 241, 0.26);
  border-top: 1px solid #e0dddd;
  border-bottom: 1px solid #e0dddd;
  padding: 13px 3px 13px 3px;
}

.border_cuve.btn-sm {
  border-radius: 13px;
  padding: 2px 29px 2px 29px;
}
.border_cuve_normal.btn-sm {
  border-radius: 13px;
}

.reply_comment {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 17px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
  border: 1px solid #dadada;
}

.radio-label3 {
  padding: 0px 10px 0px 11px;
  margin-bottom: 0px;
  color: #737373;
  font-size: 13px;
}

.side_quote {
  border-left: 1px solid #a9a5a5;
  padding: 2px 0px 17px 15px;
}

.tabaligment_blue {
  color: #55a8f0 !important;
  text-align: center;
  padding: 13px 22px;
  text-decoration: none;
  font-size: 17px;
}

.tabaligment_blue:hover {
  color: #fff;
  text-decoration: none;
  border-bottom: 3px solid #007bff;
}
.tabactive_blue {
  border-bottom: 3px solid #007bff;
  padding-right: 4px;
}

.badge_width {
  padding: 13px 20px 13px 20px;
  color: #007bff;
  background-color: #e9ebec;
  border-radius: 16px;
}
.badge_active {
  border: 1px solid #007bff;
}
