import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { Button } from 'react-bootstrap';
import { compose } from 'redux';
import { connect } from 'react-redux';
import DatePicker, { registerLocale } from 'react-datepicker';
import { startOfDay, format, set, add, formatDistanceStrict } from 'date-fns';
import { t } from 'i18next';
import PropTypes, { oneOfType } from 'prop-types';
import { List, Map } from 'immutable';
import ar from 'date-fns/locale/ar';

import Loader from '../../../Widgets/Loader/Loader';
import Input from '../../../Widgets/FormElements/Input/Input';
import '../../../Assets/css/leave_management.css';
import {
  selectLeave,
  selectLocations,
  selectStudentList,
  selectStaffList,
  selectPermissionList,
  selectReportAbsenceList,
} from '../../../_reduxapi/leave_management/selectors';
import { selectUserId, selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import * as actions from '../../../_reduxapi/leave_management/actions';
import StudentModal from '../modal/StudentModal';
import StaffModal from '../modal/StaffModal';
import ApproveReportModal from '../modal/ApproveReportmodal';
import { getURLParams, eString, getLang } from '../../../utils';
registerLocale('ar', ar);

const lang = getLang();

class StudentLeaveEntry extends Component {
  constructor(props) {
    super(props);
    this.state = {
      id: '',
      startTime: '',
      endTime: '',
      DOB: '',
      reason: '',
      comment: '',
      fileUpload: '',
      leaveCategory: List(),
      onDutyCategory: List(),
      selectedLeaveCategory: Map(),
      selectedOnDutyCategory: Map(),
      selectedLeaveType: Map(),
      selectedOnDutyType: Map(),
      fromLeaveDate: '',
      toLeaveDate: '',
      showModal: false,
      selectedAttachment: null,
      selectedAttachmentUrl: null,
      approvedDate: new Date(),
      applicationDate: '',
      selectName: getURLParams('type', true) !== '' ? getURLParams('type', true) : 'permission',
      applyDate: new Date(),
      searchView1: false,
      searchView: false,
      removalIcon: false,
      removalIcon1: false,
      searchText1: null,
      name1: '',
      filename: null,
      selectedId1: 0,
      name: '',
      searchText: null,
      selectStaffId: '',
      setData: null,
      studentId: null,
      programId: getURLParams('id', true),
    };
    this.timeoutStudent = 0;
    this.timeoutStaff = 0;
  }

  componentDidMount() {
    const {
      match: {
        params: { id },
      },
    } = this.props;
    const {
      getAllPermissionList,
      getAllLeaveClassifications,
      getAbsenceReport,
      setBreadCrumbName,
    } = this.props;
    getAllPermissionList('student');
    getAllLeaveClassifications('/lms/list');
    setBreadCrumbName(t('side_nav.menus.student_register'));
    this.setState({
      id,
    });
    if (id !== 'new') {
      getAbsenceReport(id);
    }
    this.setCategory();

    if (id !== 'new') {
      this.interval = setInterval(() => {
        const { reportAbsenceList } = this.props;
        const { leaveCategory, onDutyCategory } = this.state;
        if (reportAbsenceList.size > 0) {
          this.setState({
            fromLeaveDate: new Date(reportAbsenceList.getIn([0, 'from'], '')),
            toLeaveDate: new Date(reportAbsenceList.getIn([0, 'to'], '')),
            reason: reportAbsenceList.getIn([0, 'reason'], ''),
            selectName: reportAbsenceList.getIn([0, 'type'], ''),
            selectedAttachment: reportAbsenceList.getIn([0, '_leave_reason_doc'], ''),
            selectedAttachmentUrl: reportAbsenceList.getIn([0, '_leave_reason_doc_url'], ''),
            startTime: new Date(reportAbsenceList.getIn([0, 'from'], '')),
            endTime: new Date(reportAbsenceList.getIn([0, 'to'], '')),
            applicationDate: new Date(reportAbsenceList.getIn([0, 'application_date'], '')),
            name1:
              reportAbsenceList.getIn([0, 'user_data', 0, 'name', 'first'], '') +
              reportAbsenceList.getIn([0, 'user_data', 0, 'name', 'middle'], '') +
              reportAbsenceList.getIn([0, 'user_data', 0, 'name', 'last'], ''),
            comment: reportAbsenceList.getIn([0, 'comments', 0, 'comment'], ''),
            approvedDate: new Date(reportAbsenceList.getIn([0, 'approved_by', 'date'], '')),
            name:
              reportAbsenceList.getIn([0, 'staff_data', 0, 'name', 'first'], '') +
              reportAbsenceList.getIn([0, 'staff_data', 0, 'name', 'middle'], '') +
              reportAbsenceList.getIn([0, 'staff_data', 0, 'name', 'last'], ''),
            selectStaffId: reportAbsenceList.getIn([0, 'approved_by', '_person_id'], ''),
            studentId: reportAbsenceList.getIn([0, '_user_id'], ''),
          });

          if (reportAbsenceList.getIn([0, 'type'], '') === 'leave') {
            const categoryData = leaveCategory
              .filter(
                (item) =>
                  item.get('value', '') ===
                  reportAbsenceList.getIn([0, 'leave_category', '_leave_category_id'], '')
              )
              .reduce((_, el) => el, Map());

            this.setState({
              selectedLeaveCategory: categoryData,
              selectedLeaveType: categoryData
                .get('type', List())
                .filter(
                  (item) =>
                    item.get('value', '') ===
                    reportAbsenceList.getIn([0, 'leave_type', '_leave_type_id'], '')
                )
                .reduce((_, el) => el, Map()),
            });
          } else if (reportAbsenceList.getIn([0, 'type'], '') === 'on_duty') {
            const categoryData = onDutyCategory
              .filter(
                (item) =>
                  item.get('value', '') ===
                  reportAbsenceList.getIn([0, 'leave_category', '_leave_category_id'], '')
              )
              .reduce((_, el) => el, Map());
            this.setState({
              selectedOnDutyCategory: categoryData,
              selectedOnDutyType: categoryData
                .get('type', List())
                .filter(
                  (item) =>
                    item.get('value', '') ===
                    reportAbsenceList.getIn([0, 'leave_type', '_leave_type_id'], '')
                )
                .reduce((_, el) => el, Map()),
            });
          }

          clearInterval(this.interval);
        }
      }, 500);
    }
  }

  getType = (type) => {
    const { leaveDetails } = this.props;
    return leaveDetails.get('category', List()).size > 0
      ? leaveDetails
          .get('category', List())
          .filter(
            (data) =>
              data.get('category_to', '') === 'student' &&
              data.get('isActive', false) &&
              data.get('category_type', '') === type
          )
          .map((item) => {
            return Map({
              name: item.get('category_name'),
              value: item.get('_id'),
              type:
                item.get('leave_type', List()).filter((data) => data.get('isActive', false)).size >
                0
                  ? item
                      .get('leave_type', List())
                      .filter((data) => data.get('isActive', false))
                      .map((item) => {
                        return Map({
                          name: item.get('type_name', ''),
                          value: item.get('_id'),
                          isAttachmentRequired: item.get('is_attachment_required', false),
                          isReasonRequired: item.get('is_reason_required', false),
                        });
                      })
                  : List(),
            });
          })
      : List();
  };

  setCategory = () => {
    this.catInterval = setInterval(() => {
      if (this.props.leaveDetails.get('_id', null) !== null) {
        const studentLeaveType = this.getType('leave');
        const studentOnDutyType = this.getType('on_duty');
        this.setState(
          {
            leaveCategory: studentLeaveType,
            onDutyCategory: studentOnDutyType,
          },
          () => {
            this.setState({
              selectedLeaveCategory: studentLeaveType.get(0, Map()),
              selectedOnDutyCategory: studentOnDutyType.get(0, Map()),
              selectedLeaveType: studentLeaveType.getIn([0, 'type', 0], Map()),
              selectedOnDutyType: studentOnDutyType.getIn([0, 'type', 0], Map()),
            });
          }
        );
        clearInterval(this.catInterval);
      }
    }, 500);
  };

  componentWillUnmount() {
    clearInterval(this.interval);
    clearInterval(this.catInterval);
  }

  isLeaveTypePermission() {
    return this.state.selectName === 'permission';
  }

  getTimes() {
    const { startTime, endTime } = this.state;
    return this.isLeaveTypePermission() ? startTime === '' || endTime === '' : null;
  }

  isLeaveTypeOnDuty() {
    const { fromLeaveDate, toLeaveDate } = this.state;
    return this.state.selectName !== 'permission'
      ? fromLeaveDate === '' || toLeaveDate === ''
      : null;
  }

  getList() {
    const { fromLeaveDate, toLeaveDate } = this.state;
    return this.isLeaveTypePermission() ? fromLeaveDate === '' || toLeaveDate === '' : null;
  }

  getMinMaxTime(type) {
    const { startTime } = this.state;
    const { permissions } = this.props;

    return type === 'endTime' && startTime
      ? {
          minTime: add(startTime, { hours: 1 }),
          maxTime: add(startTime, { hours: permissions.get('permission_hours', 0) }),
        }
      : {};
  }

  getMinDate(type) {
    const { fromLeaveDate } = this.state;
    if (type === 'fromLeaveDate') return startOfDay(new Date());
    return fromLeaveDate ? startOfDay(fromLeaveDate) : startOfDay(new Date());
  }

  disableSubmit() {
    const {
      name,
      name1,
      selectedAttachment,
      selectName,
      selectedOnDutyType,
      reason,
      selectedLeaveType,
    } = this.state;
    const { permissions } = this.props;
    if (
      selectName === 'permission' &&
      !selectedAttachment &&
      permissions.get('is_permission_attachment_required')
    )
      return true;
    if (name === '' || name1 === '') return true;
    if (
      selectName === 'leave' &&
      !selectedAttachment &&
      selectedLeaveType.get('isAttachmentRequired', false)
    )
      return true;
    if (selectName === 'leave' && !reason && selectedLeaveType.get('isReasonRequired', false))
      return true;
    if (selectName === 'on_duty' && !reason && selectedOnDutyType.get('isReasonRequired', false))
      return true;
    if (
      selectName === 'on_duty' &&
      !selectedAttachment &&
      selectedOnDutyType.get('isAttachmentRequired', false)
    )
      return true;

    if (this.state.approvedDate === null) return true;
    if (this.state.applicationDate === null) return true;

    if (this.isLeaveTypeOnDuty()) return true;
    if (this.getTimes()) return true;
  }

  changeHandler1 = (value, id, name, studentId) => {
    if (value) {
      this.setState({
        selectedId1: id,
        name1: name,
        studentId: studentId,
      });
    }
  };

  changeHandler = (value, id, name, staffid) => {
    if (value) {
      this.setState({
        selectedId: id,
        name: name,
        selectStaffId: staffid,
      });
    }
  };

  handleRemoveAttachment() {
    this.setState({
      selectedAttachment: null,
    });
  }

  modalOpen = () => {
    this.setState({
      searchView: !this.state.searchView,
      removalIcon: true,
      searchText: '',
    });
  };

  modalClose = () => {
    this.setState({
      searchView: !this.state.searchView,
      name: '',
      removalIcon: false,
      selectedId: 0,
      searchText: '',
    });
  };

  removeIcon = () => {
    this.setState({
      name: '',
      removalIcon: false,
      selectedId: 0,
      searchText: '',
    });
  };

  removeIcon1 = () => {
    this.setState({
      name1: '',
      removalIcon1: false,
      selectedId1: 0,
      searchText1: '',
    });
  };

  modalOpen1 = () => {
    this.setState({
      searchView1: !this.state.searchView1,
      removalIcon1: true,
      searchText1: '',
    });
  };

  modalClose1 = () => {
    this.setState({
      searchView1: !this.state.searchView1,
      name1: '',
      removalIcon1: false,
      selectedId1: 0,
      searchText1: '',
    });
  };

  handleSelect = (e, name) => {
    const {
      leaveCategory,
      onDutyCategory,
      selectedLeaveCategory,
      selectedOnDutyCategory,
    } = this.state;
    const value = e.target.value;
    if (name === 'leaveEntryName') {
      this.setState({
        selectName: value,
      });
    }
    if (name === 'selectLeaveCategory') {
      const categoryData = leaveCategory
        .filter((item) => item.get('value', '') === value)
        .reduce((_, el) => el, Map());
      this.setState({
        selectedLeaveCategory: categoryData,
        selectedLeaveType: categoryData.getIn(['type', 0], Map()),
      });
    }
    if (name === 'selectLeaveType') {
      const leaveData = selectedLeaveCategory
        .get('type', List())
        .filter((item) => item.get('value', '') === value)
        .reduce((_, el) => el, Map());
      this.setState({
        selectedLeaveType: leaveData,
      });
    }
    if (name === 'selectOnDutyCategory') {
      const categoryData = onDutyCategory
        .filter((item) => item.get('value', '') === value)
        .reduce((_, el) => el, Map());
      this.setState({
        selectedOnDutyCategory: categoryData,
        selectedOnDutyType: categoryData.getIn(['type', 0], Map()),
      });
    }
    if (name === 'selectOnDutyType') {
      const leaveData = selectedOnDutyCategory
        .get('type', List())
        .filter((item) => item.get('value', '') === value)
        .reduce((_, el) => el, Map());
      this.setState({
        selectedOnDutyType: leaveData,
      });
    }
    if (name === 'reason') {
      this.setState({ reason: e.target.value });
    }
    if (name === 'comment') {
      this.setState({ comment: e.target.value });
    }
  };

  handleSelectDOB = (d, name) => {
    if (name === 'DOB') {
      this.setState({ DOB: d });
    }
    if (name === 'applicationDate') {
      this.setState({ applyDate: d, applicationDate: d });
    }
    if (name === 'fromLeaveDate') {
      this.setState({ fromLeaveDate: d, toLeaveDate: '', startTime: '', endTime: '' });
    }
    if (name === 'toLeaveDate') {
      this.setState({ toLeaveDate: d });
    }

    if (name === 'approvedDate') {
      this.setState({ approvedDate: d });
    }
  };

  handleFileChange(files) {
    if (!files) files = [];
    this.setState({
      selectedAttachment: files.length ? files[0] : null,
    });
  }

  handleChange = (e, name) => {
    let file = e.target.files[0];
    this.setState({ fileUpload: file }, () => {});
  };

  handleSearch1 = (e) => {
    this.setState(
      {
        searchText1: e.target.value,
      },
      () => this.doSearchStudent(e)
    );
  };

  doSearchStudent = (evt) => {
    if (this.timeoutStudent) clearTimeout(this.timeoutStudent);
    this.timeoutStudent = setTimeout(() => {
      setTimeout(() => {
        this.props.getAllStudent(
          this.props.activeInstitutionCalendar.get('_id'),
          this.state.programId,
          this.state.searchText1
        );
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState(
      {
        searchText: e.target.value,
      },
      () => this.doSearchStaff(e)
    );
  };

  doSearchStaff = (evt) => {
    if (this.timeoutStaff) clearTimeout(this.timeoutStaff);
    this.timeoutStaff = setTimeout(() => {
      setTimeout(() => {
        this.props.getAllStaff('completed', this.state.searchText);
      }, 500);
    }, 500);
  };

  handleChangeData = () => {
    this.setState({ filename: null });
  };

  handleGoBack = () => {
    this.props.history.push(`/leave-management/studentleave?id=${eString(this.state.programId)}`);
  };

  handleTimeChange(value, name) {
    const { fromLeaveDate } = this.state;
    if (fromLeaveDate && value) {
      const minutes = +format(value, 'mm');
      value =
        minutes % 60 === 0
          ? set(fromLeaveDate, { hours: +format(value, 'HH'), minutes: +format(value, 'mm') })
          : '';
    }
    this.setState({
      [name]: value,
      ...(name === 'startTime' && { endTime: '' }),
    });
  }

  confirm = () => {
    const {
      selectedAttachment,
      comment,
      approvedDate,
      selectStaffId,
      reason,
      applyDate,
      selectName,
      fromLeaveDate,
      toLeaveDate,
      startTime,
      endTime,
      selectedLeaveCategory,
      selectedOnDutyCategory,
      selectedLeaveType,
      selectedOnDutyType,
    } = this.state;

    if (selectName !== 'permission') {
      var endDate = startOfDay(toLeaveDate).toISOString();
      var end = moment(endDate).format('YYYY-MM-DD 23:59:59');
      var endUnixTime = new Date(end).valueOf();
    }

    let apply_Date;
    let approveDate;

    let applicationDate = moment(applyDate).format('YYYY-MM-DD');
    apply_Date = new Date(applicationDate).valueOf();
    let approvedDates = moment(approvedDate).format('YYYY-MM-DD');
    approveDate = new Date(approvedDates).valueOf();

    const obj = {
      _institution_calendar_id: this.props.activeInstitutionCalendar.get('_id'),
      _user_id: this.state.studentId,
      user_type: 'student',
      type: selectName,
      from:
        selectName !== 'permission'
          ? new Date(startOfDay(fromLeaveDate).toISOString()).valueOf()
          : new Date(startTime.toISOString()).valueOf(),
      to: selectName !== 'permission' ? endUnixTime : new Date(endTime.toISOString()).valueOf(),
      days:
        selectName === 'permission'
          ? 1
          : parseInt(
              formatDistanceStrict(
                new Date(startOfDay(fromLeaveDate).toISOString()).valueOf(),
                new Date(endUnixTime)
              ).slice(0, 1)
            ),
      reason,
      _leave_reason_doc: selectedAttachment,
      application_date: apply_Date,
      ...(!this.isLeaveTypePermission() && {
        _leave_category_id:
          selectName === 'leave'
            ? selectedLeaveCategory.get('value', '')
            : selectName === 'on_duty'
            ? selectedOnDutyCategory.get('value', '')
            : null,
        category_name:
          selectName === 'leave'
            ? selectedLeaveCategory.get('name', '')
            : selectName === 'on_duty'
            ? selectedOnDutyCategory.get('name', '')
            : null,
      }),
      _person_id: selectStaffId,
      approved_date: approveDate,
      approved_comments: comment,
    };

    if (selectName === 'leave' && !this.isLeaveTypePermission()) {
      obj._leave_type_id = selectedLeaveType.get('value', '');
      obj.leave_type_name = selectedLeaveType.get('name', '');
    } else if (selectName === 'on_duty' && !this.isLeaveTypePermission()) {
      obj._leave_type_id = selectedOnDutyType.get('value', '');
      obj.leave_type_name = selectedOnDutyType.get('name', '');
    }
    this.setState({ showModal: true, setData: obj });
  };

  handleClick = (status) => {
    this.confirm();
    this.setState({
      showModal: false,
    });
  };

  checkIsRequired = (type = 'attachment') => {
    const { selectName, selectedOnDutyType, selectedLeaveType } = this.state;
    const { permissions } = this.props;
    if (selectName === 'permission') {
      return permissions.get(
        type === 'attachment' ? 'is_permission_attachment_required' : 'reasons',
        false
      );
    } else if (selectName === 'on_duty') {
      return selectedOnDutyType.get(
        type === 'attachment' ? 'isAttachmentRequired' : 'isReasonRequired',
        false
      );
    } else if (selectName === 'leave') {
      return selectedLeaveType.get(
        type === 'attachment' ? 'isAttachmentRequired' : 'isReasonRequired',
        false
      );
    }
  };
  TIMES = [
    { type: 'startTime', label: t('events.start_time') },
    { type: 'endTime', label: t('events.end_time') },
  ];
  LeaveEntry = [
    [t('leaveManagement.tabs.Permission'), 'permission'],
    [t('leaveManagement.tabs.Leave'), 'leave'],
    [t('leaveManagement.tabs.On-Duty'), 'on_duty'],
  ];
  render() {
    const studentModalData = {
      searchView: this.state.searchView1,
      searchText: this.state.searchText1,
      changeHandler: this.changeHandler1.bind(this),
      handleSearch: this.handleSearch1.bind(this),
      studentList: this.props.studentList,
      modalOpen: this.modalOpen1,
      modalClose: this.modalClose1,
      selectedId: this.state.selectedId1,
    };

    const StaffModalData = {
      searchView: this.state.searchView,
      searchText: this.state.searchText,
      changeHandler: this.changeHandler.bind(this),
      handleSearch: this.handleSearch.bind(this),
      staffList: this.props.staffList,
      modalOpen: this.modalOpen,
      modalClose: this.modalClose,
      selectedId: this.state.selectedId,
    };

    const {
      selectName,
      id,
      leaveCategory,
      selectedLeaveCategory,
      selectedLeaveType,
      onDutyCategory,
      selectedOnDutyCategory,
      selectedOnDutyType,
      fromLeaveDate,
      toLeaveDate,
    } = this.state;
    let LeaveEntry_1 = this.LeaveEntry;
    if (id !== 'new' && selectName !== '') {
      LeaveEntry_1 = this.LeaveEntry.filter((item) => item[1] === selectName);
    }
    const { activeInstitutionCalendar } = this.props;
    return (
      <div className="main pt-3 pb-5 ">
        <Loader isLoading={this.state.isLoading} />
        <div className="container-fluid ">
          <div className="p-2">
            <div className="d-flex justify-content-between">
              <div className="">
                <b className="pr-3">
                  {' '}
                  <i
                    className={`fa fa-arrow-${lang === 'ar' ? 'right' : 'left'} remove_hover  f-16`}
                    aria-hidden="true"
                    onClick={this.handleGoBack}
                  ></i>
                </b>
                <b className="mb-2 f-16"> {t('leaveManagement.enterStudentAbsence')}</b>
              </div>
              <div className="">
                <Button
                  variant={this.disableSubmit() ? 'secondary' : 'primary'}
                  className="f-14"
                  disabled={this.disableSubmit()}
                  onClick={this.confirm}
                >
                  {t('settings.submit')}
                </Button>{' '}
              </div>
              {this.state.showModal && (
                <ApproveReportModal
                  closed={this.handleClick.bind(this)}
                  reason={this.state.reason}
                  category={selectedLeaveCategory.get('name', '')}
                  type={selectedLeaveType.get('name', '')}
                  componentname={'leaveentry'}
                  requestbody={this.state.setData}
                  selectName={this.state.selectName}
                  Ondutytype={selectedOnDutyCategory.get('name', '')}
                  id={this.state.id}
                  programId={this.state.programId}
                />
              )}
            </div>

            <div className="pl-35 pt-3">
              <div className="row pb-2">
                <div className="col-md-2">
                  <label className=""> {t('leaveManagement.selectStudent')} </label>
                </div>
                <div className="col-md-3">
                  <div className="sb-example-2 mt--15 ">
                    <div className="search">
                      <input
                        type="text"
                        className="searchTerm"
                        placeholder={t('user_management.Search')}
                        value={this.state.name1}
                        maxLength={10}
                      />

                      <button type="submit" className="searchButton">
                        {id === 'new' && (
                          <>
                            {!this.state.removalIcon1 ? (
                              <i
                                className="fa fa-search"
                                onClick={() => {
                                  this.modalOpen1();
                                  this.props.getAllStudent(
                                    this.props.activeInstitutionCalendar.get('_id'),
                                    this.state.programId
                                  );
                                }}
                              ></i>
                            ) : (
                              <i className="fa fa-times" onClick={() => this.removeIcon1()}></i>
                            )}
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              {this.state.searchView1 && <StudentModal studentModalData={studentModalData} />}

              <div className="row">
                <div className="col-md-2">
                  <p className="pt-4 mb-0"> {t('leaveManagement.selectLeave')} </p>
                </div>

                <div className="col-md-7 pt-4 text-left">
                  <Input
                    elementType={'radio'}
                    elementConfig={LeaveEntry_1}
                    className={'form-radio1 radio1'}
                    selected={this.state.selectName}
                    labelclass="radio-label2"
                    onChange={(e) => this.handleSelect(e, 'leaveEntryName')}
                    removeLabel={'label-remove1'}
                  />
                </div>
              </div>

              {selectName === 'permission' && (
                <React.Fragment>
                  <div className="row pt-4">
                    <div className="col-md-2">
                      <label className="mt-2">{t('Date')}</label>
                    </div>
                    <div className="col-md-2">
                      <DatePicker
                        onChange={(date) => this.handleSelectDOB(date, 'fromLeaveDate')}
                        selected={this.state.fromLeaveDate}
                        dateFormat="d MMM yyyy"
                        className={'form-control customeDatepick'}
                        style={{ width: '100%' }}
                        showMonthDropdown
                        showYearDropdown
                        yearDropdownItemNumber={15}
                        value={this.state.fromLeaveDate}
                        minDate={
                          activeInstitutionCalendar.get('start_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('start_date', ''))
                            : ''
                        }
                        maxDate={
                          activeInstitutionCalendar.get('end_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('end_date', ''))
                            : ''
                        }
                        popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                        {...(lang === 'ar' && { locale: 'ar' })}
                      />
                      <i
                        className="fa fa-calendar-o leave_caleder"
                        aria-hidden="true"
                        style={{ top: '0.6em' }}
                      ></i>
                    </div>
                  </div>
                  <>
                    {this.TIMES.map((time, i) => (
                      <div className="row pt-4" key={i}>
                        <div className="col-md-2">
                          <label className="mt-2">{t(time.label)}</label>
                        </div>
                        <div className="col-md-2">
                          <DatePicker
                            placeholderText={t('leaveManagement.Choose_time')}
                            {...this.getMinMaxTime(time.type)}
                            minDate={this.getMinDate(time.type)}
                            selected={this.state[time.type]}
                            onChange={(d) => this.handleTimeChange(d, time.type)}
                            dateFormat="h:mm aa"
                            timeFormat="h:mm aa"
                            className="form-control customeDatepick"
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={60}
                            timeCaption={time.label}
                            popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                            {...(lang === 'ar' && { locale: 'ar' })}
                          />
                          <i
                            className="fa fa-clock-o leave_caleder"
                            aria-hidden="true"
                            style={{ top: '0.7em' }}
                          ></i>
                        </div>
                      </div>
                    ))}{' '}
                  </>
                </React.Fragment>
              )}

              {(selectName === 'leave' || selectName === 'on_duty') && (
                <>
                  {selectName === 'leave' ? (
                    <>
                      <div className="row pt-4">
                        <div className="col-md-2">
                          <label className="mt-2">{t('leaveManagement.leaveCategory')}</label>
                        </div>
                        <div className="col-md-3">
                          <div className="mt--25">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: leaveCategory.toJS(),
                              }}
                              name={selectedLeaveCategory.get('name', '')}
                              value={selectedLeaveCategory.get('value', '')}
                              changed={(e) => this.handleSelect(e, 'selectLeaveCategory')}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="row pt-4">
                        <div className="col-md-2">
                          <label className="mt-2">{t('leaveManagement.leave Type')}</label>
                        </div>
                        <div className="col-md-3">
                          <div className="mt--25">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: selectedLeaveCategory.get('type', List()).toJS(),
                              }}
                              name={selectedLeaveType.get('name', '')}
                              value={selectedLeaveType.get('value', '')}
                              changed={(e) => this.handleSelect(e, 'selectLeaveType')}
                            />
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="row pt-4">
                        <div className="col-md-2">
                          <label className="mt-2">{t('leaveManagement.onDutyCategory')}</label>
                        </div>
                        <div className="col-md-3">
                          <div className="mt--25">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: onDutyCategory.toJS(),
                              }}
                              name={selectedOnDutyCategory.get('name', '')}
                              value={selectedOnDutyCategory.get('value', '')}
                              changed={(e) => this.handleSelect(e, 'selectOnDutyCategory')}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="row pt-4">
                        <div className="col-md-2">
                          <label className="mt-2">{t('leaveManagement.onDutyType')}</label>
                        </div>
                        <div className="col-md-3">
                          <div className="mt--25">
                            <Input
                              elementType={'floatingselect'}
                              elementConfig={{
                                options: selectedOnDutyCategory.get('type', List()).toJS(),
                              }}
                              name={selectedOnDutyType.get('name', '')}
                              value={selectedOnDutyType.get('value', '')}
                              changed={(e) => this.handleSelect(e, 'selectOnDutyType')}
                            />
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                  <div className="row pt-4">
                    <div className="col-md-2">
                      <label className="mt-2">{t('global_configuration.from')}</label>
                    </div>
                    <div className="col-md-2">
                      <DatePicker
                        minDate={
                          activeInstitutionCalendar.get('start_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('start_date', ''))
                            : ''
                        }
                        maxDate={
                          activeInstitutionCalendar.get('end_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('end_date', ''))
                            : ''
                        }
                        onChange={(date) => this.handleSelectDOB(date, 'fromLeaveDate')}
                        selected={this.state.fromLeaveDate}
                        dateFormat="d MMM yyyy"
                        className={'form-control customeDatepick'}
                        style={{ width: '100%' }}
                        showMonthDropdown
                        showYearDropdown
                        yearDropdownItemNumber={15}
                        value={this.state.fromLeaveDate}
                        popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                        {...(lang === 'ar' && { locale: 'ar' })}
                      />
                      <i
                        className="fa fa-calendar-o leave_caleder"
                        aria-hidden="true"
                        style={{ top: '0.6em' }}
                      ></i>
                    </div>
                  </div>

                  <div className="row pt-4">
                    <div className="col-md-2">
                      <label className="mt-2">{t('program_calendar.to')}</label>
                    </div>
                    <div className="col-md-2">
                      <DatePicker
                        //minDate={this.getMinDate('toLeaveDate')}
                        minDate={
                          fromLeaveDate !== ''
                            ? new Date(fromLeaveDate)
                            : activeInstitutionCalendar.get('start_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('start_date', ''))
                            : ''
                        }
                        maxDate={
                          activeInstitutionCalendar.get('end_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('end_date', ''))
                            : ''
                        }
                        onChange={(date) => this.handleSelectDOB(date, 'toLeaveDate')}
                        selected={toLeaveDate}
                        dateFormat="d MMM yyyy"
                        className={'form-control customeDatepick'}
                        style={{ width: '100%' }}
                        showMonthDropdown
                        showYearDropdown
                        yearDropdownItemNumber={15}
                        value={toLeaveDate}
                        popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                        {...(lang === 'ar' && { locale: 'ar' })}
                      />
                      <i
                        className="fa fa-calendar-o leave_caleder"
                        aria-hidden="true"
                        style={{ top: '0.6em' }}
                      ></i>
                    </div>
                  </div>
                </>
              )}
              <div className="row pt-2 mb-2">
                <div className="col-md-2">
                  <label className="mt-27">
                    {t('leaveManagement.Reason')}{' '}
                    {this.checkIsRequired('reason') && <span className="text-red">*</span>}
                  </label>
                </div>
                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    value={this.state.reason}
                    floatingLabel={''}
                    placeholder={t('leaveManagement.typeHere')}
                    changed={(e) => this.handleSelect(e, 'reason')}
                  />
                </div>
              </div>

              <div className="row pt-4 mb-2">
                <div className="col-md-2">
                  <label className="mt-10">
                    {t('leaveManagement.attachment')}{' '}
                    {this.checkIsRequired('attachment') && <span className="text-red">*</span>}
                  </label>
                </div>
                <div className="col-md-2">
                  <p className="mb-0">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary w-100 import-padding border-radious-8"
                    >
                      {t('browse')}
                      <input
                        id="fileUpload"
                        type="file"
                        accept="application/pdf,image/jpeg, image/png"
                        value=""
                        onChange={(e) => this.handleFileChange(e.target.files)}
                      />
                    </label>
                  </p>
                  <p className="f-11 text-center">
                    {' '}
                    {t('student_grouping.accepted_format')} [ PDF, JPG, PNG ]{' '}
                  </p>
                </div>

                {this.state.selectedAttachment !== null &&
                  typeof this.state.selectedAttachment === 'object' && (
                    <p className="f-11 mt-3">
                      {this.state.selectedAttachment.name}
                      <b className="padding-left-5">
                        <i
                          className="fa fa-remove remove_hover f-16"
                          aria-hidden="true"
                          onClick={this.handleRemoveAttachment.bind(this)}
                        ></i>
                      </b>
                      {this.state.selectedAttachment.size > 2000000 && (
                        <b className="text-red">
                          <br />
                          {t('leaveManagement.exceedsLimit')}
                        </b>
                      )}
                    </p>
                  )}
                {this.state.selectedAttachment !== 'null' &&
                  typeof this.state.selectedAttachment === 'string' && (
                    <p className="f-11 mt-3">
                      <a
                        href={this.state.selectedAttachmentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {this.state.selectedAttachment.split('/').slice(-1)}
                      </a>

                      <b className="padding-left-5">
                        <i
                          className="fa fa-remove remove_hover f-16"
                          aria-hidden="true"
                          onClick={this.handleRemoveAttachment.bind(this)}
                        ></i>
                      </b>
                    </p>
                  )}
              </div>
              {this.state.filename !== null && (
                <div className="bg-lightdark border-radious-8 w-75 ">
                  <b className=" pl-2 pr-2 f-14" onClick={this.handleChangeData}>
                    {this.state.filename?.length > 15
                      ? this.state.filename.substring(0, 4) + '...'
                      : this.state.filename}{' '}
                    <i
                      className="fa fa-times remove_hover import_remove_icon"
                      aria-hidden="true"
                    ></i>
                  </b>
                </div>
              )}

              <div className="row pt-2">
                <div className="col-md-2">
                  <label className="mt-1">{t('leaveManagement.applicationDate')}</label>
                </div>

                {this.state.id === 'new' && (
                  <div className="col-md-2">
                    <DatePicker
                      onChange={(d) => this.handleSelectDOB(d, 'applicationDate')}
                      dateFormat="d MMM yyyy"
                      selected={this.state.applyDate}
                      className={'form-control customeDatepick'}
                      style={{ width: '100%' }}
                      showMonthDropdown
                      showYearDropdown
                      yearDropdownItemNumber={15}
                      value={this.state.applyDate}
                      minDate={
                        activeInstitutionCalendar.get('start_date', '') !== ''
                          ? new Date(activeInstitutionCalendar.get('start_date', ''))
                          : ''
                      }
                      maxDate={
                        activeInstitutionCalendar.get('end_date', '') !== ''
                          ? new Date(activeInstitutionCalendar.get('end_date', ''))
                          : ''
                      }
                      popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                      {...(lang === 'ar' && { locale: 'ar' })}
                    />
                    <i
                      className="fa fa-calendar-o leave_caleder"
                      aria-hidden="true"
                      style={{ top: '0.6em' }}
                    ></i>
                  </div>
                )}

                {this.state.id !== 'new' && (
                  <div className="col-md-2">
                    <DatePicker
                      onChange={(d) => this.handleSelectDOB(d, 'applicationdate')}
                      dateFormat="d MMM yyyy"
                      selected={this.state.applicationDate}
                      className={'form-control customeDatepick'}
                      style={{ width: '100%' }}
                      showMonthDropdown
                      showYearDropdown
                      yearDropdownItemNumber={15}
                      value={this.state.applicationDate}
                      minDate={
                        activeInstitutionCalendar.get('start_date', '') !== ''
                          ? new Date(activeInstitutionCalendar.get('start_date', ''))
                          : ''
                      }
                      maxDate={
                        activeInstitutionCalendar.get('end_date', '') !== ''
                          ? new Date(activeInstitutionCalendar.get('end_date', ''))
                          : ''
                      }
                      popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                      {...(lang === 'ar' && { locale: 'ar' })}
                    />
                    <i
                      className="fa fa-calendar-o leave_caleder"
                      aria-hidden="true"
                      style={{ top: '0.6em' }}
                    ></i>
                  </div>
                )}
              </div>

              <hr className="mb-5 mt-5" />
              <div className="row pt-2 mb-2">
                <div className="col-md-2">
                  <label className="mt-2">{t('leaveManagement.approvedBy')} </label>
                </div>
                <div className="col-md-3">
                  <div className="sb-example-2 ">
                    <div className="search">
                      <input
                        type="text"
                        className="searchTerm"
                        placeholder={t('user_management.Search')}
                        value={this.state.name}
                        maxLength={10}
                      />
                      <button type="submit" className="searchButton">
                        {!this.state.removalIcon ? (
                          <i
                            className="fa fa-search"
                            onClick={() => {
                              this.modalOpen();
                              this.props.getAllStaff('completed');
                            }}
                          ></i>
                        ) : (
                          <i className="fa fa-times" onClick={() => this.removeIcon()}></i>
                        )}
                      </button>
                    </div>
                  </div>

                  {this.state.searchView && <StaffModal StaffModalData={StaffModalData} />}
                </div>
              </div>
              <div className="row pt-4">
                <div className="col-md-2">
                  <label className="mt-2">{t('Date')}</label>
                </div>
                <div className="col-md-2">
                  <DatePicker
                    placeholderText=""
                    selected={this.state.approvedDate}
                    onChange={(date) => this.handleSelectDOB(date, 'approvedDate')}
                    value={this.state.approvedDate}
                    dateFormat="d MMM yyyy"
                    className={'form-control customeDatepick'}
                    showMonthDropdown
                    showYearDropdown
                    yearDropdownItemNumber={15}
                    minDate={
                      activeInstitutionCalendar.get('start_date', '') !== ''
                        ? new Date(activeInstitutionCalendar.get('start_date', ''))
                        : ''
                    }
                    maxDate={
                      activeInstitutionCalendar.get('end_date', '') !== ''
                        ? new Date(activeInstitutionCalendar.get('end_date', ''))
                        : ''
                    }
                    popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                    {...(lang === 'ar' && { locale: 'ar' })}
                  />
                  <i
                    className="fa fa-calendar-o leave_caleder"
                    aria-hidden="true"
                    style={{ top: '0.6em' }}
                  ></i>
                </div>
              </div>

              <div className="row pt-2 mb-2">
                <div className="col-md-2">
                  <label className="mt-27">{t('leaveManagement.commentOptional')} </label>
                </div>
                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    value={this.state.comment}
                    floatingLabel={''}
                    placeholder={t('leaveManagement.typeHere')}
                    changed={(e) => this.handleSelect(e, 'comment')}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = function (state) {
  return {
    leave: selectLeave(state),
    leaveDetails: selectLocations(state),
    studentList: selectStudentList(state),
    staffList: selectStaffList(state),
    userId: selectUserId(state),
    permissions: selectPermissionList(state),
    reportAbsenceList: selectReportAbsenceList(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

StudentLeaveEntry.propTypes = {
  leaveDetails: PropTypes.instanceOf(Map),
  permissions: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  studentList: oneOfType([PropTypes.instanceOf(Map), PropTypes.instanceOf(List)]),
  staffList: PropTypes.instanceOf(Map),
  reportAbsenceList: PropTypes.instanceOf(List),
  getAllPermissionList: PropTypes.func,
  getAbsenceReport: PropTypes.func,
  getAllLeaveClassifications: PropTypes.func,
  getAllStudent: PropTypes.func,
  getAllStaff: PropTypes.func,
  match: PropTypes.object,
  history: PropTypes.object,
  setBreadCrumbName: PropTypes.func,
};

export default compose(withRouter, connect(mapStateToProps, actions))(StudentLeaveEntry);
