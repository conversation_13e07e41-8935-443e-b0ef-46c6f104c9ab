import React from 'react';
import Button from '@mui/material/Button';
import './Input.css';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import PropTypes from 'prop-types';

const { palette } = createTheme();
const { augmentColor } = palette;
const createColor = (mainColor) => augmentColor({ color: { main: mainColor } });
const theme = createTheme({
  palette: {
    yellow: createColor('#F59E0B'),
    gray: createColor('#9CA3AF'),
    red: createColor('#FF0000'),
    bgNone: createColor('#9CA3AF'),
    whiteButton: createColor('#fff'),
    warning: createColor('#FDE68A'),
    blue: createColor('#147AFC'),
    green: createColor('#BBF7D0'),
    darkGray: createColor('#374151'),
    act: createColor('#147AFC'),
    skyBlueButton: {
      main: '#EFF9FB',
      contrastText: '#147AFC',
    },
    action: {
      disabledBackground: '#E5E7EB !important',
      disabled: '#9CA3AF',
    },
    white: {
      main: '#fff !important',
      contrastText: '#374151',
    },
  },
});
function Buttons({
  disabled = false,
  clicked = () => {},
  children = '',
  className = '',
  variant = 'contained',
  size = 'medium',
  color = 'primary',
  fullWidth = false,
  startIcon = '',
  endIcon = '',
  component = '',
  type = 'button',
  ...props
}) {
  return (
    <ThemeProvider theme={theme}>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={clicked}
        color={color}
        disabled={disabled}
        fullWidth={fullWidth}
        startIcon={startIcon}
        endIcon={endIcon}
        component={component}
        type={type}
        {...props}
      >
        {children}
      </Button>
    </ThemeProvider>
  );
}

Buttons.propTypes = {
  disabled: PropTypes.bool,
  clicked: PropTypes.func,
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.array, PropTypes.object]),
  className: PropTypes.string,
  variant: PropTypes.string,
  size: PropTypes.string,
  color: PropTypes.string,
  fullWidth: PropTypes.bool,
  startIcon: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  endIcon: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  component: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  type: PropTypes.string,
  sx: PropTypes.object,
};

export default Buttons;
