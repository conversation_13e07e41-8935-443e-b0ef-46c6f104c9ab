import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker';
import { PickersDay } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { styled } from '@mui/material/styles';
import { fromJS, isList, List, Map } from 'immutable';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Breadcrumbs,
  IconButton,
  Link,
  Menu,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import MButton from 'Widgets/FormElements/material/Button';
import AddIcon from '@mui/icons-material/Add';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import moment from 'moment';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import TodayIcon from '@mui/icons-material/Today';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import LocalLibraryOutlinedIcon from '@mui/icons-material/LocalLibraryOutlined';
import PresentationIcon from 'Assets/presentation.svg';
import RuleIcon from '@mui/icons-material/Rule';
import rightArrowImg from 'Assets/rightArrow.svg';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { MobileTimePicker } from '@mui/x-date-pickers/MobileTimePicker';
import { tooltipClasses } from '@mui/material/Tooltip';
import {
  addDays,
  differenceInMinutes,
  eachDayOfInterval,
  endOfWeek,
  format,
  isAfter,
  isBefore,
  isSameMonth,
  min,
} from 'date-fns';
import { capitalize, getEnvWeekStart, indVerRename, jsUcfirstAll, levelRename } from 'utils';
import { formattedFullName } from 'Modules/ReportsAndAnalytics/utils';
import { useDispatch } from 'react-redux';
import { resetMessage } from '_reduxapi/course_scheduling/action';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import HomeIcon from '@mui/icons-material/Home';

const addScheduleMenuSx = {
  paper: {
    sx: {
      minWidth: '170px',
    },
  },
};
const addScheduleMenuItemSx = {
  color: '#374151',
  fontSize: 14,
};
const staffStatusSx = {
  padding: '4px 8px',
  fontSize: '10px',
  textTransform: 'uppercase',
  fontWeight: 500,
};
export const primaryStatusSx = {
  ...staffStatusSx,
  color: '#4338CA',
  backgroundColor: '#E0E7FF',
};
export const secondaryStatusSx = {
  ...staffStatusSx,
  color: '#6B7280',
  backgroundColor: '#F3F4F6',
};
const accordionSx = {
  '&:before': {
    display: 'none',
  },
};
const accordionSummarySx = {
  padding: '6px 16px',
  minHeight: 'auto',
  borderRadius: '8px',
  backgroundColor: '#F3F4F6',
  '& .MuiAccordionSummary-content': {
    margin: 0,
  },
  '& .MuiAccordionSummary-expandIconWrapper': {
    color: '#6B7280',
  },
};
const accordionDetailsSx = {
  padding: 0,
};
const staffDetailsSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '4px 16px',
  '&:not(:last-child)': {
    borderBottom: '1px solid #D1D5DB',
  },
};
const addScheduleButtonSx = {
  minHeight: '40px',
  boxShadow: 'none',
  borderRadius: '8px',
};
export const buttonSx = {
  minWidth: 120,
  minHeight: 40,
  boxShadow: 'none',
};
const stepperIconBoxSx = {
  borderRadius: '50%',
};
const stepperIconBoxActiveSx = {
  border: '5px solid #E1F5FA',
};
const stepperIconSx = {
  padding: '8px',
  width: 42,
  height: 42,
  borderRadius: '50%',
  border: '1px solid #E5E7EB',
  backgroundColor: '#F3F4F6',
};
const stepperIconActiveSx = {
  borderColor: '#0064C8',
  backgroundColor: '#F7FCFD',
  boxShadow: '0px 1px 3px 0px #11182733',
};
const stepperCompletedIconSx = {
  fontSize: '42px',
  color: '#15803D',
};
export const datepickerSx = {
  '& .MuiInputBase-input': {
    padding: '8.5px 12px',
    color: '#374151',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#D1D5DB',
    borderRadius: '8px',
  },
  '& .MuiButtonBase-root': {
    color: '#0064C8',
    '&.Mui-disabled': {
      color: '#0064C8',
      opacity: 0.5,
    },
  },
  '& .MuiInputAdornment-root': {
    marginLeft: 0,
  },
};
const mobileTimepickerSx = {
  '& .MuiTimePickerToolbar-hourMinuteLabel': {
    '& .MuiButtonBase-root .PrivatePickersToolbarText-root': {
      padding: '0 6px',
      lineHeight: '65px',
      '&.Mui-selected': {
        backgroundColor: 'rgb(25, 118, 210,.2)',
        color: 'rgb(25, 118, 210)',
      },
    },
    '& .MuiTimePickerToolbar-separator': { lineHeight: '65px' },
  },
  '& .MuiTimePickerToolbar-ampmSelection': {
    border: '1px solid #D1D5DB',
    borderRadius: '4px',
    '& .MuiButtonBase-root': {
      fontWeight: 400,
      '& .MuiTimePickerToolbar-ampmLabel': {
        padding: '4px 8px',
        fontSize: 15,
        fontWeight: '400',
        width: '40px',
        '&.Mui-selected': {
          backgroundColor: '#1976d2',
          color: '#fff',
        },
      },
    },
    '& .MuiButtonBase-root:nth-child(1) .MuiTimePickerToolbar-ampmLabel': {
      borderRadius: '4px 4px 0 0',
    },
    '& .MuiButtonBase-root:nth-child(2) .MuiTimePickerToolbar-ampmLabel': {
      borderRadius: '0 0 4px 4px',
    },
  },
};
export const checkboxSx = {
  padding: 0,
  marginRight: '8px',
  color: '#6B7280',
  '&.Mui-checked': {
    color: '#0064C8',
    '&.Mui-disabled': {
      opacity: 0.5,
    },
  },
  '&.MuiCheckbox-indeterminate': {
    color: '#0064C8',
  },
};
export const paginationSx = {
  '& .MuiPaginationItem-root.Mui-selected': {
    backgroundColor: 'transparent',
    border: '1px solid #00000061',
  },
  '& .MuiPaginationItem-root.Mui-selected:hover': {
    backgroundColor: 'transparent',
  },
};
const scheduleDotsBoxSx = {
  position: 'absolute',
  bottom: 4,
  width: '100%',
  display: 'flex',
  justifyContent: 'center',
  gap: '2px',
};
const scheduleDotsSx = {
  width: 5,
  height: 5,
  borderRadius: '50%',
  backgroundColor: '#1976d2',
};
const weekArrowsSx = {
  padding: '4px',
  color: '#4B5563',
  backgroundColor: '#F3F4F6',
};
const activeBreadcrumbSx = {
  fontSize: 12,
  fontWeight: 500,
  color: '#374151',
  textDecoration: 'underline',
};

const scheduleSteps = [
  {
    label: 'Step 1',
    title: 'Scheduling Sessions',
    icon: <TodayIcon />,
  },
  {
    label: 'Step 2',
    title: 'Analyzing Issues',
    icon: <RuleIcon />,
  },
];

const CustomPickersDay = styled(PickersDay, {
  shouldForwardProp: (prop) => prop !== 'isMarked',
})(({ isMarked }) => ({
  borderRadius: '4px',
  ...(isMarked && {
    borderBottom: `2px solid #F59E0B`,
    color: '#9CA3AF',
  }),
  '&.Mui-selected': {
    backgroundColor: '#374151',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#34495e',
    },
  },
  '&.Mui-selected:focus': {
    backgroundColor: '#374151',
  },
}));

const CustomTimePicker = styled(MobileTimePicker, {
  shouldForwardProp: (prop) => prop !== 'textAlign',
})(({ textAlign = 'left' }) => ({
  '& .MuiInputBase-input': {
    padding: '8.5px 12px',
    color: '#374151',
    textAlign,
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#D1D5DB',
    borderRadius: '8px',
  },
  '& .MuiSvgIcon-root': {
    color: '#0064C8',
    cursor: 'pointer',
  },
  '& .MuiInputBase-root.Mui-disabled .MuiSvgIcon-root': {
    opacity: 0.5,
    cursor: 'default',
  },
}));

const constructSubjects = (scheduleData) => {
  return scheduleData
    .get('subjects', List())
    .map((sub) => sub.get('subject_name', ''))
    .join(', ');
};

const constructStaffs = (scheduleData) => {
  return scheduleData
    .get('staffs', List())
    .map((staff) => {
      return formattedFullName(staff.get('staff_name', Map()).toJS()).replace(/\s+/g, ' ').trim();
    })
    .join(', ');
};

export const CustomCalendar = ({
  eventDates,
  scheduleCount,
  value,
  onChange,
  onMonthChange,
  minDate,
  maxDate,
}) => {
  const renderDay = (day, selectedDate, pickersDayProps) => {
    const { key, ...otherProps } = pickersDayProps;
    const formatted = day.format('YYYY-MM-DD');
    const isMarked = eventDates.includes(formatted);
    const noOfSchedules = scheduleCount.get(formatted, 0);
    const noOfDots = noOfSchedules ? (noOfSchedules > 3 ? 3 : noOfSchedules) : 0;

    return (
      <Box position="relative">
        <CustomPickersDay {...otherProps} key={key} isMarked={isMarked} />

        {noOfDots > 0 && (
          <Box sx={scheduleDotsBoxSx}>
            {Array(noOfDots)
              .fill(0)
              .map((_, index) => (
                <Box key={index} sx={scheduleDotsSx} />
              ))}
          </Box>
        )}
      </Box>
    );
  };

  const handleChange = (newValue) => {
    const dateValue = new Date(newValue);
    onChange(format(dateValue, 'yyyy-MM-dd'));
  };

  const handleMonthChange = (newValue) => {
    const dateValue = new Date(newValue);
    onMonthChange(format(dateValue, 'yyyy-MM'));
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <StaticDatePicker
        displayStaticWrapperAs="desktop"
        renderDay={renderDay}
        value={value}
        onChange={handleChange}
        onMonthChange={handleMonthChange}
        dayOfWeekFormatter={(day) => day.toUpperCase()}
        views={['day']}
        minDate={minDate}
        maxDate={maxDate}
        components={{
          LeftArrowButton: (params) => (
            <IconButton {...params} sx={weekArrowsSx}>
              <ChevronLeftIcon />
            </IconButton>
          ),
          RightArrowButton: (params) => (
            <IconButton {...params} sx={weekArrowsSx}>
              <ChevronRightIcon />
            </IconButton>
          ),
        }}
        showDaysOutsideCurrentMonth
      />
    </LocalizationProvider>
  );
};
CustomCalendar.propTypes = {
  eventDates: PropTypes.instanceOf(List),
  scheduleCount: PropTypes.instanceOf(Map),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
  onChange: PropTypes.func,
  onMonthChange: PropTypes.func,
  minDate: PropTypes.string,
  maxDate: PropTypes.string,
};

export const MuiSelect = ({ options, value, changed, placeholder = 'Select' }) => {
  return (
    <Box sx={{ mt: '5px' }}>
      <MaterialInput
        elementType="MuiSelect"
        size="small"
        placeholder={placeholder}
        elementConfig={options}
        value={value}
        changed={changed}
        displayEmpty
      />
    </Box>
  );
};
MuiSelect.propTypes = {
  options: PropTypes.instanceOf(List),
  value: PropTypes.string,
  changed: PropTypes.func,
  placeholder: PropTypes.string,
};

export const SearchInput = ({ value, changed, handleClose }) => {
  return (
    <TextField
      type="text"
      size="small"
      placeholder="Search course/topic"
      value={value}
      onChange={changed}
      InputProps={{
        startAdornment: <SearchIcon className="mr-1 text-NewLightgray" />,
        endAdornment: (
          <IconButton sx={{ p: '2px' }} onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        ),
      }}
      fullWidth
    />
  );
};
SearchInput.propTypes = {
  value: PropTypes.string,
  changed: PropTypes.func,
  handleClose: PropTypes.func,
};

export const AddScheduleButton = ({ onClick }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (e) => setAnchorEl(e.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const handleMenuClick = (type) => {
    onClick(type);
    handleClose();
  };

  return (
    <>
      <MButton
        startIcon={<AddIcon />}
        endIcon={open ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
        clicked={handleClick}
        sx={addScheduleButtonSx}
      >
        Add Schedule
      </MButton>
      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={addScheduleMenuSx}
        keepMounted
      >
        <MenuItem sx={addScheduleMenuItemSx} onClick={() => handleMenuClick('single')}>
          Single session
        </MenuItem>
        <MenuItem sx={addScheduleMenuItemSx} onClick={() => handleMenuClick('multiple')}>
          Multiple session
        </MenuItem>
      </Menu>
    </>
  );
};
AddScheduleButton.propTypes = {
  onClick: PropTypes.func,
};

export const getFirstLastDate = (date = new Date()) => {
  const current = new Date(date);
  const diff = current.getDate() - current.getDay();
  const firstDate = new Date(current.setDate(diff));
  const lastDate = new Date(current.setDate(firstDate.getDate() + 6));

  return { firstDate: firstDate.toUTCString(), lastDate: lastDate.toUTCString() };
};

export const formatStartEndDate = (dateArr) => {
  const startDate = dateArr.get('firstDate', '');
  const endDate = dateArr.get('lastDate', '');
  if (!startDate || !endDate) return '';

  return moment(startDate).format('D MMM YYYY') + ' - ' + moment(endDate).format('D MMM YYYY');
};

export const SecondaryText = ({ text, fontSize = 12 }) => {
  return (
    <Typography fontSize={fontSize} color="#6B7280">
      {text}
    </Typography>
  );
};
SecondaryText.propTypes = {
  text: PropTypes.string,
  fontSize: PropTypes.number,
};

export const ScheduleLabel = ({ label }) => {
  return <SecondaryText text={label} fontSize={14} />;
};
ScheduleLabel.propTypes = {
  label: PropTypes.string,
};

export const ScheduleStatusIcon = ({ scheduled, errorInfo }) => {
  return scheduled ? (
    <CustomTooltip title={errorInfo} placement="top">
      <ErrorIcon fontSize="small" sx={{ color: '#DC2626' }} />
    </CustomTooltip>
  ) : (
    <CheckCircleIcon fontSize="small" sx={{ color: '#15803D' }} />
  );
};
ScheduleStatusIcon.propTypes = {
  scheduled: PropTypes.bool,
  errorInfo: PropTypes.string,
};

export const StaffStatus = ({ primary }) => {
  return primary ? (
    <Box sx={primaryStatusSx}>Primary</Box>
  ) : (
    <Box sx={secondaryStatusSx}>Secondary</Box>
  );
};
StaffStatus.propTypes = {
  primary: PropTypes.bool,
};

export const SelectedStaffs = ({ staffs, showScheduledStatus, updateStaffs }) => {
  const handleDelete = (staff) => {
    const staffId = staff.get('value');
    const newValue = staffs.filter((s) => s.get('value') !== staffId).map((s) => s.get('value'));
    updateStaffs(newValue);
  };

  return (
    <Accordion elevation={0} sx={accordionSx} defaultExpanded disableGutters>
      <AccordionSummary expandIcon={<ExpandMoreIcon fontSize="small" />} sx={accordionSummarySx}>
        <Typography component="span" color="#6B7280">
          Selected staffs
        </Typography>
      </AccordionSummary>
      <AccordionDetails sx={accordionDetailsSx}>
        {staffs.map((staff, index) => (
          <Box key={index} sx={staffDetailsSx}>
            <div>
              <Typography color="#374151">{staff.get('name')}</Typography>
              <div className="text-capitalize">
                <SecondaryText text={`${staff.get('userId')} • ${staff.get('gender')}`} />
              </div>
            </div>

            <Box display="flex" alignItems="center" gap={1}>
              {/* <StaffStatus primary={staff.get('primary')} /> */}
              {showScheduledStatus && (
                <ScheduleStatusIcon
                  scheduled={staff.get('scheduled')}
                  errorInfo={staff.get('errorInfo')}
                />
              )}
              <IconButton sx={{ p: '2px', color: '#6B7280' }} onClick={() => handleDelete(staff)}>
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
        ))}
      </AccordionDetails>
    </Accordion>
  );
};
SelectedStaffs.propTypes = {
  staffs: PropTypes.instanceOf(List),
  showScheduledStatus: PropTypes.bool,
  updateStaffs: PropTypes.func,
};

export const SessionDetails = ({ scheduleData }) => {
  const programId = scheduleData.get('_program_id', '');
  const programName = scheduleData.get('program_name', '');
  const year = scheduleData.get('year_no', '').replace('year', 'Year ');
  const level = levelRename(scheduleData.get('level_no', ''), programId);
  const type = scheduleData.get('type');
  const session = scheduleData.get('session', Map());
  const deliverySymbol = session.get('delivery_symbol', '');
  const deliveryNo = session.get('delivery_no', '');
  const deliveryType = session.get('session_type', '');
  const sessionTopic = session.get('session_topic', '');
  const title =
    type === 'support_session'
      ? `Support Session - ${scheduleData.get('title', '')} - ${scheduleData.get('sub_type', '')}`
      : type === 'event'
      ? `Event - ${scheduleData.get('title', '')} - ${scheduleData.get('sub_type', '')}`
      : `${deliverySymbol}${deliveryNo} - ${sessionTopic}`;
  const courseCode = scheduleData.get('course_code', '');
  const courseName = scheduleData.get('course_name', '');
  const startDateAndTime = scheduleData.get('scheduleStartDateAndTime', '');
  const endDateAndTime = scheduleData.get('scheduleEndDateAndTime', '');
  const formattedDate = moment(startDateAndTime).format('ddd, MMM D YYYY');
  const formattedStartTime = moment(startDateAndTime).format('h:mm A');
  const formattedEndTime = moment(endDateAndTime).format('h:mm A');
  const duration = differenceInMinutes(new Date(endDateAndTime), new Date(startDateAndTime));
  const subjects = constructSubjects(scheduleData);
  const infra = scheduleData.get('infra_name', '');
  const mode = indVerRename(capitalize(scheduleData.get('mode', '')), programId);
  const maleCount = scheduleData.get('maleCount', 0);
  const femaleCount = scheduleData.get('femaleCount', 0);
  const staffs = constructStaffs(scheduleData);

  const getConcatenatedData = (data) => {
    return data.filter((item) => item).join(' - ');
  };

  return (
    <Box color="#4B5563">
      <Typography fontSize={20} color="#374151">
        {title}
      </Typography>
      <SecondaryText text={`${programName} • ${year} • ${level} • ${courseCode} - ${courseName}`} />

      <Box display="flex" alignItems="center" gap={1} mt={2} mb={1}>
        <TodayIcon fontSize="small" />
        <Typography variant="body2">
          {formattedDate}, {formattedStartTime} - {formattedEndTime} • {duration} mins
        </Typography>
      </Box>

      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <AssignmentOutlinedIcon fontSize="small" />
        <Typography variant="body2">{getConcatenatedData([subjects, deliveryType])}</Typography>
      </Box>

      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <LocationOnOutlinedIcon fontSize="small" />
        <Typography variant="body2">{getConcatenatedData([infra, mode])}</Typography>
      </Box>

      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <LocalLibraryOutlinedIcon fontSize="small" />
        <Typography variant="body2">
          {maleCount} male <span className="ml-2">{femaleCount} female</span>
        </Typography>
      </Box>

      <Box display="flex" alignItems="center" gap={1}>
        <img src={PresentationIcon} />
        <Typography variant="body2">{staffs}</Typography>
      </Box>
    </Box>
  );
};
SessionDetails.propTypes = {
  scheduleData: PropTypes.instanceOf(Map),
};

export const ScheduleStepper = ({ currentStep }) => {
  return (
    <Box display="flex" alignItems="center" gap={2}>
      {scheduleSteps.map((step, index) => {
        const isActive = currentStep === index + 1;
        const isCompleted = currentStep > index + 1;
        return (
          <React.Fragment key={index}>
            <Box display="flex" alignItems="center" color={isActive ? '#0064C8' : '#6B7280'}>
              {isCompleted ? (
                <CheckCircleIcon sx={stepperCompletedIconSx} />
              ) : (
                <Box sx={{ ...stepperIconBoxSx, ...(isActive && stepperIconBoxActiveSx) }}>
                  <Box sx={{ ...stepperIconSx, ...(isActive && stepperIconActiveSx) }}>
                    {step.icon}
                  </Box>
                </Box>
              )}

              <Box ml={1}>
                <Typography variant="caption" textTransform="uppercase">
                  {step.label}
                </Typography>
                <Typography variant="body2" fontWeight={500}>
                  {step.title}
                </Typography>
              </Box>
            </Box>

            {index !== scheduleSteps.length - 1 && (
              <div>
                <img src={rightArrowImg} />
              </div>
            )}
          </React.Fragment>
        );
      })}
    </Box>
  );
};
ScheduleStepper.propTypes = {
  currentStep: PropTypes.number,
};

export const getInitialDate = ({ minDate, maxDate, validateBy = 'date' }) => {
  const today = moment();
  if (minDate && today.isBefore(minDate, validateBy)) return moment(minDate);
  if (maxDate && today.isAfter(maxDate, validateBy)) return moment(maxDate);
  return today;
};

export const MuiDatePicker = ({ value, onChange, disabled, minDate, maxDate }) => {
  const dispatch = useDispatch();

  const handleChange = (newValue) => {
    const dateValue = new Date(newValue);
    if (isInvalidTimestamp(dateValue)) {
      if (value) onChange('');
      return;
    }

    const dateToCheck = moment(dateValue);
    if (
      !minDate ||
      !maxDate ||
      dateToCheck.isBetween(moment(minDate), moment(maxDate), 'day', '[]')
    )
      onChange(dateToCheck.format('YYYY-MM-DD'));
    else {
      const errorMsg = `Select date between ${formatDate(minDate)} and ${formatDate(maxDate)}`;
      dispatch(resetMessage(errorMsg));
      onChange('');
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        value={value}
        onChange={handleChange}
        renderInput={(params) => (
          <TextField
            {...params}
            sx={datepickerSx}
            inputProps={{
              ...params.inputProps,
              placeholder: 'DD / MM / YYYY',
            }}
          />
        )}
        disabled={disabled}
        inputFormat="DD / MM / YYYY"
        minDate={minDate}
        maxDate={maxDate}
        defaultCalendarMonth={getInitialDate({ minDate, maxDate, validateBy: 'month' })}
      />
    </LocalizationProvider>
  );
};
MuiDatePicker.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  minDate: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.string]),
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.string]),
};

export const weeks = fromJS([
  { name: 'Sun', value: 'sunday', fullName: 'Sunday' },
  { name: 'Mon', value: 'monday', fullName: 'Monday' },
  { name: 'Tue', value: 'tuesday', fullName: 'Tuesday' },
  { name: 'Wed', value: 'wednesday', fullName: 'Wednesday' },
  { name: 'Thu', value: 'thursday', fullName: 'Thursday' },
  { name: 'Fri', value: 'friday', fullName: 'Friday' },
  { name: 'Sat', value: 'saturday', fullName: 'Saturday' },
]);

export const MuiTimePicker = ({ value, onChange, disabled, minTime }) => {
  const dispatch = useDispatch();

  const handleChange = (newValue) => {
    let dateValue = new Date(newValue);
    if (isInvalidTimestamp(dateValue)) {
      if (value) onChange('');
      return;
    }

    dateValue = dateValue.setSeconds(0);
    const timeToCheck = moment(dateValue);
    if (!minTime || timeToCheck.isAfter(moment(minTime), 'time')) onChange(dateValue);
    else {
      dispatch(resetMessage('End time should be greater than start time'));
      onChange('');
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TimePicker
        value={value}
        onChange={handleChange}
        renderInput={(params) => (
          <TextField
            {...params}
            sx={datepickerSx}
            inputProps={{
              ...params.inputProps,
              placeholder: 'HH : MM AM',
            }}
          />
        )}
        disabled={disabled}
        inputFormat="hh : mm A"
      />
    </LocalizationProvider>
  );
};
MuiTimePicker.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  minTime: PropTypes.instanceOf(Date),
};

export const MuiMobileTimePicker = ({
  value,
  onChange,
  disabled,
  minTime,
  showIcon,
  textAlign,
}) => {
  const dispatch = useDispatch();
  const [timeState, setTimeState] = useState('');

  useEffect(() => {
    setTimeState(value);
  }, [value]);

  const handleChange = (newValue) => {
    let dateValue = new Date(newValue);
    if (isInvalidTimestamp(dateValue)) return;

    const timeToCheck = moment(dateValue).minutes();
    if (timeToCheck % 5 !== 0) return dispatch(resetMessage('Minutes must be multiple of 5'));

    setTimeState(dateValue);
  };

  const handleAccept = (newValue) => {
    let dateValue = new Date(newValue);
    if (isInvalidTimestamp(dateValue)) {
      if (value) onChange('');
      return;
    }

    dateValue = dateValue.setSeconds(0);
    const timeToCheck = moment(dateValue);
    if (!minTime || timeToCheck.isAfter(moment(minTime), 'time')) onChange(dateValue);
    else {
      dispatch(resetMessage('End time should be greater than start time'));
      onChange('');
      setTimeState('');
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <CustomTimePicker
        value={timeState}
        onChange={handleChange}
        onAccept={handleAccept}
        renderInput={(params) => (
          <TextField
            {...params}
            InputProps={{
              ...params.InputProps,
              ...(showIcon && { endAdornment: <AccessTimeIcon /> }),
            }}
            inputProps={{ ...params.inputProps, placeholder: 'HH : MM AM' }}
          />
        )}
        disabled={disabled}
        inputFormat="hh : mm A"
        {...(textAlign && { textAlign })}
        minutesStep={5}
        rifmFormatter={(val = '') => {
          // separate numbers and letters
          const numbers = val.replace(/[^0-9]/g, '');
          const letters = val.replace(/[^a-zA-Z]/g, '').toUpperCase();

          // hours and minutes
          let hh = numbers.slice(0, 2).padEnd(2, '_');
          let mm = numbers.slice(2, 4).padEnd(2, '_');

          // AM/PM handling
          let amPm = '';
          if (letters.startsWith('A')) amPm = 'AM';
          if (letters.startsWith('P')) amPm = 'PM';

          return `${hh} : ${mm} ${amPm || 'AM'}`;
        }}
        sx={mobileTimepickerSx}
      />
    </LocalizationProvider>
  );
};
MuiMobileTimePicker.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  minTime: PropTypes.instanceOf(Date),
  showIcon: PropTypes.bool,
  textAlign: PropTypes.string,
};

export const getDateAndTimeString = (dateString, timeString) => {
  const formattedTime = moment(timeString).format('HH:mm');
  return new Date(`${dateString}T${formattedTime}`).toISOString();
};

export const CustomTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(() => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: '#1F2937',
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#1F2937',
    color: '#FFFFFFDE',
  },
}));

export const ScheduledInfo = ({ title }) => {
  return (
    <Box>
      <Typography fontSize={12} fontWeight={600}>
        {title}
      </Typography>
      <Typography fontSize={12}>Support_Session-EEE for 9 AM to 12 PM</Typography>
      <Typography fontSize={12} color="#D1D5DB">
        Medical Testing
        <span className="mx-1">•</span>
        Yr 4<span className="mx-1">•</span>
        Lvl 8<span className="mx-1">•</span>
        Regular - OPHT 4082
      </Typography>
    </Box>
  );
};
ScheduledInfo.propTypes = {
  title: PropTypes.string,
};

export const ScheduledInfoTooltip = ({ title }) => {
  return (
    <CustomTooltip title={title} placement="top">
      <ErrorIcon sx={{ fontSize: 16, mb: '2px' }} />
    </CustomTooltip>
  );
};
ScheduledInfoTooltip.propTypes = {
  title: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
};

export const limitOptions = [10, 25, 50, 100];

export const PreviewDetailRow = ({ label, value }) => {
  return (
    <Box display="flex" alignItems="start">
      <Box width={110} display="flex" alignItems="center" justifyContent="space-between" pr="8px">
        <ScheduleLabel label={label} />
        <ScheduleLabel label=":" />
      </Box>
      <Box flex={1}>
        <Typography variant="body2">{value}</Typography>
      </Box>
    </Box>
  );
};
PreviewDetailRow.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
};

export const isInvalidTimestamp = (dateValue) => {
  const timestamp = Date.parse(dateValue);
  return isNaN(timestamp);
};

export const getSlotTimeObject = (timeValue) => {
  if (!moment(timeValue).isValid()) return {};

  const formattedTime = moment(timeValue).format('h-m-A');
  const [hour, minute, format] = formattedTime.split('-');
  return { hour: Number(hour), minute: Number(minute), format };
};

export const getOccurrenceDateRange = (scheduleState, multiScheduleDetailsRef) => {
  if (scheduleState.get('rangeMethod') === 'byDate')
    return { startDate: scheduleState.get('startDate'), endDate: scheduleState.get('endDate') };

  const startWeek = scheduleState.get('startWeek', 0);
  const endWeek = scheduleState.get('endWeek', 0);
  const weekStartDate = multiScheduleDetailsRef.current?.getWeekDate('currentStart', startWeek);
  const weekEndDate = multiScheduleDetailsRef.current?.getWeekDate('currentEnd', endWeek);

  // const startDate = new Date(courseDetails.get('start_date', new Date()));
  // const endDate = new Date(courseDetails.get('end_date', new Date()));
  // const weekStartDate = addDays(startDate, (startWeek - 1) * 7);
  // let weekEndDate = addDays(startDate, endWeek * 7 - 1);
  // weekEndDate = min([weekEndDate, endDate]);

  return {
    startDate: format(weekStartDate, 'yyyy-MM-dd'),
    endDate: format(weekEndDate, 'yyyy-MM-dd'),
  };
};

export const concatenateSubjects = (subjects) => {
  return subjects.map((sub) => sub.get('subject_name', '')).join(', ');
};

export const postScheduleKeys = {
  single: {
    subjectId: 'subjectId',
    subjectName: 'subjectName',
    studentGroupId: 'groupId',
    studentGroupName: 'groupName',
    studentGroupNo: 'groupNo',
    sessionGroup: 'sessionGroup',
    sessionGroupId: 'sessionGroupId',
    sessionGroupName: 'groupName',
    sessionGroupNo: 'groupNo',
  },
  multiple: {
    subjectId: '_subject_id',
    subjectName: 'subject_name',
    studentGroupId: 'group_id',
    studentGroupName: 'group_name',
    studentGroupNo: 'group_no',
    sessionGroup: 'session_group',
    sessionGroupId: 'session_group_id',
    sessionGroupName: 'group_name',
    sessionGroupNo: 'group_no',
  },
};

export const getCurrentDate = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const now = new Date();

  if (isSameMonth(start, now) || isSameMonth(end, now)) return format(now, 'yyyy-MM-dd');

  const days = eachDayOfInterval({ start, end });
  return format(days[3], 'yyyy-MM-dd');
};

export const getFullName = (user) => {
  const firstName = user.getIn(['name', 'first']);
  const middleName = user.getIn(['name', 'middle']);
  const lastName = user.getIn(['name', 'last']);
  const fullName = [firstName, middleName, lastName].filter((item) => item).join(' ');
  return jsUcfirstAll(fullName);
};

export const filterScheduled = (options, selected) => {
  return options.filter((option) => {
    const isSelected = isList(selected)
      ? selected.includes(option.get('value'))
      : selected === option.get('value');
    return isSelected && option.get('scheduled');
  });
};

export const getScheduledOptions = (options, value) => {
  const scheduled = filterScheduled(options, value);
  return scheduled.map((option) => option.get('name', ''));
};

export const ScheduledSelectionStatus = ({ scheduledList }) => {
  const title = `${scheduledList.join(', ')} already scheduled`;
  return (
    <CustomTooltip title={title} placement="top">
      <ErrorIcon sx={{ color: '#DC2626', mb: '8px' }} />
    </CustomTooltip>
  );
};
ScheduledSelectionStatus.propTypes = {
  scheduledList: PropTypes.instanceOf(List),
};

export const getWeekDateRange = (startDate, endDate) => {
  // const weekStartDate = addDays(startDate, (weekNo - 1) * 7);
  // let weekEndDate = addDays(weekStartDate, 6);
  // weekEndDate = min([weekEndDate, endDate]);
  // return { start: format(weekStartDate, 'dd/MM/yyyy'), end: format(weekEndDate, 'dd/MM/yyyy') };

  const weekStart = getEnvWeekStart();
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const options = { weekStartsOn: days.indexOf(weekStart) };
  const ranges = [];

  let currentStart = startDate;
  let currentEnd;
  while (isBefore(currentStart, endDate) || formatDate(currentStart) === formatDate(endDate)) {
    // find end of the week containing currentStart
    const weekEnd = endOfWeek(currentStart, options);

    // current week should end either at week's end or at the overall end date
    currentEnd = min([weekEnd, endDate]);

    ranges.push({
      start: formatDate(currentStart),
      end: formatDate(currentEnd),
      currentStart,
      currentEnd,
    });

    // next week starts the day after currentEnd
    currentStart = addDays(currentEnd, 1);
  }

  return ranges;
};

export const getFirstValue = (options) => options?.getIn([0, 'value'], '') || '';

export const filterValidOptions = (options) => {
  return options.filter((option) => !option.get('disabled'));
};

const formatDate = (dateValue) => moment(dateValue).format('DD/MM/YYYY');

const to24HourString = (timeObject) => {
  const { hour, minute, format } = timeObject.toJS();
  let hr = hour;

  if (format === 'AM') {
    if (hr === 12) hr = 0;
  } else if (format === 'PM') {
    if (hr !== 12) hr += 12;
  }

  const hrStr = String(hr).padStart(2, '0');
  const minStr = String(minute).padStart(2, '0');
  return `${hrStr}:${minStr}`;
};

export const getExtraCurricularActivities = (extraCurricularData, dateRange) => {
  if (extraCurricularData.isEmpty() || dateRange.isEmpty()) return [];

  let activities = [];
  extraCurricularData.forEach((item) => {
    let currentDate = new Date(dateRange.get('firstDate'));
    const endDate = new Date(dateRange.get('lastDate'));
    const days = item.get('days', List());

    while (!isAfter(currentDate, endDate)) {
      const currentDay = format(currentDate, 'EEEE').toLowerCase();

      if (days.includes(currentDay)) {
        const dateStr = format(currentDate, 'yyyy-MM-dd');
        activities.push({
          title: item.get('title', ''),
          start: new Date(`${dateStr}T${to24HourString(item.get('startTime', Map()))}:00`),
          end: new Date(`${dateStr}T${to24HourString(item.get('endTime', Map()))}:00`),
          className: 'extra-curricular-col',
        });
      }

      currentDate = addDays(currentDate, 1);
    }
  });

  return activities;
};

export const CustomBreadcrumbs = ({ breadcrumbList }) => {
  return (
    <Breadcrumbs
      separator=">"
      aria-label="breadcrumb"
      sx={{ fontSize: 12, '& .MuiBreadcrumbs-separator': { margin: '0 4px' } }}
    >
      <Link color="inherit" href="/" sx={{ display: 'flex', alignItems: 'center' }}>
        <HomeIcon fontSize="small" />
      </Link>

      {breadcrumbList.map((item, index) => {
        const isLast = index === breadcrumbList.length - 1;
        return isLast ? (
          <Typography key={index} sx={activeBreadcrumbSx}>
            {item.name}
          </Typography>
        ) : (
          <Link
            key={index}
            underline="hover"
            color="inherit"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              item.onClick();
            }}
          >
            {item.name}
          </Link>
        );
      })}
    </Breadcrumbs>
  );
};
