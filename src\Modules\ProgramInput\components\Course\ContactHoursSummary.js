import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';

import Warning from '../../../../Assets/alert5.png';
import { t } from 'i18next';
import { indVerRename } from 'utils';

function ContactHoursSummary({ course, sessionFlow, programId }) {
  const summary = [];
  const sessionTypes = course.get('credit_hours', List());
  sessionTypes.forEach((c, i) => {
    const creditHours = Number(c.get('credit_hours', '0') || '0');
    const contactHours = Number(c.get('contact_hours', '0') || '0') * creditHours;
    if (contactHours <= 0) return;
    const sessionTypeName = indVerRename(c.get('type_name', ''), programId);
    const sFlow = sessionFlow.filter((e) => e.get('_session_id') === c.get('_session_id'));
    const durationPerContactHour = Number(c.get('duration_per_contact_hour', '0') || '0');
    const checkDurationPerContactHour = durationPerContactHour === 0 ? 60 : durationPerContactHour;

    const totalDuration =
      sFlow.reduce((acc, e) => acc + Number(e.get('duration', '0') || '0'), 0) /
      checkDurationPerContactHour;

    const achievedHours = Number(
      totalDuration.toFixed(totalDuration === 0 || totalDuration % 1 === 0 ? 0 : 2)
    );
    summary.push(
      <div key={`summary-${i}`} className={`pr-2 ${i !== 0 ? 'border-left pl-2' : ''}`}>
        <span className="f-14">
          {`${sessionTypeName} - ${achievedHours}/${contactHours} ${t('constant.hrs')} `}
          {contactHours !== achievedHours && <img alt="" src={Warning} className="pl-2 mt--2" />}
        </span>
      </div>
    );
  });
  return summary;
}

ContactHoursSummary.propTypes = {
  course: PropTypes.instanceOf(Map),
  sessionFlow: PropTypes.instanceOf(List),
  programId: PropTypes.string,
};

export default ContactHoursSummary;
