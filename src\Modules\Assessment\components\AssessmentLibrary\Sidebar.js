import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, fromJS } from 'immutable';
import SettingsIcon from '@mui/icons-material/Settings';
import { groupArray, removeURLParams, ucFirst, eString, getURLParams } from '../../../../utils';
import { Years } from '../../../../constants';
import { CheckPermission } from 'Modules/Shared/Permissions';

function Sidebar({ history, assessmentDashboardDetails, callTermFunction }) {
  const { location } = history;
  const { pathname } = location;
  function goToAssessmentPlanning() {
    history.push(pathname + `/planning${removeURLParams(location, ['year', 'level'])}`);
  }

  function goToDashboard() {
    history.push(
      `/assessment-management/assessment_details${removeURLParams(location, [
        'term',
        'year',
        'level',
      ])}`
    );
  }

  const formatYearLevel = useCallback((yearLevel) => {
    const yearLevelArray = groupArray(yearLevel.toJS(), 'year');
    const pushArray = [];
    Object.keys(yearLevelArray).forEach(function (key) {
      pushArray.push({ year: key, level: yearLevelArray[key] });
    });
    return fromJS(pushArray);
  }, []);
  const [expanded, setExpanded] = useState([]);
  const [firstLoad, setFirstLoad] = useState(true);
  useEffect(() => {
    if (firstLoad && assessmentDashboardDetails.getIn(['0', 'term'], '') !== '') {
      setExpanded([assessmentDashboardDetails.getIn(['0', 'term'], '')]);
      setFirstLoad(false);
    }
  }, [assessmentDashboardDetails]); //eslint-disable-line
  const handleChange = (id) => {
    if (expanded.includes(id)) {
      const filteredArray = expanded.filter((item) => item !== id);
      setExpanded(filteredArray);
    } else {
      const updatedArray = [...expanded, id];
      setExpanded(updatedArray);
    }
  };

  function urlFilter({ term = '', year = '', level = '' }) {
    history.replace(
      `/assessment-management/assessment_details${removeURLParams(location, [
        'term',
        'year',
        'level',
      ])}${term !== '' ? `&term=${eString(term)}` : ``}${
        year !== '' ? `&year=${eString(year)}` : ``
      }${level !== '' ? `&level=${eString(level)}` : ``}`
    );
  }
  const term = getURLParams('term', true);
  const year = getURLParams('year', true);
  const levels = getURLParams('level', true);
  return (
    <div className="pi_sidebar bg-white pi_sidebar_right_border">
      {assessmentDashboardDetails &&
        assessmentDashboardDetails.size > 0 &&
        assessmentDashboardDetails.map((dashboard, index) => {
          const yearLevelArray = formatYearLevel(dashboard.get('yearLevel', List()));
          return (
            <React.Fragment key={index}>
              <div className={`d-flex justify-content-between subject_hover_pi`}>
                <div
                  className="d-flex w-100"
                  onClick={() => {
                    callTermFunction(dashboard.get('term', ''));
                    handleChange(dashboard.get('term', ''));
                    urlFilter({ term: dashboard.get('term', '') });
                  }}
                >
                  <p
                    className={`mb-0 text-left ${
                      (term === '' && index === 0) || term === dashboard.get('term', '')
                        ? 'AssessmentActive'
                        : ''
                    }`}
                  >
                    {ucFirst(dashboard.get('term', ''))}
                  </p>
                </div>
                <p className="mb-0 mr-1 remove_hover">
                  <i
                    className={
                      expanded.includes(dashboard.get('term', ''))
                        ? 'fa fa-caret-up'
                        : 'fa fa-caret-down'
                    }
                    aria-hidden="true"
                  ></i>
                </p>
              </div>
              {expanded.includes(dashboard.get('term', '')) && (
                <div className="d-block bg-mainBackground">
                  <>
                    <div
                      className={`innerNav remove_hover ${year === 'assess' ? 'active' : ''}`}
                      onClick={() => {
                        goToDashboard();
                        urlFilter({ term: dashboard.get('term', ''), year: 'assess' });
                      }}
                    >
                      <p className="mb-0 ">Program Assessments</p>
                    </div>
                    {yearLevelArray.size > 0 &&
                      yearLevelArray.map((yearLevel, yIndex) => {
                        return (
                          <React.Fragment key={`year-${yIndex}`}>
                            <div
                              className={`innerNav remove_hover ${
                                year === yearLevel.get('year') ? 'active' : ''
                              }`}
                              onClick={() => {
                                handleChange(dashboard.get('term', '') + yearLevel.get('year'));
                                urlFilter({
                                  term: dashboard.get('term', ''),
                                  year: yearLevel.get('year'),
                                });
                              }}
                            >
                              <div className={`d-flex justify-content-between`}>
                                <div className="d-flex w-100 remove_hover">
                                  <p className="mb-0 text-left">
                                    {Years[yearLevel.get('year').replace('year', '')].name}
                                  </p>
                                </div>
                                <p className="mb-0 mr-1">
                                  <i
                                    className={
                                      expanded.includes(
                                        dashboard.get('term', '') + yearLevel.get('year')
                                      )
                                        ? 'fa fa-caret-up'
                                        : 'fa fa-caret-down'
                                    }
                                    aria-hidden="true"
                                  ></i>
                                </p>
                              </div>
                            </div>
                            {expanded.includes(dashboard.get('term', '') + yearLevel.get('year')) &&
                              yearLevel.get('level', List()).map((level, lIndex) => {
                                return (
                                  <div
                                    className={`innerNav remove_hover ${
                                      levels === level.get('level', '') ? 'active' : ''
                                    }`}
                                    key={`level-${lIndex}`}
                                    onClick={() => {
                                      urlFilter({
                                        term: dashboard.get('term', ''),
                                        year: yearLevel.get('year'),
                                        level: level.get('level', ''),
                                      });
                                    }}
                                  >
                                    <div className={`d-flex justify-content-between`}>
                                      <div className="d-flex w-100">
                                        <p className="mb-0 text-left">{level.get('level', '')}</p>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                          </React.Fragment>
                        );
                      })}
                  </>

                  {CheckPermission(
                    'tabs',
                    'Assessment Management',
                    'Assessment Library',
                    '',
                    'Assessment Plan',
                    'View'
                  ) && (
                    <div
                      className={`innerNav d-flex align-items-center justify-content-between remove_hover ${
                        pathname.includes('/planning') ? 'active' : ''
                      }`}
                      onClick={goToAssessmentPlanning}
                    >
                      <p className="mb-0">Assessment Plan</p>
                      <SettingsIcon />
                    </div>
                  )}
                </div>
              )}
            </React.Fragment>
          );
        })}
    </div>
  );
}

Sidebar.propTypes = {
  history: PropTypes.object,
  assessmentDashboardDetails: PropTypes.instanceOf(List),
  callTermFunction: PropTypes.func,
};

export default Sidebar;
