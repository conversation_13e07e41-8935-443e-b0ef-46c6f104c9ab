import React, { useState, forwardRef, useEffect, useImperativeHandle } from 'react';
import Table from '@mui/material/Table';
import TextField from '@mui/material/TextField';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableFooter from '@mui/material/TableFooter';
import Paper from '@mui/material/Paper';
import PropTypes from 'prop-types';
import AddIcon from '@mui/icons-material/Add';
import InfoIcon from '@mui/icons-material/Info';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { useDispatch, useSelector } from 'react-redux';
import { selectAttendanceLmsConfig } from '_reduxapi/leave_management/selectors';
import { List, Map } from 'immutable';
import isEqual from 'lodash/fp/isEqual';
import {
  getAttendanceConfig,
  setData,
  updateAttendanceConfig,
} from '_reduxapi/leave_management/actions';
import TableRowLateConfig from './TableRowLateConfig';

var rowCount = 0;
const LateConfigurationTable = forwardRef(({ mode, setMode }, ref) => {
  const [range, setRange] = useState(List());
  const reducerData = useSelector(selectAttendanceLmsConfig);
  const globalLateConfig = reducerData.getIn(['globalLateConfig', 'lateConfig'], Map());
  const lateConfigData = reducerData.getIn(['getAttendanceData', 'lateConfig', 0], Map());
  const dispatch = useDispatch();

  function handleField({ index, key, e }) {
    const value = e.target.value;
    setRange((prev) => prev.setIn([index, key], value));
  }

  function handleDelete(index) {
    setRange((prev) => prev.filter((_, i) => i !== index));
  }

  useEffect(() => {
    dispatch(getAttendanceConfig());
  }, []); //eslint-disable-line

  useEffect(() => {
    if (reducerData.isEmpty()) return;
    setMode(lateConfigData.get('lateType', ''));
    setRange(lateConfigData.get('range', List()));
  }, [reducerData]); //eslint-disable-line

  function onSaveClicked() {
    if (isEqual(range.toJS(), lateConfigData.get('range', List()).toJS())) {
      return dispatch(setData(Map({ message: 'you should change something for update' })));
    }
    const set = {
      shortCode: [],
      lateLabel: [],
    };
    if (mode === 'auto') {
      set.lateLabel.push(globalLateConfig.get('label', ''));
    }
    const maxMinuteRange = [];
    for (let i = 0; i < range.size; i++) {
      const eachRange = range.get(i, Map());

      const late_label = eachRange.get('lateLabel', '').toLowerCase().trim();
      const short_code = eachRange.get('short_code', '').toLowerCase().trim();
      const noOfLate = eachRange.get('noOfLate', '').toString().toLowerCase().trim();
      const noOfAbsent = eachRange.get('noOfAbsent', '').toString().toLowerCase().trim();
      if (late_label === '') {
        return dispatch(
          setData(Map({ message: `Late Label is not allowed as Empty in Row ${i + 1}` }))
        );
      }
      if (noOfLate === '') {
        return dispatch(
          setData(Map({ message: `No Of Late is not allowed as Empty in Row ${i + 1}` }))
        );
      }
      if (noOfAbsent === '') {
        return dispatch(
          setData(Map({ message: `No Of Absent is not allowed as Empty in Row ${i + 1}` }))
        );
      }
      if (
        mode === 'auto' &&
        eachRange.get('startRange', '').toString().toLowerCase().trim() === ''
      ) {
        return dispatch(
          setData(Map({ message: `start range is not allowed as Empty in Row ${i + 1}` }))
        );
      }
      if (mode === 'auto' && eachRange.get('endRange', '').toString().toLowerCase().trim() === '') {
        return dispatch(
          setData(Map({ message: `end range is not allowed as Empty in Row ${i + 1}` }))
        );
      }
      if (mode === 'manual' && short_code === '') {
        return dispatch(
          setData(Map({ message: `short code is not allowed as Empty in Row ${i + 1}` }))
        );
      }
      if (set.lateLabel.includes(late_label)) {
        return dispatch(
          setData(Map({ message: `Duplicate Late Label is not allowed in Row ${i + 1}` }))
        );
      }
      if (mode === 'manual' && set.shortCode.includes(short_code)) {
        return dispatch(
          setData(Map({ message: `Duplicate Short code is not allowed in Row ${i + 1}` }))
        );
      }
      if (
        mode === 'auto' &&
        (eachRange.get('startRange', 0) > globalLateConfig.get('minuteRange', 0) ||
          eachRange.get('endRange', 0) > globalLateConfig.get('minuteRange', 0))
      ) {
        return dispatch(
          setData(
            Map({
              message: `Minutes range should be less than from Maximum minute range (${globalLateConfig.get(
                'minuteRange',
                0
              )}) in Row ${i + 1}`,
            })
          )
        );
      }
      if (mode === 'auto' && eachRange.get('startRange', 0) >= eachRange.get('endRange', 0)) {
        return dispatch(
          setData(
            Map({ message: `start range should be less than from end range in Row ${i + 1}` })
          )
        );
      }
      set.lateLabel.push(late_label);
      set.shortCode.push(short_code);
      if (mode === 'auto') {
        if (i !== 0) {
          for (const [startRange, endRange] of maxMinuteRange) {
            if (
              (startRange <= eachRange.get('startRange', 0) &&
                endRange >= eachRange.get('startRange', 0)) ||
              (startRange <= eachRange.get('endRange', 0) &&
                endRange >= eachRange.get('endRange', 0))
            ) {
              return dispatch(
                setData(
                  Map({
                    message: `Row ${i + 1} minutes range should be greater than exist range values`,
                  })
                )
              );
            }
          }
        }
        maxMinuteRange.push([eachRange.get('startRange', 0), eachRange.get('endRange', 0)]);
      }
    }
    const payloadRange = range.map((item) =>
      typeof item.get('_id', '') === 'number' ? item.delete('_id', '') : item
    );
    const payload = {
      lateConfig: {
        lateType: mode,
        range: payloadRange,
      },
      attendanceMarkConfig: reducerData.getIn(['getAttendanceData', 'attendanceConfig'], Map()),
    };
    dispatch(updateAttendanceConfig(payload));
  }

  useImperativeHandle(
    ref,
    () => {
      return {
        onModeChange: (updatedMode) => {
          const updatedRange =
            reducerData
              .getIn(['getAttendanceData', 'lateConfig'], List())
              .find((item) => item.get('lateType', '') === updatedMode)
              ?.get('range', List()) || List();
          setRange(updatedRange);
        },
        onSaveClicked: onSaveClicked,
      };
    },
    [lateConfigData, range, mode] //eslint-disable-line
  );
  return (
    <TableContainer
      component={Paper}
      sx={{
        background: 'transparent',
        boxShadow: 'none',
        borderRadius: '10px',
        border: '1px solid rgba(224, 224, 224, 1)',
      }}
    >
      <Table sx={{ minWidth: 650 }} aria-label="simple table">
        <TableHead>
          <TableRow>
            {mode === 'auto' && <TableCell className="pl-4">Minutes Range</TableCell>}
            <TableCell className={mode !== 'auto' && 'pl-4'}>Late Label</TableCell>
            {mode !== 'auto' && <TableCell>Label Short Code</TableCell>}
            <TableCell>No of Late</TableCell>
            <TableCell>No of Absence</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {range.map((item, index) => {
            return (
              <TableRowLateConfig
                item={item}
                rangeSize={range.size}
                handleDelete={handleDelete}
                index={index}
                mode={mode}
                handleField={handleField}
                key={item.get('_id', '')}
              />
            );
          })}

          {mode === 'auto' && (
            <TableRow sx={{ '&:last-child th': { border: 0 } }}>
              <TableCell className="pl-4">
                <TextField
                  className="disabled_input_grey text-center"
                  size={'small'}
                  value={globalLateConfig.get('minuteRange', 0) + ' Above'}
                  disabled
                />
              </TableCell>
              <TableCell>
                <TextField
                  className="disabled_input_grey"
                  size={'small'}
                  value={globalLateConfig.get('label', 0)}
                  disabled
                />
              </TableCell>

              <TableCell>
                {' '}
                <TextField
                  className="width_150 disabled_input_grey"
                  type="number"
                  size={'small'}
                  disabled
                  value="1"
                  InputProps={{
                    endAdornment: <div className="width_150">Late</div>,
                  }}
                />
              </TableCell>
              <TableCell>
                <TextField
                  className="width_150 disabled_input_grey"
                  disabled
                  value="1"
                  type="number"
                  size={'small'}
                  InputProps={{
                    endAdornment: <div className="width_150">Absence</div>,
                  }}
                />
                <Tooltips
                  title={'This setting will set in Global Configuration on Institution Level'}
                >
                  <InfoIcon className="info_absence_tooltip" />
                </Tooltips>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
        <TableFooter>
          <div
            className="d-flex p-3 pl-4"
            onClick={() =>
              setRange((prev) => {
                rowCount = rowCount + 1;
                return prev.push(Map({ _id: rowCount }));
              })
            }
          >
            <AddIcon sx={{ fontSize: '20px' }} /> <div className="ml-1 f-14">Add Row</div>
          </div>
        </TableFooter>
      </Table>
    </TableContainer>
  );
});
LateConfigurationTable.propTypes = {
  mode: PropTypes.string,
  setMode: PropTypes.func,
};
export default LateConfigurationTable;
