import { ENCRYPT_STORAGE } from 'constants';
import { decryptData, encryptData } from './encryption';
import useCustomCookie from 'Hooks/useCookieHook';

const LocalStorageService = (function () {
  var _service;
  const { setCookie, getCookie, deleteCookiesStartingWith } = useCustomCookie();

  function _getService() {
    if (!_service) {
      _service = this;
      return _service;
    }
    return _service;
  }
  function _setToken(tokenObj) {
    setCookie('access_token', tokenObj.access_token);
    setCookie('refresh_token', tokenObj.refresh_token);
    setCookie('user_Id', tokenObj.user_id);
  }
  function _setCustomToken(name, value) {
    if (typeof value === 'boolean') value = value.toString();
    if (typeof value === 'object') value = JSON.stringify(value);
    if (value && ENCRYPT_STORAGE)
      ({ data: value } = encryptData({ content: value, handledBy: 'CLIENT' }));
    localStorage.setItem(name, value);
  }
  function _getCustomToken(name, parsed = false) {
    const value = localStorage.getItem(name);
    if (value && ENCRYPT_STORAGE)
      return decryptData({ content: value, handledBy: 'CLIENT', parsed });
    return value && parsed ? JSON.parse(value) : value;
  }
  function _removeCustomToken(name) {
    localStorage.removeItem(name);
  }
  function _getAccessToken() {
    return getCookie('access_token');
  }
  function _getRefreshToken() {
    return getCookie('refresh_token');
  }
  function _getUserId() {
    return getCookie('user_Id');
  }
  function _setCustomCookie(name, value, parsed = false) {
    setCookie(name, value, parsed);
  }
  function _getCustomCookie(name, parsed = false) {
    return getCookie(name, parsed);
  }
  function _clearToken() {
    localStorage.clear();
    deleteCookiesStartingWith('DC-');
  }
  function _setCustomParsedToken(name, value) {
    if (value && ENCRYPT_STORAGE)
      ({ data: value } = encryptData({ content: value, handledBy: 'CLIENT' }));
    else value = encodeURIComponent(JSON.stringify(value));
    localStorage.setItem(name, value);
  }
  function _getCustomParsedToken(name) {
    const value = localStorage.getItem(name);
    if (value && ENCRYPT_STORAGE)
      return decryptData({ content: value, handledBy: 'CLIENT', parsed: true });
    return JSON.parse(decodeURIComponent(value));
  }
  function _getSSOAccountId() {
    return getCookie('ssoAccountId');
  }
  return {
    getService: _getService,
    setToken: _setToken,
    getAccessToken: _getAccessToken,
    getRefreshToken: _getRefreshToken,
    getUserId: _getUserId,
    clearToken: _clearToken,
    setCustomToken: _setCustomToken,
    getCustomToken: _getCustomToken,
    setCustomCookie: _setCustomCookie,
    getCustomCookie: _getCustomCookie,
    setCustomParsedToken: _setCustomParsedToken,
    getCustomParsedToken: _getCustomParsedToken,
    getSSOAccountId: _getSSOAccountId,
    removeCustomToken: _removeCustomToken,
  };
})();
export default LocalStorageService;
