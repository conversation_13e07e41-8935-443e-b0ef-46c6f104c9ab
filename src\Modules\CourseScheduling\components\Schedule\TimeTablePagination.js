import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import Dropdown from 'react-bootstrap/Dropdown';

import ArrowIcon from '../../../../Assets/dropdown.png';
import { t } from 'i18next';
import { getTranslatedDuration } from 'utils';

function TimeTableWeekPagination({
  weeks,
  activeWeek,
  handleWeekClick,
  handlePreviousWeekClick,
  handleNextWeekClick,
}) {
  return (
    <div className="calendar-header p-2 bg-white d-flex mt-3">
      <div className="border border-radious-8 p-2">
        <div className="d-flex">
          <span className="mr-2 cursor-pointer" onClick={handlePreviousWeekClick}>
            <i
              className="fa fa-angle-left bg-gray arrow_padding text-skyblue"
              aria-hidden="true"
            ></i>
          </span>

          <span className="ml-2 cursor-pointer" onClick={handleNextWeekClick}>
            <i
              className="fa fa-angle-right bg-gray arrow_padding text-skyblue"
              aria-hidden="true"
            ></i>
          </span>
          <span className="pl-3 pr-3 border-right">
            {`${t('curriculum_keys.week')} ${activeWeek.get('weekNumber', 1)}`}
          </span>
          <span className="pl-3 pr-3">
            {getTranslatedDuration(activeWeek.get('formattedDate', ''))}
          </span>
          {weeks.size > 0 && (
            <span className="pr-2 pl-1">
              <Dropdown>
                <Dropdown.Toggle
                  variant=""
                  id="dropdown-table"
                  className="table-dropdown"
                  size="sm"
                >
                  <div>
                    <img src={ArrowIcon} alt="Deactivated" title="Deactivated" />
                  </div>
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  {weeks.map((item) => (
                    <Dropdown.Item
                      key={item.get('weekNumber')}
                      onClick={() => handleWeekClick(item)}
                      className={
                        item.get('formattedDate', '') === activeWeek.get('formattedDate', '')
                          ? 'activeItem'
                          : ''
                      }
                    >
                      {getTranslatedDuration(item.get('formattedDate', ''))}
                    </Dropdown.Item>
                  ))}
                </Dropdown.Menu>
              </Dropdown>
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

TimeTableWeekPagination.propTypes = {
  weeks: PropTypes.instanceOf(List),
  activeWeek: PropTypes.instanceOf(Map),
  handleWeekClick: PropTypes.func,
  handlePreviousWeekClick: PropTypes.func,
  handleNextWeekClick: PropTypes.func,
};

export default TimeTableWeekPagination;
