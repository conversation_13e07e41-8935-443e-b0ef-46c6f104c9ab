import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';
import MButton from 'Widgets/FormElements/material/Button';

function Cancel({ show, setCancelShow, setShow, type, handleClose, setIsChanged }) {
  return (
    <Modal show={show} centered onHide={() => {}}>
      <Modal.Body className="pt-0 pb-0">
        <p className="mb-3 mt-3 f-22">
          <Trans i18nKey={'cancel'}></Trans>
        </p>

        <div className="mb-2 f-16">
          <Trans i18nKey={'add_colleges.desc.cancel_confirmation'}></Trans>
        </div>
        <div className="f-16">
          <Trans i18nKey={'add_colleges.desc.cancel_alert'}></Trans>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton
          color="inherit"
          variant="outlined"
          clicked={() => {
            if (type === 'edit') {
              setCancelShow(false);
            } else if (type === 'textEditor') {
              handleClose(false, 'no');
            } else if (type === 'create') {
              handleClose('no');
            } else {
              setCancelShow(false);
            }
          }}
        >
          <Trans i18nKey={'add_colleges.no'}></Trans>
        </MButton>
        <MButton
          className=" mr-3"
          clicked={() => {
            if (type === 'textEditor') {
              handleClose();
            } else if (type === 'create') {
              handleClose();
              setIsChanged(false);
            } else {
              setCancelShow(false);
              setShow(false);
              setIsChanged(false);
            }
          }}
        >
          <Trans i18nKey={'add_colleges.yes_cancel'}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

Cancel.propTypes = {
  show: PropTypes.string,
  setShow: PropTypes.func,
  setCancelShow: PropTypes.func,
  type: PropTypes.string,
  handleClose: PropTypes.func,
  setIsChanged: PropTypes.func,
};
export default Cancel;
