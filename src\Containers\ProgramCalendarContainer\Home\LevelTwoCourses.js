import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';
import { row_start, row_end } from '../../../_utils/function';
import {
  Level,
  PrimaryButton,
  Text,
  TextContainer,
  Course,
  CourseEvent,
  CourseEventContent,
} from '../Styled';
import { coursePopUp } from '../../../_reduxapi/actions/calender';
import { t } from 'i18next';

const LevelTwoCourses = (props) => {
  const { coursePopUp } = props;
  const match = useRouteMatch();
  const active = match.params.year || 'year2';

  return (
    <Fragment>
      <Level
        st_date={row_start(
          props[active]['level_one_start_date'],
          props[active]['level_two_start_date']
        )}
        end_date={row_end(
          props[active]['level_one_start_date'],
          props[active]['level_two_end_date']
        )}
        col={props[active]['level_two_columns']}
      >
        {props[active]['level_two_courses'].map((item, i, arr) => (
          <Course
            key={item._id}
            row_st_date={item.row_start}
            row_end_date={item.row_end}
            col_st_date={item.column_start}
            col_end_date={item.column_end}
            bg={item.color_code}
            row_len={row_start(item.start_date, item.end_date)}
            onClick={() =>
              coursePopUp(
                'edit_course',
                'level_two_courses',
                i,
                props[active]['level_two_title'],
                'level_two_columns',
                'level_two_start_date',
                'level_two_end_date',
                'level_two_course_events'
              )
            }
          >
            <div>{item.courses_name}</div>
            {item['courses_events']
              //.filter((check) => check.event_type !== "holiday")
              .map((event) => (
                <CourseEvent
                  key={event._id}
                  st_date={row_start(arr[i]['start_date'], event.event_date)}
                  end_date={row_end(arr[i]['start_date'], event.end_date)}
                >
                  {/* <CourseEventContent>{event.event_name}</CourseEventContent> */}
                </CourseEvent>
              ))}
          </Course>
        ))}
        {props[active]['level_two_courses'].length === 0 && (
          <TextContainer>
            <Text>{t('no_courses_in_this_level')}</Text>{' '}
            <Text>
              {t('add_courses_by_clicking', {
                AddIcon: (
                  <i className="fas fa-plus" style={{ color: 'black', fontSize: '14px' }}></i>
                ),
              })}
            </Text>
          </TextContainer>
        )}
      </Level>
    </Fragment>
  );
};

const mapStateToProps = ({ calender }) => ({
  // start: calender.academic_year_start,
  // active: calender.active_year,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
});

export default connect(mapStateToProps, { coursePopUp })(LevelTwoCourses);
