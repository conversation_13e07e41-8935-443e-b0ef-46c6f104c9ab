hebaBaseAPi = 'https://gemini-pro-chatbot-yk25kmkzeq-el.a.run.app'

function initHeba() {
  $('.askheba')
    .off('click')
    .on('click', function () {
      _askheba = $(this)
      $(this).text('Heba.Ai is Generating data')
      setTimeout(() => {
        $(_askheba).hide()
      }, 13000)

      $(document).on('click', '.comments', function () {
        $('.loader-comments').show()
        if ($(this).hasClass('comments')) {
          var jsonData = {
            input:
              'based on this input object  provide psycometric analyse and comment also the analyse each grade in 5000 words' +
              JSON.stringify(AllDomainData) +
              JSON.stringify(AllGradeData),
          }

          $.ajax({
            url: hebaBaseAPi,
            method: 'POST',
            beforeSend: function (xhr) {
              // Add the Access-Control-Allow-Origin header
              xhr.setRequestHeader('Access-Control-Allow-Origin', '*')
              xhr.setRequestHeader('withCredentials', 'true')
            },
            contentType: 'application/json',
            data: JSON.stringify(jsonData),
            success: function (response) {
              $('.loader-comments').hide()
              // Handle success
              console.log('Success:', response)
              response.responce = response.responce.replace('Based on the object you provided,', '')
              $('.fillheba').append(response.responce)
            },
            error: function (error) {
              $('.loader-comments').hide()
              // Handle error
              console.error('Error:', error)
              errorMessage(error)
            },
          })
        }
      })

      $(document).on('click', '.heba-recommondation', function () {
        $('.loader-recommondation').show()
        if ($(this).hasClass('heba-recommondation')) {
          var jsonData = {
            input:
              JSON.stringify(AllDomainData) +
              JSON.stringify(AllGradeData) +
              'based provide  a recommondations in positive way and in very detailed manner for NCAAA course',
          }

          $.ajax({
            url: hebaBaseAPi,
            method: 'POST',
            beforeSend: function (xhr) {
              // Add the Access-Control-Allow-Origin header
              xhr.setRequestHeader('Access-Control-Allow-Origin', '*')
              xhr.setRequestHeader('withCredentials', 'true')
            },
            contentType: 'application/json',
            data: JSON.stringify(jsonData),
            success: function (response) {
              $('.loader-recommondation').hide()
              // Handle success
              console.log('Success:', response)
              response.responce = response.responce.replace('Based on the object you provided,', '')
              $('.heba-recommondation-text').text(response.responce)
            },
            error: function (error) {
              $('.loader-recommondation').hide()
              // Handle error
              console.error('Error:', error)
              errorMessage(error)
            },
          })
        }
      })

      $(document).on('click', '.askheba.student-grade', function () {
        $('.loader-student').show()
        if ($(this).hasClass('student-grade')) {
          var jsonData = {
            input:
              JSON.stringify(AllGradeData) +
              'provide comments On student Grades in details with numbers and analyse it deeply i want to add it for NCAAA report  ',
          }

          $.ajax({
            url: hebaBaseAPi,
            method: 'POST',
            beforeSend: function (xhr) {
              // Add the Access-Control-Allow-Origin header
              xhr.setRequestHeader('Access-Control-Allow-Origin', '*')
              xhr.setRequestHeader('withCredentials', 'true')
            },
            contentType: 'application/json',
            data: JSON.stringify(jsonData),
            success: function (response) {
              // Handle success
              $('.loader-student').hide()
              console.log('Success:', response)
              response.responce = response.responce.replace('Based on the object you provided,', '')
              $('.heba-students-grade').text(response.responce)
            },
            error: function (error) {
              // Handle error
              $('.loader-student').hide()
              console.error('Error:', error)
              errorMessage(error)
            },
          })
        }
      })

      $(document).on('click', '.course-improvement-plan-req', function () {
        $('.loader-course').show()

        if ($(this).hasClass('course-improvement-plan-req')) {
          var jsonData = {
            input:
              JSON.stringify(AllDomainData) +
              JSON.stringify(AllGradeData) +
              'from provided data analyze and provide course improvements plan recommondation and action plans in points from most important to least important ',
          }

          $.ajax({
            url: hebaBaseAPi,
            method: 'POST',
            beforeSend: function (xhr) {
              // Add the Access-Control-Allow-Origin header
              xhr.setRequestHeader('Access-Control-Allow-Origin', '*')
              xhr.setRequestHeader('withCredentials', 'true')
            },
            contentType: 'application/json',
            data: JSON.stringify(jsonData),
            success: function (response) {
              $('.loader-course').hide()
              // Handle success
              console.log('Success:', response)
              response.responce = response.responce.replace('Based on the object you provided,', '')
              $('.course-improvement-plan-resp').text(response.responce)
            },
            error: function (error) {
              $('.loader-course').hide()
              // Handle error
              console.error('Error:', error)
              errorMessage(error)
            },
          })
        }
      })
    })
}
