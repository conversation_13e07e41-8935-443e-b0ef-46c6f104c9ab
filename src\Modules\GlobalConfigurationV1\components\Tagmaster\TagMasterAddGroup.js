import React from 'react';
import PropTypes from 'prop-types';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import { List } from 'immutable';

const TagMasterAddGroup = ({
  handleClickGroupClose,
  getSelectorValue,
  handleCheckboxGroup,
  addGroup,
}) => {
  const addGroupShow = addGroup.get('show', false);
  const familyIndex = addGroup.get('index', null);
  const groupList = getSelectorValue.getIn(['families', familyIndex, 'groups'], List());
  return (
    <DialogModal
      show={addGroupShow}
      onClose={handleClickGroupClose}
      maxWidth={'xs'}
      fullWidth={true}
    >
      <h4 className="m-3">Select Group</h4>
      <div className="scroll-overflow-modal">
        {getSelectorValue.get('groups', List()).map((groupName, groupIndex) => {
          return (
            <div key={groupIndex}>
              {!groupName.get('isAssigned', '') && (
                <div className="d-flex m-3">
                  <div>
                    <input
                      type="checkbox"
                      checked={groupList
                        .map((groupListItem) => groupListItem.get('_id'))
                        .includes(groupName.get('_id', ''))}
                      onChange={() =>
                        handleCheckboxGroup(groupName, 'families', familyIndex, groupIndex)
                      }
                    />
                  </div>

                  <div className="tagName-font-size">
                    <div className="ml-2 bold">
                      {groupName.get('code', '')} - {groupName.get('name', '')}
                    </div>
                    <div className="ml-2 text-muted">{groupName.get('description', '')}</div>
                  </div>
                </div>
              )}
              {groupName.get('isAssigned', '') && (
                <div className="d-flex m-3 text-muted">
                  <div>
                    <input
                      disabled={true}
                      type="checkbox"
                      checked={groupList
                        .map((groupListItem) => groupListItem.get('_id'))
                        .includes(groupName.get('_id', ''))}
                    />
                  </div>

                  <div className="tagName-font-size">
                    <div className="ml-2 bold">
                      {groupName.get('code', '')} - {groupName.get('name', '')}
                    </div>
                    <div className="ml-2 text-muted">{groupName.get('description', '')}</div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </DialogModal>
  );
};

export default TagMasterAddGroup;

TagMasterAddGroup.propTypes = {
  handleClickGroupClose: PropTypes.func,
  getSelectorValue: PropTypes.array,
  handleCheckboxGroup: PropTypes.func,
  addGroup: PropTypes.func,
};
