import React, { Component } from 'react';
import { Modal } from 'react-bootstrap';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import moment from 'moment';
import { registerLocale } from 'react-datepicker';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { t } from 'i18next';
import * as actions from '../../_reduxapi/institution/actions';
import CalenderIndex from './index';
import TableEvent from '../../Widgets/Table/TableEvent';
import axios from '../../axios';
import AddNew from '../../Widgets/Button/AddNew';
import TableTitle from '../../Widgets/Table/TableTitle';
import Loader from '../../Widgets/Loader/Loader';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import { timeFormat, getURLParams } from '../../utils';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { selectActiveInstitutionCalendar, selectUserInfo } from '../../_reduxapi/Common/Selectors';
import ar from 'date-fns/locale/ar';
import AddEditEventModal from './Modals/AddEditEventModal';
import withCalendarHooks from 'Hoc/withCalendarHooks';
registerLocale('ar', ar);

class eventListView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      deleteId: '',
      confirmDelete: false,
      currentPage: 0,
      totalDoc: '',
      totalPages: '',
      isLoading: false,
      loadOnce: true,
      eventEdit: false,
      selectType: '',
      startDate: '',
      endDate: '',
      // calendarData: [],
      editStartTime: '',
      editEndTime: '',
      eventType: [
        { name: '', value: '' },
        { name: t('events.event_types.holiday'), value: 'holiday' },
        { name: t('events.event_types.exam'), value: 'exam' },
        { name: t('events.event_types.training'), value: 'training' },
        { name: t('events.event_types.orientation'), value: 'orientation' },
        { name: t('events.event_types.general'), value: 'general' },
      ],
      evStartDate: '',
      evEndDate: '',
      evStartTime: '',
      evEndTime: '',
      evId: null,
      calendarId: getURLParams('id', true),
      isActive: getURLParams('s', true) === 'true',
    };
    this.onChangePage = this.onChangePage.bind(this);
  }

  componentDidMount() {
    this.fetchApi();
    // this.interval = setInterval(() => {
    //   const { activeInstitutionCalendar } = this.props;
    //   if (!activeInstitutionCalendar.isEmpty()) {
    //     this.fetchApi();
    //     clearInterval(this.interval);
    //   }
    // }, 500);
  }

  // componentDidUpdate(prevProps) {
  //   const { activeInstitutionCalendar } = this.props;
  //   if (activeInstitutionCalendar !== prevProps.activeInstitutionCalendar) {
  //     this.fetchApi();
  //   }
  // }

  // componentWillUnmount() {
  //   clearInterval(this.interval);
  // }

  fetchApi = () => {
    const { loggedInUserData } = this.props;
    const { calendarId } = this.state;
    const id = calendarId;
    this.setState({
      isLoading: true,
    });

    let url = `institution_calendar_event/list_calendar/${id}?limit=500&pageNo=1`;
    const subRole = loggedInUserData.get('sub_role', List());
    if (
      CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Add Event') ||
      (subRole && subRole.size > 0 && subRole.indexOf('Institution_Calendar_Reviewer') !== -1)
    ) {
      url = `institution_calendar_event/list_event/${id}`;
    }

    axios
      .get(url)
      .then((res) => {
        const data = res.data.data;
        this.setState({
          data: data,
          totalPages: res.data.totalPages,
          totalDoc: res.data.totalDoc,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  onChangePage(pageOfItems, currentPage) {
    this.setState({ pageOfItems: pageOfItems, currentPage: currentPage });
  }

  handleConfirmDeleteShow = (e, data) => {
    e.preventDefault();
    this.setState({
      confirmDelete: true,
      deleteId: data._id,
    });
  };

  handleConfirmDeleteClose = () => {
    this.setState({
      confirmDelete: false,
    });
  };

  handleDelete = () => {
    axios
      .delete(`institution_calendar_event/${this.state.deleteId}`)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
            confirmDelete: false,
          },
          () => {
            this.fetchApi();
          }
        );
        this.props.setData({ message: t('event_deleted_successfully') });
      })
      .catch((error) => {
        this.props.setData({ message: error.response.data.message });
        this.setState({
          isLoading: false,
        });
      });
  };

  validation = (e) => {
    let eventNameError = '';
    let eventDetailError = '';
    let selectTypeError = '';
    let startDateError = '';
    let endDateError = '';
    let selectVenueError = '';

    if (this.state.selectType === '') {
      selectTypeError = t('chooseEventType');
    }

    if (this.state.valueCheck === true) {
      if (this.state.selectVenue === '') {
        selectVenueError = t('chooseVenueType');
      }
    }

    if (this.state.startDate === '') {
      startDateError = t('chooseStartDate');
    }

    if (this.state.oneDay === false) {
      if (this.state.endDate === '') {
        endDateError = t('chooseEndDate');
      }
    }

    if (!this.state.eventName) {
      eventNameError = t('eventNameRequired');
    } else if (this.state.eventName.length <= 2) {
      eventNameError = t('minimumThreeChar');
    }

    if (
      eventNameError ||
      eventDetailError ||
      selectTypeError ||
      startDateError ||
      endDateError ||
      selectVenueError
    ) {
      this.setState({
        eventNameError,
        eventDetailError,
        selectTypeError,
        startDateError,
        endDateError,
        selectVenueError,
      });
      return false;
    }
    return true;
  };

  editStartTime = (date) => {
    this.setState({
      editStartTime: date,
    });
  };

  editEndTime = (date) => {
    this.setState({
      editEndTime: date,
    });
  };

  eventEdit = (e, evId) => {
    this.setState({
      isLoading: true,
      eventEdit: true,
      evId: evId,
    });
    axios
      .get(`institution_calendar_event/${evId}`)
      .then((res) => {
        const data = res.data.data;

        this.setState({
          selectType: data.event_type,
          eventName: data.event_name.first_language,
          arab1: data.event_name.second_language,
          eventDetail: data.event_description.first_language,
          arab2: data.event_description.second_language,
          startDa: data.event_date,
          endDa: data.end_date,
          startDate: moment(data.event_date).format('D MMM YYYY'),
          endDate: moment(data.end_date).format('D MMM YYYY'),
          startTimeView: data.start_time,
          endTimeView: data.end_time,
          oneDay: moment(data.event_date).format('L') === moment(data.end_date).format('L'),
          isLoading: false,
          editEndTime: '',
          editStartTime: '',
        });
      })
      .catch((error) => {
        this.props.setData({ message: 'error' });
        this.setState({
          isLoading: false,
        });
      });
  };

  eventEditClose = () => {
    this.setState({
      eventEdit: false,
    });
  };

  handleChange = (e, name) => {
    if (name === 'eventName') {
      this.setState({
        eventName: e.target.value,
        eventNameError: '',
      });
    }

    if (name === 'eventDetail') {
      this.setState({
        eventDetail: e.target.value,
        eventDetailError: '',
      });
    }
    if (name === 'arab1') {
      this.setState({
        arab1: e.target.value,
        arab1Error: '',
      });
    }
    if (name === 'arab2') {
      this.setState({
        arab2: e.target.value,
        arab2Error: '',
      });
    }
    if (name === 'startDate') {
      this.setState({
        startDate: e.target.value,
        startDateError: '',
      });
    }
    if (name === 'endDate') {
      this.setState({
        endDate: e.target.value,
        endDateError: '',
      });
    }

    if (name === 'startTime') {
      this.setState({
        startTime: e.target.value,
        startTimeError: '',
      });
    }
    if (name === 'reviewEndDate') {
      this.setState({
        reviewEndDate: e.target.value,
        reviewEndDateError: '',
      });
    }

    if (name === 'feed') {
      this.setState({
        feed: e.target.value,
      });
    }
  };

  handleSelect = (e, name) => {
    if (name === 'eventType') {
      this.setState({
        selectType: e.target.value,
        selectTypeError: '',
      });
    }
    if (name === 'venue') {
      this.setState({
        selectVenue: e.target.value,
        selectVenueError: '',
      });
    }
  };

  editStartDate = (date) => {
    this.setState({
      startDate: date,
      endDate: date,
    });
  };

  editEndDate = (date) => {
    this.setState({
      endDate: date,
    });
  };

  handleEditEventSubmit = (e) => {
    e.preventDefault();

    if (this.state.oneDay === true) {
      this.setState({
        endDate: this.state.startDate,
      });
    }

    let startDate = moment(this.state.startDate).format('YYYY-MM-DD');
    let endDate = moment(this.state.endDate).format('YYYY-MM-DD');
    let st = '';
    let et = '';

    if (this.state.editStartTime === '') {
      let startTime = timeFormat(moment(this.state.startTimeView).format('H:mm:ss'));
      st = moment(startDate + 'T' + startTime).toDate();
    } else {
      let startTime = timeFormat(moment(this.state.editStartTime).format('H:mm:ss'));
      st = moment(startDate + 'T' + startTime).toDate();
    }

    if (this.state.editEndTime === '') {
      let endTime = timeFormat(moment(this.state.endTimeView).format('H:mm:ss'));
      et = moment(endDate + 'T' + endTime).toDate();
    } else {
      let endTime = timeFormat(moment(this.state.editEndTime).format('H:mm:ss'));
      et = moment(endDate + 'T' + endTime).toDate();
    }

    let error = false;
    if (this.state.startDate !== '' && this.state.endDate !== '') {
      if (st.getTime() >= et.getTime()) {
        this.setState({ timeError: t('endTimeGreat') });
        error = true;
      } else {
        this.setState({ timeError: '' });
      }
    }

    let eventDetail = this.state.eventDetail;
    if (this.state.eventDetail === '') {
      eventDetail = ' ';
    }

    if (this.validation() && !error) {
      const data = {
        event_type: this.state.selectType,
        event_name: {
          first_language: this.state.eventName.trim(),
          second_language: this.state.arab1.trim(),
        },
        event_description: {
          first_language: eventDetail,
          second_language: this.state.arab2,
        },
        event_date: startDate,
        end_date: endDate,
        start_time: st.getTime(),
        end_time: et.getTime(),
      };

      this.setState({
        isLoading: true,
      });

      axios
        .put(`institution_calendar_event/${this.state.evId}`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              selectType: '',
              eventName: '',
              arab1: '',
              eventDetail: '',
              arab2: '',
              selectVenue: '',
              oneDay: false,
              valueCheck: false,
              eventEdit: false,
            },
            () => {
              this.fetchApi();
            }
          );
          this.props.setData({ message: t('event_updated_successfully') });
        })
        .catch((error) => {
          // let errorMessage='';
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  handleState = (data) => {
    this.setState(data);
  };

  handleCheck = (event, name) => {
    if (name === 'day') {
      this.setState({
        oneDay: event.target.checked,
        endDate: this.state.startDate,
      });
    }
    if (name === 'value') {
      this.setState({
        valueCheck: event.target.checked,
      });
    }
    if (name === 'skip') {
      this.setState({
        skip: event.target.checked,
      });
    }
  };

  render() {
    const { isActive } = this.state;
    //const { isCurrentCalendar } = this.props;
    // const academicYear = this.state.calendarData[this.state.index];
    // let dd = moment(startTimeView).format('hh:mm A');
    // let ss = moment(endTimeView).format('hh:mm A');
    const currentCalendar = isActive; // isCurrentCalendar(calendarId);
    const header = [
      t('s_no'),
      t('events.event_name'),
      t('eventDescription'),
      t('Date'),
      t('global_configuration.time'),
    ];
    if (
      currentCalendar &&
      (CheckPermission('tabs', 'Calendar', 'Institution Calendar', '', 'View All', 'Edit') ||
        CheckPermission('tabs', 'Calendar', 'Institution Calendar', '', 'View All', 'Delete'))
    ) {
      header.push(t('action'));
    }
    const getTranslatedDate = (eventDate, endDate) => {
      return `${`${t(`calender.${moment(eventDate).format('MMM')}`)} ${moment(eventDate).format(
        'Do'
      )}`} - ${`${moment(endDate).format('Do')} ${t(
        `calender.${moment(endDate).format('MMM')}`
      )} ${moment(endDate).format('YY')}`}`;
    };
    const bodyRow = this.state.data.map((data, index) => {
      return (
        <tr className="tr-change" key={index}>
          <td>{index + 1} </td>
          <td>
            <p className="f-14 mb-1">{data.event_name.first_language} </p>
            {data.event_name.second_language !== '' ? (
              <p className="f-14 mb-1 text-right">{data.event_name.second_language}</p>
            ) : (
              ''
            )}
          </td>
          <td>
            <p className="f-14 mb-1">{data.event_description.first_language} </p>
            {data.event_description.second_language !== '' ? (
              <p className="f-14 mb-1 text-right">{data.event_description.second_language}</p>
            ) : (
              ''
            )}
          </td>
          <td>{getTranslatedDate(data.event_date, data.end_date)}</td>
          <td>
            {moment(data.start_time).format('hh:mm A')} - {moment(data.end_time).format('hh:mm A')}{' '}
          </td>
          {currentCalendar && (
            <td>
              {CheckPermission(
                'tabs',
                'Calendar',
                'Institution Calendar',
                '',
                'View All',
                'Edit'
              ) && (
                <i
                  className="fa fa-edit text-blue pr-3 f-16 remove_hover"
                  onClick={(e) => this.eventEdit(e, data._id)}
                ></i>
              )}
              {CheckPermission(
                'tabs',
                'Calendar',
                'Institution Calendar',
                '',
                'View All',
                'Delete'
              ) && (
                <i
                  className="fa fa-trash text-blue f-16 remove_hover"
                  onClick={(e) => this.handleConfirmDeleteShow(e, data)}
                ></i>
              )}
            </td>
          )}
        </tr>
      );
    });

    // const iconForm = lang === 'ar' ? 'icon-form-arabic' : 'icon-form';
    // const iconFormCalender = lang === 'ar' ? 'icon-form-calender-arabic' : 'icon-form-calender';

    // const isIndianVersion = isIndVer();
    const items = [
      { to: '#', label: t('side_nav.menus.institution_calendar') },
      { label: t('viewEvents') },
    ];
    return (
      <React.Fragment>
        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={index} to={to}>
                {label}
              </Link>
            ))}
        </Breadcrumb>
        <CalenderIndex />

        <div className="main bg-gray pt-2">
          <Loader isLoading={this.state.isLoading} />
          <div className="container">
            <div className="tables">
              <TableTitle title={t('role_management.role_actions.Event List')} />
              <AddNew create={t('backToCalendar')} to={'/InstitutionCalendar'} />
              <div className="clearfix"> </div>

              {this.state.data.length === 0 ? (
                <TableEmptyMessage />
              ) : (
                <React.Fragment>
                  <TableEvent
                    headerRow={header.map((data) => {
                      return <th key={data}>{data}</th>;
                    })}
                    bodyRow={<React.Fragment>{bodyRow}</React.Fragment>}
                  />
                </React.Fragment>
              )}
            </div>
            {/* Delete model start  */}
            <Modal show={this.state.confirmDelete} onHide={this.handleConfirmDeleteClose}>
              <Modal.Header closeButton>
                <Modal.Title id="example-modal-sizes-title-sm">
                  {t('global_configuration.delete_event')}
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div className="row  justify-content-center">
                  <div className="col-md-12 pt-2">
                    <p className="pt-2"> {t('sureDelete')}? </p>
                  </div>
                  <div className="col-md-12 pt-2">
                    <span
                      className="remove_hover btn btn-primary pull-right"
                      onClick={(e) => this.handleDelete()}
                    >
                      {t('confirm')}
                    </span>
                    <span
                      className="remove_hover btn btn-outline-primary pull-right  mr-2"
                      onClick={this.handleConfirmDeleteClose}
                    >
                      {t('cancel')}
                    </span>{' '}
                  </div>
                </div>
              </Modal.Body>
            </Modal>
            {/* event edit funtion start */}
            {this.state.eventEdit && (
              <AddEditEventModal
                handleState={this.handleState}
                state={this.state}
                show={this.state.eventEdit}
                eventClose={this.eventEditClose}
                fetchApi={this.fetchApi}
                setData={this.props.setData}
                flag={'edit'}
              />
            )}
            {/* event edit funtion end */}
          </div>
        </div>
      </React.Fragment>
    );
  }
}

eventListView.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  loggedInUserData: PropTypes.instanceOf(Map),
  isCurrentCalendar: PropTypes.func,
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions)
)(withCalendarHooks(eventListView));
