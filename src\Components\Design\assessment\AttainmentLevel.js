import React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import MaterialInput from 'Widgets/FormElements/material/Input';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useStylesFunction } from './designUtils';
import SubdirectoryArrowRightOutlinedIcon from '@mui/icons-material/SubdirectoryArrowRightOutlined';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import AccountTreeIcon from '@mui/icons-material/AccountTree';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import { ListItemButton } from '@mui/material';

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Menu,
  ListItem,
  List,
  ListItemText,
  Collapse,
  ListItemIcon,
  Checkbox,
  Divider,
  Typography,
} from '@mui/material';
function AttainmentLevel(props) {
  const classes = useStylesFunction();
  const [value, setValue] = React.useState(0);
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const [arrowShow, setArrowShow] = React.useState(false);
  const handleArrowShow = () => {
    setArrowShow(true);
  };
  const [expandeds, setExpandeds] = React.useState(false);
  const handleChanges = (panel2) => (event, isExpandeds) => {
    setExpandeds(isExpandeds ? panel2 : false);
  };
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const ITEM_HEIGHT = 40;

  const [open1] = React.useState(true);

  const quentionNumber = [
    {
      name: 'C01',
      value: 'C01',
    },
    {
      name: 'C02',
      value: 'C02',
    },
    {
      name: 'C03',
      value: 'C03',
    },
  ];
  const percnt = [
    {
      name: 'percnt',
      value: 'percnt',
    },
  ];
  const number = [
    {
      name: '1',
      value: '1',
    },
    {
      name: '2',
      value: '2',
    },
    {
      name: '3',
      value: '3',
    },
  ];

  const number2 = [
    {
      name: '≥',
      value: '1',
    },
    {
      name: '≥',
      value: '2',
    },
    {
      name: '≥',
      value: '3',
    },
  ];

  const Benchmark = [
    {
      name: 'Level 1',
      value: 'Level 1',
    },
    {
      name: 'Level 2',
      value: 'Level 2',
    },
    {
      name: 'Level 3',
      value: 'Level 3',
    },
  ];

  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="bg-white p-3 border-bottom-2px">
        <div className="container-fluid">
          <p className="font-weight-bold mb-0 text-left f-17">
            <i className="fa fa-arrow-left pr-3 remove_hover" aria-hidden="true"></i>Regulation -
            2022.V2
          </p>
        </div>
      </div>

      <div className="pl-1 pr-1 bg-white">
        <Tabs
          value={value}
          onChange={handleChange}
          textColor="primary"
          variant="scrollable"
          classes={{
            textColorSecondary: classes.customLabelColor,
            indicator: classes.indicator,
          }}
        >
          <Tab label="Evaluation Plan" value={0} />
          <Tab label="Attainment Level" value={1} />
        </Tabs>
      </div>

      {value === 1 && (
        <div className="bg-gray p-4">
          <div className="col-md-3">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: quentionNumber }}
              label={'Select Outcome'}
              labelclass={'mb-0'}
              id={'#bg-white'}
            />
          </div>

          <div className="col-12 pt-3">
            <Accordion
              expanded={expandeds === 'panel2'}
              onChange={handleChanges('panel2')}
              className={classes.accordionBackgroundNone}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon fontSize={`large`} className="m-0 p-0" />}
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className={classes.accordionArrowPosition}
              >
                <p className="mb-0 bold f-17">Attainment Level</p>
              </AccordionSummary>
              <AccordionDetails>
                <div className="w-100 ">
                  <div className="row pb-4">
                    <div className="col-1">
                      <MaterialInput
                        elementType={'materialSelect'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{ options: number }}
                        label={'Levels Start'}
                        labelclass={'mb-0 f-14'}
                        id={'#bg-white'}
                      />
                    </div>
                    <div className="col-1">
                      <MaterialInput
                        elementType={'materialSelect'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{ options: number }}
                        label={'Levels End'}
                        labelclass={'mb-0 f-14'}
                        id={'#bg-white'}
                      />
                    </div>

                    <div className="col-2 pl-4 pr-5 border-left">
                      <MaterialInput
                        elementType={'materialSelect'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{ options: number }}
                        label={'Values in'}
                        labelclass={'mb-0 f-14'}
                        id={'#bg-white'}
                      />
                    </div>
                    <div className="col-6"></div>

                    <div className="justify-content-end align-items-end mt-4 ">
                      <Typography
                        component="div"
                        className="remove_hover digi-blue-bg border-radious-4"
                        aria-label="more"
                        aria-controls="long-menu"
                        aria-haspopup="true"
                        onClick={handleClick}
                      >
                        <div className="d-flex justify-content-between p-2 digi-color-white">
                          <p className="mb-0 pl-2 f-17 digi-color-white">Select Node </p>
                          <div className="digi-circle ml-2 mr-4">6</div>
                          <AccountTreeIcon className="digi-color-white" />
                        </div>
                      </Typography>

                      <Menu
                        id="long-menu"
                        anchorEl={anchorEl}
                        keepMounted
                        open={open}
                        onClose={handleClose}
                        PaperProps={{
                          style: {
                            maxHeight: ITEM_HEIGHT * 10.5,
                            width: '28.2ch',
                          },
                        }}
                      >
                        <>
                          <List component="div" className="align-items-center" disablePadding>
                            <ListItem button>
                              <ListItemIcon className="minWidth-32">
                                <Checkbox
                                  edge="start"
                                  // checked={}
                                  tabIndex={-1}
                                  disableRipple
                                  color="primary"
                                />
                              </ListItemIcon>
                              <ListItemText primary="Overall Attainment" />
                            </ListItem>
                          </List>

                          <Divider />
                          <ListItemButton onClick={handleClick}>
                            <ListItemIcon className="minWidth-32">
                              <Checkbox
                                edge="start"
                                // checked={}
                                tabIndex={-1}
                                disableRipple
                                color="primary"
                              />
                            </ListItemIcon>
                            <ListItemText primary="Direct" />
                          </ListItemButton>
                          <Divider />
                          <Collapse in={open} timeout="auto" unmountOnExit>
                            <List component="div" disablePadding>
                              <ListItemButton sx={{ pl: 4 }}>
                                <ListItemIcon className="minWidth-32">
                                  <Checkbox
                                    edge="start"
                                    // checked={}
                                    tabIndex={-1}
                                    disableRipple
                                    color="primary"
                                  />
                                </ListItemIcon>
                                <ListItemText primary="Internal" />
                              </ListItemButton>
                              <Divider />
                              <ListItemButton sx={{ pl: 4 }}>
                                <ListItemIcon className="minWidth-32">
                                  <Checkbox
                                    edge="start"
                                    // checked={}
                                    tabIndex={-1}
                                    disableRipple
                                    color="primary"
                                  />
                                </ListItemIcon>
                                <ListItemText primary="External" />
                              </ListItemButton>
                            </List>
                          </Collapse>
                          <Divider />
                          <Collapse in={open1} timeout="auto" unmountOnExit>
                            <ListItem button>
                              <ListItemIcon className="minWidth-32">
                                <Checkbox
                                  edge="start"
                                  // checked={}
                                  tabIndex={-1}
                                  disableRipple
                                  color="primary"
                                />
                              </ListItemIcon>
                              <ListItemText primary="Indirect" />
                            </ListItem>
                            <Divider />
                          </Collapse>
                        </>
                      </Menu>
                    </div>
                  </div>
                  <div className="attainment_table">
                    <table align="left">
                      <thead>
                        <tr>
                          <th scope="col" className="borderNone">
                            <div className="cw_200">
                              <p className="thHeader text-left">Levels</p>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 0</p>

                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'rgb(220, 0, 78)'}
                                />
                              </div>
                            </div>
                          </th>

                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 1</p>

                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'blue'}
                                />
                              </div>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 3</p>

                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'#FBBF24'}
                                />
                              </div>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 5</p>
                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'green'}
                                />
                              </div>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 6</p>

                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'blue'}
                                />
                              </div>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 8</p>

                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'blue'}
                                />
                              </div>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 9</p>

                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'blue'}
                                />
                              </div>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space d-flex justify-content-center align-items-center">
                              <p className="thHeader mr-2">Level 10</p>

                              <div className="pt-1">
                                <FiberManualRecordRoundedIcon
                                  className="p-0"
                                  fontSize={`large`}
                                  htmlColor={'blue'}
                                />
                              </div>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <th scope="col" className="borderNone bg-gray">
                            <div className="custom_space">
                              <p className="thHeader text-left">Range</p>
                            </div>
                          </th>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>

                          <td className="borderNone bg-gray">
                            <div className="row custom_space">
                              <div className="col-md-12">
                                <p className="thHeaderAttainment f-15">Perc %</p>
                              </div>
                            </div>
                          </td>
                        </tr>

                        <tr>
                          <th scope="col" className="attainment_border_bottom bg-white">
                            <div
                              className="d-flex justify-content-between custom_space align-items-center remove_hover"
                              onClick={handleArrowShow}
                            >
                              <p className="thHeader text-left"> Overall Attainment</p>
                              <ExpandMoreIcon
                                fontSize={`medium`}
                                className={`m-0 p-0 text-skyblue  ${arrowShow && 'rotate_180'}`}
                              />
                            </div>
                          </th>

                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                          <td className="attainment_border_bottom">
                            <div className="row custom_space">
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: number2 }}
                                  id={'#attainmentSelect'}
                                />
                              </div>
                              <div className="col-md-6">
                                <MaterialInput
                                  elementType={'materialInput'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                />
                              </div>
                            </div>
                          </td>
                        </tr>

                        {arrowShow && (
                          <>
                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> Direct</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue rotate_180"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                            </tr>

                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center pl-3">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> Internal</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue rotate_180"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center pl-3">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> External</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue rotate_180"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                            </tr>

                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> Indirect</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                              <td className="attainment_border_bottom">
                                <div className="row custom_space">
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                  <div className="col-md-6">
                                    <MaterialInput
                                      elementType={'materialInput'}
                                      type={'text'}
                                      variant={'outlined'}
                                      size={'small'}
                                    />
                                  </div>
                                </div>
                              </td>
                            </tr>
                          </>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
            <Accordion
              expanded={expandeds === 'panel2'}
              onChange={handleChanges('panel2')}
              className={classes.accordionBackgroundNone}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon fontSize={`large`} className="m-0 p-0" />}
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className={classes.accordionArrowPosition}
              >
                <p className="mb-0 bold f-17">Target Benchmark</p>
              </AccordionSummary>

              <div className="pl-4">
                <RadioGroup
                  row
                  aria-labelledby="demo-radio-buttons-group-label"
                  defaultValue="female"
                  name="radio-buttons-group"
                >
                  <FormControlLabel value="female" control={<Radio />} label="Assessment" />
                  <FormControlLabel value="male" control={<Radio />} label="PO" />
                </RadioGroup>
              </div>
              <div className="w-100">
                <div className="attainment_table">
                  <table align="left">
                    <thead>
                      <tr>
                        <th scope="col" className="borderNone">
                          <div className="row cw_350 custom_space">
                            <div className="col-md-7">
                              <div className="cw_100">
                                <p className="thHeader text-left">Node</p>
                              </div>
                            </div>
                            <div className="col-md-5">
                              <div className="cw_100">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: percnt }}
                                  labelclass={'mb-0 f-14'}
                                  id={'#bg-white'}
                                />
                              </div>
                            </div>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P01</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P02</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P03</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P04</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P05</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P06</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P06</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P06</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P06</p>
                          </div>
                        </th>
                        <th scope="col" className="borderNone">
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">P06</p>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <th scope="col" className="attainment_border_bottom bg-white">
                          <div className="justify-content-between custom_space align-items-center">
                            <p className="thHeader text-left"> Set Target </p>
                          </div>
                        </th>

                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                        <td className="attainment_border_bottom">
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <AccordionDetails>
                <div className="w-50 ">
                  <div className="attainment_table">
                    <table align="left">
                      <thead>
                        <tr>
                          <th scope="col" className="borderNone">
                            <div className="cw_200">
                              <p className="thHeader text-left">Node</p>
                            </div>
                          </th>
                          <th scope="col" className="borderNone">
                            <div className="cw_200 custom_space  justify-content-center align-items-center">
                              <p className="thHeader mr-2">Set Benchmark</p>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <th scope="col" className="attainment_border_bottom bg-white">
                            <div
                              className="d-flex justify-content-between custom_space align-items-center remove_hover"
                              onClick={handleArrowShow}
                            >
                              <p className="thHeader text-left"> Overall Attainment</p>
                              <ExpandMoreIcon
                                fontSize={`medium`}
                                className={`m-0 p-0 text-skyblue  ${arrowShow && 'rotate_180'}`}
                              />
                            </div>
                          </th>

                          <td className="attainment_border_bottom">
                            <MaterialInput
                              elementType={'materialSelect'}
                              type={'text'}
                              variant={'outlined'}
                              size={'small'}
                              elementConfig={{ options: Benchmark }}
                              labelclass={'mb-0 f-14'}
                              id={'#bg-white'}
                            />
                          </td>
                        </tr>

                        {arrowShow && (
                          <>
                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> Direct</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue rotate_180"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: Benchmark }}
                                  labelclass={'mb-0 f-14'}
                                  id={'#bg-white'}
                                />
                              </td>
                            </tr>

                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center pl-3">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> Internal</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue rotate_180"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: Benchmark }}
                                  labelclass={'mb-0 f-14'}
                                  id={'#bg-white'}
                                />
                              </td>
                            </tr>
                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center pl-3">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> External</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue rotate_180"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: Benchmark }}
                                  labelclass={'mb-0 f-14'}
                                  id={'#bg-white'}
                                />
                              </td>
                            </tr>

                            <tr>
                              <th scope="col" className="attainment_border_bottom bg-white">
                                <div className="d-flex justify-content-between custom_space align-items-center remove_hover">
                                  <div className="d-flex align-items-center">
                                    <SubdirectoryArrowRightOutlinedIcon
                                      className="mr-2 p-0 text-gray"
                                      fontSize={`small`}
                                    />
                                    <p className="thHeader text-left"> Indirect</p>
                                  </div>
                                  <ExpandMoreIcon
                                    fontSize={`medium`}
                                    className="m-0 p-0 text-skyblue"
                                  />
                                </div>
                              </th>

                              <td className="attainment_border_bottom">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  elementConfig={{ options: Benchmark }}
                                  labelclass={'mb-0 f-14'}
                                  id={'#bg-white'}
                                />
                              </td>
                            </tr>
                          </>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </AccordionDetails>
            </Accordion>
          </div>
        </div>
      )}
    </div>
  );
}

export default AttainmentLevel;
