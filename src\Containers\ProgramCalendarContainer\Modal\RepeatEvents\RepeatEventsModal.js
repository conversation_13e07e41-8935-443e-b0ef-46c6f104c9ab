import React, { Fragment, useReducer, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { toggleModal, saveRepeatEvents, changeYear } from '../../../../_reduxapi/actions/calender';
import { PrimaryButton, FlexWrapper, Null, Label } from '../../Styled';
import { NotificationManager } from 'react-notifications';
const initial_state = {
  year2: [],
  year3: false,
  year4: false,
  year5: false,
  year6: false,
  data: {
    year3: [],
    year4: [],
    year5: [],
    year6: [],
  },
};
const dataAlign = (data, year2, saveRepeatEvents, id) => {
  let error = false;
  if (year2 !== undefined && year2.level_one_course_events.length === 0) {
    NotificationManager.error('No events found in year2');
    error = true;
    return;
  }
  let final = [];
  if (data.year3) {
    final.push('3');
  }
  if (data.year4) {
    final.push('4');
  }
  if (data.year5) {
    final.push('5');
  }
  if (data.year6) {
    final.push('6');
  }

  if (final.length === 0) {
    NotificationManager.error('Choose atleast one checkbox');
    error = true;
    return;
  }

  let obj = {};
  obj._calendar_id = id;
  obj.from_year = '2';
  obj.to_year = final;
  if (!error) {
    saveRepeatEvents(obj);
  }
};

const onChangeHandler = (state, payload, name) => {
  if (payload) {
    return {
      ...state,
      [name]: payload,
      data: {
        ...state.data,
        [name]: [...state.year2],
      },
    };
  } else {
    return {
      ...state,
      [name]: payload,
      data: {
        ...state.data,
        [name]: [],
      },
    };
  }
};

const reducer = (state, action) => {
  const { type, payload, name } = action;

  switch (type) {
    case 'CLEAR':
      return {
        year2: [],
        year3: false,
        year4: false,
        year5: false,
        year6: false,
        data: {
          year3: [],
          year4: [],
          year5: [],
          year6: [],
        },
      };
    case 'ON_CHANGE':
      return onChangeHandler(state, payload, name);
    case 'INITIAL_LOAD':
      return {
        ...state,
        year2: [...payload],
      };
    default:
      return {
        ...state,
      };
  }
};

const RepeatEventsModal = ({ id, year2, toggleModal, changeYear, saveRepeatEvents }) => {
  const [yearSelected, setYearSelected] = useReducer(reducer, initial_state);

  useEffect(() => {
    //changeYear("year2");
  }, [year2, changeYear]);

  return (
    <Fragment>
      <h4>Repeat events list in other years</h4>
      <p>Select the years you want the events list to repeat</p>
      <FlexWrapper>
        <input
          type="checkbox"
          value={yearSelected.year3}
          name="year3"
          id="year3"
          onChange={(e) =>
            setYearSelected({
              type: 'ON_CHANGE',
              payload: e.target.checked,
              name: e.target.name,
            })
          }
        />
        <Label mg="10px" htmlFor="year3">
          Year 3
        </Label>
      </FlexWrapper>
      <FlexWrapper>
        <input
          type="checkbox"
          value={yearSelected.year4}
          name="year4"
          id="year4"
          onChange={(e) =>
            setYearSelected({
              type: 'ON_CHANGE',
              payload: e.target.checked,
              name: e.target.name,
            })
          }
        />
        <Label mg="10px" htmlFor="year4">
          Year 4
        </Label>
      </FlexWrapper>
      <FlexWrapper>
        <input
          type="checkbox"
          value={yearSelected.year5}
          name="year5"
          id="year5"
          onChange={(e) =>
            setYearSelected({
              type: 'ON_CHANGE',
              payload: e.target.checked,
              name: e.target.name,
            })
          }
        />
        <Label mg="10px" htmlFor="year5">
          Year 5
        </Label>
      </FlexWrapper>
      <FlexWrapper>
        <input
          type="checkbox"
          value={yearSelected.year6}
          name="year6"
          id="year6"
          onChange={(e) =>
            setYearSelected({
              type: 'ON_CHANGE',
              payload: e.target.checked,
              name: e.target.name,
            })
          }
        />
        <Label mg="10px" htmlFor="year6">
          Year 6
        </Label>
      </FlexWrapper>
      <FlexWrapper>
        <Null />
        <PrimaryButton className="light" onClick={() => toggleModal()}>
          cancel
        </PrimaryButton>
        <PrimaryButton
          onClick={() => {
            dataAlign(yearSelected, year2, saveRepeatEvents, id);
            //setYearSelected({ type: "CLEAR" });
            //changeYear("year3");
            //toggleModal();
          }}
        >
          Save
        </PrimaryButton>
      </FlexWrapper>
    </Fragment>
  );
};

RepeatEventsModal.propTypes = {
  id: PropTypes.string,
  year2: PropTypes.object,
  toggleModal: PropTypes.func,
  changeYear: PropTypes.func,
  saveRepeatEvents: PropTypes.func,
};

const mapStateToProps = ({ calender }) => ({
  year2: calender.year2,
  id: calender.institution_Calender_Id,
});

export default connect(mapStateToProps, {
  toggleModal,
  changeYear,
  saveRepeatEvents,
})(RepeatEventsModal);
