import React, { useEffect, useRef, useState } from 'react';
import {
  FormControl,
  // FormControlLabel,
  FormLabel,
  Radio,
  MenuItem,
  OutlinedInput,
  Select,
  Checkbox,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Stack,
} from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import InfoIcon from '@mui/icons-material/Info';
import DoneIcon from '@mui/icons-material/Done';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
// import MButton from 'Widgets/FormElements/material/Button';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { useBooleanState, useDispatchApi, useInputState } from '../utils/hooks';
import {
  getRolesPermissionActions,
  postRole,
  setData,
} from '_reduxapi/q360/roleAndPermission/actions';

import ModeEditIcon from '@mui/icons-material/Edit';
import { CategoryTreeView1, MultiSelect, ProgramTreeView } from './Treeview';
import {
  AccordComponent,
  AccordComponentWithState,
  CategoryData,
  QAPC_GROUPED_BY_CATEGORY,
  // QAPC_GROUPED_BY_PROGRAM,
  useQapcContext,
} from '../utils/component';
import { fromJS, List, Map } from 'immutable';
import { Popover } from '@mui/material';
import { ParentComponent } from '../../Models/CreateNewForm';

import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import {
  selectFormApprover,
  selectQ360AllRoles,
  selectQapcActions,
  selectSetOpen,
  selectIsLoading,
} from '_reduxapi/q360/roleAndPermission/selector';
import {
  useCallApiHook,
  useIsReduxEmpty,
  useNestedHook,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { useSelector } from 'react-redux';
import { Close, ExpandMoreOutlined } from '@mui/icons-material';
import LocalStorageService from 'LocalStorageService';
import { jsUcfirst } from 'utils';

const checkBoxSx = {
  padding: '0px !important',
  '& .MuiSvgIcon-root': {
    width: '15px',
    height: '15px',
  },
};

const ChipSx = {
  height: '25px',
  backgroundColor: '#F3F4F6',
  border: '1px solid #D1D5DB',
};

export const constructProgramSubData = ({
  categoryFormCourseIds,
  previousSubData,
  reduxPccfData,
}) => {
  let result = Map();
  const programId = reduxPccfData.get('programId', '');
  reduxPccfData.get('categories', Map()).foreach(([categoryId, categoryData]) => {
    let categoryName = categoryData.get('categoryName', '');
    if (result.getIn([programId, 'categories', categoryId], Map()).size === 0) {
      result = result.updateIn([programId, 'categories', categoryId], Map(), (data) => {
        data = data
          .set('childKey', 'forms')
          .set('subDataKey', List([...previousSubData, 'categories', categoryId]))
          .set('name', categoryName)
          .set('forms', Map());
        return data;
      });
      reduxPccfData.get('forms', Map()).foreach(([formId, formData]) => {
        const formName = formData.get('formName', '');
        if (
          result.getIn([programId, 'categories', categoryId, 'forms', formId], Map()).size === 0
        ) {
          result = result.updateIn(
            [programId, 'categories', categoryId, 'forms', formId],
            Map(),
            (data) => {
              data = data
                .set('name', formName)
                .set('callApi', true)
                .set('categoryFormCourseIds', categoryFormCourseIds)
                .set(
                  'subDataKey',
                  List([...previousSubData, 'categories', categoryId, 'forms', formId])
                );
              return data;
            }
          );
        }
      });
    }
  });
};
const PopoverComponent = ({ anchorEl, handleClose, children }) => {
  const openPop = Boolean(anchorEl);
  const id = openPop ? 'simple-popover' : undefined;
  return (
    <Popover
      id={id}
      open={openPop}
      anchorEl={anchorEl}
      onClose={handleClose}
      // anchorOrigin={{
      //   vertical: 'top',
      //   horizontal: 'left',
      // }}
      // transformOrigin={{
      //   vertical: 'bottom',
      //   horizontal: 'left',
      // }}
      sx={{
        '& .MuiPopover-paper': {
          width: '300px', // Set your desired width here
        },
      }}
    >
      {children}
    </Popover>
  );
};
export const AddNewRole = () => {
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  return (
    <>
      <div
        className="border rounded py-2 px-3 addRoleButton f-16 fw-500 text-dBlue d-flex align-items-center cursor-pointer order-1"
        onClick={handleOpen}
      >
        <AddIcon fontSize="small" />
        <span className="pl-2">Add New Role</span>
      </div>
      {open && <AddRoleComponent handleClose={handleClose} />}
    </>
  );
};
export const invalidValue = ['', null, undefined];
const preventDefault = (e) => {
  e.stopPropagation();
};
const RoleName = ({
  children,
  defaultValue = '',
  requestData,
  openOrClose,
  handleOpenOrClose,
  handleClose,
  roleId = '',
}) => {
  const [createRole] = useDispatchApi(postRole);
  const [updateApi] = useCallApiHook(setData);
  const allRoles = useSelector(selectQ360AllRoles);

  const onClickDone = () => {
    const { roleName } = requestData.current;
    const isDuplicateRoleName = allRoles.some((roles) => {
      const formatRoleName = roles.get('roleName', '').toLowerCase().trim();
      return formatRoleName === roleName.toLowerCase().trim();
    });
    if (invalidValue.includes(roleName.trim())) {
      return updateApi(fromJS({ message: 'Role Name Is Required' }));
    }
    if (isDuplicateRoleName) {
      return updateApi(fromJS({ message: 'Already This Role Name Is Created' }));
    }
    createRole({ roleName, qapcRoleId: roleId }, handleClose);
  };

  const onCancelDone = () => {
    handleClose();
  };

  const [roleName, handleChange] = useInputState({
    defaultValue,
    ref: requestData,
    refName: 'roleName',
  });

  return (
    <AccordComponent state={openOrClose} updateState={handleOpenOrClose}>
      <div className="d-flex align-items-center ml-2" onClick={preventDefault}>
        <MaterialInput
          elementType={'materialInput'}
          type={'text'}
          variant={'outlined'}
          size={'small'}
          value={roleName}
          changed={handleChange}
          onClick={preventDefault}
          placeholder="Enter Role Name"
        />
        <div onClick={onClickDone}>{children}</div>
        <div onClick={onCancelDone}>
          <CloseIcon
            fontSize="medium"
            className="ml-3 cursor-pointer"
            color="error"
            sx={{ marginBottom: '5px' }}
          />
        </div>
      </div>
    </AccordComponent>
  );
};
const RoleCategory = ({ category, handleChange }) => {
  return (
    <FormControl>
      <FormLabel className="f-13 mb-1 fw-400 color-lt-gray">Select the Hierarchy</FormLabel>
      <div className="d-flex fw-400 f-14 text-dGrey align-items-center">
        <Radio
          className="p-0  pr-2"
          size="small"
          value={'cfpc'}
          checked={category === QAPC_GROUPED_BY_CATEGORY}
          onClick={handleChange}
        />
        <span className="">Category, Form, Program, Course</span>
        {/* <Radio
          className="p-0 pl-2 pr-2"
          size="small"
          value={QAPC_GROUPED_BY_PROGRAM}
          checked={category === QAPC_GROUPED_BY_PROGRAM}
          onClick={handleChange}
          disabled={true}
        />
        <span className="">Program, Course, Category, Form</span> */}
      </div>
      <div className="d-flex fw-400 f-14 text-dGrey"></div>
    </FormControl>
  );
};
const AddRoleComponent = ({ handleClose }) => {
  const [openOrClose, handleOpenOrClose] = useBooleanState({ defaultValue: true });
  const [category, handleChange] = useInputState({ defaultValue: 'cfpc' });
  const requestData = useRef({});
  return (
    <section className="w-100 border rounded bl-2 px-2 py-3 mt-3 rolesAccordBorder order-3">
      <RoleName
        requestData={requestData}
        openOrClose={openOrClose}
        handleOpenOrClose={handleOpenOrClose}
        handleClose={handleClose}
        roleId={''}
      >
        <DoneIcon
          fontSize="medium"
          className="ml-3 cursor-pointer"
          color="primary"
          sx={{ marginBottom: '5px' }}
        />
      </RoleName>
      {openOrClose ? (
        <div className="mx-2 px-4 pt-2">
          <RoleCategory category={category} handleChange={handleChange} />
        </div>
      ) : (
        <></>
      )}
    </section>
  );
};

const Level = ['Course', 'Program', 'Institution'];
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const ViewOrUpdateRole = ({ name, requestData, openOrClose, handleOpenOrClose, roleId = '' }) => {
  const [edit, onEdit] = useBooleanState();
  function onClickEdit(e) {
    preventDefault(e);
    onEdit(e);
  }
  if (edit)
    return (
      <RoleName
        requestData={requestData}
        openOrClose={openOrClose}
        handleOpenOrClose={handleOpenOrClose}
        defaultValue={name}
        roleId={roleId}
        handleClose={(e) => onEdit(e)}
      >
        <DoneIcon
          fontSize="medium"
          className="ml-3 cursor-pointer"
          color="primary"
          sx={{ marginBottom: '5px' }}
        />
      </RoleName>
    );
  return (
    <AccordComponent state={openOrClose} updateState={handleOpenOrClose}>
      <div className="f-14 fw-500 text-dGrey ml-2">Q360 - {name}</div>
      <>
        {openOrClose && (
          <div onClick={onClickEdit} className="text-lGrey">
            <ModeEditIcon
              fontSize="small"
              className="ml-3 cursor-pointer"
              sx={{ marginBottom: '5px' }}
            />
          </div>
        )}
      </>
    </AccordComponent>
    // </div>
  );
};
export const ShowAllRole = ({ allRoles }) => {
  //const [open, setOpen] = useState(-1);
  const open = useSelector(selectSetOpen);
  const [updateApi] = useCallApiHook(setData);
  const isLoading = useSelector(selectIsLoading);
  const [category, handleChange] = useInputState();

  const onOpenAccord = (index, role) => () => {
    if (open === index) updateApi(fromJS({ open: -1 }));
    else {
      updateApi(fromJS({ open: index }));
      handleChange({ target: { value: role.get('categoryName', 'cfpc') } });
    }
  };
  const requestData = useRef({});
  if (allRoles.size === 0 && !isLoading)
    return (
      <div className="w-100 order-4 p-3 text-center">
        No Roles Found ,click add role to create new role{' '}
      </div>
    );

  // className="w-100 order-4 border rounded bl-2 px-2 py-3 mt-3 rolesAccordBorder"
  return (
    <CategoryData requestData={requestData} category={category}>
      {allRoles.map((role, roleIndex) => (
        <section
          key={role.get('_id', '')}
          className="w-100 border rounded bl-2 px-2 py-3 mt-3 rolesAccordBorder order-4"
        >
          <ViewOrUpdateRole
            openOrClose={open === roleIndex}
            handleOpenOrClose={onOpenAccord(roleIndex, role)}
            requestData={requestData}
            name={role.get('roleName', '')}
            roleId={role.get('_id', '')}
          />
          {open === roleIndex && (
            <div className="mx-2 px-4 pt-2">
              <RoleCategory category={category} handleChange={handleChange} />
              <AddLevel roleId={role.get('_id', '')} existingLevels={role.get('levels', List())} />
            </div>
          )}
        </section>
      ))}
    </CategoryData>
  );
};

const AddLevel = ({ roleId, existingLevels }) => {
  const [level, setLevel] = useState(List());
  const openRef = useRef(false);
  const [editedLevel, setEditedLevel] = useState([]);

  const addLevel = () => {
    setLevel((prev) => prev.push(Map()));
  };
  const onChangeLevel = (index) => (event) => {
    preventDefault(event);
    const value = event.target.value;
    openRef.current = true;
    setLevel((prev) => prev.setIn([index, 'levelName'], value));
  };
  const handleClick = (e, levelItem) => {
    setLevel((prev) =>
      prev.push(
        Map({
          levelName: jsUcfirst(levelItem),
        })
      )
    );
    setEditedLevel((prev) => [...prev, levelItem]);
    openRef.current = true;
  };

  return (
    <>
      <div className="border border-right-0 border-left-0 py-2 px-3 mt-2 rolesTitle d-flex justify-content-between">
        <div className="f-13 fw-400 text-lGrey">Levels</div>
        {level.size < 3 ? (
          <div className="f-14 fw-500 text-primary" onClick={addLevel}>
            + Level
          </div>
        ) : (
          <></>
        )}
      </div>
      <Stack direction="row" spacing={1} className="mt-2">
        {existingLevels
          .filter((levelItem) => !editedLevel.includes(levelItem))
          .map((levelItem) => {
            return (
              <Chip
                variant="outlined"
                key={levelItem}
                value={levelItem}
                label={jsUcfirst(levelItem)}
                onClick={(e) => handleClick(e, levelItem)}
                onDelete={(e) => handleClick(e, levelItem)}
                deleteIcon={<ModeEditIcon fontSize="small" sx={{ color: '#6b7280 !important' }} />}
              />
            );
          })}
      </Stack>
      {level.map((currentLevel, currentIndex) => {
        const levelName = currentLevel.get('levelName', '');
        const selectedLevel = levelName ? levelName.concat(' Level') : '';
        return (
          <AccordComponentWithState
            key={currentIndex}
            className="mt-3"
            defaultOpen={openRef.current}
          >
            <>
              <Select
                displayEmpty
                value={selectedLevel}
                renderValue={(value) => {
                  if (value === '') return <span className="f-14 text-lGrey">Select Level</span>;
                  return value;
                }}
                input={<OutlinedInput />}
                className="w-20"
                MenuProps={MenuProps}
                size="small"
                onChange={onChangeLevel(currentIndex)}
                onClick={preventDefault}
                disabled={editedLevel.includes(levelName.toLowerCase())}
              >
                {Level.filter((levelItem) => !existingLevels.includes(levelItem.toLowerCase())).map(
                  (currentLevel) => (
                    <MenuItem
                      key={currentLevel}
                      value={currentLevel}
                      onClick={preventDefault}
                      onChange={preventDefault}
                      disabled={
                        !Level.filter((data) =>
                          level.every((check) => !check.get('levelName', '').includes(data))
                        ).includes(currentLevel)
                      }
                    >
                      {`${currentLevel} Level`}
                    </MenuItem>
                  )
                )}
              </Select>
              {/* {!currentLevel.isEmpty() ? (
                <span className="text-red bold ml-2" onClick={() => removeLevel(currentIndex)}>
                  X
                </span>
              ) : (
                ''
              )} */}
            </>
            {currentLevel.get('levelName', '') !== '' ? (
              <LevelData level={currentLevel.get('levelName', '').toLowerCase()} roleId={roleId} />
            ) : (
              <div className="text-center ml-2 text-gray">Please Select the Level to Configure</div>
            )}
          </AccordComponentWithState>
        );
      })}
    </>
  );
};
const LevelData = ({ level, roleId }) => {
  const {
    getCategoryData,
    fetchCategoryData,
    category,
    actionData,
    retrieveActionData,
    rolesStaffList,
    retrieveRolesStaffList,
    subModuleList,
    retrieveSubModuleList,
    staffList,
    retrieveStaffList,
    fetchAssignedUserList,
    academicYearList,
    retrieveAcademicYearList,
  } = useQapcContext();
  const categoryData = getCategoryData({
    hierarchy: category,
    level,
  });
  useEffect(() => {
    const callBack = () => {
      fetchAssignedUserList({ qapcRoleId: roleId, hierarchy: category, levelName: level });
    };
    //if (categoryData.size === 0)
    fetchCategoryData(
      {
        params: {
          hierarchy: category,
          level,
        },
      },
      callBack
    );
  }, [level, category]);

  useEffect(() => {
    if (actionData.size === 0) retrieveActionData();
  }, [actionData]);

  useEffect(() => {
    if (rolesStaffList.size === 0) retrieveRolesStaffList();
  }, [rolesStaffList]);

  useEffect(() => {
    if (subModuleList.size === 0) retrieveSubModuleList();
  }, [subModuleList]);

  useEffect(() => {
    if (staffList.size === 0) retrieveStaffList();
  }, [staffList]);

  useEffect(() => {
    if (academicYearList.size === 0) retrieveAcademicYearList();
  }, [academicYearList]);

  const Component = category === 'pccf' ? ProgramTreeView : CategoryTreeView1;

  return (
    <>
      <div className="py-2 px-3 ml-4 mt-2 rolesTitle d-flex justify-content-between">
        <div className="f-13 fw-400 text-lGrey">
          {category === 'pccf' ? 'All Programs' : 'Categories'}
        </div>
      </div>
      <Component categoryData={categoryData} level={level} roleId={roleId} />
    </>
  );
};

export function constructCategoryData(category, params) {
  const { hierarchy } = params;
  if (hierarchy === QAPC_GROUPED_BY_CATEGORY) {
    return category.groupBy((item) => item.getIn(['categoryId', 'categoryName'], ''));
  }
  let map = Map();
  category.forEach((singleCategory) => {
    singleCategory.get('selectedProgram', List()).forEach((program) => {
      const cat = singleCategory.get('categoryId', Map());
      const categoryId = cat.get('_id', '');
      const categoryName = cat.get('categoryName', '');
      const formId = singleCategory.get('_id', '');
      const formName = singleCategory.get('formName', '');
      const programId = program.get('programId', '');
      if (map.getIn(['program', programId], Map()).size === 0) {
        map = map.updateIn(['program', programId], Map(), (data) => {
          data = data
            .update('categoryFormIds', List(), (data) => data.push(singleCategory.get('_id', '')))
            .set('programName', program.get('programName', ''))
            .set('programId', programId);
          return data;
        });
      }
      if (map.getIn(['program', programId, 'categories', categoryId], Map()).size === 0) {
        map = map.updateIn(
          ['program', programId, 'categories', categoryId].slice(0, -1),
          Map(),
          (updateData) => {
            updateData = updateData.set(
              categoryId,
              fromJS({
                name: categoryName,
                childKey: 'forms',
                subDataKey: ['program', programId, 'categories', categoryId],
                forms: {},
              })
            );
            return updateData;
          }
        );
      }
      if (
        map.getIn(['program', programId, 'categories', categoryId, 'forms', formId], Map()).size ===
        0
      ) {
        map = map.updateIn(
          ['program', programId, 'categories', categoryId, 'forms', formId].slice(0, -1),
          Map(),
          (updateData) => {
            updateData = updateData.set(
              formId,
              fromJS({
                name: formName,
                formId,
                isApiCall: true,
              })
            );
            return updateData;
          }
        );
      }
    });
    singleCategory.get('selectedInstitution', List()).forEach((institution) => {
      const institutionId = institution.get('assignedInstitutionId', '');
      const institutionName = institution.get('institutionName', '');
      const cat = singleCategory.get('categoryId', Map());
      const categoryId = cat.get('_id', '');
      const categoryName = cat.get('categoryName', '');
      const formId = singleCategory.get('_id', '');
      const formName = singleCategory.get('formName', '');
      if (map.getIn(['institution', institutionId], Map()).size === 0) {
        map = map.updateIn(['institution', institutionId], Map(), (data) => {
          data = data
            .update('categoryFormIds', List(), (data) => data.push(singleCategory.get('_id', '')))
            .set('institutionName', institutionName)
            .set('institutionId', institutionId);
          return data;
        });
      }
      if (map.getIn(['institution', institutionId, 'categories', categoryId], Map()).size === 0) {
        map = map.updateIn(
          ['institution', institutionId, 'categories', categoryId].slice(0, -1),
          Map(),
          (updateData) => {
            updateData = updateData.set(
              categoryId,
              fromJS({
                name: categoryName,
                childKey: 'forms',
                subDataKey: ['institution', institutionId, 'categories', categoryId],
                forms: {},
              })
            );
            return updateData;
          }
        );
      }
      if (
        map.getIn(['institution', institutionId, 'categories', categoryId, 'forms', formId], Map())
          .size === 0
      ) {
        map = map.updateIn(
          ['institution', institutionId, 'categories', categoryId, 'forms', formId].slice(0, -1),
          Map(),
          (updateData) => {
            updateData = updateData.set(
              formId,
              fromJS({
                name: formName,
                formId,
                isApiCall: true,
              })
            );
            return updateData;
          }
        );
      }
    });
  });
  return map;
}

export const getFormCourseGroupData = (categorySubData) => (courseId, previousKey = List()) => {
  //let formCourseGroupData = Map();
  const academicYearList = JSON.parse(
    LocalStorageService.getCustomToken('rolePermission_qapcAcademicYear')
  );

  function getProgramData(categoryFormCourseId) {
    const findProgramData = categorySubData
      .get('categoryFormCourseData', List())
      .find((item) => item.get('_id', '') === categoryFormCourseId);
    if (findProgramData) {
      return {
        programId: findProgramData.get('programId', ''),
        curriculumId: findProgramData.get('curriculumId', ''),
        year: findProgramData.get('year', ''),
        courseId: findProgramData.get('courseId', ''),
      };
    }
    return {};
  }

  const transformToNestedStructure = (array, courseId) => {
    const result = {};
    array.forEach((item) => {
      const { categoryFormCourseId, term, attemptTypeName, groupName, group, academicYear } = item;
      const checkCourse = categoryFormCourseId === courseId;
      const termCondition = term !== undefined && term !== 'none';
      const attemptTypeNameCondition = attemptTypeName !== undefined && attemptTypeName !== 'none';
      const groupCondition = group !== undefined && group !== 'none';
      const academicYearCondition = academicYear && academicYear !== 'none';
      const programData = getProgramData(categoryFormCourseId);

      if (checkCourse && termCondition) {
        if (!result.term) result.term = {};
        if (!result.term[term]) {
          result.term[term] = {
            childKey: attemptTypeNameCondition
              ? 'attemptTypeName'
              : groupCondition
              ? 'group'
              : 'academicYear',
            name: term,
            uniqueId: term,
            attemptTypeName: {},
            group: {},
            academicYear: {},
            subDataKey: [...previousKey, 'term', term],
            ...programData,
          };
        }

        if (attemptTypeNameCondition) {
          const attemptTypeObj = result.term[term].attemptTypeName;
          if (!attemptTypeObj[attemptTypeName]) {
            attemptTypeObj[attemptTypeName] = {
              childKey: groupCondition ? 'group' : 'academicYear',
              name: attemptTypeName,
              uniqueId: `${term}+${attemptTypeName}`,
              term,
              group: {},
              academicYear: {},
              subDataKey: [...previousKey, 'term', term, 'attemptTypeName', attemptTypeName],
              ...programData,
            };
          }

          if (groupCondition) {
            const groupObj = attemptTypeObj[attemptTypeName].group;
            if (!groupObj[groupName]) {
              groupObj[groupName] = {
                childKey: 'academicYear',
                name: groupName,
                uniqueId: `${term}+${attemptTypeName}+${groupName}`,
                academicYear: {},
                term,
                attemptTypeName,
                subDataKey: [
                  ...previousKey,
                  'term',
                  term,
                  'attemptTypeName',
                  attemptTypeName,
                  'group',
                  groupName,
                ],
                ...programData,
              };
            }

            if (academicYearCondition) {
              let constructData = Map();
              if (academicYear === 'every') {
                fromJS(academicYearList).forEach((aYear) => {
                  const calendarName = aYear.get('calendar_name', '');
                  const calendarId = aYear.get('_id', '');
                  constructData = constructData.set(
                    calendarId,
                    fromJS({
                      name: calendarName,
                      courseId: courseId,
                      isActionLabel: '',
                      uniqueId: `${calendarId}+${calendarName}`,
                      subDataKey: [
                        ...previousKey,
                        'term',
                        term,
                        'attemptTypeName',
                        attemptTypeName,
                        'group',
                        groupName,
                        'academicYear',
                        academicYear,
                        'all academic Year',
                        calendarId,
                      ],
                      isAcademicYear: true,
                      calendarId,
                      groupName,
                      attemptTypeName,
                      term,
                      ...programData,
                    })
                  );
                });
              }
              groupObj[groupName].academicYear[academicYear] = {
                name: academicYear,
                uniqueId: `${term}+${attemptTypeName}+${groupName}+${academicYear}`,
                term,
                attemptTypeName,
                groupName,
                subDataKey: [
                  ...previousKey,
                  'term',
                  term,
                  'attemptTypeName',
                  attemptTypeName,
                  'group',
                  groupName,
                  'academicYear',
                  academicYear,
                ],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          } else if (!groupCondition) {
            const academicYearObj = attemptTypeObj[attemptTypeName].academicYear;
            if (academicYearCondition) {
              let constructData = Map();
              if (academicYear === 'every') {
                fromJS(academicYearList).forEach((aYear) => {
                  const calendarName = aYear.get('calendar_name', '');
                  const calendarId = aYear.get('_id', '');
                  constructData = constructData.set(
                    calendarId,
                    fromJS({
                      name: calendarName,
                      courseId: courseId,
                      isActionLabel: '',
                      uniqueId: `${calendarId}+${calendarName}`,
                      subDataKey: [
                        ...previousKey,
                        'term',
                        term,
                        'attemptTypeName',
                        attemptTypeName,
                        'academicYear',
                        academicYear,
                        'all academic Year',
                        calendarId,
                      ],
                      isAcademicYear: true,
                      calendarId,
                      attemptTypeName,
                      term,
                      ...programData,
                    })
                  );
                });
              }
              academicYearObj[academicYear] = {
                name: academicYear,
                uniqueId: `${term}+${attemptTypeName}+${academicYear}`,
                term,
                attemptTypeName,
                subDataKey: [
                  ...previousKey,
                  'term',
                  term,
                  'attemptTypeName',
                  attemptTypeName,
                  'academicYear',
                  academicYear,
                ],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          }
        } else if (!attemptTypeNameCondition) {
          if (groupCondition) {
            const groupObj = result.term[term].group;
            if (!groupObj[groupName]) {
              groupObj[groupName] = {
                childKey: 'academicYear',
                name: groupName,
                uniqueId: `${term}+${groupName}`,
                academicYear: {},
                term,
                attemptTypeName,
                subDataKey: [...previousKey, 'term', term, 'group', groupName],
                ...programData,
              };
            }

            if (academicYearCondition) {
              let constructData = Map();
              if (academicYear === 'every') {
                fromJS(academicYearList).forEach((aYear) => {
                  const calendarName = aYear.get('calendar_name', '');
                  const calendarId = aYear.get('_id', '');
                  constructData = constructData.set(
                    calendarId,
                    fromJS({
                      name: calendarName,
                      courseId: courseId,
                      isActionLabel: '',
                      uniqueId: `${calendarId}+${calendarName}`,
                      subDataKey: [
                        ...previousKey,
                        'term',
                        term,
                        'group',
                        groupName,
                        'academicYear',
                        academicYear,
                        'all academic Year',
                        calendarId,
                      ],
                      isAcademicYear: true,
                      calendarId,
                      groupName,
                      attemptTypeName,
                      term,
                      ...programData,
                    })
                  );
                });
              }
              groupObj[groupName].academicYear[academicYear] = {
                name: academicYear,
                uniqueId: `${term}+${groupName}+${academicYear}`,
                term,
                attemptTypeName,
                groupName,
                subDataKey: [
                  ...previousKey,
                  'term',
                  term,
                  'group',
                  groupName,
                  'academicYear',
                  academicYear,
                ],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          } else if (!groupCondition) {
            const academicYearObj = result.term[term].academicYear;
            if (academicYearCondition) {
              let constructData = Map();
              if (academicYear === 'every') {
                fromJS(academicYearList).forEach((aYear) => {
                  const calendarName = aYear.get('calendar_name', '');
                  const calendarId = aYear.get('_id', '');
                  constructData = constructData.set(
                    calendarId,
                    fromJS({
                      name: calendarName,
                      courseId: courseId,
                      isActionLabel: '',
                      uniqueId: `${calendarId}+${calendarName}`,
                      subDataKey: [
                        ...previousKey,
                        'term',
                        term,
                        'academicYear',
                        academicYear,
                        'all academic Year',
                        calendarId,
                      ],
                      isAcademicYear: true,
                      calendarId,
                      term,
                      ...programData,
                    })
                  );
                });
              }
              academicYearObj[academicYear] = {
                name: academicYear,
                uniqueId: `${term}+${academicYear}`,
                term,
                attemptTypeName,
                subDataKey: [...previousKey, 'term', term, 'academicYear', academicYear],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          }
        }
      } else if (checkCourse && !termCondition) {
        if (attemptTypeNameCondition) {
          if (!result.attemptTypeName) result.attemptTypeName = {};
          const attemptTypeObj = result.attemptTypeName;
          if (!attemptTypeObj[attemptTypeName]) {
            attemptTypeObj[attemptTypeName] = {
              childKey: groupCondition ? 'group' : 'academicYear',
              name: attemptTypeName,
              uniqueId: `${attemptTypeName}`,
              group: {},
              academicYear: {},
              subDataKey: [...previousKey, 'attemptTypeName', attemptTypeName],
              ...programData,
            };
          }

          if (groupCondition) {
            const groupObj = attemptTypeObj[attemptTypeName].group;
            if (!groupObj[groupName]) {
              groupObj[groupName] = {
                childKey: 'academicYear',
                name: groupName,
                uniqueId: `${attemptTypeName}+${groupName}`,
                academicYear: {},
                attemptTypeName,
                subDataKey: [
                  ...previousKey,
                  'attemptTypeName',
                  attemptTypeName,
                  'group',
                  groupName,
                ],
                ...programData,
              };
            }

            if (academicYearCondition) {
              let constructData = Map();
              if (academicYear === 'every') {
                fromJS(academicYearList).forEach((aYear) => {
                  const calendarName = aYear.get('calendar_name', '');
                  const calendarId = aYear.get('_id', '');
                  constructData = constructData.set(
                    calendarId,
                    fromJS({
                      name: calendarName,
                      courseId: courseId,
                      isActionLabel: '',
                      uniqueId: `${calendarId}+${calendarName}`,
                      subDataKey: [
                        ...previousKey,
                        'attemptTypeName',
                        attemptTypeName,
                        'group',
                        groupName,
                        'academicYear',
                        academicYear,
                        'all academic Year',
                        calendarId,
                      ],
                      isAcademicYear: true,
                      calendarId,
                      groupName,
                      attemptTypeName,
                      ...programData,
                    })
                  );
                });
              }
              groupObj[groupName].academicYear[academicYear] = {
                name: academicYear,
                uniqueId: `${attemptTypeName}+${groupName}+${academicYear}`,
                attemptTypeName,
                groupName,
                subDataKey: [
                  ...previousKey,
                  'attemptTypeName',
                  attemptTypeName,
                  'group',
                  groupName,
                  'academicYear',
                  academicYear,
                ],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          } else if (!groupCondition) {
            const academicYearObj = attemptTypeObj[attemptTypeName].academicYear;
            if (academicYearCondition) {
              let constructData = Map();
              if (academicYear === 'every') {
                fromJS(academicYearList).forEach((aYear) => {
                  const calendarName = aYear.get('calendar_name', '');
                  const calendarId = aYear.get('_id', '');
                  constructData = constructData.set(
                    calendarId,
                    fromJS({
                      name: calendarName,
                      courseId: courseId,
                      isActionLabel: '',
                      uniqueId: `${calendarId}+${calendarName}`,
                      subDataKey: [
                        ...previousKey,
                        'attemptTypeName',
                        attemptTypeName,
                        'academicYear',
                        academicYear,
                        'all academic Year',
                        calendarId,
                      ],
                      isAcademicYear: true,
                      calendarId,
                      attemptTypeName,
                      ...programData,
                    })
                  );
                });
              }
              academicYearObj[academicYear] = {
                name: academicYear,
                uniqueId: `${attemptTypeName}+${academicYear}`,
                attemptTypeName,
                subDataKey: [
                  ...previousKey,
                  'attemptTypeName',
                  attemptTypeName,
                  'academicYear',
                  academicYear,
                ],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          }
        } else if (!attemptTypeNameCondition) {
          if (groupCondition) {
            if (!result.group) result.group = {};
            const groupObj = result.group;
            if (!groupObj[groupName]) {
              groupObj[groupName] = {
                childKey: 'academicYear',
                name: groupName,
                uniqueId: `${groupName}`,
                academicYear: {},
                subDataKey: [...previousKey, 'group', groupName],
                ...programData,
              };
            }

            if (academicYearCondition) {
              let constructData = Map();
              if (academicYear === 'every') {
                fromJS(academicYearList).forEach((aYear) => {
                  const calendarName = aYear.get('calendar_name', '');
                  const calendarId = aYear.get('_id', '');
                  constructData = constructData.set(
                    calendarId,
                    fromJS({
                      name: calendarName,
                      courseId: courseId,
                      isActionLabel: '',
                      uniqueId: `${calendarId}+${calendarName}`,
                      subDataKey: [
                        ...previousKey,
                        'group',
                        groupName,
                        'academicYear',
                        academicYear,
                        'all academic Year',
                        calendarId,
                      ],
                      isAcademicYear: true,
                      calendarId,
                      groupName,
                      ...programData,
                    })
                  );
                });
              }
              groupObj[groupName].academicYear[academicYear] = {
                name: academicYear,
                uniqueId: `${groupName}+${academicYear}`,
                groupName,
                subDataKey: [...previousKey, 'group', groupName, 'academicYear', academicYear],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          } else if (!groupCondition) {
            // if (!result.academicYearName)
            //   result.academicYearName = {
            //     childKey: 'academicYear',
            //     subDataKey: [...previousKey],
            //     academicYear: {},
            //   };

            // const academicYearObj = result.academicYearName;

            // if (academicYearCondition) {
            //   let constructData = Map();
            //   if (academicYear === 'every') {
            //     fromJS(academicYearList).forEach((aYear) => {
            //       const calendarName = aYear.get('calendar_name', '');
            //       const calendarId = aYear.get('_id', '');
            //       constructData = constructData.set(
            //         calendarId,
            //         fromJS({
            //           name: calendarName,
            //           courseId: courseId,
            //           isActionLabel: '',
            //           uniqueId: `${calendarId}+${calendarName}`,
            //           subDataKey: [
            //             ...previousKey,
            //             'academicYear',
            //             academicYear,
            //             'all academic Year',
            //             calendarId,
            //           ],
            //           isAcademicYear: true,
            //           calendarId,
            //           ...programData,
            //         })
            //       );
            //     });
            //   }
            //   academicYearObj[academicYear] = {
            //     name: academicYear,
            //     uniqueId: `${academicYear}`,
            //     subDataKey: [...previousKey, 'academicYear', academicYear],
            //     ...(academicYear === 'every' && {
            //       childKey: 'all academic Year',
            //       'all academic Year': constructData,
            //     }),
            //     ...programData,
            //   };
            // }

            let constructData = Map();
            if (academicYear === 'every') {
              fromJS(academicYearList).forEach((aYear) => {
                const calendarName = aYear.get('calendar_name', '');
                const calendarId = aYear.get('_id', '');
                constructData = constructData.set(
                  calendarId,
                  fromJS({
                    name: calendarName,
                    courseId: courseId,
                    isActionLabel: '',
                    uniqueId: `${calendarId}+${calendarName}`,
                    subDataKey: [
                      ...previousKey,
                      'academicYear',
                      academicYear,
                      'all academic Year',
                      calendarId,
                    ],
                    isAcademicYear: true,
                    calendarId,
                    ...programData,
                  })
                );
              });
            }

            if (!result.academicYear) result.academicYear = {};
            const academicYearObj = result.academicYear;
            if (!academicYearObj[academicYear]) {
              academicYearObj[academicYear] = {
                name: academicYear,
                uniqueId: `${academicYear}`,
                academicYear: {},
                subDataKey: [...previousKey, 'academicYear', academicYear],
                ...(academicYear === 'every' && {
                  childKey: 'all academic Year',
                  'all academic Year': constructData,
                }),
                ...programData,
              };
            }
          }
        } else if (academicYearCondition) {
          let constructData = Map();
          if (academicYear === 'every') {
            fromJS(academicYearList).forEach((aYear) => {
              const calendarName = aYear.get('calendar_name', '');
              const calendarId = aYear.get('_id', '');
              constructData = constructData.set(
                calendarId,
                fromJS({
                  name: calendarName,
                  courseId: courseId,
                  isActionLabel: '',
                  uniqueId: `${calendarId}+${calendarName}`,
                  subDataKey: [
                    ...previousKey,
                    'academicYear',
                    academicYear,
                    'all academic Year',
                    calendarId,
                  ],
                  isAcademicYear: true,
                  calendarId,
                  ...programData,
                })
              );
            });
          }

          if (!result.academicYear) result.academicYear = {};
          const academicYearObj = result.academicYear;
          if (!academicYearObj[academicYear]) {
            academicYearObj[academicYear] = {
              name: academicYear,
              uniqueId: `${academicYear}`,
              academicYear: {},
              subDataKey: [...previousKey, 'academicYear', academicYear],
              ...(academicYear === 'every' && {
                childKey: 'all academic Year',
                'all academic Year': constructData,
              }),
              ...programData,
            };
          }
        }
      }
      // else if (checkCourse && !termCondition && !attemptTypeNameCondition) {
      //   if (!result.academicYearName)
      //     result.academicYearName = {
      //       childKey: 'academicYear',
      //       subDataKey: [...previousKey],
      //       academicYear: {},
      //     };

      //   if (academicYearCondition) {
      //     let constructData = Map();
      //     if (academicYear === 'every') {
      //       fromJS(academicYearList).forEach((aYear) => {
      //         const calendarName = aYear.get('calendar_name', '');
      //         const calendarId = aYear.get('_id', '');
      //         constructData = constructData.set(
      //           calendarId,
      //           fromJS({
      //             name: calendarName,
      //             uniqueId: `${calendarId}+${calendarName}`,
      //             subDataKey: [
      //               ...previousKey,
      //               'academicYear',
      //               academicYear,
      //               'all academic Year',
      //               calendarId,
      //             ],
      //             isAcademicYear: true,
      //             calendarId,
      //             ...programData,
      //           })
      //         );
      //       });
      //     }
      //     if (!result.academicYearName.academicYear[academicYear]) {
      //       result.academicYearName.academicYear[academicYear] = {
      //         childKey: 'academicYear',
      //         name: academicYear,
      //         uniqueId: academicYear,
      //         subDataKey: [...previousKey, 'academicYear', academicYear],
      //         ...programData,
      //         ...(academicYear === 'every' && {
      //           childKey: 'all academic Year',
      //           'all academic Year': constructData,
      //         }),
      //       };
      //     }
      //   }
      // }
    });
    return result;
  };

  const transformedData = transformToNestedStructure(
    categorySubData.get('categoryGroupData', List()).toJS(),
    courseId
  );

  const formattedData = fromJS(transformedData);
  if (
    formattedData.has('term') ||
    formattedData.has('attemptTypeName') ||
    formattedData.has('group') ||
    formattedData.has('academicYear')
  ) {
    return fromJS({
      childKey: formattedData.has('term')
        ? 'term'
        : formattedData.has('attemptTypeName')
        ? 'attemptTypeName'
        : formattedData.has('group')
        ? 'group'
        : 'academicYear',
      subDataKey: [...previousKey],
      ...transformedData,
    });
  }
  if (formattedData.has('academicYearName')) {
    return fromJS({
      childKey: 'academicYear',
      subDataKey: [...previousKey],
      ...transformedData.academicYearName,
    });
  }

  // categorySubData.get('categoryGroupData', List()).forEach((actions, i) => {
  //   const _id = actions.get('_id', '');
  //   const term = actions.get('term', '');
  //   const attemptTypeName = actions.get('attemptTypeName', '');
  //   const categoryFormCourseId = actions.get('categoryFormCourseId', '');
  //   const checkCourse = categoryFormCourseId === courseId;
  //   const academicYear = actions.get('academicYear', '');
  //   const group = actions.get('groupName', '');
  //   const termCondition = actions.has('term') && term !== 'none';
  //   const attemptTypeNameCondition = actions.has('attemptTypeName') && attemptTypeName !== 'none';
  //   const groupCondition = actions.has('groupName') && group !== 'none';
  //   const academicYearCondition = academicYear && academicYear !== 'none';

  //   const programData = getProgramData(categoryFormCourseId);
  //   if (termCondition && checkCourse) {
  //     const termData = fromJS({
  //       name: term,
  //       _id: `${courseId}+${_id}`,
  //       courseId: courseId,
  //       uniqueId: term,
  //       subDataKey: [...previousKey, 'term', term],
  //       childKey: 'attemptTypeName',
  //       attemptTypeName: {},
  //       ...programData,
  //     });
  //     formCourseGroupData = formCourseGroupData.updateIn(previousKey, Map(), (update) => {
  //       update = update
  //         .set('childKey', 'term')
  //         .set('subDataKey', List(previousKey))
  //         .setIn(['term', term], termData);
  //       return update;
  //     });
  //   }

  //   if (attemptTypeNameCondition && checkCourse) {
  //     if (termCondition && checkCourse) {
  //       let key = [...previousKey, 'term', term, 'attemptTypeName', attemptTypeName];
  //       formCourseGroupData = formCourseGroupData.updateIn(key.slice(0, -1), Map(), (update) => {
  //         return update.set(
  //           attemptTypeName,
  //           fromJS({
  //             name: attemptTypeName,
  //             subDataKey: key,
  //             courseId: courseId,
  //             _id: `${courseId}+${_id}`,
  //             uniqueId: `${term}+${attemptTypeName}`,
  //             childKey: 'group',
  //             group: {},
  //             term,
  //             ...programData,
  //           })
  //         );
  //       });
  //     } else if (checkCourse) {
  //       const termData = fromJS({
  //         name: attemptTypeName,
  //         _id: `${courseId}+${_id}`,
  //         subDataKey: [...previousKey, 'attemptTypeName', attemptTypeName],
  //         childKey: 'group',
  //         courseId: courseId,
  //         uniqueId: attemptTypeName,
  //         group: {},
  //         term,
  //         ...programData,
  //       });
  //       formCourseGroupData = formCourseGroupData.updateIn(previousKey, Map(), (update) => {
  //         update = update
  //           .set('childKey', 'attemptTypeName')
  //           .set('subDataKey', List(previousKey))
  //           .setIn(['attemptTypeName', attemptTypeName], termData);
  //         return update;
  //       });
  //     }
  //   }
  //   if (groupCondition && checkCourse) {
  //     let key = [...previousKey, 'term', term, 'attemptTypeName', attemptTypeName, 'group', group];
  //     if (termCondition && attemptTypeNameCondition && checkCourse) {
  //       formCourseGroupData = formCourseGroupData.updateIn(key.slice(0, -1), Map(), (update) => {
  //         return update.set(
  //           group,
  //           fromJS({
  //             name: group,
  //             subDataKey: key,
  //             _id: `${courseId}+${_id}`,
  //             courseId: courseId,
  //             childKey: 'academicYear',
  //             uniqueId: `${term}+${attemptTypeName}+${group}`,
  //             academicYear: {},
  //             attemptTypeName,
  //             term,
  //             ...programData,
  //           })
  //         );
  //       });
  //     } else if (attemptTypeNameCondition && checkCourse) {
  //       let key = [...previousKey, 'attemptTypeName', attemptTypeName, 'group', group];
  //       formCourseGroupData = formCourseGroupData.updateIn(key.slice(0, -1), Map(), (update) => {
  //         return update.set(
  //           group,
  //           fromJS({
  //             name: group,
  //             subDataKey: key,
  //             courseId: courseId,
  //             _id: `${courseId}+${_id}`,
  //             uniqueId: `${attemptTypeName}+${group}`,
  //             childKey: 'academicYear',
  //             academicYear: {},
  //             attemptTypeName,
  //             groupName: group,
  //             ...programData,
  //           })
  //         );
  //       });
  //     } else if (checkCourse) {
  //       const termData = fromJS({
  //         name: group,
  //         subDataKey: [...previousKey, 'group', group],
  //         childKey: 'academicYear',
  //         courseId: courseId,
  //         uniqueId: academicYear,
  //         _id: `${courseId}+${_id}`,
  //         academicYear: {},
  //         groupName: group,
  //         ...programData,
  //       });
  //       formCourseGroupData = formCourseGroupData.updateIn(previousKey, Map(), (update) => {
  //         update = update
  //           .set('childKey', 'group')
  //           .set('subDataKey', List(previousKey))
  //           .setIn(['group', group], termData);
  //         return update;
  //       });
  //     }
  //   }
  //   if (academicYearCondition && checkCourse) {
  //     if (
  //       termCondition &&
  //       attemptTypeNameCondition &&
  //       groupCondition &&
  //       academicYearCondition &&
  //       checkCourse
  //     ) {
  //       let key = [
  //         ...previousKey,
  //         'term',
  //         term,
  //         'attemptTypeName',
  //         attemptTypeName,
  //         'group',
  //         group,
  //         'academicYear',
  //         academicYear,
  //       ];

  //       let constructData = Map();
  //       if (academicYear === 'every') {
  //         fromJS(academicYearList).forEach((academicYear) => {
  //           const calendarName = academicYear.get('calendar_name', '');
  //           const calendarId = academicYear.get('_id', '');
  //           constructData = constructData.set(
  //             calendarId,
  //             fromJS({
  //               name: calendarName,
  //               courseId: courseId,
  //               isActionLabel: '',
  //               _id: courseId,
  //               uniqueId: `${calendarId}+${calendarName}`,
  //               subDataKey: [...key, 'all academic Year', calendarId],
  //               isAcademicYear: true,
  //               calendarId,
  //               groupName: group,
  //               attemptTypeName,
  //               term,
  //               ...programData,
  //             })
  //           );
  //         });
  //       }

  //       formCourseGroupData = formCourseGroupData.updateIn(key.slice(0, -1), Map(), (update) => {
  //         return update.set(
  //           academicYear,
  //           fromJS({
  //             name: academicYear,
  //             uniqueId: `${term}+${attemptTypeName}+${group}+${academicYear}`,
  //             subDataKey: key,
  //             _id: `${courseId}+${_id}`,
  //             courseId: courseId,
  //             childKey: 'academicYear',
  //             groupName: group,
  //             attemptTypeName,
  //             term,
  //             ...(academicYear === 'every' && {
  //               childKey: 'all academic Year',
  //               'all academic Year': constructData,
  //             }),
  //             ...programData,
  //           })
  //         );
  //       });
  //     } else if (
  //       attemptTypeNameCondition &&
  //       groupCondition &&
  //       academicYearCondition &&
  //       checkCourse
  //     ) {
  //       let key = [
  //         ...previousKey,
  //         'attemptTypeName',
  //         attemptTypeName,
  //         'group',
  //         group,
  //         'academicYear',
  //         academicYear,
  //       ];
  //       formCourseGroupData = formCourseGroupData.updateIn(key.slice(0, -1), Map(), (update) => {
  //         return update.set(
  //           academicYear,
  //           fromJS({
  //             name: academicYear,
  //             uniqueId: `${attemptTypeName}+${group}+${academicYear}`,
  //             subDataKey: key,
  //             courseId: courseId,
  //             _id: `${courseId}+${_id}`,
  //             childKey: 'academicYear',
  //             groupName: group,
  //             attemptTypeName,
  //             ...programData,
  //           })
  //         );
  //       });
  //     } else if (groupCondition && academicYearCondition && checkCourse) {
  //       let key = [...previousKey, 'group', group, 'academicYear', academicYear];
  //       formCourseGroupData = formCourseGroupData.updateIn(key.slice(0, -1), Map(), (update) => {
  //         let constructData = Map();
  //         if (academicYear === 'every') {
  //           fromJS(academicYearList).forEach((academicYear) => {
  //             const calendarName = academicYear.get('calendar_name', '');
  //             const calendarId = academicYear.get('_id', '');
  //             constructData = constructData.set(
  //               calendarId,
  //               fromJS({
  //                 name: calendarName,
  //                 courseId: courseId,
  //                 isActionLabel: '',
  //                 _id: courseId,
  //                 uniqueId: `${calendarId}+${calendarName}`,
  //                 subDataKey: [...key, 'all academic Year', calendarId],
  //                 isAcademicYear: true,
  //                 calendarId,
  //                 groupName: group,
  //                 ...programData,
  //               })
  //             );
  //           });
  //         }
  //         return update.set(
  //           academicYear,
  //           fromJS({
  //             name: academicYear,
  //             subDataKey: key,
  //             uniqueId: `${group}+${academicYear}`,
  //             courseId: courseId,
  //             _id: `${courseId}+${_id}`,
  //             ...(academicYear === 'every' && {
  //               childKey: 'all academic Year',
  //               'all academic Year': constructData,
  //             }),
  //             groupName: group,
  //             ...programData,
  //           })
  //         );
  //       });
  //     } else if (checkCourse) {
  //       const termData = fromJS({
  //         name: academicYear,
  //         subDataKey: [...previousKey, 'academicYear', academicYear],
  //         uniqueId: academicYear,
  //         courseId: courseId,
  //         _id: `${courseId}+${_id}`,
  //         ...programData,
  //       });
  //       formCourseGroupData = formCourseGroupData.updateIn(previousKey, Map(), (update) => {
  //         update = update
  //           .set('childKey', 'academicYear')
  //           .set('subDataKey', List(previousKey))
  //           .setIn(['academicYear', academicYear], termData);
  //         return update;
  //       });
  //     }
  //   }
  // });
  // return formCourseGroupData;
};
export function splitFunctionCfpcWise(categorySubData, level) {
  let formCourseData = categorySubData
    .get('categoryFormCourseData', List())
    .reduce((formMap, formData) => formMap.setIn([formData.get('_id', '')], formData), Map());
  if (level === 'course')
    return cfpcCourseLevel(formCourseData, getFormCourseGroupData(categorySubData), level);
  if (level === 'program')
    return cfpcProgramLevel(formCourseData, getFormCourseGroupData(categorySubData), level);
  if (level === 'institution')
    return cfpcInstitution(formCourseData, getFormCourseGroupData(categorySubData), level);
  return {};
}
function cfpcProgramLevel(formCourseData, getFormCourseGroupData, level) {
  let result = Map();
  formCourseData.forEach((formCourse) => {
    const programId = formCourse.get('programId', '');
    const curriculumId = formCourse.get('curriculumId', '');
    const _id = formCourse.get('_id', '');
    if (result.get(programId, Map()).size === 0) {
      result = result.set(
        programId,
        fromJS({
          programId,
          levelName: level,
          _id: programId,
          name: formCourse.get('programName', ''),
          curriculum: {},
          childKey: 'curriculum',
          subDataKey: [programId],
          grandChildKey: 'year',
        })
      );
    }
    if (result.getIn([programId, 'curriculum', curriculumId], Map()).size === 0) {
      const preparedData = getFormCourseGroupData(_id, [programId, 'curriculum', curriculumId]);
      // getFormCourseGroupData(_id, [
      //   programId,
      //   'curriculum',
      //   curriculumId,
      // ]).getIn([programId, 'curriculum', curriculumId], Map());

      result = result.setIn(
        [programId, 'curriculum', curriculumId],
        fromJS({
          curriculumId,
          programId,
          _id: curriculumId,
          name: formCourse.get('curriculumName', ''),
        }).merge(preparedData)
      );
    }
  });
  return result;
}
function cfpcCourseLevel(formCourseData, getFormCourseGroupData, level) {
  let result = Map();
  formCourseData.forEach((formCourse) => {
    const programId = formCourse.get('programId', '');
    const curriculumId = formCourse.get('curriculumId', '');
    const year = formCourse.get('year', '');
    const courseId = formCourse.get('courseId', '');
    const _id = formCourse.get('_id', '');
    if (result.get(programId, Map()).size === 0) {
      result = result.set(
        programId,
        fromJS({
          programId,
          levelName: level,
          _id: programId,
          name: formCourse.get('programName', ''),
          curriculum: {},
          childKey: 'curriculum',
          subDataKey: [programId],
          grandChildKey: 'year',
        })
      );
    }
    if (result.getIn([programId, 'curriculum', curriculumId], Map()).size === 0) {
      result = result.setIn(
        [programId, 'curriculum', curriculumId],
        fromJS({
          programId,
          curriculumId,
          _id: curriculumId,
          name: formCourse.get('curriculumName', ''),
          year: {},
          childKey: 'year',
          subDataKey: [programId, 'curriculum', curriculumId],
        })
      );
    }
    if (result.getIn([programId, 'curriculum', curriculumId, 'year', year], Map()).size === 0) {
      result = result.setIn(
        [programId, 'curriculum', curriculumId, 'year', year],
        fromJS({
          programId,
          curriculumId,
          name: year,
          _id: year,
          course: {},
          childKey: 'course',
          subDataKey: [programId, 'curriculum', curriculumId, 'year', year],
        })
      );
    }
    if (
      result.getIn([programId, 'curriculum', curriculumId, 'year', year, 'course', courseId], Map())
        .size === 0
    ) {
      const preparedData = getFormCourseGroupData(_id, [
        programId,
        'curriculum',
        curriculumId,
        'year',
        year,
        'course',
        courseId,
      ]);
      // getFormCourseGroupData(_id, [
      //   programId,
      //   'curriculum',
      //   curriculumId,
      //   'year',
      //   year,
      //   'course',
      //   courseId,
      // ]).getIn([programId, 'curriculum', curriculumId, 'year', year, 'course', courseId], Map())
      result = result.setIn(
        [programId, 'curriculum', curriculumId, 'year', year, 'course', courseId],
        fromJS({
          programId,
          curriculumId,
          year,
          courseId,
          _id: courseId,
          name: formCourse.get('courseName', ''),
          courseCode: formCourse.get('courseCode', ''),
        }).merge(preparedData)
      );
    }
  });
  return result;
}
function cfpcInstitution(formCourseData, getFormCourseGroupData, level) {
  let result = Map();
  formCourseData.forEach((formCourse) => {
    const institutionId = formCourse.get('assignedInstitutionId', '');
    const institutionName = formCourse.get('institutionName', '');
    const _id = formCourse.get('_id', '');
    const preparedData = getFormCourseGroupData(_id, [institutionId]);
    if (result.get(institutionId, Map()).size === 0) {
      result = result.set(
        institutionId,
        fromJS({
          levelName: level,
          institutionId,
          name: institutionName,
        }).merge(preparedData)
      );
    }
  });
  return result;
}
function pccfCourseLevel(formCourseData, getCategories) {
  let result = Map();
  let nextSubDataKey = Map();
  formCourseData.forEach((formCourse) => {
    const programId = formCourse.get('programId', '');
    const curriculumId = formCourse.get('curriculumId', '');
    const year = formCourse.get('year', '');
    const courseId = formCourse.get('courseId', '');
    const categoryId = formCourse.get('categoryId', '');
    const categoryFromId = formCourse.get('categoryFormId', '');
    const categoryFormCourseId = formCourse.get('_id', '');
    if (result.get(programId, Map()).size === 0) {
      result = result.set(
        programId,
        fromJS({
          programId,
          name: formCourse.get('programName', ''),
          curriculum: {},
          childKey: 'curriculum',
          subDataKey: [programId],
          grandChildKey: 'year',
        })
      );
    }
    if (result.getIn([programId, 'curriculum', curriculumId], Map()).size === 0) {
      result = result.setIn(
        [programId, 'curriculum', curriculumId],
        fromJS({
          curriculumId,
          name: formCourse.get('curriculumName', ''),
          year: {},
          childKey: 'year',
          subDataKey: [programId, 'curriculum', curriculumId, 'year', year],
          grandChildKey: 'course',
        })
      );
    }
    if (result.getIn([programId, 'curriculum', curriculumId, 'year', year], Map()).size === 0) {
      result = result.setIn(
        [programId, 'curriculum', curriculumId, 'year', year],
        fromJS({
          name: year,
          course: {},
          childKey: 'course',
          subDataKey: [programId, 'curriculum', curriculumId, 'year', year, 'course', courseId],
          grandChildKey: 'term',
        })
      );
    }
    if (
      result.getIn([programId, 'curriculum', curriculumId, 'year', year, 'course', courseId], Map())
        .size === 0
    ) {
      const subDataKey = [
        programId,
        'curriculum',
        curriculumId,
        'year',
        year,
        'course',
        courseId,
        'categories',
        categoryId,
      ];
      nextSubDataKey = nextSubDataKey.set(
        `${programId}+${courseId}`,
        fromJS({ subDataKey, categoryFormCourseId })
      );
      result = result.setIn(
        [programId, 'curriculum', curriculumId, 'year', year, 'course', courseId],
        fromJS({
          courseId,
          name: formCourse.get('courseName', ''),
          formId: categoryFromId,
          subDataKey: subDataKey,
          childKey: 'categories',
          categories: getCategories
            .updateIn(['program', programId, 'categories'], Map(), (updateData) => {
              updateData.forEach((value, key) => {
                value.get('forms', Map()).forEach((formValue, FormKey) => {
                  updateData = updateData.setIn(
                    [key, 'forms', FormKey],
                    formValue.set('courseId', courseId)
                  );
                });
              });
              return updateData;
            })
            .getIn(['program', programId, 'categories'], Map()),
        })
      );
    }
  });
  return { result, nextSubDataKey };
}
function pccfProgramLevel(formProgramData, getCategories) {
  let result = Map();
  let nextSubDataKey = Map();
  formProgramData.forEach((formCourse) => {
    const programId = formCourse.get('programId', '');
    const curriculumId = formCourse.get('curriculumId', '');
    const categoryFromId = formCourse.get('categoryFormId', '');
    const categoryFormCourseId = formCourse.get('_id', '');
    const categoryId = formCourse.get('categoryId', '');
    if (result.get(programId, Map()).size === 0) {
      result = result.set(
        programId,
        fromJS({
          programId,
          _id: programId,
          name: formCourse.get('programName', ''),
          curriculum: {},
          childKey: 'curriculum',
          subDataKey: [programId],
          grandChildKey: 'year',
        })
      );
    }
    if (result.getIn([programId, 'curriculum', curriculumId], Map()).size === 0) {
      const subDataKey = [programId, 'curriculum', curriculumId, 'categories', categoryId];
      nextSubDataKey = nextSubDataKey.set(
        `${programId}+${curriculumId}`,
        fromJS({
          subDataKey: [programId, 'curriculum', curriculumId, 'categories', categoryId],
          categoryFormCourseId,
        })
      );
      result = result.setIn(
        [programId, 'curriculum', curriculumId],
        fromJS({
          curriculumId,
          name: formCourse.get('curriculumName', ''),
          childKey: 'categories',
          _id: curriculumId,
          subDataKey: subDataKey,
          formId: categoryFromId,
          programId,
          categories: getCategories
            .updateIn(['program', programId, 'categories'], Map(), (updateData) => {
              updateData.forEach((value, key) => {
                value.get('forms', Map()).forEach((formValue, FormKey) => {
                  updateData = updateData.setIn(
                    [key, 'forms', FormKey],
                    formValue.set('courseId', curriculumId)
                  );
                });
              });
              return updateData;
            })
            .getIn(['program', programId, 'categories'], Map()),
        })
      );
    }
  });
  return { result, nextSubDataKey };
}
function pccfInstitutionLevel(formProgramData, getCategories) {
  let result = Map();
  let nextSubDataKey = Map();
  formProgramData.forEach((formCourse) => {
    const institutionId = formCourse.get('assignedInstitutionId', '');
    const institutionName = formCourse.get('institutionName', '');
    const categoryId = formCourse.get('categoryId', '');
    const categoryFormCourseId = formCourse.get('_id', '');
    if (result.get(institutionId, Map()).size === 0) {
      nextSubDataKey = nextSubDataKey.set(
        institutionId,
        fromJS({
          subDataKey: [institutionId, 'categories', categoryId],
          categoryFormCourseId,
        })
      );
      result = result.set(
        institutionId,
        fromJS({
          institutionId,
          name: institutionName,
          childKey: 'categories',
          categories: getCategories
            .updateIn(['institution', institutionId, 'categories'], Map(), (updateData) => {
              updateData.forEach((value, key) => {
                value.get('forms', Map()).forEach((formValue, FormKey) => {
                  updateData = updateData.setIn(
                    [key, 'forms', FormKey],
                    formValue.set('courseId', institutionId)
                  );
                });
              });
              return updateData;
            })
            .getIn(['institution', institutionId, 'categories'], Map()),
        })
      );
    }
  });
  return { result, nextSubDataKey };
}
export function splitFunctionPccfWise(formProgramData, level, getCategories) {
  if (level === 'course') return pccfCourseLevel(formProgramData, getCategories);
  if (level === 'program') return pccfProgramLevel(formProgramData, getCategories);
  if (level === 'institution') return pccfInstitutionLevel(formProgramData, getCategories);
  return {};
}

export const AssignComponent = ({
  assign,
  setAssign,
  disable,
  handleChangeAssign,
  multiUserIndex,
  isMultiUser = false,
  roleWiseOrUserWise,
  subData,
  isChecked,
}) => {
  const [stateValue, handleChange] = useNestedHook(
    Map({ type: roleWiseOrUserWise, searchInput: '' })
  );
  const type = stateValue.get('type', '');
  const searchKey = stateValue.get('searchInput', '');
  const handleUpdate = (type, value) => {
    handleChange(type, value);
  };
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };
  const handleChangeCheck = (type, multiUserIndex) => (key, bool) => {
    const keys = isMultiUser ? [multiUserIndex, 'users'] : ['users'];
    setAssign((prev) => {
      let forSingleLevel = isMultiUser ? [type, multiUserIndex, key] : [type, key];
      const newData = prev.setIn([...forSingleLevel, 'checked'], bool);
      handleChangeAssign(
        keys,
        isMultiUser
          ? newData.getIn(['userList', multiUserIndex], Map())
          : newData.get('userList', Map()),
        !bool,
        key,
        'User',
        'user'
      );
      return newData;
    });
  };
  const handleChangeRolesCheck = (type) => (key, bool) => {
    const keys = isMultiUser ? [multiUserIndex, 'roles'] : ['roles'];
    let forSingleLevel = isMultiUser ? [type, multiUserIndex] : [type];
    setAssign((prev) => {
      const data = prev.updateIn(forSingleLevel, prev, (update) => {
        let newData = update;
        newData = newData.setIn([...key, 'checked'], bool);
        handleChangeAssign(keys, newData, !bool, '', 'Role', 'role');
        return newData;
      });
      return data;
    });
  };
  const handleChangeRoles = (type) => (key, bool) => {
    const keys = isMultiUser ? [multiUserIndex, 'roles'] : ['roles'];
    let forSingleLevel = isMultiUser ? [type, multiUserIndex] : [type];
    setAssign((prev) => {
      const data = prev.updateIn(forSingleLevel, prev, (update) => {
        let newData = update;
        newData = newData.setIn([key, 'checked'], bool);
        update.getIn([key, 'userIds'], Map()).forEach((_, id) => {
          newData = newData.setIn([key, 'userIds', id, 'checked'], bool);
        });
        handleChangeAssign(keys, newData, !bool, '', 'Role', 'role');
        return newData;
      });
      return data;
    });
  };
  const forSingleLevelForRole = isMultiUser ? ['rolesList', multiUserIndex] : ['rolesList'];
  let rolesUsers = Map();
  assign.getIn(forSingleLevelForRole, Map()).forEach((value) => {
    const users = value.get('userIds', Map());
    rolesUsers = rolesUsers.merge(users);
  });
  const forSingleLevel = isMultiUser ? ['userList', multiUserIndex] : ['userList'];
  const checkedUser =
    type === 'User'
      ? assign
          .getIn(forSingleLevel, Map())
          .entrySeq()
          .filter(([_, userData]) => userData.get('checked', false))
      : rolesUsers.entrySeq().filter(([_, userData]) => userData.get('checked', false));
  const checkedUserSize = checkedUser.toArray().length;

  const Component =
    type === 'User' ? (
      <UserListDown
        search={searchKey}
        userList={isMultiUser ? assign.getIn(forSingleLevel, Map()) : assign.get('userList', Map())}
        handleChangeCheck={handleChangeCheck('userList', isMultiUser ? multiUserIndex : '')}
      />
    ) : (
      <UserListInsideRole
        search={searchKey}
        rolesList={
          isMultiUser ? assign.getIn(forSingleLevelForRole, Map()) : assign.get('rolesList', Map())
        }
        handleChangeRolesCheck={handleChangeRolesCheck('rolesList')}
        handleChangeRoles={handleChangeRoles('rolesList')}
      />
    );

  useEffect(() => {
    handleUpdate('type', roleWiseOrUserWise);
  }, [subData]);

  const getUsersTooltip = (checkedUser) => {
    const usersList = checkedUser
      .map(([userId, userData]) => {
        const name = userData.get('name', Map());
        return `${name.get('first', '')} ${name.get('last')}`;
      })
      .toArray();
    if (usersList.length > 0) {
      return usersList.slice(1).map((item, index) => <div key={index}>{item}</div>);
    }
    return '';
  };
  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`${
              !disable ? 'text-disable-for-assign' : ''
            } d-flex flex-column align-items-center `}
            onClick={isChecked ? handleToggle(handleClick) : () => {}}
          >
            <div className="d-flex">
              <div className="pr-2 cursor-pointer">Assign</div>
              <AddIcon className="p-0 cursor-pointer" fontSize="small" />
            </div>
            {disable && (
              <div className="d-flex gap-3">
                {checkedUser.map(([userId, userData], userIndex) => {
                  const name = userData.get('name', Map());
                  const firstName = `${name.get('first', '')} ${name.get('last')} `;
                  if (userIndex === 0)
                    return (
                      <Chip
                        label={`${firstName.substring(0, 8)}...`}
                        key={userIndex}
                        deleteIcon={
                          <Tooltips title={firstName}>
                            <Close
                              onMouseDown={(event) => event.stopPropagation()}
                              className="f-16 text-black mr-1"
                            />
                          </Tooltips>
                        }
                        onDelete={() =>
                          handleChangeCheck('userList', multiUserIndex)(userId, false)
                        }
                        className="p-0 text-gray f-12"
                        sx={ChipSx}
                      />
                    );
                })}
                {checkedUserSize > 1 && (
                  <Chip
                    label={`+${checkedUserSize - 1}`}
                    onDelete={() => {}}
                    deleteIcon={
                      <Tooltips title={getUsersTooltip(checkedUser)}>
                        <InfoIcon sx={{ margin: '4px 5px 0px -5px' }} className="f-16 text-gray" />{' '}
                      </Tooltips>
                    }
                    className="p-0 text-black f-12"
                    sx={ChipSx}
                  />
                )}
              </div>
            )}
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="d-flex align-items-center justify-content-between pb-2">
                <div className="fw-500 f-16 text-mGrey">Assign</div>
                <MultiSelect
                  options={['User', 'Role']}
                  values={type}
                  onChangeSection={(value) => handleUpdate('type', value)}
                  multiple={false}
                  fullWidth={false}
                  placeholder={'select role'}
                  sx={{
                    width: '125px',
                    '& .MuiInputBase-input': {
                      padding: '3.5px 14px',
                    },
                  }}
                />
              </div>
              <MaterialInput
                elementType={'materialSearch'}
                type={'text'}
                size={'small'}
                value={searchKey}
                changed={(e) => handleChange('searchInput', e.target.value)}
                placeholder={'Search Name / ID'}
                addClass="f-12"
              />
              {Component}
            </div>
            {/* <div className="popover-btn-parent">
              <MButton variant="outlined" color="primary" className="mr-2" clicked={handleClose}>
                Cancel
              </MButton>
              <MButton color="primary" clicked={handleClose}>
                Apply
              </MButton>
            </div> */}
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};
const UserListDown = ({ userList, handleChangeCheck, search }) => {
  return (
    <div className="scroll-overflow-roles-permission">
      {userList
        .entrySeq()
        .filter(([_, value]) =>
          value
            .get('userName', '')
            .toLowerCase()
            .includes(search.trim().replace(/\s+/g, '').toLowerCase())
        )
        .map(([userId, userData], userIndex) => {
          const name = userData.get('name', Map());
          const firstName = `${name.get('first', '')} ${name.get('middle', '')} ${name.get(
            'last'
          )}`;
          const isCheck = userData.get('checked', false);
          return (
            <div
              className="d-flex align-items-center p-2 hover-color-selected f-14 fw-400 text-mGrey border-bottom"
              key={userIndex}
            >
              <Checkbox
                fontSize="small"
                checked={isCheck}
                sx={checkBoxSx}
                onChange={(e) => handleChangeCheck(userId, e.target.checked)}
              />
              <span className="pl-2">{firstName}</span>
              <span className="flex-grow-1 text-right">#9876</span>
            </div>
          );
        })}
    </div>
  );
};
const UserListInsideRole = ({ rolesList, handleChangeRolesCheck, handleChangeRoles, search }) => {
  return (
    <div className="scroll-overflow-roles-permission">
      {rolesList
        .entrySeq()
        .filter(([_, value]) =>
          value
            .get('roleName', '')
            .replace(/\s+/g, '')
            .toLowerCase()
            .includes(search.trim().replace(/\s+/g, '').toLowerCase())
        )
        .map(([roleId, rolesData], roleIndex) => {
          const roleName = rolesData.get('roleName', '');
          const userList = rolesData.get('userIds', Map());
          const userListCount = userList.size;
          const checkCount = userList.filter((value) => value.get('checked', false) === true).size;
          const isParentChecked = userListCount === checkCount;
          return (
            <Accordion
              disableGutters
              elevation={0}
              key={roleIndex}
              sx={{
                border: 'none',
                '&::before': {
                  display: 'none', // Remove the before pseudo-element which might be causing the border
                },
              }}
            >
              <AccordionSummary
                sx={{
                  margin: 0,
                  padding: '0 8px',
                  minHeight: '40px',
                  borderTop: 'none',
                  '& .MuiAccordionSummary-content': {
                    margin: 0,
                  },
                }}
                expandIcon={<ExpandMoreOutlined />}
                className="d-flex  align-items-center  hover-color-selected f-14 fw-400 text-mGrey border-bottom"
              >
                <Checkbox
                  fontSize="small"
                  onClick={(event) => event.stopPropagation()}
                  checked={isParentChecked}
                  sx={checkBoxSx}
                  onChange={(e) => handleChangeRoles(roleId, e.target.checked)}
                />
                <span className="pl-2 f-12 fw-400 text-mGrey">{roleName}</span>
              </AccordionSummary>
              <AccordionDetails className="scroll-overflow-roles-permission-max">
                {userList.entrySeq().map(([userId, userData], userIndex) => {
                  const name = userData.get('name', Map());
                  const fullName = `${name.get('first', '')} ${name.get('first', '')} ${name.get(
                    'last',
                    ''
                  )}`;
                  const checked = userData.get('checked', false);
                  return (
                    <div
                      className="d-flex align-items-center p-2  f-14 fw-400 text-mGrey border-bottom"
                      key={userIndex}
                    >
                      <SubdirectoryArrowRightIcon
                        color="primary"
                        fontSize="small"
                        className="mr-2 ml-1"
                      />
                      <Checkbox
                        fontSize="small"
                        checked={checked}
                        sx={checkBoxSx}
                        onChange={(e) =>
                          handleChangeRolesCheck([roleId, 'userIds', userId], e.target.checked)
                        }
                      />
                      <span className="pl-2">{fullName} </span>
                      <span className="flex-grow-1 text-right">#9876</span>
                    </div>
                  );
                })}
              </AccordionDetails>
            </Accordion>
          );
        })}
    </div>
  );
};
export const ActionComponent = ({
  disable,
  setSubModuleListState,
  subModuleListState,
  handleChangeAssign,
  isMultiUser = false,
  multiUserIndex,
  isChecked,
}) => {
  const [actions] = useIsReduxEmpty(selectQapcActions, getRolesPermissionActions);
  const handleChangeAction = (subModuleId, actionId, bool, index) => {
    const keys = isMultiUser ? [multiUserIndex, 'subModules'] : ['subModules'];
    setSubModuleListState((prev) => {
      const newData = isMultiUser
        ? prev.updateIn([multiUserIndex, subModuleId, 'actionsIds'], List(), (data) =>
            index
              ? data.set(index, Map({ actionId, checked: bool }))
              : data.map((value) => {
                  if (value.get('actionId', '') === actionId) {
                    return value.set('checked', bool);
                  }
                  return value;
                })
          )
        : prev.updateIn([subModuleId, 'actionsIds'], List(), (data) =>
            index
              ? data.set(index, Map({ actionId, checked: bool }))
              : data.map((value) => {
                  if (value.get('actionId', '') === actionId) {
                    return value.set('checked', bool);
                  }
                  return value;
                })
          );

      handleChangeAssign(
        keys,
        isMultiUser ? newData.get(multiUserIndex, Map()) : newData,
        !bool,
        subModuleId,
        '',
        'subModule'
      );
      return newData;
    });
  };
  const handleChangeLevelAction = (
    subModuleId,
    bool,
    actionIndex,
    levelIndex,
    actionId = '',
    levelName = ''
  ) => {
    const selectedSubModule = subModuleListState.getIn(
      isMultiUser ? [multiUserIndex, subModuleId] : [subModuleId],
      Map()
    );
    const levelPosition = selectedSubModule
      .get('levels', List())
      .findIndex((levels) => levels.get('level', '') === levelName);
    const actionPosition = subModuleListState
      .getIn(
        isMultiUser
          ? [multiUserIndex, subModuleId, 'levels', levelPosition]
          : [subModuleId, 'levels', levelPosition],
        List()
      )
      .get('actionsIds', List())
      .findIndex((action) => action.get('actionId', '') === actionId);
    const keys = isMultiUser ? [multiUserIndex, 'subModules'] : ['subModules'];
    setSubModuleListState((prev) => {
      const newData = isMultiUser
        ? prev.setIn(
            [
              multiUserIndex,
              subModuleId,
              'levels',
              levelIndex !== '' ? levelIndex : levelPosition,
              'actionsIds',
              actionIndex !== '' ? actionIndex : actionPosition,
              'checked',
            ],
            bool
          )
        : prev.setIn(
            [
              subModuleId,
              'levels',
              levelIndex !== '' ? levelIndex : levelPosition,
              'actionsIds',
              actionIndex !== '' ? actionIndex : actionPosition,
              'checked',
            ],
            bool
          );
      handleChangeAssign(
        keys,
        isMultiUser ? newData.get(multiUserIndex, Map()) : newData,
        !bool,
        subModuleId,
        '',
        'subModule'
      );
      return newData;
    });
  };
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };
  const checkedSubModules = isMultiUser
    ? subModuleListState
        .get(multiUserIndex, Map())
        .entrySeq()
        .filter(([_, subModule]) => subModule.get('checked', false))
    : subModuleListState.entrySeq().filter(([_, subModules]) => subModules.get('checked', false));
  let checkedActions = List();
  checkedSubModules.forEach(([subModuleId, subModule]) => {
    const actionsIds = subModule.get('actionsIds', List());
    const subModuleName = subModule.get('subModuleName', '');
    actionsIds.forEach((action) => {
      const actionId = action.get('actionId', '');
      const actionIsCheck = action.get('checked', false);
      const actionName = actions.getIn([actionId, 'actionName'], '');
      if (actionIsCheck)
        checkedActions = checkedActions.push(
          fromJS({ actionName, actionId, subModuleId, subModuleName })
        );
    });
  });
  checkedSubModules.forEach(([subModuleId, subModule]) => {
    const subModuleName = subModule.get('subModuleName', '');
    const levels = subModule.get('levels', List());
    let levelActions = List();
    if (subModuleName === 'Form Approver') {
      levels.forEach((action) => {
        const actionsIds = action.get('actionsIds', List());
        const levelName = action.get('level', '');
        actionsIds.forEach((action) => {
          const actionId = action.get('actionId', '');
          const actionIsCheck = action.get('checked', false);
          const actionName = actions.getIn([actionId, 'actionName'], '');
          if (actionIsCheck)
            levelActions = levelActions.push(
              fromJS({ actionName, actionId, subModuleId, subModuleName, levelName })
            );
        });
      });
    }
    checkedActions = checkedActions.merge(levelActions);
  });

  const getActionTooltip = (checkedActions) => {
    return checkedActions
      .slice(1)
      .map((item, index) => <div key={index}>{item.get('actionName', '')}</div>);
  };

  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`${
              !disable ? 'text-disable-for-assign' : ''
            } d-flex flex-column align-items-center `}
            onClick={isChecked ? handleToggle(handleClick) : () => {}}
          >
            <div className="d-flex">
              <div className="pr-2 cursor-pointer">Actions</div>
              <AddIcon className="p-0 cursor-pointer" fontSize="small" />
            </div>
            {disable && (
              <div className="d-flex gap-3">
                {checkedActions.map((action, actionIndex) => {
                  const actionName = action.get('actionName', '');
                  const subModuleId = action.get('subModuleId', '');
                  const subModuleName = action.get('subModuleName', '');
                  const actionId = action.get('actionId', '');
                  const levelName = action.get('levelName', '');
                  if (actionIndex === 0)
                    return (
                      <Chip
                        label={`${actionName.substring(0, 8)}...`}
                        key={actionIndex}
                        deleteIcon={
                          <Tooltips title={actionName}>
                            <Close
                              onMouseDown={(event) => event.stopPropagation()}
                              className="f-16 text-black mr-1"
                            />
                          </Tooltips>
                        }
                        onDelete={() =>
                          subModuleName === 'Form Approver'
                            ? handleChangeLevelAction(
                                subModuleId,
                                false,
                                '',
                                '',
                                actionId,
                                levelName
                              )
                            : handleChangeAction(subModuleId, actionId, false)
                        }
                        className="p-0 text-black f-12"
                        sx={ChipSx}
                      />
                    );
                })}
                {new Set(checkedActions).size > 1 && (
                  <Chip
                    label={`+${checkedActions.size - 1}`}
                    onDelete={() => {}}
                    deleteIcon={
                      <Tooltips title={getActionTooltip(checkedActions)}>
                        <InfoIcon sx={{ margin: '0px 5px 0px -5px' }} className="f-16  text-gray" />
                      </Tooltips>
                    }
                    className="p-0 text-black bg-powder-blue border-royal-blue"
                    sx={ChipSx}
                  />
                )}
              </div>
            )}
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="fw-500 f-16 text-mGrey border-bottom pb-2">Action</div>
              {checkedSubModules.map(([subModuleId, subModule], subModuleIndex) => {
                const subModuleName = subModule.get('subModuleName', '');
                const multiUserLevel = isMultiUser
                  ? subModuleListState.getIn([multiUserIndex, subModuleId, 'levels'], List())
                  : subModuleListState.getIn([subModuleId, 'levels'], List());
                const actionsIds = subModule.get('actionsIds', List());
                return (
                  <div key={subModuleIndex}>
                    <div className="fw-500 f-16 text-mGrey border-bottom pb-2  mt-2">
                      {subModuleName}
                    </div>
                    {multiUserLevel.size > 0 ? (
                      <>
                        {multiUserLevel.map((value, levelIndex) => {
                          const levelName = value.get('level', '');
                          const actionsLevelIds = value.get('actionsIds', List());
                          return (
                            <div key={levelIndex}>
                              <div className="fw-500 f-14 text-mGrey">{levelName}</div>
                              {actionsLevelIds.map((action, actionIndex) => {
                                const actionId = action.get('actionId', '');
                                const actionIsCheck = action.get('checked', false);
                                const actionName = actions.getIn([actionId, 'actionName'], '');
                                return (
                                  <div
                                    className="d-flex align-items-center py-3  f-14 fw-400 text-mGrey border-bottom"
                                    key={actionIndex}
                                  >
                                    <Checkbox
                                      fontSize="small"
                                      checked={actionIsCheck}
                                      sx={checkBoxSx}
                                      onChange={(e) =>
                                        handleChangeLevelAction(
                                          subModuleId,
                                          e.target.checked,
                                          actionIndex,
                                          levelIndex
                                        )
                                      }
                                    />
                                    <span className="pl-2">{actionName}</span>
                                  </div>
                                );
                              })}
                            </div>
                          );
                        })}
                      </>
                    ) : (
                      <>
                        {actionsIds.map((action, actionIndex) => {
                          const actionId = action.get('actionId', '');
                          const actionIsCheck = action.get('checked', false);
                          const actionName = actions.getIn([actionId, 'actionName'], '');
                          return (
                            <div
                              className="d-flex align-items-center py-3 f-14 fw-400 text-mGrey border-bottom"
                              key={actionIndex}
                            >
                              <Checkbox
                                fontSize="small"
                                checked={actionIsCheck}
                                sx={checkBoxSx}
                                onChange={(e) =>
                                  handleChangeAction(
                                    subModuleId,
                                    actionId,
                                    e.target.checked,
                                    actionIndex
                                  )
                                }
                              />
                              <span className="pl-2">{actionName}</span>
                            </div>
                          );
                        })}
                      </>
                    )}
                  </div>
                );
              })}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};
export const SubModuleActionComponent = ({
  subModuleListState,
  setSubModuleListState,
  disable,
  handleChangeAssign,
  isMultiUser = false,
  multiUserIndex,
  isChecked,
}) => {
  const handleChangeCheck = (key, bool) => {
    const keys = isMultiUser ? [multiUserIndex, 'subModules'] : ['subModules'];
    setSubModuleListState((prev) => {
      const newData = isMultiUser
        ? prev.update(multiUserIndex, prev, (data) => data.setIn(key, bool))
        : prev.setIn(key, bool);
      handleChangeAssign(
        keys,
        isMultiUser ? newData.get(multiUserIndex, Map()) : newData,
        !bool,
        key[0],
        '',
        'subModule'
      );
      return newData;
    });
  };
  const handleChangeApprover = (key, levelList, bool, subModuleName, actionsIds) => {
    const keys = isMultiUser ? [multiUserIndex, 'subModules'] : ['subModules'];
    setSubModuleListState((prev) => {
      let forSingleLevel = isMultiUser ? [multiUserIndex, key] : [key];
      const newData = prev.setIn(
        forSingleLevel,
        fromJS({ levels: bool ? levelList : List(), subModuleName, actionsIds, checked: bool })
      );
      handleChangeAssign(
        keys,
        isMultiUser ? newData.get(multiUserIndex, Map()) : newData,
        !bool,
        key,
        '',
        'subModule'
      );
      return newData;
    });
  };

  const handleChangeApproverLevel = (key, value, bool, index) => {
    const keys = isMultiUser ? [multiUserIndex, 'subModules'] : ['subModules'];
    let forSingleLevel = isMultiUser ? [multiUserIndex, ...key] : key;
    const parentChecked = isMultiUser ? [multiUserIndex, key[0], 'checked'] : [key[0], 'checked'];
    if (bool) {
      setSubModuleListState((prev) => {
        const newData = prev
          .updateIn(forSingleLevel, List(), (data) => data.push(value))
          .setIn(parentChecked, bool);
        handleChangeAssign(
          keys,
          isMultiUser ? newData.get(multiUserIndex, Map()) : newData,
          !bool,
          key[0],
          '',
          'subModule'
        );
        return newData;
      });
    } else {
      setSubModuleListState((prev) => {
        let newData = prev
          .updateIn([...forSingleLevel], List(), (data) =>
            data.filter((level) => level.get('level', '') !== value.get('level', ''))
          )
          .setIn(parentChecked, bool);
        handleChangeAssign(
          keys,
          isMultiUser ? newData.get(multiUserIndex, Map()) : newData,
          !bool,
          key[0],
          '',
          'subModule'
        );
        return newData;
      });
    }
  };
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };
  const checkedSubModules = isMultiUser
    ? subModuleListState
        .get(multiUserIndex, Map())
        .entrySeq()
        .filter(([_, subModule]) => subModule.get('checked', false))
    : subModuleListState.entrySeq().filter(([_, subModule]) => subModule.get('checked', false));
  const checkedSubModulesSize = checkedSubModules.toArray().length;
  const checkMultiUser = isMultiUser
    ? subModuleListState.get(multiUserIndex, Map())
    : subModuleListState;

  const getSubModuleTooltip = (checkedSubModules) => {
    const usersList = checkedSubModules
      .map(([userId, userData]) => {
        return userData.get('subModuleName', '');
      })
      .toArray();
    if (usersList.length > 0) {
      return usersList.slice(1).map((item, index) => <div key={index}>{item}</div>);
    }
    return '';
  };
  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`${!disable ? 'text-disable-for-assign' : ''}
            d-flex flex-column align-items-center`}
            onClick={isChecked ? handleToggle(handleClick) : () => {}}
          >
            <div className="d-flex">
              <div className="pr-2 cursor-pointer">Sub Modules</div>
              <AddIcon className="p-0 cursor-pointer" fontSize="small" />
            </div>
            {disable && (
              <div className="d-flex gap-3">
                {checkedSubModules.map(([subModuleId, subModule], subModuleIndex) => {
                  const subModuleName = subModule.get('subModuleName', '');
                  const actionsIds = subModule.get('actionsIds', List());
                  const levels = subModule.get('levels', List());
                  if (subModuleIndex === 0)
                    return (
                      <Chip
                        label={`${subModuleName.substring(0, 8)}...`}
                        key={subModuleIndex}
                        deleteIcon={
                          <Tooltips title={subModuleName}>
                            <Close
                              onMouseDown={(event) => event.stopPropagation()}
                              className="f-16 text-black mr-1"
                            />
                          </Tooltips>
                        }
                        onDelete={() =>
                          subModuleName === 'Form Approver'
                            ? handleChangeApprover(
                                subModuleId,
                                levels,
                                false,
                                subModuleName,
                                actionsIds
                              )
                            : handleChangeCheck([subModuleId, 'checked'], false, subModuleName)
                        }
                        className="p-0 text-black f-12"
                        sx={ChipSx}
                      />
                    );
                })}
                {checkedSubModulesSize > 1 && (
                  <Chip
                    label={`+${checkedSubModulesSize - 1}`}
                    onDelete={() => {}}
                    deleteIcon={
                      <Tooltips title={getSubModuleTooltip(checkedSubModules)}>
                        <InfoIcon sx={{ margin: '0px 5px 0px -5px' }} className="f-16 text-gray" />
                      </Tooltips>
                    }
                    className="p-0 text-gray bg-powder-blue border-royal-blue"
                    sx={ChipSx}
                  />
                )}
              </div>
            )}
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="fw-500 f-16 text-mGrey border-bottom pb-2">Sub Modules</div>
              {checkMultiUser.entrySeq().map(([subModuleId, subModule], moduleIndex) => {
                const subModuleName = subModule.get('subModuleName', '');
                const checked = subModule.get('checked', false);
                const isFormApprover = subModuleName === 'Form Approver';
                return isFormApprover ? (
                  <DropDownApprover
                    subModuleName={subModuleName}
                    subModuleId={subModuleId}
                    subModuleListState={checkMultiUser}
                    handleChangeApprover={handleChangeApprover}
                    handleChangeApproverLevel={handleChangeApproverLevel}
                  />
                ) : (
                  <div
                    className="d-flex align-items-center py-2  f-12 fw-400 text-mGrey border-bottom"
                    key={moduleIndex}
                  >
                    <Checkbox
                      fontSize="small"
                      checked={checked}
                      sx={checkBoxSx}
                      onChange={(e) =>
                        handleChangeCheck([subModuleId, 'checked'], e.target.checked, subModuleName)
                      }
                    />
                    <span className="pl-2">{subModuleName}</span>
                  </div>
                );
              })}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};

const DropDownApprover = ({
  subModuleName,
  handleChangeApprover,
  subModuleId,
  subModuleListState,
  handleChangeApproverLevel,
}) => {
  const levelList = useSelector(selectFormApprover);
  const actionsIds = subModuleListState.getIn([subModuleId, 'actionsIds'], List());
  const allLeveLList = levelList.map((_, index) =>
    fromJS({
      level: `level ${index + 1}`,
      actionsIds,
    })
  );
  return (
    <Accordion disableGutters elevation={0}>
      <AccordionSummary
        sx={{
          margin: 0,
          padding: '0px',
          minHeight: '40px',
          borderTop: 'none',
          '& .MuiAccordionSummary-content': {
            margin: 0,
          },
        }}
        expandIcon={<ExpandMoreOutlined />}
        className="d-flex  align-items-center  hover-color-selected f-14 fw-400 text-mGrey border-bottom"
      >
        <Checkbox
          fontSize="small"
          sx={checkBoxSx}
          onClick={(event) => event.stopPropagation()}
          checked={
            subModuleListState.getIn([subModuleId, 'levels'], List()).size === levelList.size
          }
          onChange={(e) => {
            handleChangeApprover(
              subModuleId,
              allLeveLList,
              e.target.checked,
              subModuleName,
              actionsIds
            );
          }}
        />
        <span className="pl-2 f-12 fw-400 text-mGrey">{subModuleName}</span>
      </AccordionSummary>
      <AccordionDetails>
        {levelList.map((_, index) => {
          return (
            <div
              className="d-flex align-items-center p-2  f-14 fw-400 text-mGrey border-bottom"
              key={index}
            >
              <SubdirectoryArrowRightIcon color="primary" fontSize="small" className="mr-2 ml-1" />
              <Checkbox
                fontSize="small"
                sx={checkBoxSx}
                checked={subModuleListState
                  .getIn([subModuleId, 'levels'], List())
                  .some((level) => level.get('level', '').includes(`level ${index + 1}`))}
                onChange={(e) =>
                  handleChangeApproverLevel(
                    [subModuleId, 'levels'],
                    fromJS({
                      level: `level ${index + 1}`,
                      actionsIds,
                    }),
                    e.target.checked,
                    index
                  )
                }
              />
              <span className="pl-2">Level {index + 1} </span>
              <span className="flex-grow-1 text-right">#9876</span>
            </div>
          );
        })}
      </AccordionDetails>
    </Accordion>
  );
};

export const AssignHeaderComponent = ({ headerData, formHeaderKey, headerIndex }) => {
  const { setFormHeaderData } = useQapcContext();
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };

  const handleChangeType = (value) => {
    setFormHeaderData((prev) => {
      return prev.setIn([formHeaderKey, headerIndex, 'type'], value);
    });
  };

  const handleChangeCheck = (userId, userName, checked) => {
    setFormHeaderData((prev) => {
      const changeType = prev.getIn([formHeaderKey, headerIndex, 'type'], 'User');
      if (changeType === 'User') {
        return prev.updateIn([formHeaderKey, headerIndex], (update) => {
          return update.update('staffList', (list) => {
            if (checked) {
              const userMap = Map({
                userId,
                userName,
              });
              return list.push(userMap);
            } else {
              return list.filter((item) => item.get('userId', '') !== userId);
            }
          });
        });
      }
      return prev;
    });
  };

  const handleRemoveUser = (userId) => {
    setFormHeaderData((prev) => {
      return prev.updateIn([formHeaderKey, headerIndex], (update) => {
        return update
          .update('staffList', (list) => {
            return list.filter((item) => item.get('userId', '') !== userId);
          })
          .update('rolesStaffList', (list) => {
            return list.filter((item) => item.get('userId', '') !== userId);
          });
      });
    });
  };

  const handleChangeAllRoles = (roleId, userList, checked) => {
    setFormHeaderData((prev) => {
      const changeType = prev.getIn([formHeaderKey, headerIndex, 'type'], 'User');
      if (changeType === 'Role') {
        return prev.updateIn([formHeaderKey, headerIndex], (update) => {
          return update.update('rolesStaffList', (list) => {
            if (checked) {
              const filterRoleUser = list.filter((item) => item.get('roleId', '') !== roleId);
              const userMap = [];
              userList.filter((item) => {
                const name = item.get('name', Map());
                const userName = `${name.get('first', '')} ${name.get('middle', '')} ${name.get(
                  'last',
                  ''
                )}`;
                userMap.push({
                  userId: item.get('userId'),
                  userName,
                  roleId,
                });
                return item;
              });
              const updatedUserList = [...filterRoleUser.toJS(), ...userMap];
              return fromJS(updatedUserList);
            } else {
              return list.filter((item) => item.get('roleId', '') !== roleId);
            }
          });
        });
      }
      return prev;
    });
  };

  const handleChangeRolesCheck = (userId, userName, roleId, checked) => {
    setFormHeaderData((prev) => {
      const changeType = prev.getIn([formHeaderKey, headerIndex, 'type'], 'User');
      if (changeType === 'Role') {
        return prev.updateIn([formHeaderKey, headerIndex], (update) => {
          return update.update('rolesStaffList', (list) => {
            if (checked) {
              const userMap = Map({
                userId,
                userName,
                roleId,
              });
              return list.push(userMap);
            } else {
              return list.filter((item) => item.get('userId', '') !== userId);
            }
          });
        });
      }
      return prev;
    });
  };

  const type = headerData.get('type', 'User');
  const staffList = headerData.get('staffList', List());
  const rolesStaffList = headerData.get('rolesStaffList', List());
  const [search, setSearch] = useState('');

  useEffect(() => {
    setSearch('');
  }, [type]);

  const Component =
    type === 'User' ? (
      <UserListDropDown
        search={search}
        userList={staffList}
        handleChangeCheck={handleChangeCheck}
      />
    ) : (
      <UserRoleListDropDown
        search={search}
        savedUserList={rolesStaffList}
        handleChangeRolesCheck={handleChangeRolesCheck}
        handleChangeAllRoles={handleChangeAllRoles}
      />
    );

  const getUserList = () => {
    const staffIds = staffList.map((item) => item.get('userId', ''));
    const concatUserList = staffList.concat(
      rolesStaffList.filter((item) => !staffIds.includes(item.get('userId', '')))
    );
    return concatUserList;
  };

  const userList = getUserList();

  const getUsersTooltip = (staffList) => {
    const usersList = staffList.map((userData) => {
      return userData.get('userName', Map());
    });
    if (usersList.size > 0) {
      return usersList.slice(1).map((item, index) => <div key={index}>{item}</div>);
    }
  };

  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`d-flex flex-column align-items-center`}
            onClick={handleToggle(handleClick)}
          >
            <div className="d-flex">
              <div className="pr-2 cursor-pointer">Assign</div>
              <AddIcon className="p-0 cursor-pointer" fontSize="small" />
            </div>
            <div className="d-flex gap-3">
              {userList.slice(0, 1).map((userData, userIndex) => {
                const name = userData.get('userName', Map());
                const userId = userData.get('userId', '');
                return (
                  <Chip
                    label={`${name.substring(0, 8)}...`}
                    key={userIndex}
                    deleteIcon={
                      <Tooltips title={name}>
                        <Close
                          onClick={(event) => {
                            event.stopPropagation();
                            handleRemoveUser(userId);
                          }}
                          className="f-16 text-black mr-1"
                        />
                      </Tooltips>
                    }
                    onDelete={() => handleRemoveUser(userId)}
                    className="p-0 text-gray f-12"
                    sx={ChipSx}
                  />
                );
              })}
              {userList.size > 1 && (
                <Chip
                  label={`+${userList.size - 1}`}
                  onDelete={() => {}}
                  deleteIcon={
                    <Tooltips title={getUsersTooltip(userList)}>
                      <InfoIcon sx={{ margin: '4px 5px 0px -5px' }} className="f-16 text-gray" />{' '}
                    </Tooltips>
                  }
                  className="p-0 text-black f-12"
                  sx={ChipSx}
                />
              )}
            </div>
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="d-flex align-items-center justify-content-between pb-2">
                <div className="fw-500 f-16 text-mGrey">Assign</div>
                <MultiSelect
                  options={['User', 'Role']}
                  values={type}
                  onChangeSection={(value) => handleChangeType(value)}
                  multiple={false}
                  fullWidth={false}
                  placeholder={'Select'}
                  sx={{
                    width: '125px',
                    '& .MuiInputBase-input': {
                      padding: '3.5px 14px',
                    },
                  }}
                />
              </div>
              <MaterialInput
                elementType={'materialSearch'}
                type={'text'}
                size={'small'}
                value={search}
                changed={(e) => setSearch(e.target.value)}
                placeholder={'Search Name / ID'}
                addClass="f-12"
              />
              {Component}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};

const ITEMS_PER_BATCH = 20;
const UserListDropDown = ({ userList, handleChangeCheck, search = '' }) => {
  const { staffList } = useQapcContext();
  const [displayedItems, setDisplayedItems] = useState(List());
  const [batchIndex, setBatchIndex] = useState(1);

  useEffect(() => {
    loadMoreItems(1);
  }, [staffList, search]);

  const loadMoreItems = (newBatchIndex) => {
    const filteredList = staffList.filter((value) =>
      value
        .get('userName', '')
        .toLowerCase()
        .includes(search.trim().replace(/\s+/g, '').toLowerCase())
    );
    const itemsToLoad = filteredList.slice(0, ITEMS_PER_BATCH * newBatchIndex);
    setDisplayedItems(itemsToLoad);
    setBatchIndex(newBatchIndex);
  };

  const handleScroll = (e) => {
    if (Math.ceil(e.target.scrollTop + e.target.clientHeight) >= Math.ceil(e.target.scrollHeight)) {
      loadMoreItems(batchIndex + 1);
    }
  };

  return (
    <div className="scroll-overflow-roles-permission" onScroll={handleScroll}>
      {displayedItems.map((userData, userIndex) => {
        const name = userData.get('name', Map());
        const fullName = `${name.get('first', '')} ${name.get('middle', '')} ${name.get('last')}`;
        const userId = userData.get('userId', '');
        const isCheck = userList.some((item) => item.get('userId', '') === userId);
        return (
          <div
            className="d-flex align-items-center p-2 hover-color-selected f-14 fw-400 text-mGrey border-bottom"
            key={userIndex}
          >
            <Checkbox
              fontSize="small"
              checked={isCheck}
              sx={checkBoxSx}
              onChange={(e) => handleChangeCheck(userId, fullName, e.target.checked)}
            />
            <div className="pl-2">
              {fullName} <br />#{userData.get('academicId', '')}
            </div>
          </div>
        );
      })}
    </div>
  );
};

const UserRoleListDropDown = ({
  savedUserList,
  handleChangeRolesCheck,
  handleChangeAllRoles,
  search,
}) => {
  const { rolesStaffList } = useQapcContext();
  const [displayedItems, setDisplayedItems] = useState(Map());
  const [batchIndex, setBatchIndex] = useState(1);

  useEffect(() => {
    loadMoreItems(1);
  }, [rolesStaffList, search]);

  const loadMoreItems = (newBatchIndex) => {
    const filteredList = rolesStaffList.filter((value) => {
      return value
        .get('roleName', '')
        .toLowerCase()
        .includes(search.trim().replace(/\s+/g, '').toLowerCase());
    });
    const itemsToLoad = filteredList.slice(0, ITEMS_PER_BATCH * newBatchIndex);
    setDisplayedItems(itemsToLoad);
    setBatchIndex(newBatchIndex);
  };

  const handleScroll = (e) => {
    if (Math.ceil(e.target.scrollTop + e.target.clientHeight) >= Math.ceil(e.target.scrollHeight)) {
      loadMoreItems(batchIndex + 1);
    }
  };

  return (
    <div className="scroll-overflow-roles-permission" onScroll={handleScroll}>
      {displayedItems
        .entrySeq()
        // .filter(([_, value]) =>
        //   value
        //     .get('roleName', '')
        //     .replace(/\s+/g, '')
        //     .toLowerCase()
        //     .includes(search.trim().replace(/\s+/g, '').toLowerCase())
        // )
        .map(([roleId, rolesData], roleIndex) => {
          const roleName = rolesData.get('roleName', '');
          const userList = rolesData.get('userIds', Map());
          const userListCount = userList.size;
          const checkCount = savedUserList.filter((item) => item.get('roleId', '') === roleId).size;
          const isParentChecked = userListCount === checkCount;
          return (
            <Accordion
              disableGutters
              elevation={0}
              key={roleIndex}
              sx={{
                border: 'none',
                '&::before': {
                  display: 'none',
                },
              }}
            >
              <AccordionSummary
                sx={{
                  margin: 0,
                  padding: '0 8px',
                  minHeight: '40px',
                  borderTop: 'none',
                  '& .MuiAccordionSummary-content': {
                    margin: 0,
                  },
                }}
                expandIcon={<ExpandMoreOutlined />}
                className="d-flex  align-items-center  hover-color-selected f-14 fw-400 text-mGrey border-bottom"
              >
                <Checkbox
                  fontSize="small"
                  onClick={(event) => event.stopPropagation()}
                  checked={isParentChecked}
                  sx={checkBoxSx}
                  onChange={(e) => handleChangeAllRoles(roleId, userList, e.target.checked)}
                />
                <span className="pl-2 f-12 fw-400 text-mGrey">
                  {roleName}{' '}
                  {checkCount > 0 ? (
                    <span className={'selected-role-count'}>{checkCount}</span>
                  ) : (
                    ''
                  )}
                </span>
              </AccordionSummary>
              <AccordionDetails className="scroll-overflow-roles-permission-max">
                {userList.entrySeq().map(([userId, userData], userIndex) => {
                  const name = userData.get('name', Map());
                  const fullName = `${name.get('first', '')} ${name.get('middle', '')} ${name.get(
                    'last',
                    ''
                  )}`;
                  const checked = savedUserList.some(
                    (item) => item.get('roleId', '') === roleId && item.get('userId', '') === userId
                  );
                  return (
                    <div
                      className="d-flex align-items-center p-2  f-14 fw-400 text-mGrey border-bottom"
                      key={userIndex}
                    >
                      <SubdirectoryArrowRightIcon
                        color="primary"
                        fontSize="small"
                        className="mr-2 ml-1"
                      />
                      <Checkbox
                        fontSize="small"
                        checked={checked}
                        sx={checkBoxSx}
                        onChange={(e) =>
                          handleChangeRolesCheck(userId, fullName, roleId, e.target.checked)
                        }
                      />
                      <div className="pl-2">
                        {fullName} <br />#{userData.get('academicId', '')}
                      </div>
                    </div>
                  );
                })}
              </AccordionDetails>
            </Accordion>
          );
        })}
    </div>
  );
};

export const SubModuleHeaderComponent = ({ headerData, formHeaderKey, headerIndex }) => {
  const { setFormHeaderData } = useQapcContext();
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };

  const subModuleList = headerData.get('subModuleList', Map());

  const handleChangeSubModules = (subModuleId, checked) => {
    setFormHeaderData((prev) => {
      return prev
        .setIn([formHeaderKey, headerIndex, 'subModuleList', subModuleId, 'checked'], checked)
        .updateIn([formHeaderKey, headerIndex, 'subModuleList', subModuleId], (subModuleList) => {
          return subModuleList.get('subModuleName') === 'Form Approver'
            ? subModuleList.update('levels', (level) =>
                level.map((lvl) =>
                  lvl
                    .set('checked', checked)
                    .update('actionsIds', (action) =>
                      action.map((item) => (checked ? item : item.set('checked', false)))
                    )
                )
              )
            : subModuleList.update('actionsIds', (action) =>
                action.map((item) => (checked ? item : item.set('checked', false)))
              );
        });
    });
  };

  const handleChangeApproverLevel = (subModuleId, checked, levelIndex) => {
    setFormHeaderData((prev) => {
      const copyData = [formHeaderKey, headerIndex, 'subModuleList', subModuleId];
      return prev
        .setIn([...copyData, 'levels', levelIndex, 'checked'], checked)
        .updateIn([...copyData, 'levels', levelIndex, 'actionsIds'], (action) =>
          action.map((item) => item.set('checked', false))
        )
        .updateIn([...copyData], (subModuleList) => {
          return subModuleList.get('subModuleName') === 'Form Approver'
            ? subModuleList.set(
                'checked',
                subModuleList
                  .get('levels', List())
                  .every((item) => item.get('checked', false) === true)
              )
            : subModuleList;
        });
    });
  };

  function getCheckedSubModules() {
    let moduleList = Object.values(subModuleList.toJS()).filter((moduleData) =>
      moduleData?.subModuleName === 'Form Approver'
        ? moduleData?.levels.some((item) => item?.checked === true)
        : moduleData?.checked === true
    );
    return fromJS(moduleList);
  }

  const checkedSubModules = getCheckedSubModules();

  const getSubModuleTooltip = (checkedSubModules) => {
    const moduleList = checkedSubModules.map((moduleData) => {
      return moduleData.get('subModuleName', Map());
    });
    if (moduleList.size > 0) {
      return moduleList.slice(1).map((item, index) => <div key={index}>{item}</div>);
    }
  };

  const handleRemoveSubModules = (moduleId) => {
    setFormHeaderData((prev) => {
      return prev
        .setIn([formHeaderKey, headerIndex, 'subModuleList', moduleId, 'checked'], false)
        .updateIn([formHeaderKey, headerIndex, 'subModuleList', moduleId], (subModuleList) => {
          return subModuleList.get('subModuleName') === 'Form Approver'
            ? subModuleList.update('levels', (level) =>
                level.map((item) =>
                  item
                    .set('checked', false)
                    .update('actionsIds', (action) =>
                      action.map((item) => item.set('checked', false))
                    )
                )
              )
            : subModuleList.update('actionsIds', (action) =>
                action.map((item) => item.set('checked', false))
              );
        });
    });
  };
  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`d-flex flex-column align-items-center`}
            onClick={handleToggle(handleClick)}
          >
            <div className="d-flex">
              <div className="pr-2 cursor-pointer">Sub Modules</div>
              <AddIcon className="p-0 cursor-pointer" fontSize="small" />
            </div>

            <div className="d-flex gap-3">
              {checkedSubModules.slice(0, 1).map((moduleData, moduleIndex) => {
                const subModuleName = moduleData.get('subModuleName', '');
                const subModuleId = moduleData.get('subModuleId', '');
                return (
                  <Chip
                    label={`${subModuleName.substring(0, 8)}...`}
                    key={moduleIndex}
                    deleteIcon={
                      <Tooltips title={subModuleName}>
                        <Close
                          onClick={(event) => {
                            event.stopPropagation();
                            handleRemoveSubModules(subModuleId);
                          }}
                          className="f-16 text-black mr-1"
                        />
                      </Tooltips>
                    }
                    onDelete={() => handleRemoveSubModules(subModuleId)}
                    className="p-0 text-gray f-12"
                    sx={ChipSx}
                  />
                );
              })}
              {checkedSubModules.size > 1 && (
                <Chip
                  label={`+${checkedSubModules.size - 1}`}
                  onDelete={() => {}}
                  deleteIcon={
                    <Tooltips title={getSubModuleTooltip(checkedSubModules)}>
                      <InfoIcon sx={{ margin: '4px 5px 0px -5px' }} className="f-16 text-gray" />{' '}
                    </Tooltips>
                  }
                  className="p-0 text-black f-12"
                  sx={ChipSx}
                />
              )}
            </div>
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="fw-500 f-16 text-mGrey border-bottom pb-2">Sub Modules</div>
              {subModuleList.entrySeq().map(([subModuleId, subModule], moduleIndex) => {
                const subModuleName = subModule.get('subModuleName', '');
                const checked = subModule.get('checked', false);
                const isFormApprover = subModuleName === 'Form Approver';
                const levels = subModule.get('levels', List());
                return isFormApprover ? (
                  <DropDownHeaderApprover
                    subModuleName={subModuleName}
                    subModuleId={subModuleId}
                    levels={levels}
                    handleChangeSubModules={handleChangeSubModules}
                    parentChecked={checked}
                    handleChangeApproverLevel={handleChangeApproverLevel}
                  />
                ) : (
                  <div
                    className="d-flex align-items-center py-2  f-12 fw-400 text-mGrey border-bottom"
                    key={moduleIndex}
                  >
                    <Checkbox
                      fontSize="small"
                      checked={checked}
                      sx={checkBoxSx}
                      onChange={(e) => handleChangeSubModules(subModuleId, e.target.checked)}
                    />
                    <span className="pl-2">{subModuleName}</span>
                  </div>
                );
              })}{' '}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};

const DropDownHeaderApprover = ({
  subModuleName,
  subModuleId,
  levels,
  parentChecked,
  handleChangeSubModules,
  handleChangeApproverLevel,
}) => {
  return (
    <Accordion disableGutters elevation={0}>
      <AccordionSummary
        sx={{
          margin: 0,
          padding: '0px',
          minHeight: '40px',
          borderTop: 'none',
          '& .MuiAccordionSummary-content': {
            margin: 0,
          },
        }}
        expandIcon={<ExpandMoreOutlined />}
        className="d-flex  align-items-center  hover-color-selected f-14 fw-400 text-mGrey border-bottom"
      >
        <Checkbox
          fontSize="small"
          sx={checkBoxSx}
          onClick={(event) => event.stopPropagation()}
          onChange={(e) => handleChangeSubModules(subModuleId, e.target.checked)}
          checked={parentChecked}
        />
        <span className="pl-2 f-12 fw-400 text-mGrey">{subModuleName}</span>
      </AccordionSummary>
      <AccordionDetails>
        {levels.map((level, index) => {
          const checked = level.get('checked', false);
          return (
            <div
              className="d-flex align-items-center p-2  f-14 fw-400 text-mGrey border-bottom"
              key={index}
            >
              <SubdirectoryArrowRightIcon color="primary" fontSize="small" className="mr-2 ml-1" />
              <Checkbox
                fontSize="small"
                sx={checkBoxSx}
                checked={checked}
                onChange={(e) => handleChangeApproverLevel(subModuleId, e.target.checked, index)}
              />
              <span className="pl-2">{level.get('level', '')} </span>
            </div>
          );
        })}
      </AccordionDetails>
    </Accordion>
  );
};

export const ActionHeaderComponent = ({ headerData, formHeaderKey, headerIndex }) => {
  const { setFormHeaderData, actionData } = useQapcContext();
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };

  const subModuleList = headerData.get('subModuleList', Map());

  const handleChangeAction = (subModuleId, checked, actionIndex) => {
    setFormHeaderData((prev) => {
      return prev.setIn(
        [
          formHeaderKey,
          headerIndex,
          'subModuleList',
          subModuleId,
          'actionsIds',
          actionIndex,
          'checked',
        ],
        checked
      );
    });
  };

  const handleChangeLevelAction = (subModuleId, checked, actionIndex, levelIndex) => {
    setFormHeaderData((prev) => {
      return prev.setIn(
        [
          formHeaderKey,
          headerIndex,
          'subModuleList',
          subModuleId,
          'levels',
          levelIndex,
          'actionsIds',
          actionIndex,
          'checked',
        ],
        checked
      );
    });
  };

  function getCheckedActions() {
    const actionsList = [];
    Object.values(subModuleList.toJS()).map((moduleData) => {
      if (moduleData?.subModuleName === 'Form Approver') {
        moduleData?.levels
          .filter((level) => level?.checked === true)
          .map((level) => {
            level?.actionsIds
              .filter((item) => item?.checked === true)
              .map((action) => {
                actionsList.push({
                  level: level.level,
                  subModuleId: moduleData?.subModuleId,
                  subModuleName: moduleData?.subModuleName,
                  actionId: action.actionId,
                  checked: action.checked,
                });
              });
          });
      } else {
        if (moduleData?.checked === true) {
          moduleData?.actionsIds
            .filter((item) => item?.checked === true)
            .map((action) => {
              actionsList.push({
                subModuleId: moduleData?.subModuleId,
                subModuleName: moduleData?.subModuleName,
                actionId: action.actionId,
                checked: action.checked,
              });
            });
        }
      }
      return moduleData;
    });
    return fromJS(actionsList);
  }

  const checkedActions = getCheckedActions();

  const handleRemoveActions = (actionId, subModuleId, level = '') => {
    setFormHeaderData((prev) => {
      const copyData = [formHeaderKey, headerIndex, 'subModuleList', subModuleId];
      if (level !== '') {
        const levelIndex = prev
          .getIn([...copyData, 'levels'], List())
          .findIndex((item) => item.get('level', '') === level);
        const actionIndex = prev
          .getIn([...copyData, 'levels', levelIndex, 'actionsIds'], List())
          .findIndex((item) => item.get('actionId', '') === actionId);
        return prev.setIn(
          [...copyData, 'levels', levelIndex, 'actionsIds', actionIndex, 'checked'],
          false
        );
      }
      const actionIndex = prev
        .getIn([...copyData, 'actionsIds'], List())
        .findIndex((item) => item.get('actionId', '') === actionId);
      return prev.setIn([...copyData, 'actionsIds', actionIndex, 'checked'], false);
    });
  };

  const getActionTooltip = (checkedActions) => {
    return checkedActions
      .slice(1)
      .map((item, index) => (
        <div key={index}>{actionData.getIn([item.get('actionId', ''), 'actionName'], '')}</div>
      ));
  };

  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`d-flex flex-column align-items-center`}
            onClick={handleToggle(handleClick)}
          >
            <div className="d-flex">
              <div className="pr-2 cursor-pointer">Actions</div>
              <AddIcon className="p-0 cursor-pointer" fontSize="small" />
            </div>
            <div className="d-flex gap-3">
              {checkedActions.slice(0, 1).map((actionsData, actionIndex) => {
                const actionId = actionsData.get('actionId', '');
                const subModuleId = actionsData.get('subModuleId', '');
                const level = actionsData.get('level', '');
                const actionName = actionData.getIn(
                  [actionsData.get('actionId', ''), 'actionName'],
                  ''
                );
                return (
                  <Chip
                    label={`${actionName.substring(0, 8)}...`}
                    key={actionIndex}
                    deleteIcon={
                      <Tooltips title={actionName}>
                        <Close
                          onClick={(event) => {
                            event.stopPropagation();
                            handleRemoveActions(actionId, subModuleId, level);
                          }}
                          className="f-16 text-black mr-1"
                        />
                      </Tooltips>
                    }
                    onDelete={() => handleRemoveActions(actionId, subModuleId, level)}
                    className="p-0 text-gray f-12"
                    sx={ChipSx}
                  />
                );
              })}
              {checkedActions.size > 1 && (
                <Chip
                  label={`+${checkedActions.size - 1}`}
                  onDelete={() => {}}
                  deleteIcon={
                    <Tooltips title={getActionTooltip(checkedActions)}>
                      <InfoIcon sx={{ margin: '4px 5px 0px -5px' }} className="f-16 text-gray" />{' '}
                    </Tooltips>
                  }
                  className="p-0 text-black f-12"
                  sx={ChipSx}
                />
              )}
            </div>
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="fw-500 f-16 text-mGrey border-bottom pb-2">Action</div>
              {subModuleList
                .entrySeq()
                .filter(([_, subModule]) =>
                  subModule.get('subModuleName', '') === 'Form Approver'
                    ? subModule
                        .get('levels', List())
                        .some((item) => item?.get('checked', false) === true)
                    : subModule.get('checked', false) === true
                )
                .map(([subModuleId, subModule], moduleIndex) => {
                  const subModuleName = subModule.get('subModuleName', '');
                  const multiUserLevel = subModule.get('levels', List());
                  const mainActionsIds = subModule.get('actionsIds', List());
                  return (
                    <div key={moduleIndex}>
                      <div className="fw-500 f-16 text-mGrey border-bottom pb-2 mt-2">
                        {subModuleName}
                      </div>
                      {multiUserLevel.size > 0 ? (
                        <>
                          {multiUserLevel.map((level, levelIndex) => {
                            const levelName = level.get('level', '');
                            const actionsLevelIds = level.get('actionsIds', List());
                            const checked = level.get('checked', false);
                            if (checked === false) return <></>;
                            return (
                              <div key={levelIndex}>
                                <div className="fw-500 f-14 text-mGrey mt-2">{levelName}</div>
                                {actionsLevelIds.map((action, actionIndex) => {
                                  const actionId = action.get('actionId', '');
                                  const actionIsCheck = action.get('checked', false);
                                  const actionName = actionData.getIn([actionId, 'actionName'], '');
                                  return (
                                    <div
                                      className="d-flex align-items-center py-3  f-14 fw-400 text-mGrey border-bottom"
                                      key={actionIndex}
                                    >
                                      <Checkbox
                                        fontSize="small"
                                        checked={actionIsCheck}
                                        sx={checkBoxSx}
                                        onChange={(e) =>
                                          handleChangeLevelAction(
                                            subModuleId,
                                            e.target.checked,
                                            actionIndex,
                                            levelIndex
                                          )
                                        }
                                      />
                                      <span className="pl-2">{actionName}</span>
                                    </div>
                                  );
                                })}
                              </div>
                            );
                          })}
                        </>
                      ) : (
                        <>
                          {mainActionsIds.map((action, actionIndex) => {
                            const actionId = action.get('actionId', '');
                            const actionIsCheck = action.get('checked', false);
                            const actionName = actionData.getIn([actionId, 'actionName'], '');
                            return (
                              <div
                                className="d-flex align-items-center py-3 f-14 fw-400 text-mGrey border-bottom"
                                key={actionIndex}
                              >
                                <Checkbox
                                  fontSize="small"
                                  checked={actionIsCheck}
                                  sx={checkBoxSx}
                                  onChange={(e) =>
                                    handleChangeAction(subModuleId, e.target.checked, actionIndex)
                                  }
                                />
                                <span className="pl-2">{actionName}</span>
                              </div>
                            );
                          })}
                        </>
                      )}
                    </div>
                  );
                })}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};

export const AssignChildComponent = ({
  type,
  staffList,
  rolesStaffList,
  handleAssignChild,
  assignedIndex,
  color,
  handleAssignTypeChange,
  handleAssignRoleChange,
  handleAssignAllRoleChange,
}) => {
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };
  const [search, setSearch] = useState('');

  useEffect(() => {
    setSearch('');
  }, [type]);

  const handleUserChange = (userId, userName, checked) => {
    const userMap = Map({
      userId,
      userName,
    });
    handleAssignChild(userMap, checked, assignedIndex);
  };
  const handleChangeType = (type) => {
    handleAssignTypeChange(type, assignedIndex);
  };

  const handleChangeRolesCheck = (userId, userName, roleId, checked) => {
    const userMap = Map({
      userId,
      userName,
      roleId,
    });
    handleAssignRoleChange(userMap, checked, assignedIndex);
  };
  const handleChangeAllRoles = (roleId, userList, checked) => {
    const userMapArray = [];
    const filterRoleUser = rolesStaffList.filter((item) => item.get('roleId', '') !== roleId);
    userList.filter((item) => {
      const name = item.get('name', Map());
      const userName = `${name.get('first', '')} ${name.get('middle', '')} ${name.get('last', '')}`;
      userMapArray.push({
        userId: item.get('userId'),
        userName,
        roleId,
      });
      return item;
    });
    const updatedUserList = [...filterRoleUser.toJS(), ...userMapArray];
    handleAssignAllRoleChange(fromJS(updatedUserList), roleId, checked, assignedIndex);
  };
  const Component =
    type === 'User' ? (
      <UserListDropDown search={search} userList={staffList} handleChangeCheck={handleUserChange} />
    ) : (
      <UserRoleListDropDown
        search={search}
        savedUserList={rolesStaffList}
        handleChangeRolesCheck={handleChangeRolesCheck}
        handleChangeAllRoles={handleChangeAllRoles}
      />
    );

  const handleRemoveUser = (userId, name) => {
    const userMap = Map({
      userId,
      userName: name,
    });
    handleAssignChild(userMap, false, assignedIndex);
  };

  const getUserList = () => {
    const staffIds = staffList.map((item) => item.get('userId', ''));
    const concatUserList = staffList.concat(
      rolesStaffList.filter((item) => !staffIds.includes(item.get('userId', '')))
    );
    return concatUserList;
  };

  const userList = getUserList();

  const getUsersTooltip = (staffList) => {
    const usersList = staffList.map((userData) => {
      return userData.get('userName', Map());
    });
    if (usersList.size > 0) {
      return usersList.slice(1).map((item, index) => <div key={index}>{item}</div>);
    }
  };

  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`d-flex flex-column align-items-center`}
            onClick={handleToggle(handleClick)}
          >
            <div className="d-flex">
              <div className={`pr-2 cursor-pointer ${color}`}>Assign</div>
              <AddIcon className={`p-0 cursor-pointer ${color}`} fontSize="small" />
            </div>
            <div className="d-flex gap-3">
              {userList.slice(0, 1).map((userData, userIndex) => {
                const name = userData.get('userName', Map());
                const userId = userData.get('userId', Map());
                return (
                  <Chip
                    label={`${name.substring(0, 8)}...`}
                    key={userIndex}
                    deleteIcon={
                      <Tooltips title={name}>
                        <Close
                          onClick={(event) => {
                            event.stopPropagation();
                            handleRemoveUser(userId, name);
                          }}
                          className={`f-16 mr-1 ${color != '' ? color : 'text-black'}`}
                        />
                      </Tooltips>
                    }
                    onDelete={() => handleRemoveUser(userId, name)}
                    className="p-0 text-gray f-12"
                    sx={ChipSx}
                  />
                );
              })}
              {userList.size > 1 && (
                <Chip
                  label={`+${userList.size - 1}`}
                  onDelete={() => {}}
                  deleteIcon={
                    <Tooltips title={getUsersTooltip(userList)}>
                      <InfoIcon sx={{ margin: '4px 5px 0px -5px' }} className="f-16 text-gray" />{' '}
                    </Tooltips>
                  }
                  className="p-0 text-black f-12"
                  sx={ChipSx}
                />
              )}
            </div>
          </div>
          <PopoverComponent
            anchorEl={anchorEl}
            handleClose={() => {
              handleClose(true);
              setSearch('');
            }}
          >
            <div className="m-3">
              <div className="d-flex align-items-center justify-content-between pb-2">
                <div className="fw-500 f-16 text-mGrey">Assign</div>
                <MultiSelect
                  options={['User', 'Role']}
                  values={type}
                  onChangeSection={(value) => handleChangeType(value)}
                  multiple={false}
                  fullWidth={false}
                  placeholder={'Select'}
                  sx={{
                    width: '125px',
                    '& .MuiInputBase-input': {
                      padding: '3.5px 14px',
                    },
                  }}
                />
              </div>
              <MaterialInput
                elementType={'materialSearch'}
                type={'text'}
                size={'small'}
                value={search}
                changed={(e) => setSearch(e.target.value)}
                placeholder={'Search Name / ID'}
                addClass="f-12"
              />
              {Component}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};

export const SubModuleChildComponent = ({
  subModuleList,
  handleSubmoduleChild,
  assignedIndex,
  handleSubmoduleLevelChild,
  color,
}) => {
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };

  const handleChangeSubModules = (subModuleId, checked) => {
    handleSubmoduleChild(subModuleId, checked, assignedIndex);
  };

  const handleChangeApproverLevel = (subModuleId, checked, levelIndex) => {
    handleSubmoduleLevelChild(subModuleId, checked, levelIndex, assignedIndex);
  };

  const handleRemoveSubModules = (subModuleId) => {
    handleSubmoduleChild(subModuleId, false, assignedIndex);
  };

  function getCheckedSubModules() {
    let moduleList = Object.values(subModuleList.toJS()).filter((moduleData) =>
      moduleData?.subModuleName === 'Form Approver'
        ? moduleData?.levels.some((item) => item?.checked === true)
        : moduleData?.checked === true
    );
    return fromJS(moduleList);
  }

  const checkedSubModules = getCheckedSubModules();

  const getSubModuleTooltip = (checkedSubModules) => {
    const moduleList = checkedSubModules.map((moduleData) => {
      return moduleData.get('subModuleName', Map());
    });
    if (moduleList.size > 0) {
      return moduleList.slice(1).map((item, index) => <div key={index}>{item}</div>);
    }
  };

  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`d-flex flex-column align-items-center`}
            onClick={handleToggle(handleClick)}
          >
            <div className="d-flex">
              <div className={`pr-2 cursor-pointer ${color}`}>Sub Modules</div>
              <AddIcon className={`p-0 cursor-pointer ${color}`} fontSize="small" />
            </div>

            <div className="d-flex gap-3">
              {checkedSubModules.slice(0, 1).map((moduleData, moduleIndex) => {
                const subModuleName = moduleData.get('subModuleName', '');
                const subModuleId = moduleData.get('subModuleId', '');
                return (
                  <Chip
                    label={`${subModuleName.substring(0, 8)}...`}
                    key={moduleIndex}
                    deleteIcon={
                      <Tooltips title={subModuleName}>
                        <Close
                          onClick={(event) => {
                            event.stopPropagation();
                            handleRemoveSubModules(subModuleId);
                          }}
                          className={`f-16 mr-1 ${color != '' ? color : 'text-black'}`}
                        />
                      </Tooltips>
                    }
                    onDelete={() => handleRemoveSubModules(subModuleId)}
                    className="p-0 text-gray f-12"
                    sx={ChipSx}
                  />
                );
              })}
              {checkedSubModules.size > 1 && (
                <Chip
                  label={`+${checkedSubModules.size - 1}`}
                  onDelete={() => {}}
                  deleteIcon={
                    <Tooltips title={getSubModuleTooltip(checkedSubModules)}>
                      <InfoIcon sx={{ margin: '4px 5px 0px -5px' }} className="f-16 text-gray" />{' '}
                    </Tooltips>
                  }
                  className="p-0 text-black f-12"
                  sx={ChipSx}
                />
              )}
            </div>
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="fw-500 f-16 text-mGrey border-bottom pb-2">Sub Modules</div>
              {subModuleList.entrySeq().map(([subModuleId, subModule], moduleIndex) => {
                const subModuleName = subModule.get('subModuleName', '');
                const checked = subModule.get('checked', false);
                const isFormApprover = subModuleName === 'Form Approver';
                const levels = subModule.get('levels', List());
                return isFormApprover ? (
                  <DropDownHeaderApprover
                    subModuleName={subModuleName}
                    subModuleId={subModuleId}
                    levels={levels}
                    handleChangeSubModules={handleChangeSubModules}
                    parentChecked={
                      checked || levels.every((item) => item.get('checked', false) === true)
                    }
                    handleChangeApproverLevel={handleChangeApproverLevel}
                  />
                ) : (
                  <div
                    className="d-flex align-items-center py-2  f-12 fw-400 text-mGrey border-bottom"
                    key={moduleIndex}
                  >
                    <Checkbox
                      fontSize="small"
                      checked={checked}
                      sx={checkBoxSx}
                      onChange={(e) => handleChangeSubModules(subModuleId, e.target.checked)}
                    />
                    <span className="pl-2">{subModuleName}</span>
                  </div>
                );
              })}{' '}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};

export const ActionChildComponent = ({
  subModuleList,
  handleActionChild,
  assignedIndex,
  handleActionLevelChild,
  handleActionRemoveChild,
  color,
}) => {
  const { actionData } = useQapcContext();
  const handleToggle = (callback) => (event) => {
    event.stopPropagation();
    callback(event);
  };

  const handleChangeAction = (subModuleId, checked, actionIndex) => {
    handleActionChild(subModuleId, checked, actionIndex, assignedIndex);
  };

  const handleChangeLevelAction = (subModuleId, checked, actionIndex, levelIndex) => {
    handleActionLevelChild(subModuleId, checked, actionIndex, levelIndex, assignedIndex);
  };

  const handleRemoveActions = (actionId, subModuleId, level = '') => {
    handleActionRemoveChild(assignedIndex, actionId, subModuleId, level);
  };

  function getCheckedActions() {
    const actionsList = [];
    Object.values(subModuleList.toJS()).map((moduleData) => {
      if (moduleData?.subModuleName === 'Form Approver') {
        moduleData?.levels
          .filter((level) => level?.checked === true)
          .map((level) => {
            level?.actionsIds
              .filter((item) => item?.checked === true)
              .map((action) => {
                actionsList.push({
                  level: level.level,
                  subModuleId: moduleData?.subModuleId,
                  subModuleName: moduleData?.subModuleName,
                  actionId: action.actionId,
                  checked: action.checked,
                });
              });
          });
      } else {
        if (moduleData?.checked === true) {
          moduleData?.actionsIds
            .filter((item) => item?.checked === true)
            .map((action) => {
              actionsList.push({
                subModuleId: moduleData?.subModuleId,
                subModuleName: moduleData?.subModuleName,
                actionId: action.actionId,
                checked: action.checked,
              });
            });
        }
      }
      return moduleData;
    });
    return fromJS(actionsList);
  }

  const checkedActions = getCheckedActions();

  const getActionTooltip = (checkedActions) => {
    return checkedActions
      .slice(1)
      .map((item, index) => (
        <div key={index}>{actionData.getIn([item.get('actionId', ''), 'actionName'], '')}</div>
      ));
  };

  return (
    <ParentComponent>
      {(anchorEl, handleClick, handleClose) => (
        <>
          <div
            className={`d-flex flex-column align-items-center`}
            onClick={handleToggle(handleClick)}
          >
            <div className="d-flex">
              <div className={`pr-2 cursor-pointer ${color}`}>Actions</div>
              <AddIcon className={`p-0 cursor-pointer ${color}`} fontSize="small" />
            </div>
            <div className="d-flex gap-3">
              {checkedActions.slice(0, 1).map((actionsData, actionIndex) => {
                const actionId = actionsData.get('actionId', '');
                const subModuleId = actionsData.get('subModuleId', '');
                const level = actionsData.get('level', '');
                const actionName = actionData.getIn(
                  [actionsData.get('actionId', ''), 'actionName'],
                  ''
                );
                return (
                  <Chip
                    label={`${actionName.substring(0, 8)}...`}
                    key={actionIndex}
                    deleteIcon={
                      <Tooltips title={actionName}>
                        <Close
                          onClick={(event) => {
                            event.stopPropagation();
                            handleRemoveActions(actionId, subModuleId, level);
                          }}
                          className={`f-16 mr-1 ${color != '' ? color : 'text-black'}`}
                        />
                      </Tooltips>
                    }
                    onDelete={() => handleRemoveActions(actionId, subModuleId, level)}
                    className="p-0 text-gray f-12"
                    sx={ChipSx}
                  />
                );
              })}
              {checkedActions.size > 1 && (
                <Chip
                  label={`+${checkedActions.size - 1}`}
                  onDelete={() => {}}
                  deleteIcon={
                    <Tooltips title={getActionTooltip(checkedActions)}>
                      <InfoIcon sx={{ margin: '4px 5px 0px -5px' }} className="f-16 text-gray" />{' '}
                    </Tooltips>
                  }
                  className="p-0 text-black f-12"
                  sx={ChipSx}
                />
              )}
            </div>
          </div>
          <PopoverComponent anchorEl={anchorEl} handleClose={handleClose}>
            <div className="m-3">
              <div className="fw-500 f-16 text-mGrey border-bottom pb-2">Action</div>
              {subModuleList
                .entrySeq()
                .filter(([_, subModule]) =>
                  subModule.get('subModuleName', '') === 'Form Approver'
                    ? subModule
                        .get('levels', List())
                        .some((item) => item?.get('checked', false) === true)
                    : subModule.get('checked', false) === true
                )
                .map(([subModuleId, subModule], moduleIndex) => {
                  const subModuleName = subModule.get('subModuleName', '');
                  const multiUserLevel = subModule.get('levels', List());
                  const mainActionsIds = subModule.get('actionsIds', List());
                  return (
                    <div key={moduleIndex}>
                      <div className="fw-500 f-16 text-mGrey border-bottom pb-2 mt-2">
                        {subModuleName}
                      </div>
                      {multiUserLevel.size > 0 ? (
                        <>
                          {multiUserLevel.map((level, levelIndex) => {
                            const levelName = level.get('level', '');
                            const actionsLevelIds = level.get('actionsIds', List());
                            const checked = level.get('checked', false);
                            if (checked === false) return <></>;
                            return (
                              <div key={levelIndex}>
                                <div className="fw-500 f-14 text-mGrey mt-2">{levelName}</div>
                                {actionsLevelIds.map((action, actionIndex) => {
                                  const actionId = action.get('actionId', '');
                                  const actionIsCheck = action.get('checked', false);
                                  const actionName = actionData.getIn([actionId, 'actionName'], '');
                                  return (
                                    <div
                                      className="d-flex align-items-center py-3  f-14 fw-400 text-mGrey border-bottom"
                                      key={actionIndex}
                                    >
                                      <Checkbox
                                        fontSize="small"
                                        checked={actionIsCheck}
                                        sx={checkBoxSx}
                                        onChange={(e) =>
                                          handleChangeLevelAction(
                                            subModuleId,
                                            e.target.checked,
                                            actionIndex,
                                            levelIndex
                                          )
                                        }
                                      />
                                      <span className="pl-2">{actionName}</span>
                                    </div>
                                  );
                                })}
                              </div>
                            );
                          })}
                        </>
                      ) : (
                        <>
                          {mainActionsIds.map((action, actionIndex) => {
                            const actionId = action.get('actionId', '');
                            const actionIsCheck = action.get('checked', false);
                            const actionName = actionData.getIn([actionId, 'actionName'], '');
                            return (
                              <div
                                className="d-flex align-items-center py-3 f-14 fw-400 text-mGrey border-bottom"
                                key={actionIndex}
                              >
                                <Checkbox
                                  fontSize="small"
                                  checked={actionIsCheck}
                                  sx={checkBoxSx}
                                  onChange={(e) =>
                                    handleChangeAction(subModuleId, e.target.checked, actionIndex)
                                  }
                                />
                                <span className="pl-2">{actionName}</span>
                              </div>
                            );
                          })}
                        </>
                      )}
                    </div>
                  );
                })}
            </div>
          </PopoverComponent>
        </>
      )}
    </ParentComponent>
  );
};
