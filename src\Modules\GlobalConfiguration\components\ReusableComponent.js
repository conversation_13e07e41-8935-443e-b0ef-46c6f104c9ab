// import ExpandLess from '@mui/icons-material/ExpandLess';
import React from 'react';
import PropTypes from 'prop-types';

import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { styled, createTheme, ThemeProvider } from '@mui/material/styles';
const theme = createTheme({
  palette: {
    primary: {
      main: '#147afc',
    },
    secondary: {
      main: '#f44336',
    },
  },
});
const Accordion = styled((props) => (
  <MuiAccordion disablegutters="true" elevation={0} square {...props} />
))(({ theme }) => ({
  border: 0,
  '& .MuiAccordionSummary-root': {
    padding: 0,
    alignItems: 'flex-start',
  },
  '&:not(:last-child)': {
    borderBottom: '1px',
  },
  '&:before': {
    display: 'none',
  },
}));

const AccordionSummary = styled((props) => (
  <MuiAccordionSummary
    expandIcon={
      <ThemeProvider theme={theme}>
        <ExpandMoreIcon color={'primary'} />
      </ThemeProvider>
    }
    {...props}
  />
))(({ theme }) => ({
  flexDirection: 'row-reverse',
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(90deg)',
  },
  '& .MuiAccordionSummary-content': {
    marginLeft: theme.spacing(1),
    margin: '0px 0px 12px 12px',
  },
  '& .MuiAccordionSummary-content.Mui-expanded': {
    margin: '0px 0 12px 12px',
  },
}));
const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  color: '#3d5170',
}));
export const AccordionProgramInput = ({ summaryChildren, detailChildren, onClick, expanded }) => {
  return (
    <div>
      <Accordion expanded={expanded}>
        <AccordionSummary onClick={onClick}>{summaryChildren}</AccordionSummary>
        <AccordionDetails>{detailChildren}</AccordionDetails>
      </Accordion>
    </div>
  );
};

AccordionProgramInput.propTypes = {
  summaryChildren: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  detailChildren: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
  onClick: PropTypes.func,
  expanded: PropTypes.bool,
};
