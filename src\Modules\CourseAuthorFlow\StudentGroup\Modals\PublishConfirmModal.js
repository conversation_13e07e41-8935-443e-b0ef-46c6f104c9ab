import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
// import CloseIcon from '@mui/icons-material/Close';
import { useCallGroupSettings } from '../Dashboard';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { useDispatch, useSelector } from 'react-redux';
import { publishSettings } from '_reduxapi/studentGroupV2/action';
import { Map } from 'immutable';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
const subTitleSx = {
  marginRight: '40px',
  fontSize: '12px',
  fontStyle: 'italic',
  color: '#15803D',
};
// const closeIconSx = {
//   position: 'absolute',
//   right: 8,
//   top: 11,
// };
const descriptionSx = {
  color: '#374151',
};
const formLabelSx = {
  '& .MuiFormControlLabel-label': {
    color: '#4B5563',
  },
};
const radioSx = {
  color: '#6B7280',
  '&.Mui-checked': {
    color: '#0064C8 !important',
  },
};
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};

const PublishConfirmModal = ({ open, data, type, isPublished, handleClose }) => {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const { groupSettings, callGroupSettings } = useCallGroupSettings({
    selectedData: data,
  });
  const [notifyType, setNotifyType] = useState('');

  useEffect(() => {
    if (groupSettings.isEmpty()) callGroupSettings();
  }, []);

  const handleSave = () => {
    const institutionCalendarId = activeInstitutionCalendar.get('_id');
    const groupSettingId = groupSettings.get('_id');
    const requestBody = { institutionCalendarId, groupSettingId };
    dispatch(
      publishSettings({
        requestBody,
        callback: () => {
          callGroupSettings();
          handleClose();
        },
        type,
        notifyType,
      })
    );
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          {type === 'deliveryGroups' ? 'Publish Grouping Settings' : 'Publish Grouped Students'}
          {isPublished && (
            <Typography sx={subTitleSx}>New changes will only be notified!</Typography>
          )}
        </Box>
      </DialogTitle>
      {/* <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
        <CloseIcon />
      </IconButton> */}
      <DialogContent className="p-3">
        <Typography className="mb-1 f-14" sx={descriptionSx}>
          {type === 'deliveryGroups' ? (
            <>
              Grouping settings will be notified to <b>Course Coordinators</b> via:
            </>
          ) : (
            'Grouped students will be notified via:'
          )}
        </Typography>
        <RadioGroup row value={notifyType} onChange={(e) => setNotifyType(e.target.value)}>
          {/* <FormControlLabel
            sx={formLabelSx}
            value="sms"
            control={<Radio sx={radioSx} />}
            label="SMS"
          /> */}
          <FormControlLabel
            sx={formLabelSx}
            value="email"
            control={<Radio sx={radioSx} />}
            label="Email"
          />
          <FormControlLabel
            sx={formLabelSx}
            value="nothing"
            control={<Radio sx={radioSx} />}
            label="Don’t Notify"
          />
        </RadioGroup>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" sx={buttonSx} clicked={handleClose}>
          Cancel
        </MButton>
        <MButton
          variant="contained"
          color="primary"
          sx={buttonSx}
          clicked={handleSave}
          disabled={!notifyType}
        >
          Confirm
        </MButton>
      </DialogActions>
    </Dialog>
  );
};

PublishConfirmModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  type: PropTypes.string,
  isPublished: PropTypes.bool,
  handleClose: PropTypes.func,
};

export default PublishConfirmModal;
