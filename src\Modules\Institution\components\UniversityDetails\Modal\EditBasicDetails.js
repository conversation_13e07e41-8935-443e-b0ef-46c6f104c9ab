import React, { useState } from 'react';
import { Modal } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import College from '../../../../../Assets/college_icon.svg';
import completed from '../../../../../Assets/completed.svg';
import CurrentPage from '../../../../../Assets/currentPage.svg';
import nextPage from '../../../../../Assets/nextPage.svg';
import Vector from '../../../../../Assets/vector.svg';

import { INSTITUTION_TYPES } from '../../InstitutionOnboarding/Component/InstitutionType';
import UploadImage from './EditComponents/UploadImage';
import EditDetails from './EditComponents/EditDetails';
import { checkPage } from '../../UniversityDetails/udUtil';
import MButton from 'Widgets/FormElements/material/Button';
function EditBasicDetails({
  show,
  handleClose,
  institutionDetails,
  fetchData,
  setData,
  cancelShow,
}) {
  const [currentPage, setCurrentPage] = useState(
    institutionDetails.isUniversity && institutionDetails.collegeCount === 0 ? 1 : 2
  );
  const [selectedType, setSelectedType] = useState(
    institutionDetails.type === 'University' ? 'group' : 'institution'
  );
  const [updatedDetails, setUpdatedDetails] = useState(institutionDetails);
  const [isChanged, setIsChanged] = useState(false);
  const childFunc = React.useRef(null);

  const onClickCloseOrSave = (type) => {
    if (type === 'back') return setCurrentPage(1);
    if (type === 'next') {
      setCurrentPage(2);
      setUpdatedDetails({ ...institutionDetails, selectedType });
    }
  };

  const onClickSaved = () => {
    childFunc.current();
  };

  function actionButton(type = '') {
    return (
      <div
        style={{
          width: institutionDetails?.isUniversity ? '50%' : '100%',
          marginTop: institutionDetails?.isUniversity ? '0%' : '-70px',
        }}
        className={`text-right ${type === 'edit' ? 'mt-4 mr-3' : ''}`}
      >
        {institutionDetails.isUniversity &&
          institutionDetails.collegeCount === 0 &&
          currentPage === 2 && (
            <span className="remove_hover mr-3 pt-2" onClick={() => onClickCloseOrSave('back')}>
              <Trans i18nKey={'back'}></Trans>
            </span>
          )}
        <MButton
          className="mr-3"
          color="inherit"
          variant="outlined"
          clicked={() => {
            if (isChanged) {
              handleClose('editDetails', 'cancel');
            } else {
              handleClose();
            }
          }}
        >
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>

        {institutionDetails.isUniversity &&
        institutionDetails.collegeCount === 0 &&
        currentPage !== 2 ? (
          <MButton
            className={`${
              currentPage === 2 ? 'btn btn-ins-login' : 'btn btn-primary'
            } remove_hover`}
            clicked={() => onClickCloseOrSave(currentPage === 2 ? 'back' : 'next')}
          >
            <Trans i18nKey={currentPage === 2 ? 'back' : 'next'}></Trans>
            <img src={Vector} alt="nextPage" className="ml-2" />
          </MButton>
        ) : (
          // <button
          //   className={`${
          //     currentPage === 2 ? 'btn btn-ins-login' : 'btn btn-primary'
          //   } remove_hover mr-3`}
          //   onClick={() => onClickCloseOrSave(currentPage === 2 ? 'back' : 'next')}
          // >
          //   <Trans i18nKey={currentPage === 2 ? 'back' : 'next'}></Trans>
          // </button>
          ''
        )}

        {currentPage === 2 && (
          <MButton
            className="text-uppercase"
            clicked={() => {
              onClickSaved();
              //handleClose('editDetails', 'saveDetails', '', updatedDetails);
            }}
          >
            <Trans i18nKey={'add_colleges.save'}></Trans>
          </MButton>
        )}
      </div>
    );
  }

  //collegeCount
  return (
    <Modal
      onHide={() => {}}
      dialogClassName={`model-900 ${cancelShow ? 'display-none' : 'display-block'}`}
      show={show}
    >
      <div className="d-flex mt-2" style={{ width: '100%' }}>
        <div style={{ width: '50%' }}>
          <h5 className="p-3 mt-3">
            Edit {institutionDetails.collegeCount !== 0 && 'Basic Details'}
            {!institutionDetails?.isUniversity && 'College Details'}
          </h5>
        </div>
        {institutionDetails.collegeCount !== 0 && <>{actionButton('edit')}</>}
      </div>
      <Modal.Header>
        {institutionDetails.collegeCount === 0 && (
          <div className="d-flex" style={{ width: '100%' }}>
            {institutionDetails?.isUniversity && (
              <>
                <div className="row ml-1 mt-2" style={{ width: '50%' }}>
                  <div className={`f-16 ${checkPage(currentPage, 'digi-blue', 'digi-brown')}`}>
                    <img
                      src={checkPage(currentPage, CurrentPage, completed)}
                      alt="currentPage"
                      className="mr-2"
                    />
                    Select type
                  </div>
                  <span className="digi-bdr-solid "></span>
                  <div className={`f-16 ${checkPage(currentPage, 'digi-brown', 'digi-blue')}`}>
                    <img
                      src={checkPage(currentPage, nextPage, CurrentPage)}
                      alt="nextPage"
                      className="mr-2"
                    />
                    Basic Details
                  </div>
                </div>
              </>
            )}
            {actionButton()}
          </div>
        )}
      </Modal.Header>
      <Modal.Body className={`pt-0 ${currentPage === 1 ? 'institution-type' : ''}`}>
        {currentPage === 1 && (
          <div style={{ paddingRight: '6rem', paddingLeft: '6rem' }}>
            <div className="row pt-2">
              <p className="mb-1 f-20 pt-2 bold">Choose your type of institution</p>
            </div>
            {INSTITUTION_TYPES.map((institutionType, i) => (
              <div
                key={`${institutionType.title}-${i}`}
                className="pb-2 pt-2 cursor-pointer"
                onClick={() => {
                  setSelectedType(institutionType.type);
                  setIsChanged(true);
                }}
              >
                <div
                  className={`row rounded p-3 ${
                    institutionType.type === selectedType
                      ? 'university-view-active'
                      : 'university-view'
                  }`}
                >
                  <div className="col-md-3 pr-0 pt-1">
                    <img
                      src={institutionType.logo}
                      width="60px"
                      height="60px"
                      alt="universityLogo"
                    />
                  </div>
                  <div className="col-md-9 pl-0">
                    <p className=" mb-1 f-17 bold">{institutionType.title}</p>
                    <small className="text-justify f-14">{institutionType.subtitle}</small>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        {currentPage === 2 && (
          <div className="col-md-12">
            <div className="mt-3">
              <div className="row">
                <UploadImage
                  updatedDetails={updatedDetails}
                  setUpdatedDetails={setUpdatedDetails}
                  setData={setData}
                />

                <div className="col-md-5 col-sm-4">
                  <div className="float-right">
                    <img src={College} alt="college" className="img-fluid" />
                  </div>
                </div>
                <div className="clearfix"></div>
              </div>
              <EditDetails
                updatedDetails={updatedDetails}
                setUpdatedDetails={setUpdatedDetails}
                childFunc={childFunc}
                fetchData={fetchData}
                selectedType={selectedType}
                setIsChanged={setIsChanged}
              />
            </div>
          </div>
        )}
      </Modal.Body>
    </Modal>
  );
}

EditBasicDetails.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
  fetchData: PropTypes.func,
  setData: PropTypes.func,
  institutionDetails: PropTypes.object,
  cancelShow: PropTypes.bool,
};

export default EditBasicDetails;
