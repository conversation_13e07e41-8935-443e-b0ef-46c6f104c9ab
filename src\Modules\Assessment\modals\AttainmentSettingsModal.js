import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/assessment/action';
import { selectAttainmentPrerequisite } from '_reduxapi/assessment/selector';
import MButton from 'Widgets/FormElements/material/Button';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { fromJS, List, Map } from 'immutable';
import { getURLParams, indVerRename } from 'utils';
import CancelModal from 'Containers/Modal/Cancel';

const AttainmentSettingsModal = ({
  show,
  onClose,
  attainmentPrerequisite,
  getAttainmentPrerequisite,
  addAttainment,
  getAttainmentList,
  attainmentList = List(),
  setData,
  outcomes = List(),
  data = Map(),
  getAttainment,
  handleView,
}) => {
  const programId = getURLParams('pid', true);
  const type = getURLParams('type', true);
  const courseId = getURLParams('courseId', true);
  const term = getURLParams('term', true);
  const levelNo = getURLParams('levelNo', true);
  const [edited, setEdited] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [regulationDetails, setRegulationDetails] = useState(
    fromJS({
      regulationYear: data.get('regulationYear', ''),
      regulationName: data.get('regulationName', ''),
      curriculum: data.get('_curriculum_id', ''),
      outcome: !outcomes.size ? data.get('outcomes', List()) : List(),
    })
  );

  useEffect(() => {
    getAttainmentPrerequisite(programId);
  }, [programId]); //eslint-disable-line

  const getOptions = (key) => {
    let options = attainmentPrerequisite
      .get(key, List())
      .map((item) => {
        return key === 'programCurriculums'
          ? { name: item.get('curriculum_name', ''), value: item.get('_id', '') }
          : key === 'outComes'
          ? { name: indVerRename(item.toUpperCase(), programId), value: item }
          : { name: item, value: item };
      })
      .toJS();

    if (key === 'outComes') return options.filter((item) => !outcomes.includes(item.value));
    else return [{ name: 'Select', value: '' }, ...options];
  };

  const handleChange = (e, key) => {
    const value = e.target.value;
    setRegulationDetails(regulationDetails.set(key, key === 'outcome' ? fromJS(value) : value));
    setEdited(true);
  };

  const handleCancel = () => {
    edited ? setShowCancelModal(true) : onClose();
  };

  const isDisableSave = () => {
    return (
      !edited ||
      !regulationDetails.get('regulationYear', '') ||
      !regulationDetails.get('regulationName', '') ||
      !regulationDetails.get('curriculum', '') ||
      !regulationDetails.get('outcome', List()).size
    );
  };

  const handleSave = () => {
    const attainmentId = data.get('_id', '');
    const regulationNameExists = attainmentList.filter(
      (item) =>
        item.get('regulationName', '')?.toLowerCase() ===
          regulationDetails.get('regulationName', '')?.toLowerCase() &&
        (!attainmentId || attainmentId !== item.get('_id', ''))
    );
    if (regulationNameExists.size) {
      setData(Map({ message: 'Regulation name already exists' }));
      return false;
    }

    const curriculumName = attainmentPrerequisite
      .get('programCurriculums', List())
      .find((item) => item.get('_id', '') === regulationDetails.get('curriculum', ''))
      .get('curriculum_name', '');
    const requestBody = {
      ...(attainmentId ? { _id: attainmentId } : { programId }),
      regulationYear: regulationDetails.get('regulationYear', ''),
      regulationName: regulationDetails.get('regulationName', '').trim(),
      curriculumId: regulationDetails.get('curriculum', ''),
      curriculumName,
      ...((!attainmentId || outcomes.size > 0) && {
        outcomes: !outcomes.size
          ? regulationDetails.get('outcome', List()).toJS()
          : outcomes.merge(regulationDetails.get('outcome', List())).toJS(),
      }),
      type: type,
    };
    const requestParams = { programId, courseId, levelNo, term };
    addAttainment({
      mode: attainmentId ? 'edit' : 'add',
      data: requestBody,
      callBack: (newAttainmentId) => {
        if (newAttainmentId) handleView(newAttainmentId);
        else if (!outcomes.size) getAttainmentList(programId, type);
        else getAttainment(attainmentId, type, requestParams);
        onClose();
      },
    });
  };

  return (
    <>
      <DialogModal show={show} onClose={handleCancel} maxWidth="xs" fullWidth={true}>
        <div className="w-100 p-4">
          <p className="mb-3 pb-2 border-bottom bold f-19">
            {outcomes.size
              ? `Create Attainment Tree For ${data.get('regulationName', '')} - ${data.get(
                  'regulationYear',
                  ''
                )} ${data.get('curriculumName', '')}`
              : data.get('_id', '')
              ? 'Edit Attainment Tree'
              : 'Create Attainment Tree'}
          </p>

          <div className="mt-2 mb-3">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              labelclass={'mb-1 f-14'}
              label={'Regulation Year'}
              elementConfig={{ options: getOptions('regularizationYears') }}
              value={regulationDetails.get('regulationYear', '')}
              changed={(e) => handleChange(e, 'regulationYear')}
              disabled={outcomes.size > 0}
            />
          </div>

          <div className="mt-2 mb-3">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              labelclass={'mb-1 f-14'}
              maxLength={100}
              label={'Regulation Name'}
              placeholder={'Regulation Name'}
              value={regulationDetails.get('regulationName', '')}
              changed={(e) => handleChange(e, 'regulationName')}
              disabled={outcomes.size > 0}
            />
          </div>

          <div className="mt-2 mb-3">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              labelclass={'mb-1 f-14'}
              label={'Curriculum'}
              elementConfig={{ options: getOptions('programCurriculums') }}
              value={regulationDetails.get('curriculum', '')}
              changed={(e) => handleChange(e, 'curriculum')}
              disabled={outcomes.size > 0}
            />
          </div>

          <div className="mt-2 mb-3">
            <MaterialInput
              elementType={'materialSelectMultiple'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              isAllSelected={false}
              labelclass={'mb-1 f-14'}
              label={`Select ${outcomes.size ? 'Root Node' : 'Outcome'}`}
              elementConfig={{ options: getOptions('outComes') }}
              value={regulationDetails.get('outcome', List()).toJS()}
              changed={(e) => handleChange(e, 'outcome')}
              disabled={data.get('_id', '') !== '' && !outcomes.size}
              multiple={true}
            />
          </div>

          <div className="d-flex justify-content-end border-top pt-3">
            <MButton variant="outlined" color="gray" className="mr-2" clicked={handleCancel}>
              Cancel
            </MButton>
            <MButton
              variant="contained"
              color="primary"
              clicked={handleSave}
              disabled={isDisableSave()}
            >
              Save
            </MButton>
          </div>
        </div>
      </DialogModal>

      <CancelModal showCancel={showCancelModal} setCancel={setShowCancelModal} setShow={onClose} />
    </>
  );
};

AttainmentSettingsModal.propTypes = {
  show: PropTypes.bool,
  onClose: PropTypes.func,
  attainmentPrerequisite: PropTypes.instanceOf(Map),
  getAttainmentPrerequisite: PropTypes.func,
  addAttainment: PropTypes.func,
  getAttainmentList: PropTypes.func,
  attainmentList: PropTypes.instanceOf(List),
  setData: PropTypes.func,
  outcomes: PropTypes.instanceOf(List),
  data: PropTypes.instanceOf(Map),
  getAttainment: PropTypes.func,
  handleView: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    attainmentPrerequisite: selectAttainmentPrerequisite(state),
  };
};

export default connect(mapStateToProps, actions)(AttainmentSettingsModal);
