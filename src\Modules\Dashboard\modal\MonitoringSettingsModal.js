import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';
import { Map, List } from 'immutable';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { getLang, getEnvLabelChanged, getVersionName } from '../../../utils';
import { levelRename } from '../../../utils';
import Loader from '../../../Widgets/Loader/Loader';
import { t } from 'i18next';

function MonitoringSettingsModal({
  configuredProgramLists,
  departmentSubjectLists,
  updateConfigureSettings,
  setData,
  settingsLoading,
  show,
  showModal,
  getConfigureSettings,
}) {
  MonitoringSettingsModal.propTypes = {
    settingsLoading: PropTypes.instanceOf(Map),
    show: PropTypes.bool,
    showModal: PropTypes.func,
    getConfigureSettings: PropTypes.func,
    configuredProgramLists: PropTypes.instanceOf(List),
    departmentSubjectLists: PropTypes.instanceOf(List),
    updateConfigureSettings: PropTypes.func,
    setData: PropTypes.func,
  };

  const lang = getLang();
  const [activeProgram, setActiveProgram] = useState(
    Map({
      name: '',
      id: '',
      type: 'Academic Year / Level',
    })
  );
  const [activeDepartment, setActiveDepartment] = useState(Map({ name: '', id: '' }));
  const [configuration, setConfiguration] = useState(List());
  const [configureDepartmentSubject, setConfigureDepartmentSubject] = useState(List());
  const [tabs, setTabs] = useState('Configure');
  const typesList = ['Academic Year / Level', 'Department / Subject'];
  const [activeCourse, setActiveCourse] = useState('Admin Course');

  useEffect(() => {
    getConfigureSettings();
  }, [getConfigureSettings]);

  useEffect(() => {
    if (configuredProgramLists && configuredProgramLists.size > 0) {
      setActiveProgram(
        activeProgram
          .set('name', configuredProgramLists.getIn([0, 'programName'], ''))
          .set('id', configuredProgramLists.getIn([0, '_program_id', 0], ''))
      );
    }
  }, [configuredProgramLists]); // eslint-disable-line

  useEffect(() => {
    setConfiguration(configuredProgramLists);
  }, [configuredProgramLists]);

  useEffect(() => {
    const dept =
      departmentSubjectLists.size > 0 &&
      departmentSubjectLists.filter((item) => item.get('program_id') === activeProgram.get('id'));
    setConfigureDepartmentSubject(dept !== false ? dept : List());
    if (dept !== false && dept.size > 0) {
      setActiveDepartment(
        activeDepartment
          .set('name', dept.getIn([0, 'department_name'], ''))
          .set('id', dept.getIn([0, '_id'], ''))
      );
    }
  }, [departmentSubjectLists, activeProgram]); // eslint-disable-line

  const concatYearLevelTerm = (lvl) => {
    return `${t('dashboard_view.year')} ${lvl.get('year').replace('year', '')} / ${
      getEnvLabelChanged()
        ? levelRename(lvl.get('level_no'), activeProgram.get('id', ''))
        : t('dashboard_view.level') + ' ' + lvl.get('level_no').replace('Level', '')
    } (${lvl.get('term')})`;
  };

  const getYearAndLevel = () => {
    return (
      configuration.size > 0 &&
      configuration
        .filter((item) => item.getIn(['_program_id', 0], '') === activeProgram.get('id'))
        .map((program, pgIndex) => {
          return (
            <React.Fragment key={pgIndex}>
              {program
                .get('level', List())
                .filter((item) =>
                  tabs === 'Monitoring' ? item.get('isConfigured') === true : item
                )
                .map((level, index) => {
                  return (
                    <p
                      className="f-16 mb-4 text-lightblack"
                      key={index}
                      onClick={() =>
                        tabs === 'Configure'
                          ? setConfigureData(
                              program.getIn(['_program_id', 0]),
                              index,
                              !level.get('isConfigured', false)
                            )
                          : {}
                      }
                    >
                      {' '}
                      {tabs === 'Configure' ? (
                        <Form.Check
                          type="checkbox"
                          className="ml-3 mr-3"
                          label={concatYearLevelTerm(level)}
                          defaultChecked={level.get('isConfigured', false)}
                        />
                      ) : (
                        concatYearLevelTerm(level)
                      )}{' '}
                    </p>
                  );
                })}
            </React.Fragment>
          );
        })
    );
  };

  const setConfigureData = (programId, levelIndex, status) => {
    const programIndex = configuration.findIndex(
      (item) => item.getIn(['_program_id', 0]) === programId
    );

    let updateValue = configuration
      .setIn([0, 'touched'], true)
      .setIn([programIndex, 'level', levelIndex, 'isConfigured'], status)
      .setIn([programIndex, 'level', levelIndex, 'touched'], true);
    if (!status) {
      updateValue = configuration
        .setIn([0, 'touched'], true)
        .setIn([programIndex, 'level', levelIndex, 'isConfigured'], status)
        .setIn(
          [programIndex, 'level', levelIndex, 'course'],
          configuration.getIn([programIndex, 'level', levelIndex, 'course'], List()).map((row) => {
            return row.set('isConfigured', false).set('touched', true);
          })
        );
    }
    setConfiguration(updateValue);
  };

  const getYearAndLevelCourse = () => {
    return (
      configuration.size > 0 &&
      configuration
        .filter((item) => item.getIn(['_program_id', 0], '') === activeProgram.get('id'))
        .map((program, pgIndex) => {
          //let courseRotation = [];
          return (
            <React.Fragment key={pgIndex}>
              {program
                .get('level', List())
                .filter((item) => item.get('isConfigured') === true)
                .map((level, index) => {
                  return (
                    <React.Fragment key={index}>
                      <div className="mb-2" style={{ color: '#b7b1b1' }}>
                        {concatYearLevelTerm(level)}
                      </div>
                      {level
                        .get('course', List())
                        .filter((item) =>
                          tabs === 'Monitoring' ? item.get('isConfigured') === true : item
                        )
                        .sort((a, b) =>
                          a.get('courses_number') > b.get('courses_number') ? 1 : -1
                        )
                        .map((course, cIndex) => {
                          // if (
                          //   course.get('rotation', 'no') === 'yes' &&
                          //   !courseRotation.includes(course.get('_course_id'))
                          // ) {
                          //   courseRotation.push(course.get('_course_id'));
                          // }
                          // {(course.get('rotation', 'no') === 'no' ||
                          //       (courseRotation.includes(course.get('_course_id')) &&
                          //         course.get('rotation_count', 0) === 1)) && (
                          //console.log('course', course);
                          return (
                            <p
                              className="f-16 mb-4 text-lightblack"
                              key={cIndex}
                              onClick={() =>
                                tabs === 'Configure'
                                  ? setConfigureCourseData(
                                      program.getIn(['_program_id', 0]),
                                      level.get('_id'),
                                      course.get('_course_id'),
                                      course.get('term'),
                                      course.get('rotation_count', 0),
                                      !course.get('isConfigured', false)
                                    )
                                  : {}
                              }
                            >
                              {' '}
                              {tabs === 'Configure' ? (
                                <Form.Check
                                  type="checkbox"
                                  className="ml-3 mr-3"
                                  label={`${course.get('courses_number')} - ${course.get(
                                    'courses_name'
                                  )} ${
                                    course.get('rotation', '') === 'yes'
                                      ? ' - R' + course.get('rotation_count', '')
                                      : ''
                                  }${getVersionName(course)}`}
                                  defaultChecked={course.get('isConfigured', false)}
                                />
                              ) : (
                                course.get('courses_number') +
                                ' - ' +
                                course.get('courses_name') +
                                getVersionName(course) +
                                (course.get('rotation', '') === 'yes'
                                  ? ' - R' + course.get('rotation_count', '')
                                  : '')
                              )}{' '}
                            </p>
                          );
                        })}
                    </React.Fragment>
                  );
                })}
            </React.Fragment>
          );
        })
    );
  };

  const setConfigureDepartment = (dId, sId, status) => {
    const dIndex = configureDepartmentSubject.findIndex(
      (department) => department.get('_id') === dId
    );
    const sIndex = configureDepartmentSubject
      .getIn([dIndex, 'subject'], List())
      .findIndex((subject) => subject.get('_id') === sId);

    let updateValue = configureDepartmentSubject
      .setIn([dIndex, 'subject', sIndex, 'isConfigured'], status)
      .setIn([dIndex, 'touched'], true)
      .setIn([dIndex, 'isConfigured'], true)
      .setIn([dIndex, 'subject', sIndex, 'touched'], true);

    if (!status) {
      updateValue = configureDepartmentSubject
        .setIn([dIndex, 'subject', sIndex, 'isConfigured'], status)
        .setIn([dIndex, 'touched'], true)
        .setIn([dIndex, 'isConfigured'], true)
        .setIn([dIndex, 'subject', sIndex, 'touched'], true)
        .setIn(
          [dIndex, 'subject', sIndex, 'courses'],
          configureDepartmentSubject
            .getIn([dIndex, 'subject', sIndex, 'courses'], List())
            .map((row) => {
              return row.set('isConfigured', false).set('touched', true);
            })
        );
    }
    setConfigureDepartmentSubject(updateValue);
  };

  const setConfigureCourseData = (programId, levelId, courseId, term, rotation, status) => {
    const programIndex = configuration.findIndex(
      (item) => item.getIn(['_program_id', 0]) === programId
    );
    const levelIndex = configuration
      .getIn([programIndex, 'level'], List())
      .findIndex((level) => level.get('_id') === levelId);
    const courseIndex = configuration
      .getIn([programIndex, 'level', levelIndex, 'course'], List())
      .findIndex(
        (course) =>
          course.get('_course_id') === courseId &&
          course.get('term') === term &&
          (rotation !== 0 ? course.get('rotation_count', 0) === rotation : true)
      );
    const updateValue = configuration
      .setIn([programIndex, 'level', levelIndex, 'course', courseIndex, 'isConfigured'], status)
      .setIn([programIndex, 'level', levelIndex, 'course', courseIndex, 'touched'], true);
    setConfiguration(updateValue);
  };

  const setConfigureDepartmentCourseData = (dId, sId, courseId, term, rotation, status) => {
    const dIndex = configureDepartmentSubject.findIndex(
      (department) => department.get('_id') === dId
    );
    const sIndex = configureDepartmentSubject
      .getIn([dIndex, 'subject'], List())
      .findIndex((subject) => subject.get('_id') === sId);
    const courseIndex = configureDepartmentSubject
      .getIn([dIndex, 'subject', sIndex, 'courses'], List())
      .findIndex(
        (course) =>
          course.get('_id') === courseId &&
          course.get('term') === term &&
          (rotation !== 0 ? course.get('rotation_count', 0) === rotation : true)
      );
    let updateValue = configureDepartmentSubject
      .setIn([dIndex, 'subject', sIndex, 'courses', courseIndex, 'isConfigured'], status)
      .setIn([dIndex, 'subject', sIndex, 'courses', courseIndex, 'touched'], true);
    setConfigureDepartmentSubject(updateValue);
  };

  const getProgramsLists = () => {
    return (
      configuration.size > 0 &&
      configuration.map((program, index) => {
        return (
          <div className="pt-3" key={index}>
            <div
              className="d-flex justify-content-between pl-2 remove_hover"
              onClick={() =>
                setActiveProgram(
                  activeProgram
                    .set('name', program.get('programName'))
                    .set('id', program.getIn(['_program_id', 0]))
                    .set('type', 'Academic Year / Level')
                )
              }
            >
              <p className="mb-2">
                {' '}
                {activeProgram.get('id', '') === program.getIn(['_program_id', 0]) &&
                  tabs === 'Configure' && (
                    <i className={`fa fa-minus-square text-skyblue`} aria-hidden="true"></i>
                  )}{' '}
                {program.get('programName')}
              </p>
              <p className="mb-3 remove_hover">
                <i
                  className={`fa fa-caret-${
                    activeProgram.get('id', '') === program.getIn(['_program_id', 0])
                      ? 'down'
                      : 'up'
                  } text-skyblue`}
                  aria-hidden="true"
                ></i>
              </p>
            </div>
            {activeProgram.get('id', '') === program.getIn(['_program_id', 0]) && (
              <React.Fragment>
                {typesList.map((val) => {
                  return (
                    <div
                      key={val}
                      className={`${
                        activeProgram.get('type') === val ? 'bg-lightgreen' : 'bg-gray'
                      } pl-2 pt-3 pb-3`}
                      onClick={() => setActiveProgram(activeProgram.set('type', val))}
                    >
                      <p className={`mb-0 ${activeProgram.get('type') === val && 'text-skyblue'}`}>
                        {' '}
                        <Form.Check
                          type="checkbox"
                          className="mr-3"
                          label={
                            val === 'Academic Year / Level'
                              ? levelRename('Academic Year / Level', activeProgram.get('id'))
                              : val
                          }
                          // label={
                          //   isIndVer()
                          //     ? val === 'Academic Year / Level'
                          //       ? levelRename('Academic Year / Level', activeProgram.get('id'))
                          //       : val
                          //     : t(
                          //         val === 'Academic Year / Level'
                          //           ? 'dashboard_view.academic_year'
                          //           : 'dashboard_view.depart_subjcet'
                          //       )
                          // }
                          defaultChecked={activeProgram.get('type') === val}
                        />{' '}
                      </p>
                    </div>
                  );
                })}
              </React.Fragment>
            )}
          </div>
        );
      })
    );
  };

  const getDepartmentSubject = () => {
    return configureDepartmentSubject
      .filter((item) => (tabs === 'Monitoring' ? item.get('isConfigured') === true : item))
      .filter((item) => item.get('program_id') === activeProgram.get('id'))
      .sort((a, b) => (a.get('department_name') > b.get('department_name') ? 1 : -1))
      .map((department, index) => {
        return (
          <div className="pt-3" key={index}>
            <div
              className="d-flex justify-content-between pl-2 remove_hover"
              onClick={() =>
                setActiveDepartment(
                  activeDepartment
                    .set('name', department.get('department_name'))
                    .set('id', department.get('_id'))
                )
              }
            >
              <p className="mb-2">
                {' '}
                {activeDepartment.get('id', '') === department.get('_id') &&
                  tabs === 'Configure' && (
                    <i className={`fa fa-minus-square text-skyblue`} aria-hidden="true"></i>
                  )}{' '}
                {department.get('department_name')}
              </p>
              <p className="mb-3 remove_hover">
                <i
                  className={`fa fa-caret-${
                    activeDepartment.get('id', '') === department.get('_id') ? 'down' : 'up'
                  } text-skyblue`}
                  aria-hidden="true"
                ></i>
              </p>
            </div>
            {activeDepartment.get('id', '') === department.get('_id') && (
              <React.Fragment>
                {department
                  .get('subject', List())
                  .filter((item) =>
                    tabs === 'Monitoring' ? item.get('isConfigured') === true : item
                  )
                  .sort((a, b) => (a.get('subject_name') > b.get('subject_name') ? 1 : -1))
                  .map((subject, subjectIndex) => {
                    return (
                      <div
                        key={subjectIndex}
                        className={`pl-2 pt-3 pb-3`}
                        onClick={() =>
                          tabs === 'Configure'
                            ? setConfigureDepartment(
                                department.get('_id'),
                                subject.get('_id'),
                                !subject.get('isConfigured', false)
                              )
                            : {}
                        }
                      >
                        <p className={`mb-0 ml-2`}>
                          {' '}
                          {tabs === 'Configure' ? (
                            <Form.Check
                              type="checkbox"
                              className="mr-3"
                              label={subject.get('subject_name')}
                              defaultChecked={subject.get('isConfigured', false)}
                            />
                          ) : (
                            subject.get('subject_name')
                          )}{' '}
                        </p>
                      </div>
                    );
                  })}
              </React.Fragment>
            )}
          </div>
        );
      });
  };

  const checkHasCourse = (type) => {
    let value = false;
    configureDepartmentSubject.size > 0 &&
      configureDepartmentSubject
        .filter((item) => item.get('program_id') === activeProgram.get('id'))
        .map((department) => {
          department
            .get('subject', List())
            .filter((item) => item.get('isConfigured') === true)
            .map((subject) => {
              subject
                .get('courses', List())
                .filter((item) =>
                  tabs === 'Monitoring' ? item.get('isConfigured') === true : item
                )
                .filter((item) =>
                  type === 'Admin Course'
                    ? item.get('AdminCourse') === true
                    : item.get('participatingCourse') === true
                )
                .map((course) => {
                  value = true;
                  return course;
                });
              return subject;
            });
          return department;
        });
    return value;
  };

  const getCoursesByType = (type) => {
    return checkHasCourse(type) ? (
      <React.Fragment>
        <p className="mb-2 ml-3 remove_hover" onClick={() => setActiveCourse(type)}>
          {' '}
          {activeCourse === type && tabs === 'Configure' && (
            <i className={`fa fa-minus-square text-skyblue`} aria-hidden="true"></i>
          )}{' '}
          <Trans
            i18nKey={
              type === 'Admin Course'
                ? 'dashboard_view.admin_course'
                : 'dashboard_view.participatory_course'
            }
          />
          <i
            style={{ float: lang === 'ar' ? 'left' : 'right' }}
            className={`fa fa-caret-${activeCourse === type ? 'down' : 'up'} text-skyblue`}
            aria-hidden="true"
          ></i>
        </p>

        {activeCourse === type &&
          configureDepartmentSubject.size > 0 &&
          configureDepartmentSubject.map((department, index) => {
            //let courseRotation = [];
            return (
              <React.Fragment key={index}>
                {department
                  .get('subject', List())
                  .filter((item) => item.get('isConfigured') === true)
                  .map((subject, sIndex) => {
                    return (
                      <React.Fragment key={sIndex}>
                        {subject
                          .get('courses', List())
                          .filter((item) =>
                            type === 'Admin Course'
                              ? item.get('AdminCourse') === true
                              : item.get('participatingCourse') === true
                          ).size > 0 && (
                          <>
                            <div className="mb-2" style={{ color: '#b7b1b1' }}>
                              {department.get('department_name', '')} -{' '}
                              {subject.get('subject_name', '')}
                              {}
                            </div>
                            <>
                              {subject
                                .get('courses', List())
                                .filter((item) =>
                                  tabs === 'Monitoring' ? item.get('isConfigured') === true : item
                                )
                                .filter((item) =>
                                  type === 'Admin Course'
                                    ? item.get('AdminCourse') === true
                                    : item.get('participatingCourse') === true
                                )
                                .sort((a, b) =>
                                  a.get('courses_number') > b.get('courses_number') ? 1 : -1
                                )
                                .map((course, cIndex) => {
                                  // if (
                                  //   course.get('rotation', 'no') === 'yes' &&
                                  //   !courseRotation.includes(course.get('_id'))
                                  // ) {
                                  //   courseRotation.push(course.get('_id'));
                                  // }
                                  // {(course.get('rotation', 'no') === 'no' ||
                                  // (courseRotation.includes(course.get('_id')) &&
                                  //   course.get('rotation_count', 0) === 1)) && (
                                  //console.log('course1', course);
                                  return (
                                    <p
                                      className="f-16 mb-4 ml-3 text-lightblack"
                                      key={cIndex}
                                      onClick={() =>
                                        tabs === 'Configure'
                                          ? setConfigureDepartmentCourseData(
                                              department.get('_id'),
                                              subject.get('_id'),
                                              course.get('_id'),
                                              course.get('term'),
                                              course.get('rotation_count', 0),
                                              !course.get('isConfigured', false)
                                            )
                                          : {}
                                      }
                                    >
                                      {' '}
                                      {tabs === 'Configure' ? (
                                        <Form.Check
                                          type="checkbox"
                                          className="ml-3 mr-3"
                                          label={`${course.get('courses_number')} - ${course.get(
                                            'courses_name'
                                          )}${getVersionName(course)} - ${course.get('term')} ${
                                            course.get('rotation', '') === 'yes'
                                              ? ' - R' + course.get('rotation_count', '')
                                              : ''
                                          }`}
                                          defaultChecked={course.get('isConfigured', false)}
                                        />
                                      ) : (
                                        course.get('courses_number') +
                                        ' - ' +
                                        course.get('courses_name') +
                                        ' - ' +
                                        getVersionName(course) +
                                        course.get('term') +
                                        (course.get('rotation', '') === 'yes'
                                          ? ' - R' + course.get('rotation_count', '')
                                          : '')
                                      )}{' '}
                                    </p>
                                  );
                                })}
                            </>{' '}
                          </>
                        )}
                      </React.Fragment>
                    );
                  })}
              </React.Fragment>
            );
          })}
      </React.Fragment>
    ) : (
      <></>
    );
  };

  const getDepartmentSubjectCourse = () => {
    return (
      <React.Fragment>
        <>
          {getCoursesByType('Admin Course')}
          {getCoursesByType('Participatory Course')}
        </>
      </React.Fragment>
    );
  };

  const submitData = () => {
    let yearLevelArray = [];
    let deptSubjectArray = [];
    let updatable = [];
    let flag = '';
    if (activeProgram.get('type', '') === 'Academic Year / Level') {
      configuration &&
        configuration.size > 0 &&
        configuration
          .filter((item) => item.getIn(['_program_id', 0], '') === activeProgram.get('id'))
          .map((data) => {
            data.get('level', List()).size > 0 &&
              data
                .get('level', List())
                // .filter(
                //   (item) =>
                //     item.get('touched', false) === true || item.get('isConfigured', false) === true
                // )
                .map((level) => {
                  level.get('course', List()).size > 0 &&
                    level
                      .get('course', List())
                      .filter(
                        (item) =>
                          item.get('touched', false) === true ||
                          item.get('isConfigured', false) === true
                      )
                      .map((course) => {
                        yearLevelArray.push({
                          program_id: data.getIn(['_program_id', 0]),
                          level_id: level.get('_id'),
                          course_id: course.get('_course_id'),
                          term: course.get('term'),
                          ...(course.get('rotation_count', 0) !== 0 && {
                            rotation_count: course.get('rotation_count', 0),
                          }),
                          checked: course.get('isConfigured', false),
                        });
                        return course;
                      });
                  return level;
                });
            return data;
          });
      updatable = yearLevelArray;
      flag = 'PCalenders';
    } else if (activeProgram.get('type', '') === 'Department / Subject') {
      configureDepartmentSubject &&
        configureDepartmentSubject.size > 0 &&
        configureDepartmentSubject
          .filter(
            (item) =>
              item.get('touched', false) === true || item.get('isConfigured', false) === true
          )
          .map((data) => {
            data.get('subject', List()).size > 0 &&
              data
                .get('subject', List())
                .filter(
                  (item) =>
                    item.get('touched', false) === true || item.get('isConfigured', false) === true
                )
                .map((subject) => {
                  subject.get('courses', List()).size > 0 &&
                    subject
                      .get('courses', List())
                      .filter(
                        (item) =>
                          item.get('touched', false) === true ||
                          item.get('isConfigured', false) === true
                      )
                      .map((course) => {
                        deptSubjectArray.push({
                          program_id: data.get('program_id'),
                          department_id: data.get('_id'),
                          subject_id: subject.get('_id'),
                          course_id: course.get('_id'),
                          term: course.get('term'),
                          ...(course.get('rotation_count', 0) !== 0 && {
                            rotation_count: course.get('rotation_count', 0),
                          }),
                          checked: course.get('isConfigured', false),
                        });
                        return course;
                      });
                  return subject;
                });
            return data;
          });
      updatable = deptSubjectArray;
      flag = 'Depts&Sub';
    }
    if (
      (yearLevelArray && yearLevelArray.length > 0) ||
      (deptSubjectArray && deptSubjectArray.length > 0)
    ) {
      const data = {
        updatable: updatable,
        flag: flag,
      };
      updateConfigureSettings(data, () => {
        showModal();
      });
    } else {
      setData(Map({ message: 'Course is required' }));
    }
  };
  return (
    <>
      <Modal show={show} onHide={showModal} dialogClassName="model-900" centered>
        <Loader
          isLoading={
            settingsLoading.get('GET_CONFIGURE_SETTINGS') ||
            settingsLoading.get('UPDATE_CONFIGURATION_SETTINGS')
          }
        />
        <Modal.Body>
          <div className="model-main">
            <p className="f-16 mb-3 bold">
              <Trans i18nKey={'cDetails.settings'}></Trans>
            </p>

            <div className="row digi-pl-16 digi-pr-16 mb-3">
              <div className="">
                <div
                  className={`user_list1 remove_hover mr-3 ${tabs === 'Configure' && 'active'}`}
                  onClick={() => setTabs('Configure')}
                >
                  {' '}
                  <Trans i18nKey={'dashboard_view.configure'}></Trans>
                </div>
              </div>
              <div className="">
                <div
                  className={`user_list1 remove_hover mr-3 ${tabs === 'Monitoring' && 'active'}`}
                  onClick={() => setTabs('Monitoring')}
                >
                  {' '}
                  <Trans i18nKey={'dashboard_view.monitoring'}></Trans>
                </div>
              </div>
            </div>

            <div className="border border-radious-8 digi-pl-16 digi-pr-16">
              <div className="row">
                <div className="col-md-4 border-right">
                  <p className="f-18 mb-3 mt-3 bold pl-2">
                    <Trans i18nKey={'program_name'}></Trans>
                  </p>
                  <div className="roles_height" style={{ minHeight: '500px', maxHeight: '500px' }}>
                    {getProgramsLists()}
                  </div>
                </div>

                <div className="col-md-4 border-right">
                  <p className="f-18 mb-3 mt-3 bold">
                    {' '}
                    {getEnvLabelChanged() ? (
                      <>
                        {activeProgram.get('type', '') === 'Academic Year / Level' ||
                        activeProgram.get('type', '') === ''
                          ? levelRename('Academic Year / Level', activeProgram.get('id'))
                          : activeProgram.get('type', '')}
                      </>
                    ) : (
                      <Trans
                        i18nKey={
                          activeProgram.get('type', '') === 'Academic Year / Level'
                            ? 'dashboard_view.academic_year'
                            : 'dashboard_view.depart_subjcet'
                        }
                        values={{ Level: 'Level' }}
                      />
                    )}
                    {/* {tabs === 'Configure'
                      ? activeProgram.get('type', '').toUpperCase()
                      : // <Form.Check
                        //   type="checkbox"
                        //   className="mr-3"
                        //   label={activeProgram.get('type', '').toUpperCase()}
                        // />
                        activeProgram.get('type', '').toUpperCase()}{' '} */}
                  </p>

                  <div
                    className="pt-3 roles_height"
                    style={{ minHeight: '500px', maxHeight: '500px' }}
                  >
                    {activeProgram.get('type', '') === 'Academic Year / Level'
                      ? getYearAndLevel()
                      : getDepartmentSubject()}
                  </div>
                </div>

                <div className="col-md-4">
                  <p className="f-18 mb-3 mt-3 bold">
                    {' '}
                    {tabs === 'Configure' ? (
                      <Trans i18nKey={'dashboard_view.course_name'}></Trans>
                    ) : (
                      // <Form.Check type="checkbox" className="ml-3 mr-3" label="COURSE NAME / ID" />
                      <Trans i18nKey={'dashboard_view.course_name'}></Trans>
                    )}{' '}
                  </p>

                  <div
                    className="pt-3 roles_height"
                    style={{ minHeight: '500px', maxHeight: '500px' }}
                  >
                    {activeProgram.get('type', '') === 'Academic Year / Level'
                      ? getYearAndLevelCourse()
                      : getDepartmentSubjectCourse()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>

        <Modal.Footer>
          <div className="container">
            <div className="row">
              <div className="col-md-12">
                <div className="float-right">
                  <b className="pr-3">
                    <Button variant="outline-secondary" onClick={showModal}>
                      <Trans i18nKey={'cancel'}></Trans>
                    </Button>
                  </b>

                  <b className="pr-2">
                    <Button variant="primary" onClick={submitData}>
                      <Trans i18nKey={'apply'}></Trans>
                    </Button>
                  </b>
                </div>
              </div>
            </div>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default React.memo(MonitoringSettingsModal);
