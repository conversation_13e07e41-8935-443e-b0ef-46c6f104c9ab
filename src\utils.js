import { getDay, endOfDay, format, startOfDay, set, differenceInDays, isValid } from 'date-fns';
import jsPDF from 'jspdf';
import { List, Map, fromJS } from 'immutable';
import isEqual from 'lodash/fp/isEqual';
import { createTheme } from '@mui/material/styles';
import cookies from 'js-cookie';
import { t } from 'i18next';
import i18n from './i18n';
import { getAppEnv, getProgramLabelsType } from 'Modules/Shared/Permissions';
import Config from './config.js';
import { useEffect, useState, useRef } from 'react';
import { formattedFullName, getFormattedGroupName } from 'Modules/ReportsAndAnalytics/utils';
import moment from 'moment';
import LocalStorageService from 'LocalStorageService';
import cloneDeep from 'lodash/cloneDeep';

export const updateObject = (oldObject, updatedProperties) => {
  return {
    ...oldObject,
    ...updatedProperties,
  };
};

export function exportPdf(
  headers,
  tableData,
  pdfName,
  currentSemester,
  title,
  academicYear,
  activeYear,
  activeTerm,
  activeLevel,
  courseName,
  groupName,
  deliveryType,
  activeGender,
  activeGroup,
  programId
) {
  var doc = new jsPDF();
  jsPDF.autoTableSetDefaults({
    headStyles: { fillColor: '#e0e0e0', textColor: 0 },
    bodyStyles: { fillColor: '#ffffff', textColor: 0 },
  });
  var finalY = doc.lastAutoTable.finalY || 10;

  doc.setFont('helvetica', 'bold');
  doc.text(title, 14, finalY + 20);

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(12);

  doc.text(getEnvCollegeName().toLowerCase(), 200, finalY + 20, 'right');
  if (academicYear) {
    doc.text('Academic year ' + academicYear, 200, finalY + 27, 'right');
  }

  if (currentSemester) {
    doc.text(`Semester : ${currentSemester.replace('semester', '')}`, 14, finalY + 34);
    doc.line(14, finalY + 38, 200, finalY + 38);
  }

  if (courseName) {
    doc.text(courseName, 14, finalY + 28);
  }

  if (deliveryType) {
    doc.text(deliveryType, 14, finalY + 35);
  }

  if (groupName) {
    doc.text(`${groupName}`, 14, finalY + 42);
    if (title !== 'Course Group Details') {
      doc.line(14, finalY + 60, 200, finalY + 60);
    } else {
      doc.line(14, finalY + 50, 200, finalY + 50);
    }
  }

  if (activeYear && activeLevel) {
    doc.text(
      'Year: ' +
        activeYear.replace('year', 'Year ') +
        ` ${indVerRename('Level', programId)}: ` +
        levelRename(activeLevel, programId),
      14,
      finalY + 34
    );
  }
  if (activeTerm) {
    doc.text(`${indVerRename('Term', programId)}: ` + jsUcfirst(activeTerm), 14, finalY + 40);
  }
  if (activeGender) {
    if (title !== 'Course Group Details') {
      doc.text('Gender: ' + jsUcfirst(activeGender), 14, finalY + 46);
    } else {
      doc.text(jsUcfirst(activeGender), 200, finalY + 46, 'right');
    }
  }
  if (activeGroup) {
    let groupName = title === 'Foundation Program' ? 'Foundation Group' : 'Rotation Group';
    doc.text(`${groupName} ${activeGroup.replace('group', '')}`, 200, finalY + 46, 'right');
  }
  if (activeYear && activeTerm && activeLevel) {
    doc.line(14, finalY + 50, 200, finalY + 50);
  }

  if (doc.lastAutoTable.finalY === undefined) {
    doc.autoTable({
      startY: finalY + 58,
      head: [headers],
      body: tableData.length > 0 ? tableData : [['No Data Found...']],
      tableLineColor: [189, 195, 199],
      tableLineWidth: 0.5,
      theme: 'grid',
    });
  } else {
    doc.autoTable({
      startY: finalY + 18,
      head: [headers],
      body: tableData.length > 0 ? tableData : [['No Data Found...']],
      tableLineColor: [189, 195, 199],
      tableLineWidth: 0.5,
      theme: 'grid',
    });
  }
  addPDFPageNo(doc);
  doc.save(`${pdfName}`);
}

export const EMAIL_REGEX = /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/;
export const URL_REGEX = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.)+(?:[a-z\u00a1-\uffff]{2,}\.?))(?::\d{2,5})?(?:[/?#]\S*)?$/i;

export function checkValidity(value, rules, key) {
  let feedback = '';
  let isValid = true;

  if (!rules) {
    return '';
  }

  if (rules.required) {
    // isValid = value.trim() !== "";
    isValid = value;
    if (!isValid) {
      return rules.errorMessage.requiredMsg;
    }
  }

  if (rules.pattern) {
    const pattern = new RegExp(rules.pattern);
    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg;
    }
  }

  if (rules.minLength && rules.maxLength) {
    isValid = value.length >= rules.minLength && value.length <= rules.maxLength && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg;
    }
  } else {
    if (rules.minLength) {
      isValid = value.length >= rules.minLength && isValid;
      if (!isValid) {
        return rules.errorMessage.extraMsg;
      }
    }

    if (rules.maxLength) {
      isValid = value.length <= rules.maxLength && isValid;
      if (!isValid) {
        return rules.errorMessage.extraMsg;
      }
    }
  }

  if (rules.isEmail) {
    isValid = EMAIL_REGEX.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg;
    }
  }

  if (rules.isNumeric) {
    const pattern = /^\d+$/;
    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }

  if (rules.isString) {
    const pattern = /^\d+$/;
    isValid = !pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }

  if (rules.isAlphaNumeric) {
    const pattern = /[^0-9a-bA-B\s]/gi;
    isValid = !pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }

  if (rules.isSymbol) {
    const pattern = /^[a-zA-Z_ /-]*$/;
    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }

  if (rules.isSymbolNumber) {
    const pattern = /^[a-zA-Z0-9_ /-]*$/;

    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }
  if (rules.isSymbolDot) {
    const pattern = /^[a-zA-Z0-9 /.]*$/;

    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }
  if (rules.isSymbolDotWithoutSpace) {
    const pattern = /^[a-zA-Z/.]*$/;

    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }

  if (rules.isSymbolSpace) {
    const pattern = /^[a-zA-Z ]*$/;

    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }

  if (rules.alphanum) {
    const pattern = /^[a-zA-Z0-9]+$/;
    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }
  if (rules.alpha) {
    const pattern = /^[a-zA-Z]+$/;
    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg2;
    }
  }

  if (rules.minNumber) {
    isValid = value >= rules.minNumber && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg3;
    }
  }

  if (rules.maxNumber) {
    isValid = value <= rules.maxNumber && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg3;
    }
  }

  if (rules.isSpace) {
    const pattern = /^\S$|^\S[ \S]*\S$/;
    isValid = pattern.test(value) && isValid;
    if (!isValid) {
      return rules.errorMessage.extraMsg4;
    }
  }

  // else if (setYearLevel[i].courses < 1 || setYearLevel[i].courses > 10) {
  //     setYearLevel[i].coursesError = "Study level between 1 to 10";
  //   }

  return feedback;
}

export function convertHijiriYear(year) {
  var GregorianYear = new Date(`${year}`).getFullYear();
  var HijriYear = Math.round((GregorianYear - 622) * (33 / 32));
  return HijriYear;
}

export function ucFirst(str) {
  str = str.split(' ');
  for (var i = 0, x = str.length; i < x; i++) {
    str[i] = str[i][0].toUpperCase() + str[i].substr(1);
  }
  return str.join(' ');
}

export function jsUcfirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
}

export function writeHijri(date, lang) {
  var dt = new Date(date);
  dt.setDate(dt.getDate() - 1);
  var dates = dt || new Date();
  lang = lang || 'en';
  var options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  return dates.toLocaleString(lang + '-u-ca-islamic', options);
}

export function addPDFPageNo(doc) {
  const pageCount = doc.internal.getNumberOfPages();
  doc.setFont('helvetica', 'italic');
  doc.setFontSize(8);
  for (var i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.text(
      'Page ' + String(i) + ' of ' + String(pageCount),
      doc.internal.pageSize.width / 2,
      287,
      {
        align: 'right',
      }
    );
  }
}

export function timeFormat(time) {
  let newTimePortions = [];
  let timePortions = time.split(':');
  timePortions.forEach(function (portion, index) {
    newTimePortions[index] = ('0' + portion).slice(-2);
  });
  return newTimePortions.join(':');
}

export function maxDateSet(year = 15) {
  var n = new Date(new Date().setFullYear(new Date().getFullYear() - year));
  return n;
}

export function jsUcfirstAll(sentence) {
  if (typeof sentence !== 'string') return sentence;
  return sentence
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export function getDayName(date) {
  const day = getDay(date);
  let dayName = '';
  if (day === 0) {
    dayName = 'Sunday';
  } else if (day === 1) {
    dayName = 'Monday';
  } else if (day === 2) {
    dayName = 'Tuesday';
  } else if (day === 3) {
    dayName = 'Wednesday';
  } else if (day === 4) {
    dayName = 'Thursday';
  } else if (day === 5) {
    dayName = 'Friday';
  } else if (day === 6) {
    dayName = 'Saturday';
  }
  return dayName;
}

export function getHijriDateFormat(date) {
  return new Intl.DateTimeFormat('ar-TN-u-ca-islamic', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  }).format(new Date(date));
}

export function getHijriDayFormat(date) {
  return new Intl.DateTimeFormat('ar-TN-u-ca-islamic', { weekday: 'long' }).format(new Date(date));
}

export function getTimestamp(date) {
  return Math.round(new Date(date).getTime() / 1000);
}

export function handelRightClick(event) {
  event.preventDefault();
}

export function getErrorMessage(data, fieldName = 'name') {
  let errorMessage = data;
  if (typeof errorMessage === 'object') {
    const field = errorMessage.field?.[fieldName];
    errorMessage = errorMessage.message;
    if (field !== undefined) {
      errorMessage = errorMessage + ' - ' + field;
    }
  }
  return errorMessage;
}

export function getURLParams(name, format = false) {
  let search = window.location.search;
  let params = new URLSearchParams(search);
  if (format) {
    return params.get(name) !== null ? dString(params.get(name)) : '';
  } else {
    return params.get(name) !== null ? params.get(name) : '';
  }
}

export function removeURLParams(loc, value) {
  const searchToArray = loc.search.split('&');
  // const preparedValue = value + '=';
  // const filteredArray = searchToArray.filter((item) => item.search(preparedValue) === -1);
  // return filteredArray.join('&');
  var filteredArray = searchToArray;
  for (var i = 0; i < value.length; i++) {
    let preparedValue = value[i] + '=';
    filteredArray = filteredArray.filter((item) => item.search(preparedValue) === -1);
  }
  return filteredArray.join('&');
}

export function getWeekList(start = 1, count = 52) {
  var array = [];
  for (var i = start; i <= count; i++) {
    array.push({ name: i, value: i });
  }
  return array;
}

export function fileSizeTypeCheck(
  props,
  file,
  size = 1,
  type = ['image/jpeg', 'image/jpg', 'image/png']
) {
  if (!type.includes(file.type.toLowerCase())) {
    props.resetMessage('The file format can be JPG, JPEG, PNG');
    return false;
  } else {
    const fileSize = Math.ceil(parseFloat(file.size / 1024));
    if (fileSize > 1000 * size) {
      props.resetMessage(`The file size should be less than ${size} MB.`);
      return false;
    } else {
      return true;
    }
  }
}

export function capitalize(s) {
  if (typeof s !== 'string') return s;
  return `${s.charAt(0).toUpperCase()}${s.slice(1)}`;
}

export function getRandomHexColor() {
  return `#${Math.floor(Math.random() * 16777215).toString(16)}`;
}

export const MUI_THEME = createTheme({
  palette: {
    primary: {
      main: '#3E95EF',
    },
  },
});

export const MUI_GRAPH_RADIO_THEME = createTheme({
  ...MUI_THEME,
  overrides: {
    MuiRadio: {
      root: {
        padding: '0px !important',
        marginRight: '5px',
      },
    },
  },
});

export const MUI_CHECKBOX_THEME = createTheme({
  ...MUI_THEME,
  overrides: {
    MuiCheckbox: {
      indeterminate: {
        color: '#3E95EF',
      },
    },
  },
});

export function formatFullName(data = '') {
  if (data !== '') {
    const firstName = data.first !== undefined ? data.first : '';
    const middleName = data.middle !== undefined ? data.middle : '';
    const lastName = data.last !== undefined ? data.last : '';
    return jsUcfirstAll(firstName + ' ' + middleName + ' ' + lastName);
  }
  return '';
}

export function isValidEmail(value) {
  if (typeof value !== 'string') return false;
  return EMAIL_REGEX.test(value);
}

export function isValidUrl(value) {
  if (typeof value !== 'string') return false;
  return URL_REGEX.test(value);
}

export function formatDayName(day) {
  return day.toUpperCase().slice(0, 3);
}

export function formatDays(days) {
  if (days.size > 0) {
    return days
      .toJS()
      .map((item) => formatDayName(item))
      .join(', ');
  }
  return '';
}

export function formatTwoString(str) {
  if (str !== undefined && str !== '') {
    const length = str.toString().length;
    if (length === 1) {
      return '0' + str;
    }
  }
  return str;
}

export function setDate(hour, minute = 0) {
  var stDate = new Date();
  stDate.setHours(hour);
  stDate.setMinutes(minute);
  return stDate;
}

export function getWeeks(startDate, endDate) {
  startDate = new Date(startDate);
  endDate = new Date(endDate);
  if (!isValid(startDate) || !isValid(endDate)) return 0;
  endDate = endOfDay(endDate);
  const days = differenceInDays(endDate, startDate);
  let weeks = days / 7;
  if (!Number.isInteger(weeks)) {
    weeks = Math.trunc(weeks) + 1;
  }
  return weeks;
}

export function getFormattedCourseDuration(course) {
  const startDate = new Date(course.get('start_date'));
  let endDate = new Date(course.get('end_date'));
  if (!isValid(startDate) || !isValid(endDate)) return '';
  endDate = endOfDay(endDate);
  const weeks = getWeeks(course.get('start_date'), course.get('end_date'));
  return `${format(startDate, 'MMM dd')} - ${format(endDate, 'MMM dd')} (${weeks} week${
    weeks > 1 ? 's' : ''
  })`;
}

export function getFormattedHijiriYear(calendarName = '', startDate = '', endDate = '') {
  if (calendarName !== '' && startDate !== '') {
    let year1Title = moment(startDate).format('YYYY');
    let year2Title = moment(endDate).format('YYYY');
    let sliceEndYear = convertHijiriYear(year2Title);
    sliceEndYear = sliceEndYear.toString();
    sliceEndYear = sliceEndYear.substr(2, 4);
    return !showArabicMailShow()
      ? calendarName + ' G (' + convertHijiriYear(year1Title) + ' - ' + sliceEndYear + ' H)'
      : calendarName;
  }
  return calendarName;
}

export function isArabic(text) {
  var arabic = /[\u0600-\u06FF]/;
  return arabic.test(text);
}

export function sortAlphaNumeric(a, b, sortDirection = 'asc') {
  const v1 = String(a);
  const v2 = String(b);
  const collator = new Intl.Collator('en', { numeric: true, sensitivity: 'accent' });
  return sortDirection === 'asc' ? collator.compare(v1, v2) : collator.compare(v2, v1);
}

export function sortImmutableAlphaNumeric(a, b, key, sortDirection = 'asc') {
  return sortAlphaNumeric(a.get(key, ''), b.get(key, ''), sortDirection);
}

export function trimFractionDigits(num, fractionDigits = 2) {
  if (!Number.isInteger(num)) {
    num = Number(num).toFixed(fractionDigits);
  }
  return num;
}

export function chunkArray(array, size) {
  let result = [];
  for (let i = 0; i < array.length; i += size) {
    let chunk = array.slice(i, i + size);
    result.push(chunk);
  }
  return result;
}

export function getHour(date) {
  if (date.isEmpty()) return 0;
  const meridiem = date.get('format');
  const hour = date.get('hour');
  return meridiem === 'AM' ? (hour === 12 ? 0 : hour) : hour === 12 ? 12 : hour + 12;
}

export function getStartEndDate(date) {
  if (date.isEmpty()) return null;
  return set(startOfDay(new Date()), { hours: getHour(date), minutes: date.get('minute') });
}

export function intersection(arr1, arr2) {
  const res = [];
  for (let i = 0; i < arr1.length; i++) {
    if (!arr2.includes(arr1[i])) {
      continue;
    }
    res.push(arr1[i]);
  }
  return res;
}

export function intersectMany(arr) {
  let res = arr[0].slice();
  for (let i = 1; i < arr.length; i++) {
    res = intersection(res, arr[i]);
  }
  return res;
}

export function eString(name) {
  //utf8_to_b64
  if (name !== '') {
    return window.btoa(unescape(encodeURIComponent(name)));
  }
  return '';
}

export function dString(name) {
  //b64_to_utf8
  if (name !== '') {
    return decodeURIComponent(escape(window.atob(name)));
  }
  return '';
}

export function getLang() {
  return cookies.get('i18next') || Config.DEFAULT_LANG;
}

export function setDSCookie(name, value) {
  return cookies.set(name, value);
}

export function getDSCookie(name) {
  return cookies.get(name) !== undefined ? cookies.get(name) : '';
}

export function removeDSCookie(name) {
  return cookies.remove(name);
}

export function getActiveVersion() {
  const version =
    getEnvAppEnvironment() === 'production' ? getEnvActiveVersion() : getDSCookie('version');
  return ['/', 'v1'].includes(version) ? '' : version;
}

export function isEmptyObject(obj) {
  return !!obj && Object.keys(obj).length === 0 && obj.constructor === Object;
}

export function getYearFromName(name) {
  return name.replace('year', '');
}

export function removeLastOccurrence(str = '', word = '', newWord = '') {
  var n = str.lastIndexOf(word);
  return (str = str.slice(0, n) + str.slice(n).replace(word, newWord));
}

export const getTranslatedDuration = (text) => {
  const calendarObj = [
    { name: 'Jan', transKey: 'calender.Jan' },
    { name: 'Feb', transKey: 'calender.Feb' },
    { name: 'Mar', transKey: 'calender.Mar' },
    { name: 'Apr', transKey: 'calender.Apr' },
    { name: 'May', transKey: 'calender.May' },
    { name: 'Jun', transKey: 'calender.Jun' },
    { name: 'Jul', transKey: 'calender.Jul' },
    { name: 'Aug', transKey: 'calender.Aug' },
    { name: 'Sep', transKey: 'calender.Sep' },
    { name: 'Oct', transKey: 'calender.Oct' },
    { name: 'Nov', transKey: 'calender.Nov' },
    { name: 'Dec', transKey: 'calender.Dec' },
    { name: 'weeks', transKey: 'week_other' },
    { name: 'week', transKey: 'week_one' },
    { name: 'am', transKey: 'dashboard_view.am' },
    { name: 'pm', transKey: 'dashboard_view.pm' },
    { name: 'AM', transKey: 'date.am' },
    { name: 'PM', transKey: 'date.pm' },
    { name: 'st', transKey: 'dashboard_view.st' },
    { name: 'nd', transKey: 'dashboard_view.nd' },
    { name: 'rd', transKey: 'dashboard_view.rd' },
    { name: 'th', transKey: 'dashboard_view.th' },
  ];
  calendarObj.forEach(({ name, transKey }) => {
    if (text.includes(name)) text = text.replaceAll(name, t(transKey));
  });
  return text;
};

export function isIndVer() {
  return isIndianVersion();
}

export function indVerRename(legend, pId = '', label = '') {
  if (!getEnvLabelChanged()) {
    return legend;
  }
  const programListWithType = getProgramLabelsType();
  if (programListWithType === undefined || programListWithType.isEmpty()) {
    return legend;
  }
  const type = label === '' ? (getEnvLabelChanged() ? 'Medical' : 'Engineering') : label;
  // console.log('vaaa3', programListWithType, legend, pId);
  const typeFilter =
    pId !== undefined && pId !== '' && pId !== null
      ? programListWithType
          ?.get('programDatas', List())
          ?.find((item) => item.get('_id', '') === pId)
          ?.get('college_program_type', 'Engineering')
      : type;
  const labelFilter = programListWithType
    .get('labels', List())
    .find((item) => item.get('college_program_type', '') === typeFilter);
  if (labelFilter !== undefined && !labelFilter.isEmpty()) {
    return labelFilter.get(legend.toLowerCase(), legend);
  }
  return legend;
}

export function getTPCLegend(pId = '') {
  return [
    indVerRename('Theory', pId),
    indVerRename('Practical', pId),
    indVerRename('Clinical', pId),
  ];
}

export function getTPCLegendOldValue() {
  return ['theory', 'practical', 'clinical'];
}

export const studentGroupUrl = () => `student_group${isGenderMerge() ? '_gender_merge' : ''}`;
export const studentGenderMode = () => `${isGenderMerge() ? '?mode=both' : ''}`;

export function levelRename(name, pId = '') {
  return name.replace(
    'Level',
    i18n.t('constant.level', {
      name: indVerRename('Level', pId),
    })
  );
}

export function getModes(pId = '') {
  const MODES = {
    ONSITE: 'onsite',
    REMOTE: 'remote',
  };
  const MODE_OPTIONS = Object.values(MODES).map((mode) => {
    return { name: indVerRename(capitalize(mode), pId), value: mode };
  });
  return MODE_OPTIONS;
}

export function stringToUC(s, uCase = true) {
  if (typeof s !== 'string') return s;
  return `${uCase ? s.toUpperCase() : s.toLowerCase()}`;
}

export function contentRename(name, label = '', to = '') {
  return name.replace(label, to);
}

export function isRotationProgram(name) {
  if (name !== null) {
    const programName = name.toLowerCase();
    return programName.indexOf('medic') > -1 || programName.indexOf('rotation') > -1;
  }
}

export function isIndGroup(groupName) {
  return isGenderMerge() && groupName.includes('SG');
}

export function isDemoVer() {
  return getEnvAppEnvironment() === 'demo';
}

export const getTranslatedDays = (day) => {
  const daysObj = [
    { name: 'sunday', transKey: 'global_configuration.days.sunday' },
    { name: 'monday', transKey: 'global_configuration.days.monday' },
    { name: 'tuesday', transKey: 'global_configuration.days.tuesday' },
    { name: 'wednesday', transKey: 'global_configuration.days.wednesday' },
    { name: 'thursday', transKey: 'global_configuration.days.thursday' },
    { name: 'friday', transKey: 'global_configuration.days.friday' },
    { name: 'saturday', transKey: 'global_configuration.days.saturday' },
  ];
  daysObj.forEach(({ name, transKey }) => {
    if (day.includes(name)) day = day.replaceAll(name, t(transKey));
  });
  return day;
};

export const getTranslatedShortDays = (day) => {
  const daysObj = [
    { name: 'sun', transKey: 'days.sun' },
    { name: 'mon', transKey: 'days.mon' },
    { name: 'tue', transKey: 'days.tue' },
    { name: 'wed', transKey: 'days.wed' },
    { name: 'thu', transKey: 'days.thu' },
    { name: 'fri', transKey: 'days.fri' },
    { name: 'sat', transKey: 'days.sat' },
  ];
  daysObj.forEach(({ name, transKey }) => {
    if (day.includes(name)) day = day.replaceAll(name, t(transKey));
  });
  return day;
};

export function isWeekday(date) {
  const day = date.getDay();
  //return isIndVer() ? day : day !== 5 && day !== 6;
  return !isIndVer() ? ![5, 6].includes(day) : ![-1].includes(day);
}

export function isWeekStartDay() {
  return false; //!isIndVer();
}

export function isWeekEndDay() {
  return false; //!isIndVer();
}

export function nl2br(str) {
  return str.replace(/(?:\r\n|\r|\n)/g, '<br>');
}

export function groupArray(array, type) {
  const yearLevels = array,
    result = yearLevels.reduce(function (r, a) {
      r[a[type]] = r[a[type]] || [];
      r[a[type]].push(a);
      return r;
    }, Object.create(null));
  return result;
}

export function allowedTimeInterval() {
  return 5;
}

export function GENDER() {
  return isGenderMerge()
    ? [{ name: 'Both', value: 'both' }]
    : [
        { name: 'Male', value: 'male' },
        { name: 'Female', value: 'female' },
      ];
}

export function Gender() {
  return isGenderMerge() ? ['both'] : ['male', 'female'];
}

export function getEnvCountryCode() {
  return getAppEnv('REACT_APP_COUNTRY_CODE');
}

export function getEnvCountryLength() {
  return getAppEnv('REACT_APP_COUNTRY_CODE_LENGTH');
}

export function getEnvAppEnvironment() {
  return getAppEnv('REACT_APP_ENVIRONMENT');
}

export function getEnvCollegeName() {
  const collegeName = getAppEnv('REACT_APP_COLLEGE_NAME');
  return collegeName !== undefined ? collegeName.replaceAll('"', '').replaceAll("'", '') : '';
}

export function getEnvInstitutionId() {
  return getAppEnv('REACT_APP_INSTITUTION_ID');
}

export function isIndianVersion() {
  return getEnvAppEnvironment() === 'ecs-indian';
}

export function getEnvActiveVersion() {
  return process.env.REACT_APP_ACTIVE_VERSION;
}

export function getEnvLabelChanged() {
  return getAppEnv('LABEL_CHANGE') === 'true';
}

export function getUniversityLogo() {
  return process.env.REACT_APP_UNIV_LOGO !== undefined
    ? process.env.REACT_APP_UNIV_LOGO
    : 'default.jpg';
}

export function isDocumentMandatory() {
  return getAppEnv('DOCUMENT') === 'true';
}

export function isMobileVerifyMandatory() {
  return getAppEnv('MOBILE') === 'true';
}

export function envSignUpService(name, bool = false) {
  const services = getDSCookie('login-services');
  if (services !== '') {
    const parsedValue = JSON.parse(services);
    return bool ? parsedValue[name] === 'true' : parsedValue[name];
  }
  return bool ? false : '';
}

export function showOfficeContent() {
  return process.env.REACT_APP_OFFICE_CONTENT;
}

export function showAdminOfficeContent() {
  return process.env.REACT_APP_SHOW_ADMIN_OFFICE === 'true';
}

export function showArabicMailShow() {
  return process.env.REACT_APP_ARABIC_MAIL_SHOW === 'true';
}

export function isModuleEnabled(moduleName = 'ATTAINMENT_MODULE') {
  return getAppEnv(moduleName) === 'true';
}

export function isGenderMerge() {
  return getAppEnv('GENDER_MERGE') === 'true';
}

export function getMailContent() {
  return process.env.REACT_APP_CONTENT_1;
}
export const AlphabetsArray = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];

export const getManualStaffOption = (manualStaffOptions) => {
  return manualStaffOptions.map((staff) => {
    return Map({
      name: formattedFullName(staff.get('staffName', Map()).toJS()),
      value: staff.get('staffId', ''),
    });
  });
};

export const useDeepCompareEffect = (callBack, dependencies) => {
  const currentDependenciesRef = useRef();
  if (!isEqual(currentDependenciesRef.current, dependencies)) {
    currentDependenciesRef.current = dependencies;
  }
  useEffect(callBack, currentDependenciesRef.current); //eslint-disable-line
};

export const useOnScreen = ({ options = {}, dependencies = {} }) => {
  const [ref, setRef] = useState(null);
  const [visible, setVisible] = useState(false);
  useDeepCompareEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setVisible(entry.isIntersecting);
      if (entry.isIntersecting) {
        observer.disconnect();
        return;
      }
    }, options);
    if (ref) observer.observe(ref);
    return () => {
      if (ref) observer.unobserve(ref);
    };
  }, [ref, dependencies]); //eslint-disable-line

  return [setRef, visible];
};

export function hasUserSensitiveData() {
  const userSensitive = envSignUpService('USER_SENSITIVE', true);
  return !userSensitive;
}

export function activeLMSVersion() {
  return getAppEnv('LMS_VERSION');
}

export function parentDetailsMandatory() {
  const detail = envSignUpService('REACT_APP_PARENT_DETAILS_MANDATORY', true);
  return detail !== undefined ? detail === 'true' : false;
}

export const isManualAttendanceEnabled = () => {
  return isModuleEnabled('MANUAL_ATTENDANCE');
};

export function isMissedSessionModuleEnabled() {
  return isModuleEnabled('MISSED_TO_COMPLETE');
}

export function lastNameRequired() {
  return !isIndVer();
}

export function addDefaultLastName(name) {
  return name !== undefined && name.trim() !== '' ? name : '.';
}

export function getSiteUrl() {
  const appLocal = appLocalEnabled();
  const dcAdminUrl = appLocal
    ? process.env.REACT_APP_LOCAL_DC_ADMIN_URL
    : getAppEnv('DC_ADMIN_URL');
  const dcUrl = appLocal ? process.env.REACT_APP_LOCAL_DC_URL : getAppEnv('DC_URL');
  function getCookieName() {
    const domain = process.env.REACT_APP_DOMAIN;
    return domain.includes('localhost') ? 'localhost' : `${domain}`;
  }
  const array = {
    DC_ADMIN_URL: dcAdminUrl,
    DC_URL: dcUrl,
    DA_URL: getAppEnv('DA_URL'),
    DA_URL_1: getAppEnv('DA_URL_1'),
    COOKIE_DOMAIN_NAME: appLocal ? 'localhost' : getCookieName(),
    LANDING_URL: `${removeSlashAtEnd(dcUrl)}/dashboard/main?redirect-domain=${eString(
      'digi-class'
    )}`,
  };
  return array;
}

export const allowSupportAction = () => {
  const action = LocalStorageService.getCustomToken('support');
  return action === 'true';
};

export const appLocalEnabled = () => {
  return process.env.REACT_APP_LOCAL === 'true';
};

export function isGenderMixed() {
  return getAppEnv('GENDER_MIXED') === 'true';
}

export function isAnnouncementEnabled() {
  return isModuleEnabled('ANNOUNCEMENT_V2');
}

export function isLateAbsentEnabled() {
  return isModuleEnabled('LATE_ABSENT_CONFIGURATION');
}

export function isTagMasterEnabled() {
  return isModuleEnabled('TAG_MASTER');
}

export function isDigiSurveyEnabled() {
  return isModuleEnabled('DIGI_SURVEY');
}

export function studentGroupRename(name, pId = '') {
  const patterns = [
    { regex: /mg/gi, identifier: 'mg' },
    { regex: /fg/gi, identifier: 'fg' },
    { regex: /sg/gi, identifier: 'sg' },
  ];

  let replacedString = name;

  patterns.forEach(({ regex, identifier }) => {
    replacedString = replacedString.replace(regex, indVerRename(identifier, pId));
  });

  return replacedString.toUpperCase();
}

export function getVersionName(item) {
  const version = fromJS(item);
  const versionedCourseIds =
    version.get('versionedCourseIds', List()) !== null
      ? version.get('versionedCourseIds', List())
      : List();
  const versionName = ' - ' + capitalize(version.get('versionName', ''));
  return version.get('versioned', false)
    ? versionName
    : !version.get('versioned', false) && versionedCourseIds.size > 0
    ? versionName === ' - '
      ? ' - Default'
      : versionName
    : '';
}

export function toasterMsgFormatter(sentence) {
  if (typeof sentence !== 'string') return sentence;

  const reserveWords = [
    'is',
    'are',
    'was',
    'were',
    'on',
    'in',
    'for',
    'and',
    'should',
    'be',
    'than',
    'of',
    'not',
    'the',
    'has',
    'been',
  ];
  let message = [];
  sentence.split(' ').map((word, index) => {
    (reserveWords.includes(word.toLowerCase()) && index === 0) ||
    !reserveWords.includes(word.toLowerCase())
      ? message.push(word.charAt(0).toUpperCase() + word.slice(1))
      : message.push(word.charAt(0).toLowerCase() + word.slice(1));
    return word;
  });
  return message.join(' ');
}

export const deepCopy = (data) => cloneDeep(data); //eliminating reference

// export const constructQueryParams = (Obj) => {
//   const queryObj = fromJS(Obj).toJS();
//   return Object.keys(queryObj)
//     .map((key) => {
//       const value = queryObj[key];
//       if (Array.isArray(value) && value.length > 0) {
//         return value
//           .map((item) => `${encodeURIComponent(key)}[]=${encodeURIComponent(item)}`)
//           .join('&');
//       } else if (value !== '' && value.length !== 0) {
//         return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
//       }
//     })
//     .filter((item) => item !== undefined)
//     .join('&');
// };

export const recursivelyConstructQueryParams = (Obj) => {
  const queryObj = fromJS(Obj).toJS();
  const keys = Object.keys(queryObj);
  if (!keys.length) return undefined;
  return keys
    .map((key) => {
      const value = queryObj[key];
      if (Array.isArray(value) && value.length > 0) {
        return value
          .map((item) => `${encodeURIComponent(key)}[]=${encodeURIComponent(item)}`)
          .join('&');
      } else if (value instanceof Object) {
        return recursivelyConstructQueryParams(value); // if the value if object again we are continue this process
      } else if (!(value instanceof Function) && value !== '' && value.length !== 0) {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      }
      return undefined;
    })
    .filter((item) => item !== undefined)
    .join('&');
};

export function studentGroupViewList(sessionFlow, programId) {
  const groupedDataImmutable = sessionFlow
    .groupBy((session) => session.get('group_name'))
    .map((group) => {
      return group.reduce(
        (acc, session) => {
          return acc
            .update('total_students', (total) => total + session.get('total_students', ''))
            .update(
              'selected_students',
              (selected) => selected + session.get('selected_students', '')
            )
            .update('session_group', (symbols) =>
              symbols.push(
                getFormattedGroupName(
                  studentGroupRename(
                    session.getIn(['session_group', 0, 'group_name'], ''),
                    programId
                  ),
                  3
                )
              )
            )
            .update('delivery_symbol', (delivery_symbol) => session.get('delivery_symbol', ''));
        },

        fromJS({
          total_students: 0,
          selected_students: 0,
          session_group: [],
          delivery_symbol: '',
        })
      );
    })
    .map((group) => group.update('session_group', (symbols) => symbols.join(', ')));
  return groupedDataImmutable;
}

function removeSlashAtEnd(originalString) {
  return originalString?.replace(/\/$/, '');
}

export function getCollegeLogo() {
  const response = LocalStorageService.getCustomCookie('unifyData', true);
  const collegeLogo = response?.collegeLogo;
  return collegeLogo;
}
