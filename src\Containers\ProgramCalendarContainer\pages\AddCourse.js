import React, { Fragment, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { NotificationManager } from 'react-notifications';

import CourseInput from '../AddCourse/CourseInput';
import IntetrimCourseInput from '../AddCourse/Interim/CourseInput';
import { commonApiCall } from '../../../_reduxapi/actions/calender';
import Loader from '../../../Widgets/Loader/Loader';

const AddCourse = ({ apiCalled, token, interim, commonApiCall }) => {
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let p_id = params.get('programid');
  let p_name = params.get('pname');

  useEffect(() => {
    if (p_id !== null && p_name !== null) {
      commonApiCall(token, NotificationManager, p_id, p_name);
    }
  }, [commonApiCall, p_id, p_name, token]);

  return (
    <Fragment>
      {apiCalled ? (
        interim ? (
          <IntetrimCourseInput />
        ) : (
          <CourseInput />
        )
      ) : (
        <Loader isLoading={!apiCalled} />
      )}
    </Fragment>
  );
};

AddCourse.propTypes = {
  apiCalled: PropTypes.bool,
  interim: PropTypes.bool,
  token: PropTypes.string,
  commonApiCall: PropTypes.func,
};

const mapStateToProps = ({ calender, auth }) => ({
  interim: calender.interim,
  apiCalled: calender.commonApiCalled,
  token: auth.token !== null ? auth.token.replace(/"/g, '') : null,
});

export default connect(mapStateToProps, {
  commonApiCall,
})(AddCourse);
