const jsonData = {};

/*---------------------------------PROGRAM-STATISTICS-START--------------------------------*/
const $header = $('#information');

contentChangeAble('#program-statistics', () => {
  jsonData['title'] = {
    label: $header.find('#courseTitle').html().trim(),
    value: $header.find('#courseValue').html().trim(),
  };
  jsonData['code'] = {
    label: $header.find('#codeTitle').html().trim(),
    value: $header.find('#codeValue').html().trim(),
  };
  jsonData['level'] = {
    label: $header.find('#levelTitle').html().trim(),
    value: $header.find('#levelValue').html().trim(),
  };
  jsonData['department'] = {
    label: $header.find('#departmentTitle').html().trim(),
    value: $header.find('#departmentValue').html().trim(),
  };
  jsonData['college'] = {
    label: $header.find('#collegeTitle').html().trim(),
    value: $header.find('#collegeValue').html().trim(),
  };
  jsonData['institution'] = {
    label: $header.find('#institutionTitle').html().trim(),
    value: $header.find('#institutionValue').html().trim(),
  };
  jsonData['academicYear'] = {
    label: $header.find('#academicTitle').html().trim(),
    value: $header.find('#academicValue').html().trim(),
  };
  jsonData['mainLocation'] = {
    label: $header.find('#locationTitle').html().trim(),
    value: $header.find('#locationValue').html().trim(),
  };
  jsonData['BranchesOfferingTheProgram'] = {
    label: $header.find('#offeringTitle').html().trim(),
    value: [
      $header.find('#list-one').html().trim(),
      $header.find('#list-two').html().trim(),
      $header.find('#list-three').html().trim(),
    ],
  };
});

/*---------------------------------PROGRAM-STATISTICS-END----------------------------------*/

/*---------------------------------INFORMATION-HEADER-START--------------------------------*/
const $programStatistics = $('#program-statistics');
const $programStatisticsAddRow = $programStatistics.find('table tbody');
const $programStatisticsAddColumn = $programStatistics.find('table thead tr');
// const $programStatisticsTableOne = $programStatistics.find('#table-one');

$programStatistics.find('#up-row').click(() => {
  const newRow = $('<tr></tr>');
  $programStatisticsAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $programStatisticsAddRow.append(newRow);
});

$programStatistics.find('#up-column').click(() => {
  $programStatisticsAddColumn.append('<th></th>');
  $programStatisticsAddRow.find('tr').append('<td></td>');
});

contentChangeAble('#program-statistics', () => {
  let programStatistics = {};
  const h2 = $programStatistics.find('h2').html();
  $programStatistics.find('table thead tr th').each(function () {
    programStatistics[$(this).html()] = [];
  });

  const objectKeys = Object.keys(programStatistics);
  $programStatistics.find('table tbody tr').each(function () {
    $(this)
      .find('td')
      .each(function (index) {
        programStatistics[objectKeys[index]].push($(this).html());
      });
  });
  jsonData['programStatistics'] = { title: h2, tableData: programStatistics };
});

/*---------------------------------PROGRAM-ASSESSMENT-START-------------------------------------*/
const $programAssessment = $('#program-assessment');
const $programAssessmentAddRow = $programAssessment.find('#table-one tbody');
const $programAssessmentAddColumn = $programAssessment.find('#table-one thead tr');

/*Table One*/
let selectedValue = 1;
$programAssessment.find('#heading-selector').change(function () {
  selectedValue = $(this).val();
});

$programAssessment.find('#up-row-one').click(() => {
  const newRow = $('<tr></tr>').attr('id', selectedValue);
  $programAssessmentAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $programAssessment.find(`#table-one tbody #${selectedValue}`).each(function () {
    $(this).after(newRow);
  });
});

$programAssessment.find('#up-column-one').click(() => {
  $programAssessmentAddColumn.append('<th></th>');
  $programAssessmentAddRow.find('tr:not(.sub-header)').append('<td></td>');
  $programAssessmentAddRow
    .find('tr th')
    .attr('colspan', $programAssessment.find('#table-one thead tr th').length);
});

$programAssessment.find('#up-heading-one').click(() => {
  $programAssessmentAddRow.append('<tr><th></th></tr>');
  $programAssessmentAddRow
    .find('tr th')
    .attr('colspan', $programAssessment.find('#table-one thead tr th').length);
});

$programAssessment.find('#up-header').click(() => {
  const count = $programAssessment.find('#table-one tbody tr #sub-header').length + 1;
  const columnCount = $programAssessment.find('#table-one thead tr th').length;
  let newRow = $('<tr></tr>').attr('id', count);
  for (let i = 0; i < columnCount; i++) {
    newRow.append('<td></td>');
  }
  $programAssessmentAddRow.append(
    `<tr class="sub-header" id="sub-header${count}"><th colspan=${columnCount} id="sub-header"></th></tr>
    `
  );
  $programAssessmentAddRow.append(newRow);
});

function changeDrop(isempty = false) {
  $programAssessmentAddRow.find('.sub-header th').each(function (index) {
    if (isempty) $programAssessment.find('#heading-selector').empty();
    else
      $programAssessment
        .find('#heading-selector')
        .append(`<option  value=${index + 1}>${$(this).html()}</option>`);
  });
}

$(document).ready(function () {
  changeDrop(true);
  changeDrop();
});
$programAssessment.find('#up-header').click(() => {
  $('#table-one tbody #sub-header').each(function () {
    const observer = new MutationObserver(() => {
      changeDrop(true);
      changeDrop();
    });
    observer.observe(this, {
      childList: true,
      subtree: true,
      characterData: true,
    });
  });
});
contentChangeAble('#program-assessment', () => {
  let programAssessmentTable = {};
  $programAssessment.find('#table-one tbody').each(function () {
    $(this)
      .find('.sub-header th')
      .each(function (parentIndex) {
        const insideKey = $(this).html();
        $programAssessment.find('#table-one').each(function (index) {
          $(this)
            .find('thead tr')
            .each(function () {
              $(this)
                .find('th')
                .each(function () {
                  const headerKey = $(this).html();
                  if (!programAssessmentTable.hasOwnProperty(headerKey)) {
                    programAssessmentTable[headerKey] = [];
                  }
                  programAssessmentTable[headerKey][insideKey] = [];
                });
            });
          const objKeys = Object.keys(programAssessmentTable);
          $(this)
            .find(`tbody #${parentIndex + 1}`)
            .each(function () {
              $(this)
                .find(`td`)
                .each(function (index) {
                  const data = $(this).html();
                  if (objKeys[index]) programAssessmentTable[objKeys[index]][insideKey].push(data);
                });
            });
        });
      });
  });
  jsonData['Program Assessment'] = jsonData.hasOwnProperty('Program Assessment')
    ? jsonData['Program Assessment']
    : {};
  jsonData['Program Assessment'][
    'Program Learning Outcomes Assessment and analysis according to PLOs assessment plan'
  ] = programAssessmentTable;
});

/*Table two*/
contentChangeAble('#program-assessment', () => {
  const $secondTableAddColumn = $programAssessment.find('#table-two thead tr');
  jsonData['Program Assessment'] = jsonData.hasOwnProperty('Program Assessment')
    ? jsonData['Program Assessment']
    : {};
  jsonData['Program Assessment'][
    'Program Learning Outcomes Assessment and analysis according to PLOs assessment plan*'
  ] = {
    reports: {
      frist: $secondTableAddColumn.find('#report-one').html(),
      second: $secondTableAddColumn.find('#report-two').html(),
    },
  };
});

/*Table third*/
const $thirdTableAddRow = $programAssessment.find('#table-three tbody');
const $thirdTableAddColumn = $programAssessment.find('#table-three thead tr');

$programAssessment.find('#up-row-three').click(() => {
  const newRow = $('<tr></tr>');
  $thirdTableAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $thirdTableAddRow.append(newRow);
});

$programAssessment.find('#up-column-three   ').click(() => {
  $thirdTableAddColumn.append('<th></th>');
  $thirdTableAddRow.find('tr').append('<td></td>');
});

contentChangeAble('#program-assessment', () => {
  let evaluationCourses = {};
  $programAssessment.find('#table-three thead tr th').each(function () {
    evaluationCourses[$(this).html()] = [];
  });

  const objectKeys = Object.keys(evaluationCourses);
  $programAssessment.find('#table-three tbody tr').each(function () {
    $(this)
      .find('td')
      .each(function (index) {
        evaluationCourses[objectKeys[index]].push($(this).html());
      });
  });
  jsonData['Program Assessment'] = jsonData.hasOwnProperty('Program Assessment')
    ? jsonData['Program Assessment']
    : {};
  jsonData['Program Assessment']['Students Evaluation of Courses'] = evaluationCourses;
});

/*Table four*/
const $fourTableAddRow = $programAssessment.find('#table-four tbody');
const $fourTableAddColumn = $programAssessment.find('#table-four thead tr');

$programAssessment.find('#up-row-four').click(() => {
  const newRow = $('<tr></tr>');
  $fourTableAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $fourTableAddRow.append(newRow);
});

$programAssessment.find('#up-column-four').click(() => {
  $fourTableAddColumn.append('<th></th>');
  $fourTableAddRow.find('tr').append('<td></td>');
});

/*Table five*/

const $fiveTableAddRow = $programAssessment.find('#table-five tbody');
const $fiveTableAddColumn = $programAssessment.find('#table-five thead tr');

$programAssessment.find('#up-row-five').click(() => {
  const newRow = $('<tr></tr>');
  $fiveTableAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $fiveTableAddRow.append(newRow);
});

$programAssessment.find('#up-column-five').click(() => {
  $fiveTableAddColumn.append('<th></th>');
  $fiveTableAddRow.find('tr').append('<td></td>');
});

contentChangeAble('#program-assessment', () => {
  let scientificResearch = {};
  $programAssessment.find('#table-five thead tr th').each(function () {
    scientificResearch[$(this).html()] = [];
  });

  const objectKeysTableFive = Object.keys(scientificResearch);
  $programAssessment.find('#table-five tbody tr').each(function () {
    $(this)
      .find('td')
      .each(function (index) {
        scientificResearch[objectKeysTableFive[index]].push($(this).html());
      });
  });
  jsonData['Program Assessment'] = jsonData.hasOwnProperty('Program Assessment')
    ? jsonData['Program Assessment']
    : {};
  jsonData['Program Assessment'][
    'Scientific research and innovation during the reporting year'
  ] = scientificResearch;
  console.log(jsonData, 303);
});

/*Discussion and analysics*/
$('#discussionAndAnalysis').change(function (event) {
  const {
    target: { value },
  } = event;
  jsonData['Program Assessment'] = jsonData.hasOwnProperty('Program Assessment')
    ? jsonData['Program Assessment']
    : {};
  jsonData['Program Assessment']['discussionAndAnalysis'] = value;
});

/*Table six*/

const $sixTableAddRow = $programAssessment.find('#table-six tbody');
const $sixTableAddColumn = $programAssessment.find('#table-six thead tr');

$programAssessment.find('#up-row-six').click(() => {
  const newRow = $('<tr></tr>');
  $sixTableAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $sixTableAddRow.append(newRow);
});

$programAssessment.find('#up-column-six').click(() => {
  $sixTableAddColumn.append('<th></th>');
  $sixTableAddRow.find('tr').append('<td></td>');
});

contentChangeAble('#program-assessment', () => {
  let communityPartnership = {};
  $programAssessment.find('#table-six thead tr th').each(function () {
    communityPartnership[$(this).html()] = [];
  });
  const objectKeysTableSix = Object.keys(communityPartnership);
  $programAssessment.find('#table-six tbody tr').each(function () {
    $(this)
      .find('td')
      .each(function (index) {
        communityPartnership[objectKeysTableSix[index]].push($(this).html());
      });
  });
  jsonData['Program Assessment'] = jsonData.hasOwnProperty('Program Assessment')
    ? jsonData['Program Assessment']
    : {};
  jsonData['Program Assessment']['Community Partnership'] = communityPartnership;
});

/*Comment and Community*/
$('#commentAndCommunity').change(function (event) {
  const {
    target: { value },
  } = event;
  jsonData['Program Assessment'] = jsonData.hasOwnProperty('Program Assessment')
    ? jsonData['Program Assessment']
    : {};
  jsonData['Program Assessment']['commentAndCommunity'] = value;
});

/*Table sevenx*/

const $sevenTableAddRow = $programAssessment.find('#table-seven tbody');
const $sevenTableAddColumn = $programAssessment.find('#table-seven thead tr');

$programAssessment.find('#up-row-seven').click(() => {
  const newRow = $('<tr></tr>');
  $sevenTableAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $sevenTableAddRow.append(newRow);
});

$programAssessment.find('#up-column-seven').click(() => {
  $sevenTableAddColumn.append('<th></th>');
  $sevenTableAddRow.find('tr').append('<td></td>');
});

/*---------------------------------PROGRAM-ASSESSMENT-START-------------------------------------*/

/*---------------------------------PROGRAM-KEY-PERFORMANCE-INDICATORS-START---------------------*/
const $performanceIndicators = $('#performance-indicators');
const $performanceIndicatorAddRow = $performanceIndicators.find('#table-one tbody');
const $performanceIndicatorAddColumn = $performanceIndicators.find('#table-one thead tr');

$performanceIndicators.find('#up-row-one').click(() => {
  const newRow = $('<tr></tr>');
  $performanceIndicatorAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $performanceIndicatorAddRow.append(newRow);
});

$performanceIndicators.find('#up-column-one').click(() => {
  $performanceIndicatorAddColumn.append('<th></th>');
  $performanceIndicatorAddRow.find('tr').append('<td></td>');
});
contentChangeAble('#performance-indicators', () => {
  let programKeyPerformance = {};
  $performanceIndicators.find('#table-one thead tr th').each(function () {
    programKeyPerformance[$(this).html()] = [];
  });
  const objectKeysTableOne = Object.keys(programKeyPerformance);
  $performanceIndicators.find('#table-one tbody tr').each(function () {
    $(this)
      .find('td')
      .each(function (index) {
        programKeyPerformance[objectKeysTableOne[index]].push($(this).html());
      });
  });
  jsonData['Program Program Key Performance Indicators'] = programKeyPerformance;
});
/*comment and program*/
$('#commentAndProgram').change(function (event) {
  const {
    target: { value },
  } = event;
  jsonData['Program Key Performance Indicators'] = jsonData.hasOwnProperty(
    'Program Program Key Performance Indicators'
  )
    ? jsonData['Program Program Key Performance Indicators']
    : {};
  jsonData['Program Key Performance Indicators']['commentAndProgram'] = value;
});

/*---------------------------------PROGRAM-KEY-PERFORMANCE-INDICATORS-END-----------------------*/

/*---------------------------------CHALLENGES-DIFFICULTIES-START--------------------------------*/
const $challengesDifficulties = $('#challenges-difficulties');
const $challengesDifficultiesAddRow = $challengesDifficulties.find('#table-one');

$challengesDifficulties.find('#up-row-one').click(() => {
  $challengesDifficultiesAddRow.append('<tr><th></th><td></td></tr>');
});

contentChangeAble('#challenges-difficulties', () => {
  let challengesAndDifficulties = {};
  $challengesDifficulties.find('#table-one  tr th').each(function () {
    challengesAndDifficulties[$(this).html()] = [];
  });
  const challengesObjectKeys = Object.keys(challengesAndDifficulties);
  $challengesDifficulties.find('#table-one tr td').each(function (index) {
    challengesAndDifficulties[challengesObjectKeys[index]] = $(this).html();
  });
  jsonData['Challenges and difficulties encountered by the program'] = challengesAndDifficulties;
});

/*---------------------------------CHALLENGES-DIFFICULTIES-END----------------------------------*/

/*---------------------------------PROGRAM-DEVELOPMENT-START------------------------------------*/
const $progranDevelopmentPlan = $('#program-development-plan');
const $progranDevelopmentPlanIndicatorAddRow = $progranDevelopmentPlan.find('#table-one tbody');
const $progranDevelopmentPlanAddColumn = $progranDevelopmentPlan.find('#table-one thead tr');

$progranDevelopmentPlan.find('#up-row-one').click(() => {
  const newRow = $('<tr></tr>');
  $progranDevelopmentPlanAddColumn.find('th').each(() => {
    newRow.append('<td></td>');
  });
  $progranDevelopmentPlanIndicatorAddRow.append(newRow);
});

$progranDevelopmentPlan.find('#up-column-one').click(() => {
  $progranDevelopmentPlanAddColumn.append('<th></th>');
  $progranDevelopmentPlanIndicatorAddRow.find('tr').append('<td></td>');
});
contentChangeAble('#program-development-plan', () => {
  let ProgramDevelopmentPlan = {};
  $performanceIndicators.find('#table-one thead tr th').each(function () {
    ProgramDevelopmentPlan[$(this).html()] = [];
  });
  const programDevelopTableOne = Object.keys(ProgramDevelopmentPlan);
  $performanceIndicators.find('#table-one tbody tr').each(function () {
    $(this)
      .find('td')
      .each(function (index) {
        ProgramDevelopmentPlan[programDevelopTableOne[index]].push($(this).html());
      });
  });
  jsonData['Program development Plan'] = ProgramDevelopmentPlan;
});

/*---------------------------------PROGRAM-DEVELOPMENT-END--------------------------------------*/

/*---------------------------------APPROVAL-ANNUAL-PROGRAM-START--------------------------------*/
const $approvalAnnual = $('#appproval-annual-program');
const $approvalAnnualAddRow = $approvalAnnual.find('#table-one');

$approvalAnnual.find('#up-row-one').click(() => {
  $approvalAnnualAddRow.append('<tr><th></th><td></td></tr>');
});
contentChangeAble('#appproval-annual-program', () => {
  let approvalOfAnnualProgramReport = {};
  $approvalAnnual.find('#table-one  tr th').each(function () {
    approvalOfAnnualProgramReport[$(this).html()] = [];
  });
  const approvalObjectKeys = Object.keys(approvalOfAnnualProgramReport);
  $approvalAnnual.find('#table-one tr td').each(function (index) {
    approvalOfAnnualProgramReport[approvalObjectKeys[index]] = $(this).html();
  });
  jsonData['Approval of Annual Program Report'] = approvalOfAnnualProgramReport;
  console.log(jsonData, 519);
});

/*---------------------------------APPROVAL-ANNUAL-PROGRAM-END----------------------------------*/

/*-------------------------------------POST-MESSAGE-EVENT--------------------------------------*/
window.addEventListener('message', function (event) {
  const { from } = event.data;
  if (from === 'fromDC') {
    const iframeContent = document.body.innerText;
    event.source.postMessage(iframeContent, '*');
  }
});
/*-------------------------------------POST-MESSAGE-EVENT--------------------------------------*/
/*------------------------------Mutation ObServer--------------------------*/
function contentChangeAble(domElement, callBack) {
  $(domElement).click(function () {
    const observer = new MutationObserver(callBack);
    observer.observe(this, {
      childList: true,
      subtree: true,
      characterData: true,
    });
  });
}
/*------------------------------Mutation ObServer--------------------------*/
