import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Loader from '../../Widgets/Loader/Loader';
import { <PERSON><PERSON>, <PERSON><PERSON>, Tab, Accordion, Card } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import axios from '../../axios';
import './review.css';
import { connect } from 'react-redux';
// import * as actions from "../src/_reduxapi/actions/index";
import moment from 'moment';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import { formatFullName, isIndVer } from '../../utils';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';

const agree = [
  ['Yes', 'yes'],
  ['No', 'no'],
];
class ReviewerResponse extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      selectAgree: agree[0][1][2],
      feed: '',
      reviewlist: [],
      calendarName: null,
      calendarId: null,
      createrName: null,
    };
  }

  componentDidMount() {
    this.interval = setInterval(() => {
      const { activeInstitutionCalendar } = this.props;
      if (!activeInstitutionCalendar.isEmpty()) {
        this.fetchApi();
        clearInterval(this.interval);
      }
    }, 200);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.activeInstitutionCalendar !== this.props.activeInstitutionCalendar) {
      this.fetchApi();
    }
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  getLatestCalendar(id) {
    const { activeInstitutionCalendar } = this.props;
    this.setState({
      calendarName: activeInstitutionCalendar.get('calendar_name', ''),
      calendarId: activeInstitutionCalendar.get('_id', ''),
    });
    // axios
    //   .get(`program_calendar/dashboard/${id}`)
    //   .then((res) => {
    //     let institutionCalendar = res.data.data.institution_calendar;
    //     if (institutionCalendar && institutionCalendar.length > 0) {
    //       this.setState({
    //         calendarName: institutionCalendar[0].calendar_name,
    //         calendarId: institutionCalendar[0]._id,
    //       });
    //     }
    //     this.setState({ isLoading: false });
    //   })
    //   .catch((ex) => {});
  }

  async fetchApi() {
    this.setState({
      isLoading: true,
    });

    const userId = this.props.token;
    let _id;
    const { activeInstitutionCalendar } = this.props;
    if (userId !== null) {
      _id = userId.replace(/^"(.*)"$/, '$1');

      axios
        .get(
          `program_calendar_review/list_approver_reviewer_review/reviewer/${_id}?institutionCalendarId=${activeInstitutionCalendar.get(
            '_id',
            ''
          )}`
        )
        .then((res) => {
          //if (res !== undefined && res.data.status_code === 200) {
          const program = res.data.data.programs.map((data) => {
            return {
              programId: data.program_id,
              programName: data.program_name,
            };
          });
          this.getLatestCalendar(program[0].programId);
          const reviewlist = res.data.data.review.map((data) => {
            return {
              id: data._id,
              programId: data.program_id,
              reviewer: data.reviewer
                .filter((x) => x._reviewer_id === _id)
                .map((data) => {
                  return {
                    data: data,
                    reply: [
                      { name: 'Yes', value: 'yes' },
                      { name: 'No', value: 'no' },
                    ],
                    replyRadio: -1,
                    createrReply: '',
                    _reviewer_id: data._reviewer_id,
                  };
                }),
              other: data.reviewer.filter((x) => x._reviewer_id !== _id),
            };
          });
          let reviewlistbyprogram = program.map((item, i) =>
            Object.assign({}, item, reviewlist[i])
          );
          let createrName = res.data.data.creator.name;

          this.setState({
            reviewlist: reviewlistbyprogram,
            isLoading: false,
            createrName: formatFullName(createrName),
          });
          //}
        })
        .catch((ex) => {
          this.setState({
            reviewlist: [],
            isLoading: false,
          });
        });
    }
  }

  handleChange = (e, i, index) => {
    const reviewlist = [...this.state.reviewlist];
    reviewlist[index].reviewer[i].createrReply = e.target.value;
    this.setState({
      reviewlist,
    });
  };

  onChangeReply = (option, replyindex, i, index) => {
    const reviewlist = this.state.reviewlist;
    reviewlist[index].reviewer[i].replyRadio = replyindex;
    this.setState({
      reviewlist,
    });
  };

  reviewerSubmit = (e, index) => {
    const reviewlist = this.state.reviewlist[index];
    const data = {
      _calendar_id: reviewlist.id,
      reviewer_type: 'reviewer',
      _reviewer_id: reviewlist.reviewer[0]._reviewer_id,
      review: reviewlist.reviewer[0].replyRadio === 0 ? true : false,
      review_comment: reviewlist.reviewer[0].createrReply,
    };

    // return;

    axios
      .post(`program_calendar_review/add_reviewer_review`, data)
      .then((res) => {
        NotificationManager.success(`Comment sent successfully`);
        this.setState(
          {
            isLoading: false,
          },
          () => this.fetchApi()
        );
      })
      .catch((ex) => {});
  };

  render() {
    const basePath = process.env.REACT_APP_BASE_PATH || '';
    const items = [{ to: '#', label: 'Program Calendar > Review' }];

    return (
      <React.Fragment>
        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link
                className="breadcrumb-icon"
                style={{ color: '#fff', marginTop: '-68px' }}
                key={index}
                to={to}
              >
                {label}
              </Link>
            ))}
        </Breadcrumb>
        <Tabs activeKey={this.state.key} onSelect={this.handleSelect} id="tab-example">
          {this.state.reviewlist.map((data, index) => (
            <Tab eventKey={index} title={data.programName} key={index}>
              <React.Fragment>
                <div className="pt-3 pb-5 bg-white">
                  <Loader isLoading={this.state.isLoading} />

                  {/* container start  */}
                  <div className="container">
                    {/* row start  */}
                    <div className="row mt-1 pb-1">
                      <div className="col-md-5 ">
                        <div className="float-left">
                          <span className="pl-2 f-17 font-weight-bold">
                            {' '}
                            Review Courses & Events
                          </span>
                          <h3 className=" pl-2  f-14 pt-2">
                            Academic year {this.state.calendarName} | {data.programName} Program
                            Calendar
                          </h3>
                        </div>
                      </div>
                    </div>
                    {/* row end  */}

                    <hr />

                    {/* row start  */}
                    <div className="row mt-1 pb-3">
                      <div className="col-md-6 pt-2 pb-2">
                        <p> Your Review</p>
                        {data.reviewer.map((reviewer, i) => {
                          let expiryTime = 0;
                          if (
                            reviewer.data.expire !== undefined &&
                            reviewer.data.expire.expire_time !== undefined
                          ) {
                            let st = new Date(reviewer.data.expire.expire_time);
                            var startTime = moment(st);
                            var endTime = moment();
                            // calculate total duration
                            var duration = moment.duration(startTime.diff(endTime));
                            var days1 = parseInt(duration.asDays());
                            // duration in hours
                            var hours1 = parseInt(duration.asHours());
                            // duration in minutes
                            var minutes1 = parseInt(duration.asMinutes()) % 60;
                            if (minutes1 > 0) {
                              expiryTime =
                                days1 +
                                ' ' +
                                'D' +
                                ' ' +
                                hours1 +
                                ' ' +
                                'H' +
                                ' ' +
                                minutes1 +
                                ' ' +
                                'M';
                            }
                          }

                          return (
                            <React.Fragment key={i}>
                              {reviewer.data.status.charAt(0).toUpperCase() +
                                reviewer.data.status.slice(1) ===
                                'Pending' && (
                                <div className="reply_comment">
                                  <div className="row">
                                    <div className="col-md-6">
                                      <p>{formatFullName(reviewer.data.reviewer_name)} </p>
                                    </div>

                                    <div className="col-md-6 ">
                                      <p className="text-right f-14">
                                        Time left: <b>{expiryTime}</b>
                                      </p>
                                    </div>

                                    {expiryTime !== 0 && (
                                      <>
                                        <div className="col-md-6"></div>
                                        <div className="col-md-6 float-right">
                                          <p className="text-right"></p>
                                        </div>

                                        <div className="col-md-12">
                                          <p className="mb-1"> Reply</p>

                                          {reviewer.reply.map((option, replyindex) => (
                                            <div key={replyindex}>
                                              <input
                                                type="radio"
                                                className={'form-control form-radio1'}
                                                value={option.name}
                                                checked={
                                                  parseInt(reviewer.replyRadio) === replyindex
                                                    ? true
                                                    : false
                                                }
                                                onChange={(e) =>
                                                  this.onChangeReply(option, replyindex, i, index)
                                                }
                                              />
                                              <span className="radio-label1">{option.name}</span>
                                            </div>
                                          ))}
                                          {reviewer.replyRadio === 1 && (
                                            <div className="pt-3">
                                              <textarea
                                                value={reviewer.createrReply}
                                                onChange={(e) => this.handleChange(e, i, index)}
                                                maxLength="250"
                                                name="createrReply"
                                                className={'form-control'}
                                                placeholder={'Add a comment'}
                                              />
                                            </div>
                                          )}
                                        </div>

                                        <div className="col-md-12 pt-3 ">
                                          <div className="float-right">
                                            <Button
                                              variant="outline-primary"
                                              onClick={(e) => this.reviewerSubmit(e, index)}
                                            >
                                              Send{' '}
                                            </Button>
                                          </div>
                                        </div>
                                      </>
                                    )}
                                  </div>
                                </div>
                              )}

                              {reviewer.data.status === 'Reviewed' ||
                              reviewer.data.status === 'Done' ? (
                                <div className="pb-3">
                                  <div className="reviewer-status">
                                    <div className="row">
                                      <div className="col-md-8">
                                        <p className=" mb-0">
                                          {formatFullName(reviewer.data.reviewer_name)}{' '}
                                          <small>
                                            -{' '}
                                            {moment(reviewer.data.reviewer_timestamp).format(
                                              'MMMM Do YYYY, h:mm:ss a'
                                            )}{' '}
                                          </small>
                                        </p>
                                      </div>

                                      <div className="col-md-4 ">
                                        <div className="float-right">
                                          {reviewer.data.reviews === false && (
                                            <Button variant="outline-danger border_cuve" size="sm">
                                              No
                                            </Button>
                                          )}
                                          {reviewer.data.reviews === true && (
                                            <Button variant="outline-success border_cuve" size="sm">
                                              Yes
                                            </Button>
                                          )}
                                        </div>
                                      </div>

                                      <div className="col-md-10">
                                        <small>{reviewer.data.reviewer_comment}</small>
                                      </div>
                                    </div>

                                    {reviewer.data.creater_comment !== undefined && (
                                      <div className="row pt-3">
                                        <div className="col-md-9 pl-4">
                                          <p className=" mb-0 side_quote">
                                            -{this.state.createrName} <small></small>{' '}
                                            <small>
                                              -{' '}
                                              {moment(reviewer.data.creater_timestamp).format(
                                                'MMMM Do YYYY, h:mm:ss a'
                                              )}{' '}
                                            </small>
                                          </p>
                                          <p className=" mb-0 side_quote">
                                            <small>{reviewer.data.creater_comment}</small>
                                          </p>
                                        </div>

                                        <div className="col-md-3 ">
                                          <div className="float-right">
                                            {reviewer.data.creater_feedback === false && (
                                              <Button
                                                variant="outline-danger border_cuve_normal"
                                                size="sm"
                                              >
                                                Disagree
                                              </Button>
                                            )}
                                            {reviewer.data.creater_feedback === true && (
                                              <Button
                                                variant="outline-success border_cuve_normal"
                                                size="sm"
                                              >
                                                Agree
                                              </Button>
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ) : (
                                ''
                              )}
                            </React.Fragment>
                          );
                        })}
                      </div>

                      <div className="col-md-6 pt-2 pb-2">
                        <p>Other&apos;s Responses</p>
                        {data.other.map((other, index) => {
                          return (
                            <div className="pb-3" key={index}>
                              <div className="reviewer-status">
                                <div className="row">
                                  <div className="col-md-8">
                                    <p className=" mb-0">
                                      {formatFullName(other.reviewer_name)}{' '}
                                      <small>
                                        {other.reviewer_timestamp !== undefined &&
                                        other.reviewer_timestamp !== ''
                                          ? ' - ' +
                                            moment(other.reviewer_timestamp).format(
                                              'MMMM Do YYYY, h:mm:ss a'
                                            )
                                          : ''}{' '}
                                      </small>
                                    </p>
                                  </div>

                                  <div className="col-md-4 ">
                                    <div className="float-right">
                                      {other.reviews === false && (
                                        <Button variant="outline-danger border_cuve" size="sm">
                                          No
                                        </Button>
                                      )}
                                      {other.reviews === true && (
                                        <Button variant="outline-success border_cuve" size="sm">
                                          Yes
                                        </Button>
                                      )}
                                      {other.status === 'Pending' && (
                                        <p className="text-right mb-0">Pending</p>
                                      )}
                                    </div>
                                  </div>
                                  {other.reviewer_comment !== undefined && (
                                    <div className="col-md-10">
                                      <small>{other.reviewer_comment}</small>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                    {/* row end  */}
                  </div>

                  {/* container end  */}
                </div>
                <div className="main">
                  {this.state.calendarId !== null && this.state.calendarName !== null && (
                    <div className="col-md-12 ">
                      <Accordion defaultActiveKey="" className="pt-4">
                        <Card>
                          <Accordion.Toggle as={Card.Header} eventKey="0">
                            Program Calender
                          </Accordion.Toggle>
                          <Accordion.Collapse eventKey="0">
                            <Card.Body className="bg-white">
                              <div className="row">
                                {(this.state.calendarName.toLowerCase().indexOf('foundation') ||
                                  isIndVer()) > -1 ? (
                                  <iframe
                                    src={`${basePath}/program-calendar/${this.state.calendarId}/year1?year=year1&icd=${this.state.calendarId}&programid=${data.programId}&iframeShow=true&iframeProgramId=${data.programId}&iframeName=${data.programName}`}
                                    height="1200"
                                    width="100%"
                                    title="pcYear1"
                                  />
                                ) : (
                                  <iframe
                                    src={`${basePath}/program-calendar/${this.state.calendarId}/year2?year=year2&icd=${this.state.calendarId}&programid=${data.programId}&iframeShow=true&iframeProgramId=${data.programId}&iframeName=${data.programName}`}
                                    height="1200"
                                    width="100%"
                                    title="pcYear2"
                                  />
                                )}
                                {/* <iframe src={`/program-calendar/${this.state.calendarId}/year2?year=year2&icd=${this.state.calendarId}&programid=${data.programId}&iframeShow=true&iframeProgramId=${data.programId}&iframeName=${this.state.calendarName}`} height="1200" width="100%"/>  */}
                                {/* <iframe src={`/program-calendar?year=year1&icd=5f68aa2d5932201986a904d5&programid=5f5a39c5af4bdebbcb90c8e0&iframeShow=true&iframeProgramId=5f5a39c5af4bdebbcb90c8e0&iframeName=Nursing`} height="1200" width="100%"/>  */}
                              </div>
                            </Card.Body>
                          </Accordion.Collapse>
                        </Card>
                      </Accordion>
                    </div>
                  )}
                </div>
              </React.Fragment>
            </Tab>
          ))}
        </Tabs>
        {this.state.reviewlist.length === 0 && (
          <div className="pt-3">
            <div className="col-md-12">
              <div className="notpublished-screen">
                <div className="notpublished">
                  <h2>No request at present</h2>
                </div>
              </div>
            </div>
          </div>
        )}
      </React.Fragment>
    );
  }
}

ReviewerResponse.propTypes = {
  isAuthenticated: PropTypes.bool,
  token: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  return {
    isAuthenticated: state.auth.token !== null,
    token: state.auth.token,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps)(withRouter(ReviewerResponse));
