.universityCard {
  background: white;
  min-height: 500px;
  margin-top: 13px;
  box-shadow: 0px 1px 3px rgba(17, 24, 39, 0.2);
  border-radius: 8px;
}
.innerUniverCard {
  border-bottom: 1px solid #d1d5db;
  padding: 30px 20px 30px 20px;
}
.search-border {
  border-radius: 20px !important;
}
.pd-1{
  padding-left: 1em;
}
.pd-2{
  padding-left: 2em;
}
.pd-3{
  padding-left: 3em;
}
.pd-4{
  padding-left: 4em;
}
.pd-5{
  padding-left: 5em;
}
.pd-x{
 padding-right: 6px;
 padding-left: 6px;
}
.Auto-OverFlow{
 overflow-y: scroll;
max-height: 19.7em;
}

.Auto-OverFlow::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.Auto-OverFlow::-webkit-scrollbar-thumb {
  background: #E2DBDE;
  border-radius: 10px;
}
.Auto-OverFlow_program{
  overflow-y: scroll;
 max-height: 19.4em;
 }
 
.Auto-OverFlow_program::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.Auto-OverFlow_program::-webkit-scrollbar-thumb {
  background: #E2DBDE;
  border-radius: 10px;
}
.inner-overflow{
  overflow-y: scroll;
}

.inner-overflow::-webkit-scrollbar {
  height: 0px;
  width: 0px;
}

.accordion-content {
  height: 0;
  opacity: 0;
  overflow: hidden;
  transition: height 0.5s ease-out, opacity 0.5s ease-out;
}
.accordion-content.open {
  height: auto;
  opacity: 1;
  overflow: visible;
  transition: height 0.5s ease-in, opacity 0.5s ease-in;
}
.bg-lite-green{
  background-color:#EFF9FB;
}

.bg-lite:hover{
  background-color:#EFF9FB;
  cursor: pointer;
}
.rounded-border{
  border-radius: 21px !important;
}
.pageDisableMode{
width: 100%;
height: 100%;
position: absolute;
left: 0px;
top:0px;
background-color: ghostwhite;
opacity: 0.75;
cursor:not-allowed;
}
