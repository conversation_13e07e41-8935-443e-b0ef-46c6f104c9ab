import React, { useEffect, useState, useContext, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Map, List } from 'immutable';

import { useStylesFunction } from '../../css/designUtils';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import {
  Accordion,
  AccordionDetails,
  //Collapse,
} from '@mui/material';
import {
  addAttainmentLevel,
  setData,
  getAssessmentLearningOutCome,
} from '_reduxapi/assessment/action';
import { CheckPermission } from 'Modules/Shared/Permissions';

import {
  getColor,
  optionWithSelect,
  getOutComeOptions,
  BenchMarkTableHeader,
  AttainmentLevelContext,
  LevelTableHeader,
  LevelDropdown,
  AccordionTile,
  FirstColumn,
  setValueInState,
  LevelInput,
  RangeRow,
  getData,
  getDataFromEvaluationPlan,
  getIdFromAttainmentLevel,
  noValuesInAttainment,
  getDataFromAttainmentLevel,
  overAllAttainment,
  validation,
} from './AttainmentLevelUtils';
import { getURLParams, indVerRename } from 'utils';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import InputAdornment from '@mui/material/InputAdornment';

const AttainmentProvider = (props) => {
  const manageOutCome = getURLParams('outCome', true);
  const [constructedData, setConstructedData] = useState(List());
  const [outCome, setOutcome] = useState(manageOutCome ? manageOutCome : '');
  const [outComeId, setOutcomeId] = useState('');
  const [valuesIn, setValuesIn] = useState('');
  const [levelStart, setLevelStart] = useState('');
  const [levelEnd, setLevelEnd] = useState('');
  const [targetAttainment, setTargetAttainment] = useState('assessment');
  const [manageTargetBenchMark, setManageTargetBenchMark] = useState(List());
  const type = getURLParams('type', true);

  const dispatch = useDispatch();
  const attainmentDetails = useSelector(({ assessment }) =>
    assessment.get('attainmentDetails', Map())
  );

  useEffect(() => {
    let outComeId = getIdFromAttainmentLevel(attainmentDetails, outCome);
    if (outCome) {
      if (noValuesInAttainment(attainmentDetails, outCome, type)) {
        const options = getDataFromEvaluationPlan(attainmentDetails, outCome);
        let newOptions = options.unshift(overAllAttainment);
        setConstructedData(newOptions);
        setLevelStart('');
        setLevelEnd('');
        setValuesIn('');
      } else {
        const { constructData, start, end, valuesAs } = getDataFromAttainmentLevel(
          attainmentDetails,
          outCome,
          type
        );
        setLevelStart(start);
        setLevelEnd(end);
        setValuesIn(valuesAs);
        setConstructedData(constructData);
      }
    }
    setOutcomeId(outComeId);
  }, [outCome, attainmentDetails, type]);

  const postData = (data, requestParams) => dispatch(addAttainmentLevel(data, requestParams));
  const showError = (errorMessage) => dispatch(setData(Map({ message: errorMessage })));
  const isLevelSelected = levelStart !== '' && levelEnd !== '' && valuesIn !== '';
  const state = {
    valuesIn,
    outCome,
    levelStart,
    levelEnd,
    constructedData,
    isLevelSelected,
    outComeId,
    attainmentDetails,
    targetAttainment,
    manageTargetBenchMark,
  };
  const setState = {
    setValuesIn,
    setLevelEnd,
    setLevelStart,
    setConstructedData,
    setOutcome,
    postData,
    showError,
    setTargetAttainment,
    setManageTargetBenchMark,
  };
  const value = { state, setState };
  return <AttainmentLevelContext.Provider value={value} {...props} />;
};

const LevelAccordion = () => {
  const classes = useStylesFunction();
  const { state, setState } = useContext(AttainmentLevelContext);
  const [expanded, setExpanded] = useState('panel');
  const { constructedData, isLevelSelected } = state;
  const { setConstructedData } = setState;

  const handleChanges = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  const showLevelRow = (itemProps) => {
    const { index, item } = itemProps;
    return (
      <React.Fragment key={index}>
        <tr>
          <FirstColumn
            setConstructedData={setConstructedData}
            showChildren={'showLevelChildren'}
            {...itemProps}
          />
          <LevelInput {...itemProps} {...state} {...setState} />
        </tr>
        {item.get('showLevelChildren', false) &&
          item
            .get('children', List())
            .map((sItem, sIndex) => {
              if (sItem.get('isSelected', false))
                return showLevelRow({ index: sIndex, item: sItem });
              return '';
            })
            .filter((item) => item !== '')}
      </React.Fragment>
    );
  };

  return (
    <Accordion
      expanded={expanded === 'panel'}
      onChange={handleChanges('panel')}
      className={classes.accordionBackgroundNone}
    >
      <AccordionTile title={'Attainment Level'} />
      <AccordionDetails>
        <div className="w-100 ">
          <LevelDropdown />
          {isLevelSelected && (
            <div className="attainment_table">
              <table align="left">
                <LevelTableHeader />
                <tbody>
                  <RangeRow {...state} />
                  {showLevelRow({ index: 0, item: constructedData.get(0, Map()) })}
                  {constructedData.getIn([0, 'showLevelChildren'], false) &&
                    constructedData
                      .map((item, index) => {
                        if (index !== 0 && item.get('isSelected', false))
                          return showLevelRow({ index, item });
                        return '';
                      })
                      .filter((item) => item !== '')}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </AccordionDetails>
    </Accordion>
  );
};
const BenchmarkAccordion = () => {
  const classes = useStylesFunction();
  const { state, setState } = useContext(AttainmentLevelContext);
  const [expanded, setExpanded] = useState('panel');
  const {
    levelStart,
    levelEnd,
    constructedData,
    targetAttainment,
    manageTargetBenchMark,
    attainmentDetails,
  } = state;
  const { setConstructedData, setTargetAttainment, setManageTargetBenchMark } = setState;
  const dispatch = useDispatch();
  const programId = getURLParams('pid', true);
  const institutionCalendarId = getURLParams('year', true);
  const courseId = getURLParams('courseId', true);
  const term = getURLParams('term', true);
  const outCome = getURLParams('outCome', true);
  const type = getURLParams('type', true);
  useEffect(() => {
    if (type === 'course') {
      dispatch(
        getAssessmentLearningOutCome(programId, institutionCalendarId, term, outCome, courseId)
      );
    }
  }, []); // eslint-disable-line
  const outComeLists = useSelector(({ assessment }) => assessment.get('outComeLists'));

  useEffect(() => {
    if (type === 'course') {
      const filterOutcomeType = attainmentDetails
        .get('attainmentLevel', List())
        .filter((item) => item.get('outComeType', '') === outCome);
      const filteredOutcome = filterOutcomeType.getIn(
        [0, 'manageTargetBenchMark', 'outComes'],
        List()
      );
      if (outComeLists.size) {
        setManageTargetBenchMark(filteredOutcome.size > 0 ? filteredOutcome : outComeLists);
        setTargetAttainment(
          filterOutcomeType.getIn([0, 'manageTargetBenchMark', 'outComes'], List()).size
            ? filterOutcomeType.getIn([0, 'outComeType'], '')
            : 'assessment'
        );
      }
    }
  }, [outComeLists, attainmentDetails]); // eslint-disable-line

  const handleChanges = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  const getBenchMarkOptions = () => {
    const options = Array(Number(levelEnd) - Number(levelStart) + 1)
      .fill(0)
      .map((_, index) => ({
        name: `Level ${index + Number(levelStart)}`,
        value: `Level ${index + Number(levelStart)}`,
      }));
    return optionWithSelect({ options });
  };
  const showBenchMarkRow = (index, item) => {
    return (
      <React.Fragment key={index}>
        <tr>
          <FirstColumn
            index={index}
            setConstructedData={setConstructedData}
            item={item}
            showChildren={'showBenchmarkChildren'}
          />

          <td className="attainment_border_bottom">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: getBenchMarkOptions() }}
              labelclass={'mb-0 f-14'}
              id={'#bg-white'}
              value={item.get('benchMark', '')}
              changed={(e) => {
                setValueInState(index, item, 'benchMark', setConstructedData, e);
              }}
            />
          </td>
        </tr>
        {item.get('showBenchmarkChildren', false) &&
          item
            .get('children', List())
            .map((sItem, sIndex) => {
              if (sItem.get('isSelected', false)) return showBenchMarkRow(sIndex, sItem);
              return '';
            })
            .filter((item) => item !== '')}
      </React.Fragment>
    );
  };

  const handleChange = (e, index) => {
    if (e.target.value && (isNaN(e.target.value) || e.target.value < 1 || e.target.value > 100))
      return;
    setManageTargetBenchMark(manageTargetBenchMark.setIn([index, 'values'], e.target.value));
  };

  return (
    <Accordion
      expanded={expanded === 'panel'}
      onChange={handleChanges('panel')}
      className={classes.accordionBackgroundNone}
    >
      <AccordionTile title={'Target Benchmark'} />
      <AccordionDetails>
        <>
          {type === 'course' && (
            <div className="pl-4">
              <RadioGroup
                row
                aria-labelledby="demo-radio-buttons-group-label"
                defaultValue="assessment"
                name="radio-buttons-group"
                value={targetAttainment}
                onChange={(e) => {
                  setTargetAttainment(e.target.value);
                }}
              >
                <FormControlLabel value="assessment" control={<Radio />} label="Assessment" />
                <FormControlLabel
                  value={outCome}
                  control={<Radio />}
                  label={outCome === 'clo' ? 'CO' : 'PO'}
                />
              </RadioGroup>
            </div>
          )}
          {targetAttainment === 'assessment' ? (
            <div className="w-50">
              <div className="attainment_table">
                <table align="left">
                  <BenchMarkTableHeader />
                  <tbody>
                    {showBenchMarkRow(0, constructedData.get(0, Map()))}
                    {constructedData.getIn([0, 'showBenchmarkChildren'], false) &&
                      constructedData
                        .map((item, index) => {
                          if (index !== 0 && item.get('isSelected', false))
                            return showBenchMarkRow(index, item);
                          return '';
                        })
                        .filter((item) => item !== '')}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="w-100">
              <div className="attainment_table">
                <table align="left">
                  <thead>
                    <tr>
                      <th scope="col" className="borderNone">
                        <div className="row cw_350 custom_space">
                          <div className="col-md-5">
                            <div className="cw_100">
                              <p className="thHeader text-left">
                                {outCome === 'clo' ? 'CO' : 'PO'}
                              </p>
                            </div>
                          </div>
                          <div className="col-md-7">
                            <div className="cw_150">
                              <MaterialInput
                                elementType={'materialSelect'}
                                type={'text'}
                                variant={'outlined'}
                                size={'small'}
                                elementConfig={{
                                  options: [
                                    {
                                      name: 'Percentage',
                                      value: 'percentage',
                                    },
                                  ],
                                }}
                                labelclass={'mb-0 f-14'}
                                id={'#bg-white'}
                              />
                            </div>
                          </div>
                        </div>
                      </th>
                      {outComeLists.map((item, index) => (
                        <th scope="col" className="borderNone" key={index}>
                          <div className="cw_150 custom_space  justify-content-center align-items-center">
                            <p className="thHeader mr-2">{item.get('no', '')}</p>
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <th scope="col" className="attainment_border_bottom bg-white">
                        <div className="justify-content-between custom_space align-items-center">
                          <p className="thHeader text-left"> Set Target </p>
                        </div>
                      </th>
                      {manageTargetBenchMark.map((item, index) => (
                        <td className="attainment_border_bottom" key={index}>
                          <MaterialInput
                            fullWidth={true}
                            elementType={'materialInput'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                            label={''}
                            inputAdornment={<InputAdornment position="end">%</InputAdornment>}
                            changed={(e) => handleChange(e, index)}
                            value={item.get('values')}
                          />
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      </AccordionDetails>
    </Accordion>
  );
};
function AttainmentLevelComponent() {
  const { state, setState } = useContext(AttainmentLevelContext);
  const type = getURLParams('type', true);
  const manageOutCome = getURLParams('outCome', true);
  const programId = getURLParams('pid', true);
  const courseId = getURLParams('courseId', true);
  const term = getURLParams('term', true);
  const levelNo = getURLParams('levelNo', true);
  const {
    valuesIn,
    outCome,
    levelStart,
    levelEnd,
    constructedData,
    isLevelSelected,
    outComeId,
    attainmentDetails,
    manageTargetBenchMark,
    targetAttainment,
  } = state;
  const { setOutcome, postData, showError } = setState;
  const outComeOptions = useMemo(() => getOutComeOptions(attainmentDetails, programId), [
    attainmentDetails,
    programId,
  ]);
  const onSelectOutcome = (e) => {
    setOutcome(e.target.value);
  };
  const handleSubmit = () => {
    const requestParams = { programId, courseId, levelNo, term };
    const requiredData = { valuesIn, levelStart, levelEnd };
    const { inCompleteData, validationMessage } = validation(
      constructedData,
      requiredData,
      targetAttainment,
      manageTargetBenchMark
    );

    if (inCompleteData) return showError(validationMessage);
    else {
      const data = {
        _id: attainmentDetails.get('_id', ''),
        masterId: outComeId,
        start: levelStart,
        end: levelEnd,
        valuesAs: valuesIn,
        nodes: getData(constructedData, 'nodes', requiredData),
        levels: getData(constructedData, 'levels', requiredData),
        targetBenchMark:
          targetAttainment === 'assessment'
            ? getData(constructedData, 'targetBenchMark', requiredData)
            : [],
        levelColors: getColor(levelStart, levelEnd),
        type: type,
        manageTargetBenchMark:
          targetAttainment !== 'assessment'
            ? {
                valuesType: 'percentage',
                outComes: manageTargetBenchMark.map((item) => {
                  return {
                    outComeId: item.get('outComeId', '')
                      ? item.get('outComeId', '')
                      : item.get('_id', ''),
                    values: item.get('values', ''),
                  };
                }),
              }
            : {},
      };
      return postData(data, requestParams);
    }
  };

  const Header = () => (
    <div className="row">
      <div className="col-md-4">
        {type === 'program' ? (
          <MaterialInput
            elementType={'materialSelect'}
            type={'text'}
            variant={'outlined'}
            size={'small'}
            elementConfig={{ options: outComeOptions }}
            label={'Select Outcome'}
            labelclass={'mb-0'}
            id={'#bg-white'}
            changed={onSelectOutcome}
            value={outCome}
          />
        ) : (
          indVerRename(manageOutCome.toUpperCase(), programId)
        )}
      </div>
      {CheckPermission(
        'subTabs',
        'Attainment Calculator',
        'Attainment Setting',
        '',
        'Regulations',
        '',
        'Attainment Level',
        'Add / Update'
      ) && (
        <div className="col-md-8 text-right mt-3">
          <MButton variant="contained" color="primary" clicked={handleSubmit}>
            Save
          </MButton>
        </div>
      )}
    </div>
  );
  useEffect(() => {
    if (outComeOptions.length !== 0 && outCome === '') setOutcome(outComeOptions[1].value);
  }, [setOutcome, outComeOptions]); // eslint-disable-line

  return (
    <div className="bg-gray p-4">
      <Header />
      {outCome !== '' && (
        <div className="col-12 pt-3">
          <LevelAccordion state={state} setState={setState} />
          {isLevelSelected && <BenchmarkAccordion state={state} setState={setState} />}
        </div>
      )}
    </div>
  );
}
const AttainmentLevel = () => (
  <AttainmentProvider>
    <AttainmentLevelComponent />
  </AttainmentProvider>
);
export default AttainmentLevel;
