import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import arLocale from '@fullcalendar/core/locales/ar';
import arrow from '../../../../Assets/dropdown.png';
// import success from '../../../../Assets/alert2.png';
import { Dropdown } from 'react-bootstrap';
import moment from 'moment';
import {
  getFormattedGroupName,
  extractedRequiredDate,
  convertTime12to24,
  getWeeksArray,
  getFirstLastDate,
  formatStartEndDate,
} from '../utils';
import Tooltips from '../../../../_components/UI/Tooltip/Tooltip';
import {
  formatTwoString,
  isIndGroup,
  jsUcfirstAll,
  getTranslatedDuration,
  getLang,
  studentGroup<PERSON>ena<PERSON>,
  getUR<PERSON><PERSON><PERSON>,
  studentGroupViewList,
} from '../../../../utils';
import { START_TIME, END_TIME } from '../../../../constants';
import { transformDateToCustomObject } from '../utils';
import DeliveryTypeList from './DeliveryTypeList';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

class ScheduleCalendar extends Component {
  calendarComponentRef = React.createRef();
  state = {
    weekendsVisible: true,
    eventsList: [],
    currentEvents: [],
    weeksArray: List([]),
    activeRotation: Map(),
  };

  componentDidMount() {
    this.interval = setInterval(() => {
      const { course } = this.props;
      if (course.get('start_date', '') !== '') {
        clearInterval(this.interval);
        this.loadExtraCurricularAndBreakEvents();
        this.getWeeksArray();
        this.setSessionFlowActive(course.get('session_flow', List()));
        // let calendarApi1 = this.calendarComponentRef.current.getApi();
        // calendarApi1.gotoDate(this.scheduleCalendarStartEndDate('start_date'));
      }
    }, 500);
  }

  componentDidUpdate(prevProps) {
    if (!prevProps.course.equals(this.props.course)) {
      this.loadExtraCurricularAndBreakEvents();
    }
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  setSessionFlowActive = (sessionFlow) => {
    const { setData } = this.props;
    if (sessionFlow.size > 0) {
      this.triggerWeek(sessionFlow.getIn([0, 'week'], 1));
      setData(Map({ activeSessionFlow: sessionFlow.get(0) }));
    }
  };

  loadExtraCurricularAndBreakEvents = () => {
    const { course, activeViewType } = this.props;
    const { activeRotation } = this.state;
    const fromDate = activeRotation.isEmpty()
      ? this.scheduleCalendarStartEndDate('start_date')
      : moment(activeRotation.get('start_date')).format('YYYY-MM-DD');
    const toDate = activeRotation.isEmpty()
      ? this.scheduleCalendarStartEndDate('end_date')
      : moment(activeRotation.get('end_date')).format('YYYY-MM-DD');
    let totalDateArray = [];
    let eventExcludeDate = [];
    course
      .get('event_list', List())
      .toJS()
      .map((item) => {
        var start = new Date(item.event_date);
        var end = new Date(item.end_date);
        for (var d = start; d <= end; d.setDate(d.getDate() + 1)) {
          var loopDay = new Date(d);
          const formatDate = moment(loopDay).format('YYYY-MM-DD');
          eventExcludeDate.push(formatDate);
        }
        totalDateArray.push({
          title: item.event_name,
          shortTitle: item.event_name,
          groupId: 'EXTRA_CURRICULAR_AND_BREAK',
          start: new Date(item.start_time),
          end: new Date(item.end_time),
          type: 'extra_curricular',
          allowEdit: false,
        });
        return item;
      });
    course
      .get('extra_curricular_break_timing', List())
      .toJS()
      .map((item) => {
        let checkDate = extractedRequiredDate(fromDate, toDate, item.days);
        //checkDate = checkDate.filter((item) => !eventExcludeDate.includes(item));
        if (checkDate && checkDate.length > 0) {
          checkDate.forEach((key) => {
            totalDateArray.push({
              title: item.title,
              shortTitle: item.title,
              groupId: 'EXTRA_CURRICULAR_AND_BREAK',
              start:
                key +
                'T' +
                convertTime12to24(
                  formatTwoString(item.startTime.hour) +
                    ':' +
                    formatTwoString(item.startTime.minute) +
                    ' ' +
                    item.startTime.format
                ),
              end:
                key +
                'T' +
                convertTime12to24(
                  formatTwoString(item.endTime.hour) +
                    ':' +
                    formatTwoString(item.endTime.minute) +
                    ' ' +
                    item.endTime.format
                ),
              type: 'extra_curricular',
              allowEdit: item.allowCourseCoordinatesToEdit,
              titleText:
                item.startTime.hour +
                ':' +
                formatTwoString(item.startTime.minute) +
                ' - ' +
                item.endTime.hour +
                ':' +
                formatTwoString(item.endTime.minute),
            });
          });
        }
        return item;
      });

    if (activeViewType === 'regular') {
      course.get('session_flow', List()).map((session) => {
        session.get('schedule', List()).map((schedule) => {
          const chooseDate = moment(schedule.get('schedule_date')).format('YYYY-MM-DD');
          const title = schedule
            .get('student_groups', List())
            .map((studentGroup) => {
              const studentGroupName = getFormattedGroupName(
                studentGroup.get('group_name', ''),
                isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
              );
              const sessionGroupNames = studentGroup
                .get('session_group', List())
                .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
                .join(', ');
              return `${studentGroupName.replace('-', '')} - ${sessionGroupNames.replace('-', '')}`;
            })
            .join(', ');
          const subject = schedule
            .get('subjects', List())
            .map((subject) => subject.get('subject_name'))
            .join(', ');
          const deliveryNo = session.get('delivery_symbol', '') + session.get('delivery_no', '');
          totalDateArray.push({
            title: title + ' ' + deliveryNo + '-' + subject,
            shortTitle: title,
            groupId: 'SESSION_SCHEDULE',
            start:
              chooseDate +
              'T' +
              convertTime12to24(
                formatTwoString(schedule.getIn(['start', 'hour'])) +
                  ':' +
                  formatTwoString(schedule.getIn(['start', 'minute'])) +
                  ' ' +
                  schedule.getIn(['start', 'format'])
              ),
            end:
              chooseDate +
              'T' +
              convertTime12to24(
                formatTwoString(schedule.getIn(['end', 'hour'])) +
                  ':' +
                  formatTwoString(schedule.getIn(['end', 'minute'])) +
                  ' ' +
                  schedule.getIn(['end', 'format'])
              ),
            startStr: new Date(
              chooseDate +
                'T' +
                convertTime12to24(
                  formatTwoString(schedule.getIn(['start', 'hour'])) +
                    ':' +
                    formatTwoString(schedule.getIn(['start', 'minute'])) +
                    ' ' +
                    schedule.getIn(['start', 'format'])
                )
            ),
            endStr: new Date(
              chooseDate +
                'T' +
                convertTime12to24(
                  formatTwoString(schedule.getIn(['end', 'hour'])) +
                    ':' +
                    formatTwoString(schedule.getIn(['end', 'minute'])) +
                    ' ' +
                    schedule.getIn(['end', 'format'])
                )
            ),
            type: 'schedule',
            backgroundColor: 'hsl(' + Math.random() * 360 + ', 100%, 75%)',
            sessionFlow: session,
            scheduleDate:
              schedule.get('schedule_date') !== null
                ? schedule.get('schedule_date', '').substr(0, 10)
                : '',
            schedule: schedule,
            allowEdit: false,
            titleText:
              schedule.getIn(['start', 'hour']) +
              ':' +
              formatTwoString(schedule.getIn(['start', 'minute'])) +
              ' - ' +
              schedule.getIn(['end', 'hour']) +
              ':' +
              formatTwoString(schedule.getIn(['end', 'minute'])),
          });
          return schedule;
        });
        return session;
      });
    } else {
      const { programId } = this.props;
      course.get('session_flow', List()).map((session) => {
        const chooseDate = moment(session.get('schedule_date')).format('YYYY-MM-DD');
        const title = jsUcfirstAll(session.get('sub_type', '').replace('_', ' '));
        const eventName = jsUcfirstAll(session.get('title', ''));
        const studentGroupsConcact =
          session.get('type', '') !== 'regular'
            ? studentGroupViewList(session.get('student_groups', List()), programId)
                .entrySeq()
                .map(
                  ([groupName, sGroup]) =>
                    getFormattedGroupName(studentGroupRename(groupName, programId), 2) +
                    (sGroup.get('delivery_symbol', '') !== ''
                      ? `-${sGroup.get('session_group')}`
                      : '')
                )
                .join(', ')
            : session.get('student_groups', List()).map((studentGroup) => {
                return `${getFormattedGroupName(
                  studentGroup.get('group_name', ''),
                  isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                )}`;
              });
        totalDateArray.push({
          title: eventName + ' - ' + title + ' - ' + studentGroupsConcact,
          shortTitle: title,
          groupId: 'SESSION_SCHEDULE',
          start:
            chooseDate +
            'T' +
            convertTime12to24(
              formatTwoString(session.getIn(['start', 'hour'])) +
                ':' +
                formatTwoString(session.getIn(['start', 'minute'])) +
                ' ' +
                session.getIn(['start', 'format'])
            ),
          end:
            chooseDate +
            'T' +
            convertTime12to24(
              formatTwoString(session.getIn(['end', 'hour'])) +
                ':' +
                formatTwoString(session.getIn(['end', 'minute'])) +
                ' ' +
                session.getIn(['end', 'format'])
            ),
          type: 'schedule',
          backgroundColor: 'hsl(' + Math.random() * 360 + ', 100%, 75%)',
          scheduleDate:
            session.get('schedule_date') !== null
              ? session.get('schedule_date', '').substr(0, 10)
              : '',
          schedule: session,
          allowEdit: false,
          titleText:
            session.getIn(['start', 'hour']) +
            ':' +
            formatTwoString(session.getIn(['start', 'minute'])) +
            ' - ' +
            session.getIn(['end', 'hour']) +
            ':' +
            formatTwoString(session.getIn(['end', 'minute'])),
        });

        return session;
      });
    }

    this.setState({ eventsList: totalDateArray });
  };

  prevClick = () => {
    let calendarApi = this.calendarComponentRef.current.getApi();
    calendarApi.prev();
  };

  nextClick = () => {
    let calendarApi = this.calendarComponentRef.current.getApi();
    calendarApi.next();
  };

  getWeekStartEndDate = () => {
    if (this.calendarComponentRef.current !== null) {
      let calendarApi = this.calendarComponentRef.current.getApi();
      var currentDate = calendarApi.getDate();
      return fromJS(getFirstLastDate(currentDate));
    } else {
      return fromJS({ firstDate: '', lastDate: '' });
    }
  };

  getCurrentWeekNumber = () => {
    const { weeksArray } = this.state;
    const formatDate = formatStartEndDate(this.getWeekStartEndDate());

    return weeksArray
      .filter((item) => item.get('formattedDate') === formatDate)
      .reduce((_, el) => el);
  };

  scheduleCalendarStartEndDate = (date) => {
    const { course } = this.props;
    if (course.get(date, '') !== '') {
      return moment(course.get(date)).format('YYYY-MM-DD');
    }
    return moment().format('YYYY-MM-DD');
  };

  getWeeksArray = (start = '', end = '') => {
    const valueArray = getWeeksArray(
      start === ''
        ? this.scheduleCalendarStartEndDate('start_date')
        : moment(start).format('YYYY-MM-DD'),
      end === '' ? this.scheduleCalendarStartEndDate('end_date') : moment(end).format('YYYY-MM-DD')
    );
    this.setState({ weeksArray: fromJS(valueArray) }, () => {
      if (start !== '') {
        this.goToDate(moment(start).format('YYYY-MM-DD'));
        this.triggerWeek(this.props.activeSessionFlow.get('week', 0));
        this.loadExtraCurricularAndBreakEvents();
      }
    });
  };

  goToDate = (date) => {
    let calendarApi1 = this.calendarComponentRef.current.getApi();
    calendarApi1.gotoDate(date);
  };

  allowScheduledTiming = (data) => {
    const startTime = Date.parse(moment(data.start).format('LL') + ' ' + START_TIME);
    const endTime = Date.parse(moment(data.end).format('LL') + ' ' + END_TIME);
    const checkStartTime = Date.parse(data.start);
    const checkEndTime = Date.parse(data.end);
    if (startTime <= checkStartTime && endTime >= checkEndTime) {
      return true;
    }
    return false;
  };

  handleDateSelect = (selectInfo) => {
    const allowScheduledTiming = this.allowScheduledTiming(selectInfo);
    if (!allowScheduledTiming) {
      return;
    }
    const {
      activeViewType,
      handleAddEditSchedule,
      activeSessionFlow,
      handleAddEditSupportAndEvents,
      setData,
      course,
    } = this.props;
    if (course.get('rotation', 'no') === 'yes' && this.state.activeRotation.isEmpty()) {
      setData(Map({ message: t('course_schedule.validation.select_rotation') }));
      return;
    }

    const duration = activeSessionFlow.get('duration', 0);
    const isScheduled = activeSessionFlow.get('schedule_date', '');
    const formatDate = {
      start: transformDateToCustomObject(selectInfo.start),
      end:
        duration !== 0
          ? transformDateToCustomObject(
              new Date(moment(selectInfo.start).add(duration, 'minutes').format())
            )
          : transformDateToCustomObject(selectInfo.end),
      schedule_date: moment(selectInfo.start).format('YYYY-MM-DD'),
    };
    if (activeViewType === 'regular') {
      handleAddEditSchedule(activeSessionFlow, 'create', Map(), formatDate);
    } else {
      if (!isScheduled) {
        handleAddEditSupportAndEvents({
          schedule: activeSessionFlow,
          mode: 'create',
          defaultDate: formatDate,
        });
      }
    }
  };

  handleEventClick = (clickInfo) => {
    const { activeViewType, handleAddEditSchedule, handleAddEditSupportAndEvents } = this.props;
    if (clickInfo.event?.extendedProps?.type === 'extra_curricular') {
      if (clickInfo.event?.extendedProps?.allowEdit) {
        this.handleDateSelect({ start: clickInfo.event?.start, end: clickInfo.event?.end });
      }
      return false;
    }
    const schedule = clickInfo.event.extendedProps.schedule || Map();
    if (activeViewType === 'regular') {
      handleAddEditSchedule(clickInfo.event.extendedProps.sessionFlow, 'update', schedule);
    } else {
      handleAddEditSupportAndEvents({
        schedule: schedule,
        mode: 'update',
      });
    }
  };

  handleEvents = (events) => {
    this.setState({
      currentEvents: events,
    });
  };

  checkDateAndTime = (checkStartTime, checkEndTime, startTime, endTime) => {
    if (
      (startTime < checkStartTime || checkEndTime > startTime) &&
      (endTime > checkStartTime || checkEndTime < endTime)
    ) {
      return false;
    }
    return true;
  };

  countOfSameTimeEvents = (eventInfo) => {
    const { eventsList } = this.state;
    return eventsList.filter(
      (item) =>
        !this.checkDateAndTime(
          Date.parse(item.startStr),
          Date.parse(item.endStr),
          Date.parse(eventInfo.event.start),
          Date.parse(eventInfo.event.end)
        ) &&
        item.groupId === 'SESSION_SCHEDULE' &&
        item.scheduleDate === eventInfo.event.extendedProps.scheduleDate
    ).length;
  };

  renderEventContent = (eventInfo) => {
    const programId = getURLParams('programId', true);
    const countOfSameTimeEvents = this.countOfSameTimeEvents(eventInfo);
    return (
      <div className={'text-center calendarFont'}>
        {countOfSameTimeEvents > 3 ? (
          <Tooltips title={eventInfo.event.title + '.' + eventInfo.timeText}>
            <b>{eventInfo.event.extendedProps.shortTitle}</b>
          </Tooltips>
        ) : (
          <>
            {eventInfo.event.title !== '' && (
              <Tooltips
                title={
                  studentGroupRename(eventInfo.event.title, programId) + '.' + eventInfo.timeText
                }
              >
                <b>{studentGroupRename(eventInfo.event.title, programId)}</b>
                <br />
              </Tooltips>
            )}
            <i>{eventInfo.timeText}</i>
          </>
        )}
      </div>
    );
  };

  triggerWeek = (week) => {
    const { weeksArray } = this.state;
    const data = weeksArray.filter((item, index) => index === week - 1);
    if (data.getIn([0, 'date'], '') !== '') {
      setTimeout(() => {
        this.goToDate(data.getIn([0, 'date'], ''));
        this.loadExtraCurricularAndBreakEvents();
      }, 500);
    }
  };

  getTitle(viewType) {
    const viewTypes = {
      regular: t('configuration.delivery_type'),
      support: t('Support Session'),
      events: t('event_title_type'),
    };
    return viewTypes[viewType] || t('configuration.delivery_type');
  }

  setActiveRotation = (rotation) => this.setState({ activeRotation: rotation });

  render() {
    const {
      course,
      setData,
      activeSessionFlow,
      fetchCourseSchedule,
      paginationMetaData,
      isLoading,
      activeViewType,
    } = this.props;

    const { weeksArray, eventsList, weekendsVisible, activeRotation } = this.state;
    return (
      <div className="col-lg-12">
        <div className="row border-top">
          <div className="col-md-3 border-right pr-0">
            <p className="mb-2 pl-3 pt-2 bold">{this.getTitle(activeViewType)}</p>
            <DeliveryTypeList
              activeViewType={activeViewType}
              course={course}
              activeSessionFlow={activeSessionFlow}
              fetchCourseSchedule={fetchCourseSchedule}
              paginationMetaData={paginationMetaData}
              isLoading={isLoading}
              setData={setData}
              triggerWeek={this.triggerWeek}
              activeRotation={activeRotation}
              setActiveRotation={this.setActiveRotation}
              getWeeksArray={this.getWeeksArray}
            />
          </div>
          <div className="col-md-9">
            <div className="calendar-container">
              <div className="calendar-header">
                <h1>
                  <Trans i18nKey={'schedule'}></Trans>{' '}
                  <span className="device-show">
                    (Note: For iPad / Tablet users please click and hold the time slot for one
                    second to open schedule popup){' '}
                  </span>
                </h1>
                <div className="col-md-12 col-xl-6 col-lg-6 border border-radious-8 p-2">
                  <div className="d-flex">
                    <span className="pr-2" onClick={this.prevClick}>
                      <i
                        className="fa fa-angle-left bg-gray arrow_padding text-skyblue"
                        aria-hidden="true"
                      ></i>
                    </span>

                    <span className="pl-2" onClick={this.nextClick}>
                      <i
                        className="fa fa-angle-right bg-gray arrow_padding text-skyblue"
                        aria-hidden="true"
                      ></i>
                    </span>
                    <span className="pl-3 pr-3 border-right">
                      <Trans i18nKey={'program_calendar.week'}></Trans>{' '}
                      {this.getCurrentWeekNumber() !== undefined
                        ? this.getCurrentWeekNumber().get('weekNumber', 1)
                        : 1}
                    </span>
                    <span className="pl-3 pr-3">
                      {getTranslatedDuration(formatStartEndDate(this.getWeekStartEndDate()))}
                    </span>

                    {weeksArray.size > 0 && (
                      <b>
                        <Dropdown>
                          <Dropdown.Toggle
                            variant=""
                            id="dropdown-table"
                            className="table-dropdown"
                            size="sm"
                          >
                            <div>
                              <img src={arrow} alt="Deactivated" title="Deactivated" />
                            </div>
                          </Dropdown.Toggle>
                          <Dropdown.Menu>
                            {weeksArray.map((item) => (
                              <Dropdown.Item
                                key={item.get('weekNumber')}
                                onClick={() => this.goToDate(item.get('date', ''))}
                                className={
                                  item.get('formattedDate', '') ===
                                  formatStartEndDate(this.getWeekStartEndDate())
                                    ? 'activeItem'
                                    : ''
                                }
                              >
                                {getTranslatedDuration(item.get('formattedDate', ''))}
                              </Dropdown.Item>
                            ))}
                          </Dropdown.Menu>
                        </Dropdown>{' '}
                      </b>
                    )}
                  </div>
                </div>
              </div>
              <div className="schedule-calendar">
                {course.get('start_date') !== '' && (
                  <FullCalendar
                    ref={this.calendarComponentRef}
                    plugins={[timeGridPlugin, interactionPlugin]}
                    headerToolbar={{
                      left: '',
                      center: '',
                      right: '',
                    }}
                    dayHeaderContent={(dt) => {
                      return moment(dt.date).format('ddd D, MMM');
                    }}
                    initialView="timeGridWeek"
                    dayHeaderFormat={{ day: 'numeric', weekday: 'short' }}
                    validRange={{
                      start: activeRotation.isEmpty()
                        ? this.scheduleCalendarStartEndDate('start_date')
                        : moment(activeRotation.get('start_date')).format('YYYY-MM-DD'),
                      end: activeRotation.isEmpty()
                        ? this.scheduleCalendarStartEndDate('end_date') + ' 23:59:59'
                        : moment(activeRotation.get('end_date')).format('YYYY-MM-DD') + ' 23:59:59',
                    }}
                    scrollTime={'08:00:00'}
                    eventOverlap={true}
                    editable={false}
                    selectable={true}
                    selectMirror={true}
                    dayMaxEvents={true}
                    weekends={weekendsVisible}
                    events={eventsList}
                    eventTextColor={'black'}
                    contentHeight={'auto'}
                    // eventDidMount={function (info) {
                    // }}
                    // slotDuration={'00:15:00'}
                    // slotLabelInterval={60}
                    select={
                      CheckPermission(
                        'subTabs',
                        'Schedule Management',
                        'Course Scheduling',
                        '',
                        'Schedule',
                        '',
                        'Course Schedule',
                        'Add'
                      )
                        ? this.handleDateSelect
                        : ''
                    }
                    eventContent={this.renderEventContent} // custom render function
                    eventClassNames={function (arg) {
                      if (arg.event.extendedProps.type) {
                        return ['blockEvents'];
                      } else {
                        return ['normal'];
                      }
                    }}
                    locale={getLang() === 'ar' ? arLocale : null}
                    slotEventOverlap={false}
                    eventClick={
                      CheckPermission(
                        'subTabs',
                        'Schedule Management',
                        'Course Scheduling',
                        '',
                        'Schedule',
                        '',
                        'Course Schedule',
                        'Edit'
                      )
                        ? this.handleEventClick
                        : ''
                    }
                    eventsSet={this.handleEvents} // called after events are initialized/added/changed/removed
                    allDaySlot={false}
                    longPressDelay={100}
                    /* you can update a remote database when these fire:
                      eventAdd={function(){}}
                      eventChange={function(){}}
                      eventRemove={function(){}}
                      */
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

ScheduleCalendar.propTypes = {
  activeViewType: PropTypes.string,
  handleAddEditSchedule: PropTypes.func,
  course: PropTypes.instanceOf(Map),
  activeSessionFlow: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  handleAddEditSupportAndEvents: PropTypes.func,
  fetchCourseSchedule: PropTypes.func,
  paginationMetaData: PropTypes.object,
  isLoading: PropTypes.bool,
  programId: PropTypes.string,
};

export default ScheduleCalendar;
