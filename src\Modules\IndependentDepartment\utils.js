import { List } from 'immutable';

export const getDepartments = (departmentDetails, departmentTabValue) => {
  return departmentDetails
    .get('allDepartments', List())
    .filter((fItem) => fItem.get('type', 'academic') === departmentTabValue)
    .map((item) => {
      return {
        name: item.get('departmentName', ''),
        value: item.get('_id', ''),
      };
    })
    .toJS();
};

export const getPrograms = (departmentDetails) => {
  return departmentDetails
    .get('allPrograms', List())
    .map((item) => {
      return {
        name: item.get('name', ''),
        value: item.get('_id', ''),
      };
    })
    .toJS();
};

export const getSubjects = (departmentDetails, departmentTabValue) => {
  return departmentDetails
    .get('allSubjects', List())
    .filter((fItem) => fItem.get('type', 'academic') === departmentTabValue)

    .map((item) => {
      return {
        name: item.get('subjectName', ''),
        value: item.get('_id', ''),
      };
    })
    .toJS();
};

export const getProgramData = (programInputList) => {
  if (programInputList.get('programWiseDepts', List())?.size > 0) {
    return programInputList
      .get('programWiseDepts')
      .map((item) => {
        return {
          name: item.get('programName', ''),
          value: item.get('programId', ''),
        };
      })
      .toJS();
  } else {
    return [];
  }
};

export const getProgramName = (programInputList, selectedProgramId) => {
  let programName;
  const filterProgram = programInputList
    .get('programWiseDepts', List())
    .find((item) => item.get('programId', '') === selectedProgramId);
  programName = filterProgram.get('programName', '');
  return programName;
};
