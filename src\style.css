.pl_50 {
  padding-left: 50px;
}

.notification-item {
  padding: 20px;
  background: #4062d4;
  color: #fff;
  border-radius: 5px;
}

.notification-item-img {
  height: 50px;
  margin-top: -17px;
  margin-bottom: -19px;
}

.badge_msg {
  float: right;
  margin-top: 3px;
  margin-right: 16px;
}

::-webkit-input-placeholder {
  /* Edge */
  color: black;
  opacity: 0.5;
}

:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: black;
  opacity: 0.5;
}

::placeholder {
  color: black;
  opacity: 0.5;
}

.text-grey {
  color: #ababab;
}

.error {
  color: red;
}

.ril__image {
  width: 680px;
}

.white {
  background-color: #fff;
}

.table_bgcolor {
  background-color: #d1f4ff;
}

.profile_view {
  color: #007bff;
  text-decoration: none;
  cursor: pointer;
  word-wrap: break-word;
}

.schedule_days {
  font-size: 16px;
  padding: 3px;
  color: #000000;
  background: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  cursor: pointer;
}

.selected_days {
  background: #1e7be2;
  color: #ffffff;
}

.employement_define {
  border: 1px solid #1e7be2;
  border-radius: 8px;
  padding: 20px;
}

.monthCalendar {
  /* width: 300px; */
  border: 1px solid #ccc6c6;
  /* position: absolute; */
  padding: 10px 15px 10px 15px;
  background: #fff;
  z-index: 1;
}

.selected {
  color: #ffffff !important;
  border-radius: 7px;
  background-color: #fe0f58;
}

.hijriNumber {
  font-size: 10px;
  color: #808080;
  margin-bottom: 6px;
  margin-top: -4px;
}

.td {
  display: table-cell;
  padding-top: 3px;
  cursor: pointer;
}

.disabled {
  color: lightgray !important;
  pointer-events: none;
}

.program-plus1 {
  margin-left: -14px;
  padding-top: 24px;
}

.tab_custom {
  flex-direction: row;
  padding: 8px 16px;
  margin: 4px;
  width: 48px;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 22px;
  color: #3e95ef;
  cursor: pointer;
  font-size: 16px;
}

.tab_custom_active {
  border: 2px solid #3e95ef;
}

.tabs_main {
  overflow-x: auto;
  padding: 25px 25px 15px 25px;
  background: #f5f6f8;
}

.title {
  font-size: 13px;
  color: #818ea3;
}
.hijriMonthName {
  font-size: 13px;
  color: #808080;
}
.table {
  display: table;
  color: #1a1919;
  font-size: 13px;
}
.tr {
  display: table-row;
}

.td {
  display: table-cell;
  padding-top: 3px;
}

/* .disabled{
  color: lightgray;
  pointer-events: none;
}

.selected { 
  color: #ffffff;
  width: 30px;
  padding: 5px;
  border-radius: 7px;
  background-color: #fe0f58;
} */

/* .hijriNumber{
  font-size: 10px;
  color: #808080;
  margin-bottom: 6px;
  margin-top: -4px;
} */
.mb-0 {
  margin-bottom: 0px;
}
.border-none {
  border: none;
}
.calenderTopContent {
  position: relative;
}
.calendarDropdown {
  margin-top: 0px;
  height: 32px !important;
  -webkit-appearance: none;
  width: 90px;
  padding: 4px 0px;
  /* padding: 5px; */
  padding-left: 12px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0);
  border: solid 1px #a9b9c6;
}
.settingWidth {
  width: 18%;
}
.caretDownColor {
  color: #a9b9c6;
}
.dropdownBorder {
  border: solid 1px #a9b9c6 !important;
}
.calendarCard {
  background-color: #fff;
  text-align: left;
  padding: 17px 35px 17px 35px;
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
}
.calenderLineBorder {
  margin-left: -35px;
  margin-right: -35px;
}
.calendarTodayBorder {
  border: 1px solid #ebeced;
  padding: 8px;
  border-radius: 10px;
  font-size: 12px;
}
.calendarMt-28 {
  margin-top: 28px;
}
.dropDownColor {
  color: #3d5170;
}
.calendarArrow {
  border: 1px solid #ebeced;
  padding: 8px;
  border-radius: 10px;
  font-size: 12px;
}
@media screen and (min-width: 320px) and (max-width: 767px) {
  .settingWidth {
    width: 20%;
    margin-right: -20px;
  }
  .calendarCard {
    padding: 6px;
  }
  .calenderLineBorder {
    margin-left: -6px;
    margin-right: -6px;
  }
}

.tabs_main1 {
  overflow-x: auto;
  padding: 10px 0px;
  background: #f5f6f8;
}

.tab_custom_active1 {
  border: 2px solid #4e5aeb;
}
.tab_custom1 {
  flex-direction: row;
  padding: 5px 10px;
  margin: 0px 4px 0px 0px;
  width: 19px;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 22px;
  color: #131fba;
  cursor: pointer;
}

.group-label {
  padding: 2px 10px;
  border: 1px solid rgba(0, 0, 0, 0.36);
  border-radius: 12px;
}

.groups-input {
  /* position: relative; */
  font-family: inherit;
  background-color: transparent;
  width: 10%;
  padding: 3px 4px 5px 5px;
  font-size: 17px;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #757575;
}

.groups-preview {
  box-shadow: inset 4px 4px 4px rgba(0, 0, 0, 0.2), inset 0px 0px 2px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  height: 200px;
  overflow-x: auto;
}

.preview-label {
  padding: 2px 10px;
  border: 1px solid rgba(0, 0, 0, 0.36);
  border-radius: 12px;
  width: 80%;
  margin: 10px 0px 0px 14px;
}

.notpublished-screen {
  border-radius: 7px;
  box-shadow: 5px 4px 7px 3px rgba(0, 0, 0, 0.14);
  height: 300px;
}

.notpublished {
  position: absolute;
  left: 50%;
  top: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
}

.notpublished h2 {
  color: #000;
  font-size: 24px;
  font-weight: 500;
  text-transform: uppercase;
  margin-top: 0;
}

button,
i.fas,
.avail {
  cursor: pointer;
}

.printTitle {
  display: none;
}

.printTitle h5 {
  font-size: 30px;
  word-spacing: 1px;
}

.search-list {
  width: 100%;
  position: relative;
  display: flex;
}

.searchTerm-list {
  min-width: 350px;
  padding: 4px 34px 4px 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-sizing: border-box;
  border-radius: 8px;
  height: 38px;
}

.searchButton-list {
  color: #bcc0c0;
  position: relative;
  top: 10px;
  right: 26px;
}

::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(0, 0, 0, 0.38);
}
::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(0, 0, 0, 0.38);
}
:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(0, 0, 0, 0.38);
}
:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(0, 0, 0, 0.38);
}

.tr-bottom-border:hover {
  background: linear-gradient(0deg, rgba(78, 160, 235, 0.12), rgba(78, 160, 235, 0.12)), #ffffff;
}
.fb-icon {
  background: url('Assets/hands-off.svg') no-repeat;
  height: 50px;
  width: 50px;
}

.tr-bottom-border:hover .fb-icon {
  background: url('Assets/hands-on.svg') no-repeat;
  height: 50px;
  width: 50px;
}

.wd-10 {
  width: 10%;
}

.custom-dropdown {
  top: 100px !important;
  right: 0px !important;
  opacity: 1 !important;
  left: unset !important;
  pointer-events: unset !important;
}

.custom-dropdown-left {
  top: 100px !important;
  opacity: 1 !important;
  left: -100px !important;
  pointer-events: unset !important;
  border-radius: 4px;
  box-shadow: 0 3px 6px 3px #e2dbdec7;
}

@media only screen and (min-width: 768px) {
  .custom-dropdown-left {
    top: 100px !important;
    opacity: 1 !important;
    left: 0px !important;
    pointer-events: unset !important;
    border-radius: 4px;
    box-shadow: 0 3px 6px 3px #e2dbdec7;
  }
}

.toolbar-circle {
  padding: 3px;
  text-align: center;
  max-width: 37px;
}
.toolbar-circle:hover {
  background: #eaedef;
  border-radius: 17px;
  transition: all 0.2s ease-in-out;
}

.cursor-pointer {
  cursor: pointer;
}

.display-table {
  display: table;
}
.display-table-cell {
  display: table-cell;
  top: 22px;
  position: relative;
  padding-right: 6px;
  font-size: 40px;
}

.mlm-5 {
  margin-left: -15px !important;
}

.plt-5 {
  padding-left: 5px;
  padding-top: 5px;
}

.disabled-flex {
  display: flex;
}

.modal-450 {
  max-width: 450px;
}

.max-450 {
  max-height: 450px !important;
}

.pl-20 {
  padding-left: 20px;
}

.display-flex {
  display: flex !important;
}
.icon-pos {
  position: absolute;
  bottom: 4px;
  left: -12px;
}

.icon-li-pos {
  position: relative;
}

.paddingTop-0 {
  padding-top: 0rem !important;
}

.program_table {
  position: relative;
  width: 100%;
  z-index: 1;
  margin: auto;
  overflow: auto;
  height: auto;
  min-height: 100px;
}
.program_table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.program_table tbody tr:hover {
  background: #eff9fb;
}

.program_table td,
.program_table th {
  padding: 7px 7px 5px 7px;
  border-bottom: 1px solid #d1d5db;
  /* background: #fff; */
  vertical-align: middle;
}

.program_table thead th {
  background: #fff;
  color: #000;
  position: sticky;
  top: 0;
  vertical-align: middle;
}
/* safari and ios need the tfoot itself to be position:sticky also */
.program_table tfoot,
.program_table tfoot th,
.program_table tfoot td {
  position: sticky;
  bottom: 0;
  background: #fff;
  color: #000;
  z-index: 4;
}

.program_table th:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 2;
}
.program_table thead th:first-child,
.program_table tfoot th:first-child {
  z-index: 5;
}

.bg_lightRed {
  background: #fff0f1 !important;
}

/* Handle on hover */
.program_table::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}
.program_table::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Handle */
.program_table::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.pi_sidebar {
  width: 300px;
  min-height: calc(700px - 81px);
  border-right: 2px solid #d1d5db !important;
}
.AssessmentActive {
  color: #147afc;
}
.subject_hover_pi:hover {
  background: #edf6ff;
  padding: 15px;
  color: #147afc;
}
.subject_hover_pi {
  padding: 15px;
  cursor: pointer;
  border-bottom: 1px solid #eeeeee9c;
}
.innerNav {
  padding: 15px 10px 15px 35px;
  border-bottom: 1px solid #eeeeee9c;
}
.innerNav:hover {
  background: #edf6ff;
}
.innerNav.active {
  background: #edf6ff;
  color: #147afc;
}
.border-bottom-2px {
  border-bottom: 2px solid #d1d5db !important;
}
.minWidth-32 {
  min-width: 32px !important;
}
.muiOutline {
  border: 1px solid #c5bfbf;
  border-radius: 4px;
}
.muiOutline:hover {
  border: 1px solid #1b1c1c;
}
.levelBox {
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0px 3px 6px -2px #888888c4;
}
.bg-primary {
  background-color: #53aef9 !important;
}
.height-13 {
  height: 13px;
}

.assessment_table {
  position: relative;
  width: 100%;
  z-index: 1;
  margin: auto;
  overflow: auto;
  height: 500px;
  overflow-y: scroll;
  border-radius: 6px;
}
.assessment_table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}
.table-wrap {
  position: relative;
}
.assessment_table td {
  padding: 5px 10px;
  border: 1px solid #dee2e6;
  background: #fff;
  vertical-align: middle;
}
.assessment_table th {
  padding: 5px 10px;
  background: #f9fafb;
  vertical-align: middle;
  border: 1px solid #dee2e6;
  box-shadow: 1px 0px 5px 0px #88888838;
}
.assessment_table thead th {
  background: #e5e7eb !important;
  color: #000;
  position: sticky;
  top: 0;
  z-index: 1;
}
/* safari and ios need the tfoot itself to be position:sticky also */
.assessment_table tfoot,
.assessment_table tfoot th,
.assessment_table tfoot td {
  position: sticky;
  bottom: 0;
  background: #fff;
  color: #000;
  z-index: 4;
}

.assessment_table th:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 2;
}
.assessment_table thead th:first-child,
.assessment_table tfoot th:first-child {
  z-index: 5;
}
.assessment_table tbody tr:nth-child(1) {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 56px;
}
.assessment_table tbody tr:nth-child(2) {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 123px;
}
.assessment_table tbody tr:nth-child(3) {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 179px;
}

.assessment_table tbody tr:nth-child(1),
.assessment_table tbody tr:nth-child(2),
.assessment_table tbody tr:nth-child(3) {
  z-index: 5;
}

.assessment_table thead th:nth-child(2n + 3),
.assessment_table tfoot td:nth-child(2n + 3),
.assessment_table tbody td:nth-child(2n + 3) {
  background: #f8fafc;
}

/* Handle on hover */
.assessment_table::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}
.assessment_table::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Handle */
.assessment_table::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.thHeader {
  text-align: center;
  margin-bottom: 0px;
  font-weight: 500;
  padding: 6px 0px 6px 0px;
}
.cw_80 {
  min-height: 1px;
  width: 80px;
  word-wrap: break-word;
}
.cw_200 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}
.cw_100 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}
.cw_150 {
  min-height: 1px;
  width: 250px;
  word-wrap: break-word;
}
.cw_350 {
  min-height: 1px;
  width: 350px;
  word-wrap: break-word;
}
.borderNone {
  border: none !important;
}
.w-28 {
  width: 28%;
}
.questionBorder {
  border: 2px solid #605d5d;
  padding: 0px 3px 0px 3px;
  font-size: 13px;
  border-radius: 4px;
}
select#\#bg-white {
  background-color: white;
}

.attainment_table {
  position: relative;
  width: 100%;
  z-index: 1;
  margin: auto;
  overflow: auto;
  height: auto;
  overflow-y: scroll;
  border-radius: 6px;
  box-shadow: 0px 3px 7px 1px #88888859;
  max-height: 500px;
}
.attainment_table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 6px;
}
.attainment_table td {
  padding: 5px 10px;
  border: 1px solid #dee2e6;
  background: #fff;
  vertical-align: middle;
}
.attainment_table th {
  padding: 0px 10px;
  background: #f9fafb;
  vertical-align: middle;
  border: 1px solid #dee2e6;
  box-shadow: 2px 2px 7px -2px #8888884a;
}
.attainment_table thead th td {
  background: #fff !important;
  color: #000;
  position: sticky;
  top: 0;
  z-index: 1;
}

.attainment_table th:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 2;
}
.attainment_table thead th:first-child {
  z-index: 5;
}
.attainment_table thead tr:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 0px;
}

.attainment_table thead tr:first-child {
  z-index: 5;
}

.attainment_table tbody tr:nth-child(1) {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 47px;
}

.attainment_table tbody tr:nth-child(1) {
  z-index: 5;
}

/* Handle on hover */
.attainment_table::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}
.attainment_table::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

/* Handle */
.attainment_table::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.thHeaderAttainment {
  text-align: center;
  margin-bottom: 0px;
  padding: 6px 0px 6px 0px;
}
.attainment_border_bottom {
  border-bottom: 1px solid #dee2e6 !important;
  border-top: none !important;
  border-right: none !important;
  border-left: none !important;
}
.MuiCheckbox-colorPrimary.Mui-checked {
  color: #147afc !important;
}
.MuiTab-textColorPrimary.Mui-selected {
  color: #2a7afc !important;
}
.w-13 {
  width: 13%;
}
.w-20 {
  width: 20%;
}
.thHeaderReport {
  text-align: center;
  margin-bottom: 0px;
  padding: 6px 0px 6px 0px;
  font-size: 15px;
}
.thHeaderReportStudent {
  text-align: center;
  margin-bottom: 0px;
  padding: 13px 0px 13px 0px;
  font-size: 15px;
}
.cw_300 {
  min-height: 1px;
  width: 300px;
  word-wrap: break-word;
}
.innerValue {
  text-align: center;
  margin-bottom: 0px;
  padding: 2px 8px 2px 8px;
  font-size: 15px;
  border-radius: 4px;
}
.cw_160 {
  min-height: 1px;
  width: 160px;
  word-wrap: break-word;
}
.text-NewWarning {
  color: #d97706;
}
.assessment-planning-text {
  color: #374151;
}
.digi-circle {
  border-radius: 50%;
  width: 26px;
  height: 26px;
  padding: 2px;
  background: #e1f5fa;
  color: #4b5563;
  text-align: center;
  font: 22px;
}
.digi-blue-bg {
  background-color: #147afc;
}
.digi-color-white {
  color: white;
}
.cw_200 {
  min-height: 1px;
  width: 200px;
  word-wrap: break-word;
}
.word_break {
  word-break: break-all !important;
}
select#\#attainmentSelect {
  text-align: center;
  font-weight: 500;
}
.digi-node-bg {
  margin: 8px 4px 0;
  padding: 6px 12px;
  background-color: #eef2ff;
  border-radius: 6px;
  color: #4338ca;
  font-size: 14px;
  text-transform: uppercase;
  cursor: pointer;
}
.digi-evaluation-node-bg {
  display: flex;
  background-color: #d2d4da73;
  border-radius: 4px;
}
.digi-evaluation-root-node {
  margin: 4px;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  border-radius: 4px;
  min-width: 90px;
  text-transform: uppercase;
  color: #4b5563;
  cursor: pointer;
}
.digi-evaluation-root-node.active {
  background-color: #fff;
  color: #147afc;
}
.treenode-tooltip-title {
  background-color: #1f2937;
}
.treenode-tooltip-content {
  background-color: #374151;
}
.treenode-tooltip .MuiTooltip-tooltip {
  padding: 0;
}
.normalInput {
  padding: 9px;
  border-radius: 4px;
}
.normalInput:focus {
  border: 2px solid #147afc;
}
.normalInput:hover {
  border: 1px solid black;
}
.w-128 {
  width: 128px;
}
.AutoCompleteArrow .MuiAutocomplete-popupIndicator {
  color: #147afc !important;
}

.attainment_table_plo {
  position: relative;
  width: 100%;
  z-index: 1;
  margin: auto;
  overflow: auto;
  /* height: auto; */
  overflow-y: scroll;
  border-radius: 6px;
  box-shadow: 0px 3px 7px 1px #88888859;
  min-height: 320px;
  max-height: 350px;
}
.attainment_table_plo table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 6px;
}
.attainment_table_plo td {
  padding: 5px 10px;
  border: 1px solid #dee2e6;
  background: #fff;
  vertical-align: middle;
}

.attainment_table_plo th {
  padding: 0px 10px;
  background: #f9fafb;
  vertical-align: middle;
  border: 1px solid #dee2e6;
  box-shadow: 4px 1px 6px -1px #888888ad;
  position: sticky;
  top: 0;
  z-index: 1;
}
.attainment_table_plo thead th td {
  background: #fff !important;
  color: #000;
  position: sticky;
  top: 0;
  z-index: 1;
}
.attainment_table_plo th:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 2;
}
.attainment_table_plo thead tr:first-child {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 0;
  z-index: 3;
}
.attainment_table_plo tbody tr:nth-child(1) {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 43px;
}

.attainment_table_plo tbody tr:nth-child(1),
.attainment_table_plo tbody tr:nth-child(2) {
  z-index: 5;
}

.attainment_table_plo tbody tr:nth-child(2) {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  top: 78px;
}
.attainment_table_plo thead th:first-child {
  z-index: 5;
}
.attainment_table_plo tbody tr:nth-child(1),
.attainment_table_plo tbody tr:nth-child(2) {
  z-index: 5;
}
/* Handle on hover */
.attainment_table_plo::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}
.attainment_table_plo::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

/* Handle */
.attainment_table_plo::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.black_popUp_chip {
  padding: 4px 12px;
  background-color: #f3f4f6;
  font-weight: 500;
  border-radius: 15px;
}
.chip_overwritten {
  color: #d97706;
}

.overflow-y-lms-report {
  overflow: auto;
  height: 308px;
}
.overflow-y-lms-report_no_data {
  overflow: auto;
  height: 315px;
}

.overflow-y-lms-report::-webkit-scrollbar {
  width: 10px;
}
.overflow-y-lms-report::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.overflow-y-lms-report::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}
.tardis_warning {
  color: #e8ae6b;
}
.white-space-initial {
  white-space: initial;
}

.gray-neutral {
  color: #4b5563 !important;
}

._webkit_fill_available {
  width: -webkit-fill-available;
}

.session_attendance_excluded {
  color: #d97706 !important;
}
.btnVia {
  box-sizing: border-box;
  appearance: none;
  background-color: transparent;
  border: 2px solid;
  border-radius: 0.3em;
  cursor: pointer;
  font-weight: 100;
  line-height: 1;
  padding: 7px 14px;
  text-decoration: none;
  font-size: 16px;
}
.btnVia:hover,
.btnVia:focus {
  color: #5a62c3;
  outline: 0;
}
.ViaTeams {
  border-color: #5a62c3;
  color: #5a62c3;
  background-image: linear-gradient(45deg, #d1d5db2e 50%, transparent 50%);
  background-position: 100%;
  background-size: 400%;
  transition: background 300ms ease-in-out;
}
.ViaTeams:hover {
  background-position: 0;
}

.selected-date{
  color: #ffffff !important;
  border-radius: 7px;
  background-color: #3e95ef;
}

.course-input-table {
  border: 1px solid #dee2e6;
}
.course-input-table thead tr {
  background-color: #F3F4F6;
}
.course-input-table thead th {
  color: #6b7280;
  border-bottom: 0;
}
.course-input-table tbody td {
  vertical-align: middle;
}

.student-grouping-table {
  font-size: 14px !important;
}
.student-grouping-table thead tr,
.student-grouping-table tbody tr:nth-of-type(even) {
  background-color: #F3F4F6;
}
.student-grouping-table thead th {
  color: #6B7280;
  border: 0;
  vertical-align: middle;
}
.student-grouping-table tbody td {
  color: #374151;
  border: 0;
  vertical-align: middle;
}
.student-grouping-table tbody tr.selected {
  border: 1px solid #0064C8;
  background-color: #F7FCFD;
}
.student-grouping-table.import-table thead th,
.student-grouping-table.import-table tbody td {
  padding: 8px 12px;
}
.student-grouping-table.import-table tbody tr.selected {
  border: 1px solid #53AEF9;
}

.delivery-groups-table {
  font-size: 14px !important;
}
.delivery-groups-table thead tr {
  background-color: #F3F4F6;
}
.delivery-groups-table thead th {
  padding: 7px 12px;
  color: #6B7280;
  border: 0;
  vertical-align: middle;
}
.delivery-groups-table tbody td {
  padding: 6px 12px;
  color: #374151;
  border: 0;
  vertical-align: middle;
}

.text-primary-da {
  color: #0064C8;
}

.delete-confirm-warning-img {
  margin-top: -4px;
}

.schedule-issues-table thead tr {
  background-color: #E5E7EB;
}
.schedule-issues-table thead th,
.schedule-issues-table tbody td {
  color: #374151;
  border: 0;
  vertical-align: middle;
}
.schedule-issues-table thead th {
  padding: 9.5px 12px;
}
.schedule-issues-table tbody td {
  padding: 4px 12px;
  font-size: 14px;
}
.schedule-issues-table tbody tr:not(:last-child) {
  border-bottom: 1px solid #E5E7EB;
}

.analyzing-issues-table {
  font-size: 14px !important;
}
.analyzing-issues-table thead tr {
  background-color: #F3F4F6;
  border: 1px solid #F3F4F6;
}
.analyzing-issues-table thead th {
  color: #6B7280;
  border: 0;
  vertical-align: middle;
}
.analyzing-issues-table tbody tr {
  border: 1px solid #E5E7EB;
  border-top: 0;
}
.analyzing-issues-table tbody td {
  color: #374151;
  border: 0;
}
.analyzing-issues-table thead th {
  padding: 8px 12px;
}
.analyzing-issues-table tbody td {
  padding: 10px 12px;
}