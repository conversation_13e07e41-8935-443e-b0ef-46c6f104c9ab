import React, { useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import CkEditor5 from 'Widgets/CkEditor/CkEditor';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import PriorityHighRoundedIcon from '@mui/icons-material/PriorityHighRounded';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import DownloadIcon from '@mui/icons-material/Download';
import CircleIcon from '@mui/icons-material/Circle';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import {
  Drawer,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
  Box,
  Menu,
  MenuItem,
  Button,
  FormControl,
  Select,
  Popover,
  // OutlinedInput,
  // InputAdornment,
  Divider,
  Modal,
  Avatar,
  Accordion,
  AccordionDetails,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  DialogContentText,
  DialogActions,
  Tooltip,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { fromJS, List, Map } from 'immutable';
import Badge from '@mui/material/Badge';
import CommentIcon from '@mui/icons-material/Comment';
// import AttachFileIcon from '@mui/icons-material/AttachFile';
import SendIcon from '@mui/icons-material/Send';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import {
  // useBooleanHook,
  useCallApiHook,
  useInputHook,
  useSearchParams,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import VerifiedRoundedIcon from '@mui/icons-material/VerifiedRounded';
import { useSelector } from 'react-redux';
import { selectSelectedRole } from '_reduxapi/Common/Selectors';
import {
  addComment,
  getFormInitiatorUserList,
  getSignedUrl,
  getTodoMissedData,
  // multipleFileUpload,
  putSendEmailToFormInitiator,
} from '_reduxapi/q360/actions';
import { selectFromApplicationList, selectTodoMissedData } from '_reduxapi/q360/selectors';
import { InfoOutlined } from '@mui/icons-material';
import { setData } from '_reduxapi/qapc/actions';
import SanitizedHTML from 'Shared/SanitizedHTML';
import useFileUpload from 'Hooks/useFileUpload';
/*----------------------------------UtilsSxStart------------------------------------*/
const paperSx = {
  width: 30,
  height: 30,
  borderRadius: '50%',
  padding: '3px',
  right: -15,
  bottom: -27,
  zIndex: 2,
};
const commandPopSx = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '45%',
};
export const muiPaperSx = { '& .MuiDrawer-paper': { overflow: 'visible', width: '78vw' } };
const forwardSx = { backgroundColor: '#DCFCE7', color: '#16A34A', width: '200px' };
const buttonSx = {
  backgroundColor: '#FF8080',
  color: '#ffffff',
  padding: '7px 0px',
  textTransform: 'uppercase',
};
const rejectSx = {
  backgroundColor: '#FEE2E2',
  color: '#DC2626',
  width: '200px',
};
const reSubmitSx = {
  backgroundColor: '#E0E7FF',
  color: '#4F46E5',
  width: '200px',
};
const badgeSx = {
  width: '20px',
  height: '20px',
  borderRadius: '50%',
  backgroundColor: '#F3F4F6',
  color: '#4B5563',
  paddingTop: '3px',
};
const badgeCommentSx = {
  '& .MuiBadge-badge': {
    backgroundColor: '#374151',
    width: '15px',
    height: '15px',
    fontSize: '7px',
    minWidth: 0,
  },
};
const anchorOrigin = {
  vertical: 'bottom',
  horizontal: 'right',
};
const transformOrigin = {
  vertical: 'top',
  horizontal: 'right',
};
// const SearchIconSx = { color: '#9CA3AF', fontSize: '16px' };
// const SearchInputSx = (reply) => {
//   return {
//     width: '100%',
//     height: '45px',
//     fontSize: '12px',
//     background: '#F3F4F6',
//     ...(reply && { borderTopLeftRadius: 0, borderTopRightRadius: 0 }),
//     '& .MuiInputBase-input::placeholder': {
//       color: '#9CA3AF',
//       opacity: 1,
//     },
//     '& .MuiInputBase-input': {
//       color: '#9CA3AF',
//       fontSize: 12,
//     },
//     '& .MuiSvgIcon-root': {
//       fontSize: '16px',
//       color: '#6B7280',
//     },
//     '& .MuiOutlinedInput-notchedOutline': {
//       border: 'none',
//     },
//     '&:hover .MuiOutlinedInput-notchedOutline': {
//       border: 'none',
//     },
//     '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
//       border: 'none',
//     },
//   };
// };

const commandsPopStyle = { border: '2px solid #9e9e9e', padding: '10px 10px 30px' };
const commandsPopCancelbtn = {
  ...buttonSx,
  backgroundColor: 'white',
  border: '1px solid #9e9e9e',
  padding: '5px 25px',
  color: 'black',
  '&:hover': {
    backgroundColor: 'transparent',
  },
};
const commandsPopSubmitBtn = {
  ...buttonSx,
  padding: '5px 25px',
  color: 'white',
  '&:hover': {
    backgroundColor: 'transparent',
  },
};
const textAreaStyle = { border: '2px solid #9e9e9e', padding: '10px 10px 30px' };
const rejectCancelBtn = {
  ...buttonSx,
  backgroundColor: 'white',
  border: '1px solid #9e9e9e',
  padding: '5px 25px',
  color: 'black',
  '&:hover': {
    backgroundColor: 'transparent',
  },
};
const rejectSubmitBtn = {
  ...buttonSx,
  padding: '5px 25px',
  color: 'white',
  '&:hover': {
    backgroundColor: 'transparent',
  },
};
const selectedStyle = { color: '#9CA3AF', fontWeight: 400 };
const accordionDefault = {
  width: '55vh',
  writingMode: 'horizontal-tb',
  transition: '0.5s',
  background: '#FEE2E2',
};
const accordionRotate = {
  height: '40vh',
  width: '7vh',
  alignSelf: 'center',
  writingMode: 'vertical-lr',
  transition: '0.5s',
  display: 'flex',
  justifyContent: 'center',
  flexDirection: 'row-reverse',
  background: '#FEE2E2',
};
const accordionDefaultTodo = {
  width: '55vh',
  writingMode: 'horizontal-tb',
  transition: '0.5s',
  background: '#FEF3C7',
};
const accordionRotateTodo = {
  height: '40vh',
  width: '7vh',
  writingMode: 'vertical-rl',
  transition: '0.5s',
  display: 'flex',
  justifyContent: 'center',
  flexDirection: 'row-reverse',
  background: '#FEF3C7',
};
const rotate180 = { transform: 'rotate(180deg)' };
const menuItemStyles = {
  fontSize: '12px',
  '&.MuiMenuItem-root': {
    minHeight: 'auto',
  },
};

const checkboxStyles = {
  '& .MuiSvgIcon-root': { fontSize: 16 },
};
const MenuProps = {
  PaperProps: {
    sx: {
      boxShadow: 1,
      maxHeight: 200,
      '&::-webkit-scrollbar': {
        width: '8px',
        height: '10px',
      },
      '&::-webkit-scrollbar-track': {
        background: '#f1f1f1',
        borderRadius: '15px',
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#c8c8c8',
        borderRadius: '15px',
      },
    },
  },
};
/*----------------------------------UtilsSxEnd---------------------------------------*/

export const BadgeText = ({ text, count, id, children, paper, handleClose }) => {
  const color = { color: '#4B5563' };
  return (
    <>
      <div className="d-flex text-uppercase gap-8 align-items-center ">
        <div
          className="f-10 rounded fw-500 px-2 py-1 l-blue d-flex justify-content-center align-items-center text-grey box-shadow-static"
          style={color}
        >
          {text}
        </div>
        <div
          className="f-10 d-flex justify-content-center  align-items-center mr-2 fw-500 box-shadow-static"
          style={badgeSx}
        >
          +{count}
        </div>
      </div>
    </>
  );
};
export const ApprovalStatus = ({
  text,
  sx,
  children,
  Icon,
  handleUpdate,
  isEditStatus,
  disabled,
  isReSubmit,
  hanldeReSubmit,
}) => {
  const [input, handleChange, setInitialValue] = useInputHook();
  const [error, setError] = useState('');
  const [paper, setPaper] = useState(Boolean(null));
  const [opens, setOpens] = useState(false);
  const handleClickReSubmit = (event) => {
    event.stopPropagation();
    setOpens(true);
  };
  const handleChangeInput = (e) => {
    handleChange(e);
    setError('');
  };
  const handleClosePop = (e) => () => {
    e.stopPropagation();
    setOpens(false);
    handleClose(e);
  };
  const open = Boolean(paper);
  const handleClick = (event) => {
    event.stopPropagation();
    setPaper(event.currentTarget);
  };
  const handleClose = (e) => {
    e.stopPropagation();
    setPaper(null);
    setInitialValue();
  };
  const handleValidate = (e) => {
    e.stopPropagation();
    e.preventDefault();
    setError('* Enter Re-Submit Reason');
  };
  return (
    <div className="d-flex justify-content-between gap-10 align-items-center  ">
      {children ? (
        children
      ) : (
        <div
          style={sx}
          className="text-uppercase flex-grow-1 rounded py-2    text-center fw-500 d-flex justify-content-center align-items-center"
        >
          {Icon && <Icon className="f-18 mx-2" />} {text}
        </div>
      )}
      {(isEditStatus || isReSubmit) && (
        <MoreVertIcon
          className={`f-16 ml-1 ${!disabled && 'cursor-pointer'}`}
          onClick={disabled ? () => {} : handleClick}
        />
      )}
      <Menu
        id="basic-menu"
        anchorEl={paper}
        open={open}
        onClose={handleClose}
        anchorOrigin={anchorOrigin}
        transformOrigin={transformOrigin}
      >
        {isEditStatus && (
          <MenuItem className="f-14" onClick={handleUpdate}>
            Revoke
          </MenuItem>
        )}
        {isReSubmit && (
          <MenuItem className="f-14" onClick={handleClickReSubmit}>
            Re-Submit
          </MenuItem>
        )}
      </Menu>
      <CommandsPop handleClose={(e) => handleClosePop(e)()} open={opens}>
        <Box className="bg-white rounded" sx={commandPopSx}>
          <div className="fw-500 py-3 px-4 f-17">Re - Submit</div>
          <Divider />
          <div className="my-3 mx-4">
            <div className="fw-400 text-black">Give Reason</div>
            <textarea
              className="w-100 rounded"
              value={input}
              onChange={handleChangeInput}
              placeholder="Type here..."
              style={commandsPopStyle}
            />
            {error && <div className="f-14 text-danger fw-500">{error}</div>}
          </div>
          <Divider />
          <footer className="d-flex justify-content-end pb-2 gap-10 mr-4 mt-2">
            <Button
              className="text-uppercase f-14"
              onClick={(e) => handleClosePop(e)()}
              sx={commandsPopCancelbtn}
            >
              CANCEL
            </Button>
            <Button
              className="text-uppercase f-14 bg-primary"
              onClick={(e) =>
                input.trim() ? hanldeReSubmit(e, input, handleClosePop(e)) : handleValidate(e)
              }
              sx={commandsPopSubmitBtn}
            >
              SUBMIT
            </Button>
          </footer>
        </Box>
      </CommandsPop>
    </div>
  );
};
export const CustomButton = ({
  btnText,
  click = () => {},
  bgColor = '#FF8080',
  textColor = 'white',
  padding,
  disabled = false,
}) => {
  const [input, handleChange, setInitialValue] = useInputHook();
  const [error, setError] = useState('');
  const [paper, setPaper] = useState(false);
  const handleClick = (event) => {
    event.stopPropagation();
    setPaper(true);
  };
  const handleChangeInput = (e) => {
    handleChange(e);
    setError('');
  };
  const handleClose = (e) => {
    e && e.stopPropagation();
    setPaper(false);
    setInitialValue();
  };
  const handleValidate = (e) => {
    e.stopPropagation();
    e.preventDefault();
    setError('* Enter Rejection Reason');
  };
  return (
    <>
      <Button
        className="text-uppercase f-14 "
        onClick={btnText === 'reject' || btnText === 'skip & reject' ? handleClick : click}
        disabled={disabled}
        sx={{
          ...buttonSx,
          backgroundColor: bgColor,
          color: textColor,
          '&:hover': {
            backgroundColor: bgColor,
          },
        }}
      >
        <span style={{ padding }}>{btnText}</span>
      </Button>
      <CommandsPop handleClose={handleClose} open={paper}>
        <Box className="bg-white rounded" sx={commandPopSx}>
          <div className="fw-500 py-3 px-4 f-17">Rejection</div>
          <Divider />
          <div className="my-3 mx-4">
            <div className="fw-400 text-black">Give Reason</div>
            <textarea
              className="w-100 rounded"
              value={input}
              onChange={handleChangeInput}
              placeholder="Type here..."
              style={textAreaStyle}
            />
            {error && <div className="f-14 text-danger fw-500">{error}</div>}
          </div>
          <Divider />
          <footer className="d-flex justify-content-end pb-2 gap-10 mr-4 mt-2">
            <Button className="text-uppercase f-14" onClick={handleClose} sx={rejectCancelBtn}>
              CANCEL
            </Button>
            <Button
              className="text-uppercase f-14 bg-primary"
              onClick={(e) => (input.trim() ? click(e, input, handleClose) : handleValidate(e))}
              sx={rejectSubmitBtn}
            >
              SUBMIT
            </Button>
          </footer>
        </Box>
      </CommandsPop>
    </>
  );
};
export const DrawerRight = ({ open, handleClose }) => {
  return (
    <Drawer anchor="right" open={open} className="rightDrawer" sx={muiPaperSx}>
      <div className="close-btn" onClick={handleClose}>
        <CloseIcon />
        <div className="close-btn-bottom"></div>
      </div>
      <div className="border-bottom f-18 py-2 px-2 ">
        <div className="f-18 fw-500">Change Log For Re - SubMission</div>
        <div className="f-12 text-gray">
          Fundamentals of Human Body Curriculum / Program Name 01 / All Groups
        </div>
      </div>
      <Box
        className="py-4 g-config-scrollbar more-details-accordion-scroll px-3 "
        sx={{ background: '#f9fafb' }}
      >
        <Table>
          <TableHead className="fixed-header">
            <TableRow className="bg-white">
              <TableCell>
                <div className="f-14 text-black fw-500">Approval Level</div>
              </TableCell>
              <TableCell>
                <div className="f-14 text-black fw-500">Changes Given By</div>
              </TableCell>
              <TableCell sx={{ width: '300px' }}>
                <div className="f-14 text-black fw-500">Action</div>
              </TableCell>
              <TableCell>
                <div className="f-14 text-black fw-500">Changed By</div>
              </TableCell>
              <TableCell>
                <div className="f-14 text-black fw-500">Time</div>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow className="bg-transparent" sx={{ '& td': { borderBottom: 'none' } }}>
              <TableCell>
                <div className="f-14 text-black fw-500">Today - 31st Jan 2023</div>
              </TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 1</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell>
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>
              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 2</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell className="">
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>

              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 3</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell>
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>

              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 4</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell>
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>
              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
            <TableRow sx={{ '& td': { borderBottom: 'none' } }}>
              <TableCell>
                <div className="f-14 text-black fw-500">25th Jan 2023</div>
              </TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 4</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell>
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>

              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 4</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell>
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>

              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 4</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell>
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>
              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell>Level 4</TableCell>
              <TableCell>SP021 - Dr. Steve Joseph</TableCell>
              <TableCell>
                <div className="p-2 rounded bg-grey">
                  <div>
                    Reason will be display here Reason will e...
                    <span className="f-12 text-primary text_underline">Show More</span>
                  </div>
                  <div className="text-gray">Latest - 28/02/2022 at 10:50 AM</div>
                </div>
              </TableCell>
              <TableCell>SP021 - Dr. Pall Joseph</TableCell>
              <TableCell>12:05 PM</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Box>
    </Drawer>
  );
};
export const ApprovalMultiSelect = ({
  value = '',
  onChange,
  sx,
  disabled = false,
  defaultValue,
  placeholder,
  options = Map(),
  labelclass,
  StartIcon,
  multiSelect = false,
  clearAll = true,
}) => {
  return (
    <FormControl>
      <Select
        className={labelclass}
        value={value}
        sx={sx}
        onChange={onChange}
        disabled={disabled}
        placeholder={placeholder}
        defaultValue={defaultValue}
        renderValue={(selected) => (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {StartIcon && <StartIcon className="f-12" sx={{ color: '#9CA3AF' }} />}
            {selected ? (
              options.getIn([selected, 'label'], '')
            ) : (
              <div style={selectedStyle}>{placeholder}</div>
            )}
          </Box>
        )}
        displayEmpty
        MenuProps={{
          PaperProps: {
            sx: {},
          },
        }}
      >
        {placeholder && (
          <MenuItem value="" disabled={clearAll} className="f-14 approval-menuItem-selected">
            All
          </MenuItem>
        )}
        {options.map((option) => {
          const label = option.get('label', '');
          const value = option.get('value', '');
          return (
            <MenuItem key={value} value={value} className="f-14">
              {label}
            </MenuItem>
          );
        })}
      </Select>
    </FormControl>
  );
};
export const MessagePopup = ({ formDetails, resubmissionLog, addComment, level }) => {
  const [signedUrl] = useCallApiHook(getSignedUrl);
  const { programName, courseName } = formDetails;
  const [paper, setPaper] = useState(Boolean(null));
  // const [reply, setReply] = useBooleanHook(false);
  // const [replyMessage, setReplyMessage] = useState(fromJS({}));
  // const repMessage = replyMessage.get('message', '');
  const open = Boolean(paper);
  const handleClick = (event) => {
    event.stopPropagation();
    setPaper(event.currentTarget);
  };
  const handleClose = (e) => {
    e.stopPropagation();
    setPaper(null);
  };
  // const handleCloseReply = () => {
  //   setReply();
  //   if (reply) setReplyMessage(fromJS({}));
  //   else
  //     setReplyMessage(
  //       fromJS({
  //         message:
  //           'It is long established fact that a reader will be distracted by the readable content of a page when looking at its layout...',
  //       })
  //     );
  // };
  const id = open ? 'simple-popover' : undefined;
  let constructAddCommand = fromJS({});
  addComment.forEach((command) => {
    const name = command.getIn(['userId', 'name'], Map());
    const _id = command.get('_id', '');
    const firstName = name.get('first', '');
    const lastName = name.get('last', '');
    const levelNumber = command.get('level', 0);
    const details = { type: 'command', message: command.get('comment', ''), levelNumber, level };
    const attachments = command.get('attachments', List());
    const timeStamp = '';
    constructAddCommand = constructAddCommand.set(
      _id,
      fromJS({
        name: `${firstName}${lastName}`,
        details,
        timeStamp,
        attachments,
      })
    );
  });
  let constructReSumission = fromJS({});
  resubmissionLog.forEach((reSubmit) => {
    const firstName = reSubmit.getIn(['userId', 'name', 'first'], '');
    const lastName = reSubmit.getIn(['userId', 'name', 'last'], '');
    const levelNumber = reSubmit.get('level', 0);
    const reason = reSubmit.get('reason', '');
    const _id = reSubmit.get('_id', '');
    const details = { type: 're-submit', message: reason, levelNumber, level };
    const timeStamp = '';
    constructReSumission = constructReSumission.set(
      _id,
      fromJS({
        name: `${firstName}${lastName}`,
        details,
        timeStamp,
      })
    );
  });
  const callBack = async (signedUrl) => {
    const image = await fetch(signedUrl);
    const imageBlog = await image.blob();
    const imageURL = URL.createObjectURL(imageBlog);
    const link = document.createElement('a');
    link.href = imageURL;
    link.download = 'imagefile';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  const openSignedUrl = (url) => signedUrl(url, callBack);
  return (
    <>
      <Badge
        className="text-white cursor-pointer"
        sx={badgeCommentSx}
        id={id}
        onClick={handleClick}
      >
        <CommentIcon className="text-light-blue" />
      </Badge>
      <Popover
        id={id}
        open={open}
        anchorEl={paper}
        onClose={handleClose}
        onClick={(e) => e.stopPropagation()}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            width: 350,
          },
        }}
      >
        <Box sx={{ background: '#EFF9FB' }}>
          <div className="d-flex justify-content-between p-3">
            <div>
              <div className="f-18 fw-400">{programName}</div>
              <div className="f-12 text-gray">{courseName}</div>
            </div>
            <CloseIcon onClick={handleClose} sx={{ fontSize: 20 }} className="cursor-pointer" />
          </div>
          <Box
            className="bg-white rounded-top p-3 d-flex flex-column  gap-10 q360-approval-message-chat g-config-scrollbar"
            sx={{ height: 300 }}
          >
            {constructAddCommand.entrySeq().map(([_, value], index) => {
              const name = value.get('name', '');
              const details = value.get('details', '');
              const attachments = value.get('attachments', List());
              const message = details.get('message', '');
              const levelNumber = details.get('levelNumber', '');
              const level = details.get('level', '');
              const timeStamp = value.get('timeStamp', '');
              return (
                <div key={index} className="d-flex flex-column">
                  <div>
                    <div className={`fw-500 f-16 `}>{name}</div>
                    <div className={`text-uppercase  text-gray f-12 `}>
                      {level} <CircleIcon sx={{ fontSize: 6, marginBottom: '1px' }} /> Level{' '}
                      {levelNumber}
                    </div>
                  </div>
                  <div
                    className={`p-1 px-2 f-14 bg-grey rounded my-1 align-self-start comment-ckEditor-width`}
                  >
                    <div className="ck-editor-content break-word-CkEditor-comment">
                      <SanitizedHTML content={message} />
                    </div>
                  </div>
                  <div className={`d-flex justify-content-between `}>
                    {/* {restriction && (
                      <div className="text_underline text-primary f-12" onClick={handleCloseReply}>
                        Reply
                      </div>
                    )} */}
                    <div className="f-12 ">{timeStamp}</div>
                  </div>
                  {attachments.map((file, index) => (
                    <Box
                      className={`rounded border f-14 px-1 py-2 d-flex justify-content-around align-items-center mt-1  cursor-pointer  align-self-start gap-10 px-2`}
                      onClick={() => openSignedUrl(file.get('url', ''))}
                      key={index}
                    >
                      <div>{file.get('name', '')}</div>
                      <DownloadIcon sx={{ fontSize: 17 }} className="text-grey " />
                    </Box>
                  ))}
                </div>
              );
            })}
            {constructReSumission.entrySeq().map(([_, value], index) => {
              const name = value.get('name', '');
              const details = value.get('details', '');
              const levelNumber = details.get('levelNumber', '');
              const level = details.get('level', '');
              const message = details.get('message', '');
              const timeStamp = value.get('timeStamp', '');
              return (
                <div key={index} className="d-flex flex-column">
                  <div>
                    <div
                      className={`fw-500 f-16 d-flex align-items-center justify-content-between gap-5`}
                    >
                      {name}{' '}
                      <div className="badge bg-resubmit rounded">
                        <div className="f-10 text-white">RE - SUBMIT</div>
                      </div>
                    </div>
                    <div className={`text-uppercase text-gray f-12 `}>
                      {level} <CircleIcon sx={{ fontSize: 6, marginBottom: '1px' }} /> Level{' '}
                      {levelNumber}
                    </div>
                  </div>
                  <div className={`p-1 px-2 f-14 bg-grey rounded my-1 align-self-start`}>
                    <SanitizedHTML content={message} />
                  </div>
                  <div className={`d-flex justify-content-between `}>
                    {/* {restriction && (
                  <div className="text_underline text-primary f-12" onClick={handleCloseReply}>
                    Reply
                  </div>
                )} */}
                    <div className="f-12 ">{timeStamp}</div>
                  </div>
                </div>
              );
            })}
          </Box>
          {/* <footer className="pt-2  px-2 bg-white q360-message-input">
            <div
              className={`q360-message-reply p-1 ${reply ? 'rounded-top' : 'rounded'}`}
              style={{
                '--data-': reply ? '45px' : '0px',
              }}
            >
              <div className="rounded p-1 inner-reply-message">
                <div className="d-flex justify-content-between">
                  <div className="f-12 fw-500 text-gray">You</div>
                  <CloseIcon
                    sx={{ fontSize: 14 }}
                    className="cursor-pointer"
                    onClick={handleCloseReply}
                  />
                </div>
                <div className="f-12 fw-400">{repMessage}</div>
              </div>
            </div>
            <OutlinedInput
              placeholder="Reply Here"
              startAdornment={
                <InputAdornment position="start">
                  <AttachFileIcon sx={SearchIconSx} />
                </InputAdornment>
              }
              endAdornment={
                <InputAdornment position="start">
                  <SendIcon sx={SearchIconSx} />
                </InputAdornment>
              }
              inputProps={{
                'aria-label': 'weight',
              }}
              sx={SearchInputSx(reply)}
            />
          </footer> */}
        </Box>
      </Popover>
    </>
  );
};
export const DisplayButtons = ({
  userStatus,
  toSkip,
  handleUpdate,
  isLastUser,
  isPublished,
  buttonDisabled,
  isEditStatus,
  isHistory,
  reDireact = false,
}) => {
  const isReSubmit = userStatus !== 're_submit' && userStatus === 'pending' && !isHistory;
  const constructBtnText = {
    'true+false': 'approve',
    'true+true': 'publish',
    'false+false': 'forWard',
    'false+true': 'forWard',
  };
  const constructApiPayload = {
    'true+false': 'Approved',
    'true+true': 'published',
    'false+false': 'Forwarded',
    'false+true': 'Forwarded',
  };
  const DynamicCompound = ({ text, sx, Icon }) => {
    return (
      <ApprovalStatus
        handleUpdate={handleUpdate('revoked', reDireact)}
        hanldeReSubmit={handleUpdate('re_submit', reDireact)}
        text={text}
        sx={sx}
        Icon={Icon}
        isEditStatus={isEditStatus}
        disabled={buttonDisabled}
        isReSubmit={isReSubmit}
      />
    );
  };
  const PendingCompound = () => (
    <ApprovalStatus
      handleUpdate={handleUpdate('revoked', reDireact)}
      hanldeReSubmit={handleUpdate('re_submit', reDireact)}
      isEditStatus={isEditStatus}
      disabled={buttonDisabled}
      isReSubmit={isReSubmit}
    >
      <div className="d-flex align-items-center gap-5">
        <CustomButton
          padding="2px 27px"
          btnText="reject"
          click={handleUpdate('Rejected', reDireact)}
          disabled={buttonDisabled}
        />
        <CustomButton
          btnText={constructBtnText[`${isLastUser}+${isPublished}`]}
          bgColor="#88c3af"
          textColor="black"
          padding="2px 17px"
          click={handleUpdate(constructApiPayload[`${isLastUser}+${isPublished}`], reDireact)}
          disabled={buttonDisabled}
        />
      </div>
    </ApprovalStatus>
  );

  const NotInitiator = () => (
    <ApprovalStatus
      handleUpdate={handleUpdate('revoked', reDireact)}
      hanldeReSubmit={handleUpdate('re_submit', reDireact)}
      isEditStatus={isEditStatus}
      disabled={buttonDisabled}
      isReSubmit={isReSubmit}
    >
      <div className="d-flex align-items-center gap-5">
        <CustomButton
          padding="2px 27px"
          btnText={'skip & reject'}
          click={handleUpdate('Rejected', reDireact)}
          disabled={buttonDisabled}
        />
        <CustomButton
          btnText={`skip & ${constructBtnText[`${isLastUser}+${isPublished}`]}`}
          bgColor="#88c3af"
          textColor="black"
          padding="2px 17px"
          click={handleUpdate(constructApiPayload[`${isLastUser}+${isPublished}`], reDireact)}
          disabled={buttonDisabled}
        />
      </div>
    </ApprovalStatus>
  );
  const compounds = {
    pending: toSkip ? <NotInitiator /> : <PendingCompound />,
    Forwarded: <DynamicCompound text="forwarded" sx={forwardSx} Icon={VerifiedRoundedIcon} />,
    Rejected: <DynamicCompound text="rejected" sx={rejectSx} />,
    'Not Initiated': toSkip ? <NotInitiator /> : <PendingCompound />,
    'Not Applicable': <></>,
    Delayed: <></>,
    Approved: <DynamicCompound text="Approved" sx={forwardSx} Icon={VerifiedRoundedIcon} />,
    published: <DynamicCompound text="Published" sx={forwardSx} Icon={VerifiedRoundedIcon} />,
    re_submit: <DynamicCompound text="re-submit" sx={reSubmitSx} Icon={PriorityHighRoundedIcon} />,
  };
  return <>{compounds[userStatus]}</>;
};
export const CommandsPop = ({ handleClose, open, children }) => {
  return (
    <Modal
      onClick={(e) => e.stopPropagation()}
      open={open}
      onClose={handleClose}
      aria-labelledby="child-modal-title"
      aria-describedby="child-modal-description"
    >
      {children}
    </Modal>
  );
};

export const SideBar = ({ tab, selectedCalendar }) => {
  const [groupId, setGroupId] = useState('');
  const [todoMissed] = useCallApiHook(getTodoMissedData);
  const selectedRole = useSelector(selectSelectedRole);
  const todoMissedData = useSelector(selectTodoMissedData);
  const [rotate, setRotate] = useState(false);
  const [open, setOpen] = useState(Map({ 0: true, 1: true }));
  const [opens, setOpens] = useState(false);
  const missedList = todoMissedData.get('missedList', List());
  const todoList = todoMissedData.get('toDoList', List());
  const categoryList = todoMissedData.get('categoryData', List());
  const groupByTodo = todoList.groupBy((todo) => todo.get('categoryId', ''));
  const groupByMissed = missedList.groupBy((missed) => missed.get('categoryId', ''));
  let groupByCategory = Map();
  categoryList.forEach((category) => {
    const _id = category.get('_id', '');
    groupByCategory = groupByCategory.set(_id, category);
  });
  const categoryName = groupByCategory.getIn([tab, 'categoryName'], '');
  const level = groupByCategory.getIn([tab, 'level'], '');
  const missedAccordion = open.get('0', false);
  const toDoAccordion = open.get('1', false);
  const handleClick = (event, groupId) => {
    event.stopPropagation();
    setOpens(true);
    setGroupId(groupId);
  };
  const handleClosePop = (e) => {
    e.stopPropagation();
    setOpens(false);
  };
  const handleSetTimeOut = (func) => setTimeout(() => func(), 400);
  const handleOpenRotate = (value) => {
    const callBack = () => setOpen((pre) => pre.set(value, false));
    handleSetTimeOut(callBack);
    setRotate(!rotate);
  };
  const openOrCloseAll = () => {
    if (!rotate) {
      const callBack = () => setOpen((pre) => pre.set('0', rotate).set('1', rotate));
      handleSetTimeOut(callBack);
      setRotate(!rotate);
    } else {
      const callBack = () => setRotate(!rotate);
      handleSetTimeOut(callBack);
      setOpen((pre) => pre.set('0', rotate).set('1', rotate));
    }
  };
  useEffect(() => {
    const payload = { institutionCalenderId: selectedCalendar, subModuleType: 'Form Approver' };
    if (selectedCalendar) todoMissed(payload);
  }, [selectedCalendar, selectedRole]);
  useEffect(() => {
    if (!!missedAccordion && !!toDoAccordion) {
      const callBack = () => setRotate(false);
      handleSetTimeOut(callBack);
    }
  }, [open]);
  return (
    <div className="py-3 main  pr-2">
      <div className="position-relative">
        <Accordion
          className=" box-shadow-static overflow-hidden"
          expanded={!missedAccordion}
          sx={{
            '&.MuiAccordion-root': {
              marginBottom: '0px !important',
            },
          }}
          style={{
            ...(rotate ? accordionDefault : accordionRotate),
          }}
        >
          {rotate ? (
            <div className="d-flex align-items-center justify-content-between gap-15 p-2 ">
              <div>
                <div className="fw-500 text-danger">Missed!</div>
                <div className="fw-500 f-14" style={{ width: 210 }}>
                  {categoryName && `${categoryName} - Pending`}
                </div>
              </div>
              <div>
                <div
                  className="rounded bg-white fw-500 text-center d-flex align-items-center justify-content-center"
                  style={{ width: 40, height: 40 }}
                >
                  {String(groupByMissed.get(tab, List()).size).padStart(2, '0')}
                </div>
              </div>
              <ArrowDropDownIcon
                className="text-black cursor-pointer f-20"
                onClick={() => setOpen((pre) => pre.set('0', !missedAccordion))}
              />
            </div>
          ) : (
            <div className="d-flex justify-content-between flex-row-reverse flex-grow-1 align-items-center p-2">
              <div className="fw-500 text-danger" style={rotate180}>
                Missed! ({String(groupByMissed.get(tab, List()).size).padStart(2, '0')})
              </div>
              <ArrowRightIcon
                className="text-black cursor-pointer f-20"
                onClick={() => handleOpenRotate('0')}
              />
            </div>
          )}
          <AccordionDetails className="bg-white">
            {level && (
              <div className="d-flex justify-content-between align-items-center border-bottom py-1">
                <div className="f-14 text-capitalize">{level} List</div>
                {/* <div className="f-12 text_underline fw-500 approval-view-all">View All</div> */}
              </div>
            )}
            {groupByMissed.get(tab, List()).size === 0 && (
              <div className="text-grey text-center f-12 mt-1">! No Data</div>
            )}
            <div className="custom-scroll g-config-scrollbar">
              {groupByMissed.get(tab, List()).map((item, index) => {
                const courseName = item.get('courseName', '');
                const programName = item.get('programName', '');
                const categoryFormGroupId = item.get('categoryFormGroupId', '');
                const institutionName = item.get('institutionName', '');
                const condition = groupByMissed.get(tab, List()).size - 1 !== index;
                const actions = item.get('actions', Map());
                const isTerm = actions.get('academicTerms', false);
                const isAttemptType = actions.get('attemptType', false);
                const isGroup = actions.get('studentGroups', false);
                const term = item.get('term', '');
                const attemptType = item.get('attemptTypeName', '');
                const group = item.get('groupName', '');
                const curriculumName = item.get('curriculumName', '');
                const checkLevel = {
                  course: courseName,
                  program: programName,
                  institution: institutionName,
                };
                return (
                  <div
                    className={`d-flex justify-content-between align-items-center ${
                      condition && 'border-bottom'
                    } py-1`}
                    key={index}
                  >
                    <div>
                      <div className="f-14 fw-500">{checkLevel[level]}</div>
                      <div className="f-12 text-grey d-flex gap-5">
                        <div className="f-12 text-grey d-flex gap-5">
                          {curriculumName && (
                            <div className="d-flex align-items-center f-12 text-grey">
                              <div className="text-nowrap ">
                                {getShortString(`${curriculumName.trim()}`, 7)}
                              </div>
                              <FiberManualRecordIcon sx={{ fontSize: 5 }} className="ml-1" />
                            </div>
                          )}
                          {isTerm && (
                            <div className="d-flex align-items-center f-12 text-grey">
                              <div className="text-nowrap">
                                {getShortString(`${term.trim()} Term`, 5)}
                              </div>
                              <FiberManualRecordIcon sx={{ fontSize: 5 }} className="ml-1" />
                            </div>
                          )}
                          {isAttemptType && (
                            <div className="d-flex align-items-center f-12 text-grey">
                              <div className="text-nowrap ">
                                {getShortString(`${attemptType.trim()} AttemptType`, 7)}
                              </div>
                              <FiberManualRecordIcon sx={{ fontSize: 5 }} className="ml-1" />
                            </div>
                          )}
                          {isGroup && (
                            <div className="d-flex align-items-center f-12 text-grey">
                              <div className="f-12 text-nowrap">
                                {getShortString(`${group.trim()} group`, 7)}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div
                      className="f-12 text-primary fw-500"
                      onClick={(e) => handleClick(e, categoryFormGroupId)}
                    >
                      Action
                    </div>
                  </div>
                );
              })}
            </div>
          </AccordionDetails>
        </Accordion>
        <Paper className="cursor-pointer position-absolute" onClick={openOrCloseAll} sx={paperSx}>
          {rotate ? (
            <KeyboardArrowLeftIcon className="text-gray" />
          ) : (
            <KeyboardArrowRightIcon className="text-gray" />
          )}
        </Paper>
      </div>
      <Accordion
        className=" box-shadow-static overflow-hidden"
        expanded={!toDoAccordion}
        style={{
          ...(rotate ? accordionDefaultTodo : accordionRotateTodo),
        }}
      >
        {rotate ? (
          <div className="d-flex align-items-center justify-content-between gap-15 p-2">
            <div>
              <div className="fw-500 text-waring">To Do</div>
              <div className="fw-500 f-14" style={{ width: 210 }}>
                {categoryName && `${categoryName} - Pending`}
              </div>
            </div>
            <div>
              <div
                className="rounded bg-white fw-500 text-center d-flex align-items-center justify-content-center"
                style={{ width: 40, height: 40 }}
              >
                {String(groupByTodo.get(tab, Map()).size).padStart(2, '0')}
              </div>
            </div>
            <ArrowDropDownIcon
              className="text-black cursor-pointer f-20"
              onClick={() => setOpen((pre) => pre.set('1', !toDoAccordion))}
            />
          </div>
        ) : (
          <div className="d-flex justify-content-between flex-grow-1 flex-row-reverse align-items-center p-2">
            <div className="fw-500 text-warning" style={rotate180}>
              To Do ({String(groupByTodo.get(tab, Map()).size).padStart(2, '0')})
            </div>
            <ArrowRightIcon
              className="text-black cursor-pointer f-20"
              onClick={() => handleOpenRotate('1')}
            />
          </div>
        )}
        <AccordionDetails className="bg-white">
          {level && (
            <div className="d-flex justify-content-between align-items-center border-bottom py-1">
              <div className="f-14 text-capitalize">{level} List</div>
              {/* <div className="f-12 text_underline fw-500 text-warning">View All</div> */}
            </div>
          )}
          {groupByTodo.get(tab, Map()).size === 0 && (
            <div className="text-grey text-center f-12 mt-1">! No Data</div>
          )}
          <div className="custom-scroll g-config-scrollbar">
            {groupByTodo.get(tab, List()).map((item, index) => {
              const courseName = item.get('courseName', '');
              const programName = item.get('programName', '');
              const categoryFormGroupId = item.get('categoryFormGroupId', '');
              const institutionName = item.get('institutionName', '');
              const actions = item.get('actions', Map());
              const isTerm = actions.get('academicTerms', false);
              const isAttemptType = actions.get('attemptType', false);
              const isGroup = actions.get('studentGroups', false);
              const term = item.get('term', '');
              const attemptType = item.get('attemptTypeName', '');
              const group = item.get('groupName', '');
              const curriculumName = item.get('curriculumName', '');
              const condition = groupByTodo.get(tab, Map()).size - 1 !== index;
              const checkLevel = {
                course: courseName,
                program: programName,
                institution: institutionName,
              };
              return (
                <div
                  className={`d-flex justify-content-between  align-items-center ${
                    condition && 'border-bottom'
                  } py-1`}
                  key={index}
                >
                  <div>
                    <div className="f-14 fw-500">{checkLevel[level]}</div>
                    <div className="f-12 text-grey d-flex gap-5">
                      <div className="f-12 text-grey d-flex gap-5">
                        {curriculumName && (
                          <div className="d-flex align-items-center f-12 text-grey">
                            <div className="text-nowrap ">
                              {getShortString(`${curriculumName.trim()}`, 7)}
                            </div>
                            <FiberManualRecordIcon sx={{ fontSize: 5 }} className="ml-1" />
                          </div>
                        )}
                        {isTerm && (
                          <div className="d-flex align-items-center f-12 text-grey">
                            <div className="text-nowrap">
                              {getShortString(`${term.trim()} Term`, 5)}
                            </div>
                            <FiberManualRecordIcon sx={{ fontSize: 5 }} className="ml-1" />
                          </div>
                        )}
                        {isAttemptType && (
                          <div className="d-flex align-items-center f-12 text-grey">
                            <div className="text-nowrap ">
                              {getShortString(`${attemptType.trim()} AttemptType`, 7)}
                            </div>
                            <FiberManualRecordIcon sx={{ fontSize: 5 }} className="ml-1" />
                          </div>
                        )}
                        {isGroup && (
                          <div className="d-flex align-items-center f-12 text-grey">
                            <div className="f-12 text-nowrap">
                              {getShortString(`${group.trim()} group`, 7)}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div
                    className="f-12 text-primary fw-500"
                    onClick={(e) => handleClick(e, categoryFormGroupId)}
                  >
                    Action
                  </div>
                </div>
              );
            })}
          </div>
        </AccordionDetails>
      </Accordion>
      <CommandsPop handleClose={handleClosePop} open={opens}>
        <EmailCompound handleClose={handleClosePop} categoryFormGroupId={groupId} />
      </CommandsPop>
    </div>
  );
};

export const EmailCompound = ({ handleClose, categoryFormGroupId }) => {
  const authDataArray = useSelector((state) => state?.auth);
  const authData = fromJS(authDataArray);
  const loggedInUserData = authData.get('loggedInUserData', Map());
  const email = loggedInUserData.get('email', '');
  const userName = loggedInUserData.get('name', '');
  const firstName = userName.get('first', '');
  const lastName = userName.get('last', '');
  const [data, onChange] = useState(
    `<div></br></br></br></br></br></br></br></br><div>Thanks and Regards,</div><div>${firstName} ${lastName}</div></div>`
  );
  const handleChangeCkEditor = (data) => onChange(data.getData());
  const [toMails, setToMails] = useState(List([]));
  const callBack = (data) => setToMails(fromJS(data));
  const [userList] = useCallApiHook(getFormInitiatorUserList);
  const [sendMail] = useCallApiHook(putSendEmailToFormInitiator);
  const isSized = toMails.size === 0;
  const handleSendMail = (e) =>
    sendMail({ to: toMails.map((item) => item.get('email', '')).toJS(), mailContent: data }, () =>
      handleClose(e)
    );
  useEffect(() => userList({ categoryFormGroupId }, callBack), [categoryFormGroupId]);
  useEffect(() => {
    const toMailScroll = document.querySelector('.toMail-scroll');
    toMailScroll.addEventListener('wheel', (e) => {
      if (e.deltaY !== 0) {
        e.preventDefault();
        toMailScroll.scrollLeft += e.deltaY;
      }
    });
  });
  return (
    <Box
      className="bg-white rounded pt-3 px-3 f-13"
      sx={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '60%',
      }}
    >
      <div className="text-black fw-500 f-18">Communication Preferences</div>
      <Box className="rounded border">
        <div className="my-2 ml-3 d-flex gap-10">
          <Avatar alt="Sharp" sx={{ width: 24, height: 24, backgroundColor: '#86EFAC' }} />
          From : {email}
        </div>
        <Divider />
        <div className="mx-3 my-2 d-flex justify-content-between">
          <div className="approver-email-grid">
            <div>To :</div>
            <div className="d-flex toMail-scroll gap-5">
              {toMails.map((mail, index) => (
                <div key={index} className="d-flex gap-5 approver-to align-items-center">
                  <div>{mail.get('email', '')}</div>
                  <CloseIcon
                    className="m-0 cursor-pointer"
                    sx={{ fontSize: 13 }}
                    onClick={() => setToMails((pre) => pre.delete(index))}
                  />
                </div>
              ))}
            </div>
            {/* <div className="cursor-pointer">Cc Bcc</div> */}
          </div>
        </div>
        <Divider />
        <div className="approver-ckeditor">
          <CkEditor5 onChange={handleChangeCkEditor} data={data} />
        </div>
        {/* <div className="ml-3 my-2">
          <div>Thanks and Regards,</div>
          <div>Prakash R.</div>
        </div> */}
      </Box>
      <footer className="d-flex justify-content-end  py-3 gap-10  mt-2">
        <Button
          className="f-14"
          onClick={handleClose}
          sx={{
            ...buttonSx,
            backgroundColor: 'white',
            border: '1px solid #9e9e9e',
            padding: '5px 25px',
            color: 'grey',
            '&:hover': {
              backgroundColor: 'transparent',
            },
          }}
        >
          Cancel
        </Button>
        <Button
          className=" f-14"
          startIcon={<SendIcon className="f-16" />}
          onClick={handleSendMail}
          disabled={isSized}
          sx={{
            ...buttonSx,
            padding: '5px 25px',
            backgroundColor: isSized ? 'grey' : '#147AFC',
            color: isSized ? 'grey' : '#ffffff',
            '&:hover': {
              backgroundColor: '#147AFC',
            },
          }}
        >
          Send
        </Button>
      </footer>
    </Box>
  );
};

export const convertNumberToText = (number) => {
  const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
  const teens = [
    'ten',
    'eleven',
    'twelve',
    'thirteen',
    'fourteen',
    'fifteen',
    'sixteen',
    'seventeen',
    'eighteen',
    'nineteen',
  ];
  const tens = [
    '',
    '',
    'twenty',
    'thirty',
    'forty',
    'fifty',
    'sixty',
    'seventy',
    'eighty',
    'ninety',
  ];
  function numberToWords(num) {
    if (num === 0) return 'zero';
    if (num < 10) return ones[num];
    if (num < 20) return teens[num - 10];
    if (num < 100) return tens[Math.floor(num / 10)] + (num % 10 !== 0 ? ones[num % 10] : '');
    if (num < 1000)
      return (
        ones[Math.floor(num / 100)] + 'hundred' + (num % 100 !== 0 ? numberToWords(num % 100) : '')
      );
    return num.toString();
  }
  const word = numberToWords(number);
  const capitalizedWord = 'Any' + word.charAt(0).toUpperCase() + word.slice(1);
  return `${capitalizedWord} In This Role`;
};

const useAddCommentHook = ({ _id, handleDialog }) => {
  const [updateApi] = useCallApiHook(addComment);
  const [setMessage] = useCallApiHook(setData);
  // const [updateFile] = useCallApiHook(multipleFileUpload);
  const { upload } = useFileUpload();
  // const dispatch = useDispatch();
  const initialState = fromJS(
    fromJS({
      formGuideResourcesId: '',
      addComment: [{ attachments: [], comment: '' }],
    })
  );
  const initialStateForBasic = { commentType: 'common', selectedSections: [] };
  const [commentSection, setCommentSection] = useState(initialState);
  const [basicDetails, setBasicDetails] = useState(fromJS(initialStateForBasic));
  const authDataArray = useSelector((state) => state?.auth);
  const authData = fromJS(authDataArray);
  const loggedInUserData = authData.get('loggedInUserData', Map());
  const staffId = loggedInUserData.get('_id', '');
  const roleId = authData.getIn(['selectedRole', '_id'], '');
  const handleChange = (e) => {
    const value = e.target.value;
    setBasicDetails((prev) => prev.set('commentType', value));
    setCommentSection(initialState);
  };
  const handleChangeSections = (value) => () => {
    const bool = basicDetails.get('selectedSections', List()).includes(value);
    const filterValues = basicDetails
      .get('selectedSections', List())
      .filter((section) => section !== value);
    if (bool) {
      setBasicDetails((previous) => previous.set('selectedSections', filterValues));
    } else {
      setBasicDetails((previous) =>
        previous.update('selectedSections', List(), (sections) => sections.push(value))
      );
    }
  };

  const handleChangeCkEditor = (index, level) => (data) => {
    setCommentSection((prev) =>
      prev
        .set('formGuideResourcesId', _id)
        .setIn(['addComment', index, 'comment'], data.getData())
        .setIn(['addComment', index, 'commentType'], basicDetails.get('commentType', 'common'))
        .setIn(
          ['addComment', index, 'selectedSections'],
          basicDetails.get('selectedSections', List())
        )
        .setIn(['addComment', index, 'level'], Number(level))
        .setIn(['addComment', index, 'roleId'], roleId)
        .setIn(['addComment', index, 'userId'], staffId)
    );
  };

  const callBack = (index, e) => (data) => {
    setCommentSection((pre) =>
      pre.updateIn(['addComment', index, 'attachments'], List(), (value) =>
        value.push(Map({ url: data.getIn([0, 'url'], ''), name: data.getIn([0, 'name'], '') }))
      )
    );
    if (e.target) e.target.value = null;
  };

  const handleDialogCb = () => {
    setBasicDetails(fromJS(initialStateForBasic));
    setCommentSection(fromJS(initialState));
    handleDialog();
  };

  const handleChangeProperties = async (e, index) => {
    const validExtention = ['jpg', 'png'];
    const value = e.target.files[0];
    const validate = !validExtention.includes(value?.name.toLowerCase().split('.')[1]);
    if (validate) {
      return setMessage(
        fromJS({ message: 'Only JPG and PNG file formats are accepted for attachments' })
      );
    }
    try {
      setMessage(Map({ isLoading: true }));
      const uploadedFilesData = await upload({
        files: [value],
        component: 'q360',
      });
      const formattedFiles = uploadedFilesData.map(
        ({ signedUrl, unSignedUrl, name, type, size }) => {
          return {
            size,
            url: unSignedUrl,
            signedUrl,
            name: `${name}.${type}`,
            type,
          };
        }
      );
      callBack(index, e)(fromJS(formattedFiles));
    } catch (err) {
      setMessage(fromJS({ message: err.message || 'Failed to upload file' }));
      setMessage(Map({ isLoading: false, message: err.message }));
    } finally {
      setMessage(Map({ isLoading: false }));
    }
  };
  const handleSave = () => updateApi(commentSection, handleDialogCb);
  return [
    basicDetails,
    commentSection,
    handleChange,
    handleChangeSections,
    handleSave,
    handleChangeProperties,
    handleChangeCkEditor,
  ];
};

export const AddComments = ({ _id, handleDialog, open }) => {
  const applicationList = useSelector(selectFromApplicationList);
  const [searchParams] = useSearchParams();
  const formType = searchParams.get('formType');
  const categoryId = searchParams.get('categoryId');
  const formInitiatorId = searchParams.get('formInitiatorId');
  const userLevel = searchParams.get('currentLevel');
  const [
    basicDetails,
    commentSection,
    handleChange,
    handleChangeSections,
    handleSave,
    handleChangeProperties,
    handleChangeCkEditor,
  ] = useAddCommentHook({ _id, handleDialog });
  const particularForm = applicationList.get(`${categoryId}+${formInitiatorId}`, Map());
  const approver =
    particularForm
      .getIn(['categoryFormId', 'approvalLevel'], List())
      .find((level) => level.get('levelNumber', 0) === parseInt(userLevel)) || List();
  const sections = approver.get('specificSections', List());
  const commentType = basicDetails.get('commentType', '');
  const render = (selected, placeholder) => {
    return selected.length ? (
      selected.join(', ')
    ) : (
      <div className="f-12 text-divider-color">{placeholder}</div>
    );
  };
  const handleClose = () => handleDialog(false);
  const openFile = (index) => {
    const attachFile = document.getElementById('attach-file');
    const file = document.getElementById(`file${index}`);
    attachFile.onClick = file.click();
  };
  const validate = commentSection
    .get('addComment', List())
    .some((section) => section.get('comment', '') === '');
  return (
    <Dialog open={open} fullWidth maxWidth="sm" onClose={handleClose}>
      <DialogTitle className="px-4 pt-4 pb-0 m-0">
        <h4>Add Comment</h4>
      </DialogTitle>
      <DialogContent>
        <RadioGroup row onChange={handleChange} defaultValue={commentType}>
          <FormControlLabel
            value="common"
            control={<Radio size="small" disableRipple />}
            label="Common Comment"
            className="text-black"
          />
          {formType === 'section' && (
            <FormControlLabel
              value="specific"
              control={<Radio size="small" disableRipple />}
              label="Specific Section"
              className="text-black"
            />
          )}
        </RadioGroup>
        {basicDetails.get('commentType', '') === 'specific' && (
          <FormControl fullWidth size="small">
            <Select
              multiple
              displayEmpty
              className="f-14"
              MenuProps={MenuProps}
              value={basicDetails.get('selectedSections', List()).toJS()}
              renderValue={(selected) => render(selected, '')}
            >
              {sections.map((name, index) => (
                <MenuItem
                  key={index}
                  value={name}
                  sx={menuItemStyles}
                  className="flex-wrap"
                  onClick={handleChangeSections(name)}
                >
                  <Checkbox
                    size="small"
                    checked={basicDetails.get('selectedSections', List()).includes(name)}
                    className="p-0 mr-2"
                    disableRipple
                    sx={checkboxStyles}
                  />
                  {name}
                </MenuItem>
              ))}
              ;
            </Select>
          </FormControl>
        )}
        {basicDetails.get('commentType', '') === 'specific' && (
          <DialogContentText className="custom-scroll g-config-scrollbar">
            {basicDetails.get('selectedSections', List()).map((data, index) => {
              const attachments = commentSection.getIn(
                ['addComment', index, 'attachments'],
                List()
              );
              return (
                <div key={index} className="my-2">
                  <Box sx={{ color: '#15803D' }} className="fw-500 f-16 mb-2">
                    {data}
                  </Box>
                  <div className="approver-ckeditor-particular position-relative">
                    <div
                      className="d-flex position-absolute gap-5"
                      style={{ right: 15, zIndex: 1, top: 16, lineHeight: 1 }}
                    >
                      <div
                        className="text-primary  f-14 text-underline"
                        type="file"
                        id="attach-file"
                        onClick={() => openFile(index)}
                      >
                        Attach Files
                      </div>
                      <Tooltip title={attachments.map((image) => image.get('name', '')).join(',')}>
                        <InfoOutlined
                          className={'text-lGrey cursor-pointer'}
                          fontSize="small"
                          sx={{ fontSize: '15px' }}
                        />
                      </Tooltip>
                    </div>
                    <CkEditor5
                      onChange={handleChangeCkEditor(index, userLevel)}
                      data={commentSection.getIn(['addComment', index, 'comment'], '')}
                      toolBar={['bold', 'fontSize', 'fontColor', 'bulletedList', 'italic']}
                      placeholder="Type here the comment for the section"
                    />
                    <input
                      type="file"
                      id={`file${index}`}
                      className="d-none"
                      onChange={(e) => handleChangeProperties(e, index)}
                    />
                  </div>
                </div>
              );
            })}
          </DialogContentText>
        )}
        {basicDetails.get('commentType', '') === 'common' && (
          <div className="approver-ckeditor-particular position-relative " style={{ height: 250 }}>
            <div
              className="d-flex position-absolute gap-5"
              style={{ right: 15, zIndex: 1, top: 16, lineHeight: 1 }}
            >
              <div
                className="text-primary  f-14 text-underline"
                type="file"
                id="attach-file"
                onClick={() => openFile(0)}
              >
                Attach Files
              </div>
              <Tooltip
                title={commentSection
                  .getIn(['addComment', 0, 'attachments'], List())
                  .map((image) => image.get('name', ''))
                  .join(',')}
              >
                <InfoOutlined
                  className={'text-lGrey cursor-pointer'}
                  fontSize="small"
                  sx={{ fontSize: '15px' }}
                />
              </Tooltip>
            </div>
            <div className="position-absolute w-100">
              <CkEditor5
                onChange={handleChangeCkEditor(0, userLevel)}
                data={commentSection.getIn(['addComment', 0, 'comment'], '')}
                toolBar={['bold', 'fontSize', 'fontColor', 'bulletedList', 'italic']}
                placeholder="Type here the comment"
              />
            </div>
            <input
              type="file"
              id={`file${0}`}
              className="d-none"
              onChange={(e) => handleChangeProperties(e, 0)}
            />
          </div>
        )}
      </DialogContent>
      <DialogActions className="mb-3 mr-3">
        <Button
          className="f-14"
          onClick={handleClose}
          sx={{
            ...buttonSx,
            backgroundColor: 'white',
            border: '1px solid #9e9e9e',
            padding: '5px 25px',
            color: 'grey',
            '&:hover': {
              backgroundColor: 'transparent',
            },
          }}
        >
          Cancel
        </Button>
        <Button
          className=" f-14"
          onClick={handleSave}
          disabled={validate}
          sx={{
            ...buttonSx,
            padding: '5px 25px',
            backgroundColor: validate ? 'grey' : '#147AFC',
            color: validate ? 'grey' : '#ffffff',
            '&:hover': {
              backgroundColor: '#147AFC',
            },
          }}
        >
          Send
        </Button>
      </DialogActions>
    </Dialog>
  );
};
