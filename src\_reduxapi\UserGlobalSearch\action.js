import { createAction } from '../util';
import axios from '../../axios';
import { Map, fromJS } from 'immutable';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';

const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');

export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}
export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const SET_BREADCRUMB_SUCCESS = 'SET_BREADCRUMB_SUCCESS';
const setBreadCrumbSuccess = createAction(SET_BREADCRUMB_SUCCESS, 'breadcrumbs');
export const setBreadCrumb = (arr) => {
  return (dispatch) => {
    dispatch(setBreadCrumbSuccess(arr));
  };
};

export const GET_USER_GLOBAL_SEARCH_REQUEST = 'GET_USER_GLOBAL_SEARCH_REQUEST';
export const GET_USER_GLOBAL_SEARCH_SUCCESS = 'GET_USER_GLOBAL_SEARCH_SUCCESS';
export const GET_USER_GLOBAL_SEARCH_FAILURE = 'GET_USER_GLOBAL_SEARCH_FAILURE';

const getUserGlobalSearchRequest = createAction(GET_USER_GLOBAL_SEARCH_REQUEST);
const getUserGlobalSearchSuccess = createAction(GET_USER_GLOBAL_SEARCH_SUCCESS, 'data');
const getUserGlobalSearchFailure = createAction(GET_USER_GLOBAL_SEARCH_FAILURE, 'error');

export function getUserGlobalSearch(userType, searchKey, callback) {
  return function (dispatch) {
    dispatch(getUserGlobalSearchRequest());
    let URL = `/userGlobalSearch/getUserDropDownList?userType=${userType}&searchKey=${searchKey}`;
    axios
      .get(URL)
      .then((res) => {
        dispatch(getUserGlobalSearchSuccess(res.data.data));
        callback && callback();
      })
      .catch((error) => {
        dispatch(getUserGlobalSearchFailure(error));
        callback && callback();
      });
  };
}

export const GET_USER_COURSE_DETAILS_REQUEST = 'GET_USER_COURSE_DETAILS_REQUEST';
export const GET_USER_COURSE_DETAILS_SUCCESS = 'GET_USER_COURSE_DETAILS_SUCCESS';
export const GET_USER_COURSE_DETAILS_FAILURE = 'GET_USER_COURSE_DETAILS_FAILURE';

const getUserCourseDetailsRequest = createAction(GET_USER_COURSE_DETAILS_REQUEST);
const getUserCourseDetailsSuccess = createAction(GET_USER_COURSE_DETAILS_SUCCESS, 'data');
const getUserCourseDetailsFailure = createAction(GET_USER_COURSE_DETAILS_FAILURE, 'error');

export function getUserCourseDetails(institutionCalendarId, userType, userId, callBack) {
  return function (dispatch) {
    dispatch(getUserCourseDetailsRequest());
    let URL = `/userGlobalSearch/getUserCourseDetails?institutionCalendarId=${institutionCalendarId}&userType=${userType}&userId=${userId}`;
    axios
      .get(URL)
      .then((res) => {
        dispatch(getUserCourseDetailsSuccess(res.data.data));
        callBack && callBack(res.data.data);
      })
      .catch((error) => dispatch(getUserCourseDetailsFailure(error)));
  };
}
// -------------------------------------------------------------------------------------------------

export const GET_TARDIS_LIST_REQUEST = 'GET_TARDIS_LIST_REQUEST';
export const GET_TARDIS_LIST_SUCCESS = 'GET_TARDIS_LIST_SUCCESS';
export const GET_TARDIS_LIST_FAILURE = 'GET_TARDIS_LIST_FAILURE';

const getTardisListRequest = createAction('GET_TARDIS_LIST_REQUEST');
const getTardisListSuccess = createAction('GET_TARDIS_LIST_SUCCESS', 'data');
const getTardisListFailure = createAction('GET_TARDIS_LIST_FAILURE', 'error');

export function getTardisList(callBack) {
  return function (dispatch) {
    dispatch(getTardisListRequest());
    axios
      .get(`/global-session-settings/get-tardis`)
      .then((res) => {
        dispatch(getTardisListSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getTardisListFailure(error)));
  };
}

export const GET_DISCIPLINARY_REMARKS_REQUEST = 'GET_DISCIPLINARY_REMARKS_REQUEST';
export const GET_DISCIPLINARY_REMARKS_SUCCESS = 'GET_DISCIPLINARY_REMARKS_SUCCESS';
export const GET_DISCIPLINARY_REMARKS_FAILURE = 'GET_DISCIPLINARY_REMARKS_FAILURE';

const getDisciplinaryRemarksRequest = createAction(GET_DISCIPLINARY_REMARKS_REQUEST);
const getDisciplinaryRemarksSuccess = createAction(GET_DISCIPLINARY_REMARKS_SUCCESS, 'data');
const getDisciplinaryRemarksFailure = createAction(GET_DISCIPLINARY_REMARKS_FAILURE, 'error');

export function getDisciplinaryRemarks(params, CalendarId, callBack) {
  return function (dispatch) {
    dispatch(getDisciplinaryRemarksRequest());
    axios
      .get(`/digiclass/disciplinary_remarks/students_all_remarks`, {
        params,
        headers: { _institution_calendar_id: CalendarId },
      })
      .then((res) => {
        dispatch(getDisciplinaryRemarksSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getDisciplinaryRemarksFailure(error)));
  };
}
export const POST_DISCIPLINARY_REMARKS_REQUEST = 'POST_DISCIPLINARY_REMARKS_REQUEST';
export const POST_DISCIPLINARY_REMARKS_SUCCESS = 'POST_DISCIPLINARY_REMARKS_SUCCESS';
export const POST_DISCIPLINARY_REMARKS_FAILURE = 'POST_DISCIPLINARY_REMARKS_FAILURE';

const postDisciplinaryRemarksRequest = createAction(POST_DISCIPLINARY_REMARKS_REQUEST);
const postDisciplinaryRemarksSuccess = createAction(POST_DISCIPLINARY_REMARKS_SUCCESS, 'data');
const postDisciplinaryRemarksFailure = createAction(POST_DISCIPLINARY_REMARKS_FAILURE, 'error');

export function postDisciplinaryRemarks(requestBody, callBack) {
  return function (dispatch) {
    dispatch(postDisciplinaryRemarksRequest());
    axios
      .post('/digiclass/disciplinary_remarks', requestBody)
      .then(() => {
        dispatch(postDisciplinaryRemarksSuccess());
        callBack && callBack();
      })
      .catch((error) => dispatch(postDisciplinaryRemarksFailure(error)));
  };
}
export const PUT_DISCIPLINARY_REMARKS_REQUEST = 'PUT_DISCIPLINARY_REMARKS_REQUEST';
export const PUT_DISCIPLINARY_REMARKS_SUCCESS = 'PUT_DISCIPLINARY_REMARKS_SUCCESS';
export const PUT_DISCIPLINARY_REMARKS_FAILURE = 'PUT_DISCIPLINARY_REMARKS_FAILURE';

const putDisciplinaryRemarksRequest = createAction(PUT_DISCIPLINARY_REMARKS_REQUEST);
const putDisciplinaryRemarksSuccess = createAction(PUT_DISCIPLINARY_REMARKS_SUCCESS, 'data');
const putDisciplinaryRemarksFailure = createAction(PUT_DISCIPLINARY_REMARKS_FAILURE, 'error');

export function putDisciplinaryRemarks(requestBody, callBack) {
  return function (dispatch) {
    dispatch(putDisciplinaryRemarksRequest());
    axios
      .put('/digiclass/disciplinary_remarks', requestBody)
      .then((res) => {
        dispatch(putDisciplinaryRemarksSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(putDisciplinaryRemarksFailure(error)));
  };
}
export const DELETE_DISCIPLINARY_REMARKS_REQUEST = 'DELETE_DISCIPLINARY_REMARKS_REQUEST';
export const DELETE_DISCIPLINARY_REMARKS_SUCCESS = 'DELETE_DISCIPLINARY_REMARKS_SUCCESS';
export const DELETE_DISCIPLINARY_REMARKS_FAILURE = 'DELETE_DISCIPLINARY_REMARKS_FAILURE';

const deleteDisciplinaryRemarksRequest = createAction(DELETE_DISCIPLINARY_REMARKS_REQUEST);
const deleteDisciplinaryRemarksSuccess = createAction(DELETE_DISCIPLINARY_REMARKS_SUCCESS, 'data');
const deleteDisciplinaryRemarksFailure = createAction(DELETE_DISCIPLINARY_REMARKS_FAILURE, 'error');

export function deleteDisciplinaryRemarks(remarkId, callBack) {
  return function (dispatch) {
    dispatch(deleteDisciplinaryRemarksRequest());
    axios
      .delete(`/digiclass/disciplinary_remarks?remarkId=${remarkId}`)
      .then(() => {
        dispatch(deleteDisciplinaryRemarksSuccess());
        callBack && callBack();
      })
      .catch((error) => dispatch(deleteDisciplinaryRemarksFailure(error)));
  };
}
export const UPLOAD_REMARKS_FILE_REQUEST = 'UPLOAD_REMARKS_FILE_REQUEST';
export const UPLOAD_REMARKS_FILE_SUCCESS = 'UPLOAD_REMARKS_FILE_SUCCESS';
export const UPLOAD_REMARKS_FILE_FAILURE = 'UPLOAD_REMARKS_FILE_FAILURE';

const uploadRemarksFileRequest = createAction(UPLOAD_REMARKS_FILE_REQUEST);
const uploadRemarksFileSuccess = createAction(UPLOAD_REMARKS_FILE_SUCCESS, 'responseData');
const uploadRemarksFileFailure = createAction(UPLOAD_REMARKS_FILE_FAILURE, 'errorData');

export function uploadRemarksFile(requestBody, callBack) {
  return function (dispatch) {
    dispatch(uploadRemarksFileRequest());
    axios
      .post(`/digiclass/disciplinary_remarks/userRemarkFiles`, requestBody)
      .then((res) => {
        dispatch(uploadRemarksFileSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(uploadRemarksFileFailure(error)));
  };
}
export const SEND_REMARKS_MAIL_REQUEST = 'SEND_REMARKS_MAIL_REQUEST';
export const SEND_REMARKS_MAIL_SUCCESS = 'SEND_REMARKS_MAIL_SUCCESS';
export const SEND_REMARKS_MAIL_FAILURE = 'SEND_REMARKS_MAIL_FAILURE';

const sendRemarksMailRequest = createAction(SEND_REMARKS_MAIL_REQUEST);
const sendRemarksMailSuccess = createAction(SEND_REMARKS_MAIL_SUCCESS, 'responseData');
const sendRemarksMailFailure = createAction(SEND_REMARKS_MAIL_FAILURE, 'errorData');

export function sendRemarksMail(remarkId, callBack) {
  return function (dispatch) {
    dispatch(sendRemarksMailRequest());
    axios
      .post(`/digiclass/disciplinary_remarks/sendMail`, { remarkId })
      .then(() => {
        dispatch(sendRemarksMailSuccess());
        callBack && callBack();
      })
      .catch((error) => dispatch(sendRemarksMailFailure(error)));
  };
}

export const GET_SESSION_DETAILS_REQUEST = 'GET_SESSION_DETAILS_REQUEST';
export const GET_SESSION_DETAILS_SUCCESS = 'GET_SESSION_DETAILS_SUCCESS';
export const GET_SESSION_DETAILS_FAILURE = 'GET_SESSION_DETAILS_FAILURE';

const getSessionDetailsRequest = createAction(GET_SESSION_DETAILS_REQUEST);
const getSessionDetailsSuccess = createAction(GET_SESSION_DETAILS_SUCCESS, 'data');
const getSessionDetailsFailure = createAction(GET_SESSION_DETAILS_FAILURE, 'error');

export function getSessionDetails(scheduleId, callBack) {
  return function (dispatch) {
    dispatch(getSessionDetailsRequest());
    axios
      .get(`/digiclass/disciplinary_remarks/schedule_details?scheduleId=${scheduleId}`)
      .then((res) => {
        dispatch(getSessionDetailsSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getSessionDetailsFailure(error)));
  };
}
export const GET_MAIL_DETAILS_REQUEST = 'GET_MAIL_DETAILS_REQUEST';
export const GET_MAIL_DETAILS_SUCCESS = 'GET_MAIL_DETAILS_SUCCESS';
export const GET_MAIL_DETAILS_FAILURE = 'GET_MAIL_DETAILS_FAILURE';

const getMailDetailsRequest = createAction(GET_MAIL_DETAILS_REQUEST);
const getMailDetailsSuccess = createAction(GET_MAIL_DETAILS_SUCCESS, 'data');
const getMailDetailsFailure = createAction(GET_MAIL_DETAILS_FAILURE, 'error');

export function getMailDetails(remarkId, callBack) {
  return function (dispatch) {
    dispatch(getMailDetailsRequest());
    axios
      .get(`/digiclass/disciplinary_remarks/mailDetails?remarkId=${remarkId}`)
      .then((res) => {
        dispatch(getMailDetailsSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(getMailDetailsFailure(error)));
  };
}
export const GENERATE_MULTIPLE_SIGNED_URLS_REQUEST = 'GENERATE_MULTIPLE_SIGNED_URLS_REQUEST';
export const GENERATE_MULTIPLE_SIGNED_URLS_SUCCESS = 'GENERATE_MULTIPLE_SIGNED_URLS_SUCCESS';
export const GENERATE_MULTIPLE_SIGNED_URLS_FAILURE = 'GENERATE_MULTIPLE_SIGNED_URLS_FAILURE';

const generateMultipleSignedUrlsRequest = createAction(GENERATE_MULTIPLE_SIGNED_URLS_REQUEST);
const generateMultipleSignedUrlsSuccess = createAction(
  GENERATE_MULTIPLE_SIGNED_URLS_SUCCESS,
  'data'
);
const generateMultipleSignedUrlsFailure = createAction(
  GENERATE_MULTIPLE_SIGNED_URLS_FAILURE,
  'error'
);

export function generateMultipleSignedUrls(fileUrls, callBack) {
  return function (dispatch) {
    dispatch(generateMultipleSignedUrlsRequest());
    axios
      .post(`/digiclass/disciplinary_remarks/generateSignedURLs`, { fileUrls })
      .then((res) => {
        dispatch(generateMultipleSignedUrlsSuccess());
        callBack && callBack(fromJS(res.data).get('data', Map()));
      })
      .catch((error) => dispatch(generateMultipleSignedUrlsFailure(error)));
  };
}

export const GET_COMPREHENSIVE_SETTINGS_REQUEST = 'GET_COMPREHENSIVE_SETTINGS_REQUEST';
export const GET_COMPREHENSIVE_SETTINGS_SUCCESS = 'GET_COMPREHENSIVE_SETTINGS_SUCCESS';
export const GET_COMPREHENSIVE_SETTINGS_FAILURE = 'GET_COMPREHENSIVE_SETTINGS_FAILURE';

const getComprehensiveSettingsRequest = createAction(GET_COMPREHENSIVE_SETTINGS_REQUEST, 'data');
const getComprehensiveSettingsSuccess = createAction(GET_COMPREHENSIVE_SETTINGS_SUCCESS, 'data');
const getComprehensiveSettingsFailure = createAction(GET_COMPREHENSIVE_SETTINGS_FAILURE, 'error');

export function getComprehensiveSettings({
  level,
  userId,
  institutionCalendarId,
  programId,
  term,
  type = 'warning',
}) {
  return function (dispatch) {
    dispatch(getComprehensiveSettingsRequest(type));
    axios
      .get(
        `/digiclass/course_session/${
          type === 'warning' ? 'userLevelComprehensiveWarning' : 'userLevelComprehensiveAttendance'
        }?institutionCalendarId=${institutionCalendarId}&userId=${userId}&type=student&levelNo=${level}&programId=${programId}&term=${term}`
      )
      .then((res) => {
        dispatch(getComprehensiveSettingsSuccess({ data: res.data.data, type }));
        if (type === 'warning') {
          dispatch(
            getComprehensiveSettings({
              level,
              userId,
              institutionCalendarId,
              programId,
              term,
              type: 'attendance',
            })
          );
        }
      })
      .catch((error) => dispatch(getComprehensiveSettingsFailure(error)));
  };
}
