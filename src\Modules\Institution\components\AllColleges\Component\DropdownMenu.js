import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Dropdown } from 'react-bootstrap';

function DropdownMenu({ setShow, menu }) {
  return (
    <Dropdown.Menu>
      {menu.map((item, index) => {
        return (
          <Dropdown.Item
            href="#"
            key={index}
            onClick={() => {
              setShow(item.showModal);
            }}
          >
            <Trans i18nKey={item.title}></Trans>
          </Dropdown.Item>
        );
      })}
    </Dropdown.Menu>
  );
}

DropdownMenu.propTypes = {
  setShow: PropTypes.func,
  menu: PropTypes.array,
};
export default DropdownMenu;
