import { useState, useEffect, useRef } from 'react';

function useDebounce(value, delay, doSomething) {
  // State and setters for debounced valueprog
  const initialRender = useRef(true); //prevent initial render
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(
    () => {
      if (initialRender.current) {
        initialRender.current = false;
        return () => {};
      }
      // Update debounced value after delay
      const handler = setTimeout(() => {
        setDebouncedValue(value);
        doSomething && doSomething(value);
      }, delay);
      // Cancel the timeout if value changes (also on delay change or unmount)
      // This is how we prevent debounced value from updating if value is changed ...
      // .. within the delay period. Timeout gets cleared and restarted.
      return () => {
        clearTimeout(handler);
      };
    },
    [value, delay] // eslint-disable-line
  );
  return debouncedValue;
}

export default useDebounce;
