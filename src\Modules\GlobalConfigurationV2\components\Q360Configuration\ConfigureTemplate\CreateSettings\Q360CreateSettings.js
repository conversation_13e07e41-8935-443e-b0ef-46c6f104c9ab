import React, { useState, useEffect, forwardRef, useRef } from 'react';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
//react library
import { Delete, Edit } from '@mui/icons-material';
import { Avatar, AccordionDetails } from '@mui/material';
// import { FormControlLabel, Switch } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Button from '@mui/material/Button';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
//mui Imports
import { List } from 'immutable';
// immutable js
import { getQaPcSetting, qaPcSaveSetting } from '_reduxapi/q360/actions';
import { selectQaPcSetting } from '_reduxapi/q360/selectors';
// redux Imports
import CreateAttemptType from './CreateAttemptType';
import CreateTags from './CreateTags';
//file Imports
import { Accordion, AccordionSummary } from './designUtils';
import { makeStyles } from '@mui/styles';
import { useCheckEditAccess } from '../..';
import { jsUcfirstAll } from 'utils';
//style Imports

//----------------------------------custom hooks start----------------------------------------------
const filteredTypes = (item) => {
  let filteredData = item
    .filter(
      (item) =>
        !item.isEmpty() &&
        (!item.get('isActive', true) || !item.has('_id') || item.get('isEdited', false))
    )
    .map((item) => {
      if (item.has('subTag')) {
        item = item.update('subTag', List(), (subTag) => subTag.map((tag) => jsUcfirstAll(tag)));
      }
      item = item.update('name', '', (name) => jsUcfirstAll(name));
      return item.delete('isEdited');
    });
  return {
    filteredData,
  };
};
//----------------------------------custom hooks end------------------------------------------------
//----------------------------------componentStart--------------------------------------------------
const TagComponent = forwardRef(({ buttonName, editAccess, tags }, ref) => {
  // const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [tagData, setTagData] = useState(List());
  const [editIndex, setEditIndex] = useState(-1);
  useEffect(() => {
    setTagData(tags);
  }, [tags]);
  // const handleRadio = (index) => (e) => {
  //   setTagData((prev) =>
  //     prev.setIn([index, 'isDefault'], e.target.checked).setIn([index, 'isEdited'], true)
  //   );
  // };
  ref.current.set('tags', tagData);
  return (
    <div>
      {editAccess && (
        <Button
          fullWidth
          className="text-capitalize btn-border-dotted text-primary responsiveFontSizeSmall border border-dotted"
          onClick={() => setOpen(true)}
        >
          + {buttonName}
        </Button>
      )}
      <div>
        {tagData.map((tag, index) => {
          if (!tag.get('isActive', true)) {
            return null;
          }
          const subTag = tag.get('subTag', List());
          return (
            <section className="d-flex align-items-center border-bottom" key={index}>
              <div className="flex-grow-1 p-3">
                <p className="m-0 pb-1">
                  <span className="f-15 fw-500">{tag.get('name', '')}</span>
                  <span className="f-14 fw-400 text-lGrey ml-2">
                    {tag.get('isDefault', false) ? '(Default)' : ''}
                  </span>
                </p>
                {/* <p className="m-0 f-14 fw-400 text-lGrey">Level:{tag.get('level', '')}</p> */}
                <div className="m-0 f-14 fw-400 text-lGrey d-flex align-items-center">
                  <div> {`Level: ${tag.get('level', '')}`}</div>
                  <div className="px-2">
                    {tag.has('subTag') && subTag.size > 0 && (
                      <FiberManualRecordIcon sx={{ fontSize: '9px' }} />
                    )}
                  </div>
                  <div>
                    {subTag.size > 0 && ' Sub Tags:'}
                    {tag.has('subTag') ? (
                      <span className="pl-1">
                        {subTag.map((subTag, index) => (
                          <span key={index}>
                            {index > 0 ? ', ' : ''}
                            {subTag}
                          </span>
                        ))}
                      </span>
                    ) : (
                      <span></span>
                    )}
                  </div>
                </div>
              </div>
              {editAccess && (
                <>
                  {/* <FormControlLabel
                    checked={tag.get('isDefault', false)}
                    className="m-0"
                    onChange={handleRadio(index)}
                    control={<Switch defaultChecked sx={switchSX} />}
                    classes={{ label: classes.label }}
                    label={tag.get('isDefault', false) ? 'ON' : 'OFF'}
                  /> */}
                  <Edit
                    className="ml-3 cursor-pointer"
                    onClick={() => setEditIndex(index)}
                    fontSize="small"
                  />
                  <Delete
                    className="text-red ml-3 cursor-pointer"
                    fontSize="small"
                    onClick={() => setTagData((prev) => prev.setIn([index, 'isActive'], false))}
                  />
                </>
              )}
            </section>
          );
        })}
        {open && (
          <CreateTags
            open={true}
            handleClose={() => setOpen(false)}
            existData={tagData}
            setTagData={setTagData}
          />
        )}
        {editIndex !== -1 && (
          <CreateTags
            isEdit={true}
            open={true}
            editIndex={editIndex}
            existData={tagData}
            handleClose={() => {
              setEditIndex(-1);
            }}
            setTagData={setTagData}
          />
        )}
      </div>
    </div>
  );
});

const AttemptComponent = forwardRef(({ buttonName, editAccess, attemptType }, ref) => {
  const [editIndex, setEditIndex] = useState(-1);
  const [attemptTypeData, setAttemptData] = useState(List());
  const [open, setOpen] = useState(false);
  useEffect(() => {
    setAttemptData(attemptType);
  }, [attemptType]);

  ref.current.set('attemptType', attemptTypeData);
  return (
    <div>
      {editAccess && (
        <Button
          fullWidth
          className="text-capitalize btn-border-dotted text-primary responsiveFontSizeSmall border border-dotted"
          onClick={() => setOpen(true)}
        >
          + {buttonName}
        </Button>
      )}
      <div>
        {attemptTypeData.map((attempt, index) => {
          if (!attempt.get('isActive', true)) {
            return null;
          }
          return (
            <section className="d-flex p-3 border-bottom" key={index}>
              <div className="flex-grow-1 f-16 fw-400">{attempt.get('name', '')}</div>
              {editAccess && (
                <>
                  <Edit
                    className="ml-3 cursor-pointer"
                    onClick={() => setEditIndex(index)}
                    fontSize="small"
                  />
                  <Delete
                    fontSize="small"
                    className="text-red ml-3 cursor-pointer"
                    onClick={() => setAttemptData((prev) => prev.setIn([index, 'isActive'], false))}
                  />
                </>
              )}
            </section>
          );
        })}
        {open && (
          <CreateAttemptType
            open={true}
            handleClose={() => setOpen(false)}
            existData={attemptTypeData}
            setAttemptData={setAttemptData}
          />
        )}
        {editIndex !== -1 && (
          <CreateAttemptType
            open={true}
            isEdit={true}
            editIndex={editIndex}
            existData={attemptTypeData}
            handleClose={() => setEditIndex(-1)}
            setAttemptData={setAttemptData}
          />
        )}
      </div>
    </div>
  );
});
const SkeletonTemplate = ({ summaryDetails, children, type, expanded, setExpanded }) => {
  const handleChange = (type) => setExpanded((previous) => (previous === type ? null : type));
  const { bgColors, color, tagName, SettingTitle, Action } = summaryDetails;
  return (
    <Accordion
      expanded={expanded === type}
      onChange={() => handleChange(type)}
      className={`bg-white  box-shadow  ${expanded === type ? 'box-shadow-static' : ''}`}
      elevation={0}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />} className="py-3">
        <section className="d-flex align-items-center">
          <Avatar
            alt="No-icon"
            className="cursor-pointer"
            variant="rounded"
            style={{
              backgroundColor: bgColors,
              color,
            }}
          >
            <div className="f-12">{tagName}</div>
          </Avatar>
          <div className="ml-2">
            <div className="f-14 fw-500">{SettingTitle}</div>
            <p className="f-12 fw-400 m-0">{Action}</p>
          </div>
        </section>
      </AccordionSummary>
      <AccordionDetails>{children}</AccordionDetails>
    </Accordion>
  );
};
//----------------------------------componentEnd----------------------------------------------------
//----------------------------------UI Utils Start--------------------------------------------------

// const switchSX = {
//   '& .MuiSwitch-thumb': { background: '#147afc', width: '17px', height: '17px' },
//   '& .MuiSwitch-track': {
//     height: '85%',
//     width: '90%',
//     background: '#D1D5DB !important',
//   },
//   '& .MuiSwitch-switchBase:not(.Mui-checked) .MuiSwitch-thumb': {
//     background: '#F3F4F6',
//   },
// };
export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '14px', // Adjust the font size as needed
    color: '#4B5563',
    fontWeight: 400,
    // You can also add other styles here, such as fontFamily, fontWeight, etc.
  },
}));

const cancelButtonSx = {
  backgroundColor: '#FFFFFF !important',
  border: '1px solid #D1D5DB',
  color: '#374151',
  padding: '8px, 24px, 8px, 24px',
};
const saveButtonSx = {
  backgroundColor: '#147AFC !important',
  color: '#FFFFFF',
  padding: '8px, 24px, 8px, 24px',
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
let settingsData = [
  {
    SettingTitle: 'Q360-Tags',
    Action: 'Create the tags',
    tagName: 'TA',
    buttonName: 'Add New Tag',
    popTitle: 'Create Q360-Tags',
    color: '#2159BA',
    bgColors: '#E1F5FA',
    component: TagComponent,
  },
  {
    SettingTitle: 'Attempt Type',
    tagName: 'AT',
    Action: 'Create attempt type for the reports',
    buttonName: 'Add New Type',
    popTitle: 'Create Attempt Type',
    color: '#166534',
    bgColors: '#BBF7D0',
    component: AttemptComponent,
  },
];
//----------------------------------JS Utils End----------------------------------------------------
function Q360CreateSettings() {
  const [expanded, setExpanded] = useState(null);
  const dispatch = useDispatch();
  const history = useHistory();
  const dataRef = useRef(new Map());
  const qaPcSetting = useSelector(selectQaPcSetting);
  const tagsData = qaPcSetting.get('tagData', List());
  const attemptTypeData = qaPcSetting.get('attemptData', List());
  //save setting with SaveSetting Api
  function handleSave() {
    const tags = dataRef.current.get('tags', List());
    const attemptType = dataRef.current.get('attemptType', List());
    const tagsList = filteredTypes(tags);
    const attemptTypeList = filteredTypes(attemptType);
    const data = {
      tags: tagsList.filteredData.toJS(),
      attemptType: attemptTypeList.filteredData.toJS(),
    };
    const callBack = () => {
      history.push(
        '/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration+configure_template'
      );
    };
    dispatch(qaPcSaveSetting(data, callBack));
  }
  const editAccess = useCheckEditAccess('isEditAccessConfigureTemplate');

  useEffect(() => {
    dispatch(getQaPcSetting());
  }, []);
  return (
    <div>
      <section className="d-flex align-items-center cursor-pointer setting_icon_position">
        {editAccess && (
          <div className="ml-auto mr-3">
            <Button variant="outlined" sx={cancelButtonSx} onClick={() => history.goBack()}>
              Cancel
            </Button>
            <Button variant="contained" className="ml-3" onClick={handleSave} sx={saveButtonSx}>
              Save
            </Button>
          </div>
        )}
      </section>
      <section className="QAPCchild-container p-3 text-dGrey ">
        {settingsData.map((settingValue, index) => {
          const Component = settingValue.component;
          const type = settingValue.SettingTitle;
          const { ...props } = settingValue;
          return (
            <SkeletonTemplate
              key={index}
              summaryDetails={settingValue}
              type={type}
              expanded={expanded}
              setExpanded={setExpanded}
            >
              <Component
                editAccess={editAccess}
                {...props}
                tags={tagsData}
                attemptType={attemptTypeData}
                ref={dataRef}
              />
            </SkeletonTemplate>
          );
        })}
      </section>
    </div>
  );
}
export default Q360CreateSettings;
