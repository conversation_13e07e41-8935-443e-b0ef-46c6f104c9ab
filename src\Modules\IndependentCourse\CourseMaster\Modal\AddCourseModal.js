import React, { useState } from 'react';
import { Map } from 'immutable';
// import { Modal } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { Header, Footer, Body } from './ModalComponent';
import Cancel from 'Containers/Modal/Cancel';
import Dialog from '@mui/material/Dialog';
import Fade from '@mui/material/Fade';
import { useStylesFunction } from '../../../ProgramInput/v2/piUtil';

const AddCourseModal = ({ onClose, show, onSave, isEdit, course }) => {
  const classes = useStylesFunction();
  const [showCancel, setCancel] = useState(false);
  const [isEdited, setIsEdited] = useState(false);
  const { saveDetails, onClickSave } = onSave;
  const enableSaveButton = () => {
    if (!isEdited) setIsEdited(true);
  };
  const onClickCancel = () => {
    if (isEdited) setCancel(true);
    else onClose();
  };

  return (
    <>
      {showCancel && <Cancel showCancel={showCancel} setCancel={setCancel} setShow={onClose} />}

      <Dialog
        className={classes.modalNew}
        open={show}
        onClose={() => {}}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <Fade in={show}>
          <div className={classes.paperNew}>
            <Header />
            <Body
              saveDetails={saveDetails}
              enableSaveButton={enableSaveButton}
              isEdit={isEdit}
              course={course}
            />
            <Footer
              onClickFunc={{ onClickCancel, onClickSave }}
              isEdit={isEdit}
              isEdited={isEdited}
            />
          </div>
        </Fade>
      </Dialog>
    </>
  );
};
AddCourseModal.propTypes = {
  onClose: PropTypes.func,
  onSave: PropTypes.object,
  course: PropTypes.instanceOf(Map),
  show: PropTypes.bool,
  isEdit: PropTypes.bool,
};
export default AddCourseModal;
