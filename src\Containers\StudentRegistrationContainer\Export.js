import React from 'react';
import Snackbars from '../../Modules/Utils/Snackbars';
import { selectMessage, selectIsLoading } from '../../_reduxapi/user_management/selectors';
import { connect } from 'react-redux';
import * as actions from '../../_reduxapi/user_management/action';
import PropTypes from 'prop-types';
import Loader from '../../Widgets/Loader/Loader';
import { Trans } from 'react-i18next';
const Export = ({ userType, status, message, exportHandler, isLoading }) => {
  return (
    <>
      <button
        type="button"
        className="btn btn-outline-primary pr-2.5 mr-3"
        onClick={() => exportHandler(userType, status)}
      >
        <Trans i18nKey={'program_calendar.export'} />
      </button>
      <div>{message !== '' && <Snackbars show={true} message={message} />}</div>
      <Loader isLoading={isLoading} />
    </>
  );
};

const mapStateToProps = function (state) {
  return {
    message: selectMessage(state),
    isLoading: selectIsLoading(state),
  };
};

Export.propTypes = {
  exportHandler: PropTypes.func,
  message: PropTypes.string,
  userType: PropTypes.string,
  status: PropTypes.string,
  isLoading: PropTypes.bool,
};

export default connect(mapStateToProps, actions)(Export);
