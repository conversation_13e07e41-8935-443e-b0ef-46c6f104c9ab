import React, { useState, useEffect, useContext, Suspense } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import MButton from 'Widgets/FormElements/material/Button';
import ApprovalLevelPopup from './Modal/ApprovalLevelModal';
import * as actions from '_reduxapi/leave_management/actions';
import {
  selectLmsSettings,
  selectRolesList,
  selectUserList,
} from '_reduxapi/leave_management/selectors';
import { fromJS, List, Map, Set } from 'immutable';
import { approvalRoleConfigurations, turnAroundTime, permissionValidation } from '../utils';
import LevelDetails from './LevelDetails';
import { ClassificationsContext } from './Classifications';
import ExceptionProgramModal from './Modal/ExceptionProgramModal';
const DeleteModal = React.lazy(() => import('./Modal/DeleteModal'));

const defaultDetails = {
  category: 'Role Based',
  approvalConfiguration: approvalRoleConfigurations(List())[0].value,
  turnAroundTime: turnAroundTime[0].value,
  level: '',
  role: [],
  program: [],
  user: [],
  gender: 'BOTH',
  skipPreviousLevelApproval: false,
};

const ApprovalLevelAndRoles = ({
  getLmsSettings,
  getRolesList,
  roleList,
  updateLevel,
  userList,
  getUser,
  lmsSettings,
  setData,
  updateGenderSegregation,
  updateExceptionalProgram,
  isEditApproveLevels,
  deleteExceptional,
  isEditGeneralConfig,
}) => {
  const [open, setOpen] = useState(false);
  const [deleteShow, setDeleteShow] = useState(false);
  const [programDeleteShow, setProgramDeleteShow] = useState(false);
  const [levelId, setLevelId] = useState('');
  const [searchKey, setSearchKey] = useState('');
  const [operation, setOperation] = useState('');
  const [levelApproverId, setLevelApproverId] = useState('');
  const [permissionApproval, setPermissionApproval] = useState(fromJS(defaultDetails));
  const [programOpen, setProgramOpen] = useState(false);
  const [programIds, setProgramIds] = useState(List());
  const [searchProgram, setSearchProgram] = useState('');
  const [toBeDeletedName, setToBeDeletedName] = useState('');
  const [allowSkipping, setAllowSkipping] = useState(false);
  const [addGender, setGender] = useState('');

  const { type } = useContext(ClassificationsContext);

  const categoryType = permissionApproval.get('category', '');

  useEffect(() => {
    if (open) {
      getRolesList('roleAccess');
      // getUser({ searchKey: searchKey });
    }
  }, [open]); //eslint-disable-line

  useEffect(() => {
    if (open && categoryType === 'User Based') {
      const timeout = setTimeout(() => {
        getUser({ searchKey: searchKey });
      }, 500);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [searchKey, open]); //eslint-disable-line

  const levelApprover = lmsSettings.get('levelApprover', List());

  const handleClickOpen = (id) => {
    setOperation('create');
    setOpen(true);
    setLevelApproverId(id);
  };
  const handleClose = () => {
    setOpen(false);
    setPermissionApproval(fromJS(defaultDetails));
  };

  const filterSelectedProgram = (id) => {
    const index = levelApprover.findIndex((d) => d.get('_id', '') === id);
    return levelApprover.getIn([index, 'programIds']).map((item) => item.get('_id', ''));
  };

  const handleClickOpenProgram = (key, id) => {
    setProgramOpen(true);
    setOperation(key);
    if (key === 'update') {
      const allProgramIds = filterSelectedProgram(id);
      setProgramIds(allProgramIds);
      setLevelApproverId(id);
    }
  };

  const handleCloseProgram = () => {
    setProgramOpen(false);
    setLevelApproverId('');
    setProgramIds(List());
    setSearchProgram('');
  };

  const preparedOption = (itemList, type = '') => {
    return itemList.map((item) => {
      return Map({
        title:
          type === 'user'
            ? item.getIn(['name', 'first'], '') + item.getIn(['name', 'last'], '')
            : item.get('name', ''),
        value: item.get('_id', ''),
      });
    });
  };

  const getOptions = (name) => {
    if (name === 'role') return preparedOption(roleList);
    if (name === 'user') return preparedOption(userList, 'user');
  };

  const handleAutoCompleteChange = (val, type) => {
    setPermissionApproval(permissionApproval.set(type, fromJS(val)));
  };

  const callBack = () => {
    getLmsSettings(type);
    setOpen(false);
    setDeleteShow(false);
    setProgramDeleteShow(false);
    setPermissionApproval(fromJS(defaultDetails));
    handleCloseProgram();
  };

  const handleSubmit = (name, _levelId = '', gender = '') => {
    if (name === 'exceptional') {
      const allProgramIds = filterSelectedProgram(levelApproverId);
      const requestData = {
        isExceptionalProgram: true,
        programIds: programIds,
        classificationType: type,
        ...(operation === 'update' && {
          levelApproverId: levelApproverId,
          removedProgramIds: allProgramIds.filter((item) => !programIds.includes(item)),
          addedProgramIds: programIds,
        }),
      };
      updateExceptionalProgram(requestData, callBack, operation);
    } else {
      let tempLevelName = permissionApproval.get('level', '').toLowerCase().trim();
      if (tempLevelName === '') return setData({ message: 'Level Name is Required' });

      let tempApprover = levelApprover
        .filter((data) => data.get('_id', '') === levelApproverId)
        .getIn([0, 'level'], List());

      let currentName = [];
      let existingName = [];

      // eslint-disable-next-line
      tempApprover.map((data) => {
        if (_levelId) {
          switch (gender) {
            case 'BOTH':
              data.get('_id', '') === _levelId
                ? currentName.push(data.get('levelName', '').toLowerCase().trim())
                : existingName.push(data.get('levelName', '').toLowerCase().trim());
              break;

            case 'F':
              if (data.get('gender', '') === 'F')
                data.get('_id', '') === _levelId
                  ? currentName.push(data.get('levelName', '').toLowerCase().trim())
                  : existingName.push(data.get('levelName', '').toLowerCase().trim());
              break;

            case 'M':
              if (data.get('gender', '') === 'M')
                data.get('_id', '') === _levelId
                  ? currentName.push(data.get('levelName', '').toLowerCase().trim())
                  : existingName.push(data.get('levelName', '').toLowerCase().trim());
              break;
            default:
          }
        } else {
          switch (addGender) {
            case 'BOTH':
              existingName.push(data.get('levelName', '').toLowerCase().trim());
              break;
            case 'Male':
              data.get('gender', '') === 'M' &&
                existingName.push(data.get('levelName', '').toLowerCase().trim());
              break;
            case 'Female':
              data.get('gender', '') === 'F' &&
                existingName.push(data.get('levelName', '').toLowerCase().trim());
              break;
            default:
          }
        }
      });

      if (existingName.includes(tempLevelName))
        return setData({ message: 'Level Name Already Exists' });

      const levelDetails = {
        levelName: permissionApproval.get('level', ''),
        approvalConfig:
          permissionApproval.get('role', '').size > 1
            ? permissionApproval.get('approvalConfiguration', '')
            : 'Approval Required From Any One In Any Role',
        categoryBased: permissionApproval.get('category', ''),
        ...(permissionApproval.get('category', '') === 'User Based' && {
          userIds: permissionApproval.get('user', '').map((item) => item.get('value', '')),
        }),
        ...(permissionApproval.get('category', '') === 'Role Based' && {
          roleIds: permissionApproval.get('role', '').map((item) => item.get('value', '')),
        }),
        turnAroundTime: permissionApproval.get('turnAroundTime', ''),
        escalateRequest: permissionApproval.get('escalateRequest', false),
        skipPreviousLevelApproval: permissionApproval.get('skipPreviousLevelApproval', false),
        overwritePreviousLevelApproval: permissionApproval.get(
          'overwritePreviousLevelApproval',
          false
        ),
        gender:
          operation !== 'update'
            ? permissionApproval.get('gender', '') === 'Male'
              ? 'M'
              : permissionApproval.get('gender', '') === 'Female'
              ? 'F'
              : 'BOTH'
            : permissionApproval.get('gender', ''),
        ...(operation === 'update' && {
          levelId: permissionApproval.get('levelId', ''),
          settingId: lmsSettings.get('_id'),
        }),
      };

      const requestData = {
        settingId: lmsSettings.get('_id'),
        levelApproverId: levelApproverId,
        level: levelDetails,
      };

      if (permissionValidation(permissionApproval, setData)) {
        updateLevel(operation === 'update' ? levelDetails : requestData, operation, callBack);
      }
    }
  };

  const handleEdit = (levelId, levelApproverId) => {
    const index = levelApprover.findIndex((d) => d.get('_id') === levelApproverId);
    const levelApproverDetails = levelApprover
      .getIn([index, 'level'])
      .find((item) => item.get('_id', '') === levelId);

    setPermissionApproval(
      permissionApproval
        .set('levelApproverId', levelApproverId)
        .set('level', levelApproverDetails.get('levelName'))
        .set('levelId', levelApproverDetails.get('_id'))
        .set('type', levelApproverDetails.get('levelTypeBy'))
        .set('approvalConfiguration', levelApproverDetails.get('approvalConfig'))
        .set('category', levelApproverDetails.get('categoryBased'))
        .set('turnAroundTime', levelApproverDetails.get('turnAroundTime'))
        .set('escalateRequest', levelApproverDetails.get('escalateRequest'))
        .set('skipPreviousLevelApproval', levelApproverDetails.get('skipPreviousLevelApproval'))
        .set('gender', levelApproverDetails.get('gender'))
        .set(
          'overwritePreviousLevelApproval',
          levelApproverDetails.get('overwritePreviousLevelApproval')
        )
        .set(
          'role',
          levelApproverDetails.get('roleIds', List()).map((item) => {
            return Map({
              title: item.get('name', ''),
              value: item.get('_id', ''),
            });
          })
        )
        .set(
          'user',
          levelApproverDetails.get('userIds', List()).map((item) => {
            return Map({
              title: item.getIn(['name', 'first'], '') + item.getIn(['name', 'last'], ''),
              value: item.get('_id', ''),
            });
          })
        )
    );
    setOpen(true);
    setLevelId(levelId);
    setLevelApproverId(levelApproverId);
    setOperation('update');
  };

  const handleChange = (e, value) => {
    if (value === 'selectAll') {
      const index = levelApprover.findIndex((d) => d.get('_id') === levelApproverId);
      let levelApproverData = List();
      if (operation === 'update') {
        levelApproverData = levelApprover
          .getIn([index, 'programIds'], List())
          .merge(levelApprover.getIn([0, 'programIds'], List()));
      } else {
        levelApproverData = levelApprover.getIn([0, 'programIds'], List());
      }
      if (searchProgram) {
        levelApproverData = levelApproverData.filter((s) =>
          s.get('name', '').toLowerCase().includes(searchProgram.toLowerCase())
        );
      }
      const allProgramIds = !searchProgram
        ? levelApproverData.map((item) => item.get('_id', ''))
        : levelApproverData.map((item) => item.get('_id', '')).merge(programIds);
      if (e.target.checked) setProgramIds(Set(allProgramIds).toList());
      else setProgramIds(List());
    } else {
      if (e.target.checked) setProgramIds(programIds.push(value));
      else setProgramIds(programIds.filter((i) => i !== value));
    }
  };

  const handleDelete = () => {
    const requestData = {
      settingId: lmsSettings.get('_id'),
      levelId: levelId,
      levelApproverId: levelApproverId,
    };
    updateLevel(requestData, 'delete', callBack);
  };

  const handleChangeEscalate = (e, levelId) => {
    setPermissionApproval(permissionApproval.set('escalateRequest', e.target.checked));
    const requestData = {
      escalateRequest: e.target.checked,
      levelId: levelId,
      settingId: lmsSettings.get('_id'),
    };
    updateLevel(requestData, 'update', callBack);
  };

  const handleSwitch = (e, id) => {
    const requestData = {
      classificationType: type,
      genderSegregation: e.target.checked,
      levelApproverId: id,
    };
    updateGenderSegregation(requestData, () => {
      getLmsSettings(type);
    });
  };

  const handleDeleteProgram = () => {
    const requestData = {
      settingId: lmsSettings.get('_id'),
      levelApproverId: levelApproverId,
    };
    deleteExceptional(requestData, () => {
      getLmsSettings(type);
      callBack();
    });
  };

  const value = {
    levelApprover,
    handleEdit,
    setShow: setDeleteShow,
    setProgramDeleteShow,
    setLevelId,
    handleChangeEscalate,
    handleClickOpen,
    handleSwitch,
    setLevelApproverId,
    permissionApproval,
    setPermissionApproval,
    levelApproverId,
    handleClickOpenProgram,
    updateGenderSegregation,
    getLmsSettings,
    type,
    setToBeDeletedName,
    setAllowSkipping,
  };

  return (
    <ClassificationsContext.Provider value={value}>
      <LevelDetails
        isEditApproveLevels={isEditApproveLevels}
        isEditGeneralConfig={isEditGeneralConfig}
        setGender={setGender}
      />
      <MButton
        disabled={type === 'leave' ? !isEditGeneralConfig : !isEditApproveLevels}
        variant="contained"
        color={'blue'}
        clicked={() => handleClickOpenProgram('create')}
      >
        Add Exception Program
      </MButton>
      {programOpen && (
        <ExceptionProgramModal
          open={programOpen}
          handleClose={handleClickOpenProgram}
          levelApprover={levelApprover}
          permissionApproval={permissionApproval}
          handleChange={handleChange}
          programIds={programIds}
          handleSubmit={handleSubmit}
          levelApproverId={levelApproverId}
          handleCloseProgram={handleCloseProgram}
          operation={operation}
          searchProgram={searchProgram}
          setSearchProgram={setSearchProgram}
          setProgramIds={setProgramIds}
        />
      )}
      {open && (
        <ApprovalLevelPopup
          open={open}
          handleClose={handleClose}
          permissionApproval={permissionApproval}
          setPermissionApproval={setPermissionApproval}
          handleSubmit={handleSubmit}
          getOptions={getOptions}
          handleChange={handleAutoCompleteChange}
          setSearchKey={setSearchKey}
          searchKey={searchKey}
          allowSkipping={allowSkipping}
        />
      )}
      {deleteShow && (
        <Suspense fallback="">
          <DeleteModal
            show={deleteShow}
            setShow={setDeleteShow}
            handleDelete={handleDelete}
            name={'Level'}
            toBeDeletedName={toBeDeletedName}
          />
        </Suspense>
      )}
      {programDeleteShow && (
        <Suspense fallback="">
          <DeleteModal
            show={programDeleteShow}
            setShow={setProgramDeleteShow}
            handleDelete={handleDeleteProgram}
            name={'Exceptional Program'}
            toBeDeletedName={'Program'}
          />
        </Suspense>
      )}
    </ClassificationsContext.Provider>
  );
};

ApprovalLevelAndRoles.propTypes = {
  getLmsSettings: PropTypes.func,
  lmsSettings: PropTypes.instanceOf(Map),
  getRolesList: PropTypes.func,
  roleList: PropTypes.instanceOf(List),
  updateLevel: PropTypes.func,
  userList: PropTypes.instanceOf(List),
  getUser: PropTypes.func,
  isEditApproveLevels: PropTypes.bool,
  setData: PropTypes.func,
  updateGenderSegregation: PropTypes.func,
  updateExceptionalProgram: PropTypes.func,
  deleteExceptional: PropTypes.func,
  isEditGeneralConfig: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    lmsSettings: selectLmsSettings(state),
    roleList: selectRolesList(state),
    userList: selectUserList(state),
  };
};

export default connect(mapStateToProps, actions)(ApprovalLevelAndRoles);
