import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Tabs, Tab } from 'react-bootstrap';
import { withRouter } from 'react-router-dom';
import Loader from '../../Widgets/Loader/Loader';
import ValideStudentProfile from '../../Components/StudentMangement/validStudentManagement/valideStudentView';
import ValideBiometric from '../../Components/StaffManagement/validStaffManagement/valideBiometric';
import axios from '../../axios';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { Map } from 'immutable';

class valideContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      key: 1,
      programName: '',
      isLoading: false,
      totalDoc: '',
      tabs: '',
      userStatusStudent: '',
      userTypeStudent: '',
      userAcademicId: '',
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let id = params.get('id');
    let biometric = params.get('biometric');
    let tabs = params.get('tabs');
    this.setState({ tabs: tabs });
    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';
    if (biometric) {
      this.setState({ key: 2 });
    } else {
      if (
        CheckPermission(
          'tabs',
          'User Management',
          'Student Management',
          '',
          permissionName,
          'Profile View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          permissionName,
          'Profile View'
        )
      ) {
        this.setState({ key: 1 });
      } else if (
        CheckPermission(
          'tabs',
          'User Management',
          'Student Management',
          '',
          permissionName,
          'Biometric View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          permissionName,
          'Biometric View'
        )
      ) {
        this.setState({ key: 2 });
      }
    }
    this.fetchApi(id);
  }

  fetchApi = (id) => {
    axios.get(`/user/student/${id}`).then((res) => {
      const data = res.data.data;
      this.setState({
        userStatusStudent: data.status,
        userTypeStudent: data.user_type,
        userAcademicId: data.academic,
      });
    });
  };

  handleSelect = (key) => {
    this.setState({ key: key });
  };

  handleGoBack = () => {
    this.props.history.push({
      pathname: '/student/management',
      state: {
        completeView: false,
        pendingView: true,
        inactiveView: false,
        selectedTab:
          this.state.tabs !== '' && this.state.tabs !== undefined ? parseInt(this.state.tabs) : 5,
      },
    });
  };

  render() {
    const { loggedInUserData } = this.props;
    const { tabs } = this.state;
    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';
    return (
      <React.Fragment>
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'user_management.student_management_profile'}></Trans>{' '}
        </div>

        <React.Fragment>
          <Tabs activeKey={this.state.key} onSelect={this.handleSelect} id="tab-example">
            {(CheckPermission(
              'tabs',
              'User Management',
              'Student Management',
              '',
              permissionName,
              'Profile View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Student Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Profile View'
              )) && (
              <Tab eventKey={1} title={<Trans i18nKey={'user_management.profile'}></Trans>}>
                <ValideStudentProfile
                  handleGoBack={this.handleGoBack}
                  userStatus={this.state.userStatusStudent}
                />
              </Tab>
            )}
            {loggedInUserData.get('studentFacial', false) &&
              (CheckPermission(
                'tabs',
                'User Management',
                'Student Management',
                '',
                permissionName,
                'Biometric View'
              ) ||
                CheckPermission(
                  'subTabs',
                  'User Management',
                  'Student Management',
                  '',
                  'Registration Pending',
                  '',
                  permissionName,
                  'Biometric View'
                )) && (
                <Tab eventKey={2} title={<Trans i18nKey={'user_management.biometrics'}></Trans>}>
                  <ValideBiometric
                    handleGoBack={this.handleGoBack}
                    biometricName="student"
                    userStatusStudent={this.state.userStatusStudent}
                    userTypeStudent={this.state.userTypeStudent}
                    userAcademicId={this.state.userAcademicId}
                  />
                </Tab>
              )}
          </Tabs>
        </React.Fragment>

        <Loader isLoading={this.state.isLoading} />
      </React.Fragment>
    );
  }
}

valideContainer.propTypes = {
  history: PropTypes.object,
  loggedInUserData: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(valideContainer));
