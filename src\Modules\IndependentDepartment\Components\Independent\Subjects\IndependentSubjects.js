import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import SubjectName from 'Modules/ProgramInput/v2/ConfigurationIndex/Departments/Components/DepartmentSubjects/Subjects/SubjectName';
import { useStylesFunction } from 'Modules/ProgramInput/v2/piUtil';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import SubjectBody from 'Modules/ProgramInput/v2/ConfigurationIndex/Departments/Components/DepartmentSubjects/Subjects/SubjectBody';
import SubjectActions from 'Modules/ProgramInput/v2/ConfigurationIndex/Departments/Components/DepartmentSubjects/Subjects/SubjectActions/SubjectActions';
import { Trans } from 'react-i18next';

function IndependentSubjects({ departmentData, translateInput, sharedFromDepartment }) {
  const classes = useStylesFunction();
  const addDepartment = useContext(parentContext.departmentContext);
  const { departmentTabValue } = addDepartment;

  function displaySharedFrom(item) {
    return (
      <>
        <div className={'d-flex flex-wrap align-items-center f-16'}>
          <Trans i18nKey={'shared_from_col'}></Trans>
          <span className="digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded break-word">
            <span className="digi-course-bg digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded break-word">
              {item.get('programName', '') || 'Admin'} / {item.get('departmentName', '')}
            </span>
          </span>
        </div>
      </>
    );
  }
  return (
    <>
      {departmentData.get('subject', List()).map((item, index) => (
        <div key={index} className="accordion_hover d-flex">
          <>
            <div
              className={`${
                departmentTabValue === 'admin'
                  ? classes.IndependentHeadingAdmin
                  : classes.IndependentHeading
              }`}
            >
              {' '}
            </div>
            {departmentTabValue === 'academic' && (
              <div className={classes.IndependentSecond}> </div>
            )}
            <div className={`${classes.departmentSecondHeading} d-flex align-items-center`}>
              {' '}
              <SubjectName name={item.get('subjectName', '')} />
              <div className={`d-flex justify-content-between ${classes.departmentSecondHeading}`}>
                <SubjectBody
                  subjectData={item}
                  departmentId={departmentData.get('_id', '')}
                  departmentData={departmentData}
                />
                {!sharedFromDepartment ? (
                  <SubjectActions
                    subjectData={item}
                    departmentName={departmentData.get('departmentName', '')}
                    departmentId={departmentData.get('_id', '')}
                    translateInput={translateInput}
                  />
                ) : (
                  ''
                )}
              </div>
            </div>
          </>
        </div>
      ))}
      {departmentData.get('sharedSubjects', List()).map((item, index) => (
        <div key={index} className="accordion_hover d-flex">
          <>
            <div
              className={`${
                departmentTabValue === 'admin'
                  ? classes.IndependentHeadingAdmin
                  : classes.IndependentHeading
              }`}
            >
              {' '}
            </div>
            {departmentTabValue === 'academic' && (
              <div className={classes.IndependentSecond}> </div>
            )}
            <div className={`${classes.departmentSecondHeading} d-flex align-items-center`}>
              {' '}
              <SubjectName name={item.get('subjectName', '')} />
              <div className={`d-flex justify-content-between ${classes.departmentSecondHeading}`}>
                {displaySharedFrom(item)}
              </div>
            </div>
          </>
        </div>
      ))}
    </>
  );
}

IndependentSubjects.propTypes = {
  departmentData: PropTypes.instanceOf(Map),
  translateInput: PropTypes.object,
  sharedFromDepartment: PropTypes.bool,
};
export default IndependentSubjects;
