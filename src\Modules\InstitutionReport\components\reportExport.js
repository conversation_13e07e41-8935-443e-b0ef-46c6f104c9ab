import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import {
  AlphabetsArray,
  getCollegeLogo,
  getTimestamp,
  getVersionName,
  isIndGroup,
  studentGroupRename,
  studentGroupViewList,
} from 'utils';
import { jsUcfirstAll } from 'utils';
import { List } from 'immutable';
import moment from 'moment';
import { getNonStartedSchedule, getStatusColor } from '../utils';
import { getFormattedGroupName } from '../../CourseScheduling/components/utils';
import { fromJS } from 'immutable';
import { getEnvCollegeName } from '../../../utils';
export const exportExcel = async ({
  scheduleList,
  updatedDate,
  startTime,
  endTime,
  startDate,
  endDate,
  exportCurrentPage,
  type,
}) => {
  const addRemarkStatus = scheduleList
    .filter((item) => {
      return type === 'curricularData'
        ? ['missed', 'pending', 'cancelled'].includes(item.get('status')) ||
            item.get('isActive', false) === false
        : true;
    })
    .filter((item) =>
      (type === 'curricularData' && item.get('status', '') === 'pending') ||
      item.get('isActive', false) === false
        ? item.get('isActive', false) === false
          ? item.get('isActive', false) === false
          : getNonStartedSchedule(item)
        : true
    )
    .map((item) => {
      if (
        exportCurrentPage.get('lateStarted', '') !== '' &&
        item.getIn(['sessionDetail', 'start_time'], '') !== ''
      ) {
        const lateStartCheckTime = moment(item.get('scheduleStartDateAndTime', new Date()))
          .utc()
          .add(exportCurrentPage.get('lateStarted', ''), 'minutes')
          .format();
        return Date.parse(lateStartCheckTime) <
          Date.parse(item.getIn(['sessionDetail', 'start_time'], ''))
          ? item.set('sessionStatus', fromJS(['Late Started']))
          : item;
      }
      return item;
    })
    .map((item) => {
      if (
        exportCurrentPage.get('earlyEnded', '') !== '' &&
        item.getIn(['sessionDetail', 'stop_time'], '') !== '' &&
        item.get('status', '') === 'completed'
      ) {
        const earlyEndCheckTime = moment(item.get('scheduleEndDateAndTime', new Date()))
          .utc()
          .subtract(exportCurrentPage.get('earlyEnded', ''), 'minutes')
          .format();
        let sessionStatus = item.get('sessionStatus', List()).toJS();
        sessionStatus = [...sessionStatus, 'Early Ended'];
        return Date.parse(earlyEndCheckTime) >
          Date.parse(new Date(item.getIn(['sessionDetail', 'stop_time'], '')))
          ? item.set('sessionStatus', fromJS(sessionStatus))
          : item;
      }
      return item;
    })
    .map((item) => {
      let sessionStatus = item.get('sessionStatus', List()).toJS();
      sessionStatus = [...sessionStatus, 'Merged'];
      return item.get('merge_with', List()).size > 0
        ? item.set('sessionStatus', fromJS(sessionStatus))
        : item;
    });
  const workbook = new ExcelJS.Workbook();
  let worksheet = workbook.addWorksheet('All Deliveries');
  worksheet.columns = [
    { width: 5 },
    { width: 23 },
    { width: 14 },
    { width: 14 },
    { width: 10 },
    { width: 30 },
    { width: 20 },
    { width: 20 },
    { width: 25 },
    { width: 15 },
    { width: 20 },
    { width: 10 },
    { width: 15 },
    { width: 40 },
    { width: 13 },
    { width: 13 },
    { width: 15 },
    { width: 15 },
    { width: 15 },
    { width: 13 },
    { width: 15 },
    { width: 30 },
  ];
  const headerWithKeys = [
    {
      header: 'S.No',
      key: 'S.No',
      width: 30,
    },
    {
      header: 'Program Name',
      key: 'Program Name',
      width: 40,
    },
    {
      header: 'Term',
      key: 'Term',
      width: 30,
    },
    {
      header: 'Year',
      key: 'Year',
      width: 30,
    },
    {
      header: 'Level',
      key: 'Level',
      width: 30,
    },
    {
      header: 'Course Code/Name',
      key: 'Course Code/Name',
      width: 30,
    },
    {
      header: 'Category',
      key: 'Category',
      width: 30,
    },
    {
      header: 'Delivery',
      key: 'Delivery',
      width: 30,
    },
    {
      header: 'Staff',
      key: 'Staff',
      width: 30,
    },
    {
      header: 'Contact Info',
      key: 'Contact Info',
      width: 30,
    },
    {
      header: 'Student Group',
      key: 'Student Group',
      width: 30,
    },
    {
      header: 'Mode',
      key: 'Mode',
      width: 30,
    },
    {
      header: 'Infra',
      key: 'Infra',
      width: 30,
    },
    {
      header: 'Scheduled Info',
      key: 'Scheduled Info',
      width: 30,
    },
    {
      header: 'Start Time',
      key: 'Start Time',
      width: 20,
    },
    {
      header: 'End Time',
      key: 'End Time',
      width: 20,
    },
    {
      header: 'Total Strength',
      key: 'Total Strength',
      width: 20,
    },
    {
      header: 'Auto Present',
      key: 'Auto Present',
      width: 20,
    },
    {
      header: 'Manual Present',
      key: 'Manual Present',
      width: 20,
    },
    {
      header: 'Absent',
      key: 'Absent',
      width: 15,
    },
    {
      header: 'Status',
      key: 'Status',
      width: 15,
    },
    {
      header: 'Remarks',
      key: 'Remarks',
      width: 35,
    },
  ];
  function getStatusHtml(item) {
    const status = getStatusColor(item);
    return status.name;
  }

  const timeDif = (item, actualTime, sessionTime, type) => {
    const time = moment(item.get(actualTime));
    const time2 = moment(item.getIn(['sessionDetail', sessionTime], ''));
    const diffTime = time.diff(time2, 'minutes');
    return `${type}${!isNaN(Math.abs(diffTime)) ? `(${Math.abs(diffTime)} Min)` : ''}`;
  };

  const sessionStatus = (data, item) => {
    return data.includes('Late Started')
      ? timeDif(item, 'scheduleStartDateAndTime', 'start_time', 'Late Started')
      : data.includes('Early Ended')
      ? timeDif(item, 'scheduleEndDateAndTime', 'stop_time', 'Early Ended')
      : data.includes('Merged')
      ? 'Merged'
      : '';
  };

  const getDelivery = (item) => {
    if (item.get('merge_with', List()).size > 0) {
      const merged = item
        .get('merge_with', List())
        .map((m) => {
          return `${m.getIn(['schedule_id', 'session', 'delivery_symbol'], '')}${m.getIn(
            ['schedule_id', 'session', 'delivery_no'],
            ''
          )}`;
        })
        .join(', ');
      return `${item.getIn(['session', 'delivery_symbol'], ``)}${item.getIn(
        ['session', 'delivery_no'],
        ``
      )}, ${merged}`;
    } else {
      return `${item.getIn(['session', 'delivery_symbol'], ``)}${item.getIn(
        ['session', 'delivery_no'],
        ``
      )}${
        item.get('type', '') === 'regular'
          ? `-${item.getIn(['session', 'session_topic'], '')}`
          : `${item.get('title', '')}`
      }`;
    }
  };

  function getCurrentDateTime() {
    const currentTime = moment().format('H:mm:ss');
    let date = `${endDate} ${currentTime}`;
    return new Date(date.replace(' ', 'T'));
  }

  function isCurrentDate() {
    const todayDate = moment().format('YYYY-MM-DD');
    return endDate === todayDate;
  }

  const getSelectedDate = (format = 'DD-MM-YYYY') => {
    return startDate === endDate
      ? moment(startDate).format(format)
      : `${moment(startDate).format(format)} To ${moment(endDate).format(format)}`;
  };

  const currentDateTime = getTimestamp(getCurrentDateTime());

  const headerOnly = headerWithKeys.map((head) => head.key);
  const actualSize = addRemarkStatus
    .sort((a, b) => {
      if (isCurrentDate()) {
        if (
          getTimestamp(a.get('scheduleEndDateAndTime')) < currentDateTime &&
          getTimestamp(b.get('scheduleEndDateAndTime')) > currentDateTime
        )
          return 1;
        else if (
          getTimestamp(a.get('scheduleEndDateAndTime')) > currentDateTime &&
          getTimestamp(b.get('scheduleEndDateAndTime')) < currentDateTime
        )
          return -1;
        else if (
          getTimestamp(a.get('scheduleEndDateAndTime')) <
          getTimestamp(b.get('scheduleEndDateAndTime'))
        )
          return -1;
      }
      return 1;
    })
    .filter((localFilter) =>
      exportCurrentPage.get('tableLocalFilter', List()).size > 0
        ? exportCurrentPage
            .get('tableLocalFilter', List())
            .map((item) => item.get('_id', ''))
            .includes(localFilter.get('_id', ''))
        : true
    )
    .filter((_, i) =>
      exportCurrentPage.get('pageType', '') === 'currentPage'
        ? i >=
            exportCurrentPage.get('pageCount', '') *
              (exportCurrentPage.get('currentPage', '') - 1) &&
          i < exportCurrentPage.get('pageCount', '') * exportCurrentPage.get('currentPage', '')
        : true
    );
  const filter = actualSize
    .map((item, index) => [
      index + 1,
      jsUcfirstAll(item.get('program_name', '')),
      jsUcfirstAll(item.get('term', '')),
      jsUcfirstAll(
        item.get('year_no', '') && 'Year ' + item.get('year_no', '').replace('year', '')
      ),
      item.get('level_no', ''),
      jsUcfirstAll(
        item.get('course_code', '') + ` - ` + item.get('course_name', '') + getVersionName(item)
      ),
      jsUcfirstAll(
        item.get('type', '') === 'support_session'
          ? 'support session' + item.get('type', '').replace('support_session', '')
          : item.get('type', '')
      ),
      getDelivery(item),
      item
        .get('staffs', '')
        .map((data) =>
          jsUcfirstAll(
            `${data.getIn(['staff_name', 'first'], '')} ${data.getIn(
              ['staff_name', 'middle'],
              ''
            )} ${data.getIn(['staff_name', 'last'], '')}`.toLowerCase()
          )
        )
        .join(', '),
      item
        .get('staffs', '')
        .map((data) => data.getIn(['_staff_id', 'mobile'], ''))
        .join(', '),
      item.get('type', '') !== 'regular'
        ? studentGroupViewList(item.get('student_groups', List()), item.get('_program_id', ''))
            .entrySeq()
            .map(
              ([groupName, sGroup]) =>
                getFormattedGroupName(
                  studentGroupRename(groupName, item.get('_program_id', '')),
                  2
                ) + `-${sGroup.get('session_group')}`
            )
            .join(', ')
        : item
            .get('student_groups', List())
            .map((studentGroup) => {
              const studentGroupName = getFormattedGroupName(
                studentGroupRename(studentGroup.get('group_name', ''), item.get('_program_id', '')),
                isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
              );
              const sessionGroupNames = studentGroup
                .get('session_group', List())
                .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
                .join(', ');
              return `${studentGroupName} - ${sessionGroupNames}`;
            })
            .join(', '),
      jsUcfirstAll(
        item.get('scheduleStartFrom', '') === 'web'
          ? 'Onsite (Web)'
          : item.get('classModeType', '') === 'offline'
          ? 'Onsite - (Offline)'
          : item.get('mode', '')
      ),
      jsUcfirstAll(item.get('infra_name', '')),
      moment(item.get('scheduleStartDateAndTime')).format('Do MMM - YYYY') +
        ' ' +
        moment(item.get('scheduleStartDateAndTime', '')).format('h:mm A') +
        '  ' +
        ' - ' +
        ' ' +
        moment(item.get('scheduleEndDateAndTime', '')).format('h:mm A'),
      item.getIn(['sessionDetail', 'start_time'], '') !== ''
        ? moment(item.getIn(['sessionDetail', 'start_time'], '')).format('h:mm A')
        : '-',
      item.getIn(['sessionDetail', 'stop_time'], '') !== ''
        ? moment(item.getIn(['sessionDetail', 'stop_time'], '')).format('h:mm A')
        : '-',
      item.getIn(['studentAttendance', 'studentCount'], ''),
      item.getIn(['studentAttendance', 'studentAttendanceReport', 'present', 'auto'], ''),
      item.getIn(['studentAttendance', 'studentAttendanceReport', 'present', 'manual'], ''),
      item.getIn(['studentAttendance', 'studentAttendanceReport', 'absent'], 0),
      jsUcfirstAll(getStatusHtml(item)),
      item
        .get('sessionStatus', List())
        .map((data) => sessionStatus(data, item))
        .join(', '),
    ])
    .unshift([], [], [], [], [], [], [], [], []);
  worksheet.addRows(filter.toJS());

  // worksheet.duplicateRow(1, 7, true);
  worksheet.getRow(2).values = [``, ``, `       ${getEnvCollegeName()}`];
  worksheet.getRow(3).values = ``;
  worksheet.getRow(4).values = [
    ``,
    ``,
    `Report name:`,
    `${
      exportCurrentPage.get('type', '') === 'curricularData'
        ? 'Curricular Delivery Monitoring Report - Curricular Delivery Issues'
        : 'Curricular Delivery Monitoring Report - All Deliveries'
    }`,
  ];
  worksheet.getRow(5).values = [``, ``, `Selected date:`, `${getSelectedDate()}`];
  worksheet.getRow(6).values = [
    ``,
    ``,
    `Time Range:`,
    `${
      startTime && endTime
        ? moment(new Date(startTime)).format('hh:mm A') +
          ` to ` +
          moment(new Date(endTime)).format('hh:mm A')
        : `All Day`
    }`,
  ];
  if (isCurrentDate()) {
    worksheet.getRow(7).values = [``, ``, `Last Updated:`, `${updatedDate} `];
  }
  worksheet.getRow(8).values = [
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    ``,
    `           Attendance Report`,
  ];
  worksheet.getRow(9).values = headerOnly;
  const colorObject = {
    InProgress: '318fd5',
    Completed: '3b803e',
    Missed: '850000',
    Cancelled: 'ff0000',
    'Not Started': 'ffc107',
    Upcoming: '343a40',
  };
  for (let i = 10; i < actualSize.size + 10; i++) {
    const currentCell = worksheet.getCell(`U${i}`);
    const color = colorObject[currentCell.value] ? colorObject[currentCell.value] : '318fd5';
    currentCell.font = {
      color: { argb: color },
    };
  }
  for (let i = 16; i < 20; i++) {
    worksheet.getCell(AlphabetsArray[i] + '8').fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '318fd5' },
    };
    worksheet.getCell(AlphabetsArray[i] + '8').font = {
      color: { argb: 'FFFFFF' },
      bold: true,
    };
  }

  for (let i = 1; i < 10; i++) {
    for (let j = 0; j < headerWithKeys.length; j++) {
      const currentCell = `${AlphabetsArray[j] + i}`;
      if ([1, 2, 3, 9].includes(i)) {
        worksheet.getCell(currentCell).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '318fd5' },
        };
        worksheet.getCell(currentCell).font = {
          color: { argb: [2, 8, 9].includes(i) ? 'FFFFFF' : '318fd5' },
          bold: [2, 8, 9].includes(i) ? true : false,
        };
      }
      if ([4, 5, 6, 7].includes(i)) {
        worksheet.getCell(currentCell).font = {
          color: { argb: '000000' },
        };
      }
    }
  }
  for (let i = 1; i < 9; i++) {
    const currentCell = `A${i}`;
    worksheet.getCell(currentCell).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'ffffff' },
    };
    const currentCell1 = `B${i}`;
    worksheet.getCell(currentCell1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'ffffff' },
    };
  }

  function toDataURL(src, callback, outputFormat) {
    let image = new Image();

    image.crossOrigin = 'Anonymous';
    image.onload = function () {
      let canvas = document.createElement('canvas');
      let ctx = canvas.getContext('2d');
      let dataURL;
      canvas.height = this.naturalHeight;
      canvas.width = this.naturalWidth;
      ctx.drawImage(this, 0, 0);
      dataURL = canvas.toDataURL(outputFormat);
      callback(dataURL);
    };
    image.src = src;
    if (image.complete || image.complete === undefined) {
      image.src = 'data:image/gif;base64, R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==';
      image.src = src;
    }
  }
  const imageSource = getCollegeLogo();
  toDataURL(imageSource, function (dataUrl) {
    const imageId2 = workbook.addImage({
      base64: dataUrl,
      extension: 'jpeg',
    });

    worksheet.addImage(imageId2, {
      tl: { col: 0, row: 0 },
      br: { col: 2, row: 8 },
      editAs: 'absolute',
    });
    workbook.xlsx
      .writeBuffer()
      .then((buffer) =>
        saveAs(
          new Blob([buffer]),
          `${`Curricular_Delivery_Monitoring_Report_${getSelectedDate('DD_MM_YYYY').replace(
            ' ',
            '_'
          )}`}.xlsx`
        )
      )
      .catch((err) => console.log('Error writing excel export', err)); //eslint-disable-line
  });
};
