import React, { forwardRef, useEffect, useRef, useState } from 'react';

import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box, Chip, Divider, IconButton, Tooltip } from '@mui/material';
// import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';

import { Map, List, fromJS } from 'immutable';
import StepThreeDrawer from './StepThreeDrawer';
import { formatToTwoDigitString, getSections } from 'Modules/GlobalConfigurationV2/utils/jsUtils';
import { getApprovalHierarchy, setData, updateCategoryForm } from '_reduxapi/q360/actions';
import { useDispatch } from 'react-redux';
import { useCurrentPage, useSearchParams } from '../../..';
import {
  useAutoSave,
  useCallApiHook,
  useDisableElement,
  useScrollIntoView,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import LocalStorageService from 'LocalStorageService';

//----------------------------------UI Utils Start--------------------------------------------------
const closeBtnSX = {
  borderRadius: '27%',
  '&:hover': {
    backgroundColor: '#FEE2E2',
    color: '#991B1B',
  },
};
const circleChipSx = {
  '& .MuiChip-label': {
    padding: '6px',
  },
  background: '#E1F5FA',
};
const dividerSx = { borderRightWidth: 1.5 };
const ToolTipSpecificSx = { maxHeight: '100px', overflowY: 'auto' };
//----------------------------------UI Utils End----------------------------------------------------
const useApprovalHook = ({ autoSaveRef }) => {
  const dispatch = useDispatch();
  const categoryFormId = useCurrentPage('categoryFormId');
  const [approverLevel, setApproverLevel] = useState(Map());
  const addApprovalLevel = (levelIndex) => {
    setApproverLevel((prev) => {
      const newLevelName = `Level ${levelIndex + 2} Hierarchy`;

      const newLevelData = fromJS({
        name: newLevelName,
        category: prev.get('formType', '') === 'complete' ? 'entire' : 'specific',
      });

      return prev.update('approvalLevel', Map(), (approvalLevel) =>
        approvalLevel.splice(levelIndex + 1, 0, newLevelData)
      );
    });
  };
  const deleteApprovalLevel = (event, levelIndex) => {
    event.stopPropagation();
    setApproverLevel((prev) => prev.deleteIn(['approvalLevel', levelIndex]));
    autoSaveRef.current.boolean = { isEdited: true };
  };
  const handleSave = (approvalLevel, levelIndex) => {
    setApproverLevel((prev) =>
      prev.setIn(['approvalLevel', levelIndex], approvalLevel.set('levelNumber', levelIndex + 1))
    );
  };
  const onFetchSuccess = (data) => {
    let constructData = data;
    const formType = constructData.get('formType');
    const categoryFormType = constructData.get('categoryFormType');

    // if (categoryFormType === 'form') {
    const categoryValue =
      formType === 'complete' || categoryFormType === 'template' ? 'entire' : 'specific';
    constructData = constructData.update('approvalLevel', (approvalLevel) =>
      approvalLevel.map((level) => level.set('category', categoryValue))
    );
    // }
    setApproverLevel(constructData);
  };
  useEffect(() => {
    const timeout = setTimeout(() => {
      dispatch(getApprovalHierarchy(categoryFormId, onFetchSuccess));
    }, 800);
    return () => clearTimeout(timeout);
  }, []);

  return { approverLevel, addApprovalLevel, deleteApprovalLevel, handleSave };
};
const useDrawer = (initialState = null) => {
  const [drawerOpen, setDrawerOpen] = useState(initialState);
  const toggleDrawer = (level) => () => {
    setDrawerOpen(level);
  };

  return { isDrawerOpen: drawerOpen, toggleDrawer };
};
//----------------------------------componentStart--------------------------------------------------
const ToolTipSpecific = ({ approvalInfo }) => {
  const { levelData } = approvalInfo;
  const specificSections = levelData.get('specificSections', List());

  return (
    <div>
      <div className="bold f-14">Specific Sections</div>
      <Box sx={ToolTipSpecificSx}>
        {specificSections.map((sections) => (
          <div key={sections} className="f-10 ml-3">{`* ${sections}`}</div>
        ))}
      </Box>
    </div>
  );
};
const ApprovalRole = ({ approvalInfo }) => {
  const { levelData } = approvalInfo;
  const selectedRoleUser = levelData.get('selectedRoleUser', 0);
  if (!selectedRoleUser.size) {
    return <></>;
  }
  return (
    <div className="d-flex gap-5 align-items-center">
      <div className="f-14">Approval Role :</div>
      <Chip
        size="small"
        label={selectedRoleUser.size}
        className="f-12 d-flex justify-content-center"
        sx={circleChipSx}
      />
      <InfoOutlinedIcon className="f-14" />
    </div>
  );
};
const TatDays = ({ turnAroundTime }) => {
  const formattedTAT = formatToTwoDigitString(turnAroundTime);
  return (
    <div className="d-flex gap-8 align-items-center">
      <div className="f-14">
        <span className="digi-light-gray_1">TAT :</span>
        <span>{` ${formattedTAT} Days`}</span>
      </div>
      <Divider orientation="vertical" flexItem sx={dividerSx} />
    </div>
  );
};
const EntireForm = ({ /* formType, */ category }) => {
  // if (formType.toLowerCase() !== 'complete') {
  //   return <></>;
  // }
  return (
    <div className="d-flex gap-8 align-items-center">
      <div className="f-14 text-capitalize">{category} Form</div>
    </div>
  );
};
const SpecificForm = ({ approvalInfo, formType, category }) => {
  const condition = formType.toLowerCase() !== 'section';
  if (condition) {
    return <></>;
  }
  return (
    <>
      <div className="d-flex gap-8  align-items-center">
        <div className="f-14 text-capitalize">{category} Form</div>
        <div className="d-flex align-items-center gap-3">
          <Tooltip arrow placement="top" title={<ToolTipSpecific approvalInfo={approvalInfo} />}>
            <InfoOutlinedIcon className="f-14 ml-1" />
          </Tooltip>
        </div>
      </div>
    </>
  );
};

const EmbeddedIframeForm = (isEditable) => {
  const iframeRef = useRef();

  const handleIframeLoad = () => {
    if (iframeRef.current) {
      LocalStorageService.setCustomToken('sections', JSON.stringify(getSections(iframeRef)));
    }
  };
  useDisableElement(isEditable, ['step3-iframe']);
  return (
    <iframe
      ref={iframeRef}
      src={'/course-spec.html'}
      title="Face Anomaly Report"
      width="100%"
      height="100%"
      style={{ border: 'none', overflow: 'auto', display: 'none' }}
      onLoad={handleIframeLoad}
      sandbox="allow-same-origin"
      id="step3-iframe"
    />
  );
};
const ApprovalContainer = ({ children }) => {
  return <div className="d-flex flex-column align-items-center"> {children}</div>;
};
const ApprovalCard = ({ children, onClick }) => {
  return (
    <div
      className="approval-card border-bottom rounded cursor-pointer bg-white shadow-hover"
      onClick={onClick}
    >
      {children}
    </div>
  );
};
ApprovalCard.Head = ({ approvalInfo /*, approvalHook */ }) => {
  const { levelIndex } = approvalInfo;
  // const { deleteApprovalLevel } = approvalHook;

  if (levelIndex === 0) {
    return (
      <div className="text-white text-center color-blue p-1 rounded-top">Approval Hierarchy</div>
    );
  }
  return (
    <></>
    // <div className="px-3 pt-2 pb-1 rounded-top d-flex justify-content-end align-items-center">
    //   <IconButton
    //     disableRipple
    //     className="border p-1"
    //     sx={closeBtnSX}
    //     onClick={(e) => deleteApprovalLevel(e, levelIndex)}
    //   >
    //     <CloseIcon className="f-16" />
    //   </IconButton>
    // </div>
  );
};
ApprovalCard.Main = ({ approvalInfo, formType, approvalHook, isEditable }) => {
  const { levelData, levelIndex } = approvalInfo;
  const { deleteApprovalLevel } = approvalHook;

  const levelName = levelData.get('name', '');
  const turnAroundTime = levelData.get('turnAroundTime', 0);
  const category = levelData.get('category', '');
  return (
    <div className="p-3 d-flex flex-column gap-8">
      <div className="d-flex flex-column gap-20 rounded-bottom text-nowrap">
        <div className="d-flex justify-content-between">
          <div className="f-16 bold">{levelName}</div>
          <EnableOrDisable valid={!isEditable}>
            {levelIndex !== 0 && (
              <IconButton
                disableRipple
                className="p-1"
                sx={closeBtnSX}
                onClick={(e) => deleteApprovalLevel(e, levelIndex)}
              >
                <CloseIcon className="f-16" />
              </IconButton>
            )}
          </EnableOrDisable>
          {/* <CloseIcon className="f-16" onClick={(e) => deleteApprovalLevel(e, levelIndex)} /> */}

          {/* <KeyboardArrowRightIcon className="f-16" /> */}
        </div>
        <div className="d-flex gap-8 align-items-center">
          <ApprovalRole approvalInfo={approvalInfo} />
          {turnAroundTime !== 0 && (
            <>
              <TatDays turnAroundTime={turnAroundTime} />
            </>
          )}
          {category === 'specific' ? (
            <SpecificForm approvalInfo={approvalInfo} formType={formType} category={category} />
          ) : (
            <EntireForm formType={formType} category={category} />
          )}
        </div>
      </div>
    </div>
  );
};
const ApprovalArrowBtn = ({ approvalInfo, approvalHook, isEditable }) => {
  const { levelIndex } = approvalInfo;
  const { addApprovalLevel } = approvalHook;
  useDisableElement(isEditable, ['add-level-step3']);
  return (
    <div className="approval-arrow-container">
      <div className="approval-downwards-arrow">
        <div className="approval-arrow-circle"></div>
        <div className="approval-arrow-line"></div>
        <div className="approval-arrow-down"></div>
      </div>
      <div className="approval-addedBtn">
        <IconButton
          id="add-level-step3"
          disableRipple
          className="bg-goldenrod"
          onClick={() => addApprovalLevel(levelIndex)}
        >
          <AddIcon className="f-14 text-white bold" />
        </IconButton>
      </div>
    </div>
  );
};
//----------------------------------componentEnd----------------------------------------------------
const Step3 = forwardRef((_, deriveCildData) => {
  const drawerStore = useDrawer(false);
  const autoSaveRef = useRef({ boolean: { isEdited: false } });
  const approvalHook = useApprovalHook({ autoSaveRef });
  const [updateReduxData] = useCallApiHook(setData);
  const approvalLevelState = approvalHook.approverLevel.get('approvalLevel', List());
  const formType = approvalHook.approverLevel.get('formType', '');
  const categoryFormId = useCurrentPage('categoryFormId');
  const callBack = () => updateReduxData(fromJS({ categoryForms: {} }));
  autoSaveRef.current.data = { approvalLevel: approvalLevelState, categoryFormId, step: 3 };
  useAutoSave(updateCategoryForm, autoSaveRef, callBack);
  const [scrollView] = useScrollIntoView(approvalLevelState);
  deriveCildData.current = approvalLevelState;
  const [searchParams] = useSearchParams();
  const isEditable = searchParams.get('published');
  return (
    <>
      <EmbeddedIframeForm />
      <div className="d-flex flex-column align-items-center mb-3">
        {approvalLevelState.map((levelData, levelIndex) => {
          const approvalInfo = { levelData, levelIndex };
          return (
            <ApprovalContainer key={levelIndex}>
              <ApprovalCard
                onClick={drawerStore.toggleDrawer(levelIndex + 1)}
                isEditable={isEditable}
              >
                <ApprovalCard.Head approvalInfo={approvalInfo} approvalHook={approvalHook} />
                <ApprovalCard.Main
                  approvalInfo={approvalInfo}
                  formType={formType}
                  approvalHook={approvalHook}
                  isEditable={isEditable}
                />
              </ApprovalCard>
              <ApprovalArrowBtn
                approvalInfo={approvalInfo}
                approvalHook={approvalHook}
                isEditable={isEditable}
              />
            </ApprovalContainer>
          );
        })}
        <div ref={scrollView} />
      </div>
      {drawerStore.isDrawerOpen > 0 && (
        <StepThreeDrawer
          drawerStore={drawerStore}
          approvalHook={approvalHook}
          approvalLevelState={approvalLevelState}
          ref={autoSaveRef}
          isEditable={isEditable}
        />
      )}
    </>
  );
});
export default Step3;
