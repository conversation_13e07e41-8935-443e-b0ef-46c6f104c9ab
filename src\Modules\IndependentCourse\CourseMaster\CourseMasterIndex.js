import React, { useEffect, useContext } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Map, List } from 'immutable';

import {
  selectIndependentCourses,
  selectIndependentSubjects,
} from '_reduxapi/program_input/v2/selectors';
import * as actions from '_reduxapi/program_input/v2/actions';

import { independentCourseContext, courseMasterContext } from '../context';
import AddCourseMaster from './Components/AddCourseMaster';
import CourseMasterList from './Components/CourseMasterTable/CourseMasterTableIndex';
function CourseMasterIndex({
  coursesList,
  subjectsList,
  getIndependentCoursesList,
  addIndependentCourse,
  getIndependentSubjectsList,
  deleteCourseById,
  setData,
}) {
  const { institutionId } = useContext(independentCourseContext);

  const getCourseList = ({ pageNo = 1, limit = 100 }) => {
    let params = { _institution_id: institutionId, pageNo, limit };
    getIndependentCoursesList(params);
  };
  useEffect(() => getCourseList({}), []); // eslint-disable-line
  const value = {
    coursesList,
    addIndependentCourse,
    subjectsList,
    getIndependentSubjectsList,
    getCourseList,
    deleteCourseById,
    setData,
  };
  return (
    <courseMasterContext.Provider value={value}>
      {coursesList.get('courses', List()).size === 0 ? <AddCourseMaster /> : <CourseMasterList />}
    </courseMasterContext.Provider>
  );
}

const mapStateToProps = function (state) {
  return {
    coursesList: selectIndependentCourses(state),
    subjectsList: selectIndependentSubjects(state),
  };
};
CourseMasterIndex.propTypes = {
  coursesList: PropTypes.instanceOf(Map),
  subjectsList: PropTypes.instanceOf(Map),
  getIndependentCoursesList: PropTypes.func,
  setData: PropTypes.func,
  addIndependentCourse: PropTypes.func,
  getIndependentSubjectsList: PropTypes.func,
  deleteCourseById: PropTypes.func,
};
export default connect(mapStateToProps, actions)(CourseMasterIndex);
