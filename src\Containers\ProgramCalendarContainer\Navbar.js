import React from "react";
import { connect } from "react-redux";
import styled from "styled-components";

import { FlexWrapper } from "./Styled";

const NavItems = styled.div`
  margin: 20px 25px;
  color: white;
`;

const Title = styled.h3`
  flex: 1;
  color: white;
`;

const IconWrapper = styled.div`
  display: flex;
`;

const Navbar = ({ title }) => {
  return (
    <FlexWrapper className="nav_bg">
      <div>
        <NavItems>
          <i className="fas fa-bars"></i>
        </NavItems>
      </div>
      <Title>{title}</Title>
      <IconWrapper>
        <NavItems>
          <i className="fas fa-search"></i>
        </NavItems>
        <NavItems>
          <i className="fas fa-bell"></i>
        </NavItems>
        <NavItems>
          <i className="fas fa-user"></i>
        </NavItems>
      </IconWrapper>
    </FlexWrapper>
  );
};

const mapStateToProps = ({ calender: { nav_bar_title } }) => ({
  title: nav_bar_title,
});

export default connect(mapStateToProps)(Navbar);
