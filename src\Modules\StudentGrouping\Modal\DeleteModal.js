import React from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { Button, Modal } from 'react-bootstrap';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import {
  selectActiveProgram,
  selectActiveAcademicYear,
  selectDashboard,
  selectActiveTerm,
  selectActiveYear,
  selectMessage,
  selectActiveLevel,
  selectActiveGender,
} from '../../../_reduxapi/student/selectors';
import * as actions from '../../../_reduxapi/student/action';
import { Trans } from 'react-i18next';
import { studentGroupUrl, studentGenderMode } from 'utils';

const Deletemodal = (props) => {
  const {
    show,
    activeTerm,
    activeLevel,
    activeGender,
    activeYear,
    id,
    foundationdata,
    courseid,
    activegroup,
    callBackFn,
    handleClickClose,
    componentName,
    activeLeveldata,
  } = props;

  const studentid = [];
  studentid.push(id);

  const deleteClick = () => {
    if (componentName === 'foundation' || componentName === 'rotation') {
      let endpoint;
      let requestBody = {
        _id: activeYear.get('_id'),
        batch: activeTerm.get('term'),
        level: activeLevel.get('level'),
        gender: activeGender,
        _student_ids: props.selectedDelete ? props.id : studentid,
      };
      if (componentName === 'rotation') {
        endpoint = `/${studentGroupUrl()}/rotation_bulk_delete${studentGenderMode()}`;
      } else {
        endpoint = `/${studentGroupUrl()}/fyd_student_delete${studentGenderMode()}`;
      }
      props.deleteData(endpoint, requestBody, () => foundationdata(0));
      handleClickClose();
    } else {
      let endpoint;
      let requestBody = {
        _id: activeYear.get('_id'),
        batch: activeTerm.get('term'),
        gender: activeGender,
        _student_ids: props.selectedDelete ? props.id : studentid,
      };
      if (componentName === 'individualCourse') {
        requestBody._course_id = courseid;
        requestBody.level = activeLeveldata;
        endpoint = `/${studentGroupUrl()}/student_delete${studentGenderMode()}`;
      } else {
        requestBody.level = activeLeveldata;
        requestBody._course_id = courseid;
        requestBody.master_group = activegroup.substr(5);
        componentName === 'rotationCourse'
          ? (endpoint = `/${studentGroupUrl()}/rotation_course_student_delete${studentGenderMode()}`)
          : (endpoint = `/${studentGroupUrl()}/fyd_course_student_delete${studentGenderMode()}`);
      }
      props.deleteCourse(endpoint, requestBody, () => callBackFn());
      handleClickClose();
    }
  };

  const cancel = () => {
    handleClickClose();
  };

  return (
    <Modal show={show} centered>
      <Modal.Body>
        <div className="row">
          <div className="col-md-12">
            <p className="f-20 mb-2">
              <Trans i18nKey={'student_grouping.confirm_delete'} />
            </p>
            <p className="f-14 mb-1 light-gray ">
              <Trans i18nKey={'student_grouping.confirm_delete_msg'} />
            </p>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <b className="pr-2">
          <Button variant="outline-primary" className="outline-primary" onClick={deleteClick}>
            <Trans i18nKey={'student_grouping.yes'} />
          </Button>
        </b>

        <b className="pr-2">
          <Button variant="primary" className="primary" onClick={cancel}>
            <Trans i18nKey={'student_grouping.no'} />
          </Button>
        </b>
      </Modal.Footer>
    </Modal>
  );
};

Deletemodal.propTypes = {
  show: PropTypes.bool,
  selectedDelete: PropTypes.bool,
  deleteData: PropTypes.func,
  deleteCourse: PropTypes.func,
  activeTerm: PropTypes.instanceOf(Map),
  activeLevel: PropTypes.instanceOf(Map),
  activeYear: PropTypes.instanceOf(Map),
  activeLeveldata: PropTypes.instanceOf(List),
  activeGender: PropTypes.string,
  courseid: PropTypes.string,
  id: PropTypes.string,
  componentName: PropTypes.string,
  activegroup: PropTypes.string,
  foundationdata: PropTypes.func,
  callBackFn: PropTypes.func,
  handleClickClose: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    message: selectMessage(state),
    activeProgram: selectActiveProgram(state),
    activeAcademicYear: selectActiveAcademicYear(state),

    dashboard: selectDashboard(state),
    activeGender: selectActiveGender(state),
    activeTerm: selectActiveTerm(state),
    activeYear: selectActiveYear(state),
    activeLevel: selectActiveLevel(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Deletemodal);
