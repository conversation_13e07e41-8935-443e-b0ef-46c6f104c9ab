import React from 'react';
import PropTypes from 'prop-types';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import College1 from '../../../Assets/img/globalConfiguration/College1.svg';
import College2 from '../../../Assets/img/globalConfiguration/College2.svg';

function ModeOfDepartment() {
  return (
    <div className="container">
      <div className="float-left">
        <FormControl component="fieldset">
          <div className="mb-3">
            <RadioGroup row aria-label="position" name="position" defaultValue="top">
              <FormControlLabel
                value="right"
                control={<Radio color="primary" />}
                label="Mode 1"
                labelPlacement="right"
              />
            </RadioGroup>
            <div className="col-12 col-xl-12 col-lg-12 col-md-12 ml-2">
              <img src={College1} alt="mode1" className="img-fluid" />
            </div>
          </div>
          <div className="mb-3">
            <RadioGroup row aria-label="position" name="position" defaultValue="top">
              <FormControlLabel
                value="right"
                control={<Radio color="primary" />}
                label="Mode 2"
                labelPlacement="right"
              />
            </RadioGroup>
            <div className="col-12 col-xl-12 col-lg-12 col-md-12 ml-2">
              <img src={College2} alt="mode1" className="img-fluid" />
            </div>
          </div>
        </FormControl>
      </div>
      <div className="clearfix"> </div>
    </div>
  );
}
ModeOfDepartment.propTypes = {
  children: PropTypes.array,
};
export default ModeOfDepartment;
