import React, { Component } from 'react';
import { with<PERSON>outer } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Map } from 'immutable';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, Accordion, Card, Form } from 'react-bootstrap';

import * as actions from '../../../_reduxapi/leave_management/actions';
import {
  selectPermissionList,
  selectEditedPermissionList,
} from '../../../_reduxapi/leave_management/selectors';

import Input from '../../../Widgets/FormElements/Input/Input';
import '../../../Assets/css/grouping.css';
import { jsUcfirst, getLang } from '../../../utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { t } from 'i18next';
const lang = getLang();

const permissionHrsOptions = [
  { name: '00', value: '0' },
  { name: '01', value: '1' },
  { name: '02', value: '2' },
  { name: '03', value: '3' },
  { name: '04', value: '4' },
  { name: '05', value: '5' },
  { name: '06', value: '6' },
  { name: '07', value: '7' },
  { name: '08', value: '8' },
];

const frequencyHrsOptions = [
  { name: '00', value: '0' },
  { name: '01', value: '1' },
  { name: '02', value: '2' },
  { name: '03', value: '3' },
  { name: '04', value: '4' },
  { name: '05', value: '5' },
  { name: '06', value: '6' },
  { name: '07', value: '7' },
  { name: '08', value: '8' },
  { name: '09', value: '9' },
  { name: '10', value: '10' },
];

class Permission extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      arrow1: false,
      hourObj: '',
      frequencyObj: '',
      periodObj: '',
      cancelbtn: false,
      savebutton: false,
    };
  }
  frequencyPeriodOptions = [
    { name: t('leaveManagement.perYear'), value: 'year' },
    { name: t('leaveManagement.perMonth'), value: 'month' },
  ];
  componentDidMount() {
    const { type } = this.props;
    this.props.getAllPermissionList(type);
  }

  iconPostion1 = () => {
    this.setState({
      arrow1: !this.state.arrow1,
    });
  };

  handleChange = (selectedValue, name) => {
    const { permissionList, editedPermission } = this.props;
    let hourObj = '';
    let frequencyObj = '';
    let periodObj = '';

    switch (name) {
      case 'is_scheduled':
        break;
      case 'is_permission_attachment_required':
        break;
      case 'permission_hours':
        hourObj = permissionHrsOptions.find((hrs) => {
          return hrs.value === selectedValue;
        });
        selectedValue = hourObj.value;
        break;
      case 'permission_frequency':
        frequencyObj = frequencyHrsOptions.find((hrs) => {
          return hrs.value === selectedValue;
        });
        selectedValue = frequencyObj.value;
        break;
      case 'permission_frequency_by':
        periodObj = this.frequencyPeriodOptions.find((hrs) => {
          return hrs.value === selectedValue;
        });
        selectedValue = periodObj.value;
        break;
      default:
    }

    this.setState({
      cancelbtn: true,
      savebutton: false,
    });

    const permissionObj = editedPermission.isEmpty()
      ? permissionList.set(name, selectedValue)
      : editedPermission.set(name, selectedValue);
    this.props.setData(
      Map({
        editedPermission: permissionObj,
      })
    );
  };

  disableEditedPermission = (editedPermission) => {
    if (editedPermission.get('permission_hours') === '0') return true;
    if (editedPermission.get('permission_frequency') === '0') return true;
    if (editedPermission.isEmpty()) return true;
    if (!this.state.cancelbtn) return true;
    if (this.state.savebutton) return true;
  };

  savePermission = (e, editedPermission) => {
    e.preventDefault();
    const attachment_required = editedPermission.get('is_permission_attachment_required');
    const is_scheduled = editedPermission.get('is_scheduled');

    const requestBody = {
      category_type: 'permission',
      to: this.props.type,
      permission_hours: +editedPermission.get('permission_hours'),
      permission_frequency: +editedPermission.get('permission_frequency'),
      permission_frequency_by: editedPermission.get('permission_frequency_by'),
      is_scheduled: is_scheduled ? is_scheduled : false,
      is_permission_attachment_required: attachment_required ? attachment_required : false,
    };

    this.props.updatePermission(requestBody);

    this.setState({
      savebutton: true,
      cancelbtn: false,
    });
  };

  handleCancel = () => {
    const { permissionList } = this.props;
    const permissionObj = Map({
      permission_hours: permissionList.get('permission_hours', '0'),
      permission_frequency: +permissionList.get('permission_frequency', '0'),
      permission_frequency_by: permissionList.get('permission_frequency_by', 'year'),
      is_permission_attachment_required: permissionList.get(
        'is_permission_attachment_required',
        false
      ),
      is_scheduled: permissionList.get('is_scheduled', false),
    });
    this.props.setData(
      Map({
        editedPermission: permissionObj,
      })
    );
    this.setState({
      cancelbtn: !this.state.cancelbtn,
    });
  };

  getDropDownValue = (key, defaultValue = '') => {
    const { permissionList, editedPermission } = this.props;
    return editedPermission.isEmpty()
      ? permissionList.get(key, defaultValue)
      : editedPermission.get(key, defaultValue);
  };

  render() {
    const { editedPermission, type } = this.props;

    // const stylingSave = editedPermission.isEmpty()
    //   ? {
    //       backgroundColor: 'gray',
    //       cursor: 'auto',
    //       borderColor: 'gray',
    //     }
    //   : { cursor: 'pointer' };

    return (
      <React.Fragment>
        <Accordion defaultActiveKey="">
          <Card className="rounded">
            <Accordion.Toggle
              as={Card.Header}
              eventKey="0"
              className="icon_remove_leave"
              onClick={this.iconPostion1}
            >
              <div className="d-flex justify-content-between">
                <p className="mb-0">{t('role_management.tabs.Permission')}</p>

                <p className="mb-0">
                  {this.state.arrow1 === true ? (
                    <i className="fa fa-chevron-down f-14" aria-hidden="true"></i>
                  ) : (
                    <i
                      className={`fa fa-chevron-${lang === 'ar' ? 'down' : 'up'} fa-rotate-90 f-14`}
                      aria-hidden="true"
                    ></i>
                  )}
                </p>
              </div>
            </Accordion.Toggle>
            <Accordion.Collapse eventKey="0" className="bg-white ">
              <Card.Body className=" innerbodyLeave border-top-remove">
                <div className="container-fluid ">
                  <div className="row text-left">
                    <div className="col-md-4">
                      <p className="f-14 mb-2 pt-2  ">
                        {t('leaveManagement.setTimeAllowForPermission')}
                      </p>
                    </div>

                    <div className="col-md-1">
                      <div className="mt--25">
                        <Input
                          elementType={'floatingselect'}
                          elementConfig={{
                            options: permissionHrsOptions,
                          }}
                          value={this.getDropDownValue('permission_hours')}
                          changed={(e) => this.handleChange(e.target.value, 'permission_hours')}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="row pt-2 text-left">
                    <div className="col-md-4">
                      <p className="f-14 mb-2 pt-2  ">{t('leaveManagement.setFrequency')}</p>
                    </div>

                    <div className="col-md-1">
                      <div className="mt--25">
                        <Input
                          elementType={'floatingselect'}
                          elementConfig={{
                            options: frequencyHrsOptions,
                          }}
                          value={this.getDropDownValue('permission_frequency')}
                          changed={(e) => this.handleChange(e.target.value, 'permission_frequency')}
                        />
                      </div>
                    </div>

                    <div className="col-md-2">
                      <div className="mt--25">
                        <Input
                          elementType={'floatingselect'}
                          elementConfig={{
                            options: this.frequencyPeriodOptions,
                          }}
                          value={this.getDropDownValue('permission_frequency_by')}
                          changed={(e) =>
                            this.handleChange(e.target.value, 'permission_frequency_by')
                          }
                        />
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="pt-2 mb-1 m-14">
                      {this.props.type !== 'student' && (
                        <Form.Check
                          type="checkbox"
                          label={t('leaveManagement.allowApplySessionApplied')}
                          checked={this.getDropDownValue('is_scheduled', false)}
                          onChange={(e) => this.handleChange(e.target.checked, 'is_scheduled')}
                        />
                      )}{' '}
                    </div>
                    {this.props.type !== 'student' && (
                      <p className={`mb-2 f-13 text-gray ${lang === 'ar' ? 'mr-4' : 'ml-4'}`}>
                        {' '}
                        {t('leaveManagement.applicantAssign')}{' '}
                      </p>
                    )}

                    <div className="mb-2 m-14">
                      {' '}
                      <Form.Check
                        type="checkbox"
                        label={t('leaveManagement.attachmentMandatory')}
                        checked={this.getDropDownValue('is_permission_attachment_required', false)}
                        onChange={(e) =>
                          this.handleChange(e.target.checked, 'is_permission_attachment_required')
                        }
                      />
                    </div>
                  </div>
                </div>
                <div className="col-md-12">
                  <div className="float-right pb-2">
                    {this.state.cancelbtn &&
                      CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        jsUcfirst(type),
                        '',
                        'Permission',
                        'Reset'
                      ) && (
                        <b className="pr-3">
                          <Button variant="outline-secondary" onClick={() => this.handleCancel()}>
                            {t('cancel_upper')}
                          </Button>
                        </b>
                      )}

                    {CheckPermission(
                      'subTabs',
                      'Leave Management',
                      'Leave Settings',
                      '',
                      jsUcfirst(type),
                      '',
                      'Permission',
                      'Edit'
                    ) && (
                      <b className="">
                        {!this.state.savebutton && (
                          <Button
                            variant={
                              this.disableEditedPermission(editedPermission)
                                ? 'secondary'
                                : 'primary'
                            }
                            disabled={this.disableEditedPermission(editedPermission)}
                            onClick={(e) => this.savePermission(e, editedPermission)}
                          >
                            {t('curriculum_keys.save')}
                          </Button>
                        )}
                      </b>
                    )}
                  </div>
                </div>
              </Card.Body>
            </Accordion.Collapse>
          </Card>
        </Accordion>
      </React.Fragment>
    );
  }
}

Permission.propTypes = {
  permissionList: PropTypes.instanceOf(Map),
  editedPermission: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  getAllPermissionList: PropTypes.func,
  updatePermission: PropTypes.func,
  type: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    permissionList: selectPermissionList(state),
    editedPermission: selectEditedPermissionList(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Permission);
