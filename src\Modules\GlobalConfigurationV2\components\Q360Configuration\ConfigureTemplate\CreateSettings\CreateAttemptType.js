import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
//react library
import {
  DialogActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  <PERSON><PERSON>ield,
  But<PERSON>,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
//Mui Imports
import { setData } from '_reduxapi/global_configuration/v1/actions';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
// redux Imports
import { Map, fromJS } from 'immutable';
//Immutable imports
import { dialogSX, placeholderStyles } from './designUtils';
//style Imports
//----------------------------------UI Utils Start--------------------------------------------------

//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const layOut = Map({
  name: '',
  isEdited: true,
  isActive: true,
});
//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------

//----------------------------------custom hooks end------------------------------------------------
//----------------------------------componentStart--------------------------------------------------

//----------------------------------componentEnd----------------------------------------------------

const CreateAttemptType = ({
  editIndex,
  open,
  existData = Map(),
  handleClose,
  setAttemptData,
  isEdit,
}) => {
  const isCheckEdit = isEdit ? existData.get(editIndex, Map()) : existData;
  const [attempts, setAttempts] = useState(
    fromJS([
      Map({
        name: isCheckEdit.get('name', ''),
        isEdited: isCheckEdit.get('isEdited', false),
        isActive: true,
        ...(isCheckEdit.get('_id', '') && {
          _id: isCheckEdit.get('_id', ''),
        }),
      }),
    ])
  );
  const dispatch = useDispatch();

  const setMessage = (message) => {
    dispatch(setData({ message }));
  };

  const handleValidate = () => {
    const attemptTypes = existData
      .filter((_, index) => index !== editIndex)
      .map((attemptType) => attemptType.get('name', ''));
    const isDuplicate = attemptTypes.some((type) =>
      attempts.map((attempt) => attempt.get('name')).includes(type)
    );
    for (const [index, attempt] of attempts.entries()) {
      if (attempt.get('name').trim() === '') {
        return setMessage('attempt name required at attempt' + (index + 1));
      }
    }
    if (isDuplicate) {
      return setMessage('Duplicate Attempt Register');
    }
    if (editIndex !== undefined) {
      setAttemptData((prev) => prev.set(editIndex, attempts.get(0, Map())));
      return handleClose();
    }
    setAttemptData((prev) => prev.merge(attempts));
    handleClose();
  };

  const handleDelete = (index) => {
    setAttempts((prev) => prev.filter((_, i) => i !== index));
  };
  return (
    <Dialog fullWidth open={open} onClose={handleClose} PaperProps={{ sx: dialogSX }} maxWidth="xs">
      <DialogTitle className="text-mGrey pb-0 ">
        <h5 className="pt-2 fw-400  f-24 ">
          {editIndex === undefined ? 'Create ' : 'Edit '} Attempt Type
        </h5>
      </DialogTitle>
      <DialogContent className="course-qlc-scrollbar mx-2 my-1 px-3 py-0">
        <DialogContentText className="responsiveFontSizeSmall d-flex flex-column gap-2">
          <div className="d-flex flex-column gap-3">
            {attempts.map((attempt, index) => (
              <>
                <div className="d-flex align-items-center pt-3" key={index}>
                  <div className="flex-grow-1 f-12 fw-400 text-mGrey d-flex align-items-center justify-content-between">
                    <span className="mr-2">Name</span>
                    <EnableOrDisable valid={index !== 0}>
                      <DeleteIcon
                        fontSize="small"
                        sx={{ color: 'red', cursor: 'pointer' }}
                        onClick={() => handleDelete(index)}
                      />
                    </EnableOrDisable>
                  </div>
                </div>
                <div className="d-flex align-items-center gap-15">
                  <TextField
                    value={attempt.get('name', '')}
                    onChange={(e) =>
                      setAttempts((prev) => prev.setIn([index, 'name'], e.target.value))
                    }
                    placeholder="Enter Name"
                    size="small"
                    fullWidth
                    sx={placeholderStyles}
                  />
                </div>
              </>
            ))}
            <div
              className="mt-1 f-14 fw-400 text-primary cursor-pointer"
              onClick={() => setAttempts((prev) => prev.push(layOut))}
            >
              + Add Type
            </div>
          </div>
        </DialogContentText>
      </DialogContent>
      <DialogActions className="px-4 pb-4 gap-2">
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall text-secondary border-secondary bold"
          variant="outlined"
          onClick={() => {
            handleClose();
          }}
        >
          Cancel
        </Button>
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall bg-primary"
          variant="contained"
          onClick={() => {
            handleValidate();
          }}
        >
          {editIndex === undefined ? 'Create' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

CreateAttemptType.propTypes = {
  open: PropTypes.bool,
  editIndex: PropTypes.number,
  handleClose: PropTypes.func,
  existData: PropTypes.instanceOf(Map),
  setAttemptData: PropTypes.func,
};

export default CreateAttemptType;
