import { t } from 'i18next';
import Switch from '@mui/material/Switch';
import { styled } from '@mui/material/styles';

// const infraDataKeyMapping = {
//   _building_id: 'Building',
//   floor_no: 'Floor',
//   room_no: 'Room no',
//   name: 'Name',
//   usage: 'Usage',
//   delivery_type: 'Del. type',
//   timing: 'Timings',
//   program: 'Program',
//   department: 'Department',
//   subject: 'Subject',
//   capacity: 'Capacity',
//   reserved: 'Reserved',
// };

function getRandomId() {
  return '_' + Math.random().toString(36).substr(2, 9);
}

function capitalize(s) {
  if (typeof s !== 'string') return s;
  return `${s.charAt(0).toUpperCase()}${s.slice(1)}`;
}

function getInfrastructureValidation(data) {
  const invalidFields = [];
  const invalidRange = [];
  Object.keys(data).forEach((key) => {
    switch (key) {
      case '_building_id':
      case 'usage':
        if (!data[key]) {
          invalidFields.push(t(`infra_management.table_headers.${key}`));
        }
        break;
      case 'room_no':
        if (!data[key]) {
          invalidFields.push(t(`infra_management.table_headers.${key}`));
        } else if (data[key].length < 3) {
          invalidRange.push(t('infra_management.validation_strings.room_no'));
        }
        break;
      case 'capacity':
        if (data[key] === 0 || data[key] > 150) {
          invalidRange.push(t('infra_management.validation_strings.capacity'));
        }
        break;
      case 'reserved':
        if (data[key] > 10) {
          invalidRange.push(t('infra_management.validation_strings.reserved'));
        }
        break;
      case 'name':
        if (!data[key]) {
          invalidFields.push(t(`infra_management.table_headers.${key}`));
        } else if (data[key].length < 4) {
          invalidRange.push(t('infra_management.validation_strings.name_length'));
        }
        break;
      case 'delivery_type':
      case 'timing':
      case 'program':
        if (!data[key].length) {
          invalidFields.push(t(`infra_management.table_headers.${key}`));
        }
        break;
      default:
        break;
    }
  });
  return {
    invalidFields,
    invalidRange,
  };
}

const Android12Switch = styled(Switch)(({ theme }) => ({
  padding: 8,
  '& .MuiSwitch-track': {
    borderRadius: 22 / 2,
    '&:before, &:after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      transform: 'translateY(-50%)',
      width: 16,
      height: 16,
    },
    '&:before': {
      backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 24 24"><path fill="${encodeURIComponent(
        theme.palette.getContrastText(theme.palette.primary.main)
      )}" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/></svg>')`,
      left: 12,
    },
    '&:after': {
      backgroundImage: `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" viewBox="0 0 24 24"><path fill="${encodeURIComponent(
        theme.palette.getContrastText(theme.palette.primary.main)
      )}" d="M19,13H5V11H19V13Z" /></svg>')`,
      right: 12,
    },
  },
  '& .MuiSwitch-thumb': {
    boxShadow: 'none',
    width: 16,
    height: 16,
    margin: 2,
  },
}));

export { capitalize, getRandomId, getInfrastructureValidation, Android12Switch };
