// import CryptoJS from 'crypto-js';
import LocalStorageService from 'LocalStorageService';
// import { useParams } from 'react-router-dom';
import { getSiteUrl } from 'utils';
import { decryptData, encryptData } from '../encryption';
import { ENCRYPT_STORAGE } from 'constants';

// const secretKey = '!@#digi-val-life-key#@!';
const noEncryption = ['access_token', 'refresh_token', 'forgot_password_access_token'];
const chunkSize = 4050;

const useCustomCookie = () => {
  // Function to encrypt data
  // const encryptData = (data) => {
  //   return CryptoJS.AES.encrypt(JSON.stringify(data), secretKey).toString();
  // };

  // Function t`o decrypt data
  // const decryptData = (encryptedData) => {
  //   try {
  //     const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
  //     const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  //     return decryptedData;
  //   } catch (error) {
  //     console.error('Error while decrypting data:', error); //eslint-disable-line
  //     return null; // or throw an error, or handle the error in another way
  //   }
  // };
  function getSiteDetailsCache() {
    if (window.location.href === '/auth/login') return {};
    const siteURLDetails = LocalStorageService.getCustomParsedToken('siteURLDetails');
    if (siteURLDetails) {
      return siteURLDetails;
    }
    const siteURL = getSiteUrl('fromHook setCookie');
    LocalStorageService.setCustomParsedToken('siteURLDetails', siteURL);
    return siteURL;
  }
  function getDomainNameCache() {
    const siteURL = getSiteDetailsCache();
    const domainName = siteURL.COOKIE_DOMAIN_NAME;
    const domain = 'domain=' + domainName;
    return domain;
  }
  const prefix = 'DC-';
  // Function to create or update a cookie
  const setCookie = (name, value, parsed = false, days = 1) => {
    const domain = getDomainNameCache();
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    const expires = 'expires=' + date.toUTCString();
    //console.log('vvaa', `${prefix}${name}`, encryptedValue);
    if (typeof value === 'boolean') value = value.toString();
    if (value && ENCRYPT_STORAGE && !noEncryption.includes(name))
      ({ data: value } = encryptData({ content: value, handledBy: 'CLIENT' }));
    else if (parsed) value = encodeURIComponent(JSON.stringify(value));

    if (value.length <= chunkSize)
      document.cookie = `${prefix}${name}=${value};${expires};${domain};path=/`;
    else {
      const numChunks = Math.ceil(value.length / chunkSize);
      for (let i = 0; i < numChunks; i++) {
        const chunk = value.substring(i * chunkSize, (i + 1) * chunkSize);
        document.cookie = `${prefix}${name}${i}=${chunk};${expires};${domain};path=/`;
      }
    }
    //document.cookie = cookieName +"=" + cookieValue + ";expires=" + myDate + ";domain=.example.com;path=/";
  };

  // Function to read a cookiec
  const getCookie = (name, parsed = false) => {
    const cookieName = `${prefix}${name}`;
    const cookies = document.cookie.split('; ');
    let chunks = [];
    for (const cookie of cookies) {
      const [key, value] = cookie.split('=');
      if (key.startsWith(cookieName)) chunks.push(value);
    }

    if (chunks.length) {
      const cookieValue = chunks.join('');
      if (cookieValue && ENCRYPT_STORAGE && !noEncryption.includes(name))
        return decryptData({ content: cookieValue, handledBy: 'CLIENT', parsed });

      return parsed ? JSON.parse(decodeURIComponent(cookieValue)) : cookieValue;
    }
    return '';
  };

  // Function to delete a cookie
  const deleteCookie = (name) => {
    const domain = getDomainNameCache();
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; ${domain};`;
  };

  const deleteCookiesStartingWith = (prefix, exceptions = []) => {
    const cookies = document.cookie.split('; ');

    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const cookieName = cookie.split('=')[0];
      if (cookie.startsWith(prefix) && !exceptions.includes(cookieName)) {
        deleteCookie(cookieName);
      }
    }
  };

  const deleteSingleCookie = (name) => {
    deleteCookie(`${prefix}${name}`);
  };

  function isCookieExpired(cookieName) {
    const cookies = document.cookie.split('; ');

    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const [name, value] = cookie.split('='); //eslint-disable-line

      if (name === `${prefix}${cookieName}`) {
        const expires = getCookieExpiration(cookie);
        if (expires) {
          const expirationTime = new Date(expires).getTime();
          const currentTime = new Date().getTime();
          return expirationTime <= currentTime;
        }
      }
    }

    return false; // Cookie not found
  }

  function getCookieExpiration(cookie) {
    const cookieParts = cookie.split('; ');
    for (const part of cookieParts) {
      if (part.startsWith('expires=')) {
        return decodeURIComponent(part.substring('expires='.length));
      }
    }
    return null; // No expiration found
  }

  // const handleCookie = () => {
  //   const cookieName = 'myCookie';
  //   const cookieData = { key: 'value' };

  //   // Create or update a cookie
  //   setCookie(cookieName, cookieData, 7);

  //   // Read the cookie value
  //   const retrievedCookie = getCookie(cookieName);
  //   setCookieValue(JSON.stringify(retrievedCookie));

  //   // Delete the cookie
  //   deleteCookie(cookieName);
  // };

  return {
    setCookie,
    getCookie,
    deleteCookie,
    deleteCookiesStartingWith,
    isCookieExpired,
    deleteSingleCookie,
  };
};

export default useCustomCookie;
