.timetable-container {
  height: 500px;
  overflow-x: scroll;
}

.timetable {
  color: rgba(0, 0, 0, 0.87);
  font-size: 15px;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;
  position: relative;
}

.timetable thead {
  background-color: rgba(250, 250, 250, 1);
}

.timetable thead tr th {
  padding-top: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.38);
  position: sticky;
  position: -webkit-sticky;
  top: 0;
}

.th-date {
  min-width: 100px;
  text-transform: uppercase;
  position: sticky;
  position: -webkit-sticky;
  left: 0;
  background-color: rgba(237, 246, 255, 1);
  z-index: 2;
  text-align: center;
}

.th-group {
  min-width: 50px;
  position: sticky;
  position: -webkit-sticky;
  left: 100px;
  background-color: rgba(250, 250, 250, 1);
  z-index: 2;
  text-align: center;
}

.td-date {
  position: sticky;
  position: -webkit-sticky;
  left: 0;
  background-color: rgba(237, 246, 255, 1);
  text-align: center;
  z-index: 1;
}

.td-group {
  position: sticky;
  position: -webkit-sticky;
  left: 100px;
  background-color: rgba(250, 250, 250, 1);
  text-align: center;
  padding: 24px 5px;
  z-index: 1;
}

.timetable tbody tr td {
  border-bottom: 1px solid rgba(0, 0, 0, 0.38);
}

.th-time {
  min-width: 100px;
  max-width: 100px;
  background-color: #ffffff;
  z-index: 1;
  border-right: 1px solid rgba(0, 0, 0, 0.38);
  text-align: center;
}

.td-time {
  /* min-width: 27px;
  max-width: 27px;
  width: 27px; */
  min-width: 45px;
  max-width: 45px;
  width: 45px;
  padding: 0px;
  text-align: center;
}

.schedule-card {
  border-radius: 4px;
  min-height: 70px;
  padding: 5px 5px;
  font-size: 12px;
  word-break: break-word;
}

.extra-curricular-break-card-daily {
  display: inline-block;
  width: 100%;
  height: 100%;
}

.extra-curricular-break-align-center {
  transform: translateY(-50%);
  top: 50%;
  position: relative;
  text-align: center;
}

.session-type {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.87);
}

.text-align-center {
  text-align: center;
}
