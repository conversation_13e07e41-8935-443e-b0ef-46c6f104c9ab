import React, { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { Box, Divider, TextField } from '@mui/material';
import Buttons from 'Widgets/FormElements/material/Button';
import reason_icon from 'Assets/q360_dashboard/reason_icon.svg';

function ReasonModal({ reasonPersisted, setReasonPersisted }) {
  const [open, setOpen] = React.useState(false);
  const [reason, setReason] = useState(reasonPersisted);

  const handleReset = () => {
    setReason(reasonPersisted);
    handleClickOpenOrClose();
  };
  const handleClickOpenOrClose = () => {
    setOpen((prev) => !prev);
  };

  const handleInputChange = (e) => {
    setReason(e.target.value);
  };
  const handleSave = () => {
    setReasonPersisted(reason);
    handleClickOpenOrClose();
  };
  return (
    <React.Fragment>
      <div onClick={handleClickOpenOrClose} className="f-14 mt-3">
        <span className="ml-3 font_orange_intimation">You didn’t complete the incorporation *</span>
        <span className="f-14 ml-3 text-primary text_underline">
          {reason ? 'View/Edit Reason' : 'Give Reason'}
        </span>
        <img src={reason_icon} className="ml-1" alt="reason_icon" />
      </div>
      <Dialog
        open={open}
        onClose={handleClickOpenOrClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title" sx={{ padding: '16px' }}>
          <div className="f-16">Incorporation Form Reason</div>
        </DialogTitle>
        <Divider />

        <DialogContent sx={{ padding: '8px 16px' }}>
          <Box
            component="form"
            sx={{ '& .MuiTextField-root': { m: 1, width: '550px' } }}
            noValidate
            autoComplete="off"
          >
            <div className="f-14 fw_500 mb-2">Give Reason</div>
            <TextField
              onChange={handleInputChange}
              // id="outlined-multiline-static"
              sx={{ margin: '0px !important' }}
              multiline
              fullWidth
              rows={3}
              value={reason}
              placeholder="Type here"
            />
          </Box>
        </DialogContent>
        <Divider />
        <DialogActions>
          <Buttons variant="outlined" className="mr-2 px-3" color={'gray'} clicked={handleReset}>
            <div className="px-2">CANCEL</div>
          </Buttons>
          <Buttons variant="contained" className="px-3 mr-1" clicked={handleSave} autoFocus>
            <div className="px-2">SUBMIT</div>
          </Buttons>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
}

export default ReasonModal;
