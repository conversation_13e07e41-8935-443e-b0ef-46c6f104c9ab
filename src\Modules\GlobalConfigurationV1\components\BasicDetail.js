import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

import TimeSetting from './SessionDetails';
import { selectGlobalSessionSetting } from '_reduxapi/global_configuration/v1/selectors';
import * as actions from '_reduxapi/global_configuration/v1/actions';

function Dashboard({
  getGlobalSessionSetting,
  setData,
  globalSessionSetting,
  postGlobalSessionSetting,
}) {
  useEffect(() => {
    getGlobalSessionSetting();
  }, [getGlobalSessionSetting]);

  return (
    <TimeSetting
      setData={setData}
      globalSessionSetting={globalSessionSetting}
      postGlobalSessionSetting={postGlobalSessionSetting}
    />
  );
}

Dashboard.propTypes = {
  getGlobalSessionSetting: PropTypes.func,
  setData: PropTypes.func,
  postGlobalSessionSetting: PropTypes.func,
  globalSessionSetting: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    globalSessionSetting: selectGlobalSessionSetting(state),
  };
};

export default connect(mapStateToProps, actions)(Dashboard);
