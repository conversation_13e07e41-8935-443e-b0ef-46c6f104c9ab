import { t } from 'i18next';
import { isIndVer, isModuleEnabled, lastNameRequired } from 'utils';

export const gender = [
  ['Male', 'male'],
  ['Female', 'female'],
];

export const data = {
  data: [],
  fName: '',
  mName: '',
  lName: '',
  userName: '',
  academicNo: '',
  nationalId: '',
  programNo: '',
  enrollmentYear: '',
};

export function validation() {
  // let emailVal = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  let emailVal = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  let space = /^\S$|^\S[ \S]*\S$/;
  const AlphaNum = /^[a-zA-Z0-9]+$/;
  let fNameError = '';
  let mNameError = '';
  let lNameError = '';
  let academicNoError = '';
  let nationalIdError = '';
  let emailError = '';
  let selectedBatchError = '';
  let selectedProgramError = '';

  if (!this.state.fName) {
    fNameError = t('user_management.first_name_required');
  }
  // else if (!Number.test(this.state.fName)) {
  //   fNameError = "Numbers not allowed";
  // }
  else if (this.state.fName.length <= 2) {
    fNameError = t('user_management.minimum_3_char_required');
  }

  //   if (this.state.mName) {
  //    if (!space.test(this.state.mName)) {
  //     mNameError = "Space not allowed beginning & end";
  //   } else if (!spaceAlpha.test(this.state.mName)) {
  //     mNameError = "Text Only allowed";
  //   } else if (this.state.mName.length <= 2) {
  //     mNameError = "Minimum 3 character is required ";
  //   }
  // }

  if (!lastNameRequired()) {
    if (!this.state.lName) {
      lNameError = t('user_management.last_name_required');
    } else if (!space.test(this.state.lName)) {
      lNameError = t('user_management.space_not_allowed');
    }
    // else if (!numbers.test(this.state.lName)) {
    //   lNameError = "Numbers not allowed";
    // }
    // else if (this.state.lName.length <= 2) {
    //   lNameError = t('user_management.minimum_3_char_required');
    // }
  }

  if (!this.state.email) {
    emailError = t('user_management.email_required');
  } else if (!emailVal.test(this.state.email)) {
    emailError = t('user_management.pls_enter_valid_mail');
  }

  if (!this.state.academicNo) {
    academicNoError = t('user_management.Academic_no_Required');
  } else if (!space.test(this.state.academicNo)) {
    academicNoError = t('user_management.space_not_allowed');
  } else if (!AlphaNum.test(this.state.academicNo)) {
    academicNoError = t('user_management.special_character_not_allowed');
  } else if (this.state.academicNo.length <= 0) {
    academicNoError = t('user_management.minimum_1_char_required');
  }
  const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
  if (hasNationalIdValidation) {
    if (!this.state.nationalId) {
      nationalIdError = t('user_management.National_Residence_id_required');
    } else if (!space.test(this.state.nationalId)) {
      nationalIdError = t('user_management.space_not_allowed');
    } else if (!AlphaNum.test(this.state.nationalId)) {
      nationalIdError = t('user_management.special_character_not_allowed');
    } else if (this.state.nationalId.length <= 4) {
      nationalIdError = t('user_management.minimum_5_char_required');
    }
  }
  if (this.state.selectedBatch === 'select') {
    selectedBatchError = t('user_management.Batch_is_Required', {
      Batch: isIndVer() ? 'Intake' : 'Batch',
    });
  }

  if (this.state.selectedProgram === 'select') {
    selectedProgramError = t('user_management.Program_is_Required');
  }

  if (
    fNameError ||
    mNameError ||
    lNameError ||
    academicNoError ||
    nationalIdError ||
    emailError ||
    selectedBatchError ||
    selectedProgramError
  ) {
    this.setState({
      fNameError,
      mNameError,
      lNameError,
      academicNoError,
      nationalIdError,
      emailError,
      selectedBatchError,
      selectedProgramError,
    });
    return false;
  }
  return true;
}
