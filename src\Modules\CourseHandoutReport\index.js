import React from 'react';
import { Route, Switch } from 'react-router';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import SnackBars from 'Modules/Utils/Snackbars';
import { t } from 'i18next';

// redux
import * as actions from '_reduxapi/courseHandout/action';
import { selectLoading, selectMessage } from '_reduxapi/courseHandout/selectors';

// utils
import Loader from 'Widgets/Loader/Loader';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
import CourseHandoutReport from './components/CourseHandoutReport';

const CourseHandout = ({ message, isLoading }) => {
  return (
    <div>
      {message && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>{t('side_nav.menus.course_handout_report')}</Breadcrumb>
      <Switch>
        <Route path="/courseHandoutReport" exact component={CourseHandoutReport}></Route>
      </Switch>
    </div>
  );
};

CourseHandout.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectLoading(state),
    message: selectMessage(state),
  };
};

export default connect(mapStateToProps, actions)(withRouter(CourseHandout));
