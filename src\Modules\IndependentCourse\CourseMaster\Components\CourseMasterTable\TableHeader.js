import React, { useContext } from 'react';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { independentCourseContext } from '../../../context';

const CourseTableHeader = () => {
  const { getToolTipData } = useContext(independentCourseContext);

  return (
    <thead>
      <tr>
        <th></th>
        <th className="w-10">
          <div className="">
            <span>
              <Trans i18nKey={'s_no'}></Trans>
            </span>
          </div>
        </th>

        <th className="w-10">
          <div className="">
            <span>
              <Trans i18nKey={'code'}></Trans>
            </span>
          </div>
        </th>

        <th className="w-25">
          <div className="">
            {' '}
            <span>
              {getToolTipData.course} {t('name')}
            </span>
          </div>
        </th>
        <th className="w-25">
          <div className="">
            <span>Admin Department</span>
          </div>
        </th>
        <th>
          <div className="">
            {' '}
            <span>
              {getToolTipData.course} {t('label_config.editor')}
            </span>
          </div>
        </th>
        <th className="w-10">
          <div></div>
        </th>
        <th>
          <div className=""></div>
        </th>
      </tr>
    </thead>
  );
};

export default CourseTableHeader;
