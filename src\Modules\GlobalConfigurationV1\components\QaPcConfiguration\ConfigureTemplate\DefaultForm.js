import React, { useState } from 'react';
import { List, Map } from 'immutable';
import { PropTypes } from 'prop-types';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Fade, Menu, MenuItem } from '@mui/material';
// import FormConfiguration from '../Models/FormConfiguration';
// import { useHistory } from 'react-router-dom';
import IncorporateIframeModal from 'Modules/QAPC/components/Models/IncorporateIframeModal';
import { data } from 'Modules/QAPC/components/QualityAssuranceProcess/CreateForm';
import FormConfigurationRedirect from 'Modules/GlobalConfigurationV2/components/Q360Configuration/Q360/FormConfigurationRedirect';
export default function DefaultForm({ category }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const [duplicate, setDuplicate] = useState(false);
  const [view, setView] = useState(false);
  const open = Boolean(anchorEl);
  const handleClick = (formName) => (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleClickDuplicate = () => {
    setDuplicate(true);
  };
  const handleCloseDuplicate = () => {
    setDuplicate(false);
    handleClose();
  };
  const handleCloseView = () => {
    setView(false);
  };
  const categoryName = category.get('categoryName', '');
  const templateUrl = data.get(categoryName);

  // const history = useHistory();
  // const onClickSettings = () => {
  //   history.push(
  //     `/globalConfiguration-v1/qa_pc_configuration/Create_Configuration?query=configure_template}`
  //   );
  // };
  return (
    <section>
      <div className="f-14 text-lGrey pt-3 pb-2 pointer-none">Default</div>
      <div className="templateName">
        {category.get('template', List()).map((temp) => (
          <div className="d-flex border rounded f-14 p-2" key={temp.get('_id', '')}>
            <div className="flex-grow-1">{temp.get('formName', '')}</div>
            <div
              className="text_underline text-primary mr-2 cursor-pointer"
              onClick={() => setView(true)}
            >
              View
            </div>
            <MoreVertIcon
              className="cursor-pointer"
              fontSize="small"
              onClick={handleClick(temp.get('formName', ''))}
            />
          </div>
        ))}
        {view && (
          <IncorporateIframeModal
            open={view}
            handleClose={handleCloseView}
            templateUrl={templateUrl}
          />
        )}
      </div>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
        transformOrigin={{ horizontal: 'right', vertical: 'right' }}
        anchorOrigin={{ horizontal: 'left', vertical: 'left' }}
      >
        <MenuItem onClick={handleClickDuplicate}>Duplicate</MenuItem>
      </Menu>
      {duplicate && (
        <FormConfigurationRedirect open={duplicate} handleClose={handleCloseDuplicate} />
      )}
    </section>
  );
}
DefaultForm.propTypes = {
  category: PropTypes.instanceOf(Map),
};
