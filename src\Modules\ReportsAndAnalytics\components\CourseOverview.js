import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import { useParams } from 'react-router-dom';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import ReactECharts from 'echarts-for-react';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import CountInfoCards from './CountInfoCards';
import CourseDetailsAccordion from './CourseDetailsAccordion';
import InfoChart from './InfoChart';
import SubjectsCard from './SubjectsCard';
import CloSloChart from './CloSloChart';

import * as actions from '../../../_reduxapi/reports_and_analytics/action';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import { selectCourseOverviewData } from '../../../_reduxapi/reports_and_analytics/selectors';
import {
  COUNT_INFO_GENERIC_TYPES,
  getBasicPieChartData,
  getCourseSessionTypesChartData,
  getSupportSessionTypesChartData,
  getStaffSessionTypesChartData,
  getBasicBarChartData,
  commonChartOptions,
  getFormattedGroupName,
  calculatePercentage,
} from '../utils';
import {
  getURLParams,
  trimFractionDigits,
  dString,
  getTPCLegend,
  indVerRename,
  isIndGroup,
  getEnvLabelChanged,
  studentGroupRename,
} from '../../../utils';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

function CourseOverview(props) {
  const { activeInstitutionCalendar, getCourseOverviewData, courseOverview } = props;
  const params = useParams();
  const [filters, setFilters] = useState(
    Map({ studentGroup: '', subject: '', isPopulated: false })
  );
  const [plannedSession, setPlannedSession] = useState('session');
  const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id');
  const programId = params.programId ? dString(params.programId) : '';
  const courseId = params.courseId ? dString(params.courseId) : '';
  const level = getURLParams('level', true);
  const term = getURLParams('term', true);
  const isRotation = getURLParams('rotation', true) === 'yes';
  const rotationCount = getURLParams('rotationCount', true);

  let planedSessions = {
    theory: 0,
    theoryTotal: 0,
    theoryPercentage: 0,
    practical: 0,
    practicalTotal: 0,
    practicalPercentage: 0,
    clinical: 0,
    clinicalTotal: 0,
    clinicalPercentage: 0,
  };

  useEffect(() => {
    const check = false;
    if (activeInstitutionCalendarId && programId && courseId && level && term && check) {
      getCourseOverviewData({
        institutionCalendarId: activeInstitutionCalendarId,
        programId,
        courseId,
        level,
        term,
        ...(isRotation && { rotationCount }),
      });
    }
  }, [
    getCourseOverviewData,
    activeInstitutionCalendarId,
    programId,
    courseId,
    level,
    term,
    isRotation,
    rotationCount,
  ]);

  useEffect(() => {
    const studentGroups = courseOverview.get('group_data', List());
    const subjects = courseOverview.get('subject_list', List());
    if (studentGroups.size && !filters.get('isPopulated')) {
      setFilters(
        filters.merge(
          Map({
            studentGroup: studentGroups.getIn([0, '_id'], ''),
            subject: subjects.getIn([0, '_subject_id'], ''),
            isPopulated: true,
          })
        )
      );
    }
  }, [courseOverview, filters]);

  function getActiveStudentGroup() {
    const studentGroupId = filters.get('studentGroup', '');
    return (
      courseOverview.get('group_data', List()).find((sg) => sg.get('_id') === studentGroupId) ||
      Map()
    );
  }

  function handleFilterChange(name, value) {
    setFilters(filters.set(name, value));
  }

  function handlePlannedSession(e) {
    setPlannedSession(e);
  }

  function getStudentGroupOptions() {
    return courseOverview.get('group_data', List()).map((group) =>
      Map({
        name: getFormattedGroupName(
          studentGroupRename(group.get('group_name', ''), programId),
          isIndGroup(group.get('group_name', '')) ? 1 : 2
        ),
        value: group.get('_id', ''),
      })
    );
  }

  function getSubjectOptions() {
    return courseOverview
      .get('subject_list', List())
      .map((subject) =>
        Map({ name: subject.get('subject_name', ''), value: subject.get('_subject_id', '') })
      );
  }

  function getSessionCompletionStatusInfoCardData() {
    const studentGroup = getActiveStudentGroup();
    const {
      theory = 0,
      theoryTotal = 0,
      practical = 0,
      practicalTotal = 0,
      clinical = 0,
      clinicalTotal = 0,
    } = studentGroup
      .get('session_type_schedule', List())
      .reduce((acc, sessionType) => {
        const type = sessionType.get('session_type', '').toLowerCase();
        const totalKey = `${type}Total`;
        return acc.merge(
          Map({
            [type]: acc.get(type, 0) + sessionType.get('schedule_completed_count', 0),
            [totalKey]: acc.get(totalKey, 0) + sessionType.get('schedule_count', 0),
          })
        );
      }, Map({ theory: 0, theoryTotal: 0, practical: 0, practicalTotal: 0, clinical: 0, clinicalTotal: 0 }))
      .toJS();

    return [
      COUNT_INFO_GENERIC_TYPES.getTotalCountCardData({
        type: 'session',
        count: `${theory + practical + clinical} / ${theoryTotal + practicalTotal + clinicalTotal}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionTypesTPCardData({
        theory: `${theory} / ${theoryTotal}`,
        practical: `${practical} / ${practicalTotal}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionTypesCCardData({
        count: `${clinical} / ${clinicalTotal}`,
      }),
    ];
  }

  function getSessionPlanedStatusInfoCardData() {
    const studentGroup = getActiveStudentGroup();
    const {
      theory = 0,
      theoryTotal = 0,
      practical = 0,
      practicalTotal = 0,
      clinical = 0,
      clinicalTotal = 0,
    } = studentGroup
      .get('credit_hours', List())
      .reduce((acc, sessionType) => {
        const type = sessionType.get('type_name', '').toLowerCase();
        const totalKey = `${type}Total`;
        return acc.merge(
          Map({
            [type]:
              acc.get(type, 0) +
              Math.round(
                sessionType.get(
                  plannedSession === 'session'
                    ? 'completed_sessionsSchedule'
                    : plannedSession === 'credit'
                    ? 'completed_credit_hours'
                    : 'completed_contact_hours',
                  0
                ) * 100
              ) /
                100,
            [totalKey]:
              acc.get(totalKey, 0) +
              Math.round(
                sessionType.get(
                  plannedSession === 'session'
                    ? 'no_of_sessions'
                    : plannedSession === 'credit'
                    ? 'credit_hours'
                    : 'contact_hours',
                  0
                ) * 100
              ) /
                100,
          })
        );
      }, Map({ theory: 0, theoryTotal: 0, practical: 0, practicalTotal: 0, clinical: 0, clinicalTotal: 0 }))
      .toJS();

    planedSessions.theory = theory;
    planedSessions.theoryTotal = theoryTotal;
    planedSessions.theoryPercentage = Math.round((100 * theory) / theoryTotal);
    planedSessions.practical = practical;
    planedSessions.practicalTotal = practicalTotal;
    planedSessions.practicalPercentage = Math.round((100 * practical) / practicalTotal);
    planedSessions.clinical = clinical;
    planedSessions.clinicalTotal = clinicalTotal;
    planedSessions.clinicalPercentage = Math.round((100 * clinical) / clinicalTotal);

    return [
      COUNT_INFO_GENERIC_TYPES.getTotalCountCardData({
        type: `${
          plannedSession === 'session'
            ? 'Session'
            : plannedSession === 'credit'
            ? 'Credit Hour'
            : 'Contact Hour'
        }`,
        count: `${
          plannedSession === 'credit'
            ? (theory + practical + clinical).toFixed(2)
            : Math.round(theory + practical + clinical)
        } / ${Math.round(theoryTotal + practicalTotal + clinicalTotal)}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionTypesTPCardData({
        theory: `${theory} / ${theoryTotal}`,
        practical: `${practical} / ${practicalTotal}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionTypesCCardData({
        count: `${clinical} / ${clinicalTotal}`,
      }),
    ];
  }

  function getSupportSessionInfoCardDataInfoData() {
    const studentGroup = getActiveStudentGroup();
    const supportSession = studentGroup.get('student_support_session', List());
    const deliveryData = supportSession.get('delivery_data', List()).map((plan) => {
      return COUNT_INFO_GENERIC_TYPES.getSupportSessionAllType({
        type: plan.get('type', ''),
        count:
          plan.get('complete_session', '') +
          ' ' +
          '/' +
          ' ' +
          plan.get('total_session', '') +
          ' ' +
          t('reports_analytics.session'),
      });
    });
    const overAllCount = COUNT_INFO_GENERIC_TYPES.getSingleSupportData({
      type: 'supportSession',
      count:
        supportSession.get('complete_session', '') +
        ' ' +
        '/' +
        ' ' +
        supportSession.get('total_session', ''),
    });

    const totalSupportSession = deliveryData.unshift(overAllCount);
    return totalSupportSession;
  }

  function getSupportSessionChartData(programId) {
    const studentGroup = getActiveStudentGroup();
    const supportSession = studentGroup.get('student_support_session', List());
    return getSupportSessionTypesChartData(supportSession.get('delivery_data', List()), programId);
  }

  function getStudentAttendanceInfoCardData() {
    const studentGroup = getActiveStudentGroup();
    const attendance = studentGroup.get('student_attendance', Map());
    const total = attendance.get('total_session', 0);
    const completed = attendance.get('complete_session', 0);
    const pending = attendance.get('pending_session', 0);
    const merged = attendance.get('merged_session', 0);
    const canceled = attendance.get('canceled_session', 0);
    const missed = attendance.get('missed_session', 0);
    return [
      COUNT_INFO_GENERIC_TYPES.getScheduledSessionCardData({ type: 'session', count: total }),
      COUNT_INFO_GENERIC_TYPES.getSessionStatusCompletePending({
        completed: `${completed} ${t('reports_analytics.session_s')}`,
        pending: `${pending} ${t('reports_analytics.session_s')}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionStatusMissed({
        missed: `${missed} ${t('reports_analytics.session_s')}`,
      }),
      COUNT_INFO_GENERIC_TYPES.getSessionStatusMergedCanceled({
        merged: `${merged} ${t('reports_analytics.session_s')}`,
        canceled: `${canceled} ${t('reports_analytics.session_s')}`,
      }),
    ];
  }

  function getStudentAttendanceChartData() {
    const studentGroup = getActiveStudentGroup();
    const schedule = studentGroup.getIn(['student_attendance', 'schedule'], List()).sort((a, b) => {
      const s1 = a.getIn(['session', 's_no'], 0);
      const s2 = b.getIn(['session', 's_no'], 0);
      return s1 - s2;
    });
    const { sessions, attendancePercentage } = schedule
      .reduce((acc, s) => {
        const deliverySymbol = s.getIn(['session', 'delivery_symbol'], '');
        const deliveryNo = s.getIn(['session', 'delivery_no'], '');
        let name = `${deliverySymbol}${deliveryNo}`;
        const totalStudents = s.get('student_count', 0);
        const presentStudents = s.get('present_count', 0);
        const isMergedSchedule = s.get('merge_status') === true;
        if (isMergedSchedule) {
          name = `${name}, ${s
            .get('merge_sessions', List())
            .map(
              (mergedSession) =>
                `${mergedSession.get('delivery_symbol', '')}${mergedSession.get('delivery_no', '')}`
            )
            .join(', ')}`;
        }

        let attendancePercentage =
          presentStudents === 0 ? 0 : (presentStudents / totalStudents) * 100;
        if (!Number.isInteger(attendancePercentage)) {
          attendancePercentage = trimFractionDigits(attendancePercentage, 2);
        }

        return acc.merge(
          Map({
            sessions: acc.get('sessions', List()).push(name),
            attendancePercentage: acc.get('attendancePercentage', List()).push(
              s.get('isActive') && s.get('status') === 'missed'
                ? Map({
                    name: 'missed',
                    value: 100,
                    itemStyle: Map({ color: '#fde2e2' }),
                    label: Map({
                      show: true,
                      formatter: 'Session missed',
                      position: 'insideBottom',
                      verticalAlign: 'middle',
                      align: 'left',
                      rotate: 90,
                      distance: 30,
                    }),
                  })
                : s.get('isActive')
                ? Map({ name: s.get('_id', ''), value: attendancePercentage })
                : Map({
                    name: 'canceled',
                    value: 100,
                    itemStyle: Map({ color: '#fde2e2' }),
                    label: Map({
                      show: true,
                      formatter: 'Session Canceled',
                      position: 'insideBottom',
                      verticalAlign: 'middle',
                      align: 'left',
                      rotate: 90,
                      distance: 30,
                    }),
                  })
            ),
          })
        );
      }, Map({ sessions: List(), attendancePercentage: List() }))
      .toJS();
    return {
      ...getBasicBarChartData({
        title: t('reports_analytics.attendance_percentage'),
        categories: sessions,
        series: [
          {
            data: attendancePercentage,
            label: { show: true, position: 'top', formatter: '{c}%' },
          },
        ],
      }),
      tooltip: {
        ...commonChartOptions.tooltip,
        formatter: (params) => {
          try {
            const data = params[0];
            const name = data.name;
            if (name === 'canceled') {
              return `${data.axisValue} - Session Canceled`;
            }
            if (name === 'missed') {
              return `${data.axisValue} - Session Missed`;
            }
            const groups = getStudentGroupCountTooltipContent(name);
            const formattedStaffs = getStaffDetails(name);
            return `<div>
                      <div style="font-weight: bold;">${data.axisValue} -  ${data.value}%</div>
                      ${groups} ${formattedStaffs}
                    </div>`;
          } catch (error) {
            return '';
          }
        },
      },
    };
  }

  function getStudentGroupCountTooltipContent(sessionId) {
    const studentGroup = getActiveStudentGroup();
    const scheduleList = studentGroup.getIn(['student_attendance', 'schedule'], List());
    const schedule = scheduleList.find((s) => s.get('_id') === sessionId) || Map();
    const studentGroups = schedule.get('student_groups', List()).reduce((acc, s) => {
      const groupName = getFormattedGroupName(
        studentGroupRename(s.get('group_name', ''), programId),
        isIndGroup(s.get('group_name', '')) ? 1 : 2
      );
      const sessionGroups = s
        .get('session_group', List())
        .map(
          (sg) =>
            `<div>${groupName} - ${getFormattedGroupName(sg.get('group_name', ''), 3)} - ${sg.get(
              'present_count',
              0
            )} / ${sg.get('student_count', 0)}</div>`
        );
      return acc + sessionGroups.join('');
    }, '');
    return studentGroups;
  }

  function getStaffDetails(sessionId) {
    const studentGroup = getActiveStudentGroup();
    const scheduleList = studentGroup.getIn(['student_attendance', 'schedule'], List());
    const schedule = scheduleList.find((s) => s.get('_id') === sessionId) || Map();
    const sessionGroups = schedule.get('staffs', List()).map(
      (staff) =>
        `<div>
        <span>
        ${staff.getIn(['staff_name', 'first'], '')} 
          ${staff.getIn(['staff_name', 'middle'], '')} ${staff.getIn(
          ['staff_name', 'last'],
          ''
        )}  </span>
        <span style="float: right;text-align: right;">
          <span  style='color: ${
            staff.get('status', '') === 'present'
              ? 'green'
              : staff.get('status', '') === 'absent'
              ? 'red'
              : '-'
          }'> ${
          staff.get('status', '') === 'present'
            ? 'P'
            : staff.get('status', '') === 'absent'
            ? 'A'
            : '--'
        }  </span>
          </span>
          </div>`
    );
    return sessionGroups.join('');
  }

  function getSessionCompletionStatusChartData(programId) {
    const studentGroup = getActiveStudentGroup();
    return getCourseSessionTypesChartData(
      studentGroup.get('session_type_schedule', List()),
      programId
    );
  }

  function getStaffSessionCompletionStatusChartData(programId) {
    const studentGroup = getActiveStudentGroup();
    const subject =
      studentGroup
        .get('subject_session_status', List())
        .find((subject) => subject.get('_subject_id') === filters.get('subject')) || Map();
    return getStaffSessionTypesChartData(subject, programId);
  }

  function getModePieChartData() {
    const studentGroup = getActiveStudentGroup();
    const remoteCount = studentGroup.get('remote_count', 0);
    const onsiteCount = studentGroup.get('onsite_count', 0);
    const totalCount = remoteCount + onsiteCount;

    const options = getBasicPieChartData({
      title: t('reports_analytics.mode'),
      data: [
        {
          value: calculatePercentage(remoteCount, totalCount, false),
          name: t('reports_analytics.remote'),
        },
        {
          value: calculatePercentage(onsiteCount, totalCount, false),
          name: getEnvLabelChanged()
            ? indVerRename('Onsite', programId)
            : t('reports_analytics.onsite'),
        },
      ],
      labelFormatter: '{c}%',
    });

    return {
      ...options,
      legend: {
        ...options.legend,
        formatter: (name) => {
          try {
            const formattedName = name.toLowerCase();
            if (formattedName === 'onsite') {
              return `${name}\n${onsiteCount} Sessions`;
            }
            return `${name}\n${remoteCount} Sessions`;
          } catch (error) {
            return name;
          }
        },
      },
      tooltip: {
        ...options.tooltip,
        formatter: (params) => {
          try {
            return `${params.name} - ${params.value}%`;
          } catch (error) {
            return '';
          }
        },
      },
    };
  }

  function getTotalStudentsPieChartData() {
    const maleCount = courseOverview.get('male_count', 0);
    const femaleCount = courseOverview.get('female_count', 0);
    const totalCount = maleCount + femaleCount;

    const options = getBasicPieChartData({
      title: `${t('reports_analytics.total_students')} - ${totalCount}`,
      data: [
        {
          value: calculatePercentage(maleCount, totalCount, false),
          name: t('reports_analytics.male'),
        },
        {
          value: calculatePercentage(femaleCount, totalCount, false),
          name: t('reports_analytics.female'),
        },
      ],
      labelFormatter: '{c}%',
    });
    return {
      ...options,
      legend: {
        ...options.legend,
        formatter: (name) => {
          try {
            const formattedName = name.toLowerCase();
            if (formattedName === 'male') {
              return `${name} ${maleCount}`;
            }
            return `${name} ${femaleCount}`;
          } catch (error) {
            return name;
          }
        },
      },
      tooltip: {
        ...options.tooltip,
        formatter: (params) => {
          try {
            return `${params.name} - ${params.value}%`;
          } catch (error) {
            return '';
          }
        },
      },
    };
  }

  function getActiveStudentGroupSubjects() {
    const studentGroup = getActiveStudentGroup();
    return studentGroup.get('subject_session_status', List());
  }

  const sessionLegend = getTPCLegend(programId);
  return (
    <div className="mt-3 text-left">
      <CourseDetailsAccordion course={courseOverview.get('course_details', Map())} />
      <div className="mt-3 d-flex align-items-center">
        <p className="font-weight-500 digi-black f-18 mr-2 mb-0">
          {' '}
          <Trans i18nKey={'reports_analytics.student_group'} />
        </p>
        <FormControl variant="outlined" size="small" className="w-100px">
          <Select
            native
            value={filters.get('studentGroup', '')}
            onChange={(e) => handleFilterChange('studentGroup', e.target.value)}
          >
            {getStudentGroupOptions().map((studentGroup) => (
              <option key={studentGroup.get('value')} value={studentGroup.get('value')}>
                {studentGroup.get('name')}
              </option>
            ))}
          </Select>
        </FormControl>
      </div>
      <div className="mt-3">
        <p className="font-weight-500 digi-black f-18 mr-2 mb-2">
          <Trans i18nKey={'reports_analytics.delivered_session'} />
        </p>
        <CloSloChart
          studentGroupId={filters.get('studentGroup', '')}
          domains={getActiveStudentGroup().get('domains', List())}
          programId={programId}
        />
      </div>
      <div className="mt-3">
        <p className="font-weight-500 digi-black f-18 mr-2 mb-2">
          <Trans i18nKey={'reports_analytics.attendance_status'} />
        </p>
        <div className="bg-white border-radious-8">
          <InfoChart
            countInfoCardData={getStudentAttendanceInfoCardData()}
            reportView={true}
            chartData={getStudentAttendanceChartData()}
            xAxisName={t('reports_analytics.no_of_sessions').toUpperCase()}
          />
        </div>
      </div>
      <div className="mt-3">
        <p className="font-weight-500 digi-black f-18 mr-2 mb-2">
          <Trans i18nKey={'reports_analytics.planned_session'} />
        </p>

        <div className="p-2 bg-white border-radious-8">
          <div className="row">
            <div className="col-md-4">
              <CountInfoCards data={getSessionPlanedStatusInfoCardData()} />
            </div>
            <div className="col-md-8">
              <div className="mt-4 d-flex justify-content-end">
                <FormControl variant="outlined" size="">
                  <Select
                    native
                    onChange={(e) => handlePlannedSession(e.target.value)}
                    id="#planedSessionDrop"
                  >
                    <option value={'session'}>{t('reports_analytics.session')}</option>
                    <option value={'credit'}>{t('reports_analytics.credit_hours')}</option>
                    <option value={'contact'}>{t('reports_analytics.contact_hours')}</option>
                  </Select>
                </FormControl>
              </div>
              <div className="row pt-3 pl-2 pr-2">
                <div className="col-md-4">
                  <div className="p-4">
                    <CircularProgressbar
                      value={
                        isNaN(parseFloat(planedSessions.theoryPercentage)) === true
                          ? 0
                          : planedSessions.theoryPercentage
                      }
                      text={`${
                        isNaN(parseFloat(planedSessions.theoryPercentage)) === true
                          ? 0
                          : planedSessions.theoryPercentage
                      }%`}
                      styles={buildStyles({
                        strokeLinecap: 'butt',
                        textSize: '16px',
                        pathTransitionDuration: 1.5,
                      })}
                    />
                    <p className="mb-0 mt-2 f-16 text-center bold">
                      {getEnvLabelChanged() ? (
                        sessionLegend[0]
                      ) : (
                        <Trans i18nKey={'reports_analytics.theory'} />
                      )}
                    </p>
                    <p className="mb-0 f-16 text-center bold">
                      {planedSessions.theory} / {planedSessions.theoryTotal}{' '}
                    </p>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="p-4">
                    <CircularProgressbar
                      value={
                        isNaN(parseFloat(planedSessions.practicalPercentage)) === true
                          ? 0
                          : planedSessions.practicalPercentage
                      }
                      text={`${
                        isNaN(parseFloat(planedSessions.practicalPercentage)) === true
                          ? 0
                          : planedSessions.practicalPercentage
                      }%`}
                      styles={buildStyles({
                        strokeLinecap: 'butt',
                        textSize: '16px',
                        pathTransitionDuration: 1.5,
                      })}
                    />
                    <p className="mb-0 mt-2 f-16 text-center bold">
                      {getEnvLabelChanged() ? (
                        sessionLegend[1]
                      ) : (
                        <Trans i18nKey={'reports_analytics.practical'} />
                      )}
                    </p>
                    <p className="mb-0 f-16 text-center bold">
                      {planedSessions.practical} / {planedSessions.practicalTotal}{' '}
                    </p>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="p-4">
                    <CircularProgressbar
                      value={
                        isNaN(parseFloat(planedSessions.clinicalPercentage)) === true
                          ? 0
                          : planedSessions.clinicalPercentage
                      }
                      text={`${
                        isNaN(parseFloat(planedSessions.clinicalPercentage)) === true
                          ? 0
                          : planedSessions.clinicalPercentage
                      }%`}
                      styles={buildStyles({
                        strokeLinecap: 'butt',
                        textSize: '16px',
                        pathTransitionDuration: 1.5,
                      })}
                    />
                    <p className="mb-0 mt-2 f-16 text-center bold">
                      {getEnvLabelChanged() ? (
                        sessionLegend[2]
                      ) : (
                        <Trans i18nKey={'reports_analytics.clinical'} />
                      )}
                    </p>
                    <p className="mb-0 f-16 text-center bold">
                      {planedSessions.clinical} / {planedSessions.clinicalTotal}{' '}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-3">
        <p className="font-weight-500 digi-black f-18 mr-2 mb-2">
          <Trans i18nKey={'reports_analytics.scheduled_session'} />{' '}
        </p>
        <div className="bg-white border-radious-8">
          <InfoChart
            countInfoCardData={getSessionCompletionStatusInfoCardData()}
            chartData={getSessionCompletionStatusChartData(programId)}
          />
        </div>
      </div>

      <div className="mt-3">
        <p className="font-weight-500 digi-black f-18 mr-2 mb-2">
          <Trans i18nKey={'reports_analytics.support_session'} />
        </p>
        <div className="bg-white border-radious-8">
          <InfoChart
            countInfoCardData={getSupportSessionInfoCardDataInfoData()}
            chartData={getSupportSessionChartData(programId)}
            reportView={true}
          />
        </div>
      </div>

      <div className="mt-3">
        <div className="d-flex align-items-center mb-3">
          <p className="font-weight-500 digi-black f-18 mr-2 mb-0">
            <Trans i18nKey={'reports_analytics.staff_session'} />{' '}
          </p>
          <FormControl variant="outlined" size="small" className="w-200px">
            <Select
              native
              value={filters.get('subject', '')}
              onChange={(e) => handleFilterChange('subject', e.target.value)}
            >
              {getSubjectOptions().map((subject) => (
                <option key={subject.get('value')} value={subject.get('value')}>
                  {subject.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
        <div className="p-2 bg-white border-radious-8">
          <div className="row">
            <SubjectsCard subjects={getActiveStudentGroupSubjects()} />
            <div className="col-md-8">
              <div>
                <ReactECharts
                  option={getStaffSessionCompletionStatusChartData(programId)}
                  className="echarts-height-350"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="row mt-3 mb-3">
        <div className="col-md-6 pr-0">
          <div className="bg-white border-radious-8 p-2">
            <ReactECharts option={getModePieChartData()} />
          </div>
        </div>
        <div className="col-md-6">
          <div className="bg-white border-radious-8 p-2">
            <ReactECharts option={getTotalStudentsPieChartData()} />
          </div>
        </div>
      </div>
    </div>
  );
}

CourseOverview.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  courseOverview: PropTypes.instanceOf(Map),
  getCourseOverviewData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    courseOverview: selectCourseOverviewData(state),
  };
};

export default connect(mapStateToProps, actions)(CourseOverview);
