import React, { Component } from 'react';
import { List, Map } from 'immutable';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Button } from 'react-bootstrap';
import { FormControl } from '@mui/material';
import { Trans, withTranslation } from 'react-i18next';
import Steps from './Steps';
import Input from 'Widgets/FormElements/Input/Input';
import * as actions from '_reduxapi/program_input/action';
import { selectEditedCourse, selectActiveStep } from '_reduxapi/program_input/selectors';
import ArrowLeft from 'Assets/arrow.svg';
import { getURLParams, getLang, removeURLParams, jsUcfirstAll } from 'utils';
import AutoComplete from './StaffSearchInput';
import '../../css/program.css';

const lang = getLang();

export const getUserFullName = (user) => {
  const firstName = user.getIn(['name', 'first'], '');
  const middleName = user.getIn(['name', 'middle'], '');
  const lastName = user.getIn(['name', 'last'], '');
  const fullName = [firstName, middleName, lastName].filter((item) => item).join(' ');
  return jsUcfirstAll(fullName);
};

export const getStaffNameWithId = (user) => {
  return `${user.get('user_id', '')} - ${getUserFullName(user)}`;
};

class AddCourseComponent extends Component {
  constructor(props) {
    super(props);
    const { t } = this.props;
    const {
      match: {
        params: { curriculumId, courseId },
      },
    } = props;
    this.state = {
      curriculumId,
      courseId,
      STEPS: [t('constant.course_details')],
      isLoading: false,
      showStaffOptions: false,
      staffOptions: List(),
    };
    this.handleBackClick = this.handleBackClick.bind(this);
    this.handleChange = this.handleChange.bind(this);
    this.typingTimeout = null;
  }

  componentDidMount() {
    const { setData, getCourseWithStaff } = this.props;
    const { courseId } = this.state;
    setData(
      Map({
        activeStep: 0,
        editedCourse: Map({
          course_code: '',
          course_name: '',
          staffIds: [],
        }),
      })
    );
    if (courseId !== 'new') getCourseWithStaff(courseId, this.setEditedCourse);
  }

  setEditedCourse = (data) => {
    const { setData } = this.props;
    const staffIds = data
      .get('staffIds', List())
      .map((staff) => ({
        label: getStaffNameWithId(staff),
        value: staff.get('_id', ''),
      }))
      .toJS();
    setData(Map({ editedCourse: data.set('staffIds', staffIds) }));
  };

  saveCourseDetails = () => {
    const { curriculumId, courseId } = this.state;
    const { editedCourse, addCourseFeed, updateCourseFeed, history, location } = this.props;
    const staffIds = editedCourse.get('staffIds', []).map((item) => item.value);
    const isNewCourse = courseId === 'new';
    const data = {
      ...(isNewCourse ? { programId: getURLParams('_id', true) } : { courseId }),
      curriculumId,
      courseCode: editedCourse.get('course_code', '').trim(),
      courseName: editedCourse.get('course_name', '').trim(),
      staffIds,
    };

    const saveCourseFunc = isNewCourse ? addCourseFeed : updateCourseFeed;
    saveCourseFunc({ data, callback: () => this.callback({ history, location }) });
  };

  callback({ history, location }) {
    const url = `/program-input/configuration/course-masterlist${
      getURLParams('versionName', true)
        ? removeURLParams(history.location, ['versionName', 'versionType'])
        : location.search
    }`;

    history.push(url);
  }

  handleBackClick() {
    this.props.history.goBack();
  }

  handleChange(e, name) {
    let value = e.target.value;
    const { editedCourse, setData } = this.props;

    if (name === 'course_code') value = value.replace(/\s{2,}/g, ' ').toUpperCase();
    else if (name === 'course_name') value = jsUcfirstAll(value);
    setData(Map({ editedCourse: editedCourse.set(name, value) }));
  }

  handleInputChange = (_, newInputValue, reason) => {
    this.setState({ showStaffOptions: false, staffOptions: List() });
    if (this.typingTimeout) clearTimeout(this.typingTimeout);

    if (reason === 'input')
      this.typingTimeout = setTimeout(() => this.fetchOptions(newInputValue), 1000);
  };

  fetchOptions = async (searchKey) => {
    if (!searchKey) return;
    this.setState({ isLoading: true, showStaffOptions: true });

    this.props.getStaffList({
      params: { searchKey, limit: 10, pageNo: 1 },
      callback: (data = []) => {
        const staffOptions = data.get('userList', List()).map((user) => {
          const label = getStaffNameWithId(user);
          return Map({ label, value: user.get('_id', '') });
        });
        this.setState({ isLoading: false, staffOptions });
      },
    });
  };

  handleSelectionChange = (newValue) => {
    const { editedCourse, setData } = this.props;
    setData(Map({ editedCourse: editedCourse.set('staffIds', newValue) }));
  };

  handleCloseStaffOptions = () => {
    this.setState({ showStaffOptions: false });
  };

  isDisabled = () => {
    const { editedCourse } = this.props;
    return (
      !editedCourse.get('course_code', '').trim() ||
      !editedCourse.get('course_name', '').trim() ||
      !editedCourse.get('staffIds', []).length
    );
  };

  render() {
    const { courseId, isLoading, showStaffOptions, staffOptions } = this.state;
    const { activeStep, editedCourse } = this.props;

    return (
      <React.Fragment>
        <div className="d-flex bg-white p-3">
          <div
            className={`f-20 digi-font-500 digi-black col-md-4 ${lang === 'ar' ? 'text-left' : ''}`}
            style={{ padding: '0px 8px' }}
          >
            <img
              src={ArrowLeft}
              alt="back"
              className="mr-3 cursor-pointer"
              onClick={this.handleBackClick}
            />
            <span>
              {courseId === 'new' ? (
                <Trans i18nKey={'add'}></Trans>
              ) : (
                <Trans i18nKey={'edit'}></Trans>
              )}{' '}
              <Trans i18nKey={'courses'}></Trans>
            </span>
          </div>
          <div className="col-md-8">
            <Steps steps={this.state.STEPS} activeStep={activeStep} />
          </div>
        </div>

        <div className="main bg-gray pb-5">
          <div className="bg-gray">
            <div className="container-fluid">
              <div className="row pb-4">
                <div className="col-md-12">
                  <div className="px-5 py-4">
                    <div className="d-flex justify-content-between">
                      <h5 className="pb-2 pt-2">
                        <Trans i18nKey={'course_master_list'}></Trans>{' '}
                        <i className="fa fa-angle-right pr-2 pl-2" aria-hidden="true"></i>
                        {courseId === 'new' ? (
                          <Trans i18nKey={'add'}></Trans>
                        ) : (
                          <Trans i18nKey={'edit'}></Trans>
                        )}{' '}
                      </h5>
                    </div>

                    <div className="program_card bg-white min_h">
                      <div className="px-5 py-4 min_h">
                        {activeStep === 0 && (
                          <>
                            <div className="d-flex justify-content-between mb-2">
                              <b>
                                <Trans i18nKey={'basic_details'}></Trans>
                              </b>
                              <small className="text-gray">
                                {' '}
                                <Trans i18nKey={'all_fields_mandatory'}></Trans>{' '}
                              </small>
                            </div>

                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  {' '}
                                  <Trans i18nKey={'Course_Code'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <Input
                                  elementType="floatinginput"
                                  elementConfig={{ type: 'text' }}
                                  value={editedCourse.get('course_code', '')}
                                  changed={(e) => this.handleChange(e, 'course_code')}
                                />
                              </div>
                            </div>

                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  {' '}
                                  <Trans i18nKey={'course_name_small'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <Input
                                  elementType="floatinginput"
                                  elementConfig={{ type: 'text' }}
                                  value={editedCourse.get('course_name', '')}
                                  changed={(e) => this.handleChange(e, 'course_name')}
                                />
                              </div>
                            </div>

                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  <Trans i18nKey={'staff_name'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <FormControl fullWidth variant="standard" size="small">
                                  <AutoComplete
                                    value={editedCourse.get('staffIds', [])}
                                    options={staffOptions.toJS()}
                                    onChange={this.handleSelectionChange}
                                    onInputChange={this.handleInputChange}
                                    loading={isLoading}
                                    open={showStaffOptions}
                                    onClose={this.handleCloseStaffOptions}
                                  />
                                </FormControl>
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="d-flex justify-content-between pt-3">
                      <div className="">
                        <b className="pr-3">
                          <Button variant="outline-primary" onClick={this.handleBackClick}>
                            <Trans i18nKey={'cancel'}></Trans>
                          </Button>
                        </b>
                      </div>

                      <div className="">
                        <div className="float-right">
                          <b className="pr-2">
                            <Button
                              variant="primary"
                              onClick={this.saveCourseDetails}
                              disabled={this.isDisabled()}
                            >
                              <Trans i18nKey={courseId === 'new' ? 'add_course' : 'save'}></Trans>
                            </Button>
                          </b>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

const AddCourse = withTranslation()(AddCourseComponent);

AddCourseComponent.propTypes = {
  history: PropTypes.object,
  match: PropTypes.object,
  location: PropTypes.object,
  editedCourse: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  activeStep: PropTypes.number,
  t: PropTypes.func,
  addCourseFeed: PropTypes.func,
  getStaffList: PropTypes.func,
  getCourseWithStaff: PropTypes.func,
  updateCourseFeed: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    editedCourse: selectEditedCourse(state),
    activeStep: selectActiveStep(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(AddCourse);
