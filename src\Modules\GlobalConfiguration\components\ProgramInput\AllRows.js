import React, { useState, Suspense, useContext } from 'react';
import { AccordionProgramInput } from '../ReusableComponent';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import { Dropdown } from 'react-bootstrap';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import { programInputContext } from './NewProgramInput';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';
import Module1 from 'Assets/img/globalConfiguration/module1.svg';
import Module2 from 'Assets/img/globalConfiguration/module2.svg';
import Standard from 'Assets/img/globalConfiguration/standardCreditSystem.svg';
import Dynamic from 'Assets/img/globalConfiguration/dynamicCreditSystem.svg';

const RenameModal = React.lazy(() => import('../../modal/RenameModal'));
const AddEditProgramTypeModal = React.lazy(() => import('../../modal/AddEditProgramTypeModal'));
const DeleteModal = React.lazy(() => import('../../modal/DeleteModal'));

const DisplayHeading = ({ onOpenData, open, onCloseData, heading }) => (
  <div className="row w-100">
    <div className="col-md-12">
      <div className="d-flex justify-content-between">
        <div className="d-flex">
          <div className="f-16 digi-brown remove_hover">
            <b>
              <Trans i18nKey={`global_configuration.${heading}`}></Trans>
            </b>
            {!open && onCloseData()}
          </div>
        </div>
        {open && onOpenData()}
      </div>
    </div>
  </div>
);

DisplayHeading.propTypes = {
  onOpenData: PropTypes.func,
  open: PropTypes.bool,
  onCloseData: PropTypes.func,
  heading: PropTypes.string,
};
export function LabelConfiguration() {
  const [selectedLabel, setSelectedLabel] = useState('');
  const [open, setOpen] = useState(false);
  const programInputData = useContext(programInputContext);
  const {
    show,
    handleOpen,
    programInput,
    updateLabel,
    basicDetails,
    institutionHeader,
    getLanguage,
    handleReset,
  } = programInputData;
  const onCloseData = () => (
    <div className="text-grey f-15">
      {programInput.getIn(['labelConfiguration', 0, 'labels'], List()).size}{' '}
      <Trans i18nKey={'global_configuration.labels'}></Trans>
    </div>
  );
  const onOpenData = () => (
    <div className="text-center">
      <small className="f-18" onClick={(e) => e.stopPropagation()}>
        <Dropdown>
          <Dropdown.Toggle variant="" id="dropdown-table" className="table-dropdown" size="sm">
            <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
          </Dropdown.Toggle>
          <Dropdown.Menu>
            <Dropdown.Item href="#" onClick={(e) => handleReset(e)}>
              <Trans i18nKey={'global_configuration.reset'}></Trans>
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>{' '}
      </small>
    </div>
  );
  const displayHeading = () => (
    <DisplayHeading
      heading={'label_configuration'}
      onOpenData={onOpenData}
      onCloseData={onCloseData}
      open={open}
    />
  );
  const labelConfig = (label, selectedLabel, key) => {
    return (
      <>
        <div className="mb-2 d-flex justify-content-between">
          <div className="bold mb-2">{label}</div>
          <div
            className="float-right icon-blue"
            onClick={() => {
              handleOpen('labelConfig');
              setSelectedLabel(selectedLabel);
            }}
          >
            <Trans i18nKey={'global_configuration.rename'}></Trans>
          </div>
        </div>
        {[1, 3, 6, 8].includes(key) && <hr />}
      </>
    );
  };
  const displayBody = () => (
    <div className="container body">
      <div className="row">
        <div className="float-right mb-2 text-blue ml-auto"></div>
      </div>
      {getLanguage &&
        getLanguage.size > 0 &&
        getLanguage.get('labels', List()).map((data, key) => (
          <div className="body" key={key}>
            {labelConfig(
              !data.get('translatedInput', '')
                ? data.get('name', '')
                : data.get('translatedInput', ''),
              data.get('name', ''),
              key
            )}
          </div>
        ))}
    </div>
  );
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => setOpen((previousData) => !previousData)}
      />
      <hr />
      {show['labelConfig'] && (
        <Suspense fallback="">
          <RenameModal
            show={show}
            handleClose={() => handleOpen('labelConfig')}
            labelName={selectedLabel}
            labelConfiguration={programInput}
            updateLabel={updateLabel}
            selectedLanguage={basicDetails.get('language', List())}
            institutionHeader={institutionHeader}
          />
        </Suspense>
      )}
    </>
  );
}
export function ProgramType() {
  const [open, setOpen] = useState(false);
  const programInputData = useContext(programInputContext);
  const {
    show,
    handleOpen,
    programInput,
    selectedProgramType,
    setData,
    type,
    updateProgramType,
    institutionHeader,
  } = programInputData;
  const onCloseData = () => (
    <div className="text-grey f-15">
      {programInput.get('programType', List()).size}{' '}
      <Trans i18nKey={'global_configuration.program_type'}></Trans>
    </div>
  );
  const onOpenData = () =>
    programInput.get('programType', List()).size > 0 && (
      <div
        className="float-right mb-2 text-blue ml-auto icon-blue"
        onClick={(e) => {
          e.stopPropagation();
          handleOpen('programType');
        }}
      >
        + <Trans i18nKey={'global_configuration.add_new_type'}></Trans>
      </div>
    );
  const displayHeading = () => (
    <DisplayHeading
      heading={'program_type'}
      onOpenData={onOpenData}
      onCloseData={onCloseData}
      open={open}
    />
  );
  const displayBody = () => (
    <div className="container">
      {programInput.get('programType', List()).size > 0 ? (
        <>
          {programInput.get('programType', List()) &&
            programInput.get('programType', List()).map((item, key) => (
              <div key={key} className="mb-2 d-flex justify-content-between">
                <div>
                  <div className="bold mb-2 body">
                    {item.get('name', '')} - {item.get('code', '')}
                  </div>
                  <div className="mb-2"></div>
                </div>
                <div className="float-right digi-pl-8 digi-pr-8">
                  <img
                    src={EditIcon}
                    alt="edit"
                    className="digi-pr-12 remove_hover"
                    onClick={() => handleOpen('programType', 'update', item)}
                  />

                  <img
                    src={DeleteIcon}
                    alt="Delete"
                    className="digi-pr-12 remove_hover"
                    onClick={() => handleOpen('programTypeDelete', 'programTypeDelete', item)}
                  />
                </div>
              </div>
            ))}
        </>
      ) : (
        <div className="row digi-tbl-pad pt-3 pl-0 digi-text-justify">
          <div className="col-md-12 btn-description-border text-center p-5">
            <button
              className="remove_hover btn btn-description mr-3"
              onClick={() => handleOpen('programType')}
            >
              <Trans i18nKey={'global_configuration.create_type'}></Trans>
            </button>
          </div>
        </div>
      )}
    </div>
  );
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => setOpen((previousData) => !previousData)}
      />
      {show['programType'] && (
        <Suspense fallback="">
          <AddEditProgramTypeModal
            show={show}
            handleClose={() => handleOpen('programType')}
            selectedProgramType={selectedProgramType}
            type={type}
            settingId={programInput.get('_id')}
            updateProgramType={updateProgramType}
            setData={setData}
            institutionHeader={institutionHeader}
          />
        </Suspense>
      )}
      {show['programTypeDelete'] && (
        <Suspense fallback="">
          <DeleteModal
            open={show}
            handleClose={() => handleOpen('programTypeDelete')}
            type={type}
            item={selectedProgramType}
            settingId={programInput.get('_id')}
            updateProgramType={updateProgramType}
            institutionHeader={institutionHeader}
          />
        </Suspense>
      )}
      <hr />
    </>
  );
}

export function CurriculumNaming() {
  const [open, setOpen] = useState(false);
  const programInputData = useContext(programInputContext);
  const { programInput, handleRadioButton, radioValue } = programInputData;
  const onCloseData = () => (
    <div className="text-grey f-15">
      {[1, 2].includes(radioValue.get('curriculumNaming', ''))
        ? `${t('global_configuration.mode')} ${radioValue.get('curriculumNaming', '')}`
        : t('global_configuration.manual_naming')}
    </div>
  );
  const onOpenData = () => {};
  const displayHeading = () => (
    <DisplayHeading
      heading={'curriculum_naming'}
      onOpenData={onOpenData}
      onCloseData={onCloseData}
      open={open}
    />
  );

  const displayBody = () => (
    <div className="container">
      {' '}
      <div className="float-left">
        <FormControl component="fieldset">
          <div className="mb-3">
            {programInput.get('curriculumNaming', List()).size > 0 &&
              programInput.get('curriculumNaming', List()).map((item, key) => (
                <div className="mb-3" key={key}>
                  <FormControlLabel
                    className="float-left"
                    value={item.get('mode', 3)}
                    label={
                      [1, 2].includes(item.get('mode', ''))
                        ? `${t('global_configuration.mode')} ${item.get('mode', '')}`
                        : t('global_configuration.manual_naming')
                    }
                    control={
                      <Radio
                        color="primary"
                        className="float-left"
                        checked={radioValue.get('curriculumNaming', 3) === item.get('mode', 3)}
                      />
                    }
                    onClick={() => {
                      handleRadioButton('curriculumNaming', item.get('mode', 3), item.get('_id'));
                    }}
                  />
                  <div className="clearfix"> </div>
                  {[1, 2].includes(item.get('mode', '')) && (
                    <div className="row ml-2">
                      <img src={item.get('mode', '') === 1 ? Module1 : Module2} alt="mode1" />
                    </div>
                  )}
                </div>
              ))}
          </div>
        </FormControl>
      </div>{' '}
      <div className="clearfix"> </div>
    </div>
  );
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => setOpen((previousData) => !previousData)}
      />
      <hr />
    </>
  );
}

export function CreditHours() {
  const [open, setOpen] = useState(false);
  const programInputData = useContext(programInputContext);
  const { programInput, handleRadioButton, radioValue, disabled } = programInputData;
  const onCloseData = () => (
    <div className="text-grey f-15">
      {radioValue.get('creditHours', '') === 'standard'
        ? t('global_configuration.standard')
        : t('global_configuration.dynamic')}
    </div>
  );
  const onOpenData = () => {};
  const displayHeading = () => (
    <DisplayHeading
      heading={'credit_system'}
      onOpenData={onOpenData}
      onCloseData={onCloseData}
      open={open}
    />
  );
  const displayBody = () => (
    <div className="container">
      <div className="float-left">
        <FormControl component="fieldset">
          <div className="mb-3">
            {programInput.get('creditHours', List()).size > 0 &&
              programInput
                .get('creditHours', List())
                .filter((item) => item.get('mode', '') !== 'contact')
                .map((item, key) => (
                  <div className="mb-3" key={key}>
                    <FormControlLabel
                      className="float-left"
                      value={item.get('mode', '')}
                      label={t(`global_configuration.${item.get('mode', '')}`)}
                      disabled={disabled}
                      control={
                        <Radio
                          color="primary"
                          checked={radioValue.get('creditHours', '') === item.get('mode', '')}
                        />
                      }
                      onClick={() =>
                        handleRadioButton('creditHours', item.get('mode', ''), item.get('_id'))
                      }
                    />
                    <div className="clearfix"> </div>
                    <div className="row ml-2">
                      <img
                        src={item.get('mode', '') === 'standard' ? Standard : Dynamic}
                        alt="mode1"
                      />
                    </div>
                  </div>
                ))}
          </div>
        </FormControl>
      </div>
      <div className="clearfix"> </div>
    </div>
  );
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => setOpen((previousData) => !previousData)}
      />
      <hr />
    </>
  );
}
