/* eslint-disable react/jsx-key */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Table } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import Search from '../../Widgets/Search/Search';
import Pagination from '../../Components/StaffManagement/Pagination';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import * as actions from '../../_reduxapi/user_management/action';
import Export from './Export';
class InactiveStudent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      message: '',
      active: true,
      searchText: '',
      limit: 10,
      totalPages: 1,
      pageLength: 1,
    };
    this.timeout = 0;
  }

  componentDidMount() {
    this.fetchApi({});
  }

  successTrigger = (res) => {
    const data = res.data.data.map((data) => {
      return {
        id: data._id,
        family: data.name.family,
        academic: data.academic,
        email: data.email,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        gender: data.gender,
        nationality_id: data.address.nationality_id,
        enrollment_year: data.enrollment_year,
        program_no: data.program_no,
        isChecked: false,
      };
    });
    this.setState({
      data: data,
      isLoading: false,
      totalPages: res.data.totalPages,
    });
  };

  fetchApi = ({ limit = 10, pageLength = 1 }) => {
    const { getUserManagementList } = this.props;
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });
    getUserManagementList(
      {
        searchText: searchText,
        type: 'student',
        status: 'inactive',
        limit: limit,
        pageLength: pageLength,
        slug: 'get_all',
      },
      this.successTrigger,
      () => {
        this.setState({
          data: [],
          isLoading: false,
          totalPages: 1,
          pageLength: 1,
        });
      }
    );
  };

  handleActive = (data) => {
    this.setState({
      active: true,
      isLoading: true,
    });
    const datas = {
      id: data.id,
      status: this.state.active,
    };
    axios
      .post(`/user/active_inactive`, datas)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
          },
          () => {
            this.fetchApi({});
          }
        );
        this.props.fetchApi();
        this.props.history.push({
          pathname: '/student/management',
          state: {
            completeView: false,
            pendingView: true,
            inactiveView: false,
          },
        });
        NotificationManager.success(t('user_management.Student_Activated_SuccessFully'));
      })
      .catch((error) => {
        NotificationManager.error(`${error.response.data.message}`);
        this.setState({
          isLoading: false,
        });
      });
  };

  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      const { limit } = this.state;
      setTimeout(() => {
        this.fetchApi({ limit });
        this.setState({ pageLength: 1 });
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState({ searchText: e.target.value }, () => this.doSearch(e));
  };

  pagination = (e) => {
    this.setState(
      {
        limit: e.target.value,
        pageLength: 1,
      },
      () => {
        setTimeout(() => {
          this.fetchApi({ limit: this.state.limit });
        }, 500);
      }
    );
  };

  pageCount = (value) => {
    this.setState(
      {
        pageLength: value,
      },
      () => {
        this.fetchApi({ pageLength: value, limit: this.state.limit });
      }
    );
  };

  render() {
    const header = [
      <Trans i18nKey={'academic_no'}></Trans>,
      <Trans i18nKey={'email'}></Trans>,
      <Trans i18nKey={'first_name'}></Trans>,
      'Middle Name',
      'Last Name',
      <Trans i18nKey={'gender'}></Trans>,
      <Trans i18nKey={'national_id'}></Trans>,
    ];
    if (
      CheckPermission(
        'tabs',
        'User Management',
        'Student Management',
        '',
        'Inactive',
        'Update Status'
      )
    ) {
      header.push('Action');
    }
    return (
      <div className="main bg-gray pt-2 pb-5">
        <Loader isLoading={this.state.isLoading} />
        <div className="container-fluid">
          <div className="float-right p-2 d-flex">
            <Export userType="student" status="inactive" />
            {CheckPermission(
              'tabs',
              'User Management',
              'Student Management',
              '',
              'Inactive',
              'Search'
            ) && (
              <Search
                value={this.state.searchText}
                onChange={(e) => this.handleSearch(e)}
                type="student"
              />
            )}
          </div>
          <div className="">
            <div className="clearfix"> </div>
            <React.Fragment>
              <div className="p-3">
                <div className="dash-table">
                  <Table responsive hover>
                    <thead className="th-change">
                      <tr>
                        {header.map((header, i) => (
                          <th key={i}>{header}</th>
                        ))}
                      </tr>
                    </thead>
                    {this.state.data.length === 0 ? (
                      <TableEmptyMessage />
                    ) : (
                      <tbody>
                        {this.state.data.map((data, index) => (
                          <tr className="tr-change" key={index}>
                            <td>{data.academic}</td>
                            <td>{data.email}</td>
                            <td>{data.first}</td>
                            <td>{data.middle}</td>
                            <td>{data.last}</td>
                            <td>{data.gender}</td>
                            <td>{data.nationality_id}</td>
                            {CheckPermission(
                              'tabs',
                              'User Management',
                              'Student Management',
                              '',
                              'Inactive',
                              'Update Status'
                            ) && (
                              <td>
                                <b
                                  className="text-blue remove_hover"
                                  onClick={() => this.handleActive(data)}
                                >
                                  {' '}
                                  Active{' '}
                                </b>
                              </td>
                            )}
                          </tr>
                        ))}
                      </tbody>
                    )}
                  </Table>
                </div>
                <Pagination
                  totalPages={this.state.totalPages}
                  switchPagination={this.pagination}
                  switchPageCount={this.pageCount}
                  pageLength={this.state.pageLength}
                />
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  }
}

InactiveStudent.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
  fetchApi: PropTypes.func,
};

export default connect(null, actions)(withRouter(InactiveStudent));
