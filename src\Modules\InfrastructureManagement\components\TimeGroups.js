import React, { Component } from 'react';
import { Table } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import PropTypes from 'prop-types';
import { Map, List, fromJS } from 'immutable';
import { format, set, startOfDay, startOfYear, isAfter, isEqual } from 'date-fns';
import { Trans } from 'react-i18next';
import { getRandomId, capitalize } from '../utils';
import AlertModal from '../modal/AlertModal';
import Input from '../../../Widgets/FormElements/Input/Input';
import Tooltip from '../../../_components/UI/Tooltip/Tooltip';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { t } from 'i18next';
import { allowedTimeInterval, getLang, indVerRename } from '../../../utils';

const GENDER = {
  MALE: 'male',
  FEMALE: 'female',
  BOTH: 'both',
};
const GENDER_DROPDOWN = [
  {
    name: 'Male',
    value: GENDER.MALE,
  },
  {
    name: 'Female',
    value: GENDER.FEMALE,
  },
  {
    name: 'Male & Female',
    value: GENDER.BOTH,
  },
];
const SLOT = {
  ONSITE: 'onsite',
  REMOTE: 'remote',
};
const SLOTS = [SLOT.ONSITE, SLOT.REMOTE];

const lang = getLang();

class TimeGroups extends Component {
  constructor() {
    super();
    this.state = {
      activeTimeGroup: SLOT.ONSITE,
      editedTimeGroups: Map(),
      addedTimeGroups: fromJS({
        [SLOT.ONSITE]: {},
        [SLOT.REMOTE]: {},
      }),
      page: 1,
      pageSize: 1000,
      modalData: { show: false },
    };
  }

  componentDidMount() {
    const { page, pageSize } = this.state;
    this.props.getTimeGroups(page, pageSize);
  }

  onTimeGroupChange(slot) {
    this.setState({
      activeTimeGroup: slot,
    });
  }

  capitalize(s) {
    if (typeof s !== 'string') return s;
    return `${s.charAt(0).toUpperCase()}${s.slice(1)}`;
  }

  getTimeGroups() {
    const { activeTimeGroup } = this.state;
    const { timeGroups } = this.props;
    return timeGroups
      .get('data', List())
      .filter((s) => s.get('type') === activeTimeGroup)
      .map((s) =>
        s
          .set('start_time', new Date(s.get('start_time')))
          .set('end_time', new Date(s.get('end_time')))
      );
  }

  addTimeGroup() {
    document.getElementById('time-groups-container').scrollTop = 0;
    this.setState((state) => {
      const { addedTimeGroups, activeTimeGroup } = state;
      const _id = getRandomId();
      return {
        addedTimeGroups: addedTimeGroups.setIn(
          [activeTimeGroup, _id],
          Map({
            _id,
            type: activeTimeGroup,
            start_time: '',
            end_time: '',
            gender: GENDER.MALE,
            operation: 'create',
          })
        ),
      };
    });
  }

  getAddedTimeGroups() {
    const { addedTimeGroups, activeTimeGroup } = this.state;
    return addedTimeGroups.get(activeTimeGroup, Map()).entrySeq();
  }

  handleTimeGroupEdit(group) {
    if (group.get('isAssigned')) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.time_slot_assigned.title'),
        description: t('infra_management.alerts.time_slot_assigned.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    this.setState((state) => {
      return {
        editedTimeGroups: state.editedTimeGroups.set(group.get('_id'), group),
      };
    });
  }

  handleTimeGroupCancelClick(group, operation) {
    const id = group.get('_id');
    this.setState((state) => {
      return {
        ...(operation === 'create' && {
          addedTimeGroups: state.addedTimeGroups.deleteIn([group.get('type'), id]),
        }),
        ...(operation === 'update' && { editedTimeGroups: state.editedTimeGroups.delete(id) }),
      };
    });
  }

  handleTimeChange(date, type, group, operation) {
    const id = group.get('_id');
    if (!date) date = startOfDay(new Date());
    const minutes = +format(date, 'mm');
    if (minutes % allowedTimeInterval() !== 0) date = '';
    this.setState((state) => {
      const { addedTimeGroups, editedTimeGroups } = state;
      return {
        ...(operation === 'create' && {
          addedTimeGroups: addedTimeGroups.setIn([group.get('type'), id, type], date),
        }),
        ...(operation === 'update' && {
          editedTimeGroups: editedTimeGroups.setIn([id, type], date),
        }),
      };
    });
  }

  handleGenderChange(event, type, group, operation) {
    const gender = event.target.value;
    const id = group.get('_id');
    this.setState((state) => {
      const { addedTimeGroups, editedTimeGroups } = state;
      return {
        ...(operation === 'create' && {
          addedTimeGroups: addedTimeGroups.setIn([group.get('type'), id, type], gender),
        }),
        ...(operation === 'update' && {
          editedTimeGroups: editedTimeGroups.setIn([id, type], gender),
        }),
      };
    });
  }

  handleSaveClick(id, operation, isConfirmed = false) {
    const { addedTimeGroups, editedTimeGroups, activeTimeGroup, page, pageSize } = this.state;
    const data =
      operation === 'create'
        ? addedTimeGroups.getIn([activeTimeGroup, id], Map())
        : editedTimeGroups.get(id, Map());
    const startDate = data.get('start_time');
    const endDate = data.get('end_time');
    if (operation !== 'delete' && !(startDate && endDate)) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.invalid_time_slot.title'),
        description: t('infra_management.alerts.invalid_time_slot.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    const requestBody = {
      _id: id,
      ...(operation !== 'delete' && {
        type: data.get('type'),
        start_time: set(startOfYear(new Date()), {
          hours: +format(startDate, 'HH'),
          minutes: +format(startDate, 'mm'),
        }).toISOString(),
        end_time: set(startOfYear(new Date()), {
          hours: +format(endDate, 'HH'),
          minutes: +format(endDate, 'mm'),
        }).toISOString(),
        gender: data.get('gender'),
      }),
      operation,
      page,
      pageSize,
    };
    if (operation === 'delete') {
      const matchedTimeGroup = this.props.timeGroups
        .get('data', List())
        .find((tg) => tg.get('_id') === id);
      if (matchedTimeGroup && matchedTimeGroup.get('isAssigned')) {
        this.setModalData({
          show: true,
          title: t('infra_management.alerts.time_slot_assigned.title'),
          description: t('infra_management.alerts.time_slot_assigned.description'),
          variant: 'alert',
          cancelButtonLabel: t('ok'),
        });
        return;
      }
      if (!isConfirmed) {
        this.setModalData({
          show: true,
          title: t('infra_management.modals.confirm_delete'),
          description: t('infra_management.modals.time_slot.description'),
          variant: 'confirm',
          data: { id, operation },
          cancelButtonLabel: t('no'),
          confirmButtonLabel: t('yes'),
        });
        return;
      }
    } else {
      const { start_time, end_time, type, gender } = requestBody;
      const start = new Date(start_time);
      const end = new Date(end_time);
      if (isAfter(start, end)) {
        this.setModalData({
          show: true,
          title: t('infra_management.alerts.invalid_time_slot.title'),
          description: t('infra_management.alerts.invalid_time_slot.start_date_after_end_date'),
          variant: 'alert',
          cancelButtonLabel: t('ok'),
        });
        return;
      }
      if (isEqual(start, end)) {
        this.setModalData({
          show: true,
          title: t('infra_management.alerts.invalid_time_slot.title'),
          description: t('infra_management.alerts.invalid_time_slot.same_start_end_date'),
          variant: 'alert',
          cancelButtonLabel: t('ok'),
        });
        return;
      }
      const timeGroupExists = this.props.timeGroups
        .get('data', List())
        .find(
          (s) =>
            s.get('type') === type &&
            s.get('gender') === gender &&
            format(new Date(s.get('start_time')), 'hh:mm') ===
              format(new Date(start_time), 'hh:mm') &&
            format(new Date(s.get('end_time')), 'hh:mm') === format(new Date(end_time), 'hh:mm')
        );
      if (timeGroupExists) {
        this.setModalData({
          show: true,
          title: t('infra_management.alerts.time_slot_exists.title'),
          description: t('infra_management.alerts.time_slot_exists.description'),
          variant: 'alert',
          cancelButtonLabel: t('ok'),
        });
        return;
      }
    }
    this.setState(
      {
        ...(operation === 'create' && {
          addedTimeGroups: addedTimeGroups.deleteIn([data.get('type'), id]),
        }),
        ...(operation === 'update' && {
          editedTimeGroups: editedTimeGroups.delete(id),
        }),
      },
      () => this.props.updateTimeGroup(requestBody)
    );
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ id, operation }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.handleSaveClick(id, operation, true)
    );
  }

  render() {
    const { activeTimeGroup, editedTimeGroups, addedTimeGroups, modalData } = this.state;
    return (
      <div className="row">
        <div className="col-md-12 col-xl-10">
          <span className={lang === 'en' ? 'font-weight-bold' : 'font-weight-bold float-left'}>
            <Trans
              i18nKey={'infra_management.infra_settings.time_groups.time_groups_slots'}
            ></Trans>
          </span>
          {CheckPermission(
            'subTabs',
            'Infrastructure Management',
            'Onsite',
            '',
            'Settings',
            '',
            'Time Groups',
            'Add'
          ) && (
            <p
              className="mb-0 float-right remove_hover font-weight-bold"
              onClick={this.addTimeGroup.bind(this)}
            >
              <i className="fa fa-plus-circle pr-2" aria-hidden="true"></i>
              <Trans i18nKey={'add'}></Trans>
            </p>
          )}
        </div>
        <div className="col-md-12 col-xl-10">
          <div className="infra_table_shadows">
            <div id="time-groups-container" className="height-300 m-3">
              <ul id="menu-grouping">
                {SLOTS.map((slot) => (
                  <span
                    key={slot}
                    onClick={this.onTimeGroupChange.bind(this, slot)}
                    className={`tabaligment-blue-small remove_hover ${
                      activeTimeGroup === slot ? 'tabactive-blue-small' : ''
                    }`}
                  >
                    <Trans
                      i18nKey={`role_management.modules_list.classType`}
                      values={{ classType: indVerRename(slot) }}
                    ></Trans>
                  </span>
                ))}
              </ul>
              <Table className="border-radious-8 height-unset">
                <thead className="border">
                  <tr>
                    <th className="border_color_blue">
                      <Trans i18nKey={'infra_management.s_no'}></Trans>
                    </th>
                    <th className="border_color_blue">
                      <Trans
                        i18nKey={'infra_management.infra_settings.time_groups.start_time'}
                      ></Trans>
                    </th>
                    <th className="border_color_blue">
                      <Trans
                        i18nKey={'infra_management.infra_settings.time_groups.end_time'}
                      ></Trans>
                    </th>
                    <th className="border_color_blue">
                      <Trans i18nKey={'infra_management.infra_settings.time_groups.gender'}></Trans>
                    </th>
                    <th className="border_color_blue"></th>
                    <th className="border_color_blue"></th>
                    <th className="border_color_blue"></th>
                  </tr>
                </thead>
                <tbody>
                  {this.getAddedTimeGroups().map((row) => (
                    <EditableTimeGroup
                      key={row[0]}
                      timeGroups={addedTimeGroups.get(activeTimeGroup)}
                      row={row[1]}
                      handleTimeChange={this.handleTimeChange.bind(this)}
                      handleGenderChange={this.handleGenderChange.bind(this)}
                      handleTimeGroupCancelClick={this.handleTimeGroupCancelClick.bind(this)}
                      handleSaveClick={this.handleSaveClick.bind(this)}
                      operation="create"
                    />
                  ))}
                  {this.getTimeGroups().isEmpty() && this.getAddedTimeGroups().isEmpty() && (
                    <tr>
                      <td colSpan="7">
                        <Trans i18nKey={'infra_management.no_records'}></Trans>
                      </td>
                    </tr>
                  )}
                  {this.getTimeGroups().map((row, i) =>
                    !editedTimeGroups.has(row.get('_id')) ? (
                      <tr key={row.get('_id')} className="tr-bottom-border tr-change">
                        <td>{i + 1}</td>
                        <td>{format(row.get('start_time'), 'hh:mm a')}</td>
                        <td>{format(row.get('end_time'), 'hh:mm a')}</td>
                        <td>
                          {' '}
                          <Trans
                            i18nKey={`infra_management.gender.${capitalize(
                              row.get('gender') === GENDER.BOTH
                                ? 'Male & Female'
                                : row.get('gender', '')
                            )}`}
                          ></Trans>
                        </td>
                        <td colSpan="2"></td>
                        <td>
                          {CheckPermission(
                            'subTabs',
                            'Infrastructure Management',
                            'Onsite',
                            '',
                            'Settings',
                            '',
                            'Time Groups',
                            'Edit'
                          ) && (
                            <b className="float-left pr-2 f-16">
                              <Tooltip title={<Trans i18nKey={'edit'}></Trans>}>
                                <i
                                  className="fa fa-pencil"
                                  aria-hidden="true"
                                  onClick={this.handleTimeGroupEdit.bind(this, row)}
                                ></i>
                              </Tooltip>
                            </b>
                          )}
                          {CheckPermission(
                            'subTabs',
                            'Infrastructure Management',
                            'Onsite',
                            '',
                            'Settings',
                            '',
                            'Time Groups',
                            'Delete'
                          ) && (
                            <b className="float-left pl-2 f-16">
                              <Tooltip title={<Trans i18nKey={'delete'}></Trans>}>
                                <i
                                  className="fa fa-trash"
                                  aria-hidden="true"
                                  onClick={this.handleSaveClick.bind(
                                    this,
                                    row.get('_id'),
                                    'delete',
                                    false
                                  )}
                                ></i>
                              </Tooltip>
                            </b>
                          )}
                        </td>
                      </tr>
                    ) : (
                      <EditableTimeGroup
                        key={row.get('_id')}
                        sNo={i + 1}
                        timeGroups={editedTimeGroups}
                        row={row}
                        handleTimeChange={this.handleTimeChange.bind(this)}
                        handleGenderChange={this.handleGenderChange.bind(this)}
                        handleTimeGroupCancelClick={this.handleTimeGroupCancelClick.bind(this)}
                        handleSaveClick={this.handleSaveClick.bind(this)}
                        operation="update"
                      />
                    )
                  )}
                </tbody>
              </Table>
            </div>
          </div>
        </div>
        <AlertModal
          show={modalData.show}
          title={modalData.title || ''}
          description={modalData.description || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />
      </div>
    );
  }
}

function EditableTimeGroup(props) {
  const {
    sNo = '',
    row,
    timeGroups,
    handleTimeChange,
    handleGenderChange,
    handleTimeGroupCancelClick,
    handleSaveClick,
    operation,
  } = props;
  const timeInterval = allowedTimeInterval();
  return (
    <tr className="tr-bottom-border tr-change">
      <td>
        <b className="pt-3">{sNo}</b>
      </td>
      {[
        { type: 'start_time', label: 'Start Time' },
        { type: 'end_time', label: 'End Time' },
      ].map((t) => (
        <td key={`${row.get('_id')}-${t.type}`}>
          <div>
            <DatePicker
              selected={timeGroups.getIn([row.get('_id'), t.type])}
              onChange={(date) => handleTimeChange(date, t.type, row, operation)}
              showTimeSelect
              showTimeSelectOnly
              timeIntervals={timeInterval}
              timeCaption={t.label}
              dateFormat="h:mm aa"
              className="form-control"
              placeholderText="Choose time"
            />
          </div>
        </td>
      ))}
      <td>
        <div className="mt--15">
          <Input
            elementType={'floatingselect'}
            elementConfig={{
              options: GENDER_DROPDOWN,
            }}
            value={timeGroups.getIn([row.get('_id'), 'gender'], 'male')}
            changed={(event) => handleGenderChange(event, 'gender', row, operation)}
          />
        </div>
      </td>
      <td colSpan="2"></td>
      <td>
        <b className="float-left pr-2 f-16 pt-1 remove-hover">
          <Tooltip title={<Trans i18nKey={'delete'}></Trans>}>
            <i
              className="fa fa-times-circle"
              aria-hidden="true"
              onClick={() => handleTimeGroupCancelClick(row, operation)}
            ></i>
          </Tooltip>
        </b>
        <b className="float-left pl-2 f-16 pt-1 remove-hover">
          <Tooltip title={<Trans i18nKey={'save'}></Trans>}>
            <i
              className="fa fa-floppy-o"
              aria-hidden="true"
              onClick={() => handleSaveClick(row.get('_id'), operation)}
            ></i>
          </Tooltip>
        </b>
      </td>
    </tr>
  );
}

TimeGroups.propTypes = {
  getTimeGroups: PropTypes.func,
  updateTimeGroup: PropTypes.func,
  timeGroups: PropTypes.instanceOf(Map),
};

EditableTimeGroup.propTypes = {
  sNo: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  row: PropTypes.instanceOf(Map),
  timeGroups: PropTypes.instanceOf(Map),
  handleTimeChange: PropTypes.func,
  handleGenderChange: PropTypes.func,
  handleTimeGroupCancelClick: PropTypes.func,
  handleSaveClick: PropTypes.func,
  operation: PropTypes.string,
};

export default TimeGroups;
