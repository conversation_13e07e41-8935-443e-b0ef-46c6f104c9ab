import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import { useHistory, with<PERSON>outer } from 'react-router-dom';
import { NavButton } from '../Styled';
import { selectProgramCalendarDashboard } from '_reduxapi/program_calendar/selectors';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { contentRename, removeURLParams } from 'utils';

const NavRowButtons = ({ theme, color, programCalendarDashboard }) => {
  const history = useHistory();
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlYear = params.get('year');
  const triggerFunction = (year) => {
    const location = history.location;
    const changedPath = location.pathname;
    const pathSearch = removeURLParams(location, ['year']) + `&year=${year}`;
    history.push(changedPath + pathSearch);
  };

  const yearArray = programCalendarDashboard.get('year_level', List());

  return (
    <Fragment>
      {yearArray &&
        yearArray.size > 0 &&
        yearArray.map((item, index) => {
          return (
            <NavButton
              key={index}
              className={urlYear === item.get('year', '') ? `${theme}` : null}
              onClick={() => {
                triggerFunction(item.get('year', ''));
              }}
              color={color}
            >
              {contentRename(item.get('year', ''), 'year', 'Year ')}
            </NavButton>
          );
        })}
    </Fragment>
  );
};

NavRowButtons.propTypes = {
  programCalendarDashboard: PropTypes.instanceOf(Map),
  theme: PropTypes.string,
  color: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    programCalendarDashboard: selectProgramCalendarDashboard(state),
  };
};

export default connect(mapStateToProps)(withRouter(NavRowButtons));
