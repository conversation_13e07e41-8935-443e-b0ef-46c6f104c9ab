import React, { useState, useEffect } from 'react';
import { Modal } from 'react-bootstrap';
import { Editor } from 'react-draft-wysiwyg';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import { EditorState } from 'draft-js';
import { t } from 'i18next';

import UploadMedia from './EditComponents/UploadMedia';
import MButton from 'Widgets/FormElements/material/Button';
import { EDITOR_TOOLBAR } from '../udUtil';

function EditAboutUniversityModal({
  show,
  handleClose,
  universityDescription,
  saveDescription,
  previousImg,
  title,
  setData,
  name,
  cancelShow,
}) {
  useEffect(() => {
    onEditorStateChange(universityDescription);
  }, [universityDescription]);
  const [editorState, onEditorStateChange] = useState('');
  const [media, setMediaFiles] = useState(previousImg);
  const disableSave = () => {
    if (!editorState?.getCurrentContent) return true;
    return !editorState?.getCurrentContent().getPlainText();
  };

  function editorStateLength() {
    if (!editorState?.getCurrentContent) return 0;
    return editorState?.getCurrentContent().getPlainText().length;
  }

  return (
    <Modal
      show={show}
      centered
      dialogClassName={`model-900  ${cancelShow ? 'display-none' : 'display-block'}`}
    >
      <Modal.Header className="border-none pb-0">
        <Modal.Title className="f-20">{title}</Modal.Title>
      </Modal.Header>

      <Modal.Body className="pt-0 model-border rounded mt-3 mx-3 mb-0">
        <UploadMedia media={media} setMediaFiles={setMediaFiles} name={name} setData={setData} />
        <div className="digi-editor-border rounded mt-3 min-height_200px">
          <Editor
            editorState={editorState}
            wrapperClassName="wrapperClassName"
            editorClassName="editorClassName"
            onEditorStateChange={onEditorStateChange}
            toolbar={EDITOR_TOOLBAR}
            placeholder={t(`add_colleges.Add_Description`)}
          />
        </div>
        <div className="text-right f-14">{editorStateLength()}/5000</div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton
          className="mr-3"
          color="inherit"
          variant="outlined"
          clicked={() => {
            if (
              universityDescription?.getCurrentContent().getPlainText() !==
                editorState?.getCurrentContent().getPlainText() ||
              previousImg !== media
            ) {
              handleClose('aboutUniversity', 'cancel');
            } else {
              handleClose();
            }
          }}
        >
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>

        <MButton
          clicked={() => {
            // saveDescription(editorState.getCurrentContent().getPlainText(), media);
            saveDescription(editorState.getCurrentContent(), media);
            //handleClose('aboutUniversity', 'save', '', { editorState });
          }}
          disabled={disableSave()}
        >
          <Trans i18nKey={'add_colleges.save'}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

EditAboutUniversityModal.propTypes = {
  show: PropTypes.bool,
  title: PropTypes.string,
  handleClose: PropTypes.func,
  setData: PropTypes.func,
  saveDescription: PropTypes.func,
  previousImg: PropTypes.string,
  name: PropTypes.string,
  universityDescription: PropTypes.instanceOf(EditorState),
  cancelShow: PropTypes.bool,
};

export default EditAboutUniversityModal;
