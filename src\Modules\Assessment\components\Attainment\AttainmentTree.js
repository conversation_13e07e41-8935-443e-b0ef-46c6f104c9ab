import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { List, Map } from 'immutable';
import * as actions from '_reduxapi/assessment/action';
import { selectAttainmentNodeTypes } from '_reduxapi/assessment/selector';
import { Tree, TreeNode } from 'react-organizational-chart';
import styled from 'styled-components';
import { Menu, MenuItem, IconButton } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import AttainmentNodeModal from '../../modals/AttainmentNode';
import DeleteModal from 'Containers/Modal/Delete';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { getURLParams, indVerRename } from 'utils';

const StyledNode = styled.div`
  border: 1px solid #e7e7e7;
  display: inline-flex;
  align-items: center;
  padding: 4px 4px 4px 10px;
  background: -moz-linear-gradient(top, #ffffff 0%, #fbfbfb 100%);
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #ffffff),
    color-stop(100%, #fbfbfb)
  );
  background: -webkit-linear-gradient(top, #ffffff 0%, #fbfbfb 100%);
  background: -o-linear-gradient(top, #ffffff 0%, #fbfbfb 100%);
  background: -ms-linear-gradient(top, #ffffff 0%, #fbfbfb 100%);
  background: linear-gradient(to bottom, #ffffff 0%, #fbfbfb 100%);
  line-height: 1.3em;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  position: relative;
  box-shadow: -5px 0px 0px #4ade80 !important;
  min-height: 42px;
`;
const StyledPercentageNode = styled.div`
  background: #dcfce7;
  text-align: center;
  justify-content: center;
  margin-left: 12px;
  border-radius: 3px;
  padding: 4px;
  font-size: 14px;
`;

function AttainmentTree({
  outcomeType,
  attainmentDetails,
  attainmentNodeTypes,
  getAttainmentNodeTypes,
  updateAttainmentNode,
  setData,
}) {
  const [showNodeModal, setShowNodeModal] = useState(false);
  const [deleteAttainmentNode, setDeleteAttainmentNode] = useState(false);
  const [selectedNode, setSelectedNode] = useState(Map());
  const selectedOutcome = attainmentDetails
    .get('evaluationPlan', List())
    .filter((item) => item.get('outComeType', '') === outcomeType);
  const type = getURLParams('type', true);
  const programId = getURLParams('pid', true);
  const courseId = getURLParams('courseId', true);
  const term = getURLParams('term', true);
  const levelNo = getURLParams('levelNo', true);
  const requestParams = { programId, courseId, levelNo, term };

  useEffect(() => {
    getAttainmentNodeTypes(type);
  }, []); //eslint-disable-line

  const handleClickOpen = (data, deleteMode = false) => {
    if (deleteMode && !data.get('typeId', '')) return; //prevent outcome delete
    setSelectedNode(Map(data));
    if (!deleteMode) handleNodeModal();
    else setDeleteAttainmentNode(true);
  };

  const handleNodeModal = () => setShowNodeModal(!showNodeModal);

  const handleSave = (formData) => {
    const totalPercent =
      selectedNode
        .get('levelsList', List())
        .reduce((total, el) => total + el.get('weightage', 0), 0) + formData.weightage;
    if (totalPercent > 100) {
      setData(Map({ message: 'Maximum allowed is 100%' }));
      return false;
    }

    updateAttainmentNode({
      mode: selectedNode.get('type', 'Add').toLowerCase(),
      data: {
        _id: attainmentDetails.get('_id', ''),
        masterId: selectedNode.get('masterId', ''),
        type: type,
        ...formData,
      },
      callBack: handleNodeModal,
      type: type,
      requestParams: requestParams,
    });
  };

  const handleDeleteAttainmentNode = () => {
    updateAttainmentNode({
      mode: 'delete',
      data: {
        _id: attainmentDetails.get('_id', ''),
        masterId: selectedNode.get('_id', ''),
      },
      callBack: () => setDeleteAttainmentNode(false),
      type: type,
      requestParams: requestParams,
    });
  };

  const MenuOptions = ({ data, parentType, subTypes, parentLevel, handleAddNode, setOpen }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const handleMenuClose = () => setAnchorEl(null);
    const handleClickEdit = () => {
      handleClickOpen({
        type: 'Edit',
        masterId: data.get('_id'),
        nodeType: data.get('typeId', ''),
        nodeName: data.get('nodeName', ''),
        nodePercent: data.get('weightage', ''),
        levelsList: parentLevel.filter((item) => item.get('_id', '') !== data.get('_id', '')),
        parentType,
        subTypes,
      });
    };

    return (
      <>
        <IconButton
          aria-label="more"
          aria-controls="long-menu"
          aria-haspopup="true"
          onClick={(e) => setAnchorEl(e.currentTarget)}
          onMouseOver={() => setOpen(false)}
          className="ml-1 p-1"
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: 48 * 4.5,
              width: '20ch',
            },
          }}
        >
          {CheckPermission(
            'subTabs',
            'Attainment Calculator',
            'Attainment Setting',
            '',
            'Regulations',
            '',
            'Evaluation Plan',
            'Edit'
          ) && (
            <> {data.get('typeId', '') && <MenuItem onClick={handleClickEdit}>Edit</MenuItem>}</>
          )}
          {CheckPermission(
            'subTabs',
            'Attainment Calculator',
            'Attainment Setting',
            '',
            'Regulations',
            '',
            'Evaluation Plan',
            'Delete'
          ) && <MenuItem onClick={() => handleClickOpen(data, true)}>Delete</MenuItem>}

          {CheckPermission(
            'subTabs',
            'Attainment Calculator',
            'Attainment Setting',
            '',
            'Regulations',
            '',
            'Evaluation Plan',
            'Add'
          ) && (
            <>
              {!data.get('nodeName', '') && (
                <MenuItem onClick={handleAddNode}>Add Sub Type</MenuItem>
              )}
            </>
          )}
        </Menu>
      </>
    );
  };
  MenuOptions.propTypes = {
    data: PropTypes.instanceOf(Map),
    parentType: PropTypes.string,
    subTypes: PropTypes.instanceOf(List),
    parentLevel: PropTypes.instanceOf(List),
    handleAddNode: PropTypes.func,
    setOpen: PropTypes.func,
  };

  const NodeTooltip = ({ data }) => {
    return (
      <div>
        <p className="treenode-tooltip-title mb-0 py-1 px-2">{data.get('typeName', '')}</p>
        <div className="treenode-tooltip-content d-flex py-2">
          <div className="px-2 mr-1">
            <p className="mb-1">Node Name</p>
            <p className="mb-0">{data.get('nodeName', '')}</p>
          </div>
          <div className="px-2">
            <p className="mb-1">Node Weightage</p>
            <p className="mb-0">{data.get('weightage', '')}%</p>
          </div>
        </div>
      </div>
    );
  };
  NodeTooltip.propTypes = {
    data: PropTypes.instanceOf(Map),
  };

  const NodeContent = ({
    label,
    data = Map(),
    parentType = '',
    parentLevel = List(),
    subLevel = '',
  }) => {
    const text = label || data.get('nodeName', '') || data.get('typeName', '');
    const percent = data.get('weightage', '');
    const levelsList = data.get(subLevel, List());
    const [open, setOpen] = useState(false);
    const handleAddNode = () => {
      handleClickOpen({
        masterId: data.get('_id'),
        levelsList,
        parentType: data.get('typeId', ''),
      });
    };
    const nodeDetails = () => {
      return (
        <StyledNode>
          <div className="d-flex align-items-center" onMouseOver={() => setOpen(true)}>
            <div className="f-14 text-capitalize pt-1">{text}</div>
            {percent && (
              <div>
                <StyledPercentageNode>{percent}%</StyledPercentageNode>
              </div>
            )}
          </div>
          <MenuOptions
            data={data}
            parentType={parentType}
            subTypes={levelsList}
            parentLevel={parentLevel}
            handleAddNode={handleAddNode}
            setOpen={setOpen}
          />
        </StyledNode>
      );
    };
    return (
      <div>
        {data.get('nodeName', '') !== '' ? (
          <Tooltips
            className="treenode-tooltip"
            title={<NodeTooltip data={data} />}
            placement="bottom"
            {...(!open && { propForOpen: { open } })}
          >
            {nodeDetails()}
          </Tooltips>
        ) : (
          <>{nodeDetails()}</>
        )}
        {CheckPermission(
          'subTabs',
          'Attainment Calculator',
          'Attainment Setting',
          '',
          'Regulations',
          '',
          'Evaluation Plan',
          'Add'
        ) && (
          <>
            {!data.get('nodeName', '') && (
              <div className="text-blue mt-1">
                <AddCircleOutlineIcon
                  color="inherit"
                  className="cursor-pointer"
                  onClick={handleAddNode}
                />
              </div>
            )}
          </>
        )}
      </div>
    );
  };
  NodeContent.propTypes = {
    label: PropTypes.string,
    data: PropTypes.instanceOf(Map),
    parentType: PropTypes.string,
    parentLevel: PropTypes.instanceOf(List),
    subLevel: PropTypes.string,
  };

  return (
    <div className="mt-3 mb-5">
      <Tree
        lineWidth={'2px'}
        lineColor={'#D1D5DB'}
        lineBorderRadius={'10px'}
        label={
          <NodeContent
            label={`${indVerRename(outcomeType.toUpperCase(), programId)} - Course Outcome`}
            data={selectedOutcome.get(0, Map())}
            subLevel="tree"
          />
        }
      >
        {selectedOutcome.getIn([0, 'tree'], List()).map((one, oneIndex) => (
          <TreeNode
            key={oneIndex}
            label={
              <NodeContent
                data={one}
                parentLevel={selectedOutcome.getIn([0, 'tree'], List())}
                subLevel="subTree"
              />
            }
          >
            {one.get('subTree', List()).map((two, twoIndex) => (
              <TreeNode
                key={twoIndex}
                label={
                  <NodeContent
                    data={two}
                    parentType={one.get('typeId', '')}
                    parentLevel={one.get('subTree', List())}
                    subLevel="node"
                  />
                }
              >
                {two.get('node', List()).map((three, threeIndex) => (
                  <TreeNode
                    key={threeIndex}
                    label={
                      <NodeContent
                        data={three}
                        parentType={two.get('typeId', '')}
                        parentLevel={two.get('node', List())}
                      />
                    }
                  />
                ))}
              </TreeNode>
            ))}
          </TreeNode>
        ))}
      </Tree>

      {showNodeModal && (
        <AttainmentNodeModal
          show={showNodeModal}
          onClose={handleNodeModal}
          handleSave={handleSave}
          attainmentNodeTypes={attainmentNodeTypes}
          data={selectedNode}
        />
      )}

      {deleteAttainmentNode && (
        <DeleteModal
          show={deleteAttainmentNode}
          setShow={() => setDeleteAttainmentNode(false)}
          title={'node'}
          description={'delete_node_desc'}
          deleteName={selectedNode.get('nodeName', '') || selectedNode.get('typeName', '')}
          deleteSelected={handleDeleteAttainmentNode}
        />
      )}
    </div>
  );
}

AttainmentTree.propTypes = {
  outcomeType: PropTypes.string,
  attainmentDetails: PropTypes.instanceOf(Map),
  attainmentNodeTypes: PropTypes.instanceOf(List),
  getAttainmentNodeTypes: PropTypes.func,
  updateAttainmentNode: PropTypes.func,
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    attainmentNodeTypes: selectAttainmentNodeTypes(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(AttainmentTree);
