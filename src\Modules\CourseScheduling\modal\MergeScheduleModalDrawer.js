import React from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map, Set } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { ThemeProvider } from '@mui/styles';
import DatePicker from 'react-datepicker';
import {
  startOfDay,
  format,
  isValid,
  endOfDay,
  set,
  //getDay,
  isWithinInterval,
  getHours,
  getMinutes,
  subMilliseconds,
} from 'date-fns';

import {
  MUI_THEME,
  MUI_CHECKBOX_THEME,
  capitalize,
  getModes,
  isIndGroup,
  allowedTimeInterval,
  getManualStaffOption,
  isManualAttendanceEnabled,
  studentGroupRename,
  // isModuleEnabled,
} from '../../../utils';
import {
  getFormattedGroupName,
  getHour,
  transformDateToCustomObject,
  getRotationDates,
} from '../components/utils';
import { Drawer } from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import ScheduleTabs from '../components/Schedule/ScheduleTabs';
import { selectMergeScheduledClassLeader } from '_reduxapi/course_scheduling/selectors';
import { formattedFullName } from 'Modules/ReportsAndAnalytics/utils';
const menuProps = {
  getContentAnchorEl: null,
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'center',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'center',
  },
  variant: 'menu',
};
function MergeScheduleModalDrawer({
  show,
  onHide,
  onSave,
  onChange,
  mode,
  data,
  course,
  setData,
  sessionFlowList,
  scheduleList,
  excludedTimes,
  handleDetachSchedule,
  isRotation,
  programId,
  setMergeModalData,
  courseData,
  institutionCalendarId,
  scheduleId,
  courseId,
  checkUpdateExternal,
}) {
  const subjects = getSubjects();
  const staffs = getStaffs();
  const GetselectMergeScheduledClassLeader = useSelector(selectMergeScheduledClassLeader);
  const infrastructures = getInfrastructures();
  const errors = getErrors();
  const classLeaderData = getClassLeader();
  const handleChanges = handleChange;

  const manualStaffOptions = useSelector((state) =>
    state.courseScheduling.getIn(['staffScheduleOptionList', 0], Map())
  );

  function getErrors() {
    return data.get('errors', List());
  }
  const checkclassLeaderData = data.getIn(['schedule', 'checkclassLeaderData'], List());
  const externalstaff = data.getIn(['schedule', 'checkExternalList'], List());
  function handleChange(name, value, staffList) {
    switch (name) {
      case 'schedule_date': {
        if (!value) value = '';
        onChange(name, value ? format(startOfDay(value), 'yyyy-MM-dd') : '');
        break;
      }
      case 'start':
      case 'end': {
        if (!value) {
          onChange(name, Map());
          return;
        }
        const time = transformDateToCustomObject(value);
        if (time.get('minute', NaN) % allowedTimeInterval() !== 0) return;
        onChange(name, time);
        break;
      }
      case 'subjects': {
        onChange('staffs', List());
        const selected = getAllSubjects().filter((subject) =>
          value.includes(subject.get('_subject_id'))
        );
        onChange(name, selected);
        break;
      }
      case 'staffs': {
        const selected = staffs
          .filter((staff) => value.includes(staff.get('_id')))
          .map((staff) => staff.delete('name').delete('value'));
        onChange(name, selected);
        break;
      }
      case 'checkStaff': {
        if (value) {
          onChange(name, checkStaff.push(staffList));
        } else {
          const filteredStaffs = checkStaff.filter((staff) => {
            return !(staff.get('_staff_id', '') === staffList.get('_staff_id', ''));
          });
          onChange(name, filteredStaffs);
        }
        break;
      }
      case 'checkclassLeaderData': {
        if (value) {
          onChange(name, checkclassLeaderData.push(staffList));
        } else {
          const filteredStaffs = checkclassLeaderData.filter((classLeader) => {
            return classLeader.get('_staff_id', '') !== staffList.get('_staff_id', '');
          });
          onChange(name, filteredStaffs);
        }
        break;
      }
      case 'mode': {
        onChange(name, value);
        break;
      }
      case 'manualStaffs': {
        onChange(name, value);
        break;
      }
      case 'mobileNumberOrEmail': {
        onChange(name, value);
        break;
      }
      default:
        onChange(name, value);
        break;
    }
  }

  function getScheduleDate() {
    const scheduleDate = data.getIn(['schedule', 'schedule_date'], null) || null;
    if (!scheduleDate) return null;
    return startOfDay(new Date(scheduleDate));
  }

  function getStartEndDate(type) {
    const date = data.getIn(['schedule', type], Map());
    if (date.isEmpty()) return null;
    return set(startOfDay(new Date()), { hours: getHour(date), minutes: date.get('minute') });
  }

  function getMinMaxDate() {
    let startDate = new Date(course.get('start_date'));
    let endDate = new Date(course.get('end_date'));
    if (isRotation) {
      const rotationDates = getRotationDates(course);
      const groupNo = scheduleList.getIn([0, 'rotation_count']);
      const rotationDate = rotationDates.find(
        (rotation) => rotation.get('rotation_count') === groupNo
      );
      if (!rotationDate) return {};
      startDate = rotationDate.get('start_date');
      endDate = rotationDate.get('end_date');
    }
    if (!isValid(startDate) || !isValid(endDate)) return {};
    endDate = endOfDay(endDate);
    return { minDate: startDate, maxDate: endDate };
  }

  function getAllSubjects() {
    const subjectIdList = [];
    return sessionFlowList
      .reduce((sAcc, s) => sAcc.concat(s.get('subjects', List())), List())
      .filter((s) => {
        const subjectId = s.get('_subject_id');
        if (subjectIdList.includes(subjectId)) return false;
        subjectIdList.push(subjectId);
        return true;
      });
  }

  function getSubjects() {
    return getAllSubjects().map((subject) =>
      Map({
        name: subject.get('subject_name'),
        value: subject.get('_subject_id'),
      })
    );
  }

  function getStaffs() {
    const selectedSubjects = data.getIn(['schedule', 'subjects'], List());
    if (selectedSubjects.isEmpty()) return List();
    return getAllSubjects()
      .reduce((acc, subject) => {
        return acc.concat(
          subject.get('staffs', List()).map((staff) =>
            staff.merge(
              Map({
                name: `${staff.getIn(['name', 'first'], '')} ${staff.getIn(
                  ['name', 'middle'],
                  ''
                )} ${staff.getIn(['name', 'last'], '')}`,
                value: staff.get('_id'),
                staff_name: staff.get('name'),
                _staff_id: staff.get('_id'),
              })
            )
          )
        );
      }, List())
      .reduce((acc, staff) => acc.set(staff.get('value'), staff), Map())
      .valueSeq()
      .toList();
  }

  function getClassLeader() {
    if (data.getIn(['schedule', 'outsideCampus'], false) === true) {
      if (!GetselectMergeScheduledClassLeader) {
        return List();
      }
      const classdata = GetselectMergeScheduledClassLeader.map((item) => {
        return Map({
          name: formattedFullName(item.get('name', Map()).toJS()).replace(/\s+/g, ' ').trim(),
          value: item.get('_id'),
          staff_name: item.get('name'),
          _staff_id: item.get('_id'),
        });
      });
      return classdata;
    } else {
      return [];
    }
  }

  function getInfrastructures() {
    const mode = data.getIn(['schedule', 'mode'], '');
    if (!mode) return List();
    const genders = Set(
      scheduleList
        .reduce((sAcc, s) => sAcc.concat(s.get('student_groups', List())), List())
        .map((studentGroup) => studentGroup.get('gender'))
        .filter((g) => g)
    ).toList();
    if (genders.isEmpty()) return List();
    const includeAll = genders.size !== 1;
    const gender = genders.get(0, '').toLowerCase();
    if (mode === 'remote') {
      return course
        .get('remote_scheduling', List())
        .filter((room) => {
          if (
            // room.get('yearName') === course.get('year_no') &&
            room.get('term', '').toLowerCase() === course.get('term', '').toLowerCase() &&
            room.get('levelName') === course.get('level_no')
          ) {
            return (
              room.get('gender', '').toLowerCase() === gender || room.get('gender', '') === 'both'
            );
          }
          return false;
        })
        .map((room) =>
          room.merge(
            Map({
              name: room.get('meetingTitle'),
              value: room.get('_id'),
              secondaryTitle1: `${capitalize(room.get('gender', ''))}${
                room.get('meetingUsername', '') !== null
                  ? `, ${room.get('meetingUsername', '')}`
                  : ''
              }`,
              secondaryTitle2: `${
                room.get('remotePlatform', '') === 'teams'
                  ? 'Microsoft Teams'
                  : `${room.get('meetingId', '')}, Zoom`
              }`,
            })
          )
        )
        .sort((r1, r2) => {
          const v1 = r1.get('meetingTitle', '');
          const v2 = r2.get('meetingTitle', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    } else if (mode === 'onsite') {
      const subjects = data.getIn(['schedule', 'subjects'], List());
      if (subjects.isEmpty()) return List();

      const groupedSubjects = sessionFlowList
        .map((session) => session.get('subjects', List()))
        .reduce((acc, subject) => {
          return acc.concat(subject.map((sub) => sub.get('subject_infra', List())));
        }, List())
        .reduce((acc1, sub) => {
          return acc1.concat(sub);
        }, List());
      let infrastructures = groupedSubjects;
      // let infrastructures = subjects.reduce(
      //   (acc, subject) => acc.concat(subject.get('subject_infra', List())),
      //   List()
      // );
      if (!includeAll) {
        infrastructures = infrastructures.filter((infra) => {
          const gendersInTimeGroups = Set(
            infra
              .get('timing', List())
              .map((t) => {
                const g = t.getIn(['_time_id', 'gender']);
                if (!g) return '';
                return g.toLowerCase();
              })
              .filter((g) => g)
          ).toList();
          if (gendersInTimeGroups.isEmpty()) return false;
          return gendersInTimeGroups.includes('both') ? true : gendersInTimeGroups.includes(gender);
        });
      }
      const startDate = getStartEndDate('start');
      let endDate = getStartEndDate('end');
      return infrastructures
        .filter((infra) => {
          let incInfra = false;
          infra.get('timing', List()).forEach((t) => {
            if (incInfra) return;
            let infraStartDate = new Date(t.getIn(['_time_id', 'start_time']));
            let infraEndDate = new Date(t.getIn(['_time_id', 'end_time']));
            if (!isValid(startDate) || !isValid(endDate)) return;
            if (!isValid(infraStartDate) || !isValid(infraEndDate)) return;
            endDate = subMilliseconds(endDate, 1);
            infraStartDate = set(startDate, {
              hours: getHours(infraStartDate),
              minutes: getMinutes(infraStartDate),
              seconds: 0,
              milliseconds: 0,
            });
            infraEndDate = subMilliseconds(
              set(endDate, {
                hours: getHours(infraEndDate),
                minutes: getMinutes(infraEndDate),
                seconds: 0,
                milliseconds: 0,
              }),
              1
            );
            incInfra =
              infraStartDate > infraEndDate
                ? true
                : isWithinInterval(startDate, { start: infraStartDate, end: infraEndDate }) &&
                  isWithinInterval(endDate, { start: infraStartDate, end: infraEndDate });
          });
          return incInfra;
        })
        .map((infra) =>
          infra.merge(
            Map({
              value: infra.get('_id'),
              secondaryTitle1: `${infra.get('floor_no', '')}, ${
                infra.get('zone', List()).join(', ') || 'NA'
              }, ${infra.get('room_no', '')}`,
              secondaryTitle2: `${infra.get('building_name', '')}`,
              outsideCampus: infra.get('outsideCampus', false),
            })
          )
        )
        .reduce((acc, infra) => acc.set(infra.get('_id'), infra), Map())
        .valueSeq()
        .toList()
        .sort((i1, i2) => {
          const v1 = i1.get('name', '');
          const v2 = i2.get('name', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    }
    return List();
  }

  function getSelectedSubjects() {
    return data
      .getIn(['schedule', 'subjects'], List())
      .map((subject) => subject.get('_subject_id'))
      .toJS();
  }

  function getSelectedStaffs() {
    return data
      .getIn(['schedule', 'staffs'], List())
      .map((subject) => subject.get('_id'))
      .toJS();
  }

  function groupTopics() {
    return scheduleList
      .map(
        (schedule) =>
          `${schedule.getIn(['session', 'delivery_symbol'], '')}${schedule.getIn(
            ['session', 'delivery_no'],
            ''
          )} ${schedule.getIn(['session', 'session_topic'], '')}`
      )
      .join(', ');
  }

  function getSelectedInfrastructure() {
    return data.getIn(['schedule', '_infra_id'], '');
  }

  const selectedSubjects = getSelectedSubjects();
  const selectedStaffs = getSelectedStaffs();
  const transformedErrors = errors.reduce(
    (acc, err) => acc.set(err.get('field', ''), err.get('message', '')),
    Map()
  );

  const drawerPaperW40 = {
    '& .MuiDrawer-paper': { width: '40em' },
  };
  const checkStaff = data.getIn(['schedule', 'checkStaff'], List());
  // const infra = infrastructures.find((infra) => infra.get('value')) || Map();
  const handleManagedAttendanceChanges = (e) => {
    setMergeModalData((pre) => pre.setIn(['schedule', 'selfAttendance'], e.target.checked));
  };
  return (
    <div>
      <Drawer sx={drawerPaperW40} anchor="right" open={show}>
        <div className="border-bottom mb-2 Top_sticky_drawer px-3 pt-3">
          <div className="d-flex">
            <div className="">
              <p className="mb-2">Merge Session</p>
              <p className="mb-2">{groupTopics()}</p>
              <p className="mb-2">
                {scheduleList.reduce((sAcc, s, index) => {
                  const session =
                    sessionFlowList.find(
                      (session) => session.get('_id') === s.get('_session_id')
                    ) || Map();
                  return sAcc.push(
                    <span key={s.get('_session_id')}>
                      <b>{`${session.get('delivery_symbol', '')}${session.get(
                        'delivery_no',
                        ''
                      )}`}</b>{' '}
                      {s
                        .get('student_groups', List())
                        .map((studentGroup) => {
                          const studentGroupName = getFormattedGroupName(
                            studentGroupRename(studentGroup.get('group_name', ''), programId),
                            isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                          );
                          const sessionGroupNames = studentGroup
                            .get('session_group', List())
                            .map((sessionGroup) =>
                              getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                            )
                            .join(', ');
                          return `${studentGroupName} - ${sessionGroupNames}`;
                        })
                        .join(', ')}
                      {index !== scheduleList.size - 1 && ' • '}
                    </span>
                  );
                }, List())}
              </p>
            </div>
            <div className="d-flex ml-auto">
              <div>
                <ThemeProvider theme={MUI_THEME}>
                  <div className="d-flex justify-content-end ml-auto mb-2">
                    <div className="mr-2">
                      <Button variant="outlined" color="primary" onClick={onHide}>
                        CANCEL
                      </Button>
                    </div>
                    <div>
                      <Button variant="contained" color="primary" onClick={() => onSave(mode)}>
                        SCHEDULE
                      </Button>
                    </div>
                  </div>
                  {mode === 'update' && (
                    <div className="d-flex justify-content-end ml-auto mb-2">
                      <Button
                        variant="outlined"
                        color="primary"
                        onClick={() => handleDetachSchedule(false)}
                      >
                        DETACH SESSION
                      </Button>
                    </div>
                  )}
                </ThemeProvider>
              </div>
            </div>
          </div>
        </div>
        <div style={{ overflow: 'auto', height: '100vh' }}>
          <div className="row mx-1">
            <div className="col-md-6">
              <div className="pt-2 pb-2">
                <div>
                  <div className="f-16">Date *</div>
                </div>
                <div>
                  <div className="schedule-date-picker-container">
                    <DatePicker
                      onChange={(date) => handleChange('schedule_date', date)}
                      selected={getScheduleDate()}
                      className="schedule-date-picker-input"
                      dateFormat="dd/MM/yyyy"
                      // filterDate={(date) => {
                      //   const day = getDay(date);
                      //   return day;
                      //   //return ![5, 6].includes(day);
                      // }}
                      {...getMinMaxDate()}
                    />
                  </div>
                </div>
              </div>

              <div className="pt-2 pb-2">
                <div>
                  <div className="f-16">Subject *</div>
                </div>
                <div>
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      multiple
                      value={selectedSubjects}
                      onChange={(e) => handleChange('subjects', e.target.value)}
                      MenuProps={{
                        ...menuProps,
                        PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                      }}
                      renderValue={(selected) =>
                        subjects
                          .reduce((acc, group) => {
                            if (!selected.includes(group.get('value'))) return acc;
                            return acc.push(group.get('name'));
                          }, List())
                          .join(', ')
                      }
                    >
                      {subjects.map((option) => (
                        <MenuItem
                          className="white-space-normal"
                          key={option.get('value')}
                          value={option.get('value')}
                        >
                          <ListItemIcon>
                            <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                              <Checkbox
                                color="primary"
                                checked={selectedSubjects.indexOf(option.get('value')) > -1}
                                size="small"
                              />
                            </ThemeProvider>
                          </ListItemIcon>
                          <ListItemText primary={option.get('name')} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>

              {isManualAttendanceEnabled() &&
                data.getIn(['schedule', 'mode'], '') !== 'remote' &&
                manualStaffOptions.get('manualAttendance', false) &&
                manualStaffOptions.get('assignedStaffIds', List()).size > 0 && (
                  <div className="pt-2 pb-2">
                    <div className="">
                      <div className="f-16">Attendance taking Staff</div>
                    </div>
                    <div className="">
                      <MaterialInput
                        elementType={'materialSelectMultiple'}
                        disabled={mode === 'view'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{
                          options: getManualStaffOption(
                            manualStaffOptions.get('assignedStaffIds', List())
                          ).toJS(),
                        }}
                        label={''}
                        labelclass={'mb-0 f-14'}
                        changed={(e) => handleChange('manualStaffs', e.target.value)}
                        isAllSelected={false}
                        value={data.getIn(['schedule', 'manualStaffs'], List()).toJS()}
                        multiple={true}
                      />
                    </div>
                  </div>
                )}
              {/* {isModuleEnabled('OUTSIDE_CAMPUS') && (
                <Fragment>
                  {data.getIn(['schedule', 'outsideCampus'], false) ? (
                    <div className="row align-items-center pt-2 pb-2">
                      <div className="col-md-5">
                        <div className="f-16">
                          <MaterialInput
                            elementType={'materialSelect'}
                            type={'text'}
                            variant={'outlined'}
                            size={'small'}
                            sx={withoutOutlineSelect}
                            elementConfig={{ options: programType }}
                            changed={(e) => handleChange('selectNumOrEmail', e.target.value)}
                            value={data.getIn(['schedule', 'selectNumOrEmail'], '')}
                          />
                        </div>
                      </div>
                      <div className="col-md-7">
                        <MaterialInput
                          elementType={'materialInput'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          changed={(e) => handleChange('mobileNumberOrEmail', e.target.value)}
                          value={data.getIn(['schedule', 'mobileNumberOrEmail'], '')}
                        />
                      </div>
                    </div>
                  ) : (
                    ''
                  )}
                </Fragment>
              )} */}
            </div>

            <div className="col-md-6">
              <div className="pt-2 pb-2">
                <div className="">
                  <div className="f-16">Time *</div>
                </div>
                <div className="">
                  <div className="row">
                    <div className="col-md-6 pr-1">
                      <div className="schedule-date-picker-container">
                        <DatePicker
                          onChange={(date) => handleChange('start', date)}
                          selected={getStartEndDate('start')}
                          className="schedule-date-picker-input"
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={allowedTimeInterval()}
                          timeCaption="Start time"
                          dateFormat="h:mm aa"
                          excludeTimes={excludedTimes}
                        />
                      </div>
                    </div>

                    <div className="col-md-6 pl-1">
                      <div className="schedule-date-picker-container">
                        <DatePicker
                          onChange={(date) => handleChange('end', date)}
                          selected={getStartEndDate('end')}
                          className="schedule-date-picker-input"
                          showTimeSelect
                          showTimeSelectOnly
                          timeIntervals={allowedTimeInterval()}
                          timeCaption="End time"
                          dateFormat="h:mm aa"
                          excludeTimes={excludedTimes}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-2 pb-2">
                <div>
                  <div className="f-16">Mode *</div>
                </div>
                <div>
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      native
                      value={data.getIn(['schedule', 'mode'], '')}
                      onChange={(e) => handleChange('mode', e.target.value)}
                    >
                      <option value=""></option>
                      {getModes(programId).map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.name}
                        </option>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>
            </div>
          </div>
          <div className="row mx-1">
            <div
              className={`${
                data.getIn(['schedule', 'outsideCampus'], false) === true ? ' col-12' : 'col-6'
              }`}
            >
              <div className="pt-2 pb-2">
                <div>
                  <div className="f-16">
                    Infrastructure {data.getIn(['schedule', 'mode'], '') === 'remote' && '*'}
                  </div>
                </div>
                <div>
                  <FormControl
                    fullWidth
                    variant="outlined"
                    size="small"
                    error={transformedErrors.get('infra', '').length !== 0}
                  >
                    <Select
                      value={infrastructures.isEmpty() ? '' : getSelectedInfrastructure()}
                      onChange={(e) => {}}
                      MenuProps={{
                        ...menuProps,
                        PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                      }}
                      displayEmpty
                      renderValue={(value) => {
                        const infra =
                          infrastructures.find((infra) => infra.get('value') === value) || Map();
                        return infra.get('name', '');
                      }}
                    >
                      {!infrastructures.isEmpty() && (
                        <MenuItem value="" onClick={() => handleChange('infra', Map())}>
                          <ListItemText primary="Unselect" />
                        </MenuItem>
                      )}
                      {infrastructures.map((option) => (
                        <MenuItem
                          key={option.get('value')}
                          value={option.get('value')}
                          className="white-space-normal"
                          onClick={() => handleChange('infra', option)}
                        >
                          <ListItemText
                            primary={option.get('name')}
                            secondary={
                              <span className="d-block">
                                <span className="d-block">{option.get('secondaryTitle1', '')}</span>
                                <span className="d-block">{option.get('secondaryTitle2', '')}</span>
                              </span>
                            }
                          />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>
            </div>
            {!data.getIn(['schedule', 'outsideCampus'], false) ? (
              <div className="col-6">
                <div className=" pt-2 pb-2">
                  <div>
                    <div className="f-16">Staff *</div>
                  </div>
                  <div>
                    <FormControl
                      fullWidth
                      variant="outlined"
                      size="small"
                      error={transformedErrors.get('staff', '').length !== 0}
                    >
                      <Select
                        multiple
                        value={selectedStaffs}
                        onChange={(e) => handleChange('staffs', e.target.value)}
                        MenuProps={{
                          ...menuProps,
                          PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                        }}
                        renderValue={(selected) =>
                          staffs
                            .reduce((acc, group) => {
                              if (!selected.includes(group.get('value'))) return acc;
                              return acc.push(group.get('name'));
                            }, List())
                            .join(', ')
                        }
                      >
                        {staffs
                          .sort((a, b) => (a.get('name', '') > b.get('name', '') ? 1 : -1))
                          .map((option) => (
                            <MenuItem
                              className="white-space-normal"
                              key={option.get('value')}
                              value={option.get('value')}
                            >
                              <ListItemIcon>
                                <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                                  <Checkbox
                                    color="primary"
                                    checked={selectedStaffs.indexOf(option.get('value')) > -1}
                                    size="small"
                                  />
                                </ThemeProvider>
                              </ListItemIcon>
                              <ListItemText primary={option.get('name')} />
                            </MenuItem>
                          ))}
                      </Select>
                    </FormControl>
                  </div>
                </div>
              </div>
            ) : (
              ''
            )}
          </div>
          {!errors.isEmpty() && (
            <div className="color-red f-14 mt-2 mb-2">
              <div>NOTE:-</div>
              {errors.map((err, i) => (
                <div key={`${err.get('field')}-${i}`}>{`- ${studentGroupRename(
                  err.get('message', ''),
                  programId
                )}`}</div>
              ))}
            </div>
          )}
          {data.getIn(['schedule', 'outsideCampus'], false) && (
            <ScheduleTabs
              staffs={staffs}
              data={data}
              infrastructures={infrastructures}
              handleChanges={handleChanges}
              setModalData={setMergeModalData}
              courseData={courseData}
              programId={programId}
              institutionCalendarId={institutionCalendarId}
              courseId={courseId}
              classLeaderData={classLeaderData}
              GetPostClassLeader={GetselectMergeScheduledClassLeader}
              scheduleId={scheduleId}
              checkUpdateExternal={checkUpdateExternal}
              externalstaff={externalstaff}
              type={'MergeType'}
              mode={mode}
              setData={setData}
            />
          )}
        </div>

        <div className="Bottom_sticky_drawer d-flex mt-auto ">
          <div className="w-100">
            {infrastructures.size !== 0 ? (
              <>
                {data.getIn(['schedule', 'outsideCampus'], false) ? (
                  <>
                    <div className="d-flex align-items-center my-2 py-2 border-bottom mx-3">
                      <Checkbox
                        size="small"
                        className="p-0"
                        checked={data.getIn(['schedule', 'selfAttendance'], false)}
                        onChange={(e) => handleManagedAttendanceChanges(e)}
                      />
                      <div className="f-15 ml-2">Self Managed Attendance</div>
                    </div>
                  </>
                ) : (
                  ''
                )}
              </>
            ) : (
              ''
            )}
            {/* <div
              className="d-flex justify-content-center top-sticky-Search"
              style={{ backgroundColor: '#eeffff' }}
            >
              <img src={PlanningSchedule} alt="PlanningSchedule" className="PlanningSchedule_H_W" />
            </div> */}
          </div>
        </div>
      </Drawer>
    </div>
  );
}

MergeScheduleModalDrawer.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  onSave: PropTypes.func,
  onChange: PropTypes.func,
  mode: PropTypes.string,
  data: PropTypes.instanceOf(Map),
  course: PropTypes.instanceOf(Map),
  activeSessionFlow: PropTypes.instanceOf(Map),
  excludedTimes: PropTypes.array,
  sessionFlowList: PropTypes.instanceOf(List),
  scheduleList: PropTypes.instanceOf(List),
  handleDetachSchedule: PropTypes.func,
  isRotation: PropTypes.bool,
  programId: PropTypes.string,
  setMergeModalData: PropTypes.func,
  courseData: PropTypes.instanceOf(Map),
  institutionCalendarId: PropTypes.string,
  scheduleId: PropTypes.string,
  courseId: PropTypes.string,
  checkUpdateExternal: PropTypes.instanceOf(List),
};

export default MergeScheduleModalDrawer;
