import React, { Suspense, useState } from 'react';
import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';

import { getSiteUrl } from 'utils';
import HebalogoGif from 'Assets/Heba/heba-gif-logo.gif';
import HEBA_LOGO from 'Assets/q360_dashboard/bot_logo.svg';
import CloseBlue from 'Assets/Heba/close-blue-bg.svg';
import './style.css';
// import useUnifyServiceHook from 'Hooks/useUnifyServiceHook';

const Heba = ({ accessUrl = '', activeTab = '', imageWithBackground = false }) => {
  const [open, setOpen] = useState(false);
  // const { unifyData } = useUnifyServiceHook(); //eslint-disable-line
  const handleToggleDrawer = () => {
    setOpen(!open);
  };

  Heba.propTypes = {
    accessUrl: PropTypes.string,
    imageWithBackground: PropTypes.bool,
  };

  function removeTrailingSlash(url) {
    if (url.endsWith('/')) {
      return url.slice(0, -1);
    }
    return url;
  }

  const siteURL = getSiteUrl();
  const iframeURL = `${removeTrailingSlash(siteURL.DC_URL)}/chat/index1.html${accessUrl}${
    activeTab !== '' ? `&activeTab=${activeTab}` : ``
  }`;
  const DrawerList = (
    <Box role="presentation" onClick={handleToggleDrawer} style={{ display: 'flex' }}>
      <IconButton className="digi-heba-close">
        <img src={CloseBlue} alt="Close" className="digi-cursor-pointer digi-close-icon-size" />
      </IconButton>
      <iframe
        style={{
          display: open ? 'block' : 'none',
          height: '98vh',
          width: '99%',
          right: '0',
          transition: 'all 0.5s ease',
        }}
        src={iframeURL}
        allow="clipboard-write"
        title="Embedded HTML"
      ></iframe>
    </Box>
  );

  return (
    <React.Fragment>
      <div onClick={handleToggleDrawer}>
        {imageWithBackground ? (
          <div className="digi-heba-img">
            <img src={HebalogoGif} alt="heba" className="digi-heba-logo digi-cursor-pointer" />
          </div>
        ) : (
          <img src={HEBA_LOGO} alt="bot_logo" />
        )}
      </div>
      {open && (
        <Suspense fallback="Loading...">
          <Drawer
            anchor="right"
            open={open}
            onClose={handleToggleDrawer}
            sx={{
              width: open ? '95%' : 250,
              flexShrink: 0,
              '& .MuiDrawer-paper': {
                width: open ? '95%' : 250,
                boxSizing: 'border-box',
              },
            }}
          >
            {DrawerList}
          </Drawer>
        </Suspense>
      )}
    </React.Fragment>
  );
};

export default Heba;
