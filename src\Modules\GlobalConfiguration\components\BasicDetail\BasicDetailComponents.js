import React, { useContext } from 'react';
import { fromJS, List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { AccordionProgramInput } from '../ReusableComponent';
import { Trans } from 'react-i18next';
import { basicDetailContext } from './BasicDetailIndex';
import AutoComplete from 'Widgets/FormElements/material/Autocomplete';
import { t } from 'i18next';
import FormControl from '@mui/material/FormControl';
import Checkbox from '@mui/material/Checkbox';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormLabel from '@mui/material/FormLabel';
import DatePicker from 'react-datepicker';
import Chip from '@mui/material/Chip';
import AddEditEventModal from '../../modal/AddEditEventModal';

import AddEditBreakModal from '../../modal/AddEditBreakModal';
import DeleteModal from '../../modal/DeleteModal';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';
import { addMinutes /* setHours, setMinutes */ } from 'date-fns';
import i18n from '../../../../i18n';

import {
  timeZones,
  languages,
  independentHours,
  formatDateTime,
  days,
  formatDateObject,
  daysOrder,
} from '../../utils';
export function TimeZone() {
  const timeZoneData = useContext(basicDetailContext);
  const {
    params,
    basicDetails,
    settings,
    editBasicDetails,
    getRequest,
    institutionHeader,
    updateProgramBasicDetails,
    getProgramID,
    CallingProgramSettings,
    institutionId,
    accordOpen,
    openSelectedAccord,
  } = timeZoneData;
  const displayHeading = () => (
    <div className="f-16 digi-brown remove_hover">
      <b>
        <Trans i18nKey={'global_configuration.time_zone'}></Trans>
      </b>
      {!accordOpen[1] && (
        <p>
          <span>
            {!params.programID ? basicDetails.get('timeZone', '') : settings.get('timeZone', '')}
          </span>
        </p>
      )}
    </div>
  );
  const displayBody = () => (
    <div className="container pl-4 ">
      <div className="row">
        <div className="col-md-5 col-xl-3 col-lg-4 col-sm-6 col-xl-3">
          <AutoComplete
            placeholder={t('global_configuration.set_time_zone')}
            value={
              !params.programID ? basicDetails.get('timeZone', '') : settings.get('timeZone', '')
            }
            isClearable={false}
            options={timeZones}
            onChange={(e, val) => {
              handleChange(val);
            }}
          />
        </div>
      </div>
    </div>
  );

  const handleChange = (newTimeZone) => {
    !params.programID
      ? editBasicDetails({
          id: basicDetails.get('_id', ''),
          requestBody: { ...getRequest(), timeZone: newTimeZone },
          operation: 'timeZone',
          header: institutionHeader,
        })
      : updateProgramBasicDetails({
          programId: getProgramID,
          timeZone: newTimeZone,
          header: institutionHeader,
          urlTIme: `/program-input/update-time-zone`,
          show: false,
          value: true,
          institutionId,
          CallingProgramSettings,
        });
  };
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(1)}
        expanded={accordOpen[1] || false}
      />
      <hr />
    </>
  );
}

TimeZone.propTypes = {
  children: PropTypes.array,
};

export function SelectLanguage({ language, updateLanguage }) {
  const basicDetail = useContext(basicDetailContext);
  const { basicDetails, institutionHeader, accordOpen, openSelectedAccord } = basicDetail;
  const displayHeading = () => {
    let language = basicDetails.get('language', List());
    return (
      <div className="f-16 digi-brown remove_hover">
        <b>
          <Trans i18nKey={'global_configuration.select_languages'}></Trans>
        </b>
        {!accordOpen[2] && (
          <p>
            <span>{language.size + ' ' + t('global_configuration.languages')}</span>
          </p>
        )}
      </div>
    );
  };
  const displayBody = () => (
    <div className="container pl-4">
      <div className="row">
        <div className="col">
          <FormLabel component="legend">
            <div className="row justify-content-between ml-2">
              <div className="mb-2 col-">
                <Trans i18nKey={'global_configuration.choose_languages'}></Trans>
              </div>
              <div className="mb-2 col-">
                <Trans i18nKey={'global_configuration.defaultLanguage'}></Trans>:{' '}
                {basicDetails
                  .get('language', List())
                  .filter((i) => i.get('isDefault', '') === true)
                  .reduce((_, el) => el.get('name', 'N/A'), 'N/A')}
              </div>
            </div>
          </FormLabel>
          <FormControl component="fieldset">
            <FormGroup aria-label="position" row>
              <div className="row">
                {languages
                  .filter((i) => i.value !== 'en')
                  .map((language, key) => (
                    <div className="col-sm-6 col-md-4 col-lg-3" key={key}>
                      <FormControlLabel
                        className="f-11 float-left"
                        key={language.key}
                        value={language.value}
                        control={<Checkbox size="small" color="primary" />}
                        label={language.key}
                        labelPlacement="end"
                        onChange={(e) => {
                          handleLanguageCheck(e);
                        }}
                        checked={basicDetails
                          .get('language', List())
                          .map((i) => i.get('code', ''))
                          .includes(language.value)}
                      />{' '}
                      <div className="clearfix"> </div>
                    </div>
                  ))}
              </div>
            </FormGroup>
          </FormControl>
        </div>
      </div>
    </div>
  );
  const handleLanguageCheck = (event) => {
    let languageName = languages.filter((lan) => lan.value === event.target.value)[0].key;
    let updatedLanguages = [...language];
    let languageId = updatedLanguages
      .filter((lan) => lan.get('code', '') === event.target.value)
      .reduce((_, el) => {
        return el.get('_id', '');
      }, '');
    handleChange(event, languageName, languageId);
  };
  const handleChange = (event, languageName, languageId) => {
    let checked = event.target.checked;
    let code = event.target.value;
    updateLanguage({
      operation: checked ? 'create' : 'delete',
      code,
      languageName,
      languageId,
      id: basicDetails.get('_id', ''),
      header: institutionHeader,
    });
  };
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(2)}
        expanded={accordOpen[2] || false}
      />
      <hr />
    </>
  );
}

SelectLanguage.propTypes = {
  language: PropTypes.object,
  updateLanguage: PropTypes.func,
};

export function WorkingDays({ checked, updateWorkingDays, getBasicDetails }) {
  const basicDetail = useContext(basicDetailContext);
  const {
    basicDetails,
    institutionHeader,
    basicDetailsState,
    getMinMaxTime,
    editBasicDetails,
    getRequest,
    setBasicDetailsState,
    accordOpen,
    openSelectedAccord,
  } = basicDetail;
  const displayHeading = () => {
    let workingDays = basicDetails
      .get('workingDays', List())
      .filter((i) => i.get('isActive', true) === true);
    return (
      <div className="f-16 digi-brown remove_hover">
        <b>
          <Trans i18nKey={'global_configuration.working_days'}></Trans>
        </b>
        {!accordOpen[3] && (
          <p>
            <span>{workingDays.size + ' ' + t('global_configuration.workingDays')}</span>
          </p>
        )}
      </div>
    );
  };
  const displayBody = () => (
    <div className="container pl-4 ">
      <div className="float-left">
        <FormLabel component="legend">
          {' '}
          <Trans i18nKey={'global_configuration.working_hours'}></Trans>
        </FormLabel>
      </div>
      <div className="clearfix"> </div>

      <div className="schedule-date-picker-container d-flex">
        <div>
          <DatePicker
            selected={getDateFormat('start')}
            onChange={(date) => handleChange({ name: 'start', event: date })}
            showTimeSelect
            showTimeSelectOnly
            timeIntervals={15}
            timeCaption="Time"
            dateFormat="h:mm aa"
            className="global-date-picker-input"
          />
        </div>
        <div className="mt-2 m-2">
          <Trans i18nKey={'global_configuration.to'}></Trans>
        </div>
        <div>
          <DatePicker
            {...getMinMaxTime('endTime')}
            selected={getDateFormat('end')}
            onChange={(date) => handleChange({ name: 'end', event: date })}
            showTimeSelect
            showTimeSelectOnly
            timeIntervals={15}
            timeCaption="Time"
            dateFormat="h:mm aa"
            className="global-date-picker-input"
          />
        </div>
      </div>
      <div className="mt-2 float-left">
        <FormControl component="fieldset">
          <FormGroup aria-label="position" row>
            <FormControlLabel
              value={basicDetailsState.get('isIndependentHours', false)}
              control={
                <Checkbox
                  color="primary"
                  checked={basicDetailsState.get('isIndependentHours', false)}
                  onChange={(e) =>
                    handleChange({
                      name: 'isIndependentHours',
                      event: e.target.checked,
                    })
                  }
                />
              }
              label={t('global_configuration.set_independent')}
              labelPlacement="end"
            />
          </FormGroup>
        </FormControl>
      </div>
      <div className="clearfix"> </div>
      <div className="">
        <div className="d-flex float-left">
          <FormLabel className="">
            <Trans i18nKey={'global_configuration.days.days'}></Trans>
          </FormLabel>
          {basicDetailsState.get('isIndependentHours', false) && (
            <FormLabel component="legend">
              <Trans i18nKey={'global_configuration.time'}></Trans>
            </FormLabel>
          )}
        </div>
        <div className="clearfix"> </div>
        {!basicDetailsState.get('isIndependentHours', false) ? (
          <>
            {days.map((day, key) => (
              <div key={key} className="min-wd-250">
                <FormGroup aria-label="position" row>
                  <FormControlLabel
                    checked={basicDetails
                      .get('workingDays', List())
                      .filter((y) => y.get('name', '') === day)
                      .reduce((_, el) => {
                        return el.get('isActive', false);
                      }, false)}
                    control={<Checkbox color="primary" />}
                    onChange={(e) => handleWorkingDaysCheck(e, key)}
                    label={i18n.t(`global_configuration.days.${day.toLowerCase()}`)}
                    labelPlacement="end"
                  />
                </FormGroup>
              </div>
            ))}
          </>
        ) : (
          <>
            {independentHours.map((day, key) => (
              <div key={key} className="row">
                <div className="col-md-3 col-3 col-sm-3">
                  <FormGroup aria-label="position" row>
                    <FormControlLabel
                      checked={basicDetails
                        .get('workingDays', List())
                        .filter((y) => y.get('name', '') === day.day)
                        .reduce((_, el) => {
                          return el.get('isActive', false);
                        }, false)}
                      control={<Checkbox color="primary" />}
                      label={i18n.t(`global_configuration.days.${day.day.toLowerCase()}`)}
                      labelPlacement="end"
                      onChange={(e) => handleWorkingDaysCheck(e, key, 'independent')}
                    />
                  </FormGroup>
                </div>
                <div className="col-md-9 col-9 col-sm-9">
                  <div className="schedule-date-picker-container d-flex">
                    <div>
                      <DatePicker
                        disabled={
                          !basicDetails
                            .get('workingDays', List())
                            .filter((y) => y.get('name', '') === day.day)
                            .reduce((_, el) => {
                              return el.get('isActive', false);
                            }, false)
                        }
                        selected={getDateFormat('independentStart', key)}
                        onChange={(date) =>
                          handleChange({
                            name: 'independentStart',
                            event: date,
                            key,
                          })
                        }
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeCaption="Time"
                        dateFormat="h:mm aa"
                        className="global-date-picker-input"
                      />
                    </div>
                    <div className="mt-2 mb-2 ml-3 mr-3">
                      <Trans i18nKey={'global_configuration.to'}></Trans>
                    </div>
                    <div>
                      <DatePicker
                        {...getMinMaxTime('independentEndTime', key)}
                        disabled={
                          !basicDetails
                            .get('workingDays', List())
                            .filter((y) => y.get('name', '') === day.day)
                            .reduce((_, el) => {
                              return el.get('isActive', false);
                            }, false)
                        }
                        onChange={(date) =>
                          handleChange({
                            name: 'independentEnd',
                            event: date,
                            key,
                          })
                        }
                        selected={getDateFormat('independentEnd', key)}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        timeCaption="Time"
                        dateFormat="h:mm aa"
                        className="global-date-picker-input"
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
      <div className="clearfix"> </div>
    </div>
  );
  const getDateFormat = (type, key) => {
    if (
      (type === 'start' || type === 'end') &&
      basicDetails.getIn(['session', 'start', 'hour'], '')
    ) {
      return formatDateTime(basicDetails.getIn(['session', `${type}`], ''));
    }
    if (
      (type === 'independentStart' || type === 'independentEnd') &&
      basicDetailsState.get('isIndependentHours', '') === true
    ) {
      let session = basicDetailsState.get('workingDays', Map()).map((item) => item.get('session'));
      return formatDateTime(
        session?.getIn([`${key}`, `${type}` === 'independentStart' ? 'start' : 'end'], Map())
      );
    }
    return new Date();
  };
  const handleWorkingDaysCheck = (e, key, type) => {
    let updatedDays = [...checked];
    let dayId = updatedDays[key].get('_id', '');
    if (type === 'independent') {
      handleChange({ name: 'workingDays', event: e.target.checked, dayId, type });
    } else handleChange({ name: 'workingDays', event: e.target.checked, dayId });
  };
  const handleChange = ({ name, event, dayId, type, key }) => {
    const addedMinutes = (event) => {
      let added = addMinutes(event, 15);
      return formatDateObject(added);
    };
    const request = getRequest();
    var session = fromJS(request)
      .get('workingDays', Map())
      .map((item) => item.get('session'));
    var independentSessionId = basicDetails.getIn(['workingDays', `${key}`, '_id'], '');
    if (name === 'start' && event) {
      editBasicDetails({
        id: basicDetails.get('_id', ''),
        requestBody: {
          ...request,
          session: {
            start: formatDateObject(event),
            end: addedMinutes(event),
          },
        },
        operation: name,
        header: institutionHeader,
        callBack: getBasicDetails,
      });
    }
    if (name === 'end' && event) {
      editBasicDetails({
        id: basicDetails.get('_id', ''),
        requestBody: {
          ...request,
          session: {
            end: formatDateObject(event),
            start: basicDetailsState.getIn(['session', 'start'], ''),
          },
        },
        operation: name,
        header: institutionHeader,
        callBack: getBasicDetails,
      });
    }
    if (name === 'isIndependentHours') {
      editBasicDetails({
        id: basicDetails.get('_id', ''),
        requestBody: { ...request, isIndependentHours: event },
        operation: name,
      });
      setBasicDetailsState(basicDetailsState.set('isIndependentHours', event));
    }
    if (name === 'workingDays') {
      if (type === 'independent') {
        if (event === true)
          updateWorkingDays({
            operation: 'add',
            settingId: basicDetails.get('_id', ''),
            id: dayId,
            session: {
              start: basicDetailsState.getIn(['session', 'start'], {
                hour: 8,
                minute: 0,
                format: t('date.am'),
              }),
              end: basicDetailsState.getIn(['session', 'end'], {
                hour: 6,
                minute: 0,
                format: t('date.pm'),
              }),
            },
            isIndependentHours: true,
            header: institutionHeader,
          });

        if (event === false)
          updateWorkingDays({
            operation: 'remove',
            settingId: basicDetails.get('_id', ''),
            id: dayId,
            isIndependentHours: true,
            header: institutionHeader,
          });
      } else {
        if (event === true)
          updateWorkingDays({
            operation: 'add',
            settingId: basicDetails.get('_id', ''),
            id: dayId,
            isIndependentHours: false,
            header: institutionHeader,
          });

        if (event === false)
          updateWorkingDays({
            operation: 'remove',
            settingId: basicDetails.get('_id', ''),
            id: dayId,
            isIndependentHours: false,
            header: institutionHeader,
          });
      }
    }
    if (name === 'independentStart') {
      updateWorkingDays({
        operation: '',
        id: independentSessionId,
        settingId: basicDetails.get('_id', ''),
        isIndependentHours: true,
        session: {
          start: formatDateObject(event),
          end: addedMinutes(event),
        },
        name,
        header: institutionHeader,
      });
    }
    if (name === 'independentEnd') {
      updateWorkingDays({
        operation: '',
        id: independentSessionId,
        isIndependentHours: true,
        settingId: basicDetails.get('_id', ''),
        session: {
          start: session.getIn([`${key}`, 'start'], '').toJS(),
          end: formatDateObject(event),
        },
        name,
        header: institutionHeader,
      });
    }
  };
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(3)}
        expanded={accordOpen[3] || false}
      />
      <hr />
    </>
  );
}

WorkingDays.propTypes = {
  checked: PropTypes.object,
  updateWorkingDays: PropTypes.func,
  getBasicDetails: PropTypes.func,
};

export function Breaks({ requiredFunctions, requiredData }) {
  const basicDetail = useContext(basicDetailContext);
  const {
    params,
    basicDetails,
    institutionHeader,
    updateProgramBasicDetails,
    getProgramID,
    CallingProgramSettings,
    institutionId,
    getMinMaxTime,
    open,
    setData,
    accordOpen,
    openSelectedAccord,
  } = basicDetail;
  const { item, programSettingBreaks, type, modalTitle } = requiredData;
  const { updateBreak, handleClose, handleOpen, addBreakEventReusable } = requiredFunctions;
  const displayHeading = () => {
    let breaks = basicDetails.get('breaks', List());
    return (
      <div className="f-16 digi-brown remove_hover w-100">
        <b>
          <Trans i18nKey={'global_configuration.breaks'}></Trans>
        </b>
        {!accordOpen[4] && (
          <p>
            <span>
              {!params.programID
                ? breaks.size + ' ' + t('global_configuration.breaks')
                : programSettingBreaks.size + ' ' + t('global_configuration.breaks')}
            </span>
          </p>
        )}
        {accordOpen[4] && (
          <div
            className="float-right text-blue ml-auto remove_hover pr-4"
            onClick={(e) => {
              e.stopPropagation();
              handleOpen('break', 'create', item);
            }}
          >
            + <Trans i18nKey={'global_configuration.add_new_break'}></Trans>
          </div>
        )}
      </div>
    );
  };
  const displayBody = () => (
    <div className="container pl-4">
      <div>
        {!params.programID
          ? basicDetails.get('breaks', List()).size
            ? basicDetails.get('breaks', List()).map((item, key) => (
                <div key={key} className="mb-4 d-flex justify-content-between">
                  <div>
                    <div className="bold mb-2">{item.get('name', '10')}</div>
                    <div className="mb-2">
                      {`${item.getIn(['session', 'start', 'hour'], '00')}:${item.getIn(
                        ['session', 'start', 'minute'],
                        'am'
                      )} ${item.getIn(['session', 'start', 'format'], '06')}` +
                        ' - ' +
                        `${item.getIn(['session', 'end', 'hour'], '')}:${item.getIn(
                          ['session', 'end', 'minute'],
                          '00'
                        )} ${item.getIn(['session', 'end', 'format'], 'pm')}`}
                    </div>
                    {item
                      .get('workingDays', List)
                      .sort(function (a, b) {
                        return daysOrder[a] - daysOrder[b];
                      })
                      .map((workingDay, key) => (
                        <Chip
                          key={key}
                          variant="outlined"
                          className="mr-2"
                          size="small"
                          label={workingDay}
                        />
                      ))}
                  </div>
                  <div className="float-right mt-5">
                    <img
                      src={EditIcon}
                      alt="edit"
                      className="digi-pr-12 remove_hover"
                      onClick={() => handleOpen('break', 'edit', item)}
                    />
                    <img
                      src={DeleteIcon}
                      alt="Delete"
                      className="digi-pr-12 remove_hover"
                      onClick={() => handleOpen('breakDelete', 'delete', item)}
                    />
                  </div>
                </div>
              ))
            : ''
          : ''}

        {params.programID
          ? programSettingBreaks.size
            ? programSettingBreaks.map((item, key) => (
                <div key={key} className="mb-4 d-flex justify-content-between">
                  <div>
                    <div className="bold mb-2">{item.get('name', '10')}</div>
                    <div className="mb-2">
                      {`${item.getIn(['session', 'start', 'hour'], '00')}:${item.getIn(
                        ['session', 'start', 'minute'],
                        'am'
                      )} ${item.getIn(['session', 'start', 'format'], '06')}` +
                        ' - ' +
                        `${item.getIn(['session', 'end', 'hour'], '')}:${item.getIn(
                          ['session', 'end', 'minute'],
                          '00'
                        )} ${item.getIn(['session', 'end', 'format'], 'pm')}`}
                    </div>
                    {item
                      .get('workingDays', List)
                      .sort(function (a, b) {
                        return daysOrder[a] - daysOrder[b];
                      })
                      .map((workingDay, key) => (
                        <Chip
                          key={key}
                          variant="outlined"
                          className="mr-2"
                          size="small"
                          label={workingDay}
                        />
                      ))}
                  </div>

                  {item.get('isInstitution', false) ? (
                    <span className="float-right title digi-mt-16">
                      <Trans i18nKey={'break_existed'} />
                    </span>
                  ) : (
                    <div className="float-right mt-5">
                      <img
                        src={EditIcon}
                        alt="edit"
                        className="digi-pr-12 remove_hover"
                        onClick={() => handleOpen('break', 'edit', item)}
                      />
                      <img
                        src={DeleteIcon}
                        alt="Delete"
                        className="digi-pr-12 remove_hover"
                        onClick={() => handleOpen('breakDelete', 'delete', item)}
                      />
                    </div>
                  )}
                </div>
              ))
            : ''
          : ''}
      </div>
      {!params.programID &&
        !basicDetails.get('breaks', List()).size &&
        addBreakEventReusable('break', item, 'add_break')}
      {params.programID &&
        !programSettingBreaks.size &&
        addBreakEventReusable('break', item, 'add_break')}
    </div>
  );
  const addedMinutes = (event) => {
    let added = addMinutes(event, 15);
    return formatDateObject(added);
  };
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(4)}
        expanded={accordOpen[4] || false}
      />
      <hr />
      {open['break'] && (
        <AddEditBreakModal
          addedMinutes={addedMinutes}
          settingId={basicDetails.get('_id', '')}
          updateProgramBasicDetails={updateProgramBasicDetails}
          getProgramID={getProgramID}
          institutionId={institutionId}
          programID={params.programID}
          item={item}
          open={open}
          handleClose={() => handleClose()}
          CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
          title={modalTitle}
          updateBreak={updateBreak}
          getMinMaxTime={getMinMaxTime}
          setData={setData}
          totalBreaks={
            !params.programID ? basicDetails.get('breaks', List()) : programSettingBreaks
          }
          // totalBreaks={programSettingBreaks}
          defaultValues={basicDetails.get('session', List())}
          institutionHeader={institutionHeader}
        />
      )}
      {open['breakDelete'] && (
        <DeleteModal
          type={type}
          updateProgramBasicDetails={updateProgramBasicDetails}
          getProgramID={getProgramID}
          CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
          institutionId={institutionId}
          programID={params.programID}
          item={item}
          settingId={basicDetails.get('_id', '')}
          open={open}
          handleClose={() => handleClose()}
          updateBreak={updateBreak}
          institutionHeader={institutionHeader}
        />
      )}
    </>
  );
}

Breaks.propTypes = {
  requiredFunctions: PropTypes.object,
  requiredData: PropTypes.object,
};

export function Events({ requiredFunctions, requiredData }) {
  const basicDetail = useContext(basicDetailContext);
  const {
    params,
    basicDetails,
    institutionHeader,
    updateProgramBasicDetails,
    getProgramID,
    CallingProgramSettings,
    institutionId,
    setData,
    open,
    accordOpen,
    openSelectedAccord,
  } = basicDetail;
  const { handleOpen, addBreakEventReusable, updateEventType, handleClose } = requiredFunctions;
  const { programSettingEvents, item, modalTitle, type } = requiredData;
  const displayHeading = () => {
    let events = basicDetails.get('eventType', List());
    return (
      <div className="f-16 digi-brown remove_hover w-100">
        <b>
          <Trans i18nKey={'global_configuration.events'}></Trans>
        </b>
        {!accordOpen[5] && (
          <p>
            <span>
              {!params.programID
                ? events.size + ' ' + t('global_configuration.events')
                : programSettingEvents.size + ' ' + t('global_configuration.events')}
            </span>
          </p>
        )}
        {accordOpen[5] && (
          <div
            className="float-right text-blue ml-auto remove_hover pr-4"
            onClick={(e) => {
              e.stopPropagation();
              handleOpen('event', 'create', item);
            }}
          >
            + <Trans i18nKey={'global_configuration.add_new_event'}></Trans>
          </div>
        )}
      </div>
    );
  };
  const displayBody = () => (
    <div className="container pl-4">
      <div>
        {!params.programID
          ? basicDetails.get('eventType', List()).size
            ? basicDetails.get('eventType', List()).map((item, key) => (
                <div key={key} className="mb-4 d-flex justify-content-between">
                  <div>
                    <div className="bold mb-2">{item.get('name', '')}</div>
                    <div className="mb-2">
                      <Trans i18nKey={'global_configuration.leave'}></Trans> :
                      {item.get('isLeave', false) === true ? ' Yes' : ' No'}
                    </div>
                  </div>

                  <div className="float-right mt-3">
                    <img
                      src={EditIcon}
                      alt="edit"
                      className="digi-pr-12 remove_hover"
                      onClick={() => handleOpen('event', 'edit', item)}
                    />
                    <img
                      src={DeleteIcon}
                      alt="Delete"
                      className="digi-pr-12 remove_hover"
                      onClick={() => handleOpen('eventDelete', 'delete', item)}
                    />
                  </div>
                </div>
              ))
            : ''
          : ''}
        {params.programID
          ? programSettingEvents.size
            ? programSettingEvents.map((item, key) => (
                <div key={key} className="mb-4 d-flex justify-content-between">
                  <div>
                    <div className="bold mb-2">{item.get('name', '')}</div>
                    <div className="mb-2">
                      <Trans i18nKey={'global_configuration.leave'}></Trans> :
                      {item.get('isLeave', false) === true ? ' Yes' : ' No'}
                    </div>
                  </div>

                  {item.get('isInstitution', false) ? (
                    <span className="float-right title digi-mt-16">
                      <Trans i18nKey={'break_existed'} />
                    </span>
                  ) : (
                    <div className="float-right mt-5">
                      <img
                        src={EditIcon}
                        alt="edit"
                        className="digi-pr-12 remove_hover"
                        onClick={() => handleOpen('event', 'edit', item)}
                      />
                      <img
                        src={DeleteIcon}
                        alt="Delete"
                        className="digi-pr-12 remove_hover"
                        onClick={() => handleOpen('eventDelete', 'delete', item)}
                      />
                    </div>
                  )}
                </div>
              ))
            : ''
          : ''}
      </div>
      {!params.programID &&
        !basicDetails.get('eventType', List()).size &&
        addBreakEventReusable('event', item, 'global_configuration.add_event')}
      {params.programID &&
        !programSettingEvents.size &&
        addBreakEventReusable('event', item, 'global_configuration.add_event')}

      {open['event'] && (
        <AddEditEventModal
          item={item}
          settingId={basicDetails.get('_id', '')}
          open={open}
          handleClose={() => handleClose()}
          title={modalTitle}
          updateEventType={updateEventType}
          updateProgramBasicDetails={updateProgramBasicDetails}
          setData={setData}
          institutionHeader={institutionHeader}
          getProgramID={getProgramID}
          CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
          institutionId={institutionId}
          programID={params.programID}
        />
      )}
      {open['eventDelete'] && (
        <DeleteModal
          type={type}
          item={item}
          settingId={basicDetails.get('_id', '')}
          updateProgramBasicDetails={updateProgramBasicDetails}
          getProgramID={getProgramID}
          CallingProgramSettings={(props) => CallingProgramSettings({ ...props })}
          institutionId={institutionId}
          programID={params.programID}
          open={open}
          handleClose={() => handleClose()}
          updateEventType={updateEventType}
          institutionHeader={institutionHeader}
        />
      )}
    </div>
  );

  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(5)}
        expanded={accordOpen[5] || false}
      />
      <hr />
    </>
  );
}

Events.propTypes = {
  requiredFunctions: PropTypes.object,
  requiredData: PropTypes.object,
};

export function GenderSegregation() {
  const basicDetail = useContext(basicDetailContext);
  const {
    params,
    basicDetails,
    basicDetailsState,
    settings,
    editBasicDetails,
    getRequest,
    institutionHeader,
    updateProgramBasicDetails,
    getProgramID,
    CallingProgramSettings,
    institutionId,
    setBasicDetailsState,
    accordOpen,
    openSelectedAccord,
  } = basicDetail;
  const displayHeading = () => {
    let gender = basicDetails.get('isGenderSegregation', true);
    let genders = settings.get('isGenderSegregation', true);
    return (
      <div className="f-16 digi-brown remove_hover">
        <b>
          <Trans i18nKey={'global_configuration.gender_segregation'}></Trans>
        </b>
        {!accordOpen[6] && (
          <p>
            <span>
              {!params.programID
                ? gender
                  ? t('global_configuration.groupMaleAndFemale')
                  : t('global_configuration.no_gender_segregation')
                : genders
                ? t('global_configuration.groupMaleAndFemale')
                : t('global_configuration.no_gender_segregation')}
            </span>
          </p>
        )}
      </div>
    );
  };
  const displayBody = () => (
    <div className="container pl-4">
      <FormControl component="fieldset">
        <RadioGroup
          row
          aria-label="position"
          name="position"
          value={
            !params.programID
              ? basicDetails.get('isGenderSegregation', true) === true
                ? 'separate'
                : 'mix'
              : settings.get('isGenderSegregation', true) === true
              ? 'separate'
              : 'mix'
          }
          onChange={(e) => handleChange({ name: 'isGenderSegregation', event: e.target.value })}
        >
          <FormControlLabel
            value="separate"
            control={<Radio color="primary" />}
            label={t('global_configuration.groupMaleAndFemale')}
          />
          <FormControlLabel
            value="mix"
            control={<Radio color="primary" />}
            label={t('global_configuration.no_gender_segregation')}
          />
        </RadioGroup>
      </FormControl>
    </div>
  );

  const handleChange = ({ event, name }) => {
    !params.programID
      ? editBasicDetails({
          id: basicDetails.get('_id', ''),
          requestBody: {
            ...getRequest(),
            isGenderSegregation: event === 'separate' ? true : false,
          },
          operation: name,
        })
      : updateProgramBasicDetails({
          programId: getProgramID,
          updateProgramBasicDetails,
          header: institutionHeader,
          isGenderSegregation: event === 'separate' ? true : false,
          show: true,
          urlGender: `/program-input/update-gender-segregation`,
          value: true,
          institutionId,
          CallingProgramSettings,
        });
    setBasicDetailsState(
      basicDetailsState.set('isGenderSegregation', event === 'separate' ? true : false)
    );
  };
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(6)}
        expanded={accordOpen[6] || false}
      />
      <hr />
    </>
  );
}

export function PrivacySettings({ updatePrivacySettings }) {
  const basicDetail = useContext(basicDetailContext);
  const { basicDetails, institutionHeader, accordOpen, openSelectedAccord } = basicDetail;
  const displayHeading = () => {
    return (
      <div className="f-16 digi-brown remove_hover">
        <b>
          <Trans i18nKey={'global_configuration.privacy_settings'}></Trans>
        </b>
        {!accordOpen[8] && (
          <p>
            <span>
              {basicDetails.getIn(['privacySettings', 0, 'isActive'], false)
                ? t('global_configuration.candidate_photo_blurred')
                : t('global_configuration.candidate_photo_unblurred')}
            </span>
          </p>
        )}
      </div>
    );
  };
  const displayBody = () => (
    <FormControlLabel
      control={
        <Checkbox
          onChange={(e) => handleChange({ name: 'blurPhotos', event: e.target.checked })}
          name="blurPhotos"
          color="primary"
          defaultChecked={basicDetails.getIn(['privacySettings', 0, 'isActive'], false)}
        />
      }
      label={t('global_configuration.blur_candidate_photos')}
    />
  );

  const handleChange = ({ event, name }) => {
    const settings = basicDetails.get('privacySettings', List());
    updatePrivacySettings({
      settingId: basicDetails.get('_id', ''),
      labelId: settings?.get(0)?.get('_id', ''),
      headers: institutionHeader,
    });
  };
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(8)}
        expanded={accordOpen[8] || false}
      />
      <hr />
    </>
  );
}

PrivacySettings.propTypes = {
  updatePrivacySettings: PropTypes.func,
};
