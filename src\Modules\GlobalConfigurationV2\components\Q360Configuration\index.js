/*----------------------------------Import React Library Start------------------------------------*/
import React, { Fragment, createContext, useContext, useEffect, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { Map, fromJS } from 'immutable';
import { useSelector } from 'react-redux';
/*----------------------------------Import React Library End--------------------------------------*/

/*----------------------------------Import Mui Library Start--------------------------------------*/
import HomeIcon from '@mui/icons-material/Home';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
/*----------------------------------Import Mui Library End----------------------------------------*/

/*----------------------------------Import Other Files Start--------------------------------------*/
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';
import MainConfig from 'Modules/GlobalConfigurationV1/components/QaPcConfiguration/MainConfig';
import AcademicYear from './AcademicYear';
import '../../../QAPC/style/style.css';
import '../../styles.css';
import CreateConfig from './ConfigureTemplate/FormSettings';
import ConfigureTemplate from './ConfigureTemplate/ConfigureTemplate';
/*----------------------------------Import Other Files End----------------------------------------*/

/*----------------------------------Import redux Start--------------------------------------------*/
import { selectMessage, selectedIsLoading } from '_reduxapi/q360/selectors';
import Q360CreateSettings from './ConfigureTemplate/CreateSettings/Q360CreateSettings';
import { Divider } from '@mui/material';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import ErrorBoundary from 'Modules/LeaveManagementReports/ErrorBoundaries';
/*----------------------------------Import redux Start--------------------------------------------*/

/*----------------------------------Utils Start---------------------------------------------------*/
export const QueryContext = createContext();
let initialQuery = fromJS({
  qa_pc_configuration: {
    label: 'Q360 Configuration',
    key: 'qa_pc_configuration',
    component: MainConfig,
  },
  configure_template: {
    label: 'Configure Template',
    key: 'qa_pc_configuration+configure_template',
    backKey: 'qa_pc_configuration',
    component: ConfigureTemplate,
  },
  academic_year: {
    label: 'Academic Year',
    key: 'qa_pc_configuration+academic_year',
    component: AcademicYear,
  },
  settings: {
    label: 'Settings',
    key: 'qa_pc_configuration+configure_template+settings',
    backKey: 'qa_pc_configuration+configure_template',
    component: Q360CreateSettings,
  },
});

const Dummy = () => <></>;
/*----------------------------------Utils End-----------------------------------------------------*/

/*----------------------------------CustomHooks Start---------------------------------------------*/
export const useSearchParams = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  return [searchParams];
};
export const useCurrentPage = (paramKey = 'query') => {
  const [searchParams] = useSearchParams();
  const param = searchParams.get(paramKey);
  return param;
};
export const useCheckEditAccess = (pageName) => {
  //isEditAccessAcademicYear,isEditAccessConfigureTemplate
  const permission = useContext(AccessPermissionContext);
  return permission[pageName];
};
export const useRoutePage = () => {
  const history = useHistory();
  const handleRouteing = (route) => {
    history.push(`/globalConfiguration-v1${route}`);
  };
  return [handleRouteing];
};
export const useQueryParamsSplit = (query) => {
  const queryArray = query?.length ? query.split(' ') : [];
  const pagesLength = queryArray.length - 1;
  const currentPage = queryArray[pagesLength];
  const prevPage = queryArray[pagesLength - 1];
  return [currentPage, prevPage, queryArray];
};
/*----------------------------------Custom Hooks End----------------------------------------------*/

/*----------------------------------Utils Compound Start------------------------------------------*/
const BackWordText = ({ currentPage, actionsOfQuery }) => {
  const [searchParams] = useSearchParams();
  const [handleRouteing] = useRoutePage();
  const routeKey = actionsOfQuery.getIn([currentPage, 'backKey'], '');
  const assignedPrograms = searchParams.get('assignedPrograms');
  const navigateBackPage = () => handleRouteing(`/qa_pc_configuration?query=${routeKey}`);
  return (
    <>
      <div className="f-24 d-inline-flex align-items-center  px-3 gap-3">
        <ArrowBackIcon className="cursor-pointer" onClick={navigateBackPage} />
        <div className="pl-1">
          <p className="m-0 text-capitalize cursor-pointer" onClick={navigateBackPage}>
            {actionsOfQuery.getIn([currentPage, 'label'], '').replaceAll('**', '&')}
          </p>
          {assignedPrograms && assignedPrograms.length > 0 && (
            <div className="f-14 fw-400 text-mGrey">
              Assigned Program :&nbsp;
              {getShortString(assignedPrograms, 60)}
            </div>
          )}
        </div>
      </div>
    </>
  );
};
/*----------------------------------Utils Compound End--------------------------------------------*/
export const AccessPermissionContext = React.createContext();

export default function QAPCConfiguration() {
  const message = useSelector(selectMessage);
  const isLoading = useSelector(selectedIsLoading);
  const query = useCurrentPage();
  const currentCategoryIndex = useCurrentPage('currentCategoryIndex');

  const [actionsOfQuery, setActionsOfQuery] = useState(initialQuery);
  const [currentPage, prevPage, queryArray] = useQueryParamsSplit(query);
  const [handleRouteing] = useRoutePage();
  const viewPage = actionsOfQuery.get(currentPage, Map());
  const Component = viewPage.get('component', Dummy);
  const backArrow = viewPage.has('backKey');
  const formDetailPage = prevPage === 'configure_template' && !actionsOfQuery.has(currentPage);
  const isEditAccessAcademicYear = CheckPermission('pages', 'Q360', 'Academic Year', 'Edit');
  const isEditAccessConfigureTemplate = CheckPermission(
    'pages',
    'Q360',
    'Configure Template',
    'Edit'
  );
  useEffect(() => {
    if (formDetailPage) {
      const construct = fromJS({
        label: currentPage.split(',').join(' '),
        key: `qa_pc_configuration+configure_template+${currentPage}`,
        backKey:
          'qa_pc_configuration+configure_template&currentCategoryIndex=' + currentCategoryIndex,
        component: CreateConfig,
      });
      setActionsOfQuery((previous) => previous.set(currentPage, construct));
    }
  }, [query]);
  return (
    <>
      <ErrorBoundary fallback="Q360-Settings">
        {message && <SnackBars show={true} message={message} />}
        <Loader isLoading={isLoading} />
        <AccessPermissionContext.Provider
          value={{ isEditAccessAcademicYear, isEditAccessConfigureTemplate }}
        >
          <div className="position-relative">
            <section className="py-3 bg-white">
              <div className="d-flex align-items-center f-12 px-3 ">
                <HomeIcon fontSize="small" className="digi-gray-neutral mr-1" />
                <span
                  style={{ color: '#9CA3AF' }}
                  className="cursor-pointer mt-1"
                  onClick={() => handleRouteing('/institution')}
                >
                  All Modules
                </span>
                {queryArray.map((item, index) => {
                  const data = actionsOfQuery.get(item, Map());
                  const routeKey = data.get('key', '');
                  const label = data.get('label', '').replaceAll('**', '&');
                  const isCurrent = currentPage === item;
                  return (
                    <Fragment key={index}>
                      <KeyboardArrowRightIcon
                        className={`f-16 mt-1 ${isCurrent ? 'text-dGrey' : 'text-grey'}`}
                      />
                      <span
                        className={`${
                          index + 1 === queryArray.length
                            ? 'cursor-pointer-context-menu '
                            : 'remove_hover'
                        }  ${isCurrent ? 'text-primary f-12' : 'text-grey'} text-capitalize mt-1`}
                        onClick={() => {
                          if (index + 1 === queryArray.length) return;
                          handleRouteing(`/qa_pc_configuration?query=${routeKey}`);
                        }}
                      >
                        {label}
                      </span>
                    </Fragment>
                  );
                })}
              </div>
              {backArrow && (
                <BackWordText currentPage={currentPage} actionsOfQuery={actionsOfQuery} />
              )}
            </section>
            <Divider />
            <Component />
          </div>
        </AccessPermissionContext.Provider>
      </ErrorBoundary>
    </>
  );
}
