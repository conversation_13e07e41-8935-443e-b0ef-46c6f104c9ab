import React, { Fragment } from 'react';
import { row_start, row_end, dynamicAlignment } from '../../../../_utils/function';
import { Level, Text, TextContainer, Course } from '../../Styled';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
import PropTypes, { oneOfType } from 'prop-types';

const LevelCourses = (props) => {
  const {
    active,
    coursePopUp,
    start_date,
    end_date,
    events,
    courses,
    level_no,
    term,
    activeSemester,
    iframeShow,
    currentPGAccess,
  } = props;
  let data_year2_level4 = dynamicAlignment(courses, start_date, end_date);
  let col1 = data_year2_level4[0] !== undefined ? data_year2_level4[0] : 1;
  let courses1 = data_year2_level4[1] !== undefined ? data_year2_level4[1] : [];
  let courseLength = courses1.length;
  return (
    <Fragment>
      <Level
        st_date={row_start(start_date, start_date)}
        end_date={row_end(start_date, end_date)}
        col={col1}
      >
        {courses1 &&
          courses1.map((item, i, arr) => {
            let rowLength = row_start(item.start_date, item.end_date);

            const creditHours =
              item.credit_hours &&
              item.credit_hours.length > 0 &&
              item.credit_hours.map((item) => {
                return item.credit_hours;
              });

            const creditTotal =
              item.credit_hours &&
              item.credit_hours.length > 0 &&
              item.credit_hours.reduce((sum, el) => {
                return el.credit_hours + sum;
              }, 0);

            return (
              <Course
                key={item._id}
                row_st_date={item.row_start}
                row_end_date={item.row_end}
                col_st_date={item.column_start}
                col_end_date={item.column_end}
                bg={item.color_code}
                row_len={row_start(item.start_date, item.end_date)}
                onClick={() =>
                  CheckPermission('tabs', 'Program Calendar', 'Dashboard', '', 'Course', 'View')
                    ? coursePopUp(
                        'edit_course_interim',
                        level_no,
                        item._id,
                        term,
                        active,
                        activeSemester,
                        iframeShow,
                        currentPGAccess
                      )
                    : () => {}
                }
              >
                <div className={courseLength > 5 ? 'strightView' : 'strightView'}>
                  {rowLength > 2 && item.courses_name}
                  {rowLength > 2 && <br />}
                  {`(${item.courses_number.replace(' ', '')})`}
                  <br />
                  {`${creditTotal}`}
                  {'  '}
                  {t('role_management.role_actions.Credit Hours')}
                  {'  '}
                  <br />
                  {`(${creditHours.join('+')})`}
                </div>
                {/* {item.courses_events && item.courses_events
                .map((event) => (
                  <CourseEvent
                    key={event._id}
                    st_date={row_start(arr[i]["start_date"], event.event_date)}
                    end_date={row_end(arr[i]["start_date"], event.end_date)}
                  >
                    <CourseEventContent>
                      {event.event_name}
                    </CourseEventContent>
                  </CourseEvent>
                ))} */}
              </Course>
            );
          })}
        {courses1.length === 0 && events.length === 0 && (
          <TextContainer>
            <Text>{t('no_courses_in_this_level')}</Text>{' '}
            <Text>
              <Trans
                components={{
                  addIcon: (
                    <i className="fas fa-plus" style={{ color: 'black', fontSize: '14px' }}></i>
                  ),
                }}
              >
                add_courses_by_clicking
              </Trans>
            </Text>
          </TextContainer>
        )}
      </Level>
    </Fragment>
  );
};

LevelCourses.propTypes = {
  active: PropTypes.string,
  coursePopUp: PropTypes.func,
  start_date: PropTypes.string,
  end_date: PropTypes.string,
  events: oneOfType([PropTypes.object, PropTypes.array]),
  courses: oneOfType([PropTypes.object, PropTypes.array]),
  level_no: PropTypes.string,
  term: PropTypes.string,
  activeSemester: PropTypes.object,
  iframeShow: PropTypes.bool,
  currentPGAccess: PropTypes.bool,
};

export default LevelCourses;
