<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- <link rel="stylesheet" href="./css/style.css" /> -->
    <link rel="stylesheet" href="./course-report/css/style.css" />
    <title>DigiAssess</title>
    <link rel="icon" sizes="48x48" href="../icons/DA-icon.svg" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"
      rel="stylesheet"
    />

    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
      google.charts.load('current', { packages: ['bar'] });
      //   google.charts.setOnLoadCallback(drawChart);

      function drawChart(chart) {
        var data = google.visualization.arrayToDataTable(chart);

        var options = {
          chart: {
            title: '',
            subtitle: '',
            backgroundColor: '#f5f5f5', // Light grey background
          },
          bars: {
            borderRadius: 4, // Rounded corners
            width: '70%', // Adjust bar width
          },
          colors: ['#4285F4', '#F7CA18', '#DB4437'], // Color palette
          hAxis: {
            title: 'Number of Students',
            titleTextStyle: {
              color: '#333', // Axis label color
            },
            gridlines: {
              color: '#eee', // Gridline color
            },
          },
          vAxis: {
            title: 'Percentage',
            titleTextStyle: {
              color: '#333', // Axis label color
            },
          },
          // ... other options
        };

        var chart = new google.charts.Bar(document.getElementById('columnchart_material'));
        chart.draw(data, google.charts.Bar.convertOptions(options));
      }
    </script>
  </head>

  <body>
    <div class="bg">
      <div class="da-filter">
        <h3>Auto - Course report generator</h3>

        <div class="da-filter-container">
          <select
            class="select-filter attemptType"
            name="selectedAttempTypes[]"
            multiple="multiple"
          ></select>
          <select class="select-filter exam-type" multiple="multiple"></select>
          <button class="button clear-da-filter">Clear All</button>
          <button class="button build-da-report">Build Report</button>
        </div>
      </div>
    </div>

    <!-- <div class="digi-header">
      <span style="flex: 1"></span>
      <div class="digi-logo-container">
        <img class="digi-logo" src="../../assets/icons/digiassess-black.svg" />
        <div class="digi-title-text">Powered by Digival</div>
      </div>
    </div> -->

    <div class="pb-40">
      <!-- <img src="./images/course-report-header.png" class="w-100" /> -->
      <img src="./course-report/images/course-report-header.jpg" class="w-100" />
    </div>
    <!-- <button id="deserializeBtn">Convert JSON to HTML</button> -->

    <div class="tbl-border">
      <div class="tbl-border-bottom">
        <div class="form-left">
          <label for="fname" class="report-label">Course Title:</label>
          <span
            class="report-input-content dc-course-title"
            id="courseTitle"
            contenteditable="true"
          ></span>
        </div>
        <div class="form-right">
          <label for="lname" class="report-label">Course Code:</label>
          <span
            class="report-input-content dc-course-code"
            id="courseCode"
            contenteditable="true"
          ></span>
        </div>
        <div class="clr"></div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-left">
          <label for="fname" class="report-label">Department:</label>
          <span
            class="report-input-content dc-department-name"
            id="department"
            contenteditable="true"
          ></span>
        </div>
        <div class="form-right">
          <label for="lname" class="report-label">Program:</label>
          <span
            class="report-input-content dc-program-name"
            id="program"
            contenteditable="true"
          ></span>
        </div>
        <div class="clr"></div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="fname" class="report-label">College:</label>
          <span
            class="report-input-content report-input-college"
            id="college"
            contenteditable="true"
          ></span>
        </div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-full">
          <label for="fname" class="report-label">Institution:</label>
          <span
            class="report-input-content report-input-institution"
            id="institution"
            contenteditable="true"
          ></span>
        </div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-left">
          <label for="fname" class="report-label">Academic Year:</label>
          <span
            class="report-input-content dc-acadamic-year"
            id="academicYear"
            contenteditable="true"
          ></span>
        </div>
        <div class="form-right">
          <label for="lname" class="report-label">Semester:</label>
          <span
            class="report-input-content dc-acad-label"
            contenteditable="true"
            id="semester"
          ></span>
        </div>
        <div class="clr"></div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-left">
          <label for="fname" class="report-label"> Course Instructor:</label>
          <span
            class="report-input-content dc-instructor-name"
            contenteditable="true"
            id="courseInstructor"
          ></span>
        </div>
        <div class="form-right">
          <label for="lname" class="report-label"> Course Coordinator:</label>
          <span
            class="report-input-content dc-coordinator-name"
            id="courseCoordinator"
            contenteditable="true"
          ></span>
        </div>
        <div class="clr"></div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-left location">
          <label for="fname" class="report-label">Location:</label>

          <input type="checkbox" id="mainCampus" name="" value="mainCampus" />
          <label for="campus" style="color: #6f7178"> Main campus</label>
          <input type="checkbox" id="branch" name="" value="branch" />
          <label for="branch" style="color: #6f7178"> branch </label>
        </div>
        <div class="form-right">
          <label for="lname" class="report-label">Section(s):</label>
          <span
            class="report-input-content dc-student-group"
            id="section"
            contenteditable="true"
          ></span>
        </div>
        <div class="clr"></div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-left">
          <label for="fname" class="report-label-student">
            Number of Students <span class="f-normal">(Started the Course):</span>
          </label>
          <span
            class="report-input-content report-input-total-students-number"
            contenteditable="true"
            id="noOfStudentsStarted"
          ></span>
        </div>
        <div class="form-right">
          <label for="fname" class="report-label-student">
            Number of Students <span class="f-normal">(Registered in Exam):</span>
          </label>
          <span
            class="report-input-content report-input-total-students-number total-registered"
            contenteditable="true"
            id="noOfStudentsRegistered"
          ></span>
        </div>
        <div class="clr"></div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-left">
          <label for="fname" class="report-label-student">
            Number of Students <span class="f-normal">(Completed the Course):</span>
          </label>
          <span
            class="report-input-content report-input-total-students-number started-course"
            contenteditable="true"
            id="noOfStudentsCompleted"
          ></span>
        </div>
        <div class="form-right">
          <label for="fname" class="report-label-student">
            Number of Students
            <span class="f-normal">(Completed all the Selected Exams):</span>
          </label>
          <span
            class="report-input-content report-input-total-students-number total-completed"
            contenteditable="true"
            id="noOfStudentsCompletedSelectedExam"
          ></span>
        </div>
        <div class="clr"></div>
      </div>

      <div class="tbl-border-bottom">
        <div class="form-left">
          <label for="fname" class="report-label">Report Date:</label>
          <input
            type="date"
            class="report-input-total-students"
            placeholder="Pick Report Date"
            id="reportDate"
          />
        </div>
        <div class="form-right">
          <label for="fname" class="report-label">Report Build Date And Time:</label>
          <span class="build-date-time"></span>
        </div>
      </div>
    </div>

    <br /><br /><br /><br /><br /><br />

    <div class="logo-bg">
      <img src="./course-report/images/course-report-logo.png" />
      <!-- <img src="./images/course-report-logo.png" /> -->
    </div>

    <div class="tbl-of-index">Table of Contents</div>
    <table class="w-100">
      <tr class="bg-purple-dark">
        <th class="text-center f-normal p-4">Contents</th>
        <th class="text-center f-normal p-4" style="width: 10%">Page</th>
      </tr>
      <tr>
        <td class="p-4 pl-12">A. Student Results</td>
        <td class="text-center p-4">3</td>
      </tr>
      <tr class="tbl-bg">
        <td class="pl-32">1.Grade Distribution</td>
        <td class="text-center p-4">3</td>
      </tr>

      <tr>
        <td class="pl-32">2. Comment on Student Grades</td>
        <td class="text-center p-4">3</td>
      </tr>

      <tr class="tbl-bg">
        <td class="p-4 pl-12">B. Course Learning Outcomes</td>
        <td class="text-center p-4">4</td>
      </tr>

      <tr>
        <td class="pl-32">1. Course Learning Outcomes Assessment Results</td>
        <td class="text-center p-4">4</td>
      </tr>

      <tr class="tbl-bg">
        <td class="pl-32">2. Comment on Assessment Results</td>
        <td class="text-center p-4">5</td>
      </tr>

      <tr>
        <td class="pl-32">3. Recommendations</td>
        <td class="text-center p-4">5</td>
      </tr>

      <tr class="tbl-bg">
        <td class="p-4 pl-12">C. Topics not covered</td>
        <td class="text-center p-4">5</td>
      </tr>

      <tr>
        <td class="pt-15 pr-4 pb-4 pl-12">
          D. Course Improvement Plan (if any)
          <!-- <button class="fill-all" style="margin-top: 5px">
            HEBA Co-Pilot - To complete the Data Analysis
          </button> -->
        </td>
        <td class="text-center p-4">6</td>
      </tr>
    </table>

    <h1>A. Student Results</h1>
    <h2>1. Grade Distribution</h2>
    <div class="pb-30">
      <table id="gradeDistribution">
        <thead>
          <tr class="bg-purple-dark">
            <th class="w-20"></th>
            <th colspan="9">grades</th>
            <th colspan="6">Status Distributions</th>
          </tr>
          <tr>
            <th class="w-8 bg-purple-dark"></th>
            <th class="w-8 grade-table">A</th>
            <th class="w-8 grade-table">A+</th>
            <th class="w-8 grade-table">B</th>
            <th class="w-8 grade-table">B+</th>
            <th class="w-8 grade-table">C</th>
            <th class="w-8 grade-table">C+</th>
            <th class="w-8 grade-table">D</th>
            <th class="w-8 grade-table">D+</th>
            <th class="w-8 grade-table">F</th>
            <th class="grade-table deniedEntry sd-pd">Denied Entry</th>
            <th class="grade-table sd-pd inProgress">In Progress</th>
            <th class="grade-table sd-pd incomplete">Incomplete</th>
            <th class="grade-table sd-pd pass">Pass</th>
            <th class="grade-table sd-pd pass">Fail</th>
            <th class="grade-table sd-pd deniedEntry">Withdrawn</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="bg-purple-dark">Number of Students</td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
          </tr>
          <tr>
            <td class="bg-purple-dark">Percentage</td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
            <td><div contenteditable="true" class="wordWrap-anywhere"></div></td>
          </tr>
        </tbody>
      </table>
    </div>

    <div id="columnchart_material"></div>
    <div style="clear: both"></div>

    <div>
      <h2>
        2. Comment on Student Grades -
        <!-- <button class="askheba student-grade">HEBA Co-Pilot</button> -->
      </h2>
      <p>Including special factors (if any) affecting the results</p>
      <div style="position: relative">
        <textarea
          id="commentOnStudentGrades"
          name="w3review"
          rows="4"
          cols="80"
          class="recommendations-text heba-students-grade"
        ></textarea>
        <div
          class="loader-student"
          style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
          "
        ></div>
      </div>
    </div>

    <div class="ptb-5">
      <h1>B. Course Learning Outcomes</h1>
      <h2>1. Course Learning Outcomes Assessment Results</h2>
      <div class="digi-clo-container">
        <table class="w-100" id="cloTeachingAndAssessment">
          <thead class="bg-purple-dark">
            <tr>
              <th rowspan="2" colspan="2">Course Learning Outcomes</th>
              <th rowspan="2">Related PLOs Code</th>
              <th rowspan="2">Assessment Methods</th>
              <th colspan="2">Assessment Results</th>
              <th rowspan="2">Comment on Assessment Results</th>
            </tr>
            <tr>
              <th>Targeted Level</th>
              <th>Actual Level</th>
            </tr>
          </thead>
          <tbody>
            <tr class="outcome-row">
              <td class="text-center">1.0</td>
              <td class="" colspan="">Knowledge and understanding</td>
              <td class="" colspan="5" contenteditable="true"></td>
            </tr>
            <tr>
              <td class="deniedEntry text-center">1.1</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">1.2</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">1.3</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr class="outcome-row">
              <td class="text-center">1.0</td>
              <td class="" colspan="">Skills</td>
              <td class="" colspan="5" contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">2.1</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">2.2</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">2.3</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr class="outcome-row">
              <td class="text-center">1.0</td>
              <td class="" colspan="">Values, autonomy, and responsibility</td>
              <td class="" colspan="5" contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">3.1</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">3.2</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
            <tr>
              <td class="text-center">3.3</td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
              <td contenteditable="true"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="ptb-30">
      <h2>
        2. Recommendations
        <!--  -<button class="askheba display-inline heba-recommondation">
          HEBA Co-Pilot
        </button> -->
      </h2>

      <div style="position: relative">
        <textarea
          id="recommendations"
          name="w3review"
          rows="4"
          cols="80"
          class="heba-recommondation-text"
        >
        </textarea>
        <div
          class="loader-recommondation"
          style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
          "
        ></div>
      </div>
    </div>

    <div class="ptb-5">
      <h1>C. Topics not covered.</h1>
      <table class="topics-tbl" id="topicsNotCovered">
        <thead>
          <tr class="bg-purple-dark">
            <th>Topic</th>
            <th>Reason for Not Covering/discrepancies</th>
            <th>Extent of their Impact on Learning Outcomes</th>
            <th>Compensating Action</th>
          </tr>
        </thead>
        <tbody class="topics-not-covered">
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="ptb-5">
      <h1>D. Course Improvement Plan (if any)</h1>
      <table class="topics-tbl w-100" id="courseImprovementPlan">
        <thead>
          <tr class="bg-purple-dark">
            <th>Recommendations</th>
            <th>Actions</th>
            <th>Needed Support</th>
          </tr>
        </thead>
        <tbody class="topics-not-covered">
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
          <tr class="">
            <td class="topic-height" contenteditable="true"></td>
            <td contenteditable="true"></td>
            <td contenteditable="true"></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- <script src="./js/utils.js"></script> -->
    <!-- <script src="./course-report/js/utils.js"></script> -->
    <!-- <script src="./course-report/js/da_course_report.js"></script> -->
    <!-- <script src="./course-report/js/heba.js"></script> -->
    <!-- <script src="./js/dataUploadAndRetrivie.js"></script> -->
    <script src="./course-report/js/dataUploadAndRetrivie.js"></script>
  </body>
</html>
