import React, { useEffect, useState } from 'react';
import { List, Map } from 'immutable';
import { useSelector, useDispatch } from 'react-redux';
import i18n from '../../../../i18n';
import { t } from 'i18next';

import { Dialog, Paper } from '@mui/material';
import AddBoxIcon from '@mui/icons-material/AddBox';
import CloseIcon from '@mui/icons-material/Close';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { styled } from '@mui/material/styles';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';

import Delete from './Delete';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { selectTagMaster } from '_reduxapi/global_configuration/v1/selectors';
import {
  deleteSelectedTagMaster,
  getSelectedTagMaster,
  postSelectedTagMaster,
  updateOverallTagMaster,
  setData,
} from '_reduxapi/global_configuration/v1/actions';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import TagMasterAddGroup from './TagMasterAddGroup';
import TagMasterAddTag from './TagMasterAddTag';
import TagMasterToolBar from './TagMasterToolBar';
import TagMasterHeader from './TagMasterHeader';

const Accordion = styled((props) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  border: `0px solid ${theme.palette.divider}`,
}));

const AccordionSummary = styled((props) => (
  <MuiAccordionSummary
    expandIcon={
      props.expanded ? (
        <KeyboardArrowUpIcon sx={{ fontSize: '1.2rem', color: '#147AFC' }} />
      ) : (
        <KeyboardArrowDownIcon sx={{ fontSize: '1.2rem', color: '#147AFC' }} />
      )
    }
    {...props}
  />
))(({ theme }) => ({
  backgroundColor: '#fff',
  flexDirection: 'row-reverse',
  '& .MuiAccordionSummary-content': {
    marginLeft: theme.spacing(1),
  },
}));

const AccordionSummaryGroup = styled((props) => {
  return (
    <MuiAccordionSummary
      expandIcon={
        props.expanded ? (
          <ArrowDropUpIcon sx={{ fontSize: '1.5rem', color: '#147AFC' }} />
        ) : (
          <ArrowDropDownIcon sx={{ fontSize: '1.5rem', color: '#147AFC' }} />
        )
      }
      {...props}
    />
  );
})(({ theme, expanded }) => ({
  backgroundColor: '#fff',
  flexDirection: 'row-reverse',
  borderRadius: expanded ? '10px 10px 0 0' : '10px',
  '& .MuiAccordionSummary-content': {
    marginLeft: theme.spacing(1),
  },
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: theme.spacing(2),
}));

const TagMaster = () => {
  const dispatch = useDispatch();
  const getTagMasterList = useSelector(selectTagMaster);

  const [popupOpen, setPopupOpen] = useState(Map({ isOpen: false, type: '' }));
  const [fields, setFields] = useState(List());
  const [moreIcon, setMoreIcon] = useState(null);
  const [addTag, setAddTag] = useState(Map({ show: false }));
  const [isDelete, setIsDelete] = useState(false);
  const [getSelectorValue, setGetSelectorValue] = useState(Map());
  const [previousSelectorValue, setPreviousSelectorValue] = useState(Map());
  const [addGroup, setAddGroup] = useState(Map({ show: false }));
  const [isSaveEnable, setIsSaveEnable] = useState(false);

  const [searchText, setSearchText] = useState(Map({}));
  const [expandedGroups, setExpandedGroups] = useState(Map());
  const handleAccordionToggle = (groupId) => {
    setExpandedGroups((prevExpandedGroups) => {
      return prevExpandedGroups.update(groupId, (isExpanded) => !isExpanded);
    });
  };

  useEffect(() => {
    dispatch(getSelectedTagMaster());
  }, []); //eslint-disable-line

  useEffect(() => {
    if (getTagMasterList.size) {
      let newConstructedTagDetails = new Map();
      let newConstructedGroupDetails = new Map();
      const tagDetails = getTagMasterList.get('tags', List());
      let groupsDetails = getTagMasterList.get('groups', List());
      let familyDetails = getTagMasterList.get('families', List());
      if (tagDetails.size) {
        tagDetails.forEach((tagElement) => {
          newConstructedTagDetails = newConstructedTagDetails.set(
            String(tagElement.get('_id', '')),
            tagElement
          );
        });
      }
      if (groupsDetails.size) {
        groupsDetails = groupsDetails.map((groupsElement) => {
          const groupsTagDetails = groupsElement.get('tags', List());
          if (groupsTagDetails.size) {
            groupsTagDetails.forEach((groupTagElement, groupTagIndex) => {
              const tagDetail = newConstructedTagDetails.get(String(groupTagElement));
              if (tagDetail) {
                groupsElement = groupsElement.setIn(['tags', groupTagIndex], tagDetail);
              }
            });
          }
          newConstructedGroupDetails = newConstructedGroupDetails.set(
            String(groupsElement.get('_id')),
            groupsElement
          );
          return groupsElement;
        });
      }
      if (familyDetails.size) {
        familyDetails = familyDetails.map((familiesElement) => {
          const familyGroupElement = familiesElement.get('groups', List());
          if (familyGroupElement.size) {
            familyGroupElement.forEach((familiesGroupElement, familiesGroupIndex) => {
              const groupDetail = newConstructedGroupDetails.get(String(familiesGroupElement));
              if (groupDetail) {
                familiesElement = familiesElement.setIn(
                  ['groups', familiesGroupIndex],
                  groupDetail
                );
              }
            });
          }
          return familiesElement;
        });
      }
      setGetSelectorValue((previousValue) =>
        previousValue
          .set('_id', getTagMasterList.get('_id', ''))
          .set('tags', tagDetails)
          .set('groups', groupsDetails)
          .set('families', familyDetails)
      );
      setPreviousSelectorValue((previousValue) =>
        previousValue
          .set('_id', getTagMasterList.get('_id', ''))
          .set('tags', tagDetails)
          .set('groups', groupsDetails)
          .set('families', familyDetails)
      );
    }
  }, [getTagMasterList]);

  const handleClickPopup = (type, method) => {
    setPopupOpen(popupOpen.set('isOpen', true).set('type', type).set('method', method));

    if (method !== 'update') setFields(List([Map({ code: '', name: '', description: '' })]));
  };

  const handleClosePopup = () => {
    setPopupOpen(popupOpen.set('isOpen', false));
    setMoreIcon(null);
    setFields(List([Map({ code: '', name: '', description: '' })]));
  };

  const handleAddTextField = () => {
    const newField = Map({ id: fields.size + 1 });
    setFields(fields.push(newField));
  };

  const handleRemoveField = (index) => {
    setFields(fields.delete(index));
  };

  const handleTypedData = (event, index, type) => {
    const value = event.target.value;
    setFields(fields.setIn([index, type], value));
  };

  const handleSavePopup = () => {
    const type = popupOpen.get('type', '');

    const isTagIsEmpty = fields.some((item) => item.get('code', '').trim() === '');
    const isNameIsEmpty = fields.some((item) => item.get('name', '').trim() === '');

    if (isTagIsEmpty) {
      return dispatch(setData({ message: 'Short Code is required' }));
    }
    if (isNameIsEmpty) {
      return dispatch(
        setData({
          message: `${
            type === 'tags'
              ? ' Tag Name'
              : type === 'groups'
              ? ' Group Name'
              : type === 'families'
              ? ' Family Name'
              : ''
          } is required`,
        })
      );
    }

    // Extracting the current input fields
    const inputCodes = fields.map((item) => item.get('code', '').trim());
    const inputNames = fields.map((item) => item.get('name', '').trim());

    // Extracting existing codes and names from getSelectorValue
    const existingCodes = getSelectorValue
      .get(type, '')
      .filter((codeId) => codeId.get('_id') !== popupOpen.get('_id'))
      .map((codeId) => codeId.get('code', '').trim());

    const existingNames = getSelectorValue
      .get(type, '')
      .filter((codeName) => codeName.get('_id') !== popupOpen.get('_id'))
      .map((codeName) => codeName.get('name', '').trim());

    // Check for duplicate codes and names within the fields being edited
    const hasDuplicateCodeInFields = new Set(inputCodes).size !== inputCodes.size;
    const hasDuplicateNameInFields = new Set(inputNames).size !== inputNames.size;

    if (hasDuplicateCodeInFields) {
      return dispatch(setData({ message: 'Duplicate Short Code in input fields' }));
    }
    if (hasDuplicateNameInFields) {
      return dispatch(setData({ message: `${type} Name already exists in the input fields` }));
    }

    // Check for duplicate codes and names against the existing getSelectorValue list
    const hasDuplicateCodeInExisting = inputCodes.some((code) => existingCodes.includes(code));
    const hasDuplicateNameInExisting = inputNames.some((name) => existingNames.includes(name));

    if (hasDuplicateCodeInExisting) {
      return dispatch(setData({ message: 'Short Code already exists in the list' }));
    }
    if (hasDuplicateNameInExisting) {
      return dispatch(setData({ message: `${type} Name already exists in the list` }));
    }

    const callback = () => {
      dispatch(getSelectedTagMaster());
      setPopupOpen(popupOpen.set('isOpen', false));
      setMoreIcon(null);
      setFields(List([Map({ code: '', name: '', description: '' })]));
    };

    const fieldsAr = fields.get(0, Map()).toJS();

    const { _id, ...updatedFieldsAr } = fieldsAr;
    updatedFieldsAr.updateId = _id;
    const operation = popupOpen.get('method', '');

    const requestBody = {
      type,
      ...(operation !== 'update'
        ? { [type]: fields.map((fieldItem) => fieldItem.delete('id')) }
        : { ...updatedFieldsAr }),
    };

    dispatch(postSelectedTagMaster(requestBody, callback, operation));
  };

  const handleMoreIconClick = (event, type, list) => {
    setMoreIcon(event.currentTarget);
    setPopupOpen(popupOpen.set('type', type).set('_id', list.get('_id', '')));
    setFields(fields.set(0, list));
  };

  const handleMoreIconClose = () => {
    setMoreIcon(null);
  };

  const handleClickTagOpen = (event, index) => {
    event.stopPropagation();
    setAddTag(addTag.set('show', event.currentTarget).set('index', index));
  };

  const handleClickTagClose = () => {
    setAddTag(addTag.set('show', null));
  };
  const handleCheckbox = (name, aaa, groupName = Map()) => {
    const groupId = groupName.get('_id', '');
    let foundFamilyIndex = -1;
    let foundGroupIndex = -1;

    const families = getSelectorValue.get('families', List());

    families.forEach((family, familyIndex) => {
      const groups = family.get('groups', List());
      const groupIndex = groups.findIndex((group) => group.get('_id') === groupId);

      if (groupIndex !== -1) {
        foundFamilyIndex = familyIndex;
        foundGroupIndex = groupIndex;
      }
    });
    // to handle if only tag is assigned in groups and not in program
    let dataAr = ['families', foundFamilyIndex, 'groups', foundGroupIndex];
    if (foundFamilyIndex === -1 && foundGroupIndex === -1) {
      const groups = getSelectorValue.get('groups', List());
      groups.forEach((group, groupIndex) => {
        if (group.get('_id') === groupId) {
          foundGroupIndex = groupIndex;
        }
      });
      dataAr = ['groups', foundGroupIndex];
    }

    const groupIndex = getSelectorValue
      .get('groups', List())
      .findIndex((item) => item.get('_id', '') === groupName.get('_id', ''));

    if (getSelectorValue.getIn([...dataAr, 'tags']).includes(name)) {
      setGetSelectorValue((prevState) => {
        if (groupName.size && groupIndex !== -1) {
          return prevState
            .updateIn([...dataAr, 'tags'], (tags) =>
              tags.filter((item) => item.get('_id') !== name.get('_id'))
            )
            .updateIn(['groups', groupIndex, 'tags'], (tags) =>
              tags.filter((item) => item.get('_id') !== name.get('_id'))
            );
        } else {
          return prevState.updateIn([...dataAr, 'tags'], (tags) =>
            tags.filter((item) => item.get('_id') !== name.get('_id'))
          );
        }
      });
    } else {
      const updatedTag = getSelectorValue.getIn([...dataAr, 'tags']).push(name);

      if (groupIndex !== -1) {
        const updatedTagFamily = getSelectorValue.getIn(['groups', groupIndex, 'tags']).push(name);

        const setUpdatedTag = getSelectorValue
          .setIn([...dataAr, 'tags'], updatedTag)
          .setIn(['groups', groupIndex, 'tags'], updatedTagFamily);

        setGetSelectorValue(setUpdatedTag);
      } else {
        setGetSelectorValue((prevState) => prevState.setIn([...dataAr, 'tags'], updatedTag));
      }
    }

    setIsSaveEnable(true);
  };

  const handleClickGroupOpen = (event, index) => {
    setAddGroup(addGroup.set('show', event.currentTarget).set('index', index));
  };

  const handleClickGroupClose = () => {
    setAddGroup(addGroup.set('show', null));
  };

  const handleCheckboxGroup = (name, type, familyIndex, groupIndex) => {
    if (getSelectorValue.getIn([type, familyIndex, 'groups']).includes(name)) {
      setGetSelectorValue((prevState) => {
        return prevState.updateIn([type, familyIndex, 'groups'], (tags) => {
          return tags.filter((item) => item !== name);
        });
      });
    } else {
      const updatedTag = getSelectorValue.getIn([type, familyIndex, 'groups']).push(name);
      const setUpdatedTag = getSelectorValue.setIn([type, familyIndex, 'groups'], updatedTag);
      setGetSelectorValue(setUpdatedTag);
    }
    setIsSaveEnable(true);
  };

  const handleDeleteCancel = () => {
    setIsDelete(false);
    setMoreIcon(null);
  };

  const handleDeletePopup = (type, method) => {
    setIsDelete(true);
    setPopupOpen(popupOpen.set('type', type).set('method', method));
  };

  const handleDeleteConfirm = () => {
    const callback = () => {
      dispatch(getSelectedTagMaster());
      setPopupOpen(popupOpen.set('isOpen', false));
      setIsDelete(false);
      setMoreIcon(null);
    };

    const type = popupOpen.get('type', '');

    const fieldsAr = fields.get(0, Map()).toJS();
    const { _id, ...updatedFieldsAr } = fieldsAr;
    updatedFieldsAr.tagGroupId = _id;

    const params = {
      type,
      ...updatedFieldsAr,
    };

    dispatch(deleteSelectedTagMaster(params, callback));
  };

  const handleSaveTagMaster = () => {
    const callback = () => {
      dispatch(getSelectedTagMaster());
    };
    setIsSaveEnable(false);
    let hasDuplicate = false;
    const updatedGroups = getSelectorValue.get('groups').map((groupItem) => {
      const groupId = groupItem.get('_id');

      const isDuplicate = getSelectorValue
        .get('families')
        .filter((family) => family.get('groups').some((group) => group.get('_id') === groupId));
      if (isDuplicate.size > 1) {
        setIsSaveEnable(true);
        dispatch(
          setData({
            message: `Same groups assigned in - ${isDuplicate
              .map((duplicateElement) => duplicateElement.get('name', ''))
              .join(',')}`,
          })
        );
        hasDuplicate = true;
        return null;
      }

      const isAssigned = isDuplicate.size ? true : false;
      const tagsWithIdOnly = groupItem.get('tags').map((tag) => tag.get('_id'));
      return groupItem.set('isAssigned', isAssigned).set('tags', tagsWithIdOnly); // Setting the tags array with only _id
    });
    //if same group assigned to different family - don't allow to save
    if (hasDuplicate) {
      return;
    }
    const validUpdatedGroups = updatedGroups.filter(Boolean);
    const constructData = getSelectorValue
      .set('groups', validUpdatedGroups)
      .update('families', (families) =>
        families.map((family) =>
          family.update('groups', (groups) => groups.map((item) => item.get('_id')))
        )
      );

    dispatch(updateOverallTagMaster(constructData, callback));
  };

  const handleCancel = () => {
    setGetSelectorValue(previousSelectorValue);
  };
  const handleListSearch = (event, type) => {
    const searchTerm = event.target.value;
    setSearchText(searchText.set(type, searchTerm));
  };

  const filterList = (item, type) => {
    const searchText2 = searchText.get(type, '').toLowerCase();
    if (searchText2 === '') return true;
    return (
      item.get('name', '').toLowerCase().includes(searchText2) ||
      item.get('code', '').toLowerCase().includes(searchText2)
    );
  };

  return (
    <>
      <div className="container">
        <div className="mt-3 mb-2 d-flex">
          <h4 className="">{t('Survey_TagMaster.manage_tags')}</h4>
          <div className=" ml-auto">
            <MButton
              variant="text"
              className={`${!isSaveEnable ? 'disabled' : ''} mr-2 border px-3 bold text-dark p-1`}
              sx={{ textTransform: 'none' }}
              size={'small'}
              clicked={handleCancel}
              disabled={!isSaveEnable}
            >
              {t('Survey_TagMaster.cancel')}
            </MButton>
            <MButton
              color={'primary'}
              variant="contained"
              className="ml-1 p-1"
              sx={{ textTransform: 'none' }}
              size={'small'}
              clicked={handleSaveTagMaster}
              disabled={!isSaveEnable}
            >
              {t('Survey_TagMaster.save')}
            </MButton>
          </div>
        </div>
        <div className="d-flex w-100" style={{ gap: '10px' }}>
          <div className="flex-grow-1" style={{ flexBasis: '150px' }}>
            <Paper elevation={3}>
              <div className="p-3">
                <TagMasterHeader
                  handleClickPopup={handleClickPopup}
                  title={i18n.t('Survey_TagMaster.tags')}
                  type={'tags'}
                  handleListSearch={handleListSearch}
                  searchText={searchText}
                />
                <div className="scroll-overflow">
                  {getSelectorValue.get('tags', List()).size ? (
                    getSelectorValue
                      .get('tags', List())
                      .filter((item) => filterList(item, 'tags'))
                      .map((tagMaster, tagMasterIndex) => {
                        return (
                          <div className="divider-radius" key={tagMasterIndex}>
                            <div className="mb-3 mt-2 d-flex">
                              <div>
                                <div className="tagName-font-size">
                                  <span className="bold">{`${tagMaster.get('code', '')}`}</span>
                                  <span>{` ${tagMaster.get('name', '')}`}</span>
                                </div>
                                <div className="text-muted description-font-size mr-1">
                                  {tagMaster.get('description', '')}
                                </div>
                              </div>
                              <div className="ml-auto mt-1">
                                <MoreVertIcon
                                  className="cursor-pointer"
                                  onClick={(e) => {
                                    handleMoreIconClick(e, 'tags', tagMaster);
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        );
                      })
                  ) : (
                    <div className="bold text-center">No Data Found</div>
                  )}
                </div>
              </div>
            </Paper>
          </div>
          <div className="flex-grow-1" style={{ flexBasis: '150px' }}>
            <Paper elevation={3}>
              <div className="p-3">
                <TagMasterHeader
                  handleClickPopup={handleClickPopup}
                  title={i18n.t('Survey_TagMaster.groups')}
                  type={'groups'}
                  handleListSearch={handleListSearch}
                  searchText={searchText}
                />

                <div className="scroll-overflow">
                  {getSelectorValue.get('groups', List()).size ? (
                    getSelectorValue
                      .get('groups', List())
                      .filter((item) => filterList(item, 'groups'))
                      .map((groupMaster, groupIndex) => {
                        const groupId = groupMaster.get('_id');
                        const isExpanded = expandedGroups.get(groupId, false);

                        const groupMasterIndex = getSelectorValue
                          .get('groups', List())
                          .findIndex((groupsItem) => groupsItem.get('_id') === groupId);

                        const dataAr = ['groups', groupMasterIndex];
                        return (
                          <div
                            className="divider-radius"
                            key={groupIndex}
                            style={{ position: 'relative' }}
                          >
                            <Accordion
                              expanded={isExpanded}
                              onChange={() => handleAccordionToggle(groupId)}
                            >
                              <AccordionSummary
                                sx={{
                                  padding: '0px !important',
                                }}
                                aria-controls="panel1d-content"
                                id="panel1d-header"
                              >
                                <div className="d-flex align-items-center w-100">
                                  <div>
                                    <div className="tagName-font-size">
                                      <span className="bold">{groupMaster.get('code', '')}</span>
                                      &nbsp;
                                      <span>{getShortString(groupMaster.get('name', ''), 30)}</span>
                                    </div>
                                    <div className="text-muted description-font-size mr-3">
                                      {groupMaster.get('description', '')}
                                    </div>
                                  </div>
                                </div>
                              </AccordionSummary>
                              <AccordionDetails
                                sx={{
                                  '& .MuiAccordionSummary-root': {
                                    backgroundColor: '#EFF9FB',
                                  },
                                  backgroundColor: '#EFF9FB',
                                  border: '1px solid #D1D5DB',
                                  borderRadius: '4px',
                                  marginLeft: '10px',
                                  marginRight: '10px',
                                  marginBottom: '15px',
                                }}
                              >
                                <div className="d-flex">
                                  <div className="ml-2 d-flex">
                                    <div>
                                      <SubdirectoryArrowRightIcon sx={{ color: '#6B7280' }} />
                                    </div>
                                    <div className="title-font-size title-color-tags bold">{`Tag's`}</div>
                                  </div>
                                  <div className="ml-auto cursor-pointer text-primary-font bold">
                                    <div
                                      className="text-primary"
                                      onClick={(e) => handleClickTagOpen(e, groupMasterIndex)}
                                    >
                                      Add Tags
                                    </div>
                                  </div>
                                </div>
                                <div className="m-3">
                                  {groupMaster.get('tags', List()).map((tagName, tagIndex) => {
                                    return (
                                      <Paper
                                        variant="outlined"
                                        elevation={0}
                                        key={tagIndex}
                                        className="m-3"
                                      >
                                        <div className="d-flex justified-content-center align-items-center textField-color-group p-2">
                                          <div className="ml-3 title-font-size">
                                            <span className="bold">{tagName.get('code', '')}</span>
                                            &nbsp;
                                            <span>{tagName.get('name', '')}</span>
                                          </div>
                                          <div className="ml-auto cursor-pointer">
                                            <CloseIcon
                                              sx={{ color: '#4B5563' }}
                                              onClick={(e) => {
                                                handleCheckbox(tagName, dataAr, groupMaster);
                                              }}
                                            />
                                          </div>
                                        </div>
                                      </Paper>
                                    );
                                  })}
                                </div>
                              </AccordionDetails>
                            </Accordion>
                            {addTag.get('index', 0) === groupMasterIndex &&
                              addTag.get('show', false) && (
                                <TagMasterAddTag
                                  handleClickTagClose={handleClickTagClose}
                                  getSelectorValue={getSelectorValue}
                                  handleCheckbox={handleCheckbox}
                                  addTag={addTag}
                                  dataAr={dataAr}
                                  groupName={groupMaster}
                                />
                              )}
                            <div style={{ position: 'absolute', right: '0px', top: '15px' }}>
                              <MoreVertIcon
                                className="cursor-pointer"
                                onClick={(e) => {
                                  handleMoreIconClick(e, 'groups', groupMaster);
                                }}
                              />
                            </div>
                          </div>
                        );
                      })
                  ) : (
                    <div className="bold text-center">No Data Found</div>
                  )}
                </div>
              </div>
            </Paper>
          </div>
          <div className="flex-grow-1" style={{ flexBasis: '150px' }}>
            <Paper elevation={3}>
              <div className="p-3">
                <TagMasterHeader
                  handleClickPopup={handleClickPopup}
                  title={i18n.t('Survey_TagMaster.family')}
                  type={'families'}
                  handleListSearch={handleListSearch}
                  searchText={searchText}
                />

                <div className="scroll-overflow">
                  {getSelectorValue.get('families', List()).size ? (
                    getSelectorValue
                      .get('families', List())
                      .filter((item) => filterList(item, 'families'))
                      .map((familyMaster, familyIndex) => {
                        const familyId = familyMaster.get('_id');
                        const isExpanded = expandedGroups.get(familyId, false);

                        const familyMasterIndex = getSelectorValue
                          .get('families', List())
                          .findIndex(
                            (familiesIndexName) => familiesIndexName.get('_id') === familyId
                          );
                        return (
                          <div
                            className="divider-radius"
                            key={familyIndex}
                            style={{ position: 'relative' }}
                          >
                            <Accordion
                              expanded={isExpanded}
                              onChange={() => handleAccordionToggle(familyId)}
                            >
                              <AccordionSummary
                                sx={{
                                  padding: '0px !important',
                                }}
                                aria-controls="panel1d-content"
                                id="panel1d-header"
                              >
                                <div className="d-flex align-items-center w-100">
                                  <div>
                                    <div className="tagName-font-size">
                                      <span className="bold">{familyMaster.get('code', '')}</span>
                                      &nbsp;
                                      <span>
                                        {getShortString(familyMaster.get('name', ''), 30)}
                                      </span>
                                    </div>
                                    <div className="text-muted description-font-size mr-3">
                                      {familyMaster.get('description', '')}
                                    </div>
                                  </div>
                                </div>
                              </AccordionSummary>
                              <AccordionDetails>
                                <div className="ml-2 d-flex">
                                  <div>
                                    <SubdirectoryArrowRightIcon sx={{ color: '#6B7280' }} />
                                  </div>
                                  <div className="title-font-size title-color-tags bold">{`Groups`}</div>
                                  <div className="ml-auto text-primary-font">
                                    <div
                                      className="title-font-size text-primary-font bold cursor-pointer"
                                      onClick={(e) => handleClickGroupOpen(e, familyMasterIndex)}
                                    >
                                      Add Groups
                                    </div>
                                  </div>
                                </div>
                                {familyMaster.get('groups', List()).map((groupName, groupIndex) => {
                                  const dataAr = [
                                    'families',
                                    familyMasterIndex,
                                    'groups',
                                    groupIndex,
                                  ];
                                  const familyId = familyMaster.get('_id');
                                  const groupId = groupName.get('_id');
                                  const familyGroupKey = `${familyId}(-)${groupId}`;
                                  const isExpanded = expandedGroups.get(familyGroupKey, false);
                                  return (
                                    <div className="ml-4 mb-2" key={groupIndex}>
                                      <Accordion
                                        expanded={isExpanded}
                                        onChange={() => handleAccordionToggle(familyGroupKey)}
                                        variant="elevation"
                                        sx={{
                                          '& .MuiAccordionSummary-root': {
                                            backgroundColor: '#EFF9FB',
                                          },
                                          backgroundColor: '#EFF9FB',
                                          border: '1px solid #D1D5DB',
                                          borderRadius: '4px',
                                        }}
                                      >
                                        <AccordionSummaryGroup
                                          aria-controls="panel1d-content"
                                          id="panel1d-header"
                                          expanded={isExpanded}
                                        >
                                          <div className="d-flex align-items-center w-100">
                                            <div className="title-font-size">
                                              <div>
                                                <span className="bold">
                                                  {groupName.get('code', '')}
                                                </span>
                                                &nbsp;
                                                <span>{groupName.get('name', '')}</span>
                                              </div>
                                            </div>

                                            <div className="mr-1 mt-1 ml-auto cursor-pointer">
                                              <CloseIcon
                                                sx={{ color: '#4B5563' }}
                                                onClick={(e) => {
                                                  handleCheckboxGroup(
                                                    groupName,
                                                    'families',
                                                    familyMasterIndex,
                                                    groupIndex
                                                  );
                                                }}
                                              />
                                            </div>
                                          </div>
                                        </AccordionSummaryGroup>
                                        <AccordionDetails>
                                          <div className="ml-2 d-flex">
                                            <div>
                                              <SubdirectoryArrowRightIcon
                                                sx={{ color: '#6B7280' }}
                                              />
                                            </div>
                                            <div className="title-font-size title-color-tags bold">{`Tag's`}</div>
                                            <div className="ml-auto cursor-pointer text-primary-font bold">
                                              <div
                                                className=" text-primary"
                                                onClick={(e) =>
                                                  handleClickTagOpen(
                                                    e,
                                                    groupIndex + '' + familyMasterIndex
                                                  )
                                                }
                                              >
                                                Add Tags
                                              </div>
                                              {addTag.get('index', 0) ===
                                                groupIndex + '' + familyMasterIndex &&
                                                addTag.get('show', false) && (
                                                  <TagMasterAddTag
                                                    handleClickTagClose={handleClickTagClose}
                                                    getSelectorValue={getSelectorValue}
                                                    handleCheckbox={handleCheckbox}
                                                    addTag={addTag}
                                                    dataAr={dataAr}
                                                    groupName={groupName}
                                                  />
                                                )}
                                            </div>
                                          </div>
                                          <div className="m-3">
                                            {groupName
                                              .get('tags', List())
                                              .map((tagName, tagIndex) => {
                                                return (
                                                  <Paper
                                                    variant="outlined"
                                                    elevation={0}
                                                    key={tagIndex}
                                                    className="m-3"
                                                  >
                                                    <div className="d-flex p-2">
                                                      <div className="ml-3 mt-1 title-font-size">
                                                        <span className="bold">
                                                          {tagName.get('code', '')}
                                                        </span>
                                                        &nbsp;
                                                        <span>{tagName.get('name', '')}</span>
                                                      </div>
                                                      <div className="d-flex align-items-center ml-auto cursor-pointer">
                                                        <CloseIcon
                                                          sx={{ color: '#4B5563' }}
                                                          onClick={(e) => {
                                                            handleCheckbox(
                                                              tagName,
                                                              dataAr,
                                                              groupName
                                                            );
                                                          }}
                                                        />
                                                      </div>
                                                    </div>
                                                  </Paper>
                                                );
                                              })}
                                          </div>
                                        </AccordionDetails>
                                      </Accordion>
                                    </div>
                                  );
                                })}
                              </AccordionDetails>
                            </Accordion>
                            {addGroup.get('index', 0) === familyMasterIndex &&
                              addGroup.get('show', false) && (
                                <TagMasterAddGroup
                                  handleClickGroupClose={handleClickGroupClose}
                                  getSelectorValue={getSelectorValue}
                                  handleCheckboxGroup={handleCheckboxGroup}
                                  addGroup={addGroup}
                                  fields={fields}
                                />
                              )}
                            <div style={{ position: 'absolute', right: '0px', top: '15px' }}>
                              <MoreVertIcon
                                className="cursor-pointer"
                                onClick={(e) => {
                                  handleMoreIconClick(e, 'families', familyMaster);
                                }}
                              />
                            </div>
                          </div>
                        );
                      })
                  ) : (
                    <div className="bold text-center">No Data Found</div>
                  )}
                </div>
              </div>
            </Paper>
          </div>
        </div>
        {popupOpen && (
          <Dialog
            open={popupOpen.get('isOpen', false)}
            onClose={handleClosePopup}
            aria-labelledby="responsive-dialog-title"
            fullWidth={true}
          >
            <h5 className="mt-3 ml-3">
              Create
              {popupOpen.get('type') === 'tags'
                ? ` ${t('Survey_TagMaster.tags')}`
                : popupOpen.get('type') === 'groups'
                ? ` ${t('Survey_TagMaster.groups')}`
                : popupOpen.get('type') === 'families'
                ? ` ${t('Survey_TagMaster.family')}`
                : ''}
            </h5>
            {fields.map((fieldItem, index) => {
              return (
                <div key={index} className="d-flex m-3">
                  <div className="mr-2">
                    <div className="popupName-title">{t('Survey_TagMaster.short_code')}</div>
                    <MaterialInput
                      elementType={'materialInput'}
                      variant={'outlined'}
                      type={'text'}
                      value={fieldItem.get('code', '')}
                      changed={(e) => {
                        handleTypedData(e, index, 'code');
                      }}
                      size={'small'}
                      sx={{
                        '& .MuiOutlinedInput-input': {
                          fontSize: '12px !important',
                          color: '#4B5563',
                        },
                      }}
                    />
                  </div>
                  <div className="mr-2">
                    <div className="popupName-title">
                      {popupOpen.get('type') === 'tags'
                        ? ` ${t('Survey_TagMaster.tagName')}`
                        : popupOpen.get('type') === 'groups'
                        ? ` ${t('Survey_TagMaster.groups')}`
                        : popupOpen.get('type') === 'families'
                        ? ` ${t('Survey_TagMaster.family')}`
                        : ''}{' '}
                      Name
                    </div>
                    <MaterialInput
                      elementType="materialInput"
                      type={'text'}
                      value={fieldItem.get('name', '')}
                      changed={(e) => {
                        handleTypedData(e, index, 'name');
                      }}
                      size="small"
                      sx={{
                        '& .MuiOutlinedInput-input': {
                          fontSize: '12px !important',
                          color: '#4B5563',
                        },
                      }}
                    />
                  </div>
                  <div>
                    <div className="popupName-title">
                      {t('Survey_TagMaster.description')} (Optional)
                    </div>
                    <MaterialInput
                      elementType="materialInput"
                      type={'text'}
                      value={fieldItem.get('description', '')}
                      changed={(e) => {
                        handleTypedData(e, index, 'description');
                      }}
                      size="small"
                      sx={{
                        '& .MuiOutlinedInput-input': {
                          fontSize: '12px !important',
                          color: '#4B5563',
                        },
                      }}
                    />
                  </div>
                  {popupOpen.get('method', '') === 'create' && (
                    <div className="mt-4 ml-2">
                      <CloseIcon
                        className="cursor-pointer"
                        onClick={() => handleRemoveField(index)}
                      />
                    </div>
                  )}
                </div>
              );
            })}
            {popupOpen.get('method', '') === 'create' && (
              <div className="d-flex ml-auto" onClick={handleAddTextField}>
                <div>
                  <AddBoxIcon className="text-primary" />
                </div>
                <div className="text-primary bold mr-2">{t('Survey_TagMaster.add_more')}</div>
              </div>
            )}
            <div className="d-flex ml-auto m-2">
              <div>
                <MButton clicked={handleClosePopup} variant="outlined" color="gray">
                  {t('Survey_TagMaster.cancel')}
                </MButton>
              </div>
              <div className="ml-2">
                <MButton clicked={handleSavePopup}>{t('Survey_TagMaster.save')}</MButton>
              </div>
            </div>
          </Dialog>
        )}
      </div>

      {moreIcon && (
        <TagMasterToolBar
          handleClickPopup={handleClickPopup}
          type={popupOpen.get('type', '')}
          moreIcon={moreIcon}
          handleMoreIconClose={handleMoreIconClose}
          handleDeletePopup={handleDeletePopup}
          handleDeleteConfirm={handleDeleteConfirm}
        />
      )}
      {isDelete && (
        <Delete
          isDelete={isDelete}
          handleDeletePopup={handleDeletePopup}
          handleDeleteCancel={handleDeleteCancel}
          handleDeleteConfirm={handleDeleteConfirm}
          type={popupOpen.get('type', '')}
        />
      )}
    </>
  );
};

export default TagMaster;
