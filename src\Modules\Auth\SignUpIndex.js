import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';
import * as actions from '../../_reduxapi/user_management/v2/actions';
import { selectIsLoading, selectMessage } from '_reduxapi/user_management/v2/selectors';

class SignUpIndex extends Component {
  render() {
    const { message } = this.props;
    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={this.props.isLoading} />
      </div>
    );
  }
}

SignUpIndex.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(SignUpIndex);
