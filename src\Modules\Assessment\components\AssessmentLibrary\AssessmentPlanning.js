import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Map, List, fromJS } from 'immutable';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Menu,
  MenuItem,
  IconButton,
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import PeopleIcon from '@mui/icons-material/People';
import { useStylesFunction } from '../../css/designUtils';

import * as actions from '../../../../_reduxapi/assessment/action';
import {
  selectAssessmentDashboard,
  selectAssessmentPlanning,
} from '../../../../_reduxapi/assessment/selector';
import { eString, getURLParams, ucFirst } from '../../../../utils';
import AddEditAssessment from '../../modals/AddEditAssessment';
import DeleteModal from '../../modals/DeleteModal';
import ToggleAssessmentTypesModal from '../../modals/ToggleAssessmentTypes';
import { CheckPermission } from 'Modules/Shared/Permissions';

function AssessmentPlanning({
  programId,
  activeInstitutionCalendar,
  getAssessmentPlan,
  assessmentPlanning,
  updateAssessment,
  assessmentDashboard,
  showMarks,
  history,
  toggleAssessmentType,
}) {
  const classes = useStylesFunction();
  const { location } = history;
  const { pathname } = location;
  const institutionCalendar = activeInstitutionCalendar.get('_id', '');
  const planId = assessmentPlanning.get('_id', '');
  const term = getURLParams('term', true)
    ? getURLParams('term', true)
    : assessmentDashboard.getIn(['0', 'term'], '');
  const type = getURLParams('type', true);
  const level = getURLParams('level', true);
  const courseId = getURLParams('cId', true);
  useEffect(() => {
    if (programId !== '' && institutionCalendar !== '' && term !== '')
      getAssessmentPlan(programId, institutionCalendar, { term, type, level, courseId }, showMarks);
  }, [institutionCalendar, programId, getAssessmentPlan, term, type, level, courseId, showMarks]);

  const [expanded, setExpanded] = useState([]);
  const [ids, setIds] = useState(Map());
  const [assessmentTypeArray, setAssessmentTypeArray] = useState([]);
  const [show, setShow] = useState(false);
  const [types, setTypes] = useState(Map());
  useEffect(() => {
    setExpanded([assessmentPlanning.getIn(['types', '0', '_id'], '')]);
  }, [assessmentPlanning]); //eslint-disable-line
  const handleChange = (id) => (event) => {
    if (expanded.includes(id)) {
      const filteredArray = expanded.filter((item) => item !== id);
      setExpanded(filteredArray);
    } else {
      const updatedArray = [...expanded, id];
      setExpanded(updatedArray);
    }
  };

  const handleAssessmentType = (id) => (event) => {
    if (assessmentTypeArray.includes(id)) {
      const filteredArray = assessmentTypeArray.filter((item) => item !== id);
      setAssessmentTypeArray(filteredArray);
    } else {
      const updatedArray = [...assessmentTypeArray, id];
      setAssessmentTypeArray(updatedArray);
    }
  };

  const handleShow = (typeName, subTypeName, assessmentTypes) => {
    setTypes(fromJS({ typeName, subTypeName, assessmentTypes }));
    setShow(true);
  };

  const handleAddShow = (assessmentType, typeId, subTypeId, assessmentTypeId) => {
    setIds(fromJS({ assessmentType, typeId, subTypeId, assessmentTypeId, showMarks }));
    setAddShow(true);
  };

  const handleModalClose = () => {
    setShow(false);
  };
  const [AddShow, setAddShow] = useState(false);
  const [editShow, setEditShow] = useState(false);
  const [deleteShow, setDeleteShow] = useState(false);

  const handleAddModalClose = () => {
    setAddShow(false);
  };

  const handleEditModalOpen = (assessmentType, typeId, subTypeId, assessmentTypeId, plan) => {
    setIds(
      fromJS({
        assessmentType,
        typeId,
        subTypeId,
        assessmentTypeId,
        assessmentId: plan.get('_id', ''),
        plan,
        showMarks,
      })
    );

    setEditShow(true);
    handleModalClose();
  };
  const handleEditModalClose = () => {
    setEditShow(false);
  };
  const handleDeleteModalOpen = (typeId, subTypeId, assessmentTypeId, plan) => {
    setIds(
      fromJS({
        typeId,
        subTypeId,
        assessmentTypeId,
        assessmentId: plan.get('_id', ''),
        plan,
        showMarks,
      })
    );
    setDeleteShow(true);
    handleModalClose();
  };
  const handleDeleteModalClose = () => {
    setDeleteShow(false);
  };

  const goToMarks = (plan, assessment, subType, assessmentType) => {
    const { location } = history;
    const { search } = location;
    const tId = assessment.get('_id', '');
    const tName = assessment.get('typeName', '');
    const stId = subType.get('_id', '');
    const stName = subType.get('typeName', '');
    const atId = assessmentType.get('_id', '');
    const atName = assessmentType.get('name', '');
    history.push(
      `/assessment-management/assessment_details/configure/marks${search}&mid=${eString(
        plan.get('_id', '')
      )}&mName=${eString(plan.get('name', ''))}&marks=${eString(
        plan.get('totalMark', 0)
      )}&tId=${eString(tId)}&tName=${eString(tName)}&stId=${eString(stId)}&stName=${eString(
        stName
      )}&atId=${eString(atId)}&atName=${eString(atName)}&mode=${
        plan.get('studentCount', 0) &&
        plan.get('noQuestions', 0) &&
        plan.get('assessmentOutComes', 0)
          ? 'view'
          : 'edit'
      }`
    );
  };

  const pageTabsType = pathname.includes('/planning')
    ? 'Assessment Plan'
    : 'Program / Course Assessments';
  // type === 'course' || type === 'program' ? 'Program / Course Assessments' : 'Assessment Plan';

  return (
    <>
      <div className="p-3 mb-3">
        <p className="mb-1 bold f-20 text-black">
          {' '}
          {type === 'program'
            ? 'Program Assessment'
            : type === 'course'
            ? 'All Assessments'
            : 'Assessment Planning'}{' '}
        </p>
        <div className="pt-3">
          {!assessmentPlanning.isEmpty() &&
            assessmentPlanning.get('types', List()).map((assessment, index) => {
              return (
                <React.Fragment key={index}>
                  <Accordion
                    expanded={expanded.includes(assessment.get('_id'))}
                    onChange={handleChange(assessment.get('_id'))}
                    className={classes.accordionBorderUnset}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon fontSize={`large`} edge={'start'} />}
                      aria-controls="panel1bh-content"
                      id="panel1bh-header"
                    >
                      <p className="mb-0 bold f-19 assessment-planning-text">
                        {ucFirst(assessment.get('typeName', ''))}
                      </p>
                    </AccordionSummary>
                    <AccordionDetails>
                      <div className="w-100">
                        {assessment
                          .get('subTypes', List())
                          .filter((item) => item.get('isActive', false) === true)
                          .map((subType, subIndex) => {
                            return (
                              <React.Fragment key={subIndex}>
                                <div className="d-flex justify-content-between align-items-center">
                                  <p className="mt-2 mb-2 bold assessment-planning-text">
                                    {ucFirst(subType.get('typeName', ''))}{' '}
                                  </p>
                                  {CheckPermission(
                                    'tabs',
                                    'Assessment Management',
                                    'Assessment Library',
                                    '',
                                    pageTabsType,
                                    'Set Assessment Type'
                                  ) && (
                                    <div className="d-flex">
                                      <SettingsIcon
                                        onClick={() =>
                                          handleShow(
                                            assessment.get('typeName', ''),
                                            subType.get('typeName', ''),
                                            subType.get('assessmentTypes', '')
                                          )
                                        }
                                        className="remove_hover"
                                      />
                                    </div>
                                  )}
                                </div>
                                <div>
                                  {subType
                                    .get('assessmentTypes', List())
                                    .filter((item) => item.get('isActive', false) === true)
                                    .map((assessmentType, assessmentIndex) => {
                                      return (
                                        <Accordion
                                          key={assessmentIndex}
                                          expanded={assessmentTypeArray.includes(
                                            assessmentType.get('_id', '')
                                          )}
                                          onChange={handleAssessmentType(
                                            assessmentType.get('_id', '')
                                          )}
                                          className={classes.accordionBorderInnerUnset}
                                        >
                                          <AccordionSummary
                                            expandIcon={
                                              <ExpandMoreIcon
                                                fontSize={`small`}
                                                className="m-0 p-0"
                                              />
                                            }
                                            aria-controls="panel1bh-content"
                                            id="panel1bh-header"
                                            className={classes.accordionArrowPosition}
                                          >
                                            <div className="d-flex justify-content-between align-items-center ml-2 mb-3 mt-3 w-100">
                                              <p className="mb-0 f-15 assessment-planning-text">
                                                {ucFirst(assessmentType.get('name', ''))}
                                              </p>

                                              {CheckPermission(
                                                'tabs',
                                                'Assessment Management',
                                                'Assessment Library',
                                                '',
                                                pageTabsType,
                                                'Add'
                                              ) && (
                                                <div
                                                  className="d-flex text-skyblue bold remove_hover"
                                                  onClick={() =>
                                                    handleAddShow(
                                                      assessmentType,
                                                      assessment.get('_id', ''),
                                                      subType.get('_id', ''),
                                                      assessmentType.get('_id', '')
                                                    )
                                                  }
                                                >
                                                  <AddIcon />
                                                  <p className="mb-0 f-15 padding-top-2px">
                                                    Add New
                                                  </p>
                                                </div>
                                              )}
                                            </div>
                                          </AccordionSummary>
                                          <AccordionDetails>
                                            <div className="w-100">
                                              {assessmentType
                                                .get('planning', List())
                                                .filter(
                                                  (item) => item.get('isActive', false) === true
                                                )
                                                .map((plan, planIndex) => {
                                                  let arr = plan.get('name', '').split(' ', 3);
                                                  return (
                                                    <div
                                                      key={planIndex}
                                                      className="bg-white pl-3 pr-3 pt-2 pb-2 mb-2 border-radious-4 assessment-box"
                                                    >
                                                      <div className="row align-items-center">
                                                        <div className="col-md-6 col-xl-6 col-6 align-items-center d-flex">
                                                          <div className="digi-Shared-bg p-3 border-radious-8 alphabet-icon">
                                                            <p className="mb-0 f-15 pl-1 pr-1 text-center alphabet-icon-text">
                                                              {arr.map((item) => {
                                                                return item.charAt(0);
                                                              })}
                                                            </p>
                                                          </div>
                                                          <p className="mb-0 pl-3">
                                                            {' '}
                                                            {plan.get('name')}
                                                          </p>
                                                        </div>

                                                        <div className="col-md-6 col-xl-6 col-6 d-flex align-items-center justify-content-end">
                                                          {showMarks && (
                                                            <>
                                                              <div className="border-left text-center pt-1 pb-1 w-20 d-flex justify-content-center align-items-center">
                                                                <div className="mb-0 mr-2 mt-1">
                                                                  <PeopleIcon />
                                                                </div>
                                                                <p className="mb-0  bold">
                                                                  {' '}
                                                                  {plan.get('studentCount', 0)}
                                                                </p>
                                                              </div>

                                                              <div className="border-left text-center pt-2 pb-2 w-20 d-flex justify-content-center align-items-center">
                                                                <div className="mb-0 mr-2 questionBorder bold">
                                                                  Q’s
                                                                </div>
                                                                <p className="mb-0  bold">
                                                                  {' '}
                                                                  {plan.get('noQuestions', 0)}
                                                                </p>
                                                              </div>

                                                              <div className="border-left text-center pt-2 pb-2 w-20 d-flex justify-content-center align-items-center">
                                                                <div className="mb-0 mr-2 questionBorder bold">
                                                                  Lo
                                                                </div>
                                                                <p className="mb-0  bold">
                                                                  {' '}
                                                                  {plan.get(
                                                                    'assessmentOutComes',
                                                                    0
                                                                  )}
                                                                </p>
                                                              </div>
                                                            </>
                                                          )}
                                                          <div className="border-left text-center w-20">
                                                            <p className="mb-0 pl-2 pr-2 f-14 text-lightgray">
                                                              {' '}
                                                              Mark
                                                            </p>
                                                            <p className="mb-0 pl-2 pr-2 bold">
                                                              {' '}
                                                              {plan.get('totalMark', '')}
                                                            </p>
                                                          </div>
                                                          {showMarks && (
                                                            <>
                                                              {CheckPermission(
                                                                'tabs',
                                                                'Assessment Management',
                                                                'Assessment Library',
                                                                '',
                                                                'Program / Course Assessments',
                                                                'Assessment Enter Mark'
                                                              ) && (
                                                                <div
                                                                  className="border-left text-center pb-2 pl-4 pr-4 pt-2 remove_hover w-25"
                                                                  onClick={() =>
                                                                    goToMarks(
                                                                      plan,
                                                                      assessment,
                                                                      subType,
                                                                      assessmentType
                                                                    )
                                                                  }
                                                                >
                                                                  {' '}
                                                                  <p className="mb-0  bold text-skyblue">
                                                                    {' '}
                                                                    {plan.get('studentCount', 0) &&
                                                                    plan.get('noQuestions', 0) &&
                                                                    plan.get(
                                                                      'assessmentOutComes',
                                                                      0
                                                                    )
                                                                      ? 'View'
                                                                      : 'Enter Mark'}
                                                                  </p>{' '}
                                                                </div>
                                                              )}
                                                            </>
                                                          )}
                                                          {(CheckPermission(
                                                            'tabs',
                                                            'Assessment Management',
                                                            'Assessment Library',
                                                            '',
                                                            pageTabsType,
                                                            'Edit'
                                                          ) ||
                                                            CheckPermission(
                                                              'tabs',
                                                              'Assessment Management',
                                                              'Assessment Library',
                                                              '',
                                                              pageTabsType,
                                                              'Delete'
                                                            )) && (
                                                            <DropMenu
                                                              assessment={assessment}
                                                              subType={subType}
                                                              assessmentType={assessmentType}
                                                              handleEditModalOpen={
                                                                handleEditModalOpen
                                                              }
                                                              handleDeleteModalOpen={
                                                                handleDeleteModalOpen
                                                              }
                                                              plan={plan}
                                                              pageTabsType={pageTabsType}
                                                            />
                                                          )}
                                                        </div>
                                                      </div>
                                                    </div>
                                                  );
                                                })}
                                            </div>
                                          </AccordionDetails>
                                        </Accordion>
                                      );
                                    })}
                                </div>
                              </React.Fragment>
                            );
                          })}
                      </div>
                    </AccordionDetails>
                  </Accordion>
                </React.Fragment>
              );
            })}
        </div>
      </div>

      {show && (
        <ToggleAssessmentTypesModal
          show={show}
          cancel={handleModalClose}
          assessmentPlanning={assessmentPlanning}
          toggleAssessmentType={toggleAssessmentType}
          types={types}
          getAssessmentPlan={() =>
            getAssessmentPlan(
              programId,
              institutionCalendar,
              { term, type, level, courseId },
              showMarks
            )
          }
          showMarks={showMarks}
        />
      )}
      {AddShow && (
        <AddEditAssessment
          ids={ids}
          planId={planId}
          calendarId={institutionCalendar}
          programId={programId}
          mode="create"
          show={AddShow}
          cancel={handleAddModalClose}
          programCourseLevel={fromJS({
            term,
            type,
            level,
            courseId,
          })}
        />
      )}
      {editShow && (
        <AddEditAssessment
          ids={ids}
          planId={planId}
          calendarId={institutionCalendar}
          programId={programId}
          mode="update"
          show={editShow}
          cancel={handleEditModalClose}
          programCourseLevel={fromJS({
            term,
            type,
            level,
            courseId,
          })}
        />
      )}
      {deleteShow && (
        <DeleteModal
          show={deleteShow}
          cancel={handleDeleteModalClose}
          ids={ids}
          handleDelete={updateAssessment}
          mode="plan"
          assessment={assessmentPlanning}
          callback={() =>
            getAssessmentPlan(
              programId,
              institutionCalendar,
              { term, type, level, courseId },
              showMarks
            )
          }
          fileName={ids.getIn(['plan', 'name'], '')}
          componentName={'Assessment'}
        />
      )}
    </>
  );
}
const DropMenu = ({
  assessment,
  subType,
  assessmentType,
  plan,
  handleEditModalOpen,
  handleDeleteModalOpen,
  pageTabsType,
}) => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleEditOpen = (e, assessmentObj, assessment, subType, assessmentType, plan) => {
    handleEditModalOpen(assessmentObj, assessment, subType, assessmentType, plan);
    handleClose();
  };
  const handleDeleteOpen = (e, assessment, subType, assessmentType, plan) => {
    handleDeleteModalOpen(assessment, subType, assessmentType, plan);
    handleClose();
  };
  const ITEM_HEIGHT = 48;

  return (
    <div className="">
      <IconButton
        aria-label="more"
        aria-controls="long-menu"
        aria-haspopup="true"
        onClick={handleClick}
        size="small"
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        id="long-menu"
        anchorEl={anchorEl}
        keepMounted
        open={open}
        onClose={handleClose}
        PaperProps={{
          style: {
            maxHeight: ITEM_HEIGHT * 4.5,
            width: '20ch',
          },
        }}
      >
        {CheckPermission(
          'tabs',
          'Assessment Management',
          'Assessment Library',
          '',
          pageTabsType,
          'Edit'
        ) && (
          <MenuItem
            onClick={(e) =>
              handleEditOpen(
                e,
                assessmentType,
                assessment.get('_id', ''),
                subType.get('_id', ''),
                assessmentType.get('_id', ''),
                plan
              )
            }
          >
            Edit
          </MenuItem>
        )}
        {CheckPermission(
          'tabs',
          'Assessment Management',
          'Assessment Library',
          '',
          pageTabsType,
          'Delete'
        ) && (
          <MenuItem
            onClick={(e) =>
              handleDeleteOpen(
                e,
                assessment.get('_id', ''),
                subType.get('_id', ''),
                assessmentType.get('_id', ''),
                plan
              )
            }
          >
            Delete
          </MenuItem>
        )}
      </Menu>
    </div>
  );
};
DropMenu.propTypes = {
  plan: PropTypes.instanceOf(Map),
  assessment: PropTypes.instanceOf(Map),
  subType: PropTypes.instanceOf(Map),
  assessmentType: PropTypes.instanceOf(Map),
  handleEditModalOpen: PropTypes.func,
  handleDeleteModalOpen: PropTypes.func,
  pageTabsType: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    assessmentPlanning: selectAssessmentPlanning(state),
    assessmentDashboard: selectAssessmentDashboard(state),
  };
};

AssessmentPlanning.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getAssessmentPlan: PropTypes.func,
  programId: PropTypes.string,
  assessmentPlanning: PropTypes.instanceOf(Map),
  assessmentDashboard: PropTypes.instanceOf(List),
  updateAssessment: PropTypes.func,
  showMarks: PropTypes.bool,
  history: PropTypes.object,
  toggleAssessmentType: PropTypes.func,
};

export default compose(withRouter, connect(mapStateToProps, actions))(AssessmentPlanning);
