import React, { useState, Suspense } from 'react';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';

import Loader from '../../../../Widgets/Loader/Loader';
import Tooltips from '../../../../_components/UI/Tooltip/Tooltip';
import { getLang } from '../../../../utils';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

const MonitoringSettingsModal = React.lazy(() => import('../../modal/MonitoringSettingsModal'));
const lang = getLang();

function ProgramCard(props) {
  const { redirectDetailPage, loading, programsLists } = props;
  const [searchText, setSearchText] = useState('');
  const [show, setShow] = useState(false);

  ProgramCard.propTypes = {
    redirectDetailPage: PropTypes.func,
    programsLists: PropTypes.instanceOf(List),
    loading: PropTypes.instanceOf(Map),
  };

  const showModal = () => {
    setShow(!show);
  };

  return (
    <>
      <Loader pos="absolute" isLoading={loading.get('program_list')} />
      <div className="row">
        <div className={`col-md-10 ${lang === 'ar' ? 'pl-0' : 'pr-0'}`}>
          <div className="bg-white dash_box_head2 border border-radious-8">
            <div className="sb-example-3">
              <div className="search">
                <input
                  type="text"
                  className="searchTerm pl-2"
                  placeholder={t('dashboard_view.search_program_name_code')}
                  value={searchText}
                  maxLength={100}
                  onChange={(e) => setSearchText(e.target.value)}
                />
                <button type="submit" className="searchButton">
                  <i className="fa fa-search"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-2 pl-0">
          <div className="bg-white dash_box_head2 border border-radious-8 remove_hover text-center">
            <Tooltips
              title={t(
                loading.get('GET_CONFIGURE_SETTINGS')
                  ? `loading`
                  : `dashboard_view.configure_settings`
              )}
            >
              <p
                className={`f-24 mb-0 text-center ${
                  loading.get('GET_CONFIGURE_SETTINGS') ? 'text-grey' : ''
                }`}
                onClick={() => (loading.get('GET_CONFIGURE_SETTINGS') ? {} : showModal())}
              >
                {' '}
                <i className="fa fa-cog" aria-hidden="true"></i>
              </p>
            </Tooltips>
          </div>
        </div>
        {show && (
          <Suspense fallback="">
            <MonitoringSettingsModal {...props} show={show} showModal={showModal} />
          </Suspense>
        )}
      </div>

      {programsLists.size === 0 && (
        <div className="text-center mt-5">
          {loading.get('program_list') ? (
            <Trans i18nKey={'fetching_data'}></Trans>
          ) : (
            <Trans i18nKey={'no_data'}></Trans>
          )}
        </div>
      )}
      {programsLists?.size > 0 &&
        programsLists
          // .filter((item) => item.get('isConfigured') === true)
          ?.filter((item) =>
            searchText.toLowerCase() !== ''
              ? item.get('name', '').toLowerCase().includes(searchText.toLowerCase()) ||
                item.get('code', '').toLowerCase().includes(searchText.toLowerCase())
              : item
          )
          .map((program, pgIndex) => {
            return (
              <div className="pt-3" key={pgIndex}>
                <div className="p-3 bg-white border-radious-8 ">
                  <p className={`bold mb-1 ${lang === 'ar' ? 'text-left' : ''}`}>
                    {' '}
                    {program.get('name')}{' '}
                  </p>
                  <div className="d-flex justify-content-between">
                    <p className={`mb-0 text-lightblack ${lang === 'ar' ? 'text-left' : ''}`}>
                      {' '}
                      <Trans i18nKey={'dashboard_view.no_of_year'}></Trans>-{' '}
                      {program.get('no_year', '')}{' '}
                    </p>
                    <i
                      onClick={() => redirectDetailPage(program.get('_id'), program.get('name'))}
                      className={` remove_hover text-lightblack ${
                        lang === 'ar' ? 'fa fa-chevron-left' : 'fa fa-chevron-right'
                      }`}
                      aria-hidden="true"
                    ></i>
                  </div>
                  <p className={`mb-0 f-15 text-lightblack ${lang === 'ar' ? 'text-left' : ''}`}>
                    {' '}
                    <Trans i18nKey={'dashboard_view.active_curriculum'}></Trans> -{' '}
                    {program
                      .getIn(['curriculum'], List())
                      .map((key) => key.get('curriculum_name'))
                      .toJS()
                      .join(', ')}{' '}
                  </p>
                </div>
              </div>
            );
          })}
    </>
  );
}

export default React.memo(ProgramCard);
