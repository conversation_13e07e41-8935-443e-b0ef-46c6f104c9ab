import React, { forwardRef } from 'react';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import anomaly_levels from 'Assets/user_activity_board/anomaly_levels.svg';
import LevelWiseTable from './LevelWiseTable';
import { Map } from 'immutable';
import PropTypes from 'prop-types';

const AnomalyLevels = forwardRef(({ anomalyBoard }, badgeRef) => {
  return (
    <Accordion disableGutters sx={{ background: 'white !important' }}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1a-content"
        id="panel1a-header"
        className="px-3 py-2"
      >
        <div className="d-flex">
          <img src={anomaly_levels} alt="icon_late_config" />
          <div className="ml-3">
            <div className="f-18 bold late_config_color">Anomaly Levels</div>
            <span className="f-15 text-light-grey pt-1">Select the percentages ranges.</span>
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <LevelWiseTable anomalyBoard={anomalyBoard} ref={badgeRef} />
      </AccordionDetails>
    </Accordion>
  );
});
AnomalyLevels.propTypes = {
  anomalyBoard: PropTypes.instanceOf(Map),
};
export default AnomalyLevels;
