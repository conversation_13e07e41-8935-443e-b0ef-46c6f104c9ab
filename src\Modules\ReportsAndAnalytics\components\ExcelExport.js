import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import {
  getCollegeLogo,
  getVersionName,
  isLateAbsentEnabled,
  studentGroupRename,
  trimFractionDigits,
} from 'utils';
import { jsUcfirstAll } from 'utils';
import { List, Map, fromJS } from 'immutable';
import moment from 'moment';
import { getEnvCollegeName } from '../../../utils';
import { excelDuration, getUserData } from '../utils';
export const exportExcel = async ({
  filters,
  activeInstitutionCalendar,
  courseOverview,
  authData,
  studentGroup,
  detailedData,
  programId,
}) => {
  const tableFilter = filters.get('tableFilter', List()).toJS();
  const month = filters.get('month', '');
  const courseDetails = courseOverview.get('course_details', Map());
  const activeGender = filters.get('gender', '');
  const viewType = filters.get('viewType', 'student');
  const headerFooterData = getHeaderFooterData();
  const sheet2Data = fromJS(detailedData);

  const isLateEnabled = isLateAbsentEnabled();

  function getColumnHeader() {
    const headerWithKeys = [
      {
        header: 'S.No',
        key: 'S.No',
      },
      {
        header: `${jsUcfirstAll(viewType)} Name`,
        key: 'Student Name',
      },
      {
        header: viewType === 'student' ? 'Academic Id' : 'Employee Id',
        key: 'Academic Id',
      },
      {
        header: 'Gender',
        key: 'Gender',
      },
    ];
    const array = [{ width: 30 }, { width: 60 }, { width: 20 }, { width: 20 }];
    if (headerFooterData.size > 0) {
      headerFooterData.map((schedule) => {
        array.push({ width: 20 });
        headerWithKeys.push({
          header: `${
            tableFilter.includes('delivery_group')
              ? studentGroupRename(schedule.get('session', ''), programId)
              : schedule.get('delivery_type', '')
          } ${
            tableFilter.includes('schedule_date') && schedule.get('schedule_date', '') !== ''
              ? `(${moment(schedule.get('schedule_date', '')).format('DD/MM/YYYY')})`
              : ``
          }`,
          key: schedule.get('session', ''),
        });
        return schedule;
      });
    }

    if (tableFilter.includes('monthly') && month !== '') {
      headerWithKeys.push({
        header: 'Attendance %',
        key: 'Attendance %',
      });
      array.push({ width: 20 });
    }

    if (tableFilter.includes('overall')) {
      headerWithKeys.push({
        header: 'Overall %',
        key: 'Overall %',
      });
      array.push({ width: 20 });
    }
    return { headerWithKeys, array };
  }
  const headerOnly = getColumnHeader()['headerWithKeys'].map((head) => head.header);
  function getStudentGroup() {
    return filters
      .get('studentGroup', List())
      .map((item) => studentGroupRename(item, programId))
      .join(', ');
  }

  const exportBy =
    jsUcfirstAll(authData.getIn(['name', 'first'], '')) +
    ' ' +
    jsUcfirstAll(authData.getIn(['name', 'middle'], '')) +
    ' ' +
    jsUcfirstAll(authData.getIn(['name', 'last'], ''));

  const userList = getUserData(
    studentGroup,
    filters.get('viewType', ''),
    activeGender,
    filters.get('search', '').toLowerCase()
  );
  const hasUserId = tableFilter.includes('user_id');
  const hasGender = tableFilter.includes('gender');

  function getAttendanceStatus(status) {
    switch (status) {
      case 'present':
        return 'P';
      case 'leave':
      case 'absent':
        return status.charAt(0).toUpperCase();
      case 'permission':
        return 'PER';
      case 'on_duty':
        return 'OD';
      case 'exclude':
        return 'E';
      default:
        return '';
    }
  }

  function returnStudentStatus(userId, schedule_id) {
    const findArray = userList.find((item) =>
      viewType === 'student' ? item.get('_id', '') === userId : item.get('_staff_id', '') === userId
    );
    if (findArray) {
      const findSession = findArray
        .get('session_details', List())
        .find((item) => item.get('schedule_id', '') === schedule_id);
      if (findSession) {
        if (findSession.get('group_status') === false) {
          return '---';
        } else if (!findSession.get('isActive')) {
          return '';
        } else {
          const status = getAttendanceStatus(findSession.get('attendance', ''));
          const formatText = {
            richText: [
              {
                font: {
                  color: {
                    argb: ['P', 'OD', 'PER'].includes(status)
                      ? '00B050'
                      : status === 'E'
                      ? 'D97706'
                      : 'FF0000',
                  },
                },
                text: `  ${status} ${
                  isLateEnabled
                    ? findSession.get('lateLabel', '') !== null
                      ? `(${findSession.get('lateLabel', '')})`
                      : ''
                    : ''
                }`,
              },
            ],
          };

          if (findSession.get('tardisId', null)) {
            formatText.richText.push({
              font: { color: { argb: '000000' } },
              text: ` (${jsUcfirstAll(findSession.getIn(['tardisId', 'name'], ''))})`,
            });
          }
          return formatText;
        }
      }
    }
    return '';
  }

  const filter = userList
    .map((item, index) => {
      let result = [
        index + 1,
        `${jsUcfirstAll(
          item.getIn([viewType === 'student' ? 'name' : 'staff_name', 'first'], '')
        )} ${jsUcfirstAll(
          item.getIn([viewType === 'student' ? 'name' : 'staff_name', 'middle'], '')
        )} ${jsUcfirstAll(
          item.getIn([viewType === 'student' ? 'name' : 'staff_name', 'last'], '')
        )}`,
        hasUserId ? item.get('academic_no', '') : '-',
        hasGender ? item.get('gender', '') : '-',
        // [
        //   ...item
        //     .get('session_details', List())
        //     .map((schedule) =>
        //       schedule.get('group_status') === false
        //         ? '---'
        //         : !schedule.get('isActive')
        //         ? ''
        //         : getAttendanceStatus(schedule.get('attendance', ''))
        //     ),
        // ],
        [
          ...headerFooterData.map((schedule) =>
            returnStudentStatus(
              viewType === 'student' ? item.get('_id', '') : item.get('_staff_id', ''),
              schedule.get('schedule_id', '')
            )
          ),
        ],
      ];

      if (tableFilter.includes('monthly') && month !== '') {
        result.push(trimFractionDigits(item.getIn(['currentMonth', 'percentage'], 0)));
      }
      if (tableFilter.includes('overall')) {
        result.push(trimFractionDigits(item.getIn(['overall', 'percentage'], 0)));
      }
      return result.flat();
    })
    .toJS();

  function getHeaderFooterData() {
    return studentGroup.getIn([0, `${viewType}_header`], List());
  }
  const workbook = new ExcelJS.Workbook();
  let ws = workbook.addWorksheet('Attendance Log');
  ws.columns = getColumnHeader()['array'];
  // ws.duplicateRow(1, 7, true);
  ws.mergeCells('B1:B3');
  ws.getCell('B1').value = `${getEnvCollegeName()}`;
  ws.getCell('B1').alignment = { vertical: 'middle', horizontal: 'center' };
  ws.getCell('B1').font = {
    bold: true,
    size: 20,
    color: { argb: 'FFFFFF' },
  };
  ws.getCell('B1').fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '318fd5' },
  };

  ws.getRow(4).values = [
    `Academic Year`,
    `${activeInstitutionCalendar.get('calendar_name')} (${moment(
      new Date(activeInstitutionCalendar.get('start_date'))
    ).format(`MMM Do, YYYY`)} - ${moment(
      new Date(activeInstitutionCalendar.get('end_date'))
    ).format(`MMM Do, YYYY`)})`,
  ];

  ws.getRow(5).values = [
    `Program/Term/Year/Level`,
    `${courseDetails.get('program_name', '')}/${jsUcfirstAll(
      courseDetails.get('term', '')
    )}/Year${courseDetails.get('year', '').replace('year', ' ')}/${courseDetails.get('level', '')}`,
    '',
    '',
    '',
    'P - Present',
  ];
  ws.getRow(6).values = [
    `Course Code / Name`,
    `${courseDetails.get('course_no', '')} - ${courseDetails.get(
      'course_name',
      ''
    )}${getVersionName(courseDetails)}`,
    '',
    '',
    '',
    'L - Leave',
  ];
  ws.getRow(7).values = [`Student group`, getStudentGroup(), '', '', '', 'A - Absent'];
  ws.getRow(8).values = [
    `Exported Duration`,
    excelDuration(courseDetails, month),
    '',
    '',
    '',
    'E - Exclude',
  ];
  ws.getRow(9).values = [
    `Report Name`,
    `${jsUcfirstAll(viewType)} Attendance Log`,
    '',
    '',
    '',
    'If Empty Cell - Session Missed/Pending',
  ];
  ws.getRow(10).values = [
    `Exported Date & Time`,
    moment(new Date()).format('DD-MMM-YYYY & H:mm:ss'),
  ];
  ws.getRow(11).values = [`Exported by`, exportBy];
  ws.getRow(12).values = [
    `Total ${viewType === 'student' ? 'Students' : 'Faculties'}`,
    userList.size,
  ];
  ws.getCell('B12').alignment = { vertical: 'top', horizontal: 'left' };
  ws.getRow(14).values = headerOnly;
  ws.addRows(filter);

  const rowHeight = 25;
  ws.eachRow((row) => {
    row.height = rowHeight;
  });

  const backgroundColor = '318fd5';

  const targetRow = ws.getRow(14);
  for (let i = 1; i <= headerOnly.length; i++) {
    const cell = targetRow.getCell(i);
    // Set the background fill color for the target column
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: backgroundColor },
    };
    cell.font = {
      color: { argb: 'FFFFFF' }, // Red color
    };

    cell.alignment = { vertical: 'top', horizontal: 'left' };
  }

  tableFilter.includes('attendance_summary') && getSummaryRow(ws);
  function getSummaryRow(ws) {
    function getSummary() {
      const array = [
        'Session Wise Attendance Present %',
        viewType === 'student' ? 'Students Present' : 'Faculties Present',
        viewType === 'student' ? 'Students Absent' : 'Faculties Absent',
        viewType === 'student' ? 'Students on Permission' : 'Faculties on Permission',
        viewType === 'student' ? 'Students on Leave' : 'Faculties on Leave',
        viewType === 'student' ? 'Students on OnDuty' : 'Faculties on OnDuty',
        viewType === 'student' && isLateEnabled ? 'Students on Late' : '',
      ].map((item, index) =>
        [
          index === 0 ? 'Summary' : '',
          '',
          item,
          '',
          [
            ...headerFooterData
              .map((schedule) =>
                !schedule.get('isActive')
                  ? 'Cancelled'
                  : schedule.get('schedule_status') === 'pending'
                  ? 'Pending'
                  : schedule.get('schedule_status') === 'missed'
                  ? 'Missed'
                  : index === 0
                  ? `${trimFractionDigits(schedule.getIn(['summary', 'percentage'], 0), 2)}%`
                  : index === 1
                  ? `${schedule.getIn(['summary', 'present'], 0)}`
                  : index === 2
                  ? `${schedule.getIn(['summary', 'absent'], 0)}`
                  : index === 3
                  ? `${schedule.getIn(['summary', 'permission'], 0)}`
                  : index === 4
                  ? `${schedule.getIn(['summary', 'leave'], 0)}`
                  : index === 5
                  ? `${schedule.getIn(['summary', 'onduty'], 0)}`
                  : index === 6 && isLateEnabled
                  ? `${schedule.getIn(['summary', 'studentLateAbsent'], 0)}`
                  : ''
              )
              .toJS(),
          ],
        ].flat()
      );
      return array;
    }
    ws.addRows(getSummary());
    const summaryCount = filter.length + 15;
    ws.mergeCells('A' + summaryCount, 'B' + (summaryCount + 5));
    ws.getCell('A' + summaryCount).alignment = {
      vertical: 'middle',
      horizontal: 'center',
    };
    ws.getCell('A' + summaryCount).font = {
      bold: true,
      size: 20,
    };

    const checkAbsentEnabled = isLateEnabled ? 7 : 6;

    for (let i = 0; i < checkAbsentEnabled; i++) {
      const count = summaryCount + i;
      ws.mergeCells('C' + count, 'D' + count);
      ws.getCell('C' + count).alignment = {
        vertical: 'middle',
        horizontal: 'center',
      };
      ws.getCell('C' + count).font = {
        bold: true,
      };
    }

    const targetRow1 = ws.getRow(summaryCount);
    const targetRow2 = ws.getRow(summaryCount + 1);
    const targetRow3 = ws.getRow(summaryCount + 2);
    const targetRow4 = ws.getRow(summaryCount + 3);
    const targetRow5 = ws.getRow(summaryCount + 4);
    const targetRow6 = ws.getRow(summaryCount + 5);
    const targetRow7 = isLateEnabled ? ws.getRow(summaryCount + 6) : '';
    for (let i = 1; i <= headerOnly.length; i++) {
      const cell = targetRow1.getCell(i);
      // Set the background fill color for the target column
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };

      const cell1 = targetRow2.getCell(i);
      // Set the background fill color for the target column
      cell1.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };

      const cell2 = targetRow3.getCell(i);
      // Set the background fill color for the target column
      cell2.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };

      const cell3 = targetRow4.getCell(i);
      // Set the background fill color for the target column
      cell3.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };

      const cell4 = targetRow5.getCell(i);
      // Set the background fill color for the target column
      cell4.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };

      const cell5 = targetRow6.getCell(i);
      // Set the background fill color for the target column
      cell5.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'D9D9D9' },
      };
      if (isLateEnabled) {
        const cell6 = targetRow7.getCell(i);
        // Set the background fill color for the target column
        cell6.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D9D9D9' },
        };
      }
    }
  }

  for (let j = 15; j < filter.length + 15; j++) {
    let targetRowP = ws.getRow(j);
    for (let i = 5; i <= headerOnly.length; i++) {
      const cell = targetRowP.getCell(i);
      if (['P', 'OD', 'PER'].includes(cell.value)) {
        cell.font = {
          color: { argb: '00B050' }, // Green color
        };
      } else if (['A', 'L'].includes(cell.value)) {
        cell.font = {
          color: { argb: 'FF0000' }, // Red color
        };
      } else if (cell.value === 'E') {
        cell.font = {
          color: { argb: 'D97706' }, // Red color
        };
      }
    }
  }

  /* Second Sheet */
  if (!sheet2Data.isEmpty()) {
    const ws2 = workbook.addWorksheet('Session Wise Individual Reports');
    ws2.mergeCells('A1:A2');
    ws2.getCell('A1').value = `S.No.`;
    ws2.getCell('A1').font = {
      bold: true,
      color: { argb: 'FFFFFF' },
    };
    ws2.getCell('A1').fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '318fd5' },
    };
    ws2.mergeCells('B1:B2');
    ws2.getCell('B1').value = `Student Name`;

    ws2.getCell('B1').font = {
      bold: true,
      color: { argb: 'FFFFFF' },
    };
    ws2.getCell('B1').fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '318fd5' },
    };

    const tR1 = ws2.getRow(1);
    const tR2 = ws2.getRow(2);

    const sessions = sheet2Data.get('sessions', List());
    const sessionArray = [];
    let z = 3;
    let k = 0;
    const arrayWidth = [{ width: 7 }, { width: 50 }];
    for (let i = 0; i < sessions.size; i++) {
      const first = i === 0 ? 3 : k + i;
      const cell = tR1.getCell(first);
      const sessionModeSize = sessions.getIn([i, 'mode'], List()).size;
      const mergeCell = tR1.getCell(first + sessionModeSize - 1);
      ws2.mergeCells(`${cell._address}:${mergeCell._address}`);
      k = first + sessionModeSize - 1 - i;
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: backgroundColor },
      };
      cell.font = {
        color: { argb: 'FFFFFF' },
        bold: true,
      };

      cell.value = `${studentGroupRename(sessions.getIn([i, 'session'], ''), programId)} ${
        sessions.getIn([i, 'schedule_date'], '') !== ''
          ? `(${moment(sessions.getIn([i, 'schedule_date'], new Date())).format('DD-MMM-YYYY')})`
          : ``
      }`;
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
      for (let j = 0; j < sessionModeSize; j++) {
        const cell1 = tR2.getCell(z);
        cell1.value = `${jsUcfirstAll(sessions.getIn([i, 'mode', j, 'modeBy'], ''))} ${
          sessions.getIn([i, 'mode', j, 'startTime'], '') !== ''
            ? `(${moment(sessions.getIn([i, 'mode', j, 'startTime'], new Date())).format(
                'h:mm A'
              )})`
            : ``
        }`;
        cell1.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: backgroundColor },
        };
        cell1.font = {
          color: { argb: 'FFFFFF' },
          bold: true,
        };
        cell1.alignment = { vertical: 'middle', horizontal: 'center' };
        z++;
        sessionArray.push({
          sessionData: sessions.get(i),
          modeData: sessions.getIn([i, 'mode', j], Map()),
        });
      }
      arrayWidth.push({ width: 30 });
    }
    ws2.columns = arrayWidth;
    const studentSessionArray = fromJS(sessionArray);
    const studentArray = sheet2Data
      .get('students', List())
      .map((item, index) => {
        const name =
          jsUcfirstAll(item.getIn(['name', 'first'], '')) +
          ' ' +
          jsUcfirstAll(item.getIn(['name', 'middle'], '')) +
          ' ' +
          jsUcfirstAll(item.getIn(['name', 'last'], ''));
        let result = [
          index + 1,
          name,
          [...studentSessionArray.map((session) => getStudentAttendance(session, item))],
        ];
        return result.flat();
      })
      .toJS();

    ws2.addRows(studentArray);
    ws2.eachRow((row) => {
      row.height = rowHeight;
    });
  }

  function getStudentAttendance(session, item) {
    const isActive = session.getIn(['sessionData', 'isActive'], false);
    const scheduleStatus = session.getIn(['sessionData', 'schedule_status'], 'pending');
    const scheduleId = session.getIn(['sessionData', 'schedule_id'], '');
    const modeId = session.getIn(['modeData', '_id'], '');
    const modeBy = session.getIn(['modeData', 'modeBy'], '');

    switch (isActive) {
      case true: {
        switch (scheduleStatus) {
          case 'completed': {
            const filteredSession = item
              .get('session_details', List())
              .find((val) => val.get('schedule_id', '') === scheduleId);
            if (filteredSession !== undefined) {
              if (filteredSession.get('group_status', false)) {
                const attendanceList = filteredSession.get('attendance', List());
                return attendanceList
                  .filter((fs) => fs.get('mode', '') === modeBy && fs.get('_id', '') === modeId)
                  .reduce(
                    (_, el) =>
                      el.get('status', '') === 'absent'
                        ? '-'
                        : el.get('time', '') !== ''
                        ? moment(el.get('time', new Date())).format('h:mm A')
                        : jsUcfirstAll(el.get('status', '')),
                    '-'
                  );
              } else {
                return '-';
              }
            } else {
              return '';
            }
          }
          default:
            return jsUcfirstAll(scheduleStatus);
        }
      }
      case false: {
        return 'Cancelled';
      }
      default:
        return '';
    }
  }
  /* Second Sheet */

  function toDataURL(src, callback, outputFormat) {
    let image = new Image();

    image.crossOrigin = 'Anonymous';
    image.onload = function () {
      let canvas = document.createElement('canvas');
      let ctx = canvas.getContext('2d');
      let dataURL;
      canvas.height = this.naturalHeight;
      canvas.width = this.naturalWidth;
      ctx.drawImage(this, 0, 0);
      dataURL = canvas.toDataURL(outputFormat);
      callback(dataURL);
    };
    image.src = src;
    if (image.complete || image.complete === undefined) {
      image.src = 'data:image/gif;base64, R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==';
      image.src = src;
    }
  }
  const imageSource = getCollegeLogo();
  toDataURL(imageSource, function (dataUrl) {
    const imageId2 = workbook.addImage({
      base64: dataUrl,
      extension: 'jpeg',
    });

    ws.addImage(imageId2, {
      tl: { col: 0, row: 0 },
      br: { col: 1, row: 3 },
      editAs: 'absolute',
    });
    workbook.xlsx
      .writeBuffer()
      .then((buffer) =>
        saveAs(
          new Blob([buffer]),
          `${`${jsUcfirstAll(viewType)}_attendance_log_report_${moment().format(
            'DD_MM_YYYY'
          )}`}.xlsx`
        )
      )
      .catch((err) => console.log('Error writing excel export', err)); //eslint-disable-line
  });
};
