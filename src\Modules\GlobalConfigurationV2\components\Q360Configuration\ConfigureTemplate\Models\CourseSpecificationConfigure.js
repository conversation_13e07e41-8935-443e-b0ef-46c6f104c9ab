import React from 'react';
import PropTypes from 'prop-types';
import { Map as IMap, fromJS } from 'immutable';

import {
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  FormControl,
  TextField,
  InputAdornment,
  Badge,
  Tooltip,
} from '@mui/material';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Button as MuiButton } from '@mui/material';

import {
  getSingleConfig,
  updateCategoryConfigure,
  resetFormCourseSetting,
  setData,
} from '_reduxapi/q360/actions';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import MaterialInput from 'Widgets/FormElements/material/Input';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import Button from 'Widgets/FormElements/material/Button';
import {
  useBooleanHook,
  useCallApiHook,
  useNestedHook,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { useCurrentPage } from '../..';
import { getChanges } from 'Modules/GlobalConfigurationV2/utils/jsUtils';
import { ConfirmationPopup } from 'Modules/GlobalConfigurationV2/utils/uiUtils';
import { t } from 'i18next';

export const value_label_qlc = {
  institution: 'Institution Level',
  program: 'Program Level',
  course: 'Course Level',
};
//----------------------------------Utils Start-----------------------------------------------------
const boxSize = {
  '& .MuiSvgIcon-root': {
    width: '17px',
    height: '17px',
  },
};

const outlinedNoneImportant = {
  '& .MuiOutlinedInput-root': {
    '& fieldset': {
      fontSize: '12px',
      border: 'none !important',
    },
  },
  '&.MuiTextField-root': {
    fontSize: '12px',
    border: '.5px solid #D1D5DB !important',
    borderRadius: '4px',
  },
  '& .Mui-disabled': {
    fontSize: '12px',
    backgroundColor: '#ffffff',
  },
};
const categoryForStatic = fromJS([
  {
    name: 'Annual',
    value: 'annual',
  },
  {
    name: 'Periodic',
    value: 'periodic',
  },
]);

export const baseOptions = {
  academicTerms: {
    label: 'Academic Term',
    value: 'academicTerms',
    toolTip: 'academicTermTooltip',
  },
  attemptType: { label: 'Attempt Type', value: 'attemptType', toolTip: 'attemptTypeTooltip' },
  everyAcademic: {
    label: 'Every Academic Calendar',
    value: 'everyAcademic',
    toolTip: 'academicCalendarTooltip',
  },
  occurrenceConfiguration: {
    label: 'Occurrence Configuration',
    value: 'occurrenceConfiguration',
    toolTip: 'occurrenceTooltip',
  },
  incorporateMandatory: {
    label: 'Incorporate',
    value: 'incorporateMandatory',
    toolTip: 'incorporateTooltip',
  },
  displayMandatory: {
    label: 'Display Capture',
    value: 'displayMandatory',
    toolTip: 'displayCaptureTooltip',
  },
  studentGroups: {
    label: 'Student Groups',
    value: 'studentGroups',
    toolTip: 'studentGroupTooltip',
  },
};

const checkBoxAction = {
  course: [
    baseOptions.academicTerms,
    baseOptions.studentGroups,
    baseOptions.attemptType,
    baseOptions.everyAcademic,
    baseOptions.occurrenceConfiguration,
    baseOptions.incorporateMandatory,
    baseOptions.displayMandatory,
  ],
  program: [
    baseOptions.academicTerms,
    baseOptions.attemptType,
    baseOptions.everyAcademic,
    baseOptions.occurrenceConfiguration,
    baseOptions.incorporateMandatory,
    baseOptions.displayMandatory,
  ],
  institution: [
    baseOptions.everyAcademic,
    baseOptions.occurrenceConfiguration,
    baseOptions.incorporateMandatory,
    baseOptions.displayMandatory,
  ],
};

const periodicSx = {
  '& .MuiOutlinedInput-root': {
    '&.Mui-focused': {
      '& .MuiOutlinedInput-notchedOutline': {
        border: '1px solid grey',
      },
    },
  },
  width: '100px',
};

function checkIsDisable(isDefault) {
  return {
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        border: isDefault ? 'none !important' : '.5px solid #D1D5DB !important',
      },
    },
    '& input': {
      color: '#374151 !important',
      textTransform: 'capitalize',
      backgroundColor: isDefault ? '#E5E7EB' : 'white',
      borderRadius: '4px',
    },
    '& .Mui-disabled': {
      '-webkit-text-fill-color': 'inherit !important',
      backgroundColor: '#E5E7EB',
    },
  };
}
//----------------------------------Utils End-------------------------------------------------------

const CourseSpecificationConfigure = ({ open, handleClose, category, isEdit, setIsEdit }) => {
  const [state, setState, handleChangeDeepNested] = useNestedHook(category);
  const categoryName = state.get('categoryName', '');
  const staticCategoryName = category.get('categoryName', '');
  const isDefault = state.get('isDefault', false);
  const level = state.get('level', 'institution');
  const describe = state.get('describe', '');
  const categoryFor = state.get('categoryFor', '');
  const periodicInterval = state.get('periodicInterval', 0);
  const isConfigure = state.get('isConfigure', false);
  const categoryId = state.get('_id', '');
  const actions = state.get('actions', IMap());
  const categoryActions = category.get('actions', IMap());
  const positionIndex = useCurrentPage('currentCategoryIndex');
  const [updateConfigure] = useCallApiHook(updateCategoryConfigure);
  const [updateSingleConfigure] = useCallApiHook(getSingleConfig);
  const [isOpen, setOpen] = useBooleanHook(false);
  const [resetApi] = useCallApiHook(resetFormCourseSetting);
  const [message] = useCallApiHook(setData);
  const setMessage = (text) => message(fromJS({ message: text }));
  const callBack = (statusCode) => {
    if (statusCode === 409) return setMessage('Category name already exists');
    updateSingleConfigure(categoryId, positionIndex, handleClose);
  };
  const onSave = () => {
    let changes = getChanges(category.toJS(), state.toJS());
    delete changes.createdBy;
    let updateState = { ...changes, categoryId, isConfigure: true };
    updateConfigure(updateState, callBack);
  };
  const canEdit = () => isEdit && setIsEdit();
  const checkDisable = {
    color: isEdit === true && isConfigure ? '#6B7280 !important' : '',
    '&.Mui-checked': {
      color: isEdit === true && isConfigure ? '#6B7280 !important' : '',
    },
  };
  const isPeriodicInterval = {
    value: periodicInterval,
    type: 'periodic',
    errorMessage: 'Enter Periodic Number Above 0',
  };
  const inputConstruction = [
    { value: categoryName, type: 'category', errorMessage: 'Enter Category Name' },
  ];
  if (categoryFor === 'periodic') inputConstruction.push(isPeriodicInterval);
  const handleValidate = (inputs, validate = false) => {
    const actionsChanges = getChanges(
      actions.delete('incorporateMandatory').delete('displayMandatory').toJS(),
      categoryActions.delete('incorporateMandatory').delete('displayMandatory').toJS()
    );
    if (isConfigure && Object.keys(actionsChanges).length && validate) return setOpen();
    let hasError = fromJS({});
    inputs.forEach((input) => {
      const inputType = input.type;
      const inputValue = input.value;
      const errorMessage = input.errorMessage;
      if (!inputValue) {
        hasError = hasError.set(inputType, inputValue);
        setMessage(errorMessage);
      }
    });
    if (hasError.size === 0) onSave();
  };
  const handleResit = () => resetApi(categoryId, handleValidate(inputConstruction, false));
  const handleChangeCategoryFor = (key, value) => {
    setState(key, value);
  };

  return (
    <DialogModal show={open} onClose={handleClose} maxWidth={'sm'} fullWidth={true}>
      <div className="p-4 ml-2">
        <div className="d-flex align-items-center pb-2">
          <div className="q360-popup-Header text-capitalize">
            {staticCategoryName} {`${t('q360SettingDashboard.categoryConfigure.configure')}`}
          </div>
          <div className="ml-auto">
            <EnableOrDisable valid={isEdit && isConfigure}>
              <ModeEditIcon className="ml-auto cursor-pointer" onClick={canEdit} />
            </EnableOrDisable>
          </div>
        </div>
        <div className="popup-q360-overflow p-0 pt-2 pr-2">
          <div>
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              label={
                <div className="f-12 fw-400 text-mGrey">{`${t(
                  'q360SettingDashboard.categoryConfigure.categoryName'
                )}`}</div>
              }
              value={categoryName}
              changed={(event) => setState('categoryName', event.target.value.toLowerCase())}
              disabled={isDefault ? isDefault : isEdit && isConfigure}
              sx={checkIsDisable(isDefault)}
            />
          </div>
          <label className="m-0 f-12 fw-400 text-mGrey mt-2 ">{`${t(
            'q360SettingDashboard.categoryConfigure.level'
          )}`}</label>
          <div className="d-flex ml-1 pb-2">
            {isDefault ? (
              <FormControl disabled={isDefault || (isEdit && isConfigure)}>
                <FormControlLabel
                  className="m-0 p-0 d-flex align-items-center pt-1 gap-2 "
                  value={level}
                  checked={true}
                  control={
                    <Radio size="small" className="p-0 pr-2" sx={{ ...checkDisable, ...boxSize }} />
                  }
                  label={value_label_qlc[level]}
                />
              </FormControl>
            ) : (
              <FormControl disabled={isDefault || (isEdit && isConfigure)}>
                <RadioGroup
                  row
                  value={level}
                  onChange={(event) => {
                    setState('level', event.target.value);
                    setState('actions', IMap());
                  }}
                >
                  {fromJS(value_label_qlc)
                    .entrySeq()
                    .map(([key, value], index) => (
                      <FormControlLabel
                        value={key}
                        key={key}
                        control={<Radio size="small" sx={{ ...checkDisable, ...boxSize }} />}
                        label={<div className="text-dark">{value}</div>}
                      />
                    ))}
                </RadioGroup>
              </FormControl>
            )}
          </div>
          <div>
            <MaterialInput
              elementType={'materialTextArea'}
              type={'text'}
              disabled={isEdit && isConfigure}
              variant={'outlined'}
              changed={(event) => setState('describe', event.target.value)}
              value={describe}
              label={
                <div className="f-12 fw-400 text-mGrey">{`${t(
                  'q360SettingDashboard.categoryConfigure.describeOptional'
                )}`}</div>
              }
              placeholder={`${t('q360SettingDashboard.categoryConfigure.describeHere')}`}
              maxRows={'4'}
              minRows={'4'}
              bgWhite={isEdit && isConfigure === true ? false : true}
              sx={isEdit === true ? outlinedNoneImportant : ''}
            />
          </div>
          <div className="mt-2 ml-1 d-flex align-items-center">
            <FormControl disabled={isEdit && isConfigure}>
              <div className="f-12 fw-400 text-mGrey mt-2">{`${t(
                'q360SettingDashboard.categoryConfigure.categoryFor'
              )}`}</div>
              <RadioGroup
                row
                value={categoryFor}
                onChange={(event) => handleChangeCategoryFor('categoryFor', event.target.value)}
              >
                {categoryForStatic.map((option) => (
                  <FormControlLabel
                    value={option.get('value', '')}
                    key={option.get('value', '')}
                    control={
                      <Radio
                        size="small"
                        sx={{
                          ...checkDisable,
                          ...boxSize,
                        }}
                      />
                    }
                    label={<div className="text-dark">{option.get('name', '')}</div>}
                  />
                ))}
              </RadioGroup>
            </FormControl>
            {categoryFor === 'periodic' && (
              <TextField
                placeholder={`${t('q360SettingDashboard.categoryConfigure.periodicInterval')}`}
                size="small"
                className="mt-3"
                value={periodicInterval}
                onChange={(event) => setState('periodicInterval', Number(event.target.value))}
                sx={periodicSx}
                disabled={isEdit && isConfigure}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <div>Years</div>
                    </InputAdornment>
                  ),
                }}
              />
            )}
          </div>
          <div className="w-100 ">
            <div className="m-0 f-12 fw-400 text-mGrey  pb-2">
              {`${t('q360SettingDashboard.categoryConfigure.selectCheckBox')}`}
            </div>
          </div>
          {checkBoxAction[level].map((action, actionIndex) => (
            <div className={`d-flex align-items-center ml-1`} key={actionIndex}>
              <div>
                <Checkbox
                  disabled={isEdit && isConfigure}
                  onClick={(event) =>
                    handleChangeDeepNested(['actions', action.value], event.target.checked)
                  }
                  size="small"
                  checked={state.getIn(['actions', action.value], false)}
                  className="pl-0 py-2"
                  sx={{ ...checkDisable, ...boxSize }}
                />
              </div>
              <div className="d-flex align-items-center">
                <div className="pr-2">{action.label}</div>
                <Tooltip title={`${t(`q360SettingDashboard.categoryConfigure.${action.toolTip}`)}`}>
                  <InfoOutlinedIcon
                    className={'text-lGrey'}
                    fontSize="small"
                    sx={{ fontSize: '15px' }}
                  />
                </Tooltip>
              </div>
            </div>
          ))}
        </div>
        <div className="d-flex align-items-center pt-3">
          <div className="d-flex align-items-center ml-auto gap-20">
            <div>
              <Button
                clicked={handleClose}
                variant="outlined"
                className="px-4"
                size={'small'}
                color={'gray'}
              >
                Cancel
              </Button>
            </div>
            <div>
              <MuiButton
                onClick={() => handleValidate(inputConstruction, true)}
                sx={{
                  backgroundColor: '#147AFC !important',
                  color: '#FFFFFF',
                  padding: '4px 30px',
                }}
              >
                Save
              </MuiButton>
            </div>
          </div>
        </div>
      </div>
      <ConfirmationPopup isOpen={isOpen} handleClose={() => setOpen()}>
        <div>
          <Badge
            sx={{
              margin: '0 10px 0 15px',
              '& .MuiBadge-badge': {
                backgroundColor: '#FFFBEB',
                color: '#EF4444',
                padding: '20px',
                height: '28px',
                borderRadius: '50%',
                width: '29px',
              },
            }}
            badgeContent={
              <InfoOutlinedIcon sx={{ color: '#F59E0B', transform: 'rotate(180deg)' }} />
            }
          />
          <span className="px-4">{staticCategoryName}</span>
        </div>
        <div>*A clear message highlighting the potential consequences or risks</div>
        <div></div>
        <div>
          <Button
            variant="outlined"
            clicked={() => setOpen()}
            className="text-capitalize px-4 text-secondary border-secondary bold mx-2"
          >
            Cancel
          </Button>
          <Button
            className="text-capitalize px-4 bg-warning text-white "
            variant="contained"
            clicked={handleResit}
          >
            Continue
          </Button>
        </div>
      </ConfirmationPopup>
    </DialogModal>
  );
};

CourseSpecificationConfigure.propTypes = {
  open: PropTypes.bool,
  isEdit: PropTypes.bool,
  handleClose: PropTypes.func,
  category: PropTypes.instanceOf(IMap),
};

export default CourseSpecificationConfigure;
