import React from 'react';
import { Form } from 'react-bootstrap';
import { List } from 'immutable';
import PropTypes, { oneOfType } from 'prop-types';
import useWindowDimensions from '../../../_components/UI/PageSize/PageSize';
import { Trans, useTranslation } from 'react-i18next';
import { getLang, indVerRename, getEnvLabelChanged, capitalize } from '../../../utils';

const lang = getLang();
function ActionsList({ activeModules, checkboxUpdate, editRoles, checkAllAction, checkAll }) {
  const { t } = useTranslation();
  let modulesLength = activeModules.size;
  const { height } = useWindowDimensions();
  const minHeight = height - 292;
  return (
    <div className="roles_shadow p-3  bg-white rounded">
      <div className="border-bottom d-flex justify-content-between mb-4">
        <p className="f-18 font-weight-bold">
          <Trans i18nKey={'role_management.actions'}> Actions</Trans>{' '}
        </p>
        {editRoles && (
          <Form.Check type="checkbox" checked={checkAll} onChange={(e) => checkAllAction(e)} />
        )}
      </div>
      <div
        className="roles_height"
        style={{ maxHeight: minHeight + 'px', minHeight: minHeight + 'px' }}
      >
        {activeModules &&
          activeModules.size > 0 &&
          activeModules.map((module, index) => {
            let pageActions = module
              .get('pages', List())
              .filter((item) => item.get('selected', false) === true)
              .map((page) => {
                return (
                  page.get('actions', List()) !== false &&
                  page.get('actions', List()).map((action, actionIndex) => {
                    return (
                      <div className="roles_checkbox_space text-left" key={actionIndex}>
                        {actionIndex === 0 && (
                          <>
                            <h6
                              style={{ color: '#cdcecd' }}
                              {...(lang !== 'en' && { className: 'text-left' })}
                            >
                              {getEnvLabelChanged() && page.get('name', '') === 'Onsite' ? (
                                indVerRename('Onsite')
                              ) : (
                                <Trans
                                  i18nKey={`role_management.modules_list.${page.get('name', '')}`}
                                ></Trans>
                              )}
                            </h6>
                            {/* <>
                            {editRoles && (
                              <Form.Check
                                style={{ float: 'right', top: '-1.8em' }}
                                type="checkbox"
                                checked={checkAll}
                                onChange={(e) => checkAllAction(e)}
                              />
                            )}
                          </> */}
                          </>
                        )}
                        <Form.Check
                          type="checkbox"
                          label={t(
                            `role_management.role_actions.${
                              getEnvLabelChanged() && action.get('name', '') === 'PLO Bar Graph'
                                ? `${capitalize(indVerRename('plo'))} Bar Graph`
                                : action.get('name', '')
                            }`
                          )}
                          checked={action.get('selected', false)}
                          onChange={
                            editRoles
                              ? (e) => checkboxUpdate(e, 'page', action.get('_id'))
                              : () => {}
                          }
                          disabled={!editRoles}
                        />
                      </div>
                    );
                  })
                );
              });

            let tabActions = module
              .get('pages', List())
              .filter((item) => item.get('selected', false) === true)
              .map((page) => {
                return (
                  page.get('tabs', List()) !== false &&
                  page
                    .get('tabs', List())
                    .filter((item) => item.get('selected', false) === true)
                    .map((item) => {
                      return (
                        item.get('actions') !== false &&
                        item.get('actions', List()).map((action, actionIndex) => {
                          return (
                            <React.Fragment key={actionIndex}>
                              <div className="roles_checkbox_space text-left">
                                {actionIndex === 0 && (
                                  <div className="pt-1 pb-1">
                                    <hr />
                                  </div>
                                )}
                                {actionIndex === 0 && (
                                  <h6
                                    style={{ color: '#cdcecd' }}
                                    {...(lang !== 'en' && { className: 'text-left' })}
                                  >
                                    <Trans
                                      i18nKey={`role_management.tabs.${item.get('name', '')}`}
                                    ></Trans>
                                  </h6>
                                )}
                                <Form.Check
                                  type="checkbox"
                                  label={t(
                                    `role_management.role_actions.${action.get('name', '')}`
                                  )}
                                  checked={action.get('selected', false)}
                                  onChange={
                                    editRoles
                                      ? (e) => checkboxUpdate(e, 'tab', action.get('_id'))
                                      : () => {}
                                  }
                                  disabled={!editRoles}
                                />
                              </div>
                            </React.Fragment>
                          );
                        })
                      );
                    })
                );
              });

            let subTabActions = module
              .get('pages', List())
              .filter((item) => item.get('selected', false) === true)
              .map((page) => {
                return (
                  page.get('tabs', List()) !== false &&
                  page
                    .get('tabs', List())
                    .filter((item) => item.get('selected', false) === true)
                    .map((item) => {
                      if (item.get('subTabs', List()).size > 0) {
                        return item
                          .get('subTabs', List())
                          .filter((item) => item.get('selected', false) === true)
                          .map((subTab, subTabIndex) => {
                            return subTab.get('actions', List()).map((action, actionIndex) => {
                              return (
                                <React.Fragment key={actionIndex}>
                                  <div className="roles_checkbox_space text-left">
                                    {actionIndex === 0 && (
                                      <div className="pt-1 pb-1">
                                        <hr />
                                      </div>
                                    )}
                                    {actionIndex === 0 && (
                                      <h6
                                        style={{ color: '#cdcecd' }}
                                        {...(lang !== 'en' && { className: 'text-left' })}
                                      >
                                        {' '}
                                        <Trans
                                          i18nKey={`role_management.sub_tabs.${subTab.get(
                                            'name',
                                            ''
                                          )}`}
                                        ></Trans>
                                      </h6>
                                    )}
                                    <Form.Check
                                      type="checkbox"
                                      label={t(
                                        `role_management.role_actions.${action.get('name', '')}`
                                      )}
                                      checked={action.get('selected', false)}
                                      onChange={
                                        editRoles
                                          ? (e) => checkboxUpdate(e, 'subTab', action.get('_id'))
                                          : () => {}
                                      }
                                      disabled={!editRoles}
                                    />
                                  </div>
                                </React.Fragment>
                              );
                            });
                          });
                      } else {
                        return [];
                      }
                    })
                );
              });

            return (
              <React.Fragment key={index}>
                {pageActions}
                {tabActions}
                {subTabActions}
                {modulesLength - 1 !== index && (
                  <div className="pt-1 pb-1">
                    <hr />
                  </div>
                )}
              </React.Fragment>
            );
          })}
      </div>
    </div>
  );
}

ActionsList.propTypes = {
  activeModules: PropTypes.instanceOf(List),
  checkboxUpdate: PropTypes.func,
  checkAllAction: PropTypes.func,
  checkAll: oneOfType([PropTypes.func, PropTypes.bool]),
  editRoles: PropTypes.bool,
};

export default ActionsList;
