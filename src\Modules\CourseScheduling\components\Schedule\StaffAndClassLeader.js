import { Checkbox } from '@mui/material';
import { List } from 'immutable';
import React, { useState } from 'react';
import MaterialInput from 'Widgets/FormElements/material/Input';
import PropTypes from 'prop-types';

function StaffAndClassLeader({ staffs, handleChanges, checkStaff, type, ClassLeader }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredStaffs, setFilteredStaffs] = useState(List());
  const handleSearchData = (event) => {
    const query = event.target.value;
    setSearchQuery(query);
    const filtered = staffs.filter((staff) =>
      staff.get('name', '').toLowerCase().includes(query.toLowerCase())
    );
    setFilteredStaffs(filtered);
  };
  const displayStaffs = searchQuery ? filteredStaffs : staffs;
  return (
    <div>
      <div>
        <MaterialInput
          elementType={'materialSearch'}
          placeholder={'Search'}
          id={'#rowReverse'}
          labelclass={'searchRight'}
          className="remove_hover"
          changed={handleSearchData}
          value={searchQuery}
        />
      </div>
      <div className="staffListClass border-bottom">
        <div className="d-flex">
          <div className="w-100">
            {staffs.size && displayStaffs.size !== 0 ? (
              <>
                {displayStaffs
                  .sort((a, b) => (a.get('name', '') > b.get('name', '') ? 1 : -1))
                  .map((staffList, indexStaff) => {
                    return (
                      <div key={indexStaff} className="d-flex align-items-center border-bottom">
                        <Checkbox
                          size="small"
                          checked={JSON.stringify(
                            type === 'staffs' ? checkStaff : ClassLeader
                          ).includes(staffList.get('_staff_id', ''))}
                          onChange={(e) =>
                            handleChanges(
                              type === 'staffs' ? 'checkStaff' : 'checkclassLeaderData',
                              e.target.checked,
                              staffList
                            )
                          }
                        />
                        <div className="ml-2">{staffList.get('name', '')}</div>
                      </div>
                    );
                  })}
              </>
            ) : (
              <div className="text-center p-4 f-17 w-100">Staffs Not Found </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

StaffAndClassLeader.propTypes = {
  staffs: PropTypes.func,
  handleChanges: PropTypes.func,
  checkStaff: PropTypes.instanceOf(List),
  type: PropTypes.string,
  ClassLeader: PropTypes.instanceOf(List),
};

export default StaffAndClassLeader;
