import React, { useState } from 'react';
import PropTypes from 'prop-types';
// import { Table } from 'react-bootstrap';
// import { useDispatch, useSelector } from 'react-redux';
import { Map, List } from 'immutable';
// import { format, isValid } from 'date-fns';
// import moment from 'moment';
// import SnackBars from 'Modules/Utils/Snackbars';
// import Tooltips from 'Widgets/FormElements/material/Tooltip';
// import InfoIcon from '@mui/icons-material/Info';

import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
// import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
//eslint-disable-next-line
import {
  faceGet,
  // getReRegisterScheduleList,
  // faceGet,
  setData,
  updateReRegisterStatus,
  // updateReRegisterStatus,
} from '_reduxapi/user_management/action';
import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DialogContentText from '@mui/material/DialogContentText';
import Grid from '@mui/material/Grid';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { useDispatch, useSelector } from 'react-redux';

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(2),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

const ReRegisterStudentViewModal = ({
  open,
  userId,
  setOpen,
  studentViewStatus,
  setStudentViewStatus,
  countCallBack,
}) => {
  const [passCode, setPassCode] = useState('');
  const [rejectOpen, setRejectOpen] = useState(false);
  const [reason, setReason] = useState('');
  const [statusUpdated, setStatusUpdated] = useState(false);
  const dispatch = useDispatch();
  const getBiometricFace = (e) => {
    e.preventDefault();
    const data = {
      faceId: '687f325289317eaea4ba0dd1',
      userId,
      passCode,
    };

    const callBack = () => setPassCode('');

    dispatch(faceGet(data, callBack));
  };
  const faceGetData = useSelector(({ userManagement }) => userManagement.get('faceGetData', Map()));
  const statusUpdate = (type = '') => {
    const data = {
      status: type,
      userId,
      faceId: '687f325289317eaea4ba0dd1',
    };
    dispatch(
      updateReRegisterStatus(data, () => {
        setStatusUpdated(true);
        countCallBack();
      })
    );
  };
  const rejectApprove = (e) => {
    e.preventDefault();
    const data = {
      status: 'rejected',
      userId,
      reasonToReject: reason.trim(),
      faceId: '687f325289317eaea4ba0dd1',
    };
    dispatch(
      updateReRegisterStatus(data, () => {
        openRejectModal();
        setStatusUpdated(true);
        countCallBack();
      })
    );
  };
  const openRejectModal = () => {
    setRejectOpen((rejectOpen) => !rejectOpen);
    setReason('');
  };
  return (
    <div>
      {' '}
      {open ? (
        <BootstrapDialog
          maxWidth={faceGetData.isEmpty() ? 'xs' : 'md'}
          fullWidth={true}
          aria-labelledby="customized-dialog-title"
          open={true}
        >
          <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
            {faceGetData.isEmpty() ? 'Passcode' : 'Uploaded Photo'}
          </DialogTitle>
          {CheckPermission(
            'tabs',
            'User Management',
            'Staff Management',
            '',
            'Re-Register Request',
            'Approve/Reject'
          ) &&
            studentViewStatus === 'pending' &&
            !statusUpdated &&
            !faceGetData.isEmpty() && (
              <div style={{ position: 'absolute', right: 0, top: 15 }}>
                <MButton
                  color="success"
                  // disabled={scheduleList.size === 0}
                  className="mr-3"
                  clicked={() => statusUpdate('approved')}
                >
                  Approve
                </MButton>
                <MButton
                  color="error"
                  // disabled={scheduleList.size === 0}
                  className="mr-3"
                  clicked={() => openRejectModal()}
                >
                  Reject
                </MButton>
              </div>
            )}
          <form autoComplete="off">
            <DialogContent dividers>
              {faceGetData.isEmpty() ? (
                <React.Fragment>
                  <DialogContentText>
                    To view the uploaded photo, please enter your passcode here.
                  </DialogContentText>
                  <MaterialInput
                    elementType={'materialInput'}
                    type={passCode === '' ? 'text' : 'password'}
                    variant={'outlined'}
                    size={'small'}
                    labelclass={'mb-1 f-14'}
                    maxLength={10}
                    label={''}
                    placeholder={'Enter Passcode *'}
                    value={passCode}
                    changed={(e) => setPassCode(e.target.value)}
                    inputRef={(input) => input && input.focus()}
                  />{' '}
                </React.Fragment>
              ) : (
                <Grid
                  container
                  rowSpacing={1}
                  columnSpacing={{ xs: 1, sm: 2, md: 3 }}
                  sx={{ overflowY: 'scroll', height: '20em' }}
                >
                  <Grid item xs={6}>
                    <div className="text-center pb-3">Original Photo</div>
                    <img
                      style={{ width: '100%', padding: '2px' }}
                      alt="Old Photo"
                      src={faceGetData.get('userOldPhoto', '')}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <div className="text-center pb-3">New Photo</div>
                    {faceGetData.get('signedURLs', List()).map((signedURL, index) => (
                      <img
                        key={index}
                        style={{ width: '50%', padding: '2px' }}
                        alt="New Photo"
                        src={signedURL}
                      />
                    ))}
                  </Grid>
                </Grid>
              )}
            </DialogContent>
            <DialogActions>
              <MButton
                color="inherit"
                variant="outlined"
                className="mr-3"
                clicked={() => {
                  // openModal();
                  dispatch(setData(Map({ faceGetData: Map() })));
                  // setUserStatus(null);
                  setPassCode('');
                  setOpen(false);
                }}
              >
                <Trans i18nKey={'cancel'}></Trans>
              </MButton>
              {faceGetData.isEmpty() ? (
                <MButton
                  className="mr-3"
                  type="submit"
                  disabled={!(passCode.length > 3)}
                  clicked={(e) => getBiometricFace(e)}
                >
                  Submit
                </MButton>
              ) : (
                ''
              )}
            </DialogActions>
          </form>
        </BootstrapDialog>
      ) : (
        ''
      )}
      {rejectOpen ? (
        <BootstrapDialog
          maxWidth={'xs'}
          fullWidth={true}
          onClose={() => openRejectModal()}
          aria-labelledby="customized-dialog-title"
          open={true}
        >
          <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
            Enter Reason
          </DialogTitle>
          <form autoComplete="off">
            <DialogContent dividers>
              <React.Fragment>
                <DialogContentText>Please enter reject reason here.</DialogContentText>
                <MaterialInput
                  elementType={'materialTextArea'}
                  changed={(e) => setReason(e.target.value)}
                  type={'text'}
                  value={reason}
                  placeholder={'Enter Reason *'}
                  minRows={3}
                  inputProps={{ maxLength: 500 }}
                  inputRef={(input) => input && input.focus()}
                />{' '}
              </React.Fragment>
            </DialogContent>
            <DialogActions>
              <MButton
                color="inherit"
                variant="outlined"
                className="mr-3"
                clicked={() => {
                  openRejectModal();
                }}
              >
                <Trans i18nKey={'cancel'}></Trans>
              </MButton>

              <MButton
                type="submit"
                className="mr-3"
                disabled={reason.trim() === ''}
                clicked={(e) => rejectApprove(e)}
              >
                Reject
              </MButton>
            </DialogActions>
          </form>
        </BootstrapDialog>
      ) : (
        ''
      )}
    </div>
  );
};

export default ReRegisterStudentViewModal;
ReRegisterStudentViewModal.propTypes = {
  open: PropTypes.bool,
  setOpen: PropTypes.func,
  userId: PropTypes.string,
  studentViewStatus: PropTypes.string,
  setStudentViewStatus: PropTypes.func,
  countCallBack: PropTypes.func,
};
