import React, { useEffect, memo, useState } from 'react';
import PropTypes from 'prop-types';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { ucFirst } from '../../../../utils';
import ExcelJS from 'exceljs';
import { exportExcel } from 'Modules/UserManagement/v2/StaffManagement/UserExport';
import ErrorIcon from '@mui/icons-material/Error';
import { Map, List, fromJS } from 'immutable';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import AssessmentDataExport from './AssessmentDataExport';
import OnUploadProgressBar from './UploadProgressBar';
import { FileUpload, FileDownload } from '@mui/icons-material';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { getShortString } from 'Modules/Shared/v2/Configurations';

function FilteredHeader({
  handleAddShow,
  planName,
  totalMarks,
  setDataError,
  outComeLists,
  data,
  setData,
  getStudentList,
  type,
  mode,
  term,
  NeededDataForExport,
  list,
  setList,
  defaultCount,
  checkQuestionMark,
  splittingHeading,
}) {
  const [showProgress, setShowProgress] = useState(false);
  const [fileSizeOn, setFileSizeOn] = useState('');
  const [importPercent, setImportPercent] = useState(0);
  const [fileNameOn, setFileNameOn] = useState('');
  const [uploadFile, setUploadFile] = useState(false);

  useEffect(() => {
    if (mode === 'edit') {
      forceRender();
    }
  }, [mode]);
  const forceRender = () => {
    return setUploadFile((a) => !a);
  };
  const DownloadData = AssessmentDataExport({ NeededDataForExport });
  const handleChangeFile = (event) => {
    let csv = event.files[0];
    let values = new FormData();
    values.append('file', csv);
    const fileName = csv.name;
    const format = fileName.split('.').pop().toLowerCase();

    const callBackForUpload = (studentDetails) => {
      setList(list.set('studentData', studentDetails));
    };

    const updateData = (json) => {
      json.splice(0, 3);
      const filterEmptyData = json.filter((data, index) =>
        index > 3
          ? data['Student Name'] !== '' &&
            data.Student_Id !== '' &&
            data.Gender !== '' &&
            data.Attendance !== ''
          : data
      );
      json = filterEmptyData;
      if (json.length) {
        const array = [];
        const arrayForConstValues = [];
        const arrayForConstValuesFromResult = [];
        //const splicedBody = DownloadData.bodyData.slice(3, data.length);
        const profileDataFromBody = DownloadData.bodyData?.map((item, index) =>
          item.map((eachValue, secIndex) => {
            if (secIndex === 0) {
              array.push(eachValue.value);
            }
            if (index < 3) {
              let value = '';
              if (typeof eachValue === 'number' && eachValue.toString().includes('.')) {
                value = Number(eachValue.toFixed(2));
              } else {
                value = eachValue;
              }
              arrayForConstValues.push(value);
            }
            return '';
          })
        );
        const get3RowValuesFromResult = json?.map((item, firstIndex) => {
          if (firstIndex < 3) {
            return Object.values(item)?.map(
              (value, index) => item.length !== index && arrayForConstValuesFromResult.push(value)
            );
          }
          return '';
        });
        const checkConstValuesSame =
          arrayForConstValues.join('') === arrayForConstValuesFromResult.join('');
        if (!checkConstValuesSame) {
          return setDataError(
            Map({ message: `Don't change values like Total/bench mark/outcome value` })
          );
        }

        const CheckAbsentees = json?.some((item) => {
          const checkAbsentee = item.Attendance?.toString().trim() === 'A';
          return Object.values(item).some((eachUser, index) => {
            if (index > 4 && checkAbsentee) {
              return !['', '-'].includes(eachUser);
            }
            return false;
          });
        });
        if (CheckAbsentees) {
          return setDataError(Map({ message: `Don't give Marks for Absentees ` }));
        }

        const dummyData = () => {
          if (
            Object.values(json[3]).indexOf('FirstName') > -1 ||
            Object.values(json[3]).indexOf('LastName') > -1 ||
            Object.values(json[3]).indexOf('AcademicId') > -1 ||
            Object.values(json[3]).indexOf('Gender') > -1 ||
            Object.values(json[3]).indexOf('P/A') > -1
          ) {
            return true;
          }
        };
        if (dummyData()) {
          json.splice(3, 1);
        }

        let copy = fromJS(json);

        let dups = [];
        const checkDuplicates = () => {
          let academicIds = copy
            .splice(0, 3)
            .toJS()
            .map((item, index) => item['Student_Id'].toString().trim());

          academicIds.forEach((el) => {
            if (academicIds.indexOf(el) !== academicIds.lastIndexOf(el)) {
              dups.push(el);
            }
          });

          return dups.length ? true : false;
        };
        if (checkDuplicates()) {
          let spreadedDups = [...new Set(dups)];
          return setDataError(
            Map({
              message: `Duplicate academic no.s detected ' ${spreadedDups} '`,
            })
          );
        }
        let markArray = [];
        let negativeArray = [];
        const QuestionMarkValues = DownloadData.gettingQuestions?.map((question) => {
          const getValues = json?.map((res) => res[question]);
          const maxValue = getValues[1];
          getValues.map((val, valIndex) => {
            if (valIndex >= 2) {
              if (val > maxValue) {
                const msg = `Maximum enter Mark is ${maxValue} for ${question}`;
                markArray.push(msg);
                return '';
              }
              if (val < 0) {
                const msg = `Don't give negative value ${val} for ${question}`;
                negativeArray.push(msg);
                return '';
              }
              // if (json[valIndex]['Attendance'].toString().trim() === 'P' && isNaN(val)) {
              //   const msg = `Please enter number only at ${question} for present students`;
              //   negativeArray.push(msg);
              //   return '';
              // }
            }
            return '';
          });
          return [...getValues];
        });
        const joiningValues = [profileDataFromBody, get3RowValuesFromResult, QuestionMarkValues]; //eslint-disable-line
        if (markArray.length) {
          setDataError(Map({ message: markArray[0] }));
          return (markArray = []);
        }
        if (negativeArray.length) {
          setDataError(Map({ message: negativeArray[0] }));
          return (negativeArray = []);
        }

        const constructingData = () => {
          json.splice(0, 3);
          const qnsArray = (json, k) => {
            let tempArr = [];
            for (let i = 0; i < data.get('noQuestions', 1); i++) {
              const quest = 'Q' + (i + 1);
              tempArr.push({
                mark:
                  json[k][quest] === 0 || json[k][quest] === '0'
                    ? 0
                    : !json[k][quest] || json[k][quest] === '' || json[k][quest] === '-'
                    ? null
                    : json[k][quest],
                questionName: quest,
              });
            }
            return fromJS(tempArr);
          };
          let constructedArr = [];
          let defaultData = data
            .get('studentDetails', List())
            .filter((item, index) => item.get('isDefault', false));
          let defaultStudentArr = defaultData.map((sItem, sIndex) => {
            return fromJS({ _id: sItem.get('_id', ''), studentId: sItem.get('studentId', '') });
          });
          const constructId = (k) => {
            let temp = defaultStudentArr.find(
              (item) => item.get('studentId', '') === json[k]['Student_Id']?.toString().trim()
            );
            return temp.get('_id', '');
          };

          let defaultStudentIds = defaultData.map((sItem, sIndex) => sItem.get('studentId', ''));
          for (let k = 0; k < json.length; k++) {
            constructedArr.push(
              fromJS({
                ...(defaultStudentIds.includes(json[k]['Student_Id']?.toString().trim()) && {
                  ...(constructId(k) !== '' && {
                    _id: constructId(k),
                  }),
                }),
                name: json[k]['Student Name'].toString().trim(),
                studentId: json[k]['Student_Id']?.toString().trim(),
                gender: json[k]['Gender']?.toString().trim(),
                isDefault: defaultStudentIds.includes(json[k]['Student_Id']?.toString().trim())
                  ? true
                  : false,
                attendance:
                  json[k]['Attendance']?.toString().trim() === 'A' ||
                  json[k]['Attendance']?.toString().trim() === 'a'
                    ? false
                    : true,
                studentMarks: qnsArray(json, k),
              })
            );
          }
          return fromJS(constructedArr);
        };
        let actualData = constructingData();
        setData(data.set('studentDetails', actualData));
        callBackForUpload(actualData);
        return;
      }
      return setDataError(Map({ message: `Empty File` }));
    };
    if (!['xlsx'].includes(format)) {
      return setDataError(
        Map({
          message: `Please upload file of format .xlsx`,
        })
      );
    }
    const reader = new FileReader();
    reader.onload = async (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(data);
      const worksheet = workbook.worksheets[0];
      const json = [];

      worksheet.eachRow({ includeEmpty: true }, (row) => {
        json.push(row.values);
      });

      updateData(json);
    };
    reader.readAsArrayBuffer(csv);
  };
  const [runStudent, setRunStudent] = useState(false);
  useEffect(() => {
    if (runStudent) {
      getStudentList('', updateStudentMarks);
    }
  }, [runStudent]); //eslint-disable-line

  const callBack = (studentDetails) => {
    setList(list.set('studentData', studentDetails.slice(0, defaultCount)));
  };

  function handleChange(e, name) {
    const value = e.target.value;
    let updatedData = data;
    if (name === 'selectedCurriculum') {
      updatedData = updatedData
        .set('curriculumName', value)
        .set('levelNo', fromJS([]))
        .set('noQuestions', 0)
        .set('questionMarks', fromJS([]))
        .set('studentDetails', fromJS([]));
    } else if (name === 'selectedLevels') {
      updatedData = updatedData
        .set('levelNo', fromJS(value))
        .set('noQuestions', 0)
        .set('questionMarks', fromJS([]))
        .set('studentDetails', fromJS([]));
      getStudentList(value);
    } else if (name === 'noQuestions') {
      var indents = [];
      var studentIndents = [];
      if (value !== '' && value >= 0 && !isNaN(value)) {
        for (var i = 0; i < parseInt(value); i++) {
          const quest = 'Q' + (i + 1);
          indents.push({
            questionName: quest,
            outComeIds: [],
            totalMark: null,
            attainmentBenchMark: null,
          });
          studentIndents.push({
            questionName: quest,
            mark: null,
          });
        }
      }
      const studentDetails = data
        .get('studentDetails', List())
        .map((item) => item.set('studentMarks', fromJS(studentIndents)));
      updatedData = updatedData
        .set('noQuestions', parseInt(value))
        .set('questionMarks', fromJS(indents))
        .set('studentDetails', fromJS(studentDetails))
        .set('benchMark', '');
      callBack(fromJS(studentDetails));
    }
    setData(updatedData);
    if (name === 'noQuestions' && data.get('questionOutcome', 'PLO') !== 'PLO') {
      setRunStudent(true);
    }
  }
  const fileName = `${NeededDataForExport?.heading}_${NeededDataForExport?.planName}`;
  function updateStudentMarks(dt) {
    if (dt && dt.length > 0) {
      let updatedData = data;
      var studentIndents = [];
      const question = data.get('noQuestions', 0);
      if (question !== '' && !isNaN(question) && question) {
        for (var i = 0; i < parseInt(question); i++) {
          const quest = 'Q' + (i + 1);
          studentIndents.push({
            questionName: quest,
            mark: 0,
          });
        }

        const studentDetails = fromJS(dt).map((item) =>
          item
            .set('studentId', item.get('academic_no', ''))
            .set('attendance', item.get('attendance', true))
            .set('isDefault', true)
            .set('studentMarks', fromJS(studentIndents))
        );
        updatedData = updatedData.set('studentDetails', fromJS(studentDetails));
        setData(updatedData);
      }
    }
  }

  const curriculumPLO =
    type === 'program' ? outComeLists.get('curriculumPLO', List()) : outComeLists;
  const curriculumLevel = outComeLists.get('curriculumLevel', List());

  function getCurriculums() {
    if (type === 'program' && curriculumPLO.size) {
      return curriculumPLO
        .map((item) => {
          return {
            name: item.get('curriculumName', ''),
            value: item.get('curriculumName', ''),
            list: item.get('plo', List()),
          };
        })
        .toJS();
    }
    return [];
  }
  function getCurriculumLevel() {
    if (type === 'program' && data.get('curriculumName', '') !== '' && curriculumLevel.size) {
      return curriculumLevel
        .filter((subItem) => subItem.get('term', '') === term)
        .map((item) => {
          return {
            name: item.get('level_no', ''),
            value: item.get('level_no', ''),
            term: item.get('term', ''),
          };
        })
        .toJS();
    }
    return [];
  }
  const disabled =
    type !== 'program'
      ? !data.get('noQuestions', 0) || data.get('noQuestions', 0) === '' || !checkQuestionMark
      : data.get('curriculumName', '') === '' ||
        data.get('levelNo', '') === '' ||
        !data.get('noQuestions', 0) ||
        data.get('noQuestions', 0) === '' ||
        !checkQuestionMark;

  return (
    <div className="bg-gray p-2">
      <div className="container-fluid">
        <div className="row ">
          <div className="col-md-5 pt-1">
            <p className="bold mb-0 f-20">{ucFirst(planName)}</p>
            <p className="bold mb-0 f-15">Total Marks: {totalMarks}</p>
          </div>
          <div className="col-md-7">
            <OnUploadProgressBar
              uploadFile={uploadFile}
              showProgress={showProgress}
              setShowProgress={setShowProgress}
              setImportPercent={setImportPercent}
              importPercent={importPercent}
              fileNameOn={fileNameOn}
              setFileNameOn={setFileNameOn}
              setFileSizeOn={setFileSizeOn}
              fileSizeOn={fileSizeOn}
              handleChangeFile={handleChangeFile}
            />
            {mode === 'view' && (
              <div className="d-flex justify-content-end align-items-end f-17 bold pt-4">
                {type === 'program' && (
                  <>
                    <div className="mr-3">
                      Selected Curriculum: {getShortString(data.get('curriculumName', ''), 11)}
                    </div>
                    <div className="mr-3 pl-2">
                      Selected Student Level:{' '}
                      {getShortString(data.get('levelNo', '').join(', '), 20)}
                    </div>
                  </>
                )}
                <div className="mr-3 pl-2">No of Question: {data.get('noQuestions')}</div>
              </div>
            )}
            {mode === 'edit' && (
              <div className="d-flex justify-content-end align-items-end">
                {type === 'program' && (
                  <>
                    <div className="w-20 mr-3">
                      <MaterialInput
                        elementType={'materialSelectNew'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{
                          options: [{ name: '--Select--', value: '' }, ...getCurriculums()],
                        }}
                        label={'Select Curriculum'}
                        labelclass={'mb-0 f-14'}
                        // placeholder={`---Select---`}
                        changed={(e) => handleChange(e, 'selectedCurriculum')}
                        value={data.get('curriculumName', '')}
                      />
                    </div>
                    <div className="w-20 mr-3 pl-2">
                      <MaterialInput
                        elementType={'materialSelectMultiple'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        elementConfig={{ options: getCurriculumLevel() }}
                        label={'Select Student Level'}
                        labelclass={'mb-0 f-14'}
                        // placeholder={`---Select---`}
                        changed={(e) => handleChange(e, 'selectedLevels')}
                        value={data.get('levelNo', List()).toJS()}
                        isAllSelected={false}
                        multiple={true}
                      />
                    </div>
                  </>
                )}
                <div className="w-20 mr-3 pl-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'number'}
                    variant={'outlined'}
                    size={'small'}
                    label={
                      <div className="d-flex align-items-center">
                        No of Question{' '}
                        <Tooltips title="Altering the no. of questions will reset the data">
                          <div className="mt-1 ml-1">
                            {' '}
                            <ErrorIcon fontSize="small" />
                          </div>
                        </Tooltips>
                      </div>
                    }
                    labelclass={'mb-0 f-14'}
                    changed={(e) => handleChange(e, 'noQuestions')}
                    value={data.get('noQuestions')}
                  />
                </div>
                {CheckPermission(
                  'subTabs',
                  'Assessment Management',
                  'Assessment Library',
                  '',
                  'Program / Course Assessments',
                  '',
                  'Assessment View',
                  'Import ( Mark / Student Template )'
                ) && (
                  <>
                    <div className="pt-1 mr-3">
                      <Tooltips title={'Download - Assessment Template'}>
                        <MButton
                          variant="contained"
                          color={'skyBlueButton'}
                          clicked={() => exportExcel(DownloadData, splittingHeading, fileName)}
                          className="bold mb-2 mt-3"
                          disabled={disabled}
                          startIcon={<FileDownload />}
                        >
                          Download
                        </MButton>
                      </Tooltips>
                    </div>
                    <div className="mr-3 pt-1">
                      <MButton
                        color={'skyBlueButton'}
                        variant="contained"
                        component="label"
                        disabled={disabled}
                        className="bold mb-2 mt-3"
                        startIcon={<FileUpload />}
                      >
                        <Tooltips title={'Upload - Allowed Format "XLSX" only'}>
                          <div>Upload</div>
                        </Tooltips>
                        <input
                          type="file"
                          id="upload-file-progress"
                          multiple
                          hidden
                          accept={['.csv', '.xlsx']}
                          // onChange={handleChangeFile}
                        />
                      </MButton>
                      {/* </Tooltips> */}
                    </div>
                  </>
                )}
                {CheckPermission(
                  'subTabs',
                  'Assessment Management',
                  'Assessment Library',
                  '',
                  'Program / Course Assessments',
                  '',
                  'Assessment View',
                  'Add Student Manually'
                ) && (
                  <div className="pt-1">
                    <MButton
                      variant="contained"
                      clicked={handleAddShow}
                      color={'skyBlueButton'}
                      className="bold mb-2 mt-3 w-120px"
                      disabled={disabled}
                    >
                      {' '}
                      Add Student
                    </MButton>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

FilteredHeader.propTypes = {
  handleAddShow: PropTypes.func,
  totalMarks: PropTypes.string,
  planName: PropTypes.string,
  outComeLists: PropTypes.oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  data: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  getStudentList: PropTypes.func,
  setDataError: PropTypes.func,
  NeededDataForExport: PropTypes.object,
  type: PropTypes.string,
  institutionCalendarId: PropTypes.string,
  mode: PropTypes.string,
  term: PropTypes.string,
  list: PropTypes.instanceOf(Map),
  setList: PropTypes.func,
  defaultCount: PropTypes.number,
  splittingHeading: PropTypes.string,
  checkQuestionMark: PropTypes.bool,
};

export default memo(FilteredHeader);
