import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { List, Map } from 'immutable';

import YearLevels from './YearLevels';
import RemoteRoomsList from './RemoteRoomsList';
import * as actions from '../../../../_reduxapi/course_scheduling/action';
import { selectRemoteRooms } from '../../../../_reduxapi/course_scheduling/selectors';
import { getURLParams, levelRename } from '../../../../utils';

class RemoteRooms extends Component {
  constructor() {
    super();
    this.state = {
      programId: getURLParams('programId', true),
      programName: getURLParams('programName', true),
      activeTermName: '',
      activeYearId: '',
      activeYearName: '',
      activeLevelId: '',
    };
    this.setActiveLevel = this.setActiveLevel.bind(this);
    this.setActiveTerm = this.setActiveTerm.bind(this);
    this.handleBackClick = this.handleBackClick.bind(this);
  }

  componentDidMount() {
    const { programId } = this.state;
    this.props.setData(Map({ remoteRooms: List() }));
    if (programId) {
      this.props.getRemoteRooms(programId);
    }
  }

  getActiveTerm() {
    const { remoteRooms } = this.props;
    const activeTerm = remoteRooms.find(
      (term) => term.get('termName') === this.state.activeTermName
    );
    return activeTerm || remoteRooms.get(0, Map());
  }

  setActiveTerm(termName) {
    this.setState({ activeTermName: termName });
  }

  getGroupedYearLevel() {
    const activeTerm = this.getActiveTerm();
    return activeTerm.get('levels', List()).groupBy((level) => level.get('year'));
  }

  setActiveLevel(level) {
    this.setState({
      activeYearId: level.get('year_id', ''),
      activeYearName: level.get('year', ''),
      activeLevelId: level.get('level_id', ''),
    });
  }

  getActiveLevel() {
    const { activeYearId, activeYearName, activeLevelId } = this.state;
    const levels = this.getGroupedYearLevel().get(activeYearName, List());
    if (levels.isEmpty()) return Map();
    return (
      levels.find(
        (level) => level.get('year_id') === activeYearId && level.get('level_id') === activeLevelId
      ) || Map()
    );
  }

  handleBackClick() {
    if (!this.state.activeLevelId) {
      this.props.history.goBack();
    } else {
      this.setActiveLevel(Map());
    }
  }

  render() {
    const { programId, programName } = this.state;
    const { remoteRooms } = this.props;
    const activeTerm = this.getActiveTerm();
    const activeLevel = this.getActiveLevel();

    return (
      <div className="main pb-5 bg-gray">
        <div className="bg-white pt-3 pb-3">
          <div className="container-fluid">
            <div className="d-flex justify-content-between">
              <p className="bold mb-0 f-17">
                <i
                  className="fa fa-arrow-left mr-3 cursor-pointer"
                  aria-hidden="true"
                  onClick={this.handleBackClick}
                ></i>
                {activeLevel.isEmpty()
                  ? `${programName}`
                  : `${programName} / ${activeLevel
                      .get('year', '')
                      .split('year')
                      .join('Year ')} / ${levelRename(
                      activeLevel.get('level_name', ''),
                      programId
                    )}`}
              </p>
            </div>
          </div>
        </div>
        <div className="p-4">
          <div className="container">
            {activeLevel.isEmpty() ? (
              <YearLevels
                remoteRooms={remoteRooms}
                yearLevels={this.getGroupedYearLevel()}
                handleTermChange={this.setActiveTerm}
                handleViewClick={this.setActiveLevel}
                activeTermName={activeTerm.get('termName', '')}
                programId={programId}
              />
            ) : (
              <RemoteRoomsList
                termName={activeTerm.get('termName', '')}
                level={activeLevel}
                programId={programId}
                showMessage={this.props.resetMessage}
                saveRemoteRoom={this.props.saveRemoteRoom}
              />
            )}
          </div>
        </div>
      </div>
    );
  }
}

RemoteRooms.propTypes = {
  history: PropTypes.object,
  getRemoteRooms: PropTypes.func,
  remoteRooms: PropTypes.instanceOf(List),
  resetMessage: PropTypes.func,
  saveRemoteRoom: PropTypes.func,
  setData: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    remoteRooms: selectRemoteRooms(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(RemoteRooms);
