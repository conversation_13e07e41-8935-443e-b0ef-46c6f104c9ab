import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { FlexWrapper, BlockWrapper, Select, Gap, Label } from '../Styled';
import DateInput from '../UtilityComponents/DateInput';
import moment from 'moment';
import { isWeekEndDay, isWeekStartDay } from 'utils';

export default function CourseDuration({ data }) {
  const [nonRotation, setNonRotation] = data;
  return (
    <Fragment>
      <div className="container-fluid">
        <div className="p-40">
          <BlockWrapper>
            <h5>Course duration</h5>
            {nonRotation['edit']['level'][
              nonRotation['work_course'] === 'level_two_courses' ? 1 : 0
            ]['rotation_count'] === 0 && (
              // {nonRotation.course_flow !== "rotation"
              <FlexWrapper mg="10px 0">
                <input
                  type="radio"
                  name="select_dates"
                  id="same_dates"
                  checked={nonRotation.select_dates === 'same_dates' && true}
                  onChange={(e) =>
                    setNonRotation({
                      type: 'SELECT_DATES',
                      payload: 'same_dates',
                      name: e.target.name,
                    })
                  }
                />
                <Label mg="0px 0px 0px 5px" fs="16px" htmlFor="same_dates">
                  Complete level
                </Label>
              </FlexWrapper>
            )}

            {nonRotation.select_dates === 'same_dates' && (
              <div className="row">
                {/* <FlexWrapper mg="0 30px" className="ai_end"> */}
                {/* <div className="col-xl-3">
                <Label className="pl-2 mb-0">Level</Label>
                <Select
                  name="copy_date_index"
                  value={nonRotation.copy_date_index}
                  className={"styled-select"}
                  onChange={(e) =>
                    setNonRotation({
                      type: "COPY_PREVIOUS_DATES",
                      name: e.target.name,
                      payload: e.target.value,
                    })
                  }
                >
                  {nonRotation.copy_dates
                    .filter((item, i) => i === 0)
                    .map((item, i) => (
                      <option key={i} value={i}>
                        {item.courses_name}
                      </option>
                    ))}
                </Select>
                </div> */}
                <div className="col-xl-3">
                  <DateInput
                    datepicker={true}
                    title="Start date(read only)"
                    value={moment(
                      nonRotation['copy_dates'][nonRotation.copy_date_index]['start_date']
                    ).format('D MMM YYYY')}
                    read={true}
                  />
                </div>
                <div className="col-xl-3">
                  <DateInput
                    datepicker={true}
                    title="End date (read only)"
                    value={moment(
                      nonRotation['copy_dates'][nonRotation.copy_date_index]['end_date']
                    ).format('D MMM YYYY')}
                    read={true}
                  />
                </div>
                {/* </FlexWrapper> */}
              </div>
            )}
            <FlexWrapper mg="10px 0">
              <input
                type="radio"
                name="select_dates"
                id="choose_dates"
                checked={nonRotation.select_dates === 'choose_dates' && true}
                onChange={(e) =>
                  setNonRotation({
                    type: 'SELECT_DATES',
                    payload: 'choose_dates',
                    name: e.target.name,
                  })
                }
              />
              <Label mg="0px 0px 0px 5px" fs="16px" htmlFor="choose_dates">
                Select new dates
              </Label>
            </FlexWrapper>
            {nonRotation.select_dates === 'choose_dates' && (
              <BlockWrapper>
                <FlexWrapper mg="0 30px">
                  <input
                    type="radio"
                    name="custom_dates"
                    value="academic_week"
                    checked={nonRotation.custom_dates === 'academic_week'}
                    id="by_academic"
                    onChange={(e) =>
                      setNonRotation({
                        type: 'SELECT_CUSTOM_DATES',
                        name: e.target.name,
                        payload: e.target.value,
                      })
                    }
                  />
                  <Label mg="0px 0px 0px 5px" fs="16px" htmlFor="by_academic">
                    {' '}
                    By Academic weeks{' '}
                  </Label>
                </FlexWrapper>
                {nonRotation.custom_dates === 'academic_week' && (
                  <div className="pl-35 pt-3">
                    <div className="row">
                      <div className="col-xl-2">
                        <b className="pr-2 pt-1 float-right">From week no.</b>
                      </div>

                      <div className="col-xl-3">
                        <Select
                          name="academic_week_start"
                          value={nonRotation.academic_week_start}
                          className={'styled-select'}
                          onChange={(e) =>
                            setNonRotation({
                              type: 'WEEK_START_CHANGE',
                              name: e.target.name,
                              payload: e.target.value,
                            })
                          }
                        >
                          <option value={0}> Select Start Week</option>
                          {nonRotation.academic_weeks.map((item) => (
                            <option key={item.range} value={item.row_start}>
                              Week {item.row_start} - ( {item.range} )
                            </option>
                          ))}
                        </Select>
                      </div>

                      <div className="col-xl-1">
                        <b className="pr-2 pt-1 float-right">To</b>
                      </div>

                      <div className="col-xl-3">
                        <Select
                          name="academic_week_end"
                          value={nonRotation.academic_week_end}
                          className={'styled-select'}
                          onChange={(e) =>
                            setNonRotation({
                              type: 'WEEK_END_CHANGE',
                              name: e.target.name,
                              payload: e.target.value,
                            })
                          }
                        >
                          <option value={0}> Select End Week</option>
                          {nonRotation.academic_weeks.map((item) => (
                            <option key={item.range} value={item.row_start}>
                              Week {item.row_start} - ( {item.range} )
                            </option>
                          ))}
                        </Select>{' '}
                      </div>
                    </div>
                  </div>
                )}
                <FlexWrapper mg="10px 30px">
                  <input
                    type="radio"
                    name="custom_dates"
                    value="dates"
                    checked={nonRotation.custom_dates === 'dates'}
                    id="by_dates"
                    onChange={(e) =>
                      setNonRotation({
                        type: 'SELECT_CUSTOM_DATES',
                        name: e.target.name,
                        payload: e.target.value,
                      })
                    }
                  />
                  <Label mg="0px 0px 0px 5px" fs="16px" htmlFor="by_dates">
                    {' '}
                    By Dates{' '}
                  </Label>
                </FlexWrapper>
                {nonRotation.custom_dates === 'dates' && (
                  <FlexWrapper mg="10px 45px" className="ai_end">
                    <DateInput
                      datepicker={true}
                      isWeekStartDay={isWeekStartDay()}
                      placeholderText="Start Date"
                      selected={
                        nonRotation.type.start_date !== ''
                          ? new Date(nonRotation.type.start_date)
                          : null
                      }
                      value={
                        nonRotation.type.start_date !== ''
                          ? moment(nonRotation.type.start_date).format('D MMM YYYY')
                          : null
                      }
                      edit={(value) => {
                        setNonRotation({
                          type: 'ON_CHANGE',
                          name: 'start_date',
                          payload: value,
                        });
                      }}
                      title="Start date"
                      minDate={
                        nonRotation.edit[nonRotation.work_start_date] !== ''
                          ? new Date(nonRotation.edit[nonRotation.work_start_date])
                          : ''
                      }
                      maxDate={
                        nonRotation.edit[nonRotation.work_end_date] !== ''
                          ? new Date(nonRotation.edit[nonRotation.work_end_date])
                          : ''
                      }
                    />
                    {/* <DateInput
                    title="Start date"
                    min={(() => {
                      if (nonRotation.course_flow !== "normal") {
                        let start =
                          nonRotation.edit[nonRotation.work_start_date];
                        let final = "";
                        if (start !== "") {
                          let day = new Date(start).getDay();
                          if (day !== 0) {
                            //post sunday
                            let pre = Date.parse(start) + 86400000 * (7 - day);

                            //pre sunday
                            // let pre = Date.parse(start) - 86400000 * day;

                            final = new Date(pre).toISOString().slice(0, 10);
                          } else {
                            final = new Date(start).toISOString().slice(0, 10);
                          }
                        }
                        return final;
                      } else {
                        let final = new Date(nonRotation.work_min_date)
                          .toISOString()
                          .slice(0, 10);

                        return final;
                      }
                    })()}
                    max={String(
                      nonRotation.edit[nonRotation.work_end_date]
                    ).slice(0, 10)}
                    step={nonRotation.course_flow !== "normal" ? "7" : "0"}
                    value={nonRotation.type.start_date}
                    edit={(value) =>
                      setNonRotation({
                        type: "ON_CHANGE",
                        name: "start_date",
                        payload: value,
                      })
                    }
                  /> */}
                    <Gap /> to <Gap />
                    <DateInput
                      datepicker={true}
                      isWeekEndDay={isWeekEndDay()}
                      placeholderText="End Date"
                      selected={
                        nonRotation.type.end_date !== ''
                          ? new Date(nonRotation.type.end_date)
                          : null
                      }
                      value={
                        nonRotation.type.end_date !== ''
                          ? moment(nonRotation.type.end_date).format('D MMM YYYY')
                          : null
                      }
                      edit={(value) => {
                        setNonRotation({
                          type: 'ON_CHANGE',
                          name: 'end_date',
                          payload: value,
                        });
                      }}
                      title="End date"
                      minDate={
                        nonRotation.type.start_date !== ''
                          ? new Date(nonRotation.type.start_date)
                          : ''
                      }
                      //based on new requirement start
                      maxDate={
                        nonRotation.edit[nonRotation.work_level_two_end_date]
                          ? new Date(nonRotation.edit[nonRotation.work_level_two_end_date])
                          : new Date(nonRotation.edit[nonRotation.work_end_date])
                      }
                      //based on new requirement end
                    />
                    {/* <DateInput
                    title="End date"
                    min={(() => {
                      if (nonRotation.course_flow !== "normal") {
                        let start = nonRotation.type.start_date;
                        let final = "";
                        if (start !== "") {
                          let pre = Date.parse(start) + 86400000 * 4;
                          final = new Date(pre).toISOString().slice(0, 10);
                        }
                        return final;
                      } else {
                        if (nonRotation.type.start_date) {
                          let final = new Date(nonRotation.type.start_date)
                            .toISOString()
                            .slice(0, 10);
                          return final;
                        }
                        return "";
                      }
                    })()}
                    max={String(
                      nonRotation.edit[nonRotation.work_end_date]
                    ).slice(0, 10)}
                    step={nonRotation.course_flow !== "normal" ? "7" : "0"}
                    value={nonRotation.type.end_date}
                    edit={(value) =>
                      setNonRotation({
                        type: "ON_CHANGE",
                        name: "end_date",
                        payload: value,
                      })
                    }
                  /> */}
                  </FlexWrapper>
                )}
              </BlockWrapper>
            )}
          </BlockWrapper>
        </div>
      </div>
    </Fragment>
  );
}

CourseDuration.propTypes = {
  data: PropTypes.object,
};
