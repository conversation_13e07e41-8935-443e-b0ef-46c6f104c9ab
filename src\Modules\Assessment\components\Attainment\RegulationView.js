import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/assessment/action';
import { selectAttainment } from '_reduxapi/assessment/selector';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { useHistory, useParams } from 'react-router-dom';
import { useStylesFunction } from '../../css/designUtils';
import AttainmentLevel from './AttainmentLevel';
import { dString, getURLParams, eString } from 'utils';
import { fromJS, Map } from 'immutable';
import EvaluationPlan from './EvaluationPlan';
import { CheckPermission } from 'Modules/Shared/Permissions';

function ViewAttainmentLevel({ attainmentDetails, getAttainment, setData }) {
  const history = useHistory();
  const params = useParams();
  const { regulationId } = params;
  const classes = useStylesFunction();
  const [tabValue, setTabValue] = useState('evaluation-plan');
  const programName = getURLParams('pname', true);
  const type = getURLParams('type', true);
  const programId = getURLParams('pid', true);
  const courseId = getURLParams('courseId', true);
  const term = getURLParams('term', true);
  const levelNo = getURLParams('levelNo', true);

  useEffect(() => {
    const requestParams = { programId, courseId, levelNo, term };
    getAttainment(dString(regulationId), type, requestParams);
    return () => setData(fromJS({ attainmentDetails: {} }));
  }, [regulationId]); //eslint-disable-line

  const handleChange = (e, newValue) => setTabValue(newValue);

  const getTitle = () => {
    return type === 'program'
      ? `${programName} Program / ${attainmentDetails.get('regulationName', '')} - 
            ${attainmentDetails.get('regulationYear', '')} 
            ${attainmentDetails.get('curriculumName', '')}`
      : 'Manage Attainment';
  };

  const handleBackClick = () => {
    type === 'course'
      ? history.push(`/attainment-calculator/attainment-reports?type=${eString('course')}`)
      : history.goBack();
  };

  return (
    <div className="pb-5 bg-mainBackground">
      <div className="bg-white p-3 border-bottom">
        <div className="container-fluid">
          <p className="font-weight-bold mb-0 text-left f-17 text-capitalize">
            <i
              className="fa fa-arrow-left pr-3 remove_hover"
              aria-hidden="true"
              onClick={() => handleBackClick()}
            ></i>
            {getTitle()}
          </p>
        </div>
      </div>

      <div className="pl-1 pr-1 bg-white border-bottom">
        <Tabs
          value={tabValue}
          onChange={handleChange}
          textColor="primary"
          variant="scrollable"
          classes={{ indicator: classes.indicator }}
        >
          {CheckPermission(
            'subTabs',
            'Attainment Calculator',
            'Attainment Setting',
            '',
            'Regulations',
            '',
            'Evaluation Plan',
            'View'
          ) && (
            <Tab
              label="Evaluation Plan"
              value="evaluation-plan"
              classes={{ root: classes.tabTitle }}
            />
          )}
          {CheckPermission(
            'subTabs',
            'Attainment Calculator',
            'Attainment Setting',
            '',
            'Regulations',
            '',
            'Attainment Level',
            'View'
          ) && (
            <Tab
              label="Attainment Level"
              value="attainment-level"
              classes={{ root: classes.tabTitle }}
            />
          )}
        </Tabs>
      </div>

      {tabValue === 'evaluation-plan' && <EvaluationPlan attainmentDetails={attainmentDetails} />}
      {tabValue === 'attainment-level' && <AttainmentLevel />}
    </div>
  );
}

ViewAttainmentLevel.propTypes = {
  attainmentDetails: PropTypes.instanceOf(Map),
  getAttainment: PropTypes.func,
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    attainmentDetails: selectAttainment(state),
  };
};

export default connect(mapStateToProps, actions)(ViewAttainmentLevel);
