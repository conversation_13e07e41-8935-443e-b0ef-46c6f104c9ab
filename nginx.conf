worker_processes 1;

events {
    worker_connections 1024;
}

http {
    client_header_buffer_size 32k;
    large_client_header_buffers 8 32k;

    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    gzip on;
    gzip_http_version 1.1;
    gzip_disable "MSIE [1-6]\.";
    gzip_min_length 256;
    gzip_vary on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_comp_level 9;

    server {
        listen 4210;
        root /usr/share/nginx/html;
        index index.html;

        server_tokens off;

        # Security headers
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        # add_header X-Frame-Options SAMEORIGIN always;
        add_header Referrer-Policy "no-referrer" always;

        # Allow camera access - updated to allow camera for all origins
        add_header Permissions-Policy "camera=*, microphone=(), geolocation=(), fullscreen=()" always;

        # CORS headers
        add_header Access-Control-Allow-Origin $http_origin;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization" always;

        # Content Security Policy - updated to address all specific needs
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: blob:; style-src 'self' 'unsafe-inline' https:; font-src 'self' https:; img-src 'self' data: https: blob:; connect-src 'self' https:; frame-src 'self' https://www.google.com ${CSP_DOMAIN}; frame-ancestors 'self' ${CSP_DOMAIN}; object-src 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests; block-all-mixed-content" always;

        location / {
            try_files $uri $uri/ /index.html =404;
        }

        # Specific location for face capture
        location /face/ {
            try_files $uri $uri/ /face/index1.html =404;

            # Allow camera access for face capture
            add_header Permissions-Policy "camera=*" always;
            add_header Content-Security-Policy "frame-ancestors 'self' ${CSP_DOMAIN}; script-src 'self' https://www.google.com https://www.gstatic.com; style-src 'self' 'unsafe-inline' https://www.gstatic.com; frame-src https://www.google.com; connect-src 'self' https://www.google.com https://www.gstatic.com;" always;
        }
    }
}