worker_processes 1;

events {
    worker_connections 1024;
}

http {
    client_header_buffer_size 32k;
    large_client_header_buffers 8 64k;

    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    gzip on;
    gzip_http_version 1.1;
    gzip_disable "MSIE [1-6]\.";
    gzip_min_length 256;
    gzip_vary on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_comp_level 9;

    server {
        listen 4210;
        root /usr/share/nginx/html;
        index index.html;

        server_tokens off;

        # Security headers
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
        add_header Referrer-Policy "no-referrer";
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=, fullscreen=";

        # CORS headers
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";

        location / {
            try_files $uri $uri/ /index.html =404;
        }
    }
}