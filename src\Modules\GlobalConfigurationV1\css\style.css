.universityCard {
  background: white;
  min-height: 500px;
  margin-top: 13px;
  box-shadow: 0px 1px 3px rgba(17, 24, 39, 0.2);
  border-radius: 8px;
}
.universityCard_sub {
  background: white;
  margin-top: 13px;
  box-shadow: 0px 1px 3px rgba(17, 24, 39, 0.2);
  border-radius: 8px;
}
.innerUniverCard {
  border-bottom: 1px solid #d1d5db;
  padding: 30px 20px 30px 20px;
}
.workDay {
  background: #bbf7d0;
  padding: 16px;
  border-radius: 5px;
}
.everyDayCard {
  padding: 12px 15px 12px 15px;
  border-radius: 5px !important;
  margin-top: 1rem !important;
  border: 1px solid #dee2e6 !important;
}
.circle {
  border-radius: 50%;
  width: 42px;
  height: 42px;
  padding: 9px;
  background: #fff;
  border: 2px solid #9ca3af;
  color: #6b7280;
  text-align: center;
  font: 32px;
  margin-right: 20px;
  cursor: pointer;
}
.circle.active {
  background: #147afc;
  border: 2px solid #147afc;
  color: #fff;
}
.timeBox {
  border-radius: 5px !important;
  border: 1px solid #dee2e6 !important;
}
.innerTextBox {
  margin-right: 20px;
  width: 32%;
}
.p-12 {
  padding: 12px;
}
.day-bg-color {
  background: #147afc;
  color: #ffffff;
  border: none;
}
.scroll-table-tardis {
  overflow: scroll;
  max-height: 22em;
}
.scroll-table-tardis::-webkit-scrollbar {
  height: 8px;
  width: 5px;
}
.scroll-table-tardis::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.scroll-table-tardis::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.table-tardis-sticky {
  position: sticky;
  top: -1px;
  background: #ffffff;
  z-index: 9;
}

.width_150 {
  width: 150px;
}

.late_config_color {
  color: #374151;
}

.thresHold_config {
  border: 1px solid #d1d5db;
  border-radius: 5px;
  padding: 7px;
}

.table_config {
  width: fit-content;
  border-radius: 7px;
  border: 1px solid #d1d5db;
}

.border_bottom_config {
  border-bottom: 1px solid #d1d5db;
}

.width_210 {
  width: 210px;
}

.width_275 {
  width: 275px;
}

.margin_bottom_60px {
  margin-bottom: 60px;
}

.text-primary {
  color: #147afc;
  font-size: 17px;
  cursor: pointer;
}
.popover-TagList {
  width: 131px;
  font-size: 18px;
}
.position {
  position: relative;
}
.scroll-overflow {
  max-height: calc(100vh - 300px);
  overflow: auto;
}

.scroll-overflow::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.scroll-overflow::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.description-font-size {
  font-size: 12px;
}
.tagName-font-size {
  font-size: 14px;
}
.title-font-size {
  color: #4b5563;
  font-size: 16px;
}
.text-primary-font {
  color: #147afc;
}
.title-color-tags {
  color: #4b5563;
}
.search-box-color {
  color: #f3f4f6;
}
.scroll-overflow-modal {
  max-height: calc(100vh - 180px);
  overflow: auto;
}
.scroll-overflow-modal::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.scroll-overflow-modal::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.searchBox-font-size {
  font-size: 12px;
}
.popupName-title {
  color: #9ca3af;
  font-size: 12px;
}
.divider-radius {
  border-bottom: 1px solid #d1d5db;
}
.rounded-border-survey {
  border-radius: 21px !important;
  background-color: #f9fafb !important;
}

fw-300 {
  font-weight: 300;
}
.fw-400 {
  font-weight: 400;
}
.fw-500 {
  font-weight: 500;
}
.fw-600 {
  font-weight: 600;
}
.bg-grey {
  background-color: #f7f5f5;
}
.grid-container {
  display: grid;
  grid-template-columns: 0.65fr 1.7fr;
}
.templateHeader {
  grid-column: 1 / span 2; /* Spans across both columns */
}
.text-dGrey {
  color: #374151;
}
.text-lGrey {
  color: #6b7280;
}
.text-mGrey {
  color: #4b5563;
}
.bl-2 {
  border-left: 2px solid;
}
.f-36 {
  font-size: 36px;
}

.gap-8 {
  gap: 8px;
}

.gap-5 {
  gap: 5px;
}

.bgLight {
  background-color: #e1f5fa;
}

.templateName {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* Create three equal columns */
  grid-gap: 10px; /* Add gap between columns */
}

.QAPCmain-container {
  display: flex;
  flex-wrap: wrap;
}

.QAPCchild-container:nth-child(3) {
  flex: 0 0 100%; /* Third child takes up full width */
}
.QAPCchild-container:nth-child(1) {
  flex: 0 0 70%; /* Third child takes up full width */
}
.bgLGrey {
  background-color: #f3f4f6;
}

.q360-select-pd {
  padding: 5.7px 9px;
}
.q360-select-box-pd {
  padding: 3.7px 9px;
}

.q360-overflow-y {
  overflow: auto;
  min-height: 70px;
  max-height: 280px;
  padding-right: 8px;
}
.arrowIcon:before {
  content: '';
  position: absolute;
  -webkit-transform: translate(-50%, -108%);
  transform: translate(-50%, -108%);
  width: 0;
  height: 4px;
  border-style: solid;
  border-width: 8px 6px 4px;
  border-color: #cacaca transparent transparent;
  background: #f7f5f5;
}

.arrowCircle:after {
  content: '';
  width: 10px;
  height: 10px;
  margin: auto;
  margin-bottom: auto;
  border: none;
  margin-bottom: 12px;
  border-radius: 50%;
  background: #cacaca;
  display: inline-block;
  transform: translateX(-51%);
  position: absolute;
}

.popup-q360-overflow {
  padding: 0 5px 0;
  overflow-y: scroll;
  max-height: calc(100vh - 250px);
}

.popup-q360-overflow-sub {
  overflow-y: scroll;
  max-height: calc(100vh - 220px);
}

.f-18 {
  font-size: 18px !important;
}

.f-10 {
  font-size: 10px;
}

.q360-overflow-y {
  overflow: auto;
  min-height: 70px;
  max-height: 280px;
}

.q360-popup-Header {
  font-weight: 400;
  font-size: 24px;
  color: #4b5563;
}

.q360-color-gray {
  color: #4b5563;
}

.color-black {
  background-color: #4b5563;
  color: #ffffff;
}

.color-selected {
  background-color: #eff9fb !important;
}

.hover-color-selected:hover {
  background-color: #eff9fb !important;
}

.underlined-text {
  text-decoration: underline;
}

.gap-10 {
  gap: 10px;
}

.gap-15 {
  gap: 15px;
}

.gap-20 {
  gap: 20px;
}

.gap-30 {
  gap: 30px;
}

.gap-35 {
  gap: 35px;
}

.gap-3 {
  gap: 3px;
}

.mlt-12 {
  margin-left: 12px;
}
.f-24 {
  font-size: 24px;
}

.select-color {
  background-color: #e1f5fa !important;
}

.ptx-3 {
  padding-top: 3px;
}

.color-lt-gray {
  color: #9ca3af;
}

.flex-wrp {
  flex-wrap: wrap;
}

.flex-bases-300 {
  flex-basis: 300px;
}

.flex-bases-400 {
  flex-basis: 400px;
}

.flex-bases-350 {
  flex-basis: 350px;
}

.div-arrow:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 2px;
  height: 100%;
  background-color: #cacaca;
}

.z-1 {
  z-index: 1;
}

.hvh-25 {
  height: 15vh !important;
}

/* .h-80 {
  height: 80%;
} */

.scroll {
  overflow: scroll;
}

.w-95 {
  width: 95%;
}

.calcHeightVh {
  height: calc(100vh - 210px);
  overflow: auto;
}

.borderRounded-12 {
  border-radius: 12px;
  border: 1px solid #d1d5db;
}

.add-button {
  width: 220px;
  height: 100px;
  position: relative;
  margin: auto;
}

.w-23 {
  max-width: 23em;
}

.overflow-6 {
  overflow: auto;
  max-height: 6em;
}

.position-Ob-tp {
  position: absolute;
  top: -6px;
}

.position-Ob-bm {
  position: absolute;
  bottom: -17px;
}

.h_100vh {
  height: 100vh;
}

.overflowRoleList {
  overflow: auto;
  max-height: 38vh;
}

.height-container {
  height: calc(100vh - 90px);
  overflow: auto;
}

.text-overflow-wrap {
  flex-wrap: wrap;
  max-height: 15vh;
  overflow: auto;
}

.bg-color-darkGray {
  background-color: #f3f4f6 !important;
}

.bg-lite-greenShade {
  background-color: #f9fafb !important;
}

.setting_icon_position {
  position: absolute;
  top: 24px;
  right: 0px;
}

.bg-lGreen {
  background-color: #f9fafb;
}

.w-22 {
  width: 22em;
}

.cursor-pointer {
  cursor: pointer;
}

.time_line_dot_Margin_Active {
  margin: 15px 0px !important;
  padding: 0px 16px !important;
}
.time_line_dot_Margin.Active {
  color: #006eff !important;
}

.progressBarWidth {
  height: 45px;
  background: #e5f6fa;
  border-radius: 10px;
}

.uploadLabelSpan {
  font-weight: bolder;
  padding-left: 10px;
  padding-top: 5px;
}

.ProgressBar {
  flex-wrap: wrap;
}

.img_overflow_scroll {
  overflow: auto;
  height: 45vh;
}
.ht-40vh {
  height: 40vh;
}
.img_pdf_height {
  display: flex;
  width: 100%;
}
.popup-q360-overflow-step3 {
  overflow-y: scroll;
  max-height: calc(100vh - 135px);
}

.qapGrid-closed {
  display: grid;
  grid-template-columns: 3.5% 96.5%;
  gap: 15px;
}
.qapGrid-open {
  display: grid;
  grid-template-columns: 1.1fr 2.9fr;
  gap: 15px;
}

.qapGrid-child {
  writing-mode: vertical-rl;
  transform: rotate(180deg);
}

.h-60vh {
  height: 60vh;
}

.missed {
  background-color: #fee2e2;
  color: #ef4444;
}
.todo {
  background-color: #fef3c7;
  color: #f59e0b;
}
.columnImg {
  padding: 0.35rem;
}

.qapcCreateTableTH {
  color: #374151;
  background-color: #ffffff !important;
  border: none;
  text-transform: none;
  padding: 0;
}

.qapcCreateTable {
  min-width: 50em;
  border: none;
}
.text-dBlue {
  color: #147afc;
}
.bgAsh {
  background-color: #e5e7eb;
}
.count {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #e5e7eb;
}
.approvers {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5px;
}

.approvers-1 {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 5px;
}

.disabledText {
  color: #d1d5db;
}
.box-shadow:hover {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
}
.box-shadow-static {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.06) 0px 1px 2px 0px !important;
}
.text-grey {
  color: '#9CA3C0';
}
.mb-q360-3em {
  margin-bottom: 0.3em !important;
}
.qapc-createTable {
  max-height: calc(100vh - 245px);
  overflow: auto;
}
.sidebar-height {
  height: calc(100vh - 220px);
}
.categories-hover:hover {
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
p {
  margin-bottom: 0;
}

.text-rotate-vertical {
  writing-mode: vertical-lr;
  transform: rotate(180deg);
  width: 33%;
}

.positionExpand {
  position: absolute;
  top: 1px;
  left: 14px;
  font-size: 17px !important;
}
.margin-l {
  margin-left: 30px;
}

.graphGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(32.6%, 1fr));
  /* column-gap: 10px; */
}

.graphGridChild:nth-child(1):only-child {
  grid-column: span 3; /* Takes full space when only one child */
}

.Echarts-overflow {
  overflow: auto;
}

.Echarts-overflow::-webkit-scrollbar {
  height: 8px;
  width: 5px;
}
.Echarts-overflow::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}
.Echarts-overflow::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.slider-width {
  width: 30em;
}

.referenceDocuments-Height {
  height: calc(100vh - 100px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.PositionsSearchIconQ360 {
  top: 7px;
  left: 10px;
}
.g-config-scrollbar::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

.g-config-scrollbar::-webkit-scrollbar-thumb {
  background: #c8c8c8;
  border-radius: 15px;
}

.g-config-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  height: 30px;
  border-radius: 15px;
}
.bg-goldenrod {
  background-color: #f59e0b !important;
}
.approval-card {
  min-width: 350px;
}

.approval-arrow-container {
  position: relative;
  top: -4px;
}
.approval-downwards-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.approval-arrow-circle {
  width: 9px;
  height: 9px;
  background-color: #d1d5db;
  border-radius: 50%;
}
.approval-arrow-line {
  width: 2px;
  height: 110px;
  background-color: #d1d5db;
}
.approval-arrow-down {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 12px solid #d1d5db;
  position: absolute;
  bottom: -4px;
}
.approval-addedBtn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.approval-user-input.disabled {
  pointer-events: none;
  background-color: #e9ecef;
  color: #6c757d;
  border-radius: 4px;
}
.width-content-fit {
  width: fit-content;
}
.addRoleButton {
  background-color: #eff9fb !important;
  border-color: #147afc !important;
}
.rolesTableHeaderMain {
  display: grid;
  grid-template-columns: 1fr auto; /* Adjust column widths */
}
.rolesTableHeaderSub {
  display: grid;
  width: 600px;
  grid-template-columns: 1fr 1fr 1fr 10%; /* Adjust column widths */
}
.rolesTableBody {
  display: flex;
  flex-direction: row-reverse; /* Reverse the row order */
  justify-content: flex-start; /* Align items to the start of the container */
  width: 100%; /* Optional: Ensure the container takes up full width */
}

.roleBodyRemainingColumn {
  flex: 1; /* Each flex-item takes 1fr */
}

.roleBodyFirstColumn {
  flex: auto; /* The last item takes 2fr */
}
.rolesAccordBorder {
  border-left: 5px solid #147afc !important;
}
.rolesTitle {
  background-color: #f0f4f8;
}

/* TreeView.css */
.tree-view ul,
.tree-view li {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.tree-view {
  margin-left: 12px;
  padding-left: 15px; /* Add padding to create space for the border */
  border-left: 1px solid #ccc; /* Add left border */
}

.tree-view .nested {
  display: none;
}

.tree-view .active {
  display: block;
}

.scroll-overflow-q360 {
  max-height: 250px;
  min-height: auto;
  overflow: auto;
}