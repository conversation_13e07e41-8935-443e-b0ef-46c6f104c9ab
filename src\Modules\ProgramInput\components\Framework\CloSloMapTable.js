import React, { useState } from 'react';
import { Form, Button } from 'react-bootstrap';
import Tooltip from '../../../../_components/UI/Tooltip/Tooltip';
import PropTypes from 'prop-types';
import { List } from 'immutable';

function CloSloMapTable(props) {
  const {
    CLO,
    SLO,
    firstLoad,
    clickable,
    cloDomainIndexes,
    getMappedValue,
    handleChange,
    callBack,
  } = props;

  CloSloMapTable.propTypes = {
    CLO: PropTypes.instanceOf(List),
    SLO: PropTypes.instanceOf(List),
    firstLoad: PropTypes.bool,
    clickable: PropTypes.bool,
    cloDomainIndexes: PropTypes.instanceOf(List),
    getMappedValue: PropTypes.func,
    handleChange: PropTypes.func,
    callBack: PropTypes.func,
  };

  // let dataTable = React.createRef();
  const [list, setList] = useState({
    itemsDisplayed: 50,
    data: SLO.slice(0, 50),
  });
  const [activeButton, setActiveButton] = useState(SLO.size > 50 ? true : false);
  const [loading, setLoading] = useState(false);

  // let onScroll = () => {
  //   let tableEl = dataTable.current;
  //   if (tableEl.scrollTop === tableEl.scrollHeight - tableEl.offsetHeight) {
  //     if (list.itemsDisplayed + 50 <= SLO.size) {
  //       setList({
  //         itemsDisplayed: list.itemsDisplayed + 50,
  //         data: SLO.slice(0, list.itemsDisplayed + 50),
  //       });
  //     }
  //   }
  // };

  const loadMore = () => {
    setLoading(true);
    if (list.itemsDisplayed <= SLO.size) {
      setActiveButton(true);
      setList({
        itemsDisplayed: list.itemsDisplayed + 50,
        data: SLO.slice(0, list.itemsDisplayed + 50),
      });
      setTimeout(() => {
        setLoading(false);
      }, 3000);
    } else {
      setActiveButton(false);
      setLoading(false);
    }
  };
  // id="data-table" ref={dataTable} onScroll={onScroll}
  // console.log('list', SLO, list);
  return (
    <>
      <table className="table">
        <thead className="border_bootom">
          <tr>
            <th className="bg-lightdark">
              <div className="aw-75">CLO ##</div>
            </th>
            {CLO.map((clo) => (
              <th key={clo.get('_id')}>
                <Tooltip title={clo.get('name')}>
                  <div className="aw-50 ">{clo.get('no', '')}</div>
                </Tooltip>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="program-table-height learning_outcome_table_height">
          {list?.data?.map((slo, i) => (
            <tr key={i} className="tr-change">
              <td className="text-skyblue bg-lightdark border_all_bottom">
                <Tooltip title={slo.get('name')}>
                  <div className="aw-75 f-14 pt-2 bold">
                    {`${slo.get('delivery_symbol', '')}${slo.get('delivery_no', '')} ${slo.get(
                      'no'
                    )}`}
                  </div>
                </Tooltip>
              </td>
              {CLO.map((clo, j) => (
                <td
                  key={`${clo.get('_id')}-${i}-${j}`}
                  className={`border_all_bottom vertical-align-middle ${
                    cloDomainIndexes.includes(j + 1) ? 'border_right_dark_left' : ''
                  }`}
                >
                  <div className="aw-50">
                    {firstLoad ? (
                      <Form.Check
                        type="checkbox"
                        className="ml-2 bold"
                        checked={getMappedValue(clo.get('_id'), slo.get('_id'))}
                        onChange={(e) => {
                          if (clickable) {
                            callBack();
                            handleChange(e.target.checked, clo, slo);
                          }
                        }}
                      />
                    ) : (
                      <Form.Check
                        type="checkbox"
                        className="ml-2 bold"
                        onChange={(e) => {
                          if (clickable) {
                            callBack();
                            handleChange(e.target.checked, clo, slo);
                          }
                        }}
                      />
                    )}
                  </div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      {activeButton && (
        <Button
          onClick={() => {
            setLoading(true);
            loadMore();
          }}
          disabled={loading}
          className="load-more-slo"
        >
          {loading ? 'Loading...' : 'Load More'}
        </Button>
      )}
    </>
  );
}

export default CloSloMapTable;
