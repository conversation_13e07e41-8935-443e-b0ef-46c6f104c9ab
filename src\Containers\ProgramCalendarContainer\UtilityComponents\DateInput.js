import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { FlexWrapper, Input, Label } from '../Styled';
import DatePicker, { registerLocale } from 'react-datepicker';
import { getDay } from 'date-fns';
import 'react-datepicker/dist/react-datepicker.css';
import ar from 'date-fns/locale/ar';
import { getLang, getTranslatedDuration } from 'utils';
import { t } from 'i18next';
const lang = getLang();
registerLocale('ar', ar);

const CustomDateInput = (props) => {
  return (
    <div className="wrapper">
      <i
        onClick={props.onClick}
        aria-hidden="true"
        className={props.icon + (lang !== 'ar' ? ' calendarIcon' : ' calendarIconAr')}
      ></i>
      <input
        onClick={props.onClick}
        placeholder={props.placeholderText}
        className="custom-date-picker"
        value={
          props.value.includes('AM')
            ? props.value.replace('AM', t('date.am'))
            : props.value.includes('PM')
            ? props.value.replace('PM', t('date.pm'))
            : getTranslatedDuration(props.value)
        }
        type="text"
      />
    </div>
  );
};

const isWeekEnd = (date) => {
  const day = getDay(date);
  return day === 0 || day === 4;
};

const isWeekStartDay = (date) => {
  const day = getDay(date);
  return day === 0;
};

const isWeekEndDay = (date) => {
  const day = getDay(date);
  return day === 4;
};

const isWeekEndNot = (date) => {
  const day = getDay(date);
  return day !== 0 || day !== 4;
};

export default function DateInput({ title, edit, read, ...other }) {
  let isWeekEndDayEnable =
    other.isWeekEnd !== undefined && other.isWeekEnd === true
      ? isWeekEnd
      : other.isWeekStartDay !== undefined && other.isWeekStartDay === true
      ? isWeekStartDay
      : other.isWeekEndDay !== undefined && other.isWeekEndDay === true
      ? isWeekEndDay
      : other.isWeekEndNot !== undefined && other.isWeekEndNot === true
      ? isWeekEndNot
      : '';

  return (
    <Fragment>
      {other.datepicker !== undefined && other.datepicker === true ? (
        <FlexWrapper className="column">
          <Label mg="0px 0px 0px 5px">{title}</Label>
          {read ? (
            <DatePicker
              readOnly
              {...other}
              {...(lang === 'ar' && { locale: 'ar' })}
              className="readonly-datepicker"
            />
          ) : (
            <DatePicker
              onChange={(date) => edit(date)}
              {...other}
              {...(lang === 'ar' && { locale: 'ar' })}
              customInput={
                <CustomDateInput
                  placeholderText={other.placeholderText}
                  icon="fa fa-calendar-check-o"
                />
              }
              dateFormat="d MMM yyyy"
              filterDate={isWeekEndDayEnable}
              yearDropdownItemNumber={15}
              scrollableYearDropdown
              showMonthDropdown
              showYearDropdown
            />
          )}
        </FlexWrapper>
      ) : other.timepicker !== undefined && other.timepicker === true ? (
        <FlexWrapper className="column">
          <Label mg="0px 0px 0px 5px">{title}</Label>
          {read ? (
            <Input type="date" readOnly {...other} {...(lang === 'ar' && { lang: 'ar' })} />
          ) : (
            <DatePicker
              onChange={(date) => edit(date)}
              {...(lang === 'ar' && { locale: 'ar' })}
              {...other}
              customInput={
                <CustomDateInput placeholderText={other.placeholderText} icon="fa fa-clock" />
              }
              showTimeSelect
              showTimeSelectOnly
              timeIntervals={15}
              timeCaption={t('global_configuration.time')}
              dateFormat="h:mm aa"
              timeFormat="hh:mm aa"
              // minTime={setHours(setMinutes(new Date(), 0), 8)}
              // maxTime={setHours(setMinutes(new Date(), 0), 17)}
            />
          )}
        </FlexWrapper>
      ) : (
        <FlexWrapper className="column">
          <Label mg="0px 0px 0px 5px">{title}</Label>
          {read ? (
            <Input type="date" readOnly {...other} {...(lang === 'ar' && { lang: 'ar' })} />
          ) : (
            <Input
              type="date"
              onChange={(e) => edit(e.target.value)}
              {...other}
              {...(lang === 'ar' && { lang: 'ar' })}
            />
          )}
        </FlexWrapper>
      )}
    </Fragment>
  );
}

DateInput.propTypes = {
  title: PropTypes.string,
  edit: PropTypes.func,
  read: PropTypes.bool,
};

CustomDateInput.propTypes = {
  onClick: PropTypes.func,
  icon: PropTypes.string,
  placeholderText: PropTypes.string,
  value: PropTypes.string,
};
