import React, { useContext, useEffect, useRef } from 'react';

import TableHeader from './TableHeader';
import TableBody from './TableBody';
import Pagination from 'Widgets/FormElements/material/Pagination';
import parentContext from '../Context/Context';

function TableWithPagination() {
  const details = useContext(parentContext.PIL_Context);
  const tabData = useContext(parentContext.searchFilterContext);
  const { programInputList, getPaginationData, limit, setLimit } = details;
  const { tabValue, search, filter } = tabData;
  const firstLimit = useRef(true);

  useEffect(() => {
    if (firstLimit.current) {
      firstLimit.current = false;
      return;
    }
    getPaginationData(1, search, tabValue, filter);
  }, [limit]); // eslint-disable-line

  const handleChange = (e, pageNo) => {
    getPaginationData(pageNo, search, tabValue, filter);
  };

  return (
    <>
      <div className="program_table">
        <table align="left">
          <TableHeader />
          <TableBody />
        </table>
      </div>
      <div className="mt-3 d-flex justify-content-end">
        <Pagination
          count={programInputList.get('totalPages', 1)}
          page={programInputList.get('currentPage', 1)}
          onChange={handleChange}
          switchLimit={(val) => setLimit(val)}
        />
      </div>
    </>
  );
}

export default TableWithPagination;
