import React from 'react';
import PropTypes from 'prop-types';

function ParentComponent(props) {
  return (
    <div className="main pb-5 bg-gray ">
      <div className="container">
        <div className="p-3">
          <div className="col-md-12">
            <div className="mt-3">
              <div className="p-3 bg-white rounded">{props.children}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

ParentComponent.propTypes = {
  children: PropTypes.array,
};
export default ParentComponent;
