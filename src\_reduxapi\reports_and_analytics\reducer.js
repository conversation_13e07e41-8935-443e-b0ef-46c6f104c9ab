import { fromJS, Map } from 'immutable';
import * as actions from './action';

const initialState = fromJS({
  message: '',
  loading: {},
  dashboard: {},
  academicYearLevel: {},
  allStudentDetails: {},
  studentDetails: {},
  deptSubOverview: {},
  deptSubOverviewCourse: {},
  allStaffDetails: {},
  staffDetails: {},
  courseOverview: {},
  courseSessionStatus: {},
  courseStudentDetails: {},
  courseStaffDetails: {},
  userAttendanceReport: {},
  attendanceLog: {},
  activityInfo: {},
  activityThemeData: [],
  curriculumData: [],
  reviewQuizList: [],
  activitiesData: {},
  studentActivityData: [],
  sloListData: {},
  ploListData: {},
  frameworkData: {},
  dashboardProgramList: {},
  dashboardPlo: {},
  dashboardCreditHours: {},
  dashboardStudent: {},
  dashboardStaff: {},
  courseDetail: {},
  dashboardLoader: {},
  summary: {},
  academicReportYearLevel: {},
});

//eslint-disable-next-line
export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_REPORT_DASHBOARD_DATA_REQUEST: {
      return state
        .setIn(['loading', 'GET_REPORT_DASHBOARD_DATA'], true)
        .set('academicYearLevel', Map())
        .set('allStudentDetails', Map());
    }
    case actions.GET_REPORT_DASHBOARD_DATA_SUCCESS: {
      return state
        .setIn(['loading', 'GET_REPORT_DASHBOARD_DATA'], false)
        .set('dashboard', fromJS(action.data));
    }
    case actions.GET_REPORT_DASHBOARD_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_REPORT_DASHBOARD_DATA'], false)
        .set('message', errorMessage);
    }
    case actions.GET_ACADEMIC_YEAR_LEVEL_DATA_REQUEST: {
      return state.setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL_DATA'], true);
    }
    case actions.GET_ACADEMIC_YEAR_LEVEL_DATA_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL_DATA'], false)
        .set('academicYearLevel', fromJS(action.data));
    }
    case actions.GET_ACADEMIC_YEAR_LEVEL_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_ACADEMIC_YEAR_LEVEL_DATA'], false)
        .set('message', errorMessage);
    }
    case actions.GET_ALL_STUDENT_DETAILS_REQUEST: {
      return state.setIn(['loading', 'GET_ALL_STUDENT_DETAILS'], true);
    }
    case actions.GET_ALL_STUDENT_DETAILS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ALL_STUDENT_DETAILS'], false)
        .set('allStudentDetails', fromJS(action?.data !== undefined ? action?.data : Map()));
    }
    case actions.GET_ALL_STUDENT_DETAILS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_ALL_STUDENT_DETAILS'], false)
        .set('message', errorMessage);
    }
    case actions.GET_STUDENT_DETAILS_BY_ID_REQUEST: {
      return state
        .setIn(['loading', 'GET_STUDENT_DETAILS_BY_ID'], true)
        .set('studentDetails', Map());
    }
    case actions.GET_STUDENT_DETAILS_BY_ID_SUCCESS: {
      return state
        .setIn(['loading', 'GET_STUDENT_DETAILS_BY_ID'], false)
        .set('studentDetails', fromJS(action.data));
    }
    case actions.GET_STUDENT_DETAILS_BY_ID_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_STUDENT_DETAILS_BY_ID'], false)
        .set('message', errorMessage);
    }
    case actions.GET_DEPT_SUB_OVERVIEW_REQUEST: {
      return state
        .setIn(['loading', 'GET_DEPT_SUB_OVERVIEW'], true)
        .set('deptSubOverview', Map())
        .set('deptSubOverviewCourse', Map());
    }
    case actions.GET_DEPT_SUB_OVERVIEW_SUCCESS: {
      return state
        .setIn(['loading', 'GET_DEPT_SUB_OVERVIEW'], false)
        .set('deptSubOverview', fromJS(action.data));
    }
    case actions.GET_DEPT_SUB_OVERVIEW_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_DEPT_SUB_OVERVIEW'], false).set('message', errorMessage);
    }
    case actions.GET_DEPT_SUB_OVERVIEW_COURSE_DATA_REQUEST: {
      return state
        .setIn(['loading', 'GET_DEPT_SUB_OVERVIEW_COURSE_DATA'], true)
        .set('deptSubOverviewCourse', Map());
    }
    case actions.GET_DEPT_SUB_OVERVIEW_COURSE_DATA_SUCCESS: {
      return state
        .setIn(['loading', 'GET_DEPT_SUB_OVERVIEW_COURSE_DATA'], false)
        .set('deptSubOverviewCourse', fromJS(action.data));
    }
    case actions.GET_DEPT_SUB_OVERVIEW_COURSE_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_DEPT_SUB_OVERVIEW_COURSE_DATA'], false)
        .set('message', errorMessage);
    }
    case actions.GET_ALL_STAFF_DETAILS_REQUEST: {
      return state.setIn(['loading', 'GET_ALL_STAFF_DETAILS'], true).set('allStaffDetails', Map());
    }
    case actions.GET_ALL_STAFF_DETAILS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ALL_STAFF_DETAILS'], false)
        .set('allStaffDetails', fromJS(action?.data !== undefined ? action?.data : Map()));
    }
    case actions.GET_ALL_STAFF_DETAILS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_ALL_STAFF_DETAILS'], false).set('message', errorMessage);
    }
    case actions.GET_STAFF_DETAILS_BY_ID_REQUEST: {
      return state.setIn(['loading', 'GET_STAFF_DETAILS_BY_ID'], true).set('staffDetails', Map());
    }
    case actions.GET_STAFF_DETAILS_BY_ID_SUCCESS: {
      return state
        .setIn(['loading', 'GET_STAFF_DETAILS_BY_ID'], false)
        .set('staffDetails', fromJS(action.data));
    }
    case actions.GET_STAFF_DETAILS_BY_ID_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_STAFF_DETAILS_BY_ID'], false)
        .set('message', errorMessage);
    }
    case actions.GET_COURSE_OVERVIEW_DATA_REQUEST: {
      return state
        .setIn(['loading', 'GET_COURSE_OVERVIEW_DATA'], true)
        .set('courseOverview', Map());
    }
    case actions.GET_COURSE_OVERVIEW_DATA_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_OVERVIEW_DATA'], false)
        .set('courseOverview', fromJS(action.data));
    }
    case actions.GET_COURSE_OVERVIEW_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_COURSE_OVERVIEW_DATA'], false)
        .set('message', errorMessage);
    }
    case actions.GET_COURSE_SESSION_STATUS_DATA_REQUEST: {
      return state
        .setIn(['loading', 'GET_COURSE_SESSION_STATUS_DATA'], true)
        .set('courseSessionStatus', Map());
    }
    case actions.GET_COURSE_SESSION_STATUS_DATA_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_SESSION_STATUS_DATA'], false)
        .set('courseSessionStatus', fromJS(action.data));
    }
    case actions.GET_COURSE_SESSION_STATUS_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_COURSE_SESSION_STATUS_DATA'], false)
        .set('message', errorMessage);
    }
    case actions.GET_COURSE_STUDENT_DETAILS_REQUEST: {
      return state
        .setIn(['loading', 'GET_COURSE_STUDENT_DETAILS'], true)
        .set('courseStudentDetails', Map());
    }
    case actions.GET_COURSE_STUDENT_DETAILS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_STUDENT_DETAILS'], false)
        .set('courseStudentDetails', fromJS(action.data));
    }
    case actions.GET_COURSE_STUDENT_DETAILS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_COURSE_STUDENT_DETAILS'], false)
        .set('message', errorMessage);
    }
    case actions.GET_COURSE_STAFF_DETAILS_REQUEST: {
      return state
        .setIn(['loading', 'GET_COURSE_STAFF_DETAILS'], true)
        .set('courseStaffDetails', Map());
    }
    case actions.GET_COURSE_STAFF_DETAILS_SUCCESS: {
      return state
        .setIn(['loading', 'GET_COURSE_STAFF_DETAILS'], false)
        .set('courseStaffDetails', fromJS(action.data));
    }
    case actions.GET_COURSE_STAFF_DETAILS_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_COURSE_STAFF_DETAILS'], false)
        .set('message', errorMessage);
    }
    case actions.GET_ATTENDANCE_REPORT_BY_USER_ID_REQUEST: {
      return state
        .setIn(['loading', 'GET_ATTENDANCE_REPORT_BY_USER_ID'], true)
        .set('userAttendanceReport', Map());
    }
    case actions.GET_ATTENDANCE_REPORT_BY_USER_ID_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ATTENDANCE_REPORT_BY_USER_ID'], false)
        .set('userAttendanceReport', fromJS(action.data));
    }
    case actions.GET_ATTENDANCE_REPORT_BY_USER_ID_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['loading', 'GET_ATTENDANCE_REPORT_BY_USER_ID'], false)
        .set('message', errorMessage);
    }
    case actions.GET_ATTENDANCE_LOG_REQUEST: {
      return state.setIn(['loading', 'GET_ATTENDANCE_LOG'], true).set('attendanceLog', Map());
    }
    case actions.GET_ATTENDANCE_LOG_SUCCESS: {
      return state
        .setIn(['loading', 'GET_ATTENDANCE_LOG'], false)
        .set('attendanceLog', fromJS(action.data));
    }
    case actions.GET_ATTENDANCE_LOG_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_ATTENDANCE_LOG'], false).set('message', errorMessage);
    }
    case actions.GET_ACTIVITY_INFO_REQUEST: {
      return state.setIn(['loading', 'GET_ACTIVITY_INFO_REQUEST'], true).set('activityInfo', Map());
    }
    case actions.GET_ACTIVITY_INFO_SUCCESS: {
      let data = action.data.theme
        ? action.data.theme[0]
        : action.data.curriculums
        ? action.data.curriculums[0].framework.theme[0]
        : action.data;
      let themeData = action.data.theme
        ? action.data.theme
        : action.data.curriculums
        ? action.data.curriculums[0].framework.theme
        : [];
      let frameworkData = action.data.curriculums ? action.data.curriculums[0].framework : {};
      let curriculumData = action.data.curriculums ? action.data.curriculums : [];
      let courseDetail = action.data.course_details ? action.data.course_details : {};
      const allGroup = data.studentGroups.map((val, index) => {
        return { ...val, _id: index, group_name: 'All Course Group' };
      });
      data.studentGroups = [...allGroup, ...data.studentGroups];
      return state
        .setIn(['loading', 'GET_ACTIVITY_INFO_REQUEST'], false)
        .set('activityInfo', fromJS(data))
        .set('activityThemeData', fromJS(themeData))
        .set('frameworkData', fromJS(frameworkData))
        .set('curriculumData', fromJS(curriculumData))
        .set('courseDetail', fromJS(courseDetail));
    }
    case actions.GET_ACTIVITY_INFO_FAILURE: {
      return state
        .setIn(['loading', 'GET_ACTIVITY_INFO_REQUEST'], false)
        .set(`error`, action.error);
    }

    case actions.GET_REVIEW_QUIZ_REQUEST: {
      return state
        .setIn(['loading', 'GET_REVIEW_QUIZ_REQUEST'], true)
        .set(`reviewQuizList`, fromJS([]));
    }
    case actions.GET_REVIEW_QUIZ_SUCCESS: {
      return state
        .setIn(['loading', 'GET_REVIEW_QUIZ_REQUEST'], false)
        .set(`reviewQuizList`, fromJS(action.data.data));
    }
    case actions.GET_REVIEW_QUIZ_FAILURE: {
      return state.setIn(['loading', 'GET_REVIEW_QUIZ_REQUEST'], false).set(`error`, action.error);
    }

    case actions.GET_QUIZ_REQUEST: {
      return state.set(['loading', 'GET_QUIZ_REQUEST'], true);
    }
    case actions.GET_QUIZ_SUCCESS: {
      return state
        .set(['loading', 'GET_QUIZ_REQUEST'], false)
        .set(`activitiesData`, fromJS(action.data.data));
    }
    case actions.GET_QUIZ_FAILURE: {
      return state.set(['loading', 'GET_QUIZ_REQUEST'], false).set(`error`, action.error);
    }

    case actions.GET_STUDENT_ACTIVITY_DATA_REQUEST: {
      return state.set(['loading', 'GET_STUDENT_ACTIVITY_DATA_REQUEST'], true);
    }
    case actions.GET_STUDENT_ACTIVITY_DATA_SUCCESS: {
      return state
        .set(['loading', 'GET_STUDENT_ACTIVITY_DATA_REQUEST'], false)
        .set(`studentActivityData`, fromJS(action.data.data));
    }
    case actions.GET_STUDENT_ACTIVITY_DATA_FAILURE: {
      return state
        .set(['loading', 'GET_STUDENT_ACTIVITY_DATA_REQUEST'], false)
        .set(`error`, action.error);
    }

    case actions.GET_SLO_REQUEST: {
      return state.setIn(['loading', 'GET_SLO_REQUEST'], true);
    }
    case actions.GET_SLO_SUCCESS: {
      return state
        .setIn(['loading', 'GET_SLO_REQUEST'], false)
        .set(`sloListData`, fromJS(action.data.data));
    }
    case actions.GET_SLO_FAILURE: {
      return state.setIn(['loading', 'GET_PLO_REQUEST'], false).set(`error`, action.error);
    }
    case actions.GET_PLO_REQUEST: {
      return state.setIn(['loading', 'GET_PLO_REQUEST'], true);
    }
    case actions.GET_PLO_SUCCESS: {
      return state
        .setIn(['loading', 'GET_PLO_REQUEST'], false)
        .set(`ploListData`, fromJS(action.data.data));
    }
    case actions.GET_PLO_FAILURE: {
      return state.setIn(['loading', 'GET_PLO_REQUEST'], false).set(`error`, action.error);
    }
    case actions.GET_REPORT_DASHBOARD_DATA_TYPE_REQUEST: {
      const dataType = action.dataType;
      const dashboardData =
        dataType === 'program_list'
          ? 'dashboardProgramList'
          : dataType === 'plo'
          ? 'dashboardPlo'
          : dataType === 'credit_hours'
          ? 'dashboardCreditHours'
          : dataType === 'no_staff'
          ? 'dashboardStaff'
          : dataType === 'no_student'
          ? 'dashboardStudent'
          : '';
      return state
        .setIn(['dashboardLoader', dataType], true)
        .set(dashboardData, fromJS({}))
        .set('academicYearLevel', Map())
        .set('allStudentDetails', Map());
    }
    case actions.GET_REPORT_DASHBOARD_DATA_TYPE_SUCCESS: {
      const dataType = action.data.dataType;
      const dashboardData =
        dataType === 'program_list'
          ? 'dashboardProgramList'
          : dataType === 'plo'
          ? 'dashboardPlo'
          : dataType === 'credit_hours'
          ? 'dashboardCreditHours'
          : dataType === 'no_staff'
          ? 'dashboardStaff'
          : dataType === 'no_student'
          ? 'dashboardStudent'
          : '';
      return state
        .setIn(['dashboardLoader', dataType], false)
        .set(dashboardData, fromJS(action.data.data));
    }
    case actions.GET_REPORT_DASHBOARD_DATA_TYPE_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['dashboardLoader', action.error.dataType], false)
        .set('message', errorMessage);
    }

    case actions.GET_SUMMARY_DATA_REQUEST: {
      return state.setIn(['dashboardLoader', 'GET_SUMMARY_DATA'], true);
    }
    case actions.GET_SUMMARY_DATA_SUCCESS: {
      return state
        .setIn(['dashboardLoader', 'GET_SUMMARY_DATA'], false)
        .set('summary', fromJS(action.data));
    }
    case actions.GET_SUMMARY_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state
        .setIn(['dashboardLoader', 'GET_SUMMARY_DATA'], false)
        .set('message', errorMessage);
    }

    case actions.GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_REQUEST: {
      return state.setIn(['dashboardLoader', 'GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA'], true);
    }
    case actions.GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_SUCCESS: {
      return state
        .setIn(['dashboardLoader', 'GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA'], false)
        .set('academicReportYearLevel', fromJS(action.data));
    }
    case actions.GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string'
          ? message
          : 'Something went wrong. Please try again.';
      return state
        .setIn(['dashboardLoader', 'GET_ACADEMIC_REPORT_YEAR_LEVEL_DATA'], false)
        .set('message', errorMessage);
    }
    case actions.GET_ATTENDANCE_LOG_EXPORT_REQUEST: {
      return state.setIn(['loading', 'GET_ATTENDANCE_LOG'], true);
    }
    case actions.GET_ATTENDANCE_LOG_EXPORT_SUCCESS: {
      return state.setIn(['loading', 'GET_ATTENDANCE_LOG'], false);
    }
    case actions.GET_ATTENDANCE_LOG_EXPORT_FAILURE: {
      const { response: { data: { message = '' } = {} } = {} } = action.error;
      const errorMessage =
        message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
      return state.setIn(['loading', 'GET_ATTENDANCE_LOG'], false).set('message', errorMessage);
    }
    default:
      return state;
  }
}
