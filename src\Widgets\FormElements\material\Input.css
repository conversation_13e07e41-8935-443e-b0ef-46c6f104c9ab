.MuiButton-outlinedGray {
  color: #374151 !important;
}
[disabled] {
  background-color: #fafafa;
}
#autoCompletePadding {
  padding: 2.8px !important;
}
.endIconDropdown {
  margin-bottom: 4px;
  margin-top: 4px;
}

.dropdowncheckbox {
  background-color: #F9FAFB !important;
  color: black !important;
  border: none !important;
  box-sizing: unset !important;
  transition: unset !important;
  box-shadow: none !important;
  padding-left: 0px !important;
}

.bold {
  font-size: bold !important;
}


.checkbox_radio {
  width: 20px;
  height: 17px;
  accent-color: #147afc;
  margin-right: 10px;
  padding: 0px;
}
select#paddingStaff {
  padding: 7px 10px 7px 10px !important;
}

/* .MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation1.MuiPaper-root .MuiMenu-paper.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation8.MuiPopover-paper.css-1poimk-MuiPaper-root-MuiMenu-paper-MuiPaper-root-MuiPopover-paper
{

} */
