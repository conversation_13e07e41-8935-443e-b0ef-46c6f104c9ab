import React, { Fragment, useReducer, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { convertHijiriYear, getLang, showArabicMailShow } from '../../../../utils';
import PropTypes from 'prop-types';
import { FlexWrapper, PrimaryButton, Null, Padding } from '../../Styled';
import {
  interimDashboard,
  interimLandingApi,
  interimChangeYear,
  interimChangeTitle,
  interimActivateModal,
  loadInterimDataFromInstitution,
} from '../../../../_reduxapi/actions/interimCalendar';
import Tooltip from '../../../../_components/UI/Tooltip/Tooltip';
import NavRowButtons from '../../UtilityComponents/Interim/NavRowButtons';
import LevelField from '../Interim/LevelField';
import rootInterimReducer from '../Interim/SettingsReducer';
import { level_initial_interim_state } from '../Interim/InitialState';
import Loader from '../../../../Widgets/Loader/Loader';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
const lang = getLang();
const SetCurriculum = (props) => {
  const {
    id,
    title,
    year1,
    year2,
    year3,
    year4,
    year5,
    year6,
    active,
    interimActivateModal,
    interimChangeTitle,
    loadInterimDataFromInstitution,
    isLoading,
    interimLandingApi,
    interimDashboard,
    token,
    currentProgramCalendarId,
    active_semesters,
    activeInstitutionCalendar,
  } = props;

  const eventsState = useReducer(rootInterimReducer, level_initial_interim_state);

  const [level, setLevel] = eventsState; // eslint-disable-line
  const [count1, setCount1] = useState(0);
  const [count2, setCount2] = useState(0);
  const [count3, setCount3] = useState(0);
  const [count4, setCount4] = useState(0);

  let search1 = window.location.search;
  let params1 = new URLSearchParams(search1);
  let urlYear = params1.get('year');
  let urlIcd = params1.get('calendarid') || params1.get('icd');
  let urlProgramId = params1.get('programid');
  let urlProgramName = params1.get('pname');

  useEffect(() => {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let programId = params.get('programid');
    let calendarid = params.get('calendarid') || params.get('icd');
    let yearURL = params.get('year');

    if (count1 === 0 && activeInstitutionCalendar && !activeInstitutionCalendar.isEmpty()) {
      interimDashboard(programId, calendarid, yearURL, activeInstitutionCalendar.get('_id'));
      setCount1(1);
    }

    if (
      count2 === 0 /*&& id !== null*/ &&
      activeInstitutionCalendar &&
      !activeInstitutionCalendar.isEmpty()
    ) {
      interimLandingApi(programId, calendarid, activeInstitutionCalendar.get('_id'));
      let checkYearLength = Object.keys(year2).length !== 0;
      if (count3 === 0 && checkYearLength) {
        setLevel({
          type: 'INITIAL_INTERIM_LOAD',
          payload: {
            year1: year1,
            year2: year2,
            year3: year3,
            year4: year4,
            year5: year5,
            year6: year6,
          },
        });
        setCount3(1);
      }
      setCount2(1);
    }

    if (count4 === 0 && activeInstitutionCalendar && !activeInstitutionCalendar.isEmpty()) {
      loadInterimDataFromInstitution(calendarid, activeInstitutionCalendar.get('_id'));
      setCount4(1);
    }
  }, [
    year1,
    year2,
    year3,
    year4,
    year5,
    year6,
    setLevel,
    interimDashboard,
    interimLandingApi,
    token,
    count1,
    count2,
    count3,
    count4,
    id,
    loadInterimDataFromInstitution,
    activeInstitutionCalendar,
  ]);

  useEffect(() => {
    setLevel({
      type: 'INITIAL_INTERIM_LOAD',
      payload: {
        year1: props['year1'],
        year2: props['year2'],
        year3: props['year3'],
        year4: props['year4'],
        year5: props['year5'],
        year6: props['year6'],
      },
    });
  }, [props, setLevel]);

  const history = useHistory();
  let hijiriYear = '';
  if (title !== undefined && title !== '' && title !== null) {
    let splitYear = title.split('-');
    let year1Title = splitYear[0];
    let year2Title = splitYear[1];
    let sliceEndYear = convertHijiriYear(year2Title);
    sliceEndYear = sliceEndYear.toString();
    sliceEndYear = sliceEndYear.substr(2, 4);

    hijiriYear = `${year1Title} - ${year2Title.substr(2, 4)}${
      showArabicMailShow() ? ` G (${convertHijiriYear(year1Title)} - ${sliceEndYear} H)` : ''
    }`;
  }

  let eventsBtnActive = false;
  let activeSemsesters = props[active]['semesters'];
  if (
    activeSemsesters &&
    activeSemsesters !== undefined &&
    activeSemsesters[active_semesters] !== undefined
  ) {
    let semester1 = activeSemsesters[active_semesters];
    if (semester1 && semester1.length > 0) {
      semester1.forEach((check) => {
        eventsBtnActive =
          check.start_date !== undefined &&
          check.end_date !== undefined &&
          check.start_date !== '' &&
          check !== '';
      });
      // eventsBtnActive = semester1[0].start_date !== undefined && semester1[0].end_date !== undefined && semester1[0].start_date !== "" && semester1[0].end_date !== "" && semester1[1].start_date !== undefined && semester1[1].end_date !== undefined && semester1[1].start_date !== "" && semester1[1].end_date !== "";
    }
  }
  useEffect(() => {
    interimChangeTitle(t('role_management.role_actions.Calendar Settings'));
  });

  return (
    <Fragment>
      <Loader isLoading={isLoading} />
      <FlexWrapper>
        <Padding
          className="back"
          onClick={() => {
            interimChangeTitle();
            //interimChangeYear("year2");
            history.push(
              `/program-calendar?year=${urlYear}&icd=${urlIcd}&programid=${urlProgramId}&pname=${urlProgramName}`
            );
          }}
        >
          <i
            className={`fa remove_hover ${lang !== 'ar' ? 'fa-arrow-left' : 'fa-arrow-right'}`}
          ></i>
        </Padding>
        <Padding>
          <Trans i18nKey={'program_calendar.program_calendar_settings'}></Trans>
        </Padding>
        <Null />
      </FlexWrapper>
      <FlexWrapper className="wrap" mg="0 25px 25px 25px">
        <Padding style={{ fontSize: '16px' }} pd="25px 40px">
          <Trans i18nKey={'program_calendar.academic_year'}></Trans> {hijiriYear}
        </Padding>
        <Null />

        <Tooltip
          title={
            currentProgramCalendarId !== null
              ? `${t('program_calendar.curriculum_already_set')}`
              : `${t('program_calendar.set_new_curriculum')}`
          }
        >
          <PrimaryButton
            className={currentProgramCalendarId !== null ? 'light disable' : 'light'}
            disabled={currentProgramCalendarId !== null && true}
            onClick={() => interimActivateModal('interimCurriculum')}
          >
            <Trans i18nKey={'program_calendar.set_curriculum'}></Trans>
          </PrimaryButton>
        </Tooltip>
      </FlexWrapper>

      {currentProgramCalendarId !== null && (
        <Fragment>
          <Fragment>
            <FlexWrapper mg="0 25px">
              <NavRowButtons theme="light" />
            </FlexWrapper>
            <FlexWrapper mg="0 25px">
              <Null />
              <Tooltip title={eventsBtnActive ? 'Add Events' : 'Set Level Start Date And End Date'}>
                <PrimaryButton
                  className={eventsBtnActive ? 'light' : 'light disable'}
                  disabled={!eventsBtnActive}
                  onClick={() => interimActivateModal('interimEvents')}
                >
                  <i className="fas fa-plus"></i>{' '}
                  <Trans i18nKey={'program_calendar.events'}></Trans>
                </PrimaryButton>
              </Tooltip>
            </FlexWrapper>
            <LevelField eventsState={eventsState} programId={urlProgramId} />
          </Fragment>
          {/* ) : (
          <Null />
        )} */}
        </Fragment>
      )}
    </Fragment>
  );
};

SetCurriculum.propTypes = {
  id: PropTypes.string,
  title: PropTypes.string,
  year1: PropTypes.object,
  year2: PropTypes.object,
  year3: PropTypes.object,
  year4: PropTypes.object,
  year5: PropTypes.object,
  year6: PropTypes.object,
  active: PropTypes.string,
  interimActivateModal: PropTypes.func,
  interimChangeTitle: PropTypes.func,
  loadInterimDataFromInstitution: PropTypes.func,
  isLoading: PropTypes.bool,
  interimLandingApi: PropTypes.func,
  interimDashboard: PropTypes.func,
  token: PropTypes.string,
  currentProgramCalendarId: PropTypes.string,
  active_semesters: PropTypes.string,
  activeInstitutionCalendar: PropTypes.object,
};

const mapStateToProps = function (state) {
  // ({ auth, interimCalendar }) => ({

  const { auth, interimCalendar } = state;
  return {
    id: interimCalendar.institution_Calender_Id,
    title: interimCalendar.academic_year_name,
    status: interimCalendar.curriculum,
    year1: interimCalendar.year1,
    year2: interimCalendar.year2,
    year3: interimCalendar.year3,
    year4: interimCalendar.year4,
    year5: interimCalendar.year5,
    year6: interimCalendar.year6,
    active: interimCalendar.active_year,
    active_semesters: interimCalendar.active_semesters,
    isLoading: interimCalendar.isLoading,
    isAuthenticated: auth.token !== null,
    token: auth.token !== null ? auth.token.replace(/"/g, '') : null,
    userRole: auth.loggedInUserData.role,
    currentProgramCalendarId: interimCalendar.currentProgramCalendarId,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, {
  interimDashboard,
  interimChangeTitle,
  interimChangeYear,
  interimLandingApi,
  interimActivateModal,
  loadInterimDataFromInstitution,
})(SetCurriculum);
