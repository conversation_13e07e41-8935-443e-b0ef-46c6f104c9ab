import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import axios from '../../../axios';
import Input from '../../../Widgets/FormElements/Input/Input';
import Loader from '../../../Widgets/Loader/Loader';
import { Button } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import Breadcrumb from '../../../Widgets/Breadcrumb/Breadcrumb';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
import { addDefaultLastName, getLang, isModuleEnabled, lastNameRequired } from 'utils';

const gender = [
  ['Male', 'male'],
  ['Female', 'female'],
];

// let emailVal = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
let emailVal = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

let id;
class EditStaffProfile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      singleId: '',
      fName: '',
      mName: '',
      lName: '',
      empId: '',
      email: '',
      nationalId: '',
      selectGender: gender[0][1][2],
      enrollmentYear: '',
    };
  }

  componentDidMount() {
    id = this.props.location.search.split('=')[1];

    this.fetchApi();
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });
    axios.get(`user/staff/${id}`).then((res) => {
      const data = res.data.data;
      this.setState({
        fName: data.name.first,
        lName: data.name.last,
        mName: data.name.middle,
        userName: data.name.family,
        selectGender: data.gender,
        empId: data.employee_id,
        nationalId: data.address.nationality_id,
        email: data.email,
        enrollmentYear: data.enrollment_year,
        isLoading: false,
      });
    });
  };

  handleChangeText = (e, name) => {
    if (name === 'fName') {
      this.setState({
        fName: e.target.value,
        fNameError: '',
      });
    }

    if (name === 'mName') {
      this.setState({
        mName: e.target.value,
        mNameError: '',
      });
    }
    if (name === 'lName') {
      this.setState({
        lName: e.target.value,
        lNameError: '',
      });
    }
    if (name === 'userName') {
      this.setState({
        userName: e.target.value,
        userNameError: '',
      });
    }
    if (name === 'empId') {
      this.setState({
        empId: e.target.value,
        fempIdError: '',
      });
    }
    if (name === 'nationalId') {
      this.setState({
        nationalId: e.target.value,
        nationalIdError: '',
      });
    }
    if (name === 'email') {
      this.setState({
        email: e.target.value,
        emailError: '',
      });
    }
  };

  validation = () => {
    let space = /^\S$|^\S[ \S]*\S$/;
    const AlphaNum = /^[a-zA-Z0-9]+$/;
    let fNameError = '';
    let mNameError = '';
    let lNameError = '';
    let empIdError = '';
    let nationalIdError = '';
    let emailError = '';
    let selectGenderError = '';
    let userNameError = '';

    // if (this.state.enrollmentYear === "") {
    //   enrollmentYearError = "Year Field is Required";
    // }

    if (this.state.selectGender === 'l') {
      selectGenderError = t('user_management.choose_gender');
    }
    if (!this.state.selectGender) {
      selectGenderError = t('user_management.choose_gender');
    }

    if (!this.state.fName) {
      fNameError = t('user_management.first_name_required');
    } else if (!space.test(this.state.fName)) {
      fNameError = t('user_management.space_not_allowed');
    }
    //  else if (!spaceAlpha.test(this.state.fName)) {
    //   fNameError = "Text Only allowed";
    // }
    else if (this.state.fName.length <= 2) {
      fNameError = t('user_management.minimum_3_char_required');
    }

    //   if (this.state.mName) {
    //    if (!space.test(this.state.mName)) {
    //     mNameError = "Space not allowed beginning & end";
    //   } else if (!spaceAlpha.test(this.state.mName)) {
    //     mNameError = "Text Only allowed";
    //   } else if (this.state.mName.length <= 2) {
    //     mNameError = "Minimum 3 character is required ";
    //   }
    // }

    if (!lastNameRequired()) {
      if (!this.state.lName) {
        lNameError = t('user_management.last_name_required');
      } else if (!space.test(this.state.lName)) {
        lNameError = t('user_management.space_not_allowed');
      }
      // else if (!spaceAlpha.test(this.state.lName)) {
      //   lNameError = "Text Only allowed";
      // }
      // else if (this.state.lName.length <= 2) {
      //   lNameError = t('user_management.minimum_3_char_required');
      // }
    }
    // if (this.state.userName) {
    //   if (!space.test(this.state.userName)) {
    //     userNameError = "Space not allowed beginning & end";
    //   } else if (!spaceAlpha.test(this.state.userName)) {
    //     userNameError = "Text Only allowed";
    //   } else if (this.state.userName.length <= 2) {
    //     userNameError = "Minimum 3 character is required ";
    //   }
    // }

    if (!this.state.email) {
      emailError = t('user_management.email_required');
    } else if (!emailVal.test(this.state.email)) {
      emailError = t('user_management.pls_enter_valid_mail');
    }

    if (!this.state.empId) {
      empIdError = t('user_management.employeeId_required');
    } else if (!space.test(this.state.empId)) {
      empIdError = t('user_management.space_not_allowed');
    } else if (!AlphaNum.test(this.state.empId)) {
      empIdError = t('user_management.special_character_not_allowed');
    } else if (this.state.empId.length <= 0) {
      empIdError = t('user_management.minimum_1_char_required');
    }

    if (isModuleEnabled('NATIONALITY_ID')) {
      if (!this.state.nationalId) {
        nationalIdError = t('user_management.National_Residence_id_required');
      } else if (!space.test(this.state.nationalId)) {
        nationalIdError = t('user_management.space_not_allowed');
      } else if (!AlphaNum.test(this.state.nationalId)) {
        nationalIdError = t('user_management.special_character_not_allowed');
      } else if (this.state.nationalId.length <= 4) {
        nationalIdError = t('user_management.minimum_5_char_required');
      }
    }
    if (
      fNameError ||
      mNameError ||
      lNameError ||
      empIdError ||
      nationalIdError ||
      emailError ||
      selectGenderError ||
      userNameError
    ) {
      this.setState({
        fNameError,
        mNameError,
        lNameError,
        empIdError,
        nationalIdError,
        emailError,
        selectGenderError,
        userNameError,
      });
      return false;
    }
    return true;
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
        selectGenderError: '',
      });
    }
  };

  handleSingleEditSubmit = (e) => {
    e.preventDefault();
    this.setState({
      isValidate: false,
    });
    if (this.validation()) {
      const data = {
        id: this.state.singleId,
        first_name: this.state.fName,
        last_name: addDefaultLastName(this.state.lName),
        middle_name: this.state.mName,
        family: this.state.userName,
        gender: this.state.selectGender,
        employee_id: this.state.empId,
        email: this.state.email,
        nationality_id: this.state.nationalId,
        enrollment_year: this.state.enrollmentYear,
      };

      this.setState({
        isLoading: true,
      });
      axios
        .put(`user/${id}`, data)
        .then((res) => {
          this.setState({
            isLoading: false,
          });
          this.props.history.push({
            pathname: '/staff/management',
            state: {
              completeView: false,
              pendingView: true,
              inactiveView: false,
              selectedTab: this.props.location.state !== undefined ? this.props.location.state : 0,
            },
          });
          NotificationManager.success(t('user_management.edited_success'));
        })
        .catch((error) => {
          NotificationManager.error(`${error.response.data.data}`);
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  handleChangeEnrollmentYear = (e, name, date) => {
    e.preventDefault();
    // this.setState({
    //   enrollmentYear: date,
    // });

    if (name === 'DOB') {
      this.setState({
        enrollmentYear: e.target.value,
        enrollmentYearError: '',
      });
    }
  };

  handleGoBack = () => {
    this.props.history.push({
      pathname: '/staff/management',
      state: {
        completeView: false,
        pendingView: true,
        inactiveView: false,
        selectedTab: this.props.location.state !== undefined ? this.props.location.state : 0,
      },
    });
  };

  render() {
    gender[0][0] = t('infra_management.gender.Male');
    gender[1][0] = t('infra_management.gender.Female');
    const items = [
      { to: '/staff/management', label: t('staff_management') },
      { to: '/', label: t('user_management.edit_staff') },
    ];
    const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
    const userSensitiveData = isModuleEnabled('USER_SENSITIVE');
    return (
      <React.Fragment>
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <div className="main pt-5 pb-5">
          <Loader isLoading={this.state.isLoading} />

          <div className="container">
            <div className="float-left pt-1 pb-3">
              {/* <Link to="/staff/management">← Back to Staff List</Link> */}
            </div>

            <div className="">
              <div className="float-left white p-2">
                {/* singel entry edit funtion start   */}

                <div className="row w-100">
                  <div className="col-md-6 pt-1 d-flex">
                    <Trans i18nKey={'user_management.edit_below_profile'} />
                  </div>
                  <div className="col-md-6">
                    <div className="float-right">
                      <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                        <Trans i18nKey={'back_cc'} />
                      </Button>

                      <Button onClick={(e) => this.handleSingleEditSubmit(e)}>
                        {' '}
                        <Trans i18nKey={'events.submit'} />{' '}
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={25}
                      value={this.state.fName}
                      floatingLabel={t('first_name')}
                      changed={(e) => this.handleChangeText(e, 'fName')}
                      feedback={this.state.fNameError}
                    />
                  </div>
                  <div className="col-md-6">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={25}
                      value={this.state.mName}
                      floatingLabel={t('middle_name')}
                      changed={(e) => this.handleChangeText(e, 'mName')}
                      feedback={this.state.mNameError}
                    />
                  </div>

                  <div className="col-md-6">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={25}
                      value={this.state.lName}
                      floatingLabel={!lastNameRequired() ? 'Last Name' : t('last_name')}
                      changed={(e) => this.handleChangeText(e, 'lName')}
                      feedback={this.state.lNameError}
                    />
                  </div>

                  <div className="col-md-6">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={25}
                      value={this.state.userName}
                      floatingLabel={t('family_name')}
                      changed={(e) => this.handleChangeText(e, 'userName')}
                      feedback={this.state.userNameError}
                    />
                  </div>

                  <div className="col-md-6">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={25}
                      value={this.state.empId}
                      floatingLabel={t('employee_id')}
                      changed={(e) => this.handleChangeText(e, 'empId')}
                      feedback={this.state.empIdError}
                    />
                  </div>
                  {userSensitiveData && (
                    <div className="col-md-6">
                      <Input
                        elementType={'floatinginput'}
                        elementConfig={{
                          type: 'text',
                        }}
                        maxLength={25}
                        value={this.state.nationalId}
                        floatingLabel={t('national/residence', {
                          optional: !hasNationalIdValidation ? '(Optional)' : '',
                        })}
                        changed={(e) => this.handleChangeText(e, 'nationalId')}
                        feedback={this.state.nationalIdError}
                      />
                    </div>
                  )}
                  <div className="col-md-6">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={65}
                      value={this.state.email}
                      floatingLabel={t('emailId')}
                      changed={(e) => this.handleChangeText(e, 'email')}
                      feedback={this.state.emailError}
                    />
                  </div>

                  <div className={`col-md-6 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                    <label className="mb-0">
                      {' '}
                      <Trans i18nKey={'gender'} />
                    </label>
                    <Input
                      elementType={'radio'}
                      elementConfig={gender}
                      className={'form-radio1'}
                      selected={this.state.selectGender}
                      labelclass="radio-label2"
                      onChange={(e) => this.onRadioGroupChange(e, 'selectGender')}
                      feedback={this.state.selectGenderError}
                      removeLabel={'label-remove1'}
                    />
                  </div>
                </div>

                {/* singel entry edit funtion end */}
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

EditStaffProfile.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
};

export default withRouter(EditStaffProfile);
