import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/assessment/action';
import {
  selectReportPrograms,
  selectProgramLevelCourse,
  selectCourseReport,
} from '_reduxapi/assessment/selector';
import { selectInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { fromJS, List, Map } from 'immutable';
import AttainmentReportChart from './AttainmentReportChart';
import AttainmentReportTable from './AttainmentReportTable';
import { ucFirst, getURLParams, indVerRename } from 'utils';
import AttainmentPoReportTable from './AttainmentPoReportTable';
import AutoComplete from 'Widgets/FormElements/material/Autocomplete';
import { CheckPermission } from 'Modules/Shared/Permissions';
import ReportButtons from './ReportButtons';
import LocalStorageService from 'LocalStorageService';

function AttainmentReport({
  reportPrograms,
  getReportPrograms,
  institutionCalendarLists,
  programLevelCourse,
  getProgramLevelCourse,
  courseProgramReport,
  getCourseProgramReport,
  getCloCourseProgramReport,
  setData,
  generateReport,
}) {
  const [selectValues, setSelectValues] = useState(Map());
  const [optionsList, setOptionsList] = useState(
    fromJS({
      attainmentType: [
        {
          label: 'Course Attainment',
          name: 'Course Attainment',
          value: 'Course Attainment',
        },
        {
          label: 'Class Attainment',
          name: 'Class Attainment',
          value: 'Class Attainment',
        },
        {
          label: 'Student Attainment',
          name: 'Student Attainment',
          value: 'Student Attainment',
        },
      ],
      year: institutionCalendarLists.map((item) => ({
        label: `AY - ${item.get('calendar_name', '')}`,
        name: `AY - ${item.get('calendar_name', '')}`,
        value: item.get('_id', ''),
      })),
    })
  );
  const [selectedNode, setSelectedNode] = useState('-');
  const [selectedType, setSelectedType] = useState('-');
  const [questionList, setQuestionList] = useState(List());
  const [selectedId, setSelectedId] = useState(List());
  const [selectedCoTreeId, setSelectedCoTreeId] = useState(List());
  const type = getURLParams('type', true);

  useEffect(() => {
    getReportPrograms();
    return () => setData(fromJS({ courseProgramReport: {} }));
  }, []); //eslint-disable-line

  useEffect(() => {
    const programOptions = reportPrograms.map((item) =>
      Map({
        label: item.get('programName', ''),
        name: item.get('programName', ''),
        value: item.get('_id', ''),
        attainmentList: item.get('attainmentList', List()),
        term: item.get('term', List()),
      })
    );
    setOptionsList(optionsList.set('program', programOptions));
  }, [reportPrograms]); //eslint-disable-line

  useEffect(() => {
    const yearOptions = institutionCalendarLists.map((item) =>
      Map({
        label: `${
          selectValues.getIn(['outcome', 'value'], '') === 'clo' ? 'AY' : 'GY'
        } - ${item.get('calendar_name', '')}`,
        name: `AY - ${item.get('calendar_name', '')}`,
        value: item.get('_id', ''),
      })
    );
    setOptionsList(optionsList.set('year', yearOptions));
  }, [institutionCalendarLists, selectValues]); //eslint-disable-line

  useEffect(() => {
    if (selectValues.get('program', '')) {
      const levelOptions = programLevelCourse.map((item) =>
        Map({
          label: item.get('level', ''),
          name: item.get('level', ''),
          value: item.get('level', ''),
          courses: item.get('courses', List()),
        })
      );
      setOptionsList(optionsList.set('level', levelOptions));
    }
  }, [programLevelCourse]); //eslint-disable-line

  useEffect(() => {
    fetchApi();
  }, [selectValues]); //eslint-disable-line

  const getFilteredItems = (name) => {
    const selectedData = LocalStorageService.getCustomToken(name, true);
    if (selectedData !== null) {
      return selectedData;
    }
  };

  const getFilteredReportDetails = () => {
    const selectedValues = getFilteredItems('selectValues');
    const optionsListDetails = getFilteredItems('optionsList');
    const removeFilteredItems = () => {
      const items = ['selectValues', 'optionsList'];
      items.forEach((item) => localStorage.removeItem(item));
    };
    if (selectedValues && type === 'course') {
      setSelectValues(Map(selectedValues));
      setOptionsList(fromJS(optionsListDetails));
      removeFilteredItems();
    } else {
      removeFilteredItems();
    }
  };

  useEffect(() => {
    getFilteredReportDetails();
  }, []); //eslint-disable-line

  const programId = selectValues.getIn(['program', 'value'], '');
  const attainmentId = selectValues.getIn(['attainment', 'value'], '');
  const outCome = selectValues.getIn(['outcome', 'value'], '');
  const institutionCalendarId = selectValues.getIn(['year', 'value'], '');
  const term = selectValues.getIn(['term', 'value'], '');
  const level = selectValues.getIn(['level', 'value'], '');
  const courseId = selectValues.getIn(['course', 'value'], '');
  const attainmentType = selectValues.getIn(['attainmentType', 'value'], '');

  const fetchApi = (
    selectedIds,
    callback,
    selectedCoTreeIds,
    filterType,
    selAssessment,
    selCoTree
  ) => {
    if (
      programId &&
      attainmentId &&
      outCome &&
      institutionCalendarId &&
      term &&
      attainmentType &&
      outCome
    ) {
      const requestData = {
        programId: programId,
        attainmentId: attainmentId,
        outCome: outCome,
        institutionCalendarId: institutionCalendarId,
        term: term,
        ...(outCome === 'clo' && { level: level }),
        ...(outCome === 'clo' && { courseId: courseId }),
        ...(outCome === 'clo' && { assessmentIds: selectedIds }),
        ...(outCome === 'plo' &&
          selectedIds !== undefined && {
            assessmentIds: filterType === 'coTree' ? selAssessment : selectedIds,
          }),
        ...(outCome === 'plo' &&
          selectedCoTreeIds !== undefined && {
            coTreeIds:
              filterType === 'assessment' || filterType === 'checkAll'
                ? selCoTree
                : selectedCoTreeIds,
          }),
      };

      if (outCome === 'clo' && courseId && level) {
        getCloCourseProgramReport({
          requestData,
          callback,
          attainmentType,
          outCome,
        });
      } else if (outCome === 'plo' && attainmentType === 'Course Attainment') {
        getCourseProgramReport({
          programId,
          attainmentId,
          outCome,
          institutionCalendarId,
          term,
          level,
          courseId,
        });
      } else if (outCome === 'plo' && attainmentType === 'Class Attainment') {
        getCloCourseProgramReport({
          requestData,
          callback,
          attainmentType,
          outCome,
        });
      } else {
        setData(fromJS({ courseProgramReport: {} }));
      }
    }
  };

  const getLevelAndCourse = (values) => {
    const programId = values.getIn(['program', 'value'], '');
    const calendarId = values.getIn(['year', 'value'], '');
    const term = values.getIn(['term', 'value'], '');
    if (programId && calendarId && term && selectValues.getIn(['outcome', 'value'], '') === 'clo')
      getProgramLevelCourse({ programId, calendarId, term });
  };

  const handleChange = (name, val) => {
    const value = val.value;
    // const value = e.target.value;
    let newValue = selectValues.set(name, val);
    switch (name) {
      case 'program': {
        const programSelected = optionsList
          .get('program', List())
          .find((item) => item.get('value', '') === value);
        const attainmentOptions = programSelected?.get('attainmentList', List()).map((item) =>
          Map({
            label: item.get('regulationName', ''),
            name: item.get('regulationName', ''),
            value: item.get('_id', ''),
            outcomes: item.get('outcomes', ''),
          })
        );
        const termOptions = programSelected?.get('term', List()).map((item) =>
          Map({
            label: ucFirst(item),
            name: ucFirst(item),
            value: item,
          })
        );
        newValue = newValue
          .set('attainment', '')
          .set('outcome', '')
          .set('term', '')
          .set('level', '')
          .set('course', '')
          .set('attainmentType', '')
          .set('year', '');
        setOptionsList(
          optionsList
            .set('attainment', attainmentOptions || List())
            .set('outcome', List())
            .set('term', termOptions || List())
            .set('level', List())
            .set('course', List())
        );
        setData(fromJS({ courseProgramReport: {} }));
        break;
      }
      case 'attainment': {
        const outcomeOptions = optionsList
          .get('attainment', List())
          .find((item) => item.get('value', '') === value)
          ?.get('outcomes', List())
          .map((item) =>
            Map({
              label: indVerRename(item.toUpperCase(), programId),
              name: item.toUpperCase(),
              value: item,
            })
          );
        newValue = newValue.set('outcome', '');
        setOptionsList(optionsList.set('outcome', outcomeOptions || List()));
        break;
      }
      case 'outcome': {
        newValue = newValue
          .set('term', '')
          .set('level', '')
          .set('course', '')
          .set('attainmentType', '')
          .set('year', '');
        setSelectedNode('-');
        setData(fromJS({ courseProgramReport: {} }));
        break;
      }
      case 'year': {
        newValue = newValue
          .set('term', '')
          .set('level', '')
          .set('course', '')
          .set('attainmentType', '');
        setData(fromJS({ courseProgramReport: {} }));
        break;
      }
      case 'term': {
        newValue = newValue.set('level', '').set('course', '');
        setOptionsList(optionsList.set('level', List()).set('course', List()));
        getLevelAndCourse(newValue);
        break;
      }
      case 'level': {
        const courseOptions = optionsList
          .get('level', List())
          .find((item) => item.get('value', '') === value)
          ?.get('courses', List())
          .map((item) =>
            Map({
              label: item.get('courses_name', ''),
              name: item.get('courses_name', ''),
              value: item.get('_course_id', ''),
            })
          );
        newValue = newValue.set('course', '');
        setOptionsList(optionsList.set('course', courseOptions || List()));
        break;
      }
      case 'course': {
        setSelectedNode('');
        setSelectedType('');
        break;
      }
      case 'attainmentType': {
        setSelectedNode('');
        setSelectedType('');
        setData(fromJS({ courseProgramReport: {} }));
        break;
      }

      default:
        break;
    }
    setSelectValues(newValue);
  };

  const getPermissionData = (label) => {
    return CheckPermission('pages', 'Attainment Calculator', 'Attainment Report', label);
  };

  const cloPermission = getPermissionData('CLO View');
  const ploPermission = getPermissionData('PLO View');
  const classPermission = getPermissionData('Class Attainment');
  const studentPermission = getPermissionData('Student Attainment');
  const coursePermission = getPermissionData('Course Attainment');

  const SelectInput = ({ name }) => {
    const getOptionsList = () => {
      const optionsListAr = optionsList.get(name, List()).toJS();
      const getFilterOutcome = ({ cloPermission, ploPermission }) => {
        return optionsList
          .get('outcome', List())
          .filter((item) =>
            [cloPermission, ploPermission].includes(item.get('value', '').toLowerCase())
          )
          .toJS();
      };
      const getFilterAttainment = ({ classPermission, studentPermission, coursePermission }) => {
        return optionsList
          .get('attainmentType', List())
          .filter((item) =>
            [classPermission, studentPermission, coursePermission].includes(item.get('value', ''))
          )
          .toJS();
      };
      if (name === 'outcome') {
        return getFilterOutcome({
          cloPermission: cloPermission ? 'clo' : true,
          ploPermission: ploPermission ? 'plo' : true,
        });
      } else if (name === 'attainmentType') {
        return getFilterAttainment({
          classPermission: classPermission ? 'Class Attainment' : true,
          studentPermission: outCome === 'clo' && studentPermission ? 'Student Attainment' : true,
          coursePermission: outCome === 'plo' && coursePermission ? 'Course Attainment' : true,
        });
      } else return [...optionsListAr];
    };

    return (
      <div
        className={`${selectValues.getIn(['outcome', 'value'], '') === 'clo' ? 'w-13' : 'w-20'} ${
          name !== 'attainmentType' ? 'pr-2' : ''
        }`}
      >
        <AutoComplete
          options={getOptionsList()}
          value={selectValues.get(name, '')}
          isClearable={false}
          placeholder={`Select ${
            name === 'attainmentType'
              ? 'Attainment'
              : name === 'attainment'
              ? 'Regulation'
              : name === 'year'
              ? 'Academic Year'
              : ucFirst(name)
          }`}
          onChange={(e, val) => handleChange(name, val)}
          isOptionEqualToValue={(option, value) => option?.value === value?.value}
          className={`bg-white AutoCompleteArrow`}
        />
      </div>
    );
  };
  SelectInput.propTypes = {
    name: PropTypes.string,
  };

  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="container">
        <div className="d-flex flex-row pt-3 pb-1">
          <SelectInput name="program" />
          <SelectInput name="attainment" />
          <SelectInput name="outcome" />
          <SelectInput name="year" />
          <SelectInput name="term" />
          {selectValues.getIn(['outcome', 'value'], '') === 'clo' && (
            <>
              <SelectInput name="level" />
              <SelectInput name="course" />
            </>
          )}
          <SelectInput name="attainmentType" />
        </div>
        <ReportButtons
          generateReport={generateReport}
          selectValues={selectValues}
          optionsList={optionsList}
          courseProgramReport={courseProgramReport}
        />
        <AttainmentReportChart
          selectedNode={selectedNode}
          setSelectedNode={setSelectedNode}
          outcome={selectValues.getIn(['outcome', 'value'], '')}
          courseProgramReport={courseProgramReport}
          setSelectedType={setSelectedType}
          questionList={questionList}
          setQuestionList={setQuestionList}
          selectedType={selectedType}
          selectValues={selectValues}
        />
        {courseProgramReport.size > 0 && (
          <>
            {outCome === 'clo' || (outCome === 'plo' && attainmentType === 'Class Attainment') ? (
              <AttainmentReportTable
                selectedNode={selectedNode}
                courseProgramReport={courseProgramReport}
                selectValues={selectValues}
                selectedType={selectedType}
                questionList={questionList}
                setQuestionList={setQuestionList}
                selectedId={selectedId}
                setSelectedId={setSelectedId}
                fetchApi={fetchApi}
                selectedCoTreeId={selectedCoTreeId}
                setSelectedCoTreeId={setSelectedCoTreeId}
              />
            ) : (
              <AttainmentPoReportTable
                selectedNode={selectedNode}
                courseProgramReport={courseProgramReport}
                selectValues={selectValues}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
}

AttainmentReport.propTypes = {
  reportPrograms: PropTypes.instanceOf(List),
  getReportPrograms: PropTypes.func,
  institutionCalendarLists: PropTypes.instanceOf(List),
  programLevelCourse: PropTypes.instanceOf(List),
  getProgramLevelCourse: PropTypes.func,
  courseProgramReport: PropTypes.instanceOf(Map),
  getCourseProgramReport: PropTypes.func,
  getCloCourseProgramReport: PropTypes.func,
  setData: PropTypes.func,
  generateReport: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    reportPrograms: selectReportPrograms(state),
    institutionCalendarLists: selectInstitutionCalendar(state),
    programLevelCourse: selectProgramLevelCourse(state),
    courseProgramReport: selectCourseReport(state),
  };
};

export default connect(mapStateToProps, actions)(AttainmentReport);
