import React, { useState } from 'react';
import PropTypes from 'prop-types';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import FormLabel from '@mui/material/FormLabel';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import { labelFieldValidation } from '../utils';
import CancelModal from 'Containers/Modal/Cancel';
import { List, Map } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';

const LabelRenameModal = ({
  show,
  onClose,
  selectedLabel,
  settingId,
  updateLabelField,
  setData,
  selectedLanguages,
  institutionHeader,
  type,
}) => {
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [labels, setLabels] = useState(selectedLabel.labels);
  const [edited, setEdited] = useState(false);

  const getLabelValue = (code) => {
    const langIndex = labels.findIndex((lang) => lang.get('language') === code);
    if (langIndex !== -1) return labels.getIn([langIndex, 'label']);
    else return '';
  };

  const isDisable = () => {
    return !edited || labels.every((item) => !item.get('label').trim());
  };

  const handleChange = (e, code) => {
    const value = e.target.value;
    const langIndex = labels.findIndex((lang) => lang.get('language') === code);
    if (langIndex !== -1) setLabels(labels.setIn([langIndex, 'label'], value));
    else setLabels(labels.push(Map({ language: code, label: value })));
    setEdited(true);
  };

  const handleSave = () => {
    const { labelId, labelType, labelName } = selectedLabel;
    const translatedLabels = labels
      .filter((item) => item.get('label').trim() !== '')
      .toJS()
      .map(({ label, language }) => ({ label: label.trim(), language }));

    if (labelFieldValidation(translatedLabels, setData)) {
      updateLabelField({
        formData: {
          settingId,
          labelType,
          labelName,
          translatedLabels,
        },
        labelId,
        callBack: () => onClose(),
        headers: institutionHeader,
        type,
      });
    }
  };

  const handleCancel = () => {
    edited ? setShowCancelModal(true) : onClose();
  };

  return (
    <>
      <DialogModal show={show} onClose={handleCancel} maxWidth={'xs'} fullWidth={true}>
        <div className="p-4">
          <div className="digi-text-color">
            <p className="f-22">
              <Trans i18nKey={'global_configuration.rename'} />
            </p>
            {selectedLanguages.map((lang, index) => (
              <div key={index} className="digi-mb-20">
                <FormLabel component="legend">
                  <span className="ml-1">
                    <Trans i18nKey={'global_configuration.label_name'} />
                  </span>
                  <span className="float-right mr-1">{lang.get('name')}</span>
                </FormLabel>
                <MaterialInput
                  elementType={'materialInput'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  placeholder={lang.get('name')}
                  value={getLabelValue(lang.get('code'))}
                  changed={(e) => handleChange(e, lang.get('code'))}
                  maxLength={50}
                />
              </div>
            ))}
            <div className="text-right mt-4">
              <MButton color="gray" variant="outlined" className="mr-2" clicked={handleCancel}>
                <Trans i18nKey={'cancel'} />
              </MButton>

              <MButton
                color="primary"
                variant="contained"
                clicked={handleSave}
                disabled={isDisable()}
              >
                <Trans i18nKey={'save'} />
              </MButton>
            </div>
          </div>
        </div>
      </DialogModal>

      <CancelModal showCancel={showCancelModal} setCancel={setShowCancelModal} setShow={onClose} />
    </>
  );
};

LabelRenameModal.propTypes = {
  show: PropTypes.bool,
  onClose: PropTypes.func,
  selectedLabel: PropTypes.object,
  settingId: PropTypes.string,
  type: PropTypes.string,
  updateLabelField: PropTypes.func,
  setData: PropTypes.func,
  selectedLanguages: PropTypes.instanceOf(List),
  institutionHeader: PropTypes.object,
};

export default LabelRenameModal;
