import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { t } from 'i18next';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { But<PERSON>, Accordion, Card, Form } from 'react-bootstrap';
import * as actions from '../../../_reduxapi/leave_management/actions';
import { selectPermissionList } from '../../../_reduxapi/leave_management/selectors';
import '../../../Assets/css/grouping.css';
import { getLang } from 'utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
const lang = getLang();

class ReportAbsence extends Component {
  constructor(props) {
    super(props);
    this.state = {
      show: false,
      oldData: Map(),
      savebutton: false,
    };
  }

  openToggle = () => {
    this.setState({
      show: !this.state.show,
    });
  };

  componentDidMount() {
    const { type, getAllPermissionList } = this.props;
    getAllPermissionList(type);
  }

  handleCheckbox = (e) => {
    this.setState({ checked: e.target.checked, savebutton: !this.state.savebutton });

    const { permissionList } = this.props;

    const { oldData } = this.state;
    const oldObj = Map({
      is_permission_attachment_required: permissionList.get('is_permission_attachment_required'),
      is_scheduled: permissionList.get('is_scheduled'),
      permission_frequency: permissionList.get('permission_frequency'),
      permission_frequency_by: permissionList.get('permission_frequency_by'),
      permission_hours: permissionList.get('permission_hours'),
      report_absence_document: permissionList.get('report_absence_document'),
    });
    if (oldData.isEmpty()) {
      this.setState({ oldData: oldObj });
    }

    const permissionObj = Map({
      is_permission_attachment_required: permissionList.get('is_permission_attachment_required'),
      is_scheduled: permissionList.get('is_scheduled'),
      permission_frequency: permissionList.get('permission_frequency'),
      permission_frequency_by: permissionList.get('permission_frequency_by'),
      permission_hours: permissionList.get('permission_hours'),
      report_absence_document: e.target.checked,
    });
    this.props.setData(
      Map({
        permissionList: permissionObj,
      })
    );
  };

  handleCancel = () => {
    const { permissionList } = this.props;
    const permissionObj = Map({
      is_permission_attachment_required: permissionList.get('is_permission_attachment_required'),
      is_scheduled: permissionList.get('is_scheduled'),
      permission_frequency: permissionList.get('permission_frequency'),
      permission_frequency_by: permissionList.get('permission_frequency_by'),
      permission_hours: permissionList.get('permission_hours'),
      report_absence_document: permissionList.get('report_absence_document', false),
    });

    this.props.setData(
      Map({
        permissionList: permissionObj,
      })
    );
  };

  handleSave = () => {
    const { permissionList, updateReportAbsence, getAllPermissionList } = this.props;
    const { savebutton } = this.state;
    const data = {
      report_absence_document: permissionList.get('report_absence_document', false),
    };
    updateReportAbsence(data, () => {
      getAllPermissionList(this.props.type);
    });
    this.setState({ savebutton: !savebutton });
  };
  render() {
    const { show, oldData, savebutton } = this.state;
    const { permissionList } = this.props;
    return (
      <React.Fragment>
        <Accordion defaultActiveKey="">
          <Card className="rounded">
            <Accordion.Toggle
              as={Card.Header}
              eventKey="0"
              className="icon_remove_leave"
              onClick={this.openToggle}
            >
              <div className="d-flex justify-content-between">
                <p className="mb-0">{t('side_nav.menus.report_absence')}</p>

                <p className="mb-0">
                  {show ? (
                    <i className="fa fa-chevron-down f-14" aria-hidden="true"></i>
                  ) : (
                    <i
                      className={`fa fa-chevron-${lang === 'ar' ? 'down' : 'up'} fa-rotate-90 f-14`}
                      aria-hidden="true"
                    ></i>
                  )}
                </p>
              </div>
            </Accordion.Toggle>
            <Accordion.Collapse eventKey="0" className="bg-white ">
              <Card.Body className=" innerbodyLeave border-top-remove">
                <div className="container-fluid ">
                  <div className="row">
                    <div className="col-md-6 col-xl-5">
                      <div className="mb-3">
                        <div className="pt-1 pb-3 text-left">
                          <Form.Check
                            type="checkbox"
                            className="pt-3"
                            checked={permissionList.get('report_absence_document', false)}
                            onChange={(e) => this.handleCheckbox(e)}
                            label={t('leaveManagement.attachmentMandatoryReport')}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-12">
                    <div className="float-right pb-2">
                      {/* {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Staff',
                        '',
                        'Report Absence',
                        'Reset'
                      ) && (
                        <b className="pr-3">
                          <Button variant="outline-secondary" onClick={() => this.handleCancel()}>
                            CANCEL
                          </Button>
                        </b>
                      )} */}

                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Staff',
                        '',
                        'Report Absence',
                        'Edit'
                      ) &&
                        !oldData.isEmpty() &&
                        savebutton && (
                          <b className="">
                            <Button variant={'primary'} onClick={() => this.handleSave()}>
                              {t('curriculum_keys.save')}
                            </Button>
                          </b>
                        )}
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Accordion.Collapse>
          </Card>
        </Accordion>
      </React.Fragment>
    );
  }
}

ReportAbsence.propTypes = {
  permissionList: PropTypes.instanceOf(Map),
  type: PropTypes.string,
  getAllPermissionList: PropTypes.func,
  setData: PropTypes.func,
  updateReportAbsence: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    permissionList: selectPermissionList(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ReportAbsence);
