import React, { useEffect } from 'react';
import * as echarts from 'echarts';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import { criteriaLabels } from 'Modules/GlobalConfigurationV1/components/UserActivityBoard/utils';
const MyChart = ({ criteriaAverageScore }) => {
  useEffect(() => {
    if (!criteriaAverageScore.size) return;
    // Initialize ECharts
    const chartDom = document.getElementById('leader_criteria_chart');
    const myChart = echarts.init(chartDom);
    const data = {
      criteriaName: [],
      criteriaScore: [],
    };
    for (const [index, score] of criteriaAverageScore.entries()) {
      data['criteriaName'][index] = criteriaLabels[score.get('criteriaName', '')];
      data['criteriaScore'][index] = Number(score.get('criteriaScore', ''));
    }
    // ECharts configuration
    const option = {
      // title: {
      //   text: 'All Parameters Average Score',
      // },
      xAxis: {
        fontSize: 16,
        type: 'category',
        boundaryGap: false,
        data: data['criteriaName'],
        axisLabel: {
          // Adjust font size as needed
          fontSize: 12,
          interval: 0,
          align: 'left',
        },
      },
      yAxis: {
        fontSize: 10,
        type: 'value',
        // name: 'Scores', // Set the name for the y-axis
        axisLabel: {
          formatter: function (value) {
            // Convert the value to a string and pad with leading zeros if necessary
            return value.toLocaleString('en-US', { minimumIntegerDigits: 2, useGrouping: false });
          },
        },
        max: 10,
        interval: 1,
      },
      series: [
        {
          data: data['criteriaScore'],
          type: 'line',
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(151, 182, 241, 1)' },
              { offset: 1, color: 'rgba(205, 220, 248, 0.3)' },
            ]),
          },
        },
      ],
    };

    // Set ECharts option
    myChart.setOption(option);

    // Clean up ECharts instance on component unmount
    return () => {
      myChart.dispose();
    };
  }, [criteriaAverageScore]); // Run this effect only once when the component mounts

  return <div id="leader_criteria_chart" style={{ width: 'cover', height: '340px' }} />;
};

export default MyChart;
MyChart.propTypes = {
  criteriaAverageScore: PropTypes.instanceOf(List),
};
