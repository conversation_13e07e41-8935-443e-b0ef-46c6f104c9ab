import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { Button } from 'react-bootstrap';

import InfrastructureListTable from './InfrastructureListTable';
import * as actions from '../../../_reduxapi/infrastructure/actions';
import {
  selectInfrastructures,
  selectProgramDeptSubjects,
  selectTimeGroups,
  selectBuildingsAndHospitals,
  selectAddedInfrastructures,
  selectEditedInfrastructures,
} from '../../../_reduxapi/infrastructure/selectors';
import '../../../Assets/css/grouping.css';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getLang } from '../../../utils';

const lang = getLang();
class InfrastructureList extends Component {
  constructor() {
    super();
    this.state = {
      page: 1,
      search: '',
      pageSize: 10,
    };
    this.infrastructureListTableRef = React.createRef();
  }

  componentDidMount() {
    this.props.getProgramDeptSub();
    this.props.getTimeGroups(1, 1000);
    this.props.getBuildingsAndHospitals();
  }

  navigateToInfraSettings() {
    this.props.history.push('/infrastructure-management/settings');
    this.props.setData(Map({ addedInfrastructures: Map(), editedInfrastructures: Map() }));
  }

  addInfrastructure() {
    this.infrastructureListTableRef.current.addInfrastructure();
  }

  disableAddInfrastructure() {
    const { buildingsAndHospitals, timeGroups } = this.props;
    const timings = timeGroups.get('data', List());
    return (
      buildingsAndHospitals.isEmpty() || timings.filter((t) => t.get('type') === 'onsite').isEmpty()
    );
  }
  handleSearchKeyUp(searchKey) {
    this.setState({ search: searchKey, page: 1 }, () => {
      this.doSearch();
    });
  }
  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      setTimeout(() => {
        const { page, pageSize, search } = this.state;
        this.props.getInfrastructureList(page, pageSize, search);
      }, 500);
    }, 500);
  };

  render() {
    const {
      infrastructures,
      getInfrastructureList,
      updateInfrastructure,
      programDeptSubjects,
      timeGroups,
      buildingsAndHospitals,
      addedInfrastructures,
      editedInfrastructures,
      setData,
    } = this.props;
    return (
      <div className="main pt-3 pb-5 bg-white">
        <div className="container">
          <div className="row pb-4">
            <div className="col-md-3 ">
              <div className="float-left">
                <p className="font-weight-bold mt-2 f-18 mb-0">
                  <Trans i18nKey={'infra_management.list'}></Trans>
                </p>
              </div>
            </div>
            <div className="col-md-9 ">
              <div className="row pb-2 float-right">
                <div className="col-xs-6 ">
                  <input
                    type="text"
                    className={lang === 'en' ? 'searchTerm-Infra' : 'searchTerm-Infra-rtl'}
                    id="myInput"
                    placeholder={t('infra_management.search_placeholder')}
                    value={this.state.search}
                    onChange={(e) => this.handleSearchKeyUp(e.target.value)}
                  />
                  <i className="fa fa-search searchIcon" />
                </div>
                <div className="col-xs-6">
                  {CheckPermission('pages', 'Infrastructure Management', 'Onsite', 'Add') && (
                    <b className="pr-2">
                      <Button
                        disabled={this.disableAddInfrastructure()}
                        variant={this.disableAddInfrastructure() ? 'secondary' : 'primary'}
                        className="border-radious-8 f-15"
                        onClick={this.addInfrastructure.bind(this)}
                      >
                        <Trans i18nKey={'infra_management.buttons.add'}></Trans>
                      </Button>
                    </b>
                  )}
                  {CheckPermission(
                    'tabs',
                    'Infrastructure Management',
                    'Onsite',
                    '',
                    'Settings',
                    'View'
                  ) && (
                    <b className="pr-2">
                      <Button
                        variant="outline-primary"
                        onClick={this.navigateToInfraSettings.bind(this)}
                        className="border-radious-8 f-15"
                      >
                        <Trans i18nKey={'infra_management.buttons.settings'}></Trans>
                      </Button>
                    </b>
                  )}
                </div>
              </div>
            </div>
          </div>
          <InfrastructureListTable
            ref={this.infrastructureListTableRef}
            infrastructures={infrastructures}
            getInfrastructureList={getInfrastructureList}
            updateInfrastructure={updateInfrastructure}
            programDeptSubjects={programDeptSubjects}
            timings={timeGroups.get('data', List())}
            buildingsAndHospitals={buildingsAndHospitals}
            addedInfrastructures={addedInfrastructures}
            editedInfrastructures={editedInfrastructures}
            setData={setData}
          />
        </div>
      </div>
    );
  }
}

InfrastructureList.propTypes = {
  history: PropTypes.object,
  getInfrastructureList: PropTypes.func,
  getProgramDeptSub: PropTypes.func,
  getTimeGroups: PropTypes.func,
  getBuildingsAndHospitals: PropTypes.func,
  updateInfrastructure: PropTypes.func,
  infrastructures: PropTypes.instanceOf(Map),
  programDeptSubjects: PropTypes.instanceOf(List),
  timeGroups: PropTypes.instanceOf(Map),
  buildingsAndHospitals: PropTypes.instanceOf(List),
  addedInfrastructures: PropTypes.instanceOf(Map),
  editedInfrastructures: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    infrastructures: selectInfrastructures(state),
    programDeptSubjects: selectProgramDeptSubjects(state),
    timeGroups: selectTimeGroups(state),
    buildingsAndHospitals: selectBuildingsAndHospitals(state),
    addedInfrastructures: selectAddedInfrastructures(state),
    editedInfrastructures: selectEditedInfrastructures(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(InfrastructureList);
