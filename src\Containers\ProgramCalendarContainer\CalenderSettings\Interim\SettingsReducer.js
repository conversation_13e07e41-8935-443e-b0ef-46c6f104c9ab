import moment from 'moment';
const rootReducer = (state, action) => {
  const {
    type,
    payload,
    year,
    name,
    semester,
    indexPlace,
    id,
    index,
    from,
    level_no,
    term,
    events,
    min,
    max,
    activeSemester,
  } = action;

  switch (type) {
    /*USED CASES*/
    case 'CLEAR_MODAL':
      return {
        ...state,
        modal: false,
      };
    case 'ON_TYPE':
      return {
        ...state,
        edit: {
          ...state.edit,
          [name]: payload,
        },
      };
    case 'OFF':
      return {
        ...state,
        modal: false,
      };
    case 'INITIAL_INTERIM_LOAD':
      return {
        ...state,
        year1: payload.year1,
        year2: payload.year2,
        year3: payload.year3,
        year4: payload.year4,
        year5: payload.year5,
        year6: payload.year6,
      };
    case 'ON_INPUT': {
      let copyOldState = { ...state };
      let copyYear = copyOldState[year];
      let copySemesters = copyYear['semesters'];
      let updateSemester = copySemesters[semester];
      let semesterIndex = updateSemester[indexPlace];
      semesterIndex[name] = payload;
      updateSemester[indexPlace] = semesterIndex;
      copySemesters[semester] = updateSemester;
      copyYear['semesters'] = copySemesters;
      copyOldState[year] = copyYear;
      return copyOldState;
    }
    case 'SHOW_MODAL':
      return {
        ...state,
        modal: true,
        edit: {
          activeSemester: activeSemester,
          year: year,
          event_type: payload.event_type,
          title: payload.event_name,
          start_date: moment(payload.event_date).format('D MMM YYYY'),
          start_time: payload.start_time,
          end_date: moment(payload.end_date).format('D MMM YYYY'),
          end_time: payload.end_time,

          _id: id,
          level_no: level_no,
          term: term,
          payload: payload,
        },
        edit_config: {
          year: year,
          index: index,
          from: from,
          min,
          max,
          events,
        },
      };
    default:
      return {
        ...state,
      };
  }
};

export default rootReducer;
