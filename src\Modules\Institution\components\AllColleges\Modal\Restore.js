import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';

import MButton from 'Widgets/FormElements/material/Button';

function Restore({ show, setShow, setArchiveCollege }) {
  return (
    <Modal show={show} onHide={() => setShow(false)} centered>
      <Modal.Body className="pt-0 pb-0">
        <p className="mb-3 mt-3 f-22">
          {' '}
          <Trans i18nKey={'add_colleges.restore_college'}></Trans>
        </p>
        <div>
          <Trans i18nKey={'add_colleges.are_you_sure_want_restore'}></Trans>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton className="mr-3" color="inherit" variant="outlined" clicked={() => setShow(false)}>
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>

        <MButton
          clicked={() => {
            setArchiveCollege('restore');
            setShow(false);
          }}
        >
          <Trans i18nKey={'program_input.restore'}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

Restore.propTypes = {
  show: PropTypes.string,
  setShow: PropTypes.func,
  setArchiveCollege: PropTypes.func,
};
export default Restore;
