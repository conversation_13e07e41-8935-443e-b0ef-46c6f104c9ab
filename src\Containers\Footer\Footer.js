import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

import DigiClass_Admin from 'Assets/DigiClass_Admin.svg';
import { Trans } from 'react-i18next';

const Footer = ({ className, loadVersion }) => {
  const [iframeShow, setIframeShow] = useState(false);
  const [iframeCount, setIframeCount] = useState(0);

  Footer.propTypes = {
    className: PropTypes.string,
    loadVersion: PropTypes.string,
  };

  useEffect(() => {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    var iframeShow = params.get('iframeShow');
    if (iframeCount === 0 && iframeShow !== null) {
      setIframeShow(true);
      setIframeCount(1);
    }
  }, [iframeCount]);

  return (
    <React.Fragment>
      {!iframeShow && (
        <div className="footer" style={{ display: 'flex', justifyContent: 'center' }}>
          <div className={className}>
            <div className="col-md-12 mt-2 mb-2">
              <p className="text-center mb-0 text-gray f-14">
                {loadVersion === '/v2' ? (
                  <>
                    &copy; {new Date().getFullYear()},{' '}
                    <a href="https://digi-val.com/" target="blank">
                      <Trans i18nKey={'login_footer_v2'}></Trans>
                    </a>
                    .<Trans i18nKey={'alright_reserved'}></Trans>.
                  </>
                ) : (
                  <>
                    {' '}
                    &copy; <Trans i18nKey={'copyright'}></Trans> {new Date().getFullYear()},
                    <a href="https://digi-val.com/" target="blank">
                      {' '}
                      <img src={DigiClass_Admin} alt="DigivalSolutions" width={'10%'} />{' '}
                    </a>{' '}
                    <Trans i18nKey={'developed'}></Trans> &amp; <Trans i18nKey={'UAE'}></Trans>
                  </>
                )}
              </p>
            </div>
          </div>
        </div>
      )}
    </React.Fragment>
  );
};
export default Footer;
