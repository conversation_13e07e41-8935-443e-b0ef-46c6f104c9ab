import React from 'react';
import PropTypes from 'prop-types';
import Dialog from '@mui/material/Dialog';
function DialogModal({
  className = '',
  show = false,
  children = '',
  onClose = () => {},
  maxWidth,
  fullWidth,
  sx = {},
}) {
  return (
    <Dialog
      className={className}
      open={show}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      sx={sx}
    >
      <div className="DialogModal">{children}</div>
    </Dialog>
  );
}

DialogModal.propTypes = {
  children: PropTypes.object,
  sx: PropTypes.object,
  className: PropTypes.string,
  show: PropTypes.bool,
  onClose: PropTypes.func,
  maxWidth: PropTypes.string,
  fullWidth: PropTypes.bool,
};

export default DialogModal;
