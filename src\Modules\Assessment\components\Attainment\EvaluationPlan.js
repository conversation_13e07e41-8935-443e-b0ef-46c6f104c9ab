import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import AttainmentTree from './AttainmentTree';
import AttainmentSettingsModal from '../../modals/AttainmentSettingsModal';
import AddIcon from '@mui/icons-material/Add';
import { List, Map } from 'immutable';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { getURLParams, indVerRename } from 'utils';

const EvaluationPlan = ({ attainmentDetails }) => {
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const outcomes = attainmentDetails.get('outcomes', List());
  const [outcomeType, setOutcomeType] = useState('');
  const type = getURLParams('type', true);
  const manageOutCome = getURLParams('outCome', true);
  const programId = getURLParams('pid', true);

  const attainmentTypeOutcome =
    type === 'course' ? outcomes.filter((s) => s === manageOutCome) : outcomes;

  useEffect(() => {
    if (outcomeType === '') setOutcomeType(attainmentTypeOutcome.get(0, ''));
  }, [attainmentTypeOutcome]); //eslint-disable-line

  const handleSettingsModal = () => setShowSettingsModal(!showSettingsModal);

  return (
    <div className="container pl-0">
      <div className="p-1 mt-2 d-flex">
        <div className="digi-evaluation-node-bg">
          {attainmentTypeOutcome.map((item, index) => (
            <div
              key={index}
              className={`digi-evaluation-root-node ${outcomeType === item ? 'active' : ''}`}
              onClick={() => setOutcomeType(item)}
            >
              {indVerRename(item, programId)}
            </div>
          ))}
        </div>

        {CheckPermission(
          'subTabs',
          'Attainment Calculator',
          'Attainment Setting',
          '',
          'Regulations',
          '',
          'Evaluation Plan',
          'Add Type'
        ) &&
          type === 'program' && (
            <div
              className="d-flex align-items-center text-skyblue ml-3 bold remove_hover"
              onClick={handleSettingsModal}
            >
              <AddIcon />
              <p className="mb-0 padding-top-2px">Add New</p>
            </div>
          )}
      </div>
      <AttainmentTree outcomeType={outcomeType} attainmentDetails={attainmentDetails} />

      {showSettingsModal && (
        <AttainmentSettingsModal
          show={showSettingsModal}
          onClose={handleSettingsModal}
          outcomes={outcomes}
          data={attainmentDetails}
        />
      )}
    </div>
  );
};

EvaluationPlan.propTypes = {
  attainmentDetails: PropTypes.instanceOf(Map),
};

export default EvaluationPlan;
