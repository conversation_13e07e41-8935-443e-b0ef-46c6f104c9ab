.dashboard_list {
  background: white;
  border-radius: 6px;
  padding: 13px;
  border: 1px solid #edeff3;
}
.dash_box {
  border: 1px solid #dadada82;
  box-shadow: 1px 1px 5px 0px rgb(0 0 0 / 25%);
  padding: 12px;
  border-radius: 8px;
  /* min-height: 215px;  */
  /*Note : when enable final warning make it 250px - siddiq*/
}
.border_bottom_dash {
  border-bottom: 1px solid;
  border-bottom-style: dashed;
}
.user_icon {
  background: #fffded;
  border: 1px solid #dadada;
  padding: 4px;
  border-radius: 4px;
  font-size: 13px;
}

.user_list {
  border: 1px solid #dadada;
  padding: 5px 12px 5px 12px;
  border-radius: 15px;
  font-size: 13px;
  background: #edf6ff;
  font-weight: 500;
}
.user_list.active {
  border: 1px solid #4b9eea !important;
  color: #4b9eea !important;
}

.user_list1 {
  border: 1px solid #dadada;
  padding: 5px 12px 5px 12px;
  border-radius: 8px;
  font-size: 14px;
  background: #edf6ff;
  font-weight: 500;
}
.user_list1.active {
  border: 1px solid #4b9eea !important;
  color: #4b9eea !important;
}

.alert_notification {
  background: #81cc66;
  font-size: 10px;
  padding: 2px 8px 2px 8px;
  border-radius: 20px;
  color: white;
}
.dash_tab {
  padding: 5px 7px 0px 7px;
}
.dash_tab {
  float: left;
}

.dash_tab a {
  display: block;
  color: #3e95ef;
  text-align: center;
  padding: 5px 3px 20px 3px;
  text-decoration: none;
  font-weight: 500;
}

.dash_list:hover {
  border-bottom: 2px solid #3e95f0;
}

.active_course {
  border-bottom: 2px solid #3e95f0;
  color: #3d5170 !important;
}

.dash_box_col {
  border: 1px solid #dadada;
  border-radius: 8px;
  background-color: white;
}
.dash_box_head {
  padding: 6px 15px 6px 15px;
}
.dash_box_head2 {
  padding: 12px 15px 12px 15px;
}

.sb-example-3 .search {
  width: 100%;
  position: relative;
  display: flex;
}

.sb-example-3 .searchTerm {
  width: 100%;
  border-right: none;
  padding: 4px 2px 4px 2px;
  border-radius: 8px 0 0 8px;
  outline: none;
  color: #9e9e9e;
  border: 0.5px solid rgb(0 0 0 / 6%);
  border-right: none;
  background: #dadada40;
}

.sb-example-3 .searchTerm::placeholder {
  color: rgba(0, 0, 0, 0.38);
  opacity: 1;
  /* Firefox */
  font-size: 13px;
}

.sb-example-3 .searchButton {
  width: 30px;
  height: 37px;
  border: 0.5px solid rgb(0 0 0 / 6%);
  background: #dadada40;
  text-align: center;
  color: #9e9e9e;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  font-size: 14px;
  border-left: none;
}

.dashboard_active,
.dashboard_hover:hover {
  border-bottom: 4px solid #4598e6;
  background: #edf6ff;
}

.dashboard_hover {
  padding: 15px 15px 15px 15px;
  cursor: pointer;
}

.text-orange {
  color: #f0a81f;
}

.f-35 {
  font-size: 35px;
}

.termOption {
  float: left;
  border: 1px solid gainsboro;

  border-radius: 5px;
  padding: 15px;
  font-size: 16px;
  line-height: 19px;
  letter-spacing: 0.15px;
}

select.selectArrow {
  background: url(../../../Assets/dropdown.png) no-repeat right #ddd;
  -webkit-appearance: none;
  background-position: 96%;
  background-color: #fff;
  padding: 0 30px 0 10px !important;
}

.shardDiv {
  padding: 4px 16px 4px 18px;
  background: #edf6ff;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 8px;
}

.scheduleProgress {
  font-weight: 500;
  font-size: 18px;
  letter-spacing: 0.15px;
  line-height: 130px;
}

.c-middle {
  /* position: absolute;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  width: 10px;
  bottom: -4px;
  font-size: 20px; */
  position: absolute;
  right: 10px;
  bottom: -3px;
  font-size: 20px;
  top: 5px;
}

.c-middle-left {
  position: absolute;
  left: 10px;
  bottom: 0px;
  font-size: 20px;
  top: 5px;
}

.open-box-animations {
  -webkit-animation: fadein 2s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: fadein 2s; /* Firefox < 16 */
  -ms-animation: fadein 2s; /* Internet Explorer */
  -o-animation: fadein 2s; /* Opera < 12.1 */
  animation-name: blow-box;
  animation-duration: 1s;
}

@keyframes blow-box {
  0% {
    opacity: 0;
    transform: rotate(0deg) scaleX(0) scaleY(0);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scaleX(1) scaleY(1);
  }
}

@keyframes rotate{
  100%{
      transform: rotate(-360deg);
  }
}

@-moz-keyframes blow-box {
  0% {
    opacity: 0;
    transform: rotate(0deg) scaleX(0) scaleY(0);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scaleX(1) scaleY(1);
  }
}

@-webkit-keyframes blow-box {
  0% {
    opacity: 0;
    transform: rotate(0deg) scaleX(0) scaleY(0);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scaleX(1) scaleY(1);
  }
}

@-ms-keyframes blow-box {
  0% {
    opacity: 0;
    transform: rotate(0deg) scaleX(0) scaleY(0);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scaleX(1) scaleY(1);
  }
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Firefox < 16 */
@-moz-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Safari, Chrome and Opera > 12.1 */
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Internet Explorer */
@-ms-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Opera < 12.1 */
@-o-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.text-initial {
  text-align: initial;
}
