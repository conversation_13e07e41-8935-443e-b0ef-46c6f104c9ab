import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';

import TimeGroups from './TimeGroups';
import BuildingsAndHospitals from './BuildingsAndHospitals';
import {
  selectTimeGroups,
  selectBuildingsAndHospitals,
  selectLocations,
} from '../../../_reduxapi/infrastructure/selectors';
import * as actions from '../../../_reduxapi/infrastructure/actions';
import '../../../Assets/css/grouping.css';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';

class InfrastructureSettings extends Component {
  handleBackClick() {
    this.props.history.goBack();
  }

  render() {
    const {
      getTimeGroups,
      timeGroups,
      updateTimeGroup,
      getBuildingsAndHospitals,
      buildingsAndHospitals,
      locations,
      getLocations,
      updateBuildingOrHospital,
    } = this.props;
    return (
      <div className="main pt-3 pb-5 bg-white">
        <div className="container">
          <div className="row pb-4">
            <div className="col-md-12 ">
              <div className="float-left">
                <b className="font-weight-bold mt-2 f-18 mb-0">
                  <i
                    className="fa fa-arrow-left pr-2"
                    aria-hidden="true"
                    onClick={this.handleBackClick.bind(this)}
                  ></i>
                  <Trans
                    i18nKey={'infra_management.infra_settings.infrastructure_settings'}
                  ></Trans>
                </b>
              </div>
            </div>
          </div>
          {CheckPermission(
            'subTabs',
            'Infrastructure Management',
            'Onsite',
            '',
            'Settings',
            '',
            'Time Groups',
            'View'
          ) && (
            <TimeGroups
              timeGroups={timeGroups}
              getTimeGroups={getTimeGroups}
              updateTimeGroup={updateTimeGroup}
            />
          )}
          {CheckPermission(
            'subTabs',
            'Infrastructure Management',
            'Onsite',
            '',
            'Settings',
            '',
            'Buildings and Hospitals',
            'View'
          ) && (
            <BuildingsAndHospitals
              getBuildingsAndHospitals={getBuildingsAndHospitals}
              getLocations={getLocations}
              buildingsAndHospitals={buildingsAndHospitals}
              locations={locations}
              updateBuildingOrHospital={updateBuildingOrHospital}
            />
          )}
        </div>
      </div>
    );
  }
}

InfrastructureSettings.propTypes = {
  history: PropTypes.object,
  getTimeGroups: PropTypes.func,
  updateTimeGroup: PropTypes.func,
  timeGroups: PropTypes.instanceOf(Map),
  getBuildingsAndHospitals: PropTypes.func,
  getLocations: PropTypes.func,
  updateBuildingOrHospital: PropTypes.func,
  buildingsAndHospitals: PropTypes.instanceOf(List),
  locations: PropTypes.instanceOf(List),
};

const mapStateToProps = (state) => {
  return {
    timeGroups: selectTimeGroups(state),
    buildingsAndHospitals: selectBuildingsAndHospitals(state),
    locations: selectLocations(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(InfrastructureSettings);
