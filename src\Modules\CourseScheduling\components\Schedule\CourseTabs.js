import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { getLang, getURLParams } from 'utils';
import { t } from 'i18next';
// import { Box, ToggleButton, ToggleButtonGroup } from '@mui/material';

const lang = getLang();

// const toggleGroupSx = {
//   padding: '4px',
//   borderRadius: '8px',
//   backgroundColor: '#F3F4F6',
//   border: '1px solid #E5E7EB',
// };
// const toggleButtonSx = {
//   padding: '6px 16px',
//   border: 0,
//   textTransform: 'none',
//   borderRadius: '8px !important',
//   color: '#6B7280',
//   fontSize: 14,
//   fontWeight: 'normal',
//   minWidth: 120,
//   '&.Mui-selected': {
//     color: '#374151',
//     backgroundColor: '#fff',
//     fontWeight: 500,
//     boxShadow: '0px 1px 3px 0px #11182733',
//     '&:hover': {
//       backgroundColor: '#fff',
//     },
//   },
// };
// const badgeSx = {
//   backgroundColor: '#E0E7FF',
//   color: '#4338CA',
//   fontSize: '12px',
//   padding: '3px 6px',
//   borderRadius: '6px',
//   marginLeft: '6px',
// };

function CourseTabs({
  setData,
  activeTab,
  clicked,
  showManageCourse = true,
  isAllowedToSchedule = true,
}) {
  const checkPerm =
    getURLParams('courseId', true) === ''
      ? CheckPermission('tabs', 'Schedule Management', 'Course Scheduling', '', 'TimeTable', 'View')
      : CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Course TimeTable',
          'View'
        );

  // const handleChange = (_, newValue) => {
  //   if (newValue === 'Schedule') {
  //     setData(Map({ activeTab: newValue, activeScheduleView: 'list' }));
  //     clicked && clicked({ isRefresh: false });
  //     return;
  //   }
  //   if (newValue === 'Manage Course') {
  //     return setData(
  //       fromJS({
  //         activeTab: newValue,
  //         activeSettingsView: 'list',
  //         paginationMetaData: { totalPages: null, currentPage: null },
  //       })
  //     );
  //   }

  //   setData(Map({ activeTab: newValue }));
  // };

  return (
    // <div className="text-xl-center">
    //   <ToggleButtonGroup
    //     value={activeTab}
    //     onChange={handleChange}
    //     aria-label="course-tabs"
    //     sx={toggleGroupSx}
    //     exclusive
    //   >
    //     {CheckPermission(
    //       'subTabs',
    //       'Schedule Management',
    //       'Course Scheduling',
    //       '',
    //       'Schedule',
    //       '',
    //       'Course Schedule',
    //       'View'
    //     ) && (
    //       <ToggleButton value="Schedule" sx={toggleButtonSx}>
    //         {t('role_management.tabs.Schedule')}
    //       </ToggleButton>
    //     )}

    //     {checkPerm && (
    //       <ToggleButton value="TimeTable" sx={toggleButtonSx}>
    //         {t('role_management.tabs.TimeTable')}
    //       </ToggleButton>
    //     )}

    //     {showManageCourse &&
    //       isAllowedToSchedule &&
    //       CheckPermission(
    //         'subTabs',
    //         'Schedule Management',
    //         'Course Scheduling',
    //         '',
    //         'Schedule',
    //         '',
    //         'Manage Course',
    //         'View'
    //       ) && (
    //         <ToggleButton value="Manage Course" sx={toggleButtonSx}>
    //           {t('role_management.sub_tabs.Manage Course')}
    //         </ToggleButton>
    //       )}

    //     <ToggleButton value="QuickSchedule" sx={toggleButtonSx}>
    //       Quick Schedule{' '}
    //       <Box component="span" sx={badgeSx}>
    //         New
    //       </Box>
    //     </ToggleButton>
    //   </ToggleButtonGroup>
    // </div>

    <ul className="course_tab d-flex justify-content-xl-center">
      {CheckPermission(
        'subTabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        '',
        'Course Schedule',
        'View'
      ) && (
        <li
          className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
          onClick={() => {
            setData(Map({ activeTab: 'Schedule', activeScheduleView: 'list' }));
            clicked && clicked({ isRefresh: false });
          }}
        >
          <span
            className={`course_list cursor-pointer ${activeTab === 'Schedule' && 'active_course'}`}
          >
            {t('role_management.tabs.Schedule')}
          </span>
        </li>
      )}
      {checkPerm && (
        <li
          className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
          onClick={() => {
            setData(Map({ activeTab: 'TimeTable' }));
          }}
        >
          <span
            className={`course_list cursor-pointer ${activeTab === 'TimeTable' && 'active_course'}`}
          >
            {t('role_management.tabs.TimeTable')}
          </span>
        </li>
      )}
      {showManageCourse && (
        <>
          {isAllowedToSchedule &&
            CheckPermission(
              'subTabs',
              'Schedule Management',
              'Course Scheduling',
              '',
              'Schedule',
              '',
              'Manage Course',
              'View'
            ) && (
              <li
                className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
                onClick={() => {
                  setData(
                    Map({
                      activeTab: 'Manage Course',
                      activeSettingsView: 'list',
                      paginationMetaData: Map({
                        totalPages: null,
                        currentPage: null,
                      }),
                    })
                  );
                }}
              >
                <span
                  className={`course_list cursor-pointer ${
                    activeTab === 'Manage Course' && 'active_course'
                  }`}
                >
                  {t('role_management.sub_tabs.Manage Course')}
                </span>
              </li>
            )}
        </>
      )}

      <li
        className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
        onClick={() => setData(Map({ activeTab: 'QuickSchedule' }))}
      >
        <span
          className={`course_list cursor-pointer ${
            activeTab === 'QuickSchedule' && 'active_course'
          }`}
        >
          Quick Schedule
        </span>
      </li>
    </ul>
  );
}

CourseTabs.propTypes = {
  activeTab: PropTypes.string,
  setData: PropTypes.func,
  showManageCourse: PropTypes.bool,
  clicked: PropTypes.func,
  isAllowedToSchedule: PropTypes.bool,
};

export default CourseTabs;
