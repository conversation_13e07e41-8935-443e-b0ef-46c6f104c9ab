import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { getLang, getURLParams } from '../../../../utils';
import { t } from 'i18next';

const lang = getLang();
function CourseTabs({
  setData,
  activeTab,
  clicked,
  showManageCourse = true,
  isAllowedToSchedule = true,
}) {
  const checkPerm =
    getURLParams('courseId', true) === ''
      ? CheckPermission('tabs', 'Schedule Management', 'Course Scheduling', '', 'TimeTable', 'View')
      : CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Course TimeTable',
          'View'
        );

  return (
    <ul className="course_tab d-flex justify-content-xl-center">
      {CheckPermission(
        'subTabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        '',
        'Course Schedule',
        'View'
      ) && (
        <li
          className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
          onClick={() => {
            setData(Map({ activeTab: 'Schedule', activeScheduleView: 'list' }));
            clicked && clicked({ isRefresh: false });
          }}
        >
          <span
            className={`course_list cursor-pointer ${activeTab === 'Schedule' && 'active_course'}`}
          >
            {t('role_management.tabs.Schedule')}
          </span>
        </li>
      )}
      {checkPerm && (
        <li
          className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
          onClick={() => {
            setData(Map({ activeTab: 'TimeTable' }));
          }}
        >
          <span
            className={`course_list cursor-pointer ${activeTab === 'TimeTable' && 'active_course'}`}
          >
            {t('role_management.tabs.TimeTable')}
          </span>
        </li>
      )}
      {showManageCourse && (
        <>
          {isAllowedToSchedule &&
            CheckPermission(
              'subTabs',
              'Schedule Management',
              'Course Scheduling',
              '',
              'Schedule',
              '',
              'Manage Course',
              'View'
            ) && (
              <li
                className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
                onClick={() => {
                  setData(
                    Map({
                      activeTab: 'Manage Course',
                      activeSettingsView: 'list',
                      paginationMetaData: Map({
                        totalPages: null,
                        currentPage: null,
                      }),
                    })
                  );
                }}
              >
                <span
                  className={`course_list cursor-pointer ${
                    activeTab === 'Manage Course' && 'active_course'
                  }`}
                >
                  {t('role_management.sub_tabs.Manage Course')}
                </span>
              </li>
            )}
        </>
      )}
    </ul>
  );
}

CourseTabs.propTypes = {
  activeTab: PropTypes.string,
  setData: PropTypes.func,
  showManageCourse: PropTypes.bool,
  clicked: PropTypes.func,
  isAllowedToSchedule: PropTypes.bool,
};

export default CourseTabs;
