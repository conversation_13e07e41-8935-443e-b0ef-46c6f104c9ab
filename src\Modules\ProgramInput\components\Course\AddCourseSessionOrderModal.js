import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { Button, Modal, Table, Container, Row, Col } from 'react-bootstrap';

import Input from '../../../../Widgets/FormElements/Input/Input';

function AddCourseSessionOrderModal(props) {
  const { callBack, saveModal, courseSessionOrderModule, setData } = props;
  const [name, setName] = useState('');
  const [moduleId, setModuleId] = useState('');
  const [editName, setEditName] = useState('');

  function checkValidation(type, id) {
    const changedName = type === 'create' ? name.toLowerCase() : editName.toLowerCase();
    const hasNameAlready = courseSessionOrderModule.some((item) =>
      id === ''
        ? changedName === item.get('moduleName', '').toLowerCase()
        : changedName === item.get('moduleName', '').toLowerCase() && id !== item.get('_id', '')
    );
    return hasNameAlready;
  }

  function saveCourseOrder(type, id = '') {
    if (checkValidation(type, id)) {
      setData(Map({ message: 'Duplicate Entry' }));
      return;
    }
    saveModal({
      name: type === 'create' ? name : editName,
      type,
      id,
      callBack: () => {
        setName('');
        cancelEditOrders();
      },
    });
  }

  function editCourseOrder(course) {
    setEditName(course.get('moduleName', ''));
    setModuleId(course.get('_id', ''));
  }

  function cancelEditOrders() {
    setEditName('');
    setModuleId('');
  }

  return (
    <Modal show={true} centered>
      <div className="p-3">
        <p className="f-20 mb-0 pt-3 digi-black">Course Session Order Module</p>
      </div>
      <Modal.Body>
        <Container>
          <Row>
            <Col xs={12} md={7} className="no-padding-left no-padding-right">
              <Input
                elementType="floatinginput"
                elementConfig={{
                  type: 'text',
                }}
                floatingLabel={'Module Name'}
                value={name}
                changed={(e) => setName(e.target.value)}
                maxLength="100"
              />
            </Col>
            <Col
              xs={12}
              md={5}
              className="text-right no-padding-right"
              style={{ marginTop: '15px' }}
            >
              <Button variant="outline-primary" className="mr-2" onClick={() => callBack()}>
                cancel
              </Button>
              <Button
                variant={'primary'}
                disabled={name === ''}
                onClick={() => saveCourseOrder('create')}
              >
                save
              </Button>
            </Col>
          </Row>
        </Container>
        <div className="pt-4 border-radious-8">
          <div>
            <Table striped bordered hover responsive size="sm">
              <thead>
                <tr>
                  <th className="pl-2">S.No</th>
                  <th className="pl-2">Module Name</th>
                  <th className="text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                {courseSessionOrderModule.map((course, index) => {
                  return (
                    <tr key={index}>
                      <td>{index + 1}</td>
                      <td>
                        {moduleId !== '' && moduleId === course.get('_id', '') ? (
                          <Input
                            elementType="floatinginput"
                            elementConfig={{
                              type: 'text',
                            }}
                            floatingLabel={''}
                            dd={'mt--25'}
                            value={editName}
                            changed={(e) => setEditName(e.target.value)}
                            maxLength="100"
                          />
                        ) : (
                          course.get('moduleName', '')
                        )}
                      </td>
                      <td>
                        <div className="text-center">
                          {moduleId !== '' && moduleId === course.get('_id', '') ? (
                            <>
                              <i
                                className="fa fa-times mr-2 remove_hover"
                                onClick={() => cancelEditOrders()}
                              ></i>
                              <i
                                className="fa fa-save remove_hover"
                                onClick={() => saveCourseOrder('update', course.get('_id', ''))}
                              ></i>
                            </>
                          ) : (
                            <>
                              <i
                                className="fa fa-pencil mr-2 remove_hover"
                                onClick={() => editCourseOrder(course)}
                              ></i>
                              <i
                                className="fa fa-trash remove_hover"
                                onClick={() => saveCourseOrder('delete', course.get('_id', ''))}
                              ></i>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </Table>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer className="digi-border-top"></Modal.Footer>
    </Modal>
  );
}

AddCourseSessionOrderModal.propTypes = {
  callBack: PropTypes.func,
  saveModal: PropTypes.func,
  courseSessionOrderModule: PropTypes.instanceOf(List),
  setData: PropTypes.func,
};

export default AddCourseSessionOrderModal;
