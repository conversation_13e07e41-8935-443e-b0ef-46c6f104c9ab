export const preparePercentageNew = (course) => {
  const percentageValue =
    (course.get('no_session', 0) -
      (course.get('no_session', 0) - course.get('completed_session', 0))) /
    course.get('no_session', 0);
  let percent = (percentageValue * 100).toFixed(2).replace(/\.00$/, ''); // eslint-disable-line
  return percent;
};
export const preparePercentage = (course) => {
  const percentageValue =
    (course.get('total_session', 0) -
      (course.get('total_session', 0) - course.get('completed_session', 0))) /
    course.get('total_session', 0);
  return Math.ceil(percentageValue * 100);
};
