import * as actionTypes from '../actions/actionTypes';
import axios from '../../axios';
import axiosV2 from '../../v2/axios';
import { U_ADMIN } from '../../constants';
//import { replaceVersion } from '../util';
import { getActiveVersion, getSiteUrl, isEmptyObject } from '../../utils';
import { postSuccessSignUpProcess } from '_reduxapi/user_management/v2/actions';
import LocalStorageService from 'LocalStorageService';
import useCustomCookie from 'Hooks/useCookieHook';
import { createAction } from '../util';
import { List } from 'immutable';

export const resetMessage = () => {
  return {
    type: actionTypes.RESET_MESSAGE_SUCCESS,
  };
};

export const setSwitchRole = (item) => {
  return {
    type: actionTypes.SWITCH_ROLE_SUCCESS,
    data: item,
  };
};

export const setSwitchCalendar = (item) => {
  return {
    type: actionTypes.SWITCH_CALENDAR_SUCCESS,
    data: item,
  };
};

export const authStart = () => {
  return {
    type: actionTypes.AUTH_START,
  };
};

export const setOnboardedStatus = ({ status, data }) => {
  return {
    type: actionTypes.SET_ONBOARDED_STATUS,
    status: status,
    data: data,
  };
};

export const authSuccess = (userId, response) => {
  if (response === null) {
    // window.location = 'https://digiclass.upm.digi-val.com/';
    window.location = getSiteUrl()?.['DC_URL'];
    return;
  }
  let selectedRole = {};
  if (response.roles !== undefined && response.roles.length > 0) {
    selectedRole = response.roles
      .filter((item) => item.isDefault === true)
      .reduce((_, el) => el, {});
    if (
      Object.keys(selectedRole).length === 0 &&
      (selectedRole.constructor === Object) === true &&
      response.roles.length > 0
    ) {
      selectedRole = response.roles[0];
    }
  }
  return {
    type: actionTypes.AUTH_SUCCESS,
    token: userId,
    loggedInUserData: response,
    isAdmin: false, //response.role === 'Super Admin', U_ADMIN.includes(response.email)
    isInstitutionOnboarded: false /* response.university ? response.university.status : false, */,
    userRoles: response.roles !== undefined && response.roles.length > 0 ? response.roles : [],
    selectedRole: selectedRole,
    reportsTo: response.report_to !== undefined ? response.report_to : {},
  };
};

export const authFail = (error) => {
  return {
    type: actionTypes.AUTH_FAIL,
    error: error,
  };
};

export const authStartV2 = () => {
  return {
    type: actionTypes.AUTH_START_V2,
  };
};
export const forgetPassStartV2 = () => {
  return {
    type: actionTypes.FORGET_PASS_START_V2,
  };
};
export const forgetPassSuccessV2 = () => {
  return {
    type: actionTypes.FORGET_PASS_SUCCESS_V2,
  };
};
export const forgetPassFailV2 = (error) => {
  return {
    type: actionTypes.FORGET_PASS_FAIL_V2,
    error: error,
  };
};

export const authSuccessV2 = (token, response) => {
  let selectedRole = {};
  if (response.roles !== undefined && response.roles.length > 0) {
    selectedRole = response.roles
      .filter((item) => item.isDefault === true)
      .reduce((_, el) => el, {});
    if (
      Object.keys(selectedRole).length === 0 &&
      (selectedRole.constructor === Object) === true &&
      response.roles.length > 0
    ) {
      selectedRole = response.roles[0];
    }
  }
  return {
    type: actionTypes.AUTH_SUCCESS_V2,
    token: token,
    loggedInUserData: response,
    isAdmin: getActiveVersion() === '/v2' ? U_ADMIN.includes(response.email) : false, //response.role === 'Super Admin', U_ADMIN.includes(response.email)
    isInstitutionOnboarded:
      getActiveVersion() === '/v2'
        ? response.university
          ? response.university.institutionId !== ''
          : false
        : false /* response.university ? response.university.status : false, */,
    userRoles: response.roles !== undefined && response.roles.length > 0 ? response.roles : [],
    selectedRole: selectedRole,
    reportsTo: response.report_to !== undefined ? response.report_to : {},
  };
};

export const authFailV2 = (error) => {
  return {
    type: actionTypes.AUTH_FAIL_V2,
    error: error,
  };
};

export const logout = () => {
  LocalStorageService.clearToken();
  return {
    type: actionTypes.AUTH_LOGOUT,
  };
};

export const checkAuthTimeout = (expirationTime) => {
  return (dispatch) => {
    setTimeout(() => {
      dispatch(logout());
    }, expirationTime * 1000);
  };
};

export const programListingStart = () => {
  return {
    type: actionTypes.PROGRAM_LISTING_START,
  };
};

export const programListingSuccess = (data, allPrograms, institutionCalendarLists) => {
  return {
    type: actionTypes.PROGRAM_LISTING_SUCCESS,
    data: data,
    allPrograms: allPrograms,
    institutionCalendarLists: institutionCalendarLists,
  };
};

export const programListingFailed = () => {
  return {
    type: actionTypes.PROGRAM_LISTING_FAILED,
  };
};

function onAuthSuccess(dispatch, response) {
  const setTimeExpiry = 60000;
  const userId = LocalStorageService.getUserId();
  //const expirationDate = new Date(new Date().getTime() + setTimeExpiry * 1000);
  LocalStorageService.setCustomToken('response', JSON.stringify(response));
  //LocalStorageService.setCustomCookie('response', JSON.stringify(response));
  //LocalStorageService.setCustomToken('token', JSON.stringify(token));
  //LocalStorageService.setCustomToken('expirationDate', expirationDate);
  dispatch(authSuccess(userId, response));
  dispatch(checkAuthTimeout(setTimeExpiry));
  dispatch(listingPublishedPrograms());
  dispatch(programListWithType());
}

function onAuthSuccessV2(dispatch, response) {
  const setTimeExpiry = 60000;
  //const expirationDate = new Date(new Date().getTime() + setTimeExpiry * 1000);
  const token = response._id.replace(/^"(.*)"$/, '$1');
  LocalStorageService.setCustomToken('response', JSON.stringify(response));
  //LocalStorageService.setCustomCookie('response', JSON.stringify(response));
  //LocalStorageService.setCustomToken('token', JSON.stringify(token));
  //LocalStorageService.setCustomToken('expirationDate', expirationDate);
  dispatch(authSuccessV2(token, response));
  dispatch(checkAuthTimeout(setTimeExpiry));
  if (getActiveVersion() === '') dispatch(listingPublishedPrograms());
  dispatch(programListWithType());
}

export const auth = (data, callBack) => {
  return (dispatch) => {
    dispatch(authStart());
    // if (isAdminLogin) {
    //   onAuthSuccess(dispatch, data);
    //   dispatch(getInstitution(data._id));
    //   return;
    // }
    axios
      .post('/user/otp_verify', data)
      .then((res) => {
        let response = res.data.data;
        const roleId = response?._role_id !== undefined ? response._role_id : '';
        if (res.data.status_code === 200 && roleId !== '') {
          onAuthSuccess(dispatch, response);
        } else {
          if (roleId !== '') dispatch(authFail(res.data.message));
          if (roleId === '' && callBack) callBack();
        }
      })
      .catch((error) => {
        dispatch(authFail(error.response.data.message));
      });
  };
};

export const authCheckState = () => {
  return (dispatch) => {
    const token = LocalStorageService.getAccessToken();
    const userId = LocalStorageService.getUserId();
    const response = LocalStorageService.getCustomToken('response');
    //const response = LocalStorageService.getCustomCookie('response');
    if (!token) {
      dispatch(logout());
    } else {
      const { isCookieExpired } = useCustomCookie();
      const tokenExpired = isCookieExpired('access_token');
      if (tokenExpired) {
        dispatch(logout());
      } else {
        let responseParse = JSON.parse(response);
        if (getActiveVersion() === '/v2') {
          dispatch(authSuccessV2(userId, responseParse));
        } else {
          if (response !== null) {
            dispatch(authSuccess(userId, responseParse));
          } else {
            dispatch(authCrossSiteCheckState());
          }
          // if (responseParse === null) dispatch(logout());
          // else dispatch(authSuccess(userId, responseParse));
        }
        // dispatch(checkAuthTimeout((expirationDate.getTime() - new Date().getTime()) / 1000));
        if (getActiveVersion() !== '/v2') {
          dispatch(listingPublishedPrograms());
          dispatch(programListWithType());
        }
      }
    }
  };
};

export const authCrossSiteCheckState = () => {
  return (dispatch) => {
    const token = LocalStorageService.getAccessToken();
    if (!token) {
      dispatch(logout());
    } else {
      const { isCookieExpired, deleteSingleCookie } = useCustomCookie();
      const tokenExpired = isCookieExpired('access_token');
      if (tokenExpired) {
        dispatch(logout());
      } else {
        const loginRes = LocalStorageService.getCustomCookie('authData', true);
        // const siteURL = getSiteUrl();
        // const redirectURL = siteURL.DC_ADMIN_URL;
        axios
          .get(`/user/authLoggedIn/${loginRes._id}`)
          .then((res) => {
            let response = res.data.data;
            if (res.status === 200) {
              deleteSingleCookie('landing-progress');
              onAuthSuccessV2(dispatch, { ...response, ...loginRes });
            } else {
              dispatch(authFailV2(res.data.message));
              dispatch(logout());
              // LocalStorageService.clearToken();
              // window.location = redirectURL;
            }
          })
          .catch((error) => {
            dispatch(authFailV2());
            dispatch(logout());
            // LocalStorageService.clearToken();
            // window.location = redirectURL;
            window.location.reload(true);
          });
      }
    }
  };
};

export const listingPublishedPrograms = () => {
  return (dispatch) => {
    const response = LocalStorageService.getCustomToken('staff_student_calendar_view');
    dispatch(programListingStart());
    if (!response) {
      axios
        .get('/program_calendar_review/staff_student_calendar_view')
        .then((res) => {
          if (res.data.status_code === 200) {
            let programs = [];
            let allPrograms = [];
            let institutionCalendarLists = res.data.data.institution_calendar;
            if (
              res.data.data.programs !== undefined &&
              res.data.data.programs &&
              res.data.data.programs.length > 0
            ) {
              programs = res.data.data.programs.filter((item) => item.status === true);
              allPrograms = res.data.data.programs;
            }
            LocalStorageService.setCustomToken(
              'staff_student_calendar_view',
              JSON.stringify({
                programs,
                allPrograms,
                institutionCalendarLists,
              })
            );
            dispatch(programListingSuccess(programs, allPrograms, institutionCalendarLists));
          } else {
            dispatch(programListingFailed());
          }
        })
        .catch((error) => {
          dispatch(programListingFailed(error));
        });
    } else {
      let responseParse = JSON.parse(response);
      dispatch(
        programListingSuccess(
          responseParse.programs,
          responseParse.allPrograms,
          responseParse.institutionCalendarLists
        )
      );
    }
  };
};

export const authLoggedIn = (loginRes) => {
  return (dispatch) => {
    dispatch(authStartV2());
    // const siteURL = getSiteUrl();
    // const redirectURL = siteURL.DC_ADMIN_URL;
    if (getActiveVersion() === '') {
      axios
        .get(`/user/authLoggedIn/${loginRes._id}`)
        .then((res) => {
          let response = res.data.data;
          if (res.status === 200) {
            onAuthSuccessV2(dispatch, { ...response, ...loginRes });
          } else {
            dispatch(authFailV2(res.data.message));
            dispatch(logout());
            // LocalStorageService.clearToken();
            // window.location = redirectURL;
          }
        })
        .catch(({ response }) => {
          const errorMessage =
            (response?.status === 403 && response?.data?.message) ||
            'Something went wrong. Please try again sometimes.';
          dispatch(authFailV2(errorMessage));
          dispatch(logout());
          // LocalStorageService.clearToken();
          // window.location = redirectURL;
        });
    } else {
      axiosV2
        .post(`/users/authLoggedIn`, {
          device_type: 'web',
        })
        .then((res) => {
          let response = res.data.data;
          if (res.status === 200) {
            onAuthSuccessV2(dispatch, { ...response, ...loginRes });
          } else {
            dispatch(authFailV2(res.data.message));
          }
        })
        .catch((error) => {
          dispatch(authFailV2());
        });
    }
  };
};

export const authLogin = (data, history, callBack) => {
  return (dispatch) => {
    dispatch(authStartV2());
    if (getActiveVersion() === '') {
      axios
        .post(`/digiclass/user/${data.ssoProvider ? 'authSSOLogin' : 'authLogin'}`, data)
        .then((res) => {
          let response = res.data.data;
          if (res.status === 200 && res.data.data !== null && !isEmptyObject(response)) {
            const activeVersion = response?.services?.REACT_APP_ACTIVE_VERSION;
            const indianVersion = response?.services?.REACT_APP_ENVIRONMENT === 'ecs-indian';
            if (activeVersion === 'v1' || indianVersion) {
              dispatch(authLoggedIn(response));
            } else {
              const isAdmin = response?.isAdmin;
              const isSignup = response?.signup;
              if (!isAdmin) {
                dispatch(postSuccessSignUpProcess(res.data.data, history, isSignup));
              } else {
                dispatch(authLoggedIn(response));
              }
            }
            if (data.ssoProvider === 'teams') {
              LocalStorageService.setCustomCookie('ssoAccountId', data.userId);
            }
          } else {
            dispatch(authFailV2(res.data.message));
          }
          !data.ssoProvider && callBack && callBack();
        })
        .catch((error) => {
          dispatch(authFailV2());
        });
    } else {
      axiosV2
        .post(`/users/authLogin`, data)
        .then((res) => {
          let response = res.data.data;
          if (res.status === 200 && !isEmptyObject(response)) {
            const isAdmin = response.isAdmin;
            const isSignup = response.signup;
            if (!isAdmin) {
              dispatch(postSuccessSignUpProcess(res.data.data, history, isSignup));
            } else {
              dispatch(authLoggedIn(response));
            }
          } else {
            dispatch(authFailV2(res.data.message));
          }
        })
        .catch((error) => {
          dispatch(authFailV2());
        });
    }
    // axios
    //   .post(`/digiclass/user/authLogin`, data)
    //   // .post(
    //   //   getActiveVersion() === ''
    //   //     ? `${replaceVersion('v1')}/digiclass/user/authLogin`
    //   //     : `${replaceVersion()}${isIndVer() ? '/digiclass' : ''}/user${
    //   //         isIndVer() ? '' : 's'
    //   //       }/authLogin`,
    //   //   data
    //   // )
    //   .then((res) => {
    //     let response = res.data.data;
    //     if (res.status === 200 && !isEmptyObject(response)) {
    //       const activeVersion = process.env.REACT_APP_ACTIVE_VERSION;
    //       const indianVersion = response?.services?.REACT_APP_ENVIRONMENT === 'ecs-indian';
    //       if (activeVersion === 'v1' || indianVersion) {
    //         dispatch(authLoggedIn(response));
    //       } else {
    //         const isAdmin = response.isAdmin;
    //         const isSignup = response.signup;
    //         if (!isAdmin) {
    //           dispatch(postSuccessSignUpProcess(res.data.data, history, isSignup));
    //         } else {
    //           dispatch(authLoggedIn(response));
    //         }
    //       }
    //     } else {
    //       dispatch(authFailV2(res.data.message));
    //     }
    //   })
    //   .catch((error) => {
    //     dispatch(authFailV2());
    //   });
  };
};

export const setData = (data) => {
  return {
    type: actionTypes.SET_DATA_SUCCESS,
    message: data.message,
  };
};
const setDataSuccess = createAction(actionTypes.SET_DATA_UPDATED_SUCCESS, 'data');
export function setUpdateData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const programListWithTypeStart = () => {
  return {
    type: actionTypes.PROGRAM_LIST_WITH_TYPE_START,
  };
};

export const programListWithTypeSuccess = (data) => {
  return {
    type: actionTypes.PROGRAM_LIST_WITH_TYPE_SUCCESS,
    data: data,
  };
};

export const programListWithTypeFailed = () => {
  return {
    type: actionTypes.PROGRAM_LIST_WITH_TYPE_FAILED,
  };
};

export const programListWithType = (refresh = false) => {
  return (dispatch) => {
    const response = LocalStorageService.getCustomToken('programListWithType');
    if (!response || refresh) {
      dispatch(programListWithTypeStart());
      axios
        .get('/digi_program/programListWithType')
        .then((res) => {
          if (res.data.status_code === 200) {
            const data = res.data.data;
            LocalStorageService.setCustomToken('programListWithType', JSON.stringify(data));
            dispatch(programListWithTypeSuccess(data));
          } else {
            dispatch(programListWithTypeFailed());
          }
        })
        .catch((error) => {
          dispatch(programListWithTypeFailed(error));
        });
    } else {
      let responseParse = JSON.parse(response);
      dispatch(programListWithTypeSuccess(responseParse));
    }
  };
};

export const forgetPasswordV2 = ({ mailId, callBack }) => {
  return (dispatch) => {
    dispatch(forgetPassStartV2());
    axiosV2
      .put(`users/forgot-password/${mailId}`)
      .then((res) => {
        dispatch(forgetPassSuccessV2());
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(forgetPassFailV2(error));
      });
  };
};

export const q360SetRoles = (qapcRoleIds = List()) => {
  const response = JSON.parse(LocalStorageService.getCustomToken('response'));
  if (response['roles']) response.roles = response.roles.concat(qapcRoleIds.toJS());
  else response.roles = qapcRoleIds.toJS();
  LocalStorageService.setCustomToken('response', JSON.stringify(response));
  return {
    type: actionTypes.Q360_USER_ROLES_STATUS,
    userRoles: qapcRoleIds,
  };
};

export const authLogoutSuccess = () => {
  return {
    type: actionTypes.AUTH_LOGOUT_SUCCESS,
  };
};

export const authLogoutFail = (error) => {
  return {
    type: actionTypes.AUTH_LOGOUT_FAIL,
    error: error,
  };
};

export const authLogout = (requestData, callBack) => {
  return (dispatch) => {
    dispatch(forgetPassStartV2());
    axios
      .post(`/digiclass/user/logout`, requestData)
      .then(() => {
        dispatch(authLogoutSuccess());
        callBack && callBack();
      })
      .catch((error) => {
        dispatch(authLogoutFail(error));
      });
  };
};
