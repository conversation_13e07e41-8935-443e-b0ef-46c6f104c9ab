import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { NavButton, NavButtonTerm } from '../Styled';
import { changeYear, changeTerm } from '../../../_reduxapi/actions/calender';
import { Trans } from 'react-i18next';

const NavRowButtons = (props) => {
  const { theme, changeYear, color, curriculumYear, terms, type = '', changeTerm } = props;
  const history = useHistory();
  const match = useRouteMatch();
  const active = match.params.year || 'year2';

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let urlName = params.get('pname');
  let urlPgId = params.get('programid');
  let urlTerm = params.get('term') !== null ? params.get('term') : terms[0].term_name;

  const triggerFunction = (year) => {
    history.push(
      `/${match.url
        .split('/')
        .filter((item, i) => i === 1)
        .reduce((acc, val) => val)}/${
        match.params.id
      }/${year}?programid=${urlPgId}&year=${year}&pname=${urlName}&term=${urlTerm}`
    );
  };

  const triggerTermFunction = (term) => {
    history.push(
      `/${match.url
        .split('/')
        .filter((item, i) => i === 1)
        .reduce((acc, val) => val)}/${
        match.params.id
      }/${`year${curriculumYear[0]}`}?programid=${urlPgId}&year=${`year${curriculumYear[0]}`}&pname=${urlName}&term=${term}`
    );
  };

  return (
    <Fragment>
      {type !== '' &&
        terms &&
        terms.length > 0 &&
        terms.map((term, index) => {
          return (
            <NavButtonTerm
              key={index}
              className={urlTerm === term.term_name ? `${theme}` : null}
              onClick={() => {
                changeTerm(term.term_name);
                triggerTermFunction(term.term_name);
              }}
              color={color}
            >
              {term.term_name}
            </NavButtonTerm>
          );
        })}
      <br />
      {type === '' &&
        curriculumYear &&
        curriculumYear.length > 0 &&
        curriculumYear.map((year, index) => {
          return (
            <NavButton
              key={index}
              className={active === 'year' + year ? `${theme}` : null}
              onClick={() => {
                changeYear('year' + year);
                triggerFunction('year' + year);
              }}
              color={color}
            >
              <Trans i18nKey={'year'}></Trans> {year}
            </NavButton>
          );
        })}
    </Fragment>
  );
};

const mapStateToProps = ({ calender }) => ({
  curriculumYear:
    calender.year_level &&
    calender.year_level.length > 0 &&
    calender.year_level.map((item) => item.year.replace('year', '')),
  terms: calender?.term !== undefined ? calender?.term : [],
});

NavRowButtons.propTypes = {
  theme: PropTypes.string,
  color: PropTypes.string,
  changeYear: PropTypes.func,
  curriculumYear: PropTypes.array,
  changeTerm: PropTypes.func,
  terms: PropTypes.array,
  type: PropTypes.string,
};

export default connect(mapStateToProps, { changeYear, changeTerm })(NavRowButtons);
