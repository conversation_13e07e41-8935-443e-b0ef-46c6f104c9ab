import { find_sun_to_sat } from '../../../../_utils/function';
import moment from 'moment';
import { setMinutes, setHours } from 'date-fns';

const dateSelect = (state, name, payload, start_date, end_date) => {
  if (name === 'same_dates' || name === 'choose_dates') {
    return {
      ...state,
      [payload]: name,
      type: {
        ...state.type,
        start_date: start_date,
        end_date: end_date,
      },
      academic_week_start: 0,
      academic_week_end: 0,
      academic_week_start_start_date: '',
      academic_week_start_end_date: '',
      academic_week_end_start_date: '',
      academic_week_end_end_date: '',
    };
  }
};

const eventAttach = (state, payload) => {
  let deletedEvents = [...state.deletedEvents];
  let updatedEvents = payload;
  if (deletedEvents && deletedEvents.length > 0) {
    updatedEvents = payload.filter((item) => {
      return deletedEvents.includes(item._event_id) !== true;
    });
  }
  return {
    ...state,
    events: updatedEvents,
  };
};

const eventCopyToDelete = (state, payload) => {
  let events = [...state.events];
  let separateEventIdFromEvents = [];
  if (events && events.length > 0) {
    separateEventIdFromEvents = events.map((list) => list._event_id);
  }

  let updatedEvents = [];
  if (payload && payload.length > 0) {
    updatedEvents = payload
      .filter((item) => {
        return separateEventIdFromEvents.includes(item._event_id) !== true;
      })
      .map((list) => list._event_id);
  }

  return {
    ...state,
    deletedEvents: updatedEvents,
  };
};

const deleteEvent = (state, payload, event_id) => {
  let copyEvents = [...state.events];

  let updatedEvents = copyEvents.filter((_, index) => {
    return index !== payload;
  });

  let deletedEvents = [...state.deletedEvents];
  deletedEvents.push(event_id);
  return {
    ...state,
    events: updatedEvents,
    deletedEvents: deletedEvents,
  };
};

const editLoad = (state, payload, course_id, levels, number) => {
  let title = payload.level_no;
  let background_color = '';
  let start_date = '';
  let end_date = '';
  let _course_id = '';
  let model = '';
  let courses_events = [];
  //let weeks = [];
  let updatedEvents = [];
  let rotation_count = 0;
  if (payload.course && payload.course.length > 0) {
    let courseData = payload.course
      .filter((list) => {
        return list._course_id === course_id;
      })
      .reduce((_, el) => {
        return el;
      }, {});

    background_color = courseData.color_code;
    start_date = moment(courseData.start_date).format('D MMM YYYY');
    end_date = moment(courseData.end_date).format('D MMM YYYY');
    _course_id = courseData._course_id;
    model = courseData.model;
    courses_events = courseData.courses_events;

    if (courses_events && courses_events.length > 0) {
      updatedEvents = courses_events.map((list) => {
        return {
          event_name: { first_language: list.event_name },
          _id: list._id,
          _event_id: list._event_id,
          event_type: list.event_type,
          event_date: list.event_date,
          start_time: list.start_time,
          end_time: list.end_time,
          end_date: list.end_date,
        };
      });
    }
    // if (start_date !== '' && end_date !== '') {
    //   weeks = find_sun_to_sat(courseData.start_date.slice(0, 10), courseData.end_date.slice(0, 10));
    // }
  }
  if (payload.rotation_course && payload.rotation_course.length > 0) {
    rotation_count = number;
    let rotationData = payload.rotation_course
      .filter((list) => parseInt(list.rotation_count) === parseInt(number))
      .reduce((_, el) => {
        return el.course;
      }, []);
    let courseData =
      rotationData &&
      rotationData.length > 0 &&
      rotationData
        .filter((list) => {
          return list._course_id === course_id;
        })
        .reduce((_, el) => {
          return el;
        }, {});
    background_color = courseData.color_code;
    start_date = moment(courseData.start_date).format('D MMM YYYY');
    end_date = moment(courseData.end_date).format('D MMM YYYY');
    _course_id = courseData._course_id;
    model = courseData.model;
    courses_events = courseData.courses_events;

    if (courses_events && courses_events.length > 0) {
      updatedEvents = courses_events.map((list) => {
        return {
          event_name: { first_language: list.event_name },
          _id: list._id,
          _event_id: list._event_id,
          event_type: list.event_type,
          event_date: list.event_date,
          start_time: list.start_time,
          end_time: list.end_time,
          end_date: list.end_date,
        };
      });
    }
    // if (start_date !== '' && end_date !== '') {
    //   weeks = find_sun_to_sat(courseData.start_date.slice(0, 10), courseData.end_date.slice(0, 10));
    // }
  }

  let data = {
    ...state,
    title: title,
    select_dates: 'choose_dates',
    type: {
      ...state.type,
      background_color: background_color,
      start_date: start_date,
      end_date: end_date,
      model: model,
      _course_id: _course_id,
      rotation_count: rotation_count,
    },
    disable_items: true,
    custom_dates: 'dates',
    events: updatedEvents,
    update_method: 'edit',
  };
  return data;
};

export const rootNonRotationalReducer = (state, action) => {
  const {
    type,
    payload,
    name,
    event,
    start_date,
    end_date,
    event_id,
    course_id,
    levels,
    number,
  } = action;

  switch (type) {
    case 'OFF_MODAL':
      return {
        ...state,
        modal: false,
        modal_mode: '',
        event_edit: false,
        event_index: -1,
        modal_content: {
          description: '',
          event_type: 'exam',
          title: '',
          start_date: '',
          start_time: setHours(setMinutes(new Date(), 0), 8),
          end_date: '',
          end_time: setHours(setMinutes(new Date(), 0), 17),
        },
      };
    case 'ON_TYPE':
      return {
        ...state,
        modal_content: {
          ...state.modal_content,
          [name]: payload,
          // event_type: "exam",
        },
      };
    case 'ON_TOGGLE':
      return {
        ...state,
        [name]: !state[name],
      };
    case 'LEVEL_SELECT': {
      let weeks = [];
      if (start_date !== '' && end_date !== '') {
        let yearEndDate = end_date;
        if (levels && levels.length === 2 && levels[1].rotation !== 'yes') {
          yearEndDate = levels[1].end_date;
        }
        weeks = find_sun_to_sat(start_date, yearEndDate);
      }
      return {
        ...state,
        title: payload,
        select_dates: '',
        type: {
          ...state.type,
          start_date: '',
          end_date: '',
          _course_id: '',
        },
        academic_weeks: [...weeks],
        triggerLists: true,
      };
    }
    case 'ON_CHANGE':
      return {
        ...state,
        type: {
          ...state.type,
          [name]: payload,
        },
        triggerLists: true,
      };
    case 'ADD_COURSES':
      return {
        ...state,
        add_courses: {
          ...state.add_courses,
          ...payload,
        },
        check_courses: true,
      };
    case 'SELECT_DATES':
      return dateSelect(state, name, payload, start_date, end_date);
    case 'SELECT_CUSTOM_DATES':
      if (start_date !== '' && end_date !== '') {
        let yearEndDate = end_date;
        if (levels && levels.length === 2 && levels[1].rotation !== 'yes') {
          yearEndDate = levels[1].end_date;
        }
        let weeks = find_sun_to_sat(start_date, yearEndDate);
        return {
          ...state,
          [name]: payload,
          academic_weeks: [...weeks],
        };
      } else {
        return {
          ...state,
          [name]: payload,
        };
      }
    case 'WEEK_START_CHANGE':
      return {
        ...state,
        [name]: payload,
        academic_week_start_start_date:
          payload !== '0' ? state.academic_weeks[payload - 1]['start_date'] : 0,
        academic_week_start_end_date:
          payload !== '0' ? state.academic_weeks[payload - 1]['end_date'] : 0,
        type: {
          ...state.type,
          start_date: payload !== '0' ? state.academic_weeks[payload - 1]['start_date'] : 0,
        },
        triggerLists: true,
      };
    case 'WEEK_END_CHANGE':
      return {
        ...state,
        [name]: payload,
        academic_week_end_start_date:
          payload !== '0' ? state.academic_weeks[payload - 1]['start_date'] : 0,
        academic_week_end_end_date:
          payload !== '0' ? state.academic_weeks[payload - 1]['end_date'] : 0,
        type: {
          ...state.type,
          end_date: payload !== '0' ? state.academic_weeks[payload - 1]['end_date'] : 0,
        },
        triggerLists: true,
      };
    case 'STOP_LISTING':
      return {
        ...state,
        triggerLists: payload,
      };
    case 'EVENT_ATTACH':
      return eventAttach(state, payload);
    case 'EVENT_COPY_TO_DELETE':
      return eventCopyToDelete(state, payload);
    case 'DELETE_EVENT':
      return deleteEvent(state, payload, event_id);
    case 'SHOW_MODAL':
      return {
        ...state,
        modal: true,
        modal_mode: payload,
      };
    case 'INITIAL_LOAD_EDIT_COURSE':
      return editLoad(state, payload, course_id, levels, number);
    case 'EDIT_EVENT':
      return {
        ...state,
        modal: true,
        event_edit: true,
        event_index: payload,
        modal_content: {
          ...state.modal_content,
          ...event,
          title:
            typeof event.event_name === 'object'
              ? event.event_name.first_language
              : event.event_name,
          start_date: moment(event.event_date).format('D MMM YYYY'),
          end_date: moment(event.end_date).format('D MMM YYYY'),
          start_time: event.start_time,
          end_time: event.end_time,
        },
      };
    default:
      return {
        ...state,
      };
  }
};
