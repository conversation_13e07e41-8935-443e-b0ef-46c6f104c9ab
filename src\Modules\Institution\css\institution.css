.head_logo {
  width: 40px;
  height: 40px;
  left: 36px;
  top: 32px;
  background: #dee1e1;
  border-radius: 50%;
}
.upload_logo {
  width: 150px;
  height: 146px;
  left: 36px;
  top: 115px;
  background: #fafafa;
  border: 1px dashed #818181;
  box-sizing: border-box;
  border-radius: 8px;
  margin-bottom: 20px;
}
.logo_view {
  width: 150px;
  height: 153px;
  left: 36px;
  top: 115px;
  background: #fafafa;
  border: 1px dashed #818181;
  box-sizing: border-box;
  border-radius: 8px;
}
.logo-img-border {
  border-radius: 8px;
}
.pt-55 {
  padding-top: 55px;
}

.univer_logo {
  width: 70px;
  height: 70px;
  left: 36px;
  top: 32px;
  background: #dee1e1;
  border-radius: 50%;
}

.text-black {
  color: #000000;
}

.font-weight-500 {
  font-weight: 500;
}

.jc-space-between {
  justify-content: space-between;
}

.jc-flex-end {
  justify-content: flex-end;
}

.file-name {
  overflow: hidden;
  text-overflow: ellipsis;
}

.university-view {
  border: 1px solid #c4c4c4;
  border-radius: 8px;
}
.university-view:hover {
  background-color: rgb(215 218 218 / 16%);
  border: 1px solid #56b8ff;
  border-radius: 8px;
}
.university-view-active {
  background-color: #c2ebfa;
  border: 1px solid #56b8ff;
  border-radius: 8px;
}

.colleges_card {
  box-shadow: 0px 1px 5px 0px #0000005c;
  border-radius: 8px;
  border: 1px solid #eeee;
  max-height: 500px;
}

.color-gray {
  color: #333333;
}

.text-align-center {
  text-align: center;
}

.institution-view {
  box-shadow: unset;
  padding: 17px 35px 17px;
}

.digi-gray-border {
  border: 1px solid #9ca3af;
}

/* ul li {
    list-style-type: disc;
  } */

.rotate {
  transform: rotate(180deg);
}

.display-none {
  display: none !important;
}

.display-block {
  display: block !important;
}
.pt-2_3 {
  padding-top: 2.3px;
}

.institution-type {
  max-width: 700px;
  margin: auto;
}

.university-save-btn {
  padding: 6px 16px !important;
}

.university-cancel-btn {
  padding: 6px 16px !important;
  border: 1px solid currentColor !important;
  color: inherit !important;
  border-color: currentColor !important;
  border-radius: 4px !important;
}

.text_wrap {
  overflow-wrap: anywhere;
}
#light_gray {
  color: #9ca3af;
}
.height_200px {
  height: 200px;
}

.Accreditation_box {
  padding: 16px;
  background: #f3f4f6;
  border-radius: 8px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.accreditation_main .react-datepicker-wrapper {
  width: 100%;
}

.accreditation_main .react-datepicker__tab-loop {
  position: absolute;
  top: 0%;
}

.accreditation_date {
  position: absolute;
  margin-left: -25px;
  margin-top: 10px;
}
