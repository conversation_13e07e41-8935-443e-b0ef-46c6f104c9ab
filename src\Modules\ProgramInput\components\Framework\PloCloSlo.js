import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import Button from '@mui/material/Button';

import AlertConfirmModal from '../../modal/AlertConfirmModal';
import { TYPES } from './utils';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  selectCourse,
  selectCurriculum,
  selectSessionFlow,
} from '../../../../_reduxapi/program_input/selectors';
import { getRandomId } from '../../../InfrastructureManagement/utils';
import Input from '../../../../Widgets/FormElements/Input/Input';
import { getURLParams, indVerRename } from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import '../../css/program.css';
import { Trans, withTranslation } from 'react-i18next';
import { t } from 'i18next';
import { getLang } from '../../../../utils';

const lang = getLang();
class PloCloSlo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      programId: getURLParams('_id', true),
      programName: getURLParams('_n', true),
      curriculumId: getURLParams('_curriculum_id', true),
      curriculumName: getURLParams('_curriculum_name', true),
      courseId: getURLParams('_course_id', true),
      courseName: getURLParams('_course_name', true),
      levelId: getURLParams('_level_id', true),
      levelName: getURLParams('_level_name', true),
      yearId: getURLParams('_year_id', true),
      yearName: getURLParams('_year_name', true),
      frameworkName: getURLParams('_framework_name', true),
      added: Map(),
      edited: Map(),
      modalData: {
        show: false,
      },
    };
    this.handleChange = this.handleChange.bind(this);
    this.handleCancelClick = this.handleCancelClick.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  componentDidMount() {
    const { curriculumId, courseId } = this.state;
    const { type } = this.props;
    if (type === TYPES.PLO && curriculumId) {
      this.props.getCurriculum(curriculumId);
    }
    if (type === TYPES.CLO && courseId) {
      this.props.getCourse(courseId, false);
    }
    if (type === TYPES.SLO && courseId) {
      this.props.getsessionFlow(courseId);
    }
  }

  getDomains() {
    const { type, curriculum, course, sessionFlow } = this.props;
    if (type !== TYPES.SLO) {
      const data = type === TYPES.PLO ? curriculum : course;
      return data.getIn(['framework', 'domains'], List()).map((d) =>
        d.set(
          type,
          d
            .get(type, List())
            .filter((t) => !t.get('isDeleted'))
            .toList()
        )
      );
    }
    return sessionFlow.get('session_flow_data', List()).map((sessionFlow) =>
      sessionFlow.merge(
        Map({
          no: `${sessionFlow.get('delivery_symbol', '')}${sessionFlow.get('delivery_no', '')}`,
          name: sessionFlow.get('delivery_topic', ''),
        })
      )
    );
  }

  addPloClo(domainId) {
    const randomId = getRandomId();
    this.setState((state) => {
      return {
        added: state.added.setIn(
          [domainId, randomId],
          Map({
            _id: randomId,
            no: '',
            name: '',
          })
        ),
      };
    });
  }

  editPloClo(domainId, data) {
    this.setState((state) => {
      return {
        edited: state.edited.setIn(
          [domainId, data.get('_id')],
          Map({
            _id: data.get('_id'),
            no: data.get('no'),
            name: data.get('name'),
          })
        ),
      };
    });
  }

  handleChange(e, _id, sNo, domainId, operation) {
    const value = e.target.value;
    const key = operation === 'create' ? 'added' : 'edited';
    this.setState({
      [key]: this.state[key].mergeIn([domainId, _id], Map({ no: sNo, name: value })),
    });
  }

  handleCancelClick(_id, domainId, operation) {
    const key = operation === 'create' ? 'added' : 'edited';
    this.setState({
      [key]: this.state[key].deleteIn([domainId, _id]),
    });
  }

  handleSubmit(_id, data, domainId, operation) {
    const { type, curriculum, course, sessionFlow } = this.props;
    const { levelId, levelName, yearId, yearName, programId } = this.state;

    if (type === TYPES.PLO && curriculum.isEmpty()) {
      this.props.setData(
        Map({ message: t('program_input.error_strings.curriculum_details_not_found') })
      );
      return;
    }
    if (type === TYPES.CLO && course.isEmpty()) {
      this.props.setData(
        Map({ message: t('program_input.error_strings.course_details_not_found') })
      );
      return;
    }
    if (type === TYPES.SLO && sessionFlow.isEmpty()) {
      this.props.setData(
        Map({ message: t('program_input.error_strings.sessionflow_details_not_found') })
      );
      return;
    }
    if (!data.get('no') || !data.get('name').trim()) {
      this.props.setData(
        Map({ message: t('program_input.error_strings.name_shouldnot_be_empty') })
      );
      return;
    }
    const requestBody = {
      operation,
      type,
      [type === TYPES.SLO ? 'slo_id' : '_id']: _id,
      clearEntry: this.handleCancelClick,
      ...(type === TYPES.PLO && { curriculum_id: curriculum.get('_id') }),
      ...(type === TYPES.CLO && { course_id: course.get('_id') }),
      framework_id:
        type === TYPES.PLO ? curriculum.get('_framework_id') : course.getIn(['framework', '_id']),
      [type === TYPES.SLO ? 'session_flow_data_id' : 'domain_id']: domainId,
      ...(type === TYPES.SLO && {
        course_id: sessionFlow.get('_course_id'),
        session_order_id: sessionFlow.get('_id'),
      }),
      [type]: {
        no: data.get('no'),
        name: data.get('name').trim(),
        ...(type === TYPES.CLO && {
          year_id: yearId,
          year_name: yearName,
          level_id: levelId,
          level_name: levelName,
        }),
      },
    };
    const ploLabel = indVerRename('PLO', programId);
    const cloLabel = indVerRename('CLO', programId);
    const sloLabel = indVerRename('SLO', programId);
    const label = { ploLabel, cloLabel, sloLabel };
    this.props[type === TYPES.SLO ? 'saveSlo' : 'savePloClo'](requestBody, label);
  }

  handleDelete(data, domainId, isConfirmed) {
    const { type, curriculum, course, sessionFlow } = this.props;
    const { programId } = this.state;
    const formattedType = indVerRename(type.toUpperCase(), programId);
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: t('confirm_delete'),
        body: `${t('are_you_sure_you_want_to_delete_the_selected')} ${formattedType} ${data.get(
          'no',
          ''
        )} ${t('question_mark')}`,
        variant: 'confirm',
        data: { data: { data, domainId }, operation: 'delete' },
        confirmButtonLabel: t('delete'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    if ([TYPES.PLO, TYPES.CLO].includes(type)) {
      this.props.savePloClo(
        {
          operation: 'delete',
          type,
          _id: data.get('_id'),
          ...(type === TYPES.PLO && { curriculum_id: curriculum.get('_id') }),
          ...(type === TYPES.CLO && { course_id: course.get('_id') }),
          domain_id: domainId,
        },
        programId
      );
    }
    const ploLabel = indVerRename('PLO', programId);
    const cloLabel = indVerRename('CLO', programId);
    const sloLabel = indVerRename('SLO', programId);
    const label = { ploLabel, cloLabel, sloLabel };
    if (type === TYPES.SLO) {
      this.props.saveSlo(
        {
          operation: 'delete',
          type,
          course_id: sessionFlow.get('_course_id'),
          session_order_id: sessionFlow.get('_id'),
          session_flow_data_id: domainId,
          slo_id: data.get('_id'),
        },
        label
      );
    }
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ data, operation }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => {
        switch (operation) {
          case 'delete': {
            return this.handleDelete(data.data, data.domainId, true);
          }
          default:
            return;
        }
      }
    );
  }

  setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(titleIcon && { titleIcon }),
        ...(body && { body }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  getNewPloCloSloNumber(domain) {
    const { type } = this.props;
    const domainNo = domain.get('no');
    const data = domain.get(type, List());
    const number = type === 'slo' ? domain.get('delivery_no') : domainNo;
    if (data.isEmpty()) return `${number}.1`;
    const lastItem = data.last();
    const lastItemNo = Number(lastItem.get('no').split('.').slice(-1).toString());
    return `${number}.${lastItemNo + 1}`;
  }

  render() {
    const {
      programName,
      frameworkName,
      curriculumName,
      courseName,
      added,
      edited,
      modalData,
      programId,
    } = this.state;
    const { type, history, t } = this.props;
    const formattedType = indVerRename(type.toUpperCase(), programId);

    const versionName = getURLParams('versionName', true);
    return (
      <React.Fragment>
        <div className="main bg-gray pb-5">
          <div className="bg-white pt-3 pb-3">
            <div className="container-fluid">
              <p className="font-weight-bold mb-0 text-left f-17">
                <i
                  className="fa fa-arrow-left pr-3 cursor-pointer"
                  aria-hidden="true"
                  onClick={() => history.goBack()}
                ></i>
                <Trans i18nKey={'add'}></Trans> {''}
                {formattedType}
                <Trans i18nKey={'s_for'}></Trans>{' '}
                {type === TYPES.PLO ? programName : courseName + versionName}
              </p>
            </div>
          </div>

          <div className="bg-gray">
            <div className="container-fluid">
              <div className="row pb-4">
                <div className="col-md-12">
                  <div className="p-4">
                    <div className="d-flex justify-content-between">
                      <h5 className="pb-2 pt-2">{`${programName}${
                        type !== TYPES.SLO ? ' / ' + frameworkName : ''
                      } / ${curriculumName}${
                        [TYPES.CLO, TYPES.SLO].includes(type)
                          ? ' / ' + courseName + versionName
                          : ''
                      } / ${t('add_new_small')} ${formattedType}s`}</h5>
                    </div>
                    {this.getDomains().isEmpty() && (
                      <div className="mt-4 text-align-center">
                        {type === TYPES.SLO
                          ? t('program_input.error_strings.no_session_topics_found')
                          : t('program_input.error_strings.no_domains_found')}
                      </div>
                    )}
                    {this.getDomains().map((domain) => (
                      <div className="pb-4" key={domain.get('_id')}>
                        <div className="bg-white border-radious-8 p-3">
                          <p className="f-16 mb-2 bold text-left">{`${domain.get(
                            'no',
                            ''
                          )} ${domain.get('name', '')}`}</p>

                          <div className="bg-gray rounded p-1">
                            <div className="borderBottom_blue p-3">
                              <div className="row">
                                <div className="col-md-2">
                                  <p className="f-15 mb-0 bold">
                                    {`${formattedType}`} <Trans i18nKey={'number'}></Trans>
                                  </p>
                                </div>
                                <div className="col-md-8">
                                  <p className="f-15 mb-0 bold">
                                    {`${formattedType}`}{' '}
                                    <Trans i18nKey={'mapping.description'}></Trans>
                                  </p>
                                </div>
                                <div className="col-md-2"></div>
                              </div>
                            </div>
                            {domain.get(type, List()).isEmpty() &&
                              added.get(domain.get('_id'), Map()).isEmpty() && (
                                <div className="mt-4 text-center">
                                  <Trans i18nKey={'no_record_found'}></Trans>{' '}
                                </div>
                              )}
                            {domain.get(type, List()).map((d) =>
                              edited.hasIn([domain.get('_id'), d.get('_id')]) ? (
                                <EditableRow
                                  key={d.get('_id')}
                                  sNo={d.get('no', '')}
                                  type={type}
                                  domainId={domain.get('_id')}
                                  data={edited.getIn([domain.get('_id'), d.get('_id')])}
                                  operation="update"
                                  handleChange={this.handleChange}
                                  handleCancelClick={this.handleCancelClick}
                                  handleSubmit={this.handleSubmit}
                                  programId={programId}
                                />
                              ) : (
                                <div key={d.get('_id')} className="custome_table">
                                  <div className="row">
                                    <div className="col-md-2">
                                      <p className="f-14 mb-0 pt-2">
                                        <i
                                          className={`fa-rotate-90 f-12 mr-2 ${
                                            lang === 'ar' ? 'fa fa-level-down' : 'fa fa-level-up'
                                          }`}
                                          aria-hidden="true"
                                        ></i>
                                        {d.get('no', '')}
                                      </p>
                                    </div>

                                    <div className="col-md-8">
                                      <p className="f-15 mb-0 pt-1 bold word-wrap-break-word">
                                        {d.get('name', 'NA') || 'NA'}
                                      </p>
                                    </div>
                                    <div className="col-md-2 text-align-center">
                                      <div className="pt-1 f-18">
                                        {CheckPermission(
                                          'tabs',
                                          'Program Input',
                                          'Programs',
                                          '',
                                          'Active Programs',
                                          'Edit'
                                        ) && (
                                          <i
                                            className="fa fa-pencil text-skyblue remove_hover mr-2"
                                            onClick={() => this.editPloClo(domain.get('_id'), d)}
                                          ></i>
                                        )}
                                        {CheckPermission(
                                          'tabs',
                                          'Program Input',
                                          'Programs',
                                          '',
                                          'Active Programs',
                                          'Delete'
                                        ) && (
                                          <i
                                            className="fa fa-trash text-skyblue remove_hover"
                                            onClick={() =>
                                              this.handleDelete(
                                                d.set('no', d.get('no', '')),
                                                domain.get('_id'),
                                                false
                                              )
                                            }
                                          ></i>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )
                            )}
                            {added
                              .get(domain.get('_id'), Map())
                              .entrySeq()
                              .map((a, j) => (
                                <EditableRow
                                  key={a[0]}
                                  sNo={this.getNewPloCloSloNumber(domain)}
                                  type={type}
                                  domainId={domain.get('_id')}
                                  data={a[1]}
                                  operation="create"
                                  handleChange={this.handleChange}
                                  handleCancelClick={this.handleCancelClick}
                                  handleSubmit={this.handleSubmit}
                                  programId={programId}
                                />
                              ))}
                            {(CheckPermission(
                              'pages',
                              'Program Input',
                              'Programs',
                              'Add Program'
                            ) ||
                              CheckPermission(
                                'pages',
                                'Program Input',
                                'Programs',
                                'Add Pre-requisite'
                              )) && (
                              <p className="pr-2 pt-2 pb-1 text-right">
                                <Button
                                  onClick={() => this.addPloClo(domain.get('_id'))}
                                  disabled={!added.get(domain.get('_id'), Map()).isEmpty()}
                                  classes={{
                                    root: added.get(domain.get('_id'), Map()).isEmpty()
                                      ? 'text-skyblue'
                                      : '',
                                  }}
                                >
                                  <i className="fa fa-plus pr-2" aria-hidden="true"></i>
                                  <Trans i18nKey={'add'}></Trans> {`${formattedType}`}
                                </Button>
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <AlertConfirmModal
          show={modalData.show}
          title={modalData.title || ''}
          titleIcon={modalData.titleIcon}
          body={modalData.body || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />
      </React.Fragment>
    );
  }
}

function EditableRow({
  sNo,
  type,
  domainId,
  data,
  operation,
  handleChange,
  handleCancelClick,
  handleSubmit,
  programId,
}) {
  const formattedType = indVerRename(type.toUpperCase(), programId);
  return (
    <div className="custome_table">
      <div className="row">
        <div className="col-md-2">
          <p className="f-14 mb-0 pt-2">
            <i
              className={`fa-rotate-90 f-12 mr-2 ${
                lang === 'ar' ? 'fa fa-level-down' : 'fa fa-level-up'
              }`}
              aria-hidden="true"
            ></i>
            {sNo}
          </p>
        </div>

        <div className="col-md-8">
          <div className="mt--25">
            <Input
              elementType="input"
              elementConfig={{
                type: 'text',
                placeholder: `${formattedType} ${t('mapping.description')}`,
              }}
              value={data.get('name', '')}
              changed={(e) => handleChange(e, data.get('_id'), sNo, domainId, operation)}
              className="form-control"
            />
          </div>
        </div>
        <div className="col-md-2 text-align-center">
          <div className="pt-1 f-18">
            <i
              className="fa fa-save text-skyblue remove_hover mr-2"
              onClick={() => handleSubmit(data.get('_id'), data, domainId, operation)}
            ></i>
            <i
              className="fa fa-times text-skyblue remove_hover"
              onClick={() => handleCancelClick(data.get('_id'), domainId, operation)}
            ></i>
          </div>
        </div>
      </div>
    </div>
  );
}

PloCloSlo.propTypes = {
  history: PropTypes.object,
  setData: PropTypes.func,
  getCurriculum: PropTypes.func,
  type: PropTypes.string,
  curriculum: PropTypes.instanceOf(Map),
  getCourse: PropTypes.func,
  course: PropTypes.instanceOf(Map),
  savePloClo: PropTypes.func,
  getsessionFlow: PropTypes.func,
  sessionFlow: PropTypes.instanceOf(Map),
  saveSlo: PropTypes.func,
  t: PropTypes.func,
};

EditableRow.propTypes = {
  sNo: PropTypes.string,
  type: PropTypes.string,
  domainId: PropTypes.string,
  programId: PropTypes.string,
  data: PropTypes.instanceOf(Map),
  operation: PropTypes.string,
  handleChange: PropTypes.func,
  handleCancelClick: PropTypes.func,
  handleSubmit: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    curriculum: selectCurriculum(state),
    course: selectCourse(state),
    sessionFlow: selectSessionFlow(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(withTranslation()(PloCloSlo));
