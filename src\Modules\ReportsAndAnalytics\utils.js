import { Set, List, Map, fromJS } from 'immutable';
import { isValid, startOfDay, format } from 'date-fns';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

import RobotoRegular from '../../Assets/fonts/Roboto/Roboto-Regular.ttf';

import {
  capitalize,
  chunkArray,
  getFormattedCourseDuration,
  trimFractionDigits,
  getTPCLegend,
  indVerRename,
  levelRename,
  getEnvLabelChanged,
  getVersionName,
} from '../../utils';
import { t } from 'i18next';
import { getLang } from '../../utils';
import i18n from 'i18next';
import moment from 'moment';

const lang = getLang();

export function getFormattedGroupName(name, offset) {
  if (typeof name !== 'string') return name;
  const splitted = name.split('-');
  return splitted.slice(splitted.length - offset, splitted.length).join('-');
}

const hexColors = ['#00AEEF', '#FF0000', '#33B207', '#FFB951', '#A32FFF', '#979C7A'];
export function getColor(index) {
  const colorIndex = index % hexColors.length;
  return hexColors.slice(colorIndex, colorIndex + 1)[0];
}

export function calculatePercentage(value, totalValue, shouldRound = true, fractionDigits = 2) {
  let percentage = value === 0 ? 0 : (value / totalValue) * 100;
  if (!Number.isInteger(percentage)) {
    percentage = shouldRound ? Math.floor(percentage) : percentage.toFixed(fractionDigits);
  }
  return percentage;
}

export function getHour(date) {
  if (date.isEmpty()) return 0;
  const meridiem = date.get('format');
  const hour = date.get('hour');
  return meridiem === 'AM' ? (hour === 12 ? 0 : hour) : hour === 12 ? 12 : hour + 12;
}

// chartTypes constants
export const chartTypes = {
  STAFFS: 1,
  STUDENTS: 2,
  CREDIT_HOURS: 3,
  PLO: 4,
};

export const commonChartOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  dataZoom: [{ type: 'slider', show: true }],
  grid: {
    left: '5%',
    right: '5%',
    // bottom: '5%',
    containLabel: true,
  },
  yAxis: [
    {
      type: 'value',
      zlevel: -2,
    },
  ],
  media: [
    {
      query: {
        maxWidth: 500,
      },
      option: {
        legend: {
          top: '10%',
          orient: 'horizontal',
          type: 'scroll',
        },
        grid: {
          top: '25%',
        },
        xAxis: {
          axisLabel: {
            show: false,
          },
        },
      },
    },
  ],
};

const titleTextStyle = {
  color: '#3d5170',
  fontWeight: 'normal',
  fontSize: 16,
  fontFamily: 'Roboto, sans-serif',
};

const commonBarOptions = {
  barMaxWidth: 18,
  type: 'bar',
  emphasis: {
    focus: 'series',
  },
};

function getTPCInitialSeriesData(programId = '', legend = getTPCLegend()) {
  return {
    theory: {
      name: legend[0],
      data: [],
      label: { show: true, position: 'bottom', formatter: indVerRename('T', programId) },
    },
    theoryTotal: {
      name: legend[0],
      data: [],
      label: { show: true, position: 'top', formatter: (params) => params.value || '' },
      xAxisIndex: 1,
      zlevel: -1,
      itemStyle: { color: 'rgba(160, 160, 160, 0.22)' },
    },
    practical: {
      name: legend[1],
      data: [],
      label: { show: true, position: 'bottom', formatter: indVerRename('P', programId) },
    },
    practicalTotal: {
      name: legend[1],
      data: [],
      label: { show: true, position: 'top', formatter: (params) => params.value || '' },
      xAxisIndex: 1,
      zlevel: -1,
      itemStyle: { color: 'rgba(160, 160, 160, 0.22)' },
    },
    clinical: {
      name: legend[2],
      data: [],
      label: { show: true, position: 'bottom', formatter: indVerRename('C', programId) },
    },
    clinicalTotal: {
      name: legend[2],
      data: [],
      label: { show: true, position: 'top', formatter: (params) => params.value || '' },
      xAxisIndex: 1,
      zlevel: -1,
      itemStyle: { color: 'rgba(160, 160, 160, 0.22)' },
    },

    counselling: {
      name: 'Counselling',
      data: [],
      label: { show: true, position: 'bottom', formatter: 'Cl' },
    },
    counsellingTotal: {
      name: 'Counselling',
      data: [],
      label: { show: true, position: 'top', formatter: (params) => params.value || '' },
      xAxisIndex: 1,
      zlevel: -1,
      itemStyle: { color: 'rgba(160, 160, 160, 0.22)' },
    },

    feedback: {
      name: 'Feedback',
      data: [],
      label: { show: true, position: 'bottom', formatter: 'f' },
    },
    feedbackTotal: {
      name: 'Feedback',
      data: [],
      label: { show: true, position: 'top', formatter: (params) => params.value || '' },
      xAxisIndex: 1,
      zlevel: -1,
      itemStyle: { color: 'rgba(160, 160, 160, 0.22)' },
    },
    training: {
      name: 'Training',
      data: [],
      label: { show: true, position: 'bottom', formatter: 'TR' },
    },
    trainingTotal: {
      name: 'Training',
      data: [],
      label: { show: true, position: 'top', formatter: (params) => params.value || '' },
      xAxisIndex: 1,
      zlevel: -1,
      itemStyle: { color: 'rgba(160, 160, 160, 0.22)' },
    },
  };
}

export function dataToChartOptions(chartType, dashboardData, activeTerm, programData) {
  switch (chartType) {
    case chartTypes.STAFFS: {
      return getStaffsData(dashboardData, programData);
    }
    case chartTypes.CREDIT_HOURS: {
      return getCreditHoursData(dashboardData, programData);
    }
    case chartTypes.STUDENTS: {
      return getStudentsData(dashboardData, activeTerm, programData);
    }
    case chartTypes.PLO: {
      return getPloData(dashboardData);
    }
    default:
      return getBasicBarChartData();
  }
}

export function getCreditHoursData(dashboardData, programData) {
  const categories = getProgramNames(programData);
  const xAxis = [
    {
      type: 'category',
      data: categories,
      position: 'bottom',
      offset: 10,
    },
    {
      type: 'category',
      data: categories,
      position: 'bottom',
      axisLabel: {
        show: false,
      },
    },
  ];
  const tooltip = {
    ...commonChartOptions.tooltip,
    formatter: (params) => {
      try {
        return `${params[0].name} <br/> <div> ${indVerRename(
          'T'
        )}: <span style="float:right; padding-left: 8px;"> ${
          params[0].data + '/' + params[3].data
        } </span> </div> <div> ${indVerRename(
          'P'
        )}: <span style="float:right; padding-left: 8px;"> ${
          params[1].data + '/' + params[4].data
        }</span> </div> <div> ${indVerRename(
          'C'
        )}: <span style="float:right; padding-left: 8px;"> ${
          params[2].data + '/' + params[5].data
        } </span> </div>`;
      } catch (error) {
        return '';
      }
    },
  };

  const data = dashboardData.get('credit_hours', List());
  const {
    theory,
    theoryTotal,
    practical,
    practicalTotal,
    clinical,
    clinicalTotal,
  } = getTPCInitialSeriesData();
  const legend = getTPCLegend();
  for (const program of data) {
    theory.data.push(trimFractionDigits(program.get('theory_complete', 0), 2));
    theoryTotal.data.push(program.get('theory', 0));
    practical.data.push(trimFractionDigits(program.get('practical_complete', 0), 2));
    practicalTotal.data.push(program.get('practical', 0));
    clinical.data.push(trimFractionDigits(program.get('clinical_complete', 0), 2));
    clinicalTotal.data.push(program.get('clinical', 0));
  }
  const series = [theory, theoryTotal, practical, practicalTotal, clinical, clinicalTotal];
  return {
    ...getBasicBarChartData({
      title: t('reports_analytics.no_of_credit_hours'),
      categories,
      legend,
      series,
      showCount: true,
    }),
    xAxis,
    tooltip,
  };
}

export function getStaffsData(dashboardData, programData) {
  const categories = getProgramNames(programData);

  const legend = [
    t('reports_analytics.male'),
    t('reports_analytics.female'),
    t('reports_analytics.part_time'),
    t('reports_analytics.full_time'),
  ];

  const data = dashboardData.get('staffs', List());

  const maleSeries = { name: t('reports_analytics.male'), stack: 'MF', data: [] };
  const femaleSeries = { name: t('reports_analytics.female'), stack: 'MF', data: [] };
  const partTimeSeries = { name: t('reports_analytics.part_time'), stack: 'PF', data: [] };
  const fullTimeSeries = { name: t('reports_analytics.full_time'), stack: 'PF', data: [] };
  const totalSeries = {
    name: t('constant.total'),
    data: [],
    xAxisIndex: 1,
    zlevel: -1,
    label: { show: true, position: 'top' },
    emphasis: {
      focus: 'none',
    },
  };

  for (const program of data) {
    const m = program.get('male_count', 0);
    const f = program.get('female_count', 0);
    maleSeries.data.push(m);
    femaleSeries.data.push(f);
    partTimeSeries.data.push(program.get('part_time_count', 0));
    fullTimeSeries.data.push(program.get('full_time_count', 0));
    totalSeries.data.push(getSeriesDataObj(m + f, true));
  }
  const series = [maleSeries, femaleSeries, partTimeSeries, fullTimeSeries, totalSeries];

  return getBasicBarChartData({
    title: t('reports_analytics.no_of_staffs'),
    categories,
    legend,
    series,
    showCount: true,
  });
}

export function getAllCurriculums(dashboardData) {
  const programs = dashboardData.get('programs', List());
  let curriculumNames = Set();
  programs.forEach((program) => {
    program.get('curriculum', List()).forEach((curriculum) => {
      let curriculumName = curriculum.get('curriculum_name');
      curriculumNames = curriculumNames.add(curriculumName);
    });
  });
  return curriculumNames.toList();
}

export function getPloData(dashboardData) {
  const curriculumList = dashboardData
    .get('program_learning_outcomes', List())
    .reduce((acc, program) => {
      const curriculums = program.get('curriculums', List());
      program = program.delete('curriculums');
      return acc.concat(curriculums.map((curriculum) => curriculum.merge(program)));
    }, List());
  const categories = curriculumList
    .map((curriculum) => curriculum.get('curriculum_name', ''))
    .toJS();
  return {
    ...getBasicBarChartData({
      title: getEnvLabelChanged()
        ? `No. of ${indVerRename('PLO')}`
        : t('reports_analytics.no_of_plo'),
      categories,
      series: [
        {
          data: curriculumList.map((curriculum) => curriculum.get('plo_count', 0)).toJS(),
          label: { show: true, position: 'top' },
        },
      ],
      showCount: false,
    }),
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params) => {
        try {
          const name = params[0].name;
          const dataIndex = params[0].dataIndex;
          const curriculum =
            curriculumList.find(
              (curriculum, index) =>
                curriculum.get('curriculum_name', '') === name && index === dataIndex
            ) || Map();
          return `
            <div>
              ${curriculum.get('program_code', '')} - ${curriculum.get('program_name', '')}
            </div>
            <div>${name} - <span style="font-weight: bold;">${params[0].value}</span></div>
          `;
        } catch (error) {
          return `${params[0].name} - ${params[0].value}`;
        }
      },
    },
  };
}

export function getStudentsData(dashboardData, activeTerm, programData) {
  const categories = getProgramNames(programData);
  const data = dashboardData.get('students', List());

  const maleSeries = { name: t('reports_analytics.male'), stack: 'MF', data: [] };
  const femaleSeries = { name: t('reports_analytics.female'), stack: 'MF', data: [] };
  const totalSeries = {
    name: t('constant.total'),
    data: [],
    xAxisIndex: 1,
    zlevel: -1,
    label: { show: true, position: 'top' },
    emphasis: {
      focus: 'none',
    },
  };
  data.forEach((program) => {
    const term =
      program
        .get('student_term', List())
        .find((term) => term.get('term', '').toLowerCase() === activeTerm.toLowerCase()) || Map();

    const m = term.get('male_count', 0);
    const f = term.get('female_count', 0);
    maleSeries.data.push(m);
    femaleSeries.data.push(f);
    totalSeries.data.push(getSeriesDataObj(m + f, true));
  });

  const series = [maleSeries, femaleSeries, totalSeries];

  const legend = [t('reports_analytics.male'), t('reports_analytics.female')];

  return getBasicBarChartData({
    title: t('reports_analytics.no_of_students'),
    categories,
    legend,
    series,
    showCount: true,
  });
}

export function getProgramNames(dashboardData) {
  return dashboardData
    .get('programs', List())
    .map((program) => program.get('name'))
    .toJS();
}

export function getSeriesDataObj(value, transparent = false) {
  let o = { value };
  if (transparent) o.itemStyle = { color: 'transparent' };
  return o;
}

export const COUNT_INFO_TYPES = {
  STUDENT: 'student',
  STAFF: 'staff',
  COURSE: 'course',
  SESSION: 'session',
  SUBJECT: 'subject',
};

function getTotalCountTitle(type) {
  switch (type.toLowerCase()) {
    case 'staff':
      return t('reports_analytics.total_no_of_staffs');
    case 'student':
      return t('reports_analytics.total_no_of_students');
    case 'session':
      return t('reports_analytics.total_no_of_sessions');
    case 'credit hour':
      return t('reports_analytics.total_no_of_credit_hours');
    case 'contact hour':
      return t('reports_analytics.total_no_of_contact_hours');
    case 'course':
      return t('reports_analytics.total_no_of_courses');
    default:
      return `Total No. of ${capitalize(type)}s`;
  }
}

function getTPCLegendIndex(index) {
  const legend = getTPCLegend();
  return legend[index];
}

export const COUNT_INFO_GENERIC_TYPES = {
  GENERIC_SINGLE: [{ title: '', hexColorCode: '#3E95EF33' }],
  GENDER: [
    { type: 'male', title: 'Male', hexColorCode: '#0000FF33' },
    { type: 'female', title: 'Female', hexColorCode: '#FF005C33' },
  ],
  STAFF_STUDENT_STATUS: [
    { type: 'active', title: 'Active', hexColorCode: '#00FF8E33' },
    { type: 'inactive', title: 'In-Active', hexColorCode: '#FFF50033' },
  ],
  STUDENT_WARNING_STATUS: [
    { type: 'finalWarning', title: 'Final Warning', hexColorCode: '#FFF50033' },
    { type: 'denial', title: 'Denial', hexColorCode: '#FF000033' },
  ],
  STAFF_TYPES: [
    { type: 'fullTime', title: 'Full Time', hexColorCode: '#00FF8E33' },
    { type: 'partTime', title: 'Part Time', hexColorCode: '#FFF50033' },
  ],
  STAFF_ROLE_TYPES: [
    { type: 'academic', title: 'Academic', hexColorCode: '#FF005C33' },
    { type: 'administration', title: 'Administration', hexColorCode: '#0000FF33' },
  ],
  STAFF_ROLE_TYPES_BOTH: [
    { type: 'staff_role_both', title: 'Academic and Administration', hexColorCode: '#FFF50033' },
  ],
  COURSE_STATUS: [
    { type: 'completed', title: 'Completed', hexColorCode: '#00FF0A33' },
    { type: 'ongoing', title: 'Ongoing', hexColorCode: '#FFF50033' },
  ],
  NOT_STARTED_COURSE: [{ title: 'Not Started', hexColorCode: '#FFEDED' }],
  COURSE_SESSION_TYPES_TP: [
    { type: 'theory', title: getTPCLegendIndex(0), hexColorCode: '#7868E633' },
    { type: 'practical', title: getTPCLegendIndex(1), hexColorCode: '#31326F33' },
  ],
  COURSE_SESSION_TYPE_C: [
    { type: 'clinical', title: getTPCLegendIndex(2), hexColorCode: '#FFC93C33' },
  ],
  SESSION_STATUS_COMPLETE_PENDING: [
    { type: 'completed', title: 'Completed', hexColorCode: '#00FF0A33' },
    { type: 'pending', title: 'Pending', hexColorCode: '#FFF50033' },
  ],
  SESSION_STATUS_MERGED_CANCELED: [
    { type: 'merged', title: 'Merged', hexColorCode: '#7868E633' },
    { type: 'canceled', title: 'Canceled', hexColorCode: '#FF000033' },
  ],
  SESSION_STATUS_MISSED: [
    { type: 'missed', title: 'Missed', hexColorCode: 'rgb(255 60 224 / 12%)' },
  ],
  SUPPORT_ALL_SESSION: [{ type: '', title: getTPCLegendIndex(0), hexColorCode: '' }],
  NEW_GENERIC_SINGLE_SESSION: [
    { type: '', title: '', hexColorCode: '#3E95EF33', totalCountView: true },
  ],

  getTotalCountCardData({ type = '', count = 0 }) {
    return this.GENERIC_SINGLE.map((t) => ({
      ...t,
      title: getTotalCountTitle(type),
      subTitle: `${count}`,
    }));
  },
  getGenderCountCardData({ male = 0, female = 0 }) {
    return this.GENDER.map((g) => {
      const gender = g.type;
      return {
        ...g,
        title: gender === 'male' ? t('reports_analytics.male') : t('reports_analytics.female'),
        subTitle: `${gender === 'male' ? male : female}`,
      };
    });
  },
  getGenderSingleCountCardData({ value, type }) {
    return this.GENDER.filter((item) => item.type === type).map((g) => {
      return {
        ...g,
        title: g.type === 'male' ? t('reports_analytics.male') : t('reports_analytics.female'),
        subTitle: `${value}`,
      };
    });
  },
  getStaffStudentStatusCardData({ active = 0, inactive = 0 }) {
    return this.STAFF_STUDENT_STATUS.map((s) => {
      const status = s.type;
      return {
        ...s,
        subTitle: `${status === 'active' ? active : inactive}`,
      };
    });
  },
  getStaffTypeCardData({ fullTime = 0, partTime = 0 }) {
    return this.STAFF_TYPES.map((s) => {
      const status = s.type;
      return {
        ...s,
        title:
          status === 'fullTime'
            ? t('reports_analytics.full_time')
            : t('reports_analytics.part_time'),
        subTitle: `${status === 'fullTime' ? fullTime : partTime}`,
      };
    });
  },
  getStaffRoleTypeCardData({ academic = 0, administration = 0 }) {
    return this.STAFF_ROLE_TYPES.map((s) => {
      const status = s.type;
      return {
        ...s,
        title:
          status === 'academic'
            ? t('reports_analytics.academic')
            : t('reports_analytics.administration'),
        subTitle: `${status === 'academic' ? academic : administration}`,
      };
    });
  },
  getStaffRoleTypeBothCardData({ count = 0 }) {
    return this.STAFF_ROLE_TYPES_BOTH.map((s) => ({
      ...s,
      title: t('reports_analytics.academic_and_administration'),
      subTitle: `${count}`,
    }));
  },
  getStudentWarningCardData({ finalWarning = 0, denial = 0 }) {
    return this.STUDENT_WARNING_STATUS.map((g) => {
      const warningType = g.type;
      return {
        ...g,
        title:
          warningType === 'finalWarning'
            ? t('reports_analytics.final_warning')
            : t('reports_analytics.denial'),
        subTitle: `${warningType === 'finalWarning' ? finalWarning : denial}`,
      };
    });
  },
  getCourseStatusCardData({ completed = 0, onGoing = 0 }) {
    return this.COURSE_STATUS.map((s) => {
      const status = s.type;
      return {
        ...s,
        title:
          status === 'completed'
            ? t('reports_analytics.completed')
            : t('reports_analytics.ongoing'),
        subTitle: `${status === 'completed' ? completed : onGoing}`,
      };
    });
  },
  getSessionTypesTPCardData({ theory = 0, practical = 0 }) {
    return this.COURSE_SESSION_TYPES_TP.map((s) => {
      const status = s.type;
      return {
        ...s,
        title: getEnvLabelChanged()
          ? s.title
          : status === 'theory'
          ? t('reports_analytics.theory')
          : t('reports_analytics.practical'),
        subTitle: `${status === 'theory' ? theory : practical} `,
      };
    });
  },
  getSessionTypesCCardData({ count = 0 }) {
    return this.COURSE_SESSION_TYPE_C.map((s) => ({
      ...s,
      title: getEnvLabelChanged() ? s.title : t('reports_analytics.clinical'),
      subTitle: `${count}`,
    }));
  },
  getSessionStatusCompletePending({ completed = 0, pending = 0 }) {
    return this.SESSION_STATUS_COMPLETE_PENDING.map((s) => {
      const status = s.type;
      return {
        ...s,
        title:
          status === 'completed'
            ? t('reports_analytics.completed')
            : t('reports_analytics.pending'),
        subTitle: `${status === 'completed' ? completed : pending}`,
      };
    });
  },
  getSessionStatusMergedCanceled({ merged = 0, canceled = 0 }) {
    return this.SESSION_STATUS_MERGED_CANCELED.map((s) => {
      const status = s.type;
      return {
        ...s,
        title:
          status === 'merged' ? t('reports_analytics.merged') : t('reports_analytics.canceled'),
        subTitle: `${status === 'merged' ? merged : canceled}`,
      };
    });
  },
  getSessionStatusMissed({ missed = 0 }) {
    return this.SESSION_STATUS_MISSED.map((s) => {
      const status = s.type;
      return {
        ...s,
        title: t('reports_analytics.missed'),
        subTitle: `${status === 'missed' && missed}`,
      };
    });
  },
  getScheduledSessionCardData({ type = 'session', count = 0 }) {
    return this.NEW_GENERIC_SINGLE_SESSION.map((s) => ({
      ...s,
      title:
        type === 'session'
          ? t('reports_analytics.scheduled session')
          : `Scheduled ${capitalize(type)}`,
      subTitle: `${count}`,
    }));
  },
  getSupportSessionAllType({ type = '', count = 0 }) {
    return this.SUPPORT_ALL_SESSION.map((t) => ({
      ...t,
      title: `${capitalize(type)}`,
      subTitle: `${count}`,
    }));
  },
  getSingleSupportData({ type = 'supportSession', count = 0 }) {
    return this.NEW_GENERIC_SINGLE_SESSION.map((t) => ({
      ...t,
      title: i18n.t('reports_analytics.total_no_of_support_sessions'),
      subTitle: `${count}`,
    }));
  },
};

export function getBasicBarChartData({
  title = '',
  categories = [],
  legend = [],
  series = [],
  showCount = false,
}) {
  const xAxis = [
    {
      type: 'category',
      data: categories,
    },
    {
      type: 'category',
      data: categories,
      position: 'bottom',
      axisLabel: {
        show: false,
      },
    },
  ];
  if (!showCount) xAxis.pop();
  return {
    ...commonChartOptions,
    ...(title && {
      title: {
        text: title,
        textStyle: titleTextStyle,
        ...(lang === 'ar' && { right: 0 }),
      },
    }),
    legend: {
      data: legend,
    },
    xAxis,
    series: series.map((s) => ({ ...commonBarOptions, ...s })),
  };
}

export function getSessionCompletionPercentage(course) {
  const completedSessions = course.get('completed_session', 0);
  const totalSessions = course.get('no_session', 0);
  return calculatePercentage(completedSessions, totalSessions, false);
}

export function getStudentAttendancePercentage(course) {
  const presentSessions =
    course.get('present_count', 0) +
    course.get('onduty_count', 0) +
    course.get('permission_count', 0) -
    course.get('studentLateAbsent', 0);
  const completedSessions = course.get('completed_session', 0);
  return calculatePercentage(presentSessions, completedSessions, false);
}

export function getBasicPieChartData({ title = '', data = [], labelFormatter = '{c}' }) {
  const commonPieChartOptions = {
    tooltip: { trigger: 'item' },
    legend: { orient: 'vertical', right: '5%', top: 'center', formatter: '{name}' },
  };
  const commonPieChartSeriesData = {
    type: 'pie',
    radius: ['35%', '70%'],
    center: ['40%', '50%'],
    label: {
      show: true,
      position: 'inner',
      formatter: labelFormatter,
      fontSize: 14,
      color: '#3A3A3A',
      textShadowColor: 'rgba(0, 0, 0, 0.25)',
      textShadowBlur: 1,
      textShadowOffsetY: 1,
    },
  };
  return {
    ...commonPieChartOptions,
    title: { text: title, textStyle: titleTextStyle, ...(lang === 'ar' && { right: 0 }) },
    series: [
      {
        ...commonPieChartSeriesData,
        data: data.map((d, i) => ({
          itemStyle: { color: i % 2 === 0 ? '#3E95EF' : '#B8DBFF' },
          ...d,
        })),
      },
    ],
  };
}

export function getStaffRoleTypeChartData(staffData) {
  const categories = ['Academic', 'Administration', 'Academic and Administration'];
  const data = List(categories).map((c) => {
    let key = '';
    if (c === 'Academic') {
      key = 'academic';
    } else if (c === 'Administration') {
      key = 'admin';
    } else if (c === 'Academic and Administration') {
      key = 'both';
    }
    return staffData.get(key, Map());
  });

  const legend = [
    t('reports_analytics.male'),
    t('reports_analytics.female'),
    t('reports_analytics.part_time'),
    t('reports_analytics.full_time'),
  ];

  const maleSeries = { name: t('reports_analytics.male'), stack: 'MF', data: [] };
  const femaleSeries = { name: t('reports_analytics.female'), stack: 'MF', data: [] };
  const partTimeSeries = { name: t('reports_analytics.part_time'), stack: 'PF', data: [] };
  const fullTimeSeries = { name: t('reports_analytics.full_time'), stack: 'PF', data: [] };
  const totalSeries = {
    name: t('constant.total'),
    data: [],
    xAxisIndex: 1,
    zlevel: -1,
    label: { show: true, position: 'top' },
    emphasis: { focus: 'none' },
  };

  for (const count of data) {
    const m = count.get('male_count', 0);
    const f = count.get('female_count', 0);
    maleSeries.data.push(m);
    femaleSeries.data.push(f);
    partTimeSeries.data.push(count.get('part_time_count', 0));
    fullTimeSeries.data.push(count.get('full_time_count', 0));
    totalSeries.data.push(getSeriesDataObj(m + f, true));
  }
  const series = [maleSeries, femaleSeries, partTimeSeries, fullTimeSeries, totalSeries];

  return getBasicBarChartData({
    title: t('reports_analytics.no_of_staffs'),
    categories,
    legend,
    series,
    showCount: true,
  });
}

export function getStaffRoleTypeCountInfoCardData(staff) {
  const academic = staff.get('academic', Map());
  const administration = staff.get('admin', Map());
  const both = staff.get('both', Map());
  const academicCount = academic.get('male_count', 0) + academic.get('female_count', 0);
  const administrationCount =
    administration.get('male_count', 0) + administration.get('female_count', 0);
  const bothCount = both.get('male_count', 0) + both.get('female_count', 0);

  return [
    COUNT_INFO_GENERIC_TYPES.getTotalCountCardData({
      type: 'staff',
      count: staff.get('total', 0),
    }),
    COUNT_INFO_GENERIC_TYPES.getStaffRoleTypeCardData({
      academic: academicCount,
      administration: administrationCount,
    }),
    COUNT_INFO_GENERIC_TYPES.getStaffRoleTypeBothCardData({ count: bothCount }),
  ];
}

export function getFormattedDate(date) {
  date = new Date(date);
  if (!isValid(date)) return '';
  return format(startOfDay(date), 'P');
}

export function getStaffPersonalDetails(data) {
  const personalDetails = data.get('staff_detail', Map());
  const name = personalDetails.get('name', Map());

  return List([
    List([t('name'), `${name.get('first', '')} ${name.get('middle', '')} ${name.get('last', '')}`]),
    List([t('dob'), getFormattedDate(personalDetails.get('dob', ''))]),
    List([t('nationality'), personalDetails.get('nationality', '')]),
    List([t('reports_analytics.passport_no'), personalDetails.get('passport_no', '')]),
    List([t('email'), personalDetails.get('email', '')]),
    List([t('mobile_number'), personalDetails.get('mobile_no', 'NA') || 'NA']),
    List([t('office_extension'), personalDetails.getIn(['office', 'office_extension'], '')]),
  ]).map((column) => Map({ label: column.get(0), value: column.get(1) }));
}

export function getStaffAcademicInformation(data) {
  const details = data.get('staff_detail', Map());
  const employmentType =
    details.get('employment_type', '') === 'full_time'
      ? t('reports_analytics.full_time')
      : t('reports_analytics.part_time');
  const staffType =
    details.get('staff_employment_type', '') === 'both'
      ? t('reports_analytics.academic_and_administration')
      : capitalize(details.get('staff_employment_type', ''));
  return List([
    List([t('employee_id'), details.get('user_id', '')]),
    List([t('reports_analytics.date_of_joining'), getFormattedDate(details.get('createdAt', ''))]),
    List([t('reports_analytics.employment_type'), employmentType]),
    List([t('reports_analytics.staff_type'), staffType]),
  ]).map((column) => Map({ label: column.get(0), value: column.get(1) }));
}

export function getStaffCreditHoursData(courses, programId) {
  const legend = getTPCLegend(programId);
  const categories = courses
    .map(
      (c) =>
        `${c.get('course_no', '')}\n${c.get('program_name', '')}\n${levelRename(
          c.get('level', ''),
          programId
        )} / ${capitalize(c.get('term', ''))}`
    )
    .toJS();

  const xAxis = [
    { type: 'category', data: categories, position: 'bottom', offset: 10 },
    { type: 'category', data: categories, position: 'bottom', axisLabel: { show: false } },
  ];
  const tooltip = {
    ...commonChartOptions.tooltip,
    formatter: (params) => {
      try {
        return `${params[0].name.split('\n').join(' / ')}<br/>
          <div style="text-align: right;">${indVerRename('T', programId)}: ${
          params[0].data + '/' + params[3].data
        }</div>
          <div style="text-align: right;">${indVerRename('P', programId)}: ${
          params[1].data + '/' + params[4].data
        }</div>
          <div style="text-align: right;">${indVerRename('C', programId)}: ${
          params[2].data + '/' + params[5].data
        }</div>`;
      } catch (error) {
        return '';
      }
    },
  };

  const {
    theory,
    theoryTotal,
    practical,
    practicalTotal,
    clinical,
    clinicalTotal,
  } = getTPCInitialSeriesData(programId, legend);

  for (const course of courses) {
    const grouped = course
      .get('credit_session_details', List())
      .map((c) => c.set('type_name', c.get('type_name', '').toLowerCase()))
      .groupBy((c) => c.get('type_name'));
    ['theory', 'practical', 'clinical'].forEach((key) => {
      switch (key) {
        case 'theory': {
          theory.data.push(grouped.getIn([key, 0, 'completed_sessions'], 0));
          theoryTotal.data.push(grouped.getIn([key, 0, 'no_of_sessions'], 0));
          break;
        }
        case 'practical': {
          practical.data.push(grouped.getIn([key, 0, 'completed_sessions'], 0));
          practicalTotal.data.push(grouped.getIn([key, 0, 'no_of_sessions'], 0));
          break;
        }
        case 'clinical': {
          clinical.data.push(grouped.getIn([key, 0, 'completed_sessions'], 0));
          clinicalTotal.data.push(grouped.getIn([key, 0, 'no_of_sessions'], 0));
          break;
        }
        default:
          break;
      }
    });
  }
  const series = [theory, theoryTotal, practical, practicalTotal, clinical, clinicalTotal];
  return {
    ...getBasicBarChartData({
      title: t('reports_analytics.noOfSessions'),
      categories,
      legend,
      series,
      showCount: true,
    }),
    xAxis,
    tooltip,
  };
}

export function getStaffSessionTypesChartData(subject, programId) {
  const staffs = subject.get('staffs', List());
  const categories = staffs
    .map(
      (s) =>
        `${s.getIn(['staff_name', 'first'], '')} ${s.getIn(['staff_name', 'middle'], '')} ${s.getIn(
          ['staff_name', 'last'],
          ''
        )}`
    )
    .toJS();
  const xAxis = [
    { type: 'category', data: categories, position: 'bottom', offset: 10 },
    { type: 'category', data: categories, position: 'bottom', axisLabel: { show: false } },
  ];
  const tooltip = {
    ...commonChartOptions.tooltip,
    formatter: (params) => {
      try {
        return `${params[0].name.split('\n').join(' / ')}<br/>
          <div style="text-align: right;">${indVerRename('T', programId)}: ${
          params[0].data + '/' + params[3].data
        }</div>
          <div style="text-align: right;">${indVerRename('P', programId)}: ${
          params[1].data + '/' + params[4].data
        }</div>
          <div style="text-align: right;">${indVerRename('C', programId)}: ${
          params[2].data + '/' + params[5].data
        }</div>`;
      } catch (error) {
        return '';
      }
    },
  };

  const {
    theory,
    theoryTotal,
    practical,
    practicalTotal,
    clinical,
    clinicalTotal,
  } = getTPCInitialSeriesData(programId);

  for (const staff of staffs) {
    const grouped = staff
      .get('session_type_schedule', List())
      .map((c) => c.set('session_type', c.get('session_type', '').toLowerCase()))
      .groupBy((c) => c.get('session_type'));
    ['theory', 'practical', 'clinical'].forEach((key) => {
      switch (key) {
        case 'theory': {
          theory.data.push(grouped.getIn([key, 0, 'schedule_completed_count'], 0));
          theoryTotal.data.push(grouped.getIn([key, 0, 'schedule_count'], 0));
          break;
        }
        case 'practical': {
          practical.data.push(grouped.getIn([key, 0, 'schedule_completed_count'], 0));
          practicalTotal.data.push(grouped.getIn([key, 0, 'schedule_count'], 0));
          break;
        }
        case 'clinical': {
          clinical.data.push(grouped.getIn([key, 0, 'schedule_completed_count'], 0));
          clinicalTotal.data.push(grouped.getIn([key, 0, 'schedule_count'], 0));
          break;
        }
        default:
          break;
      }
    });
  }
  const legend = getTPCLegend();
  const series = [theory, theoryTotal, practical, practicalTotal, clinical, clinicalTotal];
  return {
    ...getBasicBarChartData({
      title: t('reports_analytics.no_of_sessions'),
      categories,
      legend,
      series,
      showCount: true,
    }),
    xAxis,
    tooltip,
  };
}

export function getCourseSessionTypesChartData(sessionTypes, programId) {
  const legend = getTPCLegend(programId);
  const xAxis = [
    { type: 'category', data: legend, position: 'bottom' },
    { type: 'category', data: legend, position: 'bottom', axisLabel: { show: false } },
  ];
  const tooltip = {
    ...commonChartOptions.tooltip,
    formatter: (params) => {
      try {
        return `${indVerRename(params[0].name, programId).split('\n').join(' / ')}<br/>
          <div>Completed: ${params[0].data}</div>
          <div>Total: ${params[1].data}</div>`;
      } catch (error) {
        return '';
      }
    },
  };
  const grouped = sessionTypes
    .map((s) => s.set('session_type', s.get('session_type', '').toLowerCase()))
    .groupBy((s) => s.get('session_type'));

  const { theory: sessionType, theoryTotal: sessionTypeTotal } = getTPCInitialSeriesData();

  ['theory', 'practical', 'clinical'].forEach((key) => {
    delete sessionType.name;
    sessionType.data.push(grouped.getIn([key, 0, 'schedule_completed_count'], 0));
    sessionTypeTotal.data.push(grouped.getIn([key, 0, 'schedule_count'], 0));
    sessionType.label.show = false;
  });

  const series = [sessionType, sessionTypeTotal];
  return {
    ...getBasicBarChartData({
      title: t('reports_analytics.no_of_sessions'),
      categories: legend,
      series,
      showCount: true,
    }),
    xAxis,
    tooltip,
  };
}
export function getSupportSessionTypesChartData(supportSessionType, programId) {
  const categories1 = supportSessionType
    .map((s) => indVerRename(s.get('type', ''), programId))
    .toJS();

  const xAxis = [
    { type: 'category', data: categories1, position: 'bottom' },
    { type: 'category', data: categories1, position: 'bottom', axisLabel: { show: false } },
  ];

  const tooltip = {
    ...commonChartOptions.tooltip,
    formatter: (params) => {
      try {
        return `${indVerRename(params[0].name, programId).split('\n').join(' / ')}<br/>
          <div>Completed: ${params[0].data}</div>
          <div>Total: ${params[1].data}</div>`;
      } catch (error) {
        return '';
      }
    },
  };
  const grouped1 = supportSessionType
    .map((s) => s.set('type', s.get('type', '').toLowerCase()))
    .groupBy((s) => s.get('type'));

  const { theory: sessionType, theoryTotal: sessionTypeTotal } = getTPCInitialSeriesData();
  [
    'counselling',
    'academic advisor',
    'feedback',
    'training',
    'theory',
    'practical',
    'clinical',
  ].forEach((key) => {
    delete sessionType.name;
    sessionType.data.push(grouped1.getIn([key, 0, 'complete_session'], 0));
    sessionTypeTotal.data.push(grouped1.getIn([key, 0, 'total_session'], 0));
    sessionType.label.show = false;
  });
  const series = [sessionType, sessionTypeTotal];
  return {
    ...getBasicBarChartData({
      title: t('reports_analytics.no_of_sessions'),
      categories1,
      series,
      showCount: true,
    }),
    xAxis,
    tooltip,
  };
}

export function getCourseDeliveryTypesChartData(deliveryTypes) {
  const categories = Set(deliveryTypes.map((d) => d.get('session_type', ''))).toJS();
  const xAxis = [
    { type: 'category', data: categories, position: 'bottom' },
    { type: 'category', data: categories, position: 'bottom', axisLabel: { show: false } },
  ];
  const tooltip = {
    ...commonChartOptions.tooltip,
    formatter: (params) => {
      try {
        return `${params[0].name.split('\n').join(' / ')}<br/>
          <div>Completed: <span style="font-weight: bold;">${params[0].data}</span></div>
          <div>Total: <span style="font-weight: bold;">${params[1].data}</span></div>`;
      } catch (error) {
        return '';
      }
    },
  };

  const { theory: sessionType, theoryTotal: sessionTypeTotal } = getTPCInitialSeriesData();

  const support = deliveryTypes.filter((y) => y.get('session_type', '') === 'support_session');

  const regular = deliveryTypes
    .filter((y) => y.get('session_type', '') !== 'support_session')
    .toJS();

  const completedSession = support.reduce((acc, value) => {
    return value.get('completed_session') + acc;
  }, 0);
  const totalSession = support.reduce((acc, value) => {
    return value.get('total_session') + acc;
  }, 0);

  const supportSession = {
    support_session: 'support_session',
    completed_session: completedSession,
    total_session: totalSession,
  };

  const deliveryType = fromJS([...regular, supportSession]);

  deliveryType.forEach((d) => {
    sessionType.data.push(d.get('completed_session', 0));
    sessionTypeTotal.data.push(d.get('total_session', 0));
    sessionType.label.show = false;
  });

  const series = [sessionType, sessionTypeTotal];
  return {
    ...getBasicBarChartData({
      title: t('reports_analytics.no_of_sessions'),
      categories,
      series,
      showCount: true,
    }),
    xAxis,
    tooltip,
  };
}

function getCourseDetails(course) {
  const courseNo = course.get('course_no', '');
  const courseName = course.get('course_name', '');
  const programName = course.get('program_name', '');
  const versionName = getVersionName(course);
  const year = course.get('year', '').split('year').join('Year ');
  const level = levelRename(course.get('level', ''), course.get('_program_id', ''));
  const term = capitalize(course.get('term', ''));
  const courseType = capitalize(
    indVerRename(course.get('course_type', ''), course.get('_program_id', ''))
  );
  const isRotation = course.get('rotation', '') === 'yes';
  const rotationCount = course.get('rotation_count', '');
  const courseInfo = `${programName} | ${year}, ${level}, ${term}${
    isRotation ? `, Rotation ${rotationCount}` : ''
  } | ${courseType} | ${getFormattedCourseDuration(course)}`;

  return { courseNo, courseName, courseInfo, versionName };
}

export function exportStaffDetailsAsPDF(columns, course, staffs) {
  const { courseNo, courseName, courseInfo, versionName } = getCourseDetails(course);

  columns = columns.map((column) => ({ header: column.name, dataKey: column.key }));
  const body = staffs
    .map((staff) =>
      Map({
        formattedName: staff.get('formattedName', ''),
        staffId: staff.get('staffId', ''),
        gender: staff.get('gender', ''),
        formattedStudentGroups: staff.get('formattedStudentGroups', ''),
        total_session: staff.get('total_session', ''),
        formattedSessions: staff.get('formattedSessions', ''),
        formattedLeave: staff.get('formattedLeave', ''),
        formattedAttendancePercentage: staff.get('formattedAttendancePercentage', ''),
      })
    )
    .toJS();

  const doc = new jsPDF();
  doc.addFont(RobotoRegular, 'Roboto', 'normal');

  doc.setFont('Roboto', 'normal', 500);

  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text('Staff Details', 10, 10);
  doc.setLineWidth(0.1);
  doc.setDrawColor(0, 0, 0, 0.18);
  doc.line(0, 15, 210, 15);

  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text(`${courseNo} - ${courseName}${versionName}`, 10, 25);

  doc.setFont('Roboto', 'normal', 400);
  doc.setFontSize(9);
  doc.setTextColor(0, 0, 0, 0.54);
  doc.text(courseInfo, 10, 30, { maxWidth: 190 });

  doc.autoTable({
    startY: 40,
    margin: { right: 10, left: 10 },
    styles: { fillColor: false, textColor: '#000000', font: 'Roboto' },
    columnStyles: { 5: { cellWidth: 20 } },
    headStyles: { lineWidth: 0.5, lineColor: [189, 195, 199] },
    bodyStyles: { fontSize: 9 },
    rowPageBreak: 'avoid',
    columns,
    body: body.length
      ? body
      : [[{ content: 'No data found', colSpan: columns.length, styles: { halign: 'center' } }]],
    theme: 'grid',
  });
  doc.save(`${courseNo}-${courseName}-Staffs.pdf`);
}

export function exportStudentDetailsAsPDF(columns, course, students) {
  const { courseNo, courseName, courseInfo, versionName } = getCourseDetails(course);

  columns = columns.map((column) => ({ header: column.name, dataKey: column.key }));
  const body = students
    .map((student) =>
      Map({
        formattedName: student.get('formattedName', ''),
        academicNo: student.get('academicNo', ''),
        gender: student.get('gender', ''),
        total_session: student.get('total_session', ''),
        formattedSessionsAttended: student.get('formattedSessionsAttended', ''),
        formattedWarning: student.get('formattedWarning', ''),
        formattedAttendancePercentage: student.get('formattedAttendancePercentage', ''),
      })
    )
    .toJS();

  const doc = new jsPDF();
  doc.addFont(RobotoRegular, 'Roboto', 'normal');

  doc.setFont('Roboto', 'normal', 500);

  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text('Student Details', 10, 10);
  doc.setLineWidth(0.1);
  doc.setDrawColor(0, 0, 0, 0.18);
  doc.line(0, 15, 210, 15);

  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text(`${courseNo} - ${courseName}${versionName}`, 10, 25);

  doc.setFont('Roboto', 'normal', 400);
  doc.setFontSize(9);
  doc.setTextColor(0, 0, 0, 0.54);
  doc.text(courseInfo, 10, 30, { maxWidth: 190 });

  doc.autoTable({
    startY: 40,
    margin: { right: 10, left: 10 },
    styles: { fillColor: false, textColor: '#000000', font: 'Roboto' },
    headStyles: { lineWidth: 0.5, lineColor: [189, 195, 199] },
    bodyStyles: { fontSize: 9 },
    rowPageBreak: 'avoid',
    columns,
    body: body.length
      ? body
      : [[{ content: 'No data found', colSpan: columns.length, styles: { halign: 'center' } }]],
    theme: 'grid',
  });
  doc.save(`${courseNo}-${courseName}-Students.pdf`);
}

export function exportStaffStudentAttendanceReportAsPDF({ course, userType, user, body, summary }) {
  const name = user.get('name', '');
  const id = user.get('id', '');
  const gender = user.get('gender', '');

  const { courseNo, courseName, courseInfo, versionName } = getCourseDetails(course);

  const doc = new jsPDF();
  doc.addFont(RobotoRegular, 'Roboto', 'normal');

  doc.setFont('Roboto', 'normal', 500);
  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text('Attendance Report', 10, 10);
  doc.setLineWidth(0.1);
  doc.setDrawColor(0, 0, 0, 0.18);
  doc.line(0, 15, 210, 15);

  doc.setFont('Roboto', 'normal', 400);
  doc.setFontSize(9);
  doc.setTextColor(0, 0, 0, 0.54);
  doc.text(`${capitalize(userType)} Name`, 10, 25);

  doc.setFontSize(10.5);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text(name, 10, 30);

  doc.setFontSize(9);
  doc.setTextColor(0, 0, 0, 0.54);
  doc.text(`${id}, ${capitalize(gender)}`, 10, 35);

  if (userType === 'staff') {
    doc.setFontSize(10.5);
    doc.setTextColor(0, 0, 0, 0.87);
    doc.text(user.get('subjects', ''), 10, 40);
  }

  doc.setFont('Roboto', 'normal', 500);
  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text(`${courseNo} - ${courseName}${versionName}`, 10, 50);

  doc.setFont('Roboto', 'normal', 400);
  doc.setFontSize(9);
  doc.setTextColor(0, 0, 0, 0.54);
  doc.text(courseInfo, 10, 55, { maxWidth: 190 });

  const columns = [
    { header: 'S.No.', dataKey: 'sNo' },
    { header: 'Session Details', dataKey: 'details' },
    { header: 'Attendance', dataKey: 'status' },
  ];

  doc.autoTable({
    startY: 60,
    margin: { right: 10, left: 10 },
    showFoot: 'lastPage',
    styles: { fillColor: false, textColor: '#000000', cellPadding: 6, font: 'Roboto' },
    headStyles: { lineWidth: 0.2, lineColor: '#e0e0e0', halign: 'center' },
    bodyStyles: { fontSize: 10.5, lineColor: '#e0e0e0' },
    columnStyles: {
      sNo: { cellWidth: 25, halign: 'center' },
      status: { cellWidth: 60, halign: 'center' },
    },
    footStyles: { lineWidth: 0.2, lineColor: '#e0e0e0', halign: 'center', cellPadding: 4 },
    rowPageBreak: 'avoid',
    columns,
    body: body.length
      ? body
      : [[{ content: 'No data found', colSpan: columns.length, styles: { halign: 'center' } }]],
    foot: [
      {
        sNo: {
          content: 'SUMMARY',
          colSpan: 2,
          styles: { textColor: '#ffffff', fillColor: '#A0AAB5' },
        },
        status: {
          content: `${summary.percentage}\n${summary.info}`,
          styles: { fillColor: '#F8FAFC' },
        },
      },
    ],
    theme: 'grid',
    didParseCell: (data) => {
      if (data.column.dataKey === 'details') {
        if (data.section === 'head') {
          data.cell.styles.halign = 'left';
        }
      }
      if (data.column.dataKey === 'status') {
        if (data.section === 'body') {
          const status = data.row.raw.status;
          const lateLabel = data.row.raw.lateLabel;
          if (status === '-') {
            data.cell.text[0] = '---';
          }
          if (['A', 'L'].includes(status)) {
            data.cell.styles.textColor = '#ff0000';
          }
          if (['P', 'PER', 'OD'].includes(status)) {
            data.cell.styles.textColor = '#3BC10C';
            if (status === 'P') {
              data.cell.styles.halign = 'center';
              data.cell.text[0] = status;
              data.cell.text[1] = lateLabel === null ? '' : `(${lateLabel})`;
            }
          }
          if (status === 'E') {
            data.cell.styles.textColor = '#D97706';
          }
        }
      }
      if (data.section === 'body' && data.row.raw.status === 'SESSION CANCELED') {
        data.cell.styles.fillColor = [240, 240, 240];
      }
    },
  });
  doc.save(`${courseNo}-${courseName}-${name}.pdf`);
}

export function exportStaffStudentAttendanceLogAsPDF({ course, userType, header, body }) {
  const { courseNo, courseName, courseInfo, versionName } = getCourseDetails(course);

  const doc = new jsPDF();
  doc.addFont(RobotoRegular, 'Roboto', 'normal');

  doc.setFont('Roboto', 'normal', 500);

  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text(`${capitalize(userType)} Attendance Log`, 10, 10);
  doc.setLineWidth(0.1);
  doc.setDrawColor(0, 0, 0, 0.18);
  doc.line(0, 15, 210, 15);

  doc.setFontSize(15);
  doc.setTextColor(0, 0, 0, 0.87);
  doc.text(`${courseNo} - ${courseName}${versionName}`, 10, 25);

  doc.setFont('Roboto', 'normal', 400);
  doc.setFontSize(9);
  doc.setTextColor(0, 0, 0, 0.54);
  doc.text(courseInfo, 10, 30, { maxWidth: 190 });

  const chunkedHeader = chunkArray(header, 4);
  const chunkedBody = chunkArray(body, 8);

  chunkedBody.forEach((bodyItems, i) => {
    chunkedHeader.forEach((headerItems, j) => {
      const columns = [
        { header: 'S.No.', dataKey: 'sNo' },
        { header: `${capitalize(userType)} Details`, dataKey: 'details' },
      ].concat(headerItems);

      const columnStyles = { sNo: { cellWidth: 25 }, details: { cellWidth: 45 } };
      headerItems.forEach((h) => {
        columnStyles[h.dataKey] = { cellWidth: 30 };
      });

      const startY = i === 0 && j === 0 ? 35 : doc.lastAutoTable.finalY + 5;

      doc.autoTable({
        startY,
        pageBreak: 'avoid',
        rowPageBreak: 'avoid',
        margin: { right: 10, left: 10 },
        showFoot: 'never',
        styles: { fillColor: false, textColor: '#000000', cellPadding: 6, font: 'Roboto' },
        headStyles: { lineWidth: 0.2, lineColor: '#e0e0e0', halign: 'center' },
        bodyStyles: { fontSize: 10.5, lineColor: '#e0e0e0', halign: 'center' },
        columnStyles,
        columns,
        body: bodyItems,
        theme: 'grid',
        didParseCell: (data) => {
          if (!['sNo', 'details'].includes(data.column.dataKey)) {
            if (data.column.index !== 1 && data.column.index % 2 !== 0) {
              data.cell.styles.fillColor = [248, 250, 252];
            }

            const scheduleId = data.column.dataKey;
            const schedule = data.row.raw[scheduleId];
            if (data.section === 'head') {
              if (data.column.raw.isActive === false) {
                data.cell.styles.fillColor = [255, 226, 226];
              }
            }
            if (data.section === 'body') {
              if (schedule.isActive === false) {
                data.cell.styles.fillColor = [255, 226, 226];
              }
              const status = schedule.attendanceStatus;
              if (status === '---') {
                data.cell.text = ['---'];
              } else if (['A', 'L'].includes(status)) {
                data.cell.text = [status];
                data.cell.styles.textColor = '#ff0000';
              } else if (['P', 'OD'].includes(status)) {
                data.cell.text = [status];
                data.cell.styles.textColor = '#3BC10C';
              } else {
                data.cell.text = [''];
              }
            }
          }
        },
        didDrawPage: (data) => {
          doc.setFont('Roboto', 'normal', 400);
          doc.setFontSize(9);
          doc.setTextColor(0, 0, 0, 0.54);
          const pageSize = doc.internal.pageSize;
          const pageHeight = pageSize.height ? pageSize.height : pageSize.getHeight();
          doc.text('Powered by Digival Solutions', 85, pageHeight - 5);
        },
      });
    });
  });

  const summaryHeader = chunkArray(header, 6);

  summaryHeader.forEach((headerItems, i) => {
    if (i === 0) {
      doc.addPage();
      doc.setFont('Roboto', 'normal', 500);
      doc.setFontSize(15);
      doc.setTextColor(0, 0, 0, 0.87);
      doc.text('Summary', 10, 10);
      doc.setLineWidth(0.1);
      doc.setDrawColor(0, 0, 0, 0.18);
      doc.line(0, 15, 210, 15);
    }
    const columns = headerItems;

    const columnStyles = {};
    headerItems.forEach((h) => {
      columnStyles[h.dataKey] = { cellWidth: 30 };
    });

    const startY = i === 0 ? 25 : doc.lastAutoTable.finalY + 5;

    doc.autoTable({
      startY,
      pageBreak: 'avoid',
      rowPageBreak: 'avoid',
      margin: { right: 10, left: 10 },
      showFoot: 'never',
      styles: { fillColor: false, textColor: '#000000', cellPadding: 4, font: 'Roboto' },
      headStyles: { lineWidth: 0.2, lineColor: '#e0e0e0', halign: 'center' },
      bodyStyles: { fontSize: 10.5, lineColor: '#e0e0e0', halign: 'center' },
      columnStyles,
      columns,
      body: [
        headerItems.reduce((acc, h) => {
          acc[h.dataKey] = h;
          return acc;
        }, {}),
      ],
      theme: 'grid',
      didParseCell: (data) => {
        if (data.column.index % 2 !== 0) {
          data.cell.styles.fillColor = [248, 250, 252];
        }

        const scheduleId = data.column.dataKey;
        const schedule = data.row.raw[scheduleId];
        if (data.section === 'head') {
          if (data.column.raw.isActive === false) {
            data.cell.styles.fillColor = [255, 226, 226];
          }
        }
        if (data.section === 'body') {
          if (schedule.isActive === false) {
            data.cell.styles.fillColor = [255, 226, 226];
          }

          const status = schedule.formattedSummary;
          if (status === 'Pending') {
            data.cell.text = [status];
            data.cell.styles.textColor = '#ffa800';
          } else if (status === 'Session Canceled') {
            data.cell.text = [status];
            data.cell.styles.textColor = '#ff0000';
          } else {
            data.cell.text = status.split('\n');
            data.cell.styles.fontStyle = 'bold';
          }
        }
      },
      didDrawPage: (data) => {
        doc.setFont('Roboto', 'normal', 400);
        doc.setFontSize(9);
        doc.setTextColor(0, 0, 0, 0.54);
        const pageSize = doc.internal.pageSize;
        const pageHeight = pageSize.height ? pageSize.height : pageSize.getHeight();
        doc.text('Powered by Digival Solutions', 85, pageHeight - 5);
      },
    });
  });

  doc.save(`${courseNo}-${courseName}.pdf`);
}

export function getActiveLink(CheckPermission) {
  let Url = '';
  if (CheckPermission('tabs', 'Reports and Analytics', 'Course Details', '', 'Overview', 'View')) {
    Url = 'overview';
  } else if (
    CheckPermission('tabs', 'Reports and Analytics', 'Course Details', '', 'Attendance Log', 'View')
  ) {
    Url = 'attendance-log';
  } else if (
    CheckPermission('tabs', 'Reports and Analytics', 'Course Details', '', 'Session Status', 'View')
  ) {
    Url = 'session-status';
  } else if (
    CheckPermission(
      'tabs',
      'Reports and Analytics',
      'Course Details',
      '',
      'Student Details',
      'View'
    )
  ) {
    Url = 'students';
  } else if (
    CheckPermission('tabs', 'Reports and Analytics', 'Course Details', '', 'Staff Details', 'View')
  ) {
    Url = 'staffs';
  }
  return Url;
}

export function getSessionFlow(sessionFlow) {
  if (sessionFlow.size > 0) {
    return sessionFlow
      .filter((item) => item !== null && item.get('type', '') === 'regular')
      .map((data) => data.get('delivery_symbol', '') + data.get('delivery_no', ''))
      .join(', ');
  }
  return '';
}

export function formatTwoString(str) {
  if (str !== undefined && str !== '') {
    const length = str.toString().length;
    if (length === 1) {
      return '0' + str;
    }
  }
  return str;
}

export function getTimestamp(date) {
  return Math.round(new Date(date).getTime() / 1000);
}
export function secondsToHms(d) {
  d = Number(d);
  var h = Math.floor(d / 3600);
  var m = Math.floor((d % 3600) / 60);
  var s = Math.floor((d % 3600) % 60);

  var hDisplay = h > 0 ? h + (h === 1 ? ' h ' : ' h ') : '';
  var mDisplay = m > 0 ? m + (m === 1 ? ' m ' : ' m ') : '';
  var sDisplay = s > 0 ? s + (s === 1 ? ' s ' : ' s ') : '';
  return hDisplay + mDisplay + sDisplay;
}
export function msToTime(duration) {
  //var milliseconds = Math.floor((duration % 1000) / 100);
  var seconds = Math.floor((duration / 1000) % 60);
  var minutes = Math.floor((duration / (1000 * 60)) % 60);
  var hours = Math.floor((duration / (1000 * 60 * 60)) % 24);
  hours = hours < 10 ? '0' + hours : hours;
  minutes = minutes < 10 ? '0' + minutes : minutes;
  seconds = seconds < 10 ? '0' + seconds : seconds;
  return hours + ':' + minutes + ':' + seconds; // + '.' + milliseconds
}
export function getTimeSlotDuration(start, end) {
  if (getTimestamp(start) < getTimestamp(end)) {
    var diff = (new Date(end).getTime() - new Date(start).getTime()) / 1000;
    //diff /= 60;
    const value = diff / 60;
    return `${secondsToHms(diff)} (${Number(value).toFixed(2)})`; // + Number(diff).toFixed(2) + ' mins'; //Math.round(diff, 2) + ' mins';
  }
  return '0 mins';
}

export function CapitalizeAll(sentence) {
  if (typeof sentence !== 'string') return sentence;
  return sentence
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export function formattedFullName(data = '') {
  if (data !== '') {
    const firstName = data.first !== undefined ? data.first : '';
    const middleName = data.middle !== undefined ? data.middle : '';
    const lastName = data.last !== undefined ? data.last : '';
    return CapitalizeAll(firstName + ' ' + middleName + ' ' + lastName);
  }
  return '';
}

export function getMonthsArray(start_date, end_date) {
  const startDate = moment(start_date);
  const endDate = moment(end_date);
  const monthsArray = [];

  // Loop through the months starting from the start_date
  let currentDate = startDate.clone();
  while (currentDate.isSameOrBefore(endDate, 'month')) {
    const monthString = currentDate.format('MMMM YYYY');
    monthsArray.push({ name: monthString, value: monthString });

    // Move to the next month
    currentDate.add(1, 'month');
  }

  return monthsArray;
}

export function getFirstAndLastDateOfMonth(details, month) {
  const date = month === '' ? '' : moment(month, 'MMMM YYYY');
  const firstDate =
    month === ''
      ? moment(details.get('start_date', '')).format('YYYY-MM-DD')
      : date.startOf('month').format('YYYY-MM-DD');
  const lastDate =
    month === ''
      ? moment(details.get('end_date', '')).format('YYYY-MM-DD')
      : date.endOf('month').format('YYYY-MM-DD');

  return { firstDate, lastDate };
}

export function columnArray() {
  return [
    {
      label: 'Gender',
      name: 'gender',
      checked: false,
    },
    {
      label: 'Student Roll Number',
      name: 'user_id',
      checked: false,
    },
    {
      label: 'Delivery Group',
      name: 'delivery_group',
      checked: false,
    },
    {
      label: 'Delivery Date',
      name: 'schedule_date',
      checked: false,
    },
    {
      label: 'Attendance Summary',
      name: 'attendance_summary',
      checked: false,
    },
    {
      label: 'Overall Attendance %',
      name: 'overall',
      checked: false,
    },
    {
      label: 'Monthly Attendance %',
      name: 'monthly',
      checked: false,
    },
  ];
}

export function excelDuration(details, month = '') {
  const { firstDate, lastDate } = getFirstAndLastDateOfMonth(details, month);
  const startYear = moment(firstDate).format('YYYY');
  const endYear = moment(lastDate).format('YYYY');
  const sameYear = startYear === endYear;
  const st = moment(new Date(firstDate)).format(`MMM Do`);
  const ed = moment(new Date(lastDate)).format(`MMM Do, YYYY`);
  return `${st}${sameYear ? '' : `, ${startYear}`} - ${ed}`;
}

export const getUserData = (studentGroup, activeViewType, activeGender, searchTerm) => {
  return studentGroup.reduce(
    (acc, users) =>
      acc.concat(
        users.get(`${activeViewType}s`, List()).filter((user) => {
          let incGender = true;
          let incSearch = true;
          if (activeGender) {
            incGender = user.get('gender', '') === activeGender;
          }
          if (searchTerm) {
            const name = user.get(
              activeViewType.toLowerCase() === 'student' ? 'name' : 'staff_name',
              Map()
            );
            const middleName = name.get('middle', '');
            const formattedName = `${name.get('first', '')}${
              middleName ? ` ${middleName}` : ''
            } ${name.get('last', '')}`.toLowerCase();
            const userId = `${user.get('academic_no', '') || ''}`.toLowerCase();
            incSearch =
              formattedName.indexOf(searchTerm) !== -1 || userId.indexOf(searchTerm) !== -1;
          }
          return incGender && incSearch;
        })
      ),
    List()
  );
};

export const nameFormatIfMiddleName = (first = '', middle = '', last = '') => {
  if (middle !== '') {
    return `${first} ${middle} ${last}`;
  } else if (middle === '') {
    return `${first} ${last}`;
  }
};
