import React, { Fragment, useReducer, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  toggleModal,
  saveLevelEvents,
  saveSyncEvents,
} from '../../../../_reduxapi/actions/calender';
import {
  FlexWrapper,
  PrimaryButton,
  Null,
  BlockWrapper,
  Paragraph,
  Label,
  Input,
} from '../../Styled';
import { initial_event_state, events_reducer } from './reducers';
import AddEvent from './AddEvent';
import SyncEvent from './SyncEvent';
import { NotificationManager } from 'react-notifications';
import moment from 'moment';
import { timeFormat } from '../../../../utils';
import { Trans } from 'react-i18next';

const dataAlign = (data, active, dispatchFn, type, id, year, min, max, activeTerm) => {
  //let events = [...year['level_one_course_events'], ...year['level_two_course_events']];

  let events = year?.['raw_data']?.['level']
    .filter((item) => item.term === activeTerm)
    .map((item) => item.events)
    .reduce((acc, item) => {
      return acc.concat(item);
    }, []);
  let error = false;
  let final = { event_name: {} };

  const start = data.start_date + ' ' + data.start_time + ':00:000';
  const end = data.end_date + ' ' + data.end_time + ':00:000';

  let startDate = moment(data.start_date).format('YYYY-MM-DD');
  let endDate = moment(data.end_date).format('YYYY-MM-DD');
  let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
  let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

  let st = moment(startDate + 'T' + startTime).toDate();
  let et = moment(endDate + 'T' + endTime).toDate();

  const check_start = st;
  const check_end = et;

  if (type === 'manual') {
    events.forEach((item) => {
      if (Date.parse(item.start_time) <= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_start)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) <= Date.parse(check_end) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.start_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.end_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      }
    });
    if (data.title.trim() === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.end_date) < Date.parse(data.start_date)) {
      NotificationManager.error('End date should be greater than Start date');
      error = true;
    } else if (Date.parse(start) > Date.parse(end) || Date.parse(start) === Date.parse(end)) {
      NotificationManager.error('End time should be greater than Start time');
      error = true;
    } else if (Date.parse(min) > Date.parse(start)) {
      error = true;
      NotificationManager.error('Event start date should not be lesser than level start dates');
    }
    // else if (Date.parse(max) < Date.parse(start)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Event start date should not be greater than level end date"
    //   );
    // }
    // else if (Date.parse(max) < Date.parse(end)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Event end date should not be greater than level end date"
    //   );
    // }

    // let startTime = timeFormat(moment(this.state.startTimeView).format("H:mm:ss"));
    //   st = moment(startDate+"T"+startTime).toDate();

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    final.event_calendar = 'program';
    final.year = active;
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final.end_date = endDate;
    final._calendar_id = id;
    final.batch = activeTerm;
  } else {
    let syncData = {};
    if (data.length > 0) {
      syncData._calendar_id = id;
      syncData.year = active;
      syncData.batch = activeTerm;
      let eventIdGrouping = data.map((event) => {
        return event._id;
      });
      syncData._event_id = eventIdGrouping;
      final = syncData;
    } else {
      NotificationManager.error('Choose atleast one events');
      error = true;
    }
  }
  if (!error) {
    dispatchFn(final, NotificationManager);
  }
};

const EventsModal = (props) => {
  const {
    active,
    events,
    toggleModal,
    saveLevelEvents,
    saveSyncEvents,
    _calender_id,
    eventMinDate,
    eventMaxDate,
    activeTerm,
  } = props;

  const [event_method, setEvent_method] = useReducer(events_reducer, initial_event_state);

  useEffect(() => {
    setEvent_method({ type: 'SOURCE_FOR_COPY', payload: events });
  }, [events]);
  // const levelStartDate =
  //   props[active]?.level_one_start_date !== '' ? new Date(props[active]?.level_one_start_date) : '';
  // const levelEndDate =
  //   props?.[active]?.level_two_end_date !== undefined && props?.[active]?.level_two_end_date !== ''
  //     ? props?.[active]?.level_two_end_date
  //     : props?.[active]?.level_one_end_date;
  // const levelEndDate =
  //   props[active]?.level_one_end_date !== '' ? new Date(props[active]?.level_one_end_date) : '';
  return (
    <Fragment>
      <BlockWrapper>
        <h3 className="text-left">
          <Trans i18nKey={'role_management.role_actions.Add Event'}></Trans>
        </h3>
        <Paragraph className="text-left">
          {' '}
          <Trans i18nKey={'select_date_to_sync'}></Trans>
        </Paragraph>
      </BlockWrapper>
      <FlexWrapper mg="20px 0">
        <Input
          style={{ marginTop: '0px' }}
          type="radio"
          name="event_mode"
          value="manual"
          id="manual"
          checked={event_method.event_mode === 'manual'}
          onChange={(e) =>
            setEvent_method({
              type: 'EVENT_MODE_CHANGE',
              payload: e.target.value,
              name: e.target.name,
            })
          }
        />
        <Label htmlFor="manual">
          <Trans i18nKey={'add_event_manually'}></Trans>
        </Label>
      </FlexWrapper>
      <Fragment>
        {event_method.event_mode === 'manual' && (
          <BlockWrapper>
            <AddEvent
              data={event_method.type}
              method={setEvent_method}
              min_len={eventMinDate}
              max_len={eventMaxDate}
              levelStartDate={eventMinDate}
              levelEndDate={eventMaxDate}
            />
          </BlockWrapper>
        )}
      </Fragment>
      <FlexWrapper mg="20px 0">
        <Input
          type="radio"
          name="event_mode"
          id="sync"
          value="sync"
          checked={event_method.event_mode === 'sync'}
          disabled={event_method.source.length === 0 && true}
          onChange={(e) =>
            setEvent_method({
              type: 'EVENT_MODE_CHANGE',
              payload: e.target.value,
              name: e.target.name,
            })
          }
        />
        <Label htmlFor="sync">
          <Trans i18nKey={'add_events_from_institution_calendar'}></Trans>
        </Label>
      </FlexWrapper>
      <Fragment>
        {event_method.event_mode === 'sync' &&
          event_method.source &&
          event_method.source.length !== 0 && (
            <BlockWrapper>
              <SyncEvent data={event_method.source} method={setEvent_method} />
            </BlockWrapper>
          )}
      </Fragment>
      <FlexWrapper className="ji_end">
        <Null />
        <PrimaryButton className="light" onClick={() => toggleModal()}>
          <Trans i18nKey={'cancel'}></Trans>
        </PrimaryButton>
        <PrimaryButton
          className="bordernone"
          onClick={() => {
            if (event_method.event_mode === 'manual') {
              dataAlign(
                event_method.type,
                active,
                saveLevelEvents,
                'manual',
                _calender_id,
                props[active],
                eventMinDate,
                eventMaxDate,
                activeTerm
                // props[active]["id"]
              );
              //setEvent_method({ type: "CLEAR", payload: "manual" });
              //toggleModal();
            } else {
              dataAlign(
                event_method.copied,
                active,
                saveSyncEvents,
                'sync',
                _calender_id,
                props[active],
                eventMinDate,
                eventMaxDate,
                activeTerm
                // props[active]["id"]
              );
              //saveLevelEvents(event_method.copied, active, id);
              //setEvent_method({ type: "CLEAR", payload: "sync"  });
              //toggleModal();
            }
          }}
        >
          <Trans i18nKey={'save'}></Trans>
        </PrimaryButton>
      </FlexWrapper>
    </Fragment>
  );
};

EventsModal.propTypes = {
  active: PropTypes.string,
  events: PropTypes.array,
  toggleModal: PropTypes.func,
  saveLevelEvents: PropTypes.func,
  saveSyncEvents: PropTypes.func,
  _calender_id: PropTypes.string,
  activeTerm: PropTypes.string,
  eventMinDate: PropTypes.string,
  eventMaxDate: PropTypes.string,
};

const mapStateToProps = ({ calender }) => ({
  events: calender.academic_year_events,
  active: calender.active_year,
  programId: calender.programId,
  _calender_id: calender.program_calender_id,
  // academicStartDate: calender.academic_year_start,
  // academicEndDate: calender.academic_year_end,
  year1: calender.year1,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
  activeTerm: calender.active_term,
  eventMinDate: calender.eventMinDate,
  eventMaxDate: calender.eventMaxDate,
});

export default connect(mapStateToProps, {
  toggleModal,
  saveLevelEvents,
  saveSyncEvents,
})(EventsModal);
