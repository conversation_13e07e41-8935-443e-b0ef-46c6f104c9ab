import React, { useState } from 'react';
import Buttons from 'Widgets/FormElements/material/Button';
import { fromJS, List, Map } from 'immutable';
import { Delete } from '@mui/icons-material';
import image from 'Assets/q360_dashboard/doc_view/image.svg';
import pdf from 'Assets/q360_dashboard/doc_view/pdf.svg';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { FileViewAtDrawer } from 'Modules/GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step2';
import video from 'Assets/q360_dashboard/doc_view/video.svg';
import ShowCapturedDocs from './ShowCapturedDocs';
import { Divider, Drawer } from '@mui/material';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CloseIcon from '@mui/icons-material/Close';
import search_by_folder from 'Assets/q360_dashboard/category_overview/search_by_folder.svg';
import guide_resource_button from 'Assets/q360_dashboard/guide_resource_button.svg';
import { selectFormAddendum } from '_reduxapi/q360/selectors';
import { useSelector } from 'react-redux';
import { DrawerRight } from 'Modules/GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step2';

function GlobalViewDocuments({
  edit,
  show_display_creation,
  documents,
  setDocuments,
  isUserPerformedActionWithDocuments,
}) {
  const formAddendum = useSelector(selectFormAddendum);
  const captureDocs = formAddendum.get('displayCapture', List());
  const [internalDocs, setInternalDocs] = useState(Map());
  const sectionAttachments = documents.get('sectionAttachments', List());
  const uploadedDocs = fromJS([
    {
      sectionName: 'Common Evidence',
      evidenceAttachment: documents.get('formEvidenceAttachment', List()),
    },
  ]).concat(sectionAttachments);

  // const handleDeleteEvidence = (docIndex) => {
  //   setDocuments((prev) =>
  //     prev.update('formEvidenceAttachment', List(), (evidence) => {
  //       return evidence.filter((_, i) => i !== docIndex);
  //     })
  //   );
  // };

  // const dispatch = useDispatch();

  const handleDrawerOpen = (openStatus) => {
    if (!openStatus) {
      return setInternalDocs(uploadedDocs);
    }
    const updatedDocuments = documents
      .set('formEvidenceAttachment', internalDocs.getIn([0, 'evidenceAttachment'], List()))
      .update('sectionAttachments', (sectionAttachments) =>
        sectionAttachments.map((section, secIndex) =>
          section.set(
            'evidenceAttachment',
            internalDocs.getIn([secIndex + 1, 'evidenceAttachment'], List())
          )
        )
      );
    setDocuments(updatedDocuments);
    // if (
    //   documents.get('formEvidenceAttachment', List()).size !==
    //   formAddendum.get('formEvidenceAttachment', List()).size
    // ) {
    //   // true implies user deleted some documents we should set into Redux
    //   dispatch(
    //     setData({
    //       formAddendum: formAddendum.set(
    //         'formEvidenceAttachment',
    //         documents.get('formEvidenceAttachment', List())
    //       ),
    //     })
    //   );
    // }
  };
  const handleDelete = (sectionIndex, fileIndex) => (e) => {
    e.stopPropagation();
    // index 0 means implies the common document else implies on section documents
    setInternalDocs((prev) =>
      prev.updateIn([sectionIndex, 'evidenceAttachment'], (evidenceAttachment) =>
        evidenceAttachment.filter((doc, docIndex) => docIndex !== fileIndex)
      )
    );
  };
  const sectionWiseEvidenceCount = formAddendum
    .get('sectionAttachments', List())
    .reduce((acc, cur) => cur.get('evidenceAttachment', List()).size + acc, 0);
  const isShowEvidenceButton =
    sectionWiseEvidenceCount > 0 || documents.get('formEvidenceAttachment', List()).size > 0;
  const isShowDisplayButton =
    show_display_creation &&
    captureDocs.reduce((acc, cur) => {
      return acc + cur.get('attachments', List()).size;
    }, 0) > 0;
  const isUniDirectionButtonDesign = isShowEvidenceButton && isShowDisplayButton ? false : true; //XOR condition applied
  const attachment = isUserPerformedActionWithDocuments
    ? formAddendum.getIn(['categoryFormId', 'attachments'], List())
    : formAddendum.get('attachments', List());
  return (
    <section id="resource_category_overview">
      {attachment.size > 0 && (
        <div className={`${!isUniDirectionButtonDesign && 'pr-32'} text-right remove_hover`}>
          <DrawerRight showDelete={false} filesData={attachment}>
            <img src={guide_resource_button} alt="guide_resource" />
          </DrawerRight>
        </div>
      )}

      <div className="mt-2 d-flex justify-content-end">
        {isShowEvidenceButton && (
          <ViewEvidenceDocuments
            handleDrawerOpen={handleDrawerOpen}
            isUniDirectionButtonDesign={isUniDirectionButtonDesign}
            internalDocs={internalDocs}
            edit={edit}
            handleDelete={handleDelete}
          />
        )}
        {isShowDisplayButton && (
          <ShowCapturedDocs
            sx={{
              ...(isUniDirectionButtonDesign && {
                // for occupy full width
                width: '160px',
              }),
            }}
            captureDocs={captureDocs}
            isUniDirectionButtonDesign={isUniDirectionButtonDesign}
            childrenType="button"
          />
        )}
      </div>
    </section>
  );
}
export default GlobalViewDocuments;

function ViewEvidenceDocuments({
  isUniDirectionButtonDesign,
  handleDelete,
  internalDocs,
  handleDrawerOpen,
  edit,
}) {
  const [open, setOpen] = useState(false);
  const handleOpenOrClose = () => {
    setOpen((prev) => !prev);
    handleDrawerOpen(open);
  };

  const [openModalIndex, setOpenModalIndex] = useState(null);
  const [expandViewAttachment, setExpandViewAttachment] = useState(List());
  return (
    <>
      <Buttons
        clicked={handleOpenOrClose}
        color="blue"
        sx={{
          ...(isUniDirectionButtonDesign && {
            // for occupy full width
            width: '160px',
          }),
        }}
        startIcon={<img src={search_by_folder} alt="search_by_folder" />}
      >
        Evidence
      </Buttons>
      <Drawer
        anchor="right"
        open={open}
        className="rightDrawer"
        sx={{ '& .MuiDrawer-paper': { overflow: 'visible', width: '78vw' } }}
      >
        <div className="close-btn" onClick={handleOpenOrClose}>
          <CloseIcon />
          <div className="close-btn-bottom"></div>
        </div>
        <div className="close-btn-bottom"></div>
        <div className="my-2 mx-s3">
          <div className="f-24 pl-3">{'Evidence Documents'}</div>
          {/* <div className="f-10 text-grey">NCAA Course</div> */}
        </div>
        <Divider />
        {/* //for template wise show display capture */}

        {/* show display capture based on section wise */}
        {internalDocs.map((section, sectionIndex) => {
          if (!section.get('evidenceAttachment', List()).size) return null;
          return (
            <Accordion key={sectionIndex} className="mb-3" disableGutters>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel1-content"
                id="panel1-header"
              >
                {section.get('sectionName', '')}
              </AccordionSummary>
              <AccordionDetails>
                <div className="mt-1 my-2 mx-3 gap_doc_view d-flex flex-wrap">
                  {section.get('evidenceAttachment', List()).map((file, fileIndex) => {
                    const fileExtension = file.get('name', '').split('.').pop();
                    return (
                      <div
                        key={fileIndex + section.get('sectionName', '') + sectionIndex}
                        className="doc_view_div1 remove_hover"
                        onClick={() => {
                          setExpandViewAttachment(section.get('evidenceAttachment', List()));
                          setOpenModalIndex(fileIndex);
                        }}
                      >
                        <div className="d-flex doc_view_div2">
                          {fileExtension === 'mp4' && <img src={video} alt="img" />}
                          {fileExtension === 'pdf' && <img src={pdf} alt="img" />}
                          {fileExtension !== 'pdf' && fileExtension !== 'mp4' && (
                            <img alt="img" src={image} />
                          )}
                        </div>
                        <div className="p-2 d-flex align-items-center border_top_grey_view">
                          <div className="doc_view_div3">
                            {getShortString(
                              file.has('displayName')
                                ? file.get('displayName', '')
                                : file.get('name', ''),
                              23
                            )}
                          </div>
                          {edit && (
                            <div className="ml-auto remove_hover">
                              <Delete
                                sx={{ color: 'red', fontSize: '14px' }}
                                onClick={handleDelete(sectionIndex, fileIndex)}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </AccordionDetails>
            </Accordion>
          );
        })}
      </Drawer>
      <FileViewAtDrawer
        drawerName={'Evidence Documents'}
        file={expandViewAttachment.get(openModalIndex, Map())}
        open={openModalIndex !== null}
        // formName={formName}
        index={openModalIndex}
        filesData={expandViewAttachment}
        setOpenModalIndex={setOpenModalIndex}
        handleClose={() => setOpenModalIndex(null)}
      />
    </>
  );
}
