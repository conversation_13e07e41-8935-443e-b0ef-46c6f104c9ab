import React, { useContext, useState, useRef } from 'react';
import { List } from 'immutable';
import { useHistory, useRouteMatch } from 'react-router-dom';

import CourseTableHeader from './TableHeader';
import CourseTableRow from './TableRow';
import { ViewCourseTabs, ParentComponent, Header } from './Tabs';
import { independentCourseContext, courseMasterContext } from '../../../context';

import { eString } from 'utils';
import { checkCourseValidation } from '../../../ICutil';
function AddCourseTable() {
  const saveDetails = useRef();
  const closeRef = useRef();
  const history = useHistory();
  const match = useRouteMatch();
  const { addIndependentCourse, getCourseList, coursesList, setData } = useContext(
    courseMasterContext
  );
  const { institutionId, getToolTipData } = useContext(independentCourseContext);
  const [tabValue, setTabValue] = useState('activeTab');

  const [searchValue, setSearchValue] = useState('');

  const onClickSave = (type, editDetails = {}) => {
    let constructedData = saveDetails.current();
    constructedData = {
      _institution_id: institutionId,
      ...constructedData,
    };
    if (checkCourseValidation(constructedData, setData, getToolTipData)) {
      const callBack = (type, courseID) => {
        const goToConfig = (courseID) =>
          history.push(`${match.url}/configuration?cid=${eString(courseID)}`);
        editDetails.isEdit ? editDetails.closeEditModal(false) : closeRef.current();
        type === 'continue' ? goToConfig(courseID) : getCourseList({});
      };
      addIndependentCourse(constructedData, callBack, type, editDetails);
    }
  };

  const filteredData = (selectedTab) =>
    coursesList
      .get('courses', List())
      .filter((item) =>
        selectedTab === 'activeTab' ? item.get('isActive', true) : !item.get('isActive', true)
      )
      .filter(
        (item) =>
          item.get('courseCode', '').toLowerCase().includes(searchValue.toLowerCase()) ||
          item.get('courseName', '').toLowerCase().includes(searchValue.toLowerCase())
      );
  const addedData = filteredData('activeTab');
  const draftData = filteredData('archivedTab');
  const FilteredLength = [addedData.size, draftData.size];
  return (
    <ParentComponent>
      <Header
        onSave={{ onClickSave, saveDetails }}
        searchData={{ searchValue, setSearchValue }}
        closeRef={closeRef}
      />
      <ViewCourseTabs tabValue={tabValue} setTabValue={setTabValue} tabLength={FilteredLength} />

      <div className="program_table">
        <table align="left">
          <CourseTableHeader />

          <tbody>
            {(tabValue === 'activeTab' ? addedData : draftData).map((course, index) => (
              <CourseTableRow
                key={index}
                index={index}
                course={course}
                onSave={{ onClickSave, saveDetails }}
              />
            ))}
          </tbody>
        </table>
      </div>
    </ParentComponent>
  );
}

export default AddCourseTable;
