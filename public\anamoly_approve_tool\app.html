<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Anomaly Information</title>
    <!-- Include Bootstrap CSS -->
    <link
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      .dailylist {
        width: 84%;
        margin: 0 auto;
        background: white;
        padding: 16px;
      }

      .dailylist p {
        margin: 2px;
        margin-left: 5px;
      }

      #todayAnamoly {
        margin-left: 20px;
      }

      .facedlink {
        cursor: pointer;
        margin: 3px;
      }

      .clicked {
        color: #007bff;
      }

      .title {
        text-align: center;
        /* Aligns the title to the center */
        /* Adds some space below the title */
        color: #1877f2;
        display: inline-block;
        width: 60%;
      }

      #results {
        margin-top: 20px;
      }

      .schedule-item {
        margin: 15px;
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
      }

      /* General styling for the body */
      body {
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
        font-family: 'Mulish', sans-serif;
      }

      .search-container {
        text-align: right;
        /* Aligns the search box and button to the right */
        margin-bottom: 20px;
        /* Adds some space below the container */
        margin-top: 20px;
      }

      .search-container h1 {
        display: inline-block;
      }

      #results {
        margin-top: 25px;
        /* If you are planning to display results below the search */
      }

      /* Styles for the container holding the results */
      #results {
        margin: 20px;
        padding: 15px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }

      /* Styles for the student information section */
      .student-info {
        margin-bottom: 20px;
      }

      .student-info h3 {
        color: #333;
        margin-bottom: 10px;
      }

      .student-info p {
        font-size: 16px;
        color: #555;
        margin-bottom: 5px;
      }

      /* Styles for the schedules section */
      .schedules-section {
        border-top: 1px solid #eee;
        padding-top: 20px;
      }

      .schedules-section h3 {
        color: #333;
        margin-bottom: 15px;
      }

      /* Each individual schedule item */
      .schedule-item {
        background-color: #f9f9f9;
        padding: 10px;
        border: 1px solid #e1e1e1;
        border-radius: 5px;
        margin-bottom: 20px;
      }

      .schedule-item h6 {
        color: #007bff;
        margin-bottom: 10px;
      }

      .schedule-item p {
        font-size: 15px;
        margin-bottom: 5px;
        color: #666;
      }

      /* Staff and face data sections within each schedule */
      .staffs,
      .face-data {
        margin-top: 15px;
        margin-bottom: 5px;
      }

      .staffs h5,
      .face-data h5 {
        margin-bottom: 10px;
        font-weight: bold;
        color: #555;
      }

      .staffs p,
      .face-data span {
        font-size: 14px;
        color: #777;
      }

      .face-data span {
        display: inline-block;
        margin-right: 10px;
        color: #007bff;
        font-weight: bold;
      }

      /* Alert messages */
      .alert {
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
      }

      .alert-danger {
        color: #a94442;
        background-color: #f2dede;
        border-color: #ebccd1;
      }

      .purify {
        float: right;
      }

      .top-nav {
        padding: 10px 250px;
      }

      #popup {
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        padding: 20px;
        background: #fff;
        border: 1px solid #333;
        z-index: 1000;
      }

      #close-popup {
        margin-top: 20px;
        background: #f44;
        color: #fff;
        border: none;
        padding: 10px 20px;
        cursor: pointer;
      }

      #close-popup:hover {
        background: #d33;
      }
      .daily_subtitle {
        text-decoration: underline;
        color: #1877f2;
      }
    </style>
  </head>

  <body>
    <nav class="sticky-top top-nav nav nav-justified navbar navbar-expand-lg navbar-light bg-light">
      <a class="navbar-brand" href="#">Anomaly Details</a>
      <button
        class="navbar-toggler"
        type="button"
        data-toggle="collapse"
        data-target="#navbarTogglerDemo02"
        aria-controls="navbarTogglerDemo02"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbarTogglerDemo02">
        <form class="form-inline my-2 my-lg-0">
          <input
            class="form-control mr-sm-2"
            id="studentId"
            type="search"
            placeholder="Search"
            onkeypress="handleKeyPress(event)"
          />
          <button id="searchButton" class="btn btn-outline-success my-2 my-sm-0" type="button">
            Search
          </button>
          <button id="todayAnamoly" class="btn btn-outline-success my-2 my-sm-0" type="button">
            Today Anomaly
          </button>
        </form>
      </div>
    </nav>

    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <div id="results">Search by Student id</div>
        </div>
      </div>
    </div>

    <!-- Include jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <!-- Include Bootstrap JS -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
      //example call
      //http://localhost/face_retrain/anamoly_approve_tool/?baseUrl=https://ds.api.digivalsolutions.com/api/v1/digiclass&authToken=token&institutionId=5e5d0f1a15b4d600173d5692
      // Configurable variables
      //staging
      //var baseUrl = "https://ecs-dsapi-staging.digivalitsolutions.com/api/v1/digiclass";
      // var baseUrl = "https://ds.api.digivalsolutions.com/api/v1/digiclass";
      //     var institutionId = "5e5d0f1a15b4d600173d5692";
      var queryParams = new URLSearchParams(window.location.search);
      // Retrieving specific parameters
      var baseUrl = queryParams.get('baseUrl') + '/digiclass';
      var authToken = 'Bearer ' + queryParams.get('authToken');
      var institutionId = queryParams.get('institutionId');

      // Check if any of the parameters are not provided in the URL
      if (!baseUrl || !authToken || !institutionId) {
        alert(
          'Missing required parameters. Please ensure the URL has baseUrl, authToken, and institutionId.'
        );
        // return false; // or handle this situation accordingly
      }

      var apiBaseUrl = baseUrl + '/schedule-attendance/anomalyStudentScheduleList';

      function getCurrentDate() {
        return new Date().toISOString().split('T')[0];
      }

      // Function to fetch data and handle it
      function fetchDataAndDisplayPopup() {
        $.ajax({
          url:
            baseUrl +
            '/schedule-attendance/anomalyStudentScheduleDayWise?filterDate=' +
            getCurrentDate(), // Replace with your actual URL
          type: 'GET',
          headers: {
            Authorization: authToken,
            _institution_id: institutionId,
          },
          success: function (response) {
            if (response.status_code === 200) {
              // First, we sort the 'data' array by the 'createdAt' field of each schedule
              response.data.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
              // Start building the HTML content block for display
              let contentHtml = '<div class="dailylist">';
              // Iterate through each sorted schedule
              response.data.forEach(function (schedule) {
                // For each schedule, we also want to format the 'createdAt' date to be more human-readable
                let formattedDate = new Date(schedule.createdAt).toLocaleString('en-US', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true,
                });
                // Add a section for the schedule with the formatted creation date
                contentHtml += `<div class="schedule-block"><h6 class="daily_subtitle">Created At: ${formattedDate}</h6  >`;
                // Now, we process each student within this schedule
                schedule.studentDatas.forEach(function (student) {
                  // We construct and add the student's details to the current schedule block in the content HTML
                  contentHtml += `<p>User ID: ${student.user_id} </p>`; // Add other student details here if needed
                });
                // Close the schedule block div
                contentHtml += '</div>';
              });
              // Close the dailylist container div
              contentHtml += '</div>';
              // Insert the constructed HTML content into a specific part of your page. This could be a popup or a dedicated area for schedules.
              // Here we're appending it to the body, but you might want to append to a more specific container within your DOM.
              $('body').append(contentHtml);
              // If you're using a popup to display this content, you would trigger its display now.
              // Make sure the popup content area is correctly targeted and exists in your DOM.
              $('#popup').show();
            } else {
              console.error('Did not receive a successful response:', response);
            }
          },
          error: function (xhr, status, error) {
            console.error('An error occurred:', error);
          },
        });
      }

      // Function to close the popup
      $('#close-popup').click(function () {
        $('#popup').hide();
      });

      // Call the function when needed (e.g., on page load, button click, etc.)

      function formatTimestamp(timestamp) {
        let date = new Date(timestamp);

        // Specify options for formatting. Note: 'hour12' is not typically used in Saudi Arabia, as the region uses a 24-hour format.
        let options = {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          timeZone: 'Asia/Riyadh',
        };

        // Use the toLocaleString method with 'en-SA' locale for Saudi Arabia and specific options
        return date.toLocaleString('en-SA', options);
      }

      // Function to execute the PUT request
      function updateStudentPurity(studentId) {
        var apiUrl = baseUrl + '/schedule-attendance/studentFaceAnomalyPure';
        // Prepare the data you want to send
        var requestData = {
          studentId: studentId, // e.g., "62babe4b58e9580f791fc6a4"
        };

        // Execute the AJAX request
        $.ajax({
          type: 'PUT',
          url: apiUrl,
          data: JSON.stringify(requestData), // Convert your data to a JSON string
          contentType: 'application/json', // Inform the server you're sending JSON data
          dataType: 'json', // Inform jQuery you're expecting JSON in response
          headers: {
            Authorization: authToken,
          },
          success: function (response) {
            // This code is executed if the server responds successfully (status 200)
            alert('Student purified!');
          },
          error: function (jqXHR, textStatus, errorThrown) {
            // This code is executed if the server responds with an error
            console.error('Error: ' + textStatus, errorThrown);
          },
        });
      }

      // Call the function with the appropriate studentId

      function purifyStudent() {
        $('.purify').on('click', function () {
          let studId = $(this).attr('id');
          updateStudentPurity(studId);
        });
      }

      // Function to display data in a popup
      function displayDataInPopup(data) {
        // Assuming data.faceData is your base64 image data.
        var base64ImageData = data.faceData;
        $('.oldmodel').remove();
        // If the base64 data is not prefixed with the MIME type, prepend it
        if (!base64ImageData.startsWith('data:image')) {
          base64ImageData = 'data:image/png;base64,' + base64ImageData;
        }

        var faceDataHtml = '<div>Face ID: ' + data._id + '</div>';
        faceDataHtml +=
          '<img src="' +
          base64ImageData +
          '" alt="Face Image" style="width: 100%; height: auto;"/>';

        // Creating a modal dynamically
        var modal =
          '<div class="modal fade oldmodel" id="dataModal" tabindex="-1" role="dialog" aria-labelledby="dataModalLabel" aria-hidden="true">' +
          '<div class="modal-dialog" role="document">' +
          '<div class="modal-content">' +
          '<div class="modal-header">' +
          '<h5 class="modal-title" id="dataModalLabel">Face Data</h5>' +
          '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
          '<span aria-hidden="true">&times;</span>' +
          '</button>' +
          '</div>' +
          '<div class="modal-body">' +
          faceDataHtml +
          '</div>' +
          '<div class="modal-footer">' +
          '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>' +
          '</div>' +
          '</div>' +
          '</div>' +
          '</div>';

        // Append modal to body
        $('body').append(modal);

        // Show modal
        $('#dataModal').modal('show');
      }

      function getFaceData(faceId, pass) {
        $.ajax({
          url: baseUrl + '/schedule-attendance/anomalyStudentFace',
          type: 'GET',
          data: {
            faceId: faceId,
            anomalyPassKey: pass,
          },
          headers: {
            Authorization: authToken,
          },

          success: function (response) {
            if (response.status_code === 200) {
              displayDataInPopup(response.data);
            } else {
              // Handle the error case
              alert('not allowed to see image');
              console.error('Data retrieval was not successful.');
            }
          },
          error: function (xhr, status, error) {
            alert('not allowed to see image');
            // Handle error
            console.error('An error occurred: ' + error);
          },
        });
      }

      function getfaceData() {
        $('.facedlink').on('click', function () {
          let selectedFaceId = $(this).attr('id');
          let photopass = prompt('Please enter Secret Key');
          if (photopass != null && photopass != '') {
            getFaceData(selectedFaceId, photopass);
            $(this).addClass('clicked');
          }
        });
      }

      $(document).ready(function () {
        $('#todayAnamoly').on('click', function () {
          //alert("works")
          fetchDataAndDisplayPopup();
        });

        $('#searchButton').click(function () {
          var studentId = $('#studentId').val().trim();

          if (studentId === '') {
            alert('Please enter a student ID.');
            return;
          }

          // Complete the URL with the parameters it requires
          var requestUrl = apiBaseUrl + '?studentId=' + studentId; // You can add more parameters here if needed

          $.ajax({
            url: requestUrl,
            type: 'GET',
            headers: {
              Authorization: authToken,
              // If the institution ID is used in the headers, add it here as well
              _institution_id: institutionId,
            },
            success: function (response) {
              $('#results').empty();

              if (response.status_code === 200 && response.data) {
                // Display Student Information
                var studentInfo = $('<div class="student-info"></div>');
                // studentInfo.append('<h3>Student Information</h3>');
                studentInfo.append(
                  '<button  id="' +
                    response.data.studentId +
                    '" class="btn btn-primary   purify">Make purify</button>'
                );
                studentInfo.append('<p>ID: ' + studentId + '</p>');
                studentInfo.append('<p>Gender: ' + response.data.studentGender + '</p>');
                studentInfo.append(
                  '<p>Student Name: ' +
                    response.data.studentName.first +
                    ' ' +
                    response.data.studentName.middle +
                    ' ' +
                    response.data.studentName.last +
                    '</p>'
                );

                $('#results').append(studentInfo);

                if (response.data.studentSchedule.length > 0) {
                  response.data.studentSchedule = response.data.studentSchedule.reverse();
                  // Create a section for schedules
                  var schedulesSection = $('<div class="schedules-section row"></div>');

                  // Go through each schedule
                  response.data.studentSchedule.forEach(function (schedule) {
                    var scheduleElement = $('<div class="schedule-item col-3"></div>');

                    // Add schedule details
                    if (!schedule.title)
                      scheduleElement.append(
                        '<h6>Schedule: ' +
                          schedule.session.delivery_symbol +
                          ' ' +
                          schedule.session.delivery_no +
                          '</h6>'
                      );
                    else scheduleElement.append('<h6>Schedule: ' + schedule.title + '</h6>');

                    //  scheduleElement.append('<h6>Schedule: ' + schedule.title ?schedule.title :schedule.session.delivery_symbol+" "+schedule.session.delivery_no + '</h6>');
                    // scheduleElement.append('<p>Status: ' + schedule.status + '</p>');
                    scheduleElement.append('<p>Program: ' + schedule.program_name + '</p>');
                    scheduleElement.append(
                      '<p>Course: ' + schedule.course_name + ' (' + schedule.course_code + ')</p>'
                    );
                    scheduleElement.append(
                      '<p>Year: ' + schedule.year_no + ', Level: ' + schedule.level_no + '</p>'
                    );
                    scheduleElement.append(
                      '<p>Type: ' + schedule.type + ', Term: ' + schedule.term + '</p>'
                    );
                    scheduleElement.append(
                      '<p>Start: ' + formatTimestamp(schedule.scheduleStartDateAndTime) + '</p>'
                    );
                    scheduleElement.append(
                      '<p>End: ' + formatTimestamp(schedule.scheduleEndDateAndTime) + '</p>'
                    );
                    scheduleElement.append('<p>Pure: ' + (schedule.pure ? 'Yes' : 'No') + '</p>');

                    // Staff information
                    if (schedule.staffs && schedule.staffs.length > 0) {
                      var staffsElement = $('<div class="staffs"><h5>Staffs:</h5></div>');
                      schedule.staffs.forEach(function (staff) {
                        staffsElement.append(
                          '<p>Name: ' +
                            staff.staff_name.first +
                            ' ' +
                            (staff.staff_name.middle || '') +
                            ' ' +
                            staff.staff_name.last +
                            '</p>'
                        );
                      });
                      scheduleElement.append(staffsElement);
                    }

                    // Face Data information
                    if (schedule.faceData && schedule.faceData.length > 0) {
                      var faceDataElement = $(
                        '<div class="face-data"><h5>Face Data IDs:</h5></div>'
                      );
                      schedule.faceData.forEach(function (faceDataId, index) {
                        faceDataElement.append(
                          ' <button id="' +
                            faceDataId +
                            '" class="facedlink btn btn-warning" type="button" class="btn btn-outline-danger"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-earmark-image-fill" viewBox="0 0 16 16"><path d="M4 0h5.293A1 1 0 0 1 10 .293L13.707 4a1 1 0 0 1 .293.707v5.586l-2.73-2.73a1 1 0 0 0-1.52.127l-1.889 2.644-1.769-1.062a1 1 0 0 0-1.222.15L2 12.292V2a2 2 0 0 1 2-2zm5.5 1.5v2a1 1 0 0 0 1 1h2l-3-3zm-1.498 4a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0z"></path> <path d="M10.564 8.27 14 11.708V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-.293l3.578-3.577 2.56 1.536 2.426-3.395z"></path></svg> Anomaly - ' +
                            parseInt(index + 1) +
                            ' </button>'
                        );
                      });
                      scheduleElement.append(faceDataElement);
                    }

                    schedulesSection.append(scheduleElement);
                  });

                  $('#results').append(schedulesSection);
                } else {
                  $('#results').append('<p>No schedules available for this student.</p>');
                }
                getfaceData();
                purifyStudent();
              } else {
                $('#results').html(
                  '<div class="alert alert-danger">Error: Could not retrieve the student information.</div>'
                );
              }
            },
            error: function (error) {
              // Handle errors here
              console.error('An error occurred:', error);
              $('#results').html(
                '<div class="alert alert-danger">An error occurred while fetching the data.</div>'
              );
            },
          });
        });
      });

      function handleKeyPress(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          $('#searchButton').trigger('click');
        }
      }
    </script>
  </body>
</html>
