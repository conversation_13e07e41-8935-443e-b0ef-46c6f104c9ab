import React from 'react';
import PropTypes from 'prop-types';
import NavigationItem from 'Widgets/NavigationItem/NavigationItem';
import i18n from '../../../i18n';
import { Trans } from 'react-i18next';
function IndependentCourseNav({ toggleClick, active, to }) {
  return (
    <>
      <NavigationItem
        to={to}
        menu={i18n.t('independentCourse.independentCourseLabel')}
        //src={require('Assets/overview.svg')}
        clicked={toggleClick}
        active={active}
        arrow={true}
      />
      <div className={active ? 'd-block' : 'd-none'}>
        <span
          className={`d-flex justify-content-between subject_hover f-14 pl-5 bold ${
            active ? 'program_active_v2' : ''
          }`}
        >
          <Trans i18nKey={'global_configuration.independentCourse'}></Trans>
        </span>
      </div>
    </>
  );
}

IndependentCourseNav.propTypes = {
  to: PropTypes.string,
  active: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  toggleClick: PropTypes.func,
};
export default IndependentCourseNav;
