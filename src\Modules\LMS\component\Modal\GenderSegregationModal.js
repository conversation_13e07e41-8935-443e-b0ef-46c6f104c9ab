import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Stack } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import ErrorIcon from '@mui/icons-material/Error';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { DialogActions } from '@mui/material';
import Divider from '@mui/material/Divider';
import { ClassificationsContext } from '../Classifications';
import DeleteIcon from '../../../../Assets/deleteicon.svg';
import { useStylesFunction } from '../../designUtils';

const GenderSegregationModal = ({ open, setGenderSegModalClose, item }) => {
  const { type, updateGenderSegregation, getLmsSettings } = useContext(ClassificationsContext);
  const handleProceed = (e, id) => {
    const requestData = {
      classificationType: type,
      genderSegregation: !item.get('genderSegregation', false),
      levelApproverId: id,
    };
    updateGenderSegregation(requestData, () => {
      getLmsSettings(type);
      setGenderSegModalClose();
    });
  };
  const classes = useStylesFunction();
  return (
    <div>
      <Dialog
        className="p-2"
        maxWidth="md"
        open={open}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <div className="m-2">
          <div className="d-flex flex-column justify-content-center mt-2">
            <div className="text-center">
              {item.get('genderSegregation', false) ? (
                <img src={DeleteIcon} width="5%" alt="Deactivated" />
              ) : (
                <ErrorIcon style={{ color: '#4194E4', fontSize: '40px' }} />
              )}
            </div>
            <div>
              <DialogTitle className="p-1 ml-3" id="alert-dialog-title">
                {item.get('genderSegregation', false)
                  ? 'You Are About To Turn Off Gender Segregation'
                  : 'You Are About To Create Gender Segregation'}
              </DialogTitle>
            </div>
            <div>
              <DialogContent className={classes.genderSegregationPopUpPadding}>
                <DialogContentText id="alert-dialog-description">
                  {item.get('genderSegregation', false) ? (
                    <span className="d-flex justify-content-center mb-1">
                      <span className="text-center mb-0">
                        All previous settings will be{' '}
                        <span style={{ color: '#E44141' }}>Reset and Deleted</span>
                        <br />{' '}
                        <span className="text-center mb-0">
                          Which Applied To Both Male and Female
                        </span>
                      </span>
                    </span>
                  ) : (
                    <span className="d-flex justify-content-center mb-1">
                      <span className=" text-center mb-0">
                        {' '}
                        All previous settings will be applied to both
                        <br />
                        <span className="text-center mb-0">male and female </span>
                      </span>
                    </span>
                  )}
                </DialogContentText>
              </DialogContent>
            </div>
          </div>
          <Divider />
          <div className="d-flex justify-content-end">
            <DialogActions>
              <Stack spacing={2} direction="row">
                <MButton
                  variant="text"
                  color={'blue'}
                  // clicked={handleClose}
                  clicked={setGenderSegModalClose}
                  className="text-dark"
                >
                  CANCEL
                </MButton>
                {item.get('genderSegregation', false) ? (
                  <MButton
                    // size={'large'}
                    variant="contained"
                    color={'red'}
                    clicked={(e) => {
                      handleProceed(e, item.get('_id', ''));
                    }}
                  >
                    DELETE
                  </MButton>
                ) : (
                  <MButton
                    // size={'large'}
                    variant="contained"
                    color={'blue'}
                    clicked={(e) => {
                      handleProceed(e, item.get('_id', ''));
                    }}
                  >
                    PROCEED
                  </MButton>
                )}
              </Stack>
            </DialogActions>
          </div>
        </div>
      </Dialog>
    </div>
  );
};
GenderSegregationModal.propTypes = {
  open: PropTypes.bool,
  setGenderSegModalClose: PropTypes.func,
  item: PropTypes.object,
};
export default GenderSegregationModal;
