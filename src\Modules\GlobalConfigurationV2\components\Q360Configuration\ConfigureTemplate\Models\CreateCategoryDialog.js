import React from 'react';
import Button from '@mui/material/Button';
import DialogModal from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import { updateCategory } from '_reduxapi/q360/actions';
import {
  useCallApiHook,
  useInputHook,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { setData } from '_reduxapi/q360/actions';
import { fromJS } from 'immutable';
/*----------------------------------Utils Start---------------------------------------------------*/
const placeholderStyles = {
  '& input::placeholder': {
    fontSize: '14px',
  },
};
const dialogSX = {
  boxShadow: '0',
  borderRadius: '8px',
  width: '480px',
};
const errorMessageBox = {
  invalid: 'Enter the Category Name',
  duplicate: 'Category name already exists',
};
/*----------------------------------Utils End-----------------------------------------------------*/

const CreateCategoryDialog = ({ open, handleClose }) => {
  const [categoryName, handleChange] = useInputHook('');
  const handleAllActions = () => {
    handleClose();
    handleChange({ target: { value: '' } });
  };
  const callBack = (statusCode) => {
    if (statusCode === 409) return handleValidateInput('', '', 'duplicate');
    return handleAllActions();
  };
  const [updateApi] = useCallApiHook(updateCategory);
  const [updateRedux] = useCallApiHook(setData);
  const handleValidateInput = (input, cb, errorType) => {
    if (input.trim() !== '') return cb();
    return updateRedux(fromJS({ message: errorMessageBox[errorType] }));
  };
  const createNewCategory = () => updateApi({ categoryName: categoryName.trim() }, callBack);
  return (
    <DialogModal
      open={open}
      onClose={handleAllActions}
      maxWidth={'sm'}
      fullWidth={true}
      PaperProps={{ sx: dialogSX }}
    >
      <DialogTitle className="gray-neutral">Create New Category</DialogTitle>
      <DialogContent className="gray-neutral d-flex flex-column gap-10">
        <label className="f-14 mb-0">Category Name</label>
        <TextField
          fullWidth
          size="small"
          value={categoryName}
          onChange={handleChange}
          sx={placeholderStyles}
          placeholder="Enter category name"
        />
      </DialogContent>
      <DialogActions className="px-4 pb-4 gap-2 ">
        <Button
          variant="outlined"
          onClick={handleAllActions}
          className="text-capitalize px-4 text-secondary border-secondary bold"
        >
          Cancel
        </Button>
        <Button
          className="text-capitalize px-4"
          variant="contained"
          onClick={() => handleValidateInput(categoryName, createNewCategory, 'invalid')}
        >
          Create
        </Button>
      </DialogActions>
    </DialogModal>
  );
};
export default CreateCategoryDialog;
