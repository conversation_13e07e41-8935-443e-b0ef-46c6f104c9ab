import React, { forwardRef, useEffect, useState } from 'react';
import Table from '@mui/material/Table';
import TextField from '@mui/material/TextField';
import TableBody from '@mui/material/TableBody';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import Paper from '@mui/material/Paper';
import Rating from '@mui/material/Rating';
import PropTypes from 'prop-types';
import { Map as IMap, List, fromJS } from 'immutable';
import DeleteIcon from '@mui/icons-material/Delete';

const initialState = fromJS({
  startRange: '',
  endRange: '',
  label: '',
  starRange: {
    noOfRange: '',
    color: '',
  },
  imageUrl: 'emoji_1',
});

const colorSize = {
  1: '#DC2626',
  2: '#A5A6F6',
  3: '#F59E0B',
  4: '#53AEF9',
  5: '#16A34A',
};

const LevelWiseTable = forwardRef(({ anomalyBoard }, badgeRef) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [image, setImages] = useState(new Map());

  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const [state, setState] = useState(List());
  badgeRef.current = state;

  const handleInput = (index, key1, key2) => (e, newValue) => {
    const value = e.target.value;
    if (value !== '' && (Number(value) < 0 || isNaN(Number(value)))) return;
    if (key2) {
      setState((prev) =>
        prev.setIn([index, key1, key2], newValue).setIn([index, key1, 'color'], colorSize[newValue])
      );
    } else {
      setState((prev) => prev.setIn([index, key1], value));
    }
  };

  const handleDelete = (index) => {
    setState((prev) => prev.filter((_, i) => i !== index));
  };

  const onMenuClick = (index, key) => {
    setState((prev) => prev.setIn([index, 'imageUrl'], key));
    handleClose();
  };

  useEffect(() => {
    if (!state.size) {
      const onImageLoad = async () => {
        try {
          const emoji = new Map();
          for (let i = 1; i < 9; i++) {
            const res = await import(`Assets/user_activity_board/badges/emojis/emoji_${i}.svg`);
            emoji.set('emoji_' + i, res.default);
          }
          setImages(emoji);
        } catch (err) {
          console.log(err); //eslint-disable-line
        }
      };
      onImageLoad();
    }
    setState(anomalyBoard.get('anomalyLevel', List()));
  }, [anomalyBoard]); //eslint-disable-line
  return (
    <TableContainer
      component={Paper}
      sx={{
        background: 'transparent',
        boxShadow: 'none',
        borderRadius: '10px',
        border: '1px solid rgba(224, 224, 224, 1)',
      }}
    >
      <Table sx={{ minWidth: 650 }} aria-label="simple table">
        <TableHead>
          <TableRow>
            <TableCell className="pl-4">Percentage Range</TableCell>
            <TableCell>Anomaly Label</TableCell>
            <TableCell> Star Range</TableCell>
            <TableCell>Emoji</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {state.map((item, index) => (
            <TableRow
              className="table_row_anomaly"
              sx={{ '&:last-child th': { border: 0 } }}
              key={index}
            >
              <TableCell className="width_250px d-flex pl-4">
                <TextField
                  className="width_150 mr-1"
                  type="number"
                  size={'small'}
                  value={item.get('startRange', '')}
                  onChange={handleInput(index, 'startRange')}
                />
                <div className="mx-2 pt-2">-</div>
                <TextField
                  className="width_150 ml-1"
                  type="number"
                  size={'small'}
                  value={item.get('endRange', '')}
                  onChange={handleInput(index, 'endRange')}
                />
              </TableCell>

              <TableCell>
                {' '}
                <TextField
                  className="width_150 "
                  type="text"
                  size={'small'}
                  value={item.get('label', '')}
                  onChange={(e) => {
                    e.persist();
                    setState((prev) => prev.setIn([index, 'label'], e.target?.value));
                  }}
                />
              </TableCell>
              <TableCell>
                <Rating
                  name="simple-controlled"
                  sx={{
                    '& .MuiRating-iconFilled': {
                      color: colorSize[item.getIn(['starRange', 'noOfRange'], 0)] ?? '#F3F4F6',
                    },
                    '& .MuiRating-iconHover': {
                      color: '#3d5170',
                    },
                  }}
                  value={item.getIn(['starRange', 'noOfRange'], 0)}
                  onChange={handleInput(index, 'starRange', 'noOfRange')}
                />
              </TableCell>
              <TableCell>
                <img src={image.get(state.getIn([index, 'imageUrl'], ''))} alt="happy" />
                <ArrowDropDownIcon className="dropdown_center_mui" onClick={handleClick} />
                {image.size > 0 && (
                  <Menu
                    id="basic-menu"
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                    MenuListProps={{
                      'aria-labelledby': 'basic-button',
                    }}
                  >
                    {Array.from(image).map(([key, value]) => {
                      return (
                        <MenuItem
                          key={key}
                          onClick={() => onMenuClick(index, key)}
                          selected={key === state.getIn([index, 'imageUrl'], '')}
                        >
                          <img src={value} alt={key} />
                        </MenuItem>
                      );
                    })}
                  </Menu>
                )}
                <DeleteIcon
                  onClick={() => handleDelete(index)}
                  className="ml-3 dropdown_center_mui text-grey  hover_active_delete_icon"
                />
              </TableCell>
            </TableRow>
          ))}
          <TableRow>
            <TableCell
              colSpan={2}
              className="ml-3 text-grey"
              onClick={() => setState((prev) => prev.push(initialState))}
            >
              + Add Row
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
});

LevelWiseTable.propTypes = {
  anomalyBoard: PropTypes.instanceOf(IMap),
};
export default LevelWiseTable;
