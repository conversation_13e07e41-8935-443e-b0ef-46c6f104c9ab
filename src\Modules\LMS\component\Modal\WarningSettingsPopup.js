import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import { fromJS, Map, List } from 'immutable';

import * as actions from '_reduxapi/leave_management/actions';
import { connect } from 'react-redux';
import {
  selectRolesList,
  selectLmsSettings,
  selectUserList,
} from '_reduxapi/leave_management/selectors';
// utils
import { useStylesFunction } from '../../designUtils';
import MButton from 'Widgets/FormElements/material/Button';
import {
  turnAroundTime,
  warningValidation,
  BootstrapDialog,
  BootstrapDialogTitle,
  warningDetailValidation,
  warningName,
  iscomprehensiveMode,
  useIsWarningValue,
} from '../../utils';

// components
import WarningSettingsTable from '../WarningSettingsTable';
import WarningTypeDetails from '../WarningTypeDetails';

const details = {
  unappliedLeaveConsideredAs: '',
  denialCondition: 'Cumulative',
  isAdditionStaffNotify: false,
  acknowledgeToStudent: false,
  labelName: '',
  percentage: '',
  warningValue: '',
  warningMode: '',
  colorCode: '#ffffff',
  message: '',
  notificationToStaff: false,
  acknowledge: false,
  restrictCourseAccess: false,
  scheduleStaffRestrictedAttendance: false,
  approveRequiredForCriteriaManipulation: false,
  approverType: 'Role Based',
  specifyWhoDoesCriteria: [],
  notificationRoleIds: [],
  meetingWithStudent: false,
  meetingRoleIds: [],
  turnAroundTime: turnAroundTime[0].value,
  markItMandatory: false,
  adminRestrictedAttendance: false,
  notificationToParent: {
    isActive: false,
    setType: 'manual',
  },
  notificationToStudent: {
    isActive: false,
    setType: 'automatic',
  },
  categoryWisePercentage: [],
  isActive: false,
};

const typeDetails = {
  typeName: 0,
  ...details,
};

const denialTypeDetails = {
  typeName: 'Denial',
  ...details,
  denialManagement: {
    accessType: 'role',
    roleIds: [],
    userIds: [],
  },
  restrictCourseAccess: false,
};

const WarningSettings = ({
  roleList,
  getRolesList,
  updateWarning,
  lmsSettings,
  open,
  setOpen,
  setData,
  operation,
  getLmsSettings,
  disableIndex,
  getUser,
  userList,
  resetDenialCondition,
}) => {
  const { modalWidth, Active } = useStylesFunction();
  const [activeIndex, setActiveIndex] = useState(0);
  const [warningTypes, setWarningTypes] = useState(
    fromJS([
      {
        label: 'Define the Levels',
      },
      typeDetails,
      denialTypeDetails,
    ])
  );
  const [searchKey, setSearchKey] = useState('');
  const warningMode = lmsSettings.get('warningMode');
  const iscomprehensive = iscomprehensiveMode(warningMode);
  const isWarningValue = useIsWarningValue(iscomprehensive);
  const iscomprehensiveCondition = isWarningValue;
  const warningConfig = iscomprehensiveCondition
    ? lmsSettings.get('comprehensiveWarningConfig', List())
    : lmsSettings.get('warningConfig', List());
  useEffect(() => {
    const warningTypesAr = warningTypes.map((item) => {
      return item.set(
        'categoryWisePercentage',
        lmsSettings
          .get('catagories', List())
          .filter((s) => s.get('isActive', false))
          .map((item) => {
            return Map({
              categoryId: item.get('_id', ''),
              categoryName: item.get('categoryName', ''),
              percentage: '',
              percentageValue: item.get('percentage', ''),
            });
          })
      );
    });

    setWarningTypes(warningTypesAr);
  }, []); //eslint-disable-line

  useEffect(() => {
    if (operation === 'update' || operation === 'updateNew') {
      const warningConfigAr = warningConfig.map((item) => {
        const accessType = item.getIn(['denialManagement', 'accessType'], '');
        const mergeCategory = item
          .get('categoryWisePercentage', List())
          .map((item) => {
            return item
              .set(
                'isActive',
                lmsSettings
                  .get('catagories', List())
                  .find((data) => item.get('categoryId') === data.get('_id'))
                  ?.get('isActive', false)
              )
              .set(
                'percentageValue',
                lmsSettings
                  .get('catagories', List())
                  .find((data) => item.get('categoryId') === data.get('_id'))
                  ?.get('percentage', '')
              );
          })
          .merge(
            lmsSettings
              .get('catagories', List())
              .filter((s) => s.get('isActive', false))
              .map((item) => {
                return Map({
                  categoryId: item.get('_id', ''),
                  categoryName: item.get('categoryName', ''),
                  percentage: '',
                  percentageValue: item.get('percentage', ''),
                  isActive: item.get('isActive', ''),
                });
              })
          );

        const filteredItem = mergeCategory
          .filter((item, index) => {
            return (
              mergeCategory.findIndex(
                (fItem) => fItem.get('categoryId') === item.get('categoryId')
              ) === index
            );
          })
          .filter((s) => s.get('isActive', false));
        return item
          .set(
            'notificationRoleIds',
            item.get('notificationRoleIds', List()).map((dItem) => {
              return Map({
                title: dItem.get('name', ''),
                value: dItem.get('_id', ''),
              });
            })
          )
          .setIn(
            ['notificationToStudent', 'sendNotificationAuthority'],
            item
              .getIn(['notificationToStudent', 'sendNotificationAuthority'], List())
              .map((dItem) => {
                return Map({
                  title: dItem.get('name', ''),
                  value: dItem.get('_id', ''),
                });
              })
          )
          .setIn(
            ['denialManagement', accessType === 'role' ? 'roleIds' : 'userIds'],
            item
              .getIn(['denialManagement', accessType === 'role' ? 'roleIds' : 'userIds'], List())
              .map((dItem) => {
                return Map({
                  title:
                    accessType === 'role'
                      ? dItem.get('name', '')
                      : dItem.getIn(['name', 'first'], '') +
                        ' ' +
                        dItem.getIn(['name', 'last'], ''),
                  value: dItem.get('_id', ''),
                });
              })
          )
          .set('categoryWisePercentage', filteredItem);
      });

      setWarningTypes(
        warningConfigAr.unshift(
          Map({
            label: 'Define the Levels',
          })
        )
      );
    }
  }, []); //eslint-disable-line

  useEffect(() => {
    getRolesList();
    // getUser({ searchKey: searchKey });
  }, []); //eslint-disable-line

  useEffect(() => {
    if (warningTypes.getIn([activeIndex, 'denialManagement', 'accessType'], 'user') === 'user') {
      const timeout = setTimeout(() => {
        getUser({ searchKey: searchKey });
      }, 500);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [searchKey]); //eslint-disable-line

  const handleNext = (action) => {
    switch (action) {
      case 'Next': {
        if (warningValidation(warningTypes, setData, activeIndex, iscomprehensive)) {
          const selectedIndex = operation === 'update' ? disableIndex + 1 : activeIndex + 1;
          const findEditedIndex = warningTypes.findIndex((d) =>
            d.get('edited', false) && activeIndex === 0
              ? d.get('edited', false)
              : d.get('edited', false) && d.get('typeName', false) === activeIndex + 1
          );

          setActiveIndex(operation === 'updateNew' ? findEditedIndex : selectedIndex);
          setWarningTypes(warningTypes.setIn([selectedIndex, 'typeName'], selectedIndex));
        }
        break;
      }

      case 'Previous': {
        const selectedIndex = operation === 'update' ? 0 : activeIndex - 1;
        const addNewIndex = warningTypes.getIn([activeIndex - 1, 'edited'], false)
          ? selectedIndex
          : 0;
        setActiveIndex(operation === 'updateNew' ? addNewIndex : selectedIndex);

        break;
      }

      case 'Save': {
        const warningTypeList = warningTypes.shift().toJS();
        const prevPercentage = warningConfig.getIn([warningConfig.size - 1, 'percentage'], '');
        const currentPercentage = warningTypes.getIn([warningTypes.size - 1, 'percentage'], '');
        const callBack = () => {
          setOpen(false);
          getLmsSettings('leave');
          if (Number(prevPercentage) !== Number(currentPercentage)) {
            resetDenialCondition();
          }
        };

        const requestData = {
          settingId: lmsSettings.get('_id'),
          // warningConfig: [],
          [iscomprehensiveCondition
            ? 'comprehensiveWarningConfig'
            : 'warningConfig']: warningTypeList.map((item, i) => {
            const userIds = item?.denialManagement?.userIds;
            const roleIds = item?.denialManagement?.roleIds;

            const typeName = i + 1;
            const isDenial = warningTypeList.length === typeName;
            return {
              ...item,
              typeName,
              notificationToStudent: {
                ...item.notificationToStudent,
                sendNotificationAuthority:
                  item.notificationToStudent.setType === 'manual'
                    ? item.notificationToStudent.sendNotificationAuthority.map((data) => data.value)
                    : [],
              },
              notificationRoleIds:
                item?.isAdditionStaffNotify && item.notificationRoleIds?.length > 0
                  ? item.notificationRoleIds.map((s) => s?.value)
                  : [],
              ...(iscomprehensiveCondition && { notificationToStaff: false }),
              ...(isDenial && {
                ...(item?.denialCondition !== 'Cumulative' && {
                  unappliedLeaveConsideredAs: item?.unappliedLeaveConsideredAs,
                }),
                denialCondition: item?.denialCondition,
                categoryWisePercentage: item?.categoryWisePercentage.map((per) => {
                  return {
                    ...per,
                    percentage:
                      per?.percentage !== null && per?.percentage !== '' ? per?.percentage : 0,
                  };
                }),
                denialManagement: {
                  accessType: item?.denialManagement?.accessType,
                  userIds:
                    item?.denialManagement?.accessType === 'user' && userIds?.length > 0
                      ? userIds.map((s) => s?.value)
                      : [],
                  roleIds:
                    item?.denialManagement?.accessType === 'role' && roleIds?.length > 0
                      ? roleIds.map((s) => s?.value)
                      : [],
                },
              }),
            };
          }),
        };

        if (warningDetailValidation(warningTypes, setData, activeIndex)) {
          updateWarning(requestData, callBack);
        }
        break;
      }

      default:
        break;
    }
  };

  const addTypes = (i) => {
    const addWarning = warningTypes.splice(
      i + 1,
      0,
      fromJS({
        ...details,
        edited: operation === 'updateNew',
        typeName: i + 1,
      })
    );
    setWarningTypes(addWarning);
  };

  const deleteTypes = (index) => {
    setWarningTypes(warningTypes.delete(index));
  };

  const handleClose = () => {
    setOpen(false);
  };

  const preparedOption = (itemList, type = '') => {
    return itemList.map((item) => {
      return Map({
        title:
          type === 'user'
            ? item.getIn(['name', 'first'], '') + item.getIn(['name', 'last'], '')
            : item.get('name', ''),
        value: item.get('_id', ''),
      });
    });
  };

  const getOptions = (name) => {
    if (name === 'role') return preparedOption(roleList);
    if (name === 'user') return preparedOption(userList, 'user');
  };

  const isNext = () => {
    const checkEditedIndex = warningTypes.getIn([activeIndex + 1, 'edited'], false);
    return operation === 'updateNew'
      ? activeIndex !== 0
        ? !checkEditedIndex
        : false
      : activeIndex + 1 === warningTypes.size || disableIndex + 1 === activeIndex;
  };

  return (
    <div>
      <BootstrapDialog className={modalWidth} aria-labelledby="customized-dialog-title" open={open}>
        <BootstrapDialogTitle id="customized-dialog-title" onClose={handleClose}>
          Warning and Denial Configuration
        </BootstrapDialogTitle>

        <Box className={`${activeIndex === 0 ? 'ml-4  align-self-start' : 'w-100'}`}>
          <Stepper alternativeLabel nonLinear activeStep={activeIndex}>
            {activeIndex === 0 ? (
              <Step>
                <StepLabel className={`${Active}`}>Define the Levels</StepLabel>
              </Step>
            ) : (
              warningTypes.map((_, index) => (
                <Step
                  key={index}
                  // onClick={() => setActiveIndex(index)}
                  className="digi-cursor-pointer"
                >
                  <StepLabel className={`${Active}`}>
                    {index === 0 ? 'Define the Levels' : warningName(warningTypes, index)}
                  </StepLabel>
                </Step>
              ))
            )}
          </Stepper>
        </Box>

        <DialogContent dividers>
          {activeIndex === 0 ? (
            <WarningSettingsTable
              warningTypes={warningTypes}
              setWarningTypes={setWarningTypes}
              addTypes={addTypes}
              deleteTypes={deleteTypes}
              disableIndex={disableIndex}
              operation={operation}
              setData={setData}
              warningMode={warningMode}
            />
          ) : (
            <WarningTypeDetails
              warningTypes={warningTypes}
              setWarningTypes={setWarningTypes}
              activeIndex={activeIndex}
              getOptions={getOptions}
              setData={setData}
              setSearchKey={setSearchKey}
              searchKey={searchKey}
              warningMode={warningMode}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Stack spacing={2} direction="row">
            {/* <MButton variant="text" color={'blue'} clicked={handleClose}>
              Cancel
            </MButton> */}
            {activeIndex !== 0 && (
              <MButton variant="outlined" color={'blue'} clicked={() => handleNext('Previous')}>
                Previous
              </MButton>
            )}
            <MButton
              variant="contained"
              color={'blue'}
              clicked={() => handleNext(isNext() ? 'Save' : 'Next')}
              disabled={
                operation === 'updateNew'
                  ? !warningTypes.some((d) => d.get('edited', false))
                  : false
              }
            >
              {isNext() ? 'SAVE' : 'NEXT'}
            </MButton>
          </Stack>
        </DialogActions>
      </BootstrapDialog>
    </div>
  );
};

WarningSettings.propTypes = {
  getRolesList: PropTypes.func,
  getUser: PropTypes.func,
  roleList: PropTypes.instanceOf(List),
  updateWarning: PropTypes.func,
  lmsSettings: PropTypes.instanceOf(Map),
  open: PropTypes.bool,
  setOpen: PropTypes.func,
  setData: PropTypes.func,
  operation: PropTypes.string,
  getLmsSettings: PropTypes.func,
  resetDenialCondition: PropTypes.func,
  disableIndex: PropTypes.number,
  userList: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    roleList: selectRolesList(state),
    lmsSettings: selectLmsSettings(state),
    userList: selectUserList(state),
  };
};

export default connect(mapStateToProps, actions)(WarningSettings);
