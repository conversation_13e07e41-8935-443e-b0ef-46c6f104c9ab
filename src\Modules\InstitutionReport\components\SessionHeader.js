import React, { Suspense, useEffect, useState } from 'react';
// import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import MaterialInput from 'Widgets/FormElements/material/Input';
// import EventIcon from '@mui/icons-material/Event';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import MButton from 'Widgets/FormElements/material/Button';
import CachedOutlinedIcon from '@mui/icons-material/CachedOutlined';
import { selectInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
// import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import moment from 'moment';
import EventIcon from '@mui/icons-material/Event';
import { Box, Divider, Menu } from '@mui/material';
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker';
import { PickersDay } from '@mui/x-date-pickers/PickersDay';

const SetDayModal = React.lazy(() => import('./modal/setDayModal'));

const ITEM_HEIGHT = 48;
export function getInstitutionData(institutionCalendarLists) {
  if (institutionCalendarLists.size > 0) {
    return institutionCalendarLists
      .map((data) => {
        return {
          name: `AY  ` + data.get('calendar_name', ''),
          value: data.get('_id'),
          isActive: data.get('isActive', ''),
          color: data.get('isActive', '') ? 'blue' : 'gray',
          end_date: data.get('end_date', ''),
        };
      })
      .toJS();
  }
  return [];
}

export const getFormattedDateRange = (data) => {
  let startDate = data.get('start');
  let endDate = data.get('end');
  if (!(startDate && endDate)) return 'Select The Date';

  startDate = moment(startDate).format('DD/MM/YYYY');
  endDate = moment(endDate).format('DD/MM/YYYY');
  return startDate === endDate ? startDate : `${startDate} To ${endDate}`;
};

const CustomDateRange = ({ anchorEl, onClose, value, minDate, maxDate }) => {
  const [dateRange, setDateRange] = useState(Map());

  useEffect(() => {
    setDateRange(value);
  }, [value]);

  const handleChange = (key) => (value) => {
    const selectedDate = new Date(value);
    const timestamp = Date.parse(selectedDate);
    if (isNaN(timestamp)) return;

    const dateValue = moment(selectedDate).format('YYYY-MM-DD');
    if (key === 'start') setDateRange(Map({ start: dateValue }));
    else {
      const updatedValue = dateRange.set(key, dateValue);
      setDateRange(updatedValue);
      onClose(updatedValue);
    }
  };

  const handleClose = () => {
    if (dateRange.get('end')) return onClose(dateRange);
    const startDate = dateRange.get('start', new Date());
    onClose(dateRange.set('end', startDate));
  };

  const renderDay = (type) => (day, _value, DayComponentProps) => {
    const startDate = dateRange.get('start', '');
    const endDate = dateRange.get('end', '');
    const isHighlighted = day.isAfter(startDate, 'day') && day.isBefore(endDate, 'day');
    const isBoundary = type === 'start' ? day.isSame(endDate, 'day') : day.isSame(startDate, 'day');

    return (
      <PickersDay
        {...DayComponentProps}
        sx={{
          borderRadius: '4px',
          ...((isHighlighted || isBoundary) && {
            backgroundColor: '#EFF9FB',
            '&:hover': {
              backgroundColor: '#EFF9FB',
            },
          }),
        }}
      />
    );
  };

  return (
    <Menu
      id="long-menu"
      anchorEl={anchorEl}
      keepMounted
      open={Boolean(anchorEl)}
      onClose={handleClose}
      PaperProps={{
        style: {
          maxHeight: ITEM_HEIGHT * 10.5,
          width: 'auto',
        },
      }}
    >
      <div className="px-3 py-2 border-bottom">
        <p className="digi-gray">Please Choose Your Date</p>
      </div>
      <div className="px-3 mt-2">
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <Box sx={{ display: 'flex', alignItems: 'center', overflow: 'auto' }}>
            <div>
              <p className="text-gray f-14 ml-4">Start Date</p>
              <StaticDatePicker
                displayStaticWrapperAs="desktop"
                value={dateRange.get('start', new Date())}
                onChange={handleChange('start')}
                minDate={minDate}
                maxDate={maxDate}
                renderDay={renderDay('start')}
              />
            </div>
            <Divider orientation="vertical" className="mx-2" flexItem />
            <div>
              <p className="text-gray f-14 ml-4">End Date</p>
              <StaticDatePicker
                displayStaticWrapperAs="desktop"
                value={dateRange.get('end', '')}
                onChange={handleChange('end')}
                minDate={dateRange.get('start', new Date())}
                maxDate={maxDate}
                renderDay={renderDay('end')}
              />
            </div>
          </Box>
        </LocalizationProvider>
      </div>
    </Menu>
  );
};

function SessionHeader({
  institutionCalendarLists,
  data,
  setData,
  refreshData,
  CheckPermission,
  searchP,
  setPSearch,
  updatedDate,
}) {
  const institutionData = getInstitutionData(institutionCalendarLists);

  const iCalendarId =
    data.get('institutionCalendarId', '') !== ''
      ? data.get('institutionCalendarId', '')
      : institutionData?.[0]?.value;

  function getMinMaxDate(date) {
    if (institutionCalendarLists.size > 0) {
      const findCalendar = institutionCalendarLists.find(
        (item) => item.get('_id', '') === iCalendarId
      );
      return findCalendar.get(date, '');
    }
    return '';
  }

  const [dayOpen, setDayOpen] = React.useState(false);
  const [dateAnchorEl, setDateAnchorEl] = useState(null);

  const handleDayOpen = () => {
    setDayOpen(true);
  };
  const startTime = data.getIn(['time', 'start'], '');
  const endTime = data.getIn(['time', 'end'], '');
  const handleDayClose = () => {
    setDayOpen(false);
  };

  const handleChangeYear = (e) => {
    const year = data.set('institutionCalendarId', e.target.value);
    setData(year);
  };

  const handleDateOpen = (e) => setDateAnchorEl(e.currentTarget);
  const handleDateClose = (val) => {
    setDateAnchorEl(null);
    setData(data.set('date', val).set('time', Map()));
  };

  return (
    <div className="row ">
      <div className="col-md-12 col-xl-4 col-lg-4">
        <p className="mb-0 bold f-21 mt-2"> Curricular Delivery Monitoring</p>
      </div>
      <div className="col-md-4 col-xl-3 col-lg-2">
        {CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Academic Year') && (
          <MaterialInput
            elementType={'materialSelectNew'}
            type={'text'}
            variant={'outlined'}
            renderValue={(selected) => {
              const selectedInsObject = institutionData.find((data) => data.value === selected);
              return selectedInsObject?.name;
            }}
            size={'small'}
            elementConfig={{ options: institutionData }}
            defaultValue={institutionData?.[0]?.value}
            changed={(e) => handleChangeYear(e)}
            value={iCalendarId}
            isCalendarList={true}
            autoWidth={true}
          />
        )}
      </div>
      <div className="col-md-8 col-xl-5 col-lg-6">
        <div className="d-flex timeDayMerge digi-gray">
          {/* <div className="rightBorder d-flex w-75 remove_hover">
            {CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Date Select') ? (
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DatePicker
                  value={data.get('date')}
                  onChange={(e) => handleDateChange(e)}
                  inputFormat="DD/MM/YYYY"
                  renderInput={({ inputRef, inputProps, InputProps }) => (
                    <div className="d-flex align-items-center">
                      <input ref={inputRef} {...inputProps} className="border-none w-65" />
                      {InputProps?.endAdornment}
                    </div>
                  )}
                  minDate={getMinMaxDate('start_date')}
                  maxDate={getMinMaxDate('end_date')}
                />
              </LocalizationProvider>
            ) : (
              data.get('date')
            )}
          </div> */}

          <div
            className="rightBorder d-flex w-100 remove_hover pr-3"
            {...(CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Date Select') && {
              onClick: handleDateOpen,
            })}
          >
            <p>{getFormattedDateRange(data.get('date', Map()))}</p>
            {CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Date Select') && (
              <EventIcon className="ml-auto" />
            )}
          </div>
          <CustomDateRange
            anchorEl={dateAnchorEl}
            onClose={handleDateClose}
            value={data.get('date', Map())}
            minDate={getMinMaxDate('start_date')}
            maxDate={getMinMaxDate('end_date')}
          />

          <div
            className="d-flex w-75 remove_hover"
            onClick={
              CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Time Select') &&
              handleDayOpen
            }
          >
            <p className="mb-0 ml-3">
              {' '}
              {startTime !== '' && endTime !== ''
                ? `${moment(new Date(startTime)).format('LT')} - ${moment(new Date(endTime)).format(
                    'LT'
                  )}`
                : 'All Day'}{' '}
            </p>
            {CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Time Select') && (
              <AccessTimeOutlinedIcon className="ml-auto" />
            )}
          </div>
        </div>

        <div className="d-flex justify-content-end align-items-center">
          <p className="digi-gray-neutral mb-0 pr-1"> Last updated :</p>
          <p className="mb-0 mr-3 digi-gray"> {updatedDate}</p>
          <MButton
            variant="outlined"
            color="primary"
            className={'bg-white'}
            startIcon={<CachedOutlinedIcon />}
            clicked={refreshData}
          >
            Refresh
          </MButton>
        </div>
      </div>
      {dayOpen && (
        <Suspense fallback={''}>
          <SetDayModal handleDayClose={handleDayClose} data={data} setData={setData} />
        </Suspense>
      )}
    </div>
  );
}

SessionHeader.propTypes = {
  institutionCalendarLists: PropTypes.instanceOf(List),
  data: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  refreshData: PropTypes.func,
  CheckPermission: PropTypes.func,
  searchP: PropTypes.string,
  setPSearch: PropTypes.func,
  updatedDate: PropTypes.string,
};
const mapStateToProps = function (state) {
  return {
    institutionCalendarLists: selectInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps)(SessionHeader);
