import React, { useContext, useEffect, useRef } from 'react';
//import PropTypes from 'prop-types';
import Select from '@mui/material/Select';
import { getLang } from '../../../../../utils';
import { useTranslation } from 'react-i18next';

import parentContext from '../../Context/university-context';
import Filter from '../../../../../Assets/filter.png';
import MaterialInput from '../../../../../Widgets/FormElements/material/Input';
import { makeStyles } from '@mui/styles';

const useStylesFunction = makeStyles((themes) => ({
  select: {
    borderRadius: '22px !important',
    fontSize: '16px',
    paddingRight: '9px ',
  },
  placeholder: {
    marginRight: '10px',
  },
  root: {
    '&:focus': {
      backgroundColor: 'rgb(0 0 0 / 0%)',
    },
  },
}));

function FilterAndSearch() {
  const classes = useStylesFunction();
  const details = useContext(parentContext.addCollegeContext);
  const searchFilter = useContext(parentContext.filterSearchContext);
  const { getColleges, callId, limit } = details;
  const { setSelectOption, setSearch, setSort, sort, option, search } = searchFilter;

  const { t } = useTranslation();
  const dropDownValues = [
    { label: t('user_management.tabs.All'), value: '' },
    { label: t('program_input.active'), value: 'active' },
    { label: t('role_management.role_actions.Archive'), value: 'archived' },
  ];

  const didMount = useRef(true);
  useEffect(() => {
    if (didMount.current) {
      didMount.current = false;
      return;
    }
    const timeout = setTimeout(() => {
      getColleges({ id: callId, sort, limit, status: option, search });
    }, 500);
    return () => {
      clearTimeout(timeout);
    };
  }, [search]); // eslint-disable-line

  const sortCollegeList = () => {
    setSort(!sort);
    getColleges({ id: callId, sort: !sort, limit, status: option, search });
  };

  const selectOption = (value) => {
    setSelectOption(value);
    getColleges({ id: callId, sort: !sort, limit, status: value, search });
  };

  return (
    <div className="bg-white border border-radious-8">
      <div className="d-flex align-items-center p-3">
        <div className="col-xl-9  col-lg-8 col-md-7 col-7 pl-0 pt-1">
          <MaterialInput
            elementType={'materialSearch'}
            placeholder={t('role_management.modules_list.Search_College')}
            classes={{ placeholder: classes.placeholder }}
            changed={(event) => setSearch(event.target.value)}
            labelclass={`d-none`}
          />
        </div>
        <div
          className={`${
            getLang() === 'ar' ? 'pr-3 mr-3' : 'pr-2'
          } col-md-5 col-lg-4 col-xl-3 col-5`}
        >
          <div className={`d-flex justify-content-around`}>
            <div className="" onClick={sortCollegeList}>
              <img
                className={`float-right ${sort ? 'rotate pb-2 pl-3' : 'pt-2 pr-3'}`}
                alt=""
                src={Filter}
              />
            </div>

            <div className={`${getLang() === 'ar' ? 'mr-4' : 'pl-2'}`}>
              <Select
                labelId="term-label"
                onChange={(event) => selectOption(event.target.value)}
                native
                className={classes.select}
                inputProps={{
                  classes: {
                    root: classes.root,
                  },
                }}
                variant="outlined"
              >
                {dropDownValues.map((item, index) => (
                  <option key={index} value={item.value}>
                    {item.label}
                  </option>
                ))}
              </Select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
/* FilterAndSearch.propTypes = {
  setSearch: PropTypes.func,
  setSelectOption: PropTypes.func,
}; */
export default FilterAndSearch;
