import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router-dom';

import { selectIndependentCourse } from '_reduxapi/global_configuration/selectors';
import * as actions from '_reduxapi/global_configuration/actions';
import { getInstitutionHeader } from 'v2/utils';
import { CreditHours } from './ProgramInput/AllRows';

import { programInputContext } from './ProgramInput/NewProgramInput';
function IndependentCourse({ programInput, updateIndependentCourse, getIndependentCourse }) {
  const [radioValue, setRadioValue] = useState(
    Map({
      creditHours: '',
    })
  );
  const history = useHistory();
  useEffect(() => {
    getIndependentCourse({ header: institutionHeader, uId: institutionId });
  }, []); // eslint-disable-line
  useEffect(() => {
    if (
      programInput &&
      programInput.getIn(
        ['setting', 'globalConfiguration', 'independentCourseInput', 'creditHours'],
        List()
      ).size > 0
    ) {
      const selectedCreditHours = programInput
        .getIn(['setting', 'globalConfiguration', 'independentCourseInput', 'creditHours'], List())
        .find((item) => item.get('isDefault', false));
      const updatedValue = radioValue.update((key) => {
        return key.set('creditHours', selectedCreditHours?.get('mode', ''));
      });
      setRadioValue(updatedValue);
    }
  }, [programInput]); // eslint-disable-line
  const institutionHeader = getInstitutionHeader(history);
  const institutionId = institutionHeader?._institution_id;
  const handleRadioButton = (key, value, id) => {
    if (!programInput.getIn(['setting', 'isCreditHourEditable'], true)) {
      return;
    }
    let updatedValue = radioValue.set(key, value);
    setRadioValue(updatedValue);
    updateIndependentCourse({
      id,
      requestData: {
        settingId: programInput.getIn(['setting', '_id'], ''),
      },
      type: key,
      header: institutionHeader,
    });
  };
  const values = {
    programInput: programInput.getIn(
      ['setting', 'globalConfiguration', 'independentCourseInput'],
      Map()
    ),
    radioValue,
    handleRadioButton,
    disabled: !programInput.getIn(['setting', 'isCreditHourEditable'], true),
  };

  return (
    <programInputContext.Provider value={values}>
      <div className="pt-3 pl-3">
        <CreditHours />
      </div>
    </programInputContext.Provider>
  );
}
IndependentCourse.propTypes = {
  programInput: PropTypes.instanceOf(Map),
  updateIndependentCourse: PropTypes.func,
  getIndependentCourse: PropTypes.func,
  setData: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    programInput: selectIndependentCourse(state),
  };
};

export default connect(mapStateToProps, actions)(IndependentCourse);
