import React from 'react';

//ui-libraries

//components

//props
import PropTypes from 'prop-types';

//icon
// import AttachFile from 'Assets/attach_file.svg';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
// import { getShortString } from 'Modules/Shared/v2/Configurations';

const ProgressBarModal = ({ fileName, importPercent }) => {
  return (
    <>
      <Box sx={{ position: 'relative', display: 'inline-flex' }}>
        <CircularProgress variant="indeterminate" value={importPercent} />
        <Box
          sx={{
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography variant="caption" component="div" color="text.secondary">
            {`${importPercent}%`}
          </Typography>
        </Box>
      </Box>
    </>
  );
};

ProgressBarModal.propTypes = {
  fileName: PropTypes.string,
  importPercent: PropTypes.number,
};
export default ProgressBarModal;
