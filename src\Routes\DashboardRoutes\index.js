import React /* useEffect */ from 'react';
import { Route, withRouter /* , useHistory */ } from 'react-router-dom';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Map, List } from 'immutable';

import CalendarContainer from 'Containers/CalenderContainer/CalendarContainer';
import EventListView from 'Containers/CalenderContainer/eventListView';
import ReviewAccept from 'Containers/CalenderContainer/ReviewEvent/ReviewerAccept';
import ReviewEventView from 'Containers/CalenderContainer/ReviewEvent/ReviewEventView';
import AllCalendarList from 'Containers/CalenderContainer/AllCalendarList';

import ValidStaffView from 'Components/StaffManagement/validStaffManagement/valideStaffView';
import ValidBiometric from 'Components/StaffManagement/validStaffManagement/valideBiometric';
import ValidAcademic from 'Components/StaffManagement/validStaffManagement/valideAcademic';
import ValidEmployment from 'Components/StaffManagement/validStaffManagement/validEmployment';

import StaffManagementContainer from 'Containers/StaffManagementContainer/StaffManagementContainer';
import StudentProfileDetail from 'Components/Student/studentProfile';
import valideStudentContainer from 'Containers/valideContainer/valideStudentContainer';
import AddStaffProfile from 'Components/StaffManagement/StaffProfile/AddStaffProfile';
import EditStaffProfile from 'Components/StaffManagement/StaffProfile/EditStaffProfile';
import SubmittedStaffProfile from 'Components/StaffManagement/StaffProfile/SubmittedStaffProfile';
import MismatchStaffProfile from 'Components/StaffManagement/StaffProfile/MismatchStaffProfile';
import OverviewContainer from 'Containers/OverviewContainer/';
import OverviewContainerV2 from 'Containers/OverviewContainer/v2/';
import StaffInactive from 'Components/StaffManagement/CompleteStaffInactive';
import StudentActive from 'Components/StudentMangement/CompletedStudentInactive';
import SubmittedStudentProfile from 'Components/StudentMangement/StudentProfile/SubmittedStudentProfile';
import MismatchStudentProfile from 'Components/StudentMangement/StudentProfile/MismatchStudentProfile';
import PrivateRoute from 'Routes/PrivateRoutes/index';
import StudentRegistrationContainer from 'Containers/StudentRegistrationContainer/StudentRegistrationContainer';
import AddStudent from 'Containers/StudentRegistrationContainer/Student/AddStudent';
import EditStudent from 'Containers/StudentRegistrationContainer/Student/EditStudent';
import ProgramCalendarContainer from 'Containers/ProgramCalendarContainer/pages/Home';
import ProgramCalendar from 'Modules/ProgramCalendar/index';
import CalenderSettings from 'Containers/ProgramCalendarContainer/pages/CalenderSettings';
import InterimCalendarSettings from 'Containers/ProgramCalendarContainer/pages/Interim/CalenderSettings';
import AddCourse from 'Containers/ProgramCalendarContainer/pages/AddCourse';
import AddCourseV1 from 'Containers/ProgramCalendarContainer/pages/Regular/AddCourse';
import Review from 'Containers/ProgramCalendar/Review';
import ReviewerResponse from 'Containers/ProgramCalendar/ReviewerResponse';
import DeanResponse from 'Containers/ProgramCalendar/DeanResponse';
import StudentGrouping from 'Modules/StudentGrouping';
import InfrastructureManagement from 'Modules/InfrastructureManagement';
import LeaveManagement from 'Modules/LeaveManagement';
import Roles from 'Modules/Roles';
import ProgramInput from 'Modules/ProgramInput';
import CourseScheduling from 'Modules/CourseScheduling';
import ReportsAndAnalytics from 'Modules/ReportsAndAnalytics';
import Dashboard from 'Modules/Dashboard';
import Research from 'Modules/Research';

import Assessment from 'Modules/Assessment';
import AssessmentType from 'Components/Design/assessment/assessmentType';
import AllProgram from 'Components/Design/assessment/allProgram';
import EnterMark from 'Components/Design/assessment/enterMark';
import AssessmentPending from 'Components/Design/assessment/assessmentPending';
import assessmentPendingFullView from 'Components/Design/assessment/assessmentPendingFullView';
import levelList from 'Components/Design/assessment/levelList';
import ProgramList from 'Components/Design/assessment/programList';
import AttainmentLevel from 'Components/Design/assessment/AttainmentLevel';
import AttainmentCalculator from 'Components/Design/assessment/AttainmentCalculator';
import InstitutionSessionReport from 'Modules/InstitutionReport';
import GlobalConfigurationV1 from 'Modules/GlobalConfigurationV1';
import QAPC from 'Modules/QAPC';

import { CheckPermission } from 'Modules/Shared/Permissions';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import {
  getActiveVersion,
  isAnnouncementEnabled,
  isDigiSurveyEnabled,
  isModuleEnabled,
} from '../../utils';
import LMS from '../../Modules/LMS/index';
import LmsReportsMain from 'Modules/LeaveManagementReports/MainRoute';
import LeaveManagementPopups from '../../Modules/LmsReports/components/LeaveManagementPopups';
import LmsReports from 'Modules/LmsReports/components/lmsReports';
import IndividualStudentsReports from 'Modules/LmsReports/components/IndividualStudentsReports';
import StudentsName from 'Modules/LmsReports/components/StudentsName';
import AnnouncementSettingsIndex from 'Modules/Announcement/index';
import UserGlobalSearchIndex from 'Modules/UserGlobalSearch/index';

import FaceAnamolyReport from 'Modules/FaceAnamolyReport';
import StaffIntegrityMonitor from 'Modules/StaffIntegrityMonitor';
import StudentGroup from 'Modules/CourseAuthorFlow/StudentGroup';
import CourseHandoutReport from 'Modules/CourseHandoutReport';

const DashboardRoutes = ({ loggedInUserData }) => {
  let isStudent = loggedInUserData.get('user_type', '') === 'student';
  let hasPCReviewerPermission = false;
  let hasICReviewerPermission = false;
  const subRole = loggedInUserData.get('sub_role', List());
  if (subRole && subRole.size > 0) {
    hasPCReviewerPermission = subRole.indexOf('Program_Calendar_Reviewer') !== -1;
    hasICReviewerPermission = subRole.includes('Institution_Calendar_Reviewer');
  }
  const activeVersion = getActiveVersion();
  /*const history = useHistory();
  useEffect(() => {
    if (activeVersion === '/v2') history.push('/university-details');
  }, [activeVersion]); // eslint-disable-line */

  DashboardRoutes.propTypes = {
    loggedInUserData: PropTypes.instanceOf(Map),
  };

  const isSurveyRoleEnabled = CheckPermission('pages', 'User Module Permission', 'Survey', 'view');
  const isAnnouncementRoleEnable = CheckPermission(
    'pages',
    'User Module Permission',
    'Announcement',
    'view'
  );
  const isAnnouncementModuleEnabled = isAnnouncementEnabled();
  const isSurveyModuleEnabled = isDigiSurveyEnabled();

  const isUserModulePermission =
    (isSurveyModuleEnabled && isSurveyRoleEnabled) ||
    (isAnnouncementModuleEnabled && isAnnouncementRoleEnable);

  return (
    <>
      {CheckPermission('pages', 'Global Configuration', 'Institution', 'View') && (
        <Route path="/globalConfiguration-v1" component={GlobalConfigurationV1} />
      )}
      {isModuleEnabled('Q360') && <Route path="/qapc" component={QAPC} />}
      {CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'View') && (
        <PrivateRoute path="/InstitutionSessionReport" exact component={InstitutionSessionReport} />
      )}
      <PrivateRoute path="/InstitutionCalendar" exact component={CalendarContainer} />
      {CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Calendar List View') && (
        <PrivateRoute path="/all-calendar-list" exact component={AllCalendarList} />
      )}
      {CheckPermission('pages', 'Calendar', 'Institution Calendar', 'View All') && (
        <PrivateRoute path="/eventList" exact component={EventListView} />
      )}
      {CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Review') && (
        <PrivateRoute path="/reviewevent" exact component={ReviewEventView} />
      )}
      {hasICReviewerPermission && (
        <PrivateRoute path="/reviewaccept" exact component={ReviewAccept} />
      )}
      {(CheckPermission(
        'tabs',
        'User Management',
        'Staff Management',
        '',
        'Registered',
        'Profile View'
      ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Invalid',
          'Profile View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Valid',
          'Profile View'
        )) && <PrivateRoute path="/staff/management/profile" exact component={ValidStaffView} />}
      {(CheckPermission(
        'tabs',
        'User Management',
        'Staff Management',
        '',
        'Registered',
        'Academic View'
      ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Invalid',
          'Academic View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Valid',
          'Academic View'
        )) && <PrivateRoute path="/staff/management/academic" exact component={ValidAcademic} />}

      {(CheckPermission(
        'tabs',
        'User Management',
        'Staff Management',
        '',
        'Registered',
        'Employment View'
      ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Invalid',
          'Employment View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Valid',
          'Employment View'
        )) && (
        <PrivateRoute path="/staff/management/employment" exact component={ValidEmployment} />
      )}
      {(CheckPermission(
        'tabs',
        'User Management',
        'Staff Management',
        '',
        'Registered',
        'Biometric View'
      ) ||
        CheckPermission(
          'tabs',
          'User Management',
          'Student Management',
          '',
          'Registered',
          'Biometric View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Invalid',
          'Biometric View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Valid',
          'Biometric View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          'Invalid',
          'Biometric View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Student Pending',
          '',
          'Valid',
          'Biometric View'
        )) && <PrivateRoute path="/staff/management/biometric" exact component={ValidBiometric} />}
      {CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings') && (
        <>
          <Route exact path="/calender/:id/:year/:sem?" component={CalenderSettings} />
          <Route exact path="/interim-calender" component={InterimCalendarSettings} />
        </>
      )}
      {CheckPermission('modules', 'Student Grouping', 'Dashboard', 'View') && (
        <Route path="/student-grouping" component={StudentGrouping} />
      )}
      <Route path="/research" component={Research} />
      {CheckPermission('pages', 'Infrastructure Management', 'Onsite', 'View') && (
        <Route path="/infrastructure-management" component={InfrastructureManagement} />
      )}
      <Route path="/leave-management" component={LeaveManagement} />
      {CheckPermission('pages', 'Roles and Permissions', 'Dashboard', 'View') && (
        <Route path="/roles" component={Roles} />
      )}
      <Route path="/program-input" component={ProgramInput} />
      {(CheckPermission('tabs', 'Program Calendar', 'Dashboard', '', 'Course', 'Edit') ||
        CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Add Course')) && (
        <>
          <Route exact path="/interim-course" component={AddCourse} />
          <Route exact path="/course-v1/:id/:year/:sem?" component={AddCourseV1} />
        </>
      )}
      <Route
        path="/overview"
        exact
        render={() => {
          return activeVersion === '/v2' ? (
            <OverviewContainerV2 loggedInUserData={loggedInUserData} />
          ) : (
            <OverviewContainer loggedInUserData={loggedInUserData} />
          );
        }}
      />
      <PrivateRoute
        path="/program-calendar/:id?/:year?/:sem?"
        // exact
        component={ProgramCalendarContainer}
      />
      <PrivateRoute path="/pc" component={ProgramCalendar} />
      {CheckPermission('pages', 'User Management', 'Staff Management', 'View') && (
        <PrivateRoute path="/staff/management" exact component={StaffManagementContainer} />
      )}
      <PrivateRoute path="/staff/inactive" exact component={StaffInactive} />
      {CheckPermission('pages', 'User Management', 'Student Management', 'View') && (
        <PrivateRoute path="/student/management" exact component={StudentRegistrationContainer} />
      )}
      <PrivateRoute path="/student/inactive" exact component={StudentActive} />
      {(CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'Imported',
        'Edit'
      ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          'Invited',
          'Edit'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          'Expired',
          'Edit'
        )) && <PrivateRoute path="/student/profile/edit" exact component={EditStudent} />}
      {CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'All',
        'Add Single'
      ) && <PrivateRoute path="/student/profile/add" exact component={AddStudent} />}
      {(CheckPermission(
        'subTabs',
        'User Management',
        'Staff Management',
        '',
        'Registration Pending',
        '',
        'Imported',
        'Edit'
      ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Invited',
          'Edit'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Staff Management',
          '',
          'Registration Pending',
          '',
          'Expired',
          'Edit'
        )) && <PrivateRoute path="/staff/profile/edit" exact component={EditStaffProfile} />}
      {CheckPermission(
        'subTabs',
        'User Management',
        'Staff Management',
        '',
        'Registration Pending',
        '',
        'All',
        'Add Single'
      ) && <PrivateRoute path="/staff/profile/add" exact component={AddStaffProfile} />}
      <PrivateRoute path="/student/profile/detail" exact component={StudentProfileDetail} />
      {(CheckPermission(
        'tabs',
        'User Management',
        'Student Management',
        '',
        'Registered',
        'Profile View'
      ) ||
        CheckPermission(
          'tabs',
          'User Management',
          'Student Management',
          '',
          'Registered',
          'Biometric View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          'Invalid',
          'Profile View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          'Invalid',
          'Biometric View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          'Valid',
          'Profile View'
        ) ||
        CheckPermission(
          'subTabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          '',
          'Valid',
          'Biometric View'
        )) && <PrivateRoute path="/studentvalid/all" exact component={valideStudentContainer} />}
      {CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'Submitted',
        'Profile View'
      ) && (
        <PrivateRoute
          path="/submitted/student/profile/"
          exact
          component={SubmittedStudentProfile}
        />
      )}
      {CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'Mismatch',
        'Profile View'
      ) && (
        <PrivateRoute path="/mismatch/student/profile/" exact component={MismatchStudentProfile} />
      )}
      {CheckPermission(
        'subTabs',
        'User Management',
        'Staff Management',
        '',
        'Registration Pending',
        '',
        'Submitted',
        'Profile View'
      ) && (
        <PrivateRoute path="/submitted/staff/profile/" exact component={SubmittedStaffProfile} />
      )}
      {CheckPermission(
        'subTabs',
        'User Management',
        'Staff Management',
        '',
        'Registration Pending',
        '',
        'Mismatch',
        'Profile View'
      ) && <PrivateRoute path="/mismatch/staff/profile/" exact component={MismatchStaffProfile} />}

      {hasPCReviewerPermission && (
        <PrivateRoute path="/programcalendar/review" exact component={ReviewerResponse} />
      )}
      {CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish') && (
        <PrivateRoute path="/programcalendar/dean" exact component={DeanResponse} />
      )}
      {/* {userType === 'Vice_Dean' && ( */}
      <PrivateRoute path="/programcalendar/vice-dean" exact component={Review} />
      {/* )} */}
      {(CheckPermission('pages', 'Infrastructure Management', 'Remote', 'View') ||
        CheckPermission(
          'tabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          'Course View'
        ) ||
        CheckPermission(
          'tabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'TimeTable',
          'View'
        ) ||
        CheckPermission(
          'tabs',
          'Schedule Management',
          'Extra Curricular and Break',
          '',
          'Extra Curricular',
          'View'
        ) ||
        CheckPermission(
          'tabs',
          'Schedule Management',
          'Extra Curricular and Break',
          '',
          'Break',
          'View'
        ) ||
        CheckPermission(
          'tabs',
          'Schedule Management',
          'Assign Course Coordinator',
          '',
          'Course List',
          'View'
        )) && <Route path="/course-scheduling" component={CourseScheduling} />}
      <Route path="/reports" component={ReportsAndAnalytics} />
      {!isStudent && <Route path="/dashboard" component={Dashboard} />}
      {/* design  router assessment start */}
      <Route path="/assessment-management" component={Assessment} />
      <Route path="/courseHandoutReport" component={CourseHandoutReport} />

      <Route path="/attainment-calculator" component={Assessment} />
      <Route path="/assessment" exact component={AssessmentType} />
      <Route path="/AllProgram" exact component={AllProgram} />
      <Route path="/AssessmentPending" exact component={AssessmentPending} />
      <Route path="/EnterMark" exact component={EnterMark} />
      <Route path="/levelList" exact component={levelList} />
      <Route path="/programList" exact component={ProgramList} />
      <Route path="/AttainmentLevel" exact component={AttainmentLevel} />
      <Route path="/assessmentPendingFullView" exact component={assessmentPendingFullView} />
      <Route path="/AttainmentCalculator" exact component={AttainmentCalculator} />
      <Route path="/lms" component={LMS} />
      {!isStudent && <Route path="/lmsReportsMain" component={LmsReportsMain} />}

      <Route path="/lmsReports" exact component={LmsReports} />
      <Route path="/IndividualStudentsReports" exact component={IndividualStudentsReports} />
      <Route path="/StudentsName" exact component={StudentsName} />
      {/* design  router assessment end */}
      {/* design router LeaveManagement start*/}
      <Route path="/LeaveManagementPopups" component={LeaveManagementPopups} />
      {/* design router LeaveManagement end*/}
      {CheckPermission('pages', 'Face Anamoly Purification', 'Anomaly Purifying', 'View') && (
        <Route path="/face-anamoly-report" component={FaceAnamolyReport} />
      )}
      {isUserModulePermission && (
        <Route path="/UserModulePermissions" component={AnnouncementSettingsIndex} />
      )}
      {CheckPermission('pages', 'Global Search', 'Dashboard', 'View') && (
        <Route path="/UserGlobalSearch" component={UserGlobalSearchIndex} />
      )}
      <Route path="/staff_integrity_monitor" component={StaffIntegrityMonitor} />

      <Route path="/course-schedule/student-group" component={StudentGroup} />
    </>
  );
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(DashboardRoutes));
