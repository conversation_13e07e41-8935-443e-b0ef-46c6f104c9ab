import React from 'react';
import PropTypes, { oneOfType } from 'prop-types';
import Page from './Page';
import './pdf.css';
import Logo from '../../Assets/logo-pdf.png';
import moment from 'moment';
import { writeHijri } from '../../utils';

const MultiPage = ({ id, props }) => {
  let eventList = (
    <tr>
      <td colSpan="4" className="text-center">
        No events found....
      </td>
    </tr>
  );
  if (props.events !== undefined && props.events && props.events.length > 0) {
    var sortedByDate = props.events.sort((a, b) => {
      return new Date(a.event_date).getTime() - new Date(b.event_date).getTime();
    });

    eventList = sortedByDate.map((list, index) => {
      let oneDayEvent = moment(list.event_date).format('L') === moment(list.end_date).format('L');
      let eventDate =
        moment(list.event_date).format('MMM Do') +
        ' - ' +
        moment(list.end_date).format('MMM Do, YYYY');

      let hijiriYear =
        writeHijri(new Date(list.event_date)) + ' - ' + writeHijri(new Date(list.end_date));
      if (oneDayEvent) {
        eventDate = moment(list.event_date).format('MMM Do, YYYY');
        hijiriYear = writeHijri(new Date(list.event_date));
      }
      return (
        <React.Fragment key={index}>
          <tr>
            <td className="serial">{index + 1}</td>
            <td className="desc">{eventDate} </td>
            <td className="qty">
              {moment(list.start_time).format('hh:mm A') +
                ' - ' +
                moment(list.end_time).format('hh:mm A')}
            </td>
            <td className="unit">{list.event_name.first_language}</td>
          </tr>

          <tr>
            <td></td>
            <td>{hijiriYear}</td>
            <td colSpan="2" className="desc right borders" dir="rtl">
              <p dir="rtl" style={{ marginBottom: '0px' }}>
                {list.event_name.second_language}
              </p>
            </td>
          </tr>
        </React.Fragment>
      );
    });
  }

  return (
    <React.Fragment>
      <Page id={id}>
        <div className="main p-2">
          <div className="container mt-4">
            <div>
              <header className="clearfix">
                <div className="container">
                  <div className="company-address">
                    <figure>
                      <img src={Logo} alt="PDF Generate" className="logo-pdf" />
                    </figure>
                  </div>
                  <div className="clearfix"></div>
                </div>
              </header>

              <section>
                <div className="container">
                  <div className="details clearfix">
                    <div className="clearfix"></div>
                    <div className="col-lg-12">
                      <div className="row fs15">
                        <div className="col-lg-4">
                          <b>Academic Year :</b> {props.academicYear}
                        </div>
                        <div className="col-lg-4">
                          <b>Start Date : </b>{' '}
                          {props.academicYearStartDate !== ''
                            ? moment(props.academicYearStartDate).format('MMM Do,YYYY')
                            : ''}
                        </div>
                        <div className="col-lg-4">
                          <b>End Date : </b>{' '}
                          {props.academicYearEndDate !== ''
                            ? moment(props.academicYearEndDate).format('MMM Do,YYYY')
                            : ''}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="center">
                    <h1 className="f-30 fs18">Event Details</h1>
                    <br />
                  </div>

                  <table border={0} cellSpacing={0} cellPadding={0}>
                    <thead>
                      <tr>
                        <th className="serial">S.No</th>
                        <th className="desc">Event Date</th>
                        <th className="qty">Event Time</th>
                        <th className="unit">Event Title</th>
                      </tr>
                    </thead>
                    <tbody className="fs14">
                      {eventList}

                      {/* <tr>
                              <td className="serial">1</td>
                              <td className="desc">20/08/2020</td>
                              <td className="unit">first annual day</td>
                              <td className="qty">9.00 am</td>
                              </tr>
                              <tr>
                              <td className="serial">1</td>
                              <td className="desc">20/08/2020</td>
                              <td  colSpan="2" className="desc right borders"  dir="rtl"><p dir="rtl">عندما يريد العالم أن ‪يتكلّم ‬ ، فهو يتحدّث بلغة يونيكود. تسجّل الآن</p></td>
                            </tr>
                 */}
                    </tbody>
                  </table>
                </div>
              </section>
              <footer>
                <div className="container">
                  <div className="thanks"></div>

                  <div className="end">
                    © Copyright 2020, Digischeduler Developed & Maintained by Digival Solutions Pvt
                    Ltd.
                  </div>
                </div>
              </footer>
            </div>
          </div>
        </div>
      </Page>
    </React.Fragment>
  );
};

MultiPage.propTypes = {
  id: PropTypes.string,
  props: oneOfType([PropTypes.string, PropTypes.object]),
  academicYear: PropTypes.string,
  academicYearStartDate: PropTypes.string,
  academicYearEndDate: PropTypes.string,
  events: PropTypes.array,
};

export default MultiPage;
