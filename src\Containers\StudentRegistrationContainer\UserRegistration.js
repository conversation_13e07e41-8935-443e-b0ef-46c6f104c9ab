import React from 'react';

import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { Box } from '@mui/material';

import Pending from 'Modules/UserRegisterApproval/Pending';
import Reject from 'Modules/UserRegisterApproval/Reject';
import Approved from 'Modules/UserRegisterApproval/Approved';

const customTabStyle = {
  borderBottom: 'none !important',
  '& .MuiTabs-indicator': {
    display: 'none',
  },
  '& .MuiTabs-flexContainer': {
    gap: '10px',
  },
};
const customTabStyleRound = {
  backgroundColor: '#e7e8e9',
  color: '#3e95ef',
  borderRadius: '30px',
  padding: '6px 10px',
  fontSize: '13px',
  minHeight: '41px',
  '&.MuiTab-root.Mui-selected': {
    border: '2px solid #3e95ef',
  },
};
function UserRegistration() {
  const [value, setValue] = React.useState('1');

  const handleTabs = (event, newValue) => {
    setValue(newValue);
  };
  return (
    <div className="main bg-gray pl-4 pt-3">
      {/* <Loader isLoading={this.state.isLoading} /> */}
      <Box sx={{ borderBottom: 0 }}>
        <TabContext value={value}>
          <TabList onChange={handleTabs} sx={customTabStyle}>
            <Tab className="text-capitalize" label="Pending" value="1" sx={customTabStyleRound} />
            <Tab className="text-capitalize" label="Rejected" value="2" sx={customTabStyleRound} />
            <Tab className="text-capitalize" label="approved" value="3" sx={customTabStyleRound} />
          </TabList>
          <TabPanel value="1">
            <Pending />
          </TabPanel>
          <TabPanel value="2">
            <Reject />
          </TabPanel>
          <TabPanel value="3">
            <Approved />
          </TabPanel>
        </TabContext>
      </Box>
    </div>
  );
}

export default UserRegistration;
