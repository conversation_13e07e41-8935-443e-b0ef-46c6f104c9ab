import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import MaterialInput from 'Widgets/FormElements/material/Input';
import ReactECharts from 'echarts-for-react';
import { fromJS, List, Map } from 'immutable';
import { trimFractionDigits, ucFirst, indVerRename } from 'utils';
import { getBenchmarkValues } from '../../utils';
import { getNumber } from 'v2/utils';

const AttainmentReportChart = ({
  outcome,
  courseProgramReport,
  selectedNode,
  setSelectedNode,
  setSelectedType,
  questionList,
  setQuestionList,
  selectedType,
  selectValues,
}) => {
  const [nodeOptions, setNodeOptions] = useState(List());
  const [chartOptions, setChartOptions] = useState({
    cloList: [],
    attainmentTarget: [],
    coAttainment: [],
    targetAttainment: [],
  });
  const attainmentType = selectValues.getIn(['attainmentType', 'value'], '');
  const manageTargetBenchMark = courseProgramReport.get('manageTargetBenchMark', Map());
  const programId = selectValues.getIn(['program', 'value'], '');

  const checkCloManageTargetBenchMark =
    attainmentType === 'Class Attainment' && outcome === 'clo' && !manageTargetBenchMark.isEmpty();

  const commonOptions = {
    legend: {
      data: [
        checkCloManageTargetBenchMark && 'Attainment Target',
        outcome === 'clo'
          ? `${indVerRename('CLO', programId)} - Attainment`
          : `${indVerRename('PLO', programId)} - Attainment`,
        // (attainmentType === 'Class Attainment' || outcome === 'plo') &&
        manageTargetBenchMark.isEmpty() && 'Target Attainment',
      ],
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 15,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          formatter: function ({ value }) {
            const studentDetail = value.split(',')[0];
            return studentDetail;
          },
        },
      },
    },
    dataZoom: [
      {
        type: 'slider',
        show: attainmentType !== 'Class Attainment' ? true : false,
      },
    ],
    grid: { bottom: 90 },
    yAxis: {
      type: 'value',
      nameGap: 30,
      data: [20, 40, 60, 80, 100],
      axisLine: { show: true, lineStyle: { color: '#F0EEEE' } },
      axisLabel: { color: '#333', formatter: '{value}%' },
      splitLine: false,
      max: 100,
    },
  };

  const getOption = () => {
    let filteredCloList = [];
    let filteredCoAttainment = [];
    if (
      (outcome === 'clo' &&
        attainmentType === 'Class Attainment' &&
        manageTargetBenchMark.isEmpty()) ||
      (outcome === 'plo' &&
        attainmentType === 'Class Attainment' &&
        manageTargetBenchMark.isEmpty()) ||
      outcome === 'plo'
    ) {
      chartOptions.coAttainment.forEach((item, index) => {
        if (item !== '-') {
          filteredCloList.push(chartOptions.cloList[index]);
          filteredCoAttainment.push(chartOptions.coAttainment[index]);
        }
      });
    } else {
      filteredCloList = chartOptions.cloList;
      filteredCoAttainment = chartOptions.coAttainment;
    }
    return {
      ...commonOptions,
      xAxis: {
        type: 'category',
        data: filteredCloList,
        nameGap: 30,
        nameLocation: 'center',
        axisTick: false,
        axisLine: { lineStyle: { color: '#F0EEEE' } },
        axisLabel: {
          color: '#333',
          formatter:
            attainmentType === 'Class Attainment' || outcome === 'plo'
              ? '#{value}'
              : function (value) {
                  const studentDetail = value.split(',');
                  return studentDetail.join('\n');
                },
        },
      },
      series: [
        {
          name:
            outcome === 'clo'
              ? `${indVerRename('CLO', programId)} - Attainment`
              : `${indVerRename('PLO', programId)} - Attainment`,
          data: filteredCoAttainment,
          type: 'bar',
          barMaxWidth: 30,
          itemStyle: { color: '#6366F1' },
          lineStyle: { color: '#6366F1', barMaxWidth: 30 },
        },

        {
          name: 'Target Attainment',
          data:
            attainmentType && manageTargetBenchMark.isEmpty() ? chartOptions.targetAttainment : [],
          markLine: {
            data:
              attainmentType && manageTargetBenchMark.isEmpty()
                ? [{ yAxis: getNumber(chartOptions?.targetAttainment[0]) }]
                : [],
            lineStyle: {
              type: 'solid',
              width: 3,
            },
            symbol: 'none',
          },
          type: 'line',
          itemStyle: { color: '#F59E0B' },
          lineStyle: { color: '#F59E0B' },
          symbol: 'none',
        },
        checkCloManageTargetBenchMark && {
          name: 'Attainment Target',
          data: chartOptions.attainmentTarget,
          type: 'bar',
          barMaxWidth: 30,
          itemStyle: { color: '#22C55E' },
          lineStyle: { color: '#22C55E', barMaxWidth: 30 },
        },
      ],
    };
  };

  useEffect(() => {
    const preparedOption = (data, steps = '') => {
      nodeList.push({
        name: ucFirst(`${steps}${data.get('nodeName', '') || data.get('typeName', '')}`),
        value: data.get('typeName', '') !== 'Overall Attainment' ? data.get('_id', '') : '',
        ...(outcome === 'clo' && attainmentType === 'Class Attainment'
          ? { clo: data.get('clo', List()) }
          : outcome === 'plo' && attainmentType === 'Class Attainment'
          ? { plo: data.get('plo', List()) }
          : { studentList: data.get('studentList', List()) }),
        typeId: data.get('typeId', ''),
      });
    };
    let nodeList = [{ name: '-- Select Node --', value: '-' }];
    courseProgramReport
      .get(
        outcome === 'clo' || (outcome === 'plo' && attainmentType === 'Class Attainment')
          ? 'attainmentNodeList'
          : 'attainmentLevel',
        List()
      )
      .forEach((one) => {
        preparedOption(one);
        one.get('subTree', List()).forEach((two) => {
          preparedOption(two, '-- ');
          two.get('node', List()).forEach((three) => {
            preparedOption(three, '---- ');
          });
        });
      });
    setNodeOptions(fromJS(nodeList));
  }, [courseProgramReport, outcome]); //eslint-disable-line

  useEffect(() => {
    chartOptionData(selectedNode, selectedType);
  }, [courseProgramReport, nodeOptions]); //eslint-disable-line

  useEffect(() => {
    setSelectedNode('');
    setSelectedType('');
    setNodeOptions(List());
    setChartOptions({
      cloList: [],
      coAttainment: [],
      targetAttainment: [],
      attainmentTarget: [],
    });
  }, [outcome]); //eslint-disable-line

  const getStudentDetails = (nodeId, studentId) => {
    const getStudentPercentage = () => {
      const cloData = nodeOptions
        .find((n) => n?.get('value', '') === nodeId)
        ?.get('studentList', List());
      return cloData?.size > 0
        ? cloData.find((o) => o?.get('studentId', '') === studentId)?.get('percentage', 0)
        : 0;
    };
    const studentPercentage = getStudentPercentage();

    const percentage = outcome === 'clo' ? studentPercentage : '';
    return percentage !== '' && percentage !== null && !isNaN(percentage) && percentage !== 0
      ? `${trimFractionDigits(percentage)}`
      : '-';
  };

  const getCloDetails = (nodeId, cloId) => {
    const getCloPercentage = () => {
      const cloData = nodeOptions
        .find((n) => n?.get('value', '') === nodeId)
        ?.get(outcome === 'clo' ? 'clo' : 'plo', List());
      return cloData !== undefined && cloData.size > 0
        ? cloData.find((o) => o?.get('_id', '') === cloId)?.get('percentage', 0)
        : List();
    };
    const cloPercentage = getCloPercentage();

    const percentage =
      outcome === 'clo' || (outcome === 'plo' && attainmentType === 'Class Attainment')
        ? cloPercentage
        : courseProgramReport
            .get('curriculumPLOs', List())
            .find((o) => o.get('_id', '') === cloId)
            .get('percentage', 0);
    return percentage !== '' && percentage !== null && !isNaN(percentage) && percentage !== 0
      ? `${trimFractionDigits(percentage)}`
      : '-';
  };

  const getManageAttainmentDetails = (cloId) => {
    const getOutcomeDetail = courseProgramReport
      .getIn(['manageTargetBenchMark', 'outComes'], List())
      .filter((clo) => clo.get('outComeId') === cloId);
    const percentage = getOutcomeDetail.getIn([0, 'values'], 0);
    return percentage !== '' && percentage !== null && !isNaN(percentage) && percentage !== 0
      ? `${trimFractionDigits(percentage)}`
      : '-';
  };

  const chartOptionData = (value, typeId) => {
    let cloList = [];
    let attainmentTarget = [];
    let coAttainment = [];
    let targetAttainment = [];

    if (value !== '-') {
      const filteredBenchMarkValue = getBenchmarkValues(typeId, courseProgramReport);

      courseProgramReport
        .get(
          outcome === 'clo' && attainmentType === 'Class Attainment'
            ? 'courseCLO'
            : outcome === 'plo' && attainmentType === 'Class Attainment'
            ? 'programPLO'
            : outcome === 'clo' && attainmentType === 'Student Attainment'
            ? 'studentList'
            : 'curriculumPLOs',
          List()
        )
        .forEach((item) => {
          cloList.push(
            attainmentType === 'Student Attainment'
              ? `${item.get('name', '')},${item.get('studentId', '')}`
              : outcome === 'clo'
              ? `${indVerRename('CLO', programId)} ${item.get('no', '')}`
              : `${indVerRename('PLO', programId)} ${item.get('no', '')}`
          );
          if (
            (outcome === 'clo' && attainmentType === 'Class Attainment') ||
            (outcome === 'plo' && attainmentType === 'Class Attainment')
          ) {
            attainmentTarget.push(getManageAttainmentDetails(item.get('_id', '')));
          }
          coAttainment.push(
            attainmentType === 'Student Attainment'
              ? getStudentDetails(value, item.get('studentId', ''))
              : getCloDetails(value, item.get('_id', ''))
          );
          if (courseProgramReport.get('attainmentValuesAs', '') === 'fixed') {
            targetAttainment.push(filteredBenchMarkValue.get('percentage', 0));
          } else {
            targetAttainment.push(filteredBenchMarkValue.get('min', 0));
          }
        });
    }
    setChartOptions({
      cloList,
      coAttainment,
      targetAttainment,
      attainmentTarget,
    });
  };

  const handleNodeChange = (e) => {
    const value = e.target.value;
    setSelectedNode(value);
    const typeId = nodeOptions.find((item) => item.get('value') === value);
    setSelectedType(typeId.get('typeId', ''));

    if (questionList.size !== 0) {
      setQuestionList(List());
    }
    chartOptionData(value, typeId.get('typeId', ''));
  };

  return (
    <div className="row mt-4 mb-3">
      <div className="col-12 col-lg-10 offset-lg-1">
        <div className="p-3 bg-white">
          <div className="d-flex justify-content-between align-items-center">
            <h3 className="f-20">Attainment Reports</h3>
            <div>
              <MaterialInput
                elementType={'materialSelectNew'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{ options: nodeOptions.toJS() }}
                labelclass={'mb-0'}
                value={selectedNode}
                changed={handleNodeChange}
                displayEmpty
                renderValue={(value) => {
                  const findItem = nodeOptions.find((data) => data.get('value') === value);
                  return findItem?.get('name', '').replaceAll('-', '');
                }}
              />
            </div>
          </div>
          <div className="attainment-report-chart">
            {selectedNode === '-' && (
              <div className="chart-info">Attainment Criteria Not Yet Selected</div>
            )}
            <ReactECharts
              notMerge={true}
              lazyUpdate={true}
              style={{ width: '100%', height: '300px' }}
              option={getOption()}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

AttainmentReportChart.propTypes = {
  outcome: PropTypes.string,
  courseProgramReport: PropTypes.instanceOf(Map),
  selectedNode: PropTypes.string,
  setSelectedNode: PropTypes.func,
  setSelectedType: PropTypes.func,
  questionList: PropTypes.instanceOf(List),
  setQuestionList: PropTypes.func,
  selectedType: PropTypes.string,
  selectValues: PropTypes.instanceOf(Map),
};

export default AttainmentReportChart;
