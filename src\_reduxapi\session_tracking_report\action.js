import { createAction } from '../util';
import axios from '../../axios';
import { fromJS } from 'immutable';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const GET_SESSION_REPORT_LIST_REQUEST = 'GET_SESSION_REPORT_LIST_REQUEST';
export const GET_SESSION_REPORT_LIST_SUCCESS = 'GET_SESSION_REPORT_LIST_SUCCESS';
export const GET_SESSION_REPORT_LIST_FAILURE = 'GET_SESSION_REPORT_LIST_FAILURE';

const getSessionReportListRequest = createAction(GET_SESSION_REPORT_LIST_REQUEST);
const getSessionReportListSuccess = createAction(GET_SESSION_REPORT_LIST_SUCCESS, 'data');
const getSessionReportListFailure = createAction(GET_SESSION_REPORT_LIST_FAILURE, 'error');

export function getSessionReport(date, params, callBack) {
  return function (dispatch) {
    dispatch(getSessionReportListRequest());
    axios
      .get(`/session-report/attendanceReport/${date}`, { params })
      .then((res) => {
        dispatch(getSessionReportListSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((error) => dispatch(getSessionReportListFailure(error)));
  };
}

export const GET_SESSION_REPORT_SINGLE_REQUEST = 'GET_SESSION_REPORT_SINGLE_REQUEST';
export const GET_SESSION_REPORT_SINGLE_SUCCESS = 'GET_SESSION_REPORT_SINGLE_SUCCESS';
export const GET_SESSION_REPORT_SINGLE_FAILURE = 'GET_SESSION_REPORT_SINGLE_FAILURE';

const getSessionReportSingleRequest = createAction(GET_SESSION_REPORT_SINGLE_REQUEST);
const getSessionReportSingleSuccess = createAction(GET_SESSION_REPORT_SINGLE_SUCCESS, 'data');
const getSessionReportSingleFailure = createAction(GET_SESSION_REPORT_SINGLE_FAILURE, 'error');

export function getSingleSessionReport(params) {
  return function (dispatch) {
    dispatch(getSessionReportSingleRequest());
    axios
      .get(`/session-report/scheduleDetails`, { params })
      .then((res) => dispatch(getSessionReportSingleSuccess(res.data.data)))
      .catch((error) => dispatch(getSessionReportSingleFailure(error)));
  };
}

export const GET_PROGRAM_LIST_REQUEST = 'GET_PROGRAM_LIST_REQUEST';
export const GET_PROGRAM_LIST_SUCCESS = 'GET_PROGRAM_LIST_SUCCESS';
export const GET_PROGRAM_LIST_FAILURE = 'GET_PROGRAM_LIST_FAILURE';

const getProgramListRequest = createAction(GET_PROGRAM_LIST_REQUEST);
const getProgramListSuccess = createAction(GET_PROGRAM_LIST_SUCCESS, 'data');
const getProgramListFailure = createAction(GET_PROGRAM_LIST_FAILURE, 'error');

export function getProgramList(params) {
  return function (dispatch) {
    dispatch(getProgramListRequest(true));
    axios
      .get('session-report/userProgramList', { params })
      .then((res) => dispatch(getProgramListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramListFailure(error)));
  };
}

export const GET_PROGRAM_COURSE_LIST_REQUEST = 'GET_PROGRAM_COURSE_LIST_REQUEST';
export const GET_PROGRAM_COURSE_LIST_SUCCESS = 'GET_PROGRAM_COURSE_LIST_SUCCESS';
export const GET_PROGRAM_COURSE_LIST_FAILURE = 'GET_PROGRAM_COURSE_LIST_FAILURE';

const getProgramCourseListRequest = createAction(GET_PROGRAM_COURSE_LIST_REQUEST);
const getProgramCourseListSuccess = createAction(GET_PROGRAM_COURSE_LIST_SUCCESS, 'data');
const getProgramCourseListFailure = createAction(GET_PROGRAM_COURSE_LIST_FAILURE, 'error');

export function getProgramCourseList(currentDate, params) {
  return function (dispatch) {
    dispatch(getProgramCourseListRequest(true));
    axios
      .get(`/session-report/programCourseList/${currentDate}`, { params })
      .then((res) => dispatch(getProgramCourseListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramCourseListFailure(error)));
  };
}

export const GET_EXPORT_REPORT_LIST_REQUEST = 'GET_EXPORT_REPORT_LIST_REQUEST';
export const GET_EXPORT_REPORT_LIST_SUCCESS = 'GET_EXPORT_REPORT_LIST_SUCCESS';
export const GET_EXPORT_REPORT_LIST_FAILURE = 'GET_EXPORT_REPORT_LIST_FAILURE';

const getExportReportListRequest = createAction(GET_EXPORT_REPORT_LIST_REQUEST);
const getExportReportListSuccess = createAction(GET_EXPORT_REPORT_LIST_SUCCESS, 'data');
const getExportReportListFailure = createAction(GET_EXPORT_REPORT_LIST_FAILURE, 'error');

export function getExportReport({ date, params, callBack }) {
  return function (dispatch) {
    dispatch(getExportReportListRequest());
    axios
      .get(`/session-report/attendanceReportExport/${date}`, { params })
      .then((res) => {
        dispatch(getExportReportListSuccess(res.data.data));
        callBack && callBack(fromJS(res.data.data.scheduleList));
      })
      .catch((error) => dispatch(getExportReportListFailure(error)));
  };
}

export const SCHEDULE_DOC_LIST_REQUEST = 'SCHEDULE_DOC_LIST_REQUEST';
export const SCHEDULE_DOC_LIST_SUCCESS = 'SCHEDULE_DOC_LIST_SUCCESS';
export const SCHEDULE_DOC_LIST_FAILURE = 'SCHEDULE_DOC_LIST_FAILURE';

const scheduleDocListRequest = createAction(SCHEDULE_DOC_LIST_REQUEST, 'isLoading');
const scheduleDocListSuccess = createAction(SCHEDULE_DOC_LIST_SUCCESS, 'data');
const scheduleDocListFailure = createAction(SCHEDULE_DOC_LIST_FAILURE, 'error');
export function scheduleDocList(scheduleId) {
  return function (dispatch) {
    dispatch(scheduleDocListRequest(true));
    axios
      .get(`digiclass/courseHandout/scheduledHandoutList?scheduleId=${scheduleId}`)
      .then((res) => {
        dispatch(scheduleDocListSuccess(res.data));
      })
      .catch((error) => dispatch(scheduleDocListFailure(error)));
  };
}
