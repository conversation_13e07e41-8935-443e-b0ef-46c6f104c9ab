import React, { /* useState,  */ Fragment } from 'react';
import { Badge, Divider, Paper } from '@mui/material';
import CircleIcon from '@mui/icons-material/Circle';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { List } from 'immutable';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import InsertPhotoIcon from '@mui/icons-material/InsertPhoto';
function Step4({ state }) {
  // const [previewForm, setPreviewForm] = useState(false);
  // const handlePreviewForm = () => {
  //   setPreviewForm(true);
  // };
  const formOccurrence = state.get('formOccurrence', List());

  const attachments = state.get('attachments', List());
  let imageUrl = '';
  let normalUrl = '';
  let fileName = '';

  if (attachments.size > 0) {
    const lastFile = attachments.last();
    const signedUrl = lastFile.get('signedUrl', '');
    const Url = lastFile.get('url', '');
    const attachmentsName = lastFile.get('name', '');
    imageUrl = signedUrl;
    normalUrl = Url;
    fileName = attachmentsName;
  }
  return (
    <div className="p-4 mt-1">
      <Paper elevation={0} variant="outlined" sx={{ height: 'calc(100vh -200px)' }}>
        <div className="p-3">
          <div className="d-flex align-items-center">
            <div className="h5 fw-400 q360-color-gray">NCAAA Course</div>
            {/* <div className="ml-auto">
              <MButton
                variant="contained"
                className="px-3"
                color="primary"
                clicked={handlePreviewForm}
              >
                Preview Form
              </MButton>
            </div> */}
          </div>
          <div className="d-flex pt-3 pb-4">
            <div>
              <div className="h6 text-primary mb-2 pb-1">Basic Info</div>
              <div className="f-12 fw-400 text-lGrey">Form Template :</div>
              <div className="f-16 fw-500 text-dGrey pb-2">NCAA Course Specification</div>
              <div className="f-12 fw-400 text-lGrey pb-1">Assign & Occurrence Details :</div>
              <div className="d-flex flex-wrp gap-10">
                {formOccurrence.map((occurrence) => {
                  return (
                    <Paper
                      elevation={0}
                      className="select-color w-23"
                      key={occurrence.get('_id', '')}
                      sx={{
                        '&.MuiPaper-root': {
                          borderRadius: '6px',
                        },
                      }}
                    >
                      <div className="px-3 py-2">
                        <div className="f-12 fw-500 text-dGrey">
                          {occurrence.get('program_name', '')}
                        </div>
                        <div className="d-flex align-items-center gap-5 flex-wrp">
                          {occurrence.get('curriculum', List()).map((curriculum, index) => {
                            const totalCourses = curriculum
                              .get('years', List())
                              .flatMap((innerList) => innerList.get('courses', List()));
                            const CoursesCount = totalCourses.size;
                            const showCircleIcon = index > 0;
                            const totalCourseCount = String(CoursesCount).padStart(2, '0');
                            return (
                              <div
                                className="d-flex align-items-center gap-3 "
                                key={curriculum.get('_curriculum_id', '')}
                              >
                                {showCircleIcon && (
                                  <CircleIcon sx={{ fontSize: 4, color: '#6B7280' }} />
                                )}
                                <div className="fw-400 text-mGrey f-10">
                                  {`${curriculum.get(
                                    'curriculum_name',
                                    ''
                                  )} : ${totalCourseCount} Courses`}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </Paper>
                  );
                })}
              </div>
            </div>
            <div className="ml-auto">
              <div className="f-12 fw-400 text-lGrey">Reference Files</div>
              <Paper
                elevation={0}
                variant="outlined"
                sx={{
                  '&.MuiPaper-root': {
                    borderRadius: '4px',
                  },
                  height: '113px',
                  width: '192px',
                }}
              >
                <div>
                  <div
                    className="d-flex justify-content-center align-items-center"
                    style={{ width: '192px', height: '77px' }}
                  >
                    {normalUrl.includes('.pdf') ? (
                      <iframe src={imageUrl} title="pdf" className="d-flex w-100 ht-11vh"></iframe>
                    ) : (
                      <img src={imageUrl} alt="imageUrl" className="p-0 img-fluid" />
                    )}
                  </div>
                  <Divider />
                  <div className="d-flex  justify-content-center gap-5 mt-2">
                    <div>
                      {normalUrl.includes('.pdf') ? (
                        <PictureAsPdfIcon fontSize="small" sx={{ color: '#374151' }} />
                      ) : (
                        <InsertPhotoIcon fontSize="small" sx={{ color: '#374151' }} />
                      )}
                    </div>
                    <div className="f-12 fw-500 ptx-3">{getShortString(fileName, 20)}</div>
                  </div>
                </div>
              </Paper>
              <Paper
                elevation={0}
                variant="outlined"
                className="mt-2"
                sx={{
                  '&.MuiPaper-root': {
                    borderRadius: '4px',
                  },
                  height: '20px',
                  width: '192px',
                }}
              >
                <div className="d-flex justify-content-center align-items-center f-10 fw-500 ptx-3 color-lt-gray pb-2">
                  Attached {attachments.size} Files
                </div>
              </Paper>
            </div>
          </div>
          <Divider />
          <div>
            <div className="h6 text-primary pt-3">Approver Hierarchy</div>
            <div className="d-flex align-items-center flex-wrp gap-35">
              {state.get('approvalLevel', List()).map((level, levelIndex) => {
                return (
                  <div key={levelIndex} className="flex-bases-300">
                    <div className="f-16 fw-500 text-dGrey">{level.get('name', '')}</div>
                    <div className="d-flex align-items-center gap-5 pt-1">
                      <div>
                        <div className="d-flex align-items-center gap-15">
                          <div className="f-12 fw-400 text-lGrey">Approval Role:</div>
                          <div className="d-flex align-items-center gap-15">
                            <Badge
                              badgeContent={`${level.get('selectedRoleUser', List()).size}`}
                              color="primary"
                              sx={{
                                '& .MuiBadge-badge': {
                                  minWidth: '0px',
                                  height: '18px',
                                  backgroundColor: '#E1F5FA',
                                  color: '#333333',
                                  fontSize: '10px',
                                  fontWeight: 400,
                                  width: '20px',
                                },
                              }}
                            />
                            <InfoOutlinedIcon sx={{ color: '#374151', fontSize: '10px' }} />
                          </div>
                        </div>
                      </div>
                      <Divider orientation="vertical" flexItem />
                      {level.get('turnAroundTime', '') !== '' && (
                        <>
                          <div className="d-flex align-items-center gap-5">
                            <div className="f-12 fw-400 text-lGrey">TAT:</div>
                            <div className="f-12 fw-400 text-lGrey ">
                              {level.get('turnAroundTime', '')} Days
                            </div>
                          </div>
                          <Divider orientation="vertical" flexItem />
                        </>
                      )}
                      <div className="f-12 fw-400 text-lGrey">{level.get('category', '')}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </Paper>
    </div>
  );
}

export default Step4;
