import React, { useState } from 'react';
import { Checkbox, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import EmailConfigure from 'Modules/GlobalConfiguration/modal/EmailConfigure';
import ReactDatePicker from 'react-datepicker';
import MaterialInput from 'Widgets/FormElements/material/Input';
import * as actions from '_reduxapi/global_configuration/actions';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import { selectMailSettings } from '_reduxapi/global_configuration/selectors';
import { AccordionProgramInput } from '../ReusableComponent';
import { t } from 'i18next';
import { format } from 'date-fns';

const EmailSettings = (props) => {
  const {
    settingId,
    mailSettingDetails,
    getMailSettings,
    updateMailSettings,
    accordionOpen,
    setAccordionOpen,
    setAccordionHeader,
    institutionHeader,
    type,
  } = props;
  const [emailOpen, setEmailOpen] = useState(false);
  const today = new Date();

  today.setHours(0, 0);
  const handleEmailOpenClose = () => {
    setEmailOpen(false);
  };
  const weeks = (section) =>
    mailSettingDetails
      .getIn([section, 'weeks'], List())
      .toJS()
      .map((day) => day.labelName);

  const handleChangeRemainder = (e) => {
    const { name, value, checked } = e.target;
    const payload = mailSettingDetails.get('remainderSection', {}).toJS();
    payload.type = 'remainderSection';
    const weeks = mailSettingDetails.getIn(['remainderSection', 'weeks'], List()).toJS();
    const weekdays = weeks.map((day) => day.labelName);
    if (name === 'weekday') {
      if (checked) {
        if (!weekdays.includes(value)) weekdays.push(value);
      } else if (!checked) {
        const index = weekdays.findIndex((weekday) => weekday === value);
        index > -1 && weekdays.splice(index, 1);
      }
      payload.weeks = weekdays;
    } else if (name === 'isActive') {
      payload.isActive = checked;
    } else {
      payload[name] = value;
    }
    payload.weeks = weekdays;
    if (value !== '')
      updateMailSettings({
        settingId: settingId,
        payload: payload,
        headers: institutionHeader,
        type,
      });
  };
  const handleChangeRemainderDate = (date) => {
    const payload = mailSettingDetails.get('remainderSection', {}).toJS();
    payload.type = 'remainderSection';
    const weeks = mailSettingDetails.getIn(['remainderSection', 'weeks'], List()).toJS();
    const weekdays = weeks.map((day) => day.labelName);
    payload.weeks = weekdays;

    payload.time = new Date(date).toLocaleTimeString('en-us', {
      hour: '2-digit',
      minute: '2-digit',
    });
    updateMailSettings({
      settingId: settingId,
      payload: payload,
      headers: institutionHeader,
      type,
    });
  };

  const handleChangeInvalid = (e) => {
    const { name, value, checked } = e.target;
    const payload = mailSettingDetails.get('invalidProfileSection', {}).toJS();
    payload.type = 'invalidProfileSection';
    const weeks = mailSettingDetails.getIn(['invalidProfileSection', 'weeks'], List()).toJS();
    const weekdays = weeks.map((day) => day.labelName);
    if (name === 'weekday') {
      if (checked) {
        if (!weekdays.includes(value)) weekdays.push(value);
      } else if (!checked) {
        const index = weekdays.findIndex((weekday) => weekday === value);
        index > -1 && weekdays.splice(index, 1);
      }
      payload.weeks = weekdays;
    } else if (name === 'isActive') {
      payload.isActive = checked;
    } else {
      payload[name] = value;
    }
    payload.weeks = weekdays;

    if (value !== '')
      updateMailSettings({
        settingId: settingId,
        payload: payload,
        headers: institutionHeader,
        type,
      });
  };

  const handleChangeInvalidDate = (date) => {
    const payload = mailSettingDetails.get('invalidProfileSection', {}).toJS();
    payload.type = 'invalidProfileSection';
    const weeks = mailSettingDetails.getIn(['invalidProfileSection', 'weeks'], List()).toJS();
    const weekdays = weeks.map((day) => day.labelName);
    payload.weeks = weekdays;
    payload.time = new Date(date).toLocaleTimeString('en-us', {
      hour: '2-digit',
      minute: '2-digit',
    });
    updateMailSettings({
      settingId: settingId,
      payload: payload,
      headers: institutionHeader,
      type,
    });
  };

  const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const DetailsComponent = () => {
    return (
      <div className="container">
        <div className="digi-pl-12">
          <div className="row align-items-center ">
            <div className="col-md-5 col-5 col-xl-3 bold">
              <p className="pt-2">
                {' '}
                <Trans i18nKey={'userManagement.request_mail'}></Trans>{' '}
              </p>
            </div>
            <div className="col-md- col-3 col-xl-1 pl-1">
              {mailSettingDetails.size > 0 && (
                <MaterialInput
                  elementType={'materialInput'}
                  type={'number'}
                  variant={'outlined'}
                  size={'small'}
                  defaultValue={mailSettingDetails.get('mailExpiration', 0)}
                  name="mailExpiration"
                  changed={(e) => {
                    handleChangeRemainder(e);
                  }}
                />
              )}
            </div>

            <div className="col-md-2 col-2 col-xl-2 pl-0">
              <p className="text-lightgray pt-3 ml--5">
                <Trans i18nKey={'global_configuration.day_s'} />
              </p>
            </div>
          </div>
          <hr />
          <div className="">
            <p className="bold">
              {' '}
              <Trans i18nKey={'userManagement.set_reminder'}></Trans>{' '}
            </p>
            <FormControlLabel
              control={
                <Checkbox
                  onChange={(e) => handleChangeRemainder(e)}
                  value="isActive"
                  name="isActive"
                  color="primary"
                  checked={mailSettingDetails.getIn(['remainderSection', 'isActive'], false)}
                />
              }
              label={t('userManagement.request_verification')}
            />

            <p className="f-14 mb-2 text-lightgray">
              {' '}
              <Trans i18nKey={'userManagement.recurring'}></Trans>{' '}
            </p>

            <RadioGroup
              row
              aria-label="position"
              onChange={(e) => handleChangeRemainder(e)}
              name="recurring"
              value={mailSettingDetails.getIn(['remainderSection', 'recurring'], '')}
            >
              <FormControlLabel
                value="daily"
                control={<Radio color="primary" />}
                label={t('global_configuration.daily')}
                labelPlacement="end"
              />
              <FormControlLabel
                value="weekly"
                control={<Radio color="primary" />}
                label={t('global_configuration.weekly')}
                labelPlacement="end"
              />
              <FormControlLabel
                value="custom"
                control={<Radio color="primary" />}
                label={t('global_configuration.custom')}
                labelPlacement="end"
              />
            </RadioGroup>

            {mailSettingDetails.size > 0 &&
              mailSettingDetails.getIn(['remainderSection', 'recurring'], '') === 'custom' && (
                <div className="d-flex align-items-center">
                  <p className="pt-2 pr-2">
                    {' '}
                    <Trans i18nKey={'global_configuration.recur_every'}></Trans>
                  </p>
                  <div className="w-12">
                    <MaterialInput
                      elementType={'materialInput'}
                      type={'number'}
                      variant={'outlined'}
                      size={'small'}
                      changed={(e) => handleChangeRemainder(e)}
                      name="recurringInterval"
                      defaultValue={mailSettingDetails.getIn(
                        ['remainderSection', 'recurringInterval'],
                        0
                      )}
                    />
                  </div>
                  <p className="text-lightgray pl-2 pt-3">
                    {' '}
                    <Trans i18nKey={'global_configuration.day_s'} />
                  </p>{' '}
                </div>
              )}

            {mailSettingDetails.size > 0 &&
              mailSettingDetails.getIn(['remainderSection', 'recurring'], '') === 'weekly' && (
                <div className="d-flex align-items-center">
                  {weekdays.map((weekday) => (
                    <FormControlLabel
                      key={weekday}
                      control={
                        <Checkbox
                          checked={weeks('remainderSection').includes(weekday)}
                          color="primary"
                          value={weekday}
                          onChange={(e) => handleChangeRemainder(e)}
                          name="weekday"
                        />
                      }
                      label={weekday}
                    />
                  ))}
                </div>
              )}

            <div className="mt-2">
              <p className="f-14 digi-gray-neutral mb-1">
                {' '}
                <Trans i18nKey={'global_configuration.select_time'}></Trans>{' '}
              </p>
              {mailSettingDetails.size > 0 && (
                <ReactDatePicker
                  selected={
                    new Date(
                      format(today, 'MM/dd/yyyy') +
                        ' ' +
                        mailSettingDetails.getIn(['remainderSection', 'time'], today.toDateString())
                    )
                  }
                  onChange={(date) => handleChangeRemainderDate(date)}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={60}
                  timeCaption="Time"
                  dateFormat="h:mm aa"
                  className="global-date-picker-input icon_date"
                  placeholderText="Select Time"
                />
              )}
            </div>
          </div>
          <hr />

          <div className="">
            <FormControlLabel
              control={
                <Checkbox
                  onChange={(e) => handleChangeInvalid(e)}
                  value="isActive"
                  name="isActive"
                  color="primary"
                  checked={mailSettingDetails.getIn(['invalidProfileSection', 'isActive'], false)}
                />
              }
              label={t('global_configuration.Profile_moved_invalid')}
            />

            <p className="f-14 mb-2 text-lightgray">
              {' '}
              <Trans i18nKey={'userManagement.recurring'}></Trans>{' '}
            </p>

            <RadioGroup
              row
              aria-label="position"
              name="recurring"
              onChange={(e) => handleChangeInvalid(e)}
              value={mailSettingDetails.getIn(['invalidProfileSection', 'recurring'], '')}
            >
              <FormControlLabel
                value="daily"
                control={<Radio color="primary" />}
                label={t('global_configuration.daily')}
                labelPlacement="end"
              />
              <FormControlLabel
                value="weekly"
                control={<Radio color="primary" />}
                label={t('global_configuration.weekly')}
                labelPlacement="end"
              />
              <FormControlLabel
                value="custom"
                control={<Radio color="primary" />}
                label={t('global_configuration.custom')}
                labelPlacement="end"
              />
            </RadioGroup>

            {mailSettingDetails.getIn(['invalidProfileSection', 'recurring'], '') === 'custom' && (
              <div className="d-flex align-items-center">
                <p className="pt-2 pr-2">
                  {' '}
                  <Trans i18nKey={'global_configuration.recur_every'} />{' '}
                </p>
                <div className="w-12">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'number'}
                    variant={'outlined'}
                    size={'small'}
                    changed={(e) => handleChangeInvalid(e)}
                    name="recurringInterval"
                    value={mailSettingDetails.getIn(
                      ['invalidProfileSection', 'recurringInterval'],
                      0
                    )}
                  />
                </div>
                <p className="text-lightgray f-16 pl-2 pt-3">
                  <Trans i18nKey={'global_configuration.day_s'} />
                </p>{' '}
              </div>
            )}

            {mailSettingDetails.size > 0 &&
              mailSettingDetails.getIn(['invalidProfileSection', 'recurring'], '') === 'weekly' && (
                <div className="d-flex align-items-center">
                  {weekdays.map((weekday) => (
                    <FormControlLabel
                      key={weekday}
                      control={
                        <Checkbox
                          checked={weeks('invalidProfileSection').includes(weekday)}
                          color="primary"
                          onChange={(e) => handleChangeInvalid(e)}
                          name="weekday"
                          value={weekday}
                        />
                      }
                      label={weekday}
                    />
                  ))}
                </div>
              )}

            <div className="mt-1">
              <p className="f-14 digi-gray-neutral mb-1">
                {' '}
                <Trans i18nKey={'global_configuration.select_time'} />{' '}
              </p>
              <ReactDatePicker
                selected={
                  new Date(
                    format(today, 'MM/dd/yyyy') +
                      ' ' +
                      mailSettingDetails.getIn(['invalidProfileSection', 'time'], '')
                  )
                }
                onChange={(date) => handleChangeInvalidDate(date)}
                showTimeSelect
                showTimeSelectOnly
                timeIntervals={60}
                timeCaption="Time"
                dateFormat="h:mm aa"
                className="global-date-picker-input icon_date"
                placeholderText="Select Time"
              />
            </div>
          </div>
        </div>
      </div>
    );
  };
  const handleClick = () => {
    settingId &&
      !accordionOpen &&
      !mailSettingDetails.size &&
      getMailSettings({ settingId: settingId, headers: institutionHeader, type });
    setAccordionOpen();
  };
  return (
    <>
      {' '}
      <AccordionProgramInput
        detailChildren={<DetailsComponent />}
        summaryChildren={setAccordionHeader(
          'userManagement.mail_setting',
          t('configured'),
          !accordionOpen
        )}
        onClick={handleClick}
        expanded={accordionOpen || false}
      />
      {emailOpen && (
        <EmailConfigure
          open={emailOpen}
          handleClose={() => handleEmailOpenClose()}
          institutionHeader={institutionHeader}
          type={type}
        />
      )}
      <hr />
    </>
  );
};

EmailSettings.propTypes = {
  displayRow: PropTypes.func,
  handleEmailOpen: PropTypes.func,
  handleEmailOpenClose: PropTypes.func,
  emailOpen: PropTypes.bool,
  mailSettingDetails: PropTypes.instanceOf(Map),
  settingId: PropTypes.string,
  type: PropTypes.string,
  getMailSettings: PropTypes.func,
  updateMailSettings: PropTypes.func,
  accordionOpen: PropTypes.bool,
  setAccordionOpen: PropTypes.func,
  setAccordionHeader: PropTypes.func,
  institutionHeader: PropTypes.object,
};

const mapStateToProps = function (state) {
  return {
    mailSettingDetails: selectMailSettings(state),
  };
};
export default connect(mapStateToProps, actions)(EmailSettings);
