import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import ThemeProvider from '@mui/styles/ThemeProvider';

import { MUI_THEME } from '../../../../utils';

import FilterIcon from '../../../../Assets/sort.png';
import { t } from 'i18next';

function CourseStudentDetailsFilters({
  options,
  filters,
  sort,
  onFilterChange,
  onSortChange,
  resetLabelName = '',
  handleExport,
  isExportActive,
}) {
  function getOptions(type) {
    return options.get(type, List());
  }

  function handleFilterChange(name, value) {
    const updatedFilters = filters.merge(Map({ [name]: value }));
    onFilterChange(updatedFilters);
  }

  function handleSortChange(name, value) {
    const updatedSort = sort.merge(Map({ [name]: value }));
    onSortChange(updatedSort);
  }

  const sortDirection = sort.get('sortDirection');
  return (
    <div className="p-2 border-bottom space_nowrap">
      <div className="col-md-2 ml-auto pr-0">
        <div className="text-center">
          {isExportActive && (
            <ThemeProvider theme={MUI_THEME}>
              <Button variant="contained" color="primary" onClick={handleExport}>
                {t('reports_analytics.export').toUpperCase()}
              </Button>
            </ThemeProvider>
          )}
        </div>
      </div>
      <div className="row text-left">
        <div className="col-md-2">
          <div className="f-14">{t('reports_analytics.student_group').toUpperCase()}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('studentGroup', '')}
              onChange={(e) => handleFilterChange('studentGroup', e.target.value)}
            >
              {getOptions('studentGroups').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>

        <div className="col-md-10">
          <div className="row align-items-end">
            <div className="col-md-1 px-0">
              <div className="course_sort_icon bg-gray mb-1">
                <img src={FilterIcon} alt="sort" className="img-fluid" />
              </div>
            </div>

            <div className="col-md-2 px-2">
              <div className="f-14">{t('reports_analytics.gender').toUpperCase()}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filters.get('gender', '')}
                  onChange={(e) => handleFilterChange('gender', e.target.value)}
                >
                  <option value="">{t('reports_analytics.all')}</option>
                  {getOptions('genders').map((option) => (
                    <option key={option.get('value')} value={option.get('value')}>
                      {option.get('name')}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>

            <div className="col-md-2 px-2">
              <div className="f-14">{t('reports_analytics.warning').toUpperCase()}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filters.get('warning', '')}
                  onChange={(e) => handleFilterChange('warning', e.target.value)}
                >
                  <option value="">{resetLabelName}</option>
                  {getOptions('warnings').map((option) => (
                    <option key={option.get('value')} value={option.get('value')}>
                      {option.get('name')}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>

            <div
              className="col-md-1 px-0 cursor-pointer"
              onClick={() =>
                handleSortChange('sortDirection', sortDirection === 'asc' ? 'desc' : 'asc')
              }
            >
              <div className="course_sort_icon bg-gray">
                <i className={`fas fa-sort-amount-${sortDirection === 'asc' ? 'up' : 'down'}`}></i>
              </div>
            </div>

            <div className="col-md-3 px-2">
              <div className="f-14">{t('reports_analytics.sort_by').toUpperCase()}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={sort.get('sortBy')}
                  onChange={(e) => handleSortChange('sortBy', e.target.value)}
                >
                  {getOptions('sortableColumns').map((column) => (
                    <option key={column.get('value')} value={column.get('value')}>
                      {column.get('name')}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>
            <div className="col-md-3 mt-3 mr-auto px-2">
              <div className="bg-white dash_box_head2 p-1">
                <div className="sb-example-3">
                  <div className="search">
                    <input
                      type="text"
                      className="searchTerm pl-3"
                      placeholder={'Search Student Name, ID'}
                      value={filters.get('searchInput', '')}
                      onChange={(e) => handleFilterChange('searchInput', e.target.value)}
                    />
                    <button type="submit" className="searchButton">
                      <i className="fa fa-search"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

CourseStudentDetailsFilters.propTypes = {
  options: PropTypes.instanceOf(Map),
  filters: PropTypes.instanceOf(Map),
  sort: PropTypes.instanceOf(Map),
  onFilterChange: PropTypes.func,
  onSortChange: PropTypes.func,
  resetLabelName: PropTypes.string,
  handleExport: PropTypes.func,
  isExportActive: PropTypes.bool,
};

export default CourseStudentDetailsFilters;
