import React from 'react';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import { ThemeProvider } from '@mui/styles';
import { getFormattedGroupName } from '../components/utils';
import { isIndGroup, MUI_THEME } from '../../../utils';
import { Dialog, DialogActions } from '@mui/material';

function BreakSessionFlow({ show, onHide, schedule, onConfirm }) {
  function getGroupName() {
    return schedule
      .get('student_groups', List())
      .map((studentGroup) => {
        const studentGroupName = getFormattedGroupName(
          studentGroup.get('group_name', ''),
          isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
        );
        const sessionGroupNames = studentGroup
          .get('session_group', List())
          .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
          .join(', ');
        return `${studentGroupName} - ${sessionGroupNames}`;
      })
      .join(', ');
  }

  return (
    <Dialog fullWidth={true} maxWidth={'md'} open={show} onClose={onHide}>
      <div className="p-4">
        <div>
          <p className="mb-3 f-17 bold">Break Session Flow</p>
        </div>
        <div>
          <p className="mb-2 f-15 bold">{`${getGroupName()} is breaking the session order`}</p>
          <p className="mb-2 f-15 text-justify">
            Scheduling by breaking the session flow can affect the course learning outcomes.
            Instead, you can change the medium of instruction to online.
          </p>
          <p className="mb-2 f-15 ">Do you still want to break the session flow?</p>
        </div>
        <DialogActions className="mt-3">
          <div className="container">
            <div className="row">
              <div className="col-md-12">
                <div className="float-right">
                  <ThemeProvider theme={MUI_THEME}>
                    <span className="pr-3">
                      <Button color="primary" variant="outlined" onClick={() => onHide(false)}>
                        CANCEL
                      </Button>
                    </span>
                    <span>
                      <Button color="primary" variant="contained" onClick={onConfirm}>
                        SCHEDULE
                      </Button>
                    </span>
                  </ThemeProvider>
                </div>
              </div>
            </div>
          </div>
        </DialogActions>
      </div>
    </Dialog>
  );
}

BreakSessionFlow.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  schedule: PropTypes.instanceOf(Map),
  onConfirm: PropTypes.func,
};

export default BreakSessionFlow;
