import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Button, Form } from 'react-bootstrap';
import { List, Map } from 'immutable';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import * as actions from '../../../../_reduxapi/program_input/action';
import { selectMappingTree } from '../../../../_reduxapi/program_input/selectors';
import { Trans, withTranslation } from 'react-i18next';
import { getLang, indVerRename, getURLParams } from '../../../../utils';
const lang = getLang();
class PloCloMappingTree extends Component {
  constructor(props) {
    super(props);
    this.state = {
      program_id: getURLParams('_id', true),
      program_name: getURLParams('_n', true),
      curriculum_id: getURLParams('_curriculum_id', true),
      curriculum_name: getURLParams('_curriculum_name', true),
      course_id: getURLParams('_course_id', true),
      course_name: getURLParams('_course_name', true),
      framework_name: getURLParams('_framework_name', true),
      mapping_type: getURLParams('_mapping_type'),
      isFrameworkAssignedInCurriculum:
        getURLParams('_is_framework_assigned_in_curriculum') === 'true',
    };
  }

  handleBack() {
    this.props.history.goBack();
  }

  componentDidMount() {
    const { program_id, curriculum_id, course_id, isFrameworkAssignedInCurriculum } = this.state;
    this.props.getMappingTree({
      ...(isFrameworkAssignedInCurriculum && {
        program_id: program_id,
        curriculum_id: curriculum_id,
      }),
      course_id: course_id,
    });
  }

  getTransformedMappingTree() {
    const { mappingTree } = this.props;
    return mappingTree.map((program) => {
      const isDefault = program
        .get('curriculums')
        .every((curriculum) => curriculum.get('isDefault'));
      const isSelected = program
        .get('curriculums')
        .every((curriculum) => curriculum.get('isSelected'));
      return program.set('isDefault', isDefault).set('isSelected', isSelected);
    });
  }

  handleProgramChange(e, programId) {
    const checked = e.target.checked;
    const { mappingTree, setData } = this.props;
    const programIndex = mappingTree.findIndex((program) => program.get('_id') === programId);
    if (programIndex === -1) return;
    const curriculums = mappingTree
      .getIn([programIndex, 'curriculums'], List())
      .map((curriculum) =>
        curriculum.get('isDefault') ? curriculum : curriculum.set('isSelected', checked)
      );
    setData(
      Map({
        mappingTree: mappingTree.setIn([programIndex, 'curriculums'], curriculums),
      })
    );
  }

  handleCurriculumChange(e, programId, curriculumId) {
    const checked = e.target.checked;
    const { mappingTree, setData } = this.props;
    const programIndex = mappingTree.findIndex((program) => program.get('_id') === programId);
    if (programIndex === -1) return;
    const curriculumIndex = mappingTree
      .getIn([programIndex, 'curriculums'])
      .findIndex((curriculum) => curriculum.get('_id') === curriculumId);
    if (curriculumIndex === -1) return;
    setData(
      Map({
        mappingTree: mappingTree.setIn(
          [programIndex, 'curriculums', curriculumIndex, 'isSelected'],
          checked
        ),
      })
    );
  }

  disableProceed() {
    const { mappingTree } = this.props;
    let disable = true;
    mappingTree.forEach((program) => {
      if (!disable) return;
      program.get('curriculums', List()).forEach((curriculum) => {
        if (!disable) return;
        if (curriculum.get('isSelected')) disable = false;
      });
    });
    return disable;
  }

  proceedToMapping = () => {
    const { mappingTree, getMapCLOPLO, history, location } = this.props;
    const { course_id } = this.state;
    getMapCLOPLO(
      {
        programs: mappingTree.toJS(),
        course_id,
      },
      null,
      () => {
        history.replace(`/program-input/frameworks/plo-clo-map/${location.search}`);
      }
    );
  };

  render() {
    const {
      curriculum_name,
      framework_name,
      course_name,
      program_name,
      isFrameworkAssignedInCurriculum,
    } = this.state;
    const { t } = this.props;
    const isProceedDisabled = this.disableProceed();
    const programId = getURLParams('_id', true);
    return (
      <div className="main bg-gray pb-5">
        <div className="bg-white pt-3 pb-3">
          <div className="container-fluid">
            <p className="font-weight-bold mb-0 text-left f-17">
              <i
                className="fa fa-arrow-left pr-3 cursor-pointer"
                aria-hidden="true"
                onClick={() => this.handleBack()}
              ></i>
              <Trans i18nKey={'map'}></Trans> {''}
              <Trans
                i18nKey={'plo_clo'}
                values={{
                  CLO: indVerRename('CLO', programId),
                  PLO: indVerRename('PLO', programId),
                }}
              ></Trans>
            </p>
          </div>
        </div>

        <div className="bg-gray">
          <div className="container-fluid">
            <div className="row pb-4 justify-content-center">
              <div className="col-md-10">
                <div className="p-4">
                  <div className="d-flex justify-content-between">
                    <h5 className="pb-2 pt-2">
                      {program_name} / {framework_name} / {curriculum_name} / {course_name} /
                      <Trans i18nKey={'map'}></Trans> {''}
                      <Trans
                        i18nKey={'plo_clo'}
                        values={{
                          CLO: indVerRename('CLO', programId),
                          PLO: indVerRename('PLO', programId),
                        }}
                      ></Trans>
                    </h5>
                  </div>

                  <div className="pb-4">
                    <div className="bg-white border-radious-8 p-3">
                      <p className={`f-16 mb-2 ${lang === 'ar' ? 'text-left' : ''}`}>
                        <Trans i18nKey={'selected_course'}></Trans> : <b>{course_name}</b>
                      </p>

                      <div className="bg-gray rounded p-1">
                        <div className="p-3">
                          <div className="program_circle_header">
                            <div className="justify-content-center p-3 row">
                              <div className="digi-font-500 f-16 text-skyblue d-flex">
                                <div className="mr-3 digi-circle-data circle-active">1</div>
                                <Trans i18nKey={'selected_mapping'}></Trans> &amp;{' '}
                                <Trans i18nKey={'mapping_type'}></Trans>
                              </div>
                              <span className="digi-bdr"></span>
                              <div className="f-16 digi-brown bold d-flex">
                                <div className="mr-3 digi-circle-data">2</div>
                                <Trans
                                  i18nKey={'clo_plo_mapping'}
                                  values={{
                                    CLO: indVerRename('CLO', programId),
                                    PLO: indVerRename('PLO', programId),
                                  }}
                                ></Trans>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="pl-2 pr-2 pb-2">
                          <p
                            className={`bg-white rounded p-2 mb-0 bold font-italic ${
                              lang === 'ar' ? 'text-left' : ''
                            }`}
                          >
                            {isFrameworkAssignedInCurriculum
                              ? `${t('master_graph.tip')} : As ${course_name} ${t(
                                  'master_graph.is_under'
                                )}  ${curriculum_name} ${t('master_graph.with_plo', {
                                  PLO: indVerRename('PLO', programId),
                                })} `
                              : `${t('master_graph.tip')}: ${t('master_graph.to_clo', {
                                  CLO: indVerRename('CLO', programId),
                                })}  ${program_name}  ${t('master_graph.year_program')}`}
                          </p>
                        </div>

                        <div className="p-3">
                          <div className={`digi-font-500 f-16 ${lang === 'ar' ? 'text-left' : ''}`}>
                            <span className="mr-2 digi-circle-data2 circle-active">1</span>
                            <Trans i18nKey={'selected_mapping'}></Trans> &amp;{' '}
                            <Trans i18nKey={'mapping_type'}></Trans>
                          </div>
                          <div className="pl-2 pt-3 pb-2">
                            {this.getTransformedMappingTree().map((program) => {
                              return (
                                <>
                                  <div className={`pl-4 mt-2 ${lang === 'ar' ? 'text-left' : ''}`}>
                                    <Form.Check
                                      type="checkbox"
                                      label={program.get('name')}
                                      className="ml-2 bold"
                                      checked={program.get('isSelected')}
                                      disabled={program.get('isDefault')}
                                      onChange={(e) => {
                                        if (
                                          CheckPermission(
                                            'pages',
                                            'Program Input',
                                            'Programs',
                                            'Add Program'
                                          ) ||
                                          CheckPermission(
                                            'pages',
                                            'Program Input',
                                            'Programs',
                                            'Add Pre-requisite'
                                          )
                                        ) {
                                          this.handleProgramChange(e, program.get('_id'));
                                        }
                                      }}
                                    />
                                    {program.get('curriculums').map((curriculum) => {
                                      return (
                                        <div
                                          key={curriculum.get('_id')}
                                          className={` mt-2 ${lang === 'ar' ? 'pr-4' : 'pl-4'}`}
                                        >
                                          <Form.Check
                                            type="checkbox"
                                            label={
                                              <span>
                                                <i
                                                  className={`fa-rotate-90 text-gray ml-2 mr-2 ${
                                                    lang === 'ar'
                                                      ? 'fa fa-level-down pb-2'
                                                      : 'fa fa-level-up'
                                                  }`}
                                                  aria-hidden="true"
                                                ></i>
                                                <span className="mr-3">
                                                  {curriculum.getIn(['framework', 'name'], 'NA')}
                                                </span>
                                                <span>{curriculum.get('curriculum_name')}</span>
                                              </span>
                                            }
                                            className="ml-2 bold"
                                            disabled={curriculum.get('isDefault')}
                                            checked={curriculum.get('isSelected')}
                                            onChange={(e) => {
                                              if (
                                                CheckPermission(
                                                  'pages',
                                                  'Program Input',
                                                  'Programs',
                                                  'Add Program'
                                                ) ||
                                                CheckPermission(
                                                  'pages',
                                                  'Program Input',
                                                  'Programs',
                                                  'Add Pre-requisite'
                                                )
                                              ) {
                                                this.handleCurriculumChange(
                                                  e,
                                                  program.get('_id'),
                                                  curriculum.get('_id')
                                                );
                                              }
                                            }}
                                          />
                                        </div>
                                      );
                                    })}
                                  </div>
                                </>
                              );
                            })}
                          </div>
                          <div className={`pt-3 pb-3 ${lang === 'ar' ? 'text-left' : ''}`}>
                            <Button
                              variant={isProceedDisabled ? 'secondary' : 'primary'}
                              onClick={() => this.proceedToMapping()}
                              disabled={isProceedDisabled}
                            >
                              <Trans i18nKey={'step_2'}></Trans>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

PloCloMappingTree.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  getMappingTree: PropTypes.func,
  mappingTree: PropTypes.instanceOf(List),
  getMapCLOPLO: PropTypes.func,
  setData: PropTypes.func,
  t: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    mappingTree: selectMappingTree(state),
  };
};

export default compose(
  withRouter,
  withTranslation(),
  connect(mapStateToProps, actions)
)(PloCloMappingTree);
