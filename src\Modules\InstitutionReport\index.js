import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/session_tracking_report/action';
import { selectLoading, selectMessage } from '_reduxapi/session_tracking_report/selectors';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
import Dashboard from './components/Dashboard';
import './css/style.css';

function InstitutionSessionReport(props) {
  const { isLoading, message } = props;
  return (
    <React.Fragment>
      {message !== '' && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>{'Curricular Monitoring'}</Breadcrumb>
      <Dashboard />
    </React.Fragment>
  );
}

InstitutionSessionReport.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectLoading(state),
    message: selectMessage(state),
  };
};

export default connect(mapStateToProps, actions)(InstitutionSessionReport);
