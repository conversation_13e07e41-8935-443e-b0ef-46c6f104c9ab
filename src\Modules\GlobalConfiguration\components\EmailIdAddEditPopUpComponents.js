import MaterialInput from 'Widgets/FormElements/material/Input';
import React from 'react';
import { checkIndividualData } from 'Modules/UserManagement/v2/StaffManagement/STutils';

export const BodyInputReuse = (value, placeholder, changedValue, label, type = 'text') => {
  return (
    <div className="digi-pb-12">
      <MaterialInput
        elementType={'materialInput'}
        value={value}
        MaterialInput
        type={type}
        variant={'outlined'}
        size={'small'}
        placeholder={placeholder}
        changed={(e) => {
          changedValue(e.target.value);
        }}
        label={<div>{label}</div>}
      />
    </div>
  );
};
export const EditPopUpValidation = (data, setData) => {
  let totalFunctionCount = 9;
  let error = [];
  return data.every(
    (staff) =>
      !checkIndividualData(
        'Display name',
        staff.emailIdConfig.displayName,
        setData,
        ['emptyCheckWithOutLabel'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'From Email',
        staff.emailIdConfig.fromEmail,
        setData,
        ['emptyCheckWithOutLabel', 'emailWithName'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'To Email',
        staff.emailIdConfig.toEmail,
        setData,
        ['emptyCheckWithOutLabel', 'emailWithName'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'EmailDuplicate',
        staff.emailIdConfig.bothEmail,
        setData,
        ['EmailDuplicate'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'Password',
        staff.emailIdConfig.password,
        setData,
        ['emptyCheckWithOutLabel', 'password'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'RE-Enter Password',
        staff.emailIdConfig.reEnterPassword,
        setData,
        ['emptyCheckWithOutLabel', 'password'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'passwordDuplicate',
        staff.emailIdConfig.bothPassword,
        setData,
        ['passwordDuplicate'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'Smtp Client',
        staff.emailIdConfig.smtpClient,
        setData,
        ['emptyCheckWithOutLabel'],
        false,
        '',
        error
      ) &&
      !checkIndividualData(
        'PortNumber',
        staff.emailIdConfig.portNumber,
        setData,
        ['emptyCheckWithOutLabel'],
        false,
        '',
        error,
        totalFunctionCount
      )
  );
};
