import React from 'react';
import { Map, List } from 'immutable';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import PropTypes from 'prop-types';
import { Dialog, DialogTitle, IconButton, Switch } from '@mui/material';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { useMemo } from 'react';

export const levelTypes = [
  ['Program/Department', 'Program/Department'],
  ['Custom', 'Custom'],
];
export const categories = [
  // ['User Based', 'User Based'],
  ['Role Based', 'Role Based'],
];

export const approvalConfigurations = (users) => {
  return [
    {
      name: 'Approval Required From Any One User',
      value: 'Approval Required From Any One User',
    },
    users.size > 1 && {
      name: 'Approval Required From All Users',
      value: 'Approval Required From All Users',
    },
  ];
};

export const approvalRoleConfigurations = (roles) => {
  return [
    {
      name: 'Approval Required From Any One In Any Role',
      value: 'Approval Required From Any One In Any Role',
    },
    roles.size > 1 && {
      name: 'Approval Required From Any One In Each Role',
      value: 'Approval Required From Any One In Each Role',
    },
  ];
};

export const turnAroundTime = [
  {
    name: '1 Days',
    value: 1,
  },
  {
    name: '2 Days',
    value: 2,
  },
  {
    name: '3 Days',
    value: 3,
  },
  {
    name: '4 Days',
    value: 4,
  },
  {
    name: '5 Days',
    value: 5,
  },
  {
    name: '6 Days',
    value: 6,
  },
  {
    name: '7 Days',
    value: 7,
  },
  {
    name: '8 Days',
    value: 8,
  },
  {
    name: '9 Days',
    value: 9,
  },
  {
    name: '10 Days',
    value: 10,
  },
];

export const warningTypeName = [
  'First Warning',
  'Second Warning',
  'Third Warning',
  'Fourth Warning',
  'Fifth Warning',
  'Sixth Warning',
  'Seventh Warning',
  'Eighth Warning',
  'Ninth Warning',
  'Tenth Warning',
];

export const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.white,
    color: theme.palette.common.black,
    fontWeight: '100',
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

export const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

export const uniqueData = (data, type) => {
  return data.filter(
    (value, currentIndex) =>
      data.findIndex(
        (i) =>
          i.get(type, '').toString().toLowerCase() === value.get(type, '').toString().toLowerCase()
      ) !== currentIndex
  );
};

export const checkTypeNameExists = (permissionTypes) => {
  let currentTypeNames = [];
  let existingTypeNames = [];
  //eslint-disable-next-line
  permissionTypes.filter((type) => {
    if (type.get('edited', ''))
      currentTypeNames.push(type.get('typeName', '').toLowerCase().trim());
    else existingTypeNames.push(type.get('typeName', '').toLowerCase().trim());
  });
  if (
    existingTypeNames.some((exist) => currentTypeNames.includes(exist)) ||
    currentTypeNames.filter((item, index) => currentTypeNames.indexOf(item) !== index).length !== 0
  )
    return true;
};

export const warningValidation = (warningTypes, setData, activeIndex, isComprehenivive) => {
  const warningTypesData = warningTypes.shift();
  const labelNameEmpty = warningTypesData.some((item) => !item.get('labelName', ''));
  const percentageEmpty = warningTypesData.some((item) => !item.get(isComprehenivive, ''));
  const uniqueName = uniqueData(warningTypesData, 'labelName');
  const uniquePercentage = uniqueData(warningTypesData, isComprehenivive);
  const uniqueColorCode = uniqueData(warningTypesData, 'colorCode');
  const notificationRoleIds = warningTypes.getIn([activeIndex, 'notificationRoleIds'], List());
  const isManual = warningTypes.getIn([activeIndex, 'notificationToStudent', 'setType'], '');
  const sendNotificationAuthority = warningTypes.getIn(
    [activeIndex, 'notificationToStudent', 'sendNotificationAuthority'],
    List()
  );
  if (isManual === 'manual')
    if (sendNotificationAuthority.size === 0)
      return setData(Map({ message: 'Please select who can send notification' }));

  let errorMessage = '';
  if (labelNameEmpty) errorMessage = 'Label Name is required';
  else if (percentageEmpty) errorMessage = `${isComprehenivive} is required`;
  else if (uniqueName.size !== 0) errorMessage = 'Label Name must be unique';
  else if (uniquePercentage.size !== 0) errorMessage = `${isComprehenivive} must be unique`;
  else if (uniqueColorCode.size !== 0) errorMessage = 'Color code must be unique';
  else if (warningTypes.getIn([activeIndex, 'isAdditionStaffNotify'], false))
    if (notificationRoleIds.size === 0) errorMessage = 'Role is required';

  if (errorMessage) {
    setData(Map({ message: errorMessage }));
    return false;
  }
  return true;
};

export const warningDetailValidation = (warningTypes, setData, activeIndex) => {
  let errorMessage = '';
  const lastIndex = warningTypes.size - 1;

  const notificationRoleIds = warningTypes.getIn([activeIndex, 'notificationRoleIds'], List());
  const totalPercentage = warningTypes.getIn([activeIndex, 'percentage'], '');
  const overallPercentage = warningTypes
    .getIn([activeIndex, 'categoryWisePercentage'], List())
    .reduce((acc, item) => acc + Number(item.get('percentage', false)), 0);
  if (warningTypes.getIn([activeIndex, 'isAdditionStaffNotify'], false)) {
    if (notificationRoleIds.size === 0) errorMessage = 'Role is required';
  }
  if (
    warningTypes.getIn([activeIndex, 'denialCondition'], '') === 'Individual' &&
    lastIndex === activeIndex &&
    Number(totalPercentage) !== Number(overallPercentage)
  ) {
    errorMessage = `Percentage must be ${totalPercentage}`;
  }

  if (lastIndex === activeIndex) {
    if (
      !warningTypes.getIn([activeIndex, 'unappliedLeaveConsideredAs'], '') &&
      warningTypes.getIn([activeIndex, 'denialCondition'], '') === 'Individual'
    )
      errorMessage = 'Category is required';
    if (warningTypes.getIn([activeIndex, 'denialManagement', 'accessType'], '') === 'role') {
      if (warningTypes.getIn([activeIndex, 'denialManagement', 'roleIds'], '').size === 0)
        errorMessage = 'Role is required';
    } else {
      if (warningTypes.getIn([activeIndex, 'denialManagement', 'userIds'], '').size === 0)
        errorMessage = 'User is required';
    }
  }
  if (errorMessage) {
    setData(Map({ message: errorMessage }));
    return false;
  }
  return true;
};

export const permissionValidation = (item, setData) => {
  let errorMessage = '';
  if (!item.get('level', '')) errorMessage = 'Level Name is required';
  else {
    if (item.get('category', '') === 'Role Based') {
      if (item.get('role', List()).size === 0) errorMessage = 'Role is required';
    } else {
      if (item.get('user', List()).size === 0) errorMessage = 'User is required';
    }
  }
  if (errorMessage) {
    setData(Map({ message: errorMessage }));
    return false;
  }
  return true;
};

export const getLeaveTypeName = (type, isRolesAndPermission) => {
  if (isRolesAndPermission)
    return ['on_duty', 'On Duty'].includes(type)
      ? 'On Duty'
      : ['permission', 'Permission'].includes(type)
      ? 'Permission'
      : 'Leave';
  const leaveType = type === 'on_duty' ? 'On-Duty' : type === 'permission' ? 'Permission' : 'Leave';
  return leaveType;
};

export const checkPermissionSub = (SubTabAction = 'View', type) => {
  return CheckPermission(
    'subTabs',
    'Leave Management',
    'Leave Settings',
    '',
    'Student LMS',
    '',
    getLeaveTypeName(type, true),
    SubTabAction
  );
};

export const checkingIfOnlyViewEnabled = (type) => {
  if (type === 'Permission' || type === 'On Duty') {
    if (checkPermissionSub('View', type)) {
      if (
        !checkPermissionSub('General Configuration View', type) &&
        !checkPermissionSub('General Configuration Edit', type) &&
        !checkPermissionSub(`${type} Configuration View`, type) &&
        !checkPermissionSub(`${type} Configuration Edit`, type) &&
        !checkPermissionSub(`${type} Approval Levels and Roles View`, type) &&
        !checkPermissionSub(`${type} Approval Levels and Roles Edit`, type)
      ) {
        return false;
      } else {
        return true;
      }
    }
  }

  if (type === 'Leave') {
    if (checkPermissionSub('View', type)) {
      if (
        !checkPermissionSub('General View', type) &&
        !checkPermissionSub('General Edit', type) &&
        !checkPermissionSub('Leave Classifications View', type) &&
        !checkPermissionSub('Leave Classifications Edit', type) &&
        !checkPermissionSub('Warnings & Notifications View', type) &&
        !checkPermissionSub('Warnings & Notifications Edit', type) &&
        !checkPermissionSub('Leave Policy Documents View', type) &&
        !checkPermissionSub('Leave Policy Documents Edit', type)
      ) {
        return false;
      } else {
        return true;
      }
    }
  }
};

export const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(2),
    minWidth: '550px',
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));

export function BootstrapDialogTitle(props) {
  const { children, onClose, ...other } = props;

  return (
    <DialogTitle sx={{ m: 0, p: 2 }} {...other}>
      {children}
      {onClose ? (
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      ) : null}
    </DialogTitle>
  );
}

BootstrapDialogTitle.propTypes = {
  children: PropTypes.node,
  onClose: PropTypes.func.isRequired,
};

export const IOSSwitch = styled((props) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 36,
  height: 21,
  padding: 0,
  '& .MuiSwitch-switchBase': {
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&.Mui-checked': {
      transform: 'translateX(16px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: theme.palette.mode === 'dark' ? '#47b92a' : '#47b92a',
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-focusVisible .MuiSwitch-thumb': {
      color: '#147AFC',
      border: '6px solid #fff',
    },
    '&.Mui-disabled .MuiSwitch-thumb': {
      color: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600],
    },
    '&.Mui-disabled + .MuiSwitch-track': {
      opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 17,
    height: 17,
  },
  '& .MuiSwitch-track': {
    borderRadius: 26 / 2,
    backgroundColor: '#C4C4C4',
    opacity: 1,
    transition: theme.transitions.create(['background-color'], {
      duration: 500,
    }),
  },
}));

export const BpIcon = styled('span')(({ theme }) => ({
  'input:disabled ~ &': {
    background: '#f1f1f1',
    outline: '2px auto #black',
    outlineOffset: 2,
    borderRadius: 3,
    width: 16,
    height: 16,
    boxShadow:
      theme.palette.mode === 'dark'
        ? '0 0 0 1px rgb(16 22 26 / 40%)'
        : 'inset 0 0 0 1px rgba(16,22,26,.2), inset 0 -1px 0 rgba(16,22,26,.1)',
    backgroundColor: theme.palette.mode === 'dark' ? '#394b59' : '#f5f8fa',
    backgroundImage:
      theme.palette.mode === 'dark'
        ? 'linear-gradient(180deg,hsla(0,0%,100%,.05),hsla(0,0%,100%,0))'
        : 'linear-gradient(180deg,hsla(0,0%,100%,.8),hsla(0,0%,100%,0))',
  },
}));

export const warningName = (warningTypes, i) => {
  const typeName = warningTypeName.find((_, index) => index === i - 1);
  const lastIndex = warningTypes.size - 1;
  return lastIndex !== i ? typeName : 'Denial';
};

export const leaveTabOptions = Object.freeze({
  General: 'General',
  'Leave Classifications': 'Leave Classifications',
  'Warnings & Notifications': 'Warnings & Notifications',
  'Leave Policy Documents': 'Leave Policy Documents',
  'Attendance Configuration': 'Attendance Configuration',
});

export const iscomprehensiveMode = (warningMode) => {
  const isComprehensive = warningMode === 'comprehensive' ? 'warningValue' : 'percentage';
  return isComprehensive;
};

//hooks
export const useFilteredOptions = (isComprehensive) => {
  const radioOptions = [
    ['Automatic', 'automatic'],
    ['Manual', 'manual'],
  ];
  const filteredOptions = useMemo(() => {
    return isComprehensive === 'warningValue'
      ? radioOptions.filter((option) => option[1] !== 'manual') // Remove Manual
      : radioOptions;
  }, [isComprehensive]);

  return filteredOptions;
};

export const useIsWarningValue = (isComprehensive) => {
  return isComprehensive === 'warningValue';
};
