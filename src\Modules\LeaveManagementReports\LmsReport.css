.lms-btn-color-Approved {
  color: #16a34a;
  background-color: #dcfce7;
}

.lms-btn-color-Pending {
  color: #d97706;
  background-color: #fef3c7;
}

.lms-btn-color-Withdrawn {
  color: #4f46e5;
  background-color: #e0e7ff;
}

.lms-btn-color-Rejected {
  color: #ef4444;
  background-color: #fee2e2;
}

.lms-color-Approved {
  color: #16a34a;
}

.lms-color-Pending {
  color: #d97706;
}

.lms-color-Withdrawn {
  color: #4f46e5;
}

.lms-color-Rejected {
  color: #ef4444;
}

.table_wrapper {
  display: block;
}

.position-Ab {
  position: absolute;
}

.table--xy {
  position: relative;
  overflow-x: auto;
  overflow-y: auto;
}

.table--xy::-webkit-scrollbar {
  height: 6px;
  width: 5px;
}

.table--xy::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.table--xy::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.table-height {
  height: 30em;
}

.table-height-41vh {
  height: 41vh;
}

.table-heights {
  height: 11em;
}

.back-color {
  background-color: #eff9fb;
}

.header-sticky {
  position: sticky;
  top: 0px;
  background-color: #ffffff;
}

.LmsTable .LmsBody {
  display: block;
  max-height: 160px;
  overflow-y: scroll;
}

.LmsTableContainer table thead,
.LmsTableContainer table tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.LmsTableContainer table tbody td {
  border: none !important;
}

.LmsTableContainer table thead th {
  border: none !important;
}

.LmsTableContainer table thead.LmsHeader {
  position: sticky;
  top: 0px;
  background-color: #eff9fb;
}

.LmsTable .LmsBody::-webkit-scrollbar {
  height: 6px;
  width: 5px;
}

.LmsTable .LmsBody::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.LmsTable .LmsBody::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.TextEllipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

:-webkit-scrollbar {
  height: 6px;
  width: 5px;
}

:-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

:-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.color-tooltip {
  background-color: #374151;
}

.ToolTipColorBorder {
  border-top: 3px solid #1f2937;
  border-bottom: 3px solid #374151;
  border-radius: 8px;
}

.tooltip-head-color {
  background-color: #1f2937;
}

.Dialog_scroll_lms {
  overflow: auto;
}

.Dialog_scroll_lms::-webkit-scrollbar {
  height: 6px;
  width: 10px;
}

.Dialog_scroll_lms::-webkit-scrollbar-thumb {
  background: #e2dbde;
  border-radius: 10px;
}

.Dialog_scroll_lms::-webkit-scrollbar-thumb:hover {
  background: #e2dbde;
}

.error-boundary-reports {
  background: white;
  border-radius: 10px;
  text-align: center;
  padding: 10px;
  box-shadow: 10px 10px 5px #aaaa;
}

.textLmsWarning {
  color: #ff7f00;
}

.textLmsWarningOrg {
  color: #ffb100;
}

.LmsWarningOrg-CardBorder {
  border: 1px solid #ffb100;
}

.LmsReport-TableData-Ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 12em;
}

.LmsReport-content-Width {
  width: fit-content;
}

.Not-Acknowledged-BgColor {
  background-color: #f3f4f6;
  color: #4b5563;
  width: fit-content;
}

.Pending-Acknowledged-BgColor {
  background-color: #fef2f2;
  color: #ef4444;
  width: fit-content;
}

.Acknowledged-BgColor {
  background-color: #f0fdf4;
  color: #16a34a;
  width: fit-content;
}

.Lms-Warning-headerSticky {
  position: sticky;
  top: 0px;
  right: 0px;
  left: 0px;
}

.Warning-status-Lms-Report {
  background-color: #fffbeb;
  color: #f59e0b;
}

.Warning-popup-BgColor {
  background-color: #fffbeb;
}

.Warning-table-BgColor {
  background-color: #f7fcfd;
}

.Warning-table-container {
  max-height: 240px;
}

.Warning-table-header-sticky {
  position: sticky;
  top: 0;
  background-color: #f7fcfd;
  font-weight: bold;
}

.LmsReport-content-Width {
  width: fit-content;
}

.lms-table-height {
  max-height: 240px;
  overflow-y: auto;
}

.Lms-header-fixed {
  position: sticky;
  top: 100px;
  background-color: #eff9fb;
  z-index: 1;
}

.digi-gray-total {
  color: #6b7280;
}

.Lms-data-width {
  width: 5em;
}

.header_modal {
  position: sticky;
  top: 0px;
  background: white;
  z-index: 9;
}

.LmsModal-table-height {
  max-height: 302px;
  overflow-y: auto;
}

.chart_no_data_found {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart_no_data_found_sub {
  width: 95%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}