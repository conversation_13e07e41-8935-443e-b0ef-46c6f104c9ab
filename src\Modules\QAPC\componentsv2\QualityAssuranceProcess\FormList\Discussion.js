import React, { useState, useEffect, useRef } from 'react';
import { Map } from 'immutable';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';

import { Box, Avatar, OutlinedInput, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary, { accordionSummaryClasses } from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import InputAdornment from '@mui/material/InputAdornment';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import SendIcon from '@mui/icons-material/Send';
import DownloadIcon from '@mui/icons-material/Download';
import { Divider } from '@mui/material';

import { createDiscussion, getDiscussion, getDiscussionCount } from '_reduxapi/q360/actions';
import { selectedDiscussionLists } from '_reduxapi/q360/selectors';
import { selectUserId } from '_reduxapi/Common/Selectors';
import { formatDate, getColorFromName, SearchIconSx, SearchInputSx } from './utils';
import { formatFullName } from 'utils';

const Accordion = styled((props) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&::before': {
    display: 'none',
  },
}));

const AccordionSummary = styled((props) => (
  <MuiAccordionSummary
    expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
    {...props}
  />
))(({ theme }) => ({
  backgroundColor: 'rgba(0, 0, 0, .03)',
  flexDirection: 'row-reverse',
  [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded}`]: {
    transform: 'rotate(90deg)',
  },
  [`& .${accordionSummaryClasses.content}`]: {
    marginLeft: theme.spacing(1),
  },
  ...theme.applyStyles('dark', {
    backgroundColor: 'rgba(255, 255, 255, .05)',
  }),
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: '0px',
  borderTop: '1px solid rgba(0, 0, 0, .125)',
}));

const DiscussionItem = ({ discussion, userId }) => {
  const isCurrentUser = userId === discussion.getIn(['author', '_id']);
  const formattedTime = moment(discussion.get('createdAt')).format('hh:mm A');
  const authorData = discussion.getIn(['author', 'name'], Map()).toJS();
  const authorFullName = formatFullName(authorData);
  const authorInitial = authorFullName?.charAt(0).toUpperCase();
  const avatarBgColor = getColorFromName(authorFullName || '');

  const renderAvatar = () => (
    <Avatar
      sx={{
        width: '30px',
        height: '30px',
        backgroundColor: avatarBgColor,
        color: '#fff',
      }}
    >
      {authorInitial}
    </Avatar>
  );

  return (
    <div key={discussion.get('_id')}>
      <Box
        display="flex"
        justifyContent={isCurrentUser ? 'flex-end' : 'flex-start'}
        marginBottom="16px"
        alignItems="center"
      >
        {!isCurrentUser && <Box marginRight={1}>{renderAvatar()}</Box>}
        <Box>
          <>
            {isCurrentUser && <span className="text-muted f-11 mr-2">{formattedTime}</span>}
            <span>{authorFullName}</span>
            {!isCurrentUser && <span className="text-muted f-11 ml-2">{formattedTime}</span>}
          </>
        </Box>
        {isCurrentUser && <Box marginLeft={1}>{renderAvatar()}</Box>}
      </Box>

      <Box
        display="flex"
        justifyContent={isCurrentUser ? 'flex-end' : 'flex-start'}
        marginBottom="16px"
        alignItems="center"
      >
        <div
          className="discussion-msg px-2"
          style={{ background: !isCurrentUser ? '#F3F4F6' : '#EFF9FB' }}
        >
          {discussion.get('description')}
        </div>

        {discussion.get('attachments').size > 0 &&
          discussion.get('attachments').map((attachment, index) => (
            <Box
              key={index}
              className="rounded border f-14 px-1 py-2 d-flex justify-content-between mt-1 cursor-pointer px-2 w-40"
            >
              <div>{attachment.get('name')}</div>
              <DownloadIcon sx={{ fontSize: 17 }} className="text-grey" />
            </Box>
          ))}
      </Box>
    </div>
  );
};

const DiscussionList = ({ discussions, userId }) => {
  return (
    <>
      {discussions.map((discussion) => (
        <DiscussionItem key={discussion.get('_id')} discussion={discussion} userId={userId} />
      ))}
    </>
  );
};

function CustomizedAccordions({ categoryList, handleChangeCategory, selectCategory }) {
  const [expanded, setExpanded] = useState('panel1');

  const handleChange = (panel) => (event, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  return (
    <div>
      <Accordion expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
        <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
          Category
        </AccordionSummary>
        <AccordionDetails style={{ maxHeight: '200px', overflowY: 'auto' }}>
          {categoryList.map((categories) => {
            return (
              <div
                className={`mt-2 mb-2 cursor-pointer text-capitalize ${
                  selectCategory === categories.get('_id', '') ? 'bold' : 'text-muted'
                }`}
                key={categories.get('_id', '')}
                onClick={() => handleChangeCategory(categories.get('_id', ''))}
              >
                {categories.get('categoryName', '')}
              </div>
            );
          })}
        </AccordionDetails>
      </Accordion>
    </div>
  );
}

const Discussion = ({ categoryList, selectedCalendar, categoryId }) => {
  const [formData, setFormData] = useState(Map({ message: '', file: null }));
  const [selectCategory, setSelectCategory] = useState('');
  const discussionEndRef = useRef(null);

  const dispatch = useDispatch();
  const discussionLists = useSelector(selectedDiscussionLists);
  const userId = useSelector(selectUserId);

  useEffect(() => {
    const isView = true;
    if (categoryId === 'overview' && categoryList.size > 0) {
      const firstCategoryId = categoryList.getIn([0, '_id']);
      setSelectCategory(firstCategoryId);
      const channelId = `q360_${firstCategoryId}_${selectedCalendar}`;
      callBack(channelId);
      countCallBack(channelId, isView);
    } else if (categoryId !== 'overview') {
      setSelectCategory('');
      const channelId = `q360_${categoryId}_${selectedCalendar}`;
      callBack(channelId);
      countCallBack(channelId, isView);
    }
  }, [categoryId, categoryList]);

  const callBack = (channelId) => {
    dispatch(getDiscussion(channelId));
  };

  const handleChangeCategory = (value) => {
    setSelectCategory(value);
    const channelId = `q360_${value}_${selectedCalendar}`;
    callBack(channelId);
  };

  const handleChange = (event) => {
    const { name, value, files } = event.target;
    setFormData((prevData) => prevData.set(name, files ? files[0] : value));
  };

  const handleFileSelect = () => {
    document.getElementById('fileInput').click();
  };

  const countCallBack = (channelId, isView) => {
    const params = {
      institutionCalendarId: selectedCalendar,
      channelId: channelId,
      author: userId,
      ...(isView && { isView }),
    };
    dispatch(getDiscussionCount(params));
  };

  const handleSubmit = () => {
    const message = formData.get('message', '').trim();
    // const file = formData.get('file');
    if (!message) return;
    const channelId = `q360_${
      categoryId !== 'overview' ? categoryId : selectCategory
    }_${selectedCalendar}`;

    const requestData = {
      channel_id: channelId,
      author: userId,
      description: message,
      // attachment: [],
    };

    setFormData(Map({ message: '', file: null }));
    dispatch(createDiscussion(requestData, callBack, () => countCallBack(channelId)));
  };

  const handleKeyDown = (event) => {
    const message = formData.get('message', '').trim();
    if (event.key === 'Enter' && !message) {
      event.preventDefault();
    } else if (event.key === 'Enter') {
      event.preventDefault();
      handleSubmit();
    }
  };

  useEffect(() => {
    if (discussionEndRef.current) {
      discussionEndRef.current.scrollTo({
        top: discussionEndRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, [discussionLists]);

  const groupedByDate = discussionLists
    .sortBy((item) => new Date(item.get('createdAt')))
    .groupBy((details) => moment(details.get('createdAt')).format('YYYY-MM-DD'));

  const showDiscussions = categoryId !== 'overview' || selectCategory;

  return (
    <Box display="flex" bgcolor="#f5f5f5" marginTop="20px">
      {/* Sidebar */}
      <Box
        width="30%"
        bgcolor="#ffffff"
        borderRight="1px solid #ddd"
        padding="16px"
        paddingTop="0px"
        paddingLeft="0px"
        display="flex"
        flexDirection="column"
        justifyContent="space-between"
      >
        {/* Category List */}
        {categoryId === 'overview' && (
          <Box>
            <CustomizedAccordions
              categoryList={categoryList}
              handleChangeCategory={handleChangeCategory}
              selectCategory={selectCategory}
            />
          </Box>
        )}
      </Box>

      {/* Chat Section */}
      <Box width="70%" bgcolor="#ffffff" display="flex" flexDirection="column">
        {showDiscussions && (
          <>
            <Box flex={1} padding="16px" overflow="auto" maxHeight="44vh" ref={discussionEndRef}>
              {groupedByDate.entrySeq().map(([date, discussions]) => (
                <div key={date}>
                  <div className="d-flex align-items-center mb-2">
                    <Divider className="flex-grow-1 mx-2 " />
                    <div
                      className="badge py-2 px-2"
                      style={{ background: '#EFF9FB', color: '#147AFC' }}
                    >
                      {formatDate(date)}
                    </div>
                    <Divider className="flex-grow-1 mx-2" />
                  </div>
                  <DiscussionList discussions={discussions} userId={userId} />
                </div>
              ))}
            </Box>
            <Box
              padding="16px"
              borderTop="1px solid #ddd"
              bgcolor="#ffffff"
              display="flex"
              alignItems="center"
            >
              <OutlinedInput
                placeholder="Reply Here"
                value={formData.get('message', '')}
                name="message"
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                startAdornment={
                  <InputAdornment position="start" className="d-none">
                    <IconButton onClick={handleFileSelect}>
                      <AttachFileIcon sx={SearchIconSx} />
                    </IconButton>
                  </InputAdornment>
                }
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton onClick={handleSubmit}>
                      <SendIcon sx={SearchIconSx} />
                    </IconButton>
                  </InputAdornment>
                }
                inputProps={{
                  'aria-label': 'reply',
                }}
                sx={SearchInputSx(true)}
              />
              {/* Hidden file input */}
              <input
                id="fileInput"
                type="file"
                name="file"
                style={{ display: 'none' }}
                onChange={handleChange}
              />
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

export default Discussion;
