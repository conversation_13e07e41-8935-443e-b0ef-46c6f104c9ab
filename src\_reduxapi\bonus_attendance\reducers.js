import { fromJS } from 'immutable';
import * as actions from './actions';

const initialState = fromJS({
  bonusAttendanceList: [],
  currentBonusAttendance: null,
  studentBonusAttendance: [],
  loading: false,
  error: null,
  totalCount: 0,
  totalPages: 0,
  currentPage: 1,
});

const bonusAttendanceReducer = (state = initialState, action) => {
  switch (action.type) {
    case actions.UPSERT_BONUS_ATTENDANCE_REQUEST:
      return state.set('loading', true).set('error', null);

    case actions.UPSERT_BONUS_ATTENDANCE_SUCCESS:
      return state
        .set('loading', false)
        .update('bonusAttendanceList', (list) => {
          const newItem = fromJS(action.data.data);
          const existingIndex = list.findIndex((item) => item.get('_id') === newItem.get('_id'));

          if (existingIndex >= 0) {
            return list.set(existingIndex, newItem);
          } else {
            return list.unshift(newItem);
          }
        })
        .update('currentBonusAttendance', (current) =>
          current && current.get('_id') === action.data.data._id
            ? fromJS(action.data.data)
            : current
        );

    case actions.UPSERT_BONUS_ATTENDANCE_FAILURE:
      return state.set('loading', false).set('error', action.error);

    case actions.DELETE_BONUS_ATTENDANCE_REQUEST:
      return state.set('loading', true).set('error', null);

    case actions.DELETE_BONUS_ATTENDANCE_SUCCESS:
      return state
        .set('loading', false)
        .update('bonusAttendanceList', (list) =>
          list.filter((item) => item.get('_id') !== action.data.id)
        )
        .update('currentBonusAttendance', (current) =>
          current && current.get('_id') === action.data.id ? null : current
        );

    case actions.DELETE_BONUS_ATTENDANCE_FAILURE:
      return state.set('loading', false).set('error', action.error);

    case actions.GET_STUDENT_BONUS_ATTENDANCE_REQUEST:
      return state.set('loading', true).set('error', null);

    case actions.GET_STUDENT_BONUS_ATTENDANCE_SUCCESS:
      return state
        .set('loading', false)
        .set('studentBonusAttendance', fromJS(action.data.data || []));

    case actions.GET_STUDENT_BONUS_ATTENDANCE_FAILURE:
      return state.set('loading', false).set('error', action.error);

    default:
      return state;
  }
};

export default bonusAttendanceReducer;
