import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';
import ArchiveImg from 'Assets/archive.png';
import Button from 'Widgets/FormElements/material/Button';
import { t } from 'i18next';
import { getShortString } from 'Modules/Shared/v2/Configurations';
function Delete({ show, setShow, deleteSelected, title, description, deleteName }) {
  const getTitle = () => {
    if (typeof title === 'object') return title;
    return getShortString(t(`${title}`), 15);
  };
  const getDescription = () => {
    if (typeof description === 'object') return description;
    return t(`${description}`, { type: t(`${title}`).toLowerCase() });
  };
  return (
    <Modal show={Boolean(show)} centered onHide={() => setShow(false)} dialogClassName="modal-520">
      <Modal.Body>
        <div className="d-flex mb-3">
          <img className="mr-2" alt={'Archive'} src={ArchiveImg} />
          <p className="mb-0 f-22 bold">
            {' '}
            {t('delete')} {getTitle()}
          </p>
        </div>
        <div className="p-2">
          <p className="mb-2 break-word">{getDescription()}</p>
          <p className="mb-0 break-word">
            {' '}
            <Trans i18nKey={'are_you_sure_you_want_to_delete_the_selected'}></Trans>{' '}
            {getShortString(deleteName, 40)} ?
          </p>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none pt-0">
        <Button variant="outlined" color="inherit" clicked={() => setShow(false)}>
          <Trans i18nKey={'cancel'}></Trans>
        </Button>

        <Button variant="contained" color="red" clicked={() => deleteSelected()}>
          <Trans i18nKey={'program_input.delete'}></Trans>
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

Delete.propTypes = {
  show: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  title: PropTypes.string,
  description: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  deleteName: PropTypes.string,
  deleteSelected: PropTypes.func,
  setShow: PropTypes.func,
};
export default Delete;
