import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import { ThemeProvider } from '@mui/styles';
import ReactECharts from 'echarts-for-react';
import { withTranslation } from 'react-i18next';

import MasterGraphCourseTable from './MasterGraphCourseTable';
import Loader from '../../../../Widgets/Loader/Loader';
import MasterGraphCloSlo from './MasterGraphCloSlo';
import { getColor } from './utils';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  selectFrameworkMappingGraph,
  selectCurriculumsWithFramework,
  selectIsMappingGraphLoading,
  selectCloSloMappingGraph,
  selectMasterGraphData,
} from '../../../../_reduxapi/program_input/selectors';
import { sortImmutableAlphaNumeric } from './utils'; //sortAlphaNumeric,
import {
  getURLParams,
  MUI_GRAPH_RADIO_THEME,
  removeURLParams,
  eString,
  indVerRename,
  stringToUC,
  levelRename,
} from '../../../../utils';
import '../../css/program.css';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { getLang } from '../../../../utils';

const lang = getLang();
class MasterGraph extends Component {
  constructor(props) {
    super(props);
    this.state = {
      programId: getURLParams('_id', true),
      programName: getURLParams('_n', true),
      showCloSloMapping: false,
      activeCourse: Map(),
      activeCloSloViewType: 'domain',
    };
    this.setActiveDomainYear = this.setActiveDomainYear.bind(this);
    this.showHideCloSloMapping = this.showHideCloSloMapping.bind(this);
    this.getSortedDomainYear = this.getSortedDomainYear.bind(this);
  }

  componentDidMount() {
    const { programId } = this.state;
    const {
      activeCurriculum,
      activeCurriculumFramework,
      activeViewType,
      masterGraphData,
      activeDomain,
      activeYear,
    } = this.props;
    this.props.getFrameworkMappingGraph(programId, {
      activeCurriculum,
      activeCurriculumFramework,
      activeViewType,
      setActiveDomainYear: this.setActiveDomainYear,
      masterGraphData,
      activeDomain,
      activeYear,
      getSortedDomainYear: this.getSortedDomainYear,
    });
  }

  setData(value) {
    const { setData, masterGraphData } = this.props;
    setData(Map({ masterGraphData: masterGraphData.merge(value) }));
  }

  handleSettingTypeChange(settingType) {
    this.setData(Map({ activeSettingType: settingType }));
  }

  handleViewTypeChange(viewType) {
    const { showCloSloMapping } = this.state;
    if (showCloSloMapping) {
      this.setState({ activeCloSloViewType: viewType });
      return;
    }
    const activeCurriculum = this.getActiveCurriculum();
    if (this.props.activeCurriculum.get('hasFramework')) {
      this.handleCurriculumChange(viewType, activeCurriculum.get('_id'));
    } else {
      this.setActiveDomainYear(viewType, activeCurriculum);
    }
  }

  handleCurriculumChange(activeViewType, curriculumId) {
    const { frameworkMappingGraph } = this.props;
    this.props.setData(Map({ curriculumsWithFramework: List() }));
    const activeCurriculum = frameworkMappingGraph
      .get('curriculums', List())
      .find((c) => c.get('_id') === curriculumId);
    if (!activeCurriculum) return;
    let activeDomain = Map();
    let activeYear = Map();
    if (activeCurriculum.get('hasFramework')) {
      if (activeViewType === 'domain') {
        activeDomain = activeCurriculum
          .getIn(['framework', 'domains'], List())
          .map((domain) =>
            domain.set(
              'plo',
              domain.get('plo', List()).filter((plo) => !plo.get('isDeleted'))
            )
          )
          .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'no'))
          .get(0, Map());
      } else {
        activeYear = activeCurriculum
          .get('year_level', List())
          .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'y_type'))
          .get(0, Map());
      }
    }
    this.setData(
      Map({
        activeViewType,
        activeCurriculum,
        activeDomain,
        activeYear,
        activeColor: '#FF0000',
        activeCurriculumFramework: Map(),
      })
    );
  }

  handleCurriculumFrameworkChange(curriculumId) {
    const { programId } = this.state;
    const { curriculumsWithFramework, activeViewType, activeCurriculum } = this.props;
    const activeCurriculumFramework = activeCurriculum
      .get('frameworks', List())
      .find((c) => c.get('_id') === curriculumId);
    if (!activeCurriculumFramework) return;
    this.setData(Map({ activeCurriculumFramework }));
    const curriculumWithFramework = curriculumsWithFramework.find(
      (curriculum) =>
        curriculum.get('_framework_curriculum_id') === activeCurriculumFramework.get('_id')
    );
    if (!curriculumWithFramework) {
      this.props.getCurriculumWithFramework({
        program_id: programId,
        curriculum_id: activeCurriculum.get('_id'),
        curriculum: activeCurriculumFramework.toJS(),
        frameworkCurriculumId: activeCurriculumFramework.get('_id'),
        viewType: activeViewType,
        setActiveDomainYear: this.setActiveDomainYear,
      });
    } else {
      setTimeout(() => {
        this.setActiveDomainYear(activeViewType, curriculumWithFramework);
      }, 100);
    }
  }

  getSortedDomainYear(viewType, curriculum) {
    if (viewType === 'domain') {
      return curriculum
        .getIn(['framework', 'domains'], List())
        .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'no'));
    }
    return curriculum
      .get('year_level', List())
      .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'y_type'));
  }

  setActiveDomainYear(viewType, curriculum, index = 0) {
    let activeDomain = Map();
    let activeYear = Map();
    if (viewType === 'domain') {
      activeDomain = this.getSortedDomainYear(viewType, curriculum).get(index, Map());
    } else {
      activeYear = this.getSortedDomainYear(viewType, curriculum).get(index, Map());
    }
    this.setData(
      Map({ activeViewType: viewType, activeDomain, activeYear, activeColor: '#FF0000' })
    );
  }

  getActiveCurriculum() {
    const { curriculumsWithFramework, activeCurriculum, activeCurriculumFramework } = this.props;
    if (activeCurriculum.isEmpty()) return Map();
    if (activeCurriculum.get('hasFramework')) {
      return activeCurriculum;
    }
    const curriculum = curriculumsWithFramework.find(
      (c) => c.get('_framework_curriculum_id') === activeCurriculumFramework.get('_id')
    );
    return curriculum || Map();
  }

  getActiveFramework() {
    return this.getActiveCurriculum().get('framework', Map());
  }

  getCurriculumOptions() {
    const { frameworkMappingGraph } = this.props;
    return frameworkMappingGraph.get('curriculums', List()).map((c) =>
      Map({
        name: c.get('curriculum_name'),
        value: c.get('_id'),
        disabled: !c.get('isActive', true),
      })
    );
  }

  getFrameworkOptions() {
    return this.props.activeCurriculum.get('frameworks', List()).map((c) =>
      Map({
        name: `${c.get('curriculum_name')} - ${c.getIn(['framework', 'code'])}`,
        value: c.get('_id'),
      })
    );
  }

  setActiveDomainOrYear(viewType, data, color) {
    this.setData(
      Map({
        [viewType === 'domain' ? 'activeDomain' : 'activeYear']: data,
        activeColor: color,
      })
    );
  }

  getSettings() {
    const { activeSettingType, activeDomain, activeViewType, activeYear } = this.props;
    const settingsKey = `${
      activeSettingType === 'standard' ? 'default_' : ''
    }standard_range_settings`;
    const activeCurriculum = this.getActiveCurriculum();
    const settings = activeCurriculum.get(settingsKey, List());
    if (activeViewType === 'domain') {
      const domainSettings = settings
        .filter((s) => s.get('domain_id') === activeDomain.get('_id'))
        .sortBy((s) => s.get('year'));
      const lowerLimit = domainSettings.map((s) => s.get('lower_limit'));
      const higherLimit = domainSettings.map(
        (s, i) => s.get('higher_limit') - lowerLimit.get(i, 0)
      );
      return { lowerLimit: lowerLimit.toJS(), higherLimit: higherLimit.toJS() };
    }
    const year = Number(activeYear.get('y_type', '').split('year').join(''));
    const yearSettings = settings
      .filter((setting) => setting.get('year') === year)
      .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'domain_name'));
    const lowerLimit = yearSettings.map((s) => s.get('lower_limit'));
    const higherLimit = yearSettings.map((s, i) => s.get('higher_limit') - lowerLimit.get(i, 0));
    return { lowerLimit: lowerLimit.toJS(), higherLimit: higherLimit.toJS() };
  }

  getXAxisData(viewType) {
    if (viewType === 'domain') {
      const { activeCurriculum } = this.props;
      return Array(activeCurriculum.get('end_at', 0))
        .fill(0)
        .map((_, i) => i + 1);
    }
    const activeFramework = this.getActiveFramework();
    return (
      activeFramework
        .get('domains', List())
        .map((domain) => domain.get('no', ''))
        // .sort(sortAlphaNumeric)
        .toJS()
    );
  }

  getDomainName(domainNo) {
    const activeFramework = this.getActiveFramework();
    const domain = activeFramework
      .get('domains', List())
      .find((domain) => domain.get('no', '') === domainNo);
    if (!domain) return `Domain ${domainNo}`;
    return `${domainNo} • ${domain.get('name', '')}`;
  }

  getLineData(viewType, data) {
    if (viewType === 'domain') {
      const totalCloMappedData = data.get('mappedObject', Map());
      return this.getXAxisData(viewType).map(
        (year) => totalCloMappedData.get(`year${year}`, List()).size
      );
    }
    const activeFramework = this.getActiveFramework();
    return activeFramework
      .get('domains', List())
      .map((domain) => domain.getIn(['mappedObject', data.get('y_type')], List()).size)
      .toJS();
  }

  getGraphOptions() {
    const { activeColor, activeSettingType, activeDomain, activeYear, activeViewType } = this.props;
    const { higherLimit, lowerLimit } = this.getSettings();
    const options = {
      color: activeColor,
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const title = `${
            activeViewType === 'domain'
              ? 'Year ' + params[0].name
              : this.getDomainName(params[0].name)
          }`;
          try {
            return `${title} <br /> Higher Limit ${
              params[0].data + params[1].data
            } <br /> Lower Limit ${params[0].data}`;
          } catch (error) {
            return title;
          }
        },
      },
      xAxis: {
        type: 'category',
        data: this.getXAxisData(activeViewType),
        axisLabel: {
          formatter: `${activeViewType === 'domain' ? 'Y' : ''}{value}`,
          lineHeight: 30,
        },
        boundaryGap: false,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: lowerLimit,
          stack: 'confidence-band',
          type: 'line',
          smooth: true,
          lineStyle: {
            opacity: 0,
          },
          symbolSize: 7,
          symbol: lowerLimit.length > 1 ? 'none' : 'circle',
          itemStyle: {
            color: activeSettingType === 'standard' ? 'rgb(210, 210, 210)' : `${activeColor}1A`,
          },
        },
        {
          data: higherLimit,
          stack: 'confidence-band',
          type: 'line',
          smooth: true,
          lineStyle: {
            opacity: 0,
          },
          symbolSize: 7,
          symbol: higherLimit.length > 1 ? 'none' : 'circle',
          itemStyle: {
            color: activeSettingType === 'standard' ? 'rgb(210, 210, 210)' : `${activeColor}1A`,
          },
          areaStyle: {
            color: activeSettingType === 'standard' ? 'rgb(210, 210, 210)' : `${activeColor}1A`,
          },
        },
        {
          data: this.getLineData(
            activeViewType,
            activeViewType === 'domain' ? activeDomain : activeYear
          ),
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
          },
          label: {
            show: true,
            position: 'top',
          },
          symbol: 'circle',
          symbolSize: 7,
        },
      ],
    };
    return options;
  }

  getMiniGraphOptions(viewType, data, color) {
    const options = {
      grid: {
        height: 40,
        top: '2%',
        bottom: 2,
      },
      color,
      xAxis: {
        type: 'category',
        data: this.getXAxisData(viewType),
        axisLabel: {
          show: false,
        },
        boundaryGap: false,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      series: [
        {
          data: this.getLineData(viewType, data),
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 2,
          },
          symbol: 'circle',
          symbolSize: 2,
        },
      ],
    };
    return options;
  }

  getDomainOrYear(viewType) {
    if (viewType === 'domain') {
      return this.getActiveFramework()
        .get('domains', List())
        .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'no'));
    }
    return this.getActiveCurriculum()
      .get('year_level', List())
      .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'y_type'));
  }

  navigateToCurriculumSettings() {
    const activeCurriculum = this.getActiveCurriculum();
    const { history, location } = this.props;
    const activeFramework = this.getActiveFramework();
    history.push(
      `/program-input/configuration/frameworks/settings/${removeURLParams(location, [
        '_curriculum_id',
        '_framework_id',
      ])}&_curriculum_id=${eString(activeCurriculum.get('_id', ''))}&_framework_id=${eString(
        activeFramework.get('_id', '')
      )}`
    );
  }

  showHideCloSloMapping(show, course = Map()) {
    this.setState({ showCloSloMapping: show, activeCourse: course }, () => {
      if (show) {
        this.props.getCloSloMappingGraph(course.get('_id'));
      }
    });
  }

  render() {
    const {
      programName,
      showCloSloMapping,
      activeCourse,
      activeCloSloViewType,
      programId,
    } = this.state;
    const {
      activeCurriculum,
      activeViewType,
      activeDomain,
      activeSettingType,
      activeYear,
      activeCurriculumFramework,
      t,
    } = this.props;
    const curriculumOptions = this.getCurriculumOptions();
    const frameworkOptions = this.getFrameworkOptions();
    const activeFramework = this.getActiveFramework();
    const cloLabel = indVerRename('CLO', programId);
    const sloLabel = indVerRename('SLO', programId);
    const ploLabel = indVerRename('PLO', programId);
    return (
      <React.Fragment>
        {this.props.isMappingGraphLoading && <Loader isLoading />}
        <div className="main bg-gray">
          <div className="container-fluid">
            <div className="pb-4">
              <div className={`mb-1 ${lang === 'ar' ? 'text-left' : ''}`}>
                {showCloSloMapping && (
                  <i
                    className="fa fa-arrow-left cursor-pointer mr-2"
                    onClick={() => this.showHideCloSloMapping(false)}
                  />
                )}
                <span className="bold f-23">
                  {`${
                    showCloSloMapping ? `${cloLabel} - ${sloLabel}` : `${cloLabel} - ${ploLabel}`
                  } ${t('master_graph.matrix')} ${
                    showCloSloMapping
                      ? activeCourse.get('course_name', '')
                      : `${t('master_graph.a')} ` + programName + ` ${t('master_graph.curr')}`
                  }`}
                </span>
              </div>
              <div className="bg-white border-radious-8 p-3">
                <div className="row p-2 mt--15 align-items-center">
                  <div className="col-md-9">
                    {!showCloSloMapping ? (
                      <div className="row">
                        <div className="col-md-12 d-flex">
                          <div
                            className={`min-width-100 max-width-175 pr-3 ${
                              lang === 'ar' ? 'text-left' : ''
                            }`}
                          >
                            <div className="f-12 text-color-blue mb-1 text-uppercase">
                              {t('program')}
                            </div>
                            <div className="pt-1">{programName}</div>
                          </div>
                          <div
                            className={`min-width-100 max-width-175 pr-3 ${
                              lang === 'ar' ? 'text-left' : ''
                            }`}
                          >
                            <div className="f-12 text-color-blue mb-1 text-uppercase">
                              {t('curriculum')}
                            </div>
                            <div>
                              <FormControl variant="outlined" fullWidth size="small">
                                <Select
                                  value={activeCurriculum.get('_id', '')}
                                  onChange={(e) =>
                                    this.handleCurriculumChange(activeViewType, e.target.value)
                                  }
                                  className="border-radius-25 select_padding"
                                  classes={{ select: 'font-size-12' }}
                                  displayEmpty
                                  renderValue={(value) =>
                                    value
                                      ? activeCurriculum.get('curriculum_name')
                                      : t('master_graph.select')
                                  }
                                >
                                  {curriculumOptions.map((option) => (
                                    <MenuItem
                                      key={option.get('value')}
                                      value={option.get('value')}
                                      disabled={option.get('disabled', false)}
                                    >
                                      {option.get('name')}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            </div>
                          </div>
                          <div
                            className={`min-width-100 max-width-175  ${
                              lang === 'ar' ? 'text-left' : ''
                            }`}
                          >
                            <div className="f-12 text-color-blue mb-1 text-uppercase">
                              {t('framework')}
                            </div>
                            <div>
                              {!activeCurriculum.isEmpty() && (
                                <div>
                                  {activeCurriculum.get('hasFramework') ? (
                                    <div className="pt-1">{activeFramework.get('code', '')}</div>
                                  ) : (
                                    <FormControl variant="outlined" fullWidth size="small">
                                      <Select
                                        value={activeCurriculumFramework.get('_id', '')}
                                        className="border-radius-25 select_padding"
                                        classes={{ select: 'font-size-12' }}
                                        displayEmpty
                                        renderValue={(value) =>
                                          value
                                            ? `${activeCurriculumFramework.get(
                                                'curriculum_name',
                                                ''
                                              )} - ${activeCurriculumFramework.getIn(
                                                ['framework', 'code'],
                                                ''
                                              )}`
                                            : t('master_graph.select')
                                        }
                                        onChange={(e) =>
                                          this.handleCurriculumFrameworkChange(e.target.value)
                                        }
                                      >
                                        {frameworkOptions.map((option) => (
                                          <MenuItem
                                            key={option.get('name')}
                                            value={option.get('value')}
                                          >
                                            {option.get('name')}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                    </FormControl>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div>
                        {`${programName} > ${activeCurriculum.get(
                          'curriculum_name',
                          ''
                        )} > ${activeCourse.get('course_name', '')} (${activeCourse.getIn(
                          ['framework', 'code'],
                          ''
                        )})`}
                      </div>
                    )}
                  </div>
                  <div className="col-md-3">
                    <FormControl variant="outlined" size="small">
                      <Select
                        value={!showCloSloMapping ? activeViewType : activeCloSloViewType}
                        onChange={(e) => this.handleViewTypeChange(e.target.value)}
                        className="border-radius-25 select_domain"
                        classes={{ select: 'font-size-12 ' }}
                      >
                        <MenuItem value="domain">{t('master_graph.domain')}</MenuItem>
                        {!showCloSloMapping ? (
                          <MenuItem value="year">
                            {t('master_graph.year_level', {
                              LEVEL: stringToUC(indVerRename('Level', programId)),
                            })}
                          </MenuItem>
                        ) : (
                          <MenuItem value="deliveryType">
                            {t('master_graph.delivery_type')}
                          </MenuItem>
                        )}
                      </Select>
                    </FormControl>
                  </div>
                </div>
                {!showCloSloMapping ? (
                  <>
                    <div className="p-3 bg-gray border-radious-8">
                      <div className="bg-white">
                        <div className="row">
                          <div className="col-md-9 pr-0">
                            <div className={`p-3 ${lang === 'ar' ? 'text-left' : ''}`}>
                              <span className="text-gray f-14 ">
                                {`${t('master_graph.selected')} ${
                                  activeViewType === 'domain'
                                    ? t('master_graph.domain')
                                    : t('master_graph.year_level', {
                                        LEVEL: stringToUC(indVerRename('Level', programId)),
                                      })
                                }`}
                              </span>
                              <p className="bold f-18 mb-0">
                                {`${
                                  activeViewType === 'domain'
                                    ? t('master_graph.theme')
                                    : t('master_graph.year') + ' -'
                                } ${
                                  activeViewType === 'domain'
                                    ? activeDomain.get('no', '') +
                                      ' - ' +
                                      activeDomain.get('name', '')
                                    : activeYear
                                        .get('levels', List())
                                        .map((l) => levelRename(l.get('level_name', ''), programId))
                                        .join(' / ')
                                        .toUpperCase()
                                }`}
                              </p>
                              <p className="f-15 mb-3">{activeFramework.get('name', '')}</p>
                              <div className="f-12 mb-2">{t('master_graph.compare')}</div>
                              <div className="d-flex align-items-center">
                                <div className="framework-graph-radio-group">
                                  <ThemeProvider theme={MUI_GRAPH_RADIO_THEME}>
                                    <FormControl component="fieldset" className="pt-2 ml-3">
                                      <RadioGroup
                                        row
                                        name="settingType"
                                        value={activeSettingType}
                                        onChange={(e) =>
                                          this.handleSettingTypeChange(e.target.value)
                                        }
                                      >
                                        <FormControlLabel
                                          value="standard"
                                          control={<Radio size="small" color="primary" />}
                                          label={t('master_graph.framework_range')}
                                          classes={{ label: 'f-14' }}
                                        />
                                        <FormControlLabel
                                          value="curriculum"
                                          control={<Radio size="small" color="primary" />}
                                          label={`${t('master_graph.range')} ${activeCurriculum.get(
                                            'curriculum_name',
                                            ''
                                          )}`}
                                          classes={{ label: 'f-14' }}
                                        />
                                      </RadioGroup>
                                    </FormControl>
                                  </ThemeProvider>
                                </div>
                                {!activeCurriculum.isEmpty() && activeSettingType === 'curriculum' && (
                                  <>
                                    {CheckPermission(
                                      'tabs',
                                      'Program Input',
                                      'Programs',
                                      '',
                                      'Active Programs',
                                      'Edit'
                                    ) && (
                                      <div className="f-13 ml-1">
                                        {t('master_graph.edit_range')}{' '}
                                        <span
                                          className="cursor-pointer text-color-blue"
                                          onClick={() => this.navigateToCurriculumSettings()}
                                        >
                                          <i className="fa fa-cog ml-1" aria-hidden="true" />{' '}
                                          {t('cDetails.settings')}
                                        </span>
                                      </div>
                                    )}
                                  </>
                                )}
                              </div>
                              <div className="row pt-3">
                                <div className="col-md-3 pt-5 pr-0">
                                  <div className="pt-5">
                                    <p className="pt-5 text-gray mb-0 f-14 text-center">
                                      {t('master_graph.no_of_slo', {
                                        CLO: indVerRename('CLO', programId),
                                      })}
                                    </p>
                                    <p className="text-gray mb-0 f-14 text-center">
                                      {t('master_graph.mapped_with_plo', {
                                        PLO: indVerRename('PLO', programId),
                                      })}
                                    </p>
                                  </div>
                                </div>
                                <div className="col-md-9">
                                  <ReactECharts option={this.getGraphOptions()} />
                                  <p className="text-gray f-14 text-center">
                                    {activeViewType === 'domain'
                                      ? t('master_graph.no_of_years')
                                      : t('master_graph.domain')}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="col-md-3 pl-0">
                            <div className="pt-3 pr-2">
                              <p className="bold f-18 mb-3">
                                {`${t('master_graph.select')} ${
                                  activeViewType === 'domain'
                                    ? t('master_graph.domain')
                                    : t('master_graph.year_level', {
                                        LEVEL: stringToUC(indVerRename('Level', programId)),
                                      })
                                }`.toUpperCase()}
                              </p>
                              <div className="pt-2 pb-2">
                                {this.getDomainOrYear(activeViewType).map((data, i) => {
                                  const color = getColor(i);
                                  const activeData =
                                    activeViewType === 'domain' ? activeDomain : activeYear;
                                  return (
                                    <div
                                      key={data.get('_id')}
                                      className={`d-flex justify-content-between align-items-center graph_shadoww p-1 remove_hover mt-2 mb-2 ${
                                        data.get('_id') === activeData.get('_id')
                                          ? 'graph_active'
                                          : ''
                                      }`}
                                      onClick={() =>
                                        this.setActiveDomainOrYear(activeViewType, data, color)
                                      }
                                    >
                                      <div
                                        style={{ backgroundColor: `${color}1A` }}
                                        className="framework-mini-graph"
                                      >
                                        <ReactECharts
                                          option={this.getMiniGraphOptions(
                                            activeViewType,
                                            data,
                                            color
                                          )}
                                        />
                                      </div>
                                      <div
                                        className="text-blue f-12 text-center"
                                        style={{ width: '65px' }}
                                      >
                                        {`${
                                          activeViewType === 'domain'
                                            ? t('master_graph.theme')
                                            : t('master_graph.year')
                                        } ${
                                          activeViewType === 'domain'
                                            ? data.get('no')
                                            : data.get('y_type', '').split('year').join('')
                                        }`}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <MasterGraphCourseTable
                      viewType={activeViewType}
                      curriculum={this.getActiveCurriculum()}
                      activeCurriculumFramework={activeCurriculumFramework}
                      framework={activeFramework}
                      domain={activeDomain}
                      year={activeYear}
                      showHideCloSloMapping={this.showHideCloSloMapping}
                    />
                  </>
                ) : (
                  <MasterGraphCloSlo
                    activeViewType={activeCloSloViewType}
                    activeFramework={activeCourse.get('framework', Map())}
                    activeCourse={activeCourse}
                    cloSloMappingGraph={this.props.cloSloMappingGraph}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

MasterGraph.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  frameworkMappingGraph: PropTypes.instanceOf(Map),
  getFrameworkMappingGraph: PropTypes.func,
  getCurriculumWithFramework: PropTypes.func,
  curriculumsWithFramework: PropTypes.instanceOf(List),
  isMappingGraphLoading: PropTypes.bool,
  cloSloMappingGraph: PropTypes.instanceOf(List),
  getCloSloMappingGraph: PropTypes.func,
  setData: PropTypes.func,
  masterGraphData: PropTypes.instanceOf(Map),
  activeCurriculum: PropTypes.instanceOf(Map),
  activeViewType: PropTypes.string,
  activeSettingType: PropTypes.string,
  activeDomain: PropTypes.instanceOf(Map),
  activeYear: PropTypes.instanceOf(Map),
  activeColor: PropTypes.string,
  activeCurriculumFramework: PropTypes.instanceOf(Map),
  t: PropTypes.func,
};

const mapStateToProps = function (state) {
  const masterGraphData = selectMasterGraphData(state);
  return {
    frameworkMappingGraph: selectFrameworkMappingGraph(state),
    curriculumsWithFramework: selectCurriculumsWithFramework(state),
    isMappingGraphLoading: selectIsMappingGraphLoading(state),
    cloSloMappingGraph: selectCloSloMappingGraph(state),
    masterGraphData,
    activeCurriculum: masterGraphData.get('activeCurriculum'),
    activeViewType: masterGraphData.get('activeViewType'),
    activeSettingType: masterGraphData.get('activeSettingType'),
    activeDomain: masterGraphData.get('activeDomain'),
    activeYear: masterGraphData.get('activeYear'),
    activeColor: masterGraphData.get('activeColor'),
    activeCurriculumFramework: masterGraphData.get('activeCurriculumFramework'),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions)
)(withTranslation()(MasterGraph));
