daBaseApiUrl = ''

dafilterAcadamicYearPath = '/api/v1/academic-year?previousYears=false'

dafilterProgramPath = '/api/v1/program?checkUserPermission=true'
dafilterCoursePath = '/api/v1/reports-and-analytics/consolidated-grade/grade-distribution'
daOutcomeReportPath = '/api/v1/reports-and-analytics/course-report/clo-overall'

daGradePath = '/api/v1/reports-and-analytics/course-report/grade-distribution'
daGetTopicsNotCoveredPath = '/api/v1/reports-and-analytics/course-report/topic'

//new end poitns
dafilterAttempTypePath = '/api/v1/reports-and-analytics/course-report/attempt-type'

daGetInitialCourseDetailsPath = '/api/v1/reports-and-analytics/course-report/info'
selectedAttempTypes = []
selectedExamType = []

//app globals
dafilterProgramDrilldown = []
allAcadIds = []
chosenAcadId = ''
chosenAcadyear = ''
chosenAttemptType = ''
chosenProgram = []
programObj = []
chosenterm = ''
chosenlevel = ''

chosenYear = ''
chosenChild1 = ''
chosenChild2 = ''
chosenCourse = ''
AllDomainData = ''
AllGradeData = ''

//variables to be used for iframe integration
queryParamAcademicYearStart = ''
querParamAcademicYearEnd = ''
queryParamAttemptType = ''
queryParamCourseId = ''

$(document).ready(function () {
  $('.da-filter').show(1000)
  setEnvrionemntValues()
  daFilterGetAttemptTypeData()
})

function setEnvrionemntValues() {
  const urlParams1 = new URLSearchParams(window.location.search)
  daBaseApiUrl = urlParams1.get('daBaseApiUrl')
  dcBaseApiUrl = urlParams1.get('dcBaseApiUrl')

  if (daBaseApiUrl === null || daBaseApiUrl === undefined) {
    alert('base api url not passed')
  }

  queryParamAcademicYearStart = urlParams1.get('academicYearStart')
  querParamAcademicYearEnd = urlParams1.get('academicYearEnd')
  queryParamAttemptType = urlParams1.get('attemptType') || ''
  queryParamCourseId = urlParams1.get('courseId')
  BaseToken = 'Bearer ' + urlParams1.get('key')
  //  return daBaseApiUrl;
}

function daGetInitialCourseDetails() {
  const dynamicUrl = `${daBaseApiUrl}${daGetInitialCourseDetailsPath}?academicYearStart=${queryParamAcademicYearStart}&academicYearEnd=${querParamAcademicYearEnd}&attemptType=${queryParamAttemptType}&courseId=${queryParamCourseId}`
  var jsonData = { attemptTypes: selectedAttempTypes, examTypes: selectedExamType }
  $.ajax({
    type: 'POST',
    url: dynamicUrl,
    contentType: 'application/json',
    data: JSON.stringify(jsonData),
    headers: {
      Authorization: BaseToken,
    },
    success: function (response) {
      daRenderInitialCourseDetails(response.data)
      console.log('initial course data success ', response)
    },
    error: function (error) {
      console.error('Error get  data:', error)
      errorMessage(error)
    },
  })
}

function daRenderInitialCourseDetails(apiResponse) {
  $('.dc-course-title').append(apiResponse.courseName)
  $('.dc-course-code').append(apiResponse.courseCode)
  // $('.dc-department-name').val(apiResponse.collegeName);
  $('.dc-program-name').append(apiResponse.programName)
  $('.dc-acadamic-year').append(
    `${apiResponse.academicYear.start} - ${apiResponse.academicYear.end}`,
  )

  if (apiResponse.exams.length > 0 && apiResponse.exams[0].courseInstructors.length > 0) {
    $('.dc-instructor-name').append(apiResponse.exams[0].courseInstructors[0].name)
  }

  if (apiResponse.exams.length > 0) {
    $('.dc-coordinator-name').append(apiResponse.exams[0].courseCoordinator)
  }

  //$('.dc-student-group').val(formGroup(apiResponse));
  $('.dc-acad-label').append(apiResponse.levelCode)
  $('.report-input-college, .report-input-institution').append(apiResponse.collegeName)

  $('.total-registered').append(apiResponse.totalRegistered)
  $('.total-completed').append(apiResponse.totalCompleted)
}

function daFilterGetAcadamicYearData() {
  $.ajax({
    type: 'GET',
    url: daBaseApiUrl + dafilterAcadamicYearPath,
    contentType: 'application/json',
    headers: {
      Authorization: BaseToken,
    },
    success: function (response) {
      allAcadIds = response.data
      response.data.forEach(function (optionText, index) {
        // console.log("optionText", optionText);
        // Format the date range
        const dateRange = formatReadableDateRange(optionText.startDate, optionText.endDate)
        // Append the formatted date range to the select dropdown
        $('.da-filter')
          .find('.acad-year')
          .append('<option value="' + optionText._id + '" >' + dateRange + '</option>')
      })
    },
    error: function (error) {
      console.error('Error saving data:', error)
      errorMessage(error)
    },
  })
}

$('.da-filter')
  .find('.acad-year')
  .click(function () {
    chosenAcadId = $(this).val()
    chosenAcadData = findObjectById(allAcadIds, chosenAcadId)
  })

function daFilterGetAttemptTypeData() {
  let uniqeExamtype = []
  let dynamicUrl = `${daBaseApiUrl}${dafilterAttempTypePath}?academicYearStart=${queryParamAcademicYearStart}&academicYearEnd=${querParamAcademicYearEnd}&courseId=${queryParamCourseId}`
  console.log('dynamicUrl daFilterGetAttemptTypeData', dynamicUrl)

  $.ajax({
    type: 'GET',
    url: dynamicUrl,
    contentType: 'application/json',
    headers: {
      Authorization: BaseToken,
    },
    success: function (response) {
      console.log('daFilterGetAttemptTypeData ', response)

      response.data.forEach(function (attemptType, index) {
        $('.da-filter')
          .find('.attemptType')
          .append('<option value="' + attemptType.name + '" >' + attemptType.name + '</option>')

        attemptType.exams.forEach(function (ExamType, i) {
          if (uniqeExamtype.indexOf(ExamType.code) == -1) {
            $('.da-filter')
              .find('.exam-type')
              .append('<option value="' + ExamType.code + '" >' + ExamType.name + '</option>')
          }
          uniqeExamtype.push(ExamType.code)
        })
      })

      $('.da-filter').find('.exam-type').select2({ placeholder: 'Select Exam type' })

      $('.da-filter').find('.attemptType').select2({ placeholder: 'Select Attempt Type' })
    },

    error: function (error) {
      console.error('daFilterGetAttemptTypeData :', error)
      errorMessage(error)
    },
  })
}

function daFilterGetProgrameData() {
  $.ajax({
    type: 'GET',
    url: daBaseApiUrl + dafilterProgramPath,
    contentType: 'application/json',
    headers: {
      Authorization: BaseToken,
    },
    success: function (response) {
      dafilterProgramDrilldown = response.data
      response.data.forEach(function (optionText, index) {
        $('.da-filter')
          .find('.program')
          .append('<option value="' + optionText._id + '" >' + optionText.name + '</option>')

        $('.da-filter').find('.program,.attemptType').select2()
      })
    },
    error: function (error) {
      console.error('Error saving data:', error)
      errorMessage(error)
    },
  })
}

function ValidateFilter() {
  if (chosenAcadyear == '') {
    alert('choose Acadamic Year')
  }
}

function daGetTopicsNotCovered() {
  const dynamicUrl = `${daBaseApiUrl}${daGetTopicsNotCoveredPath}?academicYearStart=${queryParamAcademicYearStart}&academicYearEnd=${querParamAcademicYearEnd}&courseId=${queryParamCourseId}`
  console.log('dynamic url', dynamicUrl)
  //var attemptType=chos
  //  const { academicYearStart, academicYearEnd } = getStartAndEndYear(chosenAcadData);
  var jsonData = { attemptTypes: selectedAttempTypes, examTypes: selectedExamType }

  $.ajax({
    type: 'POST',
    url: dynamicUrl,
    contentType: 'application/json',
    data: JSON.stringify(jsonData),
    headers: {
      Authorization: BaseToken,
    },
    success: function (response) {
      renderTopicsNotCovered(response.data)
      console.log('topics not covered path', response.data)
    },
    error: function (error, reasons) {
      console.error('Error topics not covered path :', error, 'reasons', reasons)
      errorMessage(error)
    },
  })
}

function daSetBuildDateTime() {
  const currentDate = new Date()
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth() + 1
  const day = currentDate.getDate()
  let hours = currentDate.getHours()
  let minutes = currentDate.getMinutes()
  let seconds = currentDate.getSeconds()
  let amPm = hours >= 12 ? 'PM' : 'AM'

  hours = hours % 12
  hours = hours ? hours : 12
  minutes = minutes < 10 ? '0' + minutes : minutes
  seconds = seconds < 10 ? '0' + seconds : seconds

  const formattedDateTime = `${day}-${month}-${year} - ${hours}:${minutes}:${seconds}${amPm}`

  $('.build-date-time').append(formattedDateTime)
}

function renderTopicsNotCovered(notCoveredObj) {
  let notCoveredTableBody = ``

  if (notCoveredObj.exams.length > 1) {
    notCoveredTableBody += `<tr><td class="p-8" contenteditable="true">Overall</td><td class="p-8" contenteditable="true">`

    notCoveredObj.unUnusedTopics.forEach(function (topic) {
      notCoveredTableBody += `<p>${topic.name} - ${topic.code}</p>`
    })

    notCoveredTableBody += `</td><td class="p-8" contenteditable="true"></td><td class="p-8" contenteditable="true"></td><td class="p-8" contenteditable="true"></td></tr>`
  }

  notCoveredObj.exams.forEach(function (exam) {
    notCoveredTableBody += `<tr><td class="p-8" contenteditable="true">${exam.examType.name} - ${exam.attemptTypeName}</td><td class="p-8" contenteditable="true">`

    exam.unUnusedTopics.forEach(function (topic) {
      notCoveredTableBody += `<p>${topic.name} - ${topic.code}</p>`
    })

    notCoveredTableBody += `</td><td class="p-8" contenteditable="true"></td><td class="p-8" contenteditable="true"></td><td class="p-8" contenteditable="true"></td></tr>`
  })

  $('.topics-not-covered').append(notCoveredTableBody)
}

function daGetGradeData() {
  const dynamicUrl = `${daBaseApiUrl}${daGradePath}?academicYearStart=${queryParamAcademicYearStart}&academicYearEnd=${querParamAcademicYearEnd}&attemptType=${queryParamAttemptType}&courseId=${queryParamCourseId}`
  console.log('dynamic url', dynamicUrl)
  //var attemptType=chos
  //  const { academicYearStart, academicYearEnd } = getStartAndEndYear(chosenAcadData);
  var jsonData = { attemptTypes: selectedAttempTypes, examTypes: selectedExamType }

  $.ajax({
    type: 'POST',
    url: dynamicUrl,
    contentType: 'application/json',
    data: JSON.stringify(jsonData),
    headers: {
      Authorization: BaseToken,
    },
    success: function (response) {
      if (response?.data) {
        const chartData = response.data.studentsGradeDistribution
        const chart = getChart(chartData)
        drawChart(chart)
      }

      console.log('daGetGradeData success', response)
      renderDaReports(response.data)
      AllGradeData = response.data
      // response.data.forEach(function (optionText, index) {
      //     $('.da-filter').find(".course").append('<option value="' + optionText._id + '" >' + optionText.name + '</option>');
      // });
    },
    error: function (error, reasons) {
      alert('No Data available for this Selection')
      console.error('Error daGetGradeData :', error, 'reasons', reasons)
      errorMessage(error)
    },
  })
}

function getChart(chartdata) {
  console.log('chart data', chartdata)
  let Reports = ['Reports'],
    Male = ['Male'],
    Female = ['Female'],
    Total = ['Total']
  console.log('outside')

  chartdata.forEach(function (element, i) {
    Reports.push(element.grade)
    Male.push(element.percentage.male)
    Female.push(element.percentage.female)
    Total.push(element.percentage.total)
  })

  const chart = [Reports, Male, Female, Total]

  return chart
}

function daGetOutComeData() {
  var daGetOutComeDataUrl =
    daBaseApiUrl +
    daOutcomeReportPath +
    '?academicYearStart=' +
    queryParamAcademicYearStart +
    '&academicYearEnd=' +
    querParamAcademicYearEnd +
    '&attemptType=' +
    queryParamAttemptType +
    '&courseId=' +
    queryParamCourseId

  var jsonData = { attemptTypes: selectedAttempTypes, examTypes: selectedExamType }
  $.ajax({
    type: 'POST',
    url: daGetOutComeDataUrl,

    contentType: 'application/json',
    headers: {
      Authorization: BaseToken,
    },
    data: JSON.stringify(jsonData),
    success: function (response) {
      console.log('daGetOutComeDataUrl success ', response.data)
      renderDaOutcomeData(response.data)
    },
    error: function (error, reasons) {
      console.error('daGetOutComeDataUrl :', error, 'reasons', reasons)
      errorMessage(error)
    },
  })
}

function getPloString(obj, filterkey) {
  let plotext = ''
  if (typeof obj == 'object') {
    obj.forEach(function (i) {
      plotext += i[filterkey] + ',</br>'
    })

    return plotext
  } else {
    plotext = ''
  }
}

function renderDaOutcomeData(data) {
  AllDomainData = data.overAll

  data.overAll.domains.forEach(function (domain, domainIndex) {
    let domainRow = `
    <tr class="tbl-td1 text-left f-normal">
      <td colspan="9" style="padding: 8px">
        <span class="tbl-td-green">${domain.no}</span>${domain.name}:
      </td>
    </tr>`

    $('.da-coutcome-table').append(domainRow)

    domain.clos.forEach(function (clo, cloIndex) {
      let subClos = `
      <tr class="text-center">
        <td style="padding: 10px 4px;" rowspan="${clo.exams.length + 1}">
          <div class="tbl-td-version">${clo.name}</div>
        </td>
        <td class="digi-clo-description" style="padding: 10px 4px;" rowspan="${
          clo.exams.length + 1
        }">
          <div class='desc'>${clo.desc}</div>
        </td>
        <td class="digi-plo-code" style="padding: 10px 4px;" rowspan="${clo.exams.length + 1}">
          ${getPloString(clo.plos, 'ploCode')}
        </td>
      </tr>`

      clo.exams.forEach(function (exam, examIndex) {
        subClos += `
        <tr class="digi-exam-row">
        <td style="padding: 4px">${exam.name}</td>
        <td style="padding: 4px">
          ${exam.itemWithItemTypes ? getPloString(exam.itemWithItemTypes, 'code') : '-'}
        </td>
        <td class="dir-ver digi-type-evaluation">
          <div class="direct-col" style="padding: 4px">Direct</div><div class="indirect-col" style="padding: 8px">Indirect</div>
        </td>
        <td class="dir-ver digi-target-level">
          <div class="direct-col" style="padding: 4px">
            ${exam.values.achievementTarget}
          </div>
          <div class="indirect-col" style="padding: 4px">
          <input
          type="text"
          class="report-indirect"
          placeholder=""
        />
          </div>
        </td>
        <td class="dir-ver digi-actual-level">
          <div class="direct-col" style="padding: 4px">
            ${exam.values.achievement}
          </div>
          <div class="indirect-col" style="padding: 4px">
          <input
          type="text"
          class="report-indirect"
          placeholder=""
        />
          </div>
        </td>
        <td class="dir-ver digi-new-target">
          <div class="direct-col" contenteditable="true" style="padding: 4px"></div>
          <div class="indirect-col" style="padding: 4px">
            <input
            type="text"
            class="report-indirect"
            placeholder=""
          />
          </div>
        </td>
        </tr>`
      })

      $('.da-coutcome-table').append(subClos)
    })

    let toAddTotal = `<tr class="bg-total">

    <td style="padding: 20px 8px;">
      <div class="tbl-td-version">${domain.no}</div>
    </td>
    <td colspan="4" style="padding: 20px 8px;">
      <div>
      <div class="desc">Overall</div>
      </div>
    </td>
    <td class="dir-ver text-center digi-type-evaluation">
      <div class="direct-total" style="padding: 8px">Direct</div><div class="indirect-total" style="padding: 8px">Indirect</div>
    </td>
    <td class="dir-ver text-center digi-target-level">
        <div class="direct-total">${domain.values.achievementTarget}</div>
        <div class="indirect-total">
        <input
            type="text"
            class="report-indirect-bg"
            placeholder=""
          />
        </div>
    </td>
    <td class="dir-ver text-center digi-actual-level">
        <div class="direct-total">${domain.values.achievement}</div>
        <div class="indirect-total">
        <input
            type="text"
            class="report-indirect-bg"
            placeholder=""
          />
        </div>
    </td>
    <td class="dir-ver text-center digi-new-target">
        <div class="direct-total" contenteditable="true"></div>
        <div class="indirect-total">
        <input
            type="text"
            class="report-indirect-bg"
            placeholder=""
          />
        </div>
    </td>
    </tr>`

    $('.da-coutcome-table').append(toAddTotal)
  })

  initHeba()
}

$('.fill-all').on('click', function () {
  $('.askheba').trigger('click')
})

function renderDaReports(data) {
  renderGradeData(data)
  gradeNameStatus(data)
}

function gradeNameStatus(data) {
  let gradeLength = data.studentsGradeDistribution.length
  $('.grade-name-appender').append(
    `<th rowspan="3" colspan="3" class="text-center bg-purple-dark"></th><th class="text-center p-16 bg-purple-dark f-normal" colspan="${gradeLength}">Grades</th><th class="text-center p-16 bg-purple-dark f-normal" colspan="6">Status Distributions</th>`,
  )
}

function renderGradeData(data) {
  data.studentsGradeDistribution.forEach(function (i, element) {
    $('.grade-appender').append(
      '<th class="text-center p-16" rowspan="2" style="font-weight: normal">' + i.grade + '</th>',
    )
    $('.student-grade-appender-male').append(
      '<td class="text-center p-16">' + i.count.male + '</td>',
    )
    $('.student-grade-appender-female').append(
      '<td class="text-center p-16">' + i.count.female + '</td>',
    )
    $('.student-grade-appender-total').append(
      '<td class="text-center p-16">' + i.count.total + '</td>',
    )

    $('.student-grade-appender-male-percent').append(
      '<td class="text-center p-16">' + i.percentage.male + '</td>',
    )
    $('.student-grade-appender-female-percent').append(
      '<td class="text-center p-16">' + i.percentage.female + '</td>',
    )
    $('.student-grade-appender-total-percent').append(
      '<td class="text-center p-16">' + i.percentage.total + '</td>',
    )
  })

  $('.grade-appender').append(
    '<th class="text-center" rowspan="2" style="font-weight: normal">' + 'Denied Entry' + '</th>',
  )
  $('.grade-appender').append(
    '<th class="text-center" rowspan="2" style="font-weight: normal">' + 'In Progress' + '</th>',
  )
  $('.grade-appender').append(
    '<th class="text-center" rowspan="2" style="font-weight: normal">In Complete</th><th class="text-center" rowspan="2" style="font-weight: normal">Withdrawn</th><th class="text-center" rowspan="2" style="font-weight: normal">Fail</th> <th class="text-center p-16" rowspan="2" style="font-weight: normal">Pass</th>',
  )

  let passData = data.studentDistributionCount.count.pass
  let failData = data.studentDistributionCount.count.fail
  let passData_percent = data.studentDistributionCount.percentage.pass
  let failData_percent = data.studentDistributionCount.percentage.fail

  let dashplaceholder =
    '<td class="text-center p-16">-</td><td class="text-center p-16">-</td><td class="text-center p-16">-</td><td class="text-center p-16">-</td>'
  // $(".student-grade-appender-male").append('')
  $('.student-grade-appender-male').append(
    dashplaceholder +
      '<td class="text-center p-16">' +
      failData.male +
      '</td><td class="text-center p-16">' +
      passData.male +
      '</td>',
  )
  $('.student-grade-appender-female').append(
    dashplaceholder +
      '<td class="text-center p-16">' +
      failData.female +
      '</td><td class="text-center p-16">' +
      passData.female +
      '</td>',
  )
  $('.student-grade-appender-total').append(
    dashplaceholder +
      '<td class="text-center p-16">' +
      failData.total +
      '</td><td class="text-center p-16">' +
      passData.total +
      '</td>',
  )

  $('.student-grade-appender-male-percent').append(
    dashplaceholder +
      '<td class="text-center p-16">' +
      failData_percent.male +
      '</td><td class="text-center p-16">' +
      passData_percent.male +
      '</td>',
  )
  $('.student-grade-appender-female-percent').append(
    dashplaceholder +
      '<td class="text-center p-16">' +
      failData_percent.female +
      '</td><td class="text-center p-16">' +
      passData_percent.female +
      '</td>',
  )
  $('.student-grade-appender-total-percent').append(
    dashplaceholder +
      '<td class="text-center p-16">' +
      failData_percent.total +
      '</td><td class="text-center p-16">' +
      passData_percent.total +
      '</td>',
  )
  $('td').attr('contenteditable', 'true')
}

$('.da-filter .program').change(function () {
  var selectedValue = $(this).val()
  chosenProgram = findObjectById(dafilterProgramDrilldown, selectedValue)
  // Perform actions based on the selected value
  console.log('Selected value: ' + selectedValue, 'chosenProgram', chosenProgram)
  renderDaTerm(chosenProgram)
})

function renderDaTerm(obj) {
  $('.da-filter').find('.term option').remove()
  obj.children.forEach(function (optionText, index) {
    $('.da-filter')
      .find('.term')
      .append('<option value="' + optionText._id + '" >' + optionText.name + '</option>')
  })
}

// $('.da-filter .attemptType').click(function () {
//             chosenAttemptType=$(this).text()
//   });

$('.da-filter .attemptType').on('change', function () {
  // Get the new value
  chosenAttemptType = $(this).find(':selected').text()
  console.log(chosenAttemptType)

  selectedAttempTypes = $(this).val() // This gets an array of the selected values
  console.log(selectedAttempTypes) // You can then use this array as needed

  // Set the value
  // $(this).val(chosenVal).trigger('change');
})

$('.da-filter .exam-type').on('change', function () {
  selectedExamType = $(this).val() // This gets an array of the selected values
  console.log('exam-type', selectedExamType) // You can then use this array as needed
})

$('.da-filter .program').change(function () {
  // Get the selected value
  //dafilterProgramDrilldown
  console.log(dafilterProgramDrilldown)
  var selectedValue = $(this).val()
  //alert(selectedValue);
  chosenProgram = findObjectById(dafilterProgramDrilldown, selectedValue)
  // Perform actions based on the selected value
  console.log('Selected value: ' + selectedValue, 'chosenProgram', chosenProgram)
})

$('.da-filter .term').click(function () {
  chosenterm = $(this).val()
  chosenChild0 = findObjectById(chosenProgram.children, chosenterm)
  console.log('next level render', chosenChild0)
  $('.da-filter').find('.year option').remove()
  chosenChild0.children.forEach(function (optionText, index) {
    $('.da-filter')
      .find('.year')
      .append('<option value="' + optionText._id + '" >' + optionText.name + '</option>')
  })
})

$('.da-filter .year').click(function () {
  chosenChild1 = $(this).val()
  chosenChild1 = findObjectById(chosenChild0.children, chosenChild1)
  $('.da-filter').find('.children1 option').remove()
  chosenChild1.children.forEach(function (optionText, index) {
    $('.da-filter')
      .find('.children1')
      .append('<option value="' + optionText._id + '" >' + optionText.name + '</option>')
  })
})

$('.da-filter .children1').click(function () {
  chosenChild2 = $(this).val()
  chosenChild2 = findObjectById(chosenChild1.children, chosenChild2)
  $('.da-filter').find('.children2 option').remove()
  chosenChild2.children.forEach(function (optionText, index) {
    $('.da-filter')
      .find('.children2')
      .append('<option value="' + optionText._id + '" >' + optionText.name + '</option>')
  })
})

$('.da-filter .children2').click(function () {
  chosenChild3 = $(this).val()
  chosenChild3 = findObjectById(chosenChild2.children, chosenChild3)
  console.log('33333 filter', chosenChild3)
  $('.da-filter').find('.course option').remove()
  chosenChild3.children.forEach(function (optionText, index) {
    $('.da-filter')
      .find('.course')
      .append('<option value="' + optionText._id + '" >' + optionText.name + '</option>')
  })
})

$('.build-da-report').on('click', function () {
  if (selectedAttempTypes.length == 0 || selectedExamType.length == 0) {
    alert('alert please select attempt and exam type')
    return false
  }

  if ($(this).hasClass('another')) {
    window.location.reload()
  }
  daGetGradeData()
  daGetOutComeData()
  daGetInitialCourseDetails()
  daGetTopicsNotCovered()
  daSetBuildDateTime()
  $(this).text('Build Report').addClass('another')

  console.log('build da report')
})

$('.clear-da-filter').on('click', function () {
  window.location.reload()
})

// $('.da-filter .course').click(function () {
//   chosenCourse = $(this).val();
//   daGetcourseData();
// });

function findObjectById(passedObj, id) {
  return passedObj.find(function (obj) {
    return obj._id === id
  })
}

function errorMessage(err) {
  switch (err.status) {
    case 401:
      alert('Session Got Expired, Please Close This Tab, Go To Previous Page And Refresh Page')
    default:
  }
}
