import React, { Fragment } from 'react';
import { getTranslatedDuration } from 'utils';
import PropTypes, { oneOfType } from 'prop-types';

import { WeekWrapper, WeekRowWrapper } from '../Styled';

const DateRanges = ({ data }) => {
  return (
    <Fragment>
      <WeekWrapper len={data.length}>
        <Fragment>
          {data.map((item) => {
            return (
              <WeekRowWrapper key={item['range']}>
                <div className="pdfFont">{getTranslatedDuration(item['range'])}</div>
              </WeekRowWrapper>
            );
          })}
        </Fragment>
      </WeekWrapper>
    </Fragment>
  );
};

DateRanges.propTypes = {
  data: oneOfType([PropTypes.array, PropTypes.object]),
};

export default DateRanges;
