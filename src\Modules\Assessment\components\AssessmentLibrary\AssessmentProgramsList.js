import React, { useEffect, useState } from 'react';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { selectAssessmentProgramList } from '../../../../_reduxapi/assessment/selector';
import * as actions from '../../../../_reduxapi/assessment/action';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import ProgramsListTable from './ProgramsListTable';

const AssessmentProgramsList = ({
  getAssessmentProgramsList,
  programsList,
  activeInstitutionCalendar,
  view,
}) => {
  const institutionCalendar = activeInstitutionCalendar.get('_id', '');
  const [search, setSearch] = useState('');
  const [type, setType] = useState('all');

  useEffect(() => {
    institutionCalendar && getAssessmentProgramsList(institutionCalendar);
  }, [institutionCalendar]); // eslint-disable-line
  const programType = [
    {
      name: 'All',
      value: 'all',
    },
    {
      name: 'Undergraduate',
      value: 'undergraduate',
    },
    {
      name: 'Postgraduate',
      value: 'postgraduate',
    },
    {
      name: 'Pre-requisite',
      value: 'pre-requisite',
    },
  ];
  const handleChange = (e, field) => {
    field === 'search' && setSearch(e.target.value);
    field === 'type' && setType(e.target.value);
  };
  const filteredList = () => {
    if (search === '' && type === 'all') {
      return programsList;
    } else if (search !== '' && type === 'all') {
      const filteredArr = programsList.filter(
        (item) =>
          item.get('programCode', '').toLowerCase().includes(search.toLowerCase()) ||
          item.get('programName', '').toLowerCase().includes(search.toLowerCase())
      );
      return filteredArr;
    } else if (search === '' && type !== 'all') {
      const filteredArr = programsList.filter(
        (item) => item.get(type === 'pre-requisite' ? 'program_type' : 'type', '') === type
      );
      return filteredArr;
    } else {
      const filteredArr = programsList.filter(
        (item) =>
          (item.get('programCode', '').toLowerCase().includes(search.toLowerCase()) ||
            item.get('programName', '').toLowerCase().includes(search.toLowerCase())) &&
          item.get(type === 'pre-requisite' ? 'program_type' : 'type', '') === type
      );
      return filteredArr;
    }
  };

  return (
    <div className="main pb-5">
      <div className="container">
        <div className="pt-3 pb-5">
          <div className="course_master">
            <p className="mb-2 bold f-19"> All Program</p>
            <div className="d-flex justify-content-between align-items-center mb-4">
              <div className="pt-1`">
                <MaterialInput
                  elementType={'materialSearch'}
                  placeholder={'Search'}
                  value={search}
                  changed={(e) => handleChange(e, 'search')}
                />
              </div>
              <div className="d-flex justify-content-end">
                <div className="col-md-8">
                  <MaterialInput
                    elementType={'materialSelect'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    elementConfig={{ options: programType }}
                    label={'Program Type'}
                    labelclass={'mb-0'}
                    value={type}
                    changed={(e) => handleChange(e, 'type')}
                  />
                </div>
              </div>
            </div>
            <ProgramsListTable programList={filteredList()} view={view} />
          </div>
        </div>
      </div>
    </div>
  );
};
const mapStateToProps = function (state) {
  return {
    programsList: selectAssessmentProgramList(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};
AssessmentProgramsList.propTypes = {
  getAssessmentProgramsList: PropTypes.func,
  programsList: PropTypes.instanceOf(List),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  view: PropTypes.string,
};
export default compose(withRouter, connect(mapStateToProps, actions))(AssessmentProgramsList);
