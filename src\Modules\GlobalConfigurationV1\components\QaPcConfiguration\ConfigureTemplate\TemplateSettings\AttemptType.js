import React, { forwardRef, useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import { PropTypes } from 'prop-types';
import { List } from 'immutable';
import CreateAttemptType from '../../Models/CreateAttemptType';
import { Delete, Edit } from '@mui/icons-material';

const AttemptType = forwardRef(({ buttonName, attemptType }, ref) => {
  const [open, setOpen] = useState(false);
  const [attemptTypeData, setAttemptData] = useState(List());
  ref.current.set('attemptType', attemptTypeData);
  const [editIndex, setEditIndex] = useState(-1);
  useEffect(() => {
    setAttemptData(attemptType);
  }, [attemptType]);
  return (
    <>
      <Button
        fullWidth
        className="text-capitalize btn-border-dotted text-primary responsiveFontSizeSmall border border-dotted mb-3"
        onClick={() => setOpen(true)}
      >
        + {buttonName}
      </Button>
      {attemptTypeData.map((attempt, index) => {
        if (!attempt.get('isActive', true)) {
          return null;
        }
        return (
          <section className="d-flex p-3 border-bottom" key={index}>
            <div className="flex-grow-1 f-16 fw-400">{attempt.get('name', '')}</div>
            <Edit
              className="ml-3 cursor-pointer"
              onClick={() => setEditIndex(index)}
              fontSize="small"
            />
            <Delete
              fontSize="small"
              className="text-red ml-3 cursor-pointer"
              onClick={() => setAttemptData((prev) => prev.setIn([index, 'isActive'], false))}
            />
          </section>
        );
      })}
      {open && (
        <CreateAttemptType
          open={true}
          handleClose={() => setOpen(false)}
          setAttemptData={setAttemptData}
        />
      )}
      {editIndex !== -1 && (
        <CreateAttemptType
          open={true}
          editIndex={editIndex}
          existData={attemptTypeData.get(editIndex)}
          handleClose={() => setEditIndex(-1)}
          setAttemptData={setAttemptData}
        />
      )}
    </>
  );
});
AttemptType.propTypes = {
  buttonName: PropTypes.string,
  attemptType: PropTypes.instanceOf(List),
};

export default AttemptType;
