import React, { Fragment } from 'react';
import {
  row_start,
  row_end,
  dynamicAlignment,
  rotationDynamicAlign,
  mergeEvents,
  find_sun_to_sat,
} from '../../../../_utils/function';
import {
  Level,
  Text,
  TextContainer,
  Course,
  RotationalCourse,
  CourseEvent,
  CourseAlter,
} from '../../Styled';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { t } from 'i18next';
import { Trans } from 'react-i18next';
import PropTypes, { oneOfType } from 'prop-types';

const LevelCourses = (props) => {
  const {
    active,
    coursePopUp,
    start_date,
    end_date,
    events,
    courses,
    level_no,
    term,
    iframeShow,
    currentPGAccess,
    rotational,
    number,
    levels,
    len,
  } = props;

  let data_year2_level4 = dynamicAlignment(courses, start_date, end_date);
  let col1 = data_year2_level4[0] !== undefined ? data_year2_level4[0] : 1;
  let courses1 = data_year2_level4[1] !== undefined ? data_year2_level4[1] : [];
  let courseLength = courses1.length;

  let modified_courses = [];
  if (rotational) {
    modified_courses = rotationDynamicAlign(courses, start_date, end_date);
  }
  let displayCredit =
    document.getElementById('printViewHide') !== null
      ? document.getElementById('printViewHide').style.display
      : false;

  const courseViewPermission = CheckPermission(
    'tabs',
    'Program Calendar',
    'Dashboard',
    '',
    'Course',
    'View'
  );
  return (
    <Fragment>
      {rotational ? (
        <Level
          st_date={row_start(start_date, start_date)}
          end_date={row_end(start_date, end_date)}
          col={modified_courses[number]['columns']}
          no_of_rotation={modified_courses?.length}
          len={len}
        >
          {modified_courses[number]['course'].map((item, i, arr) => {
            const len = find_sun_to_sat(item.start_date, item.end_date).length;

            const creditHours =
              item.credit_hours &&
              item.credit_hours.length > 0 &&
              item.credit_hours.map((item) => {
                return item.credit_hours;
              });

            const creditTotal =
              item.credit_hours &&
              item.credit_hours.length > 0 &&
              item.credit_hours.reduce((sum, el) => {
                return el.credit_hours + sum;
              }, 0);

            return (
              <RotationalCourse
                key={item._id}
                row_st_date={item.row_start}
                row_end_date={item.row_end}
                col_st_date={item.column_start}
                col_end_date={item.column_end}
                bg={item.color_code}
                row_len={row_start(item.start_date, item.end_date)}
                onClick={() =>
                  courseViewPermission
                    ? coursePopUp(
                        'edit_course_regular',
                        item._id,
                        item._course_id,
                        level_no,
                        term,
                        active,
                        rotational,
                        number,
                        iframeShow,
                        currentPGAccess
                      )
                    : () => {}
                }
              >
                <div
                  className="pdfFont"
                  style={(() => {
                    const final = {
                      transform: 'rotate(270deg)',
                      // whiteSpace: 'nowrap',
                      margin: `${(len * 45) / 2}px 0px 0px 0px`,
                    };
                    return final;
                  })()}
                >
                  {len > 6
                    ? `${item.courses_name} (${item.courses_number.replace(' ', '')})`
                    : `${item.courses_number.replace(' ', '')}`}
                  <br />
                  {len > 6 && (
                    <>
                      {`${creditTotal}`}
                      {'  '}
                      {t('role_management.role_actions.Credit Hours')}
                      {'  '} ({creditHours.join('+')})
                    </>
                  )}
                  {len > 6 ? `` : `(${creditHours.join('+')})`}
                  <br />
                </div>
                {(() => {
                  const { output_events } = mergeEvents(item['courses_events']);

                  return output_events.map((event) => (
                    <CourseEvent
                      key={event._id}
                      st_date={row_start(arr[i]['start_date'], event.event_date)}
                      end_date={row_end(arr[i]['start_date'], event.end_date)}
                    >
                      {/* <CourseEventContent>{event.event_name}</CourseEventContent> */}
                    </CourseEvent>
                  ));
                })()}
              </RotationalCourse>
            );
          })}
        </Level>
      ) : (
        <Level
          st_date={row_start(start_date, start_date)}
          end_date={row_end(start_date, end_date)}
          col={col1}
          len={len}
        >
          {courses1 &&
            courses1.map((item, i, arr) => {
              let rowLength = row_start(
                item.start_date,
                item.end_date,
                item.courses_name === 'Physics' ? 'hi' : ''
              );
              //console.log('rowLength', len, rowLength, item);
              //const len = find_sun_to_sat(item.start_date, item.end_date).length;
              const creditHours =
                item.credit_hours &&
                item.credit_hours.length > 0 &&
                item.credit_hours.map((item) => {
                  return item.credit_hours;
                });

              const creditTotal =
                item.credit_hours &&
                item.credit_hours.length > 0 &&
                item.credit_hours.reduce((sum, el) => {
                  return el.credit_hours + sum;
                }, 0);

              let checkYearLongCourse =
                courses &&
                courses.length > 0 &&
                courses
                  .filter((items) => {
                    return item._id === items._id;
                  })
                  .reduce((_, el) => {
                    return el.yearLongCourses !== undefined ? el.yearLongCourses : false;
                  }, false);

              const clickFn = () => {
                if (courseViewPermission) {
                  if (!checkYearLongCourse) {
                    coursePopUp(
                      'edit_course_regular',
                      item._id,
                      item._course_id,
                      level_no,
                      term,
                      active,
                      rotational,
                      number,
                      iframeShow,
                      currentPGAccess
                    );
                  } else {
                    coursePopUp(
                      'edit_course_regular',
                      item._id,
                      item._course_id,
                      levels[0].level_no !== undefined ? levels[0].level_no : level_no,
                      term,
                      active,
                      rotational,
                      number,
                      iframeShow,
                      currentPGAccess
                    );
                  }
                }
              };
              return (
                <>
                  {courseLength > 12 && displayCredit === 'block' ? (
                    <CourseAlter
                      key={item._id}
                      row_st_date={item.row_start}
                      row_end_date={item.row_end}
                      col_st_date={item.column_start}
                      col_end_date={item.column_end}
                      bg={item.color_code}
                      row_len={row_start(item.start_date, item.end_date)}
                      onClick={clickFn}
                    >
                      <div
                        style={(() => {
                          const final = {
                            transform: 'rotate(270deg)',
                            width: rowLength > 6 ? '100vh' : '45vh',
                            // margin: `${(len * 45) / 2}px 0px 0px 0px`,
                          };
                          return final;
                        })()}
                      >
                        {item.courses_name}
                        {'  '}
                        {`(${item.courses_number.replace(' ', '')})`}
                        {'  '}
                      </div>
                    </CourseAlter>
                  ) : (
                    <Course
                      key={item._id}
                      row_st_date={item.row_start}
                      row_end_date={item.row_end}
                      col_st_date={item.column_start}
                      col_end_date={item.column_end}
                      bg={item.color_code}
                      row_len={row_start(item.start_date, item.end_date)}
                      onClick={clickFn}
                    >
                      <div className={'strightView'}>
                        {rowLength === 1 ? (
                          <>
                            {`(${item.courses_number})`}
                            {'  '}
                            {`${creditTotal}`}
                            {'  '}
                            {t('role_management.role_actions.Credit Hours')}
                            {'  '}
                          </>
                        ) : (
                          <>
                            {rowLength > 2 && item.courses_name}
                            {rowLength > 2 && <br />}({item.courses_number.replace(' ', '')})
                            <br />
                            {`${creditTotal}`}
                            {'  '}
                            {t('role_management.role_actions.Credit Hours')}
                            {'  '}
                            <br />
                            {`(${creditHours.join('+')})`}
                          </>
                        )}
                      </div>
                    </Course>
                  )}
                </>
              );
            })}
          {courses1.length === 0 && events.length === 0 && (
            <TextContainer>
              <Text>{t('no_courses_in_this_level')}</Text>{' '}
              <Text>
                <Trans
                  components={{
                    addIcon: (
                      <i className="fas fa-plus" style={{ color: 'black', fontSize: '14px' }}></i>
                    ),
                  }}
                >
                  add_courses_by_clicking
                </Trans>
              </Text>
            </TextContainer>
          )}
        </Level>
      )}
    </Fragment>
  );
};

LevelCourses.propTypes = {
  active: PropTypes.string,
  coursePopUp: PropTypes.func,
  start_date: PropTypes.string,
  end_date: PropTypes.string,
  events: oneOfType([PropTypes.object, PropTypes.array]),
  courses: oneOfType([PropTypes.object, PropTypes.array]),
  level_no: PropTypes.string,
  term: PropTypes.string,
  iframeShow: PropTypes.bool,
  currentPGAccess: PropTypes.bool,
  rotational: PropTypes.bool,
  number: PropTypes.number,
  levels: PropTypes.object,
  len: PropTypes.number,
};

export default LevelCourses;
