import { List, fromJS, Map } from 'immutable';

export const getCount = (item, type) => {
  const typeKeys = {
    institution: 'term',
    term: 'curriculum',
    curriculum: 'yearLevel',
    yearLevel: item.get('rotation', 'no') === 'no' ? 'regularCourse' : 'rotation_course',
    rotation: 'course',
    regular: 'course',
  };
  const typeKey = typeKeys[type];

  const count = item
    .get(typeKey, List())
    .filter((itemCount) => itemCount.get('status', false) === true);

  const totalCount = item.get(typeKey, List());

  return { count: count.size, totalCount: totalCount.size };
};

export const constructProgramDetails = (programDetails) => {
  console.log('selectedCourses', programDetails);
  return (
    programDetails.size > 0 &&
    programDetails.map((item) => {
      return item
        .set('status', false)
        .set('isOpen', false)
        .set(
          'term',
          item
            .get('level', List())
            .groupBy((item) => {
              return item.get('term', '');
            })
            .entrySeq()
            .map(([key, value]) =>
              Map({
                name: key,
                status: false,
                curriculum: value
                  .groupBy((item) => {
                    return item.get('curriculum', '');
                  })
                  .entrySeq()
                  .map(([key, value]) =>
                    Map({
                      name: key,
                      status: false,
                      yearLevel: value.map((item2) =>
                        item2
                          .set('status', false)
                          .set('isOpen', false)
                          .set(
                            'regularCourse',
                            fromJS([
                              {
                                name: 'Regular Courses',
                                status: false,
                                isOpen: false,
                                course: item2
                                  .get('course', List())
                                  .map((s) => s.set('status', false).set('isOpen', false)),
                              },
                            ])
                          )

                          .set(
                            'rotation_course',
                            item2.get('rotation_course', List()).map((s) =>
                              s
                                .set('status', false)
                                .set('isOpen', false)
                                .set(
                                  'course',
                                  s
                                    .get('course', List())
                                    .map((d) => d.set('status', false).set('isOpen', false))
                                )
                            )
                          )
                      ),
                    })
                  )
                  .toList(),
              })
            )
            .toList()
        );
    })
  );
};
