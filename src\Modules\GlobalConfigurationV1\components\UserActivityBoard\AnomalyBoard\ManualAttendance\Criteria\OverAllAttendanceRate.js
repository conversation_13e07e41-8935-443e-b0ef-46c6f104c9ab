import { Checkbox, Divider, Switch, TextField } from '@mui/material';
import React, { useEffect, forwardRef, useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import PropTypes from 'prop-types';
import { Map, List, fromJS } from 'immutable';

const OverAllAttendanceRate = forwardRef(({ anomalyBoard }, criteriaRef) => {
  const [state, setState] = useState(List());
  criteriaRef.current = criteriaRef.current.set(0, state);

  const handleInput = (key1, key2) => (e) => {
    const value = e.target.value;
    if (value !== '' && (Number(value) < 0 || isNaN(Number(value)))) return;
    if (key2) {
      setState((prev) => prev.setIn([key1, key2], value));
    } else {
      setState((prev) => prev.set(key1, value));
    }
  };

  useEffect(() => {
    const data =
      anomalyBoard.getIn(['parameters', 0, 'criteria', 0], null) ??
      fromJS({
        score: {
          isActive: false,
          noOfScore: '',
        },
        allowance: {
          isActive: false,
          noOfAllowance: '',
        },
        isActive: false,
        noOfSession: false,
        criteriaName: 'overall_manual',
        weight: '',
      });
    setState(data);
    criteriaRef.current = criteriaRef.current.set(0, data);
  }, [anomalyBoard]); //eslint-disable-line

  return (
    <div className="p-3 criteria_border criteria_card">
      <div className="d-flex">
        <h3>Overall Manual Attendance Rate</h3>
        <Switch
          className="ml-auto"
          checked={state.get('isActive', false)}
          onChange={(e) => setState((prev) => prev.set('isActive', e.target.checked))}
        />
      </div>
      <div className="text_underline f-14 mb-1">Calculation:</div>

      <div className="my-3 f-12">
        “Total No. of Students given Manual Attendance considering all the sessions conducted by the
        staff / Total No. of Recorded Students Attendance considering all the sessions conducted by
        the staff” * 100.
      </div>
      <div className="d-flex align-items-center">
        Weightage% *
        <Tooltips title="Contribution towards anomaly calculation">
          <InfoIcon fontSize="small" className="leader_info_color ml-2" />
        </Tooltips>
      </div>
      <div>
        <TextField
          fullWidth
          type="text"
          onChange={handleInput('weight')}
          value={state.get('weight', '')}
        />{' '}
      </div>
      <Divider className="my-3" />
      <div className="d-flex align-items-center">
        <Checkbox
          sx={{ padding: '9px 9px 9px 0px' }}
          checked={state.getIn(['score', 'isActive'], false)}
          onChange={(e) => setState((prev) => prev.setIn(['score', 'isActive'], e.target.checked))}
        />
        Anomaly Score
        <Tooltips title="Score applied for non performance">
          <InfoIcon fontSize="small" className="leader_info_color ml-2" />
        </Tooltips>
      </div>
      <div>
        <TextField
          fullWidth
          type="text"
          value={state.getIn(['score', 'noOfScore'], '')}
          onChange={handleInput('score', 'noOfScore')}
        />{' '}
      </div>

      <div className="d-flex align-items-center">
        <Checkbox
          sx={{ padding: '9px 9px 9px 0px' }}
          checked={state.getIn(['allowance', 'isActive'], false)}
          onChange={(e) =>
            setState((prev) => prev.setIn(['allowance', 'isActive'], e.target.checked))
          }
        />
        Allowance %
        <Tooltips title="Exempted %">
          <InfoIcon fontSize="small" className="leader_info_color ml-2" />
        </Tooltips>
      </div>
      <div>
        <TextField
          fullWidth
          type="text"
          value={state.getIn(['allowance', 'noOfAllowance'], '')}
          onChange={handleInput('allowance', 'noOfAllowance')}
        />{' '}
      </div>
    </div>
  );
});

OverAllAttendanceRate.propTypes = {
  anomalyBoard: PropTypes.instanceOf(Map),
};

export default OverAllAttendanceRate;
