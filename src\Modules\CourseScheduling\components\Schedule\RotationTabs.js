import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { Badge } from 'react-bootstrap';

function RotationTabs({ course, callBack }) {
  const [rotation, setRotation] = useState(0);

  RotationTabs.propTypes = {
    course: PropTypes.instanceOf(Map),
    callBack: PropTypes.func,
  };

  function handleSubmit(rotation_count) {
    setRotation(rotation_count);
    callBack(rotation_count);
  }

  function getRotation() {
    return course.get('rotation_dates', List()).map((item, index) => {
      return (
        <span className="pr-3 " key={index}>
          <Badge
            variant={`light badge_grouping ${
              rotation === item.get('rotation_count', '') && 'badge_grouping_active'
            }`}
            size="sm"
            className="f-12"
            onClick={() => handleSubmit(item.get('rotation_count', ''))}
          >
            R{item.get('rotation_count', '')}
          </Badge>
        </span>
      );
    });
  }

  return (
    <>
      <span className="pr-3">
        <Badge
          variant={`light badge_grouping ${rotation === 0 && 'badge_grouping_active'}`}
          size="sm"
          className="f-12"
          onClick={() => handleSubmit(0)}
        >
          All
        </Badge>
      </span>
      {getRotation()}
    </>
  );
}

export default RotationTabs;
