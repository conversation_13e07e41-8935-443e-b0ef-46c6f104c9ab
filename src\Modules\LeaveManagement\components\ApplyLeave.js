import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Button } from 'react-bootstrap';
import { List, Map } from 'immutable';
import moment from 'moment';
import { t } from 'i18next';
import { startOfDay, endOfDay, formatDistanceStrict, format, set, add, isValid } from 'date-fns';

import StaffSchedule from './StaffSchedule';
import LeaveConfirmModal from '../modal/LeaveConfirmModal';
import Warning from '../../../Assets/alert5.png';
import Input from '../../../Widgets/FormElements/Input/Input';
import * as actions from '../../../_reduxapi/leave_management/actions';
import {
  selectUserId,
  selectUserInfo,
  selectInstitutionCalendar,
  selectActiveInstitutionCalendar,
} from '../../../_reduxapi/Common/Selectors';
import {
  selectLeaveCategories,
  selectPermissionList,
  selectLeave,
  selectMyLeaveList,
  selectPermissionOverview,
  selectScheduleStatus,
  selectLeaveOverview,
} from '../../../_reduxapi/leave_management/selectors';
import { getScheduleListWithSubstituteStaff, getScheduleIdArray } from '../utils';
import '../../../Assets/css/leave_management.css';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import AlertModal from '../../InfrastructureManagement/modal/AlertModal';
import { getLang, getTimestamp } from '../../../utils';
import ar from 'date-fns/locale/ar';
import DatePicker, { registerLocale } from 'react-datepicker';
import WithGlobalConfigDateHooks from 'Hoc/withGlobalConfigDateHooks';
import LocalStorageService from 'LocalStorageService';

registerLocale('ar', ar);
const lang = getLang();
const LEAVE_TYPE_INFO = {
  leave: {
    title: 'Leave',
    type: 'leave',
  },
  permission: {
    title: 'Permission',
    type: 'permission',
  },
  'on-duty': {
    title: 'On-Duty',
    type: 'on_duty',
  },
};
const DATES = [
  { type: 'startDate', label: 'Start Date' },
  { type: 'endDate', label: 'End Date' },
];
const TIMES = [
  { type: 'startTime', label: 'Start Time' },
  { type: 'endTime', label: 'End Time' },
];
const initialState = {
  categoryId: '',
  typeId: '',
  startDate: '',
  endDate: '',
  startTime: '',
  endTime: '',
  reason: '',
  selectedAttachment: null,
  substituteStaff: Map(),
  modalData: { show: false, showAlert: false },
  populated: false,
  activeid: null,
  totalleaves: null,
  total_select_leave: null,
  days: [],
  isSubstituteStaffPopulated: false,
};
const TABS = ['Permission', 'Leave', 'On Duty'];
class ApplyLeave extends Component {
  constructor(props) {
    super(props);
    const {
      match: {
        params: { id },
        path,
      },
    } = props;
    this.state = { ...initialState, id, leaveTypeInfo: LEAVE_TYPE_INFO[path.split('/')[2]] || {} };
    props.setData(Map({ leave: Map(), scheduleStatus: Map() }));
  }

  componentDidMount() {
    const search = LocalStorageService.getCustomToken('activeLeaveid');
    const { startDate, endDate, days, id } = this.state;
    if (id !== 'new')
      setTimeout(() => {
        this.addDays();
        this.addDate('endDate');
      }, 2000);

    const firstDate = new Date(startDate);
    const secondDate = new Date(endDate);

    for (
      var currentDate = new Date(firstDate);
      currentDate <= secondDate;
      currentDate.setDate(currentDate.getDate() + 1)
    ) {
      if (currentDate.getDay() !== 5 && currentDate.getDay() !== 6) {
        days.push(new Date(currentDate));
        this.setState({ days: days.length });
      }
    }

    this.setState(
      {
        ...initialState,
        activeid: search,
      },
      () => {
        const { id, leaveTypeInfo } = this.state;
        if (id && id !== 'new') {
          this.props.getLeaveById({ id });
        }
        if (leaveTypeInfo.type === 'permission') {
          this.props.getAllPermissionList('staff');
        } else {
          if (id !== 1) {
            this.props.getLeaveCategories();
          }
        }
      }
    );
    this.props.setBreadCrumbName(t('side_nav.menus.Faculty_Academic_Accountability_Management'));
  }

  addDays = () => {
    const { categoryId, typeId } = this.state;
    const { leaveOverview } = this.props;
    const selectedType = this.getLeaveTypes(categoryId).find((l) => l.get('_id') === typeId);
    const SelectType_s = selectedType?.toJS();
    const leave_s = leaveOverview?.toJS();
    const filterdata = leave_s?.filter((data) => data?.leave_type === SelectType_s?.type_name);

    const total_leaves = filterdata[0]?.no_of_days - filterdata[0]?.leave_taken;
    this.setState({ totalleaves: total_leaves, total_select_leave: SelectType_s?.no_of_days });
  };

  addDate = (type) => {
    const { startDate, total_select_leave } = this.state;
    if (type === 'endDate') {
      let currentday = new Date(startDate).getDay();
      let Total_count = 0;
      let Max_leave_count = total_select_leave;
      while (Max_leave_count > 0) {
        if (currentday === 5 || currentday === 6) {
          Total_count++;
          currentday = currentday + 1 === 7 ? 0 : currentday + 1;
        } else {
          Max_leave_count--;
          Total_count++;
          currentday = currentday + 1 === 7 ? 0 : currentday + 1;
        }
      }

      const isValiddate = new Date(startDate).getTime() + 86400000 * (Total_count - 1);

      return isValiddate;
    }
  };

  onModalClose() {
    this.setState({
      modalData: { show: false, showAlert: false },
    });
  }

  setModalData({
    showAlert,
    title,
    description,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    this.setState({
      modalData: {
        showAlert,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
      },
    });
  }

  static getDerivedStateFromProps(props, state) {
    if (props.leave.isEmpty()) return { populated: false };

    if (!state.populated) {
      const { leave, userId } = props;
      if (state.leaveTypeInfo.type === 'permission') {
        let from = new Date(leave.get('from'));
        let to = new Date(leave.get('to'));
        if (isValid(from) && isValid(to)) {
          props.getScheduleStatus({
            institutionCalendarId: leave.get('_institution_calendar_id'),
            userId,
            startDate: leave.get('from'),
            endDate: leave.get('to'),
          });
        }
      }
      return {
        categoryId: leave.getIn(['leave_category', '_leave_category_id'], ''),
        typeId: leave.getIn(['leave_type', '_leave_type_id'], ''),
        startDate: new Date(leave.get('from')),
        endDate: new Date(leave.get('to')),
        startTime: new Date(leave.get('from')),
        endTime: new Date(leave.get('to')),
        reason: leave.get('reason'),
        selectedAttachment: leave.get('_leave_reason_doc', null),
        days: leave.get('days', ''),
        populated: true,
      };
    }
    if (
      !state.isSubstituteStaffPopulated &&
      props.scheduleStatus &&
      !props.scheduleStatus.get('data', List()).isEmpty()
    ) {
      const substituteStaff = props.scheduleStatus.get('data', List()).reduce((acc, s) => {
        const scheduleId = s.get('_id');
        const assignedSubstituteStaff = s.get('assigned_substitute_staff', Map());
        if (assignedSubstituteStaff.isEmpty()) return acc;
        return acc.set(
          scheduleId,
          Map({
            schedule_id: scheduleId,
            substitute_staff: Map({
              name: assignedSubstituteStaff.get('substitute_staff_name'),
              _staff_id: assignedSubstituteStaff.get('_substitute_staff_id'),
            }),
          })
        );
      }, Map());
      return { substituteStaff, isSubstituteStaffPopulated: true };
    }
    return null;
  }

  getLeaveCategories() {
    const { leaveTypeInfo } = this.state;
    const { leaveCategories, userInfo } = this.props;
    const categoryList = leaveCategories.get('category', List());
    const CategoryListStaff = categoryList.filter((c) => c.get('category_to') === 'staff');
    return List([Map({ name: t('leaveManagement.Select_category'), value: '' })])
      .concat(
        CategoryListStaff.filter((c) => {
          let leavetype = false;
          let leavetypefilter = c.get('leave_type', List());
          leavetypefilter.forEach((element) => {
            const isleave = ['both', userInfo.get('gender')].includes(element.get('gender'));
            if (isleave) {
              leavetype = true;
            }
          });

          return c.get('category_type') === leaveTypeInfo.type && leavetype;
        }).map((c) => Map({ name: c.get('category_name'), value: c.get('_id') }))
      )
      .toJS();
  }

  getLeaveTypes(categoryId) {
    const { leaveCategories } = this.props;
    const categoryList = leaveCategories.get('category', List());
    const CategoryListStaff = categoryList.filter((c) => c.get('category_to') === 'staff');

    const selectedCategory = CategoryListStaff.find((c) => c.get('_id') === categoryId);

    return selectedCategory ? selectedCategory.get('leave_type', List()) : List();
  }

  getLeaveTypesOptions(categoryId) {
    const { userInfo } = this.props;
    return List([Map({ name: t('leaveManagement.Select_type'), value: '' })])
      .concat(
        this.getLeaveTypes(categoryId)
          .filter((c) => ['both', userInfo.get('gender')].includes(c.get('gender')))
          .filter((c) => c.get('isActive') === true)
          .map((c) => Map({ name: c.get('type_name'), value: c.get('_id') }))
      )
      .toJS();
  }

  getActiveType() {
    const { categoryId, typeId } = this.state;

    if (!categoryId || !typeId) return Map();
    const selectedType = this.getLeaveTypes(categoryId).find((l) => l.get('_id') === typeId);

    return selectedType || Map();
  }

  getActiveCategory() {
    const { categoryId } = this.state;

    const { leaveCategories } = this.props;
    if (!categoryId) return Map();
    const selectedCategory = leaveCategories
      .get('category', List())
      .find((c) => c.get('_id') === categoryId);
    return selectedCategory || Map();
  }

  fetchScheduleStatus(startDate, endDate) {
    if (this.state.leaveTypeInfo.type === 'permission') {
      if (isValid(startDate) && isValid(endDate)) {
        const { activeInstitutionCalendar } = this.props;
        this.props.getScheduleStatus({
          institutionCalendarId: activeInstitutionCalendar.get('_id', ''),
          userId: this.props.userId,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        });
      }
    }
  }

  handleChange(value, name) {
    if (name === 'startDate' && value) {
      value = startOfDay(value);
      this.setState({ days: [] });
      this.addDays();
    }

    if (name === 'endDate' && value) {
      value = endOfDay(value);
      this.setState({ days: [] });
    }

    this.setState(
      {
        days: [],
        [name]: value,
        ...(name === 'categoryId' && { typeId: '', selectedAttachment: null, days: [] }),
        ...(name === 'typeId' && { selectedAttachment: null, days: [] }),
        ...(name === 'startDate' && { endDate: '', startTime: '', endTime: '' }),
      },
      () => {
        const { startDate, endDate, days } = this.state;
        if (name === 'startDate') this.props.setData(Map({ scheduleStatus: Map() }));
        this.setState({ substituteStaff: Map() });
        if (startDate && endDate && name === 'endDate') {
          this.fetchScheduleStatus(startDate, endDate);
        }

        const firstDate = new Date(startDate);
        const secondDate = new Date(endDate);

        for (
          var currentDate = new Date(firstDate);
          currentDate <= secondDate;
          currentDate.setDate(currentDate.getDate() + 1)
        ) {
          if (currentDate.getDay() !== 5 && currentDate.getDay() !== 6) {
            days.push(new Date(currentDate));
            this.setState({ days: days.length });
          }
        }
      }
    );
  }

  handleTimeChange(value, name) {
    const { startDate, startTime } = this.state;
    if (startDate && value) {
      const minutes = +format(value, 'mm');
      value =
        minutes % 60 === 0
          ? set(startDate, { hours: +format(value, 'HH'), minutes: +format(value, 'mm') })
          : '';
    }

    if (name === 'endTime' && value) {
      if (getTimestamp(startTime) >= getTimestamp(value)) {
        value = '';
      }
    }

    this.setState(
      {
        [name]: value,
        ...(name === 'startTime' && { endTime: '' }),
        substituteStaff: Map(),
      },
      () => {
        const { startTime, endTime } = this.state;
        if (['startDate', 'startTime'].includes(name)) {
          this.props.setData(Map({ scheduleStatus: Map() }));
        }
        if (startTime && endTime && name === 'endTime') {
          this.fetchScheduleStatus(startTime, endTime);
        }
      }
    );
  }

  getMinDate(type) {
    const { startDate } = this.state;

    if (type === 'endDate') {
      return startOfDay(new Date(startDate));
    }
    // if (type === 'endDate') return startDate;
  }

  getMinMaxTime(type) {
    const { startTime } = this.state;
    const { permissions } = this.props;
    return type === 'endTime' && startTime
      ? {
          minTime: add(startTime, { hours: 1 }),
          maxTime: add(startTime, { hours: permissions.get('permission_hours', 0) }),
        }
      : {};
  }

  handleFileChange(files) {
    if (!files) files = [];
    if (
      files[0].type === 'image/jpg' ||
      files[0].type === 'image/jpeg' ||
      files[0].type === 'image/png' ||
      files[0].type === 'application/pdf'
    ) {
      this.setState({
        selectedAttachment: files.length ? files[0] : null,
        selectedAttachmentError: '',
      });
    } else {
      this.setState({
        selectedAttachmentError: 'Image and pdf file only allowed',
      });
    }
  }

  handleRemoveAttachment() {
    this.setState({
      selectedAttachment: null,
    });
  }

  disableSubmit() {
    const {
      startDate,
      endDate,
      startTime,
      endTime,
      reason,
      selectedAttachment,
      populated,
    } = this.state;
    const { leave, institutionCalendar } = this.props;
    if (populated) {
      if (!leave.get('isActive')) return true;
      const calendarIndex = institutionCalendar.findIndex(
        (c) => c.get('_id') === leave.get('_institution_calendar_id')
      );
      if (calendarIndex === -1) return true;
      // if (leave.get('review') !== 'pending') return true;
      // if (leave.get('status', '') !== 'rejected') {
      //   if (isPast(new Date(leave.get('from')))) return true;
      // }
    }
    if (!this.isLeaveTypePermission()) {
      const activeCategory = this.getActiveCategory();
      const activeType = this.getActiveType();
      if (activeCategory.isEmpty() || activeType.isEmpty()) return true;

      if (!startDate || !endDate) return true;
      const pattern = /[a-zA-Z]/i;
      if (activeType.get('is_reason_required') && (!reason || !pattern.test(reason))) return true;
      if (activeType.get('is_attachment_required') && !selectedAttachment) return true;
    } else {
      const { permissions } = this.props;
      const pattern = /[a-zA-Z]/i;
      if (!reason || !pattern.test(reason)) return true;
      if (!startTime || !endTime) return true;
      if (permissions.get('is_permission_attachment_required', '') && !selectedAttachment)
        return true;
    }
    return false;
  }

  isSubstituteStaffAssignedToSessions = () => {
    const { scheduleStatus, permissions } = this.props;
    const { substituteStaff } = this.state;

    let scheduleList = scheduleStatus.get('data', List());
    if (scheduleList.isEmpty()) return true;
    if (!permissions.get('is_scheduled')) return true;

    scheduleList = scheduleList.map((s) => {
      const scheduleId = s.get('_id');
      const isSubstituteStaffRequired =
        s.get('staffs', List()).filter((staff) => staff.get('status') === 'pending').size <= 1;
      const isSubstituteStaffSelected = isSubstituteStaffRequired
        ? !substituteStaff.get(scheduleId, Map()).isEmpty()
        : true;

      return s.merge(Map({ isSubstituteStaffRequired, isSubstituteStaffSelected }));
    });
    const scheduleWithoutSubstituteStaff = scheduleList
      .filter((s) => !s.get('isSubstituteStaffSelected'))
      .toList();
    if (scheduleWithoutSubstituteStaff.size) {
      this.props.resetMessage(t('leaveManagement.staff_required_sessions'));
    }

    return scheduleWithoutSubstituteStaff.size === 0;
  };

  applyOrEditLeave(confirmed) {
    const {
      leaveTypeInfo,
      startDate,
      endDate,
      startTime,
      endTime,
      reason,
      selectedAttachment,
      populated,
      substituteStaff,
      activeid,
    } = this.state;
    const {
      userInfo,
      leave,
      permissions,
      institutionCalendar,
      history,
      permissionOverview,
      scheduleStatus,
      activeInstitutionCalendar,
    } = this.props;
    if (institutionCalendar.isEmpty()) return;
    const activeCategory = this.getActiveCategory();
    const activeType = this.getActiveType();

    if (
      this.isLeaveTypePermission() &&
      permissionOverview.getIn([0, 'permission_taken']) > permissions.get('permission_frequency')
    ) {
      this.setModalData({
        showAlert: true,
        title: 'Alert: Validation error',
        description: 'Permission Frequency is exceeded',
        variant: 'alert',
        cancelButtonLabel: 'OK',
      });
      return true;
    }
    if (!this.isLeaveTypePermission()) {
      var enddate = startOfDay(endDate).toISOString();
      var end = moment(enddate).format('YYYY-MM-DD 23:59:59');
      var endUnixtime = Date.parse(end);
    }
    const status = leave.get('status');

    if (this.isLeaveTypePermission() && !this.isSubstituteStaffAssignedToSessions()) {
      return;
    }

    const requestBody = {
      _id: leave.get('_id', ''),
      type: leaveTypeInfo.type,
      title: leaveTypeInfo.title,

      from: !this.isLeaveTypePermission()
        ? Date.parse(startOfDay(startDate).toISOString())
        : Date.parse(startTime.toISOString()),
      to: !this.isLeaveTypePermission() ? endUnixtime : Date.parse(endTime.toISOString()),
      ...(!this.isLeaveTypePermission() && {
        _leave_category_id: activeCategory.get('_id'),
        category_name: activeCategory.get('category_name'),
        _leave_type_id: activeType.get('_id'),
        leave_type_name: activeType.get('type_name'),
      }),
      // ...(status === 'rejected' && { _user_id: this.props.userId }),
      // ...(!populated && { _user_id: this.props.userId }),
      _user_id: this.props.userId,
      days:
        leaveTypeInfo.type === 'permission'
          ? 1
          : formatDistanceStrict(endDate, startDate).slice(0, 1),
      reason,
      // ...((activeType.get('is_attachment_required', false) ||
      //   permissions.get('is_permission_attachment_required', false)) &&
      // typeof selectedAttachment !== 'string' && {
      _leave_reason_doc: selectedAttachment,
      // }),

      ...(this.isLeaveTypePermission() && {
        schedules: !substituteStaff.isEmpty()
          ? getScheduleListWithSubstituteStaff(substituteStaff, scheduleStatus.get('data', List()))
              .valueSeq()
              .toList()
              .toJS()
          : getScheduleIdArray(scheduleStatus),
      }),
      user_type: userInfo.get('user_type'),
      _institution_calendar_id: activeInstitutionCalendar.get('_id', ''),
      operation: populated ? 'update' : 'create',
    };
    if (!confirmed) {
      this.setState({
        modalData: {
          show: true,
          data: {
            ...requestBody,
            ...leaveTypeInfo,
            attachmentName:
              selectedAttachment !== 'null'
                ? typeof selectedAttachment === 'string'
                  ? selectedAttachment.split('/').slice(-1)
                  : selectedAttachment
                  ? selectedAttachment.name
                  : 'N/A'
                : 'N/A',
            showCategoryAndType: leaveTypeInfo.type !== 'permission',
          },
        },
      });
      return;
    }
    this.props.applyLeave({ requestBody, activeid, history, status });
  }

  onConfirm() {
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.applyOrEditLeave(true)
    );
  }

  isLeaveTypePermission() {
    // LocalStorageService.setCustomToken('activeLeaveid', 0);
    return this.state.leaveTypeInfo.type === 'permission';
  }

  getDates() {
    return this.isLeaveTypePermission() ? [{ type: 'startDate', label: 'Date' }] : DATES;
  }

  handleBackClick() {
    const activeLeaveId = LocalStorageService.getCustomToken('activeLeaveid');
    this.props.history.push({
      pathname: '/leave-management/leave',
      state: {
        activeTab: parseInt(activeLeaveId),
      },
    });
  }

  hasSchedule() {
    const { scheduleStatus } = this.props;
    const scheduleList = scheduleStatus.get('data', List());
    return !scheduleList.isEmpty();
  }

  handleSubstituteStaffChange(staffId, schedule) {
    const scheduleId = schedule.get('_id');
    if (!staffId) {
      this.setState((state) => {
        return { substituteStaff: state.substituteStaff.delete(scheduleId) };
      });
      return;
    }
    const staffs = schedule.get('substitute_staff', List());
    const selectedStaff = staffs.find((s) => s.get('_staff_id') === staffId);
    if (selectedStaff) {
      this.setState((state) => {
        return {
          substituteStaff: state.substituteStaff.set(
            scheduleId,
            Map({
              schedule_id: scheduleId,
              substitute_staff: selectedStaff,
            })
          ),
        };
      });
    }
  }

  render() {
    const {
      leaveTypeInfo,
      categoryId,
      typeId,
      startDate,
      endDate,
      startTime,
      endTime,
      reason,
      selectedAttachment,
      modalData,
      substituteStaff,
    } = this.state;
    const {
      permissions,
      scheduleStatus,
      isGlobalConfigDate,
      activeInstitutionCalendar,
    } = this.props;
    const activeLeaveId = LocalStorageService.getCustomToken('activeLeaveid');

    return (
      <div className="main pt-3 pb-5 ">
        <div className="container-fluid ">
          <div className="p-2">
            <div className="d-flex justify-content-between">
              <div>
                <b className="pr-3">
                  <i
                    className="fa fa-arrow-left remove_hover  f-16"
                    aria-hidden="true"
                    onClick={this.handleBackClick.bind(this)}
                  ></i>
                </b>
                <b className="mb-2 f-16">{`${t(`leaveManagement.tabs.${leaveTypeInfo.title}`)} ${t(
                  'leaveManagement.Application'
                )}`}</b>
              </div>
              {(CheckPermission(
                'tabs',
                'Leave Management',
                'Staff Leave',
                '',
                TABS[activeLeaveId],
                'Add'
              ) ||
                CheckPermission(
                  'tabs',
                  'Leave Management',
                  'Staff Leave',
                  '',
                  TABS[activeLeaveId],
                  'Edit'
                )) && (
                <div>
                  <Button
                    variant={this.disableSubmit() ? 'secondary' : 'primary'}
                    className="f-14"
                    disabled={this.disableSubmit()}
                    onClick={this.applyOrEditLeave.bind(this, false)}
                  >
                    {t('settings.submit')}
                  </Button>
                </div>
              )}
            </div>
            <div className="pl-35 pt-3">
              {!this.isLeaveTypePermission() && (
                <>
                  <div className="row">
                    <div className="col-md-2">
                      <label className="mt-27">{`${t(
                        `leaveManagement.tabs.${leaveTypeInfo.title}`
                      )} ${t('userManagement.category')}`}</label>
                    </div>
                    <div className="col-md-3">
                      <Input
                        elementType="floatingselect"
                        elementConfig={{
                          options: this.getLeaveCategories(),
                        }}
                        value={categoryId}
                        changed={(e) => this.handleChange(e.target.value, 'categoryId')}
                      />
                    </div>
                  </div>
                  <div className="row pt-2 mb-2">
                    <div className="col-md-2">
                      <label className="mt-27">{`${t(
                        `leaveManagement.tabs.${leaveTypeInfo.title}`
                      )} ${t('type')}`}</label>
                    </div>
                    <div className="col-md-3">
                      <Input
                        elementType="floatingselect"
                        elementConfig={{
                          options: this.getLeaveTypesOptions(categoryId),
                        }}
                        value={typeId}
                        changed={(e) => this.handleChange(e.target.value, 'typeId')}
                      />
                      <small>{this.getActiveType().get('desc', '')}</small>
                    </div>
                  </div>
                </>
              )}
              <div className="row pt-2 mb-2">
                <div className="col-md-2">
                  <label className="mt-27">
                    {t('leaveManagement.Reason')}
                    {(this.getActiveType().get('is_reason_required', false) ||
                      leaveTypeInfo.type === 'permission') &&
                      ' *'}
                  </label>
                </div>
                <div className="col-md-3">
                  <Input
                    elementType="floatinginput"
                    value={reason}
                    changed={(e) => this.handleChange(e.target.value, 'reason')}
                  />
                </div>
              </div>
              <div className="row pt-4">
                <div className="col-md-2">
                  <label className="mt-30">
                    {`${t('Date')}${this.isLeaveTypePermission() ? '' : t('leaveManagement.s')}`}
                  </label>
                </div>
                {this.isLeaveTypePermission() &&
                  this.getDates().map((date) => (
                    <div key={date.type} className="col-md-2">
                      <small className="d-flex">{t(`leaveManagement.${date.label}`)} </small>
                      <i className="fa fa-calendar-o leave_caleder" aria-hidden="true"></i>

                      <DatePicker
                        placeholderText={t('leaveManagement.Choose_date')}
                        {...(lang === 'ar' && { locale: 'ar' })}
                        popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                        selected={this.state[date.type]}
                        filterDate={isGlobalConfigDate}
                        onChange={(d) => this.handleChange(d, date.type)}
                        dateFormat="d MMM yyyy"
                        className="form-control customeDatepick"
                        showMonthDropdown
                        showYearDropdown
                        yearDropdownItemNumber={15}
                        minDate={
                          date.type === 'endDate' && this.state.startDate !== ''
                            ? new Date(this.state.startDate)
                            : activeInstitutionCalendar.get('start_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('start_date', ''))
                            : ''
                        }
                        maxDate={
                          activeInstitutionCalendar.get('end_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('end_date', ''))
                            : ''
                        }
                      />
                    </div>
                  ))}
                {!this.isLeaveTypePermission() &&
                  this.getDates().map((date) => (
                    <div key={date.type} className="col-md-2">
                      <small className="d-flex">{t(`leaveManagement.${date.label}`)} </small>
                      <i className="fa fa-calendar-o leave_caleder" aria-hidden="true"></i>

                      <DatePicker
                        placeholderText={t('leaveManagement.Choose_date')}
                        // minDate={this.getMinDate(date.type)}
                        {...(lang === 'ar' && { locale: 'ar' })}
                        //maxDate={this.addDate(t.type)}
                        selected={this.state[date.type]}
                        popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                        filterDate={isGlobalConfigDate}
                        onChange={(d) => this.handleChange(d, date.type)}
                        dateFormat="d MMM yyyy"
                        className="form-control customeDatepick"
                        showMonthDropdown
                        showYearDropdown
                        yearDropdownItemNumber={15}
                        minDate={
                          date.type === 'endDate' && this.state.startDate !== ''
                            ? new Date(this.state.startDate)
                            : activeInstitutionCalendar.get('start_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('start_date', ''))
                            : ''
                        }
                        maxDate={
                          activeInstitutionCalendar.get('end_date', '') !== ''
                            ? new Date(activeInstitutionCalendar.get('end_date', ''))
                            : ''
                        }
                      />
                    </div>
                  ))}

                {!this.isLeaveTypePermission() &&
                  !this.getActiveType().get('weekend_consideration') && (
                    <div className="col-md-2">
                      <label className="mt-30 d-flex">
                        {`${t('duration')}: ${
                          startDate && endDate ? this.state.days : t('constant.na')
                        }
                     ${
                       startDate && endDate
                         ? this.state.days === 1
                           ? t('leaveManagement.day')
                           : t('leaveManagement.days')
                         : ''
                     } `}
                      </label>
                    </div>
                  )}

                {!this.isLeaveTypePermission() &&
                  this.getActiveType().get('weekend_consideration', false) && (
                    <div className="col-md-2">
                      <label className="mt-30 d-flex">
                        {`${t('duration')}: ${
                          startDate && endDate
                            ? formatDistanceStrict(endDate, startDate)
                            : t('constant.na')
                        }
                     `}
                      </label>
                    </div>
                  )}
              </div>
              {this.isLeaveTypePermission() && (
                <div className="row pt-4">
                  <div className="col-md-2">
                    <label className="mt-30">{t('dashboard_view.time')}</label>
                  </div>
                  {TIMES.map((item, i) => (
                    <div key={item.type} className="col-md-2">
                      <small className="d-flex">{t(`leaveManagement.${item.label}`)}</small>

                      <DatePicker
                        placeholderText={t('leaveManagement.Choose_time')}
                        {...(lang === 'ar' && { locale: 'ar' })}
                        {...this.getMinMaxTime(item.type)}
                        popperPlacement={lang === 'ar' ? 'bottom-end' : 'bottom-start'}
                        selected={this.state[item.type]}
                        onChange={(d) => this.handleTimeChange(d, item.type)}
                        dateFormat="h:mm aa"
                        className="form-control customeDatepick"
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={60}
                        timeCaption={t(`leaveManagement.${item.label}`)}
                        disabled={!startDate}
                      />

                      <i className="fa fa-clock-o leave_caleder" aria-hidden="true"></i>

                      {TIMES.length === i + 1 && (
                        <small className="d-flex">
                          {t('settings.benchmark_setting.max')}.{' '}
                          {permissions.get('permission_hours', t('constant.na'))}{' '}
                          {t('leaveManagement.hour')}
                          {permissions.get('permission_hours') === 1 ? '' : 's'}
                        </small>
                      )}
                    </div>
                  ))}
                  <div className="col-md-2 padding-right-5">
                    <label className="mt-30 d-flex">
                      {`${t('duration')}: ${
                        startTime && endTime
                          ? formatDistanceStrict(endTime, startTime)
                          : t('constant.na')
                      }`}
                    </label>
                  </div>
                </div>
              )}
              {this.hasSchedule() && (
                <div className="row">
                  <div className="col-md-10 offset-md-2">
                    <b className="text-red">
                      <img alt="warning" src={Warning} className="pr-2" />
                      {t('leaveManagement.schedule_select_time')}
                    </b>
                  </div>
                </div>
              )}
              <div className="row pt-5 mb-2">
                <div className="col-md-2">
                  <label className="mt-10">
                    {t('leaveManagement.attachment')}
                    {(this.getActiveType().get('is_attachment_required') ||
                      permissions.get('is_permission_attachment_required')) &&
                      ' *'}
                  </label>
                </div>
                <div className={`col-md-10 ${lang === 'ar' ? 'text-left' : ''}`}>
                  <p className="mb-0">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary browse-button import-padding border-radious-8"
                    >
                      {t('browse')}
                      <input
                        id="fileUpload"
                        type="file"
                        accept=".png,.pdf,.jpg,.jpeg"
                        onChange={(e) => this.handleFileChange(e.target.files)}
                      />
                    </label>
                  </p>

                  {selectedAttachment && typeof selectedAttachment === 'object' && (
                    <p className="f-11">
                      {selectedAttachment.name}
                      <b className="padding-left-5 padding-right-5">
                        <i
                          className="fa fa-remove remove_hover f-16"
                          aria-hidden="true"
                          onClick={this.handleRemoveAttachment.bind(this)}
                        ></i>
                      </b>
                      {selectedAttachment.size > 2000000 && (
                        <b className="text-red">
                          <br />
                          {t('leaveManagement.Exceeds_2MB_limit')}
                        </b>
                      )}
                    </p>
                  )}
                  {this.state.selectedAttachmentError && (
                    <p className="f-11">
                      <b className="text-red">
                        <br />
                        {this.state.selectedAttachmentError}
                      </b>
                    </p>
                  )}
                  {selectedAttachment && typeof selectedAttachment === 'string' && (
                    <>
                      {selectedAttachment !== 'null' && (
                        <p className="f-11">
                          {selectedAttachment.split('/').slice(-1)}
                          <b className="padding-left-5 padding-right-5">
                            <i
                              className="fa fa-remove remove_hover f-16"
                              aria-hidden="true"
                              onClick={this.handleRemoveAttachment.bind(this)}
                            ></i>
                          </b>
                        </p>
                      )}
                    </>
                  )}
                  <p className="f-11">{t('leaveManagement.accepted_formet')}</p>
                </div>
              </div>
              {this.isLeaveTypePermission() &&
                this.hasSchedule() &&
                this.props.permissions.get('is_scheduled', false) && (
                  <StaffSchedule
                    selectedSubstituteStaff={substituteStaff}
                    scheduleStatus={scheduleStatus}
                    handleSubstituteStaffChange={this.handleSubstituteStaffChange.bind(this)}
                  />
                )}
            </div>
          </div>
        </div>

        <LeaveConfirmModal
          show={modalData.show}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
          days={this.state.days}
          weekendConsider={this.getActiveType().get('weekend_consideration', false)}
        />
        <AlertModal
          show={modalData.showAlert}
          title={modalData.title || ''}
          description={modalData.description || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
        />
      </div>
    );
  }
}

ApplyLeave.propTypes = {
  userId: PropTypes.string,
  getLeaveCategories: PropTypes.func,
  leaveCategories: PropTypes.instanceOf(Map),
  applyLeave: PropTypes.func,
  getLeaveById: PropTypes.func,
  history: PropTypes.object,
  match: PropTypes.object,
  getAllPermissionList: PropTypes.func,
  getScheduleStatus: PropTypes.func,
  permissions: PropTypes.instanceOf(Map),
  leave: PropTypes.instanceOf(Map),
  userInfo: PropTypes.instanceOf(Map),
  setBreadCrumbName: PropTypes.func,
  institutionCalendar: PropTypes.instanceOf(List),
  permissionOverview: PropTypes.instanceOf(List),
  leaveOverview: PropTypes.instanceOf(List),
  scheduleStatus: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  resetMessage: PropTypes.func,
  getGlobalSessionSetting: PropTypes.func,
  isGlobalConfigDate: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    userId: selectUserId(state),
    userInfo: selectUserInfo(state),
    leaveCategories: selectLeaveCategories(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    permissions: selectPermissionList(state),
    myLeaveList: selectMyLeaveList(state),
    leave: selectLeave(state),
    institutionCalendar: selectInstitutionCalendar(state),
    scheduleStatus: selectScheduleStatus(state),
    leaveOverview: selectLeaveOverview(state),
    permissionOverview: selectPermissionOverview(state),
  };
};

export default connect(mapStateToProps, { ...actions })(
  withRouter(WithGlobalConfigDateHooks(ApplyLeave))
);
