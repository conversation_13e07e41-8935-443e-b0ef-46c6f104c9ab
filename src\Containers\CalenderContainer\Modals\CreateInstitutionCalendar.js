import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import CustomInput from 'Widgets/FormElements/Input/CustomInput';
import { Trans } from 'react-i18next';
import moment from 'moment';

function CreateInstitutionCalendar({
  show,
  calendarState,
  input,
  iconForm,
  chevronLeft,
  chevronRight,
  rows,
  rows2,
  monthNames,
  monthNames2,
  gregorianMonthYear,
  gregorianMonthYearEnd,
  showArabicMonth,
  onRadioGroupChange,
  handleStartDateShow,
  handleEndDateShow,
  prevMonth,
  nextMonth,
  prevMonthEnd,
  nextMonthEnd,
  onSubmit,
  onClose,
}) {
  const {
    selectedRadioButton,
    feedback,
    selectedDate,
    startDateShow,
    selectedEndDate,
    endDateShow,
  } = calendarState;
  return (
    <Modal
      show={show}
      size="lg"
      aria-labelledby="example-custom-modal-styling-title"
      onHide={() => {}}
    >
      <Modal.Header onClick={onClose} closeButton>
        <Modal.Title id="example-custom-modal-styling-title">
          <Trans i18nKey={'program_calendar.create_institution_calendar'}></Trans>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <form>
          <div className="pt-1"></div>
          <div className="managetext pt-4">
            <div className="row ">
              <div className="col-lg-12 col-md-12 col-xl-12 pb-5">
                <label className="radio-label font-weight-bold">
                  {' '}
                  <Trans i18nKey={'program_calendar.primary_calendar'}></Trans>
                </label>
                <CustomInput
                  elementType={'radio-checkboxtype'}
                  elementConfig={input}
                  selected={selectedRadioButton}
                  labelclass="radio-label2"
                  onChange={(e) => onRadioGroupChange(e, 'input')}
                  feedback={feedback}
                />
              </div>
              <div className="col-lg-6 col-md-6 col-xl-6 pb-2 ">
                <i className="fa fa-calendar-check-o calender" aria-hidden="true"></i>
                <input
                  type="text"
                  value={moment(selectedDate).format('DD-MM-YYYY')}
                  className={`form-control ${iconForm}`}
                  onClick={handleStartDateShow}
                  onChange={() => {}}
                />
                {startDateShow && (
                  <div>
                    <div className="p-4">
                      <div className="monthCalendar">
                        <div className="d-flex justify-content-between">
                          <span>
                            <i className={chevronLeft} onClick={prevMonth} aria-hidden="true"></i>
                          </span>
                          {selectedRadioButton === 'gregorian' ? (
                            <span>
                              <p className="text-center m-0">{gregorianMonthYear}</p>
                              {showArabicMonth(monthNames)}
                            </span>
                          ) : (
                            <span>
                              {showArabicMonth(monthNames)}
                              <p className="text-center m-0">{gregorianMonthYear}</p>
                            </span>
                          )}
                          <span>
                            <i
                              className={`${chevronRight} float-right pt-5px`}
                              onClick={nextMonth}
                              aria-hidden="true"
                            ></i>
                          </span>
                        </div>
                        <div className="border-none">
                          <div className="table text-center" style={{ marginTop: '2%' }}>
                            <div className="tr">
                              <div className="td">S</div>
                              <div className="td">M</div>
                              <div className="td">T</div>
                              <div className="td">W</div>
                              <div className="td">T</div>
                              <div className="td">F</div>
                              <div className="td">S</div>
                            </div>
                            {rows}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="col-lg-6 col-md-6 col-xl-6 pb-2 ">
                <i className="fa fa-calendar-check-o calender" aria-hidden="true"></i>
                <input
                  type="text"
                  className={`form-control ${iconForm}`}
                  value={moment(selectedEndDate).format('DD-MM-YYYY')}
                  onClick={handleEndDateShow}
                  onChange={() => {}}
                />
                {endDateShow && (
                  <div>
                    <div className="p-4">
                      <div className="monthCalendar">
                        <div className="d-flex justify-content-between">
                          <span>
                            <i
                              className={chevronLeft}
                              onClick={prevMonthEnd}
                              aria-hidden="true"
                            ></i>
                          </span>
                          {selectedRadioButton === 'gregorian' ? (
                            <span>
                              <p className="text-center m-0">{gregorianMonthYearEnd}</p>
                              {showArabicMonth(monthNames2)}
                            </span>
                          ) : (
                            <span>
                              {showArabicMonth(monthNames2)}
                              <p className="text-center m-0">{gregorianMonthYearEnd}</p>
                            </span>
                          )}
                          <span>
                            <i
                              className={`${chevronRight} float-right pt-5px`}
                              onClick={nextMonthEnd}
                              aria-hidden="true"
                            ></i>
                          </span>
                        </div>
                        <div className="border-none">
                          <div className="table text-center" style={{ marginTop: '2%' }}>
                            <div className="tr">
                              <div className="td">S</div>
                              <div className="td">M</div>
                              <div className="td">T</div>
                              <div className="td">W</div>
                              <div className="td">T</div>
                              <div className="td">F</div>
                              <div className="td">S</div>
                            </div>
                            {rows2}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </Modal.Body>
      <Modal.Footer>
        <div className="text-center">
          <Button onClick={onSubmit} variant="primary">
            {' '}
            <Trans i18nKey={'program_calendar.create'}></Trans>
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
}

CreateInstitutionCalendar.propTypes = {
  show: PropTypes.bool,
  calendarState: PropTypes.object,
  input: PropTypes.array,
  iconForm: PropTypes.string,
  chevronLeft: PropTypes.string,
  chevronRight: PropTypes.string,
  rows: PropTypes.object,
  rows2: PropTypes.object,
  monthNames: PropTypes.string,
  monthNames2: PropTypes.string,
  gregorianMonthYear: PropTypes.string,
  gregorianMonthYearEnd: PropTypes.string,
  showArabicMonth: PropTypes.func,
  onRadioGroupChange: PropTypes.func,
  handleStartDateShow: PropTypes.func,
  handleEndDateShow: PropTypes.func,
  prevMonth: PropTypes.func,
  nextMonth: PropTypes.func,
  prevMonthEnd: PropTypes.func,
  nextMonthEnd: PropTypes.func,
  onSubmit: PropTypes.func,
  onClose: PropTypes.func,
};

export default CreateInstitutionCalendar;
