import useGlobalConfigDateHooks from 'Hooks/useGlobalConfigDateHooks';
import React from 'react';

function WithGlobalConfigDateHooks(Component) {
  const InjectedGlobalDateCalender = function (props) {
    const { isWeekday } = useGlobalConfigDateHooks();
    return <Component {...props} isGlobalConfigDate={isWeekday} />;
  };
  return InjectedGlobalDateCalender;
}

export default WithGlobalConfigDateHooks;
