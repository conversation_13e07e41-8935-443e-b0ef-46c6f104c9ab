import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { List, Map } from 'immutable';
import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import { withStyles, makeStyles } from '@mui/styles';
import { format } from 'date-fns';
import YearLevelFilters from './YearLevelFilters';
import CheckIcon from '../../../../Assets/check.png';
import {
  capitalize,
  getURLParams,
  removeURLParams,
  eString,
  levelRename,
  getTranslatedDuration,
  getVersionName,
} from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { t } from 'i18next';

const Accordion = withStyles({
  root: {
    border: '1px solid rgba(0, 0, 0, .12)',
    borderRadius: '4px',
    boxShadow: 'none',
    '&:before': {
      display: 'none',
    },
    '&$expanded': {
      margin: 'auto auto 10px',
    },
    color: '#3d5170',
    marginBottom: 10,
  },
  expanded: {},
})(MuiAccordion);

const AccordionSummary = withStyles({
  root: {
    backgroundColor: 'rgba(0, 0, 0, .03)',
    borderBottom: '1px solid rgba(0, 0, 0, .12)',
    marginBottom: -1,
    minHeight: 56,
    '&$expanded': {
      minHeight: 56,
    },
  },
  content: {
    '&$expanded': {
      margin: '12px 0',
    },
  },
  expanded: {},
})(MuiAccordionSummary);

const AccordionDetails = withStyles((theme) => ({
  root: {
    padding: '16px', //theme.spacing(1)
    display: 'block',
  },
}))(MuiAccordionDetails);

const useStylesFunction = makeStyles((themes) => ({
  heading: {
    fontSize: '1rem',
    fontWeight: '500',
  },
}));

function YearLevelCourse({ courseList, setData, history, location }) {
  const [filter, setFilter] = useState(
    Map({
      term: getURLParams('term', true),
      year: getURLParams('activeYear', true),
      level: getURLParams('activeLevel', true),
    })
  );
  const [sortDirection, setSortDirection] = useState('asc');
  const classes = useStylesFunction();
  const programId = getURLParams('programId', true);

  useEffect(() => {
    // if (!filter.get('term')) {
    handleFilterChange(filter.get('term', '') || courseList.get('term', List()).get(0, ''), 'term');
    // }

    return () => {
      setFilter(filter.set('term', '').set('year', '').set('level', ''));
    };
    // eslint-disable-next-line
  }, [courseList]);

  function handleFilterChange(value, name) {
    const commonParams = ['year', 'level', 'courseId', 'courseName'];
    if (name === 'term') {
      setFilter(filter.merge(Map({ term: value, year: '', level: '' })));
      history.replace(
        `/course-scheduling/schedule/list/${removeURLParams(location, [
          'activeYear',
          'activeLevel',
          'term',
          ...commonParams,
        ])}&term=${eString(value)}`
      );
    } else {
      setFilter(filter.merge(Map({ [name]: value, ...(name === 'year' && { level: '' }) })));
      const key = `active${capitalize(name)}`;
      history.replace(
        `/course-scheduling/schedule/list/${removeURLParams(location, [key, ...commonParams])}${
          value ? ['&', key, '=', eString(value)].join('') : ''
        }`
      );
    }
  }

  function handleSortDirectionChange() {
    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
  }

  function getLevels() {
    return courseList
      .get('courses', List())
      .filter((level) => {
        const term = filter.get('term');
        const year = filter.get('year');
        const levelNo = filter.get('level');
        if (!term && !year && !level) {
          return true;
        }
        let incTerm = true;
        let incYear = true;
        let incLevel = true;
        if (term) {
          incTerm = level.get('term') === term;
        }
        if (year) {
          incYear = level.get('year') === year;
        }
        if (levelNo) {
          incLevel = level.get('level_no') === levelNo;
        }
        return incTerm && incYear && incLevel;
      })
      .sort((l1, l2) => {
        const v1 = `${l1.get('year', '')} - ${l1.get('level_no', '')}`;
        const v2 = `${l2.get('year', '')} - ${l2.get('level_no', '')}`;
        if (sortDirection === 'asc') {
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        }
        return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v2, v1);
      })
      .toList();
  }

  function handleCourseClick(level, course, studentGroupStatus, scheduledStatus) {
    const versionName = getVersionName(course);
    history.replace(
      `/course-scheduling/schedule/list/${removeURLParams(location, [
        'year',
        'level',
        'courseId',
        'courseName',
        'term',
        'studentGroupStatus',
        'scheduledStatus',
      ])}&year=${eString(level.get('year'))}&level=${eString(
        level.get('level_no')
      )}&courseId=${eString(course.get('_course_id'))}&courseName=${eString(
        course.get('courses_name')
      )}&term=${eString(level.get('term'))}&studentGroupStatus=${eString(
        String(studentGroupStatus)
      )}&scheduledStatus=${eString(String(scheduledStatus))}&versionName=${eString(versionName)}`
    );
    setData(
      Map({
        activeTab: 'Schedule',
        courseSchedule: Map(),
        paginationMetaData: Map({
          totalPages: null,
          currentPage: null,
        }),
      })
    );
  }
  return (
    <div className="p-4">
      <div className="container">
        <div className="bg-white border-radious-8">
          <div className="p-3">
            <YearLevelFilters
              showCourse={false}
              data={courseList}
              filter={filter}
              handleChange={handleFilterChange}
              sortDirection={sortDirection}
              handleSortDirectionChange={handleSortDirectionChange}
              programId={programId}
            />
            <div className="mt-3">
              {getLevels().isEmpty() && <div className="mt-3 text-center">{t('no_data')}</div>}
              {getLevels().map((level) => {
                const key = `${level.get('term', '')}-${level.get('year', '')}-${level.get(
                  'level_no',
                  ''
                )}`;
                const formattedYear = level.get('year', '').split('year').join('Year ');
                const levelNo = level.get('level_no', '');
                const courseCount = level.get('courses', List()).size;
                const defaultExpanded =
                  level.get('year') === getURLParams('year', true) &&
                  levelNo === getURLParams('level', true);

                return (
                  <Accordion key={key} square defaultExpanded={defaultExpanded}>
                    <AccordionSummary
                      expandIcon={<i className="fa fa-chevron-down f-14"></i>}
                      aria-controls={`${key}-content`}
                      id={`${key}-header`}
                    >
                      <Typography className={classes.heading}>
                        {`${formattedYear.replace('Year', t('year'))} - ${levelRename(
                          levelNo,
                          programId
                        )} - ${courseCount} ${
                          courseCount <= 1 ? t('program_calendar.course') : t('courses')
                        }`}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      {level.get('courses', List()).isEmpty() && (
                        <div className="mt-3 mb-3 text-center">
                          {t('program_input.error_strings.no_courses_found')}
                        </div>
                      )}
                      {level.get('courses', List()).map((course, i) => (
                        <div
                          key={`${key}-${course.get('_course_id')}-${i}`}
                          className="course-item"
                        >
                          <div className="row no-gutters course-row p-3 mt-2 mb-2 align-items-center">
                            <div className="col-md-5">
                              <div className="bold mb-1 f-15">
                                {course.get('isShared') && (
                                  <span className="shared-course-chip mr-2">S</span>
                                )}
                                <span>
                                  {course.get('courses_number', '')} -{' '}
                                  {course.get('courses_name', '')}
                                  {getVersionName(course)}
                                </span>
                              </div>
                              <div>
                                {`${formattedYear.replace('Year', t('year'))} • ${levelRename(
                                  levelNo,
                                  programId
                                ).replace(
                                  'Level',
                                  t('constant.level', {
                                    name: 'Level',
                                  })
                                )} • ${getTranslatedDuration(
                                  format(new Date(course.get('start_date')), 'MMM dd')
                                )} - ${getTranslatedDuration(
                                  format(new Date(course.get('end_date')), 'MMM dd')
                                )}`}
                              </div>
                            </div>
                            <div className="col-md-5">
                              <p className="bold mb-1 f-15">{t('regular_session')}</p>
                              <span className="pr-2">
                                {`${course
                                  .get('delivery_symbol', List())
                                  .join(' | ')} - ${course.get('no_session', 0)} ${
                                  course.get('no_session', 0) <= 1 ? t('session') : t('sessions')
                                }`}
                              </span>
                            </div>
                            <div
                              className="col-md-2 text-uppercase f-16 bold cursor-pointer"
                              onClick={() => {
                                if (
                                  CheckPermission(
                                    'subTabs',
                                    'Schedule Management',
                                    'Course Scheduling',
                                    '',
                                    'Schedule',
                                    '',
                                    'Course Schedule',
                                    'View'
                                  )
                                ) {
                                  handleCourseClick(
                                    level,
                                    course,
                                    course.get('student_group_status', false),
                                    course.get('schedule_status', false)
                                  );
                                } else {
                                  setData(Map({ message: 'Permission Not Assigned' }));
                                }
                              }}
                            >
                              {course.get('student_group_status') ? (
                                course.get('schedule_status') ? (
                                  <div className="text-center text-darkgray">
                                    <img src={CheckIcon} alt="check" className="img-fluid" />
                                    Scheduled
                                  </div>
                                ) : (
                                  <div className="text-center text-skyblue">
                                    {t('role_management.tabs.Schedule')}
                                  </div>
                                )
                              ) : (
                                <div className="text-center text-red">
                                  {t('student_group_not_created')}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </AccordionDetails>
                  </Accordion>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

YearLevelCourse.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  courseList: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
};

export default withRouter(YearLevelCourse);
