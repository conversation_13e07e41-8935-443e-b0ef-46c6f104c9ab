<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Automatic scheduling system designed uniquely for education institutions"
    />
    <!-- <link rel="icon" href="%PUBLIC_URL%/favicon.png" /> -->
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link
      rel="stylesheet"
      href="https://use.fontawesome.com/releases/v5.8.1/css/all.css"
      integrity="sha384-50oBUHEmvpQ+1lW4y57PTFmhCaXp0ML5d60M1M7uH2+nqUivzIebhndOJK28anvf"
      crossorigin="anonymous"
    />
    <link
      rel="stylesheet"
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
      integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm"
      crossorigin="anonymous"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    />
    <!-- <link rel="manifest" href="%PUBLIC_URL%/manifest.json" /> -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;0,700;0,900;1,400;1,500;1,700;1,900&display=swap"
      rel="stylesheet"
    />
    <!-- <script src="https://wchat.freshchat.com/js/widget.js"></script> -->
    <!-- <script>
      function getCookie(cname) {
        let name = cname + '=';
        let decodedCookie = decodeURIComponent(document.cookie);
        let ca = decodedCookie.split(';');
        for (let i = 0; i < ca.length; i++) {
          let c = ca[i];
          while (c.charAt(0) == ' ') {
            c = c.substring(1);
          }
          if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
          }
        }
        return '';
      }
    </script>
    <script>
      if (getCookie('version') !== '/v2') {
        window.fwSettings = {
          widget_id: 82000004936,
        };
        !(function () {
          if ('function' != typeof window.FreshworksWidget) {
            var n = function () {
              n.q.push(arguments);
            };
            (n.q = []), (window.FreshworksWidget = n);
          }
        })();
      }
    </script> -->
    <!-- <script>
      if (getCookie('version') !== '/v2') {
        var headWidget = document.getElementsByTagName('head')[0];
        var jsWidget = document.createElement('script');
        jsWidget.type = 'text/javascript';
        jsWidget.src = 'https://wchat.freshchat.com/js/widget.js';
        headWidget.appendChild(jsWidget);

        var head = document.getElementsByTagName('head')[0];
        var js = document.createElement('script');
        js.type = 'text/javascript';
        js.async = true;
        js.defer = true;
        js.src = 'https://ind-widget.freshworks.com/widgets/82000004936.js';
        head.appendChild(js);
      }
    </script> -->
    <!-- <script
      type="text/javascript"
      src="https://ind-widget.freshworks.com/widgets/82000004936.js"
      async
      defer
    ></script> -->
    <!-- <style type="text/css" media="screen, projection">
      @import url(https://s3.amazonaws.com/assets.freshdesk.com/widget/freshwidget.css);
    </style> -->

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>DigiScheduler</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add web fonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
