import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { Input, Select, Label, FlexWrapper } from '../../Styled';
import DateInput from '../../UtilityComponents/DateInput';
import moment from 'moment';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
const AddEvent = ({ data, method, min_len, max_len, levelStartDate, levelEndDate }) => {
  let edit_start_date = '';
  let edit_end_date = '';
  if (data._id !== undefined) {
    edit_start_date = moment(data.start_time).format('hh:mm A');
    edit_end_date = moment(data.end_time).format('hh:mm A');
  }
  const isEdit = data._id !== undefined;
  return (
    <Fragment>
      <FlexWrapper className="column" mg="20px 30px">
        <Label>
          <Trans i18nKey={'events.event_type'}></Trans>
        </Label>
        <Select
          name="event_type"
          onChange={(e) =>
            method({
              type: 'ON_TYPE',
              payload: e.target.value,
              name: e.target.name,
            })
          }
          value={data.event_type}
          id=""
        >
          <option value="exam">{t('events.event_types.exam')}</option>
          <option value="holiday">{t('events.event_types.holiday')}</option>
          <option value="training">{t('events.event_types.training')}</option>
          <option value="orientation">{t('events.event_types.orientation')}</option>
          <option value="general">{t('events.event_types.general')}</option>
        </Select>
      </FlexWrapper>
      <FlexWrapper className="column" mg="20px 30px">
        <Label>
          <Trans i18nKey={'event_title'}></Trans>
        </Label>
        <Input
          type="text"
          name="title"
          onChange={(e) =>
            method({
              type: 'ON_TYPE',
              payload: e.target.value,
              name: e.target.name,
            })
          }
          value={data.title}
        />
      </FlexWrapper>
      <FlexWrapper>
        <FlexWrapper className="column" mg="20px 30px">
          <DateInput
            datepicker={true}
            placeholderText={t('events.start_date')}
            selected={data.start_date !== '' ? new Date(data.start_date) : null}
            value={data.start_date}
            edit={(value) => {
              method({
                type: 'ON_TYPE',
                payload: value,
                name: 'start_date',
              });
            }}
            title={t('events.start_date')}
            minDate={
              levelStartDate !== ''
                ? new Date(levelStartDate)
                : min_len !== ''
                ? new Date(min_len)
                : ''
            }
            maxDate={
              isEdit && max_len !== ''
                ? new Date(max_len)
                : levelEndDate !== ''
                ? new Date(levelEndDate)
                : max_len !== ''
                ? new Date(max_len)
                : ''
            }
          />

          {/* <Label>Start date</Label>
          <Input
            type="date"
            name="start_date"
            className="sd"
            placeholder="Start date"
            min={String(min_len).slice(0, 10)}
            max={String(max_len).slice(0, 10)}
            onChange={(e) =>
              method({
                type: "ON_TYPE",
                payload: e.target.value,
                name: e.target.name,
              })
            }
            value={data.start_date}
          /> */}
        </FlexWrapper>
        <FlexWrapper className="column" mg="20px 30px">
          {edit_start_date === '' ? (
            <DateInput
              timepicker={true}
              placeholderText={t('events.start_time')}
              selected={data.start_time !== '' ? new Date(data.start_time) : null}
              value={data.start_time}
              edit={(value) => {
                method({
                  type: 'ON_TYPE',
                  payload: value,
                  name: 'start_time',
                });
              }}
              title={t('events.start_time')}
            />
          ) : (
            <DateInput
              timepicker={true}
              placeholderText={t('events.start_time')}
              selected={data.start_time !== '' ? new Date(data.start_time) : null}
              value={edit_start_date}
              edit={(value) => {
                method({
                  type: 'ON_TYPE',
                  payload: value,
                  name: 'start_time',
                });
              }}
              title={t('events.start_time')}
            />
          )}

          {/* <Label>Start time</Label>
          <Input
            type="time"
            name="start_time"
            onChange={(e) =>
              method({
                type: "ON_TYPE",
                payload: e.target.value,
                name: e.target.name,
              })
            }
            value={data.start_time}
          /> */}
        </FlexWrapper>
      </FlexWrapper>
      <FlexWrapper>
        <FlexWrapper className="column" mg="20px 30px">
          <DateInput
            datepicker={true}
            placeholderText={t('events.end_date')}
            selected={data.end_date !== '' ? new Date(data.end_date) : null}
            value={data.end_date}
            edit={(value) => {
              method({
                type: 'ON_TYPE',
                payload: value,
                name: 'end_date',
              });
            }}
            title={t('events.end_date')}
            minDate={
              data.start_date !== ''
                ? new Date(data.start_date)
                : levelStartDate !== ''
                ? new Date(levelStartDate)
                : ''
            }
            maxDate={
              isEdit && max_len !== ''
                ? new Date(max_len)
                : levelEndDate !== ''
                ? new Date(levelEndDate)
                : max_len !== ''
                ? new Date(max_len)
                : ''
            }
          />

          {/* <Label>End date</Label>
          <Input
            type="date"
            name="end_date"
            min={
              data.start_date !== "" ? String(data.start_date).slice(0, 10) : ""
            }
            max={String(max_len).slice(0, 10)}
            onChange={(e) =>
              method({
                type: "ON_TYPE",
                payload: e.target.value,
                name: e.target.name,
              })
            }
            value={data.end_date}
          /> */}
        </FlexWrapper>
        <FlexWrapper className="column" mg="20px 30px">
          {edit_end_date === '' ? (
            <DateInput
              timepicker={true}
              placeholderText={t('events.end_time')}
              selected={data.end_time !== '' ? new Date(data.end_time) : null}
              value={data.end_time}
              edit={(value) => {
                method({
                  type: 'ON_TYPE',
                  payload: value,
                  name: 'end_time',
                });
              }}
              title={t('events.end_time')}
            />
          ) : (
            <DateInput
              timepicker={true}
              placeholderText={t('events.end_time')}
              selected={data.end_time !== '' ? new Date(data.end_time) : null}
              value={edit_end_date}
              edit={(value) => {
                method({
                  type: 'ON_TYPE',
                  payload: value,
                  name: 'end_time',
                });
              }}
              title={t('events.end_time')}
            />
          )}

          {/*
           */}

          {/* <Label>End time</Label>
          <Input
            type="time"
            name="end_time"
            onChange={(e) =>
              method({
                type: "ON_TYPE",
                payload: e.target.value,
                name: e.target.name,
              })
            }
            value={data.end_time}
          /> */}
        </FlexWrapper>
      </FlexWrapper>
      <FlexWrapper mg="0px 30px">
        <Label>
          {(() => {
            if (data.start_date !== '' && data.end_date !== '') {
              let num = (Date.parse(data.end_date) - Date.parse(data.start_date)) / 86400000 + 1;
              if (num === 1) {
                return `${t('total_no_of_day')}: ${num}`;
              } else if (num > 1) {
                return `${t('total_no_of_days')}: ${num}`;
              } else {
                return `${t('total_no_of_days')}: wrong dates`;
              }
            } else {
              return `${t('total_no_of_days')}: 0`;
            }
          })()}
        </Label>
      </FlexWrapper>
    </Fragment>
  );
};

AddEvent.propTypes = {
  data: PropTypes.object,
  method: PropTypes.func,
  min_len: PropTypes.string,
  max_len: PropTypes.string,
  levelStartDate: PropTypes.string,
  levelEndDate: PropTypes.string,
};

export default AddEvent;
