import React, { useEffect, useState } from 'react';
// import Modal from '@mui/material/Modal';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import { Map } from 'immutable';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import MButton from 'Widgets/FormElements/material/Button';
import { isAlphaNumericWithSpace } from 'v2/utils';

function AddEditEventModal(props) {
  const {
    open,
    handleClose,
    title,
    item,
    updateEventType,
    settingId,
    setData,
    institutionHeader,
    updateProgramBasicDetails,
    getProgramID,
    institutionId,
    CallingProgramSettings,
    programID,
  } = props;
  const id = item.get('_id', '');
  const [state, setState] = useState(Map());
  useEffect(() => {
    if (item.size) {
      setState(item);
    }
  }, [item]);
  const handleChange = (name, event) => {
    if (name === 'name') setState(state.set('name', event));
    if (name === 'leave') setState(state.set('isLeave', event === 'Yes' ? true : false));
  };
  const handleSave = () => {
    if (isAlphaNumericWithSpace(state.get('name', '').trim())) {
      !programID
        ? updateEventType({
            operation: title === 'edit' ? 'update' : 'create',
            settingId,
            name: state.get('name', '').trim(),
            isLeave: state.get('isLeave', false),
            ...(title === 'edit' && { id }),
            callBack: () => handleClose(),
            header: institutionHeader,
          })
        : updateProgramBasicDetails({
            operation: title === 'edit' ? 'update' : 'create',
            urlEventAdd: `program-input/add-eventtype`,
            urlEventEdit: `program-input/edit-eventtype/${id}`,
            name: state.get('name', '').trim(),
            isLeave: state.get('isLeave', false),
            programId: getProgramID,
            callBack: () => handleClose(),
            header: institutionHeader,
            CallingProgramSettings: CallingProgramSettings,
            id: id,
            institutionId: institutionId,
          });
    } else {
      setData(Map({ message: t('global_configuration.event_name_alpha_numeric') }));
    }
  };
  return (
    <div>
      <Modal show={open} centered>
        <Modal.Header className="border-none pb-0">
          <Modal.Title className="f-20">
            {title === 'edit'
              ? t('global_configuration.edit_event')
              : t('global_configuration.add_event')}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="pt-4">
          <div className="mb-3">
            <FormLabel component="legend">
              {' '}
              <Trans i18nKey={'global_configuration.event_name'}></Trans>
            </FormLabel>
            <FormControl variant="outlined" className="wd-100">
              <OutlinedInput
                defaultValue={title === 'edit' ? item.get('name', '') : ''}
                id="outlined-adornment-weight"
                className="outline-text-input"
                onChange={(e) => handleChange('name', e.target.value)}
                labelWidth={0}
                placeholder={t('global_configuration.event_name')}
                inputProps={{ maxLength: 60 }}
              />
            </FormControl>
          </div>
          <div>
            <FormControl component="fieldset">
              <FormLabel component="legend">
                <div className="mt-2">
                  {' '}
                  <Trans i18nKey={'global_configuration.leave'}></Trans>
                </div>
              </FormLabel>
              <RadioGroup
                row
                aria-label="position"
                name="position"
                defaultValue={
                  title === 'edit' ? (item.get('isLeave', true) === true ? 'Yes' : 'No') : 'No'
                }
                onChange={(e) => handleChange('leave', e.target.value)}
              >
                <FormControlLabel
                  value="Yes"
                  control={<Radio color="primary" />}
                  label={t('yes')}
                />
                <FormControlLabel value="No" control={<Radio color="primary" />} label={t('no')} />
              </RadioGroup>
            </FormControl>
          </div>
        </Modal.Body>

        <Modal.Footer className="border-none">
          <b className="pr-2">
            <MButton color="gray" variant="outlined" clicked={handleClose}>
              <Trans i18nKey={'cancel'}></Trans>
            </MButton>
          </b>
          <b className="pr-2">
            <MButton clicked={handleSave} disabled={!state.get('name', '').length}>
              <Trans i18nKey={'save'}></Trans>
            </MButton>
          </b>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

AddEditEventModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  updateEventType: PropTypes.func,
  title: PropTypes.string,
  settingId: PropTypes.string,
  item: PropTypes.object,
  setData: PropTypes.func,
  updateProgramBasicDetails: PropTypes.func,
  institutionHeader: PropTypes.object,
  getProgramID: PropTypes.string,
  programID: PropTypes.string,
  institutionId: PropTypes.string,
  CallingProgramSettings: PropTypes.func,
};

export default AddEditEventModal;
