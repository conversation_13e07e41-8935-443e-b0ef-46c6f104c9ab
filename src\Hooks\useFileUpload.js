import { useState } from 'react';
import axios from 'axios';
import { useDispatch } from 'react-redux';
import { generateSignedUrl } from '_reduxapi/program_input/action';
import { isList } from 'immutable';
import useUnifyServiceHook from './useUnifyServiceHook';

// Utils
const ERROR_MESSAGE = 'Error while uploading file';
const defaultPath = 'digiclass';
const FILE_UPLOAD_API = process.env.REACT_APP_UNIFY_FILE_UPLOAD_API;

const formatFileMetadata = (file) => {
  const originalFileName = file.name.substring(0, file.name.lastIndexOf('.'));
  const fileName = originalFileName
    .replace(/[()]/g, '')
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '_')
    .replace(/_+/g, '_')
    .trim();
  return {
    fileName: `${Date.now()}-${fileName}`,
    fileType: file.name.split('.').pop(),
    contentType: file.type,
    file,
  };
};

const folderPaths = {
  assignment: process.env.REACT_APP_ASSIGNMENT_BUCKET_PATH,
  sessionOrderDocuments: process.env.REACT_APP_SESSION_ORDER_BUCKET_PATH,
  q360: process.env.REACT_APP_Q360_BUCKET_PATH,
  outSideCampus: process.env.REACT_APP_OUT_SIDE_CAMPUS_BUCKET_PATH,
  userData: process.env.REACT_APP_USER_DATA_BUCKET_PATH,
  documents: process.env.REACT_APP_DOCUMENT_BUCKET_PATH,
  activities: process.env.REACT_APP_ACTIVITIES_BUCKET_PATH,
};

const getFolderPath = (component) => folderPaths[component] || defaultPath;

const useFileUpload = () => {
  const dispatch = useDispatch();
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState({});
  const { unifyData } = useUnifyServiceHook();
  const fileUploadUrl = unifyData.get('fileUploadUrl', '');
  const storageBucket = unifyData.get('storageBucket', '');
  const imageUploadUrl = fileUploadUrl ? `${fileUploadUrl}/api/v1/upload` : FILE_UPLOAD_API;

  const upload = async ({
    files,
    component = 'assignment',
    options = {},
    folderPathExtension = '',
  }) => {
    if (!files || (isList(files) ? files.size === 0 : files.length === 0)) {
      throw new Error('No files selected');
    }

    setUploading(true);
    setError('');
    setUploadedFiles([]);
    setUploadProgress({});

    try {
      const componentFolder = getFolderPath(component);
      const folderPath =
        folderPathExtension !== '' ? `${componentFolder}/${folderPathExtension}` : componentFolder;
      const results = await Promise.all(
        Array.from(files).map(async (file) => {
          const { fileName, fileType, contentType, file: fileObj } = formatFileMetadata(file);

          // Get signed URL
          const { url, unSignedURL } = await axios
            .get(imageUploadUrl, {
              params: {
                fileName,
                fileType,
                app: 'DC',
                folderName: folderPath,
                contentType,
                bucket: storageBucket,
              },
            })
            .then((res) => res.data.data);

          // Upload file to cloud
          await axios.put(url, fileObj, {
            headers: { 'Content-Type': contentType },
            onUploadProgress: (progressEvent) => {
              if (options.onUploadProgress) {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
                options.onUploadProgress({
                  loaded: progressEvent.loaded,
                  total: progressEvent.total,
                  percent: percentCompleted,
                  file: fileObj,
                });
              }
              setUploadProgress((prev) => ({
                ...prev,
                [file.name]: {
                  loaded: progressEvent.loaded,
                  total: progressEvent.total,
                  percent: Math.round((progressEvent.loaded * 100) / progressEvent.total),
                  file: fileObj,
                },
              }));
            },
          });

          return new Promise((resolve) =>
            dispatch(
              generateSignedUrl(unSignedURL, (signedUrl) =>
                resolve({
                  signedUrl: signedUrl,
                  unSignedUrl: unSignedURL,
                  name: fileName,
                  size: fileObj.size,
                  type: fileType,
                })
              )
            )
          );
        })
      );

      setUploadedFiles(results);
      return results;
    } catch (err) {
      console.error(err);
      setError(err.message || ERROR_MESSAGE);
      throw err;
    } finally {
      setUploading(false);
    }
  };

  const reset = () => {
    setError('');
    setUploadedFiles([]);
    setUploadProgress({});
  };

  return {
    uploading,
    error,
    fileData: uploadedFiles,
    uploadProgress,
    upload,
    reset,
  };
};

export default useFileUpload;
