import React, { Component, useState } from 'react';
import { List, Map, fromJS } from 'immutable';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Button, Form } from 'react-bootstrap';
import {
  Checkbox,
  FormControl,
  InputLabel,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Select,
} from '@mui/material';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { Trans, withTranslation, useTranslation } from 'react-i18next';
import SessionFlow from './SessionFlow';
import Steps from './Steps';
import {
  getSubjects,
  getCourseValidation,
  getSessionFlowValidation,
  getCreditHoursValidation,
  getAchieveTargetValidation,
} from './utils';
import Input from '../../../../Widgets/FormElements/Input/Input';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  selectProgram,
  selectProgramError,
  selectProgramDeptList,
  selectSessionTypeList,
  selectCurriculum,
  selectCourse,
  selectEditedCourse,
  selectActiveStep,
  selectSessionFlow,
  selectEditedSessionFlow,
  selectEditedAdditionalSessionFlow,
  selectDeliveringSubject,
  selectCourseSessionOrder,
  selectMaxHours,
  selectVersionCourseId,
} from '../../../../_reduxapi/program_input/selectors';
import { getRandomId } from '../../../InfrastructureManagement/utils';
import ArrowLeft from '../../../../Assets/arrow.svg';
import '../../css/program.css';
import {
  getURLParams,
  jsUcfirst,
  getLang,
  indVerRename,
  levelRename,
  removeURLParams,
  eString,
} from '../../../../utils';
import { t } from 'i18next';
const lang = getLang();

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const checkboxTheme = createTheme({
  palette: {
    primary: {
      main: '#3E95EF',
    },
  },
});

function isSelected(selected, option, key) {
  return Boolean(selected.find((s) => s === option[key]));
}

class AddCourseComponent extends Component {
  constructor(props) {
    super(props);
    const { t, versionCourseId } = this.props;
    const {
      match: {
        params: { curriculumId, courseId },
      },
    } = props;
    this.state = {
      curriculumId,
      courseId,
      versionCourseId: versionCourseId,
      STEPS: [t('constant.course_details'), t('constant.session_flow')],
      EDIT_CREDIT_HOURS: [
        { name: t('allow_editing'), value: true },
        { name: t('dont_allow'), value: false },
      ],
      COURSE_TYPES: [
        { name: t('constant.standard'), value: 'standard' },
        { name: t('constant.selective'), value: 'selective' },
      ],
    };
    this.handleBackClick = this.handleBackClick.bind(this);
    this.handleChange = this.handleChange.bind(this);
    this.handleRemove = this.handleRemove.bind(this);
    this.handleNextStep = this.handleNextStep.bind(this);
    this.handlePrevStep = this.handlePrevStep.bind(this);
    this.handleSaveAsDraft = this.handleSaveAsDraft.bind(this);
    this.getCourseSessionOrderModule = this.getCourseSessionOrderModule.bind(this);
    this.postCourseSessionOrderModule = this.postCourseSessionOrderModule.bind(this);
  }

  componentDidMount() {
    const { curriculumId, courseId, versionCourseId } = this.state;
    let programId = getURLParams('_id', true);

    const versionName = getURLParams('versionName', true);
    const versionType = getURLParams('versionType', true);

    this.props.getProgramDeptList();
    this.props.getDeliveringSubject(programId);
    this.props.getCurriculum(curriculumId, courseId === 'new');
    if (courseId !== 'new') {
      this.props.getCourse(
        versionType === 'update' ? courseId : versionName ? versionCourseId || courseId : courseId
      );
      if (getURLParams('versionType', true) !== 'create') {
        this.props.getsessionFlow(
          versionType === 'update' ? courseId : versionName ? versionCourseId || courseId : courseId
        );
      }
    }
    this.props.setData(
      Map({
        activeStep: 0,
        course: Map(),
        editedCourse: Map({
          course_type: '',
          course_recurring: List(),
          course_occurring: List(),
          course_code: '',
          course_name: '',
          duration: '',
          participating: List(),
          administration: Map(),
          credit_hours: List(),
          allow_editing: '',
          achieve_target: '',
        }),
        editedSessionFlow: List([
          Map({
            s_no: 1,
            _session_id: '',
            delivery_type: '',
            _delivery_id: '',
            delivery_symbol: '',
            delivery_no: '',
            delivery_topic: '',
            subjects: List(),
            duration: '',
            _id: getRandomId(),
            _module_id: '',
          }),
        ]),
        // editedAdditionalSessionFlow: List([
        //   Map({
        //     s_no: 1,
        //     _session_id: '',
        //     delivery_type: '',
        //     _delivery_id: '',
        //     delivery_symbol: '',
        //     delivery_no: '',
        //     delivery_topic: '',
        //     subjects: List(),
        //     duration: '',
        //     _id: getRandomId(),
        //     _module_id: '',
        //   }),
        // ]),
        max_hours: '',
        sessionFlow: Map(),
      })
    );
  }

  scrollTop() {
    try {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } catch (error) {
      window.scrollTo = 0;
    }
  }

  handleSaveAsDraft() {
    const { activeStep } = this.props;
    switch (activeStep) {
      case 0:
        this.saveCourseDetails(false);
        break;
      case 1:
        this.saveSessionFlowDetails(false);
        break;
      default:
        this.props.setData(Map({ message: 'Unknown action' }));
        break;
    }
  }

  handleNextStep() {
    const { activeStep } = this.props;
    switch (activeStep) {
      case 0:
        this.saveCourseDetails(true);
        break;
      case 1:
        this.saveSessionFlowDetails(true);
        break;
      default:
        this.props.setData(Map({ message: 'Unknown action' }));
        break;
    }
  }

  saveCourseDetails(isActive = true) {
    const { curriculum, course, editedCourse, resetMessage, history, location } = this.props;
    const operation = course.isEmpty() ? 'create' : 'update';
    const requestBody = Map({
      _program_id: curriculum.get('_program_id'),
      _curriculum_id: curriculum.get('_id'),
      course_type: editedCourse.get('course_type'),
      ...(editedCourse.get('course_type') === 'selective' && {
        course_recurring: this.getSelected('course_recurring', '_id', true).map((l) =>
          Map({
            level_no: l.get('level_name'),
            isAssigned: true,
            _level_id: l.get('_id'),
            _year_id: l.get('_year_id'),
            year_no: l.get('year_no'),
          })
        ),
      }),
      ...(editedCourse.get('course_occurring') !== 'selective' && {
        course_occurring: this.getSelected('course_occurring', '_id', true).map((l) =>
          Map({
            level_no: l.get('level_name'),
            isAssigned: true,
            _level_id: l.get('_id'),
            _year_id: l.get('_year_id'),
            year_no: l.get('year_no'),
          })
        ),
      }),
      course_code: editedCourse.get('course_code').trim(),
      course_name: editedCourse.get('course_name').trim(),
      duration: editedCourse.get('duration'),
      participating: this.getSelected('participating', '_subject_id', false).map((p) =>
        p.delete('name').delete('value')
      ),
      administration: editedCourse.get('administration').delete('name').delete('value'),
      credit_hours: editedCourse.get('credit_hours'),
      allow_editing:
        editedCourse.get('allow_editing') === '' ? null : editedCourse.get('allow_editing'),
      achieve_target:
        editedCourse.get('achieve_target') === '' ? null : editedCourse.get('achieve_target'),
      isActive,
      ...(getURLParams('versionName', true) && {
        versioned: true,
        versionName: getURLParams('versionName', true),
        versionedFrom: this.state.courseId,
        versionedCourseIds: [],
      }),
    }).toJS();

    const validation = getCourseValidation(requestBody, !isActive);
    if (validation.length) {
      resetMessage(`${validation[0]} ${t('messages.is_req')}`);
      return;
    }
    const creditHoursValidation = getCreditHoursValidation(requestBody.credit_hours, !isActive);
    if (creditHoursValidation.length) {
      resetMessage(creditHoursValidation.toString());
      return;
    }
    this.props.saveCourse({
      operation,
      _id: course.get('_id', ''),
      requestBody,
      url: `/program-input/configuration/course-masterlist${this.props.location.search}&courseActiveTab=1`,
      history,
      isActive,
      isVersion: getURLParams('versionName', true) ? true : false,
      versionType: getURLParams('versionType', true),
      callback: (courseId) => this.updateQueryParam({ courseId, history, location }),
    });
  }

  updateQueryParam({ courseId, history, location }) {
    const { search } = location;
    const { curriculumId } = this.state;
    let searchParams = new URLSearchParams(search);
    searchParams.set('versionType', eString('update'));
    history.replace(
      `/program-input/curriculum/${curriculumId}/course/${courseId}?${searchParams.toString()}`
    );
  }

  saveSessionFlowDetails(isActive = true) {
    const {
      curriculum,
      course,
      sessionFlow,
      editedSessionFlow,
      resetMessage,
      max_hours,
      editedAdditionalSessionFlow,
    } = this.props;
    const operation = sessionFlow.isEmpty() ? 'create' : 'update';
    const subjects = fromJS(getSubjects(course));
    const { versionCourseId } = this.state;
    const versionName = getURLParams('versionName', true);
    const versionType = getURLParams('versionType', true);

    const sessionFlowData = editedSessionFlow.map((s, i) => {
      return Map({
        s_no: i + 1,
        _session_id: s.get('_session_id'),
        delivery_type: s.get('delivery_type'),
        _delivery_id: s.get('_delivery_id'),
        delivery_symbol: s.get('delivery_symbol'),
        delivery_no: s.get('delivery_no'),
        delivery_topic: s?.get('delivery_topic').trim(),
        subjects: s.get('subjects').map((subjectId) => {
          const subject = subjects.find((sub) => sub.get('_subject_id') === subjectId);

          return subject.delete('name').delete('value');
        }),
        duration: s.get('duration'),
        ...(s.get('_id', '').length > 15 && { _id: s.get('_id') }),
        ...(s.get('_module_id', '') !== '' && { _module_id: s.get('_module_id', '') }),
        sessionData: s.get('sessionData', ''),
        sessionDocumentDetails: s.get('sessionDocumentDetails', List()),
      });
    });

    const additionalSessionFlowData = editedAdditionalSessionFlow.map((s, i) =>
      Map({
        s_no: i + 1,
        _session_id: s.get('_session_id'),
        delivery_type: s.get('delivery_type'),
        _delivery_id: s.get('_delivery_id'),
        delivery_symbol: s.get('delivery_symbol'),
        delivery_no: s.get('delivery_no'),
        delivery_topic: s.get('delivery_topic').trim(),
        subjects: s.get('subjects').map((subjectId) => {
          const subject = subjects.find((sub) => sub.get('_subject_id') === subjectId);
          return subject.delete('name').delete('value');
        }),
        duration: s.get('duration'),
        ...(s.get('_id', '').length > 15 && { _id: s.get('_id') }),
        ...(s.get('_module_id', '') !== '' && { _module_id: s.get('_module_id', '') }),
      })
    );

    const sessionFlowDataIds = sessionFlow
      .get('session_flow_data', List())
      .map((s) => s.get('_id'));
    const currentSessionFlowDataIds = sessionFlowData.map((s) => s.get('_id')).filter((id) => id);
    const deletedSessionFlowIds = sessionFlowDataIds.filter(
      (id) => !currentSessionFlowDataIds.includes(id)
    );
    const requestBody = Map({
      session_flow_data: sessionFlowData,
      additional_session_flow_data: additionalSessionFlowData,
      session_flow_deleted_ids: deletedSessionFlowIds,
      isActive,
      _course_id:
        versionType === 'update'
          ? course.get('_id')
          : versionName
          ? versionCourseId || course.get('_id')
          : course.get('_id'),
      _program_id: curriculum.get('_program_id'),
      ...(max_hours !== undefined &&
        max_hours !== '' && {
          max_hours: max_hours,
        }),
    }).toJS();
    const validationMessage = getSessionFlowValidation(requestBody.session_flow_data, !isActive);
    if (validationMessage.length) {
      resetMessage(validationMessage);
      return;
    }
    if (isActive) {
      const achieveTargetValidationMessage = getAchieveTargetValidation(
        course,
        fromJS(requestBody.session_flow_data)
      );
      if (achieveTargetValidationMessage.length) {
        resetMessage(achieveTargetValidationMessage);
        return;
      }
    }

    const versionTypeOperation = sessionFlow.isEmpty() ? 'create' : 'update';

    this.props.saveSessionFlow({
      operation,
      sessionFlowId: sessionFlow.get('_id', ''),
      courseId: course.get('_id', ''),
      url: `/program-input/configuration/course-masterlist${
        getURLParams('versionName', true)
          ? removeURLParams(this.props.history.location, ['versionName', 'versionType'])
          : this.props.location.search
      }`,
      history: this.props.history,
      requestBody,
      isActive,
      isVersion: getURLParams('versionName', true) ? true : false,
      versionType: versionTypeOperation,
    });
  }

  handlePrevStep() {
    const { activeStep } = this.props;
    this.props.setData(Map({ activeStep: activeStep - 1 }));
    this.scrollTop();
  }

  handleBackClick() {
    this.props.history.goBack();
  }

  getLevels() {
    const { curriculum } = this.props;
    let curriculumArray = [];
    if (curriculum.get('year_level', List()).size > 0) {
      curriculum.get('year_level', List()).map((item) => {
        const _pre_requisite_id = item.get('_pre_requisite_id', '');
        if (_pre_requisite_id === '') {
          item.get('levels', List()) &&
            item.get('levels', List()).size &&
            item.get('levels', List()).map((i) => {
              const yearLevelItem = {
                ...i.toJS(),
                _year_id: item.get('_id'),
                year_no: item.get('y_type'),
              };
              curriculumArray.push(yearLevelItem);
              return i;
            });
        }
        return item.get('levels');
      });
    }
    return curriculumArray;
  }

  handleChange(e, name) {
    let value;
    if (['course_type', 'allow_editing', 'achieve_target'].includes(name)) {
      value = e;
    } else {
      value = e.target.value;
    }

    if (['duration'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    const { editedCourse } = this.props;
    switch (name) {
      case 'course_type': {
        if (value === 'standard') {
          this.props.setData(
            Map({
              editedCourse: editedCourse
                .set(name, value)
                .set(
                  editedCourse.get('course_type') !== 'selective'
                    ? 'course_occurring'
                    : 'course_recurring',
                  List()
                ),
            })
          );
          break;
        } else {
          this.props.setData(
            Map({
              editedCourse: editedCourse.set(name, value),
            })
          );
          break;
        }
      }
      case 'course_name': {
        this.props.setData(
          Map({
            editedCourse: editedCourse.set(name, jsUcfirst(value)),
          })
        );
        break;
      }
      case 'course_recurring':
      case 'course_occurring':
      case 'participating': {
        this.props.setData(
          Map({
            editedCourse: editedCourse.set(name, fromJS(value)),
          })
        );
        break;
      }
      case 'administration': {
        const administration = this.getSubjects(true, 'admin_department').find(
          (s) => s.value === value
        );
        this.props.setData(
          Map({
            editedCourse: editedCourse.set('administration', fromJS(administration || {})),
          })
        );
        break;
      }
      default:
        this.props.setData(
          Map({
            editedCourse: editedCourse.set(name, value),
          })
        );
    }
  }

  getSelected(key, idKey, isLevel) {
    const { editedCourse } = this.props;
    const data = isLevel ? this.getLevels() : this.getSubjects(false, 'participation_subject');
    return editedCourse.get(key, List()).reduce((acc, id) => {
      const selected = data.find((l) => l[idKey] === id);
      return selected ? acc.push(Map(selected)) : acc;
    }, List());
  }

  handleRemove(key, id) {
    const { editedCourse } = this.props;
    const selected = editedCourse.get(key, List());
    this.props.setData(
      Map({
        editedCourse: editedCourse.set(
          key,
          selected.filter((sId) => sId !== id)
        ),
      })
    );
  }

  getSubjects(isAdministration, type) {
    const { deliveringSubjects } = this.props;
    let currentProgramId = getURLParams('_id', true);
    // const dList = programDeptList
    //   .reduce((acc, program) => {
    //     return acc.concat(
    //       program.get('department_data', List()).reduce((depAcc, department) => {
    //         const programId = department.get('program_id');
    //         const programName = department.get('program_name');
    //         const departmentId = department.get('_id');
    //         const departmentName = department.get('department_name');
    //         return depAcc.concat(
    //           department.get('subject', List()).reduce((subAcc, subject) => {
    //             const subjectId = subject.get('_id');
    //             const subjectName = subject.get('subject_name');
    //             const value = `${subjectName} | ${departmentName} | ${programName}`;
    //             return subAcc.push(
    //               Map({
    //                 _program_id: programId,
    //                 program_name: programName,
    //                 _department_id: departmentId,
    //                 department_name: departmentName,
    //                 _subject_id: subjectId,
    //                 subject_name: subjectName,
    //                 name: isAdministration ? value : subjectName,
    //                 value: isAdministration ? value : subjectName,
    //               })
    //             );
    //           }, List())
    //         );
    //       }, List())
    //     );
    //   }, List())
    //   .toJS();
    const dList = deliveringSubjects
      .get(type, List())
      .reduce((depAcc, department) => {
        const programId = department.get('program_id');
        const programName = department.get('program_name');
        const departmentId = department.get('_id');
        const departmentName = department.get('department_name');
        return depAcc.concat(
          department.get('subject', List()).reduce((subAcc, subject) => {
            const subjectId = subject.get('_id');
            const subjectName = subject.get('subject_name');
            const nonAdministratingSubjectName =
              currentProgramId === programId ? subjectName : `${subjectName} - ${departmentName}`;
            const value = `${subject.get('subject_name')} | ${departmentName} ${
              programName === null ? '' : '| ' + programName
            }`;
            return subAcc.push(
              Map({
                _program_id: programId,
                program_name: programName,
                _department_id: departmentId,
                department_name: departmentName,
                _subject_id: subjectId,
                subject_name: isAdministration ? subjectName : nonAdministratingSubjectName,
                name: isAdministration ? value : subjectName,
                value: isAdministration ? value : subjectName,
              })
            );
          }, List())
        );
      }, List())
      .toJS();
    const sList = [...dList];
    return sList.sort((a, b) => (a.subject_name > b.subject_name ? 1 : -1));
  }

  getSelectedAdministratingSubject() {
    const { editedCourse } = this.props;
    const subjects = this.getSubjects(true, 'admin_department');
    const selected = editedCourse.get('administration', Map());
    if (selected.isEmpty()) return '';
    const programId = editedCourse.getIn(['administration', '_program_id'], '');
    const departmentId = editedCourse.getIn(['administration', '_department_id'], '');
    const subjectId = editedCourse.getIn(['administration', '_subject_id'], '');
    let subject = subjects.find(
      (subject) =>
        subject._program_id === programId &&
        subject._department_id === departmentId &&
        subject._subject_id === subjectId
    );
    if (!subject) return '';
    subject = fromJS(subject);
    return `${subject.get('subject_name')} | ${subject.get('department_name')} | ${subject.get(
      'program_name'
    )}`;
  }

  getTotalHours(key) {
    const { editedCourse } = this.props;
    const hours = editedCourse.get('credit_hours', List()).map((c) => {
      const value = Number(c.get(key, '0') || '0');
      return key === 'contact_hours' ? Number(c.get('credit_hours', '0') || '0') * value : value;
    });
    return `${hours.reduce((acc, h) => h + acc, 0)} ${t('configuration.hours')} [${hours.join(
      '+'
    )}]`;
  }

  showSaveAsDraftButton() {
    const { activeStep, course, sessionFlow } = this.props;
    switch (activeStep) {
      case 0:
        return !course.get('isActive', false);
      case 1:
        return !sessionFlow.get('isActive', false);
      default:
        return true;
    }
  }

  getCourseSessionOrderModule() {
    const { courseId } = this.state;
    const { course } = this.props;
    let programId = getURLParams('_id', true);
    this.props.getSessionOrderModules({
      programId,
      courseId: courseId !== 'new' ? courseId : course.get('_id', ''),
    });
  }

  postCourseSessionOrderModule({ name, type, id, callBack }) {
    const { courseId } = this.state;
    const { course } = this.props;
    let programId = getURLParams('_id', true);
    const requestBody = {
      _program_id: programId,
      _course_id: courseId !== 'new' ? courseId : course.get('_id', ''),
      moduleName: name.trim(),
    };
    this.props.saveSessionOrderModules({ requestBody, type, id, callBack });
  }

  render() {
    const { courseId } = this.state;
    const {
      activeStep,
      editedCourse,
      course,
      sessionFlow,
      editedSessionFlow,
      courseSessionOrderModule,
      setData,
      max_hours,
      editedAdditionalSessionFlow,
    } = this.props;
    let programId = getURLParams('_id', true);

    const versionName = getURLParams('versionName', true);

    return (
      <React.Fragment>
        <div className="d-flex bg-white p-3">
          <div
            className={`f-20 digi-font-500 digi-black col-md-4 ${lang === 'ar' ? 'text-left' : ''}`}
            style={{ padding: '0px 8px' }}
          >
            <img
              src={ArrowLeft}
              alt="back"
              className="mr-3 cursor-pointer"
              onClick={this.handleBackClick}
            />
            <span>
              {courseId === 'new' ? (
                <Trans i18nKey={'add'}></Trans>
              ) : (
                <Trans i18nKey={'edit'}></Trans>
              )}{' '}
              <Trans i18nKey={'courses'}></Trans>
            </span>
          </div>
          <div className="col-md-8">
            <Steps steps={this.state.STEPS} activeStep={activeStep} />
          </div>
        </div>

        <div className="main bg-gray pb-5">
          <div className="bg-gray">
            <div className="container-fluid">
              <div className="row pb-4">
                <div className="col-md-12">
                  <div className="p-5">
                    <div className="d-flex justify-content-between">
                      <h5 className="pb-2 pt-2">
                        <Trans i18nKey={'course_master_list'}></Trans>{' '}
                        <i className="fa fa-angle-right pr-2 pl-2" aria-hidden="true"></i>
                        {courseId === 'new' ? (
                          <Trans i18nKey={'add'}></Trans>
                        ) : (
                          <Trans i18nKey={'edit'}></Trans>
                        )}{' '}
                      </h5>
                    </div>

                    <div className="program_card bg-white ">
                      <div className="p-5 min_h">
                        {activeStep === 0 && (
                          <>
                            <div className="d-flex justify-content-between mb-2">
                              <b>
                                <Trans i18nKey={'basic_details'}></Trans>
                              </b>
                              <small className="text-gray">
                                {' '}
                                <Trans i18nKey={'all_fields_mandatory'}></Trans>{' '}
                              </small>
                            </div>
                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  <Trans i18nKey={'course_type'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6 pt-1">
                                <div className="mt-4">
                                  {this.state.COURSE_TYPES.map((courseType, i) => (
                                    <Form.Check
                                      key={`${courseType.value}-${i}`}
                                      inline
                                      className={
                                        i !== this.state.COURSE_TYPES.length - 1 ? 'pr-5' : ''
                                      }
                                      label={indVerRename(courseType.name, programId)}
                                      type="radio"
                                      checked={editedCourse.get('course_type') === courseType.value}
                                      onChange={() =>
                                        this.handleChange(courseType.value, 'course_type')
                                      }
                                      // disabled={!course.isEmpty()}
                                    />
                                  ))}
                                </div>
                              </div>
                            </div>

                            {/* {editedCourse.get('course_type') === 'selective' && ( */}
                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  {' '}
                                  <Trans
                                    i18nKey={
                                      editedCourse.get('course_type') !== 'selective'
                                        ? 'course_occurring_at'
                                        : 'course_recurring_at'
                                    }
                                  ></Trans>{' '}
                                </label>
                              </div>
                              <div className="col-md-6">
                                <FormControl fullWidth variant="standard" size="small">
                                  <InputLabel id="level-label">
                                    <Trans i18nKey={'choose_levels'}></Trans>
                                  </InputLabel>
                                  <Select
                                    labelId="level-label"
                                    multiple
                                    value={editedCourse
                                      .get(
                                        editedCourse.get('course_type') !== 'selective'
                                          ? 'course_occurring'
                                          : 'course_recurring',
                                        List()
                                      )
                                      .toJS()}
                                    onChange={(e) =>
                                      this.handleChange(
                                        e,
                                        editedCourse.get('course_type') !== 'selective'
                                          ? 'course_occurring'
                                          : 'course_recurring'
                                      )
                                    }
                                    renderValue={(selected) =>
                                      this.getSelected(
                                        editedCourse.get('course_type') !== 'selective'
                                          ? 'course_occurring'
                                          : 'course_recurring',
                                        '_id',
                                        true
                                      )
                                        .filter((s) => selected.includes(s.get('_id')))
                                        .map((s) => levelRename(s.get('level_name', ''), programId))
                                        .join(', ')
                                    }
                                    MenuProps={MenuProps}
                                  >
                                    {this.getLevels().map((option) => (
                                      <MenuItem
                                        className="white-space-normal"
                                        key={option._id}
                                        value={option._id}
                                      >
                                        <ListItemIcon>
                                          <ThemeProvider theme={checkboxTheme}>
                                            <Checkbox
                                              color="primary"
                                              checked={isSelected(
                                                editedCourse
                                                  .get(
                                                    editedCourse.get('course_type') !== 'selective'
                                                      ? 'course_occurring'
                                                      : 'course_recurring',
                                                    List()
                                                  )
                                                  .toJS(),
                                                option,
                                                '_id'
                                              )}
                                            />
                                          </ThemeProvider>
                                        </ListItemIcon>
                                        <ListItemText
                                          primary={levelRename(option.level_name, programId)}
                                        />
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                                <div className="row pt-2 pl-3">
                                  {this.getSelected(
                                    editedCourse.get('course_type') !== 'selective'
                                      ? 'course_occurring'
                                      : 'course_recurring',
                                    '_id',
                                    true
                                  ).map((level) => (
                                    <div
                                      key={`level-chip-${level.get('_id', '')}`}
                                      className="pr-2 pb-2"
                                    >
                                      <b className="close_badge">
                                        {levelRename(level.get('level_name', ''), programId)}
                                        <i
                                          className="fa fa-times pl-2 cursor-pointer"
                                          aria-hidden="true"
                                          onClick={() =>
                                            this.handleRemove(
                                              editedCourse.get('course_type') !== 'selective'
                                                ? 'course_occurring'
                                                : 'course_recurring',
                                              level.get('_id')
                                            )
                                          }
                                        ></i>
                                      </b>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                            {/* )} */}

                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  {' '}
                                  <Trans i18nKey={'Course_Code'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <Input
                                  elementType="floatinginput"
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  value={editedCourse.get('course_code', '')}
                                  changed={(e) => this.handleChange(e, 'course_code')}
                                  disabled={versionName}
                                />
                              </div>
                            </div>

                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  {' '}
                                  <Trans i18nKey={'course_name_small'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <Input
                                  elementType="floatinginput"
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  value={editedCourse.get('course_name', '')}
                                  changed={(e) => this.handleChange(e, 'course_name')}
                                  disabled={versionName}
                                />
                              </div>
                            </div>
                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  <Trans i18nKey={'duration_in_weeks'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <Input
                                  elementType="floatinginput"
                                  elementConfig={{
                                    type: 'text',
                                  }}
                                  value={editedCourse.get('duration', '')}
                                  changed={(e) => this.handleChange(e, 'duration')}
                                />
                              </div>
                            </div>

                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  <Trans i18nKey={'delivering_subjects'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <FormControl fullWidth variant="standard" size="small">
                                  <InputLabel id="subject-label" className="floatinglabel">
                                    <Trans i18nKey={'choose_subject'}></Trans>
                                  </InputLabel>
                                  <Select
                                    labelId="subject-label"
                                    multiple
                                    value={editedCourse.get('participating', List()).toJS()}
                                    onChange={(e) => this.handleChange(e, 'participating')}
                                    renderValue={(selected) =>
                                      this.getSubjects(false, 'participation_subject')
                                        .filter((s) => selected.includes(s._subject_id))
                                        .map((s) => s.name)
                                        .join(', ')
                                    }
                                    MenuProps={MenuProps}
                                  >
                                    {this.getSubjects(false, 'participation_subject').map(
                                      (option) => (
                                        <MenuItem
                                          className="white-space-normal"
                                          key={option._subject_id}
                                          value={option._subject_id}
                                        >
                                          <ListItemIcon>
                                            <ThemeProvider theme={checkboxTheme}>
                                              <Checkbox
                                                color="primary"
                                                checked={isSelected(
                                                  editedCourse.get('participating', List()).toJS(),
                                                  option,
                                                  '_subject_id'
                                                )}
                                              />
                                            </ThemeProvider>
                                          </ListItemIcon>
                                          <ListItemText primary={option.name} />
                                        </MenuItem>
                                      )
                                    )}
                                  </Select>
                                </FormControl>
                                <div className="row pt-2 pl-3">
                                  {this.getSelected('participating', '_subject_id', false).map(
                                    (subject) => (
                                      <div
                                        key={`subject-chip-${subject.get('_subject_id')}`}
                                        className="pr-2 pb-2"
                                      >
                                        <b className="close_badge">
                                          {subject.get('subject_name', '')}
                                          <i
                                            className="fa fa-times pl-2 cursor-pointer"
                                            aria-hidden="true"
                                            onClick={() =>
                                              this.handleRemove(
                                                'participating',
                                                subject.get('_subject_id')
                                              )
                                            }
                                          ></i>
                                        </b>
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="row pb-4">
                              <div className="col-md-3">
                                <label className="mt-27">
                                  {' '}
                                  <Trans i18nKey={'administrating_subject/dept/program'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6">
                                <FormControl fullWidth variant="standard" size="small">
                                  <InputLabel
                                    htmlFor="administration-select"
                                    className="float-right"
                                  >
                                    <Trans i18nKey={'choose_subject'}></Trans>
                                  </InputLabel>
                                  <Select
                                    native
                                    value={this.getSelectedAdministratingSubject()}
                                    onChange={(e) => this.handleChange(e, 'administration')}
                                    inputProps={{
                                      name: 'administration',
                                      id: 'administration-select',
                                    }}
                                  >
                                    <option aria-label="None" value="" />
                                    {this.getSubjects(true, 'admin_department').map((s) => (
                                      <option
                                        key={`administration-${s._subject_id}`}
                                        value={s.value}
                                      >
                                        {s.name}
                                      </option>
                                    ))}
                                  </Select>
                                </FormControl>
                              </div>
                            </div>

                            <div className="row pt-4 pb-4">
                              <div className="col-md-3">
                                <label className="mt-4">
                                  {' '}
                                  <Trans i18nKey={'curriculum_keys.credit_hours'}></Trans>
                                </label>
                              </div>

                              <div className="col-md-9">
                                <div className="border rounded ">
                                  <div className="course_master_card border-bottom">
                                    <div className="row ">
                                      <div className="col-md-3">
                                        <p className="master_text">
                                          <Trans i18nKey={'configuration.session_type'}></Trans>
                                        </p>
                                      </div>
                                      <div className="col-md-2">
                                        <p className="master_text">
                                          {' '}
                                          <Trans i18nKey={'curriculum_keys.credit_hours'}></Trans>
                                        </p>
                                      </div>

                                      <div className="col-md-2">
                                        <p className="master_text">
                                          <Trans i18nKey={'configuration.contact_hours'}></Trans>
                                          {/* Contact Hours <br /> per Credit Hour */}
                                        </p>
                                      </div>
                                      <div className="col-md-3">
                                        <p className="master_text">
                                          <Trans
                                            i18nKey={'configuration.duration_contact_hour'}
                                          ></Trans>
                                        </p>
                                      </div>
                                      <div className="col-md-2 no-padding-left no-padding-right">
                                        <p className="master_text">
                                          <Trans i18nKey={'duration'}></Trans> ({' '}
                                          <Trans i18nKey={'configuration.in_minutes'}></Trans>)
                                        </p>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="bg-gray">
                                    {editedCourse.get('credit_hours', List()).map((s, i) => (
                                      <SessionType
                                        key={`sessionType-${i}`}
                                        editedCourse={editedCourse}
                                        sessionType={s}
                                        sessionTypeIndex={i}
                                        setData={this.props.setData}
                                      />
                                    ))}
                                    <div className="course_master_card bg-white border-bottom ">
                                      <div className="row ">
                                        <div className="col-md-3">
                                          <p className="mb-0 pt-1 pb-1 f-14 bold">
                                            <Trans i18nKey={'curriculum_keys.total'}></Trans> [
                                            {editedCourse
                                              .get('credit_hours', List())
                                              .map((c) =>
                                                indVerRename(c.get('type_symbol', ''), programId)
                                              )
                                              .join('+')}
                                            ]
                                          </p>
                                        </div>

                                        <div className="col-md-3">
                                          <p className="mb-0  pt-1 pb-1 f-14 bold">
                                            {this.getTotalHours('credit_hours')}
                                          </p>
                                        </div>

                                        <div className="col-md-4">
                                          <p className="mb-0 pt-1 pb-1 f-14 bold">
                                            {this.getTotalHours('contact_hours')}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="row pt-2 pb-4">
                              <div className="col-md-3">
                                <label className="mt-2">
                                  <Trans i18nKey={'allow_scheduling'}></Trans>
                                </label>
                              </div>
                              <div className="col-md-6 text-left">
                                <div className="mt-2">
                                  {this.state.EDIT_CREDIT_HOURS.map((edit, i) => (
                                    <Form.Check
                                      key={`${edit.name}-${i}`}
                                      inline
                                      className={
                                        i !== this.state.EDIT_CREDIT_HOURS.length - 1 ? 'pr-5' : ''
                                      }
                                      label={edit.name}
                                      type="radio"
                                      checked={editedCourse.get('allow_editing') === edit.value}
                                      onChange={() =>
                                        this.handleChange(edit.value, 'allow_editing')
                                      }
                                    />
                                  ))}
                                </div>
                              </div>
                            </div>

                            <div className="row pt-2 pb-4 pr-4">
                              <div className="col-md-3"></div>
                              <div className="col-md-6 text-left">
                                <div className="mt-2">
                                  <Form.Check
                                    type="checkbox"
                                    label={<Trans i18nKey={'should_achieve'}></Trans>}
                                    checked={editedCourse.get('achieve_target', false)}
                                    onChange={(e) =>
                                      this.handleChange(e.target.checked, 'achieve_target')
                                    }
                                  />
                                </div>
                              </div>
                            </div>
                          </>
                        )}
                        {activeStep === 1 && (
                          <SessionFlow
                            course={course}
                            sessionFlow={sessionFlow}
                            editedSessionFlow={editedSessionFlow}
                            setData={setData}
                            getCourseSessionOrderModule={this.getCourseSessionOrderModule}
                            courseSessionOrderModule={courseSessionOrderModule}
                            postCourseSessionOrderModule={this.postCourseSessionOrderModule}
                            maxHours={max_hours}
                            editedAdditionalSessionFlow={editedAdditionalSessionFlow}
                          />
                        )}
                      </div>
                    </div>

                    <div className="d-flex justify-content-between pt-3">
                      <div className="">
                        <b className="pr-3">
                          <Button variant="outline-primary" onClick={this.handleBackClick}>
                            <Trans i18nKey={'cancel'}></Trans>
                          </Button>
                        </b>
                        {this.showSaveAsDraftButton() && (
                          <b className="pr-3">
                            <Button variant="outline-primary" onClick={this.handleSaveAsDraft}>
                              <Trans i18nKey={'save_as_draft'}></Trans>
                            </Button>
                          </b>
                        )}
                      </div>

                      <div className="">
                        <div className="float-right">
                          {activeStep !== 0 && (
                            <b className="pr-2">
                              <Button variant="outline-primary" onClick={this.handlePrevStep}>
                                <Trans i18nKey={'back'}></Trans>
                              </Button>
                            </b>
                          )}
                          <b className="pr-2">
                            <Button variant="primary" onClick={this.handleNextStep}>
                              {activeStep === 0 ? (
                                <Trans i18nKey={'configuration.next'}></Trans>
                              ) : (
                                <Trans i18nKey={'complete'}></Trans>
                              )}
                            </Button>
                          </b>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

const AddCourse = withTranslation()(AddCourseComponent);

function SessionType(props) {
  const { editedCourse, sessionType, sessionTypeIndex, setData } = props;
  const deliveryTypes = sessionType.get('delivery_type', List());
  const [expanded, setExpanded] = useState(true);
  const { t } = useTranslation();

  function toggleExpanded() {
    setExpanded(!expanded);
  }

  function handleChange(e, name, deliveryTypeIndex) {
    const value = e.target.value;

    if (['credit_hours', 'contact_hours', 'delivery_type_duration'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    const position =
      name === 'delivery_type_duration'
        ? ['credit_hours', sessionTypeIndex, 'delivery_type', deliveryTypeIndex, 'duration']
        : ['credit_hours', sessionTypeIndex, name];

    let updatedCourse = editedCourse.setIn(position, value);
    if (name === 'credit_hours' && value === '0') {
      const deliveryTypePosition = ['credit_hours', sessionTypeIndex, 'delivery_type'];
      const updatedDeliveryTypes = updatedCourse
        .getIn(deliveryTypePosition)
        .map((deliveryType) => deliveryType.set('isActive', false));
      updatedCourse = updatedCourse.setIn(deliveryTypePosition, updatedDeliveryTypes);
    }

    setData(
      Map({
        editedCourse: updatedCourse,
      })
    );
  }

  function handleCheckboxChange(e, deliveryTypeIndex) {
    const value = e.target.checked;
    const position = [
      'credit_hours',
      sessionTypeIndex,
      'delivery_type',
      deliveryTypeIndex,
      'isActive',
    ];
    const course = editedCourse.setIn(position, value);
    let durationSplit = false;
    course.getIn(['credit_hours', sessionTypeIndex, 'delivery_type'], List()).forEach((d) => {
      if (durationSplit) return;
      durationSplit = d.get('isActive', false);
    });
    setData(
      Map({
        editedCourse: course.setIn(
          ['credit_hours', sessionTypeIndex, 'duration_split'],
          durationSplit
        ),
      })
    );
  }

  function deliveryDuration() {
    if (sessionType.get('delivery_type', List()).size > 0) {
      const delivery_duration = sessionType
        .get('delivery_type', List())
        .filter((item) => item.get('isActive') === true)
        .map((item) => item.get('duration', 0))
        .toJS();
      const actualCount = delivery_duration.length;
      const uniqueCount = delivery_duration.filter((v, i, a) => a.indexOf(v) === i).length;
      const allEqual = (arr) => arr.every((v) => v === arr[0]);
      if (actualCount === uniqueCount) {
        return actualCount === 0
          ? sessionType.get('duration', '') !== 'null'
            ? sessionType.get('duration', '')
            : 0
          : !allEqual(delivery_duration)
          ? 'Mixed'
          : Math.max(...delivery_duration);
      } else {
        return !allEqual(delivery_duration) ? 'Mixed' : Math.max(...delivery_duration);
      }
    } else {
      return sessionType.get('duration', '') !== 'null' ? sessionType.get('duration', '') : 0;
    }
  }

  const isZeroCreditHours = sessionType.get('credit_hours', '') === '0';
  let programId = getURLParams('_id', true);
  return (
    <div className="course_master_card border-bottom">
      <div className="row text-left">
        <div className="col-md-3">
          <p className="mb-0 bold" onClick={toggleExpanded}>
            <i className={`fa pr-3 fa-caret-${expanded ? 'down' : 'right'}`} aria-hidden="true"></i>
            {indVerRename(sessionType.get('type_name', ''), programId)}
          </p>
        </div>
        <div className="col-md-2">
          <div className="mt--25 w-75">
            <Input
              elementType="floatinginput2"
              elementConfig={{
                type: 'text',
              }}
              value={sessionType.get('credit_hours', '')}
              placeholder={t('constant.credit_hours')}
              changed={(e) => handleChange(e, 'credit_hours')}
            />
          </div>
        </div>
        <div className="col-md-2">
          <div className="mt--25 w-75">
            <Input
              elementType="floatinginput2"
              elementConfig={{
                type: 'text',
              }}
              value={sessionType.get('contact_hours', '')}
              disabled={isZeroCreditHours}
              placeholder={t('constant.contact_hours')}
              changed={(e) => handleChange(e, 'contact_hours')}
            />
          </div>
        </div>
        <div className="col-md-3">
          <div className="mt--25 w-75">
            <Input
              elementType="floatinginput2"
              elementConfig={{
                type: 'text',
              }}
              value={sessionType.get('duration_per_contact_hour', '')}
              placeholder={t('constant.duration_hours')}
              changed={(e) => handleChange(e, 'duration_per_contact_hour')}
            />
          </div>
        </div>
        <div className="col-md-2">
          <div className="mt--25 w-75">
            <Input
              elementType="floatinginput2"
              elementConfig={{
                type: 'text',
              }}
              value={deliveryDuration()}
              disabled={isZeroCreditHours || sessionType.get('duration_split', false)}
              placeholder={t('constant.duration')}
              changed={(e) => handleChange(e, 'duration')}
            />
          </div>
        </div>
        {expanded && (
          <div className="col-md-12 pt-3">
            <div className="border rounded">
              <div className="p-2 border-bottom">
                <p className="master_text pt-1 pb-1 text-left">
                  {' '}
                  <Trans i18nKey={'applicable_delivery_types'}></Trans>
                </p>
              </div>
              {deliveryTypes.map((d, i) => (
                <div
                  key={`${sessionTypeIndex}-deliveryType-${i}`}
                  className="course_master_card bg-white border-bottom"
                >
                  <div className="row text-left">
                    <div className="col-md-10">
                      <div className="pt-1">
                        <Form.Check
                          type="checkbox"
                          label={d.get('delivery_type', '')}
                          checked={d.get('isActive', false)}
                          onChange={(e) => handleCheckboxChange(e, i)}
                          disabled={isZeroCreditHours}
                        />
                      </div>
                    </div>
                    <div className="col-md-2">
                      <div className="mt--25 w-75">
                        <Input
                          elementType="floatinginput2"
                          elementConfig={{
                            type: 'text',
                          }}
                          disabled={isZeroCreditHours || !d.get('isActive', false)}
                          value={d.get('duration', '')}
                          placeholder={t('configuration.duration')}
                          changed={(e) => handleChange(e, 'delivery_type_duration', i)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

AddCourseComponent.propTypes = {
  history: PropTypes.object,
  match: PropTypes.object,
  location: PropTypes.object,
  curriculum: PropTypes.instanceOf(Map),
  course: PropTypes.instanceOf(Map),
  editedCourse: PropTypes.instanceOf(Map),
  programDeptList: PropTypes.instanceOf(List),
  setData: PropTypes.func,
  getProgramDeptList: PropTypes.func,
  getCurriculum: PropTypes.func,
  sessionTypeList: PropTypes.instanceOf(List),
  activeStep: PropTypes.number,
  saveCourse: PropTypes.func,
  sessionFlow: PropTypes.instanceOf(Map),
  editedSessionFlow: PropTypes.instanceOf(List),
  saveSessionFlow: PropTypes.func,
  getCourse: PropTypes.func,
  getsessionFlow: PropTypes.func,
  resetMessage: PropTypes.func,
  deliveringSubjects: PropTypes.instanceOf(List),
  courseSessionOrderModule: PropTypes.instanceOf(List),
  getDeliveringSubject: PropTypes.func,
  getSessionOrderModules: PropTypes.func,
  postSessionOrderModules: PropTypes.func,
  t: PropTypes.func,
  saveSessionOrderModules: PropTypes.func,
  editedAdditionalSessionFlow: PropTypes.instanceOf(List),
  max_hours: PropTypes.number,
  versionCourseId: PropTypes.string,
};

SessionType.propTypes = {
  editedCourse: PropTypes.instanceOf(Map),
  sessionType: PropTypes.instanceOf(Map),
  sessionTypeIndex: PropTypes.number,
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    program: selectProgram(state),
    programError: selectProgramError(state),
    programDeptList: selectProgramDeptList(state),
    sessionTypeList: selectSessionTypeList(state),
    curriculum: selectCurriculum(state),
    editedCourse: selectEditedCourse(state),
    activeStep: selectActiveStep(state),
    course: selectCourse(state),
    sessionFlow: selectSessionFlow(state),
    editedSessionFlow: selectEditedSessionFlow(state),
    editedAdditionalSessionFlow: selectEditedAdditionalSessionFlow(state),
    deliveringSubjects: selectDeliveringSubject(state),
    courseSessionOrderModule: selectCourseSessionOrder(state),
    max_hours: selectMaxHours(state),
    versionCourseId: selectVersionCourseId(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(AddCourse);
