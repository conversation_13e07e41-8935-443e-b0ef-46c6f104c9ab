import React from 'react';
import { ProgressBar } from 'react-bootstrap';

function levelList(props) {
  return (
    <div className="main pb-5 bg-mainBackground">
      <div className="bg-white p-3 border-bottom-2px">
        <div className="container-fluid">
          <p className="font-weight-bold mb-0 text-left f-17">
            <i className="fa fa-arrow-left pr-3 remove_hover" aria-hidden="true"></i>1st Year, Level
            1
          </p>
        </div>
      </div>

      <div className="container pl-0">
        <div className="p-3">
          <p className="f-20 mb-4 bold"> All Courses</p>

          <div className="border-bottom mb-4">
            <div className="row align-items-center">
              <div className="col-md-3">
                <p className="mb-0"> Course Name </p>
              </div>
              <div className="col-md-3">
                <p className="mb-0"> Direct Assessment </p>
              </div>
              <div className="col-md-1 mb-0"></div>
              <div className="col-md-3">
                <p className="mb-0"> Indirect Assessment </p>
              </div>
              <div className="col-md-3"></div>
            </div>
          </div>

          <div className="levelBox mb-2 mt-3">
            <div className="row align-items-center">
              <div className="col-md-3 mb-0">
                <p className="ml-2 mb-0"> Fundamentals of Human b... </p>
              </div>

              <div className="col-md-3 mb-0">
                <p className="mb-2"> Direct - 00/42 Ass </p>
                <div className="">
                  <ProgressBar variant="primary" now={40} className="border-radious-8 height-13" />
                </div>
              </div>
              <div className="col-md-1 mb-0"></div>
              <div className="col-md-3 mb-0">
                <p className="mb-2"> Direct - 00/42 Ass </p>
                <div className="">
                  <ProgressBar variant="primary" now={40} className="border-radious-8 height-13" />
                </div>
              </div>
              <div className="col-md-2 mb-0">
                <p className="ml-2 mb-0 mr-3 bold text-right"> View </p>
              </div>
            </div>
          </div>
          <div className="levelBox mb-2 mt-3">
            <div className="row align-items-center">
              <div className="col-md-3 mb-0">
                <p className="ml-2 mb-0"> Fundamentals of Human b... </p>
              </div>

              <div className="col-md-3 mb-0">
                <p className="mb-2"> Direct - 00/42 Ass </p>
                <div className="">
                  <ProgressBar variant="primary" now={40} className="border-radious-8 height-13" />
                </div>
              </div>
              <div className="col-md-1 mb-0"></div>
              <div className="col-md-3 mb-0">
                <p className="mb-2"> Direct - 00/42 Ass </p>
                <div className="">
                  <ProgressBar variant="primary" now={40} className="border-radious-8 height-13" />
                </div>
              </div>
              <div className="col-md-2 mb-0">
                <p className="ml-2 mb-0 mr-3 bold text-right"> View </p>
              </div>
            </div>
          </div>
          <div className="levelBox mb-2 mt-3">
            <div className="row align-items-center">
              <div className="col-md-3 mb-0">
                <p className="ml-2 mb-0"> Fundamentals of Human b... </p>
              </div>

              <div className="col-md-3 mb-0">
                <p className="mb-2"> Direct - 00/42 Ass </p>
                <div className="">
                  <ProgressBar variant="primary" now={40} className="border-radious-8 height-13" />
                </div>
              </div>
              <div className="col-md-1 mb-0"></div>
              <div className="col-md-3 mb-0">
                <p className="mb-2"> Direct - 00/42 Ass </p>
                <div className="">
                  <ProgressBar variant="primary" now={40} className="border-radious-8 height-13" />
                </div>
              </div>
              <div className="col-md-2 mb-0">
                <p className="ml-2 mb-0 mr-3 bold text-right"> View </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default levelList;
