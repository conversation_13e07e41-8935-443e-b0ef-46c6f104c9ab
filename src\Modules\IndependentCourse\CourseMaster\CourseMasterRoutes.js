import React, { Suspense } from 'react';
import { Route, Switch } from 'react-router';

const CourseMaster = React.lazy(() => import('./CourseMasterIndex'));
const CourseMasterConfiguration = React.lazy(() =>
  import('./Components/CourseConfiguration/CourseConfigurationIndex')
);
const ViewCourse = React.lazy(() =>
  import('Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/ViewCourse/ViewCourse')
);

const AddNewSession = React.lazy(() =>
  import('Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/LinkSession/LinkSessionIndex')
);

function ConfigurationBody() {
  return (
    <Suspense fallback="">
      <Switch>
        <Route
          path={'/:type/:id/:name/independent-course/course-master-list'}
          exact
          component={CourseMaster}
        ></Route>
        <Route
          path={'/:type/:id/:name/independent-course/course-master-list/configuration'}
          exact
          component={CourseMasterConfiguration}
        ></Route>
        <Route
          path={'/:type/:id/:name/independent-course/course-master-list/view-course'}
          exact
          component={ViewCourse}
        ></Route>
        <Route
          path={'/:type/:id/:name/independent-course/course-master-list/configuration/linkSession'}
          component={AddNewSession}
        ></Route>
      </Switch>
    </Suspense>
  );
}

/* ConfigurationBody.propTypes = {
  location: PropTypes.instanceOf(List),
}; */

export default ConfigurationBody;
