.global-date-picker-input {
  border-radius: 4px;
  width: 100%;
  max-width: 120px;
  border: 1px solid rgba(0, 0, 0, 0.23);
  padding: 10.5px 14px;
  line-height: 19.0016px;
  height: 40px;
  color: rgba(0, 0, 0, 0.87);
}

.global-date-picker-input:hover {
  border: 1px solid rgb(0, 0, 0);
}

.outline-text-input {
}

.wd-100 {
  width: 100%;
}

.min-wd-250 {
  min-width: 250px;
}

.ml-auto {
  margin-left: auto;
}

.global-theme {
  padding: 10px 10px 10px 10px;
  background: #e5e7eb;
}

.label-ar {
  direction: rtl;
}

.icon-blue {
  cursor: pointer;
  color: #147afc;
}

.activeFlag{
  fill: #DC2626 !important;
}

.cursor-pointer {
  cursor: pointer;
}

.tab-wrapper-max-300 {
  width: 100%;
  max-width: 300px !important;
}

.modal-555w {
  max-width: 555px !important;
}

.icon_date {
  background: url('../../Assets/arrow_cart.svg') no-repeat right;
  background-size: 10px;
  background-position: 93%;
  cursor: pointer;
}

.font_size_margin{
  font-size: 12px;
  margin-top: -5%;
  margin-left: 10%;
}

.font_size_bold{
  font-size: 16px;
  font-weight: 500;
}