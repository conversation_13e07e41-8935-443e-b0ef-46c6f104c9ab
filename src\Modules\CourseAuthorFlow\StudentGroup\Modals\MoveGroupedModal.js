import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
// import CloseIcon from '@mui/icons-material/Close';
import { fromJS, List, Map, Set } from 'immutable';
import { formatToTwoDigitString } from 'utils';
import DriveFileMoveRoundedIcon from '@mui/icons-material/DriveFileMoveRounded';
import { useCallGroupSettings, useCallProgramYearLevel } from '../Dashboard';
import {
  constructManualGroups,
  getCompletedSessionCount,
  getProcessedGroups,
  ManualGrouping,
  useCompletedSessions,
} from './GroupingModal';
import { getModalSubtitle } from './DeliveryGroupsModal';
import { useDispatch } from 'react-redux';
import { groupStudents, resetMessage, setData } from '_reduxapi/studentGroupV2/action';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
const subTitleSx = {
  marginLeft: '8px',
  padding: '4px 8px',
  fontSize: '14px',
  borderRadius: '4px',
  backgroundColor: '#F3F4F6',
};
const selectedCountSx = {
  // marginRight: '38px',
  fontSize: '14px',
  color: '#6B7280',
};
const titleIconSx = {
  padding: '8px',
  backgroundColor: '#EFF9FB',
  borderRadius: '50%',
  marginRight: '16px',
  color: '#0064C8',
};
// const closeIconSx = {
//   position: 'absolute',
//   right: 8,
//   top: 16,
// };
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};

const validateGroups = ({ deliveryTypes, totalCompletedSession, status }) => {
  if (!deliveryTypes.size) return 'Select groups to move';

  const isEmptyGroupNo = deliveryTypes.some((type) => !type.get('groupNo'));
  if (isEmptyGroupNo) return 'Please select group for each selected delivery groups';

  if (totalCompletedSession && !status) return 'Select status';
  return '';
};

export const getSelectedGroups = (selectedStudents) => {
  return selectedStudents.reduce((acc, student) => {
    const groups = student.get('groups', Map());
    groups.forEach(
      (groupNo, symbol) => (acc = acc.update(symbol, Set(), (prev) => prev.add(groupNo)))
    );

    return acc;
  }, Map());
};

const MoveGroupedModal = ({ open, data, groupingData, handleClose, refreshStudents }) => {
  const dispatch = useDispatch();
  const { groupSettings, callGroupSettings } = useCallGroupSettings({
    selectedData: data,
  });
  const [fetchProgramYearLevel] = useCallProgramYearLevel({ filters: data });
  const { genderType, selectedStudents, isAllStudent } = groupingData;
  const [completedSession, fetchCompletedSessionsCount] = useCompletedSessions({
    selectedData: data,
    genderType,
  });
  const [deliveryGroups, setDeliveryGroups] = useState(List());
  const [status, setStatus] = useState('');
  const selectedCount = selectedStudents.size;

  useEffect(() => {
    if (groupSettings.isEmpty()) callGroupSettings();
    return () => dispatch(setData(fromJS({ totalCompletedSession: {} })));
  }, []);

  useEffect(() => {
    const deliveryGroups = groupSettings.get('deliveryGroups');
    if (!deliveryGroups) return;

    const genderGroup = deliveryGroups.find((group) => group.get('gender') === genderType);
    if (!genderGroup) return;

    const selectedGroups = getSelectedGroups(selectedStudents);
    const processedGroups = getProcessedGroups(genderGroup, selectedGroups);
    setDeliveryGroups(processedGroups);
  }, [groupSettings, genderType, selectedStudents]);

  const modalSubTile = useMemo(() => getModalSubtitle(data), []);

  const totalCompletedSession = useMemo(() => {
    if (completedSession.isEmpty()) return 0;

    return getCompletedSessionCount(deliveryGroups, completedSession);
  }, [deliveryGroups, completedSession]);

  useEffect(() => {
    if (!totalCompletedSession) setStatus('');
  }, [totalCompletedSession]);

  const handleChange = (key, index, typeIndex) => (e) => {
    const value = key === 'checked' ? e.target.checked : e.target.value;
    const updatePath = [index, 'selectedType', typeIndex];
    const updatedData = deliveryGroups.setIn([...updatePath, key], value);
    setDeliveryGroups(updatedData);
    fetchCompletedSessionsCount(updatedData.getIn(updatePath));
  };

  const handleSave = () => {
    const deliveryTypes = constructManualGroups(deliveryGroups);
    const errorMsg = validateGroups({ deliveryTypes, totalCompletedSession, status });
    if (errorMsg) return dispatch(resetMessage(errorMsg));

    const groupSettingId = groupSettings.get('_id');
    const requestBody = Map({
      groupSettingId,
      genderType,
      isAllStudent,
      ...(!isAllStudent && { studentIds: selectedStudents.map((s) => s.get('userId')) }),
      groupingType: 'grouped',
      moveTo: true,
      selectedDeliveryType: deliveryTypes,
      ...(totalCompletedSession && { studentAttendanceStatus: status }),
    });

    dispatch(
      groupStudents({
        requestBody,
        selectedCount,
        callback: () => {
          fetchProgramYearLevel();
          callGroupSettings();
          refreshStudents();
          handleClose();
        },
      })
    );
  };

  return (
    <Dialog open={open} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Box display="flex" sx={titleIconSx}>
              <DriveFileMoveRoundedIcon />
            </Box>
            Move Grouped Students
            <Typography sx={subTitleSx}>{modalSubTile}</Typography>
          </Box>
          <Typography sx={selectedCountSx}>
            Selected: <span className="bold">{formatToTwoDigitString(selectedCount)}</span>
          </Typography>
        </Box>
      </DialogTitle>
      {/* <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
        <CloseIcon />
      </IconButton> */}
      <DialogContent className="p-3">
        <ManualGrouping
          deliveryGroups={deliveryGroups}
          handleChange={handleChange}
          completedSession={completedSession}
          totalCompletedSession={totalCompletedSession}
          status={status}
          handleStatus={(e) => setStatus(e.target.value)}
        />
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
          Cancel
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleSave} sx={buttonSx}>
          Save
        </MButton>
      </DialogActions>
    </Dialog>
  );
};
MoveGroupedModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  groupingData: PropTypes.object,
  handleClose: PropTypes.func,
  refreshStudents: PropTypes.func,
};

export default MoveGroupedModal;
