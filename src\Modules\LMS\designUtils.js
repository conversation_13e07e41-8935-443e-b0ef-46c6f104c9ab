import { makeStyles } from '@mui/styles';

export const useStylesFunction = makeStyles(() => ({
  autocomplete: {
    '& .MuiInputBase-root': {
      padding: '0px !important',
      '& fieldset': {
        borderColor: 'blue',
        border: 'none !important',
      },
      '&.Mui-focused fieldset': {
        borderColor: 'red',
        border: 'none !important',
      },
    },
    '& .MuiInputBase-colorPrimary': {
      maxHeight: '100px !important',
      overflow: 'auto',
      border: '1px solid #C4C4C4',
      borderRadius: '4px',
    },
  },

  Active: {
    '& .MuiStepLabel-label.Mui-active': {
      borderBottom: '3px solid #1976d2',
    },
  },

  checkedDisabled: {
    color: '#a6a6a6',
    '&.Mui-checked': {
      color: '#1976d2 !important',
    },
  },

  textfield: {
    '& .MuiOutlinedInput-root': {
      '& > fieldset': {
        border: 'none',
      },
      '&:hover fieldset': {
        border: '1px solid #B3B3B3',
      },
      '&.Mui-focused fieldset': {
        border: '1px solid #B3B3B3',
      },
    },
  },

  TextInput: {
    '& .MuiOutlinedInput-input': {
      padding: ' 5.5px 100px 7px 5px !important',
    },
  },

  CheckboxSmallSize: {
    '& .MuiSvgIcon-root': {
      fontSize: '17px !important',
    },
    '& .MuiCheckbox-root > span::before': {
      background: 'none !important',
      transform: 'none !important',
    },
  },

  blueBgTab: {
    backgroundImage:
      'linear-gradient( 84.06deg, rgba(26, 123, 220, 0.85) 13%, rgba(86, 184, 255, 0.85) 107.28% )',
    '& .MuiTabs-indicator': {
      background: '#ffffff !important',
    },
    borderBottom: 'none !important',
  },

  tabButton: {
    '&.Mui-selected': {
      color: '#ffffff !important',
    },
    color: '#ffffff !important',
    textTransform: 'capitalize',
  },

  accordion: {
    boxShadow: 'none',
    '&::before': { backgroundColor: 'transparent !important' },
    '& .MuiAccordionDetails-root': {
      padding: '10px 42px',
    },
    '& .MuiAccordionSummary-root': {
      height: '60px',
    },
  },

  // accordionSummary: {
  //   borderBottom: '1px solid #A6A6A6',
  //   '&.MuiAccordionSummary-root': {
  //     height: '48px !important',
  //   },
  // },

  accordionSummaryBorder: {
    borderBottom: 'none',
  },

  tableContainer: {
    boxShadow: 'none !important',
  },

  tableBody: {
    borderLeft: '1px solid #CCCCCC !important',
    '& .MuiTableCell-root': {
      padding: '8px 16px !important',
    },
  },

  removeBorderInTableCell: {
    border: '0px !important',
  },

  tableCellAddingBorder: {
    borderRight: '1px solid #e0e0e0 !important',
  },

  tableCellRemovingBackground: {
    background: '#ffffff !important',
  },

  tableCellBorderBottom: {
    borderBottom: '1px solid #e0e0e0 !important',
  },

  modalWidth: {
    '& .MuiPaper-root.MuiPaper-elevation24': {
      maxWidth: '900px !important',
      minWidth: '900px !important',
    },
  },

  docModalWidth: {
    '& .MuiPaper-root.MuiPaper-elevation24': {
      maxWidth: '600px !important',
      minWidth: '600px !important',
    },
  },

  // color-picker
  colorPicker: {
    '& .red': {
      color: 'red !important',
    },
    '&.MuiFormControl-root': {
      margin: '-14px 8px !important',
    },
    '& .MuiSelect-select': {
      color: '#ffffff',
      padding: '4px',
      margin: '4px 0px 4px 4px',
      borderRadius: '0px',
    },
    '& .MuiSvgIcon-root': {
      background: '#ffffff',
      height: '39px',
      top: '0px',
    },
  },

  PrimaryBg: {
    '& .MuiSelect-select': {
      background: '#007bff !important',
    },
  },
  DangerBg: {
    '& .MuiSelect-select': {
      background: '#dc3545 !important',
    },
  },
  SuccessBg: {
    '& .MuiSelect-select': {
      background: '#28a745 !important',
    },
  },
  WarningBg: {
    '& .MuiSelect-select': {
      background: '#ffc107 !important',
    },
  },

  digiMultiSelect: {
    marginLeft: '0px !important',
    marginTop: '0px !important',
    width: '100%',
    '& .MuiFormLabel-root': {
      display: 'none',
    },
    '& .MuiSelect-select': {
      border: '1px solid #c4c4c4',
      padding: '8px !important',
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        display: 'none',
      },
    },
  },

  permissionTypePopup: {
    '&.MuiDialog-root': {
      zIndex: '1050 !important',
    },
  },

  BottomPadding: {
    '&.MuiAccordionDetails-root': {
      paddingBottom: '0px !important',
    },
  },

  MarginBottom: {
    '&.MuiFormControlLabel-root': {
      marginBottom: '0px !important',
    },
  },

  AccordionPadding: {
    '&.MuiAccordionDetails-root': {
      padding: '4px 42px !important',
    },
  },

  leaveNavActiveBold: {
    '& .MuiListItemText-primary': {
      fontWeight: '500 !important',
    },
  },
  genderSegregationPopUpPadding: {
    '&.MuiDialogContent-root': {
      padding: '0px 24px !important',
    },
  },
  lmsIndCummDeletePopup: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '40% !important',
    background: 'white !important',
    boxShadow: 'none',
    padding: '4px',
    borderRadius: '4px',
  },
}));
