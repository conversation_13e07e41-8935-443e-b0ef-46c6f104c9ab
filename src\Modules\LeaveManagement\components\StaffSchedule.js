import React from 'react';
import PropTypes from 'prop-types';
import { List, Map, Set } from 'immutable';
import { format, isValid } from 'date-fns';

import Input from '../../../Widgets/FormElements/Input/Input';

import { capitalize, getStartEndDate, formatFullName } from '../../../utils';

import MergeIcon from '../../../Assets/merge.svg';
import Warning from '../../../Assets/alert5.png';

function StaffSchedule(props) {
  function getSubstituteStaffOptions(staffs) {
    return List([Map({ name: 'Select staff', value: '' })])
      .concat(
        staffs.map((s) =>
          Map({
            name: `${s.getIn(['name', 'first'], '')} ${s.getIn(['name', 'last'], '')}`,
            value: s.get('_staff_id'),
          })
        )
      )
      .toJS();
  }

  function getSessionType(schedule) {
    const sessionType = schedule.get('type', '');
    switch (sessionType) {
      case 'regular': {
        let sessionTypes = Set();
        sessionTypes = sessionTypes.add(schedule.getIn(['session', 'session_type'], ''));
        const isMerged = schedule.get('merge_status', false);
        if (isMerged) {
          schedule.get('merge_sessions', List()).forEach((s) => {
            sessionTypes = sessionTypes.add(s.get('session_type', ''));
          });
        }
        return (
          <div>
            <p className="digi-black mb-0">Regular Session</p>
            <p className="color-light-gray f-14 mb-0 font-weight-normal">
              {sessionTypes.toList().join(', ')}
            </p>
          </div>
        );
      }
      case 'support_session':
      case 'event': {
        const subType = schedule.get('sub_type') || '';
        return (
          <div>
            <p className="digi-black mb-0">
              {sessionType === 'support_session' ? 'Support Session' : 'Event'}
            </p>
            <p className="color-light-gray f-14 mb-0 font-weight-normal">{capitalize(subType)}</p>
          </div>
        );
      }
      default:
        return '';
    }
  }

  function getScheduleDate(scheduleDate) {
    if (!scheduleDate) return '';
    scheduleDate = new Date(scheduleDate.split('T').slice(0, 1).join());
    if (!isValid(scheduleDate)) return '';
    return format(scheduleDate, 'd MMM yyyy');
  }

  function formatSessionTime(time) {
    if (!time) return '';
    if (time.isEmpty()) return '';
    time = getStartEndDate(time);
    if (!isValid(time)) return '';
    return format(time, 'hh:mm a');
  }

  function getSessionTitle(schedule) {
    const sessionType = schedule.get('type', '');
    switch (sessionType) {
      case 'regular': {
        const deliverySymbol = schedule.getIn(['session', 'delivery_symbol'], '');
        const deliveryNumber = schedule.getIn(['session', 'delivery_no'], '');
        const sessionTopic = schedule.getIn(['session', 'session_topic'], '');
        let title = `${deliverySymbol}${deliveryNumber} - ${sessionTopic}`;
        const isMerged = schedule.get('merge_status', false);
        if (isMerged) {
          title = Set(
            List([`${deliverySymbol}${deliveryNumber}`]).concat(
              schedule.get('merge_sessions', List()).map((s) => {
                const deliverySymbol = s.get('delivery_symbol', '');
                const deliveryNumber = s.get('delivery_no', '');
                return `${deliverySymbol}${deliveryNumber}`;
              })
            )
          )
            .toList()
            .join(', ');
        }
        return (
          <div>
            <p className="digi-black mb-0">
              {isMerged && <img src={MergeIcon} alt="merged" className="mr-1" />} {title}
            </p>
          </div>
        );
      }
      case 'support_session':
      case 'event': {
        return (
          <div>
            <p className="digi-black mb-0">{schedule.get('title', '')}</p>
          </div>
        );
      }
      default:
        return '';
    }
  }

  const {
    selectedSubstituteStaff,
    scheduleStatus,
    handleSubstituteStaffChange,
    isSubstituteStaffReadOnly,
  } = props;
  const scheduleList = scheduleStatus.get('data', List());

  return (
    <div>
      <b className="f-15">
        List of scheduled activities that require a substitute (No of Counts : {scheduleList.size})
      </b>
      <div className="leaveManage mb-2">
        <table className="table">
          <thead className="group_table_top">
            <tr>
              <th className="border_color_blue">
                <div className="aw-50">
                  <b>S.NO</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-150">
                  <b>Session Type</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-100">
                  <b>Date</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-100">
                  <b>Start</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-100">
                  <b>End</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-100">
                  <b>Program</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-100">
                  <b>Course</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-100">
                  <b>Subject</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-150">
                  <b>Title</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-150">
                  <b>Staff Name</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-150">
                  <b>Substitute Staff</b>
                </div>
              </th>
              <th className="border_color_blue">
                <div className="aw-100">-</div>
              </th>
            </tr>
          </thead>
          <tbody className="leaveManage-height">
            {scheduleList.isEmpty() ? (
              <tr>
                <td colSpan={11} className="text-center">
                  No schedule found
                </td>
              </tr>
            ) : (
              scheduleList.map((s, i) => {
                const scheduleId = s.get('_id');
                const isSubstituteStaffRequired =
                  s.get('staffs', List()).filter((staff) => staff.get('status') === 'pending')
                    .size <= 1;
                const substituteStaffs = s.get('substitute_staff', List());
                return (
                  <tr key={`${scheduleId}-${i}`} className="tr-bottom-border">
                    <td>
                      <div className="aw-50">
                        <b>{i + 1}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-150">
                        <b>{getSessionType(s)}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        <b>{getScheduleDate(s.get('schedule_date', ''))}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        <b>{formatSessionTime(s.get('start', Map()))}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        <b>{formatSessionTime(s.get('end', Map()))}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        <b>{s.get('program_name', '')}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        <b>{s.get('course_code', '')}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        <b>
                          {s
                            .get('subjects', List())
                            .map((subject) => subject.get('subject_name', ''))
                            .join(', ')}
                        </b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-150">
                        <b>{getSessionTitle(s)}</b>
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        <b>
                          {s
                            .get('staffs', List())
                            .map((staff) => formatFullName(staff.get('staff_name', Map()).toJS()))
                            .join(', ')}
                        </b>
                      </div>
                    </td>
                    <td>
                      <div className={'aw-150 '} style={{ marginTop: '-25px', marginLeft: '50px' }}>
                        <div className="mt--12">
                          <div className="mt--12">
                            {isSubstituteStaffReadOnly ? (
                              <Input
                                elementType="floatinginput"
                                elementConfig={{
                                  type: 'text',
                                  title: formatFullName(
                                    selectedSubstituteStaff
                                      .getIn([scheduleId, 'substitute_staff', 'name'], Map())
                                      .toJS()
                                  ),
                                  style: { fontSize: '13px' },
                                }}
                                value={formatFullName(
                                  selectedSubstituteStaff
                                    .getIn([scheduleId, 'substitute_staff', 'name'], Map())
                                    .toJS()
                                )}
                                disabled={isSubstituteStaffReadOnly}
                              />
                            ) : (
                              <Input
                                elementType="floatingselect"
                                elementConfig={{
                                  options: getSubstituteStaffOptions(substituteStaffs),
                                }}
                                value={selectedSubstituteStaff.getIn(
                                  [scheduleId, 'substitute_staff', '_staff_id'],
                                  ''
                                )}
                                changed={(e) => handleSubstituteStaffChange(e.target.value, s)}
                                disabled={isSubstituteStaffReadOnly}
                              />
                            )}
                          </div>
                        </div>
                        {isSubstituteStaffRequired && (
                          <div className="color-light-gray f-12">Required</div>
                        )}
                      </div>
                    </td>
                    <td>
                      <div className="aw-100">
                        {substituteStaffs.isEmpty() && (
                          <img alt="" src={Warning} className="pr-2 mt-3" />
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

StaffSchedule.propTypes = {
  selectedSubstituteStaff: PropTypes.instanceOf(Map),
  scheduleStatus: PropTypes.instanceOf(Map),
  handleSubstituteStaffChange: PropTypes.func,
  isSubstituteStaffReadOnly: PropTypes.bool,
};

StaffSchedule.defaultProps = {
  isSubstituteStaffReadOnly: false,
};

export default StaffSchedule;
