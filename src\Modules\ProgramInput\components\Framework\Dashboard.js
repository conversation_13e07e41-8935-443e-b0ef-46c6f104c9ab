import React, { Component, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Dropdown, Button, Table } from 'react-bootstrap';

import AlertConfirmModal from '../../modal/AlertConfirmModal';
import Loader from '../../../../Widgets/Loader/Loader';
import {
  selectFrameworkDashboard,
  selectFrameworkList,
  selectIsFrameworksDashboardLoading,
} from '../../../../_reduxapi/program_input/selectors';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  getURLParams,
  removeURLParams,
  capitalize,
  eString,
  getLang,
  indVerRename,
  levelRename,
  contentRename,
  getVersionName,
} from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import '../../css/program.css';
import { Trans, withTranslation } from 'react-i18next';

const currentLang = getLang();

const MAP_TYPE = {
  IMPACT: 'impact',
  ALIGNMENT: 'alignment',
};
const MAP_TYPES = Object.values(MAP_TYPE);

const CONTENT_MAP_TYPE = {
  OPTIONAL: 'optional',
  REQUIRED: 'required',
};
const CONTENT_MAP_TYPES = Object.values(CONTENT_MAP_TYPE);

class Dashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      programId: getURLParams('_id', true),
      curriculumId: getURLParams('_curriculum_id', true),
      modalData: {
        show: false,
      },
    };
    this.unlistenHistory = props.history.listen(({ action, location }) => {
      this.setState({
        programId: getURLParams('_id', true),
        curriculumId: getURLParams('_curriculum_id', true),
      });
    });
    this.assignUnassignFramework = this.assignUnassignFramework.bind(this);
  }

  componentDidMount() {
    const { programId, curriculumId } = this.state;
    const { frameworkDashboard } = this.props;
    if (frameworkDashboard.get('_id') !== programId) {
      this.props.setData(
        Map({
          frameworkDashboard: Map(),
        })
      );
    }
    this.props.getFrameworkLists();
    this.props.getFrameworkDashboardDataByProgramId(programId, curriculumId);
  }

  componentDidUpdate(preProps, preState) {
    const { programId, curriculumId } = this.state;
    if (curriculumId !== preState.curriculumId) {
      this.props.getFrameworkDashboardDataByProgramId(programId, curriculumId);
    }
  }

  componentWillUnmount() {
    this.unlistenHistory();
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ data, type }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => {
        switch (type) {
          case 'framework': {
            return this.assignUnassignFramework(data, true);
          }
          default:
            return;
        }
      }
    );
  }

  setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(titleIcon && { titleIcon }),
        ...(body && { body }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  getCurriculums() {
    const { curriculumId } = this.state;
    const { frameworkDashboard } = this.props;
    if (!curriculumId) {
      return frameworkDashboard.get('curriculums', List());
    }
    return frameworkDashboard
      .get('curriculums', List())
      .filter((curriculum) => curriculum.get('_id') === curriculumId)
      .toList();
  }

  assignUnassignFramework({ mappedCount = 0, ...data }, isConfirmed) {
    if (mappedCount) {
      this.props.setData(
        Map({
          message: `You can't change framework as this ${capitalize(
            data.framework_for
          )} has mapping`,
        })
      );
      return;
    }
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: <Trans i18nKey={'sure_delete'}></Trans>,
        body: <Trans i18nKey={'delete_framework_content'}></Trans>,
        variant: 'confirm',
        data: { data, type: 'framework' },
        confirmButtonLabel: <Trans i18nKey={'yes'}></Trans>,
        cancelButtonLabel: <Trans i18nKey={'cancel'}></Trans>,
      });
      return;
    }
    this.props.addRemoveFramework(data, getURLParams('_curriculum_id', true));
  }

  render() {
    const { modalData } = this.state;
    const { history, location, setData, isFrameworksDashboardLoading, t } = this.props;
    const programId = getURLParams('_id', true);
    return (
      <React.Fragment>
        {isFrameworksDashboardLoading && <Loader isLoading />}
        <div className="main bg-gray pb-5">
          <div className="bg-gray">
            <div className="container-fluid">
              <div className="pb-4">
                <div className="pt-3">
                  <div className="d-flex justify-content-between">
                    <h5 className="pb-2 pt-2">
                      {' '}
                      <Trans i18nKey={'tabs.frameworks'}></Trans>
                    </h5>
                  </div>

                  <div className="bg-white border rounded">
                    <div className="p-3 min_h_500">
                      <p className="f-16 mb-2 text-left">
                        <Trans
                          i18nKey={'manage_curriculum'}
                          values={{
                            CLO: indVerRename('CLO', programId),
                            SLO: indVerRename('SLO', programId),
                            PLO: indVerRename('PLO', programId),
                          }}
                        ></Trans>
                      </p>

                      <div className="bg-gray rounded p-1 overflow_height">
                        <Table hover>
                          <thead className="th-graychange">
                            <tr>
                              <th colSpan="7">
                                <div className="row">
                                  <div className="col-md-3">
                                    <span className="frameWork_head">
                                      <Trans
                                        i18nKey={'frameworks.program'}
                                        values={{ Level: indVerRename('Level', programId) }}
                                      ></Trans>
                                    </span>
                                  </div>
                                  <div className="col-md-9">
                                    <div className="row">
                                      <div className="col-md-2">
                                        <span className="frameWork_head">
                                          {' '}
                                          <Trans i18nKey={'tabs.frameworks'}></Trans>
                                        </span>
                                      </div>
                                      <div className="col-md-2">
                                        <span className="frameWork_head">
                                          <Trans
                                            i18nKey={'no_plo'}
                                            values={{
                                              PLO: indVerRename('PLO', programId),
                                            }}
                                          ></Trans>
                                        </span>
                                      </div>
                                      <div className="col-md-2">
                                        <span className="frameWork_head">
                                          <Trans
                                            i18nKey={'clo_done'}
                                            values={{ CLO: indVerRename('CLO', programId) }}
                                          ></Trans>
                                        </span>
                                      </div>
                                      <div className="col-md-2">
                                        <span className="frameWork_head">
                                          {' '}
                                          <Trans i18nKey={'map_types'}></Trans>
                                        </span>
                                      </div>
                                      <div className="col-md-2 pl-0 pr-0">
                                        <span className="frameWork_head">
                                          {' '}
                                          <Trans
                                            i18nKey={'map_plo_clo_slo'}
                                            values={{
                                              CLO: indVerRename('CLO', programId),
                                              SLO: indVerRename('SLO', programId),
                                              PLO: indVerRename('PLO', programId),
                                            }}
                                          ></Trans>
                                        </span>
                                      </div>
                                      <div className="col-md-2">
                                        <span className="frameWork_head">
                                          {' '}
                                          <Trans i18nKey={'content_map'}></Trans>
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </th>
                            </tr>
                          </thead>

                          <tbody className="min_h_500">
                            {this.getCurriculums().isEmpty() && (
                              <tr>
                                <td className="text-center">
                                  {' '}
                                  <Trans i18nKey={'no_record_found'}></Trans>
                                </td>
                              </tr>
                            )}
                            {this.getCurriculums().map((curriculum) => (
                              <CurriculumItem
                                key={curriculum.get('_id')}
                                curriculum={curriculum}
                                history={history}
                                location={location}
                                setData={setData}
                                assignUnassignFramework={this.assignUnassignFramework}
                                t={t}
                              />
                            ))}
                          </tbody>
                        </Table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <AlertConfirmModal
            show={modalData.show}
            title={modalData.title || ''}
            titleIcon={modalData.titleIcon}
            body={modalData.body || ''}
            variant={modalData.variant || 'confirm'}
            confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
            cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
            onClose={this.onModalClose.bind(this)}
            onConfirm={this.onConfirm.bind(this)}
            data={modalData.data}
          />
        </div>
      </React.Fragment>
    );
  }
}

function CurriculumItem({ curriculum, history, location, setData, assignUnassignFramework, t }) {
  const [expandCurriculum, setExpandCurriculum] = useState(true);

  const framework = curriculum.get('framework', Map());
  const isFrameworkAssigned = Boolean(curriculum.getIn(['framework', '_id']));
  const isMapTypeAssigned = Boolean(curriculum.get('mapping_type'));

  function navigateToAddPlo() {
    setData(Map({ curriculum: Map() }));
    history.push(
      `/program-input/frameworks/plo/${removeURLParams(location, [
        '_curriculum_id',
        '_curriculum_name',
        '_course_id',
        '_course_name',
        '_framework_name',
      ])}&_curriculum_id=${eString(curriculum.get('_id'))}&_curriculum_name=${eString(
        curriculum.get('curriculum_name')
      )}&_framework_name=${eString(framework.get('name'))}`
    );
  }

  function getYearLevel() {
    return curriculum.get('year_level', List()).reduce((yearAcc, year) => {
      return yearAcc.concat(
        year.get('levels', List()).reduce((levelAcc, level) => {
          return levelAcc.push(
            Map({
              year_name: year.get('y_type'),
              _year_id: year.get('_id'),
              y_type: year.get('y_type'),
              no_of_level: year.get('no_of_level'),
              _level_id: level.get('_id'),
              level_name: level.get('level_name'),
              start_week: level.get('start_week'),
              end_week: level.get('end_week'),
              courses: level.get('courses'),
            })
          );
        }, List())
      );
    }, List());
  }
  const programId = getURLParams('_id', true);
  const ploLabel = indVerRename('PLO', programId);
  return (
    <React.Fragment>
      <tr>
        <td colSpan="7">
          <div className="row">
            <div className="col-md-3 pr-0">
              <div
                className="d-flex f-14 mb-0 cursor-pointer"
                onClick={() => setExpandCurriculum(!expandCurriculum)}
              >
                <div className="w-60px">
                  {' '}
                  <i
                    className={`fa ${
                      currentLang === 'ar' ? 'fa-level-down ' : 'fa-level-up '
                    } fa-rotate-90 f-12 mr-2`}
                    aria-hidden="true"
                  ></i>{' '}
                  <i
                    className={`fa ${
                      expandCurriculum ? 'fa-sort-desc' : 'fa-sort-asc fa-rotate-90'
                    } text-skyblue f-12 mr-2`}
                    aria-hidden="true"
                  ></i>
                </div>
                <div className="mapping_button">{curriculum.get('curriculum_name', '')}</div>
              </div>
            </div>

            <div className="col-md-9 pr-0">
              <div className="border-radious-8 bg-white">
                <div className="row">
                  <div className="col-md-2 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <FrameworksDropdown
                          label={
                            isFrameworkAssigned
                              ? framework.get('code', '')
                              : t('curriculum_keys.optional')
                          }
                          frameworkId={framework.get('_id')}
                          frameworkFor="curriculum"
                          curriculum={curriculum}
                          assignUnassignFramework={assignUnassignFramework}
                          t={t}
                        />
                      </b>
                      <b className="pt-3px">
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <Button
                          disabled={!isFrameworkAssigned}
                          variant="light"
                          id="dropdown-basic-text"
                          onClick={navigateToAddPlo}
                        >
                          {curriculum.get('plo_count', 0) ? (
                            <>
                              <i className="fa fa-plus pr-1" aria-hidden="true" />
                              {`${curriculum.get('plo_count')} ${ploLabel}`}
                            </>
                          ) : (
                            `${t('add')} ${ploLabel}`
                          )}
                        </Button>
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <Button variant="light" id="dropdown-basic-text" className="cursor-default">
                          <i className="fa fa-plus pr-1" aria-hidden="true" />
                          {`${curriculum.get('total_clo_done', '') || '00'} / ${
                            curriculum.get('total_course', '') || '00'
                          }`}
                        </Button>
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <MapTypeDropdown
                          label={
                            isMapTypeAssigned
                              ? t(`frameworks.${curriculum.get('mapping_type')}`) //capitalize()
                              : t('curriculum_keys.optional')
                          }
                          mapTypeFor="curriculum"
                          currentMapType={curriculum.get('mapping_type', '')}
                          curriculumId={curriculum.get('_id')}
                          disabled={
                            !curriculum.get('plo_count', 0) ||
                            Boolean(curriculum.get('total_plo_clo'))
                          }
                          t={t}
                        />
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <Button variant="light" id="dropdown-basic-text" className="cursor-default">
                          <i className="fa fa-plus pr-1" aria-hidden="true" />
                          {`${curriculum.get('total_plo_clo', '') || '00'} / ${
                            curriculum.get('total_course', '') || '00'
                          }`}
                        </Button>
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <MapTypeDropdown
                          label={
                            curriculum.get('content_mapping_type')
                              ? capitalize(curriculum.get('content_mapping_type'))
                              : t('master_graph.select')
                          }
                          isContent
                          mapTypeFor="curriculum"
                          currentMapType={curriculum.get('content_mapping_type', '')}
                          curriculumId={curriculum.get('_id')}
                          t={t}
                        />
                      </b>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </td>
      </tr>

      {expandCurriculum && (
        <React.Fragment>
          {getYearLevel().isEmpty() ? (
            <tr>
              <td className="text-align-center">
                <Trans i18nKey={'no_level'}></Trans>
              </td>
            </tr>
          ) : (
            getYearLevel().map((yearLevel) => (
              <YearLevelItem
                key={yearLevel.get('_level_id')}
                yearLevel={yearLevel}
                isFrameworkAssignedInCurriculum={isFrameworkAssigned}
                isMapTypeAssignedInCurriculum={isMapTypeAssigned}
                history={history}
                location={location}
                setData={setData}
                curriculum={curriculum}
                assignUnassignFramework={assignUnassignFramework}
                t={t}
              />
            ))
          )}
        </React.Fragment>
      )}
    </React.Fragment>
  );
}
function YearLevelItem({
  yearLevel,
  isFrameworkAssignedInCurriculum,
  isMapTypeAssignedInCurriculum,
  history,
  location,
  setData,
  curriculum,
  assignUnassignFramework,
  t,
}) {
  const [expandYearLevel, setExpandYearLevel] = useState(true);
  const programId = getURLParams('_id', true);
  return (
    <React.Fragment>
      <tr>
        <td colSpan="7">
          <div className="row">
            <div className="col-md-12 pr-0">
              <div
                className="d-flex f-14 mb-0 pt-2 bold pl-4 cursor-pointer"
                onClick={() => setExpandYearLevel(!expandYearLevel)}
              >
                <div className="w-60px">
                  {' '}
                  <i
                    className={`fa ${
                      currentLang === 'ar' ? 'fa-level-down ' : 'fa-level-up '
                    } fa-rotate-90 f-12 mr-2`}
                    aria-hidden="true"
                  ></i>
                  <i
                    className={`fa ${
                      expandYearLevel ? 'fa-sort-desc' : 'fa-sort-asc fa-rotate-90'
                    } text-skyblue f-12 mr-2`}
                    aria-hidden="true"
                  ></i>
                </div>
                <div className="">
                  {' '}
                  {`${contentRename(yearLevel.get('y_type', ''), 'year', 'Year ')} ${levelRename(
                    yearLevel.get('level_name', ''),
                    programId
                  )}`}
                </div>
              </div>
            </div>
          </div>
        </td>
      </tr>
      {expandYearLevel && (
        <React.Fragment>
          {yearLevel.get('courses', List()).isEmpty() ? (
            <tr>
              <td className="text-align-center">
                {' '}
                <Trans i18nKey={'no_course_found'}></Trans>
              </td>
            </tr>
          ) : (
            yearLevel
              .get('courses', List())
              .map((course) => (
                <CourseItem
                  key={course.get('_id')}
                  course={course}
                  isFrameworkAssignedInCurriculum={isFrameworkAssignedInCurriculum}
                  isMapTypeAssignedInCurriculum={isMapTypeAssignedInCurriculum}
                  history={history}
                  location={location}
                  setData={setData}
                  curriculum={curriculum}
                  yearLevel={yearLevel}
                  assignUnassignFramework={assignUnassignFramework}
                  t={t}
                />
              ))
          )}
        </React.Fragment>
      )}
    </React.Fragment>
  );
}

function CourseItem({
  course,
  isFrameworkAssignedInCurriculum,
  isMapTypeAssignedInCurriculum,
  curriculum,
  history,
  location,
  setData,
  yearLevel,
  assignUnassignFramework,
  t,
}) {
  const [expandCourse, setExpandCourse] = useState(true);
  const framework = course.get('framework', Map());
  const isFrameworkAssigned = Boolean(course.getIn(['framework', '_id']));
  const isMapTypeAssigned = Boolean(course.get('mapping_type', ''));

  function navigateToAddCloSlo(type) {
    setData(Map({ course: Map(), sessionFlow: Map(), editedSessionFlow: List() }));
    let url = `/program-input/frameworks/${type}/${removeURLParams(location, [
      '_curriculum_id',
      '_curriculum_name',
      '_course_id',
      '_course_name',
      '_framework_name',
    ])}&_curriculum_id=${eString(curriculum.get('_id'))}&_curriculum_name=${eString(
      curriculum.get('curriculum_name')
    )}&_course_id=${eString(course.get('_course_id'))}&_course_name=${eString(
      course.get('course_name')
    )}&_framework_name=${eString(course.getIn(['framework', 'name']))}`;
    if (type === 'clo') {
      url = `${url}&_year_id=${eString(yearLevel.get('_year_id'))}&_year_name=${eString(
        yearLevel.get('year_name')
      )}&_level_id=${eString(yearLevel.get('_level_id'))}&_level_name=${eString(
        yearLevel.get('level_name')
      )}&versionName=${eString(getVersionName(course))}`;
    }
    history.push(url);
  }

  function navigateToSLOMapping() {
    history.push(
      `/program-input/frameworks/clo-slo-map/${removeURLParams(location, [
        '_curriculum_id',
        '_curriculum_name',
        '_course_id',
        '_course_name',
        '_framework_name',
      ])}&_curriculum_id=${eString(curriculum.get('_id'))}&_curriculum_name=${eString(
        curriculum.get('curriculum_name')
      )}&_course_id=${eString(course.get('_course_id'))}&_course_name=${eString(
        course.get('course_name')
      )}&_framework_name=${eString(course.getIn(['framework', 'name']))}`
    );
  }

  function navigateToMapping() {
    history.push(
      `/program-input/frameworks/plo-clo-mapping-tree/${removeURLParams(location, [
        '_curriculum_id',
        '_curriculum_name',
        '_course_id',
        '_course_name',
        '_framework_name',
        '_mapping_type',
      ])}&_curriculum_id=${eString(course.get('_curriculum_id'))}&_curriculum_name=${eString(
        curriculum.get('curriculum_name', 'NA')
      )}&_course_id=${eString(course.get('_course_id'))}&_course_name=${eString(
        course.get('course_name')
      )}&_framework_name=${eString(course.getIn(['framework', 'name']))}&_mapping_type=${course.get(
        'mapping_type'
      )}&_is_framework_assigned_in_curriculum=${isFrameworkAssignedInCurriculum}`
    );
  }

  function disableMapPloClo() {
    if (!course.get('clo_count', 0)) return true;
    if (isFrameworkAssignedInCurriculum && !curriculum.get('plo_count', 0)) return true;
    if (!isMapTypeAssignedInCurriculum && !isMapTypeAssigned) return true;
    return false;
  }
  const programId = getURLParams('_id', true);
  const cloLabel = indVerRename('CLO', programId);
  const sloLabel = indVerRename('SLO', programId);
  const ploLabel = indVerRename('PLO', programId);
  return (
    <React.Fragment>
      <tr>
        <td colSpan="7">
          <div className="row">
            <div className="col-md-3 pr-0">
              <div
                className="d-flex f-14 mb-0 pt-2 bold pl-5 cursor-pointer"
                onClick={() => setExpandCourse(!expandCourse)}
              >
                <div className="w-60px">
                  {' '}
                  <i
                    className={`fa ${
                      currentLang === 'ar' ? 'fa-level-down ' : 'fa-level-up '
                    } fa-rotate-90 f-12 mr-2`}
                    aria-hidden="true"
                  ></i>
                  <i
                    className={`fa ${
                      expandCourse ? 'fa-sort-desc' : 'fa-sort-asc fa-rotate-90'
                    } text-skyblue f-12 mr-2`}
                    aria-hidden="true"
                  ></i>
                </div>
                <div className="">
                  {' '}
                  {course.get('course_name', '')}
                  {getVersionName(course)}
                </div>
              </div>
            </div>

            <div className="col-md-9 pr-0">
              <div className="border-radious-8 bg-white">
                <div className="row">
                  <div className="col-md-2 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <FrameworksDropdown
                          label={
                            isFrameworkAssigned
                              ? framework.get('code', '')
                              : t('master_graph.select')
                          }
                          frameworkId={framework.get('_id')}
                          frameworkFor="course"
                          course={course}
                          //disabled={isFrameworkAssignedInCurriculum}
                          disabled={false}
                          assignUnassignFramework={assignUnassignFramework}
                          mappedCount={course.get('plo_clo_count', 0)}
                        />
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b></b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <Button
                          variant="light"
                          id="dropdown-basic-text"
                          onClick={() => navigateToAddCloSlo('clo')}
                          disabled={!isFrameworkAssignedInCurriculum && !isFrameworkAssigned}
                        >
                          {course.get('clo_count', 0) ? (
                            <>
                              <i className="fa fa-plus pr-1" aria-hidden="true" />
                              {`${course.get('clo_count')} ${cloLabel}`}
                            </>
                          ) : (
                            `${t('add')} ${cloLabel}`
                          )}
                        </Button>
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <MapTypeDropdown
                          label={
                            course.get('mapping_type')
                              ? t(`frameworks.${course.get('mapping_type')}`)
                              : t('master_graph.select')
                          }
                          currentMapType={course.get('mapping_type', '')}
                          curriculumId={curriculum.get('_id')}
                          courseId={course.get('_course_id')}
                          assignedCourseId={course.get('_id')}
                          mapTypeFor="course"
                          // disabled={
                          //   isMapTypeAssignedInCurriculum ||
                          //   !course.get('clo_count', 0) ||
                          //   Boolean(course.get('plo_clo_count', 0))
                          // }
                          disabled={
                            course.get('clo_count', 0) === 0 && !isMapTypeAssignedInCurriculum
                          }
                          t={t}
                        />
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <Button
                          variant="light"
                          id="dropdown-basic-text"
                          onClick={() => navigateToMapping()}
                          disabled={disableMapPloClo()}
                        >
                          {course.get('plo_clo_count', 0) ? (
                            <>
                              <i className="fa fa-plus pr-1" aria-hidden="true" />
                              {`${course.get('plo_clo_count', 0)} ${ploLabel}-${cloLabel}`}
                            </>
                          ) : (
                            `${t('frameworks.map')} ${ploLabel}-${cloLabel}`
                          )}
                        </Button>
                      </b>
                      <b className="pt-3px">
                        {' '}
                        <i
                          className={`fa ${
                            currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                          } pr-1 pl-1`}
                          aria-hidden="true"
                        ></i>{' '}
                      </b>
                    </div>
                  </div>

                  <div className="col-md-2 pl-0 pr-0">
                    <div className="d-flex justify-content-between p-1">
                      <b>
                        <Button
                          variant="light"
                          id="dropdown-basic-text"
                          onClick={() => navigateToAddCloSlo('content-map')}
                          disabled={!course.get('plo_clo_count')}
                        >
                          {`${t('frameworks.map')} - ${capitalize(
                            curriculum.get('content_mapping_type', '')
                          )}`}
                        </Button>
                      </b>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </td>
      </tr>
      {expandCourse && (
        <tr>
          <td colSpan="7">
            <div className="row">
              <div className="col-md-3 pr-0">
                <p className="f-14 mb-0 pt-2 bold framework-course-slo">
                  <i
                    className={`fa ${
                      currentLang === 'ar' ? 'fa-level-down ' : 'fa-level-up '
                    } fa-rotate-90 f-12 mr-2`}
                    aria-hidden="true"
                  ></i>
                  {sloLabel}
                </p>
              </div>

              <div className="col-md-9 pr-0">
                <div className="border-radious-8 bg-white">
                  <div className="row">
                    <div className="col-md-2 pr-0"></div>

                    <div className="col-md-2 pl-0 pr-0">
                      <div className="d-flex justify-content-between p-1">
                        <b></b>
                        <b className="pt-3px">
                          {' '}
                          <i
                            className={`fa ${
                              currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                            } pr-1 pl-1`}
                            aria-hidden="true"
                          ></i>{' '}
                        </b>
                      </div>
                    </div>

                    <div className="col-md-2 pl-0 pr-0">
                      <div className="d-flex justify-content-between p-1">
                        <b>
                          <Button
                            variant="light"
                            id="dropdown-basic-text"
                            onClick={() => navigateToAddCloSlo('slo')}
                          >
                            {course.getIn(['sessionOrder', 'slo_count'], 0) ? (
                              <>
                                <i className="fa fa-plus pr-1" aria-hidden="true" />
                                {`${course.getIn(['sessionOrder', 'slo_count'])} ${sloLabel}`}
                              </>
                            ) : (
                              `${t('add')} ${sloLabel}`
                            )}
                          </Button>
                        </b>
                        <b className="pt-3px">
                          {' '}
                          <i
                            className={`fa ${
                              currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                            } pr-1 pl-1`}
                            aria-hidden="true"
                          ></i>{' '}
                        </b>
                      </div>
                    </div>

                    <div className="col-md-2 pl-0 pr-0">
                      <div className="d-flex justify-content-between p-1">
                        <b>
                          <Button variant="light" id="dropdown-basic-text">
                            {t('frameworks.alignment')}
                          </Button>
                        </b>
                        <b className="pt-3px">
                          {' '}
                          <i
                            className={`fa ${
                              currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                            } pr-1 pl-1`}
                            aria-hidden="true"
                          ></i>{' '}
                        </b>
                      </div>
                    </div>

                    <div className="col-md-2 pl-0 pr-0">
                      <div className="d-flex justify-content-between p-1">
                        <b>
                          <Button
                            variant="light"
                            id="dropdown-basic-text"
                            onClick={() => navigateToSLOMapping()}
                            disabled={
                              !course.get('clo_count', 0) ||
                              !course.getIn(['sessionOrder', 'slo_count'])
                            }
                          >
                            {course.getIn(['sessionOrder', 'clo_slo_count'], 0) ? (
                              <>
                                <i className="fa fa-plus pr-1" aria-hidden="true" />
                                {`${course.getIn([
                                  'sessionOrder',
                                  'clo_slo_count',
                                ])} ${cloLabel}-${sloLabel}`}
                              </>
                            ) : (
                              `${t('frameworks.map')} ${cloLabel}-${sloLabel}`
                            )}
                          </Button>
                        </b>
                        <b className="pt-3px">
                          {' '}
                          <i
                            className={`fa ${
                              currentLang === 'ar' ? 'fa-long-arrow-left' : 'fa-long-arrow-right'
                            } pr-1 pl-1`}
                            aria-hidden="true"
                          ></i>{' '}
                        </b>
                      </div>
                    </div>

                    <div className="col-md-2 pl-0 pr-0"></div>
                  </div>
                </div>
              </div>
            </div>
          </td>
        </tr>
      )}
    </React.Fragment>
  );
}

const FrameworksDropdown = connect((state) => {
  return {
    frameworkList: selectFrameworkList(state),
  };
}, actions)(function ({
  label,
  frameworkId,
  frameworkList,
  frameworkFor,
  course,
  curriculum,
  disabled = false,
  assignUnassignFramework,
  mappedCount = 0,
  t,
}) {
  function handleSelect(selectedFrameworkId, event) {
    if (selectedFrameworkId === 'optional' && label === 'Optional') {
      return;
    }
    if (selectedFrameworkId === 'optional') {
      assignUnassignFramework(
        {
          operation: 'remove',
          programId: getURLParams('_id', true),
          framework_id: frameworkId,
          framework_for: frameworkFor,
          ...(frameworkFor === 'curriculum' && { curriculum_id: curriculum.get('_id') }),
          ...(frameworkFor === 'course' && { course_id: course.get('_course_id') }),
        },
        false
      );
      return;
    }
    const framework = frameworkList.find(
      (framework) => framework.get('id') === selectedFrameworkId
    );
    if (!framework) return;
    assignUnassignFramework(
      {
        operation: 'add',
        programId: getURLParams('_id', true),
        framework_id: framework.get('id'),
        framework_for: frameworkFor,
        mappedCount,
        ...(frameworkFor === 'curriculum' && { curriculum_id: curriculum.get('_id') }),
        ...(frameworkFor === 'course' && { course_id: course.get('_course_id') }),
      },
      frameworkFor === 'curriculum' ? label === 'Optional' : label === 'Select'
    );
  }

  return (
    <Dropdown onSelect={handleSelect}>
      <Dropdown.Toggle variant="light" id="dropdown-basic" disabled={disabled}>
        {label}
      </Dropdown.Toggle>
      {(CheckPermission('pages', 'Program Input', 'Programs', 'Add Program') ||
        CheckPermission('pages', 'Program Input', 'Programs', 'Add Pre-requisite')) && (
        <Dropdown.Menu renderOnMount={false}>
          {frameworkFor === 'curriculum' && (
            <Dropdown.Item
              active={label === 'Optional'}
              className="framework-dropdown-item"
              eventKey="optional"
            >
              {t('frameworks.optional')}
            </Dropdown.Item>
          )}
          {frameworkList.map((framework) => (
            <Dropdown.Item
              key={framework.get('id')}
              active={framework.get('id') === frameworkId}
              className="framework-dropdown-item"
              eventKey={framework.get('id')}
            >
              {framework.get('code', '')}
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
      )}
    </Dropdown>
  );
});

const MapTypeDropdown = connect(
  null,
  actions
)(function ({
  label,
  mapTypeFor,
  isContent = false,
  currentMapType,
  curriculumId,
  courseId,
  assignedCourseId,
  disabled = false,
  updateMapType,
  t,
}) {
  function handleSelect(selectedMapType, event) {
    if (
      !isContent &&
      mapTypeFor === 'curriculum' &&
      selectedMapType === 'optional' &&
      label === 'Optional'
    ) {
      return;
    }
    if (
      !isContent &&
      mapTypeFor === 'course' &&
      selectedMapType === 'optional' &&
      label === 'Select'
    ) {
      return;
    }
    if (selectedMapType === currentMapType) return;
    if (!isContent && selectedMapType === 'optional') {
      selectedMapType = '';
    }
    updateMapType({
      programId: getURLParams('_id', true),
      mapTypeFor,
      curriculumId,
      ...(mapTypeFor === 'course' && { courseId, assignedCourseId }),
      isContent,
      [`${isContent ? 'content_' : ''}mapping_type`]:
        !isContent && selectedMapType === 'optional' ? '' : selectedMapType,
    });
  }

  return (
    <Dropdown onSelect={handleSelect}>
      <Dropdown.Toggle variant="light" id="dropdown-basic" disabled={disabled}>
        {label}
      </Dropdown.Toggle>
      {(CheckPermission('pages', 'Program Input', 'Programs', 'Add Program') ||
        CheckPermission('pages', 'Program Input', 'Programs', 'Add Pre-requisite')) && (
        <Dropdown.Menu renderOnMount={false}>
          {!isContent && (
            <Dropdown.Item
              active={label === 'Optional'}
              className="framework-dropdown-item"
              eventKey="optional"
            >
              {mapTypeFor === 'curriculum' ? 'Optional' : '--'}
            </Dropdown.Item>
          )}
          {(!isContent ? MAP_TYPES : CONTENT_MAP_TYPES).map((mapType) => (
            <Dropdown.Item
              key={`${mapTypeFor === 'curriculum' ? curriculumId : assignedCourseId}-${
                isContent ? 'content-' : ''
              }${mapType}`}
              eventKey={mapType}
              active={currentMapType === mapType}
              className="framework-dropdown-item"
            >
              {t(`frameworks.${mapType}`)}
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
      )}
    </Dropdown>
  );
});

Dashboard.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
  setData: PropTypes.func,
  t: PropTypes.func,
  frameworkLists: PropTypes.instanceOf(List),
  frameworkDashboard: PropTypes.instanceOf(Map),
  getFrameworkDashboardDataByProgramId: PropTypes.func,
  getFrameworkLists: PropTypes.func,
  addRemoveFramework: PropTypes.func,
  isFrameworksDashboardLoading: PropTypes.bool,
};

CurriculumItem.propTypes = {
  curriculum: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  location: PropTypes.object,
  setData: PropTypes.func,
  assignUnassignFramework: PropTypes.func,
  t: PropTypes.func,
};

YearLevelItem.propTypes = {
  curriculum: PropTypes.instanceOf(Map),
  yearLevel: PropTypes.instanceOf(Map),
  isFrameworkAssignedInCurriculum: PropTypes.bool,
  isMapTypeAssignedInCurriculum: PropTypes.bool,
  history: PropTypes.object,
  location: PropTypes.object,
  setData: PropTypes.func,
  assignUnassignFramework: PropTypes.func,
  t: PropTypes.func,
};

CourseItem.propTypes = {
  curriculum: PropTypes.instanceOf(Map),
  yearLevel: PropTypes.instanceOf(Map),
  course: PropTypes.instanceOf(Map),
  isFrameworkAssignedInCurriculum: PropTypes.bool,
  isMapTypeAssignedInCurriculum: PropTypes.bool,
  history: PropTypes.object,
  location: PropTypes.object,
  setData: PropTypes.func,
  assignUnassignFramework: PropTypes.func,
  t: PropTypes.func,
};

FrameworksDropdown.propTypes = {
  label: PropTypes.string,
  frameworkId: PropTypes.string,
  frameworkList: PropTypes.instanceOf(List),
  frameworkFor: PropTypes.string,
  course: PropTypes.instanceOf(Map),
  curriculum: PropTypes.instanceOf(Map),
  disabled: PropTypes.bool,
  assignUnassignFramework: PropTypes.func,
  t: PropTypes.func,
};

MapTypeDropdown.propTypes = {
  label: PropTypes.string,
  mapTypeFor: PropTypes.string,
  isContent: PropTypes.bool,
  currentMapType: PropTypes.string,
  curriculumId: PropTypes.string,
  courseId: PropTypes.string,
  assignedCourseId: PropTypes.string,
  disabled: PropTypes.bool,
  updateMapType: PropTypes.func,
  t: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    frameworkDashboard: selectFrameworkDashboard(state),
    frameworkLists: selectFrameworkList(state),
    isFrameworksDashboardLoading: selectIsFrameworksDashboardLoading(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(withTranslation()(Dashboard));
