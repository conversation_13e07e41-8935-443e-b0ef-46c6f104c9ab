import React, { useEffect, useState } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { connect } from 'react-redux';
import { NotificationManager } from 'react-notifications';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import PropTypes, { oneOfType } from 'prop-types';

import CalenderDetails from '../Home/CalenderDetails';
import CalenderInterimDetails from '../Home/Interim/CalenderDetails';
import CoursesInterim from '../Home/Interim/Courses';
import CoursesRegular from '../Home/Regular/Courses';
import { commonApiCall, iframeApiCall } from '../../../_reduxapi/actions/calender';
import Loader from '../../../Widgets/Loader/Loader';
import { changeYear, changeTerm } from '../../../_reduxapi/actions/calender';
import {
  interimChangeYear,
  interimChangeSemester,
} from '../../../_reduxapi/actions/interimCalendar';
import { jsUcfirstAll, removeURLParams } from '../../../utils';
import { CheckProgramDepartment } from '../../../Modules/Shared/Permissions';
import { selectActiveInstitutionCalendar, selectUserId } from '../../../_reduxapi/Common/Selectors';
import { Trans } from 'react-i18next';
import LocalStorageService from 'LocalStorageService';

const Home = ({
  apiCalled,
  token,
  interim,
  commonApiCall,
  isLoading,
  academic_year_name,
  //active_year,
  nav_bar_title,
  changeYear,
  interimChangeYear,
  regularYears,
  interimYears,
  interimChangeSemester,
  iframeApiCall,
  activeInstitutionCalendar,
  curriculum_years,
  interim_curriculum_years,
  terms,
  changeTerm,
}) => {
  const [loading, setLoading] = useState(false);
  const [count, setCount] = useState(0);
  const match = useRouteMatch();
  const history = useHistory();
  const [sem, setSem] = useState('semester1');

  const [iframeShow, setIframeShow] = useState(false);
  const [iframeCount, setIframeCount] = useState(0);
  const [iframePrgId, setIframePrgId] = useState(null);
  useEffect(() => {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    var iframeShow = params.get('iframeShow');
    var iframeProgramId = params.get('iframeProgramId');
    if (iframeCount === 0 && iframeShow !== null) {
      setIframeShow(true);
      setIframeCount(1);
      setIframePrgId(iframeProgramId);
    }
  }, [iframeCount]);

  let search = window.location.search;
  let params = new URLSearchParams(search);
  let p_id = params.get('programid');
  let p_name = params.get('pname');

  let urlYear = params.get('year');
  let urlName = params.get('pname');
  let urlPgId = params.get('programid');
  let urlTerm = params.get('term');

  useEffect(() => {
    changeTerm(urlTerm);
    changeYear(urlYear);
  }, [urlTerm, changeTerm, urlYear, changeYear]);
  const [currentPGAccess, setCurrentPGAccess] = useState(false);
  const c_years = interim ? interim_curriculum_years : curriculum_years;

  useEffect(() => {
    const savedPgId = LocalStorageService.getCustomToken('sp-id');
    if (c_years.length > 0 && terms.length > 0) {
      LocalStorageService.setCustomToken('sp-id', urlPgId);
      if (savedPgId === null || savedPgId !== urlPgId) {
        if (savedPgId !== urlPgId) {
          const location = history.location;
          const year = c_years[0];
          const term = terms[0].term_name;
          const splitPath = location.pathname.split('/');
          splitPath[3] = year;
          const changedPath = splitPath.join('/');
          const pathSearch =
            removeURLParams(location, ['term', 'year']) + `&year=${year}&term=${term}`;
          changeTerm(term);
          changeYear(year);
          history.push(changedPath + pathSearch);
        }
      }
    }
  }, [urlPgId, c_years]); //eslint-disable-line

  useEffect(() => {
    let hasProgramAccess = CheckProgramDepartment(p_id, true);
    setCurrentPGAccess(hasProgramAccess);
  }, [p_id]);

  useEffect(() => {
    // if (!apiCalled && token) {
    if (p_id !== null && p_name !== null && activeInstitutionCalendar.get('_id', '') !== '') {
      commonApiCall(
        token,
        NotificationManager,
        p_id,
        p_name,
        activeInstitutionCalendar.get('_id', '')
      );
    }
  }, [commonApiCall, p_id, p_name, activeInstitutionCalendar]); // eslint-disable-line

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      if (iframePrgId !== null && activeInstitutionCalendar.get('_id', '') !== '') {
        iframeApiCall(iframePrgId, activeInstitutionCalendar.get('_id', ''));
      }
      setLoading(false);
    }, 3000);
  }, [iframePrgId, activeInstitutionCalendar]); // eslint-disable-line
  // eslint-disable-next-line
  const printInterimPDF = (sems) => {
    setLoading(true);
    interimChangeSemester(sems);
    if (interimYears && interimYears.length > 0) {
      interimYears.forEach((year) => {
        printInterimTask(year);
      });
    }
  };
  const printInterimTask = (inc) => {
    setTimeout(function () {
      interimChangeYear('year' + inc);
      setTimeout(() => {
        document.getElementById('PrintCalendar').style.padding = '40px 20px 10px 0px';
        document.getElementById('PrintCalendar').style.minWidth = '167em';

        var strightView = document.getElementsByClassName('strightView');
        for (var i = 0; i < strightView.length; i++) {
          strightView[i].style.fontSize = '1.5em';
        }

        var pdfFont = document.getElementsByClassName('pdfFont');
        for (var j = 0; j < pdfFont.length; j++) {
          pdfFont[j].style.fontSize = '1.5em';
        }

        var printTitle = document.getElementsByClassName('printTitle');
        for (var k = 0; k < printTitle.length; k++) {
          printTitle[k].style.display = 'block';
        }

        var printViewHide = document.getElementsByClassName('printViewHide');
        for (var l = 0; l < printViewHide.length; l++) {
          printViewHide[l].style.display = 'none';
        }

        html2canvas(document.getElementById('PrintCalendar')).then((canvas) => {
          canvas.id = 'contentImage' + inc;
          canvas.setAttribute('class', 'contentImage');
          document.body.appendChild(canvas);

          document.getElementById('PrintCalendar').style.padding = '0px 0px 0px 0px';
          document.getElementById('PrintCalendar').style.minWidth = '';

          for (var pTitle = 0; pTitle < printTitle.length; pTitle++) {
            printTitle[pTitle].style.display = 'none';
          }
          for (var pVHide = 0; pVHide < printViewHide.length; pVHide++) {
            printViewHide[pVHide].style.display = 'block';
          }

          for (var sView = 0; sView < strightView.length; sView++) {
            strightView[sView].style.fontSize = '';
          }

          for (var font = 0; font < pdfFont.length; font++) {
            pdfFont[font].style.fontSize = '';
          }
        });
        setCount(inc);
      }, 500);
    }, 2000 * inc);
  };

  const printRegularPDF = () => {
    setLoading(true);
    if (regularYears && regularYears.length > 0) {
      document.getElementById('printViewHide').style.display = 'block';
      regularYears.forEach((year) => {
        printRegularTask(year);
      });
    }
  };

  const urlSwitchRegular = (inc) => {
    changeYear('year' + inc);
    history.push(
      `/${match.url
        .split('/')
        .filter((item, i) => i === 1)
        .reduce((acc, val) => val)}/${match.params.id}/${
        'year' + inc
      }?programid=${p_id}&year=${urlYear}&pname=${urlName}&term=${urlTerm}`
    );
  };

  const printRegularTask = (inc) => {
    setTimeout(function () {
      urlSwitchRegular(inc);
      setTimeout(() => {
        document.getElementById('PrintCalendar').style.padding = '40px 20px 10px 0px';
        document.getElementById('PrintCalendar').style.minWidth = '167em';

        var strightView = document.getElementsByClassName('strightView');
        for (var sView = 0; sView < strightView.length; sView++) {
          strightView[sView].style.fontSize = '1.5em';
        }

        var pdfFont = document.getElementsByClassName('pdfFont');
        for (var font = 0; font < pdfFont.length; font++) {
          pdfFont[font].style.fontSize = '1.3em';
        }

        var printTitle = document.getElementsByClassName('printTitle');
        for (var pTitle = 0; pTitle < printTitle.length; pTitle++) {
          printTitle[pTitle].style.display = 'block';
        }

        var printViewHide = document.getElementsByClassName('printViewHide');
        for (var pVHide = 0; pVHide < printViewHide.length; pVHide++) {
          printViewHide[pVHide].style.display = 'none';
        }

        html2canvas(document.getElementById('PrintCalendar')).then((canvas) => {
          canvas.id = 'contentImage' + inc;
          canvas.setAttribute('class', 'contentImage');
          document.body.appendChild(canvas);
          document.getElementById('PrintCalendar').style.padding = '0px 0px 0px 0px';
          document.getElementById('PrintCalendar').style.minWidth = '';

          for (var pTitle = 0; pTitle < printTitle.length; pTitle++) {
            printTitle[pTitle].style.display = 'none';
          }

          for (var pVHide = 0; pVHide < printViewHide.length; pVHide++) {
            printViewHide[pVHide].style.display = 'block';
          }

          for (var sView = 0; sView < strightView.length; sView++) {
            strightView[sView].style.fontSize = '';
          }

          for (var font = 0; font < pdfFont.length; font++) {
            pdfFont[font].style.fontSize = '';
          }
        });
        setCount(inc);
      }, 500);
    }, 2000 * inc);
  };

  useEffect(() => {
    if (!interim && regularYears.length > 0) {
      const checkLength = regularYears.length;
      // regularYears.length > 2 && getEnvAppEnvironment() !== 'ecs-indian'
      //   ? regularYears.length + 1
      //   : regularYears.length;
      // console.log('count', checkLength, count, regularYears);
      if (parseInt(count) === parseInt(checkLength)) {
        setTimeout(() => {
          var contentImage = document.body.querySelectorAll('canvas');
          var imgData = contentImage[0].toDataURL('image/jpeg', 1.0);
          // var imgWidth = 210; //210
          // var imgHeight = (contentImage[0].height * imgWidth) / contentImage[0].width;
          // var doc = new jsPDF('p', 'mm', [imgHeight, imgWidth]);
          // doc.addImage(imgData, 'jpeg', 0, 0, imgWidth, imgHeight);
          var doc = new jsPDF('p', 'mm', 'a3');
          doc.addImage(imgData, 'jpeg', 10, 10, 280, 360);
          for (var i = 1; i < contentImage.length; i++) {
            var imgData1 = contentImage[i].toDataURL('image/jpeg', 1.0);
            //var imgHeight1 = contentImage[i].height * imgWidth / contentImage[i].width;
            doc.addPage();
            //doc.addImage(imgData1, 'jpeg', 0, 0, imgWidth, imgHeight);
            doc.addImage(imgData1, 'jpeg', 10, 10, 280, 360);
          }
          let pageName = nav_bar_title + ' Academic Year (' + academic_year_name + ' G)';
          doc.save(jsUcfirstAll(pageName) + '.pdf');
          setLoading(false);
          urlSwitchRegular(regularYears[0]);
          document.getElementById('printViewHide').style.display = 'none';
          removeElements(document.querySelectorAll('canvas'));
          setCount(0);
        }, 1500);
      }
    } else if (interim && interimYears.length > 0) {
      const checkLength = interimYears.length > 2 ? interimYears.length + 1 : interimYears.length;
      if (parseInt(count) === parseInt(checkLength)) {
        //+1
        setTimeout(() => {
          var contentImage = document.body.querySelectorAll('canvas');
          var imgData = contentImage[0].toDataURL('image/jpeg', 1.0);
          // var imgWidth = 310; //210
          // var imgHeight = (contentImage[0].height * imgWidth) / contentImage[0].width;
          // var doc = new jsPDF('p', 'mm', [imgHeight, imgWidth]);
          // doc.addImage(imgData, 'jpeg', 0, 0, imgWidth, imgHeight);
          var doc = new jsPDF('p', 'mm', 'a3');
          doc.addImage(imgData, 'jpeg', 10, 10, 280, 340);
          for (var i = 1; i < contentImage.length; i++) {
            var imgData1 = contentImage[i].toDataURL('image/jpeg', 1.0);
            //var imgHeight1 = contentImage[i].height * imgWidth / contentImage[i].width;
            doc.addPage(); //'p', 'mm', [imgHeight1, imgWidth]
            //doc.addImage(imgData1, 'jpeg', 0, 0, imgWidth, imgHeight);
            doc.addImage(imgData1, 'jpeg', 10, 10, 280, 340);
          }
          let pageName = nav_bar_title + ' Academic Year (' + academic_year_name + ' G) - ' + sem;
          doc.save(jsUcfirstAll(pageName) + '.pdf');
          setLoading(false);
          interimChangeYear('year' + interimYears[0]);
          removeElements(document.querySelectorAll('canvas'));
          if (sem === 'semester1') {
            setSem('semester2');
            setCount(0);
            printInterimPDF('semester2');
          } else {
            setSem('semester1');
            setCount(0);
            interimChangeSemester('semester1');
          }
        }, 1500);
      }
    }
  }, [count]); // eslint-disable-line

  const removeElements = (elms) => elms.forEach((el) => el.remove());

  return (
    <div className="main">
      <div id="printViewHide"></div>
      <Loader isLoading={isLoading || loading} />
      {apiCalled ? (
        interim ? (
          <>
            <CalenderInterimDetails
              clickedPDF={() => printInterimPDF('semester1')}
              iframeShow={iframeShow}
              currentPGAccess={currentPGAccess}
              activeInstitutionCalendar={activeInstitutionCalendar}
            />
            <div id="PrintCalendar">
              <CoursesInterim
                iframeShow={iframeShow}
                currentPGAccess={currentPGAccess}
                programId={p_id}
              />
            </div>
            {/* <CalenderDetails />
            <Courses /> */}
          </>
        ) : (
          <>
            <CalenderDetails
              clickedPDF={printRegularPDF}
              iframeShow={iframeShow}
              currentPGAccess={currentPGAccess}
              activeInstitutionCalendar={activeInstitutionCalendar}
            />
            <div id="PrintCalendar">
              <CoursesRegular
                iframeShow={iframeShow}
                currentPGAccess={currentPGAccess}
                programId={p_id}
              />
            </div>
          </>
        )
      ) : (
        <>
          <Loader isLoading={apiCalled} />
          <div className="pt-3">
            <div className="col-md-12">
              <div className="notpublished-screen">
                <div className="notpublished">
                  <h2>
                    {' '}
                    <Trans i18nKey={'program_calendar.still_not_been_published'}></Trans>
                  </h2>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

Home.propTypes = {
  apiCalled: PropTypes.bool,
  token: PropTypes.string,
  interim: PropTypes.bool,
  commonApiCall: PropTypes.func,
  isLoading: PropTypes.bool,
  academic_year_name: PropTypes.string,
  nav_bar_title: PropTypes.string,
  changeYear: PropTypes.func,
  interimChangeYear: PropTypes.func,
  regularYears: oneOfType([PropTypes.array, PropTypes.object]),
  interimYears: oneOfType([PropTypes.array, PropTypes.object]),
  interimChangeSemester: PropTypes.func,
  iframeApiCall: PropTypes.func,
  activeInstitutionCalendar: PropTypes.object,
  changeTerm: PropTypes.func,
  curriculum_years: PropTypes.array,
  interim_curriculum_years: PropTypes.array,
  terms: PropTypes.array,
};

const mapStateToProps = function (state) {
  const { calender, interimCalendar } = state;
  return {
    token: selectUserId(state),
    interim: calender.interim,
    apiCalled: calender.commonApiCalled,
    isLoading: interimCalendar.isLoading,
    academic_year_name: calender.academic_year_name || interimCalendar.academic_year_name,
    nav_bar_title:
      calender.nav_bar_title !== ''
        ? calender.nav_bar_title.replace('Calendar', '')
        : '' || interimCalendar.nav_bar_title !== ''
        ? interimCalendar.nav_bar_title.replace('Calendar', '')
        : '',
    active_year: calender.active_year || interimCalendar.active_year,
    regularYears:
      calender.year_level &&
      calender.year_level.length > 0 &&
      calender.year_level.map((item) => item.year.replace('year', '')),
    interimYears: interimCalendar.curriculum_years,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    curriculum_years: calender.curriculum_years,
    interim_curriculum_years: interimCalendar.curriculum_years,
    terms: calender?.term !== undefined ? calender?.term : [],
  };
};

export default connect(mapStateToProps, {
  commonApiCall,
  changeYear,
  interimChangeYear,
  interimChangeSemester,
  iframeApiCall,
  changeTerm,
})(Home);
