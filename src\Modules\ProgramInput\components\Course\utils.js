import { List, Map } from 'immutable';
import { createTheme } from '@mui/material/styles';
import i18n from '../../../../i18n';
import { t } from 'i18next';

const COURSE_TYPES = [
  { name: i18n.t('constant.standard'), value: 'standard' },
  { name: i18n.t('constant.selective'), value: 'selective' },
];

const COURSE_STATUS = [
  { name: 'Assigned', value: 'assigned' },
  { name: 'Not Assigned', value: 'not_assigned' },
];

// const courseKeyMap = {
//   course_type: 'Course Type',
//   course_recurring: 'Course Recurring',
//   course_code: 'Course Code',
//   course_name: 'Course Name',
//   duration: 'Duration',
//   participating: 'Delivering Subjects',
//   administration: 'Administrating Subject',
//   credit_hours: 'Credit Hours',
//   allow_editing: 'Allow to edit credit hours while scheduling',
//   achieve_target: 'Achieve targeted credit hours',
// };

function getCourseValidation(course, isDraft) {
  if (isDraft) {
    return course.course_type ? [] : [t('course_type')];
  }
  const invalidFields = [];
  Object.keys(course).forEach((key) => {
    switch (key) {
      case 'course_type':
      case 'course_code':
      case 'course_name':
        if (!course[key]) {
          invalidFields.push(`${t(`program_input.course_key_map.${key}`)}`); //todo
        }
        break;
      case 'duration':
        if (Number(course[key]) <= 0) {
          invalidFields.push(`${t(`program_input.course_key_map.${key}`)}`);
        }
        break;
      case 'administration':
        if (Object.keys(course[key]).length === 0) {
          invalidFields.push(`${t(`program_input.course_key_map.${key}`)}`);
        }
        break;
      case 'participating':
        if (Array.isArray(course[key]) && course[key].length === 0) {
          invalidFields.push(`${t(`program_input.course_key_map.${key}`)}`);
        }
        break;
      case 'course_recurring':
        if (
          course.course_type === 'selective' &&
          Array.isArray(course[key]) &&
          course[key].length === 0
        ) {
          invalidFields.push(`${t(`program_input.course_key_map.${key}`)}`);
        }
        break;
      case 'course_occurring':
        if (
          course.course_type !== 'selective' &&
          Array.isArray(course[key]) &&
          course[key].length === 0
        ) {
          invalidFields.push(`${t(`program_input.course_key_map.${key}`)}`);
        }
        break;
      case 'allow_editing':
        if (course[key] === null) {
          invalidFields.push(`${t(`program_input.course_key_map.${key}`)}`);
        }
        break;
      default:
        break;
    }
  });
  return invalidFields;
}

function getCreditHoursValidation(creditHours, isDraft) {
  if (isDraft) {
    return [];
  }
  const invalidFields = [];
  const activeDeliveryTypeCount = {};
  creditHours.forEach((sessionType) => {
    if (invalidFields.length) return;
    if (sessionType.credit_hours === '') {
      invalidFields.push(i18n.t('messages.session_zero_error'));
      return;
    }
    if (!Number(sessionType.contact_hours)) {
      invalidFields.push(i18n.t('messages.session_type_zero_error'));
      return;
    }

    if (sessionType.duration_per_contact_hour === '') {
      invalidFields.push(i18n.t('messages.duration_zero_error'));
      return;
    }
    if (sessionType.duration_per_contact_hour < 0) {
      invalidFields.push(i18n.t('messages.duration_type_zero_error'));
      return;
    }

    const sessionId = sessionType._session_id;
    if (Number(sessionType.credit_hours)) {
      if (activeDeliveryTypeCount[sessionId] === undefined) {
        activeDeliveryTypeCount[sessionId] = 0;
      }
    }
    sessionType.delivery_type.forEach((deliveryType) => {
      if (invalidFields.length) return;
      if (deliveryType.isActive && Number(sessionType.credit_hours)) {
        if (activeDeliveryTypeCount[sessionId]) {
          activeDeliveryTypeCount[sessionId] = activeDeliveryTypeCount[sessionId] + 1;
        } else {
          activeDeliveryTypeCount[sessionId] = 1;
        }
      }
      if (!Number(deliveryType.duration)) {
        invalidFields.push(t('messages.delivery_type_zero'));
        return;
      }
    });
  });
  if (invalidFields.length) {
    return invalidFields;
  }
  const hours = creditHours.reduce((acc, sessionType) => Number(sessionType.credit_hours) + acc, 0);
  if (!hours) {
    return [t('program_input.error_strings.session_type_should_be_greater_than_zero')];
  }
  if (!Object.keys(activeDeliveryTypeCount).length) {
    return [t('program_input.error_strings.no_delivery_type_selected')];
  }
  return Object.values(activeDeliveryTypeCount).includes(0)
    ? [t('program_input.error_strings.select_delivery_type')]
    : [];
}

function getSessionFlowValidation(sessionFlow, isDraft) {
  const invalidFields = [];
  if (isDraft) {
    if (sessionFlow.length === 0) {
      return t('program_input.error_strings.no_data');
    }
    sessionFlow.forEach((s, index) => {
      const i = index + 1;
      if (!s.delivery_type) {
        invalidFields.push(i);
      }
    });
    if (invalidFields.length) {
      return `${t('program_input.error_strings.choose_delivery_type')} (${invalidFields.join(
        ', '
      )}) ${t('program_input.error_strings.to_save_draft')}`;
    }
  }
  // if (sessionFlow.length === 0) {
  //   return i18n.t('messages.add_one_session');
  // }
  sessionFlow.forEach((s, index) => {
    const i = index + 1;
    Object.keys(s).forEach((key) => {
      switch (key) {
        case 'delivery_type':
        case 'delivery_topic':
          if (!s[key] && !invalidFields.includes(i)) {
            invalidFields.push(i);
          }
          break;
        case 'duration':
          if (Number(s[key]) <= 0 && !invalidFields.includes(i)) {
            invalidFields.push(i);
          }
          break;
        case 'subjects':
          if (s[key].length === 0 && !invalidFields.includes(i)) {
            invalidFields.push(i);
          }
          break;
        default:
          break;
      }
    });
  });
  return invalidFields.length
    ? // ? `Some of the fields in S.No(s). (${invalidFields.join(', ')}) is either empty or invalid`
      i18n.t('messages.empty_field')
    : '';
}

function getAchieveTargetValidation(course, sessionFlow) {
  const greaterThanContactHours = [];
  const lesserThanContactHours = [];
  const sessionTypes = course.get('credit_hours', List());
  const achieveTarget = course.get('achieve_target');
  sessionTypes.forEach((c) => {
    const creditHours = Number(c.get('credit_hours', '0') || '0');
    const contactHours = Number(c.get('contact_hours', '0') || '0') * creditHours;
    if (contactHours <= 0) return;
    const sessionTypeName = c.get('type_name', '');
    const totalDuration =
      sessionFlow
        .filter((e) => e.get('_session_id') === c.get('_session_id'))
        .reduce((acc, e) => acc + Number(e.get('duration', '0') || '0'), 0) /
      Number(c.get('duration_per_contact_hour', '0') || '0');
    const achievedHours = Number(
      totalDuration.toFixed(totalDuration === 0 || totalDuration % 1 === 0 ? 0 : 2)
    );
    if (achieveTarget) {
      if (achievedHours > contactHours) {
        greaterThanContactHours.push(sessionTypeName);
      }
      if (achievedHours < contactHours) {
        lesserThanContactHours.push(sessionTypeName);
      }
    } else if (achievedHours > contactHours) {
      greaterThanContactHours.push(sessionTypeName);
    }
  });
  // if (greaterThanContactHours.length) {
  //   return `${i18n.t('messages.contact_exceeded')} ${greaterThanContactHours.join(', ')}`;
  // }
  if (lesserThanContactHours.length) {
    return `${i18n.t('messages.contact_achieved')} ${lesserThanContactHours.join(', ')}`;
  }
  return '';
}

function getSubjects(course) {
  return course
    .get('participating', List())
    .reduce((acc, s) => {
      return acc.push(
        Map({
          _subject_id: s.get('_subject_id'),
          subject_name: s.get('subject_name'),
          name: s.get('subject_name'),
          value: s.get('_subject_id'),
        })
      );
    }, List())
    .toJS();
}

function getAssignCourseValidation(data) {
  const invalidFields = [];
  const { course_duration = {}, course_shared_with = [] } = data;
  if (!course_duration.start_week) {
    return i18n.t('messages.start_week');
  }
  if (!course_shared_with.length) {
    return '';
  }
  course_shared_with.forEach((shared, i) => {
    Object.keys(shared).forEach((k) => {
      if (invalidFields.includes(i + 1)) {
        return;
      }
      if (!shared[k]) {
        invalidFields.push(i + 1);
      }
    });
  });
  return invalidFields.length
    ? `${t('program_input.error_strings.some_of_the_fields_empty')}${invalidFields.join(', ')}${t(
        'program_input.error_strings.is_empty'
      )}`
    : '';
}

function isWeekCheckboxChecked(editedSessionFlow) {
  let checked = false;
  editedSessionFlow.forEach((s) => {
    if (checked) return;
    if (Number(s.getIn(['week', 'week_no'], ''))) {
      checked = true;
    }
  });
  return checked;
}

function getSessionFlowWeekValidation(editedSessionFlow) {
  if (!isWeekCheckboxChecked(editedSessionFlow)) {
    return '';
  }
  const invalidFields = [];
  const sessionFlow = editedSessionFlow.toJS();
  sessionFlow.forEach((s, index) => {
    const i = index + 1;
    Object.keys(s).forEach((key) => {
      switch (key) {
        case 'week':
          if (!Number(s.week ? s.week.week_no : '')) {
            invalidFields.push(i);
          }
          break;
        default:
          break;
      }
    });
  });
  return invalidFields.length
    ? `${i18n.t('messages.week_error')} (${invalidFields.join(', ')})`
    : '';
}

const FILTER_ITEM_HEIGHT = 48;
const FILTER_ITEM_PADDING_TOP = 8;
const FILTER_MENU_PROPS = {
  PaperProps: {
    style: {
      maxHeight: FILTER_ITEM_HEIGHT * 4.5 + FILTER_ITEM_PADDING_TOP,
      width: 150,
    },
  },
  getContentAnchorEl: null,
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'center',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'center',
  },
  variant: 'menu',
};

const CHECKBOX_THEME = createTheme({
  palette: {
    primary: {
      main: '#3E95EF',
    },
  },
  overrides: {
    MuiCheckbox: {
      indeterminate: {
        color: '#3E95EF',
      },
    },
  },
});
function TypeCasting(data) {
  if (typeof data === typeof 1) {
    return Number(data) === 'undefined' ? '' : Number(data);
  } else if (typeof data === typeof true) {
    return data === undefined ? '' : data;
  } else {
    return String(data) === 'undefined' ? '' : String(data).trim();
  }
}

export {
  getSubjects,
  getCourseValidation,
  getCreditHoursValidation,
  getSessionFlowValidation,
  getAchieveTargetValidation,
  isWeekCheckboxChecked,
  getSessionFlowWeekValidation,
  getAssignCourseValidation,
  TypeCasting,
  COURSE_TYPES,
  COURSE_STATUS,
  FILTER_MENU_PROPS,
  CHECKBOX_THEME,
};
