import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';
import swal from 'sweetalert2';
import PropTypes from 'prop-types';

import {
  FlexWrapper,
  Null,
  Select,
  EventWrapper,
  ModalWrapper,
  ModalBackgroundWrapper,
  PrimaryButton,
} from '../Styled';
import {
  eventDelete,
  levelDateSave,
  resetMessage,
  updateLevelEvents,
  saveRotationCount,
} from '../../../_reduxapi/actions/calender';
import AddEvent from '../Modal/Events/AddEvent';
import EventRows from '../UtilityComponents/EventRows';
import DateInput from '../UtilityComponents/DateInput';
import { NotificationManager } from 'react-notifications';
import Loader from '../../../Widgets/Loader/Loader';
import moment from 'moment';
import {
  timeFormat,
  ucFirst,
  isRotationProgram,
  getTranslatedDuration,
  levelRename,
  // isWeekStartDay,
  // isWeekEndDay,
} from '../../../utils';
import { t } from 'i18next';
import { Trans } from 'react-i18next';

const LevelField = ({
  id,
  _calender_id,
  end,
  start,
  eventDelete,
  eventsState,
  levelDateSave,
  isLoading,
  updateLevelEvents,
  saveRotationCount,
  institutionCalenderEvents,
  programId,
  appRotationLevel,
  activeTerm,
  levelStartDate,
  levelEndDate,
}) => {
  const [levelConfig, setLevelConfig] = eventsState;
  const match = useRouteMatch();
  const active = match.params.year || 'year2';
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let programName = params.get('pname').toLowerCase() || '';

  const dataAlign = (startDate, endDate, level, cb) => {
    let final = {};
    let error = false;
    if (_calender_id === '') {
      NotificationManager.error('Program calendar id is missing, try again');
      error = true;
    }
    if (!error && startDate !== '' && endDate !== '') {
      final._id = _calender_id;
      final.level_no = level;
      final.batch = activeTerm;
      final.start_date = moment(startDate).format('YYYY-MM-DD');
      final.end_date = moment(endDate).format('YYYY-MM-DD');
      cb(final, NotificationManager);
    }
  };

  const eventDeleteConfirm = (id, pro_id, level, batch, eventDelete) => {
    let error = false;
    if (level === '') {
      NotificationManager.error('Level is missing');
      error = true;
    } else if (batch === '') {
      NotificationManager.error('Batch is missing');
      error = true;
    }

    if (!error) {
      swal
        .fire({
          title: t('sure_delete'),
          text: t('once_deleted_cant_recover'),
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: t('yes_delete'),
          cancelButtonText: t('no_keep'),
          dangerMode: true,
        })
        .then((res) => {
          if (res.isConfirmed) {
            eventDelete(id, pro_id, level, batch, NotificationManager);
          }
        });
    }
  };

  const rotationConfirm = (level, count) => {
    let data = {};
    data._calendar_id = _calender_id;
    data.level_no = level;
    data.batch = activeTerm;
    data.rotation_count = count;
    return { ...data };
  };

  const updateEvent = (data, config) => {
    let error = false;
    let final = { event_name: {} };

    const start = data.start_date + ' ' + data.start_time + ':00:000';
    const end = data.end_date + ' ' + data.end_time + ':00:000';
    const check_start = data.start_date + 'T' + data.start_time + ':00.000Z';
    const check_end = data.end_date + 'T' + data.end_time + ':00.000Z';
    config.events.forEach((item) => {
      if (Date.parse(item.start_time) <= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_start)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) <= Date.parse(check_end) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.start_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.end_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      }
    });
    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.end_date) < Date.parse(data.start_date)) {
      NotificationManager.error('End date should not be lesser than the start date');
      error = true;
    } else if (Date.parse(start) > Date.parse(end) || Date.parse(start) === Date.parse(end)) {
      NotificationManager.error('End time should be greater than Start time');
      error = true;
    } else if (Date.parse(config.min) > Date.parse(start)) {
      error = true;
      NotificationManager.error('Start date should not be lesser than level start date');
    } else if (Date.parse(config.max) < Date.parse(start)) {
      error = true;
      NotificationManager.error('Start date should not be greater than level end date');
    } else if (Date.parse(config.max) < Date.parse(end)) {
      error = true;
      NotificationManager.error('End date should not be greater than level end date');
    }

    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    final.event_calendar = data.payload.event_calendar;
    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = data.start_date;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final.end_date = data.end_date;
    final._event_id = data._id;
    final.batch = activeTerm;
    final.level_no = data.level_num;
    final._calendar_id = data.cal_id;

    if (!error) {
      updateLevelEvents(final, NotificationManager, setLevelConfig);
    }
  };

  // const levelStartDate = levelConfig?.[active]?.level_one_start_date;
  // const levelEndDate =
  //   levelConfig?.[active]?.level_two_end_date !== undefined &&
  //   levelConfig?.[active]?.level_two_end_date !== ''
  //     ? levelConfig?.[active]?.level_two_end_date
  //     : levelConfig?.[active]?.level_one_end_date;

  const stringWithQuotes = appRotationLevel;
  const stringWithoutQuotes = stringWithQuotes.replace(/'/g, ''); // Remove single quotes
  const levelRotationArray = JSON.parse(stringWithoutQuotes);

  const temp = () => (
    <ModalWrapper>
      <ModalBackgroundWrapper>
        <h3 className="text-left">
          <Trans i18nKey={'events.edit_event'}></Trans>
        </h3>
        <p className="text-left">
          {' '}
          <Trans i18nKey={'select_date_to_sync'}></Trans>
        </p>
        <AddEvent
          data={levelConfig.edit}
          method={setLevelConfig}
          min_len={
            levelConfig?.edit_config?.min !== undefined
              ? new Date(levelConfig?.edit_config?.min)
              : levelStartDate
          }
          max_len={
            levelConfig?.edit_config?.max !== undefined
              ? new Date(levelConfig?.edit_config?.max)
              : levelEndDate
          }
          levelStartDate={levelStartDate}
          levelEndDate={levelEndDate}
        />
        <FlexWrapper>
          <Null />
          <PrimaryButton className="light" onClick={() => setLevelConfig({ type: 'OFF' })}>
            <Trans i18nKey={'cancel'}></Trans>
          </PrimaryButton>
          <PrimaryButton
            className="bordernone"
            onClick={() => updateEvent(levelConfig.edit, levelConfig.edit_config)}
          >
            <Trans i18nKey={'save'}></Trans>
          </PrimaryButton>
        </FlexWrapper>
      </ModalBackgroundWrapper>
    </ModalWrapper>
  );

  const disableRotation = (rotation) => {
    return rotation.filter((item) => item.course.length > 0).length > 0;
  };
  return (
    <Fragment>
      <Loader isLoading={isLoading} />
      <Fragment>{levelConfig.modal && temp()}</Fragment>
      <div className="container-fluid">
        {levelConfig[active]['level'] !== undefined &&
          levelConfig[active]['level'].length > 0 &&
          levelConfig[active]['level']
            .filter((item, index) => (activeTerm !== '' ? activeTerm === item.term : index === 0))
            .map((level, activeIndex) => {
              let level_no = level.level_no !== '' ? level.level_no : '';
              let term = level.term;
              let level_title = ucFirst(level_no);
              let start_date =
                level.start_date !== undefined && level.start_date !== ''
                  ? moment(level.start_date).format('D MMM YYYY')
                  : '';
              let end_date =
                level.end_date !== undefined && level.end_date !== ''
                  ? moment(level.end_date).format('D MMM YYYY')
                  : '';

              let rotation_count = level.rotation_count;
              let events = level.events;
              let rotationCourses =
                level.rotation_course !== undefined ? level.rotation_course : [];
              let strLevel = level_no.replace('Level ', '');

              return (
                <Fragment key={activeIndex}>
                  <div className="p-3">
                    <div className="row">
                      <div className="col-md-3 col-lg-3 col-xl-1 col-sm-12 col-12">
                        <h3 className="f-15 pt-3">{levelRename(level_title, programId)}</h3>
                      </div>
                      <div className="col-md-3 col-lg-3  col-xl-2 col-sm-12 col-12 datepickerAr">
                        <DateInput
                          datepicker={true}
                          // isWeekStartDay={isWeekStartDay()}
                          placeholderText={t('events.start_date')}
                          selected={start_date !== '' ? new Date(start_date) : null}
                          value={getTranslatedDuration(start_date)}
                          edit={(value) => {
                            setLevelConfig({
                              type: 'ON_INPUT',
                              payload: value,
                              name: 'start_date',
                              year: active,
                              index: activeIndex,
                              term: activeTerm,
                              level_num: level.level_no,
                            });
                            dataAlign(
                              value,
                              '',
                              level_no,
                              levelDateSave,
                              'start_date',
                              active,
                              activeIndex
                            );
                          }}
                          title={t('events.start_date')}
                          minDate={start !== '' ? new Date(start) : ''}
                          maxDate={end !== '' ? new Date(end) : null}
                        />
                      </div>
                      <div className="col-md-3 col-lg-3 col-xl-2 col-sm-12 col-12 datepickerAr">
                        <DateInput
                          datepicker={true}
                          // isWeekEndDay={isWeekEndDay()}
                          placeholderText={t('events.end_date')}
                          selected={end_date !== '' ? new Date(end_date) : null}
                          value={getTranslatedDuration(end_date)}
                          edit={(value) => {
                            setLevelConfig({
                              type: 'ON_INPUT',
                              payload: value,
                              name: 'end_date',
                              year: active,
                              index: activeIndex,
                              term: activeTerm,
                              level_num: level.level_no,
                            });
                            dataAlign(
                              start_date,
                              value,
                              level_no,
                              levelDateSave,
                              'end_date',
                              active,
                              activeIndex
                            );
                          }}
                          title={t('events.end_date')}
                          minDate={
                            start_date !== ''
                              ? new Date(start_date)
                              : start !== ''
                              ? new Date(start)
                              : ''
                          }
                          maxDate={end !== '' ? new Date(end) : null}
                        />
                      </div>
                      <div className="col-md-12 col-lg-12  col-xl-6 col-sm-12 col-12 ">
                        <div className="row">
                          <div
                            className={
                              levelRotationArray.some(
                                (item) => parseInt(item) === parseInt(strLevel)
                              ) && isRotationProgram(programName)
                                ? 'col-md-6'
                                : 'col-md-12'
                            }
                          >
                            {/* {levelConfig[active]["level_one_start_date"] &&	
                          levelConfig[active]["level_one_end_date"] && (	
                            <p className="text-left  f-14  mb-0 fond-bold-normal">	
                              Level start date and end date are defined as per the	
                              institute calendar
                            </p>	
                          )}	 */}
                          </div>
                          <div className="col-md-6">
                            {levelRotationArray.some(
                              (item) => parseInt(item) === parseInt(strLevel)
                            ) &&
                              isRotationProgram(programName) && (
                                <Fragment>
                                  <br />
                                  <b className="f-13">{t('no_of_rotations')}</b>
                                  <Select
                                    disabled={disableRotation(rotationCourses)}
                                    name="rotation_count"
                                    value={rotation_count}
                                    onChange={(e) => {
                                      setLevelConfig({
                                        type: 'ON_INPUT',
                                        payload: e.target.value,
                                        name: e.target.name,
                                        year: active,
                                        index: activeIndex,
                                        term: activeTerm,
                                        level_num: level.level_no,
                                      });
                                      saveRotationCount(
                                        rotationConfirm(level_no, e.target.value),
                                        NotificationManager
                                      );
                                    }}
                                  >
                                    <option value="0">{t('program_calendar.select')}</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                  </Select>
                                </Fragment>
                              )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <EventWrapper of_scroll="auto" className="go-wrapper-width">
                    <EventRows show="title" />
                    <div className="go-wrapper-height">
                      <Fragment>
                        {events && events.length !== 0 && (
                          <Fragment>
                            {events
                              .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
                              .map((item, i, arr) => {
                                let editEnable = true;
                                if (
                                  id !== null &&
                                  institutionCalenderEvents !== undefined &&
                                  institutionCalenderEvents.length > 0
                                ) {
                                  let filteredEvent = institutionCalenderEvents
                                    .filter((list) => {
                                      return list._id === item._event_id;
                                    })
                                    .reduce((_, el) => {
                                      return el._id;
                                    }, 0);
                                  if (filteredEvent !== 0) {
                                    editEnable = false;
                                  }
                                }
                                return (
                                  <EventRows
                                    key={item._id}
                                    show="content"
                                    content={item}
                                    i={i}
                                    editHide={!editEnable}
                                    edit={() =>
                                      setLevelConfig({
                                        type: 'SHOW_MODAL',
                                        payload: item,
                                        id: item._event_id,
                                        year: active,
                                        index: i,
                                        from: 'level_one_course_events',
                                        level_num: level_no,
                                        min: start_date,
                                        max: end_date,
                                        events: arr.filter((item, num) => num !== i),
                                        cal_id: _calender_id,
                                      })
                                    }
                                    // editHide={(() => {
                                    //   return events.map((item) => item._id).includes(item._event_id);
                                    // })()}
                                    del={() =>
                                      eventDeleteConfirm(
                                        item._event_id,
                                        levelConfig[active]['id'],
                                        level_no,
                                        term,
                                        eventDelete
                                      )
                                    }
                                  />
                                );
                              })}
                          </Fragment>
                        )}
                      </Fragment>
                    </div>
                  </EventWrapper>
                </Fragment>
              );
            })}
      </div>
    </Fragment>
  );
};

LevelField.propTypes = {
  id: PropTypes.string,
  _calender_id: PropTypes.string,
  end: PropTypes.string,
  start: PropTypes.string,
  programId: PropTypes.string,
  eventDelete: PropTypes.func,
  eventsState: PropTypes.object,
  levelDateSave: PropTypes.func,
  isLoading: PropTypes.bool,
  updateLevelEvents: PropTypes.func,
  saveRotationCount: PropTypes.func,
  institutionCalenderEvents: PropTypes.object,
  appRotationLevel: PropTypes.array,
  activeTerm: PropTypes.string,
  levelStartDate: PropTypes.string,
  levelEndDate: PropTypes.string,
};

const mapStateToProps = ({ calender, auth }) => ({
  events: calender.academic_year_events,
  start: calender.academic_year_start !== '' ? moment(calender.academic_year_start).format() : '',
  end: calender.academic_year_end !== '' ? moment(calender.academic_year_end).format() : '',
  id: calender.institution_Calender_Id,
  institutionCalenderEvents: calender.academic_year_events,
  _calender_id: calender.program_calender_id,
  appRotationLevel: auth?.loggedInUserData?.services?.REACT_APP_ROTATION_LEVEL,
  activeTerm: calender.active_term,
});
export default connect(mapStateToProps, {
  eventDelete,
  levelDateSave,
  resetMessage,
  updateLevelEvents,
  saveRotationCount,
})(LevelField);
