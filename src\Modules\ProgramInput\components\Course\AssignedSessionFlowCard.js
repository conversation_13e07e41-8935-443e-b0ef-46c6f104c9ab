import React from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { Table } from 'react-bootstrap';

import ContactHoursSummary from './ContactHoursSummary';
import { Trans } from 'react-i18next';
import { indVerRename } from 'utils';

function AssignedSessionFlowCard({ course, sessionFlow, editedSessionFlow, levelName, isShared }) {
  function getWeekNo(session) {
    const weeks = session.get('week', List());
    if (weeks === null) return 'NA';
    if (weeks.isEmpty()) return 'NA';
    const week = weeks.find((s) => s.get('level_no') === levelName);
    if (!week) return 'NA';
    return week.get('week_no', '');
  }

  return (
    <div className="program_card bg-white min_h">
      <div className="session_card">
        <div className="row">
          <div className="col-md-12">
            <p className="mb-1 bold">
              <Trans i18nKey={'constant.session_flow'}></Trans>
            </p>
            <div className="row ml-0">
              <div className="pr-2">
                <span className="f-14">
                  <Trans i18nKey={'contact_hours_summary'}></Trans>:{' '}
                </span>
              </div>
              <ContactHoursSummary
                course={course}
                sessionFlow={editedSessionFlow}
                programId={course.get('_program_id', '')}
              />
            </div>
          </div>

          <div className="col-md-12 mt-2 min_h">
            <Table responsive hover>
              <thead className="th-change">
                <tr>
                  <th>
                    <Trans i18nKey={'s_no'}></Trans>.
                  </th>
                  <th>
                    <Trans i18nKey={'Delivery_Type'}></Trans>
                  </th>
                  <th>
                    <Trans i18nKey={'labels.delivery_symbol'}></Trans>
                  </th>
                  <th>
                    <Trans i18nKey={'delivery_number'}></Trans>
                  </th>
                  <th>
                    <Trans i18nKey={'delivery_topic'}></Trans>
                  </th>
                  <th>
                    <Trans i18nKey={'configuration.subject'}></Trans>
                  </th>
                  <th>
                    <Trans i18nKey={'duration_in_min'}></Trans>
                  </th>
                  {!isShared && (
                    <th>
                      <Trans i18nKey={'curriculum_keys.week'}></Trans>
                    </th>
                  )}
                </tr>
              </thead>
              <tbody>
                {sessionFlow.get('session_flow_data', List()).map((s, i) => (
                  <tr key={s.get('_id')} className="tr-change">
                    <td>
                      <div className="w-100px">{i + 1}</div>
                    </td>
                    <td>
                      <div className="w-150px">{s.get('delivery_type', '')}</div>
                    </td>
                    <td>
                      <div className="w-100px">
                        {indVerRename(s.get('delivery_symbol', ''), course.get('_program_id', ''))}
                      </div>
                    </td>
                    <td>
                      <div className="w-100px">{s.get('delivery_no', '')}</div>
                    </td>
                    <td>
                      <div className="w-150px">{s.get('delivery_topic', '')}</div>
                    </td>
                    <td>
                      <div className="w-150px">
                        {s
                          .get('subjects', List())
                          .map((s) => s.get('subject_name', ''))
                          .join(', ')}
                      </div>
                    </td>
                    <td>
                      <div className="w-100px">{s.get('duration', '')}</div>
                    </td>
                    {!isShared && (
                      <td>
                        <div className="w-150px">{getWeekNo(s) || 'NA'}</div>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}

AssignedSessionFlowCard.propTypes = {
  course: PropTypes.instanceOf(Map),
  editedCourse: PropTypes.instanceOf(Map),
  sessionFlow: PropTypes.instanceOf(Map),
  editedSessionFlow: PropTypes.instanceOf(List),
  setData: PropTypes.func,
  levelName: PropTypes.string,
  isShared: PropTypes.bool,
};

export default AssignedSessionFlowCard;
