import React, { forwardRef, useDeferredValue, useEffect, useMemo, useRef, useState } from 'react';
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab';
import { tabsClasses } from '@mui/material/Tabs';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SchoolRoundedIcon from '@mui/icons-material/SchoolRounded';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import CommentIcon from '@mui/icons-material/Comment';
import QuestionMarkRoundedIcon from '@mui/icons-material/QuestionMarkRounded';
import PriorityHighRoundedIcon from '@mui/icons-material/PriorityHigh';
import { Unstable_Popup as BasePopup } from '@mui/base/Unstable_Popup';
// import CircleIcon from '@mui/icons-material/Circle';
import { fromJS, List, Map } from 'immutable';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Avatar,
  // Badge,
  Box,
  Divider,
  InputAdornment,
  OutlinedInput,
  Paper,
  // Popover,
  Tab,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import {
  useBooleanHook,
  useCallApiHook,
  useDebounce,
  useInputHook,
  useRouteing,
  useSearchParams,
  useSetInitialState,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import PaginationQ360 from '../QualityAssuranceProcess/FormList/PaginationQ360';
import {
  ApprovalMultiSelect,
  BadgeText,
  convertNumberToText,
  DisplayButtons,
  DrawerRight,
  MessagePopup,
} from './approvalUtils';
import { useSelector } from 'react-redux';
import {
  selectAcademicYear,
  selectApprovalCategoryList,
  selectApproverPending,
  selectFromApplicationList,
  selectLevelRoleUserList,
  selectRpActions,
} from '_reduxapi/q360/selectors';
import {
  getAcademicYears,
  getApprovalCategoryList,
  getApproverList,
  getFromApplicationList,
  getLevelRoleUserList,
  getRpAction,
  setData,
  updateApproverUser,
} from '_reduxapi/q360/actions';
import { selectSelectedRole } from '_reduxapi/Common/Selectors';
import moment from 'moment';
import { generateParams } from 'Modules/GlobalConfigurationV2/utils/jsUtils';
import { SideBar } from './approvalUtils';

const statusText = {
  pending: 'text-warning',
  Forwarded: 'text-success',
  published: 'text-success',
  Skipped: 'text-secondary',
  Rejected: 'text-danger',
  Delayed: 'text-secondary',
  Approved: 'text-success',
  'Not Applicable': 'text-secondary',
  'Not Initiated': 'text-secondary',
  re_submit: 'text-resubmit',
};

/*-----------------------------------------UtilsSxStart--------------------------------------*/
const selectInputSx = (maxWidth) => {
  return {
    '& .MuiInputBase-input': {
      fontSize: 12,
      background: '#ffffff',
      border: '1.5px solid #D1D5DB',
    },
    '& .MuiInputBase-input::placeholder': {
      fontWeight: 400,
      color: '#9CA3AF',
      opacity: 1,
    },
    '& .MuiSelect-select': {
      color: '#4B5563',
      fontWeight: 500,
      padding: '10px 13px',
      minWidth: maxWidth,
    },
    '& .MuiOutlinedInput-input': {
      paddingRight: '45px !important',
    },
    '& .MuiSvgIcon-root': {
      marginRight: '7px ',
    },
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
  };
};

export const LevelSx = {
  '& .MuiTab-root': {
    margin: '0px 40px 0px 0px',
    padding: '0px',
    minWidth: '0px',
  },
  border: 'none !important',
};

export const tabBorderNone = {
  borderBottom: '1px solid #D1D5DB !important',
  '& .MuiTabs-indicator': {
    backgroundColor: '#2A7AFC',
  },
  '& .MuiTabs-root': {
    margin: 0,
    padding: 0,
  },
  '& .MuiTabs-scroller': {
    height: '40px',
  },
  minHeight: 0,
};

export const PendingSx = {
  '& .MuiTab-root': {
    margin: '0px 40px 0px 0px',
    padding: '0px 7px',
    minWidth: '0px',
  },
};

const CategoryTabSx = {
  '& .MuiTab-root': {
    margin: '0px 40px 0px 0px',
    padding: '0px 0px',
    minWidth: '0px',
  },
};
const tabListSX = {
  [`& .${tabsClasses.scrollButtons}`]: {
    '& .MuiSvgIcon-root': {
      marginRight: 0,
    },
    display: {
      xs: 'flex',
      sm: 'flex',
      md: 'none',
    },
    marginTop: '7px',
    '&.Mui-disabled': { opacity: 0.3 },
  },
};

const style = {
  minWidth: 0,
  minHeight: 10,
};

const tabPadding = {
  '&.MuiTabPanel-root': {
    padding: '0px 0px',
  },
};

const accordionSx = (size = 0) => {
  return {
    style: {
      '&.MuiAccordion-root': {
        marginBottom: `${size}px !important`,
      },
    },
  };
};

const accordionSummarySx = {
  flexDirection: 'row-reverse',
  '& .MuiAccordionSummary-expandIconWrapper': {
    position: 'relative',
    top: '-18px',
    right: '5px',
  },
};

const SearchInputSx = {
  width: '295px',
  height: '43px',
  fontSize: '12px',
  border: '1.5px solid #D1D5DB',
  '& .MuiInputBase-input::placeholder': {
    fontWeight: 400,
    color: '#9CA3AF',
    opacity: 1,
  },
  '& .MuiInputBase-input': {
    fontWeight: 500,
    color: '#4B5563',
    fontSize: 12,
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'none',
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    border: 'none',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    border: 'none',
  },
};

const SearchIconSx = { color: '#9CA3AF', fontSize: '16px' };

// const reSubmitSx = { backgroundColor: '#E0E7FF', color: '#4F46E5', width: '200px' };

const disableTransition = { textTransform: 'capitalize' };

// const badgeCommentSx = {
//   '& .MuiBadge-badge': {
//     backgroundColor: '#374151',
//     width: '15px',
//     height: '15px',
//     fontSize: '7px',
//     minWidth: 0,
//   },
// };

/*-----------------------------------------UtilsSxEnd-----------------------------------------*/

/*-----------------------------------------UtilsCompoundStart-------------------------------------*/
// const logSvg = (
//   <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
//     <path
//       d="M7.21467 0C7.30856 0.0733496 7.41125 0.137897 7.49633 0.220049C8.19169 0.909535 8.88117 1.60489 9.57653 2.29731C9.69095 2.41174 9.74963 2.5379 9.7467 2.7022C9.74083 3.17164 9.74377 3.63814 9.74377 4.11638C9.49438 4.11638 9.25086 4.11638 8.9956 4.11638C8.9956 3.74963 8.9956 3.37995 8.9956 3.00147C8.93692 3.00147 8.89291 3.00147 8.84597 3.00147C8.41174 3.00147 7.98044 3.00147 7.54621 3.00147C7.0621 2.99853 6.74817 2.6846 6.74523 2.20342C6.7423 1.76626 6.74523 1.3291 6.74523 0.891932C6.74523 0.850856 6.74523 0.806846 6.74523 0.754034C6.69242 0.754034 6.65134 0.754034 6.61027 0.754034C4.81174 0.754034 3.01027 0.754034 1.21174 0.754034C0.883129 0.754034 0.748166 0.888998 0.748166 1.21467C0.748166 4.40685 0.748166 7.60196 0.748166 10.7941C0.748166 11.111 0.886063 11.2489 1.20587 11.2489C2.00685 11.2489 2.80782 11.2489 3.60587 11.2489C3.64694 11.2489 3.69095 11.2489 3.74083 11.2489C3.74083 11.5012 3.74083 11.7447 3.74083 12C3.70269 12 3.66748 12 3.63521 12C2.81663 12 1.99511 12 1.17653 12C0.786308 12 0.463569 11.8562 0.222983 11.5423C0.0557455 11.3222 0 11.067 0 10.7941C0 8.72861 0 6.66308 0 4.60049C0 3.46504 0 2.32958 0 1.1912C0 0.704156 0.208313 0.334474 0.651345 0.117359C0.759902 0.0645477 0.886063 0.0440098 1.00636 0.00586799C3.07775 2.22971e-08 5.14621 0 7.21467 0Z"
//       fill="#147AFC"
//     />
//     <path
//       d="M11.994 9.113C11.9295 9.34479 11.818 9.5531 11.5892 9.65286C11.4747 9.70274 11.3427 9.73794 11.2195 9.74088C10.7354 9.74968 10.2513 9.74968 9.77008 9.74381C9.32118 9.74088 9.00138 9.42401 8.99844 8.97511C8.99257 8.23868 8.99257 7.50225 8.99844 6.76288C9.00138 6.31398 9.32118 6.00005 9.77008 5.99418C10.2454 5.99124 10.7207 5.99124 11.196 5.99418C11.6009 5.99418 11.862 6.19369 11.9764 6.58391C11.9794 6.59858 11.9882 6.61325 11.997 6.62498C11.997 6.66313 11.997 6.7042 11.997 6.74234C11.2488 6.74234 10.5036 6.74234 9.75834 6.74234C9.75834 7.50225 9.75834 8.24454 9.75834 8.99271C10.0136 8.99271 10.2601 8.99271 10.5036 8.99271C10.7471 8.99271 10.9936 8.99271 11.24 8.99271C11.24 8.73746 11.24 8.49393 11.24 8.23868C10.9906 8.23868 10.7471 8.23868 10.5036 8.23868C10.5036 7.98635 10.5036 7.74577 10.5036 7.50225C10.7882 7.50225 11.0698 7.49638 11.3486 7.50518C11.4307 7.50811 11.5158 7.54626 11.5921 7.58146C11.8209 7.69002 11.9324 7.89247 11.997 8.12425C11.994 8.45579 11.994 8.7844 11.994 9.113Z"
//       fill="#147AFC"
//     />
//     <path
//       d="M8.63463 7.95992C8.61996 8.12423 8.6229 8.37362 8.57009 8.60833C8.41165 9.30662 7.76031 9.77606 7.03854 9.74085C6.33732 9.70564 5.73292 9.16873 5.64784 8.46457C5.59502 8.02741 5.57449 7.58144 5.67131 7.14721C5.82974 6.43719 6.47229 5.96481 7.20285 5.99709C7.90994 6.02936 8.5026 6.56628 8.60236 7.27924C8.62583 7.47875 8.61996 7.67826 8.63463 7.95992ZM6.37253 7.8719C6.37253 7.99807 6.3696 8.12129 6.37253 8.24745C6.3872 8.66995 6.707 8.99269 7.11483 8.99562C7.52559 8.99856 7.85713 8.67582 7.86886 8.24745C7.87473 7.99807 7.87473 7.74868 7.86886 7.49929C7.85713 7.07679 7.53732 6.75406 7.12656 6.75112C6.71581 6.74819 6.3872 7.07093 6.37253 7.49929C6.3696 7.62252 6.37253 7.74574 6.37253 7.8719Z"
//       fill="#147AFC"
//     />
//     <path
//       d="M3.74973 6.00585C3.74973 7.0034 3.74973 7.99509 3.74973 8.99264C4.2485 8.99264 4.73848 8.99264 5.23726 8.99264C5.23726 9.24496 5.23726 9.48849 5.23726 9.74081C5.20498 9.74374 5.17271 9.74668 5.1375 9.74668C4.56831 9.74668 3.99911 9.74668 3.42992 9.74668C3.14239 9.74668 2.99862 9.60291 2.99862 9.31538C2.99862 8.24741 2.99862 7.17651 2.99862 6.10853C2.99862 6.07919 2.99862 6.04692 2.99862 6.00878C3.24801 6.00585 3.49153 6.00585 3.74973 6.00585Z"
//       fill="#147AFC"
//     />
//   </svg>
// );

const pendingStatusFromLevel = {
  reject: <CloseRoundedIcon className="text-danger f-14" />,
  forward: <CheckRoundedIcon className="text-success f-14" />,
  skipped: <CheckRoundedIcon className="text-success f-14" />,
  pending: <QuestionMarkRoundedIcon className="f-12" style={{ color: '#B45309' }} />,
  re_submit: <PriorityHighRoundedIcon className="f-12" style={{ color: '#5455E6' }} />,
};
/*-----------------------------------------UtilsCompoundEnd---------------------------------------*/

/*-----------------------------------------CustomHooksStart---------------------------------------*/
export const useSelectedRedux = () => {
  const approvalCategory = useSelector(selectApprovalCategoryList);
  const approvalList = useSelector(selectApproverPending);
  const formApplication = useSelector(selectFromApplicationList);
  const levelRoleUserList = useSelector(selectLevelRoleUserList);
  const actions = useSelector(selectRpActions);
  const selectedRole = useSelector(selectSelectedRole);
  const academicYear = useSelector(selectAcademicYear);
  const authDataArray = useSelector((state) => state?.auth);
  return [
    approvalCategory,
    approvalList,
    formApplication,
    levelRoleUserList,
    authDataArray,
    selectedRole,
    actions,
    academicYear,
  ];
};
const useTabState = (defaultValue) => {
  const [tab, setTab] = useState(defaultValue);
  const updateTab = (_, value) => setTab(value);
  return [tab, updateTab];
};

const useTabsContext = (value, ref = null) => {
  const [parentTab, setParentTab] = useState(value);
  const handleChangeParentTab = (_, value) => {
    setParentTab(value);
    ref.current = value;
  };
  return [parentTab, handleChangeParentTab];
};

export const useChangeableEmpty = (value, action, isValid, params = {}) => {
  const [callApi] = useCallApiHook(action);
  useEffect(() => {
    if (isValid) callApi(params);
  }, [value]);
};

const useHeaderState = () => {
  const [searchParams] = useSearchParams();
  const defaultValue = searchParams.get('academicYear') || '';
  const [, , , , , , , academicYears] = useSelectedRedux();
  const [academicYear, setAcademicYear] = useState(defaultValue);
  const [programCourse, setProgramCourse] = useState(List());
  const [filterStatus, setFilterStatus] = useState('');
  const [search, handleSearch] = useInputHook('', true);
  const differedSearchValue = useDeferredValue(search);
  const handleChangeAcademic = (event) => {
    const value = event.target.value;
    setAcademicYear(value);
  };
  const handleChangeFilter = (event) => {
    const value = event.target.value;
    setFilterStatus(value);
  };
  const handleChangeProgramCourse = (event) => {
    const value = event.target.value;
    setProgramCourse(value.toLowerCase());
  };
  useEffect(() => {
    if (!academicYear) setAcademicYear(academicYears.getIn([0, '_id'], ''));
  }, [academicYears]);
  return {
    academicYear,
    programCourse,
    filterStatus,
    search,
    differedSearchValue,
    handleChangeAcademic,
    handleChangeFilter,
    handleChangeProgramCourse,
    handleSearch,
  };
};

const useConstructPending = ({
  data,
  differedSearchValue,
  filterStatus,
  programCourse,
  academicYear,
  categoryTab,
}) => {
  const [formApplication] = useCallApiHook(getFromApplicationList);
  let formInitiatorIdValue = Map();
  let formInitiatorIds = List();
  data
    .filter((pending) => pending.get('currentUserStatus', '').includes(filterStatus))
    .forEach((pending) => {
      const formInitiatorId = pending.get('formInitiatorId', '');
      formInitiatorIdValue = formInitiatorIdValue.set(formInitiatorId, pending);
      formInitiatorIds = formInitiatorIds.push(formInitiatorId);
    });
  const dependency = [programCourse, academicYear, categoryTab, data, filterStatus];
  const params = { formInitiatorIds: formInitiatorIds.toJS(), categoryTab };
  useDebounce(getFromApplicationList, differedSearchValue, differedSearchValue, params);
  useEffect(() => {
    if (formInitiatorIds.size && !differedSearchValue) formApplication(params);
  }, dependency);
  return [formInitiatorIds, formInitiatorIdValue];
};

const usePaginationHook = (selectedFormIds, pendingData, parentTab, filterStatus) => {
  const [pagination, setPagination] = useState(
    fromJS({
      currentPage: 1,
      perPageLimit: 5,
      disableButton: { previous: true, next: true },
    })
  );
  const [formIds, setFormIds] = useState(List());
  const currentPage = pagination.get('currentPage', 0);
  const perPageLimit = pagination.get('perPageLimit', 0);

  const paginationCounts = (condition) => {
    const start = Math.abs(condition * perPageLimit - perPageLimit);
    const end = condition * perPageLimit;
    return selectedFormIds.slice(start, end);
  };
  const updateState = (value) => setFormIds(value);
  const switchPagination = (type) => {
    if (type === 'next') {
      setPagination((previous) => previous.set('currentPage', previous.get('currentPage', 0) + 1));
      updateState(paginationCounts(currentPage + 1));
    } else {
      setPagination((previous) => previous.set('currentPage', previous.get('currentPage', 0) - 1));
      updateState(paginationCounts(currentPage - 1));
    }
  };
  const skipPagination = (type) => {
    if (type === 'firstPage') {
      updateState(selectedFormIds.slice(0, perPageLimit));
      setPagination((previous) => previous.set('currentPage', 1));
    } else {
      updateState(selectedFormIds.slice(selectedFormIds.size - perPageLimit));
      setPagination((previous) =>
        previous.set('currentPage', Math.ceil(selectedFormIds.size / perPageLimit))
      );
    }
  };
  const handleChangePagination = (e) => {
    setPagination((previous) => previous.set('perPageLimit', e.target.value));
  };
  useEffect(() => {
    const previousStatus = currentPage === 1;
    const nextStatus = selectedFormIds.size > currentPage * perPageLimit;
    setPagination((previous) =>
      previous
        .setIn(['disableButton', 'previous'], previousStatus)
        .setIn(['disableButton', 'next'], !nextStatus)
    );
    if (formIds.size === 0) setFormIds(selectedFormIds.slice(0, perPageLimit));
  }, [pagination, pendingData, parentTab]);
  useEffect(() => {
    setFormIds(selectedFormIds.slice(0, perPageLimit));
  }, [perPageLimit, pendingData, parentTab, filterStatus]);
  return [pagination, formIds, switchPagination, skipPagination, handleChangePagination];
};

export const useRestrictedAction = () => {
  const [, , , , , , actions] = useSelectedRedux();
  const memorization = (actions, actionIds) => {
    return useMemo(
      () =>
        actions
          .filter((action) => actionIds.includes(action.get('_id')))
          .map((action) => action.get('actionName', '')),
      [actions, actionIds]
    );
  };
  const Permission = ({ accessKey, actionIds, children }) => {
    const allActions = useMemo(() => actions, [actions]);
    const filteredActions = memorization(allActions, actionIds);
    return filteredActions.includes(accessKey) ? children : null;
  };
  return [Permission];
};

export const userActionsFunc = ({
  academicYear,
  categoryTab,
  formInitiatorId,
  formApplication,
  levelRoleUserList,
  updateRedux,
  updateApprover,
  approvalList,
  formInitiatorIdValue,
  parentTab,
  approverList,
  setExpanded = () => {},
  actionIds = List(),
  actions = List(),
  history,
}) => {
  const formActionsDetails = formInitiatorIdValue.get(formInitiatorId, Map());
  const formNamesDetails = formApplication.get(`${categoryTab}+${formInitiatorId}`, Map());
  const categoryFormId = formNamesDetails.getIn(['categoryFormId', '_id'], '');
  const categoryFormCourseId = formNamesDetails.get('categoryFormCourseId', '');
  const categoryFormGroupId = formNamesDetails.get('categoryFormGroupId', '');
  const allowToSkipping = formActionsDetails.get('allowToSkipping', false);
  const isEditStatus = formActionsDetails.get('isEditStatus', false);
  const currentUserStatus = formActionsDetails.get('currentUserStatus', '');
  const isLastUser = formActionsDetails.get('isLastUser', false);
  const userLevel = formActionsDetails.get('userLevel', false);
  const filterActions = actions
    .filter((action) => actionIds.includes(action.get('_id', '')))
    .map((action) => action.get('actionName', ''));
  const isPublished = ['Publish'].every((action) => filterActions.includes(action));

  const updatecallBack = (cb, reDireact) => (status) => {
    if (reDireact) history.goBack();
    cb & cb();
    const params = { institutionCalenderId: academicYear, categoryId: categoryTab };
    let selectedForm = approvalList
      .setIn([categoryTab, parentTab, formInitiatorId, 'currentUserStatus'], status)
      .setIn(
        [categoryTab, parentTab, formInitiatorId, 'isEditStatus'],
        !approvalList.getIn([categoryTab, parentTab, formInitiatorId, 'isEditStatus'], false)
      );
    updateRedux({
      approverPending: selectedForm,
      levelRoleUserList: levelRoleUserList.delete(formInitiatorId),
    });
    setExpanded(Map());
    approverList(params);
  };
  const handleUpdate = (value, reDireact) => (e, reason, cb = () => {}) => {
    e.stopPropagation();
    e.preventDefault();
    updateApprover(
      {
        formInitiatorId,
        userLevel,
        status: value,
        categoryFormId,
        categoryFormCourseId,
        categoryFormGroupId,
        institutionCalenderId: academicYear,
        ...(value === 'published' && isPublished && { isPublished: true }),
        ...(allowToSkipping && { isSkipped: allowToSkipping }),
        ...(reason && { reason }),
      },
      updatecallBack(cb, reDireact)
    );
  };
  return [handleUpdate, allowToSkipping, isEditStatus, currentUserStatus, isLastUser, isPublished];
};
/*-----------------------------------------CustomHooksEnd-----------------------------------------*/

const ApprovalCompound = ({
  categoryTab,
  filterStatus,
  handleChangeFilter,
  search,
  differedSearchValue,
  handleSearch,
  academicYear,
}) => {
  const expandedRef = useRef(null);
  const [searchParams] = useSearchParams();
  const defaultValue = searchParams.get('parentTab') || 'pendingWithYou';
  const [parentTab, handleChangeParentTab] = useTabsContext(defaultValue, expandedRef);
  const [history] = useRouteing();
  const [drawer, handleDrawer] = useBooleanHook(false);
  const [, approvalList] = useSelectedRedux();
  const pendingData = approvalList.get(categoryTab, Map());
  const data = pendingData.get(parentTab, List());
  const [formInitiatorIds, formInitiatorIdValue] = useConstructPending({
    data,
    differedSearchValue,
    filterStatus,
    academicYear,
    categoryTab,
  });
  const [
    pagination,
    formIds,
    switchPagination,
    skipPagination,
    handleChangePagination,
  ] = usePaginationHook(formInitiatorIds, pendingData, parentTab, filterStatus);
  const handleOpenDrawer = (e) => {
    e.stopPropagation();
    handleDrawer();
  };
  let approvalStatus = fromJS({});
  data.forEach((status) => {
    const formStatus = status.get('currentUserStatus', '');
    approvalStatus = approvalStatus.set(
      formStatus,
      Map({ label: formStatus.charAt(0).toUpperCase() + formStatus.slice(1), value: formStatus })
    );
  });
  const perPageLimit = pagination.get('perPageLimit', 0);
  const totalNumberOfPages = Math.ceil(formInitiatorIds.size / perPageLimit);
  const countFiltersYou = approvalList
    .getIn([categoryTab, 'pendingWithYou'], List())
    .filter((pending) => pending.get('currentUserStatus', '').includes(filterStatus));
  const countFiltersOthers = approvalList
    .getIn([categoryTab, 'pendingWithOthers'], List())
    .filter((pending) => pending.get('currentUserStatus', '').includes(filterStatus));
  const countFiltersHistory = approvalList
    .getIn([categoryTab, 'history'], List())
    .filter((pending) => pending.get('currentUserStatus', '').includes(filterStatus));
  const pendingWithYouCount = countFiltersYou.size;
  const pendingWithOthersCount = countFiltersOthers.size;
  const historyCount = countFiltersHistory.size;
  useEffect(() => {
    if (academicYear || categoryTab || parentTab) {
      searchParams.set('academicYear', academicYear);
      searchParams.set('categoryTab', categoryTab);
      searchParams.set('parentTab', parentTab);
      history.push({ search: searchParams.toString() });
    }
  }, [academicYear, categoryTab, parentTab]);
  return (
    <div className=" d-flex flex-column bg-white br-8 justify-content-between w-100 p-3 my-3 rounded ">
      <div>
        <div className="d-flex justify-content-between ">
          <div className="fw-600 text-black f-20">
            Course Application (
            {`${String(pendingWithYouCount + pendingWithOthersCount + historyCount).padStart(
              approvalList.size === 0 ? 1 : 2,
              '0'
            )}`}
            )
          </div>
          <div className="d-flex gap-10">
            <OutlinedInput
              id="outlined-adornment-weight"
              placeholder={'Search Program, Course Name...'}
              value={search}
              onChange={handleSearch}
              startAdornment={
                <InputAdornment position="start">
                  <SearchIcon sx={SearchIconSx} />
                </InputAdornment>
              }
              aria-describedby="outlined-weight-helper-text"
              inputProps={{
                'aria-label': 'weight',
                autoComplete: 'off',
              }}
              sx={SearchInputSx}
            />
            {/* <ApprovalMultiSelect
              // value={programCourse}
              // onChange={handleChangeProgramCourse}
              // options={academicYearOptions}
              labelclass={'mb-1 f-15'}
              placeholder="Select Program & Course"
              sx={selectInputSx('150px')}
              multiSelect={true}
            /> */}
            <ApprovalMultiSelect
              value={filterStatus}
              onChange={handleChangeFilter}
              options={approvalStatus}
              labelclass={'mb-1 f-15'}
              placeholder="Filter"
              clearAll={false}
              sx={selectInputSx('80px')}
              StartIcon={FilterListIcon}
            />
          </div>
        </div>
        <TabContext value={parentTab}>
          <Box>
            <TabList
              aria-label="lab API tabs example"
              sx={{ ...tabBorderNone, ...PendingSx }}
              onChange={handleChangeParentTab}
            >
              <Tab
                label={`Pending With Me (${String(pendingWithYouCount).padStart(
                  pendingWithYouCount === 0 ? 1 : 2,
                  '0'
                )})`}
                value="pendingWithYou"
                sx={disableTransition}
                disableRipple
              />
              <Tab
                label={`Pending With Others (${String(pendingWithOthersCount).padStart(
                  pendingWithOthersCount === 0 ? 1 : 2,
                  '0'
                )})`}
                value="pendingWithOthers"
                sx={disableTransition}
                disableRipple
              />
              <Tab
                label={`History (${String(historyCount).padStart(
                  historyCount === 0 ? 1 : 2,
                  '0'
                )})`}
                value="history"
                sx={disableTransition}
                disableRipple
              />
            </TabList>
          </Box>
          <TabCompound
            ref={expandedRef}
            value="pendingWithYou"
            handleOpenDrawer={handleOpenDrawer}
            data={{ formInitiatorIdValue, formIds, categoryTab, parentTab, academicYear }}
          />
          <TabCompound
            ref={expandedRef}
            value="pendingWithOthers"
            handleOpenDrawer={handleOpenDrawer}
            data={{ formInitiatorIdValue, formIds, categoryTab, parentTab, academicYear }}
          />
          <TabCompound
            ref={expandedRef}
            value="history"
            handleOpenDrawer={handleOpenDrawer}
            data={{
              formInitiatorIdValue,
              formIds,
              categoryTab,
              parentTab,
              academicYear,
              isHistory: true,
            }}
          />
        </TabContext>
      </div>
      <PaginationQ360
        pagination={pagination}
        switchPagination={switchPagination}
        skipPagination={skipPagination}
        handleChangePagination={handleChangePagination}
        count={totalNumberOfPages}
      />
      <DrawerRight open={drawer} handleClose={handleDrawer} />
    </div>
  );
};
const HeaderFilter = ({ academicYear, handleChangeAcademic }) => {
  const [, , , , , , , academicYears] = useSelectedRedux();
  let academicYearOptions = Map();
  academicYears.forEach((academic) => {
    const _id = academic.get('_id', '');
    const calendar_name = academic.get('calendar_name', '');
    academicYearOptions = academicYearOptions.set(_id, Map({ label: calendar_name, value: _id }));
  });
  return (
    <div className="d-flex align-items-center">
      <div className="f-22 fw-400 text-mGrey flex-grow-1 d-flex align-items-center gap-5">
        Quality Assurance Approver
      </div>
      <div className="d-flex gap-15">
        <ApprovalMultiSelect
          value={academicYear}
          onChange={handleChangeAcademic}
          options={academicYearOptions}
          labelclass={'mb-1 f-15'}
          placeholder="Select Academic Calender"
          sx={selectInputSx('150px')}
        />
      </div>
    </div>
  );
};
const HeaderTab = ({ academicYear }) => {
  const [categoryList, , , , , selectedRole] = useSelectedRedux();
  const [searchParams] = useSearchParams();
  const [approverList] = useCallApiHook(getApproverList);
  const [getApi] = useCallApiHook(getApprovalCategoryList);
  const {
    filterStatus,
    handleChangeFilter,
    search,
    handleSearch,
    differedSearchValue,
  } = useHeaderState();
  const firstIndexValue = categoryList.size ? categoryList.getIn([0, '_id'], '') : '';
  const defaultValue = searchParams.get('categoryTab') || '';
  const [tab, updateTab] = useTabState(defaultValue);
  const setFunction = () => updateTab('', firstIndexValue);
  const params = {
    institutionCalenderId: academicYear,
    categoryId: tab,
    ...(search && { searchKey: search }),
  };
  const payload = { institutionCalenderId: academicYear, subModuleType: 'Form Approver' };
  useEffect(() => {
    if (academicYear) getApi(payload);
  }, [selectedRole, academicYear]);
  useSetInitialState(firstIndexValue, setFunction, categoryList, tab);
  useChangeableEmpty('', getRpAction, true);
  useChangeableEmpty('', getAcademicYears, true);
  useDebounce(getApproverList, differedSearchValue, differedSearchValue, params);
  useEffect(() => {
    if (academicYear && tab && !differedSearchValue) approverList(params);
  }, [academicYear, tab, differedSearchValue, categoryList, selectedRole]);
  return (
    <TabContext value={tab}>
      <Box>
        <TabList
          aria-label="lab API tabs example"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            ...tabBorderNone,
            ...CategoryTabSx,
            ...tabListSX,
          }}
          style={style}
          onChange={updateTab}
        >
          {categoryList.map((category) => {
            const id = category.get('_id', '');
            const label = category.get('categoryName', '');
            return <Tab key={id} label={label} value={id} sx={disableTransition} disableRipple />;
          })}
        </TabList>
      </Box>
      <TabPanel value={tab} sx={tabPadding} className="d-flex gap-10">
        <SideBar tab={tab} selectedCalendar={academicYear} />
        <ApprovalCompound
          categoryTab={tab}
          academicYear={academicYear}
          filterStatus={filterStatus}
          search={search}
          differedSearchValue={differedSearchValue}
          handleChangeFilter={handleChangeFilter}
          handleSearch={handleSearch}
        />
      </TabPanel>
    </TabContext>
  );
};
const ApproverIndexPage = () => {
  const { academicYear, handleChangeAcademic } = useHeaderState();
  return (
    <div className="py-3 px-4">
      <div className="d-flex flex-column gap-5">
        <HeaderFilter academicYear={academicYear} handleChangeAcademic={handleChangeAcademic} />
        <HeaderTab academicYear={academicYear} />
      </div>
    </div>
  );
};
export default ApproverIndexPage;
const TabCompound = forwardRef(({ value, handleOpenDrawer, data }, ref) => {
  // const [paper, setPaper] = useState(null);
  const {
    formInitiatorIdValue,
    formIds,
    categoryTab,
    parentTab,
    academicYear,
    isHistory = false,
  } = data;
  const [
    ,
    approvalList,
    formApplication,
    levelRoleUserList,
    authDataArray,
    selectedRole,
    actions,
  ] = useSelectedRedux();
  const [updateRedux] = useCallApiHook(setData);
  const [updateApprover] = useCallApiHook(updateApproverUser);
  const [approverList] = useCallApiHook(getApproverList);
  const [getLevelUser] = useCallApiHook(getLevelRoleUserList);
  const [history] = useRouteing();
  const [Permission] = useRestrictedAction();
  const [childTab, setChildTab] = useState(Map().set(0, 0));
  const [expanded, setExpanded] = useState(Map());
  // const open = Boolean(paper);
  // const id = open ? 'simple-popover-in' : undefined;
  // const handleClose = (e) => {
  //   e.stopPropagation();
  //   setPaper(null);
  // };
  // const handleClick = (event) => {
  //   event.stopPropagation();
  //   setPaper(event.currentTarget);
  // };
  const handleChangeChildTab = (_, value) => {
    const [parentIndex, childIndex] = value.split('+');
    setChildTab((prev) => prev.set(Number(parentIndex), Number(childIndex)));
  };
  const stopPropagation = (e, func) => {
    e.stopPropagation();
    func();
  };
  useEffect(() => {
    if (ref.current) setExpanded(Map());
  }, [ref.current]);
  return (
    <>
      <TabPanel value={value} sx={{ padding: '20px 0px !important' }}>
        {formIds.size === 0 && <div className="text-center fw-400 text-gray">No Data!</div>}
        <div className="custom-scrollbar approver-list">
          {formIds.map((formInitiatorId, index) => {
            const formActionsDetails = formInitiatorIdValue.get(formInitiatorId, Map());
            const formNamesDetails = formApplication.get(
              `${categoryTab}+${formInitiatorId}`,
              Map()
            );
            const actionIds = formActionsDetails.get('actionIds', List());
            const programName = formNamesDetails.get(
              'programName',
              formNamesDetails.get('institutionName', '')
            );
            const courseName = formNamesDetails.get('courseName', '');
            const curriculumName = formNamesDetails.get('curriculumName', '');
            const selectedGroupName = formNamesDetails.get('selectedGroupName', List());
            const submissionDate = formNamesDetails.get('submissionDate', '');
            const categoryFormCourseId = formNamesDetails.get('categoryFormCourseId', '');
            const categoryFormGroupId = formNamesDetails.get('categoryFormGroupId', '');
            const approverDetails = formNamesDetails.get('approverDetails', Map());
            const currentLevel = approverDetails.get('currentLevel', 0);
            const userLevel = formActionsDetails.get('userLevel', 0);
            const levelStatus = approverDetails.get('levelStatus', '');
            const historyStatus = formActionsDetails.get('historyStatus', '');
            const currentUserStatus = isHistory
              ? historyStatus
              : formActionsDetails.get('currentUserStatus', '');
            const allowToSkipping = formActionsDetails.get('allowToSkipping', false);
            const isLastUser = formActionsDetails.get('isLastUser', false);
            const isEditStatus = formActionsDetails.get('isEditStatus', false);
            const isExpanded = expanded.get(index, false);
            const displaySection = formNamesDetails.get('displaySection', List());
            const displayName = formNamesDetails.get('displayName', List());
            const resubmissionLog = formNamesDetails.get('resubmissionLog', List());
            const addComment = formNamesDetails.get('addComment', List());
            const incorporateFrom = formNamesDetails.get('incorporateFrom', List());
            const incorporateWith = formNamesDetails.get('incorporateWith', List());
            const categoryFormDetails = formNamesDetails.get('categoryFormId', Map());
            const formName = formNamesDetails.get('formName', '');
            const categoryFormId = categoryFormDetails.get('_id', '');
            const categoryFormType = categoryFormDetails.get('categoryFormType', Map());
            const level = formNamesDetails.get('level', '');
            const formType = categoryFormDetails.get('formType', Map());
            const [handleUpdate, , , , , isPublished] = userActionsFunc({
              academicYear,
              categoryTab,
              formInitiatorId,
              formApplication,
              levelRoleUserList,
              authDataArray,
              selectedRole,
              updateRedux,
              updateApprover,
              approvalList,
              formInitiatorIdValue,
              parentTab,
              approverList,
              setExpanded,
              actionIds,
              actions,
              history,
            });
            const formDetails = { programName, courseName };
            const handleAccordion = (index) => {
              const payload = {
                categoryFormCourseId,
                categoryFormGroupId,
                formInitiatorId,
                categoryFormId,
                institutionCalenderId: academicYear,
              };
              const callBack = () => {
                setExpanded((prev) => prev.update(index, false, (value) => !value));
              };
              if (
                isExpanded === false &&
                levelRoleUserList.get(formInitiatorId, List()).size === 0
              ) {
                getLevelUser(payload, callBack);
              } else callBack();
            };
            const queryArr = [
              { formInitiatorId },
              { categoryId: categoryTab },
              { tab: parentTab },
              { calenderId: academicYear },
              { formType },
              { categoryFormType },
              { level },
              { formName },
              { currentLevel: userLevel },
            ];
            const quearyParams = generateParams(queryArr);
            const routeNextPage = () => {
              history.push(`/qapc/qualityAssuranceApprovalFlow/particularApproval?${quearyParams}`);
            };
            const basicActions = ['Edit', 'View'];
            const filterActions = actions
              .filter((action) => actionIds.includes(action.get('_id', '')))
              .map((action) => action.get('actionName', ''));
            const isEditView = basicActions.every((action) => filterActions.includes(action));
            const splitName = programName.split(' ');
            const splitNameLength = splitName.length;
            const headerName = {
              program: programName,
              course: courseName,
              institution: programName,
            };
            const tagName = `${headerName[level]?.substring(0, splitNameLength > 1 ? 1 : 2)}${
              splitNameLength > 1 ? splitName[1].substring(0, 1) : ''
            }`;
            const isPublishApproval = ['Approved', 'published'].includes(historyStatus);
            return (
              <Accordion
                elevation={0}
                className="box-shadow"
                sx={accordionSx(isExpanded ? 10 : 0).style}
                key={index}
                expanded={isExpanded}
                onChange={() => handleAccordion(index)}
              >
                <AccordionSummary
                  className="border-bottom"
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1-content"
                  id="panel1-header"
                  sx={accordionSummarySx}
                >
                  <div className="d-flex w-100  justify-content-between">
                    <div className="d-flex gap-10">
                      <div className="align-self-start text-dark-blue digi-Shared-bg rounded py-1 px-2 box-shadow-static text-uppercase">
                        {tagName}
                      </div>
                      <div className="d-flex flex-column gap-5">
                        <div className="fw-500 text-primary cursor-pointer text-capitalize">
                          <span onClick={(e) => stopPropagation(e, routeNextPage)}>
                            {headerName[level]} / {formName}
                          </span>
                          {/* <span
                            className="rounded f-12 px-1 mx-2"
                            style={{
                              border: '2px solid #FF7F00',
                              backgroundColor: '#FFFBEB',
                              color: '#FF7F00',
                              padding: '2px 4px',
                            }}
                            onClick={handleOpenDrawer}
                          >
                            Re - Submitted {logSvg}
                          </span> */}
                        </div>
                        <div className="f-12 text-lGrey d-flex align-items-center">
                          <SchoolRoundedIcon className="mr-1 p-0 f-12" />
                          {curriculumName ? `${curriculumName} /` : ''} {programName}
                          {selectedGroupName.size
                            ? ` / ${selectedGroupName.join(',')} Group`
                            : ''}{' '}
                          <CalendarTodayIcon className="mx-1 p-0 f-12" />
                          {moment(submissionDate).format('DD MMM - YYYY [at] hh:mm A')}
                        </div>
                        <div className="d-flex mt-1">
                          <BadgeText
                            text="display capture "
                            count={displaySection.size ? displaySection.size : displayName.size}
                          />
                          <Permission accessKey="Incorporation" actionIds={actionIds}>
                            <BadgeText text="incorporate with " count={incorporateWith.size} />
                          </Permission>
                          <Permission accessKey="Incorporation" actionIds={actionIds}>
                            <BadgeText count={incorporateFrom.size} text="incorporate from" />
                          </Permission>
                        </div>
                      </div>
                    </div>
                    <div className="d-flex gap-20">
                      <div className="align-self-center">
                        {(resubmissionLog.size !== 0 || addComment.size !== 0) && (
                          <MessagePopup
                            formDetails={formDetails}
                            resubmissionLog={resubmissionLog}
                            addComment={addComment}
                            level={level}
                          />
                          // <Badge
                          //   badgeContent={1}
                          //   className="text-white"
                          //   sx={badgeCommentSx}
                          //   id={id}
                          //   onClick={handleClick}
                          // >
                          //   <CommentIcon className="text-light-blue" />
                          // </Badge>
                        )}
                        {/* <Popover
                          id={id}
                          open={open}
                          anchorEl={paper}
                          onClose={handleClose}
                          onClick={(e) => e.stopPropagation()}
                          anchorOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                          }}
                          transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                          }}
                          PaperProps={{
                            sx: {
                              width: 350,
                            },
                          }}
                        >
                          <div
                            className="f-12 border rounded  gap-5 text-black bg-white px-3 py-2"
                            key={index}
                          >
                            <div className="d-flex justify-content-between align-items-center">
                              <div className="fw-500 text-black">Re-Submit Comments</div>
                              <CloseRoundedIcon
                                className="f-18 text-secondary cursor-pointer mt-1"
                                onClick={handleClose}
                              />
                            </div>
                            <Divider />
                            {resubmissionLog.map((reSubmit, index) => {
                              const firstName = reSubmit.getIn(['userId', 'name', 'first'], '');
                              const lastName = reSubmit.getIn(['userId', 'name', 'last'], '');
                              const reason = reSubmit.get('reason', '');
                              return (
                                <div key={index}>
                                  <div>
                                    <div className="f-14 fw-500 text-black mt-1">
                                      Faculty FeedBack
                                    </div>
                                    <div className="f-12 mb-1">{firstName + ' ' + lastName}</div>
                                  </div>
                                  <div className="f-12">{reason}</div>
                                </div>
                              );
                            })}
                          </div>
                        </Popover> */}
                      </div>
                      <div className="d-flex flex-column gap-10">
                        <div className="rounded py-1 f-10 text-capitalize application-status">
                          <span className="text-black fw-600 text-capitalize pl-2">
                            Application Status :
                          </span>

                          {levelStatus === 're_submit' ? (
                            <span className="mx-1 text-warning">Asked for Re-Submit</span>
                          ) : historyStatus ? (
                            <span
                              className={`${
                                isPublishApproval ? 'text-success' : 'text-danger'
                              } mx-1 fw-500`}
                            >
                              {historyStatus}
                            </span>
                          ) : (
                            <span className="mx-1 text-warning">
                              Pending with Level {currentLevel}
                            </span>
                          )}
                        </div>
                        <div className="w-100">
                          <>
                            {isEditView ? (
                              <Permission accessKey="Edit" actionIds={actionIds}>
                                <DisplayButtons
                                  handleUpdate={handleUpdate}
                                  toSkip={allowToSkipping}
                                  isEditStatus={isEditStatus}
                                  isPublished={isPublished}
                                  isLastUser={isLastUser}
                                  userStatus={currentUserStatus}
                                  buttonDisabled={false}
                                  isHistory={isHistory}
                                />
                              </Permission>
                            ) : (
                              <>
                                <Permission accessKey="Edit" actionIds={actionIds}>
                                  <DisplayButtons
                                    handleUpdate={handleUpdate}
                                    toSkip={allowToSkipping}
                                    isEditStatus={isEditStatus}
                                    isPublished={isPublished}
                                    isLastUser={isLastUser}
                                    userStatus={currentUserStatus}
                                    buttonDisabled={false}
                                    isHistory={isHistory}
                                  />
                                </Permission>
                                <Permission accessKey="View" actionIds={actionIds}>
                                  <DisplayButtons
                                    handleUpdate={handleUpdate}
                                    toSkip={allowToSkipping}
                                    isEditStatus={isEditStatus}
                                    isPublished={isPublished}
                                    isLastUser={isLastUser}
                                    userStatus={currentUserStatus}
                                    buttonDisabled={true}
                                    isHistory={isHistory}
                                  />
                                </Permission>
                              </>
                            )}
                          </>
                        </div>
                      </div>
                    </div>
                  </div>
                </AccordionSummary>
                <AccordionDetails>
                  <ChildTabCompound
                    expanded={isExpanded}
                    parentIndex={index}
                    handleChangeChildTab={handleChangeChildTab}
                    value={`${index}+${childTab.get(index, 0)}`}
                    formInitiatorId={formInitiatorId}
                  />
                </AccordionDetails>
              </Accordion>
            );
          })}
        </div>
      </TabPanel>
    </>
  );
});

const ChildTabCompound = ({
  parentIndex,
  handleChangeChildTab,
  value,
  formInitiatorId,
  expanded,
}) => {
  const [, childIndex] = value.split('+');
  const [, , , levelRoleUserList, authDataArray, selectedRole] = useSelectedRedux();
  const [anchor, setAnchor] = useState(Map());
  const handleClick = (event, roleIndex, userIndex) => {
    setAnchor((pre) =>
      pre.setIn(
        [roleIndex, userIndex],
        pre.getIn([roleIndex, userIndex], null) ? null : event.currentTarget
      )
    );
  };
  const authData = fromJS(authDataArray);
  const loggedInUserId = authData.getIn(['loggedInUserData', '_id'], '');
  const compare = (a, b) => {
    const previous = a.get('approverLevelIndex', 0);
    const current = b.get('approverLevelIndex', 0);
    if (previous >= current) return 1;
    else return -1;
  };
  const levelRoleUsers = levelRoleUserList.get(formInitiatorId, List());
  const levelData = levelRoleUsers.sort(compare);
  const TATCount = levelData.getIn([childIndex, 'turnAroundTime'], 0);
  const delayedDate = levelData.getIn([childIndex, 'delayedDate'], 0);
  let constructData = Map();
  let statusCount = Map();

  levelData.forEach((level, levelIndex) => {
    level.get('roleIds', List()).forEach((roles) => {
      const qapcRoleId = roles.get('qapcRoleId', '');
      const userIds = roles.get('userIds', List());
      userIds.forEach((users) => {
        const status = users.get('status', '');
        statusCount = statusCount.setIn(
          [levelIndex, qapcRoleId, status],
          statusCount.getIn([levelIndex, qapcRoleId, status], 0) + 1
        );
      });
      const overAll = constructData.getIn([levelIndex, qapcRoleId, 'overAll'], 0);
      constructData = constructData.setIn(
        [levelIndex, qapcRoleId],
        Map({
          overAll: overAll + userIds.size,
          forwarded: statusCount.getIn([levelIndex, qapcRoleId, 'Forwarded'], 0),
          reSubmit: statusCount.getIn([levelIndex, qapcRoleId, 're_submit'], 0),
          pending: statusCount.getIn([levelIndex, qapcRoleId, 'pending'], 0),
          delayed: statusCount.getIn([levelIndex, qapcRoleId, 'Delayed'], 0),
          reject: statusCount.getIn([levelIndex, qapcRoleId, 'Rejected'], 0),
          approved: statusCount.getIn([levelIndex, qapcRoleId, 'Approved'], 0),
          published: statusCount.getIn([levelIndex, qapcRoleId, 'published'], 0),
          skipped: statusCount.getIn([levelIndex, qapcRoleId, 'Skipped'], 0),
          notApplicable: statusCount.getIn([levelIndex, qapcRoleId, 'Not Applicable'], 0),
          notInitiator: statusCount.getIn([levelIndex, qapcRoleId, 'Not Initiated'], 0),
        })
      );
    });
  });

  function levelStatus(levelIndex) {
    const isAllUser = levelData.getIn([levelIndex, 'requireAll'], false);
    const miniumUser = levelData.getIn([levelIndex, 'requireMinium'], false);
    const miniumUserCount = levelData.getIn([levelIndex, 'minimum_user'], 0);
    const getCount = constructData.get(levelIndex, Map());
    if (isAllUser) {
      const isReSubmit = getCount.some((roles) => {
        const reSubmit = roles.get('reSubmit', 0);
        return reSubmit > 0;
      });
      const isForwarded = getCount.every((roles) => {
        const forwarded = roles.get('forwarded', 0);
        const skipped = roles.get('skipped', 0);
        const approved = roles.get('approved', 0);
        const published = roles.get('published', 0);
        const overAll = roles.get('overAll', 0);
        return forwarded + skipped + approved + published === overAll;
      });
      const isPending = getCount.some((roles) => {
        const pending = roles.get('pending', 0);
        const reject = roles.get('reject', 0);
        const notInitiator = roles.get('notInitiator', '');
        const delayed = roles.get('delayed', '');
        return pending + notInitiator + delayed > 0 && reject === 0;
      });
      if (isReSubmit) return 're_submit';
      if (isPending) return 'pending';
      if (isForwarded) return 'forward';
      else return 'reject';
    }
    if (miniumUser) {
      const isReSubmit = getCount.some((roles) => {
        const reSubmit = roles.get('reSubmit', 0);
        return reSubmit > 0;
      });
      const isForwarded = getCount.every((roles) => {
        const forwarded = roles.get('forwarded', 0);
        const skipped = roles.get('skipped', 0);
        const approved = roles.get('approved', 0);
        const published = roles.get('published', 0);
        const overAll = roles.get('overAll', 0);
        return (
          forwarded + skipped + approved + published >= miniumUserCount ||
          forwarded + skipped + approved + published === overAll
        );
      });
      const isPending = getCount.some((roles) => {
        const pending = roles.get('pending', 0);
        const forwarded = roles.get('forwarded', 0);
        const notInitiator = roles.get('notInitiator', '');
        const delayed = roles.get('delayed', '');
        const skipped = roles.get('skipped', 0);
        return (
          forwarded + skipped < miniumUserCount &&
          pending + notInitiator + delayed <= miniumUserCount &&
          pending + notInitiator + delayed > 0
        );
      });
      if (isReSubmit) return 're_submit';
      if (isPending) return 'pending';
      if (isForwarded) return 'forward';
      else return 'reject';
    }
  }
  useEffect(() => {
    setAnchor(Map());
  }, [expanded]);

  return (
    <TabContext value={`${value}`}>
      <Box>
        <TabList
          aria-label="lab API tabs example"
          sx={{ ...tabBorderNone, ...LevelSx }}
          onChange={handleChangeChildTab}
        >
          {levelData.map((level, levelIndex) => (
            <Tab
              label={
                <div className="d-flex justify-content-start align-items-center gap-10 f-12 text-capitalize">
                  level {level.get('approverLevelIndex', 0)}
                  {pendingStatusFromLevel[levelStatus(levelIndex)]}
                </div>
              }
              disableRipple
              value={`${parentIndex}+${levelIndex}`}
              sx={disableTransition}
              key={levelIndex}
            />
          ))}
        </TabList>
        <TabPanel value={`${value}`} sx={{ padding: '10px 0 0 0' }}>
          <Paper className="p-4 box-shadow box-shadow-static">
            <div className="d-flex justify-content-end">
              <div className="f-10">
                TAT -{' '}
                {delayedDate
                  ? `Delayed by ${delayedDate} Day${delayedDate > 1 ? 's' : ''}`
                  : `Approve within ${TATCount} Day${TATCount > 1 ? 's' : ''}`}
              </div>
            </div>
            {levelData.getIn([childIndex, 'roleIds'], Map()).map((roles, roleIndex) => {
              let constructThree = List();
              const chunkSize = 3;
              const qapcRoleName = roles.get('roleName', '');
              const isAllUser = levelData.getIn([childIndex, 'requireAll'], false);
              const minimum_user = levelData.getIn([childIndex, 'minimum_user'], 0);
              const users = roles.get('userIds', List());
              const selectedRoleId = selectedRole.get('_id', '');
              const isSameRole = selectedRoleId === roles.get('qapcRoleId', '');
              const label = isAllUser
                ? 'All Users In This Role'
                : convertNumberToText(minimum_user);
              for (let i = 0; i < users.size; i += chunkSize) {
                constructThree = constructThree.push(users.slice(i, i + chunkSize));
              }
              return (
                <div key={roleIndex}>
                  <div className="d-flex flex-column my-1 mb-4">
                    <div
                      className="f-12 fw-500 text-capitalize mb-1"
                      style={isSameRole ? { fontWeight: 700 } : { fontWeight: 500 }}
                    >
                      {qapcRoleName}
                    </div>
                    <div className="f-12">Approval Required From {label}</div>
                    <div className=" rounded  flex-wrap f-12  approval-role-border  mt-1">
                      <div className="mx-4">
                        {constructThree.map((users, usersIndex) => {
                          const condition = constructThree.size - 1 !== usersIndex;
                          return (
                            <div
                              key={usersIndex}
                              className={`row ${condition && 'border-bottom '}`}
                            >
                              {users.map((user, userIndex) => {
                                const status = user.get('status', '');
                                const firstName = user.getIn(['userName', 'first'], '');
                                const lastName = user.getIn(['userName', 'last'], '');
                                const reason = user.get('reason', '');
                                const userId = user.get('userId', '');
                                const isFontThickness = loggedInUserId === userId && isSameRole;
                                const textAlign = {
                                  0: 'justify-content-start',
                                  1: 'justify-content-center',
                                  2: 'justify-content-end',
                                };
                                const anchors = anchor.getIn([roleIndex, userIndex], false);
                                const open = Boolean(anchors);
                                const id = open ? 'simple-popup' : undefined;
                                return (
                                  <div
                                    key={userIndex}
                                    className={`col-4 mb-2 mt-2  d-flex ${textAlign[userIndex]} ${
                                      userIndex !== 2 && 'approval-role-border-right'
                                    }`}
                                  >
                                    <div className="row w-100 d-flex align-items-center">
                                      <div className="col-2">
                                        <Avatar alt="Sharp" sx={{ width: 24, height: 24 }} />
                                      </div>
                                      <div
                                        className="col-7 user-name text-black text-nowrap"
                                        style={
                                          isFontThickness
                                            ? { fontWeight: 700 }
                                            : { fontWeight: 400 }
                                        }
                                      >
                                        {firstName + ' ' + lastName}
                                      </div>
                                      <div
                                        className={` text-capitalize d-flex gap-20 align-items-center col-3 text-nowrap fw-500 pl-1 ${statusText[status]}`}
                                      >
                                        {status}
                                        <div>
                                          {reason && status === 'Rejected' && (
                                            <CommentIcon
                                              className="f-16 text-danger cursor-pointer mt-1"
                                              onClick={(e) => handleClick(e, roleIndex, userIndex)}
                                              aria-describedby={id}
                                            />
                                          )}
                                        </div>
                                        <BasePopup
                                          id={id}
                                          open={open}
                                          anchor={anchors}
                                          placement="top"
                                        >
                                          <div
                                            className="f-12 border rounded  gap-5 text-black bg-white px-3 py-2"
                                            style={{ width: 200 }}
                                          >
                                            <div className="d-flex justify-content-between align-items-center">
                                              <div className="fw-500 text-black">Comments</div>
                                              <CloseRoundedIcon
                                                className="f-18 text-secondary cursor-pointer mt-1"
                                                onClick={(e) =>
                                                  handleClick(e, roleIndex, userIndex)
                                                }
                                              />
                                            </div>
                                            <Divider />
                                            <div>
                                              <div>
                                                <div className="f-14 fw-500 text-black mt-1">
                                                  Faculty FeedBack
                                                </div>
                                                <div className="f-12 mb-1">
                                                  {firstName + ' ' + lastName}
                                                </div>
                                              </div>
                                              <div className="f-12">{reason}</div>
                                            </div>
                                          </div>
                                        </BasePopup>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </Paper>
        </TabPanel>
      </Box>
    </TabContext>
  );
};
