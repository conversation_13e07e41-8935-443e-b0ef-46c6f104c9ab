import { combineReducers } from 'redux';

import calender from './reducers/calender';
import interimCalendar from './reducers/interimCalendar';
import auth from './reducers/auth';
import department from './reducers/department';
import student from './student/reducer';
import infrastructure from './infrastructure/reducer';
import genericForm from './forms/reducer';
import leaveManagement from './leave_management/reducer';
import roles from './roles/reducer';
import programInput from './program_input/reducer';
import userManagement from './user_management/reducer';
import institution from './institution/reducer';
import courseScheduling from './course_scheduling/reducer';
import reportsAndAnalytics from './reports_and_analytics/reducer';
import dashboard from './dashboard/reducer';
import globalConfiguration from './global_configuration/reducer';
import programInputV2 from './program_input/v2/reducer';
import rolesV2 from './roles/v2/reducer';
import userManagementV2 from './user_management/v2/reducer';
import adminDashboard from './admin_dashboard/reducer';
import assessment from './assessment/reducer';
import programCalendar from './program_calendar/reducer';
import institution_calendar from './institution_calendar/reducer';
import sessionTrackingReport from './session_tracking_report/reducer';
import lmsReports from './LeaveManagementReports/reducer';
import globalConfigurationV1 from './global_configuration/v1/reducer';
import announcementUserPermission from './Announcement/reducer';
import userGlobalSearch from './UserGlobalSearch/reducer';
import qapc from './qapc/reducer';
import q360 from './q360/reducer';
import q360RolesAndPermission from './q360/roleAndPermission/reducer';
import studentGroupV2 from './studentGroupV2/reducer';
// import userModulePermission from './userModulePermission/reducer';
import courseHandout from './courseHandout/reducer';

const rootReducers = combineReducers({
  calender,
  interimCalendar,
  auth,
  department,
  student,
  infrastructure,
  genericForm,
  leaveManagement,
  roles,
  userManagement,
  programInput,
  lmsReports,
  institution,
  courseScheduling,
  dashboard,
  reportsAndAnalytics,
  globalConfiguration,
  programInputV2,
  rolesV2,
  userManagementV2,
  adminDashboard,
  assessment,
  programCalendar,
  institution_calendar,
  sessionTrackingReport,
  globalConfigurationV1,
  announcementUserPermission,
  userGlobalSearch,
  qapc,
  q360,
  q360RolesAndPermission,
  studentGroupV2,
  courseHandout,
});

export default rootReducers;
