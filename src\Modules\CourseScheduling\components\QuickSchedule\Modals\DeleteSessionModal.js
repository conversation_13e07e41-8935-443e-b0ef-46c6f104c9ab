import React from 'react';
import PropTypes from 'prop-types';
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { Map } from 'immutable';
import { buttonSx, SessionDetails } from '../utils';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #EB5757',
};
const titleIconSx = {
  padding: '8px',
  backgroundColor: '#FEE2E2',
  borderRadius: '50%',
  marginRight: '12px',
  color: '#EB5757',
};

const DeleteSessionModal = ({ open, data, handleClose, handleDelete }) => {
  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center">
          <Box display="flex" sx={titleIconSx}>
            <DeleteOutlineIcon />
          </Box>
          Confirm Deletion
        </Box>
      </DialogTitle>
      <DialogContent className="p-3">
        <SessionDetails scheduleData={data} />
        <Typography mt={2} color="#374151">
          Are you sure you want to <span className="bold">delete</span>?
        </Typography>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" sx={buttonSx} clicked={handleClose}>
          Cancel
        </MButton>
        <MButton
          variant="contained"
          color="error"
          className="ml-2"
          sx={{ ...buttonSx, backgroundColor: '#EB5757' }}
          clicked={handleDelete}
        >
          Yes, Delete
        </MButton>
      </DialogActions>
    </Dialog>
  );
};

DeleteSessionModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
  handleDelete: PropTypes.func,
};

export default DeleteSessionModal;
