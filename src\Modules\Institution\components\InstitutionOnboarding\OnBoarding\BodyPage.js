import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
//import { Map } from 'immutable';

import InstitutionType from '../Component/InstitutionType';
import InstitutionForm from '../Component/InstitutionForm/InstitutionIndex';
import * as actions from '../../../../../_reduxapi/institution/actions';
import { checkValidation } from '../../UniversityDetails/udUtil';
import CancelModal from '../../AllColleges/Modal/Cancel';

function BodyPage({ history, addInstitution, setData }) {
  const [type, setSelectedType] = useState('');
  const [showInstitutionForm, setShowInstitutionForm] = useState(false);
  const [cancelShow, setCancelShow] = useState(false);
  const [isChanged, setIsChanged] = useState(false);

  const handleNext = (data) => {
    setShowInstitutionForm(true);
    setSelectedType(data);
  };

  const handleBack = () => {
    setShowInstitutionForm(isChanged ? true : false);
    setCancelShow(isChanged ? true : false);
  };

  const handleClose = (type) => {
    const show = type === 'no' ? true : false;
    setShowInstitutionForm(show);
    setCancelShow(false);
    setIsChanged(false);
  };

  const onSubmit = (data) => {
    if (checkValidation(data, type.type, setData)) {
      setIsChanged(false);
      const formData = new FormData();
      Object.entries({
        ...data,
        type: type.type === 'group' ? 'University' : 'College',
      }).forEach(([key, value]) => {
        if (value !== '') formData.append(key, value);
      });
      addInstitution({ operation: 'create', formData, history });
    }

    return;
  };
  return (
    <>
      {!showInstitutionForm ? (
        <InstitutionType handleNext={handleNext} />
      ) : (
        <InstitutionForm
          handleBack={handleBack}
          onSubmit={onSubmit}
          type={type.type}
          name={type.name}
          setData={setData}
          setIsChanged={setIsChanged}
        />
      )}
      {cancelShow && (
        <CancelModal
          show={cancelShow}
          type="create"
          handleClose={handleClose}
          setIsChanged={setIsChanged}
        />
      )}
    </>
  );
}
BodyPage.propTypes = {
  history: PropTypes.object,
  onSubmit: PropTypes.func,
  addInstitution: PropTypes.func,
  setData: PropTypes.func,
  type: PropTypes.string,
};
export default compose(withRouter, connect(null, actions))(BodyPage);
