import React, { Fragment, useEffect } from 'react';
import { connect } from 'react-redux';
import { FormControl, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import * as actions from '_reduxapi/global_configuration/actions';
import { Map, List } from 'immutable';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import OnlineVerify from 'Assets/onlineVerify.svg';
import OfflineVerify from 'Assets/offlineVerify.svg';
import { selectBioCOnfigDetails } from '_reduxapi/global_configuration/selectors';
import { AccordionProgramInput } from '../ReusableComponent';

const image = [OnlineVerify, OfflineVerify];
const Biometrics = (props) => {
  const {
    bioConfigDetails,
    settingId,
    getBioMetricConfig,
    getBioMetricConfigUpdate,
    accordionOpen,
    setAccordionOpen,
    setAccordionHeader,
    count,
    institutionHeader,
    type,
  } = props;
  const [bioMetric, setBioMetric] = React.useState('');

  const handleChange = (event) => {
    let onChangeValue = event.target.value;
    getBioMetricConfigUpdate({
      settingId,
      id: onChangeValue,
      value: event.target.name,
      headers: institutionHeader,
      type,
    });
  };

  useEffect(() => {
    setBioMetric(
      bioConfigDetails
        .get('data', Map())
        .filter((el) => el.get('isActive', true) === true)
        .getIn(['0', '_id'], '')
    );
  }, [bioConfigDetails]);

  useEffect(() => {
    settingId &&
      accordionOpen &&
      count !== 0 &&
      !bioConfigDetails.size &&
      getBioMetricConfig({ settingId, headers: institutionHeader, type });
  }, [settingId, accordionOpen, count]); //eslint-disable-line

  const getDescription = (labelName) => {
    switch (labelName) {
      case 'Online verification':
        return 'global_configuration.online_verification_desc';
      case 'Offline verification':
        return 'global_configuration.offline_verification_desc';
      default:
        return 'global_configuration.both_verification_desc';
    }
  };

  const getDetailsComponent = () => {
    return (
      <div className="container">
        <div className="digi-pl-12">
          <FormControl component="fieldset">
            <RadioGroup
              aria-label="gender"
              name="bio"
              value={bioMetric}
              onChange={(e) => handleChange(e)}
            >
              {bioConfigDetails.get('data', List()).map((bio, index) => (
                <Fragment key={index}>
                  <FormControlLabel
                    name={bio.get('labelName', '')}
                    value={bio.get('_id', '')}
                    control={<Radio color="primary" />}
                    label={bio.get('labelName', '')}
                    className="mb-0"
                  />
                  <div className="mb-4">
                    <p className="mb-3 f-14 pl-4 ml-2">
                      <Trans i18nKey={getDescription(bio.get('labelName', ''))} />
                    </p>

                    {bioConfigDetails.get('data', List()).size - 1 > index && (
                      <img src={image[index]} alt="OnlineVerify" className="" />
                    )}
                  </div>
                </Fragment>
              ))}
            </RadioGroup>
          </FormControl>
        </div>
      </div>
    );
  };
  const handleClick = () => {
    setAccordionOpen();
  };

  return (
    <>
      <AccordionProgramInput
        expanded={accordionOpen || false}
        onClick={handleClick}
        summaryChildren={setAccordionHeader('userManagement.bio', count, !accordionOpen)}
        detailChildren={getDetailsComponent()}
      />
    </>
  );
};

Biometrics.propTypes = {
  bioConfigDetails: PropTypes.instanceOf(Map),
  getBioMetricConfig: PropTypes.func,
  getBioMetricConfigUpdate: PropTypes.func,
  settingId: PropTypes.string,
  accordionOpen: PropTypes.bool,
  setAccordionOpen: PropTypes.func,
  setAccordionHeader: PropTypes.func,
  count: PropTypes.string,
  type: PropTypes.string,
  institutionHeader: PropTypes.object,
};

const mapStateToProps = (state) => {
  return {
    bioConfigDetails: selectBioCOnfigDetails(state),
  };
};
export default connect(mapStateToProps, actions)(Biometrics);
