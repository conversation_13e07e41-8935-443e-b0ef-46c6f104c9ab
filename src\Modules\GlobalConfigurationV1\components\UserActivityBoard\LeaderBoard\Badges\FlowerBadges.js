import { List, Map as immutableMap } from 'immutable';
import React from 'react';
import PropTypes from 'prop-types';
export default function FlowerBadges({ inputDisable, badgeKey, setState, sourceDirectory, state }) {
  return (
    <div className="grid_badges_section">
      {state.get('badgeName', List()).map((badge, index) => (
        <div key={index} className="text-center">
          <img src={sourceDirectory.get(badgeKey + index)} alt={badge.get('name', '')} />
          <div>{badge.get('name', '')}</div>
          <span className="f-12">Percentage Range</span>
          <div className="d-flex justify-content-center mt-2">
            <input
              type="text"
              placeholder="00%"
              onChange={(e) => {
                const value = e.target.value;
                if (value !== '' && (Number(value) < 0 || isNaN(Number(value)))) return;
                setState((prev) => prev.setIn(['badgeName', index, 'startRange'], value));
              }}
              disabled={inputDisable}
              className="criteria_input_badge"
              value={badge.get('startRange', '')}
            />
            <input
              type="text"
              onChange={(e) => {
                const value = e.target.value;
                if ((value !== '' && Number(value) < 0) || isNaN(Number(value))) return;
                setState((prev) => prev.setIn(['badgeName', index, 'endRange'], value));
              }}
              placeholder="00%"
              disabled={inputDisable}
              className="criteria_input_badge ml-3"
              value={badge.get('endRange', '')}
            />
          </div>
        </div>
      ))}
    </div>
  );
}
FlowerBadges.propTypes = {
  inputDisable: PropTypes.bool,
  setState: PropTypes.func,
  state: PropTypes.instanceOf(immutableMap),
  sourceDirectory: PropTypes.instanceOf(Map),
  badgeKey: PropTypes.string,
};
