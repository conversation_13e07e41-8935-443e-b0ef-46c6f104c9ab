import React, { useState, useContext, Suspense } from 'react';
import { List } from 'immutable';
import Departments from './Departments/Departments';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import Accordion from '@mui/material/Accordion';
const Subjects = React.lazy(() =>
  import(
    'Modules/ProgramInput/v2/ConfigurationIndex/Departments/Components/DepartmentSubjects/Subjects/Subjects'
  )
);

function DepartmentSubjectBody() {
  const [expanded, setExpanded] = useState(false);
  const department = useContext(parentContext.departmentContext);
  const { departmentList, translateInput } = department;
  const handleChangeExpanded = (panel) => (event, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };
  return (
    <div className="mt-2">
      <Suspense fallback="">
        {departmentList.get('departments', List()).map((item, index) => (
          <Accordion
            key={index}
            expanded={expanded === `panel_${index}`}
            onChange={handleChangeExpanded(`panel_${index}`)}
            className="digi-mt-12 digi-p-4"
          >
            <Departments
              data={item}
              translateInput={translateInput}
              expanded={expanded === `panel_${index}`}
            />
            <Subjects
              data={item}
              translateInput={translateInput}
              subjectSharedData={departmentList.get('subjectsSharedToThisProgram', List())}
            />
          </Accordion>
        ))}
      </Suspense>
    </div>
  );
}

export default DepartmentSubjectBody;
