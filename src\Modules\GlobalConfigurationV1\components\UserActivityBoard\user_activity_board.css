.add_parameter_border{
  border:1.5px dashed #d1d5db
}

.criteria_border{
  border:1px solid #d1d5db
}

.criteria_card{
  /* width:350px; */
  border-radius:5px
}

.leader_info_color{
  color:
  #6B7280
}

.criteria_input{
  width: 40px;
  text-align: center;
  border-radius: 10px;
  border: 1px solid #D1D5DB;
  margin: 2px 7px;
}

.criteria_input_badge{
  width: 50px;
  line-height: 30px;
  border-radius: 5px;
  border: 1px solid black;
  text-align: center;
  margin: 0px 7px;
  background: white;
  color: black;
}

.criteria_input_badge:disabled{
  border: 1.5px solid #D1D5DB;
  text-align: center;
  margin: 0px 7px;
}

.grid_badges_section{
  display: grid;
  grid-template-columns: repeat(auto-fill,minmax(180px,1fr))
}

.height_100vh{
  min-height: 100vh;
}

.grid_criteria_leader{
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 20px;
}

::-webkit-scrollbar {
  width: 5px; /* You can adjust the width here */
}
::-webkit-scrollbar-thumb {
  background-color:#b2b5b8; /* Color of the scrollbar thumb */
}

.hover_active_delete_icon{
  pointer-events: none;
  visibility: hidden;
}

.table_row_anomaly:hover .hover_active_delete_icon{
  pointer-events:all;
  cursor: pointer;
  visibility: visible;
}

.dropdown_center_mui {
  position: relative;
  top: 8px;
}