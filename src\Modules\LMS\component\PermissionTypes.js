import React, { useState, useContext, useEffect, Suspense } from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PropTypes from 'prop-types';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { Divider } from '@mui/material';
import { connect } from 'react-redux';
import { Map, fromJS } from 'immutable';

// components
import TypeDetails from './TypeDetails';
import LeaveType from './LeaveType';
import { ClassificationsContext } from './Classifications';

// utils
import { useStylesFunction } from '../designUtils';
import Input from 'Widgets/FormElements/Input/Input';

// redux
import * as actions from '_reduxapi/leave_management/actions';

const DeleteModal = React.lazy(() => import('./Modal/DeleteModal'));

const PermissionTypes = ({
  catIndex,
  typeIndex,
  typeDetails,
  updateType,
  deleteType,
  isEditTypeWiseConfig,
  handleClickOpen,
  setData,
  isEditLeaveClassification,
}) => {
  const { lmsSettings, ListApi, type } = useContext(ClassificationsContext);
  const [active, setActive] = useState();
  const [detailsOfTypes, setDetailsOfTypes] = useState(Map());
  const [show, setShow] = useState(false);
  const [permissionTypes, setPermissionTypes] = useState(
    fromJS([
      {
        typeName: 'Define the Permission',
        typeDescription: '',
        color: '',
        edited: true,
      },
    ])
  );

  useEffect(() => {
    setDetailsOfTypes(typeDetails);
    setActive(typeDetails.get('isActive', true));
  }, [typeDetails]); // eslint-disable-line

  const { accordion, accordionSummaryBorder } = useStylesFunction();

  const changeActiveInactive = (e, type, btnName, btnValue) => {
    e.stopPropagation();
    if (active !== btnValue) {
      btnName === 'activeBtn' ? setActive(true) : setActive(false);
      updatePermissionTypes(type, btnValue);

      setData(
        Map({
          lmsSettings: lmsSettings.setIn(
            ['catagories', catIndex, 'types', typeIndex, 'isActive'],
            btnValue
          ),
        })
      );
    }
  };

  const changeTypeDetails = (type, value, checked) => {
    let inputValues = Map();
    let currentValue;
    let checkBoxVariableNames = [
      'attachmentMandatory',
      'isReasonFromApplicant',
      'isProofToBeAttached',
    ];

    switch (type) {
      case 'frequencyBy':
        inputValues = detailsOfTypes.setIn(
          ['frequency', type],
          value === 'Per Year' ? 'year' : 'month'
        );
        break;

      case 'setFrequency':
        inputValues = detailsOfTypes.setIn(['frequency', type], value);
        break;

      case 'frequencyByMonth':
        inputValues = detailsOfTypes.setIn(['frequency', type], value);
        break;

      default:
        currentValue = checkBoxVariableNames.includes(type) ? checked : value;
        inputValues = detailsOfTypes.set(type, currentValue);
    }

    setDetailsOfTypes(inputValues);

    const timeout = setTimeout(
      () => updatePermissionTypes(type, checkBoxVariableNames.includes(type) ? checked : value),
      500
    );
    return () => clearTimeout(timeout);
  };

  const updatePermissionTypes = (type, value) =>
    updateType({
      settingId: lmsSettings.get('_id', ''),
      typesId: typeDetails.get('_id', ''),
      [type]: value,
    });

  const [toBeDeletedName, setToBeDeletedName] = useState('');
  const handleDeleteTypes = (e) => {
    setToBeDeletedName(typeDetails.get('typeName', ''));
    e.stopPropagation();
    handleClose(e);
    setShow(true);
  };

  const handleDelete = () => {
    deleteType(
      {
        settingId: lmsSettings.get('_id', ''),
        categoryId: lmsSettings.getIn(['catagories', catIndex, '_id'], ''),
        typesId: typeDetails.get('_id', ''),
      },
      ListApi
    );
    setShow(false);
  };

  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (e) => {
    e.stopPropagation();
    setAnchorEl(e.currentTarget);
  };

  const handleClose = (e) => {
    e.stopPropagation();
    setAnchorEl(null);
  };

  return (
    <React.Fragment>
      <Accordion className={`${accordion}`}>
        <AccordionSummary
          expandIcon={
            <div className="mt-2">
              <ExpandMoreIcon />
            </div>
          }
          aria-controls="panel1bh-content"
          id="panel1bh-header"
          className={`panelSummary ${accordionSummaryBorder}`}
        >
          <Typography component={'span'} sx={{ flexShrink: 0 }} className="mt-2 text-body bold">
            <div className="pt-2 text-capitalize digi-text-ellipsis digi-text-ellipsis-accordion">
              {typeDetails.get('typeName', '')}
            </div>
          </Typography>
          <div className="flex-grow-1 m-2 mb-4 pr-3 pl-3">
            <Divider className="mt-3 pt-1 digi-divider-mdl " />
          </div>
          <div className="d-flex align-items-center mt-1 mr-2">
            <div className="pt-2">
              <Input
                elementType={'activeInactiveButton'}
                onClick={(e, btnName, btnValue) => {
                  if (type === 'leave' ? !isEditLeaveClassification : !isEditTypeWiseConfig) return;
                  changeActiveInactive(e, 'isActive', btnName, btnValue);
                }}
                active={active}
              />
            </div>
            <div className="ml-2 mt-1 pt-1">
              {(type === 'leave' ? isEditLeaveClassification : isEditTypeWiseConfig) && (
                <MoreVertIcon
                  color="primary"
                  id="basic-button"
                  aria-controls={open ? 'basic-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                  onClick={handleClick}
                />
              )}
              <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                  'aria-labelledby': 'basic-button',
                }}
                className="pr-5 pl-5"
              >
                <MenuItem
                  className=" pr-4 pl-4 text-primary"
                  onClick={(e) => {
                    handleClose(e);
                    handleClickOpen(e, catIndex, typeIndex, 'Edit');
                  }}
                >
                  Edit
                </MenuItem>
                <Divider />
                <MenuItem className=" pr-4 pl-4 text-danger" onClick={(e) => handleDeleteTypes(e)}>
                  Delete
                </MenuItem>
              </Menu>
            </div>
          </div>
        </AccordionSummary>
        <AccordionDetails>
          {lmsSettings.get('classificationType', '') === 'leave' ? (
            <LeaveType
              typeDetails={detailsOfTypes}
              changeTypeDetails={changeTypeDetails}
              isPopup={false}
              permissionTypes={permissionTypes}
              setPermissionTypes={setPermissionTypes}
              disabled={true}
            />
          ) : (
            <TypeDetails
              typeDetails={detailsOfTypes}
              changeTypeDetails={changeTypeDetails}
              isPopup={false}
              disabled={true}
            />
          )}
        </AccordionDetails>
      </Accordion>

      {show && (
        <Suspense fallback="">
          <DeleteModal
            show={show}
            setShow={setShow}
            handleDelete={handleDelete}
            name={'Type'}
            toBeDeletedName={toBeDeletedName}
          />
        </Suspense>
      )}
    </React.Fragment>
  );
};

PermissionTypes.propTypes = {
  typeDetails: PropTypes.instanceOf(Map),
  updateType: PropTypes.func,
  deleteType: PropTypes.func,
  handleClickOpen: PropTypes.func,
  typeIndex: PropTypes.number,
  isEditTypeWiseConfig: PropTypes.bool,
  catIndex: PropTypes.number,
  setData: PropTypes.func,
  isEditLeaveClassification: PropTypes.bool,
  type: PropTypes.string,
};

export default connect(null, actions)(PermissionTypes);
