import React from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import MButton from 'Widgets/FormElements/material/Button';
import i18n from '../../../i18n';
function DeleteModal(props) {
  const {
    open,
    handleClose,
    item,
    updateEventType,
    type,
    updateBreak,
    settingId,
    updateProgramType,
    institutionHeader,
    updateProgramBasicDetails,
    CallingProgramSettings,
    getProgramID,
    institutionId,
    programID,
    emailIdConFiguration,
    _id,
  } = props;
  const id = item ? item.get('_id', '') : '';
  const handleSubmit = () => {
    const requestData = {
      settingId: settingId,
    };
    if (type === 'eventDelete') {
      !programID
        ? updateEventType({
            operation: 'delete',
            id,
            settingId,
            name: item.get('name', ''),
            isLeave: item.get('isLeave', false),
            callBack: () => handleClose(),
            header: institutionHeader,
          })
        : updateProgramBasicDetails({
            operation: 'delete',
            urlEventRemove: `/program-input/remove-eventtype/${id}`,
            programId: getProgramID,
            id: id,
            callBack: () => handleClose(),
            header: institutionHeader,
            CallingProgramSettings: (props) => CallingProgramSettings({ ...props }),
            institutionId: institutionId,
          });
    }
    if (type === 'breakDelete') {
      !programID
        ? updateBreak({
            operation: 'delete',
            id,
            requestBody: { settingId },
            callBack: () => handleClose(),
            header: institutionHeader,
          })
        : updateProgramBasicDetails({
            operation: 'delete',
            programId: getProgramID,
            urlBreakRemove: `/program-input/remove-break/${id}`,
            CallingProgramSettings: (props) => CallingProgramSettings({ ...props }),
            id: id,
            breakValue: true,
            callBack: () => handleClose(),
            institutionId: institutionId,
            header: institutionHeader,
          });
    }
    if (type === 'programTypeDelete')
      updateProgramType({
        id,
        requestData: {
          settingId: settingId,
        },
        operation: 'delete',
        cb: handleClose,
        header: institutionHeader,
      });
    if (type === 'email') {
      emailIdConFiguration({
        operation: 'delete',
        id: _id,
        callBack: () => handleClose(),
        header: institutionHeader,
        requestData: requestData,
      });
    }
  };
  return (
    <div>
      <Modal show={open} centered onHide={handleClose}>
        <Modal.Header className="border-none pb-0">
          <Modal.Title className="f-20">
            <Trans i18nKey={'delete'} />{' '}
            {type === 'email'
              ? i18n.t('email')
              : type === 'breakDelete'
              ? i18n.t('course_schedule.break')
              : type === 'programTypeDelete'
              ? i18n.t('program_type')
              : i18n.t('event_type')}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="pt-4">
          <div className="f-16">
            <div className="mb-2">
              <Trans
                i18nKey={
                  type === 'email'
                    ? 'global_configuration.delete_confirm_modal.this_action_will_delete_email'
                    : type === 'breakDelete'
                    ? 'global_configuration.delete_confirm_modal.this_action_will_delete_break'
                    : type === 'eventDelete'
                    ? 'global_configuration.delete_confirm_modal.this_action_will_delete_event'
                    : 'global_configuration.delete_confirm_modal.this_action_will_delete_program_type'
                }
              ></Trans>
            </div>
            <div>
              <Trans i18nKey={'are_you_sure_you_want_to_delete_the_selected'}></Trans>{' '}
              <span className="text_wrap">
                <b>{type === 'email' ? i18n.t('mail') : item.get('name', '')}</b>
              </span>{' '}
              ?
            </div>
          </div>
        </Modal.Body>

        <Modal.Footer className="border-none">
          <b className="pr-2">
            <MButton color="primary" variant="contained" clicked={handleSubmit}>
              <Trans i18nKey={'confirm'}></Trans>
            </MButton>
          </b>
          <b className="pr-2">
            <MButton color="inherit" variant="outlined" clicked={handleClose}>
              <Trans i18nKey={'cancel'}></Trans>
            </MButton>
          </b>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

DeleteModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  updateEventType: PropTypes.func,
  updateBreak: PropTypes.func,
  settingId: PropTypes.string,
  item: PropTypes.object,
  type: PropTypes.string,
  updateProgramType: PropTypes.func,
  institutionHeader: PropTypes.object,
  updateProgramBasicDetails: PropTypes.func,
  getProgramID: PropTypes.string,
  programID: PropTypes.string,
  institutionId: PropTypes.string,
  CallingProgramSettings: PropTypes.func,
  emailIdConFiguration: PropTypes.func,
  _id: PropTypes.string,
};

export default DeleteModal;
