import * as actions from './actions';
import { List, Map, fromJS } from 'immutable';
import {
  constructCategoryData,
  getFormCourseGroupData,
  splitFunctionCfpcWise,
  splitFunctionPccfWise,
} from 'Modules/QAPC/components/RolesAndPermission/component/util';

export const initialState = fromJS({
  isLoading: false,
  message: '',
  allRoles: [],
  allProgramCheck: {},
  categoryData: {},
  categorySubData: {},
  programSubData: {},
  modules: {},
  users: [],
  staffList: {},
  rolesStaffList: {},
  subModules: {},
  actions: {},
  formApprover: [],
  qapcActions: {},
  assignedDetails: {},
  saveRolesAndPermission: {},
  academicYears: [],
  formApproverLevels: {},
  assignedUserDetails: {},
  open: -1,
  assignArray: [],
});

export default function (state = initialState, action) {
  switch (action.type) {
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.RESET_REDUCER_MESSAGE_SUCCESS: {
      return initialState;
    }
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.GET_ALL_ROLES_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_ALL_ROLES_SUCCESS: {
      return state.set('allRoles', fromJS(action.data)).set('isLoading', false);
    }
    case actions.GET_ALL_ROLES_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occurred');
    }
    case actions.CREATE_ROLE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.CREATE_ROLE_SUCCESS: {
      const { id, name } = action.data;
      const isEdit = id !== '';
      return state
        .set('isLoading', false)
        .set('message', isEdit ? 'Updated Successfully' : 'Added Successfully')
        .update('allRoles', (data) =>
          isEdit
            ? data.map((item) => (item.get('_id', '') === id ? item.set('roleName', name) : item))
            : data.insert(0, fromJS(action.data.data))
        );
    }
    case actions.CREATE_ROLE_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occurred');
    }
    case actions.GET_FORM_DATA_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FORM_DATA_SUCCESS: {
      const { hierarchy, level } = action.params;
      return state
        .set('isLoading', false)
        .update((data) =>
          data.setIn(
            ['categoryData', hierarchy, level],
            constructCategoryData(fromJS(action.data), action.params)
          )
        );
    }
    case actions.GET_FORM_DATA_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occurred');
    }
    case actions.GET_FORM_SUB_DATA_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FORM_SUB_DATA_SUCCESS: {
      const { categoryFormId, level, qapcRoleId } = action.params;
      let constructData = Map();
      const categoryFormCourseData = fromJS(action.data).get('categoryFormCourseData', List());
      const categoryGroupData = fromJS(action.data).get('categoryGroupData', List());
      categoryFormCourseData.forEach((category) => {
        const _id = category.get('_id', '');
        const categoryFormId = category.get('categoryFormId', '');
        const programName = category.get('programName', '');
        const curriculumName = category.get('curriculumName', '');
        const programId = category.get('programId', '');
        const yearName = category.get('year', '');
        const courseName = category.get('courseName', '');
        const institutionId = category.get('assignedInstitutionId', '');
        const institutionName = category.get('institutionName', '');
        if (institutionId) {
          constructData = constructData.update(
            `${institutionName}+${institutionId}`,
            List(),
            (update) => {
              if (!update.includes(_id)) update = update.push(_id);
              return update;
            }
          );
        }
        if (programName) {
          constructData = constructData.update(`${programName}+${programId}`, List(), (update) => {
            if (!update.includes(_id)) update = update.push(_id);
            return update;
          });
        }
        if (curriculumName) {
          constructData = constructData.update(
            `${curriculumName}+${programId}`,
            List(),
            (update) => {
              if (!update.includes(_id)) update = update.push(_id);
              return update;
            }
          );
        }
        if (yearName) {
          constructData = constructData.update(`${yearName}+${programId}`, List(), (update) => {
            if (!update.includes(_id)) update = update.push(_id);
            return update;
          });
        }
        if (courseName) {
          constructData = constructData.update(`${courseName}+${programId}`, List(), (update) => {
            if (!update.includes(_id)) update = update.push(_id);
            return update;
          });
        }
        constructData = constructData.update(`${categoryFormId}+allPrograms`, List(), (update) => {
          if (!update.includes(_id)) update = update.push(_id);
          return update;
        });
      });
      categoryGroupData.forEach((groupAction) => {
        const _id = groupAction.get('_id', '');
        const term = groupAction.get('term', '');
        const attemptTypeName = groupAction.get('attemptTypeName', '');
        const academicYear = groupAction.get('academicYear', '');
        const groupName = groupAction.get('groupName', '');
        const categoryFormCourseId = groupAction.get('categoryFormCourseId', '');
        const termCondition = groupAction.has('term') && term !== 'none';
        const attemptTypeNameCondition =
          groupAction.has('attemptTypeName') && attemptTypeName !== 'none';
        const groupCondition = groupAction.has('groupName') && groupName !== 'none';
        const academicYearCondition = academicYear && academicYear !== 'none';
        if (termCondition) {
          constructData = constructData.update(
            `${categoryFormCourseId}+${_id}+${term}`,
            List(),
            (update) => {
              if (!update.includes(_id)) update = update.push(_id);
              return update;
            }
          );
        }
        if (attemptTypeNameCondition) {
          if (termCondition) {
            constructData = constructData.update(
              `${categoryFormCourseId}+${_id}+${term}+${attemptTypeName}`,
              List(),
              (update) => {
                if (!update.includes(_id)) update = update.push(_id);
                return update;
              }
            );
          }
          constructData = constructData.update(
            `${categoryFormCourseId}+${_id}+${attemptTypeName}`,
            List(),
            (update) => {
              if (!update.includes(_id)) update = update.push(_id);
              return update;
            }
          );
        }
        if (groupCondition) {
          if (termCondition && attemptTypeNameCondition) {
            constructData = constructData.update(
              `${categoryFormCourseId}+${_id}+${term}+${attemptTypeName}+${groupName}`,
              List(),
              (update) => {
                if (!update.includes(_id)) update = update.push(_id);
                return update;
              }
            );
          } else if (attemptTypeNameCondition) {
            constructData = constructData.update(
              `${categoryFormCourseId}+${_id}+${attemptTypeName}+${groupName}`,
              List(),
              (update) => {
                if (!update.includes(_id)) update = update.push(_id);
                return update;
              }
            );
          }
          constructData = constructData.update(
            `${categoryFormCourseId}+${_id}+${groupName}`,
            List(),
            (update) => {
              if (!update.includes(_id)) update = update.push(_id);
              return update;
            }
          );
        }
        if (academicYearCondition) {
          if (
            termCondition &&
            attemptTypeNameCondition &&
            groupCondition &&
            academicYearCondition
          ) {
            constructData = constructData.update(
              `${categoryFormCourseId}+${_id}+${term}+${attemptTypeName}+${groupName}+${academicYear}`,
              List(),
              (update) => {
                if (!update.includes(_id)) update = update.push(_id);
                return update;
              }
            );
          } else if (attemptTypeNameCondition && groupCondition && academicYearCondition) {
            constructData = constructData.update(
              `${categoryFormCourseId}+${_id}+${attemptTypeName}+${groupName}+${academicYear}`,
              List(),
              (update) => {
                if (!update.includes(_id)) update = update.push(_id);
                return update;
              }
            );
          } else if (attemptTypeNameCondition && groupCondition) {
            constructData = constructData.update(
              `${categoryFormCourseId}+${_id}+${attemptTypeName}+${groupName}`,
              List(),
              (update) => {
                if (!update.includes(_id)) update = update.push(_id);
                return update;
              }
            );
          } else if (groupCondition) {
            constructData = constructData.update(
              `${categoryFormCourseId}+${_id}+${groupName}+${academicYear}`,
              List(),
              (update) => {
                if (!update.includes(_id)) update = update.push(_id);
                return update;
              }
            );
          }
          constructData = constructData.update(
            `${categoryFormCourseId}+${_id}+${academicYear}`,
            List(),
            (update) => {
              if (!update.includes(_id)) update = update.push(_id);
              return update;
            }
          );
        }
      });
      return state.set('isLoading', false).update((data) =>
        data
          .setIn(
            ['categorySubData', qapcRoleId, categoryFormId],
            splitFunctionCfpcWise(fromJS(action.data), level)
          )
          .setIn(
            ['categorySubData', 'categoryCourseIds', qapcRoleId, categoryFormId],
            constructData
          )
          .setIn(
            ['categorySubData', 'categoryMainData', qapcRoleId, categoryFormId],
            fromJS(action.data)
          )
      );
    }
    case actions.GET_FORM_SUB_DATA_FAILURE: {
      return state.set('isLoading', false).set('message', 'No Data Found');
    }
    case actions.GET_PROGRAM_SUB_DATA_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_SUB_DATA_SUCCESS: {
      const { level } = action.params;
      const levelData = state.getIn(['categoryData', 'pccf', level], Map());
      const { result, nextSubDataKey } = splitFunctionPccfWise(
        fromJS(action.data),
        level,
        levelData
      );
      return state
        .set('isLoading', false)
        .set('programSubData', result)
        .set('programSubDataKey', nextSubDataKey);
    }
    case actions.GET_PROGRAM_SUB_DATA_FAILURE: {
      const { response } = action.error;
      return state
        .set('isLoading', false)
        .set('error', action.error)
        .set('message', response ? `${response.data.data}` : 'Error Occurred');
    }
    case actions.GET_PROGRAM_GROUP_DATA_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_PROGRAM_GROUP_DATA_SUCCESS: {
      const { constructKey, formId, data, categoryFormIds } = action;
      const constructData = getFormCourseGroupData(Map({ categoryGroupData: fromJS(data) }))(
        formId
      );
      return state
        .set('isLoading', false)
        .setIn(
          ['programSubData', ...constructKey],
          state
            .getIn(['programSubData', ...constructKey], Map())
            .merge(constructData.get(categoryFormIds, Map()))
        );
    }
    case actions.GET_PROGRAM_GROUP_DATA_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_USERS_LIST_REQUEST: {
      return state; //.set('isLoading', true)
    }
    case actions.GET_USERS_LIST_SUCCESS: {
      // let constructData = Map();
      // fromJS(action.data).forEach((userList) => {
      //   const userId = userList.get('_id', '');
      //   const name = userList.get('name', Map());
      //   const first = name.get('first', '');
      //   const middle = name.get('middle', '');
      //   const last = name.get('last', '');
      //   const academicId = userList.get('user_id', '');
      //   const userName = `${first}${middle}${last}${academicId}`;
      //   constructData = constructData.set(userId, fromJS({ userId, name, userName, academicId }));
      // });
      let constructData = List();
      fromJS(action.data).forEach((userList, userIndex) => {
        const userId = userList.get('_id', '');
        const name = userList.get('name', Map());
        const first = name.get('first', '');
        const middle = name.get('middle', '');
        const last = name.get('last', '');
        const academicId = userList.get('user_id', '');
        const userName = `${first}${middle}${last}${academicId}`;
        constructData = constructData.set(
          userIndex,
          fromJS({ userId, name, userName, academicId })
        );
      });
      return state.set('staffList', fromJS(constructData)); //.set('isLoading', false)
    }
    case actions.GET_USERS_LIST_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_ROLE_LIST_REQUEST: {
      return state; //.set('isLoading', true)
    }
    case actions.GET_ROLE_LIST_SUCCESS: {
      let constructData = Map();
      fromJS(action.data.roleUserList).forEach((roleList) => {
        const name = roleList.getIn(['_user_id', 'name'], Map());
        const userId = roleList.getIn(['_user_id', '_id'], '');
        const first = name.get('first', '');
        const middle = name.get('middle', '');
        const last = name.get('last', '');
        const academicId = roleList.getIn(['_user_id', 'user_id'], '');
        const userName = `${first}${middle}${last}${academicId}`;
        roleList.get('roles', List()).forEach((roleDetails) => {
          const roleId = roleDetails.getIn(['_role_id', '_id'], '');
          const roleName = roleDetails.getIn(['_role_id', 'name'], '');
          constructData = constructData.update(roleId, Map(), (update) => {
            update = update
              .setIn(['userIds', userId], fromJS({ name, userId, userName, academicId }))
              .set('roleId', roleId)
              .set('roleName', roleName);
            return update;
          });
        });
      });
      return state.set('rolesStaffList', constructData); //.set('isLoading', false)
    }
    case actions.GET_ROLE_LIST_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_SUB_MODULES_REQUEST: {
      return state; //.set('isLoading', true)
    }
    case actions.GET_SUB_MODULES_SUCCESS: {
      let constructData = Map();
      fromJS(action.data).forEach((subModule) => {
        const subModuleId = subModule.get('_id', '');
        const subModuleName = subModule.get('moduleName', Map());
        const actionsIds = subModule.get('actionsIds', List());
        constructData = constructData.set(
          subModuleId,
          fromJS({
            subModuleId,
            subModuleName,
            actionsIds: actionsIds.map((actionId) => fromJS({ actionId, checked: false })),
          })
        );
      });
      return state.set('subModules', constructData); //.set('isLoading', false)
    }
    case actions.GET_SUB_MODULES_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_FORM_APPROVER_LEVEL_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_FORM_APPROVER_LEVEL_SUCCESS: {
      const { data, key } = action.data;
      return state
        .set('isLoading', false)
        .set('formApprover', fromJS(data))
        .setIn(['formApproverLevels', key], fromJS(data));
    }
    case actions.GET_FORM_APPROVER_LEVEL_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_ROLES_PERMISSION_ACTIONS_REQUEST: {
      return state; //.set('isLoading', true)
    }
    case actions.GET_ROLES_PERMISSION_ACTIONS_SUCCESS: {
      let constructData = Map();
      fromJS(action.data).forEach((actions) => {
        const userId = actions.get('_id', '');
        constructData = constructData.set(userId, actions);
      });
      return state.set('qapcActions', constructData); //.set('isLoading', false)
    }
    case actions.GET_ROLES_PERMISSION_ACTIONS_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_ASSIGNED_USER_LIST_REQUEST: {
      return state
        .set('isLoading', true)
        .set('assignedDetails', fromJS({}))
        .set('assignedUserDetails', fromJS({}));
    }
    case actions.GET_ASSIGNED_USER_LIST_SUCCESS: {
      let assignedDetails = Map();
      let number = Map();
      let multiUserList = Map();
      fromJS(action.data).forEach((response) => {
        const selectType = response.get('selectType', '').split('@@')[0];
        const formCourseGroupId = response.get('formCourseGroupId', List()).join('+');
        const categoryFormCourseId = response.get('categoryFormCourseId', List()).join('+');
        const approverLevelIndex = response.get('approverLevelIndex', '');
        const qapcRoleId = response.get('qapcRoleId', '');
        const roleId = response.get('roleId', '');
        const userId = response.get('userId', '');
        const name = response.get('userName', Map());
        const first = name.get('first', '');
        const middle = name.get('middle', '');
        const last = name.get('last', '');
        const userName = `${first}${middle}${last}`;
        const subModuleId = response.get('subModuleId', '');
        const subModuleName = response.get('subModuleName', '');
        const selectedAcademic = response.get('selectedAcademic', false);
        const institutionCalendarIds = response.get('institutionCalendarIds', List()).join('+');
        const isMultiUser = response.get('multiUser', false);
        const actionsIds = response.get('actionId', List());
        const _id = response.get('_id', '');
        const constructKey = selectedAcademic
          ? `${qapcRoleId}+${categoryFormCourseId}+${institutionCalendarIds}+${selectType}`
          : response.get('formCourseGroupId', List()).length
          ? `${qapcRoleId}+${categoryFormCourseId}+${formCourseGroupId}+${selectType}`
          : `${qapcRoleId}+${categoryFormCourseId}+${selectType}`;
        if (isMultiUser) {
          number = number.set(selectType, (number.get(selectType) || 0) + 1);
          multiUserList = multiUserList.update(selectType, List(), (data) =>
            data.push(number.get(selectType, 0) - 1)
          );
        }
        const subModuleKey = isMultiUser
          ? [number.get(selectType, 0) - 1, 'subModules', subModuleId]
          : ['subModules', subModuleId];
        assignedDetails = assignedDetails.update(constructKey, Map(), (update) => {
          if (roleId) {
            const key = isMultiUser
              ? [number.get(selectType, 0) - 1, 'roleIds', roleId]
              : ['roleIds', roleId];
            const mainKey = isMultiUser
              ? [number.get(selectType, 0) - 1, 'roleIds', roleId, 'userIds', userId]
              : ['roleIds', roleId, 'userIds', userId];
            const formatData = fromJS({ roleId, roleName: '', userIds: {} });
            update = update
              .setIn(key, formatData)
              .updateIn(mainKey, Map(), (update) => {
                update = update
                  .set('userId', userId)
                  .set('name', name)
                  .set('userName', userName)
                  .set('checked', true)
                  .set('_id', _id);
                return update;
              })
              .set('multiUser', multiUserList.get(selectType, List()))
              .set('_id', _id);
          }
          if (!roleId) {
            const key = isMultiUser
              ? [number.get(selectType, 0) - 1, 'assignUsers', userId]
              : ['assignUsers', userId];
            update = update
              .updateIn(key, Map(), (update) => {
                update = update
                  .set('userId', userId)
                  .set('name', name)
                  .set('userName', userName)
                  .set('checked', true)
                  .set('_id', _id);
                return update;
              })
              .set('multiUser', multiUserList.get(selectType, ''))
              .set('_id', _id);
          }
          update = update
            .updateIn(subModuleKey, Map(), (update) => {
              const deepActions = state.updateIn(
                ['subModules', subModuleId, 'actionsIds'],
                List(),
                (existingActions) =>
                  existingActions
                    .filter((action) => !actionsIds.includes(action.get('actionId', '')))
                    .concat(
                      List(actionsIds.map((actionId) => Map({ actionId, checked: true, _id })))
                    )
              );
              const levelActions = state.getIn(['subModules', subModuleId, 'actionsIds'], List());
              update = update
                .set('subModuleName', subModuleName)
                .set('subModuleId', subModuleId)
                .set('checked', true)
                .set('_id', _id)
                .set(
                  'actionsIds',
                  deepActions.getIn(['subModules', subModuleId, 'actionsIds'], List())
                );
              if (typeof approverLevelIndex === 'number') {
                update = update.update('levels', List(), (data) => {
                  const newLevel = `level ${approverLevelIndex + 1}`;
                  const newActionsIds = levelActions
                    .filter((action) => !actionsIds.includes(action.get('actionId', '')))
                    .merge(actionsIds.map((actionId) => Map({ actionId, checked: true, _id })));
                  const isDuplicate = data.some(
                    (item) =>
                      item.get('level') === newLevel || item.get('actionsIds').equals(newActionsIds)
                  );
                  if (!isDuplicate) {
                    return data.push(
                      fromJS({
                        level: newLevel,
                        actionsIds: newActionsIds,
                        _id,
                      })
                    );
                  }
                  return data;
                });
              }
              return update;
            })
            .set('multiUser', multiUserList.get(selectType, List()))
            .set('_id', _id);
          return update;
        });
      });
      const groupedData = organizeBySelectTypeAndUser(action.data);
      const previousData = { ...state.get('assignedUserDetails', Map()).toJS(), ...groupedData };
      return state
        .set('isLoading', false)
        .set('assignedDetails', assignedDetails)
        .set('assignedUserDetails', fromJS(previousData));
    }
    case actions.GET_ASSIGNED_USER_LIST_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.GET_ACADEMIC_YEAR_REQUEST: {
      return state;
    }
    case actions.GET_ACADEMIC_YEAR_SUCCESS: {
      // const key = action.constructKey;
      // const categoryFormId = action.categoryFormId;
      // const courseId = action.courseId;
      // const isActionLabel = action.isActionLabel;
      // const qapcRoleId = action.qapcRoleId;
      // let constructData = Map();
      // let storeCalendarId = Map();
      // fromJS(action.data).forEach((academicYear) => {
      //   const calendarName = academicYear.get('calendar_name', '');
      //   const calendarId = academicYear.get('_id', '');
      //   const concatIdName = `${courseId}+${calendarId}+${calendarName}`;
      //   storeCalendarId = storeCalendarId.update(concatIdName, List(), (data) =>
      //     data.push(calendarId)
      //   );
      //   if (constructData.size === 0) {
      //     constructData = constructData
      //       .set('childKey', 'all academic Year')
      //       .set('all academic Year', Map());
      //   } else {
      //     constructData = constructData.setIn(
      //       ['all academic Year', calendarId],
      //       fromJS({
      //         name: calendarName,
      //         courseId: courseId,
      //         isActionLabel,
      //         _id: courseId,
      //         uniqueId: `${calendarId}+${calendarName}`,
      //         subDataKey: [...key, 'all academic Year', calendarId],
      //         isAcademicYear: true,
      //         calendarId,
      //       })
      //     );
      //   }
      // });
      // return state
      //   .set('isLoading', false)
      //   .setIn(
      //     ['categorySubData', qapcRoleId, categoryFormId, ...key],
      //     state
      //       .getIn(['categorySubData', qapcRoleId, categoryFormId, ...key], Map())
      //       .merge(constructData)
      //   )
      //   .setIn(
      //     ['categorySubData', 'categoryCourseIds', qapcRoleId, categoryFormId],
      //     state
      //       .getIn(['categorySubData', 'categoryCourseIds', qapcRoleId, categoryFormId], Map())
      //       .merge(storeCalendarId)
      //   )
      //   .set('academicYears', fromJS(action.data));
      return state.set('academicYears', fromJS(action.data));
    }
    case actions.GET_ACADEMIC_YEAR_FAILURE: {
      return state.set('isLoading', false);
    }
    case actions.PUT_ROLES_PERMISSION_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.PUT_ROLES_PERMISSION_SUCCESS: {
      return state
        .set('isLoading', false)
        .set('message', 'Saved Successfully')
        .set('open', -1)
        .set('categoryData', fromJS({}))
        .set('categorySubData', fromJS({}))
        .set('assignedUserDetails', fromJS({}))
        .set('assignedDetails', fromJS({}))
        .set('assignArray', fromJS([]));
    }
    case actions.PUT_ROLES_PERMISSION_FAILURE: {
      return state.set('isLoading', false);
    }
    default:
      return state;
  }
}

function generateUniqueId(userName, subModules, actions) {
  const stringToHash = `${userName.first}-${userName.last}-${subModules
    .map((sub) => sub.subModuleId)
    .join('-')}-${actions.join('-')}`;
  let hash = 0;
  for (let i = 0; i < stringToHash.length; i++) {
    hash = (Math.imul(31, hash) + stringToHash.charCodeAt(i)) | 0;
  }
  return hash.toString();
}

function organizeBySelectTypeAndUser(dataArray) {
  const result = {};
  dataArray.forEach((item) => {
    const {
      userName,
      subModuleId,
      actionId,
      _id,
      selectType,
      userId,
      approverLevelIndex,
      uuid,
      roleId,
    } = item;
    if (!result[selectType]) {
      result[selectType] = {};
    }
    const userKey = `${userId}`;
    if (!result[selectType][userKey]) {
      result[selectType][userKey] = {
        userName,
        idArray: [],
        subModules: [],
        uniqueId: '',
        uuid,
        roleId,
      };
    }
    result[selectType][userKey].idArray.push(_id);
    const subModuleObject = { subModuleId, actionId, _id };
    if (approverLevelIndex !== undefined) {
      subModuleObject.approverLevelIndex = approverLevelIndex;
    }
    result[selectType][userKey].subModules.push(subModuleObject);
    const actions = result[selectType][userKey].subModules.flatMap(
      (subModule) => subModule.actionId
    );
    result[selectType][userKey].uniqueId = generateUniqueId(
      userName,
      result[selectType][userKey].subModules,
      actions
    );
  });
  return result;
}
