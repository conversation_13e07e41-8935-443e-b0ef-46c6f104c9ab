import React from 'react';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import PropTypes from 'prop-types';
import { useStylesFunction } from 'Modules/ProgramInput/v2/piUtil';

const AutoComplete = (props) => {
  const {
    options = [],
    onChange = () => {},
    onInputChange,
    isClearable = true,
    value = '',
    getOptionLabel,
    placeholder = '',
    widthSync = false,
    className,
    forcePopupIcon = true,
    isOptionEqualToValue,
    loading = false,
    open,
    onClose = () => {},
    renderOption,
    PaperComponent,
  } = props;
  const classes = useStylesFunction();

  return (
    <Autocomplete
      options={options}
      value={value}
      open={open}
      onClose={onClose}
      getOptionLabel={getOptionLabel}
      forcePopupIcon={forcePopupIcon}
      onChange={onChange}
      onInputChange={onInputChange}
      disableClearable={!isClearable}
      id={'autoCompletePadding'}
      className={className}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder={placeholder}
          variant={'outlined'}
          className={widthSync ? classes.inputFocusWidth : classes.inputFocus}
        />
      )}
      isOptionEqualToValue={isOptionEqualToValue}
      loading={loading}
      {...(renderOption && { renderOption })}
      {...(PaperComponent && { PaperComponent })}
    />
  );
};

AutoComplete.propTypes = {
  onChange: PropTypes.func,
  getOptionLabel: PropTypes.func,
  options: PropTypes.array,
  isClearable: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  forcePopupIcon: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  placeholder: PropTypes.string,
  onInputChange: PropTypes.func,
  widthSync: PropTypes.bool,
  className: PropTypes.string,
  isOptionEqualToValue: PropTypes.func,
  loading: PropTypes.bool,
  open: PropTypes.bool,
  onClose: PropTypes.func,
  renderOption: PropTypes.func,
  PaperComponent: PropTypes.func,
};

export default AutoComplete;
