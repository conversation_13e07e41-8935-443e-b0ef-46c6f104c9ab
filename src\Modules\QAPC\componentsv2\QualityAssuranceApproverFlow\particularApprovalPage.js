import React, { useEffect, useRef, useState } from 'react';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Checkbox,
  Divider,
} from '@mui/material';
import SchoolRoundedIcon from '@mui/icons-material/SchoolRounded';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import { AddComments, DisplayButtons } from './approvalUtils';
import document_with_focus from 'Assets/q360_dashboard/category_overview/document_with_focus.svg';
import document_with_outline from 'Assets/q360_dashboard/category_overview/document_with_outline.svg';
import {
  useBooleanHook,
  useCallApiHook,
  useRouteing,
  useSearchParams,
  // useZoomPage,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import ShowCapturedDocs from '../QualityAssuranceProcess/CategoryOverview/ShowCapturedDocs';
import Buttons from 'Widgets/FormElements/material/Button';
import { DrawerRight } from 'Modules/GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step2';
import search_by_folder from 'Assets/q360_dashboard/category_overview/search_by_folder.svg';
import ViewDocuments from '../QualityAssuranceProcess/CategoryOverview/ViewDocuments';
import guide_resource_button from 'Assets/q360_dashboard/guide_resource_button.svg';
import {
  getFormAddendum,
  getRpAction,
  setData,
  updateApproverUser,
  getIncorporate,
  getFromApplicationList,
  getApproverList,
  getSignedUrl,
} from '_reduxapi/q360/actions';
import CkEditor5 from 'Widgets/CkEditor/CkEditor';
import { selectFormAddendum } from '_reduxapi/q360/selectors';
import { useSelector } from 'react-redux';

import { fromJS, List, Map } from 'immutable';
import DeferredInput from '../QualityAssuranceProcess/CategoryOverview/DeferredInput';
import { AlphabetsArray } from 'utils';
// import AddIcon from '@mui/icons-material/Add';
// import RemoveIcon from '@mui/icons-material/Remove';
import { useChangeableEmpty, userActionsFunc, useRestrictedAction, useSelectedRedux } from '.';
import { combiningProgramAndInstitution } from '../Utils';
import { PUBLIC_PATH } from 'constants';
import ReferenceDocumentV2 from '../QualityAssuranceProcess/CategoryOverview/ReferenceDocument/ReferenceDocumentV2';
import moment from 'moment';
import CommentIcon from '@mui/icons-material/Comment';
import { ExpandMoreOutlined } from '@mui/icons-material';
const zoomIconStyle = { margin: '10px 35px gap-10', height: '100vh' };
const checkBoxSx = {
  padding: '0px !important',
  '& .MuiSvgIcon-root': {
    width: '20px',
    height: '20px',
    color: '#9CA3AF',
  },
};
export const IncorporateCard = ({
  borderColor,
  bgColor,
  text1 = 'NCAAA Course Report',
  text2 = 'Clinical Program- -AC 2022-2023',
  showIcon = false,
  fontSize = 14,
  Icon = <></>,
  reverse = '',
}) => {
  return (
    <Box
      className={`d-flex justify-content-center align-items-center px-3 py-2 ${reverse}`}
      sx={{ border: `1.5px solid ${borderColor}`, backgroundColor: bgColor, borderRadius: '4px' }}
    >
      <div className="d-flex flex-column">
        <div className={`f-${fontSize} fw-500 txt-slate-gray`}>{text1}</div>
        <div className="f-10 txt-slate-gray fw-500">{text2}</div>
      </div>
      {showIcon && <div className={`m${reverse ? 'r-1' : 'l-3'}`}>{Icon}</div>}
    </Box>
  );
};
const ParticularSection = () => {
  const [incorporates, setIncorporate] = useState(Map());
  const [
    ,
    approvalList,
    formApplication,
    levelRoleUserList,
    authDataArray,
    selectedRole,
    actions,
  ] = useSelectedRedux();
  const [Permission] = useRestrictedAction();
  const [reference, handleChange] = useBooleanHook(false);
  const [searchParams] = useSearchParams();
  const [history] = useRouteing();
  const [updateRedux] = useCallApiHook(setData);
  const [updateApprover] = useCallApiHook(updateApproverUser);
  const [incorporate] = useCallApiHook(getIncorporate);
  const [formApplications] = useCallApiHook(getFromApplicationList);
  const [approverList] = useCallApiHook(getApproverList);
  // const [zoom, handleMouseDown, handleMouseUp] = useZoomPage();
  const formAddendum = useSelector(selectFormAddendum);
  const [getFromData] = useCallApiHook(getFormAddendum);
  const [documents, setDocuments] = useState(Map());

  const captureDocs = formAddendum.get('displayCapture', List());
  const formInitiatorId = searchParams.get('formInitiatorId');
  const parentTab = searchParams.get('tab');
  const categoryId = searchParams.get('categoryId');
  const institutionCalenderId = searchParams.get('calenderId');
  const actionIds = approvalList.getIn(
    [categoryId, parentTab, formInitiatorId, 'actionIds'],
    List()
  );
  const selectFormApplication = formApplication.get(`${categoryId}+${formInitiatorId}`, Map());
  const programName = selectFormApplication.get(
    'programName',
    selectFormApplication.get('institutionName', '')
  );
  const courseName = selectFormApplication.get('courseName', '');
  const curriculumName = selectFormApplication.get('curriculumName', '');
  const categoryFormCourseId = selectFormApplication.get('categoryFormCourseId', '');
  const categoryFormGroupId = selectFormApplication.get('categoryFormGroupId', '');
  const submissionDate = selectFormApplication.get('submissionDate', '');
  const selectedGroupName = selectFormApplication.get('selectedGroupName', List());
  const level = selectFormApplication.get('level', '');
  const headerName = {
    program: programName,
    course: courseName,
    institution: programName,
  };
  const callBack = (res) => setIncorporate(fromJS(res));
  const goBack = () => history.goBack();
  const handleDeleteEvidence = (docIndex) => {
    setDocuments((prev) =>
      prev.update('formEvidenceAttachment', List(), (evidence) => {
        return evidence.filter((_, i) => i !== docIndex);
      })
    );
  };
  const payload = { formInitiatedId: formInitiatorId, categoryFormCourseId, categoryFormGroupId };
  useChangeableEmpty('', getRpAction, true);

  const [getUrl] = useCallApiHook(getSignedUrl);

  const callBackUploadPdf = (docs) => {
    const doc = docs.getIn(['pdfAttachment', 0], Map());
    const url = doc.get('url', '');
    url
      ? getUrl(url, (url) => {
          setDocuments(
            docs
              .setIn(['pdfAttachment', 0, 'signedUrl'], url)
              .setIn(['pdfAttachment', 0, 'documentType'], 'pdf')
          );
        })
      : setDocuments(fromJS(docs));
  };
  useEffect(() => {
    if (formApplication.size) incorporate(payload, callBack);
    approverList({ institutionCalenderId, categoryId });
    if (selectFormApplication.size === 0) {
      formApplications({ formInitiatorIds: [formInitiatorId], categoryTab: categoryId });
    }
  }, [formApplication]);
  useEffect(() => {
    getFromData({
      formInitiatorId: formInitiatorId,
      cb: (response) => {
        callBackUploadPdf(fromJS(response));
      },
    });
  }, []);
  return (
    <div>
      <Box className="bg-white p-3 w-100 d-flex align-items-center bottom-shadow">
        <div className="d-flex align-items-center flex-grow-1">
          <div className="ml-1">
            <ArrowBackIcon className="ml-1 cursor-pointer p-0 f-20" onClick={goBack} />
          </div>
          <div className="pl-3">
            <div className="f-24 fw-400 text-dGrey text-capitalize">{headerName[level]}</div>
            <div className="d-flex align-items-center gap-8 ta-header f-12 fw-400">
              <div className="text-gray fw-400 d-flex align-items-center">
                <SchoolRoundedIcon className="mr-1  f-14" />
                {curriculumName ? `${curriculumName} /` : ''} {programName}
                {selectedGroupName.join(',').size
                  ? `/ ${selectedGroupName.join(',')} Group`
                  : ''}{' '}
              </div>
              <div className="text-gray fw-400 d-flex align-items-center">
                <CalendarTodayIcon className="f-12 mr-1" />
                {moment(submissionDate).format('DD MMM - YYYY [at] hh:mm A')}
              </div>
            </div>
          </div>
        </div>
        <div className="d-flex align-items-center gap-15">
          <Permission accessKey="Reference Document" actionIds={actionIds}>
            <ReferenceDocumentAction reference={reference} handleChange={handleChange} />
            <Divider flexItem orientation="vertical" className="my-2 mr-1" />
          </Permission>
          <ApprovalCompound
            Permission={Permission}
            categoryTab={categoryId}
            formInitiatorId={formInitiatorId}
            updateRedux={updateRedux}
            updateApprover={updateApprover}
            formApplication={formApplication}
            levelRoleUserList={levelRoleUserList}
            authDataArray={authDataArray}
            selectedRole={selectedRole}
            actions={actions}
            actionIds={actionIds}
            academicYear={institutionCalenderId}
          />
        </div>
      </Box>
      <Permission accessKey="Incorporation" actionIds={actionIds}>
        {incorporates.get('incorporateFrom', List()).size !== 0 && (
          <div className="d-flex flex-column my-3 approval-margin-left">
            <div className="f-14 py-1 ta-header">Incorporate From *</div>
            <div className="d-flex gap-10">
              {incorporates.get('incorporateFrom', List()).map((form, index) => {
                const isLiked = form.get('isLike', false);
                return (
                  <Box
                    key={index}
                    className="d-flex  align-items-center px-3 py-2 mr-3"
                    sx={{
                      border: `1.5px solid #16A34A`,
                      backgroundColor: '#DCFCE7',
                      borderRadius: '4px',
                    }}
                  >
                    <div className="d-flex flex-column">
                      <div className="f-14">{form.get('formName', '')}</div>
                      <div className="f-10">
                        {combiningProgramAndInstitution(form) +
                          ' - ' +
                          form.get('calendar_name', '')}
                      </div>
                      <div className="f-10">Sections From: {form.get('sectionFrom', '')}</div>
                      <div className="f-10">
                        Sections To:{' '}
                        {form
                          .get('sectionTo', List())
                          .map((section) => section.get('sectionName', ''))
                          .join(', ')}
                      </div>
                    </div>
                    <div className="ml-3 mt-2">
                      <ThumbUpAltIcon className="f-16" sx={{ color: isLiked ? 'blue' : 'grey' }} />
                    </div>
                  </Box>
                );
              })}
            </div>
          </div>
        )}
        {incorporates.get('incorporateWith', List()).size !== 0 && (
          <div className="d-flex flex-column my-3 approval-margin-left">
            <div className="f-14 py-1 ta-header">Incorporate To *</div>
            <div className="d-flex gap-10">
              {incorporates.get('incorporateWith', List()).map((section, index) => {
                return (
                  <Box
                    key={index}
                    className="d-flex align-items-center px-3 py-2 mr-3"
                    sx={{
                      border: `1.5px solid #147AFC`,
                      backgroundColor: '#EFF9FB',
                      borderRadius: '4px',
                    }}
                  >
                    <div className="d-flex flex-column">
                      <div className="f-14 fw_500">{section.get('formName', '')}</div>
                      <div className="f-12">
                        {combiningProgramAndInstitution(section) +
                          ' - ' +
                          section.get('calendar_name', '')}
                      </div>
                      <div className="f-12">
                        <span className="fw_500">Sections From : </span>
                        {section.get('sectionFrom', '')}
                      </div>
                      <div className="f-12">
                        <span className="fw_500"> Sections To : </span>
                        {section
                          .get('sectionTo', List())
                          .map((section) => section.get('sectionName', ''))
                          .join(', ')}
                      </div>
                    </div>
                  </Box>
                );
              })}
            </div>
          </div>
        )}
      </Permission>

      <div className="d-flex   my-3 gap-5 w-100  flex-wrap ">
        <div className="approval-guild-resorce">
          <ViewDocuments filesData={formAddendum.getIn(['categoryFormId', 'attachments'], List())}>
            <img src={guide_resource_button} alt="guide_resource" />
          </ViewDocuments>
        </div>
        <div
          className="d-flex justify-content-end gap-5 w-100 mr-3"
          style={{ flex: 'calc(100%/1)' }}
        >
          <DrawerRight
            drawerName={'Evidence Documents'}
            handleDeleteAsProps={handleDeleteEvidence}
            filesData={documents.get('formEvidenceAttachment', List())}
          >
            <Buttons
              color="blue"
              startIcon={<img src={search_by_folder} alt="search_by_folder" />}
              sx={{ fontWeight: 'normal' }}
              className="py-2"
            >
              Evidence
            </Buttons>
          </DrawerRight>
          {/* <Permission accessKey="edit" actionIds={actionIds}> */}
          <ShowCapturedDocs
            childrenType="button"
            label="Display Capture"
            captureDocs={captureDocs}
          />
          {/* </Permission> */}
        </div>
      </div>
      <div style={zoomIconStyle} className="rounded bg-white d-flex gap-10 position-relative">
        {/* <Button
          className="box-shadow-static bg-white border-none position-absolute"
          variant="outlined"
          sx={{
            border: 'none',
            '&:hover': { boxShadow: 'none', border: 'none' },
            right: 30,
            bottom: 0,
          }}
          disableRipple
          startIcon={
            <RemoveIcon
              className="text-gray"
              onMouseDown={() => handleMouseDown('-')}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            />
          }
          endIcon={
            <AddIcon
              className="text-gray"
              onMouseDown={() => handleMouseDown('+')}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            />
          }
        >
          <span className="text-black">{String(zoom).padStart(3, '0')}</span>
        </Button> */}
        <Forms documents={documents} />
        {reference && <ReferenceDocumentV2 setShowReferenceDoc={handleChange} />}
      </div>
    </div>
  );
};

export default ParticularSection;

/*--------------------------------------UtilsCompoundStart----------------------------------------*/
const ReferenceDocumentAction = ({ reference, handleChange }) => {
  return (
    <img
      src={reference ? document_with_focus : document_with_outline}
      className="mx-1 cursor-pointer"
      alt="doc"
      width={42}
      height={42}
      onClick={handleChange}
    />
  );
};
const ApprovalCompound = ({
  actions,
  Permission,
  categoryTab,
  formInitiatorId,
  formApplication,
  levelRoleUserList,
  authDataArray,
  selectedRole,
  updateRedux,
  updateApprover,
  actionIds,
  academicYear,
}) => {
  const [searchParams] = useSearchParams();
  const parentTab = searchParams.get('tab');
  const categoryId = searchParams.get('categoryId');
  const [, approvalList] = useSelectedRedux();
  const [history] = useRouteing();
  const formInitiatorIdValue = approvalList.getIn([categoryId, parentTab], Map());
  const [approverList] = useCallApiHook(getApproverList);
  const [
    handleUpdate,
    allowToSkipping,
    isEditStatus,
    currentUserStatus,
    isLastUser,
    isPublished,
  ] = userActionsFunc({
    academicYear,
    categoryTab,
    formInitiatorId,
    formApplication,
    levelRoleUserList,
    authDataArray,
    selectedRole,
    updateRedux,
    updateApprover,
    approverList,
    formInitiatorIdValue,
    approvalList,
    parentTab,
    actionIds,
    actions,
    history,
  });
  const basicActions = ['Edit', 'View'];
  const filterActions = actions
    .filter((action) => actionIds.includes(action.get('_id', '')))
    .map((action) => action.get('actionName', ''));
  const isEditView = basicActions.every((action) => filterActions.includes(action));
  const isHistory = parentTab === 'history';
  return (
    <>
      {isEditView ? (
        <Permission accessKey="Edit" actionIds={actionIds}>
          <DisplayButtons
            handleUpdate={handleUpdate}
            toSkip={allowToSkipping}
            isPublished={isPublished}
            isEditStatus={isEditStatus}
            isLastUser={isLastUser}
            userStatus={currentUserStatus}
            buttonDisabled={false}
            isHistory={isHistory}
            reDireact={true}
          />
        </Permission>
      ) : (
        <>
          <Permission accessKey="Edit" actionIds={actionIds}>
            <DisplayButtons
              handleUpdate={handleUpdate}
              toSkip={allowToSkipping}
              isPublished={isPublished}
              isEditStatus={isEditStatus}
              isLastUser={isLastUser}
              userStatus={currentUserStatus}
              buttonDisabled={false}
              isHistory={isHistory}
              reDireact={true}
            />
          </Permission>
          <Permission accessKey="View" actionIds={actionIds}>
            <DisplayButtons
              handleUpdate={handleUpdate}
              toSkip={allowToSkipping}
              isPublished={isPublished}
              isEditStatus={isEditStatus}
              isLastUser={isLastUser}
              userStatus={currentUserStatus}
              buttonDisabled={true}
              isHistory={isHistory}
              reDireact={true}
            />
          </Permission>
        </>
      )}
    </>
  );
};
const Forms = ({ documents }) => {
  const [open, setOpen] = useState(false);
  const formAddendum = useSelector(selectFormAddendum);
  const [searchParams] = useSearchParams();
  const categoryFormType = searchParams.get('categoryFormType');
  const formType = searchParams.get('formType');
  const formName = searchParams.get('formName');
  const level = searchParams.get('level');
  const isTemplateBased = categoryFormType === 'template';
  const isSectionWise = formType === 'section';
  const iframeRef = useRef(null);
  const handleIframeLoad = () => {
    sendMessageToIframe();
  };
  const handleDialog = (value) => setOpen(value);
  const _id = documents.get('_id', '');
  const sendMessageToIframe = () => {
    const iframe = iframeRef.current;
    if (iframe) {
      iframeRef.current.contentWindow.postMessage(
        { values: formAddendum.get('formTemplate', Map()).toJS(), from: 'fromDC' },
        '*'
      );
    }
  };

  const documentType = documents.getIn(['pdfAttachment', 0, 'documentType'], '');

  const tags = documents.get('tags', List());
  const templateUrlObject = {
    course: '/templates/course-report/course_report.html',
    program: '/program-report/program_report.html',
  };
  const subUrl = templateUrlObject[level];
  const isNotInstitution = level !== 'institution';

  const PDFViewer = () => {
    return (
      <iframe
        src={documents.getIn(['pdfAttachment', 0, 'signedUrl'], '')}
        title="pdf"
        className="d-flex w-100 ht_80vh p-2"
      ></iframe>
    );
  };

  return (
    <div className="bg-grey w-100">
      <div className="from-accordion-stickey">
        {isTemplateBased && isNotInstitution && (
          <div className="position-relative mx-3">
            <Accordion elevation={0} className="mb-4 py-2">
              <AccordionSummary
                className="f-18 fw-500"
                expandIcon={<ExpandMoreOutlined className="f-20" />}
              >
                <div className="d-flex justify-content-between align-items-center w-100">
                  <div className="d-flex">
                    <div>Form Name : </div>
                    <div className="fw-500 f-16 ml-1 mt-1"> {formName}</div>
                  </div>
                  <div className="d-flex   mr-3 top-fixed">
                    <Button
                      onClick={() => handleDialog(true)}
                      sx={{ border: '1px solid #D1D5DB', color: '#147AFC' }}
                    >
                      <CommentIcon className="mr-2" color="#147AFC" />
                      Add Comment
                    </Button>
                    <AddComments _id={_id} open={open} handleDialog={handleDialog} />
                  </div>
                </div>
              </AccordionSummary>
              <AccordionDetails className="d-flex flex-column border-top">
                {documentType !== 'pdf' ? (
                  <div className="px-3">
                    <iframe
                      ref={iframeRef}
                      className="custom-scrollbar"
                      src={PUBLIC_PATH + subUrl}
                      title="Face Anomaly Report"
                      width="100%"
                      style={{ border: 'none', height: '100vh' }}
                      onLoad={handleIframeLoad}
                      id="myIframe"
                    />
                  </div>
                ) : (
                  <PDFViewer />
                )}
              </AccordionDetails>
            </Accordion>
          </div>
        )}
        {!isTemplateBased &&
          documents.get('sectionAttachments', List()).size !== 0 &&
          documents.get('sectionAttachments', List()).map((section, sectionIndex) => {
            const sectionWiseDocument = section.get('evidenceAttachment', List());
            return (
              <div className="p-3" key={sectionIndex}>
                <Accordion elevation={0} className="mb-4">
                  <AccordionSummary
                    className="f-18 fw-500 "
                    expandIcon={<ExpandMoreOutlined className="f-20" />}
                  >
                    <div className="d-flex justify-content-between align-items-center w-100">
                      {isSectionWise && (
                        <div className="d-flex mb-3 align-items-center flex-grow-1 mx-3">
                          <div className="flex-grow-1 mr-3">
                            <p className="f-12 grey_shade_1 mb-2">
                              Section {AlphabetsArray[sectionIndex]}
                            </p>
                            <DeferredInput
                              sx={{
                                '& .MuiInputBase-root': {
                                  height: '37px',
                                },
                              }}
                              initialValue={section.get('sectionName', '')}
                            />
                          </div>
                          <input
                            className="d-none"
                            multiple
                            id="individual_section_upload_form_initiator"
                            type="file"
                          />
                          {sectionWiseDocument.size > 0 && (
                            <>
                              <Divider
                                className="mr-2"
                                orientation="vertical"
                                variant="middle"
                                flexItem
                              />
                              <AttachFileIcon sx={{ fontSize: '14px' }} />
                              <span className="f-14 mr-2">{sectionWiseDocument.size}</span>
                              &nbsp;
                              <DrawerRight filesData={sectionWiseDocument}>
                                <span className="text-primary link-underline-primary f-12">
                                  View
                                </span>
                              </DrawerRight>
                            </>
                          )}
                        </div>
                      )}
                      {!isSectionWise && (
                        <div className="d-flex">
                          <div>Form Name : </div>
                          <div className="fw-500 f-16 ml-1 mt-1"> {formName}</div>
                        </div>
                      )}
                      {sectionIndex === 0 && (
                        <div className="d-flex  p-2 mr-3 top-fixed">
                          <Button
                            onClick={() => handleDialog(true)}
                            sx={{ border: '1px solid #D1D5DB', color: '#147AFC' }}
                          >
                            <CommentIcon className="mr-2" color="#147AFC" />
                            Add Comment
                          </Button>
                          <AddComments _id={_id} open={open} handleDialog={handleDialog} />
                        </div>
                      )}
                    </div>
                  </AccordionSummary>
                  <AccordionDetails className="d-flex flex-column border-top">
                    {documentType !== 'pdf' ? (
                      <div className="p-3 g-config-scrollbar custom-scroll-ck-editor ">
                        <CkEditor5 data={section.get('description', '')} isReadOnly={true} />
                      </div>
                    ) : (
                      <PDFViewer />
                    )}
                  </AccordionDetails>
                </Accordion>
              </div>
            );
          })}
      </div>
      <div className="p-3 mt-2">
        <Tags tags={tags} />
      </div>
    </div>
  );
};

const Tags = ({ tags }) => {
  const SubCompound = (props) => {
    return (
      <div className="d-flex flex-column gap-5">
        <div>
          <Checkbox sx={checkBoxSx} disableRipple defaultChecked disabled className="mx-1" />
          <span className="f-14">{props.tagName}</span>
        </div>
        <div className="d-flex ml-3 mb-3">
          {props.subTag.map((sub, index) => (
            <div key={index}>
              <Checkbox sx={checkBoxSx} disableRipple disabled className="mx-2" defaultChecked />
              <span className="f-14">{sub}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };
  const defaultTags = tags.filter((tag) => tag.get('isConfigured', false));
  const selectTags = tags.filter((tag) => !tag.get('isConfigured', false));
  return (
    <Accordion elevation={0} className="mb-4 py-2">
      <AccordionSummary
        className="f-18 fw-500"
        expandIcon={<ExpandMoreOutlined className="f-20" />}
      >
        Q360 - Tags
      </AccordionSummary>
      <AccordionDetails className="d-flex flex-column border-top">
        {defaultTags.size > 0 && <div className="mb-1 f-15">Default Tags</div>}
        {defaultTags.map((tag, index) => {
          const isConfig = tag.get('isConfigured', false);
          const tagName = tag.get('name', '');
          const subTag = tag.get('subTag', List());
          if (isConfig) return <SubCompound key={index} tagName={tagName} subTag={subTag} />;
        })}
        {selectTags.size > 0 && <div className="mb-1 f-15">Select Tags</div>}
        {selectTags.map((tag, index) => {
          const isConfig = tag.get('isConfigured', false);
          const tagName = tag.get('name', '');
          const subTag = tag.get('subTag', List());
          if (!isConfig) return <SubCompound key={index} tagName={tagName} subTag={subTag} />;
        })}
        {selectTags.size < 1 && defaultTags.size < 1 && (
          <div className="text-center py-3 text-grey">No Data !</div>
        )}
      </AccordionDetails>
    </Accordion>
  );
};
/*--------------------------------------UtilsCompoundEnd------------------------------------------*/
