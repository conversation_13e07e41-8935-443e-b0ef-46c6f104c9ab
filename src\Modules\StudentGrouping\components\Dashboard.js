import React, { Component, Suspense } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { List, Map } from 'immutable';
import PropTypes, { oneOfType } from 'prop-types';
import * as actions from '../../../_reduxapi/student/action';
import {
  selectIsLoading,
  selectPrograms,
  selectActiveProgram,
  selectInstitutionCalendar,
  //selectActiveAcademicYear,
  selectDashboard,
  selectCourseGroup,
  selectCourseGroupDetails,
  selectActiveGender,
  selectActiveLevel,
} from '../../../_reduxapi/student/selectors';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import Loader from '../../../Widgets/Loader/Loader';
import { Button, Modal } from 'react-bootstrap';
import Input from '../../../Widgets/FormElements/Input/Input';
import '../../../Assets/css/grouping.css';
import ProgramTabs from './ProgramTabs';
import ProgramHeader from './ProgramHeader';
import Course from './Course';
import CourseGroupDetailsModal from '../Modal/CourseGroupDetailsModal';
import { selectUserId } from '../../../_reduxapi/Common/Selectors';
import { CheckPermission, CheckProgramDepartment } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { isGenderMerge } from 'utils';

const FoundationGroupModal = React.lazy(() => import('../Modal/GroupModal'));
const ImportStudentsFromFile = React.lazy(() => import('./importStudentsFromFile'));

const nationalinput = [
  ['Regular ', 'Regular term '],
  ['Interim ', 'Interim term'],
];

const Levels = [
  ['Level 1', 'Level 1'],
  ['Level 2', 'Level 2'],
];
const Gender = [
  ['Male', 'Male'],
  ['Female', 'Female'],
];

class Dashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      exportShow: false,
      foundationGroup: false,
      year: [
        { name: '', value: '' },
        { name: '1', value: '1' },
        { name: '2', value: '2' },
      ],
      level: [
        { name: '', value: '' },
        { name: '1', value: '1' },
        { name: '2', value: '2' },
      ],
      selectNational: nationalinput[0][1],
      selectLevel: Levels[0][1],
      selectGender: Gender[0][1],
      activeYear: Map(),
      activeTerm: Map(),
      selectedGroups: Map(),
      showCourseGroupDetailsModal: false,
      activeCourseGroupDetailsKey: '',
      isFoundation: false,
      programIndex: 0,
      calendarIndex: 0,
      importshow: false,
      initialLoad: true,
    };
    this.onProgramChange = this.onProgramChange.bind(this);
  }

  componentDidMount() {
    const { resetCourseData, setActiveGender } = this.props;
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let programInd = params.get('pid') || 0;
    //let calInd = params.get('cid') || 0;
    this.interval = setInterval(() => {
      const { activeInstitutionCalendar } = this.props;
      if (!activeInstitutionCalendar.isEmpty()) {
        this.props.getPrograms(programInd, activeInstitutionCalendar, CheckProgramDepartment);
        clearInterval(this.interval);
      }
    }, 500);
    setActiveGender(isGenderMerge() ? 'both' : 'male');
    resetCourseData();
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  componentDidUpdate(prevProps) {
    const { activeInstitutionCalendar, activeProgram } = this.props;
    if (activeProgram !== prevProps.activeProgram) {
      this.setState({ activeYear: Map(), activeTerm: Map(), activeLevel: Map() });
    }
    if (
      activeInstitutionCalendar !== prevProps.activeInstitutionCalendar &&
      !activeProgram.isEmpty()
    ) {
      this.props.getDashboardData(activeProgram, activeInstitutionCalendar);
      this.setState({
        activeYear: Map(),
        activeTerm: Map(),
      });
    }
  }

  static getDerivedStateFromProps(props, state) {
    if (props.dashboard.isEmpty() || props.activeProgram.isEmpty()) return null;
    if (state.activeYear.isEmpty() && state.activeTerm.isEmpty()) {
      let activeYear = props.dashboard.get('year').get(0);
      let search = window.location.search;
      let params = new URLSearchParams(search);
      if (state.initialLoad) {
        let year = params.get('year') || 0;
        if (year !== 0) {
          const changedYear = props.dashboard
            .get('year', List())
            .filter((item) => item.get('year') === year);
          if (changedYear.size > 0) {
            activeYear = changedYear.get(0);
          }
        }
      }
      const firstIndexTerm = props.dashboard.getIn(['term', 0, 'term_name'], '');
      const filteredByTerm = activeYear
        .get('batch', List())
        .filter(
          (eachBatch) => eachBatch.get('term', '').toLowerCase() === firstIndexTerm.toLowerCase()
        )
        .getIn([0, 'level'], List());
      const activeTerm = Map({
        term: filteredByTerm.getIn([0, 'term'], firstIndexTerm),
        data: filteredByTerm,
      });
      const isFoundation =
        props.activeProgram.get('program_type') === 'pre-requisite' ? true : false;
      props.setActiveYear(activeYear);
      props.setActiveTerm(activeTerm);
      let programIndex = params.get('pid') || 0;
      let calendarIndex = params.get('cid') || 0;
      return {
        activeYear,
        activeTerm,
        isFoundation,
        initialLoad: false,
        programIndex,
        calendarIndex,
      };
    }
    return null;
  }

  setCommonProps = (term) => {
    this.props.setActiveLevel(term);
  };

  foundationGroup = (term) => {
    if (!this.state.foundationGroup) {
      this.setCommonProps(term);
    }
    this.setState({
      foundationGroup: !this.state.foundationGroup,
    });
  };

  exportShow = () => {
    this.setState({
      exportShow: !this.state.exportShow,
    });
  };

  handleSelect = (e, name) => {
    e.preventDefault();

    if (name === 'nationalinput') {
      this.setState({
        selectNational: e.target.value,
        selectNationalError: '',
      });
    }
    if (name === 'selectLevel') {
      this.setState({
        selectLevel: e.target.value,
      });
    }
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
      });
    }
  };

  onProgramChange(program, index) {
    const { history, activeInstitutionCalendar } = this.props;
    const { calendarIndex } = this.state;
    const isFoundation = program.get('program_type') === 'pre-requisite' ? true : false;
    this.setState({
      activeYear: Map(),
      activeTerm: Map(),
      selectedGroups: Map(),
      isFoundation: isFoundation,
      programIndex: index,
    });
    this.props.getDashboardData(program, activeInstitutionCalendar);
    history.push(`dashboard?pid=${index}&cid=${calendarIndex}`);
  }

  onTermChange(term) {
    const { activeYear } = this.state;
    const filteredByTerm = activeYear
      .get('batch', List())
      .filter((eachBatch) => eachBatch.get('term', '').toLowerCase() === term.toLowerCase())
      .getIn([0, 'level']);
    const activeTerm = Map({ term: filteredByTerm.getIn([0, 'term'], term), data: filteredByTerm });
    this.setState({
      activeTerm: activeTerm,
    });
    this.props.setActiveYear(activeYear);
    this.props.setActiveTerm(activeTerm);
    this.props.setActiveLevel(Map());
  }

  onYearChange(year) {
    const { dashboard } = this.props;
    const activeYear = dashboard.get('year').find((y) => y.get('year') === year);
    const term = this.state.activeTerm.get('term', '');
    const filteredByTerm = activeYear
      .get('batch', List())
      .filter((eachBatch) => eachBatch.get('term', '').toLowerCase() === term.toLowerCase())
      .getIn([0, 'level'], List());

    const activeTerm = Map({ term, data: filteredByTerm });
    this.setState({ activeYear, activeTerm });
    this.props.setActiveYear(activeYear);
    this.props.setActiveTerm(activeTerm);
  }

  onLevelChange(level) {
    const { activeTerm } = this.state;
    const { setActiveLevel } = this.props;
    const selectActiveLevel = activeTerm.get('data', List()).find((y) => y.get('level') === level);
    setActiveLevel(selectActiveLevel);
  }

  // onAcademicYearChange(operation) {
  //   const { institutionCalendar, activeAcademicYear, activeProgram, history } = this.props;
  //   const { programIndex } = this.state;
  //   if (this.disableNextPreviousAcademicYear(operation)) {
  //     return;
  //   }
  //   const activeAcademicYearPosition = institutionCalendar.findIndex(
  //     (c) => c.get('_id') === activeAcademicYear.get('_id')
  //   );
  //   const newAcademicYearPosition =
  //     operation === 'previous' ? activeAcademicYearPosition + 1 : activeAcademicYearPosition - 1;
  //   const modifiedAcademicYear = institutionCalendar.get(newAcademicYearPosition);
  //   this.setState({
  //     activeYear: Map(),
  //     activeTerm: Map(),
  //   });
  //   this.props.getDashboardData(activeProgram, modifiedAcademicYear);
  //   this.setState({ calendarIndex: newAcademicYearPosition });
  //   history.push(`dashboard?pid=${programIndex}&cid=${newAcademicYearPosition}`);
  // }

  // disableNextPreviousAcademicYear(operation) {
  //   const { institutionCalendar, activeAcademicYear } = this.props;
  //   if ([0, 1].includes(institutionCalendar.count())) {
  //     return true;
  //   }
  //   operation = operation === 'previous' ? institutionCalendar.count() - 1 : 0;
  //   const activeAcademicYearPosition = institutionCalendar.findIndex(
  //     (c) => c.get('_id') === activeAcademicYear.get('_id')
  //   );
  //   return activeAcademicYearPosition === -1 || activeAcademicYearPosition === operation;
  // }

  onCourseClick(data) {
    if (!data.get('open')) return;

    const { activeYear, activeTerm } = this.state;
    const { courseGroup } = this.props;
    const eventKeyValue = data.get('eventKey').split('-');
    const courseId = eventKeyValue[0];
    const levelName = eventKeyValue[1];
    if (courseGroup.has(eventKeyValue)) return;
    const yearId = activeYear.get('_id');
    const filterTerm = activeTerm.get('data').filter((item) => item.get('level', '') === levelName);
    const term = filterTerm.getIn([0, 'term'], activeTerm.get('term'));
    let level;
    activeTerm
      .get('data')
      .filter((item) => item.get('level', '') === levelName)
      .forEach((t) => {
        const matchedCourse = t.get('courses').find((c) => c.get('_course_id') === courseId);
        if (matchedCourse) {
          level = t.get('level');
        }
      });
    this.props.getCourseGroupList(yearId, level, term, courseId);
  }

  onCourseGroupChange(courseId, gender, groupName) {
    this.setState((state) => {
      return {
        selectedGroups: state.selectedGroups.set(courseId, Map({ [gender]: groupName })),
      };
    });
  }

  onImportchange(status) {
    if (status) {
      const { setActiveLevel } = this.props;
      setActiveLevel(Map());
    }
    this.setState({ importshow: status });
  }

  onCourseGroupClick(data) {
    const { activeYear, activeTerm, selectedGroups } = this.state;
    const { courseGroupDetails } = this.props;
    const yearId = activeYear.get('_id');
    const courseId = data.get('courseId');
    const levelName = data.get('level');
    const gender = data.get('gender');
    const deliverySymbol = data.get('deliverySymbol');
    const deliveryGroupNumber = data.get('deliveryGroupNumber');
    const isFoundation = data.get('isFoundation');
    const isRotation = data.get('isRotation');
    const groupName = selectedGroups.get(courseId, Map({ [gender]: 0 })).get(gender);
    let level;
    const filterTerm = activeTerm.get('data').filter((item) => item.get('level', '') === levelName);
    const term = filterTerm.getIn([0, 'term'], activeTerm.get('term'));
    activeTerm
      .get('data')
      .filter((item) => item.get('level', '') === levelName)
      .forEach((t) => {
        const matchedCourse = t.get('courses').find((c) => c.get('_course_id') === courseId);
        if (matchedCourse) {
          level = t.get('level');
        }
      });
    const courseGroupDetailsKey = `${yearId}:${level}:${term}:${courseId}:${groupName}:${gender}:${deliverySymbol}:${deliveryGroupNumber}`;
    this.setState({
      showCourseGroupDetailsModal: true,
      activeCourseGroupDetailsKey: courseGroupDetailsKey,
    });
    if (courseGroupDetails.has(courseGroupDetailsKey)) {
      return;
    }
    this.props.getCourseGroupDetails(
      yearId,
      level,
      term,
      courseId,
      Number(groupName) + 1,
      gender,
      deliverySymbol,
      deliveryGroupNumber,
      isFoundation || isRotation
    );
  }

  closeCourseGroupDetailsModal() {
    this.setState({
      showCourseGroupDetailsModal: false,
      activeCourseGroupDetailsKey: '',
    });
  }

  render() {
    const {
      activeYear,
      activeTerm,
      selectedGroups,
      isFoundation,
      programIndex,
      calendarIndex,
      showCourseGroupDetailsModal,
      activeCourseGroupDetailsKey,
    } = this.state;
    const {
      programs,
      activeProgram,
      dashboard,
      //activeAcademicYear,
      courseGroup,
      activeLevel,
      courseGroupDetails,
      activeGender,
      activeInstitutionCalendar,
    } = this.props;
    const header = [
      // "Username",
      'S.No',
      'Academic no',
      'Student Name',
    ];
    return (
      <>
        <Loader isLoading={this.props.isLoading} />
        <ProgramTabs
          programs={programs}
          activeProgram={activeProgram}
          onProgramChange={this.onProgramChange}
        />
        <div className="main pt-3 pb-5 bg-white">
          <div className="container">
            <ProgramHeader
              activeProgram={activeProgram}
              //activeAcademicYear={activeAcademicYear}
              activeYear={activeYear}
              activeTerm={activeTerm}
              dashboard={dashboard}
              onYearChange={this.onYearChange.bind(this)}
              onTermChange={this.onTermChange.bind(this)}
              onLevelChange={this.onLevelChange.bind(this)}
              //onAcademicYearChange={this.onAcademicYearChange.bind(this)}
              //disableNextPreviousAcademicYear={this.disableNextPreviousAcademicYear.bind(this)}
              onImportChange={this.onImportchange.bind(this)}
              isFoundation={isFoundation}
              isRotation={activeLevel?.includes('yes')}
              activeInstitutionCalendar={activeInstitutionCalendar}
            />
            {CheckPermission('pages', 'Student Grouping', 'Dashboard', 'Course View') && (
              <Course
                activeTerm={activeTerm}
                courseGroup={courseGroup}
                selectedGroups={selectedGroups}
                foundationGroup={this.foundationGroup}
                onCourseClick={this.onCourseClick.bind(this)}
                onCourseGroupChange={this.onCourseGroupChange.bind(this)}
                onCourseGroupClick={this.onCourseGroupClick.bind(this)}
                isFoundation={isFoundation}
                activeProgramIndex={programIndex}
                activeCalendarIndex={calendarIndex}
                activeYear={activeYear.get('year', 2)}
                setCommonProps={this.setCommonProps}
                activeProgram={activeProgram}
              />
            )}
            {dashboard.isEmpty() && (
              <div className="placeholder-message course-placeholder-message">
                <h1>
                  <Trans i18nKey={'student_grouping.no_course'} />
                </h1>
                <div>
                  <Trans i18nKey={'student_grouping.unpublished_program'} />
                </div>
              </div>
            )}

            {this.state.importshow && (
              <Suspense fallback="">
                <ImportStudentsFromFile
                  closed={this.onImportchange.bind(this)}
                  isFoundation={isFoundation}
                  dashboard={dashboard}
                  onYearChange={this.onYearChange.bind(this)}
                  onTermChange={this.onTermChange.bind(this)}
                  onLevelChange={this.onLevelChange.bind(this)}
                  activeProgramIndex={programIndex}
                  activeCalendarIndex={calendarIndex}
                  isRotation={activeLevel?.includes('yes')}
                />
              </Suspense>
            )}
            <div className="clearfix"> </div>
          </div>
        </div>

        <CourseGroupDetailsModal
          show={showCourseGroupDetailsModal}
          onClose={this.closeCourseGroupDetailsModal.bind(this)}
          header={header}
          courseGroupDetails={courseGroupDetails.get(activeCourseGroupDetailsKey, Map())}
          activeAcademicYear={activeInstitutionCalendar.get('calendar_name', null)}
          activeProgram={activeProgram}
        />

        {/* Foundation group model start */}
        {this.state.foundationGroup === true && (
          <Suspense fallback="">
            <FoundationGroupModal
              activeYearId={activeYear.get('_id')}
              ModalClicked={this.foundationGroup}
              header={header}
              level={this.state.level}
              selectedLevel={this.state.selectedLevel}
              levelError={this.state.levelError}
              activeProgramIndex={programIndex}
              activeCalendarIndex={calendarIndex}
              isFoundation={isFoundation}
              isRotation={activeLevel.includes('yes')}
              activeYear={activeYear.get('year', 2)}
              activeTerm={activeTerm.get('term', 'regular')}
              activeLevel={activeLevel.get('level', 3)}
              activeGender={activeGender}
              courses={activeLevel?.get('courses', [])}
              activeProgram={activeProgram}
            />
          </Suspense>
        )}
        {/* Foundation group model end */}

        {/* export group model start */}

        <Modal show={this.state.exportShow} onHide={this.exportShow} size="lg">
          <Modal.Body>
            <div className="dash-table">
              <p>
                <Trans i18nKey={'student_grouping.export'} />
              </p>
              <div className="row">
                <div className="col-md-6">
                  <div className="row">
                    <div className="col-md-3">
                      <p className="pt-1">
                        {' '}
                        <Trans i18nKey={'student_grouping.term'} />{' '}
                      </p>
                    </div>

                    <div className="col-md-9">
                      <Input
                        elementType={'radio'}
                        elementConfig={nationalinput}
                        className={'form-radio1'}
                        selected={this.state.selectNational}
                        labelclass="radio-label2"
                        onChange={(e) => this.handleSelect(e, 'nationalinput')}
                        feedback={this.state.selectNationalError}
                      />
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-3">
                      <p className="pt-1">
                        {' '}
                        <Trans i18nKey={'student_grouping.level'} />{' '}
                      </p>
                    </div>

                    <div className="col-md-9">
                      <Input
                        elementType={'radio'}
                        elementConfig={Levels}
                        className={'form-radio1'}
                        selected={this.state.selectLevel}
                        labelclass="radio-label2"
                        onChange={(e) => this.handleSelect(e, 'selectLevel')}
                      />
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-3">
                      <p className="pt-1">
                        {' '}
                        <Trans i18nKey={'student_grouping.gender'} />{' '}
                      </p>
                    </div>

                    <div className="col-md-9">
                      <Input
                        elementType={'radio'}
                        elementConfig={Gender}
                        className={'form-radio1'}
                        selected={this.state.selectGender}
                        labelclass="radio-label2"
                        onChange={(e) => this.handleSelect(e, 'selectGender')}
                      />
                    </div>
                  </div>
                </div>

                <div className="col-md-6 border-left"></div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="outline-dark" onClick={this.exportShow}>
              <Trans i18nKey={'student_grouping.close'} />
            </Button>
            <Button variant="primary" onClick={this.exportShow}>
              <Trans i18nKey={'student_grouping.export'} />
            </Button>
          </Modal.Footer>
        </Modal>

        {/* export model end */}
      </>
    );
  }
}

Dashboard.propTypes = {
  isLoading: PropTypes.bool,
  programs: PropTypes.instanceOf(List),
  activeLevel: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  institutionCalendar: PropTypes.instanceOf(List),
  activeProgram: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  activeAcademicYear: PropTypes.instanceOf(Map),
  dashboard: PropTypes.instanceOf(Map),
  courseGroup: PropTypes.instanceOf(Map),
  courseGroupDetails: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  getDashboardData: PropTypes.func,
  getPrograms: PropTypes.func,
  getCourseGroupList: PropTypes.func,
  getCourseGroupDetails: PropTypes.func,
  setActiveYear: PropTypes.func,
  setActiveTerm: PropTypes.func,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  resetCourseData: PropTypes.func,
  setActiveLevel: PropTypes.func,
  activeGender: PropTypes.string,
  setActiveGender: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    programs: selectPrograms(state),
    activeProgram: selectActiveProgram(state),
    institutionCalendar: selectInstitutionCalendar(state),
    //activeAcademicYear: selectActiveAcademicYear(state),
    activeLevel: selectActiveLevel(state),
    dashboard: selectDashboard(state),
    courseGroup: selectCourseGroup(state),
    courseGroupDetails: selectCourseGroupDetails(state),
    currentUserId: selectUserId(state),
    activeGender: selectActiveGender(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(Dashboard);
