import React, { Fragment } from 'react';
import styled from 'styled-components';
import { connect } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';
import { find_sun_to_sat } from '../../../_utils/function';
import useDataFromStore from '../UtilityComponents/useDataFromStore';

const LevelHeadingWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-items: center;
  position: relative;
`;

const LevelIndicator = styled.div`
  width: 40px;
  height: ${(props) => props.len};
  margin-left: 35px;
  margin-top: ${(props) => props.top};
  background-color: #f3f3f3;
  border-radius: 16px 0px 0px 16px;
  position: relative;
  font-size: 24px;
  line-height: 24px;
  letter-spacing: 4.75px;
  &.no_data {
    height: 700px;
    margin: 50px 0 50px 35px;
  }
  &.design {
    background-color: #7145cd;
  }
`;

const LevelIndicatorTitle = styled.div`
  white-space: nowrap;
  transform: rotate(270deg);
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 500;
  margin: ${(props) => props.len} 0 0 0;
  letter-spacing: 5px;
  color: rgba(0, 0, 0, 0.2);
  &.no_data {
    margin: 350px 0 0 0;
  }

  &.design {
    color: white;
  }
`;

const Level = (props) => {
  const { is_interim, choose } = props;
  const match = useRouteMatch();
  const active = match.params.year || 'year2';
  const sem = match.params.sem || 'semester1';

  let end, apply1, apply2, start1, end1;

  const {
    start,
    rotational_course,
    rotational_course1,
    course1,
    course2,
    level_one,
    level_two,
  } = useDataFromStore();

  if (is_interim) {
    let check_run, check_run1;

    start1 = props[active]?.['interim_data']?.[sem]?.[1]?.['start_date'];
    end = props[active]?.['interim_data']?.[sem]?.[0]?.['end_date'];
    end1 = props[active]?.['interim_data']?.[sem]?.[1]?.['end_date'];

    check_run =
      (rotational_course && rotational_course?.length !== 0) || (course1 && course1 !== 0);
    check_run1 =
      (rotational_course1 && rotational_course1?.length !== 0) || (course2 && course2 !== 0);

    apply1 = check_run ? 'design' : '';
    apply2 = check_run1 ? 'design' : '';
  } else {
    start1 = props[active]?.['raw_data']?.['level']?.[1]?.['start_date'];
    end = props[active]?.['raw_data']?.['level']?.[0]?.['end_date'];
    end1 = props[active]?.['raw_data']?.['level']?.[1]?.['end_date'];

    apply1 =
      (rotational_course && rotational_course !== 0) || (course1 && course1 !== 0) ? 'design' : '';
    apply2 = course2 && course2 !== 0 ? 'design' : '';
  }

  const common_start = choose === 0 ? start : start1;
  const common_end = choose === 0 ? end : end1;
  const common_apply1 = choose === 0 ? apply1 : apply2;
  const common_level_one = choose === 0 ? level_one : level_two;
  const level_name = choose === 0 ? '(Regular)' : '(Interim)';

  const level_one_length = [...find_sun_to_sat(common_start, common_end)].length;

  const level_two_cal = () => {
    if (!is_interim && start1) {
      return [...find_sun_to_sat(start1, end1)].length;
    }
    return 0;
  };

  const level_two_length = level_two_cal();

  const acad_length = [...find_sun_to_sat(common_start, end1)].length;
  const extra_space = acad_length - level_one_length - level_two_length;

  return (
    <Fragment>
      <LevelHeadingWrapper>
        <LevelIndicator
          className={level_one_length ? common_apply1 : 'no_data'}
          len={`${level_one_length * 50}px`}
        >
          <LevelIndicatorTitle
            className={level_one_length ? common_apply1 : 'no_data'}
            len={`${(level_one_length * 50) / 2}px`}
          >
            {common_level_one ? `${common_level_one} ${is_interim ? level_name : ''}` : 'Level'}
          </LevelIndicatorTitle>{' '}
        </LevelIndicator>
        <Fragment>
          {!is_interim && props[active]?.['level_two_show'] ? (
            <LevelIndicator
              className={level_two_length ? apply2 : 'no_data'}
              len={`${level_two_length * 50}px`}
              top={`${extra_space ? extra_space * 50 : 0}px`}
            >
              <LevelIndicatorTitle
                className={level_two_length ? apply2 : 'no_data'}
                len={`${(level_two_length * 50) / 2}px`}
              >
                {level_two ? `${level_two}` : 'Level'}
              </LevelIndicatorTitle>
            </LevelIndicator>
          ) : null}
        </Fragment>
      </LevelHeadingWrapper>
    </Fragment>
  );
};

const mapStateToProps = ({ calender }) => ({
  // active: calender.active_year,
  // start: calender.academic_year_start,
  // end: calender.academic_year_end,
  is_interim: calender.interim,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
});

export default connect(mapStateToProps)(Level);
