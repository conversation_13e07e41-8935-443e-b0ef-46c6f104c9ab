import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  FormControl,
  IconButton,
  ListSubheader,
  MenuItem,
  MenuList,
  Popover,
  Select,
  Typography,
} from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { List } from 'immutable';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import MButton from 'Widgets/FormElements/material/Button';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import {
  getScheduledOptions,
  ScheduledSelectionStatus,
  ScheduleLabel,
  ScheduleStatusIcon,
  SecondaryText,
} from './utils';

export const customSelectBtnSx = {
  padding: '7px 8px 7px 15px',
  textTransform: 'none !important',
  color: '#374151',
  borderRadius: '8px',
  borderColor: '#D1D5DB',
  '&:hover': {
    borderColor: 'rgba(0, 0, 0, 0.87)',
    backgroundColor: 'transparent',
  },
};
const customSearchSx = {
  '& .MuiOutlinedInput-notchedOutline': {
    borderRadius: '8px',
  },
};
const toggleBtnSx = {
  padding: '4px 12px',
  color: '#4B5563',
  fontSize: '12px',
  fontWeight: 400,
  border: '1px solid #D1D5DB',
  lineHeight: 'normal',
  textTransform: 'none !important',
};
const menuItemSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '4px 12px !important',
  minHeight: '48px !important',
  '&:hover': {
    backgroundColor: '#F3F4F6',
  },
};
export const muiSelectSx = {
  '& .MuiSelect-select': {
    color: '#374151',
    '&.Mui-disabled': {
      WebkitTextFillColor: '#9CA3AF',
    },
  },
  '& .MuiSelect-iconOutlined': {
    color: '#374151',
    right: '8px !important',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#D1D5DB',
    borderRadius: '8px',
  },
  '&.Mui-disabled': {
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: '#E5E7EB',
    },
    '& .MuiSelect-iconOutlined': {
      color: '#9CA3AF',
    },
  },
};

const menuProps = {
  PaperProps: {
    sx: {
      maxHeight: 250,
    },
  },
};

export const getFilteredOptions = ({ options, search, showScheduled }) => {
  if (!search && !showScheduled) return options;

  const searchValue = search.toLowerCase();
  return options.filter(
    (option) =>
      (!search || option.get('name', '').toLowerCase().includes(searchValue)) &&
      (!showScheduled || option.get('scheduled'))
  );
};

export const CustomSearch = ({ placeholder, value, changed, autoFocus = false }) => {
  return (
    <MaterialInput
      elementType="materialInput"
      type="text"
      variant="outlined"
      size="small"
      placeholder={placeholder}
      inputAdornment={
        value !== '' ? (
          <IconButton size="small" edge="end" onClick={() => changed('')}>
            <CloseIcon color="action" sx={{ fontSize: '16px' }} />
          </IconButton>
        ) : (
          <SearchIcon fontSize="small" color="action" />
        )
      }
      value={value}
      changed={(e) => changed(e.target.value)}
      sx={customSearchSx}
      elementConfig={{ autoFocus }}
    />
  );
};
CustomSearch.propTypes = {
  placeholder: PropTypes.string,
  value: PropTypes.string,
  changed: PropTypes.func,
  autoFocus: PropTypes.bool,
};

export const ToggleButton = ({ placeholder, showScheduled, handleClick }) => {
  return (
    <MButton
      variant="outlined"
      color="inherit"
      sx={{ ...toggleBtnSx, ...(showScheduled && { backgroundColor: '#E5E7EB' }) }}
      clicked={handleClick}
      fullWidth
    >
      {placeholder}
      {showScheduled && <CloseIcon sx={{ fontSize: 16, ml: '4px' }} />}
    </MButton>
  );
};
ToggleButton.propTypes = {
  placeholder: PropTypes.string,
  showScheduled: PropTypes.bool,
  handleClick: PropTypes.func,
};

export const MuiSelect = ({ placeholder, options, value, changed, disabled, renderValue }) => {
  return (
    <FormControl size="small" fullWidth>
      <Select
        value={value}
        onChange={changed}
        disabled={disabled}
        sx={muiSelectSx}
        MenuProps={menuProps}
        renderValue={renderValue}
        displayEmpty
      >
        {placeholder && (
          <MenuItem value="" disabled hidden>
            {placeholder}
          </MenuItem>
        )}
        {options.map((option) => {
          const value = option.get('value', '');
          const name = option.get('name', '');
          return (
            <MenuItem key={value} value={value}>
              {name}
            </MenuItem>
          );
        })}
      </Select>
    </FormControl>
  );
};
MuiSelect.propTypes = {
  placeholder: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  options: PropTypes.instanceOf(List),
  value: PropTypes.string,
  changed: PropTypes.func,
  disabled: PropTypes.bool,
  renderValue: PropTypes.string,
};

const CustomSelect = ({
  label,
  options,
  value,
  onChange,
  searchPlaceholder,
  toggleBtnText,
  showDuration,
  disabled,
  showScheduledStatus = false,
  emptyErrorMessage,
  showScheduledSelection = false,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [paperWidth, setPaperWidth] = useState(null);
  const [search, setSearch] = useState('');
  const [showScheduled, setShowScheduled] = useState(false);
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  useEffect(() => {
    if (!open) {
      setShowScheduled(false);
      setSearch('');
    } else setPaperWidth(anchorEl.offsetWidth);
  }, [open]);

  const scheduledOptions = useMemo(() => {
    if (!showScheduledSelection) return List();
    return getScheduledOptions(options, value);
  }, [showScheduledSelection, options, value]);

  const filteredOptions = useMemo(() => {
    return getFilteredOptions({ options, search, showScheduled });
  }, [options, search, showScheduled]);

  const renderValue = useMemo(() => {
    if (!value) return '- Select -';

    const selectedOption = options.find((option) => option.get('value') === value);
    return selectedOption?.get('name') || '';
  }, [options, value]);

  const handleClick = (e) => setAnchorEl(e.currentTarget);

  const handleClose = () => setAnchorEl(null);

  const handleChange = (val) => {
    if (val !== value) onChange(val);
    handleClose();
  };

  return (
    <Box display="flex" alignItems="end" gap={0.5}>
      <Box flexGrow={1} maxWidth="100%">
        <ScheduleLabel label={label} />
        <MButton
          variant="outlined"
          clicked={handleClick}
          sx={customSelectBtnSx}
          disabled={disabled}
          fullWidth
        >
          <div className="d-flex align-items-center justify-content-between w-100">
            <Typography whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
              {renderValue}
            </Typography>
            {open ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
          </div>
        </MButton>
        <Popover
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
          slotProps={{ paper: { sx: { maxHeight: 330, minWidth: 360, width: paperWidth } } }}
        >
          <ListSubheader sx={{ padding: '8px 12px 0', lineHeight: 'normal' }}>
            <CustomSearch
              placeholder={searchPlaceholder}
              value={search}
              changed={setSearch}
              autoFocus
            />
          </ListSubheader>
          {showScheduledStatus && (
            <ListSubheader sx={{ padding: '0 12px 5px', lineHeight: 'normal' }} disableSticky>
              <ToggleButton
                placeholder={toggleBtnText}
                showScheduled={showScheduled}
                handleClick={() => setShowScheduled(!showScheduled)}
              />
            </ListSubheader>
          )}
          {filteredOptions.size ? (
            <MenuList sx={{ pt: 0 }}>
              {filteredOptions.map((option, index) => {
                const optionValue = option.get('value', '');
                const secondaryText = option.get('secondaryText');
                return (
                  <MenuItem
                    key={index}
                    tabIndex={0}
                    onClick={() => handleChange(optionValue)}
                    selected={optionValue === value}
                    sx={menuItemSx}
                  >
                    <Box>
                      <Typography fontSize={14} color="#4B5563">
                        {getShortString(option.get('name', ''), 45)}
                      </Typography>

                      {secondaryText && <SecondaryText text={secondaryText} />}
                    </Box>

                    {(showDuration || showScheduledStatus) && (
                      <Box display="flex" alignItems="center" gap="8px">
                        {showDuration && <SecondaryText text={option.get('duration')} />}
                        {showScheduledStatus && (
                          <ScheduleStatusIcon
                            scheduled={option.get('scheduled')}
                            errorInfo={option.get('errorInfo')}
                          />
                        )}
                      </Box>
                    )}
                  </MenuItem>
                );
              })}
            </MenuList>
          ) : (
            <ListSubheader sx={{ color: '#9CA3AF' }} disableSticky>
              {emptyErrorMessage}
            </ListSubheader>
          )}
        </Popover>
      </Box>

      {!scheduledOptions.isEmpty() && <ScheduledSelectionStatus scheduledList={scheduledOptions} />}
    </Box>
  );
};
CustomSelect.propTypes = {
  label: PropTypes.string,
  options: PropTypes.instanceOf(List),
  value: PropTypes.string,
  onChange: PropTypes.func,
  searchPlaceholder: PropTypes.string,
  toggleBtnText: PropTypes.string,
  showDuration: PropTypes.bool,
  disabled: PropTypes.bool,
  showScheduledStatus: PropTypes.bool,
  emptyErrorMessage: PropTypes.string,
  showScheduledSelection: PropTypes.bool,
};

export default CustomSelect;
