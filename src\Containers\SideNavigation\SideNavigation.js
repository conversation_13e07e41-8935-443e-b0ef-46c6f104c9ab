import React, { Component } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Map, List } from 'immutable';
import PropTypes from 'prop-types';
import { withTranslation, Trans } from 'react-i18next';

import NavigationItem from '../../Widgets/NavigationItem/NavigationItem';
import { CheckPermission, CheckProgramDepartment } from '../../Modules/Shared/Permissions';
import { selectUserInfo, selectAllProgramLists } from '../../_reduxapi/Common/Selectors';
import { selectInstitution } from '../../_reduxapi/institution/selectors';
import {
  indVerRename,
  isAnnouncementEnabled,
  isDigiSurveyEnabled,
  isIndVer,
  isModuleEnabled,
  jsUcfirst,
} from 'utils';
class SideNavigationComponent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: Map({
        calendar: false,
        pCalendar: false,
        userManage: false,
        infra: false,
        leaveManage: false,
        scheduleManage: false,
        assessmentManagement: false,
        attainmentCalculator: false,
        pCalendarNew: false,
      }),
    };
  }

  componentDidMount() {
    const { location } = this.props;
    const url = location.pathname;
    const { activeTab } = this.state;
    let updatedTab = activeTab;
    if (url.includes('/course-scheduling') && !url.includes('/course-scheduling/remote')) {
      updatedTab = activeTab.set('scheduleManage', true);
    } else if (url.includes('/leave-management')) {
      updatedTab = activeTab.set('leaveManage', true);
    } else if (
      url.includes('/infrastructure-management') ||
      url.includes('/course-scheduling/remote')
    ) {
      updatedTab = activeTab.set('infra', true);
    } else if (url.includes('/staff/management') || url.includes('/student/management')) {
      updatedTab = activeTab.set('userManage', true);
    } else if (
      url.includes('/program-calendar') ||
      url.includes('/programcalendar') ||
      url.includes('/calendar') ||
      url.includes('/interim') ||
      url.includes('/course-v1')
    ) {
      updatedTab = activeTab.set('pCalendar', true);
    } else if (url.includes('/pc')) {
      updatedTab = activeTab.set('pCalendarNew', true);
    } else if (
      url.includes('/InstitutionCalendar') ||
      url.includes('/reviewevent') ||
      url.includes('/reviewaccept') ||
      url.includes('/eventList')
    ) {
      updatedTab = activeTab.set('calendar', true);
    }

    this.setState({ activeTab: updatedTab });
  }
  setActiveTab = (tab) => {
    const { activeTab } = this.state;
    const updatedTab = activeTab.set(tab, !activeTab.get(tab));
    this.setState({ activeTab: updatedTab });
  };

  toGetLabel = () => {
    const staffLabels = {
      staffLeaveSettings:
        CheckPermission('pages', 'Leave Management', 'Leave Settings', 'View') &&
        CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Staff', ''),
      facultyLeave: CheckPermission('pages', 'Leave Management', 'Staff Leave', 'View'),
      facultyReportAbsence: CheckPermission('pages', 'Leave Management', 'Report Absence', 'View'),
      facultyApprover: CheckPermission('pages', 'Leave Management', 'Approve Leave', 'View'),
    };
    const studentLabels = {
      studentLeaveSettings:
        CheckPermission('pages', 'Leave Management', 'Leave Settings', 'View') &&
        CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Student LMS', 'View'),
      studentRegister: CheckPermission('pages', 'Leave Management', 'Student Register', 'View'),
      studentReports: CheckPermission('pages', 'Leave Management', 'Student Report', 'View'),
    };

    let onlyStaff = Object.values(staffLabels).includes(true);
    let onlyStudent = Object.values(studentLabels).includes(true);

    if (onlyStaff && onlyStudent) return 'side_nav.menus.Academic_Accountability_Management';
    if (onlyStudent) return 'side_nav.menus.Academic_Accountability_Management';
    if (onlyStaff) return 'side_nav.menus.Faculty_Academic_Accountability_Management';
    return 'side_nav.menus.Academic_Accountability_Management';
  };

  render() {
    const { publishedProgramLists, toggleClick, institution, loggedInUserData, t } = this.props;
    const { activeTab } = this.state;
    const userType = loggedInUserData.get('user_type', '');

    let url = this.props.location.pathname;
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let programId = params.get('id') || params.get('programid') || params.get('programId');
    let programName = params.get('name');

    let hasPCReviewerPermission = false;
    //let hasICReviewerPermission = false;
    const subRole = loggedInUserData.get('sub_role', List());
    if (subRole && subRole.size > 0) {
      hasPCReviewerPermission = subRole.indexOf('Program_Calendar_Reviewer') !== -1;
      //hasICReviewerPermission = subRole.indexOf('Institution_Calendar_Reviewer') !== -1;
    }

    const isSurveyRoleEnabled = CheckPermission(
      'pages',
      'User Module Permission',
      'Survey',
      'view'
    );
    const isAnnouncementRoleEnable = CheckPermission(
      'pages',
      'User Module Permission',
      'Announcement',
      'view'
    );
    const isAnnouncementModuleEnabled = isAnnouncementEnabled();
    const isSurveyModuleEnabled = isDigiSurveyEnabled();

    const isUserModulePermission =
      (isSurveyModuleEnabled && isSurveyRoleEnabled) ||
      (isAnnouncementModuleEnabled && isAnnouncementRoleEnable);

    return (
      <React.Fragment>
        {userType !== 'student' && (
          <NavigationItem
            to="/dashboard"
            active={url === '/dashboard' || url === '/dashboard/details' ? 'sideNavActive' : ''}
            menu={t('side_nav.menus.dashboard')}
            src={require('../../Assets/overview.svg')}
            clicked={toggleClick}
          />
        )}
        {CheckPermission('pages', 'Global Search', 'Dashboard', 'View') && (
          <NavigationItem
            to="/UserGlobalSearch"
            menu={t('side_nav.menus.user_global_search')}
            clicked={toggleClick}
            active={url.indexOf('UserGlobalSearch') > -1 ? 'sideNavActive' : ''}
          />
        )}

        {CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'View') && (
          <NavigationItem
            to="/InstitutionSessionReport"
            menu={t('side_nav.menus.curricular_monitoring')}
            clicked={toggleClick}
            active={url.indexOf('InstitutionSessionReport') > -1 ? 'sideNavActive' : ''}
          />
        )}
        {institution.get('institute_type') === 'group' && (
          <NavigationItem
            to="/institution/colleges"
            menu="Colleges"
            clicked={toggleClick}
            active={url.indexOf('institution/colleges') > -1 ? 'sideNavActive' : ''}
          />
        )}
        <NavigationItem
          to="/InstitutionCalendar"
          menu={t('side_nav.menus.institution_calendar')}
          clicked={toggleClick}
          active={
            url.indexOf('InstitutionCalendar') > -1 ||
            url.indexOf('reviewevent') > -1 ||
            url.indexOf('reviewaccept') > -1 ||
            url.indexOf('eventList') > -1
              ? 'sideNavActive'
              : ''
          }
        />
        {/* <>
          <div
            onClick={() => this.setActiveTab('calendar')}
            className={`sidenav-parent-item ${
              url.indexOf('InstitutionCalendar') > -1 ||
              url.indexOf('eventList') > -1 ||
              url.indexOf('reviewaccept') > -1 ||
              url.indexOf('reviewevent') > -1
                ? 'sideNavActive'
                : ''
            }`}
          >
            <span>
              <Trans i18nKey={'side_nav.menus.calendar'}></Trans>
            </span>
            <span>
              <i className="fa fa-caret-down pd_4"></i>
            </span>
          </div>
          {activeTab.get('calendar') && (
            <div>
              <Link
                to="/InstitutionCalendar"
                className={
                  url.indexOf('InstitutionCalendar') > -1 ||
                  url.indexOf('reviewevent') > -1 ||
                  url.indexOf('reviewaccept') > -1 ||
                  url.indexOf('eventList') > -1
                    ? 'sideNavActive'
                    : ''
                }
                onClick={toggleClick}
              >
                <span className="pl_30">
                  <Trans i18nKey={'side_nav.menus.institution_calendar'}></Trans>
                </span>
              </Link>
            </div>
          )}
        </> */}
        {/* {((publishedProgramLists && publishedProgramLists.length > 0) ||
          (hasPCReviewerPermission &&
            !CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish')) ||
          CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish')) && (
          <>
            <div
              onClick={() => this.setActiveTab('pCalendar')}
              className={`sidenav-parent-item ${
                url.indexOf('program-calendar') > -1 ||
                url.indexOf('programcalendar') > -1 ||
                url === 'calendar' ||
                url.indexOf('calender') > -1 ||
                url.indexOf('interim') > -1
                  ? 'sideNavActive'
                  : ''
              }`}
            >
              <span>
                <Trans i18nKey={'side_nav.menus.program_calendar'}></Trans>
              </span>
              <span>
                <i className="fa fa-caret-down pd_4"></i>
              </span>
            </div>
            <>
              {activeTab.get('pCalendar') && (
                <div>
                  {publishedProgramLists &&
                    publishedProgramLists
                      .filter((item) =>
                        userType === 'staff'
                          ? CheckProgramDepartment(item.id, true) === true || item.status === true
                          : item.status === true
                      )
                      .map((program, index) => {
                        let year = 'year2';
                        if (program.name.toLowerCase().indexOf('foundation') > -1) {
                          year = 'year1';
                        }
                        const yearLink = `/program-calendar/${program.id}/${year}?programid=${
                          program.id
                        }&year=${year}&pname=${program.name.replace('&', 'AND')}`;

                        return (
                          <a
                            className={
                              program.id === programId || program.name === programName
                                ? 'sideNavActive'
                                : ''
                            }
                            key={index}
                            onClick={() => {
                              localStorage.removeItem('sp-id');
                              toggleClick();
                            }}
                            href={yearLink}
                          >
                            <div className="pl_30">
                              <span style={{ whiteSpace: 'normal' }}>{program.name}</span>
                            </div>
                          </a>
                        );
                      })}
                  {hasPCReviewerPermission &&
                    !CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish') && (
                      <Link
                        to="/programcalendar/review"
                        onClick={toggleClick}
                        className={
                          url === '/programcalendar/review' || url === '/programcalendar/dean'
                            ? 'sideNavActive'
                            : ''
                        }
                      >
                        <span className="pl_30">
                          <Trans i18nKey={'side_nav.menus.reviewer'}></Trans>
                        </span>
                      </Link>
                    )}

                  {CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish') && (
                    <Link
                      to="/programcalendar/dean"
                      onClick={toggleClick}
                      className={
                        url === '/programcalendar/review' || url === '/programcalendar/dean'
                          ? 'sideNavActive'
                          : ''
                      }
                    >
                      <Trans i18nKey={'side_nav.menus.reviewer'}></Trans>
                    </Link>
                  )}
                </div>
              )}
            </>
          </>
        )} */}
        {((publishedProgramLists && publishedProgramLists.length > 0) ||
          (hasPCReviewerPermission &&
            !CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish')) ||
          CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish')) && (
          <>
            <div
              onClick={() => this.setActiveTab('pCalendarNew')}
              className={`sidenav-parent-item ${url.indexOf('/pc') > -1 ? 'sideNavActive' : ''}`}
            >
              <span>
                <Trans i18nKey={'side_nav.menus.program_calendar'}></Trans>
              </span>
              <span>
                <i className="fa fa-caret-down pd_4"></i>
              </span>
            </div>
            <>
              {activeTab.get('pCalendarNew') && (
                <div>
                  {publishedProgramLists &&
                    publishedProgramLists
                      .filter((item) =>
                        userType === 'staff'
                          ? CheckProgramDepartment(item.id, true) === true || item.status === true
                          : item.status === true
                      )
                      .sort((previous, next) => previous.name.localeCompare(next.name))
                      .map((program, index) => {
                        const yearLink = `/pc?programId=${
                          program.id
                        }&programName=${program.name.replace('&', 'AND')}`;
                        return (
                          <Link
                            className={
                              program.id === programId || program.name === programName
                                ? 'sideNavActive'
                                : ''
                            }
                            key={index}
                            onClick={() => {
                              localStorage.removeItem('sp-id-new');
                              toggleClick();
                            }}
                            to={yearLink}
                          >
                            <div className="pl_30">
                              <span style={{ whiteSpace: 'normal' }}>{program.name}</span>
                            </div>
                          </Link>
                        );
                      })}
                  {hasPCReviewerPermission &&
                    !CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish') && (
                      <Link
                        to="/programcalendar/review"
                        onClick={toggleClick}
                        className={
                          url === '/programcalendar/review' || url === '/programcalendar/dean'
                            ? 'sideNavActive'
                            : ''
                        }
                      >
                        <span className="pl_30">
                          <Trans i18nKey={'side_nav.menus.reviewer'}></Trans>
                        </span>
                      </Link>
                    )}

                  {CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Publish') && (
                    <Link
                      to="/programcalendar/dean"
                      onClick={toggleClick}
                      className={
                        url === '/programcalendar/review' || url === '/programcalendar/dean'
                          ? 'sideNavActive'
                          : ''
                      }
                    >
                      <Trans i18nKey={'side_nav.menus.reviewer'}></Trans>
                    </Link>
                  )}
                </div>
              )}
            </>
          </>
        )}
        {CheckPermission('modules', 'User Management') && (
          <>
            <Link
              to={`${url}${search}`}
              onClick={() => this.setActiveTab('userManage')}
              className={
                url.indexOf('staff/management') > -1 ||
                url.indexOf('staff/inactive') > -1 ||
                url.indexOf('staff/profile') > -1 ||
                url.indexOf('staffvalid/all') > -1 ||
                url.indexOf('student/management') > -1 ||
                url.indexOf('student/inactive') > -1 ||
                url.indexOf('student/profile') > -1 ||
                url.indexOf('studentvalid/all') > -1
                  ? 'sideNavActive'
                  : ''
              }
            >
              <span>
                <Trans i18nKey={'side_nav.menus.user_management'}></Trans>
              </span>
              <span>
                <i className="fa fa-caret-down pd_4"></i>
              </span>
            </Link>

            {activeTab.get('userManage') &&
              CheckPermission('pages', 'User Management', 'Staff Management', 'View') && (
                <Link
                  to="/staff/management"
                  className={
                    url.indexOf('staff/management') > -1 ||
                    url.indexOf('staff/inactive') > -1 ||
                    url.indexOf('staff/profile') > -1 ||
                    url.indexOf('staffvalid/all') > -1
                      ? 'sideNavActive'
                      : ''
                  }
                  onClick={toggleClick}
                >
                  {' '}
                  <span className="pl_30">
                    <Trans i18nKey={'side_nav.menus.staff_management'}></Trans>
                  </span>
                </Link>
              )}
            {activeTab.get('userManage') &&
              CheckPermission('pages', 'User Management', 'Student Management', 'View') && (
                <Link
                  to="/student/management"
                  className={
                    url.indexOf('student/management') > -1 ||
                    url.indexOf('student/inactive') > -1 ||
                    url.indexOf('student/profile') > -1 ||
                    url.indexOf('studentvalid/all') > -1
                      ? 'sideNavActive'
                      : ''
                  }
                  onClick={toggleClick}
                >
                  {' '}
                  <span className="pl_30">
                    <Trans i18nKey={'side_nav.menus.student_management'}></Trans>
                  </span>
                </Link>
              )}
          </>
        )}
        {CheckPermission('modules', 'Student Grouping', 'Dashboard', 'View') && (
          <NavigationItem
            to="/student-grouping/dashboard"
            menu={t('side_nav.menus.student_grouping')}
            clicked={toggleClick}
            active={url.indexOf('student-grouping') > -1 ? 'sideNavActive' : ''}
          />
        )}
        {(CheckPermission('pages', 'Infrastructure Management', 'Onsite', 'View') ||
          CheckPermission('pages', 'Infrastructure Management', 'Remote', 'View')) && (
          <>
            <div
              onClick={() => this.setActiveTab('infra')}
              className={`sidenav-parent-item ${
                url.includes('/infrastructure-management') ||
                url.includes('/course-scheduling/remote')
                  ? 'sideNavActive'
                  : ''
              }`}
            >
              <span>
                <Trans i18nKey={'side_nav.menus.infra_management'}></Trans>
              </span>
              <span>
                <i className="fa fa-caret-down pd_4"></i>
              </span>
            </div>
            {activeTab.get('infra') && (
              <>
                {CheckPermission('pages', 'Infrastructure Management', 'Onsite', 'View') && (
                  <div>
                    <Link
                      to="/infrastructure-management"
                      className={url.includes('/infrastructure-management') ? 'sideNavActive' : ''}
                      onClick={toggleClick}
                    >
                      <span className="pl_30">
                        {isIndVer() ? (
                          jsUcfirst(indVerRename('onsite', programId))
                        ) : (
                          <Trans i18nKey={`side_nav.menus.onsite`}></Trans>
                        )}
                      </span>
                    </Link>
                  </div>
                )}
                {CheckPermission('pages', 'Infrastructure Management', 'Remote', 'View') && (
                  <div>
                    <Link
                      to="/course-scheduling/remote"
                      className={url.includes('/course-scheduling/remote') ? 'sideNavActive' : ''}
                      onClick={toggleClick}
                    >
                      <span className="pl_30">
                        <Trans i18nKey={'side_nav.menus.remote'}></Trans>
                      </span>
                    </Link>
                  </div>
                )}
              </>
            )}
          </>
        )}
        {CheckPermission('pages', 'Roles and Permissions', 'Dashboard', 'View') && (
          <NavigationItem
            to="/roles/dashboard"
            menu={t('side_nav.menus.roles_permissions')}
            clicked={toggleClick}
            active={url.indexOf('roles') > -1 ? 'sideNavActive' : ''}
          />
        )}

        {isUserModulePermission && (
          <NavigationItem
            to="/UserModulePermissions"
            menu={t('side_nav.menus.User_Module_Permission')}
            clicked={toggleClick}
            active={url.indexOf('UserModulePermissions') > -1 ? 'sideNavActive' : ''}
          />
        )}

        {CheckPermission('modules', 'Leave Management') && (
          <>
            <Link
              to={`${url}${search}`}
              onClick={() => this.setActiveTab('leaveManage')}
              className={url.indexOf('leave-management') > -1 ? 'sideNavActive' : ''}
            >
              <div className="d-flex white-space-initial">
                <Trans i18nKey={this.toGetLabel()}></Trans>
                <div>
                  <i className="fa fa-caret-down pd_4"></i>
                </div>
              </div>
            </Link>
            {activeTab.get('leaveManage') &&
              CheckPermission('pages', 'Leave Management', 'Leave Settings', 'View') &&
              (CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Staff', '') ||
                CheckPermission(
                  'tabs',
                  'Leave Management',
                  'Leave Settings',
                  '',
                  'Student LMS',
                  'View'
                )) && (
                <Link
                  to="/leave-management/settings?name=staff"
                  className={url.indexOf('leave-management/settings') > -1 ? 'sideNavActive' : ''}
                  onClick={toggleClick}
                >
                  {' '}
                  <span className="pl_30">
                    <Trans i18nKey={'side_nav.menus.leave_settings'}></Trans>
                  </span>
                </Link>
              )}
            {activeTab.get('leaveManage') &&
              (CheckPermission('pages', 'Leave Management', 'Student Leave', 'View') ||
                CheckPermission('pages', 'Leave Management', 'Staff Leave', 'View')) && (
                <>
                  <Link
                    to="/leave-management/leave"
                    className={url.indexOf('leave-management/leave') > -1 ? 'sideNavActive' : ''}
                    onClick={toggleClick}
                  >
                    {' '}
                    <span className="pl_30">
                      <Trans i18nKey={'side_nav.menus.faculty_leaves'}></Trans>
                    </span>
                  </Link>
                </>
              )}
            {activeTab.get('leaveManage') &&
              CheckPermission('pages', 'Leave Management', 'Student Register', 'View') && (
                <Link
                  to="/leave-management/student-register"
                  className={
                    url.indexOf('leave-management/student-register') > -1 ? 'sideNavActive' : ''
                  }
                  onClick={toggleClick}
                >
                  {' '}
                  <span className="pl_30">
                    <Trans i18nKey={'side_nav.menus.student_register'}></Trans>
                  </span>
                </Link>
              )}

            {activeTab.get('leaveManage') &&
              CheckPermission('pages', 'Leave Management', 'Report Absence', 'View') && (
                <Link
                  to="/leave-management/report"
                  className={url.indexOf('leave-management/report') > -1 ? 'sideNavActive' : ''}
                  onClick={toggleClick}
                >
                  {' '}
                  <span className="pl_30">
                    <Trans i18nKey={'side_nav.menus.faculty_report_absence'}></Trans>
                  </span>
                </Link>
              )}
            {activeTab.get('leaveManage') &&
              CheckPermission('pages', 'Leave Management', 'Student Report', 'View') && (
                <Link
                  to={
                    CheckPermission(
                      'tabs',
                      'Leave Management',
                      'Student Report',
                      'View',
                      'Student Leave Management Reports',
                      'View'
                    )
                      ? '/lmsReportsMain/collective_reports'
                      : '/lmsReportsMain/individual_reports'
                  }
                  className={url.indexOf('lmsReports') > -1 ? 'sideNavActive' : ''}
                  onClick={toggleClick}
                >
                  {' '}
                  <span className="pl_30">
                    <Trans i18nKey={'side_nav.menus.lms_reports'}></Trans>
                  </span>
                </Link>
              )}

            {activeTab.get('leaveManage') &&
              CheckPermission('pages', 'Leave Management', 'Approve Leave', 'View') && (
                <>
                  <Link
                    to="/leave-management/review"
                    className={url.indexOf('leave-management/review') > -1 ? 'sideNavActive' : ''}
                    onClick={toggleClick}
                  >
                    {' '}
                    <span className="pl_30">
                      <Trans i18nKey={'side_nav.menus.approver'}></Trans>
                    </span>
                  </Link>
                </>
              )}
          </>
        )}
        {CheckPermission('pages', 'Program Input', 'Programs', 'View') && (
          <NavigationItem
            to="/program-input/program"
            menu={t('side_nav.menus.program_inputs')}
            clicked={toggleClick}
            active={url.indexOf('program-input') > -1 ? 'sideNavActive' : ''}
          />
        )}
        {CheckPermission('pages', 'Global Configuration', 'Institution', 'View') && (
          <NavigationItem
            to="/globalConfiguration-v1/institution"
            menu={'Global Configuration'}
            clicked={toggleClick}
            active={url.indexOf('globalConfiguration-v1') > -1 ? 'sideNavActive' : ''}
          />
        )}
        <>
          {isModuleEnabled('ASSESSMENT_MODULE') &&
            (CheckPermission('pages', 'Assessment Management', 'Assessment Library', 'View') ||
              CheckPermission('pages', 'Assessment Management', 'Assessment Type', 'View')) && (
              <div
                onClick={() => this.setActiveTab('assessmentManagement')}
                className={`sidenav-parent-item ${
                  url.includes('/assessment-management') &&
                  !url.includes('assessment-management/assessment-types')
                    ? 'sideNavActive'
                    : ''
                }`}
              >
                <span>
                  <Trans i18nKey={'side_nav.menus.assessment-management'}></Trans>
                </span>
                <span>
                  <i className="fa fa-caret-down pd_4"></i>
                </span>
              </div>
            )}

          {activeTab.get('assessmentManagement', false) && (
            <>
              {CheckPermission('pages', 'Assessment Management', 'Assessment Library', 'View') && (
                <div>
                  <Link
                    to="/assessment-management/assessment_library"
                    className={
                      url.includes('/assessment-management/assessment_library')
                        ? 'sideNavActive'
                        : ''
                    }
                    onClick={toggleClick}
                  >
                    <span className="pl_30">
                      <Trans i18nKey={'side_nav.menus.assessment_library'}></Trans>
                    </span>
                  </Link>
                </div>
              )}

              {CheckPermission('pages', 'Assessment Management', 'Assessment Type', 'View') && (
                <div>
                  {' '}
                  <Link
                    to="/assessment-management/assessment-types"
                    className={
                      url.includes('/assessment-management/assessment-types') ? 'sideNavActive' : ''
                    }
                    onClick={toggleClick}
                  >
                    <span className="pl_30">
                      <Trans i18nKey={'side_nav.menus.assessment_type'}></Trans>
                    </span>
                  </Link>
                </div>
              )}
            </>
          )}

          {isModuleEnabled('ATTAINMENT_MODULE') &&
            (CheckPermission('pages', 'Attainment Calculator', 'Attainment Setting', 'View') ||
              CheckPermission('pages', 'Attainment Calculator', 'Attainment Report', 'View')) && (
              <div
                onClick={() => this.setActiveTab('attainmentCalculator')}
                className={`sidenav-parent-item ${
                  url.includes('/attainment-calculator') ? 'sideNavActive' : ''
                }`}
              >
                <span>
                  <Trans i18nKey={'side_nav.menus.attainment_calculator'}></Trans>
                </span>
                <span>
                  <i className="fa fa-caret-down pd_4"></i>
                </span>
              </div>
            )}
          {activeTab.get('attainmentCalculator') && (
            <>
              {CheckPermission('pages', 'Attainment Calculator', 'Attainment Setting', 'View') && (
                <div>
                  <Link
                    to="/attainment-calculator/attainment-settings"
                    className={
                      url.includes('/attainment-calculator/attainment-settings')
                        ? 'sideNavActive'
                        : ''
                    }
                    onClick={toggleClick}
                  >
                    <span className="pl_30">
                      <Trans i18nKey={'side_nav.menus.attainment_settings'}></Trans>
                    </span>
                  </Link>
                </div>
              )}

              {CheckPermission('pages', 'Attainment Calculator', 'Attainment Report', 'View') && (
                <div>
                  <Link
                    to="/attainment-calculator/attainment-reports"
                    className={
                      url.includes('/attainment-calculator/attainment-reports')
                        ? 'sideNavActive'
                        : ''
                    }
                    onClick={toggleClick}
                  >
                    <span className="pl_30">
                      <Trans i18nKey={'side_nav.menus.attainment_reports'}></Trans>
                    </span>
                  </Link>
                </div>
              )}
            </>
          )}
        </>
        {(CheckPermission(
          'pages',
          'Schedule Management',
          'Extra Curricular and Break',
          'List View'
        ) ||
          CheckPermission(
            'pages',
            'Schedule Management',
            'Assign Course Coordinator',
            'List View'
          ) ||
          CheckPermission('pages', 'Schedule Management', 'Course Scheduling', 'List View')) && (
          <>
            <div
              onClick={() => this.setActiveTab('scheduleManage')}
              className={`sidenav-parent-item ${
                url.includes('/course-scheduling') && !url.includes('/course-scheduling/remote')
                  ? 'sideNavActive'
                  : ''
              }`}
            >
              <span>
                <Trans i18nKey={'side_nav.menus.schedule_management'}></Trans>
              </span>
              <span>
                <i className="fa fa-caret-down pd_4"></i>
              </span>
            </div>
            {activeTab.get('scheduleManage') && (
              <>
                {CheckPermission(
                  'pages',
                  'Schedule Management',
                  'Course Scheduling',
                  'List View'
                ) && (
                  <div>
                    <Link
                      to="/course-scheduling/schedule"
                      className={url.includes('/course-scheduling/schedule') ? 'sideNavActive' : ''}
                      onClick={toggleClick}
                    >
                      <span className="pl_30">
                        <Trans i18nKey={'side_nav.menus.course_scheduling'}></Trans>
                      </span>
                    </Link>
                  </div>
                )}
                {CheckPermission(
                  'pages',
                  'Schedule Management',
                  'Extra Curricular and Break',
                  'List View'
                ) && (
                  <div>
                    <Link
                      to="/course-scheduling/extra-curricular-break"
                      className={
                        url.includes('/course-scheduling/extra-curricular-break')
                          ? 'sideNavActive'
                          : ''
                      }
                      onClick={toggleClick}
                    >
                      <span className="pl_30">
                        <Trans i18nKey={'side_nav.menus.extra_curricular'}></Trans>
                      </span>
                    </Link>
                  </div>
                )}
                {CheckPermission(
                  'pages',
                  'Schedule Management',
                  'Assign Course Coordinator',
                  'List View'
                ) && (
                  <div>
                    <Link
                      to="/course-scheduling/course-coordinators"
                      className={
                        url.includes('/course-scheduling/course-coordinators')
                          ? 'sideNavActive'
                          : ''
                      }
                      onClick={toggleClick}
                    >
                      <span className="pl_30">
                        <Trans i18nKey={'side_nav.menus.assign_course_coordinator'}></Trans>
                      </span>
                    </Link>
                  </div>
                )}
              </>
            )}
          </>
        )}
        {CheckPermission('pages', 'Reports and Analytics', 'Dashboard', 'View') && (
          <NavigationItem
            to="/reports/dashboard"
            menu={t('side_nav.menus.reports_analytics')}
            clicked={toggleClick}
            active={url.indexOf('reports') > -1 ? 'sideNavActive' : ''}
          />
        )}

        {CheckPermission('pages', 'Face Anamoly Purification', 'Anomaly Purifying', 'View') && (
          <NavigationItem
            to="/face-anamoly-report"
            menu={'Face Anamoly Purification'}
            clicked={toggleClick}
            active={url.indexOf('faceAnamoly') > -1 ? 'sideNavActive' : ''}
          />
        )}
        {process.env.REACT_APP_CLIENT_NAME === 'sbmch-staging' && (
          <NavigationItem
            to="/staff_integrity_monitor"
            menu={'StaffIntegrityMonitor'}
            clicked={toggleClick}
            active={url.indexOf('staff_integrity_monitor') > -1 ? 'sideNavActive' : ''}
          />
        )}
        {isModuleEnabled('Q360') && (
          <>
            <Link
              to={`${url}${search}`}
              onClick={() => this.setActiveTab('qapc')}
              className={
                url.indexOf('/qapc/QualityAssuranceProcess') === -1 &&
                url.indexOf('/qapc/QualityAssuranceApproval') === -1
                  ? ''
                  : 'sideNavActive'
              }
            >
              <span>Quality Life Cycle</span>
              <span>
                <i className="fa fa-caret-down pd_4"></i>
              </span>
            </Link>

            {activeTab.get('qapc') && (
              <>
                <Link
                  to="/qapc/QualityAssuranceProcess"
                  className={
                    url.indexOf('/qapc/QualityAssuranceProcess') > -1 ? 'sideNavActive' : ''
                  }
                  onClick={toggleClick}
                >
                  {' '}
                  <span className="pl_30">Quality Assurance Process</span>
                </Link>

                <Link
                  to="/qapc/rolesAndPermission"
                  className={url.indexOf('/qapc/rolesAndPermission') > -1 ? 'sideNavActive' : ''}
                  onClick={toggleClick}
                >
                  <span className="pl_30">Roles and Permissions</span>
                </Link>

                <Link
                  to="/qapc/QualityAssuranceApproval"
                  className={
                    url.indexOf('/qapc/QualityAssuranceApproval') > -1 ? 'sideNavActive' : ''
                  }
                  onClick={toggleClick}
                >
                  <span className="pl_30">Quality Assurance Approver</span>
                </Link>
                <Link
                  to="/qapc/Dashboard"
                  className={url.indexOf('/qapc/dashboard') > -1 ? 'sideNavActive' : ''}
                  onClick={toggleClick}
                >
                  <span className="pl_30">QA Dashboard</span>
                </Link>
              </>
            )}
          </>
        )}
        <div style={{ height: '65px' }}></div>
      </React.Fragment>
    );
  }
}

SideNavigationComponent.propTypes = {
  publishedProgramLists: PropTypes.array,
  location: PropTypes.object,
  loggedInUserData: PropTypes.instanceOf(Map),
  institution: PropTypes.instanceOf(Map),
  toggleClick: PropTypes.func,
  t: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
    publishedProgramLists: selectAllProgramLists(state),
    institution: selectInstitution(state),
  };
};

const SideNavigation = withTranslation()(SideNavigationComponent);
export default connect(mapStateToProps)(withRouter(React.memo(SideNavigation)));
