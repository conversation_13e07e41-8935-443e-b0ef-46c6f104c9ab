import React, { forwardRef } from 'react';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import parameter_board from 'Assets/user_activity_board/parameter_board.svg';
import Punctuality from './Criteria/Punctuality/Punctuality';
import AddParameter from './AddParameter';
import PropTypes from 'prop-types';
const Parameter = forwardRef(({ render }, parameterRef) => {
  const parameters = parameterRef.current;
  return (
    <Accordion disableGutters sx={{ background: 'white !important' }}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1a-content"
        id="panel1a-header"
        className="px-3 py-2"
      >
        <div className="d-flex">
          <img src={parameter_board} alt="icon_late_config" />
          <div className="ml-3">
            <div className="f-18 bold late_config_color">Parameters</div>
            <span className="f-15 text-light-grey pt-1">
              Choose the default Parameters & Criteria’s.
            </span>
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>
        <>
          <AddParameter />
          {parameters.map((parameter, index) => (
            <Punctuality
              key={index}
              render={render}
              parameter={parameter}
              ref={parameterRef}
              parameterIndex={index}
            />
          ))}
        </>
      </AccordionDetails>
    </Accordion>
  );
});
Parameter.propTypes = {
  render: PropTypes.bool,
};
export default Parameter;
