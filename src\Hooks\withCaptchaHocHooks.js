import useCaptcha from './useCaptcha';
import React from 'react';

function withCaptchaHocHooks(Component, failedAttemptsThreshold) {
  const InjectedCaptcha = function (props) {
    const {
      failedAttempts,
      showCaptcha,
      captchaVerified,
      captchaRef,
      onCaptchaChange,
      incrementFailedAttempt,
    } = useCaptcha(failedAttemptsThreshold);
    return (
      <Component
        {...props}
        failedAttempts={failedAttempts}
        showCaptcha={showCaptcha}
        captchaVerified={captchaVerified}
        captchaRef={captchaRef}
        onCaptchaChange={onCaptchaChange}
        incrementFailedAttempt={incrementFailedAttempt}
      />
    );
  };
  return InjectedCaptcha;
}

export default withCaptchaHocHooks;
