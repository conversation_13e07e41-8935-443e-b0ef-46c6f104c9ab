import React, { Fragment, useEffect } from 'react';
import { connect } from 'react-redux';
import swal from 'sweetalert2';
import PropTypes from 'prop-types';

import {
  FlexWrapper,
  Null,
  Select,
  EventWrapper,
  ModalWrapper,
  ModalBackgroundWrapper,
  PrimaryButton,
  DisplaySemesterTag,
} from '../../Styled';
import {
  interimChangeSemester,
  calendarSetLevelDate,
  saveInterimRotationCount,
  resetInterimMessage,
  eventInterimDelete,
  updateInterimEvents,
} from '../../../../_reduxapi/actions/interimCalendar';
import {
  getTranslatedDuration,
  ucFirst,
  levelRename,
  isWeekStartDay,
  isWeekEndDay,
} from '../../../../utils';

import AddEvent from '../../Modal/Events/AddEvent';
import EventRows from '../../UtilityComponents/EventRows';
import DateInput from '../../UtilityComponents/DateInput';
import { NotificationManager } from 'react-notifications';
import Loader from '../../../../Widgets/Loader/Loader';
import moment from 'moment';
import { timeFormat } from '../../../../utils';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import { t } from 'i18next';
import { Trans } from 'react-i18next';

const LevelField = ({
  id,
  active,
  active_semesters,
  eventInterimDelete,
  eventsState,
  currentProgramCalendarId,
  isLoading,
  error,
  success,
  resetInterimMessage,
  updateInterimEvents,
  saveInterimRotationCount,
  interimChangeSemester,
  calendarSetLevelDate,
  institutionCalenderEvents,
  academicStartDate,
  academicEndDate,
  activeInstitutionCalendar,
  programId,
}) => {
  let [levelConfig, setLevelConfig] = eventsState;
  const dataAlign = (start_date, end_date, level_no, term, dispatchFn) => {
    let error = false;
    if (currentProgramCalendarId === '') {
      NotificationManager.error('Program calendar id is missing, try again');
      error = true;
    }

    let final = {};
    if (start_date !== '' && end_date !== '' && !error) {
      final._id = currentProgramCalendarId;
      final.level_no = level_no;
      final.batch = term;
      final.start_date = moment(start_date).format('YYYY-MM-DD');
      final.end_date = moment(end_date).format('YYYY-MM-DD');

      dispatchFn(final, NotificationManager);
    }
  };

  const rotationConfirm = (level_no, term, count, dispatchFn) => {
    let error = false;
    if (currentProgramCalendarId === '') {
      NotificationManager.error('Program calendar id is missing, try again');
      error = true;
    }
    let data = {};
    data._calendar_id = currentProgramCalendarId;
    data.level_no = level_no;
    data.batch = term;
    data.rotation_count = count;
    if (!error) {
      dispatchFn({ ...data });
    }
  };

  const eventDeleteConfirm = (eventId, level, batch, dispatchFn) => {
    let error = false;
    if (level === '') {
      NotificationManager.error('Level is missing');
      error = true;
    } else if (batch === '') {
      NotificationManager.error('Batch is missing');
      error = true;
    }

    if (!error) {
      swal
        .fire({
          title: t('sure_delete'),
          text: t('once_deleted_cant_recover'),
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: t('yes_delete'),
          cancelButtonText: t('no_keep'),
          dangerMode: true,
        })
        .then((res) => {
          if (res.isConfirmed) {
            dispatchFn(
              currentProgramCalendarId,
              eventId,
              level,
              batch,
              activeInstitutionCalendar.get('_id')
            );
          }
        });
    }
  };
  useEffect(() => {
    if (error !== null) {
      NotificationManager.error(error);
      setTimeout(() => {
        resetInterimMessage();
      }, 2000);
    } else if (success != null) {
      NotificationManager.success(success);
      setLevelConfig({
        type: 'CLEAR_MODAL',
      });
      setTimeout(() => {
        resetInterimMessage();
      }, 2000);
    }
  }, [error, success, resetInterimMessage, setLevelConfig]);

  const updateEvent = (data, events) => {
    let error = false;
    let final = { event_name: {} };
    let startDate = moment(data.start_date).format('YYYY-MM-DD');
    let endDate = moment(data.end_date).format('YYYY-MM-DD');
    let startTime = timeFormat(moment(data.start_time).format('H:mm') + ':00');
    let endTime = timeFormat(moment(data.end_time).format('H:mm') + ':00');

    let st = moment(startDate + 'T' + startTime).toDate();
    let et = moment(endDate + 'T' + endTime).toDate();

    const check_start = st;
    const check_end = et;
    events.forEach((item) => {
      if (Date.parse(item.start_time) <= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_start)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) <= Date.parse(check_end) && !error) {
        if (Date.parse(item.end_time) >= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.start_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.start_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event start timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      } else if (Date.parse(item.end_time) >= Date.parse(check_start) && !error) {
        if (Date.parse(item.end_time) <= Date.parse(check_end)) {
          error = true;
          NotificationManager.error(
            `Event end timing is matching with this ${item.event_name}. Please use different timings `
          );
        }
      }
    });

    if (data.title === '') {
      NotificationManager.error('Event title is required');
      error = true;
    } else if (data.start_date === '' || data.end_date === '') {
      NotificationManager.error('Start date and end date is required');
      error = true;
    } else if (data.start_time === '' || data.end_time === '') {
      NotificationManager.error('Start time and end time is required');
      error = true;
    } else if (Date.parse(data.start_date) > Date.parse(data.end_date)) {
      error = true;
      NotificationManager.error('End date should be greater than start date');
    }
    // else if (Date.parse(academicStartDate) > Date.parse(data.start_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Level start date should be greater than or equal to Academic start date"
    //   );
    // } else if (Date.parse(academicEndDate) < Date.parse(data.start_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Level start date should be lesser than or equal to Academic end date"
    //   );
    // } else if (Date.parse(academicEndDate) < Date.parse(data.end_date)) {
    //   error = true;
    //   NotificationManager.error(
    //     "Level end date should be lesser than or equal to Academic end date"
    //   );
    // }

    if (startDate !== '' && endDate !== '' && !error) {
      if (st.getTime() >= et.getTime()) {
        NotificationManager.error('End time should be greater than start time');
        error = true;
      }
    }

    final.event_type = data.event_type;
    final.event_name.first_language = data.title;
    final.event_date = startDate;
    final.start_time = st.getTime();
    final.end_time = et.getTime();
    final.end_date = endDate;
    final._event_id = data._id;
    final._calendar_id = currentProgramCalendarId;
    final.level_no = data.level_no;
    final.batch = data.batch;

    if (!error && activeInstitutionCalendar && !activeInstitutionCalendar.isEmpty()) {
      updateInterimEvents(final, activeInstitutionCalendar.get('_id'));
    }
  };

  const levelStartDate = levelConfig[active]?.['semesters']?.[active_semesters]?.[0]?.start_date;
  // const levelEndDate = levelConfig[active]['semesters'][active_semesters][0].end_date;
  const levelEndDate =
    levelConfig[active]?.['semesters']?.[active_semesters]?.[1]?.end_date !== undefined &&
    levelConfig[active]?.['semesters']?.[active_semesters]?.[1]?.end_date !== ''
      ? levelConfig[active]?.['semesters']?.[active_semesters]?.[1]?.end_date
      : levelConfig[active]?.['semesters']?.[active_semesters]?.[0]?.end_date;

  const temp = () => (
    <ModalWrapper>
      <ModalBackgroundWrapper>
        <h3 className="text-left">
          <Trans i18nKey={'events.edit_event'}></Trans>
        </h3>
        <p className="text-left">
          {' '}
          <Trans i18nKey={'select_date_to_sync'}></Trans>
        </p>
        <AddEvent
          data={levelConfig.edit}
          method={setLevelConfig}
          min_len={
            levelConfig?.edit_config?.min !== undefined
              ? new Date(levelConfig?.edit_config?.min)
              : academicStartDate
          }
          max_len={
            levelConfig?.edit_config?.max !== undefined
              ? new Date(levelConfig?.edit_config?.max)
              : academicEndDate
          }
          levelStartDate={levelStartDate}
          levelEndDate={levelEndDate}
        />
        <FlexWrapper>
          <Null />
          <PrimaryButton className="light" onClick={() => setLevelConfig({ type: 'OFF' })}>
            <Trans i18nKey={'cancel'}></Trans>
          </PrimaryButton>
          <PrimaryButton
            className="bordernone"
            onClick={() => updateEvent(levelConfig.edit, levelConfig.edit_config.events)}
          >
            <Trans i18nKey={'save'}></Trans>
          </PrimaryButton>
        </FlexWrapper>
      </ModalBackgroundWrapper>
    </ModalWrapper>
  );
  let activeYear = active;
  let activeSemester = active_semesters;
  let countOfSemesters = levelConfig[activeYear]['semesters'];
  let semesterTabs = [];
  let currentActiveSemester = [];
  if (countOfSemesters !== undefined) {
    let semesterKeys = Object.keys(countOfSemesters);
    if (semesterKeys.length > 0) {
      for (let semCount = 0; semCount < semesterKeys.length; semCount++) {
        semesterTabs.push(
          <DisplaySemesterTag
            key={semCount}
            className={activeSemester === semesterKeys[semCount] ? `light` : null}
            onClick={() => interimChangeSemester(semesterKeys[semCount])}
          >
            {ucFirst(semesterKeys[semCount]).replaceAll('Semester', t('semester'))}
          </DisplaySemesterTag>
        );
      }
    }
    currentActiveSemester = countOfSemesters[activeSemester];
  }
  return (
    <Fragment>
      <Loader isLoading={isLoading} />
      <Fragment>{levelConfig.modal && temp()}</Fragment>
      <FlexWrapper>{semesterTabs}</FlexWrapper>

      {currentActiveSemester && currentActiveSemester.length > 0 && (
        <>
          {currentActiveSemester.map((current, activeIndex) => {
            let level_no = current.level_no !== '' ? current.level_no : '';
            let term = current.term;
            let level_title = current.term !== '' ? ucFirst(term + ' - ' + level_no) : '';
            let start_date =
              current.start_date !== undefined && current.start_date !== ''
                ? moment(current.start_date).format('D MMM YYYY')
                : '';
            let end_date =
              current.end_date !== undefined && current.end_date !== ''
                ? moment(current.end_date).format('D MMM YYYY')
                : '';

            let rotation = current.rotation;
            let rotation_count = current.rotation_count;
            let events = current.events;

            return (
              <Fragment key={activeIndex}>
                <div className="p-3">
                  <div className="row">
                    <div className="col-md-3 col-lg-3 col-xl-2 col-sm-12 col-12">
                      <h3 className="f-14 pt-3">{levelRename(level_title, programId)}</h3>
                    </div>

                    <div className="col-md-3 col-lg-3 col-xl-2 col-sm-12 col-12 datepickerAr">
                      <DateInput
                        datepicker={true}
                        isWeekStartDay={isWeekStartDay()}
                        placeholderText={t('events.start_date')}
                        selected={start_date !== '' ? new Date(start_date) : null}
                        value={getTranslatedDuration(start_date)}
                        edit={(value) => {
                          setLevelConfig({
                            type: 'ON_INPUT',
                            payload: value,
                            name: 'start_date',
                            year: active,
                            semester: active_semesters,
                            indexPlace: activeIndex,
                          });
                          dataAlign(
                            value,
                            end_date,
                            level_no,
                            term,
                            calendarSetLevelDate,
                            'start_date',
                            active,
                            active_semesters,
                            activeIndex
                          );
                        }}
                        title={t('events.start_date')}
                        minDate={academicStartDate !== '' ? new Date(academicStartDate) : ''}
                        maxDate={academicEndDate !== '' ? new Date(academicEndDate) : null}
                      />

                      {/* <DateInput
                          title="Start date"
                          value={String(start_date).slice(0, 10)}
                          min={
                            academicStartDate !== ""
                              ? academicStartDate.slice(0, 10)
                              : ""
                          }
                          max={
                            academicEndDate !== ""
                              ? academicEndDate.slice(0, 10)
                              : ""
                          }
                          edit={(value) => {
                            setLevelConfig({
                              type: "ON_INPUT",
                              payload: value,
                              name: "start_date",
                              year: active,
                              semester: active_semesters,
                              indexPlace: activeIndex,
                            });
                          }}
                          onBlur={() =>
                            dataAlign(
                              start_date,
                              end_date,
                              level_no,
                              term,
                              calendarSetLevelDate,
                              "start_date",
                              active,
                              active_semesters,
                              activeIndex
                            )
                          }
                        /> */}
                    </div>

                    <div className="col-md-3 col-lg-3 col-xl-2 col-sm-12 col-12 datepickerAr">
                      <DateInput
                        datepicker={true}
                        isWeekEndDay={isWeekEndDay()}
                        placeholderText={t('events.end_date')}
                        selected={end_date !== '' ? new Date(end_date) : null}
                        value={getTranslatedDuration(end_date)}
                        edit={(value) => {
                          setLevelConfig({
                            type: 'ON_INPUT',
                            payload: value,
                            name: 'end_date',
                            year: active,
                            semester: active_semesters,
                            indexPlace: activeIndex,
                          });
                          dataAlign(
                            start_date,
                            value,
                            level_no,
                            term,
                            calendarSetLevelDate,
                            'end_date',
                            active,
                            active_semesters,
                            activeIndex
                          );
                        }}
                        title={t('events.end_date')}
                        minDate={
                          start_date !== ''
                            ? new Date(start_date)
                            : academicStartDate !== ''
                            ? new Date(academicStartDate)
                            : ''
                        }
                        maxDate={academicEndDate !== '' ? new Date(academicEndDate) : null}
                      />

                      {/* <DateInput
                          title="End date"
                          value={String(end_date).slice(0, 10)}
                          min={
                            start_date !== ""
                              ? String(start_date).slice(0, 10)
                              : academicStartDate !== ""
                              ? academicStartDate.slice(0, 10)
                              : ""
                          }
                          max={
                            academicEndDate !== ""
                              ? academicEndDate.slice(0, 10)
                              : ""
                          }
                          edit={(value) => {
                            setLevelConfig({
                              type: "ON_INPUT",
                              payload: value,
                              name: "end_date",
                              year: active,
                              semester: active_semesters,
                              indexPlace: activeIndex,
                            });
                          }}
                          onBlur={() =>
                            dataAlign(
                              start_date,
                              end_date,
                              level_no,
                              term,
                              calendarSetLevelDate,
                              "end_date",
                              active,
                              active_semesters,
                              activeIndex
                            )
                          }
                        /> */}
                    </div>

                    <div className="col-md-12 col-lg-12  col-xl-6 col-sm-12 col-12">
                      <div className="row">
                        <div className={rotation === 'yes' ? 'col-md-6' : 'col-md-12'}>
                          {/* {start_date !== "" && end_date !== "" && (
                            <b className="text-left f-14  mb-0">
                              Level start date and end date are defined as per
                              the institute calendar
                            </b>
                          )} */}
                        </div>

                        <div className="col-md-6">
                          {rotation === 'yes' ? (
                            <Fragment>
                              <br />
                              <b className="f-13">{t('no_of_rotations')}</b>
                              <Select
                                name="rotation_count"
                                value={rotation_count}
                                onChange={(e) => {
                                  setLevelConfig({
                                    type: 'ON_INPUT',
                                    payload: e.target.value,
                                    name: e.target.name,
                                    year: active,
                                    semester: active_semesters,
                                    indexPlace: activeIndex,
                                  });
                                  rotationConfirm(
                                    level_no,
                                    term,
                                    e.target.value,
                                    saveInterimRotationCount
                                  );
                                }}
                              >
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                              </Select>
                            </Fragment>
                          ) : (
                            <Null></Null>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* </FlexWrapper> */}

                <EventWrapper of_scroll="auto" className="go-wrapper-width">
                  <EventRows show="title" />
                  <div className="go-wrapper-height">
                    <Fragment>
                      {events && events.length !== 0 && (
                        <Fragment>
                          {events
                            .sort((a, b) => Date.parse(a.start_time) - Date.parse(b.start_time))
                            .map((item, i, arr) => {
                              let editEnable = true;
                              if (
                                id !== null &&
                                institutionCalenderEvents !== undefined &&
                                institutionCalenderEvents.length > 0
                              ) {
                                let filteredEvent = institutionCalenderEvents
                                  .filter((list) => {
                                    return list._id === item._event_id;
                                  })
                                  .reduce((_, el) => {
                                    return el._id;
                                  }, 0);
                                if (filteredEvent !== 0) {
                                  editEnable = false;
                                }
                              }
                              return (
                                <EventRows
                                  key={item._id}
                                  show="content"
                                  content={item}
                                  i={i}
                                  editHide={!editEnable}
                                  edit={() =>
                                    setLevelConfig({
                                      type: 'SHOW_MODAL',
                                      payload: item,
                                      id: item._event_id,
                                      year: active,
                                      index: i,
                                      from: 'events',
                                      level_no: level_no,
                                      term: term,
                                      min: start_date,
                                      max: end_date,
                                      events: arr.filter((item, num) => num !== i),
                                      activeSemester: activeSemester,
                                    })
                                  }
                                  del={() =>
                                    eventDeleteConfirm(
                                      item._event_id,
                                      level_no,
                                      term,
                                      eventInterimDelete
                                    )
                                  }
                                />
                              );
                            })}
                        </Fragment>
                      )}
                    </Fragment>
                  </div>
                </EventWrapper>
              </Fragment>
            );
          })}
        </>
      )}
    </Fragment>
  );
};

LevelField.propTypes = {
  id: PropTypes.string,
  active: PropTypes.string,
  active_semesters: PropTypes.string,
  eventInterimDelete: PropTypes.func,
  eventsState: PropTypes.object,
  currentProgramCalendarId: PropTypes.string,
  isLoading: PropTypes.bool,
  error: PropTypes.object,
  success: PropTypes.object,
  resetInterimMessage: PropTypes.func,
  updateInterimEvents: PropTypes.func,
  saveInterimRotationCount: PropTypes.func,
  interimChangeSemester: PropTypes.func,
  calendarSetLevelDate: PropTypes.func,
  institutionCalenderEvents: PropTypes.object,
  academicStartDate: PropTypes.string,
  academicEndDate: PropTypes.string,
  programId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.object,
};

const mapStateToProps = function (state) {
  // ({ interimCalendar }) => ({

  const { interimCalendar } = state;
  return {
    active: interimCalendar.active_year,
    active_semesters: interimCalendar.active_semesters,
    id: interimCalendar.institution_Calender_Id,
    isLoading: interimCalendar.isLoading,
    error: interimCalendar.error,
    success: interimCalendar.success,
    currentProgramCalendarId: interimCalendar.currentProgramCalendarId,
    institutionCalenderEvents: interimCalendar.academic_year_events,
    academicStartDate: interimCalendar.academic_start_date,
    academicEndDate: interimCalendar.academic_end_date,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, {
  eventInterimDelete,
  updateInterimEvents,

  /*Remove*/

  /*Remove*/

  interimChangeSemester,
  calendarSetLevelDate,
  saveInterimRotationCount,
  resetInterimMessage,
})(LevelField);
