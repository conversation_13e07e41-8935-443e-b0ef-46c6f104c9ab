import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Map } from 'immutable';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';

import Dashboard from './components/Dashboard';
import Program from './components/Program';
import Course from './components/Course';
import ActivityView from './Activity/charts/ActivityView';

import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import { selectLoading, selectMessage } from '../../_reduxapi/reports_and_analytics/selectors';
import { selectIsLoading } from '../../_reduxapi/program_input/selectors';
import './css/reports.css';
import { t } from 'i18next';

class ReportsAndAnalytics extends Component {
  isLoading() {
    return this.props.loading.valueSeq().some((value) => value);
  }

  getLabel() {
    const { location } = this.props;
    const breadcrumbItems = [{ path: 'reports', label: t('side_nav.menus.reports_analytics') }];
    const matched = breadcrumbItems.find((item) => location.pathname.includes(item.path));
    if (!matched) return t('side_nav.menus.reports_analytics');
    return matched.label;
  }

  render() {
    const { message, loadingRep } = this.props;
    const items = [{ to: '#', label: this.getLabel() }];

    return (
      <div>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={this.isLoading()} />
        <Loader isLoading={loadingRep} />
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <Switch>
          <Route exact path="/reports/dashboard" component={Dashboard} />
          <Route
            exact
            path="/reports/programs/:programId/courses/:courseId/:category"
            component={Course}
          />
          <Route
            exact
            path="/reports/programs/:programId/:category/:subCategory"
            component={Program}
          />
          <Route
            exact
            path="/reports/programs/:programId/:category/:subCategory/:userId"
            component={Program}
          />
          <Route exact path="/reports/programs/activity/fullView" component={ActivityView} />
        </Switch>
      </div>
    );
  }
}

ReportsAndAnalytics.propTypes = {
  location: PropTypes.object,
  message: PropTypes.string,
  loading: PropTypes.instanceOf(Map),
  loadingRep: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    loading: selectLoading(state),
    message: selectMessage(state),
    loadingRep: selectIsLoading(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(ReportsAndAnalytics);
