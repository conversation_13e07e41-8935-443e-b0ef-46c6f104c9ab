{"name": "@digival/digischeduler-web", "version": "7.0.10", "private": true, "dependencies": {"@azure/msal-browser": "^3.10.0", "@ckeditor/ckeditor5-react": "^9.3.0", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@fullcalendar/core": "^5.5.1", "@fullcalendar/daygrid": "^5.5.0", "@fullcalendar/interaction": "^5.5.0", "@fullcalendar/list": "^5.5.0", "@fullcalendar/react": "^5.5.0", "@fullcalendar/timegrid": "^5.5.1", "@google-recaptcha/react": "^2.2.0", "@mui/icons-material": "^5.10.9", "@mui/lab": "^5.0.0-alpha.107", "@mui/material": "^5.10.13", "@mui/styles": "^5.10.10", "@mui/x-date-pickers": "^5.0.19", "@react-oauth/google": "^0.12.1", "@szhsin/react-menu": "^1.3.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "ajv": "^8.12.0", "apisauce": "^3.1.0", "axios": "^1.8.4", "bootstrap": "^5.3.3", "browserslist": "^4.16.4", "ckeditor5": "^43.2.0", "ckeditor5-premium-features": "^43.2.0", "crypto-js": "^4.2.0", "date-fns": "^2.9.0", "dompurify": "^3.2.4", "dotenv": "^16.4.5", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "echarts": "^5.0.2", "echarts-for-react": "^3.0.1", "exceljs": "^4.3.0", "face-api.js": "^0.22.2", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "history": "^4.10.1", "html2canvas": "^1.0.0-rc.7", "i18next": "^21.6.3", "i18next-browser-languagedetector": "^6.1.2", "i18next-http-backend": "^1.3.1", "immutable": "^4.0.0-rc.12", "js-cookie": "^3.0.1", "jspdf": "^3.0.1", "jspdf-autotable": "^3.5.13", "moment": "^2.24.0", "multiselect-react-dropdown": "^1.6.1", "papaparse": "^5.2.0", "prop-types": "^15.8.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^1.0.1", "react-circular-progressbar": "^2.0.3", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-csv": "^2.0.3", "react-datepicker": "^2.14.1", "react-doc-viewer": "^0.1.5", "react-dom": "^18.3.1", "react-ga4": "^1.0.6", "react-i18next": "^11.15.1", "react-image-lightbox": "^5.1.1", "react-infinite-scroll-hook": "^4.0.0", "react-multi-select-component": "^3.0.6", "react-notifications": "^1.6.0", "react-notifications-component": "^2.4.0", "react-organizational-chart": "^2.2.0", "react-redux": "^7.2.0", "react-responsive-modal": "^6.0.1", "react-router-dom": "^5.3.4", "react-scripts": "^5.0.1", "react-select": "^3.1.1", "react-slick": "^0.27.13", "react-svg": "^15.1.7", "react-switch": "^5.0.1", "read-excel-file": "^5.0.0", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "reselect": "^4.0.0", "slick-carousel": "^1.8.1", "styled-components": "^5.1.1", "sweetalert2": "11.0.0", "thunk": "0.0.1", "webcam-easy": "^1.0.5"}, "scripts": {"start:development": "webpack serve --mode development --open --env envFile=.env.development", "build:development": "webpack --mode production --env envFile=.env.development", "start:staging": "env-cmd -f .env.staging react-scripts --max_old_space_size=4096 start", "build:staging": "env-cmd -f .env.staging react-scripts --max_old_space_size=4096 build", "start:uat": "env-cmd -f .env.uat react-scripts --max_old_space_size=4096 start", "build:uat": "env-cmd -f .env.uat react-scripts --max_old_space_size=4096 build", "start:production": "webpack serve --mode development --open --env envFile=.env.production", "build:production": "webpack --mode production --env envFile=.env.production", "start:ecs-demo": "env-cmd -f .env.ecs-demo react-scripts --max_old_space_size=4096 start", "build:ecs-demo": "env-cmd -f .env.ecs-demo react-scripts --max_old_space_size=4096 build", "start:ecs": "env-cmd -f .env.ecs react-scripts --max_old_space_size=4096 start", "build:ecs": "env-cmd -f .env.ecs react-scripts --max_old_space_size=4096 build", "start:ecs-staging": "env-cmd -f .env.ecs-staging react-scripts --max_old_space_size=4096 start", "build:ecs-staging": "env-cmd -f .env.ecs-staging react-scripts --max_old_space_size=4096 build", "start:ecs-staging-external": "env-cmd -f .env.ecs-staging-external react-scripts --max_old_space_size=4096 start", "build:ecs-staging-external": "env-cmd -f .env.ecs-staging-external react-scripts --max_old_space_size=4096 build", "start:ecs-arabic": "env-cmd -f .env.ecs-arabic react-scripts --max_old_space_size=4096 start", "build:ecs-arabic": "env-cmd -f .env.ecs-arabic react-scripts --max_old_space_size=4096 build", "start:ecs-indian": "env-cmd -f .env.ecs-indian react-scripts --max_old_space_size=4096 start", "build:ecs-indian": "env-cmd -f .env.ecs-indian react-scripts --max_old_space_size=4096 build", "start:bharat": "env-cmd -f .env.bharat react-scripts --max_old_space_size=4096 start", "build:bharat": "env-cmd -f .env.bharat react-scripts --max_old_space_size=4096 build", "start:shanmugha": "env-cmd -f .env.s<PERSON><PERSON><PERSON>a react-scripts --max_old_space_size=4096 start", "build:shanmugha": "env-cmd -f .env.s<PERSON><PERSON><PERSON>a react-scripts --max_old_space_size=4096 build", "start:amc": "env-cmd -f .env.amc react-scripts --max_old_space_size=4096 start", "build:amc": "env-cmd -f .env.amc react-scripts --max_old_space_size=4096 build", "start:upm": "env-cmd -f .env.upm react-scripts --max_old_space_size=4096 start", "build:upm": "env-cmd -f .env.upm react-scripts --max_old_space_size=4096 build", "start:master-oracle": "webpack serve --mode development --open --env envFile=.env.master-oracle", "build:master-oracle": "webpack --mode production --env envFile=.env.master-oracle", "start:master-oracle-aws": "env-cmd -f .env.master-oracle-aws react-scripts --max_old_space_size=4096 start", "build:master-oracle-aws": "env-cmd -f .env.master-oracle-aws react-scripts --max_old_space_size=4096 build", "start:master-oracle-upm": "env-cmd -f .env.master-oracle-upm react-scripts --max_old_space_size=4096 start", "build:master-oracle-upm": "env-cmd -f .env.master-oracle-upm react-scripts --max_old_space_size=4096 build", "start:master-oracle-sla": "webpack serve --mode development --open --env envFile=.env.master-oracle-sla", "build:master-oracle-sla": "webpack --mode production --env envFile=.env.master-oracle-sla", "start:developv2": "env-cmd -f .env.developv2 react-scripts --max_old_space_size=4096 start", "build:developv2": "env-cmd -f .env.developv2 react-scripts --max_old_space_size=4096 build", "start:developv1.1-mc": "env-cmd -f .env.developv1.1-mc react-scripts --max_old_space_size=4096 start", "build:developv1.1-mc": "env-cmd -f .env.developv1.1-mc react-scripts --max_old_space_size=4096 build", "start:sbmch": "webpack serve --mode development --open --env envFile=.env.sbmch", "build:sbmch": "webpack --mode production --env envFile=.env.sbmch", "start:ecs-staging-feature": "env-cmd -f .env.ecs-staging-feature react-scripts --max_old_space_size=4096 start", "build:ecs-staging-feature": "env-cmd -f .env.ecs-staging-feature react-scripts --max_old_space_size=4096 build", "start:ecs-staging-k8s": "env-cmd -f .env.ecs-staging-k8s react-scripts --max_old_space_size=4096 start", "build:ecs-staging-k8s": "env-cmd -f .env.ecs-staging-k8s react-scripts --max_old_space_size=4096 build", "start:staging-oracle-upm": "env-cmd -f .env.staging-oracle-upm react-scripts --max_old_space_size=4096 start", "build:staging-oracle-upm": "env-cmd -f .env.staging-oracle-upm react-scripts --max_old_space_size=4096 build", "start:master-oracle-cors": "env-cmd -f .env.master-oracle-cors react-scripts --max_old_space_size=4096 start", "build:master-oracle-cors": "env-cmd -f .env.master-oracle-cors react-scripts --max_old_space_size=4096 build", "start:ecs-staging1": "webpack serve --mode development --open --env envFile=.env.ecs-staging1", "build:ecs-staging1": "webpack --mode production --env envFile=.env.ecs-staging1", "start:scd": "webpack serve --mode development --open --env envFile=.env.scd", "build:scd": "webpack --mode production --env envFile=.env.scd", "start:gcp-dev": "webpack serve --mode development --open --env envFile=.env.gcp-dev", "build:gcp-dev": "webpack --mode production --env envFile=.env.gcp-dev", "start:rak": "webpack serve --mode development --open --env envFile=.env.rak", "build:rak": "webpack --mode production --env envFile=.env.rak", "start:rak-staging": "webpack serve --mode development --open --env envFile=.env.rak-staging", "build:rak-staging": "webpack --mode production --env envFile=.env.rak-staging", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.24.7", "@svgr/webpack": "^8.1.0", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "dotenv-webpack": "^8.0.1", "env-cmd": "^10.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.21.5", "eslint-webpack-plugin": "^4.2.0", "file-loader": "^6.2.0", "glob": "^10.3.10", "html-webpack-plugin": "^5.6.0", "json-loader": "^0.5.7", "prettier": "2.1.2", "raw-loader": "^4.0.2", "react-refresh": "^0.14.0", "style-loader": "^3.3.4", "svg-react-loader": "^0.4.6", "terser-webpack-plugin": "^5.3.10", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}, "overrides": {"nth-check": "^2.1.1", "braces": "^3.0.3", "postcss": "^8.4.31", "micromatch": "^4.0.8", "minimatch": "^3.0.5", "json5": "^1.0.2", "node-fetch": "^2.6.7", "loader-utils": "^2.0.0", "cross-spawn": "^6.0.6", "shell-quote": "^1.7.3", "xml2js": "^0.5.0", "browserslist": "^4.16.4", "http-proxy-middleware": "^2.0.9", "react-dev-utils": "^12.0.0", "@babel/helpers": "^7.26.10", "@babel/runtime-corejs3": "^7.26.10", "tar-fs": "^2.1.2"}}