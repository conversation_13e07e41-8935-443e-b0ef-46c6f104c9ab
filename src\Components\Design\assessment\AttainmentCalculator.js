import { Divider } from '@mui/material';
import React from 'react';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Checkbox from '@mui/material/Checkbox';

function AttainmentCalculator(props) {
  const programType = [
    {
      name: 'All',
      value: 'All',
    },
    {
      name: 'undergraduate',
      value: 'undergraduate',
    },
    {
      name: 'postgraduate',
      value: 'postgraduate',
    },
    {
      name: 'pre-requisite',
      value: 'pre-requisite',
    },
  ];

  return (
    <div className="main pb-5">
      <div className="bg-white p-3 border-bottom-2px">
        <div className="container-fluid">
          <p className="font-weight-bold mb-0 text-left f-17">
            <i className="fa fa-arrow-left pr-3 remove_hover" aria-hidden="true"></i> Back
          </p>
        </div>
      </div>

      <div className="container mt-4">
        <p className="f-20 mb-0 pl-3 ml-4  bold"> All Courses</p>
        <div className="d-flex justify-content-between pb-2">
          <div className="col-md-2">
            <div className="pl-4 pt-4 mt-2">
              <MaterialInput elementType={'materialSearch'} placeholder={'Search'} />
            </div>
          </div>
          <div className="col-md-8">
            <div className="d-flex justify-content-end">
              <div className="col-md-3">
                <MaterialInput
                  elementType={'materialSelect'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: programType }}
                  label={'Academic Year'}
                />
              </div>
              <div className="col-md-2">
                <MaterialInput
                  elementType={'materialSelect'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: programType }}
                  label={'Year'}
                />
              </div>
              <div className="col-md-2">
                {' '}
                <MaterialInput
                  elementType={'materialSelect'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: programType }}
                  label={'Level'}
                />
              </div>
              <div className="col-md-2">
                {' '}
                <MaterialInput
                  elementType={'materialSelect'}
                  type={'text'}
                  variant={'outlined'}
                  size={'small'}
                  elementConfig={{ options: programType }}
                  label={'Course Type'}
                />
              </div>
            </div>
          </div>
        </div>
        <Divider />

        <div className="mb-3">
          <div className="pl-4 ml-3 mt-3">
            <h6>Year 1, Level 1</h6>
            <p className="f-12 mb-0">2018 - 2019</p>
          </div>
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
        </div>
        <div className="mb-3">
          <div className="pl-4 ml-3 mt-3">
            <h6>Year 1, Level 1</h6>
            <p className="f-12 mb-0">2018 - 2019</p>
          </div>
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
        </div>
        <div className="mb-5">
          <div className="pl-4 ml-3 mt-3">
            <h6>Year 1, Level 1</h6>
            <p className="f-12 mb-0">2018 - 2019</p>
          </div>
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
          <div className="d-flex justify-content-between">
            <div className="pt-2 pl-4 ml-2">
              <Checkbox />
            </div>
            <div className="col-md-4 pt-3">Course Code - Name</div>
            <div className="col-md-7 pt-3">Standrad</div>
          </div>
          <Divider />
        </div>
      </div>
    </div>
  );
}

export default AttainmentCalculator;
