import React, { useState, useContext, useRef } from 'react';
import { Trans } from 'react-i18next';
import { useHistory, useRouteMatch } from 'react-router-dom';
import CourseMaster from 'Assets/courseMaster.png';
import MButton from 'Widgets/FormElements/material/Button';
import { independentCourseContext, courseMasterContext } from '../../context';
import { checkCourseValidation } from '../../ICutil';
import { eString } from 'utils';
import AddCourseModal from '../Modal/AddCourseModal';
function AddCourseMaster() {
  const { getToolTipData, institutionId, independentOverview } = useContext(
    independentCourseContext
  );
  const { addIndependentCourse, getIndependentSubjectsList, getCourseList, setData } = useContext(
    courseMasterContext
  );
  const history = useHistory();
  const match = useRouteMatch();
  const [clicked, onClicked] = useState(false);
  const saveDetails = useRef();

  const handleClickShow = () => {
    getIndependentSubjectsList(institutionId);
    onClicked(true);
  };
  const onClickClose = () => onClicked(false);
  const onClickSave = (type) => {
    let constructedData = saveDetails.current();
    constructedData = {
      _institution_id: institutionId,
      ...constructedData,
    };
    if (checkCourseValidation(constructedData, setData, getToolTipData)) {
      const callBack = (type, courseID) => {
        const goToConfig = (courseID) =>
          history.push(`${match.url}/configuration?cid=${eString(courseID)}`);
        onClickClose();
        type === 'continue' ? goToConfig(courseID) : getCourseList({});
      };
      addIndependentCourse(constructedData, callBack, type);
    }
  };
  if (!getToolTipData.course && getToolTipData.course === '') return '';
  if (clicked)
    return (
      <AddCourseModal show={clicked} onClose={onClickClose} onSave={{ onClickSave, saveDetails }} />
    );
  const isSessionAndDeliveryAdded =
    independentOverview.get('sessionTypesCount', 0) === 0 ||
    independentOverview.get('deliveryTypesCount', 0) === 0;
  return (
    <div className="mt-5 d-flex justify-content-center">
      <div className="col-md-11 col-lg-6 col-xl-5 col-10 text-center">
        <img src={CourseMaster} alt="transparent" />
        <p className="f-27 pt-3 text-dark mb-1">
          <Trans
            components={{
              course: getToolTipData.course,
            }}
          >
            independentCourse.addNew
          </Trans>
        </p>
        <p className="f-14 pt-1 text-dark">
          <Trans
            components={{
              course: getToolTipData.course,
              department: getToolTipData.department,
            }}
          >
            independentCourse.directlyRun
          </Trans>
        </p>
        <div>
          <div>
            <MButton fullWidth clicked={handleClickShow} disabled={isSessionAndDeliveryAdded}>
              <div className="d-flex">
                <Trans i18nKey={'configuration.add_new'} />
                <span className="ml-1">
                  <Trans
                    className="ml-1"
                    components={{
                      course: getToolTipData.course,
                    }}
                  >
                    independentCourse.course
                  </Trans>
                </span>
              </div>
            </MButton>
          </div>
          {isSessionAndDeliveryAdded && (
            <p className="mt-3 f-12">
              <i className="fa fa-info-circle mr-1"></i>{' '}
              <Trans
                components={{
                  course: getToolTipData.course,
                }}
              >
                before_creating_new_course
              </Trans>
              {', '}
              <span
                className="digi-blue font-weight-bold icon-blue"
                onClick={() => {
                  history.push(`${match.url.replace('/course-master-list', '/session-types')}`);
                }}
              >
                <Trans i18nKey={'independentCourse.sessionAndDeliveryType'} />
              </span>{' '}
              <Trans i18nKey={'need_to_be_configured'} />
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default AddCourseMaster;
