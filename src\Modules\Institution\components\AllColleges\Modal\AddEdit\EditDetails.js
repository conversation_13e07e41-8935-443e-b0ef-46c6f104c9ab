import React /* useContext */ from 'react';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';

import Vector from '../../../../../../Assets/vector.png';

//import UniversityContext from '../../Context/university-context';
function EditDetails({ updatedDetails, setUpdatedDetails }) {
  // const details = useContext(UniversityContext);
  //const { updateDetails } = details;
  const { name, code, admin, address, country, district, zipCode, city, state } = updatedDetails;
  return (
    <div className="col-md-12 pt-2">
      <div className="mt-3">
        <label className="form-label">
          <Trans i18nKey={'add_colleges.college_name'}></Trans>
        </label>
        <TextField
          type="text"
          placeholder="Type Your College Name"
          variant="outlined"
          size="small"
          fullWidth
          onChange={(e) => setUpdatedDetails({ ...updatedDetails, name: e.target.value })}
          value={name}
        />
      </div>

      <div className="col-md-5 col-sm-4 mt-3 p-0">
        <label className="form-label">
          <Trans i18nKey={'add_colleges.college_code'}></Trans>
        </label>
        <TextField
          type="text"
          placeholder="VIT - 1254"
          variant="outlined"
          size="small"
          fullWidth
          onChange={(e) => setUpdatedDetails({ ...updatedDetails, code: e.target.value })}
          value={code}
        />
      </div>

      <div className="col-md-5 col-sm-4 mt-3 p-0">
        <FormControl fullWidth variant="outlined" size="small">
          <label className="form-label">
            <Trans i18nKey={'add_colleges.assign_admin'}></Trans>
          </label>
          <Select
            labelId="term-label"
            onChange={(e) => setUpdatedDetails({ ...updatedDetails, admin: e.target.value })}
            value={admin}
            native
          >
            <option value=""></option>
            <option>Dr Rashad Khan</option>
          </Select>
        </FormControl>
      </div>

      <h6 className="font-weight-normal mt-5 ml-0 mb-0">
        <img className="pr-2" alt="" src={Vector} />{' '}
        <Trans i18nKey={'add_colleges.address_details'}></Trans>
      </h6>
      <div className="mt-3">
        <label className="form-label">
          <Trans i18nKey={'add_colleges.address_line1'}></Trans>
        </label>
        <TextField
          type="text"
          placeholder="Type your Complete Address"
          variant="outlined"
          size="small"
          fullWidth
          onChange={(e) => setUpdatedDetails({ ...updatedDetails, address: e.target.value })}
          value={address}
        />
      </div>
      <div className="row">
        <div className="col-md-6">
          <div className="mt-3">
            <FormControl fullWidth variant="outlined" size="small">
              <label className="form-label">
                <Trans i18nKey={'add_colleges.country'}></Trans>
              </label>
              <Select
                labelId="term-label"
                onChange={(e) => setUpdatedDetails({ ...updatedDetails, country: e.target.value })}
                value={country}
                native
              >
                <option value=""></option>
                <option>Country</option>
              </Select>
            </FormControl>
          </div>
          <div className="mt-3">
            <FormControl fullWidth variant="outlined" size="small">
              <label className="form-label">
                <Trans i18nKey={'add_colleges.district'}></Trans>
              </label>
              <Select
                labelId="term-label"
                onChange={(e) => setUpdatedDetails({ ...updatedDetails, district: e.target.value })}
                value={district}
                native
              >
                <option value=""></option>
                <option>District</option>
              </Select>
            </FormControl>
          </div>
          <div className="mt-3">
            <FormControl fullWidth variant="outlined" size="small">
              <label className="form-label">
                <Trans i18nKey={'add_colleges.zip_code'}></Trans>
              </label>
              <TextField
                type="text"
                placeholder="ZipCode"
                variant="outlined"
                size="small"
                onChange={(e) => setUpdatedDetails({ ...updatedDetails, zipCode: e.target.value })}
                value={zipCode}
              />
            </FormControl>
          </div>
        </div>

        <div className="col-md-6">
          <div className="mt-3">
            <FormControl fullWidth variant="outlined" size="small">
              <label className="form-label">
                <Trans i18nKey={'add_colleges.state_region'}></Trans>
              </label>
              <Select
                labelId="term-label"
                onChange={(e) => setUpdatedDetails({ ...updatedDetails, state: e.target.value })}
                value={state}
                native
              >
                <option value=""></option>
                <option>State</option>
              </Select>
            </FormControl>
          </div>
          <div className="mt-3">
            <FormControl fullWidth variant="outlined" size="small">
              <label className="form-label">
                <Trans i18nKey={'add_colleges.city'}></Trans>
              </label>
              <Select
                labelId="term-label"
                onChange={(e) => setUpdatedDetails({ ...updatedDetails, city: e.target.value })}
                value={city}
                native
              >
                <option value=""></option>
                <option>City</option>
              </Select>
            </FormControl>
          </div>
        </div>
      </div>
    </div>
  );
}
EditDetails.propTypes = {
  setUpdatedDetails: PropTypes.func,
  updatedDetails: PropTypes.object,
};
export default EditDetails;
