import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Typography,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { List, Map } from 'immutable';
import { levelRename } from 'utils';
import WarningSign from 'Assets/yellow-warning-sign.png';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #EB5757',
};
const titleIconSx = {
  padding: '8px',
  backgroundColor: '#FEE2E2',
  borderRadius: '50%',
  marginRight: '8px',
  color: '#EB5757',
};
const confirmMsgSx = {
  fontWeight: 500,
  color: '#4B5563',
};
const checkboxSx = {
  color: '#6B7280',
};
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};

const DeleteConfirmModal = ({ open, data, isGrouped, studentIds, handleClose, handleDelete }) => {
  const [isConfirmed, setIsConfirmed] = useState(false);
  const programId = data.get('programId', '');
  const type = data.get('type', '');
  const selectedType = type === 'level' ? levelRename('Level', programId).toLowerCase() : type;

  const handleConfirm = () => {
    handleDelete({ ...(isGrouped && { removeCompleteCourse: isConfirmed }) });
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center">
          <Box display="flex" sx={titleIconSx}>
            <DeleteOutlineIcon />
          </Box>
          Delete Selected Student(s)
        </Box>
      </DialogTitle>
      <DialogContent className="p-3">
        <p className="mb-2 gray-neutral">You’ve selected {studentIds.size} student(s) to delete.</p>
        {isGrouped ? (
          <>
            {/* <Typography sx={confirmMsgSx}>
              Do you want to completely remove them from the {selectedType}?
            </Typography> */}
            <FormControlLabel
              control={<Checkbox sx={checkboxSx} />}
              className="gray-neutral mb-0"
              label={`Remove student(s) completely from this ${selectedType}`}
              checked={isConfirmed}
              onChange={(e) => setIsConfirmed(e.target.checked)}
            />
            <p className="gray-neutral">
              <img src={WarningSign} width={22} className="delete-confirm-warning-img mr-1" />
              <Box component="span" className="bold" sx={{ color: '#DC2626' }}>
                Warning:
              </Box>{' '}
              If checked, student(s) will be permanently removed from all scheduled and completed
              sessions. This action cannot be undone.
            </p>
          </>
        ) : (
          <Typography mt={2} mb={1} sx={confirmMsgSx}>
            Are you sure you want to delete the selected student(s)?
          </Typography>
        )}
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" sx={buttonSx} clicked={handleClose} fullWidth>
          Cancel
        </MButton>
        <MButton
          variant="contained"
          color="error"
          className="ml-3"
          sx={{ ...buttonSx, backgroundColor: '#EB5757' }}
          clicked={handleConfirm}
          fullWidth
        >
          Confirm Delete ({studentIds.size})
        </MButton>
      </DialogActions>
    </Dialog>
  );
};

DeleteConfirmModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  isGrouped: PropTypes.bool,
  studentIds: PropTypes.instanceOf(List),
  handleClose: PropTypes.func,
  handleDelete: PropTypes.func,
};

export default DeleteConfirmModal;
