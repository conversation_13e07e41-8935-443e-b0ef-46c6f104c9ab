import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Form } from 'react-bootstrap';
import { List, Map } from 'immutable';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { TYPES, getOptions, getFormattedMapList, removeDeletedPloClo } from './utils';
import { getURLParams, indVerRename, getEnvLabelChanged } from '../../../../utils';
import Input from '../../../../Widgets/FormElements/Input/Input';
import Tooltip from '../../../../_components/UI/Tooltip/Tooltip';
import * as actions from '../../../../_reduxapi/program_input/action';
import { capitalize, getRandomId } from '../../../InfrastructureManagement/utils';
import {
  selectImpactMapList,
  selectMappingMatrix,
} from '../../../../_reduxapi/program_input/selectors';
import { Trans } from 'react-i18next';
import { getLang } from '../../../../utils';
import { t } from 'i18next';
const lang = getLang();
class PloCloMap extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: 0,
      activeTabData: Map(),
      ploDomainIndexes: List(),
      mappedClo: Map(),
      program_id: getURLParams('_id', true),
      program_name: getURLParams('_n', true),
      curriculum_id: getURLParams('_curriculum_id', true),
      curriculum_name: getURLParams('_curriculum_name', true),
      course_id: getURLParams('_course_id', true),
      course_name: getURLParams('_course_name', true),
      framework_name: getURLParams('_framework_name', true),
      mapping_type: getURLParams('_mapping_type'),
    };
    this.setActiveTab = this.setActiveTab.bind(this);
    this.updateMatrixData = this.updateMatrixData.bind(this);
  }

  componentDidMount() {
    const { mappingMatrix, history } = this.props;
    if (mappingMatrix.isEmpty()) {
      return history.goBack();
    }
    this.setActiveTab(mappingMatrix.get(0, Map()), 0);
    this.props.getMapTypeLists('impact', false);
  }

  setActiveTab(data, index) {
    const matrixData = removeDeletedPloClo(data);
    this.setState({
      activeTab: index,
      activeTabData: matrixData,
      ploDomainIndexes: this.getPLODomainIndexes(matrixData),
      mappedClo: this.getMappedCLOs(matrixData),
    });
  }

  getCLOAndPLO(type, activeTabData) {
    return activeTabData.getIn(['matrix', `${type}s`], List()).reduce((acc, data) => {
      return acc.concat(data.get(type, List()).map((d) => d.set('_domain_id', data.get('_id'))));
    }, List());
  }

  getPLODomainIndexes(activeTabData) {
    return activeTabData.getIn(['matrix', `${TYPES.PLO}s`], List()).reduce((acc, data, index) => {
      const count = data.get(TYPES.PLO, List()).size;
      return acc.push(index === 0 ? count : acc.get(index - 1) + count);
    }, List());
  }

  getMappedCLOs(activeTabData) {
    const mappedClos = activeTabData
      .getIn(['matrix', `${TYPES.PLO}s`], List())
      .reduce((acc, data) => {
        return acc.concat(
          data.get(TYPES.PLO, List()).reduce((ploAcc, plo) => {
            const ploId = plo.get('_id');
            return ploAcc.concat(
              plo.get(`${TYPES.CLO}s`, List()).reduce((cloAcc, clo) => {
                return cloAcc.push(clo.set('plo_id', ploId));
              }, List())
            );
          }, List())
        );
      }, List());
    return mappedClos.reduce(
      (acc, clo) => acc.set(`${clo.get('plo_id')},${clo.get('clo_id')}`, clo),
      Map()
    );
  }

  handleBack() {
    this.props.history.goBack();
  }

  updateMatrixData(data) {
    const { activeTab, activeTabData } = this.state;
    const { mappingMatrix } = this.props;
    const plos = activeTabData.getIn(['matrix', 'plos'], List());
    const domainIndex = plos.findIndex((domain) => domain.get('_id') === data.domain_id);
    if (domainIndex === -1) return;
    const ploIndex = plos
      .getIn([domainIndex, 'plo'], List())
      .findIndex((plo) => plo.get('_id') === data.plo_id);
    if (ploIndex === -1) return;
    const clos = plos.getIn([domainIndex, 'plo', ploIndex, 'clos'], List());
    const closPath = [activeTab, 'matrix', 'plos', domainIndex, 'plo', ploIndex, 'clos'];
    if (!data.clo_id) {
      const updatedMappingData = mappingMatrix.setIn(
        closPath,
        clos.push(Map(data.clo).set('_id', getRandomId()))
      );
      this.props.setData(
        Map({
          mappingMatrix: updatedMappingData,
          message: `${
            ['', 'NONE', 'false'].includes(data.clo.mapped_value)
              ? t('program_input.response_strings.unmapped_successfully')
              : t('program_input.response_strings.mapped_successfully')
          } `,
        })
      );
      this.setActiveTab(updatedMappingData.get(activeTab), activeTab);
      return;
    }
    const cloIndex = clos.findIndex((c) => c.get('_id') === data.clo_id);
    const updatedMappingData = mappingMatrix.setIn(
      [...closPath, cloIndex, 'mapped_value'],
      data.clo.mapped_value
    );
    this.props.setData(
      Map({
        mappingMatrix: updatedMappingData,
        message: `${
          ['', 'NONE', 'false'].includes(data.clo.mapped_value)
            ? t('program_input.response_strings.unmapped_successfully')
            : t('program_input.response_strings.mapped_successfully')
        } `,
      })
    );
    this.setActiveTab(updatedMappingData.get(activeTab), activeTab);
  }

  handleChange = (e, plo, clo, activeTabData) => {
    const { mapping_type, mappedClo } = this.state;
    const { UpdateMapCLOPLO } = this.props;
    const curriculumId = activeTabData.getIn(['matrix', 'curriculum', '_id']);
    const endPoint = `digi_mapping/update_matrix_clo_plo`;
    const value = mapping_type !== 'alignment' ? e.target.value : String(e.target.checked);
    const mappedCloData = mappedClo.get(`${plo.get('_id')},${clo.get('_id')}`, Map());

    const requestBody = {
      curriculum_id: curriculumId,
      domain_id: plo.get('_domain_id'),
      plo_id: plo.get('_id'),
      ...(!mappedCloData.isEmpty() && { clo_id: mappedCloData.get('_id') }),
      clo: {
        clo_id: clo.get('_id'),
        no: clo.get('no'),
        name: clo.get('name'),
        year_id: clo.get('year_id'),
        year_name: clo.get('year_name'),
        level_id: clo.get('level_id'),
        level_name: clo.get('level_name'),
        mapped_value: !value ? 'NONE' : value,
        content_mapped_value: 'NONE',
      },
    };
    UpdateMapCLOPLO(endPoint, requestBody, this.updateMatrixData);
  };

  getMappedValue(plo, clo) {
    const mappedValue = this.state.mappedClo.getIn(
      [`${plo.get('_id')},${clo.get('_id')}`, 'mapped_value'],
      ''
    );
    return mappedValue === 'NONE' ? '' : mappedValue;
  }

  getSelectBackgroundColorClass(value) {
    const classNameMap = {
      H: 'bg_blue_H',
      M: 'bg_blue_M',
      L: 'bg_blue_L',
    };
    return !getEnvLabelChanged() ? classNameMap[value] || '' : '';
  }

  render() {
    const { mappingMatrix, impactMapList } = this.props;
    const {
      activeTabData,
      activeTab,
      ploDomainIndexes,
      mapping_type,
      mappedClo,
      curriculum_name,
      framework_name,
      course_name,
      program_name,
    } = this.state;

    const PLO = this.getCLOAndPLO(TYPES.PLO, activeTabData);
    const CLO = this.getCLOAndPLO(TYPES.CLO, activeTabData);
    const programId = getURLParams('_id', true);
    return (
      <React.Fragment>
        <div className="main bg-gray pb-5">
          <div className="bg-white pt-3 pb-3">
            <div className="container-fluid">
              <p className="font-weight-bold text-left mb-0 f-17">
                <i
                  className="fa fa-arrow-left pr-3 cursor-pointer"
                  aria-hidden="true"
                  onClick={() => this.handleBack()}
                ></i>
                <Trans i18nKey={'map'}></Trans>{' '}
                <Trans
                  i18nKey={'plo_clo'}
                  values={{
                    CLO: indVerRename('CLO', programId),
                    PLO: indVerRename('PLO', programId),
                  }}
                ></Trans>
              </p>
            </div>
          </div>

          <div className="bg-gray">
            <div className="container-fluid">
              <div className="row pb-4 justify-content-center">
                <div className="col-md-10">
                  <div className="p-4">
                    <div className="d-flex justify-content-between">
                      <h5 className="pb-2 pt-2">
                        {program_name} / {framework_name} / {curriculum_name} / {course_name} /{' '}
                        {capitalize(mapping_type)} <Trans i18nKey={'map'}></Trans>
                      </h5>
                    </div>

                    <div className="pb-4">
                      <div className="bg-white border-radious-8 p-3">
                        {' '}
                        <p className={`f-16 mb-2 ${lang === 'ar' ? 'text-left' : ''}`}>
                          {' '}
                          <Trans i18nKey={'selected_course'}></Trans>: <b>{course_name}</b>
                        </p>
                        <div className="bg-gray rounded p-1">
                          <div className="p-3">
                            <div className="program_circle_header">
                              <div className="justify-content-center p-3 row">
                                <div className="digi-brown bold f-16 d-flex">
                                  <div className="mr-3 digi-circle-data">1</div>
                                  <Trans i18nKey={'selected_mapping'}></Trans> &amp;{' '}
                                  <Trans i18nKey={'mapping_type'}></Trans>
                                </div>
                                <span className="digi-bdr"></span>
                                <div className="f-16 text-skyblue digi-font-500 d-flex">
                                  <div className="mr-3 digi-circle-data circle-active">2</div>
                                  <Trans
                                    i18nKey={'clo_plo_mapping'}
                                    values={{
                                      CLO: indVerRename('CLO', programId),
                                      PLO: indVerRename('PLO', programId),
                                    }}
                                  ></Trans>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className={`program_tabheader ${lang === 'ar' ? 'text-left' : ''}`}>
                            <ul className="program_menu">
                              {mappingMatrix &&
                                mappingMatrix.map((data, index) => {
                                  return (
                                    <span
                                      key={`${data.get('tabName')}-${index}`}
                                      className={`mb-0 mt-2 cursor-pointer program_tab ${
                                        activeTab === index ? 'active' : ''
                                      }`}
                                      onClick={() => this.setActiveTab(data, index)}
                                    >
                                      {data.get('tabName')}
                                    </span>
                                  );
                                })}
                            </ul>
                          </div>

                          <div className="program-table border-bottom">
                            <table className="table">
                              <thead className="border_bootom">
                                <tr>
                                  <th className="bg-lightdark">
                                    <div className="aw-75">{indVerRename('PLO', programId)} ##</div>
                                  </th>

                                  {PLO.map((plo) => (
                                    <th key={plo.get('_id')}>
                                      <Tooltip title={plo.get('name')}>
                                        <div className="aw-50">{plo.get('no', '')}</div>
                                      </Tooltip>
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody className="program-table-height">
                                {CLO.map((clo, i) => (
                                  <tr key={clo.get('_id')} className="tr-change">
                                    <td className="text-skyblue bg-lightdark border_all_bottom">
                                      <Tooltip title={clo.get('name')}>
                                        <div className="aw-75 f-14 pt-4 bold">
                                          {`${indVerRename('CLO', programId)} ${clo.get('no')}`}
                                        </div>
                                      </Tooltip>
                                    </td>
                                    {PLO.map((plo, j) => (
                                      <td
                                        key={`${plo.get('_id')}-${i}-${j}`}
                                        className={`border_all_bottom vertical-align-middle ${
                                          ploDomainIndexes.includes(j + 1)
                                            ? 'border_right_dark_left'
                                            : ''
                                        }`}
                                      >
                                        <div className="aw-50">
                                          {mapping_type === 'alignment' && (
                                            <Form.Check
                                              type="checkbox"
                                              className="ml-2 bold"
                                              onChange={(e) => {
                                                if (
                                                  CheckPermission(
                                                    'pages',
                                                    'Program Input',
                                                    'Programs',
                                                    'Add Program'
                                                  ) ||
                                                  CheckPermission(
                                                    'pages',
                                                    'Program Input',
                                                    'Programs',
                                                    'Add Pre-requisite'
                                                  )
                                                ) {
                                                  this.handleChange(e, plo, clo, activeTabData);
                                                }
                                              }}
                                              checked={
                                                mappedClo.getIn(
                                                  [
                                                    `${plo.get('_id')},${clo.get('_id')}`,
                                                    'mapped_value',
                                                  ],
                                                  ''
                                                ) === 'true'
                                                  ? true
                                                  : false
                                              }
                                            />
                                          )}
                                          {mapping_type === 'impact' && (
                                            <Input
                                              elementType="select"
                                              elementConfig={{
                                                options: getOptions(impactMapList),
                                              }}
                                              value={this.getMappedValue(plo, clo)}
                                              className={`program_select ${this.getSelectBackgroundColorClass(
                                                this.getMappedValue(plo, clo)
                                              )}`}
                                              changed={(e) =>
                                                this.handleChange(e, plo, clo, activeTabData)
                                              }
                                            />
                                          )}
                                        </div>
                                      </td>
                                    ))}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                          {mapping_type === 'impact' && (
                            <div className="p-4 text-align-center">
                              <div className="mb-3 color-black">
                                {getFormattedMapList(impactMapList)}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

PloCloMap.propTypes = {
  history: PropTypes.object,
  mappingMatrix: PropTypes.instanceOf(List),
  UpdateMapCLOPLO: PropTypes.func,
  setData: PropTypes.func,
  getMapTypeLists: PropTypes.func,
  impactMapList: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    impactMapList: selectImpactMapList(state),
    mappingMatrix: selectMappingMatrix(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(PloCloMap);
