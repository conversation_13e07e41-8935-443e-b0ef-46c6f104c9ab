import React, { useState } from 'react';
import PropTypes from 'prop-types';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
// import DeleteIcon from 'Assets/delete_icon_dark.svg';
import { t } from 'i18next';
import { fromJS, List, Map } from 'immutable';
import EditableRow from '../components/StaffUserManagement/EditableRow';
import { vaccinationValidation } from '../utils';
import CancelModal from 'Containers/Modal/Cancel';

const VaccineDetailsModal = ({ show, onClose, vaccineDetailsId, details, handleSave, setData }) => {
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [formValues, setFormValues] = useState({
    vaccineNumber: details.get('vaccineNumber', ''),
    vaccineName: details.get('vaccineName', ''),
    vaccineType: details.get('vaccineType', ''),
    antigenName: details.get('antigenName', ''),
    companyName: details.get('companyName', ''),
    brandName: details.get('brandName', ''),
  });
  const [dosages, setDosages] = useState(details.get('dosageDetails', List()));
  const [boosters, setBoosters] = useState(details.get('boosterDetails', List()));
  const [edited, setEdited] = useState(false);

  const handleChange = (e, name) => {
    let value = e.target.value;
    if (name === 'noOfDosage' || name === 'noOfBooster') {
      let i;
      let newArr = [];
      value = value ? parseInt(value) : 0;
      const labelName = name === 'noOfDosage' ? 'Dose' : 'Booster';
      for (i = 1; i <= value; i++) newArr.push({ labelName: `${labelName} ${i}`, days: 0 });
      name === 'noOfDosage' ? setDosages(fromJS(newArr)) : setBoosters(fromJS(newArr));
    } else setFormValues({ ...formValues, [name]: value });
    setEdited(true);
  };

  const handleAddDosage = (labelName) => {
    if (labelName === 'Dose')
      setDosages(dosages.push(fromJS({ labelName: `${labelName} ${dosages.size + 1}`, days: 0 })));
    else
      setBoosters(
        boosters.push(fromJS({ labelName: `${labelName} ${boosters.size + 1}`, days: 0 }))
      );
    setEdited(true);
  };

  const handleRowValue = (e, name, type, i) => {
    let value = e.target.value;
    value = name === 'days' ? (value ? parseInt(value) : 0) : value;
    if (type === 'dose') setDosages(dosages.setIn([i, name], value));
    else setBoosters(boosters.setIn([i, name], value));
    setEdited(true);
  };

  const handleDeleteRow = (type, i) => {
    if (type === 'dose') setDosages(dosages.delete(i));
    else setBoosters(boosters.delete(i));
    setEdited(true);
  };

  const isDisableSave = () => {
    return Object.entries(formValues).some(([key, value]) => !value.trim());
  };

  const onClickSave = () => {
    const operation = vaccineDetailsId ? 'update' : 'add';
    const dosageDetails = dosages
      .toJS()
      .map(({ labelName, days }) => ({ labelName: labelName.trim(), days }));
    const boosterDetails = boosters
      .toJS()
      .map(({ labelName, days }) => ({ labelName: labelName.trim(), days }));
    const data = {
      vaccineDetails: {
        vaccineNumber: formValues.vaccineNumber.trim(),
        vaccineName: formValues.vaccineName.trim(),
        vaccineType: formValues.vaccineType.trim(),
        antigenName: formValues.antigenName.trim(),
        companyName: formValues.companyName.trim(),
        brandName: formValues.brandName.trim(),
        noOfDosage: String(dosages.size),
        noOfBooster: String(boosters.size),
        dosageDetails,
        boosterDetails,
      },
    };
    if (vaccinationValidation(data.vaccineDetails, setData)) handleSave(operation, data);
  };

  const handleCancel = () => {
    edited ? setShowCancelModal(true) : onClose();
  };

  return (
    <>
      <DialogModal show={show} onClose={handleCancel}>
        <div className="p-4">
          <div className="digi-text-color">
            <p className="f-22">
              <Trans i18nKey={'global_configuration.vaccination_configuration'} />
            </p>

            <div>
              <div className="row">
                <div className="col-12 col-md-6 mb-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    label={<Trans i18nKey={'global_configuration.vaccine_number'} />}
                    labelclass={'mb-1 f-14'}
                    placeholder={t('global_configuration.vaccine_number')}
                    value={formValues.vaccineNumber}
                    changed={(e) => handleChange(e, 'vaccineNumber')}
                    maxLength={100}
                    size={'small'}
                  />
                </div>

                <div className="col-12 col-md-6 mb-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    label={<Trans i18nKey={'global_configuration.vaccination_name'} />}
                    labelclass={'mb-1 f-14'}
                    placeholder={t('global_configuration.vaccination_name')}
                    value={formValues.vaccineName}
                    changed={(e) => handleChange(e, 'vaccineName')}
                    maxLength={100}
                    size={'small'}
                  />
                </div>

                <div className="col-12 col-md-6 mb-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    label={<Trans i18nKey={'global_configuration.vaccination_type'} />}
                    labelclass={'mb-1 f-14'}
                    placeholder={t('global_configuration.vaccination_type')}
                    value={formValues.vaccineType}
                    changed={(e) => handleChange(e, 'vaccineType')}
                    maxLength={100}
                    size={'small'}
                  />
                </div>

                <div className="col-12 col-md-6 mb-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    label={<Trans i18nKey={'global_configuration.antigen_name'} />}
                    labelclass={'mb-1 f-14'}
                    placeholder={t('global_configuration.antigen_name')}
                    value={formValues.antigenName}
                    changed={(e) => handleChange(e, 'antigenName')}
                    maxLength={100}
                    size={'small'}
                  />
                </div>
              </div>

              <div className="row my-3">
                <div className="col-12 col-md-6 mb-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    label={<Trans i18nKey={'global_configuration.company_name'} />}
                    labelclass={'mb-1 f-14'}
                    placeholder={t('global_configuration.company_name')}
                    value={formValues.companyName}
                    changed={(e) => handleChange(e, 'companyName')}
                    maxLength={100}
                    size={'small'}
                  />
                </div>

                <div className="col-12 col-md-6 mb-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    label={<Trans i18nKey={'global_configuration.brand_name'} />}
                    labelclass={'mb-1 f-14'}
                    placeholder={t('global_configuration.brand_name')}
                    value={formValues.brandName}
                    changed={(e) => handleChange(e, 'brandName')}
                    maxLength={100}
                    size={'small'}
                  />
                </div>
              </div>

              <div className="row">
                <div className="col-12 col-md-6 mb-2">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'number'}
                    variant={'outlined'}
                    label={<Trans i18nKey={'global_configuration.total_no_of_dosage'} />}
                    labelclass={'mb-1 f-14'}
                    placeholder={'00'}
                    value={String(dosages.size)}
                    size={'small'}
                    // changed={(e) => handleChange(e, 'noOfDosage')}
                    disabled
                  />
                </div>
                {dosages.size < 10 && (
                  <div className="col-12 col-md-6 mb-2 align-self-center">
                    <MButton
                      variant="text"
                      className="text-blue mt-4"
                      clicked={() => handleAddDosage('Dose')}
                    >
                      Add Dosage
                    </MButton>
                  </div>
                )}
              </div>

              {dosages.size > 0 && (
                <div className="digi-border-vaccine digi-pl-12 digi-pr-12 mb-3">
                  <div className="row no-gutters">
                    <div className="col-6 digi-pt-10 digi-pb-10 bold f-14 border-bottom">
                      <Trans i18nKey={'global_configuration.doses'} />
                    </div>
                    <div className="col-6 digi-pt-10 digi-pb-10 bold f-14 pl-3 border-bottom">
                      <Trans i18nKey={'global_configuration.deadline'} />
                    </div>
                  </div>
                  <EditableRow
                    data={dosages}
                    type="dose"
                    handleRowValue={handleRowValue}
                    handleDeleteRow={handleDeleteRow}
                  />
                </div>
              )}

              {!boosters.size ? (
                <MButton
                  variant="text"
                  className="text-blue"
                  clicked={() => handleAddDosage('Booster')}
                >
                  + <Trans i18nKey={'global_configuration.add_booster_dosage'} />
                </MButton>
              ) : (
                <>
                  <div className="row mt-3">
                    <div className="col-12 col-md-6 mb-2">
                      <MaterialInput
                        elementType={'materialInput'}
                        type={'number'}
                        variant={'outlined'}
                        label={<Trans i18nKey={'global_configuration.total_no_of_booster'} />}
                        labelclass={'mb-1 f-14'}
                        placeholder={'00'}
                        value={String(boosters.size)}
                        size={'small'}
                        // changed={(e) => handleChange(e, 'noOfBooster')}
                        disabled
                      />
                    </div>
                    {boosters.size < 10 && (
                      <div className="col-12 col-md-6 mb-2 align-self-center">
                        <MButton
                          variant="text"
                          className="text-blue mt-4"
                          clicked={() => handleAddDosage('Booster')}
                        >
                          Add Booster
                        </MButton>
                      </div>
                    )}
                  </div>

                  {boosters.size > 0 && (
                    <div className="digi-border-vaccine digi-pl-12 digi-pr-12 mb-3">
                      {/* <div className="mt-2 text-right">
                    <img
                      src={DeleteIcon}
                      alt="Delete"
                      className="remove_hover"
                      onClick={() => setBoosters(List())}
                    />
                  </div> */}
                      <div className="row no-gutters">
                        <div className="col-6 digi-pt-10 digi-pb-10 bold f-14 border-bottom">
                          <Trans i18nKey={'global_configuration.boosters'} />
                        </div>
                        <div className="col-6 digi-pt-10 digi-pb-10 bold f-14 pl-3 border-bottom">
                          <Trans i18nKey={'global_configuration.deadline'} />
                        </div>
                      </div>
                      <EditableRow
                        data={boosters}
                        type="booster"
                        handleRowValue={handleRowValue}
                        handleDeleteRow={handleDeleteRow}
                      />
                    </div>
                  )}
                </>
              )}
            </div>

            <div className="text-right mt-4">
              <MButton color="gray" variant="outlined" className="mr-2" clicked={handleCancel}>
                <Trans i18nKey={'cancel'} />
              </MButton>

              <MButton
                color="primary"
                variant="contained"
                clicked={onClickSave}
                disabled={!edited || isDisableSave()}
              >
                <Trans i18nKey={'save'} />
              </MButton>
            </div>
          </div>
        </div>
      </DialogModal>

      <CancelModal showCancel={showCancelModal} setCancel={setShowCancelModal} setShow={onClose} />
    </>
  );
};

VaccineDetailsModal.propTypes = {
  show: PropTypes.bool,
  onClose: PropTypes.func,
  vaccineDetailsId: PropTypes.string,
  details: PropTypes.instanceOf(Map),
  handleSave: PropTypes.func,
  setData: PropTypes.func,
};

export default VaccineDetailsModal;
