import React from 'react';
import EditIcon from '@mui/icons-material/Edit';
import { Map as IMap, List } from 'immutable';
import FormConfigurationRedirect from 'Modules/GlobalConfigurationV2/components/Q360Configuration/Q360/FormConfigurationRedirect';
export default function EditCategoryForm({ categoryForm }) {
  const existingData = IMap({
    formName: categoryForm.get('formName', ''),
    describe: categoryForm.get('describe', ''),
    formId: categoryForm.get('_id', ''),
    matchingForm: categoryForm.get('matchingForm', List()),
    selectedProgram: categoryForm.get('selectedProgram', List()),
  });

  return (
    <div className="">
      <FormConfigurationRedirect existingData={existingData}>
        <EditIcon fontSize="small" />
      </FormConfigurationRedirect>
    </div>
  );
}
