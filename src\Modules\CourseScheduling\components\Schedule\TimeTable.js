import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';
import {
  eachHourOfInterval,
  set,
  format,
  startOfDay,
  getYear,
  getMonth,
  getDate,
  differenceInMinutes,
  isWithinInterval,
  compareAsc,
  getHours,
  getMinutes,
  eachDayOfInterval,
  isValid,
  endOfDay,
  isBefore,
  addHours,
} from 'date-fns';
import Tooltip from '@mui/material/Tooltip';
import Popover from '@mui/material/Popover';
import { makeStyles } from '@mui/styles';

//import { getColor } from './utils';
import { getFormattedGroupName, getStartEndDate } from '../utils';
import {
  capitalize,
  getTranslatedDays,
  getTranslatedDuration,
  getURLParams,
  indVerRename,
  isIndGroup,
  getEnvLabelChanged,
  //isIndVer,
  jsUcfirstAll,
  studentGroupRename,
  getVersionName,
  studentGroupViewList,
} from '../../../../utils';

import MergeIcon from '../../../../Assets/merge.svg';
import '../../css/timetable.css';
import { t } from 'i18next';

const times = eachHourOfInterval({
  start: set(new Date(), { hours: 8, minutes: 0, seconds: 0, milliseconds: 0 }),
  end: set(new Date(), { hours: 17, minutes: 0, seconds: 0, milliseconds: 0 }),
}).map((date) => `${format(date, 'h')} - ${format(addHours(date, 1), 'h  aaa')}`);
const totalColumns = times.length * 4;

const useStylesFunction = makeStyles(() => ({
  paper: {
    border: '2px solid #F9D1FF',
    boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.25)',
  },
}));

function TimeTable({ timeTableData, week, timeTableFor }) {
  const classes = useStylesFunction();
  const [popoverData, setPopoverData] = useState({ anchorEl: null, data: {} });
  const programId = getURLParams('programId', true);
  function handleScheduleClick(event, data) {
    setPopoverData({ anchorEl: event.currentTarget, data });
  }

  function handlePopoverClose() {
    setPopoverData({ anchorEl: null, data: {} });
  }

  function getWorkingDates() {
    const start = startOfDay(new Date(week.get('date')));
    const end = startOfDay(new Date(week.get('endDate')));
    if (!isValid(start) || !isValid(end)) {
      return List();
    }
    return fromJS(
      eachDayOfInterval({ start, end }).reduce((dAcc, d) => {
        // const day = format(d, 'EEEE');
        // const weekDays = isIndVer() ? ['Saturday', 'Sunday'] : ['Friday', 'Saturday'];
        // if (weekDays.includes(day)) return dAcc;
        return dAcc.concat([format(d, 'yyyy-MM-dd').concat('T00:00:00.000Z')]);
      }, [])
    );
  }

  function getPosition(s) {
    const { startHours, startMinutes } = {
      startHours: getHours(s.start),
      startMinutes: getMinutes(s.start),
    };
    const span = s.span;
    const start = Math.round((startHours - 8) * 4 + startMinutes / 15);
    const end = Math.round(start + (span === 1 ? span : span - 1));
    return { start, end };
  }

  function getTransformedGroupedData(groupedData) {
    return groupedData
      .entrySeq()
      .toList()
      .reduce((gAcc, [key, value]) => {
        const groups = value
          .entrySeq()
          .toList()
          .reduce((sgAcc, [sgKey, sgValue]) => {
            sgValue = sgValue.sort((a, b) => compareAsc(a.get('start'), b.get('start'))).toJS();
            const result = [[]];
            let rowIndex = 0;
            let occupied = [Array(totalColumns).fill(0)];

            function incRowIndexIfNoRoom(s) {
              if (occupied[rowIndex].filter((o) => o === 0).length >= s.span) {
                return rowIndex;
              }
              rowIndex = rowIndex + 1;
              if (!occupied[rowIndex]) {
                occupied.push(Array(totalColumns).fill(0));
                result[rowIndex] = [];
              }
              incRowIndexIfNoRoom(s);
            }

            function checkIfItHasSuitablePlaceAndInsert(s) {
              const { start, end } = getPosition(s);
              if (occupied[rowIndex].slice(start, end).every((o) => o === 0)) {
                result[rowIndex].push({
                  ...s,
                  startPosition: start,
                  endPosition: end,
                });
                for (let index = start; index <= end; index++) {
                  occupied[rowIndex][index] = 1;
                }
              } else {
                rowIndex = rowIndex + 1;
                if (!occupied[rowIndex]) {
                  occupied.push(Array(totalColumns).fill(0));
                  result[rowIndex] = [];
                }
                checkIfItHasSuitablePlaceAndInsert(s);
              }
            }
            sgValue.forEach((s) => {
              rowIndex = 0;
              incRowIndexIfNoRoom(s);
              checkIfItHasSuitablePlaceAndInsert(s);
            });
            return sgAcc.push(
              Map({
                groupName: sgKey,
                span: result.length,
                data: fromJS(result),
              })
            );
          }, List());
        return gAcc.push(
          Map({
            date: key.split('T').slice(0, 1).join(''),
            day: format(new Date(key), 'EEEE').toLowerCase(),
            span: groups.reduce((countAcc, g) => g.get('span') + countAcc, 0),
            data: groups,
          })
        );
      }, List());
  }

  function includeScheduleInWorkingHours(s) {
    const start = s.get('start');
    const end = s.get('end');
    const startTime = set(start, { hours: 8, minutes: 0 });
    const endTime = set(start, { hours: 18, minutes: 0 });
    return (
      isWithinInterval(start, { start: startTime, end: endTime }) &&
      isWithinInterval(end, { start: startTime, end: endTime })
    );
  }

  function getExtraCurricularBasedOnDate(scheduleDate) {
    const extraCurricularAndBreak = timeTableData.get('extra_curricular_break_timing', List());
    return extraCurricularAndBreak
      .reduce((acc, e) => {
        if (!e.get('days').includes(format(scheduleDate, 'EEEE').toLowerCase())) {
          return acc;
        }
        const date = {
          year: getYear(scheduleDate),
          month: getMonth(scheduleDate),
          date: getDate(scheduleDate),
        };
        const start = set(getStartEndDate(e.get('startTime')), date);
        const end = set(getStartEndDate(e.get('endTime')), date);
        const durationInMinutes = differenceInMinutes(end, start);
        return acc.push(
          e.merge(
            Map({
              start,
              end,
              durationInMinutes,
              span: durationInMinutes / 15,
              isExtraCurricularAndBreak: true,
            })
          )
        );
      }, List())
      .filter(includeScheduleInWorkingHours)
      .toList();
  }

  function getEventsBasedOnDate(scheduleDate) {
    const events = timeTableData.get('event_list', List());
    return events.reduce((acc, e) => {
      if (e.get('whom') !== 'student') return acc;

      let start = new Date(e.get('start_time'));
      let end = new Date(e.get('end_time'));
      if (!isValid(start) || !isValid(end)) return acc;
      if (
        !isWithinInterval(startOfDay(scheduleDate), {
          start: startOfDay(start),
          end: endOfDay(end),
        })
      ) {
        return acc;
      }
      let [eventStartHours, eventStartMinutes] = [getHours(start), getMinutes(start)];
      let [eventEndHours, eventEndMinutes] = [getHours(end), getMinutes(end)];
      if (format(scheduleDate, 'yyyy-MM-dd') !== format(start, 'yyyy-MM-dd')) {
        eventStartHours = 8;
        eventStartMinutes = 0;
      }
      if (format(scheduleDate, 'yyyy-MM-dd') !== format(end, 'yyyy-MM-dd')) {
        eventEndHours = 18;
        eventEndMinutes = 0;
      }
      if (eventStartHours < 8 && eventEndHours >= 18) {
        eventStartHours = 8;
        eventStartMinutes = 0;
        eventEndHours = 18;
        eventEndMinutes = 0;
      }
      if (eventStartHours >= 8 && eventEndHours >= 18) {
        eventEndHours = 18;
        eventEndMinutes = 0;
      }
      if (eventStartHours < 8 && eventEndHours <= 18) {
        eventStartHours = 8;
        eventStartMinutes = 0;
      }
      if (
        (eventStartHours < 8 && eventEndHours <= 8) ||
        (eventStartHours >= 18 && eventEndHours >= 18) ||
        !(eventStartHours >= 8 && eventEndHours <= 18)
      ) {
        return acc;
      }
      start = set(scheduleDate, { hours: eventStartHours, minutes: eventStartMinutes });
      end = set(scheduleDate, { hours: eventEndHours, minutes: eventEndMinutes });

      if (isBefore(end, start)) {
        return acc;
      }

      const durationInMinutes = differenceInMinutes(end, start);
      return acc.push(
        e.merge(
          Map({
            start,
            end,
            durationInMinutes,
            span: durationInMinutes / 15,
            isExtraCurricularAndBreak: true,
            title: `${e.get('event_name')} (${capitalize(e.get('gender'))})`,
          })
        )
      );
    }, List());
  }

  function transformData() {
    const schedule = timeTableData.get('schedule', List());
    const transformedSchedule = schedule
      .reduce((gAcc, group) => {
        return gAcc.concat(
          group
            .get('schedule', List())
            .filter((s) => !s.get('start', Map()).isEmpty() && !s.get('end', Map()).isEmpty())
            .reduce((sAcc, schedule) => {
              const scheduleDate = startOfDay(new Date(schedule.get('schedule_date')));
              const date = {
                year: getYear(scheduleDate),
                month: getMonth(scheduleDate),
                date: getDate(scheduleDate),
              };
              const start = set(getStartEndDate(schedule.get('start')), date);
              const end = set(getStartEndDate(schedule.get('end')), date);
              const durationInMinutes = differenceInMinutes(end, start);
              return sAcc.push(
                schedule.merge(
                  Map({
                    start,
                    end,
                    durationInMinutes,
                    span: Math.round(durationInMinutes / 15), //manipulate
                    group_id: group.get('_id'),
                    gender: group.get('gender'),
                    group_name: getFormattedGroupName(
                      group.get('group_name'),
                      isIndGroup(group.get('group_name')) ? 1 : 2
                    ),
                  })
                )
              );
            }, List())
        );
      }, List())
      .filter(includeScheduleInWorkingHours)
      .toList();
    const groupedByDate = transformedSchedule.groupBy((s) => s.get('schedule_date'));
    let groupedByStudentGroup = groupedByDate
      .keySeq()
      .toList()
      .reduce((gAcc, key) => {
        let groupedSchedule = groupedByDate.get(key).groupBy((s) => s.get('group_name'));
        const scheduleDate = startOfDay(new Date(key));
        const filteredExtraCurricularAndBreak = getExtraCurricularBasedOnDate(scheduleDate);
        const filteredEvents = getEventsBasedOnDate(scheduleDate);
        groupedSchedule = groupedSchedule
          .entrySeq()
          .toList()
          .reduce((groupedAcc, [gKey, gValue]) => {
            const gender = gKey.slice(0, 1).toLowerCase() === 'm' ? 'male' : 'female';
            // const gender =
            //   gKey.split('-').slice(0, 1).join('').toLowerCase() === 'm' ? 'male' : 'female';
            const filteredExtraCurricularByGender = filteredExtraCurricularAndBreak
              .filter(
                (extraCurricular) =>
                  extraCurricular.get('gender') === 'both' ||
                  extraCurricular.get('gender') === gender
              )
              .toList();
            const filteredEventsByGender = filteredEvents
              .filter((event) => event.get('gender') === 'both' || event.get('gender') === gender)
              .toList();
            return groupedAcc.set(
              gKey,
              gValue.concat(filteredExtraCurricularByGender).concat(filteredEventsByGender)
            );
          }, Map());
        return gAcc.set(key, groupedSchedule);
      }, Map());
    const scheduleDates = groupedByStudentGroup.keySeq().toList();
    groupedByStudentGroup = getWorkingDates().reduce((gAcc, workDate) => {
      if (scheduleDates.includes(workDate)) {
        return gAcc.set(workDate, groupedByStudentGroup.get(workDate));
      }
      const scheduleDate = new Date(workDate);
      const filteredExtraCurricularAndBreak = getExtraCurricularBasedOnDate(scheduleDate)
        .filter((extraCurricular) => extraCurricular.get('gender') === 'both')
        .toList();
      const filteredEvents = getEventsBasedOnDate(scheduleDate);
      return gAcc.set(
        workDate,
        Map({ '-': filteredExtraCurricularAndBreak.concat(filteredEvents) })
      );
    }, Map());
    let transformedData = getTransformedGroupedData(groupedByStudentGroup);
    transformedData = transformedData.sort((a, b) =>
      compareAsc(new Date(a.get('date')), new Date(b.get('date')))
    );
    return transformedData.toJS();
  }

  function getExtraCurricularAndBreakCard(s) {
    return (
      <td className="td-time" colSpan={s.span}>
        <Tooltip
          title={`${getTranslatedDuration(format(s.start, 'hh:mm a'))} - ${getTranslatedDuration(
            format(s.end, 'hh:mm a')
          )}`}
          arrow
          placement="top"
        >
          <div
            className="schedule-card extra-curricular-break-card-daily"
            style={{ backgroundColor: '#E9ECEF' }}
          >
            <div className="extra-curricular-break-align-center">{s.title}</div>
          </div>
        </Tooltip>
      </td>
    );
  }

  function getStudentGroupName(s) {
    return s
      .get('student_groups', List())
      .map((studentGroup) => {
        const studentGroupName = getFormattedGroupName(
          studentGroup.get('group_name', ''),
          isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
        );
        if (['event', 'support_session'].includes(s.get('type', ''))) {
          return studentGroupName;
        }
        const sessionGroupNames = studentGroup
          .get('session_group', List())
          .map((sessionGroup) => getFormattedGroupName(sessionGroup.get('group_name', ''), 3))
          .join(', ');
        return `${sessionGroupNames}`;
      })
      .join(', ');
  }

  function getCardColor(value) {
    const deliveryTypes = getDeliveryTypes();
    const hexColors = ['#F9D1FF', '#D1F4FF', '#D5FFD1', '#FFE2D1', '#FFD1D1', '#D5D1FF'];
    let colorValue = hexColors[0];
    if (deliveryTypes && deliveryTypes.length > 0) {
      const findIndexValue = deliveryTypes.findIndex((item) => item === value);
      colorValue =
        hexColors[findIndexValue] !== undefined ? hexColors[findIndexValue] : hexColors[0];
    }
    return colorValue;
  }

  function getScheduleCard(s, currentRow) {
    const schedule = fromJS(s);
    const scheduleType = schedule.get('type', '');
    const isEventOrSupportSession = ['event', 'support_session'].includes(scheduleType);
    const isRotation = schedule.get('rotation', '') === 'yes';
    const rotationCount = `R${schedule.get('rotation_count', '')}`;

    let title = !isEventOrSupportSession
      ? `${schedule.getIn(['session', 'session_type'], '')} ${schedule.getIn(
          ['session', 'delivery_no'],
          ''
        )}`
      : `${capitalize(schedule.get('title', ''))} / ${capitalize(schedule.get('sub_type', ''))}`;
    if (isRotation) {
      title = `${rotationCount} - ${title}`;
    }
    const subjects = `${schedule
      .get('subjects', List())
      .map((s) => s.get('subject_name', ''))
      .join(', ')}`;

    return (
      <td className="td-time" colSpan={schedule.get('span')}>
        <Tooltip
          title={`${getTranslatedDuration(
            format(schedule.get('start'), 'hh:mm a')
          )} - ${getTranslatedDuration(format(schedule.get('end'), 'hh:mm a'))}`}
          arrow
          placement="top"
        >
          <div
            className="schedule-card cursor-pointer"
            style={{
              backgroundColor: getCardColor(
                schedule.getIn(['session', 'session_type'], '') || schedule.get('type', '')
              ),
            }} //getColor(currentRow)
            onClick={(event) => handleScheduleClick(event, s)}
          >
            <div>
              {schedule.get('merge_status') && (
                <img src={MergeIcon} alt="merged" className="mr-1" />
              )}
              <div className="session-type">
                {schedule.get('type', '').replace('_', ' ') !== 'regular'
                  ? jsUcfirstAll(schedule.get('type', '').replace('_', ' ') + ' / ')
                  : ''}
              </div>
              <div>{title}</div>
              {schedule.get('type', '') !== 'regular'
                ? studentGroupViewList(schedule.get('student_groups', List()), programId)
                    .entrySeq()
                    .map(([groupName, sGroup]) => {
                      return (
                        <div key={groupName} className="mr-2 mb-1">
                          <div>
                            {getFormattedGroupName(studentGroupRename(groupName, programId), 2)}
                            {sGroup.get('delivery_symbol', '') !== '' &&
                              `-${sGroup.get('session_group')}`}
                          </div>
                        </div>
                      );
                    })
                : `, ${studentGroupRename(getStudentGroupName(schedule), programId)}`}
            </div>
            {timeTableFor === 'level' && <div>{schedule.get('course_code', '')}</div>}
            <div>{`${subjects} - ${schedule
              .get('staffs', List())
              .map(
                (staff) =>
                  `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
                    ['staff_name', 'middle'],
                    ''
                  )} ${staff.getIn(['staff_name', 'last'], '')}`
              )
              .join(', ')}`}</div>
            <div>
              {getEnvLabelChanged()
                ? `${capitalize(indVerRename(schedule.get('mode'), programId))}${
                    schedule.get('mode') !== 'remote'
                      ? ' - ' + (schedule.get('infra_name') || 'N/A')
                      : ''
                  }`
                : `${
                    schedule.get('mode') !== 'remote'
                      ? t('side_nav.menus.onsite')
                      : t('side_nav.menus.remote')
                  }${
                    schedule.get('mode') !== 'remote'
                      ? ' - ' + (schedule.get('infra_name') || 'N/A')
                      : ''
                  }`}
            </div>
          </div>
        </Tooltip>
      </td>
    );
  }

  function getPopoverContent() {
    const schedule = fromJS(popoverData.data);
    if (schedule.isEmpty()) return <div />;
    const scheduleType = schedule.get('type', '');
    const isRotation = schedule.get('rotation', '') === 'yes';
    const isEventOrSupportSession = ['event', 'support_session'].includes(scheduleType);
    return (
      <div className="width-400 p-3">
        {isRotation && <h6>{`Rotation - ${schedule.get('rotation_count', '')}`}</h6>}
        <h6>
          {`${schedule.get('course_code', '')} - ${schedule.get('course_name', '')}${getVersionName(
            schedule
          )}`}
        </h6>
        <h6 className="mb-3">
          {schedule.get('merge_status') && <img src={MergeIcon} alt="merged" className="mr-1" />}
          {!isEventOrSupportSession
            ? `${schedule.getIn(['session', 'delivery_symbol'], '')}${schedule.getIn(
                ['session', 'delivery_no'],
                ''
              )} - ${schedule.getIn(['session', 'session_topic'], '')}`
            : `${capitalize(schedule.get('title', ''))} / ${capitalize(
                schedule.get('sub_type', '')
              )}`}
        </h6>
        <div className="popover-content-group">
          <span>{t('TYPE_colon')} </span>
          <span>{jsUcfirstAll(schedule.get('type', '').replace('_', ' '))}</span>
        </div>
        {schedule.get('topic', '') !== '' && (
          <div className="popover-content-group">
            <span>{t('TOPIC_colon')} </span>
            <span>{schedule.get('topic', '')}</span>
          </div>
        )}
        <div className="popover-content-group">
          <span>{t('STUDENT_GROUPS_colon')} </span>
          <span>
            {schedule.get('type', '') !== 'regular'
              ? studentGroupViewList(schedule.get('student_groups', List()), programId)
                  .entrySeq()
                  .map(([groupName, sGroup], index, array) => {
                    return (
                      <span key={groupName} className="mr-2 mb-1">
                        <span>
                          {getFormattedGroupName(studentGroupRename(groupName, programId), 2)}
                          {sGroup.get('delivery_symbol', '') !== '' &&
                            `-${sGroup.get('session_group')}`}
                        </span>
                        {index !== array.size - 1 && ' • '}
                      </span>
                    );
                  })
              : studentGroupRename(getStudentGroupName(schedule), programId)}
          </span>
        </div>
        <div className="popover-content-group">
          <span>{t('DURATION_colon')} </span>
          <span>{`${schedule.get('durationInMinutes')} minutes`}</span>
        </div>
        <div className="popover-content-group">
          <span>{t('SUBJECT_colon')} </span>
          <span>
            {schedule
              .get('subjects', List())
              .map((s) => s.get('subject_name', ''))
              .join(', ')}
          </span>
        </div>
        <div className="popover-content-group">
          <span>{t('MODE_colon')} </span>
          <span>{capitalize(indVerRename(schedule.get('mode', ''), programId))}</span>
        </div>
        <div className="popover-content-group">
          <span>{t('STAFF_colon')} </span>
          <span>
            {schedule
              .get('staffs', List())
              .map(
                (staff) =>
                  `${staff.getIn(['staff_name', 'first'], '')} ${staff.getIn(
                    ['staff_name', 'middle'],
                    ''
                  )} ${staff.getIn(['staff_name', 'last'], '')}`
              )
              .join(', ')}
          </span>
        </div>
        <div className="popover-content-group">
          <span>{t('INFRA_colon')} </span>
          <span>
            {schedule.get('mode') === 'remote' && (
              <span>{schedule.get('infra_name') || 'N/A'}</span>
            )}
            {schedule.get('mode') === 'onsite' && (
              <span>
                {schedule.get('_infra_id') !== undefined &&
                schedule.get('_infra_id') !== null &&
                !schedule.get('_infra_id', Map()).isEmpty()
                  ? `${schedule.getIn(['_infra_id', 'name'], '')}, ${schedule.getIn(
                      ['_infra_id', 'floor_no'],
                      ''
                    )}, ${
                      !schedule.getIn(['_infra_id', 'zone'], List()).isEmpty()
                        ? schedule.getIn(['_infra_id', 'zone'], List()).join(', ')
                        : 'N/A'
                    }, ${schedule.getIn(['_infra_id', 'room_no'], '')}, ${schedule.getIn(
                      ['_infra_id', 'building_name'],
                      ''
                    )}`
                  : 'N/A'}
              </span>
            )}
          </span>
        </div>
      </div>
    );
  }

  function getRows() {
    const transformedTimetable = transformData();
    let currentRow = -1;
    return transformedTimetable.map((g) => {
      const date = g.date;
      const day = g.day;
      return g.data.map((d, i) =>
        d.data.map((grouped, j) => {
          let spanned = 0;
          currentRow = currentRow + 1;
          let cells = grouped
            // .filter((item) => item._id !== '64620ebe19719e41ab5b7a62')
            .filter((item) => item.startPosition < item.endPosition)
            .map((s, index) => {
              const isExtraCurricularAndBreak = s.isExtraCurricularAndBreak;
              if (s.startPosition === 0) {
                spanned = spanned + s.span;
                return !isExtraCurricularAndBreak
                  ? getScheduleCard(s, currentRow)
                  : getExtraCurricularAndBreakCard(s, d.span);
              }
              const hasPrev = index - 1 >= 0;
              // const leftSpan = s.startPosition - (hasPrev ? grouped[index - 1].endPosition + 1 : 0);
              // spanned = spanned + s.span + leftSpan;
              // const leftSpanInt = Math.abs(parseInt(leftSpan));
              const leftSpan = Math.round(
                s.startPosition - (hasPrev ? grouped[index - 1].endPosition + 1 : 0)
              );

              spanned = Math.round(spanned + s.span + leftSpan);
              return (
                <>
                  {Boolean(leftSpan) &&
                    Array(leftSpan)
                      .fill(1)
                      .map((v, index) => <td key={index} className="td-time" colSpan={v}></td>)}
                  {!isExtraCurricularAndBreak
                    ? getScheduleCard(s, currentRow)
                    : getExtraCurricularAndBreakCard(s, d.span)}
                </>
              );
            });
          if (spanned < totalColumns) {
            cells = cells.concat(
              Array(totalColumns - spanned)
                .fill(1)
                .map((v, index) => <td key={index} className="td-time" colSpan={v}></td>)
            );
          }
          return (
            <tr key={`${date}-${d.groupName}-${i}-${j}`}>
              {i === 0 && j === 0 && (
                <td rowSpan={`${g.span}`} className="td-date">
                  <div>
                    <div>
                      {getTranslatedDuration(format(startOfDay(new Date(date)), 'dd MMM yyyy'))}
                    </div>
                    <div>{getTranslatedDays(day)}</div>
                  </div>
                </td>
              )}
              {j === 0 && (
                <td className="td-group" rowSpan={`${d.span}`}>
                  {d.groupName === '-' ? d.groupName : studentGroupRename(d.groupName, programId)}
                </td>
              )}
              {cells}
            </tr>
          );
        })
      );
    });
  }

  function getDeliveryTypes() {
    const deliveryTypes = timeTableData
      .get('schedule', List())
      .filter((item) => item.get('schedule', List()).size > 0)
      .reduce((acc, el) => {
        return acc.concat(
          el
            .get('schedule', List())
            .map((l) => l.getIn(['session', 'session_type'], '') || l.get('type', ''))
        );
      }, List())
      .toJS();
    return deliveryTypes.reduce(
      (unique, item) => (unique.includes(item) ? unique : [...unique, item]),
      []
    );
  }
  const rows = getRows();
  return (
    <>
      <table className="timetable" id="level-course-timetable">
        <thead>
          <tr>
            <th className="th-date">{t('Date')}</th>
            <th className="th-group">{t('role_management.role_actions.Group')}</th>
            {times.map((time) => (
              <th key={time} colSpan="4" className="th-time">
                {getTranslatedDuration(time)}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.length ? (
            rows
          ) : (
            <tr>
              <td colSpan={46} className="text-align-center">
                {t('no_schedule_found')}
              </td>
            </tr>
          )}
        </tbody>
      </table>
      <Popover
        open={Boolean(popoverData.anchorEl)}
        anchorEl={popoverData.anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'center',
          horizontal: 'left',
        }}
        classes={{ paper: classes.paper }}
      >
        {getPopoverContent()}
      </Popover>
    </>
  );
}

TimeTable.propTypes = {
  timeTableData: PropTypes.instanceOf(Map),
  week: PropTypes.instanceOf(Map),
  timeTableFor: PropTypes.string,
};

export default TimeTable;
