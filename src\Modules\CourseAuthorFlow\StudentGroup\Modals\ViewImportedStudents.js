import React, { lazy, Suspense, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Dialog, DialogContent, DialogTitle, IconButton, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import CloseIcon from '@mui/icons-material/Close';
import { List, Map } from 'immutable';
// import ExcelJS from 'exceljs';
// import { saveAs } from 'file-saver';
import MuiPagination from '../Pagination';
// import FilterListIcon from '@mui/icons-material/FilterList';
import AddIcon from '@mui/icons-material/Add';
import { getConstructedStudents, getUserFullName, useCallGroupSettings } from '../Dashboard';
import SearchInput from '../SearchInput';
import { isGenderMerge } from 'utils';

const AddStudentModal = lazy(() => import('./AddStudentModal'));

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
const closeIconSx = {
  position: 'absolute',
  right: 8,
  top: 11,
};
const buttonSx = {
  padding: '4px 16px',
  color: '#0064C8',
  '& .MuiButton-startIcon': {
    marginRight: '4px',
  },
};
const dialogContentSx = {
  padding: '10px 16px 16px',
};
const addIconSx = {
  fontSize: '24px !important',
};
const searchInputSx = {
  '& .MuiInputBase-root': {
    fontSize: 14,
    paddingRight: '8px',
  },
};
// const filterIconSx = {
//   padding: 0,
//   marginLeft: '8px',
//   color: '#0064C8',
// };

const checkIsGrouped = (student) => {
  const groups = student.get('groups', Map());
  if (!groups.size) return false;
  return groups.every((item) => item);
};

const StudentsTable = ({ deliveryGroups, students, paginationData }) => {
  const { pagination, handlePageChange, handleLimitChange } = paginationData;
  const pageNo = pagination.get('pageNo', '');
  const limit = pagination.get('limit', '');
  const totalStudents = students.size;
  const startIndex = (pageNo - 1) * limit;
  const paginatedStudents = students.slice(startIndex, startIndex + limit);

  return (
    <div className="border-radious-8 mt-3">
      <div className="table-responsive border-radious-8">
        <table className="table student-grouping-table import-table">
          <thead>
            <tr>
              <th>Academic ID</th>
              <th>Student Name</th>
              <th>CGPA</th>
              {deliveryGroups.map((group, index) => (
                <th key={index}>{group.get('deliverySymbol', '')}</th>
              ))}
              {/* <th>
                <div className="d-flex align-items-center">
                  Del. Type <FilterListIcon sx={filterIconSx} />
                </div>
              </th> */}
              <th>
                <Box className="d-flex align-items-center" minHeight={24}>
                  Status
                  {/* <IconButton sx={filterIconSx}>
                    <FilterListIcon />
                  </IconButton> */}
                </Box>
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedStudents.size ? (
              paginatedStudents.map((student, index) => (
                <tr key={index}>
                  <td>{student.get('academicId', '')}</td>
                  <td>{getUserFullName(student)}</td>
                  <td>{student.get('mark', '')}</td>
                  {deliveryGroups.map((group, index) => (
                    <td key={index}>
                      {student.getIn(['groups', group.get('deliverySymbol', '')], '') || '-'}
                    </td>
                  ))}
                  <td>
                    {checkIsGrouped(student) ? (
                      <Typography fontSize={14} color="#15803D">
                        Grouped
                      </Typography>
                    ) : (
                      <Typography fontSize={14} color="#DC2626">
                        Ungrouped
                      </Typography>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4 + deliveryGroups.size}>
                  <p className="text-center">No data found</p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalStudents > 10 && (
        <div className="my-2 px-2 gray-neutral">
          <MuiPagination
            totalItems={totalStudents}
            limit={limit}
            page={pageNo}
            handleLimitChange={handleLimitChange}
            handlePageChange={handlePageChange}
            sx={{ fontSize: 14 }}
          />
        </div>
      )}
    </div>
  );
};
StudentsTable.propTypes = {
  deliveryGroups: PropTypes.instanceOf(List),
  students: PropTypes.instanceOf(List),
  paginationData: PropTypes.object,
};

const ViewImportedStudents = ({ open, genderType, data, handleClose, refreshStudents }) => {
  const { groupSettings, callGroupSettings, callImportedStudents } = useCallGroupSettings({
    selectedData: data,
  });
  const [show, setShow] = useState(open);
  const [students, setStudents] = useState(List());
  const [search, setSearch] = useState(Map());
  const [pagination, setPagination] = useState(Map({ pageNo: 1, limit: 10 }));
  const [showAddStudent, setShowAddStudent] = useState(false);
  const genderMerge = isGenderMerge();

  useEffect(() => {
    if (groupSettings.isEmpty()) callGroupSettings();
  }, []);

  const getImportedStudents = () => {
    setStudents(List());
    callImportedStudents({
      params: { studentGender: genderType },
      callback: (res) => {
        const settingId = groupSettings.get('_id', '');
        const constructedData = getConstructedStudents(res, settingId);
        setStudents(constructedData.get('studentList', List()));
      },
    });
  };

  useEffect(() => {
    if (!groupSettings.isEmpty()) getImportedStudents();
  }, [groupSettings]);

  // useEffect(() => {
  //   if (!search.get('isEdited', false)) return;

  //   const searchKey = search.get('value', '');
  //   const timeout = setTimeout(() => {
  //     setPagination((prev) => prev.set('pageNo', 1));
  //     getImportedStudents({ pageNo: 1, searchKey });
  //   }, 1000);

  //   return () => clearTimeout(timeout);
  // }, [search]);

  const filteredStudents = useMemo(() => {
    const searchKey = search.get('value', '').toLowerCase();
    if (!searchKey) return students;

    return students.filter((s) => {
      const academicId = s.get('academicId', '').toLowerCase();
      const name = getUserFullName(s).toLowerCase();
      return academicId.includes(searchKey) || name.includes(searchKey);
    });
  }, [students, search]);

  const sortedStudents = useMemo(() => {
    return filteredStudents.sort((a, b) => {
      // Sort enabled first
      return a.get('isDisabled', false) - b.get('isDisabled', false);
    });
  }, [filteredStudents]);

  const deliveryGroups = useMemo(() => {
    const deliveryGroups = groupSettings.get('deliveryGroups', List());
    if (!deliveryGroups.size) return List();

    const selectedGender = genderMerge ? 'both' : genderType;
    const genderGroup = deliveryGroups.find((group) => group.get('gender') === selectedGender);
    if (!genderGroup) return List();

    return genderGroup
      .get('deliveryTypes', List())
      .flatMap((type) => type.get('selectedType', List()));
  }, [groupSettings]);

  const handleSearch = (value) => {
    setSearch(Map({ isEdited: true, value }));
    setPagination((prev) => prev.set('pageNo', 1));
  };

  const handlePageChange = (_, pageNo) => {
    setPagination((prev) => prev.set('pageNo', pageNo));
  };

  const handleLimitChange = (e) => {
    const limit = parseInt(e.target.value, 10);
    setPagination(Map({ pageNo: 1, limit }));
  };

  const handleShowAddStudent = () => {
    setShow(false);
    setShowAddStudent(true);
  };

  const handleCloseAddStudent = () => {
    setShowAddStudent(false);
    setShow(true);
  };

  const addStudentCallback = () => {
    getImportedStudents();
    refreshStudents();
  };

  return (
    <>
      <Dialog open={show} maxWidth="md" fullWidth>
        <DialogTitle className="border-bottom" sx={titleSx}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <p className="text-capitalize">Import {genderType} Students</p>
            {/* <Box mr={4}>
              <MButton variant="text" sx={buttonSx} clicked={() => {}}>
                Export List
              </MButton>
            </Box> */}
          </Box>
        </DialogTitle>
        <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
          <CloseIcon />
        </IconButton>
        <DialogContent sx={dialogContentSx}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <p className="f-14 gray-neutral text-uppercase">List of students({students.size})</p>
            <Box display="flex" alignItems="center" gap={0.75}>
              <MButton
                variant="text"
                sx={buttonSx}
                startIcon={<AddIcon sx={addIconSx} />}
                clicked={handleShowAddStudent}
                disabled={!groupSettings.get('_id')}
              >
                Add student
              </MButton>
              <Box sx={{ paddingTop: '5px' }}>
                <SearchInput
                  placeholder="Search student"
                  value={search.get('value', '')}
                  changed={handleSearch}
                  sx={searchInputSx}
                />
              </Box>
            </Box>
          </Box>
          <StudentsTable
            deliveryGroups={deliveryGroups}
            students={sortedStudents}
            paginationData={{ pagination, handlePageChange, handleLimitChange }}
          />
        </DialogContent>
      </Dialog>

      {showAddStudent && (
        <Suspense fallback="">
          <AddStudentModal
            open
            data={data}
            handleClose={handleCloseAddStudent}
            refreshStudents={addStudentCallback}
          />
        </Suspense>
      )}
    </>
  );
};

ViewImportedStudents.propTypes = {
  open: PropTypes.bool,
  genderType: PropTypes.string,
  data: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
  refreshStudents: PropTypes.func,
};

export default ViewImportedStudents;
