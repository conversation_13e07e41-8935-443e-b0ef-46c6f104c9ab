import React, { useEffect } from 'react';
import { Divider } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { useGetRoles } from './utils/hooks';
import { AddNewRole, ShowAllRole } from './component/util';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectIsLoading,
  selectMessage,
  selectQ360CategorySubData,
} from '_reduxapi/q360/roleAndPermission/selector';
import {
  useCallApiHook,
  useInputHook,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { List, Map } from 'immutable';
import {
  putRolesPermission,
  setData,
  resetReducer,
} from '_reduxapi/q360/roleAndPermission/actions';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';

const RolesAndPermission = () => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(resetReducer());
  }, []);

  const [allRoles] = useGetRoles();
  const isLoading = useSelector(selectIsLoading);
  const message = useSelector(selectMessage);
  const categorySubData = useSelector(selectQ360CategorySubData);
  const [updateRolesAndPermission] = useCallApiHook(putRolesPermission);
  const [searchRole, handleChangeState] = useInputHook('');

  const filterRoles = allRoles.filter((roles) =>
    roles.get('roleName', '').toLowerCase().includes(searchRole.toLowerCase())
  );

  const saveRole = () => {
    let payload = [];
    function extractData({
      currentObject,
      qapcRoleId,
      result,
      levelName,
      childKey,
      categoryKey,
      programId,
      categoryId,
      uniqueId,
    }) {
      const categoryFormCourseData = categorySubData.getIn(
        ['categoryMainData', qapcRoleId, categoryKey, 'categoryFormCourseData'],
        List()
      );
      const categoryGroupData = categorySubData.getIn(
        ['categoryMainData', qapcRoleId, categoryKey, 'categoryGroupData'],
        List()
      );

      const getCourseGroupData = ({
        courseData,
        gpData,
        mainChildKey,
        includeKeyName = [],
        childKey = '',
      }) => {
        const categoryFormCourseId = categoryFormCourseData
          .filter((item) =>
            Object.keys(courseData).every((key) => {
              return item.get(key, '') === courseData[key];
            })
          )
          .map((item) => item.get('_id', ''));
        if (categoryFormCourseId.size > 0) {
          const formData = { ...gpData };
          formData.categoryFormCourseId = categoryFormCourseId.get(0);
          const includeGroupKeys = [
            'term',
            'attemptTypeName',
            'group',
            'academicYear',
            'all academic Year',
          ];
          let groupData = [];
          if (includeGroupKeys.includes(mainChildKey)) {
            const filterGroupData = categoryGroupData
              .filter((item) => {
                return Object.keys(formData).every((key) => {
                  if (formData[key] !== undefined) {
                    return item.get(key, '') === formData[key];
                  }
                  return true;
                });
              })
              .filter((item) => {
                return includeKeyName.length > 0
                  ? includeKeyName.includes(
                      item.get(childKey === 'group' ? 'groupName' : childKey, '')
                    )
                  : item;
              })
              .map((item) => item.get('_id', ''));
            groupData = filterGroupData.toJS();
          }
          const formCourseId = categoryFormCourseId;
          const preparedData = {
            categoryFormCourseId: formCourseId.toJS(),
            ...(includeGroupKeys.includes(mainChildKey) && { formCourseGroupId: groupData }),
          };
          return preparedData;
        }

        return {
          categoryFormCourseId: [],
        };
      };

      const getAvailableChildKeys = (productItem) => {
        const { childKey } = productItem;
        const filteredItems = [];
        Object.keys(productItem?.[childKey]).forEach((productKey) => {
          if (productKey === 'All' && childKey === 'attemptTypeName') {
            const groups = productItem?.[childKey]?.[productKey]?.['group'];
            if (groups) {
              const groupValues = Object.values(groups);
              if (groupValues.length > 0) {
                const groupKey = groupValues
                  .filter((item) => item?.assigned?.length > 0)
                  .map((item) => item.name);
                filteredItems.push(...groupKey);
              }
            }
          } else {
            if (productItem?.[childKey]?.[productKey]?.['assigned']?.length > 0) {
              filteredItems.push(productKey);
            }
          }
        });
        return filteredItems;
      };

      const getFormCourseGroupId = (productItem, mainChildKey, levelName) => {
        const {
          childKey,
          programId,
          curriculumId,
          name,
          courseId,
          year,
          attemptTypeName,
          groupName,
          term,
          institutionId,
        } = productItem;
        const courseData = {};
        const gpData = {};
        let includeKeyName = [];
        if (levelName === 'course') {
          if (mainChildKey === 'program') {
            courseData.programId = programId;
          } else if (mainChildKey === 'curriculum') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
          } else if (mainChildKey === 'year') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            courseData.year = name;
          } else if (['course', 'term'].includes(mainChildKey)) {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            courseData.year = year;
            courseData.courseId = courseId;
            if (['term'].includes(mainChildKey)) {
              gpData.term = name;
            }
          } else if (['attemptTypeName'].includes(mainChildKey)) {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            courseData.year = year;
            courseData.courseId = courseId;
            gpData.attemptTypeName = name;
            gpData.term = term;
          } else if (mainChildKey === 'group') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            courseData.year = year;
            courseData.courseId = courseId;
            gpData.groupName = name;
            gpData.attemptTypeName = attemptTypeName;
            gpData.term = term;
          } else if (mainChildKey === 'academicYear') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            courseData.year = year;
            courseData.courseId = courseId;
            gpData.attemptTypeName = attemptTypeName;
            gpData.groupName = groupName;
            gpData.academicYear = name;
            gpData.term = term;
          } else if (mainChildKey === 'all academic Year') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            courseData.year = year;
            courseData.courseId = courseId;
            gpData.attemptTypeName = attemptTypeName;
            gpData.groupName = groupName;
            gpData.academicYear = 'every';
            gpData.term = term;
          }
        } else if (levelName === 'institution') {
          if (mainChildKey === 'program') {
            courseData.assignedInstitutionId = institutionId;
          }
        } else if (levelName === 'program') {
          if (mainChildKey === 'program') {
            courseData.programId = programId;
          } else if (mainChildKey === 'curriculum') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
          } else if (mainChildKey === 'term') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            gpData.term = name;
          } else if (['attemptTypeName'].includes(mainChildKey)) {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            gpData.attemptTypeName = name;
            gpData.term = term;
          } else if (mainChildKey === 'group') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            gpData.groupName = name;
            gpData.attemptTypeName = attemptTypeName;
            gpData.term = term;
          } else if (mainChildKey === 'academicYear') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            gpData.attemptTypeName = attemptTypeName;
            gpData.groupName = groupName;
            gpData.academicYear = name;
            gpData.term = term;
          } else if (mainChildKey === 'all academic Year') {
            courseData.programId = programId;
            courseData.curriculumId = curriculumId;
            gpData.attemptTypeName = attemptTypeName;
            gpData.groupName = groupName;
            gpData.academicYear = 'every';
            gpData.term = term;
          }
        }

        if (['term', 'attemptTypeName'].includes(mainChildKey)) {
          includeKeyName = getAvailableChildKeys(productItem);
        }

        return getCourseGroupData({
          courseData,
          gpData,
          mainChildKey,
          includeKeyName,
          childKey,
        });
      };

      Object.keys(currentObject).forEach((productKey) => {
        const productItem = currentObject[productKey];
        levelName = productItem?.levelName || levelName;
        programId = productItem?.programId || programId;
        // const selectType = `${levelName}@@${categoryId}@@${programId}@@${childKey}@@${productItem.name.trim()}`;
        if (!productItem.subDataKey) return;
        const selectType = `${categoryId}@@${productItem.subDataKey.join('@@')}`;
        if (productItem.assigned && productItem.assigned.length > 0) {
          const formCourseGroupId = getFormCourseGroupId(productItem, childKey, levelName);
          const staffListIds = [];
          productItem.assigned.forEach((assign) => {
            assign.staffList.forEach((staff) => {
              const userId = staff.userId;
              staffListIds.push(userId);
              if (userId) {
                Object.keys(assign.subModuleList || {}).forEach((subModuleId) => {
                  const subModule = assign.subModuleList[subModuleId];
                  const subModuleName = subModule.subModuleName;

                  const academicYearData = {};
                  if (productItem?.calendarId) {
                    academicYearData.academicYear = 'every';
                    academicYearData.institutionCalendarIds = [productItem?.calendarId];
                    academicYearData.selectedAcademic = true;
                  }
                  // Handle general subModules
                  if (subModule.checked) {
                    const actionIds = subModule.actionsIds
                      .filter((action) => action.checked)
                      .map((action) => action.actionId);

                    if (actionIds.length > 0) {
                      result.push({
                        userId,
                        subModuleId,
                        subModuleName,
                        actionId: actionIds,
                        qapcRoleId,
                        levelName,
                        selectType,
                        ...formCourseGroupId,
                        uuid: uniqueId,
                        ...academicYearData,
                      });
                    }
                  }
                  if (subModule.subModuleName === 'Form Approver' && subModule.levels) {
                    subModule.levels.forEach((level, levelIndex) => {
                      const actionIds = level.actionsIds
                        .filter((action) => action.checked)
                        .map((action) => action.actionId);
                      if (actionIds.length > 0) {
                        result.push({
                          userId,
                          subModuleId,
                          subModuleName: subModule.subModuleName,
                          actionId: actionIds,
                          approverLevelIndex: levelIndex + 1,
                          qapcRoleId,
                          levelName,
                          selectType,
                          ...formCourseGroupId,
                          uuid: uniqueId,
                          ...academicYearData,
                        });
                      }
                    });
                  }
                });
              }
            });
            assign.rolesStaffList
              .filter((item) => !staffListIds.includes(item.userId))
              .forEach((staff) => {
                const userId = staff.userId;
                const roleId = staff.roleId;
                staffListIds.push(userId);
                if (userId) {
                  Object.keys(assign.subModuleList || {}).forEach((subModuleId) => {
                    const subModule = assign.subModuleList[subModuleId];
                    const subModuleName = subModule.subModuleName;

                    const academicYearData = {};
                    if (productItem?.calendarId) {
                      academicYearData.academicYear = 'every';
                      academicYearData.institutionCalendarIds = [productItem?.calendarId];
                      academicYearData.selectedAcademic = true;
                    }
                    // Handle general subModules
                    if (subModule.checked) {
                      const actionIds = subModule.actionsIds
                        .filter((action) => action.checked)
                        .map((action) => action.actionId);

                      if (actionIds.length > 0) {
                        result.push({
                          userId,
                          roleId,
                          subModuleId,
                          subModuleName,
                          actionId: actionIds,
                          qapcRoleId,
                          levelName,
                          selectType,
                          ...formCourseGroupId,
                          uuid: uniqueId,
                          ...academicYearData,
                        });
                      }
                    }
                    if (subModule.subModuleName === 'Form Approver' && subModule.levels) {
                      subModule.levels.forEach((level, levelIndex) => {
                        const actionIds = level.actionsIds
                          .filter((action) => action.checked)
                          .map((action) => action.actionId);
                        if (actionIds.length > 0) {
                          result.push({
                            userId,
                            subModuleId,
                            subModuleName: subModule.subModuleName,
                            actionId: actionIds,
                            approverLevelIndex: levelIndex + 1,
                            qapcRoleId,
                            levelName,
                            selectType,
                            ...formCourseGroupId,
                            uuid: uniqueId,
                            ...academicYearData,
                          });
                        }
                      });
                    }
                  });
                }
              });
          });
        }

        if (productItem.childKey && productItem[productItem.childKey]) {
          // Object.keys(productItem[productItem.childKey]).forEach(() => {
          const qapcData = productItem[productItem.childKey];
          extractData({
            currentObject: qapcData,
            qapcRoleId,
            result,
            levelName,
            childKey: productItem.childKey,
            categoryKey,
            programId,
            categoryId,
            uniqueId,
          });
          // });
        }
      });

      return result;
    }

    const formattedObject = categorySubData.toJS();
    const uniqueIdArray = [];
    Object.keys(formattedObject).forEach((qapcRoleId) => {
      if (qapcRoleId === 'categoryCourseIds' || qapcRoleId === 'categoryMainData') return;
      const qapcData = formattedObject[qapcRoleId];
      Object.keys(qapcData).forEach((categoryKey) => {
        let levelName = '';
        let programId = '';
        const programObject = qapcData[categoryKey];
        // const dynamicKey = Object.keys(programObject)[0];
        // const existingUuid = programObject?.[dynamicKey]?.uuid;
        const categoryId = categoryKey;
        // const uniqueId =
        //   existingUuid !== undefined && existingUuid !== '' ? existingUuid : uuidv4();
        // uniqueIdArray.push(uniqueId);
        const uniqueId = `${qapcRoleId}-${categoryId}`;
        uniqueIdArray.push(uniqueId);
        extractData({
          currentObject: programObject,
          qapcRoleId,
          result: payload,
          levelName,
          childKey: 'program',
          categoryKey,
          programId,
          categoryId,
          uniqueId,
        });
      });
    });
    const uniqueKey = [...new Set(uniqueIdArray)];
    const uniqueObjects = payload.filter(
      (obj, index, self) => index === self.findIndex((t) => t.selectType === obj.selectType)
    );

    const result1 = payload.reduce((acc, item) => {
      const key = item.selectType;
      if (acc[key]) {
        acc[key]++;
      } else {
        acc[key] = 1;
      }
      return acc;
    }, {});

    console.log({
      categorySubData,
      uniqueObjects,
      payload,
      updateRolesAndPermission,
      uniqueIdArray: uniqueKey,
      result1,
    });

    if (uniqueIdArray.length === 0) {
      dispatch(
        setData(
          Map({
            message: 'Please assign the roles to the user',
          })
        )
      );
    }

    if (uniqueIdArray.length > 0) {
      updateRolesAndPermission({
        hierarchy: 'cfpc',
        cfpcPermissions: payload,
        uniqueId: uniqueKey,
      });
    }
  };

  const cancelRole = () => {
    dispatch(
      setData(
        Map({
          categoryData: Map(),
          categorySubData: Map(),
          open: -1,
          assignArray: List(),
          assignedUserDetails: Map(),
          formApprover: List(),
          formApproverLevels: List(),
          assignedDetails: Map(),
        })
      )
    );
  };
  return (
    <>
      {message && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <main className="px-4 py-3">
        <header className="d-flex justify-content-between align-items-center">
          <div className="f-20 fw-500 text-dGrey">Q360 Permission</div>
          <div>
            <MButton
              variant="outlined"
              color="primary"
              className="w-100px mr-2"
              clicked={cancelRole}
            >
              Cancel
            </MButton>
            <MButton
              variant="contained"
              color="primary"
              // disabled={!rolesAndPermission.size}
              clicked={saveRole}
              className="w-100px"
            >
              Save
            </MButton>
          </div>
        </header>
        <Divider className="my-3" style={{ margin: '0 -26px' }} />
        <section className="d-flex flex-wrap justify-content-between align-items-center pt-2">
          <div className="order-0">
            <MaterialInput
              elementType={'materialSearch'}
              placeholder={'Search Role'}
              labelclass="searchLeft"
              value={searchRole}
              changed={handleChangeState}
            />
          </div>
          <section className="w-100 mt-3 p-3 rolesTableHeaderMain color-selected fw-500 text-lGrey f-14 rounded order-2">
            <div>Roles</div>
            <div className="rolesTableHeaderSub text-center">
              <div>Assign</div>
              <div>Sub modules</div>
              <div>Actions</div>
            </div>
          </section>
          <AddNewRole />
          <ShowAllRole allRoles={filterRoles} />
        </section>
      </main>
    </>
  );
};

export default RolesAndPermission;
