import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import Table from '@mui/material/Table';
import makeStyles from '@mui/styles/makeStyles';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import Pagination from '../../StudentGrouping/components/Pagination';
import { Trans } from 'react-i18next';

const useStylesFunction = makeStyles({
  tableWrapper: { overflowX: 'scroll' },
  clickableRow: { '&:hover': { cursor: 'pointer' } },
  inactive: { color: '#00000061' },
});

function TableWithPagination({ columns, data = [], handleRowClick }) {
  const classes = useStylesFunction();
  const tableContainer = useRef();
  const [pagination, setPagination] = useState(Map({ page: 1, pageSize: 10 }));

  useEffect(() => {
    setPagination(pagination.set('page', 1));
    // eslint-disable-next-line
  }, [data]);

  const scrollToTop = () => {
    tableContainer.current.scrollIntoView();
  };

  const getPageData = () => {
    const { page, pageSize } = pagination.toJS();
    const startRow = (page - 1) * pageSize;
    const endRow = startRow + pageSize;
    return data.slice(startRow, endRow);
  };

  const getTotalPages = () => {
    return Math.ceil(data.size / pagination.get('pageSize'));
  };

  const onPageSizeChange = (newPageSize) => {
    scrollToTop();
    setPagination(Map({ page: 1, pageSize: +newPageSize }));
  };

  const handleNextPageClick = () => {
    const lastPage = getTotalPages();
    const currentPage = pagination.get('page');
    if (currentPage === lastPage || lastPage === 0) return;
    scrollToTop();
    setPagination(pagination.set('page', currentPage + 1));
  };

  const handlePrevPageClick = () => {
    const currentPage = pagination.get('page');
    if (currentPage === 1) return;
    scrollToTop();
    setPagination(pagination.set('page', currentPage - 1));
  };

  const handleFirstPageClick = () => {
    const currentPage = pagination.get('page');
    if (currentPage === 1) return;
    scrollToTop();
    setPagination(pagination.set('page', 1));
  };

  const handleLastPageClick = () => {
    const lastPage = getTotalPages();
    if (pagination.get('page') === lastPage) return;
    scrollToTop();
    setPagination(pagination.set('page', lastPage));
  };

  return (
    <>
      <div ref={tableContainer} className={classes.tableWrapper}>
        <Table className="table pt-2 pb-2 ">
          <TableHead className="bg-gray">
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column.key} className="text-dark space_nowrap">
                  {column.name}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          <TableBody>
            {getPageData().isEmpty() && (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center">
                  <Trans i18nKey={'reports_analytics.no_records_found'} />
                </TableCell>
              </TableRow>
            )}
            {getPageData().map((row) => (
              <TableRow
                key={row.get('_id')}
                hover={true}
                className={handleRowClick ? classes.clickableRow : ''}
              >
                {columns.map((column, columnIdx) => (
                  <TableCell
                    key={row.get('_id') + '_' + columnIdx}
                    className={row.get('isActive') === false ? classes.inactive : ''}
                    onClick={() => {
                      if (handleRowClick) {
                        handleRowClick(row);
                      }
                    }}
                  >
                    {column.key === 'formattedMode' && row.get('scheduleStartFrom', '') === 'web'
                      ? 'Onsite (Web)'
                      : row.get('classModeType', '') === 'offline'
                      ? 'Onsite (Offline)'
                      : row.get(column.key)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <Pagination
        data={getTotalPages()}
        currentPage={getTotalPages() && pagination.get('page')}
        pagination={onPageSizeChange}
        onNextClick={handleNextPageClick}
        onBackClick={handlePrevPageClick}
        onFullForwardClick={handleLastPageClick}
        onFullLastClick={handleFirstPageClick}
      />
    </>
  );
}

TableWithPagination.propTypes = {
  columns: PropTypes.array.isRequired,
  data: PropTypes.instanceOf(List).isRequired,
  handleRowClick: PropTypes.func,
};

export default TableWithPagination;
