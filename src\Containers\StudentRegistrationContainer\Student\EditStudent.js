import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import axios from '../../../axios';
import Input from '../../../Widgets/FormElements/Input/Input';

import Loader from '../../../Widgets/Loader/Loader';
import { Button } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import { data, gender, validation } from './data';
import Breadcrumb from '../../../Widgets/Breadcrumb/Breadcrumb';
import {
  getLang,
  jsUcfirstAll,
  isIndVer,
  isModuleEnabled,
  addDefaultLastName,
  lastNameRequired,
} from '../../../utils';
import { t } from 'i18next';
import { Trans } from 'react-i18next';

let id;
class EditStudent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ...data,
      selectGender: gender[0][1],
      selectedBatch: 'select',
      programData: [],
      selectedProgram: '',
      isLoading: false,
      programList: [],
    };
  }

  componentDidMount() {
    let search = window.location.search;
    let params = new URLSearchParams(search);
    id = params.get('id');
    this.fetchApi(id);
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });

    let field = { field: ['name', 'no'] };

    axios
      .get(`/digi_program?limit=200&pageNo=1`, field)
      .then((res) => {
        const data = res.data.data.map((data) => {
          return {
            name: data.name !== '' ? jsUcfirstAll(data.name) : '',
            value: data.code,
          };
        });
        data.unshift({
          name: 'Select Program',
          value: 'select',
        });
        this.setState({
          programData: data,
          isLoading: false,
          programList: res.data.data && res.data.data.length > 0 ? res.data.data : [],
        });
      })
      .catch((ex) => {
        this.setState({
          isLoading: false,
        });
      });

    axios
      .get(`user/student/${id}`)
      .then((res) => {
        let data = res.data.data;

        this.setState({
          userName: data.name.family,
          academicNo: data.academic,
          email: data.email,
          fName: data.name.first,
          lName: data.name.last,
          mName: data.name.middle,
          selectGender: data.gender,
          nationalId: data.address.nationality_id,
          enrollmentYear: data.enrollment_year,
          selectedProgram: data.program_no,
          selectedBatch: data.batch,
          isLoading: false,
        });
      })
      .catch((ex) => {
        console.log('repose error', ex); //eslint-disable-line
      });
  };

  handleChangeText = (e, name) => {
    if (name === 'fName') {
      this.setState({
        fName: e.target.value,
        fNameError: '',
      });
    }

    if (name === 'mName') {
      this.setState({
        mName: e.target.value,
        mNameError: '',
      });
    }
    if (name === 'lName') {
      this.setState({
        lName: e.target.value,
        lNameError: '',
      });
    }
    if (name === 'userName') {
      this.setState({
        userName: e.target.value,
        userNameError: '',
      });
    }
    if (name === 'academicNo') {
      this.setState({
        academicNo: e.target.value,
        academicNoError: '',
      });
    }
    if (name === 'nationalId') {
      this.setState({
        nationalId: e.target.value,
        nationalIdError: '',
      });
    }
    if (name === 'email') {
      this.setState({
        email: e.target.value,
        emailError: '',
      });
    }
  };

  handleSelect = (e, name) => {
    e.preventDefault();
    if (name === 'batch') {
      this.setState({
        selectedBatch: e.target.value,
        selectedBatchError: '',
      });
    }
    if (name === 'program') {
      this.setState({
        selectedProgram: e.target.value,
        selectedProgramError: '',
        selectedBatch: 'select',
      });
    }
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
        selectGenderError: '',
      });
    }
  };

  handleSingleEditSubmit(e) {
    e.preventDefault();
    this.setState({
      isValidate: false,
    });
    if (validation.call(this)) {
      const data = {
        id: this.state.id,
        first_name: this.state.fName,
        last_name: addDefaultLastName(this.state.lName),
        middle_name: this.state.mName,
        family: this.state.userName,
        gender: this.state.selectGender,
        academic_no: this.state.academicNo,
        email: this.state.email,
        nationality_id: this.state.nationalId,
        program_no: this.state.selectedProgram,
        batch: this.state.selectedBatch,
      };

      this.setState({
        isLoading: true,
      });
      axios
        .put(`user/${id}`, data)
        .then((res) => {
          this.setState({
            isLoading: false,
          });

          // this.setState(
          //   {
          //     isLoading: false,
          //     singleEntryShow: false,
          //   },
          //   () => {
          //     this.fetchApi();
          //   }
          // );

          NotificationManager.success(t('user_management.student_updated_successfully'));
          this.handleGoBack();
        })
        .catch((error) => {
          NotificationManager.error(`${error.response.data.data}`);
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  }

  handleGoBack = () => {
    this.props.history.push({
      pathname: '/student/management',
      state: {
        completeView: false,
        pendingView: true,
        inactiveView: false,
        selectedTab: this.props.location.state !== undefined ? this.props.location.state : 0,
        name: 'invited',
      },
    });
  };

  getBatch = () => {
    const { programList, selectedProgram } = this.state;
    if (programList && programList.length > 0 && selectedProgram !== 'select') {
      const terms = programList
        .filter((item) => item.code === selectedProgram)
        .reduce((_, el) => el.term, [])
        .map((item) => {
          return { name: item.term_name, value: item.term_name };
        });

      terms.unshift({
        name: `Select ${isIndVer() ? 'Intake' : 'Batch'}`,
        value: 'select',
      });

      return terms;
    }
    return [];
  };

  render() {
    gender[0][0] = t('infra_management.gender.Male');
    gender[1][0] = t('infra_management.gender.Female');
    const items = [
      { to: '/student/management', label: t('side_nav.menus.student_management') },
      { to: '/', label: t('user_management.edit_student') },
    ];
    const hasNationalIdValidation = isModuleEnabled('NATIONALITY_ID');
    const userSensitiveData = isModuleEnabled('USER_SENSITIVE');
    return (
      <React.Fragment>
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <div className="main pt-5 pb-5">
          <Loader isLoading={this.state.isLoading} />

          <div className="container">
            <div className="float-left pt-1 pb-3">
              {/* <Link to="/student/management">← Back to Student List</Link> */}
            </div>
            <div className="float-left white p-2">
              <div className="row w-100">
                <div className="col-md-6 pt-1 d-flex">
                  <Trans i18nKey={'user_management.Personal_Details'} />
                </div>
                <div className="col-md-6">
                  <div className="float-right">
                    <Button variant="outline-primary" className="m-2" onClick={this.handleGoBack}>
                      <Trans i18nKey={'back_cc'} />
                    </Button>

                    <Button onClick={(e) => this.handleSingleEditSubmit(e)}>
                      {' '}
                      <Trans i18nKey={'add_colleges.save'} />{' '}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="row">
                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.fName}
                    floatingLabel={t('first_name')}
                    changed={(e) => this.handleChangeText(e, 'fName')}
                    feedback={this.state.fNameError}
                  />
                </div>
                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.mName}
                    floatingLabel={t('middle_name')}
                    changed={(e) => this.handleChangeText(e, 'mName')}
                    feedback={this.state.mNameError}
                  />
                </div>

                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.lName}
                    floatingLabel={!lastNameRequired() ? 'Last Name' : t('last_name')}
                    changed={(e) => this.handleChangeText(e, 'lName')}
                    feedback={this.state.lNameError}
                  />
                </div>

                <div className="col-md-3">
                  <Input
                    elementType={'floatinginput'}
                    elementConfig={{
                      type: 'text',
                    }}
                    maxLength={25}
                    value={this.state.userName}
                    floatingLabel={t('user_management.Family_Name_Optional')}
                    changed={(e) => this.handleChangeText(e, 'userName')}
                  />
                </div>

                <div className={`col-md-12 pt-2 ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  <label>
                    {' '}
                    <Trans i18nKey={'gender'} />
                  </label>
                  <Input
                    elementType={'radio'}
                    elementConfig={gender}
                    className={'form-radio1'}
                    selected={this.state.selectGender}
                    labelclass="radio-label2"
                    onChange={(e) => this.onRadioGroupChange(e, 'selectGender')}
                    feedback={this.state.selectGenderError}
                  />
                </div>
                <div className="col-md-12">
                  <div className="row">
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatinginput'}
                        elementConfig={{
                          type: 'text',
                        }}
                        maxLength={40}
                        value={this.state.email}
                        floatingLabel={t('emailId')}
                        changed={(e) => this.handleChangeText(e, 'email')}
                        feedback={this.state.emailError}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-md-12">
                  <div className="row">
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatinginput'}
                        elementConfig={{
                          type: 'text',
                        }}
                        maxLength={25}
                        value={this.state.academicNo}
                        floatingLabel={t('academic_no')}
                        changed={(e) => this.handleChangeText(e, 'academicNo')}
                        feedback={this.state.academicNoError}
                      />
                    </div>
                  </div>
                </div>

                <div className="col-md-12">
                  <div className="row">
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatingselect'}
                        elementConfig={{
                          options: this.state.programData,
                        }}
                        value={this.state.selectedProgram}
                        className={'customize_dropdown'}
                        floatingLabel={t('program_name')}
                        changed={(e) => this.handleSelect(e, 'program')}
                        feedback={this.state.selectedProgramError}
                      />
                    </div>
                  </div>
                </div>

                <div className="col-md-12">
                  <div className="row">
                    <div className="col-md-3 pt-2">
                      <Input
                        elementType={'floatingselect'}
                        elementConfig={{
                          options: this.getBatch(),
                        }}
                        value={this.state.selectedBatch}
                        className={'customize_dropdown'}
                        floatingLabel={t('user_management.batch', {
                          Batch: isIndVer() ? 'Intake' : 'Batch',
                        })}
                        changed={(e) => this.handleSelect(e, 'batch')}
                        feedback={this.state.selectedBatchError}
                      />
                    </div>
                  </div>
                </div>
                {userSensitiveData && (
                  <div className="col-md-12">
                    <div className="row">
                      <div className="col-md-3 pt-2">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.nationalId}
                          floatingLabel={t('national/residence', {
                            optional: !hasNationalIdValidation ? '(Optional)' : '',
                          })}
                          changed={(e) => this.handleChangeText(e, 'nationalId')}
                          feedback={this.state.nationalIdError}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

EditStudent.propTypes = {
  history: PropTypes.object,
  location: PropTypes.object,
};

export default withRouter(EditStudent);
