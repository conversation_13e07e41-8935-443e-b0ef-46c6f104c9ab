import React, { Fragment, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Divider } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import q360IdeaBook from 'Assets/q360IdeaBook.svg';
import BookIcon from 'Assets/BookIcon.svg';
import selectedAvatarIcon from 'Assets/selectedAvatarIcon.svg';
import selectedFormIcon from 'Assets/selectedFormIcon.svg';
import selectedSendIcon from 'Assets/selectedSendIcon.svg';
import q360Note from 'Assets/q360Note.svg';
import q360Avatar from 'Assets/q360Avatar.svg';
import q360SendIcon from 'Assets/q360SendIcon.svg';
import { useSearchParams } from '../QaPcConfiguration';
import { List, Map, fromJS } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import { selectCategoryFormIndex } from '_reduxapi/global_configuration/v1/selectors';
import { getCategoryForms, updateDuplicateForm } from '_reduxapi/global_configuration/v1/actions';
// import Step1 from './Step1';
import Step3 from '../../../../GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step3/index';
import Step4 from '../../../../GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step4';
import { useHistory } from 'react-router-dom';
import UploadDragAndDrop from './UploadDragAndDrop';
import Step1 from '../../../../GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step1/Step1';
import { useCallApiHook } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { getSingleFormOccurrence } from '_reduxapi/q360/actions';
import { selectFormOccurrence } from '_reduxapi/q360/selectors';
import LocalStorageService from 'LocalStorageService';

const headerUtils = fromJS([
  {
    active: {
      name: 'Occurrence Configure',
      icon: q360IdeaBook,
      currentStep: 'Step 1/4',
    },
    notActive: {
      name: 'Occurance',
      icon: BookIcon,
    },
  },
  {
    active: {
      name: 'Specification Form',
      icon: selectedFormIcon,
      currentStep: 'Step 2/4',
    },
    notActive: {
      name: 'Form',
      icon: q360Note,
    },
  },
  {
    active: {
      name: 'Approver Hierarchy',
      icon: selectedAvatarIcon,
      currentStep: 'Step 3/4',
    },
    notActive: {
      name: 'Approver',
      icon: q360Avatar,
    },
  },
  {
    active: {
      name: 'Concluding Phase',
      icon: selectedSendIcon,
      currentStep: 'Step 4/4',
    },
    notActive: {
      name: 'Preview',
      icon: q360SendIcon,
    },
  },
]);

export function getSections(iframeRef) {
  if (iframeRef.current) {
    const document = iframeRef.current.contentWindow.document;
    return Array.from(document.querySelectorAll('.table-of-contents td'))
      .filter((data) => data.className === 'p-4 pl-12')
      .map((data) => data.textContent);
  }
  return [];
}
const Step2 = ({ state, setState }) => {
  const [files, setFiles] = useState(List());
  const iframeRef = useRef();
  const sendMessageToIframe = () => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow.postMessage({ values: [], from: 'fromDC' }, '*');
      LocalStorageService.setCustomToken('sections', JSON.stringify(getSections(iframeRef)));
    }
  };
  const handleIframeLoad = () => {
    sendMessageToIframe();
  };
  useEffect(() => {
    return () => {
      setState((prev) => (prev = prev.set('attachments', fromJS(files))));
    };
  }, [files]);
  useEffect(() => {
    setFiles(state.get('attachments', List()));
  }, []);
  return (
    <div className="w-100 h_100vh">
      <UploadDragAndDrop files={files} setFiles={setFiles} />
      <iframe
        ref={iframeRef}
        src={'/course-spec.html'}
        title="Face Anomaly Report"
        width="100%"
        height="100%"
        style={{ border: 'none', overflow: 'auto' }}
        onLoad={handleIframeLoad}
        sandbox="allow-same-origin"
      ></iframe>
    </div>
  );
};

const component = {
  0: Step1,
  1: Step2,
  2: Step3,
  3: Step4,
};

export default function CreateConfig() {
  const [searchParams] = useSearchParams();
  const history = useHistory();
  const dispatch = useDispatch();
  const [step, setStep] = useState(searchParams.get('step') ? Number(searchParams.get('step')) : 0);
  const updateStep = (index) => () => {
    setStep(index);
  };

  const categoryFormIndex = searchParams.get('categoryFormIndex');
  const selectedCategoryForm = useSelector((state) =>
    selectCategoryFormIndex(state, categoryFormIndex)
  );

  const formOccurrences = useSelector(selectFormOccurrence);

  const formId = searchParams.get('categoryFormId');
  const singleFormOccurrence = formOccurrences.get(formId, List());

  const [getApi] = useCallApiHook(getSingleFormOccurrence);

  useEffect(() => {
    if (singleFormOccurrence.size === 0) getApi(formId);
  }, [formId]);

  useEffect(() => {
    if (singleFormOccurrence.size !== 0) {
      const detail3 = [];

      singleFormOccurrence.toJS().forEach((detail) => {
        // Find or create the program
        let program = detail3.find((p) => p._program_id === detail.programId);
        if (!program) {
          program = {
            _program_id: detail.programId,
            program_name: detail.programName,
            curriculum: [],
          };
          detail3.push(program);
        }

        // Find or create the curriculum
        let curriculum = program.curriculum.find((c) => c._curriculum_id === detail.curriculumId);
        if (!curriculum) {
          curriculum = {
            _curriculum_id: detail.curriculumId,
            curriculum_name: detail.curriculumName,
            years: [],
          };
          program.curriculum.push(curriculum);
        }

        // Find or create the year
        let year = curriculum.years.find((y) => y.year === detail.year);
        if (!year) {
          year = {
            year: detail.year,
            courses: [],
          };
          curriculum.years.push(year);
        }

        // Add the course to the year
        year.courses.push({
          _id: detail._id,
          _program_id: detail.programId,
          program_name: detail.programName,
          _curriculum_id: detail.curriculumId,
          curriculum_name: detail.curriculumName,
          year: detail.year,
          courseId: detail.courseId,
          course_name: detail.courseName,
          course_code: detail.courseCode,
          shared_with_others: detail.sharedWithOthers,
          shared_from_others: detail.sharedFormOthers,
          course_type: detail.courseType,
          institution_type: detail.institutionType,
          isConfigure: false,
          isEnable: detail.isEnable,
        });
      });

      const selectedCategoryForm2 = selectedCategoryForm.set('formOccurrence', fromJS(detail3));

      if (selectedCategoryForm2.size === 0) {
        const categoryId = searchParams.get('categoryId');
        dispatch(
          getCategoryForms({
            params: {
              categoryId,
            },
          })
        );
      } else {
        setState((prev) =>
          prev
            .merge(
              selectedCategoryForm2.update('formOccurrence', (formOccurrence) =>
                formOccurrence.map((program) => {
                  return program.update('curriculum', List(), (curriculum) =>
                    curriculum.map((curriculum) =>
                      curriculum.update('years', List(), (years) =>
                        years.map((year) => {
                          return year.update('courses', (courses) => {
                            let courseArray = fromJS([{}, {}]);
                            courses.forEach((course) => {
                              const index = course.get('isConfigure', false) ? 1 : 0;
                              if (course.get('shared_with_others', false)) {
                                courseArray = courseArray.update(index, (courseMap) =>
                                  courseMap.update('shared_from', List(), (data) =>
                                    data.push(course.set('isEnable', true))
                                  )
                                );
                                return;
                              }
                              if (course.get('shared_from', false)) {
                                courseArray = courseArray.update(index, (courseMap) =>
                                  courseMap.update('shared_with_others', List(), (data) =>
                                    data.push(course.set('isEnable', true))
                                  )
                                );
                                return;
                              }
                              if (course.get('course_type', '') === 'standard')
                                courseArray = courseArray.update(index, (courseMap) =>
                                  courseMap.update('standard', List(), (data) =>
                                    data.push(course.set('isEnable', true))
                                  )
                                );
                              else
                                courseArray = courseArray.update(index, (courseMap) =>
                                  courseMap.update('selective', List(), (data) =>
                                    data.push(course.set('isEnable', true))
                                  )
                                );
                            });
                            return courseArray;
                          });
                        })
                      )
                    )
                  );
                })
              )
            )
            .set(
              'approvalLevel',
              selectedCategoryForm
                .get(
                  'approvalLevel',
                  fromJS([
                    {
                      name: 'Level 1 Hierarchy',
                    },
                  ])
                )
                .map((item, index) => item.set('level', index + 1))
            )
        );
      }
    }
  }, [selectedCategoryForm, singleFormOccurrence]);

  const [state, setState] = useState(
    fromJS({
      approvalLevel: [
        {
          level: 1,
          name: 'Level 1 Hierarchy',
        },
      ],
    })
  );
  const handleNext = () => {
    setStep((prev) => prev + 1);
  };
  // const [yearTab, setYearTab] = useState(0);
  // const onClickYearTab = (_, newValue) => {
  //   setYearTab(newValue);
  // };
  const Component = component[step];
  const handleSave = (status = 'draft') => () => {
    const callBack = () => {
      history.push(
        '/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration+configure_template&currentCategoryIndex=' +
          categoryFormIndex
      );
    };
    dispatch(
      updateDuplicateForm(
        state
          .update('formOccurrence', List(), (formOccurrence) => {
            return formOccurrence.map((program) =>
              program.update('curriculum', List(), (curriculums) => {
                return curriculums.map((curriculum) => {
                  return curriculum.update('years', List(), (years) => {
                    return years.map((year) => {
                      return year.update('courses', List(), (courses) => {
                        let courseList = List();
                        courses.forEach((course) => {
                          course.forEach((singleCourse, type) => {
                            courseList = courseList.push(singleCourse.set('course_type', type));
                          });
                        });
                        return courseList.flatMap((item) => item);
                      });
                    });
                  });
                });
              })
            );
          })
          .set('step', step + 1)
          .set('status', status)
          .set('isActive', true),
        callBack
      )
    );
  };
  return (
    <>
      <section
        className="d-flex align-items-center cursor-pointer setting_icon_position mr-3"
        // onClick={onClickSettings}
      >
        {selectedCategoryForm.get('status', '') === 'draft' && (
          <MButton
            variant="contained"
            color="primary"
            clicked={handleSave('draft')}
            className="px-4 mr-3"
          >
            Save As Draft
          </MButton>
        )}

        {step !== 3 && (
          <MButton
            variant="contained"
            color="primary"
            clicked={handleNext}
            // disabled={currentStep === steps.length - 1}
            className="px-4"
          >
            Next Step
          </MButton>
        )}
        {step === 3 && <MButton clicked={handleSave()}>Save</MButton>}
      </section>
      <section className="bg-grey calcHeightVh">
        <Header step={step} updateStep={updateStep} />
        <main className="d-flex justify-content-center">
          <div className="w-100 px-4">
            <Component state={state} setState={setState} />
          </div>
        </main>
      </section>
    </>
  );
}
CreateConfig.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
  loggedInUserData: PropTypes.instanceOf(Map),
};

const Header = ({ step, updateStep }) => {
  return (
    <header className="d-flex justify-content-center my-4 py-2">
      <div className="bg-white borderRounded-12 d-flex px-3 py-3 ">
        {headerUtils.map((iconData, iconIndex) => {
          if (iconIndex === step)
            return (
              <Fragment key={iconIndex}>
                <div
                  className="d-flex align-items-center gap-8 cursor-pointer"
                  onClick={updateStep(iconIndex)}
                >
                  <img
                    src={iconData.getIn(['active', 'icon'], '')}
                    alt="q360IdeaBook"
                    width={'46px'}
                  />
                  <div>
                    <div className="f-12 fw-400 text-primary">
                      {iconData.getIn(['active', 'currentStep'], '')}
                    </div>
                    <div className="f-14 fw-500 text-dGrey">
                      {iconData.getIn(['active', 'name'], '')}
                    </div>
                  </div>
                </div>
                {iconIndex !== 3 && (
                  <Divider orientation="vertical" flexItem variant="middle" className="my-2 mx-5" />
                )}
              </Fragment>
            );
          return (
            <Fragment key={iconIndex}>
              <div className="text-center cursor-pointer" onClick={updateStep(iconIndex)}>
                <img src={iconData.getIn(['notActive', 'icon'], '')} alt="BookIcon" />
                <p className="m-0 fw-400 text-dGrey f-12 ">
                  {iconData.getIn(['notActive', 'name'], '')}
                </p>
              </div>
              {iconIndex !== 3 && (
                <Divider orientation="vertical" flexItem variant="middle" className="my-2 mx-5" />
              )}
            </Fragment>
          );
        })}
      </div>
    </header>
  );
};
Header.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
  loggedInUserData: PropTypes.instanceOf(Map),
};
