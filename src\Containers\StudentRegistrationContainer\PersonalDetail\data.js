
  export const data = {
    chooseFname: "",
    chooseMname: "",
    chooseLname: "",
    choosegender: "",
    chooseAcadamic: "",
    chooseEnrollment: "",
    chooseNational: "",
    chooseProgram: "",
    chooseContact: "",
    fName: "",
    mName: "",
    lName: "",
    DOB: "",
    selectedCountry: "",
    residendId: "",
    passpord: "",
    empId: "",
    buildingNo: "",
    city: "",
    distric: "",
    zipCode: "",
    phone: "",
    unit: "",
    GenderError: "",
    DOBError: "",
    countryError: "",
    buildingNoError: "",
    cityError: "",
    districError: "",
    zipCodeError: "",
    phoneError: "",
    unitError: "",

    selectedOption: null,
    isloading: false,
    university: [],

    pdf: "",
    faName: "",
    maName: "",
    fEmail: "",
    mEmail: "",
    fPhone: "",
    mPhone: "",

    gName: "",
    gPhone: "",
    gEmail: "",
    studendRelation: "",

    sName: "",
    sPhone: "",
    sEmail: "",
    academic:'',
    programNumber:'',
    enrollment:'',
  };
  
  
  export function validation() {
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    const dotAlpha = /^[a-zA-Z0-9/. -]*$/;
    const Number = /^[0-9]+$/;

    let genderError = "";
    let DOBError = "";
    let countryError = "";
    let buildingNoError = "";
    let cityError = "";
    let districError = "";
    let zipCodeError = "";
    let unitError = "";
    let selectFnameError = "";
    let selectMnameError = "";
    let selectLnameError = "";
    let acadamicError = "";
    let enrollmentError = "";
    let selectNationalError = "";
    let programError = "";

    if (this.state.selectFname === "r") {
      selectFnameError = "Choose First Name Correct Or Wrong";
    }

    if (this.state.selectMname === "r") {
      selectMnameError = "Choose Middle Name Correct Or Wrong";
    }
    if (this.state.selectLname === "r") {
      selectLnameError = "Choose Last Name Correct Or Wrong";
    }
    if (this.state.selectgender === "r") {
      genderError = "Choose Gender Correct Or Wrong";
    }
    if (this.state.selectAcadamic === "r") {
      acadamicError = "Choose Acadamic Correct Or Wrong";
    }
    if (this.state.selectEnrollment === "r") {
      enrollmentError = "Choose Enrollment Correct Or Wrong";
    }

    if (this.state.selectProgram === "r") {
      programError = "Choose Program Correct Or Wrong";
    }

    if (this.state.selectNational === "r") {
      selectNationalError = "Choose National ID Correct Or Wrong";
    }

    if (this.state.DOB === "") {
      DOBError = "DOB Field is Required";
    }
    if (this.state.selectedCountry === "") {
      countryError = "Nationalty Field is Required";
    }


    if (!this.state.buildingNo) {
      buildingNoError = "Building No, Street Name is Required";
    } else if (!space.test(this.state.buildingNo)) {
      buildingNoError = "Space not allowed beginning & end";
    } else if (!dotAlpha.test(this.state.buildingNo)) {
      buildingNoError = "Special character not allowed";
    } else if (this.state.buildingNo.length <= 2) {
      buildingNoError = "Minimum 3 character is required ";
    }

    if (!this.state.city) {
      cityError = "City Name is Required";
    } else if (!space.test(this.state.city)) {
      cityError = "Space not allowed beginning & end";
    } else if (!spaceAlpha.test(this.state.city)) {
      cityError = "Text Only allowed";
    } else if (this.state.city.length <= 2) {
      cityError = "Minimum 3 character is required ";
    }

    if (!this.state.distric) {
      districError = "Distric Name is Required";
    } else if (!space.test(this.state.distric)) {
      districError = "Space not allowed beginning & end";
    } else if (!spaceAlpha.test(this.state.distric)) {
      districError = "Text Only allowed";
    } else if (this.state.distric.length <= 2) {
      districError = "Minimum 3 character is required ";
    }

    if (!this.state.zipCode) {
      zipCodeError = "Zip Code is Required";
    } else if (!space.test(this.state.zipCode)) {
      zipCodeError = "Space not allowed beginning & end";
    } else if (!Number.test(this.state.zipCode)) {
      zipCodeError = "Numeric Only allowed";
    } else if (this.state.zipCode.length <= 2) {
      zipCodeError = "Minimum 3 character is required ";
    } else if (parseInt(this.state.zipCode) > 1000000) {
      zipCodeError = "Pls Enter 1000000 Lesser than value ";
    } else if (parseInt(this.state.zipCode) === 0) {
      zipCodeError = "Pls Enter 0 Greater than value ";
    }

  
    if (!this.state.unit) {
      unitError = "Floor Number is Required";
    } else if (!space.test(this.state.unit)) {
      unitError = "Space not allowed beginning & end";
    } else if (!Number.test(this.state.unit)) {
      unitError = "Numeric Only allowed";
    } else if (parseInt(this.state.unit) > 50) {
      unitError = "Pls Enter 50 Lesser than value ";
    } else if (parseInt(this.state.unit) === 0) {
      unitError = "Pls Enter 0 Greater than value ";
    }

    if (
      genderError ||
      DOBError ||
      countryError ||
      buildingNoError ||
      cityError ||
      districError ||
      zipCodeError ||
      unitError ||
      selectFnameError ||
      selectMnameError ||
      selectLnameError ||
      acadamicError ||
      selectNationalError ||
      enrollmentError ||
      programError
    ) {
      this.setState({
        genderError,
        DOBError,
        countryError,
        buildingNoError,
        cityError,
        districError,
        zipCodeError,
        unitError,
        selectFnameError,
        selectMnameError,
        selectLnameError,
        acadamicError,
        selectNationalError,
        enrollmentError,
        programError,
      });

      return false;
    }
    return true;
  }
  