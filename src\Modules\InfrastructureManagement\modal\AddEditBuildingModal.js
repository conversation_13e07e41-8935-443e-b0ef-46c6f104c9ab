import React, { Component } from 'react';
import { Button, Form, Modal } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';

import { getRandomId, capitalize } from '../utils';
import AlertModal from './AlertModal';
import Input from '../../../Widgets/FormElements/Input/Input';
import MaterialInput from '../../../Widgets/FormElements/material/Input';
import Tooltip from '../../../_components/UI/Tooltip/Tooltip';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { isModuleEnabled } from 'utils';

const BUILDING = {
  COLLEGE: 'college',
  HOSPITAL: 'hospital',
};
const BUILDINGS = [
  { name: 'College', value: BUILDING.COLLEGE },
  { name: 'Hospital', value: BUILDING.HOSPITAL },
];
const CATEGORY = {
  LEVEL: 'level',
  BASEMENT: 'basement',
};
const CATEGORIES = [
  { name: 'Level', value: CATEGORY.LEVEL },
  { name: 'Basement', value: CATEGORY.BASEMENT },
];
const LOCATION_TYPE = {
  NEW: 'new',
  EXISTING: 'existing',
};

class AddEditBuildingModal extends Component {
  constructor() {
    super();
    this.state = {
      name: '',
      newLocation: '',
      existingLocation: '',
      type: BUILDING.COLLEGE,
      floors: List(),
      zones: List(),
      locationType: LOCATION_TYPE.NEW,
      hasFloors: false,
      hasZones: false,
      addedFloors: Map(),
      editedFloors: Map(),
      addedZones: Map(),
      editedZones: Map(),
      isPopulated: false,
      modalData: {
        show: false,
      },
      latitude: '',
      longitude: '',
      radius: '100',
    };
  }

  static getDerivedStateFromProps(props, state) {
    if (!state.isPopulated && !props.selectedBuilding.isEmpty()) {
      const { selectedBuilding } = props;
      return {
        name: selectedBuilding.get('name'),
        locationType: LOCATION_TYPE.EXISTING,
        existingLocation: selectedBuilding.get('location'),
        type: selectedBuilding.get('type'),
        floors: selectedBuilding.get('floors'),
        zones: selectedBuilding
          .get('zones')
          .map((zone) =>
            Map({ _id: getRandomId(), name: zone.get('zone'), isAssigned: zone.get('isAssigned') })
          ),
        hasFloors: selectedBuilding.get('no_of_floors') > 0,
        hasZones: selectedBuilding.get('no_of_zones') > 0,
        isPopulated: true,
        latitude: selectedBuilding.get('latitude', ''),
        longitude: selectedBuilding.get('longitude', ''),
        radius: selectedBuilding.get('radius', ''),
      };
    }
    return null;
  }

  componentDidMount() {
    window.addEventListener('message', this.handleMessage);
  }

  componentWillUnmount() {
    window.removeEventListener('message', this.handleMessage);
  }

  handleMessage = (event) => {
    const msg = event.data;
    const { latitude, longitude, radius } = msg;
    if (latitude !== undefined && longitude !== undefined && radius !== undefined) {
      this.setState({
        latitude: msg.latitude,
        longitude: msg.longitude,
        radius: msg.radius,
      });
    }
  };

  handleChange(event, name) {
    const { locations } = this.props;
    let value = '';
    let locationData = {};
    if (['type', 'locationType'].includes(name)) value = event;
    else if (['hasFloors', 'hasZones'].includes(name)) value = event.target.checked;
    else if (name === 'existingLocation') {
      const location = locations.find(
        (location) => location.get('location', '') === event.target.value
      );
      locationData = {
        radius: location.get('radius', ''),
        latitude: location.get('latitude', ''),
        longitude: location.get('longitude', ''),
      };
      value = event.target.value;
    } else value = event.target.value;
    this.setState({
      [name]: value,
      ...(name === 'existingLocation' && {
        radius: locationData.radius,
        longitude: locationData.longitude,
        latitude: locationData.latitude,
      }),
      ...(name === 'locationType' && { newLocation: '', existingLocation: '' }),
    });
  }

  getLocations() {
    const { locations } = this.props;
    const transformedLocations = locations.map((l) =>
      Map({ name: l.get('location', ''), value: l.get('location', '') })
    );
    return transformedLocations
      .insert(
        0,
        Map({
          name: t('infra_management.add_location_modal.select_from_existing_locations'),
          value: '',
        })
      )
      .toJS();
  }

  addFloorOrZone(type) {
    const _id = getRandomId();
    this.setState((state) => {
      return {
        ...(type === 'floor' && {
          addedFloors: state.addedFloors.set(_id, Map({ _id, floor_name: '', category: 'level' })),
        }),
        ...(type === 'zone' && { addedZones: state.addedZones.set(_id, Map({ _id, name: '' })) }),
      };
    });
  }

  getAddedFloorsZones(type) {
    return type === 'floor' ? this.state.addedFloors.entrySeq() : this.state.addedZones.entrySeq();
  }

  handleFloorChange(event, name, row, operation) {
    const value = event.target.value;
    const id = row.get('_id');
    this.setState((state) => {
      const { addedFloors, editedFloors } = state;
      return {
        ...(operation === 'create' && { addedFloors: addedFloors.setIn([id, name], value) }),
        ...(operation === 'update' && { editedFloors: editedFloors.setIn([id, name], value) }),
      };
    });
  }

  handleFloorCancelClick(row, operation) {
    const id = row.get('_id');
    this.setState((state) => {
      const { addedFloors, editedFloors } = state;
      return {
        ...(operation === 'create' && { addedFloors: addedFloors.delete(id) }),
        ...(operation === 'update' && { editedFloors: editedFloors.delete(id) }),
      };
    });
  }

  handleFloorEdit(row) {
    if (row.get('isAssigned')) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.floor_assigned.title'),
        description: t('infra_management.alerts.floor_assigned.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    this.setState((state) => {
      return {
        editedFloors: state.editedFloors.set(row.get('_id'), row),
      };
    });
  }

  handleFloorDelete(row, isConfirmed = false) {
    if (row.get('isAssigned')) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.floor_assigned.title'),
        description: t('infra_management.alerts.floor_assigned.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: t('infra_management.modals.confirm_delete'),
        description: t('infra_management.modals.floor.description'),
        variant: 'confirm',
        data: { data: row, operation: 'delete', type: 'floor' },
      });
      return;
    }
    this.setState((state) => {
      return {
        floors: state.floors.filter((f) => f.get('_id') !== row.get('_id')),
      };
    });
  }

  handleFloorSaveClick(row, operation) {
    const id = row.get('_id');
    const floorName = row.get('floor_name');
    const category = row.get('category');
    const floorExists = this.state.floors.find(
      (f) => f.get('floor_name') === floorName && f.get('category') === category
    );
    if (!floorName) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.invalid_floor_name.title'),
        description: t('infra_management.alerts.invalid_floor_name.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    if (floorExists) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.floor_exists.title'),
        description: t('infra_management.alerts.floor_exists.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    this.setState((state) => {
      const { addedFloors, editedFloors, floors } = state;
      const data = operation === 'create' ? addedFloors.get(id) : editedFloors.get(id);
      const floorIndex = floors.findIndex((f) => f.get('_id') === id);
      return {
        ...(operation === 'create' && {
          addedFloors: addedFloors.delete(id),
          floors: floors.insert(0, data),
        }),
        ...(operation === 'update' && {
          editedFloors: editedFloors.delete(id),
          floors: floors.setIn([floorIndex], data),
        }),
      };
    });
  }

  handleZoneChange(event, row, operation) {
    const value = event.target.value;
    const id = row.get('_id');
    this.setState((state) => {
      const { addedZones, editedZones } = state;
      return {
        ...(operation === 'create' && { addedZones: addedZones.setIn([id, 'name'], value) }),
        ...(operation === 'update' && { editedZones: editedZones.setIn([id, 'name'], value) }),
      };
    });
  }

  handleZoneCancelClick(row, operation) {
    const id = row.get('_id');
    this.setState((state) => {
      const { addedZones, editedZones } = state;
      return {
        ...(operation === 'create' && { addedZones: addedZones.delete(id) }),
        ...(operation === 'update' && { editedZones: editedZones.delete(id) }),
      };
    });
  }

  handleZoneEdit(row) {
    if (row.get('isAssigned')) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.zone_assigned.title'),
        description: t('infra_management.alerts.zone_assigned.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    this.setState((state) => {
      return {
        editedZones: state.editedZones.set(row.get('_id'), row),
      };
    });
  }

  handleZoneDelete(row, isConfirmed = false) {
    if (row.get('isAssigned')) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.zone_assigned.title'),
        description: t('infra_management.alerts.zone_assigned.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: t('infra_management.modals.confirm_delete'),
        description: t('infra_management.modals.zone.description'),
        variant: 'confirm',
        data: { data: row, operation: 'delete', type: 'zone' },
      });
      return;
    }
    this.setState((state) => {
      return {
        zones: state.zones.filter((f) => f.get('_id') !== row.get('_id')),
      };
    });
  }

  handleZoneSaveClick(row, operation) {
    const id = row.get('_id');
    const zoneName = row.get('name');
    const zoneExists = this.state.zones.find((z) => z.get('name') === zoneName);
    if (!zoneName) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.invalid_zone.title'),
        description: t('infra_management.alerts.invalid_zone.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    if (zoneExists) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.zone_exists.title'),
        description: t('infra_management.alerts.zone_exists.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    this.setState((state) => {
      const { addedZones, editedZones, zones } = state;
      const data = operation === 'create' ? addedZones.get(id) : editedZones.get(id);
      const floorIndex = zones.findIndex((f) => f.get('_id') === id);
      return {
        ...(operation === 'create' && {
          addedZones: addedZones.delete(id),
          zones: zones.insert(0, data),
        }),
        ...(operation === 'update' && {
          editedZones: editedZones.delete(id),
          zones: zones.setIn([floorIndex], data),
        }),
      };
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ data, operation, type }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => {
        switch (operation) {
          case 'delete': {
            if (type === 'floor') this.handleFloorDelete(data, true);
            else if (type === 'zone') this.handleZoneDelete(data, true);
            return;
          }
          default:
            return;
        }
      }
    );
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  disableSaveButton() {
    let disabled = false;
    const { floors, zones, addedFloors, addedZones, locationType } = this.state;
    Object.keys(this.state).forEach((key) => {
      if (!disabled) {
        switch (key) {
          case 'name':
          case 'type':
            if (!this.state[key]) {
              disabled = true;
            }
            break;
          case 'newLocation':
            disabled = locationType === LOCATION_TYPE.NEW ? !this.state[key] : false;
            break;
          case 'existingLocation':
            disabled = locationType === LOCATION_TYPE.EXISTING ? !this.state[key] : false;
            break;
          case 'hasFloors':
            disabled = this.state[key] ? floors.isEmpty() : false;
            break;
          case 'hasZones':
            disabled = this.state[key] ? zones.isEmpty() : false;
            break;
          case 'addedFloors':
            disabled = this.state.hasFloors ? !addedFloors.isEmpty() : false;
            break;
          case 'addedZones':
            disabled = this.state.hasZones ? !addedZones.isEmpty() : false;
            break;
          default:
            return;
        }
      }
    });
    return disabled;
  }

  createOrUpdateBuilding() {
    const { operation, selectedBuilding } = this.props;
    const {
      hasFloors,
      floors,
      hasZones,
      zones,
      locationType,
      name,
      newLocation,
      existingLocation,
      type,
      radius,
      latitude,
      longitude,
    } = this.state;
    const id = selectedBuilding ? selectedBuilding.get('_id') : '';
    const requestBody = {
      name,
      location: locationType === LOCATION_TYPE.NEW ? newLocation : existingLocation,
      type,
      floors: hasFloors
        ? floors
            .map((f) => Map({ floor_name: f.get('floor_name'), category: f.get('category') }))
            .toJS()
        : [],
      zones: hasZones ? zones.map((z) => z.get('name')).toJS() : [],
      operation,
      _id: id,
      outsideCampus: true,
      radius: radius,
      latitude: latitude,
      longitude: longitude,
    };
    this.props.onSave(requestBody);
  }

  isAssigned() {
    const { selectedBuilding } = this.props;
    return selectedBuilding.get('isAssigned', false);
  }

  hasAssignedFloorsZones(key) {
    const { selectedBuilding } = this.props;
    return selectedBuilding.get(key, List()).filter((f) => f.get('isAssigned')).size !== 0;
  }

  handleLocation(e, key) {
    const value = e.target.value;
    this.setState({
      [key]: value,
    });
  }

  toGetMap(operation) {
    const { radius, latitude, longitude } = this.state;
    return (
      <>
        <div>
          <iframe
            src={`${
              process.env.REACT_APP_BASE_PATH || '/'
            }/map_viewer/app.html?radius=${radius}&latitude=${latitude}&longitude=${longitude}`}
            title="Face Anomaly Report"
            width="100%"
            height={'500px'}
          ></iframe>
        </div>
        <div className="d-flex p-2">
          <div className="p-1">
            <MaterialInput
              elementType={'materialInput'}
              inputAdornment={<div className="text-muted">Meters</div>}
              value={radius}
              label={'Radius'}
              size={'small'}
              changed={(e) => this.handleLocation(e, 'radius')}
            />
          </div>
          <div className="p-1">
            <MaterialInput
              size={'small'}
              value={latitude}
              elementType={'materialInput'}
              placeholder={'Select Radius'}
              label={'Latitude'}
              changed={(e) => this.handleLocation(e, 'latitude')}
            />
          </div>
          <div className="p-1">
            <MaterialInput
              size={'small'}
              value={longitude}
              elementType={'materialInput'}
              placeholder={'Select Radius'}
              label={'Longitude'}
              changed={(e) => this.handleLocation(e, 'longitude')}
            />
          </div>
        </div>
      </>
    );
  }

  render() {
    const { show, onClose, operation } = this.props;
    const {
      name,
      locationType,
      newLocation,
      existingLocation,
      type,
      hasFloors,
      addedFloors,
      editedFloors,
      hasZones,
      floors,
      zones,
      addedZones,
      editedZones,
      modalData,
      radius,
      longitude,
      latitude,
    } = this.state;
    return (
      <Modal show={show} onHide={onClose} dialogClassName="modal-970" centered>
        <Modal.Body>
          <div className=" container pt-2 pb-5 border-radious-8">
            <p className="mb-0">{`${operation === 'update' ? t('edit') : t('add')} ${t(
              'infra_management.infra_settings.buildings_hospitals.building/hospital'
            )}`}</p>
            <div className="row mb-5">
              <div className="col-md-6 ">
                <div className="col-md-10 mb-3 ml--15">
                  <Input
                    elementType="floatinginput"
                    elementConfig={{
                      type: 'text',
                    }}
                    value={name}
                    floatingLabel={t('name')}
                    changed={(e) => this.handleChange(e, 'name')}
                  />
                </div>
                <div className="mt-1">
                  <p className="mb-3 f-15">
                    {t('infra_management.infra_settings.buildings_hospitals.location')}
                  </p>
                  <div className="row mt-1">
                    <div className="col-md-1">
                      <Form.Check
                        type="radio"
                        name="locationType"
                        className="mt-1 ml-2"
                        checked={locationType === LOCATION_TYPE.NEW}
                        onChange={() => this.handleChange(LOCATION_TYPE.NEW, 'locationType')}
                        disabled={this.isAssigned()}
                      />
                    </div>
                    <div className="col-md-9">
                      <div className="mt--25">
                        <Input
                          elementType="floatinginput"
                          elementConfig={{
                            type: 'text',
                          }}
                          disabled={this.isAssigned() || locationType !== LOCATION_TYPE.NEW}
                          value={newLocation}
                          placeholder={t('infra_management.add_location_modal.new_location')}
                          changed={(e) => this.handleChange(e, 'newLocation')}
                        />
                      </div>
                    </div>
                  </div>
                  {locationType === 'new' &&
                    isModuleEnabled('OUTSIDE_CAMPUS_V2') &&
                    this.toGetMap(operation)}
                  <div className="row mt-2">
                    <div className="col-md-1">
                      <Form.Check
                        type="radio"
                        name="locationType"
                        className="mt-1 ml-2"
                        checked={locationType === LOCATION_TYPE.EXISTING}
                        onChange={() => this.handleChange(LOCATION_TYPE.EXISTING, 'locationType')}
                        disabled={this.isAssigned()}
                      />
                    </div>
                    <div className="col-md-9">
                      <div className="mt--25">
                        <Input
                          elementType="floatingselect"
                          elementConfig={{
                            options: this.getLocations(),
                          }}
                          disabled={this.isAssigned() || locationType !== LOCATION_TYPE.EXISTING}
                          value={existingLocation}
                          changed={(e) => {
                            this.handleChange(e, 'existingLocation');
                          }}
                        />
                      </div>
                    </div>
                  </div>
                  {locationType === 'existing' &&
                    radius !== null &&
                    latitude !== null &&
                    longitude !== null &&
                    isModuleEnabled('OUTSIDE_CAMPUS_V2') &&
                    this.toGetMap(operation)}
                </div>
              </div>
              <div className="col-md-6 border-left">
                <div className="col-md-8">
                  <p className="mb-2 ml-2">
                    <Trans
                      i18nKey={'infra_management.add_location_modal.building_type.Building_Type'}
                    ></Trans>
                  </p>
                  {BUILDINGS.map((b) => (
                    <Form.Check
                      key={b.name}
                      type="radio"
                      label={t(`infra_management.add_location_modal.building_type.${b.name}`)}
                      checked={type === b.value}
                      name="type"
                      className="mt-1 ml-2"
                      onChange={() => this.handleChange(b.value, 'type')}
                      disabled={this.isAssigned()}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="row pb-2">
              <div className="col-md-6 ">
                <Form.Check
                  type="checkbox"
                  label={t('infra_management.add_location_modal.floors')}
                  className="ml-2"
                  checked={hasFloors}
                  onChange={(e) => this.handleChange(e, 'hasFloors')}
                  disabled={this.hasAssignedFloorsZones('floors')}
                />
                {hasFloors && (
                  <>
                    <div>
                      <p
                        className="mb-0 mr-3 float-right remove_hover font-weight-bold"
                        onClick={this.addFloorOrZone.bind(this, 'floor')}
                      >
                        <i className="fa fa-plus-circle pr-2" aria-hidden="true"></i>
                        <Trans i18nKey={'add'}></Trans>
                      </p>
                    </div>
                    <div className="pb-3 ">
                      <div className="go-wrapper border-bottom">
                        <table className="table">
                          <thead className="group_table_top th-change">
                            <tr>
                              <th>
                                <div className="aw-150">
                                  <Trans>{'infra_management.infra_settings.floor_name'}</Trans>
                                </div>
                              </th>
                              <th>
                                <div className="aw-150">
                                  <Trans>{'infra_management.infra_settings.category'}</Trans>
                                </div>
                              </th>
                              <th>
                                <div className="aw-50"></div>
                              </th>
                            </tr>
                          </thead>
                          <tbody className="go-wrapper-height">
                            {this.getAddedFloorsZones('floor').map((row) => (
                              <EditableFloorZone
                                key={row[0]}
                                row={row[1]}
                                data={addedFloors}
                                handleChange={this.handleFloorChange.bind(this)}
                                handleCancelClick={this.handleFloorCancelClick.bind(this)}
                                handleSaveClick={this.handleFloorSaveClick.bind(this)}
                                operation="create"
                                type="floor"
                              />
                            ))}
                            {floors.map((row) =>
                              !editedFloors.has(row.get('_id')) ? (
                                <tr key={row.get('_id')} className="tr-change">
                                  <td>
                                    <div className="aw-150">{row.get('floor_name', '')}</div>
                                  </td>
                                  <td>
                                    <div className="aw-150">
                                      {capitalize(row.get('category', ''))}
                                    </div>
                                  </td>
                                  <td>
                                    <div className="aw-50">
                                      <b className="float-left pr-2 f-16 remove-hover">
                                        <Tooltip title={t('edit')}>
                                          <i
                                            className="fa fa-pencil"
                                            aria-hidden="true"
                                            onClick={this.handleFloorEdit.bind(this, row)}
                                          ></i>
                                        </Tooltip>
                                      </b>
                                      <b className="float-left pl-2 f-16 remove-hover">
                                        <Tooltip title={t('delete')}>
                                          <i
                                            className="fa fa-trash"
                                            aria-hidden="true"
                                            onClick={this.handleFloorDelete.bind(this, row, false)}
                                          ></i>
                                        </Tooltip>
                                      </b>
                                    </div>
                                  </td>
                                </tr>
                              ) : (
                                <EditableFloorZone
                                  key={row.get('_id')}
                                  row={row}
                                  data={editedFloors}
                                  handleChange={this.handleFloorChange.bind(this)}
                                  handleCancelClick={this.handleFloorCancelClick.bind(this)}
                                  handleSaveClick={this.handleFloorSaveClick.bind(this)}
                                  operation="update"
                                  type="floor"
                                />
                              )
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </>
                )}
              </div>
              <div className="col-md-6 ">
                <Form.Check
                  type="checkbox"
                  label={t('infra_management.add_location_modal.zones')}
                  className="ml-4"
                  checked={hasZones}
                  onChange={(e) => this.handleChange(e, 'hasZones')}
                  disabled={this.hasAssignedFloorsZones('zones')}
                />
                {hasZones && (
                  <>
                    <div>
                      <p
                        className="mb-0 mr-3 float-right remove_hover font-weight-bold"
                        onClick={this.addFloorOrZone.bind(this, 'zone')}
                      >
                        <i className="fa fa-plus-circle pr-2" aria-hidden="true"></i>
                        <Trans i18nKey={'add'}></Trans>
                      </p>
                    </div>
                    <div className="pb-3 ml-4">
                      <div className="go-wrapper border-bottom">
                        <table className="table">
                          <thead className="group_table_top th-change">
                            <tr>
                              <th>
                                <div className="aw-300">
                                  {t('infra_management.infra_settings.zone_name')}
                                </div>
                              </th>
                              <th>
                                <div className="aw-50"></div>
                              </th>
                            </tr>
                          </thead>
                          <tbody className="go-wrapper-height">
                            {this.getAddedFloorsZones('zone').map((row) => (
                              <EditableFloorZone
                                key={row[0]}
                                row={row[1]}
                                data={addedZones}
                                handleChange={this.handleZoneChange.bind(this)}
                                handleCancelClick={this.handleZoneCancelClick.bind(this)}
                                handleSaveClick={this.handleZoneSaveClick.bind(this)}
                                operation="create"
                                type="zone"
                              />
                            ))}
                            {zones.map((row) =>
                              !editedZones.has(row.get('_id')) ? (
                                <tr key={row.get('_id')} className="tr-change">
                                  <td>
                                    <div className="aw-300">{row.get('name', '')}</div>
                                  </td>
                                  <td>
                                    <div className="aw-50">
                                      <b className="float-left pr-2 f-16 remove-hover">
                                        <Tooltip title={t('edit')}>
                                          <i
                                            className="fa fa-pencil"
                                            aria-hidden="true"
                                            onClick={this.handleZoneEdit.bind(this, row)}
                                          ></i>
                                        </Tooltip>
                                      </b>
                                      <b className="float-left pl-2 f-16 remove-hover">
                                        <Tooltip title={t('delete')}>
                                          <i
                                            className="fa fa-trash"
                                            aria-hidden="true"
                                            onClick={this.handleZoneDelete.bind(this, row, false)}
                                          ></i>
                                        </Tooltip>
                                      </b>
                                    </div>
                                  </td>
                                </tr>
                              ) : (
                                <EditableFloorZone
                                  key={row.get('_id')}
                                  row={row}
                                  data={editedZones}
                                  handleChange={this.handleZoneChange.bind(this)}
                                  handleCancelClick={this.handleZoneCancelClick.bind(this)}
                                  handleSaveClick={this.handleZoneSaveClick.bind(this)}
                                  operation="update"
                                  type="zone"
                                />
                              )
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
          <AlertModal
            show={modalData.show}
            title={modalData.title || ''}
            description={modalData.description || ''}
            variant={modalData.variant || 'confirm'}
            confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
            cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
            onClose={this.onModalClose.bind(this)}
            onConfirm={this.onConfirm.bind(this)}
            data={modalData.data}
          />
        </Modal.Body>
        <Modal.Footer>
          <div className="pr-2">
            <Button variant="outline-secondary" onClick={onClose}>
              <Trans i18nKey={'cancel'}></Trans>
            </Button>
          </div>
          <div className="pr-2">
            <Button
              variant={this.disableSaveButton() ? 'secondary' : 'primary'}
              onClick={this.createOrUpdateBuilding.bind(this)}
              disabled={this.disableSaveButton()}
            >
              <Trans i18nKey={'save'}></Trans>
            </Button>
          </div>
        </Modal.Footer>
      </Modal>
    );
  }
}

function EditableFloorZone(props) {
  const { row, handleChange, handleCancelClick, handleSaveClick, data, operation, type } = props;
  const id = row.get('_id');
  return type === 'floor' ? (
    <tr className="tr-change">
      <td>
        <div className="aw-150 mt--25">
          <Input
            elementType="floatinginput2"
            elementConfig={{
              type: 'text',
            }}
            value={data.getIn([id, 'floor_name'])}
            placeholder={t('infra_management.add_infra.Enter floor name')}
            floatingLabel=""
            changed={(e) => handleChange(e, 'floor_name', row, operation)}
          />
        </div>
      </td>
      <td>
        <div className="aw-150 mt--25">
          <Input
            elementType="floatingselect"
            elementConfig={{
              options: CATEGORIES,
            }}
            value={data.getIn([id, 'category'])}
            changed={(e) => handleChange(e, 'category', row, operation)}
          />
        </div>
      </td>
      <td>
        <div className="aw-50">
          <b className="float-left pr-2 f-16 remove-hover">
            <Tooltip title={t('cancel')}>
              <i
                className="fa fa-times-circle"
                aria-hidden="true"
                onClick={() => handleCancelClick(row, operation)}
              ></i>
            </Tooltip>
          </b>
          <b className="float-left pl-2 f-16 remove-hover">
            <Tooltip title={t('save')}>
              <i
                className="fa fa-floppy-o"
                aria-hidden="true"
                onClick={() => handleSaveClick(data.get(id), operation)}
              ></i>
            </Tooltip>
          </b>
        </div>
      </td>
    </tr>
  ) : (
    <tr className="tr-change">
      <td>
        <div className="aw-300 mt--25">
          <div className="aw-200">
            <Input
              elementType={'floatinginput2'}
              elementConfig={{
                type: 'text',
              }}
              value={data.getIn([id, 'name'])}
              placeholder={t('infra_management.add_infra.Enter zone name')}
              floatingLabel=""
              changed={(e) => handleChange(e, row, operation)}
            />
          </div>
        </div>
      </td>
      <td>
        <div className="aw-50">
          <b className="float-left pr-2 f-16 remove-hover">
            <Tooltip title={t('cancel')}>
              <i
                className="fa fa-times-circle"
                aria-hidden="true"
                onClick={() => handleCancelClick(row, operation)}
              ></i>
            </Tooltip>
          </b>
          <b className="float-left pl-2 f-16 remove-hover">
            <Tooltip title={t('save')}>
              <i
                className="fa fa-floppy-o"
                aria-hidden="true"
                onClick={() => handleSaveClick(data.get(id), operation)}
              ></i>
            </Tooltip>
          </b>
        </div>
      </td>
    </tr>
  );
}

AddEditBuildingModal.propTypes = {
  show: PropTypes.bool,
  locations: PropTypes.instanceOf(List),
  selectedBuilding: PropTypes.instanceOf(Map),
  onClose: PropTypes.func,
  onSave: PropTypes.func,
  operation: PropTypes.string,
};

EditableFloorZone.propTypes = {
  row: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  handleCancelClick: PropTypes.func,
  handleSaveClick: PropTypes.func,
  data: PropTypes.instanceOf(Map),
  operation: PropTypes.string,
  type: PropTypes.string,
};

export default AddEditBuildingModal;
