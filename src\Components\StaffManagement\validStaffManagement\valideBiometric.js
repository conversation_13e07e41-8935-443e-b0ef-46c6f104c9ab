import React, { Component, Suspense } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import Swal from 'sweetalert2';

import axios from '../../../axios';
import Loader from '../../../Widgets/Loader/Loader';
import Input from '../../../Widgets/FormElements/Input/Input';
import { handelRightClick } from '../../../utils';
import { FlexWrapper, NavButton } from '../../../_components/Styled/styled';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { AUTOMATION_EMAILS_SKIP } from '../../../constants';
import Avatar from '../../../Assets/avatar.png';
import PrivatePerson from '../../../Assets/private-person.svg';
import FingerStatic from '../../../Assets/finger-static.png';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { connect } from 'react-redux';

const Webcam = React.lazy(() => import('./webcam'));
const NewWebCam = React.lazy(() => import('Components/Biometric'));

const gender = [
  ['Thumb Finger', 'thumb'],
  ['Index Finger', 'index'],
];
class ValidBiometric extends Component {
  constructor(props) {
    super(props);
    this.state = {
      image2: null,
      selectGender: gender[0][1],
      faceModel: false,
      faceCapture: false,
      faceSubmit: false,
      isLoading: false,
      fingerModel: false,
      fingerThumb: false,
      thumbSubmit: false,
      fingerIndex: false,
      indexSubmit: false,
      uri: 'http://localhost:8004/mfs100',
      IsoTemplate: null,
      image: 'https://files.slack.com/files-pri/T014CKBR50V-F0160LDRPBQ/finger1.png',
      id: null,
      image_message: null,
      tabs: 0,
      userStatusStaff: '',
      userTypeStaff: '',
      email: null,
      employeeId: null,
      faceNewModel: false,
    };
  }

  componentDidMount() {
    const { loggedInUserData, biometricName } = this.props;
    let search = window.location.search;
    let params = new URLSearchParams(search);
    let id = params.get('id');
    let tab = params.get('tabs') || params.get('tab');
    this.setState({ id: id, tabs: tab });
    this.loadImage(id);
    this.fetchApi(id);

    const isStudent = biometricName === 'student';
    const hasBiometrics = loggedInUserData.get(isStudent ? 'studentFacial' : 'staffFacial', false);
    if (hasBiometrics) {
      document
        .getElementById('disableRightClick')
        .addEventListener('contextmenu', handelRightClick);
    }
  }

  fetchApi = (id) => {
    if (this.props.biometricName !== 'student') {
      axios.get(`/user/staff/${id}`).then((res) => {
        const data = res.data.data;
        this.setState({
          userStatusStaff: data.status,
          userTypeStaff: data.user_type,
          email: data.email,
          employeeId: data.employee_id,
        });
      });
    }
  };

  loadImage = (id) => {
    axios.get(`/user/user_img_get/${id}`).then((res) => {
      var data = res?.data;
      if (
        data?.status_code === 200 &&
        data?.data?.biometric_data?.face !== undefined &&
        data?.data?.biometric_data?.face?.length >= 0
      ) {
        this.setState({
          image2: data.data.biometric_data.face[0],
        });
      }
    });
  };

  handleFace = () => {
    this.setState({
      faceModel: true,
    });
  };

  handleFaceClose = () => {
    const { id } = this.state;
    this.setState({
      faceModel: false,
      faceCapture: false,
    });
    this.loadImage(id);
  };

  handleFaceCapture = () => {
    this.setState({
      faceCapture: true,
    });
  };
  handleFaceCaptureClose = () => {
    this.setState({
      faceCapture: false,
      isLoading: false,
    });
  };

  handleFinger = () => {
    this.setState({
      fingerModel: true,
    });
  };

  handleFingerClose = () => {
    this.setState({
      fingerModel: false,
    });
  };

  handleThumbFinger = () => {
    this.setState({
      fingerThumb: true,
    });
  };

  handleThumbFingerRetake = () => {
    this.setState({
      fingerThumb: false,
    });
  };

  handleIndexFinger = () => {
    this.setState({
      fingerIndex: true,
    });
  };

  handleIndexFingerRetake = () => {
    this.setState({
      fingerIndex: false,
    });
  };

  handleIndexFingerConfirm = () => {
    this.setState({
      indexSubmit: true,
      fingerModel: false,
    });
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'selectGender') {
      this.setState({
        selectGender: e.target.value,
      });
    }
  };

  capture = () => {
    var header = {
      async: false,
      crossDomain: true,
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
    };
    var MFS100Request = {
      Quality: 60,
      TimeOut: 10,
    };
    var jsondata = JSON.stringify(MFS100Request);

    axios.post(this.state.uri + '/capture', header, jsondata).then((res) => {
      this.setState({
        image: 'data:image/bmp;base64,' + res.data.BitmapData,
        IsoTemplate: res.data.IsoTemplate,
      });
    });
  };

  matcher = () => {
    var header = {
      async: false,
      crossDomain: true,
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
    };
    var MFS100Request = {
      Quality: 60,
      TimeOut: 10,
      GalleryTemplate: this.state.IsoTemplate,
      BioType: 'FMR',
    };
    axios.post(this.state.uri + '/match', header, MFS100Request).then((res) => {
      if (res.status === 200) {
        if (res.data.Status) {
          alert('Finger matched');
        } else {
          if (res.data.ErrorCode !== '0') {
            alert(res.data.ErrorDescription + '&&&&&&&&&&&&&');
          } else {
            alert('Finger not matched');
          }
        }
      } else {
        alert(res.err + '############3');
      }
    });
  };

  handleGoBack = () => {
    if (
      this.props.biometricName === 'student' &&
      this.props.userTypeStudent === 'student' &&
      this.props.userStatusStudent === 'completed'
    ) {
      this.props.history.push({
        pathname: '/student/management',
      });
    } else if (this.state.userStatusStaff === 'completed' && this.state.userTypeStaff === 'staff') {
      this.props.history.push({
        pathname: '/staff/management',
      });
    } else if (this.props.biometricName === 'student') {
      this.props.handleGoBack();
    } else {
      this.props.history.push({
        pathname: '/staff/management',
        state: {
          completeView: false,
          pendingView: true,
          inactiveView: false,
          selectedTab: this.state.tabs !== '' ? parseInt(this.state.tabs) : 0,
        },
      });
    }
  };

  handleNewFace = () => {
    this.setState({
      faceNewModel: true,
    });
  };

  handleNewFaceClose = () => {
    const { id } = this.state;
    this.setState({
      faceNewModel: false,
    });
    this.loadImage(id);
  };

  render() {
    const { loggedInUserData, biometricName } = this.props;
    const { tabs, email, employeeId, userTypeStaff } = this.state;
    const permissionName =
      tabs === '5' ? 'Invalid' : tabs === '6' ? 'Valid' : tabs === null ? 'Registered' : '';
    const isSkip = email !== null ? AUTOMATION_EMAILS_SKIP.includes(email) : false;
    const eId = userTypeStaff === 'staff' ? employeeId : this.props.userAcademicId;

    const isStudent = biometricName === 'student';
    const hasBiometrics = loggedInUserData.get(isStudent ? 'studentFacial' : 'staffFacial', false);
    if (!hasBiometrics) return <></>;
    const authId = loggedInUserData.get('_id', '');
    return (
      <React.Fragment>
        {this.props.location.pathname !== '/studentvalid/all' && (
          <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
            <Trans i18nKey={'user_management.staff_biometric'}></Trans>{' '}
          </div>
        )}
        <Loader isLoading={this.state.isLoading} />
        {this.props.location.pathname !== '/studentvalid/all' && (
          <FlexWrapper className="nav_bg">
            {(CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Profile View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Profile View'
              )) && (
              <Link exact="true" to={`/staff/management/profile${this.props.location.search}`}>
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/profile' && 'active'
                  }
                  color="white"
                >
                  <Trans i18nKey={'user_management.profile'}></Trans>
                </NavButton>
              </Link>
            )}

            {(CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Academic View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Academic View'
              )) && (
              <Link exact="true" to={`/staff/management/academic${this.props.location.search}`}>
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/academic' && 'active'
                  }
                  color="white"
                >
                  <Trans i18nKey={'user_management.academic'}></Trans>
                </NavButton>
              </Link>
            )}
            {(CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Employment View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Employment View'
              )) && (
              <Link exact="true" to={`/staff/management/employment${this.props.location.search}`}>
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/employment' && 'active'
                  }
                  color="white"
                >
                  <Trans i18nKey={'user_management.employment'}></Trans>
                </NavButton>
              </Link>
            )}
            {(CheckPermission(
              'tabs',
              'User Management',
              'Staff Management',
              '',
              permissionName,
              'Biometric View'
            ) ||
              CheckPermission(
                'subTabs',
                'User Management',
                'Staff Management',
                '',
                'Registration Pending',
                '',
                permissionName,
                'Biometric View'
              )) && (
              <Link exact="true" to={`/staff/management/biometric${this.props.location.search}`}>
                <NavButton
                  className={
                    this.props.location.pathname === '/staff/management/biometric' && 'active'
                  }
                  color="white"
                >
                  <Trans i18nKey={'user_management.biometric'}></Trans>
                </NavButton>
              </Link>
            )}
          </FlexWrapper>
        )}

        <div className="main pt-4" id="disableRightClick">
          <div className="container">
            {/* faceModel start  */}

            {this.state.faceModel && (
              <Suspense fallback="">
                <Webcam
                  id={this.state.id}
                  close={this.handleFaceClose}
                  history={this.props.history}
                  handleFaceClose={this.handleFaceClose}
                  isSkip={isSkip}
                  employeeId={eId}
                />
              </Suspense>
            )}

            {this.state.faceNewModel && (
              <Suspense fallback="">
                <NewWebCam
                  userId={this.state.id}
                  _user_id={authId}
                  handleFaceClose={this.handleNewFaceClose}
                  successCallBack={() => {
                    Swal.fire({
                      icon: 'success',
                      title: 'Registration Successful...',
                      text: 'Biometric Registration successful!',
                    }).then(() => {
                      this.handleNewFaceClose();
                    });
                  }}
                  errorCallBack={() => {
                    Swal.fire({
                      icon: 'error',
                      title: 'Registration Failed...',
                      text:
                        'Could not detect face or does not have clarity. Please retake the picture.',
                    }).then(() => {});
                  }}
                />
              </Suspense>
            )}

            {/* faceModel end  */}
            {/* fingerModel start  */}
            <Modal show={this.state.fingerModel} centered size="lg" onHide={this.handleFingerClose}>
              <Modal.Header closeButton>
                <div className="row w-100">
                  <div className="col-md-12 pt-1">
                    <Trans i18nKey={'capture_fingerprint'}></Trans>
                  </div>
                </div>
              </Modal.Header>

              <Modal.Body>
                <div className="row justify-content-center">
                  <div className="col-md-6">
                    <img width="100%" src={FingerStatic} alt="#" className="w-100" />
                  </div>
                </div>
                <br />
                <br />
                <div className="img_border">
                  <div className="row justify-content-center">
                    <div className="col-md-8">
                      <p>
                        {' '}
                        <Trans i18nKey={'biometric.point1'}></Trans>
                      </p>
                      <p>
                        {' '}
                        <Trans i18nKey={'biometric.point2'}></Trans>{' '}
                      </p>
                      <label>
                        <Trans i18nKey={'biometric.choose_option'}></Trans>{' '}
                      </label>
                      <Input
                        elementType={'radio'}
                        elementConfig={gender}
                        className={'form-radio1'}
                        selected={this.state.selectGender}
                        labelclass="radio-label2"
                        onChange={(e) => this.onRadioGroupChange(e, 'selectGender')}
                      />
                    </div>

                    <div className="col-md-4">
                      {this.state.selectGender === 'thumb' ? (
                        <div>
                          {this.state.fingerThumb === true ? (
                            <img width="100%" src={this.state.image} alt="#" className="w-100" />
                          ) : (
                            <img width="100%" src={this.state.image} alt="#" className="w-100" />
                          )}
                        </div>
                      ) : (
                        <div>
                          {this.state.fingerIndex === true ? (
                            <img width="100%" src={this.state.image} alt="#" className="w-100" />
                          ) : (
                            <img width="100%" src={this.state.image} alt="#" className="w-100" />
                          )}
                        </div>
                      )}

                      {this.state.selectGender === 'thumb' ? (
                        <div className="pt-3">
                          {this.state.fingerThumb === true ? (
                            <div className="">
                              <div className="float-left ">
                                <Button onClick={this.capture}>
                                  {' '}
                                  <Trans i18nKey={'biometric.capture'}></Trans>{' '}
                                  <i className="fa fa-retweet" aria-hidden="true">
                                    {' '}
                                  </i>
                                </Button>
                              </div>
                              <div className="float-right ">
                                <Button onClick={this.matcher}>
                                  {' '}
                                  <Trans i18nKey={'biometric.matcher'}></Trans>{' '}
                                  <i className="fa fa-save" aria-hidden="true">
                                    {' '}
                                  </i>
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="float-right  ">
                              <Button onClick={(e) => this.handleThumbFinger(e)}>
                                {' '}
                                <Trans i18nKey={'biometric.thumb_finger'}></Trans>{' '}
                                <i className="fa fa-camera" aria-hidden="true">
                                  {' '}
                                </i>
                              </Button>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="pt-3">
                          {this.state.fingerIndex === true ? (
                            <div className="float-right ">
                              <Button onClick={(e) => this.handleIndexFingerConfirm(e)}>
                                {' '}
                                <Trans i18nKey={'save'}></Trans>{' '}
                                <i className="fa fa-save" aria-hidden="true">
                                  {' '}
                                </i>
                              </Button>
                            </div>
                          ) : (
                            <div className="float-right  ">
                              <Button onClick={(e) => this.handleIndexFinger(e)}>
                                {' '}
                                <Trans i18nKey={'biometric.index_finger'}></Trans>
                                <i className="fa fa-camera" aria-hidden="true">
                                  {' '}
                                </i>
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <br />
              </Modal.Body>
            </Modal>
            {/* fingerModel end  */}
            <div className="clearfix"></div>
            <div className="white p-4 mb-5">
              <div className="row">
                <div className="col-md-8">
                  <div className="img_border">
                    <div className="d-flex justify-content-between">
                      <div className="">
                        <h4 className="pb-3 pt-2">
                          {' '}
                          <Trans i18nKey={'biometric.bio_verify'}></Trans>{' '}
                        </h4>
                        {this.state.image2 === null || this.state.image2 === undefined ? (
                          <img
                            width="100%"
                            onClick={() => this.setState({ isOpen: true })}
                            src={Avatar}
                            alt="#"
                            className="w-100"
                          />
                        ) : (
                          <img
                            width="100%"
                            onClick={() => this.setState({ isOpen: true })}
                            // src={this.state.image2}
                            src={PrivatePerson}
                            alt="#"
                            className="w-100"
                          />
                        )}
                      </div>
                      <div className="d-flex justify-content-end">
                        {/* {this.state.image2 === null ? null : ( */}
                        <div>
                          <Button
                            variant="outline-primary"
                            className="m-2"
                            onClick={this.handleGoBack}
                          >
                            <Trans i18nKey={'back'}></Trans>{' '}
                          </Button>
                          {(CheckPermission(
                            'tabs',
                            'User Management',
                            'Staff Management',
                            '',
                            permissionName,
                            'Biometric Edit'
                          ) ||
                            CheckPermission(
                              'subTabs',
                              'User Management',
                              'Staff Management',
                              '',
                              'Registration Pending',
                              '',
                              permissionName,
                              'Biometric Edit'
                            ) ||
                            CheckPermission(
                              'tabs',
                              'User Management',
                              'Student Management',
                              '',
                              permissionName,
                              'Biometric Edit'
                            ) ||
                            CheckPermission(
                              'subTabs',
                              'User Management',
                              'Student Management',
                              '',
                              'Registration Pending',
                              '',
                              permissionName,
                              'Biometric Edit'
                            )) && (
                            <>
                              {/* <Button
                                className="m-2"
                                variant="outline-primary"
                                onClick={this.handleFace}
                              >
                                <Trans i18nKey={'biometric.capture'}></Trans>{' '}
                              </Button> */}
                              <Button
                                className="m-2"
                                variant="outline-primary"
                                onClick={this.handleNewFace}
                              >
                                Biometric Capture
                              </Button>
                            </>
                          )}{' '}
                        </div>
                        {/* )} */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

ValidBiometric.propTypes = {
  userStatusStudent: PropTypes.string,
  userTypeStudent: PropTypes.string,
  biometricName: PropTypes.string,
  history: PropTypes.object,
  location: PropTypes.object,
  handleGoBack: PropTypes.func,
  userAcademicId: PropTypes.string,
};

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps)(withRouter(ValidBiometric));
