import React from 'react';
import PropTypes from 'prop-types';
import {
  DialogActions,
  Dialog,
  DialogTitle,
  DialogContent,
  Switch,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { Map } from 'immutable';
import { dialogSX } from './ModelsUtils';
import { placeholderStyles } from '../style/designUtils';
import { useManipulateDataForTags } from '../utils';
import { makeStyles } from '@mui/styles';
import { switchSX } from '../ConfigureTemplate/TemplateSettings/Tag';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';

//----------------------------------Utils Start-----------------------------------------------------
const radioIconStyle = {
  '& .MuiSvgIcon-root': {
    fontSize: 'clamp(0.875em, 2vw, 1.0625em)',
  },
};
const radioControl = () => {
  return <Radio size="small" disableRipple sx={radioIconStyle} className="p-0" />;
};
//----------------------------------Utils End-------------------------------------------------------
const layOut = Map({
  name: '',
  isDefault: false,
  level: '',
  isActive: true,
  isEdited: true,
});
export const label_levels_qlc = ['Institution Level', 'Program Level', 'Course Level'];
export const value_label_qlc = ['institution', 'program', 'course'];
export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '14px', // Adjust the font size as needed
    color: '#4B5563',
    fontWeight: 400,
    paddingLeft: '5px',
    // You can also add other styles here, such as fontFamily, fontWeight, etc.
  },
}));
const CreateTags = ({ editIndex, open, existData = Map(), handleClose, setTagData }) => {
  const [tags, setTags, handleValidate, handleDelete] = useManipulateDataForTags(
    existData,
    handleClose,
    setTagData,
    editIndex
  );
  const classes = useStyles();
  return (
    <Dialog fullWidth open={open} onClose={handleClose} PaperProps={{ sx: dialogSX }}>
      <DialogTitle className="text-mGrey pb-0 ">
        <h5 className="pt-3 fw-400 pl-2 f-24 ">
          {editIndex === undefined ? 'Create ' : 'Edit '} Tags
        </h5>
      </DialogTitle>
      <DialogContent>
        {tags.map((tag, index) => {
          return (
            <section key={index} className="pt-3">
              <div className="d-flex align-items-center">
                <div className="flex-grow-1 f-12 fw-400 text-mGrey d-flex align-items-center">
                  <span className="mr-2">Name</span>
                  <EnableOrDisable valid={index !== 0}>
                    <DeleteIcon
                      sx={{ fontSize: '1rem', color: 'red' }}
                      onClick={() => handleDelete(index)}
                    />
                  </EnableOrDisable>
                </div>
                <div className="f-14 fw-400 text-lGrey">Default</div>
                <FormControlLabel
                  checked={tag.get('isDefault', false)}
                  className="m-0"
                  control={
                    <Switch
                      checked={tag.get('isDefault', false)}
                      sx={switchSX}
                      onChange={(e) =>
                        setTags((prev) => prev.setIn([index, 'isDefault'], e.target.checked))
                      }
                    />
                  }
                  classes={{ label: classes.label }}
                  label={tag.get('isDefault', false) ? 'ON' : 'OFF'}
                />
              </div>
              <TextField
                value={tag.get('name', '')}
                onChange={(e) => setTags((prev) => prev.setIn([index, 'name'], e.target.value))}
                placeholder="Enter Name"
                size="small"
                fullWidth
                sx={placeholderStyles}
              />
              <div className="pt-2 ">
                <label className="m-0 f-12 fw-400 text-mGrey">Level</label>
                <RadioGroup
                  value={tag.get('level', '')}
                  row
                  className="d-flex gap-8 flex-wrap"
                  onChange={(e) => setTags((prev) => prev.setIn([index, 'level'], e.target.value))}
                >
                  {value_label_qlc.map((option, i) => (
                    <FormControlLabel
                      className="m-0 d-flex gap-2"
                      key={option}
                      value={option}
                      control={radioControl()}
                      label={label_levels_qlc[i]}
                      classes={{ label: classes.label }}
                    />
                  ))}
                </RadioGroup>
              </div>
            </section>
          );
        })}
        {editIndex === undefined && (
          <div
            className="mt-1 f-14 fw-400 text-primary pt-3 cursor-pointer"
            onClick={() => setTags((prev) => prev.push(layOut))}
          >
            + Add Tag
          </div>
        )}
      </DialogContent>
      <DialogActions className="px-4 pb-4 gap-2">
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall text-secondary border-secondary bold"
          variant="outlined"
          onClick={() => {
            handleClose();
          }}
        >
          Cancel
        </Button>
        <Button
          className="text-capitalize px-4 responsiveFontSizeSmall color-blue"
          variant="contained"
          onClick={() => {
            handleValidate();
          }}
        >
          {editIndex === undefined ? 'Create' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

CreateTags.propTypes = {
  open: PropTypes.bool,
  editIndex: PropTypes.number,
  handleClose: PropTypes.func,
  existData: PropTypes.instanceOf(Map),
  setTagData: PropTypes.func,
};

export default CreateTags;
