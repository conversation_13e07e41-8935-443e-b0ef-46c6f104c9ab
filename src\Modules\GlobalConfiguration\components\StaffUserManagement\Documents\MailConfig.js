import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { Trans } from 'react-i18next';
import ReactDatePicker from 'react-datepicker';
import { format } from 'date-fns';
import { Checkbox, FormControlLabel, RadioGroup, Radio } from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import EmailConfigure from 'Modules/GlobalConfiguration/modal/EmailConfigure';
import { checkBoxStyles } from 'Modules/GlobalConfiguration/utils';
import { checkOnlyNumber } from 'v2/utils';
import i18n from '../../../../../i18n';

const MailConfig = (props) => {
  const { updateDocumentRemainder, documents, settingId, type } = props;
  const [recurringInterval, setRecurringInterval] = useState(1);
  const [emailOpen, setEmailOpen] = useState(false);
  const [labelName, setLabel] = useState(false);
  const classes = checkBoxStyles();

  const today = new Date();
  today.setHours(0, 0);
  const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  const mailConfiguration = documents.get('allowRemainderMail', Map());
  const notificationMail = documents.get('notificationMail', Map());
  const activeDays = () => mailConfiguration.get('weeks', List()).toJS();

  const handlChangeRecurringTime = (date) => {
    updateDocumentRemainder({
      requestBody: {
        time: new Date(date).toLocaleTimeString('en-us', {
          hour: '2-digit',
          minute: '2-digit',
        }),
        isMandatoryDocuments: documents.getIn(
          ['allowRemainderMail', 'isMandatoryDocuments'],
          false
        ),
        weeks: activeDays(),
        notificationMailLabel: notificationMail.get('label', ''),
        notificationMailTime: notificationMail.get('time', ''),
        recurring: documents.getIn(['allowRemainderMail', 'recurring'], ''),
        recurringInterval: documents.getIn(['allowRemainderMail', 'recurringInterval'], ''),
      },
      // callBack: handleCallBack,
      settingId,
      type,
    });
  };
  const handleDaysCheck = (e) => {
    const { checked, value } = e.target;

    const tempWeeks = documents.getIn(['allowRemainderMail', 'weeks'], List()).toJS();
    updateDocumentRemainder({
      requestBody: {
        time: documents.getIn(['allowRemainderMail', 'time'], ''),
        isMandatoryDocuments: documents.getIn(
          ['allowRemainderMail', 'isMandatoryDocuments'],
          false
        ),
        weeks: checked ? tempWeeks.concat([value]) : tempWeeks.filter((item) => item !== value),
        recurring: documents.getIn(['allowRemainderMail', 'recurring'], ''),
        notificationMailLabel: notificationMail.get('label', ''),
        notificationMailTime: notificationMail.get('time', ''),
        recurringInterval: documents.getIn(['allowRemainderMail', 'recurringInterval'], ''),
      },
      // callBack: handleCallBack,
      settingId,
      type,
    });
  };
  const handleCheckMandatoryDocuments = (e) => {
    updateDocumentRemainder({
      requestBody: {
        time: documents.getIn(['allowRemainderMail', 'time'], ''),
        isMandatoryDocuments: e.target.checked,
        weeks: activeDays(),
        recurring: documents.getIn(['allowRemainderMail', 'recurring'], ''),
        recurringInterval: documents.getIn(['allowRemainderMail', 'recurringInterval'], ''),
        notificationMailLabel: notificationMail.get('label', ''),
        notificationMailTime: notificationMail.get('time', ''),
      },
      // callBack: handleCallBack,
      settingId,
      type,
    });
  };
  const didMount = useRef(true);
  useEffect(() => {
    if (didMount.current) {
      didMount.current = false;
      return;
    }
    const timeout = setTimeout(() => {
      updateDocumentRemainder({
        requestBody: {
          time: documents.getIn(['allowRemainderMail', 'time'], ''),
          isMandatoryDocuments: documents.getIn(
            ['allowRemainderMail', 'isMandatoryDocuments'],
            false
          ),
          weeks: activeDays(),
          recurring: documents.getIn(['allowRemainderMail', 'recurring'], ''),
          recurringInterval,
          notificationMailLabel: notificationMail.get('label', ''),
          notificationMailTime: notificationMail.get('time', ''),
        },
        settingId,
        type,
      });
    }, 500);
    return () => {
      clearTimeout(timeout);
    };
  }, [recurringInterval]); // eslint-disable-line
  const handleInterval = (e) => {
    const { value } = e.target;
    if (!checkOnlyNumber(value) && e.target.value !== '') {
      return false;
    } else {
      setRecurringInterval(value);
    }
  };
  const handleRecurringRadio = (e) => {
    updateDocumentRemainder({
      requestBody: {
        time: documents.getIn(['allowRemainderMail', 'time'], ''),
        isMandatoryDocuments: documents.getIn(
          ['allowRemainderMail', 'isMandatoryDocuments'],
          false
        ),
        weeks: activeDays(),
        notificationMailLabel: notificationMail.get('label', ''),
        notificationMailTime: notificationMail.get('time', ''),
        recurring: e.target.value,
        recurringInterval: documents.getIn(['allowRemainderMail', 'recurringInterval'], ''),
      },
      // callBack: handleCallBack,
      settingId,
      type,
    });
  };
  const handleNotificationTime = (date) => {
    date !== null &&
      updateDocumentRemainder({
        requestBody: {
          time: documents.getIn(['allowRemainderMail', 'time'], ''),
          isMandatoryDocuments: documents.getIn(
            ['allowRemainderMail', 'isMandatoryDocuments'],
            false
          ),
          weeks: activeDays(),
          notificationMailLabel: notificationMail.get('label', ''),
          notificationMailTime: new Date(date).toLocaleTimeString('en-us', {
            hour: '2-digit',
            minute: '2-digit',
          }),
          recurring: documents.getIn(['allowRemainderMail', 'recurring'], ''),
          recurringInterval: documents.getIn(['allowRemainderMail', 'recurringInterval'], ''),
        },
        //  callBack: handleCallBack,
        settingId,
        type,
      });
  };
  const handleEmailClose = () => {
    setEmailOpen(false);
  };
  const handleEmailOpen = () => {
    setLabel(notificationMail.get('label', ''));
    setEmailOpen(true);
  };
  return (
    <>
      <div className="bold digi-pt-8 col-sm-6 col-md-4 col-lg-3">
        <Trans i18nKey={'global_configuration.notification_mail'}></Trans>
      </div>

      <div className="row">
        <div className="col-md-11">
          <div className="d-flex justify-content-between digi-pl-12">
            <div className="d-flex">
              <div className="f-14 text-lightgray digi-brown digi-pt-20 digi-pl-8">
                <Trans
                  i18nKey={'global_configuration.mandatory_document_notification_mail'}
                ></Trans>
              </div>
            </div>
          </div>
        </div>

        <div className="col-md-1 pl-0">
          <p
            className="text-lightgray text-skyblue bold pt-2 remove_hover"
            onClick={handleEmailOpen}
          >
            Edit
          </p>
        </div>
      </div>
      <div className="mt-2 digi-pl-20 digi-pb-16">
        <p className="f-13 text-lightgray mb-1">
          <Trans i18nKey={'global_configuration.select_time'}></Trans>
        </p>
        <ReactDatePicker
          selected={new Date(format(today, 'MM/dd/yyyy') + ' ' + notificationMail.get('time', ''))}
          onChange={(date) => handleNotificationTime(date)}
          showTimeSelect
          showTimeSelectOnly
          timeIntervals={15}
          timeCaption="Time"
          dateFormat="h:mm aa"
          className="global-date-picker-input icon_date"
          placeholderText="Select Time"
        />
      </div>
      <hr />
      <div className="bold digi-pt-8 col-sm-6 col-md-4 col-lg-3">
        <Trans i18nKey={'userManagement.set_reminder'}></Trans>
      </div>

      <div className="f-14 text-lightgray digi-pl-20 digi-pt-12">
        <FormControlLabel
          control={
            <Checkbox
              classes={{ root: classes.root }}
              color="primary"
              checked={mailConfiguration.get('isMandatoryDocuments', false)}
              onChange={(e) => handleCheckMandatoryDocuments(e)}
            />
          }
          value={mailConfiguration.get('isMandatoryDocuments', false)}
          label={i18n.t('global_configuration.Mandatory_Documents')}
        />
      </div>
      <div className="digi-pl-16">
        <Trans i18nKey={'global_configuration.recurring'}></Trans>
      </div>

      <div className="col-sm-6 col-md-4 col-lg-6 pl-3">
        <RadioGroup
          row
          aria-label="position"
          name="position"
          value={mailConfiguration.get('recurring', 'daily')}
          onChange={(e) => handleRecurringRadio(e)}
        >
          <FormControlLabel
            className="px-0"
            value="daily"
            control={<Radio color="primary" />}
            label={<Trans i18nKey={'global_configuration.daily'}></Trans>}
            labelPlacement="end"
          />
          <FormControlLabel
            className="px-3"
            value="weekly"
            control={<Radio color="primary" />}
            label={<Trans i18nKey={'global_configuration.weekly'}></Trans>}
            labelPlacement="end"
          />
          <FormControlLabel
            className="px-3"
            value="custom"
            control={<Radio color="primary" />}
            label={<Trans i18nKey={'global_configuration.custom'}></Trans>}
            labelPlacement="end"
          />
        </RadioGroup>
      </div>
      {mailConfiguration.size > 0 && mailConfiguration.get('recurring', '') === 'custom' && (
        <div className="d-flex align-items-center pl-3">
          <p className="pt-2 pr-2">
            {' '}
            <Trans i18nKey={'global_configuration.recur_every'}></Trans>
          </p>
          <div className="w-12">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              changed={(e) => handleInterval(e)}
              value={mailConfiguration.get('recurringInterval', 1)}
            />
          </div>
          <p className="text-lightgray pl-2 pt-3">
            <Trans i18nKey={'global_configuration.day_s'} />
          </p>{' '}
        </div>
      )}
      {mailConfiguration.size > 0 && mailConfiguration.get('recurring', '') === 'weekly' && (
        <div className="d-flex align-items-center pl-3">
          {weekdays.map((weekday) => (
            <FormControlLabel
              key={weekday}
              control={
                <Checkbox
                  classes={{ root: classes.root }}
                  checked={activeDays().includes(weekday)}
                  color="primary"
                  value={weekday}
                  onChange={(e) => handleDaysCheck(e)}
                  name="weekday"
                />
              }
              label={weekday}
            />
          ))}
        </div>
      )}
      <div className="mt-2 digi-pl-20 digi-pb-16">
        <p className="f-13 text-lightgray mb-1">
          <Trans i18nKey={'global_configuration.select_time'}></Trans>{' '}
        </p>
        <ReactDatePicker
          selected={
            new Date(format(today, 'MM/dd/yyyy') + ' ' + mailConfiguration.get('time', '10:00 am'))
          }
          onChange={(date) => handlChangeRecurringTime(date)}
          showTimeSelect
          showTimeSelectOnly
          timeIntervals={15}
          timeCaption="Time"
          dateFormat="h:mm aa"
          className="global-date-picker-input icon_date"
        />
      </div>
      {emailOpen && (
        <EmailConfigure
          open={emailOpen}
          setting={fromJS({ labelName })}
          settingId={settingId}
          handleClose={() => handleEmailClose()}
          source={'document'}
        />
      )}
    </>
  );
};
MailConfig.propTypes = {
  updateDocumentRemainder: PropTypes.func,
  documents: PropTypes.instanceOf(Map),
  settingId: PropTypes.string,
  type: PropTypes.string,
  setData: PropTypes.func,
};
export default MailConfig;
