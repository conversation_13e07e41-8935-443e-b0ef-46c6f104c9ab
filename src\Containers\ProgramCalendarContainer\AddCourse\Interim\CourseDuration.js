import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { FlexWrapper, BlockWrapper, Select, Gap, Label } from '../../Styled';
import DateInput from '../../UtilityComponents/DateInput';
import moment from 'moment';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getLang, indVerRename, isWeekEndDay, isWeekStartDay } from 'utils';
const lang = getLang();

export default function CourseDuration({ data, startDate, endDate, rotation, programId }) {
  const [nonRotation, setNonRotation] = data;
  const dateSwitch = (dispatchFn, type, name, startDate, endDate) => {
    if (name === 'same_dates' || name === 'choose_dates') {
      dispatchFn({
        type: 'SELECT_DATES',
        payload: type,
        start_date: startDate,
        end_date: endDate,
        name: name,
      });
    }
  };

  //todo

  return (
    <Fragment>
      <div className="container-fluid pb-5">
        <div className="p-40">
          <BlockWrapper>
            <h5 className={`${lang !== 'ar' ? '' : 'text-left'}`}>
              <Trans i18nKey={'program_calendar.course_duration'}></Trans>
            </h5>
            {rotation === 'no' && (
              <FlexWrapper mg="10px 0">
                <input
                  type="radio"
                  name="select_dates"
                  id="same_dates"
                  checked={nonRotation.select_dates === 'same_dates' && true}
                  onChange={(e) =>
                    dateSwitch(setNonRotation, 'select_dates', 'same_dates', startDate, endDate)
                  }
                />
                <Label
                  mg={lang !== 'ar' ? '0px 0px 0px 5px' : '0px 5px 0px 0px'}
                  fs="16px"
                  htmlFor="same_dates"
                >
                  <Trans
                    i18nKey={'program_calendar.complete_level'}
                    values={{ Level: indVerRename('Level', programId) }}
                  ></Trans>
                </Label>
              </FlexWrapper>
            )}
            {nonRotation.select_dates === 'same_dates' && (
              <div className="row">
                <div className="col-xl-3">
                  <DateInput
                    datepicker={true}
                    title={t('program_calendar.start_date_read_only')}
                    value={moment(nonRotation['type']['start_date']).format('D MMM YYYY')}
                    read={true}
                  />
                </div>

                <div className="col-xl-3">
                  <DateInput
                    title={t('program_calendar.end_date_read_only')}
                    datepicker={true}
                    value={moment(nonRotation['type']['end_date']).format('D MMM YYYY')}
                    read={true}
                  />
                </div>
              </div>
            )}
            <FlexWrapper mg="10px 0">
              <input
                type="radio"
                name="select_dates"
                id="choose_dates"
                checked={nonRotation.select_dates === 'choose_dates' && true}
                onChange={(e) => dateSwitch(setNonRotation, e.target.name, 'choose_dates', '', '')}
              />
              <Label
                mg={lang !== 'ar' ? '0px 0px 0px 5px' : '0px 5px 0px 0px'}
                fs="16px"
                htmlFor="choose_dates"
              >
                <Trans i18nKey={'program_calendar.select_new_dates'}></Trans>
              </Label>
            </FlexWrapper>
            {nonRotation.select_dates === 'choose_dates' && (
              <BlockWrapper>
                <FlexWrapper mg="0 30px">
                  <input
                    type="radio"
                    name="custom_dates"
                    value="academic_week"
                    checked={nonRotation.custom_dates === 'academic_week'}
                    id="by_academic"
                    onChange={(e) =>
                      setNonRotation({
                        type: 'SELECT_CUSTOM_DATES',
                        name: e.target.name,
                        payload: e.target.value,
                        start_date: startDate,
                        end_date: endDate,
                      })
                    }
                  />
                  <Label
                    mg={lang !== 'ar' ? '0px 0px 0px 5px' : '0px 5px 0px 0px'}
                    fs="16px"
                    htmlFor="by_academic"
                  >
                    {' '}
                    <Trans i18nKey={'program_calendar.by_academic_weeks'}></Trans>{' '}
                  </Label>
                </FlexWrapper>

                {nonRotation.custom_dates === 'academic_week' && (
                  <div className="pl-35 pt-3">
                    <div className="row">
                      <div className="col-xl-2">
                        <b className="pr-2 pt-1 float-right">
                          <Trans i18nKey={'program_calendar.from_week_number'}></Trans>
                        </b>
                      </div>

                      <div className="col-xl-3">
                        <Select
                          name="academic_week_start"
                          value={nonRotation.academic_week_start}
                          className={'styled-select'}
                          onChange={(e) =>
                            setNonRotation({
                              type: 'WEEK_START_CHANGE',
                              name: e.target.name,
                              payload: e.target.value,
                            })
                          }
                        >
                          <option value={0}> Select Start Week</option>
                          {nonRotation.academic_weeks &&
                            nonRotation.academic_weeks.map((item) => (
                              <option key={item.range} value={item.row_start}>
                                {t('program_calendar.week')} {item.row_start} - ( {item.range} )
                              </option>
                            ))}
                        </Select>{' '}
                      </div>

                      <div className="col-xl-1">
                        <b className="pr-2 pt-1 float-right">To</b>
                      </div>

                      <div className="col-xl-3">
                        <Select
                          name="academic_week_end"
                          value={nonRotation.academic_week_end}
                          className={'styled-select'}
                          onChange={(e) =>
                            setNonRotation({
                              type: 'WEEK_END_CHANGE',
                              name: e.target.name,
                              payload: e.target.value,
                            })
                          }
                        >
                          <option value={0}> Select End Week</option>
                          {nonRotation.academic_weeks &&
                            nonRotation.academic_weeks.map((item) => (
                              <option key={item.range} value={item.row_start}>
                                {t('program_calendar.week')} {item.row_start} - ( {item.range} )
                              </option>
                            ))}
                        </Select>{' '}
                      </div>
                    </div>
                  </div>
                )}

                <FlexWrapper mg="10px 30px">
                  <input
                    type="radio"
                    name="custom_dates"
                    value="dates"
                    checked={nonRotation.custom_dates === 'dates'}
                    id="by_dates"
                    onChange={(e) =>
                      setNonRotation({
                        type: 'SELECT_CUSTOM_DATES',
                        name: e.target.name,
                        payload: e.target.value,
                      })
                    }
                  />
                  <Label
                    mg={lang !== 'ar' ? '0px 0px 0px 5px' : '0px 5px 0px 0px'}
                    fs="16px"
                    htmlFor="by_dates"
                  >
                    {' '}
                    <Trans i18nKey={'program_calendar.by_dates'}></Trans>
                  </Label>
                </FlexWrapper>
                {nonRotation.custom_dates === 'dates' && (
                  <FlexWrapper mg="10px 45px" className="ai_end">
                    <DateInput
                      datepicker={true}
                      isWeekStartDay={isWeekStartDay()}
                      placeholderText="Start Date"
                      selected={
                        nonRotation.type.start_date !== ''
                          ? new Date(nonRotation.type.start_date)
                          : null
                      }
                      value={
                        nonRotation.type.start_date !== ''
                          ? moment(nonRotation.type.start_date).format('D MMM YYYY')
                          : null
                      }
                      edit={(value) => {
                        setNonRotation({
                          type: 'ON_CHANGE',
                          name: 'start_date',
                          payload: value,
                        });
                      }}
                      title="Start date"
                      minDate={startDate !== '' ? new Date(startDate) : ''}
                      maxDate={endDate !== '' ? new Date(endDate) : ''}
                    />
                    {/* <DateInput
                      title="Start date"
                      min={(() => {
                        let start = startDate;
                        let final = "";
                        if (start !== "") {
                          let day = new Date(start).getDay();
                          if (day !== 0) {
                            let pre = Date.parse(start) + 86400000 * (7 - day);
                            final = new Date(pre).toISOString().slice(0, 10);
                          } else {
                            final = new Date(start).toISOString().slice(0, 10);
                          }
                        }
                        return final;
                      })()}
                      max={String(endDate)}
                      step="7"
                      value={nonRotation.type.start_date.slice(0, 10)}
                      edit={(value) =>
                        setNonRotation({
                          type: "ON_CHANGE",
                          name: "start_date",
                          payload: value,
                        })
                      }
                    /> */}
                    <Gap /> to <Gap />
                    <DateInput
                      datepicker={true}
                      isWeekEndDay={isWeekEndDay()}
                      placeholderText="End Date"
                      selected={
                        nonRotation.type.end_date !== ''
                          ? new Date(nonRotation.type.end_date)
                          : null
                      }
                      value={
                        nonRotation.type.end_date !== ''
                          ? moment(nonRotation.type.end_date).format('D MMM YYYY')
                          : null
                      }
                      edit={(value) => {
                        setNonRotation({
                          type: 'ON_CHANGE',
                          name: 'end_date',
                          payload: value,
                        });
                      }}
                      title={t('events.end_date')}
                      minDate={
                        nonRotation.type.start_date !== ''
                          ? new Date(nonRotation.type.start_date)
                          : ''
                      }
                      maxDate={endDate !== '' ? new Date(endDate) : ''}
                    />
                    {/* <DateInput
                      title="End date"
                      min={(() => {
                        let start = nonRotation.type.start_date;
                        let final = "";
                        if (start !== "") {
                          let pre = Date.parse(start) + 86400000 * 4;
                          final = new Date(pre).toISOString().slice(0, 10);
                        }
                        return final;
                      })()}
                      max={String(endDate)}
                      step="7"
                      value={nonRotation.type.end_date.slice(0, 10)}
                      edit={(value) =>
                        setNonRotation({
                          type: "ON_CHANGE",
                          name: "end_date",
                          payload: value,
                        })
                      }
                    /> */}
                  </FlexWrapper>
                )}
              </BlockWrapper>
            )}
          </BlockWrapper>
        </div>
      </div>
    </Fragment>
  );
}

CourseDuration.propTypes = {
  data: PropTypes.func,
  startDate: PropTypes.string,
  endDate: PropTypes.string,
  rotation: PropTypes.string,
  programId: PropTypes.string,
};
