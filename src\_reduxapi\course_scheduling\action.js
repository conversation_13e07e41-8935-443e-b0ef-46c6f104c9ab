import { createAction } from '../util';
import axios from '../../axios';
import { fromJS, Map, List } from 'immutable';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';
const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');
export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}
export const GET_EXTRA_CURRICULAR_LIST_REQUEST = 'GET_EXTRA_CURRICULAR_LIST_REQUEST';
export const GET_EXTRA_CURRICULAR_LIST_SUCCESS = 'GET_EXTRA_CURRICULAR_LIST_SUCCESS';
export const GET_EXTRA_CURRICULAR_LIST_FAILURE = 'GET_EXTRA_CURRICULAR_LIST_FAILURE';

const getExtraCurricularListRequest = createAction(GET_EXTRA_CURRICULAR_LIST_REQUEST);
const getExtraCurricularListSuccess = createAction(GET_EXTRA_CURRICULAR_LIST_SUCCESS, 'data');
const getExtraCurricularListFailure = createAction(GET_EXTRA_CURRICULAR_LIST_FAILURE, 'error');

export function getExtraCurricularList(programId, calendarId) {
  return function (dispatch) {
    dispatch(getExtraCurricularListRequest());
    axios
      .get(`/course-schedule-settings/extraCurricular-breakTiming/${programId}/${calendarId}`)
      .then((res) => dispatch(getExtraCurricularListSuccess(res.data.data)))
      .catch((error) => dispatch(getExtraCurricularListFailure(error)));
  };
}

export const GET_REMOTE_ROOMS_REQUEST = 'GET_REMOTE_ROOMS_REQUEST';
export const GET_REMOTE_ROOMS_SUCCESS = 'GET_REMOTE_ROOMS_SUCCESS';
export const GET_REMOTE_ROOMS_FAILURE = 'GET_REMOTE_ROOMS_FAILURE';

const getRemoteRoomsRequest = createAction(GET_REMOTE_ROOMS_REQUEST);
const getRemoteRoomsSuccess = createAction(GET_REMOTE_ROOMS_SUCCESS, 'data');
const getRemoteRoomsFailure = createAction(GET_REMOTE_ROOMS_FAILURE, 'error');

export function getRemoteRooms(programId) {
  return function (dispatch) {
    dispatch(getRemoteRoomsRequest());
    axios
      .get(`/course_schedule_setting/remote-scheduling/list/${programId}`)
      .then((res) => dispatch(getRemoteRoomsSuccess(res.data.data)))
      .catch((error) => dispatch(getRemoteRoomsFailure(error)));
  };
}

export const SAVE_REMOTE_ROOM_REQUEST = 'SAVE_REMOTE_ROOM_REQUEST';
export const SAVE_REMOTE_ROOM_SUCCESS = 'SAVE_REMOTE_ROOM_SUCCESS';
export const SAVE_REMOTE_ROOM_FAILURE = 'SAVE_REMOTE_ROOM_FAILURE';

const saveRemoteRoomRequest = createAction(SAVE_REMOTE_ROOM_REQUEST);
const saveRemoteRoomSuccess = createAction(SAVE_REMOTE_ROOM_SUCCESS, 'data', 'operation');
const saveRemoteRoomFailure = createAction(SAVE_REMOTE_ROOM_FAILURE, 'error');

export function saveRemoteRoom({ mode, programId, _id, requestBody, callback }) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveRemoteRoomRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](
        `/course_schedule_setting/${mode === 'create' ? programId : _id + '/' + programId}`,
        mode !== 'delete' ? requestBody : {}
      )
        .then((res) => {
          const operation = {
            create: 'Added',
            update: 'Edited',
            delete: 'Deleted',
          };
          callback && callback();
          dispatch(saveRemoteRoomSuccess(res.data.data, operation[mode]));
          dispatch(getRemoteRooms(programId));
        })
        .catch((error) => dispatch(saveRemoteRoomFailure(error)));
    };
  }
}

export const GET_COURSE_COORDINATORS_REQUEST = 'GET_COURSE_COORDINATORS_REQUEST';
export const GET_COURSE_COORDINATORS_SUCCESS = 'GET_COURSE_COORDINATORS_SUCCESS';
export const GET_COURSE_COORDINATORS_FAILURE = 'GET_COURSE_COORDINATORS_FAILURE';

const getCourseCoordinatorsRequest = createAction(GET_COURSE_COORDINATORS_REQUEST);
const getCourseCoordinatorsSuccess = createAction(GET_COURSE_COORDINATORS_SUCCESS, 'data');
const getCourseCoordinatorsFailure = createAction(GET_COURSE_COORDINATORS_FAILURE, 'error');

export function getCourseCoordinators(institutionCalendarId, programId) {
  return function (dispatch) {
    dispatch(getCourseCoordinatorsRequest());
    axios
      .get(`/course_coordinator/coordinators/${institutionCalendarId}/${programId}`)
      .then((res) => dispatch(getCourseCoordinatorsSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseCoordinatorsFailure(error)));
  };
}

export const ASSIGN_COURSE_COORDINATOR_REQUEST = 'ASSIGN_COURSE_COORDINATOR_REQUEST';
export const ASSIGN_COURSE_COORDINATOR_SUCCESS = 'ASSIGN_COURSE_COORDINATOR_SUCCESS';
export const ASSIGN_COURSE_COORDINATOR_FAILURE = 'ASSIGN_COURSE_COORDINATOR_FAILURE';

const assignCourseCoordinatorRequest = createAction(ASSIGN_COURSE_COORDINATOR_REQUEST);
const assignCourseCoordinatorSuccess = createAction(ASSIGN_COURSE_COORDINATOR_SUCCESS, 'data');
const assignCourseCoordinatorFailure = createAction(ASSIGN_COURSE_COORDINATOR_FAILURE, 'error');

export function assignCourseCoordinator(
  requestBody,
  coordinator,
  callback,
  institutionCalendarId,
  programId
) {
  return function (dispatch) {
    dispatch(assignCourseCoordinatorRequest());
    axios
      .post('/course_coordinator', requestBody)
      .then((_) => {
        dispatch(
          assignCourseCoordinatorSuccess(
            Map({
              _course_id: requestBody._course_id,
              coordinator,
            })
          )
        );
        dispatch(getCourseCoordinators(institutionCalendarId, programId));
        callback && callback();
      })
      .catch((error) => dispatch(assignCourseCoordinatorFailure(error)));
  };
}

export const SAVE_EXTRA_CURRICULAR_REQUEST = 'SAVE_EXTRA_CURRICULAR_REQUEST';
export const SAVE_EXTRA_CURRICULAR_SUCCESS = 'SAVE_EXTRA_CURRICULAR_SUCCESS';
export const SAVE_EXTRA_CURRICULAR_FAILURE = 'SAVE_EXTRA_CURRICULAR_FAILURE';

const saveExtraCurricularAndTimingRequest = createAction(SAVE_EXTRA_CURRICULAR_REQUEST);
const saveExtraCurricularAndTimingSuccess = createAction(
  SAVE_EXTRA_CURRICULAR_SUCCESS,
  'data',
  'operation'
);
const saveExtraCurricularAndTimingFailure = createAction(SAVE_EXTRA_CURRICULAR_FAILURE, 'error');

export function saveExtraCurricularAndTiming({
  mode,
  programId,
  _id,
  instCalId,
  requestBody,
  type,
  callback,
}) {
  if (['create', 'update', 'delete', 'status'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveExtraCurricularAndTimingRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
        status: 'put',
      };
      axios[method[mode]](
        `/course-schedule-settings/extraCurricular-breakTiming${
          mode === 'create'
            ? `?programId=${programId}&instCalId=${instCalId}`
            : mode === 'status'
            ? `/enable-disable/${_id}/${programId}`
            : '/' + _id + '/' + programId
        }`,
        mode !== 'delete' ? requestBody : {}
      )
        .then((res) => {
          const operation = {
            create: 'Added',
            update: 'Edited',
            delete: 'Deleted',
            status: 'Status Updated',
          };
          callback && callback();
          dispatch(saveExtraCurricularAndTimingSuccess(type, operation[mode]));
          dispatch(getExtraCurricularList(programId, instCalId));
        })
        .catch((error) => dispatch(saveExtraCurricularAndTimingFailure(error)));
    };
  }
}

export const GET_COURSE_LIST_REQUEST = 'GET_COURSE_LIST_REQUEST';
export const GET_COURSE_LIST_SUCCESS = 'GET_COURSE_LIST_SUCCESS';
export const GET_COURSE_LIST_FAILURE = 'GET_COURSE_LIST_FAILURE';

const getCourseListRequest = createAction(GET_COURSE_LIST_REQUEST);
const getCourseListSuccess = createAction(GET_COURSE_LIST_SUCCESS, 'data');
const getCourseListFailure = createAction(GET_COURSE_LIST_FAILURE, 'error');

export function getCourseList(userId, institutionCalendarId, programId, roleId) {
  return function (dispatch) {
    dispatch(getCourseListRequest());
    axios
      .get(
        `/course_schedule/course_list/${institutionCalendarId}/${programId}/${userId}/${roleId}`,
        {
          headers: { _user_id: userId },
        }
      )
      .then((res) => dispatch(getCourseListSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseListFailure(error)));
  };
}

export const GET_COURSE_SCHEDULE_REQUEST = 'GET_COURSE_SCHEDULE_REQUEST';
export const GET_COURSE_SCHEDULE_SUCCESS = 'GET_COURSE_SCHEDULE_SUCCESS';
export const GET_COURSE_SCHEDULE_FAILURE = 'GET_COURSE_SCHEDULE_FAILURE';

const getCourseScheduleRequest = createAction(GET_COURSE_SCHEDULE_REQUEST);
const getCourseScheduleSuccess = createAction(GET_COURSE_SCHEDULE_SUCCESS, 'data');
const getCourseScheduleFailure = createAction(GET_COURSE_SCHEDULE_FAILURE, 'error');

export function getCourseSchedule({
  programId,
  institutionCalendarId,
  courseId,
  term,
  level,
  page = 1,
  pageSize = 10,
  sessionFlowId = '',
  isRefresh = false,
  callback,
  type = 'regular',
}) {
  return function (dispatch) {
    dispatch(getCourseScheduleRequest());
    axios
      .get(
        `/course_schedule/schedule_list/${programId}/${institutionCalendarId}/${courseId}/${term}/${level}`,
        {
          params: {
            // limit: pageSize,
            // pageNo: page,
            type,
          },
        }
      )
      .then((res) => {
        dispatch(
          getCourseScheduleSuccess({
            data: res.data.data,
            totalPages: res.data.totalPages,
            currentPage: res.data.currentPage,
            totalDoc: res.data.totalDoc,
            sessionFlowId,
            isRefresh: false,
          })
        );
        const response = fromJS(res.data.data);
        callback && callback(response.get('session_flow', List()));
      })
      .catch((error) => dispatch(getCourseScheduleFailure(error)));
  };
}

export const SAVE_SCHEDULE_REQUEST = 'SAVE_SCHEDULE_REQUEST';
export const SAVE_SCHEDULE_SUCCESS = 'SAVE_SCHEDULE_SUCCESS';
export const SAVE_SCHEDULE_FAILURE = 'SAVE_SCHEDULE_FAILURE';

const saveScheduleRequest = createAction(SAVE_SCHEDULE_REQUEST);
const saveScheduleSuccess = createAction(SAVE_SCHEDULE_SUCCESS, 'data', 'operation');
const saveScheduleFailure = createAction(SAVE_SCHEDULE_FAILURE, 'error');

export function saveSchedule({ mode, isReassign, _id, requestBody, page, callback, errCallback }) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveScheduleRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](
        `/course_schedule${mode !== 'create' ? '/' + _id : ''}`,
        mode !== 'delete' ? requestBody : {}
      )
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
            reassign: 'Re-Assigned',
          };
          callback && callback();
          dispatch(saveScheduleSuccess(res.data.data, operation[isReassign ? 'reassign' : mode]));
          const {
            _institution_calendar_id,
            _program_id,
            _course_id,
            term,
            level_no,
            _session_id,
          } = requestBody;
          dispatch(
            getCourseSchedule({
              institutionCalendarId: _institution_calendar_id,
              programId: _program_id,
              courseId: _course_id,
              term,
              level: level_no,
              sessionFlowId: _session_id,
              isRefresh: true,
              page,
              type: 'regular',
            })
          );
        })
        .catch((error) => {
          const { response: { status, data: { data = [], message = '' } = {} } = {} } = error;
          if (status === 409) {
            errCallback && errCallback(fromJS(data));
          } else {
            const errorMessage =
              message && typeof message === 'string'
                ? message
                : 'An error occurred. Please try again.';
            dispatch(setData(Map({ message: errorMessage })));
          }
          dispatch(saveScheduleFailure(error));
        });
    };
  }
}

export const SAVE_SUPPORT_AND_EVENTS_SCHEDULE_REQUEST = 'SAVE_SUPPORT_AND_EVENTS_SCHEDULE_REQUEST';
export const SAVE_SUPPORT_AND_EVENTS_SCHEDULE_SUCCESS = 'SAVE_SUPPORT_AND_EVENTS_SCHEDULE_SUCCESS';
export const SAVE_SUPPORT_AND_EVENTS_SCHEDULE_FAILURE = 'SAVE_SUPPORT_AND_EVENTS_SCHEDULE_FAILURE';

const saveSupportAndEventsScheduleRequest = createAction(SAVE_SUPPORT_AND_EVENTS_SCHEDULE_REQUEST);
const saveSupportAndEventsScheduleSuccess = createAction(
  SAVE_SUPPORT_AND_EVENTS_SCHEDULE_SUCCESS,
  'data',
  'viewType',
  'operation'
);
const saveSupportAndEventsScheduleFailure = createAction(
  SAVE_SUPPORT_AND_EVENTS_SCHEDULE_FAILURE,
  'error'
);

export function saveSupportAndEventsSchedule({
  mode,
  type,
  _id,
  requestBody,
  page,
  isReassign,
  callback,
  errCallback,
  _institution_calendar_id,
  _program_id,
  _course_id,
  term,
  level_no,
}) {
  if (['create', 'update', 'delete', 'cancel'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveSupportAndEventsScheduleRequest());
      const method = {
        create: 'put',
        update: 'put',
        delete: 'delete',
        cancel: 'put',
      };
      axios[method[mode]](
        `/course_schedule/${
          mode !== 'delete'
            ? mode === 'cancel'
              ? 'cancel_session'
              : 'create_schedule_support_session_event'
            : ''
        }/${_id}`,
        mode !== 'delete' ? (mode === 'cancel' ? { isActive: false } : requestBody) : {}
      )
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
            cancel: 'Canceled',
            reassign: 'Re-Assigned',
          };
          callback && callback();
          dispatch(
            saveSupportAndEventsScheduleSuccess(
              res.data.data,
              type,
              operation[isReassign ? 'reassign' : mode]
            )
          );
          if (mode === 'delete') {
            dispatch(setData(Map({ courseSchedule: Map() })));
          }
          dispatch(
            getCourseSchedule({
              institutionCalendarId: _institution_calendar_id,
              programId: _program_id,
              courseId: _course_id,
              term,
              level: level_no,
              ...(mode !== 'delete' && { sessionFlowId: _id, isRefresh: true, page }),
              type,
            })
          );
        })
        .catch((error) => {
          const { response: { status, data: { data = [], message = '' } = {} } = {} } = error;
          if (status === 409 && ['create', 'update'].includes(mode)) {
            errCallback && errCallback(fromJS(data));
          } else {
            const errorMessage =
              message && typeof message === 'string'
                ? message
                : 'An error occurred. Please try again.';
            dispatch(setData(Map({ message: errorMessage })));
          }
          dispatch(saveSupportAndEventsScheduleFailure(error));
        });
    };
  }
}

export const CANCEL_SCHEDULE_REQUEST = 'CANCEL_SCHEDULE_REQUEST';
export const CANCEL_SCHEDULE_SUCCESS = 'CANCEL_SCHEDULE_SUCCESS';
export const CANCEL_SCHEDULE_FAILURE = 'CANCEL_SCHEDULE_FAILURE';

const cancelScheduleRequest = createAction(CANCEL_SCHEDULE_REQUEST);
const cancelScheduleSuccess = createAction(CANCEL_SCHEDULE_SUCCESS, 'data');
const cancelScheduleFailure = createAction(CANCEL_SCHEDULE_FAILURE, 'error');

export function cancelSchedule({ _id, page, requestBody }) {
  return function (dispatch) {
    dispatch(cancelScheduleRequest());
    const {
      _institution_calendar_id,
      _program_id,
      _course_id,
      term,
      level_no,
      _session_id,
      isActive,
    } = requestBody;

    axios
      .put(`/course_schedule/cancel_session/${_id}`, { isActive })
      .then((res) => {
        dispatch(cancelScheduleSuccess(res.data.data));
        dispatch(
          getCourseSchedule({
            institutionCalendarId: _institution_calendar_id,
            programId: _program_id,
            courseId: _course_id,
            term,
            level: level_no,
            sessionFlowId: _session_id,
            isRefresh: true,
            page,
            type: 'regular',
          })
        );
      })
      .catch((error) => dispatch(cancelScheduleFailure(error)));
  };
}

export const GET_SCHEDULE_AVAILABILITY_REQUEST = 'GET_SCHEDULE_AVAILABILITY_REQUEST';
export const GET_SCHEDULE_AVAILABILITY_SUCCESS = 'GET_SCHEDULE_AVAILABILITY_SUCCESS';
export const GET_SCHEDULE_AVAILABILITY_FAILURE = 'GET_SCHEDULE_AVAILABILITY_FAILURE';

const getScheduleAvailabilityRequest = createAction(GET_SCHEDULE_AVAILABILITY_REQUEST);
const getScheduleAvailabilitySuccess = createAction(GET_SCHEDULE_AVAILABILITY_SUCCESS, 'data');
const getScheduleAvailabilityFailure = createAction(GET_SCHEDULE_AVAILABILITY_FAILURE, 'error');

export function getScheduleAvailability({
  programId,
  institutionCalendarId,
  courseId,
  term,
  level,
  sessionId,
  scheduleId,
  startDate,
  endDate,
  mode,
  isReassign = false,
  schedule,
  callback,
}) {
  return function (dispatch) {
    dispatch(getScheduleAvailabilityRequest());
    axios
      .get(
        `/course_schedule/available_list/${programId}/${institutionCalendarId}/${courseId}/${term}/${level}/${sessionId}/${startDate}/${endDate}`,
        { params: { ...(scheduleId && { scheduleId }) } }
      )
      .then((res) => {
        const response = res.data.data;
        dispatch(getScheduleAvailabilitySuccess(response));
        if (mode === 'update') {
          callback && callback(fromJS(response), 'update', schedule, undefined, false, isReassign);
        }
      })
      .catch((error) => {
        dispatch(getScheduleAvailabilityFailure(error));
      });
  };
}

export const GET_MERGE_SCHEDULE_REQUEST = 'GET_MERGE_SCHEDULE_REQUEST';
export const GET_MERGE_SCHEDULE_SUCCESS = 'GET_MERGE_SCHEDULE_SUCCESS';
export const GET_MERGE_SCHEDULE_FAILURE = 'GET_MERGE_SCHEDULE_FAILURE';

const getMergeScheduleRequest = createAction(GET_MERGE_SCHEDULE_REQUEST);
const getMergeScheduleSuccess = createAction(GET_MERGE_SCHEDULE_SUCCESS, 'data');
const getMergeScheduleFailure = createAction(GET_MERGE_SCHEDULE_FAILURE, 'error');

export function getMergeSchedule({
  programId,
  institutionCalendarId,
  courseId,
  term,
  level,
  sessionIdList,
  scheduleIdList,
  callback,
}) {
  return function (dispatch) {
    dispatch(getMergeScheduleRequest());
    axios
      .get(
        `/course_schedule/merge_schedule/${programId}/${institutionCalendarId}/${courseId}/${term}/${level}`,
        { params: { session_ids: sessionIdList, schedule_ids: scheduleIdList } }
      )
      .then((res) => {
        const response = res.data.data;
        dispatch(getMergeScheduleSuccess(response));
        const sessionList = response.session_list || [];
        callback && callback(fromJS(sessionList));
      })
      .catch((error) => {
        dispatch(getMergeScheduleFailure(error));
      });
  };
}

export const SAVE_MERGE_SCHEDULE_REQUEST = 'SAVE_MERGE_SCHEDULE_REQUEST';
export const SAVE_MERGE_SCHEDULE_SUCCESS = 'SAVE_MERGE_SCHEDULE_SUCCESS';
export const SAVE_MERGE_SCHEDULE_FAILURE = 'SAVE_MERGE_SCHEDULE_FAILURE';

const saveMergeScheduleRequest = createAction(SAVE_MERGE_SCHEDULE_REQUEST);
const saveMergeScheduleSuccess = createAction(SAVE_MERGE_SCHEDULE_SUCCESS, 'data', 'operation');
const saveMergeScheduleFailure = createAction(SAVE_MERGE_SCHEDULE_FAILURE, 'error');

export function saveMergeSchedule({
  mode,
  _id,
  requestBody,
  callback,
  openScheduleModal,
  errCallback,
  _institution_calendar_id,
  _program_id,
  _course_id,
  term,
  level_no,
  page = 1,
  _session_id = '',
}) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveMergeScheduleRequest());
      const method = {
        create: 'put',
        update: 'put',
        delete: 'put',
      };
      axios[method[mode]](
        `/course_schedule/${
          mode === 'delete'
            ? 'detach_session'
            : mode === 'create'
            ? 'merge_session'
            : 'merge_session_edit'
        }${mode === 'delete' ? '/' + _id : ''}`,
        requestBody
      )
        .then((res) => {
          const operation = {
            create: 'Merged',
            update: 'Edited',
            delete: 'Detached',
          };
          callback && callback();
          dispatch(saveMergeScheduleSuccess(res.data.data, operation[mode]));
          dispatch(setData(Map({ courseSchedule: Map() })));
          dispatch(
            getCourseSchedule({
              institutionCalendarId: _institution_calendar_id,
              programId: _program_id,
              courseId: _course_id,
              term,
              level: level_no,
              page,
              sessionFlowId: _session_id,
              type: 'regular',
              ...(mode === 'delete' && {
                callback: (sessionFlow) => {
                  if (mode === 'delete') {
                    openScheduleModal(_session_id, sessionFlow);
                  }
                },
              }),
            })
          );
        })
        .catch((error) => {
          const { response: { status, data: { data = [], message = '' } = {} } = {} } = error;
          if (status === 409) {
            errCallback && errCallback(fromJS(data));
          } else {
            const errorMessage =
              message && typeof message === 'string'
                ? message
                : 'An error occurred. Please try again.';
            dispatch(setData(Map({ message: errorMessage })));
          }
          dispatch(saveMergeScheduleFailure(error));
        });
    };
  }
}

export const GET_TIME_TABLE_DATA_REQUEST = 'GET_TIME_TABLE_DATA_REQUEST';
export const GET_TIME_TABLE_DATA_SUCCESS = 'GET_TIME_TABLE_DATA_SUCCESS';
export const GET_TIME_TABLE_DATA_FAILURE = 'GET_TIME_TABLE_DATA_FAILURE';

const getTimeTableDataRequest = createAction(GET_TIME_TABLE_DATA_REQUEST);
const getTimeTableDataSuccess = createAction(GET_TIME_TABLE_DATA_SUCCESS, 'data');
const getTimeTableDataFailure = createAction(GET_TIME_TABLE_DATA_FAILURE, 'error');

export function getTimeTableData({
  type,
  programId,
  institutionCalendarId,
  courseId,
  term,
  level,
  startDate,
  endDate,
}) {
  return function (dispatch) {
    dispatch(getTimeTableDataRequest());
    let url = '/course_schedule';
    if (type === 'course') {
      url =
        url +
        `/course_time_table/${programId}/${institutionCalendarId}/${courseId}/${term}/${level}/${startDate}/${endDate}`;
    } else {
      url =
        url +
        `/level_time_table/${programId}/${institutionCalendarId}/${term}/${level}/${startDate}/${endDate}`;
    }
    axios
      .get(url)
      .then((res) => dispatch(getTimeTableDataSuccess(res.data.data)))
      .catch((error) => dispatch(getTimeTableDataFailure(error)));
  };
}

export const GET_COURSE_SCHEDULE_EXPORT_REQUEST = 'GET_COURSE_SCHEDULE_EXPORT_REQUEST';
export const GET_COURSE_SCHEDULE_EXPORT_SUCCESS = 'GET_COURSE_SCHEDULE_EXPORT_SUCCESS';
export const GET_COURSE_SCHEDULE_EXPORT_FAILURE = 'GET_COURSE_SCHEDULE_EXPORT_FAILURE';

const getCourseScheduleExportRequest = createAction(GET_COURSE_SCHEDULE_EXPORT_REQUEST);
const getCourseScheduleExportSuccess = createAction(GET_COURSE_SCHEDULE_EXPORT_SUCCESS, 'data');
const getCourseScheduleExportFailure = createAction(GET_COURSE_SCHEDULE_EXPORT_FAILURE, 'error');

export function getCourseScheduleExport({
  institutionCalendarId,
  courseId,
  studentGroupId,
  exportType,
  groupId,
  type,
  term,
  callback,
}) {
  return function (dispatch) {
    dispatch(getCourseScheduleExportRequest());
    axios
      .get(
        `/course_schedule/course_schedule_export/${exportType}/${
          type === 'regular' ? studentGroupId : courseId
        }/${courseId}/${institutionCalendarId}`,
        {
          params: { groupId, type, term },
        }
      )
      .then((res) => {
        let response = res.data.data;
        if (typeof response !== 'object') response = {};
        if (Object.keys(response).length) {
          callback && callback(fromJS(response));
        }
        dispatch(getCourseScheduleExportSuccess(response));
      })
      .catch((error) => dispatch(getCourseScheduleExportFailure(error)));
  };
}

export const PUBLISH_COURSE_COORDINATOR_REQUEST = 'PUBLISH_COURSE_COORDINATOR_REQUEST';
export const PUBLISH_COURSE_COORDINATOR_SUCCESS = 'PUBLISH_COURSE_COORDINATOR_SUCCESS';
export const PUBLISH_COURSE_COORDINATOR_FAILURE = 'PUBLISH_COURSE_COORDINATOR_FAILURE';

const publishCourseCoordinatorRequest = createAction(PUBLISH_COURSE_COORDINATOR_REQUEST);
const publishCourseCoordinatorSuccess = createAction(PUBLISH_COURSE_COORDINATOR_SUCCESS, 'data');
const publishCourseCoordinatorFailure = createAction(PUBLISH_COURSE_COORDINATOR_FAILURE, 'error');

export function publishCourseCoordinator(requestBody) {
  return function (dispatch) {
    dispatch(publishCourseCoordinatorRequest());
    axios
      .post('/course_coordinator/publish', requestBody)
      .then((res) => dispatch(publishCourseCoordinatorSuccess(res.data.data)))
      .catch((error) => dispatch(publishCourseCoordinatorFailure(error)));
  };
}

export const PUBLISH_COURSE_SCHEDULE_REQUEST = 'PUBLISH_COURSE_SCHEDULE_REQUEST';
export const PUBLISH_COURSE_SCHEDULE_SUCCESS = 'PUBLISH_COURSE_SCHEDULE_SUCCESS';
export const PUBLISH_COURSE_SCHEDULE_FAILURE = 'PUBLISH_COURSE_SCHEDULE_FAILURE';

const publishCourseScheduleRequest = createAction(PUBLISH_COURSE_SCHEDULE_REQUEST);
const publishCourseScheduleSuccess = createAction(PUBLISH_COURSE_SCHEDULE_SUCCESS, 'data');
const publishCourseScheduleFailure = createAction(PUBLISH_COURSE_SCHEDULE_FAILURE, 'error');

export function publishCourseSchedule(requestBody) {
  return function (dispatch) {
    dispatch(publishCourseScheduleRequest());
    axios
      .post('/course_schedule/publish', requestBody)
      .then((res) => dispatch(publishCourseScheduleSuccess(res.data.data)))
      .catch((error) => dispatch(publishCourseScheduleFailure(error)));
  };
}

export const GET_INDIVIDUAL_COURSE_REQUEST = 'GET_INDIVIDUAL_COURSE_REQUEST';
export const GET_INDIVIDUAL_COURSE_SUCCESS = 'GET_INDIVIDUAL_COURSE_SUCCESS';
export const GET_INDIVIDUAL_COURSE_FAILURE = 'GET_INDIVIDUAL_COURSE_FAILURE';

const getIndividualCourseRequest = createAction(GET_INDIVIDUAL_COURSE_REQUEST);
const getIndividualCourseSuccess = createAction(GET_INDIVIDUAL_COURSE_SUCCESS, 'data');
const getIndividualCourseFailure = createAction(GET_INDIVIDUAL_COURSE_FAILURE, 'error');

export function getIndividualCourse(programId, instCalId, courseId, term, levelNo) {
  return function (dispatch) {
    dispatch(getIndividualCourseRequest());
    axios
      .get(
        `/course_schedule/manage_course/${programId}/${instCalId}/${courseId}/${term}/${levelNo}`
      )
      .then((res) => {
        dispatch(
          getIndividualCourseSuccess({
            course_details: res.data.data?.course_details,
            session_delivery: res.data.data?.session_delivery,
          })
        );
      })
      .catch((error) => dispatch(getIndividualCourseFailure(error)));
  };
}

export const GET_MANAGE_COURSE_SETTINGS_REQUEST = 'GET_MANAGE_COURSE_SETTINGS_REQUEST';
export const GET_MANAGE_COURSE_SETTINGS_SUCCESS = 'GET_MANAGE_COURSE_SETTINGS_SUCCESS';
export const GET_MANAGE_COURSE_SETTINGS_FAILURE = 'GET_MANAGE_COURSE_SETTINGS_FAILURE';

const getManageCourseSettingsRequest = createAction(GET_MANAGE_COURSE_SETTINGS_REQUEST);
const getManageCourseSettingsSuccess = createAction(GET_MANAGE_COURSE_SETTINGS_SUCCESS, 'data');
const getManageCourseSettingsFailure = createAction(GET_MANAGE_COURSE_SETTINGS_FAILURE, 'error');

export function getManageCourseSettings(
  programId,
  instCalId,
  courseId,
  term,
  levelNo,
  deliveryId,
  id = null
) {
  return function (dispatch) {
    dispatch(getManageCourseSettingsRequest());
    axios
      .get(
        `/course_schedule/manage_course_delivery/${programId}/${instCalId}/${courseId}/${term}/${levelNo}/${deliveryId}${
          id !== null ? `?id=${id}` : ``
        }`
      )
      .then((res) => {
        dispatch(getManageCourseSettingsSuccess(res.data.data));
      })
      .catch((error) => dispatch(getManageCourseSettingsFailure(error)));
  };
}

export const SAVE_MANAGE_COURSE_SCHEDULE_REQUEST = 'SAVE_MANAGE_COURSE_SCHEDULE_REQUEST';
export const SAVE_MANAGE_COURSE_SCHEDULE_SUCCESS = 'SAVE_MANAGE_COURSE_SCHEDULE_SUCCESS';
export const SAVE_MANAGE_COURSE_SCHEDULE_FAILURE = 'SAVE_MANAGE_COURSE_SCHEDULE_FAILURE';

const saveManageCourseScheduleRequest = createAction(SAVE_MANAGE_COURSE_SCHEDULE_REQUEST);
const saveManageCourseScheduleSuccess = createAction(
  SAVE_MANAGE_COURSE_SCHEDULE_SUCCESS,
  'data',
  'operation'
);
const saveManageCourseScheduleFailure = createAction(SAVE_MANAGE_COURSE_SCHEDULE_FAILURE, 'error');

export function saveManageCourseSchedule({ mode, _id, requestBody, callback }) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveManageCourseScheduleRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](
        `/course_schedule/lecture_setting${
          mode === 'delete' ? '_delete' : mode === 'update' ? '_update' : ''
        }${mode !== 'create' ? '/' + _id : ''}`,
        mode !== 'delete' ? requestBody : {}
      )
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
          };
          callback && callback();
          dispatch(saveManageCourseScheduleSuccess(res.data.data, operation[mode]));
          const { _institution_calendar_id, _program_id, _course_id, term, level_no } = requestBody;
          dispatch(
            getIndividualCourse(_program_id, _institution_calendar_id, _course_id, term, level_no)
          );
        })
        .catch((error) => {
          dispatch(saveManageCourseScheduleFailure(error));
        });
    };
  }
}

export const SAVE_ADVANCE_SETTING_REQUEST = 'SAVE_ADVANCE_SETTING_REQUEST';
export const SAVE_ADVANCE_SETTING_SUCCESS = 'SAVE_ADVANCE_SETTING_SUCCESS';
export const SAVE_ADVANCE_SETTING_FAILURE = 'SAVE_ADVANCE_SETTING_FAILURE';

const saveAdvanceSettingRequest = createAction(SAVE_ADVANCE_SETTING_REQUEST);
const saveAdvanceSettingSuccess = createAction(SAVE_ADVANCE_SETTING_SUCCESS, 'data');
const saveAdvanceSettingFailure = createAction(SAVE_ADVANCE_SETTING_FAILURE, 'error');

export function saveAdvanceSetting(requestBody, programId, courseId, level, from, callback) {
  return function (dispatch) {
    dispatch(saveAdvanceSettingRequest());
    axios
      .put(
        `/course_schedule/update_advanced_setting_manage_course/${programId}/${courseId}/${level}`,
        requestBody
      )
      .then((res) => {
        let message = 'Advance Settings Updated Successfully';
        if (requestBody.break_session_flow !== undefined) {
          message = `Break Session Flow ${
            requestBody.break_session_flow ? 'Enabled' : 'Disabled'
          } Successfully`;
        } else if (requestBody.auto_assign_session_default_settings !== undefined) {
          message = '';
        }

        dispatch(saveAdvanceSettingSuccess(message));
        if (from === 'manage-course') {
          dispatch(setData(Map({ advancedSettings: Map() })));
        }
        dispatch(
          getAdvancedSettings(
            programId,
            courseId,
            level,
            from === 'schedule' ? callback : undefined
          )
        );
      })
      .catch((error) => dispatch(saveAdvanceSettingFailure(error)));
  };
}

export const GET_ADVANCED_SETTINGS_REQUEST = 'GET_ADVANCED_SETTINGS_REQUEST';
export const GET_ADVANCED_SETTINGS_SUCCESS = 'GET_ADVANCED_SETTINGS_SUCCESS';
export const GET_ADVANCED_SETTINGS_FAILURE = 'GET_ADVANCED_SETTINGS_FAILURE';

const getAdvancedSettingsRequest = createAction(GET_ADVANCED_SETTINGS_REQUEST);
const getAdvancedSettingsSuccess = createAction(GET_ADVANCED_SETTINGS_SUCCESS, 'data');
const getAdvancedSettingsFailure = createAction(GET_ADVANCED_SETTINGS_FAILURE, 'error');

export function getAdvancedSettings(programId, courseId, level, callback) {
  return function (dispatch) {
    dispatch(getAdvancedSettingsRequest());
    axios
      .get(`/course_schedule/get_advanced_setting_manage_course/${programId}/${courseId}/${level}`)
      .then((res) => {
        dispatch(getAdvancedSettingsSuccess(res.data.data));
        if (callback !== undefined) {
          callback(true);
        }
      })
      .catch((error) => dispatch(getAdvancedSettingsFailure(error)));
  };
}

export const GET_MANAGE_TOPICS_REQUEST = 'GET_MANAGE_TOPICS_REQUEST';
export const GET_MANAGE_TOPICS_SUCCESS = 'GET_MANAGE_TOPICS_SUCCESS';
export const GET_MANAGE_TOPICS_FAILURE = 'GET_MANAGE_TOPICS_FAILURE';

const getManageTopicsRequest = createAction(GET_MANAGE_TOPICS_REQUEST);
const getManageTopicsSuccess = createAction(GET_MANAGE_TOPICS_SUCCESS, 'data');
const getManageTopicsFailure = createAction(GET_MANAGE_TOPICS_FAILURE, 'error');

export function getManageTopics(programId, courseId, level, institutionCalendarId) {
  return function (dispatch) {
    dispatch(getManageTopicsRequest());
    axios
      .get(
        `/course_schedule/manage_topic/${courseId}/${level}/${programId}/${institutionCalendarId}`
      )
      .then((res) => dispatch(getManageTopicsSuccess(res.data.data)))
      .catch((error) => dispatch(getManageTopicsFailure(error)));
  };
}

export const SAVE_MANAGE_TOPICS_REQUEST = 'SAVE_MANAGE_TOPICS_REQUEST';
export const SAVE_MANAGE_TOPICS_SUCCESS = 'SAVE_MANAGE_TOPICS_SUCCESS';
export const SAVE_MANAGE_TOPICS_FAILURE = 'SAVE_MANAGE_TOPICS_FAILURE';

const saveManageTopicsRequest = createAction(SAVE_MANAGE_TOPICS_REQUEST);
const saveManageTopicsSuccess = createAction(SAVE_MANAGE_TOPICS_SUCCESS, 'data', 'operation');
const saveManageTopicsFailure = createAction(SAVE_MANAGE_TOPICS_FAILURE, 'error');

export function saveManageTopics({ mode, _id, requestBody, callback, alteredBody }) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveManageTopicsRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](
        `/course_schedule/manage_topic${
          mode !== 'create'
            ? `/${_id}/${requestBody.course_id}/${requestBody.level_no}/${requestBody.program_id}`
            : ''
        }`,
        mode !== 'delete' && mode !== 'update' ? requestBody : mode === 'update' ? alteredBody : {}
      )
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
          };
          callback && callback();
          dispatch(saveManageTopicsSuccess(res.data.data, operation[mode]));
          const { program_id, course_id, level_no, _institution_calendar_id } = requestBody;
          dispatch(getManageTopics(program_id, course_id, level_no, _institution_calendar_id));
        })
        .catch((error) => {
          dispatch(saveManageTopicsFailure(error));
        });
    };
  }
}

export const SAVE_MANAGE_COURSE_DAY_TIME_REQUEST = 'SAVE_MANAGE_COURSE_DAY_TIME_REQUEST';
export const SAVE_MANAGE_COURSE_DAY_TIME_SUCCESS = 'SAVE_MANAGE_COURSE_DAY_TIME_SUCCESS';
export const SAVE_MANAGE_COURSE_DAY_TIME_FAILURE = 'SAVE_MANAGE_COURSE_DAY_TIME_FAILURE';

const saveManageCourseDayTimeSetRequest = createAction(SAVE_MANAGE_COURSE_DAY_TIME_REQUEST);
const saveManageCourseDayTimeSetSuccess = createAction(
  SAVE_MANAGE_COURSE_DAY_TIME_SUCCESS,
  'data',
  'operation'
);
const saveManageCourseDayTimeSetFailure = createAction(
  SAVE_MANAGE_COURSE_DAY_TIME_FAILURE,
  'error'
);

export function saveManageCourseDayTimeSet({
  mode,
  id,
  requestBody,
  occurrenceId,
  callback,
  alteredBody,
}) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveManageCourseDayTimeSetRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](
        `/course_schedule/manage_course/${
          mode === 'create' ? 'assign_day_time' : mode === 'update' ? 'update' : 'remove'
        }/${id}${mode !== 'create' ? '/' + occurrenceId : ''}`,
        requestBody
      )
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
          };
          callback && callback();
          dispatch(saveManageCourseDayTimeSetSuccess(res.data.data, operation[mode]));
          const { programId, institutionCalendarId, courseId, term, level } = alteredBody;
          dispatch(getIndividualCourse(programId, institutionCalendarId, courseId, term, level));
        })
        .catch((error) => {
          dispatch(saveManageCourseDayTimeSetFailure(error));
        });
    };
  }
}

export const GET_AUTO_ASSIGN_COURSE_DELIVERY_REQUEST = 'GET_AUTO_ASSIGN_COURSE_DELIVERY_REQUEST';
export const GET_AUTO_ASSIGN_COURSE_DELIVERY_SUCCESS = 'GET_AUTO_ASSIGN_COURSE_DELIVERY_SUCCESS';
export const GET_AUTO_ASSIGN_COURSE_DELIVERY_FAILURE = 'GET_AUTO_ASSIGN_COURSE_DELIVERY_FAILURE';

const getAutoAssignCourseDeliveryRequest = createAction(GET_AUTO_ASSIGN_COURSE_DELIVERY_REQUEST);
const getAutoAssignCourseDeliverySuccess = createAction(
  GET_AUTO_ASSIGN_COURSE_DELIVERY_SUCCESS,
  'data'
);
const getAutoAssignCourseDeliveryFailure = createAction(
  GET_AUTO_ASSIGN_COURSE_DELIVERY_FAILURE,
  'error'
);

export function getAutoAssignCourseDelivery(
  programId,
  institutionCalendarId,
  courseId,
  term,
  level,
  deliveryId,
  page,
  pageSize,
  isRefresh,
  rotationCount
) {
  return function (dispatch) {
    dispatch(getAutoAssignCourseDeliveryRequest());
    axios
      .get(
        `/course_schedule/auto_assign_course_delivery/${programId}/${institutionCalendarId}/${courseId}/${term}/${level}/${deliveryId}`,
        {
          params: {
            ...(rotationCount !== '' && { rotation_count: rotationCount }),
            //     limit: pageSize,
            //     pageNo: page,
          },
        }
      )
      .then((res) =>
        dispatch(
          getAutoAssignCourseDeliverySuccess({
            data: res.data.data,
            totalPages: res.data.totalPages,
            currentPage: res.data.currentPage,
            totalDoc: res.data.totalDoc,
            isRefresh: isRefresh,
          })
        )
      )
      .catch((error) => dispatch(getAutoAssignCourseDeliveryFailure(error)));
  };
}

export const SAVE_ASSIGN_COURSE_DELIVERY_REQUEST = 'SAVE_ASSIGN_COURSE_DELIVERY_REQUEST';
export const SAVE_ASSIGN_COURSE_DELIVERY_SUCCESS = 'SAVE_ASSIGN_COURSE_DELIVERY_SUCCESS';
export const SAVE_ASSIGN_COURSE_DELIVERY_FAILURE = 'SAVE_ASSIGN_COURSE_DELIVERY_FAILURE';

const saveAssignCourseDeliveryRequest = createAction(SAVE_ASSIGN_COURSE_DELIVERY_REQUEST);
const saveAssignCourseDeliverySuccess = createAction(
  SAVE_ASSIGN_COURSE_DELIVERY_SUCCESS,
  'data',
  'operation'
);
const saveAssignCourseDeliveryFailure = createAction(SAVE_ASSIGN_COURSE_DELIVERY_FAILURE, 'error');

export function saveAssignCourseDelivery({ mode, requestBody, callback }) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveAssignCourseDeliveryRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[mode]](`/course_schedule/auto_schedule`, mode !== 'update' ? requestBody : {})
        .then((res) => {
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
          };
          callback && callback();
          dispatch(saveAssignCourseDeliverySuccess(res.data.data, operation[mode]));
        })
        .catch((error) => {
          dispatch(saveAssignCourseDeliveryFailure(error));
        });
    };
  }
}

export const GET_STUDENT_GROUPS_WITH_STUDENTS_REQUEST = 'GET_STUDENT_GROUPS_WITH_STUDENTS_REQUEST';
export const GET_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS = 'GET_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS';
export const GET_STUDENT_GROUPS_WITH_STUDENTS_FAILURE = 'GET_STUDENT_GROUPS_WITH_STUDENTS_FAILURE';

const getStudentGroupsWithStudentsRequest = createAction(GET_STUDENT_GROUPS_WITH_STUDENTS_REQUEST);
const getStudentGroupsWithStudentsSuccess = createAction(
  GET_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS,
  'data'
);
const getStudentGroupsWithStudentsFailure = createAction(
  GET_STUDENT_GROUPS_WITH_STUDENTS_FAILURE,
  'error'
);

export function getStudentGroupsWithStudents({
  programId,
  institutionCalendarId,
  courseId,
  term,
  level_no,
}) {
  return function (dispatch) {
    dispatch(getStudentGroupsWithStudentsRequest());
    axios
      .get(
        `/course_schedule/get_student_group/${institutionCalendarId}/${programId}/${courseId}/${term}?level_no=${level_no}`
      )
      .then((res) => {
        dispatch(getStudentGroupsWithStudentsSuccess(res.data.data));
      })
      .catch((error) => {
        dispatch(getStudentGroupsWithStudentsFailure(error));
      });
  };
}

export const SAVE_STUDENT_GROUPS_WITH_STUDENTS_REQUEST =
  'SAVE_STUDENT_GROUPS_WITH_STUDENTS_REQUEST';
export const SAVE_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS =
  'SAVE_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS';
export const SAVE_STUDENT_GROUPS_WITH_STUDENTS_FAILURE =
  'SAVE_STUDENT_GROUPS_WITH_STUDENTS_FAILURE';

const saveStudentGroupsWithStudentsRequest = createAction(
  SAVE_STUDENT_GROUPS_WITH_STUDENTS_REQUEST
);
const saveStudentGroupsWithStudentsSuccess = createAction(
  SAVE_STUDENT_GROUPS_WITH_STUDENTS_SUCCESS,
  'data'
);
const saveStudentGroupsWithStudentsFailure = createAction(
  SAVE_STUDENT_GROUPS_WITH_STUDENTS_FAILURE,
  'error'
);

export function saveStudentGroupsWithStudents({ type, mode, _id, requestBody, callback }) {
  if (['create', 'update'].includes(mode)) {
    return function (dispatch) {
      dispatch(saveStudentGroupsWithStudentsRequest());
      const method = {
        create: 'post',
        update: 'put',
      };
      const institutionCalendarId = requestBody._institution_calendar_id;
      if (mode === 'update') {
        delete requestBody._institution_calendar_id;
      }
      axios[method[mode]](
        `/course_schedule/${type}${mode === 'create' ? '' : '/' + _id}`,
        requestBody
      )
        .then((res) => {
          dispatch(saveStudentGroupsWithStudentsSuccess(res.data.data));
          dispatch(
            setData(
              Map({
                message: `${type === 'event' ? 'Event' : 'Support Session'} ${
                  mode === 'create' ? 'Created' : 'Edited'
                } Successfully`,
              })
            )
          );
          callback && callback();
          dispatch(setData(Map({ courseSchedule: Map() })));
          const { _program_id, _course_id, term, level_no } = requestBody;
          dispatch(
            getCourseSchedule({
              institutionCalendarId,
              programId: _program_id,
              courseId: _course_id,
              term,
              level: level_no,
              type,
            })
          );
        })
        .catch((error) => {
          dispatch(saveStudentGroupsWithStudentsFailure(error));
        });
    };
  }
}

export const GET_PROGRAM_LIST_REQUEST = 'GET_PROGRAM_LIST_REQUEST';
export const GET_PROGRAM_LIST_SUCCESS = 'GET_PROGRAM_LIST_SUCCESS';
export const GET_PROGRAM_LIST_FAILURE = 'GET_PROGRAM_LIST_FAILURE';

const getProgramListRequest = createAction(GET_PROGRAM_LIST_REQUEST);
const getProgramListSuccess = createAction(GET_PROGRAM_LIST_SUCCESS, 'data');
const getProgramListFailure = createAction(GET_PROGRAM_LIST_FAILURE, 'error');

export function getProgramList({ userId, roleId }) {
  return function (dispatch) {
    dispatch(getProgramListRequest(true));
    axios
      .get(`/course_coordinator/program_list/${userId}/${roleId}`)
      .then((res) => dispatch(getProgramListSuccess(res.data.data)))
      .catch((error) => dispatch(getProgramListFailure(error)));
  };
}

export const GET_STAFF_OPTION_LIST_REQUEST = 'GET_STAFF_OPTION_LIST_REQUEST';
export const GET_STAFF_OPTION_LIST_SUCCESS = 'GET_STAFF_OPTION_LIST_SUCCESS';
export const GET_STAFF_OPTION_LIST_FAILURE = 'GET_STAFF_OPTION_LIST_FAILURE';

const getStaffOptionListRequest = createAction(GET_STAFF_OPTION_LIST_REQUEST);
const getStaffOptionListSuccess = createAction(GET_STAFF_OPTION_LIST_SUCCESS, 'data');
const getStaffOptionListFailure = createAction(GET_STAFF_OPTION_LIST_FAILURE, 'error');

export function getStaffOptionList() {
  return function (dispatch) {
    dispatch(getStaffOptionListRequest(true));
    axios
      .get(`/course_schedule/listManualAttendanceStaff`)
      .then((res) => {
        dispatch(getStaffOptionListSuccess(res.data.data));
      })
      .catch((error) => dispatch(getStaffOptionListFailure(error)));
  };
}

export const SET_STAFF_LIST_REQUEST = 'SET_STAFF_LIST_REQUEST';
export const SET_STAFF_LIST_SUCCESS = 'SET_STAFF_LIST_SUCCESS';
export const SET_STAFF_LIST_FAILURE = 'SET_STAFF_LIST_FAILURE';

const setStaffListRequest = createAction(SET_STAFF_LIST_REQUEST);
const setStaffListSuccess = createAction(SET_STAFF_LIST_SUCCESS, 'data');
const setStaffListFailure = createAction(SET_STAFF_LIST_FAILURE, 'error');

export function setStaffList(payLoad, courseId, programId, level, callBack) {
  return function (dispatch) {
    dispatch(setStaffListRequest(true));
    axios
      .put(`/course_schedule/updateManualAttendance/${programId}/${courseId}/${level}`, payLoad)
      .then((res) => {
        dispatch(setStaffListSuccess(res.data));
        callBack && callBack();
        dispatch(getAdvancedSettings(programId, courseId, level));
      })
      .catch((error) => dispatch(setStaffListFailure(error)));
  };
}

export const GET_SCHEDULE_STAFF_OPTION_LIST_REQUEST = 'GET_SCHEDULE_STAFF_OPTION_LIST_REQUEST';
export const GET_SCHEDULE_STAFF_OPTION_LIST_SUCCESS = 'GET_SCHEDULE_STAFF_OPTION_LIST_SUCCESS';
export const GET_SCHEDULE_STAFF_OPTION_LIST_FAILURE = 'GET_SCHEDULE_STAFF_OPTION_LIST_FAILURE';

const getScheduleStaffOptionListRequest = createAction(GET_SCHEDULE_STAFF_OPTION_LIST_REQUEST);
const getScheduleStaffOptionListSuccess = createAction(
  GET_SCHEDULE_STAFF_OPTION_LIST_SUCCESS,
  'data'
);
const getScheduleStaffOptionListFailure = createAction(
  GET_SCHEDULE_STAFF_OPTION_LIST_FAILURE,
  'error'
);

export function getScheduleStaffOptionList(programId, courseId, level) {
  return function (dispatch) {
    dispatch(getScheduleStaffOptionListRequest(true));
    axios
      .get(`/course_schedule/listManualAttendanceDetails/${programId}/${courseId}/${level}`)
      .then((res) => {
        dispatch(getScheduleStaffOptionListSuccess(res.data.data));
      })
      .catch((error) => dispatch(getScheduleStaffOptionListFailure(error)));
  };
}

export const SAVE_MISSED_SESSION_REQUEST = 'SAVE_MISSED_SESSION_REQUEST';
export const SAVE_MISSED_SESSION_SUCCESS = 'SAVE_MISSED_SESSION_SUCCESS';
export const SAVE_MISSED_SESSION_FAILURE = 'SAVE_MISSED_SESSION_FAILURE';

const saveMissedSessionRequest = createAction(SAVE_MISSED_SESSION_REQUEST);
const saveMissedSessionSuccess = createAction(SAVE_MISSED_SESSION_SUCCESS, 'data');
const saveMissedSessionFailure = createAction(SAVE_MISSED_SESSION_FAILURE, 'error');

export function saveMissedSession(requestBody, programId, courseId, level, from) {
  return function (dispatch) {
    dispatch(saveMissedSessionRequest());
    axios
      .put(
        `/course_schedule/update_advanced_setting_missed_schedule/${programId}/${courseId}/${level}`,
        requestBody
      )
      .then((res) => {
        dispatch(saveMissedSessionSuccess());
      })
      .catch((error) => dispatch(saveMissedSessionFailure(error)));
  };
}

export const UPDATE_MISSED_SESSION_REQUEST = 'UPDATE_MISSED_SESSION_REQUEST';
export const UPDATE_MISSED_SESSION_SUCCESS = 'UPDATE_MISSED_SESSION_SUCCESS';
export const UPDATE_MISSED_SESSION_FAILURE = 'UPDATE_MISSED_SESSION_FAILURE';

const updateMissedSessionRequest = createAction(UPDATE_MISSED_SESSION_REQUEST);
const updateMissedSessionSuccess = createAction(UPDATE_MISSED_SESSION_SUCCESS, 'data');
const updateMissedSessionFailure = createAction(UPDATE_MISSED_SESSION_FAILURE, 'error');

export function updateMissedSession(requestBody, callBack) {
  return function (dispatch) {
    dispatch(updateMissedSessionRequest());
    axios
      .put(`/sessions/session_missedToComplete`, requestBody)
      .then((res) => {
        dispatch(updateMissedSessionSuccess());
        callBack && callBack();
      })
      .catch((error) => dispatch(updateMissedSessionFailure(error)));
  };
}

export const SYNC_SCHEDULE_MODAL_DATA_REQUEST = 'SYNC_SCHEDULE_MODAL_DATA_REQUEST';
export const SYNC_SCHEDULE_MODAL_DATA_SUCCESS = 'SYNC_SCHEDULE_MODAL_DATA_SUCCESS';
export const SYNC_SCHEDULE_MODAL_DATA_FAILURE = 'SYNC_SCHEDULE_MODAL_DATA_FAILURE';

const syncModalDataRequest = createAction(SYNC_SCHEDULE_MODAL_DATA_REQUEST, 'isLoading');
const syncModalDataSuccess = createAction(SYNC_SCHEDULE_MODAL_DATA_SUCCESS, 'data');
const syncModalDataFailure = createAction(SYNC_SCHEDULE_MODAL_DATA_FAILURE, 'error');

export const syncModalData = (data, callBack) => {
  return (dispatch) => {
    dispatch(syncModalDataRequest(true));
    return axios
      .post(`/sis/programInput/courseScheduleSync`, data)
      .then((res) => {
        dispatch(syncModalDataSuccess(res.data.data));
        callBack && callBack();
      })
      .catch((errors) => dispatch(syncModalDataFailure(errors)));
  };
};

export const POST_Add_EXTERNAL_Staff_REQUEST = 'POST_Add_EXTERNAL_Staff_REQUEST';
export const POST_Add_EXTERNAL_Staff_SUCCESS = 'POST_Add_EXTERNAL_Staff_SUCCESS';
export const POST_Add_EXTERNAL_Staff_FAILURE = 'POST_Add_EXTERNAL_Staff_FAILURE';

const postExternalStaffRequest = createAction(POST_Add_EXTERNAL_Staff_REQUEST);
const postExternalStaffSuccess = createAction(
  POST_Add_EXTERNAL_Staff_SUCCESS,
  'data',
  'operation',
  'index',
  'requestBody',
  'resultsId'
);
const postExternalStaffFailure = createAction(POST_Add_EXTERNAL_Staff_FAILURE, 'error');

export function addExternalStaff({
  mode,
  requestBody = {},
  callBack = () => {},
  institutionCalendarId = '',
  resultsId,
}) {
  if (['create', 'update', 'delete'].includes(mode)) {
    return function (dispatch) {
      dispatch(postExternalStaffRequest());
      const method = {
        create: 'post',
        update: 'put',
        delete: 'put',
      };
      axios[method[mode]](
        `/externalStaff/${mode === 'create' ? `addExternalStaff` : ''}${
          mode === 'update' ? `editExternalStaff` : ''
        }${mode === 'delete' ? `deleteStaff` : ''}${mode === 'delete' ? `/${resultsId}` : ''}`,
        mode !== 'delete' ? requestBody : {}
      )
        .then((res) => {
          const resultId = res.data.data._id;
          const operation = {
            create: 'Created',
            update: 'Edited',
            delete: 'Deleted',
          };
          callBack && callBack(resultId);
          dispatch(postExternalStaffSuccess(res.data.data, operation[mode]));
        })
        .catch((error) => {
          dispatch(postExternalStaffFailure(error));
        });
    };
  }
}

export const GET_EXTERNAL_STAFF_REQUEST = 'GET_EXTERNAL_STAFF_REQUEST';
export const GET_EXTERNAL_STAFF_SUCCESS = 'GET_EXTERNAL_STAFF_SUCCESS';
export const GET_EXTERNAL_STAFF_FAILURE = 'GET_EXTERNAL_STAFF_FAILURE';

const getExternalStaffRequest = createAction(GET_EXTERNAL_STAFF_REQUEST);
const getExternalStaffSuccess = createAction(GET_EXTERNAL_STAFF_SUCCESS, 'data');
const getExternalStaffFailure = createAction(GET_EXTERNAL_STAFF_FAILURE, 'error');

export function getExternalStaff() {
  return function (dispatch) {
    dispatch(getExternalStaffRequest(true));
    axios
      .get(`/externalStaff/getExternalStaff`)
      .then((res) => {
        dispatch(getExternalStaffSuccess(res.data.data));
      })
      .catch((error) => dispatch(getExternalStaffFailure(error)));
  };
}
export const GET_SCHEDULED_EXTERNAL_STAFF_REQUEST = 'GET_SCHEDULED_EXTERNAL_STAFF_REQUEST';
export const GET_SCHEDULED_EXTERNAL_STAFF_SUCCESS = 'GET_SCHEDULED_EXTERNAL_STAFF_SUCCESS';
export const GET_SCHEDULED_EXTERNAL_STAFF_FAILURE = 'GET_SCHEDULED_EXTERNAL_STAFF_SUCCESS';

const getscheduledExternalStaffRequest = createAction(GET_SCHEDULED_EXTERNAL_STAFF_REQUEST);
const getscheduledExternalStaffSuccess = createAction(GET_SCHEDULED_EXTERNAL_STAFF_SUCCESS, 'data');
const getscheduledExternalStaffFailure = createAction(
  GET_SCHEDULED_EXTERNAL_STAFF_FAILURE,
  'error'
);

export function scheduledExternalStaff(scheduleId) {
  return function (dispatch) {
    dispatch(getscheduledExternalStaffRequest(true));
    axios
      .get(`/course_schedule/scheduledExternalStaff/${scheduleId}`)
      .then((res) => {
        dispatch(getscheduledExternalStaffSuccess(res.data.data));
      })
      .catch((error) => dispatch(getscheduledExternalStaffFailure(error)));
  };
}

export const GET_MERGE_SCHEDULED_CLASSLEADER_REQUEST = 'GET_MERGE_SCHEDULED_CLASSLEADER_REQUEST';
export const GET_MERGE_SCHEDULED_CLASSLEADER_SUCCESS = 'GET_MERGE_SCHEDULED_CLASSLEADER_SUCCESS';
export const GET_MERGE_SCHEDULED_CLASSLEADER_FAILURE = 'GET_MERGE_SCHEDULED_CLASSLEADER_SUCCESS';

const getMergeScheduledClassLeaderRequest = createAction(GET_MERGE_SCHEDULED_CLASSLEADER_REQUEST);
const getMergeScheduledClassLeaderSuccess = createAction(
  GET_MERGE_SCHEDULED_CLASSLEADER_SUCCESS,
  'data'
);
const getMergeScheduledClassLeaderFailure = createAction(
  GET_MERGE_SCHEDULED_CLASSLEADER_FAILURE,
  'error'
);

export function mergeScheduledClassLeader({ scheduleIdList }) {
  return function (dispatch) {
    dispatch(getMergeScheduledClassLeaderRequest(true));
    axios
      .get(`/course_schedule/mergeScheduledClassLeader?`, {
        params: { schedule_ids: scheduleIdList },
      })
      .then((res) => {
        dispatch(getMergeScheduledClassLeaderSuccess(res.data.data));
      })
      .catch((error) => dispatch(getMergeScheduledClassLeaderFailure(error)));
  };
}

export const POST_CLASSLEADER_REQUEST = 'POST_CLASSLEADER_REQUEST';
export const POST_CLASSLEADER_SUCCESS = 'POST_CLASSLEADER_SUCCESS';
export const POST_CLASSLEADER_FAILURE = 'POST_CLASSLEADER_FAILURE';

const postClassLeaderRequest = createAction(POST_CLASSLEADER_REQUEST);
const postClassLeaderSuccess = createAction(POST_CLASSLEADER_SUCCESS, 'data');
const postClassLeaderFailure = createAction(POST_CLASSLEADER_FAILURE, 'error');
export function postClassLeader(requestBodyClassLeader) {
  return function (dispatch) {
    dispatch(postClassLeaderRequest());
    axios
      .post('/course_schedule/classLeader', requestBodyClassLeader)
      .then((res) => dispatch(postClassLeaderSuccess(res.data.data)))
      .catch((error) => dispatch(postClassLeaderFailure(error)));
  };
}

export const GET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST =
  'GET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST';
export const GET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS =
  'GET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS';
export const GET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE =
  'GET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE';

const getCourseSessionManagementRequest = createAction(
  GET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST
);
const getCourseSessionManagementSuccess = createAction(
  GET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS,
  'data'
);
const getCourseSessionManagementFailure = createAction(
  GET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE,
  'error'
);

export function getCourseSessionManagement(url) {
  return function (dispatch) {
    dispatch(getCourseSessionManagementRequest(true));
    axios
      .get(url)
      .then((res) => {
        dispatch(getCourseSessionManagementSuccess(res.data.data));
      })
      .catch((error) => dispatch(getCourseSessionManagementFailure(error)));
  };
}
export const SET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST =
  'SET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST';
export const SET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS =
  'SET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS';
export const SET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE =
  'SET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE';

const setCourseSessionManagementRequest = createAction(
  SET_COURSE_SESSION_STATUS_MANAGEMENT_REQUEST
);
const setCourseSessionManagementSuccess = createAction(
  SET_COURSE_SESSION_STATUS_MANAGEMENT_SUCCESS,
  'data'
);
const setCourseSessionManagementFailure = createAction(
  SET_COURSE_SESSION_STATUS_MANAGEMENT_FAILURE,
  'error'
);

export function setCourseSessionManagement(_id, payload, cb) {
  return function (dispatch) {
    dispatch(setCourseSessionManagementRequest(true));
    axios
      .put('/session-status-management/updateCourseSessionStatusDetail/' + _id, payload)
      .then((res) => {
        dispatch(setCourseSessionManagementSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(setCourseSessionManagementFailure(error)));
  };
}

export const GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_REQUEST =
  'GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_REQUEST';
export const GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_SUCCESS =
  'GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_SUCCESS';
export const GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_FAILURE =
  'GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_FAILURE';

const getStudentGroupBasedDeliveryRequest = createAction(
  GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_REQUEST
);
const getStudentGroupBasedDeliverySuccess = createAction(
  GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_SUCCESS,
  'data'
);
const getStudentGroupBasedDeliveryFailure = createAction(
  GET_STUDENT_GROUP_BASED_DELIVERY_TYPES_FAILURE,
  'error'
);

export function getStudentGroupBasedDeliveryTypes({
  _program_id,
  _institution_calendar_id,
  _course_id,
  term,
  level_no,
  callBack,
}) {
  return function (dispatch) {
    dispatch(getStudentGroupBasedDeliveryRequest(true));
    axios
      .get(
        `course_schedule/getStudentGroupBasedDeliveryTypes?_institution_calendar_id=${_institution_calendar_id}&_program_id=${_program_id}&_course_id=${_course_id}&term=${term}&level_no=${level_no}`
      )
      .then((res) => {
        dispatch(getStudentGroupBasedDeliverySuccess(res.data.data));
        callBack && callBack(res.data.data);
      })
      .catch((error) => dispatch(getStudentGroupBasedDeliveryFailure(error)));
  };
}

export const GET_LEVEL_WISE_COURSES_REQUEST = 'GET_LEVEL_WISE_COURSES_REQUEST';
export const GET_LEVEL_WISE_COURSES_SUCCESS = 'GET_LEVEL_WISE_COURSES_SUCCESS';
export const GET_LEVEL_WISE_COURSES_FAILURE = 'GET_LEVEL_WISE_COURSES_FAILURE';

const getLevelWiseCoursesRequest = createAction(GET_LEVEL_WISE_COURSES_REQUEST);
const getLevelWiseCoursesSuccess = createAction(GET_LEVEL_WISE_COURSES_SUCCESS, 'data');
const getLevelWiseCoursesFailure = createAction(GET_LEVEL_WISE_COURSES_FAILURE, 'error');

export function getLevelWiseCourses(params) {
  return function (dispatch) {
    dispatch(getLevelWiseCoursesRequest());
    axios
      .get('courseScheduleV2/courseList', { params })
      .then((res) => dispatch(getLevelWiseCoursesSuccess(res.data.data)))
      .catch((error) => dispatch(getLevelWiseCoursesFailure(error)));
  };
}

export const GET_COURSE_TOPICS_REQUEST = 'GET_COURSE_TOPICS_REQUEST';
export const GET_COURSE_TOPICS_SUCCESS = 'GET_COURSE_TOPICS_SUCCESS';
export const GET_COURSE_TOPICS_FAILURE = 'GET_COURSE_TOPICS_FAILURE';

const getCourseTopicsRequest = createAction(GET_COURSE_TOPICS_REQUEST);
const getCourseTopicsSuccess = createAction(GET_COURSE_TOPICS_SUCCESS, 'data');
const getCourseTopicsFailure = createAction(GET_COURSE_TOPICS_FAILURE, 'error');

export function getCourseTopics(params) {
  return function (dispatch) {
    dispatch(getCourseTopicsRequest());
    axios
      .get('courseScheduleV2/courseTopics', { params })
      .then((res) => dispatch(getCourseTopicsSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseTopicsFailure(error)));
  };
}

export const GET_AVAILABLE_LIST_REQUEST = 'GET_AVAILABLE_LIST_REQUEST';
export const GET_AVAILABLE_LIST_SUCCESS = 'GET_AVAILABLE_LIST_SUCCESS';
export const GET_AVAILABLE_LIST_FAILURE = 'GET_AVAILABLE_LIST_FAILURE';

const getAvailableListRequest = createAction(GET_AVAILABLE_LIST_REQUEST);
const getAvailableListSuccess = createAction(GET_AVAILABLE_LIST_SUCCESS, 'data');
const getAvailableListFailure = createAction(GET_AVAILABLE_LIST_FAILURE, 'error');

export function getAvailableList({ params, isMultipleSession }) {
  return function (dispatch) {
    dispatch(getAvailableListRequest());
    const url = isMultipleSession
      ? 'courseScheduleV2/courseSchedulingDetails'
      : 'courseScheduleV2/availableList';
    axios
      .get(url, { params })
      .then((res) => dispatch(getAvailableListSuccess(res.data.data)))
      .catch((error) => dispatch(getAvailableListFailure(error)));
  };
}

export const GET_COURSE_DELIVERY_TYPES_REQUEST = 'GET_COURSE_DELIVERY_TYPES_REQUEST';
export const GET_COURSE_DELIVERY_TYPES_SUCCESS = 'GET_COURSE_DELIVERY_TYPES_SUCCESS';
export const GET_COURSE_DELIVERY_TYPES_FAILURE = 'GET_COURSE_DELIVERY_TYPES_FAILURE';

const getCourseDeliveryTypesRequest = createAction(GET_COURSE_DELIVERY_TYPES_REQUEST);
const getCourseDeliveryTypesSuccess = createAction(GET_COURSE_DELIVERY_TYPES_SUCCESS, 'data');
const getCourseDeliveryTypesFailure = createAction(GET_COURSE_DELIVERY_TYPES_FAILURE, 'error');

export function getCourseDeliveryTypes(params) {
  return function (dispatch) {
    dispatch(getCourseDeliveryTypesRequest());
    axios
      .get('courseScheduleV2/courseDeliverySymbol', { params })
      .then((res) => dispatch(getCourseDeliveryTypesSuccess(res.data.data)))
      .catch((error) => dispatch(getCourseDeliveryTypesFailure(error)));
  };
}

export const POST_SCHEDULE_ANALYZE_REQUEST = 'POST_SCHEDULE_ANALYZE_REQUEST';
export const POST_SCHEDULE_ANALYZE_SUCCESS = 'POST_SCHEDULE_ANALYZE_SUCCESS';
export const POST_SCHEDULE_ANALYZE_FAILURE = 'POST_SCHEDULE_ANALYZE_FAILURE';

const postScheduleAnalyzeRequest = createAction(POST_SCHEDULE_ANALYZE_REQUEST);
const postScheduleAnalyzeSuccess = createAction(POST_SCHEDULE_ANALYZE_SUCCESS, 'data');
const postScheduleAnalyzeFailure = createAction(POST_SCHEDULE_ANALYZE_FAILURE, 'error');

export function postScheduleAnalyze({ requestBody, callback }) {
  return function (dispatch) {
    dispatch(postScheduleAnalyzeRequest());
    axios
      .post('courseScheduleV2/multiScheduleAnalysis', requestBody)
      .then((res) => {
        dispatch(postScheduleAnalyzeSuccess(res.data.data));
        callback();
      })
      .catch((error) => dispatch(postScheduleAnalyzeFailure(error)));
  };
}

export const POST_MULTI_SCHEDULE_REQUEST = 'POST_MULTI_SCHEDULE_REQUEST';
export const POST_MULTI_SCHEDULE_SUCCESS = 'POST_MULTI_SCHEDULE_SUCCESS';
export const POST_MULTI_SCHEDULE_FAILURE = 'POST_MULTI_SCHEDULE_FAILURE';

const postMultiScheduleRequest = createAction(POST_MULTI_SCHEDULE_REQUEST);
const postMultiScheduleSuccess = createAction(POST_MULTI_SCHEDULE_SUCCESS, 'data');
const postMultiScheduleFailure = createAction(POST_MULTI_SCHEDULE_FAILURE, 'error');

export function postMultiSchedule({ requestBody, callback }) {
  return function (dispatch) {
    dispatch(postMultiScheduleRequest());
    axios
      .post('courseScheduleV2/multiSchedule', requestBody)
      .then(() => {
        const scheduleCount = requestBody.get('scheduleIds', List()).size;
        dispatch(postMultiScheduleSuccess(scheduleCount));
        callback();
      })
      .catch((error) => dispatch(postMultiScheduleFailure(error)));
  };
}

export const GET_EVENT_LIST_REQUEST = 'GET_EVENT_LIST_REQUEST';
export const GET_EVENT_LIST_SUCCESS = 'GET_EVENT_LIST_SUCCESS';
export const GET_EVENT_LIST_FAILURE = 'GET_EVENT_LIST_FAILURE';

const getEventListRequest = createAction(GET_EVENT_LIST_REQUEST);
const getEventListSuccess = createAction(GET_EVENT_LIST_SUCCESS, 'data');
const getEventListFailure = createAction(GET_EVENT_LIST_FAILURE, 'error');

export function getEventList(params) {
  return function (dispatch) {
    dispatch(getEventListRequest());
    axios
      .get('courseScheduleV2/eventList', { params })
      .then((res) => {
        const { withSchedule } = params;
        dispatch(getEventListSuccess({ data: res.data.data, withSchedule }));
      })
      .catch((error) => dispatch(getEventListFailure(error)));
  };
}

export const POST_SINGLE_SCHEDULE_REQUEST = 'POST_SINGLE_SCHEDULE_REQUEST';
export const POST_SINGLE_SCHEDULE_SUCCESS = 'POST_SINGLE_SCHEDULE_SUCCESS';
export const POST_SINGLE_SCHEDULE_FAILURE = 'POST_SINGLE_SCHEDULE_FAILURE';

const postSingleScheduleRequest = createAction(POST_SINGLE_SCHEDULE_REQUEST);
const postSingleScheduleSuccess = createAction(POST_SINGLE_SCHEDULE_SUCCESS, 'data');
const postSingleScheduleFailure = createAction(POST_SINGLE_SCHEDULE_FAILURE, 'error');

export function postSingleSchedule({ requestBody, topicName, callback }) {
  return function (dispatch) {
    dispatch(postSingleScheduleRequest());
    axios
      .post('courseScheduleV2/createSingleCourseSchedule', requestBody)
      .then(() => {
        dispatch(postSingleScheduleSuccess(topicName));
        callback();
      })
      .catch((error) => dispatch(postSingleScheduleFailure(error)));
  };
}

export const DELETE_SCHEDULE_REQUEST = 'DELETE_SCHEDULE_REQUEST';
export const DELETE_SCHEDULE_SUCCESS = 'DELETE_SCHEDULE_SUCCESS';
export const DELETE_SCHEDULE_FAILURE = 'DELETE_SCHEDULE_FAILURE';

const deleteScheduleRequest = createAction(DELETE_SCHEDULE_REQUEST);
const deleteScheduleSuccess = createAction(DELETE_SCHEDULE_SUCCESS, 'data');
const deleteScheduleFailure = createAction(DELETE_SCHEDULE_FAILURE, 'error');

export function deleteSchedule({ scheduleId, topicName, callback }) {
  return function (dispatch) {
    dispatch(deleteScheduleRequest());
    axios
      .delete('courseScheduleV2/deleteSchedule', { params: { scheduleId } })
      .then(() => {
        dispatch(deleteScheduleSuccess(topicName));
        callback();
      })
      .catch((error) => dispatch(deleteScheduleFailure(error)));
  };
}

export const EDIT_SCHEDULE_REQUEST = 'EDIT_SCHEDULE_REQUEST';
export const EDIT_SCHEDULE_SUCCESS = 'EDIT_SCHEDULE_SUCCESS';
export const EDIT_SCHEDULE_FAILURE = 'EDIT_SCHEDULE_FAILURE';

const editScheduleRequest = createAction(EDIT_SCHEDULE_REQUEST);
const editScheduleSuccess = createAction(EDIT_SCHEDULE_SUCCESS, 'data');
const editScheduleFailure = createAction(EDIT_SCHEDULE_FAILURE, 'error');

export function editSchedule({ requestBody, topicName, callback }) {
  return function (dispatch) {
    dispatch(editScheduleRequest());
    axios
      .put('courseScheduleV2/editSingleCourseSchedule', requestBody)
      .then(() => {
        dispatch(editScheduleSuccess(topicName));
        callback();
      })
      .catch((error) => dispatch(editScheduleFailure(error)));
  };
}
