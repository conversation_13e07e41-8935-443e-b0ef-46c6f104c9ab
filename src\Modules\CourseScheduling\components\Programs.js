import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import Input from '../../../Widgets/FormElements/Input/Input';
import * as actions from '../../../_reduxapi/course_scheduling/action';
import { selectProgramList } from '../../../_reduxapi/course_scheduling/selectors';
import {
  selectSelectedRole,
  selectUserId,
  selectActiveInstitutionCalendar,
} from '../../../_reduxapi/Common/Selectors';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { eString, getLang, indVerRename } from '../../../utils';
import { t } from 'i18next';

const lang = getLang();

class Programs extends Component {
  constructor() {
    super();
    this.state = {
      programType: [
        {
          name: t('all'),
          value: 'all',
        },
        {
          name: t('undergraduate'),
          value: 'undergraduate',
        },
        {
          name: t('postgraduate'),
          value: 'postgraduate',
        },
        {
          name: t('pre_requisite'),
          value: 'pre-requisite',
        },
      ],
      selectedProgramType: 'all',
      searchText: '',
    };
  }

  componentDidMount() {
    const { userId, selectedRole } = this.props;
    if (userId && selectedRole.getIn(['_role_id', '_id'])) {
      this.fetchPrograms();
    }
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.activeInstitutionCalendar.get('_id') !==
      prevProps.activeInstitutionCalendar.get('_id')
    ) {
      this.fetchPrograms();
    }
  }

  fetchPrograms() {
    const { userId, selectedRole } = this.props;
    this.props.getProgramList({ userId, roleId: selectedRole.getIn(['_role_id', '_id']) });
  }

  handleChange = (e, name) => {
    this.setState({
      [name]: e.target.value,
    });
  };

  formatCurriculum = (curriculum) => {
    if (curriculum && curriculum.size > 0) {
      const name = curriculum.toJS().map((item) => item.curriculum_name);
      return name.join(', ');
    }
    return '';
  };

  urlRedirect = (id, name) => {
    const { history, setData } = this.props;
    const componentName = this.getComponentName();

    let componentArray = [];
    if (
      CheckPermission(
        'tabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        'Course View'
      )
    ) {
      componentArray.push('schedule');
    }
    if (
      CheckPermission(
        'tabs',
        'Schedule Management',
        'Assign Course Coordinator',
        '',
        'Course List',
        'View'
      )
    ) {
      componentArray.push('course-coordinators');
    }
    if (CheckPermission('tabs', 'Infrastructure Management', 'Remote', '', 'Level List', 'View')) {
      componentArray.push('remote');
    }
    if (
      CheckPermission(
        'tabs',
        'Schedule Management',
        'Extra Curricular and Break',
        '',
        'Extra Curricular',
        'View'
      ) ||
      CheckPermission(
        'tabs',
        'Schedule Management',
        'Extra Curricular and Break',
        '',
        'Break',
        'View'
      )
    ) {
      componentArray.push('extra-curricular-break');
    }
    if (!componentArray.includes(componentName)) {
      return;
    }
    setData(Map({ activeTab: 'Schedule' }));
    const query = `?programId=${eString(id)}&programName=${eString(name)}`;
    history.push(`/course-scheduling/${componentName}/list` + query);
  };

  programList = () => {
    const { programList } = this.props;
    const { searchText, selectedProgramType } = this.state;
    const lowerCasedFilter = searchText.toLowerCase();
    const filteredData =
      programList &&
      programList.size > 0 &&
      programList.filter((item) => {
        return (
          item.get('code', '').toLowerCase().includes(lowerCasedFilter) ||
          item.get('name', '').toLowerCase().includes(lowerCasedFilter)
        );
      });
    // .filter((item) => {
    //   if (
    //     CheckPermission('pages', 'Program Input', 'Programs', 'Add Program') &&
    //     CheckPermission('pages', 'Program Input', 'Programs', 'Add Pre-requisite')
    //   ) {
    //     return item;
    //   } else if (CheckPermission('pages', 'Program Input', 'Programs', 'Add Program')) {
    //     return item.get('program_type') === 'program';
    //   } else if (CheckPermission('pages', 'Program Input', 'Programs', 'Add Pre-requisite')) {
    //     return item.get('program_type') === 'pre-requisite';
    //   } else {
    //     return CheckProgramDepartment(item.get('_id')) === true;
    //   }
    // });

    return (
      <div className="pt-2">
        <div className={`course_master ${lang === 'ar' ? 'text-right' : ''}`}>
          <div className="go-wrapper border-bottom">
            <table className="table">
              <thead className="th-change">
                <tr>
                  <th>
                    <div className="aw-75">{t('s_no')}</div>
                  </th>

                  <th>
                    <div className="aw-150">{t('code')}</div>
                  </th>

                  <th>
                    <div className="aw-200">{t('program_names')}</div>
                  </th>

                  <th>
                    <div className="aw-150">{t('type')}</div>
                  </th>
                  <th>
                    <div className="aw-150">{t('degree_name')}</div>
                  </th>
                  <th>
                    <div className="aw-150">{t('terms', { name: indVerRename('term') })}</div>
                  </th>
                  <th>
                    <div className="aw-150">{t('active_curriculum')}</div>
                  </th>
                </tr>
              </thead>
              <tbody className="go-wrapper-height">
                {filteredData.size === 0 && (
                  <tr>
                    <td colSpan="7">{t('infra_management.no_data')}...</td>
                  </tr>
                )}
                {filteredData.size > 0 &&
                  filteredData
                    .filter((item) => {
                      return (
                        item.get('code', '').toLowerCase().includes(lowerCasedFilter) ||
                        item.get('name', '').toLowerCase().includes(lowerCasedFilter)
                      );
                    })
                    .filter((item) => {
                      return selectedProgramType === 'all'
                        ? item
                        : selectedProgramType === 'pre-requisite'
                        ? item.get('program_type') === selectedProgramType
                        : item.get('type') === selectedProgramType;
                    })
                    .map((list, i) => {
                      const termSize = list.get('term', List()).size;
                      const termLabel = indVerRename('Term', list?.get('_id', ''));
                      const termName =
                        termSize + (termSize > 1 ? ` ${termLabel}s` : ` ${termLabel}`);
                      return (
                        <tr
                          key={i}
                          className="tr-change remove_hover"
                          onClick={() => this.urlRedirect(list.get('_id'), list.get('name'))}
                        >
                          <td className="mt-20">
                            <div className="aw-75">{i + 1}</div>
                          </td>
                          <td className="mt-20">
                            <div className="aw-150">{list.get('code', '')}</div>
                          </td>
                          <td className="mt-20">
                            <div className="aw-200">{list.get('name', '')}</div>
                          </td>
                          <td className="mt-20">
                            <div className="aw-150">
                              {list.get('program_type', '') === 'program'
                                ? list.get('type', '') === 'undergraduate'
                                  ? t('undergraduate')
                                  : t('postgraduate')
                                : t('pre_requisite')}
                            </div>
                          </td>
                          <td className="mt-20">
                            <div className="aw-150">{list.get('degree', 'NA')}</div>
                          </td>
                          <td className="mt-20">
                            <div className="aw-150">
                              {termName.replace(
                                'Term',
                                t('term', {
                                  name: 'Term',
                                })
                              )}
                            </div>
                          </td>
                          <td className="mt-20">
                            <div className="aw-150">
                              {this.formatCurriculum(list.get('curriculum', List()))}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  getComponentName() {
    const {
      match: {
        params: { type },
      },
    } = this.props;
    return type;
  }

  render() {
    const { searchText, programType, selectedProgramType } = this.state;
    return (
      <React.Fragment>
        <div className="main pb-5 bg-gray ">
          <div className="container">
            <div className="p-3">
              <div className="col-md-12">
                <div className="mt-3">
                  <p className={`f-16 font-weight-bold mb-1 ${lang === 'ar' ? 'text-left' : ''}`}>
                    {t('tabs.programs')}
                  </p>
                  <div className="p-3 bg-white rounded">
                    <p className={`f-16 font-weight-bold mb-2 ${lang === 'ar' ? 'text-left' : ''}`}>
                      {t('all_programs')}
                    </p>
                    <div className="d-flex justify-content-between mb-2">
                      <div className="col-md-8 pl-0">
                        <div className="row">
                          <div className="col-md-4">
                            <div className="mt--11">
                              <div className="search-list">
                                <Input
                                  elementType={'floatinginput'}
                                  value={searchText}
                                  floatingLabel={t('constant.search_code_name')}
                                  changed={(e) => this.handleChange(e, 'searchText')}
                                />
                                <i
                                  className="fa fa-search searchButton-list"
                                  style={{ top: '30px' }}
                                ></i>
                              </div>
                            </div>
                          </div>

                          <div className="col-md-4">
                            <div className="mt--15">
                              <Input
                                elementType={'floatingselect'}
                                elementConfig={{
                                  options: programType,
                                }}
                                value={selectedProgramType}
                                floatingLabel={t('program_type')}
                                changed={(e) => this.handleChange(e, 'selectedProgramType')}
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="col-md-4 mt-1 pr-0"></div>
                    </div>
                  </div>
                  {this.programList()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

Programs.propTypes = {
  history: PropTypes.object,
  match: PropTypes.object,
  userId: PropTypes.string,
  programList: PropTypes.instanceOf(List),
  getProgramList: PropTypes.func,
  setData: PropTypes.func,
  selectedRole: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  return {
    programList: selectProgramList(state),
    userId: selectUserId(state),
    selectedRole: selectSelectedRole(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, actions)(Programs);
