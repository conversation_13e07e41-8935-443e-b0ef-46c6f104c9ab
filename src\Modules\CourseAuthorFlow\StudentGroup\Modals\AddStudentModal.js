import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import { fromJS, Map } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { useDispatch, useSelector } from 'react-redux';
import { addStudent, getStudentDetails, setData } from '_reduxapi/studentGroupV2/action';
import { getUserFullName, useCallGroupSettings, useCallProgramYearLevel } from '../Dashboard';
import { selectStudentDetails } from '_reduxapi/studentGroupV2/selectors';
import { t } from 'i18next';
import { getModalSubtitle, NumberInput, ValidationError } from './DeliveryGroupsModal';
import PersonAddAltIcon from '@mui/icons-material/PersonAddAlt1';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { getEnvSgMaxMark } from 'utils';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};
const addIconSx = {
  padding: '8px',
  backgroundColor: '#EFF9FB',
  borderRadius: '50%',
  marginRight: '16px',
  color: '#0064C8',
};
const subTitleSx = {
  marginLeft: '8px',
  padding: '4px 8px',
  fontSize: '14px',
  borderRadius: '4px',
  backgroundColor: '#F3F4F6',
};
const circleIconSx = {
  marginRight: '8px',
  color: '#15803D',
};

const MIN_CGPA = 1;
const MAX_CGPA = getEnvSgMaxMark();
const validationMessage = {
  emptyId: 'Enter ID here!',
  invalidId: 'Student not found! Enter a valid ID',
  invalidMark: `Enter CGPA between ${MIN_CGPA} to ${MAX_CGPA}!`,
};

const AddStudentModal = ({ open, data, handleClose, refreshStudents }) => {
  const dispatch = useDispatch();
  const { studentDetails, error } = useSelector(selectStudentDetails);
  const { groupSettings, callGroupSettings } = useCallGroupSettings({ selectedData: data });
  const [fetchProgramYearLevel] = useCallProgramYearLevel({ filters: data });
  const [search, setSearch] = useState(Map());
  const [mark, setMark] = useState(0);
  const [inputErrors, setInputErrors] = useState(Map());
  const groupSettingId = groupSettings.get('_id', '');
  const academicId = search.get('value', '');

  const clearStudentDetails = () => dispatch(setData(fromJS({ singleStudentDetails: {} })));

  useEffect(() => {
    if (groupSettings.isEmpty()) callGroupSettings();
    return () => clearStudentDetails();
  }, []);

  useEffect(() => {
    if (!error) return clearError('academicId');

    const message =
      error === t('student_grouping.no_data') ? validationMessage['invalidId'] : error;
    setInputErrors((prev) => prev.set('academicId', message));
  }, [error]);

  useEffect(() => {
    clearError('academicId');
    if (!search.get('isEdited', false) || !academicId) return clearStudentDetails();

    const timeout = setTimeout(() => {
      dispatch(getStudentDetails({ groupSettingId, academicId }));
    }, 1000);

    return () => clearTimeout(timeout);
  }, [academicId]);

  const modalSubTile = useMemo(() => getModalSubtitle(data), []);

  const clearError = (key) => setInputErrors((prev) => prev.delete(key));

  const handleSearch = (value) => {
    setSearch(Map({ isEdited: true, value: value.trim() }));
  };

  const handleMark = (val) => {
    if (typeof val === 'number' && (val < MIN_CGPA || val > MAX_CGPA))
      return setInputErrors((prev) => prev.set('mark', validationMessage['invalidMark']));

    const regex = /^(\d+(\.\d{0,2})?)?$/;
    if (!regex.test(val)) return;

    setMark(val);
    clearError('mark');
  };

  const callback = () => {
    fetchProgramYearLevel();
    refreshStudents();
    handleClose();
  };

  const handleSave = () => {
    let errorMessage = inputErrors;
    if (!academicId) errorMessage = errorMessage.set('academicId', validationMessage['emptyId']);
    if (!mark || mark < MIN_CGPA || mark > MAX_CGPA)
      errorMessage = errorMessage.set('mark', validationMessage['invalidMark']);
    else if (errorMessage.has('mark')) errorMessage = errorMessage.delete('mark');

    if (errorMessage.size) return setInputErrors(errorMessage);

    const studentId = studentDetails.get('_id');
    const userId = studentDetails.get('user_id');
    const requestBody = { groupSettingId, academicId: userId, mark: mark.toString(), studentId };
    dispatch(addStudent({ requestBody, callback }));
  };

  return (
    <Dialog open={open} fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center">
          <Box display="flex" sx={addIconSx}>
            <PersonAddAltIcon />
          </Box>
          Add Student
          <Typography sx={subTitleSx}>{modalSubTile}</Typography>
        </Box>
      </DialogTitle>
      <DialogContent className="p-3">
        <Box display="flex" gap={1}>
          <Box flexGrow={1}>
            <p className="f-14 text-light-grey">Student Academic ID</p>
            <MaterialInput
              elementType="materialInput"
              type="text"
              size="small"
              placeholder="Enter an Academic ID"
              value={academicId}
              changed={(e) => handleSearch(e.target.value)}
              error={!!inputErrors.get('academicId')}
            />
            <ValidationError
              show={inputErrors.get('academicId')}
              message={inputErrors.get('academicId')}
            />

            {!studentDetails.isEmpty() && (
              <div className="d-flex align-items-center mt-2">
                <CheckCircleIcon sx={circleIconSx} />
                <div className="text-capitalize">
                  <p className="f-14 gray-neutral">{getUserFullName(studentDetails)}</p>
                  <p className="f-12 text-light-grey">
                    {studentDetails.get('user_id', '')}
                    <span className="mx-1">•</span>
                    {studentDetails.get('gender', '')}
                  </p>
                </div>
              </div>
            )}
          </Box>

          <Box width={110}>
            <p className="f-14 text-light-grey">CGPA (Out of {MAX_CGPA})</p>
            <NumberInput value={mark} changed={handleMark} error={!!inputErrors.get('mark')} />
            <ValidationError show={inputErrors.get('mark')} message={inputErrors.get('mark')} />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
          {t('student_grouping.cancel').toLowerCase()}
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleSave} sx={buttonSx}>
          {t('student_grouping.add').toLowerCase()}
        </MButton>
      </DialogActions>
    </Dialog>
  );
};
AddStudentModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
  refreshStudents: PropTypes.func,
};

export default AddStudentModal;
