import { setMinutes, setHours } from 'date-fns';
export const nonRotational = {
  modal: false,
  modal_mode: '',
  update_method: 'add',
  type: {
    start_date: '',
    end_date: '',
    model: '',
    _course_id: '',
    background_color: '#FFF4BD',
    events: [],
    rotation_count: 0,
  },
  disable_items: false,
  title: '',
  select_dates: '',
  events: [],
  triggerLists: true,
  academic_week_start: 0,
  academic_week_end: 0,
  academic_week_start_start_date: '',
  academic_week_start_end_date: '',
  academic_week_end_start_date: '',
  academic_week_end_end_date: '',
  academic_weeks: [],
  add_courses: {
    course: null,
  },
  copy_dates: [],
  custom_dates: '',
  deletedEvents: [],
  event_edit: false,
  course_add_mode: 'manual',
  course_events: [],
  batch_courses_id: [],
  modal_content: {
    description: '',
    event_type: 'exam',
    title: '',
    start_date: '',
    start_time: setHours(setMinutes(new Date(), 0), 8),
    end_date: '',
    end_time: setHours(setMinutes(new Date(), 0), 17),
  },
  year2: {},
  year3: {},
  year4: {},
  year5: {},
  year6: {},
};
