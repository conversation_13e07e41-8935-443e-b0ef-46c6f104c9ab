import React, { useContext } from 'react';
import { Trans } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import parentContext from '../Context/university-context';
import { List } from 'immutable';
import i18n from '../../../../i18n';

const axisStyle = {
  fontSize: '18px',
  fontFamily: 'roboto',
};
const flexStyle = {
  display: 'flex',
};
function AllCollegeGraph() {
  const details = useContext(parentContext.addCollegeContext);
  const { allCollegeData } = details;
  let options = {};
  if (allCollegeData.get('institutes', List()).size) {
    options = {
      xAxis: {
        type: 'category',
        style: flexStyle,
        data: allCollegeData
          .get('institutes', List())
          .map((item) => item.get('code', ''))
          .toJS(),
        name: `${i18n.t('role_management.modules_list.Colleges')}`,
        //<Trans i18nKey={'modules_list.Colleges'}></Trans>,
        nameGap: 30,
        nameLocation: 'center',
        nameTextStyle: axisStyle,
      },
      yAxis: {
        type: 'value',
        nameGap: 30,
        nameTextStyle: axisStyle,
        name: `${i18n.t('role_management.modules_list.Programs')}`,
        //<Trans i18nKey={'modules_list.Programs'}></Trans>
      },
      series: [
        {
          data: allCollegeData
            .get('institutes', List())
            .map((item) => item.get('programCount', 0))
            .toJS(),
          type: 'bar',
          barMaxWidth: 30,
          itemStyle: { color: '#3E95EF' },
          lineStyle: {
            color: '#3E95EF',
            barMaxWidth: 30,
          },
        },
      ],
    };
  }
  return (
    <>
      {allCollegeData.get('institutes', List()).size > 0 && (
        <div className="add-college">
          <div className="mt-3">
            <div className="p-3 bg-white rounded">
              <h5 className="d-flex font-weight-normal ml-0 mb-4">
                <Trans i18nKey={'add_colleges.no_of_colleges'}></Trans>{' '}
                {allCollegeData.get('childInstitutesCount', 0)}
              </h5>
              <ReactECharts
                style={{ width: '100%' }}
                option={options}
                className="echarts-height-400"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default AllCollegeGraph;
