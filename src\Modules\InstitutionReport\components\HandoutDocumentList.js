import React from 'react';
import { List } from 'immutable';
import PropTypes from 'prop-types';
import { Drawer, Grid, Card, IconButton, CardContent } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useSelector } from 'react-redux';
import { selectScheduleDocList } from '_reduxapi/session_tracking_report/selectors';
import { extensionColors } from '../utils';

const getAllAttachments = (data) => {
  const output = data.flatMap((item) =>
    item
      .get('attachments', List())
      .map((attachment) => item.delete('attachments').merge(attachment))
  );

  return data.size > 0 ? output : List();
};

function HandoutDocumentList(props) {
  const { docView, handleViewHandout } = props;

  const handoutDocumentList = useSelector(selectScheduleDocList);

  const allAttachments = getAllAttachments(handoutDocumentList);
  const recentDocumentList = allAttachments;

  return (
    <Drawer
      open={docView}
      onClose={() => handleViewHandout(false)}
      anchor="right"
      sx={{ '& .MuiDrawer-paper': { width: '75%' } }}
    >
      <div className="mt-4 f-24 ml-3 bold d-flex justify-content-between align-items-center">
        <span>Attachment File</span>
        <IconButton
          onClick={() => handleViewHandout(false)}
          sx={{ position: 'absolute', right: 8, top: 14 }}
        >
          <CloseIcon />
        </IconButton>
      </div>
      <div className="d-flex justify-content-md-center pt-2">
        <Grid container spacing={2} className="m-3">
          {recentDocumentList.map((document, index) => {
            return (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card className="shadow rounded">
                  <FileRenderer
                    fileUrl={document?.get('signedUrl', '')}
                    docName={document.get('documentName', '')}
                  />
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </div>
    </Drawer>
  );
}

HandoutDocumentList.propTypes = {
  docView: PropTypes.string,
  handleViewHandout: PropTypes.func,
};

export default HandoutDocumentList;

export const getFileExtension = (url) => {
  const cleanUrl = url.split('?')[0];
  return cleanUrl.split('.').pop().toLowerCase();
};

const FileRenderer = ({ fileUrl, docName }) => {
  const renderContent = () => {
    const ext = getFileExtension(fileUrl);
    const imageFormats = ['png', 'jpg', 'jpeg'];
    const videoFormats = ['mp4'];

    if (imageFormats.includes(ext)) {
      return (
        <Card>
          <img
            src={fileUrl}
            alt={docName}
            loading="lazy"
            style={{ height: '300px', width: '100%', objectFit: 'contain' }}
          />
          <CardContent>{docName}</CardContent>
        </Card>
      );
    } else if (videoFormats.includes(ext)) {
      return (
        <Card>
          <video
            src={fileUrl}
            controls
            style={{ height: '300px', width: '100%', objectFit: 'contain' }}
          />
          <CardContent>{docName}</CardContent>
        </Card>
      );
    } else {
      return (
        <Card>
          <DynamicSVG fileUrl={fileUrl} />
          <CardContent>{docName}</CardContent>
        </Card>
      );
    }
  };

  return <>{renderContent()}</>;
};

const DynamicSVG = ({ fileUrl }) => {
  const extension = getFileExtension(fileUrl);
  const textColor = extensionColors[extension] || extensionColors.default;

  return (
    <svg width="100%" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" rx="15" ry="15" fill="white" />

      <text
        x="50%"
        y="50%"
        fontSize="24"
        fontWeight="bold"
        fill="rgba(0, 0, 0, 0.3)"
        textAnchor="middle"
        dominantBaseline="middle"
        filter="url(#shadow)"
      >
        {extension.toUpperCase()}
      </text>

      <text
        x="50%"
        y="50%"
        fontSize="24"
        fontWeight="bold"
        fill={textColor}
        textAnchor="middle"
        dominantBaseline="middle"
      >
        {extension.toUpperCase()}
      </text>
    </svg>
  );
};
