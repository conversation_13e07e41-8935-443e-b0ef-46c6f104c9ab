import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { List, Map, Set } from 'immutable';
import Button from '@mui/material/Button';
import ThemeProvider from '@mui/styles/ThemeProvider';
import { t } from 'i18next';

import ProgramTabs from './ProgramTabs';
import CourseTabs from './CourseTabs';
import CourseTable from './CourseTable';
import StudentRegisterFilters from './Filters/StudentRegisterFilters';

import { CheckPermission } from '../../Shared/Permissions';
import {
  capitalize,
  eString,
  MUI_THEME,
  sortImmutableAlphaNumeric,
  isGenderMerge,
  isGenderMixed,
  getVersionName,
} from '../../../utils';
import * as actions from '../../../_reduxapi/leave_management/actions';
import {
  selectActiveInstitutionCalendar,
  selectSelectedRole,
  selectUserId,
  selectUserInfo,
} from '../../../_reduxapi/Common/Selectors';
import {
  selectProgramCourseList,
  selectAbsencePercentage,
  selectStudentRegisterList,
  selectCriteriaManipulationList,
  selectStudentAttnSheetDetails,
  selectAllStudentRegisteredData,
  selectStudentBlackBoxContent,
} from '../../../_reduxapi/leave_management/selectors';

const initialFilters = Map({
  program: '',
  term: '',
  year: '',
  level: '',
  courseId: '',
  courseName: '',
  courseNumber: '',
  isRotation: false,
  rotationCount: null,
  gender: 'male',
  programName: '',
});

function StudentRegister(props) {
  const {
    setBreadCrumbName,
    activeInstitutionCalendar,
    userId,
    selectedRole,
    programCourseList,
    getStudentRegisterProgramList,
    getStudentRegisterList,
    absencePercentage,
    studentRegisterList,
    getCriteriaManipulation,
    criteriaManipulationList,
    updateStudentAbsencePercentage,
    getStudentAttendanceSheetDetails,
    studentAttnSheetDetails,
    userInfo,
    updateReason,
    setData,
    studentRegisteredData,
    studentBlackBoxContent,
    getStudentBlackBoxContent,
  } = props;
  const history = useHistory();
  const [filters, setFilters] = useState(initialFilters);

  const activeInstitutionCalendarId = activeInstitutionCalendar.get('_id');
  const roleId = selectedRole.getIn(['_role_id', '_id']);
  const roleName = selectedRole.get('role_name', '');
  const selectedPrograms =
    selectedRole.get('program', List()).size > 0
      ? selectedRole
          .get('program', List())
          .map((item) => item.get('_program_id', ''))
          .toJS()
      : [];

  useEffect(() => {
    setBreadCrumbName(t('side_nav.menus.student_register'));
    if (activeInstitutionCalendarId && userId && roleId) {
      getStudentRegisterProgramList({
        institutionCalendarId: activeInstitutionCalendarId,
        userId,
        roleId,
        selectedPrograms,
        roleName,
      });
    }
    // eslint-disable-next-line
  }, [activeInstitutionCalendarId, userId, roleId]);

  const levelNo = filters.get('level');
  useEffect(() => {
    const programId = filters.get('program');
    const courseId = filters.get('courseId');
    let updatedFilters = filters;
    if (!programId) {
      const programs = getPrograms();
      updatedFilters = updatedFilters
        .set('program', programs.getIn([0, '_id'], ''))
        .set('programName', programs.getIn([0, 'name'], ''));
    }
    if (!courseId && levelNo) {
      const course = getCourseTabData().get(0, Map());
      updatedFilters = updatedFilters.merge(
        Map({
          courseId: course.get('courseId', ''),
          courseName: course.get('courseName', ''),
          courseNumber: course.get('courseNumber', ''),
          isRotation: course.get('isRotation', false),
          rotationCount: course.get('rotationCount', null),
          versionName: course.get('versionName', ''),
        })
      );
    }
    const updatedGender = isGenderMerge() ? 'both' : 'male';
    updatedFilters = updatedFilters.merge(Map({ gender: updatedGender }));
    setFilters(updatedFilters);
    // eslint-disable-next-line
  }, [programCourseList, levelNo]);

  function navigateToStudentLeaveEntry() {
    history.push(`/leave-management/Studentleave?id=${eString(filters.get('program', ''))}`);
  }

  function getPrograms() {
    return programCourseList.sort((a, b) => sortImmutableAlphaNumeric(a, b, 'name'));
  }

  function getActiveProgram() {
    const programs = getPrograms();
    const programId = filters.get('program', '');
    let program = Map();
    if (programId) {
      program = programs.find((p) => p.get('_id') === programId) || Map();
    }

    return program;
  }

  function getProgramTabData() {
    const programs = getPrograms();
    return programs.map((program) =>
      Map({ name: program.get('name', ''), value: program.get('_id') })
    );
  }

  function getCurrentLevel() {
    const activeProgram = getActiveProgram();
    return (
      activeProgram
        .get('level', List())
        .find(
          (level) =>
            level.get('term', '').toLowerCase() === filters.get('term', '').toLowerCase() &&
            level.get('year') === filters.get('year') &&
            level.get('level_no') === filters.get('level')
        ) || Map()
    );
  }

  function getCourses() {
    const term = filters.get('term', '');
    const year = filters.get('year', '');
    const level = filters.get('level', '');
    if (term && year && level) {
      const level = getCurrentLevel();
      if (level.get('rotation', '') === 'yes') {
        return level.get('rotation_course', List()).reduce((acc, rotation) => {
          return acc.concat(
            rotation
              .get('course', List())
              .map((course) =>
                course.merge(
                  Map({ isRotation: true, rotationCount: rotation.get('rotation_count') })
                )
              )
          );
        }, List());
      }
      return level.get('course', List());
    }
    return List();
  }

  function getCourseTabData() {
    const courses = getCourses();
    return courses.map((course) =>
      Map({
        courseId: course.getIn(['_course_id', '_id'], ''),
        courseNumber: course.get('courses_number', ''),
        courseName: course.get('courses_name', ''),
        isRotation: course.get('isRotation', false),
        rotationCount: course.get('rotationCount', null),
        versionName: getVersionName(course.get('_course_id', Map())),
      })
    );
  }

  function handleProgramChange(programId, programName) {
    if (filters.get('program') !== programId) {
      setFilters(initialFilters.set('program', programId).set('programName', programName));
    }
  }

  function handleCourseChange(
    courseId,
    courseName,
    courseNumber,
    isRotation,
    rotationCount,
    versionName
  ) {
    setFilters(
      filters.merge(
        Map({ courseId, courseName, courseNumber, isRotation, rotationCount, versionName })
      )
    );
  }

  function getTermOptions() {
    const activeProgram = getActiveProgram();
    const terms = activeProgram.get('term', List());
    return terms.map((term) =>
      Map({ name: term.get('term_name'), value: term.get('term_name', '') })
    );
  }

  function getYearOptions() {
    const activeProgram = getActiveProgram();
    const years = Set(
      activeProgram
        .get('level', List())
        .filter(
          (level) => level.get('term', '').toLowerCase() === filters.get('term', '').toLowerCase()
        )
        .map((level) => level.get('year', ''))
    ).toList();
    return years
      .map((year) => Map({ name: `${t('year')} ${year.split('year').join('')}`, value: year }))
      .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'name'));
  }

  function getLevelOptions() {
    const activeProgram = getActiveProgram();
    const levels = Set(
      activeProgram
        .get('level', List())
        .filter(
          (level) =>
            level.get('term', '').toLowerCase() === filters.get('term').toLowerCase() &&
            level.get('year') === filters.get('year')
        )
        .map((level) => level.get('level_no', ''))
    ).toList();
    return levels
      .map((level) => Map({ name: level, value: level }))
      .sort((a, b) => sortImmutableAlphaNumeric(a, b, 'name'));
  }

  function getGenderOptions() {
    const genderMerge = isGenderMerge();
    const genderMixed = isGenderMixed();
    const option = genderMerge
      ? ['both']
      : genderMixed
      ? ['male', 'female', 'both']
      : ['male', 'female'];
    return List(option).map((gender) => Map({ name: capitalize(gender), value: gender }));
  }

  function getFilterOptions() {
    return Map({
      terms: getTermOptions(),
      years: getYearOptions(),
      levels: getLevelOptions(),
      genders: getGenderOptions(),
    });
  }

  return (
    <>
      <ProgramTabs
        data={getProgramTabData()}
        activeProgramId={filters.get('program', '')}
        onTabClick={handleProgramChange}
      />
      <div className="p-4">
        <div className="d-flex justify-content-between align-items-center">
          <ThemeProvider theme={MUI_THEME}>
            <StudentRegisterFilters
              options={getFilterOptions()}
              filters={filters}
              handleChange={setFilters}
              handleApplyFilters={() => {}}
            />
            {CheckPermission(
              'tabs',
              'Leave Management',
              'Student Register',
              '',
              'Student Leave Entry',
              'View'
            ) && (
              <div>
                <Button variant="contained" color="primary" onClick={navigateToStudentLeaveEntry}>
                  {t('leaveManagement.studentLevelEntry')}
                </Button>
              </div>
            )}
          </ThemeProvider>
        </div>
        <div className="pt-5 pb-3">
          <CourseTabs data={getCourseTabData()} filters={filters} onTabClick={handleCourseChange} />
          {filters.get('courseId', '') !== '' ? (
            <CourseTable
              metaData={filters}
              activeInstitutionCalendar={activeInstitutionCalendar}
              getStudentRegisterList={getStudentRegisterList}
              absencePercentage={absencePercentage}
              studentRegisterList={studentRegisterList}
              getCriteriaManipulation={getCriteriaManipulation}
              criteriaManipulationList={criteriaManipulationList}
              updateStudentAbsencePercentage={updateStudentAbsencePercentage}
              getStudentAttendanceSheetDetails={getStudentAttendanceSheetDetails}
              userInfo={userInfo}
              studentAttnSheetDetails={studentAttnSheetDetails}
              updateReason={updateReason}
              setData={setData}
              studentBlackBoxContent={studentBlackBoxContent}
              getStudentBlackBoxContent={getStudentBlackBoxContent}
              studentRegisteredData={studentRegisteredData}
              activeProgram={getActiveProgram()}
            />
          ) : (
            <div className="placeholder-message course-placeholder-message">
              <h1>{t('leaveManagement.noDataFound')}</h1>
              <div>{t('leaveManagement.selectFilters')}</div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

StudentRegister.propTypes = {
  setBreadCrumbName: PropTypes.func,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  userId: PropTypes.string,
  selectedRole: PropTypes.instanceOf(Map),
  getStudentRegisterProgramList: PropTypes.func,
  programCourseList: PropTypes.instanceOf(List),
  getStudentRegisterList: PropTypes.func,
  getCriteriaManipulation: PropTypes.func,
  updateStudentAbsencePercentage: PropTypes.func,
  criteriaManipulationList: PropTypes.instanceOf(List),
  absencePercentage: PropTypes.number,
  studentRegisterList: PropTypes.instanceOf(List),
  getStudentAttendanceSheetDetails: PropTypes.func,
  getStudentBlackBoxContent: PropTypes.func,
  studentAttnSheetDetails: PropTypes.instanceOf(Map),
  userInfo: PropTypes.instanceOf(Map),
  updateReason: PropTypes.func,
  setData: PropTypes.func,
  studentRegisteredData: PropTypes.instanceOf(Map),
  studentBlackBoxContent: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    userId: selectUserId(state),
    selectedRole: selectSelectedRole(state),
    programCourseList: selectProgramCourseList(state),
    absencePercentage: selectAbsencePercentage(state),
    studentRegisterList: selectStudentRegisterList(state),
    criteriaManipulationList: selectCriteriaManipulationList(state),
    studentAttnSheetDetails: selectStudentAttnSheetDetails(state),
    userInfo: selectUserInfo(state),
    studentRegisteredData: selectAllStudentRegisteredData(state),
    studentBlackBoxContent: selectStudentBlackBoxContent(state),
  };
};

export default connect(mapStateToProps, actions)(StudentRegister);
