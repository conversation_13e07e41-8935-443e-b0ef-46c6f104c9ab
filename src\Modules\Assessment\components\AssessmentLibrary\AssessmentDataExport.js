import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { getURLParams, indVerRename } from 'utils';

const AssessmentDataExport = ({ setExportDownloadTrue, NeededDataForExport }) => {
  const { planName, totalMarks, programName, data, type, globalOutcomeList } = NeededDataForExport;
  let {
    noQuestions,
    curriculumName,
    levelNo,
    questionMarks,
    studentDetails,
    benchMark,
    questionOutcome,
  } = data;
  const programId = getURLParams('pid', true);
  const date = new Date();
  const current_date = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
  const outComeIds = questionMarks?.map((question) => question.outComeIds);
  const gettingQuestions = questionMarks
    ? questionMarks?.map((question) => question.questionName)
    : [];
  const checkingType =
    type === 'program'
      ? indVerRename('PLO', programId)
      : questionOutcome === 'CLO'
      ? indVerRename('CLO', programId)
      : indVerRename('SLO', programId);
  const emptyQnsArr = () => {
    let emptyMarks = [];
    for (let i = 1; i <= noQuestions; i++) emptyMarks.push('-');
    return emptyMarks;
  };
  const emptyQns = emptyQnsArr();
  const studentData = () => {
    let tempArr = [];
    if (studentDetails.length) {
      studentDetails.map((student) => {
        const name = student.name;
        const id = student.studentId;
        const gender = student.gender;
        let attendance = student.attendance ? ' P ' : ' A ';

        let mark = student.studentMarks?.map((item) => {
          return item.mark !== null ? item.mark : '-';
        });
        return tempArr.push([name, id, gender, attendance, ...mark]);
      });
    } else {
      tempArr = [['FirstName LastName', 'AcademicId', 'Gender', 'P/A', ...emptyQns]];
    }
    return tempArr;
  };
  const studentDataSpread = studentData();
  const attainmentBenchMark = questionMarks
    ? data.questionMarks?.map((question) =>
        question.attainmentBenchMark !== null ? question.attainmentBenchMark : '-'
      )
    : [];
  const totalMark = questionMarks
    ? data.questionMarks?.map((question) =>
        question.totalMark !== null ? question.totalMark : '-'
      )
    : [];
  const convertedIds = outComeIds
    ? outComeIds.map((item) => {
        const returnedData = item?.map((value) => {
          const id = globalOutcomeList?.filter((data) => data.value === value)?.[0]?.['name'];

          const idByName = id ? id : '';
          return idByName;
        });
        return returnedData.join(',');
      })
    : [];
  const headerBeforeConstruct = ['Student Name'];
  headerBeforeConstruct.push('Student_Id', 'Gender', 'Attendance');
  convertedIds.unshift('', '');
  totalMark.unshift('', '');
  attainmentBenchMark.unshift('', '');

  headerBeforeConstruct.push(...gettingQuestions);

  const header = [headerBeforeConstruct];
  const doc = new jsPDF('landscape');
  const bodyData = [
    [
      `${checkingType} - ${type === 'program' ? 'Program' : 'Course'} Learning Outcome`,
      '',
      ...convertedIds,
    ],
    ['Total Mark', '', ...totalMark],
    [`Attainment BenchMark ${benchMark + ' % '}`, '', ...attainmentBenchMark],
    ...studentDataSpread,
  ];
  const uploadedData = { header, bodyData, studentDetails, gettingQuestions };
  jsPDF.autoTableSetDefaults({
    headStyles: { fillColor: '#e0e0e0', textColor: 0 },
    bodyStyles: { fillColor: '#ffffff', textColor: 0 },
  });
  var finalY = doc.lastAutoTable.finalY || 10;

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(
    `${type === 'program' ? 'Program Name' : 'Course Name'}: ${programName.split('-')[0]}`,
    14,
    finalY + 3
  );
  doc.text(`Assessment Name: ${planName}`, 14, finalY + 13);
  doc.text(`Total Marks: ${totalMarks}`, 14, finalY + 23);
  // doc.text(`Total Marks: ${totalMarks}`, 200, finalY + 12, 'right');
  doc.text(`Download Date: ${current_date}`, 230, finalY + 23);
  type === 'program' && doc.text(`CurriculumName: ${curriculumName}`, 150, finalY + 3);
  type === 'program' && doc.text(`Selected Level :${levelNo?.join(',')}`, 150, finalY + 13);
  doc.text(`No of Questions:${noQuestions ? noQuestions : 0}`, 150, finalY + 23);
  doc.line(14, finalY + 25, 283, finalY + 25);
  doc.setFontSize(6);
  if (setExportDownloadTrue) {
    doc.autoTable({
      startY: finalY + 28,
      head: header,
      //horizontalPageBreak: true,
      //horizontalPageBreakRepeat: 0,
      //html: '#table',
      tableWidth: 'auto',
      columnWidth: 'auto',
      theme: 'striped',
      pageBreak: 'auto',
      styles: {
        fontSize: 7,
        overflow: 'linebreak',
        cellWidth: 20,
      },
      columnStyles: {
        0: { cellWidth: 60 },
      },
      body: bodyData,
      didParseCell: function (data) {
        if (data.column.index === 3 && data.section === 'body') {
          if (studentDetails?.length)
            data.cell.styles.textColor =
              data.cell.raw.toString().trim() === 'A' ? [216, 78, 75] : [0, 128, 0];
        }
      },
    });
  }

  setExportDownloadTrue && doc.save(`AssessmentData_${checkingType}.pdf`);
  setExportDownloadTrue && setExportDownloadTrue(false);
  return uploadedData;
};

export default AssessmentDataExport;
