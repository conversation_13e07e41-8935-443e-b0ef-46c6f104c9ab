import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import College_img from 'Assets/college_thumbnail.svg';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { Trans } from 'react-i18next';
import { selectColleges } from '_reduxapi/institution/selectors';
import * as actions from '_reduxapi/institution/actions';
import '../styles.css';
import { getFullAddress } from 'Modules/Institution/components/UniversityDetails/udUtil';
function College(props) {
  const { getColleges, loggedInUserData, allCollegeData } = props;
  const [search, setSearch] = useState('');
  const callId = loggedInUserData.getIn(['university', 'institutionId'], '');

  useEffect(() => {
    const timeout = setTimeout(() => {
      getColleges({ id: callId, search: search });
    }, 500);
    return () => {
      clearTimeout(timeout);
    };
  }, [search]); // eslint-disable-line

  return (
    <div>
      <div className="bg-white rounded global-settings">
        <h3 className="f-22 mb-2 mt-3 pl-3">
          <Trans i18nKey={'add_colleges.all_colleges'}></Trans>
        </h3>
        <div className="col-md-11 pl-3">
          <MaterialInput
            elementType={'materialSearch'}
            changed={(event) => setSearch(event.target.value)}
            placeholder={'Search College By Name or Id'}
            value={search}
          />
        </div>
        {allCollegeData.get('institutes', List()).map((college, index) => {
          return (
            <div className="col-md-11 digi-college-details m-3" key={index}>
              <div className="row">
                <div className="col-md-2 col-xl-1 col-lg-1 col-2 py-3">
                  <img
                    src={college.get('presignedLogoURL', College_img)}
                    height="100%"
                    width="100%"
                    alt="College"
                    className="rounded"
                  />
                </div>
                <div className="col-md-10 col-xl-10 col-lg-10 col-10">
                  <div className="bold pt-3 f-16">
                    {college.get('name', '')} - {college.get('code', '')}
                  </div>
                  <div className="pt-1 f-14 text_wrap">
                    <Trans i18nKey={'address'}></Trans>:{' '}
                    {getFullAddress(college.get('address', List()))}
                  </div>
                  <div className="bold pt-1 f-15">
                    {' '}
                    <Trans i18nKey={'admin'}></Trans> -{' '}
                    {college.get('admin', <Trans i18nKey={'na'}></Trans>)}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
College.propTypes = {
  institution: PropTypes.instanceOf(List),
};
const mapStateToProps = (state) => {
  return {
    allCollegeData: selectColleges(state),
  };
};

College.propTypes = {
  allCollegeData: PropTypes.oneOfType([PropTypes.instanceOf(Map), PropTypes.instanceOf(List)]),
  getColleges: PropTypes.func,
  loggedInUserData: PropTypes.instanceOf(Map),
};
export default compose(withRouter, connect(mapStateToProps, actions))(College);
