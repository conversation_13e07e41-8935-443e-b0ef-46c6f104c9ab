const sessionTrackingReport = (state) => state.sessionTrackingReport;

const selectLoading = (state) => sessionTrackingReport(state).get('loading');
const selectMessage = (state) => sessionTrackingReport(state).get('message');
const selectSessionReport = (state) => sessionTrackingReport(state).get('sessionReport');
const selectSingleSessionReport = (state) =>
  sessionTrackingReport(state).get('singleSessionReport');
const selectProgramList = (state) => sessionTrackingReport(state).get('programList');
const selectProgramCourseList = (state) => sessionTrackingReport(state).get('programCourseList');
const selectExportReport = (state) => sessionTrackingReport(state).get('exportReport');

export {
  selectLoading,
  selectMessage,
  selectSessionReport,
  selectSingleSessionReport,
  selectProgramList,
  selectProgramCourseList,
  selectExportReport,
};
