import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import * as actions from '../../../_reduxapi/leave_management/actions';
import moment from 'moment';
import EmployeeModal from '../modal/EmployeeModal';
import ExportModal from '../modal/Exportmodal';
import { selectAllProgramLists, selectSelectedRole } from '../../../_reduxapi/Common/Selectors';
import {
  selectActiveInstitutionCalendar,
  selectReviewerList,
} from '../../../_reduxapi/leave_management/selectors';
import { Button, Badge, Table } from 'react-bootstrap';
import LocalStorageService from 'LocalStorageService';

const TABS = ['Permission', 'Leave', 'On Duty'];
const LEAVE_TYPE = {
  0: 'permission',
  1: 'leave',
  2: 'on_duty',
};

class ApproverLeave extends Component {
  constructor(props) {
    super(props);
    this.state = {
      completeView: true,
      inactiveView: false,
      ondutyView: true,
      reviewed: false,
      rejected: false,
      pending: false,
      review: false,
      leaveList: [],
      activeTab: 1,
      show: false,
      showexport: false,
      employee_id: null,
    };
  }

  inactiveTab = () => {
    this.setState({
      completeView: false,
      inactiveView: true,
      ondutyView: false,
    });
  };
  ondutyTab = () => {
    this.setState({
      completeView: false,
      inactiveView: false,
      ondutyView: true,
    });
  };
  completeTab = () => {
    this.setState({
      completeView: true,
      inactiveView: false,
      ondutyView: false,
    });
  };

  exportShow = (status) => {
    this.setState({
      showexport: status,
    });
  };
  componentDidMount() {
    setTimeout(() => {
      const { activeinstitutionCalendar } = this.props;
      this.setState({ pending: true });
      const institution_id = activeinstitutionCalendar.get('_id');

      let activeid = +LocalStorageService.getCustomToken('activeLeaveid');
      this.setState(
        {
          activeTab: this.state.activeTab === activeid ? this.state.activeTab : activeid,
        },
        () => {
          this.props.reviewer(LEAVE_TYPE[this.state.activeTab], institution_id, 'approver');
        }
      );
    }, 100);
  }

  handlereview = (name) => {
    const { activeinstitutionCalendar } = this.props;
    if (name === 'reviewed') {
      this.setState({ reviewed: true, rejected: false, pending: false });
    }
    if (name === 'rejected') {
      this.setState({ rejected: true, reviewed: false, pending: false });
    }
    if (name === 'pending') {
      this.setState({ rejected: false, reviewed: false, pending: true });
    }

    const institution_id = activeinstitutionCalendar.get('_id');
    this.props.reviewer(LEAVE_TYPE[this.state.activeTab], institution_id, 'approver');
  };
  handleChange = (e) => {
    if (e.target.value === '') {
      this.props.reviewer(
        LEAVE_TYPE[this.state.activeTab],
        this.props.activeinstitutionCalendar.get('_id'),
        'approver'
      );
    } else {
      if (e.target.value.length > 3) {
        this.props.getSearch({
          userType: 'staff',
          type: LEAVE_TYPE[this.state.activeTab],
          institution_id: this.props.activeinstitutionCalendar.get('_id'),
          assign: 'approver',
          textvalue: e.target.value,
        });
      }
    }
  };

  onTabChange(index) {
    this.setState(
      {
        activeTab: index,
      },
      () => {
        const institution_id = this.props.activeinstitutionCalendar.get('_id');
        this.props.reviewer(LEAVE_TYPE[this.state.activeTab], institution_id, 'approver');
        LocalStorageService.setCustomToken('activeLeaveid', this.state.activeTab);
      }
    );
  }
  edit = (data, active, tab) => {
    const datalist = data.toJS();
    const list = {
      ...datalist,
      activeTab: active,
      leave_flow: 'approve',
    };

    this.props.setLeaveData(list);
    this.props.history.push(`/leave-management/${list.leave_flow}/${tab}`);
  };
  employeedetails = (status, data) => {
    this.setState({ show: status, employee_id: data });
  };
  programdetails = (data) => {
    return (
      <div className="aw-150">
        {' '}
        <b className="ml-4">
          {' '}
          {this.props?.allprogramList
            .filter((item) => item?.id === data)
            .map((item) => {
              return item?.name;
            })}
        </b>
      </div>
    );
  };
  render() {
    const { reviewerList } = this.props;
    const { pending, reviewed, rejected, activeTab } = this.state;
    const list = pending ? 'pending' : reviewed ? 'approved' : 'rejected';

    return (
      <>
        <div className="customize_tab">
          <ul id="menu">
            {TABS.map((tab, i) => (
              <span
                key={`${tab}-${i}`}
                onClick={this.onTabChange.bind(this, i)}
                className={`tabaligment${i === activeTab ? ' tabactive' : ''}`}
              >
                {tab}
              </span>
            ))}
          </ul>
        </div>

        <div className="main pt-3 pb-5 bg-white">
          <div className="container">
            <div className="row pb-3">
              <div className="col-md-5">
                <span className="pr-3" onClick={() => this.handlereview('pending')}>
                  <Badge
                    variant={
                      pending === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    Approve Pending
                  </Badge>
                </span>
                <span className="pr-3" onClick={() => this.handlereview('reviewed')}>
                  <Badge
                    variant={
                      reviewed === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    Approved
                  </Badge>
                </span>
                <span className="pr-3" onClick={() => this.handlereview('rejected')}>
                  <Badge
                    variant={
                      rejected === true
                        ? 'light badge_grouping badge_grouping_active'
                        : 'light badge_grouping '
                    }
                    size="sm"
                  >
                    Rejected/Cancelled
                  </Badge>
                </span>
              </div>

              <div className="col-md-7">
                <div className="row">
                  <div className="col-md-10">
                    <div className="sb-example-1">
                      <div className="search">
                        <input
                          type="text"
                          className="searchTerm"
                          placeholder="Search by employee id,name,.."
                          onClick={(e) => this.handleChange(e)}
                        />
                        <button type="submit" className="searchButton">
                          <i className="fa fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-2">
                    <div className="float-right">
                      <Button variant="primary" className="f-14" onClick={this.exportShow}>
                        Export{' '}
                      </Button>{' '}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <div className="row pt-4">
              <div className="col-md-12">
             */}
            <div className="row pb-3">
              <div className="col-md-12">
                <b className="f-15 ">List of all pending {LEAVE_TYPE[activeTab]} applications </b>
                <div className="dash-table">
                  <Table responsive>
                    <thead className="group_table_top">
                      <tr>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className="fa fa-align-center remove_hover pr-2"
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold">Employee ID</span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className="fa fa-align-center remove_hover pr-2"
                                aria-hidden="true"
                              >
                                <span className="pl-1 font-weight-bold">Employee Name</span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div id="icon_space">
                            <li id="icon_space_li">
                              {' '}
                              <i
                                className="fa fa-align-center remove_hover pr-2"
                                aria-hidden="true"
                              >
                                <span className="pl-1 ">Program </span>
                              </i>
                            </li>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <span className="">From</span>
                        </th>
                        <th className="border_color_blue">
                          <b className="">To</b>
                        </th>
                        <th className="border_color_blue">
                          <b className="">Leave Type</b>
                        </th>
                        <th className="border_color_blue">
                          <b className="">Time Stamp</b>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {reviewerList?.getIn([0, `${list}`])?.map((data, i) => {
                        return (
                          <tr
                            className="tr-bottom-border"
                            onClick={() => {
                              this.edit(data, LEAVE_TYPE[activeTab], activeTab);
                            }}
                          >
                            <td
                              className="text-blue remove_hover"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                this.employeedetails(true, data.getIn(['user_data', 0, '_id'], ''));
                              }}
                            >
                              <p className="pl-20 mb-0">
                                {data.getIn(['user_data', 0, 'user_id'], '')}{' '}
                              </p>
                            </td>
                            <td className="text-blue">
                              <p className="pl-20 mb-0">
                                {data.getIn(['user_data', 0, 'name', 'first'], '') +
                                  data.getIn(['user_data', 0, 'name', 'middle'], '') +
                                  data.getIn(['user_data', 0, 'name', 'last'], '')}{' '}
                              </p>
                            </td>

                            <td className="text-blue">
                              {this.programdetails(
                                data.getIn(
                                  ['user_data', 0, 'academic_allocation', 0, '_program_id'],
                                  ''
                                )
                              )}{' '}
                            </td>
                            <td>
                              <p className="mb-0">
                                {' '}
                                {moment(data.get('from')).format('DD MMM	YYYY')}
                              </p>
                            </td>
                            <td>
                              <p className="mb-0"> {moment(data.get('to')).format('DD MMM	YYYY')}</p>
                            </td>
                            <td>
                              <p className=" mb-0">{data.getIn(['leave_type', 'name'])}</p>
                            </td>
                            <td>
                              <p className=" mb-0">
                                {' '}
                                {moment(data.get('updatedAt')).format('DD MMM	YYYY hh:mm A')}
                              </p>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                </div>
              </div>
            </div>
            {/* <div className="leaveManage mb-2">
                  <table className="table">
                    <thead className="group_table_top">
                      <tr>
                        <th className="border_color_blue">
                          <div className="aw-50">
                            <b>S.NO </b>
                          </div>
                        </th>

                        <th className="border_color_blue">
                          <div className="aw-100">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className="fa fa-align-center remove_hover pr-2"
                                  aria-hidden="true"
                                ></i>
                                <b>Employee ID</b>
                              </li>
                            </div>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-100">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className="fa fa-align-center remove_hover pr-2"
                                  aria-hidden="true"
                                ></i>
                                <b>Employee Name</b>
                              </li>
                            </div>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-100">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                {' '}
                                <i
                                  className="fa fa-align-center remove_hover pr-2"
                                  aria-hidden="true"
                                ></i>
                                <b>Program</b>
                              </li>
                            </div>
                          </div>
                        </th>
                        {LEAVE_TYPE[activeTab] !== 'permission' && (
                          <>
                            <th className="border_color_blue">
                              <div className="aw-100">
                                <div id="icon_space">
                                  <li id="icon_space_li">
                                    {' '}
                                    <i
                                      className="fa fa-align-center remove_hover pr-2"
                                      aria-hidden="true"
                                    ></i>
                                    <b>From </b>
                                  </li>
                                </div>
                              </div>
                            </th>
                            <th className="border_color_blue">
                              <div className="aw-100">
                                <b className="">To</b>{' '}
                              </div>
                            </th>
                          </>
                        )}
                        {LEAVE_TYPE[activeTab] === 'permission' && (
                          <>
                            <th className="border_color_blue">
                              <div className="aw-100">
                                <div id="icon_space">
                                  <li id="icon_space_li">
                                    {' '}
                                    <i
                                      className="fa fa-align-center remove_hover pr-2"
                                      aria-hidden="true"
                                    ></i>
                                    <b>Permission Date </b>
                                  </li>
                                </div>
                              </div>
                            </th>
                          </>
                        )}
                        <th className="border_color_blue">
                          <div className="aw-100">
                            <b className="">Duration</b>
                          </div>
                        </th>
                        {LEAVE_TYPE[activeTab] !== 'permission' && (
                          <>
                            <th className="border_color_blue">
                              <div className="aw-150">
                                <b className="">
                                  {LEAVE_TYPE[activeTab] === 'on_duty'
                                    ? 'Onduty'
                                    : activeTab === 3
                                    ? LEAVE_TYPE[2]?.charAt(0).toUpperCase() +
                                      LEAVE_TYPE[activeTab]?.slice(1)
                                    : LEAVE_TYPE[2]?.charAt(0).toUpperCase() +
                                      LEAVE_TYPE[activeTab]?.slice(1)}{' '}
                                  category
                                </b>
                              </div>
                            </th>
                            <th className="border_color_blue">
                              <div className="aw-150">
                                <b className="">
                                  {LEAVE_TYPE[activeTab] === 'on_duty'
                                    ? 'Onduty'
                                    : activeTab === 3
                                    ? LEAVE_TYPE[2]?.charAt(0).toUpperCase() +
                                      LEAVE_TYPE[activeTab]?.slice(1)
                                    : LEAVE_TYPE[2]?.charAt(0).toUpperCase() +
                                      LEAVE_TYPE[activeTab]?.slice(1)}{' '}
                                  type
                                </b>
                              </div>
                            </th>
                          </>
                        )}
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Reason</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Salary</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Details</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-200">
                            <b className="">Timestamp</b>
                          </div>
                        </th>
                      </tr>
                    </thead>

                    {reviewerList?.getIn([0, `${list}`])?.map((data, i) => {
                      return (
                        <tbody className="leaveManage-height">
                          <tr className="tr-bottom-border">
                            <td>
                              <div className="aw-50">
                                {' '}
                                <b className=""> {i + 1}</b>
                              </div>
                            </td>
                            <td>
                              <div
                                className="aw-100"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  this.employeedetails(true, data.getIn(['user_data', 0, '_id']));
                                }}
                              >
                                {' '}
                                <b className=""> {data.getIn(['user_data', 0, 'user_id'])} </b>
                              </div>
                            </td>

                            <td>
                              <div className="aw-100">
                                {' '}
                                <b className="">
                                  {' '}
                                  {data.getIn(['user_data', 0, 'name', 'first'], '') +
                                    data.getIn(['user_data', 0, 'name', 'middle'], '') +
                                    data.getIn(['user_data', 0, 'name', 'last'], '')}{' '}
                                </b>
                              </div>
                            </td>
                            <td>
                              <div className="aw-100">
                                {' '}
                                <b className="">
                                  {' '}
                                  {this.programdetails(
                                    data.getIn(
                                      ['user_data', 0, 'academic_allocation', 0, '_program_id'],
                                      ''
                                    )
                                  )}{' '}
                                </b>
                              </div>
                            </td>
                            {LEAVE_TYPE[activeTab] !== 'permission' && (
                              <>
                                <td>
                                  <div className="aw-100">
                                    {' '}
                                    <b className="">
                                      {' '}
                                      {moment(data.get('from')).format('DD MMM	YYYY')}
                                    </b>
                                  </div>
                                </td>
                                <td>
                                  {' '}
                                  <div className="aw-100">
                                    {' '}
                                    <b className="">
                                      {' '}
                                      {moment(data.get('to')).format('DD MMM	YYYY')}
                                    </b>
                                  </div>
                                </td>
                              </>
                            )}
                            {LEAVE_TYPE[activeTab] === 'permission' && (
                              <>
                                <td>
                                  <div className="aw-100">
                                    {' '}
                                    <b className="">
                                      {' '}
                                      {moment(data.get('from')).format('hh:mm ')}-{' '}
                                      {moment(data.get('to')).format('hh:mm A')}{' '}
                                      {moment(data.get('from')).format('DD MMM	YYYY')}
                                    </b>
                                  </div>
                                </td>
                              </>
                            )}

                            {LEAVE_TYPE[this.state.activeTab] === 'permission' && (
                              <td>
                                {' '}
                                <div className="aw-100">
                                  {' '}
                                  <b className="">
                                    {' '}
                                    {formatDistanceStrict(
                                      new Date(data.get('from')),
                                      new Date(data.get('to'))
                                    )}
                                  </b>
                                </div>
                              </td>
                            )}
                            {LEAVE_TYPE[this.state.activeTab] !== 'permission' && (
                              <td>
                                {' '}
                                <div className="aw-100">
                                  {' '}
                                  <b className=""> {data.get('days')}days</b>
                                </div>
                              </td>
                            )}

                            {LEAVE_TYPE[this.state.activeTab] !== 'permission' && (
                              <>
                                {' '}
                                <td>
                                  {' '}
                                  <div className="aw-150">
                                    {' '}
                                    <b className=""> {data.getIn(['leave_category', 'name'])}</b>
                                  </div>
                                </td>
                                <td>
                                  {' '}
                                  <div className="aw-150">
                                    {' '}
                                    <b className=""> {data.getIn(['leave_type', 'name'])}</b>
                                  </div>
                                </td>
                              </>
                            )}

                            <td>
                              {' '}
                              <div className="aw-200">
                                {' '}
                                <b className=""> {data.get('reason')}</b>
                              </div>
                            </td>
                            <td>
                              {' '}
                              <div className="aw-200">
                                {' '}
                                <b className=""> {data.get('payment_status')}</b>
                              </div>
                            </td>
                            <Link>
                              <td>
                                {' '}
                                <div className="aw-200">
                                  {' '}
                                  <b
                                    className=""
                                    onClick={() => {
                                      this.edit(data, LEAVE_TYPE[activeTab], activeTab);
                                    }}
                                  >
                                    {' '}
                                    View
                                  </b>
                                </div>
                              </td>
                            </Link>
                            <td>
                              {' '}
                              <div className="aw-200">
                                {' '}
                                <b className="">
                                  {' '}
                                  {moment(data.get('updatedAt')).format('DD MMM	YYYY hh:mm A')}
                                </b>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      );
                    })}
                  </table>
                </div> */}
          </div>
          {this.state.show && (
            <EmployeeModal
              closed={this.employeedetails.bind(this)}
              dataId={this.state.employee_id}
            />
          )}
          {this.state.showexport && (
            <ExportModal
              closed={this.exportShow.bind(this)}
              data={reviewerList?.getIn([0, `${list}`])}
              status={list}
              leavetype={LEAVE_TYPE[this.state.activeTab]}
            />
          )}
        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    activeinstitutionCalendar: selectActiveInstitutionCalendar(state),
    reviewerList: selectReviewerList(state),
    allprogramList: selectAllProgramLists(state),
    selectedRole: selectSelectedRole(state),
  };
};
export default compose(withRouter, connect(mapStateToProps, actions))(ApproverLeave);
