import { Map, List } from 'immutable';
import { timeFormat } from '../../../../utils';
import moment from 'moment';
import { t } from 'i18next';

const fieldNames = {
  title: 'Title',
  gender: 'Gender',
  mode: 'Mode',
  days: 'Days',
  startTime: 'Start Time',
  endTime: 'End Time',
};

const fields = [
  'title',
  'gender',
  'mode',
  'days',
  'startTime',
  'endTime',
  'allowCourseCoordinatesToEdit',
];

function modifyState(key, data) {
  if (key === 'allowCourseCoordinatesToEdit') {
    if (data === '') {
      return false;
    } else {
      return data;
    }
  } else {
    return data;
  }
}

export function getTrimmedValues(data) {
  let updatedData = Map();
  fields.forEach((key) => {
    updatedData = !['startTime', 'endTime', 'allowCourseCoordinatesToEdit', 'days'].includes(key)
      ? updatedData.set(key, data.get(key, '').trim())
      : updatedData.set(key, modifyState(key, data.get(key, '')));
  });
  return updatedData;
}

// function checkTitleExists(data, allData, currentId) {
//   return Boolean(
//     allData.find((r) => {
//       if (currentId === '') {
//         return r.get('title') === data.get('title');
//       } else {
//         return r.get('title') === data.get('title') && currentId !== r.get('_id');
//       }
//     })
//   );
// }

function checkDateAndTime(data, rData) {
  const checkStartTime = Date.parse(data.get('startTime'));
  const checkEndTime = Date.parse(data.get('endTime'));
  const startTime = Date.parse(formatDateTime(rData.get('startTime')));
  const endTime = Date.parse(formatDateTime(rData.get('endTime')));
  if (
    (startTime < checkStartTime || checkEndTime > startTime) &&
    (endTime > checkStartTime || checkEndTime < endTime)
  ) {
    return false;
  }
  return true;
}

function checkDataExists(data, allData, currentId) {
  const dt = allData.find((r) => {
    const array1Days = r.get('days').toJS();
    const array2Days = data.get('days').toJS();
    const intersectionArray = array1Days.filter((element) => array2Days.includes(element));
    if (currentId === '') {
      return (
        r.get('gender') === data.get('gender') &&
        intersectionArray.length > 0 &&
        !checkDateAndTime(data, r)
      );
    } else {
      return (
        currentId !== r.get('_id') &&
        r.get('gender') === data.get('gender') &&
        intersectionArray.length > 0 &&
        !checkDateAndTime(data, r)
      );
    }
  });
  return Boolean(dt);
}

export function validateData(data, allData, currentId) {
  const errorFields = [];
  ['title', 'gender', 'mode', 'days', 'startTime', 'endTime'].forEach((key) => {
    if (!data.get(key, '') || data.get(key, '') === null) {
      errorFields.push(fieldNames[key]);
    } else if (key === 'days' && data.get(key, List()).size === 0) {
      errorFields.push(fieldNames[key]);
    }
  });

  if (errorFields.length) {
    return `${getTranslatedKeys(errorFields[0])}`;
  }

  if (!isValidDate(data)) {
    return t('course_schedule.validation.end_time_should_be_greater_than_start_time');
  }
  //if (checkTitleExists(data, allData, currentId)) return 'Title already exists';
  if (checkDataExists(data, allData, currentId))
    return t('course_schedule.validation.data_already_exists');

  return '';
}

export function formatDateObject(data) {
  return {
    hour: timeFormat(moment(data).format('h')),
    minute: parseInt(moment(data).format('mm')),
    format: moment(data).format('A'),
  };
}

export function checkFormatDateObject(data) {
  return {
    hour: parseInt(moment(data).format('h')),
    minute: parseInt(moment(data).format('mm')),
    format: moment(data).format('A'),
  };
}

export function formatDateTime(time) {
  const date =
    moment().format('LL') +
    ' ' +
    time.get('hour', '08') +
    ':' +
    time.get('minute', '00') +
    ' ' +
    time.get('format', 'AM');
  return new Date(date);
}

function isValidDate(data) {
  if (data.get('startTime') !== null && data.get('endTime') !== null) {
    if (Date.parse(data.get('startTime')) < Date.parse(data.get('endTime'))) {
      return true;
    } else {
      return false;
    }
  }
  return true;
}

export function sortDays(dys) {
  var daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  for (var i = 0; i < 7; i++) daysOfWeek.push(daysOfWeek.shift());
  return daysOfWeek.filter(function (d) {
    return dys.indexOf(d) >= 0;
  });
}

export const getTranslatedKeys = (day) => {
  const daysObj = [
    { name: 'Title', transKey: 'course_schedule.fieldNames.Title' },
    { name: 'Gender', transKey: 'course_schedule.fieldNames.Gender' },
    { name: 'Mode', transKey: 'course_schedule.fieldNames.Mode' },
    { name: 'Days', transKey: 'course_schedule.fieldNames.Days' },
    { name: 'Start Time', transKey: 'course_schedule.fieldNames.Start Time' },
    { name: 'End Time', transKey: 'course_schedule.fieldNames.End Time' },
  ];
  daysObj.forEach(({ name, transKey }) => {
    if (day.includes(name)) day = day.replaceAll(name, t(transKey));
  });
  return day;
};
