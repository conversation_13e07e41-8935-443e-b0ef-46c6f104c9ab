import React, { useMemo, useRef, useState } from 'react';
import { List, Map as IMap, fromJS } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import Button from 'Widgets/FormElements/material/Button';
import SubdirectoryArrowRightIcon from '@mui/icons-material/SubdirectoryArrowRight';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import InfoIcon from '@mui/icons-material/Info';
import { jsUcfirstAll } from 'utils';
import { createDuplicateForm, getCategoryForm, updateDuplicateForm } from '_reduxapi/q360/actions';
import LoadSelectedChips, {
  useDispatchAndSelectorFunctionsQlc,
  AccordionLayoutProgramCreation,
  MenuWithOpenAndClose,
  useMatchingForms,
  describeSx,
  typeOfForms,
} from './utils';
import {
  Checkbox,
  FormControl,
  RadioGroup,
  Radio,
  FormControlLabel,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
} from '@mui/material';
import PropTypes from 'prop-types';

export default function FormConfigurationProgramWise({
  params,
  handleClose,
  existingData = IMap(),
}) {
  const [deletedManipulatedIds, setDeletedManipulatedIds] = useState(IMap());
  const [pgmDetailsState, setPgmDetailsState] = useState(IMap());
  const [pgmDetailsParentState, setPgmDetailsParentState] = useState(IMap());

  const [formData, setFormData] = useState(
    IMap({
      formName: existingData.get('formName', ''),
      describe: existingData.get('describe', ''),
      formType: existingData.get('formType', 'complete'),
      incorporateMandatory: existingData.get('incorporateMandatory', false),
    })
  );

  const {
    fetchCurriculumDetails,
    programDetails: reduxProgramDetails,
    dispatch,
    configureTemplate,
    currentCategoryIndex,
    setMessage,
  } = useDispatchAndSelectorFunctionsQlc();
  const isIncorporateMandatory = configureTemplate.getIn(
    [currentCategoryIndex, 'actions', 'incorporateMandatory'],
    false
  );
  const categoryFormType = configureTemplate.getIn([currentCategoryIndex, 'categoryFormType'], '');
  const reduxSelectedProgramIds = useMemo(function () {
    const uniqueProgramIds = new Set();
    for (const program of existingData.get('selectedProgram', List())) {
      if (program.get('all', false)) uniqueProgramIds.add(program.get('programId', ''));
    }
    return fromJS([...uniqueProgramIds]);
  }, []);
  const [constructedPgmCreation, setConstructedPgmCreation] = useMatchingForms(
    existingData.get('formId', '')
  );
  const [selectedProgramIds, setSelectedProgramIds] = useState(reduxSelectedProgramIds);

  const fetchApi = (pgmId, checked = false, callBack = null, program_name) => {
    fetchCurriculumDetails({
      params: { programId: pgmId }, //65606e4f01fe4248e23d151e
      checked,
      cb: callBack,
      programId: pgmId,
      program_name: program_name,
    });
  };

  function checkedProcessCallBack(reduxState, programId) {
    setPgmDetailsState((prev) => {
      return prev
        .update(programId, IMap(), () => {
          let selectedProgram = reduxState.get(programId, IMap());
          selectedProgram = selectedProgram.update('curriculum', IMap(), (curriculum) => {
            return curriculum.map((cur) => cur.set('isChecked', true));
          });
          return selectedProgram;
        })
        .updateIn([programId, 'selectedProgramCount'], () => {
          return reduxState.getIn([programId, 'constructedProgramCount'], 0);
        });
    });
  } //this is for make seelcted coutn full and checked true

  function clearAll() {
    // if (pgmDetailsParentState.size || pgmDetailsState.size) {
    //   setPgmDetailsParentState(IMap());
    //   setPgmDetailsState(IMap());
    //   return;
    // }
    // manipulatingProgramData('');
    setPgmDetailsState(pgmDetailsParentState);
  }

  function onSave() {
    setPgmDetailsParentState(
      pgmDetailsState.filter((program) => program.get('selectedProgramCount', 0) > 0)
    );
  }
  function handleConstruction() {
    const selectCourses = [];
    const selectedProgram = [];
    const selectedProgramNewSet = [];
    for (const [programId, program] of pgmDetailsParentState.entrySeq()) {
      // if (program.get('selectedProgramCount', 0) === 0) continue;
      for (const [curId, cur] of program.get('curriculum', IMap()).entrySeq()) {
        const checkNewlySelectedCheckbox =
          cur.get('isChecked', false) && !cur.has('_manipulate_id', ''); // this is for when uesr checked the new checkbox with make sure doesn't exist in previous user respone
        const thisIsDeletedCheckBoxId =
          !cur.get('isChecked', false) &&
          deletedManipulatedIds.hasIn([programId, cur.get('_manipulate_id', '')]); //is user uncheck the checkbox and this checkbox is previously selected by user
        if (checkNewlySelectedCheckbox) {
          selectCourses.push({
            programId: programId,
            programName: reduxProgramDetails.getIn([programId, 'program_name'], ''),
            curriculumId: curId,
            curriculumName: cur.get('curriculum_name'),
          });
          continue;
        }
        if (thisIsDeletedCheckBoxId) {
          selectCourses.push({
            _id: cur.get('_manipulate_id', ''),
            isDeleted: true,
          });
        }
      }
      if (selectedProgramNewSet.includes(programId)) {
        continue;
      }
      selectedProgramNewSet.push(programId);
      selectedProgram.push({
        programId,
        programName: reduxProgramDetails.getIn([programId, 'program_name'], ''),
        all:
          program.get('selectedProgramCount', -1) ===
          reduxProgramDetails.getIn([programId, 'constructedProgramCount'], -1),
      });
    }
    for (const program of existingData.get('selectedProgram', List())) {
      const programId = program.get('programId', '');
      if (pgmDetailsParentState.has(programId)) {
        continue;
      }
      if (!deletedManipulatedIds.has(programId)) {
        selectedProgram.push({
          programId: programId,
          programName: program.get('programName', ''),
          all: selectedProgramIds.includes(programId),
        });
      }
      if (!selectedProgramIds.includes(programId)) {
        for (const eachExistingCourseId of deletedManipulatedIds.get(programId, IMap())) {
          selectCourses.push({
            _id: eachExistingCourseId[0],
            isDeleted: true,
          });
        }
      }
    }
    if (selectCourses.length === 0)
      return {
        selectedProgram,
      };
    return {
      selectCourses,
      selectedProgram,
    };
  }

  const handleCheckboxInput = (key) => (e) => {
    setFormData((prev) => prev.set(key, e.target.checked));
  };

  function handleCreateCallback() {
    dispatch(getCategoryForm(params));
    handleClose();
  }

  function handleCreate() {
    if (!formData.get('formName', '').trim()) {
      return setMessage('Enter the Form Name');
    }
    if (selectedProgramIds.size === 0 && pgmDetailsParentState.isEmpty()) {
      return setMessage('Select the curriculum from program');
    }
    const payload = {
      [existingData.has('formId') ? 'categoryFormId' : 'categoryId']: existingData.get(
        'formId',
        configureTemplate.getIn([currentCategoryIndex, '_id'])
      ),
      ...(existingData.get('formName', '') !== formData.get('formName', '') && {
        formName: formData.get('formName', ''),
      }),
      describe: formData.get('describe', ''),
      formType: formData.get('formType', ''),
      categoryFormType: categoryFormType,
      incorporateMandatory: formData.get('incorporateMandatory', false),
      categoryId: configureTemplate.getIn([currentCategoryIndex, '_id'], ''),
      ...handleConstruction(),
    };
    if (existingData.has('formId'))
      return dispatch(updateDuplicateForm(payload, handleCreateCallback));
    payload['categorySettings'] = {
      level: configureTemplate.getIn([currentCategoryIndex, 'level'], ''),
    };
    const actions = {};
    actions['studentGroups'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'studentGroups'],
      false
    );
    actions['everyAcademic'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'everyAcademic'],
      false
    );
    actions['occurrenceConfiguration'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'occurrenceConfiguration'],
      false
    );
    actions['attemptType'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'attemptType'],
      false
    );
    actions['academicTerms'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'academicTerms'],
      false
    );
    payload['actions'] = actions;
    dispatch(createDuplicateForm(payload, params, 'create', handleCreateCallback));
  }

  const handleInput = (key) => (e) => {
    setFormData((prev) => prev.set(key, e.target.value));
  };

  const deleteExistingProgramCreationData = (programId) => () => {
    //we are deleting programIds already selected comes from backend not newly selected ones( edit purpose)
    setConstructedPgmCreation((prev) => prev.delete(programId));
    setSelectedProgramIds((prev) => prev.filter((pgmId) => pgmId !== programId));
    let ids = IMap();
    for (const curriculum of constructedPgmCreation.get(programId, List())) {
      ids = ids.set(curriculum.get('_id', ''), curriculum.get('_id', ''));
    }
    setDeletedManipulatedIds((prev) => prev.set(programId, ids));
  };

  const removingProgramFrom_deletedManipulatedIds = (programId) => {
    setDeletedManipulatedIds((prev) => prev.delete(programId));
  };

  const set_program_details_from_redux_state = (programId) => {
    if (
      reduxProgramDetails.getIn([programId, 'selectedProgramCount'], 0) ===
      reduxProgramDetails.getIn([programId, 'constructedProgramCount'], -1)
    ) {
      setPgmDetailsState((prev) => prev.set(programId, reduxProgramDetails.get(programId, IMap())));
    } else {
      checkedProcessCallBack(reduxProgramDetails, programId);
    }
    removingProgramFrom_deletedManipulatedIds(programId);
  };
  const push_programId_redux_to_local_state = (programId) => {
    setSelectedProgramIds((prev) => prev.push(programId));
    removingProgramFrom_deletedManipulatedIds(programId);
  };
  const incorporate_existing_assigned_course_with_current_redux_response = (
    response,
    programId
  ) => {
    for (const eachCurriculum of constructedPgmCreation.get(programId, List())) {
      response = response.setIn(
        [programId, 'curriculum', eachCurriculum.get('curriculumId', ''), '_manipulate_id'],
        eachCurriculum.get('_id', '')
      );
    }
    return response;
  };
  const syncing_previous_selected_checkbox_data_with_current_state = (response, programId) => {
    for (const eachCourse of constructedPgmCreation.get(programId, List())) {
      response = response
        .setIn([programId, 'curriculum', eachCourse.get('curriculumId', ''), 'isChecked'], true)
        .updateIn([programId, 'selectedProgramCount'], 0, (count) => count + 1);
    }
    return response;
  };
  const handleProgramCheckedRedirect = (programId, program_name) => (e) => {
    const checked = e?.target?.checked;
    // purpose-1=> this is for when use check and uncheck the program checkbox we are doing something
    // purpose-2=> and this same function we can use for remove icon of outer layer of Popup
    //because it is similiar to unChecked action
    if (checked) {
      // condition 1  // if it is exist in redux just we set program data form redux to local state
      if (reduxProgramDetails.has(programId)) {
        return set_program_details_from_redux_state(programId);
      }
      //condition 2  this is for when user don't expand the accordion and he previously selected (for edit) data
      if (reduxSelectedProgramIds.includes(programId)) {
        return push_programId_redux_to_local_state(programId);
      }
      //condition 3 this is does't exist pgmDetailsState and selectedProgramIds so we should call api for fresh thing
      return fetchApi(
        programId,
        checked,
        (response) => {
          response = incorporate_existing_assigned_course_with_current_redux_response(
            response,
            programId
          );
          setPgmDetailsState((prev) => prev.merge(response));
        },
        program_name
      );
    }
    //this below block for unchecked
    setPgmDetailsState((prev) => prev.delete(programId));
    if (selectedProgramIds.includes(programId)) {
      deleteExistingProgramCreationData(programId)();
    }
    // setSelectedProgramIds((prev) => prev.filter((pgmId) => pgmId !== programId));
    // const programIds = {};
    // for (const eachCurriculum of constructedPgmCreation.get(programId, List())) {
    //   programIds[eachCurriculum.get('_id', '')] = eachCurriculum.get('_id', '');
    // }
    // setDeletedManipulatedIds((prev) => prev.set(programId, fromJS(programIds)));
  };
  const handleChipClose = (programId) => () => {
    setPgmDetailsParentState((prev) => prev.delete(programId));
  };
  const isProgramChecked = (programId) => {
    if (pgmDetailsState.has(programId)) {
      return (
        reduxProgramDetails.getIn([programId, 'constructedProgramCount'], -1) ===
        pgmDetailsState.getIn([programId, 'selectedProgramCount'], 0)
      );
    }
    return selectedProgramIds.includes(programId);
  };
  const programSelectedCount = (programId) => {
    if (pgmDetailsState.has(programId)) {
      return pgmDetailsState.getIn([programId, 'selectedProgramCount'], 0);
    }
    return constructedPgmCreation.get(programId, List()).size;
  };
  const accordionIsExpanded = (programId, program_name) => {
    if (!reduxProgramDetails.has(programId)) {
      return fetchApi(
        programId,
        false,
        (response) => {
          response = syncing_previous_selected_checkbox_data_with_current_state(
            response,
            programId
          );
          response = incorporate_existing_assigned_course_with_current_redux_response(
            response,
            programId
          );
          setPgmDetailsState((prev) => prev.merge(response));
        },
        program_name
      );
    }
    // if (selectedProgramIds.includes(programId)) {
    //   const response = incorporate_existing_assigned_course_with_current_redux_response(
    //     reduxProgramDetails,
    //     programId
    //   );
    //  return checkedProcessCallBack(response,programId);
    // }
    // if(constructedPgmCreation.has(programId)){

    // }
  };
  const parentDialogRef = useRef(null);
  const selectedPgmDetailsState = pgmDetailsState.filter(
    (program) => program.get('selectedProgramCount', 0) > 0
  );
  return (
    <Dialog
      open={true}
      onClose={handleClose}
      maxWidth={'sm'}
      fullWidth={true}
      // className={`${open ? 'invisible' : 'visible'}`}
    >
      <DialogTitle id="scroll-dialog-title">
        <div className="form_heading_creation">{existingData.get('dialogTitle', '')}</div>
      </DialogTitle>
      <DialogContent ref={parentDialogRef}>
        <div className="">
          <div>
            <MaterialInput
              changed={handleInput('formName')}
              elementType={'materialInput'}
              type={'text'}
              value={formData.get('formName', '')}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey">Form Name</div>}
              placeholder={'Enter Name'}
              sx={{
                '& .MuiInputBase-root': {
                  color: '#000000 !important',
                },
                '& .MuiInputBase-input': {
                  border: '1px solid #D1D5DB !important',
                  borderRadius: '4px',
                  padding: '8px 7px',
                },
              }}
            />
          </div>
          <div>
            <MaterialInput
              changed={handleInput('describe')}
              value={formData.get('describe', '')}
              elementType={'materialTextArea'}
              type={'text'}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey pt-2">Describe (Optional)</div>}
              placeholder={'Describe here'}
              maxRows={'4'}
              minRows={'4'}
              bgWhite={true}
              sx={describeSx}
            />
          </div>
          {isIncorporateMandatory && (
            <div>
              <div className="f-12 fw-400 text-mGrey pt-1">Incorporate Type</div>
              <div className="d-flex align-items-center gap-10 mt-1">
                <Checkbox
                  size="small"
                  checked={formData.get('incorporateMandatory', false)}
                  onChange={handleCheckboxInput('incorporateMandatory')}
                  sx={{
                    padding: '5px 0px',
                    '& .MuiSvgIcon-root': { width: 17, height: 17 },
                    color: '#000000',
                  }}
                />
                <div className="f-14">Incorporate is Mandatory</div>
                <Tooltip title="Incorporate is Mandatory">
                  <InfoIcon className="f-16" sx={{ color: '#6B7280' }} />
                </Tooltip>
              </div>
            </div>
          )}
          {categoryFormType === 'form' && (
            <>
              <div className="f-12 fw-400 text-mGrey mt-2">Form Type</div>
              <FormControl>
                <RadioGroup value={formData.get('formType', '')} onChange={handleInput('formType')}>
                  <div className="d-flex align-items-center gap-10 mt-2">
                    {typeOfForms.map((element, index) => (
                      <div className="d-flex align-items-center gap-10" key={index}>
                        <FormControlLabel
                          key={index}
                          className="m-0 d-flex gap-2"
                          value={element.value}
                          control={
                            <Radio
                              size="small"
                              sx={{
                                padding: '0px',
                                '& .MuiSvgIcon-root': {
                                  width: '17px',
                                  height: '17px',
                                },
                              }}
                            />
                          }
                          label={<div className="f-14 ml-2">{element.label}</div>}
                        />
                        <Tooltip title="Form Type">
                          <InfoIcon className="f-16" sx={{ color: '#6B7280' }} />
                        </Tooltip>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </FormControl>
            </>
          )}

          <div className="mt-1">
            <div className="f-12 fw-400 text-dGrey mb-2 pt-2">Assign Programs</div>
            <div className="  rounded pl-3 q360-select-pd " style={{ border: '1px solid #D1D5DB' }}>
              <MenuWithOpenAndClose
                children1={(handleClick) => (
                  <div
                    className="d-flex align-items-center cursor-pointer"
                    onClick={(e) => {
                      setPgmDetailsState(pgmDetailsParentState);
                      handleClick(e);
                    }}
                  >
                    <div className="text-muted">Select Program</div>
                    <div className="ml-auto cursor">
                      <ArrowDropDownIcon className="pt-1" />
                    </div>
                  </div>
                )}
              >
                {(handleClose) => (
                  <AccordionLayoutProgramCreation
                    accordionIsExpanded={accordionIsExpanded}
                    handleClose={handleClose}
                    handleProgramCheckBox={handleProgramCheckedRedirect}
                    programSelectedCount={programSelectedCount}
                    clearAll={clearAll}
                    onSave={onSave}
                    height={parentDialogRef?.current?.offsetHeight}
                    isProgramChecked={isProgramChecked}
                    isSaveButtonDisabled={
                      !selectedPgmDetailsState.size && !deletedManipulatedIds.size
                    }
                  >
                    {(programId) => {
                      return (
                        <FormCurriculum
                          key={programId}
                          pgmDetailsState={pgmDetailsState}
                          setPgmDetailsState={setPgmDetailsState}
                          programId={programId}
                          setDeletedManipulatedIds={setDeletedManipulatedIds}
                          domLoopPgmDetails={reduxProgramDetails}
                        />
                      );
                    }}
                  </AccordionLayoutProgramCreation>
                )}
              </MenuWithOpenAndClose>
            </div>
            <div className="d-flex align-items-center text-overflow-wrap">
              {constructedPgmCreation.entrySeq().map(([programId, program]) => {
                if (pgmDetailsState.has(programId)) return null;
                return (
                  <LoadSelectedChips
                    key={programId}
                    programId={programId}
                    programName={program.getIn([0, 'programName'], '')}
                    levelLabel={'Curriculum'}
                    onDelete={deleteExistingProgramCreationData}
                    selectedCourseCount={program.size}
                  />
                );
              })}

              {pgmDetailsParentState.entrySeq().map(([programId, program]) => {
                if (!program.get('selectedProgramCount', 0)) return null;
                return (
                  <LoadSelectedChips
                    programId={programId}
                    key={programId}
                    programName={reduxProgramDetails.getIn([programId, 'program_name'], '')}
                    levelLabel={'Curriculum'}
                    onDelete={handleChipClose}
                    selectedCourseCount={program.get('selectedProgramCount', 0)}
                  />
                );
              })}
            </div>
          </div>
        </div>
      </DialogContent>
      <DialogActions
        sx={{
          padding: '16px 24px',
        }}
      >
        <Button
          clicked={handleClose}
          variant="outlined"
          className="px-4"
          size={'small'}
          color={'gray'}
        >
          Cancel
        </Button>
        <Button
          clicked={handleCreate}
          variant="contained"
          className="px-4"
          color="primary"
          size={'small'}
        >
          {existingData.has('formId') ? 'Save' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

FormConfigurationProgramWise.propTypes = {
  existingData: PropTypes.instanceOf(IMap),
  handleClose: PropTypes.func,
  params: PropTypes.string,
  formType: PropTypes.string,
};

function FormCurriculum({
  programId,
  pgmDetailsState,
  setDeletedManipulatedIds,
  setPgmDetailsState,
  domLoopPgmDetails,
}) {
  const curriculum = domLoopPgmDetails.getIn([programId, 'curriculum'], IMap()).entrySeq();

  if (!curriculum.size) {
    return <div className="deepak_developer">No Data...</div>;
  }
  const handleCurriculumCheckBox = (curriculumId) => (e) => {
    const _manipulate_id = pgmDetailsState.getIn(
      [programId, 'curriculum', curriculumId, '_manipulate_id'],
      ''
    );
    const checked = e.target.checked;
    if (checked) {
      setPgmDetailsState((prev) =>
        prev
          .updateIn([programId, 'selectedProgramCount'], 0, (count) => count + 1)
          .setIn([programId, 'curriculum', curriculumId, 'isChecked'], true)
      );
      setDeletedManipulatedIds((prev) =>
        prev.deleteIn([programId, _manipulate_id], _manipulate_id)
      );
      return;
    }
    setPgmDetailsState((prev) =>
      prev
        .updateIn([programId, 'selectedProgramCount'], 0, (count) => count - 1)
        .setIn([programId, 'curriculum', curriculumId, 'isChecked'], false)
    );
    setDeletedManipulatedIds((prev) => prev.setIn([programId, _manipulate_id], _manipulate_id));
  };
  return curriculum.map(([curId, individualCurriculum], index) => (
    <>
      <div className="d-flex align-items-center" key={curId}>
        <div className="mx-2">
          <SubdirectoryArrowRightIcon
            color="primary"
            className="cursor-pointer"
            sx={{ fontSize: 10 }}
          />
        </div>
        <div>
          <Checkbox
            onChange={handleCurriculumCheckBox(curId)}
            size="small"
            checked={pgmDetailsState.getIn([programId, 'curriculum', curId, 'isChecked'], false)}
          />
        </div>

        <div className="f-14">{jsUcfirstAll(individualCurriculum.get('curriculum_name', ''))}</div>
      </div>
      {curriculum.size - 1 > index ? <Divider /> : <></>}
    </>
  ));
}
