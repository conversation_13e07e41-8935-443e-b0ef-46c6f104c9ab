import React, { useState } from 'react';
import { List, Map } from 'immutable';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
// import selectedSendIcon from 'Assets/selectedSendIcon.svg';
import concluding_step_2 from 'Assets/q360_dashboard/concluding_step_2.svg';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import MButton from 'Widgets/FormElements/material/Button';
import { Button, Divider, Menu, MenuItem } from '@mui/material';
import { Box, Paper } from '@mui/material';
import {
  ApproverHierarchy,
  AttachedDocsChatSection,
} from 'Modules/GlobalConfigurationV2/components/Q360Configuration/ConfigureTemplate/FormSettings/Step4';
import { useDispatch, useSelector } from 'react-redux';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import {
  selectFormAddendum,
  selectIncorporateFromWithData,
  selectSingleFormList,
} from '_reduxapi/q360/selectors';
import { useHistory } from 'react-router-dom';
import ReasonModal from '../CategoryOverview/Modal/ReasonModal';
import { setData, updateFormConfig, updateFormStatus } from '_reduxapi/q360/actions';
import LocalStorageService from 'LocalStorageService';
import { useSearchParams } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { combiningProgramAndInstitution } from '../../Utils';
import ShowCapturedDocs from '../CategoryOverview/ShowCapturedDocs';
import IncorporateFromViewModal from '../CategoryOverview/IncorporateFromViewModal';

export default function ConcludingPhaseConfiguration() {
  const formAddendum = useSelector(selectFormAddendum);
  const evidenceDocs = formAddendum.get('formEvidenceAttachment', List());
  const singleFormList = useSelector(selectSingleFormList);
  const history = useHistory();
  const [searchParams] = useSearchParams();
  const formInitiatorId = searchParams.get('formId');
  const calendar = searchParams.get('calendar');
  const dispatch = useDispatch();
  const goBack = () => history.goBack();
  // const incorporateMandatory = (searchParams.get('incorporateMandatory')?.trim() === 'true');
  const status = searchParams.get('status');
  const incorporateFromWith = useSelector(selectIncorporateFromWithData);
  const incorporateFromReason = formAddendum.get('incorporateFromReason', '');
  const captureDocs = formAddendum.get('displayCapture', List());
  const [anchorEl, setAnchorEl] = useState(null);

  const redirectingToFormInitiatorPage = () =>
    history.push(`/qapc/QualityAssuranceProcess?calendar=${calendar}`);
  const handleClick = (event) => {
    if (isFormNotStartedByApprover) {
      return redirectingToFormInitiatorPage();
    }
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const [reasonPersisted, setReasonPersisted] = useState(incorporateFromReason);
  const incorporateWith = incorporateFromWith.get('incorporateWith', List());
  const incorporateFrom = incorporateFromWith.get('incorporateFrom', List());
  const categoryFormId = singleFormList.getIn(['categoryFormId', '_id'], '');
  const approvalLevel = singleFormList.getIn(['categoryFormId', 'approvalLevel'], List());
  const mergedFormId = JSON.parse(LocalStorageService.getCustomToken('mergedFormId')).map(
    (item) => item['formInitiatorId']
  );

  const checkIsLikedEverySection = incorporateFrom.every((section) => section.get('isLike', false));
  const onSave = (type, status = 'draft') => {
    const reason = reasonPersisted;

    if (!checkIsLikedEverySection && reason === '') {
      return dispatch(setData(Map({ message: 'Enter the Reason' })));
    } else {
      if (reason !== formAddendum.get('incorporateFromReason', '')) {
        const payload = {
          categoryFormId,
          formInitiatorIds: [formInitiatorId, mergedFormId],
          incorporateFromReason: reason,
          formGuideResourcesId: formAddendum.get('_id', ''),
        };
        dispatch(updateFormConfig(payload, redirectingToFormInitiatorPage));
      } else {
        const payload = {
          formInitiatorIds: [formInitiatorId, ...mergedFormId],
          archive: false,
          submissionStatus: type,
          isDeleted: false,
          status,
        };

        dispatch(updateFormStatus(payload, redirectingToFormInitiatorPage, type));
      }
    }
  };
  const academicYearNames = singleFormList
    .get('formCalenderIds', List())
    .map((calender) => {
      if (!calender.get('isDeleted', false))
        return calender.getIn(['institutionCalenderId', 'calendar_name'], '');
      return '';
    })
    .join(', ');
  const groupNameFiltered = singleFormList
    .get('selectedGroupName', List())
    .filter((group) => group !== 'none');
  const [showModal, setShowModal] = useState(-1);

  const splittedTags = formAddendum.get('tags', List()).reduce((acc, cur) => {
    const key = cur.get('isConfigured', false) ? 'defaultTags' : 'configuredTags';
    acc = acc.update(key, List(), (tags) => {
      tags = tags.push(cur);
      return tags;
    });
    return acc;
  }, Map({ configuredTags: List(), defaultTags: List() }));

  const isFormNotStartedByApprover = status === 'Yet to Start';
  const term = singleFormList.getIn(['categoryFormGroupId', 'term'], '');
  const attemptType = singleFormList.getIn(['categoryFormGroupId', 'attemptTypeName'], '');

  return (
    <main>
      <section className="bg-white py-3 pr-3 pl-1 w-100 d-flex align-items-center paper_box_shadow_as_figma">
        <div className="d-flex align-items-center flex-grow-1">
          <div className="mx-2 px-1">
            <ArrowBackIcon className="ml-1 cursor-pointer" onClick={goBack} />
          </div>
          <div>
            <div className="grey_shade_2 f-24"> {singleFormList.get('formName', '')}</div>
            <div className="f-14 grey_shade_1">
              {singleFormList.get('level', '') === 'course'
                ? singleFormList.get('courseName', '')
                : singleFormList.get('level', '') === 'program'
                ? singleFormList.get('programName', '')
                : singleFormList.get('institutionName', '')}{' '}
              &#x2022; Academic Year {academicYearNames}
              {['course', 'program'].includes(singleFormList.get('level', '')) && (
                <span>
                  {' '}
                  {term !== 'none' && <span>&#x2022; {term} Term </span>}
                  {attemptType !== 'none' && <span>&#x2022; {attemptType} Attempt Type </span>}
                  {groupNameFiltered.size > 0 && (
                    <span>
                      {' '}
                      &#x2022; {singleFormList.get('selectedGroupName', List()).join(',')}
                    </span>
                  )}
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="d-flex">
          <MButton
            className="text-capitalize  responsiveFontSizeSmall text-secondary border-secondary fw_500 mr-3"
            variant="outlined"
            clicked={goBack}
          >
            Cancel
          </MButton>
          {/* <MButton
            className="text-capitalize  responsiveFontSizeSmall bg-primary"
            variant="contained"
            clicked={onSave}
          >
            Save
          </MButton> */}

          <div>
            <Button
              variant="contained"
              color="primary"
              onClick={handleClick}
              endIcon={isFormNotStartedByApprover ? null : <ArrowDropDownIcon />}
            >
              Save
            </Button>

            {!isFormNotStartedByApprover && (
              <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
                {['draft', 're_submit'].includes(status) && (
                  <MenuItem
                    onClick={() =>
                      onSave(status === 're_submit' ? 'resubmission' : 'submitted', 'Yet to Start')
                    }
                  >
                    Send to Approver
                  </MenuItem>
                )}
                {status === 'draft' && (
                  <MenuItem onClick={() => onSave('draft')}>Save as Draft</MenuItem>
                )}
              </Menu>
            )}
          </div>
        </div>
      </section>
      <header className="d-flex justify-content-center my-4 py-2">
        {/* <div className="bg-white borderRoundeds-12 d-flex px-4 py-3 ">
          <div
            className="d-flex align-items-center gap-8 cursor-pointer"
            // onClick={updateStep(iconIndex)}
          >
            <img src={selectedSendIcon} alt="q360IdeaBook" width={'46px'} />
            <div>
              <div className="f-12 fw-400 text-primary">Step 2/2</div>
              <div className="f-14 fw-500 text-dGrey">Concluding Phase</div>
            </div>
          </div>
           <Divider orientation="vertical" flexItem variant="middle" className="my-2 mx-5" />
          <Fragment key={iconIndex}>
            <div className="text-center cursor-pointer" onClick={updateStep(iconIndex)}>
              <img src={iconData.getIn(['notActive', 'icon'], '')} alt="BookIcon" />
              <p className="m-0 fw-400 text-dGrey f-12 ">
                {iconData.getIn(['notActive', 'name'], '')}
              </p>
            </div>
            <Divider orientation="vertical" flexItem variant="middle" className="my-2 mx-5" />
          </Fragment>
        </div> */}
        <img src={concluding_step_2} alt="concluding_step_2" />
      </header>

      <Paper className="p-4 m-3">
        <div className="d-flex justify-content-between">
          {/* Basic Info Section */}
          <div>
            <p className="f-24 grey_shade_1">
              {singleFormList.getIn(['categoryFormId', 'categoryFormType'], '') === 'template'
                ? 'Template Based Form'
                : singleFormList.getIn(['categoryFormId', 'formType'], '') === 'complete'
                ? 'Complete Form'
                : 'Section Form'}
            </p>
            <p className="f-20 text-blue fw_500">Basic Info</p>

            {splittedTags.get('defaultTags', List()).size > 0 && (
              <div>
                <p className="f-12 grey_shade_4 mt-2">Default Tags</p>
                <div className="mb-3">
                  {splittedTags.get('defaultTags', List()).map((tag) => (
                    <div className="text-capitalize f-14 fw_500" key={tag.get('_id', '')}>
                      {tag.get('name', '')}
                      {tag.get('subTag', List()).size > 0 && (
                        <span>{' - ' + tag.get('subTag', List()).join(', ')}</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            {splittedTags.get('configuredTags', List()).size > 0 && (
              <div>
                <p className="f-12 grey_shade_4 mt-2">Category Tags</p>
                <div className="mb-3">
                  {splittedTags.get('configuredTags', List()).map((tag) => (
                    <div className="text-capitalize f-14 fw_500" key={tag.get('_id', '')}>
                      {tag.get('name', '')}
                      {tag.get('subTag', List()).size > 0 && (
                        <span>{' - ' + tag.get('subTag', List()).join(', ')}</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <div>
            {/* Document Section */}
            {evidenceDocs.size > 0 && (
              <AttachedDocsChatSection
                attachments={evidenceDocs}
                drawerName={'Evidence Documents'}
              />
            )}
          </div>
        </div>

        <section className="">
          {incorporateFrom.size > 0 && (
            <section id="incorporate_category_overview" className="mb-3">
              <div className="d-flex">
                <div className="f-14 mt-3 grey_shade_4 ">Incorporate From*</div>
                {!checkIsLikedEverySection && (
                  <ReasonModal
                    reasonPersisted={reasonPersisted}
                    setReasonPersisted={setReasonPersisted}
                  />
                )}
              </div>
              <div className="d-flex flex-wrap">
                {incorporateFrom.map((section, secIndex) => (
                  <Box
                    key={secIndex}
                    className="d-flex align-items-center px-3 cursor-pointer py-2 mr-3"
                    sx={{
                      border: `1px solid #16A34A`,
                      backgroundColor: '#DCFCE7',
                      width: '240px',
                      marginBottom: '10px',
                      borderRadius: '4px',
                    }}
                    onClick={() => {
                      setShowModal(secIndex);
                    }}
                  >
                    <div className="d-flex flex-column">
                      <div className="f-14">{section.get('formName', '')}</div>
                      <div className="f-10">
                        {combiningProgramAndInstitution(section) +
                          ' - ' +
                          section.get('calendar_name', '')}
                      </div>
                      {/* <div className="f-10">Sections From: {section.get('sectionFrom', '')}</div>
                      <div className="f-10">
                        Sections To:{' '}
                        {section
                          .get('sectionTo', List())
                          .map((section) => section.get('sectionName', ''))

                          .join(', ')}
                      </div> */}
                    </div>
                    <div className="ml-2 mt-2">
                      <ThumbUpAltIcon
                        className="f-16"
                        sx={{ color: section.get('isLike', false) ? 'blue' : 'grey' }}
                      />
                    </div>
                  </Box>
                ))}
              </div>
              {showModal !== -1 && (
                <IncorporateFromViewModal
                  activeIndex={showModal}
                  incorporateFrom={incorporateFrom}
                  handleClose={(index = -1) => {
                    setShowModal(index);
                  }}
                />
              )}
            </section>
          )}
          {incorporateWith.size > 0 && (
            <section id="incorporate_category_overview" className="mb-3">
              <p className="f-14 grey_shade_4 ">Incorporate To</p>
              <div className="d-flex flex-wrap">
                {incorporateWith.map((section, i) => (
                  <Box
                    key={i}
                    className="d-flex align-items-center px-3 py-2 mr-3"
                    sx={{
                      border: `1px solid #147AFC`,
                      backgroundColor: '#EFF9FB',
                      borderRadius: '4px',
                      width: '240px',
                      marginBottom: '10px',
                    }}
                  >
                    <div className="d-flex flex-column">
                      <div className="f-14 fw_500">{section.get('formName', '')}</div>
                      <div className="f-10">
                        {combiningProgramAndInstitution(section) +
                          ' - ' +
                          section.get('calendar_name', '')}
                      </div>
                      <div className="f-10">Sections From: {section.get('sectionFrom', '')}</div>
                      <div className="f-10">
                        Sections To:{' '}
                        {section
                          .get('sectionTo', List())
                          .map((section) => section.get('sectionName', ''))
                          .join(', ')}
                      </div>
                    </div>
                  </Box>
                ))}
              </div>
            </section>
          )}

          <div className="my-2">
            <ShowCapturedDocs
              childrenType={'document'}
              showButton={false}
              showDelete={false}
              captureDocs={captureDocs}
            />
          </div>
          {approvalLevel.size > 0 && (
            <>
              <Divider className="my-3" />
              <ApproverHierarchy
                approvalLevel={approvalLevel}
                headerClass="f-20 text-blue fw_500 mb-3"
              />
            </>
          )}
        </section>
      </Paper>
    </main>
  );
}
