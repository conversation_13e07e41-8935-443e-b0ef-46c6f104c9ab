import { Checkbox, Divider, Switch, TextField } from '@mui/material';
import React, { forwardRef, useEffect, useState } from 'react';
import InfoIcon from '@mui/icons-material/Info';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { criteriaLabels } from 'Modules/GlobalConfigurationV1/components/UserActivityBoard/utils';
import CalculationLayout from './CalculationLayout';
import { useSelector } from 'react-redux';
import { selectLeaderBoardSetting } from '_reduxapi/global_configuration/v1/selectors';
function CriteriaLayout({ criteria = Map(), criteriaIndex, render, parameterIndex }, parameterRef) {
  const [state, setState] = useState(criteria);
  const leaderBoardSetting = useSelector(selectLeaderBoardSetting);

  parameterRef.current = parameterRef.current?.setIn(
    [parameterIndex, 'criteria', criteriaIndex],
    state
  );
  const CalculationComponent = CalculationLayout[criteria.get('criteriaName', '')];

  const handleInput = (key1, key2) => (e) => {
    const value = e.target.value;
    if (value !== '' && (Number(value) < 0 || isNaN(Number(value)))) return;
    setState((prev) => prev.setIn([key1, key2], value));
  };

  useEffect(() => {
    if (leaderBoardSetting.isEmpty()) return;
    setState(
      leaderBoardSetting.getIn(['parameters', parameterIndex, 'criteria', criteriaIndex], Map())
    );
  }, [render]); //eslint-disable-line
  const disabled = criteria.get('disabled', false) || !state.get('isActive', false);
  return (
    <div className="p-3 criteria_border criteria_card">
      <div className="d-flex">
        <h3>{criteriaLabels[criteria.get('criteriaName', '')]}</h3>
        <Switch
          className="ml-auto"
          checked={state.get('isActive', false)}
          onChange={(e) => setState((prev) => prev.set('isActive', e.target.checked))}
        />
      </div>
      <div className="text_underline f-14 mb-1">Calculation:</div>
      <CalculationComponent
        state={state}
        setState={setState}
        criteria={criteria}
        disabled={disabled}
        handleInput={handleInput}
      />
      <Divider className="my-2" />
      <div className="d-flex align-items-center">
        <Checkbox
          disabled={disabled}
          sx={{ padding: '9px 9px 9px 0px' }}
          checked={state.getIn(['score', 'isActive'], false)}
          onChange={(e) => setState((prev) => prev.setIn(['score', 'isActive'], e.target.checked))}
        />
        Score
        <Tooltips title="A score to be awarded on achieving the Criteria">
          <InfoIcon fontSize="small" className="leader_info_color ml-2" />
        </Tooltips>
      </div>
      <div>
        <TextField
          disabled={disabled}
          fullWidth
          type="text"
          value={state.getIn(['score', 'noOfScore'], '')}
          onChange={handleInput('score', 'noOfScore')}
        />{' '}
      </div>
      <div className="d-flex align-items-center">
        <Checkbox
          disabled={disabled}
          sx={{ padding: '9px 9px 9px 0px' }}
          checked={state.getIn(['consideration', 'isActive'], false)}
          onChange={(e) =>
            setState((prev) => prev.setIn(['consideration', 'isActive'], e.target.checked))
          }
        />
        Consideration %
        <Tooltips title="Minimum % to be met">
          <InfoIcon fontSize="small" className="leader_info_color ml-2" />
        </Tooltips>
      </div>
      <div>
        <TextField
          fullWidth
          disabled={disabled}
          type="text"
          value={state.getIn(['consideration', 'noOfConsideration'], '')}
          onChange={handleInput('consideration', 'noOfConsideration')}
        />{' '}
      </div>
      {criteria.get('criteriaName', '') === 'staff_attendance' && (
        <>
          <div className="d-flex align-items-center">
            <Checkbox
              disabled={disabled}
              sx={{ padding: '9px 9px 9px 0px' }}
              checked={state.getIn(['bufferTime', 'isActive'], false)}
              onChange={(e) =>
                setState((prev) => prev.setIn(['bufferTime', 'isActive'], e.target.checked))
              }
            />
            Buffer Time
            <Tooltips title="Grace Period for the session">
              <InfoIcon fontSize="small" className="leader_info_color ml-2" />
            </Tooltips>
          </div>
          <div>
            <TextField
              disabled={disabled}
              InputProps={{
                endAdornment: <div className="text-grey">MIN</div>,
              }}
              fullWidth
              type="text"
              value={state.getIn(['bufferTime', 'noOfBuffer'], '')}
              onChange={handleInput('bufferTime', 'noOfBuffer')}
            />{' '}
          </div>
        </>
      )}
      <div className="d-flex align-items-center">
        <Checkbox
          disabled={disabled}
          sx={{ padding: '9px 9px 9px 0px' }}
          checked={state.getIn(['allowance', 'isActive'], false)}
          onChange={(e) =>
            setState((prev) => prev.setIn(['allowance', 'isActive'], e.target.checked))
          }
        />
        Allowance %
        <Tooltips title="Exempted %">
          <InfoIcon fontSize="small" className="leader_info_color ml-2" />
        </Tooltips>
      </div>
      <div>
        <TextField
          disabled={disabled}
          fullWidth
          type="text"
          value={state.getIn(['allowance', 'noOfAllowance'], '')}
          onChange={handleInput('allowance', 'noOfAllowance')}
        />{' '}
      </div>
    </div>
  );
}
export default forwardRef(CriteriaLayout);

CriteriaLayout.propTypes = {
  criteria: PropTypes.instanceOf(Map),
  parameterIndex: PropTypes.number,
  render: PropTypes.bool,
  criteriaIndex: PropTypes.number,
};
