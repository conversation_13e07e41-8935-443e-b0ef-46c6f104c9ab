import * as React from 'react';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import Checkbox from '@mui/material/Checkbox';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import { useStylesFunction } from '../designUtils';
import { capitalize } from 'utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

export default function CheckboxesTags({
  options,
  handleChange,
  type,
  value,
  setSearchKey,
  index,
  disabled,
  activeIndex,
  name,
}) {
  CheckboxesTags.propTypes = {
    options: PropTypes.instanceOf(List),
    handleChange: PropTypes.func,
    type: PropTypes.string,
    value: PropTypes.instanceOf(List),
    setSearchKey: PropTypes.func,
    index: PropTypes.number,
    searchKey: PropTypes.string,
    disabled: PropTypes.bool,
    activeIndex: PropTypes.number,
    name: PropTypes.string,
  };

  const { autocomplete } = useStylesFunction();
  const getOption = () => {
    if (options) {
      if (type === 'user') {
        const optionList = [...value.toJS(), ...options.toJS()];
        return optionList.filter(
          (t, i) => optionList.findIndex((s) => t?.value === s?.value) === i
        );
      } else return options.toJS();
    } else return [];
  };
  return (
    <Autocomplete
      className={`${autocomplete}`}
      multiple
      id={`controllable-states-demo${activeIndex}${index}`}
      key={`${activeIndex}${index}`}
      options={getOption()}
      disableCloseOnSelect
      disableClearable
      disabled={disabled}
      getOptionLabel={(option) => option.title}
      onChange={(e, val) => handleChange(val, type, index)}
      isOptionEqualToValue={(option, value) => option?.title === value?.title}
      defaultValue={value?.toJS()}
      onInputChange={(_, newInputValue) => {
        setSearchKey && setSearchKey(newInputValue);
      }}
      renderOption={(props, option, { selected }) => {
        return (
          <li {...props}>
            <Checkbox
              icon={icon}
              checkedIcon={checkedIcon}
              style={{ marginRight: 8 }}
              checked={value.some((item) => item.get('value', '') === option.value)}
            />
            <div>
              <div>{getShortString(option.title, 20)}</div>
            </div>
          </li>
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined"
          placeholder={value.size > 0 ? '' : `Select ${capitalize(name)}`}
        />
      )}
    />
  );
}
