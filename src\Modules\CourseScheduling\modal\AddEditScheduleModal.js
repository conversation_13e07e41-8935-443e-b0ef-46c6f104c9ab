import React from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map, Set, fromJS } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { ThemeProvider } from '@mui/styles';
import DatePicker from 'react-datepicker';
import {
  startOfDay,
  format,
  isValid,
  endOfDay,
  set,
  //getDay,
  addMinutes,
  isWithinInterval,
  getHours,
  getMinutes,
  subMilliseconds,
} from 'date-fns';
import { Trans } from 'react-i18next';
import {
  MUI_THEME,
  MUI_CHECKBOX_THEME,
  capitalize,
  getModes,
  isIndGroup,
  allowedTimeInterval,
  getManualStaffOption,
  isManualAttendanceEnabled,
  studentGroupRename,
  // isModuleEnabled,
} from '../../../utils';
import {
  getFormattedGroupName,
  useMuiMultiSelectStyles,
  getHour,
  transformDateToCustomObject,
  getRotationDates,
} from '../components/utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Dialog, DialogActions } from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';

// const menuProps = {
//   getContentAnchorEl: null,
//   anchorOrigin: {
//     vertical: 'bottom',
//     horizontal: 'center',
//   },
//   transformOrigin: {
//     vertical: 'top',
//     horizontal: 'center',
//   },
//   variant: 'menu',
// };

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 200,
    },
  },
};

function AddEditScheduleModal({
  show,
  onHide,
  onSave,
  onChange,
  mode,
  data,
  course,
  activeSessionFlow,
  getScheduleAvailability,
  excludedTimes,
  studentGroupScheduleStatus,
  handleDeleteSchedule,
  autoChange,
  isRotation,
  programId,
}) {
  const sessionFlow = getActiveSessionFlow();
  const courseGroups = getCourseGroups();
  const deliveryGroups = getDeliveryGroups();
  const subjects = getSubjects();
  const staffs = getStaffs();
  const infrastructures = getInfrastructures();
  const errors = getErrors();
  const topics = getTopics();

  const manualStaffOptions = useSelector((state) =>
    state.courseScheduling.getIn(['staffScheduleOptionList', 0], Map())
  );

  function getErrors() {
    return data.get('errors', List());
  }

  function handleChange(name, value) {
    switch (name) {
      case 'schedule_date': {
        if (!value) value = '';
        onChange(name, value ? format(startOfDay(value), 'yyyy-MM-dd') : '');
        break;
      }
      case 'start':
      case 'end': {
        if (!value) {
          onChange(name, Map());
          return;
        }
        const time = transformDateToCustomObject(value);
        if (time.get('minute', NaN) % allowedTimeInterval() !== 0) return;
        let endTime = Map();
        if (name === 'start') {
          const endDate = addMinutes(value, sessionFlow.get('duration', allowedTimeInterval()));
          endTime = transformDateToCustomObject(endDate);
        }
        onChange(name, {
          [name]: time,
          ...(name === 'start' && { end: endTime }),
        });
        break;
      }
      case 'student_groups': {
        const selected = activeSessionFlow
          .get('student_group', List())
          .filter((group) => value.includes(group.get('_id')))
          .map((group) => group.set('session_group', List()));
        onChange(name, selected);
        break;
      }
      case 'session_groups': {
        onChange('student_groups', value);
        handleAutoPopulate(value);
        break;
      }
      case 'subjects': {
        onChange('staffs', List());
        const selected = activeSessionFlow
          .get('subjects', List())
          .filter((subject) => value.includes(subject.get('_subject_id')));
        onChange(name, selected);
        break;
      }
      case 'staffs': {
        const selected = staffs
          .filter((staff) => value.includes(staff.get('_staff_id')))
          .map((staff) => staff.delete('name').delete('value'));
        onChange(name, selected);
        break;
      }
      case 'mode': {
        onChange(name, value);
        break;
      }
      case '_topic_id': {
        value = Map({
          value: value,
          name: topics.filter((item) => item.value === value).reduce((_, el) => el.name, ''),
        });
        onChange(name, value);
        break;
      }
      case 'manualStaffs': {
        onChange(name, value);
        break;
      }
      case 'mobileNumberOrEmail': {
        onChange(name, value);
        break;
      }
      default:
        onChange(name, value);
        break;
    }
  }

  function handleDeliveryGroupChange(option) {
    const isStudentGroup = option.get('isStudentGroup');
    const groups = data.getIn(['schedule', 'student_groups'], List());
    const selectedStudentGroupIndex = groups.findIndex(
      (g) => g.get('_id') === option.get(isStudentGroup ? 'value' : 'studentGroupId')
    );
    if (selectedStudentGroupIndex === -1) return;
    const selectedStudentGroup = groups.get(selectedStudentGroupIndex, Map());
    if (selectedStudentGroup.isEmpty()) return;
    if (isStudentGroup) {
      const cbState = getCourseDeliveryGroupCBState(
        option.get('value'),
        option.get('totalSessionGroups')
      );
      if (cbState === 'checked') {
        handleChange(
          'session_groups',
          groups.set(selectedStudentGroupIndex, selectedStudentGroup.set('session_group', List()))
        );
      } else {
        const actualStudentGroup = activeSessionFlow
          .get('student_group', List())
          .find((g) => g.get('_id') === option.get('value'));
        if (!actualStudentGroup) return;
        handleChange('session_groups', groups.set(selectedStudentGroupIndex, actualStudentGroup));
      }
    } else {
      const actualStudentGroup = activeSessionFlow
        .get('student_group', List())
        .find((g) => g.get('_id') === option.get('studentGroupId'));
      if (!actualStudentGroup) return;
      const sessionGroup = selectedStudentGroup
        .get('session_group', List())
        .find((g) => g.get('_id') === option.get('value'));
      if (!sessionGroup) {
        const actualSessionGroup = actualStudentGroup
          .get('session_group', List())
          .find((g) => g.get('_id') === option.get('value'));
        handleChange(
          'session_groups',
          groups.set(
            selectedStudentGroupIndex,
            selectedStudentGroup.set(
              'session_group',
              selectedStudentGroup.get('session_group', List()).push(actualSessionGroup)
            )
          )
        );
      } else {
        handleChange(
          'session_groups',
          groups.set(
            selectedStudentGroupIndex,
            selectedStudentGroup.set(
              'session_group',
              selectedStudentGroup
                .get('session_group', List())
                .filter((sg) => sg.get('_id') !== option.get('value'))
            )
          )
        );
      }
    }
  }

  function getCourseDeliveryGroupCBState(studentGroupId, actualCount) {
    const selectedStudentGroup = data
      .getIn(['schedule', 'student_groups'], List())
      .find((g) => g.get('_id') === studentGroupId);
    if (!selectedStudentGroup) return 'unchecked';
    const selectedCount = selectedStudentGroup.get('session_group', List()).size;
    if (selectedCount === 0) return 'unchecked';
    if (actualCount === selectedCount) return 'checked';
    return 'indeterminate';
  }

  function getScheduleDate() {
    const scheduleDate = data.getIn(['schedule', 'schedule_date'], null) || null;
    if (!scheduleDate) return null;
    return startOfDay(new Date(scheduleDate));
  }

  function getStartEndDate(type) {
    const date = data.getIn(['schedule', type], Map());
    if (date.isEmpty()) return null;
    return set(startOfDay(new Date()), { hours: getHour(date), minutes: date.get('minute') });
  }

  function getMinMaxDate() {
    if (isRotation) return {};
    const startDate = new Date(course.get('start_date'));
    let endDate = new Date(course.get('end_date'));
    if (!isValid(startDate) || !isValid(endDate)) return {};
    endDate = endOfDay(endDate);
    return { minDate: startDate, maxDate: endDate };
  }

  function getRotationCountForSelectedDate() {
    const scheduleDate = getScheduleDate();
    if (scheduleDate === null) return null;
    const rotationDates = getRotationDates(course);
    const rotationDate = rotationDates.find((rotation) =>
      isWithinInterval(scheduleDate, {
        start: rotation.get('start_date'),
        end: rotation.get('end_date'),
      })
    );
    if (!rotationDate) return -1;
    return rotationDate.get('rotation_count', -1);
  }

  function getActiveSessionFlow() {
    const sessionFlowId = data.get('sessionFlowId');
    if (!sessionFlowId) return Map();
    return (
      course
        .get('session_flow', List())
        .find((sessionFlow) => sessionFlow.get('_id') === sessionFlowId) || Map()
    );
  }

  function getCourseGroups() {
    const studentGroups = activeSessionFlow.get('student_group', List()).map((studentGroup) =>
      Map({
        name: getFormattedGroupName(
          studentGroupRename(studentGroup.get('group_name', ''), programId),
          isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
        ),
        value: studentGroup.get('_id'),
        ...(isRotation && { groupNo: studentGroup.get('group_no') }),
      })
    );
    if (!isRotation) return studentGroups;
    const rotationCount = getRotationCountForSelectedDate();
    return studentGroups
      .filter(
        (studentGroup) => rotationCount === null || studentGroup.get('groupNo') === rotationCount
      )
      .toList();
  }

  function getDeliveryGroups() {
    const selectedStudentGroupIds = getSelectedCourseGroups();
    if (!selectedStudentGroupIds.length) return List();
    const selectedStudentGroups = activeSessionFlow
      .get('student_group', List())
      .filter((g) => selectedStudentGroupIds.includes(g.get('_id')));
    return selectedStudentGroups.reduce((acc, g) => {
      return acc.concat(
        List([
          Map({
            name: getFormattedGroupName(
              g.get('group_name', ''),
              isIndGroup(g.get('group_name', '')) ? 1 : 2
            ),
            value: g.get('_id'),
            isStudentGroup: true,
            totalSessionGroups: g.get('session_group', List()).size,
          }),
        ]).concat(
          g.get('session_group', List()).map((sessionGroup) =>
            Map({
              name: getFormattedGroupName(sessionGroup.get('group_name', ''), 3),
              value: sessionGroup.get('_id'),
              isStudentGroup: false,
              studentGroupId: g.get('_id'),
            })
          )
        )
      );
    }, List());
  }

  function getSubjects() {
    return activeSessionFlow.get('subjects', List()).map((subject) =>
      Map({
        name: subject.get('subject_name'),
        value: subject.get('_subject_id'),
      })
    );
  }

  function getStaffList() {
    const fStaff = Map();
    return activeSessionFlow
      .get('subjects', List())
      .reduce((acc, subject) => {
        return acc.concat(
          subject.get('staffs', List()).map((staff) => {
            const filteredStaff = getStaffDetail(staff);
            return fStaff.merge(
              Map({
                name: `${filteredStaff.getIn(['name', 'first'], '')} ${filteredStaff.getIn(
                  ['name', 'middle'],
                  ''
                )} ${filteredStaff.getIn(['name', 'last'], '')}`,
                value: filteredStaff.get('_id'),
                staff_name: filteredStaff.get('name'),
                _staff_id: filteredStaff.get('_id'),
              })
            );
          })
        );
      }, List())
      .reduce((acc, staff) => acc.set(staff.get('value'), staff), Map())
      .valueSeq()
      .toList();
  }

  // function getStaffs() {
  //   const selectedSubjects = data.getIn(['schedule', 'subjects'], List());
  //   if (selectedSubjects.isEmpty()) return List();
  //   return getStaffList();
  // }

  function getStaffDetail(staff) {
    const filteredStaff = activeSessionFlow
      .get('scheduleStaffs', List())
      .find((st) => st.get('_id') === staff);
    return filteredStaff !== undefined ? filteredStaff : Map();
  }

  function getInfraDetail(infra) {
    const filteredInfra = activeSessionFlow
      .get('scheduleInfra', List())
      .find((st) => st.get('_id') === infra);
    return filteredInfra !== undefined ? filteredInfra : Map();
  }

  function getStaffs() {
    const selectedSubjects = data.getIn(['schedule', 'subjects'], List());

    if (selectedSubjects.isEmpty()) return List();
    const subjects = selectedSubjects.map((item) => item.get('_subject_id')).toJS();
    return activeSessionFlow
      .get('subjects', List())
      .filter((item) => subjects.includes(item.get('_subject_id')))
      .reduce((acc, subject) => {
        const fStaff = Map();
        return acc.concat(
          subject.get('staffs', List()).map((staff) => {
            const filteredStaff = getStaffDetail(staff);
            return fStaff.merge(
              Map({
                name: `${filteredStaff.getIn(['name', 'first'], '')} ${filteredStaff.getIn(
                  ['name', 'middle'],
                  ''
                )} ${filteredStaff.getIn(['name', 'last'], '')}`,
                value: filteredStaff.get('_id'),
                staff_name: filteredStaff.get('name'),
                _staff_id: filteredStaff.get('_id'),
              })
            );
          })
        );
      }, List())
      .reduce((acc, staff) => acc.set(staff.get('value'), staff), Map())
      .valueSeq()
      .toList();
  }

  function getInfrastructuresList(mode, studentGroups, subjects) {
    const genders = Set(
      studentGroups.map((studentGroup) => studentGroup.get('gender')).filter((g) => g)
    ).toList();
    if (genders.isEmpty()) return List();
    const includeAll = genders.size !== 1;
    const gender = genders.get(0, '').toLowerCase();
    if (mode === 'remote') {
      return course
        .get('remote_scheduling', List())
        .filter((room) => {
          if (
            // room.get('yearName') === course.get('year_no') &&
            room.get('term', '').toLowerCase() === course.get('term', '').toLowerCase() &&
            room.get('levelName') === course.get('level_no')
          ) {
            return (
              room.get('gender', '').toLowerCase() === gender || room.get('gender', '') === 'both'
            );
          }
          return false;
        })
        .map((room) =>
          room.merge(
            Map({
              name: room.get('meetingTitle'),
              value: room.get('_id'),
              secondaryTitle1: `${capitalize(room.get('gender', ''))}${
                room.get('meetingUsername', '') !== null
                  ? `, ${room.get('meetingUsername', '')}`
                  : ''
              }`,
              secondaryTitle2: `${
                room.get('remotePlatform', '') === 'teams'
                  ? 'Microsoft Teams'
                  : `${room.get('meetingId', '')}, Zoom`
              }`,
            })
          )
        )
        .sort((r1, r2) => {
          const v1 = r1.get('meetingTitle', '');
          const v2 = r2.get('meetingTitle', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    } else if (mode === 'onsite') {
      //const subjects = data.getIn(['schedule', 'subjects'], List());
      if (subjects.isEmpty()) return List();
      let infrastructures = subjects.reduce(
        (acc, subject) => acc.concat(subject.get('subject_infra', List())),
        List()
      );
      if (!includeAll) {
        infrastructures = infrastructures.filter((infra) => {
          const filteredInfra = getInfraDetail(infra);
          const gendersInTimeGroups = Set(
            filteredInfra
              .get('timing', List())
              .map((t) => {
                const g = t.getIn(['_time_id', 'gender']);
                if (!g) return '';
                return g.toLowerCase();
              })
              .filter((g) => g)
          ).toList();
          if (gendersInTimeGroups.isEmpty()) return false;
          return gendersInTimeGroups.includes('both') ? true : gendersInTimeGroups.includes(gender);
        });
      }
      const startDate = getStartEndDate('start');
      let endDate = getStartEndDate('end');
      const fInfra = Map();
      return infrastructures
        .filter((infra) => {
          let incInfra = false;
          const filteredInfra = getInfraDetail(infra);
          filteredInfra.get('timing', List()).forEach((t) => {
            if (incInfra) return;
            let infraStartDate = new Date(t.getIn(['_time_id', 'start_time']));
            let infraEndDate = new Date(t.getIn(['_time_id', 'end_time']));
            if (!isValid(startDate) || !isValid(endDate)) return;
            if (!isValid(infraStartDate) || !isValid(infraEndDate)) return;
            endDate = subMilliseconds(endDate, 1);
            infraStartDate = set(startDate, {
              hours: getHours(infraStartDate),
              minutes: getMinutes(infraStartDate),
              seconds: 0,
              milliseconds: 0,
            });
            infraEndDate = subMilliseconds(
              set(endDate, {
                hours: getHours(infraEndDate),
                minutes: getMinutes(infraEndDate),
                seconds: 0,
                milliseconds: 0,
              }),
              1
            );
            incInfra =
              infraStartDate > infraEndDate
                ? true
                : isWithinInterval(startDate, { start: infraStartDate, end: infraEndDate }) &&
                  isWithinInterval(endDate, { start: infraStartDate, end: infraEndDate });
          });
          return incInfra;
        })
        .map((infra) => {
          const filteredInfra = getInfraDetail(infra);
          return fInfra.merge(
            Map({
              _id: filteredInfra.get('_id', ''),
              name: filteredInfra.get('name', ''),
              value: filteredInfra.get('_id'),
              secondaryTitle1: `${filteredInfra.get('floor_no', '')}, ${
                filteredInfra.get('zone', List()).join(', ') || 'NA'
              }, ${filteredInfra.get('room_no', '')}`,
              secondaryTitle2: `${filteredInfra.get('building_name', '')}`,
            })
          );
        })
        .reduce((acc, infra) => acc.set(infra.get('_id'), infra), Map())
        .valueSeq()
        .toList()
        .sort((i1, i2) => {
          const v1 = i1.get('name', '');
          const v2 = i2.get('name', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    }
    return List();
  }

  function getInfrastructures() {
    const mode = data.getIn(['schedule', 'mode'], '');
    if (!mode) return List();
    const genders = Set(
      data
        .getIn(['schedule', 'student_groups'], List())
        .map((studentGroup) => studentGroup.get('gender'))
        .filter((g) => g)
    ).toList();
    if (genders.isEmpty()) return List();
    const includeAll = genders.size !== 1;
    const gender = genders.get(0, '').toLowerCase();
    if (mode === 'remote') {
      return course
        .get('remote_scheduling', List())
        .filter((room) => {
          if (
            // room.get('yearName') === course.get('year_no') &&
            room.get('term', '').toLowerCase() === course.get('term', '').toLowerCase() &&
            room.get('levelName') === course.get('level_no')
          ) {
            return (
              room.get('gender', '').toLowerCase() === gender || room.get('gender', '') === 'both'
            );
          }
          return false;
        })
        .map((room) =>
          room.merge(
            Map({
              name: room.get('meetingTitle'),
              value: room.get('_id'),
              secondaryTitle1: `${capitalize(room.get('gender', ''))}${
                room.get('meetingUsername', '') !== null
                  ? `, ${room.get('meetingUsername', '')}`
                  : ''
              }`,
              secondaryTitle2: `${
                room.get('remotePlatform', '') === 'teams'
                  ? 'Microsoft Teams'
                  : `${room.get('meetingId', '')}, Zoom`
              }`,
            })
          )
        )
        .sort((r1, r2) => {
          const v1 = r1.get('meetingTitle', '');
          const v2 = r2.get('meetingTitle', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    } else if (mode === 'onsite') {
      const subjects = data.getIn(['schedule', 'subjects'], List());
      if (subjects.isEmpty()) return List();
      let infrastructures = subjects.reduce(
        (acc, subject) => acc.concat(subject.get('subject_infra', List())),
        List()
      );
      if (!includeAll) {
        infrastructures = infrastructures.filter((infra) => {
          const filteredInfra = getInfraDetail(infra);
          const gendersInTimeGroups = Set(
            filteredInfra
              .get('timing', List())
              .map((t) => {
                const g = t.getIn(['_time_id', 'gender']);
                if (!g) return '';
                return g.toLowerCase();
              })
              .filter((g) => g)
          ).toList();
          if (gendersInTimeGroups.isEmpty()) return false;
          return gendersInTimeGroups.includes('both') ? true : gendersInTimeGroups.includes(gender);
        });
      }
      const startDate = getStartEndDate('start');
      let endDate = getStartEndDate('end');
      const fInfra = Map();
      return infrastructures
        .filter((infra) => {
          let incInfra = false;
          const filteredInfra = getInfraDetail(infra);
          filteredInfra.get('timing', List()).forEach((t) => {
            if (incInfra) return;
            let infraStartDate = new Date(t.getIn(['_time_id', 'start_time']));
            let infraEndDate = new Date(t.getIn(['_time_id', 'end_time']));
            if (!isValid(startDate) || !isValid(endDate)) return;
            if (!isValid(infraStartDate) || !isValid(infraEndDate)) return;
            endDate = subMilliseconds(endDate, 1);
            infraStartDate = set(startDate, {
              hours: getHours(infraStartDate),
              minutes: getMinutes(infraStartDate),
              seconds: 0,
              milliseconds: 0,
            });
            infraEndDate = subMilliseconds(
              set(endDate, {
                hours: getHours(infraEndDate),
                minutes: getMinutes(infraEndDate),
                seconds: 0,
                milliseconds: 0,
              }),
              1
            );
            incInfra =
              infraStartDate > infraEndDate
                ? true
                : isWithinInterval(startDate, { start: infraStartDate, end: infraEndDate }) &&
                  isWithinInterval(endDate, { start: infraStartDate, end: infraEndDate });
          });
          return incInfra;
        })
        .map((infra) => {
          const filteredInfra = getInfraDetail(infra);
          return fInfra.merge(
            Map({
              _id: filteredInfra.get('_id', ''),
              name: filteredInfra.get('name', ''),
              value: filteredInfra.get('_id'),
              secondaryTitle1: `${filteredInfra.get('floor_no', '')}, ${
                filteredInfra.get('zone', List()).join(', ') || 'NA'
              }, ${filteredInfra.get('room_no', '')}`,
              secondaryTitle2: `${filteredInfra.get('building_name', '')}`,
              outsideCampus: filteredInfra.get('outsideCampus', false),
            })
          );
        })
        .reduce((acc, infra) => acc.set(infra.get('_id'), infra), Map())
        .valueSeq()
        .toList()
        .sort((i1, i2) => {
          const v1 = i1.get('name', '');
          const v2 = i2.get('name', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    }
    return List();
  }

  function getSelectedCourseGroups() {
    return data
      .getIn(['schedule', 'student_groups'], List())
      .map((studentGroup) => studentGroup.get('_id'))
      .toJS();
  }

  function getSelectedDeliveryGroups() {
    return data
      .getIn(['schedule', 'student_groups'], List())
      .reduce(
        (acc, studentGroup) =>
          acc.concat(studentGroup.get('session_group', List()).map((g) => g.get('_id'))),
        List()
      )
      .toJS();
  }

  function getSelectedSubjects() {
    return data
      .getIn(['schedule', 'subjects'], List())
      .map((subject) => subject.get('_subject_id'))
      .toJS();
  }

  function getSelectedStaffs() {
    return data
      .getIn(['schedule', 'staffs'], List())
      .map((subject) => subject.get('_staff_id'))
      .toJS();
  }

  function getSelectedInfrastructure() {
    return data.getIn(['schedule', '_infra_id'], '');
  }

  const selectedCourseGroups = getSelectedCourseGroups();
  const selectedDeliveryGroups = getSelectedDeliveryGroups();
  const selectedSubjects = getSelectedSubjects();
  const selectedStaffs = getSelectedStaffs();
  const transformedErrors = errors.reduce(
    (acc, err) => acc.set(err.get('field', ''), err.get('message', '')),
    Map()
  );

  function handleAutoPopulate(studentGroups) {
    const studentGroupIds = studentGroups.map((item) => item.get('_id')).toJS();
    let sessionGroupIds = [];
    if (studentGroups.size > 0) {
      sessionGroupIds = studentGroups
        .reduce((acc, c) => {
          const sessionGroups = c.get('session_group', List());
          if (sessionGroups.isEmpty()) {
            return acc;
          }
          return acc.concat(
            sessionGroups.reduce((acc1, a) => {
              return acc1.push(a.get('_id'));
            }, List())
          );
        }, List())
        .toJS();
    }
    let filterSGManageCourse = [];
    activeSessionFlow.get('manage_course', List()).map((acc) => {
      const matchedStudentGroups = acc
        .get('student_groups', List())
        .filter((acc1) => studentGroupIds.includes(acc1.get('group_id')));
      const matchedSessionGroups = matchedStudentGroups
        .getIn([0, 'session_group'], List())
        .filter((item) => sessionGroupIds.includes(item.get('session_group_id')));

      if (matchedSessionGroups.size > 0) {
        filterSGManageCourse.push(acc);
      }
      return acc;
    }, List());

    const filterStManageCourseList = fromJS(filterSGManageCourse);
    if (
      filterStManageCourseList !== undefined &&
      filterStManageCourseList.size === 1 &&
      sessionGroupIds.length > 0
    ) {
      const mode = filterStManageCourseList.getIn([0, 'mode'], '');
      const subjectId = filterStManageCourseList.getIn([0, 'subjects', 0, '_subject_id'], '');
      const subjects = activeSessionFlow
        .get('subjects', List())
        .filter((item) => item.get('_subject_id') === subjectId);

      const staffId = filterStManageCourseList
        .getIn([0, 'staffs'], List())
        .map((item) => item.get('_staff_id', ''));
      const staffList = getStaffList()
        .filter((staff) => staffId.includes(staff.get('_staff_id')))
        .map((staff) => staff.delete('name').delete('value'));
      let _infra_id = filterStManageCourseList.getIn([0, '_infra_id']);
      let infra_name = filterStManageCourseList.getIn([0, 'infra_name']);
      let remotePlatform = '';
      const infraLists = getInfrastructuresList(mode, studentGroups, subjects).filter(
        (item) => item.get('_id') === _infra_id
      );
      if (infraLists.size === 0) {
        _infra_id = '';
        infra_name = '';
      } else {
        remotePlatform =
          infraLists.getIn([0, 'remotePlatform'], '') !== null
            ? infraLists.getIn([0, 'remotePlatform'], '')
            : 'zoom';
      }

      setTimeout(
        () =>
          setAutoPopulateData(
            mode,
            studentGroups,
            subjects,
            staffList,
            _infra_id,
            infra_name,
            remotePlatform
          ),
        500
      );
    } else {
      setTimeout(() => setAutoPopulateData('', studentGroups, List(), List(), '', '', ''), 500);
    }
  }

  function setAutoPopulateData(
    mode,
    studentGroups,
    subjects,
    staffs,
    _infra_id,
    infra_name,
    remotePlatform
  ) {
    autoChange(mode, studentGroups, subjects, staffs, _infra_id, infra_name, remotePlatform);
  }

  const classes = useMuiMultiSelectStyles();
  function getTopics() {
    const deliveryType = sessionFlow.get('delivery_type', '');
    const topics = course
      .get('topics', List())
      .filter((item) => {
        const deliveryTypeDetails = item
          .get('delivery_type_details', List())
          .map((dItem) => dItem.get('delivery_type', ''))
          .toJS();
        return item.get('isActive', false) === true && deliveryTypeDetails.includes(deliveryType);
      })
      .reduce((_, el) => el.get('topics', List()), List());
    let topic = [];
    if (topics && topics.size > 0) {
      topic = topics.toJS().map((topic) => {
        return { name: capitalize(topic.title), value: topic._id };
      });
    }
    return topic;
  }

  function isSameRotationGroup(groupNo) {
    if (!isRotation) return false;
    const rotationCount = data.getIn(['schedule', 'rotation_count']);
    if (rotationCount === null) return false;
    return rotationCount !== groupNo;
  }

  // const programType = [
  //   {
  //     name: 'Mobile Number *',
  //     value: 'Mobile Number',
  //   },
  //   {
  //     name: 'Email *',
  //     value: 'Email',
  //   },
  // ];

  // const withoutOutlineSelect = {
  //   '& .MuiOutlinedInput-input': {
  //     padding: '0px',
  //   },
  //   '&.MuiOutlinedInput-root': {
  //     width: '10em',
  //     '& fieldset': {
  //       border: 'none',
  //       color: 'red',
  //     },
  //     '&:hover fieldset': {
  //       border: 'none',
  //     },
  //     '&.Mui-focused fieldset': {
  //       border: 'none',
  //     },
  //   },
  // };

  return (
    <Dialog fullWidth={true} maxWidth={'md'} open={show} onClose={onHide}>
      <div className="p-3">
        <div className="border-bottom mb-3">
          <div className=" row pt-1 mb-2">
            <div className={`col-md-${topics && topics.length > 0 ? '8' : '12'}`}>
              <Trans i18nKey={'delivery_session'}></Trans> -{' '}
              <b>
                {`${sessionFlow.get('delivery_symbol', '')}${sessionFlow.get(
                  'delivery_no',
                  ''
                )} - ${sessionFlow.get('delivery_topic', '')}`}
              </b>
            </div>
            {topics && topics.length > 0 && (
              <div className="col-md-4">
                <div className="row pb-2">
                  <div className="col-md-4">
                    <p className="f-16 pt-2">
                      <Trans i18nKey={'topic'}></Trans> *
                    </p>
                  </div>
                  <div className="col-md-8">
                    <FormControl fullWidth variant="outlined" size="small">
                      <Select
                        native
                        value={data.getIn(['schedule', '_topic_id'], '')}
                        onChange={(e) => handleChange('_topic_id', e.target.value)}
                      >
                        <option value=""></option>
                        {topics.map((option) => (
                          <option key={option.name} value={option.value}>
                            {option.name}
                          </option>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="row">
          <div className="col-md-6">
            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'Date'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <div className="schedule-date-picker-container">
                  <DatePicker
                    onChange={(date) => handleChange('schedule_date', date)}
                    selected={getScheduleDate()}
                    className="schedule-date-picker-input"
                    dateFormat="dd/MM/yyyy"
                    // filterDate={(date) => {
                    //   const day = getDay(date);
                    //   return day;
                    //   //return ![5, 6].includes(day);
                    // }}
                    {...getMinMaxDate()}
                  />
                </div>
              </div>
            </div>

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'course_group'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={transformedErrors.get('course_group', '').length !== 0}
                >
                  <Select
                    labelId="demo-multiple-checkbox-label"
                    id="demo-multiple-checkbox"
                    MenuProps={MenuProps}
                    multiple
                    value={selectedCourseGroups}
                    onChange={(e) => handleChange('student_groups', e.target.value)}
                    // MenuProps={{
                    //   ...menuProps,
                    //   PaperProps: { style: { maxHeight: 200 } },
                    // }}
                    renderValue={(selected) =>
                      courseGroups
                        .reduce((acc, group) => {
                          if (!selected.includes(group.get('value'))) return acc;
                          return acc.push(group.get('name'));
                        }, List())
                        .join(', ')
                    }
                  >
                    {courseGroups.map((option) => (
                      <MenuItem
                        className="white-space-normal"
                        key={option.get('value')}
                        value={option.get('value')}
                        disabled={
                          isSameRotationGroup(option.get('groupNo')) ||
                          studentGroupScheduleStatus.getIn([option.get('value'), 'disabled'], false)
                        }
                      >
                        <ListItemIcon>
                          <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                            <Checkbox
                              color="primary"
                              checked={selectedCourseGroups.indexOf(option.get('value')) > -1}
                              size="small"
                            />
                          </ThemeProvider>
                        </ListItemIcon>
                        <ListItemText primary={option.get('name')} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'configuration.subject'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl fullWidth variant="outlined" size="small">
                  <Select
                    labelId="demo-multiple-checkbox-label"
                    id="demo-multiple-checkbox"
                    MenuProps={MenuProps}
                    multiple
                    value={selectedSubjects}
                    onChange={(e) => handleChange('subjects', e.target.value)}
                    // MenuProps={{
                    //   ...menuProps,
                    //   PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                    // }}
                    renderValue={(selected) =>
                      subjects
                        .reduce((acc, group) => {
                          if (!selected.includes(group.get('value'))) return acc;
                          return acc.push(group.get('name'));
                        }, List())
                        .join(', ')
                    }
                  >
                    {subjects.map((option) => (
                      <MenuItem
                        className="white-space-normal"
                        key={option.get('value')}
                        value={option.get('value')}
                      >
                        <ListItemIcon>
                          <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                            <Checkbox
                              color="primary"
                              checked={selectedSubjects.indexOf(option.get('value')) > -1}
                              size="small"
                            />
                          </ThemeProvider>
                        </ListItemIcon>
                        <ListItemText primary={option.get('name')} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>
            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'user_management.staff'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={transformedErrors.get('staff', '').length !== 0}
                >
                  <Select
                    labelId="demo-multiple-checkbox-label"
                    id="demo-multiple-checkbox"
                    MenuProps={MenuProps}
                    multiple
                    value={selectedStaffs}
                    onChange={(e) => handleChange('staffs', e.target.value)}
                    // MenuProps={{
                    //   ...menuProps,
                    //   PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                    // }}
                    renderValue={(selected) => {
                      return staffs
                        .reduce((acc, group) => {
                          if (!selected.includes(group.get('value'))) return acc;
                          return acc.push(group.get('name'));
                        }, List())
                        .join(', ');
                    }}
                  >
                    {staffs
                      .sort((a, b) => (a.get('name', '') > b.get('name', '') ? 1 : -1))
                      .map((option) => (
                        <MenuItem
                          className="white-space-normal"
                          key={option.get('value')}
                          value={option.get('value')}
                        >
                          <ListItemIcon>
                            <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                              <Checkbox
                                color="primary"
                                checked={selectedStaffs.indexOf(option.get('value')) > -1}
                                size="small"
                              />
                            </ThemeProvider>
                          </ListItemIcon>
                          <ListItemText primary={option.get('name')} />
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </div>
            </div>
            {isManualAttendanceEnabled() &&
              data.getIn(['schedule', 'mode'], '') !== 'remote' &&
              manualStaffOptions.get('manualAttendance', false) &&
              manualStaffOptions.get('assignedStaffIds', List()).size > 0 && (
                <div className="row align-items-center pt-2 pb-2">
                  <div className="col-md-5">
                    <div className="f-16">Attendance taking Staff</div>
                  </div>
                  <div className="col-md-7">
                    <MaterialInput
                      elementType={'materialSelectMultiple'}
                      disabled={mode === 'view'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{
                        options: getManualStaffOption(
                          manualStaffOptions.get('assignedStaffIds', List())
                        ).toJS(),
                      }}
                      label={''}
                      labelclass={'mb-0 f-14'}
                      changed={(e) => handleChange('manualStaffs', e.target.value)}
                      isAllSelected={false}
                      value={data.getIn(['schedule', 'manualStaffs'], List()).toJS()}
                      multiple={true}
                    />
                  </div>
                </div>
              )}
            {/* {isModuleEnabled('OUTSIDE_CAMPUS') && (
              <Fragment>
                {data.getIn(['schedule', 'outsideCampus'], false) ? (
                  <div className="row align-items-center pt-2 pb-2">
                    <div className="col-md-5">
                      <div className="f-16">
                        <MaterialInput
                          elementType={'materialSelect'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          sx={withoutOutlineSelect}
                          elementConfig={{ options: programType }}
                          changed={(e) => handleChange('selectNumOrEmail', e.target.value)}
                          value={data.getIn(['schedule', 'selectNumOrEmail'], '')}
                        />
                      </div>
                    </div>
                    <div className="col-md-7">
                      <MaterialInput
                        elementType={'materialInput'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        changed={(e) => handleChange('mobileNumberOrEmail', e.target.value)}
                        value={data.getIn(['schedule', 'mobileNumberOrEmail'], '')}
                      />
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </Fragment>
            )} */}
          </div>

          <div className="col-md-6">
            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'dashboard_view.time'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <div className="row">
                  <div className="col-md-6 pr-1">
                    <div className="schedule-date-picker-container">
                      <DatePicker
                        onChange={(date) => handleChange('start', date)}
                        selected={getStartEndDate('start')}
                        className="schedule-date-picker-input"
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={allowedTimeInterval()}
                        timeCaption="Start time"
                        dateFormat="h:mm aa"
                        excludeTimes={excludedTimes}
                      />
                    </div>
                  </div>

                  <div className="col-md-6 pl-1">
                    <div className="schedule-date-picker-container">
                      <DatePicker
                        onChange={(date) => handleChange('end', date)}
                        onCalendarClose={getScheduleAvailability}
                        selected={getStartEndDate('end')}
                        className="schedule-date-picker-input"
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={allowedTimeInterval()}
                        timeCaption="End time"
                        dateFormat="h:mm aa"
                        excludeTimes={excludedTimes}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'delivery_group'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={transformedErrors.get('session_group', '').length !== 0}
                >
                  <Select
                    labelId="demo-multiple-checkbox-label"
                    id="demo-multiple-checkbox"
                    MenuProps={MenuProps}
                    multiple
                    value={selectedDeliveryGroups}
                    onChange={() => {}}
                    // MenuProps={{
                    //   ...menuProps,
                    //   PaperProps: { style: { maxHeight: 200 } },
                    // }}
                    renderValue={(selected) =>
                      deliveryGroups
                        .reduce((acc, group) => {
                          if (!selected.includes(group.get('value')) || group.get('isStudentGroup'))
                            return acc;
                          return acc.push(group.get('name'));
                        }, List())
                        .join(', ')
                    }
                    displayEmpty
                  >
                    {deliveryGroups.map((option) => {
                      const isStudentGroup = option.get('isStudentGroup');
                      const checked = isStudentGroup
                        ? getCourseDeliveryGroupCBState(
                            option.get('value'),
                            option.get('totalSessionGroups')
                          ) === 'checked'
                        : selectedDeliveryGroups.includes(option.get('value'));
                      return (
                        <MenuItem
                          key={option.get('value')}
                          value={option.get('value')}
                          disableGutters
                          className={`${classes[isStudentGroup ? 'single' : 'nested']} ${
                            isStudentGroup && checked ? classes.selectedAll : ''
                          }`}
                          onClick={() => handleDeliveryGroupChange(option)}
                          disabled={studentGroupScheduleStatus.getIn(
                            [
                              option.get(isStudentGroup ? 'value' : 'studentGroupId'),
                              isStudentGroup ? 'deliveryGroupParentDisabled' : option.get('value'),
                            ],
                            false
                          )}
                        >
                          <ListItemIcon className={classes.listItemIcon}>
                            <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                              <Checkbox
                                size="small"
                                color="primary"
                                checked={checked}
                                indeterminate={
                                  isStudentGroup
                                    ? getCourseDeliveryGroupCBState(
                                        option.get('value'),
                                        option.get('totalSessionGroups')
                                      ) === 'indeterminate'
                                    : false
                                }
                              />
                            </ThemeProvider>
                          </ListItemIcon>
                          <ListItemText
                            primary={studentGroupRename(option.get('name'), programId)}
                            classes={{
                              primary: isStudentGroup ? classes.singleMenuItemLabel : '',
                            }}
                          />
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </div>
            </div>
            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'configure_levels.mode'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl fullWidth variant="outlined" size="small">
                  <Select
                    native
                    value={data.getIn(['schedule', 'mode'], '')}
                    onChange={(e) => handleChange('mode', e.target.value)}
                  >
                    <option value=""></option>
                    {getModes(programId).map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.name}
                      </option>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'infrastructure'}></Trans>{' '}
                  {data.getIn(['schedule', 'mode'], '') === 'remote' && '*'}
                </div>
              </div>
              <div className="col-md-7">
                <FormControl
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={transformedErrors.get('infra', '').length !== 0}
                >
                  <Select
                    value={infrastructures.isEmpty() ? '' : getSelectedInfrastructure()}
                    onChange={(e) => {}}
                    // MenuProps={{
                    //   ...menuProps,
                    //   PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                    // }}
                    MenuProps={MenuProps}
                    displayEmpty
                    renderValue={(value) => {
                      const infra =
                        infrastructures.find((infra) => infra.get('value') === value) || Map();
                      return infra.get('name', '');
                    }}
                  >
                    {!infrastructures.isEmpty() && (
                      <MenuItem value="" onClick={() => handleChange('infra', Map())}>
                        <ListItemText primary="Unselect" />
                      </MenuItem>
                    )}
                    {infrastructures.map((option) => (
                      <MenuItem
                        key={option.get('value')}
                        value={option.get('value')}
                        className="white-space-normal"
                        onClick={() => handleChange('infra', option)}
                      >
                        <ListItemText
                          primary={option.get('name')}
                          secondary={
                            <span className="d-block">
                              <span className="d-block">{option.get('secondaryTitle1', '')}</span>
                              <span className="d-block">{option.get('secondaryTitle2', '')}</span>
                            </span>
                          }
                        />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>
          </div>
        </div>

        {!errors.isEmpty() && (
          <div className="color-red f-14 mt-2 mb-2">
            <div>
              <Trans i18nKey={'note'}></Trans>:-
            </div>
            {errors.map((err, i) => (
              <div key={`${err.get('field')}-${i}`}>{`- ${studentGroupRename(
                err.get('message', ''),
                programId
              )}`}</div>
            ))}
          </div>
        )}

        <DialogActions className="mt-3">
          <div className="d-flex justify-content-end">
            <ThemeProvider theme={MUI_THEME}>
              {mode === 'update' &&
                CheckPermission(
                  'subTabs',
                  'Schedule Management',
                  'Course Scheduling',
                  '',
                  'Schedule',
                  '',
                  'Course Schedule',
                  'Delete'
                ) && (
                  <div className="mr-2" style={{ position: 'absolute', left: '15px' }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      className="text-uppercase"
                      onClick={handleDeleteSchedule}
                    >
                      <Trans i18nKey={'delete'}></Trans>
                    </Button>
                  </div>
                )}

              <div className="mr-2">
                <Button
                  variant="outlined"
                  color="primary"
                  className="text-uppercase"
                  onClick={onHide}
                >
                  <Trans i18nKey={'cancel'}></Trans>
                </Button>
              </div>
              <div>
                <Button
                  variant="contained"
                  color="primary"
                  className="text-uppercase"
                  onClick={() => onSave(mode, '', topics)}
                >
                  <Trans i18nKey={'schedule'}></Trans>
                </Button>
              </div>
            </ThemeProvider>
          </div>
        </DialogActions>
      </div>
    </Dialog>
  );
}

AddEditScheduleModal.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  onChange: PropTypes.func,
  onSave: PropTypes.func,
  mode: PropTypes.string,
  data: PropTypes.instanceOf(Map),
  course: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  activeSessionFlow: PropTypes.instanceOf(Map),
  getScheduleAvailability: PropTypes.func,
  excludedTimes: PropTypes.array,
  studentGroupScheduleStatus: PropTypes.instanceOf(Map),
  handleDeleteSchedule: PropTypes.func,
  autoChange: PropTypes.func,
  isRotation: PropTypes.bool,
  programId: PropTypes.string,
};

export default AddEditScheduleModal;
