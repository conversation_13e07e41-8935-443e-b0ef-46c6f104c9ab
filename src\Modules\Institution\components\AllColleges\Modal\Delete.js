import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';

import ArchiveImg from '../../../../../Assets/archive.png';
import MButton from 'Widgets/FormElements/material/Button';

function Restore({ show, setShow, setArchiveCollege, title, handleDelete, deleteItem }) {
  return (
    <Modal show={show} onHide={() => setShow(false)} centered>
      <Modal.Body className="pt-0 pb-0">
        <p className="mb-3 mt-3 f-22">
          <img className="mr-2" alt={'Archive'} src={ArchiveImg} />
          {title === 'description' ? (
            <Trans i18nKey={`add_colleges.${deleteItem}`}></Trans>
          ) : (
            <Trans i18nKey={'add_colleges.delete_college'}></Trans>
          )}
        </p>
        <div>
          {' '}
          {title === 'description' ? (
            <Trans i18nKey={`add_colleges.desc.${deleteItem}`}></Trans>
          ) : (
            <Trans i18nKey={'add_colleges.are_you_sure_want_delete'}></Trans>
          )}
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <MButton className="mr-3" color="inherit" variant="outlined" clicked={() => setShow(false)}>
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>

        <MButton
          color="red"
          clicked={() => {
            if (title === 'description') {
              handleDelete();
            } else {
              setArchiveCollege('delete');
              setShow(false);
            }
          }}
        >
          <Trans i18nKey={'program_input.delete'}></Trans>
        </MButton>
      </Modal.Footer>
    </Modal>
  );
}

Restore.propTypes = {
  show: PropTypes.bool,
  setShow: PropTypes.func,
  setArchiveCollege: PropTypes.func,
  title: PropTypes.string,
  handleDelete: PropTypes.func,
  deleteItem: PropTypes.string,
};
export default Restore;
