import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Button, Modal } from 'react-bootstrap';
import * as actions from '../../../_reduxapi/course_scheduling/action';
import { t } from 'i18next';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';

function SyncModal({ course, programId, activeInstitutionCalendar, syncModalData, callBackFn }) {
  const [open, setOpen] = useState(false);
  const handleSubmit = () => {
    const data = {
      institutionCalendarId: activeInstitutionCalendar.get('_id'),
      programId: programId,
      courseId: course.get('_course_id'),
      yearNo: course.get('year_no'),
      levelNo: course.get('level_no'),
      term: course.get('term'),
      dbUpdate: true,
    };
    syncModalData(data, () => {
      setOpen(false);
      callBackFn();
    });
  };

  return (
    <React.Fragment>
      <b className="pr-2" style={{ position: 'relative', right: '145px', top: '38px' }}>
        <Button variant={'primary'} onClick={() => setOpen(true)}>
          SYNC DATA
        </Button>
      </b>
      <React.Fragment>
        {open && (
          <Modal centered show={true} onHide={() => setOpen(false)}>
            <Modal.Body>
              <div className="row">
                <div className="col-md-12">
                  <p className="f-20 mb-2">Confirm Sync Data</p>
                  <p className="f-14 mb-1 light-gray ">
                    Are you sure you want to sync third party data?
                  </p>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer>
              <b className="pr-3">
                <Button variant="outline-primary" onClick={() => setOpen(false)}>
                  {t('student_grouping.cancel')}
                </Button>
              </b>

              <b className="pr-2">
                <Button variant={'primary'} onClick={handleSubmit}>
                  {t('student_grouping.confirm')}
                </Button>
              </b>
            </Modal.Footer>
          </Modal>
        )}
      </React.Fragment>
    </React.Fragment>
  );
}

SyncModal.propTypes = {
  course: PropTypes.instanceOf(Map),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  syncModalData: PropTypes.func,
  callBackFn: PropTypes.func,
  programId: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, actions)(SyncModal);
