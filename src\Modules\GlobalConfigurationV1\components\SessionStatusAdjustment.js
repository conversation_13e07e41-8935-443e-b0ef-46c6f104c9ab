import { Paper } from '@mui/material';
import React, { useEffect, useState } from 'react';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import { record_session_status } from 'Modules/GlobalConfiguration/utils';
import MButton from 'Widgets/FormElements/material/Button';
import Switch from '@mui/material/Switch';
import { Map } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import isEqual from 'lodash/fp/isEqual';
import {
  setSessionStatusDetails,
  getSessionStatusDetails,
} from '_reduxapi/global_configuration/v1/actions';
import {
  selectIsLoading,
  selectSessionStatusDetails,
} from '_reduxapi/global_configuration/v1/selectors';

export default function SessionStatusAdjustment() {
  const [state, setState] = useState(Map());
  const sessionStatusDetails = useSelector(selectSessionStatusDetails);
  const dispatch = useDispatch();
  const isLoading = useSelector(selectIsLoading);
  useEffect(() => {
    dispatch(getSessionStatusDetails());
  }, []); //eslint-disable-line

  useEffect(() => {
    setState(sessionStatusDetails);
  }, [sessionStatusDetails]); //eslint-disable-line

  const isChangesHappened = !isEqual(state.toJS(), sessionStatusDetails.toJS());

  const handleSave = () => {
    dispatch(setSessionStatusDetails(state));
  };
  return (
    <div className="m-3">
      <div className="d-flex mb-3">
        <div>
          <span className="f-16 digi-gray-total">Institutional Session Status Manager </span>
          <span className="f-19 bold">/ Configure global settings </span>
        </div>
        <div className="d-flex ml-auto">
          <MButton
            disabled={!isChangesHappened}
            variant="outlined"
            clicked={() => setState(sessionStatusDetails)}
          >
            Cancel
          </MButton>

          <MButton
            color="blue"
            className="ml-3"
            clicked={handleSave}
            disabled={isLoading || !isChangesHappened}
          >
            Save
          </MButton>
        </div>
      </div>

      <Paper className="p-3">
        <div className="f-19 bold mb-2 d-flex">
          <div> Configure global settings for automatic session status adjustments.</div>
          <Switch
            className="ml-auto"
            checked={state.get('isEnabled', false)}
            onChange={(e) => setState((prev) => prev.set('isEnabled', e.target?.checked))}
          />
        </div>
        <div>Record the session status for the previously completed session :</div>
        <div className="f-14 digi-gray-neutral">
          The system will automatically adjust the status according to these settings.
        </div>
        {state.get('isEnabled', false) && (
          <>
            <div className="row pt-2">
              <div className="col-6">
                <div>Active To Inactive</div>
                <div className="text-lightwhite f-12 mb-1">Select Status</div>{' '}
                <TextField
                  fullWidth
                  id="outlined-select-currency"
                  select
                  onChange={(e) =>
                    setState((prev) => prev.set('activeToInactive', e.target?.value))
                  }
                  value={state.get('activeToInactive', '')}
                >
                  {record_session_status.map((option) => (
                    <MenuItem
                      key={option.value}
                      value={option.value}
                      selected={option.value === state.get('activeToInactive', '')}
                    >
                      {option.name}
                    </MenuItem>
                  ))}
                </TextField>
              </div>
              <div className="col-6">
                <div>Inactive To Active</div>
                <div className="text-light-grey f-12 mb-1">Select Status</div>{' '}
                <TextField
                  fullWidth
                  id="outlined-select-currency"
                  select
                  value={state.get('inactiveToActive')}
                  onChange={(e) =>
                    setState((prev) => prev.set('inactiveToActive', e.target?.value))
                  }
                >
                  {record_session_status.map((option) => (
                    <MenuItem
                      key={option.value}
                      value={option.value}
                      selected={option.value === state.get('inactiveToActive')}
                    >
                      {option.name}
                    </MenuItem>
                  ))}
                </TextField>
              </div>
            </div>
            <div className="pt-3 text-light-grey f-14">
              <span className="bold">For example :</span> After 10 days of a student being inactive,
              when the admin changes their status back to active, the system adjusts the session
              records for the inactive period based on the chosen settings.
            </div>
          </>
        )}
      </Paper>
    </div>
  );
}
