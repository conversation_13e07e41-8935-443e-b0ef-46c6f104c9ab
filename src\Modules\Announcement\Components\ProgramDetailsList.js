import React, { useEffect } from 'react';
import { capitalize, getVersionName } from 'utils';
import { List, Map, fromJS } from 'immutable';
import PropTypes from 'prop-types';
import AccordionTreeView from './AccordionTreeView';

const ProgramDetailsList = ({
  userProgramDetails,
  setUserProgramDetails,
  userIndex,
  programDetails,
  programId,
  checkAllData,
  selectedCourses,
  handleIsChecked,
  setSelectedCourse,
}) => {
  const programIndex = userProgramDetails
    .getIn([userIndex, 'programList'], List())
    .findIndex((item) => item.get('_id', '') === programId);

  const handleChangeProgram = ({
    event,
    type,
    institutionIndex,
    termIndex,
    curriculumIndex,
    yearLevelIndex,
    rotationIndex,
    rotationCourseIndex,
    regularSizeIndex,
    regularCourseIndex,
    name = 'isOpen',
  }) => {
    setUserProgramDetails((prevState) => {
      const programPath = [
        userIndex,
        'programList',
        programIndex,
        'programDetail',
        institutionIndex,
      ];
      let nextState = prevState;
      switch (type) {
        case 'institution': {
          nextState = nextState.updateIn([...programPath, name], (isOpen) =>
            name === 'isOpen' ? !isOpen : event.target.checked
          );

          if (name !== 'isOpen') {
            nextState = nextState
              .setIn(
                [...programPath, 'term'],
                nextState.getIn([...programPath, 'term'], List()).map((s) =>
                  s.set('status', event.target.checked).set(
                    'curriculum',
                    s.get('curriculum', List()).map((r) =>
                      r.set('status', event.target.checked).set(
                        'yearLevel',
                        r.get('yearLevel', List()).map((e) =>
                          e.set('status', event.target.checked).set(
                            e.get('rotation', '') === 'yes' ? 'rotation_course' : 'regularCourse',
                            e
                              .get(
                                e.get('rotation', '') === 'yes'
                                  ? 'rotation_course'
                                  : 'regularCourse',
                                List()
                              )
                              .map((d) =>
                                d.set('status', event.target.checked).set(
                                  'course',
                                  d
                                    .get('course', List())
                                    .map((t) => t.set('status', event.target.checked))
                                )
                              )
                          )
                        )
                      )
                    )
                  )
                )
              )

              .setIn(
                [userIndex, 'programList', programIndex, 'status'],
                nextState
                  .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
                  .every((s) => s.get('status', false))
              );
            // .setIn([userIndex, 'allProgramStatus'], isAllChecked)
          }
          break;
        }
        case 'term': {
          nextState = nextState.updateIn([...programPath, 'term', termIndex, name], (isOpen) =>
            name === 'isOpen' ? !isOpen : event.target.checked
          );

          if (name !== 'isOpen') {
            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'curriculum'],
              nextState.getIn([...programPath, 'term', termIndex, 'curriculum']).map((s) =>
                s.set('status', event.target.checked).set(
                  'yearLevel',
                  s.get('yearLevel', List()).map((e) =>
                    e.set('status', event.target.checked).set(
                      e.get('rotation', '') === 'yes' ? 'rotation_course' : 'regularCourse',
                      e
                        .get(
                          e.get('rotation', '') === 'yes' ? 'rotation_course' : 'regularCourse',
                          List()
                        )
                        .map((d) =>
                          d.set('status', event.target.checked).set(
                            'course',
                            d
                              .get('course', List())
                              .map((t) => t.set('status', event.target.checked))
                          )
                        )
                    )
                  )
                )
              )
            );

            nextState = nextState.setIn(
              [...programPath, 'status'],
              nextState.getIn([...programPath, 'term'], List()).every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [userIndex, 'programList', programIndex, 'status'],
              nextState
                .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
                .every((s) => s.get('status', false))
            );
          }

          break;
        }
        case 'curriculum':
          nextState = nextState.updateIn(
            [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, name],
            (isOpen) => (name === 'isOpen' ? !isOpen : event.target.checked)
          );
          if (name !== 'isOpen') {
            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'yearLevel'],
              nextState
                .getIn([
                  ...programPath,
                  'term',
                  termIndex,
                  'curriculum',
                  curriculumIndex,
                  'yearLevel',
                ])
                .map((s) =>
                  s.set('status', event.target.checked).set(
                    s.get('rotation', '') === 'yes' ? 'rotation_course' : 'regularCourse',
                    s
                      .get(
                        s.get('rotation', '') === 'yes' ? 'rotation_course' : 'regularCourse',
                        List()
                      )
                      .map((d) =>
                        d.set('status', event.target.checked).set(
                          'course',
                          d.get('course', List()).map((t) => t.set('status', event.target.checked))
                        )
                      )
                  )
                )
            );

            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'status'],
              nextState
                .getIn([...programPath, 'term', termIndex, 'curriculum'], List())
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'status'],
              nextState.getIn([...programPath, 'term'], List()).every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [userIndex, 'programList', programIndex, 'status'],
              nextState
                .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
                .every((s) => s.get('status', false))
            );
          }
          break;
        case 'yearLevel': {
          const yearLevelPath = [
            'term',
            termIndex,
            'curriculum',
            curriculumIndex,
            'yearLevel',
            yearLevelIndex,
          ];
          nextState = nextState.updateIn([...programPath, ...yearLevelPath, name], (isOpen) =>
            name === 'isOpen' ? !isOpen : event.target.checked
          );

          if (name !== 'isOpen') {
            const isRotation = nextState.getIn([...programPath, ...yearLevelPath, 'rotation']);

            nextState = nextState.setIn(
              [
                ...programPath,
                ...yearLevelPath,
                isRotation === 'yes' ? 'rotation_course' : 'regularCourse',
              ],
              nextState
                .getIn([
                  ...programPath,
                  ...yearLevelPath,
                  isRotation === 'yes' ? 'rotation_course' : 'regularCourse',
                ])
                .map((s) =>
                  s.set('status', event.target.checked).set(
                    'course',
                    s.get('course', List()).map((d) => d.set('status', event.target.checked))
                  )
                )
            );

            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'status'],
              nextState
                .getIn(
                  [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'yearLevel'],
                  List()
                )
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'status'],
              nextState
                .getIn([...programPath, 'term', termIndex, 'curriculum'], List())
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'status'],
              nextState.getIn([...programPath, 'term'], List()).every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [userIndex, 'programList', programIndex, 'status'],
              nextState
                .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
                .every((s) => s.get('status', false))
            );
          }
          break;
        }

        case 'rotation': {
          const rotationPath = [
            'term',
            termIndex,
            'curriculum',
            curriculumIndex,
            'yearLevel',
            yearLevelIndex,
            'rotation_course',
            rotationIndex,
          ];
          nextState = nextState.updateIn([...programPath, ...rotationPath, name], (isOpen) =>
            name === 'isOpen' ? !isOpen : event.target.checked
          );

          if (name !== 'isOpen') {
            nextState = nextState.setIn(
              [...programPath, ...rotationPath, 'course'],
              nextState
                .getIn([...programPath, ...rotationPath, 'course'])
                .map((s) => s.set('status', event.target.checked))
            );

            nextState = nextState.setIn(
              [
                ...programPath,
                'term',
                termIndex,
                'curriculum',
                curriculumIndex,
                'yearLevel',
                yearLevelIndex,
                'status',
              ],
              nextState
                .getIn(
                  [
                    ...programPath,
                    'term',
                    termIndex,
                    'curriculum',
                    curriculumIndex,
                    'yearLevel',
                    yearLevelIndex,
                    'rotation_course',
                  ],
                  List()
                )
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'status'],
              nextState
                .getIn(
                  [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'yearLevel'],
                  List()
                )
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'status'],
              nextState
                .getIn([...programPath, 'term', termIndex, 'curriculum'], List())
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'status'],
              nextState.getIn([...programPath, 'term'], List()).every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [userIndex, 'programList', programIndex, 'status'],
              nextState
                .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
                .every((s) => s.get('status', false))
            );
          }
          break;
        }

        case 'rotationCourse': {
          const rotationCoursePath = [
            'term',
            termIndex,
            'curriculum',
            curriculumIndex,
            'yearLevel',
            yearLevelIndex,
            'rotation_course',
            rotationIndex,
          ];
          nextState = nextState.updateIn(
            [...programPath, ...rotationCoursePath, 'course', rotationCourseIndex, name],
            (isOpen) => !isOpen
          );

          nextState = nextState.setIn(
            [...programPath, ...rotationCoursePath, name],
            nextState
              .getIn([...programPath, ...rotationCoursePath, 'course'], List())
              .every((item) => item.get('status', false))
          );
          nextState = nextState.setIn(
            [
              ...programPath,
              'term',
              termIndex,
              'curriculum',
              curriculumIndex,
              'yearLevel',
              yearLevelIndex,
              'status',
            ],
            nextState
              .getIn(
                [
                  ...programPath,
                  'term',
                  termIndex,
                  'curriculum',
                  curriculumIndex,
                  'yearLevel',
                  yearLevelIndex,
                  'rotation_course',
                ],
                List()
              )
              .every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'status'],
            nextState
              .getIn(
                [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'yearLevel'],
                List()
              )
              .every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [...programPath, 'term', termIndex, 'status'],
            nextState
              .getIn([...programPath, 'term', termIndex, 'curriculum'], List())
              .every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [...programPath, 'status'],
            nextState.getIn([...programPath, 'term'], List()).every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [userIndex, 'programList', programIndex, 'status'],
            nextState
              .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
              .every((s) => s.get('status', false))
          );
          break;
        }
        case 'regular': {
          const regularPath = [
            'term',
            termIndex,
            'curriculum',
            curriculumIndex,
            'yearLevel',
            yearLevelIndex,
            'regularCourse',
            regularCourseIndex,
          ];
          nextState = nextState.updateIn([...programPath, ...regularPath, name], (isOpen) =>
            name === 'isOpen' ? !isOpen : event.target.checked
          );

          if (name !== 'isOpen') {
            nextState = nextState.setIn(
              [...programPath, ...regularPath, 'course'],
              nextState
                .getIn([...programPath, ...regularPath, 'course'])
                .map((s) => s.set('status', event.target.checked))
            );

            nextState = nextState.setIn(
              [
                ...programPath,
                'term',
                termIndex,
                'curriculum',
                curriculumIndex,
                'yearLevel',
                yearLevelIndex,
                'status',
              ],
              nextState
                .getIn(
                  [
                    ...programPath,
                    'term',
                    termIndex,
                    'curriculum',
                    curriculumIndex,
                    'yearLevel',
                    yearLevelIndex,
                    'regularCourse',
                  ],
                  List()
                )
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'status'],
              nextState
                .getIn(
                  [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'yearLevel'],
                  List()
                )
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'term', termIndex, 'status'],
              nextState
                .getIn([...programPath, 'term', termIndex, 'curriculum'], List())
                .every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [...programPath, 'status'],
              nextState.getIn([...programPath, 'term'], List()).every((s) => s.get('status', false))
            );
            nextState = nextState.setIn(
              [userIndex, 'programList', programIndex, 'status'],
              nextState
                .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
                .every((s) => s.get('status', false))
            );
          }

          break;
        }

        case 'regularCourse': {
          const regularCoursePath = [
            'term',
            termIndex,
            'curriculum',
            curriculumIndex,
            'yearLevel',
            yearLevelIndex,
            'regularCourse',
            regularCourseIndex,
          ];
          nextState = nextState.updateIn(
            [...programPath, ...regularCoursePath, 'course', regularSizeIndex, name],
            (isOpen) => !isOpen
          );

          nextState = nextState.setIn(
            [...programPath, ...regularCoursePath, name],
            nextState
              .getIn([...programPath, ...regularCoursePath, 'course'], List())
              .every((item) => item.get('status', false))
          );
          nextState = nextState.setIn(
            [
              ...programPath,
              'term',
              termIndex,
              'curriculum',
              curriculumIndex,
              'yearLevel',
              yearLevelIndex,
              'status',
            ],
            nextState
              .getIn(
                [
                  ...programPath,
                  'term',
                  termIndex,
                  'curriculum',
                  curriculumIndex,
                  'yearLevel',
                  yearLevelIndex,
                  'regularCourse',
                ],
                List()
              )
              .every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'status'],
            nextState
              .getIn(
                [...programPath, 'term', termIndex, 'curriculum', curriculumIndex, 'yearLevel'],
                List()
              )
              .every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [...programPath, 'term', termIndex, 'status'],
            nextState
              .getIn([...programPath, 'term', termIndex, 'curriculum'], List())
              .every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [...programPath, 'status'],
            nextState.getIn([...programPath, 'term'], List()).every((s) => s.get('status', false))
          );
          nextState = nextState.setIn(
            [userIndex, 'programList', programIndex, 'status'],
            nextState
              .getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
              .every((s) => s.get('status', false))
          );

          break;
        }

        default:
          break;
      }
      return nextState;
    });
  };

  const constructProgramDetails = () => {
    return programDetails.map((item) => {
      return item
        .set('status', false)
        .set('isOpen', false)
        .set(
          'term',
          item
            .get('level', List())
            .groupBy((item) => {
              return item.get('term', '');
            })
            .entrySeq()
            .map(([key, value]) =>
              Map({
                name: key,
                status: false,
                curriculum: value
                  .groupBy((item) => {
                    return item.get('curriculum', '');
                  })
                  .entrySeq()
                  .map(([key, value]) =>
                    Map({
                      name: key,
                      status: false,
                      yearLevel: value.map((item2) =>
                        item2
                          .set('status', false)
                          .set('isOpen', false)
                          .set(
                            'regularCourse',
                            fromJS([
                              {
                                name: 'Regular Courses',
                                status: false,
                                isOpen: false,
                                course: item2
                                  .get('course', List())
                                  .map((s) => s.set('status', false).set('isOpen', false)),
                              },
                            ])
                          )

                          .set(
                            'rotation_course',
                            item2.get('rotation_course', List()).map((s) =>
                              s
                                .set('status', false)
                                .set('isOpen', false)
                                .set(
                                  'course',
                                  s
                                    .get('course', List())
                                    .map((d) => d.set('status', false).set('isOpen', false))
                                )
                            )
                          )
                      ),
                    })
                  )
                  .toList(),
              })
            )
            .toList()
        );
    });
  };

  useEffect(() => {
    if (programId !== '' && programDetails.size > 0) {
      let selectedCoursesMap = Map();
      selectedCourses.forEach((programList) => {
        programList.get('programList', List()).forEach((selectedCourseList) => {
          selectedCourseList.get('selectedCourseList', List()).forEach((program) => {
            program.get('level', List()).forEach((level) => {
              if (level.get('rotation_course', List()).size > 0) {
                level.get('rotation_course', List()).forEach((rot) => {
                  rot.get('course', List()).forEach((course) => {
                    const courseId =
                      course.get('_course_id', '') +
                      program.getIn(['_institution_calendar_id', '_id']) +
                      level.get('year', '') +
                      level.get('level_no', '') +
                      level.get('term', '') +
                      'yes' +
                      rot.get('rotation_count', '');
                    if (!selectedCoursesMap.has(courseId)) {
                      selectedCoursesMap = selectedCoursesMap.set(courseId, true);
                    }
                  });
                });
              } else {
                level.get('course', List()).forEach((course) => {
                  const courseId =
                    course.get('_course_id', '') +
                    program.getIn(['_institution_calendar_id', '_id']) +
                    level.get('year', '') +
                    level.get('level_no', '') +
                    level.get('term', '');
                  if (!selectedCoursesMap.has(courseId)) {
                    selectedCoursesMap = selectedCoursesMap.set(courseId, true);
                  }
                });
              }
            });
          });
        });
      });

      let updatedProgramDetails = constructProgramDetails().map((item) => {
        const updatedItem = item.set(
          'term',
          item.get('term', List()).map((level) => {
            return level.set(
              'curriculum',
              level.get('curriculum', List()).map((curriculumItem) => {
                return curriculumItem.set(
                  'yearLevel',
                  curriculumItem.get('yearLevel', List()).map((yearLevelItem) => {
                    return yearLevelItem.set(
                      yearLevelItem.get('rotation', '') === 'no'
                        ? 'regularCourse'
                        : 'rotation_course',
                      yearLevelItem
                        .get(
                          yearLevelItem.get('rotation', '') === 'no'
                            ? 'regularCourse'
                            : 'rotation_course',
                          List()
                        )
                        .map((rcItem) => {
                          return rcItem.set(
                            'course',
                            rcItem.get('course', List()).map((courseItem) => {
                              // const courseId = courseItem.get('_course_id');
                              const isRotation =
                                yearLevelItem.get('rotation', '') === 'yes' ? 'yes' : '';

                              const courseId =
                                courseItem.get('_course_id', '') +
                                item.getIn(['_institution_calendar_id', '_id']) +
                                yearLevelItem.get('year', '') +
                                yearLevelItem.get('level_no', '') +
                                level.get('name', '') +
                                isRotation +
                                rcItem.get('rotation_count', '');

                              if (selectedCoursesMap.has(courseId)) {
                                return courseItem.set('status', true);
                              }
                              return courseItem;
                            })
                          );
                        })
                    );
                  })
                );
              })
            );
          })
        );
        return updatedItem;
      });

      const updateRegularCourse = fromJS(updatedProgramDetails).map((programItem) => {
        return programItem.updateIn(['term'], (term) => {
          return term.map((termItem) => {
            return termItem.updateIn(['curriculum'], (curriculum) => {
              return curriculum.map((curriculumItem) => {
                return curriculumItem.updateIn(['yearLevel'], (yearLevel) => {
                  return yearLevel.map((yearLevelItem) => {
                    return yearLevelItem.updateIn(
                      [
                        yearLevelItem.get('rotation', '') === 'no'
                          ? 'regularCourse'
                          : 'rotation_course',
                      ],
                      (regularCourse) => {
                        return regularCourse.map((regularCourseItem) => {
                          const allCoursesStatusTrue = regularCourseItem
                            .get('course', List())
                            .every((courseItem) => courseItem.get('status') === true);
                          return regularCourseItem.set('status', allCoursesStatusTrue);
                        });
                      }
                    );
                  });
                });
              });
            });
          });
        });
      });

      const updateYearLevel = fromJS(updateRegularCourse).map((programItem) => {
        return programItem.updateIn(['term'], (term) => {
          return term.map((termItem) => {
            return termItem.updateIn(['curriculum'], (curriculum) => {
              return curriculum.map((curriculumItem) => {
                return curriculumItem.updateIn(['yearLevel'], (yearLevel) => {
                  return yearLevel.map((yearLevelItem) => {
                    const allCoursesStatusTrue = yearLevelItem
                      .get(
                        yearLevelItem.get('rotation', '') === 'no'
                          ? 'regularCourse'
                          : 'rotation_course',
                        List()
                      )
                      .every((courseItem) => courseItem.get('status') === true);
                    return yearLevelItem
                      .set('status', allCoursesStatusTrue)
                      .updateIn(
                        [
                          yearLevelItem.get('rotation', '') === 'no'
                            ? 'regularCourse'
                            : 'rotation_course',
                        ],
                        (regularCourse) => {
                          return regularCourse.map((regularCourseItem) => {
                            return regularCourseItem;
                          });
                        }
                      );
                  });
                });
              });
            });
          });
        });
      });

      const updateCurriculum = fromJS(updateYearLevel).map((programItem) => {
        return programItem.updateIn(['term'], (term) => {
          return term.map((termItem) => {
            return termItem.updateIn(['curriculum'], (curriculum) => {
              return curriculum.map((curriculumItem) => {
                const allCoursesStatusTrue = curriculumItem
                  .get('yearLevel', List())
                  .every((courseItem) => courseItem.get('status') === true);
                return curriculumItem
                  .set('status', allCoursesStatusTrue)
                  .updateIn(['yearLevel'], (yearLevel) => {
                    return yearLevel.map((yearLevelItem) => {
                      return yearLevelItem;
                    });
                  });
              });
            });
          });
        });
      });

      const updateTerm = fromJS(updateCurriculum).map((programItem) => {
        return programItem.updateIn(['term'], (term) => {
          return term.map((termItem) => {
            const allCoursesStatusTrue = termItem
              .get('curriculum', List())
              .every((courseItem) => courseItem.get('status') === true);
            return termItem
              .set('status', allCoursesStatusTrue)
              .updateIn(['curriculum'], (curriculum) => {
                return curriculum.map((curriculumItem) => {
                  return curriculumItem;
                });
              });
          });
        });
      });

      const updateProgram = fromJS(updateTerm).map((programItem) => {
        const allCoursesStatusTrue = programItem
          .get('term', List())
          .every((courseItem) => courseItem.get('status') === true);
        return programItem.set('status', allCoursesStatusTrue).updateIn(['term'], (term) => {
          return term.map((termItem) => {
            return termItem;
          });
        });
      });

      const programDetailsMap = constructProgramDetails();

      const event = userProgramDetails.getIn(
        [userIndex, 'programList', programIndex, 'status'],
        false
      );

      if (
        userProgramDetails.getIn([userIndex, 'programList', programIndex, 'programDetail'], List())
          .size === 0
      ) {
        setUserProgramDetails(
          userProgramDetails.setIn(
            [userIndex, 'programList', programIndex, 'programDetail'],
            event ? checkAllData(programDetailsMap, event) : updateProgram
          )
        );
        // setSelectedCourse(
        //   userProgramDetails.setIn(
        //     [userIndex, 'programList', programIndex, 'programDetail'],
        //     event ? checkAllData(programDetailsMap, event) : updateProgram
        //   )
        // );
      }
    }
  }, [userIndex, programDetails, programId]); //eslint-disable-line

  const ProgramDetailsMap = userProgramDetails.getIn(
    [userIndex, 'programList', programIndex, 'programDetail'],
    List()
  );

  return (
    <div className="bg-light">
      <div className="announce-overflow-y-lms">
        <div>
          {ProgramDetailsMap.map((item, institutionIndex) => {
            return (
              <div key={institutionIndex}>
                {item.get('_institution_calendar_id', '') !== null ? (
                  <>
                    <AccordionTreeView
                      handleChangeProgram={handleChangeProgram}
                      institutionIndex={institutionIndex}
                      item={item}
                      title={item.getIn(['_institution_calendar_id', 'calendar_name'])}
                      type={'institution'}
                    />
                    <div className={`accordion-content ${item.get('isOpen', false) ? 'open' : ''}`}>
                      {item.get('term', List()).map((termItem, termIndex) => {
                        return (
                          <div key={termIndex}>
                            <AccordionTreeView
                              handleChangeProgram={handleChangeProgram}
                              institutionIndex={institutionIndex}
                              termIndex={termIndex}
                              item={termItem}
                              classes="pd-1"
                              title={termItem.get('name', '')}
                              type={'term'}
                            />
                            <div
                              className={`accordion-content ${
                                termItem.get('isOpen', false) ? 'open' : ''
                              }`}
                            >
                              {termItem.get('isOpen', false) &&
                                termItem
                                  .get('curriculum', List())
                                  .map((curriculumItem, curriculumIndex) => {
                                    return (
                                      <div key={curriculumIndex}>
                                        <AccordionTreeView
                                          handleChangeProgram={handleChangeProgram}
                                          institutionIndex={institutionIndex}
                                          termIndex={termIndex}
                                          curriculumIndex={curriculumIndex}
                                          item={curriculumItem}
                                          classes="pd-2"
                                          title={curriculumItem.get('name', '')}
                                          type={'curriculum'}
                                        />
                                        <div
                                          className={`accordion-content ${
                                            curriculumItem.get('isOpen', false) ? 'open' : ''
                                          }`}
                                        >
                                          {curriculumItem.get('isOpen', false) &&
                                            curriculumItem
                                              .get('yearLevel')
                                              .map((yearLevelItem, yearLevelIndex) => {
                                                return (
                                                  <div key={yearLevelIndex}>
                                                    <AccordionTreeView
                                                      handleChangeProgram={handleChangeProgram}
                                                      institutionIndex={institutionIndex}
                                                      termIndex={termIndex}
                                                      curriculumIndex={curriculumIndex}
                                                      yearLevelIndex={yearLevelIndex}
                                                      item={yearLevelItem}
                                                      classes="pd-3"
                                                      title={`${capitalize(
                                                        yearLevelItem.get('year', '')
                                                      )}, ${capitalize(
                                                        yearLevelItem.get('level_no', '')
                                                      )}`}
                                                      type={'yearLevel'}
                                                    />

                                                    <div
                                                      className={`accordion-content ${
                                                        yearLevelItem.get('isOpen', false)
                                                          ? 'open'
                                                          : ''
                                                      }`}
                                                    >
                                                      {yearLevelItem.get('isOpen', false) &&
                                                        yearLevelItem
                                                          .get('rotation_course', List())
                                                          .map((rotationItem, rotationIndex) => {
                                                            return (
                                                              <div key={rotationIndex}>
                                                                {yearLevelItem.get(
                                                                  'rotation',
                                                                  ''
                                                                ) === 'yes' && (
                                                                  <AccordionTreeView
                                                                    handleChangeProgram={
                                                                      handleChangeProgram
                                                                    }
                                                                    institutionIndex={
                                                                      institutionIndex
                                                                    }
                                                                    termIndex={termIndex}
                                                                    curriculumIndex={
                                                                      curriculumIndex
                                                                    }
                                                                    yearLevelIndex={yearLevelIndex}
                                                                    rotationIndex={rotationIndex}
                                                                    item={rotationItem}
                                                                    classes="pd-4"
                                                                    title={`Rotation ${rotationItem.get(
                                                                      'rotation_count',
                                                                      ''
                                                                    )}`}
                                                                    type={'rotation'}
                                                                  />
                                                                )}

                                                                <div
                                                                  className={`accordion-content ${
                                                                    rotationItem.get(
                                                                      'isOpen',
                                                                      false
                                                                    )
                                                                      ? 'open'
                                                                      : ''
                                                                  }`}
                                                                >
                                                                  {rotationItem.get(
                                                                    'isOpen',
                                                                    false
                                                                  ) &&
                                                                    rotationItem
                                                                      .get('course', List())
                                                                      .map(
                                                                        (
                                                                          rotationCourseItem,
                                                                          rotationCourseIndex
                                                                        ) => {
                                                                          return (
                                                                            <div
                                                                              key={
                                                                                rotationCourseIndex
                                                                              }
                                                                            >
                                                                              <AccordionTreeView
                                                                                handleChangeProgram={
                                                                                  handleChangeProgram
                                                                                }
                                                                                institutionIndex={
                                                                                  institutionIndex
                                                                                }
                                                                                termIndex={
                                                                                  termIndex
                                                                                }
                                                                                curriculumIndex={
                                                                                  curriculumIndex
                                                                                }
                                                                                yearLevelIndex={
                                                                                  yearLevelIndex
                                                                                }
                                                                                rotationIndex={
                                                                                  rotationIndex
                                                                                }
                                                                                rotationCourseIndex={
                                                                                  rotationCourseIndex
                                                                                }
                                                                                item={
                                                                                  rotationCourseItem
                                                                                }
                                                                                classes="pd-5"
                                                                                title={`${rotationCourseItem.get(
                                                                                  'courses_number',
                                                                                  ''
                                                                                )} - ${capitalize(
                                                                                  rotationCourseItem.get(
                                                                                    'courses_name',
                                                                                    ''
                                                                                  )
                                                                                )}${getVersionName(
                                                                                  rotationCourseItem
                                                                                )}`}
                                                                                type={
                                                                                  'rotationCourse'
                                                                                }
                                                                              />
                                                                            </div>
                                                                          );
                                                                        }
                                                                      )}
                                                                </div>
                                                              </div>
                                                            );
                                                          })}
                                                    </div>

                                                    <div
                                                      className={`accordion-content ${
                                                        yearLevelItem.get('isOpen', false)
                                                          ? 'open'
                                                          : ''
                                                      }`}
                                                    >
                                                      {yearLevelItem.get('isOpen', false) &&
                                                        yearLevelItem
                                                          .get('regularCourse', List())
                                                          .map(
                                                            (
                                                              regularCourseName,
                                                              regularCourseIndex
                                                            ) => {
                                                              return (
                                                                <div key={regularCourseIndex}>
                                                                  {yearLevelItem.get(
                                                                    'rotation',
                                                                    ''
                                                                  ) === 'no' && (
                                                                    <AccordionTreeView
                                                                      handleChangeProgram={
                                                                        handleChangeProgram
                                                                      }
                                                                      institutionIndex={
                                                                        institutionIndex
                                                                      }
                                                                      termIndex={termIndex}
                                                                      curriculumIndex={
                                                                        curriculumIndex
                                                                      }
                                                                      yearLevelIndex={
                                                                        yearLevelIndex
                                                                      }
                                                                      regularCourseIndex={
                                                                        regularCourseIndex
                                                                      }
                                                                      item={regularCourseName}
                                                                      classes="pd-4"
                                                                      title={regularCourseName.get(
                                                                        'name',
                                                                        ''
                                                                      )}
                                                                      type={'regular'}
                                                                    />
                                                                  )}
                                                                  <div
                                                                    className={`accordion-content ${
                                                                      regularCourseName.get(
                                                                        'isOpen',
                                                                        false
                                                                      )
                                                                        ? 'open'
                                                                        : ''
                                                                    }`}
                                                                  >
                                                                    {regularCourseName.get(
                                                                      'isOpen',
                                                                      false
                                                                    ) &&
                                                                      regularCourseName
                                                                        .get('course', List())
                                                                        .map(
                                                                          (
                                                                            regularSizeItem,
                                                                            regularSizeIndex
                                                                          ) => {
                                                                            return (
                                                                              <div
                                                                                key={
                                                                                  regularSizeIndex
                                                                                }
                                                                              >
                                                                                {yearLevelItem.get(
                                                                                  'rotation',
                                                                                  ''
                                                                                ) === 'no' && (
                                                                                  <AccordionTreeView
                                                                                    handleChangeProgram={
                                                                                      handleChangeProgram
                                                                                    }
                                                                                    institutionIndex={
                                                                                      institutionIndex
                                                                                    }
                                                                                    termIndex={
                                                                                      termIndex
                                                                                    }
                                                                                    curriculumIndex={
                                                                                      curriculumIndex
                                                                                    }
                                                                                    yearLevelIndex={
                                                                                      yearLevelIndex
                                                                                    }
                                                                                    regularCourseIndex={
                                                                                      regularCourseIndex
                                                                                    }
                                                                                    regularSizeIndex={
                                                                                      regularSizeIndex
                                                                                    }
                                                                                    item={
                                                                                      regularSizeItem
                                                                                    }
                                                                                    classes="pd-5"
                                                                                    title={`${regularSizeItem.get(
                                                                                      'courses_number',
                                                                                      ''
                                                                                    )} - ${capitalize(
                                                                                      regularSizeItem.get(
                                                                                        'courses_name',
                                                                                        ''
                                                                                      )
                                                                                    )}${getVersionName(
                                                                                      regularSizeItem
                                                                                    )}`}
                                                                                    type={
                                                                                      'regularCourse'
                                                                                    }
                                                                                  />
                                                                                )}
                                                                              </div>
                                                                            );
                                                                          }
                                                                        )}
                                                                  </div>
                                                                </div>
                                                              );
                                                            }
                                                          )}
                                                    </div>
                                                  </div>
                                                );
                                              })}
                                        </div>
                                      </div>
                                    );
                                  })}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </>
                ) : (
                  ''
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

ProgramDetailsList.propTypes = {
  userProgramDetails: PropTypes.object,
  setUserProgramDetails: PropTypes.func,
  userIndex: PropTypes.number,
  programIndex: PropTypes.number,
  programDetails: PropTypes.object,
  programId: PropTypes.string,
  checkAllData: PropTypes.func,
  selectedCourses: PropTypes.func,
  handleIsChecked: PropTypes.func,
  setSelectedCourse: PropTypes.func,
};

export default ProgramDetailsList;
