import React from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
//import { Trans, withTranslation } from 'react-i18next';
import i18n from 'i18next';
import * as actions from '../../../_reduxapi/institution/actions';
import BasicDetails from './BasicDetail/BasicDetailIndex';
import UserManagement from './StaffUserManagement/StaffUserManagementIndex';
import '../styles.css';
import ProgramInput from './ProgramInput/NewProgramInput';
import LearningOutcomeManagement from './LearningOutcomeManagement';
import IndependentCourse from './IndependentCourse';
import DepartmentSubject from './DepartmentSubject';
import { makeStyles } from '@mui/styles';

function GlobalConfiguration(props) {
  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
    if (newValue === 1) {
      localStorage.removeItem('staff_management_settings');
    }
  };

  const useStylesFunction = makeStyles(() => ({
    root: {
      borderBottom: '1px solid #e5e5e5 !important',
    },
    indicator: {
      backgroundColor: '#297fdc',
    },
  }));

  const classes = useStylesFunction();
  return (
    <div className="bg-white rounded global-settings">
      <Tabs
        value={value}
        onChange={handleChange}
        classes={{
          indicator: classes.indicator,
        }}
        textColor="primary"
        variant="scrollable"
        className={classes.root}
        scrollButtons="auto"
      >
        <Tab
          label={i18n.t('global_configuration.basic_details')}
          value={0}
          // className={classes.tabColor}
        />
        <Tab label={i18n.t('global_configuration.staff_user_management')} value={1} />
        <Tab label={i18n.t('global_configuration.student_user_management')} value={2} />
        <Tab label={i18n.t('global_configuration.program_input')} value={3} />
        <Tab label={i18n.t('global_configuration.department_subject')} value={4} />
        {/* <Tab label="department subject" value={3} /> */}
        <Tab
          label={i18n.t('global_configuration.learning_outcome_management')}
          className="tab-wrapper-max-300"
          value={5}
        />
        <Tab label={i18n.t('global_configuration.independentCourse')} value={6} />
      </Tabs>
      {value === 0 && <BasicDetails {...props} />}
      {value === 1 && <UserManagement type={'staff'} />}
      {value === 2 && <UserManagement type={'student'} />}
      {value === 3 && <ProgramInput />}
      {value === 4 && <DepartmentSubject />}
      {value === 5 && <LearningOutcomeManagement />}
      {value === 6 && <IndependentCourse />}
    </div>
  );
}
GlobalConfiguration.propTypes = {
  institution: PropTypes.instanceOf(List),
};
const mapStateToProps = (state) => {
  return {};
};

export default compose(withRouter, connect(mapStateToProps, actions))(GlobalConfiguration);
