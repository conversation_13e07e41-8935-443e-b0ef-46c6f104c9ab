import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Table, Modal, Button, Dropdown } from 'react-bootstrap';
import { Multiselect } from 'multiselect-react-dropdown';
import { List, Map } from 'immutable';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { t } from 'i18next';

export default function ManageTopics({
  course,
  advancedSettings,
  saveTopics,
  manageTopics,
  setData,
  handleDeleteSchedule,
  individualSessionDetails,
  currentCalendar,
}) {
  const [modalOpen, setModalOpen] = useState(false);
  const [deliveryType, setDeliveryType] = useState([]);
  const [topics, setTopics] = useState([]);
  const [mode, setMode] = useState('create');
  const [expanded, setExpanded] = useState(Map());
  const [currentTopic, setCurrentTopic] = useState(Map());
  const [topicList, setTopicList] = useState(List());
  const [deletedTopics, setDeletedTopics] = useState([]);

  useEffect(() => {
    let topics = List();
    if (individualSessionDetails && individualSessionDetails.size > 0) {
      topics = individualSessionDetails.reduce((acc, c) => {
        const delivery = c.get('delivery', List());
        if (delivery.isEmpty()) {
          return acc;
        }
        return acc.concat(
          delivery.reduce((acc1, a) => {
            const settings = a.get('settings', List());
            if (settings.isEmpty()) {
              return acc1;
            }
            return acc1.concat(
              settings.reduce((acc2, b) => {
                return acc2.push(b.get('_topic_id', ''));
              }, List())
            );
          }, List())
        );
      }, List());
    }
    setTopicList(topics.filter((item) => item !== '').toJS());
  }, [individualSessionDetails]);

  function openModal() {
    setModalOpen(!modalOpen);
    setMode('create');
    setCurrentTopic(Map());
    setDeliveryType([]);
    setTopics([]);
  }

  function saveData() {
    let error = '';
    const emptyTopics = topics.filter((item) => item.title === '');
    const duplicateTopics = topics.filter(
      (ele, ind) => ind === topics.findIndex((elem) => elem.title === ele.title)
    );

    if (emptyTopics.length > 0) {
      error = 'Topics should not be empty';
    } else if (topics.length !== duplicateTopics.length) {
      error = 'Topics should be unique';
    }
    if (error) {
      setData(Map({ message: error }));
      return;
    }

    const filteredTopics = topics.map((topic) => {
      return topic._id !== '' ? topic : { title: topic.title };
    });
    const data = {
      delivery_type_details: deliveryType,
      topics: filteredTopics,
      deleted_topics: deletedTopics,
    };
    saveTopics(data, mode, currentTopic.get('_id', ''), () => openModal());
  }

  function getDeliveryTypes() {
    if (!advancedSettings.isEmpty()) {
      const groupDeliveryId = manageTopics
        .filter((item) =>
          currentTopic.get('_id', '') !== ''
            ? currentTopic.get('_id', '') !== item.get('_id')
            : item
        )
        .map((item) =>
          item.get('delivery_type_details', List()).map((data) => data.get('delivery_type_id'))
        )
        .reduce((_, el) => el, List())
        .toJS();
      let deliveryTypes = advancedSettings
        .get('delivery_type', List())
        .filter((item) =>
          groupDeliveryId.length > 0 ? !groupDeliveryId.includes(item.get('_delivery_id')) : item
        )
        .map((item) => {
          return {
            session_id: item.get('_session_id'),
            delivery_type_id: item.get('_delivery_id'),
            delivery_type: item.get('delivery_type'),
            delivery_symbol: item.get('delivery_symbol'),
          };
        })
        .toJS();
      return deliveryTypes;
    }
    return [];
  }

  function handleSelect(selectedList) {
    handleChange(selectedList, 'deliveryType');
  }

  function handleRemove(selectedList) {
    handleChange(selectedList, 'deliveryType');
  }

  function handleChange(value, name, index, id) {
    switch (name) {
      case 'deliveryType':
        setDeliveryType(value);
        break;
      case 'topics':
        setTopics(value);
        break;
      case 'enterTopic': {
        let copyTopic = [...topics];
        let copyIndex = copyTopic[index];
        copyIndex = { _id: id, title: value };
        copyTopic[index] = copyIndex;
        setTopics(copyTopic);
        break;
      }
      case 'removeTopic': {
        let filterTopic = topics.filter((item, i) => index !== i);
        setTopics(filterTopic);
        if (id !== '') {
          const deleteT = [...deletedTopics];
          deleteT.push(id);
          setDeletedTopics(deleteT);
        }
        break;
      }
      default:
        break;
    }
  }

  function editTopic(editData) {
    openModal();
    setCurrentTopic(editData);
    setMode('update');
    const delivery_type_details = editData.get('delivery_type_details', List()).map((item) => {
      return {
        delivery_symbol: item.get('delivery_symbol'),
        delivery_type: item.get('delivery_type'),
        delivery_type_id: item.get('delivery_type_id'),
        session_id: item.get('session_id'),
      };
    });
    setDeliveryType(delivery_type_details.toJS());
    setTopics(editData.get('topics', []).toJS());
  }

  function addMoreTopic() {
    const copyTopic = [...topics];
    copyTopic.push({ _id: '', title: '' });
    handleChange(copyTopic, 'topics');
  }

  const style = {
    searchBox: {
      border: '1px solid #dee2e6',
      borderRadius: '5px',
    },
    chips: {
      background: 'rgb(222 226 230 / 0%)',
      border: '1px solid #b0b4b7',
      color: '#554d4d',
    },
  };

  function checkDisabled() {
    let arr = [];
    if (topics && topics.length > 0) {
      topics.map((topic) => {
        const checkDisabled = topicList.includes(topic._id);
        arr.push(checkDisabled);
        return topic;
      });
    }
    return arr.filter((item) => item === true).length > 0;
  }

  return (
    <>
      {' '}
      <Table responsive hover className="min_h">
        <thead className="bg-white thead_border ">
          <tr>
            <th>{t('delivery_type_set')}</th>
            <th>{t('topic_title')}</th>

            {currentCalendar &&
            CheckPermission(
              'subTabs',
              'Schedule Management',
              'Course Scheduling',
              '',
              'Schedule',
              '',
              'Manage Course',
              'Manage Topic Add'
            ) ? (
              <th className="text-skyblue text-right remove_hover" onClick={openModal}>
                {t('create_topic_set')}{' '}
              </th>
            ) : (
              <th></th>
            )}
          </tr>
        </thead>
        <tbody>
          {manageTopics.size === 0 ? (
            <tr>
              <td colSpan="3">
                <div className="mt-3 mb-3 text-center">{t('no_topics_found')}</div>
              </td>
            </tr>
          ) : (
            <>
              {manageTopics.map((topic, index) => {
                const topicsId = topic
                  .get('topics', List())
                  .map((item) => item.get('_id'))
                  .toJS();
                const intersection = topicList.filter((element) => topicsId.includes(element));
                const intersectionSchedule = course
                  .get('assigned_topics', List())
                  .toJS()
                  .filter((element) => topicsId.includes(element));
                return (
                  <React.Fragment key={index}>
                    <tr>
                      <td className="">
                        <div className="pt-1">
                          {topic
                            .get('delivery_type_details', List())
                            .map((ses) => ses.get('delivery_symbol', ''))
                            .join(', ')}
                        </div>
                      </td>
                      <td>
                        <div className="pt-1"></div>
                      </td>

                      <td className="">
                        <div className="pt-1 d-flex justify-content-end remove_hover ">
                          <i
                            className={`fa fa-chevron-${
                              expanded.get(topic.get('_id')) ? 'up' : 'down'
                            } text-skyblue cursor-pointer  pr-3 pt-1`}
                            aria-hidden="true"
                            onClick={() =>
                              setExpanded(
                                expanded.set(
                                  topic.get('_id'),
                                  !expanded.get(topic.get('_id'), false)
                                )
                              )
                            }
                          ></i>
                          {currentCalendar &&
                            (CheckPermission(
                              'subTabs',
                              'Schedule Management',
                              'Course Scheduling',
                              '',
                              'Schedule',
                              '',
                              'Manage Course',
                              'Manage Topic Edit'
                            ) ||
                              CheckPermission(
                                'subTabs',
                                'Schedule Management',
                                'Course Scheduling',
                                '',
                                'Schedule',
                                '',
                                'Manage Course',
                                'Manage Topic Delete'
                              )) && (
                              <small className="f-18 mr-1">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    variant=""
                                    id="dropdown-table"
                                    className="table-dropdown"
                                    size="sm"
                                  >
                                    <div className="f-16">
                                      <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                                    </div>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu renderOnMount={false}>
                                    {CheckPermission(
                                      'subTabs',
                                      'Schedule Management',
                                      'Course Scheduling',
                                      '',
                                      'Schedule',
                                      '',
                                      'Manage Course',
                                      'Manage Topic Edit'
                                    ) && (
                                      <Dropdown.Item onClick={() => editTopic(topic)}>
                                        {t('edit')}
                                      </Dropdown.Item>
                                    )}
                                    {intersection.length === 0 &&
                                      intersectionSchedule.length === 0 && (
                                        <>
                                          {CheckPermission(
                                            'subTabs',
                                            'Schedule Management',
                                            'Course Scheduling',
                                            '',
                                            'Schedule',
                                            '',
                                            'Manage Course',
                                            'Manage Topic Delete'
                                          ) && (
                                            <Dropdown.Item
                                              onClick={() =>
                                                handleDeleteSchedule(
                                                  { setting: topic },
                                                  false,
                                                  'topics'
                                                )
                                              }
                                            >
                                              {t('delete')}
                                            </Dropdown.Item>
                                          )}
                                        </>
                                      )}
                                  </Dropdown.Menu>
                                </Dropdown>
                              </small>
                            )}
                        </div>
                      </td>
                    </tr>
                    {expanded.get(topic.get('_id')) && (
                      <>
                        {topic.get('topics', List()).map((list, index) => {
                          return (
                            <tr className="bg-gray" key={index}>
                              <td className="">
                                <div className="pt-1"> </div>
                              </td>
                              <td>
                                <div className="pt-1"> {list.get('title', '')}</div>
                              </td>

                              <td className=""></td>
                            </tr>
                          );
                        })}
                      </>
                    )}
                  </React.Fragment>
                );
              })}
            </>
          )}
        </tbody>
      </Table>
      {modalOpen && (
        <Modal show={modalOpen} dialogClassName="model-600" centered>
          <Modal.Body>
            <div className="p-2">
              <div className="border-bottom mb-2">
                <div className="row">
                  <div className="col-md-12">
                    <p className="mb-1 f-17 bold"> {t('topic_set')}</p>
                  </div>
                </div>
              </div>

              <div className="row pb-2">
                <div className="col-md-12 mt-2 mb-2">
                  <span className=""> {t('choose_delivery_type')} </span>
                  <Multiselect
                    disable={checkDisabled()}
                    options={getDeliveryTypes()}
                    displayValue="delivery_type"
                    showCheckbox={true}
                    style={style}
                    closeIcon="close"
                    placeholder={t('select_delivery_type')}
                    onSelect={handleSelect}
                    onRemove={handleRemove}
                    selectedValues={deliveryType}
                    closeOnSelect={false}
                  />
                </div>
                <div className="col-md-12 pt-3">
                  <div className="d-flex justify-content-between">
                    <p className="mb-3 "> {t('enter_topics')}</p>
                    <p
                      className="mb-3 text-skyblue remove_hover text-uppercase"
                      onClick={() => addMoreTopic()}
                    >
                      {' '}
                      {t('add_topic')}
                    </p>
                  </div>
                </div>
                <div className="roles_height" style={{ maxHeight: '350px', width: '98%' }}>
                  {topics &&
                    topics.length > 0 &&
                    topics.map((topic, index) => {
                      const checkDisabled =
                        topicList.includes(topic._id) ||
                        course.get('assigned_topics', List()).toJS().includes(topic._id);
                      return (
                        <div className="col-md-12 pb-3" key={index}>
                          <div className="d-flex ">
                            <div className="p-2  w-100">
                              <input
                                type="text"
                                value={topic.title}
                                placeholder={t('enter_topic')}
                                className="mb-0 form-control border-radious-8"
                                onChange={(e) =>
                                  handleChange(e.target.value, 'enterTopic', index, topic._id)
                                }
                              />
                            </div>
                            <div
                              className="pl-2 pt-3 remove_hover"
                              onClick={
                                !checkDisabled
                                  ? () => handleChange('', 'removeTopic', index, topic._id)
                                  : () => {}
                              }
                              style={(() => {
                                let final = {};
                                if (checkDisabled) {
                                  final = {
                                    cursor: 'not-allowed',
                                  };
                                }
                                return final;
                              })()}
                            >
                              <i className="fa fa-times-circle-o text-gray" aria-hidden="true"></i>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <div className="container">
              <div className="row">
                <div className="col-md-12">
                  <div className="float-right">
                    <b className="">
                      <Button variant="outline-primary mr-3" onClick={openModal}>
                        {t('cancel')}
                      </Button>

                      <Button
                        variant="primary"
                        onClick={saveData}
                        disabled={deliveryType.length === 0 || topics.length === 0}
                      >
                        {t('save')}
                      </Button>
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Footer>
        </Modal>
      )}
    </>
  );
}

ManageTopics.propTypes = {
  course: PropTypes.instanceOf(Map),
  advancedSettings: PropTypes.instanceOf(Map),
  manageTopics: PropTypes.instanceOf(List),
  handleDeleteSchedule: PropTypes.instanceOf(List),
  saveTopics: PropTypes.func,
  setData: PropTypes.func,
  individualSessionDetails: PropTypes.func,
  currentCalendar: PropTypes.bool,
};
