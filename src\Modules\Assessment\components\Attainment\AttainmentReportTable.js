import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import {
  Checkbox,
  Menu,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Radio,
  RadioGroup,
  FormControlLabel,
} from '@mui/material';
import { List, Map, fromJS } from 'immutable';
import moment from 'moment';
import dotIcon from 'Assets/dotIcon.svg';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { trimFractionDigits, ucFirst, indVerRename } from 'utils';
import { setTreeLevel, filterAttainmentList, getBenchmarkValues } from '../../utils';
import AttainmentAchieved from './AttainmentAchieved';
import AttainmentStudentReportTable from './AttainmentStudentReportTable';
import SubdirectoryArrowRightOutlinedIcon from '@mui/icons-material/SubdirectoryArrowRightOutlined';

function DropdownOptions({
  anchorEl,
  handleClose,
  details,
  handleMenuClick,
  outCome,
  selectedId,
  setSelectedId,
  setSelectedCoTreeId,
  selectedCoTreeId,
  isTreeLevel,
}) {
  const [ploAttainment, setPloAttainment] = useState('assessment');
  const attainments = details.get('attainments', List());
  const [coTree, setCoTree] = useState(List());
  const [checkedAll, setCheckedAll] = useState(false);
  const coTreeList = fromJS([details.get('coTree', List())]);

  useEffect(() => {
    const coTreeAr = [];
    const preparedOption = (data, steps = '') => {
      coTreeAr.push({
        name: `${data.get('nodeName', '') || data.get('typeName', '')}`,
        value: data.get('_id', ''),
        typeId: details.get('typeId', ''),
        coTreeId: data.get('typeId', ''),
        step: steps,
      });
    };
    if (isTreeLevel === 'subTree') {
      coTreeList.forEach((one) => {
        preparedOption(one);
        one.get('tree', List()).forEach((two) => {
          preparedOption(two, 1);
          two.get('subTree', List()).forEach((three) => {
            preparedOption(three, 2);
            three.get('node', List()).forEach((four) => {
              preparedOption(four, 3);
            });
          });
        });
      });
    } else if (isTreeLevel === 'node') {
      coTreeList.forEach((one) => {
        preparedOption(one);
        one.get('subTree', List()).forEach((two) => {
          preparedOption(two, 1);
          two.get('node', List()).forEach((three) => {
            preparedOption(three, 2);
          });
        });
      });
    } else {
      coTreeList.forEach((one) => {
        preparedOption(one);
        if (one.get('subTree', List()).size > 0) {
          one.get('subTree', List()).forEach((two) => {
            preparedOption(two, 1);
            two.get('node', List()).forEach((three) => {
              preparedOption(three, 2);
            });
          });
        } else {
          one.get('node', List()).forEach((three) => {
            preparedOption(three, 1);
          });
        }
      });
    }
    setCoTree(fromJS(coTreeAr));
  }, []); //eslint-disable-line

  useEffect(() => {
    if (outCome === 'plo') {
      const checkSelectedId = attainments.find((item1) =>
        selectedId.includes(item1.get('_assessment_id', ''))
      );
      setPloAttainment(checkSelectedId !== undefined ? 'assessment' : 'co-tree');
    }
    const selAssessmentAr = selectedId.filter((item1) =>
      attainments.some((item2) => item2.get('_assessment_id', '') === item1)
    );
    setCheckedAll(selAssessmentAr.size === attainments.size ? true : false);
  }, []); //eslint-disable-line

  const filterCoTreeId = selectedCoTreeId.filter(
    (item) => item.get('typeId') === details.get('typeId', '')
  );

  const selAssessment = selectedId.filter(
    (item1) => !attainments.some((item2) => item2.get('_assessment_id', '') === item1)
  );

  const selCoTree = selectedCoTreeId.filter(
    (item1) => !coTree.some((item2) => item2.get('coTreeId', '') === item1.get('coTreeId', ''))
  );

  const handlePloAttainment = (e) => {
    e.preventDefault();
    setPloAttainment(e.target.value);
  };

  const filterData = () => {
    if (ploAttainment === 'co-tree') {
      setSelectedId(fromJS(selAssessment));
    } else {
      setSelectedCoTreeId(fromJS(selCoTree));
    }
  };

  const handleClickAway = () => {
    handleClose();
  };

  const handleCheckAll = (e) => {
    setCheckedAll(!checkedAll);
    const assessmentIds = !checkedAll
      ? attainments.map((s) => s.get('_assessment_id', ''))
      : selAssessment;
    const checkedData = [...assessmentIds, ...selAssessment];
    const uniqueCheckedData = checkedData
      .reverse()
      .filter((t, i) => checkedData.findIndex((s) => t === s) === i);
    handleMenuClick(e, uniqueCheckedData, 'checkAll', filterData, [], selCoTree);
  };

  return (
    <Menu
      id="long-menu"
      anchorEl={anchorEl}
      keepMounted
      open={Boolean(anchorEl)}
      onClose={handleClickAway}
      PaperProps={{ style: { maxHeight: 48 * 10.5, width: '41.2ch' } }}
      mouseEvent={false}
    >
      <div>
        <p className="bold pt-2 pl-3 mb-2">
          {details.get('nodeName', '') || details.get('typeName', '')} (
          {details.get('weightage', 0)})
        </p>
        {!isTreeLevel && outCome === 'plo' && (
          <div className="d-flex">
            <div className="pt-2 pr-3 pl-3">From</div>
            <RadioGroup
              row
              aria-labelledby="demo-radio-buttons-group-label"
              defaultValue="assessment"
              name="radio-buttons-group"
              value={ploAttainment}
              onChange={(e) => handlePloAttainment(e)}
            >
              <FormControlLabel value="co-tree" control={<Radio />} label="CO Tree" />
              <FormControlLabel
                value={'assessment'}
                control={<Radio />}
                label={'Assessment Plan'}
              />
            </RadioGroup>
          </div>
        )}

        {ploAttainment === 'assessment' ? (
          <>
            <ListItem button onClick={(e) => handleCheckAll(e)}>
              <ListItemIcon className="minWidth-32">
                <Checkbox edge="start" checked={checkedAll} color="primary" />
              </ListItemIcon>
              <ListItemText primary={'Select All'} />
            </ListItem>
            {attainments.map((item, index) => (
              <React.Fragment key={index}>
                <ListItem
                  button
                  onClick={(e) =>
                    handleMenuClick(e, item, 'assessment', filterData, selAssessment, selCoTree)
                  }
                >
                  <ListItemIcon className="minWidth-32">
                    <Checkbox
                      edge="start"
                      checked={selectedId.includes(item.get('_assessment_id', ''))}
                      color="primary"
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={ucFirst(item.get('assessmentName', ''))}
                    secondary={
                      <div>
                        <div>
                          <span>M {item.get('assessmentMark', '')} </span>
                          <img src={dotIcon} alt="" className="mx-1" />
                          <span className="f-14"> Q {item.get('noQuestions', '')} </span>
                          <img src={dotIcon} alt="" className="mx-1" />
                          <span className="f-14">
                            {' '}
                            {moment(item.get('updatedAt', '')).format('DD MMM YYYY')}
                          </span>
                        </div>

                        {outCome === 'plo' && (
                          <div>
                            {'GY - '}
                            {item.get('institutionCalendar', '')}
                          </div>
                        )}
                      </div>
                    }
                  />
                </ListItem>
                {index + 1 < attainments.size && <Divider />}
              </React.Fragment>
            ))}
          </>
        ) : (
          coTree.map((item, index) => (
            <React.Fragment key={index}>
              <ListItem
                className={`${`ml-${item.get('step', '')}`}`}
                button
                onClick={(e) =>
                  handleMenuClick(e, item, 'coTree', filterData, selAssessment, selCoTree)
                }
              >
                <ListItemIcon className="minWidth-32">
                  <SubdirectoryArrowRightOutlinedIcon
                    className="mr-2 mt-2 text-gray"
                    fontSize={`small`}
                  />
                  <Radio
                    edge="start"
                    checked={filterCoTreeId.getIn([0, 'coTreeId']) === item.get('coTreeId')}
                    color="primary"
                  />
                </ListItemIcon>
                <ListItemText primary={ucFirst(item.get('name', ''))} />
              </ListItem>
              <Divider />
            </React.Fragment>
          ))
        )}
      </div>
    </Menu>
  );
}
DropdownOptions.propTypes = {
  anchorEl: PropTypes.object,
  handleClose: PropTypes.func,
  details: PropTypes.instanceOf(Map),
  selectedId: PropTypes.string,
  handleMenuClick: PropTypes.func,
  outCome: PropTypes.string,
  setSelectedId: PropTypes.func,
  setSelectedCoTreeId: PropTypes.func,
  selectedCoTreeId: PropTypes.string,
  isTreeLevel: PropTypes.bool,
};

function AttainmentReportTable({
  courseProgramReport,
  selectedNode,
  selectValues,
  selectedType,
  questionList,
  setQuestionList,
  selectedId,
  setSelectedId,
  fetchApi,
  setSelectedCoTreeId,
  selectedCoTreeId,
}) {
  const attainmentType = selectValues.getIn(['attainmentType', 'value'], '');
  const outCome = selectValues.getIn(['outcome', 'value'], '');
  const programId = selectValues.getIn(['program', 'value'], '');
  useEffect(() => {
    // if (courseProgramReport.size) {
    const filterCourseReport = filterList(courseProgramReport.get('attainmentNodeList', List()));
    if (filterCourseReport !== undefined && filterCourseReport?.get('level') === 'three') {
      let questionListAr = [];
      filterCourseReport.get('attainments', List()).forEach((one) => {
        if (
          courseProgramReport.get('assessmentIds', List()).includes(one.get('_assessment_id', ''))
        ) {
          questionListAr.push(one);
        }
      });
      setQuestionList(fromJS(questionListAr));
    }
    setSelectedId(courseProgramReport.get('assessmentIds', List()));
    setSelectedCoTreeId(
      courseProgramReport?.get('coTreeIds', List()).filter((item) => item.get('mode', '') === 'co')
    );

    // }
  }, [courseProgramReport, selectedNode]); //eslint-disable-line

  const filterList = (array) => {
    if (selectedType) {
      for (const item of array) {
        if (selectedNode === item.get('_id', '')) return setTreeLevel(item);
        for (const item2 of item.get('subTree', List())) {
          if (item2.get('_id', '') === selectedNode) return setTreeLevel(item2);
          for (const item3 of item2.get('node', List())) {
            if (item3.get('_id', '') === selectedNode) return setTreeLevel(item3);
          }
        }
      }
    }
  };

  const filterCourseReport = filterList(courseProgramReport.get('attainmentNodeList', List()));
  const manageTargetBenchMark = courseProgramReport.get('manageTargetBenchMark', Map());

  const getCloDetails = (details, cloId) => {
    const nodeDetails = details.get('nodeName', '') ? details : details;
    return nodeDetails
      .get(outCome === 'clo' ? 'clo' : 'plo', List())
      .filter((item) => item.get('_id', '') === cloId);
  };

  const handleMenuClick = (e, item, type, filterData, selAssessment, selCoTree) => {
    let selectedIds = [...selectedId.toJS()];
    let selectedCoTreeIds = [...selectedCoTreeId.toJS()];
    if (type === 'coTree') {
      selectedCoTreeIds.push({
        typeId: item.get('typeId', ''),
        coTreeId: item.get('coTreeId', ''),
        mode: 'co',
      });
    } else if (type === 'checkAll') {
      selectedIds = item;
    } else {
      const isChecked = selectedId.includes(item.get('_assessment_id', ''));
      const selectedItemId = item.get('_assessment_id', '');
      if (!isChecked) {
        selectedIds.push(selectedItemId);
      } else {
        selectedIds = selectedId.filter((e) => e !== selectedItemId);
      }
    }

    const uniqueSelectedCoTreeIds = selectedCoTreeIds
      .reverse()
      .filter((t, i) => selectedCoTreeIds.findIndex((s) => t?.typeId === s?.typeId) === i);

    const callback = () => {
      filterData();
    };

    fetchApi(selectedIds, callback, uniqueSelectedCoTreeIds, type, selAssessment, selCoTree);
  };

  const attainmentSubTree = (typeId) => {
    const filteredNodeList = filterAttainmentList(
      courseProgramReport.get('attainmentNodeList', List()),
      typeId
    );

    const subTreeLevel =
      filteredNodeList.get('level', '') === 'one'
        ? 'subTree'
        : filteredNodeList.get('level', '') === 'two'
        ? 'node'
        : 'attainments';

    let subTreeValue = [];
    if (typeId === 'Overall Attainment') {
      subTreeValue = courseProgramReport
        .get('attainmentNodeList', List())
        .slice(1)
        .map(
          (item) =>
            `${ucFirst(item.get('typeName', '')) || ucFirst(item.get('nodeName', ''))} ${item.get(
              'weightage',
              ''
            )}`
        )
        .join(', ');
    } else {
      subTreeValue = filteredNodeList
        .get(subTreeLevel, List())
        .map((item) =>
          subTreeLevel === 'attainments'
            ? `${ucFirst(item.get('assessmentName', ''))} ${item.get('benchMark', '')}`
            : `${ucFirst(item.get('typeName', '')) || ucFirst(item.get('nodeName', ''))} ${item.get(
                'weightage',
                ''
              )}`
        )
        .join(', ');
    }

    return subTreeValue ? `Avg Of (${subTreeValue})` : '';
  };

  const TargetBenchMarkData = ({ filteredBenchMarkValue }) => {
    return filteredBenchMarkValue.get('level', '')
      ? courseProgramReport.get('attainmentValuesAs', '') === 'fixed'
        ? `Target = ${filteredBenchMarkValue.get('level', '')}, ${filteredBenchMarkValue.get(
            'percentage',
            ''
          )}%`
        : `Target = ${filteredBenchMarkValue.get('level', '')}, ${filteredBenchMarkValue.get(
            'min',
            ''
          )}% - ${filteredBenchMarkValue.get('max', '')}%`
      : '';
  };

  const NodeHeadingTitle = ({ details, disableClass }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const handleClick = (e) => {
      setAnchorEl(e.currentTarget);
    };
    const handleClose = () => {
      setAnchorEl(null);
    };
    const title = details?.get('nodeName', '') || details?.get('typeName', '');
    const filteredBenchMarkValue = getBenchmarkValues(
      details.get('typeId', ''),
      courseProgramReport
    );

    const checkNodeTileView = () => {
      if (outCome === 'clo') {
        return details.get('nodeName', '') === '';
      } else {
        const detailKeys = details.keySeq().toArray();
        if (detailKeys.includes('subTree')) {
          return details.get('subTree', '').size > 0;
        } else if (detailKeys.includes('node') || !detailKeys.includes('node')) {
          return details.get('node', '').size > 0;
        } else {
          return details.get('nodeName', '') === '';
        }
      }
    };

    const nodeTileView = checkNodeTileView();

    return nodeTileView ? (
      <Tooltips
        title={
          <>
            <div>{ucFirst(title)}</div>
            <div>{attainmentSubTree(details.get('typeId', ''))}</div>
            <div>
              <TargetBenchMarkData filteredBenchMarkValue={filteredBenchMarkValue} />{' '}
            </div>
          </>
        }
      >
        <div className={`${!disableClass ? 'cw_160' : ''}`}>
          <p className="thHeaderReport bold">
            {ucFirst(title)}{' '}
            {details.get('weightage', '') && <span>({details.get('weightage', 0)})</span>}
          </p>
        </div>
      </Tooltips>
    ) : (
      <>
        <div
          className={`${
            !disableClass ? '' : 'w-auto'
          } d-flex justify-content-between align-items-center bg-gray border-radious-4 m-2 remove_hover`}
          onClick={handleClick}
        >
          <Tooltips
            title={
              <>
                <div>{ucFirst(title)}</div>
                <div>{attainmentSubTree(details.get('typeId', ''))}</div>
                <div>
                  <TargetBenchMarkData filteredBenchMarkValue={filteredBenchMarkValue} />{' '}
                </div>
              </>
            }
          >
            <p className="thHeaderReport theme-text-overflow text-NewWarning pl-2">
              {ucFirst(title)} ({details.get('weightage', 0)})
            </p>
          </Tooltips>
          <div className="pt-1">
            <ArrowDropDownIcon className="p-0 text-skyblue" fontSize={`medium`} />
          </div>
        </div>
        {Boolean(anchorEl) && (
          <DropdownOptions
            anchorEl={anchorEl}
            handleClose={handleClose}
            details={details}
            handleMenuClick={handleMenuClick}
            outCome={outCome}
            selectedId={selectedId}
            setSelectedId={setSelectedId}
            setSelectedCoTreeId={setSelectedCoTreeId}
            selectedCoTreeId={selectedCoTreeId}
            setAnchorEl={setAnchorEl}
            isTreeLevel={
              details.get('subTree', '').size === 0
                ? 'subTree'
                : details.get('node', '').size === 0
                ? 'node'
                : details.get('attainments', '').size === 0
                ? 'attainments'
                : ''
            }
          />
        )}
      </>
    );
  };
  NodeHeadingTitle.propTypes = {
    details: PropTypes.instanceOf(Map),
    disableClass: PropTypes.bool,
  };

  const NodeHeading = ({ details }) => {
    return (
      <th scope="col" className="borderNone">
        <NodeHeadingTitle details={details} />
      </th>
    );
  };
  NodeHeading.propTypes = {
    details: PropTypes.instanceOf(Map),
  };

  const NodeParentHeading = () => {
    const filteredBenchMarkValue = getBenchmarkValues('Overall Attainment', courseProgramReport);
    return (
      <th scope="col" className="borderNone cw_300">
        <div className="cw_300 row align-items-center">
          <div className="col-6 pr-0">
            <p className="thHeaderReport text-left bold">Node</p>
          </div>
          {selectedNode ? (
            <div className="col-6 pl-0">
              <NodeHeadingTitle details={filterCourseReport} disableClass={true} />
            </div>
          ) : (
            <div className="col-6 pl-0">
              <Tooltips
                title={
                  <>
                    <div>Overall Attainment</div>
                    <div>{attainmentSubTree('Overall Attainment')}</div>
                    <div>
                      {' '}
                      <TargetBenchMarkData filteredBenchMarkValue={filteredBenchMarkValue} />{' '}
                    </div>
                  </>
                }
              >
                <p className="thHeaderReport text-left bold">Overall Attainment</p>
              </Tooltips>
            </div>
          )}
        </div>
      </th>
    );
  };

  const getMarkColor = (detail, percent, type) => {
    const typeId = type === 'question' ? detail : detail.get('typeId', '');
    const filteredBenchMarkValue = getBenchmarkValues(typeId, courseProgramReport);
    const markValue =
      courseProgramReport.get('attainmentValuesAs', '') === 'fixed'
        ? filteredBenchMarkValue.get('percentage', '')
        : filteredBenchMarkValue.get('min', '');
    const markColor = markValue && trimFractionDigits(percent) > markValue ? '#16A34A' : '#6B7280';
    return markColor;
  };

  const getAttainmentTargetMarkColor = (cloId, percent) => {
    const getOutcomeDetail = courseProgramReport
      .getIn(['manageTargetBenchMark', 'outComes'], List())
      .filter((clo) => clo.get('outComeId') === cloId);
    const markValue = getOutcomeDetail.getIn([0, 'values'], 0);
    const markColor = markValue && trimFractionDigits(percent) > markValue ? '#16A34A' : '#6B7280';
    return markColor;
  };

  const AttainmentDetails = ({ details, cloId }) => {
    const cloDetails = getCloDetails(details, cloId);
    const percent = cloDetails.getIn([0, 'percentage'], '');
    const level = cloDetails.getIn([0, 'level'], '');
    const levelColor = courseProgramReport
      .get('levelColors', List())
      .find((item) => item.get('level', '') === level);
    const markColor = manageTargetBenchMark.isEmpty()
      ? getMarkColor(details, percent)
      : getAttainmentTargetMarkColor(cloId, percent);

    return (
      <div className="row align-items-center">
        <div className="col-6 pr-0">
          <p className="thHeaderReport" style={{ color: markColor }}>
            {percent !== '' && percent !== null ? `${trimFractionDigits(percent)}%` : '---'}
          </p>
        </div>
        <div className="col-6 pl-0">
          <div className="d-flex justify-content-center ">
            <p
              className="innerValue"
              {...(level &&
                levelColor && {
                  style: { backgroundColor: levelColor.get('color', ''), color: '#fff' },
                })}
            >
              {level.replace('Level', 'L') || '---'}
            </p>
          </div>
        </div>
      </div>
    );
  };
  AttainmentDetails.propTypes = {
    details: PropTypes.instanceOf(Map),
    cloId: PropTypes.string,
  };

  const getQuestionMarks = () => {
    return questionList.reduce(
      (acc, ques) =>
        acc.concat(
          ques
            .get('questionMarks', List())
            .map((item) => item.set('typeId', ques.get('typeId', '')))
        ),
      List()
    );
  };

  const questionMarksData = getQuestionMarks();

  const questionMarks = questionMarksData.filter(
    (a, i) => questionMarksData.findIndex((s) => a.get('_id') === s.get('_id')) === i
  );

  const getCloName = (co) => {
    const cloName = courseProgramReport
      .get(outCome === 'clo' ? 'courseCLO' : 'programPLO', List())
      .find((item) => item.get('_id', '') === co);
    return `${cloName.get('no', '')}`;
  };

  const getMark = (question, average, cloId) => {
    const getId = question.get('outComeIds', List()).find((co) => co === cloId);
    const percent = getId !== undefined ? Number(average) * 100 : '';
    const markColor = manageTargetBenchMark.isEmpty()
      ? getMarkColor(selectedType, percent, 'question')
      : getAttainmentTargetMarkColor(cloId, percent);

    return (
      <span style={{ color: markColor }}>
        {percent !== '' && percent !== null ? `${trimFractionDigits(percent)}%` : '---'}
      </span>
    );
  };

  const getTooltipData = (question, cloId) => {
    const getId = question.get('outComeIds', List()).find((co) => co === cloId);
    return getId !== undefined ? true : false;
  };

  const ReportTableColumn = ({ type, item, cloId, disableClass }) => {
    const ColumnType = ({ details }) => {
      if (type === 'header') return <NodeHeading details={details} />;
      if (type === 'subHeader')
        return (
          <td className="attainment_border_bottom bg-gray">
            <p className="thHeaderAttainment f-15">%</p>
          </td>
        );
      if (type === 'body')
        return (
          <td className={`${!disableClass ? '' : 'cw_160'} attainment_border_bottom `}>
            <AttainmentDetails details={details} cloId={item.get('_id', '')} />
          </td>
        );
    };
    ColumnType.propTypes = {
      details: PropTypes.instanceOf(Map),
    };

    if (selectedType) {
      if (filterCourseReport?.get('level') === 'one') {
        return (
          <>
            {filterCourseReport.get('subTree', List()).map((two, twoIndex) => (
              <React.Fragment key={twoIndex}>
                <ColumnType details={two} />
                {two.get('node', List()).map((three, threeIndex) => (
                  <React.Fragment key={threeIndex}>
                    <ColumnType details={three} />
                  </React.Fragment>
                ))}
              </React.Fragment>
            ))}
          </>
        );
      }
      if (filterCourseReport?.get('level') === 'two') {
        return (
          <>
            {filterCourseReport.get('node', List()).map((three, threeIndex) => (
              <React.Fragment key={threeIndex}>
                <ColumnType details={three} />
              </React.Fragment>
            ))}
          </>
        );
      }
      if (filterCourseReport?.get('level') === 'three') {
        const getAssessmentName = (question) => {
          let assessmentName = '';
          questionList.forEach((one) => {
            one.get('questionMarks', List()).forEach((two) => {
              if (two.get('_id', '') === question.get('_id', ''))
                assessmentName = one.get('assessmentName', '');
            });
          });

          return assessmentName
            .split(/\s/)
            .reduce((response, word) => (response += word.slice(0, 1)), '');
        };

        return (
          <>
            {questionMarks.map((question, questionIndex) => (
              <>
                {type === 'header' ? (
                  <th scope="col" className="borderNone">
                    <div className={`cw_160`}>
                      <p className="thHeaderReport bold">
                        {getAssessmentName(question)} {question.get('questionName', '')} (
                        {question.get('totalMark', '')})
                      </p>
                    </div>
                  </th>
                ) : type === 'subHeader' ? (
                  <td className="attainment_border_bottom bg-gray" key={questionIndex}>
                    <p className="thHeaderAttainment f-15">
                      {question
                        .get('outComeIds', List())
                        .map((co) => getCloName(co))
                        .join(', ')}
                    </p>
                  </td>
                ) : (
                  <td className="attainment_border_bottom" key={questionIndex}>
                    <p className="thHeaderAttainment f-15">
                      {getTooltipData(question, cloId) ? (
                        <Tooltips
                          title={
                            <>
                              <div>{question.get('questionName')}</div>
                              <div className="d-flex">
                                <div>
                                  <div>Mark</div>
                                  <div>{question.get('totalMark')}</div>
                                </div>
                                <div className="float-right ml-3">
                                  <div>Min - Attainment</div>
                                  <div>{question.get('attainmentBenchMark')}</div>
                                </div>
                              </div>
                            </>
                          }
                        >
                          <span>{getMark(question, question.get('average'), cloId)}</span>
                        </Tooltips>
                      ) : (
                        <span>{getMark(question, question.get('average'), cloId)}</span>
                      )}
                    </p>
                  </td>
                )}
              </>
            ))}
          </>
        );
      }
    } else {
      return courseProgramReport
        .get('attainmentNodeList', List())
        .slice(1)
        .map((one, oneIndex) => (
          <React.Fragment key={oneIndex}>
            <ColumnType details={one} />
            {one.get('subTree', List()).map((two, twoIndex) => (
              <React.Fragment key={twoIndex}>
                <ColumnType details={two} />
                {two.get('node', List()).map((three, threeIndex) => (
                  <React.Fragment key={threeIndex}>
                    <ColumnType details={three} />
                  </React.Fragment>
                ))}
              </React.Fragment>
            ))}
          </React.Fragment>
        ));
    }
  };
  ReportTableColumn.propTypes = {
    item: PropTypes.instanceOf(Map),
    type: PropTypes.string,
    cloId: PropTypes.string,
    disableClass: PropTypes.bool,
  };

  return (
    <>
      {selectedNode !== '-' && (
        <>
          <div className="border border-radious-6 mt-4 mb-5">
            <div className="attainment_table">
              {attainmentType === 'Class Attainment' ? (
                <table align="left">
                  <thead>
                    <tr>
                      <NodeParentHeading />
                      <ReportTableColumn type="header" />
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <th scope="col" className="attainment_border_bottom bg-gray">
                        <div className="cw_300 row">
                          <div className="col-6 pr-0">
                            <p className="thHeaderReport text-left">Attainment</p>
                          </div>
                          <div className="col-6 pl-0">
                            <p className="thHeaderReport ">%</p>
                          </div>
                        </div>
                      </th>

                      <ReportTableColumn type="subHeader" />
                    </tr>

                    {courseProgramReport
                      .get(outCome === 'clo' ? 'courseCLO' : 'programPLO', List())
                      .map((detail, index) => (
                        <tr key={index}>
                          <th scope="col" className="attainment_border_bottom bg-white">
                            <div className="cw_300 row">
                              <div className="col-6 pr-0">
                                <p className="thHeaderReport text-left">
                                  <Tooltips title={detail.get('name', '')}>
                                    <span>
                                      {indVerRename(
                                        selectValues.getIn(['outcome', 'value'], '').toUpperCase(),
                                        programId
                                      )}{' '}
                                      {/* {selectValues.getIn(['outcome', 'value'], '').toUpperCase()}{' '} */}
                                      {detail.get('no', '')}
                                    </span>
                                  </Tooltips>
                                </p>
                              </div>
                              <div className="col-6 pl-0">
                                <AttainmentDetails
                                  details={
                                    selectedType
                                      ? filterCourseReport
                                      : courseProgramReport.getIn(['attainmentNodeList', 0], Map())
                                  }
                                  cloId={detail.get('_id', '')}
                                />
                              </div>
                            </div>
                          </th>
                          <ReportTableColumn
                            type="body"
                            item={detail}
                            disableClass={true}
                            cloId={detail.get('_id', '')}
                          />
                        </tr>
                      ))}
                  </tbody>
                </table>
              ) : (
                <AttainmentStudentReportTable
                  courseProgramReport={courseProgramReport}
                  selectedNode={selectedNode}
                  selectValues={selectValues}
                  selectedType={selectedType}
                  filterCourseReport={filterCourseReport}
                  getMarkColor={getMarkColor}
                  getAttainmentTargetMarkColor={getAttainmentTargetMarkColor}
                />
              )}
            </div>
            <AttainmentAchieved />
          </div>
        </>
      )}
    </>
  );
}

AttainmentReportTable.propTypes = {
  courseProgramReport: PropTypes.instanceOf(Map),
  selectedNode: PropTypes.string,
  selectedType: PropTypes.string,
  questionList: PropTypes.instanceOf(List),
  setQuestionList: PropTypes.func,
  selectedId: PropTypes.string,
  setSelectedId: PropTypes.func,
  fetchApi: PropTypes.func,
  selectedCoTreeId: PropTypes.string,
  setSelectedCoTreeId: PropTypes.func,
  selectValues: PropTypes.instanceOf(Map),
};

export default AttainmentReportTable;
