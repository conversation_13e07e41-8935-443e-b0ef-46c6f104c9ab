import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import readXlsxFile from 'read-excel-file';
import { Modal, Button, Table } from 'react-bootstrap';
import { Trans, withTranslation } from 'react-i18next';
import { List } from 'immutable';

import ErrorModal from '../../../StudentGrouping/Modal/ErrorModal';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  selectInvalidDataList,
  selectValidData,
} from '../../../../_reduxapi/program_input/selectors';
import { getURLParams, getLang } from '../../../../utils';
import { t } from 'i18next';
import { exportToExcel } from 'Modules/ProgramInput/utils';

const lng = getLang();
class ImportFromProgramFile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      exportShow: true,
      importShow: false,
      importStudent: false,
      importStudentList: false,
      directValid: true,
      chooseNational: '',
      csvRecords: [],
      group1: true,
      group2: false,
      group3: false,
      importAlert: false,
      importTable: false,
      filename: null,
      dataCheckShow: true,
      invalidList: [],
      importFormatError: false,
      importBtnEnable: false,
      programId: getURLParams('_id', true),
      curriculumId: getURLParams('_curriculum_id', true),
    };
  }

  handleChange = (event) => {
    if (
      event.target.files[0].type !== 'application/vnd.ms-excel' &&
      event.target.files[0].type !==
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      this.setState({ importFormatError: true });
    } else {
      this.setState({ importFormatError: false, tempMismatch: false });
      this.getRecords(event.target.files[0]);
    }
  };

  handleChangeData = () => {
    this.setState({
      filename: null,
      csvRecords: [],
      importBtnEnable: false,
      tempMismatch: false,
    });
  };
  async getRecords(csvFileObject) {
    const map = {
      [t('program_input.sample_data_headers.Program_Name')]: 'Program_Name',
      [t('program_input.sample_data_headers.Program_Code')]: 'Program_Code',
      [t('program_input.sample_data_headers.Type')]: 'Type',
      [t('program_input.sample_data_headers.Program_Type')]: 'Program_Type',
      [t('program_input.sample_data_headers.Program_Level')]: 'Program_Level',
      [t('program_input.sample_data_headers.Degree_Name')]: 'Degree_Name',
      [t('program_input.sample_data_headers.No_of_Terms')]: 'No_of_Terms',
    };
    var filename = csvFileObject.name;
    let Program = ['Degree_Name', 'No_of_Terms'];
    this.setState({ filename });

    await readXlsxFile(csvFileObject, { map }).then(({ rows }) => {
      if (rows[0] === undefined) {
        this.setState({ tempMismatch: true });
        return true;
      }
      let tempMismatch = Object.keys(rows[0]).filter((element) => Program.includes(element));
      if (tempMismatch.length === 0) {
        this.setState({ tempMismatch: true });
        return true;
      }

      this.setState({ csvRecords: rows, importBtnEnable: true });
    });
  }

  sampleData = () => {
    const sampleData = [
      {
        [t('program_input.sample_data_headers.Program_Name')]: '',
        [t('program_input.sample_data_headers.Program_Code')]: '',
        [t('program_input.sample_data_headers.Type')]: '',
        [t('program_input.sample_data_headers.Program_Type')]: '',
        [t('program_input.sample_data_headers.Program_Level')]: '',
        [t('program_input.sample_data_headers.Degree_Name')]: '',
        [t('program_input.sample_data_headers.No_of_Terms')]: '',
      },
    ];

    exportToExcel(sampleData, 'sampleProgram');
  };

  afterImport = () => {
    const { ImportProgramCsvCheck } = this.props;
    const { csvRecords } = this.state;
    this.setState({
      dataCheckShow: false,
    });
    const Records = csvRecords.map((item) => {
      const NO_OF_TERMS = item?.No_of_Terms;
      if (NO_OF_TERMS) {
        var Terms = NO_OF_TERMS?.split(',');
        var list = [];

        for (let i = 1; i <= Terms.length - 1; i++) {
          list.push({
            no: i,
            name: String(Terms[i]).trim(),
          });
        }
      }

      return {
        Program_Name:
          String(item.Program_Name) === 'undefined' ? '' : String(item.Program_Name).trim(),
        Program_Code:
          String(item.Program_Code) === 'undefined' ? '' : String(item.Program_Code).trim(),
        ...(item.Type.toLowerCase() === 'program' && {
          Type: String(item.Type) === 'undefined' ? '' : String(item.Type).trim(),
          Program_Type:
            String(item.Program_Type) === 'undefined' ? '' : String(item.Program_Type).trim(),
          Program_Level:
            String(item.Program_Level) === 'undefined' ? '' : String(item.Program_Level).trim(),
          Degree_Name:
            String(item.Degree_Name) === 'undefined' ? '' : String(item.Degree_Name).trim(),
          No_of_Terms: NO_OF_TERMS ? Number(Terms[0]) : undefined,
          Terms: NO_OF_TERMS ? list : undefined,
        }),
        Type: String(item.Type) === 'undefined' ? '' : String(item.Type).trim(),

        No_of_Terms: NO_OF_TERMS ? Number(Terms[0]) : undefined,
        Terms: NO_OF_TERMS ? list : undefined,
      };
    });

    let body = {
      programs: Records,
    };

    ImportProgramCsvCheck(
      `/digi_program/data_check_program`,
      body,
      () => {
        this.inValidDataCheck();
      },
      () => {
        this.validDataCheck();
      }
    );
  };

  validDataCheck = () => {
    this.props.closed(false);
  };
  fileName = () => {
    this.setState({
      filename: null,
      tempMismatch: false,
    });
  };

  importContinue = () => {
    const { ValidDataList, ImportCsv } = this.props;

    if (ValidDataList.size === 0) {
      this.props.closed(false);
    }
    if (ValidDataList.size > 0) {
      let listData = this.props.ValidDataList && this.props.ValidDataList.toJS();

      let data = listData.map((row) => {
        const TermsList = [];
        for (let i = 0; i < row.data.No_of_Terms; i++) {
          TermsList.push({
            no: row.data.Terms[i].no,
            name: String(row.data.Terms[i].name),
          });
        }
        //todo
        return {
          Program_Name:
            String(row.data.Program_Name) === 'undefined'
              ? ''
              : String(row.data.Program_Name).trim(),
          Program_Code:
            String(row.data.Program_Code) === 'undefined'
              ? ''
              : String(row.data.Program_Code).trim(),
          ...(row.data.Type.toLowerCase() === 'program' && {
            Type: String(row.data.Type) === 'undefined' ? '' : String(row.data.Type).trim(),
            Program_Type:
              String(row.data.Program_Type) === 'undefined'
                ? ''
                : String(row.data.Program_Type).trim(),
            Program_Level:
              String(row.data.Program_Level) === 'undefined'
                ? ''
                : String(row.data.Program_Level).trim(),
            Degree_Name:
              String(row.data.Degree_Name) === 'undefined'
                ? ''
                : String(row.data.Degree_Name).trim(),
            No_of_Terms: row.data.No_of_Terms,
            Terms: row.data.No_of_Terms ? TermsList : undefined,
          }),
          Type: String(row.data.Type) === 'undefined' ? '' : String(row.data.Type).trim(),

          No_of_Terms: row.data.No_of_Terms,
          Terms: row.data.No_of_Terms ? TermsList : undefined,
        };
      });
      let body = {
        programs: data,
      };

      ImportCsv(`digi_program/import_program`, body, () => {
        this.props.closed(false);
      });
    }
  };
  importListBack = () => {
    this.setState({
      dataCheckShow: true,
      directValid: true,
    });
  };
  exportList = () => {
    let listData = this.props.InvalidDataList && this.props.InvalidDataList.toJS();
    let data = listData.map((row) => {
      const TermsList = row.data.Terms;
      if (TermsList) {
        var list = [];
        for (let i = 0; i < TermsList?.length; i++) {
          list.push(row.data.Terms[i].no);
          list.push(row.data.Terms[i].name);
        }
      }

      return {
        [t('program_input.sample_data_headers.Program_Name')]: row.data.Program_Name,
        [t('program_input.sample_data_headers.Program_Code')]: row.data.Program_Code,
        [t('program_input.sample_data_headers.Program_Type')]: row.data.Program_Type,
        [t('program_input.sample_data_headers.Program_Level')]: row.data.Program_Level,
        [t('program_input.sample_data_headers.Type')]: row.data.Type,
        [t('program_input.sample_data_headers.Degree_Name')]: row.data.Degree_Name,
        [t('program_input.sample_data_headers.No_of_Terms')]: TermsList ? list.toString() : '',
      };
    });

    exportToExcel(data, 'InvalidProgramList');
  };

  cancelBtn = () => {
    this.setState(
      {
        dataCheckShow: false,
      },
      () => {
        this.props.closed(false);
      }
    );
  };

  inValidDataCheck = () => {
    this.setState({ directValid: false });
  };
  render() {
    const {
      filename,
      csvRecords,
      directValid,
      dataCheckShow,
      importFormatError,
      tempMismatch,
      importBtnEnable,
    } = this.state;
    const { InvalidDataList, ValidDataList, t } = this.props;

    return (
      <div className="main pt-3 pb-5 bg-white">
        <Modal show={dataCheckShow} dialogClassName="model-800">
          <div className="container">
            <p className="f-20 mb-2 pt-3">
              {' '}
              <Trans i18nKey={'import_program'}></Trans>{' '}
            </p>
          </div>

          <Modal.Body>
            <div className="model-main bg-gray pl-3 border-radious-8">
              <p className="f-14 mb-2 pt-2 text-gray">
                {' '}
                <Trans i18nKey={'select_appropriate'}></Trans>
              </p>

              <div className="row pb-5">
                <div className="col-md-5">
                  <b className="f-16 mb-4 pt-3">
                    <Trans i18nKey={'select_a_file'}></Trans> <br></br>
                    <small>
                      <Trans i18nKey={'accepted_formats'}></Trans>{' '}
                    </small>
                  </b>
                  {filename === null ? (
                    ''
                  ) : (
                    <div className="bg-lightdark border-radious-8 w-75 ">
                      <b className=" pl-2 pr-2 f-14" onClick={this.handleChangeData}>
                        {filename.length > 15 ? filename.substring(0, 4) + '...' : filename}{' '}
                        <i
                          className={`fa fa-times remove_hover import_remove_icon ${
                            lng === 'ar' ? 'float-right' : ''
                          }`}
                          aria-hidden="true"
                        ></i>
                      </b>
                    </div>
                  )}
                </div>

                <div className="col-md-7">
                  <b className="pl-5">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary  import-padding border-radious-8"
                    >
                      <Trans i18nKey={'browse'}></Trans>
                      <input
                        id="fileUpload"
                        type="file"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        value=""
                        onChange={this.handleChange}
                      />
                    </label>
                  </b>
                  <Link>
                    <b
                      className="pl-5 text-blue"
                      onClick={() => {
                        this.sampleData();
                      }}
                    >
                      {' '}
                      <i className="fa fa-download pr-2" aria-hidden="true"></i>
                      <Trans i18nKey={'download_template'}></Trans>
                    </b>
                  </Link>
                </div>
              </div>

              <div className="border-radious-8 bg-lightgray">
                <div className="row d-flex justify-content-center">
                  <div className="col-md-11 pt-5 ">
                    <div className="p-2 bg-lightdark border-radious-8">
                      <Table className="">
                        <thead>
                          <tr className="no-border">
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_code'}></Trans>{' '}
                              </Button>
                            </th>

                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_type'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'type'}></Trans>
                              </Button>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {csvRecords &&
                            csvRecords
                              .filter((val, i) => i < 3)
                              .map((item, i) => {
                                return (
                                  <tr className="border-dark-import" key={i}>
                                    <td className="border-left-import overflow_wrap">
                                      {item.Program_Name}
                                    </td>
                                    <td className="border-left-import overflow_wrap">
                                      {item.Program_Code}
                                    </td>

                                    <td className="border-left-import overflow_wrap">
                                      {item.Program_Type}
                                    </td>
                                    <td className="border-left-import overflow_wrap">
                                      <Trans i18nKey={item.Type}></Trans>
                                    </td>
                                  </tr>
                                );
                              })}
                        </tbody>
                      </Table>
                    </div>
                  </div>

                  <div className="col-md-11 pt-2 pb-2">
                    <b className="float-right f-14">
                      {csvRecords.length} <Trans i18nKey={'rows_import'}></Trans>{' '}
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <div className={`${lng === 'ar' ? 'pl-4' : 'pr-4'}`}>
              <p className="text-blue mb-0 remove_hover" onClick={this.cancelBtn}>
                {' '}
                <Trans i18nKey={'cancel'}></Trans>{' '}
              </p>
            </div>

            <div className="pr-2">
              <Button
                variant={importBtnEnable && csvRecords.length > 0 ? 'primary' : 'secondary'}
                disabled={importBtnEnable && csvRecords.length > 0 ? false : true}
                onClick={this.afterImport}
              >
                <Trans i18nKey={'import'}></Trans>
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {!directValid && (
          <Modal show={true} size="lg">
            <div className="container">
              <p className="f-20 mb-1 pt-3">
                {' '}
                <Trans i18nKey={'import_program'}></Trans>
              </p>

              <p className="f-14 mb-2 ">
                {' '}
                {InvalidDataList?.size} <Trans i18nKey={'of'}></Trans> {csvRecords.length}{' '}
                <Trans i18nKey={'not_imported'}></Trans>
              </p>
            </div>

            <Modal.Body>
              <div className="pb-1">
                <p className="f-16 mb-2">
                  {' '}
                  <Trans i18nKey={'data_check'}></Trans>
                </p>
                <div className="row">
                  <div className="col-md-6">
                    <p className="f-14 mb-2">
                      <Trans i18nKey={'list_error_entity'}></Trans>
                    </p>
                  </div>
                  <p
                    className="f-14 mb-2 float-right text-blue cursor-pointer"
                    onClick={() => this.exportList()}
                  >
                    <Trans i18nKey={'export_entry'}></Trans>
                  </p>
                </div>

                <div className="go-wrapper">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>
                          <div className="aw-75">
                            <Trans i18nKey={'s_no'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-100">
                            <Trans i18nKey={'program_name'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-200">
                            <Trans i18nKey={'program_code'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-300">
                            <Trans i18nKey={'error_message'}></Trans>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="go-wrapper-height tr-change">
                      {InvalidDataList &&
                        InvalidDataList.map((item, i) => {
                          return (
                            <tr key={i}>
                              <td>
                                <div className="aw-75">{i + 1}</div>
                              </td>
                              <td>
                                <div className="aw-100">{item.getIn(['data', 'Program_Name'])}</div>
                              </td>
                              <td>
                                <div className="aw-200">{item.getIn(['data', 'Program_Code'])}</div>
                              </td>

                              <td>
                                <div className="aw-300">
                                  {' '}
                                  {item.getIn(['message']).map((message, i) => (
                                    <li key={i}>{message}</li>
                                  ))}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <div className="pr-4">
                <p className="text-blue mb-0 remove_hover" onClick={this.importListBack}>
                  {' '}
                  <Trans i18nKey={'back'}></Trans>{' '}
                </p>
              </div>

              <div className="pr-2">
                <Button
                  variant={ValidDataList && ValidDataList.size > 0 ? 'primary' : 'secondary'}
                  disabled={ValidDataList && ValidDataList.size === 0}
                  onClick={this.importContinue}
                >
                  <Trans i18nKey={'continue'}></Trans>
                </Button>
              </div>
            </Modal.Footer>
          </Modal>
        )}
        {importFormatError && (
          <div>
            <ErrorModal
              showDetail={importFormatError}
              title={t('modal.messages.invalid_format')}
              content={t('modal.messages.upload_error')}
              filename={this.fileName}
            />
          </div>
        )}

        {tempMismatch && (
          <div>
            <ErrorModal
              showDetail={tempMismatch}
              title={t('modal.messages.template_mismatch')}
              content={t('modal.messages.template_content')}
              filename={this.fileName}
            />
          </div>
        )}
        {/* import table modalend */}
      </div>
    );
  }
}

ImportFromProgramFile.propTypes = {
  ImportProgramCsvCheck: PropTypes.func,
  closed: PropTypes.func,
  t: PropTypes.func,
  ValidDataList: PropTypes.instanceOf(List),
  ImportCsv: PropTypes.func,
  InvalidDataList: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    InvalidDataList: selectInvalidDataList(state),
    ValidDataList: selectValidData(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions)
)(withTranslation()(ImportFromProgramFile));
