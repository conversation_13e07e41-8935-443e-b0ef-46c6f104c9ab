import React from 'react';
import { useHistory, withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Map } from 'immutable';
import AssessmentPlanningDashboard from './AssessmentPlanning';
import * as actions from '../../../../_reduxapi/assessment/action';
import { Years } from '../../../../constants';
import { Header } from './Header';
import { getURLParams } from 'utils';

function CourseList({ programId, activeInstitutionCalendar }) {
  const history = useHistory();
  const type = getURLParams('type', true);
  const cName = getURLParams('cName', true);
  const year = getURLParams('year', true);
  const level = getURLParams('level', true);
  const programName = getURLParams('pname', true);
  const formattedYear = year !== '' ? Years[year.replace('year', '')].name : '';
  const heading =
    type === 'program'
      ? `${programName} Program Level`
      : `${programName} Program / ${formattedYear}, ${level} / ${cName}`;
  return (
    <div className="main pb-5 bg-mainBackground">
      <Header history={history} programName={heading} isConfigurePages={true} type={type} />
      <div className="container pl-0">
        <div className="p-3">
          <AssessmentPlanningDashboard
            programId={programId}
            activeInstitutionCalendar={activeInstitutionCalendar}
            showMarks={true}
            history={history}
          />
        </div>
      </div>
    </div>
  );
}

CourseList.propTypes = {
  history: PropTypes.object,
  programId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

export default compose(withRouter, connect(null, actions))(CourseList);
