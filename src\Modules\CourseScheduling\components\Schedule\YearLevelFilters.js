import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import SortIcon from '../../../../Assets/sort.png';
import { t } from 'i18next';
import { getVersionName, indVerRename, levelRename, stringToUC } from '../../../../utils';

function YearLevelFilters({
  data,
  filter,
  handleChange,
  handleSortDirectionChange,
  sortDirection,
  showCourse = true,
  resetLabelName = t('all'),
  programId,
}) {
  function getTerms() {
    return data.get('term', List());
  }

  function getYearsOrLevels(key) {
    const term = filter.get('term', '') || data.getIn(['term', 0], '');
    let i = 1;
    return data
      .get('courses', List())
      .filter((level) => {
        const incTerm = level.get('term') === term;
        if (key === 'year' || (key === 'level_no' && !filter.get('year'))) return incTerm;
        const incYear = level.get('year') === filter.get('year');
        return incTerm && incYear;
      })
      .reduce((acc, level) => {
        const value = level.get(key);
        return acc.set(
          value,
          Map({
            name: key === 'year' ? value.split('year').join('Year ') : value,
            value,
            inc: key === 'level_no' ? i++ : 0,
          })
        );
      }, Map())
      .valueSeq()
      .toList();
  }

  function getCourses() {
    const term = filter.get('term', '');
    const year = filter.get('year', '');
    const levelNo = filter.get('level', '');
    if (!term || !year || !levelNo) return List();
    const levelData = data
      .get('courses', List())
      .find(
        (level) =>
          level.get('term') === term &&
          level.get('year') === year &&
          level.get('level_no') === levelNo
      );
    if (!levelData) return List();
    return levelData.get('courses', List()).map((course) =>
      Map({
        name: course.get('courses_name'),
        value: course.get('_course_id'),
        versionName: getVersionName(course),
      })
    );
  }

  return (
    <div className="pb-2 border-bottom-partial-gray">
      <div className="row">
        <div className="col-md-3">
          <div className="f-14">{stringToUC(indVerRename('Term', programId))}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filter.get('term', '')}
              onChange={(e) => handleChange(e.target.value, 'term')}
            >
              {getTerms().map((term) => (
                <option key={term} value={term}>
                  {term}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>

        <div className="col-md-9">
          <div className="row align-items-end">
            <div className="col-md-1 pl-0 pr-0">
              <div
                className="course_sort_icon bg-gray cursor-pointer"
                onClick={handleSortDirectionChange}
              >
                <img
                  src={SortIcon}
                  alt="sort"
                  className={`img-fluid ${sortDirection === 'asc' ? 'rotate-180' : ''}`}
                />
              </div>
            </div>

            <div className="col-md-3 pl-2 pr-1">
              <div className="f-14">{t('year')}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filter.get('year', '')}
                  onChange={(e) => handleChange(e.target.value, 'year')}
                >
                  <option value="">{resetLabelName}</option>
                  {getYearsOrLevels('year').map((year) => (
                    <option key={year.get('value')} value={year.get('value')}>
                      {year.get('name').replace('Year', t('year'))}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </div>

            <div className="col-md-3 pl-2 pr-1">
              <div className="f-14">{stringToUC(indVerRename('Level', programId))}</div>
              <FormControl fullWidth variant="outlined" size="small">
                <Select
                  native
                  value={filter.get('level', '')}
                  onChange={(e) => handleChange(e.target.value, 'level')}
                >
                  <option value="">{resetLabelName}</option>
                  {getYearsOrLevels('level_no')
                    .sort((a, b) => a.get('inc') - b.get('inc'))
                    .map((level) => (
                      <option key={level.get('value')} value={level.get('value')}>
                        {levelRename(level.get('name'), programId)}
                      </option>
                    ))}
                </Select>
              </FormControl>
            </div>

            {showCourse && (
              <div className="col-md-3 pl-2 pr-1">
                <div className="f-14">{t('program_calendar.course')}</div>
                <FormControl fullWidth variant="outlined" size="small">
                  <Select
                    native
                    value={filter.get('course', '')}
                    onChange={(e) => handleChange(e.target.value, 'course')}
                  >
                    <option value="">{resetLabelName}</option>
                    {getCourses().map((course) => (
                      <option key={course.get('value')} value={course.get('value')}>
                        {course.get('name')}
                        {course.get('versionName', '')}
                      </option>
                    ))}
                  </Select>
                </FormControl>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

YearLevelFilters.propTypes = {
  data: PropTypes.instanceOf(Map),
  filter: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  handleSortDirectionChange: PropTypes.func,
  sortDirection: PropTypes.string,
  showCourse: PropTypes.bool,
  resetLabelName: PropTypes.string,
  programId: PropTypes.string,
};

export default YearLevelFilters;
