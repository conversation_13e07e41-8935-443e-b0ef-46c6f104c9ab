import React, { Fragment, useState } from 'react';
import ParameterModal from '../../ParameterModal';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import CreateIcon from '@mui/icons-material/Create';

export default function EditParameterModal({ parameter }) {
  const [open, setOpen] = useState(false);

  return (
    <Fragment>
      <CreateIcon
        size="small"
        className="text-gray"
        onClick={(e) => {
          e.stopPropagation();
          setOpen('edit');
        }}
      />
      {open && <ParameterModal parameter={parameter} onClose={() => setOpen(false)} />}
    </Fragment>
  );
}
EditParameterModal.propTypes = {
  parameter: PropTypes.instanceOf(Map),
};
