import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { connect } from 'react-redux';

import * as actions from '../../../_reduxapi/program_input/v2/actions';
import { selectPgmInput, selectIsLoading } from '../../../_reduxapi/program_input/v2/selectors';
import Header from './Header/Header';
import TableWithPagination from './Table/TableWithPagination';
import parentContext from './Context/Context';
import { getInstitutionHeader } from '../../../v2/utils';
import { GetConfigSettings } from 'Modules/Shared/v2/Configurations';

function GlobalTableIndex({ getPgmInput, programInputList, history, isLoading }) {
  const [filter, setFilter] = useState('all');
  const [tabValue, setValue] = useState('active');
  const [search, setSearch] = useState('');
  const institutionHeader = getInstitutionHeader(history);
  const institutionId = institutionHeader?._institution_id;
  const [limit, setLimit] = useState(10);
  const selectedLanguageData = GetConfigSettings({});

  useEffect(() => {
    pgmInputFun({});
  }, [setLimit, setSearch, setFilter]); // eslint-disable-line

  function pgmInputFun({ pageNo = 1, searchKey = '', tab = 'active', programType = '' }) {
    let params = { pageNo, limit, tab };
    if (searchKey) params.searchKey = searchKey;
    if (programType && programType !== 'all') params.programType = programType;
    getPgmInput({
      institutionId,
      params,
      header: institutionHeader,
    });
  }

  const getPaginationData = (pageNo, searchKey, tab, programType) => {
    pgmInputFun({ tab, searchKey, pageNo, programType });
  };

  const getSearchedAndFilteredData = (searchKey, tab, programType) => {
    pgmInputFun({ tab, searchKey, programType });
  };

  return (
    <div className="container">
      <div className="pt-2 pb-5">
        <div className="course_master">
          <parentContext.PIL_Context.Provider
            value={{
              programInputList,
              institutionId,
              selectedLanguageData,
              getSearchedAndFilteredData,
              getPaginationData,
              limit,
              setLimit,
              isLoading,
            }}
          >
            <parentContext.searchFilterContext.Provider
              value={{ filter, tabValue, setValue, search, setSearch, setFilter }}
            >
              <Header />
              <div className="program_border pt-3"></div>
              <TableWithPagination />
            </parentContext.searchFilterContext.Provider>
          </parentContext.PIL_Context.Provider>
        </div>
      </div>
    </div>
  );
}

GlobalTableIndex.propTypes = {
  programInputList: PropTypes.instanceOf(Map),
  getPgmInput: PropTypes.func,
  programInputSettings: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  isLoading: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    programInputList: selectPgmInput(state),
    isLoading: selectIsLoading(state),
  };
};

export default connect(mapStateToProps, actions)(GlobalTableIndex);
