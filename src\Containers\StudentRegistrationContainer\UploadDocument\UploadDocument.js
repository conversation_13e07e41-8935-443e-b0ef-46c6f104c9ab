import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Input from '../../../Widgets/FormElements/Input/Input';
import Button from '../../../Widgets/FormElements/Button/Button';
import axios from '../../../axios';
import Loader from '../../../Widgets/Loader/Loader';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import { data, validation } from './data';
import {
  envSignUpService,
  getURLParams,
  hasUserSensitiveData,
  removeDSCookie,
} from '../../../utils';
import VaccinationDetails from '../../../Modules/UserManagement/VaccinationDetails';
import { t } from 'i18next';
import LocalStorageService from 'LocalStorageService';
let id;

class UploadDocument extends Component {
  state = {
    ...data,
    id: getURLParams('id'),
  };

  componentDidMount = (id) => {
    id = this.props.location.search.split('=')[1];
    this.fetchApi(id);
  };

  fetchApi = (id) => {
    const universe = {
      field: ['name'],
    };
    this.setState({ isLoading: true });

    axios
      .get(`/country`, universe)
      .then((res) => {
        const university = res.data.data.map((data) => {
          return {
            name: data.name,
            value: data._id,
          };
        });
        university.unshift({
          name: '',
          value: '',
        });
        this.setState({
          university: university,
          univer: university[0].value,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });

    axios
      .get(`/user/student/${id}`)
      .then((res) => {
        const staff = res.data.data;

        this.setState({
          academic: staff.academic,
          residendId: staff.address.nationality_id,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  handleImageChange = (e, name) => {
    // e.preventDefault();

    if (name === 'appointment') {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }

      const size = Math.ceil(parseFloat(file.size / 1024));
      if (size > 6000) {
        this.setState({
          appointmentError: 'image size should not be greater than 6 mb',
        });
        return;
      }

      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== 'image') {
        this.setState({
          appointmentError: 'Image files only allowed',
        });
        return;
      }
      if (file !== null && file !== undefined) {
        NotificationManager.success(t(`user_management.image_added`));

        reader.onloadend = () => {
          this.setState({
            appointment: file,
            appointmentError: '',
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === 'degreeImage') {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }

      const size = Math.ceil(parseFloat(file.size / 1024));
      if (size > 6000) {
        this.setState({
          degreeImageError: 'image size should not be greater than 6 mb',
        });
        return;
      }

      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== 'image') {
        this.setState({
          degreeImageError: 'Image files only allowed',
        });
        return;
      }
      if (file !== null && file !== undefined) {
        NotificationManager.success(t(`user_management.image_added`));

        reader.onloadend = () => {
          this.setState({
            degreeImage: file,
            degreeImageError: '',
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === 'addressImage') {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }

      const size = Math.ceil(parseFloat(file.size / 1024));
      if (size > 6000) {
        this.setState({
          addressImageError: 'image size should not be greater than 6 mb',
        });
        return;
      }

      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== 'image') {
        this.setState({
          addressImageError: 'Image files only allowed',
        });
        return;
      }
      if (file !== null && file !== undefined) {
        NotificationManager.success(t(`user_management.image_added`));

        reader.onloadend = () => {
          this.setState({
            addressImage: file,
            addressImageError: '',
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === 'collegeImage') {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }

      const size = Math.ceil(parseFloat(file.size / 1024));
      if (size > 6000) {
        this.setState({
          collegeImageError: 'image size should not be greater than 6 mb',
        });
        return;
      }

      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== 'image') {
        this.setState({
          collegeImageError: 'Image files only allowed',
        });
        return;
      }
      if (file !== null && file !== undefined) {
        NotificationManager.success(t(`user_management.image_added`));

        reader.onloadend = () => {
          this.setState({
            collegeImage: file,
            collegeImageError: '',
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === 'residentImage') {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }

      const size = Math.ceil(parseFloat(file.size / 1024));
      if (size > 6000) {
        this.setState({
          residentImageError: 'image size should not be greater than 6 mb',
        });
        return;
      }

      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== 'image') {
        this.setState({
          residentImageError: 'Image files only allowed',
        });
        return;
      }
      if (file !== null && file !== undefined) {
        NotificationManager.success(t(`user_management.image_added`));

        reader.onloadend = () => {
          this.setState({
            residentImage: file,
            residentImageError: '',
          });
        };
        reader.readAsDataURL(file);
      }
    }

    if (name === 'address1') {
      let reader = new FileReader();
      let file = e.target.files[0];
      if (file === null || file === undefined) {
        return;
      }

      const size = Math.ceil(parseFloat(file.size / 1024));
      if (size > 6000) {
        this.setState({
          address1Error: 'image size should not be greater than 6 mb',
        });
        return;
      }

      const mimeType = file.type;
      if (mimeType.substr(0, 5) !== 'image') {
        this.setState({
          address1Error: 'Image files only allowed',
        });
        return;
      }
      if (file !== null && file !== undefined) {
        NotificationManager.success(t(`user_management.image_added`));

        reader.onloadend = () => {
          this.setState({
            address1: file,
            address1Error: '',
          });
        };
        reader.readAsDataURL(file);
      }
    }
  };

  handleSubmit = (e) => {
    id = this.props.location.search.split('=')[1];
    e.preventDefault();
    this.setState({
      isValidate: false,
    });
    if (validation.call(this)) {
      let data = new FormData();
      data.append('id', id);
      data.append('_school_certificate_doc', this.state.residentImage);
      data.append('_entrance_exam_certificate_doc', this.state.collegeImage);
      data.append('_admission_order_doc', this.state.addressImage);
      data.append('_college_id_doc', this.state.appointment);
      data.append('_nationality_id_doc', this.state.degreeImage);
      data.append('_address_doc', this.state.address1);
      this.setState({ isLoading: true, isValidate: true });
      axios
        .post(`/user/profile_document_update`, data)
        .then((res) => {
          NotificationManager.success(t(`user_management.staff_Document_Added_successfully`));
          this.setState({ isLoading: false });
          removeDSCookie('login-services');
          LocalStorageService.clearToken();
          this.props.history.push('/login');
        })
        .catch((error) => {
          NotificationManager.error(`${error.response.data.data}`);
          this.setState({ isLoading: false });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  render() {
    const documentMandatory = envSignUpService('DOCUMENT', true);
    const userSensitive = hasUserSensitiveData();
    return (
      <div>
        <div className="staffprofile pl-3 pr-3 pt-3">
          <h2 className="f-20 text-white"> Profile Information </h2>
          <div className="row pt-4 pb-1 ">
            <div className="col-xl-1 col-lg-1 col-md-1 col-3 text-white ">
              <div className="">
                <h3 className="f-16 text-center disabled-icon ">Personal</h3>
              </div>
            </div>
            <div className="col-xl-1 col-lg-2 col-md-2 col-3 text-white  ">
              <div className="border-bottom-white-2px">
                <h3 className="f-16 text-center ">Upload</h3>
              </div>
            </div>
          </div>
        </div>

        <div className="main p-2">
          <Loader isLoading={this.state.isLoading} />
          <NotificationContainer />
          <div className="container mt-4">
            <div className="float-right pb-3">
              <Button btnType="Success" clicked={this.handleSubmit}>
                {' '}
                {documentMandatory ? 'Submit' : 'Skip & Submit'}
              </Button>
            </div>
            <div className="clearfix"></div>

            <div className=" outter ">
              <h3 className="f-16 ">
                {' '}
                Fill the below information and attach the corresponding documents
              </h3>
              <div className="row">
                <div className="col-md-5">
                  <p className="f-12 ">
                    {' '}
                    Accepted formats [ JPG, PNG ], Upload file max size [ 6mb ]
                  </p>
                  <div className="row border-bottom pt-3">
                    <div className="col-md-6">
                      <label className="toplabel">School Certificate</label>
                      <div>{this.state.residentImage.name}</div>
                    </div>
                    <div className="col-md-6 pt-3">
                      <Input
                        elementType={'imageUpload'}
                        elementConfig={{
                          type: 'file',
                        }}
                        class={'float-right'}
                        imagename={'Browse'}
                        accept="image/png, image/jpeg"
                        changed={(e) => this.handleImageChange(e, 'residentImage')}
                        feedback={this.state.residentImageError}
                      />
                    </div>
                  </div>

                  <div className="row border-bottom pt-3">
                    <div className="col-md-6">
                      <label className="toplabel">
                        {t('user_management.entrance_exam_certificate')}
                      </label>
                      <div>{this.state.collegeImage.name}</div>
                    </div>
                    <div className="col-md-6 pt-3">
                      <Input
                        elementType={'imageUpload'}
                        elementConfig={{
                          type: 'file',
                        }}
                        class={'float-right'}
                        imagename={'Browse'}
                        accept="image/png, image/jpeg"
                        changed={(e) => this.handleImageChange(e, 'collegeImage')}
                        feedback={this.state.collegeImageError}
                      />
                    </div>
                  </div>

                  <div className="row border-bottom pt-3">
                    <div className="col-md-6">
                      <label className="toplabel">Admission Document</label>
                      <div>{this.state.addressImage.name}</div>
                    </div>

                    <div className="col-md-6 pt-3">
                      <Input
                        elementType={'imageUpload'}
                        elementConfig={{
                          type: 'file',
                        }}
                        imagename={'Browse'}
                        accept="image/png, image/jpeg"
                        changed={(e) => this.handleImageChange(e, 'addressImage')}
                        feedback={this.state.addressImageError}
                      />
                    </div>
                  </div>

                  <div className="row border-bottom pt-3">
                    <div className="col-md-6">
                      <label className="toplabel"> College ID</label>
                      <p className="">{this.state.academic}</p>
                      <div>{this.state.appointment.name}</div>
                    </div>

                    <div className="col-md-6 pt-3">
                      <Input
                        elementType={'imageUpload'}
                        elementConfig={{
                          type: 'file',
                        }}
                        imagename={'Browse'}
                        accept="image/png, image/jpeg"
                        changed={(e) => this.handleImageChange(e, 'appointment')}
                        feedback={this.state.appointmentError}
                      />
                    </div>
                  </div>

                  <div className="row border-bottom pt-3">
                    <div className="col-md-6">
                      <label className="toplabel">
                        {' '}
                        National ID / Residence ID{' '}
                        {documentMandatory ? <span className="red">*</span> : ''}
                      </label>
                      <p className="">{this.state.residendId}</p>
                      <div>{this.state.degreeImage.name}</div>
                    </div>

                    <div className="col-md-6 pt-3">
                      <Input
                        elementType={'imageUpload'}
                        elementConfig={{
                          type: 'file',
                        }}
                        imagename={'Browse'}
                        accept="image/png, image/jpeg"
                        changed={(e) => this.handleImageChange(e, 'degreeImage')}
                        feedback={this.state.degreeImageError}
                      />
                    </div>
                  </div>

                  <div className="row border-bottom pt-3">
                    <div className="col-md-6">
                      <label className="toplabel"> National Address </label>
                      <div>{this.state.address1.name}</div>
                    </div>

                    <div className="col-md-6 pt-3">
                      <Input
                        elementType={'imageUpload'}
                        elementConfig={{
                          type: 'file',
                        }}
                        imagename={'Browse'}
                        accept="image/png, image/jpeg"
                        changed={(e) => this.handleImageChange(e, 'address1')}
                        feedback={this.state.address1Error}
                      />
                    </div>
                  </div>

                  {!userSensitive && this.state.id !== '' && (
                    <VaccinationDetails
                      edit={true}
                      userType="student"
                      permissionName={'NO NEED'}
                      userId={this.state.id}
                      type="image"
                    />
                  )}
                  <br />
                  <br />
                </div>
                <div className="col-md-2"></div>
              </div>
            </div>
          </div>
          <br />
          <br />
          <br />
          <br />
        </div>
      </div>
    );
  }
}

UploadDocument.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
};

export default UploadDocument;
