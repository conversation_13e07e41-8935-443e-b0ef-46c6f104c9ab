import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import MaterialInput from 'Widgets/FormElements/material/Input';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import CancelModal from 'Containers/Modal/Cancel';
import { fromJS, List, Map } from 'immutable';
import { documentValidation } from '../utils';
import { checkOnlyNumber } from 'v2/utils';

function DocumentModal({
  open,
  close,
  settingId,
  state,
  postDocument,
  categoryId,
  updateDocument,
  setOpen,
  isIndependent,
  maxSize,
  documents,
  setData,
  type,
}) {
  const [name, setName] = useState(state.get('labelName', ''));
  const [size, setSize] = useState(state.get('size'));
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [edited, setEdited] = useState(false);
  const [namesArray, setNamesArray] = useState(List);
  useEffect(() => {
    const tempNamesArray = documents
      .get('chooseDocuments', List())
      .filter((item, index) => item.get('documentCategory', '') === state.get('categoryName', ''))
      .reduce((_, el) => el)
      .get('document', List())
      .map((xy) => xy.get('labelName', '').toLowerCase());
    tempNamesArray.size > 0 && setNamesArray(tempNamesArray);
  }, []); //eslint-disable-line
  const handleChangeName = (e) => {
    setEdited(true);
    setName(e.target.value);
  };
  const handleChangeSize = (e) => {
    if (!checkOnlyNumber(e.target.value) && e.target.value !== '') {
      setData(Map({ message: `Please enter numbers only` }));
      return false;
    } else {
      setEdited(true);
      setSize(e.target.value);
    }
  };

  const editCallBack = () => {
    close();
  };
  const handleCancel = () => {
    edited ? setShowCancelModal(true) : setOpen(false);
  };

  const handleSubmit = () => {
    setEdited(false);
    const newArr = fromJS([{ labelName: name, size: isIndependent ? size : maxSize }]);
    const newNamesArray = namesArray.filter(
      (item) => item !== state.get('labelName', '').toLowerCase()
    );
    state.size > 1
      ? documentValidation(name, newNamesArray, setData, 'Document') &&
        updateDocument({
          operation: 'update',
          settingId,
          name,
          size: isIndependent ? size : maxSize,
          isActive: state.get('isActive', false),
          isMandatory: state.get('isMandatory', false),
          callBack: () => {
            setEdited(true);
            close();
          },
          categoryId,
          documentId: state.get('id', ''),
          type,
        })
      : documentValidation(name, namesArray, setData, 'Document') &&
        postDocument(
          {
            settingId,
            documentCategoryId: categoryId,
            document: newArr,
          },
          editCallBack,
          type
        );
  };
  return (
    <>
      <DialogModal show={open} onClose={handleCancel}>
        <div className="p-4">
          <div className="mb-2">
            <p className="f-22"> {state.size > 1 ? 'Edit Document' : 'Add New Document'}</p>
            <div>
              <MaterialInput
                elementType={'materialInput'}
                type={'text'}
                variant={'outlined'}
                label={'Document Name'}
                labelclass={'mb-1 f-15'}
                changed={(e) => handleChangeName(e)}
                value={name}
                maxLength={40}
              />
            </div>
            {isIndependent && (
              <div className="d-flex pt-3 mb-2">
                <p className="pr-5 mb-0 pt-2">Document Size</p>
                <div className="w-30">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    label={''}
                    labelclass={'mb-1 f-15'}
                    changed={(e) => handleChangeSize(e)}
                    value={size}
                    // inputProps={{ pattern: '[0-9]*' }}
                  />
                </div>
                <p className="pl-2 pt-2 f-15 mb-0 text-lightgray">MB</p>
              </div>
            )}
          </div>

          <div className="mt-2 text-right">
            <MButton className="mr-3" color="inherit" variant="outlined" clicked={handleCancel}>
              <Trans i18nKey={'cancel'}></Trans>
            </MButton>
            <MButton
              color="primary"
              variant="contained"
              clicked={handleSubmit}
              disabled={isIndependent ? !size || !name || !edited : !name || !edited}
            >
              <Trans i18nKey={'save'}></Trans>
            </MButton>
          </div>
        </div>
      </DialogModal>{' '}
      {showCancelModal && (
        <CancelModal
          showCancel={showCancelModal}
          setCancel={setShowCancelModal}
          setShow={setOpen(false)}
        />
      )}
    </>
  );
}

DocumentModal.propTypes = {
  open: PropTypes.bool,
  close: PropTypes.func,
  postDocument: PropTypes.func,
  getDesignations: PropTypes.func,
  settingId: PropTypes.string,
  type: PropTypes.string,
  state: PropTypes.instanceOf(Map),
  documents: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  getDocumentConfig: PropTypes.func,
  updateDocument: PropTypes.func,
  categoryId: PropTypes.string,
  selectedCategory: PropTypes.instanceOf(List),
  setOpen: PropTypes.func,
  isIndependent: PropTypes.bool,
  maxSize: PropTypes.number,
};

export default DocumentModal;
