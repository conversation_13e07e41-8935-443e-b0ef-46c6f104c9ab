import React, { Fragment, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Divider } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import q360IdeaBook from 'Assets/q360IdeaBook.svg';
import BookIcon from 'Assets/BookIcon.svg';
import selectedAvatarIcon from 'Assets/selectedAvatarIcon.svg';
import selectedFormIcon from 'Assets/selectedFormIcon.svg';
import selectedSendIcon from 'Assets/selectedSendIcon.svg';
import q360Note from 'Assets/q360Note.svg';
import q360Avatar from 'Assets/q360Avatar.svg';
import q360SendIcon from 'Assets/q360SendIcon.svg';
import { useSearchParams } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { List, Map, fromJS } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import { selectCategoryFormIndex } from '_reduxapi/global_configuration/v1/selectors';
import { getCategoryForm, setData, updateCategoryForm } from '_reduxapi/q360/actions';
// import Step1 from 'Modules/GlobalConfigurationV1/components/QaPcConfiguration/CreateConfig/Step1';
import Step3 from './Step3';
import Step4 from './Step4';
import Step1 from './Step1/Step1';
import { useHistory } from 'react-router-dom';
// import UploadDragAndDrop from 'Modules/GlobalConfigurationV1/components/QaPcConfiguration/CreateConfig/UploadDragAndDrop';
import Step2New from './Step2';
import { useCallApiHook } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { getSingleFormOccurrence } from '_reduxapi/q360/actions';
import { selectFormOccurrence } from '_reduxapi/q360/selectors';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';

const headerUtils = fromJS([
  {
    active: {
      name: 'Occurrence Configure',
      icon: q360IdeaBook,
      currentStep: 'Step 1/4',
    },
    notActive: {
      name: 'Occurance',
      icon: BookIcon,
    },
  },
  {
    active: {
      name: 'Specification Form',
      icon: selectedFormIcon,
      currentStep: 'Step 2/4',
    },
    notActive: {
      name: 'Form',
      icon: q360Note,
    },
  },
  {
    active: {
      name: 'Approver Hierarchy',
      icon: selectedAvatarIcon,
      currentStep: 'Step 3/4',
    },
    notActive: {
      name: 'Approver',
      icon: q360Avatar,
    },
  },
  {
    active: {
      name: 'Concluding Phase',
      icon: selectedSendIcon,
      currentStep: 'Step 4/4',
    },
    notActive: {
      name: 'Preview',
      icon: q360SendIcon,
    },
  },
]);

export function getSections(iframeRef) {
  if (iframeRef.current) {
    const document = iframeRef.current.contentWindow.document;
    return Array.from(document.querySelectorAll('.table-of-contents td'))
      .filter((data) => data.className === 'p-4 pl-12')
      .map((data) => data.textContent);
  }
  return [];
}
// const Step2 = ({ state, setState }) => {
//   const [files, setFiles] = useState(List());
//   const iframeRef = useRef();
//   const sendMessageToIframe = () => {
//     if (iframeRef.current) {
//       iframeRef.current.contentWindow.postMessage({ values: [], from: 'fromDC' }, '*');
//       LocalStorageService.setCustomToken('sections', JSON.stringify(getSections(iframeRef)));
//     }
//   };
//   const handleIframeLoad = () => {
//     sendMessageToIframe();
//   };
//   useEffect(() => {
//     return () => {
//       setState((prev) => (prev = prev.set('attachments', fromJS(files))));
//     };
//   }, [files]);
//   useEffect(() => {
//     setFiles(state.get('attachments', List()));
//   }, []);
//   return (
//     <div className="w-100 h_100vh">
//       <UploadDragAndDrop files={files} setFiles={setFiles} />
//       <iframe
//         ref={iframeRef}
//         src={'/course-spec.html'}
//         title="Face Anomaly Report"
//         width="100%"
//         height="100%"
//         style={{ border: 'none', overflow: 'auto' }}
//         onLoad={handleIframeLoad}
//         sandbox="allow-same-origin"
//       ></iframe>
//     </div>
//   );
// };

const component = {
  0: Step1,
  1: Step2New,
  2: Step3,
  3: Step4,
};

export default function CreateConfig() {
  const [searchParams] = useSearchParams();
  const history = useHistory();
  const dispatch = useDispatch();
  const [isStepOneConfigured, setIsStepOneConfigured] = useState(false);
  const [step, setStep] = useState(searchParams.get('step') ? Number(searchParams.get('step')) : 0);
  const deriveChildData = useRef({});
  const validateMiddleWare = () => {
    if (deriveChildData.current?.size > 0) {
      const AllLevelProvideTAT = deriveChildData.current.every((level) =>
        level.has('turnAroundTime')
      );
      if (AllLevelProvideTAT) return true;
      else {
        dispatch(setData({ message: 'Please ensure all required TAT fields are filled' }));
        return false;
      }
    }
    if (deriveChildData.current?.validateStepTwo) {
      return deriveChildData.current.validateStepTwo(); // if it is returned falsy values means validation are failed
    }
    return true; // all test case are passed
  };
  const updateStep = (index, isMoveToNextStep) => () => {
    if (validateMiddleWare()) {
      const updateStep = () =>
        setStep((prev) => {
          if (isMoveToNextStep) return prev + 1;
          return index;
        });
      if (deriveChildData.current?.handleConstruction) {
        // ISSUE: when we update sections in step2 , it's not reflected in step3 immediately.
        //Solution:
        // handleConstruction=> this function is a update Api from S tep2 - with help of this condition we will make sure calling Step3 getApi as a callback of step2 update Api
        // This condition only satisfied when user moves to step 2 to step 3 directly/
        deriveChildData.current.handleConstruction(updateStep);
        deriveChildData.current = {};
        return;
      } else {
        // if step !==2 => else part will be work
        updateStep();
      }
    }
  };

  const categoryFormIndex = searchParams.get('categoryFormIndex');
  const level = searchParams.get('level');
  const isEditable = searchParams.get('published');

  const selectedCategoryForm = useSelector((state) =>
    selectCategoryFormIndex(state, categoryFormIndex)
  );
  const formOccurrences = useSelector(selectFormOccurrence);

  const formId = searchParams.get('categoryFormId');
  const singleFormOccurrence = formOccurrences.get(formId, List());

  // const ifConfiguredObjectExist = useMemo(() => {
  //   return singleFormOccurrence.some((occurence) => occurence.get('isConfigure', false));
  // }, [singleFormOccurrence]);

  const [getApi] = useCallApiHook(getSingleFormOccurrence);
  const categoryId = searchParams.get('categoryId');

  useEffect(() => {
    if (singleFormOccurrence.size === 0) getApi(formId);
  }, [formId]);
  useEffect(() => {
    if (selectedCategoryForm.size === 0) {
      const detail3 = [];
      if (level === 'course') {
        singleFormOccurrence.toJS().forEach((detail) => {
          // Find or create the program
          let program = detail3.find((p) => p._program_id === detail.programId);
          if (!program) {
            program = {
              _program_id: detail.programId,
              program_name: detail.programName,
              curriculum: [],
            };
            detail3.push(program);
          }

          // Find or create the curriculum
          let curriculum = program.curriculum.find((c) => c._curriculum_id === detail.curriculumId);
          if (!curriculum) {
            curriculum = {
              _curriculum_id: detail.curriculumId,
              curriculum_name: detail.curriculumName,
              years: [],
            };
            program.curriculum.push(curriculum);
          }

          // Find or create the year
          let year = curriculum.years.find((y) => y.year === detail.year);
          if (!year) {
            year = {
              year: detail.year,
              courses: [],
            };
            curriculum.years.push(year);
          }

          // Add the course to the year
          year.courses.push({
            _id: detail._id,
            _program_id: detail.programId,
            program_name: detail.programName,
            _curriculum_id: detail.curriculumId,
            curriculum_name: detail.curriculumName,
            year: detail.year,
            courseId: detail.courseId._id,
            course_name: detail.courseName,
            course_code: detail.courseCode,
            shared_with_others: detail.sharedWithOthers,
            shared_from_others: detail.sharedFormOthers,
            course_type: detail.courseType,
            institution_type: detail.institutionType,
            isConfigure: detail.isConfigure,
            isEnable: detail.isEnable,
            sharedCourses: detail.courseId,
          });
        });
      }

      if (level === 'program') {
        singleFormOccurrence.toJS().forEach((detail) => {
          // Find or create the program
          let program = detail3.find((p) => p._program_id === detail.programId);
          if (!program) {
            program = {
              _program_id: detail.programId,
              program_name: detail.programName,
              curriculum: [],
            };
            detail3.push(program);
          }

          // Find or create the curriculum
          let curriculum = program.curriculum.find((c) => c._curriculum_id === detail.curriculumId);
          if (!curriculum) {
            curriculum = {
              _curriculum_id: detail.curriculumId,
              curriculum_name: detail.curriculumName,
              isConfigure: detail.isConfigure,
              isEnable: detail.isEnable,
              _id: detail._id,
              institution_type: detail.institutionType,
            };
            program.curriculum.push(curriculum);
          }
        });
      }

      if (level === 'institution') {
        singleFormOccurrence.toJS().forEach((detail) => {
          // Find or create the program
          let institution = detail3.find(
            (p) => p.assignedInstitutionId === detail.assignedInstitutionId
          );
          if (!institution) {
            institution = {
              assignedInstitutionId: detail.assignedInstitutionId,
              institutionName: detail.institutionName,
              isConfigure: detail.isConfigure,
              isEnable: detail.isEnable,
              _id: detail._id,
              institution_type: detail.institutionType,
            };
            detail3.push(institution);
          }
        });
      }

      const selectedCategoryForm2 = selectedCategoryForm.set('formOccurrence', fromJS(detail3));

      if (selectedCategoryForm2.size === 0) {
        const params = `categoryId=${categoryId}&pageNo=1&limit=5`;
        dispatch(getCategoryForm(params));
      } else {
        setState((prev) => {
          let prevState = prev;
          if (level === 'course' || level === 'program') {
            prevState = prevState.merge(
              selectedCategoryForm2.update('formOccurrence', (formOccurrence) =>
                formOccurrence.map((program) => {
                  if (level === 'course') {
                    return program.update('curriculum', List(), (curriculum) =>
                      curriculum.map((curriculum) =>
                        curriculum.update('years', List(), (years) =>
                          years.map((year) => {
                            return year.update('courses', (courses) => {
                              let courseArray = fromJS([{}, {}]);
                              courses.forEach((course) => {
                                const index = course.get('isConfigure', false) ? 1 : 0;
                                if (course.get('shared_with_others', false)) {
                                  courseArray = courseArray.update(index, (courseMap) =>
                                    courseMap.update('shared_from', List(), (data) =>
                                      data.push(course.set('isEnable', true))
                                    )
                                  );
                                  return;
                                }
                                if (course.get('shared_from', false)) {
                                  courseArray = courseArray.update(index, (courseMap) =>
                                    courseMap.update('shared_with_others', List(), (data) =>
                                      data.push(course.set('isEnable', true))
                                    )
                                  );
                                  return;
                                }
                                if (course.get('course_type', '') === 'standard')
                                  courseArray = courseArray.update(index, (courseMap) =>
                                    courseMap.update('standard', List(), (data) =>
                                      data.push(course.set('isEnable', true))
                                    )
                                  );
                                else
                                  courseArray = courseArray.update(index, (courseMap) =>
                                    courseMap.update('selective', List(), (data) =>
                                      data.push(course.set('isEnable', true))
                                    )
                                  );
                              });
                              return courseArray;
                            });
                          })
                        )
                      )
                    );
                  }
                  if (level === 'program') {
                    return program.update('curriculum', List(), (curriculum) => {
                      let courseArray = fromJS([{}, {}]);

                      curriculum.forEach((course) => {
                        const index = course.get('isConfigure', false) ? 1 : 0;
                        courseArray = courseArray.update(index, (courseMap) =>
                          courseMap.update('standard', List(), (data) =>
                            data.push(course.set('isEnable', true))
                          )
                        );
                      });
                      return courseArray;
                    });
                  }
                })
              )
            );
          }

          if (level === 'institution') {
            prevState = prevState.merge(
              selectedCategoryForm2.update('formOccurrence', (formOccurrence) =>
                formOccurrence
                  .map((program) => {
                    let courseArray = fromJS([{}, {}]);
                    const index = program.get('isConfigure', false) ? 1 : 0;
                    courseArray = courseArray.update(index, (courseMap) =>
                      courseMap.update('standard', List(), (data) =>
                        data.push(program.set('isEnable', true))
                      )
                    );

                    return courseArray;
                  })
                  .flatMap((innerList) => innerList)
              )
            );
          }

          return prevState.set(
            'approvalLevel',
            selectedCategoryForm
              .get(
                'approvalLevel',
                fromJS([
                  {
                    name: 'Level 1 Hierarchy',
                  },
                ])
              )
              .map((item, index) => item.set('level', index + 1))
          );
        });
      }
    }
  }, [selectedCategoryForm, singleFormOccurrence]);

  const [state, setState] = useState(
    fromJS({
      approvalLevel: [
        {
          level: 1,
          name: 'Level 1 Hierarchy',
        },
      ],
    })
  );
  const updateStepOneConfiguration = () => {
    if (!isStepOneConfigured) {
      dispatch(
        updateCategoryForm(
          {
            categoryId,
            categoryFormId: formId,
            step: 1,
          },
          () => setIsStepOneConfigured(true)
        )
      );
    }
  };
  // const [yearTab, setYearTab] = useState(0);
  // const onClickYearTab = (_, newValue) => {
  //   setYearTab(newValue);
  // };
  const Component = component[step];
  const handleSave = () => history.goBack();

  return (
    <>
      <section
        className="d-flex align-items-center cursor-pointer setting_icon_position mr-3"
        // onClick={onClickSettings}
      >
        {selectedCategoryForm.get('status', '') === 'draft' && (
          <MButton variant="contained" color="primary" clicked={handleSave} className="px-4 mr-3">
            Save As Draft
          </MButton>
        )}
        <EnableOrDisable valid={!isEditable}>
          {step !== 3 && (
            <MButton
              variant="contained"
              color="primary"
              clicked={updateStep(-1, true)}
              // disabled={currentStep === steps.length - 1}
              className="px-4"
            >
              Next Step
            </MButton>
          )}
          {step === 3 && <MButton clicked={handleSave}>Save</MButton>}
        </EnableOrDisable>
      </section>
      <section className="bg-grey calcHeightVh">
        <Header step={step} updateStep={updateStep} />
        <main className="d-flex justify-content-center">
          <div className="w-100 px-4">
            <Component
              updateStepOneConfiguration={updateStepOneConfiguration}
              state={state}
              setState={setState}
              ref={deriveChildData}
            />
          </div>
        </main>
      </section>
    </>
  );
}
CreateConfig.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
  loggedInUserData: PropTypes.instanceOf(Map),
};

const Header = ({ step, updateStep }) => {
  return (
    <header className="d-flex justify-content-center my-4 py-2">
      <div className="bg-white borderRounded-12 d-flex px-3 py-3 ">
        {headerUtils.map((iconData, iconIndex) => {
          if (iconIndex === step)
            return (
              <Fragment key={iconIndex}>
                <div
                  className="d-flex align-items-center gap-8 cursor-pointer"
                  onClick={updateStep(iconIndex)}
                >
                  <img
                    src={iconData.getIn(['active', 'icon'], '')}
                    alt="q360IdeaBook"
                    width={'46px'}
                  />
                  <div>
                    <div className="f-12 fw-400 text-primary">
                      {iconData.getIn(['active', 'currentStep'], '')}
                    </div>
                    <div className="f-14 fw-500 text-dGrey">
                      {iconData.getIn(['active', 'name'], '')}
                    </div>
                  </div>
                </div>
                {iconIndex !== 3 && (
                  <Divider orientation="vertical" flexItem variant="middle" className="my-2 mx-5" />
                )}
              </Fragment>
            );
          return (
            <Fragment key={iconIndex}>
              <div className="text-center cursor-pointer" onClick={updateStep(iconIndex)}>
                <img src={iconData.getIn(['notActive', 'icon'], '')} alt="BookIcon" />
                <p className="m-0 fw-400 text-dGrey f-12 ">
                  {iconData.getIn(['notActive', 'name'], '')}
                </p>
              </div>
              {iconIndex !== 3 && (
                <Divider orientation="vertical" flexItem variant="middle" className="my-2 mx-5" />
              )}
            </Fragment>
          );
        })}
      </div>
    </header>
  );
};
Header.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
  loggedInUserData: PropTypes.instanceOf(Map),
};
