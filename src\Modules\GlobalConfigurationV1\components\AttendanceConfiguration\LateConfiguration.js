import React, { Fragment, useEffect, useState } from 'react';
import HomeIcon from '@mui/icons-material/Home';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import Accordion from '@mui/material/Accordion';
import isEqual from 'lodash/fp/isEqual';
import { Map } from 'immutable';
import TextField from '@mui/material/TextField';
import AccordionSummary from '@mui/material/AccordionSummary';
import MButton from 'Widgets/FormElements/material/Button.js';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Switch from '@mui/material/Switch';
import icon_late_config from 'Assets/icon_late_config.svg';
import info_config from 'Assets/info_config.svg';
import { getLateConfig, setLateConfig, setData } from '_reduxapi/global_configuration/v1/actions';
import { useDispatch, useSelector } from 'react-redux';
import { selectIsLoading, selectLateConfig } from '_reduxapi/global_configuration/v1/selectors';

export default function LateConfiguration() {
  const history = useHistory();
  const dispatch = useDispatch();
  const lateConfig = useSelector(selectLateConfig);
  const isLoading = useSelector(selectIsLoading);
  const [config, setConfig] = useState(
    Map({
      minuteRange: 0,
      lateAbsenceThreshold: false,
      label: '',
    })
  );

  useEffect(() => {
    dispatch(getLateConfig());
  }, []); //eslint-disable-line

  useEffect(() => {
    setConfig(lateConfig.get('lateConfig', Map()));
  }, [lateConfig]); //eslint-disable-line

  function handleUpdate() {
    let errorMessage = '';
    const minute = Number(config.get('minuteRange', ''));
    if (config.get('lateAbsenceThreshold', false)) {
      if (config.get('label', '') === '') {
        errorMessage = 'Late Label is not allowed as Empty';
      } else if (isNaN(minute)) {
        errorMessage = 'Minute Range is not allowed as Empty';
      } else if (!minute > 0) {
        errorMessage = 'Minute Range should be greater than 0';
      }
    }
    if (errorMessage !== '') {
      return dispatch(setData(Map({ message: errorMessage })));
    }
    dispatch(setLateConfig({ lateConfig: config }));
  }

  function handleMinute(event) {
    let value = event.target?.value;
    const maxValue = 60;
    // Check if the value is within the allowed range
    if (!isNaN(value) && Number(value) > maxValue) {
      value = 30;
      dispatch(setData(Map({ message: 'Minutes Range maximum value is 30 ' })));
    }
    value = value !== '' ? Number(value) : value;
    setConfig((prev) => prev.set('minuteRange', value));
  }

  const saveButtonDisabled = isEqual(config.toJS(), lateConfig.get('lateConfig', Map()).toJS());
  return (
    <Fragment>
      <div className="d-flex align-items-center border-bottom pt-3 pb-3 mb-2">
        <HomeIcon fontSize="small" className="digi-gray-neutral" />
        <span
          className="ml-2 mr-2 remove_hover"
          onClick={() => history.push('/globalConfiguration-v1/institution')}
        >
          All Modules
        </span>
        <KeyboardArrowRightIcon fontSize="" />
        <span className="ml-2 text-skyblue">Attendance Configuration System</span>
      </div>
      <div className="d-flex m-2">
        <MButton
          className="ml-auto"
          disabled={isLoading || saveButtonDisabled}
          clicked={handleUpdate}
        >
          SAVE
        </MButton>
      </div>
      {/* ///////////////////////////////// body part//////////////////// */}
      <Accordion disableGutters sx={{ background: 'white !important' }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1a-content"
          id="panel1a-header"
          className="px-3 py-2"
        >
          <div className="d-flex">
            <img src={icon_late_config} alt="icon_late_config" />
            <div className="ml-3">
              <div className="f-18 bold late_config_color">Late Configuration</div>
              <span className="f-15 text-light-grey pt-1">
                {' '}
                Create the late configuration for the student while joining the session
              </span>
            </div>
          </div>
        </AccordionSummary>
        <AccordionDetails>
          <div className="thresHold_config ">
            <div className="d-flex align-items-center">
              <div className="pt-1 pl-2 bold f-17">Late Absence Threshold</div>
              <div className="ml-auto mr-2">
                <Switch
                  checked={config.get('lateAbsenceThreshold', false)}
                  onChange={() =>
                    setConfig((prev) =>
                      prev.set('lateAbsenceThreshold', !prev.get('lateAbsenceThreshold', false))
                    )
                  }
                />
                <span>{config.get('lateAbsenceThreshold', false) ? 'ON' : 'OFF'} </span>
              </div>
            </div>

            {config.get('lateAbsenceThreshold', false) && (
              <div className="ml-2 mb-2 table_config mt-2">
                <table>
                  <tr>
                    <th className="p-2 pl-3 width_210 border_bottom_config">Minutes Range</th>
                    <th className="p-2 width_275 border_bottom_config">Late Label</th>
                  </tr>
                  <tr>
                    <td className="p-2 px-3">
                      <TextField
                        className="width_150"
                        type="number"
                        size={'small'}
                        value={config.get('minuteRange', 0)}
                        onChange={handleMinute}
                        InputProps={{
                          endAdornment: <div className="width_150">&#8805;</div>,
                        }}
                      />
                    </td>
                    <td className="p-2 pr-4">
                      <TextField
                        size={'small'}
                        value={config.get('label', '')}
                        onChange={(event) =>
                          setConfig((prev) => prev.set('label', event.target?.value))
                        }
                      />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={2} className="pb-2">
                      <img src={info_config} alt="info_config" className="ml-3 mr-2" />
                      <span className="digi-light-gray f-13">
                        A student is automatically marked absent if they any join after{' '}
                        {config.get('minuteRange', 0) || 0} minutes
                      </span>
                    </td>
                  </tr>
                </table>
              </div>
            )}
          </div>
        </AccordionDetails>
      </Accordion>
    </Fragment>
  );
}
