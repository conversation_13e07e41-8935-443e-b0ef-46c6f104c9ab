import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import { NotificationContainer, NotificationManager } from 'react-notifications';
import Select from 'react-select';
import { t } from 'i18next';
import { envSignUpService, maxDateSet, hasUserSensitiveData } from 'utils';

import Input from '../../Widgets/FormElements/Input/Input';
import Button from '../../Widgets/FormElements/Button/Button';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import Config from '../../config';

const fNameInput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const mNameInput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const lNameInput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];
const genderInput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];

const empInput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];

const nationalInput = [
  ['Correct', 'correct'],
  ['Wrong', 'wrong'],
];

let id;
class staffProfile extends Component {
  state = {
    selectFname: fNameInput[0][1][2],
    chooseFname: '',
    selectMname: mNameInput[0][1][2],
    chooseMname: '',
    selectLname: lNameInput[0][1][2],
    chooseLname: '',
    selectGender: genderInput[0][1][2],
    chooseGender: '',
    selectEmp: empInput[0][1][2],
    chooseEmp: '',
    selectNational: nationalInput[0][1][2],
    chooseNational: '',
    fName: '',
    mName: '',
    lName: '',
    DOB: '',
    selectedCountry: null,
    residenceId: '',
    passport: '',
    empId: '',
    buildingNo: '',
    city: '',
    district: '',
    zipCode: '',
    phone: '',
    unit: '',
    extension: '',
    room: '',
    GenderError: '',
    DOBError: '',
    countryError: '',
    buildingNoError: '',
    cityError: '',
    districtError: '',
    zipCodeError: '',
    phoneError: '',
    unitError: '',
    extensionError: '',
    roomError: '',
    selectedOption: null,
    validated: false,
    isLoading: false,
    university: [],
    programList: [],
    departmentList: [],
    divisionList: [],
    divisionSubject: [],
    subjectList: [],
    academic: [
      { name: '', value: '' },
      { name: 'Professor', value: 'Professor' },
      { name: 'HOD', value: 'HOD' },
    ],
    isValidate: true,
    appointment: '',
    degreeImage: '',
    addressImage: '',
    collegeImage: '',
    residentImage: '',
    pdf: '',
  };

  componentDidMount = (id) => {
    id = this.props.location.search.split('=')[1];
    this.fetchApi(id);

    const userSensitive = hasUserSensitiveData();
    if (userSensitive) {
      this.setState({
        buildingNo: 'Not Applicable',
        city: 'Not Applicable',
        district: 'Not Applicable',
        zipCode: '111',
        unit: '1',
        chooseNational: true,
        selectNational: 'correct',
      });
    }
  };

  fetchApi = (id) => {
    this.setState({ isLoading: true });
    axios
      .get(`country?limit=300&pageNo=1`)
      .then((res) => {
        const duplicateAr = res.data.data.filter(
          (v, i, a) => a.findIndex((t) => t.name === v.name) === i
        );
        const university = duplicateAr.map((data) => {
          return {
            label: data.name,
            value: data._id,
          };
        });
        // university.unshift({
        //   name: "",
        //   value: "",
        // });
        this.setState({
          university: university,
          // univer: university[0].value,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });

    // staff api start //
    this.setState({ isLoading: true });
    axios
      .get(`user/staff/${id}`)
      .then((res) => {
        const staff = res.data.data;

        this.setState({
          fName: staff.name.first,
          lName: staff.name.last !== undefined && staff.name.last !== null ? staff.name.last : '',
          mName:
            staff.name.middle !== undefined && staff.name.middle !== null ? staff.name.middle : '',
          gender: staff.gender,
          empId: staff.employee_id,
          residenceId: staff.address.nationality_id,
          phone: staff.mobile,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });

    // staff api end //
  };

  validation = (e, name) => {
    let space = /^\S$|^\S[ \S]*\S$/;
    let spaceAlpha = /^[a-zA-Z ]*$/;
    const dotAlpha = /^[a-zA-Z0-9/. -]*$/;
    const AlphaNum = /^[a-zA-Z0-9]+$/;
    const Number = /^[0-9]+$/;

    let genderError = '';
    let DOBError = '';
    // let countryError = '';
    let buildingNoError = '';
    let cityError = '';
    let districtError = '';
    let zipCodeError = '';
    let phoneError = '';
    let unitError = '';
    let passpordError = '';

    let extensionError = '';
    let roomError = '';
    let selectFnameError = '';
    let selectMnameError = '';
    let selectLnameError = '';
    let selectEmpError = '';
    let selectNationalError = '';
    if (this.state.selectFname === 'r') {
      selectFnameError = 'Choose First Name Correct Or Wrong';
    }
    if (this.state.mName) {
      if (this.state.selectMname === 'r') {
        selectMnameError = 'Choose Middle Name Correct Or Wrong';
      }
    }
    if (this.state.lName) {
      if (this.state.selectLname === 'r') {
        selectLnameError = 'Choose Last Name Correct Or Wrong';
      }
    }
    if (this.state.selectGender === 'r') {
      genderError = 'Choose Gender Correct Or Wrong';
    }
    if (this.state.selectEmp === 'r') {
      selectEmpError = 'Choose Emp ID Correct Or Wrong';
    }
    if (this.state.selectNational === 'r') {
      selectNationalError = 'Choose National ID Correct Or Wrong';
    }

    const userDOBValidation = envSignUpService('USER_DOB', true);
    if (userDOBValidation) {
      if (this.state.DOB === '' || this.state.DOB === null) {
        DOBError = 'DOB Field is Required';
      }
    }
    // if (this.state.selectedCountry === '') {
    //   countryError = 'Nationalty Field is Required';
    // }

    if (this.state.passport) {
      if (!space.test(this.state.passport)) {
        passpordError = 'Space not allowed beginning & end';
      } else if (!AlphaNum.test(this.state.passport)) {
        passpordError = 'Special Charcter not Alowed';
      } else if (this.state.passport.length <= 2) {
        passpordError = 'Minimum 3 character is required ';
      }
    }

    if (!this.state.buildingNo) {
      buildingNoError = 'Building No, Street Name Field is Required';
    } else if (!space.test(this.state.buildingNo)) {
      buildingNoError = 'Space not allowed beginning & end';
    } else if (this.state.buildingNo.length <= 2) {
      buildingNoError = 'Minimum 3 character is required ';
    }

    if (this.state.room) {
      if (!space.test(this.state.room)) {
        roomError = 'Space not allowed beginning & end';
      } else if (!dotAlpha.test(this.state.room)) {
        roomError = 'Special character not allowed';
      } else if (this.state.room.length <= 1) {
        roomError = 'Minimum 2 character is required ';
      }
    }

    if (!this.state.city) {
      cityError = 'City Name Field is Required';
    } else if (!space.test(this.state.city)) {
      cityError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.city)) {
      cityError = 'Text Only allowed';
    } else if (this.state.city.length <= 2) {
      cityError = 'Minimum 3 character is required ';
    }

    if (!this.state.district) {
      districtError = 'District Name Field is Required';
    } else if (!space.test(this.state.district)) {
      districtError = 'Space not allowed beginning & end';
    } else if (!spaceAlpha.test(this.state.district)) {
      districtError = 'Text Only allowed';
    } else if (this.state.district.length <= 2) {
      districtError = 'Minimum 3 character is required ';
    }

    if (!this.state.zipCode) {
      zipCodeError = 'Zip Code Field is Required';
    } else if (!space.test(this.state.zipCode)) {
      zipCodeError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.zipCode)) {
      zipCodeError = 'Numeric Only allowed';
    } else if (this.state.zipCode.length <= 2) {
      zipCodeError = 'Minimum 3 character is required ';
    } else if (parseInt(this.state.zipCode) > 1000000) {
      zipCodeError = 'Pls Enter 1000000 Lesser than value ';
    } else if (parseInt(this.state.zipCode) === 0) {
      zipCodeError = 'Pls Enter 0 Greater than value ';
    }

    // if (!this.state.phone) {
    //   phoneError = 'Phone Number is Required';
    // }

    if (this.state.extension) {
      if (!space.test(this.state.extension)) {
        extensionError = 'Space not allowed beginning & end';
      } else if (!Number.test(this.state.extension)) {
        extensionError = 'Numeric Only allowed';
      } else if (this.state.extension.length <= 1) {
        extensionError = 'Minimum 2 character is required ';
      }
    }
    if (!this.state.unit) {
      unitError = 'Floor Number Field is Required';
    } else if (!space.test(this.state.unit)) {
      unitError = 'Space not allowed beginning & end';
    } else if (!Number.test(this.state.unit)) {
      unitError = 'Numeric Only allowed';
    } else if (parseInt(this.state.unit) > 50) {
      unitError = 'Pls Enter 50 Lesser than value ';
    } else if (parseInt(this.state.unit) === 0) {
      unitError = 'Pls Enter 0 Greater than value ';
    }

    if (
      genderError ||
      DOBError ||
      // countryError ||
      buildingNoError ||
      passpordError ||
      cityError ||
      districtError ||
      zipCodeError ||
      phoneError ||
      unitError ||
      roomError ||
      extensionError ||
      selectFnameError ||
      selectMnameError ||
      selectLnameError ||
      selectEmpError ||
      selectNationalError
    ) {
      this.setState({
        genderError,
        DOBError,
        // countryError,
        buildingNoError,
        cityError,
        districtError,
        zipCodeError,
        phoneError,
        unitError,
        passpordError,
        extensionError,
        roomError,
        selectFnameError,
        selectMnameError,
        selectLnameError,
        selectEmpError,
        selectNationalError,
      });

      return false;
    }
    return true;
  };

  handleChange = (e, name) => {
    e.preventDefault();

    if (name === 'fName') {
      this.setState({
        fName: e.target.value,
        fNameError: '',
      });
    }
    if (name === 'mName') {
      this.setState({
        mName: e.target.value,
        mNameError: '',
      });
    }
    if (name === 'lName') {
      this.setState({
        lName: e.target.value,
        lNameError: '',
      });
    }
    if (name === 'familyName') {
      this.setState({
        familyName: e.target.value,
        familyNameError: '',
      });
    }
    if (name === 'residenceId') {
      if (isNaN(e.target.value)) return;
      this.setState({
        residenceId: e.target.value,
      });
    }
    if (name === 'passport') {
      // if (isNaN(e.target.value)) return;
      this.setState({
        passport: e.target.value,
        passpordError: '',
      });
    }
    if (name === 'empId') {
      this.setState({
        empId: e.target.value,
        empIdError: '',
      });
      axios
        .get(`user/staff/${e.target.value}`)
        .then((res) => {
          const em = res.data.status;
          if (em === true) {
            this.setState({
              empIdError: 'This Employee Id is Already Exit',
            });
          } else {
            this.setState({
              empIdError: '',
            });
          }
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    }
    if (name === 'collegeId') {
      this.setState({
        collegeId: e.target.value,
        collegeIdError: '',
      });
    }
    if (name === 'buildingNo') {
      this.setState({
        buildingNo: e.target.value,
        buildingNoError: '',
      });
    }
    if (name === 'street') {
      this.setState({
        street: e.target.value,
        streetError: '',
      });
    }
    if (name === 'city') {
      this.setState({
        city: e.target.value,
        cityError: '',
      });
    }
    if (name === 'district') {
      this.setState({
        district: e.target.value,
        districtError: '',
      });
    }

    if (name === 'zipCode') {
      if (isNaN(e.target.value)) return;
      this.setState({
        zipCode: e.target.value,
        zipCodeError: '',
      });
    }

    if (name === 'phone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        phone: e.target.value,
        phoneError: '',
      });
      axios
        .get(`user/mobile/${e.target.value}`)
        .then((res) => {
          const mo = res.data.status;
          if (mo === true) {
            this.setState({
              phoneError: 'This Mobile Number is Already Exit',
            });
          } else {
            this.setState({
              phoneError: '',
            });
          }
        })
        .catch((error) => {
          this.setState({
            isLoading: false,
          });
        });
    }
    if (name === 'Additionalphone') {
      if (isNaN(e.target.value)) return;
      this.setState({
        Additionalphone: e.target.value,
        AdditionalphoneError: '',
      });
    }
    if (name === 'unit') {
      if (isNaN(e.target.value)) return;
      this.setState({
        unit: e.target.value,
        unitError: '',
      });
    }
    if (name === 'degree') {
      this.setState({
        degree: e.target.value,
        degreeError: '',
      });
    }
    if (name === 'room') {
      this.setState({
        room: e.target.value,
        roomError: '',
      });
    }

    if (name === 'extension') {
      if (isNaN(e.target.value)) return;
      this.setState({
        extension: e.target.value,
        extensionError: '',
      });
    }
  };

  handleSelectDOB = (date) => {
    this.setState({
      DOB: date,
      DOBError: '',
    });
  };

  handleSelect = (e, name) => {
    e.preventDefault();

    if (name === 'country') {
      this.setState({
        selectedCountry: e.target.value,
        countryError: '',
      });
    }
  };

  handleSubmit = (e) => {
    id = this.props.location.search.split('=')[1];
    const documentSecNeed = envSignUpService('DOCUMENT_SEC_NEED', true);
    e.preventDefault();
    this.setState({
      isValidate: false,
    });
    if (this.validation()) {
      let data = {
        id: id,
        first_name: this.state.chooseFname,
        middle_name: this.state.chooseMname !== '' ? this.state.chooseMname : true,
        last_name: this.state.chooseLname !== '' ? this.state.chooseLname : true,
        gender: this.state.chooseGender,
        employee_id: this.state.chooseEmp,
        nationality_id: this.state.chooseNational,
        dob: this.state.DOB ? moment(this.state.DOB).format('YYYY-MM-DD') : '',
        passport_no: this.state.passport,
        building: this.state.buildingNo,
        city: this.state.city,
        district: this.state.district,
        zip_code: this.state.zipCode,
        unit: this.state.unit,
        office_extension: this.state.extension,
        office_room_no: this.state.room,
      };

      if (this.state.selectedCountry !== null) {
        data._nationality_id =
          this.state.selectedCountry?.value !== undefined ? this.state.selectedCountry?.value : '';
      }
      this.setState({ isLoading: true, isValidate: true });
      axios
        .post(`user/profile_update`, data)
        .then((res) => {
          NotificationManager.success(t(`user_management.Staff_Detail_Added_Successfully`));
          this.setState({ isLoading: false });
          if (documentSecNeed) {
            id = this.props.location.search.split('=')[1];
            this.props.history.push({
              pathname: '/staffupload/',
              search: '?id=' + id,
            });
          } else {
            this.props.history.replace('/login');
          }
        })

        .catch((error) => {
          NotificationManager.error(`${error.response.data.data}`);
          this.setState({ isLoading: false });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'fNameInput') {
      this.setState({
        selectFname: e.target.value,
        selectFnameError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseFname: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseFname: false,
        });
      }
    }

    if (name === 'mNameInput') {
      this.setState({
        selectMname: e.target.value,
        selectMnameError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseMname: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseMname: false,
        });
      }
    }

    if (name === 'lNameInput') {
      this.setState({
        selectLname: e.target.value,
        selectLnameError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseLname: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseLname: false,
        });
      }
    }

    if (name === 'genderInput') {
      this.setState({
        selectGender: e.target.value,
        genderError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseGender: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseGender: false,
        });
      }
    }

    if (name === 'empInput') {
      this.setState({
        selectEmp: e.target.value,
        selectEmpError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseEmp: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseEmp: false,
        });
      }
    }

    if (name === 'nationalInput') {
      this.setState({
        selectNational: e.target.value,
        selectNationalError: '',
      });
      if (e.target.value === 'correct') {
        this.setState({
          chooseNational: true,
        });
      } else if (e.target.value === 'wrong') {
        this.setState({
          chooseNational: false,
        });
      }
    }
  };

  handleSelectedNationality = (selectedCountry) => {
    this.setState({
      selectedCountry,
      countryError: '',
    });
  };

  render() {
    const userSensitive = hasUserSensitiveData();
    const documentSecNeed = envSignUpService('DOCUMENT_SEC_NEED', true);
    const userDOBValidation = envSignUpService('USER_DOB', true);
    return (
      <div>
        <div className="staffprofile pl-3 pr-3 pt-3">
          <h2 className="f-20 text-white"> Profile Information </h2>
          <div className="row pt-4 pb-1 ">
            <div className="col-xl-1 col-lg-2 col-md-2 col-3 text-white  ">
              <div className="border-bottom-white-2px">
                <h3 className="f-16 text-center ">Personal</h3>
              </div>
            </div>
            {documentSecNeed && (
              <div className="col-xl-1 col-lg-1 col-md-1 col-3 text-white  ">
                <div className="">
                  <h3 className="f-16 text-center disabled-icon">Upload</h3>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="main pt-2">
          <Loader isLoading={this.state.isLoading} />
          <NotificationContainer />
          <div className="container mt-4">
            <div className="float-right pb-3">
              <Button btnType="Success" clicked={this.handleSubmit}>
                {' '}
                Submit
              </Button>
            </div>
            <div className="clearfix"></div>

            <div className=" outter-profile ">
              <div className="row">
                <div className="col-md-5">
                  <h3 className="f-16 "> Complete your profile information</h3>
                  {userDOBValidation && (
                    <div className="col-md-10 pt-3 customeDatePickWrapper">
                      <p className="floatinglabelcustom"> Date Of Birth *</p>
                      <i
                        className="fa fa-clock-o calenderCustom"
                        style={{ top: '3.5em' }}
                        aria-hidden="true"
                      ></i>
                      <DatePicker
                        placeholderText="Date Of Birth"
                        selected={
                          this.state.DOB !== '' && this.state.DOB !== null
                            ? new Date(this.state.DOB)
                            : ''
                        }
                        onChange={(d) => this.handleSelectDOB(d, 'DOB')}
                        value={
                          this.state.DOB !== '' && this.state.DOB !== null
                            ? moment(this.state.DOB).format('D MMM YYYY')
                            : ''
                        }
                        dateFormat="d MMM yyyy"
                        className={'form-control customeDatepick'}
                        showMonthDropdown
                        showYearDropdown
                        maxDate={maxDateSet()}
                        yearDropdownItemNumber={15}
                      />
                      <div className="InvalidFeedback">{this.state.DOBError}</div>

                      {/* <Input
                      elementType={"floatinginput"}
                      elementConfig={{
                        type: "date",
                      }}
                      maxLength={25}
                      value={this.state.DOB}
                      floatingLabel={"Date of birth"}
                      changed={(e) => this.handleSelect(e, "DOB")}
                      feedback={this.state.DOBError}
                    /> */}
                    </div>
                  )}

                  <div className="col-md-10 ">
                    <p className="floatinglabelcustom"> Nationality (Optional)</p>
                    <Select
                      options={this.state.university}
                      onChange={this.handleSelectedNationality}
                      isClearable={true}
                    />
                    <div className="InvalidFeedback">{this.state.countryError}</div>
                    {/* <Input
                      elementType={"floatingselect"}
                      elementConfig={{
                        options: this.state.university,
                      }}
                      value={this.state.selectedCountry}
                      floatingLabel={"Nationality"}
                      changed={(e) => this.handleSelect(e, "country")}
                      feedback={this.state.countryError}
                    /> */}
                  </div>
                  {!userSensitive && (
                    <>
                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.passport}
                          floatingLabel={'Passport No'}
                          changed={(e) => this.handleChange(e, 'passport')}
                          feedback={this.state.passpordError}
                        />
                      </div>

                      <h3 className="f-16 pt-4 ">Address Details</h3>

                      <div className="col-md-10 pt-1">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          value={this.state.buildingNo}
                          floatingLabel={'Building No, Street Name *'}
                          changed={(e) => this.handleChange(e, 'buildingNo')}
                          feedback={this.state.buildingNoError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.district}
                          floatingLabel={'District Name *'}
                          changed={(e) => this.handleChange(e, 'district')}
                          feedback={this.state.districtError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={25}
                          value={this.state.city}
                          floatingLabel={'City Name *'}
                          changed={(e) => this.handleChange(e, 'city')}
                          feedback={this.state.cityError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={10}
                          value={this.state.zipCode}
                          floatingLabel={'Zip Code *'}
                          changed={(e) => this.handleChange(e, 'zipCode')}
                          feedback={this.state.zipCodeError}
                        />
                      </div>

                      <div className="col-md-10">
                        <Input
                          elementType={'floatinginput'}
                          elementConfig={{
                            type: 'text',
                          }}
                          maxLength={2}
                          value={this.state.unit}
                          floatingLabel={'Floor Number *'}
                          changed={(e) => this.handleChange(e, 'unit')}
                          feedback={this.state.unitError}
                        />
                      </div>
                    </>
                  )}
                  <h3 className="f-16 pt-4 ">Contact details</h3>

                  <div className="col-md-10 pt-1">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={5}
                      value={this.state.extension}
                      floatingLabel={'Office Extension  (Optional)'}
                      changed={(e) => this.handleChange(e, 'extension')}
                      feedback={this.state.extensionError}
                    />
                  </div>

                  <div className="col-md-10 pt-1">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={5}
                      value={this.state.room}
                      floatingLabel={'Office Room  (Optional)'}
                      changed={(e) => this.handleChange(e, 'room')}
                      feedback={this.state.roomError}
                    />
                  </div>

                  <div className="col-md-10 pt-1">
                    <Input
                      elementType={'floatinginput'}
                      elementConfig={{
                        type: 'text',
                      }}
                      maxLength={Config.COUNTRY_CODE_LENGTH}
                      disabled={'disabled'}
                      value={this.state.phone}
                      floatingLabel={'Phone Number'}
                      changed={(e) => this.handleChange(e, 'phone')}
                      feedback={this.state.phoneError}
                    />
                  </div>
                </div>

                <div className="col-md-2"></div>

                <div className="col-md-5">
                  <h3 className="f-16 ">
                    Personal Details{' '}
                    <span className="float-right pr-30"> Tap “correct” or “wrong” </span>{' '}
                  </h3>

                  <div className="col-md-12 pt-3">
                    <div className="row border-bottom">
                      <div className="col-md-6">
                        <label className="toplabel">First Name *</label>
                        <p className="pl-2">{this.state.fName}</p>
                      </div>
                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={'radio'}
                          elementConfig={fNameInput}
                          className={'form-radio1'}
                          selected={this.state.selectFname}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'fNameInput')}
                          feedback={this.state.selectFnameError}
                        />
                      </div>
                    </div>
                    {this.state.mName && (
                      <div className="row border-bottom pt-2">
                        <div className="col-md-6">
                          <label className="toplabel">Middle Name *</label>
                          <p className="pl-2">{this.state.mName}</p>
                        </div>
                        <div className="col-md-6 pt-3">
                          <Input
                            elementType={'radio'}
                            elementConfig={mNameInput}
                            className={'form-radio1'}
                            selected={this.state.selectMname}
                            labelclass="radio-label2"
                            onChange={(e) => this.onRadioGroupChange(e, 'mNameInput')}
                            feedback={this.state.selectMnameError}
                          />
                        </div>
                      </div>
                    )}
                    {this.state.lName && (
                      <div className="row border-bottom pt-2">
                        <div className="col-md-6">
                          <label className="toplabel">Last Name *</label>
                          <p className="pl-2">{this.state.lName}</p>
                        </div>
                        <div className="col-md-6 pt-3">
                          <Input
                            elementType={'radio'}
                            elementConfig={lNameInput}
                            className={'form-radio1'}
                            selected={this.state.selectLname}
                            labelclass="radio-label2"
                            onChange={(e) => this.onRadioGroupChange(e, 'lNameInput')}
                            feedback={this.state.selectLnameError}
                          />
                        </div>
                      </div>
                    )}
                    <div className="row border-bottom pt-2">
                      <div className="col-md-6">
                        <label className="toplabel">Gender *</label>
                        <p className="pl-2">{this.state.gender}</p>
                      </div>
                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={'radio'}
                          elementConfig={genderInput}
                          className={'form-radio1'}
                          selected={this.state.selectGender}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'genderInput')}
                          feedback={this.state.genderError}
                        />
                      </div>
                    </div>
                  </div>

                  <h3 className="f-16 pt-3 ">Academic Details</h3>

                  <div className="col-md-12 pt-3">
                    <div className="row border-bottom">
                      <div className="col-md-6">
                        <label className="toplabel">Employee Id *</label>
                        <p className="pl-2">{this.state.empId}</p>
                      </div>
                      <div className="col-md-6 pt-3">
                        <Input
                          elementType={'radio'}
                          elementConfig={empInput}
                          className={'form-radio1'}
                          selected={this.state.selectEmp}
                          labelclass="radio-label2"
                          onChange={(e) => this.onRadioGroupChange(e, 'empInput')}
                          feedback={this.state.selectEmpError}
                        />
                      </div>
                    </div>
                    {!userSensitive && (
                      <div className="row border-bottom pt-2">
                        <div className="col-md-6">
                          <label className="toplabel">Resident ID / National ID *</label>
                          <p className="pl-2">{this.state.residenceId}</p>
                        </div>
                        <div className="col-md-6 pt-3">
                          <Input
                            elementType={'radio'}
                            elementConfig={nationalInput}
                            className={'form-radio1'}
                            selected={this.state.selectNational}
                            labelclass="radio-label2"
                            onChange={(e) => this.onRadioGroupChange(e, 'nationalInput')}
                            feedback={this.state.selectNationalError}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

staffProfile.propTypes = {
  location: PropTypes.object,
  history: PropTypes.object,
};

export default staffProfile;
