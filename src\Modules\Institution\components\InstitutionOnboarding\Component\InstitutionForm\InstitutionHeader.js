import React from 'react';
import PropTypes from 'prop-types';

import MButton from 'Widgets/FormElements/material/Button';
import '../../../../css/institution.css';

function InstitutionHeader({ handleBack, onSubmit, name }) {
  return (
    <div className="d-flex p-2">
      <div className="f-17 pt-2 bold mr-auto">{`Add New ${name}`}</div>
      <MButton
        className="text-uppercase mr-3 university-cancel-btn"
        color="inherit"
        variant="outlined"
        clicked={handleBack}
      >
        CANCEL
      </MButton>
      <div className="ml-2">
        <MButton clicked={onSubmit} className="university-save-btn text-white" fullWidth={true}>
          SAVE
        </MButton>
      </div>
    </div>
  );
}

InstitutionHeader.propTypes = {
  handleBack: PropTypes.func,
  onSubmit: PropTypes.func,
  name: PropTypes.string,
};
export default InstitutionHeader;
