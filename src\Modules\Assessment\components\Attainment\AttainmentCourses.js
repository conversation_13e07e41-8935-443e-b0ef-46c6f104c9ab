import React, { useState, useEffect } from 'react';
import { Divider } from '@mui/material';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/assessment/action';
import PropTypes from 'prop-types';
import { Map, List, fromJS } from 'immutable';
import { selectAttainmentCourse } from '_reduxapi/assessment/selector';
import AttainmentCourseList from './AttainmentCourseList';
import { dString, getURLParams, eString } from 'utils';
import { useHistory, useParams } from 'react-router-dom';

function Header() {
  const history = useHistory();
  return (
    <div className="bg-white p-3 border-bottom-2px">
      <div className="container-fluid">
        <p className="font-weight-bold mb-0 text-left f-17">
          <i
            className="fa fa-arrow-left pr-3 remove_hover"
            aria-hidden="true"
            onClick={() =>
              history.push(`/attainment-calculator/attainment-reports?type=${eString('course')}`)
            }
          ></i>{' '}
          Back
        </p>
      </div>
    </div>
  );
}

function AttainmentCourses({ getAttainmentCourse, attainmentCourse, setData, updateCourseStatus }) {
  const [selectValues, setSelectValues] = useState(Map());
  const [optionsList, setOptionsList] = useState(
    fromJS({
      courseType: [
        {
          name: 'Standard',
          value: 'standard',
        },
        {
          name: 'Selective',
          value: 'selective',
        },
      ],
    })
  );
  const programId = getURLParams('pid', true);
  const term = getURLParams('term', true);
  const icId = getURLParams('icId', true);
  const outCome = getURLParams('outCome', true);

  const params = useParams();
  const { regulationId } = params;

  useEffect(() => {
    const requestParams = {
      programId,
      institutionCalendarId: icId,
      term,
      attainmentId: dString(regulationId),
      outCome,
    };
    getAttainmentCourse(requestParams);
  }, []); //eslint-disable-line

  const getOption = (key) => {
    const groupedData = attainmentCourse
      .groupBy((item) => item.get(key))
      .keySeq()
      .toList();
    if (key === 'calendar_name') {
      return groupedData
        .sort((a, b) => a.split('-')[0] - b.split('-')[0])
        .map((item) =>
          Map({
            name: item,
            value: item,
          })
        );
    } else {
      return groupedData.map((item) =>
        Map({
          name: key === 'year' ? `Year ${item.replace('year', '')}` : item,
          value: item,
        })
      );
    }
  };

  useEffect(() => {
    if (attainmentCourse.size) {
      const academicYear = getOption('calendar_name');
      const year = getOption('year');
      const level = getOption('levelNo');
      setOptionsList(
        optionsList.set('academicYear', academicYear).set('year', year).set('level', level)
      );
    }
  }, [attainmentCourse]); //eslint-disable-line

  const handleChange = (e, name) => {
    const value = e.target.value;
    setSelectValues(selectValues.set(name, value));
  };

  const SelectInput = ({ label, name }) => {
    const getOptions = () => {
      return optionsList.get(name, List()).toJS();
    };

    return (
      <MaterialInput
        elementType={'materialSelect'}
        type={'text'}
        variant={'outlined'}
        size={'small'}
        elementConfig={{ options: [{ name: 'All', value: '' }, ...getOptions()] }}
        label={label}
        changed={(e) => handleChange(e, name)}
        value={selectValues.get(name, '')}
      />
    );
  };
  SelectInput.propTypes = {
    label: PropTypes.string,
    name: PropTypes.string,
  };

  const filterAttainmentCourse = () => {
    const filterCourse = attainmentCourse.filter((item) => {
      const academicYear = selectValues.get('academicYear', '');
      const courseType = selectValues.get('courseType', '');
      const level = selectValues.get('level', '');
      const year = selectValues.get('year', '');
      const searchText = selectValues.get('searchText', '');
      if (!academicYear && !courseType && !level && !year && !searchText) {
        return true;
      }
      let academicYearStatus = true;
      let yearStatus = true;
      let levelStatus = true;
      let courseTypeStatus = true;
      let searchTextStatus = true;
      if (academicYear) {
        academicYearStatus = item.get('calendar_name', '') === academicYear;
      }
      if (year) {
        yearStatus = item.get('year', '') === year;
      }
      if (level) {
        levelStatus = item.get('levelNo', '') === level;
      }
      if (courseType) {
        courseTypeStatus = item
          .get('courses', '')
          .some((course) => course.get('model').toLowerCase() === courseType);
      }
      if (searchText) {
        searchTextStatus =
          item.get('calendar_name', '').toLowerCase().includes(searchText.toLowerCase()) ||
          item.get('year', '').toLowerCase().includes(searchText.toLowerCase()) ||
          item.get('levelNo', '').toLowerCase().includes(searchText.toLowerCase()) ||
          item
            .get('courses', List())
            .some(
              (course) =>
                course.get('model', '').toLowerCase().includes(searchText.toLowerCase()) ||
                course.get('courses_name', '').toLowerCase().includes(searchText.toLowerCase()) ||
                course.get('courses_number', '').toLowerCase().includes(searchText.toLowerCase())
            );
      }
      return (
        academicYearStatus && yearStatus && levelStatus && courseTypeStatus && searchTextStatus
      );
    });
    return filterCourse;
  };

  const onChangeCheckbox = (e, courseIndex, index, courseId) => {
    const checked = e.target.checked;
    const callBack = () => {
      setData(
        fromJS({
          attainmentCourse: attainmentCourse.setIn(
            [index, 'courses', courseIndex, 'status'],
            checked
          ),
        })
      );
    };
    const filteredCourseList = attainmentCourse.reduce((acc, item) => {
      return acc.concat(
        item.get('courses', List()).map((data) => {
          return Map({
            term: term,
            levelNo: item.get('levelNo', ''),
            courseId: data.get('_course_id', ''),
            status: data.get('status', false),
          });
        })
      );
    }, List());
    const findCourseIndex = filteredCourseList.findIndex(
      (item) => item.get('courseId', '') === courseId
    );
    const courseList = filteredCourseList.setIn([findCourseIndex, 'status'], checked);
    const requestData = {
      attainmentId: dString(regulationId),
      courseList: courseList.toJS(),
    };
    updateCourseStatus({ requestData, callBack });
  };

  return (
    <div className="main pb-5">
      <Header />
      <div className="container mt-4">
        <p className="f-20 mb-0 pl-3 ml-4  bold"> All Courses</p>
        <div className="d-flex justify-content-between pb-2 ml-4">
          <div className="col-md-2">
            <div className="pt-4 mt-2">
              <MaterialInput
                elementType={'materialSearch'}
                placeholder={'Search'}
                changed={(e) => handleChange(e, 'searchText')}
                value={selectValues.get('searchText', '')}
              />
            </div>
          </div>
          <div className="col-md-8">
            <div className="d-flex justify-content-end">
              <div className="col-md-3">
                <SelectInput label="Academic Year" name="academicYear" />
              </div>
              <div className="col-md-2">
                <SelectInput label="Year" name="year" />
              </div>
              <div className="col-md-2">
                <SelectInput label="Level" name="level" />
              </div>
              <div className="col-md-2">
                <SelectInput label="Course Type" name="courseType" />
              </div>
            </div>
          </div>
        </div>
        <Divider />
        <AttainmentCourseList
          attainmentCourse={filterAttainmentCourse()}
          onChangeCheckbox={onChangeCheckbox}
        />
      </div>
    </div>
  );
}

AttainmentCourses.propTypes = {
  attainmentCourse: PropTypes.instanceOf(Map),
  getAttainmentCourse: PropTypes.func,
  setData: PropTypes.func,
  updateCourseStatus: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    attainmentCourse: selectAttainmentCourse(state),
  };
};

export default connect(mapStateToProps, actions)(AttainmentCourses);
