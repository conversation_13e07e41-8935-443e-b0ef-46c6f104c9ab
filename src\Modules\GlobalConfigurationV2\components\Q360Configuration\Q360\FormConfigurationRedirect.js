import React, { useState } from 'react';
import FormConfigurationCourseWise from './FormConfigurationCourseWise';
import FormConfigurationProgramWise from './FormConfigurationProgramWise';
import { Map as IMap } from 'immutable';
import FormConfigurationInstitutionWise from './FormConfigurationInstitutionWise';
import { useDispatchAndSelectorFunctionsQlc } from './utils';
import PropTypes from 'prop-types';

const componentContainer = Object.freeze({
  course: FormConfigurationCourseWise,
  program: FormConfigurationProgramWise,
  institution: FormConfigurationInstitutionWise,
});
export default function FormConfigurationRedirect({
  children,
  existingData = IMap(),
  handleClose,
  params,
  formType,
}) {
  const [open, setOpen] = useState(false);
  function handleCloseGloabl() {
    handleOpenOrClose();
    handleClose && handleClose();
  }

  const {
    configureTemplate,
    currentCategoryIndex,
    clearProgramDetails,
  } = useDispatchAndSelectorFunctionsQlc();
  const level = configureTemplate.getIn([currentCategoryIndex, 'level'], '');
  const Component = componentContainer[level];
  function handleOpenOrClose() {
    if (!open) {
      clearProgramDetails();
    }
    setOpen((prev) => !prev);
  }
  return (
    <>
      <div onClick={handleOpenOrClose}>{children}</div>
      {Component && open && (
        <Component
          params={params}
          handleClose={handleCloseGloabl}
          existingData={existingData}
          formType={formType}
        />
      )}
    </>
  );
}

FormConfigurationRedirect.propTypes = {
  children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.node]),
  existingData: PropTypes.instanceOf(IMap),
  handleClose: PropTypes.func,
  params: PropTypes.string,
  formType: PropTypes.string,
};
