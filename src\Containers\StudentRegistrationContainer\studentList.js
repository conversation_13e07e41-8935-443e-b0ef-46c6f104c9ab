import React, { Component } from 'react';
import { Table } from 'react-bootstrap';
import { withRouter } from 'react-router-dom';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';

import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { jsUcfirst } from '../../utils';
class StudentList extends Component {
  handleClickView = (name, data) => {
    if (name === 'submitted') {
      this.props.history.push({
        pathname: '/submitted/student/profile',
        search: '?id=' + data.id + '&tab=3',
      });
    }
    if (name === 'mismatch') {
      this.props.history.push({
        pathname: '/mismatch/student/profile',
        search: '?id=' + data.id + '&tab=4',
      });
    }
    if (name === 'completed') {
      this.props.history.push({
        pathname: '/student/inactive',
        search: '?id=' + data.id + '&tab=5',
      });
    }
  };

  render() {
    const {
      header,
      data,
      handleAllChecked,
      handleCheckFieldElement,
      handleEdit,
      // handleConfirmDeleteShow,
      name,
    } = this.props;
    return (
      <div className="dash-table">
        <Table responsive hover>
          <thead className="th-change">
            <tr>
              {name === 'invited' && (
                <th>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={handleAllChecked}
                    value="checkedall"
                  />
                </th>
              )}
              {header.map((header, index) => (
                <th key={index}>{header}</th>
              ))}
              {name === 'all' && (
                <th>
                  {' '}
                  <Trans i18nKey={'status'}></Trans>
                </th>
              )}
              {name === 'invited' && (
                <th>
                  <Trans i18nKey={'action'}></Trans>
                </th>
              )}
            </tr>
          </thead>
          {data.length === 0 ? (
            <TableEmptyMessage />
          ) : (
            <tbody>
              {data.map((data, index) => (
                <tr className="tr-change" key={index}>
                  {name === 'invited' && (
                    <React.Fragment>
                      {data.isChecked !== true ? (
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={(event) => handleCheckFieldElement(event, index)}
                            value="checkedall"
                          />
                        </td>
                      ) : (
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={(event) => handleCheckFieldElement(event, index)}
                            value="checkedall"
                            checked
                          />
                        </td>
                      )}
                    </React.Fragment>
                  )}
                  {name === 'submitted' || name === 'mismatch' ? (
                    <td
                      className={
                        CheckPermission(
                          'subTabs',
                          'User Management',
                          'Student Management',
                          '',
                          'Registration Pending',
                          '',
                          jsUcfirst(name),
                          'Profile View'
                        )
                          ? 'profile_view'
                          : ''
                      }
                      onClick={
                        CheckPermission(
                          'subTabs',
                          'User Management',
                          'Student Management',
                          '',
                          'Registration Pending',
                          '',
                          jsUcfirst(name),
                          'Profile View'
                        )
                          ? (e) => this.handleClickView(name, data)
                          : () => {}
                      }
                    >
                      {data.academic}
                    </td>
                  ) : name === 'completed' ? (
                    <td
                      className="profile_view"
                      onClick={() => this.handleClickView('completed', data)}
                    >
                      {data.academic}
                    </td>
                  ) : (
                    <td>{data.academic}</td>
                  )}
                  <td>{data.email}</td>
                  <td>{data.first}</td>
                  <td>{data.middle}</td>
                  <td>{data.last}</td>
                  <td>{data.gender}</td>
                  <td>{data.nationality_id}</td>
                  {name === 'all' && <td>{data.user_state}</td>}
                  {name === 'invited' && (
                    <td>
                      {CheckPermission(
                        'subTabs',
                        'User Management',
                        'Student Management',
                        '',
                        'Registration Pending',
                        '',
                        'Invited',
                        'Edit'
                      ) && (
                        <i
                          className="fa fa-pencil text-darkgray pr-3 f-16 remove_hover"
                          onClick={() => handleEdit(data)}
                        ></i>
                      )}
                      {/* <i
                          className="fa fa-trash text-blue f-16 remove_hover"
                          onClick={(e) => handleConfirmDeleteShow(e, data)}
                        ></i> */}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          )}
        </Table>
      </div>
    );
  }
}

StudentList.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
  name: PropTypes.string,
  header: PropTypes.array,
  handleAllChecked: PropTypes.func,
  handleCheckFieldElement: PropTypes.func,
  handleEdit: PropTypes.func,
  data: PropTypes.array,
};

export default withRouter(StudentList);
