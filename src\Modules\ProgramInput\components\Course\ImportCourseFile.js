import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { Modal, But<PERSON>, Table } from 'react-bootstrap';
import readXlsxFile from 'read-excel-file';
import { Trans } from 'react-i18next';
import { List } from 'immutable';

import ErrorModal from '../../../StudentGrouping/Modal/ErrorModal';
import * as actions from '../../../../_reduxapi/program_input/action';
import { TypeCasting } from './utils';
import {
  selectInvalidDataCourseList,
  selectvalidDataCourseList,
} from '../../../../_reduxapi/program_input/selectors';
import { getLang, getURLParams } from '../../../../utils';
import { t } from 'i18next';
import { exportToExcel } from 'Modules/ProgramInput/utils';

const lng = getLang();
class ImportFromCourseFile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      exportShow: true,
      importShow: false,
      importStudent: false,
      importStudentList: false,
      directValid: true,
      chooseNational: '',
      csvRecords: [],
      group1: true,
      group2: false,
      group3: false,
      importAlert: false,
      importTable: false,
      filename: null,
      dataCheckShow: true,
      invalidList: [],
      importFormatError: false,
      importBtnEnable: false,
      programId: getURLParams('_id', true),
      curriculumId: getURLParams('_curriculum_id', true),
    };
  }

  handleChange = (event) => {
    if (
      event.target.files[0].type !==
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      this.setState({ importFormatError: true });
    } else {
      this.setState({ importFormatError: false, tempMismatch: false });
      this.getRecords(event.target.files[0]);
    }
  };

  sampleData = () => {
    const sampleData = [
      {
        [t('program_input.sample_data_headers.Program_Name')]: '',
        [t('program_input.sample_data_headers.Program_Code')]: '',
        [t('program_input.sample_data_headers.Curriculum_Name')]: '',
        [t('program_input.sample_data_headers.Year')]: '',
        [t('program_input.sample_data_headers.Level')]: '',
        [t('program_input.sample_data_headers.Course_Type')]: '',
        [t('program_input.sample_data_headers.Course_Recurring_at')]: '',
        [t('program_input.sample_data_headers.Course_Code')]: '',
        [t('program_input.sample_data_headers.Course_Name')]: '',
        [t('program_input.sample_data_headers.Start_Week')]: '',
        [t('program_input.sample_data_headers.Duration_in_weeks')]: '',
        [t('program_input.sample_data_headers.Delivering_Subjects')]: '',
        [t('program_input.sample_data_headers.Administrating_Subject_Dept_Prog')]: '',
        [t('program_input.sample_data_headers.Theory_Credit_Hours')]: '',
        [t('program_input.sample_data_headers.Theory_Contact_hours_per_credit_hour')]: '',
        [t('program_input.sample_data_headers.Theory_duration_per_contact_Hour_in_minutes')]: '',
        [t('program_input.sample_data_headers.Theory_duration_in_minutes')]: '',
        [t('program_input.sample_data_headers.Practical_Credit_Hours')]: '',
        [t('program_input.sample_data_headers.Practical_Contact_hours_per_credit_hour')]: '',
        [t('program_input.sample_data_headers.Practical_duration_per_contact_Hour_in_minutes')]: '',
        [t('program_input.sample_data_headers.Practical_duration_in_minutes')]: '',
        [t('program_input.sample_data_headers.Clinical_Credit_Hours')]: '',
        [t('program_input.sample_data_headers.Clinical_Contact_hours_per_credit_hour')]: '',
        [t('program_input.sample_data_headers.Clinical_duration_per_contact_Hour_in_minutes')]: '',
        [t('program_input.sample_data_headers.Clinical_duration_in_minutes')]: '',
        [t('program_input.sample_data_headers.Allow_to_edit_credit_hours_while_scheduling')]: '',
        [t('program_input.sample_data_headers.Should_Achieve_target_credit_hours')]: '',
      },
    ];

    exportToExcel(sampleData, 'sampleCourse');
  };

  handleChangeData = () => {
    this.setState({
      filename: null,
      csvRecords: [],
      importBtnEnable: false,
      tempMismatch: false,
    });
  };
  async getRecords(csvFileObject) {
    const map = {
      Program_Name: 'Program_Name',
      Program_Code: 'Program_Code',
      Curriculum_Name: 'Curriculum_Name',
      Year: 'Year',
      Level: 'Level',
      Course_Type: 'Course_Type',
      Course_Recurring_at: 'Course_Recurring_at',
      Course_Code: 'Course_Code',
      Course_Name: 'Course_Name',
      Start_Week: 'Start_Week',
      Duration_in_weeks: 'Duration_in_weeks',
      Delivering_Subjects: 'Delivering_Subjects',
      Administrating_Subject_Dept_Prog: 'Administrating_Subject_Dept_Prog',
      Theory_Credit_Hours: 'Theory_Credit_Hours',
      Theory_Contact_hours_per_credit_hour: 'Theory_Contact_hours_per_credit_hour',
      Theory_duration_per_contact_Hour_in_minutes: 'Theory_duration_per_contact_Hour_in_minutes',
      Theory_duration_in_minutes: 'Theory_duration_in_minutes',
      Practical_Credit_Hours: 'Practical_Credit_Hours',
      Practical_Contact_hours_per_credit_hour: 'Practical_Contact_hours_per_credit_hour',
      Practical_duration_per_contact_Hour_in_minutes:
        'Practical_duration_per_contact_Hour_in_minutes',
      Practical_duration_in_minutes: 'Practical_duration_in_minutes',
      Clinical_Credit_Hours: 'Clinical_Credit_Hours',
      Clinical_Contact_hours_per_credit_hour: 'Clinical_Contact_hours_per_credit_hour',
      Clinical_duration_per_contact_Hour_in_minutes:
        'Clinical_duration_per_contact_Hour_in_minutes',
      Clinical_duration_in_minutes: 'Clinical_duration_in_minutes',
      Allow_to_edit_credit_hours_while_scheduling: 'Allow_to_edit_credit_hours_while_scheduling',
      Should_Achieve_target_credit_hours: 'Should_Achieve_target_credit_hours',
    };
    var filename = csvFileObject.name;
    let Course = [
      'Course_Type',

      'Course_Name',
      'Start_Week',
      'Duration_in_weeks',
      'Delivering_Subjects',
      'Administrating_Subject_Dept_Prog',
      'Theory_Credit_Hours',
      'Theory_Contact_hours_per_credit_hour',
      'Theory_duration_per_contact_Hour_in_minutes',
      'Theory_duration_in_minutes',
      'Practical_Credit_Hours',
      'Practical_Contact_hours_per_credit_hour',
      'Practical_duration_per_contact_Hour_in_minutes',
      'Practical_duration_in_minutes',
      'Clinical_Credit_Hours',
      'Clinical_Contact_hours_per_credit_hour',
      'Clinical_duration_per_contact_Hour_in_minutes',
      'Clinical_duration_in_minutes',
      'Allow_to_edit_credit_hours_while_scheduling',
      'Should_Achieve_target_credit_hours',
    ];

    this.setState({ filename });
    await readXlsxFile(csvFileObject, { map }).then(({ rows }) => {
      if (rows[0] === undefined) {
        this.setState({ tempMismatch: true });
        return true;
      }
      let tempMismatch = Object.keys(rows[0]).filter((element) => Course.includes(element));
      if (tempMismatch.length === 0) {
        this.setState({ tempMismatch: true });
        return true;
      }

      this.setState({ csvRecords: rows, importBtnEnable: true });
    });
  }

  afterImport = () => {
    const { ImportCourseCsvCheck } = this.props;
    const { csvRecords } = this.state;
    this.setState({
      dataCheckShow: false,
    });

    let data = csvRecords.map((row) => {
      return {
        Program_Name: TypeCasting(row.Program_Name),

        Program_Code: TypeCasting(row.Program_Code),

        Curriculum_Name: TypeCasting(row.Curriculum_Name),
        Year: TypeCasting(row.Year),

        Level: TypeCasting(row.Level),

        Course_Type: TypeCasting(row.Course_Type),

        Course_Recurring_at: TypeCasting(row.Course_Recurring_at),

        Course_Code: TypeCasting(row.Course_Code),
        Course_Name: TypeCasting(row.Course_Name),

        Start_Week: String(row.Start_Week) === 'undefined' ? '' : String(row.Start_Week).trim(),

        Duration_in_weeks:
          String(row.Duration_in_weeks) === 'undefined' ? '' : String(row.Duration_in_weeks).trim(),
        Delivering_Subjects: TypeCasting(row.Delivering_Subjects),
        Administrating_Subject_Dept_Prog: TypeCasting(row.Administrating_Subject_Dept_Prog),
        Theory_Credit_Hours: TypeCasting(row.Theory_Credit_Hours),

        Theory_Contact_hours_per_credit_hour: TypeCasting(row.Theory_Contact_hours_per_credit_hour),
        Theory_per_contact_hour: TypeCasting(row.Theory_duration_per_contact_Hour_in_minutes),

        Theory_duration_in_minutes: TypeCasting(row.Theory_duration_in_minutes),

        Practical_Credit_Hours: TypeCasting(row.Practical_Credit_Hours),

        Practical_Contact_hours_per_credit_hour: TypeCasting(
          row.Practical_Contact_hours_per_credit_hour
        ),
        Practical_per_contact_hour: TypeCasting(row.Practical_duration_per_contact_Hour_in_minutes),

        Practical_duration_in_minutes: TypeCasting(row.Practical_duration_in_minutes),

        Clinical_Credit_Hours: TypeCasting(row.Clinical_Credit_Hours),

        Clinical_Contact_hours_per_credit_hour: TypeCasting(
          row.Clinical_Contact_hours_per_credit_hour
        ),
        Clinical_per_contact_hour: TypeCasting(row.Clinical_duration_per_contact_Hour_in_minutes),

        Clinical_duration_in_minutes: TypeCasting(row.Clinical_duration_in_minutes),
        Allow_to_edit_credit_hours_while_scheduling: TypeCasting(
          row.Allow_to_edit_credit_hours_while_scheduling
        ),
        Should_Achieve_target_credit_hours: TypeCasting(row.Should_Achieve_target_credit_hours),
      };
    });
    let body = {
      course: data,
    };

    ImportCourseCsvCheck(
      `/digi_course/data_check_import_course`,
      body,

      () => {
        this.inValidDataCheck();
      },
      () => {
        this.validDataCheck();
      }
    );
  };
  validDataCheck = () => {
    this.props.closed(false);
  };
  fileName = () => {
    this.setState({
      filename: null,
      tempMismatch: false,
    });
  };

  importContinue = () => {
    const { validDataCourseList, ImportCourseCsv } = this.props;

    if (validDataCourseList === undefined) {
      this.props.closed(false);
    }
    if (validDataCourseList !== undefined) {
      let listData = this.props.validDataCourseList && this.props.validDataCourseList.toJS();
      let data = listData.map((row) => {
        return {
          Program_Name: TypeCasting(row.data.Program_Name),

          Program_Code: TypeCasting(row.data.Program_Code),

          Curriculum_Name: TypeCasting(row.data.Curriculum_Name),
          Year: TypeCasting(row.data.Year),

          Level: TypeCasting(row.data.Level),

          Course_Type: TypeCasting(row.data.Course_Type),

          Course_Recurring_at: TypeCasting(row.data.Course_Recurring_at),

          Course_Code: TypeCasting(row.data.Course_Code),

          Course_Name: TypeCasting(row.data.Course_Name),

          Start_Week:
            String(row.data.Start_Week) === 'undefined' ? '' : String(row.data.Start_Week).trim(),

          Duration_in_weeks:
            String(row.data.Duration_in_weeks) === 'undefined'
              ? ''
              : String(row.data.Duration_in_weeks).trim(),

          Delivering_Subjects: TypeCasting(row.data.Delivering_Subjects),

          Administrating_Subject_Dept_Prog: TypeCasting(row.data.Administrating_Subject_Dept_Prog),

          Theory_Credit_Hours: TypeCasting(row.data.Theory_Credit_Hours),

          Theory_Contact_hours_per_credit_hour: TypeCasting(
            row.data.Theory_Contact_hours_per_credit_hour
          ),
          Theory_duration_per_contact_Hour_in_minutes: TypeCasting(
            row.data.Theory_duration_per_contact_Hour_in_minutes
          ),

          Theory_duration_in_minutes: TypeCasting(row.data.Theory_duration_in_minutes),

          Practical_Credit_Hours: TypeCasting(row.data.Practical_Credit_Hours),

          Practical_Contact_hours_per_credit_hour: TypeCasting(
            row.data.Practical_Contact_hours_per_credit_hour
          ),

          Practical_duration_per_contact_Hour_in_minutes: TypeCasting(
            row.data.Practical_duration_per_contact_Hour_in_minutes
          ),
          Practical_duration_in_minutes: TypeCasting(row.data.Practical_duration_in_minutes),

          Clinical_Credit_Hours: TypeCasting(row.data.Clinical_Credit_Hours),

          Clinical_Contact_hours_per_credit_hour: TypeCasting(
            row.data.Clinical_Contact_hours_per_credit_hour
          ),

          Clinical_duration_per_contact_Hour_in_minutes: TypeCasting(
            row.data.Clinical_duration_per_contact_Hour_in_minutes
          ),

          Clinical_duration_in_minutes: TypeCasting(row.data.Clinical_duration_in_minutes),

          Allow_to_edit_credit_hours_while_scheduling: TypeCasting(
            row.data.Allow_to_edit_credit_hours_while_scheduling
          ),

          Should_Achieve_target_credit_hours: TypeCasting(
            row.data.Should_Achieve_target_credit_hours
          ),
        };
      });
      let body = {
        course: data,
      };

      ImportCourseCsv(
        `digi_course/import_course_with_assign`,
        body,

        () => {
          this.props.closed(false);
        }
      );
    }
  };
  importListBack = () => {
    this.setState({
      dataCheckShow: true,
      directValid: true,
    });
  };
  exportList = () => {
    let listData = this.props.InvalidDataCourseList && this.props.InvalidDataCourseList.toJS();

    let data = listData.map((row) => {
      return {
        Program_Name: row.data.Program_Name,
        Program_Code: row.data.Program_Code,
        Curriculum_Name: row.data.Curriculum_Name,
        Year: row.data.Year,
        Level: row.data.Level,
        Course_Type: row.data.Course_Type,
        Course_Recurring_at: row.data.Course_Recurring_at,
        Course_Code: row.data.Course_Code,
        Course_Name: row.data.Course_Name,
        Start_Week: row.data.Start_Week,
        Duration_in_weeks: row.data.Duration_in_weeks,
        Delivering_Subjects: row.data.Delivering_Subjects,
        Administrating_Subject_Dept_Prog: row.data.Administrating_Subject_Dept_Prog,
        Theory_Credit_Hours: row.data.Theory_Credit_Hours,
        Theory_Contact_hours_per_credit_hour: row.data.Theory_Contact_hours_per_credit_hour,
        Theory_duration_per_contact_Hour_in_minutes:
          row.data.Theory_duration_per_contact_Hour_in_minutes,
        Theory_duration_in_minutes: row.data.Theory_duration_in_minutes,
        Practical_Credit_Hours: row.data.Practical_Credit_Hours,
        Practical_Contact_hours_per_credit_hour: row.data.Practical_Contact_hours_per_credit_hour,
        Practical_duration_per_contact_Hour_in_minutes:
          row.data.Practical_duration_per_contact_Hour_in_minutes,
        Practical_duration_in_minutes: row.data.Practical_duration_in_minutes,
        Clinical_Credit_Hours: row.data.Clinical_Credit_Hours,
        Clinical_Contact_hours_per_credit_hour: row.data.Clinical_Contact_hours_per_credit_hour,
        Clinical_duration_per_contact_Hour_in_minutes:
          row.data.Clinical_duration_per_contact_Hour_in_minutes,
        Clinical_duration_in_minutes: row.data.Clinical_duration_in_minutes,
        Allow_to_edit_credit_hours_while_scheduling:
          row.data.Allow_to_edit_credit_hours_while_scheduling,
        Should_Achieve_target_credit_hours: row.data.Should_Achieve_target_credit_hours,
      };
    });
    this.setState({ invalidList: data });

    exportToExcel(data, 'InValidCourseList');
  };
  cancelBtn = () => {
    this.setState(
      {
        dataCheckShow: false,
      },
      () => {
        this.props.closed(false);
      }
    );
  };

  inValidDataCheck = () => {
    this.setState({ directValid: false });
  };
  render() {
    const {
      filename,
      csvRecords,
      directValid,
      dataCheckShow,
      importFormatError,
      tempMismatch,
      importBtnEnable,
    } = this.state;
    const { InvalidDataCourseList, validDataCourseList } = this.props;

    return (
      <div className="main pt-3 pb-5 bg-white">
        <Modal show={dataCheckShow} dialogClassName="model-800">
          <div className="container">
            <p className="f-20 mb-2 pt-3">
              {' '}
              <Trans i18nKey={'import_course'}></Trans>
            </p>
          </div>

          <Modal.Body>
            <div className="model-main bg-gray pl-3 border-radious-8">
              <p className="f-14 mb-2 pt-2 text-gray">
                {' '}
                <Trans i18nKey={'select_appropriate'}></Trans>
              </p>

              <div className="row pb-5">
                <div className="col-md-5">
                  <b className="f-16 mb-4 pt-3">
                    <Trans i18nKey={'select_a_file'}></Trans> <br></br>
                    <small>
                      {' '}
                      <Trans i18nKey={'accepted_formats'}></Trans>{' '}
                    </small>
                  </b>
                  {filename === null ? (
                    ''
                  ) : (
                    <div className="bg-lightdark border-radious-8 w-75 ">
                      <b className=" pl-2 pr-2 f-14" onClick={this.handleChangeData}>
                        {filename.length > 15 ? filename.substring(0, 4) + '...' : filename}{' '}
                        <i
                          className={`fa fa-times remove_hover import_remove_icon ${
                            lng === 'ar' ? 'float-right' : ''
                          }`}
                          aria-hidden="true"
                        ></i>
                      </b>
                    </div>
                  )}
                </div>

                <div className="col-md-7">
                  <b className="pl-5">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary  import-padding border-radious-8"
                    >
                      <Trans i18nKey={'browse'}></Trans>{' '}
                      <input
                        id="fileUpload"
                        type="file"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        value=""
                        onChange={this.handleChange}
                      />
                    </label>
                  </b>

                  <Link>
                    <b className="pl-5 text-blue" onClick={() => this.sampleData()}>
                      {' '}
                      <i className="fa fa-download pr-2" aria-hidden="true"></i>
                      <Trans i18nKey={'download_template'}></Trans>
                    </b>
                  </Link>
                </div>
              </div>

              <div className="border-radious-8 bg-lightgray">
                <div className="row d-flex justify-content-center">
                  <div className="col-md-11 pt-5 ">
                    <div className="p-2 bg-lightdark border-radious-8">
                      <Table className="">
                        <thead>
                          <tr className="no-border">
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_code'}></Trans>{' '}
                              </Button>
                            </th>

                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'Curriculum_Name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'year'}></Trans>
                              </Button>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {csvRecords &&
                            csvRecords
                              .filter((val, i) => i < 3)
                              .map((item, i) => {
                                return (
                                  <tr className="border-dark-import" key={i}>
                                    <td className="border-left-import">{item.Program_Name}</td>
                                    <td className="border-left-import">{item.Program_Code}</td>

                                    <td className="border-left-import">{item.Curriculum_Name}</td>
                                    <td className="border-left-import">{item.Year}</td>
                                  </tr>
                                );
                              })}
                        </tbody>
                      </Table>
                    </div>
                  </div>

                  <div className="col-md-11 pt-2 pb-2">
                    <b className="float-right f-14">
                      {csvRecords.length} <Trans i18nKey={'rows_import'}></Trans>{' '}
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <div className={`${lng === 'ar' ? 'pl-4' : 'pr-4'}`}>
              <p className="text-blue mb-0 remove_hover" onClick={this.cancelBtn}>
                {' '}
                <Trans i18nKey={'cancel'}></Trans>{' '}
              </p>
            </div>

            <div className="pr-2">
              <Button
                variant={importBtnEnable && csvRecords.length > 0 ? 'primary' : 'secondary'}
                disabled={importBtnEnable && csvRecords.length > 0 ? false : true}
                onClick={this.afterImport}
              >
                <Trans i18nKey={'import'}></Trans>
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {!directValid && (
          <Modal show={true} size="lg">
            <div className="container">
              <p className="f-20 mb-1 pt-3">
                {' '}
                <Trans i18nKey={'import_course'}></Trans>
              </p>

              <p className="f-14 mb-2 ">
                {' '}
                {InvalidDataCourseList?.size} {t('of')} {csvRecords.length} {t('not_imported')}
              </p>
            </div>

            <Modal.Body>
              <div className="pb-1">
                <p className="f-16 mb-2">
                  <Trans i18nKey={'data_check'}></Trans>
                </p>
                <div className="row">
                  <div className="col-md-6">
                    <p className="f-14 mb-2">
                      {' '}
                      <Trans i18nKey={'list_error_entity'}></Trans>.
                    </p>
                  </div>

                  <p
                    className="f-14 mb-2 float-right text-blue cursor-pointer"
                    onClick={() => this.exportList()}
                  >
                    <Trans i18nKey={'export_entry'}></Trans>
                  </p>
                </div>

                <div className="go-wrapper">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>
                          <div className="aw-75">
                            <Trans i18nKey={'s_no'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-100">
                            <Trans i18nKey={'program_name'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-200">
                            <Trans i18nKey={'program_code'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-300">
                            <Trans i18nKey={'error_message'}></Trans>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="go-wrapper-height tr-change">
                      {InvalidDataCourseList &&
                        InvalidDataCourseList.map((item, i) => {
                          return (
                            <tr key={i}>
                              <td>
                                <div className="aw-75">{i + 1}</div>
                              </td>
                              <td>
                                <div className="aw-100">{item.getIn(['data', 'Program_Name'])}</div>
                              </td>
                              <td>
                                <div className="aw-200">{item.getIn(['data', 'Program_Code'])}</div>
                              </td>

                              <td>
                                <div className="aw-300">
                                  {item.getIn(['message']).map((message, i) => (
                                    <li key={i}>{message}</li>
                                  ))}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <div className="pr-4">
                <p className="text-blue mb-0 remove_hover" onClick={this.importListBack}>
                  {' '}
                  <Trans i18nKey={'back'}></Trans>
                </p>
              </div>

              <div className="pr-2">
                <Button
                  variant={
                    validDataCourseList && validDataCourseList.size > 0 ? 'primary' : 'Secondary'
                  }
                  onClick={this.importContinue}
                  disabled={validDataCourseList && validDataCourseList.size === 0}
                >
                  <Trans i18nKey={'continue'}></Trans>
                </Button>
              </div>
            </Modal.Footer>
          </Modal>
        )}
        {importFormatError && (
          <div>
            <ErrorModal
              showDetail={importFormatError}
              title={`Error: Invalid file format`}
              content={`You have uploaded an invalid file type. Accepted file formats is .xlsx`}
              filename={this.fileName}
            />
          </div>
        )}

        {tempMismatch && (
          <div>
            <ErrorModal
              showDetail={tempMismatch}
              title={t('modal.messages.template_mismatch')}
              content={t('modal.messages.template_content')}
              filename={this.fileName}
            />
          </div>
        )}
        {/* import table modalend */}
      </div>
    );
  }
}

ImportFromCourseFile.propTypes = {
  ImportCourseCsvCheck: PropTypes.func,
  closed: PropTypes.func,
  validDataCourseList: PropTypes.instanceOf(List),
  ImportCourseCsv: PropTypes.func,
  InvalidDataCourseList: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    InvalidDataCourseList: selectInvalidDataCourseList(state),
    validDataCourseList: selectvalidDataCourseList(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ImportFromCourseFile);
