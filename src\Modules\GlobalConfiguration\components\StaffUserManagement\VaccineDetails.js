import React from 'react';
import PropTypes from 'prop-types';
import MuiAccordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { styled } from '@mui/styles';
import { List, Map } from 'immutable';
import { Trans } from 'react-i18next';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';

export const Accordion = styled((props) => (
  <MuiAccordion disablegutters="true" elevation={0} square {...props} />
))(({ theme }) => ({
  color: 'rgb(61, 81, 112)',
  backgroundColor: 'transparent',
  '&:before': {
    display: 'none',
  },
  '& .MuiAccordionSummary-content': {
    display: 'block',
  },
  '& .MuiAccordionDetails-root': {
    display: 'block',
    padding: '8px 0 16px',
  },
  '& .MuiAccordionSummary-expandIcon.Mui-expanded': {
    marginTop: '18px',
  },
  '& .MuiAccordionSummary-expandIcon': {
    marginTop: '10px',
  },
}));

const VaccineDetails = ({ details, openDetailsModal }) => {
  const handleDetailsModal = (e, type) => {
    e.stopPropagation();
    openDetailsModal(type);
  };

  return (
    <div className="digi-vaccinebg mt-3 digi-pl-12 digi-pr-12">
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <div className="row no-gutters align-items-center">
            <div className="col-9 f-14 digi-brown">
              <p className="bold mb-1">
                {details.get('vaccineName')} • {details.get('vaccineNumber')}
              </p>
              <p className="digi-light-gray mb-0">{details.get('vaccineType')}</p>
            </div>

            <div className="col-3 text-right">
              <img
                src={EditIcon}
                alt="edit"
                className="mx-4 remove_hover"
                onClick={(e) => handleDetailsModal(e, 'edit')}
              />
              <img
                src={DeleteIcon}
                alt="Delete"
                className="mr-2 remove_hover"
                onClick={(e) => handleDetailsModal(e, 'delete')}
              />
            </div>
          </div>
        </AccordionSummary>
        <AccordionDetails>
          <div className="row">
            <div className="col-sm-6 col-md-4 col-lg-4">
              <p className="digi-mb-0 f-14 digi-gray">
                <Trans i18nKey={'global_configuration.company_name'} />
              </p>
              <p className="bold">{details.get('companyName')}</p>
            </div>
            <div className="col-sm-6 col-md-4 col-lg-4">
              <p className="digi-mb-0 f-14 digi-gray">
                <Trans i18nKey={'global_configuration.brand_name'} />
              </p>
              <p className="bold">{details.get('brandName')}</p>
            </div>
            <div className="col-sm-6 col-md-4 col-lg-4">
              <p className="digi-mb-0 f-14 digi-gray">
                <Trans i18nKey={'global_configuration.antigen_name'} />
              </p>
              <p className="bold">{details.get('antigenName')}</p>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-6 col-md-4 col-lg-4">
              <p className="digi-mb-0 f-14 digi-gray">
                <Trans i18nKey={'global_configuration.total_dosage'} />
              </p>
              <p className="bold">{details.get('noOfDosage')}</p>
            </div>
            <div className="col-sm-6 col-md-4 col-lg-4">
              <p className="digi-mb-0 f-14 digi-gray">
                <Trans i18nKey={'global_configuration.booster_dose'} />
              </p>
              <p className="bold">{details.get('noOfBooster')}</p>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-6 col-md-4 col-lg-4">
              {details.get('dosageDetails', List()).map((dose, doseIndex) => (
                <p key={doseIndex} className="digi-mb-0">
                  <b>{dose.get('labelName')}</b>
                  <span className="digi-pl-12">Day {dose.get('days')}</span>
                </p>
              ))}
            </div>
            <div className="col-sm-6 col-md-4 col-lg-4">
              {details.get('boosterDetails', List()).map((booster, boosterIndex) => (
                <p key={boosterIndex} className="digi-mb-0">
                  <b>{booster.get('labelName')}</b>
                  <span className="digi-pl-12">Day {booster.get('days')}</span>
                </p>
              ))}
            </div>
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};

VaccineDetails.propTypes = {
  details: PropTypes.instanceOf(Map),
  openDetailsModal: PropTypes.func,
};

export default VaccineDetails;
