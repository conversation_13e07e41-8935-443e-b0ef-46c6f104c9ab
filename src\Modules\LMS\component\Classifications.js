import React, {
  useEffect,
  useCallback,
  useState,
  createContext,
  useRef,
  lazy,
  Suspense,
} from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';
import { connect } from 'react-redux';
import { Col, Row } from 'react-bootstrap';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';

// utils
import { useStylesFunction } from '../designUtils';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { checkPermissionSub, getLeaveTypeName, IOSSwitch } from '../utils';
import Tooltip from 'Widgets/FormElements/material/Tooltip';
import useDebounce from 'Hooks/useDebounceHook';

// redux
import * as actions from '_reduxapi/leave_management/actions';
import { selectLmsSettings } from '_reduxapi/leave_management/selectors';

// components
import Configurations from './Configurations';
import ApprovalLevelAndRoles from './ApprovalLevelAndRoles';
import AddDocumentPopup from './Modal/AddDocumentPopup';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import LeavePolicyDocuments from './LeavePolicyDocument';
import AddIcon from '@mui/icons-material/Add';

export const ClassificationsContext = createContext({});
const CategoryPopup = lazy(() => import('./Modal/CategoryPopup'));

const Classifications = ({
  changeSettings,
  updateGlobalConfig,
  addCategory,
  isEditPolicyDocs,
  getLmsSettings,
  lmsSettings,
  type,
  subType,
  updateTermsAndConditions,
  setData,
  isLeavePolicy,
  addLeavePolicy,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [termsAndCondition, setTermsAndCondition] = useState('');
  const [leaveCalculation, setLeaveCalculation] = useState('hours');
  const [expanded, setExpanded] = useState(
    fromJS({
      generalConfig: true,
    })
  );

  const [policy, setPolicy] = useState(Map({ attachmentDocument: List() }));
  const [parentApplyPermission, setParentApplyPermission] = useState(false);
  const [parentAcknowledgePermission, setParentAcknowledgePermission] = useState(false);
  const [isOpenLeaveDoc, setIsOpenLeaveDoc] = useState(false);
  const flagRef = useRef(false);
  const categoriesRef = useRef({ categoryName: '', percentage: '' });

  const debounceTermsAndCondition = useDebounce(termsAndCondition, 500);

  const { accordion, AccordionPadding } = useStylesFunction();

  const ListApi = useCallback(() => {
    getLmsSettings(isLeavePolicy ? 'leave' : type);
  }, [getLmsSettings, type, isLeavePolicy]);

  const settingId = lmsSettings.get('_id', '');
  const classificationType = lmsSettings.get('classificationType', '');
  useEffect(() => {
    ListApi();
  }, [ListApi]);

  useEffect(() => {
    setTermsAndCondition(lmsSettings.get('termsAndCondition', ''));
    setLeaveCalculation(lmsSettings.get('leaveCalculation', ''));
    setParentApplyPermission(lmsSettings.getIn(['generalConfig', 'parentApplyPermission'], false));
    setParentAcknowledgePermission(
      lmsSettings.getIn(['generalConfig', 'parentAcknowledgePermission'], false)
    );
  }, [lmsSettings]);

  const handlePopup = (e, open) => {
    e.stopPropagation();
    categoriesRef.current = { categoryName: '', percentage: '' };
    setIsOpen(open);
  };

  const handleLeaveDocPopup = (e, open) => {
    e.stopPropagation();
    setIsOpenLeaveDoc(open);
    setPolicy(Map({ attachmentDocument: List(), allowStudentVisibilityStatus: false }));
  };

  const createCategories = () => {
    let isCategoryNameExist = lmsSettings
      .get('catagories', Map())
      .some(
        (category) =>
          category.get('categoryName', '').toLowerCase().trim() ===
          categoriesRef.current.categoryName.toLowerCase().trim()
      );

    if (categoriesRef.current.categoryName.trim() === '')
      return setData(Map({ message: 'Category Name is Required' }));
    if (isCategoryNameExist) return setData(Map({ message: 'Category Name Already Exists' }));
    if (classificationType === 'leave') {
      if (categoriesRef.current.percentage === '' || Number(categoriesRef.current.percentage) === 0)
        return setData(Map({ message: 'Percentage is Required' }));
    }

    addCategory(
      {
        categoryName: categoriesRef.current.categoryName,
        percentage: categoriesRef.current.percentage,
        settingId: lmsSettings.get('_id', ''),
      },
      ListApi
    );

    categoriesRef.current = { categoryName: '', percentage: '' };
    setIsOpen(false);
  };

  const handleDocChange = (e, type) => {
    const { value, checked } = e.target;
    let inputValues = '';
    switch (type) {
      case 'documentTitle':
        inputValues = value;
        break;
      case 'description':
        inputValues = value;
        break;
      case 'allowStudentVisibilityStatus':
        inputValues = checked;
        break;
      default:
    }

    setPolicy(policy.set(type, inputValues));
  };

  const handleAddDocDetails = () => {
    if (!policy.get('documentTitle', '').trim())
      return setData(Map({ message: 'Document Title is Required' }));

    let isDocumentTitleExist = lmsSettings
      .get('leavePolicy', Map())
      .map((currentPolicy) => currentPolicy.get('documentTitle', '').toLowerCase().trim())
      .some(
        (currentPolicy) => currentPolicy === policy.get('documentTitle', '').toLowerCase().trim()
      );
    if (isDocumentTitleExist) return setData(Map({ message: 'Document Title Already Exists' }));

    addLeavePolicy({ settingId: lmsSettings.get('_id', ''), ...policy.toJS() }, ListApi);
    setPolicy(Map({ attachmentDocument: List() }));
    setIsOpenLeaveDoc(false);
  };

  useEffect(() => {
    const callBack = () => {
      setData(
        Map({
          lmsSettings: lmsSettings
            .set('termsAndCondition', termsAndCondition)
            .set('leaveCalculation', leaveCalculation),
        })
      );
    };
    flagRef.current &&
      updateTermsAndConditions(
        {
          settingId: lmsSettings.get('_id', ''),
          termsAndCondition,
          leaveCalculation,
        },
        callBack
      );
  }, [debounceTermsAndCondition, leaveCalculation, flagRef]); //eslint-disable-line

  const handleUpdateWarning = (e, type) => {
    const { value } = e.target;
    flagRef.current = true;
    type === 'termsAndCondition' ? setTermsAndCondition(value) : setLeaveCalculation(value);
  };

  const value = {
    lmsSettings,
    ListApi,
    type,
    classificationType,
  };

  const toolTip = (value) => {
    return (
      <div className="pt-1">
        <Tooltip
          title={
            <div>
              <div className="font-weight-bold mb-2 f-16">
                Leave Management Calculation ({value}-Based)
              </div>
              <div className="f-14 mb-1">
                <span className="font-weight-bold">Attendance </span>{' '}
                <span className="font-weight-normal">
                  = 100 x No. of {value} Attended / No. of Conducted {value}
                </span>
              </div>
              <div className="f-14 mb-1">
                <span className="font-weight-bold">Absent </span>{' '}
                <span className="font-weight-normal">
                  = 100 x No. of Not Attended {value} / Total No. of Conducted {value}
                </span>
              </div>
              <div className="f-14 mb-1">
                <span className="font-weight-bold">Warning Calculation </span>{' '}
                <span className="font-weight-normal">
                  = 100 x No. of Not Attended {value} / Total No. of Scheduled {value}
                </span>
              </div>
            </div>
          }
          color="white"
          maxWidth={1000}
          placement="top-end"
        >
          <span className="ml-2">
            <InfoOutlinedIcon sx={{ fontSize: 16 }} />
          </span>
        </Tooltip>
      </div>
    );
  };
  const isEditGeneralConfig = checkPermissionSub(
    type === 'leave' ? 'General Edit' : 'General Configuration Edit',
    type
  );
  const isEditLeaveClassification = checkPermissionSub('Leave Classifications Edit', type);

  const isEditTypeWiseConfig = checkPermissionSub(
    `${getLeaveTypeName(type, true)} Configuration Edit`,
    type
  );
  const isEditApproveLevels = checkPermissionSub(
    `${getLeaveTypeName(type, true)} Approval Levels and Roles Edit`,
    type
  );

  return (
    <ClassificationsContext.Provider value={value}>
      <div className={`m-4 digi-mb-70`}>
        {type !== 'leave' && !isLeavePolicy && (
          <div className="d-flex align-items-center mb-4">
            <span
              className="digi-color-blue mt-1 digi-cursor-pointer"
              onClick={() => changeSettings('settings')}
            >
              <KeyboardArrowLeftIcon />
            </span>
            <span
              className="digi-color-blue digi-cursor-pointer"
              onClick={() => changeSettings('settings')}
            >
              Settings &nbsp;
            </span>
            <span className="digi-light-gray text-capitalize">
              /&nbsp; {getLeaveTypeName(type)} Classification
            </span>
          </div>
        )}

        {subType !== 'leaveConfigurations' &&
          !isLeavePolicy &&
          checkPermissionSub(
            type === 'leave' ? 'General View' : 'General Configuration View',
            type
          ) && (
            <Accordion
              className={`${accordion} mb-3`}
              expanded={expanded.get('generalConfig', false)}
              onChange={() =>
                setExpanded(expanded.set('generalConfig', !expanded.get('generalConfig')))
              }
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className={`panelSummary`}
              >
                <Typography
                  className="mt-1 digi-lms-perm-divider flex-grow-1"
                  sx={{ flexShrink: 0, fontWeight: '500' }}
                >
                  General Configurations
                </Typography>
                <div className="mb-2">&nbsp;</div>
              </AccordionSummary>
              {expanded.get('generalConfig', false) && (
                <AccordionDetails className={`${AccordionPadding}`}>
                  <div className="mb-4">
                    <div className="d-flex justify-content-between align-items-center digi-lms-perm-divider mt-3">
                      <div className="digi-light-gray">
                        Parents apply for{' '}
                        <span className="text-lowercase">{getLeaveTypeName(type)}</span> on behalf
                        of students
                      </div>
                      <div className="mb-2">
                        <IOSSwitch
                          checked={parentApplyPermission || false}
                          disabled={true}
                          // disabled={!isEditGeneralConfig}
                          inputProps={{ 'aria-label': 'ant design' }}
                          onChange={() => {
                            setParentApplyPermission(!parentApplyPermission);
                            updateGlobalConfig(
                              {
                                parentApplyPermission: !lmsSettings.getIn(
                                  ['generalConfig', 'parentApplyPermission'],
                                  false
                                ),
                              },
                              settingId,
                              setData(
                                Map({
                                  lmsSettings: lmsSettings.setIn(
                                    ['generalConfig', 'parentApplyPermission'],
                                    !parentApplyPermission
                                  ),
                                })
                              )
                            );
                          }}
                        />
                      </div>
                    </div>
                    <div className="d-flex justify-content-between align-items-center digi-lms-perm-divider mt-3">
                      <div className="digi-light-gray">
                        Parents acknowledge student{' '}
                        <span className="text-lowercase">{getLeaveTypeName(type)}</span> requests
                      </div>
                      <div className="mb-3">
                        <IOSSwitch
                          checked={parentAcknowledgePermission || false}
                          disabled={true}
                          // disabled={!isEditGeneralConfig}
                          inputProps={{ 'aria-label': 'ant design' }}
                          onChange={() => {
                            setParentAcknowledgePermission(!parentAcknowledgePermission);
                            updateGlobalConfig(
                              {
                                parentAcknowledgePermission: !lmsSettings.getIn(
                                  ['generalConfig', 'parentAcknowledgePermission'],
                                  false
                                ),
                              },
                              settingId,
                              setData(
                                Map({
                                  lmsSettings: lmsSettings.setIn(
                                    ['generalConfig', 'parentAcknowledgePermission'],
                                    !parentAcknowledgePermission
                                  ),
                                })
                              )
                            );
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  {type === 'leave' && (
                    <>
                      <Row className="mb-4">
                        <Col lg={5}>
                          <div className="digi-light-gray mb-1">Terms And Conditions:</div>
                          <div>
                            <MaterialInput
                              bgWhite={true}
                              elementType={'materialTextArea'}
                              placeholder={'Add Your Terms And Conditions Here'}
                              name={'description'}
                              disabled={!isEditGeneralConfig}
                              maxRows={'4'}
                              minRows={'4'}
                              value={termsAndCondition}
                              changed={(e) => handleUpdateWarning(e, 'termsAndCondition')}
                            />
                          </div>
                        </Col>
                      </Row>

                      <div className="mb-4">
                        <div className="digi-light-gray d-flex align-items-center">
                          <span className="mr-1">Leave Calculation Settings:</span>
                        </div>
                        <FormControl disabled={!isEditGeneralConfig}>
                          <RadioGroup
                            row
                            aria-labelledby="demo-row-radio-buttons-group-label"
                            name="row-radio-buttons-group"
                            onChange={(e) => handleUpdateWarning(e, 'leaveCalculation')}
                          >
                            <FormControlLabel
                              value="hours"
                              control={<Radio size="small" disabled />}
                              label={
                                <div className="pt-1 d-flex align-items-center">
                                  <div className="f-18">Credit-Contact Hours/Periods </div>
                                  <div className="pt-1">{toolTip('Contact Hours')}</div>
                                </div>
                              }
                              checked={leaveCalculation === 'hours'}
                            />
                            <FormControlLabel
                              value="sessions"
                              control={<Radio size="small" className="" />}
                              label={
                                <div className="pt-1 d-flex align-items-center">
                                  <div className="f-18">No. of Sessions </div>
                                  {toolTip('Sessions')}
                                </div>
                              }
                              checked={leaveCalculation === 'sessions'}
                            />
                          </RadioGroup>
                        </FormControl>
                      </div>
                    </>
                  )}
                </AccordionDetails>
              )}
            </Accordion>
          )}

        {lmsSettings.get('generalConfig', true) && !isLeavePolicy && (
          <>
            {(type !== 'leave' || subType === 'leaveConfigurations') &&
              (type === 'leave'
                ? checkPermissionSub(`${getLeaveTypeName(type, true)} Classifications View`, type)
                : checkPermissionSub(
                    `${getLeaveTypeName(type, true)} Configuration View`,
                    type
                  )) && (
                <Accordion
                  className={`${accordion} mb-3`}
                  expanded={expanded.get('configuration', false)}
                  onChange={() =>
                    setExpanded(expanded.set('configuration', !expanded.get('configuration')))
                  }
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                    className={`panelSummary`}
                  >
                    <Typography
                      className={`digi-lms-perm-divider flex-grow-1 ${
                        lmsSettings.get('catagories', List()).size === 0 ? 'mt-1' : 'mt-2'
                      }`}
                      sx={{ flexShrink: 0, fontWeight: '500' }}
                    >
                      {getLeaveTypeName(type)}&nbsp;Configurations
                    </Typography>
                    {lmsSettings.get('catagories', List()).size !== 0 && (
                      <div className="digi-lms-perm-divider">
                        <MButton
                          className="mb-1"
                          variant="contained"
                          color={'blue'}
                          disabled={
                            type === 'leave' ? !isEditLeaveClassification : !isEditTypeWiseConfig
                          }
                          clicked={(e) => handlePopup(e, true)}
                        >
                          Create Category
                        </MButton>
                      </div>
                    )}
                  </AccordionSummary>
                  {expanded.get('configuration', false) && (
                    <AccordionDetails>
                      {lmsSettings.get('catagories', List()).size === 0 && (
                        <div className={`d-flex mb-3 justify-content-center`}>
                          <div className="text-center">
                            <div className="digi-light-gray mb-2">
                              Create {getLeaveTypeName(type)} Categories
                            </div>

                            <MButton
                              variant="contained"
                              color={'blue'}
                              disabled={
                                type === 'leave'
                                  ? !isEditLeaveClassification
                                  : !isEditTypeWiseConfig
                              }
                              clicked={(e) => handlePopup(e, true)}
                            >
                              Create Category
                            </MButton>
                          </div>
                        </div>
                      )}

                      <Configurations
                        isEditTypeWiseConfig={isEditTypeWiseConfig}
                        isEditLeaveClassification={isEditLeaveClassification}
                      />
                    </AccordionDetails>
                  )}
                </Accordion>
              )}

            {subType !== 'leaveConfigurations' &&
              (type === 'leave'
                ? checkPermissionSub('General View', type)
                : checkPermissionSub(
                    `${getLeaveTypeName(type, true)} Approval Levels and Roles View`,
                    type
                  )) && (
                <Accordion
                  className={`${accordion} mb-3`}
                  expanded={expanded.get('approveLevel', false)}
                  onChange={() =>
                    setExpanded(expanded.set('approveLevel', !expanded.get('approveLevel')))
                  }
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                    className={`panelSummary`}
                  >
                    <Typography
                      className="mt-1 digi-lms-perm-divider flex-grow-1"
                      sx={{ flexShrink: 0, fontWeight: '500' }}
                    >
                      <span className="text-capitalize">{getLeaveTypeName(type)}</span> Approval
                      Levels and Roles
                    </Typography>
                    <div className="mb-2">&nbsp;</div>
                  </AccordionSummary>
                  {expanded.get('approveLevel', false) && (
                    <AccordionDetails>
                      <ApprovalLevelAndRoles
                        isEditApproveLevels={isEditApproveLevels}
                        lmsSettings={lmsSettings}
                        isEditGeneralConfig={isEditGeneralConfig}
                      />
                    </AccordionDetails>
                  )}
                </Accordion>
              )}
          </>
        )}

        {isLeavePolicy && (
          <Accordion
            className={`${accordion} mb-3`}
            expanded={expanded.get('leavePolicy', false)}
            onChange={() => setExpanded(expanded.set('leavePolicy', !expanded.get('leavePolicy')))}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="panel1bh-content"
              id="panel1bh-header"
              className={`panelSummary`}
            >
              <Typography
                className={`digi-lms-perm-divider flex-grow-1 ${
                  lmsSettings.get('catagories', List()).size === 0 ? 'mt-1' : 'mt-1'
                }`}
                sx={{ flexShrink: 0, fontWeight: '500' }}
              >
                Leave Policy Documentary
              </Typography>
              <div className="digi-lms-perm-divider flex-grow-1"></div>
              {lmsSettings.get('leavePolicy', List()).size !== 0 && (
                <div className="digi-lms-perm-divider">
                  <MButton
                    size="small"
                    variant="text"
                    color={'blue'}
                    disabled={!isEditPolicyDocs}
                    startIcon={<AddIcon />}
                    clicked={(e) => handleLeaveDocPopup(e, true)}
                  >
                    Add Document
                  </MButton>
                </div>
              )}
            </AccordionSummary>
            <AccordionDetails>
              {expanded.get('leavePolicy') && (
                <>
                  {lmsSettings.get('leavePolicy', List()).size === 0 && (
                    <div className={`d-flex mb-3 justify-content-center`}>
                      <div className="text-center">
                        <div className="digi-light-gray mb-2">Add document here</div>

                        <MButton
                          variant="contained"
                          disabled={!isEditPolicyDocs}
                          color={'blue'}
                          clicked={(e) => handleLeaveDocPopup(e, true)}
                        >
                          Add
                        </MButton>
                      </div>
                    </div>
                  )}
                  <LeavePolicyDocuments isEditPolicyDocs={isEditPolicyDocs} />
                </>
              )}
              {isOpenLeaveDoc && (
                <AddDocumentPopup
                  isOpen={isOpenLeaveDoc}
                  handlePopup={handleLeaveDocPopup}
                  typeAction={'Add'}
                  policy={policy}
                  handleDocChange={handleDocChange}
                  handleAddDocDetails={handleAddDocDetails}
                  setPolicy={setPolicy}
                />
              )}
            </AccordionDetails>
          </Accordion>
        )}
        {isOpen && (
          <Suspense fallback="">
            <CategoryPopup
              isOpen={isOpen}
              handlePopup={handlePopup}
              createCategories={createCategories}
              categoriesRef={categoriesRef}
            />
          </Suspense>
        )}
      </div>
    </ClassificationsContext.Provider>
  );
};

Classifications.propTypes = {
  changeSettings: PropTypes.func,
  updateGlobalConfig: PropTypes.func,
  getLmsSettings: PropTypes.func,
  lmsSettings: PropTypes.instanceOf(Map),
  addCategory: PropTypes.func,
  updateTermsAndConditions: PropTypes.func,
  type: PropTypes.string,
  subType: PropTypes.string,
  setData: PropTypes.func,
  isLeavePolicy: PropTypes.bool,
  isEditPolicyDocs: PropTypes.bool,
  addLeavePolicy: PropTypes.func,
  editLeavePolicy: PropTypes.func,
  uploadAttachment: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    lmsSettings: selectLmsSettings(state),
  };
};

export default connect(mapStateToProps, actions)(Classifications);
