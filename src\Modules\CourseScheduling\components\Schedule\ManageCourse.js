import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Map, List, fromJS } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import CourseExtendInfo from './CourseExtendInfo';
import ManageScheduleListView from './ManageScheduleListView';
import ManageScheduleCalendarView from './ManageScheduleCalendarView';
import ManageCourseTabs from './ManageCourseTabs';
import AdvanceSettings from './AdvanceSettings';
import ManageTopics from './ManageTopics';
import AlertConfirmModal from '../../modal/AlertConfirmModal';
import * as actions from '../../../../_reduxapi/course_scheduling/action';
import withCountryCodeHooks from 'Hoc/withCountryCodeHooks';
import {
  selectIndividualCourseDetails,
  selectIndividualSessionDetails,
  selectMcActiveTab,
  selectCourseGroupSettingDetails,
  selectAdvancedSettings,
  selectManageTopics,
  selectActiveSettingsView,
  selectAutoAssignCourseDeliveryList,
  selectCourseSchedule,
  selectLoading,
  selectPaginationCourseMetaData,
  selectStaffScheduleOptionList,
} from '../../../../_reduxapi/course_scheduling/selectors';
import {
  selectUserId,
  selectActiveInstitutionCalendar,
} from '../../../../_reduxapi/Common/Selectors';
import { getURLParams, isModuleEnabled } from '../../../../utils';
import ViewCreditHours from '../../modal/ViewCreditHours';
import AddEditCourseScheduleModal from '../../modal/AddEditCourseScheduleModal';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { getRemotePlatform, validateManageCourseSchedule } from './utils';
import { t } from 'i18next';
import withCalendarHooks from 'Hoc/withCalendarHooks';

const initialModalData = Map({
  show: false,
  settingId: '',
  sessionTypeId: '',
  deliveryData: Map(),
  mode: 'create',
  schedule: Map({
    type: '',
    student_groups: List(),
    mode: '',
    subjects: List(),
    staffs: List(),
    _infra_id: '',
    infra_name: '',
    remotePlatform: '',
    topic: '',
    _topic_id: '',
    manualStaffs: List(),
    mobileNumberOrEmail: '',
    ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
      outsideCampus: false,
      selectNumOrEmail: '',
    }),
  }),
});
export class ManageCourse extends Component {
  constructor() {
    super();
    this.state = {
      viewCreditHours: false,
      modalData: initialModalData,
      alertConfirmModalData: { show: false },
    };
  }

  componentDidMount() {
    const { institutionCalendarId } = this.props;
    if (institutionCalendarId !== '') {
      this.triggerFunction();
      this.checkPermission();
    }
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.institutionCalendarId !== '' &&
      this.props.institutionCalendarId !== prevProps.institutionCalendarId
    ) {
      this.triggerFunction();
      this.checkPermission();
    }
  }

  checkPermission = () => {
    const { setData } = this.props;
    if (
      CheckPermission(
        'subTabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        '',
        'Manage Course',
        'Session Default Settings View'
      )
    ) {
      setData(Map({ mcActiveTab: 'Session Default Settings' }));
    } else if (
      CheckPermission(
        'subTabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        '',
        'Manage Course',
        'Manage Topic View'
      )
    ) {
      setData(Map({ mcActiveTab: 'Manage Topic' }));
    } else if (
      CheckPermission(
        'subTabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        '',
        'Manage Course',
        'Advance Settings View'
      )
    ) {
      setData(Map({ mcActiveTab: 'Advance Settings' }));
    }
  };

  triggerFunction = () => {
    const {
      getIndividualCourse,
      getAdvancedSettings,
      getManageTopics,
      programId,
      institutionCalendarId,
      courseId,
      term,
      level,
      setData,
      getScheduleStaffOptionList,
    } = this.props;
    setData(Map({ advancedSettings: Map() }));
    getIndividualCourse(programId, institutionCalendarId, courseId, term, level);
    getAdvancedSettings(programId, courseId, level);
    getManageTopics(programId, courseId, level, institutionCalendarId);
    getScheduleStaffOptionList(programId, courseId, level);
  };

  saveAdvanceSetting = (data) => {
    const { programId, courseId, level, saveAdvanceSetting } = this.props;
    saveAdvanceSetting(data, programId, courseId, level, 'manage-course');
  };

  manualStaffSaveSetting = (data, callBack) => {
    const { programId, courseId, level, setStaffList } = this.props;
    setStaffList(data, courseId, programId, level, callBack);
  };

  saveMissedSession = (data) => {
    const { programId, courseId, level, saveMissedSession } = this.props;
    saveMissedSession(data, programId, courseId, level, 'manage-course');
  };

  modalClick = () => {
    const { viewCreditHours } = this.state;
    this.setState({ viewCreditHours: !viewCreditHours });
  };

  isRotation() {
    return this.props.courseSchedule.get('rotation', '') === 'yes';
  }

  handleAddEditSchedule = (delivery, sessionId, mode, existingSetting) => {
    const { modalData } = this.state;
    const {
      getManageCourseSettings,
      programId,
      institutionCalendarId,
      courseId,
      term,
      level,
    } = this.props;
    if (mode === 'create') {
      let updatedModalData = modalData.merge(
        Map({
          show: true,
          deliveryData: delivery,
          sessionTypeId: sessionId,
          mode,
        })
      );
      if (this.isRotation()) {
        updatedModalData = updatedModalData.setIn(['schedule', 'rotation_count'], null);
      }
      this.setState({ modalData: updatedModalData });
      getManageCourseSettings(
        programId,
        institutionCalendarId,
        courseId,
        term,
        level,
        delivery.get('delivery_id')
      );
    }
    if (mode === 'update') {
      getManageCourseSettings(
        programId,
        institutionCalendarId,
        courseId,
        term,
        level,
        delivery.get('delivery_id'),
        existingSetting.get('_id')
      );
      this.loadEditSettings(delivery, sessionId, mode, existingSetting);
    }
  };

  loadEditSettings = (delivery, sessionId, mode, existingSetting) => {
    this.interval = setInterval(() => {
      const { courseGroupSettingDetails } = this.props;
      if (courseGroupSettingDetails.has('subjects')) {
        const selectedSubjectId = existingSetting
          .get('subjects', List())
          .map((item) => item.get('_subject_id'));
        const selectedSubject = courseGroupSettingDetails
          .get('subjects')
          .filter((item) => selectedSubjectId.includes(item.get('_subject_id')));
        const subjectIndex = courseGroupSettingDetails
          .get('subjects')
          .findIndex((item) => selectedSubjectId.includes(item.get('_subject_id')));
        const { modalData } = this.state;
        const sessionFlow = courseGroupSettingDetails.getIn([
          'subjects',
          subjectIndex,
          'session_flow',
        ]);
        const checkId = existingSetting.getIn(['session', 0, '_session_id']);
        const checkStudentGroups = existingSetting
          .getIn(['session', 0, 'student_groups'], List())
          .map((item) => item.get('group_id', ''))
          .toJS();

        const studentGroups = sessionFlow
          .filter((item) => item.get('_id') === checkId)
          .reduce((_, el) => el.get('student_group'), List());
        const filteredStudentGroups = studentGroups.filter((item) =>
          checkStudentGroups.includes(item.get('_id'))
        );

        let modifiedStudentGroup = List();
        if (filteredStudentGroups.size > 0) {
          modifiedStudentGroup = filteredStudentGroups.toJS().map((item) => {
            const session_group = item.session_group.filter((sess) => sess.checked_status === true);
            return { ...item, session_group: session_group };
          });
        }
        const schedule = Map({
          student_groups: fromJS(modifiedStudentGroup),
          subjects: selectedSubject,
          mode: existingSetting.get('mode'),
          _topic_id: existingSetting.get('_topic_id', ''),
          topic: existingSetting.get('topic', ''),
          staffs: existingSetting.get('staffs', List()).map((staff) =>
            Map({
              _id: staff.get('_staff_id'),
              _staff_id: staff.get('_staff_id'),
              name: staff.get('staff_name'),
              staff_name: staff.get('staff_name'),
            })
          ),
          _infra_id: existingSetting.get('_infra_id', ''),
          infra_name: existingSetting.get('infra_name', ''),
          ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
            outsideCampus: existingSetting.get('outsideCampus', false),
            mobileNumberOrEmail:
              existingSetting.getIn(['campusDetails', 'mobile'], '') ||
              existingSetting.getIn(['campusDetails', 'email'], ''),
          }),
          remotePlatform: getRemotePlatform(existingSetting),
          ...(this.isRotation() && { rotation_count: existingSetting.get('rotation_count', null) }),
          manualStaffs: existingSetting
            .get('attendanceTakingStaff', List())
            .map((item) => item.get('staffId', '')),
        });

        const updatedModalData = modalData.merge(
          Map({
            show: true,
            settingId: existingSetting.get('_id'),
            deliveryData: delivery,
            sessionTypeId: sessionId,
            mode,
            schedule,
          })
        );
        this.setState({ modalData: updatedModalData });
        clearInterval(this.interval);
      }
    }, 200);
  };

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  handleCloseModal = () => {
    this.setState({ modalData: initialModalData });
  };

  handleSaveDayTime = (requestBody, id, occurrenceId, mode, callBack) => {
    const {
      saveManageCourseDayTimeSet,
      programId,
      institutionCalendarId,
      courseId,
      term,
      level,
    } = this.props;
    saveManageCourseDayTimeSet({
      mode,
      id,
      occurrenceId,
      requestBody,
      callback: callBack,
      alteredBody: {
        programId,
        institutionCalendarId,
        courseId,
        term,
        level,
      },
    });
  };

  handleScheduleDataChange = (name, value) => {
    const { modalData } = this.state;
    let schedule = modalData.get('schedule', Map());
    if (name === 'mode') {
      if (value === 'remote') schedule = schedule.set('manualStaffs', fromJS([]));
      schedule = schedule.merge(
        Map({ mode: value, _infra_id: '', infra_name: '', remotePlatform: '' })
      );
    } else if (name === 'infra') {
      schedule = schedule.merge(
        Map({
          _infra_id: value.get('value', ''),
          infra_name: value.get('name', ''),
          remotePlatform: value.get('remotePlatform', ''),
          ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
            outsideCampus: value.get('outsideCampus', false),
          }),
        })
      );
    } else if (name === 'student_groups') {
      if (this.isRotation()) {
        const rotationCount = schedule.get('rotation_count');
        if (value.size === 0) {
          schedule = schedule.set('rotation_count', null);
        } else if (rotationCount === null) {
          const currentRotationCount = value.getIn([0, 'group_no']);
          if (currentRotationCount) {
            schedule = schedule.set('rotation_count', currentRotationCount);
          }
        }
      }
      schedule = schedule.set(name, value);
    } else if (name === 'topic') {
      schedule = schedule.merge(value);
    } else if (name === 'subjects') {
      schedule = schedule.set(name, value).set('staffs', List());
    } else if (name === 'manualStaffs') {
      schedule = schedule.set(name, fromJS(value));
    } else {
      schedule = schedule.set(name, value);
    }
    const updatedModalData = modalData.set('schedule', schedule);
    this.setState({ modalData: updatedModalData });
  };

  getSelectedSessionDetails = (subjects) => {
    let data = [];
    if (subjects && subjects.size > 0) {
      const { modalData } = this.state;
      const studentGroupIds = modalData
        .getIn(['schedule', 'student_groups'], List())
        .map((item) => item.get('_id'))
        .toJS();

      let sessionGroupIds = [];
      if (modalData.getIn(['schedule', 'student_groups'], List()).size > 0) {
        sessionGroupIds = modalData
          .getIn(['schedule', 'student_groups'], List())
          .reduce((acc, c) => {
            const sessionGroups = c.get('session_group', List());
            if (sessionGroups.isEmpty()) {
              return acc;
            }
            return acc.concat(
              sessionGroups.reduce((acc1, a) => {
                return acc1.push(a.get('_id'));
              }, List())
            );
          }, List())
          .toJS();
      }

      data = subjects
        .map((subject) => {
          let studentGroups = [];
          return subject.get('session_flow', List()).map((session) => {
            if (session.get('student_group', List()).size > 0) {
              studentGroups = session
                .get('student_group', List())
                .filter((item) => studentGroupIds.includes(item.get('_id')))
                .map((stGroup) => {
                  let sessionGroup = [];
                  if (stGroup.get('session_group', List()).size > 0) {
                    sessionGroup = stGroup
                      .get('session_group', List())
                      .filter((item) => sessionGroupIds.includes(item.get('_id')))
                      .filter((item) => item.get('checked_status') === true)
                      .map((ssGroup) => {
                        return {
                          session_group_id: ssGroup.get('_id'),
                          group_no: ssGroup.get('group_no'),
                          group_name: ssGroup.get('group_name'),
                        };
                      });
                  }
                  if (sessionGroup.size > 0) {
                    return Map({
                      group_id: stGroup.get('_id'),
                      gender: stGroup.get('gender'),
                      group_no: stGroup.get('group_no'),
                      group_name: stGroup.get('group_name'),
                      session_group: sessionGroup,
                    });
                  } else {
                    return Map({});
                  }
                });
            }

            const studentGrouping = studentGroups.filter((item) => !item.isEmpty());
            if (studentGrouping.size > 0) {
              return {
                session_id: session.get('_id'),
                student_groups: studentGroups
                  .toJS()
                  .filter((value) => Object.keys(value).length !== 0),
              };
            } else {
              return {};
            }
          });
        })
        .toJS();
      data = data.reduce((acc, el) => {
        return acc.concat(el);
      }, []);
      data = data.filter((value) => Object.keys(value).length !== 0);
    }

    return data;
  };

  checkTopicIsRequired = () => {
    const { courseGroupSettingDetails } = this.props;
    if (courseGroupSettingDetails.has('subjects')) {
      return courseGroupSettingDetails.get('topics', List()).size > 0;
    }
    return false;
  };

  handleSaveSchedule = (mode) => {
    const { modalData } = this.state;
    const {
      setData,
      institutionCalendarId,
      programName,
      course,
      saveManageCourseSchedule,
      manualStaffOptions,
      // countryCodeLength,
    } = this.props;
    const scheduleData = modalData.get('schedule', Map());
    // const valueToCheckMerge = scheduleData.get('mobileNumberOrEmail', '');
    // const checkedValueMerge = identifyValueSchedule(valueToCheckMerge, countryCodeLength);
    const checkTopicIsRequired = this.checkTopicIsRequired();
    const manualStaffObject = scheduleData
      .get('manualStaffs', List())
      .map((staff) =>
        manualStaffOptions
          .getIn([0, 'assignedStaffIds'], List())
          .find((staffObj) => staffObj.get('staffId', '') === staff)
      );

    const requestBody = {
      _institution_calendar_id: institutionCalendarId,
      _program_id: getURLParams('programId', true),
      program_name: programName,
      _course_id: course.get('_course_id'),
      term: course.get('term'),
      year_no: course.get('year_no'),
      level_no: course.get('level_no'),
      _session_type_id: modalData.get('sessionTypeId'),
      _delivery_id: modalData.getIn(['deliveryData', 'delivery_id']),
      rotation: this.isRotation() ? 'yes' : 'no',
      ...(this.isRotation() && { rotation_count: scheduleData.get('rotation_count') }),
      mode: scheduleData.get('mode'),
      subject: scheduleData
        .get('subjects', List())
        .map((subject) =>
          Map({
            _subject_id: subject.get('_subject_id'),
            subject_name: subject.get('subject_name'),
          })
        )
        .toJS(),
      staff: scheduleData
        .get('staffs', List())
        .map((staff) =>
          Map({
            staff_name: {
              //formatFullName(staff.get('staff_name').toJS())
              first: staff.getIn(['staff_name', 'first'], ''),
              middle: staff.getIn(['staff_name', 'middle'], ''),
              last: staff.getIn(['staff_name', 'last'], ''),
            },
            _staff_id: staff.get('_staff_id'),
          })
        )
        .toJS(),
      session_details: this.getSelectedSessionDetails(scheduleData.get('subjects', List())),
      ...(manualStaffOptions.getIn([0, 'manualAttendance'], false) && {
        attendanceTakingStaff: manualStaffObject.map((item) => item.delete('_id')).toJS(),
      }),
      // ...(isModuleEnabled('OUTSIDE_CAMPUS') && {
      //   outsideCampus: scheduleData.get('outsideCampus', false),
      //   ...(scheduleData.get('outsideCampus', false) === true && {
      //     campusDetails: {
      //       mobile:
      //         checkedValueMerge === 'MobileNumber'
      //           ? scheduleData.get('mobileNumberOrEmail', '')
      //           : '',
      //       email: checkedValueMerge === 'Gmail' ? scheduleData.get('mobileNumberOrEmail', '') : '',
      //     },
      //   }),
      // }),
    };
    if (scheduleData.get('_infra_id', '') !== '' && scheduleData.get('_infra_id', '') !== null) {
      requestBody._infra_id = scheduleData.get('_infra_id', '');
      requestBody.infra_name = scheduleData.get('infra_name', '');
      if (scheduleData.get('mode') === 'remote' && scheduleData.get('remotePlatform') !== null) {
        requestBody.remotePlatform = scheduleData.get('remotePlatform', '');
      } else {
        requestBody.remotePlatform = 'zoom';
      }
    }

    if (scheduleData.get('_topic_id', '') !== '') {
      requestBody._topic_id = scheduleData.get('_topic_id', '');
      requestBody.topic = scheduleData.get('topic', '');
    }
    const validationErrorMsg = validateManageCourseSchedule(requestBody, checkTopicIsRequired);
    if (validationErrorMsg) {
      setData(Map({ message: validationErrorMsg }));
      return;
    }
    saveManageCourseSchedule({
      mode,
      ...(mode === 'update' && { _id: modalData.get('settingId') }),
      requestBody,
      callback: this.handleCloseModal,
    });
  };

  checkStudentGroupStatus = (e, sessionId, studentGroupId) => {
    const { modalData } = this.state;
    const studentGroupIndex = modalData
      .getIn(['schedule', 'subjects', 0, 'session_flow', 0, 'student_group'], List())
      .findIndex((g) => g.get('_id') === studentGroupId);
    const sessionGroupIndex = modalData
      .getIn(
        [
          'schedule',
          'subjects',
          0,
          'session_flow',
          0,
          'student_group',
          studentGroupIndex,
          'session_group',
        ],
        List()
      )
      .findIndex((g) => g.get('_id') === sessionId);
    const schedule = modalData.getIn(['schedule', 'subjects'], List()).update((key) => {
      return key.map((value1) => {
        return value1.set(
          'session_flow',
          value1.get('session_flow', List()).map((value) => {
            return value.getIn(
              [
                'student_group',
                studentGroupIndex,
                'session_group',
                sessionGroupIndex,
                'schedule_status',
              ],
              false
            ) &&
              !value.getIn(
                [
                  'student_group',
                  studentGroupIndex,
                  'session_group',
                  sessionGroupIndex,
                  'checked_status',
                ],
                false
              )
              ? value.setIn(
                  [
                    'student_group',
                    studentGroupIndex,
                    'session_group',
                    sessionGroupIndex,
                    'schedule_status',
                  ],
                  true
                )
              : value
                  .setIn(
                    [
                      'student_group',
                      studentGroupIndex,
                      'session_group',
                      sessionGroupIndex,
                      'checked_status',
                    ],
                    e.target.checked
                  )
                  .setIn(
                    [
                      'student_group',
                      studentGroupIndex,
                      'session_group',
                      sessionGroupIndex,
                      'schedule_status',
                    ],
                    false
                  );
          })
        );
      });
    });
    const scheduleData = modalData.setIn(['schedule', 'subjects'], schedule);
    this.setState({ modalData: scheduleData });
  };

  checkScheduleStatus = (e, subjectId, flowId, studentGroupId, sessionId) => {
    const { modalData } = this.state;
    const subjectIndex = modalData
      .getIn(['schedule', 'subjects'], List())
      .findIndex((g) => g.get('_id') === subjectId);

    const sessionFlowIndex = modalData
      .getIn(['schedule', 'subjects', subjectIndex, 'session_flow'], List())
      .findIndex((g) => g.get('_id') === flowId);
    const studentGroupIndex = modalData
      .getIn(
        ['schedule', 'subjects', subjectIndex, 'session_flow', sessionFlowIndex, 'student_group'],
        List()
      )
      .findIndex((g) => g.get('_id') === studentGroupId);
    const sessionGroupIndex = modalData
      .getIn(
        [
          'schedule',
          'subjects',
          subjectIndex,
          'session_flow',
          sessionFlowIndex,
          'student_group',
          studentGroupIndex,
          'session_group',
        ],
        List()
      )
      .findIndex((g) => g.get('_id') === sessionId);

    const schedule = modalData
      .setIn(
        [
          'schedule',
          'subjects',
          subjectIndex,
          'session_flow',
          sessionFlowIndex,
          'student_group',
          studentGroupIndex,
          'session_group',
          sessionGroupIndex,
          'checked_status',
        ],
        e.target.checked
      )
      .setIn(
        [
          'schedule',
          'subjects',
          subjectIndex,
          'session_flow',
          sessionFlowIndex,
          'student_group',
          studentGroupIndex,
          'session_group',
          sessionGroupIndex,
          'schedule_status',
        ],
        false
      );
    this.setState({ modalData: schedule });
  };

  onModalClose = () => {
    this.setState({ alertConfirmModalData: { show: false } });
  };

  onConfirm = ({ data, operation, type }) => {
    this.setState({ alertConfirmModalData: { show: false } });
    switch (operation) {
      case 'delete': {
        return this.handleDeleteSchedule(data, true, type);
      }
      default:
        return;
    }
  };

  getActiveSessionFlow = () => {
    const { courseGroupSettingDetails } = this.props;
    if (courseGroupSettingDetails.has('subjects')) {
      return courseGroupSettingDetails.set(
        'student_group',
        courseGroupSettingDetails.getIn(['subjects', 0, 'session_flow', 0, 'student_group'], List())
      );
    }
    return Map();
  };

  handleDeleteSchedule = ({ setting }, isConfirmed, type) => {
    if (!isConfirmed) {
      this.setAlertModalData({
        show: true,
        title: type === 'schedule' ? 'Delete Setting' : 'Delete Topic Set',
        body: (
          <div>
            Are you sure you want to delete the selected{' '}
            {type === 'schedule' ? 'setting' : 'topic set'}?
          </div>
        ),
        variant: 'confirm',
        data: { data: { setting }, operation: 'delete', type: type },
        confirmButtonLabel: 'DELETE',
        cancelButtonLabel: 'CANCEL',
      });
      return;
    }
    const {
      institutionCalendarId,
      saveManageCourseSchedule,
      course,
      saveManageTopics,
    } = this.props;
    if (type === 'schedule') {
      saveManageCourseSchedule({
        mode: 'delete',
        _id: setting.get('_id'),
        callback: this.handleCloseModal,
        requestBody: {
          _institution_calendar_id: institutionCalendarId,
          _program_id: getURLParams('programId', true),
          _course_id: course.get('_course_id'),
          term: course.get('term'),
          level_no: course.get('level_no'),
        },
      });
    } else {
      saveManageTopics({
        mode: 'delete',
        _id: setting.get('_id'),
        callback: this.handleCloseModal,
        requestBody: {
          program_id: getURLParams('programId', true),
          course_id: course.get('_course_id'),
          level_no: course.get('level_no'),
          _institution_calendar_id: institutionCalendarId,
        },
      });
    }
  };

  setAlertModalData = ({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) => {
    this.setState({
      alertConfirmModalData: {
        show,
        ...(title && { title }),
        ...(titleIcon && { titleIcon }),
        ...(body && { body }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  };

  checkSessionGroupStatus = (e, subjectId, sessionId) => {
    const { modalData } = this.state;
    const subjectIndex = modalData
      .getIn(['schedule', 'subjects'], List())
      .findIndex((g) => g.get('_id') === subjectId);
    const sessionFlow = modalData.getIn(['schedule', 'subjects', subjectIndex, 'session_flow']);
    const sessionIndex = sessionFlow.findIndex((g) => g.get('_id') === sessionId);
    const studentGroupIds = modalData
      .getIn(['schedule', 'student_groups'], List())
      .map((item) => item.get('_id'))
      .toJS();
    const schedule = modalData
      .getIn(
        ['schedule', 'subjects', subjectIndex, 'session_flow', sessionIndex, 'student_group'],
        List()
      )
      .update((key) => {
        return key.map((value) => {
          return studentGroupIds.includes(value.get('_id'))
            ? value.set(
                'session_group',
                value.get('session_group').update((key1) => {
                  return key1.map((value1) => {
                    return value1.get('schedule_status') && !value1.get('checked_status')
                      ? value1.set('schedule_status', true)
                      : value1
                          .set('checked_status', e.target.checked)
                          .set('schedule_status', false);
                  });
                })
              )
            : value.set(
                'session_group',
                value.get('session_group').update((key1) => {
                  return key1.map((value1) => {
                    return value1.set('checked_status', false);
                  });
                })
              );
        });
      });
    const scheduleUpdate = modalData.setIn(
      ['schedule', 'subjects', subjectIndex, 'session_flow', sessionIndex, 'student_group'],
      schedule
    );
    this.setState({ modalData: scheduleUpdate });
  };

  saveTopics = (data, mode, id, callBack) => {
    const { course, saveManageTopics, institutionCalendarId } = this.props;
    data.course_id = course.get('_course_id');
    data.level_no = course.get('level_no');
    data.program_id = getURLParams('programId', true);
    data._institution_calendar_id = institutionCalendarId;
    let requestBody = data;
    if (mode === 'create') {
      delete requestBody.deleted_topics;
    }

    saveManageTopics({
      mode,
      ...(mode === 'update' && { _id: id }),
      requestBody,
      callback: callBack,
      alteredBody: {
        delivery_type_details: requestBody.delivery_type_details,
        topics: requestBody.topics,
        ...(mode === 'update' && { deleted_topics: requestBody.deleted_topics }),
      },
    });
  };

  autoAssignCourseDelivery = ({
    page = 1,
    pageSize = 10,
    deliveryId,
    isRefresh = true,
    rotationCount = '',
  }) => {
    const {
      getAutoAssignCourseDelivery,
      programId,
      institutionCalendarId,
      courseId,
      term,
      level,
    } = this.props;
    getAutoAssignCourseDelivery(
      programId,
      institutionCalendarId,
      courseId,
      term,
      level,
      deliveryId,
      page,
      pageSize,
      isRefresh,
      rotationCount
    );
  };

  saveAssignCourseDelivery = (mode, rotationCount = '') => {
    const {
      institutionCalendarId,
      programName,
      course,
      saveAssignCourseDelivery,
      autoAssignCourseDeliveryList,
      courseSchedule,
    } = this.props;

    const sessionIds = autoAssignCourseDeliveryList
      .filter((item) => item.get('checked', false) === true)
      .map((item) => item.get('_session_id'))
      .toJS();
    const requestBody = {
      _institution_calendar_id: institutionCalendarId,
      _program_id: getURLParams('programId', true),
      program_name: programName,
      _course_id: course.get('_course_id'),
      course_name: course.get('courses_name'),
      course_code: course.get('courses_number'),
      _student_group_id: courseSchedule.getIn(['session_flow', 0, '_student_group_id']),
      type: 'regular',
      term: course.get('term'),
      year_no: course.get('year_no'),
      level_no: course.get('level_no'),
      session_ids: sessionIds,
      ...(rotationCount !== '' && { rotation_count: parseInt(rotationCount) }),
    };
    saveAssignCourseDelivery({
      mode,
      requestBody,
      callback: () => {},
    });
  };

  checkCalendarEnabled = () => {
    const { individualSessionDetails } = this.props;
    let arrayValue = [false];
    if (individualSessionDetails.size > 0) {
      // eslint-disable-next-line
      individualSessionDetails.map((session) => {
        // eslint-disable-next-line
        session.get('delivery', List()).map((delivery) => {
          arrayValue.push(delivery.get('settings', List()).size > 0);
        });
      });
    }
    return arrayValue.filter((item) => item === true).length > 0;
  };

  render() {
    const {
      individualCourseDetails,
      individualSessionDetails,
      mcActiveTab,
      setData,
      course,
      advancedSettings,
      manageTopics,
      activeSettingsView,
      autoAssignCourseDeliveryList,
      loading,
      paginationCourseMetaData,
      courseSchedule,
      programId,
      isCurrentCalendar,
    } = this.props;
    const { viewCreditHours, modalData, alertConfirmModalData } = this.state;
    const isRotation = courseSchedule.get('rotation', '') === 'yes';
    const isSharedCourse = courseSchedule.get('isShared', false);

    const currentCalendar = isCurrentCalendar();
    return (
      <div className="p-2">
        <CourseExtendInfo
          course={individualCourseDetails.merge(
            Map({ rotation_dates: course.get('rotation_dates', List()) })
          )}
          modalClick={this.modalClick}
          programId={programId}
        />
        {(CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Manage Course',
          'Session Default Settings View'
        ) ||
          CheckPermission(
            'subTabs',
            'Schedule Management',
            'Course Scheduling',
            '',
            'Schedule',
            '',
            'Manage Course',
            'Manage Topic View'
          ) ||
          CheckPermission(
            'subTabs',
            'Schedule Management',
            'Course Scheduling',
            '',
            'Schedule',
            '',
            'Manage Course',
            'Advance Settings View'
          )) && (
          <div className="program_card bg-white ">
            <div className="session_card ">
              <div className="">
                <div className="row">
                  <div className="col-md-12 col-xl-9 col-lg-9 col-12 col-sm-12 ">
                    <ManageCourseTabs
                      mcActiveTab={mcActiveTab}
                      setData={setData}
                      showManageTopic={!isSharedCourse}
                    />
                  </div>
                  {mcActiveTab === 'Session Default Settings' && (
                    <div className={`col-md-12 col-xl-3 col-lg-3 col-12 col-sm-12`}>
                      <div className="d-flex justify-content-xl-end justify-content-lg-end justify-content-md-center">
                        <div className="row course_setting_tab remove_hover mr-0">
                          <div
                            className={`border-right-blue ${
                              activeSettingsView === 'list' && 'setting_active'
                            }  p-2`}
                            onClick={() => {
                              setData(Map({ activeSettingsView: 'list' }));
                            }}
                          >
                            <i className="fa fa-list-alt" aria-hidden="true"></i>
                            {activeSettingsView === 'list' && (
                              <span className="pl-1"> {t('group_setting')}</span>
                            )}
                          </div>
                          {/* disabled_course */}
                          {currentCalendar && (
                            <div
                              className={`${
                                activeSettingsView === 'calendar' && 'setting_active'
                              }  p-2 ${
                                getURLParams('studentGroupStatus', true) === 'true' &&
                                this.checkCalendarEnabled()
                                  ? 'cursor-pointer'
                                  : 'cursor-not-allowed opacity-05'
                              }`}
                              onClick={() => {
                                if (
                                  getURLParams('studentGroupStatus', true) === 'true' &&
                                  this.checkCalendarEnabled()
                                ) {
                                  setData(Map({ activeSettingsView: 'calendar' }));
                                }
                              }}
                            >
                              <i className="fa fa-calendar" aria-hidden="true"></i>
                              {activeSettingsView === 'calendar' && (
                                <span className="pl-1">CALENDAR</span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="min_h thead_border">
                  {mcActiveTab === 'Session Default Settings' && (
                    <>
                      {activeSettingsView === 'list' ? (
                        <ManageScheduleListView
                          manageTopics={manageTopics}
                          studentGroupStatus={getURLParams('studentGroupStatus', true) === 'true'}
                          isRotation={isRotation}
                          individualSessionDetails={individualSessionDetails}
                          handleAddEditSchedule={this.handleAddEditSchedule}
                          handleDeleteSchedule={this.handleDeleteSchedule}
                          currentCalendar={currentCalendar}
                          programId={programId}
                        />
                      ) : (
                        <ManageScheduleCalendarView
                          course={course}
                          manageTopics={manageTopics}
                          individualSessionDetails={individualSessionDetails}
                          handleAddEditSchedule={this.handleAddEditSchedule}
                          handleDeleteSchedule={this.handleDeleteSchedule}
                          setData={setData}
                          handleSaveDayTime={this.handleSaveDayTime}
                          currentCalendar={currentCalendar}
                        />
                      )}
                    </>
                  )}
                  {mcActiveTab === 'Manage Topic' && (
                    <ManageTopics
                      course={course}
                      manageTopics={manageTopics}
                      advancedSettings={advancedSettings}
                      saveTopics={this.saveTopics}
                      setData={setData}
                      handleDeleteSchedule={this.handleDeleteSchedule}
                      individualSessionDetails={individualSessionDetails}
                      currentCalendar={currentCalendar}
                    />
                  )}
                  {mcActiveTab === 'Advance Settings' && (
                    <AdvanceSettings
                      advancedSettings={advancedSettings}
                      saveAdvanceSetting={this.saveAdvanceSetting}
                      manageTopics={manageTopics}
                      autoAssignCourseDelivery={this.autoAssignCourseDelivery}
                      autoAssignCourseDeliveryList={autoAssignCourseDeliveryList}
                      setData={setData}
                      saveAssignCourseDelivery={this.saveAssignCourseDelivery}
                      isLoading={loading.get('GET_AUTO_ASSIGN_COURSE_DELIVERY', false)}
                      paginationCourseMetaData={paginationCourseMetaData}
                      isRotation={isRotation}
                      courseSchedule={courseSchedule}
                      currentCalendar={currentCalendar}
                      manualStaffSaveSetting={this.manualStaffSaveSetting}
                      saveMissedSession={this.saveMissedSession}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {viewCreditHours && (
          <ViewCreditHours
            course={individualCourseDetails}
            modalClick={this.modalClick}
            programId={programId}
          />
        )}

        {modalData.getIn(['deliveryData', 'delivery_id'], '') !== '' && (
          <AddEditCourseScheduleModal
            show={modalData.get('show', false)}
            onHide={this.handleCloseModal}
            mode={modalData.get('mode', 'create')}
            data={modalData}
            course={course}
            onChange={this.handleScheduleDataChange}
            onSave={this.handleSaveSchedule}
            setData={setData}
            activeSessionFlow={this.getActiveSessionFlow()}
            checkScheduleStatus={this.checkScheduleStatus}
            checkStudentGroupStatus={this.checkStudentGroupStatus}
            checkSessionGroupStatus={this.checkSessionGroupStatus}
            isRotation={isRotation}
            programId={programId}
          />
        )}
        {alertConfirmModalData.show && (
          <AlertConfirmModal
            show={alertConfirmModalData.show}
            title={alertConfirmModalData.title || ''}
            titleIcon={alertConfirmModalData.titleIcon}
            body={alertConfirmModalData.body || ''}
            variant={alertConfirmModalData.variant || 'confirm'}
            confirmButtonLabel={alertConfirmModalData.confirmButtonLabel || 'YES'}
            cancelButtonLabel={alertConfirmModalData.cancelButtonLabel || 'NO'}
            onClose={this.onModalClose}
            onConfirm={this.onConfirm}
            data={alertConfirmModalData.data}
          />
        )}
      </div>
    );
  }
}

ManageCourse.propTypes = {
  loading: PropTypes.instanceOf(Map),
  institutionCalendarId: PropTypes.string,
  programId: PropTypes.string,
  programName: PropTypes.string,
  courseId: PropTypes.string,
  term: PropTypes.string,
  level: PropTypes.string,
  userId: PropTypes.string,
  course: PropTypes.instanceOf(Map),
  getIndividualCourse: PropTypes.func,
  setData: PropTypes.func,
  saveManageCourseSchedule: PropTypes.func,
  saveManageTopics: PropTypes.func,
  saveAssignCourseDelivery: PropTypes.func,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  individualCourseDetails: PropTypes.instanceOf(Map),
  individualSessionDetails: PropTypes.instanceOf(List),
  mcActiveTab: PropTypes.string,
  courseGroupSettingDetails: PropTypes.instanceOf(Map),
  advancedSettings: PropTypes.instanceOf(Map),
  manageTopics: PropTypes.instanceOf(List),
  activeSettingsView: PropTypes.string,
  courseSchedule: PropTypes.instanceOf(Map),
  autoAssignCourseDeliveryList: PropTypes.instanceOf(List),
  manualStaffOptions: PropTypes.instanceOf(List),
  paginationCourseMetaData: PropTypes.instanceOf(Map),
  getAutoAssignCourseDelivery: PropTypes.func,
  saveManageCourseDayTimeSet: PropTypes.func,
  getAdvancedSettings: PropTypes.func,
  getManageTopics: PropTypes.func,
  saveAdvanceSetting: PropTypes.func,
  getManageCourseSettings: PropTypes.func,
  isCurrentCalendar: PropTypes.func,
  setStaffList: PropTypes.func,
  getScheduleStaffOptionList: PropTypes.func,
  saveMissedSession: PropTypes.func,
  countryCodeLength: PropTypes.bool,
};

const mapStateToProps = (state) => {
  return {
    userId: selectUserId(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    individualCourseDetails: selectIndividualCourseDetails(state),
    individualSessionDetails: selectIndividualSessionDetails(state),
    mcActiveTab: selectMcActiveTab(state),
    courseGroupSettingDetails: selectCourseGroupSettingDetails(state),
    advancedSettings: selectAdvancedSettings(state),
    manageTopics: selectManageTopics(state),
    activeSettingsView: selectActiveSettingsView(state),
    autoAssignCourseDeliveryList: selectAutoAssignCourseDeliveryList(state),
    courseSchedule: selectCourseSchedule(state),
    loading: selectLoading(state),
    paginationCourseMetaData: selectPaginationCourseMetaData(state),
    manualStaffOptions: selectStaffScheduleOptionList(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, actions),
  withCountryCodeHooks,
  withCalendarHooks
)(ManageCourse);
