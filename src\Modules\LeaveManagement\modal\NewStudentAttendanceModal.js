import React, { useState, useEffect, Fragment } from 'react';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import { t } from 'i18next';
import EditRoundedIcon from '@mui/icons-material/EditRounded';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined';
import ReportProblemSharpIcon from '@mui/icons-material/ReportProblemSharp';
import MaterialInput from 'Widgets/FormElements/material/Input';
//import { CheckPermission } from 'Modules/Shared/Permissions';
import ErrorOutlinedIcon from '@mui/icons-material/ErrorOutlined';
import ArrowDropDownOutlinedIcon from '@mui/icons-material/ArrowDropDownOutlined';
import { formatFullName, jsUcfirstAll, isModuleEnabled, isLateAbsentEnabled } from 'utils';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import moment from 'moment';
import {
  getStudentBlackBoxContent,
  getLateExcludeStatus,
  updateLateExclude,
} from '_reduxapi/leave_management/actions';
import { getStudentBonusAttendance } from '_reduxapi/bonus_attendance/actions';
import {
  selectStudentBlackBoxContent,
  selectIsSaving,
  selectGetLateExcludeStatus,
} from '_reduxapi/leave_management/selectors';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import PopoverBlackBox from './PopoverBlackBox';
import { useDispatch, useSelector } from 'react-redux';
import { useStylesFunction } from '../../Assessment/css/designUtils';
import {
  IconButton,
  Avatar,
  Badge,
  Tooltip,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  ListItemText,
  Chip,
  Box,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import {
  MenuForThead,
  PopoverStaff,
  exportPdf,
  gettingSessionLabel,
  getSessionTypes,
  SubtractTwoTimeConvertByMinutes,
  staffFullName,
  ReasonByToolTip,
  PaginationWithCount,
  sizeForModal,
  getZoomDuration,
  statusBasedObject,
} from './NewStudentAttendanceModalUtils';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { lmsTypeNames } from '../utils';
import StudentLateAbsentHead from '../components/LateConfigTooltip/StudentLateAbsentHead';
import { makeStyles } from '@mui/styles';
import ErrorIcon from '@mui/icons-material/Error';
import { getInfraName } from 'Modules/InstitutionReport/utils';
import BonusAttendanceDrawer from './BonusAttendanceDrawer';
import { selectBonusAttendanceData } from '_reduxapi/bonus_attendance/selectors';

const useStyles = makeStyles((theme) => ({
  customTooltip: {
    padding: 0,
    '& .MuiTooltip-arrow': {
      color: '#1F2937',
    },
  },
}));

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

function NewStudentAttendanceModal({
  attendanceModal,
  setAttendanceModal,
  getStudentAttendanceSheetDetails,
  metaData,
  studentAttnSheetDetails,
  institutionCalendarId,
  updateReason,
  setData,
  //callBack,
  setCriteriaModal,
  activeProgram,
  isTermCheck,
  typeWise,
  setTypeWise,
}) {
  const [anchorEl, setAnchorEl] = useState(null);
  const [popStaff, setPopStaff] = useState(null);
  const [pageCount, setPageCount] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [typePopOver, setTypePopOver] = useState(null);
  const [isEdit, setIsEdit] = useState(null);
  const open = Boolean(anchorEl);
  const dispatch = useDispatch();
  const isSaving = useSelector(selectIsSaving);
  const studentBlackBoxContent = useSelector(selectStudentBlackBoxContent);
  const authData = useSelector(selectUserInfo);
  const [popNew, setPopNew] = useState(null);
  const [search, setSearch] = useState('');
  const [editIndex, setEditIndex] = useState(-1);
  const [editIndexPop, setEditIndexPop] = useState(-1);
  const [studentSessions, setStudentSessions] = useState(List());
  const [studentSessionsForExport, setStudentSessionsForExport] = useState(List());
  const [filterSelectedArray, setFilterSelectedArray] = useState(
    Map({
      sessionEnds: List(),
      status: List(),
    })
  );
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [anchorElPopover, setAnchorElPopover] = useState(null);
  const [openPopup, setOpenPopup] = useState(false);
  const [value, setValue] = useState('default');
  const [personName, setPersonName] = useState(List());
  const LateExcludeStatus = useSelector(selectGetLateExcludeStatus);
  const isLateEnabled = isLateAbsentEnabled();
  const [openBonusPopup, setOpenBonusPopup] = useState(false);
  const bonusAttendanceList = useSelector(selectBonusAttendanceData) || [];
  const attendanceModalStatus = attendanceModal?.get('status', false);
  const params = {
    institutionCalendarId: institutionCalendarId,
    programId: metaData.get('program', ''),
    courseId: metaData.get('courseId', ''),
    levelNo: metaData.get('level', ''),
    term: metaData.get('term', ''),
    gender: metaData.get('gender', ''),
    rotationCount: metaData.get('rotationCount', null),
    studentId: attendanceModal.getIn(['data', '_student_id'], ''),
    typeWiseUpdate: 'student_wise',
  };

  useEffect(() => {
    if (openPopup) {
      const status = studentAttnSheetDetails.get('session_wise', false)
        ? 'session'
        : studentAttnSheetDetails.get('course_wise', false)
        ? 'course'
        : 'default';

      setValue(status);
      const getSession = studentAttnSheetDetails
        .get('studentSessions', List())
        .filter((s) => s.get('lateExcludeForStudentSession', false) === true)
        .map((item) => {
          return item
            .set('name', item.get('session', '') + ' - ' + getInfraName(item))
            .set('value', item.get('_id'));
        });

      if (getSession.size > 0) setPersonName(getSession);
    }
  }, [openPopup]); //eslint-disable-line

  useEffect(() => {
    if (openPopup === true) {
      dispatch(getLateExcludeStatus({ params }));
    }
  }, []); //eslint-disable-line

  useEffect(() => {
    if (attendanceModalStatus) {
      const studentId = attendanceModal.getIn(['data', '_student_id'], '');
      if (studentId) {
        dispatch(
          getStudentBonusAttendance(
            studentId,
            metaData.get('program', ''),
            metaData.get('courseId', ''),
            metaData.get('year', ''),
            metaData.get('level', ''),
            metaData.get('term', ''),
            metaData.get('rotationCount', '')
          )
        );
      }
    }
  }, [attendanceModalStatus, dispatch, metaData]);

  const handleClickOpenPopup = () => {
    setOpenPopup(true);
    // dispatch(getLateExcludeStatus({ params }));
  };

  const handleClosePopup = () => {
    setOpenPopup(false);
  };

  const handleChangeRadio = (event) => {
    setValue(event.target.value);
  };

  const handleClose = () => {
    setAnchorElPopover(null);
    setPopoverOpen(false);
  };
  const isTardisEnabled = authData.getIn(['services', 'TARDIS_MODULE'], '') === 'true';
  const classes = useStylesFunction();

  const saveData = (session) => {
    const reason = gettingSessionLabel(session, 'reason');
    if (reason === '') {
      dispatch(setData(Map({ message: 'Reason field is required' })));
    } else {
      //const staff = getStaffName(session.getIn(['staffs', 0, '_staff_id'], ''), true);
      const data = {
        status: session.get('attendance_status', ''),
        reason: reason,
        updated_by: {
          _staff_id: authData.get('_id', ''),
          staff_name: {
            first: authData.getIn(['name', 'first'], ''),
            middle: authData.getIn(['name', 'middle'], ''),
            last: authData.getIn(['name', 'last'], ''),
            family: authData.getIn(['name', 'family'], ''),
          },
        },
      };

      updateReason(
        data,
        session.get('_id'),
        attendanceModal?.getIn(['data', '_student_id'], ''),
        () => {
          getStudentAttendanceSheet(false);
          // callBack();
          setEditIndex(-1);
          setIsEdit(false);
        }
      );
    }
  };
  useEffect(() => {
    getStudentAttendanceSheet();
    // eslint-disable-next-line
  }, []);

  const openPopStaff = Boolean(popStaff);

  const getStaffName = (staffId, needObject = false) => {
    const staffArray = studentAttnSheetDetails
      .get('staffNames', List())
      .find((eachStaff) => eachStaff.get('_id', '') === staffId);
    if (needObject) return staffArray;
    return staffArray && staffFullName(staffArray?.get('staffName', Map()));
  };
  const cancelSession = (index, type, value) => {
    const indexForResponse = (currentPage - 1) * pageCount + index;
    const check = studentAttnSheetDetails.getIn(
      ['studentSessions', indexForResponse, 'students', 'reasonIds'],
      List()
    ).size
      ? value.setIn(
          [
            index,
            'students',
            'reasonIds',
            value.getIn(['students', 'reasonIds'], List()).size - 1,
            '_id',
            'reason',
          ],
          !type
            ? studentAttnSheetDetails.getIn(
                [
                  'studentSessions',
                  indexForResponse,
                  'students',
                  'reasonIds',
                  studentAttnSheetDetails.getIn(
                    ['studentSessions', indexForResponse, 'students', 'reasonIds'],
                    List()
                  ).size - 1,
                  '_id',
                  'reason',
                ],
                ''
              )
            : ''
        )
      : value.deleteIn([index, 'students', 'reasonIds']);
    setStudentSessions(check);
  };
  const onChangeEdit = (value, index, type) => {
    const indexForResponse = (currentPage - 1) * pageCount + index;
    if (type === 'attendance') {
      const co = studentSessions.setIn([index, 'attendance_status'], value);
      setStudentSessions(co);
    } else if (type === 'cancel') {
      setEditIndex(-1);
      setIsEdit(false);
      const value = studentSessions.setIn(
        [index, 'attendance_status'],
        studentAttnSheetDetails.getIn(
          ['studentSessions', indexForResponse, 'attendance_status'],
          ''
        )
      );
      cancelSession(index, false, value);
    } else {
      const coat = studentAttnSheetDetails.getIn(
        ['studentSessions', indexForResponse, 'students', 'reasonIds'],
        List()
      ).size
        ? studentSessions.setIn(
            [
              index,
              'students',
              'reasonIds',
              studentSessions.getIn(['students', 'reasonIds'], List()).size - 1,
              '_id',
              'reason',
            ],
            value
          )
        : studentSessions.setIn([index, 'students', 'reasonIds', 0, '_id', 'reason'], value);
      setStudentSessions(coat);
    }
  };

  const openPop = Boolean(popNew);
  const id = openPop ? 'simple-popover' : undefined;
  const clearFilter = (index, type) => {
    const filter = filterSelectedArray.get(type, List()).filter((_, index2) => index2 !== index);
    setFilterSelectedArray(filterSelectedArray.set(type, filter));
  };

  function getStudentAttendanceSheet(isLoading = true) {
    // const callBack = () => {
    //   if (item) setAttendanceModal(Map({ status: true, data: item }));
    //   // return setTimeout(() => setAttendanceModal(Map({ status: true, data: item })), 1000);
    // };
    const fetchTermFromLevel = () =>
      activeProgram
        .get('level', List())
        .filter(
          (item) =>
            item.get('year', '') === metaData.get('year', '') &&
            item.get('level_no', '') === metaData.get('level', '') &&
            item.get('term', '').toLowerCase() === metaData.get('term', '').toLowerCase()
        );

    getStudentAttendanceSheetDetails({
      isLoading: isLoading,
      institutionCalendarId: institutionCalendarId,
      programId: metaData.get('program', ''),
      courseId: metaData.get('courseId', ''),
      yearNo: metaData.get('year', ''),
      levelNo: metaData.get('level', ''),
      term:
        isTermCheck === 'termKey'
          ? metaData.get('term', '')
          : fetchTermFromLevel().getIn([0, 'term'], metaData.get('term', '')),
      // term: metaData.get('term', ''),
      type: 'regular',
      userId: attendanceModal.getIn(['data', '_student_id'], ''),
      rotationCount: metaData.get('rotationCount', ''),
      //callBack: callBack,
    });
  }

  const handleClickOpenPopupBonus = () => {
    setOpenBonusPopup(true);
  };

  const handleClosePopupBonus = () => {
    setOpenBonusPopup(false);
  };

  const handleSubmitPopup = () => {
    const sessionIds = personName.map((item) => item.get('sessionId', ''));

    const typeWiseUpdate = value === 'session' ? 'session_wise' : 'student_wise';
    setTypeWise(typeWiseUpdate);

    var requestBody = {
      institutionCalendarId: institutionCalendarId,
      programId: metaData.get('program', ''),
      courseId: metaData.get('courseId', ''),
      levelNo: metaData.get('level', ''),
      term: metaData.get('term', ''),
      gender: metaData.get('gender', ''),
      rotationCount: metaData.get('rotationCount', null),
      typeWiseUpdate: typeWiseUpdate,
      excludeStatus: value === 'default' ? false : true,
      studentId: attendanceModal.getIn(['data', '_student_id'], ''),
      sessionIds: value === 'session' ? sessionIds : [],
    };

    const callBack = () => {
      const fetchTermFromLevel = () =>
        activeProgram
          .get('level', List())
          .filter(
            (item) =>
              item.get('year', '') === metaData.get('year', '') &&
              item.get('level_no', '') === metaData.get('level', '') &&
              item.get('term', '').toLowerCase() === metaData.get('term', '').toLowerCase()
          );

      dispatch(getLateExcludeStatus({ params }));

      getStudentAttendanceSheetDetails({
        isLoading: true,
        institutionCalendarId: institutionCalendarId,
        programId: metaData.get('program', ''),
        courseId: metaData.get('courseId', ''),
        yearNo: metaData.get('year', ''),
        levelNo: metaData.get('level', ''),
        term:
          isTermCheck === 'termKey'
            ? metaData.get('term', '')
            : fetchTermFromLevel().getIn([0, 'term'], metaData.get('term', '')),
        type: 'regular',
        userId: attendanceModal.getIn(['data', '_student_id'], ''),
        rotationCount: metaData.get('rotationCount', ''),
      });
    };
    dispatch(updateLateExclude(requestBody, callBack));
    setOpenPopup(false);
  };

  const handleChange = (value) => {
    const selected = studentAttnSheetDetails
      .get('studentSessions', List())
      .filter((group) => value.includes(group.get('_id')));
    setPersonName(selected);
  };

  const calculateTotalBonusPercentage = () => {
    if (!bonusAttendanceList || bonusAttendanceList.length === 0) {
      return 0;
    }
    const totalBonus = bonusAttendanceList.reduce((sum, item) => {
      return sum + (item.percentage || 0);
    }, 0);
    return Math.round(totalBonus);
  };

  const classesTool = useStyles();
  const GetStudentLateAbsent = ({ studentAttnSheetDetails }) => {
    const lateAbsentValue = studentAttnSheetDetails.get('studentLateAbsent', '');

    const padLeadingZero = (number) => {
      return number < 10 ? `0${number}` : `${number}`;
    };
    const formattedLateAbsent = `Late Absent ${padLeadingZero(lateAbsentValue)}`;
    const lateConfig = studentAttnSheetDetails.get('lateConfig', List());
    return (
      <div className="badge-blue border-left f-14 mb-1 pr-2 pl-2 d-flex align-items-center">
        <div className="pr-1">{formattedLateAbsent}</div>
        <Tooltip
          placement={'top-end'}
          arrow
          classes={{ tooltip: classesTool.customTooltip }}
          title={
            <div className="table-LateAbsent borderClass">
              <table className="my-tableLateAbsent">
                <thead>
                  <tr>
                    <th>Late Label</th>
                    <th>No of Late</th>
                    <th>Absence</th>
                  </tr>
                </thead>
                <tbody>
                  {lateConfig.map((value, indexKey) => (
                    <tr key={indexKey}>
                      <td>
                        <div>
                          <div>{value.get('labelName', '')}</div>
                          <div className="text-warning">{`Setting*(${value.get(
                            'noOfLate',
                            ''
                          )} Late = ${value.get('noOfAbsent', '')} Absent)`}</div>
                        </div>
                      </td>
                      <td>{value.get('studentLate', '')}</td>
                      <td>{value.get('studentAbsent', '')}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          }
        >
          <InfoIcon fontSize="small" color="action" className="cursor-pointer" />
        </Tooltip>
      </div>
    );
  };

  const AvatarLateLabel = {
    borderRadius: '0px',
    color: '#6B7280',
    backgroundColor: 'transparent',
    fontSize: '12px',
    width: '85px',
    height: 'auto',
  };

  const studentSessionsAr = studentAttnSheetDetails
    .get('studentSessions', List())
    .map((item) =>
      item
        .set('name', item.get('session', '') + ' - ' + getInfraName(item))
        .set('value', item.get('_id'))
    );
  const personNameAr = personName.map((item) => item.get('_id', '')).toJS();
  return (
    <>
      <DialogModal
        show={attendanceModal?.get('status', false)}
        onClose={() => setAttendanceModal(Map({ status: false, data: Map() }))}
        maxWidth={'xl'}
        sx={sizeForModal}
        fullWidth={true}
      >
        <div className="w-100">
          <div className="mb-2">
            <div className="Header_Sticky_StudentPopup px-3 pt-3">
              <div className="d-flex justify-content-between align-items-top">
                <div>
                  <p className="mb-0 bold">{t('leaveManagement.studentAttendanceSheet')}</p>
                  <span className="f-15">
                    {t('user_management.academic')} #
                    {attendanceModal?.getIn(['data', 'academic_no'], '')} (
                    {formatFullName(attendanceModal?.getIn(['data', 'name'], Map()).toJS())}) •{' '}
                    {studentAttnSheetDetails.get('denialLabel', 'Denial')} {` - Absence Percentage`}{' '}
                    : {Math.round(studentAttnSheetDetails.get('absencePercentage', 0))}% •{' '}
                    {`Bonus Attendance`} : {calculateTotalBonusPercentage()}%
                  </span>
                </div>
                <div className="ml-auto">
                  <div>
                    {/* {CheckPermission(
                  'tabs',
                  'Leave Management',
                  'Student Register',
                  '',
                  'Criteria Manipulation',
                  'View'
                ) && (
                  <MButton
                    variant="outlined"
                    color="primary"
                    className="mr-3"
                    clicked={setCriteriaModal}
                  >
                    {t('leaveManagement.criteriaManipulation')}
                  </MButton>
                )} */}
                    <MButton
                      variant="contained"
                      clicked={() =>
                        exportPdf(
                          studentAttnSheetDetails,
                          metaData,
                          getStaffName,
                          attendanceModal,
                          gettingSessionLabel,
                          studentSessionsForExport,
                          isTardisEnabled
                        )
                      }
                      className="remove_hover"
                      color="primary"
                      startIcon={<FileUploadOutlinedIcon className="mt--2" />}
                    >
                      EXPORT
                    </MButton>
                  </div>
                </div>
              </div>
              <div className="row align-items-center">
                <div className="pl-3">
                  <p className="f-15 mb-0">
                    {' '}
                    {t('program_calendar.course')} : {metaData.get('courseNumber', '')} -{' '}
                    {metaData.get('courseName', '')}
                    {metaData.get('versionName', '')}{' '}
                  </p>
                </div>
                <div className="ml-auto">
                  <MButton
                    disabled={LateExcludeStatus && (typeWise === 'course_wise' || typeWise === '')}
                    color="primary"
                    variant="contained"
                    className="mr-3"
                    clicked={handleClickOpenPopupBonus}
                  >
                    Add Bonus Attendance
                  </MButton>
                  {isLateEnabled ? (
                    <MButton
                      disabled={
                        LateExcludeStatus && (typeWise === 'course_wise' || typeWise === '')
                      }
                      color="primary"
                      variant="contained"
                      className="mr-3"
                      clicked={handleClickOpenPopup}
                    >
                      Exclude Late Attendance
                    </MButton>
                  ) : (
                    ''
                  )}
                </div>
              </div>
              <div className="row">
                <div className="col-12 d-flex flex-wrap">
                  <div className="badge-blue f-14 mb-1 bold ">
                    {t('leaveManagement.totalAttendedSessions')} :{' '}
                    {studentAttnSheetDetails.getIn(['student_attendance_details', 'present'], '')}{' '}
                    {t('leaveManagement.outOf')}{' '}
                    {studentAttnSheetDetails.getIn(
                      ['student_attendance_details', 'total_session_completed'],
                      ''
                    )}{' '}
                    (
                    {studentAttnSheetDetails.getIn(
                      ['student_attendance_details', 'percentage'],
                      0
                    ) !== null
                      ? studentAttnSheetDetails
                          .getIn(['student_attendance_details', 'percentage'], 0)
                          .toFixed(2)
                      : 0}
                    %){' '}
                  </div>
                  {getSessionTypes(
                    studentAttnSheetDetails,
                    setPopoverOpen,
                    popoverOpen,
                    anchorElPopover,
                    handleClose,
                    setAnchorElPopover
                  )}
                  {studentAttnSheetDetails.get('studentLateAbsent', '') > 0 ? (
                    <GetStudentLateAbsent studentAttnSheetDetails={studentAttnSheetDetails} />
                  ) : (
                    ''
                  )}
                  {studentAttnSheetDetails.get('warning', '') !== '' && (
                    <div className="mx-1 my-1">
                      <div className="d-flex align-items-center bg-warning-red text-red px-2 py-2">
                        <ReportProblemSharpIcon className="f-15" />
                        <span className="pl-2 f-15">
                          {' '}
                          {studentAttnSheetDetails.get('warning', '')}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="d-flex justify-content-end mt-1">
                <div className="col-md-5 col-xl-5 col-5 pr-0 remove_hover remove_hover">
                  <MaterialInput
                    elementType={'materialSearch'}
                    changed={(e) => setSearch(e.target.value)}
                    value={search}
                    placeholder={'Search for Session / Staffs name'}
                    labelclass={'searchLeft'}
                  />
                </div>
              </div>
            </div>
            {filterSelectedArray.get('sessionEnds', List()).size ||
            filterSelectedArray.get('status', List()).size ? (
              <div className="d-flex justify-content-between align-items-center pt-1">
                <div className={'d-flex'}>
                  {filterSelectedArray.get('sessionEnds', List()).size
                    ? filterSelectedArray.get('sessionEnds', List()).map((item, index) => (
                        <div className="lmsChip d-flex align-items-center" key={index}>
                          {item === 'M' ? 'Manually' : 'Automatically'}
                          <CloseRoundedIcon
                            fontSize="small"
                            className="remove_hover pl-1"
                            onClick={() => clearFilter(index, 'sessionEnds')}
                          />
                        </div>
                      ))
                    : ''}
                  {filterSelectedArray.get('status', List()).size
                    ? filterSelectedArray.get('status', List()).map((item, index) => (
                        <div className="lmsChip d-flex align-items-center" key={index}>
                          {statusBasedObject[item]}
                          <CloseRoundedIcon
                            className="remove_hover"
                            fontSize="small"
                            onClick={() => clearFilter(index, 'status')}
                          />
                        </div>
                      ))
                    : ''}
                </div>
                <p
                  className="mt-2 mb-2 text-skyblue bold remove_hover"
                  onClick={() => setFilterSelectedArray(Map())}
                >
                  {' '}
                  Clear Filter
                </p>
              </div>
            ) : (
              ''
            )}
            <div className="lms_table px-3 ">
              <table align="left">
                <thead>
                  <tr>
                    <th scope="col" className="">
                      <div className="custom_space cw_150">
                        <p className="thHeader text-left">Session</p>
                      </div>
                    </th>
                    <th scope="col" className="">
                      <div className="cw_150 custom_space">
                        <div className="d-flex justify-content-between align-items-center thHeader">
                          <p className="text-left mb-0 pr-2">Session ends </p>
                          {filterSelectedArray.get('sessionEnds', List()).size ? (
                            <Badge
                              badgeContent={filterSelectedArray.get('sessionEnds', List()).size}
                              sx={{
                                '& .MuiBadge-badge': {
                                  color: 'white',
                                  backgroundColor: '#aba3a3',
                                },
                              }}
                              className="mr-2 ml-1"
                            />
                          ) : (
                            ''
                          )}
                          <IconButton
                            aria-label="more"
                            id="long-button"
                            aria-controls={open ? 'long-menu' : undefined}
                            aria-expanded={open ? 'true' : undefined}
                            aria-haspopup="true"
                            onClick={(event) => {
                              setAnchorEl(event.currentTarget);
                              setTypePopOver('sessionEnds');
                            }}
                            className="p-0 remove_hover"
                          >
                            <ArrowDropDownOutlinedIcon />
                          </IconButton>
                        </div>
                      </div>
                    </th>

                    <th scope="col" className="">
                      <div className="cw_215 custom_space">
                        <p className="thHeader text-left">Staff(s)</p>
                      </div>
                    </th>
                    <th scope="col" className="">
                      <div className="cw_475 custom_space staff-model-tablehead border-radious-4">
                        <div className="row">
                          <div className="col-4">
                            <div className="d-flex justify-content-between align-items-center thHeader">
                              <p className="text-left mb-0 pr-2">Status </p>
                              {filterSelectedArray.get('status', List()).size ? (
                                <Badge
                                  badgeContent={filterSelectedArray.get('status', List()).size}
                                  sx={{
                                    '& .MuiBadge-badge': {
                                      color: 'white',
                                      backgroundColor: '#aba3a3',
                                    },
                                  }}
                                  className="mr-2"
                                />
                              ) : (
                                ''
                              )}
                              <IconButton
                                aria-label="more"
                                id="long-button"
                                aria-controls={open ? 'long-menu' : undefined}
                                aria-expanded={open ? 'true' : undefined}
                                aria-haspopup="true"
                                onClick={(event) => {
                                  setAnchorEl(event.currentTarget);
                                  setTypePopOver('status');
                                }}
                                className="p-0 remove_hover"
                              >
                                <ArrowDropDownOutlinedIcon />
                              </IconButton>
                            </div>
                          </div>
                          <div className="col-8">
                            <p className="thHeader text-left">Reason for manual change</p>
                          </div>
                        </div>
                      </div>
                    </th>
                    {isModuleEnabled('LATE_ABSENT_CONFIGURATION') && (
                      <th className="text_no_wrap">
                        Late Label
                        <StudentLateAbsentHead />
                      </th>
                    )}

                    <th scope="col" className="">
                      <div className="custom_space d-flex align-items-center cw_140">
                        <p className="thHeader text-left">Present %</p>
                        <Tooltips title="Present percentage per session">
                          <ErrorOutlinedIcon className="newText-gray ml-2 remove_hover" />
                        </Tooltips>
                      </div>
                    </th>
                  </tr>
                </thead>
                {studentSessions.size ? (
                  <tbody>
                    {studentSessions.map((session, index) => {
                      const isRestrictedValue =
                        session.getIn(['students', 'isRestricted'], false) &&
                        session.get('attendance_status', '') === 'absent'
                          ? !studentAttnSheetDetails.get('adminRestrictedAttendance', '')
                          : false;
                      return (
                        <Fragment key={session.get('_id', index)}>
                          <tr className={`${editIndex === index && `editBg`}`} key={index}>
                            <td className="">
                              <div className="cw_150 custom_space">
                                {editIndex === index && (
                                  <p className="mb-1 f-14 digi-gray"> Session</p>
                                )}
                                <p className="mb-1 f-15 bold">
                                  {' '}
                                  {session.get('session', '')} -{' '}
                                  {jsUcfirstAll(getInfraName(session))}
                                </p>
                                <p className="mb-0 f-15">
                                  {' '}
                                  {moment(session.get('scheduleStartDateAndTime', '')).format(
                                    'hh:mm A'
                                  )}{' '}
                                  -{' '}
                                  {moment(session.get('scheduleEndDateAndTime', '')).format(
                                    'hh:mm A'
                                  )}{' '}
                                </p>
                                <p className="mb-0 f-15">
                                  {moment(session.get('schedule_date', '')).format('DD/MM/YYYY')}
                                </p>
                                {session.get('isMissedToComplete', false) && (
                                  <p className="mb-0 f-15 text-red">Missed to Completed</p>
                                )}
                              </div>
                            </td>
                            <td className="">
                              <div className="cw_150 custom_space">
                                {editIndex === index && (
                                  <p className="mb-1 f-14 digi-gray"> Session ends</p>
                                )}
                                <p
                                  className={`mb-1 f-15 bold ${
                                    session
                                      .getIn(['sessionDetail', 'mode'], 'manual')
                                      .toLowerCase() === 'm' && 'text-red'
                                  }`}
                                >
                                  {' '}
                                  {session
                                    .getIn(['sessionDetail', 'mode'], 'manual')
                                    .toLowerCase() === 'm'
                                    ? 'Manually'
                                    : 'Automatically'}
                                </p>
                                <p className="mb-0 f-15">
                                  {' '}
                                  At{' '}
                                  {moment(session.getIn(['sessionDetail', 'stop_time'], '')).format(
                                    'h:mm A DD/MM/YYYY'
                                  )}
                                </p>
                                {session.get('mode', '').toLowerCase() !== 'onsite' ? (
                                  <p className="mb-0 f-15">
                                    {' '}
                                    {getZoomDuration(session.getIn(['staffs', 0, 'duration'], ''))}
                                  </p>
                                ) : (
                                  <p className="mb-0 f-15">
                                    {' '}
                                    {SubtractTwoTimeConvertByMinutes(
                                      session.getIn(['sessionDetail', 'start_time'], ''),
                                      session.getIn(['sessionDetail', 'stop_time'], '')
                                    )}
                                  </p>
                                )}
                              </div>
                            </td>

                            <td className="">
                              <div className="cw_215 custom_space">
                                {editIndex === index && (
                                  <p className="mb-1 f-14 digi-gray"> Staff(s)</p>
                                )}

                                <p className="mb-1 f-15 bold word_break wd_250">
                                  {getShortString(
                                    getStaffName(session.getIn(['staffs', 0, '_staff_id'], '')),
                                    25
                                  )}
                                </p>
                                <p className="mb-0 f-15">
                                  {' '}
                                  {getStaffName(
                                    session.getIn(['staffs', 0, '_staff_id'], ''),
                                    true
                                  )?.get('academicId', '')}
                                </p>
                                {session.get('staffs', List()).size > 1 && (
                                  <span
                                    className="mb-0 f-15 staffCard"
                                    id={index}
                                    key={index}
                                    onClick={(e) => {
                                      setPopStaff(popStaff ? null : e.currentTarget);
                                      setEditIndexPop(index);
                                    }}
                                  >
                                    {' '}
                                    +{session.get('staffs', List()).size - 1} more staffs
                                    {/* {openPopStaff && ( */}
                                    {editIndexPop === index && (
                                      <PopoverStaff
                                        popStaff={popStaff}
                                        setPopStaff={() => {
                                          setPopStaff(null);
                                          setEditIndexPop(-1);
                                        }}
                                        staffArray={session.get('staffs', List())}
                                        openPopStaff={openPopStaff}
                                        getStaffName={getStaffName}
                                      />
                                    )}
                                    {/* )} */}
                                  </span>
                                )}
                              </div>
                            </td>

                            <td className="">
                              <div className="cw_475 custom_space staff-model-tablehead border-radious-4">
                                <div className="row">
                                  <div className="col-4">
                                    <div className="d-flex justify-content-between align-items-end">
                                      {/* <Tooltips
                                                  title="Inactivated, While this session was happened
                                    Status will be reflected, based on Advance settings configuration"
                                                > */}
                                      <div className="pr-2">
                                        {!['OD', 'PER', 'L'].includes(
                                          lmsTypeNames(session.get('attendance_status', ''))
                                        ) &&
                                          !session.getIn(['students', 'isRestricted'], false) && (
                                            <ErrorOutlinedIcon
                                              aria-describedby={id}
                                              className="newText-gray remove_hover"
                                              onClick={(e) => {
                                                dispatch(
                                                  getStudentBlackBoxContent(
                                                    `/lms_review/attendance_history/${session.get(
                                                      '_id',
                                                      ''
                                                    )}/${session.getIn(['students', '_id'], '')}`,
                                                    () => {
                                                      setPopNew(e);
                                                      //setTimeout(() => setPopNew(e), 300);
                                                    }
                                                  )
                                                );
                                              }}
                                            />
                                          )}
                                      </div>
                                      {/* </Tooltips> */}
                                      {Boolean(popNew) && (
                                        <PopoverBlackBox
                                          handleClosePop={() => setPopNew(null)}
                                          studentBlackBoxContent={studentBlackBoxContent}
                                          popNew={popNew}
                                          id={id}
                                          staffFullName={staffFullName}
                                        />
                                      )}
                                      {editIndex === index ? (
                                        <div className="" key={index}>
                                          <p className="mb-1 f-14 digi-gray"> Status</p>
                                          <FormControl>
                                            <Select
                                              value={session.get('attendance_status', '')}
                                              onChange={(e) =>
                                                onChangeEdit(e.target.value, index, 'attendance')
                                              }
                                              className={classes.newTextFile}
                                            >
                                              <MenuItem value={`absent`}>
                                                <p className="text-red mb-0"> Absent </p>
                                              </MenuItem>
                                              <MenuItem value={`present`}>
                                                <p className="text-green mb-0"> Present </p>
                                              </MenuItem>
                                              <MenuItem value={`exclude`}>
                                                <p className="mb-0"> Exclude </p>
                                              </MenuItem>
                                            </Select>
                                          </FormControl>
                                        </div>
                                      ) : (
                                        <div
                                          className={`d-flex ${
                                            session.getIn(['students', 'isRestricted'], false)
                                              ? 'flex-column align-items-end'
                                              : ''
                                          }`}
                                        >
                                          <Avatar
                                            alt="status"
                                            className={`${
                                              session.getIn(['students', 'reasonIds'], List()).size
                                                ? 'alert-pending-bg'
                                                : 'staff-model-tablehead'
                                            } ${
                                              ['absent', 'leave'].includes(
                                                session.get('attendance_status', '')
                                              )
                                                ? 'text-red'
                                                : 'text-green'
                                            } f-15 bold ${
                                              session.getIn(['students', 'isRestricted'], false)
                                                ? 'ml-auto'
                                                : 'm-auto'
                                            }`}
                                          >
                                            {lmsTypeNames(session.get('attendance_status', ''))}
                                          </Avatar>
                                          {session.getIn(['students', 'isRestricted'], false) &&
                                          session.get('attendance_status', '') === 'absent' ? (
                                            <Avatar sx={AvatarLateLabel}>
                                              <div
                                                className="text-red f-12 ml-auto"
                                                style={{ textWrap: 'nowrap' }}
                                              >
                                                Restricted
                                              </div>
                                            </Avatar>
                                          ) : (
                                            <></>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  <div className="col-8">
                                    {editIndex === index ? (
                                      <MaterialInput
                                        elementType={'materialTextArea'}
                                        changed={(e) =>
                                          onChangeEdit(e.target.value, index, 'reason')
                                        }
                                        type={'text'}
                                        value={gettingSessionLabel(session, 'reason')}
                                        placeholder={'Enter Reason for this changes'}
                                        minRows={3}
                                      />
                                    ) : gettingSessionLabel(session, 'reason').length ? (
                                      <ReasonByToolTip session={session} />
                                    ) : (
                                      '---'
                                    )}
                                  </div>
                                </div>
                              </div>
                              {isTardisEnabled &&
                                session.getIn(['students', 'tardisId', '_id'], '') !== '' && (
                                  <div className="text-capitalize tardis_warning pt-1">
                                    (
                                    {session.getIn(['students', 'tardisId', 'short_code']) +
                                      ' - ' +
                                      jsUcfirstAll(session.getIn(['students', 'tardisId', 'name']))}
                                    )
                                  </div>
                                )}
                            </td>

                            {isModuleEnabled('LATE_ABSENT_CONFIGURATION') && (
                              <td className="text-center">{session.get('lateLabel', '-')}</td>
                            )}
                            <td className="">
                              <div className="cw_140 custom_space">
                                {editIndex === index ? (
                                  <>
                                    {session.get('mode', '').toLowerCase() !== 'onsite' && (
                                      <div className="d-flex justify-content-end">
                                        <div>
                                          <p className="mb-1 f-15 bold">
                                            {Math.round(
                                              session.getIn(['students', 'percentage'], 0)
                                            )}{' '}
                                            %
                                          </p>
                                        </div>
                                      </div>
                                    )}
                                    <div className="d-flex justify-content-end align-items-end mt-5">
                                      <MButton
                                        variant="outlined"
                                        color="gray"
                                        className={'mr-2'}
                                        clicked={() => onChangeEdit('', index, 'cancel')}
                                      >
                                        Cancel
                                      </MButton>
                                      <MButton
                                        variant="contained"
                                        disabled={isSaving}
                                        color="primary"
                                        clicked={() => saveData(session)}
                                      >
                                        {isSaving ? 'Saving...' : 'Save'}
                                      </MButton>
                                    </div>
                                  </>
                                ) : (
                                  <div className="d-flex justify-content-end">
                                    {session.get('mode', '').toLowerCase() === 'onsite' ? (
                                      <div className="mr-5">---</div>
                                    ) : (
                                      <div className="mr-4">
                                        <p className="mb-1 f-15 bold">
                                          {Math.round(session.getIn(['students', 'percentage'], 0))}{' '}
                                          %
                                        </p>
                                        <p className="mb-1 f-15 digi-gray">
                                          {' '}
                                          {getZoomDuration(
                                            session.getIn(['students', 'duration'], '')
                                          )}
                                        </p>
                                      </div>
                                    )}
                                    {!isEdit &&
                                    ['absent', 'present', 'exclude'].includes(
                                      session.get('attendance_status', '')
                                    ) &&
                                    !isRestrictedValue ? (
                                      <div className="">
                                        <EditRoundedIcon
                                          fontSize="small"
                                          sx={{
                                            color: '#000000',
                                          }}
                                          className={`remove_hover`}
                                          onClick={() => {
                                            // cancelSession(index, 'clear', studentSessions);
                                            // if (!isEdit) {
                                            setEditIndex(index);
                                            setIsEdit(true);
                                          }}
                                        />
                                      </div>
                                    ) : (
                                      <div className="">
                                        <EditRoundedIcon
                                          fontSize="small"
                                          sx={{
                                            color: 'lightgray',
                                            cursor: 'not-allowed',
                                          }}
                                        />
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </td>
                          </tr>
                        </Fragment>
                      );
                    })}
                  </tbody>
                ) : (
                  <tbody className="go-wrapper-height">
                    <tr className="tr-change">
                      <td colSpan="12" className="text-center">
                        {t('leaveManagement.noSessionsFound')}...
                      </td>
                    </tr>
                  </tbody>
                )}
              </table>
              {open && (
                <MenuForThead
                  type={typePopOver}
                  setAnchorEl={setAnchorEl}
                  open={open}
                  filterSelectedArray={filterSelectedArray}
                  setFilterSelectedArray={setFilterSelectedArray}
                  anchorEl={anchorEl}
                />
              )}
            </div>
          </div>
          <div className="footer_Sticky_StudentPopup pb-1">
            <PaginationWithCount
              studentAttnSheetDetails={studentAttnSheetDetails}
              filterSelectedArray={filterSelectedArray}
              search={search}
              studentSessions={studentSessions}
              studentSessionsForExport={studentSessionsForExport}
              setStudentSessionsForExport={setStudentSessionsForExport}
              getStaffName={getStaffName}
              setStudentSessions={setStudentSessions}
              pageCount={pageCount}
              currentPage={currentPage}
              setPageCount={setPageCount}
              setCurrentPage={setCurrentPage}
            />
            <div className="d-flex justify-content-end px-3 pb-1">
              <MButton
                variant="outlined"
                color="gray"
                clicked={() => setAttendanceModal(Map({ status: false, data: Map() }))}
              >
                Cancel
              </MButton>
            </div>
          </div>
        </div>
        <BonusAttendanceDrawer
          show={openBonusPopup}
          onClose={handleClosePopupBonus}
          studentId={attendanceModal.getIn(['data', '_student_id'], '')}
          programId={metaData.get('program', '')}
          courseId={metaData.get('courseId', '')}
          year={metaData.get('year', '')}
          level={metaData.get('level', '')}
          term={metaData.get('term', '')}
          rotationCount={metaData.get('rotationCount', '')}
        />
      </DialogModal>
      <DialogModal show={openPopup} onClose={handleClosePopup} fullWidth={true} maxWidth={'sm'}>
        <h5 className="m-3">Exclude Late Attendance</h5>
        <div>
          <RadioGroup
            aria-labelledby="demo-controlled-radio-buttons-group"
            name="controlled-radio-buttons-group"
            value={value}
            onChange={handleChangeRadio}
          >
            <div className="d-flex">
              <div className="ml-2">
                <FormControlLabel value="default" control={<Radio />} label="Default" />
              </div>
              <div>
                <FormControlLabel value="course" control={<Radio />} label="Course" />
              </div>
              <div>
                <FormControlLabel value="session" control={<Radio />} label="Session" />
              </div>
            </div>
          </RadioGroup>
        </div>
        <div className="d-flex">
          <div className="ml-2">
            {value === 'course' || value === 'session' ? (
              <ErrorIcon fontSize="small" color="warning" className="cursor-pointer" />
            ) : (
              ''
            )}
          </div>
          <div className="textColor-warning ml-2">
            {value === 'course'
              ? 'Late Attendance will not be calculated for the entire course'
              : value === 'session'
              ? 'Selected session will not calculate the Late Attendance'
              : ''}
          </div>
        </div>
        {value === 'session' ? (
          <div>
            <FormControl fullWidth variant="outlined" size="small">
              <Select
                labelId="demo-multiple-checkbox-label"
                id="demo-multiple-checkbox"
                MenuProps={MenuProps}
                multiple
                value={personNameAr}
                onChange={(e) => {
                  handleChange(e.target.value);
                }}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {studentSessionsAr.map((value) => {
                      if (selected.includes(value.get('value'))) {
                        return (
                          <Chip
                            key={value.get('session', '')}
                            label={`${value.get('session', '')} - ${getInfraName(value)}`}
                          />
                        );
                      }
                      return null;
                    })}
                  </Box>
                )}
              >
                {studentSessionsAr.map((option) => (
                  <MenuItem
                    className="white-space-normal"
                    key={option.get('value')}
                    value={option.get('value')}
                  >
                    <Checkbox
                      color="primary"
                      checked={personNameAr.indexOf(option.get('value')) > -1}
                      size="small"
                    />

                    <ListItemText primary={option.get('name')} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
        ) : (
          ''
        )}
        <div className="d-flex m-3">
          <div className="ml-auto">
            <MButton clicked={handleClosePopup}>Cancel</MButton>
          </div>
          <div className="ml-2">
            <MButton clicked={handleSubmitPopup}>Submit</MButton>
          </div>
        </div>
      </DialogModal>
    </>
  );
}

NewStudentAttendanceModal.propTypes = {
  setAttendanceModal: PropTypes.func,
  attendanceModal: PropTypes.string,
  getStudentAttendanceSheetDetails: PropTypes.string,
  institutionCalendarId: PropTypes.string,
  metaData: PropTypes.instanceOf(Map),
  userInfo: PropTypes.instanceOf(Map),
  studentAttnSheetDetails: PropTypes.instanceOf(Map),
  updateReason: PropTypes.func,
  setData: PropTypes.func,
  callBack: PropTypes.func,
  setCriteriaModal: PropTypes.func,
  studentBlackBoxContent: PropTypes.instanceOf(Map),
  getStudentBlackBoxContent: PropTypes.func,
  activeProgram: PropTypes.instanceOf(Map),
  isTermCheck: PropTypes.string,
  typeWise: PropTypes.string,
  setTypeWise: PropTypes.func,
};
export default NewStudentAttendanceModal;
