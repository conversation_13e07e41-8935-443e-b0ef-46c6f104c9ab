import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import TextField from '@mui/material/TextField';
import { List, Map } from 'immutable';
import { connect } from 'react-redux';
import { Trans, useTranslation } from 'react-i18next';
import * as actions from '../../../../../../_reduxapi/institution/actions';
import {
  selectStateList,
  selectCityList,
  selectCountryList,
  selectAccreditationType,
} from '../../../../../../_reduxapi/institution/selectors';
import AddressDetails from 'Assets/address_details.svg';
import Accreditation from 'Assets/Accreditation.svg';
import AutoComplete from 'Widgets/FormElements/material/Autocomplete';
import Select from 'react-select';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import AddAccreditationModal from '../../../UniversityDetails/Modal/AddAccreditation';
import DeleteAccreditation from '../../../UniversityDetails/Modal/DeleteAccreditation';

function InstitutionAddDetails({
  isGroup,
  cityList,
  stateList,
  countryList,
  accreditationType,
  getCountryList,
  childFunc,
  getData,
  updatedDetails = {},
  selectedType = '',
  setIsChanged,
  getAccreditation,
  postAddAccreditation,
  deleteAddAccreditation,
}) {
  const validityEndRef = useRef();
  const validityStartRef = useRef();
  useEffect(() => {
    getAccreditation();
  }, []); // eslint-disable-line
  useEffect(() => {
    childFunc.current = fetchData;
  });
  useEffect(() => {
    getCountryList();
    if (updatedDetails.stateId)
      getCountryList('cities?stateId=', 'cityList', updatedDetails.stateId);
    if (updatedDetails.countryId)
      getCountryList('states?countryId=', 'stateList', updatedDetails.countryId);
  }, [getCountryList, updatedDetails.countryId, updatedDetails.stateId]);
  const { t } = useTranslation();
  const [name, setName] = useState(updatedDetails.name ? updatedDetails.name : '');
  const [code, setCode] = useState(updatedDetails.code ? updatedDetails.code : '');
  const [putFlag, setPutFlag] = useState(true);
  const callBack = (value, method) => {
    let copyAccreditation = [...accreditation];
    optionsAccreditationTypeSelect[accreditationIndex] = value.name;

    copyAccreditation[accreditationIndex]['accreditationType'] = {
      value: value.name,
      label: value.name,
      _id: value.id,
    };

    if (method === 'put') {
      copyAccreditation.forEach((item, index) => {
        if (item.accreditationType?._id === value.id) {
          optionsAccreditationTypeSelect[index] = value._id;
          setOptionsAccreditationTypeSelect({ ...optionsAccreditationTypeSelect });
          item.accreditationType.label = value.name;
          item.accreditationType.value = value.name;
        }
      });
      setPutFlag(false);
    }
    setAccreditation(copyAccreditation);
  };
  const setAccreditationName = (value, type) => {
    const formData = { name: value };
    if (Object.keys(type).length === 0) {
      postAddAccreditation(formData, callBack, 'post', type._id);
      setAccreditationShow(false);
      getAccreditation();
    } else {
      postAddAccreditation(formData, callBack, 'put', type._id);
      setAccreditationShow(false);
      getAccreditation();
    }
  };

  useEffect(() => {
    let accreditationArray = Array.isArray(updatedDetails?.accreditation)
      ? updatedDetails?.accreditation?.toJS()
      : [];
    if (updatedDetails?.accreditation) {
      if (Object.keys(optionsAccreditationTypeSelect).length === 0 || putFlag) {
        accreditationArray.forEach((item, index) => {
          optionsAccreditationTypeSelect[index] = item.accreditationType;
          item.accreditationType = accreditationType
            ?.filter((value) => value.get('_id', '') === item.accreditationType)
            ?.map((obj) => {
              return {
                value: obj.get('name', ''),
                label: obj.get('name', ''),
                _id: obj.get('_id', ''),
              };
            })
            .get(0, {});
        });
        setAccreditation(accreditationArray);
      }
      if (!putFlag) {
        accreditation.forEach((item, index) => {
          optionsAccreditationTypeSelect[index] = item.accreditationType._id;
        });
      }
      setOptionsAccreditationType(
        accreditationType?.toJS().map((item) => {
          return { value: item.name, label: item.name, _id: item._id };
        })
      );
      accreditationType?.toJS() !== undefined &&
        setOptionsAccreditationType((arr) => [
          ...arr,
          { value: 'others', label: 'others', _id: 'others' },
        ]);
      setOptionsAccreditationTypeSelect({ ...optionsAccreditationTypeSelect });
    }
  }, [updatedDetails.accreditation, accreditationType]); // eslint-disable-line

  const [accreditationShow, setAccreditationShow] = useState(false);
  const [accreditationTypeName, setAccreditationTypeName] = useState({});
  const [accreditationDeleteShow, setAccreditationDeleteShow] = useState(false);
  const [accreditationDeleteType, setAccreditationDeleteType] = useState('');
  const [accreditationDeleteName, setAccreditationDeleteName] = useState('');
  const [accreditationDeleteID, setAccreditationDeleteID] = useState('');
  const [accreditationIndex, setAccreditationIndex] = useState(0);
  const [accreditation, setAccreditation] = useState([]);
  const [optionsAccreditationType, setOptionsAccreditationType] = useState([]);
  const [optionsAccreditationTypeSelect, setOptionsAccreditationTypeSelect] = useState({});
  const [noOfColleges, setNoCollege] = useState(
    updatedDetails.noOfColleges ? updatedDetails.noOfColleges : ''
  );

  const [address, setAddress] = useState(updatedDetails.address ? updatedDetails.address : '');
  const [country, setCountry] = useState(
    updatedDetails.country
      ? {
          name: updatedDetails.country,
          value: updatedDetails.country,
          countryID: updatedDetails.countryId,
        }
      : {
          name: '',
          value: '',
          countryID: '',
        }
  );
  const [state, setState] = useState(
    updatedDetails.state
      ? {
          name: updatedDetails.state,
          value: updatedDetails.state,
          stateID: updatedDetails.stateId,
        }
      : {
          name: '',
          value: '',
          stateID: '',
        }
  );
  const [city, setCity] = useState(
    updatedDetails.city
      ? {
          name: updatedDetails.city,
          value: updatedDetails.city,
        }
      : {
          name: '',
          value: '',
        }
  );
  const [district, setDistrict] = useState(updatedDetails.district ? updatedDetails.district : '');
  const [zipCode, setZipCode] = useState(updatedDetails.zipCode ? updatedDetails.zipCode : '');

  const [cID, setCID] = useState({ current: -1, previous: -1 });
  const [sID, setSID] = useState({ current: -1, previous: -1 });
  useEffect(() => {
    getOptions('state');
  }, [cID]);
  useEffect(() => {
    getOptions('city');
  }, [sID]);
  function getCountries() {
    return countryList
      .map((c) =>
        Map({ name: c.get('name', ''), value: c.get('name', ''), countryID: c.get('id', '') })
      )
      .toJS();
  }
  const getOptions = (name) => {
    if (name === 'state' && cID.previous !== cID.current) {
      setCID({ ...cID, previous: cID.current });
      getCountryList('states?countryId=', 'stateList', cID.current);
      return;
    }
    if (name === 'city' && sID.previous !== sID.current) {
      setSID({ ...sID, previous: sID.current });

      getCountryList('cities?stateId=', 'cityList', sID.current);
      return;
    }
    if (name === 'country' && !countryList.size) {
      getCountryList();
      return;
    }
  };

  function getStates() {
    return stateList
      .map((c) =>
        Map({ name: c.get('name', ''), value: c.get('name', ''), stateID: c.get('id', '') })
      )
      .toJS();
  }

  function getCities() {
    return cityList.map((c) => Map({ name: c.get('name', ''), value: c.get('name', '') })).toJS();
  }
  function fetchData() {
    let logo = {};
    let data = {};
    if (!isGroup) data = { isUniversity: isGroup, accreditation: accreditation };
    if (!isGroup && selectedType === 'college') data = { isUniversity: isGroup };
    if (isGroup) data = { accreditation: accreditation, noOfColleges, isUniversity: isGroup };
    logo.logo = updatedDetails.logo ? updatedDetails.logo : '';
    return getData({
      ...data,
      name: name.trim(),
      code: code.trim(),
      address: address.trim(),
      country: country.name,
      state: state.name,
      city: city.name,
      district: district.trim(),
      zipCode: zipCode.trim(),
      countryId: cID.current === -1 ? updatedDetails.countryId : cID.current,
      stateId: sID.current === -1 ? updatedDetails.stateId : sID.current,
      ...logo,
    });
  }
  function resetStateAndCity() {
    setState({
      name: '',
      value: '',
      stateID: '',
    });
    setCity({
      name: '',
      value: '',
    });
  }
  function resetCity() {
    setCity({
      name: '',
      value: '',
    });
  }
  const getName = () => `${selectedType ? 'College' : isGroup ? 'University' : 'College'}`;

  const handleChange = (event, setStateData) => {
    setStateData(event);
    setIsChanged(true);
  };

  const handleAccreditationChange = (event, setStateData, index) => {
    if (setStateData === 'accreditationType' && event?.value === 'others') {
      setAccreditationIndex(index);
      setAccreditationShow(true);
      setAccreditationTypeName({});
      return;
    } else if (setStateData === 'accreditationType') {
      optionsAccreditationTypeSelect[index] = event?._id;
      setOptionsAccreditationTypeSelect({ ...optionsAccreditationTypeSelect });
    }
    let newArr = [...accreditation];
    newArr[index][setStateData] = event;
    setAccreditation(newArr);
  };

  const addNew = () => {
    let copyAccreditation = [...accreditation];
    copyAccreditation[accreditation?.length] = {
      accreditationAgencyName: '',
      accreditationType: '',
      accreditationNumber: '',
      accreditationValue: '',
      validityStart: '',
      validityEnd: '',
      others: '',
    };
    setAccreditation(copyAccreditation);
  };
  const removeAccreditation = (val) => {
    if (val === '') {
      let copyAccreditation = accreditation.filter((item, index) => index !== accreditationIndex);
      setAccreditation(copyAccreditation);
    } else {
      deleteAddAccreditation(accreditationDeleteID);
      let copyOptionsAccreditationType = optionsAccreditationType.filter(
        (item) => item._id !== accreditationDeleteID
      );
      setOptionsAccreditationType([...copyOptionsAccreditationType]);
      let copyAccreditation = accreditation.map((item) => {
        if (item.accreditationType._id === accreditationDeleteID) {
          item.accreditationType = '';
        }
        return item;
      });
      setAccreditation(copyAccreditation);
      getAccreditation();
    }
  };
  const autoClickDate = (ref) => {
    ref?.current?.setFocus(); // eslint-disable-line
  };

  return (
    <div className="row pt-2">
      <div className="col-md-12">
        <label>{`${getName()} Name`}</label>
        <TextField
          type="text"
          placeholder={`Type Your ${getName()} Name`}
          variant="outlined"
          value={name}
          size="small"
          fullWidth
          onChange={(e) => handleChange(e.target.value, setName)}
          inputProps={{ maxLength: 150 }}
        />
      </div>
      <div className="col-md-12 pt-3">
        <label>{`${getName()} Code`}</label>

        <TextField
          placeholder={`Type Your ${getName()} Code`}
          type="text"
          variant="outlined"
          size="small"
          fullWidth
          value={code}
          onChange={(e) => handleChange(e.target.value, setCode)}
          inputProps={{ maxLength: 20 }}
        />
      </div>
      {/* {!isGroup && (
        <div className="col-md-12 pt-3">
          <label>College Name</label>
          <TextField
            type="text"
            variant="outlined"
            size="small"
            fullWidth
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
        </div>
      )} */}
      {isGroup && (
        <div className="col-md-12 pt-3">
          <label>
            <Trans i18nKey={'add_colleges.No_colleges_under_university'}></Trans>
          </label>
          <TextField
            placeholder="05"
            type="text"
            variant="outlined"
            size="small"
            fullWidth
            value={noOfColleges}
            onChange={(e) => handleChange(e.target.value, setNoCollege)}
            inputProps={{
              maxLength: 4,
              pattern: '[0-9]*',
            }}
          />
        </div>
      )}
      {!selectedType && (
        <AddAccreditationModal
          show={accreditationShow}
          handleAccreditationClose={() => setAccreditationShow(false)}
          setAccreditationName={setAccreditationName}
          textName={accreditationTypeName}
          optionsAccreditationType={optionsAccreditationType}
        />
      )}
      {accreditationDeleteShow && (
        <DeleteAccreditation
          show={accreditationDeleteShow}
          handleClose={() => setAccreditationDeleteShow(false)}
          type={accreditationDeleteType}
          handleDelete={(val) => removeAccreditation(val)}
          accreditationDeleteName={accreditationDeleteName}
        />
      )}
      {!selectedType && (
        <>
          <div className="col-md-12 col-lg-12 d-flex flex-row pt-3">
            <div className="col-md-9 px-0 d-flex">
              <img alt="" src={Accreditation} className="pr-2" />
              <p className="mb-0 bold f-18">
                <Trans i18nKey={'add_colleges.Accreditation_Details'}></Trans>
              </p>
            </div>
            <div
              className="col-md-3 px-0 AssessmentActive framework-domain-actions remove_hover"
              onClick={() => addNew()}
            >
              <i className="fa fa-plus pr-2" aria-hidden="true"></i>
              <Trans i18nKey={'add_new_small'}></Trans>
            </div>
          </div>
          {accreditation.length > 0 &&
            accreditation.map((item, index) => {
              return (
                <div className="col-md-12 pt-3 px-0" key={index}>
                  <div className="d-flex flex-row">
                    <label className="col-md-9">
                      <Trans i18nKey={'add_colleges.Accreditation_Details'}></Trans>{' '}
                      {index !== 0 ? index + 1 : null}
                    </label>
                    <div
                      className="col-md-3 px-0 text-danger framework-domain-actions remove_hover"
                      onClick={() => {
                        let setNaming = t('add_colleges.Accreditation_Details');
                        setNaming = setNaming + ' ' + (index !== 0 ? (index + 1).toString() : '');
                        setAccreditationDeleteShow(true);
                        setAccreditationDeleteType('Remove');
                        setAccreditationDeleteName(setNaming);
                        setAccreditationIndex(index);
                      }}
                    >
                      <Trans i18nKey={'Remove'}></Trans>
                    </div>
                  </div>
                  <div className="Accreditation_box">
                    <div className="row m-0">
                      <div className="col-md-6 col-lg-6 px-1">
                        <label className="py-2">
                          <Trans i18nKey={'add_colleges.Accreditation_Agency_name'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          variant="outlined"
                          size="small"
                          fullWidth
                          className="bg-white"
                          placeholder={t('settings.benchmark_setting.enter_name')}
                          value={item.accreditationAgencyName}
                          onChange={(e) =>
                            handleAccreditationChange(
                              e.target.value,
                              'accreditationAgencyName',
                              index
                            )
                          }
                          inputProps={{ maxLength: 150 }}
                        />
                      </div>
                      <div className="col-md-6 col-lg-6 px-1">
                        <label className="py-2">
                          <Trans i18nKey={'add_colleges.Accreditation_Type'}></Trans>
                        </label>
                        <Select
                          value={item.accreditationType}
                          options={optionsAccreditationType?.filter(
                            (item) => item?._id !== optionsAccreditationTypeSelect[index]
                          )}
                          onChange={(e) => handleAccreditationChange(e, 'accreditationType', index)}
                          isClearable={true}
                          placeholder={t('add_colleges.Select_type')}
                          getOptionLabel={(e) => (
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                              }}
                            >
                              <span style={{ marginLeft: 5 }}>{e.value}</span>
                              {!(
                                e.value === 'others' ||
                                e._id === optionsAccreditationTypeSelect[index]
                              ) && (
                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                  }}
                                >
                                  <i
                                    className="fa fa-pencil px-2"
                                    onClick={() => {
                                      setAccreditationIndex(index);
                                      setAccreditationShow(true);
                                      setAccreditationTypeName(e);
                                    }}
                                  ></i>
                                  <i
                                    className="fa fa-trash px-2"
                                    onClick={() => {
                                      if (
                                        !Object.values(optionsAccreditationTypeSelect).includes(
                                          e.value
                                        )
                                      ) {
                                        setAccreditationDeleteShow(true);
                                        setAccreditationDeleteType('delete');
                                        setAccreditationDeleteName(e);
                                        setAccreditationDeleteID(e._id);
                                        setAccreditationIndex(index);
                                      }
                                    }}
                                  ></i>
                                </div>
                              )}
                            </div>
                          )}
                        />
                      </div>
                    </div>
                    <div className="row m-0">
                      <div className="col-md-6 col-lg-6 px-1">
                        <label className="py-2">
                          <Trans i18nKey={'add_colleges.Accreditation_Number'}></Trans>
                        </label>
                        <TextField
                          type="number"
                          variant="outlined"
                          size="small"
                          fullWidth
                          placeholder={t('settings.benchmark_setting.enter_number')}
                          className="bg-white"
                          value={item.accreditationNumber}
                          onChange={(e) =>
                            handleAccreditationChange(e.target.value, 'accreditationNumber', index)
                          }
                          inputProps={{ maxLength: 150 }}
                        />
                      </div>
                      <div className="col-md-6 col-lg-6 px-1">
                        <label className="py-2">
                          <Trans i18nKey={'add_colleges.Accreditation_Value'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          variant="outlined"
                          size="small"
                          fullWidth
                          className="bg-white"
                          placeholder={t('settings.benchmark_setting.enter_value')}
                          value={item.accreditationValue}
                          onChange={(e) =>
                            handleAccreditationChange(e.target.value, 'accreditationValue', index)
                          }
                          inputProps={{ maxLength: 150 }}
                        />
                      </div>
                    </div>
                    <div className="row m-0">
                      <div className="col-md-6 col-lg-6 px-1 d-flex flex-column">
                        <label className="py-2">
                          <Trans i18nKey={'add_colleges.Validity_Start_On'}></Trans>
                        </label>
                        <div className="accreditation_main">
                          <DatePicker
                            ref={validityStartRef}
                            placeholderText="DD / MM / YYYY"
                            openCalendarOnFocus={true}
                            onChange={(d) => handleAccreditationChange(d, 'validityStart', index)}
                            value={
                              item.validityStart !== '' && item.validityStart !== null
                                ? moment(item.validityStart).format('DD/MM/YYYY')
                                : ''
                            }
                            dateFormat="dd MM yyyy"
                            className={'form-control'}
                            maxDate={
                              item.validityEnd !== '' && item.validityEnd !== null
                                ? item.validityEnd
                                : ''
                            }
                            showMonthDropdown
                            showYearDropdown
                            yearDropdownItemNumber={15}
                          />
                          <i
                            className="fa fa-calendar accreditation_date"
                            onClick={() => autoClickDate(validityStartRef)}
                          ></i>
                        </div>
                      </div>
                      <div className="col-md-6 col-lg-6 px-1 d-flex flex-column">
                        <label className="py-2">
                          <Trans i18nKey={'add_colleges.Validity_Ends_On'}></Trans>
                        </label>
                        <div className="accreditation_main">
                          <DatePicker
                            ref={validityEndRef}
                            placeholderText="DD / MM / YYYY"
                            onChange={(d) => handleAccreditationChange(d, 'validityEnd', index)}
                            value={
                              item.validityEnd !== '' && item.validityEnd !== null
                                ? moment(item.validityEnd).format('DD/MM/YYYY')
                                : ''
                            }
                            dateFormat="dd MM yyyy"
                            className={'form-control'}
                            showMonthDropdown
                            showYearDropdown
                            minDate={
                              item.validityStart !== '' && item.validityStart !== null
                                ? item.validityStart
                                : ''
                            }
                            yearDropdownItemNumber={15}
                          />
                          <i
                            className="fa fa-calendar accreditation_date"
                            onClick={() => autoClickDate(validityEndRef)}
                          ></i>
                        </div>
                      </div>
                    </div>
                    <div className="row m-0">
                      <div className="col-md-12 col-lg-12 px-1">
                        <label className="py-2">
                          <Trans i18nKey={'add_colleges.Others'}></Trans>
                        </label>
                        <TextField
                          type="text"
                          variant="outlined"
                          size="small"
                          fullWidth
                          placeholder={t('add_colleges.Small_Discription')}
                          className="bg-white"
                          value={item.others}
                          onChange={(e) =>
                            handleAccreditationChange(e.target.value, 'others', index)
                          }
                          inputProps={{ maxLength: 150 }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
        </>
      )}

      <div className="col-md-12 pt-4 d-flex">
        <img className="pr-2" alt="" src={AddressDetails} />{' '}
        <p className="mb-0 bold f-18">
          <Trans i18nKey={'add_colleges.address_details'}></Trans>
        </p>
      </div>
      <div className="col-md-12 pt-3">
        <label>
          <Trans i18nKey={'add_colleges.address_line1'}></Trans>
        </label>
        <TextField
          type="text"
          variant="outlined"
          size="small"
          fullWidth
          placeholder={t('add_colleges.complete_address')}
          value={address}
          onChange={(e) => handleChange(e.target.value, setAddress)}
          inputProps={{ maxLength: 150 }}
        />
      </div>
      <div className="col-md-6 pt-3">
        <label className="form-label">
          <Trans i18nKey={'add_colleges.country'}></Trans>
        </label>
        <AutoComplete
          placeholder={t('add_colleges.Choose_Country')}
          options={getCountries()}
          getOptionLabel={(option) => option.value}
          value={country}
          isClearable={false}
          onChange={(event, value) => {
            setCID({
              previous: cID.current,
              current: value.countryID,
            });
            setCountry(value);
            resetStateAndCity();
          }}
        />
      </div>
      <div className="col-md-6  pt-3">
        <label>
          <Trans i18nKey={'add_colleges.state_region'}></Trans>
        </label>
        <AutoComplete
          placeholder={t('add_colleges.Choose_State')}
          options={getStates()}
          getOptionLabel={(option) => option.value}
          value={state}
          isClearable={false}
          onChange={(event, value) => {
            setSID({
              previous: sID.current,
              current: value.stateID,
            });
            setState(value);
            resetCity();
          }}
        />
      </div>
      <div className="col-md-6 pt-3">
        <label>
          {' '}
          <Trans i18nKey={'add_colleges.district'}></Trans>
        </label>
        <TextField
          placeholder={t('add_colleges.type_district')}
          type="text"
          variant="outlined"
          size="small"
          fullWidth
          value={district}
          onChange={(e) => handleChange(e.target.value, setDistrict)}
        />
      </div>
      <div className="col-md-6 pt-3">
        <label>
          <Trans i18nKey={'add_colleges.city'}></Trans>
        </label>
        <AutoComplete
          placeholder={t('add_colleges.choose_city')}
          options={getCities()}
          getOptionLabel={(option) => option.value}
          value={city}
          isClearable={false}
          onChange={(event, value) => {
            setCity(value);
          }}
        />
      </div>
      <div className="col-md-6 pt-3">
        <label>
          <Trans i18nKey={'zip_code'}></Trans>
        </label>
        <TextField
          type="text"
          variant="outlined"
          size="small"
          fullWidth
          value={zipCode}
          onChange={(e) => handleChange(e.target.value, setZipCode)}
          placeholder={t('type_zipcode')}
          inputProps={{ maxLength: 25 }}
        />
      </div>
    </div>
  );
}

InstitutionAddDetails.propTypes = {
  isGroup: PropTypes.bool,
  stateList: PropTypes.instanceOf(List),
  cityList: PropTypes.instanceOf(List),
  countryList: PropTypes.instanceOf(List),
  accreditationType: PropTypes.instanceOf(List),
  getCountryList: PropTypes.func,
  childFunc: PropTypes.object,
  updatedDetails: PropTypes.object,
  getData: PropTypes.func,
  selectedType: PropTypes.string,
  setIsChanged: PropTypes.func,
  getAccreditation: PropTypes.func,
  postAddAccreditation: PropTypes.func,
  deleteAddAccreditation: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    stateList: selectStateList(state),
    cityList: selectCityList(state),
    countryList: selectCountryList(state),
    accreditationType: selectAccreditationType(state),
  };
};

export default connect(mapStateToProps, actions)(InstitutionAddDetails);
