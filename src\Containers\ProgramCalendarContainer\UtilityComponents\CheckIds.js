import { useEffect } from 'react';
import PropTypes, { oneOfType } from 'prop-types';
import { connect } from 'react-redux';
import { useRouteMatch, useHistory, useLocation } from 'react-router-dom';
import { changeYear, indexChange } from '../../../_reduxapi/actions/calender';

const CheckIds = (props) => {
  const {
    id,
    apiCalled,
    id_array,
    changeYear,
    state_active,
    indexChange,
    _calender_id,
    load,
    is_interim,
    year1,
    year2,
    year3,
    year4,
    year5,
    year6,
  } = props;

  const history = useHistory();
  const match = useRouteMatch();
  const location = useLocation();
  const active = match.params.year || 'year1';

  useEffect(() => {
    if (
      apiCalled &&
      load &&
      match.params.id &&
      match.params.year &&
      (!year1.year_show || match.params.year !== 'year1') &&
      (!year2.year_show || match.params.year !== 'year2') &&
      (!year3.year_show || match.params.year !== 'year3') &&
      (!year4.year_show || match.params.year !== 'year4') &&
      (!year5.year_show || match.params.year !== 'year5') &&
      (!year6.year_show || match.params.year !== 'year6')
    ) {
      if (match.params.sem) {
        // history.push(
        //   `/${match.url
        //     .split("/")
        //     .filter((item, i) => i === 1)
        //     .reduce((acc, val) => val)}/${match.params.id}/year2/${
        //     match.params.sem
        //   }`
        // );
      } else {
        history.push(
          `/${match.url
            .split('/')
            .filter((item, i) => i === 1)
            .reduce((acc, val) => val)}/${match.params.id}/year1${location.search}`
        );
      }
    }

    if (
      apiCalled &&
      load &&
      is_interim &&
      match.params.id &&
      match.params.year &&
      match.params.sem &&
      (!year2.semester1_show || match.params.sem !== 'semester1') &&
      (!year2.semester2_show || match.params.sem !== 'semester2') &&
      (!year3.semester2_show || match.params.sem !== 'semester2') &&
      (!year3.semester1_show || match.params.sem !== 'semester1') &&
      (!year4.semester2_show || match.params.sem !== 'semester2') &&
      (!year4.semester1_show || match.params.sem !== 'semester1') &&
      (!year5.semester2_show || match.params.sem !== 'semester2') &&
      (!year5.semester1_show || match.params.sem !== 'semester1') &&
      (!year6.semester2_show || match.params.sem !== 'semester2') &&
      (!year6.semester1_show || match.params.sem !== 'semester1')
    ) {
      // history.push(
      //   `/${match.url
      //     .split("/")
      //     .filter((item, i) => i === 1)
      //     .reduce((acc, val) => val)}/${match.params.id}/${
      //     match.params.year
      //   }/semester1`
      // );
    }

    if (
      apiCalled &&
      load &&
      !is_interim &&
      match.params.id &&
      match.params.year &&
      match.params.sem
    ) {
      // history.push(
      //   `/${match.url
      //     .split("/")
      //     .filter((item, i) => i === 1)
      //     .reduce((acc, val) => val)}/${match.params.id}/${match.params.year}`
      // );
    }

    if (
      apiCalled &&
      load &&
      match.params.id &&
      match.params.year &&
      id_array.length !== 0 &&
      id_array.map((item) => item._id).indexOf(match.params.id) === -1
    ) {
      if (match.params.sem) {
        // history.push(
        //   `/${match.url
        //     .split("/")
        //     .filter((item, i) => i === 1)
        //     .reduce((acc, val) => val)}/${id_array[0]["_id"]}/${
        //     match.params.year
        //   }/${match.params.sem}`
        // );
      } else {
        history.push(
          `/${match.url
            .split('/')
            .filter((item, i) => i === 1)
            .reduce((acc, val) => val)}/${id_array[0]['_id']}/${match.params.year}${
            location.search
          }`
        );
      }
    }

    if (
      apiCalled &&
      load &&
      id_array.length !== 0 &&
      _calender_id !== '' &&
      (match.params.id !== id || (_calender_id && year2['id'] && _calender_id !== year2['id']))
    ) {
      indexChange(-2, null, id_array, match, history.push);
    }

    if (apiCalled && load && state_active && active && active !== state_active) {
      changeYear(active);
    }
  }, [
    id,
    indexChange,
    changeYear,
    active,
    load,
    year2,
    history,
    is_interim,
    state_active,
    _calender_id,
    id_array,
    apiCalled,
    match.params.year,
    match.params.id,
    match.params.sem,
    year2.semester1_show,
    year2.semester2_show,
    year3.semester1_show,
    year3.semester2_show,
    year4.semester1_show,
    year4.semester2_show,
    year5.semester1_show,
    year5.semester2_show,
    year6.semester1_show,
    year6.semester2_show,
    year1.year_show,
    year2.year_show,
    year3.year_show,
    year4.year_show,
    year5.year_show,
    year6.year_show,
    match,
    location.search,
  ]);

  return null;
};

CheckIds.propTypes = {
  id: PropTypes.string,
  _calender_id: PropTypes.string,
  apiCalled: PropTypes.bool,
  state_active: oneOfType([PropTypes.bool, PropTypes.string]),
  load: PropTypes.bool,
  is_interim: PropTypes.bool,
  year1: PropTypes.object,
  year2: PropTypes.object,
  year3: PropTypes.object,
  year4: PropTypes.object,
  year5: PropTypes.object,
  year6: PropTypes.object,
  id_array: PropTypes.array,
  changeYear: PropTypes.func,
  indexChange: PropTypes.func,
};

const mapStateToProps = ({ calender }) => ({
  apiCalled: calender.commonApiCalled,
  state_active: calender.active_year,
  id_array: calender.acad_array,
  _calender_id: calender.program_calender_id,
  id: calender.institution_Calender_Id,
  is_interim: calender.interim,
  year1: calender.year1,
  year2: calender.year2,
  year3: calender.year3,
  year4: calender.year4,
  year5: calender.year5,
  year6: calender.year6,
});

export default connect(mapStateToProps, { changeYear, indexChange })(CheckIds);
