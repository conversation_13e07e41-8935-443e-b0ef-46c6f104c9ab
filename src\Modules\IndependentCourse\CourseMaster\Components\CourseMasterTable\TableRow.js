import React, { useContext, useState } from 'react';
import { useHistory, useRouteMatch, useParams } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Map } from 'immutable';
import { t } from 'i18next';

import { IconButton, Menu, MenuItem } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import { eString } from 'utils';
import Delete from 'Containers/Modal/Delete';
import SharedIcon from 'Assets/img/v2/shared.svg';
import Tooltip from 'Widgets/FormElements/material/Tooltip';
import Button from 'Widgets/FormElements/material/Button';
import EditCourseModal from '../../Modal/AddCourseModal';
import { independentCourseContext, courseMasterContext } from '../../../context';
import { getShortString } from 'Modules/Shared/v2/Configurations';
const CourseTableRow = ({ course, index, onSave }) => {
  const { getToolTipData, institutionId } = useContext(independentCourseContext);
  const { getCourseList, deleteCourseById, getIndependentSubjectsList } = useContext(
    courseMasterContext
  );
  const history = useHistory();
  const match = useRouteMatch();
  const { type: institutionType, name: institutionName, id: institutionID } = useParams();
  const [anchorEl, setAnchorEl] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const open = Boolean(anchorEl);
  const closeEditModal = () => setShowEditModal(false);
  const onClickSave = (type, isEdit) =>
    onSave.onClickSave(type, {
      isEdit,
      courseID: course.get('_id', ''),
      closeEditModal: setShowEditModal,
    });
  if (showEditModal)
    return (
      <EditCourseModal
        show={showEditModal}
        onClose={closeEditModal}
        onSave={{ saveDetails: onSave.saveDetails, onClickSave }}
        course={course}
        isEdit={true}
      />
    );
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const isConfigured =
    course.get('isConfigured', false) && course.get('isActive', false)
      ? course.get('isConfigured', false)
      : false;
  const handleEditDelete = (option) => {
    if (option === 'Delete') {
      setShowDeleteModal(true);
    }
    if (option === 'Edit' && !isConfigured) {
      getIndependentSubjectsList(institutionId);
      setShowEditModal(true);
    } else if (option === 'Edit' && isConfigured) {
      history.push(`${match.url}/configuration?cid=${eString(course.get('_id', ''))}`);
    }
    setAnchorEl(null);
  };
  const options = ['Edit', 'Delete'];

  const handleDelete = () => {
    const callBack = () => {
      setShowDeleteModal(false);
      getCourseList({});
    };
    deleteCourseById(course.get('_id', ''), callBack);
  };

  const ITEM_HEIGHT = 48;
  const onClickViewOrConfigure = () => {
    const goToSharedCourse = () => {
      const programId = eString(course.getIn(['courseSharedFrom', '_program_id'], ''));
      const verId = eString(course.getIn(['courseSharedFrom', '_curriculum_id'], ''));
      const level_id = eString(course.getIn(['courseSharedFrom', '_level_id'], ''));
      const redirectUrl = `/${institutionType}/${institutionID}/${institutionName}/pgm-input/configuration/${programId}/course-master-list/view-course?cid=${eString(
        course.get('_id', '')
      )}&_ver_id=${verId}&level_id=${level_id}&shared=${eString('independent')}`;
      return redirectUrl;
    };
    let reDirectUrl = '';
    if (course.get('courseShared', false)) reDirectUrl = goToSharedCourse();
    else
      reDirectUrl = `${match.url}/${isConfigured ? 'view-course' : 'configuration'}?cid=${eString(
        course.get('_id', '')
      )}`;
    history.push(reDirectUrl);
  };
  return (
    <tr className="tr-change">
      <td>
        {course.get('courseShared', false) && (
          <Tooltip
            title={`${t('program_input.shared_from')} ${course.getIn(
              ['courseSharedFrom', 'programName'],
              ''
            )}`}
          >
            <img src={SharedIcon} alt="icon" className="ml-2" />
          </Tooltip>
        )}
      </td>
      <td className="">
        <div className="mt-2">{index + 1}</div>
      </td>
      <td className="">
        <div className="mt-2">{getShortString(course.get('courseCode', ''), 10)}</div>
      </td>
      <td className="">
        <div className="mt-2">{getShortString(course.get('courseName', ''), 25)}</div>
      </td>
      <td className="break-word">
        <div className="mt-2">{course.getIn(['administration', 'departmentName'], '')}</div>
      </td>
      <td className="">
        <div className="d-flex">
          <div className="mt-2">N/A</div>
          {/* <div className="mt-1 pr-4 remove_hover">
            <i className="fa fa-caret-down" aria-hidden="true"></i>
          </div> */}
        </div>
      </td>

      <td className="">
        <div className="mt-2">
          <Button
            clicked={onClickViewOrConfigure}
            variant="outlined"
            color={!isConfigured ? 'yellow' : 'gray'}
            //className={classes.warningButton}
            fullWidth
          >
            {isConfigured ? 'View' : 'Configure'}
          </Button>
        </div>
      </td>

      <td className="">
        {!course.get('courseShared', false) && (
          <>
            <IconButton
              aria-label="more"
              aria-controls="long-menu"
              aria-haspopup="true"
              onClick={handleClick}
            >
              <MoreVertIcon />
            </IconButton>
            <Menu
              id="long-menu"
              anchorEl={anchorEl}
              keepMounted
              open={open}
              onClose={handleClose}
              PaperProps={{
                style: {
                  maxHeight: ITEM_HEIGHT * 4.5,
                  width: '20ch',
                },
              }}
            >
              {options.map((option) => (
                <MenuItem
                  key={option}
                  selected={option === 'Pyxis'}
                  onClick={() => handleEditDelete(option)}
                >
                  {option}
                </MenuItem>
              ))}
            </Menu>
          </>
        )}
      </td>
      {showDeleteModal && (
        <Delete
          show={showDeleteModal}
          setShow={setShowDeleteModal}
          deleteSelected={handleDelete}
          title={'course'}
          description={
            <Trans
              components={{
                courseToolTip: getToolTipData.course,
              }}
            >
              course_delete
            </Trans>
          }
          deleteName={course.get('courseName', '')}
        />
      )}
    </tr>
  );
};
CourseTableRow.propTypes = {
  course: PropTypes.instanceOf(Map),
  onSave: PropTypes.object,
  index: PropTypes.number,
};

export default CourseTableRow;
