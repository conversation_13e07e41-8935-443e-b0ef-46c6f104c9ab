import React, { useState } from 'react';
import { t } from 'i18next';
import { Map } from 'immutable';
import { GetLabelName, getLabelName } from 'Modules/Shared/v2/Configurations';
import { checkIndividual } from 'Modules/ProgramInput/v2/piUtil';
export const getTransLatedLabelName = () => ({
  course: <GetLabelName label="course" length={10} />,
  department: <GetLabelName label="departments" length={15} />,
  program: <GetLabelName label="program" length={10} />,
  subject: <GetLabelName label="subject" length={10} />,
  year: <GetLabelName label="year" length={10} />,
  level: <GetLabelName label="level" length={10} />,
  yearNT: getLabelName({ label: 'year', length: 10, isToolTip: false }),
  levelNT: getLabelName({ label: 'level', length: 10, isToolTip: false }),
  courseNT: getLabelName({ label: 'course', length: 10, isToolTip: false }),
  departmentNT: getLabelName({ label: 'departments', length: 15, isToolTip: false }),
  programNT: getLabelName({ label: 'program', length: 10, isToolTip: false }),
  subjectNT: getLabelName({ label: 'subject', length: 10, isToolTip: false }),
});

export const useStateHook = (defaultValue) => {
  const [state, setState] = useState(defaultValue);
  const onChangeState = (event) => {
    if (event && event.target) return setState(event.target.value);
    return setState(event);
  };
  return [state, onChangeState];
};

export const checkCourseValidation = (data, setData, getToolTipData) => {
  const { courseNT, subjectNT } = getToolTipData;
  if (checkIndividual(data.courseCode, `${courseNT} code`, setData, 20)) return false;
  if (checkIndividual(data.courseName, `${courseNT} name`, setData, 150)) return false;
  if (data.participating.length === 0) {
    setData(Map({ message: `Delivering ${subjectNT} is required` }));
    return false;
  }
  if (
    Object.entries(data.administration).length === 0 ||
    data.administration._subject_id === null
  ) {
    setData(Map({ message: `Administration ${subjectNT} is required` }));
    return false;
  }
  return true;
};

export const checkShareValidation = (courseSharedWith, courseDuration, setData, getToolTipData) => {
  if (!courseDuration.startWeek) {
    setData(Map({ message: t('start_week_is_required') }));
    return true;
  }
  if (courseDuration.length !== 0) {
    const programNameMissed = courseSharedWith.some((item) => item.programName === '');
    if (programNameMissed) {
      const { programNT } = getToolTipData;
      setData(Map({ message: t('is_required', { label: programNT }) }));
      return true;
    }

    const curriculumNameMissed = courseSharedWith.some((item) => item.curriculumName === '');
    if (curriculumNameMissed) {
      setData(Map({ message: t('is_required', { label: 'Curriculum' }) }));
      return true;
    }
    const yearNameMissed = courseSharedWith.some((item) => item.year === '');
    if (yearNameMissed) {
      const { yearNT } = getToolTipData;
      setData(Map({ message: t('is_required', { label: yearNT }) }));
      return true;
    }
    const levelNameMissed = courseSharedWith.some((item) => item.levelNo === '');
    if (levelNameMissed) {
      const { levelNT } = getToolTipData;
      setData(Map({ message: t('is_required', { label: levelNT }) }));
      return true;
    }
  }
  return false;
};
