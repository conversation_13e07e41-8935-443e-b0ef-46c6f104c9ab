import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import parentContext from '../Context/Context';
import { useHistory, useRouteMatch } from 'react-router';
import MButton from 'Widgets/FormElements/material/Button';
import { eString } from 'utils';

import { Nodata } from '../../../ProgramInput/v2/piUtil';
import { getShortString } from 'Modules/Shared/v2/Configurations';

function TableBody() {
  const history = useHistory();
  const match = useRouteMatch();
  const IndividualRow = ({ RowData }) => {
    IndividualRow.propTypes = {
      RowData: PropTypes.instanceOf(Map),
    };

    return (
      <>
        <tr className="tr-change">
          <td className="">
            <div className="mt-2 d-flex">{getShortString(RowData.get('code', ''), 15)}</div>
          </td>
          <td className="">
            <div className="mt-2 d-flex">{getShortString(RowData.get('name', ''), 15)}</div>
          </td>
          <td className="">
            <div className="mt-2 d-flex">
              {getShortString(RowData.get('programType', '---'), 15)}
            </div>
          </td>
          <td className="">
            <div className="mt-2 d-flex">{getShortString(RowData.get('degree', '---'), 15)}</div>
          </td>
          <td className="">
            <div className="d-flex justify-content-between ">
              <div className="pr-1">
                <MButton
                  color="gray"
                  variant="outlined"
                  fullWidth={true}
                  clicked={() => history.push(`${match.url}/${eString(RowData.get('_id'))}/view`)}
                >
                  <Trans i18nKey={'view'}></Trans>
                </MButton>
              </div>
            </div>
          </td>
        </tr>
      </>
    );
  };

  function redirectUrl() {
    const { params } = match;
    history.push(`/${params.type}/${params.id}/${params.name}/pgm-input`);
  }

  const details = useContext(parentContext.PIL_Context);
  const { programInputList, isLoading } = details;
  let filteredData = programInputList.get('programs', []);
  return (
    <>
      <tbody>
        {filteredData.size ? (
          filteredData.map((RowData, rIndex) => {
            return <IndividualRow RowData={RowData} key={rIndex} />;
          })
        ) : !isLoading ? (
          <Nodata callBack={redirectUrl} />
        ) : null}
      </tbody>
    </>
  );
}

export default TableBody;
