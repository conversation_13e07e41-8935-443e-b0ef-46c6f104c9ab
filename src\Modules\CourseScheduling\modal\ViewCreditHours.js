import React from 'react';
import { Mo<PERSON>, But<PERSON> } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import moment from 'moment';
import { getWeeks } from '../components/utils';
import { indVerRename } from 'utils';
import { t } from 'i18next';

export default function ViewCreditHours({ course, modalClick, programId }) {
  const weeks = getWeeks(course.get('start_date'), course.get('end_date'));

  ViewCreditHours.propTypes = {
    course: PropTypes.instanceOf(Map),
    modalClick: PropTypes.func,
    programId: PropTypes.string,
  };

  return (
    <Modal show={true} dialogClassName="model-956" centered>
      <Modal.Body>
        <div className="">
          <div className="border-bottom mb-2">
            <div className="d-flex justify-content-between mb-2 ">
              <p className="mb-1 bold">{t('credit_hours_details')}</p>
              <Button variant="outline-primary" className="f-14" size="sm" onClick={modalClick}>
                {t('student_grouping.close')}
              </Button>
            </div>
          </div>
          <div className="row pt-2">
            <div className="col-md-12 pb-3 ">
              <div className="go-wrapper border-bottom">
                <table className="table">
                  <thead className="group_table_top th-change">
                    <tr>
                      <th>
                        <div className="aw-100">{t('configuration.session_type')}</div>
                      </th>
                      <th>
                        <div className="aw-50">{t('program_input.credit_hours')}</div>
                      </th>
                      <th>
                        <div className="aw-100">{t('co_hr_per_cr_hr')}</div>
                      </th>
                      <th>
                        <div className="aw-150">{t('session_per_cr_hr')}</div>
                      </th>
                      <th>
                        <div className="aw-150">{t('per_session_duration')}</div>
                      </th>
                      <th>
                        <div className="aw-100">{t('total_co_hr')}</div>
                      </th>
                      <th>
                        <div className="aw-100">{t('total_sessions')}</div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="go-wrapper-height">
                    {course.get('credit_hours', List()).map((credit, index) => {
                      const convertMinToHours = Math.ceil(credit.get('duration', 0) / 60);
                      const sessionPerHour = credit.get('contact_hours', 0) / convertMinToHours;
                      return (
                        <tr className="tr-change" key={index}>
                          <td>
                            <div className="aw-100">
                              {indVerRename(credit.get('type_name', ''), programId)}{' '}
                            </div>
                          </td>
                          <td>
                            <div className="aw-50">{credit.get('credit_hours', 0)} </div>
                          </td>
                          <td>
                            <div className="aw-100">
                              {credit.get('contact_hours', 0)} {t('hr')}{' '}
                            </div>
                          </td>
                          <td>
                            <div className="aw-150">
                              {sessionPerHour} {t('program_input.types.Session')}{' '}
                            </div>
                          </td>
                          <td>
                            <div className="aw-150">
                              {credit.get('duration', '')} {t('min')}{' '}
                            </div>
                          </td>
                          <td>
                            <div className="aw-100">
                              {credit.get('credit_hours', 0) * credit.get('contact_hours', 0)}{' '}
                              {t('hr')}{' '}
                            </div>
                          </td>
                          <td>
                            <div className="aw-100">
                              {credit.get('credit_hours', 0) * sessionPerHour}{' '}
                              {t('program_input.types.Session')}{' '}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <p className="bold f-16 mb-2">{t('program_input.course_duration')}</p>
          <div className="row pt-2 pl-2">
            <div className="col-md-2  ">
              <p className="bold f-14 mb-2">{t('weeks')} </p>
              <p className="bold f-14 mb-2">
                {weeks}{' '}
                <span className="text-gray">
                  {' '}
                  {`${weeks > 1 ? t('weeks') : t('curriculum_keys.week')}`}
                </span>{' '}
              </p>
            </div>
            <div className="col-md-2 ">
              <p className="bold f-14 mb-2">{t('events.start_date')} </p>
              <p className="bold f-14 mb-2">
                {course.get('start_date', '') !== ''
                  ? moment(course.get('start_date')).format('DD/MM/YYYY')
                  : ''}
              </p>
            </div>
            <div className="col-md-2">
              <p className="bold f-14 mb-2">{t('events.end_date')} </p>
              <p className="bold f-14 mb-2">
                {course.get('end_date', '') !== ''
                  ? moment(course.get('end_date')).format('DD/MM/YYYY')
                  : ''}
              </p>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
}
