import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import readXlsxFile from 'read-excel-file';
import { Modal, But<PERSON>, Table } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import { List } from 'immutable';

import ErrorModal from '../../../StudentGrouping/Modal/ErrorModal';
import * as actions from '../../../../_reduxapi/program_input/action';
import { capitalize } from '../../../InfrastructureManagement/utils';
import {
  selectInvalidCurricullumList,
  selectvalidCurricullumList,
} from '../../../../_reduxapi/program_input/selectors';
import { getURLParams, getLang } from '../../../../utils';
import { t } from 'i18next';
import { exportToExcel } from 'Modules/ProgramInput/utils';
const lng = getLang();

class ImportFromCurricullumFile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      exportShow: true,
      importShow: false,
      importStudent: false,
      importStudentList: false,
      directValid: true,
      chooseNational: '',
      csvRecords: [],
      group1: true,
      group2: false,
      group3: false,
      importAlert: false,
      importTable: false,
      filename: null,
      dataCheckShow: true,
      invalidList: [],
      importFormatError: false,
      importBtnEnable: false,
      programId: getURLParams('_id', true),
      curriculumId: getURLParams('_curriculum_id', true),
    };
  }

  handleChange = (event) => {
    if (
      event.target.files[0].type !==
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      this.setState({ importFormatError: true });
    } else {
      this.setState({ importFormatError: false, tempMismatch: false });
      this.getRecords(event.target.files[0]);
    }
  };

  handleChangeData = () => {
    this.setState({
      filename: null,
      csvRecords: [],
      importBtnEnable: false,
      tempMismatch: false,
    });
  };

  sampleData = () => {
    const sampleData = [
      {
        [t('program_input.sample_data_headers.Program_Name')]: '',
        [t('program_input.sample_data_headers.Program_Code')]: '',
        [t('program_input.sample_data_headers.Curriculum_Name')]: '',
        [t('program_input.sample_data_headers.Hour_As')]: '',
        [t('program_input.sample_data_headers.Set_Value_As')]: '',
        [t('program_input.sample_data_headers.Program_Duration_Start_At')]: '',
        [t('program_input.sample_data_headers.Program_Duration_End_At')]: '',
      },
    ];

    exportToExcel(sampleData, 'SampleCurriculum');
  };
  async getRecords(csvFileObject) {
    const map = {
      Program_Name: 'Program_Name',
      Program_Code: 'Program_Code',
      Curriculum_Name: 'Curriculum_Name',
      Hour_As: 'Hour_As',
      Set_Value_As: 'Set_Value_As',
      Program_Duration_Start_At: 'Program_Duration_Start_At',
      Program_Duration_End_At: 'Program_Duration_End_At',
    };
    var filename = csvFileObject.name;
    let Curriculum = [
      'Hour_As',
      'Set_Value_As',
      'Program_Duration_Start_At',
      'Program_Duration_End_At',
    ];

    this.setState({ filename });
    await readXlsxFile(csvFileObject, { map }).then(({ rows }) => {
      if (rows[0] === undefined) {
        this.setState({ tempMismatch: true });
        return true;
      }
      let tempMismatch = Object.keys(rows[0]).filter((element) => Curriculum.includes(element));
      if (tempMismatch.length === 0) {
        this.setState({ tempMismatch: true });
        return true;
      }

      this.setState({ csvRecords: rows, importBtnEnable: true });
    });
  }

  afterImport = () => {
    const { ImportCurricullumCsvCheck } = this.props;
    const { csvRecords } = this.state;
    this.setState({
      dataCheckShow: false,
    });
    let data = csvRecords.map((row) => {
      return {
        Program_Name:
          String(capitalize(row.Program_Name)) === 'undefined'
            ? ''
            : String(capitalize(row.Program_Name)).trim(),
        Program_Code:
          String(capitalize(row.Program_Code)) === 'undefined'
            ? ''
            : String(capitalize(row.Program_Code)).trim(),
        Curriculum_Name:
          String(capitalize(row.Curriculum_Name)) === 'undefined'
            ? ''
            : String(capitalize(row.Curriculum_Name)).trim(),
        Hour_As: String(row.Hour_As) === 'undefined' ? '' : String(row.Hour_As).trim(),
        Set_Value_As:
          String(row.Set_Value_As) === 'undefined' ? '' : String(row.Set_Value_As).trim(),
        Program_Duration_Start_At:
          row.Program_Duration_Start_At === undefined ? '' : row.Program_Duration_Start_At,
        Program_Duration_End_At:
          row.Program_Duration_End_At === undefined ? '' : row.Program_Duration_End_At,
      };
    });
    let body = {
      curriculum: data,
    };

    ImportCurricullumCsvCheck(
      `digi_curriculum/data_check_curriculum`,
      body,
      () => {
        this.inValidDataCheck();
      },
      () => {
        this.validDataCheck();
      }
    );
  };

  validDataCheck = () => {
    this.props.closed(false);
  };
  fileName = () => {
    this.setState({
      filename: null,
      tempMismatch: false,
    });
  };

  importContinue = () => {
    const { ValidCurricullumList, ImportCurricullumCsv } = this.props;

    if (ValidCurricullumList.size === 0) {
      this.props.closed(false);
    }
    if (ValidCurricullumList.size > 0) {
      let listData = this.props.ValidCurricullumList && this.props.ValidCurricullumList.toJS();

      let data = listData.map((row) => {
        return {
          Program_Name: String(capitalize(row.data.Program_Name)).trim(),
          Program_Code: String(capitalize(row.data.Program_Code)).trim(),
          Curriculum_Name: String(capitalize(row.data.Curriculum_Name)).trim(),
          Hour_As: String(row.data.Hour_As).trim(),
          Set_Value_As: String(row.data.Set_Value_As).trim(),
          Program_Duration_Start_At: row.data.Program_Duration_Start_At,
          Program_Duration_End_At: row.data.Program_Duration_End_At,
        };
      });
      let body = {
        curriculum: data,
      };

      ImportCurricullumCsv(`digi_curriculum/import_curriculum`, body, () => {
        this.props.closed(false);
      });
    }
  };
  importListBack = () => {
    this.setState({
      dataCheckShow: true,
      directValid: true,
    });
  };
  exportList = () => {
    let listData = this.props.InValidCurricullumList && this.props.InValidCurricullumList.toJS();

    let data = listData.map((row) => {
      return {
        Program_Name: row.data.Program_Name,
        Program_Code: row.data.Program_Code,
        Curriculum_Name: row.data.Curriculum_Name,
        Hour_As: row.data.Hour_As,
        Set_Value_As: row.data.Set_Value_As,
        Program_Duration_Start_At: row.data.Program_Duration_Start_At,
        Program_Duration_End_At: row.data.Program_Duration_End_At,
      };
    });
    this.setState({ invalidList: data });

    exportToExcel(data, 'InValidCurriculumList');
  };
  cancelBtn = () => {
    this.setState(
      {
        dataCheckShow: false,
      },
      () => {
        this.props.closed(false);
      }
    );
  };

  inValidDataCheck = () => {
    this.setState({ directValid: false });
  };
  render() {
    const {
      filename,
      csvRecords,
      directValid,
      dataCheckShow,
      importFormatError,
      tempMismatch,
      importBtnEnable,
    } = this.state;
    const { InValidCurricullumList, ValidCurricullumList } = this.props;

    return (
      <div className="main pt-3 pb-5 bg-white">
        <Modal show={dataCheckShow} dialogClassName="model-800">
          <div className="container">
            <p className="f-20 mb-2 pt-3">
              {' '}
              <Trans i18nKey={'import_curriculum'}></Trans>{' '}
            </p>
          </div>

          <Modal.Body>
            <div className="model-main bg-gray pl-3 border-radious-8">
              <p className="f-14 mb-2 pt-2 text-gray">
                {' '}
                <Trans i18nKey={'select_appropriate'}></Trans>
              </p>

              <div className="row pb-5">
                <div className="col-md-5">
                  <b className="f-16 mb-4 pt-3">
                    <Trans i18nKey={'select_a_file'}></Trans> <br></br>
                    <small>
                      {' '}
                      <Trans i18nKey={'accepted_formats'}></Trans>{' '}
                    </small>
                  </b>
                  {filename === null ? (
                    ''
                  ) : (
                    <div className="bg-lightdark border-radious-8 w-75 ">
                      <b className=" pl-2 pr-2 f-14" onClick={this.handleChangeData}>
                        {filename.length > 15 ? filename.substring(0, 4) + '...' : filename}{' '}
                        <i
                          className="fa fa-times remove_hover import_remove_icon"
                          aria-hidden="true"
                        ></i>
                      </b>
                    </div>
                  )}
                </div>

                <div className="col-md-7">
                  <b className="pl-5">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary  import-padding border-radious-8"
                    >
                      <Trans i18nKey={'browse'}></Trans>{' '}
                      <input
                        id="fileUpload"
                        type="file"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        value=""
                        onChange={this.handleChange}
                      />
                    </label>
                  </b>

                  <Link>
                    <b className="pl-5 text-blue" onClick={() => this.sampleData()}>
                      {' '}
                      <i className="fa fa-download pr-2" aria-hidden="true"></i>
                      <Trans i18nKey={'download_template'}></Trans>
                    </b>
                  </Link>
                </div>
              </div>

              <div className="border-radious-8 bg-lightgray">
                <div className="row d-flex justify-content-center">
                  <div className="col-md-11 pt-5 ">
                    <div className="p-2 bg-lightdark border-radious-8">
                      <Table className="">
                        <thead>
                          <tr className="no-border">
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_code'}></Trans>{' '}
                              </Button>
                            </th>

                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'Curriculum_Name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'Hour_As'}></Trans>
                              </Button>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {csvRecords &&
                            csvRecords
                              .filter((val, i) => i < 3)
                              .map((item, i) => {
                                return (
                                  <tr className="border-dark-import" key={i}>
                                    <td className="border-left-import">{item.Program_Name}</td>
                                    <td className="border-left-import">{item.Program_Code}</td>

                                    <td className="border-left-import">{item.Curriculum_Name}</td>
                                    <td className="border-left-import">{item.Hour_As}</td>
                                  </tr>
                                );
                              })}
                        </tbody>
                      </Table>
                    </div>
                  </div>

                  <div className="col-md-11 pt-2 pb-2">
                    <b className="float-right f-14">
                      {csvRecords.length} <Trans i18nKey={'rows_import'}></Trans>{' '}
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <div className={`${lng === 'ar' ? 'pl-4' : 'pr-4'}`}>
              <p className="text-blue mb-0 remove_hover" onClick={this.cancelBtn}>
                {' '}
                <Trans i18nKey={'cancel'}></Trans>{' '}
              </p>
            </div>

            <div className="pr-2">
              <Button
                variant={importBtnEnable && csvRecords.length > 0 ? 'primary' : 'secondary'}
                disabled={importBtnEnable && csvRecords.length > 0 ? false : true}
                onClick={this.afterImport}
              >
                <Trans i18nKey={'import'}></Trans>
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {!directValid && (
          <Modal show={true} size="lg">
            <div className="container">
              <p className="f-20 mb-1 pt-3"> Import Curriculum</p>

              <p className="f-14 mb-2 ">
                {' '}
                {InValidCurricullumList?.size} {t('of')} {csvRecords.length}
                {t('not_imported')}
              </p>
            </div>

            <Modal.Body>
              <div className="pb-1">
                <p className="f-16 mb-2">
                  {' '}
                  <Trans i18nKey={'data_check'}></Trans>
                </p>
                <div className="row">
                  <div className="col-md-6">
                    <p className="f-14 mb-2">
                      {' '}
                      <Trans i18nKey={'list_error_entity'}></Trans>
                    </p>
                  </div>
                  <p className="f-14 mb-2 float-right text-blue" onClick={() => this.exportList()}>
                    <Trans i18nKey={'export_entry'}></Trans>
                  </p>
                </div>

                <div className="go-wrapper">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>
                          <div className="aw-75">
                            {' '}
                            <Trans i18nKey={'s_no'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-100">
                            {' '}
                            <Trans i18nKey={'program_name'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-200">
                            {' '}
                            <Trans i18nKey={'program_code'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-300">
                            {' '}
                            <Trans i18nKey={'error_message'}></Trans>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="go-wrapper-height tr-change">
                      {InValidCurricullumList &&
                        InValidCurricullumList.map((item, i) => {
                          return (
                            <tr key={i}>
                              <td>
                                <div className="aw-75">{i + 1}</div>
                              </td>
                              <td>
                                <div className="aw-100">{item.getIn(['data', 'Program_Name'])}</div>
                              </td>
                              <td>
                                <div className="aw-200">{item.getIn(['data', 'Program_Code'])}</div>
                              </td>

                              <td>
                                <div className="aw-300">
                                  {' '}
                                  {item.getIn(['message']).map((message, i) => (
                                    <li key={i}>{message}</li>
                                  ))}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <div className="pr-4">
                <p className="text-blue mb-0 remove_hover" onClick={this.importListBack}>
                  {' '}
                  BACK{' '}
                </p>
              </div>

              <div className="pr-2">
                <Button
                  variant={
                    ValidCurricullumList && ValidCurricullumList.size > 0 ? 'primary' : 'Secondary'
                  }
                  onClick={this.importContinue}
                  disabled={ValidCurricullumList && ValidCurricullumList.size === 0}
                >
                  CONTINUE
                </Button>
              </div>
            </Modal.Footer>
          </Modal>
        )}
        {importFormatError && (
          <div>
            <ErrorModal
              showDetail={importFormatError}
              title={`Error: Invalid file format`}
              content={`You have uploaded an invalid file type. Accepted file formats is .xlsx`}
              filename={this.fileName}
            />
          </div>
        )}

        {tempMismatch && (
          <div>
            <ErrorModal
              showDetail={tempMismatch}
              title={`Template Mismatch`}
              content={`Imported xlsx columns are not matched. Check the sample template.`}
              filename={this.fileName}
            />
          </div>
        )}
      </div>
    );
  }
}

ImportFromCurricullumFile.propTypes = {
  ImportCurricullumCsvCheck: PropTypes.func,
  closed: PropTypes.func,
  ValidCurricullumList: PropTypes.instanceOf(List),
  ImportCurricullumCsv: PropTypes.func,
  InValidCurricullumList: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    InValidCurricullumList: selectInvalidCurricullumList(state),
    ValidCurricullumList: selectvalidCurricullumList(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ImportFromCurricullumFile);
