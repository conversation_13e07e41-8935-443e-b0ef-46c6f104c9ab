import { Checkbox, FormControlLabel } from '@mui/material';
import * as actions from '_reduxapi/global_configuration/actions';
import StaffDisableModal from 'Modules/GlobalConfiguration/modal/StaffDisableModal';
import StaffTypeModal from 'Modules/GlobalConfiguration/modal/StaffTypeModal';
import React, { useState } from 'react';
import { Trans } from 'react-i18next';
import { getLang } from 'utils';
import MButton from 'Widgets/FormElements/material/Button';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { selectDesignations } from '_reduxapi/global_configuration/selectors';
import { List, Map } from 'immutable';
import { t } from 'i18next';
import { AccordionProgramInput } from '../ReusableComponent';
import { checkBoxStyles } from 'Modules/GlobalConfiguration/utils';

const Designation = (props) => {
  const {
    getDesignations,
    designations,
    settingId,
    designationCheck,
    updateDesignation,
    setData,
    count,
    accordionOpen,
    setAccordionOpen,
    setAccordionHeader,
    institutionHeader,

    type,
  } = props;

  const [disable, setDisableOpen] = useState(Map({ show: false, index: '', id: '' }));
  const [addEditOpen, setAddEditOpen] = useState(false);
  const [itemState, setItemState] = useState(Map());

  const classes = checkBoxStyles();
  const handleOpen = (operation, item) => {
    setAddEditOpen(true);
    if (operation === 'edit') {
      setItemState(item);
    } else {
      setItemState(Map());
    }
  };

  const handleOpenDelete = (e, id, index) => {
    setDisableOpen({ show: true, index, id, operation: 'delete' });
  };

  const handleClose = () => {
    setAddEditOpen(false);
  };

  const handleChecked = (e, index) => {
    if (!e.target.checked) {
      setDisableOpen({ show: true, index, id: e.target.value, operation: 'disable' });
    } else {
      designationCheck({
        id: e.target.value,
        settingId,
        index,
        check: e.target.checked,
        headers: institutionHeader,
        type,
      });
    }
  };

  const handleCloseDisable = () => {
    setDisableOpen(false);
  };
  const getDetails = () => {
    return (
      <div className="container">
        <p className={`${getLang() === 'ar' && 'staff_manageRight'} bold ml-2 pl-1 text-left mb-0`}>
          <Trans i18nKey={'userManagement.designations'} />
        </p>

        {!designations.size ? (
          <div className="row digi-tbl-pad pt-3 pl-0 digi-text-justify">
            <div className="col-md-12 btn-description-border text-center p-5">
              <MButton
                variant="outlined"
                color="gray"
                className={'f-16'}
                clicked={() => handleOpen('add')}
              >
                <Trans i18nKey={'userManagement.add_designation'}></Trans>
              </MButton>
            </div>
          </div>
        ) : (
          <div className="p-3">
            {designations.map((role, index) => (
              <div key={index} className="d-flex justify-content-between align-items-center">
                <div className="">
                  <FormControlLabel
                    {...(!role.get('isActive', true) && { classes: { label: classes.label } })}
                    value={role.get('_id', '')}
                    control={<Checkbox color="primary" classes={{ root: classes.root }} />}
                    label={role.get('designationName', '')}
                    labelPlacement="end"
                    onChange={(e) => handleChecked(e, index)}
                    checked={role.get('isActive', true)}
                  />
                </div>
                <div className="">
                  <img
                    src={EditIcon}
                    alt="edit"
                    className="digi-pr-12 remove_hover"
                    onClick={() => handleOpen('edit', role)}
                  />
                  <img
                    src={DeleteIcon}
                    alt="Delete"
                    className="digi-pr-12 remove_hover"
                    onClick={(e) => handleOpenDelete(e, role.get('_id', ''), index)}
                  />
                </div>{' '}
              </div>
            ))}

            <MButton
              className="mb-2 mt-2 text-skyblue bold remove_hover"
              variant="text"
              clicked={handleOpen}
            >
              <Trans i18nKey={'userManagement.add_new_designation'}></Trans>
            </MButton>
          </div>
        )}
      </div>
    );
  };

  const handleClick = () => {
    settingId &&
      !accordionOpen &&
      !designations.size &&
      count !== 0 &&
      getDesignations({ headers: institutionHeader, settingId, type });
    setAccordionOpen();
  };
  return (
    <>
      <AccordionProgramInput
        expanded={accordionOpen || false}
        onClick={handleClick}
        summaryChildren={setAccordionHeader(
          'userManagement.configuration_designation',
          count + ' ' + t('userManagement.designations'),
          !accordionOpen
        )}
        detailChildren={getDetails()}
      />

      <hr />
      {disable['show'] && (
        <StaffDisableModal
          state={itemState}
          disable={disable}
          handleClose={() => handleCloseDisable()}
          designationCheck={designationCheck}
          settingId={settingId}
          designations={designations}
          setDisableOpen={setDisableOpen}
          institutionHeader={institutionHeader}
          updateDesignation={updateDesignation}
          getDesignations={getDesignations}
          type={type}
        />
      )}
      {addEditOpen && (
        <StaffTypeModal
          state={itemState}
          open={addEditOpen}
          handleClose={() => handleClose()}
          updateDesignation={updateDesignation}
          getDesignations={getDesignations}
          settingId={settingId}
          designations={designations}
          setData={setData}
          institutionHeader={institutionHeader}
          type={type}
        />
      )}
    </>
  );
};
Designation.propTypes = {
  getDesignations: PropTypes.func,
  designations: PropTypes.instanceOf(List),
  settingId: PropTypes.string,
  designationCheck: PropTypes.func,
  updateDesignation: PropTypes.func,
  setData: PropTypes.func,
  count: PropTypes.number,
  accordionOpen: PropTypes.bool,
  setAccordionOpen: PropTypes.func,
  setAccordionHeader: PropTypes.func,
  institutionHeader: PropTypes.object,
  type: PropTypes.string,
};
const mapStateToProps = (state) => {
  return {
    designations: selectDesignations(state),
  };
};
export default connect(mapStateToProps, actions)(Designation);
