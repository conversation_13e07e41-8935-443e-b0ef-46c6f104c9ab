import React, { useState } from 'react';
import EmailContent from './EmailContent';
import EmailSettings from './EmailSettings';

const EmailIndex = (props) => {
  const { displayRow, settingId } = props;
  const [emailOpen, setEmailOpen] = useState(false);
  const handleEmailOpen = () => {
    setEmailOpen(true);
  };

  const handleEmailOpenClose = () => {
    setEmailOpen(false);
  };

  return (
    <>
      <EmailContent
        displayRow={displayRow}
        handleEmailOpen={handleEmailOpen}
        handleEmailOpenClose={handleEmailOpenClose}
        emailOpen={emailOpen}
        settingId={settingId}
      />
      <EmailSettings
        displayRow={displayRow}
        handleEmailOpen={handleEmailOpen}
        handleEmailOpenClose={handleEmailOpenClose}
        settingId={settingId}
      />
    </>
  );
};
export default EmailIndex;
