import React from 'react';
import PropTypes from 'prop-types';
import ReactECharts from 'echarts-for-react';
import { Tabs, Tab } from '@mui/material';
import i18n from 'i18next';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import { styled } from '@mui/material/styles';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import Delete from 'Assets/delete.svg';
import Share from 'Assets/share.svg';
import Edit from 'Assets/edit_pencil.svg';
import Chip from '@mui/material/Chip';

import { useStyles } from '../../ProgramInput/Design/program_input/Utils';

import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const IOSSwitch = styled((props) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 42,
  height: 26,
  padding: 0,
  '& .MuiSwitch-switchBase': {
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&.Mui-checked': {
      transform: 'translateX(16px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        backgroundColor: theme.palette.mode === 'dark' ? '#2ECA45' : '#65C466',
        opacity: 1,
        border: 0,
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-focusVisible .MuiSwitch-thumb': {
      color: '#33cf4d',
      border: '6px solid #fff',
    },
    '&.Mui-disabled .MuiSwitch-thumb': {
      color: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600],
    },
    '&.Mui-disabled + .MuiSwitch-track': {
      opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 22,
    height: 22,
  },
  '& .MuiSwitch-track': {
    borderRadius: 26 / 2,
    backgroundColor: theme.palette.mode === 'light' ? '#E9E9EA' : '#39393D',
    opacity: 1,
    transition: theme.transitions.create(['background-color'], {
      duration: 500,
    }),
  },
}));

const handleDelete = () => {
  console.info('You clicked the delete icon.');
};

const flexStyle = {
  display: 'flex',
};
const axisStyle = {
  fontSize: '18px',
  fontFamily: 'roboto',
};
function TotalDepartments() {
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const classes = useStylesFunction();

  const [tabValue, setValue] = React.useState('activeTab');
  const options = {
    legend: {
      data: ['Departments', 'Subjects'],
    },
    dataZoom: [
      {
        type: 'inside',
      },
      {
        type: 'slider',
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'category',
      style: flexStyle,
      data: ['Admin departments', 'DENTAL Program', 'PHARMACY Program', 'OPTHAMOLOGY Program'],

      nameGap: 30,
      nameLocation: 'center',
      nameTextStyle: axisStyle,
    },
    yAxis: {
      type: 'value',
      nameGap: 30,
      nameTextStyle: axisStyle,
      data: [10, 20, 30, 40, 50],
      // name: `${i18n.t('role_management.modules_list.Programs')}`,

      //<Trans i18nKey={'modules_list.Programs'}></Trans>
    },
    series: [
      {
        name: 'Departments',
        data: [8, 26, 19, 45, 43],
        type: 'bar',
        barMaxWidth: 30,
        itemStyle: { color: '#3E95EF' },
        lineStyle: {
          color: '#3E95EF',
          barMaxWidth: 30,
        },
      },
      {
        name: 'Subjects',
        data: [18, 6, 19, 25, 23],
        type: 'bar',
        barMaxWidth: 30,
        itemStyle: { color: '#3E95EF' },
        lineStyle: {
          color: '#3E95EF',
          barMaxWidth: 30,
        },
      },
    ],
  };

  return (
    <div className="container">
      <div className="row align-items-center mt-3 mb-2">
        <div className="col-md-6 col-xl-3 col-lg-3 col-6 col-sm-6 mt-3">
          <div className="digi-border-gray rounded pt-4 pb-4 pl-3 pr-3">
            <p className="f-18 font-weight-500 mb-1 mt-1">
              <Trans i18nKey={'departments_subjects.total_departments_subjects'}></Trans>
            </p>
            <div className="digi-border-gray rounded digi-mt-12 digi-mb-12">
              <p className="float-left f-20 digi-pl-12  mb-2 mt-2 font-weight-normal digi-gray-neutral">
                <Trans i18nKey={'departments_subjects.department_type'}></Trans>
              </p>
              <p className="float-right mb-2 mt-2 f-20 digi-pr-12">25</p>
              <div className="clearfix"></div>
            </div>
            <div className="digi-border-gray rounded digi-mt-12 digi-mb-16">
              <p className="float-left f-20 digi-pl-12  mb-2 mt-2 font-weight-normal digi-gray-neutral">
                <Trans i18nKey={'the_subjects'}></Trans>
                Subjects
              </p>
              <p className="float-right mb-2 mt-2 f-20 digi-pr-12">50</p>
              <div className="clearfix"></div>
            </div>
          </div>
        </div>

        <div className="col-md-6 col-xl-3 col-lg-3 col-6 col-sm-6 mt-3">
          <div className="digi-border-gray rounded pt-4 pb-4 pl-3 pr-3">
            <p className="f-18 font-weight-500 mb-1 mt-1">
              {' '}
              <Trans i18nKey={'departments_subjects.department_type'}></Trans>
            </p>
            <div className="digi-border-gray rounded digi-mt-12 digi-mb-12">
              <p className="float-left f-20 digi-pl-12  mb-2 mt-2 font-weight-normal digi-gray-neutral">
                <Trans i18nKey={'user_management.academic'}></Trans>
              </p>
              <p className="float-right mb-2 mt-2 f-20 digi-pr-12">10</p>
              <div className="clearfix"></div>
            </div>
            <div className="digi-border-gray rounded digi-mt-12 digi-mb-16">
              <p className="float-left f-20 digi-pl-12  mb-2 mt-2 font-weight-normal digi-gray-neutral">
                <Trans i18nKey={'departments_subjects.admin'}></Trans>
              </p>
              <p className="float-right mb-2 mt-2 f-20 digi-pr-12">05</p>
              <div className="clearfix"></div>
            </div>
          </div>
        </div>

        <div className="col-md-12 col-xl-6 col-lg-6 col-12 col-sm-12 mt-3">
          <div className="digi-border-gray rounded">
            <ReactECharts
              style={{ width: '100%', height: '200px' }}
              option={options}
              className="echarts-height-200"
            />
          </div>
        </div>
      </div>

      <div className="d-flex border-bottom">
        <Tabs
          value={tabValue}
          indicatorColor="primary"
          textColor="primary"
          onChange={handleChange}
          aria-label="disabled tabs example"
          fullWidth={true}
        >
          <Tab label={`${i18n.t('user_management.academic')} (01)`} value={'activeTab'} />
          <Tab label={`${i18n.t('admin')} (11)`} value={'archivedTab'} />
        </Tabs>
      </div>
      <div className="program_table">
        {tabValue === 'activeTab' && (
          <div>
            <div className="row align-items-center">
              <div className="col-md-6 col-xl-6 col-lg-6 col-6">
                <div className="row mt-3">
                  <div className="col-md-12 col-xl-9 col-lg-9 col-12">
                    <div className="d-flex justify-content-between digi-border-gray rounded align-items-center">
                      <p className="mb-0 pl-2">Show shared departments and subjects </p>
                      <div className="">
                        <FormControlLabel
                          control={<IOSSwitch sx={{ m: 1 }} defaultChecked />}
                          label=""
                          className="mb-0 mr-1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-md-6 col-xl-6 col-lg-6 col-6">
                <div className="row float-xl-right align-items-center">
                  <div className="col-md-4 col-xl-6 col-8 col-lg-6 col-sm-4">
                    <div className="">
                      <MaterialInput
                        elementType={'materialSearch'}
                        placeholder={i18n.t('user_management.Search')}
                      />
                    </div>
                  </div>
                  <div className="col-md-8 col-xl-6 col-8 col-lg-6 col-sm-8 mt-3 pt-1">
                    <MButton variant="outlined" className="digi-blue-button pb-2 pt-2 f-16">
                      + <Trans i18nKey={'department_restructured.add_new_department'}></Trans>
                    </MButton>
                  </div>
                </div>
              </div>
            </div>
            <div className="pt-3 pb-5">
              <div className="container">
                <div className="d-flex justify-content-start pb-3">
                  <div className={classes.heading}>
                    Department{' '}
                    <span className="digi-vertical-middle">
                      <ArrowDropDownIcon />
                    </span>
                  </div>
                  <div className={classes.secondaryHeading}>
                    Program
                    <span className="digi-vertical-middle">
                      <ArrowDropDownIcon />
                    </span>
                  </div>
                  <div className={classes.subjectHeading}>
                    Subjects{' '}
                    <span className="digi-vertical-middle">
                      <ArrowDropDownIcon />
                    </span>
                  </div>

                  <div className={classes.thirdHeading}>
                    Shared with / from
                    <span className="digi-vertical-middle">
                      <ArrowDropDownIcon />
                    </span>
                  </div>

                  <div className={classes.secondaryHeading}>Action</div>
                </div>

                <Accordion>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                  >
                    <Typography className={classes.heading}>Anatomy</Typography>
                    <Typography className={classes.programHeading}>Medicine</Typography>
                    <Typography className={classes.programShared}>
                      Shared from{' '}
                      <span className="digi-course-bg digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                        Nursing
                      </span>
                    </Typography>

                    <Typography className={classes.actionShared}>
                      <span className="digi-Shared-bg digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                        Shared
                      </span>
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <div className="p-1 w-100">
                      <div className="d-flex justify-content-end bg-lightgray p-3">
                        <p className={classes.actionSub}>Subjects</p>
                        <p className={classes.addSubject}>
                          {' '}
                          <span className="font-weight-bold digi-blue f-17">+ Add Subject</span>
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub1}>Sub1</p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="delete" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="delete" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="delete" />
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub1}>Sub1</p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="delete" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="delete" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="delete" />
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub1}>Sub1</p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="delete" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="delete" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="delete" />
                        </p>
                      </div>
                    </div>
                  </AccordionDetails>
                </Accordion>

                <Accordion className="digi-mt-12 digi-p-4">
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                  >
                    <Typography className={classes.heading}>Anatomy</Typography>
                    <Typography className={classes.programHeading}>Medicine</Typography>
                    <Typography className={classes.programShared}>
                      Shared from{' '}
                      <span className="digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                        <Chip label="Medicine" variant="outlined" onDelete={handleDelete} />
                      </span>
                    </Typography>

                    <Typography className={classes.actionShared}>
                      <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                      <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                      <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                      {/* <span className="digi-Shared-bg digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                        Shared
                      </span> */}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <div className="p-1 w-100">
                      <div className="d-flex justify-content-end bg-lightgray p-3">
                        <p className={classes.actionSub}>Subjects</p>
                        <p className={classes.addSubject}>
                          {' '}
                          <span className="font-weight-bold digi-blue f-17">+ Add Subject</span>
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub1}>Sub1</p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub2}>Sub2</p>
                        <p className={classes.shareFrom}>
                          Shared From
                          <span className="digi-course-bg digi-ml-4 digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                            Nursing / Department 01
                          </span>
                        </p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub2}>Sub3</p>
                        <p className={classes.shareFrom}>
                          Shared From
                          <span className="digi-ml-4 digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                            <Chip
                              label="Nursing / Department 04"
                              variant="outlined"
                              onDelete={handleDelete}
                            />
                          </span>
                        </p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                        </p>
                      </div>
                    </div>
                  </AccordionDetails>
                </Accordion>

                <Accordion className="digi-mt-12 digi-p-4">
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                  >
                    <Typography className={classes.heading}>Anatomy</Typography>
                    <Typography className={classes.programHeading}>Medicine</Typography>
                    <Typography className={classes.programShared}>
                      Shared from{' '}
                      <span className="digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                        <Chip label="Medicine" variant="outlined" onDelete={handleDelete} />
                      </span>
                    </Typography>

                    <Typography className={classes.actionShared}>
                      <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                      <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                      <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                      {/* <span className="digi-Shared-bg digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                        Shared
                      </span> */}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <div className="p-1 w-100">
                      <div className="d-flex justify-content-end bg-lightgray p-3">
                        <p className={classes.actionSub}>Subjects</p>
                        <p className={classes.addSubject}>
                          {' '}
                          <span className="font-weight-bold digi-blue f-17">+ Add Subject</span>
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub1}>Sub1</p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub2}>Sub2</p>
                        <p className={classes.shareFrom}>
                          Shared From
                          <span className="digi-course-bg digi-ml-4 digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                            Nursing / Department 01
                          </span>
                        </p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                        </p>
                      </div>

                      <div className="d-flex justify-content-end accordion_hover">
                        <p className={classes.actionSub2}>Sub3</p>
                        <p className={classes.shareFrom}>
                          Shared From
                          <span className="digi-ml-4 digi-pl-12 digi-pr-12 digi-pt-4 digi-pb-4 rounded">
                            <Chip
                              label="Nursing Program"
                              variant="outlined"
                              onDelete={handleDelete}
                            />
                          </span>
                        </p>
                        <p className={classes.addIcons}>
                          <img className="digi-pr-16 remove_hover" src={Share} alt="Share" />
                          <img className="digi-pr-16 remove_hover" src={Edit} alt="Edit" />
                          <img className="digi-pr-16 remove_hover" src={Delete} alt="Delete" />
                        </p>
                      </div>
                    </div>
                  </AccordionDetails>
                </Accordion>
              </div>
            </div>
          </div>
        )}

        {tabValue === 'archivedTab' && <div> Test2</div>}
      </div>
    </div>
  );
}
TotalDepartments.propTypes = {
  children: PropTypes.array,
};
export default TotalDepartments;
