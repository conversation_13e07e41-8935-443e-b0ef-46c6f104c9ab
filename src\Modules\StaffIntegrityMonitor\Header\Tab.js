import React, { useContext, useState } from 'react';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { makeStyles } from '@mui/styles';
import LeaderBoard from '../LeaderBoard/LeaderBoard';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { StaffMonitorContext } from '..';
import { recursivelyConstructQueryParams } from 'utils';
import { useSelector } from 'react-redux';
import { selectUserId } from '_reduxapi/Common/Selectors';

const useStylesFunction = makeStyles(() => ({
  root: {
    borderBottom: '1px solid #e5e5e5 !important',
    margin: '0px 20px',
  },
  indicator: {
    backgroundColor: '#297fdc',
  },
}));

function MonitorTab({ yearWithProgram, setYearWithProgram }) {
  const [value, setValue] = useState(0);
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const userId = useSelector(selectUserId);
  const { refreshStatusRef } = useContext(StaffMonitorContext);
  const pageViewType = yearWithProgram.getIn(['primaryData', 'pageViewType'], '');
  const commonQueryParams = `?board=${
    value === 0 ? 'leader' : 'anomaly'
  }&_institution_calendar_id=${yearWithProgram.getIn(
    ['primaryData', 'academicYear', '_id'],
    ''
  )}&from=${yearWithProgram.getIn(['primaryData', 'date', 'from'], '')}&to=${yearWithProgram.getIn(
    ['primaryData', 'date', 'to'],
    ''
  )}&state=${refreshStatusRef.current}&${recursivelyConstructQueryParams({
    programIds: yearWithProgram.getIn(['primaryData', 'program'], List()),
  })}${pageViewType === 'performanceView' ? '&currentUserId=' + userId : ''}`;

  const classes = useStylesFunction();

  return (
    <>
      <Tabs
        value={value}
        onChange={handleChange}
        classes={{
          indicator: classes.indicator,
        }}
        textColor="primary"
        className={classes.root}
        variant="scrollable"
        // scrollButtons={'off'}
      >
        <Tab label={'Leader Board'} value={0} />

        {/* <Tab label={'Anomaly Board'} value={1} /> */}
      </Tabs>
      {value === 0 && (
        <LeaderBoard
          commonQueryParams={commonQueryParams}
          yearWithProgram={yearWithProgram}
          setYearWithProgram={setYearWithProgram}
        />
      )}
      {value === 1 && <div />}
    </>
  );
}
MonitorTab.propTypes = {
  yearWithProgram: PropTypes.instanceOf(Map),
  setYearWithProgram: PropTypes.func,
};

export default MonitorTab;
