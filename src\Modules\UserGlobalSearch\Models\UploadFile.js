import React, { useState } from 'react';
import PropTypes from 'prop-types';

import { Trans, useTranslation } from 'react-i18next';
import CloseIcon from '@mui/icons-material/Close';
import { Button, Chip, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';

import { fromJS, List, Map } from 'immutable';
import { useDispatch } from 'react-redux';
import { setData } from '_reduxapi/leave_management/actions';
import { getShortString } from 'Modules/Shared/v2/Configurations';
// import { uploadRemarksFile } from '_reduxapi/UserGlobalSearch/action';
import useFileUpload from 'Hooks/useFileUpload';

//----------------------------------UI Utils Start--------------------------------------------------
const dialogSX = {
  boxShadow: '0',
  borderRadius: '8px',
  width: '450px',
};
const ChipSX = {
  backgroundColor: '#F9FAFB',
  color: '#4B5563',
};
//----------------------------------UI Utils End----------------------------------------------------
//----------------------------------JS Utils Start--------------------------------------------------
const allowedImageTypes = ['image/png', 'image/jpeg'];
const allowedVideoTypes = ['video/mp4'];

export const getAttachmentName = (url) => {
  let fileName = '';

  if (url instanceof File) {
    fileName = url.name;
  } else {
    const urlParts = url.split('/');
    fileName = urlParts[urlParts.length - 1];
    let dynamicNumberRegex = /^\d+-/;
    fileName = fileName.replace(dynamicNumberRegex, '');
  }

  return fileName;
};

//----------------------------------JS Utils End----------------------------------------------------
//----------------------------------custom hooks start----------------------------------------------
const useUploadFileList = ({ isOpen, setRemarks }) => {
  const studentRemark = isOpen.get('studentRemark', Map());
  const studentRemarkIndex = isOpen.get('studentRemarkIndex', 0);

  const dispatch = useDispatch();
  const { t: translate } = useTranslation();
  const attachments = studentRemark.get('attachments', List());
  const [files, setFiles] = useState(attachments);

  const handleDrop = (event) => {
    event.preventDefault();
    const droppedFiles = Array.from(event.dataTransfer.files);

    updateFiles(event, droppedFiles);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleFileUpdate = (event) => {
    const selectedFiles = event.target.files;
    const newFiles = Array.from(selectedFiles);
    updateFiles(event, newFiles);
  };

  const handleRemoveFile = (index) => (event) => {
    event.stopPropagation();
    setFiles(files.delete(index));
    setRemarks((prev) =>
      prev.deleteIn(['studentDisciplinaryRemarks', studentRemarkIndex, 'attachments', index])
    );
  };

  const updateFiles = (event, newFiles) => {
    const maxSize = 50 * 1024 * 1024; // 50MB in bytes
    const maxImageCount = 5;
    let updatedFiles = List();
    let errorMsg = '';

    const totalFileSize = newFiles.reduce(
      (accumulator, currentValue) => accumulator + currentValue.size,
      0
    );
    const exceedsIndividualSizeLimit = newFiles.some((file) => file.size > maxSize);
    const exceedsTotalSizeLimit = totalFileSize > maxSize;

    if (exceedsTotalSizeLimit) {
      errorMsg = translate('user_global_search.uploadFile.totalFileSizeExceeded');
      event.target.value = null;
    } else if (newFiles.length > maxImageCount || files.size >= maxImageCount) {
      errorMsg = translate('user_global_search.uploadFile.maxFileCountExceeded');
      event.target.value = null;
    } else if (exceedsIndividualSizeLimit) {
      errorMsg = translate('user_global_search.uploadFile.unsupportedFileSize');
      event.target.value = null;
    } else {
      newFiles.forEach((file) => {
        if (allowedImageTypes.includes(file.type) || allowedVideoTypes.includes(file.type)) {
          updatedFiles = updatedFiles.push(file);
          event.target.value = null;
        } else {
          errorMsg = translate('user_global_search.uploadFile.unsupportedFileType');
          event.target.value = null;
        }
      });
    }

    if (errorMsg) {
      dispatch(setData(Map({ message: errorMsg })));
    }
    const allFiles = files.concat(updatedFiles);
    setFiles(allFiles);
  };

  return {
    files,
    handleFileUpdate,
    handleDragOver,
    handleDrop,
    handleRemoveFile,
  };
};
//----------------------------------custom hooks end------------------------------------------------
//----------------------------------componentStart--------------------------------------------------
const UploadFileCard = ({ children, handleDrop, handleDragOver }) => {
  return (
    <label
      htmlFor="file-upload"
      className="border-dashed-dodger-blue p-4 d-flex align-items-center justify-content-center cursor-pointer"
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      {children}
      <div className="text-center">
        <div className="f-16 alphabet-icon-text mb-1">
          <Trans i18nKey={'user_global_search.uploadFile.dragDropLabel'} />
        </div>
        <div className="f-10 text-NewLightgray">
          <Trans i18nKey={'user_global_search.uploadFile.fileTypesLabel'} />
        </div>
      </div>
    </label>
  );
};
UploadFileCard.propTypes = {
  children: PropTypes.node,
  handleDrop: PropTypes.func.isRequired,
  handleDragOver: PropTypes.func.isRequired,
};

const FileUploadInput = ({ onChange }) => {
  return <input type="file" id="file-upload" className="d-none" multiple onChange={onChange} />;
};
FileUploadInput.propTypes = {
  onChange: PropTypes.func.isRequired,
};

const UploadDialogActions = ({ children }) => {
  return (
    <div className="row m-0 w-100">
      <div className="col-6 pl-0 pr-2">{children[0]}</div>
      <div className="col-6 pr-0 pl-2">{children[1]}</div>
    </div>
  );
};
UploadDialogActions.propTypes = {
  children: PropTypes.node,
};

const CancelButton = ({ onClick }) => {
  return (
    <Button
      onClick={onClick}
      variant="outlined"
      fullWidth
      className="text-secondary border-secondary"
    >
      <Trans i18nKey={'user_global_search.cancelButton'} />
    </Button>
  );
};
CancelButton.propTypes = {
  onClick: PropTypes.func.isRequired,
};

const UploadButton = ({ onClick, disabled }) => {
  return (
    <Button
      variant="contained"
      fullWidth
      disabled={disabled}
      className={`${disabled ? '' : 'appBar-bg'}`}
      onClick={onClick}
    >
      <Trans i18nKey={'user_global_search.uploadButton'} />
    </Button>
  );
};
UploadButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  disabled: PropTypes.bool.isRequired,
};

const UploadedFileChip = ({ files, handleRemoveFile }) => {
  return (
    <div className="d-flex flex-wrap">
      {files.map((uploadedFile, fileIndex) => {
        const attachmentName = getAttachmentName(uploadedFile);
        const shortAttachmentName = getShortString(attachmentName, 16);

        return (
          <Chip
            key={fileIndex}
            label={<span>{shortAttachmentName}</span>}
            size="small"
            className="rounded-1 px-2 py-3 cursor-pointer"
            sx={ChipSX}
            deleteIcon={<CloseIcon className="text-slate-gray" />}
            onDelete={handleRemoveFile(fileIndex)}
          />
        );
      })}
    </div>
  );
};
UploadedFileChip.propTypes = {
  files: PropTypes.instanceOf(List).isRequired,
  handleRemoveFile: PropTypes.func.isRequired,
};

//----------------------------------componentEnd----------------------------------------------------

const UploadFile = ({ isOpen, onClose, onAddFile, setRemarks }) => {
  const dispatch = useDispatch();
  const { upload } = useFileUpload();
  const UploadListHook = useUploadFileList({ isOpen, setRemarks });
  const files = UploadListHook.files;
  const handleDrop = UploadListHook.handleDrop;
  const handleDragOver = UploadListHook.handleDragOver;
  const handleFileUpdate = UploadListHook.handleFileUpdate;
  const handleRemoveFile = UploadListHook.handleRemoveFile;
  const studentRemarkIndex = isOpen.get('studentRemarkIndex', 0);

  const handleClose = () => {
    onClose(Map());
  };
  const onSuccessCallBack = (getFiles) => {
    onAddFile(getFiles, studentRemarkIndex);
  };
  const handleUpdate = async () => {
    if (files.size === 0) {
      handleClose();
      return;
    }
    try {
      const uploadedFilesData = await upload({
        files: files,
        component: 'documents',
      });
      if (uploadedFilesData.length > 0) {
        const fileData = uploadedFilesData[0];
        const unSignedUrl = fileData.unSignedUrl;
        onSuccessCallBack(fromJS([unSignedUrl]));
      }
      handleClose();
    } catch (err) {
      dispatch(setData(Map({ isLoading: false, message: err.message })));
    }
  };

  return (
    <Dialog fullWidth open={isOpen.size !== 0} PaperProps={{ sx: dialogSX }}>
      <DialogTitle className="pt-4 pb-0 px-4">
        <div className="f-18">
          <Trans i18nKey={'user_global_search.uploadFile.uploadFileTitle'} />
        </div>
        <div className="f-10 text-NewLightgray mb-3">
          <Trans i18nKey={'user_global_search.uploadFile.fileTypesLabel'} />
        </div>
        <UploadFileCard handleDrop={handleDrop} handleDragOver={handleDragOver}>
          <FileUploadInput onChange={handleFileUpdate} />
        </UploadFileCard>
      </DialogTitle>
      <DialogContent className="px-4 py-3">
        <UploadedFileChip files={files} handleRemoveFile={handleRemoveFile} />
      </DialogContent>
      <DialogActions className="pt-0 pb-4 px-4">
        <UploadDialogActions>
          <CancelButton onClick={handleClose} />
          <UploadButton onClick={handleUpdate} />
        </UploadDialogActions>
      </DialogActions>
    </Dialog>
  );
};
UploadFile.propTypes = {
  isOpen: PropTypes.instanceOf(Map).isRequired,
  onClose: PropTypes.func.isRequired,
  onAddFile: PropTypes.func.isRequired,
  setRemarks: PropTypes.func.isRequired,
};

export default UploadFile;
