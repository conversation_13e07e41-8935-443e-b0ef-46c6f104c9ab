import React, { useState, useEffect, Suspense } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/session_tracking_report/action';
import MButton from 'Widgets/FormElements/material/Button';
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined';
import CloseIcon from '@mui/icons-material/Close';
import SettingsIcon from '@mui/icons-material/Settings';
import TableModule from './tableModule';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { Menu, ListItemButton, List as ListView, ListItemText } from '@mui/material';
import KeyboardArrowDownOutlinedIcon from '@mui/icons-material/KeyboardArrowDownOutlined';
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined';
import SessionFilter, { getFormattedDateRange, getInstitutionData } from './SessionHeader';
import { Map, List, fromJS } from 'immutable';
import ReactECharts from 'echarts-for-react';
import {
  getCurricularDelivery,
  getAllDelivery,
  getDeliveryOption,
  getModeOption,
  getStatus,
  convertTime12to24,
} from '../utils';
import {
  selectSessionReport,
  selectProgramCourseList,
} from '_reduxapi/session_tracking_report/selectors';
import moment from 'moment';
import { capitalize } from '../../../Modules/InfrastructureManagement/utils';
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded';
import RectangleRoundedIcon from '@mui/icons-material/RectangleRounded';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { exportExcel } from './reportExport';
import html2canvas from 'html2canvas';
import { selectInstitutionCalendar } from '_reduxapi/Common/Selectors';

const ProgramFilterModal = React.lazy(() => import('./modal/programFilterModal'));
const CustomFilterModal = React.lazy(() => import('./modal/customFilterModal'));

const defaultPgm = {
  term: `all`,
  year: `all`,
  level: `all`,
  courseId: `all`,
};

const ExportIconButton = ({ clicked }) => {
  return (
    <MButton
      variant="contained"
      color="primary"
      size="small"
      className="ml-2 export-btn"
      sx={{ minWidth: 0, padding: '6px' }}
      clicked={clicked}
    >
      <FileUploadOutlinedIcon fontSize="small" />
    </MButton>
  );
};

const ChartFilters = ({ data, institutionCalendarLists, statusWithPermission }) => {
  const institutionData = getInstitutionData(institutionCalendarLists);
  const iCalendarId =
    data.get('institutionCalendarId', '') !== ''
      ? data.get('institutionCalendarId', '')
      : institutionData?.[0]?.value;
  const academicYear = institutionData.find((data) => data.value === iCalendarId)?.name;

  const startTime = data.getIn(['time', 'start'], '');
  const endTime = data.getIn(['time', 'end'], '');
  const selectedTime =
    startTime !== '' && endTime !== ''
      ? `${moment(new Date(startTime)).format('LT')} - ${moment(new Date(endTime)).format('LT')}`
      : 'All Day';

  const sessionStatus = data.get('sessionStatus', List());
  const programs = data.get('programIds', List());
  const courseData = data.get('courseData', Map());

  return (
    <div id="chart-filters" className="f-16 mb-3 d-none">
      <div className="mb-1">
        <p className="text-grey">Academic Year</p>
        <p>{academicYear}</p>
      </div>
      <div className="d-flex justify-content-between align-items-center">
        <div className="mb-1">
          <p className="text-grey">Date</p>
          <p>{getFormattedDateRange(data.get('date', Map()))}</p>
        </div>
        <div className="mb-1">
          <p className="text-grey">Time</p>
          <p>{selectedTime}</p>
        </div>
      </div>
      <div className="mt-1">
        <p className="text-grey">Filters</p>
        <div className="d-flex align-items-center flex-wrap-wrap">
          <SessionStatusFilters
            statusWithPermission={fromJS(statusWithPermission)}
            sessionStatus={sessionStatus}
            isDeleteDisabled
          />
          <ProgramFilters programs={programs} isDeleteDisabled />
          {courseData.get('courseId', '') && <CourseFilters data={courseData} isDeleteDisabled />}
        </div>
      </div>
    </div>
  );
};

const ProgramFilters = ({ programs, onDelete = () => {}, isDeleteDisabled = false }) => {
  return (
    <>
      {programs.map((item, i) => (
        <div className="d-flex program_list align-items-center mb-2" key={i}>
          <p className="mb-0 mr-3"> {item.get('name', '')}</p>
          {!isDeleteDisabled && (
            <CloseIcon
              className="remove_hover"
              fontSize="small"
              onClick={() => onDelete(item.get('_id', ''))}
            />
          )}
        </div>
      ))}
    </>
  );
};

const CourseFilters = ({ data, onDelete = () => {}, isDeleteDisabled = false }) => {
  return (
    <div className="d-flex program_list align-items-center mb-2">
      <div>
        <p className="mb-0 mr-3">
          {data.get('courseNumber', '')} - {data.get('courseName', '')}
          {data.get('versionName', '')}
        </p>
        <p className="mb-0 mr-3 f-14 text-capitalize">
          {data.get('programName', '')} / {data.get('term', '')} / {data.get('year', '')} /{' '}
          {data.get('levelNo', '')}
        </p>
      </div>
      {!isDeleteDisabled && (
        <CloseIcon className="remove_hover" fontSize="small" onClick={onDelete} />
      )}
    </div>
  );
};

const SessionStatusFilters = ({
  statusWithPermission,
  sessionStatus,
  onDelete = () => {},
  isDeleteDisabled = false,
}) => {
  return (
    <>
      {statusWithPermission
        .filter((item) => sessionStatus.includes(item.get('name', '')))
        .map((item, i) => (
          <div className="d-flex program_list align-items-center mb-2" key={i}>
            <p className="mb-0 mr-3"> {item.get('label', '')}</p>
            {!isDeleteDisabled && (
              <CloseIcon
                className="remove_hover"
                fontSize="small"
                onClick={() => onDelete(item.get('name', ''))}
              />
            )}
          </div>
        ))}
    </>
  );
};

function Dashboard({
  sessionReport,
  getSessionReport,
  getProgramCourseList,
  courseList,
  getExportReport,
  institutionCalendarLists,
  setData: updateReduxData,
}) {
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElAll, setAnchorElAll] = useState(null);
  const [exportCurrent, setExportCurrent] = useState(
    Map({
      allCurrentPage: 1,
      allPageCount: 10,
      curriculumCurrentPage: 1,
      curriculumPageCount: 10,
    })
  );
  const [actualFilterData, setActualFilterData] = useState(fromJS({}));
  const [filterOpen, setFilterOpen] = React.useState(false);
  const [programFilter, setProgramFilter] = React.useState(false);
  const open = Boolean(anchorEl);
  const openAll = Boolean(anchorElAll);

  function getStatusWithPermission() {
    return getStatus().filter(
      (item) =>
        CheckPermission(
          'tabs',
          'Curriculum Monitoring',
          'Dashboard',
          '',
          'Customize',
          item.label
        ) === true
    );
  }
  const statusWithPermission = getStatusWithPermission();

  function getSelectedStatus() {
    return statusWithPermission
      .map((item) => item.name)
      .filter((item) => !['late', 'early'].includes(item));
  }
  const selectedStatus = getSelectedStatus();
  const [search, setPSearch] = useState('');
  const [data, setData] = useState(
    fromJS({
      institutionCalendarId: '',
      date: {
        start: moment(new Date()).format('YYYY-MM-DD'),
        end: moment(new Date()).format('YYYY-MM-DD'),
      },
      time: {},
      programIds: [],
      courseData: {},
      sessionStatus: selectedStatus,
      singleProgramFilter: {
        term: `all`,
        year: `all`,
        level: `all`,
        courseId: `all`,
      },
      lateStarted: '',
      earlyEnded: '',
    })
  );
  const [updatedDate, setUpdatedDate] = useState(null);
  const institutionCalendarId = data.get('institutionCalendarId', '');
  const handleApply = (dt, type) => {
    if (type === 'program') {
      setData(data.set('programIds', dt).set('singleProgramFilter', fromJS(defaultPgm)));
      if (dt.size === 1) {
        const params = {
          ...{ programIds: dt.getIn(['0', '_id'], '') },
          ...(institutionCalendarId !== '' && { institutionCalendarId: institutionCalendarId }),
        };
        getProgramCourseList(formattedStartDate, params);
      }
    } else {
      const courseFilter = data.set('courseData', fromJS(dt));
      setData(courseFilter);
    }
    setProgramFilter(false);
  };

  const startDate = data.getIn(['date', 'start'], new Date());
  const endDate = data.getIn(['date', 'end'], new Date());
  const startTime = data.getIn(['time', 'start'], '');
  const endTime = data.getIn(['time', 'end'], '');
  const programIds = data.get('programIds', List()).map((item) => item.get('_id'));
  const courseData = data.get('courseData', List());
  const sessionStatus = data.get('sessionStatus', List());
  const term = data.getIn([`singleProgramFilter`, 'term'], '');
  const year = data.getIn([`singleProgramFilter`, 'year'], '');
  const level = data.getIn([`singleProgramFilter`, 'level'], '');
  const courseId = data.getIn([`singleProgramFilter`, 'courseId'], '');
  const lateStarted = data.get('lateStarted', '');
  const earlyEnded = data.get('earlyEnded', '');
  const formattedStartDate = moment(startDate).format('YYYY-MM-DD');
  const formattedEndDate = moment(endDate).format('YYYY-MM-DD');
  function getUTCTime(time, currentDate) {
    const convertedTime = convertTime12to24(moment(new Date(time)).format('hh:mm A'));
    let date = `${currentDate} ${convertedTime}:00`;
    let d = new Date(date.replace(' ', 'T'));
    let utcDate = moment(d).utc().format();
    return utcDate;
  }

  const getTimeObject = () => {
    if (startTime && endTime) {
      return {
        start: getUTCTime(startTime, formattedStartDate),
        end: getUTCTime(endTime, formattedEndDate),
      };
    }
    if (formattedStartDate !== formattedEndDate) {
      return {
        start: moment(`${formattedStartDate} 00:00`).utc().format(),
        end: moment(`${formattedEndDate} 23:59`).utc().format(),
      };
    }
    return {};
  };

  const params = {
    ...getTimeObject(),
    ...(institutionCalendarId !== '' && { institutionCalendarId: institutionCalendarId }),
    ...(programIds.size > 0 && { programIds: programIds.toJS() }),
    ...(sessionStatus.size > 0 && { sessionStatus: sessionStatus.toJS() }),
    ...(term && term !== 'all' && { term: term }),
    ...(year && year !== 'all' && { year: year }),
    ...(level && level !== 'all' && { levelNo: level }),
    ...(courseId && courseId !== 'all' && { courseId: courseId }),
    ...(courseData.get('courseId', '') && {
      courseId: courseData.get('courseId', ''),
      term: courseData.get('term', ''),
      levelNo: courseData.get('levelNo', ''),
      year: courseData.get('year', ''),
      programIds: courseData.get('programIds', ''),
    }),
  };

  // useEffect(() => {
  //   setData(
  //     data
  //       .set('programIds', List())
  //       .set('singleProgramFilter', fromJS(defaultPgm))
  //       .set('courseData', Map())
  //   );
  // }, [institutionCalendarId]); //eslint-disable-line

  useEffect(() => {
    getSessionReport(formattedStartDate, params, () => {
      setUpdatedDate(moment().format('LT'));
    });
    setActualFilterData(fromJS({}));
  }, [(formattedStartDate, data)]); //eslint-disable-line

  const refreshData = () => {
    getSessionReport(formattedStartDate, params, () => setUpdatedDate(moment().format('LT')));
    setActualFilterData(fromJS({}));
  };

  const handleClick = ({ type, pageType }) => {
    const cStatus = [];
    if (sessionStatus.includes('missed')) {
      cStatus.push('missed');
    }
    if (sessionStatus.includes('notstarted')) {
      cStatus.push('notstarted');
    }
    if (sessionStatus.includes('cancelled')) {
      cStatus.push('cancelled');
    }
    if (sessionStatus.includes('merged')) {
      cStatus.push('merged');
    }

    const aStatus = [];
    if (sessionStatus.includes('completed')) {
      aStatus.push('completed');
    }
    if (sessionStatus.includes('inprogress')) {
      aStatus.push('inprogress');
    }
    if (sessionStatus.includes('upcoming')) {
      aStatus.push('upcoming');
    }
    if (sessionStatus.includes('late')) {
      aStatus.push('late');
    }
    if (sessionStatus.includes('early')) {
      aStatus.push('early');
    }
    const value =
      type === 'curricularData'
        ? {
            ...params,
            sessionStatus: cStatus,
          }
        : {
            ...params,
            sessionStatus: cStatus.concat(aStatus),
          };
    const exportCurrentPage = fromJS({
      pageType: pageType,
      pageCount:
        type === 'curricularData'
          ? exportCurrent.get('curriculumPageCount', '')
          : exportCurrent.get('allPageCount', ''),
      currentPage:
        type === 'curricularData'
          ? exportCurrent.get('curriculumCurrentPage', '')
          : exportCurrent.get('allCurrentPage', ''),
      tableLocalFilter:
        type === 'curricularData'
          ? actualFilterData.get(`curriculumFilter`, List())
          : actualFilterData.get(`allFilter`, List()),
      sessionStatus: sessionStatus,
      lateStarted: lateStarted,
      earlyEnded: earlyEnded,
      type: type,
    });
    getExportReport({
      date: formattedStartDate,
      params: value,
      callBack: (scheduleList) =>
        exportExcel({
          scheduleList,
          updatedDate,
          startTime,
          endTime,
          startDate: formattedStartDate,
          endDate: formattedEndDate,
          exportCurrentPage,
          type,
        }),
    });
  };

  const exportOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const exportOpenAll = (event) => {
    setAnchorElAll(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setAnchorElAll(null);
  };

  const handleFilterModalOpen = () => {
    setFilterOpen(true);
  };
  const handleFilterModalClose = () => {
    setFilterOpen(false);
  };
  const handleProgramFilter = () => {
    setProgramFilter(true);
  };
  const handleProgramFilterClose = () => {
    setProgramFilter(false);
  };

  function getCurricularHtml() {
    const curricularDelivery = getCurricularDelivery(sessionReport);
    const length = curricularDelivery.length;
    return curricularDelivery.map((item, index) => {
      return (
        <React.Fragment key={index}>
          <span className={`pr-2 f-15 ${item.color}`}> {item.name} : </span>
          <span className="pr-2 f-15"> {item.count}</span>
          {index !== length - 1 && <span className="pr-2 pl-1 f-15"> {'|'} </span>}
        </React.Fragment>
      );
    });
  }

  function getAllDeliveryHtml() {
    const allDelivery = getAllDelivery(sessionReport);
    const length = allDelivery.length;
    return allDelivery.map((item, index) => {
      return (
        <React.Fragment key={index}>
          <span className={`pr-2 f-15 ${item.color}`}> {item.name} : </span>
          <span className="pr-2 f-15"> {item.count}</span>
          {index !== length - 1 && <span className="pr-2 pl-1 f-15"> {'|'} </span>}
        </React.Fragment>
      );
    });
  }

  function getHandoutStatus() {
    const allDelivery = sessionReport.get('courseHandout', List());
    const length = allDelivery.size;
    return allDelivery.map((item, index) => {
      return (
        <React.Fragment key={index}>
          <RectangleRoundedIcon
            sx={{
              color: item.get('colorCode', '').replace(/^['"]|['"]$/g, ''),
            }}
          />
          <div className="pr-2 pl-2 f-15 text-capitalize">{item.get('status', '')}</div>
          {index !== length - 1 && <span className="pr-2 pl-1 f-15"> {'|'} </span>}
        </React.Fragment>
      );
    });
  }

  const handleProgramRemove = (type, item) => {
    if (type === 'programRemove' && item) {
      const removeProgram = data
        .get('programIds', List())
        .filter((value) => value.get(`_id`) !== item);
      setData(data.set('programIds', removeProgram).set('singleProgramFilter', fromJS(defaultPgm)));
      if (removeProgram.size === 1) {
        const params = {
          ...{ programIds: removeProgram.getIn(['0', '_id'], '') },
          ...(institutionCalendarId !== '' && { institutionCalendarId: institutionCalendarId }),
        };
        getProgramCourseList(formattedStartDate, params);
      }
    }
    if (type === 'courseRemove') {
      const removeCourse = data.set('courseData', Map({}));
      setData(removeCourse);
    }
  };

  const handleRemoveSessionStatus = (status) => {
    const updatedList = data.get('sessionStatus', List()).filter((item) => item !== status);
    const statusList = updatedList.filter((item) => !['merged', 'late', 'early'].includes(item));
    if (!statusList.size)
      return updateReduxData(Map({ message: 'Atleast one status should be selected' }));
    setData((prev) => prev.set('sessionStatus', updatedList));
  };

  const setStatus = (status, lateStarted, earlyEnded) => {
    const updatedData = data
      .set('sessionStatus', status)
      .set('lateStarted', lateStarted)
      .set('earlyEnded', earlyEnded);
    setData(updatedData);
  };

  const courseTermData = (data, type) => {
    return data
      .groupBy((s) => s.get(type))
      .keySeq()
      .toList()
      .map((item) =>
        Map({
          name:
            type === `year`
              ? `Year` + item.replace('year', '')
              : type === `term`
              ? capitalize(item)
              : type === `level_no` && capitalize(item),
          value: item,
        })
      );
  };

  const handleSelectFilter = (e, type) => {
    let assignData;
    switch (type) {
      case 'term': {
        assignData = data
          .setIn(['singleProgramFilter', type], e.target.value)
          .setIn(['singleProgramFilter', 'year'], 'all')
          .setIn(['singleProgramFilter', 'level'], 'all')
          .setIn(['singleProgramFilter', 'courseId'], 'all');
        break;
      }
      case 'year': {
        assignData = data
          .setIn(['singleProgramFilter', type], e.target.value)
          .setIn(['singleProgramFilter', 'level'], 'all')
          .setIn(['singleProgramFilter', 'courseId'], 'all');
        break;
      }
      case 'level': {
        assignData = data
          .setIn(['singleProgramFilter', type], e.target.value)
          .setIn(['singleProgramFilter', 'courseId'], 'all');
        break;
      }
      default:
        assignData = data.setIn(['singleProgramFilter', type], e.target.value);
        break;
    }
    setData(assignData);
  };

  const handleDownloadChart = (id) => {
    const captureElement = document.getElementById(id);
    const exportButton = document.querySelector(`#${id} .export-btn`);
    const filterElement = document.querySelector(`#${id} #chart-filters`);
    exportButton.style.display = 'none';
    filterElement.classList.remove('d-none');

    html2canvas(captureElement).then((canvas) => {
      const link = document.createElement('a');
      link.href = canvas.toDataURL('image/png');
      link.download = `${id}.png`;
      link.click();
      exportButton.style.display = 'inline-flex';
      filterElement.classList.add('d-none');
    });
  };

  const filteredTerm =
    data.getIn(['singleProgramFilter', 'term'], '') !== 'all'
      ? courseList.filter((s) =>
          data.getIn(['singleProgramFilter', 'term'], '') !== 'all'
            ? s.get('term') === data.getIn(['singleProgramFilter', 'term'], '')
            : true
        )
      : List();

  const filteredYear =
    data.getIn(['singleProgramFilter', 'year'], '') !== 'all'
      ? filteredTerm.filter((s) =>
          data.getIn(['singleProgramFilter', 'year'], '') !== 'all'
            ? s.get('year') === data.getIn(['singleProgramFilter', 'year'], '')
            : true
        )
      : List();

  const filteredCourse =
    data.getIn(['singleProgramFilter', 'level_no'], '') !== 'all'
      ? filteredYear
          .filter((s) => s.get('level_no') === data.getIn(['singleProgramFilter', 'level'], ''))
          .map((item) => {
            return {
              name: item.get(`courses_name`),
              value: item.get(`_course_id`),
            };
          })
      : List();

  const programStatus = data.get('programIds', List()).map((item) => item.get('_id'));
  const termYear = data.getIn(['singleProgramFilter', 'courseId'], '');
  const courseStatus = courseData.get('courseId', '');
  const selectedProgram = data.get('programIds', List());
  return (
    <div className="addSubTheme_bg">
      <div className="container pt-3 mb-5 pb-3">
        <SessionFilter
          data={data}
          setData={setData}
          refreshData={refreshData}
          CheckPermission={CheckPermission}
          searchP={search}
          setPSearch={setPSearch}
          updatedDate={updatedDate}
        />
        <div className={'d-flex align-items-center flex-wrap-wrap mt-2'}>
          {CheckPermission('tabs', 'Curriculum Monitoring', 'Dashboard', '', 'Customize', 'View') &&
            statusWithPermission.filter((item) => {
              return !['Merged', 'Late Started', 'Early Ended'].includes(item.label);
            }).length > 0 && (
              <MButton
                variant="contained"
                color={'darkGray'}
                startIcon={<SettingsIcon />}
                className="mr-3 mb-2"
                clicked={handleFilterModalOpen}
              >
                {' '}
                Customize
              </MButton>
            )}
          {(CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Filter Program') ||
            CheckPermission('pages', 'Curriculum Monitoring', 'Dashboard', 'Filter Course')) && (
            <div
              className="d-flex justify-content-between bg-white z-1 border-radious-4 remove_hover mb-2 mr-3 p-2 w-11"
              onClick={handleProgramFilter}
            >
              <p className="mb-0 text-NewLightgray"> Filter</p>
              <FilterListOutlinedIcon className="text-NewLightgray" />
            </div>
          )}

          <SessionStatusFilters
            statusWithPermission={fromJS(statusWithPermission)}
            sessionStatus={sessionStatus}
            onDelete={handleRemoveSessionStatus}
          />

          <ProgramFilters
            programs={data.get('programIds', List())}
            onDelete={(id) => handleProgramRemove('programRemove', id)}
          />

          {courseData.get('courseId', '') && (
            <CourseFilters data={courseData} onDelete={() => handleProgramRemove('courseRemove')} />
          )}
        </div>

        <p className="mt-4 bold f-18"> Summary of All Program</p>

        {data.get('programIds', '').size === 1 && (
          <div className="d-flex">
            <div className="w-10 mr-3">
              <MaterialInput
                elementType={'materialSelectNew'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{
                  options: [
                    { name: 'All', value: 'all' },
                    ...courseTermData(courseList, 'term').toJS(),
                  ],
                }}
                changed={(e) => handleSelectFilter(e, 'term')}
                value={data.getIn(['singleProgramFilter', 'term'], '')}
                label={'Term'}
                labelclass={'mb-0 f-14'}
              />
            </div>
            <div className="w-10 mr-3">
              <MaterialInput
                elementType={'materialSelectNew'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{
                  options: [
                    { name: 'All', value: 'all' },
                    ...courseTermData(filteredTerm, 'year').toJS(),
                  ],
                }}
                changed={(e) => handleSelectFilter(e, 'year')}
                value={data.getIn(['singleProgramFilter', 'year'], '')}
                label={'Year'}
                labelclass={'mb-0 f-14'}
              />
            </div>
            <div className="w-10 mr-3">
              <MaterialInput
                elementType={'materialSelectNew'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{
                  options: [
                    { name: 'All', value: 'all' },
                    ...courseTermData(filteredYear, 'level_no').toJS(),
                  ],
                }}
                changed={(e) => handleSelectFilter(e, 'level')}
                value={data.getIn(['singleProgramFilter', 'level'], '')}
                label={'Level'}
                labelclass={'mb-0 f-14'}
              />
            </div>
            <div className="w-15">
              <MaterialInput
                elementType={'materialSelectNew'}
                type={'text'}
                variant={'outlined'}
                size={'small'}
                elementConfig={{ options: [{ name: 'All', value: 'all' }, ...filteredCourse] }}
                changed={(e) => handleSelectFilter(e, 'courseId')}
                value={data.getIn(['singleProgramFilter', 'courseId'])}
                label={'Course'}
                labelclass={'mb-0 f-14'}
              />
            </div>
          </div>
        )}

        <div className="row mt-3">
          <div className="col-md-12 col-12 col-xl-7 col-lg-12">
            <div className="p-3 bg-white border-radious-4 mb-4" id="delivery-status-chart">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <p className="bold f-18 mb-0">
                  {' '}
                  Today Delivery Status ({sessionReport.get('totalSchedule', 0)})
                </p>
                <div className="d-flex">
                  {sessionStatus.includes('late') && (
                    <div className="d-flex align-items-center">
                      <FiberManualRecordRoundedIcon
                        className="p-0 color-LateStarted"
                        fontSize={`small`}
                      />
                      <span className="f-14 ml-2">Late Started</span>
                    </div>
                  )}
                  {sessionStatus.includes('early') && (
                    <div className="d-flex ml-3 align-items-center">
                      <FiberManualRecordRoundedIcon
                        className="p-0 color-earlyEnded"
                        fontSize={`small`}
                      />
                      <span className="f-14 ml-2">Early Ended</span>
                    </div>
                  )}
                  <ExportIconButton clicked={() => handleDownloadChart('delivery-status-chart')} />
                </div>
              </div>
              <ChartFilters
                data={data}
                institutionCalendarLists={institutionCalendarLists}
                statusWithPermission={statusWithPermission}
              />
              <ReactECharts
                notMerge={true}
                lazyUpdate={true}
                option={getDeliveryOption(sessionReport, lateStarted, earlyEnded, sessionStatus)}
                className={sessionStatus.includes('merged') && 'echart_custom_height'}
              />
            </div>
          </div>
          <div className="col-md-12 col-12 col-xl-5 col-lg-12">
            {sessionStatus.includes('merged') && (
              <div className="pl-3 pr-3 pt-2 pb-2 d-flex justify-content-between bg-white border-radious-4 mb-3 align-items-center">
                <p className="bold f-18 mb-0"> Merged Session</p>
                <p className="bold f-18 mb-0 f-27"> {sessionReport.get('mergedSchedule', '')}</p>
              </div>
            )}

            <div className="p-3 bg-white border-radious-4 mb-4" id="mode-chart">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <p className="bold f-18 mb-0"> Mode</p>
                <ExportIconButton clicked={() => handleDownloadChart('mode-chart')} />
              </div>
              <ChartFilters
                data={data}
                institutionCalendarLists={institutionCalendarLists}
                statusWithPermission={statusWithPermission}
              />
              <ReactECharts
                notMerge={true}
                lazyUpdate={true}
                option={getModeOption(sessionReport)}
              />
            </div>
          </div>
        </div>

        <div className="p-3 bg-white border-radious-4 mb-4">
          <div className="d-flex justify-content-between border-bottom pb-3">
            <div>
              <p className="mb-0 f-18 bold"> Curricular Delivery Issues</p>
              {getCurricularHtml()}
            </div>
            <div>
              {CheckPermission(
                'pages',
                'Curriculum Monitoring',
                'Dashboard',
                'Curricular Delivery Export'
              ) && (
                <MButton
                  variant="contained"
                  color="primary"
                  startIcon={<FileUploadOutlinedIcon />}
                  endIcon={<KeyboardArrowDownOutlinedIcon />}
                  clicked={exportOpen}
                  aria-label="more"
                  aria-controls="long-menu"
                  disabled={
                    !(
                      sessionStatus.includes('missed') ||
                      sessionStatus.includes('notstarted') ||
                      sessionStatus.includes('cancelled')
                    )
                  }
                >
                  Export Data |
                </MButton>
              )}

              <Menu
                id="long-menu"
                anchorEl={anchorEl}
                keepMounted
                open={open}
                onClose={handleClose}
                PaperProps={{
                  style: {
                    width: '18.2ch',
                  },
                }}
              >
                <ListView component="div" className="align-items-center" disablePadding>
                  <ListItemButton onClick={() => handleClick({ type: 'curricularData' })}>
                    <ListItemText primary="Export All Pages" />
                  </ListItemButton>
                  <ListItemButton
                    onClick={() => handleClick({ type: 'curricularData', pageType: 'currentPage' })}
                  >
                    <ListItemText primary="Export This Page" />
                  </ListItemButton>
                </ListView>
              </Menu>
            </div>
          </div>

          <TableModule
            tableList={sessionReport}
            type={'curricularData'}
            lateStarted={lateStarted}
            earlyEnded={earlyEnded}
            sessionStatus={sessionStatus}
            programStatus={programStatus}
            termYear={termYear}
            courseStatus={courseStatus}
            CheckPermission={CheckPermission}
            search={search}
            setExportCurrent={setExportCurrent}
            exportCurrent={exportCurrent}
            setActualFilterData={setActualFilterData}
            actualFilterData={actualFilterData}
            currentDate={formattedEndDate}
          />
        </div>

        <div className="p-3 bg-white border-radious-4 mb-5">
          <div className="d-flex justify-content-between border-bottom pb-3">
            <div>
              <p className="mb-0 f-18 bold"> All Deliveries</p>
              {getAllDeliveryHtml()}
              <div className="d-flex mt-2">{getHandoutStatus()}</div>
            </div>

            <div>
              {CheckPermission(
                'pages',
                'Curriculum Monitoring',
                'Dashboard',
                'All Deliveries Export'
              ) && (
                <MButton
                  variant="contained"
                  color="primary"
                  startIcon={<FileUploadOutlinedIcon />}
                  endIcon={<KeyboardArrowDownOutlinedIcon />}
                  clicked={exportOpenAll}
                  aria-label="more"
                  aria-controls="long-menuAll"
                  aria-haspopup="true"
                >
                  Export Data |
                </MButton>
              )}
            </div>
            <Menu
              id="long-menuAll"
              anchorEl={anchorElAll}
              keepMounted
              open={openAll}
              onClose={handleClose}
              PaperProps={{
                style: {
                  width: '18.2ch',
                },
              }}
            >
              <ListView component="div" className="align-items-center" disablePadding>
                <ListItemButton onClick={handleClick}>
                  <ListItemText primary="Export All Pages" />
                </ListItemButton>
                <ListItemButton onClick={() => handleClick({ pageType: 'currentPage' })}>
                  <ListItemText primary="Export This Page" />
                </ListItemButton>
              </ListView>
            </Menu>
          </div>

          <TableModule
            tableList={sessionReport}
            type=""
            lateStarted={lateStarted}
            earlyEnded={earlyEnded}
            sessionStatus={sessionStatus}
            programStatus={programStatus}
            termYear={termYear}
            courseStatus={courseStatus}
            CheckPermission={CheckPermission}
            search={search}
            currentDate={formattedEndDate}
            setExportCurrent={setExportCurrent}
            exportCurrent={exportCurrent}
            setActualFilterData={setActualFilterData}
            actualFilterData={actualFilterData}
          />
        </div>
      </div>

      {programFilter && (
        <Suspense fallback="">
          <ProgramFilterModal
            show={programFilter}
            handleProgramFilterClose={handleProgramFilterClose}
            handleApply={handleApply}
            programStatus={selectedProgram}
            CheckPermission={CheckPermission}
            institutionCalendarId={institutionCalendarId}
          />
        </Suspense>
      )}
      {filterOpen && (
        <Suspense fallback="">
          <CustomFilterModal
            show={filterOpen}
            handleFilterModalClose={handleFilterModalClose}
            sessionStatus={sessionStatus}
            setStatus={setStatus}
            lateStarted={lateStarted}
            earlyEnded={earlyEnded}
            statusWithPermission={statusWithPermission}
          />
        </Suspense>
      )}
    </div>
  );
}

Dashboard.propTypes = {
  sessionReport: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  getSessionReport: PropTypes.func,
  getProgramCourseList: PropTypes.func,
  courseList: PropTypes.instanceOf(List),
  getExportReport: PropTypes.func,
  institutionCalendarLists: PropTypes.instanceOf(List),
  setData: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    sessionReport: selectSessionReport(state),
    courseList: selectProgramCourseList(state),
    institutionCalendarLists: selectInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps, actions)(Dashboard);
