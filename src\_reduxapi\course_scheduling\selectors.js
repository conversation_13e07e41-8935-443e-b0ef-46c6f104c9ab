import { List } from 'immutable';
const courseSchedulingState = (state) => state.courseScheduling;

const selectLoading = (state) => courseSchedulingState(state).get('loading');
const selectMessage = (state) => courseSchedulingState(state).get('message');
const selectProgramList = (state) => courseSchedulingState(state).get('programList');
const selectRemoteRooms = (state) => courseSchedulingState(state).get('remoteRooms');
const selectExtraCurricularList = (state) =>
  courseSchedulingState(state).get('extraCurricularAndBreakTiming');
const selectInstitutionCalendar = (state) =>
  courseSchedulingState(state).get('institutionCalendar');
const selectActiveInstitutionCalendar = (state) =>
  courseSchedulingState(state).get('activeInstitutionCalendar');
const selectCourseCoordinators = (state) => courseSchedulingState(state).get('courseCoordinators');
const selectCourseList = (state) => courseSchedulingState(state).get('courseList');
const selectActiveCourse = (state) => courseSchedulingState(state).get('activeCourse');
const selectActiveScheduleView = (state) => courseSchedulingState(state).get('activeScheduleView');
const selectCourseSchedule = (state) => courseSchedulingState(state).get('courseSchedule');
const selectActiveSessionFlow = (state) => courseSchedulingState(state).get('activeSessionFlow');
const selectScheduleAvailability = (state) =>
  courseSchedulingState(state).get('scheduleAvailability');
const selectPaginationMetaData = (state) => courseSchedulingState(state).get('paginationMetaData');
const selectActiveTab = (state) => courseSchedulingState(state).get('activeTab');
const selectTimeTableData = (state) => courseSchedulingState(state).get('timeTableData');
const selectCourseScheduleExport = (state) =>
  courseSchedulingState(state).get('courseScheduleExport');
const selectIndividualCourseDetails = (state) =>
  courseSchedulingState(state).get('individualCourseDetails');
const selectIndividualSessionDetails = (state) =>
  courseSchedulingState(state).get('individualSessionDetails');
const selectMcActiveTab = (state) => courseSchedulingState(state).get('mcActiveTab');
const selectCourseGroupSettingDetails = (state) =>
  courseSchedulingState(state).get('courseGroupSettingDetails');
const selectAdvancedSettings = (state) => courseSchedulingState(state).get('advancedSettings');
const selectManageTopics = (state) => courseSchedulingState(state).get('manageTopics');
const selectActiveSettingsView = (state) => courseSchedulingState(state).get('activeSettingsView');
const selectMergeSchedule = (state) => courseSchedulingState(state).get('mergeSchedule');
const selectAutoAssignCourseDeliveryList = (state) =>
  courseSchedulingState(state).get('autoAssignCourseDeliveryList');
const selectStudentGroupsWithStudents = (state) =>
  courseSchedulingState(state).get('studentGroupsWithStudents');
const selectPaginationCourseMetaData = (state) =>
  courseSchedulingState(state).get('paginationCourseMetaData');
const selectCurrentMainView = (state) => courseSchedulingState(state).get('mainView');
const selectStaffScheduleOptionList = (state) =>
  courseSchedulingState(state).get('staffScheduleOptionList');
const selectExternalStaff = (state) => courseSchedulingState(state).get('externalStaff');
const selectClassLeader = (state) => courseSchedulingState(state).get('ClassLeader');
const selectCourseSessionStatusManagement = (state) =>
  courseSchedulingState(state).get('courseSessionStatusManagement');
const selectStudentBasedDeliveryTypes = (state) =>
  courseSchedulingState(state).get('studentBasedDeliveryTypes');

const selectpostClassLeader = (state) =>
  courseSchedulingState(state).get('postClassLeader', List());
const selectScheduledExternalStaff = (state) =>
  courseSchedulingState(state).get('scheduledExternalStaff', List());
const selectMergeScheduledClassLeader = (state) =>
  courseSchedulingState(state).get('mergeScheduledClassLeader', List());

const selectLevelWiseCourses = (state) => courseSchedulingState(state).get('levelWiseCourses');
const selectCourseTopics = (state) => courseSchedulingState(state).get('courseTopics');
const selectAvailableList = (state) => courseSchedulingState(state).get('availableList');
const selectCourseDeliveryTypes = (state) =>
  courseSchedulingState(state).get('courseDeliveryTypes');
const selectScheduleAnalysis = (state) => courseSchedulingState(state).get('scheduleAnalysis');
const selectEventList = (state) => courseSchedulingState(state).get('eventList');

export {
  selectLoading,
  selectMessage,
  selectProgramList,
  selectRemoteRooms,
  selectExtraCurricularList,
  selectInstitutionCalendar,
  selectActiveInstitutionCalendar,
  selectCourseCoordinators,
  selectCourseList,
  selectActiveCourse,
  selectActiveScheduleView,
  selectCourseSchedule,
  selectActiveSessionFlow,
  selectScheduleAvailability,
  selectPaginationMetaData,
  selectActiveTab,
  selectTimeTableData,
  selectCourseScheduleExport,
  selectIndividualCourseDetails,
  selectIndividualSessionDetails,
  selectMcActiveTab,
  selectCourseGroupSettingDetails,
  selectAdvancedSettings,
  selectManageTopics,
  selectActiveSettingsView,
  selectMergeSchedule,
  selectAutoAssignCourseDeliveryList,
  selectStudentGroupsWithStudents,
  selectPaginationCourseMetaData,
  selectCurrentMainView,
  selectStaffScheduleOptionList,
  selectExternalStaff,
  selectClassLeader,
  selectCourseSessionStatusManagement,
  selectStudentBasedDeliveryTypes,
  selectpostClassLeader,
  selectScheduledExternalStaff,
  selectMergeScheduledClassLeader,
  selectLevelWiseCourses,
  selectCourseTopics,
  selectAvailableList,
  selectCourseDeliveryTypes,
  selectScheduleAnalysis,
  selectEventList,
};
