import React, { Component } from 'react';
import { connect } from 'react-redux';
import DatePicker, { registerLocale } from 'react-datepicker';
import { Button, Modal, Badge, Table } from 'react-bootstrap';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import moment from 'moment';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import { List, Map } from 'immutable';
import ar from 'date-fns/locale/ar';

import axios from '../../../axios';
import AddNew from '../../../Widgets/Button/AddNew';
import Loader from '../../../Widgets/Loader/Loader';
import '../ReviewEvent/review.css';
import MainTitle from '../../../Widgets/MainTitle';
import TableEmptyMessage from '../../../Widgets/CustomMessage/TableEmptyMessage';
import Input from '../../../Widgets/FormElements/Input/Input';
import Breadcrumb from '../../../Widgets/Breadcrumb/Breadcrumb';
import {
  timeFormat,
  formatFullName,
  getURLParams,
  isMobileVerifyMandatory,
  getEnvCollegeName,
  // isIndVer,
} from '../../../utils';
import Search from '../../../Widgets/Search/Search';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import {
  selectActiveInstitutionCalendar,
  selectUserInfo,
} from '../../../_reduxapi/Common/Selectors';

import { getLang } from 'utils';
import AddEditEventModal from '../Modals/AddEditEventModal';
import CalenderIndx from '../index';
import * as actions from '../../../_reduxapi/institution/actions';
import withCalendarHooks from 'Hoc/withCalendarHooks';

const lang = getLang();
registerLocale('ar', ar);

const agree = [
  ['Agree', 'agree'],
  ['Disagree', 'disagree'],
];

let selectedArray = [];
class ReviewEventView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      staffData: [],
      selectStaffList: [],
      review: [],
      eventName: '',
      eventDetail: '',
      arab1: '',
      arab2: '',
      selectType: '',
      startDate: '',
      endDate: '',
      editStartTime: '',
      editEndTime: '',
      eventType: [
        { name: '', value: '' },
        { name: t('events.event_types.holiday'), value: 'holiday' },
        { name: t('events.event_types.exam'), value: 'exam' },
        { name: t('events.event_types.training'), value: 'training' },
        { name: t('events.event_types.orientation'), value: 'orientation' },
        { name: t('events.event_types.general'), value: 'general' },
      ],
      venue: [
        { name: '', value: '' },
        { name: 'Madurai', value: '5ea93af58d49c51140fcf63e' },
        { name: 'Chennai', value: '5ea93af58d49c51140fcf64e' },
      ],
      selectVenue: '',
      skip: false,
      eventView: false,
      eventEdit: false,
      searchView: false,
      requestView: false,
      completeView: false,
      publishView: false,
      isLoading: false,
      reviewEndDate: '',
      reviewEndTime: new Date(),
      evId: '',
      evType: '',
      evName: '',
      evArab1: '',
      evDetail: '',
      evArab2: '',
      evStartDate: '',
      evEndDate: '',
      evStartTime: '',
      evEndTime: '',
      feed: '',
      selectAgree: agree[0][1][2],
      chooseAgree: '',
      eventId: '',
      completeEmail: '',
      completeSms: '',
      completeScheduler: '',
      completeClass: '',
      completeError: '',
      publishStaff: '',
      publishStudent: '',
      publishError: '',
      blockBtn: false,
      searchText: '',
      editReview: false,
      activeIndex: null,
      calendarId: getURLParams('id', true),
      calendarName: getURLParams('name', true),
      isActive: getURLParams('s', true) === 'true',
    };
  }

  componentDidMount() {
    this.fetchApi();
    // this.interval = setInterval(() => {
    //   const { activeInstitutionCalendar } = this.props;
    //   if (!activeInstitutionCalendar.isEmpty()) {
    //     this.fetchApi();
    //     clearInterval(this.interval);
    //   }
    // }, 500);
    if (this.state.reviewEndDate === '')
      this.setState({
        reviewEndDate: new Date().setDate(new Date().getDate() + 1),
      });
  }

  // componentDidUpdate(prevProps) {
  //   const { activeInstitutionCalendar } = this.props;
  //   if (activeInstitutionCalendar !== prevProps.activeInstitutionCalendar) {
  //     this.fetchApi();
  //   }
  // }

  // componentWillUnmount() {
  //   clearInterval(this.interval);
  // }

  fetchApi = () => {
    const { loggedInUserData } = this.props;
    const { calendarId } = this.state;
    this.setState({
      isLoading: true,
    });
    let url = `institution_calendar_event/list_calendar/${calendarId}?limit=500&pageNo=1`;
    const subRole = loggedInUserData.get('sub_role', List());
    if (
      CheckPermission('pages', 'Calendar', 'Institution Calendar', 'Add Event') ||
      (subRole && subRole.size > 0 && subRole.indexOf('Institution_Calendar_Reviewer') !== -1)
    ) {
      url = `institution_calendar_event/list_event/${calendarId}`;
    }
    axios
      .get(url)
      .then((res) => {
        let data = [];
        if (res.data.data && res.data.data.length > 0) {
          data = res.data.data.map((r) => {
            return {
              _id: r._id,
              first_language: r.event_name.first_language,
              event_date: r.event_date,
              status: r.status,
              reviewEvent: r.review.map((rs) => {
                return {
                  reviewerId: rs._reviewer_ids,
                  reviews: rs.status,
                };
              }),
            };
          });

          let view = {
            _id: data[0]._id,
          };
          this.eventViewShow('', view);
        }

        this.setState({
          data: data,
          isLoading: false,
          evId: data && data.length > 0 ? data[0]._id : '',
        });
      })
      .catch((error) => {
        this.props.setData({ message: error.response.data.message });
        this.setState({
          isLoading: false,
        });
      });

    axios
      .get(`institution_calendar_event/list_calendar_event_reviewer/${calendarId}`)
      .then((res) => {
        const selectStaffList = res.data.data.map((data) => {
          return {
            staffID: data._id,
            first: data.name.first,
            last: data.name.last,
            middle: data.name.middle,
            isCheckedEmail: false,
            isCheckedSms: false,
            isCheckedScheduler: false,
            isCheckedClass: false,
          };
        });

        this.setState({
          selectStaffList: selectStaffList,
          isLoading: false,
        });
      })

      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  editStartTime = (date) => {
    this.setState({
      editStartTime: date,
    });
  };

  editEndTime = (date) => {
    this.setState({
      editEndTime: date,
    });
  };

  reviewEndTimes = (date) => {
    this.setState({
      reviewEndTime: date,
    });
  };

  reviewEndDate = (date) => {
    this.setState({
      reviewEndDate: date,
    });
  };

  handleCheck = (event, name) => {
    if (name === 'day') {
      this.setState({
        oneDay: event.target.checked,
        endDate: this.state.startDate,
      });
    }
    if (name === 'value') {
      this.setState({
        valueCheck: event.target.checked,
      });
    }
    if (name === 'skip') {
      this.setState({
        skip: event.target.checked,
      });
    }
  };

  handleChange = (e, name) => {
    if (name === 'eventName') {
      this.setState({
        eventName: e.target.value,
        eventNameError: '',
      });
    }

    if (name === 'eventDetail') {
      this.setState({
        eventDetail: e.target.value,
        eventDetailError: '',
      });
    }
    if (name === 'arab1') {
      this.setState({
        arab1: e.target.value,
        arab1Error: '',
      });
    }
    if (name === 'arab2') {
      this.setState({
        arab2: e.target.value,
        arab2Error: '',
      });
    }
    if (name === 'startDate') {
      this.setState({
        startDate: e.target.value,
        startDateError: '',
      });
    }
    if (name === 'endDate') {
      this.setState({
        endDate: e.target.value,
        endDateError: '',
      });
    }

    if (name === 'startTime') {
      this.setState({
        startTime: e.target.value,
        startTimeError: '',
      });
    }
    if (name === 'reviewEndDate') {
      this.setState({
        reviewEndDate: e.target.value,
        reviewEndDateError: '',
      });
    }

    if (name === 'feed') {
      this.setState({
        feed: e.target.value,
      });
    }
  };

  handleSelect = (e, name) => {
    if (name === 'eventType') {
      this.setState({
        selectType: e.target.value,
        selectTypeError: '',
      });
    }
    if (name === 'venue') {
      this.setState({
        selectVenue: e.target.value,
        selectVenueError: '',
      });
    }
  };

  eventViewShow = (e, view) => {
    this.setState({
      eventView: true,
      isLoading: true,
      eventId: view._id,
    });
    axios
      .get(`institution_calendar_event/list_id_reviewer/${view._id}`)
      .then((res) => {
        const eventSingleView = res.data.data;
        const review = eventSingleView.review.map((re) => {
          return {
            reviewerId: re._reviewer_ids,
            first: re.reviewer_name.name.first,
            middle: re.reviewer_name.name.middle,
            last: re.reviewer_name.name.last,
            expireTime: re.expire.expire_time,
            comment: re.reviewer_comment,
            status: re.status,
            dean_comment: re.dean_comment !== undefined ? re.dean_comment : '',
            dean_feedback: re.dean_feedback !== undefined ? re.dean_feedback : '',
            // reviewBox:this.state.loginType === re._reviewer_ids ? re.reviews : '',
            reviews: re.reviews,
          };
        });

        this.setState({
          review: review,
          evId: eventSingleView._id,
          evType: eventSingleView.event_type,
          evName: eventSingleView.event_name.first_language,
          evArab1: eventSingleView.event_name.second_language,
          evDetail: eventSingleView.event_description.first_language,
          evArab2: eventSingleView.event_description.second_language,
          evStartDate: eventSingleView.event_date,
          evEndDate: eventSingleView.end_date,
          evStartTime: eventSingleView.start_time,
          evEndTime: eventSingleView.end_time,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  editStartDate = (date) => {
    this.setState({
      startDate: date,
      endDate: date,
    });
  };

  editEndDate = (date) => {
    this.setState({
      endDate: date,
    });
  };

  eventEdit = (e, views) => {
    this.setState({
      isLoading: true,
      eventEdit: true,
    });
    axios
      .get(`institution_calendar_event/${this.state.evId}`)
      .then((res) => {
        const data = res.data.data;
        this.setState({
          selectType: data.event_type,
          eventName: data.event_name.first_language,
          arab1: data.event_name.second_language,
          eventDetail: data.event_description.first_language,
          arab2: data.event_description.second_language,
          startDa: data.event_date,
          endDa: data.end_date,
          startDate: moment(data.event_date).format('D MMM YYYY'),
          endDate: moment(data.end_date).format('D MMM YYYY'),
          startTimeView: data.start_time,
          endTimeView: data.end_time,
          oneDay: moment(data.event_date).format('L') === moment(data.end_date).format('L'),
          isLoading: false,
          editEndTime: '',
          editStartTime: '',
        });
      })
      .catch((error) => {
        this.props.setData({ message: 'error' });
        this.setState({
          isLoading: false,
        });
      });
  };

  eventEditClose = () => {
    this.setState({
      eventEdit: false,
    });
  };

  validation = (e) => {
    let eventNameError = '';
    let eventDetailError = '';
    let selectTypeError = '';
    let startDateError = '';
    let endDateError = '';
    let selectVenueError = '';

    if (this.state.selectType === '') {
      selectTypeError = t('chooseEventType');
    }

    if (this.state.valueCheck === true) {
      if (this.state.selectVenue === '') {
        selectVenueError = t('chooseVenueType');
      }
    }

    if (this.state.startDate === '') {
      startDateError = t('chooseStartDate');
    }

    if (this.state.oneDay === false) {
      if (this.state.endDate === '') {
        endDateError = t('chooseEndDate');
      }
    }

    if (!this.state.eventName) {
      eventNameError = t('eventNameRequired');
    } else if (this.state.eventName.length <= 2) {
      eventNameError = t('minimumThreeChar');
    }

    if (
      eventNameError ||
      eventDetailError ||
      selectTypeError ||
      startDateError ||
      endDateError ||
      selectVenueError
    ) {
      this.setState({
        eventNameError,
        eventDetailError,
        selectTypeError,
        startDateError,
        endDateError,
        selectVenueError,
      });
      return false;
    }
    return true;
  };

  handleAllChecked = (event) => {
    let data = this.state.staffData;
    data.map((data) => (data.isChecked = event.target.checked));
    this.setState({ staffData: data });
  };

  handleCheckFieldElement = (event, id) => {
    let data = [...this.state.staffData];
    if (event.target.checked) {
      selectedArray.push(id);
    } else {
      selectedArray =
        selectedArray && selectedArray.length > 0 && selectedArray.filter((e) => e !== id);
    }
    const updatedData = data.map((item) => {
      if (item.id === id) {
        return { ...item, isChecked: event.target.checked };
      } else {
        return item;
      }
    });
    this.setState({ staffData: updatedData });
  };

  handleAllCheckBox = (event, name) => {
    let data = this.state.selectStaffList;
    if (name === 'email') {
      data.map((data) => (data.isCheckedEmail = event.target.checked));
      this.setState({ selectStaffList: data });
    }
    if (name === 'sms') {
      data.map((data) => (data.isCheckedSms = event.target.checked));
      this.setState({ selectStaffList: data });
    }
    if (name === 'scheduler') {
      data.map((data) => (data.isCheckedScheduler = event.target.checked));
      this.setState({ selectStaffList: data });
    }

    if (name === 'class') {
      data.map((data) => (data.isCheckedClass = event.target.checked));
      this.setState({ selectStaffList: data });
    }

    if (name === 'completeemail') {
      let email = event.target.checked;
      if (email === true) {
        this.setState({
          completeEmail: 'email',
          completeError: '',
        });
      } else {
        this.setState({
          completeEmail: '',
        });
      }
    }

    if (name === 'completesms') {
      let sms = event.target.checked;
      if (sms === true) {
        this.setState({
          completeSms: 'sms',
          completeError: '',
        });
      } else {
        this.setState({
          completeSms: '',
        });
      }
    }

    if (name === 'completescheduler') {
      let Scheduler = event.target.checked;
      if (Scheduler === true) {
        this.setState({
          completeScheduler: 'digiclass',
          completeError: '',
        });
      } else {
        this.setState({
          completeScheduler: '',
        });
      }
    }

    if (name === 'completeclass') {
      let digiclass = event.target.checked;
      if (digiclass === true) {
        this.setState({
          completeClass: 'digiclass',
          completeError: '',
        });
      } else {
        this.setState({
          completeClass: '',
        });
      }
    }

    if (name === 'staff') {
      let staff = event.target.checked;
      if (staff === true) {
        this.setState({
          publishStaff: 'staff',
          publishError: '',
        });
      } else {
        this.setState({
          publishStaff: '',
        });
      }
    }

    if (name === 'student') {
      let student = event.target.checked;
      if (student === true) {
        this.setState({
          publishStudent: 'student',
          publishError: '',
        });
      } else {
        this.setState({
          publishStudent: '',
        });
      }
    }
  };
  handleState = (data) => {
    this.setState(data);
  };
  handleCheckBox = (event, index, name) => {
    let data = this.state.selectStaffList;

    if (name === 'email') {
      data[index].isCheckedEmail = event.target.checked;
      this.setState({ selectStaffList: data });
    }
    if (name === 'sms') {
      data[index].isCheckedSms = event.target.checked;
      this.setState({ selectStaffList: data });
    }

    if (name === 'scheduler') {
      data[index].isCheckedScheduler = event.target.checked;
      this.setState({ selectStaffList: data });
    }

    if (name === 'class') {
      data[index].isCheckedClass = event.target.checked;
      this.setState({ selectStaffList: data });
    }
  };

  reviewValidation = () => {
    let reviewEndDateError = '';
    let reviewEndTimeError = '';

    if (this.state.reviewEndDate === '') {
      reviewEndDateError = t('chooseEndDate');
    }
    if (this.state.reviewEndTime === '') {
      reviewEndTimeError = t('chooseEndTime');
    }
    if (reviewEndDateError || reviewEndTimeError) {
      this.setState({
        reviewEndTimeError,
        reviewEndDateError,
      });
      return false;
    }
    return true;
  };

  sendReview = (e) => {
    e.preventDefault();
    if (this.reviewValidation()) {
      let emailChecked = this.state.selectStaffList.filter((data) => {
        return (
          data.isCheckedEmail === true ||
          data.isCheckedSms === true ||
          data.isCheckedScheduler === true ||
          data.isCheckedClass === true
        );
      });

      let data = emailChecked.map((id) => {
        return {
          _reviewer_id: id.staffID,
          notify_via: [
            id.isCheckedEmail === true ? 'email' : '',
            id.isCheckedSms === true ? 'sms' : '',
            id.isCheckedScheduler === true ? 'digischeduler' : '',
            id.isCheckedClass === true ? 'digiclass' : '',
          ],
        };
      });

      if (data.length !== this.state.selectStaffList.length) {
        this.props.setData({ message: t('mandatoryRequired') });
        return;
      }

      let reviewDate = moment(this.state.reviewEndDate).format('YYYY-MM-DD');

      let reviewTime = timeFormat(moment(this.state.reviewEndTime).format('H:mm:ss'));
      let st = moment(reviewDate + 'T' + reviewTime).toDate();

      const fullData = {
        data,
        expire_date: reviewDate,
        expire_time: st.getTime(),
      };

      this.setState({
        isLoading: true,
      });

      const { calendarId } = this.state;
      const id = calendarId;

      axios
        .post(`institution_calendar_event/send_reviewer_notification/${id}`, fullData)
        .then((res) => {
          this.props.setData({ message: t('reviewRequestSuccess') });
          this.setState(
            {
              requestView: false,
              isLoading: false,
              selectStaffList: [],
            },
            (e) => {
              this.fetchApi();
            }
          );
        })
        .catch((error) => {
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  selectStaff = (e) => {
    let selectStaff = selectedArray;
    if (selectStaff && selectStaff.length > 0) {
      this.setState({
        isLoading: true,
      });
      const _reviewer_id = selectStaff;
      const { calendarId } = this.state;
      axios
        .post(`institution_calendar_event/add_reviewer/${calendarId}`, {
          _reviewer_id,
        })
        .then((res) => {
          this.props.setData({ message: t('staffAdded') });
          this.setState(
            {
              // data: data,
              searchView: false,
              isLoading: false,
              searchText: '',
              blockBtn: false,
            },
            () => {
              this.selectStaffList();
              let view = {
                _id: this.state.evId,
              };
              this.eventViewShow(e, view);
              selectedArray = [];
            }
          );
        })
        .catch((error) => {
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
            searchText: '',
          });
          selectedArray = [];
        });
    } else {
      this.props.setData({ message: t('chooseOneStaff') });
    }
  };

  searchModel = () => {
    this.setState({
      searchView: true,
      isLoading: true,
    });

    let endPoint;

    if (this.state.searchText !== '') {
      endPoint = `user/user_search/staff/completed/${this.state.searchText}?limit=100&pageNo=1`;
    } else {
      endPoint = `user/get_all/staff/completed?limit=100&pageNo=1`;
    }
    const loggedInRole =
      this.props.token !== undefined && this.props.token !== ''
        ? this.props.token.replace(/^"(.*)"$/, '$1')
        : '';

    axios
      .get(endPoint)
      .then((res) => {
        const staffData = res.data.data.map((data) => {
          return {
            id: data._id,
            username: data.username,
            employee_id: data.employee_id,
            email: data.email,
            first: data.name.first,
            last: data.name.last,
            middle: data.name.middle,
            role: data.role,
            isChecked:
              selectedArray && selectedArray.length > 0 && selectedArray.includes(data._id)
                ? true
                : false,
          };
        });

        let selectStaffList = [];
        if (this.state.selectStaffList && this.state.selectStaffList.length > 0) {
          selectStaffList = this.state.selectStaffList.map((list) => list.staffID);
        }
        let staffFilter =
          staffData &&
          staffData.length > 0 &&
          staffData.filter((data) => {
            return data.id !== loggedInRole && !selectStaffList.includes(data.id);
          });

        this.setState({
          staffData: staffFilter,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.setState({
          staffData: [],
          isLoading: false,
        });
      });
  };

  selectStaffList = () => {
    const { calendarId } = this.state;
    this.setState({
      isLoading: true,
    });
    axios
      .get(`institution_calendar_event/list_calendar_event_reviewer/${calendarId}`)
      .then((res) => {
        const selectStaffList = res.data.data.map((data) => {
          return {
            staffID: data._id,
            first: data.name.first,
            last: data.name.last,
            middle: data.name.middle,
            isCheckedEmail: false,
            isCheckedSms: false,
            isCheckedScheduler: false,
            isCheckedClass: false,
          };
        });

        this.setState({
          selectStaffList: selectStaffList,
          isLoading: false,
        });
      })
      .catch((error) => {
        this.props.setData({ message: error.response.data.message });
        this.setState({
          isLoading: false,
        });
      });
  };

  searchModelClose = () => {
    this.setState({
      searchView: false,
      staffData: [],
      searchText: '',
    });
    selectedArray = [];
  };

  deleteStaff = (e, data) => {
    e.preventDefault();
    this.setState({
      isLoading: true,
    });
    const { calendarId } = this.state;
    axios
      .delete(`institution_calendar_event/remove_reviewer/${calendarId}/${data.staffID}`)
      .then((res) => {
        this.setState(
          {
            isLoading: false,
            blockBtn: false,
          },
          () => {
            this.selectStaffList();
            let view = {
              _id: this.state.evId,
            };
            this.eventViewShow(e, view);
          }
        );
        this.props.setData({ message: t('Staff_Deleted_Successfully') });
      })
      .catch((error) => {
        this.props.setData({ message: error.response.data.message });
        this.setState({
          isLoading: false,
        });
      });
  };

  handleEditEventSubmit = (e) => {
    e.preventDefault();

    if (this.state.oneDay === true) {
      this.setState({
        endDate: this.state.startDate,
      });
    }

    let startDate = moment(this.state.startDate).format('YYYY-MM-DD');
    let endDate = moment(this.state.endDate).format('YYYY-MM-DD');
    let st = '';
    let et = '';

    if (this.state.editStartTime === '') {
      let startTime = timeFormat(moment(this.state.startTimeView).format('H:mm:ss'));
      st = moment(startDate + 'T' + startTime).toDate();
    } else {
      let startTime = timeFormat(moment(this.state.editStartTime).format('H:mm:ss'));
      st = moment(startDate + 'T' + startTime).toDate();
    }

    if (this.state.editEndTime === '') {
      let endTime = timeFormat(moment(this.state.endTimeView).format('H:mm:ss'));
      et = moment(endDate + 'T' + endTime).toDate();
    } else {
      let endTime = timeFormat(moment(this.state.editEndTime).format('H:mm:ss'));
      et = moment(endDate + 'T' + endTime).toDate();
    }

    let error = false;
    if (this.state.startDate !== '' && this.state.endDate !== '') {
      if (st.getTime() >= et.getTime()) {
        this.setState({ timeError: t('endTimeGreat') });
        error = true;
      } else {
        this.setState({ timeError: '' });
      }
    }

    let eventDetail = this.state.eventDetail;
    if (this.state.eventDetail === '') {
      eventDetail = ' ';
    }

    if (this.validation() && !error) {
      const data = {
        event_type: this.state.selectType,
        event_name: {
          first_language: this.state.eventName.trim(),
          second_language: this.state.arab1.trim(),
        },
        event_description: {
          first_language: eventDetail,
          second_language: this.state.arab2,
        },
        event_date: startDate,
        end_date: endDate,
        start_time: st.getTime(),
        end_time: et.getTime(),
      };

      this.setState({
        isLoading: true,
      });

      axios
        .put(`institution_calendar_event/${this.state.evId}`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              selectType: '',
              eventName: '',
              arab1: '',
              eventDetail: '',
              arab2: '',
              selectVenue: '',
              oneDay: false,
              valueCheck: false,
              eventEdit: false,
            },
            () => {
              let view = {
                _id: this.state.evId,
              };
              this.eventViewShow(e, view);
            }
          );

          this.props.setData({ message: t('event_updated_successfully') });
        })
        .catch((error) => {
          // let errorMessage='';
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  handleReviewRequest = () => {
    this.setState({
      requestView: true,
    });
  };

  handleReviewRequestClose = () => {
    this.setState({
      requestView: false,
    });
  };

  onRadioGroupChange = (e, name) => {
    if (name === 'agree') {
      this.setState({
        selectAgree: e.target.value,
        selectAgreeError: '',
      });
      if (e.target.value === 'agree') {
        this.setState({
          chooseAgree: true,
        });
      } else if (e.target.value === 'disagree') {
        this.setState({
          chooseAgree: false,
        });
      }
    }
  };

  validationReview = (index) => {
    let selectAgreeError = '';

    if (this.state.review[index].reviews === false) {
      selectAgreeError = 'Choose Agree Or Disagree';
      this.props.setData({ message: t('chooseAgreeOrDisagree') });
    }

    if (selectAgreeError) {
      this.setState({
        selectAgreeError,
      });

      return false;
    }
    return true;
  };

  onRadioGroupChangeIndex = (e, name, index) => {
    let copyState = [...this.state.review];
    let indexState = copyState[index];
    if (name === 'reviews') {
      indexState[name] = e.target.value === 'agree' ? 'agree' : 'disagree';
    } else {
      indexState[name] = e.target.value;
    }

    copyState[index] = indexState;
    this.setState({ review: copyState, editReview: true, activeIndex: index });
  };

  reviewSubmit = (e, re, index) => {
    e.preventDefault();
    const reviewerId = re.reviewerId;
    if (this.validationReview(index)) {
      const data = {
        reviewer_type: 'dean',
        _event_id: this.state.eventId,
        _reviewer_id: reviewerId,
        review: this.state.review[index].reviews === 'agree' ? true : false,
        review_comment: this.state.review[index].dean_feedback,
      };
      this.setState({
        isLoading: true,
      });

      axios
        .post(`institution_calendar_event/add_reviewer_review`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              review: [],
            },
            () => {
              let view = {
                _id: this.state.evId,
              };
              this.eventViewShow(e, view);
            }
          );

          this.props.setData({ message: t('reviewCompleted') });
        })
        .catch((error) => {
          // let errorMessage='';
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      console.log('Error in form data'); //eslint-disable-line
    }
  };

  completeReview = (e) => {
    this.setState({
      completeView: true,
      blockBtn: true,
    });
  };

  completeReviewClose = (e) => {
    this.setState({
      completeView: false,
      blockBtn: false,
    });
  };

  completeReviewSubmit = (e) => {
    const { calendarId } = this.state;
    const { loggedInUserData } = this.props;
    if (
      this.state.completeEmail !== '' ||
      this.state.completeSms !== '' ||
      this.state.completeScheduler !== '' ||
      this.state.completeClass !== ''
    ) {
      const data = {
        to: 'reviewer',
        _calendar_id: calendarId,
        message: `<p>Dear User,<br><br>
        ${formatFullName(
          loggedInUserData.get('name', Map()).toJS()
        )} has Completed the Institution Calendar Review,<br> Thank you for your valuable comments.
        <br><br>
        Best Regards<br>
        ${getEnvCollegeName()}</p>`,
        notify_via: [
          this.state.completeEmail,
          this.state.completeSms,
          this.state.completeScheduler,
          this.state.completeClass,
        ],
      };
      this.setState({
        isLoading: true,
      });

      axios
        .post(`institution_calendar_event/send_notification`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              completeView: false,
              blockBtn: true,
              completeEmail: '',
              completeSms: '',
              completeScheduler: '',
              completeClass: '',
            },
            () => {
              this.fetchApi();
            }
          );

          this.props.setData({ message: t('allReviewCompleted') });
        })
        .catch((error) => {
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      this.setState({
        completeError: t('chooseNotification'),
      });
    }
  };

  publishViewShow = (e) => {
    this.setState({
      publishView: true,
    });
  };

  publishViewClose = (e) => {
    this.setState({
      publishView: false,
    });
  };

  publishSubmit = (e) => {
    let type = '';

    const { calendarName, calendarId } = this.state;
    if (this.state.publishStaff !== '' && this.state.publishStudent !== '') {
      type = 'both';
    } else if (this.state.publishStaff !== '') {
      type = 'staff';
    } else if (this.state.publishStudent !== '') {
      type = 'student';
    } else if (this.state.publishStudent === '' && this.state.publishStudent === '') {
      this.setState({
        publishError: t('chooseSendNotify'),
      });
    }
    if (
      this.state.completeEmail !== '' ||
      this.state.completeSms !== '' ||
      this.state.completeScheduler !== '' ||
      (this.state.completeClass !== '' && type !== '')
    ) {
      const data = {
        to: 'publish',
        type: type,
        _calendar_id: calendarId,
        message: `<p>Dear User,<br><br>
      The following Institution Calendar has successfully been published.
      <br><br>
      Academic year ${calendarName}
      <br><br>
      Best Regards<br>
      ${getEnvCollegeName()}</p>`,
        notify_via: [
          this.state.completeEmail,
          this.state.completeSms,
          this.state.completeScheduler,
          this.state.completeClass,
        ],
      };

      this.setState({
        isLoading: true,
      });

      axios
        .post(`institution_calendar_event/send_notification`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              publishView: false,
              completeEmail: '',
              completeSms: '',
              completeScheduler: '',
              completeClass: '',
              publishStaff: '',
              publishStudent: '',
              type: '',
            },
            () => {
              this.fetchApi();
            }
          );

          this.props.setData({ message: t('publishedSuccessfully') });
          this.props.history.push('/InstitutionCalendar');
        })
        .catch((error) => {
          // let errorMessage='';
          this.props.setData({ message: error.response.data.message });
          this.setState({
            isLoading: false,
          });
        });
    } else {
      this.setState({
        completeError: t('chooseNotification'),
      });
    }
  };

  handleSearch = (e) => {
    this.setState(
      {
        searchText: e.target.value,
      },
      () => {
        this.searchModel();
      }
    );
  };

  render() {
    const { calendarName } = this.state;
    // let dd = moment(this.state.startTimeView).format('hh:mm A');
    // let ss = moment(this.state.endTimeView).format('hh:mm A');
    const items = [
      { to: '#', label: t('side_nav.menus.institution_calendar') },
      { label: t('role_management.tabs.Review') },
    ];
    let header = [
      { name: '', width: 'aw-50' },
      { name: t('employee_id'), width: 'aw-150' },
      { name: t('name'), width: 'aw-200' },
      { name: t('email'), width: 'aw-200' },
      { name: t('role'), width: 'aw-100' },
    ];
    //const { isCurrentCalendar } = this.props;
    const currentCalendar = this.state.isActive; //isCurrentCalendar(calendarId);

    const iconForm = lang === 'ar' ? 'icon-form-arabic' : 'icon-form';
    const iconFormCalender = lang === 'ar' ? 'icon-form-calender-arabic' : 'icon-form-calender';

    // const isIndianVersion = isIndVer();
    return (
      <React.Fragment>
        <CalenderIndx />
        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={index} to={to}>
                {label}
              </Link>
            ))}
        </Breadcrumb>
        <Loader isLoading={this.state.isLoading} />
        <div className="main" style={{ backgroundColor: '#fff' }}>
          <div className="container">
            <div className="row mt-2 pb-3">
              <div className="col-lg-12">
                <AddNew create={t('backToCalendar')} to="/InstitutionCalendar" />
                <div className="clearfix"> </div>
              </div>
              <div className="col-md-6">
                <MainTitle
                  title={t('reviewEvent')}
                  subTitle={`${t('academic_year')} ${calendarName} | ${t(
                    'side_nav.menus.institution_calendar'
                  )} | ${t('mainCalendar')}`}
                  fontsize="f-14 pt-2"
                />
              </div>

              <div className="col-md-6">
                <div className="float-right">
                  <div className="col-md-12 ">
                    {CheckPermission(
                      'tabs',
                      'Calendar',
                      'Institution Calendar',
                      '',
                      'Review',
                      'Complete Review'
                    ) && (
                      <p className="mt-3 float-left ml-2">
                        <React.Fragment>
                          {this.state.review !== '' &&
                          this.state.blockBtn === false &&
                          this.state.skip === false &&
                          this.state.selectStaffList &&
                          this.state.selectStaffList.length > 0 ? (
                            <Button variant="primary" block onClick={this.completeReview}>
                              {t('completeAllReview')}
                            </Button>
                          ) : (
                            <Button variant="secondary" block disabled>
                              {t('completeAllReview')}
                            </Button>
                          )}
                        </React.Fragment>
                      </p>
                    )}
                    {CheckPermission(
                      'tabs',
                      'Calendar',
                      'Institution Calendar',
                      '',
                      'Review',
                      'Publish'
                    ) && (
                      <>
                        <p className={`${lang === 'ar' ? '' : 'ml-3'} float-left mr-3 mt-4`}>
                          <span className={`${lang === 'ar' ? 'pl-3' : ''} mr-2`}>
                            {this.state.data.length === 0 ? (
                              <input
                                type="checkbox"
                                className="calendarFormRadio"
                                value={this.state.skip}
                              />
                            ) : (
                              <input
                                type="checkbox"
                                className="calendarFormRadio"
                                onClick={(event) => this.handleCheck(event, 'skip')}
                                value={this.state.skip}
                              />
                            )}
                          </span>
                          {t('skipReview')}
                        </p>

                        <p className="mt-3 float-right">
                          {this.state.skip === true || this.state.blockBtn === true ? (
                            <Button variant="primary" block onClick={this.publishViewShow}>
                              {t('role_management.role_actions.Publish')}
                            </Button>
                          ) : (
                            <Button variant="secondary" block disabled>
                              {t('role_management.role_actions.Publish')}
                            </Button>
                          )}
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="col-md-6">
                <h4 className="f-16 pt-4 text-left"> {t('sendEventsToReview')} </h4>
              </div>

              <div className="col-md-6">
                <div className="float-right">
                  <div className="col-md-12 ">
                    {CheckPermission(
                      'tabs',
                      'Calendar',
                      'Institution Calendar',
                      '',
                      'Review',
                      'Send Review Request'
                    ) && (
                      <p className={`${lang === 'ar' ? 'mr-5' : 'ml-5'} mt-2 float-right`}>
                        {this.state.selectStaffList.length === 0 || this.state.blockBtn === true ? (
                          <Button variant="secondary" disabled>
                            {t('role_management.role_actions.Send Review Request')}
                          </Button>
                        ) : (
                          <Button variant="primary" onClick={this.handleReviewRequest}>
                            {t('role_management.role_actions.Send Review Request')}
                          </Button>
                        )}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="col-md-12">
                {CheckPermission(
                  'tabs',
                  'Calendar',
                  'Institution Calendar',
                  '',
                  'Review',
                  'Search'
                ) && (
                  <div className="flex-container">
                    <div>
                      <p className={`${lang === 'ar' ? 'to-arabic' : 'to'}`}>
                        {' '}
                        {t('program_calendar.to')}
                      </p>
                    </div>
                    <div className="search_event">
                      <div className="inline_search text-left">
                        {this.state.selectStaffList.map((data, index) => {
                          let fullName = formatFullName(data);
                          return (
                            <span className="pr-3 " key={index}>
                              <Badge variant="secondary" className="f-12">
                                {fullName}
                                <i
                                  className="fa fa-times pl-2"
                                  aria-hidden="true"
                                  onClick={(e) => this.deleteStaff(e, data)}
                                ></i>
                              </Badge>
                            </span>
                          );
                        })}

                        <i
                          className={`fa fa-search remove_hover ${lang === 'ar' ? 'pl-4' : 'pr-4'}`}
                          aria-hidden="true"
                          onClick={this.searchModel}
                        ></i>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <hr />

            <div className="row">
              <div className="col-md-12">
                {this.state.data.length === 0 && (
                  <p className="f-22 text-center">{t('noEventData')}</p>
                )}
              </div>

              <div className="col-md-3">
                {this.state.data.map((view, index) => (
                  <div
                    key={index}
                    className="p-2 flex-container review_sideNav"
                    onClick={(e) => this.eventViewShow(e, view)}
                  >
                    <div className="flex-10">
                      <p className="f-16 mb-1">{moment(view.event_date).format('MMM Do YYYY')} </p>
                      <p className="f-14 mb-1 font-weight-bold"> {view.first_language} </p>
                    </div>

                    <div className="flex-1">
                      <p>
                        {view.status === 'Done' && (
                          <i
                            className="fa fa-check-circle tick pt-10 f-24 text-green"
                            aria-hidden="true"
                          ></i>
                        )}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="col-md-9">
                {/* event view start          */}

                {this.state.eventView === true && (
                  <div className="p-3 review_mainNav">
                    <div className="row">
                      <div className="col-md-9">
                        <h4 className="f-20 mb-1 text-left">
                          {' '}
                          {this.state.evName}{' '}
                          <Badge variant="secondary">
                            {t(`events.event_types.${this.state.evType}`)}
                          </Badge>{' '}
                        </h4>
                      </div>

                      <div className="col-md-3">
                        {currentCalendar &&
                          CheckPermission(
                            'tabs',
                            'Calendar',
                            'Institution Calendar',
                            '',
                            'Review',
                            'Edit'
                          ) && (
                            <span
                              className="remove_hover btn btn-outline-primary float-right"
                              onClick={(e) => this.eventEdit(e)}
                            >
                              {t('events.edit_event')}
                            </span>
                          )}
                      </div>

                      <div className="col-md-12 pt-2 text-left">
                        <p className="f-16 mb-2"> {this.state.evDetail}</p>
                        <p className="f-16 mb-2">
                          {' '}
                          <b>{t('Date')}:</b>{' '}
                          {this.state.evStartDate !== ''
                            ? `${t(
                                `calender.${moment(this.state.evStartDate).format('MMM')}`
                              )} ${moment(this.state.evStartDate).format('Do')}`
                            : t('loading')}{' '}
                          -{' '}
                          {this.state.evEndDate !== ''
                            ? `${t(
                                `calender.${moment(this.state.evEndDate).format('MMM')}`
                              )} ${moment(this.state.evEndDate).format('Do')}`
                            : t('loading')}{' '}
                        </p>
                        <p className="f-16 mb-2">
                          {' '}
                          <b>{t('global_configuration.time')}:</b>{' '}
                          {this.state.evStartTime !== ''
                            ? moment(this.state.evStartTime).format('hh:mm A')
                            : t('loading')}{' '}
                          -{' '}
                          {this.state.evEndTime !== ''
                            ? moment(this.state.evEndTime).format('hh:mm A')
                            : t('loading')}{' '}
                        </p>
                        {/* <p className="f-16 mb-2">
                          {" "}
                          <b>Venue:</b> Conference Hall No. 4
                        </p> */}
                      </div>
                    </div>
                  </div>
                )}

                <div className="col-md-9 pl-0 pt-5 ">
                  {this.state.review.length !== 0 && <p> {t('responses')}</p>}
                  {this.state.review.map((re, index) => (
                    <div className="pt-2" key={index}>
                      <div className="dash-inner">
                        <React.Fragment>
                          {/* pending Status start  */}
                          {re.reviews === '' && !this.state.editReview && (
                            <div className="row">
                              <div className="col-md-8">
                                <p className=" mb-0">Dr. {formatFullName(re)}</p>
                              </div>

                              <div className="col-md-4 ">
                                <div className="float-right">
                                  {re.reviews === '' && (
                                    <p className="text-right mb-0">
                                      {t('configure_levels.pending')}
                                    </p>
                                  )}
                                  {re.reviews === true && (
                                    <Button variant="outline-success" size="sm">
                                      {t('yes')}
                                    </Button>
                                  )}
                                  {re.reviews === false && (
                                    <Button variant="outline-danger" size="sm">
                                      {t('no')}
                                    </Button>
                                  )}
                                </div>
                              </div>
                              {re.comment !== '' && (
                                <div className="col-md-12 pt-2">
                                  <p className="f-14">{re.comment}</p>
                                </div>
                              )}
                            </div>
                          )}
                          {/* pending Status  end  */}

                          {/* yes Status start  */}
                          {re.reviews === true && !this.state.editReview && (
                            <div className="row">
                              <div className="col-md-8">
                                <p className=" mb-0">Dr. {formatFullName(re)} </p>
                              </div>

                              <div className="col-md-4 ">
                                <div className="float-right">
                                  {re.reviews === '' && (
                                    <p className="text-right mb-0">
                                      {t('configure_levels.pending')}
                                    </p>
                                  )}
                                  {re.reviews === true && (
                                    <Button variant="outline-success" size="sm">
                                      {t('yes')}
                                    </Button>
                                  )}
                                  {re.reviews === false && (
                                    <Button variant="outline-danger" size="sm">
                                      {t('no')}
                                    </Button>
                                  )}
                                </div>
                              </div>
                              {re.comment !== '' && (
                                <div className="col-md-12 pt-2">
                                  <p className="f-14 m-0" style={{ wordBreak: 'break-all' }}>
                                    {re.comment}
                                  </p>
                                </div>
                              )}
                            </div>
                          )}
                          {/* yes Status end  */}

                          {/* no Status end  */}
                          {(re.reviews === false || this.state.editReview) && (
                            <div className="row">
                              <div className="col-md-8">
                                <p>Dr. {formatFullName(re)}</p>
                              </div>

                              <div className="col-md-4 ">
                                <div className="float-right">
                                  {re.reviews === '' && (
                                    <p className="text-right mb-0">
                                      {t('configure_levels.pending')}
                                    </p>
                                  )}
                                  {re.reviews === true && re.comment !== '' && (
                                    <Button variant="outline-success" size="sm">
                                      {t('yes')}
                                    </Button>
                                  )}
                                  {(re.reviews === false || this.state.editReview) &&
                                    re.comment !== '' && (
                                      <Button variant="outline-danger" size="sm">
                                        {t('no')}
                                      </Button>
                                    )}
                                </div>
                              </div>

                              {re.comment !== '' && (
                                <div className="col-md-12 pt-2">
                                  <p className="f-14" style={{ wordBreak: 'break-all' }}>
                                    {re.comment}
                                  </p>
                                </div>
                              )}

                              {re.comment !== '' && (
                                <div className="col-md-12">
                                  <p> {t('reply')}</p>

                                  {re.dean_comment === '' ? (
                                    <Input
                                      elementType={'radio'}
                                      elementConfig={agree}
                                      className={'form-radio1'}
                                      selected={re.reviews}
                                      labelclass="radio-label2"
                                      onChange={(e) =>
                                        this.onRadioGroupChangeIndex(e, 'reviews', index)
                                      }
                                    />
                                  ) : re.dean_feedback === true ? (
                                    <span style={{ color: 'green', fontSize: '15px' }}>
                                      {t('agree')}
                                    </span>
                                  ) : (
                                    <span style={{ color: 'red', fontSize: '15px' }}>
                                      {t('disagree')}
                                    </span>
                                  )}

                                  <div className="pt-3" style={{ wordBreak: 'break-word' }}>
                                    {re.dean_comment === '' ? (
                                      <textarea
                                        disabled={re.dean_comment !== ''}
                                        value={
                                          re.dean_comment !== ''
                                            ? re.dean_comment
                                            : re.dean_feedback
                                        }
                                        onChange={(e) =>
                                          this.onRadioGroupChangeIndex(e, 'dean_feedback', index)
                                        }
                                        className={'form-control'}
                                        placeholder={'Add a comment'}
                                      />
                                    ) : re.dean_comment !== '' ? (
                                      re.dean_comment
                                    ) : (
                                      re.dean_feedback
                                    )}
                                  </div>
                                </div>
                              )}
                              {re.dean_comment === '' && re.comment !== '' && (
                                <div className="col-md-12 pt-3 ">
                                  <div className="float-right">
                                    <Button
                                      variant="outline-primary"
                                      onClick={(e) => this.reviewSubmit(e, re, index)}
                                    >
                                      {t('send')}
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                          {/* no Status end  */}
                        </React.Fragment>
                      </div>
                    </div>
                  ))}
                </div>

                {/* event view end          */}
              </div>
            </div>
          </div>
        </div>
        <Modal
          show={this.state.publishView}
          centered
          onHide={this.publishViewClose}
          dialogClassName="modal-30w"
          aria-labelledby="example-custom-modal-styling-title"
        >
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1">{t('publishInstitution')}</div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <div className="row justify-content-center pt-1">
              <div className="col-md-12">
                <p>{t('publishCalender')}</p>

                <p className="pt-2 pr-2 ">
                  {t('notifyUser')}
                  <span className="pl-3">
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'staff')}
                      value="checkedall"
                    />{' '}
                    {t('events.staff')}
                  </span>
                  <span className="pl-5">
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'student')}
                      value="checkedall"
                    />{' '}
                    {t('students')}
                  </span>
                </p>

                <div className="f-16 text-red text-center"> {this.state.publishError}</div>
                <div className="pt-2 pb-2"> {t('notifyVia')}</div>

                <p className={`${lang === 'ar' ? 'pl-4' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completeemail')}
                    value="checkedall"
                  />{' '}
                  {t('email')}
                </p>
                {isMobileVerifyMandatory() && (
                  <p className={`${lang === 'ar' ? '' : 'pr-4'} float-left`}>
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'completesms')}
                      value="checkedall"
                    />{' '}
                    {t('sms')}
                  </p>
                )}

                <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completescheduler')}
                    value="checkedall"
                  />{' '}
                  DigiScheduler
                </p>

                <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completeclass')}
                    value="checkedall"
                  />{' '}
                  DigiClass
                </p>

                <div>
                  {' '}
                  <p className="f-16 text-red">{this.state.completeError}</p>{' '}
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <span className="remove_hover btn btn-outline-primary" onClick={this.publishViewClose}>
              {t('events.close')}
            </span>
            {(this.state.completeClass !== '' ||
              this.state.completeEmail !== '' ||
              this.state.completeScheduler !== '' ||
              this.state.completeSms !== '') &&
              (this.state.publishStaff !== '' || this.state.publishStudent !== '') && (
                <span className="remove_hover btn btn-primary" onClick={this.publishSubmit}>
                  {t('role_management.role_actions.Publish')}
                </span>
              )}
          </Modal.Footer>
        </Modal>
        {/* publish funtion end */} {/* review request view funtion start   */}
        <Modal
          show={this.state.completeView}
          centered
          onHide={this.completeReviewClose}
          dialogClassName="modal-30w"
          aria-labelledby="example-custom-modal-styling-title"
        >
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1">{t('confirmReview')}</div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <div className="row justify-content-center pt-4">
              <div className="col-md-12">
                <p>{t('wantToCompleteReview')}? </p>

                <p className="pt-2">{t('notifyUserVia')}</p>
                <p className={`${lang === 'ar' ? 'pl-4' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completeemail')}
                    value="checkedall"
                  />{' '}
                  {t('email')}
                </p>
                {isMobileVerifyMandatory() && (
                  <p className={`${lang === 'ar' ? '' : 'pr-4'} float-left`}>
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'completesms')}
                      value="checkedall"
                    />{' '}
                    {t('sms')}
                  </p>
                )}

                <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completescheduler')}
                    value="checkedall"
                  />{' '}
                  DigiScheduler
                </p>

                <p className={`${lang === 'ar' ? 'pl-3' : 'pr-4'} float-left`}>
                  <input
                    type="checkbox"
                    className="calendarFormRadio"
                    onClick={(e) => this.handleAllCheckBox(e, 'completeclass')}
                    value="checkedall"
                  />{' '}
                  DigiClass
                </p>

                <div>
                  {' '}
                  <p className="f-16 text-red">{this.state.completeError}</p>{' '}
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <span
              className="remove_hover btn btn-outline-primary"
              onClick={this.completeReviewClose}
            >
              {t('events.close')}
            </span>

            {(this.state.completeClass !== '' ||
              this.state.completeEmail !== '' ||
              this.state.completeScheduler !== '' ||
              this.state.completeSms !== '') && (
              <span className="remove_hover btn btn-primary" onClick={this.completeReviewSubmit}>
                {t('events.submit')}
              </span>
            )}
          </Modal.Footer>
        </Modal>
        {/* complete review  send funtion end */} {/* review request view funtion start   */}
        <Modal
          show={this.state.requestView}
          centered
          size="lg"
          onHide={this.handleReviewRequestClose}
        >
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1 font-weight-bold f-18">{t('sendersList')}</div>
              <div className="col-md-12 pt-1 font-opacity">{t('selectNotificationType')}</div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <Table responsive>
              <thead className="th-change">
                <tr>
                  <th>{t('s_no')}</th>
                  <th>{t('name')}</th>
                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'email')}
                      value="checkedall"
                    />{' '}
                    {t('email')}
                  </th>

                  {isMobileVerifyMandatory() && (
                    <th>
                      {' '}
                      <input
                        type="checkbox"
                        className="calendarFormRadio"
                        onClick={(e) => this.handleAllCheckBox(e, 'sms')}
                        value="checkedall"
                      />{' '}
                      {t('sms')}
                    </th>
                  )}

                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'scheduler')}
                      value="checkedall"
                    />{' '}
                    DigiScheduler
                  </th>
                  <th>
                    {' '}
                    <input
                      type="checkbox"
                      className="calendarFormRadio"
                      onClick={(e) => this.handleAllCheckBox(e, 'class')}
                      value="checkedall"
                    />{' '}
                    DigiClass
                  </th>
                </tr>
              </thead>
              {this.state.selectStaffList.length === 0 ? (
                <TableEmptyMessage />
              ) : (
                <tbody>
                  {this.state.selectStaffList.map((data, index) => (
                    <tr
                      key={index}
                      className="tr-change"
                      style={{
                        background: data.isCheckedEmail === true ? '#D1F4FF' : '',
                      }}
                    >
                      <td>{index + 1}</td>
                      <td>{formatFullName(data)}</td>
                      <td>
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => this.handleCheckBox(event, index, 'email')}
                          value="checkedall"
                          checked={data.isCheckedEmail}
                        />
                      </td>
                      {isMobileVerifyMandatory() && (
                        <td>
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={(event) => this.handleCheckBox(event, index, 'sms')}
                            value="checkedall"
                            checked={data.isCheckedSms}
                          />
                        </td>
                      )}
                      <td>
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => this.handleCheckBox(event, index, 'scheduler')}
                          value="checkedall"
                          checked={data.isCheckedScheduler}
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(event) => this.handleCheckBox(event, index, 'class')}
                          value="checkedall"
                          checked={data.isCheckedClass}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </Table>

            <div className="row pt-4">
              <div className="col-md-4">
                <p className="pt-5 "> {t('addDeadLine')} </p>
              </div>

              <div className="col-md-4">
                {/* <Input
                  elementType={"input"}
                  elementConfig={{
                    type: "date",
                  }}
                  maxLength={25}
                  value={this.state.reviewEndDate}
                  changed={(e) => this.handleChange(e, "reviewEndDate")}
                  feedback={this.state.reviewEndDateError}
                  className={"form-control icon-form-calender"}
                  label={"End Date"}
                /> */}

                <p className="mb-2">{t('events.end_date')}</p>
                <i className="fa fa-calendar-check-o calender1" aria-hidden="true"></i>

                <DatePicker
                  selected={
                    this.state.reviewEndDate !== '' ? new Date(this.state.reviewEndDate) : ''
                  }
                  onChange={this.reviewEndDate}
                  value={this.state.reviewEndDate !== '' ? this.state.reviewEndDate : ''}
                  minDate={new Date()}
                  dateFormat="d MMM yyyy"
                  className={`form-control ${iconFormCalender}`}
                  showMonthDropdown
                  showYearDropdown
                  feedback={this.state.reviewEndDateError}
                  {...(lang === 'ar' && { locale: 'ar' })}
                />
                <div className="InvalidFeedback">{this.state.reviewEndDateError}</div>
              </div>

              <div className="col-md-4">
                <p className="mb-2"> {t('events.end_time')}</p>
                <i className="fa fa-clock-o calender" aria-hidden="true"></i>

                <DatePicker
                  selected={this.state.reviewEndTime}
                  onChange={this.reviewEndTimes}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  dateFormat="h:mm aa"
                  timeFormat="h:mm aa"
                  className={`form-control ${iconForm}`}
                  feedback={this.state.reviewEndDateError}
                  {...(lang === 'ar' && { locale: 'ar' })}
                />
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <span
              className="remove_hover btn btn-outline-primary"
              onClick={this.handleReviewRequestClose}
            >
              {t('events.close')}
            </span>
            <span className="remove_hover btn btn-primary" onClick={this.sendReview}>
              {t('events.submit')}
            </span>
          </Modal.Footer>
        </Modal>
        <Modal
          show={this.state.searchView}
          centered
          dialogClassName="modal-900"
          onHide={this.searchModelClose}
          aria-labelledby="example-custom-modal-styling-title"
        >
          <Modal.Header closeButton>
            <div className="row w-100">
              <div className="col-md-12 pt-1 f-18 font-weight-bold">{t('searchStaff')}</div>
            </div>
          </Modal.Header>

          <Modal.Body>
            <div className="row pb-2">
              <div className="col-md-12">
                <Search
                  value={this.state.searchText}
                  className="w-100"
                  onChange={(e) => this.handleSearch(e)}
                />
              </div>
            </div>
            <div className="col-md-12 pt-3">
              <div className="go-wrapper">
                <table className="table">
                  <thead>
                    <tr>
                      {header.map((header, i) => (
                        <th key={i}>
                          <div className={`${header.width} text-dark`}>{header.name}</div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  {this.state.staffData.length === 0 ? (
                    <TableEmptyMessage />
                  ) : (
                    <tbody className="go-wrapper-height">
                      {this.state.staffData &&
                        this.state.staffData.length > 0 &&
                        this.state.staffData
                          .filter(
                            (item) =>
                              item.role !== 'Dean' ||
                              !CheckPermission(
                                'pages',
                                'Calendar',
                                'Institution Calendar',
                                'Publish'
                              )
                          )
                          .map((data, index) => (
                            <tr key={index}>
                              <td>
                                <div className="aw-50">
                                  <input
                                    type="checkbox"
                                    className="calendarFormRadio"
                                    onClick={(event) =>
                                      this.handleCheckFieldElement(event, data.id)
                                    }
                                    checked={data.isChecked}
                                  />
                                </div>
                              </td>

                              <td>
                                <div className="aw-150">{data.employee_id}</div>
                              </td>
                              <td>
                                <div className="aw-200">{formatFullName(data)}</div>
                              </td>
                              <td>
                                <div className="aw-200">{data.email}</div>
                              </td>
                              <td>
                                <div className="aw-100">{data.role}</div>
                              </td>
                            </tr>
                          ))}
                    </tbody>
                  )}
                </table>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <span className="remove_hover btn btn-outline-primary" onClick={this.searchModelClose}>
              {t('events.close')}
            </span>
            <span className="remove_hover btn btn-primary" onClick={this.selectStaff}>
              {t('events.submit')}
            </span>
          </Modal.Footer>
        </Modal>
        {/* search staff view funtion end */} {/* event edit funtion start   */}
        {this.state.eventEdit && (
          <AddEditEventModal
            handleState={this.handleState}
            state={this.state}
            show={this.state.eventEdit}
            eventClose={this.eventEditClose}
            fetchApi={this.fetchApi}
            flag={'edit'}
          />
        )}
        {/* event edit funtion end */}
      </React.Fragment>
    );
  }
}

ReviewEventView.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  loggedInUserData: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  token: PropTypes.string,
  setData: PropTypes.func,
  isCurrentCalendar: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    isAuthenticated: state.auth.token !== null,
    token: state.auth.token,
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default connect(mapStateToProps, actions)(withRouter(withCalendarHooks(ReviewEventView)));
