import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>rap<PERSON>, FlexWrapper, Input, Label } from '../Styled';

const BackgroundSelect = ({ data }) => {
  const [nonRotation, setNonRotation] = data;
  return (
    <Fragment>
      <BlockWrapper>
        <p>Box color in course map</p>
        <FlexWrapper>
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#FFF4BD"
            id="#FFF4BD"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#FFF4BD"
            style={{ marginLeft: '0' }}
            className={nonRotation.type.background_color === '#FFF4BD' ? 'color selected' : 'color'}
            mg="8px"
            bg="#FFF4BD"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#BEFFBD"
            id="#BEFFBD"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#BEFFBD"
            className={nonRotation.type.background_color === '#BEFFBD' ? 'color selected' : 'color'}
            mg="8px"
            bg="#BEFFBD"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#FFBDBD"
            id="#FFBDBD"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#FFBDBD"
            className={nonRotation.type.background_color === '#FFBDBD' ? 'color selected' : 'color'}
            mg="8px"
            bg="#FFBDBD"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#FFBDE5"
            id="#FFBDE5"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#FFBDE5"
            className={nonRotation.type.background_color === '#FFBDE5' ? 'color selected' : 'color'}
            mg="8px"
            bg="#FFBDE5"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#EABDFF"
            id="#EABDFF"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#EABDFF"
            className={nonRotation.type.background_color === '#EABDFF' ? 'color selected' : 'color'}
            mg="8px"
            bg="#EABDFF"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#BDC4FF"
            id="#BDC4FF"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#BDC4FF"
            className={nonRotation.type.background_color === '#BDC4FF' ? 'color selected' : 'color'}
            mg="8px"
            bg="#BDC4FF"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#BDEFFF"
            id="#BDEFFF"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#BDEFFF"
            className={nonRotation.type.background_color === '#BDEFFF' ? 'color selected' : 'color'}
            mg="8px"
            bg="#BDEFFF"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#DADADA"
            id="#DADADA"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#DADADA"
            className={nonRotation.type.background_color === '#DADADA' ? 'color selected' : 'color'}
            mg="8px"
            bg="#DADADA"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#DFD391"
            id="#DFD391"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#DFD391"
            className={nonRotation.type.background_color === '#DFD391' ? 'color selected' : 'color'}
            mg="8px"
            bg="#DFD391"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#B6A656"
            id="#B6A656"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#B6A656"
            className={nonRotation.type.background_color === '#B6A656' ? 'color selected' : 'color'}
            mg="8px"
            bg="#B6A656"
          />
          <Input
            type="radio"
            name="background_color"
            className="none"
            value="#FFFFFF"
            id="#FFFFFF"
            onChange={(e) =>
              setNonRotation({
                type: 'ON_CHANGE',
                payload: e.target.value,
                name: e.target.name,
              })
            }
          />
          <Label
            htmlFor="#FFFFFF"
            className={nonRotation.type.background_color === '#FFFFFF' ? 'color selected' : 'color'}
            mg="8px"
            bg="#FFFFFF"
          />
        </FlexWrapper>
      </BlockWrapper>
    </Fragment>
  );
};

BackgroundSelect.propTypes = {
  data: PropTypes.object,
};

export default BackgroundSelect;
