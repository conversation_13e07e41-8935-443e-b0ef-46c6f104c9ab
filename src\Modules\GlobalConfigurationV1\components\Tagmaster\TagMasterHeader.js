import React from 'react';
import PropTypes from 'prop-types';
import { t } from 'i18next';

import AddBoxIcon from '@mui/icons-material/AddBox';

import MaterialInput from 'Widgets/FormElements/material/Input';

const TagMasterHeader = ({ handleClickPopup, title, type, handleListSearch, searchText }) => {
  return (
    <div>
      <div className="d-flex">
        <div className="title-font-size bold">{title}</div>
        <div
          className="d-flex ml-auto"
          onClick={() => {
            handleClickPopup(type, 'create');
          }}
        >
          <div>
            <AddBoxIcon className="text-primary" />
          </div>
          <div className="text-primary bold">{t('Survey_TagMaster.add_more')}</div>
        </div>
      </div>
      <div>
        <MaterialInput
          elementType="materialSearch"
          className="searchBox-font-size"
          value={searchText.get(type, '')}
          placeholder={`${t('Survey_TagMaster.search')}${
            type === 'tags'
              ? ' Tags'
              : type === 'groups'
              ? ' Groups'
              : type === 'families'
              ? ' Family'
              : ''
          } ${t('Survey_TagMaster.search_name_code')}`}
          changed={(e) => handleListSearch(e, type)}
          addClass={'rounded-border-survey'}
          size="small"
        />
      </div>
    </div>
  );
};

export default TagMasterHeader;

TagMasterHeader.propTypes = {
  handleClickPopup: PropTypes.func,
  handleListSearch: PropTypes.func,
  title: PropTypes.string,
  type: PropTypes.string,
  searchText: PropTypes.string,
};
