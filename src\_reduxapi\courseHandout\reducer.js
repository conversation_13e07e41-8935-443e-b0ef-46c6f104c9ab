import { fromJS } from 'immutable';
import * as actions from './action';

const initialState = fromJS({
  message: '',
  isLoading: false,
  userAndCourse: {},
  courseReport: {},
});

export default function (state = initialState, action) {
  switch (action.type) {
    case actions.RESET_MESSAGE_SUCCESS: {
      return state.set('message', action.message);
    }
    case actions.SET_DATA_SUCCESS: {
      return state.merge(action.data);
    }
    case actions.GET_USER_AND_COURSE_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_USER_AND_COURSE_SUCCESS: {
      return state.set('isLoading', false).set('userAndCourse', fromJS(action.data));
    }
    case actions.GET_USER_AND_COURSE_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state.set('isLoading', false).set('message', errorMessage);
    }

    case actions.GET_HANDOUT_REPORT_REQUEST: {
      return state.set('isLoading', true);
    }
    case actions.GET_HANDOUT_REPORT_SUCCESS: {
      return state.set('isLoading', false).set('courseReport', fromJS(action.data));
    }
    case actions.GET_HANDOUT_REPORT_FAILURE: {
      const errorMessage = getErrorMessage(action);
      return state.set('isLoading', false).set('message', errorMessage);
    }

    default:
      return state;
  }
}
function getErrorMessage(action) {
  const { response: { data: { message = '' } = {} } = {} } = action.error;
  return message && typeof message === 'string' ? message : 'An error occurred. Please try again.';
}
