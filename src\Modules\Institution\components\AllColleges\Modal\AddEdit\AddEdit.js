import React, { useState } from 'react';
import { Modal } from 'react-bootstrap';
import PropTypes from 'prop-types';
import UploadImage from '../../../UniversityDetails/Modal/EditComponents/UploadImage';
import Header from './Header';
import Logo from './Logo';
import EditDetails from '../../../InstitutionOnboarding/Component/InstitutionForm/InstitutionAddDetails';

function AddEdit({
  show,
  setShow,
  college,
  title,
  fetchData,
  setData,
  cancelShow,
  setIsChanged,
  handleBack,
}) {
  /* useEffect(() => {
    setName(portfolioName);
    onEditorStateChange(portfolioDescription);
  }, [portfolioName, portfolioDescription]);
  const [name, setName] = useState('');*/

  const [updatedDetails, setUpdatedDetails] = useState({
    name: college.get('name', ''),
    code: college.get('code', ''),
    type: college.get('type', ''),
    noOfColleges: college.get('noOfColleges', ''),
    accreditation: college.get('accreditation', ''),
    logo: college.get('presignedLogoURL', ''),
    isUniversity: college.get('isUniversity', false),
    address: college.getIn(['address', 'address'], ''),
    city: college.getIn(['address', 'city', 'name'], ''),
    countryId: college.getIn(['address', 'country', 'countryId'], ''),
    stateId: college.getIn(['address', 'state', 'stateId'], ''),
    state: college.getIn(['address', 'state', 'name'], ''),
    country: college.getIn(['address', 'country', 'name'], ''),
    zipCode: college.getIn(['address', 'zipCode'], ''),
    _id: college.get('_id', ''),
    district: college.getIn(['address', 'district'], ''),
    collegeCount: college.get('childInstitutionCount', 0),
  });
  const childFunc = React.useRef(null);
  const onClickSaved = () => {
    childFunc.current();
    //setShow(false);
  };

  return (
    <Modal
      onHide={() => {}}
      dialogClassName={`model-700 centered ${cancelShow ? 'display-none' : 'display-block'}`}
      show={show}
    >
      <Modal.Header className="border-none">
        <Header
          setShow={setShow}
          title={title}
          onClickSaved={onClickSaved}
          handleBack={handleBack}
        />
      </Modal.Header>
      <Modal.Body className="pt-0">
        <div className="col-md-12">
          <div className="mt-3">
            <div className="row">
              <UploadImage
                updatedDetails={updatedDetails}
                setUpdatedDetails={setUpdatedDetails}
                page={'AddEdit'}
                setData={setData}
              />
              <Logo />
            </div>
            {/* <EditDetails updatedDetails={updatedDetails} setUpdatedDetails={setUpdatedDetails} /> */}
            <EditDetails
              isGroup={false}
              childFunc={childFunc}
              getData={fetchData}
              updatedDetails={updatedDetails}
              selectedType={'college'}
              setIsChanged={setIsChanged}
            />
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
}

AddEdit.propTypes = {
  show: PropTypes.bool,
  college: PropTypes.object,
  setShow: PropTypes.func,
  setData: PropTypes.func,
  fetchData: PropTypes.func,
  title: PropTypes.string,
  cancelShow: PropTypes.bool,
  setIsChanged: PropTypes.func,
  handleBack: PropTypes.func,
};

export default AddEdit;
