import React, { useEffect, useRef, useState } from 'react';
import { Avatar, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Button from '@mui/material/Button';
import { Accordion, AccordionSummary, cardAvatarColors } from '../../style/designUtils';
import Tag from './Tag';
import AttemptType from './AttemptType';
import { useDispatch, useSelector } from 'react-redux';
import { getQaPcSetting, qaPcSaveSetting } from '_reduxapi/q360/actions';
import { selectQaPcSetting } from '_reduxapi/q360/selectors';
import { List, Map as MapI } from 'immutable';
import { useHistory } from 'react-router-dom';

let settingsData = [
  {
    SettingTitle: 'Tags',
    Action: 'Create the tags',
    tagName: 'CT',
    buttonName: 'Add New Tag',
    popTitle: 'Create Tags',
  },
  {
    SettingTitle: 'Attempt Type',
    tagName: 'AT',
    Action: 'Create attempt type for the reports',
    buttonName: 'Add New Type',
    popTitle: 'Create Attempt Type',
  },
];

const TemplateSettings = () => {
  const [expanded, setExpanded] = useState(null);
  const history = useHistory();
  const dispatch = useDispatch();
  const handleChange = (index) => {
    setExpanded((prevExpanded) => (prevExpanded === index ? null : index));
  };
  const dataRef = useRef(new Map());
  const qaPcSetting = useSelector(selectQaPcSetting);
  const tags = qaPcSetting.get('tags', List());
  const attemptType = qaPcSetting.get('attemptType', List());
  function handleSave() {
    const loopMaxSize = Math.max(
      dataRef.current.get('tags', List()).size,
      dataRef.current.get('attemptType', List()).size
    );
    const data = {
      tags: [],
      attemptType: [],
    };
    const tags = dataRef.current.get('tags', List());
    const attemptType = dataRef.current.get('attemptType', List());
    for (let i = 0; i < loopMaxSize; i++) {
      if (
        !tags.get(i, MapI()).isEmpty() &&
        (!tags.getIn([i, 'isActive'], true) ||
          !tags.hasIn([i, '_id']) ||
          tags.getIn([i, 'isEdited'], false))
      ) {
        data.tags.push(tags.get(i, MapI()).delete('isEdited'));
      }
      if (
        !attemptType.get(i, MapI()).isEmpty() &&
        (!attemptType.getIn([i, 'isActive'], true) ||
          !attemptType.hasIn([i, '_id']) ||
          attemptType.getIn([i, 'isEdited'], false))
      ) {
        data.attemptType.push(attemptType.get(i, MapI()).delete('isEdited'));
      }
    }
    const callBack = () => {
      history.push(
        '/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration+configure_template'
      );
    };

    dispatch(qaPcSaveSetting(data, callBack));
  }
  useEffect(() => {
    dispatch(getQaPcSetting());
  }, []);
  return (
    <>
      <section className="d-flex align-items-center cursor-pointer setting_icon_position">
        <div className="ml-auto mr-5">
          <Button variant="outlined">Cancel</Button>
          <Button variant="contained" className="ml-3" onClick={handleSave}>
            Save
          </Button>
        </div>
      </section>
      <section className="QAPCchild-container p-3 text-dGrey">
        {settingsData.map((setting, index) => {
          const colorCombination = cardAvatarColors.get(index % cardAvatarColors.size);
          const bgColors = colorCombination.get('bgColors');
          const color = colorCombination.get('color');
          return (
            <Accordion
              key={index}
              expanded={expanded === index}
              onChange={() => handleChange(index)}
              className="bg-white box-shadow"
              elevation={0}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />} className="py-3">
                <section className="d-flex align-items-center">
                  <Avatar
                    alt="No-icon"
                    className="cursor-pointer"
                    variant="rounded"
                    style={{
                      backgroundColor: bgColors,
                      color,
                    }}
                  >
                    <div className="f-12">{setting.tagName}</div>
                  </Avatar>
                  <div className="ml-2">
                    <div className="f-14 fw-500">{setting.SettingTitle}</div>
                    <p className="f-12 fw-400 m-0">{setting.Action}</p>
                  </div>
                </section>
              </AccordionSummary>
              <AccordionDetails>
                {index === 0 && <Tag ref={dataRef} tags={tags} buttonName={setting.buttonName} />}
                {index === 1 && (
                  <AttemptType
                    ref={dataRef}
                    attemptType={attemptType}
                    buttonName={setting.buttonName}
                  />
                )}
              </AccordionDetails>
            </Accordion>
          );
        })}
      </section>
    </>
  );
};

export default TemplateSettings;
