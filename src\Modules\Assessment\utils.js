import { Map, List } from 'immutable';
import { makeStyles } from '@mui/styles';

export const gender = [
  {
    name: 'Male',
    value: 'male',
  },
  {
    name: 'Female',
    value: 'female',
  },
];

export const assessmentValidation = (name, namesArray, setData, mode) => {
  let newArray = namesArray.set(namesArray.size, name.toLowerCase());
  if (new Set(newArray).size !== newArray.size) {
    setData(
      Map({
        message: `${mode} already exists`,
      })
    );
    return false;
  } else return true;
};

export const checkOnlyNumber = (item) => {
  return /^\d+$/.test(item);
};

export const useStylesFunction = makeStyles(() => ({
  selectRoot: {
    '& .MuiInputBase-root': {
      '&.Mui-focused fieldset': {
        borderColor: '#147AFC',
      },
    },
  },
}));

export const setTreeLevel = (item) => {
  const itemAr = item.toJS();
  return 'subTree' in itemAr
    ? item.set('level', 'one')
    : 'node' in itemAr
    ? item.set('level', 'two')
    : item.set('level', 'three');
};

export const filterAttainmentList = (array, selectedId) => {
  for (const item of array) {
    const selectedNodeId =
      selectedId === 'Overall Attainment'
        ? item.get('typeName', '') === selectedId
        : selectedId === item.get('typeId', '');
    if (selectedNodeId) return setTreeLevel(item);
    for (const item2 of item.get('subTree', List())) {
      if (item2.get('typeId', '') === selectedId) return setTreeLevel(item2);
      for (const item3 of item2.get('node', List())) {
        if (item3.get('typeId', '') === selectedId) return setTreeLevel(item3);
      }
    }
  }
};

export const getBenchmarkValues = (typeId, courseProgramReport) => {
  const filteredBenchmark = filterAttainmentList(
    courseProgramReport.get('attainmentTargetBenchMark', List()),
    typeId
  );
  const filteredLevel = filterAttainmentList(
    courseProgramReport.get('attainmentLevel', List()),
    typeId
  );
  if (filteredBenchmark?.size && filteredLevel?.size) {
    const selectedLevel = filteredLevel
      .get('levelValues', List())
      .find((item) => item.get('level', '') === filteredBenchmark.get('benchMark', ''));

    if (selectedLevel !== undefined) {
      return selectedLevel;
    }
  }

  return Map({
    min: '',
    max: '',
    percentage: '',
  });
};
