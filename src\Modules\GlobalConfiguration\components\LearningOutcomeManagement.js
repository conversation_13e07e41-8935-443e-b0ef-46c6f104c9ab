import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter } from 'react-router-dom';
import { Accordion, Card } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';

import * as actions from '../../../_reduxapi/institution/actions';
import AddEditFramework from '../modal/AddEditFramework';
import AddEditTheme from '../modal/AddEditTheme';
import Theme1 from '../../../Assets/img/globalConfiguration/theme1.svg';
import Theme2 from '../../../Assets/img/globalConfiguration/theme2.svg';
import Theme3 from '../../../Assets/img/globalConfiguration/theme3.svg';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';
import i18n from '../../../i18n';
function LearningOutcomeManagement(props) {
  const [arrow, setArrow] = useState(false);
  const [open, setOpen] = useState(false);

  const displayRow = (key, heading) => {
    return (
      <div className="row">
        <div className="col-md-11">
          <Accordion.Toggle as={Card.Header} eventKey={key} className="icon_remove_leave pl-0">
            <div className="d-flex justify-content-between">
              <div className="d-flex">
                <span
                  onClick={() => {
                    setArrow({ ...arrow, [key]: !arrow[key] });
                  }}
                >
                  <i
                    className={`fa fa-chevron-${arrow[key] ? 'up' : 'down'} f-14 icon-blue`}
                    aria-hidden="true"
                  ></i>
                </span>

                <div className="pl-3">
                  <div className="f-16 digi-brown">
                    <b>
                      <Trans i18nKey={heading}></Trans>
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Accordion.Toggle>
        </div>
      </div>
    );
  };

  const frameworkDetail = (key, frameworkName, curriculam) => {
    return (
      <div className="row">
        <div className="col-md-12">
          <Accordion.Toggle as={Card.Header} eventKey={key} className="icon_remove_leave pl-0">
            <div className="d-flex justify-content-between">
              <div className="d-flex">
                <span>
                  {arrow === true ? (
                    <i className="fa fa-chevron-up f-14" aria-hidden="true"></i>
                  ) : (
                    <i className="fa fa-chevron-down" aria-hidden="true"></i>
                  )}
                </span>

                <div className="pl-3">
                  <div className="f-16 digi-brown">
                    <b>
                      <Trans i18nKey={frameworkName}></Trans>
                    </b>
                  </div>
                  <div className="f-16">
                    <b>
                      <Trans i18nKey={'used_in'} /> : {curriculam} <Trans i18nKey={'curriculam'} />{' '}
                    </b>
                  </div>
                </div>
              </div>
              <div className="float-right">
                <img src={EditIcon} alt="edit" className="digi-pr-12 remove_hover" />

                <img src={DeleteIcon} alt="Delete" className="digi-pr-12 remove_hover" />
              </div>
            </div>
          </Accordion.Toggle>
        </div>
      </div>
    );
  };

  const handleOpen = (name) => {
    let popup = {
      ...open,
      [name]: !open[`${name}`],
    };
    setOpen(popup);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <div>
        <div>
          <div className=" pl-3">
            <Accordion defaultActiveKey="">
              {displayRow('0', i18n.t('framework'))}
              <Accordion.Collapse eventKey="0" className="bg-white">
                <Card.Body className="innerbodyLeave border-top-remove">
                  <div className="container ">
                    <div
                      className="float-right mb-2 text-blue ml-auto remove_hover"
                      onClick={() => {
                        handleOpen('framework');
                      }}
                    >
                      + <Trans i18nKey={'configuration.add_new'}></Trans>
                    </div>
                    <Accordion defaultActiveKey="" className="">
                      {frameworkDetail('0', i18n.t('Framework1'), '00')}
                      <Accordion.Collapse eventKey="0" className="bg-white">
                        <Card.Body className="innerbodyLeave border-top-remove">
                          <div className="container ">
                            <div className="mb-2 d-flex global-theme justify-content-between">
                              <div>
                                <div className="bold">
                                  <Trans i18nKey={'events.event_types.exam'}></Trans>
                                </div>
                              </div>
                              <div className="float-right">
                                <img
                                  src={EditIcon}
                                  alt="edit"
                                  className="digi-pr-12 remove_hover"
                                />

                                <img
                                  src={DeleteIcon}
                                  alt="Delete"
                                  className="digi-pr-12 remove_hover"
                                />
                              </div>
                            </div>

                            <div
                              className="float-right digi-mt-8 mb-2 text-blue ml-auto remove_hover"
                              onClick={() => {
                                handleOpen('domain');
                              }}
                            >
                              + <Trans i18nKey={'configuration.add_new'}></Trans>
                            </div>
                          </div>
                        </Card.Body>
                      </Accordion.Collapse>
                    </Accordion>
                    {open['framework'] && (
                      <AddEditFramework open={open} handleClose={() => handleClose()} />
                    )}
                    {open['domain'] && (
                      <AddEditTheme open={open} handleClose={() => handleClose()} />
                    )}
                  </div>
                </Card.Body>
              </Accordion.Collapse>
              <hr />
              {displayRow('1', i18n.t('Outcome_Naming'))}
              <Accordion.Collapse eventKey="1" className="bg-white">
                <Card.Body className="innerbodyLeave border-top-remove">
                  <div className="container">
                    <div className="float-left">
                      <FormControl component="fieldset">
                        <div className="mb-3">
                          <RadioGroup row aria-label="position" name="position" defaultValue="top">
                            <FormControlLabel
                              value="right"
                              control={<Radio color="primary" />}
                              label={i18n.t('Mode1')}
                            />
                          </RadioGroup>
                          <div className="row ml-2">
                            <img src={Theme1} alt="mode1" />
                          </div>
                        </div>
                        <div className="mb-3">
                          <RadioGroup row aria-label="position" name="position" defaultValue="top">
                            <FormControlLabel
                              value="right"
                              control={<Radio color="primary" />}
                              label={i18n.t('Mode2')}
                            />
                          </RadioGroup>
                          <div className="row ml-2">
                            <img src={Theme2} alt="mode1" />
                          </div>
                        </div>
                        <div className="mb-3">
                          <RadioGroup row aria-label="position" name="position" defaultValue="top">
                            <FormControlLabel
                              value="right"
                              control={<Radio color="primary" />}
                              label={i18n.t('course_schedule.gender_list.both')}
                            />
                          </RadioGroup>
                          <div className="row ml-2">
                            <img src={Theme3} alt="mode1" />
                          </div>
                        </div>
                      </FormControl>
                    </div>
                    <div className="clearfix"> </div>
                  </div>
                </Card.Body>
              </Accordion.Collapse>

              <hr />
            </Accordion>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}
LearningOutcomeManagement.propTypes = {
  institution: PropTypes.instanceOf(List),
};
const mapStateToProps = (state) => {
  return {};
};

export default compose(withRouter, connect(mapStateToProps, actions))(LearningOutcomeManagement);
