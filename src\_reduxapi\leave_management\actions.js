import { createAction } from '../util';
import axios from '../../axios';
import { studentGroupUrl } from '../../utils';
import { fromJS } from 'immutable';

export const RESET_MESSAGE_SUCCESS = 'RESET_MESSAGE_SUCCESS';

const setResetMessage = createAction(RESET_MESSAGE_SUCCESS, 'message');

export function resetMessage(message) {
  return function (dispatch) {
    dispatch(setResetMessage(message));
  };
}

export const SET_DATA_SUCCESS = 'SET_DATA_SUCCESS';
const setDataSuccess = createAction(SET_DATA_SUCCESS, 'data');
export function setData(data) {
  return function (dispatch) {
    dispatch(setDataSuccess(data));
  };
}

export const SET_LEAVE_DATA_SUCCESS = 'SET_LEAVE_DATA_SUCCESS';

const setLeaveDataSuccess = createAction(SET_LEAVE_DATA_SUCCESS, 'data');

export function setLeaveData(data) {
  return function (dispatch) {
    dispatch(setLeaveDataSuccess(data));
  };
}

export const GET_PERMISSION_LIST_REQUEST = 'GET_PERMISSION_LIST_REQUEST';
export const GET_PERMISSION_LIST_SUCCESS = 'GET_PERMISSION_LIST_SUCCESS';
export const GET_PERMISSION_LIST_FAILURE = 'GET_PERMISSION_LIST_FAILURE';

const getPermissionListRequest = createAction(GET_PERMISSION_LIST_REQUEST);
const getPermissionListSuccess = createAction(GET_PERMISSION_LIST_SUCCESS, 'data');
const getPermissionListFailure = createAction(GET_PERMISSION_LIST_FAILURE, 'error');

export function getAllPermissionList(type) {
  return function (dispatch) {
    dispatch(getPermissionListRequest());
    axios
      .get(`/lms/permissions/${type}`)
      .then((res) => dispatch(getPermissionListSuccess(res.data)))
      .catch((error) => dispatch(getPermissionListFailure(error)));
  };
}
export const GET_STUDENT_REQUEST = 'GET_STUDENT_REQUEST';
export const GET_STUDENT_SUCCESS = 'GET_STUDENT_SUCCESS';
export const GET_STUDENT_FAILURE = 'GET_STUDENT_FAILURE';

const getStudentListRequest = createAction(GET_STUDENT_REQUEST);
const getStudentListSuccess = createAction(GET_STUDENT_SUCCESS, 'data');
const getStudentListFailure = createAction(GET_STUDENT_FAILURE, 'error');

export function getAllStudent(calendarId, programId, search = '') {
  var endpoint = `/lms_review/student_list/${calendarId}/${programId}?limit=1000&pageNo=1${
    search !== '' ? `&search=${search}` : ``
  }`;
  return function (dispatch) {
    dispatch(getStudentListRequest());
    axios
      .get(endpoint)
      .then((res) => dispatch(getStudentListSuccess(res.data)))
      .catch((error) => dispatch(getStudentListFailure(error)));
  };
}

export const GET_STAFF_REQUEST = 'GET_STAFF_REQUEST';
export const GET_STAFF_SUCCESS = 'GET_STAFF_SUCESS';
export const GET_STAFF_FAILURE = 'GET_STAFF_FAILURE';

const getStaffListRequest = createAction(GET_STAFF_REQUEST);
const getStaffListSuccess = createAction(GET_STAFF_SUCCESS, 'data');
const getStaffListFailure = createAction(GET_STAFF_FAILURE, 'error');

export function getAllStaff(params, search) {
  var endpoint = `user/get_all/staff/${params}?limit=1000&pageNo=1`;
  if (search) {
    endpoint = `user/user_search/staff/${params}/${search}?limit=1000&pageNo=1`;
  }
  return function (dispatch) {
    dispatch(getStaffListRequest());
    axios
      .get(endpoint)
      .then((res) => dispatch(getStaffListSuccess(res.data)))
      .catch((error) => dispatch(getStaffListFailure(error)));
  };
}

export const ADD_WARNING_REQUEST = 'ADD_WARNING_REQUEST';
export const ADD_WARNING_SUCCESS = 'ADD_WARNING_SUCCESS';
export const ADD_WARNING_FAILURE = 'ADD_WARNING_FAILURE';

const addwarningrequest = createAction(ADD_WARNING_REQUEST, 'requestBody');
const addwarningsuccess = createAction(ADD_WARNING_SUCCESS, 'data');
const addwarningfailure = createAction(ADD_WARNING_FAILURE, 'error');

export function addWarning(data, cb) {
  const { ...body } = data;
  return function (dispatch) {
    dispatch(addwarningrequest());
    axios
      .post(`/lms/insert_student_warning_absence_calculation`, body)
      .then((res) => {
        dispatch(addwarningsuccess(res.data));
        cb();
      })
      .catch((error) => dispatch(addwarningfailure(error)));
  };
}

export const ROLE_ASSIGN_REQUEST = 'ROLE_ASSIGN_REQUEST';
export const ROLE_ASSIGN_SUCCESS = 'ROLE_ASSIGN_SUCCESS';
export const ROLE_ASSIGN_FAILURE = 'ROLE_ASSIGN_FAILURE';

const roleassignrequest = createAction(ROLE_ASSIGN_REQUEST, 'requestBody');
const roleassignsuccess = createAction(ROLE_ASSIGN_SUCCESS, 'data');
const roleassignfailure = createAction(ROLE_ASSIGN_FAILURE, 'error');

export function roleassign(data) {
  return function (dispatch) {
    dispatch(roleassignrequest());
    axios
      .post(`/lms/lms_roles_assign`, data)
      .then((res) => {
        dispatch(roleassignsuccess(res.data));
      })
      .catch((error) => dispatch(roleassignfailure(error)));
  };
}

export const EDIT_WARNING_REQUEST = 'EDIT_WARNING_REQUEST';
export const EDIT_WARNING_SUCCESS = 'EDIT_WARNING_SUCCESS';
export const EDIT_WARNING_FAILURE = 'EDIT_WARNING_FAILURE';

const editwarningrequest = createAction(EDIT_WARNING_REQUEST, 'requestBody');
const editarningsuccess = createAction(EDIT_WARNING_SUCCESS, 'data');
const editwarningfailure = createAction(EDIT_WARNING_FAILURE, 'error');

export function editWarning(data, cid, sid, cb) {
  const { ...body } = data;
  return function (dispatch) {
    dispatch(editwarningrequest());
    axios
      .put(`/lms/update_student_warning_absence_calculation/${cid}/${sid}`, body)
      .then((res) => {
        dispatch(editarningsuccess(res.data));
        cb();
      })
      .catch((error) => dispatch(editwarningfailure(error)));
  };
}
export const DELETE_WARNING_REQUEST = 'DELETE_WARNING_REQUEST';
export const DELETE_WARNING_SUCCESS = 'DELETE_WARNING_SUCCESS';
export const DELETE_WARNING_FAILURE = 'DELETE_WARNING_FAILURE';

const deletewarningrequest = createAction(DELETE_WARNING_REQUEST);
const deletewarningsuccess = createAction(DELETE_WARNING_SUCCESS, 'data');
const deletewarningfailure = createAction(DELETE_WARNING_FAILURE, 'error');

export function deleteWarning(data, cb) {
  return function (dispatch) {
    dispatch(deletewarningrequest());
    axios
      .delete(`/lms/delete_student_warning_absence_calculation/${data}`)
      .then((res) => {
        dispatch(deletewarningsuccess(res.data));
        cb();
      })
      .catch((error) => dispatch(deletewarningfailure(error)));
  };
}

export const GET_LEAVE_CLASSIFICATIONS_LIST_REQUEST = 'GET_LEAVE_CLASSIFICATIONS_LIST_REQUEST';
export const GET_LEAVE_CLASSIFICATIONS_LIST_SUCCESS = 'GET_LEAVE_CLASSIFICATIONS_LIST_SUCCESS';
export const GET_LEAVE_CLASSIFICATIONS_LIST_FAILURE = 'GET_LEAVE_CLASSIFICATIONS_LIST_FAILURE';

const getLeaveClassificationsListRequest = createAction(GET_LEAVE_CLASSIFICATIONS_LIST_REQUEST);
const getLeaveClassificationsListSuccess = createAction(
  GET_LEAVE_CLASSIFICATIONS_LIST_SUCCESS,
  'data'
);
const getLeaveClassificationsListFailure = createAction(
  GET_LEAVE_CLASSIFICATIONS_LIST_FAILURE,
  'error'
);

export function getAllLeaveClassifications(endpoint) {
  return function (dispatch) {
    dispatch(getLeaveClassificationsListRequest());
    axios
      .get(endpoint)
      .then((res) => dispatch(getLeaveClassificationsListSuccess(res.data)))
      .catch((error) => dispatch(getLeaveClassificationsListFailure(error)));
  };
}
export const UPDATE_PERMISSION_REQUEST = 'UPDATE_PERMISSION_REQUEST';
export const UPDATE_PERMISSION_SUCCESS = 'UPDATE_PERMISSION_SUCCESS';
export const UPDATE_PERMISSION_FAILURE = 'UPDATE_PERMISSION_FAILURE';

const updatePermissionRequest = createAction(UPDATE_PERMISSION_REQUEST, 'requestBody');
const updatePermissionSuccess = createAction(UPDATE_PERMISSION_SUCCESS, 'data');
const updatePermissionFailure = createAction(UPDATE_PERMISSION_FAILURE, 'error');

export function updatePermission(requestBody, cb) {
  return function (dispatch) {
    dispatch(updatePermissionRequest(requestBody));
    axios
      .put(`/lms/permissions/`, requestBody)
      .then((res) => {
        dispatch(updatePermissionSuccess({ data: res.data }));
        cb();
      })
      .catch((error) => dispatch(updatePermissionFailure(error)));
  };
}

export const GET_LEAVE_CATEGORIES_REQUEST = 'GET_LEAVE_CATEGORIES_REQUEST';
export const GET_LEAVE_CATEGORIES_SUCCESS = 'GET_LEAVE_CATEGORIES_SUCCESS';
export const GET_LEAVE_CATEGORIES_FAILURE = 'GET_LEAVE_CATEGORIES_FAILURE';

const getLeaveCategoriesRequest = createAction(GET_LEAVE_CATEGORIES_REQUEST);
const getLeaveCategoriesSuccess = createAction(GET_LEAVE_CATEGORIES_SUCCESS, 'data');
const getLeaveCategoriesFailure = createAction(GET_LEAVE_CATEGORIES_FAILURE, 'error');

export function getLeaveCategories() {
  return function (dispatch) {
    dispatch(getLeaveCategoriesRequest());
    axios
      .get('/lms/list')
      .then((res) => dispatch(getLeaveCategoriesSuccess(res.data)))
      .catch((error) => dispatch(getLeaveCategoriesFailure(error)));
  };
}

export const GET_MY_LEAVES_LIST_REQUEST = 'GET_MY_LEAVES_LIST_REQUEST';
export const GET_MY_LEAVES_LIST_SUCCESS = 'GET_MY_LEAVES_LIST_SUCCESS';
export const GET_MY_LEAVES_LIST_FAILURE = 'GET_MY_LEAVES_LIST_FAILURE';

const getMyLeaveListRequest = createAction(GET_MY_LEAVES_LIST_REQUEST);
const getMyLeaveListSuccess = createAction(GET_MY_LEAVES_LIST_SUCCESS, 'data');
const getMyLeaveListFailure = createAction(GET_MY_LEAVES_LIST_FAILURE, 'error');

export function getMyLeaveList({ institutionCalendarId, userId, type }) {
  return function (dispatch) {
    dispatch(getMyLeaveListRequest());

    axios
      .get(`/lms_review/${institutionCalendarId}/${userId}/${type}`)
      .then((res) => dispatch(getMyLeaveListSuccess(res.data)))
      .catch((error) => dispatch(getMyLeaveListFailure(error)));
  };
}

export const GET_ABSENCE_REPORT_REQUEST = 'GET_ABSENCE_REPORT_REQUEST';
export const GET_ABSENCE_REPORT_SUCCESS = 'GET_ABSENCE_REPORT_SUCCESS';
export const GET_ABSENCE_REPORT_FAILURE = 'GET_ABSENCE_REPORT_FAILURE';

const getabsencereportrequest = createAction(GET_ABSENCE_REPORT_REQUEST);
const getabsencereportsuccess = createAction(GET_ABSENCE_REPORT_SUCCESS, 'data');
const getabsencereportfailure = createAction(GET_ABSENCE_REPORT_FAILURE, 'error');

export function getAbsenceReport(id, callBack = null) {
  return function (dispatch) {
    dispatch(getabsencereportrequest());
    axios
      .get(`lms_review/get_report_student_absence/${id}`)
      .then((res) => {
        dispatch(getabsencereportsuccess(res.data));
        callBack();
      })
      .catch((error) => dispatch(getabsencereportfailure(error)));
  };
}

export const ABSENCE_REPORT_REQUEST = 'ABSENCE_REPORT_REQUEST';
export const ABSENCE_REPORT_SUCCESS = 'ABSENCE_REPORT_SUCCESS';
export const ABSENCE_REPORT_FAILURE = 'ABSENCE_REPORT_FAILURE';

const absenceReportRequest = createAction(ABSENCE_REPORT_REQUEST, 'requestBody');
const absenceReportSuccess = createAction(ABSENCE_REPORT_SUCCESS, 'data');
const absenceReportFailure = createAction(ABSENCE_REPORT_FAILURE, 'error');

export function absenceReport(data, id, callBack = null) {
  const formData = new FormData();
  Object.keys(data).forEach((k) => {
    formData.append(k, data[k]);
  });

  return function (dispatch) {
    dispatch(absenceReportRequest());
    if (id === 'new') {
      axios
        .post(`lms_review/student_report_absence_insert`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })

        .then((res) => {
          dispatch(absenceReportSuccess(res.data.data));
          //dispatch(studentLeaveList(data._institution_calendar_id));
          callBack();
        })
        .catch((error) => dispatch(absenceReportFailure(error)));
    } else {
      axios
        .put(`lms_review/student_report_absence_update/${id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        .then((res) => {
          dispatch(absenceReportSuccess(res.data.data));
          // dispatch(studentLeaveList(data._institution_calendar_id));
          callBack();
        })
        .catch((error) => dispatch(absenceReportFailure(error)));
    }
  };
}

export const GET_LEAVE_OVERVIEW_REQUEST = 'GET_LEAVE_OVERVIEW_REQUEST';
export const GET_LEAVE_OVERVIEW_SUCCESS = 'GET_LEAVE_OVERVIEW_SUCCESS';
export const GET_LEAVE_OVERVIEW_FAILURE = 'GET_LEAVE_OVERVIEW_FAILURE';

const getLeaveOverviewRequest = createAction(GET_LEAVE_OVERVIEW_REQUEST);
const getLeaveOverviewSuccess = createAction(GET_LEAVE_OVERVIEW_SUCCESS, 'data');
const getLeaveOverviewFailure = createAction(GET_LEAVE_OVERVIEW_FAILURE, 'error');

export function getLeaveOverview({ userId, userType, type, institutionCalendarId }) {
  return function (dispatch) {
    dispatch(getLeaveOverviewRequest());
    axios
      .get(`/lms_review/user/leave_overview/${userId}/${userType}/${type}/${institutionCalendarId}`)
      .then((res) => dispatch(getLeaveOverviewSuccess(res.data)))
      .catch((error) => dispatch(getLeaveOverviewFailure(error)));
  };
}
export const GET_PERMISSION_OVERVIEW_REQUEST = 'GET_PERMISSION_OVERVIEW_REQUEST';
export const GET_PERMISSION_OVERVIEW_SUCCESS = 'GET_PERMISSION_OVERVIEW_SUCCESS';
export const GET_PERMISSION_OVERVIEW_FAILURE = 'GET_PERMISSION_OVERVIEW_FAILURE';

const getPermissionOverviewRequest = createAction(GET_PERMISSION_OVERVIEW_REQUEST);
const getPermissionOverviewSuccess = createAction(GET_PERMISSION_OVERVIEW_SUCCESS, 'data');
const getPermissionOverviewFailure = createAction(GET_PERMISSION_OVERVIEW_FAILURE, 'error');

export function getPermissionOverview({ userId, userType, institutionCalendarId }) {
  return function (dispatch) {
    dispatch(getPermissionOverviewRequest());
    axios
      .get(`/lms_review/permission_overview/${userType}/${userId}/${institutionCalendarId}`)
      .then((res) => dispatch(getPermissionOverviewSuccess(res.data)))
      .catch((error) => dispatch(getPermissionOverviewFailure(error)));
  };
}

export const GET_SEARCH_REQUEST = 'GET_SEARCH_REQUEST';
export const GET_SEARCH_SUCCESS = 'GET_SEARCH_SUCCESS';
export const GET_SEARCH_FAILURE = 'GET_SEARCH_FAILURE';

const getSearchRequest = createAction(GET_SEARCH_REQUEST);
const getSearchSuccess = createAction(GET_SEARCH_SUCCESS, 'data');
const getSearchFailure = createAction(GET_SEARCH_FAILURE, 'error');

export function getSearch({ userType, type, institution_id, assign, textvalue }) {
  return function (dispatch) {
    dispatch(getSearchRequest());
    axios
      .get(`/lms_review/leave_search/${userType}/${type}/${assign}/${institution_id}/${textvalue}`)
      .then((res) => dispatch(getSearchSuccess(res.data)))
      .catch((error) => dispatch(getSearchFailure(error)));
  };
}

export const POST_LEAVE_REVIEW_REQUEST = 'POST_LEAVE_REVIEW_REQUEST';
export const POST_LEAVE_REVIEW_SUCCESS = 'POST_LEAVE_REVIEW_SUCCESS';
export const POST_LEAVE_REVIEW_FAILURE = 'POST_LEAVE_REVIEW_FAILURE';

const postLeavereviewRequest = createAction(POST_LEAVE_REVIEW_REQUEST);
const postLeavereviewSuccess = createAction(POST_LEAVE_REVIEW_SUCCESS, 'data');
const postLeavereviewFailure = createAction(POST_LEAVE_REVIEW_FAILURE, 'error');

export function postleave(userId, data, leaveflow, search, activetab, status, callback) {
  return function (dispatch) {
    dispatch(postLeavereviewRequest());
    axios
      .put(`/lms_review/update_leave_flow_process/${userId}`, data)
      .then((res) => {
        dispatch(
          postLeavereviewSuccess({
            status: data.leave_flow_type_status,
            type: activetab,
            data: res.data,
          })
        );
        callback && callback();
      })
      .catch((error) => dispatch(postLeavereviewFailure(error)));
  };
}
export const POST_LEAVE_REPORT_REQUEST = 'POST_LEAVE_REPORT_REQUEST';
export const POST_LEAVE_REPORT_SUCCESS = 'POST_LEAVE_REPORT_SUCCESS';
export const POST_LEAVE_REPORT_FAILURE = 'POST_LEAVE_REPORT_FAILURE';

const postLeavereportRequest = createAction(POST_LEAVE_REPORT_REQUEST);
const postLeavereportSuccess = createAction(POST_LEAVE_REPORT_SUCCESS, 'data');
const postLeavereportFailure = createAction(POST_LEAVE_REPORT_FAILURE, 'error');

export function postleavereport(data, id, callBack = null) {
  return function (dispatch) {
    dispatch(postLeavereportRequest());

    const formData = new FormData();
    Object.keys(data).forEach((k) => {
      if (k === 'schedules' && Array.isArray(data[k])) {
        data[k].forEach((d, i) => {
          Object.keys(d).forEach((key) => {
            if (key === 'schedule_id') {
              formData.append(`${k}[${i}][${key}]`, d[key]);
            } else if (key === 'substitute_staff') {
              Object.keys(d[key]).forEach((substituteStaffKey) => {
                if (substituteStaffKey === '_staff_id') {
                  formData.append(
                    `${k}[${i}][${key}][${substituteStaffKey}]`,
                    d[key][substituteStaffKey]
                  );
                } else if (substituteStaffKey === 'name') {
                  Object.keys(d[key][substituteStaffKey]).forEach((substituteStaffNameKey) => {
                    formData.append(
                      `${k}[${i}][${key}][${substituteStaffKey}][${substituteStaffNameKey}]`,
                      d[key][substituteStaffKey][substituteStaffNameKey]
                    );
                  });
                }
              });
            }
          });
        });
      } else {
        formData.append(k, data[k]);
      }
    });
    // return;
    if (id === null || id === '1') {
      axios
        .post(`/lms_review/report_staff_absence`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        .then((res) => {
          dispatch(postLeavereportSuccess(res.data));
          callBack();
        })
        .catch((error) => dispatch(postLeavereportFailure(error)));
    } else {
      axios
        .put(`/lms_review/report_staff_absence/${id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        .then((res) => {
          dispatch(postLeavereportSuccess(res.data));
          callBack();
        })
        .catch((error) => dispatch(postLeavereportFailure(error)));
    }
  };
}

export function SuspendReport(data, id, callBack = null) {
  return function (dispatch) {
    dispatch(postLeavereportRequest());

    axios
      .put(`/lms_review/cancel/${id}`, data)
      .then((res) => {
        dispatch(postLeavereportSuccess(res.data));
        callBack();
      })
      .catch((error) => dispatch(postLeavereportFailure(error)));
  };
}
export const POST_ADD_APPROVER_REQUEST = 'POST_ADD_APPROVER_REQUEST';
export const POST_ADD_APPROVER_SUCCESS = 'POST_ADD_APPROVER_SUCCESS';
export const POST_ADD_APPROVER_FAILURE = 'POST_ADD_APPROVER_FAILURE';

const postAddapproverRequest = createAction(POST_ADD_APPROVER_REQUEST);
const postAddapproverSuccess = createAction(POST_ADD_APPROVER_SUCCESS, 'data');
const postAddapproverFailure = createAction(POST_ADD_APPROVER_FAILURE, 'error');
export function addapprover(data, id, cb) {
  return function (dispatch) {
    dispatch(postAddapproverRequest());
    if (id) {
      axios
        .put(`/lms/lms_approver_edit/${id}`, data)
        .then((res) => {
          dispatch(
            postAddapproverSuccess({
              data: res.data,
              level: data.approver_level_name,
              type: 'Updated',
            })
          );
          cb();
          dispatch(getApproverList());
        })
        .catch((error) => dispatch(postAddapproverFailure(error)));
    } else {
      axios
        .post(`/lms/lms_approver_add`, data)
        .then((res) => {
          dispatch(
            postAddapproverSuccess({
              data: res.data,
              level: data.approver_level_name,
              type: 'Added',
            })
          );
          dispatch(getApproverList());
        })
        .catch((error) => dispatch(postAddapproverFailure(error)));
    }
  };
}
export const DELETE_APPROVER_REQUEST = 'DELETE_APPROVER_REQUEST';
export const DELETE_APPROVER_SUCCESS = 'DELETE_APPROVER_SUCCESS';
export const DELETE_APPROVER_FAILURE = 'DELETE_APPROVER_FAILURE';

const postDeleteapproverRequest = createAction(DELETE_APPROVER_REQUEST);
const postDeleteapproverSuccess = createAction(DELETE_APPROVER_SUCCESS, 'data');
const postDeletepproverFailure = createAction(DELETE_APPROVER_FAILURE, 'error');
export function deletestudent(id, cb) {
  return function (dispatch) {
    dispatch(postDeleteapproverRequest());

    axios
      .delete(`/lms_review/delete_report_student_absence/${id}`)
      .then((res) => {
        dispatch(postDeleteapproverSuccess(res.data));
        cb();
      })
      .catch((error) => dispatch(postDeletepproverFailure(error)));
  };
}

export function deleteapprover(data) {
  const id = data.get('_id', '');
  return function (dispatch) {
    dispatch(postDeleteapproverRequest());

    axios
      .delete(`/lms/lms_approver_remove/${id}`)
      .then((res) => {
        dispatch(
          postDeleteapproverSuccess({
            data: res.data,
            LevelName: data.get('approver_level_name', ''),
          })
        );
        dispatch(getApproverList());
      })
      .catch((error) => dispatch(postDeletepproverFailure(error)));
  };
}
export const GET_LMS_APPROVER_LIST_REQUEST = 'GET_LMS_APPROVER_LIST_REQUEST';
export const GET_LMS_APPROVER_LIST_SUCCESS = 'GET_LMS_APPROVER_LIST_SUCCESS';
export const GET_LMS_APPROVER_LIST_FAILURE = 'GET_LMS_APPROVER_LIST_FAILURE';

const getLMSApproverRequest = createAction(GET_LMS_APPROVER_LIST_REQUEST, 'isLoading');
const getLMSApproverSuccess = createAction(GET_LMS_APPROVER_LIST_SUCCESS, 'data');
const getLMSApproverFailure = createAction(GET_LMS_APPROVER_LIST_FAILURE, 'error');

export function getApproverList() {
  return function (dispatch) {
    dispatch(getLMSApproverRequest());
    axios
      .get(`/lms/lms_approver`)
      .then((res) => {
        dispatch(getLMSApproverSuccess(res.data.data.leave_approval));
      })
      .catch((error) => dispatch(getLMSApproverFailure(error)));
  };
}
export const GET_ROLES_LIST_REQUEST = 'GET_ROLES_LIST_REQUEST';
export const GET_ROLES_LIST_SUCCESS = 'GET_ROLES_LIST_SUCCESS';
export const GET_ROLES_LIST_FAILURE = 'GET_ROLES_LIST_FAILURE';

const getRolesListRequest = createAction(GET_ROLES_LIST_REQUEST, 'isLoading');
const getRolesListSuccess = createAction(GET_ROLES_LIST_SUCCESS, 'data');
const getRolesListFailure = createAction(GET_ROLES_LIST_FAILURE, 'error');

export function getRolesList(type) {
  return function (dispatch) {
    dispatch(getRolesListRequest(true));
    axios
      .get(`/role${type === 'roleAccess' ? '/listRoleBasedOnTheRoleAndPermission' : ''}`)
      .then((res) => {
        const lists = res.data.data;
        dispatch(getRolesListSuccess(lists));
      })
      .catch((error) => dispatch(getRolesListFailure(error)));
  };
}
export const GET_PROGRAMS_REQUEST = 'GET_PROGRAMS_REQUEST';
export const GET_PROGRAMS_SUCCESS = 'GET_PROGRAMS_SUCCESS';
export const GET_PROGRAMS_FAILURE = 'GET_PROGRAMS_FAILURE';

const getProgramsRequest = createAction(GET_PROGRAMS_REQUEST);
const getProgramsSuccess = createAction(GET_PROGRAMS_SUCCESS, 'data');
const getProgramsFailure = createAction(GET_PROGRAMS_FAILURE, 'error');

export function getPrograms() {
  return function (dispatch) {
    dispatch(getProgramsRequest());
    axios
      .get(`/${studentGroupUrl()}/program_ic_list_dashboard`)
      .then((res) => {
        dispatch(getProgramsSuccess(res.data.data));
      })
      .catch((error) => dispatch(getProgramsFailure(error)));
  };
}

export const GET_SCHEDULE_STATUS_REQUEST = 'GET_SCHEDULE_STATUS_REQUEST';
export const GET_SCHEDULE_STATUS_SUCCESS = 'GET_SCHEDULE_STATUS_SUCCESS';
export const GET_SCHEDULE_STATUS_FAILURE = 'GET_SCHEDULE_STATUS_FAILURE';

const getScheduleStatusRequest = createAction(GET_SCHEDULE_STATUS_REQUEST);
const getScheduleStatusSuccess = createAction(GET_SCHEDULE_STATUS_SUCCESS, 'data');
const getScheduleStatusFailure = createAction(GET_SCHEDULE_STATUS_FAILURE, 'error');

export function getScheduleStatus({ institutionCalendarId, userId, startDate, endDate }) {
  return function (dispatch) {
    dispatch(getScheduleStatusRequest());
    axios
      .get(`/lms_review/check_available/${institutionCalendarId}/${userId}/${startDate}/${endDate}`)
      .then((res) => dispatch(getScheduleStatusSuccess(res.data)))
      .catch((error) => dispatch(getScheduleStatusFailure(error)));
  };
}

// export const POST_SCHEDULE_REQUEST = 'POST_SCHEDULE_REQUEST';
// export const POST_SCHEDULE_SUCCESS = 'POST_SCHEDULE_SUCCESS';
// export const POST_SCHEDULE_SUCCESS = 'POST_SCHEDULE_SUCCESS';

// const getScheduleStatusRequest = createAction(GET_SCHEDULE_STATUS_REQUEST);
// const getScheduleStatusSuccess = createAction(GET_SCHEDULE_STATUS_SUCCESS, 'data');
// const getScheduleStatusFailure = createAction(GET_SCHEDULE_STATUS_FAILURE, 'error');

// export function postReview ({ userId, startDate, endDate }) {
//   return function (dispatch) {
//     dispatch(getScheduleStatusRequest());
//     axios
//       .get(`/lms_review/check_available/${userId}/${startDate}/${endDate}`)
//       .then((res) => dispatch(getScheduleStatusSuccess(res.data)))
//       .catch((error) => dispatch(getScheduleStatusFailure(error)));
//   };
// }

export const GET_REVIEWER_REQUEST = 'GET_REVIEWER_REQUEST';
export const GET_REVIEWER_SUCCESS = 'GET_REVIEWER_SUCCESS';
export const GET_REVIEWER_FAILURE = 'GET_REVIEWER_FAILURE';

const getReviewerRequest = createAction(GET_REVIEWER_REQUEST);
const getReviewerStatusSuccess = createAction(GET_REVIEWER_SUCCESS, 'data');
const getReviewerStatusFailure = createAction(GET_REVIEWER_FAILURE, 'error');

export function reviewer(userId, type, institutionId, roleId) {
  return function (dispatch) {
    dispatch(getReviewerRequest());
    let URL =
      userId === 'report_absence'
        ? `/lms_review/${userId}/${type}/${institutionId}`
        : `/lms_review/user_s/${userId}/${type}/${institutionId}/${roleId}`;
    axios
      .get(URL)
      .then((res) => {
        dispatch(getReviewerStatusSuccess({ data: res.data, type: userId }));
      })
      .catch((error) => dispatch(getReviewerStatusFailure(error)));
  };
}

export const GET_STUDENT_LIST_REQUEST = 'GET_STUDENT_LIST_REQUEST';
export const GET_STUDENT_LIST_SUCCESS = 'GET_STUDENT_LIST_SUCCESS';
export const GET_STUDENT_LIST_FAILURE = 'GET_STUDENT_LIST_FAILURE';

const getStudentListLeaveRequest = createAction(GET_STUDENT_LIST_REQUEST);
const getStudentListLeaveSuccess = createAction(GET_STUDENT_LIST_SUCCESS, 'data');
const getStudentListLeaveFailure = createAction(GET_STUDENT_LIST_FAILURE, 'error');

export function studentLeaveList(institutionid, programId) {
  return function (dispatch) {
    dispatch(getStudentListLeaveRequest());
    axios
      .get(`lms_review/student/${institutionid}?id=${programId}`)
      .then((res) => {
        dispatch(getStudentListLeaveSuccess(res.data));
      })
      .catch((error) => dispatch(getStudentListLeaveFailure(error)));
  };
}
export const GET_STAFF_PROFILE_REQUEST = 'GET_STAFF_PROFILE_REQUEST';
export const GET_STAFF_PROFILE_SUCCESS = 'GET_STAFF_PROFILE_SUCCESS';
export const GET_STAFF_PROFILE_FAILURE = 'GET_STAFF_PROFILE_FAILURE';

const getStaffRequest = createAction(GET_STAFF_PROFILE_REQUEST);
const getStaffStatusSuccess = createAction(GET_STAFF_PROFILE_SUCCESS, 'data');
const getStaffStatusFailure = createAction(GET_STAFF_PROFILE_FAILURE, 'error');

export function getStaff(id) {
  return function (dispatch) {
    dispatch(getStaffRequest());
    axios
      .get(`/user/user_details/${id}`)
      .then((res) => dispatch(getStaffStatusSuccess(res.data.data)))
      .catch((error) => dispatch(getStaffStatusFailure(error)));
  };
}

export const APPLY_LEAVE_REQUEST = 'APPLY_LEAVE_REQUEST';
export const APPLY_LEAVE_SUCCESS = 'APPLY_LEAVE_SUCCESS';
export const APPLY_LEAVE_FAILURE = 'APPLY_LEAVE_FAILURE';

const applyLeaveRequest = createAction(APPLY_LEAVE_REQUEST);
const applyLeaveSuccess = createAction(APPLY_LEAVE_SUCCESS, 'data');
const applyLeaveFailure = createAction(APPLY_LEAVE_FAILURE, 'error');

export function applyLeave(requestData) {
  const {
    requestBody: { _id, operation, title, ...data },
    activeid,
    history,
    status,
  } = requestData;
  return function (dispatch) {
    if (['create', 'update'].includes(operation)) {
      const formData = new FormData();
      Object.keys(data).forEach((k) => {
        if (k === 'schedules' && Array.isArray(data[k])) {
          data[k].forEach((d, i) => {
            Object.keys(d).forEach((key) => {
              if (key === 'schedule_id') {
                formData.append(`${k}[${i}][${key}]`, d[key]);
              } else if (key === 'substitute_staff') {
                Object.keys(d[key]).forEach((substituteStaffKey) => {
                  if (substituteStaffKey === '_staff_id') {
                    formData.append(
                      `${k}[${i}][${key}][${substituteStaffKey}]`,
                      d[key][substituteStaffKey]
                    );
                  } else if (substituteStaffKey === 'name') {
                    Object.keys(d[key][substituteStaffKey]).forEach((substituteStaffNameKey) => {
                      formData.append(
                        `${k}[${i}][${key}][${substituteStaffKey}][${substituteStaffNameKey}]`,
                        d[key][substituteStaffKey][substituteStaffNameKey]
                      );
                    });
                  }
                });
              }
            });
          });
        } else {
          formData.append(k, data[k]);
        }
      });

      dispatch(applyLeaveRequest());
      const method = {
        create: 'post',
        update: 'post',
      };
      axios[method[operation]](
        `/lms_review${operation === 'update' && status !== 'rejected' ? '/edit/' + _id : ''}`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        }
      )
        .then((res) => {
          dispatch(applyLeaveSuccess({ data: res.data, operation, title, type: data.type }));

          localStorage.setItem('activeLeaveid', activeid);
          history.push({
            pathname: '/leave-management/leave',
            state: {
              activeTab: parseInt(activeid),
            },
          });
        })
        .catch((error) => dispatch(applyLeaveFailure(error)));
    }
  };
}

export const CANCEL_LEAVE_REQUEST = 'CANCEL_LEAVE_REQUEST';
export const CANCEL_LEAVE_SUCCESS = 'CANCEL_LEAVE_SUCCESS';
export const CANCEL_LEAVE_FAILURE = 'CANCEL_LEAVE_FAILURE';

const cancelLeaveRequest = createAction(CANCEL_LEAVE_REQUEST);
const cancelLeaveSuccess = createAction(CANCEL_LEAVE_SUCCESS, 'data');
const cancelLeaveFailure = createAction(CANCEL_LEAVE_FAILURE, 'error');

export function cancelLeave({ id, title, ...data }, cb) {
  return function (dispatch) {
    dispatch(cancelLeaveRequest());
    axios
      .put(`/lms_review/cancel/${id}`)
      .then((res) => {
        dispatch(cancelLeaveSuccess({ id, title, data: res.data }));
        dispatch(getMyLeaveList(data));
        cb();
      })
      .catch((error) => dispatch(cancelLeaveFailure(error)));
  };
}

export const GET_LEAVE_BY_ID_REQUEST = 'GET_LEAVE_BY_ID_REQUEST';
export const GET_LEAVE_BY_ID_SUCCESS = 'GET_LEAVE_BY_ID_SUCCESS';
export const GET_LEAVE_BY_ID_FAILURE = 'GET_LEAVE_BY_ID_FAILURE';

const getLeaveByIdRequest = createAction(GET_LEAVE_BY_ID_REQUEST);
const getLeaveByIdSuccess = createAction(GET_LEAVE_BY_ID_SUCCESS, 'data');
const getLeaveByIdFailure = createAction(GET_LEAVE_BY_ID_FAILURE, 'error');

export function getLeaveById({ id }) {
  return function (dispatch) {
    dispatch(getLeaveByIdRequest());

    axios
      .get(`/lms_review/${id}`)
      .then((res) => dispatch(getLeaveByIdSuccess(res.data)))
      .catch((error) => dispatch(getLeaveByIdFailure(error)));
  };
}

export const UPDATE_LEAVE_CATEGORY_REQUEST = 'UPDATE_LEAVE_CATEGORY_REQUEST';
export const UPDATE_LEAVE_CATEGORY_SUCCESS = 'UPDATE_LEAVE_CATEGORY_SUCCESS';
export const UPDATE_LEAVE_CATEGORY_FAILURE = 'UPDATE_LEAVE_CATEGORY_FAILURE';

const updateLeaveCategoryRequest = createAction(UPDATE_LEAVE_CATEGORY_REQUEST, 'requestBody');
const updateLeaveCategorySuccess = createAction(UPDATE_LEAVE_CATEGORY_SUCCESS, 'data');
const updateLeaveCategoryFailure = createAction(UPDATE_LEAVE_CATEGORY_FAILURE, 'error');

export function updateLeaveCategory({ operation, categoryId, ...requestBody }, componentName) {
  return function (dispatch) {
    if (['create', 'update', 'delete'].includes(operation)) {
      dispatch(updateLeaveCategoryRequest(requestBody));
      const method = {
        create: 'post',
        update: 'put',
        delete: 'delete',
      };
      axios[method[operation]](
        `/lms${operation !== 'create' ? '/' + categoryId : ''}`,
        ['create', 'update'].includes(operation) ? requestBody : {}
      )

        .then((res) => {
          dispatch(
            updateLeaveCategorySuccess({
              data: res.data,
              operation,
              type: requestBody.category_type,
              componentName: componentName,
            })
          );
          dispatch(getLeaveCategories());
        })

        .catch((error) => dispatch(updateLeaveCategoryFailure(error)));
    }
  };
}

export const UPDATE_HR_CONTACT_REQUEST = 'UPDATE_HR_CONTACT_REQUEST';
export const UPDATE_HR_CONTACT_SUCCESS = 'UPDATE_HR_CONTACT_SUCCESS';
export const UPDATE_HR_CONTACT_FAILURE = 'UPDATE_HR_CONTACT_FAILURE';

const updateHrContactRequest = createAction(UPDATE_HR_CONTACT_REQUEST, 'requestBody');
const updateHrContactSuccess = createAction(UPDATE_HR_CONTACT_SUCCESS, 'data');
const updateHrContactFailure = createAction(UPDATE_HR_CONTACT_FAILURE, 'error');

export function HrContact(data) {
  return function (dispatch) {
    dispatch(updateHrContactRequest());
    axios
      .put(`/lms/hr`, data)

      .then((res) => {
        dispatch(updateHrContactSuccess(res.data));
        dispatch(getLeaveCategories());
      })

      .catch((error) => dispatch(updateHrContactFailure(error)));
  };
}

export const UPDATE_LEAVE_TYPE_REQUEST = 'UPDATE_LEAVE_TYPE_REQUEST';
export const UPDATE_LEAVE_TYPE_SUCCESS = 'UPDATE_LEAVE_TYPE_SUCCESS';
export const UPDATE_LEAVE_TYPE_FAILURE = 'UPDATE_LEAVE_TYPE_FAILURE';

const updateLeaveTypeRequest = createAction(UPDATE_LEAVE_TYPE_REQUEST);
const updateLeaveTypeSuccess = createAction(UPDATE_LEAVE_TYPE_SUCCESS, 'data');
const updateLeaveTypeFailure = createAction(UPDATE_LEAVE_TYPE_FAILURE, 'error');

export function updateLeaveType(
  { operation, categoryId, leaveTypeId, ...requestBody },
  componentname
) {
  return function (dispatch) {
    if (['create', 'update'].includes(operation)) {
      dispatch(updateLeaveTypeRequest(requestBody));
      const method = {
        create: 'post',
        update: 'put',
      };
      axios[method[operation]](
        `/lms/leavetype${operation !== 'create' ? '/' + categoryId + '/' + leaveTypeId : ''}`,
        ['create', 'update'].includes(operation) ? requestBody : {}
      )
        .then((res) => {
          dispatch(
            updateLeaveTypeSuccess({ data: res.data, operation, ComponentName: componentname })
          );
          dispatch(getLeaveCategories());
        })
        .catch((error) => dispatch(updateLeaveTypeFailure(error)));
    }
  };
}

export const DELETE_LEAVE_TYPE_REQUEST = 'DELETE_LEAVE_TYPE_REQUEST';
export const DELETE_LEAVE_TYPE_SUCCESS = 'DELETE_LEAVE_TYPE_SUCCESS';
export const DELETE_LEAVE_TYPE_FAILURE = 'DELETE_LEAVE_TYPE_FAILURE';

const deleteLeaveTypeRequest = createAction(DELETE_LEAVE_TYPE_REQUEST);
const deleteLeaveTypeSuccess = createAction(DELETE_LEAVE_TYPE_SUCCESS, 'data');
const deleteLeaveTypeFailure = createAction(DELETE_LEAVE_TYPE_FAILURE, 'error');

export function deleteLeaveType({ categoryId, leaveTypeId }) {
  return function (dispatch) {
    dispatch(deleteLeaveTypeRequest());
    axios
      .delete(`/lms/leave_type/delete/${categoryId}/leave/${leaveTypeId}`, {})
      .then((res) => {
        dispatch(deleteLeaveTypeSuccess({ data: res.data }));
        dispatch(getLeaveCategories());
      })
      .catch((error) => dispatch(deleteLeaveTypeFailure(error)));
  };
}

export const DEACTIVATION_LEAVE_TYPE_REQUEST = 'DEACTIVATION_LEAVE_TYPE_REQUEST';
export const DEACTIVATION_LEAVE_TYPE_SUCCESS = 'DEACTIVATION_LEAVE_TYPE_SUCCESS';
export const DEACTIVATION_LEAVE_TYPE_FAILURE = 'DEACTIVATION_LEAVE_TYPE_FAILURE';

const deactivationLeaveTypeRequest = createAction(DEACTIVATION_LEAVE_TYPE_REQUEST);
const deactivationLeaveTypeSuccess = createAction(DEACTIVATION_LEAVE_TYPE_SUCCESS, 'data');
const deactivationLeaveTypeFailure = createAction(DEACTIVATION_LEAVE_TYPE_FAILURE, 'error');

export function deactivateLeaveType({ categoryId, leaveTypeId, ...requestBody }) {
  return function (dispatch) {
    dispatch(deactivationLeaveTypeRequest(requestBody));
    axios
      .put(`/lms/leavetype/activate_deactivate/${categoryId}/${leaveTypeId}`, requestBody)
      .then((res) => {
        dispatch(deactivationLeaveTypeSuccess({ data: res.data, requestBody }));
        dispatch(getLeaveCategories());
      })

      .catch((error) => dispatch(deactivationLeaveTypeFailure(error)));
  };
}

export const SET_BREADCRUMB_SUCCESS = 'SET_BREADCRUMB_SUCCESS';

const setBreadCrumbSuccess = createAction(SET_BREADCRUMB_SUCCESS, 'breadcrumbs');
export const setBreadCrumbName = (name) => {
  return (dispatch) => {
    dispatch(setBreadCrumbSuccess(name));
  };
};

export const UPDATE_RP_ABSENCE_REQUEST = 'UPDATE_RP_ABSENCE_REQUEST';
export const UPDATE_RP_ABSENCE_SUCCESS = 'UPDATE_RP_ABSENCE_SUCCESS';
export const UPDATE_RP_ABSENCE_FAILURE = 'UPDATE_RP_ABSENCE_FAILURE';

const updateReportAbsenceRequest = createAction(UPDATE_RP_ABSENCE_REQUEST, 'requestBody');
const updateReportAbsenceSuccess = createAction(UPDATE_RP_ABSENCE_SUCCESS, 'data');
const updateReportAbsenceFailure = createAction(UPDATE_RP_ABSENCE_FAILURE, 'error');

export function updateReportAbsence(requestBody, cb) {
  return function (dispatch) {
    dispatch(updateReportAbsenceRequest(requestBody));
    axios
      .put(`/lms/report_absence`, requestBody)
      .then(() => {
        dispatch(updateReportAbsenceSuccess());
        cb();
      })

      .catch((error) => dispatch(updateReportAbsenceFailure(error)));
  };
}

export const GET_STUDENT_REGISTER_PROGRAM_LIST_REQUEST =
  'GET_STUDENT_REGISTER_PROGRAM_LIST_REQUEST';
export const GET_STUDENT_REGISTER_PROGRAM_LIST_SUCCESS =
  'GET_STUDENT_REGISTER_PROGRAM_LIST_SUCCESS';
export const GET_STUDENT_REGISTER_PROGRAM_LIST_FAILURE =
  'GET_STUDENT_REGISTER_PROGRAM_LIST_FAILURE';

const getStudentRegisterProgramListRequest = createAction(
  GET_STUDENT_REGISTER_PROGRAM_LIST_REQUEST
);
const getStudentRegisterProgramListSuccess = createAction(
  GET_STUDENT_REGISTER_PROGRAM_LIST_SUCCESS,
  'data'
);
const getStudentRegisterProgramListFailure = createAction(
  GET_STUDENT_REGISTER_PROGRAM_LIST_FAILURE,
  'error'
);

export function getStudentRegisterProgramList({
  institutionCalendarId,
  userId,
  roleId,
  selectedPrograms,
  roleName = '',
}) {
  return function (dispatch) {
    dispatch(getStudentRegisterProgramListRequest());
    axios
      .get(`/lms_review/program_course_list/${institutionCalendarId}/${userId}/${roleId}`)
      .then((res) => {
        const response = res.data.data;
        let data = [];
        if (response) {
          data = response.data || [];
        }
        if (roleName !== 'Course Coordinator' && data.length > 0) {
          data = data.filter((program) => selectedPrograms.includes(program._id));
        }
        dispatch(getStudentRegisterProgramListSuccess(data));
      })
      .catch((error) => dispatch(getStudentRegisterProgramListFailure(error)));
  };
}

export const GET_STUDENT_REGISTER_LIST_REQUEST = 'GET_STUDENT_REGISTER_LIST_REQUEST';
export const GET_STUDENT_REGISTER_LIST_SUCCESS = 'GET_STUDENT_REGISTER_LIST_SUCCESS';
export const GET_STUDENT_REGISTER_LIST_FAILURE = 'GET_STUDENT_REGISTER_LIST_FAILURE';

const getStudentRegisterListRequest = createAction(GET_STUDENT_REGISTER_LIST_REQUEST);
const getStudentRegisterListSuccess = createAction(GET_STUDENT_REGISTER_LIST_SUCCESS, 'data');
const getStudentRegisterListFailure = createAction(GET_STUDENT_REGISTER_LIST_FAILURE, 'error');

export function getStudentRegisterList({
  institutionCalendarId,
  programId,
  courseId,
  levelNo,
  term,
  gender,
  rotationCount,
}) {
  return function (dispatch) {
    dispatch(getStudentRegisterListRequest());
    axios
      .get(
        `/lms_review/get_course_students_register/${institutionCalendarId}/${programId}/${courseId}/${levelNo}/${term}/${gender}${
          rotationCount !== undefined && rotationCount !== 0 && rotationCount !== null
            ? `?rotationNo=${rotationCount}`
            : ''
        }`
      )
      // .get(
      //   `/lms_review/get_course_students_register/60d05e6d8ff3991b21c9bc2c/60a4e13350e71413f7c9c385/60a50e8f48e026edc6b9faa5/Level%203/interim/male`
      // )
      .then((res) => {
        const response = res.data.data;
        dispatch(getStudentRegisterListSuccess(typeof response === 'string' ? [] : response));
      })
      .catch((error) => dispatch(getStudentRegisterListFailure(error)));
  };
}

export const GET_CRITERIA_MANIPULATION_REQUEST = 'GET_CRITERIA_MANIPULATION_REQUEST';
export const GET_CRITERIA_MANIPULATION_SUCCESS = 'GET_CRITERIA_MANIPULATION_SUCCESS';
export const GET_CRITERIA_MANIPULATION_FAILURE = 'GET_CRITERIA_MANIPULATION_FAILURE';

const getCriteriaManipulationRequest = createAction(GET_CRITERIA_MANIPULATION_REQUEST);
const getCriteriaManipulationSuccess = createAction(GET_CRITERIA_MANIPULATION_SUCCESS, 'data');
const getCriteriaManipulationFailure = createAction(GET_CRITERIA_MANIPULATION_FAILURE, 'error');

export function getCriteriaManipulation({
  institutionCalendarId,
  programId,
  courseId,
  levelNo,
  term,
  gender,
  absencePercentage,
  selectAll = [],
}) {
  return function (dispatch) {
    dispatch(getCriteriaManipulationRequest());
    axios
      .get(
        `/studentCriteria/criteriaManipulationAbsence/${institutionCalendarId}/${programId}/${courseId}/${levelNo}/${term}/${gender}/${absencePercentage}`,
        { params: selectAll.length > 0 && { studentIds: selectAll } }
      )
      .then((res) => dispatch(getCriteriaManipulationSuccess(res.data.data)))
      .catch((error) => dispatch(getCriteriaManipulationFailure(error)));
  };
}

export const UPDATE_ST_ABSENCE_PERCENTAGE_REQUEST = 'UPDATE_ST_ABSENCE_PERCENTAGE_REQUEST';
export const UPDATE_ST_ABSENCE_PERCENTAGE_SUCCESS = 'UPDATE_ST_ABSENCE_PERCENTAGE_SUCCESS';
export const UPDATE_ST_ABSENCE_PERCENTAGE_FAILURE = 'UPDATE_ST_ABSENCE_PERCENTAGE_FAILURE';

const updateStudentAbsencePercentageRequest = createAction(
  UPDATE_ST_ABSENCE_PERCENTAGE_REQUEST,
  'requestBody'
);
const updateStudentAbsencePercentageSuccess = createAction(
  UPDATE_ST_ABSENCE_PERCENTAGE_SUCCESS,
  'data'
);
const updateStudentAbsencePercentageFailure = createAction(
  UPDATE_ST_ABSENCE_PERCENTAGE_FAILURE,
  'error'
);

export function updateStudentAbsencePercentage(requestBody, cb, status = false) {
  return function (dispatch) {
    dispatch(updateStudentAbsencePercentageRequest(requestBody));
    axios
      .put(`/studentCriteria/studentCriteriaChange`, requestBody)
      .then(() => {
        dispatch(updateStudentAbsencePercentageSuccess());
        if (status) {
          dispatch(
            getStudentAttendanceSheetDetails({
              institutionCalendarId: requestBody.institutionCalendarId,
              programId: requestBody.programId,
              courseId: requestBody.courseId,
              yearNo: requestBody.yearNo,
              levelNo: requestBody.levelNo,
              term: requestBody.term,
              type: 'regular',
              userId: requestBody.studentIds[0],
              rotationCount: requestBody.rotationCount,
            })
          );
        }
        dispatch(
          getStudentRegisterList({
            institutionCalendarId: requestBody.institutionCalendarId,
            programId: requestBody.programId,
            courseId: requestBody.courseId,
            levelNo: requestBody.levelNo,
            term: requestBody.term,
            gender: requestBody.gender,
            rotationCount: requestBody.rotationCount,
          })
        );
        cb && cb();
      })

      .catch((error) => dispatch(updateStudentAbsencePercentageFailure(error)));
  };
}

export const GET_BLACK_BOX_CONTENT_REQUEST = 'GET_BLACK_BOX_CONTENT_REQUEST';
export const GET_BLACK_BOX_CONTENT_SUCCESS = 'GET_BLACK_BOX_CONTENT_SUCCESS';
export const GET_BLACK_BOX_CONTENT_FAILURE = 'GET_BLACK_BOX_CONTENT_FAILURE';

const getStudentBlackBoxContentRequest = createAction(GET_BLACK_BOX_CONTENT_REQUEST);
const getStudentBlackBoxContentSuccess = createAction(GET_BLACK_BOX_CONTENT_SUCCESS, 'data');
const getStudentBlackBoxContentFailure = createAction(GET_BLACK_BOX_CONTENT_FAILURE, 'error');

export const getStudentBlackBoxContent = (url, callback) => {
  return function (dispatch) {
    dispatch(getStudentBlackBoxContentRequest());
    axios
      .get(url)
      .then((res) => {
        const response = res.data.data;
        dispatch(getStudentBlackBoxContentSuccess(response));
        callback && callback();
      })
      .catch((error) => dispatch(getStudentBlackBoxContentFailure(error)));
  };
};

export const GET_STUDENT_ATTN_SHEET_DETAILS_REQUEST = 'GET_STUDENT_ATTN_SHEET_DETAILS_REQUEST';
export const GET_STUDENT_ATTN_SHEET_DETAILS_SUCCESS = 'GET_STUDENT_ATTN_SHEET_DETAILS_SUCCESS';
export const GET_STUDENT_ATTN_SHEET_DETAILS_FAILURE = 'GET_STUDENT_ATTN_SHEET_DETAILS_FAILURE';

const getStudentAttendanceSheetDetailsRequest = createAction(
  GET_STUDENT_ATTN_SHEET_DETAILS_REQUEST,
  'isLoading'
);
const getStudentAttendanceSheetDetailsSuccess = createAction(
  GET_STUDENT_ATTN_SHEET_DETAILS_SUCCESS,
  'data'
);
const getStudentAttendanceSheetDetailsFailure = createAction(
  GET_STUDENT_ATTN_SHEET_DETAILS_FAILURE,
  'error'
);

export function getStudentAttendanceSheetDetails({
  institutionCalendarId,
  programId,
  courseId,
  yearNo,
  levelNo,
  term,
  type,
  userId,
  rotationCount,
  callBack,
  isLoading,
}) {
  return function (dispatch) {
    dispatch(getStudentAttendanceSheetDetailsRequest(isLoading));
    axios
      .get(
        `/lms_review/students_attendance_sheet/${institutionCalendarId}/${programId}/${courseId}/${yearNo}/${levelNo}/${term}/${type}/${userId}${
          rotationCount !== undefined && rotationCount !== 0 && rotationCount !== null
            ? `?rotationCount=${rotationCount}`
            : ''
        }`
      )
      .then((res) => {
        const response = res.data.data;
        dispatch(getStudentAttendanceSheetDetailsSuccess(response));
        callBack && callBack();
      })
      .catch((error) => dispatch(getStudentAttendanceSheetDetailsFailure(error)));
  };
}

export const UPDATE_REASON_REQUEST = 'UPDATE_REASON_REQUEST';
export const UPDATE_REASON_SUCCESS = 'UPDATE_REASON_SUCCESS';
export const UPDATE_REASON_FAILURE = 'UPDATE_REASON_FAILURE';

const updateReasonRequest = createAction(UPDATE_REASON_REQUEST, 'requestBody');
const updateReasonSuccess = createAction(UPDATE_REASON_SUCCESS, 'data');
const updateReasonFailure = createAction(UPDATE_REASON_FAILURE, 'error');

export function updateReason(requestBody, scheduleId, studentId, cb) {
  return function (dispatch) {
    dispatch(updateReasonRequest(requestBody));
    axios
      .put(`/lms_review/reason_update/${scheduleId}/${studentId}`, requestBody)
      .then(() => {
        dispatch(updateReasonSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateReasonFailure(error)));
  };
}

export const GET_LMS_SETTINGS_REQUEST = 'GET_LMS_SETTINGS_REQUEST';
export const GET_LMS_SETTINGS_SUCCESS = 'GET_LMS_SETTINGS_SUCCESS';
export const GET_LMS_SETTINGS_FAILURE = 'GET_LMS_SETTINGS_FAILURE';

const getLmsSettingsRequest = createAction(GET_LMS_SETTINGS_REQUEST);
const getLmsSettingsSuccess = createAction(GET_LMS_SETTINGS_SUCCESS, 'data');
const getLmsSettingsFailure = createAction(GET_LMS_SETTINGS_FAILURE, 'error');

export function getLmsSettings(classificationType, callback) {
  return function (dispatch) {
    dispatch(getLmsSettingsRequest());
    axios
      .get(`/lmsStudentSetting/getSetting?classificationType=${classificationType}`)
      .then((res) => {
        dispatch(getLmsSettingsSuccess(res.data.data));
        callback && callback(res.data.data);
      })
      .catch((error) => dispatch(getLmsSettingsFailure(error)));
  };
}

export const UPDATE_GLOBAL_CONFIG_REQUEST = 'UPDATE_GLOBAL_CONFIG_REQUEST';
export const UPDATE_GLOBAL_CONFIG_SUCCESS = 'UPDATE_GLOBAL_CONFIG_SUCCESS';
export const UPDATE_GLOBAL_CONFIG_FAILURE = 'UPDATE_GLOBAL_CONFIG_FAILURE';

const updateGlobalConfigRequest = createAction(UPDATE_GLOBAL_CONFIG_REQUEST, 'requestBody');
const updateGlobalConfigSuccess = createAction(UPDATE_GLOBAL_CONFIG_SUCCESS, 'data');
const updateGlobalConfigFailure = createAction(UPDATE_GLOBAL_CONFIG_FAILURE, 'error');

export function updateGlobalConfig(requestBody, settingId, cb) {
  return function (dispatch) {
    dispatch(updateGlobalConfigRequest(requestBody));
    axios
      .put(`/lmsStudentSetting/updateGeneral/${settingId}`, requestBody)
      .then(() => {
        dispatch(updateGlobalConfigSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateGlobalConfigFailure(error)));
  };
}

export const ADD_CATEGORY_REQUEST = 'ADD_CATEGORY_REQUEST';
export const ADD_CATEGORY_SUCCESS = 'ADD_CATEGORY_SUCCESS';
export const ADD_CATEGORY_FAILURE = 'ADD_CATEGORY_FAILURE';

const addCategoryRequest = createAction(ADD_CATEGORY_REQUEST, 'requestBody');
const addCategorySuccess = createAction(ADD_CATEGORY_SUCCESS, 'data');
const addCategoryFailure = createAction(ADD_CATEGORY_FAILURE, 'error');

export function addCategory(body, cb) {
  return function (dispatch) {
    dispatch(addCategoryRequest());
    axios
      .post(`/lmsStudentSetting/createCategory`, body)
      .then((res) => {
        dispatch(addCategorySuccess(res.data));
        cb && cb();
      })
      .catch((error) => dispatch(addCategoryFailure(error)));
  };
}

export const DELETE_CATEGORY_REQUEST = 'DELETE_CATEGORY_REQUEST';
export const DELETE_CATEGORY_SUCCESS = 'DELETE_CATEGORY_SUCCESS';
export const DELETE_CATEGORY_FAILURE = 'DELETE_CATEGORY_FAILURE';

const deleteCategoryRequest = createAction(DELETE_CATEGORY_REQUEST);
const deleteCategorySuccess = createAction(DELETE_CATEGORY_SUCCESS, 'data');
const deleteCategoryFailure = createAction(DELETE_CATEGORY_FAILURE, 'error');

export function deleteCategory(data, cb) {
  return function (dispatch) {
    dispatch(deleteCategoryRequest());
    axios
      .delete(`/lmsStudentSetting/removeCategory`, { data })
      .then((res) => {
        dispatch(deleteCategorySuccess(res.data));
        cb && cb();
      })
      .catch((error) => dispatch(deleteCategoryFailure(error)));
  };
}

export const ADD_CATEGORY_TYPES_REQUEST = 'ADD_CATEGORY_TYPES_REQUEST';
export const ADD_CATEGORY_TYPES_SUCCESS = 'ADD_CATEGORY_TYPES_SUCCESS';
export const ADD_CATEGORY_TYPES_FAILURE = 'ADD_CATEGORY_TYPES_FAILURE';

const addCategoryTypeRequest = createAction(ADD_CATEGORY_TYPES_REQUEST, 'requestBody');
const addCategoryTypeSuccess = createAction(ADD_CATEGORY_TYPES_SUCCESS, 'data');
const addCategoryTypeFailure = createAction(ADD_CATEGORY_TYPES_FAILURE, 'error');

export function addCategoryType(body, cb) {
  return function (dispatch) {
    dispatch(addCategoryTypeRequest());
    axios
      .put(`/lmsStudentSetting/createTypes`, body)
      .then((res) => {
        dispatch(addCategoryTypeSuccess(res.data));
        cb && cb();
      })
      .catch((error) => dispatch(addCategoryTypeFailure(error)));
  };
}

export const UPDATE_TYPE_REQUEST = 'UPDATE_TYPE_REQUEST';
export const UPDATE_TYPE_SUCCESS = 'UPDATE_TYPE_SUCCESS';
export const UPDATE_TYPE_FAILURE = 'UPDATE_TYPE_FAILURE';

const updateTypeRequest = createAction(UPDATE_TYPE_REQUEST, 'requestBody');
const updateTypeSuccess = createAction(UPDATE_TYPE_SUCCESS, 'data');
const updateTypeFailure = createAction(UPDATE_TYPE_FAILURE, 'error');

export function updateType(body) {
  return function (dispatch) {
    dispatch(updateTypeRequest(body));
    axios
      .put(`/lmsStudentSetting/updateTypes`, body)
      .then(() => {
        dispatch(updateTypeSuccess());
      })
      .catch((error) => dispatch(updateTypeFailure(error)));
  };
}

export const DELETE_TYPE_REQUEST = 'DELETE_TYPE_REQUEST';
export const DELETE_TYPE_SUCCESS = 'DELETE_TYPE_SUCCESS';
export const DELETE_TYPE_FAILURE = 'DELETE_TYPE_FAILURE';

const deleteTypeRequest = createAction(DELETE_TYPE_REQUEST);
const deleteTypeSuccess = createAction(DELETE_TYPE_SUCCESS, 'data');
const deleteTypeFailure = createAction(DELETE_TYPE_FAILURE, 'error');

export function deleteType(data, cb) {
  return function (dispatch) {
    dispatch(deleteTypeRequest());
    axios
      .delete(`/lmsStudentSetting/removeCategoryTypes`, { data })
      .then((res) => {
        dispatch(deleteTypeSuccess(res.data));
        cb && cb();
      })
      .catch((error) => dispatch(deleteTypeFailure(error)));
  };
}

export const UPDATE_LEVEL_REQUEST = 'UPDATE_LEVEL_REQUEST';
export const UPDATE_LEVEL_SUCCESS = 'UPDATE_LEVEL_SUCCESS';
export const UPDATE_LEVEL_FAILURE = 'UPDATE_LEVEL_FAILURE';
export const VALIDATION_ERROR = 'VALIDATION_ERROR';

const updateLevelRequest = createAction(UPDATE_LEVEL_REQUEST, 'requestBody');
const updateLevelSuccess = createAction(UPDATE_LEVEL_SUCCESS, 'data');
const updateLevelFailure = createAction(UPDATE_LEVEL_FAILURE, 'error');
const validationError = createAction(VALIDATION_ERROR, 'error');

export function updateLevel(requestData, operation, cb) {
  const method = {
    create: 'put',
    update: 'put',
    delete: 'delete',
  };
  return function (dispatch) {
    dispatch(updateLevelRequest(requestData));
    axios[method[operation]](
      `/lmsStudentSetting/${
        operation === 'create' ? 'addLevels' : operation === 'update' ? 'editLevel' : 'removeLevel'
      }`,
      operation !== 'delete' ? requestData : { data: requestData }
    )
      .then((res) => {
        let users = fromJS(res.data.data);
        let message;
        let existedUserNames = [];
        let roleGroupedArray = [];
        let userNames = [];
        let roleWithUserNames = [];

        const userRoleValidation = (type) => {
          roleGroupedArray = users.groupBy((item) => {
            return item.getIn(['roleName', '_id']);
          });

          // eslint-disable-next-line
          roleGroupedArray.map((user, testIndex) => {
            userNames = [];
            // eslint-disable-next-line
            user.map((inner) => {
              userNames.push([
                inner.getIn(['userName', 'name', 'first']) +
                  ' ' +
                  inner.getIn(['userName', 'name', 'last']),
              ]);
            });
            roleWithUserNames.push(
              `${roleGroupedArray.getIn([testIndex, 0, 'roleName', 'name'])} - (${userNames.join(
                ','
              )})`
            );
          });

          if (type === 'USER_ROLE')
            return (message = `'${roleWithUserNames.join(
              ','
            )}' role's user already exists at another level`);
          else
            return (message = `'${roleWithUserNames.join(
              ','
            )}' user already exists at another level of role`);
        };

        if (users.size > 0) {
          switch (res.data.message) {
            //USER_USER
            case 'User already existed':
              users.map((user) => {
                return existedUserNames.push(
                  user.getIn(['name', 'first']) + ' ' + user.getIn(['name', 'last'])
                );
              });
              message = `'${existedUserNames.join(',')}' already exists at another level`;
              break;

            // USER_ROLE
            case 'role already exited in user':
              message = userRoleValidation('USER_ROLE');
              break;

            // ROLE_ROLE
            case 'Role already existed':
              users.map((user) => userNames.push(user.get('name')));
              message = `'${userNames.join(',')}' role already exists at another level`;
              break;

            // ROLE_USER
            case 'User already exited in role':
              message = userRoleValidation('ROLE_USER');
              break;
            default:
          }

          dispatch(validationError({ message }));
        } else {
          dispatch(updateLevelSuccess());
          cb && cb();
        }
      })
      .catch((error) => dispatch(updateLevelFailure(error)));
  };
}

export const GET_USER_REQUEST = 'GET_USER_REQUEST';
export const GET_USER_SUCCESS = 'GET_USER_SUCCESS';
export const GET_USER_FAILURE = 'GET_USER_FAILURE';

const getUserRequest = createAction(GET_USER_REQUEST);
const getUserSuccess = createAction(GET_USER_SUCCESS, 'data');
const getUserFailure = createAction(GET_USER_FAILURE, 'error');

export function getUser({ limit = 20, pageNo = 1, searchKey }) {
  return function (dispatch) {
    dispatch(getUserRequest());
    axios
      .get(
        `/user/get_all_user?user_type=staff&limit=${limit}&pageNo=${pageNo}&searchKey=${searchKey}`
      )
      .then((res) => dispatch(getUserSuccess(res.data.data)))
      .catch((error) => dispatch(getUserFailure(error)));
  };
}

export const UPDATE_TERMS_AND_CONDITIONS_REQUEST = 'UPDATE_TERMS_AND_CONDITIONS_REQUEST';
export const UPDATE_TERMS_AND_CONDITIONS_SUCCESS = 'UPDATE_TERMS_AND_CONDITIONS_SUCCESS';
export const UPDATE_TERMS_AND_CONDITIONS_FAILURE = 'UPDATE_TERMS_AND_CONDITIONS_FAILURE';

const updateTermsAndConditionsRequest = createAction(
  UPDATE_TERMS_AND_CONDITIONS_REQUEST,
  'requestBody'
);
const updateTermsAndConditionsSuccess = createAction(UPDATE_TERMS_AND_CONDITIONS_SUCCESS, 'data');
const updateTermsAndConditionsFailure = createAction(UPDATE_TERMS_AND_CONDITIONS_FAILURE, 'error');

export function updateTermsAndConditions(requestBody, cb) {
  return function (dispatch) {
    dispatch(updateTermsAndConditionsRequest(requestBody));
    axios
      .put(`/lmsStudentSetting/termsAndCondition`, requestBody)
      .then(() => {
        dispatch(updateTermsAndConditionsSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateTermsAndConditionsFailure(error)));
  };
}

export const UPDATE_WARNING_REQUEST = 'UPDATE_WARNING_REQUEST';
export const UPDATE_WARNING_SUCCESS = 'UPDATE_WARNING_SUCCESS';
export const UPDATE_WARNING_FAILURE = 'UPDATE_WARNING_FAILURE';

const updateWarningRequest = createAction(UPDATE_WARNING_REQUEST, 'requestBody');
const updateWarningSuccess = createAction(UPDATE_WARNING_SUCCESS, 'data');
const updateWarningFailure = createAction(UPDATE_WARNING_FAILURE, 'error');

export function updateWarning(requestData, cb) {
  return function (dispatch) {
    dispatch(updateWarningRequest(requestData));
    axios
      .put(`lmsStudentSetting/add-warning-denial-configuration`, requestData)
      .then(() => {
        dispatch(updateWarningSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateWarningFailure(error)));
  };
}

export const UPDATE_CATEGORY_STATUS_REQUEST = 'UPDATE_CATEGORY_STATUS_REQUEST';
export const UPDATE_CATEGORY_STATUS_SUCCESS = 'UPDATE_CATEGORY_STATUS_SUCCESS';
export const UPDATE_CATEGORY_STATUS_FAILURE = 'UPDATE_CATEGORY_STATUS_FAILURE';

const updateCategoryStatusRequest = createAction(UPDATE_CATEGORY_STATUS_REQUEST, 'requestBody');
const updateCategoryStatusSuccess = createAction(UPDATE_CATEGORY_STATUS_SUCCESS, 'data');
const updateCategoryStatusFailure = createAction(UPDATE_CATEGORY_STATUS_FAILURE, 'error');

export function updateCategoryStatus(requestData, cb) {
  return function (dispatch) {
    dispatch(updateCategoryStatusRequest(requestData));
    axios
      .put(`lmsStudentSetting/updateCategoryStatus`, requestData)
      .then(() => {
        dispatch(updateCategoryStatusSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateCategoryStatusFailure(error)));
  };
}

export const UPDATE_CATEGORY_NAME_REQUEST = 'UPDATE_CATEGORY_NAME_REQUEST';
export const UPDATE_CATEGORY_NAME_SUCCESS = 'UPDATE_CATEGORY_NAME_SUCCESS';
export const UPDATE_CATEGORY_NAME_FAILURE = 'UPDATE_CATEGORY_NAME_FAILURE';

const updateCategoryNameRequest = createAction(UPDATE_CATEGORY_NAME_REQUEST, 'requestBody');
const updateCategoryNameSuccess = createAction(UPDATE_CATEGORY_NAME_SUCCESS, 'data');
const updateCategoryNameFailure = createAction(UPDATE_CATEGORY_NAME_FAILURE, 'error');

export function updateCategoryName(requestData, cb) {
  return function (dispatch) {
    dispatch(updateCategoryNameRequest(requestData));
    axios
      .put(`lmsStudentSetting/editCategoryName`, requestData)
      .then(() => {
        dispatch(updateCategoryNameSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateCategoryNameFailure(error)));
  };
}

export const UPDATE_CATEGORY_TYPES_REQUEST = 'UPDATE_CATEGORY_TYPES_REQUEST';
export const UPDATE_CATEGORY_TYPES_SUCCESS = 'UPDATE_CATEGORY_TYPES_SUCCESS';
export const UPDATE_CATEGORY_TYPES_FAILURE = 'UPDATE_CATEGORY_TYPES_FAILURE';

const updateCategoryTypesRequest = createAction(UPDATE_CATEGORY_TYPES_REQUEST, 'requestBody');
const updateCategoryTypesSuccess = createAction(UPDATE_CATEGORY_TYPES_SUCCESS, 'data');
const updateCategoryTypesFailure = createAction(UPDATE_CATEGORY_TYPES_FAILURE, 'error');

export function updateCategoryTypes(requestData, cb) {
  return function (dispatch) {
    dispatch(updateCategoryTypesRequest(requestData));
    axios
      .put(`lmsStudentSetting/updateTypes`, requestData)
      .then(() => {
        dispatch(updateCategoryTypesSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateCategoryTypesFailure(error)));
  };
}

export const UPDATE_GENDER_SEGREGATION_REQUEST = 'UPDATE_GENDER_SEGREGATION_REQUEST';
export const UPDATE_GENDER_SEGREGATION_SUCCESS = 'UPDATE_GENDER_SEGREGATION_SUCCESS';
export const UPDATE_GENDER_SEGREGATION_FAILURE = 'UPDATE_GENDER_SEGREGATION_FAILURE';

const updateGenderSegregationRequest = createAction(
  UPDATE_GENDER_SEGREGATION_REQUEST,
  'requestBody'
);
const updateGenderSegregationSuccess = createAction(UPDATE_GENDER_SEGREGATION_SUCCESS, 'data');
const updateGenderSegregationFailure = createAction(UPDATE_GENDER_SEGREGATION_FAILURE, 'error');

export function updateGenderSegregation(requestData, cb) {
  return function (dispatch) {
    dispatch(updateGenderSegregationRequest(requestData));
    axios
      .put(`lmsStudentSetting/divideGenderSegregation`, requestData)
      .then(() => {
        dispatch(updateGenderSegregationSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateGenderSegregationFailure(error)));
  };
}

export const UPDATE_EXCEPTIONAL_PROGRAM_REQUEST = 'UPDATE_EXCEPTIONAL_PROGRAM_REQUEST';
export const UPDATE_EXCEPTIONAL_PROGRAM_SUCCESS = 'UPDATE_EXCEPTIONAL_PROGRAM_SUCCESS';
export const UPDATE_EXCEPTIONAL_PROGRAM_FAILURE = 'UPDATE_EXCEPTIONAL_PROGRAM_FAILURE';

const updateExceptionalProgramRequest = createAction(
  UPDATE_EXCEPTIONAL_PROGRAM_REQUEST,
  'requestBody'
);
const updateExceptionalProgramSuccess = createAction(UPDATE_EXCEPTIONAL_PROGRAM_SUCCESS, 'data');
const updateExceptionalProgramFailure = createAction(UPDATE_EXCEPTIONAL_PROGRAM_FAILURE, 'error');

export function updateExceptionalProgram(requestData, cb, operation) {
  return function (dispatch) {
    dispatch(updateExceptionalProgramRequest(requestData));
    axios
      .put(
        `lmsStudentSetting/${operation === 'update' ? 'editProgramLevel' : 'addProgramLevel'}`,
        requestData
      )
      .then(() => {
        dispatch(updateExceptionalProgramSuccess());
        cb && cb();
      })
      .catch((error) => dispatch(updateExceptionalProgramFailure(error)));
  };
}

export const UPDATE_WARNING_CONFIG_REQUEST = 'UPDATE_WARNING_CONFIG_REQUEST';
export const UPDATE_WARNING_CONFIG_SUCCESS = 'UPDATE_WARNING_CONFIG_SUCCESS';
export const UPDATE_WARNING_CONFIG_FAILURE = 'UPDATE_WARNING_CONFIG_FAILURE';

const updateWarningConfigRequest = createAction(UPDATE_WARNING_CONFIG_REQUEST, 'requestBody');
const updateWarningConfigSuccess = createAction(UPDATE_WARNING_CONFIG_SUCCESS, 'data');
const updateWarningConfigFailure = createAction(UPDATE_WARNING_CONFIG_FAILURE, 'error');

export function updateWarningConfig(requestData, configKey) {
  return function (dispatch) {
    dispatch(updateWarningConfigRequest(requestData));
    axios
      .put(`lmsStudentSetting/updateWarningConfig`, requestData)
      .then(() => {
        dispatch(updateWarningConfigSuccess({ ...requestData, configKey }));
      })
      .catch((error) => dispatch(updateWarningConfigFailure(error)));
  };
}

export const ADD_LEAVE_POLICY_REQUEST = 'ADD_LEAVE_POLICY_REQUEST';
export const ADD_LEAVE_POLICY_SUCCESS = 'ADD_LEAVE_POLICY_SUCCESS';
export const ADD_LEAVE_POLICY_FAILURE = 'ADD_LEAVE_POLICY_FAILURE';

const addLeavePolicyRequest = createAction(ADD_LEAVE_POLICY_REQUEST, 'requestBody');
const addLeavePolicySuccess = createAction(ADD_LEAVE_POLICY_SUCCESS, 'data');
const addLeavePolicyFailure = createAction(ADD_LEAVE_POLICY_FAILURE, 'error');

export function addLeavePolicy(body, cb) {
  return function (dispatch) {
    dispatch(addLeavePolicyRequest());
    axios
      .put(`/lmsStudentSetting/addLeavePolicy`, body)
      .then((res) => {
        dispatch(addLeavePolicySuccess(res.data));
        cb && cb();
      })
      .catch((error) => dispatch(addLeavePolicyFailure(error)));
  };
}

export const EDIT_LEAVE_POLICY_REQUEST = 'EDIT_LEAVE_POLICY_REQUEST';
export const EDIT_LEAVE_POLICY_SUCCESS = 'EDIT_LEAVE_POLICY_SUCCESS';
export const EDIT_LEAVE_POLICY_FAILURE = 'EDIT_LEAVE_POLICY_FAILURE';

const editLeavePolicyRequest = createAction(EDIT_LEAVE_POLICY_REQUEST, 'requestBody');
const editLeavePolicySuccess = createAction(EDIT_LEAVE_POLICY_SUCCESS, 'data');
const editLeavePolicyFailure = createAction(EDIT_LEAVE_POLICY_FAILURE, 'error');

export function editLeavePolicy(body, cb) {
  return function (dispatch) {
    dispatch(editLeavePolicyRequest());
    axios
      .put(`/lmsStudentSetting/editLeavePolicy`, body)
      .then((res) => {
        dispatch(editLeavePolicySuccess(res.data));
        cb && cb();
      })
      .catch((error) => dispatch(editLeavePolicyFailure(error)));
  };
}

export const DELETE_LEAVE_POLICY_REQUEST = 'DELETE_LEAVE_POLICY_REQUEST';
export const DELETE_LEAVE_POLICY_SUCCESS = 'DELETE_LEAVE_POLICY_SUCCESS';
export const DELETE_LEAVE_POLICY_FAILURE = 'DELETE_LEAVE_POLICY_FAILURE';

const deleteLeavePolicyRequest = createAction(DELETE_LEAVE_POLICY_REQUEST, 'requestBody');
const deleteLeavePolicySuccess = createAction(DELETE_LEAVE_POLICY_SUCCESS, 'data');
const deleteLeavePolicyFailure = createAction(DELETE_LEAVE_POLICY_FAILURE, 'error');

export function deleteLeavePolicy(body, cb) {
  return function (dispatch) {
    dispatch(deleteLeavePolicyRequest());
    axios
      .delete(`/lmsStudentSetting/deleteLeavePolicy`, { data: body })
      .then((res) => {
        dispatch(deleteLeavePolicySuccess(res.data));
        cb && cb();
      })
      .catch((error) => dispatch(deleteLeavePolicyFailure(error)));
  };
}

export const UPLOAD_ATTACHMENT_REQUEST = 'UPLOAD_ATTACHMENT_REQUEST';
export const UPLOAD_ATTACHMENT_SUCCESS = 'UPLOAD_ATTACHMENT_SUCCESS';
export const UPLOAD_ATTACHMENT_FAILURE = 'UPLOAD_ATTACHMENT_FAILURE';

const uploadAttachmentRequest = createAction(UPLOAD_ATTACHMENT_REQUEST);
const uploadAttachmentSuccess = createAction(UPLOAD_ATTACHMENT_SUCCESS, 'data');
const uploadAttachmentFailure = createAction(UPLOAD_ATTACHMENT_FAILURE, 'error');
export function uploadAttachment(file, successCallBack, failureCallBack) {
  return function (dispatch) {
    dispatch(uploadAttachmentRequest());
    axios
      .post(`/lmsStudentSetting/upload`, file)
      .then((res) => {
        dispatch(uploadAttachmentSuccess(res.data.data));
        successCallBack && successCallBack(fromJS(res.data.data));
      })
      .catch((error) => {
        dispatch(uploadAttachmentFailure(error));
        failureCallBack && failureCallBack();
      });
  };
}

export const DELETE_EXCEPTIONAL_REQUEST = 'DELETE_EXCEPTIONAL_REQUEST';
export const DELETE_EXCEPTIONAL_SUCCESS = 'DELETE_EXCEPTIONAL_SUCCESS';
export const DELETE_EXCEPTIONAL_FAILURE = 'DELETE_EXCEPTIONAL_FAILURE';

const deleteExceptionalRequest = createAction(DELETE_EXCEPTIONAL_REQUEST);
const deleteExceptionalSuccess = createAction(DELETE_EXCEPTIONAL_SUCCESS, 'data');
const deleteExceptionalFailure = createAction(DELETE_EXCEPTIONAL_FAILURE, 'error');
export function deleteExceptional(requestBody, cb) {
  return function (dispatch) {
    dispatch(deleteExceptionalRequest());
    axios
      .delete(`/lmsStudentSetting/deleteProgramLevel`, { data: requestBody })
      .then((res) => {
        dispatch(deleteExceptionalSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => {
        dispatch(deleteExceptionalFailure(error));
      });
  };
}

export const DELETE_WARNING_CONFIG_REQUEST = 'DELETE_WARNING_CONFIG_REQUEST';
export const DELETE_WARNING_CONFIG_SUCCESS = 'DELETE_WARNING_CONFIG_SUCCESS';
export const DELETE_WARNING_CONFIG_FAILURE = 'DELETE_WARNING_CONFIG_FAILURE';

const deleteWarningConfigRequest = createAction(DELETE_WARNING_CONFIG_REQUEST, 'requestBody');
const deleteWarningConfigSuccess = createAction(DELETE_WARNING_CONFIG_SUCCESS, 'data');
const deleteWarningConfigFailure = createAction(DELETE_WARNING_CONFIG_FAILURE, 'error');

export function deleteWarningConfig(params, cb, type) {
  return function (dispatch) {
    dispatch(deleteWarningConfigRequest());
    axios
      .delete(`/lmsStudentSetting/deleteWarningConfig`, { params })
      .then((res) => {
        dispatch(deleteWarningConfigSuccess(res.data));
        dispatch(getLmsSettings('leave', type));
        cb && cb();
      })
      .catch((error) => dispatch(deleteWarningConfigFailure(error)));
  };
}

export const GENERATE_URL_REQUEST = 'GENERATE_URL_REQUEST';
export const GENERATE_URL_SUCCESS = 'GENERATE_URL_SUCCESS';
export const GENERATE_URL_FAILURE = 'GENERATE_URL_FAILURE';

const generateUrlRequest = createAction(GENERATE_URL_REQUEST);
const generateUrlSuccess = createAction(GENERATE_URL_SUCCESS, 'data');
const generateUrlFailure = createAction(GENERATE_URL_FAILURE, 'error');

export function generateUrl(url) {
  return function (dispatch) {
    dispatch(generateUrlRequest());
    axios
      .get(`/lmsStudentSetting/generateUrl?url=${url}`)
      .then((res) => dispatch(generateUrlSuccess(res.data.data)))
      .catch((error) => dispatch(generateUrlFailure(error)));
  };
}

export const RESET_DENIAL_CONDITION_REQUEST = 'RESET_DENIAL_CONDITION_REQUEST';
export const RESET_DENIAL_CONDITION_SUCCESS = 'RESET_DENIAL_CONDITION_SUCCESS';
export const RESET_DENIAL_CONDITION_FAILURE = 'RESET_DENIAL_CONDITION_FAILURE';

const resetDenialConditionRequest = createAction(RESET_DENIAL_CONDITION_REQUEST);
const resetDenialConditionSuccess = createAction(RESET_DENIAL_CONDITION_SUCCESS, 'data');
const resetDenialConditionFailure = createAction(RESET_DENIAL_CONDITION_FAILURE, 'error');

export function resetDenialCondition(callback) {
  return function (dispatch) {
    dispatch(resetDenialConditionRequest());
    axios
      .get(`/lmsDenial/reset-denials`)
      .then((res) => {
        dispatch(resetDenialConditionSuccess(res.data.data));
        callback && callback();
      })
      .catch((error) => dispatch(resetDenialConditionFailure(error)));
  };
}

export const ATTENDANCE_CONFIGURATION_REQUEST = 'ATTENDANCE_CONFIGURATION_REQUEST';
export const ATTENDANCE_CONFIGURATION_SUCCESS = 'ATTENDANCE_CONFIGURATION_SUCCESS';
export const ATTENDANCE_CONFIGURATION_FAILURE = 'ATTENDANCE_CONFIGURATION_FAILURE';

const getAttendanceConfigRequest = createAction(ATTENDANCE_CONFIGURATION_REQUEST);
const getAttendanceConfigSuccess = createAction(ATTENDANCE_CONFIGURATION_SUCCESS, 'data');
const getAttendanceConfigFailure = createAction(ATTENDANCE_CONFIGURATION_FAILURE, 'error');

export function getAttendanceConfig() {
  return function (dispatch) {
    dispatch(getAttendanceConfigRequest());
    axios
      .get(`/lmsAttendanceConfig/getAttendanceConfig`)
      .then((res) => dispatch(getAttendanceConfigSuccess(res.data.data)))
      .catch((error) => dispatch(getAttendanceConfigFailure(error)));
  };
}

export const UPDATE_ATTENDANCE_CONFIGURATION_REQUEST = 'UPDATE_ATTENDANCE_CONFIGURATION_REQUEST';
export const UPDATE_ATTENDANCE_CONFIGURATION_SUCCESS = 'UPDATE_ATTENDANCE_CONFIGURATION_SUCCESS';
export const UPDATE_ATTENDANCE_CONFIGURATION_FAILURE = 'UPDATE_ATTENDANCE_CONFIGURATION_FAILURE';

const updateAttendanceConfigRequest = createAction(UPDATE_ATTENDANCE_CONFIGURATION_REQUEST);
const updateAttendanceConfigSuccess = createAction(UPDATE_ATTENDANCE_CONFIGURATION_SUCCESS, 'data');
const updateAttendanceConfigFailure = createAction(
  UPDATE_ATTENDANCE_CONFIGURATION_FAILURE,
  'error'
);

export function updateAttendanceConfig(payload) {
  return function (dispatch) {
    dispatch(updateAttendanceConfigRequest());
    axios
      .post('/lmsAttendanceConfig/attendanceAddRow', payload)
      .then((res) => {
        dispatch(updateAttendanceConfigSuccess(res.data.data));
        dispatch(getAttendanceConfig());
      })
      .catch((error) => dispatch(updateAttendanceConfigFailure(error)));
  };
}

export const GET_LATE_EXCLUDE_STATUS_REQUEST = 'GET_LATE_EXCLUDE_STATUS_REQUEST';
export const GET_LATE_EXCLUDE_STATUS_SUCCESS = 'GET_LATE_EXCLUDE_STATUS_SUCCESS';
export const GET_LATE_EXCLUDE_STATUS_FAILURE = 'GET_LATE_EXCLUDE_STATUS_FAILURE';

const getLateExcludeStatusRequest = createAction(GET_LATE_EXCLUDE_STATUS_REQUEST);
const getLateExcludeStatusSuccess = createAction(GET_LATE_EXCLUDE_STATUS_SUCCESS, 'data');
const getLateExcludeStatusFailure = createAction(GET_LATE_EXCLUDE_STATUS_FAILURE, 'error');

export function getLateExcludeStatus(params) {
  return function (dispatch) {
    dispatch(getLateExcludeStatusRequest());
    axios
      .get(`/lmsAttendanceConfig/getLateExcludeStatus`, params)
      .then((res) => {
        const response = res.data.data;
        dispatch(getLateExcludeStatusSuccess(typeof response === 'string' ? [] : response));
      })
      .catch((error) => dispatch(getLateExcludeStatusFailure(error)));
  };
}

export const UPDATE_LATE_EXCLUDE_REQUEST = 'UPDATE_LATE_EXCLUDE_REQUEST';
export const UPDATE_LATE_EXCLUDE_SUCCESS = 'UPDATE_LATE_EXCLUDE_SUCCESS';
export const UPDATE_LATE_EXCLUDE_FAILURE = 'UPDATE_LATE_EXCLUDE_FAILURE';

const updateLateExcludeRequest = createAction(UPDATE_LATE_EXCLUDE_REQUEST, 'requestBody');
const updateLateExcludeSuccess = createAction(UPDATE_LATE_EXCLUDE_SUCCESS, 'data');
const updateLateExcludeFailure = createAction(UPDATE_LATE_EXCLUDE_FAILURE, 'error');

export function updateLateExclude(data, callBack) {
  const { ...body } = data;
  return function (dispatch) {
    dispatch(updateLateExcludeRequest());
    axios
      .put(`/lmsAttendanceConfig/updateLateExclude`, body)
      .then((res) => {
        dispatch(updateLateExcludeSuccess(res.data));
        callBack && callBack();
      })
      .catch((error) => dispatch(updateLateExcludeFailure(error)));
  };
}

export const DELETE_LMS_SETTINGS_DATA_REQUEST = 'DELETE_LMS_SETTINGS_DATA_REQUEST';
export const DELETE_LMS_SETTINGS_DATA_SUCCESS = 'DELETE_LMS_SETTINGS_DATA_SUCCESS';
export const DELETE_LMS_SETTINGS_DATA_FAILURE = 'DELETE_LMS_SETTINGS_DATA_FAILURE';

const deleteLmsSettingsDataRequest = createAction(DELETE_LMS_SETTINGS_DATA_REQUEST);
const deleteLmsSettingsDataSuccess = createAction(
  DELETE_LMS_SETTINGS_DATA_SUCCESS,
  'data',
  'studentDetails'
);
const deleteLmsSettingsDataFailure = createAction(DELETE_LMS_SETTINGS_DATA_FAILURE, 'error');

export function deleteLmsSettingsData(params, cb) {
  return function (dispatch) {
    dispatch(deleteLmsSettingsDataRequest());
    axios
      .delete(
        `/lmsStudentSetting/deleteLmsSetting?classificationType=${params.classificationType}&leaveApplicationCriteria=${params.leaveApplicationCriteria}`
      )
      .then((res) => {
        dispatch(deleteLmsSettingsDataSuccess(res.data.data));
        cb && cb();
      })
      .catch((error) => dispatch(deleteLmsSettingsDataFailure(error)));
  };
}

export const UPDATE_COURSEORCOMPREHENSIVE_REQUEST = 'UPDATE_COURSEORCOMPREHENSIVE_REQUEST';
export const UPDATE_COURSEORCOMPREHENSIVE_SUCCESS = 'UPDATE_COURSEORCOMPREHENSIVE_SUCCESS';
export const UPDATE_COURSEORCOMPREHENSIVE_FAILURE = 'UPDATE_COURSEORCOMPREHENSIVE_FAILURE';

const updateCourseOrComprehensiveRequest = createAction(
  UPDATE_COURSEORCOMPREHENSIVE_REQUEST,
  'requestBody'
);
const updateCourseOrComprehensiveSuccess = createAction(
  UPDATE_COURSEORCOMPREHENSIVE_SUCCESS,
  'data'
);
const updateCourseOrComprehensiveFailure = createAction(
  UPDATE_COURSEORCOMPREHENSIVE_FAILURE,
  'error'
);

export function courseOrComprehensive(data, callBack) {
  const { ...body } = data;
  const warningModeValue = body.warningModeValue;
  return function (dispatch) {
    dispatch(updateCourseOrComprehensiveRequest());
    axios
      .put(`/lmsStudentSetting/updateCourseOrComprehensive`, body)
      .then((res) => {
        dispatch(updateCourseOrComprehensiveSuccess(res.data));
        callBack && callBack(warningModeValue);
      })
      .catch((error) => dispatch(updateCourseOrComprehensiveFailure(error)));
  };
}
