import React, { createContext, useContext, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map, fromJS } from 'immutable';
import { useHistory } from 'react-router-dom';
import moment from 'moment';
import EditIcon from '@mui/icons-material/Edit';
import { useSelector } from 'react-redux';
import {
  createUnpublishedForm,
  getCategoryForm,
  getConfigureTemplate,
  getVarietyOfForms,
  setData,
} from '_reduxapi/q360/actions';
import { selectFormOccurrence, selectedForms } from '_reduxapi/q360/selectors';

import Drawer from '@mui/material/Drawer';
import AddIcon from '@mui/icons-material/Add';
import Checkbox from '@mui/material/Checkbox';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import { makeStyles } from '@mui/styles';
import DeleteIcon from '@mui/icons-material/Delete';
import CircleIcon from '@mui/icons-material/Circle';
import OutlinedInput from '@mui/material/OutlinedInput';
import SearchIcon from '@mui/icons-material/Search';
import { InputAdornment, Box, Badge, Avatar, Tooltip } from '@mui/material';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import { styled } from '@mui/material/styles';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Fade, Menu, MenuItem } from '@mui/material';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepConnector, { stepConnectorClasses } from '@mui/material/StepConnector';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import CloseIcon from '@mui/icons-material/Close';
import DoneIcon from '@mui/icons-material/Done';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Button as MuiButton } from '@mui/material';

import ConfigureModal from './Models/CourseSpecificationConfigure';
import { jsUcfirstAll } from 'utils';
import { HtmlToolTip } from 'Modules/GlobalConfigurationV2/utils/uiUtils';
import settings from 'Assets/settings.svg';
import folderEmpty from 'Assets/folderEmpty.svg';
import staffName from 'Assets/staffName.svg';
import date_range from 'Assets/date_range.svg';
import courseLevel from 'Assets/courseLevel.svg';
import courseSpec from 'Assets/courseSpec.svg';
import courseSpecActive from 'Assets/courseSpecActive.svg';
import courseReport from 'Assets/courseReport.svg';
import courseReportActive from 'Assets/courseReportActive.svg';
import programSpec from 'Assets/programSpec.svg';
import programSpecActive from 'Assets/programSpecActive.svg';
import programReport from 'Assets/programReport.svg';
import programReportActive from 'Assets/programReportActive.svg';
import archiveIcon from 'Assets/archive.svg';
import deleteIcon from 'Assets/delete.svg';
import institutionActive from 'Assets/institutionSep.svg';

import Button from 'Widgets/FormElements/material/Button';
import CreateCategoryDialog from './Models/CreateCategoryDialog';
import {
  useBooleanHook,
  useInputHook,
  useDebounce,
  useCallApiHook,
  useSearchParams,
  useIsReduxEmpty,
  useNestedHook,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { PaginationQ360 } from 'Modules/GlobalConfigurationV2/utils/uiUtils';
import { generateParams } from 'Modules/GlobalConfigurationV2/utils/jsUtils';
import { updateCategoryForm, getSingleFormOccurrence } from '_reduxapi/q360/actions';
import { selectedConfigureTemplate } from '_reduxapi/q360/selectors';
import IncorporateIframeModal from 'Modules/QAPC/components/Models/IncorporateIframeModal';
// import { data } from 'Modules/QAPC/components/QualityAssuranceProcess/CreateForm';
import FormConfigurationRedirect from '../Q360/FormConfigurationRedirect';
import { ConfirmationPopup } from 'Modules/GlobalConfigurationV2/utils/uiUtils';
import { baseOptions } from './Models/CourseSpecificationConfigure';
import { checkCurrentCompletedStep, programShortCode } from '../Q360/utils';
import { useCheckEditAccess, useCurrentPage } from '..';
import { getShortString } from 'Modules/Shared/v2/Configurations';
/*----------------------------------Utils Start---------------------------------------------------*/
export const tabBorderNone = {
  borderBottom: '1px solid transparent !important',
  '& .MuiTabs-indicator': {
    backgroundColor: '#2A7AFC',
  },
  '& .MuiTabs-root': {
    margin: 0,
    padding: 0,
  },
  '& .MuiTabs-scroller': {
    height: '40px',
  },
  minHeight: 0,
};
export var PendingSx = {
  '& .MuiTab-root': {
    margin: '0px 25px 0px 0px',
    padding: '0px',
    minWidth: '0px',
  },
};
const addSpecification = fromJS([
  { categoryName: 'archive forms', type: 'archive', isDefault: true },
  { categoryName: 'deleted forms', type: 'deleted', isDefault: true },
]);
const statusData = fromJS({
  draft: {
    statusCss: 'bg-draft text-white text-capitalize',
    display: 'draft',
    showAction: false,
    showPublish: true,
  },
  unpublished: {
    statusCss: 'bg-secondary text-white text-capitalize',
    display: 'UnPublished',
    showAction: false,
    showPublish: false,
  },
  published: {
    statusCss: 'bgLight text-primary text-capitalize',
    display: 'Published',
    showAction: false,
    showPublish: false,
  },
  revoked: {
    statusCss: 'bg-light-red text-dark-red text-capitalize',
    display: 'Revoked',
  },
  inactive: {
    statusCss: 'bg-light-yellow text-warning text-capitalize',
    display: 'Inactive',
  },
  active: {
    statusCss: 'bgLight text-warning text-capitalize',
    display: 'active',
  },
});
const archive = { name: 'Archive', value: 'archive' };
const revoked = { name: 'Revoked', value: 'revoked' };
const inActive = { name: 'InActive', value: 'inactive' };
const active = { name: 'Active', value: 'published' };
const unPublished = { name: 'UnPublished', value: 'unPublished' };

export const circleSX = { fontSize: 6, color: 'grey' };
export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '10px', // Adjust the font size as needed
    // You can also add other styles here, such as fontFamily, fontWeight, etc.
  },
}));
const searchInputStyle = {
  width: '100%',
  height: '45px',
  fontSize: '14px',
  background: 'white',
  color: '#9CA3AF',
  borderColor: '#D1D5DB',
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: '#D1D5DB',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: '#D1D5DB',
    boxShadow: 'none',
  },
  '&.MuiOutlinedInput-root': {
    '& fieldset': {
      borderColor: '#D1D5DB',
    },
  },
};
const InputAdornmentStyle = {
  color: '#9CA3AF',
  fontSize: '16px',
};
const containerHeight = { height: '50vh' };
const steps = ['STEP 1', 'STEP 2', 'STEP 3', 'STEP 4'];
const disableStepper = ['inactive', 'revoked'];
const disableStepperFormsWise = ['deleted', 'archive'];
const ConnectingLine = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 14,
    left: 'calc(-50% + 14px)',
    right: 'calc(50% + 6px)',
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : '#62a6fb',
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : '#62a6fb',
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: '.1rem',
    border: 0,
    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : '#62a6fb',
    borderRadius: 1,
  },
}));
const CompleteRootIcon = styled('div')(() => ({
  backgroundColor: '#147afc',
  zIndex: 1,
  color: '#c6defe',
  width: '20px',
  height: '20.09px',
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  paddingRight: '0px',
}));
const PendingRootIcon = styled('div')(() => ({
  width: '20px',
  height: '20.09px',
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  paddingRight: '0px',
  color: '#a8aeb9',
  background: 'white',
  border: '.1rem solid #a8aeb9',
}));
const groupByData = (data, key) => data.groupBy((element) => element.get(key, ''));
const TabContextParams = createContext();
const checkBoxSx = {
  padding: '0px !important',
  '& .MuiSvgIcon-root': {
    width: '15px',
    height: '15px',
    color: '#9CA3AF',
  },
};

const configureImg = {
  'Course Specification+false': courseSpec,
  'Course Reports+false': courseReport,
  'Program Specification+false': programSpec,
  'Program Reports+false': programReport,
  'Course Specification+true': courseSpecActive,
  'Course Reports+true': courseReportActive,
  'Program Specification+true': programSpecActive,
  'Program Reports+true': programReportActive,
  'Institution Specification+true': institutionActive,
  'archive forms+true': archiveIcon,
  'deleted forms+false': deleteIcon,
  'archive forms+false': archiveIcon,
  'deleted forms+true': deleteIcon,
};

const textCapitalize = { textTransform: 'capitalize' };
const muiPaperSx = { '& .MuiDrawer-paper': { overflow: 'visible', width: '78vw' } };
const accordionSx = {
  border: '1px solid #D1D5DB',
  borderRadius: '4px',
  '& .MuiAccordion-root': {
    border: '2px solid #9CA3AF',
  },
  '&.MuiAccordion-root::before': {
    display: 'none',
  },
};

const accordionSummerySx = {
  padding: '0px !important',
  '& .MuiAccordionSummary-expandIconWrapper': {
    position: 'relative',
    right: '5px',
  },
};

function stepperLineSx(isDisable) {
  return {
    '& .MuiStepConnector-line': {
      background: isDisable ? '#6B7280 !important' : '',
    },
  };
}

const stepperLabel = {
  '& .MuiStepLabel-alternativeLabel': {
    marginTop: '0px !important',
  },
};

/*----------------------------------Utils End-----------------------------------------------------*/

/*----------------------------------Utils Component Start-----------------------------------------*/
const TabContextCompound = ({ groupByKey, children }) => {
  const { tab, handleChange, curriculum } = useContext(TabContextParams);
  const condition = groupByKey === 'year' ? groupByKey : 'curriculumName';
  const value = tab.get(groupByKey, '');
  return (
    <TabContext value={value}>
      <Box>
        <TabList aria-label="lab API tabs example" sx={{ ...tabBorderNone, ...PendingSx }}>
          {groupByData(curriculum, groupByKey)
            .entrySeq()
            .map(([_id, element]) => {
              return groupByData(element, groupByKey)
                .entrySeq()
                .map(([_, data], index) => {
                  return (
                    <Tab
                      className="f-14"
                      label={data.getIn([index, condition], '')}
                      value={_id}
                      onClick={() => handleChange(_id, groupByKey)}
                      sx={textCapitalize}
                      key={index}
                    />
                  );
                });
            })}
        </TabList>
      </Box>
      {children}
    </TabContext>
  );
};
TabContextCompound.propTypes = {
  groupByKey: PropTypes.string,
  children: PropTypes.func,
};
const TabCompound = ({ curriculum }) => {
  const [courses, setCourses] = useState(Map());
  const [tab, setTab] = useState(Map({ curriculumId: '', year: '' }));
  const curriculumId = tab.get('curriculumId', '');
  const year = tab.get('year', '');
  const handleChange = (value, key) => setTab((previous) => previous.set(key, value));
  const construct = curriculum
    .groupBy((curriculum) => curriculum.get('curriculumId', ''))
    .map((grouping) => grouping.groupBy((year) => year.get('year', '')))
    .map((groupType) =>
      groupType.map((group) => group.groupBy((courseData) => courseData.get('courseType', '')))
    );
  const firstKey = Object.keys(construct.toJS())[0];
  const secondKey = Object.keys(construct.get(firstKey, Map()).toJS())[0];

  useEffect(() => {
    setCourses(construct.getIn([curriculumId, year], Map()));
  }, [tab]);
  useEffect(() => {
    setTab((previous) => previous.set('curriculumId', firstKey).set('year', secondKey));
  }, []);
  return (
    <TabContextParams.Provider value={{ curriculum, handleChange, tab }}>
      <TabContextCompound groupByKey="curriculumId">
        <TabContextCompound groupByKey="year">
          <div className="course-scroll g-config-scrollbar">
            {courses.entrySeq().map(([key, element], index) => (
              <CourseCard
                courses={element}
                courseType={`${key} Course (${String(element.size).padStart(2, '0')} Courses)`}
                key={index}
              />
            ))}
          </div>
        </TabContextCompound>
      </TabContextCompound>
    </TabContextParams.Provider>
  );
};
TabCompound.propTypes = {
  curriculum: PropTypes.object,
};

const DrawerRight = ({ open, handleClose, expanded, handleChange }) => {
  const formOccurrences = useSelector(selectFormOccurrence);
  const [searchParams] = useSearchParams();
  const formId = searchParams.get('categoryFormId');
  const singleFormOccurrence = formOccurrences.get(formId, List());
  const categoryId = searchParams.get('categoryId');
  const categoryIndex = searchParams.get('currentCategoryIndex');
  const [configureTemplate] = useIsReduxEmpty(selectedConfigureTemplate, getConfigureTemplate);
  const findCategoryLevel = configureTemplate.find((value) => value.get('_id', '') === categoryId);
  const getLevel = findCategoryLevel ? findCategoryLevel.get('level', '') : '';
  const categoryLevel = categoryId
    ? getLevel
    : configureTemplate.getIn([categoryIndex, 'level'], '');
  const [getApi] = useCallApiHook(getSingleFormOccurrence);
  const history = useHistory();
  const handleCloseDrawer = () => {
    handleChange(0);
    handleClose();
    searchParams.delete('categoryFormId');
    history.push({ search: searchParams.toString() });
  };
  useEffect(() => {
    getApi(formId);
  }, [open, formId]);
  return (
    <Drawer anchor="right" open={open} className="rightDrawer" sx={muiPaperSx}>
      <div className="close-btn" onClick={handleCloseDrawer}>
        <CloseIcon />
        <div className="close-btn-bottom"></div>
      </div>
      <div className="border-bottom f-18 py-2 px-2 ">Assigned Program</div>
      <div className="my-2 g-config-scrollbar more-details-accordion-scroll ">
        {groupByData(singleFormOccurrence, 'programId')
          .entrySeq()
          .map(([_, curriculum], index) => {
            const programName = curriculum.getIn([0, 'programName'], '');
            const courseCount = curriculum.size;
            return (
              <Accordion
                key={index}
                className={`mx-3 my-2 box-shadow ${expanded ? 'box-shadow-static' : ''}`}
                expanded={expanded === index + 1}
                onChange={() =>
                  handleChange((previous) => (previous === index + 1 ? 0 : index + 1))
                }
                sx={accordionSx}
                elevation={0}
              >
                <AccordionSummary
                  className={`mx-3`}
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1-content"
                  id="panel1-header"
                  sx={accordionSummerySx}
                >
                  <div className="d-flex gap-10 align-items-center  ">
                    <div className="align-self-center text-warning digi-Shared-bg rounded-1 py-3 px-3 f-12">
                      {programShortCode(programName)}
                    </div>
                    <div className="d-flex flex-column">
                      <div className="fw-500 f-12 cursor-pointer">{programName}</div>
                      <div className="f-12">
                        {courseCount} {categoryLevel === 'course' ? 'Courses' : 'Curriculum'}
                      </div>
                    </div>
                  </div>
                </AccordionSummary>
                <AccordionDetails>
                  {categoryLevel === 'course' && <TabCompound curriculum={curriculum} />}
                  {categoryLevel === 'program' && <CurriculumCard curriculum={curriculum} />}
                </AccordionDetails>
              </Accordion>
            );
          })}
      </div>
    </Drawer>
  );
};
DrawerRight.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  expanded: PropTypes.bool,
  handleChange: PropTypes.fun,
};

const InputStartAdornment = () => (
  <InputAdornment position="start">
    <SearchIcon sx={InputAdornmentStyle} />
  </InputAdornment>
);
function CustomizedSteppers({ categoryForm, onClickRoute, activeStep }) {
  const formStatus = categoryForm.get('status', '');
  // const show = step === 4 ? step : step - 1;
  const classes = useStyles();
  const [searchParams] = useSearchParams();
  const formsType = searchParams.get('forms');
  const isDisable =
    disableStepper.includes(formStatus) || disableStepperFormsWise.includes(formsType);
  return (
    <Stepper
      alternativeLabel
      activeStep={activeStep}
      connector={<ConnectingLine />}
      sx={stepperLineSx(isDisable)}
    >
      {steps.map((label, i) => (
        <Step key={label} className="pr-0" active={true}>
          <StepLabel
            classes={{ label: classes.label }}
            onClick={isDisable ? () => {} : onClickRoute(i + 1)}
            className={`m-1 ${isDisable ? '' : 'cursor-pointer'}`}
            StepIconComponent={(props) => <StepIcon {...props} formStatus={formStatus} />}
            sx={stepperLabel}
          >
            {label}
          </StepLabel>
        </Step>
      ))}
    </Stepper>
  );
}

function StepIcon(props) {
  const { className, completed, active, formStatus } = props;
  const [searchParams] = useSearchParams();
  const formsType = searchParams.get('forms');
  if (completed)
    return (
      <CompleteRootIcon
        ownerState={{ completed, active }}
        className={` ${
          disableStepper.includes(formStatus) || disableStepperFormsWise.includes(formsType)
            ? 'stepper-disable'
            : { className }
        }`}
      >
        <DoneIcon sx={{ fontSize: '12px' }} />
      </CompleteRootIcon>
    );
  else
    return (
      <PendingRootIcon>
        <FiberManualRecordIcon sx={{ fontSize: '9px' }} />
      </PendingRootIcon>
    );
}
/*----------------------------------Utils Component End-------------------------------------------*/

const goToSteps = (index, actionParams, assignedPrograms) => (category, categoryId) => {
  const history = useHistory();
  const [searchParams] = useSearchParams();
  const _id = category.get('_id', '');
  const formName = category.get('formName', '');
  const step = category.get('step', 0);
  const status = category.get('status', '') === 'published';
  const selectedProgram = category
    .get('selectedProgram', List())
    .map((program) => program.get('programName', ''))
    .join(',');
  const newQuery = `${searchParams.get('query')?.split(' ').join('+')}+${formName
    .replaceAll('&', '**')
    .split(' ')
    .join(',')}`;
  searchParams.delete('query');
  const queryArr = [
    { query: newQuery },
    { categoryFormIndex: index },
    { categoryFormId: _id },
    { step: step - 1 },
    { categoryId: categoryId },
    { categoryId: selectedProgram },
    { assignedPrograms: assignedPrograms.join(',+') },
    { published: status },
  ];
  const queryParams = generateParams(queryArr);
  const updatedSearch = searchParams.toString();
  const updatedUrl = `/globalConfiguration-v1/qa_pc_configuration?${updatedSearch}&${queryParams}&${actionParams}`;
  const handleRoute = () => history.push(updatedUrl);
  return handleRoute;
};

export default function ConfigureTemplate() {
  const [searchParams] = useSearchParams();
  const currentCategoryIndex = Number(searchParams.get('currentCategoryIndex') ?? 0);
  const history = useHistory();
  const onClickSettings = () => {
    history.push(
      `/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration+configure_template+settings`
    );
  };
  const [configureTemplate] = useIsReduxEmpty(selectedConfigureTemplate, getConfigureTemplate);
  const [categoryToggle, handleCategory] = useBooleanHook();
  const [search, handleSearch] = useInputHook();
  const editAccess = useCheckEditAccess('isEditAccessConfigureTemplate');
  return (
    <>
      <section
        className="d-flex align-items-center cursor-pointer setting_icon_position"
        onClick={onClickSettings}
      >
        <img src={settings} alt="settings" className="ml-auto mr-3" />
      </section>
      <section>
        <main className="text-dGrey grid-container">
          <aside className="p-3 bg-color-darkGray">
            <div className="pl-2">
              <OutlinedInput
                placeholder={'Search for a form'}
                value={search}
                onChange={handleSearch}
                startAdornment={<InputStartAdornment />}
                inputProps={{
                  'aria-label': 'weight',
                }}
                sx={searchInputStyle}
              />
            </div>
            <Divider className="py-2" />
            <section className="d-flex py-3 align-items-center" onClick={handleCategory}>
              <div className="flex-grow-1 f-14">Categories</div>
              {editAccess && (
                <>
                  <Paper className="d-flex" sx={{ borderRadius: '50px' }} elevation={0}>
                    <AddIcon color="primary" sx={{ padding: '3px' }} />
                  </Paper>
                  <span className="text-primary pl-2 f-12 fw-400">New Categories</span>
                </>
              )}
            </section>
            <PaperCompound configureTemplate={configureTemplate} query="currentCategoryIndex" />
            <PaperCompound configureTemplate={addSpecification} query="forms" />
          </aside>
          <section className="bg-lite-greenShade  border-left p-3 scroll">
            <Categories
              category={configureTemplate.get(currentCategoryIndex, Map())}
              search={search}
            />
          </section>
        </main>
      </section>
      <CreateCategoryDialog open={categoryToggle} handleClose={handleCategory} />
    </>
  );
}

const FolderEmpty = ({ category }) => {
  const [open, setOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(true);
  const handleOpenOrClose = () => {
    setOpen((prev) => !prev);
    setIsEdit(true);
  };
  const lowerName = category.get('categoryName', '').toLowerCase();
  const level = lowerName.includes('course')
    ? 'course'
    : lowerName.includes('program')
    ? 'program'
    : 'institution';
  const editAccess = useCheckEditAccess('isEditAccessConfigureTemplate');
  return (
    <main>
      <section className="d-flex">
        <div className="flex-grow-1 f-24 text-capitalize">{category.get('categoryName', '')} </div>
        {editAccess && (
          <div
            className="text_underline text-primary f-14 cursor-pointer"
            onClick={handleOpenOrClose}
          >
            Configure
          </div>
        )}
      </section>
      <Divider className="py-2" />
      <section className="text-center pt-3">
        <img src={folderEmpty} alt="folderEmpty" />
        <div className="f-36 pt-2">This folder is empty.</div>
        <div className="f-16 text-grey">Start a new form to fill up this folder.</div>
      </section>
      {open && (
        <ConfigureModal
          category={category.set('level', level)}
          open={open}
          isEdit={isEdit}
          setIsEdit={setIsEdit}
          handleClose={handleOpenOrClose}
        />
      )}
    </main>
  );
};
const Categories = ({ category, search }) => {
  const history = useHistory();
  const [pages, setPages] = useState(Map({ pageNo: 1, limit: 5, searchKey: '' }));
  const [searchParams] = useSearchParams();
  const configureTemplate = useSelector(selectedConfigureTemplate);
  const currentCategoryIndex = searchParams.get('currentCategoryIndex');
  const formType = searchParams.get('forms');
  const categoryId = category.get('_id', '');
  const isConfigure = category.get('isConfigure', false);
  const isTemplateBased = category.get('categoryFormType', '') === 'template';
  const paramsArray = Object.entries({
    ...pages.toJS(),
    ...(formType && { formType }),
    ...(categoryId && !formType && { categoryId }),
  }).map(([key, value]) => ({
    [key]: value,
  }));
  const constructParams = generateParams(paramsArray);
  const level = category.get('level', '');
  const isNotInstitution = level !== 'institution';
  useEffect(() => {
    if (!currentCategoryIndex && !formType) {
      searchParams.set('currentCategoryIndex', 0);
      history.push({ search: searchParams.toString() });
    }
  }, [configureTemplate]);
  return (
    <>
      {isConfigure ? (
        <main>
          {currentCategoryIndex && (
            <ConfigureCategories
              getData={getCategoryForm}
              params={constructParams}
              type={categoryId}
            >
              <ConfigureCategories.Configure category={category} params={constructParams} />
              {isTemplateBased && isNotInstitution && (
                <ConfigureCategories.Duplicate category={category} params={constructParams} />
              )}
              <ConfigureCategories.Forms
                category={category}
                params={constructParams}
                type={categoryId}
                search={search}
                setPages={setPages}
                menus={[archive]}
              />
            </ConfigureCategories>
          )}
          {formType && (
            <ConfigureCategories
              getData={getVarietyOfForms}
              params={constructParams}
              type={formType}
            >
              <ConfigureCategories.Header type={formType} search={search} />
              <ConfigureCategories.Forms
                search={search}
                params={constructParams}
                setPages={setPages}
                menus={[revoked]}
                type={formType}
              />
            </ConfigureCategories>
          )}
        </main>
      ) : (
        <FolderEmpty category={category} />
      )}
    </>
  );
};
export const usePaginationHook = ({ totalCount, search }) => {
  const [pagination, setPagination] = useState(
    fromJS({
      currentPage: 1,
      perPageSize: 5,
      disableButton: { previous: true, next: true },
    })
  );
  const currentPage = pagination.get('currentPage', 0);
  const perPageLimit = pagination.get('perPageSize', 0);

  const switchPagination = (type) => {
    if (type === 'next') {
      setPagination((previous) => previous.set('currentPage', previous.get('currentPage', 0) + 1));
    } else {
      setPagination((previous) => previous.set('currentPage', previous.get('currentPage', 0) - 1));
    }
  };
  const skipPagination = (type) => {
    if (type === 'firstPage') {
      setPagination((previous) => previous.set('currentPage', 1));
    } else {
      setPagination((previous) =>
        previous.set('currentPage', Math.ceil(totalCount / perPageLimit))
      );
    }
  };
  const handleChange = (e) => {
    setPagination((previous) => previous.set('perPageSize', e.target.value).set('currentPage', 1));
  };
  useEffect(() => {
    const previousStatus = currentPage === 1;
    const nextStatus = totalCount <= currentPage * perPageLimit;
    setPagination((previous) =>
      previous
        .setIn(['disableButton', 'previous'], previousStatus)
        .setIn(['disableButton', 'next'], nextStatus)
    );
  }, [pagination, totalCount, search]);
  useEffect(() => {
    setPagination((previous) => previous.set('currentPage', 1));
  }, [search]);
  return [pagination, switchPagination, skipPagination, handleChange, setPagination];
};
const PaperCompound = ({ configureTemplate, query }) => {
  const history = useHistory();
  const onCategoryClick = ({ type, query }) => () => {
    history.push(
      `/globalConfiguration-v1/qa_pc_configuration?query=qa_pc_configuration+configure_template&${query}=` +
        type
    );
  };
  const [searchParams] = useSearchParams();
  const currentCategoryIndex = searchParams.get('currentCategoryIndex');
  const formsType = searchParams.get('forms');
  return (
    <div className="category-scroll pb-1">
      {configureTemplate.map((category, index) => {
        const isCategorySelected =
          query === 'currentCategoryIndex'
            ? currentCategoryIndex == index
            : formsType === category.get('type', '');
        const logo = configureImg[`${category.get('categoryName', '')}+${isCategorySelected}`];
        return (
          <Paper
            key={category.get('_id', index)}
            className={`${
              !isCategorySelected ? ' bg-transparent ' : 'bg-white'
            } cursor-pointer mt-2  categories-hover  `}
            onClick={onCategoryClick(
              query === 'currentCategoryIndex'
                ? { type: index, query }
                : { type: category.get('type', ''), query }
            )}
            sx={{
              borderRadius: '0px !important',
              boxShadow: 'none',
              background: 'none',
            }}
          >
            <section
              className={`${
                isCategorySelected ? 'bl-2 border-primary' : ''
              } d-flex align-items-center p-2`}
            >
              {logo ? (
                <img src={logo} alt="courseSpec" width="20px" height="20px" />
              ) : (
                <Avatar
                  alt="No-icon"
                  variant="rounded"
                  sx={{ width: 16, height: 16 }}
                  style={{
                    backgroundColor: isCategorySelected ? '#E1F5FA' : '#6b7280',
                    color: isCategorySelected ? '#2159BA' : '#eff9fb',
                  }}
                >
                  <div className="f-10 text-capitalize">{category.get('categoryName', '')[0]}</div>
                </Avatar>
              )}
              <span className="fw-400 flex-grow-1 f-12 text-capitalize pl-2">
                {category.get('categoryName', '')}
              </span>
              {category.get('isDefault', false) && (
                <div className={`text-grey f-10 ${query === 'forms' ? 'paddingRight' : ''}`}>
                  DEFAULT
                </div>
              )}
            </section>
          </Paper>
        );
      })}
    </div>
  );
};
const ConfigureCategories = ({ children, params, getData, type }) => {
  const forms = useSelector(selectedForms);
  const param = new URLSearchParams(params);
  const pageNo = param.get('pageNo');
  const limit = param.get('limit');
  const searchKey = param.get('searchKey') ?? '';
  const concatPagination = `${pageNo}+${limit}`;
  const checkSize = forms.getIn([type, 'forms', searchKey, concatPagination], List());
  const [updateApi] = useCallApiHook(getData);
  useDebounce(getData, param.get('searchKey', ''), checkSize.size === 0, params);
  useEffect(() => {
    if (type) updateApi(params);
  }, [pageNo, limit, type]);
  return <>{children}</>;
};
ConfigureCategories.Configure = ({ category, params }) => {
  const [isEdit, setIsEdit] = useState(true);
  const actions = category.get('actions', Map());
  const editAccess = useCheckEditAccess('isEditAccessConfigureTemplate');
  const action = actions
    .entrySeq()
    .filter(([_, ele]) => ele)
    .map(([key]) => baseOptions[key].label)
    .toJS()
    .join(', ');
  const [configure, handleConfigure] = useBooleanHook(false);
  const handleClose = () => {
    handleConfigure();
    setIsEdit(true);
  };
  return (
    <>
      <header className="d-flex align-items-center">
        <section className="flex-grow-1">
          <div className="f-26 d-flex align-items-center text-capitalize">
            {category.get('categoryName', '')}
            {editAccess && (
              <span
                onClick={handleClose}
                className="text_underline text-primary f-12 cursor-pointer fw-500  rounded p-1 bgLight ml-2"
              >
                Configured
              </span>
            )}
          </div>
          <div className="d-flex align-items-center gap-8 text-lGrey f-14 fw-400">
            <img src={staffName} alt={'staffName'} />
            <span className="text-capitalize">
              {category.getIn(['createdBy', 'name', 'first'], '') +
                ' ' +
                category.getIn(['createdBy', 'name', 'last'], '')}
            </span>
            <CircleIcon sx={circleSX} />
            <span>
              <img src={date_range} alt={'date_range'} className="pr-1" />
              {moment(category.get('updatedAt', new Date())).format('DD MMM YYYY - hh:mm A')}
            </span>
            <CircleIcon sx={circleSX} />
            <img src={courseLevel} alt={'courseLevel'} />
            <span>Level: {jsUcfirstAll(category.get('level', 'course'))}</span>
            <CircleIcon sx={circleSX} />
            <span>
              Category For: {category.get('categoryFor', '') === 'annual' ? 'Annual' : 'Periodic'}{' '}
            </span>
          </div>
          {action.length > 0 && (
            <div className="">
              <Checkbox
                className="py-0"
                checked={true}
                color="default"
                size="small"
                sx={checkBoxSx}
              />
              <span className="text-lGrey f-14 px-1">{getShortString(action, 100)}</span>
            </div>
          )}
          {category.get('describe', '').trim() && (
            <div className="d-flex text-lGrey gap-3 f-14 fw-400 text-capitalize">
              Describe:
              <Tooltip
                title={category.get('describe', '').length > 100 && category.get('describe', '')}
              >
                {category.get('describe', '').substring(0, 100)}
              </Tooltip>
            </div>
          )}
        </section>
        {editAccess && (
          <FormConfigurationRedirect
            existingData={Map({ dialogTitle: 'New Form' })}
            params={params}
          >
            <MuiButton
              sx={{
                backgroundColor: '#147AFC !important',
                color: '#FFFFFF',
                padding: '5px 17px',
              }}
              className="f-14 text-nowrap"
            >
              + New Form
            </MuiButton>
          </FormConfigurationRedirect>
        )}
        {configure && (
          <ConfigureModal
            isEdit={isEdit}
            setIsEdit={setIsEdit}
            category={category}
            open={configure}
            handleClose={handleClose}
          />
        )}
      </header>
      <Divider className="mt-2 mb-1" />
    </>
  );
};
ConfigureCategories.Header = ({ type, search }) => {
  const forms = useSelector(selectedForms);
  const totalCount = forms.getIn([type, search, 'totalCount'], 0);
  return (
    <>
      <div className="d-flex align-items-center ">
        <span className="f-25 fw-400 text-capitalize mr-2">{type}</span> {totalCount} forms
      </div>
      <Divider className="pt-3" />
    </>
  );
};
ConfigureCategories.Duplicate = ({ category, params }) => {
  const [view, setView] = useBooleanHook(false);
  const [formName, setFormName] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const editAccess = useCheckEditAccess('isEditAccessConfigureTemplate');
  const open = Boolean(anchorEl);
  const handleClick = (formName) => (event) => {
    setAnchorEl(event.currentTarget);
    setFormName(formName);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleCloseView = () => setView();
  // const categoryName = category.get('categoryName', '');
  const level = category.get('level', '');
  const templateUrl = 'course-report';
  return (
    <section>
      <div className="f-14 text-lGrey pt-3 pb-2 pointer-none">Default</div>
      <div className="templateName">
        {category.get('template', List()).map((temp) => (
          <div className="d-flex border rounded f-14 p-2" key={temp.get('_id', '')}>
            <div className="flex-grow-1">{temp.get('formName', '')}</div>
            <div className="text_underline text-primary mr-2 cursor-pointer f-14" onClick={setView}>
              View
            </div>
            {editAccess && (
              <MoreVertIcon
                className="cursor-pointer"
                fontSize="small"
                onClick={handleClick(temp.get('formName', ''))}
              />
            )}
          </div>
        ))}
        {view && (
          <IncorporateIframeModal
            open={view}
            handleClose={handleCloseView}
            templateUrl={templateUrl}
            level={level}
          />
        )}
      </div>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
        transformOrigin={{ horizontal: 'right', vertical: 'right' }}
        anchorOrigin={{ horizontal: 'left', vertical: 'left' }}
      >
        <FormConfigurationRedirect
          handleClose={handleClose}
          params={params}
          existingData={Map({ dialogTitle: `Duplicate ${formName}` })}
        >
          <MenuItem>Duplicate</MenuItem>
        </FormConfigurationRedirect>
      </Menu>
    </section>
  );
};
ConfigureCategories.Forms = ({ type, setPages, search, menus, params, category = Map() }) => {
  const history = useHistory();
  const forms = useSelector(selectedForms);
  const [drawer, setDrawer] = useBooleanHook(false);
  const [deleteConformation, , setConformation, setDefault] = useNestedHook(Map());
  const [searchParams] = useSearchParams();
  const totalCount = forms.getIn([type, search, 'totalCount'], 0);
  const categoryIndex = searchParams.get('currentCategoryIndex');
  const [
    pagination,
    switchPagination,
    skipPagination,
    handleChange,
    setPagination,
  ] = usePaginationHook({
    totalCount,
    search,
  });
  const perPageSize = pagination.get('perPageSize', 0);
  const currentPage = pagination.get('currentPage', 0);
  const actions = category.get('actions', Map());
  const level = category.get('level', '');
  const concatTwoNumber = `${currentPage}+${perPageSize}`;
  const [configureTemplate] = useIsReduxEmpty(selectedConfigureTemplate, getConfigureTemplate);
  const categoryLevel = configureTemplate.getIn([categoryIndex, 'level'], '');
  const categoryId = configureTemplate.getIn([categoryIndex, '_id'], '');
  const [updateApi] = useCallApiHook(updateCategoryForm);
  const [updateFormStatus] = useCallApiHook(updateCategoryForm);
  const [getForms] = useCallApiHook(getCategoryForm);
  const [setReduxData] = useCallApiHook(setData);
  const [expanded, handleChangeAccordion] = useState(0);
  const callBack = () => {
    setReduxData(fromJS({ categoryForms: { deleted: {} } }));
    getForms(params);
    setDefault();
  };
  const handleToggle = (fromId, categoryId) => () => {
    const previousId = searchParams.get('categoryFormId');
    if (!previousId || previousId !== fromId) {
      searchParams.set('categoryFormId', fromId);
      // searchParams.set('categoryId', categoryId);
      history.push({ search: searchParams.toString() });
    }
    setDrawer();
  };
  const routeToCreateSettings = ({
    categoryFormName,
    categoryFormIndex,
    categoryFormId,
    step,
    categoryId,
    level,
    assignedPrograms,
    conductParams,
  }) => {
    const newQuery = `${searchParams.get('query')?.split(' ').join('+')}+${categoryFormName
      .split(' ')
      .join(',')}`;
    searchParams.delete('query');
    const updatedSearch = searchParams.toString();
    const updatedUrl = `/globalConfiguration-v1/qa_pc_configuration?${updatedSearch}&query=${newQuery}&categoryFormIndex=${categoryFormIndex}&categoryFormId=${categoryFormId}&step=${
      step - 1
    }&categoryId=${categoryId}&level=${level}&assignedPrograms=${assignedPrograms.join(
      ',+'
    )}&${conductParams}`;
    history.push(updatedUrl);
  };
  const editAccess = useCheckEditAccess('isEditAccessConfigureTemplate');
  const action = actions
    .entrySeq()
    .filter(([_, ele]) => ele)
    .map(([key]) => {
      const value = baseOptions[key].value;
      return { [value]: value };
    })
    .concat([{ level }]);
  const conductParams = generateParams(action);
  const updateForm = (_id) =>
    updateApi({ categoryFormId: _id, categoryId, isDeleted: true }, callBack);
  useEffect(() => {
    setPages((previous) =>
      previous.set('pageNo', currentPage).set('limit', perPageSize).set('searchKey', search)
    );
  }, [pagination, search]);
  useEffect(() => {
    setPagination((previous) => previous.set('currentPage', 1));
  }, [categoryIndex]);
  return (
    <>
      <div className=" g-config-scrollbar q360-forms-scroll">
        {forms
          .getIn([type, 'forms', search, concatTwoNumber], List())
          .map((element, elementIndex) => {
            const archiveCategoryId = element.getIn(['categoryId', '_id'], '');
            const _id = element.get('_id', '');
            const status = element.get('status', '');
            const formName = element.get('formName', '');
            const formType = useCurrentPage('forms');
            const step = element.get('step', 0);
            const statusStyle = statusData.get(status, Map());
            const statusCss = statusStyle.get('statusCss', '');
            const statusDisplay = statusStyle.get('display', '');
            const selectedPrograms = element.get('selectedProgram', List());
            const selectedInstitution = element.get('selectedInstitution', List());
            const institutionNames = selectedInstitution.map((institution) =>
              institution.get('institutionName', '')
            );
            const title = selectedPrograms.map((program) => program.get('programName', ''));
            const institutionTitle = selectedInstitution.map((institution) =>
              institution.get('institutionName', '')
            );
            const assignedPrograms = level === 'institution' ? institutionNames : title;
            const clickRoute = goToSteps(
              elementIndex,
              conductParams,
              assignedPrograms
            )(element, type);
            const isShow = !['archive', 'deleted'].includes(type) && status !== 'inactive';
            const activeStep = checkCurrentCompletedStep(step);
            const isPublish = activeStep === 4 && !['published', 'inactive'].includes(status);
            const isAction = status !== 'draft' || ['archive', 'deleted'].includes(type);
            const isPublished = status === 'published';
            const isUnPublished = status === 'unpublished';
            const updateStatus = (elementIndex) =>
              updateFormStatus(
                {
                  status: 'published',
                  categoryFormId: _id,
                  categoryId,
                },
                callBack(elementIndex)
              );

            const callBack = (elementIndex) => () =>
              getForms(params, setConformation(['publish', elementIndex], false));
            const onClickRoute = (step) => () => {
              if (!editAccess || formType === 'archive') return;
              routeToCreateSettings({
                categoryFormId: _id,
                categoryFormIndex: elementIndex,
                categoryFormName: formName,
                step: step,
                categoryId,
                level,
                assignedPrograms: level === 'institution' ? institutionNames : title,
                conductParams,
              });
            };
            return (
              <>
                <section
                  className="d-flex align-items-center pl-3 box-shadow py-2 border rounded mt-3 mr-2"
                  key={elementIndex}
                >
                  <div className="w-50">
                    <div className={`f-16 fw-500 text-capitalize`}>
                      <span
                        className={`${
                          !editAccess || formType === 'archive' || formType === 'deleted'
                            ? ''
                            : 'cursor-pointer'
                        }`}
                        onClick={() => {
                          if (!editAccess || formType === 'archive' || formType === 'deleted')
                            return;
                          clickRoute();
                        }}
                      >
                        {formName}
                      </span>
                      <span className={`${statusCss} rounded f-10 p-1 ml-2`}>{statusDisplay}</span>
                    </div>
                    <div className="d-flex align-items-center text-lGrey f-14 fw-400 gap-8 py-1 ">
                      <div>
                        <img src={courseLevel} />
                        <span className="pl-1">Assigned:</span>
                      </div>
                      {/* {categoryLevel === 'institution' && <HtmlToolTip title={institutionNames} />} */}
                      {selectedInstitution.size > 0 && <HtmlToolTip title={institutionTitle} />}
                      {categoryLevel !== 'institution' && !selectedInstitution.size && (
                        <>
                          <HtmlToolTip title={title} />
                          <div
                            className="text_underline text-primary  f-12 fw-500"
                            onClick={handleToggle(_id, archiveCategoryId)}
                          >
                            More Details
                          </div>
                        </>
                      )}
                    </div>
                    {element.get('describe', '').trim() && (
                      <div className="text-lGrey f-14 fw-400 py-1">
                        Describe: {getShortString(element.get('describe', ''), 50)}
                      </div>
                    )}
                  </div>
                  <div className="form-action-grid w-50 ">
                    <div className="grid-self-center ">
                      <CustomizedSteppers
                        activeStep={activeStep}
                        categoryForm={element.set(
                          'step',
                          statusDisplay === 'Published' ? 4 : element.get('step', 0)
                        )}
                        onClickRoute={onClickRoute}
                      />
                    </div>
                    <div className="grid-self-center  d-flex gap-15  justify-content-center algin-items-center">
                      {isShow && editAccess && !isPublished && (
                        <FormConfigurationRedirect
                          params={params}
                          existingData={Map({
                            formName: element.get('formName', ''),
                            formType: element.get('formType', ''),
                            dialogTitle: 'Edit Form',
                            describe: element.get('describe', ''),
                            formId: element.get('_id', ''),
                            matchingForm: element.get('matchingForm', List()),
                            selectedProgram: element.get('selectedProgram', List()),
                            incorporateMandatory: element.get('incorporateMandatory', List()),
                            selectedInstitution: element.get('selectedInstitution', List()),
                          })}
                        >
                          <EditIcon fontSize="small" sx={{ cursor: 'pointer' }} />
                        </FormConfigurationRedirect>
                      )}
                      {isShow && editAccess && !isPublished && (
                        <div>
                          <DeleteIcon
                            fontSize="small"
                            sx={{ color: 'red', cursor: 'pointer' }}
                            onClick={() => setConformation(['delete', elementIndex], true)}
                          />
                        </div>
                      )}
                    </div>
                    {(isPublish || isAction) && (
                      <div
                        className={`grid-self-center d-flex gap-15 ${
                          isAction ? 'justify-content-end pr-3' : ''
                        } algin-items-center`}
                      >
                        {(isPublish && editAccess && !disableStepperFormsWise.includes(type)) ||
                        (isUnPublished && editAccess && !disableStepperFormsWise.includes(type)) ? (
                          <MuiButton
                            onClick={() => setConformation(['publish', elementIndex], true)}
                            sx={{
                              backgroundColor: '#147AFC !important',
                              color: '#FFFFFF',
                              padding: '7px 12px',
                            }}
                          >
                            Publish
                          </MuiButton>
                        ) : null}
                        {isAction && editAccess && (
                          <ThreeDots
                            menus={menus}
                            fromStatus={status}
                            categoryFormId={_id}
                            categoryId={type}
                            element={element}
                            params={params}
                          />
                        )}
                      </div>
                    )}
                  </div>
                </section>

                <ConfirmationPopup
                  isOpen={deleteConformation.getIn(['delete', elementIndex], false)}
                  handleClose={() => setConformation(['delete', elementIndex], false)}
                >
                  <div className="text-capitalize f-18">
                    <Badge
                      sx={{
                        margin: '0 10px 0 15px',
                        '& .MuiBadge-badge': {
                          backgroundColor: '#FECACA',
                          color: '#EF4444',
                          padding: '20px',
                          height: '28px',
                          borderRadius: '50%',
                          width: '29px',
                        },
                      }}
                      badgeContent={
                        <InfoOutlinedIcon sx={{ color: '#EF4444', transform: 'rotate(180deg)' }} />
                      }
                    />

                    <span className="px-4">{formName}</span>
                  </div>
                  {/* <div>This action will applied for while published for this Category.</div> */}
                  <div>Are you sure you want to Delete ?</div>
                  <div></div>
                  <div>
                    <Button
                      variant="outlined"
                      clicked={() => setConformation(['delete', elementIndex], false)}
                      className="text-capitalize px-4 text-secondary border-secondary bold mx-2"
                    >
                      Cancel
                    </Button>
                    <Button
                      className="text-capitalize px-4 bg-danger text-white "
                      variant="contained"
                      clicked={() => updateForm(_id)}
                    >
                      Continue
                    </Button>
                  </div>
                </ConfirmationPopup>
                <ConfirmationPopup
                  isOpen={deleteConformation.getIn(['publish', elementIndex], false)}
                  handleClose={() => setConformation(['publish', elementIndex], false)}
                >
                  <div className="text-capitalize f-18">
                    <Badge
                      sx={{
                        margin: '0 10px 0 15px',
                        '& .MuiBadge-badge': {
                          backgroundColor: '#BBF7D0',
                          color: '#4B5563',
                          padding: '20px',
                          height: '28px',
                          borderRadius: '50%',
                          width: '29px',
                        },
                      }}
                      badgeContent={<DoneIcon sx={{ color: '#22C55E' }} />}
                    />
                    <span className="px-4">{formName}</span>
                  </div>
                  <div>This action will applied for while published for this Category.</div>
                  <div>Are you sure you want to Publish ?</div>
                  <div>
                    <Button
                      variant="outlined"
                      clicked={() => setConformation(['publish', elementIndex], false)}
                      className="text-capitalize px-4 text-secondary border-secondary bold mx-2"
                    >
                      Cancel
                    </Button>
                    <Button
                      className="text-capitalize px-4 bg-primary text-white "
                      variant="contained"
                      clicked={() => updateStatus(elementIndex)}
                    >
                      Proceed
                    </Button>
                  </div>
                </ConfirmationPopup>
              </>
            );
          })}

        {drawer && (
          <DrawerRight
            open={drawer}
            handleClose={handleToggle()}
            expanded={expanded}
            handleChange={handleChangeAccordion}
          />
        )}
        {forms.getIn([type, 'forms', search, concatTwoNumber], List()).size == 0 && (
          <div
            className="text-lGrey d-flex justify-content-center f-16  align-items-center"
            style={containerHeight}
          >
            No Data found!
          </div>
        )}
      </div>
      {totalCount > 5 && (
        <PaginationQ360
          totalSize={Math.ceil(totalCount / perPageSize)}
          paginationState={pagination}
          switchPagination={switchPagination}
          skipPagination={skipPagination}
          handleChange={handleChange}
        />
      )}
    </>
  );
};

const ThreeDots = ({ menus, fromStatus, categoryFormId, categoryId, params, element }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  const [updateFormStatus] = useCallApiHook(updateCategoryForm);
  const [callUnPublish] = useCallApiHook(createUnpublishedForm);
  const [getForms] = useCallApiHook(getVarietyOfForms);
  const [getCategoryForms] = useCallApiHook(getCategoryForm);
  const [setDataRedux] = useCallApiHook(setData);
  const isPublished = fromStatus === 'published';
  const categoryIdInForm = element.getIn(['categoryId', '_id'], '');
  const param = new URLSearchParams(params);
  const formType = param.get('formType');
  const isPublishedOrInactive =
    (isPublished || fromStatus === 'inactive') && formType !== 'archive' && formType !== 'deleted';
  const callBack = (status) => () => {
    if (status === 'archive') setDataRedux(fromJS({ categoryForms: { archive: {} } }));
    if (['archive', 'deleted'].includes(formType)) {
      setDataRedux(fromJS({ categoryForms: { [formType]: {} } }));
    }
    if (['inactive', 'published', 'archive', 'unPublished'].includes(status)) {
      getCategoryForms(params);
    } else {
      getForms(params);
    }
    handleClose();
  };
  const handleUpdate = (status, booleanValue = false) => {
    if (status.value === 'unPublished') {
      return callUnPublish(categoryFormId, callBack(status.value));
    }
    return updateFormStatus(
      {
        ...(status.value === 'archive' && { archive: booleanValue, categoryId }),
        ...(formType === 'deleted' && {
          isDeleted: booleanValue,
          categoryId: categoryIdInForm,
        }),
        ...(formType === 'archive' && {
          archive: booleanValue,
          categoryId: categoryIdInForm,
        }),
        ...(status.value === 'inactive' && { status: status.value, categoryId }),
        ...(status.value === 'published' && { status: status.value, categoryId }),
        categoryFormId,
      },
      callBack(status.value)
    );
  };
  return (
    <>
      <div>
        <MoreVertIcon className="cursor-pointer" fontSize="small" onClick={handleClick} />
      </div>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
        transformOrigin={{ horizontal: 'right', vertical: 'right' }}
        anchorOrigin={{ horizontal: 'left', vertical: 'left' }}
      >
        {isPublishedOrInactive
          ? isPublished
            ? [inActive, unPublished, ...menus].map((status, index) => (
                <div key={index}>
                  <MenuItem onClick={() => handleUpdate(status, true)}>{status.name}</MenuItem>
                </div>
              ))
            : [active].map((status, index) => (
                <div key={index}>
                  <MenuItem onClick={() => handleUpdate(status, true)}>{status.name}</MenuItem>
                </div>
              ))
          : menus.map((status, index) => (
              <div key={index}>
                <MenuItem onClick={() => handleUpdate(status)}>{status.name}</MenuItem>
              </div>
            ))}
      </Menu>
    </>
  );
};
const CourseCard = ({ courses, courseType }) => {
  return (
    <>
      <div className="f-12 text-capitalize mt-2">{courseType}</div>
      <div className="rounded border pt-2 px-3 pb-3  my-2">
        {courses.map((course, index) => {
          const isShared = course.get('sharedFormOthers', false);
          const courseName = course.get('courseName', '');
          return (
            <div key={index}>
              <div className=" pt-3 pb-2 fw-500 f-14 text-capitalize d-flex align-items-center text-mGrey">
                {courseName}
                {isShared && (
                  <ShareOutlinedIcon className="mx-2" color="primary" fontSize="small" />
                )}
              </div>
              {courses.size !== index + 1 && <Divider className="mt-2" />}
            </div>
          );
        })}
      </div>
    </>
  );
};
const CurriculumCard = ({ curriculum }) => {
  return (
    <div className="rounded border pt-2 px-3 pb-3  my-2">
      {curriculum.map((course, index) => {
        const curriculumName = course.get('curriculumName', '');
        return (
          <div
            key={index}
            className="border-bottom pt-3 pb-2 fw-500 f-14 text-capitalize d-flex align-items-center text-mGrey"
          >
            {curriculumName}
          </div>
        );
      })}
    </div>
  );
};
