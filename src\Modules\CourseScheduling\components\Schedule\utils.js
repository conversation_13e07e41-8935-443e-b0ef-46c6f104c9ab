import { isBefore, isSameHour, isSameMinute, isValid } from 'date-fns';
import { fromJS } from 'immutable';
import { t } from 'i18next';
import { isIndGroup, studentGroupRename } from 'utils';

import { getFormattedGroupName, getStartEndDate } from '../utils';

export function validateSchedule(
  schedule,
  isMergeSession = false,
  checkTopicIsRequired = false,
  isSubTypeRequired = false,
  programId
) {
  const errorFields = [];
  let fields = ['schedule_date', 'start', 'end', 'student_groups', 'subjects', 'staffs', 'mode'];
  if (isMergeSession) {
    fields = ['schedule_date', 'start', 'end', 'subjects', 'staffs', 'mode'];
  }
  if (checkTopicIsRequired) {
    fields.unshift('_topic_id');
  }
  if (isSubTypeRequired) {
    fields.unshift('sub_type');
  }
  if (schedule['mode'] === 'remote') {
    fields.unshift('_infra_id');
  }

  fields.forEach((key) => {
    switch (key) {
      case '_topic_id': {
        if (!schedule[key]) {
          // errorFields.push('Please select a topic');
          errorFields.push(t('course_schedule.validation.select_topic'));
        }
        break;
      }
      case 'sub_type': {
        if (!schedule[key]) {
          // errorFields.push('Please select a type');
          errorFields.push(t('course_schedule.validation.select_type'));
        }
        break;
      }
      case 'schedule_date': {
        if (!schedule[key]) {
          // errorFields.push('Please choose a date');
          errorFields.push(t('course_schedule.validation.choose_date'));
        } else if (!isValid(new Date(schedule[key]))) {
          // errorFields.push('Invalid date. Please choose a valid date.');
          errorFields.push(t('course_schedule.validation.choose_valid_date'));
        }
        break;
      }
      case 'start': {
        const startDate = getStartEndDate(fromJS(schedule[key]));
        if (!startDate) {
          // errorFields.push('Please choose a valid start time');
          errorFields.push(t('course_schedule.validation.valid_start_time'));
        }
        break;
      }
      case 'end': {
        const startDate = getStartEndDate(fromJS(schedule.start));
        const endDate = getStartEndDate(fromJS(schedule[key]));
        if (!startDate) {
          // errorFields.push('Please choose a valid start time');
          errorFields.push(t('course_schedule.validation.valid_start_time'));
        } else if (!endDate) {
          // errorFields.push('Please choose a valid end time');
          errorFields.push(t('course_schedule.validation.valid_end_time'));
        } else if (isSameHour(startDate, endDate) && isSameMinute(startDate, endDate)) {
          // errorFields.push('Start and end time cannot be same');
          errorFields.push(t('course_schedule.validation.same_time'));
        } else if (isBefore(endDate, startDate)) {
          // errorFields.push('End time should be greater than start time');
          errorFields.push(t('course_schedule.validation.greater_start_time'));
        }
        break;
      }
      case 'student_groups': {
        if (!schedule[key].length) {
          // errorFields.push('Please select one or more course groups');
          errorFields.push(t('course_schedule.validation.select_course_group'));
        } else {
          const courseGroups = schedule[key];
          const emptySessionGroups = [];
          courseGroups.forEach((group) => {
            if (!group.session_group.length) {
              emptySessionGroups.push(
                getFormattedGroupName(group.group_name, isIndGroup(group.group_name) ? 1 : 2)
              );
            }
          });
          if (emptySessionGroups.length) {
            errorFields.push(
              `Please select one or more session groups under the course groups '${studentGroupRename(
                emptySessionGroups.join(', '),
                programId
              )}'`
            );
          }
        }
        break;
      }
      case 'subjects': {
        if (!schedule[key].length) {
          // errorFields.push('Please select one or more subjects');
          errorFields.push(t('course_schedule.validation.select_subjects'));
        }
        break;
      }
      case 'staffs': {
        if (schedule.outsideCampus) {
          if (
            schedule.staffs.length === 0 &&
            schedule.classLeaders.size === 0 &&
            schedule.externalStaffs.size === 0
          ) {
            errorFields.push('Please select one or more Attendance Taking User');
          }
        } else {
          if (!schedule[key].length) {
            errorFields.push(t('course_schedule.validation.select_staffs'));
          }
        }
        break;
      }
      case 'mode': {
        if (!schedule[key]) {
          // errorFields.push('Please select a mode');
          errorFields.push(t('course_schedule.validation.select_mode'));
        }
        break;
      }
      case '_infra_id': {
        if (!schedule[key]) {
          // errorFields.push('Please select a infrastructure');
          errorFields.push(t('course_schedule.validation.select_infrastructure'));
        }
        break;
      }
      default:
        break;
    }
  });
  return errorFields.length ? errorFields[0] : '';
}

const hexColors = ['#F9D1FF', '#D1F4FF', '#D5FFD1', '#FFE2D1', '#FFD1D1', '#D5D1FF'];
export function getColor(index) {
  const colorIndex = index % hexColors.length;
  return hexColors.slice(colorIndex, colorIndex + 1)[0];
}

export function validateManageCourseSchedule(schedule, checkTopicIsRequired) {
  const errorFields = [];
  let arrays = ['session_details', 'subject', 'staff', 'mode'];
  if (checkTopicIsRequired) {
    arrays.unshift('_topic_id');
  }
  if (schedule['mode'] === 'remote') {
    arrays.unshift('_infra_id');
  }
  arrays.forEach((key) => {
    switch (key) {
      case 'session_details': {
        if (!schedule[key].length) {
          errorFields.push('Please select a session');
        }
        break;
      }
      case 'subject': {
        if (!schedule[key].length) {
          errorFields.push('Please select a subject');
        }
        break;
      }
      case 'staff': {
        if (!schedule[key].length) {
          errorFields.push('Please select one or more staffs');
        }
        break;
      }
      case 'mode': {
        if (!schedule[key]) {
          errorFields.push('Please select a mode');
        }
        break;
      }
      case '_topic_id': {
        if (!schedule[key]) {
          errorFields.push('Please select a topic');
        }
        break;
      }
      case '_infra_id': {
        if (!schedule[key]) {
          errorFields.push('Please select a infrastructure');
        }
        break;
      }
      default:
        break;
    }
  });
  return errorFields.length ? errorFields[0] : '';
}

export function getRemotePlatform(existingSchedule) {
  return existingSchedule.get('remotePlatform', '') !== null
    ? existingSchedule.get('remotePlatform', '')
    : 'zoom';
}

export function identifyValueSchedule(value, countryCodeLength) {
  const gmailPattern = /^[a-zA-Z0-9._-]+@gmail\.com$/;
  const isGmail = gmailPattern.test(value);
  const mobilePattern = new RegExp(`^[0-9]{${countryCodeLength},}$`);
  const isMobileNumber = mobilePattern.test(value);

  if (isGmail) {
    return 'Gmail';
  } else if (isMobileNumber) {
    return 'MobileNumber';
  } else {
    return 'Unknown';
  }
}
