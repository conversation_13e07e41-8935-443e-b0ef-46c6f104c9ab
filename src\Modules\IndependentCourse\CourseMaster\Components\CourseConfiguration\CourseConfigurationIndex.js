import React, { useEffect, useState, useRef } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { Map, List } from 'immutable';
import { useParams } from 'react-router-dom';
import { dString } from 'utils';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import * as actions from '_reduxapi/program_input/v2/actions';
import {
  selectFullCourseDetails,
  selectIsLoading,
  selectSessionFlowDetails,
  selectIndependentCourses,
  selectSessionDeliveryTypes,
  selectSessionDeliveryTypesStatus,
  selectProgramCurriculum,
  selectIndependentSubjects,
} from '_reduxapi/program_input/v2/selectors';
import { getURLParams, removeURLParams, removeLastOccurrence } from 'utils';
import ConfigurationHeader from 'Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/CourseConfiguration/Component/Header';
import SessionFlow from 'Modules/ProgramInput/v2/ConfigurationIndex/CourseMaster/CourseConfiguration/Component/SessionFlow/SessionFlowIndex';
import parentContext from 'Modules/ProgramInput/v2/ProgramInputContext/context';
import Configuration from './Configuration/ConfigurationIndex';
import { getIndependentCreditMode } from 'Modules/Shared/v2/Configurations';

function CourseConfigurationIndex({
  setData,
  getFullCourseDetails,
  fullCourseDetails,
  isLoading,
  getSessionDeliveryTypes,
  getSessionFlowList,
  sessionDeliveryTypes,
  sessionFlowDetails,
  sessionDeliveryTypesStatus,
  coursesList,
  getIndependentCoursesList,
  programCurriculum,
  getProgramCurriculum,
  updateCourseDetails,
  subjectsList,
  getIndependentSubjectsList,
  postSession,
}) {
  const _course_id = getURLParams('cid', true);
  const { id } = useParams();
  const institutionID = dString(id);
  const status = getURLParams('next');
  const [nextPage, setNextPage] = useState(status ? true : false);
  const history = useHistory();
  const match = useRouteMatch();
  const saveClickedRef = useRef();
  const openThemeRef = useRef();
  const saveConfigRef = useRef();
  const creditHours = getIndependentCreditMode();
  const creditHoursMode = creditHours.get('mode', '');
  const isStandardMode = creditHoursMode === 'standard';
  const getCourseList = ({ pageNo = 1, limit = 100 }) => {
    let params = { _institution_id: institutionID, pageNo, limit };
    getIndependentCoursesList(params);
  };
  useEffect(() => {
    getCourseList({});
    getFullCourseDetails(_course_id, 'independent');
    getProgramCurriculum(institutionID);
  }, [_course_id]); // eslint-disable-line
  const goToListPage = () => {
    const url = removeLastOccurrence(match.url, '/configuration');
    const search = removeURLParams(history.location, ['cid', 'next']);
    history.push(`${url}?${search}`);
  };
  const setEmptyValues = () => {
    setData(Map({ sessionFlowList: Map({}) }));
    setData(Map({ fullCourseDetails: Map({}) }));
  };

  const defaultCallBacks = (redirectURL = '') => {
    setEmptyValues();
    redirectURL === '' ? goToListPage() : history.push(`${match.url}/${redirectURL}`);
  };
  const onClickBack = () => {
    setEmptyValues();
    setNextPage(false);
    getFullCourseDetails(_course_id, 'independent');
  };
  const onCourseDetailSave = (draft = false) => {
    let courseRequestData = saveConfigRef.current(draft);
    if (courseRequestData) {
      const successCallBack = () => {
        draft ? defaultCallBacks() : setNextPage(true);
      };
      courseRequestData.draft = draft;
      updateCourseDetails(courseRequestData, successCallBack, () => {}, 'independent');
    }
  };
  const onSessionDetailsSave = (isDraft = false, redirectURL = '', callingFrom = '') => {
    const sessionData = saveClickedRef.current(callingFrom);
    const { sessions, isWeekActiveForIndependentCourse } = sessionData;

    if (sessions) {
      const callBackFun = () => {
        if (callingFrom === 'theme') {
          openThemeRef.current('right', true)();
          getSessionFlowList({ pageNo: 1, limit: 200, _course_id }, 'independent');
          return;
        }
        defaultCallBacks(redirectURL);
      };

      let Data = {
        sessions,
        isWeekActiveForIndependentCourse,
        _institution_id: institutionID,
        _course_id,
        areActive: !isDraft,
      };
      postSession('add', Data, callBackFun, 'independent');
    }
  };
  const value = {
    _course_id,
    courseId: _course_id,
    setData,
    saveConfigRef,
    saveClickedRef,
    openThemeRef,
    institutionID,
    isStandardMode,
    fullCourseDetails,
    sessionDeliveryTypes,
    getSessionDeliveryTypes,
    sessionFlowDetails,
    getSessionFlowList,
    sessionDeliveryTypesStatus,
    coursesList,
    programCurriculum,
    subjectsList,
    getIndependentSubjectsList,
    onSessionDetailsSave,
  };
  return (
    <parentContext.programDetailsContext.Provider
      value={{ institutionId: institutionID, programDetails: Map() }}
    >
      <parentContext.courseConfigurationContext.Provider value={value}>
        <div className="main pb-5">
          <ConfigurationHeader
            onClickBack={onClickBack}
            nextPage={nextPage}
            onSessionDetailsSave={onSessionDetailsSave}
            onCourseDetailsSave={onCourseDetailSave}
            goBack={defaultCallBacks}
            title={`${fullCourseDetails.get('courseCode', '')} `}
            goToListPage={goToListPage}
            isLoading={isLoading}
            fullCourseDetails={fullCourseDetails}
          />
          {!nextPage ? <Configuration /> : <SessionFlow />}
        </div>
      </parentContext.courseConfigurationContext.Provider>
    </parentContext.programDetailsContext.Provider>
  );
}

CourseConfigurationIndex.propTypes = {
  getFullCourseDetails: PropTypes.func,
  fullCourseDetails: PropTypes.instanceOf(Map),
  getSessionDeliveryTypes: PropTypes.func,
  sessionDeliveryTypes: PropTypes.instanceOf(List),
  getSessionFlowList: PropTypes.func,
  sessionFlowDetails: PropTypes.instanceOf(Map),
  sessionDeliveryTypesStatus: PropTypes.bool,
  getIndependentCoursesList: PropTypes.func,
  coursesList: PropTypes.instanceOf(Map),
  getProgramCurriculum: PropTypes.func,
  programCurriculum: PropTypes.instanceOf(List),
  getIndependentSubjectsList: PropTypes.func,
  subjectsList: PropTypes.instanceOf(Map),
  isLoading: PropTypes.bool,
  setData: PropTypes.func,
  updateCourseDetails: PropTypes.func,
  postSession: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    fullCourseDetails: selectFullCourseDetails(state),
    sessionDeliveryTypes: selectSessionDeliveryTypes(state),
    sessionFlowDetails: selectSessionFlowDetails(state),
    coursesList: selectIndependentCourses(state),
    sessionDeliveryTypesStatus: selectSessionDeliveryTypesStatus(state),
    isLoading: selectIsLoading(state),
    programCurriculum: selectProgramCurriculum(state),
    subjectsList: selectIndependentSubjects(state),
  };
};
export default connect(mapStateToProps, actions)(CourseConfigurationIndex);
