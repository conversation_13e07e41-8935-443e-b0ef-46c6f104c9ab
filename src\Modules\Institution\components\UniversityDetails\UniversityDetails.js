import React, { Component, Suspense } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Accordion, Dropdown, Card } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import { Editor } from 'react-draft-wysiwyg';
import { EditorState, convertToRaw, convertFromRaw } from 'draft-js';
import MoreVertIcon from '@mui/icons-material/MoreVert';
//import IconButton from '@mui/material/IconButton';
import Breadcrumb from 'Widgets/Breadcrumb/v2/Breadcrumb';
import manage_accounts from '../../../../Assets/manage_accounts.svg';
import school from '../../../../Assets/school.svg';

import {
  selectInstitution,
  selectDescription,
  selectPortFolio,
  selectIsLoading,
  selectMessage,
  selectBreadCrumb,
} from '_reduxapi/institution/selectors';
import * as actions from '_reduxapi/institution/actions';
import { selectUserInfo } from '_reduxapi/Common/Selectors';

import AddPortfolioModal from './Modal/AddPortfollio';
import EditAboutUniversityModal from './Modal/EditAboutUniversityModal';
import EditBasicDetails from './Modal/EditBasicDetails';
import CancelModal from '../AllColleges/Modal/Cancel';

import parentContext from '.././Context/university-context';
import ParentComponent from './ParentComponent';
import { BasicDetails, checkValidation, checkValidationEditor } from './udUtil';
import InstitutionIndex from './index';
import ShowMultiMedia from './ShowMultiMedia';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from 'Modules/Utils/Snackbars';

const DeleteModal = React.lazy(() => import('../AllColleges/Modal/Delete'));

class UniversityDetails extends Component {
  constructor() {
    super();
    this.state = {
      programType: [],
      selectedProgramType: 'all',
      searchText: '',
      arrow: '',
      show: false,
      universityDescription: Map(),
      portfolios: List(),
      editorState: EditorState.createEmpty(),
      portfolioName: '',
      portfolioIndex: 0,
      institutionDetails: {
        name: '',
        code: '',
        type: '',
        accreditation: '',
        logo: '',
        address: '',
        userID: '',
      },
      cancelShow: false,
      deleteShow: false,
      index: '',
      deleteItem: '',
    };
  }
  componentDidMount() {
    const { getInstitution, setBreadCrumb } = this.props;

    //const id = getURLParams('id', true);
    getInstitution(this.getUserID());
    setBreadCrumb([{ to: '#', label: `Profile` }]);
  }
  componentDidUpdate(prevProps) {
    if (this.props.institution !== prevProps.institution) {
      const { institution } = this.props;
      if (institution.size) {
        let data = {
          name: institution.get('name', ''),
          code: institution.get('code', ''),
          type: institution.get('type', ''),
          noOfColleges: institution.get('noOfColleges', ''),
          accreditation: institution.get('accreditation', List()),
          logo: institution.get('presignedLogoURL', ''),
          logoURL: institution.get('logo', ''),
          isUniversity: institution.get('isUniversity', false),
          address: institution.getIn(['address', 'address'], ''),
          city: institution.getIn(['address', 'city', 'name'], ''),
          countryId: institution.getIn(['address', 'country', 'countryId'], ''),
          stateId: institution.getIn(['address', 'state', 'stateId'], ''),
          state: institution.getIn(['address', 'state', 'name'], ''),
          district: institution.getIn(['address', 'district'], ''),
          country: institution.getIn(['address', 'country', 'name'], ''),
          zipCode: institution.getIn(['address', 'zipCode'], ''),
          _id: institution.get('_id', ''),
          collegeCount: institution.get('childInstitutionCount', 0),
        };
        this.setState({
          institutionDetails: data,
          universityDescription: institution.get('instituteDescription', Map()),
          portfolios: institution.get('portfolio', List()),
        });
      }
    }
    if (this.props.description !== prevProps.description) {
      this.setState({ universityDescription: this.props.description });
    }
    if (this.props.portfolio !== prevProps.portfolio) {
      this.setState({ portfolios: this.props.portfolio });
    }
  }
  getUserID = () => {
    const { loggedInUserData } = this.props;
    return loggedInUserData.getIn(['university', 'institutionId'], '');
  };
  handleClose = (selectedModal, type = '') => {
    // const { show } = this.state;
    let temp = {};
    if (type === 'cancel') {
      temp = {
        cancelShow: true,
      };
    } else if (type === 'no') {
      temp = {
        cancelShow: false,
      };
    } else {
      temp = {
        show: false,
        // show: { ...show, [selectedModal]: false },
        editorState: EditorState.createEmpty(),
        portfolioName: '',
        portfolioIndex: 0,
        cancelShow: false,
      };
    }
    this.setState(temp);
  };

  handleShow = (selectedModal, index = 0) => {
    let temp = {};
    const { universityDescription, portfolios } = this.state;
    if (universityDescription.get('description', '') && selectedModal === 'aboutUniversity') {
      const contentState = convertFromRaw(JSON.parse(universityDescription.get('description', '')));
      temp = {
        // editorState: EditorState.createWithText(universityDescription.get('description', '')),
        editorState: EditorState.createWithContent(contentState),
      };
    }
    if (index !== 0 && selectedModal === 'portfolio') {
      const contentState = convertFromRaw(
        JSON.parse(portfolios.getIn([index - 1, 'description'], ''))
      );

      temp = {
        portfolioName: portfolios.getIn([index - 1, 'title'], '') /* portfolios[index - 1].name */,
        // editorState: EditorState.createWithText(
        //   portfolios.getIn([index - 1, 'description'], '')
        // )  /* (portfolios[index - 1].description) */,
        editorState: EditorState.createWithContent(contentState),
        portfolioIndex: index,
      };
    }
    if (selectedModal === 'editDetails') {
      temp = {
        type: this.props.institution.get('type', ''),
      };
    }
    this.setState({ show: { ...this.state.show, [selectedModal]: true }, arrow: '', ...temp });
  };

  handleDeleteShow = (value, index, deleteItem) => {
    this.setState({ deleteShow: value, index: index, deleteItem: deleteItem });
  };
  handleDelete = () => {
    const { postUniversityDetail } = this.props;
    const { index, deleteItem } = this.state;
    if (deleteItem === 'portfolio') {
      const { portfolios } = this.state;
      postUniversityDetail(
        this.getUserID(),
        { portfolioId: portfolios.getIn([index - 1, '_id'], '') },
        `remove-portfolio`,
        'portFolio',
        'portfolio',
        'delete'
      );
      this.setState({ arrow: '', deleteShow: false });
      return;
    }
    if (deleteItem === 'aboutUniversity')
      postUniversityDetail(
        this.getUserID(),
        {},
        `remove-description`,
        'description',
        'instituteDescription',
        'delete'
      );
    this.setState({ arrow: '', deleteShow: false });
    return;
  };

  saveDescription = (description, media) => {
    const { universityDescription } = this.state;
    const { postUniversityDetail, setData } = this.props;
    const formData = new FormData();
    // formData.append('description', description);
    formData.append('description', JSON.stringify(convertToRaw(description)));
    media &&
      formData.append(
        'media',
        media === universityDescription.get('presignedMediaURL', '')
          ? universityDescription.get('mediaURL', '')
          : media
      );
    if (checkValidationEditor(description, setData)) {
      postUniversityDetail(
        this.getUserID(),
        formData,
        'institute-description',
        'description',
        'instituteDescription',
        'patch',
        this.handleClose('aboutUniversity')
      );
    }
  };
  savePortFolio = (title, description, media = null, index) => {
    const { postUniversityDetail, setData } = this.props;
    const { portfolios } = this.state;
    const formData = new FormData();
    // formData.append('description', description);
    formData.append('description', JSON.stringify(convertToRaw(description)));
    formData.append('title', title);
    media &&
      formData.append(
        'media',
        media === portfolios.getIn([index - 1, 'presignedMediaURL'], '')
          ? portfolios.getIn([index - 1, 'mediaURL'], '')
          : media
      );
    index && formData.append('portfolioId', portfolios.getIn([index - 1, '_id'], ''));

    if (checkValidationEditor(description, setData)) {
      postUniversityDetail(
        this.getUserID(),
        formData,
        `institute-${!index ? 'add' : 'edit'}-portfolio`,
        'portFolio',
        'portfolio',
        'patch',
        this.handleClose('portfolio')
      );
    }
  };
  fetchData = (data) => {
    const { institutionDetails } = this.state;
    const { setData, getInstitution } = this.props;

    if (checkValidation(data, institutionDetails.type, setData)) {
      const { history, addInstitution } = this.props;
      const formData = new FormData();
      for (const [key, value] of Object.entries(data)) {
        if (key === 'logo' && value === institutionDetails.logo) {
          formData.append(key, institutionDetails.logoURL);
          continue;
        } else if (key === 'accreditation') {
          value.forEach((item) => {
            item.accreditationType = item.accreditationType._id;
            delete item._id;
          });
          formData.append(key, JSON.stringify(value));
          continue;
        }
        formData.append(key, value);
      }
      formData.append('type', data.isUniversity ? 'University' : 'College');
      addInstitution({
        operation: 'update',
        _id: institutionDetails._id,
        history,
        formData,
        callBack: () => getInstitution(this.getUserID()),
      });
      this.setState({ show: { editDetails: false } });
      return;
    }
  };
  emptyValidation = (heading) => {
    const { universityDescription } = this.state;
    return heading === 'add_colleges.about_university' || heading === 'add_colleges.about_college'
      ? universityDescription?.size
      : true;
  };
  displayRow = (key, heading, show) => {
    const { arrow } = this.state;
    return (
      <div className="d-flex justify-content-between">
        <div className="d-flex">
          <div className="pt-2">
            <div className="f-16 digi-brown">
              {heading === 'add_colleges.about_university' ? (
                <img className="pr-2" src={school} alt="about university" />
              ) : (
                ''
              )}

              <b>
                <Trans i18nKey={heading}></Trans>
              </b>
            </div>
          </div>
        </div>

        <div className="d-flex justify-content-around text-center d-flex">
          <small className="f-18 mr-0 pr-0 mt-2 ">
            {this.emptyValidation(heading) ? (
              <Dropdown>
                <Dropdown.Toggle
                  variant=""
                  id="dropdown-table"
                  className="table-dropdown"
                  size="sm"
                >
                  <div className="">
                    <MoreVertIcon />
                  </div>
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item
                    className="d-flex"
                    href="#"
                    onClick={() => this.handleShow(show, key)}
                  >
                    <Trans i18nKey={'edit'}></Trans>
                  </Dropdown.Item>
                  <Dropdown.Item
                    href="#"
                    className="d-flex"
                    // onClick={() => this.handleDelete(key, show)}
                    onClick={() => this.handleDeleteShow(true, key, show)}
                  >
                    <Trans i18nKey={'delete'}></Trans>
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            ) : (
              ''
            )}
          </small>
          <div
            className="text-right pl-3"
            style={{ marginTop: '-7px', marginLeft: '20px' }}
            onClick={() => {
              this.setState({ arrow: { [heading]: !arrow[heading] } });
            }}
          >
            <Accordion.Toggle as={Card.Header} eventKey={key} className="icon_remove_leave pl-0">
              <span
                className=""
                onClick={() => {
                  this.setState({ arrow: { [heading]: !arrow[heading] } });
                }}
              >
                <i
                  className={`fa fa-chevron-${arrow[heading] ? 'up' : 'down'}`}
                  aria-hidden="true"
                ></i>
              </span>
            </Accordion.Toggle>
          </div>
        </div>
      </div>
    );
  };

  render() {
    const { institution, message, isLoading, breadcrumbs, setData } = this.props;
    const {
      show,
      portfolios,
      universityDescription,
      editorState,
      type,
      portfolioName,
      portfolioIndex,
      institutionDetails,
      cancelShow,
      deleteShow,
      deleteItem,
    } = this.state;

    let editorText = '';

    if (universityDescription.get('description', '')) {
      const contentState = convertFromRaw(JSON.parse(universityDescription.get('description', '')));
      const editorStateData = EditorState.createWithContent(contentState);
      editorText = editorStateData;
    }
    return (
      <>
        {message && <SnackBars show={true} message={message} />}
        <Loader isLoading={isLoading} />
        <Breadcrumb>
          <Link
            className="breadcrumb-icon"
            style={{ color: '#fff' }}
            key={breadcrumbs.getIn([0, 'to'], '')}
            to={breadcrumbs.getIn([0, 'to'], '')}
          >
            {breadcrumbs.getIn([0, 'label'], '')}
          </Link>
        </Breadcrumb>
        <InstitutionIndex />
        {institution.size !== 0 && (
          <ParentComponent>
            <div className="d-flex justify-content-between">
              <div className="d-flex">
                <div className="p-3">
                  <img
                    width="150px"
                    height="150px"
                    src={institution.get('presignedLogoURL', '')}
                    alt="logo"
                    className="digi-img-rounded"
                  />
                </div>
                <div className="">
                  <h3 className="mr-4 d-flex font-weight-normal mt-5 ml-4 mb-0 break-word">
                    {institution.get('name', '')}
                  </h3>
                  <h5 className="mr-4 d-flex font-weight-normal ml-4 text_wrap">
                    <Trans i18nKey={institution.get('isUniversity', true) ? 'uc' : 'cc'}></Trans>{' '}
                    {institution.get('code', '')}, <Trans i18nKey={'ac'}></Trans>{' '}
                    {institution?.get('accreditation', List())?.size > 0
                      ? institution
                          ?.get('accreditation', List())
                          .map((item) => item?.get('accreditationAgencyName', ''))
                          .join(', ')
                      : 'NA'}
                  </h5>
                </div>
              </div>
              <div className="">
                <div className="d-flex justify-content-end">
                  <div className="mb-0 mr-2">
                    <small className="f-18">
                      <Dropdown>
                        <Dropdown.Toggle
                          variant=""
                          id="dropdown-table"
                          className="table-dropdown"
                          size="sm"
                        >
                          <MoreVertIcon className="digi-gray-neutral" />
                          {/* <IconButton
                            edge="start"
                            color="inherit"
                            aria-label="menu"
                            id="light_gray"
                          >                          
                          </IconButton> */}
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                          <Dropdown.Item href="#" onClick={() => this.handleShow('editDetails')}>
                            <Trans i18nKey={'edit'}></Trans>
                          </Dropdown.Item>
                        </Dropdown.Menu>
                      </Dropdown>{' '}
                    </small>
                  </div>
                </div>
              </div>
            </div>

            <div className="clearfix"></div>

            <hr />
            <div>
              <div>
                <div className="d-flex pr-3 pb-3">
                  <img className="d-flex pl-3  pr-2 " src={manage_accounts} alt="manage_accounts" />
                  <b>
                    {' '}
                    <Trans i18nKey={'global_configuration.basic_details'}></Trans>
                  </b>
                </div>
                <div className="pl-3">
                  {BasicDetails('address', institution.getIn(['address', 'address'], ''))}
                  {BasicDetails(
                    'add_colleges.city',
                    institution.getIn(['address', 'city', 'name'], '')
                  )}
                  {BasicDetails(
                    'add_colleges.state',
                    institution.getIn(['address', 'state', 'name'], '')
                  )}
                  {BasicDetails(
                    'add_colleges.district',
                    institution.getIn(['address', 'district'], '')
                  )}
                  {BasicDetails(
                    'add_colleges.country',
                    institution.getIn(['address', 'country', 'name'], '')
                  )}
                  {BasicDetails(
                    'add_colleges.zip_code',
                    institution.getIn(['address', 'zipCode'], '')
                  )}
                  <hr />
                  <Accordion defaultActiveKey="" className="mt-3">
                    {this.displayRow(
                      '0',
                      `add_colleges.about_${
                        institution.get('isUniversity', false) ? 'university' : 'college'
                      }`,
                      'aboutUniversity'
                    )}
                    <Accordion.Collapse eventKey="0" className="bg-white">
                      <Card.Body className=" innerbodyLeave border-top-remove">
                        <div className="container ">
                          {!universityDescription?.size ? (
                            <div className="row pr-4 pt-3 digi-text-justify">
                              <div className="col-md-12 btn-description-border text-center p-5">
                                <button
                                  className="remove_hover btn btn-description mr-3"
                                  onClick={() => this.handleShow('aboutUniversity')}
                                >
                                  <Trans i18nKey={'add_colleges.add_description'}></Trans>
                                </button>
                              </div>
                            </div>
                          ) : (
                            <div className="row">
                              {universityDescription.get('presignedMediaURL', '') && (
                                <ShowMultiMedia uploadDetails={universityDescription} />
                              )}
                              <div
                                className={`${
                                  universityDescription.get('presignedMediaURL', '')
                                    ? 'col-md-6'
                                    : 'col-md-12'
                                }`}
                              >
                                <div className="editor-mt-15-n">
                                  <Editor
                                    editorState={editorText}
                                    readOnly={true}
                                    wrapperClassName="wrapperClassName"
                                    editorClassName="editorClassName"
                                    toolbarHidden
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </Card.Body>
                    </Accordion.Collapse>
                    <hr />
                    {portfolios.size !== 0 &&
                      portfolios.map((item, index) => {
                        const contentState = convertFromRaw(
                          JSON.parse(item.get('description', 'no description'))
                        );
                        const portfolioEditorState = EditorState.createWithContent(contentState);
                        return (
                          <React.Fragment key={index}>
                            {this.displayRow(`${index + 1}`, item.get('title', ''), 'portfolio')}
                            <Accordion.Collapse eventKey={`${index + 1}`} className="bg-white">
                              <Card.Body className="innerbodyLeave border-top-remove">
                                <div className="container p-0">
                                  <div className="row">
                                    {item.get('presignedMediaURL', '') && (
                                      <ShowMultiMedia uploadDetails={item} />
                                    )}
                                    <div
                                      className={`${
                                        item.get('presignedMediaURL', '') ? 'col-md-6' : 'col-md-12'
                                      }`}
                                    >
                                      <div className="editor-mt-15-n">
                                        <Editor
                                          editorState={portfolioEditorState}
                                          readOnly={true}
                                          wrapperClassName="wrapperClassName"
                                          editorClassName="editorClassName"
                                          toolbarHidden
                                        />
                                      </div>
                                      {/* <p>{item.get('description', 'no description')}</p> */}
                                    </div>
                                  </div>
                                </div>
                              </Card.Body>
                            </Accordion.Collapse>
                            <hr />
                          </React.Fragment>
                        );
                      })}

                    <p
                      className="text-blue remove_hover align-items-center bold d-flex"
                      onClick={() => this.handleShow('portfolio')}
                    >
                      <i className="fa fa-plus pr-2" aria-hidden="true"></i>
                      <Trans i18nKey={'add_colleges.add_portfolio'}></Trans>
                    </p>
                    <parentContext.universityContext.Provider value={{ ...institutionDetails }}>
                      {show['portfolio'] && (
                        <AddPortfolioModal
                          show={show['portfolio']}
                          handleClose={this.handleClose}
                          portfolioName={portfolioName}
                          portfolioIndex={portfolioIndex}
                          setData={setData}
                          portfolioDescription={editorState}
                          savePortFolio={this.savePortFolio}
                          previousImg={
                            portfolioIndex
                              ? portfolios.getIn([portfolioIndex - 1, 'presignedMediaURL'], '')
                              : ''
                          }
                          imgName={
                            portfolioIndex
                              ? portfolios.getIn([portfolioIndex - 1, 'mediaURL'], '')
                              : ''
                          }
                          cancelShow={cancelShow}
                        />
                      )}
                      {show['aboutUniversity'] && (
                        <EditAboutUniversityModal
                          show={show['aboutUniversity']}
                          handleClose={this.handleClose}
                          setData={setData}
                          universityDescription={editorState}
                          saveDescription={this.saveDescription}
                          title={
                            institution.get('isUniversity', false)
                              ? 'About University'
                              : 'About College'
                          }
                          name={universityDescription.get('mediaURL', '')}
                          previousImg={universityDescription.get('presignedMediaURL', '')}
                          cancelShow={cancelShow}
                        />
                      )}

                      {show['editDetails'] && (
                        <EditBasicDetails
                          show={show['editDetails']}
                          handleClose={this.handleClose}
                          defaultType={type}
                          setData={setData}
                          fetchData={this.fetchData}
                          institutionDetails={institutionDetails}
                          cancelShow={cancelShow}
                        />
                      )}
                      {cancelShow && (
                        <CancelModal
                          show={cancelShow}
                          type="textEditor"
                          handleClose={this.handleClose}
                        />
                      )}
                      {deleteShow && (
                        <Suspense fallback="">
                          <DeleteModal
                            show={deleteShow}
                            status={false}
                            setShow={this.handleDeleteShow}
                            handleDelete={this.handleDelete}
                            title="description"
                            deleteItem={deleteItem}
                          />
                        </Suspense>
                      )}
                    </parentContext.universityContext.Provider>
                  </Accordion>
                </div>
              </div>
            </div>
          </ParentComponent>
        )}
      </>
    );
  }
}

UniversityDetails.propTypes = {
  institution: PropTypes.instanceOf(Map),
  description: PropTypes.instanceOf(List),
  portfolio: PropTypes.instanceOf(List),
  history: PropTypes.instanceOf(Object),
  addInstitution: PropTypes.func,
  getInstitution: PropTypes.func,
  postUniversityDetail: PropTypes.func,
  setData: PropTypes.func,
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  loggedInUserData: PropTypes.instanceOf(Map),
  setBreadCrumb: PropTypes.func,
  breadcrumbs: PropTypes.oneOfType([PropTypes.array, PropTypes.object]),
};

const mapStateToProps = (state) => {
  return {
    institution: selectInstitution(state),
    description: selectDescription(state),
    portfolio: selectPortFolio(state),
    isLoading: selectIsLoading(state),
    breadcrumbs: selectBreadCrumb(state),
    message: selectMessage(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(UniversityDetails);
