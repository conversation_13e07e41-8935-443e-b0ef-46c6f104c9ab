import React, { useState, Suspense } from 'react';
import PropTypes from 'prop-types';
import DeleteModal from '../modal/DeleteModal';
import { connect } from 'react-redux';
import { selectMailIsLoading } from '_reduxapi/global_configuration/selectors';
import { AccordionProgramInput } from './ReusableComponent';
import { t } from 'i18next';
import { Map } from 'immutable';
import { Trans } from 'react-i18next';
import * as actions from '_reduxapi/global_configuration/actions';

const EmailIdAddEditPopUp = React.lazy(() => import('./EmailIdAddEditPopUp'));
const EmailIdConFiguration = (props) => {
  const {
    openSelectedAccord,
    accordOpen,
    emailIdConFiguration,
    settingId,
    header,
    emailIdConfig,
    mailIsLoading,
  } = props;

  const id = emailIdConfig.get('_id', '');
  const size = emailIdConfig.size && 1;
  const [open, setOpen] = useState(false);

  const handleOpen = (name) => {
    let popup = {
      ...open,
      [name]: !open[`${name}`],
    };
    setOpen(popup);
  };

  const handleClose = (UpdateResponseData) => {
    UpdateResponseData();
    setOpen(false);
  };
  const handleDeleteClose = () => {
    setOpen(false);
  };
  const displayHeading = () => {
    return (
      <div className="f-16 digi-brown remove_hover">
        <b>
          <Trans i18nKey={'email_id_config'}></Trans>
        </b>
        {!accordOpen[7] && (
          <p>
            <span>{`${size} ${t('configured')}`}</span>
          </p>
        )}
      </div>
    );
  };
  const displayBody = () => (
    <div className="container pl-4">
      <div className="mb-2">
        {emailIdConfig.size ? (
          <>
            <>
              <div className="bold mb-2 d-flex">{emailIdConfig.get('fromEmail', '')}</div>
              <div className="float-right d-flex mt--30">
                <div
                  className="text-blue ml-auto remove_hover"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpen('emailEdit', 'edit');
                  }}
                >
                  <Trans i18nKey={'edit'}></Trans>
                </div>
                <div
                  className="pl-3 text-blue pl-3 remove_hover"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpen('emailDelete', 'delete');
                  }}
                >
                  <Trans i18nKey={'delete'}></Trans>
                </div>
              </div>
              <br />
            </>
          </>
        ) : (
          <>
            <div>
              <div className="bold mb-2">
                <Trans i18nKey={'emailId'}></Trans>
              </div>
            </div>
            <div
              className="float-right text-blue ml-auto remove_hover mt--30"
              onClick={(e) => {
                e.stopPropagation();
                handleOpen('emailCreate', 'create');
              }}
            >
              <Trans i18nKey={'dashboard_view.configure'}></Trans>
            </div>
          </>
        )}
      </div>
    </div>
  );
  return (
    <>
      <AccordionProgramInput
        summaryChildren={displayHeading()}
        detailChildren={displayBody()}
        onClick={() => openSelectedAccord(7)}
        expanded={accordOpen[7] || false}
      />
      <hr />
      <Suspense fallback="">
        <EmailIdAddEditPopUp
          emailIdConfig={emailIdConfig}
          handleClose={handleClose}
          mailIsLoading={mailIsLoading}
          settingId={settingId}
          open={open}
          emailIdConFiguration={emailIdConFiguration}
          id={id}
          header={header}
        />
      </Suspense>
      {open['emailDelete'] && (
        <DeleteModal
          emailIdConFiguration={emailIdConFiguration}
          type="email"
          settingId={settingId}
          handleClose={handleDeleteClose}
          institutionHeader={header}
          _id={id}
          open={open}
        />
      )}
    </>
  );
};

const mapStateToProps = (state) => {
  return {
    mailIsLoading: selectMailIsLoading(state),
  };
};

EmailIdConFiguration.propTypes = {
  accordOpen: PropTypes.object,
  openSelectedAccord: PropTypes.func,
  emailIdConFiguration: PropTypes.func,
  settingId: PropTypes.string,
  setData: PropTypes.func,
  header: PropTypes.object,
  emailIdConfig: PropTypes.instanceOf(Map),
  mailIsLoading: PropTypes.object,
};

export default connect(mapStateToProps, actions)(EmailIdConFiguration);
