import React, {
  forwardRef,
  lazy,
  Suspense,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Checkbox,
  Divider,
  Drawer,
  FormControlLabel,
  IconButton,
  MenuItem,
  Pagination,
  Select,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import CustomSelect, { MuiSelect, muiSelectSx } from './CustomSelect';
import { fromJS, List, Map, Set } from 'immutable';
import CustomMultiSelect, { StaffSelect, WeekSelect } from './CustomMultiSelect';
import {
  buttonSx,
  checkboxSx,
  CustomTooltip,
  filterScheduled,
  filterValidOptions,
  getDateAndTimeString,
  getFirstValue,
  getOccurrenceDateRange,
  getSlotTimeObject,
  getWeekDateRange,
  limitOptions,
  MuiDatePicker,
  MuiMobileTimePicker,
  MuiTimePicker,
  paginationSx,
  postScheduleKeys,
  PreviewDetailRow,
  ScheduledInfoTooltip,
  ScheduleLabel,
  ScheduleStepper,
  SearchInput,
  SecondaryText,
  SelectedStaffs,
  weeks,
} from './utils';
import {
  capitalize,
  formatToTwoDigitString,
  getModes,
  getTranslatedDuration,
  getURLParams,
  getVersionName,
  indVerRename,
  isIndGroup,
  levelRename,
  studentGroupRename,
} from 'utils';
import { useDispatch, useSelector } from 'react-redux';
import {
  getAvailableList,
  getCourseDeliveryTypes,
  getCourseTopics,
  postMultiSchedule,
  postScheduleAnalyze,
  postSingleSchedule,
  resetMessage,
  setData,
} from '_reduxapi/course_scheduling/action';
import {
  selectAvailableList,
  selectCourseDeliveryTypes,
  selectCourseTopics,
  selectScheduleAnalysis,
} from '_reduxapi/course_scheduling/selectors';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { addMinutes, differenceInMinutes } from 'date-fns';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import AddIcon from '@mui/icons-material/Add';
import InfoIcon from '@mui/icons-material/Info';
import ErrorIcon from '@mui/icons-material/Error';
import SearchIcon from '@mui/icons-material/Search';
import TableChartIcon from '@mui/icons-material/TableChart';
import TableChartOutlinedIcon from '@mui/icons-material/TableChartOutlined';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import EditIcon from '@mui/icons-material/Edit';
import { getCourseDuration, getFormattedGroupName, getWeeks } from '../utils';
import { formattedFullName } from 'Modules/ReportsAndAnalytics/utils';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import moment from 'moment';

const ScheduleIssuesModal = lazy(() => import('./Modals/ScheduleIssuesModal'));
const ScheduleConfirmationModal = lazy(() => import('./Modals/ScheduleConfirmationModal'));
const EditTopicModal = lazy(() => import('./Modals/EditTopicModal'));

const drawerSx = {
  '& .MuiDrawer-paper': { width: '80%', maxWidth: '1000px' },
};
const headerSx = {
  padding: '10px 24px',
  backgroundColor: '#F9FAFB',
  borderBottom: '1px solid #0064C8',
};
const drawerContentSx = {
  padding: '16px 24px',
  color: '#374151',
  border: '1px solid #E5E7EB',
  borderRight: 0,
  height: 'calc(100vh - 130px)',
  overflow: 'auto',
};
const mandatoryTextSx = {
  marginBottom: '16px',
  fontSize: '14px',
  '& span': {
    color: '#DC2626',
  },
};
const infoText = {
  fontSize: '14px',
  color: '#6B7280',
};
const footerSx = {
  display: 'flex',
  justifyContent: 'end',
  padding: '12px 16px',
  backgroundColor: '#F9FAFB',
};
const customRowSx = {
  margin: '0 -8px',
};
const customColLargeSx = {
  padding: '0 8px',
  '@media (min-width:992px)': {
    flex: '0 0 44%',
    maxWidth: '44%',
  },
};
const customColSmallSx = {
  padding: '0 8px',
  '@media (min-width:992px)': {
    flex: '0 0 28%',
    maxWidth: '28%',
  },
};
const toggleGroupSx = {
  padding: '4px',
  borderRadius: '8px',
  backgroundColor: '#F3F4F6',
};
const toggleButtonSx = {
  padding: '4px 16px',
  border: 0,
  textTransform: 'none',
  borderRadius: '8px !important',
  color: '#6B7280',
  fontWeight: 'normal',
  lineHeight: '24px',
  '&.Mui-selected': {
    color: '#374151',
    backgroundColor: '#fff',
    fontWeight: 500,
    boxShadow: '0px 1px 3px 0px #11182733',
    '&:hover': {
      backgroundColor: '#fff',
    },
  },
  '&.Mui-disabled': {
    color: '#6B7280',
    border: 0,
    opacity: 0.5,
  },
};
const weekToggleButtonSx = {
  padding: '8px 24px',
  border: '1px solid #D1D5DB !important',
  textTransform: 'none',
  borderRadius: '4px !important',
  color: '#374151',
  fontSize: 12,
  fontWeight: 'normal',
  lineHeight: 'normal',
  '&.Mui-selected': {
    color: '#fff',
    backgroundColor: '#0064C8',
    borderColor: '#0064C8 !important',
    '&:hover': {
      backgroundColor: '#0064C8',
    },
  },
  '&:not(:last-of-type)': {
    marginRight: '8px',
  },
};
const slotBoxSx = {
  padding: '2px 8px',
  textAlign: 'center',
  borderRadius: '8px',
  backgroundColor: '#F3F4F6',
  width: 40,
  height: 40,
};
const issueDetailsSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: '8px',
  padding: '4px 16px',
  borderRadius: '8px',
  border: '1px solid #FEF3C7',
  color: '#D97706',
  backgroundColor: '#FFFBEB',
};
const filterIconSx = {
  padding: '7px',
  border: '1px solid #D1D5DB',
  borderRadius: '8px',
  color: '#374151',
};
const previewBtnSx = {
  border: '1px solid #D1D5DB',
  borderRadius: '8px',
  height: 40,
};
const previewBtnActiveSx = {
  backgroundColor: '#E5E7EB',
  borderColor: '#E5E7EB',
};
const previewCloseIconSx = {
  marginLeft: '4px',
  marginBottom: '2px',
};
const viewToggleButtonSx = {
  padding: '4px 8px',
  border: 0,
  borderRadius: '8px !important',
  color: '#6B7280',
  '&.Mui-selected': {
    color: '#374151',
    backgroundColor: '#fff',
    boxShadow: '0px 1px 3px 0px #11182733',
    '&:hover': {
      backgroundColor: '#fff',
    },
  },
  '&:not(:last-of-type)': {
    marginRight: '8px',
  },
};
const statusBoxSx = {
  display: 'flex',
  alignItems: 'center',
  gap: 0.5,
  '&.scheduled': {
    color: '#D97706',
  },
  '&.groupSliced': {
    color: 'rgba(220, 38, 38, 0.85)',
  },
};
const tableFooterSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '8px',
  border: '1px solid #E5E7EB',
  borderTop: 0,
  borderRadius: '0 0 8px 8px',
};
const previewBoxSx = {
  my: '8px',
  padding: '8px 16px',
  borderRadius: '8px',
  border: '1px solid #D1D5DB',
};

const courseDetailsKeys = ['course', 'deliveryType', 'topic', 'subjects'];
const scheduleDetailsKeys = ['sessionDate', 'startTime', 'endTime'];
const conductionDetailsKeys = [
  'courseGroups',
  'deliveryGroups',
  'mode',
  'infrastructure',
  'staffs',
];

function getInfraDetail(infra, availableList) {
  const filteredInfra = availableList
    .get('scheduleInfra', List())
    .find((st) => st.get('_id') === infra);
  return filteredInfra !== undefined ? filteredInfra : Map();
}

function getInfrastructures({
  scheduleState,
  courseGroupOptions,
  courseGroupIds,
  availableList,
  selectedTerm,
  selectedLevel,
}) {
  const mode = scheduleState.get('mode', '');
  if (!mode) return List();
  const genders = Set(
    courseGroupOptions
      .filter((studentGroup) => courseGroupIds.includes(studentGroup.get('value')))
      .map((studentGroup) => studentGroup.get('gender'))
      .filter((g) => g)
  ).toList();
  if (genders.isEmpty()) return List();
  const gender = genders.get(0, '').toLowerCase();
  if (mode === 'remote') {
    return availableList
      .get('remote_scheduling', List())
      .filter((room) => {
        if (
          // room.get('yearName') === course.get('year_no') &&
          room.get('term', '').toLowerCase() === selectedTerm.toLowerCase() &&
          room.get('levelName') === selectedLevel
        ) {
          return (
            room.get('gender', '').toLowerCase() === gender || room.get('gender', '') === 'both'
          );
        }
        return false;
      })
      .map((room) =>
        room.merge(
          Map({
            name: room.get('meetingTitle'),
            value: room.get('_id'),
            // secondaryTitle1: `${capitalize(room.get('gender', ''))}${
            //   room.get('meetingUsername', '') !== null
            //     ? `, ${room.get('meetingUsername', '')}`
            //     : ''
            // }`,
            secondaryText: `${
              room.get('remotePlatform', '') === 'teams'
                ? 'Microsoft Teams'
                : `${room.get('meetingId', '')}, Zoom`
            }`,
          })
        )
      )
      .sort((r1, r2) => {
        const v1 = r1.get('meetingTitle', '');
        const v2 = r2.get('meetingTitle', '');
        return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
      });
  } else if (mode === 'onsite') {
    const selectedSubjects = scheduleState.get('subjects', List());
    if (selectedSubjects.isEmpty()) return List();
    const infrastructures = availableList
      .get('subjects', List())
      .filter((item) => selectedSubjects.includes(item.get('_subject_id')))
      .flatMap((item) => item.get('subject_infra', List()))
      .toSet()
      .toList();

    return infrastructures
      .map((infra) => {
        const filteredInfra = getInfraDetail(infra, availableList);
        return Map({
          name: filteredInfra.get('name', ''),
          value: filteredInfra.get('_id'),
          scheduled: !filteredInfra.get('availability', false),
          errorInfo: filteredInfra.get('conflict', ''),
        });
      })
      .sort((i1, i2) => {
        const v1 = i1.get('name', '');
        const v2 = i2.get('name', '');
        return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
      });
  }
  return List();
}

function getStaffDetail(staff, availableList) {
  const filteredStaff = availableList
    .get('scheduleStaffs', List())
    .find((st) => st.get('_id') === staff);
  return filteredStaff !== undefined ? filteredStaff : Map();
}

function getStaffs({ scheduleState, availableList }) {
  const selectedSubjects = scheduleState.get('subjects', List());
  if (!selectedSubjects.size) return List();
  return availableList
    .get('subjects', List())
    .filter((item) => selectedSubjects.includes(item.get('_subject_id')))
    .reduce((acc, subject) => {
      const fStaff = Map();
      return acc.concat(
        subject.get('staffs', List()).map((staff) => {
          const filteredStaff = getStaffDetail(staff, availableList);
          const userId = filteredStaff.get('user_id', '');
          const gender = filteredStaff.get('gender', '');
          return fStaff.merge(
            Map({
              name: formattedFullName(filteredStaff.get('name', Map()).toJS())
                .replace(/\s+/g, ' ')
                .trim(),
              value: filteredStaff.get('_id'),
              secondaryText: `${userId} • ${gender}`,
              userId,
              gender,
              scheduled: !filteredStaff.get('availability', false),
              errorInfo: filteredStaff.get('conflict', ''),
              primary: true,
            })
          );
        })
      );
    }, List())
    .reduce((acc, staff) => acc.set(staff.get('value'), staff), Map())
    .valueSeq()
    .toList();
}

export const getSelectedLevelData = (levelData, filters) => {
  const selectedTerm = filters.get('term', '');
  const selectedYear = filters.get('year', '');
  const selectedLevel = filters.get('level', '');
  const key = [selectedTerm, selectedYear, selectedLevel].join('+');
  return levelData.get(key, Map());
};

const clearSelectedValues = (state, key) => {
  let keysToDelete = [];
  if (scheduleDetailsKeys.includes(key)) keysToDelete = conductionDetailsKeys;
  else {
    const stateKeys = [...courseDetailsKeys, ...conductionDetailsKeys];
    const keyIndex = stateKeys.indexOf(key);
    if (keyIndex === -1) return state;

    keysToDelete = stateKeys.slice(keyIndex + 1);
  }

  state.keySeq().forEach((stateKey) => {
    if (keysToDelete.includes(stateKey)) state = state.delete(stateKey);
  });
  return state;
};

const constructSubjects = (subjectList, selectedSubjects, isMultiple = false) => {
  const keys = isMultiple ? postScheduleKeys['multiple'] : postScheduleKeys['single'];
  return subjectList
    .filter((sub) => selectedSubjects.includes(sub.get('value')))
    .map((sub) => Map({ [keys.subjectId]: sub.get('value'), [keys.subjectName]: sub.get('name') }));
};

const getInfraName = (infraList, infraId) => {
  return infraList.find((infra) => infraId === infra.get('value'))?.get('name');
};

const constructOccurrences = (slots) => {
  return slots.flatMap((slot) => {
    const start = getSlotTimeObject(slot.get('startTime'));
    const end = getSlotTimeObject(slot.get('endTime'));
    return slot.get('occurOn', List()).map((day) => Map({ day, start, end }));
  });
};

const constructStudentGroups = (studentGroups, deliveryGroupIds, isMultiple = false) => {
  const keys = isMultiple ? postScheduleKeys['multiple'] : postScheduleKeys['single'];
  return studentGroups
    .map((group) => {
      const sessionGroups = group
        .get('session_group', List())
        .filter((session) => deliveryGroupIds.includes(session.get('_id')))
        .map((session) => {
          return Map({
            [keys.sessionGroupId]: session.get('_id'),
            [keys.sessionGroupNo]: session.get('group_no'),
            [keys.sessionGroupName]: session.get('group_name'),
          });
        });
      if (!sessionGroups.size) return;

      return Map({
        [keys.studentGroupId]: group.get('_id'),
        gender: group.get('gender'),
        [keys.studentGroupNo]: group.get('group_no'),
        [keys.studentGroupName]: group.get('group_name'),
        [keys.sessionGroup]: sessionGroups,
        ...(isMultiple
          ? { delivery_symbol: group.get('delivery_symbol') }
          : {
              // mode: 'foundation'
            }),
      });
    })
    .filter((item) => item);
};

const getDeliveryTypeDetails = (type) => {
  const [deliverySymbol, deliveryType] = type.split('+');
  return { deliverySymbol, deliveryType };
};

const getDeliveryId = (deliveryTypeList, selectedType) => {
  return deliveryTypeList.find((item) => item.get('value') === selectedType)?.get('deliveryId');
};

const getFormattedTime = (scheduleTime) => {
  const minute = formatToTwoDigitString(scheduleTime.get('minute', 0));
  return `${scheduleTime.get('hour', 0)}:${minute}${scheduleTime.get('format', '')}`;
};

const constructConflicts = (schedule) => {
  return schedule
    .get('conflict', List())
    .reduce((acc, item) => acc.set(item.get('field'), item), Map());
};

const constructPreviewDetails = ({
  scheduleState,
  requestBody,
  courseOptions,
  courseGroupOptions,
  deliveryGroupOptions,
  staffOptions,
}) => {
  const programId = requestBody.get('programId', '');
  const course = courseOptions
    .find((course) => course.get('value') === scheduleState.get('course', ''))
    ?.get('name', '');
  const { deliverySymbol, deliveryType } = getDeliveryTypeDetails(
    scheduleState.get('deliveryType', '')
  );
  const subjects = requestBody
    .get('subjects', List())
    .map((sub) => sub.get('subject_name', ''))
    .join(', ');
  const formattedStartDate = moment(requestBody.get('occurrenceStartDate', '')).format(
    'DD / MM / yyyy'
  );
  const formattedEndDate = moment(requestBody.get('occurrenceEndDate', '')).format(
    'DD / MM / yyyy'
  );
  const dateRange = `${formattedStartDate} to ${formattedEndDate}`;
  const sessionSlots = scheduleState
    .get('slots', List())
    .map(
      (slot) =>
        `${moment(slot.get('startTime', '')).format('h:mma')} - ${moment(
          slot.get('endTime', '')
        ).format('h:mma')}`
    )
    .join(', ');
  const selectedCourseGroups = scheduleState.get('courseGroups', List());
  const courseGroups = courseGroupOptions
    .filter((group) => selectedCourseGroups.includes(group.get('value')))
    .map((group) => group.get('name', ''))
    .join(', ');
  const deliveryGroups = deliveryGroupOptions
    .entrySeq()
    .reduce((acc, [id, groups]) => {
      if (!selectedCourseGroups.includes(id)) return acc;
      return acc.concat(groups);
    }, List())
    .filter((group) => scheduleState.get('deliveryGroups', List()).includes(group.get('value')))
    .map((group) => group.get('name', ''))
    .join(', ');
  const mode = indVerRename(capitalize(requestBody.get('mode', '')), programId);
  const infra = requestBody.get('infraName', '');
  const staffs = staffOptions
    .filter((staff) => scheduleState.get('staffs', List()).includes(staff.get('value')))
    .map((staff) => staff.get('name', ''))
    .join(', ');

  return Map({
    course,
    deliveryType: `${deliverySymbol} - ${deliveryType}`,
    subjects,
    dateRange,
    sessionSlots,
    courseGroups,
    deliveryGroups,
    mode,
    infra,
    staffs,
  });
};

const checkStudentConflict = (conflicts) => {
  let groupMsg = '';
  let studentMsg = '';
  if (conflicts.has('course_group')) groupMsg = conflicts.getIn(['course_group', 'message']);
  else if (conflicts.has('session_group')) groupMsg = conflicts.getIn(['session_group', 'message']);
  if (conflicts.has('student')) studentMsg = conflicts.getIn(['student', 'message']);

  return { hasStudentConflict: !!(groupMsg || studentMsg), groupMsg, studentMsg };
};

const getDeliveryGroups = (deliveryGroupOptions, courseGroups, scheduledIds = List()) => {
  return courseGroups.flatMap((id) => {
    return scheduledIds.includes(id) ? List() : deliveryGroupOptions.get(id, List());
  });
};

const getSessionStudents = (session) => {
  const groupName = session.get('name', '');
  return session.get('studentIds', List()).map((s) => s.set('groupName', groupName));
};

const constructGroupMap = (group, students) => {
  return Map({ name: group.get('name', ''), errorInfo: group.get('errorInfo', ''), students });
};

const checkScheduleConflicts = ({
  scheduleState,
  courseGroupOptions,
  deliveryGroupOptions,
  infraOptions,
  staffOptions,
}) => {
  const courseGroups = scheduleState.get('courseGroups', List());
  const deliveryGroups = scheduleState.get('deliveryGroups', List());
  const infrastructure = scheduleState.get('infrastructure', '');
  const staffs = scheduleState.get('staffs', List());

  const scheduledCourseGroups = filterScheduled(courseGroupOptions, courseGroups);
  const scheduledIds = scheduledCourseGroups.map((g) => g.get('value'));
  const deliveryGroupsList = getDeliveryGroups(deliveryGroupOptions, courseGroups, scheduledIds);
  const scheduledDeliveryGroups = filterScheduled(deliveryGroupsList, deliveryGroups);
  const scheduledInfra = filterScheduled(infraOptions, infrastructure);
  const scheduledStaffs = filterScheduled(staffOptions, staffs);

  const groupList = scheduledCourseGroups.map((group) => {
    const id = group.get('value');
    const sessions = deliveryGroupOptions.get(id, List());
    const students = sessions.flatMap((session) => getSessionStudents(session));
    return constructGroupMap(group, students);
  });
  const sessionList = scheduledDeliveryGroups.map((group) => {
    return constructGroupMap(group, getSessionStudents(group));
  });

  const studentList = groupList.concat(sessionList);
  if (studentList.size || scheduledStaffs.size || scheduledInfra.size)
    return Map({ studentList, staffList: scheduledStaffs, infraList: scheduledInfra });

  return Map();
};

const updateCount = (acc, key) => acc.update(key, 0, (prev) => prev + 1);

const getAssignedCounts = (scheduleList) => {
  const counts = scheduleList.reduce((acc, item) => {
    const conflictsMap = constructConflicts(item);

    if (conflictsMap.has('course_group')) acc = updateCount(acc, 'groups');
    else if (conflictsMap.has('session_group')) acc = updateCount(acc, 'groups');

    if (conflictsMap.has('student')) acc = updateCount(acc, 'students');
    if (conflictsMap.has('staff')) acc = updateCount(acc, 'staffs');
    if (conflictsMap.has('infra')) acc = updateCount(acc, 'infra');

    return acc;
  }, Map());

  let assigned = [];
  if (counts.get('groups', 0)) assigned.push(`(${counts.get('groups', 0)}) groups`);
  if (counts.get('students', 0)) assigned.push(`(${counts.get('students', 0)}) students`);
  if (counts.get('staffs', 0)) assigned.push(`(${counts.get('staffs', 0)}) staff`);
  if (counts.get('infra', 0)) assigned.push(`(${counts.get('infra', 0)}) rooms`);

  if (!assigned.length) return '';
  if (assigned.length === 1) return assigned[0];

  const last = assigned.pop();
  return `${assigned.join(', ')} & ${last}`;
};

const useGetReduxData = ([selectorFunc, fetchFunc]) => {
  const dispatch = useDispatch();
  const fetchedData = useSelector(selectorFunc);

  const callFetchFunc = (data) => dispatch(fetchFunc(data));

  return [fetchedData, callFetchFunc];
};

const CourseDetails = ({
  selectedCourse,
  courseOptions,
  deliveryTypeOptions,
  topicOptions,
  subjectList,
  scheduleState,
  handleChange,
  isMultipleSession,
  setFirstOption,
}) => {
  const courseId = scheduleState.get('course', '');
  const deliveryType = scheduleState.get('deliveryType', '');
  const deliveryTopic = scheduleState.get('topic', '');

  useEffect(() => {
    setFirstOption('course', courseOptions);
  }, [courseOptions]);

  useEffect(() => {
    if (courseId) setFirstOption('deliveryType', deliveryTypeOptions);
  }, [courseId, deliveryTypeOptions]);

  useEffect(() => {
    if (!isMultipleSession && deliveryType) setFirstOption('topic', topicOptions);
  }, [isMultipleSession, deliveryType, topicOptions]);

  useEffect(() => {
    setFirstOption('subjects', subjectList, true);
  }, [subjectList]);

  return (
    <>
      <p className="bold gray-neutral">Course Details</p>
      <Typography sx={infoText}>Select course, delivery and subject details</Typography>

      <Box className="row mt-2" {...(isMultipleSession && { sx: customRowSx })}>
        <Box className="col-md-6 mb-2" {...(isMultipleSession && { sx: customColLargeSx })}>
          <CustomSelect
            label="Course"
            options={courseOptions}
            value={courseId}
            onChange={handleChange('course')}
            searchPlaceholder="Search course"
            toggleBtnText="Show courses without groups"
            emptyErrorMessage="No courses found"
            showScheduledStatus
          />

          {courseId && (
            <Typography sx={infoText}>
              {/* Credit Hours: 4 (3 + 1) | {getTranslatedDuration(getCourseDuration(selectedCourse))} */}
              Date Range: {getTranslatedDuration(getCourseDuration(selectedCourse))}
            </Typography>
          )}
        </Box>
        <Box className="col-md-6 mb-2" {...(isMultipleSession && { sx: customColSmallSx })}>
          <CustomSelect
            label="Delivery type"
            options={deliveryTypeOptions}
            value={deliveryType}
            onChange={handleChange('deliveryType')}
            searchPlaceholder="Search del. type"
            toggleBtnText="Show scheduled types"
            emptyErrorMessage="No delivery types found"
            disabled={!courseId}
          />

          {isMultipleSession && deliveryType && (
            <Typography fontSize={14}>All topics involved</Typography>
          )}
        </Box>
        {!isMultipleSession && (
          <Box className="col-md-6 mb-2">
            <CustomSelect
              label="Topic"
              options={topicOptions}
              value={deliveryTopic}
              onChange={handleChange('topic')}
              searchPlaceholder="Search topic"
              toggleBtnText="Show scheduled topics"
              emptyErrorMessage="No topics found"
              disabled={!deliveryType}
              showDuration
            />
          </Box>
        )}
        <Box className="col-md-6 mb-2" {...(isMultipleSession && { sx: customColSmallSx })}>
          <CustomMultiSelect
            label="Subject"
            options={subjectList}
            value={scheduleState.get('subjects', List())}
            onChange={handleChange('subjects')}
            searchPlaceholder="Search subject"
            toggleBtnText="Show scheduled subjects"
            emptyErrorMessage="No subjects found"
            disabled={isMultipleSession ? !deliveryType : !deliveryTopic}
          />
        </Box>
      </Box>
    </>
  );
};
CourseDetails.propTypes = {
  selectedCourse: PropTypes.instanceOf(Map),
  courseOptions: PropTypes.instanceOf(List),
  deliveryTypeOptions: PropTypes.instanceOf(List),
  topicOptions: PropTypes.instanceOf(List),
  subjectList: PropTypes.instanceOf(List),
  scheduleState: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  isMultipleSession: PropTypes.bool,
  setFirstOption: PropTypes.func,
};

const ScheduleDetails = ({ selectedCourse, scheduleState, handleChange }) => {
  const subjectIds = scheduleState.get('subjects', List()).size;
  const startDate = new Date(selectedCourse.get('start_date', new Date()));
  const endDate = new Date(selectedCourse.get('end_date', new Date()));
  const startTime = scheduleState.get('startTime', '');
  const endTime = scheduleState.get('endTime', '');

  const sessionDuration = useMemo(() => {
    if (!startTime || !endTime) return 0;
    return differenceInMinutes(endTime, startTime);
  }, [startTime, endTime]);

  return (
    <>
      <Typography sx={infoText}>Set date and session time</Typography>

      {/* {courseId && (
        <Typography my="8px" fontSize={14}>
          <Box component="span" color="#6B7280">
            Course Date Range:
          </Box>{' '}
          {format(startDate, 'MMM dd yyyy')} - {format(endDate, 'MMM dd yyyy')}
        </Typography>
      )} */}

      <Box display="flex" gap={2} mt="8px">
        <Box width={190} mb={1}>
          <ScheduleLabel label="Session date" />
          <MuiDatePicker
            value={scheduleState.get('sessionDate', '')}
            onChange={handleChange('sessionDate')}
            disabled={!subjectIds}
            minDate={startDate}
            maxDate={endDate}
          />
        </Box>

        <div className="d-flex align-items-end text-NewLightgray mb-2">
          <Box width={165}>
            <ScheduleLabel label="Start time" />
            <MuiTimePicker
              value={startTime}
              onChange={handleChange('startTime')}
              disabled={!subjectIds || !scheduleState.get('sessionDate')}
            />
          </Box>

          <span className="mx-2 mb-2">to</span>
          <Box width={165}>
            <ScheduleLabel label="End time" />
            <MuiTimePicker
              value={endTime}
              onChange={handleChange('endTime')}
              disabled={!subjectIds || !startTime}
            />
          </Box>

          {startTime && endTime && <span className="ml-3 mb-2">{sessionDuration} minutes</span>}
        </div>
      </Box>
    </>
  );
};
ScheduleDetails.propTypes = {
  selectedCourse: PropTypes.instanceOf(Map),
  scheduleState: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
};

const MultipleScheduleDetails = ({ selectedCourse, scheduleState, handleChange }) => {
  const courseStartDate = new Date(selectedCourse.get('start_date', new Date()));
  const courseEndDate = new Date(selectedCourse.get('end_date', new Date()));
  const rangeMethod = scheduleState.get('rangeMethod', '');
  const startDate = scheduleState.get('startDate', '');
  const endDate = scheduleState.get('endDate', '');
  const startWeek = scheduleState.get('startWeek', '');
  const endWeek = scheduleState.get('endWeek', '');
  const occurrence = scheduleState.get('occurrence', List());
  const slots = scheduleState.get('slots', fromJS([{}]));

  const { startWeekOptions, weekMap } = useMemo(() => {
    let weekMap = Map();
    const noOfWeeks = getWeeks(courseStartDate, courseEndDate);
    const startWeekOptions = List([...Array(noOfWeeks)]).map((_, i) => {
      const weekNo = i + 1;
      const { start, end } = getWeekDateRange(courseStartDate, courseEndDate, weekNo);
      weekMap = weekMap.set(weekNo, Map({ start, end }));
      return Map({ name: `Week ${weekNo} (${start} - ${end})`, value: weekNo });
    });

    return { startWeekOptions, weekMap };
  }, [courseStartDate, courseEndDate]);

  const endWeekOptions = useMemo(() => {
    if (!startWeek) return List();
    return startWeekOptions.filter((week) => week.get('value') >= startWeek);
  }, [startWeekOptions, startWeek]);

  const weekdays = useMemo(() => {
    return weeks.filter((week) => occurrence.includes(week.get('value')));
  }, [occurrence]);

  const getWeekDate = (key, weekNo) => {
    if (!weekNo) return '';
    return weekMap.getIn([weekNo, key], '');
  };

  const isOccurrenceDisabled = () => {
    return (
      !rangeMethod ||
      (rangeMethod === 'byDate' && !endDate) ||
      (rangeMethod === 'byWeek' && !endWeek)
    );
  };

  const handleChangeSlot = (index, key) => (val) => {
    const updatedSlots = slots.setIn([index, key], val);
    handleChange('slots')(updatedSlots);
  };

  const handleDeleteSlot = (index) => {
    const updatedSlots = slots.delete(index);
    handleChange('slots')(updatedSlots);
  };

  const handleAddSlot = () => {
    const updatedSlots = slots.push(Map());
    handleChange('slots')(updatedSlots);
  };

  return (
    <>
      <Typography sx={infoText}>Set date range and session timings</Typography>

      <Box display="flex" alignItems="end" gap={2} mt="8px">
        <div className="mb-2">
          <ScheduleLabel label="Range method" />
          <ToggleButtonGroup
            value={rangeMethod}
            onChange={handleChange('rangeMethod')}
            aria-label="range-method"
            sx={toggleGroupSx}
            exclusive
            disabled={false}
          >
            <ToggleButton value="byDate" sx={toggleButtonSx}>
              By Date
            </ToggleButton>
            <ToggleButton value="byWeek" sx={toggleButtonSx}>
              By Week
            </ToggleButton>
          </ToggleButtonGroup>
        </div>

        {rangeMethod === 'byDate' && (
          <>
            <Box display="flex" alignItems="center" gap={0.5} mb={1}>
              <ScheduleLabel label="Start date:" />
              <Box width={190}>
                <MuiDatePicker
                  value={startDate}
                  onChange={handleChange('startDate')}
                  minDate={courseStartDate}
                  maxDate={courseEndDate}
                />
              </Box>
            </Box>

            <Box display="flex" alignItems="center" gap={0.5} mb={1}>
              <ScheduleLabel label="End date:" />
              <Box width={190}>
                <MuiDatePicker
                  value={endDate}
                  onChange={handleChange('endDate')}
                  disabled={!startDate}
                  minDate={startDate}
                  maxDate={courseEndDate}
                />
              </Box>
            </Box>
          </>
        )}

        {rangeMethod === 'byWeek' && (
          <>
            <Box display="flex" alignItems="center" gap={0.5} mb={1}>
              <ScheduleLabel label="Start week:" />
              <Box width={140}>
                <MuiSelect
                  placeholder="- Select -"
                  options={startWeekOptions}
                  value={startWeek}
                  changed={handleChange('startWeek')}
                  renderValue={() => (startWeek ? `Week ${startWeek}` : '- Select -')}
                />
              </Box>
            </Box>

            <Box display="flex" alignItems="center" gap={0.5} mb={1}>
              <ScheduleLabel label="End week:" />
              <Box width={140}>
                <MuiSelect
                  placeholder="- Select -"
                  options={endWeekOptions}
                  value={endWeek}
                  changed={handleChange('endWeek')}
                  renderValue={() => (endWeek ? `Week ${endWeek}` : '- Select -')}
                  disabled={!startWeek}
                />
              </Box>
            </Box>

            {startWeek && (
              <Typography mb={2} color="#6B7280">
                {getWeekDate('start', startWeek)} - {getWeekDate('end', endWeek)}
              </Typography>
            )}
          </>
        )}
      </Box>

      <div className="mt-1 mb-2">
        <ScheduleLabel label="Occurrence in a week" />
        <ToggleButtonGroup
          value={occurrence.toJS()}
          onChange={(_, val) => handleChange('occurrence')(fromJS(val))}
          aria-label="occurrence"
          disabled={isOccurrenceDisabled()}
        >
          {weeks.map((week, index) => (
            <ToggleButton key={index} value={week.get('value')} sx={weekToggleButtonSx}>
              {week.get('name')}
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </div>

      <Box mt={1.5}>
        <table>
          <thead>
            <tr>
              <th></th>
              <th className="px-2">
                <ScheduleLabel label="Start time" />
              </th>
              <th></th>
              <th className="px-2">
                <ScheduleLabel label="End time" />
              </th>
              <th>
                <ScheduleLabel label="Occur on" />
              </th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {slots.map((slot, index) => (
              <tr key={index}>
                <td className="py-1">
                  <Box sx={slotBoxSx}>
                    <Box className="f-12" color="#6B7280">
                      Slot
                    </Box>
                    <div className="f-12 fw-600">{index + 1}</div>
                  </Box>
                </td>
                <td className="px-2">
                  <div className="width_150px">
                    <MuiMobileTimePicker
                      value={slot.get('startTime', '')}
                      onChange={handleChangeSlot(index, 'startTime')}
                      disabled={!occurrence.size}
                    />
                  </div>
                </td>
                <td>
                  <SecondaryText fontSize={14} text="to" />
                </td>
                <td className="px-2">
                  <div className="width_150px">
                    <MuiMobileTimePicker
                      value={slot.get('endTime', '')}
                      onChange={handleChangeSlot(index, 'endTime')}
                      disabled={!occurrence.size}
                    />
                  </div>
                </td>
                <td>
                  <div className="width_300px">
                    <WeekSelect
                      options={weekdays}
                      value={slot.get('occurOn', List()).toJS()}
                      changed={handleChangeSlot(index, 'occurOn')}
                      disabled={!occurrence.size}
                    />
                  </div>
                </td>
                <td className="pl-2">
                  {occurrence.size > 0 && (
                    <IconButton
                      sx={{ p: '2px', color: '#DC2626' }}
                      onClick={() => handleDeleteSlot(index)}
                    >
                      <DeleteOutlineIcon fontSize="small" />
                    </IconButton>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="mt-1 ml-5 pl-1">
          <MButton
            variant="text"
            startIcon={<AddIcon />}
            clicked={handleAddSlot}
            sx={{ p: '2px', color: '#0064C8' }}
            disabled={!occurrence.size}
          >
            Slot {slots.size + 1}
          </MButton>
        </div>

        <div className="d-flex align-items-center mt-2 mb-4">
          <FormControlLabel
            control={<Checkbox sx={checkboxSx} />}
            label="Skip Holiday/Event Sessions"
            checked={scheduleState.get('skipSessions', false)}
            onChange={(e) => handleChange('skipSessions')(e.target.checked)}
            disabled={!occurrence.size}
            sx={{ m: '0 8px 0 0' }}
          />

          <CustomTooltip
            title="If checked, sessions will be skipped on holidays and event days."
            placement="top"
          >
            <InfoIcon sx={{ color: '#4B5563' }} />
          </CustomTooltip>
        </div>
      </Box>
    </>
  );
};
MultipleScheduleDetails.propTypes = {
  selectedCourse: PropTypes.instanceOf(Map),
  scheduleState: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
};

const ConductionDetails = ({
  courseGroupOptions,
  deliveryGroupOptions,
  modeOptions,
  infraOptions,
  staffOptions,
  scheduleState,
  handleChange,
  isMultipleSession,
  setFirstOption,
}) => {
  const courseGroups = scheduleState.get('courseGroups', List());
  const deliveryGroupIds = scheduleState.get('deliveryGroups', List());
  const mode = scheduleState.get('mode', '');
  const staffs = scheduleState.get('staffs', List());

  const isCourseGroupsDisabled = useMemo(() => {
    return isMultipleSession
      ? !scheduleState.get('occurrence', List()).size
      : !scheduleState.get('subjects', List()).size || !scheduleState.get('endTime');
  }, [isMultipleSession, scheduleState]);

  useEffect(() => {
    if (!isCourseGroupsDisabled) setFirstOption('courseGroups', courseGroupOptions, true);
  }, [isCourseGroupsDisabled, courseGroupOptions]);

  const deliveryGroups = useMemo(() => {
    return getDeliveryGroups(deliveryGroupOptions, courseGroups);
  }, [deliveryGroupOptions, courseGroups]);

  useEffect(() => {
    setFirstOption('deliveryGroups', deliveryGroups, true);
  }, [deliveryGroups]);

  useEffect(() => {
    if (deliveryGroupIds.size) setFirstOption('mode', modeOptions);
  }, [deliveryGroupIds, modeOptions]);

  useEffect(() => {
    if (mode) setFirstOption('infraAndStaff');
  }, [mode]);

  const selectedStaffs = useMemo(() => {
    if (!staffs.size) return List();

    return staffOptions.filter((staff) => staffs.includes(staff.get('value')));
  }, [staffOptions, staffs]);

  return (
    <>
      <p className="bold gray-neutral">Conduction Details</p>
      <Typography sx={infoText}>Select conduction method, students and staffs involved</Typography>

      <div className="row mt-2">
        <div className="col-6">
          <CustomMultiSelect
            label="Course groups"
            options={courseGroupOptions}
            value={scheduleState.get('courseGroups', List())}
            onChange={handleChange('courseGroups')}
            searchPlaceholder="Search group"
            toggleBtnText="Show scheduled groups"
            emptyErrorMessage="No groups found"
            disabled={isCourseGroupsDisabled}
            showScheduledStatus={!isMultipleSession}
            showScheduledSelection={!isMultipleSession}
          />
        </div>
        <div className="col-6">
          <CustomMultiSelect
            label="Delivery groups"
            options={deliveryGroups}
            value={scheduleState.get('deliveryGroups', List())}
            onChange={handleChange('deliveryGroups')}
            searchPlaceholder="Search group"
            toggleBtnText="Show scheduled groups"
            emptyErrorMessage="No groups found"
            disabled={!scheduleState.get('courseGroups', List()).size}
            showScheduledStatus={!isMultipleSession}
            showScheduledSelection={!isMultipleSession}
          />
        </div>
      </div>

      <div className="row mt-2">
        <div className="col-6">
          <ScheduleLabel label="Mode" />
          <MuiSelect
            placeholder="- Select -"
            options={modeOptions}
            value={scheduleState.get('mode', '')}
            changed={handleChange('mode')}
            disabled={!scheduleState.get('deliveryGroups', List()).size}
          />
        </div>
        <div className="col-6">
          <CustomSelect
            label="Infrastructure (Optional)"
            options={infraOptions}
            value={scheduleState.get('infrastructure', '')}
            onChange={handleChange('infrastructure')}
            searchPlaceholder="Search infrastructure"
            toggleBtnText="Show scheduled infrastructure"
            emptyErrorMessage="No infrastructure found"
            disabled={!scheduleState.get('mode')}
            showScheduledStatus={!isMultipleSession}
            showScheduledSelection={!isMultipleSession}
          />
        </div>
      </div>

      <div className="mt-2">
        <StaffSelect
          options={staffOptions}
          value={staffs}
          onChange={handleChange('staffs')}
          disabled={!scheduleState.get('mode')}
          showScheduledStatus={!isMultipleSession}
          showScheduledSelection={!isMultipleSession}
        />

        {selectedStaffs.size > 0 && (
          <div className="mt-1">
            <SelectedStaffs
              staffs={selectedStaffs}
              showScheduledStatus={!isMultipleSession}
              updateStaffs={handleChange('staffs')}
            />
          </div>
        )}
      </div>
    </>
  );
};
ConductionDetails.propTypes = {
  courseGroupOptions: PropTypes.instanceOf(List),
  deliveryGroupOptions: PropTypes.instanceOf(Map),
  modeOptions: PropTypes.instanceOf(List),
  infraOptions: PropTypes.instanceOf(List),
  staffOptions: PropTypes.instanceOf(List),
  scheduleState: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  isMultipleSession: PropTypes.bool,
  setFirstOption: PropTypes.func,
};

const AnalyzingIssues = forwardRef(({ previewDetails, scheduleAnalysis, programId }, ref) => {
  const [viewType, setViewType] = useState('table');
  const [search, setSearch] = useState(Map());
  const [viewOnTop, setViewOnTop] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedSchedules, setSelectedSchedules] = useState(List());
  const scheduleList = scheduleAnalysis.get('autoAssignReport', List());
  const totalNoOfSchedules = scheduleList.size;

  const {
    scheduleWithIssues,
    scheduleWithoutIssues,
    allScheduleIds,
    assignedCounts,
  } = useMemo(() => {
    const constructedData = scheduleList.reduce((acc, item) => {
      const id = item.get('scheduleId', '');
      if (item.get('status')) return acc.update('withoutIssues', List(), (prev) => prev.push(id));

      return acc.update('withIssues', List(), (prev) => prev.push(id));
    }, Map());

    const scheduleWithIssues = constructedData.get('withIssues', List());
    const scheduleWithoutIssues = constructedData.get('withoutIssues', List());
    const allScheduleIds = scheduleWithIssues.concat(scheduleWithoutIssues);
    const assignedCounts = getAssignedCounts(scheduleList);

    return { scheduleWithIssues, scheduleWithoutIssues, allScheduleIds, assignedCounts };
  }, [scheduleList]);

  useEffect(() => {
    setSelectedSchedules(scheduleWithoutIssues);
  }, [scheduleWithoutIssues]);

  const { isAllChecked, isIndeterminate } = useMemo(() => {
    //  const isAllChecked =
    //   scheduleWithIssues.size > 0 &&
    //   scheduleWithIssues.every((id) => selectedSchedules.includes(id));
    // const isAnyChecked = scheduleWithIssues.some((id) => selectedSchedules.includes(id));
    const isAllChecked =
      allScheduleIds.size > 0 && allScheduleIds.every((id) => selectedSchedules.includes(id));
    const isAnyChecked = allScheduleIds.some((id) => selectedSchedules.includes(id));

    return { isAllChecked, isIndeterminate: isAnyChecked && !isAllChecked };
  }, [allScheduleIds, selectedSchedules]);

  const filteredSchedules = useMemo(() => {
    const searchValue = search.get('value', '').toLowerCase();
    if (!searchValue) return scheduleList;

    return scheduleList.filter((s) => {
      const topic = s.get('deliveryTopic', '').toLowerCase();
      return topic.includes(searchValue);
    });
  }, [scheduleList, search]);

  const sortedSchedules = useMemo(() => {
    if (!viewOnTop) return filteredSchedules;

    return filteredSchedules.sort((a, b) => a.get('status', false) - b.get('status', false));
  }, [filteredSchedules, viewOnTop]);

  const handleSearch = (key) => (e) => {
    if (key === 'show') return setSearch((prev) => Map({ show: !prev.get('show') }));
    setSearch((prev) => prev.set('value', e.target.value));
  };

  const handleScheduleCheck = (scheduleId) => {
    if (scheduleId === 'all') {
      return setSelectedSchedules(
        (prev) => (isAllChecked ? List() : allScheduleIds)
        // isAllChecked
        //   ? prev.filter((id) => !scheduleWithIssues.includes(id))
        //   : prev.concat(scheduleWithIssues)
      );
    }

    setSelectedSchedules((prev) =>
      prev.includes(scheduleId) ? prev.filter((id) => id !== scheduleId) : prev.push(scheduleId)
    );
  };

  useImperativeHandle(ref, () => ({
    scheduleData: Map({
      previewDetails,
      selectedSchedules,
      totalNoOfSchedules,
      scheduleWithIssues,
    }),
  }));

  return (
    <>
      {scheduleWithIssues.size > 0 && (
        <Box sx={issueDetailsSx}>
          <div className="d-flex align-items-center">
            <ErrorIcon />
            <p className="f-14 ml-2">
              <span className="bold">({scheduleWithIssues.size}) Issues found:</span>{' '}
              {assignedCounts} are already scheduled in another session.
            </p>
          </div>
          <MButton
            variant="text"
            color="inherit"
            sx={{ height: 30, padding: '6px' }}
            clicked={() => setViewOnTop(!viewOnTop)}
          >
            {viewOnTop ? 'Reset to default' : `View on top (${scheduleWithIssues.size})`}
          </MButton>
        </Box>
      )}

      <div className="d-flex align-items-center justify-content-between mb-2 pl-2">
        <Typography variant="body2" color="#6B7280">
          Selected:{' '}
          <Typography variant="body2" component="span" fontWeight={500} color="#374151">
            {selectedSchedules.size}
          </Typography>
          /{totalNoOfSchedules}{' '}
          {scheduleWithIssues.size > 0 && `(Select the topics with Issues to be scheduled)`}
        </Typography>
        <Box display="flex" alignItems="center" gap={1}>
          {search.get('show') ? (
            <div>
              <SearchInput
                value={search.get('value', '')}
                changed={handleSearch()}
                handleClose={handleSearch('show')}
              />
            </div>
          ) : (
            <IconButton sx={filterIconSx} onClick={handleSearch('show')}>
              <SearchIcon />
            </IconButton>
          )}

          <MButton
            variant="outlined"
            color="gray"
            sx={{ ...previewBtnSx, ...(showPreview && previewBtnActiveSx) }}
            clicked={() => setShowPreview(!showPreview)}
          >
            Preview Step 1{showPreview && <CloseIcon fontSize="small" sx={previewCloseIconSx} />}
          </MButton>

          <ToggleButtonGroup
            value={viewType}
            onChange={(_, newValue) => setViewType(newValue)}
            aria-label="view-type"
            sx={toggleGroupSx}
            exclusive
          >
            <CustomTooltip title="Table view" placement="top">
              <ToggleButton value="table" sx={viewToggleButtonSx}>
                {viewType === 'table' ? <TableChartIcon /> : <TableChartOutlinedIcon />}
              </ToggleButton>
            </CustomTooltip>
            <CustomTooltip title="Calendar view" placement="top">
              <ToggleButton value="calendar" sx={viewToggleButtonSx}>
                <CalendarTodayIcon />
              </ToggleButton>
            </CustomTooltip>
          </ToggleButtonGroup>
        </Box>
      </div>

      {showPreview && <PreviewDetails scheduleData={previewDetails} />}
      <IssuesTable
        previewDetails={previewDetails}
        scheduleData={scheduleAnalysis}
        scheduleList={sortedSchedules}
        programId={programId}
        selectedSchedules={selectedSchedules}
        checkboxProps={{ isAllChecked, isIndeterminate, handleScheduleCheck }}
      />
    </>
  );
});
AnalyzingIssues.propTypes = {
  previewDetails: PropTypes.instanceOf(Map),
  scheduleAnalysis: PropTypes.instanceOf(Map),
  programId: PropTypes.string,
};

const IssuesTable = ({
  previewDetails,
  scheduleData,
  scheduleList,
  programId,
  selectedSchedules,
  checkboxProps,
}) => {
  const [pagination, setPagination] = useState(Map());
  const [editModal, setEditModal] = useState(Map());
  const { isAllChecked, isIndeterminate, handleScheduleCheck } = checkboxProps;
  const totalEntries = scheduleList.size;
  const pageNo = pagination.get('pageNo', 1);
  const limit = pagination.get('limit', 10);
  const startIndex = (pageNo - 1) * limit;
  const paginatedList = scheduleList.slice(startIndex, startIndex + limit);

  const { studentGroups, staffs } = useMemo(() => {
    const studentGroups = scheduleData
      .get('studentGroups', List())
      .flatMap((sg) => {
        return sg.get('session_group', List()).map((group) => {
          const groupName = group.get('group_name', '');
          return studentGroupRename(getFormattedGroupName(groupName, 3), programId);
        });
      })
      .join(', ');

    const staffs = scheduleData
      .get('staffs', List())
      .map((staff) => {
        return formattedFullName(staff.get('staff_name', Map()).toJS()).replace(/\s+/g, ' ').trim();
      })
      .join(', ');

    return { studentGroups, staffs };
  }, [programId, scheduleData]);

  const handlePageChange = (_, pageNo) => {
    setPagination((prev) => prev.set('pageNo', pageNo));
  };

  const handleLimitChange = (e) => {
    const limit = parseInt(e.target.value, 10);
    setPagination(Map({ pageNo: 1, limit }));
  };

  const handleClickEdit = () => setEditModal(Map({ show: true }));

  return (
    <>
      <Box className="table-responsive" borderRadius="8px 8px 0 0">
        <table className="table analyzing-issues-table">
          <thead>
            <tr>
              <th>
                <Checkbox
                  sx={{ ...checkboxSx, mr: 0 }}
                  checked={isAllChecked}
                  indeterminate={isIndeterminate}
                  onChange={() => handleScheduleCheck('all')}
                />
              </th>
              <th>Status</th>
              <th>Topic details</th>
              <th>Date & Time</th>
              <th>Student</th>
              <th>Infrastructure</th>
              <th>Staff</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {paginatedList.size ? (
              paginatedList.map((schedule, index) => {
                const scheduleId = schedule.get('scheduleId');
                const isScheduled = !schedule.get('status', false);
                const conflicts = constructConflicts(schedule);
                const { hasStudentConflict, groupMsg, studentMsg } = checkStudentConflict(
                  conflicts
                );
                const isGroupSliced = schedule.get('groupSliced', false);
                return (
                  <tr key={index}>
                    <td>
                      <Checkbox
                        sx={{ ...checkboxSx, mr: 0 }}
                        checked={selectedSchedules.includes(scheduleId)}
                        // disabled={!isScheduled}
                        onChange={() => handleScheduleCheck(scheduleId)}
                      />
                    </td>
                    <td>
                      {isGroupSliced ? (
                        <ErrorIcon sx={{ color: 'rgba(220, 38, 38, 0.85)' }} />
                      ) : isScheduled ? (
                        <ErrorIcon sx={{ color: '#D97706' }} />
                      ) : (
                        <CheckCircleIcon sx={{ color: '#15803D' }} />
                      )}
                    </td>
                    <td>
                      <p>
                        {schedule.get('deliverySymbol', '')}
                        {schedule.get('deliveryNo', '')} - {schedule.get('deliveryTopic', '')}
                      </p>
                      <SecondaryText text={previewDetails.get('subjects', '')} />
                    </td>
                    <td>
                      <Box
                        sx={statusBoxSx}
                        {...(conflicts.has('event') && { className: 'scheduled' })}
                      >
                        {getFormattedTime(schedule.get('start', Map()))} -{' '}
                        {getFormattedTime(schedule.get('end', Map()))}
                        {conflicts.has('event') && (
                          <ScheduledInfoTooltip title={conflicts.getIn(['event', 'message'], '')} />
                        )}
                      </Box>
                      <SecondaryText
                        text={moment(schedule.get('scheduleDate', '')).format('DD MMM yyyy')}
                      />
                    </td>
                    <td>
                      <Box
                        sx={statusBoxSx}
                        {...(hasStudentConflict && {
                          className: isGroupSliced ? 'groupSliced' : 'scheduled',
                        })}
                      >
                        {studentGroups}
                        {hasStudentConflict && (
                          <ScheduledInfoTooltip
                            title={
                              <>
                                {groupMsg && <p>{groupMsg}</p>}
                                {studentMsg && <p>{studentMsg}</p>}
                              </>
                            }
                          />
                        )}
                      </Box>
                    </td>
                    <td>
                      <Box
                        sx={statusBoxSx}
                        {...(conflicts.has('infra') && { className: 'scheduled' })}
                      >
                        {scheduleData.get('infraName', '')}
                        {conflicts.has('infra') && (
                          <ScheduledInfoTooltip title={conflicts.getIn(['infra', 'message'], '')} />
                        )}
                      </Box>
                    </td>
                    <td>
                      <Box
                        sx={statusBoxSx}
                        {...(conflicts.has('staff') && { className: 'scheduled' })}
                      >
                        {staffs}
                        {conflicts.has('staff') && (
                          <ScheduledInfoTooltip title={conflicts.getIn(['staff', 'message'], '')} />
                        )}
                      </Box>
                    </td>
                    <td>
                      <IconButton sx={{ p: '2px', color: '#0064C8' }} onClick={handleClickEdit}>
                        <EditIcon />
                      </IconButton>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={8} className="text-center">
                  <SecondaryText text="No topics found" />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </Box>

      {totalEntries > 10 && (
        <Box sx={tableFooterSx}>
          <Box width="33%" />
          <Box width="33%" display="flex" justifyContent="center">
            <Pagination
              sx={paginationSx}
              count={Math.ceil(totalEntries / limit)}
              page={pageNo}
              onChange={handlePageChange}
              shape="rounded"
            />
          </Box>
          <Box width="33%" display="flex" alignItems="center" justifyContent="end" gap={0.5}>
            <ScheduleLabel label="Lists per page:" />
            <Box width={80}>
              <Select
                sx={muiSelectSx}
                value={limit}
                onChange={handleLimitChange}
                size="small"
                fullWidth
              >
                {limitOptions.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </Box>
          </Box>
        </Box>
      )}

      {editModal.get('show') && (
        <Suspense fallback="">
          <EditTopicModal open topicData={Map()} handleClose={() => setEditModal(Map())} />
        </Suspense>
      )}
    </>
  );
};
IssuesTable.propTypes = {
  previewDetails: PropTypes.instanceOf(Map),
  scheduleData: PropTypes.instanceOf(Map),
  scheduleList: PropTypes.instanceOf(List),
  programId: PropTypes.string,
  selectedSchedules: PropTypes.instanceOf(List),
  checkboxProps: PropTypes.object,
};

export const PreviewDetails = ({ scheduleData, sx = {} }) => {
  return (
    <Box sx={{ ...previewBoxSx, ...sx }}>
      <div className="row">
        <div className="col-md-6">
          <PreviewDetailRow label="Course" value={scheduleData.get('course', '')} />
          <PreviewDetailRow
            label="Delivery type"
            value={`${scheduleData.get('deliveryType', '')} (All topics)`}
          />
          <PreviewDetailRow label="Subject" value={scheduleData.get('subjects', '')} />
          <PreviewDetailRow label="Date range" value={scheduleData.get('dateRange', '')} />
          <PreviewDetailRow label="Session slots" value={scheduleData.get('sessionSlots', '')} />
        </div>
        <div className="col-md-6 pl-0">
          <div className="d-flex align-items-center">
            <Divider orientation="vertical" className="mr-4" flexItem />
            <div>
              <PreviewDetailRow label="Course group" value={scheduleData.get('courseGroups', '')} />
              <PreviewDetailRow
                label="Delivery group"
                value={scheduleData.get('deliveryGroups', '')}
              />
              <PreviewDetailRow label="Mode" value={scheduleData.get('mode', '')} />
              <PreviewDetailRow label="Infra" value={scheduleData.get('infra') || '-'} />
              <PreviewDetailRow label="Staff" value={scheduleData.get('staffs', '')} />
            </div>
          </div>
        </div>
      </div>
    </Box>
  );
};
PreviewDetails.propTypes = {
  scheduleData: PropTypes.instanceOf(Map),
  sx: PropTypes.object,
};

const ScheduleDrawer = ({ show, handleClose, sessionType, filters, levelData, fetchEventList }) => {
  const programId = getURLParams('programId', true);
  const programName = getURLParams('programName', true);
  let dispatch = useDispatch();
  const [activeInstitutionCalendar] = useGetReduxData([selectActiveInstitutionCalendar]);
  const [deliveryTypes, fetchDeliveryTypes] = useGetReduxData([
    selectCourseDeliveryTypes,
    getCourseDeliveryTypes,
  ]);
  const [topics, fetchTopics] = useGetReduxData([selectCourseTopics, getCourseTopics]);
  const [availableList, fetchAvailableList] = useGetReduxData([
    selectAvailableList,
    getAvailableList,
  ]);
  const [scheduleAnalysis, fetchScheduleAnalysis] = useGetReduxData([
    selectScheduleAnalysis,
    postScheduleAnalyze,
  ]);
  const [scheduleState, setScheduleState] = useState(Map());
  const [showIssuesModal, setShowIssuesModal] = useState(Map());
  const [currentStep, setCurrentStep] = useState(1);
  const [scheduleConfirmModal, setScheduleConfirmModal] = useState(Map());
  const [previewDetails, setPreviewDetails] = useState(Map());
  const analyzingIssuesRef = useRef(null);
  const isMultipleSession = sessionType === 'multiple';
  const institutionCalendarId = activeInstitutionCalendar.get('_id');
  const selectedTerm = filters.get('term', '');
  const selectedYear = filters.get('year', '');
  const selectedLevel = filters.get('level', '');
  const courseId = scheduleState.get('course', '');
  const selectedDeliveryType = scheduleState.get('deliveryType', '');
  const deliveryTopic = scheduleState.get('topic', '');
  const sessionDate = scheduleState.get('sessionDate', '');
  const startTime = scheduleState.get('startTime', '');
  const endTime = scheduleState.get('endTime', '');
  const courseGroupIds = scheduleState.get('courseGroups', List());

  useEffect(() => {
    return () =>
      dispatch(setData(fromJS({ courseDeliveryTypes: [], courseTopics: [], availableList: {} })));
  }, []);

  useEffect(() => {
    if (!programId || !courseId) return dispatch(setData(fromJS({ courseDeliveryTypes: [] })));
    fetchDeliveryTypes({ programId, courseId });
  }, [programId, courseId]);

  useEffect(() => {
    if (!programId || !courseId || !selectedDeliveryType)
      return dispatch(setData(fromJS({ courseTopics: [] })));

    const { deliveryType, deliverySymbol } = getDeliveryTypeDetails(selectedDeliveryType);
    fetchTopics({ programId, courseId, deliveryType, deliverySymbol });
  }, [programId, courseId, selectedDeliveryType]);

  useEffect(() => {
    if (
      !institutionCalendarId ||
      !programId ||
      !selectedTerm ||
      !selectedYear ||
      !selectedLevel ||
      !courseId ||
      (!isMultipleSession && (!deliveryTopic || !sessionDate || !startTime || !endTime))
    )
      return dispatch(setData(fromJS({ availableList: {} })));

    const params = {
      institutionCalendarId,
      programId,
      term: selectedTerm,
      courseId,
      ...(isMultipleSession
        ? { yearNo: selectedYear, levelNo: selectedLevel }
        : {
            year: selectedYear,
            level: selectedLevel,
            sessionId: deliveryTopic,
            startTime: getDateAndTimeString(sessionDate, startTime),
            endTime: getDateAndTimeString(sessionDate, endTime),
          }),
    };
    fetchAvailableList({ params, isMultipleSession });
  }, [
    isMultipleSession,
    institutionCalendarId,
    programId,
    selectedTerm,
    selectedYear,
    selectedLevel,
    courseId,
    deliveryTopic,
    sessionDate,
    startTime,
    endTime,
  ]);

  const selectedLevelData = useMemo(() => {
    return getSelectedLevelData(levelData, filters);
  }, [levelData, filters]);

  const { courseOptions, courseList } = useMemo(() => {
    const year = selectedYear.replace('year', 'Year ');
    const level = levelRename(selectedLevel, programId);
    let courseList = Map();

    const courseOptions = selectedLevelData.get('courses', List()).map((course) => {
      const id = course.get('_course_id', '');
      const code = course.get('courses_number', '');
      const name = course.get('courses_name', '');
      courseList = courseList.set(id, course);

      return Map({
        name: `${code} - ${name}${getVersionName(course)}`,
        secondaryText: `${programName} • ${year} • ${level}`,
        value: id,
        scheduled: !course.get('student_group_status', false),
        errorInfo: 'Student group not created',
      });
    });

    return { courseOptions, courseList };
  }, [programId, programName, selectedYear, selectedLevel, selectedLevelData]);

  const deliveryTypeOptions = useMemo(() => {
    return deliveryTypes.map((item) => {
      const symbol = item.get('symbol', '');
      const type = item.get('types', '');
      const topics = item.get('topics', '');

      return Map({
        name: `${symbol} - ${type}`,
        secondaryText: `${topics} topics`,
        value: `${symbol}+${type}`,
        deliveryId: item.get('deliveryId', ''),
      });
    });
  }, [deliveryTypes]);

  const { topicOptions, subjectOptions, topicDuration } = useMemo(() => {
    let subjectMap = Map();
    let durationMap = Map();

    const topicOptions = topics.map((item) => {
      const id = item.get('sessionId', '');
      const deliverySymbol = item.get('deliverySymbol', '');
      const deliveryNumber = item.get('deliveryNumber', '');
      const deliveryTopic = item.get('delivery_topic', '');
      const duration = item.get('duration', 0);

      const subjects = item
        .get('subjects', List())
        .map((subject) =>
          Map({ name: subject.get('subject_name', ''), value: subject.get('_subject_id', '') })
        );

      subjectMap = subjectMap.set(id, subjects);
      durationMap = durationMap.set(id, duration);

      return Map({
        name: `${deliverySymbol}${deliveryNumber} - ${deliveryTopic}`,
        duration: `${duration} mins`,
        value: id,
      });
    });

    return { topicOptions, subjectOptions: subjectMap, topicDuration: durationMap };
  }, [topics]);

  const subjectList = useMemo(() => {
    return isMultipleSession
      ? subjectOptions
          .reduce((acc, sub) => acc.concat(sub), List())
          .toSet()
          .toList()
      : subjectOptions.get(deliveryTopic, List());
  }, [isMultipleSession, subjectOptions, deliveryTopic]);

  const { courseGroupOptions, deliveryGroupOptions } = useMemo(() => {
    let deliveryGroupsMap = Map();

    const courseGroupOptions = availableList
      .get('student_group', List())
      .filter((studentGroup) => {
        const { deliverySymbol } = getDeliveryTypeDetails(selectedDeliveryType);
        return studentGroup.get('delivery_symbol') === deliverySymbol;
      })
      .map((studentGroup) => {
        const id = studentGroup.get('_id');
        const groupName = studentGroup.get('group_name', '');
        const offset = isIndGroup(groupName) ? 1 : 2;
        let isGroupScheduled = true;

        const deliveryGroups = studentGroup.get('session_group', List()).map((sessionGroup) => {
          const sessionGroupName = sessionGroup.get('group_name', '');
          const availability = sessionGroup.get('availability', false);
          if (availability) isGroupScheduled = false;
          return Map({
            name: studentGroupRename(getFormattedGroupName(sessionGroupName, 3), programId),
            value: sessionGroup.get('_id'),
            scheduled: !availability,
            errorInfo: sessionGroup.get('conflict', ''),
            studentIds: sessionGroup.get('studentIds', List()),
            disabled: !sessionGroup.get('scheduleStatus', true),
          });
        });

        deliveryGroupsMap = deliveryGroupsMap.set(id, deliveryGroups);

        return studentGroup.merge(
          Map({
            name: getFormattedGroupName(studentGroupRename(groupName, programId), offset),
            value: id,
            scheduled: isGroupScheduled,
            errorInfo: studentGroup.get('conflict', ''),
            disabled: !studentGroup.get('scheduleStatus', true),
          })
        );
      });

    return { courseGroupOptions, deliveryGroupOptions: deliveryGroupsMap };
  }, [availableList, programId, selectedDeliveryType]);

  const setFirstOption = (key, options, isMultiple = false) => {
    if (key === 'infraAndStaff') return setDefaultInfraAndStaff();

    const validOptions = filterValidOptions(options);
    if (validOptions.size !== 1) return;

    let firstValue = getFirstValue(validOptions);
    if (isMultiple) firstValue = List([firstValue]);
    else if (key === 'mode') firstValue = { target: { value: firstValue } };

    handleChange(key)(firstValue);
  };

  const setDefaultInfraAndStaff = () => {
    let updatedValue = scheduleState;
    let key;
    if (infraOptions.size === 1) {
      key = 'infrastructure';
      updatedValue = updatedValue.set(key, getFirstValue(infraOptions));
    }
    if (staffOptions.size === 1) {
      key = 'staffs';
      updatedValue = updatedValue.set(key, List([getFirstValue(staffOptions)]));
    }

    if (key) {
      updatedValue = clearSelectedValues(updatedValue, key);
      setScheduleState(updatedValue);
    }
  };

  const modeOptions = useMemo(() => fromJS(getModes(programId)), [programId]);

  const handleChange = (key) => (val) => {
    if (['rangeMethod', 'startWeek', 'endWeek', 'mode'].includes(key)) val = val.target.value;

    let updatedValue = scheduleState.set(key, val);
    if (key === 'startTime') {
      const endTime = addMinutes(val, topicDuration.get(deliveryTopic, 0));
      updatedValue = updatedValue.set('endTime', endTime);
    }

    updatedValue = clearSelectedValues(updatedValue, key);
    setScheduleState(updatedValue);
  };

  const handleSingleSchedule = (isConfirmed = false) => {
    if (!isConfirmed) {
      const options = { courseGroupOptions, deliveryGroupOptions, infraOptions, staffOptions };
      const conflicts = checkScheduleConflicts({ scheduleState, ...options });
      if (!conflicts.isEmpty()) return setShowIssuesModal(Map({ show: true, conflicts }));
    }

    const courseDetails = courseList.get(courseId, Map());
    const topicName = topicOptions
      .find((topic) => topic.get('value') === deliveryTopic)
      ?.get('name', '');
    const subjects = constructSubjects(subjectList, scheduleState.get('subjects', List()));
    const deliveryGroups = scheduleState.get('deliveryGroups', List());
    const studentGroups = constructStudentGroups(courseGroupOptions, deliveryGroups);
    const infraId = scheduleState.get('infrastructure');
    const staffs = availableList
      .get('scheduleStaffs', List())
      .filter((staff) => scheduleState.get('staffs', List()).includes(staff.get('_id')))
      .map((staff) => Map({ staffId: staff.get('_id'), staffName: staff.get('name', Map()) }));

    const requestBody = {
      institutionCalendarId,
      programId,
      programName,
      courseId,
      courseCode: courseDetails.get('courses_number'),
      courseName: courseDetails.get('courses_name'),
      term: selectedTerm,
      year: selectedYear,
      level: selectedLevel,
      studentGroupId: availableList.get('_student_group_id'),
      type: 'regular',
      // "deliveryId":"668bafd8f02329c465b4e3fa",
      sessionId: deliveryTopic,
      studentGroups,
      // rotation: 'no',
      scheduleDate: sessionDate,
      start: getSlotTimeObject(startTime),
      end: getSlotTimeObject(endTime),
      scheduleStartDateAndTime: getDateAndTimeString(sessionDate, startTime),
      scheduleEndDateAndTime: getDateAndTimeString(sessionDate, endTime),
      mode: scheduleState.get('mode'),
      subjects,
      staffs,
      ...(infraId && { infraId, infraName: getInfraName(infraOptions, infraId) }),
      // isActive: true,
      // remotePlatform: 'zoom',
      // outsideCampus: '',
      overWriteConflict: true,
    };

    dispatch(
      postSingleSchedule({
        requestBody,
        topicName,
        callback: () => {
          handleClose();
          fetchEventList();
        },
      })
    );
  };

  const handleScheduleWithIssues = (isConfirmed = false) => {
    if (!isConfirmed) {
      const scheduleData = analyzingIssuesRef.current?.scheduleData;
      if (!scheduleData.get('selectedSchedules', List()).size)
        return dispatch(resetMessage('No topics selected'));
      return setScheduleConfirmModal(Map({ show: true, scheduleData }));
    }

    const scheduleIds = scheduleConfirmModal.getIn(['scheduleData', 'selectedSchedules'], List());
    const requestBody = Map({
      institutionCalendarId,
      programId,
      courseId,
      term: selectedTerm,
      yearNo: selectedYear,
      levelNo: selectedLevel,
      scheduleIds,
    });

    dispatch(
      postMultiSchedule({
        requestBody,
        callback: () => {
          handleClose();
          fetchEventList();
        },
      })
    );
  };

  const handleMultiSchedule = () => {
    if (currentStep === 2) return handleScheduleWithIssues();

    const { deliveryType } = getDeliveryTypeDetails(selectedDeliveryType);
    const deliveryId = getDeliveryId(deliveryTypeOptions, selectedDeliveryType);
    const subjects = constructSubjects(subjectList, scheduleState.get('subjects', List()), true);
    const infraId = scheduleState.get('infrastructure');
    const courseDetails = courseList.get(courseId, Map());
    const { startDate, endDate } = getOccurrenceDateRange(scheduleState, courseDetails);
    const occurrences = constructOccurrences(scheduleState.get('slots', List()));
    const deliveryGroups = scheduleState.get('deliveryGroups', List());
    const studentGroups = constructStudentGroups(courseGroupOptions, deliveryGroups, true);

    const requestBody = Map({
      institutionCalendarId,
      programId,
      courseId,
      term: selectedTerm,
      yearNo: selectedYear,
      levelNo: selectedLevel,
      studentGroupId: availableList.get('_student_group_id'),
      type: 'Regular',
      deliveryType,
      deliveryId,
      studentGroups,
      mode: scheduleState.get('mode'),
      subjects,
      staffs: scheduleState.get('staffs', List()),
      ...(infraId && { infraId, infraName: getInfraName(infraOptions, infraId) }),
      occurrenceBy: scheduleState.get('rangeMethod'),
      occurrenceWeek: scheduleState.get('occurrence', List()),
      occurrences,
      occurrenceStartDate: startDate,
      occurrenceEndDate: endDate,
      allowToScheduleOnEvents: !scheduleState.get('skipSessions', false),
    });

    fetchScheduleAnalysis({
      requestBody,
      callback: () => {
        const previewDetails = constructPreviewDetails({
          scheduleState,
          requestBody,
          courseOptions,
          courseGroupOptions,
          deliveryGroupOptions,
          staffOptions,
        });
        setPreviewDetails(previewDetails);
        setCurrentStep(2);
      },
    });
  };

  const infraOptions = getInfrastructures({
    scheduleState,
    courseGroupOptions,
    courseGroupIds,
    availableList,
    selectedTerm,
    selectedLevel,
  });

  const staffOptions = getStaffs({ scheduleState, availableList });

  return (
    <Drawer open={show} anchor="right" sx={drawerSx}>
      <Box sx={headerSx}>
        <Typography fontWeight={500} color="#374151">
          Schedule - {!isMultipleSession ? 'Single' : 'Multi'} Session
        </Typography>
        <Typography fontSize={14} color="#6B7280">
          {programName}
          <span className="mx-1">•</span>
          {selectedLevelData.get('curriculum', '')}
          <span className="mx-1">•</span>
          {selectedYear.replace('year', 'Year ')}
          <span className="mx-1">•</span>
          {levelRename(selectedLevel, programId)}
        </Typography>
      </Box>

      <Box sx={drawerContentSx}>
        {isMultipleSession && (
          <div className="mb-3">
            <ScheduleStepper currentStep={currentStep} />
          </div>
        )}

        {currentStep === 1 && (
          <>
            <Typography sx={mandatoryTextSx}>
              All fields mandatory<span>*</span>
            </Typography>

            <CourseDetails
              selectedCourse={courseList.get(courseId, Map())}
              courseOptions={courseOptions}
              deliveryTypeOptions={deliveryTypeOptions}
              topicOptions={topicOptions}
              subjectList={subjectList}
              scheduleState={scheduleState}
              handleChange={handleChange}
              isMultipleSession={isMultipleSession}
              setFirstOption={setFirstOption}
            />
            <Divider className="mt-3 mb-4" />

            <p className="bold gray-neutral">Schedule Details</p>
            {isMultipleSession ? (
              <MultipleScheduleDetails
                selectedCourse={courseList.get(courseId, Map())}
                scheduleState={scheduleState}
                handleChange={handleChange}
              />
            ) : (
              <ScheduleDetails
                selectedCourse={courseList.get(courseId, Map())}
                scheduleState={scheduleState}
                handleChange={handleChange}
              />
            )}
            <Divider className="mt-3 mb-4" />

            <ConductionDetails
              courseGroupOptions={courseGroupOptions}
              deliveryGroupOptions={deliveryGroupOptions}
              modeOptions={modeOptions}
              infraOptions={infraOptions}
              staffOptions={staffOptions}
              scheduleState={scheduleState}
              handleChange={handleChange}
              isMultipleSession={isMultipleSession}
              setFirstOption={setFirstOption}
            />
          </>
        )}

        {currentStep === 2 && (
          <AnalyzingIssues
            ref={analyzingIssuesRef}
            previewDetails={previewDetails}
            scheduleAnalysis={scheduleAnalysis}
            programId={programId}
          />
        )}
      </Box>
      <Box sx={{ ...footerSx, ...(currentStep === 2 && { justifyContent: 'space-between' }) }}>
        <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
          Cancel
        </MButton>

        {isMultipleSession ? (
          <div>
            {currentStep === 2 && (
              <MButton
                variant="outlined"
                color="gray"
                clicked={() => setCurrentStep(1)}
                sx={buttonSx}
              >
                Back
              </MButton>
            )}
            <MButton
              variant="contained"
              color="primary"
              className="ml-2"
              clicked={handleMultiSchedule}
              sx={buttonSx}
              disabled={scheduleState.get('staffs', List()).isEmpty()}
            >
              {currentStep === 1 ? 'Next' : 'Schedule Now'}
            </MButton>
          </div>
        ) : (
          <MButton
            variant="contained"
            color="primary"
            className="ml-2"
            clicked={() => handleSingleSchedule()}
            sx={buttonSx}
            disabled={scheduleState.get('staffs', List()).isEmpty()}
          >
            Schedule
          </MButton>
        )}
      </Box>

      {showIssuesModal.get('show') && (
        <Suspense fallback="">
          <ScheduleIssuesModal
            open
            conflicts={showIssuesModal.get('conflicts', Map())}
            handleConfirm={() => handleSingleSchedule(true)}
            handleClose={() => setShowIssuesModal(Map())}
          />
        </Suspense>
      )}

      {scheduleConfirmModal.get('show') && (
        <Suspense fallback="">
          <ScheduleConfirmationModal
            open
            scheduleData={scheduleConfirmModal.get('scheduleData', Map())}
            handleClose={() => setScheduleConfirmModal(Map())}
            handleConfirm={() => handleScheduleWithIssues(true)}
          />
        </Suspense>
      )}
    </Drawer>
  );
};

ScheduleDrawer.propTypes = {
  show: PropTypes.bool,
  handleClose: PropTypes.func,
  sessionType: PropTypes.string,
  filters: PropTypes.instanceOf(Map),
  levelData: PropTypes.instanceOf(Map),
  fetchEventList: PropTypes.func,
};

export default ScheduleDrawer;
