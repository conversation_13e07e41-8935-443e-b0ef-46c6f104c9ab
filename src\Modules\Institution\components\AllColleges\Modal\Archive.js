import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { Modal } from 'react-bootstrap';
import ArchiveImg from '../../../../../Assets/archive.png';
import MButton from 'Widgets/FormElements/material/Button';

function Archive({ show, setShow, status, setArchiveCollege }) {
  return (
    <Modal show={show} centered onHide={() => setShow(false)}>
      <Modal.Body>
        <p className="mb-3 mt-2 f-22">
          {!status && <img className="mr-2" alt={'Archive'} src={ArchiveImg} />}
          {status ? (
            <Trans i18nKey={'add_colleges.cannot_archive'} />
          ) : (
            <Trans i18nKey={'add_colleges.archive_college'} />
          )}
        </p>
        <p className="mb-0">
          {' '}
          {status ? (
            <Trans i18nKey={'add_colleges.currently_active'} />
          ) : (
            <Trans i18nKey={'add_colleges.archive_selected_college'} />
          )}
        </p>
      </Modal.Body>

      <Modal.Footer className="border-none">
        {status ? (
          <MButton className="onboard-button-wd-140" color="primary" clicked={() => setShow(false)}>
            <Trans i18nKey={'program_input.okay'}></Trans>
          </MButton>
        ) : (
          <>
            <MButton
              className="onboard-button-wd-140"
              color="inherit"
              clicked={() => setShow(false)}
              variant="outlined"
            >
              <Trans i18nKey={'cancel'}></Trans>
            </MButton>

            {/* <button className="remove_hover btn btn-ins-login mr-3" onClick={() => setShow(false)}>
              <Trans i18nKey={'cancel'}></Trans>
            </button> */}

            <MButton
              className="onboard-button-wd-140"
              color="red"
              clicked={() => {
                setArchiveCollege('archieve');
                setShow(false);
              }}
            >
              <Trans i18nKey={'program_input.archive'}></Trans>
            </MButton>
          </>
        )}
      </Modal.Footer>
    </Modal>
  );
}

Archive.propTypes = {
  show: PropTypes.string,
  status: PropTypes.bool,
  setShow: PropTypes.func,
  setArchiveCollege: PropTypes.func,
};
export default Archive;
