import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { getLang } from 'utils';
import { t } from 'i18next';
const lang = getLang();
function ManageCourseTabs({ setData, mcActiveTab, showManageTopic = true }) {
  return (
    <ul className="course_tab">
      {CheckPermission(
        'subTabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        '',
        'Manage Course',
        'Session Default Settings View'
      ) && (
        <li
          className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
          onClick={() => {
            setData(Map({ mcActiveTab: 'Session Default Settings' }));
          }}
        >
          <span
            className={`course_list cursor-pointer ${
              mcActiveTab === 'Session Default Settings' && 'active_course'
            }`}
          >
            {t('session_default_settings')}
          </span>
        </li>
      )}

      {showManageTopic &&
        CheckPermission(
          'subTabs',
          'Schedule Management',
          'Course Scheduling',
          '',
          'Schedule',
          '',
          'Manage Course',
          'Manage Topic View'
        ) && (
          <li
            className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
            onClick={() => {
              setData(Map({ mcActiveTab: 'Manage Topic' }));
            }}
          >
            <span
              className={`course_list cursor-pointer ${
                mcActiveTab === 'Manage Topic' && 'active_course'
              }`}
            >
              {t('manage_topic')}
            </span>
          </li>
        )}
      {CheckPermission(
        'subTabs',
        'Schedule Management',
        'Course Scheduling',
        '',
        'Schedule',
        '',
        'Manage Course',
        'Advance Settings View'
      ) && (
        <li
          className={`${lang !== 'ar' ? 'course_tab_li' : 'course_tab_li_ar'}`}
          onClick={() => {
            setData(Map({ mcActiveTab: 'Advance Settings' }));
          }}
        >
          <span
            className={`course_list cursor-pointer ${
              mcActiveTab === 'Advance Settings' && 'active_course'
            }`}
          >
            {t('settings.advance_settings')}
          </span>
        </li>
      )}
    </ul>
  );
}

ManageCourseTabs.propTypes = {
  mcActiveTab: PropTypes.string,
  setData: PropTypes.func,
  showManageTopic: PropTypes.bool,
};

export default ManageCourseTabs;
