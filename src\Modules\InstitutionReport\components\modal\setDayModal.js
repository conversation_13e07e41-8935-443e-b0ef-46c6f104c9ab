import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import TextField from '@mui/material/TextField';
import { Map } from 'immutable';
import moment from 'moment';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { convertTime12to24 } from 'Modules/InstitutionReport/utils';
function SetDayModal({ handleDayClose, data, setData }) {
  const [popUpData, setPopUpData] = useState(
    Map({
      type: 'All Day',
      startTime: null,
      endTime: null,
    })
  );

  const dateRange = data.get('date', Map());
  const startTime = data.getIn(['time', 'start'], '');
  const endTime = data.getIn(['time', 'end'], '');

  useEffect(() => {
    if (startTime !== '' && endTime !== '') {
      setPopUpData(
        popUpData.set('type', 'Time Range').set('startTime', startTime).set('endTime', endTime)
      );
    }
  }, []); //eslint-disable-line

  const isSameDay = useMemo(() => {
    const startDate = dateRange.get('start', '');
    const endDate = dateRange.get('end', '');
    return startDate === endDate;
  }, [dateRange]);

  const handleChange = (e, name) => {
    const value = e;
    if (name === 'type' && value === 'All Day') {
      setPopUpData(popUpData.set('type', 'All Day').set('startTime', '').set('endTime', ''));
      setData(data.setIn(['time', 'start'], '').setIn(['time', 'end'], ''));
    } else setPopUpData(popUpData.set(name, value));
  };
  const handleApply = () => {
    setData(
      data
        .setIn(['time', 'start'], popUpData.get('startTime', ''))
        .setIn(['time', 'end'], popUpData.get('endTime', ''))
    );
    handleDayClose();
  };

  function disabledButton() {
    if (popUpData.get('type', '') === 'All Day') return false;

    const startTime = popUpData.get('startTime', '');
    const endTime = popUpData.get('endTime', '');
    if (!startTime || !endTime) return true;

    const currentDate = moment(new Date()).format('YYYY-MM-DD');
    const convertedStartDate = convertTime12to24(moment(new Date(startTime)).format('hh:mm A'));
    const convertedEndDate = convertTime12to24(moment(new Date(endTime)).format('hh:mm A'));
    const startDate = `${currentDate}T${convertedStartDate}:00.000Z`;
    const endDate = `${currentDate}T${convertedEndDate}:00.000Z`;
    return isSameDay && Date.parse(startDate) >= Date.parse(endDate);
  }

  return (
    <MaterialDialog
      show={true}
      onClose={handleDayClose}
      maxWidth={popUpData.get('type', '') === 'Time Range' ? (isSameDay ? 'sm' : 'md') : 'xs'}
      fullWidth={true}
    >
      <div className="w-100 p-4">
        <p className="mb-3 bold f-19"> Set Time Duration</p>
        <FormControl>
          <RadioGroup
            value={popUpData.get('type', '')}
            onChange={(e) => handleChange(e.target.value, 'type')}
          >
            <FormControlLabel value="All Day" control={<Radio />} label="All Day" />
            <FormControlLabel value="Time Range" control={<Radio />} label="Time Range" />
          </RadioGroup>
        </FormControl>
        {popUpData.get('type', '') === 'Time Range' && (
          <React.Fragment>
            <div className="d-flex">
              <div className="d-flex align-items-center">
                <label className="mr-3 mt-1">
                  {isSameDay ? 'Start Range' : 'Start Date Range'}
                </label>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <TimePicker
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        sx={{
                          width: 161,
                          '& .MuiOutlinedInput-input': {
                            padding: '11.5px 14px !important',
                          },
                        }}
                        inputProps={{
                          ...params.inputProps,
                          placeholder: '08:00 AM',
                        }}
                      />
                    )}
                    value={popUpData.get('startTime', null)}
                    onChange={(newValue) => {
                      setPopUpData(popUpData.set('startTime', newValue));
                    }}
                  />
                </LocalizationProvider>
              </div>

              <div className="ml-4 d-flex align-items-center">
                <label className="mr-3 mt-1">{isSameDay ? 'End Range' : 'End Date Range'}</label>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <TimePicker
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        sx={{
                          width: 161,
                          '& .MuiOutlinedInput-input': {
                            padding: '11.5px 14px !important',
                          },
                        }}
                        inputProps={{
                          ...params.inputProps,
                          placeholder: '10:00 AM',
                        }}
                      />
                    )}
                    value={popUpData.get('endTime', null)}
                    onChange={(newValue) => {
                      setPopUpData(popUpData.set('endTime', newValue));
                    }}
                  />
                </LocalizationProvider>
              </div>
            </div>
            <p className="mb-3 mt-2 f-13 curriculum-title">
              {' '}
              Sessions Started during the time range will be gathered{' '}
            </p>
          </React.Fragment>
        )}

        <div className="d-flex justify-content-end mt-2">
          <MButton variant="outlined" color="darkGray" clicked={handleDayClose} className="mr-3">
            Cancel
          </MButton>
          <MButton variant="contained" clicked={handleApply} disabled={disabledButton()}>
            Apply
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
}

SetDayModal.propTypes = {
  handleDayClose: PropTypes.func,
  data: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
};

export default SetDayModal;
