import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory, useParams } from 'react-router-dom';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import { dString } from 'utils';
import { selectProgramSettings } from '_reduxapi/program_input/v2/selectors';
import * as action from '_reduxapi/program_input/v2/actions';
import EmailIdConFiguration from '../EmailIdConfiguration';
import {
  TimeZone,
  SelectLanguage,
  WorkingDays,
  Breaks,
  Events,
  GenderSegregation,
  PrivacySettings,
} from './BasicDetailComponents';
import Vaccination from '../StaffUserManagement/Vaccination';
import { setAccordionHeader } from '../StaffUserManagement/StaffUserManagementIndex';
import { formatDateObject, formatDateTime } from '../../utils';
import * as actions from '_reduxapi/global_configuration/actions';
import { selectBasicDetails } from '_reduxapi/global_configuration/selectors';
import { addMinutes, setHours, setMinutes } from 'date-fns';
import { getHour, getInstitutionHeader } from 'v2/utils';
export const basicDetailContext = React.createContext({});

function BasicDetails(props) {
  const {
    basicDetails,
    getBasicDetails,
    editBasicDetails,
    updateBreak,
    updateEventType,
    updateWorkingDays,
    updateLanguage,
    setData,
    updateProgramBasicDetails,
    programSettings,
    getProgramSettings,
    updatePrivacySettings,
  } = props;

  const settings = programSettings.getIn(['programSettings', 'settings', 'basicDetails'], List());
  const id = basicDetails.get('_id', '');

  const history = useHistory();
  const params = useParams();
  const getProgramID = dString(params.programID ? params.programID : params.id);
  const [basicDetailsState, setBasicDetailsState] = useState(Map());
  const [accordOpen, setAccordOpen] = useState({});
  const [open, setOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [item, setItem] = useState(Map());
  const [type, setType] = useState('');
  const [checked, setChecked] = useState({});
  const [language, setLanguage] = useState({});
  const institutionHeader = getInstitutionHeader(history);
  const institutionId = institutionHeader?._institution_id;
  const openSelectedAccord = (rowNumber) => {
    setAccordOpen({ [rowNumber]: !accordOpen[rowNumber] });
  };
  const getInstitutionData = (type) => {
    const getList = programSettings
      .getIn(['institutionSettings', 'globalConfiguration', 'basicDetails', type], List())
      .toJS();
    const updateList = getList.map((item) => {
      return {
        ...item,
        isInstitution: true,
      };
    });
    return fromJS(updateList);
  };

  const getBreaks = getInstitutionData('breaks');
  const getEvents = getInstitutionData('eventType');

  const programSettingBreaks = getBreaks.concat(settings.get('breaks', List()));
  const programSettingEvents = getEvents.concat(settings.get('eventType', List()));

  const CallingProgramSettings = ({ institutionId, programId }) => {
    return getProgramSettings({ institutionId, getProgramID });
  };

  useEffect(() => {
    !params.programID && getBasicDetails({ header: institutionHeader });
  }, []); // eslint-disable-line

  useEffect(() => {
    if (basicDetails.size) {
      let request = { ...basicDetails.toJS() };
      request.session.start.minute = parseInt(request.session.start.minute);
      request.session.end.minute = parseInt(request.session.end.minute);
      setBasicDetailsState(fromJS(request));
      setChecked(basicDetails.get('workingDays', List()));
      setLanguage(basicDetails.get('language', List()));
    }
  }, [basicDetails]);

  const addedMinutes = (event) => {
    let added = addMinutes(event, 15);
    return formatDateObject(added);
  };

  const addBreakEventReusable = (type, item, addType) => {
    return (
      <div className="row digi-tbl-pad pt-3 pl-2 digi-text-justify">
        <div className="col-md-12 btn-description-border text-center p-5">
          <button
            className="remove_hover btn btn-description mr-3"
            onClick={() => handleOpen(type, 'create', item)}
          >
            <Trans i18nKey={addType}></Trans>
          </button>
        </div>
      </div>
    );
  };
  const getRequest = () => {
    let request = {};
    if (basicDetails.size) {
      request = { ...basicDetailsState.toJS() };
      delete request.eventType;
      delete request.breaks;
      request.session.start.minute = parseInt(request.session.start.minute);
      request.session.end.minute = parseInt(request.session.end.minute);
      request.workingDays.map((i) => (i.session.end.minute = parseInt(i.session.end.minute)));
      request.workingDays.map((i) => (i.session.start.minute = parseInt(i.session.start.minute)));
    }
    return request;
  };

  const handleOpen = (name, operation, item) => {
    let popup = {
      ...open,
      [name]: !open[`${name}`],
    };
    setOpen(popup);
    setModalTitle(operation);
    if (operation === 'create') setItem(List());
    if (operation === 'edit' || operation === 'delete') setItem(item);
    setType(name);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const getMinMaxTime = (type, key, state) => {
    return type === 'endTime' && basicDetails.getIn(['session', 'start'], Map()).size
      ? {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 45, format: 'PM' }))
          ),
          minTime: formatDateTime(
            fromJS(addedMinutes(formatDateTime(basicDetails?.getIn(['session', 'start'], 15))))
          ),
        }
      : type === 'independentEndTime' &&
        basicDetails?.getIn(['workingDays', `${key}`, 'session'], {}).size
      ? {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 45, format: 'PM' }))
          ),
          minTime: formatDateTime(
            fromJS(
              addedMinutes(
                formatDateTime(
                  basicDetails?.getIn(['workingDays', `${key}`, 'session', 'start'], 15)
                )
              )
            )
          ),
        }
      : type === 'breakEnd' && state.size
      ? {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 30, format: 'PM' }))
          ),
          minTime: formatDateTime(
            fromJS(addedMinutes(formatDateTime(state.getIn(['session', 'start'], Map()))))
          ),
        }
      : {
          maxTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 11, minute: 30, format: t('date.pm') }))
          ),
          minTime: setHours(
            setMinutes(new Date(), 45),
            getHour(Map({ hour: 6, minute: 30, format: t('date.am') }))
          ),
        };
  };

  const values = {
    params,
    basicDetails,
    basicDetailsState,
    settings,
    editBasicDetails,
    getRequest,
    institutionHeader,
    updateProgramBasicDetails,
    getProgramID,
    CallingProgramSettings,
    institutionId,
    getMinMaxTime,
    setBasicDetailsState,
    open,
    setData,
    accordOpen,
    openSelectedAccord,
  };
  const commonProps = {
    institutionHeader,
    settingId: basicDetails.get('_id', ''),
    setAccordionHeader,
  };

  return (
    <basicDetailContext.Provider value={values}>
      <div className="pt-3 pl-3">
        <TimeZone />
        {!params.programID && (
          <>
            <SelectLanguage language={language} updateLanguage={updateLanguage} />
            <WorkingDays
              checked={checked}
              updateWorkingDays={updateWorkingDays}
              getBasicDetails={getBasicDetails}
            />
          </>
        )}

        <Breaks
          requiredFunctions={{ handleOpen, handleClose, updateBreak, addBreakEventReusable }}
          requiredData={{ item, programSettingBreaks, type, modalTitle }}
        />
        <Events
          requiredFunctions={{ handleOpen, handleClose, updateEventType, addBreakEventReusable }}
          requiredData={{ item, programSettingEvents, type, modalTitle }}
        />
        <GenderSegregation />
        <EmailIdConFiguration
          openSelectedAccord={openSelectedAccord}
          accordOpen={accordOpen}
          settingId={id}
          header={institutionHeader}
          emailIdConfig={basicDetails.get('emailIdConfiguration', Map())}
        />
        <PrivacySettings updatePrivacySettings={updatePrivacySettings} />
        <Vaccination
          accordionOpen={accordOpen[9]}
          setAccordionOpen={() => openSelectedAccord(9)}
          count={basicDetails.get('vaccineConfiguration', List()).size}
          {...commonProps}
        />
      </div>
    </basicDetailContext.Provider>
  );
}
BasicDetails.propTypes = {
  institution: PropTypes.instanceOf(List),
  getBasicDetails: PropTypes.func,
  editBasicDetails: PropTypes.func,
  updateBreak: PropTypes.func,
  updateEventType: PropTypes.func,
  updateWorkingDays: PropTypes.func,
  updateLanguage: PropTypes.func,
  basicDetails: PropTypes.object,
  setData: PropTypes.func,
  programSettings: PropTypes.instanceOf(Map),
  getProgramSettings: PropTypes.func,
  updateProgramBasicDetails: PropTypes.func,
  updatePrivacySettings: PropTypes.func,
};
const mapStateToProps = (state) => {
  return {
    basicDetails: selectBasicDetails(state),
    programSettings: selectProgramSettings(state),
  };
};

export default compose(
  withRouter,
  connect(mapStateToProps, { ...actions, ...action })
)(BasicDetails);
