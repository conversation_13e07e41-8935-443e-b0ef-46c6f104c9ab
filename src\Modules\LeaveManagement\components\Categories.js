import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Dropdown } from 'react-bootstrap';
import { connect } from 'react-redux';
import { t } from 'i18next';

import * as actions from '../../../_reduxapi/leave_management/actions';
import AddLeaveTypeModal from './../modal/AddLeaveTypeModal';
import EditLeaveTypeModal from './../modal/EditLeaveTypeModal';
import EditCategoryModal from './../modal/EditCategoryModal';
import AlertModal from './../../InfrastructureManagement/modal/AlertModal';
import { jsUcfirst, getLang } from '../../../utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
const lang = getLang();

let strDeactivateBtn = '';

class Categories extends Component {
  constructor(props) {
    super(props);

    this.state = {
      innerArrow: [],

      leaveModal: {
        show: false,
      },
      editCategoryModal: {
        show: false,
      },
      deleteCategoryModal: {
        show: false,
      },
      editLeaveTypeModal: {
        show: false,
      },
      deleteLeaveTypeModal: {
        show: false,
      },
      deactivateLeaveTypeModal: {
        show: false,
      },
    };
  }

  handleArrow = (i) => {
    let arrow = [...this.state.innerArrow];
    if (arrow.includes(i)) {
      arrow = arrow.filter((item, index) => item !== i);
    } else {
      arrow.push(i);
    }

    this.setState({
      innerArrow: arrow,
    });
  };

  handleClick = (type) => {
    if (type === 'add_leave_type') {
      this.setState({
        leaveModal: { show: true },
      });
    }

    if (type === 'edit_category') {
      this.setState({
        editCategoryModal: { show: true },
      });
    }

    if (type === 'delete_category') {
      this.setState({
        deleteCategoryModal: { show: true },
      });
    }
  };

  onModalClose = (type) => {
    if (type === 'add_leave_type') {
      this.setState({
        leaveModal: { show: false },
      });
    }

    if (type === 'edit_category') {
      this.setState({
        editCategoryModal: { show: false },
      });
    }

    if (type === 'delete_category') {
      this.setState({
        deleteCategoryModal: { show: false },
      });
    }

    if (type === 'edit_leave_type') {
      this.setState({
        editLeaveTypeModal: { show: false },
      });
    }

    if (type === 'delete_leave_type') {
      this.setState({
        deleteLeaveTypeModal: { show: false },
      });
    }

    if (type === 'deactivate_leave_type') {
      this.setState({
        deactivateLeaveTypeModal: { show: false },
      });
    }
  };

  onEditCategoryModalSave = (categoryName) => {
    const { type, categoryId } = this.props;
    const requestBody = {
      operation: 'update',
      categoryId,
      to: type,
      category_type: this.props.ComponentName,
      category_name: categoryName,
    };

    this.props.updateLeaveCategory(requestBody);
    this.setState({
      editCategoryModal: { show: false },
    });
  };

  // handleAddLeaveTypeSave = (data) => {
  //   const requestBody = {
  //     operation: 'create',
  //     categoryId: '',
  //     leaveTypeId: '',
  //     category_id: this.props.categoryId,
  //     type_name: data.typeName,
  //     desc: data.desc,
  //     gender: data.genderSpecific ? (data.genderMale ? 'male' : 'female') : 'both',
  //     payment: data.payment ? 'paid' : 'unpaid',
  //     entitlement: data.entitlement,
  //     no_of_days: parseInt(data.noOfDays),
  //     per_month: parseInt(data.perMonth),
  //     weekend_consideration: data.weekendConsideration,
  //     is_reason_required: data.isReasonRequired,
  //     is_attachment_required: data.isAttachmentRequired,
  //   };
  //   this.props.updateLeaveType(requestBody);
  //   this.setState({
  //     leaveModal: { show: false },
  //   });
  // };

  handleEditLeaveTypeModalSave = (modalData) => {
    let requestBody;
    const { categoryId, ComponentName } = this.props;
    const {
      leaveTypeId,
      leaveTypeName,
      leaveDesc,
      leaveGenderSpecific,
      leaveGenderMale,
      leavePayment,
      leaveEntitlement,
      leaveNoOfDays,
      leavePerMonth,
      leaveWeekendConsideration,
      leaveIsReasonRequired,
      leaveIsAttachmentRequired,
    } = modalData;
    if (ComponentName === 'on_duty') {
      requestBody = {
        operation: 'update',
        categoryId,
        leaveTypeId: leaveTypeId,
        type_name: leaveTypeName,
        desc: leaveDesc,
        gender: 'both',
        payment: 'paid',
        entitlement: leaveEntitlement,
        no_of_days: parseInt(leaveNoOfDays),
        per_month: parseInt(leavePerMonth),
        weekend_consideration: leaveWeekendConsideration,
        is_reason_required: leaveIsReasonRequired,
        is_attachment_required: leaveIsAttachmentRequired,
      };
    } else {
      requestBody = {
        operation: 'update',
        categoryId,
        leaveTypeId: leaveTypeId,
        type_name: leaveTypeName,
        desc: leaveDesc,
        gender: leaveGenderSpecific ? (leaveGenderMale ? 'male' : 'female') : 'both',
        payment: leavePayment ? 'paid' : 'unpaid',
        entitlement: leaveEntitlement,
        no_of_days: parseInt(leaveNoOfDays),
        per_month: parseInt(leavePerMonth),
        weekend_consideration: leaveWeekendConsideration,
        is_reason_required: leaveIsReasonRequired,
        is_attachment_required: leaveIsAttachmentRequired,
      };
    }

    this.props.updateLeaveType(requestBody, ComponentName);
    this.setState({
      editLeaveTypeModal: { show: false },
    });
  };

  handleClickDelete = () => {
    const { categoryId } = this.props;
    const requestBody = {
      operation: 'delete',
      categoryId,
    };

    this.props.updateLeaveCategory(requestBody, this.props.ComponentName);
    this.setState({
      deleteCategoryModal: { show: false },
    });
  };

  handleLeaveTypeClickDelete = () => {
    const { categoryId } = this.props;
    const requestBody = {
      categoryId,
      leaveTypeId: this.state.deleteLeaveTypeModal.data.leaveTypeId,
    };

    this.props.deleteLeaveType(requestBody);
    this.setState({
      deleteLeaveTypeModal: { show: false },
    });
  };

  handleLeaveTypeClickDeactivate = () => {
    const { categoryId } = this.props;
    const { data } = this.state.deactivateLeaveTypeModal;
    const requestBody = {
      categoryId,
      leaveTypeId: data.leaveTypeId,
      isActive: !data.isActive,
    };

    this.props.deactivateLeaveType(requestBody);
    this.setState({
      deactivateLeaveTypeModal: {
        show: false,
        data: {
          isActive: !data.isActive,
        },
      },
    });
  };

  handleClickLeaveUpdate(leaveTypeIndex, type) {
    const { leaveTypes } = this.props;

    const {
      type_name,
      desc,
      gender,
      payment,
      entitlement,
      weekend_consideration,
      is_reason_required,
      is_attachment_required,
      no_of_days,
      per_month,
      isActive,
      _id,
    } = leaveTypes[leaveTypeIndex];

    if (type === 'edit_leave_type') {
      this.setState({
        editLeaveTypeModal: {
          show: true,
          data: {
            typeName: type_name,
            desc,
            genderSpecific: gender,
            payment,
            entitlement,
            noOfDays: no_of_days,
            perMonth: per_month,
            weekendConsideration: weekend_consideration,
            isReasonRequired: is_reason_required,
            isAttachmentRequired: is_attachment_required,
            leaveTypeId: _id,
          },
        },
      });
    }

    if (type === 'delete_leave_type') {
      this.setState({
        deleteLeaveTypeModal: {
          show: true,
          data: {
            leaveTypeId: _id,
          },
        },
      });
    }

    if (type === 'deactivate_leave_type') {
      strDeactivateBtn = isActive ? 'Deactivate' : 'Activate';
      this.setState({
        deactivateLeaveTypeModal: {
          show: true,
          data: {
            leaveTypeId: _id,
            isActive,
          },
        },
      });
    }
  }

  render() {
    const { leaveTypes, categoryName, ComponentName, type, permissionName } = this.props;
    const {
      leaveModal,
      editCategoryModal,
      deleteCategoryModal,
      editLeaveTypeModal,
      deleteLeaveTypeModal,
      deactivateLeaveTypeModal,
    } = this.state;
    const isActive = t(`leaveManagement.${strDeactivateBtn}`);
    return (
      <React.Fragment>
        <div className="mb-3">
          <div className="border border-radious-8 p-3">
            {/* Leave Category */}
            <div className="d-flex justify-content-between ">
              <p className="f-14 mb-2 font-weight-bold"> {categoryName} </p>

              {(CheckPermission(
                'subTabs',
                'Leave Management',
                'Leave Settings',
                '',
                jsUcfirst(type),
                '',
                permissionName,
                'Edit Category'
              ) ||
                CheckPermission(
                  'subTabs',
                  'Leave Management',
                  'Leave Settings',
                  '',
                  jsUcfirst(type),
                  '',
                  permissionName,
                  'Delete Category'
                )) && (
                <small className="f-18">
                  <Dropdown>
                    <Dropdown.Toggle
                      variant=""
                      id="dropdown-table"
                      className="table-dropdown"
                      size="sm"
                    >
                      <div className="">
                        <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                      </div>
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        jsUcfirst(type),
                        '',
                        permissionName,
                        'Edit Category'
                      ) && (
                        <Dropdown.Item onClick={() => this.handleClick('edit_category')}>
                          {t('role_management.role_actions.Edit Category')}
                        </Dropdown.Item>
                      )}
                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        jsUcfirst(type),
                        '',
                        permissionName,
                        'Delete Category'
                      ) && (
                        <Dropdown.Item onClick={() => this.handleClick('delete_category')}>
                          {t('role_management.role_actions.Delete Category')}
                        </Dropdown.Item>
                      )}
                    </Dropdown.Menu>
                  </Dropdown>
                </small>
              )}
              {editCategoryModal.show && (
                <EditCategoryModal
                  show={editCategoryModal.show}
                  onClose={() => this.onModalClose('edit_category')}
                  onSave={this.onEditCategoryModalSave}
                  ComponentName={this.props.ComponentName}
                  categoryName={categoryName}
                />
              )}
              <AlertModal
                show={deleteCategoryModal.show}
                title={t('confirm_delete')}
                description={t('leaveManagement.confirmDeleteCategory')}
                variant={'confirm'}
                confirmButtonLabel={t('leaveManagement.yesSure')}
                cancelButtonLabel={t('no')}
                onClose={() => this.onModalClose('delete_category')}
                isAlertText
                optionalText={t('leaveManagement.allTypeDelete')}
                onConfirm={this.handleClickDelete}
                data={null}
              />
            </div>

            {/* Leave type list */}
            {leaveTypes.map((lt, i) => {
              return (
                <div className="mb-1" key={i}>
                  <div className="bg-gray border-radious-5 p-2">
                    <div
                      className="d-flex justify-content-between "
                      onClick={() => this.handleArrow(i)}
                    >
                      <p className="f-12 mb-0 font-weight-bold"> {lt.type_name} </p>
                      <p className="mb-0 f-12 remove_hover">
                        {this.state.innerArrow.includes(i) ? (
                          <i
                            className="fa fa-chevron-down f-14"
                            aria-hidden="true"
                            onClick={() => this.handleArrow(i)}
                          ></i>
                        ) : (
                          <i
                            className={`fa fa-chevron-${
                              lang === 'ar' ? 'down' : 'up'
                            } fa-rotate-90 f-14`}
                            aria-hidden="true"
                            onClick={() => this.handleArrow(i)}
                          ></i>
                        )}
                      </p>
                    </div>

                    {/* Inside Leave list (on expanding arrow button)*/}
                    {this.state.innerArrow.includes(i) && (
                      <div className="pt-2 text-left">
                        {lt.gender === 'both' && ComponentName !== 'on_duty' && (
                          <p className="f-12 mb-1 ">{t('leaveManagement.bothGender')}</p>
                        )}
                        {lt.gender === 'male' && (
                          <p className="f-12 mb-1 ">{t('leaveManagement.genderMale')}</p>
                        )}
                        {lt.gender === 'female' && (
                          <p className="f-12 mb-1 ">{t('leaveManagement.genderFemale')} </p>
                        )}
                        {type === 'student' && ComponentName !== 'on_duty' ? (
                          <p className="f-12 mb-1 ">
                            {t('leaveManagement.entitlementPerCourse', {
                              no_of_days: lt.no_of_days,
                            })}
                          </p>
                        ) : (
                          <p className="f-12 mb-1 ">
                            {t('leaveManagement.entitlementPerEntitlement', {
                              no_of_days: lt.no_of_days,
                              entitlement: lt.entitlement,
                            })}
                          </p>
                        )}
                        {ComponentName !== 'on_duty' && type !== 'student' && (
                          <p className="f-12 mb-1 ">
                            {t('leaveManagement.paymentStatus', { payment: lt.payment })}
                          </p>
                        )}
                        <p className="f-12 mb-2 ">
                          {lt.weekend_consideration && (
                            <React.Fragment>
                              <i className="fa fa-check pr-2" aria-hidden="true"></i>
                              {t('leaveManagement.considerHolidayBetweenWorkingDays')}
                            </React.Fragment>
                          )}
                        </p>
                        <div className="d-flex justify-content-between ">
                          <p className="f-12 mb-2 ">
                            {lt.is_reason_required === true && (
                              <React.Fragment>
                                <i className="fa fa-check pr-2" aria-hidden="true"></i>
                                {t('leaveManagement.requiredReason')}
                              </React.Fragment>
                            )}
                          </p>

                          {/* Edit, View and Delete Leave type */}
                          {(CheckPermission(
                            'subTabs',
                            'Leave Management',
                            'Leave Settings',
                            '',
                            jsUcfirst(type),
                            '',
                            permissionName,
                            'Edit Leave Type'
                          ) ||
                            CheckPermission(
                              'subTabs',
                              'Leave Management',
                              'Leave Settings',
                              '',
                              jsUcfirst(type),
                              '',
                              permissionName,
                              'Delete Leave Type'
                            ) ||
                            CheckPermission(
                              'subTabs',
                              'Leave Management',
                              'Leave Settings',
                              '',
                              jsUcfirst(type),
                              '',
                              permissionName,
                              'Deactivate Leave Type'
                            )) && (
                            <small className="f-18">
                              <Dropdown>
                                <Dropdown.Toggle
                                  variant=""
                                  id="dropdown-table"
                                  className="table-dropdown"
                                  size="sm"
                                >
                                  <div className="">
                                    <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                                  </div>
                                </Dropdown.Toggle>
                                <Dropdown.Menu>
                                  {CheckPermission(
                                    'subTabs',
                                    'Leave Management',
                                    'Leave Settings',
                                    '',
                                    jsUcfirst(type),
                                    '',
                                    permissionName,
                                    'Edit Leave Type'
                                  ) && (
                                    <Dropdown.Item
                                      onClick={() =>
                                        this.handleClickLeaveUpdate(i, 'edit_leave_type')
                                      }
                                    >
                                      {t('edit')}
                                    </Dropdown.Item>
                                  )}

                                  {CheckPermission(
                                    'subTabs',
                                    'Leave Management',
                                    'Leave Settings',
                                    '',
                                    jsUcfirst(type),
                                    '',
                                    permissionName,
                                    'Delete Leave Type'
                                  ) && (
                                    <Dropdown.Item
                                      onClick={() =>
                                        this.handleClickLeaveUpdate(i, 'delete_leave_type')
                                      }
                                    >
                                      {t('delete')}
                                    </Dropdown.Item>
                                  )}

                                  {CheckPermission(
                                    'subTabs',
                                    'Leave Management',
                                    'Leave Settings',
                                    '',
                                    jsUcfirst(type),
                                    '',
                                    permissionName,
                                    'Deactivate Leave Type'
                                  ) && (
                                    <Dropdown.Item
                                      onClick={() =>
                                        this.handleClickLeaveUpdate(i, 'deactivate_leave_type')
                                      }
                                    >
                                      {lt.isActive
                                        ? t('role_management.role_events.deactivate')
                                        : t('role_management.role_events.activate')}
                                    </Dropdown.Item>
                                  )}
                                </Dropdown.Menu>
                              </Dropdown>
                            </small>
                          )}
                        </div>

                        {lt.is_attachment_required === true && (
                          <p className="f-12 mb-2 ">
                            <i className="fa fa-check pr-2" aria-hidden="true"></i>
                            {t('leaveManagement.requiredProof')}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}

            {/* Leave type button */}
            {CheckPermission(
              'subTabs',
              'Leave Management',
              'Leave Settings',
              '',
              jsUcfirst(type),
              '',
              permissionName,
              'Add Leave Type'
            ) && (
              <p
                className="f-12 pt-2 mb-0 text-skyblue remove_hover"
                onClick={() => this.handleClick('add_leave_type')}
              >
                <i className="fa fa-plus pr-2" aria-hidden="true"></i>
                {this.props.ComponentName !== 'on_duty'
                  ? t('role_management.role_actions.Add Leave Type')
                  : t('leaveManagement.addType')}
              </p>
            )}
            <AddLeaveTypeModal
              show={leaveModal.show}
              onClose={() => this.onModalClose('add_leave_type')}
              // onSave={this.handleAddLeaveTypeSave}
              categoryName={categoryName}
              categoryId={this.props.categoryId}
              ComponentName={this.props.ComponentName}
              type={this.props.type}
            />
            {editLeaveTypeModal.show && (
              <EditLeaveTypeModal
                show={editLeaveTypeModal.show}
                onClose={() => this.onModalClose('edit_leave_type')}
                onSave={this.handleEditLeaveTypeModalSave}
                data={editLeaveTypeModal.data}
                type={this.props.type}
                categoryName={categoryName}
                ComponentName={this.props.ComponentName}
              />
            )}
            {deleteLeaveTypeModal.show && (
              <AlertModal
                show={deleteLeaveTypeModal.show}
                title={t('confirm_delete')}
                description={t('leaveManagement.confirmDeleteCategory')}
                variant={'confirm'}
                confirmButtonLabel={t('leaveManagement.yesSure')}
                cancelButtonLabel={t('no')}
                onClose={() => this.onModalClose('delete_leave_type')}
                onConfirm={this.handleLeaveTypeClickDelete}
                isAlertText={false}
                data={null}
              />
            )}
            {deactivateLeaveTypeModal.show && (
              <AlertModal
                show={deactivateLeaveTypeModal.show}
                title={t('leaveManagement.confirm', {
                  data: isActive,
                })}
                description={t('leaveManagement.confirmDeleteType', {
                  data: strDeactivateBtn.toLowerCase(),
                })}
                variant={'confirm'}
                confirmButtonLabel={t('YES')}
                cancelButtonLabel={t('no')}
                onClose={() => this.onModalClose('deactivate_leave_type')}
                onConfirm={this.handleLeaveTypeClickDeactivate}
                isAlertText={false}
                data={null}
              />
            )}
          </div>
        </div>
      </React.Fragment>
    );
  }
}

Categories.propTypes = {
  leaveTypes: PropTypes.array,
  categoryName: PropTypes.string,
  ComponentName: PropTypes.string,
  permissionName: PropTypes.string,
  updateLeaveType: PropTypes.func,
  categoryId: PropTypes.string,
  type: PropTypes.string,
  updateLeaveCategory: PropTypes.func,
  deleteLeaveType: PropTypes.func,
  deactivateLeaveType: PropTypes.func,
};

export default connect(null, actions)(Categories);
