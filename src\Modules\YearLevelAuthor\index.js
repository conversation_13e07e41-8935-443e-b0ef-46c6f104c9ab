import React, { lazy, Suspense, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { Route, Switch } from 'react-router';
import SnackBars from 'Modules/Utils/Snackbars';
import Loader from 'Widgets/Loader/Loader';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
import { Trans } from 'react-i18next';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Divider,
  TextField,
} from '@mui/material';
import { fromJS, List, Map } from 'immutable';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import AddIcon from '@mui/icons-material/Add';
import { levelRename } from 'utils';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectMessage,
  selectIsLoading,
  selectUserPrograms,
  selectProgramYearLevel,
} from '_reduxapi/year_level_author/selectors';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import {
  addUserModules,
  getProgramYearLevel,
  getUserPrograms,
  // resetMessage,
  setData,
} from '_reduxapi/year_level_author/action';
import Tooltips from '_components/UI/Tooltip/Tooltip';
import { getUserFullName } from 'Modules/ProgramInput/components/Course/AddCourseWithoutDetails';
import EditIcon from '@mui/icons-material/Edit';
import Popover from '@mui/material/Popover';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { CheckPermission } from 'Modules/Shared/Permissions';

const AddUserModal = lazy(() => import('./Modals/AddUserModal'));

const inactiveStyle = {
  pointerEvents: 'none',
  opacity: 0.4,
};

const iconSx = {
  fontSize: '0.9rem',
};
const editIconSx = {
  fontSize: '1rem',
};
// const menuItemStyles = {
//   fontSize: '14px',
// };
// const menuProps = {
//   PaperProps: {
//     sx: {
//       maxHeight: 250,
//     },
//   },
//   anchorOrigin: {
//     vertical: 'bottom',
//     horizontal: 'left',
//   },
//   transformOrigin: {
//     vertical: 'top',
//     horizontal: 'left',
//   },
//   autoFocus: false,
//   disableAutoFocusItem: true,
// };
const accordionSx = {
  border: '1px solid #D1D5DB',
  marginBottom: '12px',
  '&:before': {
    display: 'none',
  },
};
const accordionSummarySx = {
  flexDirection: 'row-reverse',
  '&.Mui-expanded': {
    backgroundColor: '#f0f0f0',
    minHeight: '43px',
  },
  '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
    transform: 'rotate(90deg)',
  },
  '& .MuiAccordionSummary-content': {
    margin: 0,
    marginLeft: '4px',
  },
};
const accordionDetailsSx = {
  padding: 0,
};
const levelSx = {
  padding: '9px 16px',
  borderTop: '1px solid #D1D5DB',
};

// const terms = fromJS([{ name: 'Regular', value: 'Regular' }]);

// const validateUsers = (users) => {
//   const hasUserWithoutPermissions = users.some(
//     (user) => user.get('checked') && !user.get('modules', List()).size
//   );
//   return hasUserWithoutPermissions
//     ? 'Atleast one permission must be selected for each selected user'
//     : '';
// };

const AddEditUser = ({ users, handleClick, EditAction }) => {
  if (!users.size) {
    return (
      <div
        className="d-flex align-items-center f-14 bold text-blue cursor-pointer"
        onClick={handleClick}
        style={!EditAction ? inactiveStyle : {}}
      >
        <AddIcon className="mr-1" sx={iconSx} />
        User
      </div>
    );
  }

  const userNames = users.map((user) => getUserFullName(user));

  return (
    <div className="d-flex align-items-center f-14">
      {userNames.size > 1 ? (
        <Tooltips title={userNames.join(', ')}>
          {userNames.get(0, '')} <span className="text-blue">+{userNames.size - 1}</span>
        </Tooltips>
      ) : (
        <span>{userNames.get(0, '-')}</span>
      )}
      <EditIcon
        style={!EditAction ? inactiveStyle : {}}
        className="ml-1 text-blue cursor-pointer"
        sx={editIconSx}
        onClick={handleClick}
      />
    </div>
  );
};
AddEditUser.propTypes = {
  users: PropTypes.instanceOf(List),
  handleClick: PropTypes.func,
};

function YearLevelAuthor() {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const userPrograms = useSelector(selectUserPrograms);
  const programYearLevel = useSelector(selectProgramYearLevel);
  const [expanded, setExpanded] = useState(-1);
  const [userModal, setUserModal] = useState({});
  const [programId, setProgramId] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [searchProgram, setSearchProgram] = useState('');
  const [selectedName, setProgramName] = useState('');
  const calendarId = activeInstitutionCalendar.get('_id', '');
  const yearList = programYearLevel.get('year', List());
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  const viewAction = CheckPermission('pages', 'Year Level Author', 'Author Assign', 'View');
  const EditAction = CheckPermission('pages', 'Year Level Author', 'Author Assign', 'Edit');

  useEffect(() => {
    setProgramId('');
    dispatch(setData(fromJS({ userPrograms: [], programYearLevel: {} })));
    calendarId && dispatch(getUserPrograms(calendarId));
  }, [calendarId]);

  useEffect(() => {
    setExpanded(-1);
    programId && dispatch(getProgramYearLevel(programId));
  }, [programId]);

  const programOptions = useMemo(() => {
    return userPrograms.map((item) =>
      Map({ name: item.get('name', ''), value: item.get('_id', '') })
    );
  }, [userPrograms]);

  const handleAccordion = (index) => setExpanded(index === expanded ? -1 : index);

  const handleAddUserModal = (type, year, levelIndex) => (e) => {
    let data = Map({ programId, type, year, levelIndex });
    if (type === 'year') {
      e.stopPropagation();
      const yearName = year.get('yearName', '').replace('year', 'Year ');
      const users = year.get('users', List());
      data = data.merge(Map({ name: yearName, users }));
    } else {
      const level = year.getIn(['level', levelIndex], Map());
      const levelName = levelRename(level.get('levelName', ''), programId);
      const users = level.get('users', List());
      data = data.merge(Map({ name: levelName, users }));
    }

    setUserModal({ show: true, data });
  };

  const handleAddUser = (users) => {
    // const validationError = validateUsers(users);
    // if (validationError) return dispatch(resetMessage(validationError));

    const { data } = userModal;
    const type = data.get('type');
    const year = data.get('year', Map());
    const levelIndex = data.get('levelIndex');
    const isLevel = type === 'level';

    const requestBody = Map({
      programId,
      curriculumId: programYearLevel.get('curriculumId'),
      yearName: year.get('yearName'),
      ...(isLevel && { levelName: year.getIn(['level', levelIndex, 'levelName']) }),
      authorType: type,
      users: users
        .filter((user) => user.get('checked') && user.get('modules', List()).size)
        .map((user) => Map({ userId: user.get('value'), modules: user.get('modules', List()) })),
    });
    dispatch(addUserModules({ requestBody, callback: () => setUserModal({}) }));
  };

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSelectProgram = (selectedId) => {
    setProgramId(selectedId.get('value', ''));
    setProgramName(selectedId.get('name', ''));
    handleClose();
    setSearchProgram('');
  };

  const handleSearchProgram = (e) => {
    setSearchProgram(e.target.value);
  };
  const filteredProgramOptions = useMemo(() => {
    if (!searchProgram) return programOptions;
    return programOptions.filter((option) =>
      option.get('name', '').toLowerCase().includes(searchProgram.toLowerCase())
    );
  }, [searchProgram, programOptions]);

  return (
    <div className="main bg-gray pb-5">
      <div className="mt-4">
        <div className="row justify-content-center">
          <div
            className="col-md-11 bg-white p-4"
            style={{ borderRadius: '8px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}
          >
            <h4 className="mb-3">Year/Level Author</h4>
            <div className="mb-2 ml-2">Programs</div>
            <div className="row">
              {/* <div className="col-6">
                <MaterialInput
                  elementType="MuiSelect"
                  size="small"
                  className="f-14"
                  labelclass="bold mb-1"
                  menuItemStyles={menuItemStyles}
                  MenuProps={menuProps}
                  label="Program"
                  placeholder="Select program"
                  elementConfig={programOptions}
                  value={programId}
                  changed={(e) => setProgramId(e.target.value)}
                  displayEmpty
                  searchable
                  searchPlaceholder="Search program..."
                />
              </div> */}
              <div style={!viewAction ? inactiveStyle : {}} className="col-12">
                <div>
                  <div
                    className="ml-2 mr-2 mb-2 px-3 py-2 border custom-width-textfield custom-height-textfield cursor-pointer hover:border-blue-500 flex items-center justify-between"
                    onClick={handleClick}
                  >
                    <div className="d-flex justify-content-between f-16 text-black">
                      <div>{programId ? selectedName : 'Select program...'}</div>
                      <ArrowDropDownIcon />
                    </div>
                  </div>
                  <Popover
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'left',
                    }}
                    sx={{ maxHeight: 'calc(100% - 230px)' }}
                  >
                    <TextField
                      className="ml-3 my-2 col-11"
                      label="Search program..."
                      onChange={handleSearchProgram}
                      size="small"
                    />
                    {filteredProgramOptions.size ? (
                      filteredProgramOptions.map((i, index) => {
                        return (
                          <div
                            onClick={() => handleSelectProgram(i)}
                            key={index}
                            className="p-2 ml-1 cursor-pointer custom-width-textfield f-16 text-black"
                          >
                            {i.get('name', '')}
                          </div>
                        );
                      })
                    ) : (
                      <div className="p-2 ml-3 cursor-pointer custom-width-textfield f-16 text-black text-center">
                        No Data Found
                      </div>
                    )}
                  </Popover>
                </div>
              </div>
              {/* <div className="col-6">
                <MaterialInput
                  elementType="MuiSelect"
                  size="small"
                  className="f-14"
                  labelclass="bold mb-1 text-capitalize"
                  menuItemStyles={menuItemStyles}
                  label="Term"
                  placeholder="Select term"
                  elementConfig={terms}
                  value={''}
                  changed={(e) => {}}
                  displayEmpty
                />
              </div> */}
            </div>
            {!yearList.isEmpty() && (
              <>
                <Divider className="mt-4 mb-3" />
                <div>
                  {yearList.map((year, index) => (
                    <Accordion
                      key={index}
                      expanded={expanded === index}
                      onChange={() => handleAccordion(index)}
                      elevation={0}
                      sx={accordionSx}
                      disableGutters
                    >
                      <AccordionSummary
                        expandIcon={<ArrowForwardIosSharpIcon sx={iconSx} />}
                        sx={accordionSummarySx}
                      >
                        <div className="d-flex align-items-center justify-content-between w-100">
                          <div className="f-16 bold">
                            {year.get('yearName', '').replace('year', 'Year ')}
                          </div>
                          <AddEditUser
                            users={year.get('users', List())}
                            handleClick={handleAddUserModal('year', year)}
                            EditAction={EditAction}
                          />
                        </div>
                      </AccordionSummary>
                      <AccordionDetails sx={accordionDetailsSx}>
                        {year.get('level', List()).map((level, index) => (
                          <Box
                            key={index}
                            className="d-flex align-items-center justify-content-between w-100"
                            sx={levelSx}
                          >
                            <div className="f-16 ml-5">
                              {levelRename(level.get('levelName', ''), programId)}
                            </div>
                            <AddEditUser
                              users={level.get('users', List())}
                              handleClick={handleAddUserModal('level', year, index)}
                              EditAction={EditAction}
                            />
                          </Box>
                        ))}
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {userModal.show && (
        <Suspense fallback="">
          <AddUserModal
            open
            data={userModal.data}
            handleClose={() => setUserModal({})}
            handleSave={handleAddUser}
          />
        </Suspense>
      )}
    </div>
  );
}

export default function YearLevelAuthorIndex() {
  const message = useSelector(selectMessage);
  const isLoading = useSelector(selectIsLoading);

  return (
    <div>
      {message && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>
        <Trans i18nKey="side_nav.menus.year_level_author" />
      </Breadcrumb>
      <Switch>
        <Route path="/year-level-author" component={YearLevelAuthor} />
      </Switch>
    </div>
  );
}
