import React from 'react';
import { List, Map } from 'immutable';
import moment from 'moment';

export const duplicateSessionDetail = (item2, item) => {
  const sessionStart = moment(item2.get('sessionStart', '')).format('hh:mm a');
  const sessionEnd = moment(item2.get('sessionEnd', '')).format('hh:mm a');
  const startTime = moment(item.get('sessionStart', '')).format('hh:mm a');
  const endTime = moment(item.get('sessionEnd', '')).format('hh:mm a');

  return (
    item2.get('sessionName', '') === item.get('sessionName', '') ||
    sessionStart === startTime ||
    sessionEnd === endTime ||
    (sessionStart < endTime && sessionEnd > startTime)
  );
};

export const sessionEmptyCheck = (sessionDetails) => {
  return sessionDetails
    .get('sessions', List())
    .some(
      (item) =>
        item.get('sessionStart', '') === null ||
        item.get('sessionEnd', '') === null ||
        !item.get('sessionName', '')
    );
};

const sessionTimeValidation = (sessionDetails) => {
  if (sessionDetails.get('isSameTimeForEveryDay', false)) {
    const hasDuplicates = sessionDetails.get('sessions', List()).some(
      (item, index) =>
        index !==
        sessionDetails.get('sessions', List()).findIndex((item2) => {
          return duplicateSessionDetail(item2, item);
        })
    );
    return hasDuplicates;
  } else {
    const hasDuplicates = sessionDetails
      .get('workingDays', List())
      .filter((item) => item.get('isActive', false))
      .some((day) =>
        day.get('sessions', List()).some(
          (item, index) =>
            index !==
            day.get('sessions', List()).findIndex((item2) => {
              return duplicateSessionDetail(item2, item);
            })
        )
      );

    return hasDuplicates;
  }
};

export const isSameTimeForEveryDayValidation = (sessionDetails, setSessionDetails) => {
  if (!sessionDetails.get('isSameTimeForEveryDay', false)) {
    const errorSessions = sessionDetails.get('workingDays', List()).map((session) =>
      session.set(
        'error',
        (session.get('isActive', false) &&
          (session.get('sessions', List()).size === 0 || sessionEmptyCheck(session))) ||
          session.get('sessions', List()).some(
            (item2, index) =>
              index !==
              session.get('sessions', List()).findIndex((item) => {
                return duplicateSessionDetail(item2, item);
              })
          )
      )
    );

    setSessionDetails(sessionDetails.set('workingDays', errorSessions));
  }
};

export const validation = ({ sessionDetails, setSessionDetails, setData }) => {
  const sessionValidation = () => {
    if (sessionDetails.get('isSameTimeForEveryDay', false)) {
      return sessionEmptyCheck(sessionDetails);
    } else {
      return sessionDetails
        .get('workingDays', List())
        .filter((item) => item.get('isActive', false))
        .some((day) => day.get('sessions', List()).size === 0 || sessionEmptyCheck(day));
    }
  };

  isSameTimeForEveryDayValidation(sessionDetails, setSessionDetails);

  if (!sessionDetails.get('workingDays', List()).some((d) => d.get('isActive'))) {
    setData(Map({ message: 'Please select atleast one day' }));
    return false;
  }
  if (sessionValidation()) {
    setData(Map({ message: 'Please fill all the fields' }));
    return false;
  }
  if (sessionTimeValidation(sessionDetails)) {
    setData(Map({ message: 'Duplicate session data found' }));
    return false;
  }

  return true;
};

export const EnableOrDisable = ({ valid, children }) => {
  return valid ? children : <></>;
};
