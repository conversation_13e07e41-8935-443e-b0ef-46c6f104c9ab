import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Map, List, fromJS } from 'immutable';
import Modal from 'react-bootstrap/Modal';
import Form from 'react-bootstrap/Form';
import Button from '@mui/material/Button';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import { ThemeProvider } from '@mui/styles';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import uniqWith from 'lodash/uniqWith';
import CheckCircleGreenIcon from 'Assets/alert2.png';

import {
  capitalize,
  MUI_THEME,
  MUI_CHECKBOX_THEME,
  studentGroupRename,
  getURLParams,
} from '../../../utils';
import { getFormattedGroupName } from '../components/utils';

function SelectStudentGroupsModal({
  show,
  mode,
  readOnly,
  onHide,
  studentGroups,
  data,
  activeStudentGroupId,
  handleChange,
  handleSave,
  isRotation,
  state,
  setState,
  setSearchValue,
  searchValue,
}) {
  const programId = getURLParams('programId', true);

  useEffect(() => {
    if (activeStudentGroupId === null && studentGroups.size) {
      handleChange(
        'activeStudentGroupId',
        mode !== 'create' ? data.getIn(['data', 'studentGroups', 0, 'group_id']) : ''
      );
    }
    // eslint-disable-next-line
  }, [activeStudentGroupId, handleChange, studentGroups]);

  const uniqueProgramNames = studentGroups.get('masterGroup', List());

  const uniqueArray = uniqWith(uniqueProgramNames.toJS(), (obj1, obj2) => {
    return obj1.group_name === obj2.group_name;
  });
  const unique = fromJS(uniqueArray);

  const filterDeliveryType = uniqueProgramNames.filter((s) => {
    return s.get('group_name', '') === state.get('group', '');
  });
  const uniqueDeliveryType = uniqWith(filterDeliveryType.toJS(), (obj1, obj2) => {
    return obj1.delivery_symbol === obj2.delivery_symbol;
  });
  const deliveryType = fromJS(uniqueDeliveryType);

  const filterSessionGroup = deliveryType.filter(
    (s) => s.get('delivery_symbol', '') === state.get('deliveryType', '')
  );
  const uniqueSessionGroup = uniqWith(filterSessionGroup.toJS(), (obj1, obj2) => {
    return obj1.session_group === obj2.session_group;
  });
  const uniqueGroup = fromJS(uniqueSessionGroup);

  function getModalTitle() {
    const sessionType = data.get('type', '');
    let title = !readOnly ? (mode === 'create' ? 'Add ' : 'Edit ') : '';
    if (['support'].includes(sessionType)) {
      title = title + 'Support Session';
    } else {
      title = title + capitalize(sessionType);
    }
    return title;
  }

  function isSameRotationGroup(groupNo) {
    if (!isRotation) return true;
    const rotationCount = state.get('rotation_count');
    if (rotationCount === null) return true;
    if (state.get('selectedStudents', List()).size === 0) return true;
    return rotationCount === groupNo;
  }

  const handleClick = ({ name, value, studentId = List(), groupId = '', rotation_count }) => {
    setState((prevState) => {
      const key =
        prevState.get('group', '') +
        prevState.get('deliveryType', '') +
        prevState.get('groupNo', '');
      switch (name) {
        case 'group': {
          const findDeliveryDetails = prevState.get('selectedStudents', List()).find((item) => {
            return item.get('group', '') === value;
          });

          return prevState
            .set(name, value)
            .set(
              'deliveryType',
              findDeliveryDetails === undefined ? '' : findDeliveryDetails.get('deliveryType', '')
            )
            .set(
              'groupNo',
              findDeliveryDetails === undefined ? '' : findDeliveryDetails.get('groupNo', '')
            )
            .set(
              'studentIds',
              findDeliveryDetails === undefined
                ? List()
                : findDeliveryDetails.get('studentIds', List())
            )
            .set('rotation_count', rotation_count);
        }
        case 'deliveryType': {
          const filteredStudentData = prevState.get('selectedStudents', List()).filter((item) => {
            const ids = state.get('studentIds', List());
            return !ids.includes(item.get('_student_id', ''));
          });
          return (
            prevState
              .set(name, value)
              .set('groupId', groupId)
              .set('groupNo', '')
              // .set('rotation_count', '')
              .set('studentIds', List())
              .setIn(['selectAll', key], false)
              .set('selectedStudents', filteredStudentData)
          );
        }
        default:
          return prevState.set(name, value).set('studentIds', studentId);
      }
    });
    setSearchValue('');
  };

  const checkIconsSelected = (item) => {
    return item
      .get('session_group', List())
      .some((s) =>
        state
          .get('selectedStudents', List())
          .some(
            (d) =>
              d.get('groupNo', '') === s.get('group_name', '') &&
              d.get('deliveryType', '') === state.get('deliveryType', '') &&
              d.get('group', '') === state.get('group', '')
          )
      );
  };
  const wantToDisabled = deliveryType.some((item) => checkIconsSelected(item));

  const getStudentList = () => {
    const filteredStudentData = studentGroups
      .get('sgStudentList', List())
      .filter((item) => {
        const ids = state.get('studentIds', List());
        return ids.includes(item.get('_student_id', ''));
      })
      .filter((item) => {
        const trimSpaces = (str) => str.replace(/^\s+|\s+$/g, '').replace(/\s+/g, ' ');
        const searchString = searchValue.toLowerCase();
        const studentName = trimSpaces(getStudentName(item).trim().toLowerCase());
        return studentName.includes(searchString);
      });

    return filteredStudentData.map((s) => s.merge(state));
  };

  const StudentList = getStudentList();

  function getStudentName(student) {
    return `${student.getIn(['name', 'first'], '')} ${student.getIn(
      ['name', 'middle'],
      ''
    )} ${student.getIn(['name', 'last'], '')}`;
  }

  const handleCheckboxChange = (type, checked, student) => {
    const key = state.get('group', '') + state.get('deliveryType', '') + state.get('groupNo', '');

    if (type === 'all') {
      const updatedState = state
        .set(
          'selectedStudents',
          state.get('selectedStudents', List()).merge(
            StudentList.map((item) => {
              return item
                .set('group', item.get('group'))
                .set('deliveryType', item.get('deliveryType'))
                .set('groupNo', item.get('groupNo'))
                .set('studentIds', item.get('studentIds'))
                .set('groupId', item.get('groupId'));
            })
          )
        )
        .setIn(['selectAll', key], checked);

      const updatedState2 = state
        .set(
          'selectedStudents',
          state
            .get('selectedStudents', List())
            .filter(
              (item1) =>
                !StudentList.some(
                  (item2) =>
                    item2.get('_student_id', '') +
                      item2.get('group') +
                      item2.get('deliveryType') +
                      item2.get('groupNo') ===
                    item1.get('_student_id', '') +
                      item1.get('group') +
                      item1.get('deliveryType') +
                      item1.get('groupNo')
                )
            )
        )
        .setIn(['selectAll', key], checked);

      setState(checked ? updatedState : updatedState2);
    } else if (type === 'individual') {
      const updatedState = state.set(
        'selectedStudents',
        state
          .get('selectedStudents', List())
          .filter(
            (selectedStudent) =>
              selectedStudent.get('user_id', '') +
                selectedStudent.get('group') +
                selectedStudent.get('deliveryType') +
                selectedStudent.get('groupNo') !==
              student.get('user_id', '') +
                student.get('group') +
                student.get('deliveryType') +
                student.get('groupNo')
          )
      );
      const updatedState2 = state.set(
        'selectedStudents',
        state
          .get('selectedStudents', List())
          .push(
            student
              .set('group', student.get('group'))
              .set('deliveryType', student.get('deliveryType'))
              .set('groupNo', student.get('groupNo'))
              .set('studentIds', student.get('studentIds'))
              .set('groupId', student.get('groupId'))
          )
      );
      const updatedSelectedStudents = checked ? updatedState2 : updatedState;
      const filteredStudents = updatedSelectedStudents
        .get('selectedStudents', List())
        .filter((item1) =>
          StudentList.some(
            (item2) =>
              item2.get('_student_id', '') +
                item2.get('group') +
                item2.get('deliveryType') +
                item2.get('groupNo') ===
              item1.get('_student_id', '') +
                item1.get('group') +
                item1.get('deliveryType') +
                item1.get('groupNo')
          )
        );
      setState(
        updatedSelectedStudents.setIn(
          ['selectAll', key],
          filteredStudents.size === StudentList.size
        )
      );
    }
  };

  const handleSearch = (event) => {
    setSearchValue(event);
  };

  return (
    <Modal show={show} onHide={onHide} dialogClassName="modal-750" centered>
      <Modal.Body className="p-2">
        <div className="container-fluid pt-3 pb-1 ">
          <div className="border-bottom">
            <div className="d-flex justify-content-between mb-2">
              <p className="mb-1 f-18 bold">{getModalTitle()}</p>
              <div>
                <ThemeProvider theme={MUI_THEME}>
                  <b className="pr-2">
                    <Button
                      variant="outlined"
                      size="small"
                      color="primary"
                      className="text-uppercase"
                      onClick={onHide}
                    >
                      <Trans i18nKey={'cancel'}></Trans>
                    </Button>
                  </b>
                  {!readOnly && (
                    <Button
                      variant="contained"
                      size="small"
                      color="primary"
                      className="text-uppercase"
                      onClick={handleSave}
                    >
                      <Trans i18nKey={'save'}></Trans>
                    </Button>
                  )}
                </ThemeProvider>
              </div>
            </div>
          </div>

          <div className="row mb-2 pt-1">
            <div className="col-md-12 mt-2 mb-2">
              <TextField
                label={`${
                  getModalTitle().includes('Support') ? t('session_title') : t('event_title')
                }`}
                fullWidth
                value={data.getIn(['data', 'title'], '')}
                onChange={(event) => handleChange('title', event.target.value)}
                InputProps={{ readOnly }}
                className="text-uppercase"
              />
            </div>

            <div className="col-md-12 mt-2 mb-2">
              <div className="d-flex flex-wrap-wrap">
                {unique.map((sg) => {
                  const isGroupSelectedForIcon =
                    state
                      .get('selectedStudents', List())
                      .filter((d) => d.get('group', '') === sg.get('group_name', '')).size > 0;
                  return (
                    <div
                      key={sg.get('_id')}
                      className={`student-group-chip${
                        sg.get('group_name') === state.get('group', '')
                          ? ' student-group-chip-active'
                          : ''
                      } ${
                        isSameRotationGroup(sg.get('rotation_count'))
                          ? ''
                          : 'student-group-chip-disabled disabled'
                      }`}
                      onClick={() => {
                        handleChange('activeStudentGroupId', sg.get('_id'));
                        handleClick({
                          name: 'group',
                          value: sg.get('group_name'),
                          rotation_count: sg.get('rotation_count'),
                        });
                      }}
                    >
                      {`${getFormattedGroupName(
                        studentGroupRename(sg.get('group_name', ''), programId),
                        2
                      )}`}
                      {isGroupSelectedForIcon ? (
                        <img src={CheckCircleGreenIcon} alt="check" className="pl-1" />
                      ) : (
                        ''
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
            <div className="col-md-12 mt-2 mb-2">
              <div className="d-flex flex-wrap-wrap">
                {deliveryType.map((item, i) => {
                  const isDeliverySelectedIcon = checkIconsSelected(item);
                  return (
                    <div
                      key={i}
                      className={`student-group-chip ${
                        state.get('deliveryType', '') === item.get('delivery_symbol', '')
                          ? 'student-group-chip-active'
                          : state.get('deliveryType', '') === ''
                          ? ''
                          : (state.get('selectedStudents', List()).size === 0 &&
                              getModalTitle() === 'Add Support Session') ||
                            (!wantToDisabled && getModalTitle() === 'Edit Support Session')
                          ? 'student-group-chip-disable-new'
                          : 'disabled'
                      }`}
                      onClick={() => {
                        handleClick({
                          name: 'deliveryType',
                          value: item.get('delivery_symbol', ''),
                          groupId: item.get('_id', ''),
                        });
                      }}
                    >
                      {`${item.get('delivery_symbol', '')}`}
                      {isDeliverySelectedIcon ? (
                        <img src={CheckCircleGreenIcon} alt="check" className="pl-1" />
                      ) : (
                        ''
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
            <div className="col-md-12 mt-2 mb-2">
              <div className="d-flex flex-wrap-wrap">
                {uniqueGroup.map((item, i) => {
                  return item.get('session_group', List()).map((s) => {
                    const isStudentSelectedForIcon = state
                      .get('selectedStudents', List())
                      .some(
                        (d) =>
                          d.get('groupNo', '') === s.get('group_name', '') &&
                          d.get('deliveryType', '') === state.get('deliveryType', '') &&
                          d.get('group', '') === state.get('group', '')
                      );

                    return (
                      <div
                        className={`student-group-chip ${
                          state.get('groupNo', '') === s.get('group_name', '')
                            ? 'student-group-chip-active'
                            : ''
                        }`}
                        key={i}
                        onClick={() => {
                          handleClick({
                            name: 'groupNo',
                            value: s.get('group_name', ''),
                            studentId: s.get('_student_ids', List()),
                          });
                        }}
                      >
                        {getFormattedGroupName(s.get('group_name', ''), 3)}
                        {isStudentSelectedForIcon ? (
                          <img src={CheckCircleGreenIcon} alt="check" className="pl-1" />
                        ) : (
                          ''
                        )}
                      </div>
                    );
                  });
                })}
              </div>
            </div>
            <div className="col-md-12 mt-2 mb-2">
              <div className="go-wrapper">
                <table className="table">
                  <thead>
                    <tr>
                      <th className="vertical-align-top">
                        <div className="aw-200 text-dark">
                          <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  color="primary"
                                  size="small"
                                  onChange={(event) =>
                                    handleCheckboxChange('all', event.target.checked)
                                  }
                                  checked={state.getIn(
                                    [
                                      'selectAll',
                                      state.get('group', '') +
                                        state.get('deliveryType', '') +
                                        state.get('groupNo', ''),
                                    ],
                                    false
                                  )}
                                  disabled={!state.get('groupNo', '')}
                                />
                              }
                              label={t('acad_no')}
                            />
                          </ThemeProvider>
                        </div>
                      </th>
                      <th className="vertical-align-top">
                        <div className="aw-200 text-dark">
                          <Trans i18nKey={'student_grouping.student_name'}></Trans>
                        </div>
                        <div className="aw-200 text-dark">
                          <div className="sb-example-1">
                            <div className="search">
                              <OutlinedInput
                                endAdornment={
                                  <InputAdornment
                                    position="end"
                                    classes={{ root: 'student-search-input-end-adornment' }}
                                  >
                                    <i className="fa fa-search"></i>
                                  </InputAdornment>
                                }
                                labelWidth={0}
                                margin="dense"
                                placeholder={t('search_student')}
                                classes={{
                                  root: 'student-search-input-root',
                                  input: 'student-search-input',
                                }}
                                value={searchValue}
                                onChange={(event) => handleSearch(event.target.value)}
                              />
                            </div>
                          </div>
                        </div>
                      </th>
                    </tr>
                  </thead>

                  <tbody className="go-wrapper-height">
                    {StudentList.size ? (
                      StudentList.map((student) => {
                        return (
                          <tr key={student.get('_id')}>
                            <td>
                              <div className="aw-200">
                                <Form.Check
                                  type="checkbox"
                                  label={student.get('user_id', 'N/A') || 'N/A'}
                                  checked={state
                                    .get('selectedStudents', List())
                                    .map(
                                      (s) =>
                                        s.get('user_id', '') +
                                        s.get('group', '') +
                                        s.get('deliveryType', '') +
                                        s.get('groupNo', '')
                                    )
                                    .includes(
                                      student.get('user_id', '') +
                                        student.get('group', '') +
                                        student.get('deliveryType', '') +
                                        student.get('groupNo', '')
                                    )}
                                  disabled={readOnly}
                                  onChange={(event) =>
                                    handleCheckboxChange(
                                      'individual',
                                      event.target.checked,
                                      student
                                    )
                                  }
                                />
                              </div>
                            </td>
                            <td>
                              <div className="aw-200">{getStudentName(student)}</div>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={2} className="text-align-center">
                          <Trans i18nKey={'no_students_found'}></Trans>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
}

SelectStudentGroupsModal.propTypes = {
  show: PropTypes.bool,
  mode: PropTypes.string,
  readOnly: PropTypes.bool,
  onHide: PropTypes.func,
  studentGroups: PropTypes.object,
  data: PropTypes.instanceOf(Map),
  activeStudentGroupId: PropTypes.string,
  handleChange: PropTypes.func,
  handleSave: PropTypes.func,
  isRotation: PropTypes.bool,
  state: PropTypes.func,
  setState: PropTypes.instanceOf(Map),
  setSearchValue: PropTypes.func,
  searchValue: PropTypes.string,
};

export default SelectStudentGroupsModal;
