import React, { useState } from 'react';
import { Map, List } from 'immutable';
import { useHistory } from 'react-router-dom';
import { Button } from 'react-bootstrap';
import moment from 'moment';
import PropTypes, { oneOfType } from 'prop-types';

import user from '../../../../Assets/dash_user.png';
import list from '../../../../Assets/dash_list.png';
import Input from '../../../../Widgets/FormElements/Input/Input';
import GreenCircle from '../../../../Assets/greencircle.png';
import cursorDown from '../../../../Assets/couse_down.png';
import {
  formatFullName,
  formatTwoString,
  jsUcfirst,
  getTimestamp,
  eString,
  getTranslatedDuration,
  isIndGroup,
} from '../../../../utils';
import useWindowDimensions from '../../../../_components/UI/PageSize/PageSize';
import Tooltips from '../../../../_components/UI/Tooltip/Tooltip';
import {
  getFormattedGroupName,
  convertTime12to24,
} from '../../../CourseScheduling/components/utils';
import Loader from '../../../../Widgets/Loader/Loader';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

export default function FirstCard(props) {
  const { activeTabs, leaveRequestsLists, mySessionLists, loading } = props;

  FirstCard.propTypes = {
    activeTabs: PropTypes.instanceOf(Map),
    leaveRequestsLists: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
    mySessionLists: PropTypes.instanceOf(List),
    loading: PropTypes.instanceOf(Map),
    clicked: PropTypes.func,
  };

  return (
    <div className="dash_box_col">
      <Loader
        pos="absolute"
        isLoading={
          (activeTabs.get('leaveManagementList') && loading.get('GET_LEAVE_REQUESTS_LIST')) ||
          (activeTabs.get('sessionList') && loading.get('GET_MY_SESSION_LIST'))
        }
      />
      <div className="d-flex justify-content-between border-bottom">
        {/* <div
          className="dash_tab"
          onClick={() =>
            clicked(
              Map({
                toDoList: true,
                leaveManagementList: false,
                sessionList: false,
              })
            )
          }
        >
          <a
            href="#home"
            className={`dash_list ${activeTabs.get('toDoList') ? 'active_course' : ''}`}
          >
            <img src={check} />
            {activeTabs.get('toDoList') ? (
              <Tooltips title="To Do">
                <span className="ml-1"> To Do </span>
              </Tooltips>
            ) : (
              <Tooltips title="To Do">
                <span className="ml-1 mr-1 alert_notification"> 00 </span>
              </Tooltips>
            )}
          </a>
        </div>
        */}
        <div
          className="dash_tab"
          onClick={() =>
            props.clicked(
              Map({
                toDoList: false,
                leaveManagementList: true,
                sessionList: false,
              })
            )
          }
        >
          <a
            href="#news"
            className={`dash_list ${activeTabs.get('leaveManagementList') ? 'active_course' : ''}`}
          >
            <img src={user} alt="user profile" />

            {activeTabs.get('leaveManagementList') ? (
              <Tooltips title={<Trans i18nKey={'dashboard_view.leave_mgmt'}></Trans>}>
                <span className="pl-1">
                  <Trans i18nKey={'dashboard_view.leave_mgmt'}></Trans>{' '}
                </span>
              </Tooltips>
            ) : (
              <Tooltips title={<Trans i18nKey={'dashboard_view.leave_mgmt'} />}>
                <span className="mx-1 alert_notification">
                  {' '}
                  {leaveRequestsLists.get('pending', List()).size}{' '}
                </span>
              </Tooltips>
            )}
          </a>
        </div>
        <div
          className="dash_tab"
          onClick={() =>
            props.clicked(
              Map({
                toDoList: false,
                leaveManagementList: false,
                sessionList: true,
              })
            )
          }
        >
          <a
            href="#contact"
            className={`dash_list ${activeTabs.get('sessionList') ? 'active_course' : ''}`}
          >
            <img src={list} alt="My Session" />
            {activeTabs.get('sessionList') ? (
              <Tooltips title={<Trans i18nKey={'dashboard_view.my_session'} />}>
                <span className="pl-1">
                  <Trans i18nKey={'dashboard_view.my_session'} />
                </span>
              </Tooltips>
            ) : (
              <Tooltips title={<Trans i18nKey={'dashboard_view.my_session'} />}>
                <span className="mx-1 alert_notification"> {mySessionLists.size} </span>
              </Tooltips>
            )}
          </a>
        </div>
      </div>
      {activeTabs.get('toDoList') ? (
        <TodoLists {...props} />
      ) : activeTabs.get('leaveManagementList') ? (
        <LeaveLists {...props} />
      ) : (
        <SessionsLists {...props} />
      )}
    </div>
  );
}

function LeaveLists(props) {
  const { leaveRequestsLists, setLeaveData, programsLists, loading } = props;

  LeaveLists.propTypes = {
    leaveRequestsLists: oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
    programsLists: PropTypes.instanceOf(List),
    loading: PropTypes.instanceOf(Map),
    setLeaveData: PropTypes.func,
  };

  const { height } = useWindowDimensions();
  const minHeight = height - 265;
  const history = useHistory();

  //const [tabs, setTabs] = useState('Staff');
  const [selectedProgram, setSelectedProgram] = useState('');
  const getProgramList = () => {
    let list = [{ name: t('all'), value: '' }];
    if (programsLists && programsLists.size > 0) {
      list = programsLists.map((item) => {
        return { name: item.get('name'), value: item.get('name') };
      });
      list = [{ name: t('all'), value: '' }, ...list];
    }
    return list;
  };

  const redirectURL = (data, active, flow) => {
    const datalist = data.toJS();
    const list = {
      ...datalist,
      activeTab: active,
      leave_flow: flow,
    };
    setLeaveData(list);
    history.push(
      `/leave-management/${list.leave_flow}/pending?id=${list._id}&type=${eString(
        active
      )}&flow=${eString(flow)}`
    );
  };

  return (
    <>
      <div className="bg-gray">
        {/* <div className="row pl-4 pr-4">
          <div className="mt-3 mb-3">
            <div
              className={`remove_hover user_list mr-3 ${tabs === 'Staff' && 'active'}`}
              onClick={() => setTabs('Staff')}
            >
              {' '}
              Staff
              <span className="ml-1 mr-1 alert_notification"> 02 </span>
            </div>
          </div>
          <div className="mt-3 mb-3">
            <div
              className={`remove_hover user_list mr-3 ${tabs === 'Student' && 'active'}`}
              onClick={() => setTabs('Student')}
            >
              {' '}
              Student
              <span className="ml-1 mr-1 alert_notification"> 02 </span>
            </div>
          </div>
        </div> */}
      </div>
      <div className="p-3">
        <Input
          elementType={'select'}
          elementConfig={{
            options: getProgramList(),
          }}
          value={selectedProgram}
          className={'mt--20 selectArrow'}
          label={''}
          labelclass={'mt--20'}
          changed={(e) => setSelectedProgram(e.target.value)}
        />
      </div>
      <div
        className="roles_height"
        style={{ maxHeight: minHeight + 'px', minHeight: minHeight + 'px' }}
      >
        {leaveRequestsLists.get('pending', List()).size === 0 && (
          <div className="text-center mt-5">
            {loading.get('GET_LEAVE_REQUESTS_LIST') ? (
              <Trans i18nKey={'fetching_data'}></Trans>
            ) : (
              <Trans i18nKey={'no_data'}></Trans>
            )}
          </div>
        )}
        {leaveRequestsLists.get('pending', List()).size > 0 && (
          <>
            {leaveRequestsLists
              .get('pending', List())
              .filter((item) =>
                selectedProgram !== ''
                  ? item
                      .getIn(['staff', 'program'], '')
                      .toLowerCase()
                      .includes(selectedProgram.toLowerCase())
                  : item
              )
              .map((leave, index) => {
                return (
                  <div className="border-bottom" key={index}>
                    <div className="p-3 text-left">
                      <span className="pr-2 f-14 text-lightblack">
                        {' '}
                        {getTranslatedDuration(
                          moment(leave.get('status_time')).format('h:mma, DMMM')
                        )}
                      </span>
                      <span>
                        <img src={GreenCircle} className="" alt="Permission" />{' '}
                      </span>
                      <p className="bold mt-1 mb-0">
                        {leave.get('type', '') === 'permission' ? (
                          <>
                            {t('dashboard_view.permission')} -{' '}
                            {getTranslatedDuration(`${moment(leave.get('from')).format(
                              'h:mma'
                            )} ${t('to')} 
                            ${moment(leave.get('to')).format('h:mma')}, 
                            ${moment(leave.get('from')).format('Do MMM YY')}`)}
                          </>
                        ) : (
                          <>
                            {leave.getIn(['leave_category', 'name'], '')} -{' '}
                            {getTranslatedDuration(`${moment(leave.get('from')).format(
                              'Do MMM YY'
                            )} ${t('to')} 
                            ${moment(leave.get('to')).format('Do MMM YY')}`)}
                          </>
                        )}
                      </p>
                      <p className="bold text-lightblack mb-0">
                        {leave.getIn(['staff', 'program'], '')}
                        <span className="float-right text-grey">
                          {jsUcfirst(leave.get('type', '').replace('_', ' '))}
                        </span>
                      </p>
                      <p className=" mb-0">
                        {formatFullName(leave.getIn(['staff', 'name'], {}).toJS())} -{' '}
                        {leave.getIn(['staff', 'user_id'], '')}
                      </p>
                      <p className="text-right mb-0">
                        {' '}
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => redirectURL(leave, leave.get('type'), 'pending')}
                        >
                          <Trans i18nKey={'view'} />
                        </Button>
                      </p>
                    </div>
                  </div>
                );
              })}
          </>
        )}
      </div>
    </>
  );
}

function TodoLists(props) {
  const { height } = useWindowDimensions();
  const minHeight = height - 265;
  return (
    <div
      className="text-center mt-5 roles_height"
      style={{ maxHeight: minHeight + 'px', minHeight: minHeight + 'px' }}
    >
      No data found...
    </div>
  );
}

function SessionsLists(props) {
  const { mySessionLists, loading } = props;

  SessionsLists.propTypes = {
    mySessionLists: PropTypes.instanceOf(List),
    loading: PropTypes.instanceOf(Map),
  };

  const { height } = useWindowDimensions();
  const minHeight = height - 185;
  let mergedSession = [];
  const getMergeSessions = (mergeData) => {
    if (mergeData.size > 0) {
      let temp;
      temp = mergeData
        .map(
          (item) =>
            item.getIn(['schedule_id', 'session', 'delivery_symbol'], '') +
            '' +
            item.getIn(['schedule_id', 'session', 'delivery_no'], '')
        )
        .join(', ');
      mergedSession.push(temp);
      return temp;
    }
    return '';
  };
  return (
    <div
      className="roles_height"
      style={{ maxHeight: minHeight + 'px', minHeight: minHeight + 'px' }}
    >
      {mySessionLists.size === 0 && (
        <div className="text-center mt-5">
          {loading.get('GET_MY_SESSION_LIST') ? (
            <Trans i18nKey={'fetching_data'}></Trans>
          ) : (
            <Trans i18nKey={'no_data'}></Trans>
          )}
        </div>
      )}
      {mySessionLists.size > 0 && (
        <>
          {mySessionLists
            .sort((a, b) => {
              const startDate = getTimestamp(
                moment(a.get('schedule_date')).format('YYYY-MM-DD') +
                  'T' +
                  convertTime12to24(
                    formatTwoString(a.getIn(['start', 'hour'])) +
                      ':' +
                      formatTwoString(a.getIn(['start', 'minute'])) +
                      ' ' +
                      a.getIn(['start', 'format'])
                  )
              );
              const endDate = getTimestamp(
                moment(b.get('schedule_date')).format('YYYY-MM-DD') +
                  'T' +
                  convertTime12to24(
                    formatTwoString(b.getIn(['start', 'hour'])) +
                      ':' +
                      formatTwoString(b.getIn(['start', 'minute'])) +
                      ' ' +
                      b.getIn(['start', 'format'])
                  )
              );
              return startDate - endDate;
            })
            .map((session, index) => {
              let display = true;
              mergedSession.forEach((item) => {
                let currentSession =
                  session.getIn(['session', 'delivery_symbol'], '') +
                  session.getIn(['session', 'delivery_no'], '');
                if (item === currentSession) {
                  display = false;
                }
              });
              if (display) {
                const title = session
                  .get('student_groups', List())
                  .map((studentGroup) => {
                    const studentGroupName = getFormattedGroupName(
                      studentGroup.get('group_name', ''),
                      isIndGroup(studentGroup.get('group_name', '')) ? 1 : 2
                    );
                    const sessionGroupNames = studentGroup
                      .get('session_group', List())
                      .map((sessionGroup) =>
                        getFormattedGroupName(sessionGroup.get('group_name', ''), 3)
                      )
                      .join(', ');
                    return `${studentGroupName.replace('-', '')} - ${sessionGroupNames.replace(
                      '-',
                      ''
                    )}`;
                  })
                  .join(', ');
                const DelType =
                  session.get('type') === 'support_session' || session.get('type') === 'event'
                    ? session.get('sub_type')
                    : session.getIn(['session', 'delivery_symbol'], '') +
                      session.getIn(['session', 'delivery_no'], '') +
                      (session.get('merge_with').size
                        ? ', ' + getMergeSessions(session.get('merge_with'))
                        : ' ');
                return (
                  <div className="border-bottom" key={index}>
                    <div className="p-3">
                      <div>
                        <b>
                          <span className="pr-2 f-16 text-gray">
                            <Trans i18nKey={'dashboard_view.subject'} />
                          </span>
                        </b>{' '}
                        - <span>{session.getIn(['subjects', 0, 'subject_name'], '')}</span>
                      </div>
                      <div>
                        <b>
                          <span className="pr-2 f-16 text-gray">
                            <Trans i18nKey={'dashboard_view.delivery_type'} />
                          </span>
                        </b>
                        -{' '}
                        <span>
                          {session.get('merge_with').size ? (
                            <>
                              <img style={{ marginTop: '-2px' }} src={cursorDown} alt="Digiclass" />{' '}
                            </>
                          ) : (
                            ''
                          )}
                          {DelType}
                        </span>
                      </div>
                      <div>
                        {' '}
                        <b>
                          <span className="pr-2 f-16 text-gray">
                            <Trans i18nKey={'dashboard_view.time'} />
                          </span>{' '}
                        </b>{' '}
                        -{' '}
                        <span>
                          {formatTwoString(session.getIn(['start', 'hour'], ''))}:
                          {formatTwoString(session.getIn(['start', 'minute'], ''))}{' '}
                          {session.getIn(['start', 'format'], '')} -{' '}
                          {formatTwoString(session.getIn(['end', 'hour'], ''))}:
                          {formatTwoString(session.getIn(['end', 'minute'], ''))}{' '}
                          {session.getIn(['end', 'format'], '')}
                        </span>
                      </div>
                      <div>
                        {' '}
                        <b>
                          <span className="pr-2 f-16 text-gray">
                            <Trans i18nKey={'dashboard_view.student_groups'} />
                          </span>{' '}
                        </b>{' '}
                        - <span>{title}</span>
                      </div>
                      <div>
                        {' '}
                        <b>
                          <span className="pr-2 f-16 text-gray">
                            {session.getIn(['_course_id', 'course_code'])}
                          </span>{' '}
                        </b>{' '}
                        -{' '}
                        <b>
                          <span className="pr-2 f-16 text-gray">
                            {session.getIn(['_course_id', 'course_name'])}
                          </span>
                        </b>{' '}
                        {session.get('rotation') === 'yes' ? (
                          <b>
                            -{' '}
                            <span className="pr-2 f-16 text-gray">
                              R{session.get('rotation_count')}
                            </span>{' '}
                          </b>
                        ) : (
                          ''
                        )}
                      </div>
                      <div>
                        {' '}
                        <b>
                          <span className="pr-2 f-16 text-gray">
                            {session.getIn(['_program_id', 'name'])} / {t('dashboard_view.year')}{' '}
                            {session.get('year_no', '').replace('year', '')} /{' '}
                            {session.get('level_no', '')} /{' '}
                            {session.get('term', '').charAt(0).toUpperCase() +
                              session.get('term', '').slice(1)}{' '}
                          </span>{' '}
                        </b>{' '}
                      </div>
                    </div>
                  </div>
                );
              } else {
                return '';
              }
            })}
        </>
      )}
    </div>
  );
}
